package com.midea.pam.basedata.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.basedata.entity.SpecialCharacterConfig;
import com.midea.pam.common.basedata.entity.SpecialCharacterConfigExample;
import java.util.List;

public interface SpecialCharacterConfigMapper extends Mapper {
    long countByExample(SpecialCharacterConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SpecialCharacterConfig record);

    int insertSelective(SpecialCharacterConfig record);

    List<SpecialCharacterConfig> selectByExample(SpecialCharacterConfigExample example);

    SpecialCharacterConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SpecialCharacterConfig record);

    int updateByPrimaryKey(SpecialCharacterConfig record);
}