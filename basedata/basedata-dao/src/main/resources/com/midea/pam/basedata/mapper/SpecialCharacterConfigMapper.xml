<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.basedata.mapper.SpecialCharacterConfigMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.basedata.entity.SpecialCharacterConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_name" jdbcType="VARCHAR" property="configName" />
    <result column="database_name" jdbcType="VARCHAR" property="databaseName" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="column_name" jdbcType="VARCHAR" property="columnName" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="processing_rule" jdbcType="VARCHAR" property="processingRule" />
    <result column="replacement_value" jdbcType="VARCHAR" property="replacementValue" />
    <result column="handle_xml_invalid" jdbcType="BIT" property="handleXmlInvalid" />
    <result column="handle_utf8_4byte" jdbcType="BIT" property="handleUtf84byte" />
    <result column="handle_other_special" jdbcType="BIT" property="handleOtherSpecial" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, config_name, database_name, table_name, column_name, enabled, processing_rule, 
    replacement_value, handle_xml_invalid, handle_utf8_4byte, handle_other_special, priority, 
    description, create_by, create_at, update_by, update_at, deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.basedata.entity.SpecialCharacterConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from special_character_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from special_character_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from special_character_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.basedata.entity.SpecialCharacterConfig">
    insert into special_character_config (id, config_name, database_name, 
      table_name, column_name, enabled, 
      processing_rule, replacement_value, handle_xml_invalid, 
      handle_utf8_4byte, handle_other_special, priority, 
      description, create_by, create_at, 
      update_by, update_at, deleted_flag
      )
    values (#{id,jdbcType=BIGINT}, #{configName,jdbcType=VARCHAR}, #{databaseName,jdbcType=VARCHAR}, 
      #{tableName,jdbcType=VARCHAR}, #{columnName,jdbcType=VARCHAR}, #{enabled,jdbcType=BIT}, 
      #{processingRule,jdbcType=VARCHAR}, #{replacementValue,jdbcType=VARCHAR}, #{handleXmlInvalid,jdbcType=BIT}, 
      #{handleUtf84byte,jdbcType=BIT}, #{handleOtherSpecial,jdbcType=BIT}, #{priority,jdbcType=INTEGER}, 
      #{description,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{deletedFlag,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.basedata.entity.SpecialCharacterConfig">
    insert into special_character_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="configName != null">
        config_name,
      </if>
      <if test="databaseName != null">
        database_name,
      </if>
      <if test="tableName != null">
        table_name,
      </if>
      <if test="columnName != null">
        column_name,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="processingRule != null">
        processing_rule,
      </if>
      <if test="replacementValue != null">
        replacement_value,
      </if>
      <if test="handleXmlInvalid != null">
        handle_xml_invalid,
      </if>
      <if test="handleUtf84byte != null">
        handle_utf8_4byte,
      </if>
      <if test="handleOtherSpecial != null">
        handle_other_special,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="configName != null">
        #{configName,jdbcType=VARCHAR},
      </if>
      <if test="databaseName != null">
        #{databaseName,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="columnName != null">
        #{columnName,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="processingRule != null">
        #{processingRule,jdbcType=VARCHAR},
      </if>
      <if test="replacementValue != null">
        #{replacementValue,jdbcType=VARCHAR},
      </if>
      <if test="handleXmlInvalid != null">
        #{handleXmlInvalid,jdbcType=BIT},
      </if>
      <if test="handleUtf84byte != null">
        #{handleUtf84byte,jdbcType=BIT},
      </if>
      <if test="handleOtherSpecial != null">
        #{handleOtherSpecial,jdbcType=BIT},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.basedata.entity.SpecialCharacterConfigExample" resultType="java.lang.Long">
    select count(*) from special_character_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.basedata.entity.SpecialCharacterConfig">
    update special_character_config
    <set>
      <if test="configName != null">
        config_name = #{configName,jdbcType=VARCHAR},
      </if>
      <if test="databaseName != null">
        database_name = #{databaseName,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null">
        table_name = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="columnName != null">
        column_name = #{columnName,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="processingRule != null">
        processing_rule = #{processingRule,jdbcType=VARCHAR},
      </if>
      <if test="replacementValue != null">
        replacement_value = #{replacementValue,jdbcType=VARCHAR},
      </if>
      <if test="handleXmlInvalid != null">
        handle_xml_invalid = #{handleXmlInvalid,jdbcType=BIT},
      </if>
      <if test="handleUtf84byte != null">
        handle_utf8_4byte = #{handleUtf84byte,jdbcType=BIT},
      </if>
      <if test="handleOtherSpecial != null">
        handle_other_special = #{handleOtherSpecial,jdbcType=BIT},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.basedata.entity.SpecialCharacterConfig">
    update special_character_config
    set config_name = #{configName,jdbcType=VARCHAR},
      database_name = #{databaseName,jdbcType=VARCHAR},
      table_name = #{tableName,jdbcType=VARCHAR},
      column_name = #{columnName,jdbcType=VARCHAR},
      enabled = #{enabled,jdbcType=BIT},
      processing_rule = #{processingRule,jdbcType=VARCHAR},
      replacement_value = #{replacementValue,jdbcType=VARCHAR},
      handle_xml_invalid = #{handleXmlInvalid,jdbcType=BIT},
      handle_utf8_4byte = #{handleUtf84byte,jdbcType=BIT},
      handle_other_special = #{handleOtherSpecial,jdbcType=BIT},
      priority = #{priority,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>