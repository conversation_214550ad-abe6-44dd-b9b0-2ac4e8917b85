<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.basedata.mapper.VendorSiteBankExMapper">

    <resultMap id="DtoResultMap" type="com.midea.pam.common.basedata.dto.VendorSiteBankDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode"/>
        <result column="vendor_name" jdbcType="VARCHAR" property="vendorName"/>
        <result column="operating_unit_id" jdbcType="BIGINT" property="operatingUnitId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        source_system_code as sourceSystemCode,
        erp_vendor_id AS erpVendorId,
        vendor_code AS vendorCode,
        vendor_name AS vendorName,
        vendor_type_lookup_code AS vendorTypeLookupCode,
        currency_code AS currencyCode,
        vendor_type_name AS vendorTypeName,
        erp_icp_org_id AS erpIcpOrgId,
        erp_vendor_site_id AS erpVendorSiteId,
        vendor_site_code AS vendorSiteCode,
        operating_unit_id AS operatingUnitId,
        unite_credit_code AS uniteCreditCode,
        vendor_org_code AS vendorOrgCode,
        tax_register_code AS taxRegisterCode,
        business_license AS businessLicense,
        bank_account AS bankAccount,
        account_num AS accountNum,
        account_name AS accountName,
        combine_bank_nummber AS combineBankNummber,
        pvs_status AS pvsStatus,
        pvs_inactive_date AS pvsInactiveDate,
        payment_method_name AS paymentMethodName,
        term_name AS termName,
        payment_method_code AS paymentMethodCode,
        erp_term_id AS erpTermId,
        purchasing_site_flag AS purchasingSiteFlag,
        start_date_active AS startDateActive,
        end_date_active AS endDateActive,
        bank_inactive_date AS bankInactiveDate,
        account_inactive_date AS accountInactiveDate,
        create_by AS createBy,
        create_at AS createAt,
        update_by AS updateBy,
        update_at AS updateAt,
        bank_country_code as bankCountryCode,
        territory_code as territoryCode,
        full_address as fullAddress,
        swift_code as swiftCode
    </sql>

    <update id="updateVendorSiteInfo" parameterType="com.midea.pam.common.basedata.entity.VendorSiteBank">
        update vendor_site_bank
        set vendor_site_code = #{vendorSiteCode,jdbcType=VARCHAR},
            pvs_status = #{pvsStatus,jdbcType=VARCHAR},
            pvs_inactive_date = #{pvsInactiveDate,jdbcType=TIMESTAMP},
            payment_method_name = #{paymentMethodName,jdbcType=VARCHAR},
            term_name = #{termName,jdbcType=VARCHAR},
            payment_method_code = #{paymentMethodCode,jdbcType=VARCHAR},
            erp_term_id = #{erpTermId,jdbcType=BIGINT},
            purchasing_site_flag = #{purchasingSiteFlag,jdbcType=VARCHAR},
            territory_code = #{territoryCode,jdbcType=VARCHAR},
            full_address = #{fullAddress,jdbcType=VARCHAR}
        where operating_unit_id = #{operatingUnitId,jdbcType=BIGINT}
            and erp_vendor_id = #{erpVendorId,jdbcType=BIGINT}
            and erp_vendor_site_id = #{erpVendorSiteId,jdbcType=BIGINT}
    </update>

    <update id="updateVendorInfo" parameterType="com.midea.pam.common.basedata.entity.VendorSiteBank">
        update vendor_site_bank
        set vendor_code = #{vendorCode,jdbcType=VARCHAR},
            vendor_name = #{vendorName,jdbcType=VARCHAR},
            vendor_type_lookup_code = #{vendorTypeLookupCode,jdbcType=VARCHAR},
            city = #{city,jdbcType=VARCHAR},
            province = #{province,jdbcType=VARCHAR},
            country_name = #{countryName,jdbcType=VARCHAR},
            vendor_type_name = #{vendorTypeName,jdbcType=VARCHAR},
            unite_credit_code = #{uniteCreditCode,jdbcType=VARCHAR},
            vendor_org_code = #{vendorOrgCode,jdbcType=VARCHAR},
            tax_register_code = #{taxRegisterCode,jdbcType=VARCHAR},
            business_license = #{businessLicense,jdbcType=VARCHAR},
            start_date_active = #{startDateActive,jdbcType=TIMESTAMP},
            end_date_active = #{endDateActive,jdbcType=TIMESTAMP}
        where erp_vendor_id = #{erpVendorId,jdbcType=BIGINT}
    </update>

    <select id="selectByUnitId" parameterType="com.midea.pam.common.basedata.dto.VendorSiteBankDto"
            resultMap="DtoResultMap">
        select distinct vendor_code,
                        vendor_name
        from vendor_site_bank
        where ((start_date_active is not null and start_date_active <![CDATA[<=]]> #{currentDate} and end_date_active is not null and end_date_active <![CDATA[>]]> #{currentDate})
            or (start_date_active is not null and start_date_active <![CDATA[<=]]> #{currentDate} and end_date_active is null)
            or (start_date_active is null and end_date_active is null))
          and operating_unit_id in (
            select ou.operating_unit_id
            from operating_unit ou
                     left join unit_ou_rel ul on ou.id = ul.ou_id
            where ((ou.operating_unit_end_date is not null and ou.operating_unit_end_date <![CDATA[>]]> #{currentDate}) or
                   ou.operating_unit_end_date is null)
              and ((ul.start_date is not null and ul.start_date <![CDATA[<=]]> #{currentDate} and ul.end_date is not null and ul.end_date <![CDATA[>]]> #{currentDate})
                or (ul.start_date is not null and ul.start_date <![CDATA[<=]]> #{currentDate} and ul.end_date is null)
                or (ul.start_date is null and ul.end_date is null))
              and ou.pam_enabled = 0
              and ul.delete_flag = 0
              and ul.unit_id in
                  (select u.id
                   from unit u
                   where u.parent_id = #{unitId}
                     and u.delete_flag = 0
                     and ((u.start_date is not null and u.start_date <![CDATA[<=]]> #{currentDate} and u.end_date is not null and
                           u.end_date <![CDATA[>]]> #{currentDate})
                       or (u.start_date is not null and u.start_date <![CDATA[<=]]> #{currentDate} and u.end_date is null)
                       or (u.start_date is null and u.end_date is null))
                  )
        )

    </select>
    <select id="findAllByPaymentMethodCode" resultType="com.midea.pam.common.basedata.entity.VendorSiteBank">
        select distinct
        <include refid="Base_Column_List"/>
        from vendor_site_bank
        where 1=1
        <if test="item.operatingUnitIds != null and item.operatingUnitIds.size() > 0">
            and operating_unit_id in
            <foreach collection="item.operatingUnitIds" item="operatingUnitId" open="(" separator="," close=")" >
                #{operatingUnitId}
            </foreach>
        </if>
        <if test="item.ouId != null">
            and operating_unit_id = #{item.ouId}
        </if>
        <if test="item.vendorCode != null">
            and vendor_code = #{item.vendorCode}
        </if>
        <if test="item.vendorName != null">
            and vendor_name = #{vendorName}
        </if>
        <if test="item.paymentMethodCode != null">
            and payment_method_code = #{item.paymentMethodCode}
        </if>
        <if test="item.erpVendorSiteId != null">
            and erp_vendor_site_id = #{item.erpVendorSiteId}
        </if>
        <if test="item.purchasingSiteFlag !=null">
            and purchasing_site_flag = #{item.purchasingSiteFlag}
        </if>
        GROUP BY bank_account, combine_bank_nummber, currency_code, account_inactive_date,bank_inactive_date
    </select>
    <select id="selectInfoByParam" parameterType="com.midea.pam.common.basedata.dto.VendorSiteBankDto"
            resultType="com.midea.pam.common.basedata.entity.VendorSiteBank">
        select
        <include refid="Base_Column_List"/>
        from
            pam_basedata.vendor_site_bank vsb
        where
            vsb.operating_unit_id =	#{ouId}
            and vsb.bank_account = #{vendorBankNum}
            and vsb.vendor_code = #{vendorCode}
            and vsb.account_name = #{accountName}
    </select>

    <sql id="conditionForDisplay">
        <if test="id != null">
            and t1.id = #{id, jdbcType=BIGINT}
        </if>

        <if test="erpVendorSiteId != null">
            and t1.erp_vendor_site_id = #{erpVendorSiteId, jdbcType=BIGINT}
        </if>

        <if test="erpVendorId != null">
            and t1.erp_vendor_id = #{erpVendorId, jdbcType=BIGINT}
        </if>

        <if test="vendorName != null and vendorName != ''">
            and t1.vendor_name like #{vendorName, jdbcType=VARCHAR}
        </if>
        <if test="ouId != null ">
            and t1.operating_unit_id = #{ouId, jdbcType=BIGINT}
        </if>
        <if test="operatingUnitIdList != null and operatingUnitIdList.size>0">
            and t1.operating_unit_id in
            <foreach collection="operatingUnitIdList" index="index" item="itemOu" open="(" separator="," close=")">
                #{itemOu}
            </foreach>
        </if>
        <if test="operatingUnitIds != null and operatingUnitIdList.size > 0">
            and t1.operating_unit_id in
            <foreach collection="operatingUnitIds" index="index" item="itemOu" open="(" separator="," close=")">
                #{itemOu}
            </foreach>
        </if>
        <if test="vendorCode != null and vendorCode != ''">
            and t1.vendor_code like #{vendorCode, jdbcType=VARCHAR}
        </if>

        <if test="uniteCreditCode != null and uniteCreditCode != ''">
            and t1.unite_credit_code like #{uniteCreditCode, jdbcType=VARCHAR}
        </if>

        <if test="vendorOrgCode != null and vendorOrgCode != ''">
            and t1.vendor_org_code like #{vendorOrgCode, jdbcType=VARCHAR}
        </if>

        <if test="taxRegisterCode != null and taxRegisterCode != ''">
            and t1.tax_register_code like #{taxRegisterCode, jdbcType=VARCHAR}
        </if>

        <if test="bankAccount != null and bankAccount != ''">
            and t1.bank_account like #{bankAccount, jdbcType=VARCHAR}
        </if>

        <if test="paymentMethodName != null and paymentMethodName != ''">
            and t1.payment_method_name like #{paymentMethodName, jdbcType=VARCHAR}
        </if>

        <if test="termName != null and termName != ''">
            and t1.term_name like #{termName, jdbcType=VARCHAR}
        </if>

        <if test="purchasingSiteFlag != null and purchasingSiteFlag != ''">
            and t1.purchasing_site_flag like #{purchasingSiteFlag, jdbcType=VARCHAR}
        </if>

        <if test="vendorTypeNames != null and vendorTypeNames.size>0">
            and t1.vendor_type_name in
            <foreach collection="vendorTypeNames" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="vendorCodeOrName != null and vendorCodeOrName != ''">
            and (t1.vendor_name like #{vendorCodeOrName, jdbcType=VARCHAR} or t1.vendor_code like #{vendorCodeOrName, jdbcType=VARCHAR})
        </if>

    </sql>

    <select id="selectForDisplay" parameterType="com.midea.pam.common.basedata.dto.VendorSiteBankDto"
            resultType="com.midea.pam.common.basedata.entity.VendorSiteBankForDisplay">
        select
        t1.id as id,
        t1.source_system_code as sourceSystemCode,
        t1.erp_vendor_id as erpVendorId,
        t1.vendor_code as vendorCode,
        t1.vendor_name as vendorName,
        t1.vendor_type_lookup_code as vendorTypeLookupCode,
        t1.vendor_type_name as vendorTypeName,
        t1.erp_icp_org_id as erpIcpOrgId,
        t1.erp_vendor_site_id as erpVendorSiteId,
        t1.vendor_site_code as vendorSiteCode,
        t1.operating_unit_id as operatingUnitId,
        t1.unite_credit_code as uniteCreditCode,
        t1.vendor_org_code as vendorOrgCode,
        t1.tax_register_code as taxRegisterCode,
        t1.business_license as businessLicense,
        t1.bank_account as bankAccount,
        t1.account_num as accountNum,
        t1.account_name as accountName,
        t1.combine_bank_nummber as combineBankNummber,
        t1.pvs_status as pvsStatus,
        t1.pvs_inactive_date as pvsInactiveDate,
        t1.payment_method_name as paymentMethodName,
        t1.term_name as termName,
        t1.payment_method_code as paymentMethodCode,
        t1.erp_term_id aserpTermId,
        t1.purchasing_site_flag as purchasingSiteFlag,
        t1.start_date_active startDateActive,
        t1.end_date_active as endDateActive,
        t1.account_inactive_date as accountInactiveDate,
        t1.bank_inactive_date as bankInactiveDate,
        t1.create_by as createBy,
        t1.create_at as createAt,
        t1.update_by as updateBy,
        t1.update_at as updateAt,
        t2.operating_unit_name as operatingUnitName
        from vendor_site_bank t1
        left join operating_unit t2 on t1.operating_unit_id = t2.operating_unit_id
        where 1=1
        <if test="operatingUnitName != null and operatingUnitName != ''">
            and t2.operating_unit_name like concat('%',#{operatingUnitName, jdbcType=VARCHAR},'%')
        </if>
        <include refid="conditionForDisplay"></include>
        limit #{offset, jdbcType=INTEGER}, #{pageSize, jdbcType=INTEGER}
    </select>

    <select id="countForDisplay" parameterType="com.midea.pam.common.basedata.dto.VendorSiteBankDto" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM (
        select
        t1.id as id
        from vendor_site_bank t1
        left join operating_unit t2 on t1.operating_unit_id = t2.operating_unit_id
        where 1=1
        <if test="operatingUnitName != null and operatingUnitName != ''">
            and t2.operating_unit_name like concat('%',#{operatingUnitName, jdbcType=VARCHAR},'%')
        </if>
        <include refid="conditionForDisplay"></include>
        ) a
    </select>

    <select id="selectForDisplayNew" parameterType="com.midea.pam.common.basedata.dto.VendorSiteBankDto"
            resultType="com.midea.pam.common.basedata.entity.VendorSiteBankForDisplay">
        select
            t1.id as id,
            t1.source_system_code as sourceSystemCode,
            t1.erp_vendor_id as erpVendorId,
            t1.vendor_code as vendorCode,
            t1.vendor_name as vendorName,
            t1.vendor_type_lookup_code as vendorTypeLookupCode,
            t1.vendor_type_name as vendorTypeName,
            t1.erp_icp_org_id as erpIcpOrgId,
            t1.erp_vendor_site_id as erpVendorSiteId,
            t1.vendor_site_code as vendorSiteCode,
            t1.operating_unit_id as operatingUnitId,
            t1.unite_credit_code as uniteCreditCode,
            t1.vendor_org_code as vendorOrgCode,
            t1.tax_register_code as taxRegisterCode,
            t1.business_license as businessLicense,
            t1.bank_account as bankAccount,
            t1.currency_code as currencyCode,
            t1.account_num as accountNum,
            t1.account_name as accountName,
            t1.combine_bank_nummber as combineBankNummber,
            t1.pvs_status as pvsStatus,
            t1.pvs_inactive_date as pvsInactiveDate,
            t1.payment_method_name as paymentMethodName,
            t1.term_name as termName,
            t1.payment_method_code as paymentMethodCode,
            t1.erp_term_id aserpTermId,
            t1.purchasing_site_flag as purchasingSiteFlag,
            t1.start_date_active startDateActive,
            t1.end_date_active as endDateActive,
            t1.account_inactive_date as accountInactiveDate,
            t1.bank_inactive_date as bankInactiveDate,
            t1.create_by as createBy,
            t1.create_at as createAt,
            t1.update_by as updateBy,
            t1.update_at as updateAt
        from vendor_site_bank t1
        where 1=1 AND t1.vendor_site_code NOT IN ('押金质保金','非贸易型')
        <if test="showPurchasingSiteFlagIsN == null">
            and t1.purchasing_site_flag='Y'
        </if>
        <include refid="conditionForDisplay"></include>
        GROUP BY t1.erp_vendor_id ,t1.erp_vendor_site_id
        limit #{offset, jdbcType=INTEGER}, #{pageSize, jdbcType=INTEGER}
    </select>

    <select id="countForDisplayNew" parameterType="com.midea.pam.common.basedata.dto.VendorSiteBankDto" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM (
        select
        distinct
        t1.erp_vendor_id ,t1.erp_vendor_site_id
        from vendor_site_bank t1
        where 1=1 AND t1.vendor_site_code NOT IN ('押金质保金','非贸易型')
        <if test="showPurchasingSiteFlagIsN == null">
            and t1.purchasing_site_flag='Y'
        </if>
        <include refid="conditionForDisplay"></include>
        ) a
    </select>

    <select id="selectByCodes" resultType="com.midea.pam.common.basedata.dto.VendorSiteBankDto">
        select id,
        erp_vendor_id as erpVendorId,
        vendor_code as vendorCode,
        vendor_name as vendorName,
        vendor_site_code as vendorSiteCode,
        erp_vendor_site_id as erpVendorSiteId,
        vendor_site_code as vendorSiteCode,
        payment_method_name as paymentMethodName,
        bank_account as bankAccount,
        account_num as accountNum,
        account_name as accountName,
        operating_unit_id as operatingUnitId,
        purchasing_site_flag as purchasingSiteFlag
        from vendor_site_bank
        where vendor_code in
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and ( start_date_active is null or start_date_active &lt;=now() )
        and ( end_date_active is null or end_date_active >= now() )
    </select>

    <select id="selectByCodes1" resultType="com.midea.pam.common.basedata.dto.VendorSiteBankDto">
        select
            operating_unit_id operatingUnitId,
            vendor_code vendorCode,
            erp_vendor_id erpVendorId,
            erp_vendor_site_id erpVendorSiteId,
            vendor_type_name vendorTypeName,
            vendor_type_lookup_code vendorTypeLookupCode,
            pvs_status pvsStatus,
            end_date_active endDateActive
        from vendor_site_bank
        where vendor_code in
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and vendor_site_code NOT IN ('押金质保金','非贸易型')
    </select>

    <select id="getVendorTypeNames" resultType="java.lang.String">
        select distinct vendor_type_name from vendor_site_bank
    </select>

    <select id="selectByCodeAndOuId" resultType="com.midea.pam.common.basedata.dto.VendorSiteBankDto">
            select
                vsb.id,
                vsb.bank_account as bankAccount,
                vsb.account_name as accountName,
                vsb.combine_bank_nummber as combineBankNummber
            from vendor_site_bank vsb
            where  vendor_code in
            <foreach collection="codes" item="code" open="(" separator="," close=")">
                    #{code}
            </foreach>
            and vsb.operating_unit_id = #{ouId}
            group by vsb.bank_account,vsb.account_name,combine_bank_nummber
    </select>
</mapper>
