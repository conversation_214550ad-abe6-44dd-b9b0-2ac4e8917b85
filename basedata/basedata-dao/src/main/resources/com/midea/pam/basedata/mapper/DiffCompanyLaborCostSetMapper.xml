<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.basedata.mapper.DiffCompanyLaborCostSetMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.basedata.entity.DiffCompanyLaborCostSet">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_company_id" jdbcType="BIGINT" property="projectCompanyId" />
    <result column="user_company_id" jdbcType="BIGINT" property="userCompanyId" />
    <result column="ou_id" jdbcType="BIGINT" property="ouId" />
    <result column="ou_name" jdbcType="VARCHAR" property="ouName" />
    <result column="hard_working" jdbcType="VARCHAR" property="hardWorking" />
    <result column="full_pay_ou_id" jdbcType="BIGINT" property="fullPayOuId" />
    <result column="full_pay_ou_name" jdbcType="VARCHAR" property="fullPayOuName" />
    <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_company_id, user_company_id, ou_id, ou_name, hard_working, full_pay_ou_id, 
    full_pay_ou_name, deleted_flag, create_by, create_at, update_by, update_at
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.basedata.entity.DiffCompanyLaborCostSetExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from diff_company_labor_cost_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from diff_company_labor_cost_set
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from diff_company_labor_cost_set
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.basedata.entity.DiffCompanyLaborCostSet">
    insert into diff_company_labor_cost_set (id, project_company_id, user_company_id, 
      ou_id, ou_name, hard_working, 
      full_pay_ou_id, full_pay_ou_name, deleted_flag, 
      create_by, create_at, update_by, 
      update_at)
    values (#{id,jdbcType=BIGINT}, #{projectCompanyId,jdbcType=BIGINT}, #{userCompanyId,jdbcType=BIGINT}, 
      #{ouId,jdbcType=BIGINT}, #{ouName,jdbcType=VARCHAR}, #{hardWorking,jdbcType=VARCHAR}, 
      #{fullPayOuId,jdbcType=BIGINT}, #{fullPayOuName,jdbcType=VARCHAR}, #{deletedFlag,jdbcType=BIT}, 
      #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.basedata.entity.DiffCompanyLaborCostSet">
    insert into diff_company_labor_cost_set
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectCompanyId != null">
        project_company_id,
      </if>
      <if test="userCompanyId != null">
        user_company_id,
      </if>
      <if test="ouId != null">
        ou_id,
      </if>
      <if test="ouName != null">
        ou_name,
      </if>
      <if test="hardWorking != null">
        hard_working,
      </if>
      <if test="fullPayOuId != null">
        full_pay_ou_id,
      </if>
      <if test="fullPayOuName != null">
        full_pay_ou_name,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectCompanyId != null">
        #{projectCompanyId,jdbcType=BIGINT},
      </if>
      <if test="userCompanyId != null">
        #{userCompanyId,jdbcType=BIGINT},
      </if>
      <if test="ouId != null">
        #{ouId,jdbcType=BIGINT},
      </if>
      <if test="ouName != null">
        #{ouName,jdbcType=VARCHAR},
      </if>
      <if test="hardWorking != null">
        #{hardWorking,jdbcType=VARCHAR},
      </if>
      <if test="fullPayOuId != null">
        #{fullPayOuId,jdbcType=BIGINT},
      </if>
      <if test="fullPayOuName != null">
        #{fullPayOuName,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=BIT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.basedata.entity.DiffCompanyLaborCostSetExample" resultType="java.lang.Long">
    select count(*) from diff_company_labor_cost_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.basedata.entity.DiffCompanyLaborCostSet">
    update diff_company_labor_cost_set
    <set>
      <if test="projectCompanyId != null">
        project_company_id = #{projectCompanyId,jdbcType=BIGINT},
      </if>
      <if test="userCompanyId != null">
        user_company_id = #{userCompanyId,jdbcType=BIGINT},
      </if>
      <if test="ouId != null">
        ou_id = #{ouId,jdbcType=BIGINT},
      </if>
      <if test="ouName != null">
        ou_name = #{ouName,jdbcType=VARCHAR},
      </if>
      <if test="hardWorking != null">
        hard_working = #{hardWorking,jdbcType=VARCHAR},
      </if>
      <if test="fullPayOuId != null">
        full_pay_ou_id = #{fullPayOuId,jdbcType=BIGINT},
      </if>
      <if test="fullPayOuName != null">
        full_pay_ou_name = #{fullPayOuName,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=BIT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.basedata.entity.DiffCompanyLaborCostSet">
    update diff_company_labor_cost_set
    set project_company_id = #{projectCompanyId,jdbcType=BIGINT},
      user_company_id = #{userCompanyId,jdbcType=BIGINT},
      ou_id = #{ouId,jdbcType=BIGINT},
      ou_name = #{ouName,jdbcType=VARCHAR},
      hard_working = #{hardWorking,jdbcType=VARCHAR},
      full_pay_ou_id = #{fullPayOuId,jdbcType=BIGINT},
      full_pay_ou_name = #{fullPayOuName,jdbcType=VARCHAR},
      deleted_flag = #{deletedFlag,jdbcType=BIT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>