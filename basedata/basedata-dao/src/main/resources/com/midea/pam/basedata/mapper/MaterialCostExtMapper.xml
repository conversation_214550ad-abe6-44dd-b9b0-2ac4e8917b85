<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.basedata.mapper.MaterialCostExtMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.basedata.entity.MaterialCost">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cost_type_id" jdbcType="BIGINT" property="costTypeId" />
    <result column="cost_type" jdbcType="VARCHAR" property="costType" />
    <result column="organization_id" jdbcType="BIGINT" property="organizationId" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
    <result column="descr" jdbcType="VARCHAR" property="descr" />
    <result column="material_cost_type" jdbcType="INTEGER" property="materialCostType" />
    <result column="item_cost" jdbcType="DECIMAL" property="itemCost" />
    <result column="currency_id" jdbcType="BIGINT" property="currencyId" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="business_classification" jdbcType="VARCHAR" property="businessClassification" />
    <result column="material_classification" jdbcType="VARCHAR" property="materialClassification" />
    <result column="materiel_type" jdbcType="VARCHAR" property="materielType" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
    <result column="approved_supplier" jdbcType="BIT" property="approvedSupplier" />
    <result column="nuclear_price_user_by" jdbcType="BIGINT" property="nuclearPriceUserBy" />
    <result column="nuclear_price_at" jdbcType="TIMESTAMP" property="nuclearPriceAt" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="milepost_id" jdbcType="BIGINT" property="milepostId" />
    <result column="requirement_num" jdbcType="DECIMAL" property="requirementNum" />
  </resultMap>

  <resultMap id="BaseResultMap2" type="com.midea.pam.common.basedata.dto.MaterialCostDto">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cost_type_id" jdbcType="BIGINT" property="costTypeId" />
    <result column="cost_type" jdbcType="VARCHAR" property="costType" />
    <result column="organization_id" jdbcType="BIGINT" property="organizationId" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
    <result column="descr" jdbcType="VARCHAR" property="descr" />
    <result column="material_cost_type" jdbcType="INTEGER" property="materialCostType" />
    <result column="item_cost" jdbcType="DECIMAL" property="itemCost" />
    <result column="currency_id" jdbcType="BIGINT" property="currencyId" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="business_classification" jdbcType="VARCHAR" property="businessClassification" />
    <result column="material_classification" jdbcType="VARCHAR" property="materialClassification" />
    <result column="materiel_type" jdbcType="VARCHAR" property="materielType" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
    <result column="approved_supplier" jdbcType="BIT" property="approvedSupplier" />
    <result column="nuclear_price_user_by" jdbcType="BIGINT" property="nuclearPriceUserBy" />
    <result column="nuclear_price_at" jdbcType="TIMESTAMP" property="nuclearPriceAt" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="milepost_id" jdbcType="BIGINT" property="milepostId" />
    <result column="requirement_num" jdbcType="DECIMAL" property="requirementNum" />
  </resultMap>

  <sql id="Base_Column_List">
    id, cost_type_id, cost_type, organization_id, item_code, pam_code, descr, material_cost_type,
    item_cost, currency_id, currency, business_classification, material_classification,
    materiel_type, unit, remark, last_update_date, deleted_flag, approved_supplier, nuclear_price_user_by,
    nuclear_price_at, create_by, create_at, update_by, update_at,project_code,project_id, milepost_id, requirement_num
  </sql>

    <resultMap id="BaseResultUserInfo" type="com.midea.pam.common.basedata.dto.UserDetailsDto">
        <result column="id" property="id"/>      <!-- -->
        <result column="name" property="name"/>      <!-- -->
        <result column="username" property="userName"/>        <!-- -->
        <result column="mobile" property="mobile"/>        <!-- -->
        <result column="email" property="email"/>        <!-- -->
        <result column="gender" property="gender"/>        <!-- -->
        <result column="portrait" property="portrait"/>        <!-- -->
        <result column="birthday" property="birthday"/>        <!-- -->
        <result column="position_level" property="positionLevel"/>        <!-- -->
        <result column="position_rank" property="positionRank"/>        <!-- -->
        <result column="employee_number" property="employeeNumber"/>        <!-- -->
        <result column="company_name" property="companyName"/>        <!-- -->
        <result column="companyId" property="companyId"/>        <!-- -->
        <result column="levelId" property="levelId"/>        <!-- -->
        <result column="level" property="level"/>        <!-- -->
        <result column="level_name" property="levelName"/>        <!-- -->
        <result column="user_type" property="userType"/>
        <result column="position_type" property="positionType"/>      <!-- -->
        <result column="positionName" property="positionName"/>      <!-- -->
    </resultMap>

    <sql id="Base_Column_List_UserInfo">
    id, name, username, mobile, email, gender,
    portrait, birthday, position_level, position_rank, employee_number,
    create_by, create_at, update_by, update_at, source_id, type, full_pinyin, first_pinyin
  </sql>

  <select id="findApprovedSupplierMaterialCosts" resultMap="BaseResultMap2">
    select
    distinct
    <include refid="Base_Column_List" />
    from material_cost
    where 1=1
    <if test="query.fuzzyPamCode != null">
      and pam_code like CONCAT('%', #{query.fuzzyPamCode}, '%')
    </if>
    <if test="query.fuzzyErpCode != null">
      and item_code like CONCAT('%', #{query.fuzzyErpCode}, '%')
    </if>
    <if test="query.fuzzyDescr != null">
      and descr like CONCAT('%', #{query.fuzzyDescr}, '%')
    </if>
    <if test="query.organizationId != null">
      and organization_id = #{query.organizationId}
    </if>
    <if test="query.materielType != null">
      and materiel_type = #{query.materielType}
    </if>
    <if test="query.nuclearPriceStartDate != null">
      and nuclear_price_at >= #{query.nuclearPriceStartDate}
    </if>
    <if test="query.nuclearPriceEndDate != null">
      and nuclear_price_at <![CDATA[<=]]> #{query.nuclearPriceEndDate}
    </if>
    <if test="query.nuclearPriceUserBy != null">
      and nuclear_price_user_by = #{query.nuclearPriceUserBy}
    </if>
    <if test="query.itemCostIsNull != null and 'true'.toString() == query.itemCostIsNull.toString()">
      and item_cost is null
    </if>
    <if test="query.itemCostIsNull != null and 'false'.toString() == query.itemCostIsNull.toString()">
      and item_cost is not null
    </if>
    <if test="query.approvedSupplier != null">
      and approved_supplier = #{query.approvedSupplier}
    </if>
    order by create_at desc
  </select>



    <select id="findUserInfo" resultMap="BaseResultUserInfo">
        select
        <include refid="Base_Column_List_UserInfo" />
        from pam_basedata.ltc_user_info lui
        left join (
        select mcd.value
        from pam_ctc.material_custom_dict mcd
        left join pam_ctc.material_custom_dict_header mcdh on
        mcd.header_id = mcdh.id
        where mcdh.coding_middleclass = #{codingMiddleClass}
        and mcdh.coding_class = #{materialClassification}
        and mcdh.coding_subclass is null) u on u.value = lui.username
        where u.value is not null
        and lui.username is not null
    </select>


    <select id="getMaterialCostDtoByDate" resultType="com.midea.pam.common.basedata.dto.MaterialCostDto">
    select
        mc.id,
        mc.item_code as itemCode,
        mc.item_cost as itemCost,
        mc.item_cost_is_null as itemCostIsNull,
        mc.material_cost_type as materialCostType,
        or2.operating_unit_id as ouId
    from
        pam_basedata.material_cost mc
    left join pam_basedata.organization_rel or2 on
        mc.organization_id = or2.organization_id
        and or2.pam_enabled = 0
    where
        mc.item_code is not null
        and mc.item_cost is not null
        and ( mc.update_at <![CDATA[ >= ]]> #{updateAt}
            or mc.create_at <![CDATA[ >= ]]> #{updateAt} )
        and mc.deleted_flag = 0
    </select>

    <update id="batchUpdate" parameterType="com.midea.pam.common.basedata.entity.MaterialCost">
        <foreach collection="list" item="item" separator=";">
            update material_cost
            <set>
                <if test="item.costTypeId != null">
                    cost_type_id = #{item.costTypeId,jdbcType=BIGINT},
                </if>
                <if test="item.costType != null">
                    cost_type = #{item.costType,jdbcType=VARCHAR},
                </if>
                <if test="item.organizationId != null">
                    organization_id = #{item.organizationId,jdbcType=BIGINT},
                </if>
                <if test="item.itemCode != null">
                    item_code = #{item.itemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.pamCode != null">
                    pam_code = #{item.pamCode,jdbcType=VARCHAR},
                </if>
                <if test="item.descr != null">
                    descr = #{item.descr,jdbcType=VARCHAR},
                </if>
                <if test="item.materialCostType != null">
                    material_cost_type = #{item.materialCostType,jdbcType=INTEGER},
                </if>
                <if test="item.itemCost != null">
                    item_cost = #{item.itemCost,jdbcType=DECIMAL},
                </if>
                <if test="item.currencyId != null">
                    currency_id = #{item.currencyId,jdbcType=BIGINT},
                </if>
                <if test="item.currency != null">
                    currency = #{item.currency,jdbcType=VARCHAR},
                </if>
                <if test="item.businessClassification != null">
                    business_classification = #{item.businessClassification,jdbcType=VARCHAR},
                </if>
                <if test="item.materialClassification != null">
                    material_classification = #{item.materialClassification,jdbcType=VARCHAR},
                </if>
                <if test="item.materielType != null">
                    materiel_type = #{item.materielType,jdbcType=VARCHAR},
                </if>
                <if test="item.codingMiddleclass != null">
                    coding_middleclass = #{item.codingMiddleclass,jdbcType=VARCHAR},
                </if>
                <if test="item.machiningPartType != null">
                    machining_part_type = #{item.machiningPartType,jdbcType=VARCHAR},
                </if>
                <if test="item.material != null">
                    material = #{item.material,jdbcType=VARCHAR},
                </if>
                <if test="item.fixedLotMultiplier != null">
                    fixed_lot_multiplier = #{item.fixedLotMultiplier,jdbcType=VARCHAR},
                </if>
                <if test="item.minOrderQuantity != null">
                    min_order_quantity = #{item.minOrderQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.advancePurchaseData != null">
                    advance_purchase_data = #{item.advancePurchaseData,jdbcType=VARCHAR},
                </if>
                <if test="item.unitWeight != null">
                    unit_weight = #{item.unitWeight,jdbcType=DECIMAL},
                </if>
                <if test="item.materialProcessing != null">
                    material_processing = #{item.materialProcessing,jdbcType=VARCHAR},
                </if>
                <if test="item.unit != null">
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateDate != null">
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.deletedFlag != null">
                    deleted_flag = #{item.deletedFlag,jdbcType=BIT},
                </if>
                <if test="item.nuclearPriceUserBy != null">
                    nuclear_price_user_by = #{item.nuclearPriceUserBy,jdbcType=BIGINT},
                </if>
                <if test="item.nuclearPriceAt != null">
                    nuclear_price_at = #{item.nuclearPriceAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.projectCode != null">
                    project_code = #{item.projectCode,jdbcType=VARCHAR},
                </if>
                <if test="item.projectId != null">
                    project_id = #{item.projectId,jdbcType=BIGINT},
                </if>
                <if test="item.milepostId != null">
                    milepost_id = #{item.milepostId,jdbcType=BIGINT},
                </if>
                <if test="item.requirementNum != null">
                    requirement_num = #{item.requirementNum,jdbcType=DECIMAL},
                </if>
                <if test="item.approvedSupplier != null">
                    approved_supplier = #{item.approvedSupplier,jdbcType=BIT},
                </if>
                <if test="item.materialCategory != null">
                    material_category = #{item.materialCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.figureNumber != null">
                    figure_number = #{item.figureNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.chartVersion != null">
                    chart_version = #{item.chartVersion,jdbcType=VARCHAR},
                </if>
                <if test="item.brandMaterialCode != null">
                    brand_material_code = #{item.brandMaterialCode,jdbcType=VARCHAR},
                </if>
                <if test="item.orSparePartsMask != null">
                    or_spare_parts_mask = #{item.orSparePartsMask,jdbcType=VARCHAR},
                </if>
                <if test="item.brand != null">
                    brand = #{item.brand,jdbcType=VARCHAR},
                </if>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.model != null">
                    model = #{item.model,jdbcType=VARCHAR},
                </if>
                <if test="item.itemCostIsNull != null">
                    item_cost_is_null = #{item.itemCostIsNull,jdbcType=BIT},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

</mapper>