<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.basedata.mapper.MaterialPriceExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.basedata.entity.MaterialPrice">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="purchase_bpa_price_id" jdbcType="BIGINT" property="purchaseBpaPriceId" />
        <result column="ou_id" jdbcType="BIGINT" property="ouId" />
        <result column="organization_id" jdbcType="BIGINT" property="organizationId" />
        <result column="price_source" jdbcType="BIT" property="priceSource" />
        <result column="config_header_id" jdbcType="BIGINT" property="configHeaderId" />
        <result column="config_detail_id" jdbcType="BIGINT" property="configDetailId" />
        <result column="receipt_header_id" jdbcType="BIGINT" property="receiptHeaderId" />
        <result column="receipt_detail_id" jdbcType="BIGINT" property="receiptDetailId" />
        <result column="execute_id" jdbcType="BIGINT" property="executeId" />
        <result column="material_id" jdbcType="BIGINT" property="materialId" />
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
        <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
        <result column="currency" jdbcType="VARCHAR" property="currency" />
        <result column="conversion_date" jdbcType="TIMESTAMP" property="conversionDate" />
        <result column="conversion_type" jdbcType="VARCHAR" property="conversionType" />
        <result column="conversion_rate" jdbcType="DECIMAL" property="conversionRate" />
        <result column="amount" jdbcType="DECIMAL" property="amount" />
        <result column="price_is_lock" jdbcType="BIT" property="priceIsLock" />
        <result column="modified_by" jdbcType="BIGINT" property="modifiedBy" />
        <result column="modified_time" jdbcType="TIMESTAMP" property="modifiedTime" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
    </resultMap>

    <insert id="batchInsert" parameterType="com.midea.pam.common.basedata.entity.MaterialPrice">
        insert into material_price (id, purchase_bpa_price_id, ou_id, organization_id,
        price_source, config_header_id, config_detail_id,
        receipt_header_id, receipt_detail_id, execute_id,
        material_id, pam_code, erp_code,
        currency, conversion_date, conversion_type,
        conversion_rate, amount, price_is_lock,
        modified_by, modified_time,
        create_by, create_at, update_by,
        update_at)
        values
        <foreach collection="list" item="obj" separator=",">
            (#{obj.id,jdbcType=BIGINT}, #{obj.purchaseBpaPriceId,jdbcType=BIGINT}, #{obj.ouId,jdbcType=BIGINT}, #{obj.organizationId,jdbcType=BIGINT},
            #{obj.priceSource,jdbcType=BIT}, #{obj.configHeaderId,jdbcType=BIGINT},
            #{obj.configDetailId,jdbcType=BIGINT},
            #{obj.receiptHeaderId,jdbcType=BIGINT}, #{obj.receiptDetailId,jdbcType=BIGINT},
            #{obj.executeId,jdbcType=BIGINT},
            #{obj.materialId,jdbcType=BIGINT}, #{obj.pamCode,jdbcType=VARCHAR}, #{obj.erpCode,jdbcType=VARCHAR},
            #{obj.currency,jdbcType=VARCHAR}, #{obj.conversionDate,jdbcType=TIMESTAMP},
            #{obj.conversionType,jdbcType=VARCHAR},
            #{obj.conversionRate,jdbcType=DECIMAL}, #{obj.amount,jdbcType=DECIMAL}, #{obj.priceIsLock,jdbcType=BIT},
            #{obj.modifiedBy,jdbcType=BIGINT}, #{obj.modifiedTime,jdbcType=TIMESTAMP},
            #{obj.createBy,jdbcType=BIGINT}, #{obj.createAt,jdbcType=TIMESTAMP}, #{obj.updateBy,jdbcType=BIGINT},
            #{obj.updateAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <update id="updateBatchLock" parameterType="java.util.List">
        update material_price set price_is_lock = 1 where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateBatchUnLock" parameterType="java.util.List">
        update material_price set price_is_lock = 0 where id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateBatch" parameterType="java.util.List">
        update pam_basedata.material_price
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="purchase_bpa_price_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.purchaseBpaPriceId !=null">
                        when config_header_id=#{item.configHeaderId} and material_id=#{item.materialId} then #{item.purchaseBpaPriceId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="price_source =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.priceSource !=null">
                        when config_header_id=#{item.configHeaderId} and material_id=#{item.materialId} then #{item.priceSource}
                    </if>
                </foreach>
            </trim>
            <trim prefix="config_detail_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.configDetailId !=null">
                        when config_header_id=#{item.configHeaderId} and material_id=#{item.materialId} then #{item.configDetailId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="execute_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.executeId !=null">
                        when config_header_id=#{item.configHeaderId} and material_id=#{item.materialId} then #{item.executeId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="amount =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.amount !=null">
                        when config_header_id=#{item.configHeaderId} and material_id=#{item.materialId} then #{item.amount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_by =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.updateBy !=null">
                        when config_header_id=#{item.configHeaderId} and material_id=#{item.materialId} then #{item.updateBy}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_at =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.updateAt !=null">
                        when config_header_id=#{item.configHeaderId} and material_id=#{item.materialId} then #{item.updateAt}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modified_by =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.modifiedBy !=null">
                        when config_header_id=#{item.configHeaderId} and material_id=#{item.materialId} then #{item.modifiedBy}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modified_time =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.modifiedTime !=null">
                        when config_header_id=#{item.configHeaderId} and material_id=#{item.materialId} then #{item.modifiedTime}
                    </if>
                </foreach>
            </trim>
        </trim>
        where (config_header_id,material_id) in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            (#{item.configHeaderId},#{item.materialId})
        </foreach>
    </update>

    <select id="getAllPurchaseBpaPrice" resultType="com.midea.pam.common.basedata.dto.MaterialPriceDto">
        select
            pbp.id as purchaseBpaPriceId,
            pbp.ou_id as ouId,
            or2.organization_id as organizationId,
            m.id as materialId,
            m.pam_code as pamCode,
            pbp.material_code as erpCode,
            or2.currency,
            pbp.rate_type as conversionType,
            pbp.rate_date as conversionDate,
            pbp.rate as conversionRate,
            pbp.currency as sourceCurrency,
            if(STRCMP(pbp.currency,or2.currency)=0 , 1 , gdr.exchange_rate) as exchangeRate,
            round(if(STRCMP(pbp.currency,or2.currency)=0 , pbp.price_override , COALESCE(gdr.exchange_rate, 1)  * pbp.price_override) ,6 )  as amount,
            ifnull(pbp.start_date, DATE('2022-01-01 00:00:00')) as startDate,
            ifnull(pbp.end_date, DATE_ADD(SYSDATE(), interval 1 year)) as endDate
        from
            pam_ctc.purchase_bpa_price pbp
            left join pam_basedata.organization_rel or2 on
            pbp.ou_id = or2.operating_unit_id
            and or2.pam_enabled = 0
            inner join pam_basedata.material m on
            pbp.material_code = m.item_code
            and or2.organization_id = m.organization_id
            and m.material_category is not null
            left join pam_basedata.gl_daily_rate gdr on
            gdr.exchange_date =  current_date
            and gdr.exchange_type = 'Corporate'
            and gdr.from_currency = pbp.currency
            and gdr.to_currency = or2.currency
        where
            pbp.ou_id in
            <foreach collection="ouIdList" index="index" item="ouId" open="(" separator="," close=")">
                #{ouId}
            </foreach>
            and pbp.deleted_flag = 0
            and m.delete_flag = 0
    </select>

    <select id="getList" resultType="com.midea.pam.common.basedata.dto.MaterialPriceDto">
        select
            mp.id,
            mp.pam_code as pamCode,
            mp.erp_code as erpCode,
            m.name as materialName,
            m.item_info as materielDescr,
            m.unit,
            m.item_status as materialStatus,
            ld.name as materialStatusName,
            mpch.name as configHeaderName,
            (case when mpch.execution_status = 0 then '已完成'
            when mp.price_source = 2 then '运行中'
            else ''
            end ) as executionStatus,
            mp.amount,
            mp.currency,
            (case when mp.price_source = 0 then '自动更新'
            when mp.price_source = 1 then '手工单据'
            else ''
            end ) as updateType,
            ifnull(lui.name, '系统') as updateByName,
            date_format(mp.modified_time, '%Y-%m-%d') as updateAt,
            m.material_classification as materialClassification,
            m.coding_middleclass as codingMiddleClass,
            m.material_type as materialType,
            mp.ou_id as ouId,
            mp.config_header_id as configHeaderId,
            mp.organization_id as organizationId,
            or2.organization_name as organizationName,
            mp.price_is_lock as priceIsLock
        from
            pam_basedata.material_price mp
            left join pam_basedata.material m on
            mp.material_id = m.id
            left join pam_basedata.material_price_config_header mpch on
            mp.config_header_id = mpch.id
            left join pam_basedata.ltc_user_info lui on
            mp.modified_by = lui.id
            left join pam_basedata.organization_rel or2 on
            or2.organization_id = mp.organization_id
            and or2.pam_enabled = 0
            left join pam_basedata.ltc_dict ld on
            ld.code = m.item_status
            and ld.type = 'material_item_status'
        <where>
            <if test="id != null and id != ''">
                and mp.id = #{id}
            </if>
            <if test="pamCode != null and pamCode != ''">
                and mp.pam_code like concat('%',#{pamCode},'%')
            </if>
            <if test="erpCode != null and erpCode != ''">
                and mp.erp_code like concat('%',#{erpCode},'%')
            </if>
            <if test="materialName != null and materialName != ''">
                and m.name like concat('%',#{materialName},'%')
            </if>
            <if test="materielDescr != null and materielDescr != ''">
                and m.item_info like concat('%',#{materielDescr},'%')
            </if>
            <if test="materialStatusList != null and materialStatusList.size > 0">
                and m.item_status in
                <foreach collection="materialStatusList" index="index" item="materialStatus" open="(" separator="," close=")">
                    #{materialStatus}
                </foreach>
            </if>
            <if test="configHeaderName != null and configHeaderName != ''">
                and mpch.name like concat('%',#{configHeaderName},'%')
            </if>
            <if test="updateStartTime != null">
                and mp.modified_time &gt;= #{updateStartTime}
            </if>
            <if test="updateEndTime != null">
                and mp.modified_time &lt;= #{updateEndTime}
            </if>
            <if test="materialClassification != null and materialClassification != ''">
                and m.material_classification like concat('%',#{materialClassification},'%')
            </if>
            <if test="codingMiddleClass != null and codingMiddleClass != ''">
                and m.coding_middleclass like concat('%',#{codingMiddleClass},'%')
            </if>
            <if test="organizationIdList != null and organizationIdList.size > 0">
                and mp.organization_id in
                <foreach collection="organizationIdList" index="index" item="organizationId" open="(" separator="," close=")">
                    #{organizationId}
                </foreach>
            </if>
            <if test="ouIdList != null and ouIdList.size > 0">
                and mp.ou_id in
                <foreach collection="ouIdList" index="index" item="ouId" open="(" separator="," close=")">
                    #{ouId}
                </foreach>
            </if>
            <if test="priceIsLock != null">
                and mp.price_is_lock = #{priceIsLock}
            </if>
                and mp.deleted_flag = 0
        </where>
            order by mp.modified_time desc,mp.erp_code asc
    </select>

    <select id="getListByConfigHeaderIdAndMaterialId" resultMap="BaseResultMap">
        select
            mp.*
        from
            pam_basedata.material_price mp
        where
            mp.organization_id = #{organizationId}
            and mp.config_header_id = #{configHeaderId}
            <if test="erpCodeList != null and erpCodeList.size > 0">
                and mp.erp_code  in
                <foreach collection="erpCodeList" index="index" item="erpCode" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
            </if>
            and mp.deleted_flag = 0
    </select>

    <select id="getPriceConfigHeaderIdsByMaterialId" resultType="java.lang.Long">
        select
            mp.config_header_id
        from
            pam_basedata.material_price mp
        where
            mp.deleted_flag = 0
          and mp.material_id = #{materialId}
        group by
            mp.config_header_id
    </select>

    <select id="checkPriceIsLock" resultType="java.lang.Boolean">
        select exists (
            select mp.*
            from pam_basedata.material_price mp
            where
                material_id = #{materialId}
                and mp.config_header_id = #{configHeaderId}
                and mp.organization_id = #{organizationId}
                and price_is_lock = 1
                and mp.deleted_flag = 0
        )
    </select>

    <select id="selectByConfigHeaderWithCurrency" resultMap="BaseResultMap">
        select
            mp.id,
            mp.purchase_bpa_price_id,
            mp.ou_id,
            mp.organization_id,
            mp.price_source,
            mp.config_header_id,
            mp.config_detail_id,
            mp.receipt_header_id,
            mp.receipt_detail_id,
            mp.execute_id,
            mp.material_id,
            mp.pam_code,
            mp.erp_code,
            mp.currency,
            mp.conversion_date,
            mp.conversion_type,
            mp.conversion_rate,
            round(gdr.exchange_rate  * mp.amount ,6 )  as amount,
            mp.price_is_lock,
            mp.modified_by,
            mp.modified_time,
            mp.create_by,
            mp.create_at,
            mp.update_by,
            mp.update_at
        from
            pam_basedata.material_price mp
            left join  (select
                        gdr.from_currency,
                        gdr.to_currency,
                        gdr.exchange_rate
                        from
                        pam_basedata.gl_daily_rate gdr
                        where
                        gdr.exchange_date = current_date
                        and gdr.exchange_type = 'Corporate'
                        union all
                        select
                        distinct c.currency_code as from_currency,
                        c.currency_code as to_currency,
                        1 as exchange_rate
                        from
                        pam_basedata.currency c
                        where
                        c.deleted_flag = 0) gdr on
            gdr.from_currency = mp.currency
            and to_currency = #{currency}
            where config_header_id = #{configHeaderId}
    </select>

    <select id="getAllPurchaseOrderPrice" resultType="com.midea.pam.common.basedata.dto.MaterialPriceDto">
        select
            pod.id as purchaseBpaPriceId,
            po.ou_id as ouId,
            or2.organization_id as organizationId,
            pod.materiel_id as materialId,
            pod.pam_code as pamCode,
            pod.erp_code as erpCode,
            or2.currency,
            po.conversion_type as conversionType,
            po.conversion_date as conversionDate,
            po.conversion_rate as conversionRate,
            po.currency as sourceCurrency,
            if(STRCMP(po.currency, or2.currency)= 0 , 1 ,ifnull(po.conversion_rate,gdr.exchange_rate)) as exchangeRate,
            round(if(STRCMP(po.currency, or2.currency)= 0 , pod.discount_price , ifnull(po.conversion_rate,gdr.exchange_rate) * pod.discount_price) , 6 ) as amount,
            pod.create_at as startDate,
            pod.create_at as endDate
        from
            pam_ctc.purchase_order po
        left join pam_ctc.purchase_order_detail pod on
            po.id = pod.purchase_order_id
            and pod.deleted_flag = 0
        left join pam_basedata.organization_rel or2 on
            po.ou_id = or2.operating_unit_id
            and or2.pam_enabled = 0
        left join pam_basedata.gl_daily_rate gdr on
            gdr.exchange_date = current_date
            and gdr.exchange_type = 'Corporate'
            and gdr.from_currency = po.currency
            and gdr.to_currency = or2.currency
        where
            po.ou_id in
            <foreach collection="ouIdList" index="index" item="ouId" open="(" separator="," close=")">
                #{ouId}
            </foreach>
            and (pod.update_at <![CDATA[ >= ]]> #{updateAt} or pod.create_at <![CDATA[ >= ]]> #{updateAt} )
            and po.source = 2
            and po.order_status in (1, 3)
            and ifnull(pod.order_num, 0)!= 0
            and (ifnull(pod.order_num, 0) <![CDATA[<> ]]> ifnull(pod.cancel_num, 0))
            and pod.erp_code is not null
            and pod.create_at is not null
            and po.deleted_flag = 0
    </select>

</mapper>