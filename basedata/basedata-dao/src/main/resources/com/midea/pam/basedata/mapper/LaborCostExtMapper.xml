<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.basedata.mapper.LaborCostExtMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.dto.BudgetHumanDto">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
     <result column="username" jdbcType="BIGINT" property="userName" />
    <result column="function_id" jdbcType="BIGINT" property="functionId" />
    <result column="function_name" jdbcType="BIGINT" property="functionName" />
    <result column="user_attr_id" jdbcType="BIGINT" property="userAttrId" />
    <result column="user_attr_name" jdbcType="VARCHAR" property="userAttrName" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="internal_project_cost" jdbcType="DECIMAL" property="internalProjectCost" />
    <result column="external_project_cost" jdbcType="DECIMAL" property="externalProjectCost" />
    <result column="internal_development_cost" jdbcType="DECIMAL" property="internalDevelopmentCost" />
    <result column="internal_secondment_cost" jdbcType="DECIMAL" property="internalSecondmentCost" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="external_quotation" jdbcType="DECIMAL" property="externalQuotation" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="biz_unit_id" jdbcType="BIGINT" property="bizUnitId" />
      <result column="level_name" jdbcType="VARCHAR" property="levelName" />
      <result column="labor_cost_type_code" jdbcType="VARCHAR" property="laborCostTypeCode" />
      <result column="labor_cost_type_set_id" jdbcType="VARCHAR" property="laborCostTypeSetId" />
      <result column="org_role_name" jdbcType="VARCHAR" property="orgRoleName" />
  </resultMap>

    <resultMap id="LaborCostResultMap" type="com.midea.pam.common.basedata.entity.LaborCost">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="biz_unit_id" jdbcType="BIGINT" property="bizUnitId" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="type_id" jdbcType="BIGINT" property="typeId" />
        <result column="cost_type_id" jdbcType="BIGINT" property="costTypeId" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="external_quotation" jdbcType="DECIMAL" property="externalQuotation" />
        <result column="external_project_cost" jdbcType="DECIMAL" property="externalProjectCost" />
        <result column="internal_project_cost" jdbcType="DECIMAL" property="internalProjectCost" />
        <result column="internal_development_cost" jdbcType="DECIMAL" property="internalDevelopmentCost" />
        <result column="internal_secondment_cost" jdbcType="DECIMAL" property="internalSecondmentCost" />
        <result column="seq" jdbcType="INTEGER" property="seq" />
        <result column="invalid_at" jdbcType="TIMESTAMP" property="invalidAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result property="projectActivityCode" column="project_activity_code" jdbcType="VARCHAR"/>
        <result property="projectActivityId" column="project_activity_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="LaborCost_Column_List">
    id, biz_unit_id, type, type_id, cost_type_id, name, external_quotation, external_project_cost,
    internal_project_cost, internal_development_cost, internal_secondment_cost, seq,project_activity_code,project_activity_id,
    invalid_at, deleted_flag, create_by, create_user_id, create_at, update_by, update_user_id,
    update_at
  </sql>

    <select id="queryLabCostByUserId" parameterType="list" resultMap="BaseResultMap" >
        select
             a.`name`,
             a.username,
             a.id user_Id,
             b.id user_attr_id,
             b.level_name user_attr_name,
             c.external_quotation,
             c.internal_project_cost,
             c.external_project_cost,
             c.internal_development_cost,
             c.internal_secondment_cost,
             c.biz_unit_id,
             a.type,
             a.mobile,
             cp.company_name as company
        from ltc_user_info a , ltc_user_extend_attr b left  join  company cp on b.company_id = cp.id , labor_cost c
        where a.extend_attr_id = b.id AND
            c.type_id = b.id and
            a.id in (
                <foreach collection="userIds" item="userId" separator=",">
                    #{userId}
                </foreach>
            )
        order by b.id
    </select>

    <select id="queryLabCost" parameterType="com.midea.pam.common.ctc.dto.BudgetHumanDto"  resultMap="BaseResultMap">
        SELECT
            b.id user_attr_id,
            b.level_name user_attr_name,
            c.external_quotation,
            c.internal_project_cost,
            c.external_project_cost,
            c.internal_development_cost,
            c.internal_secondment_cost,
            com.company_name company,
            b.user_type type
        FROM
            ltc_user_extend_attr b
        LEFT JOIN labor_cost c ON b.id = c.type_id
        LEFT JOIN company com ON b.company_id = com.id
        WHERE
            IFNULL(b.deleted_flag, 0) = 0
        AND IFNULL(c.deleted_flag, 0) = 0
        <if test="type != null">
           AND b.user_type  = #{type}
        </if>
        <if test="bizUnitId != null">
            AND c.biz_unit_id  = #{bizUnitId}
        </if>
        ORDER BY
            b.id
    </select>

    <select id="queryLaborCostByUserId" parameterType="list" resultMap="BaseResultMap" >
        select
            ui.`name`,
            ui.username,
            ui.id user_Id,
            ui.labor_cost_role,
            ui.labor_cost_type_code,
            ui.labor_cost_type_set_id,
            ui.labor_cost_type_set_id as user_attr_id,
            case when ui.labor_cost_type_code = 'role' then ui.labor_cost_role when ui.labor_cost_type_code = 'position' then ui.position_level else null end as user_attr_name,
            case when ui.labor_cost_type_code = 'role' then ui.labor_cost_role when ui.labor_cost_type_code = 'position' then ui.position_level else null end level_name,
            case when ui.labor_cost_type_code = 'role' then t.external_quotation when ui.labor_cost_type_code = 'position' then t2.external_quotation else null end external_quotation,
            case when ui.labor_cost_type_code = 'role' then t.internal_project_cost when ui.labor_cost_type_code = 'position' then t2.internal_project_cost else null end internal_project_cost,
            case when ui.labor_cost_type_code = 'role' then t.external_project_cost when ui.labor_cost_type_code = 'position' then t2.external_project_cost else null end external_project_cost,
            case when ui.labor_cost_type_code = 'role' then t.internal_development_cost when ui.labor_cost_type_code = 'position' then t2.internal_development_cost else null end internal_development_cost,
            case when ui.labor_cost_type_code = 'role' then t.internal_secondment_cost when ui.labor_cost_type_code = 'position' then t2.internal_secondment_cost else null end internal_secondment_cost,
            t.biz_unit_id
        from ltc_user_info ui
        left join
            (select lc.*, lc.name as roleName from labor_cost lc, labor_cost_type lct
            where lc.cost_type_id = lct.id and lc.`type` = 1 and lc.deleted_flag = 0 and lc.biz_unit_id = #{companyId} ) t on ui.labor_cost_role COLLATE utf8mb4_unicode_ci = t.roleName
        left join
            (select lc.* from labor_cost lc
             where lc.`type` = 2 and lc.deleted_flag = 0 and lc.biz_unit_id = #{companyId}) t2 on ui.position_level = t2.name
        where ui.id in (
        <foreach collection="userIds" item="userId" separator=",">
            #{userId}
        </foreach>
        )
        order by ui.id
    </select>

    <select id="queryLabCostByNameRoleUnitIdType" resultMap="LaborCostResultMap">
        select
            lc.*
        from labor_cost as lc
        left join labor_cost_type as roletype on lc.cost_type_id = roletype.id
        where lc.deleted_flag=0
        <if test="name != null">
            and lc.name =  #{name}
        </if>
        <if test="role != null">
            and lc.name = #{role}
        </if>
        <if test="bizUnitId != null">
            and lc.biz_unit_id =  #{bizUnitId}
        </if>
        <if test="type != null">
            and lc.type =  #{type}
        </if>
    </select>

    <select id="queryLabCostByNameCostTypeIdUnitIdType" resultMap="LaborCostResultMap">
        select
            lc.*
        from labor_cost as lc
        where lc.deleted_flag=0
        <if test="name != null">
            and lc.name =  #{name}
        </if>
        <if test="costTypeId != null">
            and lc.cost_type_id  = #{costTypeId}
        </if>
        <if test="bizUnitId != null">
            and lc.biz_unit_id =  #{bizUnitId}
        </if>
        <if test="type != null">
            and lc.type =  #{type}
        </if>
    </select>

    <select id="getLaborCostType" resultType="com.midea.pam.common.basedata.entity.UserMesDto">
        SELECT
        t1.labor_cost_type_code AS laborCostType,
        t4.id AS userId
        FROM org_labor_cost_type_set t1
        JOIN ltc_organization t2 ON t1.org_id = t2.id
        JOIN ltc_org_user t3 ON t2.id = t3.org_id
        JOIN ltc_user_info t4 ON t3.user_id = t4.id
        WHERE 1=1
        AND t4.id IN
        <foreach collection="userIdList" item="userId" index="index" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>


    <select id="queryLaborCostByUserId_ks" parameterType="list" resultMap="BaseResultMap" >
        select
        ui.`name`,
        ui.username,
        ui.id user_Id,
        ui.labor_cost_role,
        ui.labor_cost_type_code,
        ui.labor_cost_type_set_id,
        case when ui.labor_cost_type_code = 'role' then ui.labor_cost_role else null end org_role_name,
        case when ui.labor_cost_type_code = 'position' then ui.position_level else null end level_name,
        case when ui.labor_cost_type_code = 'role' then t.external_quotation when ui.labor_cost_type_code = 'position' then t2.external_quotation else null end external_quotation,
        case when ui.labor_cost_type_code = 'role' then t.internal_project_cost when ui.labor_cost_type_code = 'position' then t2.internal_project_cost else null end internal_project_cost,
        case when ui.labor_cost_type_code = 'role' then t.external_project_cost when ui.labor_cost_type_code = 'position' then t2.external_project_cost else null end external_project_cost,
        case when ui.labor_cost_type_code = 'role' then t.internal_development_cost when ui.labor_cost_type_code = 'position' then t2.internal_development_cost else null end internal_development_cost,
        case when ui.labor_cost_type_code = 'role' then t.internal_secondment_cost when ui.labor_cost_type_code = 'position' then t2.internal_secondment_cost else null end internal_secondment_cost,
        t.biz_unit_id
        from ltc_user_info ui
        left join
        (select lc.*,lc.name as roleName from labor_cost lc left join labor_cost_type lct on  lc.cost_type_id = lct.id
        left join
        (select
        distinct olcts.*
        from
        org_labor_cost_type_set olcts
        left join ltc_org_user lou on
        lou.org_id = olcts.org_id
        where
        lou.`type` = '1'
        and olcts.labor_cost_name is not null
        and olcts.deleted_flag = '0'
        and olcts.labor_cost_type_code = 'role'
        and olcts.company_id = #{companyId}
        and lou.status = 'Y' and lou.user_id in (
        <foreach collection="userIds" item="userId" separator=",">
            #{userId}
        </foreach>
        ))ol on lc.name = ol.labor_cost_name
        where lc.`type` = 1 and lc.deleted_flag = 0 and lc.biz_unit_id = #{companyId})t on ui.labor_cost_role COLLATE utf8mb4_unicode_ci = t.roleName
        left join
        (select lc.* from labor_cost lc
        where lc.`type` = 2 and lc.deleted_flag = 0 and lc.biz_unit_id = #{companyId}) t2 on ui.position_level = t2.name
        where ui.id in (
        <foreach collection="userIds" item="userId" separator=",">
            #{userId}
        </foreach>)
        order by ui.id
    </select>

    <select id="queryLaborCostByUserId_YL" parameterType="list" resultMap="BaseResultMap" >
        select
            ui.`name`,
            ui.username,
            ui.id user_Id,
            ui.labor_cost_type_code,
            ui.labor_cost_type_set_id,
            ui.labor_cost_type_set_id as user_attr_id,
            case when ui.labor_cost_type_code = 'role' and ui.labor_cost_role is not null and ui.labor_cost_role != '' then ui.labor_cost_role
                 when ui.labor_cost_type_code = 'role' and (ui.labor_cost_role is null or ui.labor_cost_role = '') then t3.ltcPositionName
                 when ui.labor_cost_type_code = 'position' then ui.labor_cost_role else null end as labor_cost_role,
            case when ui.labor_cost_type_code = 'role' and ui.labor_cost_role is not null and ui.labor_cost_role != '' then ui.labor_cost_role
                 when ui.labor_cost_type_code = 'role' and (ui.labor_cost_role is null or ui.labor_cost_role = '') then t3.ltcPositionName
                 when ui.labor_cost_type_code = 'position' then ui.position_level else null end as user_attr_name,
            case when ui.labor_cost_type_code = 'role' and ui.labor_cost_role is not null and ui.labor_cost_role != '' then ui.labor_cost_role
                 when ui.labor_cost_type_code = 'role' and (ui.labor_cost_role is null or ui.labor_cost_role = '') then t3.ltcPositionName
                 when ui.labor_cost_type_code = 'position' then ui.position_level else null end level_name,
            case when ui.labor_cost_type_code = 'role' and ui.labor_cost_role is not null and ui.labor_cost_role != '' then t.external_quotation
                 when ui.labor_cost_type_code = 'role' and (ui.labor_cost_role is null or ui.labor_cost_role = '') then t3.external_quotation
                 when ui.labor_cost_type_code = 'position' then t2.external_quotation else null end external_quotation,
            case when ui.labor_cost_type_code = 'role' and ui.labor_cost_role is not null and ui.labor_cost_role != '' then t.internal_project_cost
                 when ui.labor_cost_type_code = 'role' and (ui.labor_cost_role is null or ui.labor_cost_role = '') then t3.internal_project_cost
                 when ui.labor_cost_type_code = 'position' then t2.internal_project_cost else null end internal_project_cost,
            case when ui.labor_cost_type_code = 'role' and ui.labor_cost_role is not null and ui.labor_cost_role != '' then t.external_project_cost
                 when ui.labor_cost_type_code = 'role' and (ui.labor_cost_role is null or ui.labor_cost_role = '') then t3.external_project_cost
                 when ui.labor_cost_type_code = 'position' then t2.external_project_cost else null end external_project_cost,
            case when ui.labor_cost_type_code = 'role' and ui.labor_cost_role is not null and ui.labor_cost_role != '' then t.internal_development_cost
                 when ui.labor_cost_type_code = 'role' and (ui.labor_cost_role is null or ui.labor_cost_role = '') then t3.internal_development_cost
                 when ui.labor_cost_type_code = 'position' then t2.internal_development_cost else null end internal_development_cost,
            case when ui.labor_cost_type_code = 'role' and ui.labor_cost_role is not null and ui.labor_cost_role != '' then t.internal_secondment_cost
                 when ui.labor_cost_type_code = 'role' and (ui.labor_cost_role is null or ui.labor_cost_role = '') then t3.internal_secondment_cost
                 when ui.labor_cost_type_code = 'position' then t2.internal_secondment_cost else null end internal_secondment_cost,
            t.biz_unit_id
        from pam_basedata.ltc_user_info ui
                 left join
             (select lc.*, lc.name as roleName from pam_basedata.labor_cost lc, pam_basedata.labor_cost_type lct
              where lc.cost_type_id = lct.id and lc.`type` = 1 and lc.deleted_flag = 0 and lc.biz_unit_id = #{companyId}) t on ui.labor_cost_role COLLATE utf8mb4_unicode_ci = t.roleName
                 left join
             (select lc.* from pam_basedata.labor_cost lc
              where lc.`type` = 2 and lc.deleted_flag = 0 and lc.biz_unit_id = #{companyId}) t2 on ui.position_level = t2.name
                 left join
             (select lc.*, lc.name as roleName,lp.name as ltcPositionName,lou.user_id as userId from pam_basedata.ltc_position lp,pam_basedata.ltc_org_user lou,pam_basedata.labor_cost lc, pam_basedata.labor_cost_type lct
              where lp.id = lou.position_id and lc.cost_type_id = lct.id and lp.name = lc.name and lc.`type` = 1 and lc.deleted_flag = 0 and lc.biz_unit_id = #{companyId} and lou.status = 'Y') t3 on t3.userId = ui.id
        where ui.id in (
        <foreach collection="userIds" item="userId" separator=",">
            #{userId}
        </foreach>
            )
        order by ui.id
    </select>

    <select id="queryLabCostByNameRoleUnitIdType_YL" resultMap="LaborCostResultMap">
        select
            lc.*
        from
            pam_basedata.ltc_position lp,
            pam_basedata.ltc_org_user lou,
            pam_basedata.labor_cost lc,
            pam_basedata.labor_cost_type lct
        where
            lp.id = lou.position_id
          and lc.cost_type_id = lct.id
          and lp.name = lc.name
          and lc.`type` = 1
          and lc.deleted_flag = 0
          and lc.biz_unit_id = #{bizUnitId}
          and lou.status = 'Y'
          and lou.user_id = #{userId}
    </select>
    <select id="selectLtcUserInfo" resultType="com.midea.pam.common.basedata.entity.UserInfo">
        select lui.*,lui.labor_cost_role as laborCostRole  from pam_basedata.ltc_user_info lui where lui.id = #{userId}
    </select>

    <select id="selectByIds" resultMap="LaborCostResultMap">
        select
        id,
        name,
        external_quotation,
        external_project_cost,
        internal_project_cost,
        internal_development_cost,
        internal_secondment_cost,
        project_activity_id,
        project_activity_code
        from labor_cost
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and (invalid_at is null or invalid_at > current_date())
        and (deleted_flag is null or deleted_flag = 0)
    </select>

    <select id="selectByNames" resultMap="LaborCostResultMap">
        select
        id,
        name,
        external_quotation,
        external_project_cost,
        internal_project_cost,
        internal_development_cost,
        internal_secondment_cost,
        project_activity_id,
        project_activity_code
        from labor_cost
        where name in
        <foreach collection="names" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
        and (invalid_at is null or invalid_at > current_date())
        and (deleted_flag is null or deleted_flag = 0)
        and biz_unit_id = #{unitId}
        group by name
    </select>

    <select id="getMaxSeq" resultType="java.lang.Integer">
        select max(seq) from labor_cost
    </select>

    <select id="selectByUnitIds" resultType="com.midea.pam.common.basedata.dto.LaborCostDto">
        select id, name,
        biz_unit_id as bizUnitId,
        project_activity_id as projectActivityId,
        project_activity_code as projectActivityCode
        from labor_cost
        where type = 1
        and (invalid_at is null or invalid_at > current_date())
        <if test="unitIds != null and unitIds.size>0">
            and biz_unit_id in
            <foreach collection="unitIds" item="unitId" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>
        and (deleted_flag is null or deleted_flag = 0)
    </select>

    <insert id="batchInsert" parameterType="com.midea.pam.common.basedata.entity.LaborCost">
        insert into labor_cost (id, biz_unit_id, type,
            type_id, cost_type_id, name,
            external_quotation, external_project_cost,
            internal_project_cost, internal_development_cost,
            internal_secondment_cost, seq, invalid_at,
            deleted_flag, create_by, create_user_id,
            create_at, update_by, update_user_id,
            update_at, project_activity_id, project_activity_code,
            role_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.bizUnitId,jdbcType=BIGINT}, #{item.type,jdbcType=INTEGER},
            #{item.typeId,jdbcType=BIGINT}, #{item.costTypeId,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR},
            #{item.externalQuotation,jdbcType=DECIMAL}, #{item.externalProjectCost,jdbcType=DECIMAL},
            #{item.internalProjectCost,jdbcType=DECIMAL}, #{item.internalDevelopmentCost,jdbcType=DECIMAL},
            #{item.internalSecondmentCost,jdbcType=DECIMAL}, #{item.seq,jdbcType=INTEGER}, #{item.invalidAt,jdbcType=TIMESTAMP},
            #{item.deletedFlag,jdbcType=TINYINT}, #{item.createBy,jdbcType=BIGINT}, #{item.createUserId,jdbcType=VARCHAR},
            #{item.createAt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT}, #{item.updateUserId,jdbcType=VARCHAR},
            #{item.updateAt,jdbcType=TIMESTAMP}, #{item.projectActivityId,jdbcType=BIGINT}, #{item.projectActivityCode,jdbcType=VARCHAR},
            #{item.roleType,jdbcType=INTEGER})
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
        update labor_cost
        <set>
            <if test="item.bizUnitId != null">
                biz_unit_id = #{item.bizUnitId,jdbcType=BIGINT},
            </if>
            <if test="item.type != null">
                type = #{item.type,jdbcType=INTEGER},
            </if>
            <if test="item.typeId != null">
                type_id = #{item.typeId,jdbcType=BIGINT},
            </if>
            <if test="item.externalQuotation != null">
                external_quotation = #{item.externalQuotation,jdbcType=DECIMAL},
            </if>
            <if test="item.externalProjectCost != null">
                external_project_cost = #{item.externalProjectCost,jdbcType=DECIMAL},
            </if>
            <if test="item.internalProjectCost != null">
                internal_project_cost = #{item.internalProjectCost,jdbcType=DECIMAL},
            </if>
            <if test="item.internalDevelopmentCost != null">
                internal_development_cost = #{item.internalDevelopmentCost,jdbcType=DECIMAL},
            </if>
            <if test="item.internalSecondmentCost != null">
                internal_secondment_cost = #{item.internalSecondmentCost,jdbcType=DECIMAL},
            </if>
            <if test="item.seq != null">
                seq = #{item.seq,jdbcType=INTEGER},
            </if>
            <if test="item.invalidAt != null">
                invalid_at = #{item.invalidAt,jdbcType=TIMESTAMP},
            </if>
            <if test="item.deletedFlag != null">
                deleted_flag = #{item.deletedFlag,jdbcType=TINYINT},
            </if>
            <if test="item.createBy != null">
                create_by = #{item.createBy,jdbcType=BIGINT},
            </if>
            <if test="item.createUserId != null">
                create_user_id = #{item.createUserId,jdbcType=VARCHAR},
            </if>
            <if test="item.createAt != null">
                create_at = #{item.createAt,jdbcType=TIMESTAMP},
            </if>
            <if test="item.updateBy != null">
                update_by = #{item.updateBy,jdbcType=BIGINT},
            </if>
            <if test="item.updateUserId != null">
                update_user_id = #{item.updateUserId,jdbcType=VARCHAR},
            </if>
            <if test="item.updateAt != null">
                update_at = #{item.updateAt,jdbcType=TIMESTAMP},
            </if>
            <if test="item.projectActivityCode != null">
                project_activity_code = #{item.projectActivityCode,jdbcType=VARCHAR},
            </if>
            <if test="item.projectActivityId != null">
                project_activity_id = #{item.projectActivityId,jdbcType=BIGINT},
            </if>
            <if test="item.roleType != null">
                role_type = #{item.roleType,jdbcType=INTEGER},
            </if>
        </set>
        where
            cost_type_id = #{item.costTypeId,jdbcType=BIGINT}
            and name = #{item.name,jdbcType=VARCHAR}
        </foreach>
    </update>

</mapper>