<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.basedata.mapper.MaterialExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.basedata.entity.Material">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="item_id" jdbcType="BIGINT" property="itemId"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="item_info" jdbcType="VARCHAR" property="itemInfo"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="item_type" jdbcType="VARCHAR" property="itemType"/>
        <result column="item_status" jdbcType="VARCHAR" property="itemStatus"/>
        <result column="delist_flag" jdbcType="BIT" property="delistFlag"/>
        <result column="buyer_number" jdbcType="VARCHAR" property="buyerNumber"/>
        <result column="buyer_id" jdbcType="VARCHAR" property="buyerId"/>
        <result column="buyer_round" jdbcType="VARCHAR" property="buyerRound"/>
        <result column="bond_flag" jdbcType="VARCHAR" property="bondFlag"/>
        <result column="code_name" jdbcType="VARCHAR" property="codeName"/>
        <result column="item_code_old" jdbcType="VARCHAR" property="itemCodeOld"/>
        <result column="fixed_lot_multiplier" jdbcType="VARCHAR" property="fixedLotMultiplier"/>
        <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType"/>
        <result column="pur_type" jdbcType="VARCHAR" property="purType"/>
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode"/>
        <result column="business_categories_id" jdbcType="BIGINT" property="businessCategoriesId"/>
        <result column="business_categories" jdbcType="VARCHAR" property="businessCategories"/>
        <result column="material_classification_id" jdbcType="BIGINT" property="materialClassificationId"/>
        <result column="material_classification" jdbcType="VARCHAR" property="materialClassification"/>
        <result column="material_type_id" jdbcType="BIGINT" property="materialTypeId"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="machining_part_type" jdbcType="VARCHAR" property="machiningPartType"/>
        <result column="material" jdbcType="VARCHAR" property="material"/>
        <result column="unit_weight" jdbcType="DECIMAL" property="unitWeight"/>
        <result column="material_processing" jdbcType="VARCHAR" property="materialProcessing"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="delete_flag" jdbcType="BIT" property="deleteFlag"/>
        <result column="erp_code" jdbcType="VARCHAR" property="erpCode"/>
        <result column="erp_message" jdbcType="VARCHAR" property="erpMessage"/>
        <result column="brand_material_code" jdbcType="VARCHAR" property="brandMaterialCode"/>
        <result column="material_category" jdbcType="VARCHAR" property="materialCategory"/>
        <result column="figure_number" jdbcType="VARCHAR" property="figureNumber"/>
        <result column="chart_version" jdbcType="VARCHAR" property="chartVersion"/>
        <result column="or_spare_parts_mask" jdbcType="VARCHAR" property="orSparePartsMask"/>
        <result column="minimum_order_quantity" jdbcType="BIGINT" property="minimumOrderQuantity"/>
        <result column="receving_subinventory" jdbcType="VARCHAR" property="recevingSubinventory"/>
        <result column="shelves" jdbcType="VARCHAR" property="shelves"/>
        <result column="sourcer" jdbcType="VARCHAR" property="sourcer"/>
        <result column="safety_stock_quantity" jdbcType="BIGINT" property="safetyStockQuantity"/>
        <result column="coding_middleclass" jdbcType="VARCHAR" property="codingMiddleclass"/>
        <result column="coding_middleclass_id" jdbcType="BIGINT" property="codingMiddleclassId"/>
        <result column="material_attribute" jdbcType="VARCHAR" property="materialAttribute" />
    </resultMap>
    <resultMap id="BaseResultDtoMap" type="com.midea.pam.common.basedata.dto.MaterialDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName"/>
        <result column="item_id" jdbcType="BIGINT" property="itemId"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="item_info" jdbcType="VARCHAR" property="itemInfo"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="item_type" jdbcType="VARCHAR" property="itemType"/>
        <result column="item_type_name" jdbcType="VARCHAR" property="itemTypeName"/>
        <result column="item_status" jdbcType="VARCHAR" property="itemStatus"/>
        <result column="delist_flag" jdbcType="BIT" property="delistFlag"/>
        <result column="item_status_name" jdbcType="VARCHAR" property="itemStatusName"/>
        <result column="buyer_number" jdbcType="VARCHAR" property="buyerNumber"/>
        <result column="buyer_id" jdbcType="VARCHAR" property="buyerId"/>
        <result column="buyer_round" jdbcType="VARCHAR" property="buyerRound"/>
        <result column="bond_flag" jdbcType="VARCHAR" property="bondFlag"/>
        <result column="code_name" jdbcType="VARCHAR" property="codeName"/>
        <result column="item_cost" jdbcType="DECIMAL" property="itemCost"/>
        <result column="material_cost_type" jdbcType="INTEGER" property="materialCostType"/>
        <result column="item_code_old" jdbcType="VARCHAR" property="itemCodeOld"/>
        <result column="fixed_lot_multiplier" jdbcType="VARCHAR" property="fixedLotMultiplier"/>
        <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType"/>
        <result column="pur_type" jdbcType="VARCHAR" property="purType"/>
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode"/>
        <result column="business_categories_id" jdbcType="BIGINT" property="businessCategoriesId"/>
        <result column="business_categories" jdbcType="VARCHAR" property="businessCategories"/>
        <result column="material_classification_id" jdbcType="BIGINT" property="materialClassificationId"/>
        <result column="material_classification" jdbcType="VARCHAR" property="materialClassification"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="machining_part_type" jdbcType="VARCHAR" property="machiningPartType"/>
        <result column="material" jdbcType="VARCHAR" property="material"/>
        <result column="unit_weight" jdbcType="DECIMAL" property="unitWeight"/>
        <result column="material_processing" jdbcType="VARCHAR" property="materialProcessing"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="delete_flag" jdbcType="BIT" property="deleteFlag"/>
        <result column="erp_code" jdbcType="VARCHAR" property="erpCode"/>
        <result column="erp_message" jdbcType="VARCHAR" property="erpMessage"/>
        <result column="material_category" jdbcType="VARCHAR" property="materialCategory"/>
        <result column="figure_number" jdbcType="VARCHAR" property="figureNumber"/>
        <result column="chart_version" jdbcType="VARCHAR" property="chartVersion"/>
        <result column="brand_material_code" jdbcType="VARCHAR" property="brandMaterialCode"/>
        <result column="or_spare_parts_mask" jdbcType="VARCHAR" property="orSparePartsMask"/>
        <result column="coding_middleclass" jdbcType="VARCHAR" property="codingMiddleclass"/>
        <result column="material_attribute" jdbcType="VARCHAR" property="materialAttribute" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_by_mip" jdbcType="VARCHAR" property="createByMip" />
        <result column="create_by_user_name" jdbcType="VARCHAR" property="createByUserName" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, organization_id, item_id, item_code, item_info, unit, item_type, item_status,delist_flag,
        buyer_number, buyer_id, buyer_round, bond_flag, code_name, item_code_old, fixed_lot_multiplier,
        inventory_type, pur_type, pam_code, business_categories_id, business_categories,
        material_classification_id, material_classification, material_type_id, material_type,
        machining_part_type, material, unit_weight, material_processing,
        name, model, brand, create_by, create_at, update_by, update_at, delete_flag, erp_code, erp_message,brand_material_code
    </sql>

    <sql id="New_Material_Column_List">
        id, organization_id, item_id, item_code, item_info, unit, item_type, pam_code, material_classification, material_type,
        coding_middleclass, machining_part_type, material, unit_weight, material_processing, name, model, brand,
        material_category, figure_number,delist_flag,delete_flag,brand_material_code
    </sql>

    <select id="getMaterialListByItemInfo" parameterType="java.util.Map"
            resultMap="BaseResultDtoMap">
        select
        org.organization_name,
        ma.*
        from material ma
        left join organization_rel org ON org.organization_id=ma.organization_id
        where
        ma.delete_flag = '0'
        and ma.item_status = 'Active'
        and ( org.organization_code = 'MM0' or (org.organization_code = 'MA2' and ma.pam_code is not null and
        ma.item_code is null) )
        <if test="itemInfo != null">
            and ma.item_info like concat('%', #{itemInfo}, '%')
        </if>
    </select>

    <select id="getMaterialListByOrgAndTicketTaskId" parameterType="java.util.Map"
            resultMap="BaseResultDtoMap">
        select
        (ifnull(mg.getNumber,0) - ifnull(mr.returnNumber,0)) as returnMaterialsNum,
        ttd.id as ticketTasksDetailId,
        ma.*,
        lui.username as create_by_mip,
        lui.name as create_by_user_name
        from
        pam_ctc.ticket_tasks_detail ttd
        inner join pam_basedata.material ma on
        ma.item_code = ttd.erp_code
        left join pam_basedata.material_cost mc on
        mc.organization_id = ma.organization_id
        and mc.item_code = ma.item_code
        <if test="materialCostType != null">
            and mc.material_cost_type = #{materialCostType}
        </if>
        left join (
        select
        mgh.ticket_task_id,
        mgd.material_code,
        sum(ifnull(mgd.actual_amount, 0)) as getNumber
        from
        pam_ctc.material_get_header mgh
        inner join pam_ctc.material_get_detail mgd on
        mgh.id = mgd.header_id
        where
        mgh.status = 5
        and mgh.deleted_flag = 0
        and mgd.deleted_flag = 0
        group by
        material_code,
        ticket_task_id) mg on
        mg.ticket_task_id = ttd.ticket_tasks_id and mg.material_code = ttd.erp_code
        left join (
        select
        mrh.ticket_task_id,
        mrd.material_code,
        sum((case when mrh.status in (1, 2, 3, 4) then ifnull(mrd.apply_amount, 0) when mrh.status = 5 then
        ifnull(mrd.actual_amount, 0) else 0 end)) as returnNumber
        from
        pam_ctc.material_return_header mrh
        inner join pam_ctc.material_return_detail mrd on
        mrh.id = mrd.header_id
        where
        mrh.deleted_flag = 0
        and mrd.deleted_flag = 0
        group by
        mrd.material_code,
        mrh.ticket_task_id) mr on
        mr.ticket_task_id = ttd.ticket_tasks_id and mr.material_code = ttd.erp_code
        left join pam_basedata.ltc_user_info lui on ma.create_by = lui.id
        where
        ttd.deleted_flag = 0
        and (ma.item_status = 'Active' or ma.item_status is null)
        and ttd.ticket_tasks_id = #{ticketTaskId}
        <if test="organizationId != null">
            and ma.organization_id = #{organizationId}
        </if>
        <if test="fuzzyLike != null">
            and ( ma.item_info like concat('%', #{fuzzyLike}, '%') or ma.item_code like concat('%', #{fuzzyLike}, '%') )
        </if>
        <if test="itemCodeList != null and itemCodeList.size() > 0">
            and ma.item_code in
            <foreach collection="itemCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by ttd.id
    </select>

    <select id="getMaterialSync" parameterType="java.util.Map"
            resultMap="BaseResultDtoMap">
        select
        distinct
        ma.*
        from material ma
        left join material_cost mc ON mc.organization_id = ma.organization_id and mc.pam_code = ma.pam_code
        where
        ma.delete_flag = '0'
        and mc.deleted_flag = 0
        and mc.material_cost_type = 3
        and mc.item_cost is not null
        and (ma.id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        or ma.erp_code = 2)
    </select>

    <select id="count" parameterType="com.midea.pam.common.basedata.query.MaterialQuery"
            resultType="java.lang.Long">
        select
        count(ma.id)
        from material ma
        left join organization_rel org ON org.organization_id=ma.organization_id
        left join ltc_dict d1 on d1.code=ma.item_type and d1.type='material_item_type'
        left join ltc_dict d2 on d2.code=ma.item_status and d2.type='material_item_status'
        where
        delete_flag='0'
        <if test="organizationName != null">
            and org.organization_name like concat('%', #{organizationName}, '%')
        </if>
        <if test="itemInfo != null">
            and (ma.item_info like concat('%', #{itemInfo}, '%') or ma.item_code like concat('%', #{itemInfo}, '%'))
        </if>
        <if test="itemType != null">
            and ma.item_type = #{itemType,jdbcType=VARCHAR}
        </if>
        <if test="itemStatus != null">
            and ma.item_status = #{itemStatus,jdbcType=VARCHAR}
        </if>
        <if test="organizationId != null">
            and ma.organization_id = #{organizationId,jdbcType=BIGINT}
        </if>
        <if test="pamCode != null">
            and ma.pam_code = #{pamCode,jdbcType=VARCHAR}
        </if>
        <if test="organizationCode != null">
            and org.organization_code = #{organizationCode,jdbcType=VARCHAR}
        </if>


        <if test="name != null">
            and ma.name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="model != null">
            and ma.model = #{model,jdbcType=VARCHAR}
        </if>
        <if test="brand != null">
            and ma.brand = #{brand,jdbcType=VARCHAR}
        </if>

        <if test="materialType != null">
            and ma.material_type = #{materialType,jdbcType=VARCHAR}
        </if>

        <if test="fuzzyName != null or fuzzyModel != null">
            and
            (1!=1
            <if test="fuzzyName != null">
                or ma.name like concat('%', #{fuzzyName}, '%')
            </if>
            <if test="fuzzyModel != null">
                or ma.model like concat('%', #{fuzzyModel}, '%')
            </if>
            )
        </if>
        <if test="secFuzzyMatch != null">
            and (
            ma.name like concat('%', #{secFuzzyMatch}, '%')
            or ma.model like concat('%', #{secFuzzyMatch}, '%')
            )
        </if>
        <if test="itemCodeFrom != null">
            <![CDATA[ and  ma.item_code >=#{itemCodeFrom,jdbcType=VARCHAR}]]>
        </if>
        <if test="itemCodeTo != null">
            <![CDATA[ and ma.item_code <= #{itemCodeTo,jdbcType=VARCHAR}]]>
        </if>
    </select>

    <select id="getMaterialList" parameterType="com.midea.pam.common.basedata.query.MaterialQuery"
            resultMap="BaseResultDtoMap">
        select distinct
        org.organization_name,
        ma.id, ma.organization_id, ma.item_id, ma.item_code, ma.item_info, ma.unit, ma.item_type,
        ma.item_status,ma.delist_flag,
        ma.buyer_number, ma.buyer_id, ma.buyer_round, ma.bond_flag, ma.code_name, ma.item_code_old,
        ma.fixed_lot_multiplier,
        ma.inventory_type, ma.pur_type, ma.pam_code, ma.business_categories_id, ma.business_categories,
        ma.material_classification_id, ma.material_classification, ma.material_type_id, ma.material_type,
        ma.machining_part_type, ma.material, ma.unit_weight, ma.material_processing,
        ma.name, ma.model, ma.brand, ma.create_by, ma.create_at, ma.update_by, ma.update_at, ma.delete_flag,
        ma.erp_code, ma.erp_message, ma.material_attribute, ma.remark,
        ma.material_category,ma.figure_number,ma.chart_version,ma.brand_material_code,ma.or_spare_parts_mask,ma.coding_middleclass,
        d1.name as item_type_name,
        d2.name as item_status_name,ma.maximum_draw_version_num as maximumDrawVersionNum
        from material ma
        inner join organization_rel org ON org.organization_id=ma.organization_id
        left join ltc_dict d1 on d1.code=ma.item_type and d1.type='material_item_type'
        left join ltc_dict d2 on d2.code=ma.item_status and d2.type='material_item_status'
        where
        delete_flag='0'
        <if test="id != null">
            and ma.id = #{id,jdbcType=BIGINT}
        </if>
        <if test="organizationName != null">
            and org.organization_name like concat('%', #{organizationName}, '%')
        </if>
        <if test="itemInfo != null">
            and (ma.item_info like concat('%', #{itemInfo}, '%') or ma.item_code like concat('%', #{itemInfo}, '%'))
        </if>
        <if test="itemType != null">
            and ma.item_type = #{itemType,jdbcType=VARCHAR}
        </if>
        <if test="itemStatus != null">
            and ma.item_status = #{itemStatus,jdbcType=VARCHAR}
        </if>
        <if test="delistFlag != null">
            and ma.delist_flag = #{delistFlag,jdbcType=BIT}
        </if>
        <if test="organizationId != null">
            and ma.organization_id = #{organizationId,jdbcType=BIGINT}
        </if>
        <if test="organizationIdList != null and organizationIdList.size > 0">
            and ma.organization_id in
            <foreach collection="organizationIdList" item="organizationId" index="index" open="(" separator=","
                     close=")">
                #{organizationId}
            </foreach>
        </if>
        <if test="pamCodeList != null and pamCodeList.size > 0">
            and ma.pam_code in
            <foreach collection="pamCodeList" item="pamCode" index="index" open="(" separator="," close=")">
                #{pamCode}
            </foreach>
        </if>
        <if test="pamCode != null">
            and ma.pam_code = #{pamCode,jdbcType=VARCHAR}
        </if>
        <if test="organizationCode != null">
            and (org.organization_code = #{organizationCode,jdbcType=VARCHAR} or org.organization_code = 'MF2')
        </if>

        <if test="name != null">
            and ma.name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="model != null">
            and ma.model = #{model,jdbcType=VARCHAR}
        </if>
        <if test="brand != null">
            and ma.brand = #{brand,jdbcType=VARCHAR}
        </if>
        <if test="materialType != null">
            and ma.material_type = #{materialType,jdbcType=VARCHAR}
        </if>

        <if test="machiningPartType != null">
            and ma.machining_part_type = #{machiningPartType,jdbcType=VARCHAR}
        </if>
        <if test="unitWeight != null">
            and ma.unit_weight = #{unitWeight,jdbcType=VARCHAR}
        </if>
        <if test="materialProcessing != null">
            and ma.material_processing = #{materialProcessing,jdbcType=VARCHAR}
        </if>
        <if test="material != null">
            and ma.material = #{material,jdbcType=VARCHAR}
        </if>

        <if test="fuzzyName != null or fuzzyModel != null">
            and
            (1!=1
            <if test="fuzzyName != null">
                or ma.name like concat('%', #{fuzzyName}, '%')
            </if>
            <if test="fuzzyModel != null">
                or ma.model like concat('%', #{fuzzyModel}, '%')
            </if>
            )
        </if>
        <if test="secFuzzyMatch != null">
            and (
            ma.name like concat('%', #{secFuzzyMatch}, '%')
            or ma.model like concat('%', #{secFuzzyMatch}, '%')
            )
        </if>
        <if test="brandMaterialCode != null">
            and (
            ma.brand_material_code like concat('%', #{brandMaterialCode}, '%')
            )
        </if>
        <if test="itemCodeFrom != null">
            <![CDATA[ and  ma.item_code >=#{itemCodeFrom,jdbcType=VARCHAR}]]>
        </if>
        <if test="itemCodeTo != null">
            <![CDATA[ and ma.item_code <= #{itemCodeTo,jdbcType=VARCHAR}]]>
        </if>
        <if test="groupByFlag != null and groupByFlag == true">
            group by ma.pam_code
        </if>
    </select>

    <select id="getNotDeletedMaterialByPamCodeOrErpCode" resultMap="BaseResultDtoMap">
        select distinct
        org.organization_name,
        ma.id, ma.organization_id, ma.item_id, ma.item_code, ma.item_info, ma.unit, ma.item_type,
        ma.item_status,ma.delist_flag,
        ma.buyer_number, ma.buyer_id, ma.buyer_round, ma.bond_flag, ma.code_name, ma.item_code_old,
        ma.fixed_lot_multiplier,
        ma.inventory_type, ma.pur_type, ma.pam_code, ma.business_categories_id, ma.business_categories,
        ma.material_classification_id, ma.material_classification, ma.material_type_id, ma.material_type,
        ma.machining_part_type, ma.material, ma.unit_weight, ma.material_processing,
        ma.name, ma.model, ma.brand, ma.create_by, ma.create_at, ma.update_by, ma.update_at, ma.delete_flag,
        ma.erp_code, ma.erp_message, ma.material_attribute,
        ma.material_category,ma.figure_number,ma.chart_version,ma.brand_material_code,ma.or_spare_parts_mask,ma.coding_middleclass,
        d1.name as item_type_name,
        d2.name as item_status_name,ma.maximum_draw_version_num as maximumDrawVersionNum
        from material ma
        inner join organization_rel org ON org.organization_id=ma.organization_id
        left join ltc_dict d1 on d1.code=ma.item_type and d1.type='material_item_type'
        left join ltc_dict d2 on d2.code=ma.item_status and d2.type='material_item_status'
        where
        delete_flag = 0
        <if test="organizationId != null">
            and ma.organization_id = #{organizationId}
        </if>
        and (ma.pam_code = #{pamCode}
        <if test="erpCode != null">
            or ma.item_code = #{erpCode}
        </if>
        )
    </select>

    <update id="erpSyncUpdate" parameterType="com.midea.pam.common.basedata.entity.Material">
        update material
        set update_at = now(),
        erp_code = #{erpCode},
        erp_message = #{erpMessage}
        where id = #{id} and erp_code != '2'

    </update>


    <update id="updateMaterialAttr" parameterType="java.util.Map">
        UPDATE pam_basedata.material m
        INNER JOIN pam_ctc.material_adjust_detail mad ON m.pam_code = mad.pam_code and m.organization_id =
        #{organizationId}
        SET m.chart_version = ifnull(mad.chart_version_new, m.chart_version),
        m.unit_weight = ifnull(mad.unit_weight_new, m.unit_weight),
        m.machining_part_type = ifnull(mad.machining_part_type_new, m.machining_part_type),
        m.material_processing = ifnull(mad.surface_handle_new, m.material_processing),
        m.material = ifnull(mad.material_new, m.material),
        m.or_spare_parts_mask = ifnull(mad.or_spare_parts_mask_new, m.or_spare_parts_mask)
        where mad.header_id = #{headerId};
    </update>

    <select id="judgeStandardItem" resultType="java.lang.Integer">
        select count(*) from pam_basedata.material
        where organization_id = #{organizationId}
        and coding_middleclass = #{materialMiddleClass}
        and upper(figure_number) = upper(#{figureNumber})
    </select>

    <select id="judgeProcessItem" resultType="java.lang.Integer">
        select count(*) from pam_basedata.material
        where organization_id = #{organizationId}
        and coding_middleclass = #{materialMiddleClass}
        and brand_material_code = #{brandMaterialCode}
    </select>

    <select id="materialPageByOrg" parameterType="com.midea.pam.common.basedata.query.MaterialQuery"
            resultMap="BaseResultDtoMap">
        select m.*
        from pam_basedata.material m
        where m.material_category is not null and m.organization_id = #{organizationId}
        <if test="pamCode != null">
            and m.pam_code like concat('%', #{pamCode}, '%')
        </if>
        <if test="erpCode != null">
            and m.item_code like concat('%', #{erpCode}, '%')
        </if>
    </select>

    <select id="materialPageByCodeAndOu" parameterType="com.midea.pam.common.basedata.query.MaterialQuery"
            resultMap="BaseResultDtoMap">
        select
        m.*
        from
        pam_basedata.material m
        left join pam_basedata.organization_rel or2 on
        m.organization_id = or2.organization_id
        where m.material_category is not null and or2.operating_unit_id = #{ouId}
        <if test="pamCode != null">
            and m.pam_code like concat('%', #{pamCode}, '%')
        </if>
        <if test="erpCode != null">
            and m.item_code like concat('%', #{erpCode}, '%')
        </if>
        and m.delete_flag = 0
    </select>


    <update id="updateMaterialTypeById">
        UPDATE material
        SET material_category = #{MaterialTypeNew},
        item_type = #{itemType}
        where id = #{MaterialId}
    </update>

    <select id="judgeUniqueCheckType2" resultMap="BaseResultMap">
        select * from material
        where organization_id = #{organizationId}
        and brand = #{brand}
        and model = #{model}
        and delist_flag = 0
        and delete_flag =0
        <if test="brandMaterialCode != null and brandMaterialCode != ''">
            and brand_material_code = #{brandMaterialCode}
        </if>
        order by create_at desc limit 1
    </select>

    <select id="judgeUniqueCheckType3" resultMap="BaseResultMap">
        select * from material
        where organization_id = #{organizationId}
        and figure_number = #{figureNumber}
        and chart_version = #{chartVersion}
        and delist_flag = 0
        and delete_flag =0
        order by create_at desc limit 1
    </select>

    <select id="judgeAssemblyPartUnique" resultMap="BaseResultMap">
        select * from material
        where organization_id = #{organizationId}
        and name = #{name}
        and figure_number = #{figureNumber}
        and chart_version = #{chartVersion}
        and delist_flag = 0
        and delete_flag =0
        order by create_at desc limit 1
    </select>

    <select id="judgeUniqueCheckType4" resultMap="BaseResultMap">
        select * from material
        where organization_id = #{organizationId}
        and brand_material_code = #{brandMaterialCode}
        and brand = #{brand}
        and delist_flag = 0
        and delete_flag =0
        order by create_at desc limit 1
    </select>

    <select id="checkMaterial1" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and figure_number = #{figureNumber}
        and delist_flag = 0
        and delete_flag = 0
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
        <if test="secFuzzyMatch != null">
            and (
            name like concat('%', #{secFuzzyMatch}, '%')
            or model like concat('%', #{secFuzzyMatch}, '%')
            )
        </if>
        <if test="itemInfo != null">
            and (item_info like concat('%', #{itemInfo}, '%') or item_code like concat('%', #{itemInfo}, '%'))
        </if>
    </select>

    <select id="checkMaterial2" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and model = #{model}
        and delist_flag = 0
        and delete_flag = 0
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
        <if test="secFuzzyMatch != null">
            and (
            name like concat('%', #{secFuzzyMatch}, '%')
            or model like concat('%', #{secFuzzyMatch}, '%')
            )
        </if>
        <if test="itemInfo != null">
            and (item_info like concat('%', #{itemInfo}, '%') or item_code like concat('%', #{itemInfo}, '%'))
        </if>
    </select>

    <select id="checkMaterial3" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and brand_material_code = #{brandMaterialCode}
        and delist_flag = 0
        and delete_flag = 0
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
        <if test="secFuzzyMatch != null">
            and (
            name like concat('%', #{secFuzzyMatch}, '%')
            or model like concat('%', #{secFuzzyMatch}, '%')
            )
        </if>
        <if test="itemInfo != null">
            and (item_info like concat('%', #{itemInfo}, '%') or item_code like concat('%', #{itemInfo}, '%'))
        </if>
    </select>

    <select id="checkMaterialNew1" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and figure_number = #{figureNumber}
        and delist_flag = 0
        and delete_flag = 0
        <if test="materialCategoryList != null and materialCategoryList.size() > 0">
            and material_category in
            <foreach collection="materialCategoryList" index="index" item="materialCategory" open="(" separator=","
                     close=")">
                #{materialCategory}
            </foreach>
        </if>
        order by create_at desc limit 1
    </select>

    <select id="checkMaterialNew2" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and model = #{model}
        and delist_flag = 0
        and delete_flag = 0
        <if test="materialCategoryList != null and materialCategoryList.size() > 0">
            and material_category in
            <foreach collection="materialCategoryList" index="index" item="materialCategory" open="(" separator=","
                     close=")">
                #{materialCategory}
            </foreach>
        </if>
        order by create_at desc limit 1
    </select>

    <select id="checkMaterialNew3" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and brand_material_code = #{brandMaterialCode}
        and delist_flag = 0
        and delete_flag = 0
        <if test="materialCategoryList != null and materialCategoryList.size() > 0">
            and material_category in
            <foreach collection="materialCategoryList" index="index" item="materialCategory" open="(" separator=","
                     close=")">
                #{materialCategory}
            </foreach>
        </if>
        order by create_at desc limit 1
    </select>

    <select id="checkMaterialAd1" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and figure_number = #{figureNumber}
        and delist_flag = 0
        and delete_flag = 0
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
    </select>

    <select id="checkMaterialAd2" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and model = #{model}
        and delist_flag = 0
        and delete_flag = 0
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
    </select>

    <select id="checkMaterialAd3" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and brand_material_code = #{brandMaterialCode}
        and delist_flag = 0
        and delete_flag = 0
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
    </select>

    <select id="checkMaterialAd4" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and figure_number = #{figureNumber}
        and (delist_flag = 1 or delete_flag = 1)
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
    </select>

    <select id="checkMaterialAd5" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and model = #{model}
        and (delist_flag = 1 or delete_flag = 1)
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
    </select>

    <select id="checkMaterialAd6" resultMap="BaseResultMap">
        select *
        from material
        where coding_middleclass = #{codingMiddleClass}
        and brand = #{brand}
        and brand_material_code = #{brandMaterialCode}
        and (delist_flag = 1 or delete_flag = 1)
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
    </select>

    <select id="getIdByOrganizationIdAndItemCode" resultType="java.lang.Long">
        select id
        from material
        where delete_flag = 0
        and organization_id = #{organizationId}
        and item_code = #{itemCode}
    </select>
    <select id="getMaterialListByOrg" parameterType="java.util.Map"
            resultMap="BaseResultDtoMap">
        select
        distinct
        ma.*,
        lui.username as create_by_mip,
        lui.name as create_by_user_name
        from material ma
        left join material_cost mc ON mc.organization_id = ma.organization_id and mc.item_code = ma.item_code
        left join pam_basedata.ltc_user_info lui on ma.create_by = lui.id
        where
        delete_flag = '0'
        and ma.item_status = 'Active'
        <if test="organizationId != null">
            and ma.organization_id = #{organizationId}
        </if>
        <if test="fuzzyLike != null">
            and ( ma.item_info like concat('%', #{fuzzyLike}, '%') or ma.item_code like concat('%', #{fuzzyLike}, '%') )
        </if>
        <if test="fuzzyLikeDelist != null">
            and ( ma.item_info like concat('%', #{fuzzyLikeDelist}, '%') or ma.item_code like concat('%', #{fuzzyLikeDelist}, '%') or ma.pam_code like concat('%', #{fuzzyLikeDelist}, '%') )
        </if>
        <if test="materialCostType != null">
            and mc.material_cost_type = #{materialCostType}
        </if>
        <if test="itemCodeList != null and itemCodeList.size() > 0">
            and ma.item_code in
            <foreach collection="itemCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by ma.item_code
    </select>

    <select id="getNotSuccessErpCodeList" resultType="java.lang.String" parameterType="map">
        select item_code
        from material
        where delete_flag = 0
        and erp_code != '1'
        <if test="organizationId != null">
            and organization_id = #{organizationId}
        </if>
        <if test="erpCodeList != null and erpCodeList.size() > 0">
            and item_code in
            <foreach collection="erpCodeList" index="index" item="erpCode" open="(" separator=","
                     close=")">
                #{erpCode}
            </foreach>
        </if>
    </select>

    <select id="getActivityForDesignPlanDetail" resultType="java.lang.String">
        select value from (
        select value
        from pam_ctc.material_custom_dict mcd
        where deleted_flag != 1
        and name = '活动事项编码'
        and header_id = (
        select id from pam_ctc.material_custom_dict_header mcdh
        where mcdh.deleted_flag != 1
        and organization_id = #{storageId}
        and coding_class = #{materialClassification}
        and coding_middleclass = #{codingMiddleClass}
        and coding_subclass = #{materielType}
        ) ) result limit 1
    </select>

    <select id="getProjectBudgetTypeForDesignPlanDetail" resultType="java.lang.String">
        select
        type
        from pam_ctc.project_activity
        where code = #{code}
        and unit_id = #{unitId}
    </select>

    <select id="getActivityCodeByOrganizationIdAndItemCode" resultType="java.lang.String">
        select
            mcd.value
        from
            pam_basedata.material m
            inner join pam_ctc.material_custom_dict_header mcdh
            on
            m.material_classification = mcdh.coding_class
            and m.coding_middleclass = mcdh.coding_middleclass
            and m.material_type = mcdh.coding_subclass
            and m.organization_id = mcdh.organization_id
            and mcdh.deleted_flag = 0
            inner join pam_ctc.material_custom_dict mcd
            on
            mcdh.id = mcd.header_id
            and mcd.name = '活动事项编码'
            and mcd.deleted_flag = 0
        where 1 = 1
        <if test="itemCode != null">
            and m.item_code = #{itemCode}
        </if>
        <if test="organizationId != null">
            and m.organization_id = #{organizationId}
        </if>
            and m.delete_flag = 0
    </select>

    <select id="selectByItemCodes" resultType="com.midea.pam.common.basedata.dto.MaterialDto">
        select id,
               item_id as itemId,
               item_code as itemCode,
               item_info as itemInfo,
               organization_id as organizationId,
               unit
        from material
        where item_code in
                <foreach collection="itemCodes" item="itemCode" open="(" separator="," close=")">
                    #{itemCode}
                </foreach>
        and delete_flag = 0
    </select>

    <insert id="batchInsert" parameterType="com.midea.pam.common.basedata.entity.Material">
        insert into material (id, organization_id, item_id,
        item_code, item_info, unit,
        item_type, item_status, delist_flag,
        buyer_number, buyer_id, buyer_round,
        bond_flag, code_name, item_code_old,
        fixed_lot_multiplier, inventory_type, pur_type,
        pam_code, business_categories_id, business_categories,
        material_classification_id, material_classification,
        material_type_id, material_type, coding_middleclass,
        coding_middleclass_id, machining_part_type,
        material, unit_weight, material_processing,
        name, model, brand,
        minimum_order_quantity, receving_subinventory,
        shelves, sourcer, safety_stock_quantity,
        create_by, create_at, update_by,
        update_at, delete_flag, erp_code,
        erp_message, material_category, figure_number,
        chart_version, brand_material_code, or_spare_parts_mask,
        erp_code_create_at, maximum_draw_version_num,
        activity_code, material_attribute, remark)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.organizationId,jdbcType=BIGINT}, #{item.itemId,jdbcType=BIGINT},
            #{item.itemCode,jdbcType=VARCHAR}, #{item.itemInfo,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR},
            #{item.itemType,jdbcType=VARCHAR}, #{item.itemStatus,jdbcType=VARCHAR}, #{item.delistFlag,jdbcType=TINYINT},
            #{item.buyerNumber,jdbcType=VARCHAR}, #{item.buyerId,jdbcType=VARCHAR}, #{item.buyerRound,jdbcType=VARCHAR},
            #{item.bondFlag,jdbcType=VARCHAR}, #{item.codeName,jdbcType=VARCHAR}, #{item.itemCodeOld,jdbcType=VARCHAR},
            #{item.fixedLotMultiplier,jdbcType=VARCHAR}, #{item.inventoryType,jdbcType=VARCHAR}, #{item.purType,jdbcType=VARCHAR},
            #{item.pamCode,jdbcType=VARCHAR}, #{item.businessCategoriesId,jdbcType=BIGINT}, #{item.businessCategories,jdbcType=VARCHAR},
            #{item.materialClassificationId,jdbcType=BIGINT}, #{item.materialClassification,jdbcType=VARCHAR},
            #{item.materialTypeId,jdbcType=BIGINT}, #{item.materialType,jdbcType=VARCHAR}, #{item.codingMiddleclass,jdbcType=VARCHAR},
            #{item.codingMiddleclassId,jdbcType=BIGINT}, #{item.machiningPartType,jdbcType=VARCHAR},
            #{item.material,jdbcType=VARCHAR}, #{item.unitWeight,jdbcType=DECIMAL}, #{item.materialProcessing,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR}, #{item.model,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR},
            #{item.minimumOrderQuantity,jdbcType=BIGINT}, #{item.recevingSubinventory,jdbcType=VARCHAR},
            #{item.shelves,jdbcType=VARCHAR}, #{item.sourcer,jdbcType=VARCHAR}, #{item.safetyStockQuantity,jdbcType=BIGINT},
            #{item.createBy,jdbcType=BIGINT}, #{item.createAt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT},
            #{item.updateAt,jdbcType=TIMESTAMP}, #{item.deleteFlag,jdbcType=TINYINT}, #{item.erpCode,jdbcType=VARCHAR},
            #{item.erpMessage,jdbcType=VARCHAR}, #{item.materialCategory,jdbcType=VARCHAR}, #{item.figureNumber,jdbcType=VARCHAR},
            #{item.chartVersion,jdbcType=VARCHAR}, #{item.brandMaterialCode,jdbcType=VARCHAR}, #{item.orSparePartsMask,jdbcType=VARCHAR},
            #{item.erpCodeCreateAt,jdbcType=TIMESTAMP}, #{item.maximumDrawVersionNum,jdbcType=BIGINT},
            #{item.activityCode,jdbcType=VARCHAR}, #{item.materialAttribute,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="com.midea.pam.common.basedata.entity.Material">
        <foreach collection="list" item="item" separator=";">
            update material
            <set>
                <if test="item.organizationId != null">
                    organization_id = #{item.organizationId,jdbcType=BIGINT},
                </if>
                <if test="item.itemId != null">
                    item_id = #{item.itemId,jdbcType=BIGINT},
                </if>
                <if test="item.itemCode != null">
                    item_code = #{item.itemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.itemInfo != null">
                    item_info = #{item.itemInfo,jdbcType=VARCHAR},
                </if>
                <if test="item.unit != null">
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.itemType != null">
                    item_type = #{item.itemType,jdbcType=VARCHAR},
                </if>
                <if test="item.itemStatus != null">
                    item_status = #{item.itemStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.delistFlag != null">
                    delist_flag = #{item.delistFlag,jdbcType=TINYINT},
                </if>
                <if test="item.buyerNumber != null">
                    buyer_number = #{item.buyerNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.buyerId != null">
                    buyer_id = #{item.buyerId,jdbcType=VARCHAR},
                </if>
                <if test="item.buyerRound != null">
                    buyer_round = #{item.buyerRound,jdbcType=VARCHAR},
                </if>
                <if test="item.bondFlag != null">
                    bond_flag = #{item.bondFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.codeName != null">
                    code_name = #{item.codeName,jdbcType=VARCHAR},
                </if>
                <if test="item.itemCodeOld != null">
                    item_code_old = #{item.itemCodeOld,jdbcType=VARCHAR},
                </if>
                <if test="item.fixedLotMultiplier != null">
                    fixed_lot_multiplier = #{item.fixedLotMultiplier,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryType != null">
                    inventory_type = #{item.inventoryType,jdbcType=VARCHAR},
                </if>
                <if test="item.purType != null">
                    pur_type = #{item.purType,jdbcType=VARCHAR},
                </if>
                <if test="item.pamCode != null">
                    pam_code = #{item.pamCode,jdbcType=VARCHAR},
                </if>
                <if test="item.businessCategoriesId != null">
                    business_categories_id = #{item.businessCategoriesId,jdbcType=BIGINT},
                </if>
                <if test="item.businessCategories != null">
                    business_categories = #{item.businessCategories,jdbcType=VARCHAR},
                </if>
                <if test="item.materialClassificationId != null">
                    material_classification_id = #{item.materialClassificationId,jdbcType=BIGINT},
                </if>
                <if test="item.materialClassification != null">
                    material_classification = #{item.materialClassification,jdbcType=VARCHAR},
                </if>
                <if test="item.materialTypeId != null">
                    material_type_id = #{item.materialTypeId,jdbcType=BIGINT},
                </if>
                <if test="item.materialType != null">
                    material_type = #{item.materialType,jdbcType=VARCHAR},
                </if>
                <if test="item.codingMiddleclass != null">
                    coding_middleclass = #{item.codingMiddleclass,jdbcType=VARCHAR},
                </if>
                <if test="item.codingMiddleclassId != null">
                    coding_middleclass_id = #{item.codingMiddleclassId,jdbcType=BIGINT},
                </if>
                <if test="item.machiningPartType != null">
                    machining_part_type = #{item.machiningPartType,jdbcType=VARCHAR},
                </if>
                <if test="item.material != null">
                    material = #{item.material,jdbcType=VARCHAR},
                </if>
                <if test="item.unitWeight != null">
                    unit_weight = #{item.unitWeight,jdbcType=DECIMAL},
                </if>
                <if test="item.materialProcessing != null">
                    material_processing = #{item.materialProcessing,jdbcType=VARCHAR},
                </if>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.model != null">
                    model = #{item.model,jdbcType=VARCHAR},
                </if>
                <if test="item.brand != null">
                    brand = #{item.brand,jdbcType=VARCHAR},
                </if>
                <if test="item.minimumOrderQuantity != null">
                    minimum_order_quantity = #{item.minimumOrderQuantity,jdbcType=BIGINT},
                </if>
                <if test="item.recevingSubinventory != null">
                    receving_subinventory = #{item.recevingSubinventory,jdbcType=VARCHAR},
                </if>
                <if test="item.shelves != null">
                    shelves = #{item.shelves,jdbcType=VARCHAR},
                </if>
                <if test="item.sourcer != null">
                    sourcer = #{item.sourcer,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockQuantity != null">
                    safety_stock_quantity = #{item.safetyStockQuantity,jdbcType=BIGINT},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.deleteFlag != null">
                    delete_flag = #{item.deleteFlag,jdbcType=TINYINT},
                </if>
                <if test="item.erpCode != null">
                    erp_code = #{item.erpCode,jdbcType=VARCHAR},
                </if>
                <if test="item.erpMessage != null">
                    erp_message = #{item.erpMessage,jdbcType=VARCHAR},
                </if>
                <if test="item.materialCategory != null">
                    material_category = #{item.materialCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.figureNumber != null">
                    figure_number = #{item.figureNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.chartVersion != null">
                    chart_version = #{item.chartVersion,jdbcType=VARCHAR},
                </if>
                <if test="item.brandMaterialCode != null">
                    brand_material_code = #{item.brandMaterialCode,jdbcType=VARCHAR},
                </if>
                <if test="item.orSparePartsMask != null">
                    or_spare_parts_mask = #{item.orSparePartsMask,jdbcType=VARCHAR},
                </if>
                <if test="item.erpCodeCreateAt != null">
                    erp_code_create_at = #{item.erpCodeCreateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.maximumDrawVersionNum != null">
                    maximum_draw_version_num = #{item.maximumDrawVersionNum,jdbcType=BIGINT},
                </if>
                <if test="item.activityCode != null">
                    activity_code = #{item.activityCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialAttribute != null">
                    material_attribute = #{item.materialAttribute,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="batchUpdateStatus">
        update material
        set update_at = now(),
            erp_code = '2',
            erp_message = #{erpMessage}
        where id in
            <foreach collection="materialIds" item="materialId" open="(" separator="," close=")">
                #{materialId}
            </foreach>
          and erp_code != '2'
    </update>

    <select id="getAllUnitByUsingMaterialCodeRule" resultType="com.midea.pam.common.basedata.entity.Unit">
        select
        u.id,
        u.unit_name unitName
        from
        pam_basedata.unit u left join pam_ctc.organization_custom_dict ocd on u.id = ocd.org_id
        where
        u.parent_id is null and ocd.name ='物料编码规则' and ocd.value =#{materialCodeRule}
    </select>

    <select id="getAllPamCodeAndErpCodeAdjustMaterial" resultType="com.midea.pam.common.basedata.dto.MaterialDto">
        select
        h.adjust_code as adjustCode,
        d.pam_code as pamCode,
        d.erp_code as itemCode,
        d.item_des as itemInfo,
        d.unit,
        d.unit_code as unitCode,
        d.material_class_id as materialClassificationId,
        d.material_class as materialClassification,
        d.material_middle_class_id as codingMiddleclassId,
        d.material_middle_class codingMiddleclass,
        d.material_small_class_id as materialTypeId,
        d.material_small_class as materialType,
        d.name,
        d.model,
        d.brand,
        d.machining_part_type as machiningPartType,
        d.material,
        d.unit_weight as unitWeight,
        ifnull(d.surface_handle_new,d.surface_handle) as materialProcessing,
        d.figure_number as figureNumber,
        h.organization_id as organizationId,
        d.chart_version as chartVersion,
        d.inventory_type as inventoryType,
        h.status as materialAdjustHeaderStatus
        from pam_ctc.material_adjust_detail d left join pam_ctc.material_adjust_header h on d.header_id = h.id
        where d.deleted_flag = false and h.deleted_flag = false and h.status != 2 and h.status != 3
        <if test="organizationIds != null and organizationIds.size>0">
            and h.organization_id in
            <foreach collection="organizationIds" item="organizationId" index="index" open="(" separator="," close=")">
                #{organizationId}
            </foreach>
        </if>
        <choose>
            <when test="pamCodeList != null and pamCodeList.size>0">
                and (d.pam_code in
                <foreach collection="pamCodeList" item="pamCode" index="index" open="(" separator="," close=")">
                    #{pamCode}
                </foreach>
                <if test="erpCodeList != null and erpCodeList.size>0">
                    or d.erp_code in
                    <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                        #{erpCode}
                    </foreach>
                </if>
                )
            </when>
            <when test="erpCodeList != null and erpCodeList.size>0">
                and (d.erp_code in
                <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
                <if test="pamCodeList != null and pamCodeList.size>0">
                    or d.pam_code in
                    <foreach collection="pamCodeList" item="pamCode" index="index" open="(" separator="," close=")">
                        #{pamCode}
                    </foreach>
                </if>
                )
            </when>
        </choose>
    </select>

    <select id="getAllPamCodeAndErpCodeMaterial" resultMap="BaseResultDtoMap">
        select
        <include refid="New_Material_Column_List"/>
        from material
        where 1=1
        <if test="organizationIds != null and organizationIds.size>0">
            and organization_id in
            <foreach collection="organizationIds" item="organizationId" index="index" open="(" separator="," close=")">
                #{organizationId}
            </foreach>
        </if>
        <choose>
            <when test="pamCodeList != null and pamCodeList.size>0">
                and (pam_code in
                <foreach collection="pamCodeList" item="pamCode" index="index" open="(" separator="," close=")">
                    #{pamCode}
                </foreach>
                <if test="erpCodeList != null and erpCodeList.size>0">
                    or item_code in
                    <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                        #{erpCode}
                    </foreach>
                </if>
                )
            </when>
            <when test="erpCodeList != null and erpCodeList.size>0">
                and (item_code in
                <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
                <if test="pamCodeList != null and pamCodeList.size>0">
                    or pam_code in
                    <foreach collection="pamCodeList" item="pamCode" index="index" open="(" separator="," close=")">
                        #{pamCode}
                    </foreach>
                </if>
                )
            </when>
        </choose>
    </select>

    <select id="getAllFigureNumberAndBrandAndModelMaterial" resultMap="BaseResultDtoMap">
        select
        <include refid="New_Material_Column_List"/>
        from material
        where 1=1
        <if test="organizationIds != null and organizationIds.size>0">
            and organization_id in
            <foreach collection="organizationIds" item="organizationId" index="index" open="(" separator="," close=")">
                #{organizationId}
            </foreach>
        </if>
        <choose>
            <when test="figureNumberList != null and figureNumberList.size>0">
                and (figure_number in
                <foreach collection="figureNumberList" item="figureNumber" index="index" open="(" separator=","
                         close=")">
                    #{figureNumber}
                </foreach>
                <if test="brandList != null and brandList.size>0">
                    or brand in
                    <foreach collection="brandList" item="brand" index="index" open="(" separator="," close=")">
                        #{brand}
                    </foreach>
                </if>
                <if test="modelList != null and modelList.size>0">
                    or model in
                    <foreach collection="modelList" item="model" index="index" open="(" separator="," close=")">
                        #{model}
                    </foreach>
                </if>
                )
            </when>
            <when test="brandList != null and brandList.size>0">
                and (brand in
                <foreach collection="brandList" item="brand" index="index" open="(" separator="," close=")">
                    #{brand}
                </foreach>
                <if test="figureNumberList != null and figureNumberList.size>0">
                    or figure_number in
                    <foreach collection="figureNumberList" item="figureNumber" index="index" open="(" separator=","
                             close=")">
                        #{figureNumber}
                    </foreach>
                </if>
                <if test="modelList != null and modelList.size>0">
                    or model in
                    <foreach collection="modelList" item="model" index="index" open="(" separator="," close=")">
                        #{model}
                    </foreach>
                </if>
                )
            </when>
            <when test="modelList != null and modelList.size>0">
                and (model in
                <foreach collection="modelList" item="model" index="index" open="(" separator="," close=")">
                    #{model}
                </foreach>
                <if test="figureNumberList != null and figureNumberList.size>0">
                    or figure_number in
                    <foreach collection="figureNumberList" item="figureNumber" index="index" open="(" separator=","
                             close=")">
                        #{figureNumber}
                    </foreach>
                </if>
                <if test="brandList != null and brandList.size>0">
                    or brand in
                    <foreach collection="brandList" item="brand" index="index" open="(" separator="," close=")">
                        #{brand}
                    </foreach>
                </if>
                )
            </when>
        </choose>
    </select>

    <select id="getAllFigureNumberAndBrandAndModelAdjustMaterial"
            resultType="com.midea.pam.common.basedata.dto.MaterialDto">
        select
        h.adjust_code as adjustCode,
        d.pam_code as pamCode,
        d.erp_code as itemCode,
        d.item_des as itemInfo,
        d.unit,
        d.unit_code as unitCode,
        d.material_class_id as materialClassificationId,
        d.material_class as materialClassification,
        d.material_middle_class_id as codingMiddleclassId,
        d.material_middle_class codingMiddleclass,
        d.material_small_class_id as materialTypeId,
        d.material_small_class as materialType,
        d.name,
        d.model,
        d.brand,
        d.machining_part_type as machiningPartType,
        d.material,
        d.unit_weight as unitWeight,
        ifnull(d.surface_handle_new,d.surface_handle) as materialProcessing,
        d.figure_number as figureNumber,
        h.organization_id as organizationId,
        d.chart_version as chartVersion,
        d.material_type as materialCategory,
        d.inventory_type as inventoryType,
        h.status as materialAdjustHeaderStatus
        from pam_ctc.material_adjust_detail d left join pam_ctc.material_adjust_header h on d.header_id = h.id
        where d.deleted_flag = false and h.deleted_flag = false and h.status != 2 and h.status != 3
        <if test="organizationIds != null and organizationIds.size>0">
            and h.organization_id in
            <foreach collection="organizationIds" item="organizationId" index="index" open="(" separator="," close=")">
                #{organizationId}
            </foreach>
        </if>
        <choose>
            <when test="figureNumberList != null and figureNumberList.size>0">
                and (d.figure_number in
                <foreach collection="figureNumberList" item="figureNumber" index="index" open="(" separator=","
                         close=")">
                    #{figureNumber}
                </foreach>
                <if test="brandList != null and brandList.size>0">
                    or d.brand in
                    <foreach collection="brandList" item="brand" index="index" open="(" separator="," close=")">
                        #{brand}
                    </foreach>
                </if>
                <if test="modelList != null and modelList.size>0">
                    or d.model in
                    <foreach collection="modelList" item="model" index="index" open="(" separator="," close=")">
                        #{model}
                    </foreach>
                </if>
                )
            </when>
            <when test="brandList != null and brandList.size>0">
                and (d.brand in
                <foreach collection="brandList" item="brand" index="index" open="(" separator="," close=")">
                    #{brand}
                </foreach>
                <if test="figureNumberList != null and figureNumberList.size>0">
                    or d.figure_number in
                    <foreach collection="figureNumberList" item="figureNumber" index="index" open="(" separator=","
                             close=")">
                        #{figureNumber}
                    </foreach>
                </if>
                <if test="modelList != null and modelList.size>0">
                    or d.model in
                    <foreach collection="modelList" item="model" index="index" open="(" separator="," close=")">
                        #{model}
                    </foreach>
                </if>
                )
            </when>
            <when test="modelList != null and modelList.size>0">
                and (d.model in
                <foreach collection="modelList" item="model" index="index" open="(" separator="," close=")">
                    #{model}
                </foreach>
                <if test="figureNumberList != null and figureNumberList.size>0">
                    or d.figure_number in
                    <foreach collection="figureNumberList" item="figureNumber" index="index" open="(" separator=","
                             close=")">
                        #{figureNumber}
                    </foreach>
                </if>
                <if test="brandList != null and brandList.size>0">
                    or d.brand in
                    <foreach collection="brandList" item="brand" index="index" open="(" separator="," close=")">
                        #{brand}
                    </foreach>
                </if>
                )
            </when>
        </choose>
    </select>

    <select id="getMaterialListByOrgPamCodes" resultMap="BaseResultMap">
        select
            *
        from
            material
        where
            organization_id = #{organizationId}
        <if test="pamCodes!=null and pamCodes.size>0">
            and pam_code in
            <foreach collection = "pamCodes" item = "pamCode" open = "(" separator = "," close = ")">
                #{pamCode}
            </foreach>
        </if>
            and material_category is not null
            and delete_flag = 0
    </select>

    <select id="getMaterialListByOrgErpCodes" resultType="com.midea.pam.common.basedata.entity.Material">
        select id,
               item_id as itemId,
               item_code as itemCode,
               item_info as itemInfo,
               pam_code as pamCode,
               unit
        from material
        where item_code in
                <foreach collection="itemCodes" item="itemCode" open="(" separator="," close=")">
                    #{itemCode}
                </foreach>
        and organization_id = #{organizationId}
        and delete_flag = 0
    </select>

    <select id="listByItemCodesAndOrganizationIds" resultType="com.midea.pam.common.basedata.dto.MaterialDto">
        select
            item_code as itemCode,
            organization_id as organizationId
        from pam_basedata.material
        where delete_flag = 0
        and (item_code, organization_id) in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            (#{item.itemCode}, #{item.organizationId})
        </foreach>
    </select>
</mapper>