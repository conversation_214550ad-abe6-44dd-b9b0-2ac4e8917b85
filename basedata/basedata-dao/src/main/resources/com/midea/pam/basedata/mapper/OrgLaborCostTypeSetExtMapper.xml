<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.basedata.mapper.OrgLaborCostTypeSetExtMapper">
    <resultMap id="DtoResultMap" type="com.midea.pam.common.basedata.dto.OrgLaborCostTypeSetDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="hr_org_name" jdbcType="VARCHAR" property="hrOrgName"/>
        <result column="labor_cost_type_code" jdbcType="VARCHAR" property="laborCostTypeCode"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="unit_id" jdbcType="BIGINT" property="unitId"/>
        <result column="standard_working_hour_rate" jdbcType="DECIMAL" property="standardWorkingHourRate" />
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="labor_cost_type_name" jdbcType="BIGINT" property="laborCostTypeName"/>
        <result column="unit_name" jdbcType="BIGINT" property="unitName"/>
        <result column="labor_cost_id" jdbcType="BIGINT" property="laborCostId"/>
        <result column="labor_cost_name" jdbcType="VARCHAR" property="laborCostName"/>
        <result column="labor_wbs_cost_ids" property="laborWbsCostIds" jdbcType="VARCHAR"/>
        <result column="labor_wbs_cost_names" property="laborWbsCostNames" jdbcType="VARCHAR"/>
        <result column="hard_working" jdbcType="VARCHAR" property="hardWorking"/>
        <result column="role_type" jdbcType="INTEGER" property="roleType" />
    </resultMap>

    <sql id="Base_Column_List">
    olct.id, olct.org_id, olct.org_name, olct.hr_org_name, olct.labor_cost_type_code, olct.company_id, olct.unit_id,olct.standard_working_hour_rate,
    olct.deleted_flag, olct.create_by,olct.labor_wbs_cost_ids,olct.labor_wbs_cost_names,
    olct.create_at, olct.update_by, olct.update_at, olct.labor_cost_id, olct.labor_cost_name, olct.hard_working, olct.role_type
    </sql>

    <select id="list" parameterType="com.midea.pam.common.basedata.dto.OrgLaborCostTypeSetDTO"
            resultMap="DtoResultMap">
        select
        <include refid="Base_Column_List"/>
        , u.unit_name as unit_name
        , d.name as labor_cost_type_name
        from org_labor_cost_type_set olct
        left join unit u on u.id = olct.unit_id
        left join ltc_dict d on d.code = olct.labor_cost_type_code and d.type = 'labor_cost_type_code'
        where olct.deleted_flag = 0

        <if test="companyId != null">
            and olct.company_id = #{companyId}
        </if>

        <if test="hrOrgName != null">
            and olct.hr_org_name like concat('%', #{hrOrgName}, '%')
        </if>

        <if test="laborCostTypeCode != null">
            and olct.labor_cost_type_code = #{laborCostTypeCode}
        </if>

        <if test="roleTypeList != null and roleTypeList.size > 0">
            and olct.role_type in
            <foreach collection="roleTypeList" item="roleType" open="(" separator="," close=")">
                #{roleType}
            </foreach>
        </if>

        <if test="laborWbsCostIdList!=null and laborWbsCostIdList.size>0">
            and (
            <foreach collection="laborWbsCostIdList" item="id" separator=" or">
                olct.labor_wbs_cost_ids like #{id}
            </foreach>
            )
        </if>

        <if test="unitId != null">
            and olct.unit_id = #{unitId}
        </if>

        <if test="unitIdList != null">
            and (
            olct.unit_id in
            <foreach collection="unitIdList" index="index" item="unitId" open="(" separator="," close=")">
                #{unitId}
            </foreach>

            <if test="unitIdIsNull==true">
                or olct.unit_id is null
            </if>
            )
        </if>

        <if test="unitIdList == null and unitIdIsNull==true">
            and olct.unit_id is null
        </if>

        <if test="laborCostTypeCodeList != null">
            and (

            olct.labor_cost_type_code in
            <foreach collection="laborCostTypeCodeList" index="index" item="laborCostTypeCode" open="(" separator=","
                     close=")">
                #{laborCostTypeCode}
            </foreach>

            <if test="laborCostTypeCodeIsNull == true">
                or olct.labor_cost_type_code is null
            </if>
            )
        </if>

        <if test="laborCostTypeCodeList == null and laborCostTypeCodeIsNull == true">
            and olct.labor_cost_type_code is null
        </if>

        <if test="laborCostIdList != null">
            and (

            olct.labor_cost_id in
            <foreach collection="laborCostIdList" index="index" item="laborCostId" open="(" separator=","
                     close=")">
                #{laborCostId}
            </foreach>

            <if test="laborCostIdIsNull == true">
                or olct.labor_cost_id is null
            </if>
            )
        </if>

        <if test="laborCostIdList == null and laborCostIdIsNull == true">
            and olct.labor_cost_id is null
        </if>

        order by olct.labor_cost_type_code asc, olct.hr_org_name asc

    </select>

    <select id="getNeedAdd" resultType="com.midea.pam.common.basedata.entity.OrgLaborCostTypeSet">
      select
        t.id as orgId,
        t.name as orgName,
        substring(t.namePath, 2, length(t.namePath)-1) as hrOrgName
      from (
          select
          lo.id, lo.name, lo.name_path, replace(lo.name_path, SUBSTRING_INDEX(lo.name_path, '/', 2), '') as namePath
          from pam_basedata.ltc_organization lo
          where lo.name_path like concat(#{hrOrgName}, '%') and lo.status = 'Y'
          ) t
      where t.namePath != '' and t.id not in (
        select olcts.org_id
        from pam_basedata.org_labor_cost_type_set olcts
        where olcts.company_id = #{companyId}
        and olcts.deleted_flag = 0
      )
   </select>
    <select id="listDiffCompanyLaborCostSet"
            resultType="com.midea.pam.common.basedata.dto.DiffCompanyLaborCostSetDTO">
        select
            dclcs.id as id,
            dclcs.project_company_id as projectCompanyId,
            u1.unit_name as projectCompanyName,
            u2.unit_name as userCompanyName,
            dclcs.user_company_id as userCompanyId,
            dclcs.ou_id as ouId,
            dclcs.ou_name as ouName,
            dclcs.hard_working as hardWorking,
            dclcs.full_pay_ou_id as fullPayOuId,
            dclcs.full_pay_ou_name as fullPayOuName,
            dclcs.deleted_flag as deletedFlag,
            dclcs.create_by as createBy,
            dclcs.create_at as createAt,
            dclcs.update_by as updateBy,
            dclcs.update_at as updateAt
        from diff_company_labor_cost_set dclcs
        left join unit u1 on u1.id = dclcs.project_company_id
        left join unit u2 on u2.id = dclcs.user_company_id
        where dclcs.deleted_flag = 0
        <if test="projectCompanyId != null">
            and dclcs.project_company_id = #{projectCompanyId}
        </if>
        <if test="userCompanyId != null">
            and dclcs.user_company_id = #{userCompanyId}
        </if>
        <if test="ouId != null">
            and dclcs.ou_id = #{ouId}
        </if>
        <if test="fullPayOuId != null">
            and dclcs.full_pay_ou_id = #{fullPayOuId}
        </if>
        order by dclcs.project_company_id asc, dclcs.user_company_id asc

    </select>

    <update id="syncUpdate">
      update
        (select
            lo.id, lo.name, lo.name_path, replace(lo.name_path, SUBSTRING_INDEX(lo.name_path, '/', 2), '') as namePath
            from pam_basedata.ltc_organization lo
            where lo.name_path like concat(#{hrOrgName}, '%') and lo.status = 'Y'
            ) t,
        pam_basedata.org_labor_cost_type_set olcts
      set
        olcts.hr_org_name = substring(t.namePath, 2, length(t.namePath)-1),
        update_at = now()
      where olcts.org_id = t.id and olcts.company_id = #{companyId}
         and t.namePath != '' and t.namePath != olcts.hr_org_name
  </update>

    <update id="syncRemove">
      update
      ( select
            lo.id
          from pam_basedata.ltc_organization lo
          where lo.status = 'N'
          ) t,
      pam_basedata.org_labor_cost_type_set olcts
      set
        olcts.deleted_flag = 1,
        update_at = now()
      where olcts.org_id = t.id and olcts.company_id = #{companyId}
  </update>

</mapper>