package com.midea.pam.basedata.web;

import com.github.pagehelper.PageInfo;
import com.midea.pam.basedata.service.SpecialCharacterConfigService;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.SpecialCharacterConfigDTO;
import com.midea.pam.common.basedata.entity.SpecialCharacterConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 特殊字符配置控制器
 * 提供特殊字符配置的RESTful API接口
 * 
 * <AUTHOR> Team
 */
@Api(tags = "特殊字符配置管理")
@RestController
@RequestMapping("specialCharacterConfig")
public class SpecialCharacterConfigController {

    private static final Logger logger = LoggerFactory.getLogger(SpecialCharacterConfigController.class);

    @Resource
    private SpecialCharacterConfigService specialCharacterConfigService;

    /**
     * 根据ID查询特殊字符配置
     */
    @ApiOperation(value = "根据ID查询配置")
    @GetMapping("/{id}")
    public Response findById(@ApiParam(value = "配置ID", required = true) @PathVariable Long id) {
        logger.info("[特殊字符配置] 查询配置请求，ID: {}", id);
        
        SpecialCharacterConfig config = specialCharacterConfigService.findById(id);
        if (config == null) {
            return Response.err("配置不存在");
        }
        
        return Response.dataResponse().setData(config);
    }

    /**
     * 新增特殊字符配置
     */
    @ApiOperation(value = "新增配置")
    @PostMapping
    public Response save(@ApiParam(value = "配置信息", required = true) @RequestBody @Valid SpecialCharacterConfig config) {
        logger.info("[特殊字符配置] 新增配置请求，配置名称: {}, 数据库: {}, 表: {}, 字段: {}", 
                   config.getConfigName(), config.getDatabaseName(), 
                   config.getTableName(), config.getColumnName());
        
        SpecialCharacterConfig savedConfig = specialCharacterConfigService.save(config);
        return Response.dataResponse().setData(savedConfig);
    }

    /**
     * 更新特殊字符配置
     */
    @ApiOperation(value = "更新配置")
    @PutMapping
    public Response update(@ApiParam(value = "配置信息", required = true) @RequestBody @Valid SpecialCharacterConfig config) {
        logger.info("[特殊字符配置] 更新配置请求，ID: {}, 配置名称: {}", 
                   config.getId(), config.getConfigName());
        
        SpecialCharacterConfig updatedConfig = specialCharacterConfigService.update(config);
        return Response.dataResponse().setData(updatedConfig);
    }

    /**
     * 根据ID删除特殊字符配置
     */
    @ApiOperation(value = "删除配置")
    @DeleteMapping("/{id}")
    public Response delete(@ApiParam(value = "配置ID", required = true) @PathVariable Long id) {
        logger.info("[特殊字符配置] 删除配置请求，ID: {}", id);
        
        boolean result = specialCharacterConfigService.delete(id);
        if (result) {
            return Response.response();
        } else {
            return Response.err("删除失败");
        }
    }

    /**
     * 分页查询特殊字符配置
     */
    @ApiOperation(value = "分页查询配置")
    @GetMapping("/page")
    public Response page(@ApiParam(value = "查询条件") SpecialCharacterConfigDTO query) {
        logger.info("[特殊字符配置] 分页查询请求，页码: {}, 每页大小: {}", 
                   query.getPageNum(), query.getPageSize());
        
        PageInfo<SpecialCharacterConfigDTO> pageInfo = specialCharacterConfigService.page(query);
        return Response.dataResponse().setData(pageInfo);
    }

    /**
     * 根据条件查询特殊字符配置列表
     */
    @ApiOperation(value = "条件查询配置列表")
    @PostMapping("/list")
    public Response list(@ApiParam(value = "查询条件") @RequestBody SpecialCharacterConfigDTO query) {
        logger.info("[特殊字符配置] 条件查询请求");
        
        List<SpecialCharacterConfig> list = specialCharacterConfigService.findByCondition(query);
        return Response.dataResponse().setData(list);
    }

    /**
     * 根据数据库名称查询启用的配置
     */
    @ApiOperation(value = "查询数据库启用配置")
    @GetMapping("/enabled/database/{databaseName}")
    public Response findEnabledByDatabase(@ApiParam(value = "数据库名称", required = true) @PathVariable String databaseName) {
        logger.info("[特殊字符配置] 查询数据库启用配置请求，数据库: {}", databaseName);
        
        List<SpecialCharacterConfig> list = specialCharacterConfigService.findEnabledByDatabase(databaseName);
        return Response.dataResponse().setData(list);
    }

    /**
     * 根据数据库名称和表名称查询启用的配置
     */
    @ApiOperation(value = "查询表启用配置")
    @GetMapping("/enabled/database/{databaseName}/table/{tableName}")
    public Response findEnabledByDatabaseAndTable(
            @ApiParam(value = "数据库名称", required = true) @PathVariable String databaseName,
            @ApiParam(value = "表名称", required = true) @PathVariable String tableName) {
        logger.info("[特殊字符配置] 查询表启用配置请求，数据库: {}, 表: {}", databaseName, tableName);
        
        List<SpecialCharacterConfig> list = specialCharacterConfigService.findEnabledByDatabaseAndTable(databaseName, tableName);
        return Response.dataResponse().setData(list);
    }

    /**
     * 批量启用/禁用配置
     */
    @ApiOperation(value = "批量更新启用状态")
    @PutMapping("/batch/enabled")
    public Response batchUpdateEnabled(
            @ApiParam(value = "配置ID列表", required = true) @RequestParam List<Long> ids,
            @ApiParam(value = "启用状态", required = true) @RequestParam Boolean enabled) {
        logger.info("[特殊字符配置] 批量更新启用状态请求，ID数量: {}, 启用状态: {}", 
                   ids.size(), enabled);
        
        int updateCount = specialCharacterConfigService.batchUpdateEnabled(ids, enabled);
        return Response.dataResponse().setData(updateCount);
    }

    /**
     * 验证配置的唯一性
     */
    @ApiOperation(value = "验证配置唯一性")
    @PostMapping("/validate/uniqueness")
    public Response validateUniqueness(@ApiParam(value = "配置信息", required = true) @RequestBody SpecialCharacterConfig config) {
        logger.info("[特殊字符配置] 验证唯一性请求，数据库: {}, 表: {}, 字段: {}", 
                   config.getDatabaseName(), config.getTableName(), config.getColumnName());
        
        boolean isUnique = specialCharacterConfigService.validateUniqueness(config);
        return Response.dataResponse().setData(isUnique);
    }
}
