package com.midea.pam.basedata.config;

import com.midea.pam.basedata.mapper.SpecialCharacterConfigMapper;
import com.midea.pam.common.basedata.entity.SpecialCharacterConfig;
import com.midea.pam.common.basedata.entity.SpecialCharacterConfigExample;
import com.midea.pam.support.mybatis.specialchar.loader.SpecialCharacterConfigLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于数据库的特殊字符配置加载器
 * 从special_character_config表中加载配置信息
 *
 * <AUTHOR> Team
 */
public class DatabaseSpecialCharacterConfigLoader implements SpecialCharacterConfigLoader {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseSpecialCharacterConfigLoader.class);

    private final SpecialCharacterConfigMapper specialCharacterConfigMapper;

    /**
     * 构造函数注入依赖
     */
    public DatabaseSpecialCharacterConfigLoader(SpecialCharacterConfigMapper specialCharacterConfigMapper) {
        this.specialCharacterConfigMapper = specialCharacterConfigMapper;
    }

    @Override
    public Map<String, Set<String>> loadTableFieldConfigurations() {
        Map<String, Set<String>> tableFieldMap = new ConcurrentHashMap<>();

        // 获取当前数据库名称（移到方法开始，确保在catch块中也能访问）
        String databaseName = getCurrentDatabaseName();

        try {
            // 构建查询条件
            SpecialCharacterConfigExample example = new SpecialCharacterConfigExample();
            example.createCriteria()
                    .andDatabaseNameEqualTo(databaseName)
                    .andDeletedFlagEqualTo(false)
                    .andEnabledEqualTo(true);

            // 查询数据库配置
            List<SpecialCharacterConfig> configs = specialCharacterConfigMapper.selectByExample(example);

            // 构建配置映射
            for (SpecialCharacterConfig config : configs) {
                String tableName = config.getTableName();
                String fieldName = config.getColumnName();

                if (StringUtils.hasText(tableName)) {
                    if (StringUtils.hasText(fieldName)) {
                        // 具体字段配置或全表配置（使用 * 标识符）
                        tableFieldMap.computeIfAbsent(tableName, k -> ConcurrentHashMap.newKeySet()).add(fieldName);

                        // 记录配置类型
                        if ("*".equals(fieldName)) {
                            logger.debug("加载全表配置 - 表: {}, 数据库: {}", tableName, databaseName);
                        } else {
                            logger.debug("加载字段配置 - 表: {}, 字段: {}, 数据库: {}", tableName, fieldName, databaseName);
                        }
                    } else {
                        // 字段名为空的情况，记录警告但不影响其他配置
                        logger.warn("发现字段名为空的配置 - 表: {}, 配置ID: {}, 数据库: {}",
                                   tableName, config.getId(), databaseName);
                    }
                }
            }

            logger.debug("从数据库加载特殊字符配置完成 - 数据库: {}, 配置数量: {}, 表数量: {}",
                    databaseName, configs.size(), tableFieldMap.size());

        } catch (Exception e) {
            logger.error("从数据库加载特殊字符配置失败 - 数据库: {}, 错误: {}", databaseName, e.getMessage(), e);
        }

        return tableFieldMap;
    }

    @Override
    public boolean isEnabled() {
        try {
            // 获取当前数据库名称
            String currentDatabaseName = getCurrentDatabaseName();

            // 检查当前数据库是否有启用的配置
            SpecialCharacterConfigExample example = new SpecialCharacterConfigExample();
            example.createCriteria()
                    .andDatabaseNameEqualTo(currentDatabaseName)
                    .andDeletedFlagEqualTo(false)
                    .andEnabledEqualTo(true);

            List<SpecialCharacterConfig> configs = specialCharacterConfigMapper.selectByExample(example);
            boolean enabled = !configs.isEmpty();

            logger.debug("数据库配置加载器功能状态检查 - 数据库: {}, 状态: {}, 启用配置数量: {}",
                    currentDatabaseName, enabled ? "启用" : "禁用", configs.size());

            return enabled;

        } catch (Exception e) {
            logger.error("检查特殊字符处理功能状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getLoaderType() {
        return "database";
    }

    @Override
    public int getPriority() {
        return 100; // 中等优先级，高于默认实现
    }

    /**
     * 获取当前数据库名称
     * 可以从环境变量、配置文件或其他方式获取
     */
    private String getCurrentDatabaseName() {
        // 可以从环境变量、配置文件或其他方式获取
        String databaseName = System.getProperty("pam.database.name", "pam_basedata");
        logger.debug("使用数据库名称: {}", databaseName);
        return databaseName;
    }
}
