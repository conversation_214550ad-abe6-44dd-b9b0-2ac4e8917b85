package com.midea.pam.basedata.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.basedata.mapper.SpecialCharacterConfigMapper;
import com.midea.pam.basedata.service.SpecialCharacterConfigService;
import com.midea.pam.common.basedata.dto.SpecialCharacterConfigDTO;
import com.midea.pam.common.basedata.entity.SpecialCharacterConfig;
import com.midea.pam.common.basedata.entity.SpecialCharacterConfigExample;
import com.midea.pam.common.enums.SpecialCharacterConfigEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 特殊字符配置服务实现类
 * 
 * <AUTHOR> Team
 */
public class SpecialCharacterConfigServiceImpl implements SpecialCharacterConfigService {

    private static final Logger logger = LoggerFactory.getLogger(SpecialCharacterConfigServiceImpl.class);

    @Resource
    private SpecialCharacterConfigMapper specialCharacterConfigMapper;

    @Override
    public SpecialCharacterConfig findById(Long id) {
        if (id == null) {
            logger.warn("[特殊字符配置] 查询配置失败，ID为空");
            return null;
        }

        try {
            SpecialCharacterConfig config = specialCharacterConfigMapper.selectByPrimaryKey(id);
            if (config != null && Boolean.TRUE.equals(config.getDeletedFlag())) {
                logger.debug("[特殊字符配置] 配置已被删除，ID: {}", id);
                return null;
            }
            logger.debug("[特殊字符配置] 查询配置成功，ID: {}, 配置名称: {}", id, 
                        config != null ? config.getConfigName() : "null");
            return config;
        } catch (Exception e) {
            logger.error("[特殊字符配置] 查询配置异常，ID: {}, 错误: {}", id, e.getMessage(), e);
            throw new BizException(500, "查询配置失败");
        }
    }

    @Override
    public SpecialCharacterConfig save(SpecialCharacterConfig config) {
        if (config == null) {
            throw new BizException(400, "配置信息不能为空");
        }

        // 参数验证
        validateConfig(config);

        // 唯一性验证
        if (!validateUniqueness(config)) {
            throw new BizException(400, "该数据库表字段的配置已存在");
        }

        try {
            // 设置默认值
            setDefaultValues(config);
            
            // 设置审计信息
            Long userId = SystemContext.getUserId();
            if (userId == null) {
                userId = -1L; // 系统用户
            }
            Date now = new Date();
            config.setCreateBy(userId);
            config.setCreateAt(now);
            config.setUpdateBy(userId);
            config.setUpdateAt(now);
            config.setDeletedFlag(false);

            int result = specialCharacterConfigMapper.insertSelective(config);
            if (result > 0) {
                logger.info("[特殊字符配置] 新增配置成功，配置名称: {}, 数据库: {}, 表: {}, 字段: {}", 
                           config.getConfigName(), config.getDatabaseName(), 
                           config.getTableName(), config.getColumnName());
                return config;
            } else {
                throw new BizException(500, "新增配置失败");
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logger.error("[特殊字符配置] 新增配置异常，配置名称: {}, 错误: {}", 
                        config.getConfigName(), e.getMessage(), e);
            throw new BizException(500, "新增配置失败");
        }
    }

    @Override
    public SpecialCharacterConfig update(SpecialCharacterConfig config) {
        if (config == null || config.getId() == null) {
            throw new BizException(400, "配置信息或ID不能为空");
        }

        // 检查配置是否存在
        SpecialCharacterConfig existingConfig = findById(config.getId());
        if (existingConfig == null) {
            throw new BizException(404, "配置不存在");
        }

        // 参数验证
        validateConfig(config);

        // 唯一性验证（排除当前记录）
        if (!validateUniquenessForUpdate(config)) {
            throw new BizException(400, "该数据库表字段的配置已存在");
        }

        try {
            // 设置审计信息
            Long userId = SystemContext.getUserId();
            if (userId == null) {
                userId = -1L; // 系统用户
            }
            config.setUpdateBy(userId);
            config.setUpdateAt(new Date());

            int result = specialCharacterConfigMapper.updateByPrimaryKeySelective(config);
            if (result > 0) {
                logger.info("[特殊字符配置] 更新配置成功，ID: {}, 配置名称: {}", 
                           config.getId(), config.getConfigName());
                return config;
            } else {
                throw new BizException(500, "更新配置失败");
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logger.error("[特殊字符配置] 更新配置异常，ID: {}, 错误: {}", 
                        config.getId(), e.getMessage(), e);
            throw new BizException(500, "更新配置失败");
        }
    }

    @Override
    public boolean delete(Long id) {
        if (id == null) {
            throw new BizException(400, "配置ID不能为空");
        }

        // 检查配置是否存在
        SpecialCharacterConfig existingConfig = findById(id);
        if (existingConfig == null) {
            throw new BizException(404, "配置不存在");
        }

        try {
            // 逻辑删除
            SpecialCharacterConfig config = new SpecialCharacterConfig();
            config.setId(id);
            config.setDeletedFlag(true);
            
            Long userId = SystemContext.getUserId();
            if (userId == null) {
                userId = -1L; // 系统用户
            }
            config.setUpdateBy(userId);
            config.setUpdateAt(new Date());

            int result = specialCharacterConfigMapper.updateByPrimaryKeySelective(config);
            if (result > 0) {
                logger.info("[特殊字符配置] 删除配置成功，ID: {}, 配置名称: {}", 
                           id, existingConfig.getConfigName());
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            logger.error("[特殊字符配置] 删除配置异常，ID: {}, 错误: {}", id, e.getMessage(), e);
            throw new BizException(500, "删除配置失败");
        }
    }

    /**
     * 参数验证
     */
    private void validateConfig(SpecialCharacterConfig config) {
        if (!StringUtils.hasText(config.getConfigName())) {
            throw new BizException(400, "配置名称不能为空");
        }
        if (!StringUtils.hasText(config.getDatabaseName())) {
            throw new BizException(400, "数据库名称不能为空");
        }
        if (!StringUtils.hasText(config.getTableName())) {
            throw new BizException(400, "表名称不能为空");
        }
        if (!StringUtils.hasText(config.getColumnName())) {
            throw new BizException(400, "字段名称不能为空");
        }
        if (!StringUtils.hasText(config.getProcessingRule())) {
            throw new BizException(400, "处理规则不能为空");
        }

        // 验证处理规则的有效性
        if (!SpecialCharacterConfigEnum.isValidProcessingRule(config.getProcessingRule())) {
            throw new BizException(400, "处理规则无效，支持的规则：REPLACE_WITH_SPACE、REMOVE、CUSTOM");
        }

        // 如果是自定义规则，替换值不能为空
        if (SpecialCharacterConfigEnum.PROCESSING_RULE_CUSTOM.code().equals(config.getProcessingRule())) {
            if (!StringUtils.hasText(config.getReplacementValue())) {
                throw new BizException(400, "自定义处理规则时，替换值不能为空");
            }
        }
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(SpecialCharacterConfig config) {
        if (config.getEnabled() == null) {
            config.setEnabled(true);
        }
        if (config.getPriority() == null) {
            config.setPriority(100);
        }
        if (config.getHandleXmlInvalid() == null) {
            config.setHandleXmlInvalid(true);
        }
        if (config.getHandleUtf84byte() == null) {
            config.setHandleUtf84byte(true);
        }
        if (config.getHandleOtherSpecial() == null) {
            config.setHandleOtherSpecial(true);
        }

        // 设置默认替换值
        if (SpecialCharacterConfigEnum.PROCESSING_RULE_REPLACE_WITH_SPACE.code().equals(config.getProcessingRule())) {
            config.setReplacementValue(" ");
        } else if (SpecialCharacterConfigEnum.PROCESSING_RULE_REMOVE.code().equals(config.getProcessingRule())) {
            config.setReplacementValue("");
        }
    }

    /**
     * 唯一性验证（排除当前记录）
     */
    private boolean validateUniquenessForUpdate(SpecialCharacterConfig config) {
        SpecialCharacterConfigExample example = new SpecialCharacterConfigExample();
        example.createCriteria()
                .andDatabaseNameEqualTo(config.getDatabaseName())
                .andTableNameEqualTo(config.getTableName())
                .andColumnNameEqualTo(config.getColumnName())
                .andDeletedFlagEqualTo(false)
                .andIdNotEqualTo(config.getId()); // 排除当前记录

        List<SpecialCharacterConfig> existingConfigs = specialCharacterConfigMapper.selectByExample(example);
        return CollectionUtils.isEmpty(existingConfigs);
    }

    @Override
    public PageInfo<SpecialCharacterConfigDTO> page(SpecialCharacterConfigDTO query) {
        if (query == null) {
            query = new SpecialCharacterConfigDTO();
        }

        try {
            // 设置分页参数
            int pageNum = query.getPageNum() == null ? 1 : query.getPageNum();
            int pageSize = query.getPageSize() == null ? 20 : query.getPageSize();
            PageHelper.startPage(pageNum, pageSize);

            // 构建查询条件
            SpecialCharacterConfigExample example = buildQueryExample(query);

            // 查询数据
            List<SpecialCharacterConfig> list = specialCharacterConfigMapper.selectByExample(example);

            // 转换为DTO并设置扩展信息
            PageInfo<SpecialCharacterConfig> pageInfo = new PageInfo<>(list);
            PageInfo<SpecialCharacterConfigDTO> result = new PageInfo<>();

            // 复制分页信息
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setTotal(pageInfo.getTotal());
            result.setPages(pageInfo.getPages());
            result.setHasNextPage(pageInfo.isHasNextPage());
            result.setHasPreviousPage(pageInfo.isHasPreviousPage());

            // 转换数据
            List<SpecialCharacterConfigDTO> dtoList = convertToDTO(list);
            result.setList(dtoList);

            logger.debug("[特殊字符配置] 分页查询成功，页码: {}, 每页大小: {}, 总数: {}",
                        pageNum, pageSize, result.getTotal());
            return result;
        } catch (Exception e) {
            logger.error("[特殊字符配置] 分页查询异常，错误: {}", e.getMessage(), e);
            throw new BizException(500, "查询配置失败");
        }
    }

    @Override
    public List<SpecialCharacterConfig> findByCondition(SpecialCharacterConfigDTO query) {
        try {
            SpecialCharacterConfigExample example = buildQueryExample(query);
            List<SpecialCharacterConfig> list = specialCharacterConfigMapper.selectByExample(example);
            logger.debug("[特殊字符配置] 条件查询成功，结果数量: {}", list.size());
            return list;
        } catch (Exception e) {
            logger.error("[特殊字符配置] 条件查询异常，错误: {}", e.getMessage(), e);
            throw new BizException(500, "查询配置失败");
        }
    }

    @Override
    public List<SpecialCharacterConfig> findEnabledByDatabase(String databaseName) {
        if (!StringUtils.hasText(databaseName)) {
            throw new BizException(400, "数据库名称不能为空");
        }

        try {
            SpecialCharacterConfigExample example = new SpecialCharacterConfigExample();
            example.createCriteria()
                    .andDatabaseNameEqualTo(databaseName)
                    .andEnabledEqualTo(true)
                    .andDeletedFlagEqualTo(false);
            example.setOrderByClause("priority ASC, table_name, column_name");

            List<SpecialCharacterConfig> list = specialCharacterConfigMapper.selectByExample(example);
            logger.debug("[特殊字符配置] 查询数据库启用配置成功，数据库: {}, 结果数量: {}",
                        databaseName, list.size());
            return list;
        } catch (Exception e) {
            logger.error("[特殊字符配置] 查询数据库启用配置异常，数据库: {}, 错误: {}",
                        databaseName, e.getMessage(), e);
            throw new BizException(500, "查询配置失败");
        }
    }

    @Override
    public List<SpecialCharacterConfig> findEnabledByDatabaseAndTable(String databaseName, String tableName) {
        if (!StringUtils.hasText(databaseName)) {
            throw new BizException(400, "数据库名称不能为空");
        }
        if (!StringUtils.hasText(tableName)) {
            throw new BizException(400, "表名称不能为空");
        }

        try {
            SpecialCharacterConfigExample example = new SpecialCharacterConfigExample();
            example.createCriteria()
                    .andDatabaseNameEqualTo(databaseName)
                    .andTableNameEqualTo(tableName)
                    .andEnabledEqualTo(true)
                    .andDeletedFlagEqualTo(false);
            example.setOrderByClause("priority ASC, column_name");

            List<SpecialCharacterConfig> list = specialCharacterConfigMapper.selectByExample(example);
            logger.debug("[特殊字符配置] 查询表启用配置成功，数据库: {}, 表: {}, 结果数量: {}",
                        databaseName, tableName, list.size());
            return list;
        } catch (Exception e) {
            logger.error("[特殊字符配置] 查询表启用配置异常，数据库: {}, 表: {}, 错误: {}",
                        databaseName, tableName, e.getMessage(), e);
            throw new BizException(500, "查询配置失败");
        }
    }

    @Override
    public int batchUpdateEnabled(List<Long> ids, Boolean enabled) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BizException(400, "配置ID列表不能为空");
        }
        if (enabled == null) {
            throw new BizException(400, "启用状态不能为空");
        }

        try {
            int updateCount = 0;
            Long userId = SystemContext.getUserId();
            if (userId == null) {
                userId = -1L; // 系统用户
            }
            Date now = new Date();

            for (Long id : ids) {
                SpecialCharacterConfig config = new SpecialCharacterConfig();
                config.setId(id);
                config.setEnabled(enabled);
                config.setUpdateBy(userId);
                config.setUpdateAt(now);

                int result = specialCharacterConfigMapper.updateByPrimaryKeySelective(config);
                if (result > 0) {
                    updateCount++;
                }
            }

            logger.info("[特殊字符配置] 批量更新启用状态成功，更新数量: {}, 启用状态: {}",
                       updateCount, enabled);
            return updateCount;
        } catch (Exception e) {
            logger.error("[特殊字符配置] 批量更新启用状态异常，错误: {}", e.getMessage(), e);
            throw new BizException(500, "批量更新失败");
        }
    }

    @Override
    public boolean validateUniqueness(SpecialCharacterConfig config) {
        if (config == null) {
            return false;
        }

        try {
            SpecialCharacterConfigExample example = new SpecialCharacterConfigExample();
            example.createCriteria()
                    .andDatabaseNameEqualTo(config.getDatabaseName())
                    .andTableNameEqualTo(config.getTableName())
                    .andColumnNameEqualTo(config.getColumnName())
                    .andDeletedFlagEqualTo(false);

            List<SpecialCharacterConfig> existingConfigs = specialCharacterConfigMapper.selectByExample(example);
            return CollectionUtils.isEmpty(existingConfigs);
        } catch (Exception e) {
            logger.error("[特殊字符配置] 唯一性验证异常，错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建查询条件
     */
    private SpecialCharacterConfigExample buildQueryExample(SpecialCharacterConfigDTO query) {
        SpecialCharacterConfigExample example = new SpecialCharacterConfigExample();
        SpecialCharacterConfigExample.Criteria criteria = example.createCriteria();

        // 基本条件：未删除
        criteria.andDeletedFlagEqualTo(false);

        // 动态查询条件
        if (StringUtils.hasText(query.getConfigNameLike())) {
            criteria.andConfigNameLike("%" + query.getConfigNameLike().trim() + "%");
        }
        if (StringUtils.hasText(query.getDatabaseNameQuery())) {
            criteria.andDatabaseNameEqualTo(query.getDatabaseNameQuery().trim());
        }
        if (StringUtils.hasText(query.getTableNameLike())) {
            criteria.andTableNameLike("%" + query.getTableNameLike().trim() + "%");
        }
        if (StringUtils.hasText(query.getColumnNameLike())) {
            criteria.andColumnNameLike("%" + query.getColumnNameLike().trim() + "%");
        }
        if (query.getEnabledQuery() != null) {
            criteria.andEnabledEqualTo(query.getEnabledQuery());
        }
        if (StringUtils.hasText(query.getProcessingRuleQuery())) {
            criteria.andProcessingRuleEqualTo(query.getProcessingRuleQuery().trim());
        }

        // 默认排序：优先级升序，创建时间降序
        example.setOrderByClause("priority ASC, create_at DESC");

        return example;
    }

    /**
     * 转换为DTO列表
     */
    private List<SpecialCharacterConfigDTO> convertToDTO(List<SpecialCharacterConfig> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new java.util.ArrayList<>();
        }

        List<SpecialCharacterConfigDTO> dtoList = new java.util.ArrayList<>();
        for (SpecialCharacterConfig config : list) {
            SpecialCharacterConfigDTO dto = new SpecialCharacterConfigDTO();

            // 复制基本属性
            copyProperties(config, dto);

            // 设置扩展属性
            dto.setProcessingRuleDesc(SpecialCharacterConfigEnum.getMsgByCode(config.getProcessingRule()));

            dtoList.add(dto);
        }

        return dtoList;
    }

    /**
     * 属性复制
     */
    private void copyProperties(SpecialCharacterConfig source, SpecialCharacterConfigDTO target) {
        target.setId(source.getId());
        target.setConfigName(source.getConfigName());
        target.setDatabaseName(source.getDatabaseName());
        target.setTableName(source.getTableName());
        target.setColumnName(source.getColumnName());
        target.setEnabled(source.getEnabled());
        target.setProcessingRule(source.getProcessingRule());
        target.setReplacementValue(source.getReplacementValue());
        target.setHandleXmlInvalid(source.getHandleXmlInvalid());
        target.setHandleUtf84byte(source.getHandleUtf84byte());
        target.setHandleOtherSpecial(source.getHandleOtherSpecial());
        target.setPriority(source.getPriority());
        target.setDescription(source.getDescription());
        target.setDeletedFlag(source.getDeletedFlag());
        target.setCreateBy(source.getCreateBy());
        target.setCreateAt(source.getCreateAt());
        target.setUpdateBy(source.getUpdateBy());
        target.setUpdateAt(source.getUpdateAt());
    }
}
