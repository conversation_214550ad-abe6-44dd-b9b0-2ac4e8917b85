package com.midea.pam.basedata.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.midea.pam.basedata.common.enums.DeletedFlag;
import com.midea.pam.basedata.common.utils.BeanConverter;
import com.midea.pam.basedata.common.utils.DateUtil;
import com.midea.pam.basedata.mapper.LtcTerritoryMapper;
import com.midea.pam.basedata.mapper.VendorMipMapper;
import com.midea.pam.basedata.mapper.VendorSiteBankExMapper;
import com.midea.pam.basedata.mapper.VendorSiteBankMapper;
import com.midea.pam.basedata.service.CtcExtService;
import com.midea.pam.basedata.service.EsbService;
import com.midea.pam.basedata.service.OperatingUnitService;
import com.midea.pam.basedata.service.OrganizationRelService;
import com.midea.pam.basedata.service.SdpCarrierService;
import com.midea.pam.basedata.service.VendorService;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.LtcTerritory;
import com.midea.pam.common.basedata.entity.LtcTerritoryExample;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.VendorMip;
import com.midea.pam.common.basedata.entity.VendorMipExample;
import com.midea.pam.common.basedata.entity.VendorSiteBank;
import com.midea.pam.common.basedata.entity.VendorSiteBankExample;
import com.midea.pam.common.basedata.entity.VendorSiteBankForDisplay;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.excelVo.VendorMipExcelVo;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.system.SystemContext;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: pam
 * @description: VendorServiceImpl
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
public class VendorServiceImpl implements VendorService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    VendorSiteBankMapper vendorSiteBankMapper;
    @Resource
    OperatingUnitService operatingUnitService;
    @Resource
    VendorSiteBankExMapper vendorSiteBankExMapper;
    @Resource
    EsbService esbService;
    @Resource
    OrganizationRelService organizationRelService;
    @Resource
    VendorMipMapper vendorMipMapper;
    @Resource
    LtcTerritoryMapper ltcTerritoryMapper;
    @Resource
    CtcExtService ctcExtService;

    @Resource
    private javax.validation.Validator validator;

    @Resource
    private SdpCarrierService sdpCarrierService;


    @Override
    public VendorSiteBankDto findVendorSiteBankDtoById(Long id) {
        if (id != null && id > 0) {
            VendorSiteBank dbVendorSiteBank = vendorSiteBankMapper.selectByPrimaryKey(id);
            if (dbVendorSiteBank != null) {
                VendorSiteBankDto vendorSiteBankDto = new VendorSiteBankDto();
                BeanUtils.copyProperties(dbVendorSiteBank, vendorSiteBankDto);
                String territoryCode = vendorSiteBankDto.getTerritoryCode();
                if (StringUtils.isNotEmpty(territoryCode)) {
                    LtcTerritoryExample example = new LtcTerritoryExample();
                    LtcTerritoryExample.Criteria criteria = example.createCriteria();
                    criteria.andTerritoryCodeEqualTo(territoryCode).andLanguageEqualTo("ZHS").andDeletedFlagEqualTo(Boolean.FALSE);
                    List<LtcTerritory> ltcTerritories = ltcTerritoryMapper.selectByExample(example);
                    if (ListUtils.isNotEmpty(ltcTerritories)) {
                        vendorSiteBankDto.setTerritoryName(ltcTerritories.get(0).getTerritoryShortName());
                    }
                }
                return vendorSiteBankDto;
            }
        }
        return null;
    }

    @Override
    public VendorSiteBankDto save(VendorSiteBankDto vendorSiteBankDto) {
        VendorSiteBank vendorSiteBank = BeanConverter.copy(vendorSiteBankDto, VendorSiteBank.class);
        vendorSiteBankMapper.insert(vendorSiteBank);
        return vendorSiteBankDto;
    }

    @Override
    public VendorSiteBank getVendorById(Long vendorId) {
        VendorSiteBank vendorSiteBank = null;
        if (null != vendorId) {
            vendorSiteBank = vendorSiteBankMapper.selectByPrimaryKey(vendorId);
        }
        return vendorSiteBank;
    }

    @Override
    public VendorSiteBank update(VendorSiteBankDto vendorSiteBankDto) {
        VendorSiteBank entity = BeanConverter.copy(vendorSiteBankDto, VendorSiteBank.class);
        this.vendorSiteBankMapper.updateByPrimaryKeySelective(entity);
        return entity;
    }

    @Override
    public PageInfo<VendorSiteBankForDisplay> list(VendorSiteBankDto vendorSiteBankDto) {
        //计算数据从第几行开始取值
        Integer offset = vendorSiteBankDto.getPageNum() < 1 ? 0 : (vendorSiteBankDto.getPageNum() - 1) * vendorSiteBankDto.getPageSize();
        vendorSiteBankDto.setOffset(offset);
        List<Long> ouIds = SystemContext.getOus();
        ouIds.add(-1L);// 防止空值
        vendorSiteBankDto.setOperatingUnitIdList(ouIds);
        long count = vendorSiteBankExMapper.countForDisplay(vendorSiteBankDto);
        List<VendorSiteBankForDisplay> list = vendorSiteBankExMapper.selectForDisplay(vendorSiteBankDto);
        PageInfo<VendorSiteBankForDisplay> pageInfo = new PageInfo<>();
        pageInfo.setTotal(count);
        pageInfo.setList(list);
        return pageInfo;
    }

    @Override
    public PageInfo<VendorSiteBankForDisplay> listNew(VendorSiteBankDto vendorSiteBankDto) {
        //计算数据从第几行开始取值
        Integer offset = vendorSiteBankDto.getPageNum() < 1 ? 0 : (vendorSiteBankDto.getPageNum() - 1) * vendorSiteBankDto.getPageSize();
        vendorSiteBankDto.setOffset(offset);
        // 查询当前单位下所有业务实体
        if (SystemContext.getUnitId() != null) {
            List<Long> ouIds = getOuIdList(SystemContext.getUnitId());
            vendorSiteBankDto.setOperatingUnitIdList(ouIds);
        }
        long count = vendorSiteBankExMapper.countForDisplayNew(vendorSiteBankDto);
        List<VendorSiteBankForDisplay> list = vendorSiteBankExMapper.selectForDisplayNew(vendorSiteBankDto);
        PageInfo<VendorSiteBankForDisplay> pageInfo = new PageInfo<>();
        pageInfo.setTotal(count);
        pageInfo.setList(list);
        return pageInfo;
    }

    private List<Long> getOuIdList(Long currentUnitId) {
        // 查询当前单位下所有业务实体
        List<Long> ouIds = new ArrayList<>();
        ouIds.add(-1L);// 防止空值
        List<OperatingUnitDto> operatingUnitDtos = operatingUnitService.queryCurrentUnitOu(SystemContext.getUnitId());
        if (ListUtils.isNotEmpty(operatingUnitDtos)) {
            List<Long> ouIdList = operatingUnitDtos.stream().map(OperatingUnitDto::getOperatingUnitId).collect(Collectors.toList());
            ouIds.addAll(ouIdList);
        }
        return ouIds;
    }

    @Override
    public VendorSiteBank getCurrencyCode(VendorSiteBankDto vendorSiteBankDto) {
        VendorSiteBankExample example = new VendorSiteBankExample();
        VendorSiteBankExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotEmpty(vendorSiteBankDto.getVendorName())) {
            criteria.andVendorNameEqualTo(vendorSiteBankDto.getVendorName());
        }
        if (StringUtils.isNotEmpty(vendorSiteBankDto.getVendorSiteCode())) {
            criteria.andVendorSiteCodeEqualTo(vendorSiteBankDto.getVendorSiteCode());
        }
        if (vendorSiteBankDto.getOuId() != null) {
            criteria.andOperatingUnitIdEqualTo(vendorSiteBankDto.getOuId());
        }
        List<VendorSiteBank> vendorSiteBanks = vendorSiteBankMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(vendorSiteBanks)) {
            return vendorSiteBanks.get(0);
        }
        return new VendorSiteBank();
    }

    @Override
    public List<VendorSiteBankDto> allList(VendorSiteBankDto vendorSiteBankDto) {
        // 查询当前单位下所有业务实体
        if (SystemContext.getUnitId() != null) {
            List<Long> ouIds = getOuIdList(SystemContext.getUnitId());
            vendorSiteBankDto.setOperatingUnitIds(ouIds);
        }
        List<VendorSiteBank> vendorList = vendorSiteBankExMapper.findAllByPaymentMethodCode(vendorSiteBankDto);
        return BeanConverter.copy(vendorList, VendorSiteBankDto.class);
    }

    private VendorSiteBankExample buildExampleNew(VendorSiteBankDto vendorSiteBankDto) {
        VendorSiteBankExample example = new VendorSiteBankExample();
        VendorSiteBankExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(vendorSiteBankDto.getOperatingUnitName())) {
            //业务实体
            //从organization_rel表，根据operating_unit_name字段模糊查询出operation_unit_id
            OrganizationRelQuery orgRelQuery = new OrganizationRelQuery();
            orgRelQuery.setOperatingUnitName(vendorSiteBankDto.getOperatingUnitName());
            List<OrganizationRelDto> listOrgRel = organizationRelService.allList(orgRelQuery);
            //把结果重新封装到list
            List<Long> operationUnitIds = new ArrayList<>();
            listOrgRel.forEach(orgRel -> {
                operationUnitIds.add(orgRel.getOperatingUnitId());
            });

            //组合查询条件
            criteria.andOperatingUnitIdIn(operationUnitIds);
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getVendorCode())) {
            //供应商编码
            criteria.andVendorCodeEqualTo(vendorSiteBankDto.getVendorCode());
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getVendorName())) {
            //供应商名称
            criteria.andVendorNameLike("%" + vendorSiteBankDto.getVendorName() + "%");
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getPaymentMethodCode())) {
            //支付方式编码
            criteria.andPaymentMethodCodeEqualTo(vendorSiteBankDto.getPaymentMethodCode());
        }

        return example;
    }


    private VendorSiteBankExample buildExample(VendorSiteBankDto vendorSiteBankDto) {
        VendorSiteBankExample example = new VendorSiteBankExample();
        VendorSiteBankExample.Criteria criteria = example.createCriteria();

        if (StringUtils.isNotBlank(vendorSiteBankDto.getOperatingUnitName())) {
            //业务实体
            //从organization_rel表，根据operating_unit_name字段模糊查询出operation_unit_id
            OrganizationRelQuery orgRelQuery = new OrganizationRelQuery();
            orgRelQuery.setOperatingUnitName(vendorSiteBankDto.getOperatingUnitName());
            List<OrganizationRelDto> listOrgRel = organizationRelService.allList(orgRelQuery);
            //把结果重新封装到list
            List<Long> operationUnitIds = new ArrayList<>();
            listOrgRel.forEach(orgRel -> {
                operationUnitIds.add(orgRel.getOperatingUnitId());
            });

            //组合查询条件
            criteria.andOperatingUnitIdIn(operationUnitIds);
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getVendorName())) {
            //供应商名称
            criteria.andVendorNameLike("%" + vendorSiteBankDto.getVendorName() + "%");
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getVendorName())) {
            //供应商名称
            criteria.andVendorNameLike("%" + vendorSiteBankDto.getVendorName() + "%");
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getVendorCode())) {
            //供应商编码
            criteria.andVendorCodeLike("%" + vendorSiteBankDto.getVendorCode() + "%");
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getUniteCreditCode())) {
            //统一社会信用代码
            criteria.andUniteCreditCodeLike("%" + vendorSiteBankDto.getUniteCreditCode() + "%");
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getVendorOrgCode())) {
            //组织机构代码
            criteria.andVendorOrgCodeLike("%" + vendorSiteBankDto.getVendorOrgCode() + "%");
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getTaxRegisterCode())) {
            //税务登记号
            criteria.andTaxRegisterCodeLike("%" + vendorSiteBankDto.getTaxRegisterCode() + "%");
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getBankAccount())) {
            //银行账号
            criteria.andBankAccountLike("%" + vendorSiteBankDto.getBankAccount() + "%");
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getPaymentMethodName())) {
            //付款方式
            criteria.andPaymentMethodNameLike("%" + vendorSiteBankDto.getPaymentMethodName() + "%");
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getTermName())) {
            //付款条件
            criteria.andTermNameLike("%" + vendorSiteBankDto.getTermName() + "%");
        }

        if (StringUtils.isNotBlank(vendorSiteBankDto.getPurchasingSiteFlag())) {
            //是否可采购
            criteria.andPurchasingSiteFlagLike("%" + vendorSiteBankDto.getPurchasingSiteFlag() + "%");
        }

        return example;
    }

    /**
     * 唯一条件查重：operatingUnitId+vendorId+erpVendorSiteId+bankAccount
     *
     * @param vendorSiteBankDto
     * @return
     */
    private VendorSiteBankDto findByUniqueKey(VendorSiteBank vendorSiteBankDto) {
        VendorSiteBankExample example = new VendorSiteBankExample();
        VendorSiteBankExample.Criteria criteria = example.createCriteria();
        criteria.andOperatingUnitIdEqualTo(vendorSiteBankDto.getOperatingUnitId()).
                andErpVendorIdEqualTo(vendorSiteBankDto.getErpVendorId()).
                andErpVendorSiteIdEqualTo(vendorSiteBankDto.getErpVendorSiteId());
        if (!ObjectUtils.isEmpty(vendorSiteBankDto.getBankAccount())) {
            criteria.andBankAccountEqualTo(vendorSiteBankDto.getBankAccount());
        }
        if (!ObjectUtils.isEmpty(vendorSiteBankDto.getCombineBankNummber())) {
            criteria.andCombineBankNummberEqualTo(vendorSiteBankDto.getCombineBankNummber());
        }
        if (!ObjectUtils.isEmpty(vendorSiteBankDto.getCurrencyCode())) {
            criteria.andCurrencyCodeEqualTo(vendorSiteBankDto.getCurrencyCode());
        }
        List<VendorSiteBank> entityList = this.vendorSiteBankMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(entityList)) {
            return null;
        }
        VendorSiteBankDto existEntity = BeanConverter.copy(entityList.get(0), VendorSiteBankDto.class);
        return existEntity;
    }

    /**
     * 根据指定时间或前一天0点拉取数据
     *
     * @param lastUpdateDate
     * @param vendorCode
     */
    @Override
    public void getVendorFromErp(String lastUpdateDate, String lastUpdateDateEnd, String vendorCode) {
        Map<String, String> paramMap = new HashMap();
        Date yesterday = DateUtils.subtractDay(new Date(), 1);
        if (ObjectUtils.isEmpty(lastUpdateDate)) {
            lastUpdateDate = DateUtils.format(yesterday, "yyyy-MM-dd 00:00:00");
        }
        //----erp接口取消了更新日期至,注释此参数----
        if (ObjectUtils.isEmpty(lastUpdateDateEnd)) {
            lastUpdateDateEnd = DateUtils.format(new Date(), "yyyy-MM-dd 23:59:59");
        }

        //取有效的库存组织ID作为P01参数
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setPamEnabled(0);
        List<OrganizationRelDto> orgRelList = organizationRelService.list(query).getList();
        Set<Long> ouIdSet = new HashSet<Long>();
        //去重
        for (OrganizationRelDto orgRel : orgRelList) {
            ouIdSet.add(orgRel.getOperatingUnitId());
        }
        for (Long ouId : ouIdSet) {
            paramMap.clear();
//            paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(ouId));
//            paramMap.put(EsbConstant.ERP_IP_P02, lastUpdateDate);
            paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(ouId));
            paramMap.put(EsbConstant.ERP_SDP_P02, lastUpdateDate);
            //----erp接口取消了更新日期至,注释此参数----
            if (!StringUtils.isEmpty(lastUpdateDateEnd)) {
//                paramMap.put(EsbConstant.ERP_IP_P03, lastUpdateDateEnd);
                paramMap.put(EsbConstant.ERP_SDP_P03, lastUpdateDateEnd);
            }
            if (!StringUtils.isEmpty(vendorCode)) {
//                paramMap.put(EsbConstant.ERP_IP_P04, vendorCode);
                paramMap.put(EsbConstant.ERP_SDP_P04, vendorCode);
            }
            this.getVendorFromErp(paramMap);
        }

        // 更新采购合同冗余的供应商信息
        ctcExtService.updatePurchaseContractVendorInfoBatch();
    }

    /**
     * erp供应商信息拉取接口 ERP-PAM-001
     */
    @Override
    public void getVendorFromErp(Map<String, String> paramMap) {
        //调erp接口
//        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_001, paramMap);
        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierService.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_001, paramMap);
        for (SdpTradeResultResponseEleDto item : returnItemList) {
            VendorSiteBank vendor = new VendorSiteBank();
            vendor.setSourceSystemCode(item.getC1());
            if (StringUtils.isEmpty(item.getC2())) {
                continue;
            }
            vendor.setErpVendorId(Long.valueOf(item.getC2()));
            vendor.setVendorCode(item.getC3());
            vendor.setVendorName(item.getC4());
            vendor.setVendorTypeLookupCode(item.getC5());
            vendor.setErpIcpOrgId(item.getC6());
            if (StringUtils.isEmpty(item.getC7())) {
                continue;
            }
            vendor.setErpVendorSiteId(Long.valueOf(item.getC7()));
            vendor.setVendorSiteCode(item.getC8());
            if (StringUtils.isEmpty(item.getC9())) {
                continue;
            }
            vendor.setOperatingUnitId(Long.valueOf(item.getC9()));
            vendor.setUniteCreditCode(item.getC10());
            vendor.setVendorOrgCode(item.getC11());
            vendor.setTaxRegisterCode(item.getC12());
            vendor.setBusinessLicense(item.getC13());
            vendor.setBankAccount(item.getC14());
            vendor.setAccountNum(item.getC15());
            vendor.setAccountName(item.getC16());
            vendor.setCombineBankNummber(item.getC17());
            vendor.setPvsStatus(item.getC18());
            vendor.setPaymentMethodName(item.getC20());
            vendor.setTermName(item.getC21());
            vendor.setPaymentMethodCode(item.getC22());
            vendor.setErpTermId(Long.valueOf(item.getC23()));
            vendor.setPurchasingSiteFlag(item.getC24());
            vendor.setVendorTypeName(item.getC28());
            vendor.setCurrencyCode(item.getC29());
            vendor.setOrderOfPreference(item.getC30());
            vendor.setCountryName(item.getC31());
            vendor.setProvince(item.getC32());
            vendor.setCity(item.getC33());

            vendor.setTerritoryCode(item.getC38()); //供应商地址-国家代码
            vendor.setFullAddress(item.getC39()); //供应商地址-详细地址
            vendor.setSwiftCode(item.getC40()); //银行swiftcode
            try {
                vendor.setAccountInactiveDate(DateUtil.parseDate(item.getC34())); // 银行账号失效标识
                vendor.setBankInactiveDate(DateUtil.parseDate(item.getC35())); // 银行账号失效标识
                vendor.setStartDateActive(DateUtil.parseDate(item.getC25())); // 供应商生效日期
                vendor.setEndDateActive(DateUtil.parseDate(item.getC26())); // 供应商失效日期
                vendor.setPvsInactiveDate(DateUtil.parseDate(item.getC19())); //供应商地点失效日期
                // 根据失效日期更新失效状态
                if (vendor.getPvsInactiveDate() != null && vendor.getPvsInactiveDate().before(new Date())) {
                    // A是有效 I是失效
                    vendor.setPvsStatus("I");
                }
            } catch (Exception e) {
                logger.error("供应商信息拉取接口日期转换出错:{}", JSON.toJSONString(item));
                logger.error("供应商信息拉取接口日期转换出错", e);
                continue;
            }
            // 过滤员工供应商
            if (vendor.getVendorTypeLookupCode().equals("120")) {
                continue;
            }

            // 根据供应商银行唯一性更新：OU、供应商ID、供应商地点ID、联行号、银行账号、币种
            VendorSiteBankDto existEntity = this.findByUniqueKey(vendor);
            if (ObjectUtils.isEmpty(existEntity)) {
                vendorSiteBankMapper.insert(vendor);
            } else {
                vendor.setId(existEntity.getId());
                vendorSiteBankMapper.updateByPrimaryKey(vendor);
            }

            // 根据供应商地点唯一性更新：OU、供应商ID、供应商地点ID
            vendorSiteBankExMapper.updateVendorSiteInfo(vendor);

            // 根据供应商唯一性更新：供应商ID
            vendorSiteBankExMapper.updateVendorInfo(vendor);
        }
    }

    @Override
    public List<String> getVendorTypeNames() {
        return vendorSiteBankExMapper.getVendorTypeNames();
    }

    @Override
    public List<VendorSiteBankDto> findVendorSiteBankDtoList(VendorSiteBankDto vendorSiteBankDto) {
        VendorSiteBankExample vendorSiteBankExample = new VendorSiteBankExample();
        vendorSiteBankExample.createCriteria().andOperatingUnitIdEqualTo(vendorSiteBankDto.getOperatingUnitId())
                .andVendorCodeEqualTo(vendorSiteBankDto.getVendorCode()).andErpIcpOrgIdIsNotNull();
        final List<VendorSiteBank> vendorSiteBankList = vendorSiteBankMapper.selectByExample(vendorSiteBankExample);
        if (ListUtils.isNotEmpty(vendorSiteBankList)) {
            final List<VendorSiteBank> vendorSiteBankList1 = vendorSiteBankList.stream().filter(v -> StringUtils.isNotEmpty(v.getErpIcpOrgId())).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(vendorSiteBankList1)) {
                return BeanConverter.copy(vendorSiteBankList1, VendorSiteBankDto.class);
            }
        }
        return null;
    }

    @Override
    public List<VendorMip> listVendorMip(String vendorCodeStr) {
        VendorMipExample example = new VendorMipExample();
        example.setOrderByClause("create_at desc");
        VendorMipExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(false);
        if (StringUtils.isNotBlank(vendorCodeStr)) {
            List<String> vendorCodeList = Arrays.asList(vendorCodeStr.split(","));
            criteria.andVendorCodeIn(vendorCodeList);
        }
        return vendorMipMapper.selectByExample(example);
    }

    @Override
    public VendorSiteBank findVendorByCodeAndName(String code, String site) {
        VendorSiteBankExample vendorSiteBankExample = new VendorSiteBankExample();
        vendorSiteBankExample.createCriteria().andVendorCodeEqualTo(code).andVendorNameEqualTo(site);
        List<VendorSiteBank> vendorSiteBanks = vendorSiteBankMapper.selectByExample(vendorSiteBankExample);
        return vendorSiteBanks.isEmpty() ? null : vendorSiteBanks.get(0);
    }

    @Override
    public VendorSiteBank getVendorSiteBankInfo(Long ouId, String vendorBankNum, String vendorCode, String accountName) {
        List<VendorSiteBank> vendorSiteBanks = vendorSiteBankExMapper.selectInfoByParam(ouId, vendorBankNum, vendorCode, accountName);
        return vendorSiteBanks.get(0);
    }

    @Override
    public List<VendorSiteBankDto> getVendorSiteBankByCodes(List<String> codes) {
        if (ListUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        return vendorSiteBankExMapper.selectByCodes(codes);
    }

    @Override
    public List<VendorSiteBankDto> getVendorSiteBankByCodes1(List<String> codes) {
        if (ListUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        return vendorSiteBankExMapper.selectByCodes1(codes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<VendorMipExcelVo> importVendorMip(List<VendorMipExcelVo> excelVoList) {
        boolean isPass = validVendorMipExcelVo(excelVoList);
        if (isPass) {
            List<Long> excelVoMipIdList = excelVoList.stream().map(VendorMipExcelVo::getVendorMipId).collect(Collectors.toList());
            VendorMipExample vendorMipExample = new VendorMipExample();
            vendorMipExample.createCriteria().andDeletedFlagEqualTo(false)
                    .andVendorMipIdIn(excelVoMipIdList);
            List<VendorMip> vendorMips = vendorMipMapper.selectByExample(vendorMipExample);
            Map<Long, VendorMip> vendorMipMap = vendorMips.stream().collect(Collectors.toMap(VendorMip::getVendorMipId, v -> v));
            for (VendorMipExcelVo vendorMipExcelVo : excelVoList) {
                VendorMip vendorMip = vendorMipMap.get(vendorMipExcelVo.getVendorMipId());
                if (Objects.isNull(vendorMip)) {
                    vendorMip = BeanConverter.copy(vendorMipExcelVo, VendorMip.class);
                    vendorMip.setDeletedFlag(Boolean.FALSE);
                    vendorMip.setCreateAt(new Date());
                    vendorMip.setCreateBy(SystemContext.getUserId());
                    vendorMip.setUpdateAt(new Date());
                    vendorMip.setUpdateBy(SystemContext.getUserId());
                    vendorMipMapper.insert(vendorMip);
                } else {
                    VendorMip vendorMipNew = BeanConverter.copy(vendorMipExcelVo, VendorMip.class);
                    vendorMipNew.setId(vendorMip.getId());
                    vendorMipNew.setDeletedFlag(Boolean.FALSE);
                    vendorMipNew.setUpdateAt(new Date());
                    vendorMipNew.setUpdateBy(SystemContext.getUserId());
                    vendorMipNew.setCreateAt(vendorMip.getCreateAt());
                    vendorMipNew.setCreateBy(vendorMip.getCreateBy());
                    vendorMipMapper.updateByPrimaryKey(vendorMipNew);
                }
            }
        }
        return excelVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList());
    }

    /**
     * 验证导入供应商MIP参数
     *
     * @param excelVoList
     * @return
     */
    private boolean validVendorMipExcelVo(List<VendorMipExcelVo> excelVoList) {

        excelVoList.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));


        if (excelVoList.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getErrMsg()))) {
            return false;
        }

        VendorMipExample vendorMipExample = new VendorMipExample();
        vendorMipExample.createCriteria().andDeletedFlagEqualTo(false);

        return excelVoList.stream().allMatch(e -> StringUtils.isEmpty(e.getErrMsg()));
    }

    @Override
    public List<VendorSiteBankDto> getVendorSiteBankListByCodeAndOuId(Long ouId) {
        List<OrganizationRel> organizationRels = organizationRelService.queryByOuId(ouId);
        if (ListUtils.isEmpty(organizationRels)) {
            return Collections.emptyList();
        }
        return vendorSiteBankExMapper.selectByCodeAndOuId(Lists.newArrayList("E0"+ organizationRels.get(0).getCompanyCode()), ouId);
    }
}
