package com.midea.pam.basedata.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.basedata.mapper.MaterialPriceConfigDetailExecuteMapper;
import com.midea.pam.basedata.mapper.MaterialPriceConfigHeaderExtMapper;
import com.midea.pam.basedata.mapper.MaterialPriceConfigHeaderMapper;
import com.midea.pam.basedata.mapper.MaterialPriceExtMapper;
import com.midea.pam.basedata.mapper.MaterialPriceHistoryExtMapper;
import com.midea.pam.basedata.mapper.MaterialPriceMapper;
import com.midea.pam.basedata.service.CostCoefficientService;
import com.midea.pam.basedata.service.FormConfigService;
import com.midea.pam.basedata.service.GlDailyRateService;
import com.midea.pam.basedata.service.LtcDictService;
import com.midea.pam.basedata.service.MaterialPriceConfigDetailExecuteService;
import com.midea.pam.basedata.service.MaterialPriceService;
import com.midea.pam.basedata.service.MaterialService;
import com.midea.pam.basedata.service.OrganizationRelService;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.CostCoefficientDto;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.FormConfigDTO;
import com.midea.pam.common.basedata.dto.GlDailyRateDto;
import com.midea.pam.common.basedata.dto.MaterialPriceConfigDetailExecuteDto;
import com.midea.pam.common.basedata.dto.MaterialPriceDetailDto;
import com.midea.pam.common.basedata.dto.MaterialPriceDto;
import com.midea.pam.common.basedata.dto.MaterialPriceHistoryDto;
import com.midea.pam.common.basedata.dto.MaterialPricePlanDto;
import com.midea.pam.common.basedata.entity.FormConfig;
import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.basedata.entity.MaterialPrice;
import com.midea.pam.common.basedata.entity.MaterialPriceConfigDetailExecute;
import com.midea.pam.common.basedata.entity.MaterialPriceConfigHeader;
import com.midea.pam.common.basedata.entity.MaterialPriceConfigHeaderExample;
import com.midea.pam.common.basedata.entity.MaterialPriceExample;
import com.midea.pam.common.basedata.entity.MaterialPriceHistory;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.query.GlDailyRateQuery;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.crm.dto.PlanCurrencyCostDto;
import com.midea.pam.common.crm.dto.PlanProductCostDto;
import com.midea.pam.common.crm.excelVo.PlanImportExcelVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.DeleteFlagEnum;
import com.midea.pam.common.enums.FormConfigEnums;
import com.midea.pam.common.enums.MaterialPriceConfigDetailMetricEnum;
import com.midea.pam.common.enums.MaterialPriceConfigDetailRangeEnum;
import com.midea.pam.common.enums.MaterialPriceConfigExecutionStatusEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MaterialPriceServiceImpl implements MaterialPriceService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());


    //当天时间-仅日期
    private static final Date CURRENT_DATE = Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
    private static final Map<String, Integer> RANGE_DAYS_MAP = new HashMap<>();

    static {
        RANGE_DAYS_MAP.put(MaterialPriceConfigDetailRangeEnum.ONE_MONTH.getName(), 30);
        RANGE_DAYS_MAP.put(MaterialPriceConfigDetailRangeEnum.THREE_MONTH.getName(), 90);
        RANGE_DAYS_MAP.put(MaterialPriceConfigDetailRangeEnum.HALF_A_YEAR.getName(), 180);
        RANGE_DAYS_MAP.put(MaterialPriceConfigDetailRangeEnum.YEAR.getName(), 365);
        RANGE_DAYS_MAP.put(MaterialPriceConfigDetailRangeEnum.THREE_YEAR.getName(), 1095);
    }

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MaterialPriceMapper materialPriceMapper;

    @Resource
    private MaterialPriceExtMapper materialPriceExtMapper;

    @Resource
    private MaterialPriceHistoryExtMapper materialPriceHistoryExtMapper;

    @Resource
    private MaterialPriceConfigHeaderMapper materialPriceConfigHeaderMapper;

    @Resource
    private MaterialPriceConfigHeaderExtMapper materialPriceConfigHeaderExtMapper;

    @Resource
    private MaterialPriceConfigDetailExecuteMapper materialPriceConfigDetailExecuteMapper;

    @Resource
    private MaterialPriceConfigDetailExecuteService materialPriceConfigDetailExecuteService;

    @Resource
    private GlDailyRateService glDailyRateService;

    @Resource
    private FormConfigService formConfigService;
    @Resource
    private OrganizationRelService organizationRelService;

    @Resource
    private LtcDictService ltcDictService;
    @Resource
    private MaterialService materialService;
    @Resource
    private CostCoefficientService costCoefficientService;

    @Override
    public PageInfo<MaterialPriceDto> page(MaterialPriceDto dto, Integer pageSize, Integer pageNum) {
        paramsHandle(dto);
        PageHelper.startPage(pageNum, pageSize);
        List<MaterialPriceDto> list = getList(dto);
        return new PageInfo<>(list);
    }

    @Override
    public List<MaterialPriceDto> exportList(MaterialPriceDto dto) {
        paramsHandle(dto);
        return getList(dto);
    }

    @Override
    public List<MaterialPriceDto> getList(MaterialPriceDto dto) {
        dto.setOuIdList(SystemContext.getOus());
        return materialPriceExtMapper.getList(dto);
    }

    private void paramsHandle(MaterialPriceDto dto) {
        if (Boolean.FALSE.equals(StringUtils.isEmpty(dto.getMaterialStatusStr()))) {
            String[] split = dto.getMaterialStatusStr().split(",");
            List<String> statusList = Stream.of(split).collect(Collectors.toList());
            dto.setMaterialStatusList(statusList);
        }
        if (Boolean.FALSE.equals(StringUtils.isEmpty(dto.getOrganizationIdStr()))) {
            String[] split = dto.getOrganizationIdStr().split(",");
            List<Long> organizationIdList = Stream.of(split).map(Long::parseLong).collect(Collectors.toList());
            dto.setOrganizationIdList(organizationIdList);
        }
    }

    @Override
    public MaterialPriceDetailDto getMaterialPriceDetailDtoById(Long id) {
        MaterialPriceDto dto = new MaterialPriceDto();
        dto.setId(id);
        List<MaterialPriceDto> list = getList(dto);
        if (CollectionUtils.isEmpty(list)) {
            throw new ApplicationBizException("物料价格不存在");
        }
        MaterialPriceDto materialPriceDto = list.get(0);
        MaterialPriceDetailDto detailDto = BeanConverter.copy(materialPriceDto, MaterialPriceDetailDto.class);
        //参数校验
        Assert.notNull(materialPriceDto.getOuId(), "所属业务实体在价格库中不存在");
        Assert.notNull(materialPriceDto.getOrganizationId(), "库存组织在价格库中不存在");
        Assert.notNull(materialPriceDto.getErpCode(), "erp物料编码在价格库中不存在");
        Assert.notNull(materialPriceDto.getConfigHeaderId(), "无法找到对应的价格类型配置");
        List<MaterialPriceHistoryDto> details = materialPriceHistoryExtMapper.getHistory(materialPriceDto);
        detailDto.setDetails(details);
        return detailDto;
    }

    @Override
    public MaterialPriceConfigDetailExecuteDto configDetail(Long executeId) {
        MaterialPriceConfigDetailExecute configDetailExecute = materialPriceConfigDetailExecuteService.selectByPrimaryKey(executeId);
        MaterialPriceConfigHeader header = materialPriceConfigHeaderMapper.selectByPrimaryKey(configDetailExecute.getConfigHeaderId());
        MaterialPriceConfigDetailExecuteDto dto = BeanConverter.copy(configDetailExecute, MaterialPriceConfigDetailExecuteDto.class);
        if (Objects.nonNull(header)) {
            dto.setName(header.getName());
            dto.setDescription(header.getDescription());
        }
        return dto;
    }

    @Override
    public void lock(List<Long> ids) {
        if (ListUtils.isNotEmpty(ids)) {
            List<List<Long>> lists = ListUtils.splistList(ids, 200);
            for (List<Long> list : lists) {
                materialPriceExtMapper.updateBatchLock(list);
            }
        }
    }

    @Override
    public void unLock(List<Long> ids) {
        if (ListUtils.isNotEmpty(ids)) {
            List<List<Long>> lists = ListUtils.splistList(ids, 200);
            for (List<Long> list : lists) {
                materialPriceExtMapper.updateBatchUnLock(list);
            }
        }
    }

    @Override
    public int batchInsert(List<MaterialPrice> list) {
        return materialPriceExtMapper.batchInsert(list);
    }

    @Override
    public void updateExecutionStatus(Integer executionStatus, List<Long> configHeaderIds) {
        materialPriceConfigHeaderExtMapper.updateExecutionStatus(executionStatus, configHeaderIds);
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generateMaterialPrice(List<MaterialPriceConfigDetailExecute> executeList) {
        materialPriceConfigDetailExecuteService.batchInsert(executeList);
        Map<String, List<MaterialPriceConfigDetailExecute>> collect = executeList.stream().collect(Collectors.groupingBy(u -> u.getValueSource()));
        List<MaterialPriceConfigDetailExecute> bpaPriceList = collect.getOrDefault("一揽子协议", new ArrayList<>());
        List<MaterialPriceConfigDetailExecute> orderPriceList = collect.getOrDefault("WBS采购订单行的折后价（不含税）", new ArrayList<>());
        //处理来源为一揽子协议的数据
        if (CollectionUtils.isNotEmpty(bpaPriceList)) {
            bpaPriceExecute(bpaPriceList);
        }
        //处理来源为WBS采购订单行的折后价（不含税）的数据
        if (CollectionUtils.isNotEmpty(orderPriceList)) {
            orderPriceExecute(orderPriceList);
        }
        for (MaterialPriceConfigDetailExecute execute : executeList) {
            //更新material_price_config_header为已完成
            updateExecutionStatus(MaterialPriceConfigExecutionStatusEnum.COMPLETED.getCode(), Arrays.asList(execute.getConfigHeaderId()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void bpaPriceExecute(List<MaterialPriceConfigDetailExecute> executeList) {
        List<Long> ouIdList = executeList.stream().map(MaterialPriceConfigDetailExecute::getOuId).distinct().collect(Collectors.toList());
        //找出所有的一揽子数据并初始化开始与结束时间、汇率转换
        List<MaterialPriceDto> dtos = materialPriceExtMapper.getAllPurchaseBpaPrice(ouIdList);
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        //补充未找到的汇率转换
        Map<String, BigDecimal> exchangeRateMap = new HashMap<>();
        dtos.forEach(e -> {
            if (Objects.isNull(e.getExchangeRate())) {
                e.setExchangeRate(findExchangeRate(e.getSourceCurrency(), e.getCurrency(), exchangeRateMap));
                e.setAmount(e.getExchangeRate().multiply(e.getAmount()));
            }
        });

        //只取固定长度为14且格式为A+数字或者纯数字的数据，然后按照库存组织分类
        Map<String, List<MaterialPriceDto>> ruleMap = new HashMap<>(dtos.stream().filter(e -> Objects.nonNull(e.getOuId())
                && getValidityData(e.getErpCode())).collect(Collectors.groupingBy(item -> item.getOuId() + "_" + item.getOrganizationId())));
        List<MaterialPrice> materialPriceList = new ArrayList<>();
        for (MaterialPriceConfigDetailExecute materialPriceConfigDetailExecute : executeList) {
            long totalStart = System.currentTimeMillis();
            Long configHeaderId = materialPriceConfigDetailExecute.getConfigHeaderId();
            int executeTotal = 0;
            //取对应的库存组织list
            List<MaterialPriceDto> purchaseBpaPrices =
                    ruleMap.get(materialPriceConfigDetailExecute.getOuId() + "_" + materialPriceConfigDetailExecute.getOrganizationId());
            if (CollectionUtils.isNotEmpty(purchaseBpaPrices)) {
                //取编码范围的数据
                List<MaterialPriceDto> materialPriceDtos = new ArrayList<>();
                for (MaterialPriceDto purchaseBpaPrice : purchaseBpaPrices) {
                    if ((Long.parseLong(toNumberPart(purchaseBpaPrice.getErpCode())) - materialPriceConfigDetailExecute.getCodeScopeStart()) >= 0 && (Long.parseLong(toNumberPart(purchaseBpaPrice.getErpCode())) - materialPriceConfigDetailExecute.getCodeScopeEnd()) <= 0) {
                        materialPriceDtos.add(purchaseBpaPrice);
                    }
                }
                //根据ERP编码分组处理
                executeTotal = generateMaterialPriceListByBpaPrice(materialPriceList, materialPriceConfigDetailExecute, configHeaderId,
                        executeTotal, materialPriceDtos);

            }
            logger.info("本次生成的物料行数据大小:{}", executeTotal);
            materialPriceConfigDetailExecute.setExecuteSum(executeTotal);
            long totalEnd = System.currentTimeMillis();
            long totalM = totalEnd - totalStart;
            logger.info("执行总耗时:{} ms", totalM);
            materialPriceConfigDetailExecute.setExecuteResult(0);
            materialPriceConfigDetailExecute.setExecuteTime(totalM);
            materialPriceConfigDetailExecuteMapper.updateByPrimaryKeySelective(materialPriceConfigDetailExecute);
        }
        this.insertOrUpdateMaterialPrice(materialPriceList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void orderPriceExecute(List<MaterialPriceConfigDetailExecute> executeList) {
        List<Long> ouIdList = executeList.stream().map(MaterialPriceConfigDetailExecute::getOuId).distinct().collect(Collectors.toList());
        //找出所有的WBS采购订单行数据并初始化开始与结束时间、汇率转换,增加筛数范围：上一天的数据
        Date yesterday = Date.from(LocalDate.now().minusDays(1).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        List<MaterialPriceDto> dtos = materialPriceExtMapper.getAllPurchaseOrderPrice(ouIdList, yesterday);
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        //补充未找到的汇率转换
        Map<String, BigDecimal> exchangeRateMap = new HashMap<>();
        dtos.forEach(e -> {
            if (Objects.isNull(e.getExchangeRate())) {
                e.setExchangeRate(findExchangeRate(e.getSourceCurrency(), e.getCurrency(), exchangeRateMap));
                e.setAmount(e.getExchangeRate().multiply(e.getAmount()));
            }
        });

        //只取固定长度为14且格式为A+数字或者纯数字的数据，然后按照库存组织分类
        Map<String, List<MaterialPriceDto>> ruleMap = new HashMap<>(dtos.stream().filter(e -> Objects.nonNull(e.getOuId())
                && getValidityData(e.getErpCode())).collect(Collectors.groupingBy(item -> item.getOuId() + "_" + item.getOrganizationId())));

        for (MaterialPriceConfigDetailExecute materialPriceConfigDetailExecute : executeList) {
            long totalStart = System.currentTimeMillis();
            Long configHeaderId = materialPriceConfigDetailExecute.getConfigHeaderId();
            int executeTotal = 0;
            List<List<MaterialPrice>> materialPrices = new ArrayList<>();
            //取对应的库存组织list
            List<MaterialPriceDto> purchaseBpaPrices =
                    ruleMap.get(materialPriceConfigDetailExecute.getOuId() + "_" + materialPriceConfigDetailExecute.getOrganizationId());
            if (CollectionUtils.isNotEmpty(purchaseBpaPrices)) {
                //取编码范围的数据
                List<MaterialPriceDto> materialPriceDtos = new ArrayList<>();
                for (MaterialPriceDto purchaseBpaPrice : purchaseBpaPrices) {
                    if ((Long.parseLong(toNumberPart(purchaseBpaPrice.getErpCode())) - materialPriceConfigDetailExecute.getCodeScopeStart()) >= 0 && (Long.parseLong(toNumberPart(purchaseBpaPrice.getErpCode())) - materialPriceConfigDetailExecute.getCodeScopeEnd()) <= 0) {
                        materialPriceDtos.add(purchaseBpaPrice);
                    }
                }
                //根据ERP编码分组
                executeTotal = generateMaterialPriceListByOrderPrice(materialPrices, materialPriceConfigDetailExecute, configHeaderId, materialPriceDtos);
            }
            logger.info("本次采购订单历史价应更新的物料行数据大小:{}", executeTotal);
            materialPriceConfigDetailExecute.setExecuteSum(executeTotal);
            try {
                for (List<MaterialPrice> materialPriceList : materialPrices) {
                    List<String> erpCodeList = materialPriceList.stream().map(MaterialPrice::getErpCode).collect(Collectors.toList());
                    //根据organizationId + configHeaderId + erpCode 查询现有的值
                    List<MaterialPrice> oldPriceList =
                            materialPriceExtMapper.getListByConfigHeaderIdAndMaterialId(
                                    materialPriceConfigDetailExecute.getOrganizationId(), materialPriceConfigDetailExecute.getConfigHeaderId(), erpCodeList);
                    this.insertOrUpdateMaterialPriceByOrderPrice(materialPriceList, oldPriceList);
                    oldPriceList.clear();
                }
                materialPriceConfigDetailExecute.setExecuteResult(0);
            } catch (Exception e) {
                logger.error("WBS采购订单行的折后价更新失败", e);
                materialPriceConfigDetailExecute.setExecuteResult(1);
            } finally {
                long totalEnd = System.currentTimeMillis();
                long totalM = totalEnd - totalStart;
                logger.info("执行总耗时:{} ms", totalM);
                materialPriceConfigDetailExecute.setExecuteTime(totalM);
                materialPriceConfigDetailExecuteMapper.updateByPrimaryKeySelective(materialPriceConfigDetailExecute);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertOrUpdateMaterialPrice(List<MaterialPrice> materialPriceList) {
        //key：organizationId + configHeaderId
        Map<String, List<MaterialPrice>> materialPriceMap = new HashMap<>(materialPriceList.stream().filter(e ->
                Objects.nonNull(e.getMaterialId())).collect(Collectors.groupingBy(item -> item.getOrganizationId() + "_" + item.getConfigHeaderId())));
        if (materialPriceMap.isEmpty()) {
            throw new ApplicationBizException("没有找到有效的物料价格数据");
        }
        for (Map.Entry<String, List<MaterialPrice>> mapEntry : materialPriceMap.entrySet()) {
            // 插入的List
            List<MaterialPrice> insertList = new ArrayList<>();
            // 更新的List
            List<MaterialPrice> updateList = new ArrayList<>();

            List<MaterialPriceHistory> materialPriceHistoryList = new ArrayList<>();

            String key = mapEntry.getKey();
            String[] arr = key.split("_");
            List<MaterialPrice> materialPrices = mapEntry.getValue();
            //根据organizationId + configHeaderId 查询所有的值
            List<MaterialPrice> priceList =
                    materialPriceExtMapper.getListByConfigHeaderIdAndMaterialId(Long.parseLong(arr[0]), Long.parseLong(arr[1]), new ArrayList<>());
            //锁定的数据
            List<String> lockList =
                    priceList.stream().filter(e -> e.getPriceIsLock() == 1).map(MaterialPrice::getPamCode).collect(Collectors.toList());
            Map<String, BigDecimal> map = priceList.stream().collect(Collectors.toMap(MaterialPrice::getPamCode, MaterialPrice::getAmount));
            logger.info("预处理的数据大小：" + materialPrices.size());
            logger.info("锁定的数据的数据大小：" + lockList.size());
            for (MaterialPrice materialPrice : materialPrices) {
                String pamCode = materialPrice.getPamCode();
                if (Objects.isNull(map.get(pamCode))) {
                    logger.info("新增数据：" + pamCode);
                    insertList.add(materialPrice);
                    MaterialPriceHistory materialPriceHistory = BeanConverter.copy(materialPrice, MaterialPriceHistory.class);
                    materialPriceHistoryList.add(materialPriceHistory);
                } else if (!lockList.contains(pamCode) && materialPrice.getAmount().compareTo(map.getOrDefault(pamCode, BigDecimal.ZERO)) != 0) {
                    logger.info("更新数据：" + pamCode);
                    materialPrice.setUpdateBy(SystemContext.getUserId());
                    materialPrice.setUpdateAt(new Date());
                    materialPrice.setModifiedBy(SystemContext.getUserId());
                    materialPrice.setModifiedTime(new Date());
                    updateList.add(materialPrice);
                    MaterialPriceHistory materialPriceHistory = BeanConverter.copy(materialPrice, MaterialPriceHistory.class);
                    materialPriceHistory.setOriginalPrice(map.get(pamCode));
                    materialPriceHistoryList.add(materialPriceHistory);
                } else {
                    logger.info("不是新增也不是修改的问题数据：" + pamCode);
                }
            }
            /* 历史记录插入 */
            if (CollectionUtils.isEmpty(materialPriceHistoryList)) {
                continue;
            }
            List<List<MaterialPriceHistory>> lists1 = ListUtils.splistList(materialPriceHistoryList, 500);
            for (List<MaterialPriceHistory> list1 : lists1) {
                logger.info("批量插入material_price历史表");
                materialPriceHistoryExtMapper.batchInsert(list1);
            }

            /* 更新 */
            if (CollectionUtils.isNotEmpty(updateList)) {
                List<List<MaterialPrice>> lists2 = ListUtils.splistList(updateList, 500);
                for (List<MaterialPrice> list2 : lists2) {
                    logger.info("批量更新material_price表");
                    materialPriceExtMapper.updateBatch(list2);
                }
            }

            /* 插入 */
            if (CollectionUtils.isNotEmpty(insertList)) {
                List<List<MaterialPrice>> lists3 = ListUtils.splistList(insertList, 500);
                for (List<MaterialPrice> list3 : lists3) {
                    logger.info("批量插入material_price表");
                    batchInsert(list3);
                }
            }
        }
    }

    @Override
    public List<MaterialPricePlanDto> getMaterialPriceByFormConfig(Long unitId, Long ouId, String toCurrency, String pamCode, String erpCode) {
        Assert.notNull(unitId, "使用单位不能为空！");
        Assert.notNull(ouId, "业务实体不能为空！");
        Assert.notNull(toCurrency, "目标币种不能为空！");

        List<OrganizationRel> organizationRels = organizationRelService.queryByOuId(ouId);
        if (CollectionUtils.isEmpty(organizationRels)) {
            throw new BizException(Code.ERROR, String.format("根据业务实体查询库存组织失败，请联系IT人员进行维护！"));
        }
        //获取库存组织id
        Long organizationId = organizationRels.get(0).getOrganizationId();
        String organizationName = organizationRels.get(0).getOrganizationName();
        //获取目标汇率
        String fromCurrency = organizationRels.get(0).getCurrency();
        //获取方案所有配置
        List<FormConfigDTO> formConfigList = formConfigService.listByUnitIdAndCodeName(unitId, FormConfigEnums.PLAN.getCode(), "materialCost");
        Map<String, String> maps = formConfigList.stream().collect(Collectors.toMap(FormConfig::getCode, FormConfig::getConfigValue,
                (key1, key2) -> key2));
        //根据使用单位获取商机成本价格类型配置
        List<JSONObject> priceTypeConfig = getJsonListByJsonString(maps, FormConfigEnums.PRICE_TYPE_CONFIG);
        //查询成本价
        List<MaterialPrice> costPrice = getMaterialPrices(organizationId, organizationName, priceTypeConfig, pamCode, erpCode,
                FormConfigEnums.PRICE_TYPE_CONFIG);
        if (CollectionUtils.isEmpty(costPrice)) {
            if (Boolean.FALSE.equals(StringUtils.isEmpty(pamCode))) {
                throw new BizException(Code.ERROR, String.format("物料：%s没有获取到成本价信息，请联系财务人员进行维护！", pamCode));
            } else if (Boolean.FALSE.equals(StringUtils.isEmpty(erpCode))) {
                throw new BizException(Code.ERROR, String.format("物料：%s没有获取到成本价信息，请联系财务人员进行维护！", erpCode));
            }
//            throw new BizException(Code.ERROR, String.format("库存组织【%s】下未能获取有效的【%s】，请联系IT人员进行维护！", organizationName, FormConfigEnums
//            .PRICE_TYPE_CONFIG.getName()));
        }
        //根据使用单位获取公开价计算方式
        String productIntention = maps.get(FormConfigEnums.PUBLIC_PRICE_CALCULATION.getCode());
        List<MaterialPrice> openPrice = new ArrayList<>();
        Boolean isUnitPrice = false;
        if (Objects.equals(productIntention, "\"2\"")) {
            //根据使用单位获取商机公开价格类型配置
            List<JSONObject> openPriceTypeConfig = getJsonListByJsonString(maps, FormConfigEnums.OPEN_PRICE_TYPE_CONFIG);
            openPrice = getMaterialPrices(organizationId, organizationName, openPriceTypeConfig, pamCode, erpCode,
                    FormConfigEnums.OPEN_PRICE_TYPE_CONFIG);
            isUnitPrice = true;
        }
        //按目标币种转换汇率
        BigDecimal exchangeRate = getExchangeRate(toCurrency, fromCurrency);
        if (CollectionUtils.isNotEmpty(costPrice)) {
            costPrice.forEach(e -> {
                e.setAmount(e.getAmount().multiply(exchangeRate));
            });
        }
        if (CollectionUtils.isNotEmpty(openPrice)) {
            openPrice.forEach(e -> {
                e.setAmount(e.getAmount().multiply(exchangeRate));
            });
        }
        return leftJoinList(costPrice, openPrice, isUnitPrice);
    }

    private List<JSONObject> getJsonListByJsonString(Map<String, String> maps, FormConfigEnums enums) {
        String jsonString = maps.get(enums.getCode());
        ObjectMapper objectMapper = new ObjectMapper();
        List<JSONObject> jsonObjectList = null;
        try {
            jsonObjectList = objectMapper.readValue(jsonString, new TypeReference<List<JSONObject>>() {
            });
        } catch (JsonProcessingException e) {
            throw new BizException(Code.ERROR, String.format("【%s】配置值错误", enums.getName()));
        }
        return jsonObjectList;
    }

    private List<MaterialPricePlanDto> leftJoinList(List<MaterialPrice> costPrice, List<MaterialPrice> openPrice, Boolean isUnitPrice) {
        return new ArrayList<>(Stream.concat(
                        costPrice.stream().map(a -> new MaterialPricePlanDto(a.getPamCode(), a.getId(), a.getAmount(), null, isUnitPrice)),
                        openPrice.stream().map(b -> new MaterialPricePlanDto(b.getPamCode(), null, null, b.getAmount(), isUnitPrice))
                )
                .collect(Collectors.toMap(MaterialPricePlanDto::getPamCode, c -> c, (c1, c2) -> {
                    Long materialPriceId = c1.getMaterialPriceId() != null ? c1.getMaterialPriceId() : c2.getMaterialPriceId();
                    BigDecimal unitCost = c1.getUnitCost() != null ? c1.getUnitCost() : c2.getUnitCost();
                    BigDecimal unitPrice = c1.getUnitPrice() != null ? c1.getUnitPrice() : c2.getUnitPrice();
                    return new MaterialPricePlanDto(c1.getPamCode(), materialPriceId, unitCost, unitPrice, isUnitPrice);
                }))
                .values());
    }

    private List<MaterialPrice> getMaterialPrices(Long organizationId, String organizationName, List<JSONObject> priceTypeConfig, String pamCode,
                                                  String erpCode, FormConfigEnums enums) {
        //根据库存组织从方案配置中获取价格类型配置
        Optional<JSONObject> jsonObjec = priceTypeConfig.stream().filter(e -> Objects.equals(e.getLong("organizationId"), organizationId)).findAny();
        if (!jsonObjec.isPresent()) {
            throw new BizException(Code.ERROR, String.format("库存组织【%s】下未能获取有效的【%s】，请联系IT人员进行维护！", organizationName, enums.getName()));
        }
        Long configHeaderId = jsonObjec.get().getLong("priceType");
        if (Objects.isNull(configHeaderId)) {
            throw new BizException(Code.ERROR, String.format("库存组织【%s】下未能获取有效的【%s】，请联系IT人员进行维护！", organizationName, enums.getName()));
        }
        //查询价格库
        MaterialPriceExample example = new MaterialPriceExample();
        MaterialPriceExample.Criteria criteria = example.createCriteria();
        if (Boolean.FALSE.equals(StringUtils.isEmpty(pamCode))) {
            criteria.andPamCodeEqualTo(pamCode);
        }
        if (Boolean.FALSE.equals(StringUtils.isEmpty(erpCode))) {
            criteria.andErpCodeEqualTo(erpCode);
        }
        criteria.andConfigHeaderIdEqualTo(configHeaderId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<MaterialPrice> materialPrices = materialPriceMapper.selectByExample(example);
        return materialPrices;
    }

    private BigDecimal getExchangeRate(String toCurrency, String fromCurrency) {
        if (Objects.equals(fromCurrency, toCurrency)) {
            return new BigDecimal(1);
        }
        GlDailyRateQuery query = new GlDailyRateQuery();
        query.setFromCurrency(fromCurrency);
        query.setToCurrency(toCurrency);
        query.setExchangeType("Corporate");
        query.setExchangeDate(CURRENT_DATE);
        List<GlDailyRateDto> glDailyRateDtos = glDailyRateService.selectDailyRate(query);
        Optional<GlDailyRateDto> max = glDailyRateDtos.stream().max(Comparator.comparing(GlDailyRateDto::getExchangeDate));
        if (!max.isPresent()) {
            throw new BizException(Code.ERROR, String.format("汇率转换失败,原因：%s转%s不存在", fromCurrency, toCurrency));
        }
        return max.get().getExchangeRate();
    }

    @Override
    public boolean checkPriceIsLock(Long materialId, Long configHeaderId, Long organizationId) {
        return materialPriceExtMapper.checkPriceIsLock(materialId, configHeaderId, organizationId);
    }

    private boolean getValidityData(String materialCode) {
        if (materialCode.matches("^[A\\d]\\d{13}$")) {
            return true;
        }
        return false;
    }

    private String getExchangeRateKey(String fromCurrency, String toCurrency) {
        return String.format("RateKey_%s_%s", fromCurrency, toCurrency);
    }

    /**
     * 获取String字符串中的数字(正整数、正小数、负整数、负小数)
     *
     * @param str
     * @return
     */
    public static String toNumberPart(String str) {
        String content = str.trim();
        Pattern p = Pattern.compile("([-+])?\\d+(\\.\\d+)?");
        Matcher matcher = p.matcher(content);
        return matcher.find() ? matcher.group(0) : "";
    }

    private boolean dateConfigFilter(MaterialPriceDto dto, String valueRange) {
        if (Objects.equals(MaterialPriceConfigDetailRangeEnum.ALL.getName(), valueRange)) {
            return true;
        }
        int days = RANGE_DAYS_MAP.getOrDefault(valueRange, 0);
        boolean onLeft = DateUtils.daysBetween(dto.getEndDate(), CURRENT_DATE) - days > 0;
        boolean onRight = DateUtils.daysBetween(CURRENT_DATE, dto.getStartDate()) > 0;
        return !onLeft && !onRight;
    }

    private BigDecimal findExchangeRate(String fromCurrency, String toCurrency, Map<String, BigDecimal> exchangeRateMap) {
        if (exchangeRateMap.containsKey(getExchangeRateKey(fromCurrency, toCurrency))) {
            return exchangeRateMap.get(getExchangeRateKey(fromCurrency, toCurrency));
        } else {
            GlDailyRateQuery query = new GlDailyRateQuery();
            query.setFromCurrency(fromCurrency);
            query.setToCurrency(toCurrency);
            query.setExchangeType("Corporate");
            query.setExchangeDateEnd(CURRENT_DATE);
            List<GlDailyRateDto> glDailyRateDtos = glDailyRateService.selectDailyRate(query);
            Optional<GlDailyRateDto> max = glDailyRateDtos.stream().max(Comparator.comparing(GlDailyRateDto::getExchangeDate));
            if (!max.isPresent()) {
                throw new BizException(Code.ERROR, String.format("汇率转换失败,原因：%s转%s不存在", fromCurrency, toCurrency));
            }
            BigDecimal bigDecimal = max.get().getExchangeRate();
            exchangeRateMap.put(getExchangeRateKey(fromCurrency, toCurrency), bigDecimal);
            return bigDecimal;
        }
    }

    private int generateMaterialPriceListByBpaPrice(List<MaterialPrice> materialPriceList,
                                                    MaterialPriceConfigDetailExecute materialPriceConfigDetailExecute, Long configHeaderId,
                                                    int executeTotal, List<MaterialPriceDto> materialPriceDtos) {
        if (CollectionUtils.isNotEmpty(materialPriceDtos)) {
            Map<String, List<MaterialPriceDto>> mapByErpCode =
                    materialPriceDtos.stream().collect(Collectors.groupingBy(MaterialPriceDto::getErpCode));
            if (!mapByErpCode.isEmpty()) {
                for (Map.Entry<String, List<MaterialPriceDto>> stringListEntry : mapByErpCode.entrySet()) {
                    List<MaterialPriceDto> priceDtos = stringListEntry.getValue();
                    MaterialPriceDto materialPriceDto = getAmountByConfig(priceDtos, materialPriceConfigDetailExecute);
                    MaterialPrice po = BeanConverter.copy(materialPriceDto, MaterialPrice.class);
                    po.setPriceSource(0);
                    po.setConfigHeaderId(configHeaderId);
                    po.setConfigDetailId(materialPriceConfigDetailExecute.getConfigDetailId());
                    po.setExecuteId(materialPriceConfigDetailExecute.getId());
                    po.setPriceIsLock(0);
                    po.setModifiedBy(SystemContext.getUserId());
                    po.setModifiedTime(new Date());
                    materialPriceList.add(po);
                }
                executeTotal = mapByErpCode.size();
            }
        }
        return executeTotal;
    }

    private int generateMaterialPriceListByOrderPrice(List<List<MaterialPrice>> materialPrices,
                                                      MaterialPriceConfigDetailExecute materialPriceConfigDetailExecute, Long configHeaderId,
                                                      List<MaterialPriceDto> materialPriceDtos) {
        if (CollectionUtils.isEmpty(materialPriceDtos)) {
            return 0;
        }
        Map<String, List<MaterialPriceDto>> mapByErpCode =
                materialPriceDtos.stream().filter(e -> StringUtils.isNotBlank(e.getErpCode())).collect(Collectors.groupingBy(MaterialPriceDto::getErpCode));
        if (!mapByErpCode.isEmpty()) {
            List<MaterialPrice> currentGroup = new ArrayList<>();
            for (Map.Entry<String, List<MaterialPriceDto>> stringListEntry : mapByErpCode.entrySet()) {
                List<MaterialPriceDto> priceDtos = stringListEntry.getValue();
                MaterialPriceDto materialPriceDto = getAmountByConfigForWbs(priceDtos, materialPriceConfigDetailExecute);
                MaterialPrice po = BeanConverter.copy(materialPriceDto, MaterialPrice.class);
                po.setPriceSource(0);
                po.setConfigHeaderId(configHeaderId);
                po.setConfigDetailId(materialPriceConfigDetailExecute.getConfigDetailId());
                po.setExecuteId(materialPriceConfigDetailExecute.getId());
                po.setPriceIsLock(0);
                po.setModifiedBy(SystemContext.getUserId());
                po.setModifiedTime(new Date());
                currentGroup.add(po); // 添加到当前组
                // 每100个为一组，添加到 materialPrices 中
                if (currentGroup.size() == 100) {
                    materialPrices.add(new ArrayList<>(currentGroup)); // 添加当前组的副本
                    currentGroup.clear(); // 清空
                }
            }
            //检查 currentGroup是否还有剩余数据
            if (!currentGroup.isEmpty()) {
                materialPrices.add(new ArrayList<>(currentGroup)); // 添加剩余的组
            }
        }
        return mapByErpCode.size();
    }

    /**
     * 根据配置行查找符合的金额 -- 来源为一揽子场景
     *
     * @param dtos
     * @param configDetail
     * @return
     */
    private MaterialPriceDto getAmountByConfig(List<MaterialPriceDto> dtos, MaterialPriceConfigDetailExecute configDetail) {
        String valueRange = configDetail.getValueRange();
        if (dtos.stream().anyMatch(e -> dateConfigFilter(e, valueRange))) {
            return getMaterialPriceDtoByConfigExistRange(dtos, configDetail);
        } else {
            Optional<MaterialPriceDto> max = dtos.stream().max(Comparator.comparing(MaterialPriceDto::getEndDate));
            if (max.isPresent()) {
                return max.get();
            }
        }
        return new MaterialPriceDto();
    }

    /**
     * 根据配置行查找符合的金额 - 来源为wbs场景
     *
     * @param dtos
     * @param configDetail
     * @return
     */
    private MaterialPriceDto getAmountByConfigForWbs(List<MaterialPriceDto> dtos, MaterialPriceConfigDetailExecute configDetail) {
        String valueRange = configDetail.getValueRange();
        if (dtos.stream().anyMatch(e -> dateConfigFilter(e, valueRange))) {
            return getMaterialPriceDtoByConfigExistRange(dtos, configDetail);
        }
        return new MaterialPriceDto();
    }

    /**
     * 能找到配置行对应范围区间的处理逻辑-据时间范围筛选合适的数据，根据配置取最大最小等
     *
     * @param dtos
     * @param configDetail
     * @return
     */
    private MaterialPriceDto getMaterialPriceDtoByConfigExistRange(List<MaterialPriceDto> dtos, MaterialPriceConfigDetailExecute configDetail) {
        MaterialPriceDto materialPriceDto = new MaterialPriceDto();
        if (dtos == null || dtos.isEmpty()) return materialPriceDto;
        String valueRange = configDetail.getValueRange();
        List<MaterialPriceDto> filteredList = dtos.stream().filter(e -> dateConfigFilter(e, valueRange)).collect(Collectors.toList());
        MaterialPriceConfigDetailMetricEnum configDetailMetricEnum = MaterialPriceConfigDetailMetricEnum.getEnumByCode(configDetail.getValueMetric());
        if (configDetailMetricEnum == null) {
            return materialPriceDto;
        } else {
            switch (configDetailMetricEnum) {
                case MAX:
                    return filteredList.stream().max(Comparator.comparing(MaterialPriceDto::getAmount)).orElse(materialPriceDto);
                case MIN:
                    return filteredList.stream().min(Comparator.comparing(MaterialPriceDto::getAmount)).orElse(materialPriceDto);
                case AVG:
                    materialPriceDto = filteredList.get(0);
                    BigDecimal amount =
                            filteredList.stream().map(MaterialPriceDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(filteredList.size()), 2, RoundingMode.HALF_UP);
                    materialPriceDto.setAmount(amount);
                    materialPriceDto.setPurchaseBpaPriceId(null);
                    return materialPriceDto;
                case LAST:
                    return filteredList.stream().max(Comparator.comparing(MaterialPriceDto::getEndDate)).orElse(materialPriceDto);
                default:
                    return materialPriceDto;
            }
        }
    }

    @Override
    public PlanCurrencyCostDto importBatchFromExcel(PlanCurrencyCostDto dto) {
        String currencyCode = dto.getCurrencyCode();
        Assert.notNull(currencyCode, "币种不能为空");
        int offsetLine = dto.getOffsetLine();
        dto.setErrorMsgList(new ArrayList<>());
        //先认证导入的文档结构是否正确。再判断导入的文件是纯手工还是取价格库
        //1、检查序号是否规范 2、检查序号是否重复 3、检查是否缺少上层数据
        List<String> errorMsgList = dto.getErrorMsgList();
        checkData(dto, offsetLine);
        if (ListUtils.isNotEmpty(errorMsgList)) {
            return dto;
        }
        //2、检查其他字段必填和合法性校验(带产品意向验证,仅根据产品意向类型判断导入的数据是否必填)
        //获取公开价计算方式
        String publicPriceCalculation = getPublicPriceCalculationByFormConfig();
        List<DictDto> unitList = ltcDictService.listByType("measurement_unit");
        checkOtherData(dto, publicPriceCalculation, unitList, offsetLine);
        if (ListUtils.isNotEmpty(errorMsgList)) {
            return dto;
        }
        //3、根据导入的数据进行配置项是否符合
        //获取方案所有配置
        List<PlanImportExcelVO> importExcelVos = dto.getImportExcelVos();
        boolean allNamesAreManual =
                importExcelVos.stream().filter(PlanImportExcelVO::getIsLowestLevel).allMatch(item -> item.getPriceValue().equals("手工"));
        //存在价格来源为价格库的数据行
        if (!allNamesAreManual) {
            addPriceLibrary(dto, publicPriceCalculation, unitList, offsetLine);
        }
        if (ListUtils.isNotEmpty(errorMsgList)) {
            return dto;
        }
        //4、复制数据至List<PlanProductCostDto>
        if (ListUtils.isEmpty(errorMsgList)) {
            List<PlanProductCostDto> planProductCostDtos = generatePlanProductCostDto(importExcelVos);
            dto.setList(streamToTreeByOrderNo(planProductCostDtos));
        }
        //5、返回前端数据前做产品意向的拦截
        if (Objects.equals(publicPriceCalculation, "\"1\"")) {
            try {
                checkPublicPriceCalculation(dto);
            } catch (ParseException e) {
                throw new BizException(Code.ERROR, "成本系数日期转换失败");
            }
        }
        dto.setPriceUpdateAt(new Date());
        return dto;
    }

    @Override
    public Map<String, BigDecimal> getMaterialPriceByConfigHeader(MaterialPriceDto dto) {
        Map<String, BigDecimal> historyPriceMap = new HashMap<>();
        List<Long> organizationIdList = dto.getOrganizationIdList();
        List<String> erpCodeList = dto.getErpCodeList();
        List<String> materialPriceGroupKeyList = dto.getMaterialPriceGroupKeyList();
        if (CollectionUtils.isEmpty(organizationIdList) || CollectionUtils.isEmpty(erpCodeList) || CollectionUtils.isEmpty(materialPriceGroupKeyList)) {
            return historyPriceMap;
        }
        // 查询配置的组织参数：WBS采购订单历史价价格类型 是否已维护在 物料价格类型配置表 上
        Map<Long, String> configHeaderNameMap = dto.getConfigHeaderNameMap();
        List<MaterialPriceConfigHeader> materialPriceConfigHeaderList = new ArrayList<>();
        for (Long organizationId : organizationIdList) {
            String configHeaderName = configHeaderNameMap.get(organizationId);
            if (StringUtils.isBlank(configHeaderName)) {
                continue;
            }
            MaterialPriceConfigHeaderExample configHeaderExample = new MaterialPriceConfigHeaderExample();
            configHeaderExample.createCriteria().andNameEqualTo(configHeaderName).andOrganizationIdEqualTo(organizationId).andDeletedFlagEqualTo(DeleteFlagEnum.NOT_DELETED.getBolValue());
            materialPriceConfigHeaderList.addAll(materialPriceConfigHeaderMapper.selectByExample(configHeaderExample));
        }
        if (CollectionUtils.isEmpty(materialPriceConfigHeaderList)) {
            return historyPriceMap;
        }
        // 批量查询物料价格
        List<Long> configHeaderIdList = materialPriceConfigHeaderList.stream().map(MaterialPriceConfigHeader::getId).collect(Collectors.toList());
        MaterialPriceExample example = new MaterialPriceExample();
        example.createCriteria().andConfigHeaderIdIn(configHeaderIdList).andErpCodeIn(erpCodeList).andDeletedFlagEqualTo(DeleteFlagEnum.NOT_DELETED.getBolValue());
        List<MaterialPrice> materialPriceList = materialPriceMapper.selectByExample(example);

        for (MaterialPrice materialPrice : materialPriceList) {
            String key = buildMaterialPriceGroupKey(materialPrice.getErpCode(), materialPrice.getOrganizationId());
            if (materialPriceGroupKeyList != null && materialPriceGroupKeyList.contains(key)) {
                historyPriceMap.put(key, materialPrice.getAmount());
            }
        }
        return historyPriceMap;
    }

    private String buildMaterialPriceGroupKey(String erpCode, Long organizationId) {
        return String.format("buildMaterialPriceGroupKey_%s_%s", erpCode, organizationId);
    }

    private void addPriceLibrary(PlanCurrencyCostDto dto, String publicPriceCalculation, List<DictDto> unitList, int offsetLine) {
        Long ouId = dto.getOuId();
        Long businessId = dto.getBusinessId();
        if (Objects.isNull(businessId) && Objects.isNull(ouId)) {
            throw new BizException(Code.ERROR, "入参商机ID和业务实体ID不能同时为空");
        }
        if (Objects.isNull(ouId)) {
            BusinessDto business = getBusinessById(businessId);
            ouId = business.getOperatingUnitId();
            //业务实体不存在
            if (Objects.isNull(ouId)) {
                throw new BizException(Code.ERROR, "商机未关联业务实体");
            }
            dto.setOuId(ouId);
        }
        //获取库存组织名称
        List<OrganizationRel> operatingUnitDtoList = organizationRelService.queryByOuId(ouId);
        if (org.springframework.util.CollectionUtils.isEmpty(operatingUnitDtoList)) {
            throw new BizException(Code.ERROR, "根据ouId查询库存组织失败");
        }
        String organizationName = operatingUnitDtoList.get(0).getOrganizationName();
        Long organizationId = operatingUnitDtoList.get(0).getOrganizationId();

        //获取物料信息
        List<Material> materialList = materialService.getMaterialListByOrgPamCodes(organizationId, null);
        Map<String, Material> materialMap =
                materialList.stream().filter(e -> StringUtils.isNotEmpty(e.getPamCode())).collect(Collectors.toMap(Material::getPamCode, e -> e,
                        (existing, duplicate) -> existing));

        //根据使用单位的商机方案配置获取相应全量价格库的数据
        List<MaterialPricePlanDto> materialPrices = getMaterialPriceByFormConfig(SystemContext.getUnitId(), ouId, dto.getCurrencyCode(), null, null);
//        if (CollectionUtils.isEmpty(materialPrices)) {
//            throw new BizException(Code.ERROR, String.format("未能获取有效的【%s】，请联系IT人员进行维护！", FormConfigEnums.PRICE_TYPE_CONFIG.getName()));
//        }
        Map<String, MaterialPricePlanDto> collect = materialPrices.stream().collect(Collectors.toMap(MaterialPricePlanDto::getPamCode, e -> e, (e1,
                                                                                                                                                e2) -> e1));
        List<PlanImportExcelVO> excelVos = dto.getImportExcelVos();
        List<String> errorMsgList = dto.getErrorMsgList();
        for (int i = 0; i < excelVos.size(); i++) {
            PlanImportExcelVO excelVO = excelVos.get(i);
            if (!Objects.equals(excelVO.getPriceSource(), 0)) {
                continue;
            }
            MaterialPricePlanDto materialPricePlanDto = collect.get(excelVO.getPamCode());
            //价格库信息补充
            if (Objects.isNull(materialPricePlanDto)) {
                errorMsgList.add(String.format("行：%s物料%s没有获取到成本价信息，请联系财务人员进行维护！", offsetLine + 2 + i, excelVO.getPamCode()));
                addErrorMsg(excelVO, String.format("物料%s没有获取到成本价信息，请联系财务人员进行维护！", excelVO.getPamCode()));
                excelVO.setUnitCost(null);
                if (Objects.equals(publicPriceCalculation, "\"2\"")) {
                    errorMsgList.add(String.format("行：%s物料%s没有获取到公开价信息，请联系财务人员进行维护！", offsetLine + 2 + i, excelVO.getPamCode()));
                    addErrorMsg(excelVO, String.format("物料%s没有获取到公开价信息，请联系财务人员进行维护！", excelVO.getPamCode()));
                    excelVO.setUnitPrice(null);
                }
                continue;
            }
            //获取成本价 公开价有值
            if (Objects.isNull(materialPricePlanDto.getUnitCost())) {
                errorMsgList.add(String.format("行：%s物料%s没有获取到成本价信息，请联系财务人员进行维护！", offsetLine + 2 + i, excelVO.getPamCode()));
                addErrorMsg(excelVO, String.format("物料%s没有获取到成本价信息，请联系财务人员进行维护！", excelVO.getPamCode()));
                excelVO.setUnitCost(null);
                continue;
            }
            //获取公开价,成本价有值
            if (Objects.equals(publicPriceCalculation, "\"2\"") && Objects.isNull(materialPricePlanDto.getUnitPrice())) {
                errorMsgList.add(String.format("行：%s物料%s没有获取到公开价信息，请联系财务人员进行维护！", offsetLine + 2 + i, excelVO.getPamCode()));
                addErrorMsg(excelVO, String.format("物料%s没有获取到公开价信息，请联系财务人员进行维护！", excelVO.getPamCode()));
                excelVO.setUnitPrice(null);
                continue;
            }
            excelVO.setMaterialPriceId(materialPricePlanDto.getMaterialPriceId());
            excelVO.setUnitCost(materialPricePlanDto.getUnitCost().stripTrailingZeros().toPlainString());
            if (Objects.equals(publicPriceCalculation, "\"2\"")) {
                excelVO.setUnitPrice(materialPricePlanDto.getUnitPrice().stripTrailingZeros().toPlainString());
            } else {
                excelVO.setUnitPrice(null);
            }

            //物料基本信息补充
            if (!materialMap.containsKey(excelVO.getPamCode())) {
                errorMsgList.add(String.format("行：%sPAM编码：%s不存在库存组织%s内，无法获取物料信息，请检查物料编码信息是否错误！", offsetLine + 2 + i, excelVO.getPamCode()
                        , organizationName));
                addErrorMsg(excelVO, String.format("PAM编码：%s不存在库存组织%s内，无法获取物料信息，请检查物料编码信息是否错误！", excelVO.getPamCode()
                        , organizationName));
            } else {
                //补充物料信息
                Material material = materialMap.get(excelVO.getPamCode());
                excelVO.setName(material.getName());//名称
                if (material.getMaterialAttribute().startsWith("外购") || material.getMaterialAttribute().startsWith("自制")) {
                    excelVO.setMaterialAttribute(material.getMaterialAttribute().startsWith("外购") ? "外购" : "自制");//属性
                } else {
                    errorMsgList.add(String.format("行：%s物料%s对应的属性字段数据不为自制或外购,请联系管理员！", offsetLine + 2 + i, excelVO.getPamCode()));
                    addErrorMsg(excelVO, String.format("物料%s对应的属性字段数据不为自制或外购,请联系管理员！", excelVO.getPamCode()));
                }
                excelVO.setBrand(material.getBrand());//品牌
                excelVO.setDescr(material.getItemInfo());//描述
                excelVO.setUnitCode(material.getUnit());//单位
                for (DictDto dictDto : unitList) {
                    if (dictDto.getCode().equals(excelVO.getUnitCode())) {
                        excelVO.setUnit(dictDto.getName());
                        break;
                    }
                }
                excelVO.setPamCode(material.getPamCode());
                excelVO.setErpCode(material.getItemCode());
            }
        }
    }

    /**
     * 序号检查
     *
     * @param dto
     * @param offsetLine
     */
    private void checkData(PlanCurrencyCostDto dto, int offsetLine) {
        List<PlanImportExcelVO> excelVos = dto.getImportExcelVos();
        List<String> errorMsgList = dto.getErrorMsgList();
        for (int i = 0; i < excelVos.size(); i++) {
            PlanImportExcelVO excelVO = excelVos.get(i);
            if (StringUtils.isBlank(excelVO.getSerialNumber())) {
                errorMsgList.add(String.format("行：%s，序号字段数据不能为空", offsetLine + 2 + i));
                addErrorMsg(excelVO, "序号字段数据不能为空");
                continue;
            }
            //检查序号是否规范
            String[] numArr = excelVO.getSerialNumber().split("\\.");
            for (String num : numArr) {
                if (Integer.parseInt(num.trim()) < 0) {
                    errorMsgList.add(String.format("行：%s，序号字段数据只能为大于等于0的数字", offsetLine + 2 + i));
                    addErrorMsg(excelVO, "序号字段数据只能为大于等于0的数字");
                    continue;
                }
                if (num.length() > 5) {
                    errorMsgList.add(String.format("行：%s，序号字段长度异常", offsetLine + 2 + i));
                    addErrorMsg(excelVO, "序号字段长度异常");
                }
            }
            //检查是否缺少上层数据
            if (excelVO.getSerialNumber().contains(".")) {
                String fatherNode = StringUtils.substringBeforeLast(excelVO.getSerialNumber(), ".");//获取父节点
                long nodeCount = excelVos.stream().filter(vo -> Objects.equals(vo.getSerialNumber(), fatherNode)).count();
                if (nodeCount == 0) {
                    errorMsgList.add(String.format("行：%s，该层级数据缺少上层数据", offsetLine + 2 + i));
                    addErrorMsg(excelVO, "该层级数据缺少上层数据");
                    continue;
                }
            }
            //检查序号是否重复
            long count = excelVos.stream().filter(vo -> Objects.equals(vo.getSerialNumber(), excelVO.getSerialNumber())).count();
            if (count > 1) {
                errorMsgList.add(String.format("行：%s，序号重复", offsetLine + 2 + i));
                addErrorMsg(excelVO, "序号重复");
            }
        }
    }

    /**
     * 其他数据检查
     *
     * @param dto
     * @param publicPriceCalculation
     * @param unitList
     * @param offsetLine
     */
    private void checkOtherData(PlanCurrencyCostDto dto, String publicPriceCalculation, List<DictDto> unitList, int offsetLine) {
        List<PlanImportExcelVO> excelVos = dto.getImportExcelVos();
        List<String> errorMsgList = dto.getErrorMsgList();
        for (int i = 0; i < excelVos.size(); i++) {
            PlanImportExcelVO excelVO = excelVos.get(i);
            //公共非空项 数量
            if (StringUtils.isBlank(excelVO.getAmount())) {
                errorMsgList.add(String.format("行：%s，数量字段数据不能为空", offsetLine + 2 + i));
                addErrorMsg(excelVO, "数量字段数据不能为空");
            } else {
                if (NumberUtils.isNumber(excelVO.getAmount()) && new BigDecimal(excelVO.getAmount()).compareTo(BigDecimal.ZERO) > 0) {
                    if (BigDecimalUtils.numberOfDecimalPlaces(excelVO.getAmount()) > 5) {
                        errorMsgList.add(String.format("行：%s，数量字段不能超过5位小数", offsetLine + 2 + i));
                        addErrorMsg(excelVO, "数量字段不能超过5位小数");
                    }
                } else {
                    errorMsgList.add(String.format("行：%s，数量字段数据必须为大于0的数字", offsetLine + 2 + i));
                    addErrorMsg(excelVO, "数量字段数据必须为大于0的数字");
                }
            }
            if (findLowestLevels(excelVO, excelVos)) {
                excelVO.setPriceSource(2);//系统
                excelVO.setIsLowestLevel(false);
                //非最下级  只需要定义名称、单位
                if (StringUtils.isBlank(excelVO.getName())) {
                    errorMsgList.add(String.format("行：%s，名称字段数据不能为空", offsetLine + 2 + i));
                    addErrorMsg(excelVO, "名称字段数据不能为空");
                }
                if (StringUtils.isBlank(excelVO.getUnit())) {
                    errorMsgList.add(String.format("行：%s，单位字段数据不能为空", offsetLine + 2 + i));
                    addErrorMsg(excelVO, "单位字段数据不能为空");
                } else {
                    for (DictDto dictDto : unitList) {
                        if (dictDto.getName().equals(excelVO.getUnit())) {
                            excelVO.setUnitCode(dictDto.getCode());
                            break;
                        }
                    }
                }
                if (StringUtils.isBlank(excelVO.getUnitCode())) {
                    errorMsgList.add(String.format("行：%s，单位字段必须为系统已有的单位", offsetLine + 2 + i));
                    addErrorMsg(excelVO, "单位字段必须为系统已有的单位");
                }
            } else {
                excelVO.setIsLowestLevel(true);
                /**
                 * 最下级          价格来源、数量
                 *  根据价格来源->价格库：PAM编码、公开价单价（手工必填）
                 *              手  工 ：名称、属性、品牌、单位、成本单价、公开价单价（非按产品意向必填）
                 */
                if (StringUtils.isBlank(excelVO.getPriceValue())) {
                    errorMsgList.add(String.format("行：%s，价格来源字段数据不能为空", offsetLine + 2 + i));
                    addErrorMsg(excelVO, "价格来源字段数据不能为空");
                } else {
                    if (!excelVO.getPriceValue().equals("价格库") && !excelVO.getPriceValue().equals("手工")) {
                        errorMsgList.add(String.format("行：%s，价格来源字段数据只能为价格库或手工", offsetLine + 2 + i));
                        addErrorMsg(excelVO, "价格来源字段数据只能为价格库或手工");
                        continue;
                    }
                }

                if (Objects.equals(excelVO.getPriceValue(), "手工")) {
                    excelVO.setPriceSource(1);
                    if (StringUtils.isBlank(excelVO.getName())) {
                        errorMsgList.add(String.format("行：%s，名称字段数据不能为空", offsetLine + 2 + i));
                        addErrorMsg(excelVO, "名称字段数据不能为空");
                    }
                    if (StringUtils.isNotBlank(excelVO.getMaterialAttribute())) {
                        if (!excelVO.getMaterialAttribute().equals("自制") && !excelVO.getMaterialAttribute().equals("外购")) {
                            errorMsgList.add(String.format("行：%s，属性字段数据只能为自制或外购", offsetLine + 2 + i));
                            addErrorMsg(excelVO, "属性字段数据只能为自制或外购");
                        }
                    } else {
                        errorMsgList.add(String.format("行：%s，属性字段数据不能为空", offsetLine + 2 + i));
                        addErrorMsg(excelVO, "属性字段数据不能为空");
                    }
                    if (StringUtils.isBlank(excelVO.getBrand())) {
                        errorMsgList.add(String.format("行：%s，品牌字段数据不能为空", offsetLine + 2 + i));
                        addErrorMsg(excelVO, "品牌字段数据不能为空");
                    }

                    if (StringUtils.isBlank(excelVO.getUnit())) {
                        errorMsgList.add(String.format("行：%s，单位字段数据不能为空", offsetLine + 2 + i));
                        addErrorMsg(excelVO, "单位字段数据不能为空");
                    } else {
                        for (DictDto dictDto : unitList) {
                            if (dictDto.getName().equals(excelVO.getUnit())) {
                                excelVO.setUnitCode(dictDto.getCode());
                                break;
                            }
                        }
                    }
                    if (StringUtils.isBlank(excelVO.getUnitCode())) {
                        errorMsgList.add(String.format("行：%s，单位字段必须为系统已有的单位", offsetLine + 2 + i));
                        addErrorMsg(excelVO, "单位字段必须为系统已有的单位");
                    }

                    if (StringUtils.isBlank(excelVO.getUnitCost())) {
                        errorMsgList.add(String.format("行：%s，成本单价字段数据不能为空", offsetLine + 2 + i));
                        addErrorMsg(excelVO, "成本单价字段数据不能为空");
                    } else {
                        if (NumberUtils.isNumber(excelVO.getUnitCost()) && new BigDecimal(excelVO.getUnitCost()).compareTo(BigDecimal.ZERO) > 0) {
                            if (BigDecimalUtils.numberOfDecimalPlaces(excelVO.getUnitCost()) > 6) {
                                errorMsgList.add(String.format("行：%s，成本单价字段不能超过6位小数", offsetLine + 2 + i));
                                addErrorMsg(excelVO, "成本单价字段不能超过6位小数");
                            }
                        } else {
                            errorMsgList.add(String.format("行：%s，成本单价字段数据必须为大于等于0的数字", offsetLine + 2 + i));
                            addErrorMsg(excelVO, "成本单价字段数据必须为大于等于0的数字");
                        }
                    }
                } else if (Objects.equals(excelVO.getPriceValue(), "价格库")) {
                    excelVO.setPriceSource(0);
                    if (StringUtils.isBlank(excelVO.getPamCode())) {
                        errorMsgList.add(String.format("行：%s，PAM编码字段数据不能为空", offsetLine + 2 + i));
                        addErrorMsg(excelVO, "PAM编码字段数据不能为空");
                    }
                }
                //公开价必填检查
                if (Objects.equals(publicPriceCalculation, "\"0\"") ||
                        (Objects.equals(publicPriceCalculation, "\"2\"") && Objects.equals(excelVO.getPriceValue(), "手工"))) {
                    if (excelVO.getUnitPrice() == null) {
                        errorMsgList.add(String.format("行：%s，公开价字段数据不能为空", offsetLine + 2 + i));
                        addErrorMsg(excelVO, "公开价字段数据不能为空");
                    } else {
                        if (NumberUtils.isNumber(excelVO.getUnitPrice()) && new BigDecimal(excelVO.getUnitPrice()).compareTo(BigDecimal.ZERO) > 0) {
                            if (BigDecimalUtils.numberOfDecimalPlaces(excelVO.getUnitPrice()) > 6) {
                                errorMsgList.add(String.format("行：%s，公开价字段不能超过6位小数", offsetLine + 2 + i));
                                addErrorMsg(excelVO, "公开价字段不能超过6位小数");
                            }
                        } else {
                            errorMsgList.add(String.format("行：%s，公开价字段数据必须为大于等于0的数字", offsetLine + 2 + i));
                            addErrorMsg(excelVO, "公开价字段数据必须为大于等于0的数字");
                        }
                    }
                }
            }
        }

    }

    /**
     * 获取产品意向配置
     *
     * @return
     */
    private String getPublicPriceCalculationByFormConfig() {
        return formConfigService.getPublicPriceCalculationBycode(SystemContext.getUnitId(), FormConfigEnums.PLAN.getCode(),
                FormConfigEnums.PUBLIC_PRICE_CALCULATION.getCode());
    }

    private void addErrorMsg(PlanImportExcelVO excelVO, String errorMsg) {
        if (StringUtils.isNotEmpty(excelVO.getErrorMsg())) {
            excelVO.setErrorMsg(excelVO.getErrorMsg() + "," + errorMsg);
        } else {
            excelVO.setErrorMsg(errorMsg);
        }
    }

    public static boolean findLowestLevels(PlanImportExcelVO excelVO, List<PlanImportExcelVO> excelVOS) {
        String regex = excelVO.getSerialNumber() + "\\..*";
        for (PlanImportExcelVO otherExcelVO : excelVOS) {
            if (otherExcelVO.getSerialNumber().matches(regex) && !otherExcelVO.getSerialNumber().equals(excelVO.getSerialNumber())) {
                return true;
            }
        }
        return false;
    }

    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime() || nowTime.getTime() == endTime.getTime()) {
            return true;
        }
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);
        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);
        Calendar end = Calendar.getInstance();
        end.setTime(endTime);
        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 生成解析后数据
     *
     * @param importExcelVos
     * @return
     */
    private List<PlanProductCostDto> generatePlanProductCostDto(List<PlanImportExcelVO> importExcelVos) {
        List<PlanProductCostDto> dtoList = new ArrayList<>();
        for (PlanImportExcelVO excelVO : importExcelVos) {
            PlanProductCostDto planProductCostDto = new PlanProductCostDto();
            planProductCostDto.setOrderNo(excelVO.getSerialNumber());
            planProductCostDto.setName(excelVO.getName());
            planProductCostDto.setPriceSource(excelVO.getPriceSource());
            planProductCostDto.setAmount(new BigDecimal(excelVO.getAmount()));
            planProductCostDto.setPamCode(excelVO.getPamCode());
            planProductCostDto.setErpCode(excelVO.getErpCode());
            planProductCostDto.setMaterialPriceId(excelVO.getMaterialPriceId());
            if (StringUtils.isNotEmpty(excelVO.getUnitCost())) {
                planProductCostDto.setUnitCost(new BigDecimal(excelVO.getUnitCost()));
            }
            if (StringUtils.isNotEmpty(excelVO.getUnitPrice())) {
                planProductCostDto.setUnitPrice(new BigDecimal(excelVO.getUnitPrice()));
            }
            planProductCostDto.setUnit(excelVO.getUnit());
            planProductCostDto.setUnitCode(excelVO.getUnitCode());
            if (excelVO.getIsLowestLevel()) {
                planProductCostDto.setMaterialAttribute(excelVO.getMaterialAttribute());
            }
            planProductCostDto.setBrand(excelVO.getBrand());
            planProductCostDto.setDescr(excelVO.getDescr());
            planProductCostDto.setRemark(excelVO.getRemark());
            dtoList.add(planProductCostDto);
        }
        return dtoList;
    }

    /**
     * 产品意向合法性检查
     *
     * @param dto
     * @throws ParseException
     */
    private void checkPublicPriceCalculation(PlanCurrencyCostDto dto) throws ParseException {
        String productIntentionName = dto.getProductIntentionName();
        if (StringUtils.isBlank(productIntentionName)) {
            throw new BizException(Code.ERROR, "当前单位公开价获取方式配置为根据产品意向获取,入参产品意向名不能为空，请确认！");
        } else {
            List<PlanProductCostDto> list = dto.getList();
            //检查成本系数是否正常获取
            List<String> coefficientTypes =
                    list.stream().filter(e -> Objects.equals(0, e.getPriceSource())).map(PlanProductCostDto::getMaterialAttribute).distinct().collect(Collectors.toList());
            for (String coefficientType : coefficientTypes) {
                CostCoefficientDto queryDto = new CostCoefficientDto();
                queryDto.setUnitId(SystemContext.getUnitId());
                queryDto.setProductIntention(productIntentionName);
                queryDto.setCoefficientType(coefficientType);
                List<CostCoefficientDto> costCoefficientDtos = costCoefficientService.queryList(queryDto);
                if (ListUtils.isEmpty(costCoefficientDtos)) {
                    throw new BizException(Code.ERROR, String.format("产品意向：%s,费用类型：%s没有获取到成本系数信息，请联系财务人员进行维护！", productIntentionName,
                            coefficientType));
                } else {
                    CostCoefficientDto costCoefficientDto = costCoefficientDtos.get(0);
                    Date startDate = costCoefficientDto.getCommissioningDate();
                    Date endDate = costCoefficientDto.getExpiryDate();
                    Date now = DateUtils.getShortDate(new Date());
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    if (Objects.isNull(startDate)) {
                        startDate = sdf.parse("2000-1-1");
                    }
                    if (Objects.isNull(endDate)) {
                        endDate = sdf.parse("2099-12-30");
                    }
                    if (!isEffectiveDate(now, startDate, endDate)) {
                        throw new BizException(Code.ERROR, String.format("产品意向：%s,费用类型：%s没有获取到符合当前时间范围内的成本系数信息，请联系财务人员进行维护", productIntentionName,
                                coefficientType));
                    }
                }
            }
        }
    }

    /**
     * list根据orderNo转树形
     *
     * @param dtoList list
     * @return list转树形
     */
    public static List<PlanProductCostDto> streamToTreeByOrderNo(List<PlanProductCostDto> dtoList) {
        Map<String, PlanProductCostDto> map = new HashMap<>();
        List<PlanProductCostDto> resultList = new ArrayList<>();
        for (PlanProductCostDto dto : dtoList) {
            map.put(dto.getOrderNo(), dto);
        }
        for (PlanProductCostDto dto : dtoList) {
            String orderNo = dto.getOrderNo();
            int dotIndex = orderNo.lastIndexOf(".");
            if (dotIndex != -1) {
                String parentOrderNo = orderNo.substring(0, dotIndex);
                PlanProductCostDto parent = map.get(parentOrderNo);
                if (parent != null) {
                    if (parent.getSonDtos() == null) {
                        parent.setSonDtos(new ArrayList<>());
                    }
                    parent.getSonDtos().add(dto);
                }
            } else {
                resultList.add(dto);
            }
        }
        for (PlanProductCostDto dto : dtoList) {
            if (dto.getSonDtos() == null) {
                dto.setSonDtos(new ArrayList<>());
            } else {
                //将系统更改未手工或者价格库 ：根据下级得出上级的价格来源
                setPriceSource(dto);
            }
        }
        return resultList;
    }

    private static int setPriceSource(PlanProductCostDto node) {
        List<PlanProductCostDto> children = node.getSonDtos();
        if (children != null) {
            int childPriceSource = 1;
            for (PlanProductCostDto child : children) {
                childPriceSource &= setPriceSource(child);
            }
            node.setPriceSource(childPriceSource);
            return childPriceSource;
        }
        return node.getPriceSource();
    }

    private BusinessDto getBusinessById(Long businessId) {
        String url = String.format("%sbusiness/getBusinessById?id=%s", ModelsEnum.CRM.getBaseUrl(), businessId);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<BusinessDto> dataResponse = JSON.parseObject(res, new com.alibaba.fastjson.TypeReference<DataResponse<BusinessDto>>() {
        });
        assert dataResponse != null;
        Assert.isTrue(dataResponse.getCode() == 0, dataResponse.getMsg());
        Assert.notNull(dataResponse.getData(), "根据ID查询商机详情失败!");
        return dataResponse.getData();
    }

    /**
     * 单独处理采购订单价格
     *
     * @param materialPriceList
     */
    @Transactional(rollbackFor = Exception.class)
    private void insertOrUpdateMaterialPriceByOrderPrice(List<MaterialPrice> materialPriceList, List<MaterialPrice> oldList) {
        //解决一物多码问题 pamCode 重复，故将 A码与P码拼接作为key
        //锁定的数据
        List<String> lockList = oldList.stream().filter(e -> e.getPriceIsLock() == 1).map(e -> String.format("key_%s_%s", e.getErpCode(), e.getPamCode())).collect(Collectors.toList());
        //取现有库中的价格
        Map<String, BigDecimal> map = oldList.stream().collect(Collectors.toMap(e ->
                String.format("key_%s_%s", e.getErpCode(), e.getPamCode()), MaterialPrice::getAmount, (e1, e2) -> e2));

        // 插入的List
        List<MaterialPrice> insertList = new ArrayList<>();
        // 更新的List
        List<MaterialPrice> updateList = new ArrayList<>();
        // 变更表的List
        List<MaterialPriceHistory> materialPriceHistoryList = new ArrayList<>();

        for (MaterialPrice materialPrice : materialPriceList) {
            //非空的materialId不处理
            if (Objects.nonNull(materialPrice.getMaterialId())) {
                String erpCode = materialPrice.getErpCode();
                String pamCode = materialPrice.getPamCode();
                if (Objects.isNull(map.get(String.format("key_%s_%s", erpCode, pamCode)))) {
                    logger.info("采购订单价格新增数据：" + pamCode);
                    insertList.add(materialPrice);
                    MaterialPriceHistory materialPriceHistory = BeanConverter.copy(materialPrice, MaterialPriceHistory.class);
                    materialPriceHistoryList.add(materialPriceHistory);
                }
                // 如果现有存在且价格没被锁定，且价格有更新
                else if (!lockList.contains(String.format("key_%s_%s", erpCode, pamCode)) && materialPrice.getAmount().compareTo(map.getOrDefault(String.format("key_%s_%s", erpCode, pamCode), BigDecimal.ZERO)) != 0) {
                    logger.info("采购订单价格更新数据：" + pamCode);
                    materialPrice.setUpdateBy(SystemContext.getUserId());
                    materialPrice.setUpdateAt(new Date());
                    materialPrice.setModifiedBy(SystemContext.getUserId());
                    materialPrice.setModifiedTime(new Date());
                    updateList.add(materialPrice);
                    MaterialPriceHistory materialPriceHistory = BeanConverter.copy(materialPrice, MaterialPriceHistory.class);
                    materialPriceHistory.setOriginalPrice(map.get(String.format("key_%s_%s", erpCode, pamCode)));
                    materialPriceHistoryList.add(materialPriceHistory);
                }
                // 这部分数据需要排查
                else {
                    logger.info("无需处理的数据：" + pamCode);
                }
            }
        }
        /* 历史记录插入 */
        if (CollectionUtils.isNotEmpty(materialPriceHistoryList)) {
            List<List<MaterialPriceHistory>> lists1 = ListUtils.splistList(materialPriceHistoryList, 500);
            for (List<MaterialPriceHistory> list1 : lists1) {
                logger.info("批量插入material_price历史表");
                materialPriceHistoryExtMapper.batchInsert(list1);
            }
        }
        /* 更新 */
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<MaterialPrice>> lists2 = ListUtils.splistList(updateList, 500);
            for (List<MaterialPrice> list2 : lists2) {
                logger.info("批量更新material_price表");
                materialPriceExtMapper.updateBatch(list2);
            }
        }
        /* 插入 */
        if (CollectionUtils.isNotEmpty(insertList)) {
            List<List<MaterialPrice>> lists3 = ListUtils.splistList(insertList, 500);
            for (List<MaterialPrice> list3 : lists3) {
                logger.info("批量插入material_price表");
                batchInsert(list3);
            }
        }
    }
}
