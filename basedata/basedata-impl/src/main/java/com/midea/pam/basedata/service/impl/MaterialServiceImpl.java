package com.midea.pam.basedata.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.midea.esb.RequestHeader;
import com.midea.mcomponent.core.util.ListUtil;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.basedata.common.enums.ErpCodeSourceBaseData;
import com.midea.pam.basedata.common.enums.MaterialCategoryEnum;
import com.midea.pam.basedata.common.utils.BeanConverter;
import com.midea.pam.basedata.common.utils.DateUtil;
import com.midea.pam.basedata.esb.util.EsbBasedataUtil;
import com.midea.pam.basedata.mapper.ErpCodeRuleExtMapper;
import com.midea.pam.basedata.mapper.EsbMassQueryRecordMapper;
import com.midea.pam.basedata.mapper.MaterialCustomDictExtMapper;
import com.midea.pam.basedata.mapper.MaterialCustomDictHeaderExtMapper;
import com.midea.pam.basedata.mapper.MaterialExtMapper;
import com.midea.pam.basedata.mapper.MaterialMapper;
import com.midea.pam.basedata.mapper.MilepostDesignPlanMiddleExtMapper;
import com.midea.pam.basedata.mapper.MilepostDesignPlanMiddleMapper;
import com.midea.pam.basedata.mapper.OrganizationCustomDictExtMapper;
import com.midea.pam.basedata.mapper.ProjectExtMapper;
import com.midea.pam.basedata.mapper.ProjectTypeExtMapper;
import com.midea.pam.basedata.mapper.RocketMQMsgParameterExtMapper;
import com.midea.pam.basedata.rocketmq.service.CommonRocketMQProducerService;
import com.midea.pam.basedata.service.AsyncRequestResultBaseDataService;
import com.midea.pam.basedata.service.BrandMaintenanceService;
import com.midea.pam.basedata.service.CtcExtService;
import com.midea.pam.basedata.service.EsbService;
import com.midea.pam.basedata.service.LtcDictService;
import com.midea.pam.basedata.service.MaterialCostService;
import com.midea.pam.basedata.service.MaterialService;
import com.midea.pam.basedata.service.MilepostDesignPlanMiddleService;
import com.midea.pam.basedata.service.OrganizationRelService;
import com.midea.pam.basedata.service.SdpCarrierService;
import com.midea.pam.basedata.service.UnitService;
import com.midea.pam.basedata.service.event.MatchingMaterialCodeBaseDataEvent;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.MaterialQueryDTO;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.AsyncRequestResultBaseData;
import com.midea.pam.common.basedata.entity.BrandMaintenance;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.basedata.entity.MaterialCost;
import com.midea.pam.common.basedata.entity.MaterialExample;
import com.midea.pam.common.basedata.entity.MilepostDesignPlanMiddle;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.MaterialQuery;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.ErpCodeRuleDto;
import com.midea.pam.common.ctc.dto.MaterialAdjustDetailDTO;
import com.midea.pam.common.ctc.dto.MaterialAdjustHeaderDTO;
import com.midea.pam.common.ctc.dto.MaterialChangeDetailDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.ResendExecuteDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.entity.ErpCodeRule;
import com.midea.pam.common.ctc.entity.MaterialCustomDict;
import com.midea.pam.common.ctc.entity.MaterialCustomDictHeader;
import com.midea.pam.common.ctc.entity.MaterialCustomDictHeaderExample;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.excelVo.ImportAssemblyPartsAndBudgetLayerExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailImportExcelVo;
import com.midea.pam.common.ctc.vo.MerielSubclassVO;
import com.midea.pam.common.enums.*;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.gateway.entity.RocketMQMsgParameter;
import com.midea.pam.common.rocketmq.dto.MsgSendDto;
import com.midea.pam.common.sdp.dto.GERPInvItemImportDTO;
import com.midea.pam.common.sdp.vo.ResponseObj;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.SnowFlakeUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.common.util.idaas.TwoTuple;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import com.midea.sdp.dto.common.SdpTradeResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class MaterialServiceImpl implements MaterialService {

    private static final Logger logger = LoggerFactory.getLogger(MaterialServiceImpl.class);

    private final static Long MAX_WAIT_TIME_SYNC = (long) 1000 * 60 * 10;//10分钟

    private static final Integer EACH_THREAD_DATA_NUM = 500;

    private final static String MATERIAL_TYPE = "material_type";
    private final static String MACHINED_PART_TYPE = "jiagongjian_type";
    private final static String ERROR_JGJFL_KEY = "ERROR_JGJFL_KEY";
    private final static String ERROR_CZ_KEY = "ERROR_CZ_KEY";
    private final static String ERROR_DWZL_KEY = "ERROR_DWZL_KEY";
    private final static String ERROR_JGJFL = "加工件分类";
    private final static String ERROR_CZ = "材质";
    private final static String ERROR_DWZL = "单位重量(Kg)";

    private final static String ERROR_TH_KEY = "ERROR_TH_KEY";
    private final static String ERROR_TZBBH_KEY = "ERROR_TZBBH_KEY";
    private final static String ERROR_BMCL_KEY = "ERROR_BMCL_KEY";
    private final static String ERROR_TH = "图号";
    private final static String ERROR_TZBBH = "图纸版本号";
    private final static String ERROR_BMCL = "表面处理";
    private final static String MACHINED_PART = "机械加工件";

    @Resource
    private MaterialMapper materialMapper;
    @Resource
    private MaterialService materialService;
    @Resource
    private MaterialExtMapper materialExtMapper;
    @Resource
    EsbService esbService;
    @Resource
    private OrganizationRelService organizationRelService;
    @Resource
    private MaterialCostService materialCostService;
    @Resource
    private CtcExtService ctcExtService;
    @Value("${rocketmq.basedata.inv-item-import.topic}")
    private String basedataInvItemImportTopic;
    @Resource
    private CommonRocketMQProducerService commonRocketMQProducerService;
    @Resource
    private RocketMQMsgParameterExtMapper rocketMQMsgParameterExtMapper;
    @Resource
    private LtcDictService ltcDictService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OrganizationCustomDictExtMapper organizationCustomDictExtMapper;
    @Resource
    private MaterialCustomDictHeaderExtMapper materialCustomDictHeaderExtMapper;
    @Resource
    private MaterialCustomDictExtMapper materialCustomDictExtMapper;
    @Resource
    private SdpCarrierService sdpCarrierService;
    @Resource
    private UnitService unitService;
    @Resource
    private ErpCodeRuleExtMapper erpCodeRuleExtMapper;
    @Resource
    private BrandMaintenanceService brandMaintenanceService;
    @Resource
    private MilepostDesignPlanMiddleMapper middleMapper;
    @Resource
    private MilepostDesignPlanMiddleService milepostDesignPlanMiddleService;
    @Resource
    private AsyncRequestResultBaseDataService asyncRequestResultBaseDataService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private MilepostDesignPlanMiddleExtMapper middleExtMapper;
    @Qualifier("importAssemblyPartsAndBudgetLayerExecutor")
    @Resource
    private ThreadPoolTaskExecutor importAssemblyPartsAndBudgetLayerExecutor;
    @Resource
    private ProjectExtMapper projectExtMapper;
    @Resource
    private ProjectTypeExtMapper projectTypeExtMapper;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private EsbMassQueryRecordMapper esbMassQueryRecordMapper;


    @Override
    public PageInfo<MaterialDto> getMaterialPage(MaterialQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<MaterialDto> materialList = materialExtMapper.getMaterialList(query);
        return new PageInfo<>(materialList);
    }

    @Override
    public List<MaterialDto> getMaterialList(MaterialQuery query) {
        List<MaterialDto> materialList = materialExtMapper.getMaterialList(query);
        return materialList;
    }

    @Override
    public int add(Material material) {
        material.setCreateAt(new Date());
        if (material.getCreateBy() == null) {
            material.setCreateBy(SystemContext.getUserId());
        }
        material.setDeleteFlag(false);
        material.setDelistFlag(Boolean.FALSE);//是否待退市物料
        String itemInfo = material.getItemInfo();
        if (StringUtils.isNotEmpty(itemInfo)) {
            material.setItemInfo(itemInfo.replaceAll("\r|\n", " ").replaceAll(",", " ").replaceAll("，", " "));
        }
        if (StringUtils.isEmpty(material.getMaterialAttribute())) {
            if (Objects.equals(material.getMaterialCategory(), "外购物料")) {
                material.setMaterialAttribute("外购件");
            } else if (Objects.equals(material.getMaterialCategory(), "看板物料")) {
                material.setMaterialAttribute("看板件");
            }
        }
        return materialMapper.insertSelective(material);
    }

    @Override
    public Boolean checkMaterialDelistBatch(List<MaterialChangeDetailDTO> materialChangeDetailDTOS) {
        if (CollectionUtils.isEmpty(materialChangeDetailDTOS)) return false;
        Map<Long, Boolean> delistFlagNewMap = new HashMap<>();
        for (MaterialChangeDetailDTO detailDTO : materialChangeDetailDTOS) {
            if (detailDTO.getMaterialId() != null && detailDTO.getDelistFlagNew() != null) {
                delistFlagNewMap.put(detailDTO.getMaterialId(), detailDTO.getDelistFlagNew());
            }
        }
        if (MapUtils.isNotEmpty(delistFlagNewMap)) {
            Long unitId = materialChangeDetailDTOS.get(0).getUnitId();
            MaterialExample example = new MaterialExample();
            example.createCriteria().andIdIn(new ArrayList<>(delistFlagNewMap.keySet()));
            List<Material> oldMaterialList = materialMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(oldMaterialList)) {
                List<String> errorMsgList = new ArrayList<>();
                // 查询当前使用单位的物料编码规则
                String materialCodeRule = getMaterialCodeRule(unitId);
                for (Material oldMaterial : oldMaterialList) {
                    Material material = BeanConverter.copy(oldMaterial, Material.class);
                    material.setDelistFlag(delistFlagNewMap.get(material.getId()));
                    this.checkMaterialDelist(material, oldMaterial, materialCodeRule, errorMsgList);
                }
                if (CollectionUtils.isNotEmpty(errorMsgList)) {
                    throw new ApplicationBizException(String.join("；", errorMsgList));
                }
            }
        }
        return true;
    }

    @Override
    public Boolean checkMaterialSyncErp(List<Long> materialIdList) {
        List<String> errorMsgList = new ArrayList<>();
        MaterialExample example = new MaterialExample();
        example.createCriteria().andIdIn(materialIdList);
        List<Material> materialList = materialMapper.selectByExample(example);
        for (Material material : materialList) {
            if (material.getItemId() == null) {
                errorMsgList.add(String.format("物料：%S，未同步ERP", material.getItemCode()));
            }
        }
        if (CollectionUtils.isNotEmpty(errorMsgList)) {
            throw new ApplicationBizException(String.join("；", errorMsgList));
        }
        return Boolean.TRUE;
    }

    @Override
    public List<MaterialDto> listByItemCodesAndOrganizationIds(List<MaterialDto> materialDtos) {
        if (CollectionUtils.isEmpty(materialDtos)) {
            return new ArrayList<>();
        }
        return materialExtMapper.listByItemCodesAndOrganizationIds(materialDtos);
    }

    @Override
    public List<MaterialDto> listMaterialByTransferCost(String fuzzyLike, String fuzzyLikeDelist,
                                                        Long transferOutOrganizationId, Integer transferOutMaterialType,
                                                        Long transferInOrganizationId, Integer transferInMaterialType,
                                                        Long transferOutTicketTaskId, Long transferInTicketTaskId, String itemCodes) {
        Map<String, Object> transferOutParam = new HashMap<>();
        transferOutParam.put("organizationId", transferOutOrganizationId);
        transferOutParam.put("fuzzyLike", fuzzyLike);
        transferOutParam.put("materialGetType", transferOutMaterialType);
        transferOutParam.put("ticketTaskId", transferOutTicketTaskId);
        transferOutParam.put("fuzzyLikeDelist", fuzzyLikeDelist);
        if (StringUtils.hasText(itemCodes)) {
            transferOutParam.put("itemCodeList", Arrays.asList(itemCodes.split(",")));
        }
        List<MaterialDto> transferOutMaterials = new ArrayList<MaterialDto>();
        //工单退料:0,项目退料:1,项目退料(WBS):2
        if (transferOutMaterialType == 0) {
            transferOutMaterials = materialExtMapper.getMaterialListByOrgAndTicketTaskId(transferOutParam);
        } else if (transferOutMaterialType == 1) {
            transferOutMaterials = materialExtMapper.getMaterialListByOrg(transferOutParam);
        } else if (transferOutMaterialType == 2) {
            transferOutMaterials = materialExtMapper.getMaterialListByOrg(transferOutParam);
        }

        Map<String, Object> transferInParam = new HashMap<>();
        transferInParam.put("organizationId", transferInOrganizationId);
        transferInParam.put("fuzzyLike", fuzzyLike);
        transferInParam.put("materialGetType", transferInMaterialType);
        transferInParam.put("ticketTaskId", transferInTicketTaskId);
        transferInParam.put("fuzzyLikeDelist", fuzzyLikeDelist);
        if (StringUtils.hasText(itemCodes)) {
            transferInParam.put("itemCodeList", Arrays.asList(itemCodes.split(",")));
        }
        List<MaterialDto> transferInMaterials = new ArrayList<MaterialDto>();
        //工单退料:0,项目退料:1,项目退料(WBS):2
        if (transferInMaterialType == 0) {
            transferInMaterials = materialExtMapper.getMaterialListByOrgAndTicketTaskId(transferInParam);
        } else if (transferInMaterialType == 1) {
            transferInMaterials = materialExtMapper.getMaterialListByOrg(transferInParam);
        } else if (transferInMaterialType == 2) {
            transferInMaterials = materialExtMapper.getMaterialListByOrg(transferInParam);
        }
        List<Long> materialIds = transferInMaterials.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<MaterialDto> materialDtoList =
                transferOutMaterials.stream().filter(item -> materialIds.contains(item.getId())).collect(Collectors.toList());
        return materialDtoList;
    }

    /**
     * 物料退市状态修改校验
     *
     * @param material         物料修改后
     * @param oldMaterial      物料修改前
     * @param materialCodeRule 物料编码规则
     * @param errorMsgList     错误信息
     */
    private void checkMaterialDelist(Material material, Material oldMaterial, String materialCodeRule, List<String> errorMsgList) {
        Boolean oldDelistFlag = oldMaterial.getDelistFlag();
        Boolean delistFlag = material.getDelistFlag();
        if (!Objects.equals(oldDelistFlag, delistFlag)) {
            ErpCodeRuleDto query = new ErpCodeRuleDto();
            query.setDeletedFlag(Boolean.FALSE);
            query.setRuleName(materialCodeRule);
            List<ErpCodeRule> erpCodeRuleList = erpCodeRuleExtMapper.selectByExample(query);
            Map<String, List<ErpCodeRule>> erpCodeRuleMap = new HashMap<>();

            if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
                erpCodeRuleMap.putAll(erpCodeRuleList.stream().collect(Collectors.groupingBy(e ->
                        getUpdateMaterialERPCodeRuleKeyKUNSHANKey(e.getCodingMiddleclass(), e.getCodingSubclass()))));
            } else if (MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
                erpCodeRuleMap.putAll(erpCodeRuleList.stream().collect(Collectors.groupingBy(e ->
                        getUpdateMaterialERPCodeRuleKeyKUKA2022Key(e.getCodingClass(), e.getCodingMiddleclass(), e.getCodingSubclass()))));
            }

            String pamCode = oldMaterial.getPamCode();
            String materialCategory = oldMaterial.getMaterialCategory();
            String materialClassification = oldMaterial.getMaterialClassification();
            String codingMiddleClass = oldMaterial.getCodingMiddleclass();
            String materielType = oldMaterial.getMaterialType();
            if ((Objects.equals(materialCategory, "外购物料") || Objects.equals(materialCategory, "看板物料"))) {
                if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
                    if (StringUtils.isNotEmpty(codingMiddleClass) && StringUtils.isNotEmpty(materielType)
                            && !erpCodeRuleMap.containsKey(getUpdateMaterialERPCodeRuleKeyKUNSHANKey(codingMiddleClass, materielType))) {
                        errorMsgList.add(String.format("物料基础数据的pam编码：%s的物料中类：%s，物料小类：%s，在物料编码规则：%s中不存在", pamCode, codingMiddleClass, materielType,
                                materialCodeRule));
//                        throw new ApplicationBizException(String.format("物料基础数据的pam编码：%s的物料中类：%s，物料小类：%s，在物料编码规则：%s中不存在", pamCode,
//                        codingMiddleClass, materielType, materialCodeRule));
                    }
                } else if (MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
                    if (StringUtils.isNotEmpty(materialClassification) && StringUtils.isNotEmpty(codingMiddleClass) && StringUtils.isNotEmpty(materielType)
                            && !erpCodeRuleMap.containsKey(getUpdateMaterialERPCodeRuleKeyKUKA2022Key(materialClassification, codingMiddleClass,
                            materielType))) {
                        errorMsgList.add(String.format("物料基础数据的pam编码：%s的物料大类：%s，物料中类：%s，物料小类：%s，在物料编码规则：%s中不存在", pamCode, materialClassification,
                                codingMiddleClass, materielType, materialCodeRule));
//                        throw new ApplicationBizException(String.format("物料基础数据的pam编码：%s的物料大类：%s，物料中类：%s，物料小类：%s，在物料编码规则：%s中不存在", pamCode,
//                        materialClassification, codingMiddleClass, materielType, materialCodeRule));
                    }
                }
            }
        }

        // 已存在相同唯一性条件、并且未退市未删除的pam编码XX
        if (Boolean.TRUE.equals(oldDelistFlag) && Boolean.FALSE.equals(delistFlag) && MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
            checkUniqueMaterial(material, errorMsgList);
        }
    }

    @Override
    public int update(Material material) {
        Guard.notNull(material, "修改的物料为空");
        Long id = material.getId();
        Guard.notNull(id, "修改的物料ID为空");
//        Material oldMaterial = materialMapper.selectByPrimaryKey(id);
//        Guard.notNull(oldMaterial, "修改的物料不存在");
        //退市状态修改校验
//        checkMaterialDelist(material, oldMaterial);
        material.setUpdateAt(new Date());
        material.setUpdateBy(SystemContext.getUserId());
        return materialMapper.updateByPrimaryKeySelective(material);
    }

    private String getUpdateMaterialERPCodeRuleKeyKUKA2022Key(String codingClass, String codingMiddleclass, String codingSubclass) {
        return String.format("getUpdateMaterialERPCodeRuleKeyKUKA2022Key_%s_%s_%s", codingClass, codingMiddleclass, codingSubclass);
    }

    private String getUpdateMaterialERPCodeRuleKeyKUNSHANKey(String codingMiddleclass, String codingSubclass) {
        return String.format("getUpdateMaterialERPCodeRuleKeyKUNSHANKey_%s_%s", codingMiddleclass, codingSubclass);
    }

    private void checkUniqueMaterial(Material material, List<String> errorMsgList) {
        // 非标件大类
        List<String> nonStandardMaterialType = ltcDictService.getLtcDict(DictType.MATERIAL_TYPE_NON_STANDARD.code(), null, null).stream()
                .map(Dict::getName).distinct().collect(Collectors.toList());
        MaterialExample example = new MaterialExample();
        MaterialExample.Criteria criteria = example.createCriteria();
        criteria.andDeleteFlagEqualTo(Boolean.FALSE).andDelistFlagEqualTo(Boolean.FALSE);
        if (nonStandardMaterialType.contains(material.getMaterialClassification())) {
            criteria.andFigureNumberEqualTo(material.getFigureNumber());
        } else {
            criteria.andBrandEqualTo(material.getBrand());
            criteria.andModelEqualTo(material.getModel());
        }
        List<Material> materialList = materialMapper.selectByExample(example);
        materialList = materialList.stream().filter(e -> !Objects.equals(e.getId(), material.getId())).collect(Collectors.toList());
        // 根据物料唯一性条件查找物料表有效物料，当前组织退市物料，与其他组织未退市物料相同，当前组织允许改为非退市
        if (CollectionUtils.isNotEmpty(materialList) && materialList.stream().noneMatch(e ->
                !Objects.equals(e.getOrganizationId(), material.getOrganizationId()) && Objects.equals(e.getPamCode(), material.getPamCode()))) {
            List<String> pamCodeList = materialList.stream().map(Material::getPamCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            errorMsgList.add(String.format("物料[%s]已存在相同唯一性条件、并且未退市未删除的pam编码：%s", material.getPamCode(), pamCodeList.toString()));
//            throw new ApplicationBizException(String.format("已存在相同唯一性条件、并且未退市未删除的pam编码：%s", pamCodeList.toString()));
        }
    }

    @Override
    public int erpSyncUpdate(Material material) {
        return materialExtMapper.erpSyncUpdate(material);
    }

    @Override
    public int delete(Long id) {
        Material material = materialMapper.selectByPrimaryKey(id);
        material.setUpdateAt(new Date());
        material.setDeleteFlag(true);
        return materialMapper.updateByPrimaryKeySelective(material);
    }

    @Override
    public void getMaterialFromErp(String lastUpdateDate, String lastUpdateDateEnd) {
        if (ObjectUtils.isEmpty(lastUpdateDate)) {
            Date yesterday = DateUtils.subtractDay(new Date(), 1);
            lastUpdateDate = DateUtils.format(yesterday, "yyyy-MM-dd 00:00:00");
        }
        if (ObjectUtils.isEmpty(lastUpdateDateEnd)) {
            lastUpdateDateEnd = DateUtils.format(new Date(), "yyyy-MM-dd 23:59:59");
        }
        Map<String, String> paramMap = new HashMap();
        //取有效的库存组织ID作为P01参数
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setPamEnabled(0);
        List<OrganizationRelDto> orgRelList = organizationRelService.list(query).getList();
        Set<Long> organizationIdSet = new HashSet<Long>();
        //去重
        for (OrganizationRelDto orgRel : orgRelList) {
            organizationIdSet.add(orgRel.getOrganizationId());
        }
        //ERP特殊逻辑-拉取MM0库存组织物料 -- 此逻辑不再需要
//        organizationIdSet.add(Constants.MMO_ORGANIZATION_ID);
        for (Long orgId : organizationIdSet) {
            paramMap.clear();
            paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(orgId));
//           paramMap.put(EsbConstant.ERP_IP_P02, inventoryLastUpdateDate);
            paramMap.put(EsbConstant.ERP_SDP_P03, lastUpdateDate);
            if (!ObjectUtils.isEmpty(lastUpdateDateEnd)) {
                paramMap.put(EsbConstant.ERP_SDP_P04, lastUpdateDateEnd);
            }
            this.getMaterialFromErp(paramMap);
        }
    }

    private Material findByUniqueKey(Material material) {
        MaterialExample example = new MaterialExample();
        example.createCriteria().
                andOrganizationIdEqualTo(material.getOrganizationId()).
                andItemCodeEqualTo(material.getItemCode()).
                andDeleteFlagEqualTo(Boolean.FALSE);
        List<Material> entitys = this.materialMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(entitys)) {
            return null;
        }
        return entitys.get(0);
    }

    @Override
    public void getMaterialFromErp(Map<String, String> paramMap) {
        //调SDP接口
        List<SdpTradeResultResponseEleDto> resultResponseEleDtos = sdpCarrierService.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_002, paramMap);
        for (SdpTradeResultResponseEleDto item : resultResponseEleDtos) {
            Material material = new Material();
            material.setOrganizationId(Long.valueOf(item.getC1()));//库存组织ID
            material.setItemId(Long.valueOf(item.getC2()));//物料ID
            material.setItemCode(item.getC3());//物料编码
            material.setItemInfo(item.getC4());//物料描述
            material.setUnit(item.getC5());//基本计量单位
            material.setItemType(item.getC6());//用户物料类型
            material.setItemStatus(item.getC7());//物料状态
            material.setBuyerNumber(item.getC8());//采购员
            material.setBuyerId(item.getC9());//采购员ID
            material.setBuyerRound(item.getC10());//采购周期
            material.setBondFlag(item.getC11());//保税标识
            material.setCodeName(item.getC12());//代号(图号)
            material.setItemCodeOld(item.getC13());//旧物料编码
            material.setFixedLotMultiplier(item.getC14());//固定批次增加
            if (StringUtils.isNotEmpty(item.getC19())) {
                Long minimumOrderQuantity = NumberUtil.isNumber(item.getC19()) ? BigDecimalUtils.stringToLong(item.getC19()) : null;
                material.setMinimumOrderQuantity(minimumOrderQuantity);//最小订货量
            }
            material.setRecevingSubinventory(item.getC20());//接受子库存
            material.setShelves(item.getC21());//货架
            material.setSourcer(item.getC22());//配套人员
            if (StringUtils.isNotEmpty(item.getC23())) {
                Long safetyStockQuantity = NumberUtil.isNumber(item.getC23()) ? BigDecimalUtils.stringToLong(item.getC23()) : null;
                material.setSafetyStockQuantity(safetyStockQuantity);//安全库存
            }
            //create Or update
            Material existEntity = this.findByUniqueKey(material);
            if (Objects.isNull(existEntity)) {
                this.add(material);
            } else {
                material.setId(existEntity.getId());
                materialMapper.updateByPrimaryKeySelective(material);
            }
        }
    }

    private String generateMaterialPAMCode() {
        StringBuffer pamCode = new StringBuffer();
        pamCode.append(CodePrefix.PAM_CODE.code());
        pamCode.append(CacheDataUtils.generateSequence(Constants.CODE_PAM_CODE_LENGTH, CodePrefix.PAM_CODE.code()));
        return pamCode.toString();
    }

    @Override
    public EsbResponse callErpItemImport(List<Long> ids) {
        MaterialExample example = new MaterialExample();
        MaterialExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        List<Material> materials = this.materialMapper.selectByExample(example);
        List<MaterialDto> materialdtos = BeanConverter.copy(materials, MaterialDto.class);

        for (MaterialDto materialDto : materialdtos) {
            if (materialDto.getItemType() != null && materialDto.getItemType().equals("P")) {
                List<MaterialCostDto> materialCostDtoList = materialCostService.getByPamCode(materialDto.getPamCode()
                        , materialDto.getOrganizationId());
                if (ListUtils.isNotEmpty(materialCostDtoList)) {
                    MaterialCostDto materialCostDto = materialCostDtoList.get(0);
                    materialDto.setDesignCost(materialCostDto.getItemCost());
                }
            }
        }
       // return esbService.callErpItemImport(materialdtos);
        return callErpItemImportFromSdp(materialdtos);
    }

    @Override
    public MaterialDto add(MaterialDto dto) {
        Material entity = BeanConverter.copy(dto, Material.class);
        entity.setCreateAt(new Date());
        if (entity.getCreateBy() == null) {
            entity.setCreateBy(SystemContext.getUserId());
        }
        entity.setDeleteFlag(false);
        String itemInfo = entity.getItemInfo();
        if (StringUtils.isNotEmpty(itemInfo)) {
            entity.setItemInfo(itemInfo.replaceAll("\r|\n", " ").replaceAll(",", " ").replaceAll("，", " "));
        }
        if (StringUtils.isEmpty(entity.getMaterialAttribute())) {
            if (Objects.equals(entity.getMaterialCategory(), "外购物料")) {
                entity.setMaterialAttribute("外购件");
            } else if (Objects.equals(entity.getMaterialCategory(), "看板物料")) {
                entity.setMaterialAttribute("看板件");
            }
        }
        materialMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public MaterialDto update(MaterialDto dto) {
        Material entity = BeanConverter.copy(dto, Material.class);
        entity.setUpdateAt(new Date());
        entity.setUpdateBy(SystemContext.getUserId());
        materialMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public MaterialDto save(MaterialDto dto, Long userBy) {
        if (dto.getId() == null) {
            if (userBy != null) {
                dto.setCreateBy(userBy);
            } else {
                dto.setCreateBy(SystemContext.getUserId());
            }
            dto.setErpCode(CommonStatus.TODO.getCode() + "");//待同步
            dto.setDelistFlag(Boolean.FALSE);//是否待退市物料
            return this.add(dto);
        } else {
            if (userBy != null) {
                dto.setUpdateBy(userBy);
            } else {
                dto.setUpdateBy(SystemContext.getUserId());
            }
            return this.update(dto);
        }
    }

    @Override
    public Boolean deleteByPamCode(String pamCode, Long organizationId) {
        MaterialDto dto = this.getByPamCode(pamCode, organizationId);
        if (dto != null) {
            dto.setDeleteFlag(true);
            this.save(dto, SystemContext.getUserId());
        }
        return true;
    }

    @Override
    public List<MaterialDto> selectList(MaterialDto query) {
        List<Material> list = materialMapper.selectByExample(buildCondition(query));
        List<MaterialDto> dtos = BeanConverter.copy(list, MaterialDto.class);
        return dtos;
    }

    @Override
    public MaterialDto view(Long id) {
        MaterialQuery query = new MaterialQuery();
        query.setId(id);
        List<MaterialDto> dtos = this.getMaterialList(query);
        if (ListUtils.isEmpty(dtos)) {
            return null;
        }
        MaterialDto materialDto = dtos.get(0);
        if (materialDto.getCreateBy() != null) {
            UserInfo user = CacheDataUtils.findUserById(materialDto.getCreateBy());
            if (user != null) {
                materialDto.setCreatorName(user.getName());
            }
        }
        if (materialDto.getUpdateBy() != null) {
            UserInfo user = CacheDataUtils.findUserById(materialDto.getUpdateBy());
            if (user != null) {
                materialDto.setUpdaterName(user.getName());
            }
        }
        return materialDto;
    }

    @Override
    public Boolean batchUpdate(List<Material> materialList) {
        return materialExtMapper.batchUpdate(materialList) > 0;
    }

    @Override
    public MaterialDto getByPamCode(String pamCode, Long organizationId) {

        MaterialQuery query = new MaterialQuery();
        query.setPamCode(pamCode);
        if (organizationId != null) {
            query.setOrganizationId(organizationId);
        } else {
            // 物料查询根据使用单位过滤
            query.setOrganizationIdList(queryCurrentUnitOrganizationId());
        }
        List<MaterialDto> dtos = this.getMaterialList(query);
        if (ListUtils.isEmpty(dtos)) {
            return null;
        }
        MaterialDto materialDto = dtos.get(0);
        if (materialDto.getCreateBy() != null) {
            UserInfo user = CacheDataUtils.findUserById(materialDto.getCreateBy());
            if (user != null) {
                materialDto.setCreatorName(user.getName());
            }
        }
        if (materialDto.getUpdateBy() != null) {
            UserInfo user = CacheDataUtils.findUserById(materialDto.getUpdateBy());
            if (user != null) {
                materialDto.setUpdaterName(user.getName());
            }
        }
        return materialDto;
    }

    @Override
    public List<MaterialDto> getByPamCodes(List<String> pamCodeList, Long organizationId) {
        if (CollectionUtils.isEmpty(pamCodeList)) {
            return new ArrayList<>();
        }
        MaterialQuery query = new MaterialQuery();
        query.setPamCodeList(pamCodeList);
        if (organizationId != null) {
            query.setOrganizationId(organizationId);
        } else {
            // 物料查询根据使用单位过滤
            query.setOrganizationIdList(queryCurrentUnitOrganizationId());
        }
        List<MaterialDto> dtos = this.getMaterialList(query);
        if (ListUtils.isEmpty(dtos)) {
            return new ArrayList<>();
        }
        for (MaterialDto materialDto : dtos) {
            if (materialDto.getCreateBy() != null) {
                UserInfo user = CacheDataUtils.findUserById(materialDto.getCreateBy());
                if (user != null) {
                    materialDto.setCreatorName(user.getName());
                }
            }
            if (materialDto.getUpdateBy() != null) {
                UserInfo user = CacheDataUtils.findUserById(materialDto.getUpdateBy());
                if (user != null) {
                    materialDto.setUpdaterName(user.getName());
                }
            }
        }
        return dtos;
    }

    @Override
    public MaterialDto getNotDeletedMaterialByPamCodeOrErpCode(String pamCode, String erpCode, Long organizationId) {
        List<MaterialDto> dtoList = materialExtMapper.getNotDeletedMaterialByPamCodeOrErpCode(pamCode, erpCode, organizationId);
        if (ListUtils.isEmpty(dtoList)) {
            return null;
        }
        return dtoList.get(0);
    }

    private List<Long> queryCurrentUnitOrganizationId() {
        Map<String, Object> param = new HashMap<>();
        param.put("unitId", SystemContext.getUnitId());
        String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operatingUnit/queryCurrentUnitOu",
                param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OperatingUnitDto>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
                });
        List<Long> organizationIdList = new ArrayList<>();
        List<OperatingUnitDto> operatingUnitDtoList = response.getData();
        if (CollectionUtils.isEmpty(operatingUnitDtoList)) {
            return organizationIdList;
        }
        for (OperatingUnitDto operatingUnitDto : operatingUnitDtoList) {
            if (operatingUnitDto.getRels() != null && !operatingUnitDto.getRels().isEmpty()) {
                for (OrganizationRel organizationRel : operatingUnitDto.getRels()) {
                    if (organizationRel != null && organizationRel.getOrganizationId() != null) {
                        organizationIdList.add(organizationRel.getOrganizationId());
                    }
                }
            }
        }
        return organizationIdList;
    }

    @Override
    public List<Material> listByPamCode(MaterialQueryDTO materialQueryDTO) {
        MaterialExample example = new MaterialExample();
        MaterialExample.Criteria criteria = example.createCriteria();
        criteria.andDeleteFlagEqualTo(false);

        if (CollectionUtils.isNotEmpty(materialQueryDTO.getPamCodeList())) {
            criteria.andPamCodeIn(materialQueryDTO.getPamCodeList());
        }
        if (CollectionUtils.isNotEmpty(materialQueryDTO.getOrganizationIdList())) {
            criteria.andOrganizationIdIn(materialQueryDTO.getOrganizationIdList());
        }
        if (materialQueryDTO.getOrganizationId() != null) {
            criteria.andOrganizationIdEqualTo(materialQueryDTO.getOrganizationId());
        }

        final List<Material> materials = materialMapper.selectByExample(example);
        if (ListUtils.isEmpty(materials)) {
            return new ArrayList<>();
        }
        return materials;
    }

    /*
     * 匹配规则：
     * 1、品牌/名称/规格型号/物料小类 => 精确匹配
     * 2、规格型号 => 模糊匹配
     * 3、物料名称 => 模糊匹配
     * 4、物料小类 => 物料小类为“机加件”
     */
    @Override
    public PageInfo<MaterialDto> materialWithCostPage(MaterialQuery query) {
        //PageInfo<MaterialDto> page = pageMaterial(query);
        PageInfo<MaterialDto> page = pageMaterialNew(query);

        if (page != null && ListUtils.isNotEmpty(page.getList())) {
            for (MaterialDto dto : page.getList()) {
                if (StringUtils.isNotEmpty(dto.getPamCode())) {
                    Long organizationId = query.getOrganizationId() != null ? query.getOrganizationId() :
                            dto.getOrganizationId();
                    List<MaterialCostDto> materialCostDtoList = materialCostService.getByPamCode(dto.getPamCode(),
                            organizationId);
                    if (ListUtils.isNotEmpty(materialCostDtoList)) {
                        MaterialCostDto materialCostDto = materialCostDtoList.get(0);
                        dto.setDesignCostId(materialCostDto.getId());
                        dto.setDesignCost(materialCostDto.getItemCost());
                    }
                }
                if (StringUtils.isNotEmpty(dto.getUnit())) {
                    DictDto unit = CacheDataUtils.findDictByTypeAndCode(DictType.MEASUREMENT_UNIT.code(),
                            dto.getUnit());
                    if (unit != null) {
                        dto.setUnitName(unit.getName());
                    }
                }
                if (null != dto.getOrganizationId()) {
                    OrganizationRelQuery organizationRelQuery = new OrganizationRelQuery();
                    organizationRelQuery.setPamEnabled(0);
                    organizationRelQuery.setOrganizationId(dto.getOrganizationId());
                    List<OrganizationRelDto> organizationRelDtos =
                            organizationRelService.selectOrganization(organizationRelQuery);
                    if (ListUtils.isNotEmpty(organizationRelDtos)) {
                        dto.setOrganizationName(organizationRelDtos.get(0).getOrganizationName());
                    }
                }
            }
        }

        return page;
    }

    private PageInfo<MaterialDto> pageMaterialNew(MaterialQuery query) {
        PageInfo<MaterialDto> page = null;
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        if ("机械加工件".equals(query.getMaterialMiddleClass())) { //物料中类+品牌+图号 检索相同编码
            page = this.getMaterialPageNew1(query);
        } else {
            if (null != query.getBrandMaterialCode()) { //品牌商物料编码存在  物料中类+品牌+品牌商物料编码 检索相同编码
                page = this.getMaterialPageNew3(query);
            } else { //品牌商物料编码不存在  物料中类+品牌+规格/型号 检索相同编码
                page = this.getMaterialPageNew2(query);
            }
        }
        return page;
    }

    private PageInfo<MaterialDto> getMaterialPageNew2(MaterialQuery query) {
        List<Material> materialList = materialExtMapper.checkMaterial2(query.getMaterialMiddleClass(),
                query.getBrand(), query.getModel(), null, query.getSecFuzzyMatch(), query.getItemInfo());
        List<MaterialDto> materialDtoList = BeanConverter.copy(materialList, MaterialDto.class);
        return new PageInfo<>(materialDtoList);
    }

    private PageInfo<MaterialDto> getMaterialPageNew3(MaterialQuery query) {
        List<Material> materialList = materialExtMapper.checkMaterial3(query.getMaterialMiddleClass(),
                query.getBrand(), query.getBrandMaterialCode(), null, query.getSecFuzzyMatch(), query.getItemInfo());
        if (materialList.size() == 0) { //物料中类+品牌+规格/型号  检索相同编码
            materialList = materialExtMapper.checkMaterial2(query.getMaterialMiddleClass(), query.getBrand(),
                    query.getModel(), null, query.getSecFuzzyMatch(), query.getItemInfo());
        }
        List<MaterialDto> materialDtoList = BeanConverter.copy(materialList, MaterialDto.class);
        return new PageInfo<>(materialDtoList);
    }

    private PageInfo<MaterialDto> getMaterialPageNew1(MaterialQuery query) {
        List<Material> materialList = materialExtMapper.checkMaterial1(query.getMaterialMiddleClass(),
                query.getBrand(), query.getFigureNumber(), null, query.getSecFuzzyMatch(), query.getItemInfo());
        List<MaterialDto> materialDtoList = BeanConverter.copy(materialList, MaterialDto.class);
        return new PageInfo<>(materialDtoList);
    }

    @Override
    public List<Material> listMaterialByCodeOrName(String fuzzyLike, String organizationIds) {
        MaterialExample materialExample = new MaterialExample();
//        final MaterialExample.Criteria criteria = materialExample.createCriteria();
//        criteria.andDeleteFlagEqualTo(Boolean.FALSE);

        if (StringUtils.isEmpty(organizationIds)) {
            return Lists.newArrayList();
        }
        String[] organizationIdArr = organizationIds.split(",");
        List<Long> organizationIdList = new ArrayList<>();
        for (String s : organizationIdArr) {
            organizationIdList.add(Long.valueOf(s));
        }

        final MaterialExample.Criteria nameCriteria = materialExample.createCriteria();
        nameCriteria.andNameLike(StringUtils.buildSqlLikeCondition(fuzzyLike));
        nameCriteria.andDeleteFlagEqualTo(Boolean.FALSE);
        nameCriteria.andOrganizationIdIn(organizationIdList);

        if (StringUtils.isNotEmpty(fuzzyLike)) {
            final MaterialExample.Criteria itemCodeCriteria = materialExample.createCriteria();
            itemCodeCriteria.andCodeNameLike(StringUtils.buildSqlLikeCondition(fuzzyLike));
            itemCodeCriteria.andDeleteFlagEqualTo(Boolean.FALSE);
            itemCodeCriteria.andOrganizationIdIn(organizationIdList);

            final MaterialExample.Criteria pamCodeCriteria = materialExample.createCriteria();
            pamCodeCriteria.andPamCodeLike(StringUtils.buildSqlLikeCondition(fuzzyLike));
            pamCodeCriteria.andDeleteFlagEqualTo(Boolean.FALSE);
            itemCodeCriteria.andOrganizationIdIn(organizationIdList);

            materialExample.or(itemCodeCriteria);
            materialExample.or(pamCodeCriteria);
        }

        final List<Material> materials = materialMapper.selectByExample(materialExample);
        if (materials == null) {
            return Lists.newArrayList();
        }
        return materials;
    }

    @Override
    public List<MaterialDto> listMaterialByOrg(String fuzzyLike, Long organizationId, Integer materialGetType,
                                               Long ticketTaskId, String itemCodes, String fuzzyLikeDelist) {
        Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        param.put("fuzzyLike", fuzzyLike);
        param.put("materialGetType", materialGetType);
        param.put("ticketTaskId", ticketTaskId);
        param.put("fuzzyLikeDelist", fuzzyLikeDelist);
        if (StringUtils.hasText(itemCodes)) {
            param.put("itemCodeList", Arrays.asList(itemCodes.split(",")));
        }
        List<MaterialDto> materials = new ArrayList<>();
        //工单退料:0,项目退料:1,项目退料(WBS):2
        if (materialGetType == 0) {
            materials = materialExtMapper.getMaterialListByOrgAndTicketTaskId(param);
        } else if (materialGetType == 1) {
            materials = materialExtMapper.getMaterialListByOrg(param);
        } else if (materialGetType == 2) {
            materials = materialExtMapper.getMaterialListByOrg(param);
        }
        return materials;
    }

    @Override
    public PageInfo<MaterialDto> materialByItemInfoPage(MaterialQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        // 构建查询条件
        Map<String, Object> param = new HashMap<>();
        param.put("itemInfo", query.getItemInfo());
        // 数据转换
        final List<MaterialDto> materials = materialExtMapper.getMaterialListByItemInfo(param);
        final PageInfo<MaterialDto> page = BeanConverter.convertPage(materials, MaterialDto.class);

        for (MaterialDto dto : page.getList()) {
            if (StringUtils.isNotEmpty(dto.getPamCode()) && null != dto.getOrganizationId()) {
                MaterialCost materialCost = materialCostService.getByPamCodeAndOrd(dto.getPamCode(),
                        dto.getOrganizationId());
                if (materialCost != null) {
                    dto.setDesignCostId(materialCost.getId());
                    dto.setDesignCost(materialCost.getItemCost());
                    dto.setMaterialCostType(materialCost.getMaterialCostType());
                    dto.setItemCost(materialCost.getItemCost());
                }
            }
            if (StringUtils.isNotEmpty(dto.getUnit())) {
                DictDto unit = CacheDataUtils.findDictByTypeAndCode(DictType.MEASUREMENT_UNIT.code(), dto.getUnit());
                if (unit != null) {
                    dto.setUnitName(unit.getName());
                }
            }
        }
        return page;
    }

    @Override
    public MaterialDto getByErpCode(String erpCode, Long organizationId) {
        MaterialDto query = new MaterialDto();
        query.setItemCode(erpCode);
        if (organizationId != null) {
            query.setOrganizationId(organizationId);
        }
        List<MaterialDto> dtos = this.selectList(query);
        if (dtos.isEmpty() && organizationId != null) {
            return null;
        }
        Asserts.notEmpty(dtos, ErrorCode.BASEDATA_ITEM_CODE_MATERIAL_NOT_FIND);
        if (ListUtils.isNotEmpty(dtos)) {
            return dtos.get(0);
        }
        return null;
    }

    @Override
    public List<MaterialDto> getByErpCodes(String erpCode, Long organizationId) {
        MaterialDto query = new MaterialDto();
        query.setItemCode(erpCode);
        if (organizationId != null) {
            query.setOrganizationId(organizationId);
        }
        List<MaterialDto> dtos = this.selectList(query);
        if (dtos.isEmpty() && organizationId != null) {
            return null;
        }
        Asserts.notEmpty(dtos, ErrorCode.BASEDATA_ITEM_CODE_MATERIAL_NOT_FIND);
        return dtos;
    }

    @Override
    public List<MaterialDto> getByErpCodeListAndOrgIdList(MaterialQueryDTO materialQueryDTO) {
        MaterialExample example = new MaterialExample();
        MaterialExample.Criteria criteria = example.createCriteria();
        criteria.andDeleteFlagEqualTo(false);
        if (CollectionUtils.isNotEmpty(materialQueryDTO.getErpCodeList())) {
            criteria.andItemCodeIn(materialQueryDTO.getErpCodeList());
        }
        if (CollectionUtils.isNotEmpty(materialQueryDTO.getOrganizationIdList())) {
            criteria.andOrganizationIdIn(materialQueryDTO.getOrganizationIdList());
        }

        List<Material> materialList = materialMapper.selectByExample(example);
        Guard.notNullOrEmpty(materialList, "erp编码对应的物料不存在");
        return BeanConverter.copy(materialList, MaterialDto.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setPamCode2MaterialAndCost(String erpCode, String pamCode) {
        MaterialDto materialQuery = new MaterialDto();
        materialQuery.setItemCode(erpCode);
        List<MaterialDto> materialDtos = this.selectList(materialQuery);
        for (MaterialDto materialDto : materialDtos) {
            if (StringUtils.isNotEmpty(materialDto.getPamCode())) {
                continue;
            }
            materialDto.setPamCode(pamCode);
            this.save(materialDto, SystemContext.getUserId());
        }

        MaterialCostDto materialCostQuery = new MaterialCostDto();
        materialCostQuery.setItemCode(erpCode);
        List<MaterialCostDto> materialCostDtos = materialCostService.selectList(materialCostQuery);
        for (MaterialCostDto materialCostDto : materialCostDtos) {
            if (StringUtils.isNotEmpty(materialCostDto.getPamCode())) {
                continue;
            }
            materialCostDto.setPamCode(pamCode);
            materialCostService.save(materialCostDto, null);
        }

        return true;
    }

    @Override
    public Boolean saveBatchErpCode(List<MaterialDto> dtos) {
        logger.info("saveBatchErpCode start");

        String lockName = "Material.saveBatchErpCode";
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME_SYNC, MAX_WAIT_TIME_SYNC * 2)) {
                List<Long> materialDtoIdList = new ArrayList<>();
                for (MaterialDto materialDto : dtos) {
                    materialDtoIdList.add(materialDto.getId());
                }
                MaterialExample mExample = new MaterialExample();
                mExample.createCriteria().andIdIn(materialDtoIdList);
                List<Material> mList = materialMapper.selectByExample(mExample);
                Map<Long, Material> mMap = new HashMap<>();
                Map<String, List<Material>> materialMap = new HashMap<>();
                List<String> pamCodeList = new ArrayList<>();
                for (Material material : mList) {
                    mMap.put(material.getId(), material);
                    if (StringUtils.isNotEmpty(material.getPamCode())) {
                        pamCodeList.add(material.getPamCode());
                    }

                    String key = material.getPamCode() + "_" + material.getOrganizationId();
                    if (Boolean.FALSE.equals(material.getDeleteFlag())) {
                        if (materialMap.containsKey(key)) {
                            materialMap.get(key).add(material);
                        } else {
                            List<Material> materials = new ArrayList<>();
                            materials.add(material);
                            materialMap.put(key, materials);
                        }
                    }
                }

                MaterialDto query = new MaterialDto();
                query.setPamCodeList(pamCodeList);
                List<MaterialDto> materialDtoList = CollectionUtils.isEmpty(pamCodeList) ? new ArrayList<>()
                        : materialService.selectList(query);
                Map<String, List<MaterialDto>> materialDtoMap = materialDtoList.stream().collect(Collectors.groupingBy(MaterialDto::getPamCode));
                List<MaterialCostDto> mCostDtoList = CollectionUtils.isEmpty(pamCodeList) ? new ArrayList<>()
                        : materialCostService.getByPamCodeList(pamCodeList, null);
                Map<String, List<MaterialCostDto>> mCostDtoMap = mCostDtoList.stream().collect(Collectors.groupingBy(MaterialCostDto::getPamCode));

                TwoTuple<List<Long>, List<Material>> twoTuple = this.getSaveBatchMaterial(dtos, mMap, materialDtoMap);
                List<Long> materialIdList = twoTuple.getFirst();
                List<Material> materialList = twoTuple.getSecond();
                List<MaterialCost> materialCostList = new ArrayList<>();
                Map<Long, String> sourcerMap = new HashMap<>();

                for (MaterialDto dto : dtos) {
                    //Material material = materialMapper.selectByPrimaryKey(dto.getId());
                    Material material = mMap.get(dto.getId());
                    if (material == null) {
                        continue;
                    }
                    if (StringUtils.isEmpty(material.getPamCode())) {
                        continue;
                    }
                    //List<MaterialCostDto> dtoList = materialCostService.getByPamCode(material.getPamCode(), null);
                    List<MaterialCostDto> dtoList = mCostDtoMap.get(material.getPamCode());
                    if (ListUtils.isNotEmpty(dtoList)) {
                        for (MaterialCostDto materialCostDto : dtoList) {
                            materialCostDto.setItemCode(material.getItemCode());

                            materialCostService.supplyDto(materialCostDto);

                            //更新物料基础表的配套人员信息
                            /*MaterialExample materialExample = new MaterialExample();
                            materialExample.createCriteria().andDeleteFlagEqualTo(false).andPamCodeEqualTo(materialCostDto.getPamCode())
                                    .andOrganizationIdEqualTo(materialCostDto.getOrganizationId());
                            List<Material> materials = materialMapper.selectByExample(materialExample);*/
                            List<Material> materials = materialMap.get(materialCostDto.getPamCode() + "_" + materialCostDto.getOrganizationId());
                            if (ListUtils.isNotEmpty(materials)) {
                                UserInfo userById = CacheDataUtils.findUserById(materialCostDto.getNuclearPriceUserBy());
                                if (null != userById) {
                                    if (materialIdList.contains(materials.get(0).getId())) {
                                        sourcerMap.put(materials.get(0).getId(), userById.getUsername());
                                    } else {
                                        materials.get(0).setSourcer(userById.getUsername());
                                        materialList.add(materials.get(0));
                                    }
                                }
                            }

                            materialCostList.add(materialCostDto);
                        }
                    }
                }

                for (Material material : materialList) {
                    if (sourcerMap.containsKey(material.getId())) {
                        material.setSourcer(sourcerMap.get(material.getId()));
                    }
                }

                logger.info("saveBatchErpCode materialList：{}，materialCostList：{}",
                        JSON.toJSONString(materialList), JSON.toJSONString(materialCostList));
                transactionTemplate.execute(status -> {
                    try {
                        if (ListUtils.isNotEmpty(materialList)) {
                            this.batchUpdate(materialList);
                        }
                        if (ListUtils.isNotEmpty(materialCostList)) {
                            materialCostService.batchUpdate(materialCostList);
                        }

                        return true;
                    } catch (Exception e) {
                        logger.error("saveBatchErpCode更新物料和物料估价失败", e);
                        status.setRollbackOnly();
                        return false;
                    }
                });
            }
        } catch (Exception e) {
            logger.error("saveBatchErpCode加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }

        logger.info("saveBatchErpCode end");
        return true;
    }

    private TwoTuple<List<Long>, List<Material>> getSaveBatchMaterial(List<MaterialDto> dtos,
                                                                      Map<Long, Material> mMap,
                                                                      Map<String, List<MaterialDto>> materialDtoMap) {
        List<Long> materialIdList = new ArrayList<>();
        List<Material> resultList = new ArrayList<>();

        Map<String, String> materialTypeMap =
                ltcDictService.getLtcDict(MATERIAL_TYPE, null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));

        for (MaterialDto dto : dtos) {
            Material m = mMap.get(dto.getId());
            if (m == null) {
                continue;
            }

            List<MaterialDto> materialDtoList = materialDtoMap.get(m.getPamCode());
            if (ListUtils.isEmpty(materialDtoList)) {
                continue;
            }

            for (MaterialDto material : materialDtoList) {
                material.setItemCode(dto.getItemCode());
                //设置物料类型编码
                if (materialTypeMap.containsKey(material.getMaterialCategory())) {
                    logger.info("getSaveBatchMaterial字典之物料类型：" + materialTypeMap.get(material.getMaterialCategory())
                            + " 物料类型：" + material.getMaterialCategory());
                    material.setItemType(materialTypeMap.get(material.getMaterialCategory()));
                }

                materialIdList.add(material.getId());
                resultList.add(material);
            }
        }

        return new TwoTuple<>(materialIdList, resultList);
    }

    @Override
    public long count(MaterialQuery query) {
        return materialExtMapper.count(query);
    }

    private MaterialExample buildCondition(MaterialDto query) {
        MaterialExample example = new MaterialExample();
        MaterialExample.Criteria criteria = example.createCriteria();
        criteria.andDeleteFlagEqualTo(false);

        if (StringUtils.isNotEmpty(query.getPamCode())) {
            criteria.andPamCodeEqualTo(query.getPamCode());
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            criteria.andItemCodeEqualTo(query.getItemCode());
        }
        if (query.getOrganizationId() != null) {
            criteria.andOrganizationIdEqualTo(query.getOrganizationId());
        }
        if (CollectionUtils.isNotEmpty(query.getPamCodeList())) {
            criteria.andPamCodeIn(query.getPamCodeList());
        }

        return example;
    }

    /**
     * 物料同步异常的数据重推
     */
    @Override
    public void MaterialResend() {
        //查询同步异常的resend数据
        List<ResendExecute> resendExecuteList = ctcExtService.getResendExecuteList(2, Lists.newArrayList(BusinessTypeEnums.MATERIAL_CREATE.getCode())
                , null);
        Map<Long, ResendExecute> resendExecuteMap = new HashMap<>();
        //需要重发的记录
        List<ResendExecuteDto> resendData = new ArrayList<>();
        List<Long> materialIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(resendExecuteList)) {
            materialIds.add(-1L);
        } else {
            for (ResendExecute resendExecute : resendExecuteList) {
                materialIds.add(Long.valueOf(resendExecute.getApplyNo()));
                resendExecuteMap.put(Long.valueOf(resendExecute.getApplyNo()), resendExecute);
            }
        }
        List<Material> firstMaterialList = materialExtMapper.getMaterialSync(materialIds);
        logger.info("MaterialResend的firstMaterialList：{}", JSON.toJSONString(firstMaterialList));

        //resend表的status=1，但是material表的erp_code=2 &item_id=null的物料也需要重推
        MaterialExample example = new MaterialExample();
        MaterialExample.Criteria criteria = example.createCriteria();
        criteria.andErpCodeEqualTo("2").andItemIdIsNull().andDeleteFlagEqualTo(false);
        List<Material> list = materialMapper.selectByExample(example);
        List<Material> secondMaterialList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            Map<Long, Material> materialMap = new HashMap<>();
            List<String> applyNoList = new ArrayList<>();
            for (Material material : list) {
                materialMap.put(material.getId(), material);
                applyNoList.add(String.valueOf(material.getId()));
            }
            logger.info("MaterialResend的applyNoList：{}", JSON.toJSONString(applyNoList));
            List<ResendExecute> successResendExecuteList = ctcExtService.getSuccessResendExecuteList(1,
                    Lists.newArrayList(BusinessTypeEnums.MATERIAL_CREATE.getCode(),
                            BusinessTypeEnums.MATERIAL_UPDATE.getCode()), applyNoList);
            for (ResendExecute resendExecute : successResendExecuteList) {
                resendExecuteMap.put(Long.valueOf(resendExecute.getApplyNo()), resendExecute);
                secondMaterialList.add(materialMap.get(Long.valueOf(resendExecute.getApplyNo())));
            }
        }
        logger.info("MaterialResend的secondMaterialList：{}", JSON.toJSONString(secondMaterialList));

        // 合并去重两种方式可以重推的
        List<Material> materialList =
                Stream.of(firstMaterialList, secondMaterialList).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        logger.info("MaterialResend的materialList：{}", JSON.toJSONString(materialList));
        if (CollectionUtils.isEmpty(materialList)) {
            return;
        }

        for (Material material : materialList) {
            //物料编码描述字符长度大于220(erp控制240)的不重推
            if (StringUtil.isNotNull(material.getItemInfo())
                    && material.getItemInfo().getBytes(Charset.forName("UTF-8")).length > 220) {
                continue;
            }
            ResendExecute resendExecute = resendExecuteMap.get(material.getId());
            if (resendExecute != null) {
                ResendExecuteDto executeDto = BeanConverter.copy(resendExecute, ResendExecuteDto.class);
                executeDto.setStatus(CommonStatus.TODO.getCode());
                executeDto.setResponMsg(null);
                executeDto.setResponCode(null);
                executeDto.setUpdateAt(new Date());
                executeDto.setUpdateBy(-1L);
                resendData.add(executeDto);
            } else {
                List<ResendExecute> resendExecutes = ctcExtService.getResendExecuteList(null,
                        Lists.newArrayList(BusinessTypeEnums.MATERIAL_CREATE.getCode(),
                                BusinessTypeEnums.MATERIAL_UPDATE.getCode()), material.getId() + "");
                if (ListUtils.isNotEmpty(resendExecutes)) {
                    ResendExecuteDto executeDto = BeanConverter.copy(resendExecutes.get(0), ResendExecuteDto.class);
                    executeDto.setStatus(CommonStatus.TODO.getCode());
                    executeDto.setResponMsg(null);
                    executeDto.setResponCode(null);
                    executeDto.setUpdateAt(new Date());
                    executeDto.setUpdateBy(-1L);
                    resendData.add(executeDto);
                }
            }
        }
        ctcExtService.batchUpdate(resendData);
    }

    @Override
    public MaterialDto getById(Long id) {
        if (id == null) {
            return null;
        }
        Material material = materialMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(material, ErrorCode.CTC_PAM_CODE_MATERIAL_NOT_NULL);
        MaterialDto dto = BeanConverter.copy(material, MaterialDto.class);
        return dto;
    }

    @Override
    public List<MaterialDto> getByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }

        MaterialExample example = new MaterialExample();
        example.createCriteria().andIdIn(idList);
        List<Material> materials = materialMapper.selectByExample(example);
        return BeanConverter.copy(materials, MaterialDto.class);
    }

    @Override
    public Map<Long, Long> getOrgByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new HashMap<>();
        }
        MaterialExample example = new MaterialExample();
        example.createCriteria().andIdIn(idList);
        List<Material> materialList = materialMapper.selectByExample(example);
        return materialList.stream().collect(Collectors.toMap(Material::getId, Material::getOrganizationId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EsbResponse materialPushErpItemImport(List<Long> ids) {
        EsbResponse esbResponse = new EsbResponse();
        //String serialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime() + "";
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.getEnumByInterfaceCode(BusinessTypeEnums.MATERIAL_UPDATE.getCode());
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        String businessId = sdpCarrierService.generateSequence(topicCodeEnum);
        esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
        esbResponse.setResponsemessage(ResponseCodeEnums.SUCESS.getMsg());
        esbResponse.setData(businessId);
        try {
            RocketMQMsgParameter rocketMQMsgParameter = new RocketMQMsgParameter();
            rocketMQMsgParameter.setOperationId(businessId);
            rocketMQMsgParameter.setTopic(basedataInvItemImportTopic);
            rocketMQMsgParameter.setParameter(JSONObject.toJSONString(ids).getBytes(StandardCharsets.UTF_8));
            rocketMQMsgParameter.setDeletedFlag(Boolean.FALSE);
            rocketMQMsgParameterExtMapper.insertSelective(rocketMQMsgParameter);

            MsgSendDto msgSendDto = new MsgSendDto();
            msgSendDto.setOperationId(businessId);
            msgSendDto.setTopic(basedataInvItemImportTopic);
            msgSendDto.setMessages(rocketMQMsgParameter.getId());
            commonRocketMQProducerService.sendCommonMessages(msgSendDto);
        } catch (Exception e) {
            logger.error("MQ主题[basedata.inv-item-import.topic]推送异常", e);
            esbResponse.setResponsecode(ResponseCodeEnums.FAULT.getCode());
            esbResponse.setResponsemessage("MQ主题[basedata.inv-item-import.topic]推送异常");
        }
        return esbResponse;
    }

    @Override
    public void materialInvItemImport(String esbSerialNo, List<Long> materialIds) {
        MaterialExample example = new MaterialExample();
        MaterialExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(materialIds);
        List<Material> materials = this.materialMapper.selectByExample(example);
        List<MaterialDto> materialdtos = BeanConverter.copy(materials, MaterialDto.class);

        for (MaterialDto materialDto : materialdtos) {
            if (materialDto.getItemType() != null && materialDto.getItemType().equals("P")) {
                List<MaterialCostDto> materialCostDtoList = materialCostService.getByPamCode(materialDto.getPamCode()
                        , materialDto.getOrganizationId());
                if (ListUtils.isNotEmpty(materialCostDtoList)) {
                    MaterialCostDto materialCostDto = materialCostDtoList.get(0);
                    materialDto.setDesignCost(materialCostDto.getItemCost());
                }
            }
        }
       // EsbResponse esbResponse = esbService.materialPushErpItemImport(esbSerialNo, materialdtos);
        EsbResponse esbResponse = this.materialPushErpItemImportFromSdp(esbSerialNo,materialdtos);
        // 同步回调，只处理失败回调，成功回调异步处理
        if (!Objects.equals(esbResponse.getResponsecode(), ResponseCodeEnums.SUCESS.getCode())) {
            // 批量更新物料状态为同步失败
            materialExtMapper.batchUpdateStatus(materialIds, esbResponse.getResponsemessage());
            // 回写resend记录
            ResendExecuteDto resendExecuteDto = new ResendExecuteDto();
            resendExecuteDto.setEsbSerialNo(String.valueOf(esbResponse.getData()));
            resendExecuteDto.setBusinessType(BusinessTypeEnums.MATERIAL_UPDATE.getCode());
            // 把materialIds从List<Long> 转List<String>
            List<String> materialIdStr = materialIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            resendExecuteDto.setApplyNoList(materialIdStr);
            resendExecuteDto.setResponCode(ResponseCodeEnums.FAULT.getCode());
            resendExecuteDto.setStatus(CommonStatus.ERROR.getCode());
            resendExecuteDto.setResponMsg(esbResponse.getResponsemessage());
            ctcExtService.batchUpdateByBusinessType(resendExecuteDto);
        }
    }

    @Override
    public EsbResponse materialValuationErpItemImport(List<Long> ids) {
        MaterialExample example = new MaterialExample();
        MaterialExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        List<Material> materials = this.materialMapper.selectByExample(example);
        List<MaterialDto> materialdtos = BeanConverter.copy(materials, MaterialDto.class);

        for (MaterialDto materialDto : materialdtos) {
            if (materialDto.getItemType() != null && materialDto.getItemType().equals("P")) {
                List<MaterialCostDto> materialCostDtoList = materialCostService.getByPamCode(materialDto.getPamCode()
                        , materialDto.getOrganizationId());
                if (ListUtils.isNotEmpty(materialCostDtoList)) {
                    MaterialCostDto materialCostDto = materialCostDtoList.get(0);
                    materialDto.setDesignCost(materialCostDto.getItemCost());
                }
            }
        }
        return materialValuationErpItemImportFromSdp(materialdtos);
    }

    @Override
    public Boolean syncMaterialAttribute(List<MaterialAdjustDetailDTO> materialAdjustDetailDTOS) {

        if (ListUtils.isNotEmpty(materialAdjustDetailDTOS)) {
            final Long organizationId = materialAdjustDetailDTOS.get(0).getOrganizationId();
            if (organizationId != null) {
                materialExtMapper.updateMaterialAttr(materialAdjustDetailDTOS.get(0).getHeaderId(), organizationId);
            }
            final List<String> pamCodeList =
                    materialAdjustDetailDTOS.stream().map(MaterialAdjustDetailDTO::getPamCode).distinct().collect(Collectors.toList());
            MaterialQueryDTO materialQueryDTO = new MaterialQueryDTO();
            materialQueryDTO.setPamCodeList(pamCodeList);
            materialQueryDTO.setOrganizationId(organizationId);
            final List<Material> materialList = materialService.listByPamCode(materialQueryDTO);
            if (ListUtils.isNotEmpty(materialList)) {
                for (Material material : materialList) {
                    ResendExecute resendExecute = new ResendExecute();
                    resendExecute.setBusinessType(BusinessTypeEnums.MATERIAL_UPDATE.getCode());
                    resendExecute.setApplyNo(Long.toString(material.getId()));
                    resendExecute.setOuId(organizationId);
                    resendExecute.setDeletedFlag(Boolean.FALSE);
                    resendExecute.setCreateAt(new Date());
                    resendExecute.setBatch(true);
                    resendExecute.setStatus(CommonStatus.TODO.getCode());
                    ctcExtService.saveResendExecute(resendExecute);
                }
            }

        }


        return Boolean.TRUE;
    }

    @Override
    public Boolean judgeUnique(Long organizationId, String materialMiddleClass, String figureNumber,
                               String brandMaterialCode, String brand, String name, String model) {
        int count = 0;
        if ("机械加工件".equals(materialMiddleClass)) {
            count = materialExtMapper.judgeStandardItem(organizationId, materialMiddleClass, figureNumber);
        } else if ("机械标准件".equals(materialMiddleClass)) {
            if (StringUtils.isNotEmpty(brandMaterialCode)) {
                count = materialExtMapper.judgeProcessItem(organizationId, materialMiddleClass, brandMaterialCode,
                        brand, name, model);
            }
        }
        return count == 0 ? true : false;
    }

    @Override
    public Material judgeUniqueCheckType2(String brand, String model, String brandMaterialCode, Long organizationId) {
        return materialExtMapper.judgeUniqueCheckType2(brand, model, brandMaterialCode, organizationId);
    }

    @Override
    public Material judgeUniqueCheckType3(String figureNumber, String chartVersion, Long organizationId) {
        return materialExtMapper.judgeUniqueCheckType3(figureNumber, chartVersion, organizationId);
    }

    @Override
    public Material judgeAssemblyPartUnique(String name, String figureNumber, String chartVersion,
                                            Long organizationId) {
        return materialExtMapper.judgeAssemblyPartUnique(name, figureNumber, chartVersion, organizationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncMaterialMaterialAdjustDetail(MaterialAdjustHeaderDTO materialAdjustHeaderDTO) {
        List<Long> materialIdList = new ArrayList<>();
        List<MaterialAdjustDetailDTO> detailDTOList = materialAdjustHeaderDTO.getDetailDTOList();
        List<String> pamCodeList = detailDTOList.stream().map(MaterialAdjustDetailDTO::getPamCode).distinct().collect(Collectors.toList());

        MaterialQueryDTO materialQueryDTO = new MaterialQueryDTO();
        materialQueryDTO.setPamCodeList(pamCodeList);
        materialQueryDTO.setOrganizationId(materialAdjustHeaderDTO.getOrganizationId());
        Map<String, Material> materialMap = materialService.listByPamCode(materialQueryDTO)
                .stream().collect(Collectors.toMap(Material::getPamCode, Function.identity(), (key1, key2) -> key2));
        Map<String, MaterialCost> materialCostMap = materialCostService.listByPamCodeAndOrganizationId(pamCodeList,
                        materialAdjustHeaderDTO.getOrganizationId()).stream()
                .collect(Collectors.toMap(MaterialCost::getPamCode, Function.identity(), (key1, key2) -> key2));

        for (MaterialAdjustDetailDTO materialAdjustDetailDTO : detailDTOList) {
            Material originalMaterial = materialMap.get(materialAdjustDetailDTO.getPamCode());
            String itemCode = (Objects.nonNull(originalMaterial) && StringUtils.isNotEmpty(originalMaterial.getItemCode()))
                    ? originalMaterial.getItemCode() : materialAdjustDetailDTO.getErpCode();

            //审批通过后-物料类型="装配件"的物料不生成E码、不生成物料估价pam_basedata.material_cost、不同步ERP（不插pam_ctc.resend_execute表）
            boolean assemblyIs = Objects.equals("装配件", materialAdjustDetailDTO.getMaterialType());
            /*审批通过后更新物料基础表的逻辑*/
            Material material = new Material();
            material.setOrganizationId(materialAdjustHeaderDTO.getOrganizationId());
            material.setItemCode(assemblyIs ? null : itemCode);
            material.setItemInfo(materialAdjustDetailDTO.getItemDes());
            material.setUnit(materialAdjustDetailDTO.getUnitCode());
            material.setItemType(materialAdjustDetailDTO.getMaterialTypeCode());// 外购物料用户物料类型为P，对应数据字典material_type
            material.setItemStatus("Active");// 传给ERP默认为Active
            material.setBuyerNumber(materialAdjustHeaderDTO.getBuyerNumber());
            material.setBuyerRound(materialAdjustDetailDTO.getPerchasingLeadtime() == null ? null :
                    materialAdjustDetailDTO.getPerchasingLeadtime().toString());
            material.setFixedLotMultiplier(materialAdjustDetailDTO.getMinPackageQuantity() == null ? null :
                    materialAdjustDetailDTO.getMinPackageQuantity().toString());
            material.setPamCode(materialAdjustDetailDTO.getPamCode());
            material.setMaterialClassification(materialAdjustDetailDTO.getMaterialClass());
            material.setMaterialType(materialAdjustDetailDTO.getMaterialSmallClass());
            material.setCodingMiddleclass(materialAdjustDetailDTO.getMaterialMiddleClass());
            material.setMachiningPartType(materialAdjustDetailDTO.getMachiningPartType());
            material.setMaterial(materialAdjustDetailDTO.getMaterial());
            material.setUnitWeight(materialAdjustDetailDTO.getUnitWeight());
            material.setMaterialProcessing(materialAdjustDetailDTO.getSurfaceHandle());
            material.setName(materialAdjustDetailDTO.getName());
            material.setModel(materialAdjustDetailDTO.getModel());
            material.setBrand(materialAdjustDetailDTO.getBrand());
            material.setMinimumOrderQuantity(materialAdjustDetailDTO.getMinPerchaseQuantity());
            material.setSourcer(materialAdjustDetailDTO.getValuerMip());
            material.setErpCode(CommonStatus.TODO.getCode() + "");//待同步
            material.setMaterialCategory(materialAdjustDetailDTO.getMaterialType());
            material.setFigureNumber(materialAdjustDetailDTO.getFigureNumber());
            material.setBrandMaterialCode(materialAdjustDetailDTO.getBrandMaterialCode());
            material.setOrSparePartsMask(materialAdjustDetailDTO.getOrSparePartsMask());
            material.setInventoryType(materialAdjustDetailDTO.getInventoryType());
            material.setMaterialAttribute(materialAdjustDetailDTO.getMaterialAttribute());
            material.setRemark(materialAdjustDetailDTO.getRemark());
            material.setCreateBy(materialAdjustHeaderDTO.getApplyBy()); //记录物料新增单据的申请人为create_by

            if (originalMaterial == null) {
                material.setDelistFlag(Boolean.FALSE);//是否待退市物料
                material.setDeleteFlag(Boolean.FALSE);
                material.setChartVersion(materialAdjustDetailDTO.getChartVersion());
                if (NumberUtil.isNumber(material.getChartVersion())) {
                    material.setMaximumDrawVersionNum(BigDecimalUtils.stringToLong(material.getChartVersion()));
                }
                this.add(material);
            } else {
                if (NumberUtil.isNumber(material.getChartVersion())) {
                    if (Objects.isNull(originalMaterial.getMaximumDrawVersionNum())) {
                        material.setMaximumDrawVersionNum(BigDecimalUtils.stringToLong(material.getChartVersion()));
                    } else if (NumberUtil.compare(Long.parseLong(material.getChartVersion()),
                            originalMaterial.getMaximumDrawVersionNum()) > 0) {
                        material.setMaximumDrawVersionNum(BigDecimalUtils.stringToLong(material.getChartVersion()));
                    }
                }
                material.setId(originalMaterial.getId());
                materialMapper.updateByPrimaryKeySelective(material);
            }

            // 保存物料基础表数据成功后将id存到list，在外层去同步erp
            if (material.getId() != null && !assemblyIs) {
                materialIdList.add(material.getId());
            }

            if (!assemblyIs) {
                /*审批通过后更新物料报价的逻辑*/
                MaterialCost materialCost = new MaterialCost();
                materialCost.setOrganizationId(materialAdjustHeaderDTO.getOrganizationId());
                materialCost.setItemCode(itemCode);
                materialCost.setPamCode(materialAdjustDetailDTO.getPamCode());
                materialCost.setDescr(materialAdjustDetailDTO.getItemDes());
                materialCost.setMaterialCostType(MaterialCostType.APPRAISAL.code());
                materialCost.setMaterialClassification(materialAdjustDetailDTO.getMaterialClass());
                materialCost.setMaterielType(materialAdjustDetailDTO.getMaterialSmallClass());
                materialCost.setCodingMiddleclass(materialAdjustDetailDTO.getMaterialMiddleClass());
                materialCost.setMachiningPartType(materialAdjustDetailDTO.getMachiningPartType());
                materialCost.setMaterial(materialAdjustDetailDTO.getMaterial());
                materialCost.setFixedLotMultiplier(materialAdjustDetailDTO.getMinPackageQuantity() == null ? null :
                        materialAdjustDetailDTO.getMinPackageQuantity().toString());
                materialCost.setMinOrderQuantity(materialAdjustDetailDTO.getMinPerchaseQuantity() == null ? null :
                        materialAdjustDetailDTO.getMinPerchaseQuantity().toString());
                materialCost.setAdvancePurchaseData(materialAdjustDetailDTO.getPerchasingLeadtime() == null ? null :
                        materialAdjustDetailDTO.getPerchasingLeadtime().toString());
                materialCost.setUnitWeight(materialAdjustDetailDTO.getUnitWeight());
                materialCost.setMaterialProcessing(materialAdjustDetailDTO.getSurfaceHandle());
                materialCost.setUnit(materialAdjustDetailDTO.getUnitCode());
                materialCost.setDeletedFlag(false);
                materialCost.setNuclearPriceUserBy(materialAdjustDetailDTO.getValuerId());
                materialCost.setMaterialCategory(materialAdjustDetailDTO.getMaterialType());
                materialCost.setFigureNumber(materialAdjustDetailDTO.getFigureNumber());
                materialCost.setChartVersion(materialAdjustDetailDTO.getChartVersion());
                materialCost.setBrandMaterialCode(materialAdjustDetailDTO.getBrandMaterialCode());
                materialCost.setOrSparePartsMask(materialAdjustDetailDTO.getOrSparePartsMask());
                materialCost.setBrand(materialAdjustDetailDTO.getBrand());
                materialCost.setName(materialAdjustDetailDTO.getName());
                materialCost.setModel(materialAdjustDetailDTO.getModel());
                materialCost.setRemark(materialAdjustDetailDTO.getRemark());
                MaterialCostDto materialCostDto = BeanConverter.copy(materialCost, MaterialCostDto.class);

                if (materialCostMap.get(materialAdjustDetailDTO.getPamCode()) != null) {
                    materialCostDto.setId(materialCostMap.get(materialAdjustDetailDTO.getPamCode()).getId());
                }
                materialCostService.save(materialCostDto, materialAdjustHeaderDTO.getApplyBy());
            }
        }

        // 同步物料基础表信息到ERP
        List<ResendExecute> resendExecuteList = new ArrayList<>();
        for (Long materialId : materialIdList) {
            ResendExecute resendExecute = new ResendExecute();
            resendExecute.setBusinessType(BusinessTypeEnums.MATERIAL_UPDATE.getCode());
            resendExecute.setOuId(materialAdjustHeaderDTO.getOrganizationId());
            resendExecute.setApplyNo(materialId.toString());
            resendExecute.setDeletedFlag(Boolean.FALSE);
            resendExecute.setCreateAt(new Date());
            resendExecute.setStatus(CommonStatus.TODO.getCode());
            resendExecute.setBatch(true);
            resendExecuteList.add(resendExecute);
        }
        if (CollectionUtils.isNotEmpty(resendExecuteList)) {
            ctcExtService.batchInsert(resendExecuteList);
        }

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncMaterialType(Long MaterialId, String MaterialTypeNew) {
        String itemType = "";
        if ("装配件".equals(MaterialTypeNew)) {
            itemType = "SA";
        } else if ("虚拟件".equals(MaterialTypeNew)) {
            itemType = "PH";
        } else if ("外购物料".equals(MaterialTypeNew)) {
            itemType = "P";
        } else if ("看板物料".equals(MaterialTypeNew)) {
            itemType = "SA-P";
        }
        materialExtMapper.updateMaterialTypeById(MaterialId, MaterialTypeNew, itemType);
        return Boolean.TRUE;
    }

    @Override
    public List<Material> selectByPamCodeAndOrganizationId(String pamCode, Long organizationId) {
        MaterialExample example = new MaterialExample();
        MaterialExample.Criteria criteria = example.createCriteria();
        criteria.andPamCodeEqualTo(pamCode).andOrganizationIdEqualTo(organizationId).andDeleteFlagEqualTo(false).andDelistFlagEqualTo(false);
        return materialMapper.selectByExample(example);
    }

    @Override
    public PageInfo<MaterialDto> materialPageByOrg(MaterialQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        //根据当前使用单位下的库存组织查询物料信息
        //query.setUnitId(SystemContext.getUnitId());
        List<MaterialDto> materialDtos = materialExtMapper.materialPageByOrg(query);
        return new PageInfo<>(materialDtos);
    }

    @Override
    public PageInfo<MaterialDto> materialPageByCodeAndOu(MaterialQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<MaterialDto> materialDtos = materialExtMapper.materialPageByCodeAndOu(query);
        return new PageInfo<>(materialDtos);
    }

    @Override
    public List<Material> getMaterialListByOrgPamCodes(Long organizationId, List<String> pamCodes) {
        return materialExtMapper.getMaterialListByOrgPamCodes(organizationId, pamCodes);
    }

    @Override
    public void saveAndResend(List<MaterialDto> dtos) {
        Long userId = SystemContext.getUserId();
        Map<String, String> unitMap =
                ltcDictService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));
        //设置组信息
        Long group = 0L;
        int begin = 0;
        int groupNumber = 1000;
        Long organizationId = 1L;
        String materialCodeRule = MaterialCodeRuleEnum.KUNSHAN.getCode();

        for (MaterialDto m : dtos) {
            if (organizationId != m.getOrganizationId()) {
                logger.info("组织：" + organizationId + "名称" + materialCodeRule);
                Long aLong = unitService.selectUnitIdByOrganizationId(m.getOrganizationId());
                String materialCodeRuleStr = getMaterialCodeRule(aLong);
                if (StringUtils.isNotEmpty(materialCodeRuleStr)) {
                    materialCodeRule = materialCodeRuleStr;
                }
                organizationId = aLong;
                logger.info("名称" + materialCodeRule + "组织：" + organizationId);
            }

            //如果没有物料编码
            if (StringUtils.isEmpty(m.getItemCode())) {
                //生成erpcode
                final Map<String, Object> param = new HashMap<>();
                param.put("materialSmallClass", m.getMaterialType());
                param.put("materialMiddleClass", m.getCodingMiddleclass());
                param.put("materialClass", m.getMaterialClassification());
                param.put("materialCodeRule", materialCodeRule);
                final String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/erpCodeRule/generateMaterialERPCode", param);
                String res = restTemplate.getForEntity(url, String.class).getBody();
                DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
                });
                m.setItemCode(response.getData());
                m.setPamCode(generateMaterialPAMCode());
                m.setCreateAt(new Date());
            } else {
                MaterialExample example = new MaterialExample();
                example.createCriteria().andOrganizationIdEqualTo(m.getOrganizationId()).andItemCodeEqualTo(m.getItemCode());
                List<Material> list = materialMapper.selectByExample(example);
                //存在则更新
                if (ListUtils.isNotEmpty(list)) {
                    m.setId(list.get(0).getId());
                } else {
                    m.setPamCode(generateMaterialPAMCode());
                    m.setCreateAt(new Date());
                }
            }
            if (MaterialCategoryEnum.PURCHASED_PARTS.msg().equals(m.getMaterialCategory())) {
                m.setItemType("P");
            }
            if (MaterialCategoryEnum.ASSEMBLY_PARTS.msg().equals(m.getMaterialCategory())) {
                m.setItemType("SA");
            }
            if (MaterialCategoryEnum.KANBAN_MATERIALS.msg().equals(m.getMaterialCategory())) {
                m.setItemType("SA-P");
            }
            if (MaterialCategoryEnum.VIRTUAL_PARTS.msg().equals(m.getMaterialCategory())) {
                m.setItemType("PH");
            }

            if (StringUtils.isNotEmpty(unitMap.get(m.getUnit()))) {
                m.setUnit(unitMap.get(m.getUnit()));
            }
            //物料的状态为  退市状态='是'
            m.setDelistFlag(Boolean.TRUE);

            m.setUpdateAt(new Date());
            m = save(m, userId);
            String materialId = m.getId().toString();
            //记录到推送中
            ResendExecute resendExecute = new ResendExecute();
            resendExecute.setBusinessType(BusinessTypeEnums.MATERIAL_UPDATE.getCode());
            resendExecute.setApplyNo(materialId);
            resendExecute.setDeletedFlag(Boolean.FALSE);
            resendExecute.setCreateAt(new Date());
            resendExecute.setStatus(CommonStatus.TODO.getCode());
            resendExecute.setBatch(true);
            resendExecute.setOuId(group);
            ctcExtService.saveResendExecute(resendExecute);

            if (begin < groupNumber) {
                begin++;
            } else {
                begin = 0;
                group += 1L;
            }
        }
    }

    private String buildGetUrl(final String baseUrl, final String action, Map<String, Object> param) {
        final StringBuffer url = new StringBuffer(baseUrl).append(action).append("?1=1");
        if (null != param) {
            param.forEach((k, v) -> {
                if (!org.springframework.util.StringUtils.isEmpty(v)) {
                    url.append("&").append(k).append("=").append(v);
                }
            });
        }
        return url.toString();
    }

    @Override
    public List<Material> selectByKsOrgId(MaterialDto dto) {
        MaterialExample materialExample = new MaterialExample();
        MaterialExample.Criteria criteria = materialExample.createCriteria();
        criteria.andOrganizationIdIn(dto.getOuIdList()).andDeleteFlagEqualTo(false);
        return materialMapper.selectByExample(materialExample);

    }

    @Override
    public void updateMaterialDto(List<MaterialDto> dtos) {
        Long userId = SystemContext.getUserId();
        Map<String, String> unitMap =
                ltcDictService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));
        //设置组信息
        Long group = 0L;
        int begin = 0;
        int groupNumber = 1000;
        for (MaterialDto m : dtos) {
            MaterialExample example = new MaterialExample();
            example.createCriteria().andOrganizationIdEqualTo(m.getOrganizationId()).andItemCodeEqualTo(m.getItemCode());
            List<Material> list = materialMapper.selectByExample(example);
            //存在则更新
            if (ListUtils.isNotEmpty(list)) {
                Material material = list.get(0);
                material.setDelistFlag(m.getDelistFlag());
                material.setDeleteFlag(m.getDeleteFlag());
                if (StringUtils.isEmpty(m.getMaterialCategory())) {
                    material.setMaterialCategory(null);
                } else {
                    material.setMaterialCategory(m.getMaterialCategory());
                }
                materialMapper.updateByPrimaryKey(material);
            }
        }
    }

    @Override
    public Material judgeUniqueCheckType4(String brand, String brandMaterialCode, Long organizationId) {
        return materialExtMapper.judgeUniqueCheckType4(brand, brandMaterialCode, organizationId);
    }

    @Override
    public String getMaterialCodeRule(Long unitId) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("unitId", unitId);
        String getMaterialCodeRuleUrl = StringUtils.buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/material/v1" +
                "/getMaterialCodeRuleConfig", param);
        String getMaterialCodeRuleRes = restTemplate.getForObject(getMaterialCodeRuleUrl, String.class);
        DataResponse<String> materialCodeRuleResponse = JSON.parseObject(getMaterialCodeRuleRes, new TypeReference<DataResponse<String>>() {
        });
        String materialCodeRule = materialCodeRuleResponse.getData(); //当前单位的物料编码规则
        Guard.notNullOrEmpty(materialCodeRule, "当前单位的物料编码规则为空");
        MaterialCodeRuleEnum materialCodeRuleEnum = MaterialCodeRuleEnum.getEnumByCode(materialCodeRule);
        Guard.notNull(materialCodeRuleEnum, String.format("当前单位的物料编码规则：%s不存在", materialCodeRule));
        return materialCodeRule;
    }

    @Override
    public String getMaterialCodeRuleOverride(Long unitId) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("unitId", unitId);
        String getMaterialCodeRuleUrl = StringUtils.buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/material/v1" +
                "/getMaterialCodeRuleConfig", param);
        String getMaterialCodeRuleRes = restTemplate.getForObject(getMaterialCodeRuleUrl, String.class);
        DataResponse<String> materialCodeRuleResponse = JSON.parseObject(getMaterialCodeRuleRes, new TypeReference<DataResponse<String>>() {
        });
        String materialCodeRule = materialCodeRuleResponse.getData(); //当前单位的物料编码规则
        Guard.notNullOrEmpty(materialCodeRule, "当前单位的物料编码规则为空");
        MaterialCodeRuleEnum materialCodeRuleEnum = MaterialCodeRuleEnum.getEnumByCode(materialCodeRule);
        Guard.notNull(materialCodeRuleEnum, String.format("当前单位的物料编码规则：%s不存在", materialCodeRule));
        return materialCodeRule;
    }

    @Override
    public ErpCodeRuleDto getByCodingSubclass(String codingSubClass) {
        logger.info("生成erp编码流水号处理{}", codingSubClass);
        if (null != codingSubClass) {
            ErpCodeRuleDto query = new ErpCodeRuleDto();
            query.setCodingSubclass(codingSubClass);
            List<ErpCodeRuleDto> dtos = this.selectList(query);
            if (ListUtil.notEmpty(dtos)) {
                return dtos.get(0);
            }
        }
        return null;
    }

    @Override
    public ErpCodeRuleDto getByCodingSubclass(String codingSubClass, String codingMiddleclass, String ruleName) {
        logger.info("生成erp编码流水号处理{} =" + codingMiddleclass, codingSubClass);
        ErpCodeRuleDto query = new ErpCodeRuleDto();
        query.setCodingSubclass(codingSubClass);
        query.setCodingMiddleclass(codingMiddleclass);
        query.setRuleName(ruleName);
        query.setDeletedFlag(Boolean.FALSE);
        List<ErpCodeRuleDto> dtos = this.selectList(query);
        if (ListUtil.notEmpty(dtos)) {
            return dtos.get(0);
        }
        return null;
    }

    @Override
    public List<ErpCodeRuleDto> selectList(ErpCodeRuleDto query) {
        List<ErpCodeRule> list = erpCodeRuleExtMapper.selectByExample(query);
        List<ErpCodeRuleDto> dtos = BeanConverter.copy(list, ErpCodeRuleDto.class);
        return dtos;
    }

    @Override
    public Material checkMaterial1(String codingMiddleClass, String brand, String figureNumber, List<String> materialCategoryList) {
        return materialExtMapper.checkMaterialNew1(codingMiddleClass, brand, figureNumber, materialCategoryList);

    }

    @Override
    public Material checkMaterial2(String codingMiddleClass, String brand, String model, List<String> materialCategoryList) {
        return materialExtMapper.checkMaterialNew2(codingMiddleClass, brand, model, materialCategoryList);

    }

    @Override
    public Material checkMaterial3(String codingMiddleClass, String brand, String brandMaterialCode, List<String> materialCategoryList) {
        return materialExtMapper.checkMaterialNew3(codingMiddleClass, brand, brandMaterialCode, materialCategoryList);

    }

    @Override
    public boolean ksMatchingMaterialCodeEstimateNew(List<MilepostDesignPlanDetailDto> designPlanDetailDtos, Long storageId, Long unitId,
                                                     Long markId, boolean validation, Long projectId, Long projectTypeId,
                                                     List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo) {
        if (projectTypeId == null) {
            Guard.notNull(projectId, "项目ID为空");
            Project project = projectExtMapper.selectByPrimaryKey(projectId);
            Guard.notNull(project, String.format("项目ID：%s对应的项目不存在", projectId));
            Guard.notNull(project.getType(), String.format("项目ID：%s对应的项目类型为空", projectId));
            projectTypeId = project.getType();
        }
        ProjectType projectType = projectTypeExtMapper.selectByPrimaryKey(projectTypeId);
        Guard.notNull(projectType, String.format("项目ID：%s对应的项目类型不存在", projectId));
        String budgetConfig = projectType.getBudgetConfig();
        JSONArray materiel = JSON.parseObject(budgetConfig).getJSONArray(BudgetConfigEnums.MATERIEL.getCode());
        // 项目类型设置的预算设置的字段设置的计划交付日期 的开关是否开启
        Boolean isOpenDeliveryTime = false;
        for (int i = 0; i < materiel.size(); i++) {
            JSONObject jsonObject = materiel.getJSONObject(i);
            if (null == jsonObject) {
                continue;
            }
            String code = Optional.ofNullable(jsonObject.get("code")).map(Object::toString).orElse(null);
            String value = Optional.ofNullable(jsonObject.get("value")).map(Object::toString).orElse(null);
            if (Objects.equals("fieldSetByMaterial", code) && Objects.equals("[1]", value)) {
                isOpenDeliveryTime = true;
                break;
            }
        }

        // 校验非标件的图号唯一性 - 放在编码规则判断之前，因为这是通用校验
        if (!validateNonStandardPartsFigureNumber(designPlanDetailDtos)) {
            return false;
        }

        String materialCodeRule = getMaterialCodeRule(unitId); //当前单位的物料编码规则
        if (MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
            return enableKUKA2022MaterialCodeRule(designPlanDetailDtos, storageId, unitId, markId, validation, materialCodeRule, isOpenDeliveryTime,
                    designPlanDetailDtoForWebInfo);
        } else {
            return disableKUKA2022MaterialCodeRule(designPlanDetailDtos, storageId, unitId, markId, validation, materialCodeRule, isOpenDeliveryTime);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean disableKUKA2022MaterialCodeRule(List<MilepostDesignPlanDetailDto> designPlanDetailDtos,
                                                   Long storageId, Long unitId, Long markId, boolean validation, String materialCodeRule,
                                                   Boolean isOpenDeliveryTime) {

        boolean result = validation;

        //获取"机械加工件"对应"加工件分类"的属性
        List<DictDto> dicts = ltcDictService.getLtcDict(MACHINED_PART_TYPE, null, null);
        Guard.notNullOrEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");
        // 获取类别列表数据
        List<MerielSubclassVO> merielSubclassList;
        if (MaterialCodeRuleEnum.KUKAMIA.getCode().equals(materialCodeRule)) {
            merielSubclassList = ctcExtService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUKAMIA.getCode());//昆山
        } else {
            merielSubclassList = ctcExtService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUNSHAN.getCode());//昆山
        }

        // 物料类别Table数据（row小类，column中类，value实体类）
        HashBasedTable<String, String, MerielSubclassVO> merielClassTable = HashBasedTable.create();
        for (MerielSubclassVO merielSubclassVO : merielSubclassList) {
            merielClassTable.put(merielSubclassVO.getValue(), merielSubclassVO.getMiddileValue(), merielSubclassVO);
        }
        // 获取计量单位
        List<DictDto> dictDtoList = ltcDictService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Map<String, String> unitNameCodeMap = new HashMap<>();
        Map<String, String> unitCodeNameMap = new HashMap<>();
        for (DictDto dictDto : dictDtoList) {
            String code = dictDto.getCode();
            String name = dictDto.getName();
            unitNameCodeMap.put(name, code);
            unitCodeNameMap.put(code, name);
        }

        //获取品牌列表数据
        List<BrandMaintenance> brandMaintenanceList = brandMaintenanceService.selectList();
        Guard.notNullOrEmpty(brandMaintenanceList, "请先维护品牌信息");

        // 获取备件标识组织参数配置
        Set<String> orSparePartsMaskSet = ctcExtService.queryByName(Constants.SPAREPARTS_MASK, unitId, OrgCustomDictOrgFrom.COMPANY);
        Guard.notNullOrEmpty(orSparePartsMaskSet, "备件标识需要在组织参数中维护");

        // 获取物料类型及编码字典
        Byte a = 1;
        Map<String, DictDto> materialTypeMap = ltcDictService.getLtcDict(MATERIAL_TYPE, null, null).stream()
                .filter(o -> !Objects.equals(a, o.getOrderNum())).collect(Collectors.toMap(DictDto::getName, Function.identity(),
                        (key1, key2) -> key2));

        List<String> repetition1 = new ArrayList<>();
        List<String> repetition2 = new ArrayList<>();
        List<String> repetition3 = new ArrayList<>();
        List<MilepostDesignPlanDetailDto> erpCodeIsNotEmptyList = new ArrayList<>();
        List<MilepostDesignPlanDetailDto> erpCodeIsEmptyList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto dto : designPlanDetailDtos) {
            String perchasingLeadtimeStr = dto.getPerchasingLeadtimeStr();
            if (StringUtils.isNotEmpty(perchasingLeadtimeStr)) {
                if (isInteger(perchasingLeadtimeStr)) {
                    dto.setPerchasingLeadtime(Long.valueOf(perchasingLeadtimeStr));
                } else {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "采购提前期不是整数");
                }
            }
            String minPerchaseQuantityStr = dto.getMinPerchaseQuantityStr();
            if (StringUtils.isNotEmpty(minPerchaseQuantityStr)) {
                if (isInteger(minPerchaseQuantityStr)) {
                    dto.setMinPerchaseQuantity(Long.valueOf(minPerchaseQuantityStr));
                } else {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "最小采购量不是整数");
                }
            }
            String minPackageQuantityStr = dto.getMinPackageQuantityStr();
            if (StringUtils.isNotEmpty(minPackageQuantityStr)) {
                if (isInteger(minPackageQuantityStr)) {
                    dto.setMinPackageQuantity(Long.valueOf(minPackageQuantityStr));
                } else {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "最小包装量不是整数");
                }
            }
            String numberStr = dto.getNumberStr();
            if (StringUtils.isNotEmpty(numberStr)) {
                if (BigDecimalUtils.isBigDecimal(numberStr)) {
                    dto.setNumber(BigDecimalUtils.stringToBigDecimal(numberStr));
                } else {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "数量有误");
                }
            }
            // 单件重量(Kg)
            String unitWeightStr = dto.getUnitWeightStr();
            if (StringUtils.isNotEmpty(unitWeightStr)) {
                if (BigDecimalUtils.isBigDecimal(unitWeightStr)) {
                    dto.setUnitWeight(BigDecimalUtils.stringToBigDecimal(unitWeightStr));
                } else {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "单件重量(Kg)有误");
                }
            }
            if (!result) {
                continue;
            }

            // PAM物料编码 和 ERP物料编码 不能都有值
            String pamCode = dto.getPamCode();
            String erpCode = dto.getErpCode();
            boolean erpCodeIsNotEmpty = StringUtils.isNotEmpty(erpCode);
            // 手动添加
            boolean isHand = Objects.equals("1", dto.getOperationType());
            if (StringUtils.isNotEmpty(pamCode) && erpCodeIsNotEmpty) {
                // 手动增加不走这个校验
                if (!isHand) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "PAM编码和ERP编码填写其一中一个即可");
                    continue;
                }
            }

            // 图号
            Integer figureNumberOracleLength = getOracleLengthFromMysqlVarchar(dto.getFigureNumber());
            // 型号/规格
            Integer modelOracleLength = getOracleLengthFromMysqlVarchar(dto.getModel());
            if (figureNumberOracleLength + modelOracleLength > 70) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "图号和型号字段的字符之和不能超过70");
                continue;
            }

            dto.setImportErpCodeIsNotEmpty(erpCodeIsNotEmpty);
            // ERP物料编码有值则走 跨组织物料引用 逻辑，否则走以前的逻辑
            if (erpCodeIsNotEmpty) {
                //校验必填项
                String materialCategory = dto.getMaterialCategory();
                if (StringUtils.isNotEmpty(materialCategory)) {
                    DictDto materialTypeDict = materialTypeMap.get(materialCategory);
                    if (Objects.isNull(materialTypeDict)) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，"))
                                + "物料类型为字典项material_type中序号不为“1”对应的值");
                        continue;
                    }
                } else {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料类型不能为空");
                    continue;
                }
                logger.info("ksMatchingMaterialCodeEstimateNew的commonValidate前：{}", JSONObject.toJSONString(dto));
                result = commonValidate(dto, merielClassTable, dicts, orSparePartsMaskSet, unitNameCodeMap, result, brandMaintenanceList, isHand,
                        isOpenDeliveryTime);
                if (!result) {
                    continue;
                }
                logger.info("ksMatchingMaterialCodeEstimateNew的commonValidate后：{}", JSONObject.toJSONString(dto));

                MaterialExample example = new MaterialExample();
                MaterialExample.Criteria criteria = example.createCriteria();
                criteria.andItemCodeEqualTo(erpCode).andDeleteFlagEqualTo(false).andDelistFlagEqualTo(false);
                List<Material> materialList = materialMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(materialList)) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "ERP编码不存在");
                    continue;
                }
                // 如果按ERP编码在当前库存组织136找到多条数据
                List<Material> currentMaterialList =
                        materialList.stream().filter(e -> Objects.equals(storageId, e.getOrganizationId())).collect(Collectors.toList());
                Material currentMaterial = CollectionUtils.isEmpty(currentMaterialList) ? null : currentMaterialList.get(0);
                if (currentMaterial != null) {
                    dto.setImportFindCurrentMaterialByErpCode(true);
                    String pCode = currentMaterial.getPamCode();
                    // 如果按ERP编码在当前库存组织136找到一条数据，并且这条数据PAM编码已存在：和走PAM编码导入的逻辑一样
                    if (StringUtils.isNotEmpty(pCode)) {
                        dto.setPamCode(pCode);
                        String unitCode = currentMaterial.getUnit();
                        if (!unitCodeNameMap.containsKey(unitCode)) {
                            String errorMsg = String.format("引用的物料id为：%d的物料的单位：%s格式错误", currentMaterial.getId(), unitCode);
                            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + errorMsg);
                            result = false;
                        }
                        dto.setUnitCode(unitCode);
                        dto.setUnit(unitCodeNameMap.get(unitCode));
                        dto.setMaterielDescr(currentMaterial.getItemInfo()); // 物料描述
                        Integer materielDescrLength = getOracleLengthFromMysqlVarchar(dto.getMaterielDescr());
                        if (materielDescrLength > 240) {
                            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，"))
                                    + "物料描述的字段长度超过了240个字符");
                            result = false;
                        }
                        erpCodeIsEmptyList.add(dto);
                    } else {
                        // 如果按ERP编码在当前库存组织136找到一条数据，并且这条数据PAM编码不存在：    改中间表新增逻辑-改变更表新增逻辑-更新物料基础表
                        dto.setImportFindMaterialPamCodeIsNotExist(true);
                        dto.setPamCode(generateMaterialPAMCode());
                        dto.setErpCodeSource(2);
                        result = setDtoByErpCodeIsNotEmpty(currentMaterial, dto, unitCodeNameMap, isOpenDeliveryTime);
                        erpCodeIsNotEmptyList.add(dto);
                    }
                } else {
                    Material material = BeanConverter.copy(materialList.get(0), Material.class);
                    String pCode = material.getPamCode();
                    if (StringUtils.isNotEmpty(pCode)) {
                        // 如果按ERP编码在其它库存组织（如137）找到一条数据，并且这条数据PAM编码已存在： 中间表&变更表取基础表的数据，和PAM编码导入逻辑一致，-新增物料基础表到136
                        dto.setPamCode(pCode);
                        String unitCode = material.getUnit();
                        if (!unitCodeNameMap.containsKey(unitCode)) {
                            String errorMsg = String.format("引用的物料id为：%d的物料的单位：%s格式错误", material.getId(), unitCode);
                            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + errorMsg);
                            result = false;
                        }
                        dto.setUnitCode(unitCode);
                        dto.setUnit(unitCodeNameMap.get(unitCode));
                        dto.setMaterielDescr(material.getItemInfo()); // 物料描述
                        Integer materielDescrLength = getOracleLengthFromMysqlVarchar(dto.getMaterielDescr());
                        if (materielDescrLength > 240) {
                            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，"))
                                    + "物料描述的字段长度超过了240个字符");
                            result = false;
                        }
                        erpCodeIsEmptyList.add(dto);
                    } else {
                        // 如果按ERP编码在其它库存组织（如137）找到一条数据，并且这条数据PAM编码不存在：改中间表新增逻辑-改变更表新增逻辑-新增物料基础表到136
                        dto.setImportFindMaterialPamCodeIsNotExist(true);
                        dto.setPamCode(generateMaterialPAMCode());
                        dto.setErpCodeSource(2);
                        result = setDtoByErpCodeIsNotEmpty(material, dto, unitCodeNameMap, isOpenDeliveryTime);
                        erpCodeIsNotEmptyList.add(dto);
                    }
                }
            } else {
                // ERP物料编码没有值走以前的逻辑
                result = this.oldAdditionalValidate(dto, merielClassTable, dicts, orSparePartsMaskSet, unitNameCodeMap, unitCodeNameMap, result,
                        repetition1, repetition2, repetition3, storageId, materialTypeMap, brandMaintenanceList, isHand, isOpenDeliveryTime);
                erpCodeIsEmptyList.add(dto);
            }
        }
        if (!result) {
            return false;
        }

        //判断物料的唯一性(新逻辑 跨组织物料引用 不走该校验)
        //多线程处理
        if (erpCodeIsEmptyList.size() >= 100) {
            handleList(erpCodeIsEmptyList, storageId, unitCodeNameMap);
        } else {
            for (MilepostDesignPlanDetailDto dto : erpCodeIsEmptyList) {
                List<BrandMaintenance> brandList = brandMaintenanceList.stream().filter(brand ->
                                (Objects.equals("1", dto.getOperationType()) ? (Objects.equals(brand.getBrandName(), dto.getBrand()))
                                        : (Objects.equals(brand.getBrandNameCn(), dto.getBrand())
                                        || Objects.equals(brand.getBrandNameEn(), dto.getBrand())
                                        || Objects.equals(brand.getBrandName(), dto.getBrand())))
                                        && (Boolean.FALSE.equals(brand.getDeletedFlag()) || Objects.equals(0, brand.getBrandStatus())))
                        .collect(Collectors.toList());
                dto.setBrand(CollectionUtils.isNotEmpty(brandList) ? brandList.get(0).getBrandName() : null);
                this.checkKsMaterial(dto, storageId, unitCodeNameMap);
            }
        }
        result = erpCodeIsEmptyList.stream().noneMatch(e -> StringUtils.isNotEmpty(e.getValidResult()));
        if (!result) {
            return false;
        }

        // 合并
        erpCodeIsEmptyList.addAll(erpCodeIsNotEmptyList);

        // 是否启用物料类别映射库存分类
        String currentDateStr = DateUtil.format(new Date(), "yyyy-MM-dd");
        OrganizationCustomDict organizationCustomDict = organizationCustomDictExtMapper.queryByNameAndOrgId("是否启用物料类别映射库存分类", storageId,
                currentDateStr);
        boolean enableInventoryType = Objects.nonNull(organizationCustomDict);
        // 处理库存分类的inventoryType
        Map<String, String> inventoryTypeMap = new HashMap<>();
        if (enableInventoryType) {
            List<String> materialClassificationList = new ArrayList<>();
            List<String> codingMiddleClassList = new ArrayList<>();
            List<String> materielTypeList = new ArrayList<>();
            for (MilepostDesignPlanDetailDto designPlanDetailDto : erpCodeIsEmptyList) {
                //校验必填项
                String materialClassification = designPlanDetailDto.getMaterialClassification();
                if (StringUtils.isEmpty(materialClassification)) {
                    result = false;
                    designPlanDetailDto.setValidResult((StringUtils.isEmpty(designPlanDetailDto.getValidResult()) ? ""
                            : (designPlanDetailDto.getValidResult() + "，")) + "物料大类不能为空");
                    continue;
                } else if (merielSubclassList.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(materialClassification))) {
                    result = false;
                    designPlanDetailDto.setValidResult((StringUtils.isEmpty(designPlanDetailDto.getValidResult()) ? ""
                            : (designPlanDetailDto.getValidResult() + "，")) + "物料大类:[" + materialClassification + "]不存在,请先维护");
                }
                materialClassificationList.add(materialClassification);
                codingMiddleClassList.add(designPlanDetailDto.getCodingMiddleClass());
                materielTypeList.add(designPlanDetailDto.getMaterielType());
            }
            if (!result) {
                return false;
            }

            // 获取物料类别配置（库存分类）
            MaterialCustomDictHeaderExample customDictHeaderExample = new MaterialCustomDictHeaderExample();
            customDictHeaderExample.createCriteria().andDeletedFlagEqualTo(false).andOrganizationIdEqualTo(storageId)
                    .andCodingClassIn(materialClassificationList).andCodingMiddleclassIn(codingMiddleClassList).andCodingSubclassIn(materielTypeList);
            List<MaterialCustomDictHeader> materialCustomDictHeaders = materialCustomDictHeaderExtMapper.selectByExample(customDictHeaderExample);
            Map<String, List<Long>> materialCustomDictHeaderMap = new HashMap<>();
            List<Long> headerIdList = new ArrayList<>();
            for (MaterialCustomDictHeader customDictHeader : materialCustomDictHeaders) {
                Long headerId = customDictHeader.getId();
                String customDictHeaderKey = getMaterialCustomDictHeaderKey(customDictHeader.getCodingClass(),
                        customDictHeader.getCodingMiddleclass(), customDictHeader.getCodingSubclass());
                if (materialCustomDictHeaderMap.containsKey(customDictHeaderKey)) {
                    List<Long> valueList = materialCustomDictHeaderMap.get(customDictHeaderKey);
                    valueList.add(headerId);
                    materialCustomDictHeaderMap.put(customDictHeaderKey, valueList);
                } else {
                    materialCustomDictHeaderMap.put(customDictHeaderKey, Lists.newArrayList(headerId));
                }
                headerIdList.add(headerId);
            }
            if (MapUtils.isNotEmpty(materialCustomDictHeaderMap)) {
                // “物料类别配置”，按"库存组织+物料大中小类"配置大中小类的"库存分类"（存在且未删除、启用日期<=系统当前日期<=失效日期、属性值不为空）
                List<MaterialCustomDict> dictList = materialCustomDictExtMapper.queryByNameAndHeaderIdList("库存分类", headerIdList, currentDateStr);
                if (CollectionUtils.isNotEmpty(dictList)) {
                    materialCustomDictHeaderMap.forEach((k, v) -> {
                        // material_custom_dict中多个headerId 对应一个value
                        String inventoryType =
                                dictList.stream().filter(e -> v.contains(e.getHeaderId())).map(MaterialCustomDict::getValue).findFirst().orElse(null);
                        inventoryTypeMap.put(k, inventoryType);
                    });
                }
            }
            logger.info("非柔性详设导入的库存分类的inventoryTypeMap：{}", JsonUtils.toString(inventoryTypeMap));

            // 设置库存分类的inventoryType
            for (MilepostDesignPlanDetailDto dto : erpCodeIsEmptyList) {
                // 库存分类维护
                String inventoryType = inventoryTypeMap.get(getMaterialCustomDictHeaderKey(dto.getMaterialClassification(),
                        dto.getCodingMiddleClass(), dto.getMaterielType()));
                if (StringUtils.isEmpty(inventoryType)) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "该物料大中小类的库存分类未维护，不允许导入");
                    continue;
                }
                dto.setInventoryType(inventoryType);
            }
        }

        if (result) {
            this.saveMilepostDesignDetailMiddle(erpCodeIsEmptyList, markId, storageId);
        }
        return result;
    }

    /**
     * 校验非标件的图号唯一性
     * 检查是否存在多个E码对应同一个图号的情况
     *
     * @param designPlanDetailDtos 设计计划详情列表
     * @return 校验结果，true-通过，false-不通过
     */
    private boolean validateNonStandardPartsFigureNumber(List<MilepostDesignPlanDetailDto> designPlanDetailDtos) {
        // 检查非标件多E码对应同一图号的情况
        Map<String, List<String>> figureNumberToErpCodes = new HashMap<>();

        ArrayList<String> nonStandardPartsList = Lists.newArrayList("非标准类物料", "服务类非标准物料");

        // 收集非标件的图号和ERP编码对应关系
        for (MilepostDesignPlanDetailDto detailDto : designPlanDetailDtos) {
            // 只处理非标件且有ERP编码的情况
            if (nonStandardPartsList.contains(detailDto.getMaterialClassification()) &&
                    StringUtils.isNotEmpty(detailDto.getErpCode()) &&
                    StringUtils.isNotEmpty(detailDto.getFigureNumber())) {

                figureNumberToErpCodes.computeIfAbsent(detailDto.getFigureNumber(), k -> new ArrayList<>())
                        .add(detailDto.getErpCode());
            }
        }

        // 检查并设置错误信息
        boolean result = true;
        for (Map.Entry<String, List<String>> entry : figureNumberToErpCodes.entrySet()) {
            if (entry.getValue().size() > 1) {
                String figureNumber = entry.getKey();
                List<String> erpCodes = entry.getValue();

                String errorMsg = String.format("所导入数据中,存在多个非标物料：%s，对应同一个图号:[%s]",
                        String.join("，", erpCodes), figureNumber);

                // 为所有相关记录设置错误信息
                for (MilepostDesignPlanDetailDto detailDto : designPlanDetailDtos) {
                    if (figureNumber.equals(detailDto.getFigureNumber()) &&
                            erpCodes.contains(detailDto.getErpCode())) {
                        detailDto.setValidResult(StringUtils.isEmpty(detailDto.getValidResult()) ?
                                errorMsg : detailDto.getValidResult() + "，" + errorMsg);
                    }
                }
                result = false;
            }
        }

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean enableKUKA2022MaterialCodeRule(List<MilepostDesignPlanDetailDto> designPlanDetailDtos,
                                                  Long storageId, Long unitId, Long markId, boolean validation, String materialCodeRule,
                                                  Boolean isOpenDeliveryTime, List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo) {
        boolean result = validation;

        //获取"机械加工件"对应"加工件分类"的属性
        List<DictDto> dicts = ltcDictService.getLtcDict(MACHINED_PART_TYPE, null, null);
        Guard.notNullOrEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");
        // 获取类别列表数据
        List<MerielSubclassVO> merielSubclassList = ctcExtService.findErpCodeRuleByRuleName(materialCodeRule);
        Guard.notNullOrEmpty(merielSubclassList, "物料编码规则" + MaterialCodeRuleEnum.KUKA2022.getName() + "尚未维护数据,请先维护");
        Map<String, MerielSubclassVO> materialClassMap = new HashMap<>();
        for (MerielSubclassVO merielSubclassVO : merielSubclassList) {
            String erpCodeRuleKey = getErpCodeRuleKey(merielSubclassVO.getBigValue(), merielSubclassVO.getMiddileValue(),
                    merielSubclassVO.getValue());
            materialClassMap.put(erpCodeRuleKey, merielSubclassVO);
        }
        // 获取计量单位
        List<DictDto> dictDtoList = ltcDictService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Map<String, String> unitNameCodeMap = new HashMap<>();
        Map<String, String> unitCodeNameMap = new HashMap<>();
        for (DictDto dictDto : dictDtoList) {
            String code = dictDto.getCode();
            String name = dictDto.getName();
            unitNameCodeMap.put(name, code);
            unitCodeNameMap.put(code, name);
        }

        //获取品牌列表数据
        List<BrandMaintenance> brandMaintenanceList = brandMaintenanceService.selectList();
        Guard.notNullOrEmpty(brandMaintenanceList, "请先维护品牌信息");

        // 获取备件标识组织参数配置
        Set<String> orSparePartsMaskSet = ctcExtService.queryByName(Constants.SPAREPARTS_MASK, unitId, OrgCustomDictOrgFrom.COMPANY);
        Guard.notNullOrEmpty(orSparePartsMaskSet, "备件标识需要在组织参数中维护");

        // 获取物料类型及编码字典
        Byte a = 1;
        Map<String, DictDto> materialTypeMap = ltcDictService.getLtcDict(MATERIAL_TYPE, null, null).stream()
                .filter(o -> !Objects.equals(a, o.getOrderNum())).collect(Collectors.toMap(DictDto::getName, Function.identity(),
                        (key1, key2) -> key2));

        // 非标件大类
        List<String> nonStandardMaterialType = ltcDictService.getLtcDict(DictType.MATERIAL_TYPE_NON_STANDARD.code(), null, null).stream()
                .map(Dict::getName).distinct().collect(Collectors.toList());

        //获得所有使用当前物料编码规则的使用单位
        List<Unit> units = materialExtMapper.getAllUnitByUsingMaterialCodeRule(materialCodeRule);
        //根据使用单位获得对应所有库存组织
        List<OrganizationRel> organizationRels = organizationRelService.queryByUnitId(units.stream().map(Unit::getId).collect(Collectors.toList()));
        List<Long> organizationIds = new ArrayList<>();
        Map<Long, String> organizationMap = new HashMap<>();
        for (OrganizationRel organizationRel : organizationRels) {
            Long organizationId = organizationRel.getOrganizationId();
            if (!organizationIds.contains(organizationId)) {
                organizationIds.add(organizationId);
                organizationMap.put(organizationId, organizationRel.getOrganizationName());
            }
        }

        // 物料编码规则字符忽略
        List<String> characterIgnoreList = ltcDictService.getLtcDict(DictType.MATERIAL_CODE_RULE_CHARACTER_IGNORE.code(), null, null).stream()
                .map(Dict::getName).distinct().collect(Collectors.toList());

        List<String> nonStandardMaterialsForWebInfo = new ArrayList<>();
        List<String> standardMaterialsForWebInfoForWebInfo = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(designPlanDetailDtoForWebInfo)) {
            for (MilepostDesignPlanDetailDto webInfo : designPlanDetailDtoForWebInfo) {
                String materialClassification = webInfo.getMaterialClassification();
                if (StringUtils.isNotEmpty(materialClassification) && nonStandardMaterialType.contains(materialClassification)
                        && StringUtils.isNotEmpty(webInfo.getFigureNumber())) {
                    nonStandardMaterialsForWebInfo.add(webInfo.getFigureNumber());
                } else if (StringUtils.isNotEmpty(materialClassification) && !nonStandardMaterialType.contains(materialClassification)) {
                    String webInfoBrand = webInfo.getBrand();
                    if (StringUtils.isNotEmpty(webInfoBrand)) {
                        List<BrandMaintenance> maintenanceList = brandMaintenanceList.stream().filter(brand ->
                                        (Objects.equals("1", designPlanDetailDtos.get(0).getOperationType())
                                                ? (Objects.equals(brand.getBrandName(), webInfoBrand))
                                                : (Objects.equals(brand.getBrandNameCn(), webInfoBrand)
                                                || Objects.equals(brand.getBrandNameEn(), webInfoBrand)
                                                || Objects.equals(brand.getBrandName(), webInfoBrand)))
                                                && (Boolean.FALSE.equals(brand.getDeletedFlag()) || Objects.equals(0, brand.getBrandStatus())))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(maintenanceList) && StringUtils.isNotEmpty(webInfo.getModel())) {
                            standardMaterialsForWebInfoForWebInfo.add(maintenanceList.get(0).getBrandName() + ":"
                                    + removeCharacterIgnoreList(webInfo.getModel(), characterIgnoreList));
                        }
                    }
                }
            }
        }
        logger.info("非标件大类物料编码:{}", JsonUtils.toString(nonStandardMaterialsForWebInfo));
        logger.info("标件大类物料编码:{}", JsonUtils.toString(standardMaterialsForWebInfoForWebInfo));

        List<String> pamCodeList = new ArrayList<>();
        List<String> erpCodeList = new ArrayList<>();
        List<String> figureNumberList = new ArrayList<>();
        List<String> brandList = new ArrayList<>();
        List<String> modelList = new ArrayList<>();
        List<String> indexCheckList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto detailDto : designPlanDetailDtos) {
            String pamCode = null;
            String erpCode = null;
            boolean pamCodeImportFlag = detailDto.isPamCodeImportFlag();
            boolean erpCodeImportFlag = detailDto.isErpCodeImportFlag();
            boolean notPamCodeAndNotErpCodeImportFlag = detailDto.isNotPamCodeAndNotErpCodeImportFlag();
            if (!pamCodeImportFlag && !erpCodeImportFlag && !notPamCodeAndNotErpCodeImportFlag) {
                pamCode = detailDto.getPamCode();
                erpCode = detailDto.getErpCode();
            } else if (pamCodeImportFlag) {
                pamCode = detailDto.getPamCode();
            } else if (erpCodeImportFlag) {
                erpCode = detailDto.getErpCode();
            }

            String serialNumber = detailDto.getSerialNumber();
            String materialCategory = detailDto.getMaterialCategory();
            List<String> validResultList = StringUtils.isEmpty(detailDto.getValidResult()) ? new ArrayList<>()
                    : Arrays.asList(detailDto.getValidResult().split("，"));
            if (StringUtils.isEmpty(serialNumber)) {
                result = false;
                validResultList.add("序号不能为空");
            } else {
                if (!NumberUtil.isNumber(serialNumber)) {
                    result = false;
                    validResultList.add("序号只能填数字");
                } else if (indexCheckList.contains(serialNumber)) {
                    result = false;
                    validResultList.add("序号不能重复");
                } else {
                    indexCheckList.add(serialNumber);
                }
            }
            if (StringUtils.isEmpty(materialCategory)) {
                result = false;
                validResultList.add("物料类型不能为空");
            } else if (!materialTypeMap.containsKey(materialCategory)) {
                result = false;
                validResultList.add("物料类型只允许为:看板物料，外购物料，装配件，虚拟件");
            }

            String deliveryTimeStr = detailDto.getDeliveryTimeStr();
            // 计划交货日期=不打勾   外购物料、看板物料 需要填写 物料到货日期；计划交货日期=打钩    外购物料、看板物料  不需要填写 物料到货日期
            if (Boolean.FALSE.equals(isOpenDeliveryTime) && ("外购物料".equals(detailDto.getMaterialCategory()) || "看板物料".equals(detailDto.getMaterialCategory()))) {
                if (StringUtils.isEmpty(deliveryTimeStr)) {
                    result = false;
                    validResultList.add("外购物料/看板物料,物料到货日期不能为空");
                }
            }
            if (StringUtils.isNotEmpty(deliveryTimeStr)) {
                if (DateUtils.isValidDate(deliveryTimeStr, DateUtils.FORMAT_SHORT)) {
                    detailDto.setDeliveryTime(DateUtils.parse(deliveryTimeStr, DateUtils.FORMAT_SHORT));
                } else {
                    validResultList.add("物料到货日期格式不正确");
                    result = false;
                }
            }

            String perchasingLeadtimeStr = detailDto.getPerchasingLeadtimeStr();
            if (StringUtils.isNotEmpty(perchasingLeadtimeStr)) {
                if (isInteger(perchasingLeadtimeStr)) {
                    detailDto.setPerchasingLeadtime(Long.valueOf(perchasingLeadtimeStr));
                } else {
                    result = false;
                    validResultList.add("采购提前期不是整数");
                }
            }
            String minPerchaseQuantityStr = detailDto.getMinPerchaseQuantityStr();
            if (StringUtils.isNotEmpty(minPerchaseQuantityStr)) {
                if (isInteger(minPerchaseQuantityStr)) {
                    detailDto.setMinPerchaseQuantity(Long.valueOf(minPerchaseQuantityStr));
                } else {
                    result = false;
                    validResultList.add("最小采购量不是整数");
                }
            }
            String minPackageQuantityStr = detailDto.getMinPackageQuantityStr();
            if (StringUtils.isNotEmpty(minPackageQuantityStr)) {
                if (isInteger(minPackageQuantityStr)) {
                    detailDto.setMinPackageQuantity(Long.valueOf(minPackageQuantityStr));
                } else {
                    result = false;
                    validResultList.add("最小包装量不是整数");
                }
            }
            String numberStr = detailDto.getNumberStr();
            if (StringUtils.isNotEmpty(numberStr)) {
                if (BigDecimalUtils.isBigDecimal(numberStr)) {
                    detailDto.setNumber(BigDecimalUtils.stringToBigDecimal(numberStr));
                } else {
                    result = false;
                    validResultList.add("数量有误");
                }
            } else {
                if (detailDto.getNumber() == null) { //手工新增，numberStr为空
                    result = false;
                    validResultList.add("数量不能为空");
                }
            }
            // 单件重量(Kg)
            String unitWeightStr = detailDto.getUnitWeightStr();
            if (StringUtils.isNotEmpty(unitWeightStr)) {
                if (BigDecimalUtils.isBigDecimal(unitWeightStr)) {
                    detailDto.setUnitWeight(BigDecimalUtils.stringToBigDecimal(unitWeightStr));
                } else {
                    result = false;
                    validResultList.add("单件重量(Kg)有误");
                }
            }

            // 图号
            Integer figureNumberOracleLength = getOracleLengthFromMysqlVarchar(detailDto.getFigureNumber());
            // 型号/规格
            Integer modelOracleLength = getOracleLengthFromMysqlVarchar(detailDto.getModel());
            if (figureNumberOracleLength + modelOracleLength > 70) {
                result = false;
                validResultList.add("图号和型号字段的字符之和不能超过70");
            }

            boolean erpCodeIsNotEmpty = StringUtils.isNotEmpty(erpCode);
            if (StringUtils.isNotEmpty(pamCode) && erpCodeIsNotEmpty) {
                // 手动增加不走这个校验
                if (!Objects.equals("1", detailDto.getOperationType())) {
                    result = false;
                    validResultList.add("PAM编码和ERP编码填写其一中一个即可");
                }
            }
            detailDto.setImportErpCodeIsNotEmpty(erpCodeIsNotEmpty);
            if (StringUtils.isNotEmpty(pamCode)) {
                pamCodeList.add(pamCode);
            } else if (StringUtils.isNotEmpty(erpCode)) {
                erpCodeList.add(erpCode);
            }

            String figureNumber = detailDto.getFigureNumber();
            if (StringUtils.isNotEmpty(figureNumber)) {
                figureNumberList.add(figureNumber);
            }

            //如果品牌不为空则转大写
            if (StringUtils.isNotEmpty(detailDto.getBrand())) {
                if (detailDto.getBrand().length() > 150) {
                    result = false;
                    validResultList.add("品牌字符长度不能超过150");
                }
                detailDto.setBrand(detailDto.getBrand().toUpperCase(Locale.ROOT));
                if (Objects.equals(detailDto.getBrand(), "无") || Objects.equals(detailDto.getBrand(), "NONE")) {
                    result = false;
                    validResultList.add("品牌不能为无或NONE");
                }
            }
            String brandName = detailDto.getBrand();
            if (StringUtils.isNotEmpty(brandName)) {
                List<BrandMaintenance> maintenanceList = brandMaintenanceList.stream().filter(brand ->
                                (Objects.equals("1", detailDto.getOperationType()) ? (Objects.equals(brand.getBrandName(), brandName))
                                        : (Objects.equals(brand.getBrandNameCn(), brandName) || Objects.equals(brand.getBrandNameEn(), brandName)
                                        || Objects.equals(brand.getBrandName(), brandName)))
                                        && (Boolean.FALSE.equals(brand.getDeletedFlag()) || Objects.equals(0, brand.getBrandStatus())))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(maintenanceList)) {
                    result = false;
                    validResultList.add("品牌名称：" + brandName + "在品牌表中不存在");
                } else {
                    String finalBrandName = maintenanceList.get(0).getBrandName();
                    // 设置最终的品牌名称
                    detailDto.setBrand(finalBrandName);
                    brandList.add(finalBrandName);
                }
            }
            String model = detailDto.getModel();
            if (StringUtils.isNotEmpty(model)) {
                modelList.add(model);
            }

            String materialProcessing = detailDto.getMaterialProcessing();
            if (StringUtils.isNotEmpty(materialProcessing) && materialProcessing.length() > 32) {
                validResultList.add("表面处理字段超长");
                result = false;
            }

            //材质内容长度校验
            if (StringUtils.isNotEmpty(detailDto.getMaterial())) {
                if (detailDto.getMaterial().length() > 32) {
                    result = false;
                    validResultList.add("材质字段的字符不能超过32");
                }
            }

            detailDto.setValidResult(Joiner.on("，").join(validResultList));
        }
        if (!result) {
            return false;
        }

        Map<String, List<MaterialDto>> pamAdjustMaterialsMap = new HashMap<>();
        Map<String, List<MaterialDto>> erpAdjustMaterialsMap = new HashMap<>();
        //通过pamCode和erpCode查询所有物料变更表中的数据
        List<MaterialDto> allPamCodeAndErpCodeAdjustMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pamCodeList) || CollectionUtils.isNotEmpty(erpCodeList)) {
            allPamCodeAndErpCodeAdjustMaterials.addAll(materialExtMapper.getAllPamCodeAndErpCodeAdjustMaterial(organizationIds, pamCodeList,
                    erpCodeList));
        }
        for (MaterialDto materialDto : allPamCodeAndErpCodeAdjustMaterials) {
            String pamCode = materialDto.getPamCode();
            if (StringUtils.isNotEmpty(pamCode)) {
                if (pamAdjustMaterialsMap.containsKey(pamCode)) {
                    pamAdjustMaterialsMap.get(pamCode).add(materialDto);
                } else {
                    pamAdjustMaterialsMap.put(pamCode, Lists.newArrayList(materialDto));
                }
            }
            String erpCode = materialDto.getErpCode();
            if (StringUtils.isNotEmpty(erpCode)) {
                if (erpAdjustMaterialsMap.containsKey(erpCode)) {
                    erpAdjustMaterialsMap.get(erpCode).add(materialDto);
                } else {
                    erpAdjustMaterialsMap.put(erpCode, Lists.newArrayList(materialDto));
                }
            }
        }

        Map<String, List<MaterialDto>> pamMaterialsMap = new HashMap<>();
        Map<String, List<MaterialDto>> erpMaterialsMap = new HashMap<>();
        Map<String, List<MaterialDto>> pamDelistAndDeleteMaterialMap = new HashMap<>();
        Map<String, List<MaterialDto>> erpDelistAndDeleteMaterialMap = new HashMap<>();
        //通过pamCode和erpCode查找所有的物料（包括删除和退市的）
        List<MaterialDto> allPamCodeAndErpCodeMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pamCodeList) || CollectionUtils.isNotEmpty(erpCodeList)) {
            allPamCodeAndErpCodeMaterials.addAll(materialExtMapper.getAllPamCodeAndErpCodeMaterial(organizationIds, pamCodeList, erpCodeList));
        }
        for (MaterialDto materialDto : allPamCodeAndErpCodeMaterials) {
            String pamCode = materialDto.getPamCode();
            String erpCode = materialDto.getItemCode();
            //查询所有物料信息表中的数据
            if (Boolean.FALSE.equals(materialDto.getDeleteFlag()) && Boolean.FALSE.equals(materialDto.getDelistFlag())) {
                if (StringUtils.isNotEmpty(pamCode)) {
                    if (pamMaterialsMap.containsKey(pamCode)) {
                        pamMaterialsMap.get(pamCode).add(materialDto);
                    } else {
                        pamMaterialsMap.put(pamCode, Lists.newArrayList(materialDto));
                    }
                }
                if (StringUtils.isNotEmpty(erpCode)) {
                    if (erpMaterialsMap.containsKey(erpCode)) {
                        erpMaterialsMap.get(erpCode).add(materialDto);
                    } else {
                        erpMaterialsMap.put(erpCode, Lists.newArrayList(materialDto));
                    }
                }
            }
            //从基础信息表中查询所有退市或者已删除的物料
            if (Boolean.TRUE.equals(materialDto.getDeleteFlag()) || Boolean.TRUE.equals(materialDto.getDelistFlag())) {
                if (StringUtils.isNotEmpty(pamCode)) {
                    if (pamDelistAndDeleteMaterialMap.containsKey(pamCode)) {
                        pamDelistAndDeleteMaterialMap.get(pamCode).add(materialDto);
                    } else {
                        pamDelistAndDeleteMaterialMap.put(pamCode, Lists.newArrayList(materialDto));
                    }
                }
                if (StringUtils.isNotEmpty(erpCode)) {
                    if (erpDelistAndDeleteMaterialMap.containsKey(erpCode)) {
                        erpDelistAndDeleteMaterialMap.get(erpCode).add(materialDto);
                    } else {
                        erpDelistAndDeleteMaterialMap.put(erpCode, Lists.newArrayList(materialDto));
                    }
                }
            }
        }

        //通过figureNumber和brand和model查找所有的物料（包括删除和退市的）
        Map<String, List<MaterialDto>> materialForCheckRepeatMap = new HashMap<>();
        Map<String, List<MaterialDto>> materialForCheckAdjustRepeatMap = new HashMap<>();
        List<MaterialDto> allFigureNumberAndBrandAndModelMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(figureNumberList) || CollectionUtils.isNotEmpty(brandList) || CollectionUtils.isNotEmpty(modelList)) {
            // KUKA2022 规则下标准件的 model 忽略一些特殊符号
            allFigureNumberAndBrandAndModelMaterials.addAll(materialExtMapper.getAllFigureNumberAndBrandAndModelMaterial(organizationIds,
                    figureNumberList, brandList, new ArrayList<>()));
        }
        for (MaterialDto materialDto : allFigureNumberAndBrandAndModelMaterials) {
            //查询所有物料信息表中的数据
            if (Boolean.FALSE.equals(materialDto.getDeleteFlag()) && Boolean.FALSE.equals(materialDto.getDelistFlag())) {
                String materialForCheckRepeatKey = getMaterialForCheckRepeatKey(materialDto.getFigureNumber(), materialDto.getBrand(),
                        materialDto.getModel(), nonStandardMaterialType, materialDto.getMaterialClassification(), characterIgnoreList);
                if (materialForCheckRepeatMap.containsKey(materialForCheckRepeatKey)) {
                    materialForCheckRepeatMap.get(materialForCheckRepeatKey).add(materialDto);
                } else {
                    materialForCheckRepeatMap.put(materialForCheckRepeatKey, Lists.newArrayList(materialDto));
                }
            }
            //从基础信息表中查询所有退市或者已删除的物料
            if (Boolean.TRUE.equals(materialDto.getDeleteFlag()) || Boolean.TRUE.equals(materialDto.getDelistFlag())) {
                String materialForCheckAdjustRepeatKey = getMaterialForCheckAdjustRepeatKey(materialDto.getFigureNumber(), materialDto.getBrand(),
                        materialDto.getModel(), nonStandardMaterialType, materialDto.getMaterialClassification(), characterIgnoreList);
                if (materialForCheckAdjustRepeatMap.containsKey(materialForCheckAdjustRepeatKey)) {
                    materialForCheckAdjustRepeatMap.get(materialForCheckAdjustRepeatKey).add(materialDto);
                } else {
                    materialForCheckAdjustRepeatMap.put(materialForCheckAdjustRepeatKey, Lists.newArrayList(materialDto));
                }
            }
        }

        //通过figureNumber和brand和model查询所有物料变更表中的数据
        Map<String, List<MaterialDto>> adjustCodeForKukaCodeRuleMap = new HashMap<>();
        List<MaterialDto> allFigureNumberAndBrandAndModelAdjustMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(figureNumberList) || CollectionUtils.isNotEmpty(brandList) || CollectionUtils.isNotEmpty(modelList)) {
            // KUKA2022 规则下标准件的 model 忽略一些特殊符号
            allFigureNumberAndBrandAndModelAdjustMaterials.addAll(materialExtMapper.getAllFigureNumberAndBrandAndModelAdjustMaterial(organizationIds,
                    figureNumberList, brandList, new ArrayList<>()));
        }
        for (MaterialDto materialDto : allFigureNumberAndBrandAndModelAdjustMaterials) {
            String adjustCodeForKukaCodeRuleKey = getAdjustCodeForKukaCodeRuleKey(materialDto.getFigureNumber(), materialDto.getBrand(),
                    materialDto.getModel(), nonStandardMaterialType, materialDto.getMaterialClassification(), characterIgnoreList);
            if (adjustCodeForKukaCodeRuleMap.containsKey(adjustCodeForKukaCodeRuleKey)) {
                adjustCodeForKukaCodeRuleMap.get(adjustCodeForKukaCodeRuleKey).add(materialDto);
            } else {
                adjustCodeForKukaCodeRuleMap.put(adjustCodeForKukaCodeRuleKey, Lists.newArrayList(materialDto));
            }
        }

        //序号与引用物料map
        Map<Long, MaterialDto> forFillImportMap = new HashMap<>();
        // 获取加工分类字典值
        List<String> machinedPartTypeList = dicts.stream().map(Dict::getName).distinct().collect(Collectors.toList());

        // 唯一性校验
        List<String> nonStandardMaterials = new ArrayList<>();
        List<String> standardMaterials = new ArrayList<>();
        List<Boolean> uniqueList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto dto : designPlanDetailDtos) {
            boolean res;
            String pamCode = dto.getPamCode();
            String erpCode = dto.getErpCode();
            boolean pamCodeImportFlag = dto.isPamCodeImportFlag();
            boolean erpCodeImportFlag = dto.isErpCodeImportFlag();
            boolean notPamCodeAndNotErpCodeImportFlag = dto.isNotPamCodeAndNotErpCodeImportFlag();
            // 防止导入成功后临时生成的P码点确定重新校验出现问题
            if (pamCodeImportFlag || (StringUtils.isNotEmpty(pamCode) && !erpCodeImportFlag && !notPamCodeAndNotErpCodeImportFlag)) {
                // 有P码
                res = pamCodeCheck(storageId, dto, forFillImportMap, pamAdjustMaterialsMap, pamMaterialsMap,
                        pamDelistAndDeleteMaterialMap, organizationMap);
                dto.setPamCodeImportFlag(true);
            } else if (erpCodeImportFlag || (StringUtils.isNotEmpty(erpCode) && !notPamCodeAndNotErpCodeImportFlag)) {
                // 有E码
                res = erpCodeCheck(dto, erpMaterialsMap, storageId, materialForCheckRepeatMap, materialForCheckAdjustRepeatMap,
                        adjustCodeForKukaCodeRuleMap, nonStandardMaterialType, merielSubclassList, unitNameCodeMap, unitCodeNameMap,
                        organizationMap, orSparePartsMaskSet, machinedPartTypeList, forFillImportMap, pamAdjustMaterialsMap, pamMaterialsMap,
                        pamDelistAndDeleteMaterialMap, erpDelistAndDeleteMaterialMap, materialClassMap, isOpenDeliveryTime, characterIgnoreList);
                dto.setErpCodeImportFlag(true);
            } else {
                // P码、E码都没有
                res = erpCodeIsEmptyAndPamCodeIsEmptyCheck(storageId, dto, merielSubclassList, unitNameCodeMap, orSparePartsMaskSet,
                        nonStandardMaterialType, machinedPartTypeList, forFillImportMap, materialForCheckRepeatMap, materialForCheckAdjustRepeatMap,
                        adjustCodeForKukaCodeRuleMap, materialClassMap, organizationMap, standardMaterials, nonStandardMaterials,
                        standardMaterialsForWebInfoForWebInfo, nonStandardMaterialsForWebInfo, characterIgnoreList);
                dto.setNotPamCodeAndNotErpCodeImportFlag(true);
            }
            uniqueList.add(res);
        }
        if (uniqueList.contains(false)) {
            return false;
        }

        logger.info("enableKUKA2022MaterialCodeRule的forFillImportMap：{}", JsonUtils.toString(forFillImportMap));
        logger.info("enableKUKA2022MaterialCodeRule的designPlanDetailDtos：{}", JsonUtils.toString(designPlanDetailDtos));

        List<String> materialClassificationList = new ArrayList<>();
        List<String> codingMiddleClassList = new ArrayList<>();
        List<String> materielTypeList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto forFillDetailDto : designPlanDetailDtos) {
            MaterialDto materialDto = forFillImportMap.get(Long.parseLong(forFillDetailDto.getSerialNumber()));
            if (Objects.isNull(materialDto)) {
                // 没有引用物料
                forFillDetailDto.setUnitCode(unitNameCodeMap.get(forFillDetailDto.getUnit()));
                // 导入的物料大类、物料中类、物料小类都不能为空
                materialClassificationList.add(forFillDetailDto.getMaterialClassification());
                codingMiddleClassList.add(forFillDetailDto.getCodingMiddleClass());
                materielTypeList.add(forFillDetailDto.getMaterielType());
            } else {
                // 引用了物料
                String unitCode = "";
                if (StringUtils.isNotEmpty(materialDto.getAdjustCode())) {
                    // 引用了adjust
                    unitCode = materialDto.getUnitCode();
                    if (!unitCodeNameMap.containsKey(unitCode)) {
                        String errorMsg = String.format("引用的物料adjust表的pam编码：%s的物料的单位：%s 格式错误", materialDto.getPamCode(), unitCode);
                        forFillDetailDto.setValidResult((StringUtils.isEmpty(forFillDetailDto.getValidResult()) ? ""
                                : (forFillDetailDto.getValidResult() + "，")) + errorMsg);
                        result = false;
                    }
                } else {
                    // 引用了material
                    unitCode = materialDto.getUnit();
                    if (!unitCodeNameMap.containsKey(unitCode)) {
                        String errorMsg = String.format("引用的物料material表的pam编码：%s的物料的单位：%s 格式错误", materialDto.getPamCode(), unitCode);
                        forFillDetailDto.setValidResult((StringUtils.isEmpty(forFillDetailDto.getValidResult()) ? ""
                                : (forFillDetailDto.getValidResult() + "，")) + errorMsg);
                        result = false;
                    }
                }
                forFillDetailDto.setUnitCode(unitCode);
                forFillDetailDto.setUnit(unitCodeNameMap.get(unitCode));
                materialClassificationList.add(materialDto.getMaterialClassification());
                codingMiddleClassList.add(materialDto.getCodingMiddleclass());
                materielTypeList.add(materialDto.getMaterialType());
            }
        }
        if (!result) {
            return false;
        }

        // 是否启用物料类别映射库存分类
        String currentDateStr = DateUtil.format(new Date(), "yyyy-MM-dd");
        OrganizationCustomDict organizationCustomDict = organizationCustomDictExtMapper.queryByNameAndOrgId("是否启用物料类别映射库存分类", storageId,
                currentDateStr);
        boolean enableInventoryType = Objects.nonNull(organizationCustomDict);
        // 处理库存分类的inventoryType
        Map<String, String> inventoryTypeMap = new HashMap<>();
        if (enableInventoryType) {
            MaterialCustomDictHeaderExample customDictHeaderExample = new MaterialCustomDictHeaderExample();
            customDictHeaderExample.createCriteria().andDeletedFlagEqualTo(false).andOrganizationIdEqualTo(storageId)
                    .andCodingClassIn(materialClassificationList).andCodingMiddleclassIn(codingMiddleClassList).andCodingSubclassIn(materielTypeList);
            List<MaterialCustomDictHeader> materialCustomDictHeaders = materialCustomDictHeaderExtMapper.selectByExample(customDictHeaderExample);
            Map<String, List<Long>> materialCustomDictHeaderMap = new HashMap<>();
            List<Long> headerIdList = new ArrayList<>();
            for (MaterialCustomDictHeader customDictHeader : materialCustomDictHeaders) {
                Long headerId = customDictHeader.getId();
                String customDictHeaderKey = getMaterialCustomDictHeaderKey(customDictHeader.getCodingClass(),
                        customDictHeader.getCodingMiddleclass(), customDictHeader.getCodingSubclass());
                if (materialCustomDictHeaderMap.containsKey(customDictHeaderKey)) {
                    List<Long> valueList = materialCustomDictHeaderMap.get(customDictHeaderKey);
                    valueList.add(headerId);
                    materialCustomDictHeaderMap.put(customDictHeaderKey, valueList);
                } else {
                    materialCustomDictHeaderMap.put(customDictHeaderKey, Lists.newArrayList(headerId));
                }
                headerIdList.add(headerId);
            }
            if (MapUtils.isNotEmpty(materialCustomDictHeaderMap)) {
                // “物料类别配置”，按"库存组织+物料大中小类"配置大中小类的"库存分类"（存在且未删除、启用日期<=系统当前日期<=失效日期、属性值不为空）
                List<MaterialCustomDict> customDictList = materialCustomDictExtMapper.queryByNameAndHeaderIdList("库存分类", headerIdList,
                        currentDateStr);
                if (CollectionUtils.isNotEmpty(customDictList)) {
                    materialCustomDictHeaderMap.forEach((k, v) -> {
                        // material_custom_dict中多个headerId 对应一个value
                        String inventoryType =
                                customDictList.stream().filter(e -> v.contains(e.getHeaderId())).map(MaterialCustomDict::getValue).findFirst().orElse(null);
                        inventoryTypeMap.put(k, inventoryType);
                    });
                }
            }
        }
        logger.info("非柔性详设导入的库存分类的inventoryTypeMap：{}", JsonUtils.toString(inventoryTypeMap));

        for (MilepostDesignPlanDetailDto forFillDetailDto : designPlanDetailDtos) {
            List<String> validResultList = StringUtils.isEmpty(forFillDetailDto.getValidResult()) ? new ArrayList<>()
                    : Arrays.asList(forFillDetailDto.getValidResult().split("，"));
            MaterialDto materialDto = forFillImportMap.get(Long.parseLong(forFillDetailDto.getSerialNumber()));
            if (null != materialDto) {
                // 设置库存分类的inventoryType(本组织直接用原物料的inventoryType-也就是不新建物料的情况)
                if (Objects.equals(storageId, materialDto.getOrganizationId())) {
                    forFillDetailDto.setInventoryType(materialDto.getInventoryType());
                } else if (enableInventoryType) {
                    String inventoryType = inventoryTypeMap.get(getMaterialCustomDictHeaderKey(materialDto.getMaterialClassification(),
                            materialDto.getCodingMiddleclass(), materialDto.getMaterialType()));
                    if (StringUtils.isEmpty(inventoryType)) {
                        validResultList.add("该物料大中小类的库存分类未维护，不允许导入");
                        forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                        result = false;
                        continue;
                    }
                    forFillDetailDto.setInventoryType(inventoryType);
                }

                if (Boolean.TRUE.equals(materialDto.getAllExtend())) {
                    // 全部继承
                    forFillDetailDto.setMaterialCategory(materialDto.getMaterialCategory());
                    forFillDetailDto.setItemStatus(materialDto.getItemStatus());
                    forFillDetailDto.setBrandMaterialCode(materialDto.getBrandMaterialCode());
                } else if (StringUtils.isEmpty(materialDto.getAdjustCode()) && Boolean.TRUE.equals(materialDto.getDelistFlag())) {
                    // 部分继承：本组织有是退市但不删除，物料类型不变
                    forFillDetailDto.setMaterialCategory(materialDto.getMaterialCategory());
                    forFillDetailDto.setChartVersion(materialDto.getChartVersion());
                }
                forFillDetailDto.setPamCode(StringUtils.isNotEmpty(forFillDetailDto.getPamCode()) ? forFillDetailDto.getPamCode() :
                        materialDto.getPamCode());
                forFillDetailDto.setErpCode(materialDto.getItemCode());
                forFillDetailDto.setMaterielDescr(materialDto.getItemInfo());
                Integer materielDescrLength = getOracleLengthFromMysqlVarchar(forFillDetailDto.getMaterielDescr());
                if (materielDescrLength > 240) {
                    validResultList.add("物料描述的字段长度超过了240个字符");
                    forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                    result = false;
                }
                forFillDetailDto.setMaterialClassification(materialDto.getMaterialClassification());
                forFillDetailDto.setCodingMiddleClass(materialDto.getCodingMiddleclass());
                forFillDetailDto.setMaterielType(materialDto.getMaterialType());
                if ((MaterialCategoryEnum.ASSEMBLY_PARTS.msg().equals(forFillDetailDto.getMaterialCategory())
                        || MaterialCategoryEnum.VIRTUAL_PARTS.msg().equals(forFillDetailDto.getMaterialCategory()))) {
                    forFillDetailDto.setWhetherModel(true);
                }
                forFillDetailDto.setName(materialDto.getName());
                forFillDetailDto.setModel(materialDto.getModel());
                forFillDetailDto.setBrand(materialDto.getBrand());
                forFillDetailDto.setMachiningPartType(materialDto.getMachiningPartType());
                forFillDetailDto.setMaterial(materialDto.getMaterial());
                forFillDetailDto.setUnitWeight(materialDto.getUnitWeight());
                forFillDetailDto.setMaterialProcessing(materialDto.getMaterialProcessing());//材质处理
                forFillDetailDto.setFigureNumber(materialDto.getFigureNumber());
            } else {
                // 设置库存分类的inventoryType
                if (enableInventoryType) {
                    String inventoryType = inventoryTypeMap.get(getMaterialCustomDictHeaderKey(forFillDetailDto.getMaterialClassification(),
                            forFillDetailDto.getCodingMiddleClass(), forFillDetailDto.getMaterielType()));
                    if (StringUtils.isEmpty(inventoryType)) {
                        validResultList.add("该物料大中小类的库存分类未维护，不允许导入");
                        forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                        result = false;
                        continue;
                    }
                    forFillDetailDto.setInventoryType(inventoryType);
                }

                if ((MaterialCategoryEnum.ASSEMBLY_PARTS.msg().equals(forFillDetailDto.getMaterialCategory())
                        || MaterialCategoryEnum.VIRTUAL_PARTS.msg().equals(forFillDetailDto.getMaterialCategory()))) {
                    forFillDetailDto.setWhetherModel(true);
                }
                MaterialAdjustDetailDTO adjustDetailDTO = new MaterialAdjustDetailDTO();
                adjustDetailDTO.setFigureNumber(forFillDetailDto.getFigureNumber());
                adjustDetailDTO.setBrand(forFillDetailDto.getBrand());
                adjustDetailDTO.setModel(forFillDetailDto.getModel());
                adjustDetailDTO.setName(forFillDetailDto.getName());
                adjustDetailDTO.setMaterialClass(forFillDetailDto.getMaterialClassification());
                // 生成物料描述
                forFillDetailDto.setMaterielDescr(forFillDetailDto.isImportFindMaterialPamCodeIsNotExist() ? forFillDetailDto.getMaterielDescr()
                        : generateMaterialDesForKuka2022(adjustDetailDTO, nonStandardMaterialType));
                Integer materielDescrLength = getOracleLengthFromMysqlVarchar(forFillDetailDto.getMaterielDescr());
                if (materielDescrLength > 240) {
                    validResultList.add("物料描述的字段长度超过了240个字符");
                    forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                    result = false;
                }
                // 生成PAM编码
                forFillDetailDto.setPamCode(StringUtils.isNotEmpty(forFillDetailDto.getPamCode()) ? forFillDetailDto.getPamCode() :
                        generateMaterialPAMCode());
            }

            List<MaterialDto> fillMaterialDtoList = forFillDetailDto.getFillMaterialDtoList();
            if (CollectionUtils.isNotEmpty(fillMaterialDtoList)) {
                for (MaterialDto fillMaterialDto : fillMaterialDtoList) {
                    fillMaterialDto.setItemInfo(forFillDetailDto.getMaterielDescr());
                    fillMaterialDto.setUnit(forFillDetailDto.getUnitCode());
                    fillMaterialDto.setDelistFlag(Boolean.FALSE);
                    fillMaterialDto.setMaterialClassification(forFillDetailDto.getMaterialClassification());
                    fillMaterialDto.setCodingMiddleclass(forFillDetailDto.getCodingMiddleClass());
                    fillMaterialDto.setMaterialType(forFillDetailDto.getMaterielType());
                    fillMaterialDto.setName(forFillDetailDto.getName());
                    fillMaterialDto.setModel(forFillDetailDto.getModel());
                    fillMaterialDto.setBrand(forFillDetailDto.getBrand());
                    fillMaterialDto.setMachiningPartType(forFillDetailDto.getMachiningPartType());
                    fillMaterialDto.setMaterial(forFillDetailDto.getMaterial());
                    fillMaterialDto.setMaterialProcessing(forFillDetailDto.getMaterialProcessing());
                    fillMaterialDto.setUnitWeight(forFillDetailDto.getUnitWeight());
                    fillMaterialDto.setFigureNumber(forFillDetailDto.getFigureNumber());
                    if (Objects.equals(fillMaterialDto.getItemType(), "P")) {
                        fillMaterialDto.setMaterialCategory("外购物料");
                        fillMaterialDto.setMaterialAttribute("外购件");
                    } else if (Objects.equals(fillMaterialDto.getItemType(), "PH")) {
                        fillMaterialDto.setMaterialCategory("虚拟件");
                    } else if (Objects.equals(fillMaterialDto.getItemType(), "SA")) {
                        fillMaterialDto.setMaterialCategory("装配件");
                    } else if (Objects.equals(fillMaterialDto.getItemType(), "FG")) {
                        fillMaterialDto.setMaterialCategory("成品");
                    } else if (Objects.equals(fillMaterialDto.getItemType(), "SA-P")) {
                        fillMaterialDto.setMaterialCategory("看板物料");
                        fillMaterialDto.setMaterialAttribute("看板件");
                    }
                }
            }
        }

        if (result) {
            this.saveMilepostDesignDetailMiddle(designPlanDetailDtos, markId, storageId);
        }
        return result;
    }

    private boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    private String getErpCodeRuleKey(String materialClassification, String codingMiddleclass, String materialType) {
        return String.format("getNotFlexibleErpCodeRuleKey_%s_%s_%s", materialClassification, codingMiddleclass, materialType);
    }

    private Integer getOracleLengthFromMysqlVarchar(String varchar) {
        if (StringUtils.isEmpty(varchar)) {
            return 0;
        }
        Integer length = varchar.length();

        StringBuffer sb = new StringBuffer();
        String pattern = "[\\u4e00-\\u9fa5]+";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(varchar);
        while (m.find()) {
            sb.append(m.group());
        }
        if (sb.length() == 0) {
            return length;
        }
        // oracle数据库的varchar2 每个汉字将占用3个byte
        return length - sb.length() + sb.length() * 3;
    }

    // 详设导入E码校验
    private boolean erpCodeCheck(MilepostDesignPlanDetailDto dto, Map<String, List<MaterialDto>> erpMaterialsMap,
                                 Long storageId, Map<String, List<MaterialDto>> materialForCheckRepeatMap,
                                 Map<String, List<MaterialDto>> materialForCheckAdjustRepeatMap,
                                 Map<String, List<MaterialDto>> adjustCodeForKukaCodeRuleMap, List<String> nonStandardMaterialType,
                                 List<MerielSubclassVO> merielSubclassList, Map<String, String> unitNameCodeMap, Map<String, String> unitCodeNameMap,
                                 Map<Long, String> organizationMap, Set<String> orSparePartsMaskSet, List<String> machinedPartTypeList,
                                 Map<Long, MaterialDto> forFillImportMap, Map<String, List<MaterialDto>> pamAdjustMaterialsMap,
                                 Map<String, List<MaterialDto>> pamMaterialsMap, Map<String, List<MaterialDto>> pamDelistAndDeleteMaterialMap,
                                 Map<String, List<MaterialDto>> erpDelistAndDeleteMaterialMap,
                                 Map<String, MerielSubclassVO> materialClassMap, Boolean isOpenDeliveryTime, List<String> characterIgnoreList) {
        boolean result = true;
        String erpCode = dto.getErpCode();
        List<String> validResultList = StringUtils.isEmpty(dto.getValidResult()) ? new ArrayList<>()
                : Arrays.asList(dto.getValidResult().split("，"));
        //查询物料信息表中的数据
        List<MaterialDto> materialDtoList = erpMaterialsMap.get(erpCode);
        if (CollectionUtils.isEmpty(materialDtoList)) {
            validResultList.add("Erp编码:[" + erpCode + "]在物料表中不存在");
            dto.setValidResult(Joiner.on("，").join(validResultList));
            return false;
        }

        //筛选当前库存组织的物料基础表中的数据
        List<MaterialDto> currentMaterialDtoList = new ArrayList<>();
        List<MaterialDto> otherMaterialDtoList = new ArrayList<>();
        for (MaterialDto materialDto : materialDtoList) {
            if (Objects.equals(materialDto.getOrganizationId(), storageId)) {
                currentMaterialDtoList.add(materialDto);
            } else {
                otherMaterialDtoList.add(materialDto);
            }
        }

        //从基础信息表中查询退市或者已删除的物料
        List<MaterialDto> erpDelistAndDeleteMaterialDtos = erpDelistAndDeleteMaterialMap.get(erpCode);
        List<MaterialDto> currentErpDelistAndDeleteMaterialDtos = new ArrayList<>();
        List<MaterialDto> otherErpDelistAndDeleteMaterialDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(erpDelistAndDeleteMaterialDtos)) {
            for (MaterialDto materialDto : erpDelistAndDeleteMaterialDtos) {
                if (Objects.equals(materialDto.getOrganizationId(), storageId)) {
                    currentErpDelistAndDeleteMaterialDtos.add(materialDto);
                } else {
                    otherErpDelistAndDeleteMaterialDtos.add(materialDto);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(currentMaterialDtoList)) {
            // 如果按ERP编码在当前库存组织136找到多条数据
            if (currentMaterialDtoList.size() > 1) {
                validResultList.add("当前库存组织找到多条" + "[" + erpCode + "]" + "的数据，请联系系统管理员");
                dto.setValidResult(Joiner.on("，").join(validResultList));
                return false;
            }
            MaterialDto currentMaterialDto = currentMaterialDtoList.get(0);
            dto.setImportFindCurrentMaterialByErpCode(true);
            String pCode = currentMaterialDto.getPamCode();
            if (StringUtils.isNotEmpty(pCode)) {
                // 如果按ERP编码在当前库存组织136找到一条数据，并且这条数据PAM编码已存在：和走PAM编码导入的逻辑一样
                dto.setPamCode(pCode);
                // 走P码逻辑
                result = pamCodeCheck(storageId, dto, forFillImportMap, pamAdjustMaterialsMap, pamMaterialsMap,
                        pamDelistAndDeleteMaterialMap, organizationMap);
            } else {
                String newPamCode = StringUtils.isNotEmpty(dto.getPamCode()) ? dto.getPamCode() : generateMaterialPAMCode();
                if (CollectionUtils.isNotEmpty(otherMaterialDtoList)) {
                    MaterialDto otherMaterialDto = otherMaterialDtoList.stream().filter(e -> StringUtils.isNotEmpty(e.getPamCode()))
                            .findFirst().orElse(null);
                    if (Objects.nonNull(otherMaterialDto)) {
                        String materialCategory = dto.getMaterialCategory();
                        String itemType = currentMaterialDto.getItemType();
                        if (Objects.equals("P", itemType)) {
                            if (!Objects.equals("外购物料", materialCategory)) {
                                result = false;
                                validResultList.add("本组织物料类型为【外购物料】与导入的物料类型不一致");
                            }
                        } else if (Objects.equals("SA", itemType)) {
                            if (!Objects.equals("装配件", materialCategory)) {
                                result = false;
                                validResultList.add("本组织物料类型为【装配件】与导入的物料类型不一致");
                            }
                        } else if (Objects.equals("SA-P", itemType)) {
                            if (!Objects.equals("看板物料", materialCategory)) {
                                result = false;
                                validResultList.add("本组织物料类型为【看板物料】与导入的物料类型不一致");
                            }
                        } else if (Objects.equals("PH", itemType)) {
                            if (!Objects.equals("虚拟件", materialCategory)) {
                                result = false;
                                validResultList.add("本组织物料类型为【虚拟件】与导入的物料类型不一致");
                            }
                        } else {
                            result = false;
                            validResultList.add("本组织物料类型不属于【外购物料】、【装配件】、【看板物料】、【虚拟件】，不允许引用该E码");
                        }
                        if (!result) {
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }

                        // 只E导入，当本组织有E没P，还需考虑其它组织有E有P的情况,即走 它库存组织（如137）找到一条数据，并且这条数据PAM编码已存在 的逻辑
                        dto.setImportFindMaterialPamCodeIsNotExist(true);
                        dto.setPamCode(otherMaterialDto.getPamCode());
                        // 走P码逻辑
                        result = pamCodeCheck(storageId, dto, forFillImportMap, pamAdjustMaterialsMap, pamMaterialsMap,
                                pamDelistAndDeleteMaterialMap, organizationMap);
                        return result;
                    } else {
                        // 136有数据没P，137有数据也没P，则137也要补充数据
                        List<MaterialDto> fillMaterialDtoList = new ArrayList<>();
                        for (MaterialDto materialDto : otherMaterialDtoList) {
                            materialDto.setPamCode(newPamCode);
                            fillMaterialDtoList.add(materialDto);
                        }
                        dto.setFillMaterialDtoList(fillMaterialDtoList);
                    }
                }

                // 如果按ERP编码在当前库存组织136找到一条数据，并且这条数据PAM编码不存在：更新物料基础表136本身数据，136增加P码（导入的字段）
                // 按照物料大类，判断属于标准件/非标件，经过"KUKA2022"物料唯一性的校验
                String materialCategory = dto.getMaterialCategory();
                String itemType = currentMaterialDto.getItemType();
                if (Objects.equals("P", itemType)) {
                    if (!Objects.equals("外购物料", materialCategory)) {
                        result = false;
                        validResultList.add("本组织物料类型为【外购物料】与导入的物料类型不一致");
                    }
                } else if (Objects.equals("SA", itemType)) {
                    if (!Objects.equals("装配件", materialCategory)) {
                        result = false;
                        validResultList.add("本组织物料类型为【装配件】与导入的物料类型不一致");
                    }
                } else if (Objects.equals("SA-P", itemType)) {
                    if (!Objects.equals("看板物料", materialCategory)) {
                        result = false;
                        validResultList.add("本组织物料类型为【看板物料】与导入的物料类型不一致");
                    }
                } else if (Objects.equals("PH", itemType)) {
                    if (!Objects.equals("虚拟件", materialCategory)) {
                        result = false;
                        validResultList.add("本组织物料类型为【虚拟件】与导入的物料类型不一致");
                    }
                } else {
                    result = false;
                    validResultList.add("本组织物料类型不属于【外购物料】、【装配件】、【看板物料】、【虚拟件】，不允许引用该E码");
                }
                if (!result) {
                    logger.info("erpCodeCheck的dto：{}", JsonUtils.toString(dto));
                    logger.info("erpCodeCheck的currentMaterialDto：{}", JsonUtils.toString(currentMaterialDto));
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }

                String materialClassification = dto.getMaterialClassification();
                if (nonStandardMaterialType.contains(materialClassification)) {
                    result = checkDesignPlanDetailErpCodeQuotable(dto, dto.getFigureNumber(), null, null, validResultList,
                            materialForCheckRepeatMap, materialForCheckAdjustRepeatMap, adjustCodeForKukaCodeRuleMap, nonStandardMaterialType,
                            merielSubclassList, unitNameCodeMap, organizationMap, orSparePartsMaskSet, machinedPartTypeList, materialClassMap,
                            characterIgnoreList);
                    dto.setImportMaterialType(KukaMaterialType.NON_STANDARD_MATERIAL.getCode());
                } else {
                    String brandName = dto.getBrand();
                    result = checkDesignPlanDetailErpCodeQuotable(dto, null, brandName, dto.getModel(), validResultList,
                            materialForCheckRepeatMap, materialForCheckAdjustRepeatMap, adjustCodeForKukaCodeRuleMap, nonStandardMaterialType,
                            merielSubclassList, unitNameCodeMap, organizationMap, orSparePartsMaskSet, machinedPartTypeList, materialClassMap,
                            characterIgnoreList);
                }
                if (!result) {
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }

                if (CollectionUtils.isNotEmpty(erpDelistAndDeleteMaterialDtos)) {
                    // E码查询已退市未删除并且P码已存在的物料，如果有，报错"XX组织XX编码已退市"
                    MaterialDto erpDelistAndDeleteMaterialDto = erpDelistAndDeleteMaterialDtos.stream()
                            .filter(e -> Boolean.FALSE.equals(e.getDeleteFlag()) && Boolean.TRUE.equals(e.getDelistFlag())
                                    && StringUtils.isNotEmpty(e.getPamCode())).findFirst().orElse(null);
                    if (Objects.nonNull(erpDelistAndDeleteMaterialDto)) {
                        boolean equals = Objects.equals(erpDelistAndDeleteMaterialDto.getOrganizationId(), storageId);
                        validResultList.add(equals ? "本" : "其他" + "组织ERP物料编码:[" + erpCode + "]已退市");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                }

                dto.setImportFindMaterialPamCodeIsNotExist(true);
                dto.setErpCodeSource(2);
                dto.setPamCode(newPamCode);
                dto.setMaterielDescr(currentMaterialDto.getItemInfo());
                result = setDtoByErpCodeIsNotEmpty(currentMaterialDto, dto, unitCodeNameMap, isOpenDeliveryTime);

            }
        } else {
            if (CollectionUtils.isNotEmpty(currentErpDelistAndDeleteMaterialDtos)) {
                // 本组织mat退市未删除 + 其它组织mat有E无P未退市未删除，的数据，就报错"本组织已存在退市的ERP物料编码XX"
                boolean currentErpDelistAndDeleteCheck = currentErpDelistAndDeleteMaterialDtos.stream()
                        .anyMatch(e -> Boolean.FALSE.equals(e.getDeleteFlag()) && Boolean.TRUE.equals(e.getDelistFlag()));
                if (currentErpDelistAndDeleteCheck) {
                    validResultList.add("本组织ERP物料编码:[" + erpCode + "]已退市");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            }

            // 1.如果其它组织多条，并且存在1条以上P码存在的，走c
            // 2.如果其它组织多条，都不存在P码，走e（补充全部其它的字段）
            String pCode = otherMaterialDtoList.stream().map(MaterialDto::getPamCode).filter(StringUtils::isNotEmpty).findFirst().orElse(null);
            // 如果按ERP编码在其它库存组织（如137）找到一条数据，并且这条数据PAM编码已存在： 中间表&变更表取基础表的数据，和PAM编码导入逻辑一致，-新增物料基础表到136
            if (StringUtils.isNotEmpty(pCode)) {
                dto.setPamCode(pCode);
                // 走P码逻辑
                result = pamCodeCheck(storageId, dto, forFillImportMap, pamAdjustMaterialsMap, pamMaterialsMap,
                        pamDelistAndDeleteMaterialMap, organizationMap);
            } else {
                // 如果按ERP编码在其它库存组织（如137）找到一条数据，并且这条数据PAM编码不存在：新增物料基础表136本身数据，
                // 137增加P码、部分继承的字段（上线时，要根据136修复历史137相同P码部分继承的字段）
                // 按照物料大类，判断属于标准件/非标件，经过"KUKA2022"物料唯一性的校验
                String materialClassification = dto.getMaterialClassification();
                if (nonStandardMaterialType.contains(materialClassification)) {
                    result = checkDesignPlanDetailErpCodeQuotable(dto, dto.getFigureNumber(), null, null, validResultList,
                            materialForCheckRepeatMap, materialForCheckAdjustRepeatMap, adjustCodeForKukaCodeRuleMap, nonStandardMaterialType,
                            merielSubclassList, unitNameCodeMap, organizationMap, orSparePartsMaskSet, machinedPartTypeList, materialClassMap,
                            characterIgnoreList);
                    dto.setImportMaterialType(KukaMaterialType.NON_STANDARD_MATERIAL.getCode());
                } else {
                    String brandName = dto.getBrand();
                    result = checkDesignPlanDetailErpCodeQuotable(dto, null, brandName, dto.getModel(), validResultList,
                            materialForCheckRepeatMap, materialForCheckAdjustRepeatMap, adjustCodeForKukaCodeRuleMap, nonStandardMaterialType,
                            merielSubclassList, unitNameCodeMap, organizationMap, orSparePartsMaskSet, machinedPartTypeList, materialClassMap,
                            characterIgnoreList);
                }
                if (!result) {
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }

                if (CollectionUtils.isNotEmpty(erpDelistAndDeleteMaterialDtos)) {
                    // E码查询已退市未删除并且P码已存在的物料，如果有，报错"XX组织XX编码已退市"
                    MaterialDto erpDelistAndDeleteMaterialDto = erpDelistAndDeleteMaterialDtos.stream()
                            .filter(e -> Boolean.FALSE.equals(e.getDeleteFlag()) && Boolean.TRUE.equals(e.getDelistFlag())
                                    && StringUtils.isNotEmpty(e.getPamCode())).findFirst().orElse(null);
                    if (Objects.nonNull(erpDelistAndDeleteMaterialDto)) {
                        boolean equals = Objects.equals(erpDelistAndDeleteMaterialDto.getOrganizationId(), storageId);
                        validResultList.add(equals ? "本" : "其他" + "组织ERP物料编码:[" + erpCode + "]已退市");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                }

                String newPamCode = StringUtils.isNotEmpty(dto.getPamCode()) ? dto.getPamCode() : generateMaterialPAMCode();
                dto.setImportFindMaterialPamCodeIsNotExist(true);
                dto.setPamCode(newPamCode);
                dto.setErpCodeSource(2);
                List<MaterialDto> fillMaterialDtoList = new ArrayList<>();
                for (MaterialDto materialDto : otherMaterialDtoList) {
                    materialDto.setPamCode(newPamCode);
                    fillMaterialDtoList.add(materialDto);
                }
                dto.setFillMaterialDtoList(fillMaterialDtoList);
                dto.setMaterielDescr(fillMaterialDtoList.get(0).getItemInfo());
                result = setDtoByErpCodeIsNotEmpty(fillMaterialDtoList.get(0), dto, unitCodeNameMap, isOpenDeliveryTime);
            }
        }

        return result;
    }

    // 详设导入P码校验
    private boolean pamCodeCheck(Long storageId, MilepostDesignPlanDetailDto dto, Map<Long, MaterialDto> forFillImportMap,
                                 Map<String, List<MaterialDto>> pamAdjustMaterialsMap, Map<String, List<MaterialDto>> pamMaterialsMap,
                                 Map<String, List<MaterialDto>> pamDelistAndDeleteMaterialMap, Map<Long, String> organizationMap) {
        String pamCode = dto.getPamCode();
        String materialCategory = dto.getMaterialCategory();
        String serialNumber = dto.getSerialNumber();
        List<String> validResultList = StringUtils.isEmpty(dto.getValidResult()) ? new ArrayList<>()
                : Arrays.asList(dto.getValidResult().split("，"));

        //查询物料变更表中的数据
        List<MaterialDto> adjustMaterials = pamAdjustMaterialsMap.get(pamCode);
        //查询物料信息表中的数据
        List<MaterialDto> materials = pamMaterialsMap.get(pamCode);
        //筛选当前库存组织的物料基础表中的数据
        List<MaterialDto> currentMaterials = new ArrayList<>();
        List<MaterialDto> otherMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(materials)) {
            for (MaterialDto materialDto : materials) {
                if (Objects.equals(materialDto.getOrganizationId(), storageId)) {
                    currentMaterials.add(materialDto);
                } else {
                    otherMaterials.add(materialDto);
                }
            }
        }
        //从基础信息表中查询退市或者已删除的物料
        List<MaterialDto> delistAndDeleteMaterialDtos = pamDelistAndDeleteMaterialMap.get(pamCode);
        List<MaterialDto> currentDelistAndDeleteMaterialDtos = new ArrayList<>();
        List<MaterialDto> otherDelistAndDeleteMaterialDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(delistAndDeleteMaterialDtos)) {
            for (MaterialDto materialDto : delistAndDeleteMaterialDtos) {
                if (Objects.equals(materialDto.getOrganizationId(), storageId)) {
                    currentDelistAndDeleteMaterialDtos.add(materialDto);
                } else {
                    otherDelistAndDeleteMaterialDtos.add(materialDto);
                }
            }
        }
        if (ListUtils.isEmpty(adjustMaterials) && ListUtils.isEmpty(materials)) {
            validResultList.add("pam编码:[" + pamCode + "]在物料变更表和物料信息表中都不存在");
            dto.setValidResult(Joiner.on("，").join(validResultList));
            return false;
        }

        if (ListUtils.isNotEmpty(adjustMaterials)) {
            //adjust表中有数据的情况校验
            //筛选当前库存组织的物料变更表中的数据
            List<MaterialDto> currentAdjustMaterials = new ArrayList<>();
            List<MaterialDto> otherAdjustMaterials = new ArrayList<>();
            for (MaterialDto adjustMaterial : adjustMaterials) {
                if (Objects.equals(adjustMaterial.getOrganizationId(), storageId)) {
                    currentAdjustMaterials.add(adjustMaterial);
                } else {
                    otherAdjustMaterials.add(adjustMaterial);
                }
            }
            //如果当前组织物料变更表中有数据时
            if (ListUtils.isNotEmpty(currentAdjustMaterials)) {
                List<MaterialDto> currentDtoList = currentAdjustMaterials.stream().filter(e ->
                        !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(currentDtoList)) {
                    //本组织adj有(有单据状态！="审批通过")
                    for (MaterialDto mDto : currentDtoList) {
                        validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                    }
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
                //如果大于一个就报错
                if (currentAdjustMaterials.size() > 1) {
                    List<String> adjustCodeList = currentAdjustMaterials.stream().map(MaterialDto::getAdjustCode).collect(Collectors.toList());
                    validResultList.add("pam编码:[" + pamCode + "]对应的物料有多个单据:[+" + adjustCodeList.toString() + "]在同时导入,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    if (currentMaterials.size() > 1) {
                        validResultList.add("pam编码:[" + pamCode + "]对应的物料在本组织中存在多个,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    MaterialDto materialDto = currentMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                                + "]类型不一致, 本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                } else if (ListUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    if (CollectionUtils.isNotEmpty(otherAdjustMaterials)) {
                        if (CollectionUtils.isNotEmpty(otherMaterials)) {
                            List<String> delistFlagPamCodeList =
                                    currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                            .map(MaterialDto::getPamCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                                //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                                validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                        } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                            validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        } else {
                            List<String> delistFlagPamCodeList =
                                    currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                            .map(MaterialDto::getPamCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                                //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                                validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                    !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(dtoList)) {
                                //本组织adj有 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                                for (MaterialDto mDto : dtoList) {
                                    validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                            "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                                }
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            forFillImportMap.put(Long.parseLong(serialNumber), otherAdjustMaterials.get(0));
                        }
                    } else {
                        if (CollectionUtils.isNotEmpty(otherMaterials)) {
                            List<String> delistFlagPamCodeList =
                                    currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                            .map(MaterialDto::getPamCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                                //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                                validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                        } else {
                            //如果通过pam编码查到本组织已经退市或删除的 报错
                            validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                    }
                } else {
                    // material 全部都没有
                    MaterialDto materialDto = currentAdjustMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                                + "]类型不一致, 本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                }
            } else {
                //本组织adj无，其他adj有, 本组织mat有值
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    if (currentMaterials.size() > 1) {
                        validResultList.add("pam编码:[" + pamCode + "]对应的物料在本组织中存在多个,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    MaterialDto materialDto = currentMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                                + "]类型不一致, 本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                } else if (ListUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    //本组织adj无，其他adj有, 本组织mat退市或删除
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        List<String> delistFlagPamCodeList =
                                currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                        .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        //如果通过pam编码查到本组织已经退市或删除的 报错
                        validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    } else {
                        List<String> delistFlagPamCodeList =
                                currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                        .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto mDto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                            }
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(serialNumber), otherAdjustMaterials.get(0));
                    }
                } else {
                    //本组织adj无，其他adj有, 本组织mat无
                    if (ListUtils.isNotEmpty(otherMaterials)) {
                        forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        //如果通过pam编码查到本组织已经退市或删除的 报错
                        validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    } else {
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat无 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto mDto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                            }
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(serialNumber), otherAdjustMaterials.get(0));
                    }
                }
            }
        } else {
            // adjust没有数据时(本组织adj无，其他adj无)
            // 本组织adj无，其他adj无，本组织mat有
            if (ListUtils.isNotEmpty(currentMaterials)) {
                if (currentMaterials.size() > 1) {
                    validResultList.add("pam编码:[" + pamCode + "]对应的物料在本组织中存在多个,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
                MaterialDto materialDto = currentMaterials.get(0);
                if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                    materialDto.setAllExtend(true);
                    forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                } else {
                    //物料类型不一致时
                    validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                            + "]类型不一致, " + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                //本组织adj无，其他adj无, 本组织mat退市或删除
                if (CollectionUtils.isNotEmpty(otherMaterials)) {
                    List<String> delistFlagPamCodeList =
                            currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                    .map(MaterialDto::getPamCode).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                        //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                        validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                    //如果通过pam编码查到本组织已经退市或删除的 报错
                    validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            } else {
                //本组织adj无，其他adj无, 本组织mat无
                if (CollectionUtils.isNotEmpty(otherMaterials)) {
                    forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                    //如果通过pam编码查到本组织已经退市或删除的 报错
                    validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            }
        }
        dto.setValidResult(Joiner.on("，").join(validResultList));

        return true;
    }

    // 详设导入没P、没E校验
    private boolean erpCodeIsEmptyAndPamCodeIsEmptyCheck(Long storageId, MilepostDesignPlanDetailDto dto,
                                                         List<MerielSubclassVO> merielSubclassList, Map<String, String> unitNameCodeMap,
                                                         Set<String> orSparePartsMaskSet, List<String> nonStandardMaterialType,
                                                         List<String> machinedPartTypeList, Map<Long, MaterialDto> forFillImportMap,
                                                         Map<String, List<MaterialDto>> materialForCheckRepeatMap,
                                                         Map<String, List<MaterialDto>> materialForCheckAdjustRepeatMap,
                                                         Map<String, List<MaterialDto>> adjustCodeForKukaCodeRuleMap,
                                                         Map<String, MerielSubclassVO> materialClassMap, Map<Long, String> organizationMap,
                                                         List<String> standardMaterials, List<String> nonStandardMaterials,
                                                         List<String> standardMaterialsForWebInfoForWebInfo,
                                                         List<String> nonStandardMaterialsForWebInfo, List<String> characterIgnoreList) {
        boolean result = true;
        List<String> validResultList = StringUtils.isEmpty(dto.getValidResult()) ? new ArrayList<>()
                : Arrays.asList(dto.getValidResult().split("，"));
        String serialNumber = dto.getSerialNumber();
        String materialClassification = dto.getMaterialClassification();
        if (StringUtils.isEmpty(materialClassification)) {
            validResultList.add("物料大类不能为空");
            dto.setValidResult(Joiner.on("，").join(validResultList));
            return false;
        } else if (merielSubclassList.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(materialClassification))) {
            result = false;
            validResultList.add("物料大类:[" + materialClassification + "]尚未在物料编码规则:[" + MaterialCodeRuleEnum.KUKA2022.getName() + "]中维护,请先维护");
        }
        if (StringUtils.isEmpty(dto.getCodingMiddleClass())) {
            result = false;
            validResultList.add("物料中类不能为空");
        } else if (merielSubclassList.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(materialClassification)
                && materialClass.getMiddileValue().equals(dto.getCodingMiddleClass()))) {
            result = false;
            validResultList.add("此物料中类不在填写的大类中");
        }
        if (StringUtils.isEmpty(dto.getMaterielType())) {
            result = false;
            validResultList.add("物料小类不能为空");
        } else {
            if (merielSubclassList.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(materialClassification)
                    && materialClass.getMiddileValue().equals(dto.getCodingMiddleClass()) && materialClass.getValue().equals(dto.getMaterielType()))) {
                result = false;
                validResultList.add("此物料小类不在填写的大类或中类中");
            }
        }
        if (StringUtils.isEmpty(dto.getUnit())) {
            result = false;
            validResultList.add("单位不能为空");
        } else if (!unitNameCodeMap.containsKey(dto.getUnit())) {
            result = false;
            validResultList.add("单位必须为字典项measurement_unit的值");
        }
        if (StringUtils.isEmpty(dto.getName())) {
            result = false;
            validResultList.add("名称不能为空");
        } else if (dto.getName().length() > 150) {
            result = false;
            validResultList.add("名称字符长度不能超过150");
        }
        if (StringUtils.isEmpty(dto.getOrSparePartsMask())) {
            result = false;
            validResultList.add("备件标识不能为空");
        } else if (!orSparePartsMaskSet.contains(dto.getOrSparePartsMask())) {
            result = false;
            validResultList.add("备件标识必须为组织参数中配置的备件标识中的值");
        }
        //非标件校验
        if (nonStandardMaterialType.contains(materialClassification)) {
            //「加工件分类」字段只能选择字典项中的值且不能为空
            if (StringUtils.isEmpty(dto.getMachiningPartType())) {
                result = false;
                validResultList.add("非标件,加工分类不能为空");
            } else if (!machinedPartTypeList.contains(dto.getMachiningPartType())) {
                result = false;
                validResultList.add("非标件,加工分类只能选择字典项中的值");
            }
            // 图号必填
            if (StringUtils.isEmpty(dto.getFigureNumber())) {
                result = false;
                validResultList.add("非标件,图号不能为空");
            } else {
                if (CollectionUtils.isNotEmpty(nonStandardMaterials) && nonStandardMaterials.contains(dto.getFigureNumber())) {
                    result = false;
                    validResultList.add("所导入数据中,存在多条图号:[" + dto.getFigureNumber() + "]的数据");
                } else {
                    nonStandardMaterials.add(dto.getFigureNumber());
                }
            }
            if (StringUtils.isEmpty(dto.getChartVersion()) || !org.apache.commons.lang3.StringUtils.isNumeric(dto.getChartVersion())) {
                result = false;
                validResultList.add("非标件,图纸版本号不能为空,或只能为数字");
            } else if (dto.getChartVersion().length() > 2) {
                result = false;
                validResultList.add("图纸版本号字符长度不能超过2");
            }
            if (StringUtils.isEmpty(dto.getMaterial())) {
                result = false;
                validResultList.add("非标件,材质不能为空");
            }
            if (StringUtils.isEmpty(dto.getMaterialProcessing())) {
                result = false;
                validResultList.add("非标件,表面处理不能为空");
            }
            if (null == dto.getUnitWeight()) {
                result = false;
                validResultList.add("非标件,单位重量不能为空");
            }
        } else {
            //标准件校验
            if (StringUtils.isEmpty(dto.getModel())) {
                result = false;
                validResultList.add("型号/规格不能为空");
            } else {
                if (dto.getModel().length() > 150) {
                    result = false;
                    validResultList.add("型号/规格字符长度不能超过150");
                }
            }
            if (StringUtils.isEmpty(dto.getBrand())) {
                result = false;
                validResultList.add("品牌不能为空");
            } else {
                if (CollectionUtils.isNotEmpty(standardMaterials) && standardMaterials.contains(dto.getBrand() + ":"
                        + removeCharacterIgnoreList(dto.getModel(), characterIgnoreList))) {
                    result = false;
                    validResultList.add("所导入数据中,存在多条品牌:[" + dto.getBrand() + "] + 型号/规格:[" + dto.getModel() + "的数据");
                } else {
                    standardMaterials.add(dto.getBrand() + ":" + removeCharacterIgnoreList(dto.getModel(), characterIgnoreList));
                }
            }
            if (StringUtils.isNotEmpty(dto.getChartVersion()) && !org.apache.commons.lang3.StringUtils.isNumeric(dto.getChartVersion())) {
                result = false;
                validResultList.add("图纸版本号只能为数字");
            }
        }
        dto.setValidResult(Joiner.on("，").join(validResultList));


        if (result) {
            //非标件校验
            if (nonStandardMaterialType.contains(materialClassification)) {
                result = checkDesignPlanDetailQuotable(dto, dto.getFigureNumber(), null, null, storageId, validResultList,
                        forFillImportMap, materialForCheckRepeatMap, materialForCheckAdjustRepeatMap, adjustCodeForKukaCodeRuleMap,
                        nonStandardMaterialType, materialClassMap, organizationMap, standardMaterialsForWebInfoForWebInfo,
                        nonStandardMaterialsForWebInfo, characterIgnoreList);
                dto.setImportMaterialType(KukaMaterialType.NON_STANDARD_MATERIAL.getCode());
            } else {
                String brandName = dto.getBrand();
                result = checkDesignPlanDetailQuotable(dto, null, brandName, dto.getModel(), storageId, validResultList,
                        forFillImportMap, materialForCheckRepeatMap, materialForCheckAdjustRepeatMap, adjustCodeForKukaCodeRuleMap,
                        nonStandardMaterialType, materialClassMap, organizationMap, standardMaterialsForWebInfoForWebInfo,
                        nonStandardMaterialsForWebInfo, characterIgnoreList);
                if (!result) {
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
                if (Objects.isNull(forFillImportMap.get(Long.parseLong(serialNumber)))) {
                    result = false;
                    validResultList.add("标准件需要物料新增,不允许通过详设导入新增");
                }
            }
            dto.setValidResult(Joiner.on("，").join(validResultList));
        }

        return result;
    }

    private Boolean checkDesignPlanDetailErpCodeQuotable(MilepostDesignPlanDetailDto detailDto, String figureNumber,
                                                         String brand, String model, List<String> validResultList,
                                                         Map<String, List<MaterialDto>> materialForCheckRepeatMap,
                                                         Map<String, List<MaterialDto>> materialForCheckAdjustRepeatMap,
                                                         Map<String, List<MaterialDto>> adjustCodeForKukaCodeRuleMap,
                                                         List<String> nonStandardMaterialType,
                                                         List<MerielSubclassVO> merielSubclassList, Map<String, String> unitNameCodeMap,
                                                         Map<Long, String> organizationMap, Set<String> orSparePartsMaskSet,
                                                         List<String> machinedPartTypeList,
                                                         Map<String, MerielSubclassVO> materialClassMap, List<String> characterIgnoreList) {
        boolean result = true;
        String materialClassification = detailDto.getMaterialClassification();
        if (StringUtils.isEmpty(materialClassification)) {
            validResultList.add("物料大类不能为空");
            return false;
        } else if (merielSubclassList.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(materialClassification))) {
            result = false;
            validResultList.add("物料大类:[" + materialClassification + "]尚未在物料编码规则:[" + MaterialCodeRuleEnum.KUKA2022.getName() + "]中维护,请先维护");
        }
        if (StringUtils.isEmpty(detailDto.getCodingMiddleClass())) {
            result = false;
            validResultList.add("物料中类不能为空");
        } else if (merielSubclassList.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(materialClassification)
                && materialClass.getMiddileValue().equals(detailDto.getCodingMiddleClass()))) {
            result = false;
            validResultList.add("此物料中类不在填写的大类中");
        }
        if (StringUtils.isEmpty(detailDto.getMaterielType())) {
            result = false;
            validResultList.add("物料小类不能为空");
        } else {
            if (merielSubclassList.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(materialClassification)
                    && materialClass.getMiddileValue().equals(detailDto.getCodingMiddleClass()) && materialClass.getValue().equals(detailDto.getMaterielType()))) {
                result = false;
                validResultList.add("此物料小类不在填写的大类或中类中");
            }
        }
        if (StringUtils.isEmpty(detailDto.getUnit())) {
            result = false;
            validResultList.add("单位不能为空");
        } else if (!unitNameCodeMap.containsKey(detailDto.getUnit())) {
            result = false;
            validResultList.add("单位必须为字典项measurement_unit的值");
        }
        if (StringUtils.isEmpty(detailDto.getName())) {
            result = false;
            validResultList.add("名称不能为空");
        } else if (detailDto.getName().length() > 150) {
            result = false;
            validResultList.add("名称字符长度不能超过150");
        }
        if (StringUtils.isEmpty(detailDto.getOrSparePartsMask())) {
            result = false;
            validResultList.add("备件标识不能为空");
        } else if (!orSparePartsMaskSet.contains(detailDto.getOrSparePartsMask())) {
            result = false;
            validResultList.add("备件标识必须为组织参数中配置的备件标识中的值");
        }
        //非标件校验
        if (nonStandardMaterialType.contains(materialClassification)) {
            //「加工件分类」字段只能选择字典项中的值且不能为空
            if (StringUtils.isEmpty(detailDto.getMachiningPartType())) {
                result = false;
                validResultList.add("非标件,加工分类不能为空");
            } else if (!machinedPartTypeList.contains(detailDto.getMachiningPartType())) {
                result = false;
                validResultList.add("非标件,加工分类只能选择字典项中的值");
            }
            // 图号必填
            if (StringUtils.isEmpty(detailDto.getFigureNumber())) {
                result = false;
                validResultList.add("非标件,图号不能为空");
            }
            if (StringUtils.isEmpty(detailDto.getChartVersion()) || !org.apache.commons.lang3.StringUtils.isNumeric(detailDto.getChartVersion())) {
                result = false;
                validResultList.add("非标件,图纸版本号不能为空,或只能为数字");
            } else if (detailDto.getChartVersion().length() > 2) {
                result = false;
                validResultList.add("图纸版本号字符长度不能超过2");
            }
            if (StringUtils.isEmpty(detailDto.getMaterial())) {
                result = false;
                validResultList.add("非标件,材质不能为空");
            }
            if (StringUtils.isEmpty(detailDto.getMaterialProcessing())) {
                result = false;
                validResultList.add("非标件,表面处理不能为空");
            }
            if (null == detailDto.getUnitWeight()) {
                result = false;
                validResultList.add("非标件,单位重量不能为空");
            }
        } else {
            //标准件校验
            if (StringUtils.isEmpty(detailDto.getBrand())) {
                result = false;
                validResultList.add("品牌不能为空");
            }
            if (StringUtils.isEmpty(detailDto.getModel())) {
                result = false;
                validResultList.add("型号/规格不能为空");
            } else {
                if (detailDto.getModel().length() > 150) {
                    result = false;
                    validResultList.add("型号/规格字符长度不能超过150");
                }
            }
            if (StringUtils.isNotEmpty(detailDto.getChartVersion()) && !org.apache.commons.lang3.StringUtils.isNumeric(detailDto.getChartVersion())) {
                result = false;
                validResultList.add("图纸版本号只能为数字");
            }
        }
        if (!result) {
            return false;
        }

        //查出所有相关的物料
        List<MaterialDto> adjustCodes = adjustCodeForKukaCodeRuleMap.get(getAdjustCodeForKukaCodeRuleKey(figureNumber, brand, model,
                nonStandardMaterialType, detailDto.getMaterialClassification(), characterIgnoreList));
        if (CollectionUtils.isNotEmpty(adjustCodes)) {
            if (nonStandardMaterialType.contains(detailDto.getMaterialClassification())) {
                adjustCodes = adjustCodes.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            } else {
                adjustCodes = adjustCodes.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            }
        }
        //非标件就只根据图号 ,标件根据品牌 和型号/规格 查 基础信息表 未退市和未删除的物料
        List<MaterialDto> materialForCheckRepeat = materialForCheckRepeatMap.get(getMaterialForCheckRepeatKey(figureNumber, brand, model,
                nonStandardMaterialType, detailDto.getMaterialClassification(), characterIgnoreList));
        if (CollectionUtils.isNotEmpty(materialForCheckRepeat)) {
            if (nonStandardMaterialType.contains(detailDto.getMaterialClassification())) {
                materialForCheckRepeat = materialForCheckRepeat.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            } else {
                materialForCheckRepeat = materialForCheckRepeat.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(materialForCheckRepeat)) {
            //找全部库存组织的mat 136/137已经在mat，有 大类=标准件，品牌A+规格B的物料编码P3、E3（只要有1条数据，未删除/退市）
            StringBuffer stringBuffer = new StringBuffer("根据物料唯一性规则查询");
            for (MaterialDto materialDto : materialForCheckRepeat) {
                String organizationName = organizationMap.get(materialDto.getOrganizationId());
                stringBuffer.append("，库存组织：[" + organizationName + "]已存在物料ERP编码：[" + materialDto.getItemCode() + "]");
            }
            validResultList.add(stringBuffer.toString());
            return false;
        } else if (CollectionUtils.isNotEmpty(adjustCodes)) {
            String materialForCheckAdjustRepeatKey = getMaterialForCheckAdjustRepeatKey(figureNumber, brand, model, nonStandardMaterialType,
                    detailDto.getMaterialClassification(), characterIgnoreList);
            List<MaterialDto> delistAndDeleteMaterialDtos = materialForCheckAdjustRepeatMap.get(materialForCheckAdjustRepeatKey);
            if (CollectionUtils.isNotEmpty(delistAndDeleteMaterialDtos)) {
                if (nonStandardMaterialType.contains(detailDto.getMaterialClassification())) {
                    delistAndDeleteMaterialDtos =
                            delistAndDeleteMaterialDtos.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                                            && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                            e.getMaterialType())))
                                    .collect(Collectors.toList());
                } else {
                    delistAndDeleteMaterialDtos =
                            delistAndDeleteMaterialDtos.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                                            && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                            e.getMaterialType())))
                                    .collect(Collectors.toList());
                }
            }
            //找全部库存组织的adj(通过大类=标准件，品牌A+规格B，查adj，有数据，未作废) 136/136mat（无数据）
            if (CollectionUtils.isEmpty(delistAndDeleteMaterialDtos)) {
                StringBuffer stringBuffer = new StringBuffer("根据物料唯一性规则查询");
                for (MaterialDto materialDto : adjustCodes) {
                    String organizationName = organizationMap.get(materialDto.getOrganizationId());
                    stringBuffer.append("，库存组织：[" + organizationName + "]已存在物料PAM编码：[" + materialDto.getPamCode() + "]");
                }
                validResultList.add(stringBuffer.toString());
                return false;
            }
        }
        return result;
    }

    private Boolean checkDesignPlanDetailQuotable(MilepostDesignPlanDetailDto detailDto, String figureNumber,
                                                  String brand, String model,
                                                  Long organizationId, List<String> validResultList,
                                                  Map<Long, MaterialDto> forFillImportMap,
                                                  Map<String, List<MaterialDto>> materialForCheckRepeatMap,
                                                  Map<String, List<MaterialDto>> materialForCheckAdjustRepeatMap,
                                                  Map<String, List<MaterialDto>> adjustCodeForKukaCodeRuleMap, List<String> nonStandardMaterialType,
                                                  Map<String, MerielSubclassVO> materialClassMap, Map<Long, String> organizationMap,
                                                  List<String> standardMaterialsForWebInfoForWebInfo, List<String> nonStandardMaterialsForWebInfo,
                                                  List<String> characterIgnoreList) {
        //查出所有相关的物料
        List<MaterialDto> adjustCodes = adjustCodeForKukaCodeRuleMap.get(getAdjustCodeForKukaCodeRuleKey(figureNumber, brand, model,
                nonStandardMaterialType, detailDto.getMaterialClassification(), characterIgnoreList));
        if (CollectionUtils.isNotEmpty(adjustCodes)) {
            if (nonStandardMaterialType.contains(detailDto.getMaterialClassification())) {
                adjustCodes = adjustCodes.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            } else {
                adjustCodes = adjustCodes.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            }
        }
        List<MaterialDto> currentAdjustMaterials = new ArrayList<>();
        List<MaterialDto> otherAdjustMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(adjustCodes)) {
            for (MaterialDto adjustMaterial : adjustCodes) {
                if (Objects.equals(adjustMaterial.getOrganizationId(), organizationId)) {
                    currentAdjustMaterials.add(adjustMaterial);
                } else {
                    otherAdjustMaterials.add(adjustMaterial);
                }
            }
        }
        //非标件就只根据图号 ,标件根据品牌 和型号/规格 查 基础信息表 未退市和未删除的物料
        List<MaterialDto> materialForCheckRepeat = materialForCheckRepeatMap.get(getMaterialForCheckRepeatKey(figureNumber, brand, model,
                nonStandardMaterialType, detailDto.getMaterialClassification(), characterIgnoreList));
        if (CollectionUtils.isNotEmpty(materialForCheckRepeat)) {
            if (nonStandardMaterialType.contains(detailDto.getMaterialClassification())) {
                materialForCheckRepeat = materialForCheckRepeat.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            } else {
                materialForCheckRepeat = materialForCheckRepeat.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            }
        }
        //筛选当前库存组织的物料基础表中的数据
        List<MaterialDto> currentMaterials = new ArrayList<>();
        List<MaterialDto> otherMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(materialForCheckRepeat)) {
            for (MaterialDto materialDto : materialForCheckRepeat) {
                if (Objects.equals(materialDto.getOrganizationId(), organizationId)) {
                    currentMaterials.add(materialDto);
                } else {
                    otherMaterials.add(materialDto);
                }
            }
        } else {
            logger.info("checkDesignPlanDetailQuotable的nonStandardMaterialsForWebInfo：{}",
                    JsonUtils.toString(nonStandardMaterialsForWebInfo));
            logger.info("checkDesignPlanDetailQuotable的standardMaterialsForWebInfoForWebInfo：{}",
                    JsonUtils.toString(standardMaterialsForWebInfoForWebInfo));
            logger.info("checkDesignPlanDetailQuotable的detailDto：{}", JsonUtils.toString(detailDto));
            if (nonStandardMaterialType.contains(detailDto.getMaterialClassification())) {
                //非标件是否已在系统存在"物料的大中小类属于KUKA2022规则"并且"未退市未删除"的物料：
                //如果存在，不报错
                //如果不存在，才报错，才校验单次不能导多个
                if (CollectionUtils.isNotEmpty(nonStandardMaterialsForWebInfo) && nonStandardMaterialsForWebInfo.contains(detailDto.getFigureNumber())) {
                    validResultList.add("所导入数据中,存在多条图号:[" + detailDto.getFigureNumber() + "]的数据");
                    return false;
                } else {
                    nonStandardMaterialsForWebInfo.add(detailDto.getFigureNumber());
                }
            } else {
                if (CollectionUtils.isNotEmpty(standardMaterialsForWebInfoForWebInfo)
                        && standardMaterialsForWebInfoForWebInfo.contains(detailDto.getBrand() + ":"
                        + removeCharacterIgnoreList(detailDto.getModel(), characterIgnoreList))) {
                    validResultList.add("所导入数据中,存在多条品牌:[" + detailDto.getBrand() + "] + 型号/规格:[" + detailDto.getModel() + "的数据");
                    return false;
                } else {
                    standardMaterialsForWebInfoForWebInfo.add(detailDto.getBrand() + ":"
                            + removeCharacterIgnoreList(detailDto.getModel(), characterIgnoreList));
                }
            }
        }

        //非标件就只查图号 标件查品牌 和型号/规格的
        String materialForCheckAdjustRepeatKey = getMaterialForCheckAdjustRepeatKey(figureNumber, brand, model, nonStandardMaterialType,
                detailDto.getMaterialClassification(), characterIgnoreList);
        List<MaterialDto> delistAndDeleteMaterialDtos = materialForCheckAdjustRepeatMap.get(materialForCheckAdjustRepeatKey);
        if (CollectionUtils.isNotEmpty(delistAndDeleteMaterialDtos)) {
            if (nonStandardMaterialType.contains(detailDto.getMaterialClassification())) {
                delistAndDeleteMaterialDtos =
                        delistAndDeleteMaterialDtos.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                                        && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                        e.getMaterialType())))
                                .collect(Collectors.toList());
            } else {
                delistAndDeleteMaterialDtos =
                        delistAndDeleteMaterialDtos.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                                        && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                        e.getMaterialType())))
                                .collect(Collectors.toList());
            }
        }
        List<MaterialDto> currentDelistAndDeleteMaterialDtos = new ArrayList<>();
        List<MaterialDto> otherDelistAndDeleteMaterialDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(delistAndDeleteMaterialDtos)) {
            for (MaterialDto materialDto : delistAndDeleteMaterialDtos) {
                if (Objects.equals(materialDto.getOrganizationId(), organizationId)) {
                    currentDelistAndDeleteMaterialDtos.add(materialDto);
                } else {
                    otherDelistAndDeleteMaterialDtos.add(materialDto);
                }
            }
        }

        if (ListUtils.isEmpty(adjustCodes)) {
            if (ListUtils.isNotEmpty(currentMaterials)) {
                //本组织adj无，其他adj无, 本组织mat有
                if (currentMaterials.size() > 1) {
                    List<String> pamCodeList = currentMaterials.stream().map(Material::getPamCode).collect(Collectors.toList());
                    validResultList.add("根据唯一性条件查找到本组织存在多个物料:[" + pamCodeList.toString() + "]，请联系系统管理员");
                    return false;
                }
                MaterialDto materialDto = currentMaterials.get(0);
                if (Objects.equals(materialDto.getMaterialCategory(), detailDto.getMaterialCategory())) {
                    materialDto.setAllExtend(true);
                    forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), materialDto);
                } else {
                    //物料类型不一致时
                    validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName() + "]类型不一致, "
                            + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                    return false;
                }
            } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                //本组织adj无，其他adj无, 本组织mat退市或删除
                if (CollectionUtils.isNotEmpty(otherMaterials)) {
                    List<String> delistFlagPamCodeList =
                            currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                    .map(MaterialDto::getPamCode).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                        //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                        validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                        detailDto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherMaterials.get(0));
                } else {
                    return true;
                }
            } else {
                //本组织adj无，其他adj无, 本组织mat无
                if (CollectionUtils.isNotEmpty(otherMaterials)) {
                    forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherMaterials.get(0));
                } else {
                    return true;
                }
            }
        } else {
            if (CollectionUtils.isNotEmpty(currentAdjustMaterials)) {
                List<MaterialDto> currentDtoList = currentAdjustMaterials.stream().filter(e ->
                        !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(currentDtoList)) {
                    //本组织adj有(有单据状态！="审批通过")
                    for (MaterialDto dto : currentDtoList) {
                        validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                    }
                    return false;
                }
                //本组织adj有
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    //本组织adj有,本组织mat有
                    if (currentMaterials.size() > 1) {
                        List<String> pamCodeList = currentMaterials.stream().map(Material::getPamCode).collect(Collectors.toList());
                        validResultList.add("根据唯一性条件查找到本组织存在多个物料:[" + pamCodeList.toString() + "]，请联系系统管理员");
                        return false;
                    }
                    MaterialDto materialDto = currentMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), detailDto.getMaterialCategory())) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName() + "]类型不一致, "
                                + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        return false;
                    }
                } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    //本组织adj有, 本组织mat退市或删除
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        //本组织adj有, 本组织mat退市或删除, 其他mat有
                        List<String> delistFlagPamCodeList =
                                currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                        .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            detailDto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        //本组织adj有, 本组织mat退市或删除, 其他mat退市或删除
                        return true;
                    } else {
                        //本组织adj有, 本组织mat退市或删除, 其他mat无
                        if (CollectionUtils.isNotEmpty(otherAdjustMaterials)) {
                            List<String> delistFlagPamCodeList =
                                    currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                            .map(MaterialDto::getPamCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                                //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                                validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                                detailDto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                    !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(dtoList)) {
                                //本组织adj有 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                                for (MaterialDto dto : dtoList) {
                                    validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                            "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                                }
                                return false;
                            }
                            forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherAdjustMaterials.get(0));
                        } else {
                            return true;
                        }
                    }
                } else {
                    //本组织adj有，本组织mat无
                    MaterialDto materialDto = currentAdjustMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), detailDto.getMaterialCategory())) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName() + "]类型不一致, "
                                + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        return false;
                    }
                    forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), materialDto);
                }
            } else {
                //本组织adj无，其他adj有
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    //本组织adj无，其他adj有，本组织mat有
                    if (currentMaterials.size() > 1) {
                        List<String> pamCodeList = currentMaterials.stream().map(Material::getPamCode).collect(Collectors.toList());
                        validResultList.add("根据唯一性条件查找到本组织存在多个物料:[" + pamCodeList.toString() + "]，请联系系统管理员");
                        return false;
                    }
                    MaterialDto materialDto = currentMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), detailDto.getMaterialCategory())) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName() + "]类型不一致, "
                                + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        return false;
                    }
                } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    //本组织adj无，其他adj有, 本组织mat删除或退市
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        List<String> delistFlagPamCodeList =
                                currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                        .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            detailDto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        return true;
                    } else {
                        List<String> delistFlagPamCodeList =
                                currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                        .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            detailDto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto dto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                            }
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherAdjustMaterials.get(0));
                    }
                } else {
                    //本组织adj无，其他adj有, 本组织mat无
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        return true;
                    } else {
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat无 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto dto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                            }
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherAdjustMaterials.get(0));
                    }
                }
            }
        }
        return true;
    }

    private String getAdjustCodeForKukaCodeRuleKey(String figureNumber, String brand, String model, List<String> nonStandardMaterialType,
                                                   String materialClassification, List<String> characterIgnoreList) {

        StringBuffer key = new StringBuffer("getNotFlexibleAdjustCodeForKukaCodeRuleKey");
        if (nonStandardMaterialType.contains(materialClassification)) {
            if (StringUtils.isNotEmpty(figureNumber)) {
                key.append("_").append(figureNumber.toUpperCase());
            }
        } else {
            if (StringUtils.isNotEmpty(brand)) {
                key.append("_").append(brand.toUpperCase());
            }
            if (StringUtils.isNotEmpty(model)) {
                key.append("_").append(removeCharacterIgnoreList(model, characterIgnoreList));
            }
        }
        return key.toString();
    }

    private String getMaterialForCheckRepeatKey(String figureNumber, String brand, String model, List<String> nonStandardMaterialType,
                                                String materialClassification, List<String> characterIgnoreList) {
        StringBuffer key = new StringBuffer("getNotFlexibleMaterialForCheckRepeatKey");
        if (nonStandardMaterialType.contains(materialClassification)) {
            if (StringUtils.isNotEmpty(figureNumber)) {
                key.append("_").append(figureNumber.toUpperCase());
            }
        } else {
            if (StringUtils.isNotEmpty(brand)) {
                key.append("_").append(brand.toUpperCase());
            }
            if (StringUtils.isNotEmpty(model)) {
                key.append("_").append(removeCharacterIgnoreList(model, characterIgnoreList));
            }
        }
        return key.toString();
    }

    private String getMaterialForCheckAdjustRepeatKey(String figureNumber, String brand, String model, List<String> nonStandardMaterialType,
                                                      String materialClassification, List<String> characterIgnoreList) {
        StringBuffer key = new StringBuffer("getNotFlexibleMaterialForCheckAdjustRepeatKey");
        if (nonStandardMaterialType.contains(materialClassification)) {
            if (StringUtils.isNotEmpty(figureNumber)) {
                key.append("_").append(figureNumber.toUpperCase());
            }
        } else {
            if (StringUtils.isNotEmpty(brand)) {
                key.append("_").append(brand.toUpperCase());
            }
            if (StringUtils.isNotEmpty(model)) {
                key.append("_").append(removeCharacterIgnoreList(model, characterIgnoreList));
            }
        }
        return key.toString();
    }

    private String generateMaterialDesForKuka2022(MaterialAdjustDetailDTO detailDTO, List<String> nonStandardMaterialType) {
        StringBuffer sb = new StringBuffer();
        sb.append(detailDTO.getName());
        //非标件
        if (nonStandardMaterialType.contains(detailDTO.getMaterialClass())) {
            sb.append("#").append(detailDTO.getFigureNumber());
        } else {
            sb.append("#").append(detailDTO.getBrand());
            sb.append("#").append(detailDTO.getModel());
            sb.append("#").append(detailDTO.getOrSparePartsMask());
        }
        return sb.toString().length() <= 230 ? sb.toString() : sb.substring(0, 230);
    }

    private String getMaterialCustomDictHeaderKey(String materialClassification, String codingMiddleClass, String materielType) {
        StringBuffer key = new StringBuffer("getMaterialCustomDictHeaderKey");
        if (StringUtils.isNotEmpty(materialClassification)) {
            key.append("_").append(materialClassification);
        }
        if (StringUtils.isNotEmpty(codingMiddleClass)) {
            key.append("_").append(codingMiddleClass);
        }
        if (StringUtils.isNotEmpty(materielType)) {
            key.append("_").append(materielType);
        }
        return key.toString();
    }

    //去除字符串中含有 characterIgnoreList集合字符忽略
    private String removeCharacterIgnoreList(String str, List<String> characterIgnoreList) {
        if (StringUtils.isEmpty(str) || CollectionUtils.isEmpty(characterIgnoreList)) {
            return StringUtils.isEmpty(str) ? str : str.toUpperCase(Locale.ROOT);
        }
        for (String character : characterIgnoreList) {
            if (Objects.equals(character, "空格")) {
                //给str去空格
                str = str.replaceAll(" ", "");
            } else {
                str = str.replace(character, "");
            }
        }
        return str.toUpperCase(Locale.ROOT);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean ksMatchingMaterialCodeEstimateNewWBS(List<MilepostDesignPlanDetailDto> designPlanDetailDtos,
                                                        Long storageId, Long unitId, Long markId, boolean validation,
                                                        List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo, String wbsSummaryCode,
                                                        Long projectId) {
        Guard.notNull(projectId, "项目ID为空");
        Project project = projectExtMapper.selectByPrimaryKey(projectId);
        Guard.notNull(project, String.format("项目ID：%s对应的项目不存在", projectId));
        Guard.notNull(project.getType(), String.format("项目ID：%s对应的项目类型为空", projectId));
        ProjectType projectType = projectTypeExtMapper.selectByPrimaryKey(project.getType());
        Guard.notNull(projectType, String.format("项目ID：%s对应的项目类型不存在", projectId));
        String budgetConfig = projectType.getBudgetConfig();
        JSONArray materiel = JSON.parseObject(budgetConfig).getJSONArray(BudgetConfigEnums.MATERIEL.getCode());
        // 项目类型设置的预算设置的字段设置的计划交付日期 的开关是否开启
        Boolean isOpenDeliveryTime = false;
        for (int i = 0; i < materiel.size(); i++) {
            JSONObject jsonObject = materiel.getJSONObject(i);
            if (null == jsonObject) {
                continue;
            }
            String code = Optional.ofNullable(jsonObject.get("code")).map(Object::toString).orElse(null);
            String value = Optional.ofNullable(jsonObject.get("value")).map(Object::toString).orElse(null);
            if (Objects.equals("fieldSetByMaterial", code) && Objects.equals("[1]", value)) {
                isOpenDeliveryTime = true;
                break;
            }
        }

        boolean result = validation;

        String materialCodeRule = getMaterialCodeRule(unitId); //当前单位的物料编码规则
        //获取"机械加工件"对应"加工件分类"的属性
        List<DictDto> dicts = ltcDictService.getLtcDict(MACHINED_PART_TYPE, null, null);
        Guard.notNullOrEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");
        // 获取类别列表数据
        List<MerielSubclassVO> merielSubclassList;
        if (MaterialCodeRuleEnum.KUKAMIA.getCode().equals(materialCodeRule)) {
            merielSubclassList = ctcExtService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUKAMIA.getCode());//机器人
        } else {
            merielSubclassList = ctcExtService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUNSHAN.getCode());//昆山
        }
        // 物料类别Table数据（row小类，column中类，value实体类）
        HashBasedTable<String, String, MerielSubclassVO> merielClassTable = HashBasedTable.create();
        for (MerielSubclassVO merielSubclassVO : merielSubclassList) {
            merielClassTable.put(merielSubclassVO.getValue(), merielSubclassVO.getMiddileValue(), merielSubclassVO);
        }
        // 获取计量单位
        List<DictDto> dictDtoList = ltcDictService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Map<String, String> unitNameCodeMap = new HashMap<>();
        Map<String, String> unitCodeNameMap = new HashMap<>();
        for (DictDto dictDto : dictDtoList) {
            String code = dictDto.getCode();
            String name = dictDto.getName();
            unitNameCodeMap.put(name, code);
            unitCodeNameMap.put(code, name);
        }

        //获取品牌列表数据
        List<BrandMaintenance> brandMaintenanceList = brandMaintenanceService.selectList();
        Guard.notNullOrEmpty(brandMaintenanceList, "请先维护品牌信息");

        List<String> brandMaintenanceCnList =
                brandMaintenanceList.stream().filter(brand -> !Boolean.TRUE.equals(brand.getDeletedFlag()) || 0 == brand.getBrandStatus())
                        .map(BrandMaintenance::getBrandNameCn).filter(brand -> Objects.equals(brand, "无")).collect(Collectors.toList());

        List<String> brandMaintenanceEnList =
                brandMaintenanceList.stream().filter(brand -> !Boolean.TRUE.equals(brand.getDeletedFlag()) || 0 == brand.getBrandStatus())
                        .map(BrandMaintenance::getBrandNameEn).filter(brand -> Objects.equals(brand, "NONE")).collect(Collectors.toList());

        // 获取备件标识组织参数配置
        Set<String> orSparePartsMaskSet = ctcExtService.queryByName(Constants.SPAREPARTS_MASK, unitId, OrgCustomDictOrgFrom.COMPANY);
        Guard.notNullOrEmpty(orSparePartsMaskSet, "备件标识需要在组织参数中维护");
        // 获取物料类型及编码字典
        Byte a = 1;
        Map<String, DictDto> materialTypeMap = ltcDictService.getLtcDict(MATERIAL_TYPE, null, null).stream()
                .filter(o -> !Objects.equals(a, o.getOrderNum())).collect(Collectors.toMap(DictDto::getName, Function.identity(),
                        (key1, key2) -> key2));
        List<String> checkRepeatPamWBSDeliveryTime = new ArrayList<>();

        List<String> repetition1 = new ArrayList<>();
        List<String> repetition2 = new ArrayList<>();
        List<String> repetition3 = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(designPlanDetailDtoForWebInfo)) {
            checkRepeatPamWBSDeliveryTime =
                    designPlanDetailDtoForWebInfo.stream().map(webInfo -> webInfo.getPamCode() + ":" + webInfo.getWbsSummaryCode()
                                    + ":" + ((ObjectUtils.isEmpty(webInfo.getDeliveryTime())) ? "" :
                                    DateUtils.parse(webInfo.getDeliveryTime().toString(),
                                            DateUtils.FORMAT_SHORT)))
                            .collect(Collectors.toList());

            repetition1 = designPlanDetailDtoForWebInfo.stream().map(webInfo -> webInfo.getCodingMiddleClass() + ":" + webInfo.getBrand()
                    + ":" + webInfo.getFigureNumber() + ":" + webInfo.getWbsSummaryCode()).collect(Collectors.toList());
            repetition2 = designPlanDetailDtoForWebInfo.stream().map(webInfo -> webInfo.getCodingMiddleClass() + ":" + webInfo.getBrand()
                    + ":" + webInfo.getBrandMaterialCode() + ":" + webInfo.getWbsSummaryCode()).collect(Collectors.toList());
            repetition3 = designPlanDetailDtoForWebInfo.stream().map(webInfo -> webInfo.getCodingMiddleClass() + ":" + webInfo.getBrand()
                    + ":" + webInfo.getModel() + ":" + webInfo.getWbsSummaryCode()).collect(Collectors.toList());
        }

        List<MilepostDesignPlanDetailDto> erpCodeIsNotEmptyList = new ArrayList<>();
        List<MilepostDesignPlanDetailDto> erpCodeIsEmptyList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto dto : designPlanDetailDtos) {
            // PAM物料编码 和 ERP物料编码 不能都有值
            String pamCode = dto.getPamCode();
            String erpCode = dto.getErpCode();
            boolean erpCodeIsNotEmpty = StringUtils.isNotEmpty(erpCode);
            if (StringUtils.isNotEmpty(pamCode) && erpCodeIsNotEmpty) {
                // 手动增加不走这个校验
                if (!Objects.equals("1", dto.getOperationType())) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "PAM编码和ERP编码填写其一中一个即可");
                    continue;
                }
            }
            dto.setImportErpCodeIsNotEmpty(erpCodeIsNotEmpty);
            // ERP物料编码有值则走 跨组织物料引用 逻辑，否则走以前的逻辑
            if (erpCodeIsNotEmpty) {
                //校验必填项
                String materialCategory = dto.getMaterialCategory();
                if (StringUtils.isNotEmpty(materialCategory)) {
                    DictDto materialTypeDict = materialTypeMap.get(materialCategory);
                    if (Objects.isNull(materialTypeDict)) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，"))
                                + "物料类型为字典项material_type中序号不为“1”对应的值");
                        continue;
                    }
                } else {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料类型不能为空");
                    continue;
                }
                logger.info("ksMatchingMaterialCodeEstimateNewWBS的commonValidate前：{}", JSONObject.toJSONString(dto));
                result = commonValidateWbs(brandMaintenanceList, dto, merielClassTable, dicts, orSparePartsMaskSet, unitNameCodeMap,
                        brandMaintenanceCnList, brandMaintenanceEnList, result, storageId, unitId
                );
                if (!result) {
                    continue;
                }
                logger.info("ksMatchingMaterialCodeEstimateNewWBS的commonValidate后：{}", JSONObject.toJSONString(dto));

                MaterialExample example = new MaterialExample();
                MaterialExample.Criteria criteria = example.createCriteria();
                criteria.andItemCodeEqualTo(erpCode).andDeleteFlagEqualTo(false).andDelistFlagEqualTo(false);
                List<Material> materialList = materialMapper.selectByExample(example);
                if (CollectionUtils.isEmpty(materialList)) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "ERP编码不存在");
                    continue;
                }
                // 如果按ERP编码在当前库存组织136找到多条数据
                List<Material> currentMaterialList =
                        materialList.stream().filter(e -> Objects.equals(storageId, e.getOrganizationId())).collect(Collectors.toList());
                Material currentMaterial = CollectionUtils.isEmpty(currentMaterialList) ? null : currentMaterialList.get(0);
                if (currentMaterial != null) {
                    dto.setImportFindCurrentMaterialByErpCode(true);
                    dto.setChartVersion(StringUtils.isNotEmpty(currentMaterial.getChartVersion()) ? currentMaterial.getChartVersion() : "0");
                    String pCode = currentMaterial.getPamCode();
                    result = materialCheck(brandMaintenanceList, dto, storageId, unitId, result);
                    if (!result) {
                        continue;
                    }
                    // 如果按ERP编码在当前库存组织136找到一条数据，并且这条数据PAM编码已存在：和走PAM编码导入的逻辑一样
                    if (StringUtils.isNotEmpty(pCode)) {
                        dto.setPamCode(pCode);
                        erpCodeIsEmptyList.add(dto);
                    } else {
                        // 如果按ERP编码在当前库存组织136找到一条数据，并且这条数据PAM编码不存在：    改中间表新增逻辑-改变更表新增逻辑-更新物料基础表
                        dto.setImportFindMaterialPamCodeIsNotExist(true);
                        dto.setPamCode(generateMaterialPAMCode());
                        dto.setErpCodeSource(2);
                        result = setDtoByErpCodeIsNotEmpty(currentMaterial, dto, unitCodeNameMap, isOpenDeliveryTime);
                        erpCodeIsNotEmptyList.add(dto);
                    }
                } else {
                    Material material = BeanConverter.copy(materialList.get(0), Material.class);
                    dto.setChartVersion(StringUtils.isNotEmpty(material.getChartVersion()) ? material.getChartVersion() : "0");
                    String pCode = material.getPamCode();
                    if (StringUtils.isNotEmpty(pCode)) {
                        // 如果按ERP编码在其它库存组织（如137）找到一条数据，并且这条数据PAM编码已存在： 中间表&变更表取基础表的数据，和PAM编码导入逻辑一致，-新增物料基础表到136
                        dto.setPamCode(pCode);
                        erpCodeIsEmptyList.add(dto);
                    } else {
                        // 如果按ERP编码在其它库存组织（如137）找到一条数据，并且这条数据PAM编码不存在：改中间表新增逻辑-改变更表新增逻辑-新增物料基础表到136
                        dto.setImportFindMaterialPamCodeIsNotExist(true);
                        dto.setPamCode(generateMaterialPAMCode());
                        dto.setErpCodeSource(2);
                        result = setDtoByErpCodeIsNotEmpty(material, dto, unitCodeNameMap, isOpenDeliveryTime);
                        erpCodeIsNotEmptyList.add(dto);
                    }
                }
                result = detailUniquenessCheck(dto, storageId, unitId, result, checkRepeatPamWBSDeliveryTime, wbsSummaryCode);
            } else {
                // ERP物料编码没有值走以前的逻辑
                result = this.oldAdditionalValidateWbs(brandMaintenanceList, dto, merielClassTable, dicts, orSparePartsMaskSet, unitNameCodeMap,
                        unitCodeNameMap,
                        brandMaintenanceCnList, brandMaintenanceEnList, result, repetition1, repetition2, repetition3, storageId,
                        materialTypeMap, checkRepeatPamWBSDeliveryTime, wbsSummaryCode, unitId);
                erpCodeIsEmptyList.add(dto);
            }
        }


        //判断物料的唯一性(新逻辑 跨组织物料引用 不走该校验)
        if (result) {
            //多线程处理
            if (erpCodeIsEmptyList.size() >= 100) {
                handleList(erpCodeIsEmptyList, storageId, unitCodeNameMap);
            } else {
                for (MilepostDesignPlanDetailDto dto : erpCodeIsEmptyList) {
                    List<BrandMaintenance> maintenanceList = brandMaintenanceList.stream().filter(brand ->
                                    (Objects.equals(brand.getBrandNameCn(), dto.getBrand()) || Objects.equals(brand.getBrandNameEn(), dto.getBrand())
                                            || Objects.equals(brand.getBrandName(), dto.getBrand()))
                                            && (Boolean.FALSE.equals(brand.getDeletedFlag()) || Objects.equals(0, brand.getBrandStatus())))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(maintenanceList)) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，"))
                                + "品牌名称：" + dto.getBrand() + "在品牌表中不存在");
                    } else {
                        // 设置最终的品牌名称
                        dto.setBrand(maintenanceList.get(0).getBrandName());
                    }

                    this.checkKsMaterial(dto, storageId, unitCodeNameMap);
                    //ksMatchingMaterialCodeAsync(dto,storageId);
                    result = materialCheck(brandMaintenanceList, dto, storageId, unitId, result);
                    result = detailUniquenessCheck(dto, storageId, unitId, result, checkRepeatPamWBSDeliveryTime, wbsSummaryCode);
                }
            }
            result = erpCodeIsEmptyList.stream().noneMatch(e -> StringUtils.isNotEmpty(e.getValidResult()));
        }

        // 合并
        erpCodeIsEmptyList.addAll(erpCodeIsNotEmptyList);

        if (result) {
            this.saveMilepostDesignDetailMiddle(erpCodeIsEmptyList, markId, storageId);
        }
        return result;
    }

    private Boolean materialCheck(List<BrandMaintenance> brandMaintenanceList, MilepostDesignPlanDetailDto dto, Long storageId, Long unitId,
                                  boolean result) {
        if (StringUtils.isNotEmpty(dto.getMaterialClassification()) && ("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory()))) {
            String activityCode = materialExtMapper.getActivityForDesignPlanDetail(dto.getMaterialClassification(), dto.getCodingMiddleClass(),
                    dto.getMaterielType(), storageId);
            if (StringUtils.isEmpty(activityCode)) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "活动事项编码不存在，不允许导入！");
            } else {
                List<String> activityCodes = Arrays.asList(activityCode.split(","));
                dto.setActivityCode(activityCodes.get(0));
                String projectBudgetType = materialExtMapper.getProjectBudgetTypeForDesignPlanDetail(activityCodes.get(0), unitId);
                if (StringUtils.isEmpty(projectBudgetType)) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "项目预算类别属性不存在，不允许导入！");
                } else {
                    dto.setProjectBudgetType(projectBudgetType);
                }
            }
        } else {
            dto.setWhetherModel(true);
        }
        return result;
    }

    private Boolean detailUniquenessCheck(MilepostDesignPlanDetailDto dto, Long storageId, Long unitId, boolean result,
                                          List<String> checkRepeatPamWBSDeliveryTime, String wbsSummaryCode) {
        if (("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory()))) {
            if (checkRepeatPamWBSDeliveryTime.contains(dto.getPamCode() + ":" + wbsSummaryCode + ":" + dto.getDeliveryTime())) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，"))
                        + "WBS+PAM物料编码+物料到货日期维度的详细设计行不允许重复！");
            } else {
                checkRepeatPamWBSDeliveryTime.add(dto.getPamCode() + ":" + wbsSummaryCode + ":" + dto.getDeliveryTime());
            }
        }
        return result;
    }

    private Boolean oldAdditionalValidate(MilepostDesignPlanDetailDto dto, HashBasedTable<String, String, MerielSubclassVO> merielClassTable,
                                          List<DictDto> dicts, Set<String> orSparePartsMaskSet, Map<String, String> unitNameCodeMap,
                                          Map<String, String> unitCodeNameMap, boolean result,
                                          List<String> repetition1, List<String> repetition2, List<String> repetition3, Long storageId,
                                          Map<String, DictDto> materialTypeMap, List<BrandMaintenance> brandMaintenanceList, boolean isHand,
                                          Boolean isOpenDeliveryTime) {
        //如果导入文件已经有PAM物料编码，则根据PAM编码和库存组织id去物料表查询并带出其他属性,不需校验其他属性。
        if (StringUtils.isNotEmpty(dto.getPamCode())) {
            if (dto.getNumber() == null) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "数量不能为空");
            } else {
                List<Material> materials = materialService.selectByPamCodeAndOrganizationId(dto.getPamCode(), storageId);
                if (ListUtils.isEmpty(materials)) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "导入的PAM编码有误");
                } else {
                    result = this.setDto(materials.get(0), dto, result, unitCodeNameMap, isOpenDeliveryTime);
                }
            }
        } else {
            //校验导入数据 pam编码和erp编码都为空
            result = this.checkDesignDetailDto(dto, dicts, merielClassTable, orSparePartsMaskSet, unitNameCodeMap, materialTypeMap,
                    result, repetition1, repetition2, repetition3, brandMaintenanceList, isHand, isOpenDeliveryTime);
        }
        return result;
    }

    private Boolean oldAdditionalValidateWbs(List<BrandMaintenance> brandMaintenanceList, MilepostDesignPlanDetailDto dto,
                                             HashBasedTable<String, String, MerielSubclassVO> merielClassTable, List<DictDto> dicts,
                                             Set<String> orSparePartsMaskSet, Map<String, String> unitNameCodeMap,
                                             Map<String, String> unitCodeNameMap,
                                             List<String> brandMaintenanceCnList, List<String> brandMaintenanceEnList, boolean result,
                                             List<String> repetition1,
                                             List<String> repetition2, List<String> repetition3, Long storageId, Map<String, DictDto> materialTypeMap,
                                             List<String> checkRepeatPamWBSDeliveryTime, String wbsSummaryCode, Long unitId) {
        //如果导入文件已经有PAM物料编码，则根据PAM编码和库存组织id去物料表查询并带出其他属性,不需校验其他属性。
        if (StringUtils.isNotEmpty(dto.getPamCode())) {
            if (dto.getNumber() == null) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "数量不能为空");
            } else {
                List<Material> materials = materialService.selectByPamCodeAndOrganizationId(dto.getPamCode(), storageId);
                if (ListUtils.isEmpty(materials)) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "导入的PAM编码有误");
                } else {
                    result = this.setDtoWbs(materials.get(0), dto, result, checkRepeatPamWBSDeliveryTime, unitCodeNameMap);
                }
            }
        } else {
            //校验导入数据 pam编码和erp编码都为空
            result = this.checkDesignDetailDtoWBS(brandMaintenanceList, dto, dicts, merielClassTable, orSparePartsMaskSet, unitNameCodeMap,
                    materialTypeMap,
                    brandMaintenanceCnList, brandMaintenanceEnList, result, repetition1, repetition2, repetition3, wbsSummaryCode, storageId, unitId);
        }
        return result;
    }

    private Boolean setDtoByErpCodeIsNotEmpty(Material material, MilepostDesignPlanDetailDto dto, Map<String, String> unitCodeNameMap,
                                              Boolean isOpenDeliveryTime) {
        boolean result = true;
        if (StringUtils.isEmpty(dto.getMaterielDescr())) {
            dto.setMaterielDescr(material.getItemInfo()); // 物料描述
        }
        Integer materielDescrLength = getOracleLengthFromMysqlVarchar(dto.getMaterielDescr());
        if (materielDescrLength > 240) {
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料描述的字段长度超过了240个字符");
            result = false;
        }
        // 引用了物料
        String unitCode = material.getUnit();
        if (!unitCodeNameMap.containsKey(unitCode)) {
            String errorMsg = String.format("引用的物料id为：%d的物料的单位：%s格式错误", material.getId(), unitCode);
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + errorMsg);
            result = false;
        }
        dto.setUnitCode(unitCode);
        dto.setUnit(unitCodeNameMap.get(unitCode));

        if (StringUtils.isEmpty(dto.getMaterielType())) {
            dto.setMaterielType(material.getMaterialType()); //物料小类
        }
        if (StringUtils.isEmpty(dto.getMaterialClassification())) {
            dto.setMaterialClassification(material.getMaterialClassification()); //物料大类
        }
        if (StringUtils.isEmpty(dto.getName())) {
            dto.setName(material.getName()); //名称
        }
        if (StringUtils.isEmpty(dto.getModel())) {
            dto.setModel(material.getModel()); //规格型号
        }
        if (StringUtils.isEmpty(dto.getMachiningPartType())) {
            dto.setMachiningPartType(material.getMachiningPartType());//加工件分类
        }
        if (StringUtils.isEmpty(dto.getMaterial())) {
            dto.setMaterial(material.getMaterial());//材料
        }
        if (StringUtils.isEmpty(dto.getMaterialProcessing())) {
            dto.setMaterialProcessing(material.getMaterialProcessing());//材质处理
        }
        if (StringUtils.isEmpty(dto.getUnitWeight())) {
            dto.setUnitWeight(material.getUnitWeight());//单位重量
        }

        if (StringUtils.isEmpty(dto.getCodingMiddleClass())) {
            dto.setCodingMiddleClass(material.getCodingMiddleclass()); //物料中类
        }
        if (StringUtils.isEmpty(dto.getFigureNumber())) {
            dto.setFigureNumber(material.getFigureNumber()); //图号
        }
        if (StringUtils.isEmpty(dto.getChartVersion())) {
            dto.setChartVersion(material.getChartVersion()); //图纸版本
        }
        if (StringUtils.isEmpty(dto.getBrandMaterialCode())) {
            dto.setBrandMaterialCode(material.getBrandMaterialCode());  //品牌商物料编码
        }
        if (StringUtils.isEmpty(dto.getOrSparePartsMask())) {
            dto.setOrSparePartsMask(material.getOrSparePartsMask()); //备件标识
        }
        if (Objects.isNull(dto.getPerchasingLeadtime())) {
            dto.setPerchasingLeadtime(StringUtils.isEmpty(material.getBuyerRound()) ? null :
                    Long.parseLong(material.getBuyerRound())); //采购提前期
        }
        if (Objects.isNull(dto.getMinPerchaseQuantity())) {
            dto.setMinPerchaseQuantity(material.getMinimumOrderQuantity()); //最小订货量（最小采购量）
        }
        if (StringUtils.isEmpty(dto.getMinPackageQuantity())) {
            dto.setMinPackageQuantity(StringUtils.isEmpty(material.getFixedLotMultiplier()) ? null :
                    Long.parseLong(material.getFixedLotMultiplier())); //固定批次增加（最小包装量）
        }

        String deliveryTimeStr = dto.getDeliveryTimeStr();
        // 计划交货日期=不打勾   外购物料、看板物料 需要填写 物料到货日期；计划交货日期=打钩    外购物料、看板物料  不需要填写 物料到货日期
        if (Boolean.FALSE.equals(isOpenDeliveryTime) && ("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory()))) {
            if (StringUtils.isEmpty(deliveryTimeStr)) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "外购物料/看板物料,物料到货日期不能为空");
            }
        }
        if (StringUtils.isNotEmpty(deliveryTimeStr)) {
            if (DateUtils.isValidDate(deliveryTimeStr, DateUtils.FORMAT_SHORT)) {
                dto.setDeliveryTime(DateUtils.parse(deliveryTimeStr, DateUtils.FORMAT_SHORT));
            } else {
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料到货日期格式不正确");
                result = false;
            }
        }

        // 给dto属性品牌赋值(brandName = 中文品牌+英文品牌)
        List<BrandMaintenance> brandMaintenanceList = brandMaintenanceService.selectList();
        if (StringUtils.isEmpty(dto.getBrand())) { // 如果dto的brand为空则取material的brand
            dto.setBrand(material.getBrand());
        } else {
            //如果品牌不为空则转大写
            if (dto.getBrand().length() > 150) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "品牌字符长度不能超过150");
            }
            dto.setBrand(dto.getBrand().toUpperCase(Locale.ROOT));
            if (Objects.equals(dto.getBrand(), "无") || Objects.equals(dto.getBrand(), "NONE")) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "品牌不能为无或NONE");
            }
            List<BrandMaintenance> maintenanceList = brandMaintenanceList.stream().filter(brand ->
                            (Objects.equals(brand.getBrandNameCn(), dto.getBrand()) || Objects.equals(brand.getBrandNameEn(), dto.getBrand())
                                    || Objects.equals(brand.getBrandName(), dto.getBrand()))
                                    && (Boolean.FALSE.equals(brand.getDeletedFlag()) || Objects.equals(0, brand.getBrandStatus())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(maintenanceList)) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "品牌名称：" + dto.getBrand() +
                        "在品牌表中不存在");
            } else {
                // 设置最终的品牌名称
                dto.setBrand(maintenanceList.get(0).getBrandName());
            }
        }

        return result;
    }

    private List<MilepostDesignPlanDetailDto> handleList(List<MilepostDesignPlanDetailDto> list, Long storageId,
                                                         Map<String, String> unitCodeNameMap) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        // 一个线程处理100条数据
        int count = 101;
        // 数据集合大小
        int listSize = list.size();
        // 开启的线程数
        int runSize = (listSize / count) + 1;
        // 存放每个线程的执行数据
        List<MilepostDesignPlanDetailDto> newList = null;
        // 创建一个线程池，数量和开启线程的数量一样
        ExecutorService executor = Executors.newFixedThreadPool(runSize);
        // 创建两个个计数器
        CountDownLatch begin = new CountDownLatch(1);
        CountDownLatch end = new CountDownLatch(runSize);
        List<Future<List<MilepostDesignPlanDetailDto>>> futures =
                new ArrayList<Future<List<MilepostDesignPlanDetailDto>>>();
        // 创建一个存储所有返回值的list
        List<MilepostDesignPlanDetailDto> listAll =
                Collections.synchronizedList(new ArrayList<MilepostDesignPlanDetailDto>());

        for (int i = 0; i < runSize; i++) {
            // 计算每个线程执行的数据
            if ((i + 1) == runSize) {
                int startIdx = (i * count);
                int endIdx = list.size();
                newList = list.subList(startIdx, endIdx);
            } else {
                int startIdx = (i * count);
                int endIdx = (i + 1) * count;
                newList = list.subList(startIdx, endIdx);
            }
            PersionThread PersionThread = new PersionThread(newList, begin, end, storageId, unitCodeNameMap);
            futures.add(executor.submit(PersionThread));
        }
        begin.countDown();
        try {
            for (Future<List<MilepostDesignPlanDetailDto>> future : futures) {
                //合并操作
                List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtoList = null;
                try {
                    milepostDesignPlanDetailDtoList = future.get();
                } catch (Exception e) {
                    logger.error("异常信息：", e);
                }
                listAll.addAll(milepostDesignPlanDetailDtoList);
            }
            end.await();

        } catch (InterruptedException e) {
            logger.error("异常信息：", e);
            Thread.currentThread().interrupt();
        }
        executor.shutdown();
        return listAll;
    }

    private void saveMilepostDesignDetailMiddle(List<MilepostDesignPlanDetailDto> designPlanDetailDtos, Long markId,
                                                Long storageId) {

        Long batch = null;
        if (markId == 0L) {
            SnowFlakeUtils snowFlakeUtils = new SnowFlakeUtils(4, 1, 1);
            batch = snowFlakeUtils.nextId();
        } else {
            batch = markId;
        }
        List<MilepostDesignPlanMiddle> milepostDesignPlanMiddleList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
            designPlanDetailDto.setMarkId(batch);
            MilepostDesignPlanMiddle milepostDesignPlanMiddle = BeanConverter.copy(designPlanDetailDto,
                    MilepostDesignPlanMiddle.class);
            milepostDesignPlanMiddle.setMarkId(batch);
            milepostDesignPlanMiddle.setOrganizationId(storageId);
            milepostDesignPlanMiddle.setCreateAt(new Date());
            milepostDesignPlanMiddle.setCreateBy(SystemContext.getUserId());
            milepostDesignPlanMiddle.setMarkId(batch);
            milepostDesignPlanMiddle.setDeletedFlag(false);
            if (StringUtils.isNotEmpty(milepostDesignPlanMiddle.getPamCode())) {
                milepostDesignPlanMiddleList.add(milepostDesignPlanMiddle);
                //materialService.saveMilepostDesignMiddle(milepostDesignPlanMiddle);
            }
        }
        if (ListUtils.isNotEmpty(milepostDesignPlanMiddleList)) {
            materialService.batchInsert(milepostDesignPlanMiddleList);
        }
    }

    @Override
    public void checkKsMaterial(MilepostDesignPlanDetailDto dto, Long storageId, Map<String, String> unitCodeNameMap) {
        if ("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory())) {
            List<String> materialCategoryList = Lists.newArrayList(MaterialCategoryEnum.PURCHASED_PARTS.msg(),
                    MaterialCategoryEnum.KANBAN_MATERIALS.msg());
            if (StringUtils.isEmpty(dto.getPamCode())) {
                if ("机械加工件".equals(dto.getCodingMiddleClass())) { //物料中类+品牌+图号 且物料类型为：外购物料或看板物料  检索相同编码
                    Material material = materialService.checkMaterial1(dto.getCodingMiddleClass(), dto.getBrand(), dto.getFigureNumber(),
                            materialCategoryList);
                    if (Objects.nonNull(material)) {
                        setDtoNew(material, dto, unitCodeNameMap);
                    } else {
                        //去临时表查
                        MilepostDesignPlanMiddle milepostDesignPlanMiddle = milepostDesignPlanMiddleService.checkMaterial1(dto.getCodingMiddleClass(),
                                dto.getBrand(), dto.getFigureNumber(), materialCategoryList);
                        if (Objects.nonNull(milepostDesignPlanMiddle)) {
                            dto.setPamCode(milepostDesignPlanMiddle.getPamCode());
                        } else {
                            dto.setPamCode(generateMaterialPAMCode());
                        }
                    }
                } else {
                    if (StringUtils.isNotEmpty(dto.getBrandMaterialCode())) { //品牌商物料编码存在  物料中类+品牌+品牌商物料编码
                        // 且物料类型为：外购物料或看板物料  检索相同编码
                        Material material = materialService.checkMaterial3(dto.getCodingMiddleClass(), dto.getBrand(), dto.getBrandMaterialCode(),
                                materialCategoryList);
                        if (Objects.isNull(material)) { //物料中类+品牌+规格/型号 且物料类型为：外购物料或看板物料  检索相同编码
                            MilepostDesignPlanMiddle middle = milepostDesignPlanMiddleService.checkMaterial3(dto.getCodingMiddleClass(),
                                    dto.getBrand(), dto.getBrandMaterialCode(), materialCategoryList);
                            if (null != middle) {
                                dto.setPamCode(middle.getPamCode());
                            } else {
                                Material material1 = materialService.checkMaterial2(dto.getCodingMiddleClass(), dto.getBrand(), dto.getModel(),
                                        materialCategoryList);
                                if (Objects.nonNull(material1)) {
                                    setDtoNew(material1, dto, unitCodeNameMap);
                                } else {
                                    MilepostDesignPlanMiddle milepostDesignPlanMiddle =
                                            milepostDesignPlanMiddleService.checkMaterial2(dto.getCodingMiddleClass(),
                                                    dto.getBrand(), dto.getModel(), materialCategoryList);
                                    if (Objects.nonNull(milepostDesignPlanMiddle)) {
                                        dto.setPamCode(milepostDesignPlanMiddle.getPamCode());
                                    } else {
                                        dto.setPamCode(generateMaterialPAMCode());
                                    }
                                }
                            }
                        } else {
                            setDtoNew(material, dto, unitCodeNameMap);
                        }
                    } else { //品牌商物料编码不存在  物料中类+品牌+规格/型号 且物料类型为：外购物料或看板物料  检索相同编码
                        Material material = materialService.checkMaterial2(dto.getCodingMiddleClass(), dto.getBrand(), dto.getModel(),
                                materialCategoryList);
                        if (Objects.nonNull(material)) {
                            setDtoNew(material, dto, unitCodeNameMap);
                        } else {
                            MilepostDesignPlanMiddle milepostDesignPlanMiddle =
                                    milepostDesignPlanMiddleService.checkMaterial2(dto.getCodingMiddleClass(),
                                            dto.getBrand(), dto.getModel(), materialCategoryList);
                            if (Objects.nonNull(milepostDesignPlanMiddle)) {
                                dto.setPamCode(milepostDesignPlanMiddle.getPamCode());
                            } else {
                                dto.setPamCode(generateMaterialPAMCode());
                            }
                        }
                    }
                }
            }
        } else if ("装配件".equals(dto.getMaterialCategory()) || "虚拟件".equals(dto.getMaterialCategory())) {
            // 装配件、虚拟件,自动生成pamCode
            dto.setPamCode(generateMaterialPAMCode());
        }

        if (StringUtils.isEmpty(dto.getMaterielDescr())) {
            ksGenerateMaterielDescr(dto);//物料描述
        }

        List<MaterialCostDto> materialCostDtos = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getPamCode())) {
            MaterialCostDto query = new MaterialCostDto();
            query.setPamCode(dto.getPamCode());
            query.setOrganizationId(storageId);
            materialCostDtos = materialCostService.selectListWithDetail(query);
        }
        MaterialCostDto materialCostDto = new MaterialCostDto();
        if (ListUtils.isNotEmpty(materialCostDtos)) {
            materialCostDto = materialCostDtos.get(0);
        }
        if (Objects.nonNull(materialCostDto)) {
            if (null != materialCostDto.getMaterialCategory() && Objects.equals(materialCostDto.getMaterialCategory(), dto.getMaterialCategory())) {
                //因为原来的估价表没有物料类型的字段，因此不带出非昆山物料的字段属性
                dto.setDesignCostId(materialCostDto.getId());
                dto.setDesignCost(materialCostDto.getItemCost());
                dto.setMaterielType(materialCostDto.getMaterielType());
                dto.setMachiningPartType(materialCostDto.getMachiningPartType());
                dto.setMaterial(materialCostDto.getMaterial());
                dto.setUnitWeight(materialCostDto.getUnitWeight());
                dto.setMaterialProcessing(materialCostDto.getMaterialProcessing());
                dto.setMaterialClassification(materialCostDto.getMaterialClassification());//物料大类
                dto.setCodingMiddleClass(materialCostDto.getCodingMiddleclass());//物料中类
                dto.setFigureNumber(materialCostDto.getFigureNumber());//图号
                dto.setBrandMaterialCode(materialCostDto.getBrandMaterialCode());//品牌商物料编码
                dto.setOrSparePartsMask(materialCostDto.getOrSparePartsMask());//备件标识
                dto.setBrand(materialCostDto.getBrand()); //品牌
                dto.setName(materialCostDto.getName()); //名称
                dto.setModel(materialCostDto.getModel()); //规格型号
                dto.setMaterielDescr(materialCostDto.getDescr()); // 物料描述
                Integer materielDescrLength = getOracleLengthFromMysqlVarchar(dto.getMaterielDescr());
                if (materielDescrLength > 240) {
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料描述的字段长度超过了240个字符");
                }
            }
        }
    }

    private Boolean setDto(Material material, MilepostDesignPlanDetailDto dto, boolean result, Map<String, String> unitCodeNameMap,
                           Boolean isOpenDeliveryTime) {
        dto.setMaterielDescr(material.getItemInfo()); // 物料描述
        Integer materielDescrLength = getOracleLengthFromMysqlVarchar(dto.getMaterielDescr());
        if (materielDescrLength > 240) {
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料描述的字段长度超过了240个字符");
            result = false;
        }
        // 引用了物料
        String unitCode = material.getUnit();
        if (!unitCodeNameMap.containsKey(unitCode)) {
            String errorMsg = String.format("引用的物料id为：%d的物料的单位：%s格式错误", material.getId(), unitCode);
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + errorMsg);
            result = false;
        }
        dto.setUnitCode(unitCode);
        dto.setUnit(unitCodeNameMap.get(unitCode));

        dto.setMaterielType(material.getMaterialType()); //物料小类
        dto.setErpCode(material.getItemCode()); //erp编码
        dto.setMaterialClassification(material.getMaterialClassification()); //物料大类
        dto.setName(material.getName()); //名称
        dto.setModel(material.getModel()); //规格型号
        dto.setBrand(material.getBrand()); //品牌
        dto.setMachiningPartType(material.getMachiningPartType());//加工件分类
        dto.setMaterial(material.getMaterial());//材料
        dto.setMaterialProcessing(material.getMaterialProcessing());//材质处理
        dto.setUnitWeight(material.getUnitWeight());//单位重量

        dto.setMaterialCategory(material.getMaterialCategory()); //物料类型
        dto.setCodingMiddleClass(material.getCodingMiddleclass()); //物料中类
        dto.setFigureNumber(material.getFigureNumber()); //图号
        dto.setChartVersion(material.getChartVersion()); //图纸版本
        dto.setBrandMaterialCode(material.getBrandMaterialCode());  //品牌商物料编码
        dto.setOrSparePartsMask(material.getOrSparePartsMask()); //备件标识
        dto.setPerchasingLeadtime(StringUtils.isEmpty(material.getBuyerRound()) ? null :
                Long.parseLong(material.getBuyerRound())); //采购提前期
        dto.setMinPerchaseQuantity(material.getMinimumOrderQuantity()); //最小订货量（最小采购量）
        dto.setMinPackageQuantity(StringUtils.isEmpty(material.getFixedLotMultiplier()) ? null :
                Long.parseLong(material.getFixedLotMultiplier())); //固定批次增加（最小包装量）

        String deliveryTimeStr = dto.getDeliveryTimeStr();
        // 计划交货日期=不打勾   外购物料、看板物料 需要填写 物料到货日期；计划交货日期=打钩    外购物料、看板物料  不需要填写 物料到货日期
        if (Boolean.FALSE.equals(isOpenDeliveryTime) && ("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory()))) {
            if (StringUtils.isEmpty(deliveryTimeStr)) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "外购物料/看板物料,物料到货日期不能为空");
            }
        }
        if (StringUtils.isNotEmpty(deliveryTimeStr)) {
            if (DateUtils.isValidDate(deliveryTimeStr, DateUtils.FORMAT_SHORT)) {
                dto.setDeliveryTime(DateUtils.parse(deliveryTimeStr, DateUtils.FORMAT_SHORT));
            } else {
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料到货日期格式不正确");
                result = false;
            }
        }

        return result;
    }

    private Boolean setDtoWbs(Material material, MilepostDesignPlanDetailDto dto, boolean result, List<String> checkRepeatPamWBSDeliveryTime,
                              Map<String, String> unitCodeNameMap) {
        dto.setMaterielDescr(material.getItemInfo()); // 物料描述
        Integer materielDescrLength = getOracleLengthFromMysqlVarchar(dto.getMaterielDescr());
        if (materielDescrLength > 240) {
            result = false;
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料描述的字段长度超过了240个字符");
        }
        // 引用了物料
        String unitCode = material.getUnit();
        if (!unitCodeNameMap.containsKey(unitCode)) {
            String errorMsg = String.format("引用的物料id为：%d的物料的单位：%s格式错误", material.getId(), unitCode);
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + errorMsg);
            result = false;
        }
        dto.setUnitCode(unitCode);
        dto.setUnit(unitCodeNameMap.get(unitCode));

        dto.setMaterielType(material.getMaterialType()); //物料小类
        dto.setErpCode(material.getItemCode()); //erp编码
        dto.setMaterialClassification(material.getMaterialClassification()); //物料大类
        dto.setName(material.getName()); //名称
        dto.setModel(material.getModel()); //规格型号
        dto.setBrand(material.getBrand()); //品牌
        dto.setMachiningPartType(material.getMachiningPartType());//加工件分类
        dto.setMaterial(material.getMaterial());//材料
        dto.setMaterialProcessing(material.getMaterialProcessing());//材质处理
        dto.setUnitWeight(material.getUnitWeight());//单位重量

        dto.setMaterialCategory(material.getMaterialCategory()); //物料类型
        dto.setCodingMiddleClass(material.getCodingMiddleclass()); //物料中类
        dto.setFigureNumber(material.getFigureNumber()); //图号
        dto.setChartVersion(StringUtils.isEmpty(material.getChartVersion()) ? "0" : material.getChartVersion()); //图纸版本
        dto.setBrandMaterialCode(material.getBrandMaterialCode());  //品牌商物料编码
        dto.setOrSparePartsMask(material.getOrSparePartsMask()); //备件标识
        dto.setPerchasingLeadtime(StringUtils.isEmpty(material.getBuyerRound()) ? null :
                Long.parseLong(material.getBuyerRound())); //采购提前期
        dto.setMinPerchaseQuantity(material.getMinimumOrderQuantity()); //最小订货量（最小采购量）
        dto.setMinPackageQuantity(StringUtils.isEmpty(material.getFixedLotMultiplier()) ? null : Long.parseLong(material.getFixedLotMultiplier())); //固定批次增加（最小包装量）

        // 启用WBS必须填写“物料到货日期”
        String deliveryTimeStr = dto.getDeliveryTimeStr();
        if (StringUtils.isEmpty(deliveryTimeStr)) {
            result = false;
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料到货日期不能为空");
        } else {
            if (DateUtils.isValidDate(deliveryTimeStr, DateUtils.FORMAT_SHORT)) {
                dto.setDeliveryTime(DateUtils.parse(deliveryTimeStr, DateUtils.FORMAT_SHORT));
                if (checkRepeatPamWBSDeliveryTime.contains(dto.getPamCode() + ":" + dto.getWbsSummaryCode() + ":" + dto.getDeliveryTime().toString())) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "根据物料唯一性原则，该条数据重复！");
                } else {
                    checkRepeatPamWBSDeliveryTime.add(dto.getPamCode() + ":" + dto.getWbsSummaryCode() + ":" + dto.getDeliveryTime().toString());
                }
            } else {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料到货日期格式不正确");
            }
        }

        return result;
    }

    private MilepostDesignPlanDetailDto setDtoNew(Material materialDto, MilepostDesignPlanDetailDto dto, Map<String, String> unitCodeNameMap) {
        String unitCode = materialDto.getUnit();
        if (!unitCodeNameMap.containsKey(unitCode)) {
            String errorMsg = String.format("引用的物料id为：%d的物料的单位：%s格式错误", materialDto.getId(), unitCode);
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + errorMsg);
        }
        dto.setUnitCode(unitCode);
        dto.setUnit(unitCodeNameMap.get(unitCode));
        dto.setMaterielDescr(materialDto.getItemInfo()); // 物料描述
        Integer materielDescrLength = getOracleLengthFromMysqlVarchar(dto.getMaterielDescr());
        if (materielDescrLength > 240) {
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料描述的字段长度超过了240个字符");
        }
        dto.setPamCode(materialDto.getPamCode());
        dto.setErpCode(materialDto.getItemCode());
        dto.setMaterielType(materialDto.getMaterialType()); //物料小类
        dto.setMaterialClassification(materialDto.getMaterialClassification()); //物料大类
        dto.setName(materialDto.getName()); //名称
        dto.setModel(materialDto.getModel()); //规格型号
        dto.setBrand(materialDto.getBrand()); //品牌
        dto.setMachiningPartType(materialDto.getMachiningPartType());//加工件分类
        dto.setMaterial(materialDto.getMaterial());//材料
        dto.setMaterialProcessing(materialDto.getMaterialProcessing());//材质处理
        dto.setUnitWeight(materialDto.getUnitWeight());//单位重量

        dto.setCodingMiddleClass(materialDto.getCodingMiddleclass()); //物料中类
        dto.setFigureNumber(materialDto.getFigureNumber()); //图号
        dto.setChartVersion(materialDto.getChartVersion()); //图纸版本
        dto.setBrandMaterialCode(materialDto.getBrandMaterialCode());  //品牌商物料编码
        dto.setOrSparePartsMask(materialDto.getOrSparePartsMask()); //备件标识
        dto.setPerchasingLeadtime(StringUtils.isEmpty(materialDto.getBuyerRound()) ? null :
                Long.parseLong(materialDto.getBuyerRound())); //采购提前期
        dto.setMinPerchaseQuantity(materialDto.getMinimumOrderQuantity()); //最小订货量（最小采购量）
        dto.setMinPackageQuantity(StringUtils.isEmpty(materialDto.getFixedLotMultiplier()) ? null :
                Long.parseLong(materialDto.getFixedLotMultiplier())); //固定批次增加（最小包装量）
        return dto;
    }

    private boolean checkDesignDetailDto(MilepostDesignPlanDetailDto dto, List<DictDto> dicts,
                                         HashBasedTable<String, String, MerielSubclassVO> merielClassTable,
                                         Set<String> orSparePartsMaskSet, Map<String, String> unitNameCodeMap, Map<String, DictDto> materialTypeMap,
                                         boolean result, List<String> repetition1, List<String> repetition2, List<String> repetition3,
                                         List<BrandMaintenance> brandMaintenanceList, boolean isHand, Boolean isOpenDeliveryTime) {
        logger.info("materialCategory：{}", dto.getMaterialCategory());
        if (StringUtils.isNotEmpty(dto.getMaterialCategory())) {
            DictDto materialTypeDict = materialTypeMap.get(dto.getMaterialCategory());
            if (null == materialTypeDict) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，"))
                        + "物料类型为字典项material_type中序号不为“1”对应的值");
            }
            if (materialTypeDict != null && ("装配件".equals(materialTypeDict.getName()) || "虚拟件".equals(materialTypeDict.getName()))) {
                //校验物料类型为“装配件”的数据
                result = checkFather(dto, unitNameCodeMap, result);
            } else {
                //校验物料类型为“非装配件”的数据
                result = checkSon(dto, merielClassTable, dicts, orSparePartsMaskSet, unitNameCodeMap, result, repetition1, repetition2, repetition3,
                        brandMaintenanceList, isHand, isOpenDeliveryTime);
            }
        } else {
            result = false;
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料类型不能为空");
        }
        return result;
    }

    private boolean checkDesignDetailDtoWBS(List<BrandMaintenance> brandMaintenanceList, MilepostDesignPlanDetailDto dto, List<DictDto> dicts,
                                            HashBasedTable<String, String, MerielSubclassVO> merielClassTable,
                                            Set<String> orSparePartsMaskSet, Map<String, String> unitNameCodeMap,
                                            Map<String, DictDto> materialTypeMap,
                                            List<String> brandMaintenanceCnList,
                                            List<String> brandMaintenanceEnList, boolean result,
                                            List<String> repetition1, List<String> repetition2,
                                            List<String> repetition3, String wbsSummaryCode, Long storageId, Long unitId) {
        logger.info("materialCategory：{}", dto.getMaterialCategory());
        if (StringUtils.isNotEmpty(dto.getMaterialCategory())) {
            DictDto materialTypeDict = materialTypeMap.get(dto.getMaterialCategory());
            if (null == materialTypeDict) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，"))
                        + "物料类型为字典项material_type中序号不为“1”对应的值");
            }
            if (materialTypeDict != null && ("装配件".equals(materialTypeDict.getName()) || "虚拟件".equals(materialTypeDict.getName()))) {
                //校验物料类型为“装配件”的数据
                result = checkFather(dto, unitNameCodeMap, result);
            } else {
                //校验物料类型为“非装配件”的数据
                result = checkSonWBS(brandMaintenanceList, dto, merielClassTable, dicts, orSparePartsMaskSet, unitNameCodeMap, brandMaintenanceCnList,
                        brandMaintenanceEnList, result, repetition1, repetition2, repetition3, wbsSummaryCode, storageId, unitId);
            }
            if (StringUtils.isEmpty(dto.getChartVersion())) {
                dto.setChartVersion("0");
            }
        } else {
            result = false;
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料类型不能为空");
        }
        return result;
    }

    private Boolean checkFather(MilepostDesignPlanDetailDto dto, Map<String, String> unitNameCodeMap, boolean result) {
        List<String> validResultList = StringUtils.isEmpty(dto.getValidResult()) ? new ArrayList<>()
                : Arrays.asList(dto.getValidResult().split("，"));
        //校验必填项
        if (StringUtils.isEmpty(dto.getName())) {
            result = false;
            validResultList.add("名称不能为空");
        }
        if (Objects.isNull(dto.getNumber())) {
            result = false;
            validResultList.add("数量不能为空");
        }
        if (StringUtils.isEmpty(dto.getUnit()) || !unitNameCodeMap.containsKey(dto.getUnit())) {
            result = false;
            validResultList.add("物料[" + dto.getName() + " " + dto.getModel() + "]的单位不能为空且必须为字典项measurement_unit的值");
        } else {
            dto.setUnitCode(unitNameCodeMap.get(dto.getUnit()));
        }
        dto.setValidResult(Joiner.on("，").join(validResultList));

        return result;
    }

    private Boolean checkSon(MilepostDesignPlanDetailDto dto, HashBasedTable<String, String, MerielSubclassVO> merielClassTable, List<DictDto> dicts,
                             Set<String> orSparePartsMaskSet, Map<String, String> unitNameCodeMap, boolean result,
                             List<String> repetition1, List<String> repetition2, List<String> repetition3,
                             List<BrandMaintenance> brandMaintenanceList, boolean isHand, Boolean isOpenDeliveryTime) {
        // 判重
        result = this.checkRepetition(dto, result, repetition1, repetition2, repetition3);
        return commonValidate(dto, merielClassTable, dicts, orSparePartsMaskSet, unitNameCodeMap, result, brandMaintenanceList, isHand,
                isOpenDeliveryTime);
    }

    private Boolean checkSonWBS(List<BrandMaintenance> brandMaintenanceList, MilepostDesignPlanDetailDto dto,
                                HashBasedTable<String, String, MerielSubclassVO> merielClassTable, List<DictDto> dicts,
                                Set<String> orSparePartsMaskSet, Map<String, String> unitNameCodeMap,
                                List<String> brandMaintenanceCnList, List<String> brandMaintenanceEnList,
                                boolean result, List<String> repetition1, List<String> repetition2, List<String> repetition3,
                                String wbsSummaryCode, Long storageId, Long unitId) {
        // 判重
        result = this.checkRepetitionWBS(dto, result, repetition1, repetition2, repetition3, wbsSummaryCode);
        return commonValidateWbs(brandMaintenanceList, dto, merielClassTable, dicts, orSparePartsMaskSet, unitNameCodeMap, brandMaintenanceCnList,
                brandMaintenanceEnList, result, storageId, unitId);
    }


    private Boolean commonValidate(MilepostDesignPlanDetailDto dto, HashBasedTable<String, String, MerielSubclassVO> merielClassTable,
                                   List<DictDto> dicts, Set<String> orSparePartsMaskSet, Map<String, String> unitNameCodeMap,
                                   boolean result, List<BrandMaintenance> brandMaintenanceList, boolean isHand, Boolean isOpenDeliveryTime) {
        List<String> validResultList = StringUtils.isEmpty(dto.getValidResult()) ? new ArrayList<>()
                : Arrays.asList(dto.getValidResult().split("，"));
        //校验必填项
        if (StringUtils.isEmpty(dto.getCodingMiddleClass())) {
            result = false;
            validResultList.add("物料中类不能为空");
        } else {
            if (!merielClassTable.columnKeySet().contains(dto.getCodingMiddleClass())) {
                result = false;
                validResultList.add("物料[" + dto.getName() + " " + dto.getModel() + "]的物料中类不正确");
            }
        }
        if (StringUtils.isEmpty(dto.getMaterielType())) {
            result = false;
            validResultList.add("物料小类不能为空");
        } else {
            if (merielClassTable.rowKeySet().contains(dto.getMaterielType())) {
                MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(), dto.getCodingMiddleClass());
                if (merielSubclassVO == null) {
                    result = false;
                    validResultList.add("物料小类对应中类不存在");
                } else {
                    dto.setMaterialClassification(merielSubclassVO.getBigValue());
                }
            } else {
                result = false;
                validResultList.add("物料小类不存在");
            }
        }
        if (StringUtils.isEmpty(dto.getBrand())) {
            result = false;
            validResultList.add("品牌不能为空");
        } else {
            if (dto.getBrand().length() > 150) {
                result = false;
                validResultList.add("品牌字符长度不能超过150");
            }
            dto.setBrand(dto.getBrand().toUpperCase(Locale.ROOT));
            if (Objects.equals(dto.getBrand(), "无") || Objects.equals(dto.getBrand(), "NONE")) {
                result = false;
                validResultList.add("品牌不能为无或NONE");
            } else {
                List<BrandMaintenance> maintenanceList = brandMaintenanceList.stream().filter(brand ->
                                (isHand ? (Objects.equals(brand.getBrandName(), dto.getBrand()))
                                        : (Objects.equals(brand.getBrandNameCn(), dto.getBrand())
                                        || Objects.equals(brand.getBrandNameEn(), dto.getBrand())
                                        || Objects.equals(brand.getBrandName(), dto.getBrand())))
                                        && (Boolean.FALSE.equals(brand.getDeletedFlag()) || Objects.equals(0, brand.getBrandStatus())))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(maintenanceList)) {
                    result = false;
                    validResultList.add("品牌名称：" + dto.getBrand() + "在品牌表中不存在");
                } else {
                    dto.setBrand(maintenanceList.get(0).getBrandName());
                }
            }
        }
        if (StringUtil.isNull(dto.getName())) {
            result = false;
            validResultList.add("名称不能为空");
        }
        if (StringUtil.isNull(dto.getModel())) {
            result = false;
            validResultList.add("规格/型号不能为空");
        }
        if (Objects.isNull(dto.getNumber())) {
            result = false;
            validResultList.add("数量不能为空");
        }
        if (StringUtils.isEmpty(dto.getUnit()) || !unitNameCodeMap.containsKey(dto.getUnit())) {
            result = false;
            validResultList.add("单位不能为空且必须为字典项measurement_unit的值");
        } else {
            dto.setUnitCode(unitNameCodeMap.get(dto.getUnit()));
        }
        if (StringUtils.isNotEmpty(dto.getOrSparePartsMask())) {
            if (!orSparePartsMaskSet.contains(dto.getOrSparePartsMask())) {
                result = false;
                validResultList.add("备件标识不正确");
            }
        }
        //当物料中类为”机械加工件、外协加工“时，图号，图纸版本号，加工分类，材质，单件重量(Kg)，表面处理为必填项，若未填写，在数据校验环节需要弹出提示“导入文件有误,请重新下载模板填写数据”
        MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(), dto.getCodingMiddleClass());

        if (merielSubclassVO != null && Objects.equals(3, merielSubclassVO.getCheckType())) {
            Map<String, String> errorMap = new HashMap<>();
            if (!org.springframework.util.StringUtils.hasText(dto.getMachiningPartType())) {
                errorMap.put(ERROR_JGJFL_KEY, ERROR_JGJFL);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getMaterial())) {
                errorMap.put(ERROR_CZ_KEY, ERROR_CZ);
            }
            if (Objects.isNull(dto.getUnitWeight())) {
                errorMap.put(ERROR_DWZL_KEY, ERROR_DWZL);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getFigureNumber())) {
                errorMap.put(ERROR_TH_KEY, ERROR_TH);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getChartVersion())) {
                errorMap.put(ERROR_TZBBH_KEY, ERROR_TZBBH);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getMaterialProcessing())) {
                errorMap.put(ERROR_BMCL_KEY, ERROR_BMCL);
            }
            if (!dicts.stream().anyMatch(dict -> dict.getName().equals(dto.getMachiningPartType()))) {
                result = false;
                validResultList.add("物料[" + dto.getName() + " " + dto.getModel() + "]的加工件分类不正确");
            }
            if (MapUtils.isNotEmpty(errorMap)) {
                result = false;
                StringBuffer buffer = new StringBuffer();
                errorMap.entrySet().forEach(entry -> buffer.append(entry.getValue()).append("、"));
                validResultList.add("物料中类为机械加工件、外协加工时，" + buffer.toString().substring(0, buffer.length() - 1) + "不能为空");
            }
        }
        //校验导入日
        String deliveryTimeStr = dto.getDeliveryTimeStr();
        // 计划交货日期=不打勾   外购物料、看板物料 需要填写 物料到货日期；计划交货日期=打钩    外购物料、看板物料  不需要填写 物料到货日期
        if (Boolean.FALSE.equals(isOpenDeliveryTime) && ("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory()))) {
            if (StringUtils.isEmpty(deliveryTimeStr)) {
                result = false;
                validResultList.add("外购物料/看板物料,物料到货日期不能为空");
            }
        }
        if (StringUtils.isNotEmpty(deliveryTimeStr)) {
            if (DateUtils.isValidDate(deliveryTimeStr, DateUtils.FORMAT_SHORT)) {
                dto.setDeliveryTime(DateUtils.parse(deliveryTimeStr, DateUtils.FORMAT_SHORT));
            } else {
                validResultList.add("物料到货日期格式不正确");
                result = false;
            }
        }
        dto.setValidResult(Joiner.on("，").join(validResultList));
        return result;
    }

    private Boolean commonValidateWbs(List<BrandMaintenance> brandMaintenanceList, MilepostDesignPlanDetailDto dto,
                                      HashBasedTable<String, String, MerielSubclassVO> merielClassTable,
                                      List<DictDto> dicts, Set<String> orSparePartsMaskSet, Map<String, String> unitNameCodeMap,
                                      List<String> brandMaintenanceCnList, List<String> brandMaintenanceEnList,
                                      boolean result, Long storageId, Long unitId) {
        List<String> validResultList = StringUtils.isEmpty(dto.getValidResult()) ? new ArrayList<>()
                : Arrays.asList(dto.getValidResult().split("，"));
        //校验必填项
        if (StringUtils.isEmpty(dto.getCodingMiddleClass())) {
            result = false;
            validResultList.add("物料中类不能为空");
        } else {
            if (!merielClassTable.columnKeySet().contains(dto.getCodingMiddleClass())) {
                result = false;
                validResultList.add("物料[" + dto.getName() + " " + dto.getModel() + "]的物料中类不正确");
            }
        }
        if (StringUtils.isEmpty(dto.getMaterielType())) {
            result = false;
            validResultList.add("物料小类不能为空");
        } else {
            if (merielClassTable.rowKeySet().contains(dto.getMaterielType())) {
                MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(), dto.getCodingMiddleClass());
                if (merielSubclassVO == null) {
                    result = false;
                    validResultList.add("物料小类对应中类不存在");
                } else {
                    dto.setMaterialClassification(merielSubclassVO.getBigValue());
                }
            } else {
                result = false;
                validResultList.add("物料小类不存在");
            }
        }
        if (StringUtil.isNull(dto.getBrand())) {
            result = false;
            validResultList.add("品牌不能为空");
        } else {
            if (!brandMaintenanceCnList.contains(dto.getBrand()) && !brandMaintenanceEnList.contains(dto.getBrand())) {
                result = false;
                validResultList.add("在品牌表中不存在");
            }
        }
        if (StringUtil.isNull(dto.getName())) {
            result = false;
            validResultList.add("名称不能为空");
        }
        if (StringUtil.isNull(dto.getModel())) {
            result = false;
            validResultList.add("规格/型号不能为空");
        }
        if (Objects.isNull(dto.getNumber())) {
            result = false;
            validResultList.add("数量不能为空");
        }
        if (StringUtils.isEmpty(dto.getUnit()) || !unitNameCodeMap.containsKey(dto.getUnit())) {
            result = false;
            validResultList.add("单位不能为空且必须为字典项measurement_unit的值");
        } else {
            dto.setUnitCode(unitNameCodeMap.get(dto.getUnit()));
        }
        if (StringUtils.isNotEmpty(dto.getOrSparePartsMask())) {
            if (!orSparePartsMaskSet.contains(dto.getOrSparePartsMask())) {
                result = false;
                validResultList.add("备件标识不正确");
            }
        }
        //当物料中类为”机械加工件、外协加工“时，图号，图纸版本号，加工分类，材质，单件重量(Kg)，表面处理为必填项，若未填写，在数据校验环节需要弹出提示“导入文件有误,请重新下载模板填写数据”
        MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(), dto.getCodingMiddleClass());

        if (merielSubclassVO != null && Objects.equals(3, merielSubclassVO.getCheckType())) {
            Map<String, String> errorMap = new HashMap<>();
            if (!org.springframework.util.StringUtils.hasText(dto.getMachiningPartType())) {
                errorMap.put(ERROR_JGJFL_KEY, ERROR_JGJFL);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getMaterial())) {
                errorMap.put(ERROR_CZ_KEY, ERROR_CZ);
            }
            if (Objects.isNull(dto.getUnitWeight())) {
                errorMap.put(ERROR_DWZL_KEY, ERROR_DWZL);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getFigureNumber())) {
                errorMap.put(ERROR_TH_KEY, ERROR_TH);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getChartVersion())) {
                errorMap.put(ERROR_TZBBH_KEY, ERROR_TZBBH);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getMaterialProcessing())) {
                errorMap.put(ERROR_BMCL_KEY, ERROR_BMCL);
            }
            if (!dicts.stream().anyMatch(dict -> dict.getName().equals(dto.getMachiningPartType()))) {
                result = false;
                validResultList.add("物料[" + dto.getName() + " " + dto.getModel() + "]的加工件分类不正确");
            }
            if (MapUtils.isNotEmpty(errorMap)) {
                result = false;
                StringBuffer buffer = new StringBuffer();
                errorMap.entrySet().forEach(entry -> buffer.append(entry.getValue()).append("、"));
                validResultList.add("物料中类为机械加工件、外协加工时，" + buffer.toString().substring(0, buffer.length() - 1) + "不能为空");
            }
        }
        //校验导入日
        // 启用WBS必须填写“物料到货日期”
        String deliveryTimeStr = dto.getDeliveryTimeStr();
        if (StringUtils.isEmpty(deliveryTimeStr)) {
            result = false;
            validResultList.add("物料到货日期不能为空");
        } else {
            if (DateUtils.isValidDate(deliveryTimeStr, DateUtils.FORMAT_SHORT)) {
                dto.setDeliveryTime(DateUtils.parse(deliveryTimeStr, DateUtils.FORMAT_SHORT));
            } else {
                result = false;
                validResultList.add("物料到货日期格式不正确");
            }
        }
        dto.setValidResult(Joiner.on("，").join(validResultList));

        result = materialCheck(brandMaintenanceList, dto, storageId, unitId, result);

        return result;
    }

    private Boolean checkRepetition(MilepostDesignPlanDetailDto dto, boolean result, List<String> repetition1,
                                    List<String> repetition2, List<String> repetition3) {
        if ("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory())) {
            if ("机械加工件".equals(dto.getCodingMiddleClass())) { //物料中类+品牌+图号 检索相同编码
                if (ListUtils.isNotEmpty(repetition1) && repetition1.contains(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getFigureNumber())) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "根据物料唯一性原则，模板中存在重复数据！");
                }
                repetition1.add(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getFigureNumber());
            } else {
                if (null != dto.getBrandMaterialCode()) { //品牌商物料编码存在  物料中类+品牌+品牌商物料编码 检索相同编码
                    if (ListUtils.isNotEmpty(repetition2) && repetition2.contains(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getBrandMaterialCode())) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "根据物料唯一性原则，模板中存在重复数据！");
                    }
                    repetition2.add(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getBrandMaterialCode());
                } else { //品牌商物料编码不存在  物料中类+品牌+规格/型号 检索相同编码
                    if (ListUtils.isNotEmpty(repetition3) && repetition3.contains(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getModel())) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "根据物料唯一性原则，模板中存在重复数据！");
                    }
                    repetition3.add(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getModel());
                }
            }
        }
        return result;
    }

    private Boolean checkRepetitionWBS(MilepostDesignPlanDetailDto dto, boolean result, List<String> repetition1,
                                       List<String> repetition2, List<String> repetition3, String wbsSummaryCode) {
        if ("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory())) {
            if ("机械加工件".equals(dto.getCodingMiddleClass())) { //物料中类+品牌+图号 检索相同编码
                if (ListUtils.isNotEmpty(repetition1) && repetition1.contains(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getFigureNumber() + ":" + wbsSummaryCode)) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "根据物料唯一性原则，模板中存在重复数据！");
                }
                repetition1.add(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getFigureNumber() + ":" + wbsSummaryCode);
            } else {
                if (null != dto.getBrandMaterialCode()) { //品牌商物料编码存在  物料中类+品牌+品牌商物料编码 检索相同编码
                    if (ListUtils.isNotEmpty(repetition2) && repetition2.contains(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getBrandMaterialCode() + ":" + wbsSummaryCode)) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "根据物料唯一性原则，模板中存在重复数据！");
                    }
                    repetition2.add(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getBrandMaterialCode() + ":" + wbsSummaryCode);
                } else { //品牌商物料编码不存在  物料中类+品牌+规格/型号 检索相同编码
                    if (ListUtils.isNotEmpty(repetition3) && repetition3.contains(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getModel() + ":" + wbsSummaryCode)) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "根据物料唯一性原则，模板中存在重复数据！");
                    }
                    repetition3.add(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getModel() + ":" + wbsSummaryCode);
                }
            }
        }
        return result;
    }

    private void ksGenerateMaterielDescr(MilepostDesignPlanDetailDto dto) {
        if ("装配件".equals(dto.getMaterialCategory())) { //先写死"装配件",后续修改
            dto.setMaterielDescr(dto.getName());
        } else {
            StringBuffer materielDescr = new StringBuffer();
            if (StringUtils.isNotEmpty(dto.getName())) {
                materielDescr.append(dto.getName());
                materielDescr.append("#");
            }
            if (StringUtils.isNotEmpty(dto.getFigureNumber())) {
                materielDescr.append(dto.getFigureNumber());
                materielDescr.append("#");
            }
            if (StringUtils.isNotEmpty(dto.getChartVersion())) {
                materielDescr.append(dto.getChartVersion());
                materielDescr.append("#");
            }
            if (StringUtils.isNotEmpty(dto.getModel())) {
                materielDescr.append(dto.getModel());
                materielDescr.append("#");
            }
            if (StringUtils.isNotEmpty(dto.getBrand())) {
                materielDescr.append(dto.getBrand());
                materielDescr.append("#");
            }
            if (StringUtils.isNotEmpty(dto.getBrandMaterialCode())) {
                materielDescr.append(dto.getBrandMaterialCode());
            }
            dto.setMaterielDescr(materielDescr.toString());
        }

        Integer materielDescrLength = getOracleLengthFromMysqlVarchar(dto.getMaterielDescr());
        if (materielDescrLength > 240) {
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : (dto.getValidResult() + "，")) + "物料描述的字段长度超过了240个字符");
        }
    }

    @Override
    public void saveMilepostDesignMiddle(MilepostDesignPlanMiddle milepostDesignPlanMiddle) {
        middleMapper.insertSelective(milepostDesignPlanMiddle);
    }

    @Override
    public List<MilepostDesignPlanDetailDto> matchingMaterialCodeEstimate(List<MilepostDesignPlanDetailImportExcelVo> detailExcelVos,
                                                                          String remindType) throws UnsupportedEncodingException {
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = BeanConverter.copy(detailExcelVos,
                MilepostDesignPlanDetailDto.class);
        // 获取计量单位
        Map<String, String> unitMap =
                ltcDictService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));
        // 获取类别列表数据
        List<MerielSubclassVO> merielSubclassList =
                ctcExtService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUKAMIA.getCode());
        // 物料类别Table数据（row小类，column中类，value实体类）
        HashBasedTable<String, String, MerielSubclassVO> merielClassTable = HashBasedTable.create();
        for (MerielSubclassVO merielSubclassVO : merielSubclassList) {
            merielClassTable.put(merielSubclassVO.getValue(), merielSubclassVO.getMiddileValue(), merielSubclassVO);
        }

        if (ListUtils.isNotEmpty(designPlanDetailDtos)) {
            Long organizationId =
                    ctcExtService.getOrganizationIdByProjectId(designPlanDetailDtos.get(0).getProjectId());
            for (MilepostDesignPlanDetailDto dto : designPlanDetailDtos) {
                generateMaterielDescr(dto);//物料描述
                checkImport(dto, remindType, merielClassTable); //检查导入的excel
                //
                matchingMaterialUnitCode(dto, remindType, unitMap); //匹配物料单位编码
                matchingMaterialCodeAsync(dto, organizationId, remindType, unitMap);
            }
        }

        return designPlanDetailDtos;
    }

    private void generateMaterielDescr(MilepostDesignPlanDetailDto dto) {
        StringBuffer materielDescr = new StringBuffer();
        if (StringUtils.isNotEmpty(dto.getBrand())) {
            materielDescr.append(dto.getBrand());
            materielDescr.append(" ");
        }
        if (StringUtils.isNotEmpty(dto.getName())) {
            materielDescr.append(dto.getName());
            materielDescr.append(" ");
        }
        if (StringUtils.isNotEmpty(dto.getModel())) {
            materielDescr.append(dto.getModel());
        }
        dto.setMaterielDescr(materielDescr.toString());
    }

    private void checkImport(MilepostDesignPlanDetailDto dto, String remindType, HashBasedTable<String, String,
            MerielSubclassVO> merielClassTable) {
        if ("excel".equalsIgnoreCase(remindType)) {
            StringBuffer stringBuffer = new StringBuffer(StringUtils.isEmpty(dto.getValidResult()) ? "" :
                    dto.getValidResult());
            if (StringUtils.isEmpty(dto.getMaterielType())) {
                stringBuffer.append("，").append(ErrorCode.CTC_CODING_SUBCLASS_NOT_NULL.getMsg());
            } else {
                //校验物料小类是否有对应的erp编码规则
                Map<String, MerielSubclassVO> map = merielClassTable.row(dto.getMaterielType());
                if (map.isEmpty()) {
                    stringBuffer.append("，").append(ErrorCode.CTC_MATERIAL_CLASS_TYPE_ILLEGAL.getMsg());
                } else {
                    //设置物料大类值
                    for (Map.Entry<String, MerielSubclassVO> entry : map.entrySet()) {
                        if (!Objects.equals("物料", entry.getValue().getBigValue())) {
                            stringBuffer.append("，").append(ErrorCode.CTC_CODING_SUBCLASS_NOT_TRUE.getMsg());
                        } else {
                            dto.setMaterialClassification(entry.getValue().getBigValue());

                        }
                    }

                }
            }
            if (StringUtils.isEmpty(dto.getMaterielDescr())) {
                stringBuffer.append("，").append(ErrorCode.CTC_MATERIEL_DESCR_NOT_NULL.getMsg());
            }
            if (dto.getNumber() == null) {
                stringBuffer.append("，").append(ErrorCode.CTC_NUMBER_NOT_NULL.getMsg());
            }
            if (StringUtils.isEmpty(dto.getUnit())) {
                stringBuffer.append("，").append(ErrorCode.CTC_MATERIAL_UNIT_NOT_NULL.getMsg());
            }
            if (dto.getDeliveryTime() == null) {
                stringBuffer.append("，").append(ErrorCode.CTC_MILEPOST_DESIGN_PLAN_DELIVERY_TIME_NULL.getMsg());
            }
            dto.setValidResult(stringBuffer.toString());
        } else {
            Asserts.notEmpty(dto.getMaterielType(), ErrorCode.CTC_CODING_SUBCLASS_NOT_NULL);
            Asserts.notEmpty(dto.getMaterielDescr().trim(), ErrorCode.CTC_MATERIEL_DESCR_NOT_NULL);
            Asserts.notEmpty(dto.getNumber(), ErrorCode.CTC_NUMBER_NOT_NULL);
            Asserts.notEmpty(dto.getUnit(), ErrorCode.CTC_MATERIAL_UNIT_NOT_NULL);
            Asserts.notEmpty(dto.getDeliveryTime(), ErrorCode.CTC_MILEPOST_DESIGN_PLAN_DELIVERY_TIME_NULL);

            //校验物料小类是否有对应的erp编码规则
            Map<String, MerielSubclassVO> map = merielClassTable.row(dto.getMaterielType());
            Asserts.isTrue(!map.isEmpty(), ErrorCode.CTC_MATERIAL_CLASS_TYPE_ILLEGAL);
            //设置物料大类值
            for (Map.Entry<String, MerielSubclassVO> entry : map.entrySet()) {
                dto.setMaterialClassification(entry.getValue().getBigValue());
            }
        }
    }

    public void matchingMaterialUnitCode(MilepostDesignPlanDetailDto dto, String remindType,
                                         Map<String, String> unitMap) {
        if ("excel".equalsIgnoreCase(remindType)) {
            StringBuffer stringBuffer = new StringBuffer(StringUtils.isEmpty(dto.getValidResult()) ? "" :
                    dto.getValidResult());
            if (StringUtils.isEmpty(dto.getUnit())) {
                stringBuffer.append("，").append(ErrorCode.CTC_MATERIAL_UNIT_NOT_NULL.getMsg());
            } else {
                if (unitMap.containsKey(dto.getUnit())) {
                    dto.setUnitCode(unitMap.get(dto.getUnit()));
                } else {
                    stringBuffer.append("，").append(ErrorCode.BASEDATA_MATERIAL_UNIT_NOT_FIND.getMsg());
                }
            }
            dto.setValidResult(stringBuffer.toString());
        } else {
            Asserts.notEmpty(dto.getUnit(), ErrorCode.CTC_MATERIAL_UNIT_NOT_NULL);
            Asserts.isTrue(unitMap.containsKey(dto.getUnit()), ErrorCode.BASEDATA_MATERIAL_UNIT_NOT_FIND);
            dto.setUnitCode(unitMap.get(dto.getUnit()));
        }
    }

    @Override
    public AsyncRequestResultBaseData matchingMaterialCodeAsync(MilepostDesignPlanDetailDto dto, Long organizationId,
                                                                String remindType, Map<String, String> unitMap) throws UnsupportedEncodingException {
        AsyncRequestResultBaseData asyncRequestResultBaseData = new AsyncRequestResultBaseData();
        asyncRequestResultBaseData = asyncRequestResultBaseDataService.add(asyncRequestResultBaseData);
        applicationEventPublisher.publishEvent(new MatchingMaterialCodeBaseDataEvent(this, dto, organizationId,
                remindType, unitMap));
        return asyncRequestResultBaseData;
    }

    @Override
    public boolean matchingMaterialCode(MilepostDesignPlanDetailDto dto) {
        boolean matchFlag = false;

        //判断当物料小类为”机械加工件“时，需要用型号+名称+品牌+加工件分类+材质+单位重量+材质处理 7个条件来判断是否为同一个物料
        if (MACHINED_PART.equals(dto.getMaterielType())) {
            MaterialQuery query = new MaterialQuery();
            query.setName(dto.getName());
            query.setModel(dto.getModel());
            query.setBrand(dto.getBrand());
            query.setMaterialType(dto.getMaterielType());
            query.setMachiningPartType(dto.getMaterielType());
            query.setUnitWeight(dto.getUnitWeight());
            query.setMaterialProcessing(dto.getMaterialProcessing());
            query.setMaterial(dto.getMaterial());
            query.setDelistFlag(Boolean.FALSE);
            List<MaterialDto> materials = materialService.getMaterialList(query);
            if (!CollectionUtils.isEmpty(materials)) {
                return buildMilepostDesignPlanDetailDto(dto, materials,
                        ErpCodeSourceBaseData.INVENTORY_ORGANIZATION.code(), Boolean.TRUE);
            }
            dto.setPamCode(generateMaterialPAMCode());
            dto.setNumberOfMatchingResults(0);
            return matchFlag;
        } else {
            //匹配MA2(型号+名称+品牌+物料小类:精确匹配)
            if (StringUtils.isNotEmpty(dto.getModel())) {
                List<MaterialDto> materialDtosFromMA2ByModel =
                        getMaterial(dto.getName(), dto.getModel(), dto.getBrand(), "MA2", null, dto.getMaterielType());
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMA2ByModel,
                        ErpCodeSourceBaseData.INVENTORY_ORGANIZATION.code(), Boolean.TRUE);
                if (matchFlag) {
                    return matchFlag;
                }
            }

            //匹配MM0(型号+名称+品牌+物料小类:精确匹配)
            if (StringUtils.isNotEmpty(dto.getModel())) {
                List<MaterialDto> materialDtosFromMM0ByModel =
                        getMaterial(dto.getName(), dto.getModel(), dto.getBrand(), null,
                                Constants.MMO_ORGANIZATION_ID, dto.getMaterielType());
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMM0ByModel,
                        ErpCodeSourceBaseData.MAIN_ORGANIZATION.code(), Boolean.TRUE);
                if (matchFlag) {
                    return matchFlag;
                }
            }

            // 模糊匹配规格型号
            if (StringUtils.isNotEmpty(dto.getModel())) {
                List<MaterialDto> materialDtosFromMA2ByModel = getMaterial("MA2", null, null, dto.getModel());
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMA2ByModel,
                        ErpCodeSourceBaseData.INVENTORY_ORGANIZATION.code(), Boolean.FALSE);
                if (matchFlag) {
                    return matchFlag;
                }

                List<MaterialDto> materialDtosFromMM0ByModel = getMaterial(null, null, null, dto.getModel());
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMM0ByModel,
                        ErpCodeSourceBaseData.MAIN_ORGANIZATION.code(), Boolean.FALSE);
                if (matchFlag) {
                    return matchFlag;
                }
            }

            // 模糊匹配物料名称
            if (StringUtils.isNotEmpty(dto.getModel())) {
                List<MaterialDto> materialDtosFromMA2ByModel = getMaterial("MA2", null, dto.getName(), null);
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMA2ByModel,
                        ErpCodeSourceBaseData.INVENTORY_ORGANIZATION.code(), Boolean.FALSE);
                if (matchFlag) {
                    return matchFlag;
                }

                List<MaterialDto> materialDtosFromMM0ByModel = getMaterial(null, null, dto.getName(), null);
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMM0ByModel,
                        ErpCodeSourceBaseData.MAIN_ORGANIZATION.code(), Boolean.FALSE);
                if (matchFlag) {
                    return matchFlag;
                }
            }
        }
        return matchFlag;
    }

    private boolean buildMilepostDesignPlanDetailDto(MilepostDesignPlanDetailDto dto, List<MaterialDto> materialDtos,
                                                     Integer erpCodeSource, boolean needUpdateFlag) {
        if (!ListUtil.isEmpty(materialDtos) && materialDtos.size() == 1) {
            dto.setNumberOfMatchingResults(1);
            dto.setMatchingCondition(1);
            dto.setErpCodeSource(erpCodeSource);

            if (!needUpdateFlag) {
                return Boolean.TRUE;
            }

            if (materialDtos.get(0).getPamCode() != null) {
                //导入的信息以系统存在的为准
                dto.setPamCode(materialDtos.get(0).getPamCode());
                dto.setMaterielDescr(materialDtos.get(0).getItemInfo());
                dto.setBrand(materialDtos.get(0).getBrand());
                dto.setModel(materialDtos.get(0).getModel());
                dto.setName(materialDtos.get(0).getName());
                dto.setMaterialClassification(materialDtos.get(0).getMaterialClassification());//物料大类
                dto.setMaterielType(materialDtos.get(0).getMaterialType());//物料小类
                dto.setUnitCode(materialDtos.get(0).getUnit());
                dto.setMachiningPartType(materialDtos.get(0).getMachiningPartType());
                dto.setMaterial(materialDtos.get(0).getMaterial());
                dto.setUnitWeight(materialDtos.get(0).getUnitWeight());
                dto.setMaterialProcessing(materialDtos.get(0).getMaterialProcessing());
            } else {
                dto.setPamCode(generateMaterialPAMCode());
            }
            dto.setErpCode(materialDtos.get(0).getItemCode());

            return Boolean.TRUE;
        } else if (!ListUtil.isEmpty(materialDtos) && materialDtos.size() > 1) {
            dto.setNumberOfMatchingResults(materialDtos.size());
            dto.setMatchingCondition(1);

            return Boolean.FALSE;
        }

        return Boolean.FALSE;
    }

    private List<MaterialDto> getMaterial(String materialName, String materialModel, String materialBrand,
                                          String organizationCode, Long organizationId, String materielType) {
        MaterialQuery query = new MaterialQuery();
        query.setName(materialName);
        query.setModel(materialModel);
        query.setBrand(materialBrand);
        query.setOrganizationId(organizationId);
        query.setOrganizationCode(organizationCode);
        query.setMaterialType(materielType);
        query.setDelistFlag(Boolean.FALSE);
        List<MaterialDto> materialDtoList = materialService.getMaterialList(query);
        return materialDtoList;
    }

    private List<MaterialDto> getMaterial(String organizationCode, Long organizationId, String fuzzyName,
                                          String fuzzyModel) {
        MaterialQuery query = new MaterialQuery();
        query.setOrganizationCode(organizationCode);
        query.setOrganizationId(organizationId);
        query.setFuzzyName(fuzzyName);
        query.setFuzzyModel(fuzzyModel);
        query.setDelistFlag(Boolean.FALSE);
        List<MaterialDto> materialDtoList = materialService.getMaterialList(query);
        return materialDtoList;
    }

    @Override
    public void batchInsert(List<MilepostDesignPlanMiddle> milepostDesignPlanMiddleList) {
        middleExtMapper.batchInsert(milepostDesignPlanMiddleList);
    }

    @Override
    public Integer updateMiddle(Long markId) {
        return middleExtMapper.updateMilepostDesignPlanMiddle(markId);
    }

    @Override
    public List<Material> checkMaterialAd1(String codingMiddleClass, String brand, String figureNumber,
                                           Long organizationId) {
        return materialExtMapper.checkMaterialAd1(codingMiddleClass, brand, figureNumber, organizationId);
    }

    @Override
    public List<Material> checkMaterialAd2(String codingMiddleClass, String brand, String model, Long organizationId) {
        return materialExtMapper.checkMaterialAd2(codingMiddleClass, brand, model, organizationId);
    }

    @Override
    public List<Material> checkMaterialAd3(String codingMiddleClass, String brand, String brandMaterialCode,
                                           Long organizationId) {
        return materialExtMapper.checkMaterialAd3(codingMiddleClass, brand, brandMaterialCode, organizationId);
    }

    @Override
    public List<Material> checkMaterialAd4(String codingMiddleClass, String brand, String figureNumber,
                                           Long organizationId) {
        return materialExtMapper.checkMaterialAd4(codingMiddleClass, brand, figureNumber, organizationId);
    }

    @Override
    public List<Material> checkMaterialAd5(String codingMiddleClass, String brand, String model, Long organizationId) {
        return materialExtMapper.checkMaterialAd5(codingMiddleClass, brand, model, organizationId);
    }

    @Override
    public List<Material> checkMaterialAd6(String codingMiddleClass, String brand, String brandMaterialCode,
                                           Long organizationId) {
        return materialExtMapper.checkMaterialAd6(codingMiddleClass, brand, brandMaterialCode, organizationId);
    }

    private class PersionThread implements Callable {
        private List<MilepostDesignPlanDetailDto> list;
        private Long storageId;
        private CountDownLatch begin;
        private CountDownLatch end;
        private Map<String, String> unitCodeNameMap;

        public PersionThread(List<MilepostDesignPlanDetailDto> list, CountDownLatch begin, CountDownLatch end,
                             Long storageId, Map<String, String> unitCodeNameMap) {
            this.list = list;
            this.begin = begin;
            this.end = end;
            this.storageId = storageId;
            this.unitCodeNameMap = unitCodeNameMap;
        }

        @Override
        public List<MilepostDesignPlanDetailDto> call() throws Exception {
            try {
                if (list != null && !list.isEmpty()) {
                    for (MilepostDesignPlanDetailDto dto : list) {
                        checkKsMaterial(dto, storageId, unitCodeNameMap);
                    }
                    return list;
                }
                // 执行完让线程直接进入等待
                begin.await();

            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                // 当一个线程执行完 了计数要减一不然这个线程会被一直挂起
                end.countDown();
            }
            return null;
        }

    }

    @Override
    public Long getIdByOrganizationIdAndItemCode(Long organizationId, String itemCode) {
        return materialExtMapper.getIdByOrganizationIdAndItemCode(organizationId, itemCode);
    }

    @Override
    public List<String> getNotSuccessErpCodeList(List<String> erpCodeList, Long organizationId) {
        return materialExtMapper.getNotSuccessErpCodeList(erpCodeList, organizationId);
    }

    @Override
    public Map<String, Object> getActivityCodeByOrganizationIdAndItemCode(Long organizationId, List<String> itemCodes) {
        Asserts.notEmpty(organizationId, ErrorCode.BASEDATA_ORGANIZATION_ID_NOT_NULL);
        Map<String, Object> map = new HashMap<>();
        if (ListUtils.isNotEmpty(itemCodes)) {
            MaterialExample example = new MaterialExample();
            MaterialExample.Criteria criteria = example.createCriteria();
            criteria.andOrganizationIdEqualTo(organizationId).andItemCodeIn(itemCodes).andDeleteFlagEqualTo(false);
            List<Material> materialList = materialMapper.selectByExample(example);
            Guard.notNullOrEmpty(materialList, ErrorCode.BASEDATA_ITEM_CODE_MATERIAL_NOT_FIND.getMsg());
            map = materialList.stream().collect(Collectors.toMap(Material::getItemCode, this::getActivityCodeByOrganizationIdAndItemCode, (key1,
                                                                                                                                           key2) -> key1));
        }
        return map;
    }

    private String getActivityCodeByOrganizationIdAndItemCode(Material material) {
        String activityCode = material.getActivityCode();
        if (StringUtils.isEmpty(activityCode)) {
            activityCode = materialExtMapper.getActivityCodeByOrganizationIdAndItemCode(material.getOrganizationId(), material.getItemCode());
            Assert.hasLength(activityCode, "物料编码" + material.getItemCode() + "不是有效的关联活动事项!");
        }
        return activityCode;
    }

    @Override
    public void importAssemblyPartsAndBudgetLayer(List<ImportAssemblyPartsAndBudgetLayerExcelVo> detailExcelVOList) {
        List<ImportAssemblyPartsAndBudgetLayerExcelVo> resultList = new ArrayList<>();
        List<Boolean> booleanList = new ArrayList<>();
        int threadNum = detailExcelVOList.size() / EACH_THREAD_DATA_NUM + (detailExcelVOList.size() % EACH_THREAD_DATA_NUM == 0 ? 0 : 1);
        final CountDownLatch countDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * EACH_THREAD_DATA_NUM;
            int endIndex = i == threadNum ? detailExcelVOList.size() : i * EACH_THREAD_DATA_NUM;
            List<ImportAssemblyPartsAndBudgetLayerExcelVo> eachTreadExcelVos = detailExcelVOList.subList(startIndex, endIndex);
            importAssemblyPartsAndBudgetLayerExecutor.execute(() -> {
                try {
                    // 多线程异步执行主干导入方法
                    Boolean executeEachTreadResult = executeEachTreadExcelVos(eachTreadExcelVos);
                    booleanList.add(executeEachTreadResult);
                } catch (Exception e) {
                    logger.error("executeEachTreadExcelVos操作记录失败");
                    booleanList.add(false);
                } finally {
                    countDownLatch.countDown();
                    if (Objects.equals(countDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after executeEachTreadExcelVos", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                    resultList.addAll(eachTreadExcelVos);
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("importAssemblyPartsAndBudgetLayer的executeEachTreadExcelVos的await失败", ex);
            Thread.currentThread().interrupt();
        }

        if (!booleanList.contains(false)) {
            batchInsertMaterial(resultList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsertMaterial(List<ImportAssemblyPartsAndBudgetLayerExcelVo> resultList) {
        int size = resultList.size() / EACH_THREAD_DATA_NUM + (resultList.size() % EACH_THREAD_DATA_NUM == 0 ? 0 : 1);
        for (int i = 1; i <= size; i++) {
            int startIndex = (i - 1) * EACH_THREAD_DATA_NUM;
            int endIndex = i == size ? resultList.size() : i * EACH_THREAD_DATA_NUM;
            List<ImportAssemblyPartsAndBudgetLayerExcelVo> eachAddExcelVos = resultList.subList(startIndex, endIndex);
            List<Material> materialList = new ArrayList<>();
            for (ImportAssemblyPartsAndBudgetLayerExcelVo excelVo : eachAddExcelVos) {
                Material material = BeanConverter.copy(excelVo, Material.class);
                material.setPamCode(generateMaterialPAMCode());
                material.setOrganizationId(100251L);
                material.setDeleteFlag(false);
                materialList.add(material);
            }
            if (CollectionUtils.isNotEmpty(materialList)) {
                materialExtMapper.batchInsert(materialList);
            }
        }
    }

    private Boolean executeEachTreadExcelVos(List<ImportAssemblyPartsAndBudgetLayerExcelVo> eachTreadExcelVos) {
        return true;
    }

    @Override
    public List<MaterialDto> listByItemCodes(List<String> itemCodes) {
        if (itemCodes == null) {
            return Collections.emptyList();
        }
        return materialExtMapper.selectByItemCodes(itemCodes);
    }

    @Override
    public Map<String, String> getMaterialShelvesByOrgIdAndItemCode(Long organizationId, List<String> itemCodes) {
        if (Objects.nonNull(organizationId) && ListUtils.isNotEmpty(itemCodes)) {
            MaterialExample materialExample = new MaterialExample();
            materialExample.createCriteria()
                    .andDeleteFlagEqualTo(Boolean.FALSE)
                    .andOrganizationIdEqualTo(organizationId)
                    .andItemCodeIn(itemCodes);
            List<Material> materials = materialMapper.selectByExample(materialExample);
            return materials.stream().filter(material -> StringUtils.isNotEmpty(material.getShelves()))
                    .collect(Collectors.toMap(Material::getItemCode, Material::getShelves));
        }
        return Collections.emptyMap();
    }

    private EsbResponse materialPushErpItemImportFromSdp(String businessId,List<MaterialDto> materialdtos){
        if(ListUtils.isEmpty(materialdtos)){
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), "物料数据不能为空", businessId);
        }
        ArrayList<GERPInvItemImportDTO> itemImportDTOS = new ArrayList<>();

        TopicCodeEnum topicCodeEnum = TopicCodeEnum.getEnumByInterfaceCode(BusinessTypeEnums.MATERIAL_UPDATE.getCode());

        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        if(StringUtils.isEmpty(businessId)){
            // 生成流水号
            businessId = sdpCarrierService.generateSequence(topicCodeEnum);
        }

        // 服务流水号，自己定义的流水码
//        if (StringUtils.isEmpty(esbSerialNo)) {
//            esbSerialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime() + "";
//        }

        OrganizationCustomDict employeenumber = queryEmployeenumberByOrdId(materialdtos.get(0).getOrganizationId());

        for (MaterialDto materialdto : materialdtos) {
            GERPInvItemImportDTO itemImportDTO = new GERPInvItemImportDTO();

            itemImportDTO.setSourceCode(EsbConstant.PAM_REQUEST_ID);

            itemImportDTO.setEsbDataSource(BusinessTypeEnums.MATERIAL_UPDATE.getCode());

            itemImportDTO.setSourceNum(businessId.substring(0, businessId.lastIndexOf("-", businessId.lastIndexOf("-") - 1)) + "_" + materialdto.getId());

            itemImportDTO.setOrganizationId(materialdto.getOrganizationId());

            //库存组织ID
            itemImportDTO.setSegment1(materialdto.getItemCode());//物料编码
            itemImportDTO.setGlobalAttribute1(materialdto.getItemCodeOld());//旧物料编码
            itemImportDTO.setLongDescription(materialdto.getItemInfo());//物料描述
            itemImportDTO.setPrimaryUomCode(materialdto.getUnit());
            //基本计量单位
            itemImportDTO.setItemType(materialdto.getItemType());//用户物料类型
            //物料估计
            itemImportDTO.setFixedLotMultiplier(materialdto.getFixedLotMultiplier());
            //固定批次增加\最小包装量
            if (StringUtils.isEmpty(materialdto.getItemStatus())) {
                materialdto.setItemStatus("Active");
            }
            itemImportDTO.setInventoryItemStatusCode(materialdto.getItemStatus());//物料状态
            itemImportDTO.setFullLeadTime(Integer.parseInt(Optional.ofNullable(materialdto.getBuyerRound()).orElse("0")));//采购周期
            itemImportDTO.setAttribute19(materialdto.getBondFlag());
            //保税标识
            itemImportDTO.setAttribute1(materialdto.getCodeName());//代号
            // (图号)
            itemImportDTO.setDescription(materialdto.getItemInfo());
            itemImportDTO.setEsbSerialNum(businessId);
            if (StringUtils.isNotEmpty(materialdto.getInventoryType())) {
                itemImportDTO.setAddValue10(materialdto.getInventoryType());
            } else {
                itemImportDTO.setAddValue10("0.0.0");
            }


            if (StringUtils.isNotEmpty(materialdto.getBuyerNumber())) {
                itemImportDTO.setEmployeeNumber(materialdto.getBuyerNumber());//采购员
            } else {
                if (employeenumber != null && StringUtils.isNotEmpty(employeenumber.getValue())) {
                    itemImportDTO.setEmployeeNumber(employeenumber.getValue());//采购员
                }
            }


            itemImportDTO.setAddValue1(materialdto.getBrand());//品牌
            itemImportDTO.setAddValue2(materialdto.getName());//名称
            itemImportDTO.setAddValue3(materialdto.getModel());//型号/规格
            itemImportDTO.setAddValue4(materialdto.getChartVersion());
            //图纸版本号
            itemImportDTO.setAddValue5(materialdto.getMaterial());//材质

            itemImportDTO.setAddValue6(materialdto.getMaterialProcessing());//表面处理
            if (null != materialdto.getDesignCost()) {
                itemImportDTO.setEstimateCost(materialdto.getDesignCost());
                itemImportDTO.setAddValue7(materialdto.getBrandMaterialCode());
                //品牌商物料编码brand_material_code
                itemImportDTO.setAddValue8(materialdto.getSourcer());//配套人员
                String minimumOrderQuantity = "";
                if (null != materialdto.getMinimumOrderQuantity()) {
                    minimumOrderQuantity = materialdto.getMinimumOrderQuantity().toString();
                }
                itemImportDTO.setAddValue9(minimumOrderQuantity);//最小订货量
            }

            itemImportDTOS.add(itemImportDTO);
        }

        logger.info("物料主数据更新 推送开始,businessId:{}",businessId);
        HashMap<String, Object> params = new HashMap<>();
        params.put("pkgPIfaceLinesTbl",itemImportDTOS);
        params.put("orgId","");
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierService.doSdpDataTrade(targetSystemCode, topicCode, businessId, params);

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getMsg());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            logger.error("物料主数据更新 推送失败,businessId:{}",businessId);
            String result = JSON.toJSONString(sdpTradeResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    /**
     * 物料信息推送至ERP(APS产品)
     *
     * @param materialDtos
     */
    private EsbResponse callErpItemImportFromSdp(List<MaterialDto> materialDtos) {
        TwoTuple<String, RequestHeader> twoTuple = EsbBasedataUtil.getPamRequestHeader();
        logger.info("callErpItemImport的twoTuple:{}", JSON.toJSONString(twoTuple));
        String esbSerialNo = twoTuple.getFirst();

        ArrayList<GERPInvItemImportDTO> itemImportDTOS = new ArrayList<>();


        TopicCodeEnum topicCodeEnum = TopicCodeEnum.getEnumByInterfaceCode(BusinessTypeEnums.MATERIAL_CREATE.getCode());

        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = sdpCarrierService.generateSequence(topicCodeEnum);

        // 服务流水号，自己定义的流水码
        if (StringUtils.isEmpty(esbSerialNo)) {
            esbSerialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime() + "";
        }
        //物料信息_start
        for (MaterialDto materialDto : materialDtos) {

            GERPInvItemImportDTO itemImportDTO = new GERPInvItemImportDTO();

            itemImportDTO.setSourceCode(EsbConstant.PAM_REQUEST_ID);
            itemImportDTO.setEsbDataSource(topicCodeEnum.getInterfaceCode());
            itemImportDTO.setSourceNum(businessId.substring(0, businessId.lastIndexOf("-", businessId.lastIndexOf("-") - 1)) + "_" + materialDto.getId());

            //库存组织ID
            itemImportDTO.setOrganizationId(materialDto.getOrganizationId());
            itemImportDTO.setSegment1(materialDto.getItemCode());//物料编码
            itemImportDTO.setLongDescription(materialDto.getItemInfo());//物料描述
            itemImportDTO.setPrimaryUomCode(materialDto.getUnit());
            //基本计量单位
            itemImportDTO.setItemType(materialDto.getItemType());//用户物料类型
            itemImportDTO.setEstimateCost(materialDto.getDesignCost());//物料估计
            itemImportDTO.setInventoryItemStatusCode(materialDto.getItemStatus());//物料状态
            if(StringUtils.isNotEmpty(materialDto.getBuyerRound())) {
                itemImportDTO.setFullLeadTime(Integer.valueOf(materialDto.getBuyerRound()));//采购周期
            }
            itemImportDTO.setAttribute19(materialDto.getBondFlag());
            //保税标识
            itemImportDTO.setAttribute1(materialDto.getCodeName());//代号
            // (图号)
            itemImportDTO.setInventoryItemStatusCode("Active");
            //itemImportDTO.setEsbSerialNum(Optional.ofNullable(materialDto.getId()).map(String::valueOf).orElse(""));
            itemImportDTO.setDescription(materialDto.getItemInfo());
            itemImportDTO.setEsbSerialNum(businessId);

            if (StringUtils.isNotEmpty(materialDto.getBuyerNumber())) {
                itemImportDTO.setEmployeeNumber(materialDto.getBuyerNumber());//采购员
            } else {
                //采购员
                itemImportDTO.setEmployeeNumber("129701-BUYER,");
            }

            itemImportDTOS.add(itemImportDTO);
        }
        logger.info("物料信息推送至ERP(APS产品) 推送开始,businessId:{}",businessId);
        HashMap<String, Object> params = new HashMap<>();
        params.put("pkgPIfaceLinesTbl",itemImportDTOS);
        params.put("orgId","");
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierService.doSdpDataTrade(targetSystemCode, topicCode, businessId, params);

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getCode(), "000000")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getMsg());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            logger.error("物料信息推送至ERP(APS产品) 推送失败,businessId:{}",businessId);
            String result = JSON.toJSONString(sdpTradeResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    private EsbResponse materialValuationErpItemImportFromSdp(List<MaterialDto> materialDtos) {
        TwoTuple<String, RequestHeader> twoTuple = EsbBasedataUtil.getPamRequestHeader();
        logger.info("materialValuationErpItemImport的twoTuple:{}", JSON.toJSONString(twoTuple));
        String esbSerialNo = twoTuple.getFirst();

        ArrayList<GERPInvItemImportDTO> itemImportDTOS = new ArrayList<>();


        TopicCodeEnum topicCodeEnum = TopicCodeEnum.getEnumByInterfaceCode(BusinessTypeEnums.MATERIAL_VALUATION.getCode());

        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = sdpCarrierService.generateSequence(topicCodeEnum);

        // 服务流水号，自己定义的流水码
        if (StringUtils.isEmpty(esbSerialNo)) {
            esbSerialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime() + "";
        }

        for (MaterialDto materialDto : materialDtos) {
            GERPInvItemImportDTO itemImportDTO = new GERPInvItemImportDTO();

            itemImportDTO.setSourceCode(EsbConstant.PAM_REQUEST_ID);
            itemImportDTO.setEsbDataSource(topicCodeEnum.getInterfaceCode());
            itemImportDTO.setSourceNum(businessId.substring(0, businessId.lastIndexOf("-", businessId.lastIndexOf("-") - 1)) + "_" + materialDto.getId());

            //库存组织ID
            itemImportDTO.setOrganizationId(materialDto.getOrganizationId());

            itemImportDTO.setSegment1(materialDto.getItemCode());//物料编码
            itemImportDTO.setGlobalAttribute1(materialDto.getItemCodeOld());//旧物料编码
            itemImportDTO.setLongDescription(materialDto.getItemInfo());//物料描述
            itemImportDTO.setPrimaryUomCode(materialDto.getUnit());
            //基本计量单位
            itemImportDTO.setItemType(materialDto.getItemType());//用户物料类型
            itemImportDTO.setEstimateCost(materialDto.getDesignCost());//物料估计
            itemImportDTO.setFixedLotMultiplier(materialDto.getFixedLotMultiplier());
            //固定批次增加\最小包装量
            if (StringUtils.isEmpty(materialDto.getItemStatus())) {
                materialDto.setItemStatus("Active");
            }
            itemImportDTO.setInventoryItemStatusCode(materialDto.getItemStatus());//物料状态
            if(StringUtils.isNotEmpty(materialDto.getBuyerRound())) {
                itemImportDTO.setFullLeadTime(Integer.valueOf(materialDto.getBuyerRound()));//采购周期
            }
            itemImportDTO.setAttribute19(materialDto.getBondFlag());
            //保税标识
            itemImportDTO.setAttribute1(materialDto.getCodeName());//代号

            itemImportDTO.setEsbSerialNum(businessId);
            itemImportDTO.setDescription(materialDto.getItemInfo());

            if (StringUtils.isNotEmpty(materialDto.getBuyerNumber())) {
                itemImportDTO.setEmployeeNumber(materialDto.getBuyerNumber());//采购员
            } else {
                itemImportDTO.setEmployeeNumber("129701-BUYER,");
                //采购员
            }

            itemImportDTO.setAddValue1(materialDto.getBrand());//品牌
            itemImportDTO.setAddValue2(materialDto.getName());//名称
            itemImportDTO.setAddValue3(materialDto.getModel());//型号/规格
            itemImportDTO.setAddValue4(materialDto.getChartVersion());
            //图纸版本号
            itemImportDTO.setAddValue5(materialDto.getMaterial());//材质

            itemImportDTO.setAddValue6(materialDto.getMaterialProcessing());//表面处理
            itemImportDTO.setAddValue7(materialDto.getBrandMaterialCode());//品牌商物料编码brand_material_code
            itemImportDTO.setAddValue8(materialDto.getSourcer());//配套人员
            String minimumOrderQuantity = "";
            if (null != materialDto.getMinimumOrderQuantity()) {
                minimumOrderQuantity = materialDto.getMinimumOrderQuantity().toString();
            }
            itemImportDTO.setAddValue9(minimumOrderQuantity);//最小订货量

            itemImportDTOS.add(itemImportDTO);
        }
        logger.info("物料估价推送开始,businessId:{}", businessId);
        HashMap<String, Object> params = new HashMap<>();
        params.put("pkgPIfaceLinesTbl",itemImportDTOS);
        params.put("orgId","");
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierService.doSdpDataTrade(targetSystemCode, topicCode, businessId, params);
        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getCode(), "000000")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getMsg());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            logger.error("物料估价 推送失败,businessId:{}",businessId);
            String result = JSON.toJSONString(sdpTradeResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    private OrganizationCustomDict queryEmployeenumberByOrdId(Long organizationId) {
        //查找组织参数
        String url = "";
        String res = "";
        final Map<String, Object> query = new HashMap<>();
        //根据organizationId查找organization_rel中的operating_unit_id
        OrganizationRel ouId = organizationRelService.getOuId(organizationId);
        query.put("orgId", ouId.getOperatingUnitId());
        query.put("orgFrom", "ou");
        query.put("name", "采购员ID");
        url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query);
        res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationCustomDict>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
                });
        return ListUtils.isNotEmpty(response.getData()) ? response.getData().get(0) : null;
    }
}
