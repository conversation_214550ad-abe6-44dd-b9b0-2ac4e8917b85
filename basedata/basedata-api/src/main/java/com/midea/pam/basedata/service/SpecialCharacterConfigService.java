package com.midea.pam.basedata.service;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.SpecialCharacterConfigDTO;
import com.midea.pam.common.basedata.entity.SpecialCharacterConfig;

import java.util.List;

/**
 * 特殊字符配置服务接口
 * 提供特殊字符配置的CRUD操作
 * 
 * <AUTHOR> Team
 */
public interface SpecialCharacterConfigService {

    /**
     * 根据ID查询特殊字符配置
     * 
     * @param id 配置ID
     * @return 配置信息，如果不存在返回null
     */
    SpecialCharacterConfig findById(Long id);

    /**
     * 新增特殊字符配置
     * 
     * @param config 配置信息
     * @return 保存后的配置信息
     */
    SpecialCharacterConfig save(SpecialCharacterConfig config);

    /**
     * 更新特殊字符配置
     * 
     * @param config 配置信息
     * @return 更新后的配置信息
     */
    SpecialCharacterConfig update(SpecialCharacterConfig config);

    /**
     * 根据ID删除特殊字符配置（逻辑删除）
     * 
     * @param id 配置ID
     * @return 删除成功返回true，否则返回false
     */
    boolean delete(Long id);

    /**
     * 分页查询特殊字符配置
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageInfo<SpecialCharacterConfigDTO> page(SpecialCharacterConfigDTO query);

    /**
     * 根据条件查询特殊字符配置列表
     * 
     * @param query 查询条件
     * @return 配置列表
     */
    List<SpecialCharacterConfig> findByCondition(SpecialCharacterConfigDTO query);

    /**
     * 根据数据库名称查询启用的配置
     * 
     * @param databaseName 数据库名称
     * @return 启用的配置列表
     */
    List<SpecialCharacterConfig> findEnabledByDatabase(String databaseName);

    /**
     * 根据数据库名称和表名称查询启用的配置
     * 
     * @param databaseName 数据库名称
     * @param tableName 表名称
     * @return 启用的配置列表
     */
    List<SpecialCharacterConfig> findEnabledByDatabaseAndTable(String databaseName, String tableName);

    /**
     * 批量启用/禁用配置
     * 
     * @param ids 配置ID列表
     * @param enabled 启用状态
     * @return 更新成功的数量
     */
    int batchUpdateEnabled(List<Long> ids, Boolean enabled);

    /**
     * 验证配置的唯一性
     * 检查同一数据库、表、字段的配置是否已存在
     * 
     * @param config 配置信息
     * @return true-唯一，false-重复
     */
    boolean validateUniqueness(SpecialCharacterConfig config);
}
