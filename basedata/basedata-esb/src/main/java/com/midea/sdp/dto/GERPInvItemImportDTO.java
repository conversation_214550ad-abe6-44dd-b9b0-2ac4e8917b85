package com.midea.sdp.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class GERPInvItemImportDTO {
    private String sourceCode;
    private String sourceNum;
    private String sourceLineNum;
    private String esbSerialNum;
    private String esbDataSource;
    private String sourceSystemCode;
    private Long organizationId;
    private String segment1;
    private String longDescription;
    private String description;
    private String attribute1;
    private String primaryUomCode;
    private String volumeUomCode;
    private String weightUomCode;
    private BigDecimal unitVolume;
    private BigDecimal unitWeight;
    private String itemType;
    private Long templateId;
    private Long attribute2;
    private String attribute4;
    private String attribute5;
    private String attribute8;
    private String attribute19;
    private String employeeNumber;
    private Integer fullLeadTime;
    private String globalAttribute1;
    private String fixedLotMultiplier;
    private String inventoryItemStatusCode;
    private Long costOfSalesAccount;
    private Long salesAccount;
    private BigDecimal estimateCost;
    private Long referenceCode;
    private String transactionType;
    private Integer postprocessingLeadTime;
    private String addValue1;
    private String addValue2;
    private String addValue3;
    private String addValue4;
    private String addValue5;
    private String addValue6;
    private String addValue7;
    private String addValue8;
    private String addValue9;
    private String addValue10;
}
