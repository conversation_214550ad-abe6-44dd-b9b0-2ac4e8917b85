package com.midea.pam.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 接口调用日志记录注解
 * 用于自动记录接口调用的日志信息，包括请求参数、响应结果、执行状态等
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface InterfaceInvokeLog {

    /**
     * 接口类型枚举
     */
    enum InterfaceType {
        GCEB("GCEB", "金融网关接口", "gfp.apiUrl"),
        GEMS("GEMS", "GEMS系统接口", "mgp.apiurl");

        private final String systemName;
        private final String description;
        private final String configKey;

        InterfaceType(String systemName, String description, String configKey) {
            this.systemName = systemName;
            this.description = description;
            this.configKey = configKey;
        }

        public String getSystemName() {
            return systemName;
        }

        public String getDescription() {
            return description;
        }

        public String getConfigKey() {
            return configKey;
        }
    }

    /**
     * 接口类型，用于区分不同的接口调用方式
     * GCEB: 使用 callGceb 方法调用，判断 responseCode.equals("000000")
     * GEMS: 使用 carrierHelper.doPost 方法调用，判断 code.equals("0000")
     */
    InterfaceType interfaceType();

    /**
     * 业务编号字段名，用于从方法参数中提取业务编号
     * 支持嵌套属性，如 "paymentApplyCode" 或 "dto.code"
     */
    String businessCodeField();
    
    /**
     * URL后缀，用于构建完整的请求URL
     */
    String urlSuffix();
    
    /**
     * 是否记录请求参数，默认为 true
     */
    boolean logParams() default true;
    
    /**
     * 是否记录响应结果，默认为 true
     */
    boolean logResult() default true;

    /**
     * 自定义流水号字段名，用于从方法参数中提取自定义流水号
     * 如果指定此字段，将优先使用此字段的值作为流水号
     */
    String customSerialNoField() default "";
}
