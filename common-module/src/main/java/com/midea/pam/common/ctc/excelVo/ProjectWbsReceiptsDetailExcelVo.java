package com.midea.pam.common.ctc.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

@Getter
@Setter
public class ProjectWbsReceiptsDetailExcelVo {

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "单据类型", width = 15, replace = {"详细设计发布_1", "详细设计变更_2", "需求发布_3", "WBS变更_5"})
    private Integer requirementType;

    @Excel(name = "确认方式", width = 15, replace = {"部分确认_1", "进度确认_2","_0","_null"})
    private Integer confirmMode;

    @Excel(name = "单据编号", width = 20)
    private String requirementCode;

    @Excel(name = "制单人", width = 15)
    private String producerName;

    @Excel(name = "处理人", width = 15)
    private String handleName;

    @Excel(name = "备注", width = 15)
    private String remark;

}
