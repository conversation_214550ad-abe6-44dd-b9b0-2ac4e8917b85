package com.midea.pam.common.enums;

import com.midea.pam.common.base.BaseEnum;

/**
 * 特殊字符配置相关枚举类
 * 包含处理规则等配置项的枚举值
 * 
 * <AUTHOR> Team
 */
public enum SpecialCharacterConfigEnum implements BaseEnum<String> {

    // ==================== 处理规则枚举 ====================
    /**
     * 处理规则：替换为空格
     */
    PROCESSING_RULE_REPLACE_WITH_SPACE("REPLACE_WITH_SPACE", "替换为空格"),
    
    /**
     * 处理规则：删除特殊字符
     */
    PROCESSING_RULE_REMOVE("REMOVE", "删除"),
    
    /**
     * 处理规则：自定义替换
     */
    PROCESSING_RULE_CUSTOM("CUSTOM", "自定义"),

    ;

    private final String code;
    private final String msg;

    SpecialCharacterConfigEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String msg() {
        return this.msg;
    }

    /**
     * 根据code获取枚举
     * 
     * @param code 枚举编码
     * @return 对应的枚举，如果未找到返回null
     */
    public static SpecialCharacterConfigEnum getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SpecialCharacterConfigEnum enumItem : values()) {
            if (enumItem.code.equals(code)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 根据code获取描述信息
     * 
     * @param code 枚举编码
     * @return 对应的描述信息，如果未找到返回空字符串
     */
    public static String getMsgByCode(String code) {
        SpecialCharacterConfigEnum enumItem = getEnumByCode(code);
        return enumItem != null ? enumItem.msg : "";
    }

    /**
     * 验证处理规则是否有效
     * 
     * @param processingRule 处理规则
     * @return true-有效，false-无效
     */
    public static boolean isValidProcessingRule(String processingRule) {
        return PROCESSING_RULE_REPLACE_WITH_SPACE.code.equals(processingRule) ||
               PROCESSING_RULE_REMOVE.code.equals(processingRule) ||
               PROCESSING_RULE_CUSTOM.code.equals(processingRule);
    }
}
