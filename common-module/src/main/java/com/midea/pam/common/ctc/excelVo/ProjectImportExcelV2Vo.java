package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.util.excel4j.ExcelDate2DateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

/**
 * 项目详情导入（柔性）
 *
 * 2022-07-27
 */
@Getter
@Setter
public class ProjectImportExcelV2Vo {

    @Excel(name = "序号")
    @ExcelField(title = "序号", order = 1)
    @ApiModelProperty(required = false)
    private Long index;

    @Excel(name = "项目编号-旧")
    @ExcelField(title = "项目编号-旧", order = 2)
    @ApiModelProperty(required = false)
    private String codeOld;

    @Excel(name = "项目名称-旧")
    @ExcelField(title = "项目名称-旧", order = 3)
    @ApiModelProperty(required = false)
    private String nameOld;

    @Excel(name = "项目编号-新")
    @ExcelField(title = "项目编号-新", order = 4)
    @ApiModelProperty(required = true, value = "code")
    private String code;

    @Excel(name = "项目名称-新")
    @ExcelField(title = "项目名称-新", order = 5)
    @ApiModelProperty(required = true, value = "name")
    private String name;

    @Excel(name = "项目状态")
    @ExcelField(title = "项目状态", order = 6)
    @ApiModelProperty(required = true, notes = "审批状态 3:进行中", value = "status")
    private String statusName;

    @Excel(name = "项目类型")
    @ExcelField(title = "项目类型", order = 7)
    @ApiModelProperty(required = true, notes = "校验合法性", value = "type")
    private String typeName;

    @Excel(name = "项目概述")
    @ExcelField(title = "项目概述", order = 8)
    @ApiModelProperty(required = false, notes = "内容为空时取项目名称", value = "summary")
    private String summary;

    /**
     * 1、研发项目客户选自己E0133602
     * 2、多合同不同客户，取主合同的客户
     * 3、校验本ou是否生效
     * 4、按照客户对照关系匹配
     */
    @Excel(name = "CRM客户编码")
    @ExcelField(title = "CRM客户编码", order = 9)
    @ApiModelProperty(required = true)
    private String customerCRMCode;

    @Excel(name = "客户名称")
    @ExcelField(title = "客户名称", order = 10)
    @ApiModelProperty(required = true, value = "customerId,customerName")
    private String customerName;

    @Excel(name = "项目级别")
    @ExcelField(title = "项目级别", order = 11)
    @ApiModelProperty(required = false, notes = "项目级别，1代表A级，2代表B级，3代表C级；柔性项目不填", value = "projectLevel")
    private String projectLevelName;

    @Excel(name = "项目开始日期", format = "yyyy/MM/dd")
    @ExcelField(title = "项目开始日期", order = 12, readConverter = ExcelDate2DateConverter.class)
    @ApiModelProperty(required = true, notes = "开始日期要 < 结束日期", value = "startDate")
    private Date startDate;

    @Excel(name = "项目结束日期", format = "yyyy/MM/dd")
    @ExcelField(title = "项目结束日期", order = 13, readConverter = ExcelDate2DateConverter.class)
    @ApiModelProperty(required = true, notes = "开始日期要 < 结束日期", value = "endDate")
    private Date endDate;

    @Excel(name = "项目经理MIP账号")
    @ExcelField(title = "项目经理MIP账号", order = 14)
    @ApiModelProperty(required = true, notes = "校验合法性")
    private String managerMip;

    @Excel(name = "项目经理姓名")
    @ExcelField(title = "项目经理姓名", order = 15)
    @ApiModelProperty(required = true, notes = "校验合法性", value = "managerId,managerName")
    private String managerName;

    @Excel(name = "项目财务MIP账号")
    @ExcelField(title = "项目财务MIP账号", order = 16)
    private String financialMip;

    @Excel(name = "项目财务姓名")
    @ExcelField(title = "项目财务姓名", order = 17)
    private String financialName;

    @Excel(name = "技术负责人MIP账号")
    @ExcelField(title = "技术负责人MIP账号", order = 18)
    @ApiModelProperty(required = true, notes = "校验合法性")
    private String technologyLeaderMip;

    @Excel(name = "技术负责人姓名")
    @ExcelField(title = "技术负责人姓名", order = 19)
    @ApiModelProperty(required = true, notes = "校验合法性", value = "technologyLeaderId,salesManagerName")
    private String technologyLeaderName;

    @Excel(name = "方案设计人MIP账号")
    @ExcelField(title = "方案设计人MIP账号", order = 20)
    @ApiModelProperty(required = true, notes = "校验合法性")
    private String planDesignerMip;

    @Excel(name = "方案设计人名称")
    @ExcelField(title = "方案设计人名称", order = 21)
    @ApiModelProperty(required = true, notes = "校验合法性", value = "planDesignerId、planDesignerName")
    private String planDesignerName;

    @Excel(name = "应用行业")
    @ExcelField(title = "应用行业", order = 22 )
    private String applicationIndustryName;

    @Excel(name = "业务分类")
    @ExcelField(title = "业务分类", order = 23)
    @ApiModelProperty(required = true, notes = "校验合法性", value = "unitId")
    private String unitName;

    @Excel(name = "利润中心")
    @ExcelField(title = "利润中心", order = 24 )
    private String profitCenter;

    @Excel(name = "产品中心")
    @ExcelField(title = "产品中心", order = 25 )
    private String productCenter;

    @Excel(name = "项目属性")
    @ExcelField(title = "项目属性", order = 26)
    @ApiModelProperty(required = true, notes = "项目属性 1:内部;2:外部;3:研发;", value = "priceType")
    private String priceTypeName;

    @Excel(name = "业务实体")
    @ExcelField(title = "业务实体", order = 27)
    @ApiModelProperty(required = true, notes = "校验合法性", value = "ouId")
    private String ouName;

    @Excel(name = "预算部门")
    @ExcelField(title = "预算部门", order = 28)
    @ApiModelProperty(required = true, notes = "校验合法性", value = "budgetDeptId")
    private String budgetDeptName;

    @Excel(name = "预算树头")
    @ExcelField(title = "预算树头", order = 29)
    @ApiModelProperty(required = true, notes = "校验合法性", value = "budgetHeaderId、budgetHeaderName")
    private String budgetHeaderName;

    @Excel(name = "币种")
    @ExcelField(title = "币种", order = 30)
    @ApiModelProperty(required = true, notes = "校验合法性，只校验必填")
    private String currency;

    @Excel(name = "WBS模板名称")
    @ExcelField(title = "WBS模板名称", order = 31)
    private String wbsTemplateInfoName;

    @Excel(name = "产品")
    @ExcelField(title = "产品", order = 31)
    private String productName;

    @ExcelField(title = "预算合计", order = 32)
    private String budgetCostStr;

    @Excel(name = "业务分部")
    @ExcelField(title = "业务分部", order = 33)
    private String businessSegment;

    @ApiModelProperty(notes = "校验结果")
    private String validResult;
}
