package com.midea.pam.common.constants;

import java.util.List;
import java.util.Map;

public class ResponseMap {
    public ResponseMap(){}
    private String status;//success 成功  fail失败
    private String msg;//失败信息
    private Map<String,String> headMap; //[11] 可伸缩单列文本块（一般用于基本信息配置）
    // 最大支持同时配置5个明细列表控件（键值对集合）
    private List<Map<String,String>> list1; // [21] 可伸缩列表块1（对应美信移动审批控件标识明细=list1）
    private List<Map<String,String>> list2; // [21] 可伸缩列表块1（对应美信移动审批控件标识明细=list2）
    private List<Map<String,String>> list3; // [21] 可伸缩列表块1（对应美信移动审批控件标识明细=list3）
    private List<Map<String,String>> list4; // [21] 可伸缩列表块1（对应美信移动审批控件标识明细=list4）
    private List<Map<String,String>> list5; // [21] 可伸缩列表块1（对应美信移动审批控件标识明细=list5）
    private List<Map<String,String>> list6; // [21] 可伸缩列表块1（对应美信移动审批控件标识明细=list6）
    private List<AduitAtta> fileList; //附件，固定参数，美信移动审批模板需要勾选显示表单附件

    public Map<String, String> getHeadMap() {
        return headMap;
    }

    public void setHeadMap(Map<String, String> headMap) {
        this.headMap = headMap;
    }

    public List<Map<String, String>> getList1() {
        return list1;
    }

    public void setList1(List<Map<String, String>> list1) {
        this.list1 = list1;
    }

    public List<Map<String, String>> getList2() {
        return list2;
    }

    public void setList2(List<Map<String, String>> list2) {
        this.list2 = list2;
    }

    public List<Map<String, String>> getList3() {
        return list3;
    }

    public void setList3(List<Map<String, String>> list3) {
        this.list3 = list3;
    }

    public List<Map<String, String>> getList4() {
        return list4;
    }

    public void setList4(List<Map<String, String>> list4) {
        this.list4 = list4;
    }

    public List<Map<String, String>> getList5() {
        return list5;
    }

    public void setList5(List<Map<String, String>> list5) {
        this.list5 = list5;
    }


    public List<Map<String, String>> getList6() {
        return list6;
    }

    public void setList6(List<Map<String, String>> list6) {
        this.list6 = list6;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<AduitAtta> getFileList() {
        return fileList;
    }

    public void setFileList(List<AduitAtta> fileList) {
        this.fileList = fileList;
    }
}
