package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "采购合同")
public class PurchaseContract extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "关联项目ID")
    private Long projectId;

    @ApiModelProperty(value = "合同编号")
    private String code;

    @ApiModelProperty(value = "合同名称")
    private String name;

    @ApiModelProperty(value = "所属业务实体")
    private Long ouId;

    @ApiModelProperty(value = "合同类型ID")
    private Long typeId;

    @ApiModelProperty(value = "合同类型名称")
    private String typeName;

    @ApiModelProperty(value = "法务合同编号")
    private String legalAffairsCode;

    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    @ApiModelProperty(value = "供应商编号")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商地点ID")
    private String erpVendorSiteId;

    @ApiModelProperty(value = "供应商地点")
    private String vendorSiteCode;

    @ApiModelProperty(value = "合同金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "不含税金额")
    private BigDecimal excludingTaxAmount;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "合同开始时间")
    private Date startTime;

    @ApiModelProperty(value = "合同结束时间")
    private Date endTime;

    @ApiModelProperty(value = "项目经理")
    private Long manager;

    @ApiModelProperty(value = "项目经理姓名")
    private String managerName;

    @ApiModelProperty(value = "采购跟进人")
    private Long purchasingFollower;

    @ApiModelProperty(value = "采购跟进人名称")
    private String purchasingFollowerName;

    @ApiModelProperty(value = "合同级别 重大(0),普通(1)")
    private Integer classes;

    @ApiModelProperty(value = "中国国内(0),涉外合同(1),美的内部(2)")
    private Integer belongArea;

    @ApiModelProperty(value = "支付方式")
    private String paymentMethod;

    @ApiModelProperty(value = "发票类型 0-纸质专票 1-纸质普票 2-电子专票 3-电子普票")
    private Integer invoiceType;

    @ApiModelProperty(value = "是否电子合同(1是，0否)")
    private Boolean isElectronicContract;

    @ApiModelProperty(value = "签约人姓名")
    private String otherName;

    @ApiModelProperty(value = "暂时用来存放流程id")
    private String otherId;

    @ApiModelProperty(value = "签约人手机号")
    private String otherPhone;

    @ApiModelProperty(value = "签约人邮箱")
    private String otherEmail;

    @ApiModelProperty(value = "对公对私 对公0或对私1 默认0")
    private Integer publicOrPrivate;

    @ApiModelProperty(value = "合同用印类别 合同章(5),公章(1)")
    private Integer sealCategory;

    @ApiModelProperty(value = "合同印章管理员id 多个人员 字符串用，连接")
    private String sealAdminAccountIds;

    @ApiModelProperty(value = "附件，多个用,隔开")
    private String annex;

    @ApiModelProperty(value = "状态(1:草稿,2:审核中,3:驳回,4:待生效,5:生效,6:失效,7:已结束,8:冻结,9:作废,10:变更中)")
    private Integer status;

    @ApiModelProperty(value = "归档日期")
    private Date filingDate;

    @ApiModelProperty(value = "是否参与结转(1:参与 0:不参与)")
    private Boolean carryoverFlag;

    @ApiModelProperty(value = "是否同步法务系统(1是，0否)")
    private Boolean isSynchronizeLegalSystemFlag;

    @ApiModelProperty(value = "是否已双签(1是，0否)")
    private Boolean gleSignFlag;

    @ApiModelProperty(value = "不同步法务类型")
    private String notsyncType;

    @ApiModelProperty(value = "不同步法务原因")
    private String notsyncReason;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "税率ID")
    private Long taxId;

    @ApiModelProperty(value = "税率")
    private String taxRate;

    @ApiModelProperty(value = "汇率类型")
    private String conversionType;

    @ApiModelProperty(value = "汇率时间")
    private Date conversionDate;

    @ApiModelProperty(value = "汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "法务系统合同id")
    private String legalBusinessId;

    @ApiModelProperty(value = "法务系统合同编号")
    private String legalContractNum;

    @ApiModelProperty(value = "法务跳转地址")
    private String legalUrl;

    @ApiModelProperty(value = "是否生成水印(1是，0否)")
    private Boolean ifWatermarking;

    @ApiModelProperty(value = "不加水印原因")
    private String noWatermarkingReason;

    @ApiModelProperty(value = "累计进度执行百分比")
    private BigDecimal executeContractPercentTotal;

    @ApiModelProperty(value = "交货方式")
    private String deliveryType;

    @ApiModelProperty(value = "交货条款")
    private String deliveryClause;

    @ApiModelProperty(value = "attachmentId(业务附件关系ID)")
    private String fileAttachmentId;

    @ApiModelProperty(value = "承诺交期")
    private Date deliveryDate;

    @ApiModelProperty(value = "追踪货期")
    private Date trackLeadTime;

    @ApiModelProperty(value = "账期（天）")
    private Integer paymentTerm;

    @ApiModelProperty(value = "法务合同状态(合同草稿：110；合同审批：120；合同待盖章：130；合同待归档：150；合同归档：151；合同废弃：100；合同废弃中：101；变更审批：220；变更待盖章：230；变更待归档：250；变更废弃中：201；变更归档：251；变更废弃中：201；终止审批：320；终止待盖章：330；终止待归档：350；变更废弃中：301；终止归档：351；终止废弃中：301；合同终止失效：353)")
    private Integer contractStatus;

    @ApiModelProperty(value = "合同条款类型(0：非标准条款；1：标准条款)")
    private String contractTermsFlg;

    @ApiModelProperty(value = "标准条款Id集合")
    private String contractTermsIds;

    @ApiModelProperty(value = "合同原件，多个用,隔开")
    private String originalContractAnnex;

    @ApiModelProperty(value = "变更协议")
    private String ifUploadChangeFile;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "合同条款")
    private String contractTerms;

    @ApiModelProperty(value = "货期跟进备注")
    private String trackLeadTimeRemark;

    private static final long serialVersionUID = 1L;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName == null ? null : typeName.trim();
    }

    public String getLegalAffairsCode() {
        return legalAffairsCode;
    }

    public void setLegalAffairsCode(String legalAffairsCode) {
        this.legalAffairsCode = legalAffairsCode == null ? null : legalAffairsCode.trim();
    }

    public Long getVendorId() {
        return vendorId;
    }

    public void setVendorId(Long vendorId) {
        this.vendorId = vendorId;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getErpVendorSiteId() {
        return erpVendorSiteId;
    }

    public void setErpVendorSiteId(String erpVendorSiteId) {
        this.erpVendorSiteId = erpVendorSiteId == null ? null : erpVendorSiteId.trim();
    }

    public String getVendorSiteCode() {
        return vendorSiteCode;
    }

    public void setVendorSiteCode(String vendorSiteCode) {
        this.vendorSiteCode = vendorSiteCode == null ? null : vendorSiteCode.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getExcludingTaxAmount() {
        return excludingTaxAmount;
    }

    public void setExcludingTaxAmount(BigDecimal excludingTaxAmount) {
        this.excludingTaxAmount = excludingTaxAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getManager() {
        return manager;
    }

    public void setManager(Long manager) {
        this.manager = manager;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName == null ? null : managerName.trim();
    }

    public Long getPurchasingFollower() {
        return purchasingFollower;
    }

    public void setPurchasingFollower(Long purchasingFollower) {
        this.purchasingFollower = purchasingFollower;
    }

    public String getPurchasingFollowerName() {
        return purchasingFollowerName;
    }

    public void setPurchasingFollowerName(String purchasingFollowerName) {
        this.purchasingFollowerName = purchasingFollowerName == null ? null : purchasingFollowerName.trim();
    }

    public Integer getClasses() {
        return classes;
    }

    public void setClasses(Integer classes) {
        this.classes = classes;
    }

    public Integer getBelongArea() {
        return belongArea;
    }

    public void setBelongArea(Integer belongArea) {
        this.belongArea = belongArea;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod == null ? null : paymentMethod.trim();
    }

    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public Boolean getIsElectronicContract() {
        return isElectronicContract;
    }

    public void setIsElectronicContract(Boolean isElectronicContract) {
        this.isElectronicContract = isElectronicContract;
    }

    public String getOtherName() {
        return otherName;
    }

    public void setOtherName(String otherName) {
        this.otherName = otherName == null ? null : otherName.trim();
    }

    public String getOtherId() {
        return otherId;
    }

    public void setOtherId(String otherId) {
        this.otherId = otherId == null ? null : otherId.trim();
    }

    public String getOtherPhone() {
        return otherPhone;
    }

    public void setOtherPhone(String otherPhone) {
        this.otherPhone = otherPhone == null ? null : otherPhone.trim();
    }

    public String getOtherEmail() {
        return otherEmail;
    }

    public void setOtherEmail(String otherEmail) {
        this.otherEmail = otherEmail == null ? null : otherEmail.trim();
    }

    public Integer getPublicOrPrivate() {
        return publicOrPrivate;
    }

    public void setPublicOrPrivate(Integer publicOrPrivate) {
        this.publicOrPrivate = publicOrPrivate;
    }

    public Integer getSealCategory() {
        return sealCategory;
    }

    public void setSealCategory(Integer sealCategory) {
        this.sealCategory = sealCategory;
    }

    public String getSealAdminAccountIds() {
        return sealAdminAccountIds;
    }

    public void setSealAdminAccountIds(String sealAdminAccountIds) {
        this.sealAdminAccountIds = sealAdminAccountIds == null ? null : sealAdminAccountIds.trim();
    }

    public String getAnnex() {
        return annex;
    }

    public void setAnnex(String annex) {
        this.annex = annex == null ? null : annex.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getFilingDate() {
        return filingDate;
    }

    public void setFilingDate(Date filingDate) {
        this.filingDate = filingDate;
    }

    public Boolean getCarryoverFlag() {
        return carryoverFlag;
    }

    public void setCarryoverFlag(Boolean carryoverFlag) {
        this.carryoverFlag = carryoverFlag;
    }

    public Boolean getIsSynchronizeLegalSystemFlag() {
        return isSynchronizeLegalSystemFlag;
    }

    public void setIsSynchronizeLegalSystemFlag(Boolean isSynchronizeLegalSystemFlag) {
        this.isSynchronizeLegalSystemFlag = isSynchronizeLegalSystemFlag;
    }

    public Boolean getGleSignFlag() {
        return gleSignFlag;
    }

    public void setGleSignFlag(Boolean gleSignFlag) {
        this.gleSignFlag = gleSignFlag;
    }

    public String getNotsyncType() {
        return notsyncType;
    }

    public void setNotsyncType(String notsyncType) {
        this.notsyncType = notsyncType == null ? null : notsyncType.trim();
    }

    public String getNotsyncReason() {
        return notsyncReason;
    }

    public void setNotsyncReason(String notsyncReason) {
        this.notsyncReason = notsyncReason == null ? null : notsyncReason.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getTaxId() {
        return taxId;
    }

    public void setTaxId(Long taxId) {
        this.taxId = taxId;
    }

    public String getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(String taxRate) {
        this.taxRate = taxRate == null ? null : taxRate.trim();
    }

    public String getConversionType() {
        return conversionType;
    }

    public void setConversionType(String conversionType) {
        this.conversionType = conversionType == null ? null : conversionType.trim();
    }

    public Date getConversionDate() {
        return conversionDate;
    }

    public void setConversionDate(Date conversionDate) {
        this.conversionDate = conversionDate;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public String getLegalBusinessId() {
        return legalBusinessId;
    }

    public void setLegalBusinessId(String legalBusinessId) {
        this.legalBusinessId = legalBusinessId == null ? null : legalBusinessId.trim();
    }

    public String getLegalContractNum() {
        return legalContractNum;
    }

    public void setLegalContractNum(String legalContractNum) {
        this.legalContractNum = legalContractNum == null ? null : legalContractNum.trim();
    }

    public String getLegalUrl() {
        return legalUrl;
    }

    public void setLegalUrl(String legalUrl) {
        this.legalUrl = legalUrl == null ? null : legalUrl.trim();
    }

    public Boolean getIfWatermarking() {
        return ifWatermarking;
    }

    public void setIfWatermarking(Boolean ifWatermarking) {
        this.ifWatermarking = ifWatermarking;
    }

    public String getNoWatermarkingReason() {
        return noWatermarkingReason;
    }

    public void setNoWatermarkingReason(String noWatermarkingReason) {
        this.noWatermarkingReason = noWatermarkingReason == null ? null : noWatermarkingReason.trim();
    }

    public BigDecimal getExecuteContractPercentTotal() {
        return executeContractPercentTotal;
    }

    public void setExecuteContractPercentTotal(BigDecimal executeContractPercentTotal) {
        this.executeContractPercentTotal = executeContractPercentTotal;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType == null ? null : deliveryType.trim();
    }

    public String getDeliveryClause() {
        return deliveryClause;
    }

    public void setDeliveryClause(String deliveryClause) {
        this.deliveryClause = deliveryClause == null ? null : deliveryClause.trim();
    }

    public String getFileAttachmentId() {
        return fileAttachmentId;
    }

    public void setFileAttachmentId(String fileAttachmentId) {
        this.fileAttachmentId = fileAttachmentId == null ? null : fileAttachmentId.trim();
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Date getTrackLeadTime() {
        return trackLeadTime;
    }

    public void setTrackLeadTime(Date trackLeadTime) {
        this.trackLeadTime = trackLeadTime;
    }

    public Integer getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(Integer paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public Integer getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(Integer contractStatus) {
        this.contractStatus = contractStatus;
    }

    public String getContractTermsFlg() {
        return contractTermsFlg;
    }

    public void setContractTermsFlg(String contractTermsFlg) {
        this.contractTermsFlg = contractTermsFlg == null ? null : contractTermsFlg.trim();
    }

    public String getContractTermsIds() {
        return contractTermsIds;
    }

    public void setContractTermsIds(String contractTermsIds) {
        this.contractTermsIds = contractTermsIds == null ? null : contractTermsIds.trim();
    }

    public String getOriginalContractAnnex() {
        return originalContractAnnex;
    }

    public void setOriginalContractAnnex(String originalContractAnnex) {
        this.originalContractAnnex = originalContractAnnex == null ? null : originalContractAnnex.trim();
    }

    public String getIfUploadChangeFile() {
        return ifUploadChangeFile;
    }

    public void setIfUploadChangeFile(String ifUploadChangeFile) {
        this.ifUploadChangeFile = ifUploadChangeFile == null ? null : ifUploadChangeFile.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getContractTerms() {
        return contractTerms;
    }

    public void setContractTerms(String contractTerms) {
        this.contractTerms = contractTerms == null ? null : contractTerms.trim();
    }

    public String getTrackLeadTimeRemark() {
        return trackLeadTimeRemark;
    }

    public void setTrackLeadTimeRemark(String trackLeadTimeRemark) {
        this.trackLeadTimeRemark = trackLeadTimeRemark == null ? null : trackLeadTimeRemark.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectId=").append(projectId);
        sb.append(", code=").append(code);
        sb.append(", name=").append(name);
        sb.append(", ouId=").append(ouId);
        sb.append(", typeId=").append(typeId);
        sb.append(", typeName=").append(typeName);
        sb.append(", legalAffairsCode=").append(legalAffairsCode);
        sb.append(", vendorId=").append(vendorId);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", erpVendorSiteId=").append(erpVendorSiteId);
        sb.append(", vendorSiteCode=").append(vendorSiteCode);
        sb.append(", amount=").append(amount);
        sb.append(", excludingTaxAmount=").append(excludingTaxAmount);
        sb.append(", currency=").append(currency);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", manager=").append(manager);
        sb.append(", managerName=").append(managerName);
        sb.append(", purchasingFollower=").append(purchasingFollower);
        sb.append(", purchasingFollowerName=").append(purchasingFollowerName);
        sb.append(", classes=").append(classes);
        sb.append(", belongArea=").append(belongArea);
        sb.append(", paymentMethod=").append(paymentMethod);
        sb.append(", invoiceType=").append(invoiceType);
        sb.append(", isElectronicContract=").append(isElectronicContract);
        sb.append(", otherName=").append(otherName);
        sb.append(", otherId=").append(otherId);
        sb.append(", otherPhone=").append(otherPhone);
        sb.append(", otherEmail=").append(otherEmail);
        sb.append(", publicOrPrivate=").append(publicOrPrivate);
        sb.append(", sealCategory=").append(sealCategory);
        sb.append(", sealAdminAccountIds=").append(sealAdminAccountIds);
        sb.append(", annex=").append(annex);
        sb.append(", status=").append(status);
        sb.append(", filingDate=").append(filingDate);
        sb.append(", carryoverFlag=").append(carryoverFlag);
        sb.append(", isSynchronizeLegalSystemFlag=").append(isSynchronizeLegalSystemFlag);
        sb.append(", gleSignFlag=").append(gleSignFlag);
        sb.append(", notsyncType=").append(notsyncType);
        sb.append(", notsyncReason=").append(notsyncReason);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", taxId=").append(taxId);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", conversionType=").append(conversionType);
        sb.append(", conversionDate=").append(conversionDate);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", legalBusinessId=").append(legalBusinessId);
        sb.append(", legalContractNum=").append(legalContractNum);
        sb.append(", legalUrl=").append(legalUrl);
        sb.append(", ifWatermarking=").append(ifWatermarking);
        sb.append(", noWatermarkingReason=").append(noWatermarkingReason);
        sb.append(", executeContractPercentTotal=").append(executeContractPercentTotal);
        sb.append(", deliveryType=").append(deliveryType);
        sb.append(", deliveryClause=").append(deliveryClause);
        sb.append(", fileAttachmentId=").append(fileAttachmentId);
        sb.append(", deliveryDate=").append(deliveryDate);
        sb.append(", trackLeadTime=").append(trackLeadTime);
        sb.append(", paymentTerm=").append(paymentTerm);
        sb.append(", contractStatus=").append(contractStatus);
        sb.append(", contractTermsFlg=").append(contractTermsFlg);
        sb.append(", contractTermsIds=").append(contractTermsIds);
        sb.append(", originalContractAnnex=").append(originalContractAnnex);
        sb.append(", ifUploadChangeFile=").append(ifUploadChangeFile);
        sb.append(", remark=").append(remark);
        sb.append(", contractTerms=").append(contractTerms);
        sb.append(", trackLeadTimeRemark=").append(trackLeadTimeRemark);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}