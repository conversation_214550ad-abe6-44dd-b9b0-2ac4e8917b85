package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MaterialAdjustDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MaterialAdjustDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andHeaderIdIsNull() {
            addCriterion("header_id is null");
            return (Criteria) this;
        }

        public Criteria andHeaderIdIsNotNull() {
            addCriterion("header_id is not null");
            return (Criteria) this;
        }

        public Criteria andHeaderIdEqualTo(Long value) {
            addCriterion("header_id =", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdNotEqualTo(Long value) {
            addCriterion("header_id <>", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdGreaterThan(Long value) {
            addCriterion("header_id >", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("header_id >=", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdLessThan(Long value) {
            addCriterion("header_id <", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdLessThanOrEqualTo(Long value) {
            addCriterion("header_id <=", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdIn(List<Long> values) {
            addCriterion("header_id in", values, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdNotIn(List<Long> values) {
            addCriterion("header_id not in", values, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdBetween(Long value1, Long value2) {
            addCriterion("header_id between", value1, value2, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdNotBetween(Long value1, Long value2) {
            addCriterion("header_id not between", value1, value2, "headerId");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andItemDesIsNull() {
            addCriterion("item_des is null");
            return (Criteria) this;
        }

        public Criteria andItemDesIsNotNull() {
            addCriterion("item_des is not null");
            return (Criteria) this;
        }

        public Criteria andItemDesEqualTo(String value) {
            addCriterion("item_des =", value, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesNotEqualTo(String value) {
            addCriterion("item_des <>", value, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesGreaterThan(String value) {
            addCriterion("item_des >", value, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesGreaterThanOrEqualTo(String value) {
            addCriterion("item_des >=", value, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesLessThan(String value) {
            addCriterion("item_des <", value, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesLessThanOrEqualTo(String value) {
            addCriterion("item_des <=", value, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesLike(String value) {
            addCriterion("item_des like", value, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesNotLike(String value) {
            addCriterion("item_des not like", value, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesIn(List<String> values) {
            addCriterion("item_des in", values, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesNotIn(List<String> values) {
            addCriterion("item_des not in", values, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesBetween(String value1, String value2) {
            addCriterion("item_des between", value1, value2, "itemDes");
            return (Criteria) this;
        }

        public Criteria andItemDesNotBetween(String value1, String value2) {
            addCriterion("item_des not between", value1, value2, "itemDes");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIsNull() {
            addCriterion("unit_code is null");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIsNotNull() {
            addCriterion("unit_code is not null");
            return (Criteria) this;
        }

        public Criteria andUnitCodeEqualTo(String value) {
            addCriterion("unit_code =", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotEqualTo(String value) {
            addCriterion("unit_code <>", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeGreaterThan(String value) {
            addCriterion("unit_code >", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeGreaterThanOrEqualTo(String value) {
            addCriterion("unit_code >=", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLessThan(String value) {
            addCriterion("unit_code <", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLessThanOrEqualTo(String value) {
            addCriterion("unit_code <=", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLike(String value) {
            addCriterion("unit_code like", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotLike(String value) {
            addCriterion("unit_code not like", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIn(List<String> values) {
            addCriterion("unit_code in", values, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotIn(List<String> values) {
            addCriterion("unit_code not in", values, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeBetween(String value1, String value2) {
            addCriterion("unit_code between", value1, value2, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotBetween(String value1, String value2) {
            addCriterion("unit_code not between", value1, value2, "unitCode");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdIsNull() {
            addCriterion("material_class_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdIsNotNull() {
            addCriterion("material_class_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdEqualTo(Long value) {
            addCriterion("material_class_id =", value, "materialClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdNotEqualTo(Long value) {
            addCriterion("material_class_id <>", value, "materialClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdGreaterThan(Long value) {
            addCriterion("material_class_id >", value, "materialClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdGreaterThanOrEqualTo(Long value) {
            addCriterion("material_class_id >=", value, "materialClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdLessThan(Long value) {
            addCriterion("material_class_id <", value, "materialClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdLessThanOrEqualTo(Long value) {
            addCriterion("material_class_id <=", value, "materialClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdIn(List<Long> values) {
            addCriterion("material_class_id in", values, "materialClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdNotIn(List<Long> values) {
            addCriterion("material_class_id not in", values, "materialClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdBetween(Long value1, Long value2) {
            addCriterion("material_class_id between", value1, value2, "materialClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIdNotBetween(Long value1, Long value2) {
            addCriterion("material_class_id not between", value1, value2, "materialClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIsNull() {
            addCriterion("material_class is null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIsNotNull() {
            addCriterion("material_class is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassEqualTo(String value) {
            addCriterion("material_class =", value, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassNotEqualTo(String value) {
            addCriterion("material_class <>", value, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassGreaterThan(String value) {
            addCriterion("material_class >", value, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassGreaterThanOrEqualTo(String value) {
            addCriterion("material_class >=", value, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassLessThan(String value) {
            addCriterion("material_class <", value, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassLessThanOrEqualTo(String value) {
            addCriterion("material_class <=", value, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassLike(String value) {
            addCriterion("material_class like", value, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassNotLike(String value) {
            addCriterion("material_class not like", value, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassIn(List<String> values) {
            addCriterion("material_class in", values, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassNotIn(List<String> values) {
            addCriterion("material_class not in", values, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassBetween(String value1, String value2) {
            addCriterion("material_class between", value1, value2, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialClassNotBetween(String value1, String value2) {
            addCriterion("material_class not between", value1, value2, "materialClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdIsNull() {
            addCriterion("material_middle_class_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdIsNotNull() {
            addCriterion("material_middle_class_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdEqualTo(Long value) {
            addCriterion("material_middle_class_id =", value, "materialMiddleClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdNotEqualTo(Long value) {
            addCriterion("material_middle_class_id <>", value, "materialMiddleClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdGreaterThan(Long value) {
            addCriterion("material_middle_class_id >", value, "materialMiddleClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdGreaterThanOrEqualTo(Long value) {
            addCriterion("material_middle_class_id >=", value, "materialMiddleClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdLessThan(Long value) {
            addCriterion("material_middle_class_id <", value, "materialMiddleClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdLessThanOrEqualTo(Long value) {
            addCriterion("material_middle_class_id <=", value, "materialMiddleClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdIn(List<Long> values) {
            addCriterion("material_middle_class_id in", values, "materialMiddleClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdNotIn(List<Long> values) {
            addCriterion("material_middle_class_id not in", values, "materialMiddleClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdBetween(Long value1, Long value2) {
            addCriterion("material_middle_class_id between", value1, value2, "materialMiddleClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIdNotBetween(Long value1, Long value2) {
            addCriterion("material_middle_class_id not between", value1, value2, "materialMiddleClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIsNull() {
            addCriterion("material_middle_class is null");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIsNotNull() {
            addCriterion("material_middle_class is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassEqualTo(String value) {
            addCriterion("material_middle_class =", value, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassNotEqualTo(String value) {
            addCriterion("material_middle_class <>", value, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassGreaterThan(String value) {
            addCriterion("material_middle_class >", value, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassGreaterThanOrEqualTo(String value) {
            addCriterion("material_middle_class >=", value, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassLessThan(String value) {
            addCriterion("material_middle_class <", value, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassLessThanOrEqualTo(String value) {
            addCriterion("material_middle_class <=", value, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassLike(String value) {
            addCriterion("material_middle_class like", value, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassNotLike(String value) {
            addCriterion("material_middle_class not like", value, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassIn(List<String> values) {
            addCriterion("material_middle_class in", values, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassNotIn(List<String> values) {
            addCriterion("material_middle_class not in", values, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassBetween(String value1, String value2) {
            addCriterion("material_middle_class between", value1, value2, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialMiddleClassNotBetween(String value1, String value2) {
            addCriterion("material_middle_class not between", value1, value2, "materialMiddleClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdIsNull() {
            addCriterion("material_small_class_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdIsNotNull() {
            addCriterion("material_small_class_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdEqualTo(Long value) {
            addCriterion("material_small_class_id =", value, "materialSmallClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdNotEqualTo(Long value) {
            addCriterion("material_small_class_id <>", value, "materialSmallClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdGreaterThan(Long value) {
            addCriterion("material_small_class_id >", value, "materialSmallClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdGreaterThanOrEqualTo(Long value) {
            addCriterion("material_small_class_id >=", value, "materialSmallClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdLessThan(Long value) {
            addCriterion("material_small_class_id <", value, "materialSmallClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdLessThanOrEqualTo(Long value) {
            addCriterion("material_small_class_id <=", value, "materialSmallClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdIn(List<Long> values) {
            addCriterion("material_small_class_id in", values, "materialSmallClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdNotIn(List<Long> values) {
            addCriterion("material_small_class_id not in", values, "materialSmallClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdBetween(Long value1, Long value2) {
            addCriterion("material_small_class_id between", value1, value2, "materialSmallClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIdNotBetween(Long value1, Long value2) {
            addCriterion("material_small_class_id not between", value1, value2, "materialSmallClassId");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIsNull() {
            addCriterion("material_small_class is null");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIsNotNull() {
            addCriterion("material_small_class is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassEqualTo(String value) {
            addCriterion("material_small_class =", value, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassNotEqualTo(String value) {
            addCriterion("material_small_class <>", value, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassGreaterThan(String value) {
            addCriterion("material_small_class >", value, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassGreaterThanOrEqualTo(String value) {
            addCriterion("material_small_class >=", value, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassLessThan(String value) {
            addCriterion("material_small_class <", value, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassLessThanOrEqualTo(String value) {
            addCriterion("material_small_class <=", value, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassLike(String value) {
            addCriterion("material_small_class like", value, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassNotLike(String value) {
            addCriterion("material_small_class not like", value, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassIn(List<String> values) {
            addCriterion("material_small_class in", values, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassNotIn(List<String> values) {
            addCriterion("material_small_class not in", values, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassBetween(String value1, String value2) {
            addCriterion("material_small_class between", value1, value2, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialSmallClassNotBetween(String value1, String value2) {
            addCriterion("material_small_class not between", value1, value2, "materialSmallClass");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNull() {
            addCriterion("material_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIsNotNull() {
            addCriterion("material_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeEqualTo(String value) {
            addCriterion("material_type =", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotEqualTo(String value) {
            addCriterion("material_type <>", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThan(String value) {
            addCriterion("material_type >", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeGreaterThanOrEqualTo(String value) {
            addCriterion("material_type >=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThan(String value) {
            addCriterion("material_type <", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLessThanOrEqualTo(String value) {
            addCriterion("material_type <=", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeLike(String value) {
            addCriterion("material_type like", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotLike(String value) {
            addCriterion("material_type not like", value, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeIn(List<String> values) {
            addCriterion("material_type in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotIn(List<String> values) {
            addCriterion("material_type not in", values, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeBetween(String value1, String value2) {
            addCriterion("material_type between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNotBetween(String value1, String value2) {
            addCriterion("material_type not between", value1, value2, "materialType");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewIsNull() {
            addCriterion("material_type_new is null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewIsNotNull() {
            addCriterion("material_type_new is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewEqualTo(String value) {
            addCriterion("material_type_new =", value, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewNotEqualTo(String value) {
            addCriterion("material_type_new <>", value, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewGreaterThan(String value) {
            addCriterion("material_type_new >", value, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewGreaterThanOrEqualTo(String value) {
            addCriterion("material_type_new >=", value, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewLessThan(String value) {
            addCriterion("material_type_new <", value, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewLessThanOrEqualTo(String value) {
            addCriterion("material_type_new <=", value, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewLike(String value) {
            addCriterion("material_type_new like", value, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewNotLike(String value) {
            addCriterion("material_type_new not like", value, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewIn(List<String> values) {
            addCriterion("material_type_new in", values, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewNotIn(List<String> values) {
            addCriterion("material_type_new not in", values, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewBetween(String value1, String value2) {
            addCriterion("material_type_new between", value1, value2, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialTypeNewNotBetween(String value1, String value2) {
            addCriterion("material_type_new not between", value1, value2, "materialTypeNew");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNull() {
            addCriterion("material_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNotNull() {
            addCriterion("material_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdEqualTo(Long value) {
            addCriterion("material_id =", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotEqualTo(Long value) {
            addCriterion("material_id <>", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThan(Long value) {
            addCriterion("material_id >", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThanOrEqualTo(Long value) {
            addCriterion("material_id >=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThan(Long value) {
            addCriterion("material_id <", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThanOrEqualTo(Long value) {
            addCriterion("material_id <=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIn(List<Long> values) {
            addCriterion("material_id in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotIn(List<Long> values) {
            addCriterion("material_id not in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdBetween(Long value1, Long value2) {
            addCriterion("material_id between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotBetween(Long value1, Long value2) {
            addCriterion("material_id not between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNull() {
            addCriterion("figure_number is null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNotNull() {
            addCriterion("figure_number is not null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberEqualTo(String value) {
            addCriterion("figure_number =", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotEqualTo(String value) {
            addCriterion("figure_number <>", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThan(String value) {
            addCriterion("figure_number >", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThanOrEqualTo(String value) {
            addCriterion("figure_number >=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThan(String value) {
            addCriterion("figure_number <", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThanOrEqualTo(String value) {
            addCriterion("figure_number <=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLike(String value) {
            addCriterion("figure_number like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotLike(String value) {
            addCriterion("figure_number not like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIn(List<String> values) {
            addCriterion("figure_number in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotIn(List<String> values) {
            addCriterion("figure_number not in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberBetween(String value1, String value2) {
            addCriterion("figure_number between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotBetween(String value1, String value2) {
            addCriterion("figure_number not between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andChartVersionIsNull() {
            addCriterion("chart_version is null");
            return (Criteria) this;
        }

        public Criteria andChartVersionIsNotNull() {
            addCriterion("chart_version is not null");
            return (Criteria) this;
        }

        public Criteria andChartVersionEqualTo(String value) {
            addCriterion("chart_version =", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotEqualTo(String value) {
            addCriterion("chart_version <>", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionGreaterThan(String value) {
            addCriterion("chart_version >", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionGreaterThanOrEqualTo(String value) {
            addCriterion("chart_version >=", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLessThan(String value) {
            addCriterion("chart_version <", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLessThanOrEqualTo(String value) {
            addCriterion("chart_version <=", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLike(String value) {
            addCriterion("chart_version like", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotLike(String value) {
            addCriterion("chart_version not like", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionIn(List<String> values) {
            addCriterion("chart_version in", values, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotIn(List<String> values) {
            addCriterion("chart_version not in", values, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionBetween(String value1, String value2) {
            addCriterion("chart_version between", value1, value2, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotBetween(String value1, String value2) {
            addCriterion("chart_version not between", value1, value2, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewIsNull() {
            addCriterion("chart_version_new is null");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewIsNotNull() {
            addCriterion("chart_version_new is not null");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewEqualTo(String value) {
            addCriterion("chart_version_new =", value, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewNotEqualTo(String value) {
            addCriterion("chart_version_new <>", value, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewGreaterThan(String value) {
            addCriterion("chart_version_new >", value, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewGreaterThanOrEqualTo(String value) {
            addCriterion("chart_version_new >=", value, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewLessThan(String value) {
            addCriterion("chart_version_new <", value, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewLessThanOrEqualTo(String value) {
            addCriterion("chart_version_new <=", value, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewLike(String value) {
            addCriterion("chart_version_new like", value, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewNotLike(String value) {
            addCriterion("chart_version_new not like", value, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewIn(List<String> values) {
            addCriterion("chart_version_new in", values, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewNotIn(List<String> values) {
            addCriterion("chart_version_new not in", values, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewBetween(String value1, String value2) {
            addCriterion("chart_version_new between", value1, value2, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andChartVersionNewNotBetween(String value1, String value2) {
            addCriterion("chart_version_new not between", value1, value2, "chartVersionNew");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIsNull() {
            addCriterion("unit_weight is null");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIsNotNull() {
            addCriterion("unit_weight is not null");
            return (Criteria) this;
        }

        public Criteria andUnitWeightEqualTo(BigDecimal value) {
            addCriterion("unit_weight =", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotEqualTo(BigDecimal value) {
            addCriterion("unit_weight <>", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightGreaterThan(BigDecimal value) {
            addCriterion("unit_weight >", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_weight >=", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightLessThan(BigDecimal value) {
            addCriterion("unit_weight <", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_weight <=", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIn(List<BigDecimal> values) {
            addCriterion("unit_weight in", values, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotIn(List<BigDecimal> values) {
            addCriterion("unit_weight not in", values, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_weight between", value1, value2, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_weight not between", value1, value2, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewIsNull() {
            addCriterion("unit_weight_new is null");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewIsNotNull() {
            addCriterion("unit_weight_new is not null");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewEqualTo(BigDecimal value) {
            addCriterion("unit_weight_new =", value, "unitWeightNew");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewNotEqualTo(BigDecimal value) {
            addCriterion("unit_weight_new <>", value, "unitWeightNew");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewGreaterThan(BigDecimal value) {
            addCriterion("unit_weight_new >", value, "unitWeightNew");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_weight_new >=", value, "unitWeightNew");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewLessThan(BigDecimal value) {
            addCriterion("unit_weight_new <", value, "unitWeightNew");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_weight_new <=", value, "unitWeightNew");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewIn(List<BigDecimal> values) {
            addCriterion("unit_weight_new in", values, "unitWeightNew");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewNotIn(List<BigDecimal> values) {
            addCriterion("unit_weight_new not in", values, "unitWeightNew");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_weight_new between", value1, value2, "unitWeightNew");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNewNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_weight_new not between", value1, value2, "unitWeightNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIsNull() {
            addCriterion("machining_part_type is null");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIsNotNull() {
            addCriterion("machining_part_type is not null");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeEqualTo(String value) {
            addCriterion("machining_part_type =", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotEqualTo(String value) {
            addCriterion("machining_part_type <>", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeGreaterThan(String value) {
            addCriterion("machining_part_type >", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeGreaterThanOrEqualTo(String value) {
            addCriterion("machining_part_type >=", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLessThan(String value) {
            addCriterion("machining_part_type <", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLessThanOrEqualTo(String value) {
            addCriterion("machining_part_type <=", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLike(String value) {
            addCriterion("machining_part_type like", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotLike(String value) {
            addCriterion("machining_part_type not like", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIn(List<String> values) {
            addCriterion("machining_part_type in", values, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotIn(List<String> values) {
            addCriterion("machining_part_type not in", values, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeBetween(String value1, String value2) {
            addCriterion("machining_part_type between", value1, value2, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotBetween(String value1, String value2) {
            addCriterion("machining_part_type not between", value1, value2, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewIsNull() {
            addCriterion("machining_part_type_new is null");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewIsNotNull() {
            addCriterion("machining_part_type_new is not null");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewEqualTo(String value) {
            addCriterion("machining_part_type_new =", value, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewNotEqualTo(String value) {
            addCriterion("machining_part_type_new <>", value, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewGreaterThan(String value) {
            addCriterion("machining_part_type_new >", value, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewGreaterThanOrEqualTo(String value) {
            addCriterion("machining_part_type_new >=", value, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewLessThan(String value) {
            addCriterion("machining_part_type_new <", value, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewLessThanOrEqualTo(String value) {
            addCriterion("machining_part_type_new <=", value, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewLike(String value) {
            addCriterion("machining_part_type_new like", value, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewNotLike(String value) {
            addCriterion("machining_part_type_new not like", value, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewIn(List<String> values) {
            addCriterion("machining_part_type_new in", values, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewNotIn(List<String> values) {
            addCriterion("machining_part_type_new not in", values, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewBetween(String value1, String value2) {
            addCriterion("machining_part_type_new between", value1, value2, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNewNotBetween(String value1, String value2) {
            addCriterion("machining_part_type_new not between", value1, value2, "machiningPartTypeNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleIsNull() {
            addCriterion("surface_handle is null");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleIsNotNull() {
            addCriterion("surface_handle is not null");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleEqualTo(String value) {
            addCriterion("surface_handle =", value, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNotEqualTo(String value) {
            addCriterion("surface_handle <>", value, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleGreaterThan(String value) {
            addCriterion("surface_handle >", value, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleGreaterThanOrEqualTo(String value) {
            addCriterion("surface_handle >=", value, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleLessThan(String value) {
            addCriterion("surface_handle <", value, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleLessThanOrEqualTo(String value) {
            addCriterion("surface_handle <=", value, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleLike(String value) {
            addCriterion("surface_handle like", value, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNotLike(String value) {
            addCriterion("surface_handle not like", value, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleIn(List<String> values) {
            addCriterion("surface_handle in", values, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNotIn(List<String> values) {
            addCriterion("surface_handle not in", values, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleBetween(String value1, String value2) {
            addCriterion("surface_handle between", value1, value2, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNotBetween(String value1, String value2) {
            addCriterion("surface_handle not between", value1, value2, "surfaceHandle");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewIsNull() {
            addCriterion("surface_handle_new is null");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewIsNotNull() {
            addCriterion("surface_handle_new is not null");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewEqualTo(String value) {
            addCriterion("surface_handle_new =", value, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewNotEqualTo(String value) {
            addCriterion("surface_handle_new <>", value, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewGreaterThan(String value) {
            addCriterion("surface_handle_new >", value, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewGreaterThanOrEqualTo(String value) {
            addCriterion("surface_handle_new >=", value, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewLessThan(String value) {
            addCriterion("surface_handle_new <", value, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewLessThanOrEqualTo(String value) {
            addCriterion("surface_handle_new <=", value, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewLike(String value) {
            addCriterion("surface_handle_new like", value, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewNotLike(String value) {
            addCriterion("surface_handle_new not like", value, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewIn(List<String> values) {
            addCriterion("surface_handle_new in", values, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewNotIn(List<String> values) {
            addCriterion("surface_handle_new not in", values, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewBetween(String value1, String value2) {
            addCriterion("surface_handle_new between", value1, value2, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andSurfaceHandleNewNotBetween(String value1, String value2) {
            addCriterion("surface_handle_new not between", value1, value2, "surfaceHandleNew");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNull() {
            addCriterion("material is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNotNull() {
            addCriterion("material is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialEqualTo(String value) {
            addCriterion("material =", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotEqualTo(String value) {
            addCriterion("material <>", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThan(String value) {
            addCriterion("material >", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThanOrEqualTo(String value) {
            addCriterion("material >=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThan(String value) {
            addCriterion("material <", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThanOrEqualTo(String value) {
            addCriterion("material <=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLike(String value) {
            addCriterion("material like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotLike(String value) {
            addCriterion("material not like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialIn(List<String> values) {
            addCriterion("material in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotIn(List<String> values) {
            addCriterion("material not in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialBetween(String value1, String value2) {
            addCriterion("material between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotBetween(String value1, String value2) {
            addCriterion("material not between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNewIsNull() {
            addCriterion("material_new is null");
            return (Criteria) this;
        }

        public Criteria andMaterialNewIsNotNull() {
            addCriterion("material_new is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialNewEqualTo(String value) {
            addCriterion("material_new =", value, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewNotEqualTo(String value) {
            addCriterion("material_new <>", value, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewGreaterThan(String value) {
            addCriterion("material_new >", value, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewGreaterThanOrEqualTo(String value) {
            addCriterion("material_new >=", value, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewLessThan(String value) {
            addCriterion("material_new <", value, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewLessThanOrEqualTo(String value) {
            addCriterion("material_new <=", value, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewLike(String value) {
            addCriterion("material_new like", value, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewNotLike(String value) {
            addCriterion("material_new not like", value, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewIn(List<String> values) {
            addCriterion("material_new in", values, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewNotIn(List<String> values) {
            addCriterion("material_new not in", values, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewBetween(String value1, String value2) {
            addCriterion("material_new between", value1, value2, "materialNew");
            return (Criteria) this;
        }

        public Criteria andMaterialNewNotBetween(String value1, String value2) {
            addCriterion("material_new not between", value1, value2, "materialNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskIsNull() {
            addCriterion("or_spare_parts_mask is null");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskIsNotNull() {
            addCriterion("or_spare_parts_mask is not null");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskEqualTo(String value) {
            addCriterion("or_spare_parts_mask =", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNotEqualTo(String value) {
            addCriterion("or_spare_parts_mask <>", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskGreaterThan(String value) {
            addCriterion("or_spare_parts_mask >", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskGreaterThanOrEqualTo(String value) {
            addCriterion("or_spare_parts_mask >=", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskLessThan(String value) {
            addCriterion("or_spare_parts_mask <", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskLessThanOrEqualTo(String value) {
            addCriterion("or_spare_parts_mask <=", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskLike(String value) {
            addCriterion("or_spare_parts_mask like", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNotLike(String value) {
            addCriterion("or_spare_parts_mask not like", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskIn(List<String> values) {
            addCriterion("or_spare_parts_mask in", values, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNotIn(List<String> values) {
            addCriterion("or_spare_parts_mask not in", values, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskBetween(String value1, String value2) {
            addCriterion("or_spare_parts_mask between", value1, value2, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNotBetween(String value1, String value2) {
            addCriterion("or_spare_parts_mask not between", value1, value2, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewIsNull() {
            addCriterion("or_spare_parts_mask_new is null");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewIsNotNull() {
            addCriterion("or_spare_parts_mask_new is not null");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewEqualTo(String value) {
            addCriterion("or_spare_parts_mask_new =", value, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewNotEqualTo(String value) {
            addCriterion("or_spare_parts_mask_new <>", value, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewGreaterThan(String value) {
            addCriterion("or_spare_parts_mask_new >", value, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewGreaterThanOrEqualTo(String value) {
            addCriterion("or_spare_parts_mask_new >=", value, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewLessThan(String value) {
            addCriterion("or_spare_parts_mask_new <", value, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewLessThanOrEqualTo(String value) {
            addCriterion("or_spare_parts_mask_new <=", value, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewLike(String value) {
            addCriterion("or_spare_parts_mask_new like", value, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewNotLike(String value) {
            addCriterion("or_spare_parts_mask_new not like", value, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewIn(List<String> values) {
            addCriterion("or_spare_parts_mask_new in", values, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewNotIn(List<String> values) {
            addCriterion("or_spare_parts_mask_new not in", values, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewBetween(String value1, String value2) {
            addCriterion("or_spare_parts_mask_new between", value1, value2, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNewNotBetween(String value1, String value2) {
            addCriterion("or_spare_parts_mask_new not between", value1, value2, "orSparePartsMaskNew");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIsNull() {
            addCriterion("brand_material_code is null");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIsNotNull() {
            addCriterion("brand_material_code is not null");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeEqualTo(String value) {
            addCriterion("brand_material_code =", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotEqualTo(String value) {
            addCriterion("brand_material_code <>", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeGreaterThan(String value) {
            addCriterion("brand_material_code >", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("brand_material_code >=", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLessThan(String value) {
            addCriterion("brand_material_code <", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("brand_material_code <=", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLike(String value) {
            addCriterion("brand_material_code like", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotLike(String value) {
            addCriterion("brand_material_code not like", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIn(List<String> values) {
            addCriterion("brand_material_code in", values, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotIn(List<String> values) {
            addCriterion("brand_material_code not in", values, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeBetween(String value1, String value2) {
            addCriterion("brand_material_code between", value1, value2, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("brand_material_code not between", value1, value2, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeIsNull() {
            addCriterion("perchasing_leadtime is null");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeIsNotNull() {
            addCriterion("perchasing_leadtime is not null");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeEqualTo(Long value) {
            addCriterion("perchasing_leadtime =", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeNotEqualTo(Long value) {
            addCriterion("perchasing_leadtime <>", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeGreaterThan(Long value) {
            addCriterion("perchasing_leadtime >", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeGreaterThanOrEqualTo(Long value) {
            addCriterion("perchasing_leadtime >=", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeLessThan(Long value) {
            addCriterion("perchasing_leadtime <", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeLessThanOrEqualTo(Long value) {
            addCriterion("perchasing_leadtime <=", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeIn(List<Long> values) {
            addCriterion("perchasing_leadtime in", values, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeNotIn(List<Long> values) {
            addCriterion("perchasing_leadtime not in", values, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeBetween(Long value1, Long value2) {
            addCriterion("perchasing_leadtime between", value1, value2, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeNotBetween(Long value1, Long value2) {
            addCriterion("perchasing_leadtime not between", value1, value2, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityIsNull() {
            addCriterion("min_perchase_quantity is null");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityIsNotNull() {
            addCriterion("min_perchase_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityEqualTo(Long value) {
            addCriterion("min_perchase_quantity =", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityNotEqualTo(Long value) {
            addCriterion("min_perchase_quantity <>", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityGreaterThan(Long value) {
            addCriterion("min_perchase_quantity >", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("min_perchase_quantity >=", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityLessThan(Long value) {
            addCriterion("min_perchase_quantity <", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityLessThanOrEqualTo(Long value) {
            addCriterion("min_perchase_quantity <=", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityIn(List<Long> values) {
            addCriterion("min_perchase_quantity in", values, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityNotIn(List<Long> values) {
            addCriterion("min_perchase_quantity not in", values, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityBetween(Long value1, Long value2) {
            addCriterion("min_perchase_quantity between", value1, value2, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityNotBetween(Long value1, Long value2) {
            addCriterion("min_perchase_quantity not between", value1, value2, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityIsNull() {
            addCriterion("min_package_quantity is null");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityIsNotNull() {
            addCriterion("min_package_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityEqualTo(Long value) {
            addCriterion("min_package_quantity =", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityNotEqualTo(Long value) {
            addCriterion("min_package_quantity <>", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityGreaterThan(Long value) {
            addCriterion("min_package_quantity >", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("min_package_quantity >=", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityLessThan(Long value) {
            addCriterion("min_package_quantity <", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityLessThanOrEqualTo(Long value) {
            addCriterion("min_package_quantity <=", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityIn(List<Long> values) {
            addCriterion("min_package_quantity in", values, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityNotIn(List<Long> values) {
            addCriterion("min_package_quantity not in", values, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityBetween(Long value1, Long value2) {
            addCriterion("min_package_quantity between", value1, value2, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityNotBetween(Long value1, Long value2) {
            addCriterion("min_package_quantity not between", value1, value2, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIsNull() {
            addCriterion("sync_status is null");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIsNotNull() {
            addCriterion("sync_status is not null");
            return (Criteria) this;
        }

        public Criteria andSyncStatusEqualTo(Integer value) {
            addCriterion("sync_status =", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotEqualTo(Integer value) {
            addCriterion("sync_status <>", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusGreaterThan(Integer value) {
            addCriterion("sync_status >", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("sync_status >=", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusLessThan(Integer value) {
            addCriterion("sync_status <", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusLessThanOrEqualTo(Integer value) {
            addCriterion("sync_status <=", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIn(List<Integer> values) {
            addCriterion("sync_status in", values, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotIn(List<Integer> values) {
            addCriterion("sync_status not in", values, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusBetween(Integer value1, Integer value2) {
            addCriterion("sync_status between", value1, value2, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("sync_status not between", value1, value2, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncMesIsNull() {
            addCriterion("sync_mes is null");
            return (Criteria) this;
        }

        public Criteria andSyncMesIsNotNull() {
            addCriterion("sync_mes is not null");
            return (Criteria) this;
        }

        public Criteria andSyncMesEqualTo(String value) {
            addCriterion("sync_mes =", value, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesNotEqualTo(String value) {
            addCriterion("sync_mes <>", value, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesGreaterThan(String value) {
            addCriterion("sync_mes >", value, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesGreaterThanOrEqualTo(String value) {
            addCriterion("sync_mes >=", value, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesLessThan(String value) {
            addCriterion("sync_mes <", value, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesLessThanOrEqualTo(String value) {
            addCriterion("sync_mes <=", value, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesLike(String value) {
            addCriterion("sync_mes like", value, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesNotLike(String value) {
            addCriterion("sync_mes not like", value, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesIn(List<String> values) {
            addCriterion("sync_mes in", values, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesNotIn(List<String> values) {
            addCriterion("sync_mes not in", values, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesBetween(String value1, String value2) {
            addCriterion("sync_mes between", value1, value2, "syncMes");
            return (Criteria) this;
        }

        public Criteria andSyncMesNotBetween(String value1, String value2) {
            addCriterion("sync_mes not between", value1, value2, "syncMes");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNull() {
            addCriterion("attribute1 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNotNull() {
            addCriterion("attribute1 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute1EqualTo(String value) {
            addCriterion("attribute1 =", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotEqualTo(String value) {
            addCriterion("attribute1 <>", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThan(String value) {
            addCriterion("attribute1 >", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThanOrEqualTo(String value) {
            addCriterion("attribute1 >=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThan(String value) {
            addCriterion("attribute1 <", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThanOrEqualTo(String value) {
            addCriterion("attribute1 <=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Like(String value) {
            addCriterion("attribute1 like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotLike(String value) {
            addCriterion("attribute1 not like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1In(List<String> values) {
            addCriterion("attribute1 in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotIn(List<String> values) {
            addCriterion("attribute1 not in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Between(String value1, String value2) {
            addCriterion("attribute1 between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotBetween(String value1, String value2) {
            addCriterion("attribute1 not between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNull() {
            addCriterion("attribute2 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNotNull() {
            addCriterion("attribute2 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute2EqualTo(String value) {
            addCriterion("attribute2 =", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotEqualTo(String value) {
            addCriterion("attribute2 <>", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThan(String value) {
            addCriterion("attribute2 >", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThanOrEqualTo(String value) {
            addCriterion("attribute2 >=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThan(String value) {
            addCriterion("attribute2 <", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThanOrEqualTo(String value) {
            addCriterion("attribute2 <=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Like(String value) {
            addCriterion("attribute2 like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotLike(String value) {
            addCriterion("attribute2 not like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2In(List<String> values) {
            addCriterion("attribute2 in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotIn(List<String> values) {
            addCriterion("attribute2 not in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Between(String value1, String value2) {
            addCriterion("attribute2 between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotBetween(String value1, String value2) {
            addCriterion("attribute2 not between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNull() {
            addCriterion("attribute3 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNotNull() {
            addCriterion("attribute3 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute3EqualTo(String value) {
            addCriterion("attribute3 =", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotEqualTo(String value) {
            addCriterion("attribute3 <>", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThan(String value) {
            addCriterion("attribute3 >", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThanOrEqualTo(String value) {
            addCriterion("attribute3 >=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThan(String value) {
            addCriterion("attribute3 <", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThanOrEqualTo(String value) {
            addCriterion("attribute3 <=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Like(String value) {
            addCriterion("attribute3 like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotLike(String value) {
            addCriterion("attribute3 not like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3In(List<String> values) {
            addCriterion("attribute3 in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotIn(List<String> values) {
            addCriterion("attribute3 not in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Between(String value1, String value2) {
            addCriterion("attribute3 between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotBetween(String value1, String value2) {
            addCriterion("attribute3 not between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNull() {
            addCriterion("activity_code is null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNotNull() {
            addCriterion("activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualTo(String value) {
            addCriterion("activity_code =", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualTo(String value) {
            addCriterion("activity_code <>", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThan(String value) {
            addCriterion("activity_code >", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_code >=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThan(String value) {
            addCriterion("activity_code <", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("activity_code <=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLike(String value) {
            addCriterion("activity_code like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotLike(String value) {
            addCriterion("activity_code not like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIn(List<String> values) {
            addCriterion("activity_code in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotIn(List<String> values) {
            addCriterion("activity_code not in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeBetween(String value1, String value2) {
            addCriterion("activity_code between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotBetween(String value1, String value2) {
            addCriterion("activity_code not between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeIsNull() {
            addCriterion("material_attribute is null");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeIsNotNull() {
            addCriterion("material_attribute is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeEqualTo(String value) {
            addCriterion("material_attribute =", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeNotEqualTo(String value) {
            addCriterion("material_attribute <>", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeGreaterThan(String value) {
            addCriterion("material_attribute >", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeGreaterThanOrEqualTo(String value) {
            addCriterion("material_attribute >=", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeLessThan(String value) {
            addCriterion("material_attribute <", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeLessThanOrEqualTo(String value) {
            addCriterion("material_attribute <=", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeLike(String value) {
            addCriterion("material_attribute like", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeNotLike(String value) {
            addCriterion("material_attribute not like", value, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeIn(List<String> values) {
            addCriterion("material_attribute in", values, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeNotIn(List<String> values) {
            addCriterion("material_attribute not in", values, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeBetween(String value1, String value2) {
            addCriterion("material_attribute between", value1, value2, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andMaterialAttributeNotBetween(String value1, String value2) {
            addCriterion("material_attribute not between", value1, value2, "materialAttribute");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeIsNull() {
            addCriterion("inventory_type is null");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeIsNotNull() {
            addCriterion("inventory_type is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeEqualTo(String value) {
            addCriterion("inventory_type =", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotEqualTo(String value) {
            addCriterion("inventory_type <>", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeGreaterThan(String value) {
            addCriterion("inventory_type >", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_type >=", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLessThan(String value) {
            addCriterion("inventory_type <", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLessThanOrEqualTo(String value) {
            addCriterion("inventory_type <=", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeLike(String value) {
            addCriterion("inventory_type like", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotLike(String value) {
            addCriterion("inventory_type not like", value, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeIn(List<String> values) {
            addCriterion("inventory_type in", values, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotIn(List<String> values) {
            addCriterion("inventory_type not in", values, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeBetween(String value1, String value2) {
            addCriterion("inventory_type between", value1, value2, "inventoryType");
            return (Criteria) this;
        }

        public Criteria andInventoryTypeNotBetween(String value1, String value2) {
            addCriterion("inventory_type not between", value1, value2, "inventoryType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}