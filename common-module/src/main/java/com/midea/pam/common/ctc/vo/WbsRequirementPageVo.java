package com.midea.pam.common.ctc.vo;

import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class WbsRequirementPageVo {

    private String wbsLayer;
    private String wbsSummaryCode;

    private String requirementCode;

    private String projectId;

    private Integer purchaseType;

    @ApiModelProperty(value = "是否急件")
    private Boolean dispatchIs;

    @ApiModelProperty(value = "物料采购需求id",notes = "采购合同查询使用")
    private Long requirementId;

    @ApiModelProperty(value = "详细设计单据id")
    private Long receiptsId;

    @ApiModelProperty("序号")
    private Integer num;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String projectNum;

    @ApiModelProperty("项目类型")
    private String projectType;

    @ApiModelProperty("项目状态")
    private String projectStatus;

    @ApiModelProperty("需求预算")
    private BigDecimal demandCost;

    @ApiModelProperty("剩余wbs需求预算占用（外包）")
    private BigDecimal wbsRemainingDemandOsCost;

    @ApiModelProperty("累计采购合同占用金额")
    private BigDecimal contractTotalAmount;

    @ApiModelProperty("状态集合")
    private List<Integer> statusList;

    @ApiModelProperty("未下达量")
    private BigDecimal unreleasedAmount;

    @ApiModelProperty("已下达量")
    private BigDecimal releasedQuantity;

    @ApiModelProperty("订单量")
    private BigDecimal orderQuantity;

    @ApiModelProperty("项目现有量")
    private BigDecimal projectHavedQuantity;

    @ApiModelProperty("非项目现有量")
    private BigDecimal noProjectHavedQuantity;

    @ApiModelProperty(value = "项目业务实体")
    private String projectOuName;

    @ApiModelProperty(value = "项目库存组织id")
    private Long projectOrganizationId;

    @ApiModelProperty("项目")
    private ProjectDto projectDto;

    @ApiModelProperty("物料")
    private MaterialDto materialDto;

    @ApiModelProperty("系统建议下达量")
    private BigDecimal systemQuantity;

    @ApiModelProperty("下达采购需求")
    private List<PurchaseMaterialRequirementDto> requirementDtos;

    @ApiModelProperty("订单详情")
    private List<PurchaseOrderDetailDto> purchaseOrderDetailDtos;

    @ApiModelProperty("业务实体ID")
    private Long ouId;

    /*筛选条件*/
    private Integer pageNum = 1;

    private Integer pageSize = 10;

    @ApiModelProperty(value = "模糊物料编码")
    private String fuzzyErpCode;

    @ApiModelProperty(value = "模糊物料描述")
    private String fuzzyMaterielDescr;

    @ApiModelProperty(value = "模糊PAM编码")
    private String fuzzyPamCode;

    @ApiModelProperty(value = "模糊项目名称")
    private String fuzzyProjectName;

    @ApiModelProperty(value = "模糊项目编号")
    private String fuzzyProjectNum;

    @ApiModelProperty(value = "业务实体id(多选)")
    private String projectOuId;

    @ApiModelProperty(value = "交货开始日期")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "交货开始日期")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "状态(多选)")
    private String[] manyStatus;

    @ApiModelProperty(value = "业务实体(多选)")
    private String[] manyProjectOuName;

    @ApiModelProperty(value = "业务实体id(多选)")
    private String[] manyProjectOuId;

    @ApiModelProperty(value = "状态")
    private String statusStr;

    @ApiModelProperty(value = "发布日期")
    private Date publishTime;

    @ApiModelProperty(value = "发布开始日期")
    private Date publishStartTime;

    @ApiModelProperty(value = "发布结束日期")
    private Date publishEndTime;

    @ApiModelProperty(value = "物料id")
    private Long materialId;

    @ApiModelProperty(value = "物料中类")
    private String codingMiddleclass;

    @ApiModelProperty(value = "物料小类")
    private String materialType;

    @ApiModelProperty(value = "图号/型号")
    private String model;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "图纸版本号")
    private String chartVersion;

    @ApiModelProperty(value = "设计发布批次号")
    private String designReleaseLotNumber;

    @ApiModelProperty(value = "更新开始日期")
    private Date updateStartDate;

    @ApiModelProperty(value = "更新结束日期")
    private Date updateEndDate;

    private List<Long> ouList;


    @ApiModelProperty(value = "批准供应商数量(erp同步或创建时统计)")
    private Integer approvedSupplierNumber;

    @ApiModelProperty(value = "需求类型")
    private String requirementTypeStr;

    @ApiModelProperty(value = "冻结标志（0：未冻结，1：冻结）")
    private Integer freezeFlag;

}
