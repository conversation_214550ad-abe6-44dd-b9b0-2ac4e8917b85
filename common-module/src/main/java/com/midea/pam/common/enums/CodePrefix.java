package com.midea.pam.common.enums;

import com.midea.pam.common.base.BaseEnum;

/**
 * 全局编码前缀枚举
 */
public enum CodePrefix implements BaseEnum<String> {
    PURCHASE_ORDER_1("P", "采购订单前缀1"),
    PURCHASE_ORDER_2("129701", "采购订单前缀2"),
    PURCHASE_ORDER_3("KFL", "柔性单位采购订单前缀"),
    PAM_CODE("P", "PAM编码"),
    BUSINESS("SJ", "商机编号"),
    LEAD("XS", "线索编号"),
    MATERIAL_PRICE_CONFIG("MP", "价格类型编码前缀"),
    MATERIALGET("LL", "领料单编号"),

    MATERIAL_COST_TRANSFER("CT", "成本转移单编号"),
    INVOICECODE("IV", "应收发票编号"),
    MATERIALTRANSFER("ZY", "转移单编号"),
    MATERIALRETURN("LL", "退料单编号"),
    BUSINESS_PLAN("FA", "商机方案编号"),
    BUSINESS_PLAN_QUOTATION("BJ", "商机方案报价编号"),
    CUSTOMER_PAM_CODE("KH", "客户pam编码"),
    CARRYOVER_BILL_CODE("JZ", "结转单编码"),
    WORKING_HOUR_COST_CODE("BM", "工时变更单编码"),
    CARRYOVER_BILL_INCOME_FILE_NAME("SRQRQD", "项目收入确认清单文件名"),
    CARRYOVER_BILL_FILE_NAME("JZQD", "结转清单文件名"),
    REVENUE_COST_ORDER_CODE("SG", "收入成本工单编码"),
    COST_COLLECTION("CC", "成本归集编码"),
    WORKING_HOUR_ACCOUNTING("GS", "工时成本入账单编码"),
    WAIT_CARRYOVER_BILL_FILE_NAME("JZQRJH", "结转确认计划文件名"),
    DIFFERENCE_SHARE_ACCOUNT_CODE("CY", "差异分摊编码"),
    PROJECT_PROBLEM_CODE("WT", "项目问题编码"),
    PURCHASE_CONTRACT_MATERIAL_BUDGET_FILE_NAME("PCMB", "采购合同关联预算信息文件名"),
    PPQD("PPQD", "品牌清单文件名"),
    BR_CODE("BR", "品牌编码"),
    BRAND_CODE("Brand", "单据号"),
    CONTRACT_CLASSIFICATION_CODE("CFL", "合同资产重分类入账编码"),
    HRO_REQUIREMENT_CODE("RLDG", "点工需求单据编号"),
    HRO_WORKING_HOUR_CODE("DGGS", "人力点工工时导入单号"),
    HRO_WORKING_HOUR_BILL_CODE("DGDZ", "人力点工工时对账单单号"),
    PROJECT_SATISFACTION_AND_INCIDENT_CODE("AQ", "项目满意度和安全事件编码"),
    ACCOUNTING_BATCH_NUM("AC", "入账批次号"),
    EXCHANGE_ACCOUNT_CODE("HDSY", "汇兑损益入帐单"),
    ASSET_DEPRN_ACCOUNTING("ZJ", "资产折旧成本入账单"),
    STANDARD_TERMS("CGTK","标准条款"),
    ;

    private String code;
    private String msg;

    CodePrefix(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String code() {
        return code;
    }

    @Override
    public String msg() {
        return msg;
    }
}
