package com.midea.pam.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.AreaDto;
import com.midea.pam.common.basedata.dto.BudgetDepDto;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.FeeItemDto;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.LaborCost;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UnitOuRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.gateway.entity.CurrentContext;
import com.midea.pam.system.SystemContext;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.text.NumberFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 获取basedata数据.
 * chengzy.
 */
public class CacheDataUtils {

    private static final Joiner CACHE_KEY_JOINER = Joiner.on(":");
    private final static StringRedisTemplate stringRedisTemplate;
    private static final String CODE_SEQUENCE = "CODE:SEQUENCE:";
    private static Random rand = new Random();
    private static RestTemplate restTemplate;



    static {
        restTemplate = (RestTemplate) ApplicationContextProvider.getBean("restTemplate");
        stringRedisTemplate = (StringRedisTemplate) ApplicationContextProvider.getBean("stringRedisTemplate");
    }

    /**
     * 获取缓存内容.
     *
     * @param key
     * @return String
     */
    public static String get(final String key) {
        String string = stringRedisTemplate.opsForValue().get(key);
        if (!StringUtils.isEmpty(string)) {
            return string;
        }
        return null;
    }

    /**
     * 设置缓存内容.
     *
     * @param key
     * @param value
     * @return String
     */
    public static String set(final String key, final String value) {
        stringRedisTemplate.opsForValue().set(key, String.valueOf(value));
        return null;
    }


    /**
     * 获取区域.
     *
     * @param id 区域id.
     * @return 区域
     */
    public static AreaDto findAreaById(final Long id) {
        if (id == null) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.AREA + id));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, AreaDto.class);
        }
        return null;
    }

    /**
     * 获取区域.
     *
     * @param code 区域代码.
     * @return 区域
     */
    public static AreaDto findAreaByCode(final String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.AREA + code));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, AreaDto.class);
        }
        return null;
    }

    /**
     * 获取用户.
     *
     * @param id 用户id.
     * @return 用户
     */
    public static UserInfo findUserById(final Long id) {
        if (id == null) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.USER + id));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, UserInfo.class);
        }
        return null;
    }

    public static String findUserNameById(final Long id) {
        if (id == null) {
            return null;
        }
        UserInfo user = findUserById(id);
        if (user != null) {
            return user.getName();
        }
        return null;
    }

    public static String findUserMipById(final Long id) {
        if (id == null) {
            return null;
        }
        UserInfo user = findUserById(id);
        if (user != null) {
            return user.getUsername();
        }
        return null;
    }

    /**
     * 获取用户.
     *
     * @param employeeNumber 用户员工编号.
     * @return 用户
     */
    public static UserInfo findUserByNumber(final String employeeNumber) {
        if (StringUtils.isEmpty(employeeNumber)) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.USER + employeeNumber));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, UserInfo.class);
        }
        return null;
    }

    /**
     * 获取用户.
     *
     * @param mip 用户.
     * @return 用户
     */
    public static UserInfo findUserByMip(final String mip) {
        if (StringUtils.isEmpty(mip)) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.USER + mip));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, UserInfo.class);
        }
        return null;
    }

    /**
     * 获取erp组织关系.
     *
     * @param orgId orgId.
     * @return erp组织关系dto
     */
    public static OrganizationRelDto findOrganizationById(final Long orgId) {
        if (orgId == null) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.ORGANIZATION + orgId));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, OrganizationRelDto.class);
        }
        return null;
    }

    public static String findOrganizationNameById(final Long orgId) {
        if (orgId == null) {
            return null;
        }
        OrganizationRelDto organizationRelDto = findOrganizationById(orgId);
        if (organizationRelDto != null) {
            return organizationRelDto.getOrganizationName();
        }
        return null;
    }

    /**
     * 获取数据字典.
     *
     * @param type 数据字典类型.
     * @param code 数据字典编码.
     * @return 数据字典
     */
    public static DictDto findDictByTypeAndCode(final String type, final String code) {
        if (StringUtils.isEmpty(type) || StringUtils.isEmpty(code)) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.DICT_CODE + type + ":" + code));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, DictDto.class);
        }
        return null;
    }

    /**
     * 获取数据字典.
     *
     * @param type 数据字典类型.
     * @param name 数据字典名称.
     * @return 数据字典
     */
    public static DictDto findDictByTypeAndName(final String type, final String name) {
        if (StringUtils.isEmpty(type) || StringUtils.isEmpty(name)) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.DICT_NAME + type + ":" + name));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, DictDto.class);
        }
        return null;
    }

    /**
     * 获取数据字典.
     *
     * @param id 数据字典ID.
     * @return 数据字典
     */
    public static DictDto findDictById(final Long id) {
        if (id == null) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.DICT_ID + id));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, DictDto.class);
        }
        return new DictDto();
    }

    /**
     * 获取虚拟部门.
     *
     * @param id 部门id.
     * @return 部门
     */
    public static Unit findUnitById(final Long id) {
        if (id == null) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.UNIT + id));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, Unit.class);
        }
        return null;
    }

    public static String findUnitNameById(final Long id) {
        if (id == null) {
            return null;
        }
        Unit unit = findUnitById(id);
        if (unit != null) {
            return unit.getUnitName();
        }
        return null;
    }

    /**
     * 获取费用类型.
     *
     * @param id 部门id.
     * @return 部门
     */
    public static FeeItemDto findFeeItemById(final Long id) {
        if (id == null) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.FEEITEM + id));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, FeeItemDto.class);
        }
        return null;
    }

    /**
     * 查询所有费用类型
     *
     * @return 费用列表
     */
    public static List<FeeItemDto> findAllFeeItem() {
        final Set<String> keys = stringRedisTemplate.keys(buildKey(Constants.Prefix.FEEITEM + "*"));
        final List<String> list = stringRedisTemplate.opsForValue().multiGet(keys);
        List<FeeItemDto> feeItemDtoList = new ArrayList<>();
        if (ListUtils.isNotEmpty(list)) {
            list.forEach(s -> {
                final FeeItemDto feeItemDto = JSONObject.parseObject(s, FeeItemDto.class);
                feeItemDtoList.add(feeItemDto);
            });
        }
        return feeItemDtoList;
    }

    /**
     * 获取费用类型.
     *
     * @param feeFlag 费用类型.
     * @return 部门
     */
    public static FeeItemDto findFeeItemByFeeFlag(final Long bizUnitId, final Integer feeFlag) {
        if (bizUnitId == null || feeFlag == null) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.FEEITEM + bizUnitId + ":" + feeFlag));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, FeeItemDto.class);
        }
        return null;
    }

    /**
     * 获取预算部门.
     *
     * @param id 部门id.
     * @return 部门
     */
    public static BudgetDepDto findBudgetDepById(final Long id) {
        if (id == null) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.BUDGET_DEP + id));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, BudgetDepDto.class);
        }
        return null;
    }

    /**
     * 获取业务实体ou.
     *
     * @param id ouId
     * @return 业务实体ou
     */
    public static OperatingUnitDto findOuById(final Long id) {
        if (id == null) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.OU + id));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, OperatingUnitDto.class);
        }
        return null;
    }

    public static String findOuNameById(final Long id) {
        if (id == null) {
            return null;
        }
        OperatingUnit ou = findOuById(id);
        if (ou != null) {
            return ou.getOperatingUnitName();
        }
        return null;
    }

    /**
     * 获取PAM启用的业务实体ou列表.
     *
     * @return ouList
     */
    public static List<OperatingUnit> getPamEnabledOuList() {
        List<String> strings = scanMatchKeysAndMultiGet(buildKey(Constants.Prefix.OU));
        return strings.stream()
                .map(s -> JSONObject.parseObject(s, OperatingUnit.class))
                .filter(ou -> !PublicUtil.isEmpty(ou) && !PublicUtil.isEmpty(ou.getPamEnabled()) && "0".equals(String.valueOf(ou.getPamEnabled())))
                .collect(Collectors.toList());
    }

    /**
     * 获取虚拟部门与业务实体关系.
     *
     * @param ouId ouId
     * @return 业务实体ou
     */
    public static UnitOuRel findUnitOuRelByOuId(final Long ouId) {
        if (ouId == null) {
            return null;
        }
        //PengBo;--获取ou_id(业务实体id)
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.OU_UNIT_REL + ouId));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, UnitOuRel.class);
        }
        return null;
    }

    /**
     * 获取客户
     *
     * @param id customerId
     * @return CustomerDto
     */
    public static CustomerDto findCustomerById(final Long id) {
        if (id == null) {
            return null;
        }
//        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.CUSTOMER + id));
//        if (!StringUtils.isEmpty(json)) {
//            return JSONObject.parseObject(json, CustomerDto.class);
//        }
        String url = String.format("%scustomer/getCustomerId?customerId=%s", ModelsEnum.CRM.getBaseUrl(), id);
        String crmRes = restTemplate.getForObject(url, String.class);
        final DataResponse<CustomerDto> customerDtoDataResponse = JSON.parseObject(crmRes, new TypeReference<DataResponse<CustomerDto>>() {
        });
        final CustomerDto customerDto = customerDtoDataResponse.getData();
        if (Objects.nonNull(customerDto)) {
            stringRedisTemplate.opsForValue().set(buildKey(Constants.Prefix.CUSTOMER + id), JSON.toJSONString(customerDto));
        }
        return customerDto;
    }

    /**
     * 获取会计区间
     *
     * @param applicationId applicationId
     * @param ledgerId      ledgerId
     * @param periodName    periodName
     * @return GlPeriodDto
     */
    public static GlPeriodDto findCachePeriod(final Long applicationId, Long ledgerId, String periodName) {
        if (applicationId == null || ledgerId == null || StringUtils.isEmpty(periodName)) {
            return null;
        }
        System.out.println(buildKey(Constants.Prefix.PERIOD, applicationId, ledgerId, periodName));
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.PERIOD, applicationId, ledgerId, periodName));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, GlPeriodDto.class);
        }
        return null;
    }

    /**
     * 获取库存会计区间
     *
     * @param organizationId
     * @param periodName
     * @return
     */
    public static GlPeriodDto findCacheOrgPeriod(final Long organizationId, String periodName) {
        if (organizationId == null || StringUtils.isEmpty(periodName)) {
            return null;
        }
        System.out.println(buildKey(Constants.Prefix.ORG_PERIOD, organizationId, periodName));
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.ORG_PERIOD, organizationId, periodName));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, GlPeriodDto.class);
        }
        return null;
    }

    /**
     * 获取最近的打开的库存会计日期
     *
     * @param organizationId 分类详见GlPeriod
     * @param localDate      需要查询的日期
     * @param reTry          递归次数
     * @return GlPeriodDto  开放的会计期间
     */
    public static GlPeriodDto getOpenOrgGlPeriod(Long organizationId, LocalDate localDate, int reTry) {
        if (organizationId == null || localDate == null) {
            return null;
        }
        if (reTry < 1) {
            reTry = 1;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        GlPeriodDto dto = CacheDataUtils.findCacheOrgPeriod(organizationId, localDate.format(formatter));
        if (null == dto || !dto.getShowStatus().equals(GlPeriodDto.OPEN_STATUS)) {
            localDate = localDate.minusMonths(1);
            if (reTry == 1) {
                return null;
            }
            return getOpenOrgGlPeriod(organizationId, localDate, --reTry);
        }
        return dto;
    }


    /**
     * 获取预算角色.
     *
     * @param id 角色id
     * @return 预算角色
     */
    public static LaborCost findLaborCostById(final Long id) {
        if (id == null) {
            return null;
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.LABOR_COST + id));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, LaborCost.class);
        }
        return null;
    }

    /**
     * 生成业务编号.
     *
     * @param len  流水长度(不含前面的yyyyMMdd)
     * @param type 业务类型
     * @return 业务编号
     */
    public static String generateSequence(final Integer len, final String type) {
        return generateSequence(len, type, "yyyyMMdd");
    }

    /**
     * 生成业务编号.
     *
     * @param len        流水长度(不含前面的yyyyMMdd)
     * @param type       业务类型
     * @param dataFormat 时间格式
     * @return 业务编号
     */
    public static String generateSequence(final Integer len, final String type, final String dataFormat){
        if (len == null || len <= 0 || com.midea.pam.common.util.StringUtils.isEmpty(type)) {
            return null;
        }
        StringBuilder maxStr = new StringBuilder();
        for (int i = 0; i < len; i++) {
            maxStr.append("9");
        }
        long max = Long.parseLong(maxStr.toString());

        ZoneId zoneId = ZoneId.systemDefault();
        //服务器时间出现2019-09-26格式的时间，且dataFormat为yyyyMMdd这类不带“-”时，会报错Unsupported field: MinuteOfHour
        //final String today = LocalDate.now(zoneId).format(DateTimeFormatter.ofPattern(dataFormat));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dataFormat).withLocale(Locale.getDefault()).withZone(zoneId);
        final String today = formatter.format(Instant.now());

        final Date expireTime = Date.from(LocalDate.now(zoneId).plusDays(1).atStartOfDay(zoneId).toInstant());

        String key = CODE_SEQUENCE + type + today;


        final String serialStr = stringRedisTemplate.opsForValue().get(key);
        Integer serial;
        if (serialStr == null) {
            serial = 1;
            stringRedisTemplate.opsForValue().set(key,"1");
        } else {
            serial = stringRedisTemplate.opsForValue().increment(key, 1).intValue();
            if (Objects.equals(serial, max)) {
                serial = 1;
            }
        }


        //只有日期格式的流水号才设置失效时间
        if (dataFormat.toLowerCase().equals("yyyymmdd") || dataFormat.toLowerCase().equals("yymmdd")) {
            stringRedisTemplate.expireAt(key, expireTime);
        }
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        nf.setMaximumIntegerDigits(len);
        nf.setMinimumIntegerDigits(len);
        if (String.valueOf(serial).length() < len) {
            return today + nf.format(serial);
        }
        return today + String.valueOf(serial);
    }

    /**
     * 获取用户有权限的ou.
     *
     * @param mip 用户mip
     * @return 用户有权限的ou
     */
    public static CurrentContext getContextByMip(String mip) {
        if (com.midea.pam.common.util.StringUtils.isEmpty(mip)) {
            mip = SystemContext.getUid();
        }
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.SYSTEM_CONTEXT + mip));
        if (!StringUtils.isEmpty(json)) {
            return JSONObject.parseObject(json, CurrentContext.class);
        }
        return null;
    }

    /**
     * 获取当前部门的顶级部门
     *
     * @param unitId
     * @return
     */
    public static Long getTopUnitIdByUnitId(Long unitId) {
        Unit unit = findUnitById(unitId);
        if (unit != null) {
            if (unit.getParentFlag() == 0) {
                return unitId;
            } else {
                return getTopUnitIdByUnitId(unit.getParentId());
            }
        }
        return null;
    }

    /**
     * 根据ouId获取顶级部门
     *
     * @param ouId
     * @return
     */
    public static Long getTopUnitIdByOuId(Long ouId) {
        if (ouId == null) {
            return null;
        }
        UnitOuRel unitOuRel = findUnitOuRelByOuId(ouId);
        if (unitOuRel != null && unitOuRel.getUnitId() != null) {
            return getTopUnitIdByUnitId(unitOuRel.getUnitId());
        }
        return null;
    }

    private static String buildKey(final String key) {
        return CacheKey.getNamespace() + ":" + key;
    }

    /**
     * 多key链接
     * 前缀不需要：结尾会自动添加
     * zhuym13
     *
     * @param prefix 前缀
     * @param keys   多key值连接
     */
    public static String buildKey(final Object prefix, final Object... keys) {
        return CACHE_KEY_JOINER.skipNulls().join(CacheKey.getNamespace(), prefix, keys);
    }

    /**
     * @param prefix 前缀(已添加 "*")
     * @return keys
     */
    public static Set<String> scans(String prefix) {
        return stringRedisTemplate.execute((RedisCallback<Set<String>>) conn -> {
            Set<String> keysTmp = new HashSet<>();
            Cursor<byte[]> cursor = conn.scan(new ScanOptions.ScanOptionsBuilder().count(Integer.MAX_VALUE).match(prefix + "*").build());
            while (cursor.hasNext()) {
                keysTmp.add(new String(cursor.next()));
            }
            return keysTmp;
        });
    }

    /**
     * 根据 keys 获取所有value
     *
     * @param keys keys
     * @return values
     */
    public static List<String> multiGet(Collection<String> keys) {
        return stringRedisTemplate.opsForValue().multiGet(keys);
    }

    /**
     * @param prefix 根据前缀获取所有value
     * @return values
     */
    public static List<String> scanMatchKeysAndMultiGet(String prefix) {
        return multiGet(scans(prefix));
    }

    /**
     * 根据单位ID获取ouId集合
     * @return
     */
    public static List<Long> getOuIdListByUnitId(Long unitId){
        String key =buildKey(Constants.Prefix.UNIT_OU_LIST + unitId);
        String json = stringRedisTemplate.opsForValue().get(key);
        if (!StringUtils.isEmpty(json)) {
            return  JSON.parseObject(json,new TypeReference<ArrayList<Long>>(){});
        }
        return Collections.emptyList();
    }

    /**
     * 根据单位ID获取关联子单位ID集合
     * @param unitId
     * @return
     */
    public static List<Long> getChildUnitIdsByUnitId(Long unitId){
        String json = stringRedisTemplate.opsForValue().get(buildKey(Constants.Prefix.UNIT_CHILD_LIST + unitId));
        if (!StringUtils.isEmpty(json)) {
            return  JSON.parseObject(json,new TypeReference<ArrayList<Long>>(){});
        }
        return Collections.emptyList();
    }
}
