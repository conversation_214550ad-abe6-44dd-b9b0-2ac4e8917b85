package com.midea.pam.common.sdp.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 主题【CUSTOMER_CRM】sdp_crm_transfer_organization
 */
@Getter
@Setter
public class CustomerCrmCustomerOu {

    private String customerOuRowId;
    private String customerId;
    private String ouId;
    private String hedgeFlag;
    private String entityId;
    private String approvalStatusCode;
    private String transferOrganizationCode;
    private String statusCode; //状态：valid-有效 invalid-废弃 frozen-冻结
    private String mdmCode;
    private String created;
    private Date createDate;
    private String updated;
    private Date updateDate;
}
