package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PurchaseOrderExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PurchaseOrderExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNumIsNull() {
            addCriterion("num is null");
            return (Criteria) this;
        }

        public Criteria andNumIsNotNull() {
            addCriterion("num is not null");
            return (Criteria) this;
        }

        public Criteria andNumEqualTo(String value) {
            addCriterion("num =", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotEqualTo(String value) {
            addCriterion("num <>", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThan(String value) {
            addCriterion("num >", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThanOrEqualTo(String value) {
            addCriterion("num >=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThan(String value) {
            addCriterion("num <", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThanOrEqualTo(String value) {
            addCriterion("num <=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLike(String value) {
            addCriterion("num like", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotLike(String value) {
            addCriterion("num not like", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumIn(List<String> values) {
            addCriterion("num in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotIn(List<String> values) {
            addCriterion("num not in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumBetween(String value1, String value2) {
            addCriterion("num between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotBetween(String value1, String value2) {
            addCriterion("num not between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdIsNull() {
            addCriterion("vendor_asl_id is null");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdIsNotNull() {
            addCriterion("vendor_asl_id is not null");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdEqualTo(Long value) {
            addCriterion("vendor_asl_id =", value, "vendorAslId");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdNotEqualTo(Long value) {
            addCriterion("vendor_asl_id <>", value, "vendorAslId");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdGreaterThan(Long value) {
            addCriterion("vendor_asl_id >", value, "vendorAslId");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdGreaterThanOrEqualTo(Long value) {
            addCriterion("vendor_asl_id >=", value, "vendorAslId");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdLessThan(Long value) {
            addCriterion("vendor_asl_id <", value, "vendorAslId");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdLessThanOrEqualTo(Long value) {
            addCriterion("vendor_asl_id <=", value, "vendorAslId");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdIn(List<Long> values) {
            addCriterion("vendor_asl_id in", values, "vendorAslId");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdNotIn(List<Long> values) {
            addCriterion("vendor_asl_id not in", values, "vendorAslId");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdBetween(Long value1, Long value2) {
            addCriterion("vendor_asl_id between", value1, value2, "vendorAslId");
            return (Criteria) this;
        }

        public Criteria andVendorAslIdNotBetween(Long value1, Long value2) {
            addCriterion("vendor_asl_id not between", value1, value2, "vendorAslId");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNull() {
            addCriterion("vendor_name is null");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNotNull() {
            addCriterion("vendor_name is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNameEqualTo(String value) {
            addCriterion("vendor_name =", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotEqualTo(String value) {
            addCriterion("vendor_name <>", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThan(String value) {
            addCriterion("vendor_name >", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_name >=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThan(String value) {
            addCriterion("vendor_name <", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanOrEqualTo(String value) {
            addCriterion("vendor_name <=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLike(String value) {
            addCriterion("vendor_name like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotLike(String value) {
            addCriterion("vendor_name not like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameIn(List<String> values) {
            addCriterion("vendor_name in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotIn(List<String> values) {
            addCriterion("vendor_name not in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameBetween(String value1, String value2) {
            addCriterion("vendor_name between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotBetween(String value1, String value2) {
            addCriterion("vendor_name not between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIsNull() {
            addCriterion("vendor_site_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIsNotNull() {
            addCriterion("vendor_site_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeEqualTo(String value) {
            addCriterion("vendor_site_code =", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotEqualTo(String value) {
            addCriterion("vendor_site_code <>", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeGreaterThan(String value) {
            addCriterion("vendor_site_code >", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_site_code >=", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLessThan(String value) {
            addCriterion("vendor_site_code <", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_site_code <=", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLike(String value) {
            addCriterion("vendor_site_code like", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotLike(String value) {
            addCriterion("vendor_site_code not like", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIn(List<String> values) {
            addCriterion("vendor_site_code in", values, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotIn(List<String> values) {
            addCriterion("vendor_site_code not in", values, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeBetween(String value1, String value2) {
            addCriterion("vendor_site_code between", value1, value2, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_site_code not between", value1, value2, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andBuyerIdIsNull() {
            addCriterion("buyer_id is null");
            return (Criteria) this;
        }

        public Criteria andBuyerIdIsNotNull() {
            addCriterion("buyer_id is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerIdEqualTo(Long value) {
            addCriterion("buyer_id =", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdNotEqualTo(Long value) {
            addCriterion("buyer_id <>", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdGreaterThan(Long value) {
            addCriterion("buyer_id >", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("buyer_id >=", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdLessThan(Long value) {
            addCriterion("buyer_id <", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdLessThanOrEqualTo(Long value) {
            addCriterion("buyer_id <=", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdIn(List<Long> values) {
            addCriterion("buyer_id in", values, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdNotIn(List<Long> values) {
            addCriterion("buyer_id not in", values, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdBetween(Long value1, Long value2) {
            addCriterion("buyer_id between", value1, value2, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdNotBetween(Long value1, Long value2) {
            addCriterion("buyer_id not between", value1, value2, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIsNull() {
            addCriterion("buyer_name is null");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIsNotNull() {
            addCriterion("buyer_name is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerNameEqualTo(String value) {
            addCriterion("buyer_name =", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotEqualTo(String value) {
            addCriterion("buyer_name <>", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThan(String value) {
            addCriterion("buyer_name >", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_name >=", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThan(String value) {
            addCriterion("buyer_name <", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThanOrEqualTo(String value) {
            addCriterion("buyer_name <=", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLike(String value) {
            addCriterion("buyer_name like", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotLike(String value) {
            addCriterion("buyer_name not like", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIn(List<String> values) {
            addCriterion("buyer_name in", values, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotIn(List<String> values) {
            addCriterion("buyer_name not in", values, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameBetween(String value1, String value2) {
            addCriterion("buyer_name between", value1, value2, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotBetween(String value1, String value2) {
            addCriterion("buyer_name not between", value1, value2, "buyerName");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Integer value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Integer value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Integer value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Integer value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Integer> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Integer> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIsNull() {
            addCriterion("sync_status is null");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIsNotNull() {
            addCriterion("sync_status is not null");
            return (Criteria) this;
        }

        public Criteria andSyncStatusEqualTo(Integer value) {
            addCriterion("sync_status =", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotEqualTo(Integer value) {
            addCriterion("sync_status <>", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusGreaterThan(Integer value) {
            addCriterion("sync_status >", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("sync_status >=", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusLessThan(Integer value) {
            addCriterion("sync_status <", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusLessThanOrEqualTo(Integer value) {
            addCriterion("sync_status <=", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIn(List<Integer> values) {
            addCriterion("sync_status in", values, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotIn(List<Integer> values) {
            addCriterion("sync_status not in", values, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusBetween(Integer value1, Integer value2) {
            addCriterion("sync_status between", value1, value2, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("sync_status not between", value1, value2, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andErpMessageIsNull() {
            addCriterion("erp_message is null");
            return (Criteria) this;
        }

        public Criteria andErpMessageIsNotNull() {
            addCriterion("erp_message is not null");
            return (Criteria) this;
        }

        public Criteria andErpMessageEqualTo(String value) {
            addCriterion("erp_message =", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageNotEqualTo(String value) {
            addCriterion("erp_message <>", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageGreaterThan(String value) {
            addCriterion("erp_message >", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageGreaterThanOrEqualTo(String value) {
            addCriterion("erp_message >=", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageLessThan(String value) {
            addCriterion("erp_message <", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageLessThanOrEqualTo(String value) {
            addCriterion("erp_message <=", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageLike(String value) {
            addCriterion("erp_message like", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageNotLike(String value) {
            addCriterion("erp_message not like", value, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageIn(List<String> values) {
            addCriterion("erp_message in", values, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageNotIn(List<String> values) {
            addCriterion("erp_message not in", values, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageBetween(String value1, String value2) {
            addCriterion("erp_message between", value1, value2, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andErpMessageNotBetween(String value1, String value2) {
            addCriterion("erp_message not between", value1, value2, "erpMessage");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIsNull() {
            addCriterion("conversion_type is null");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIsNotNull() {
            addCriterion("conversion_type is not null");
            return (Criteria) this;
        }

        public Criteria andConversionTypeEqualTo(String value) {
            addCriterion("conversion_type =", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotEqualTo(String value) {
            addCriterion("conversion_type <>", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeGreaterThan(String value) {
            addCriterion("conversion_type >", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("conversion_type >=", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLessThan(String value) {
            addCriterion("conversion_type <", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLessThanOrEqualTo(String value) {
            addCriterion("conversion_type <=", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLike(String value) {
            addCriterion("conversion_type like", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotLike(String value) {
            addCriterion("conversion_type not like", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIn(List<String> values) {
            addCriterion("conversion_type in", values, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotIn(List<String> values) {
            addCriterion("conversion_type not in", values, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeBetween(String value1, String value2) {
            addCriterion("conversion_type between", value1, value2, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotBetween(String value1, String value2) {
            addCriterion("conversion_type not between", value1, value2, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionDateIsNull() {
            addCriterion("conversion_date is null");
            return (Criteria) this;
        }

        public Criteria andConversionDateIsNotNull() {
            addCriterion("conversion_date is not null");
            return (Criteria) this;
        }

        public Criteria andConversionDateEqualTo(Date value) {
            addCriterion("conversion_date =", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotEqualTo(Date value) {
            addCriterion("conversion_date <>", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateGreaterThan(Date value) {
            addCriterion("conversion_date >", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateGreaterThanOrEqualTo(Date value) {
            addCriterion("conversion_date >=", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateLessThan(Date value) {
            addCriterion("conversion_date <", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateLessThanOrEqualTo(Date value) {
            addCriterion("conversion_date <=", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateIn(List<Date> values) {
            addCriterion("conversion_date in", values, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotIn(List<Date> values) {
            addCriterion("conversion_date not in", values, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateBetween(Date value1, Date value2) {
            addCriterion("conversion_date between", value1, value2, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotBetween(Date value1, Date value2) {
            addCriterion("conversion_date not between", value1, value2, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNull() {
            addCriterion("conversion_rate is null");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNotNull() {
            addCriterion("conversion_rate is not null");
            return (Criteria) this;
        }

        public Criteria andConversionRateEqualTo(BigDecimal value) {
            addCriterion("conversion_rate =", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <>", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThan(BigDecimal value) {
            addCriterion("conversion_rate >", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate >=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThan(BigDecimal value) {
            addCriterion("conversion_rate <", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateIn(List<BigDecimal> values) {
            addCriterion("conversion_rate in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotIn(List<BigDecimal> values) {
            addCriterion("conversion_rate not in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate not between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("source like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("source not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusIsNull() {
            addCriterion("erp_order_status is null");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusIsNotNull() {
            addCriterion("erp_order_status is not null");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusEqualTo(Integer value) {
            addCriterion("erp_order_status =", value, "erpOrderStatus");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusNotEqualTo(Integer value) {
            addCriterion("erp_order_status <>", value, "erpOrderStatus");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusGreaterThan(Integer value) {
            addCriterion("erp_order_status >", value, "erpOrderStatus");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("erp_order_status >=", value, "erpOrderStatus");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusLessThan(Integer value) {
            addCriterion("erp_order_status <", value, "erpOrderStatus");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("erp_order_status <=", value, "erpOrderStatus");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusIn(List<Integer> values) {
            addCriterion("erp_order_status in", values, "erpOrderStatus");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusNotIn(List<Integer> values) {
            addCriterion("erp_order_status not in", values, "erpOrderStatus");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("erp_order_status between", value1, value2, "erpOrderStatus");
            return (Criteria) this;
        }

        public Criteria andErpOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("erp_order_status not between", value1, value2, "erpOrderStatus");
            return (Criteria) this;
        }

        public Criteria andDispatchIsIsNull() {
            addCriterion("dispatch_is is null");
            return (Criteria) this;
        }

        public Criteria andDispatchIsIsNotNull() {
            addCriterion("dispatch_is is not null");
            return (Criteria) this;
        }

        public Criteria andDispatchIsEqualTo(Boolean value) {
            addCriterion("dispatch_is =", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsNotEqualTo(Boolean value) {
            addCriterion("dispatch_is <>", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsGreaterThan(Boolean value) {
            addCriterion("dispatch_is >", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("dispatch_is >=", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsLessThan(Boolean value) {
            addCriterion("dispatch_is <", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsLessThanOrEqualTo(Boolean value) {
            addCriterion("dispatch_is <=", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsIn(List<Boolean> values) {
            addCriterion("dispatch_is in", values, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsNotIn(List<Boolean> values) {
            addCriterion("dispatch_is not in", values, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsBetween(Boolean value1, Boolean value2) {
            addCriterion("dispatch_is between", value1, value2, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("dispatch_is not between", value1, value2, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdIsNull() {
            addCriterion("receipts_id is null");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdIsNotNull() {
            addCriterion("receipts_id is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdEqualTo(Long value) {
            addCriterion("receipts_id =", value, "receiptsId");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdNotEqualTo(Long value) {
            addCriterion("receipts_id <>", value, "receiptsId");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdGreaterThan(Long value) {
            addCriterion("receipts_id >", value, "receiptsId");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdGreaterThanOrEqualTo(Long value) {
            addCriterion("receipts_id >=", value, "receiptsId");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdLessThan(Long value) {
            addCriterion("receipts_id <", value, "receiptsId");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdLessThanOrEqualTo(Long value) {
            addCriterion("receipts_id <=", value, "receiptsId");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdIn(List<Long> values) {
            addCriterion("receipts_id in", values, "receiptsId");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdNotIn(List<Long> values) {
            addCriterion("receipts_id not in", values, "receiptsId");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdBetween(Long value1, Long value2) {
            addCriterion("receipts_id between", value1, value2, "receiptsId");
            return (Criteria) this;
        }

        public Criteria andReceiptsIdNotBetween(Long value1, Long value2) {
            addCriterion("receipts_id not between", value1, value2, "receiptsId");
            return (Criteria) this;
        }

        public Criteria andPromisedDateIsNull() {
            addCriterion("promised_date is null");
            return (Criteria) this;
        }

        public Criteria andPromisedDateIsNotNull() {
            addCriterion("promised_date is not null");
            return (Criteria) this;
        }

        public Criteria andPromisedDateEqualTo(Date value) {
            addCriterion("promised_date =", value, "promisedDate");
            return (Criteria) this;
        }

        public Criteria andPromisedDateNotEqualTo(Date value) {
            addCriterion("promised_date <>", value, "promisedDate");
            return (Criteria) this;
        }

        public Criteria andPromisedDateGreaterThan(Date value) {
            addCriterion("promised_date >", value, "promisedDate");
            return (Criteria) this;
        }

        public Criteria andPromisedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("promised_date >=", value, "promisedDate");
            return (Criteria) this;
        }

        public Criteria andPromisedDateLessThan(Date value) {
            addCriterion("promised_date <", value, "promisedDate");
            return (Criteria) this;
        }

        public Criteria andPromisedDateLessThanOrEqualTo(Date value) {
            addCriterion("promised_date <=", value, "promisedDate");
            return (Criteria) this;
        }

        public Criteria andPromisedDateIn(List<Date> values) {
            addCriterion("promised_date in", values, "promisedDate");
            return (Criteria) this;
        }

        public Criteria andPromisedDateNotIn(List<Date> values) {
            addCriterion("promised_date not in", values, "promisedDate");
            return (Criteria) this;
        }

        public Criteria andPromisedDateBetween(Date value1, Date value2) {
            addCriterion("promised_date between", value1, value2, "promisedDate");
            return (Criteria) this;
        }

        public Criteria andPromisedDateNotBetween(Date value1, Date value2) {
            addCriterion("promised_date not between", value1, value2, "promisedDate");
            return (Criteria) this;
        }

        public Criteria andTrackingDateIsNull() {
            addCriterion("tracking_date is null");
            return (Criteria) this;
        }

        public Criteria andTrackingDateIsNotNull() {
            addCriterion("tracking_date is not null");
            return (Criteria) this;
        }

        public Criteria andTrackingDateEqualTo(Date value) {
            addCriterion("tracking_date =", value, "trackingDate");
            return (Criteria) this;
        }

        public Criteria andTrackingDateNotEqualTo(Date value) {
            addCriterion("tracking_date <>", value, "trackingDate");
            return (Criteria) this;
        }

        public Criteria andTrackingDateGreaterThan(Date value) {
            addCriterion("tracking_date >", value, "trackingDate");
            return (Criteria) this;
        }

        public Criteria andTrackingDateGreaterThanOrEqualTo(Date value) {
            addCriterion("tracking_date >=", value, "trackingDate");
            return (Criteria) this;
        }

        public Criteria andTrackingDateLessThan(Date value) {
            addCriterion("tracking_date <", value, "trackingDate");
            return (Criteria) this;
        }

        public Criteria andTrackingDateLessThanOrEqualTo(Date value) {
            addCriterion("tracking_date <=", value, "trackingDate");
            return (Criteria) this;
        }

        public Criteria andTrackingDateIn(List<Date> values) {
            addCriterion("tracking_date in", values, "trackingDate");
            return (Criteria) this;
        }

        public Criteria andTrackingDateNotIn(List<Date> values) {
            addCriterion("tracking_date not in", values, "trackingDate");
            return (Criteria) this;
        }

        public Criteria andTrackingDateBetween(Date value1, Date value2) {
            addCriterion("tracking_date between", value1, value2, "trackingDate");
            return (Criteria) this;
        }

        public Criteria andTrackingDateNotBetween(Date value1, Date value2) {
            addCriterion("tracking_date not between", value1, value2, "trackingDate");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("org_id is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(Long value) {
            addCriterion("org_id =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(Long value) {
            addCriterion("org_id <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(Long value) {
            addCriterion("org_id >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("org_id >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(Long value) {
            addCriterion("org_id <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("org_id <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<Long> values) {
            addCriterion("org_id in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<Long> values) {
            addCriterion("org_id not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(Long value1, Long value2) {
            addCriterion("org_id between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("org_id not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNull() {
            addCriterion("org_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNotNull() {
            addCriterion("org_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNameEqualTo(String value) {
            addCriterion("org_name =", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotEqualTo(String value) {
            addCriterion("org_name <>", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThan(String value) {
            addCriterion("org_name >", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_name >=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThan(String value) {
            addCriterion("org_name <", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThanOrEqualTo(String value) {
            addCriterion("org_name <=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLike(String value) {
            addCriterion("org_name like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotLike(String value) {
            addCriterion("org_name not like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameIn(List<String> values) {
            addCriterion("org_name in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotIn(List<String> values) {
            addCriterion("org_name not in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameBetween(String value1, String value2) {
            addCriterion("org_name between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotBetween(String value1, String value2) {
            addCriterion("org_name not between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andVendorNumIsNull() {
            addCriterion("vendor_num is null");
            return (Criteria) this;
        }

        public Criteria andVendorNumIsNotNull() {
            addCriterion("vendor_num is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNumEqualTo(String value) {
            addCriterion("vendor_num =", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumNotEqualTo(String value) {
            addCriterion("vendor_num <>", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumGreaterThan(String value) {
            addCriterion("vendor_num >", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_num >=", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumLessThan(String value) {
            addCriterion("vendor_num <", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumLessThanOrEqualTo(String value) {
            addCriterion("vendor_num <=", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumLike(String value) {
            addCriterion("vendor_num like", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumNotLike(String value) {
            addCriterion("vendor_num not like", value, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumIn(List<String> values) {
            addCriterion("vendor_num in", values, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumNotIn(List<String> values) {
            addCriterion("vendor_num not in", values, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumBetween(String value1, String value2) {
            addCriterion("vendor_num between", value1, value2, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andVendorNumNotBetween(String value1, String value2) {
            addCriterion("vendor_num not between", value1, value2, "vendorNum");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdIsNull() {
            addCriterion("payment_method_id is null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdIsNotNull() {
            addCriterion("payment_method_id is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdEqualTo(Long value) {
            addCriterion("payment_method_id =", value, "paymentMethodId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdNotEqualTo(Long value) {
            addCriterion("payment_method_id <>", value, "paymentMethodId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdGreaterThan(Long value) {
            addCriterion("payment_method_id >", value, "paymentMethodId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdGreaterThanOrEqualTo(Long value) {
            addCriterion("payment_method_id >=", value, "paymentMethodId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdLessThan(Long value) {
            addCriterion("payment_method_id <", value, "paymentMethodId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdLessThanOrEqualTo(Long value) {
            addCriterion("payment_method_id <=", value, "paymentMethodId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdIn(List<Long> values) {
            addCriterion("payment_method_id in", values, "paymentMethodId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdNotIn(List<Long> values) {
            addCriterion("payment_method_id not in", values, "paymentMethodId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdBetween(Long value1, Long value2) {
            addCriterion("payment_method_id between", value1, value2, "paymentMethodId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIdNotBetween(Long value1, Long value2) {
            addCriterion("payment_method_id not between", value1, value2, "paymentMethodId");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameIsNull() {
            addCriterion("payment_method_name is null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameIsNotNull() {
            addCriterion("payment_method_name is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameEqualTo(String value) {
            addCriterion("payment_method_name =", value, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameNotEqualTo(String value) {
            addCriterion("payment_method_name <>", value, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameGreaterThan(String value) {
            addCriterion("payment_method_name >", value, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameGreaterThanOrEqualTo(String value) {
            addCriterion("payment_method_name >=", value, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameLessThan(String value) {
            addCriterion("payment_method_name <", value, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameLessThanOrEqualTo(String value) {
            addCriterion("payment_method_name <=", value, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameLike(String value) {
            addCriterion("payment_method_name like", value, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameNotLike(String value) {
            addCriterion("payment_method_name not like", value, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameIn(List<String> values) {
            addCriterion("payment_method_name in", values, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameNotIn(List<String> values) {
            addCriterion("payment_method_name not in", values, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameBetween(String value1, String value2) {
            addCriterion("payment_method_name between", value1, value2, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNameNotBetween(String value1, String value2) {
            addCriterion("payment_method_name not between", value1, value2, "paymentMethodName");
            return (Criteria) this;
        }

        public Criteria andPaymentWayIsNull() {
            addCriterion("payment_way is null");
            return (Criteria) this;
        }

        public Criteria andPaymentWayIsNotNull() {
            addCriterion("payment_way is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentWayEqualTo(String value) {
            addCriterion("payment_way =", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayNotEqualTo(String value) {
            addCriterion("payment_way <>", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayGreaterThan(String value) {
            addCriterion("payment_way >", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayGreaterThanOrEqualTo(String value) {
            addCriterion("payment_way >=", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayLessThan(String value) {
            addCriterion("payment_way <", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayLessThanOrEqualTo(String value) {
            addCriterion("payment_way <=", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayLike(String value) {
            addCriterion("payment_way like", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayNotLike(String value) {
            addCriterion("payment_way not like", value, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayIn(List<String> values) {
            addCriterion("payment_way in", values, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayNotIn(List<String> values) {
            addCriterion("payment_way not in", values, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayBetween(String value1, String value2) {
            addCriterion("payment_way between", value1, value2, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andPaymentWayNotBetween(String value1, String value2) {
            addCriterion("payment_way not between", value1, value2, "paymentWay");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIsNull() {
            addCriterion("delivery_type is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIsNotNull() {
            addCriterion("delivery_type is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeEqualTo(String value) {
            addCriterion("delivery_type =", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotEqualTo(String value) {
            addCriterion("delivery_type <>", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeGreaterThan(String value) {
            addCriterion("delivery_type >", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_type >=", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLessThan(String value) {
            addCriterion("delivery_type <", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLessThanOrEqualTo(String value) {
            addCriterion("delivery_type <=", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLike(String value) {
            addCriterion("delivery_type like", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotLike(String value) {
            addCriterion("delivery_type not like", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIn(List<String> values) {
            addCriterion("delivery_type in", values, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotIn(List<String> values) {
            addCriterion("delivery_type not in", values, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeBetween(String value1, String value2) {
            addCriterion("delivery_type between", value1, value2, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotBetween(String value1, String value2) {
            addCriterion("delivery_type not between", value1, value2, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseIsNull() {
            addCriterion("delivery_clause is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseIsNotNull() {
            addCriterion("delivery_clause is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseEqualTo(String value) {
            addCriterion("delivery_clause =", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseNotEqualTo(String value) {
            addCriterion("delivery_clause <>", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseGreaterThan(String value) {
            addCriterion("delivery_clause >", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_clause >=", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseLessThan(String value) {
            addCriterion("delivery_clause <", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseLessThanOrEqualTo(String value) {
            addCriterion("delivery_clause <=", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseLike(String value) {
            addCriterion("delivery_clause like", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseNotLike(String value) {
            addCriterion("delivery_clause not like", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseIn(List<String> values) {
            addCriterion("delivery_clause in", values, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseNotIn(List<String> values) {
            addCriterion("delivery_clause not in", values, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseBetween(String value1, String value2) {
            addCriterion("delivery_clause between", value1, value2, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseNotBetween(String value1, String value2) {
            addCriterion("delivery_clause not between", value1, value2, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andTaxIdIsNull() {
            addCriterion("tax_id is null");
            return (Criteria) this;
        }

        public Criteria andTaxIdIsNotNull() {
            addCriterion("tax_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaxIdEqualTo(Long value) {
            addCriterion("tax_id =", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdNotEqualTo(Long value) {
            addCriterion("tax_id <>", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdGreaterThan(Long value) {
            addCriterion("tax_id >", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tax_id >=", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdLessThan(Long value) {
            addCriterion("tax_id <", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdLessThanOrEqualTo(Long value) {
            addCriterion("tax_id <=", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdIn(List<Long> values) {
            addCriterion("tax_id in", values, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdNotIn(List<Long> values) {
            addCriterion("tax_id not in", values, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdBetween(Long value1, Long value2) {
            addCriterion("tax_id between", value1, value2, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdNotBetween(Long value1, Long value2) {
            addCriterion("tax_id not between", value1, value2, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNull() {
            addCriterion("tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNotNull() {
            addCriterion("tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualTo(String value) {
            addCriterion("tax_rate =", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualTo(String value) {
            addCriterion("tax_rate <>", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThan(String value) {
            addCriterion("tax_rate >", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualTo(String value) {
            addCriterion("tax_rate >=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThan(String value) {
            addCriterion("tax_rate <", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualTo(String value) {
            addCriterion("tax_rate <=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLike(String value) {
            addCriterion("tax_rate like", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotLike(String value) {
            addCriterion("tax_rate not like", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateIn(List<String> values) {
            addCriterion("tax_rate in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotIn(List<String> values) {
            addCriterion("tax_rate not in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateBetween(String value1, String value2) {
            addCriterion("tax_rate between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotBetween(String value1, String value2) {
            addCriterion("tax_rate not between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andPricingTypeIsNull() {
            addCriterion("pricing_type is null");
            return (Criteria) this;
        }

        public Criteria andPricingTypeIsNotNull() {
            addCriterion("pricing_type is not null");
            return (Criteria) this;
        }

        public Criteria andPricingTypeEqualTo(Integer value) {
            addCriterion("pricing_type =", value, "pricingType");
            return (Criteria) this;
        }

        public Criteria andPricingTypeNotEqualTo(Integer value) {
            addCriterion("pricing_type <>", value, "pricingType");
            return (Criteria) this;
        }

        public Criteria andPricingTypeGreaterThan(Integer value) {
            addCriterion("pricing_type >", value, "pricingType");
            return (Criteria) this;
        }

        public Criteria andPricingTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("pricing_type >=", value, "pricingType");
            return (Criteria) this;
        }

        public Criteria andPricingTypeLessThan(Integer value) {
            addCriterion("pricing_type <", value, "pricingType");
            return (Criteria) this;
        }

        public Criteria andPricingTypeLessThanOrEqualTo(Integer value) {
            addCriterion("pricing_type <=", value, "pricingType");
            return (Criteria) this;
        }

        public Criteria andPricingTypeIn(List<Integer> values) {
            addCriterion("pricing_type in", values, "pricingType");
            return (Criteria) this;
        }

        public Criteria andPricingTypeNotIn(List<Integer> values) {
            addCriterion("pricing_type not in", values, "pricingType");
            return (Criteria) this;
        }

        public Criteria andPricingTypeBetween(Integer value1, Integer value2) {
            addCriterion("pricing_type between", value1, value2, "pricingType");
            return (Criteria) this;
        }

        public Criteria andPricingTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("pricing_type not between", value1, value2, "pricingType");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andExchangeRateIsNull() {
            addCriterion("exchange_rate is null");
            return (Criteria) this;
        }

        public Criteria andExchangeRateIsNotNull() {
            addCriterion("exchange_rate is not null");
            return (Criteria) this;
        }

        public Criteria andExchangeRateEqualTo(BigDecimal value) {
            addCriterion("exchange_rate =", value, "exchangeRate");
            return (Criteria) this;
        }

        public Criteria andExchangeRateNotEqualTo(BigDecimal value) {
            addCriterion("exchange_rate <>", value, "exchangeRate");
            return (Criteria) this;
        }

        public Criteria andExchangeRateGreaterThan(BigDecimal value) {
            addCriterion("exchange_rate >", value, "exchangeRate");
            return (Criteria) this;
        }

        public Criteria andExchangeRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("exchange_rate >=", value, "exchangeRate");
            return (Criteria) this;
        }

        public Criteria andExchangeRateLessThan(BigDecimal value) {
            addCriterion("exchange_rate <", value, "exchangeRate");
            return (Criteria) this;
        }

        public Criteria andExchangeRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("exchange_rate <=", value, "exchangeRate");
            return (Criteria) this;
        }

        public Criteria andExchangeRateIn(List<BigDecimal> values) {
            addCriterion("exchange_rate in", values, "exchangeRate");
            return (Criteria) this;
        }

        public Criteria andExchangeRateNotIn(List<BigDecimal> values) {
            addCriterion("exchange_rate not in", values, "exchangeRate");
            return (Criteria) this;
        }

        public Criteria andExchangeRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("exchange_rate between", value1, value2, "exchangeRate");
            return (Criteria) this;
        }

        public Criteria andExchangeRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("exchange_rate not between", value1, value2, "exchangeRate");
            return (Criteria) this;
        }

        public Criteria andApproveInfoIsNull() {
            addCriterion("approve_info is null");
            return (Criteria) this;
        }

        public Criteria andApproveInfoIsNotNull() {
            addCriterion("approve_info is not null");
            return (Criteria) this;
        }

        public Criteria andApproveInfoEqualTo(String value) {
            addCriterion("approve_info =", value, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoNotEqualTo(String value) {
            addCriterion("approve_info <>", value, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoGreaterThan(String value) {
            addCriterion("approve_info >", value, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoGreaterThanOrEqualTo(String value) {
            addCriterion("approve_info >=", value, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoLessThan(String value) {
            addCriterion("approve_info <", value, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoLessThanOrEqualTo(String value) {
            addCriterion("approve_info <=", value, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoLike(String value) {
            addCriterion("approve_info like", value, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoNotLike(String value) {
            addCriterion("approve_info not like", value, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoIn(List<String> values) {
            addCriterion("approve_info in", values, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoNotIn(List<String> values) {
            addCriterion("approve_info not in", values, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoBetween(String value1, String value2) {
            addCriterion("approve_info between", value1, value2, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andApproveInfoNotBetween(String value1, String value2) {
            addCriterion("approve_info not between", value1, value2, "approveInfo");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdIsNull() {
            addCriterion("erp_buyer_id is null");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdIsNotNull() {
            addCriterion("erp_buyer_id is not null");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdEqualTo(Long value) {
            addCriterion("erp_buyer_id =", value, "erpBuyerId");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdNotEqualTo(Long value) {
            addCriterion("erp_buyer_id <>", value, "erpBuyerId");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdGreaterThan(Long value) {
            addCriterion("erp_buyer_id >", value, "erpBuyerId");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("erp_buyer_id >=", value, "erpBuyerId");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdLessThan(Long value) {
            addCriterion("erp_buyer_id <", value, "erpBuyerId");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdLessThanOrEqualTo(Long value) {
            addCriterion("erp_buyer_id <=", value, "erpBuyerId");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdIn(List<Long> values) {
            addCriterion("erp_buyer_id in", values, "erpBuyerId");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdNotIn(List<Long> values) {
            addCriterion("erp_buyer_id not in", values, "erpBuyerId");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdBetween(Long value1, Long value2) {
            addCriterion("erp_buyer_id between", value1, value2, "erpBuyerId");
            return (Criteria) this;
        }

        public Criteria andErpBuyerIdNotBetween(Long value1, Long value2) {
            addCriterion("erp_buyer_id not between", value1, value2, "erpBuyerId");
            return (Criteria) this;
        }

        public Criteria andInitFlagIsNull() {
            addCriterion("init_flag is null");
            return (Criteria) this;
        }

        public Criteria andInitFlagIsNotNull() {
            addCriterion("init_flag is not null");
            return (Criteria) this;
        }

        public Criteria andInitFlagEqualTo(Integer value) {
            addCriterion("init_flag =", value, "initFlag");
            return (Criteria) this;
        }

        public Criteria andInitFlagNotEqualTo(Integer value) {
            addCriterion("init_flag <>", value, "initFlag");
            return (Criteria) this;
        }

        public Criteria andInitFlagGreaterThan(Integer value) {
            addCriterion("init_flag >", value, "initFlag");
            return (Criteria) this;
        }

        public Criteria andInitFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("init_flag >=", value, "initFlag");
            return (Criteria) this;
        }

        public Criteria andInitFlagLessThan(Integer value) {
            addCriterion("init_flag <", value, "initFlag");
            return (Criteria) this;
        }

        public Criteria andInitFlagLessThanOrEqualTo(Integer value) {
            addCriterion("init_flag <=", value, "initFlag");
            return (Criteria) this;
        }

        public Criteria andInitFlagIn(List<Integer> values) {
            addCriterion("init_flag in", values, "initFlag");
            return (Criteria) this;
        }

        public Criteria andInitFlagNotIn(List<Integer> values) {
            addCriterion("init_flag not in", values, "initFlag");
            return (Criteria) this;
        }

        public Criteria andInitFlagBetween(Integer value1, Integer value2) {
            addCriterion("init_flag between", value1, value2, "initFlag");
            return (Criteria) this;
        }

        public Criteria andInitFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("init_flag not between", value1, value2, "initFlag");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryIsNull() {
            addCriterion("secondary_inventory is null");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryIsNotNull() {
            addCriterion("secondary_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryEqualTo(String value) {
            addCriterion("secondary_inventory =", value, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNotEqualTo(String value) {
            addCriterion("secondary_inventory <>", value, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryGreaterThan(String value) {
            addCriterion("secondary_inventory >", value, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryGreaterThanOrEqualTo(String value) {
            addCriterion("secondary_inventory >=", value, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryLessThan(String value) {
            addCriterion("secondary_inventory <", value, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryLessThanOrEqualTo(String value) {
            addCriterion("secondary_inventory <=", value, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryLike(String value) {
            addCriterion("secondary_inventory like", value, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNotLike(String value) {
            addCriterion("secondary_inventory not like", value, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryIn(List<String> values) {
            addCriterion("secondary_inventory in", values, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNotIn(List<String> values) {
            addCriterion("secondary_inventory not in", values, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryBetween(String value1, String value2) {
            addCriterion("secondary_inventory between", value1, value2, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNotBetween(String value1, String value2) {
            addCriterion("secondary_inventory not between", value1, value2, "secondaryInventory");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameIsNull() {
            addCriterion("secondary_inventory_name is null");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameIsNotNull() {
            addCriterion("secondary_inventory_name is not null");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameEqualTo(String value) {
            addCriterion("secondary_inventory_name =", value, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameNotEqualTo(String value) {
            addCriterion("secondary_inventory_name <>", value, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameGreaterThan(String value) {
            addCriterion("secondary_inventory_name >", value, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("secondary_inventory_name >=", value, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameLessThan(String value) {
            addCriterion("secondary_inventory_name <", value, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameLessThanOrEqualTo(String value) {
            addCriterion("secondary_inventory_name <=", value, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameLike(String value) {
            addCriterion("secondary_inventory_name like", value, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameNotLike(String value) {
            addCriterion("secondary_inventory_name not like", value, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameIn(List<String> values) {
            addCriterion("secondary_inventory_name in", values, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameNotIn(List<String> values) {
            addCriterion("secondary_inventory_name not in", values, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameBetween(String value1, String value2) {
            addCriterion("secondary_inventory_name between", value1, value2, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andSecondaryInventoryNameNotBetween(String value1, String value2) {
            addCriterion("secondary_inventory_name not between", value1, value2, "secondaryInventoryName");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeIsNull() {
            addCriterion("approval_time is null");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeIsNotNull() {
            addCriterion("approval_time is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeEqualTo(Date value) {
            addCriterion("approval_time =", value, "approvalTime");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeNotEqualTo(Date value) {
            addCriterion("approval_time <>", value, "approvalTime");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeGreaterThan(Date value) {
            addCriterion("approval_time >", value, "approvalTime");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("approval_time >=", value, "approvalTime");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeLessThan(Date value) {
            addCriterion("approval_time <", value, "approvalTime");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeLessThanOrEqualTo(Date value) {
            addCriterion("approval_time <=", value, "approvalTime");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeIn(List<Date> values) {
            addCriterion("approval_time in", values, "approvalTime");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeNotIn(List<Date> values) {
            addCriterion("approval_time not in", values, "approvalTime");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeBetween(Date value1, Date value2) {
            addCriterion("approval_time between", value1, value2, "approvalTime");
            return (Criteria) this;
        }

        public Criteria andApprovalTimeNotBetween(Date value1, Date value2) {
            addCriterion("approval_time not between", value1, value2, "approvalTime");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemIsNull() {
            addCriterion("sync_source_system is null");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemIsNotNull() {
            addCriterion("sync_source_system is not null");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemEqualTo(String value) {
            addCriterion("sync_source_system =", value, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemNotEqualTo(String value) {
            addCriterion("sync_source_system <>", value, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemGreaterThan(String value) {
            addCriterion("sync_source_system >", value, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemGreaterThanOrEqualTo(String value) {
            addCriterion("sync_source_system >=", value, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemLessThan(String value) {
            addCriterion("sync_source_system <", value, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemLessThanOrEqualTo(String value) {
            addCriterion("sync_source_system <=", value, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemLike(String value) {
            addCriterion("sync_source_system like", value, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemNotLike(String value) {
            addCriterion("sync_source_system not like", value, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemIn(List<String> values) {
            addCriterion("sync_source_system in", values, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemNotIn(List<String> values) {
            addCriterion("sync_source_system not in", values, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemBetween(String value1, String value2) {
            addCriterion("sync_source_system between", value1, value2, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andSyncSourceSystemNotBetween(String value1, String value2) {
            addCriterion("sync_source_system not between", value1, value2, "syncSourceSystem");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgIsNull() {
            addCriterion("contract_terms_flg is null");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgIsNotNull() {
            addCriterion("contract_terms_flg is not null");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgEqualTo(String value) {
            addCriterion("contract_terms_flg =", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgNotEqualTo(String value) {
            addCriterion("contract_terms_flg <>", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgGreaterThan(String value) {
            addCriterion("contract_terms_flg >", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgGreaterThanOrEqualTo(String value) {
            addCriterion("contract_terms_flg >=", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgLessThan(String value) {
            addCriterion("contract_terms_flg <", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgLessThanOrEqualTo(String value) {
            addCriterion("contract_terms_flg <=", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgLike(String value) {
            addCriterion("contract_terms_flg like", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgNotLike(String value) {
            addCriterion("contract_terms_flg not like", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgIn(List<String> values) {
            addCriterion("contract_terms_flg in", values, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgNotIn(List<String> values) {
            addCriterion("contract_terms_flg not in", values, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgBetween(String value1, String value2) {
            addCriterion("contract_terms_flg between", value1, value2, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgNotBetween(String value1, String value2) {
            addCriterion("contract_terms_flg not between", value1, value2, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andSyncToFapIsNull() {
            addCriterion("sync_to_fap is null");
            return (Criteria) this;
        }

        public Criteria andSyncToFapIsNotNull() {
            addCriterion("sync_to_fap is not null");
            return (Criteria) this;
        }

        public Criteria andSyncToFapEqualTo(Boolean value) {
            addCriterion("sync_to_fap =", value, "syncToFap");
            return (Criteria) this;
        }

        public Criteria andSyncToFapNotEqualTo(Boolean value) {
            addCriterion("sync_to_fap <>", value, "syncToFap");
            return (Criteria) this;
        }

        public Criteria andSyncToFapGreaterThan(Boolean value) {
            addCriterion("sync_to_fap >", value, "syncToFap");
            return (Criteria) this;
        }

        public Criteria andSyncToFapGreaterThanOrEqualTo(Boolean value) {
            addCriterion("sync_to_fap >=", value, "syncToFap");
            return (Criteria) this;
        }

        public Criteria andSyncToFapLessThan(Boolean value) {
            addCriterion("sync_to_fap <", value, "syncToFap");
            return (Criteria) this;
        }

        public Criteria andSyncToFapLessThanOrEqualTo(Boolean value) {
            addCriterion("sync_to_fap <=", value, "syncToFap");
            return (Criteria) this;
        }

        public Criteria andSyncToFapIn(List<Boolean> values) {
            addCriterion("sync_to_fap in", values, "syncToFap");
            return (Criteria) this;
        }

        public Criteria andSyncToFapNotIn(List<Boolean> values) {
            addCriterion("sync_to_fap not in", values, "syncToFap");
            return (Criteria) this;
        }

        public Criteria andSyncToFapBetween(Boolean value1, Boolean value2) {
            addCriterion("sync_to_fap between", value1, value2, "syncToFap");
            return (Criteria) this;
        }

        public Criteria andSyncToFapNotBetween(Boolean value1, Boolean value2) {
            addCriterion("sync_to_fap not between", value1, value2, "syncToFap");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}