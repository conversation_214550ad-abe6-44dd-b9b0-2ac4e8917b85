package com.midea.pam.common.enums;

/**
 * 全局错误码
 */
public enum ErrorCode implements Code {
    SUCCESS(0, "操作成功"),
    ERROR(1, "操作失败"),
    NULL_POINTER(2, "空指导异常"),
    WARNING(999, "异常提示"),
    PARAMS_INVALID(10000001, "参数不正确"),
    ID_NOT_NULL(10000002, "id不能为空"),
    CHANGE_SEQ_ERROR(10000003, "置换顺序异常"),
    DELIVERIES_ID_NOT_NULL(10000004, "交付物id不能为空"),
    IDS_NOT_NULL(10000005, "ids不能为空"),
    SYSTEM_PARAM_DEPT_NOT_NULL(10000006, "部门不能为空"),
    SYSTEM_FILE_ERROR(10000007, "导入文件有误,请使用模板填写数据"),
    TIME_OUT(10000008, "请求超时,请稍后重试"),
    SYSTEM_FILE_EMPTY(10000009, "导入文件有误,模板数据为空"),
    FORMINSTANCEID_EMPTY(10000010, "导入文件有误,模板数据为空"),
    WLBMGZ_ERROR(10000011, "物料编码规则设置有误"),

    SYSTEM_FORMAT_ERROR(10000012, "导入文件数据行格式错误,请检查"),

    SUBMIT_CHECK_WARNING(888, "重复提交，请稍后再试"),

    //basedata
    BASEDATA_LABOR_COST_TYPE_NOT_NULL(10010001, "人力费用类型不能为空"),
    BASEDATA_MATERIAL_COST_NOF_FIND(10010002, "物料成本不存在"),
    BASEDATA_PAM_CODE_NOT_NULL(10010003, "pam编码不能为空"),
    BASEDATA_DICT_TYPE_NOT_NULL(10010004, "数据字典type不能为空"),
    BASEDATA_DICT_CODE_NOT_NULL(10010005, "数据字典code不能为空"),
    BASEDATA_MATERIAL_CODE_NOT_NULL(10010006, "物料编码不能为空"),
    BASEDATA_MATERIAL_NOT_FIND(10010007, "物料不存在"),
    BASEDATA_PROJECT_CODE_NOT_FIND(10010008, "项目编码不能为空"),
    BASEDATA_ORGANIZATION_ID_NOT_NULL(10010009, "库存组织id不能为空"),
    BASEDATA_STORAGE_INVENTORY_TYPE_NOT_NULL(10010010, "子库类型不能为空"),
    BASEDATA_DICT_NAME_NOT_NULL(10010011, "数据字典name不能为空"),
    BASEDATA_MATERIAL_UNIT_NOT_FIND(10010012, "物料单位不存在"),
    BASEDATA_CURRENCY_NOT_FIND(10010013, "币种信息不存在"),
    BASEDATA_STORAGE_INVENTORY_TYPE_EXIST(10010014, "每个库存组织只允许存在一个接收仓子库"),
    BASEDATA_ITEM_CODE_MATERIAL_NOT_FIND(10010015, "erp编码对应的物料不存在"),
    BASEDATA_CURRENCY_NOT_NULL(10010016, "存在币种为空的数据不允许导入，请修改导入模板数据后重新导入"),
    BASEDATA_OVERLAPPING(10010017, "该时间段与已配置时间段有重叠，请调整时间段"),
    BASEDATA_BRAND_HEADER_NOT_NULL(10010018, "品牌新增和变更记录不能为空"),
    BASEDATA_BRAND_DETAIL_NOT_NULL(10030019, "品牌明细不能为空"),
    BASEDATA_STORAGE_INVENTORY_QUERY_NOT_NULL(10010020, "子库查询条件不能为空"),
    BASEDATA_STORAGE_INVENTORY_NOT_MORE_THAN_ONE(10010021, "不能找到多个子库"),
    BASEDATA_LABOR_COST_TYPE_PROJECT_ACTIVITY_NO_VALID(10010022, "不是有效的关联活动事项"),
    // 商机模块
    CRM_USER_ID_NO_NULL(10020001, "userId不能为空"),
    CRM_TEAMID_NOT_NULL(10020002, "teamId不能为空"),
    CRM_ISLEADER_NOT_NULL(10020003, "isLeader不能为空"),
    CRM_BUSINESS_UPDATE_FAIL(10020004, "商机更新失败"),
    CRM_DEPT_ID_REPEAT(10020005, "部门id有重复"),
    CRM_DEPT_TYPE_ERROR(10020006, "请传入正确部门类型的id"),
    CRM_LEAD_NAME_NOT_NULL(10020007, "客户名称不能为空"),
    CRM_LEAD_INDUSTRY_NOT_NULL(10020008, "行业不能为空"),
    CRM_LEAD_DISTRICT_NOT_NULL(10020009, "区域不能为空"),
    CRM_LEAD_AREA_NOT_NULL(10020010, "省市区不能为空"),
    CRM_LEAD_SOURCE_NOT_NULL(10020011, "来源不能为空"),
    CRM_BUSINESS_STATUS_NOT_IN_WIN_CONTRACT(10020012, "商机不在生效状态"),
    CRM_BUSINESS_ID_NOT_NULL(10020013, "商机Id不能为空"),
    CRM_BUSINESS_NOT_FOUND(10020014, "商机不存在"),
    CRM_BUSINESS_ATTACHMENT_ID_NOT_FOUND(10020015, "商机附件id不能为空"),
    CRM_BUSINESS_ID_NOT_FOUND(10020016, "商机id不能为空"),
    CRM_BUSINESS_ATTACHMENT_TYPE_NOT_FOUND(10020017, "商机附件类型不能为空"),
    CRM_BUSINESS_ATTACHMENT_NOT_FOUND(********, "商机附件不能为空"),
    CRM_CUSTOMER_NOT_FOUND(********, "客户不存在"),
    CRM_CUSTOMER_NOT_IN_ENABLE(********, "客户不在生效状态"),
    CRM_CUSTOMER_BUSINESS_STATUS_IN_WIN_CONTRACT(********, "客户存在关联商机在赢单状态"),
    CRM_CUSTOMER_INNER_DATA_NULL(********, "客户是否内部客户数据缺失"),
    CRM_BUSINESS_PROMPT_UPDATE_FAIL(********, "商机流程失败"),
    CRM_CUSTOMER_ID_NOT_NULL(********, "客户id不能为空"),
    CRM_CUSTOMER_BANKACCOUNT_NOT_NULL(********, "请完善客户财务信息"),
    CRM_CUSTOMER_CONTACT_NOT_NULL(********, "请完善客户联系人信息"),
    CRM_CUSTOMER_OUREL_NOT_NULL(********, "请完善客户业务实体信息"),

    //ctc
    CTC_PROJECT_ID_NULL(********, "项目id不能为空"),
    CTC_MILEPOST_ID_NULL(********, "里程碑id不能为空"),
    CTC_BOM_MILEPOST_NULL(********, "BOM交付类型里程碑不存在"),
    CTC_PLAN_NOT_FIND(********, "方案不存在"),
    CTC_CODING_SUBCLASS_NOT_NULL(********, "物料小类不能为空"),
    CTC_CODING_CLASS_NOT_NULL(********, "物料大类不能为空"),
    CTC_PAM_CODE_NOT_NULL(********, "pam编码不能为空"),
    CTC_PAM_CODE_MATERIAL_NOT_NULL(********, "pam编码对应物料不存在"),
    CTC_MILEPOST_DESIGN_PLAN_SUBMIT_ID_NULL(********, "里程碑详细方案提交记录id不能为空"),
    CTC_MILEPOST_DESIGN_PLAN_CONFIRM_ID_NULL(********, "里程碑详细方案确认记录id不能为空"),
    CTC_MILEPOST_DESIGN_PLAN_DELIVERY_TIME_NULL(10030011, "交货时间不能为空"),
    CTC_BUYER_INFO_NOT_NULL(10030012, "采购员信息不能为空"),
    CTC_PURCHASE_MATERIAL_REQUIREMENT_ID_NOT_NULL(10030013, "物料采购需求id不能为空"),
    CTC_PURCHASE_MATERIAL_REQUIREMENT_NOT_FIND(10030014, "物料采购需求不存在"),
    CTC_PROJECT_NOT_FIND(10030015, "项目不存在"),
    CTC_PURCHASE_MATERIAL_REQUIREMENT_PROJECT_ID_NULL(10030016, "物料采购需求项目id缺失"),
    CTC_PURCHASE_MATERIAL_REQUIREMENT_PAM_CODE_NULL(10030017, "物料采购需求pam编码缺失"),
    CTC_ORDER_VENDOR_ID_NOT_NULL(10030018, "采购订单供应商id不能为空"),
    CTC_ORDER_NOT_NULL(10030019, "采购订单不能为空"),
    CTC_ORDER_DETAILS_NOT_NULL(10030020, "采购订单明细不能为空"),
    CTC_REQUIREMENT_UNRELEASED_AMOUNT_OUT(10030021, "当前下达数量已超过物料采购需求未下达量"),
    CTC_ORDER_DETAILS_ORDER_NUM_NULL(10030022, "采购订单明细下达数量为空"),
    CTC_PURCHASE_MATERIAL_REQUIREMENT_UNRELEASED_AMOUNT_NULL(10030023, "物料采购需求未下达量为空"),
    CTC_MATERIAL_UNIT_NOT_NULL(10030024, "物料单位不能为空"),
    CTC_ERP_CODE_RULE_NOT_FIND(10030025, "erp编码规则不存在"),
    CTC_ERP_CODE_GENERATE_ERROR(10030026, "erp编码生成失败"),
    CTC_MATERIAL_CLASS_TYPE_ILLEGAL(10030027, "物料大类和物料小类不合法"),
    CTC_MILEPOST_DESIGN_PLAN_DETAIL_NOT_FIND(10030028, "设计信息不存在"),
    CTC_CARRYOVER_BILL_ID_NOT_NULL(10030029, "结转单id不能为空"),
    CTC_BATCH_NUM_NOT_NULL(********, "执行批次不能为空"),
    CTC_CARRYOVER_USERBY_NOT_NULL(********, "结转人id不能为空"),
    CTC_PROJECT_PROFIT_NOT_FIND(********, "收入成本计划不存在"),
    CTC_GL_PERIOD_ID_NOT_NULL(********, "会计期间id不能为空"),
    CTC_GL_PERIOD_NAME_NOT_NULL(********, "会计期间不能为空"),
    CTC_OU_ID_NOT_NULL(********, "业务实体id不能为空"),
    CTC_GENERAL_LEDGER_PERIOD_NOT_OPEN(********, "当前会计期间未打开，请联系财务人员"),
    CTC_ACCOUNTABLE_PERIOD_NOT_OPEN(********, "当前会计期间未打开，请联系财务人员"),
    CTC_COMPANY_CODE_NULL(********, "公司代码为空"),
    CTC_MILEPOST_CAN_NOT_PASS(********, "该里程碑不允许评审通过"),
    CTC_MILEPOST_IS_NOT_BOM(********, "该里程碑类型为非BOM交付类型"),
    CTC_PROJECT_CAN_NOT_ENTER_MILEPOST_DESIGN(********, "当前项目不允许详细方案发布或变更"),
    CTC_PURCHASE_PROGRESS_TRAN_TYPE_NOT_NULL(********, "订单明细进展处理类型不能为空"),
    CTC_ITEM_CODE_NOT_NULL(********, "物料编码不能为空"),
    CTC_UPLOAD_PATH_ID_NOT_NULL(********, "上传路径id不能为空"),
    CTC_MDP_CHECKING(********, "当前模组处于变更中,不允许发起新的变更!"),
    CTC_ORDER_VENDOR_CODE_NOT_NULL(********, "采购订单供应商编码不能为空"),
    CTC_CHANGE_RECORD_NOT_FIND(********, "变更记录不存在"),
    CTC_MILEPOST_CAN_NOT_OPERATE(********, "当前里程碑暂不允许操作,待上一个里程碑完成交付"),
    CTC_MILEPOST_NOT_FIND(10030049, "请先交付上一个里程碑"),
    CTC_MILEPOST_STATUS_FIND(10030050, "里程碑状态缺失"),
    CTC_ASYNC_REQUEST_RESULT_ID_NOT_NULL(10030051, "异步请求id不能为空"),
    CTC_MILEPOST_TEMPLATE_STAGE_NULL(10030052, "您选择的项目类型对应的里程碑名称为空,请确认后再选择."),
    CTC_PROJECT_TYPE_NOT_NULL(10030053, "项目类型不能为空"),
    CTC_PROJECT_NOT_NULL(10030053, "项目不能为空"),
    CTC_COST_RATIO_CONFIG_SAME_VALID_DATE_EXIST(10030054, "您选择项目类型，分配比例还在有效期内，不可重复选择."),
    CTC_UNIT_ID_NOT_NULL(10030055, "使用单位id不能为空"),
    CTC_STAGET_NOT_NULL(10030056, "阶段不能为空"),
    CTC_COST_RATIO_SUM_NOT_NULL(10030057, "成本比例合计不能为空"),
    CTC_ABLIE_AT_START_NOT_NULL(10030058, "启用日期不能为空"),
    CTC_PROJECT_TYPE_ID_NOT_NULL(10030059, "项目类型ID不能为空"),
    CTC_CHANGE_DETAIL_ID_NULL(10031001, "变更详设ID不能为空"),

    CTC_MATERIEL_DESCR_NOT_NULL(10030059, "品牌或名称或型号/规格/图号至少一处必填"),
    CTC_NUMBER_NOT_NULL(10030060, "数量必填"),
    CTC_PAYMENT_INVOICE_DETAIL_ASSOCIATION_PAYMENT_APPLY(10030061, "该发票已关联付款申请"),
    CTC_INVOICE_DETAIL_CODE_NOT_NULL(10030062, "采购发票编号不能为空"),
    CTC_INVOICE_DETAIL_CODE_EXIST(10030063, "采购发票编号已存在!"),

    CTC_PAYMENT_PLAN_CODE_EXIST(10030064, "付款计划不能为空!"),
    CTC_PAYMENT_APPLY_CODE_EXIST(10030065, "付款申请不能为空!"),
    CTC_PAYMENT_PLAN_CODE_EXCESS(10030066, "该付款计划金额已满,不能继续申请!"),
    CTC_PAYMENT_INVOICE_CODE_EXCESS(10030067, "入账发票可用余额不足,不能继续申请!"),


    CTC_MILEPOST_DESIGN_PLAN_DETAIL_BRAND_NULL(10030068, "品牌不能为空"),
    CTC_MILEPOST_DESIGN_PLAN_DETAIL_NAME_NULL(10030069, "名称不能为空"),
    CTC_MILEPOST_DESIGN_PLAN_DETAIL_MODEL_NULL(10030070, "型号/规格/图号不能为空"),

    CTC_CARRYOVER_BILL_OU_ID_NULL(10030071, "业务实体不能为空"),
    CTC_DESIGN_PLAN_CONFIRM_NOT_NULL(10030072, "模组选择不能为空"),
    CTC_DESIGN_PLAN_APPROVALING_NOT_NULL(10030073, "还有审批中的详细设计方案流程，不能进行进度确认"),
    CTC_DESIGN_PLAN_CHANGE_RECORD_ERROR(10030074, "有物料在变更审批中，不能再进行变更"),
    CTC_DESIGN_PLAN_CHANGE_IMPORT_NOT_NULL(10030075, "导入物料不能为空"),
    CTC_PURCHASE_MATERIAL_REQUIREMENT_STATUS_NOT_NULL(10030076, "物料采购需求状态不能为空"),

    CTC_DELIVERYINSPECTION_NOT_NULL(10030077, "任务不存在"),
    CTC_DELIVERYINSPECTION_IS_NOT_NULL(10030078, "已经存在进行中的检查任务，请稍后再试"),
    CTC_DESIGN_PLAN_RECEIPTCLAIMCONTRACT(10030080, "该项目关联的合同当前没有回款，不允许提交模组确认"),
    CTC_QUOTATION_NOT_FIND(10030079, "报价单不存在"),
    CTC_PROJECT_HAS_STOP(10030081, "项目已终止"),

    CTC_PROJECT_TYPE_CODE_RULE_CONFIG_NOT_NULL(10030082, "项目类型设置编码规则类型不能为空"),
    CTC_PROJECT_CODE_RULE_TYPE_NOT_NULL(10030083, "项目编码规则类型不能为空"),
    CTC_PROJECT_CODE_NOT_NULL(10030084, "项目编码不能为空"),
    CTC_PROJECT_CODE_EXISTS(10030085, "项目编码已存在"),
    CTC_PROJECT_CODE_GENERATE_FAILED(10030086, "项目编码生成失败"),
    CTC_PROJECT_APPLICATION_INDUSTRY_NOT_NULL(10030087, "应用行业不能为空"),

    CTC_MATERIAL_ADJUST_HEADER_NOT_NULL(10030088, "物料新增和变更记录不能为空"),
    CTC_MATERIAL_ADJUST_HEADER_ADJUSTTYPE_NOT_NULL(10030089, "物料新增和变更-变更类型参数错误"),
    CTC_MATERIAL_ADJUST_DETAIL_NOT_NULL(10030090, "物料明细不能为空"),
    CTC_MATERIAL_ADJUST_SPAREPARTS_MASK(10030091, "备件标识需要在组织参数中维护"),
    CTC_INCOME_RATIO_TOTAL(10030092, "累计确认收入金额不能为空"),
    CTC_AUTO_LOANDAILY_TOTAL(10030093, "累计确认收入比例不能为空"),
    CTC_CODING_MIDDLECLASS_NOT_NULL(10030094, "物料中类不能为空"),
    CTC_SUBJECT_NOT_NULL(10030095, "存在无入账科目的工时，无法创建工时入账单"),
    CTC_SUBJECT_NOT_TRUE(10030096, "工时入账结转科目未配置正确，请先维护"),
    CTC_CODING_SUBCLASS_NOT_TRUE(10030097, "物料小类不正确，请按模板要求填写物料小类"),
    CTC_ORDER_UN_PUSH(10030098, "请勿重复同步采购订单"),
    CTC_MATERIAL_ADJUST_DETAIL_NOT_TRUE(10030099, "物料明细格式不正确"),
    CTC_INVOICE_PLAN_NOT_TRUE(10030100, "开票计划格式不正确"),

    CTC_PROJECT_WITH_CONTRACT_NOT_NULL(10030053, "当前项目没有成功关联合同，请返回检查数据！"),
    // statistics
    STATISTICS_REPORT_DEPARTMENT_CODE_NOT_NULL(10040001, "事业部代码不能为空"),
    STATISTICS_REPORT_DEPARTMENT_NAME_NOT_NULL(10040002, "事业部名称不能为空"),
    STATISTICS_REPORT_DEPARTMENT_FINANCIAL_NOT_NULL(10040003, "项目财务不能为空"),
    STATISTICS_REPORT_DEPARTMENT_START_DATE_NOT_NULL(10040004, "启用日期不能为空"),
    STATISTICS_REPORT_DEPARTMENT_UNIT_NOT_NULL(10040005, "业务分类不能为空"),
    STATISTICS_REPORT_DEPARTMENT_UNIT_EXISTS(10040006, "业务分类已分配"),
    STATISTICS_REPORT_DEPARTMENT_CODE_EXISTS(10040007, "事业部代码已存在"),
    STATISTICS_REPORT_DEPARTMENT_NAME_EXISTS(10040008, "事业部名称已存在"),
    STATISTICS_REPORT_DEPARTMENT_MANAGER_NOT_NULL(10040016, "事业部部门负责人不能为空"),

    STATISTICS_REPORT_GROUP_DESC_NOT_NULL(10040009, "报表组描述不能为空"),
    STATISTICS_REPORT_GROUP_NAME_NOT_NULL(10040010, "报表组名称不能为空"),
    STATISTICS_REPORT_GROUP_PERMISSION_NOT_NULL(10040011, "报表组控制权限不能为空"),
    STATISTICS_REPORT_GROUP_START_DATE_NOT_NULL(10040012, "启用日期不能为空"),

    STATISTICS_REPORT_GROUP_NAME_EXISTS(10040013, "报表组名称已存在"),
    STATISTICS_REPORT_GROUP_NOT_EXISTS(10040014, "报表组名称不存在"),
    STATISTICS_REPORT_GROUP_NO_DELETE(10040015, "报表组已分配给用户，不能进行删除"),

    CTC_TERMINATIONINSPECTION_IS_NOT_NULL(10040016, "已经存在进行中的项目终止检查任务，请稍后再试"),
    CTC_DELIVERY_TIME_IS_NOT_NULL(10040017, "模组层没有计划交货日期,无法提交，请联系项目经理"),
    CTC_PROJECT_TYPE_NOT_EQUALS(10040018, "项目类型不一致"),
    CTC_DESIGNPLAN_DRFT_IS_NOT_NULL(10040019, "模组层有草稿状态，无法提交，请联系项目经理"),
    CTC_INVOICE_DETAIL_CONTRACT_NOT_NULL(10040020, "采购合同编号不能为空"),
    CTC_INVOICE_DETAIL_VENDOR_NOT_NULL(10040021, "供应商信息不能为空"),
    CTC_RECEIPT_PLAN_NOT_NULL(10040022, "回款计划信息不能为空"),
    CTC_IMPORT_RECEIPT_PLAN_NOT_NULL(10040023, "回款计划导入信息不能为空"),
    CTC_RECEIPT_PLAN_NOT_TRUE(10040024, "回款计划信息格式不正确"),
    CTC_UNITID_NOT_NULL(10040025, "销售部门id不能为空"),
    CTC_CONTRACT_ID_NOT_NULL(10040026, "合同id不能为空"),
    CTC_INVOICE_PLAN_NOT_NULL(10040027, "开票计划不能为空"),
    CTC_IMPORT_INVOICE_PLAN_NOT_NULL(10040028, "导入的开票计划不能为空"),
    CTC_PROJECT_WBS_BUDGET_NOT_NULL(10040029, "WBS预算导入信息不能为空"),
    CTC_PROJECT_WBS_BUDGET_NOT_TRUE(10040030, "WBS预算导入信息格式不正确，请下载最新模板"),
    CTC_FILE_DATA_ERRO(100, "导入文件有误或数据类型未按要求填写，出勤日期必须为段日期格式，工时必须为数字"),

    CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS(10040031, "详细设计单据不存在"),
    CTC_PROJECT_WBS_DESIGN_PLAN_DETAIL_LIST_NOT_NULL(10040032, "WBS详细设计列表不能为空"),
    CTC_PROJECT_WBS_DESIGN_PLAN_REL_LIST_NOT_NULL(10040033, "WBS详细设计关联列表不能为空"),
    CTC_PROJECT_WBS_ERP_CODE_LIST_NOT_NULL(10040034, "ERP物料编码列表不能为空"),

    CTC_PURCHASE_CONTRACT_NOT_EXISTS(10040041, "采购合同不存在"),
    CTC_PURCHASE_CONTRACT_ID_NOT_NULL(10040042, "采购合同id不能为空"),
    CTC_PURCHASE_CONTRACT_AMOUNT_NOT_NULL(10040043, "采购合同金额不能为空"),
    CTC_PURCHASE_CONTRACT_EXECUTE_AMOUNT_NOT_NULL(10040044, "采购合同进度执行金额不能为空"),
    CTC_PURCHASE_CONTRACT_EXECUTE_PERCENT_NOT_NULL(10040045, "采购合同进度执行百分比不能为空"),
    CTC_PURCHASE_CONTRACT_BUDGET_ID_NOT_NULL(10040046, "采购合同预算ID不能为空"),
    CTC_PURCHASE_CONTRACT_PROGRESS_BUDGET_REL_LIST_NOT_NULL(10040047, "采购合同进度预算关联列表不能为空"),
    CTC_PURCHASE_CONTRACT_TEMPLATE_CODE_NOT_NULL(10040048, "采购合同模板编号不能为空"),
    CTC_PURCHASE_CONTRACT_DETAIL_LIST_NOT_EXISTS(10040049, "采购合同内容缺失"),
    CTC_PROJECT_WBS_BUDGET_NOT_FOUND(10040050, "项目不存在WBS预算"),

    CTC_CUSTOMER_TRANSFER_NOT_EXISTS(10040051, "客户间转款单不存在"),
    CTC_OUS_IS_NULL(10040052, "获取系统OU列表异常"),

    CTC_WBSSUMMARY_CODE_NULL(10040061, "WBS编码不能为空"),
    CTC_ACTIVITY_CODE_NULL(10040062, "活动事项编码不能为空"),

    WBS_RECEIPTS_IS_USING(1001, "当前所选模组有正在进行的单据"),

    CTC_CONTRACT_LIST_NOT_NULL(10040064, "合同列表不能为空"),

    PRODUCT_INTENTION_NOT_NULL(10002361, "产品意向不能为空"),

    COEFFICIENT_TYPE_NOT_NULL(10002362, "成本系数类型不能为空"),


    CTC_PAYMENT_INVOICE_ID_NOT_NULL(10040065, "应付发票id不能为空"),
    CTC_PAYMENT_INVOICE_FREEZE_TYPE_NOT_NULL(10040066, "应付发票冻结类型不能为空"),
    CTC_PAYMENT_INVOICE_FREEZE_REASON_NOT_NULL(10040067, "应付发票冻结原因不能为空"),

    CLOSE_CLASS_NOT_NULL(10000068, "商机关闭原因分类不能为空"),

    CLOSE_REASON_NOT_NULL(10000068, "商机关闭原因不能为空"),

    HANDLE_BY_NOT_NULL(10000069, "处理人ID不能为空"),
    ;


    ErrorCode(int code, String msg) {
        errorCode = code;
        errorMsg = msg;
    }

    private int errorCode;
    private String errorMsg;


    @Override
    public int getCode() {
        return errorCode;
    }

    @Override
    public String getMsg() {
        return errorMsg;
    }

    public static String getMsg(Integer code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.errorCode == code) {
                return errorCode.errorMsg;
            }
        }
        return null;
    }
}
