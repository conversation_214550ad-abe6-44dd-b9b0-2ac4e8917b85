package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.ProjectWbsChangeReceiptDetail;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.vo.DesignPlanDetailChangeHistoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@ApiModel(value = "详细设计单据")
public class ProjectWbsReceiptsDto extends ProjectWbsReceipts {

    //序号
    private Integer number;

    private String startTimeStr;

    private String endTimeStr;

    private String requirementStatusStr;

    private List<Integer> requirementStatusList;

    private String requirementTypeStr;

    private List<Integer> requirementTypeList;

    private List<Long> unitIds;

    private Long userId;

    //是否查询我的 true 是,false 否
    private Boolean me;

    @ApiModelProperty(value = "上传路径id(模组id)")
    private Long uploadPathId;

    @ApiModelProperty(value = "前端搜索结果详设Id列表")
    private List<Long> searchIdList;

    @ApiModelProperty(value = "单据详细设计关联列表")
    private List<ProjectWbsReceiptsDesignPlanRelDto> designRelList;

    @ApiModelProperty(value = "预算信息列表")
    private List<ProjectWbsReceiptsBudgetDto> budgetList;

    @ApiModelProperty(value = "附件列表")
    private List<CtcAttachmentDto> attachmentList;

    @ApiModelProperty(value = "详细设计方案Excel表格附件")
    private CtcAttachmentDto excelAttachment;

    @ApiModelProperty("详设列表")
    private List<MilepostDesignPlanDetailDto> designPlanDetailDtos;

    @ApiModelProperty("变更记录列表")
    private List<ProjectWbsReceiptsDto> changeReceiptsList;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "PAM物料编码")
    private String pamCode;

    @ApiModelProperty(value = "ERP物料编码")
    private String erpCode;

    @ApiModelProperty(value = "项目预算的WBS的上层WBS或;项目预算的WBS;")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "WBS层级")
    private String wbsLayer;

    @ApiModelProperty("原因后缀")
    private String reasonSuffix;

    @ApiModelProperty("是否已经发布暂存")
    private Boolean orStorage;

    @ApiModelProperty("变更模组")
    private MilepostDesignPlanDetailChangeDto detailChangeDto;

    @ApiModelProperty("变更模组及变更前后对比")
    private DesignPlanDetailChangeHistoryVO designPlanDetailChangeHistoryVO;

    @ApiModelProperty("审批流程实例ID")
    private String formInstanceId;

    @ApiModelProperty("附件")
    private List<CtcAttachmentDto> attachmentDtos;

    @ApiModelProperty(value = "发布人id")
    private Long publisherBy;

    @ApiModelProperty(value = "发布日期")
    private Date publishAt;


    private Boolean uploadPathIdIsNull;

    private List<Long> uploadPathIds;

    private Long markId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String projectCode;

    @ApiModelProperty("库存组织")
    private Long storageId;

    //所选模组的id及父id (wbs详设变更使用)
    private List<MilepostDesignPlanDetailDto> uploadPathList;

    //是否有已确认的
    private Boolean hasConfirmed;

    //详设id
    private Long designPlanDetailId;

    @ApiModelProperty(value = "预算信息变更列表")
    private List<ProjectWbsReceiptsBudgetChangeHistoryDto> projectWbsReceiptsBudgetChangeHistories;

    @ApiModelProperty(value = "采购员Mip")
    private String buyerMip;

    @ApiModelProperty("表单模板id")
    private String formTemplateId;

    @ApiModelProperty("物料采购预算汇总金额")
    private BigDecimal demandTotalAmount;

    @ApiModelProperty("物料外包预算汇总金额")
    private BigDecimal outsourceTotalAmount;

    @ApiModelProperty("预算汇总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("需求发布单为维度，预算占用金额，取金额最大的需求发布单")
    private BigDecimal maxTotalAmount;

    @ApiModelProperty("修改前的处理人MIP")
    private String oldHandleUserMip;

    @ApiModelProperty("详细设计变更记录表id")
    private Long milepostDesignPlanDetailChangeRecordId;

    private List<ProjectWbsReceiptsDto> totalAmountDtoList;

    private Long projectWbsReceiptsId;

    private List<MilepostDesignPlanDetailDto> flatMilepostDesignPlanDtos;

    @ApiModelProperty("平铺结构的变更模组")
    private List<MilepostDesignPlanDetailChangeDto> flatDetailChangeDto;

    private boolean projectWbsReceiptsRequirementIsChange = false;

    List<List<String>> wbsExcelRows;

    @ApiModelProperty("wbs变更单明细行")
    private List<ProjectWbsChangeReceiptDetail> projectWbsChangeReceiptDetailList;

    @ApiModelProperty("wbs变更单明细行")
    private List<Map<String, Object>> projectWbsChangeReceiptDetailMapList;

    private List<ProjectHistoryHeaderDto> projectHistoryHeaderList;


    @ApiModelProperty("停留天数")
    private String stayDay;

    @ApiModelProperty("项目leaderMip")
    private String projectLeaderMip;

    @ApiModelProperty("采购leaderMip")
    private String purchaseLeaderMip;


    @ApiModelProperty("处理的MIP账号对应名称")
    private String handleName;
    @ApiModelProperty("处理的MIP")
    private String handleMip;

    @ApiModelProperty("项目业务分类")
    private String unitName;

    @ApiModelProperty("leaderMip")
    private String projectLeaderListStr;

    @ApiModelProperty("流程实例id(iflow流程id)")
    private String fdInstanceId;

    @ApiModelProperty("上一流程操作时间")
    private Date approvalStayTime;

    private Long formInstanceIdChange;


}
