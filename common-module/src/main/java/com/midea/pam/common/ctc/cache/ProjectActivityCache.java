package com.midea.pam.common.ctc.cache;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Getter
@Setter
@ApiModel(value = "项目活动事项缓存对象")
public class ProjectActivityCache implements Comparable<ProjectActivityCache>{

    @ApiModelProperty(value = "序号")
    private String orderNo;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "类别名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "预算事项(0否/1是)")
    private Boolean budgetMattersState;

    @ApiModelProperty(value = "类别属性")
    private String type;

    @ApiModelProperty(value = "启用日期")
    private Date startTime;

    @ApiModelProperty(value = "失效日期")
    private Date endTime;

    @ApiModelProperty(value = "是否父级(0否/1是)")
    private Boolean parentState;

    @ApiModelProperty(value = "层级")
    private String level;

    @ApiModelProperty(value = "虚拟部门id")
    private Long unitId;


    public String getKey() {
        return "activity_" + orderNo;
    }


    @Override
    public int compareTo(ProjectActivityCache compare) {
        if(StringUtils.isBlank(orderNo)){
            return -1;
        }
        if(StringUtils.isBlank(compare.getOrderNo())){
            return 1;
        }
        String[] currentArr = orderNo.split("[.]");
        String[] compareArr = compare.getOrderNo().split("[.]");
        for(int i = 0; i < currentArr.length; i++){
            // 其中一项到最后一段
            if (i + 1 == currentArr.length || i + 1 == compareArr.length) {
                if(Integer.parseInt(currentArr[i]) == Integer.parseInt(compareArr[i]) && currentArr.length != compareArr.length){
                    // 如果其中一项全匹配都相同，sise短的排优先
                    return currentArr.length - compareArr.length ;
                }
            }
            // 如果相同continue，否则比较大小
            if(Integer.parseInt(currentArr[i]) == Integer.parseInt(compareArr[i])){
                continue;
            }
            return Integer.parseInt(currentArr[i]) - Integer.parseInt(compareArr[i]);
        }
        return 0;
    }
}
