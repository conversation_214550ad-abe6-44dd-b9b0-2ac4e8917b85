package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsContent;
import com.midea.pam.common.ctc.entity.StandardTermsDeviation;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class PurchaseOrderStandardTermsDto extends PurchaseOrderStandardTerms {

    /**
     * 采购订单标准条款内容集合
     */
//    List<PurchaseOrderStandardTermsContent> purchaseOrderStandardTermsContentList;

    /**
     * 采购订单标准条款偏离信息
     */
    List<StandardTermsDeviation> standardTermsDeviationList;
}
