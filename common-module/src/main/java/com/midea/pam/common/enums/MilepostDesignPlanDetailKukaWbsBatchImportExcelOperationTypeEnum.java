package com.midea.pam.common.enums;

public enum MilepostDesignPlanDetailKukaWbsBatchImportExcelOperationTypeEnum {

    ADD("ADD", "新增"),
    EDIT("EDIT", "修改");

    private String type;
    private String name;


    MilepostDesignPlanDetailKukaWbsBatchImportExcelOperationTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }
    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static MilepostDesignPlanDetailKukaWbsBatchImportExcelOperationTypeEnum getEnumByType(String type) {
        for (MilepostDesignPlanDetailKukaWbsBatchImportExcelOperationTypeEnum emun : values()) {
            if (emun.type.equals(type)) {
                return emun;
            }
        }
        return null;
    }
}
