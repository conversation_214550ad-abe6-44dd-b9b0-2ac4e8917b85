package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class MilepostDesignPlanDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MilepostDesignPlanDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdIsNull() {
            addCriterion("project_budget_material_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdIsNotNull() {
            addCriterion("project_budget_material_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdEqualTo(Long value) {
            addCriterion("project_budget_material_id =", value, "projectBudgetMaterialId");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdNotEqualTo(Long value) {
            addCriterion("project_budget_material_id <>", value, "projectBudgetMaterialId");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdGreaterThan(Long value) {
            addCriterion("project_budget_material_id >", value, "projectBudgetMaterialId");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_budget_material_id >=", value, "projectBudgetMaterialId");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdLessThan(Long value) {
            addCriterion("project_budget_material_id <", value, "projectBudgetMaterialId");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdLessThanOrEqualTo(Long value) {
            addCriterion("project_budget_material_id <=", value, "projectBudgetMaterialId");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdIn(List<Long> values) {
            addCriterion("project_budget_material_id in", values, "projectBudgetMaterialId");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdNotIn(List<Long> values) {
            addCriterion("project_budget_material_id not in", values, "projectBudgetMaterialId");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdBetween(Long value1, Long value2) {
            addCriterion("project_budget_material_id between", value1, value2, "projectBudgetMaterialId");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetMaterialIdNotBetween(Long value1, Long value2) {
            addCriterion("project_budget_material_id not between", value1, value2, "projectBudgetMaterialId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdIsNull() {
            addCriterion("milepost_id is null");
            return (Criteria) this;
        }

        public Criteria andMilepostIdIsNotNull() {
            addCriterion("milepost_id is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostIdEqualTo(Long value) {
            addCriterion("milepost_id =", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdNotEqualTo(Long value) {
            addCriterion("milepost_id <>", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdGreaterThan(Long value) {
            addCriterion("milepost_id >", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdGreaterThanOrEqualTo(Long value) {
            addCriterion("milepost_id >=", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdLessThan(Long value) {
            addCriterion("milepost_id <", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdLessThanOrEqualTo(Long value) {
            addCriterion("milepost_id <=", value, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdIn(List<Long> values) {
            addCriterion("milepost_id in", values, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdNotIn(List<Long> values) {
            addCriterion("milepost_id not in", values, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdBetween(Long value1, Long value2) {
            addCriterion("milepost_id between", value1, value2, "milepostId");
            return (Criteria) this;
        }

        public Criteria andMilepostIdNotBetween(Long value1, Long value2) {
            addCriterion("milepost_id not between", value1, value2, "milepostId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdIsNull() {
            addCriterion("submit_record_id is null");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdIsNotNull() {
            addCriterion("submit_record_id is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdEqualTo(Long value) {
            addCriterion("submit_record_id =", value, "submitRecordId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdNotEqualTo(Long value) {
            addCriterion("submit_record_id <>", value, "submitRecordId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdGreaterThan(Long value) {
            addCriterion("submit_record_id >", value, "submitRecordId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdGreaterThanOrEqualTo(Long value) {
            addCriterion("submit_record_id >=", value, "submitRecordId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdLessThan(Long value) {
            addCriterion("submit_record_id <", value, "submitRecordId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdLessThanOrEqualTo(Long value) {
            addCriterion("submit_record_id <=", value, "submitRecordId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdIn(List<Long> values) {
            addCriterion("submit_record_id in", values, "submitRecordId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdNotIn(List<Long> values) {
            addCriterion("submit_record_id not in", values, "submitRecordId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdBetween(Long value1, Long value2) {
            addCriterion("submit_record_id between", value1, value2, "submitRecordId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordIdNotBetween(Long value1, Long value2) {
            addCriterion("submit_record_id not between", value1, value2, "submitRecordId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdIsNull() {
            addCriterion("submit_record_rel_id is null");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdIsNotNull() {
            addCriterion("submit_record_rel_id is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdEqualTo(Long value) {
            addCriterion("submit_record_rel_id =", value, "submitRecordRelId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdNotEqualTo(Long value) {
            addCriterion("submit_record_rel_id <>", value, "submitRecordRelId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdGreaterThan(Long value) {
            addCriterion("submit_record_rel_id >", value, "submitRecordRelId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("submit_record_rel_id >=", value, "submitRecordRelId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdLessThan(Long value) {
            addCriterion("submit_record_rel_id <", value, "submitRecordRelId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdLessThanOrEqualTo(Long value) {
            addCriterion("submit_record_rel_id <=", value, "submitRecordRelId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdIn(List<Long> values) {
            addCriterion("submit_record_rel_id in", values, "submitRecordRelId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdNotIn(List<Long> values) {
            addCriterion("submit_record_rel_id not in", values, "submitRecordRelId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdBetween(Long value1, Long value2) {
            addCriterion("submit_record_rel_id between", value1, value2, "submitRecordRelId");
            return (Criteria) this;
        }

        public Criteria andSubmitRecordRelIdNotBetween(Long value1, Long value2) {
            addCriterion("submit_record_rel_id not between", value1, value2, "submitRecordRelId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdIsNull() {
            addCriterion("change_record_id is null");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdIsNotNull() {
            addCriterion("change_record_id is not null");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdEqualTo(Long value) {
            addCriterion("change_record_id =", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdNotEqualTo(Long value) {
            addCriterion("change_record_id <>", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdGreaterThan(Long value) {
            addCriterion("change_record_id >", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdGreaterThanOrEqualTo(Long value) {
            addCriterion("change_record_id >=", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdLessThan(Long value) {
            addCriterion("change_record_id <", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdLessThanOrEqualTo(Long value) {
            addCriterion("change_record_id <=", value, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdIn(List<Long> values) {
            addCriterion("change_record_id in", values, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdNotIn(List<Long> values) {
            addCriterion("change_record_id not in", values, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdBetween(Long value1, Long value2) {
            addCriterion("change_record_id between", value1, value2, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andChangeRecordIdNotBetween(Long value1, Long value2) {
            addCriterion("change_record_id not between", value1, value2, "changeRecordId");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Long value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Long value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Long value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Long value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Long value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Long> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Long> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Long value1, Long value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Long value1, Long value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andLevelIsNull() {
            addCriterion("level is null");
            return (Criteria) this;
        }

        public Criteria andLevelIsNotNull() {
            addCriterion("level is not null");
            return (Criteria) this;
        }

        public Criteria andLevelEqualTo(Integer value) {
            addCriterion("level =", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotEqualTo(Integer value) {
            addCriterion("level <>", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThan(Integer value) {
            addCriterion("level >", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("level >=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThan(Integer value) {
            addCriterion("level <", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThanOrEqualTo(Integer value) {
            addCriterion("level <=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelIn(List<Integer> values) {
            addCriterion("level in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotIn(List<Integer> values) {
            addCriterion("level not in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelBetween(Integer value1, Integer value2) {
            addCriterion("level between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("level not between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andNumIsNull() {
            addCriterion("num is null");
            return (Criteria) this;
        }

        public Criteria andNumIsNotNull() {
            addCriterion("num is not null");
            return (Criteria) this;
        }

        public Criteria andNumEqualTo(String value) {
            addCriterion("num =", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotEqualTo(String value) {
            addCriterion("num <>", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThan(String value) {
            addCriterion("num >", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThanOrEqualTo(String value) {
            addCriterion("num >=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThan(String value) {
            addCriterion("num <", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThanOrEqualTo(String value) {
            addCriterion("num <=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLike(String value) {
            addCriterion("num like", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotLike(String value) {
            addCriterion("num not like", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumIn(List<String> values) {
            addCriterion("num in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotIn(List<String> values) {
            addCriterion("num not in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumBetween(String value1, String value2) {
            addCriterion("num between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotBetween(String value1, String value2) {
            addCriterion("num not between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andWhetherModelIsNull() {
            addCriterion("whether_model is null");
            return (Criteria) this;
        }

        public Criteria andWhetherModelIsNotNull() {
            addCriterion("whether_model is not null");
            return (Criteria) this;
        }

        public Criteria andWhetherModelEqualTo(Boolean value) {
            addCriterion("whether_model =", value, "whetherModel");
            return (Criteria) this;
        }

        public Criteria andWhetherModelNotEqualTo(Boolean value) {
            addCriterion("whether_model <>", value, "whetherModel");
            return (Criteria) this;
        }

        public Criteria andWhetherModelGreaterThan(Boolean value) {
            addCriterion("whether_model >", value, "whetherModel");
            return (Criteria) this;
        }

        public Criteria andWhetherModelGreaterThanOrEqualTo(Boolean value) {
            addCriterion("whether_model >=", value, "whetherModel");
            return (Criteria) this;
        }

        public Criteria andWhetherModelLessThan(Boolean value) {
            addCriterion("whether_model <", value, "whetherModel");
            return (Criteria) this;
        }

        public Criteria andWhetherModelLessThanOrEqualTo(Boolean value) {
            addCriterion("whether_model <=", value, "whetherModel");
            return (Criteria) this;
        }

        public Criteria andWhetherModelIn(List<Boolean> values) {
            addCriterion("whether_model in", values, "whetherModel");
            return (Criteria) this;
        }

        public Criteria andWhetherModelNotIn(List<Boolean> values) {
            addCriterion("whether_model not in", values, "whetherModel");
            return (Criteria) this;
        }

        public Criteria andWhetherModelBetween(Boolean value1, Boolean value2) {
            addCriterion("whether_model between", value1, value2, "whetherModel");
            return (Criteria) this;
        }

        public Criteria andWhetherModelNotBetween(Boolean value1, Boolean value2) {
            addCriterion("whether_model not between", value1, value2, "whetherModel");
            return (Criteria) this;
        }

        public Criteria andModuleStatusIsNull() {
            addCriterion("module_status is null");
            return (Criteria) this;
        }

        public Criteria andModuleStatusIsNotNull() {
            addCriterion("module_status is not null");
            return (Criteria) this;
        }

        public Criteria andModuleStatusEqualTo(Integer value) {
            addCriterion("module_status =", value, "moduleStatus");
            return (Criteria) this;
        }

        public Criteria andModuleStatusNotEqualTo(Integer value) {
            addCriterion("module_status <>", value, "moduleStatus");
            return (Criteria) this;
        }

        public Criteria andModuleStatusGreaterThan(Integer value) {
            addCriterion("module_status >", value, "moduleStatus");
            return (Criteria) this;
        }

        public Criteria andModuleStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("module_status >=", value, "moduleStatus");
            return (Criteria) this;
        }

        public Criteria andModuleStatusLessThan(Integer value) {
            addCriterion("module_status <", value, "moduleStatus");
            return (Criteria) this;
        }

        public Criteria andModuleStatusLessThanOrEqualTo(Integer value) {
            addCriterion("module_status <=", value, "moduleStatus");
            return (Criteria) this;
        }

        public Criteria andModuleStatusIn(List<Integer> values) {
            addCriterion("module_status in", values, "moduleStatus");
            return (Criteria) this;
        }

        public Criteria andModuleStatusNotIn(List<Integer> values) {
            addCriterion("module_status not in", values, "moduleStatus");
            return (Criteria) this;
        }

        public Criteria andModuleStatusBetween(Integer value1, Integer value2) {
            addCriterion("module_status between", value1, value2, "moduleStatus");
            return (Criteria) this;
        }

        public Criteria andModuleStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("module_status not between", value1, value2, "moduleStatus");
            return (Criteria) this;
        }

        public Criteria andExtIsNull() {
            addCriterion("ext is null");
            return (Criteria) this;
        }

        public Criteria andExtIsNotNull() {
            addCriterion("ext is not null");
            return (Criteria) this;
        }

        public Criteria andExtEqualTo(Boolean value) {
            addCriterion("ext =", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotEqualTo(Boolean value) {
            addCriterion("ext <>", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtGreaterThan(Boolean value) {
            addCriterion("ext >", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtGreaterThanOrEqualTo(Boolean value) {
            addCriterion("ext >=", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLessThan(Boolean value) {
            addCriterion("ext <", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLessThanOrEqualTo(Boolean value) {
            addCriterion("ext <=", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtIn(List<Boolean> values) {
            addCriterion("ext in", values, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotIn(List<Boolean> values) {
            addCriterion("ext not in", values, "ext");
            return (Criteria) this;
        }

        public Criteria andExtBetween(Boolean value1, Boolean value2) {
            addCriterion("ext between", value1, value2, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotBetween(Boolean value1, Boolean value2) {
            addCriterion("ext not between", value1, value2, "ext");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNull() {
            addCriterion("materiel_descr is null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNotNull() {
            addCriterion("materiel_descr is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrEqualTo(String value) {
            addCriterion("materiel_descr =", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotEqualTo(String value) {
            addCriterion("materiel_descr <>", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThan(String value) {
            addCriterion("materiel_descr >", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_descr >=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThan(String value) {
            addCriterion("materiel_descr <", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThanOrEqualTo(String value) {
            addCriterion("materiel_descr <=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLike(String value) {
            addCriterion("materiel_descr like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotLike(String value) {
            addCriterion("materiel_descr not like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIn(List<String> values) {
            addCriterion("materiel_descr in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotIn(List<String> values) {
            addCriterion("materiel_descr not in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrBetween(String value1, String value2) {
            addCriterion("materiel_descr between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotBetween(String value1, String value2) {
            addCriterion("materiel_descr not between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIsNull() {
            addCriterion("unit_code is null");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIsNotNull() {
            addCriterion("unit_code is not null");
            return (Criteria) this;
        }

        public Criteria andUnitCodeEqualTo(String value) {
            addCriterion("unit_code =", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotEqualTo(String value) {
            addCriterion("unit_code <>", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeGreaterThan(String value) {
            addCriterion("unit_code >", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeGreaterThanOrEqualTo(String value) {
            addCriterion("unit_code >=", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLessThan(String value) {
            addCriterion("unit_code <", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLessThanOrEqualTo(String value) {
            addCriterion("unit_code <=", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLike(String value) {
            addCriterion("unit_code like", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotLike(String value) {
            addCriterion("unit_code not like", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIn(List<String> values) {
            addCriterion("unit_code in", values, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotIn(List<String> values) {
            addCriterion("unit_code not in", values, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeBetween(String value1, String value2) {
            addCriterion("unit_code between", value1, value2, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotBetween(String value1, String value2) {
            addCriterion("unit_code not between", value1, value2, "unitCode");
            return (Criteria) this;
        }

        public Criteria andNumberIsNull() {
            addCriterion("number is null");
            return (Criteria) this;
        }

        public Criteria andNumberIsNotNull() {
            addCriterion("number is not null");
            return (Criteria) this;
        }

        public Criteria andNumberEqualTo(BigDecimal value) {
            addCriterion("number =", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotEqualTo(BigDecimal value) {
            addCriterion("number <>", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThan(BigDecimal value) {
            addCriterion("number >", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("number >=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThan(BigDecimal value) {
            addCriterion("number <", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThanOrEqualTo(BigDecimal value) {
            addCriterion("number <=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberIn(List<BigDecimal> values) {
            addCriterion("number in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotIn(List<BigDecimal> values) {
            addCriterion("number not in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("number between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("number not between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNull() {
            addCriterion("delivery_time is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNotNull() {
            addCriterion("delivery_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeEqualTo(Date value) {
            addCriterion("delivery_time =", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotEqualTo(Date value) {
            addCriterion("delivery_time <>", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThan(Date value) {
            addCriterion("delivery_time >", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("delivery_time >=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThan(Date value) {
            addCriterion("delivery_time <", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThanOrEqualTo(Date value) {
            addCriterion("delivery_time <=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIn(List<Date> values) {
            addCriterion("delivery_time in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotIn(List<Date> values) {
            addCriterion("delivery_time not in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeBetween(Date value1, Date value2) {
            addCriterion("delivery_time between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotBetween(Date value1, Date value2) {
            addCriterion("delivery_time not between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationIsNull() {
            addCriterion("business_classification is null");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationIsNotNull() {
            addCriterion("business_classification is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationEqualTo(String value) {
            addCriterion("business_classification =", value, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationNotEqualTo(String value) {
            addCriterion("business_classification <>", value, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationGreaterThan(String value) {
            addCriterion("business_classification >", value, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationGreaterThanOrEqualTo(String value) {
            addCriterion("business_classification >=", value, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationLessThan(String value) {
            addCriterion("business_classification <", value, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationLessThanOrEqualTo(String value) {
            addCriterion("business_classification <=", value, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationLike(String value) {
            addCriterion("business_classification like", value, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationNotLike(String value) {
            addCriterion("business_classification not like", value, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationIn(List<String> values) {
            addCriterion("business_classification in", values, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationNotIn(List<String> values) {
            addCriterion("business_classification not in", values, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationBetween(String value1, String value2) {
            addCriterion("business_classification between", value1, value2, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andBusinessClassificationNotBetween(String value1, String value2) {
            addCriterion("business_classification not between", value1, value2, "businessClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIsNull() {
            addCriterion("material_classification is null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIsNotNull() {
            addCriterion("material_classification is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationEqualTo(String value) {
            addCriterion("material_classification =", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotEqualTo(String value) {
            addCriterion("material_classification <>", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationGreaterThan(String value) {
            addCriterion("material_classification >", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationGreaterThanOrEqualTo(String value) {
            addCriterion("material_classification >=", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLessThan(String value) {
            addCriterion("material_classification <", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLessThanOrEqualTo(String value) {
            addCriterion("material_classification <=", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationLike(String value) {
            addCriterion("material_classification like", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotLike(String value) {
            addCriterion("material_classification not like", value, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationIn(List<String> values) {
            addCriterion("material_classification in", values, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotIn(List<String> values) {
            addCriterion("material_classification not in", values, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationBetween(String value1, String value2) {
            addCriterion("material_classification between", value1, value2, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterialClassificationNotBetween(String value1, String value2) {
            addCriterion("material_classification not between", value1, value2, "materialClassification");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeIsNull() {
            addCriterion("materiel_type is null");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeIsNotNull() {
            addCriterion("materiel_type is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeEqualTo(String value) {
            addCriterion("materiel_type =", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotEqualTo(String value) {
            addCriterion("materiel_type <>", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeGreaterThan(String value) {
            addCriterion("materiel_type >", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_type >=", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeLessThan(String value) {
            addCriterion("materiel_type <", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeLessThanOrEqualTo(String value) {
            addCriterion("materiel_type <=", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeLike(String value) {
            addCriterion("materiel_type like", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotLike(String value) {
            addCriterion("materiel_type not like", value, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeIn(List<String> values) {
            addCriterion("materiel_type in", values, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotIn(List<String> values) {
            addCriterion("materiel_type not in", values, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeBetween(String value1, String value2) {
            addCriterion("materiel_type between", value1, value2, "materielType");
            return (Criteria) this;
        }

        public Criteria andMaterielTypeNotBetween(String value1, String value2) {
            addCriterion("materiel_type not between", value1, value2, "materielType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIsNull() {
            addCriterion("machining_part_type is null");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIsNotNull() {
            addCriterion("machining_part_type is not null");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeEqualTo(String value) {
            addCriterion("machining_part_type =", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotEqualTo(String value) {
            addCriterion("machining_part_type <>", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeGreaterThan(String value) {
            addCriterion("machining_part_type >", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeGreaterThanOrEqualTo(String value) {
            addCriterion("machining_part_type >=", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLessThan(String value) {
            addCriterion("machining_part_type <", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLessThanOrEqualTo(String value) {
            addCriterion("machining_part_type <=", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeLike(String value) {
            addCriterion("machining_part_type like", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotLike(String value) {
            addCriterion("machining_part_type not like", value, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeIn(List<String> values) {
            addCriterion("machining_part_type in", values, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotIn(List<String> values) {
            addCriterion("machining_part_type not in", values, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeBetween(String value1, String value2) {
            addCriterion("machining_part_type between", value1, value2, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMachiningPartTypeNotBetween(String value1, String value2) {
            addCriterion("machining_part_type not between", value1, value2, "machiningPartType");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNull() {
            addCriterion("material is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNotNull() {
            addCriterion("material is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialEqualTo(String value) {
            addCriterion("material =", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotEqualTo(String value) {
            addCriterion("material <>", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThan(String value) {
            addCriterion("material >", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThanOrEqualTo(String value) {
            addCriterion("material >=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThan(String value) {
            addCriterion("material <", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThanOrEqualTo(String value) {
            addCriterion("material <=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLike(String value) {
            addCriterion("material like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotLike(String value) {
            addCriterion("material not like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialIn(List<String> values) {
            addCriterion("material in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotIn(List<String> values) {
            addCriterion("material not in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialBetween(String value1, String value2) {
            addCriterion("material between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotBetween(String value1, String value2) {
            addCriterion("material not between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIsNull() {
            addCriterion("unit_weight is null");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIsNotNull() {
            addCriterion("unit_weight is not null");
            return (Criteria) this;
        }

        public Criteria andUnitWeightEqualTo(BigDecimal value) {
            addCriterion("unit_weight =", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotEqualTo(BigDecimal value) {
            addCriterion("unit_weight <>", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightGreaterThan(BigDecimal value) {
            addCriterion("unit_weight >", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_weight >=", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightLessThan(BigDecimal value) {
            addCriterion("unit_weight <", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_weight <=", value, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightIn(List<BigDecimal> values) {
            addCriterion("unit_weight in", values, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotIn(List<BigDecimal> values) {
            addCriterion("unit_weight not in", values, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_weight between", value1, value2, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andUnitWeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_weight not between", value1, value2, "unitWeight");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingIsNull() {
            addCriterion("material_processing is null");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingIsNotNull() {
            addCriterion("material_processing is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingEqualTo(String value) {
            addCriterion("material_processing =", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotEqualTo(String value) {
            addCriterion("material_processing <>", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingGreaterThan(String value) {
            addCriterion("material_processing >", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingGreaterThanOrEqualTo(String value) {
            addCriterion("material_processing >=", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingLessThan(String value) {
            addCriterion("material_processing <", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingLessThanOrEqualTo(String value) {
            addCriterion("material_processing <=", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingLike(String value) {
            addCriterion("material_processing like", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotLike(String value) {
            addCriterion("material_processing not like", value, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingIn(List<String> values) {
            addCriterion("material_processing in", values, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotIn(List<String> values) {
            addCriterion("material_processing not in", values, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingBetween(String value1, String value2) {
            addCriterion("material_processing between", value1, value2, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andMaterialProcessingNotBetween(String value1, String value2) {
            addCriterion("material_processing not between", value1, value2, "materialProcessing");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceIsNull() {
            addCriterion("budget_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceIsNotNull() {
            addCriterion("budget_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceEqualTo(BigDecimal value) {
            addCriterion("budget_unit_price =", value, "budgetUnitPrice");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("budget_unit_price <>", value, "budgetUnitPrice");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("budget_unit_price >", value, "budgetUnitPrice");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_unit_price >=", value, "budgetUnitPrice");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceLessThan(BigDecimal value) {
            addCriterion("budget_unit_price <", value, "budgetUnitPrice");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_unit_price <=", value, "budgetUnitPrice");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceIn(List<BigDecimal> values) {
            addCriterion("budget_unit_price in", values, "budgetUnitPrice");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("budget_unit_price not in", values, "budgetUnitPrice");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_unit_price between", value1, value2, "budgetUnitPrice");
            return (Criteria) this;
        }

        public Criteria andBudgetUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_unit_price not between", value1, value2, "budgetUnitPrice");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdIsNull() {
            addCriterion("design_cost_id is null");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdIsNotNull() {
            addCriterion("design_cost_id is not null");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdEqualTo(Long value) {
            addCriterion("design_cost_id =", value, "designCostId");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdNotEqualTo(Long value) {
            addCriterion("design_cost_id <>", value, "designCostId");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdGreaterThan(Long value) {
            addCriterion("design_cost_id >", value, "designCostId");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdGreaterThanOrEqualTo(Long value) {
            addCriterion("design_cost_id >=", value, "designCostId");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdLessThan(Long value) {
            addCriterion("design_cost_id <", value, "designCostId");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdLessThanOrEqualTo(Long value) {
            addCriterion("design_cost_id <=", value, "designCostId");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdIn(List<Long> values) {
            addCriterion("design_cost_id in", values, "designCostId");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdNotIn(List<Long> values) {
            addCriterion("design_cost_id not in", values, "designCostId");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdBetween(Long value1, Long value2) {
            addCriterion("design_cost_id between", value1, value2, "designCostId");
            return (Criteria) this;
        }

        public Criteria andDesignCostIdNotBetween(Long value1, Long value2) {
            addCriterion("design_cost_id not between", value1, value2, "designCostId");
            return (Criteria) this;
        }

        public Criteria andDesignCostIsNull() {
            addCriterion("design_cost is null");
            return (Criteria) this;
        }

        public Criteria andDesignCostIsNotNull() {
            addCriterion("design_cost is not null");
            return (Criteria) this;
        }

        public Criteria andDesignCostEqualTo(BigDecimal value) {
            addCriterion("design_cost =", value, "designCost");
            return (Criteria) this;
        }

        public Criteria andDesignCostNotEqualTo(BigDecimal value) {
            addCriterion("design_cost <>", value, "designCost");
            return (Criteria) this;
        }

        public Criteria andDesignCostGreaterThan(BigDecimal value) {
            addCriterion("design_cost >", value, "designCost");
            return (Criteria) this;
        }

        public Criteria andDesignCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("design_cost >=", value, "designCost");
            return (Criteria) this;
        }

        public Criteria andDesignCostLessThan(BigDecimal value) {
            addCriterion("design_cost <", value, "designCost");
            return (Criteria) this;
        }

        public Criteria andDesignCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("design_cost <=", value, "designCost");
            return (Criteria) this;
        }

        public Criteria andDesignCostIn(List<BigDecimal> values) {
            addCriterion("design_cost in", values, "designCost");
            return (Criteria) this;
        }

        public Criteria andDesignCostNotIn(List<BigDecimal> values) {
            addCriterion("design_cost not in", values, "designCost");
            return (Criteria) this;
        }

        public Criteria andDesignCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("design_cost between", value1, value2, "designCost");
            return (Criteria) this;
        }

        public Criteria andDesignCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("design_cost not between", value1, value2, "designCost");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalIsNull() {
            addCriterion("budget_subtotal is null");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalIsNotNull() {
            addCriterion("budget_subtotal is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalEqualTo(BigDecimal value) {
            addCriterion("budget_subtotal =", value, "budgetSubtotal");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalNotEqualTo(BigDecimal value) {
            addCriterion("budget_subtotal <>", value, "budgetSubtotal");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalGreaterThan(BigDecimal value) {
            addCriterion("budget_subtotal >", value, "budgetSubtotal");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_subtotal >=", value, "budgetSubtotal");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalLessThan(BigDecimal value) {
            addCriterion("budget_subtotal <", value, "budgetSubtotal");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_subtotal <=", value, "budgetSubtotal");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalIn(List<BigDecimal> values) {
            addCriterion("budget_subtotal in", values, "budgetSubtotal");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalNotIn(List<BigDecimal> values) {
            addCriterion("budget_subtotal not in", values, "budgetSubtotal");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_subtotal between", value1, value2, "budgetSubtotal");
            return (Criteria) this;
        }

        public Criteria andBudgetSubtotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_subtotal not between", value1, value2, "budgetSubtotal");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceIsNull() {
            addCriterion("erp_code_source is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceIsNotNull() {
            addCriterion("erp_code_source is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceEqualTo(Integer value) {
            addCriterion("erp_code_source =", value, "erpCodeSource");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceNotEqualTo(Integer value) {
            addCriterion("erp_code_source <>", value, "erpCodeSource");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceGreaterThan(Integer value) {
            addCriterion("erp_code_source >", value, "erpCodeSource");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("erp_code_source >=", value, "erpCodeSource");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceLessThan(Integer value) {
            addCriterion("erp_code_source <", value, "erpCodeSource");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceLessThanOrEqualTo(Integer value) {
            addCriterion("erp_code_source <=", value, "erpCodeSource");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceIn(List<Integer> values) {
            addCriterion("erp_code_source in", values, "erpCodeSource");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceNotIn(List<Integer> values) {
            addCriterion("erp_code_source not in", values, "erpCodeSource");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceBetween(Integer value1, Integer value2) {
            addCriterion("erp_code_source between", value1, value2, "erpCodeSource");
            return (Criteria) this;
        }

        public Criteria andErpCodeSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("erp_code_source not between", value1, value2, "erpCodeSource");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusIsNull() {
            addCriterion("materiel_status is null");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusIsNotNull() {
            addCriterion("materiel_status is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusEqualTo(Integer value) {
            addCriterion("materiel_status =", value, "materielStatus");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusNotEqualTo(Integer value) {
            addCriterion("materiel_status <>", value, "materielStatus");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusGreaterThan(Integer value) {
            addCriterion("materiel_status >", value, "materielStatus");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("materiel_status >=", value, "materielStatus");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusLessThan(Integer value) {
            addCriterion("materiel_status <", value, "materielStatus");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusLessThanOrEqualTo(Integer value) {
            addCriterion("materiel_status <=", value, "materielStatus");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusIn(List<Integer> values) {
            addCriterion("materiel_status in", values, "materielStatus");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusNotIn(List<Integer> values) {
            addCriterion("materiel_status not in", values, "materielStatus");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusBetween(Integer value1, Integer value2) {
            addCriterion("materiel_status between", value1, value2, "materielStatus");
            return (Criteria) this;
        }

        public Criteria andMaterielStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("materiel_status not between", value1, value2, "materielStatus");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("source like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("source not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andIndexNumIsNull() {
            addCriterion("index_num is null");
            return (Criteria) this;
        }

        public Criteria andIndexNumIsNotNull() {
            addCriterion("index_num is not null");
            return (Criteria) this;
        }

        public Criteria andIndexNumEqualTo(String value) {
            addCriterion("index_num =", value, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumNotEqualTo(String value) {
            addCriterion("index_num <>", value, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumGreaterThan(String value) {
            addCriterion("index_num >", value, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumGreaterThanOrEqualTo(String value) {
            addCriterion("index_num >=", value, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumLessThan(String value) {
            addCriterion("index_num <", value, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumLessThanOrEqualTo(String value) {
            addCriterion("index_num <=", value, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumLike(String value) {
            addCriterion("index_num like", value, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumNotLike(String value) {
            addCriterion("index_num not like", value, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumIn(List<String> values) {
            addCriterion("index_num in", values, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumNotIn(List<String> values) {
            addCriterion("index_num not in", values, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumBetween(String value1, String value2) {
            addCriterion("index_num between", value1, value2, "indexNum");
            return (Criteria) this;
        }

        public Criteria andIndexNumNotBetween(String value1, String value2) {
            addCriterion("index_num not between", value1, value2, "indexNum");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementIsNull() {
            addCriterion("generate_requirement is null");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementIsNotNull() {
            addCriterion("generate_requirement is not null");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementEqualTo(Boolean value) {
            addCriterion("generate_requirement =", value, "generateRequirement");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementNotEqualTo(Boolean value) {
            addCriterion("generate_requirement <>", value, "generateRequirement");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementGreaterThan(Boolean value) {
            addCriterion("generate_requirement >", value, "generateRequirement");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementGreaterThanOrEqualTo(Boolean value) {
            addCriterion("generate_requirement >=", value, "generateRequirement");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementLessThan(Boolean value) {
            addCriterion("generate_requirement <", value, "generateRequirement");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementLessThanOrEqualTo(Boolean value) {
            addCriterion("generate_requirement <=", value, "generateRequirement");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementIn(List<Boolean> values) {
            addCriterion("generate_requirement in", values, "generateRequirement");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementNotIn(List<Boolean> values) {
            addCriterion("generate_requirement not in", values, "generateRequirement");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementBetween(Boolean value1, Boolean value2) {
            addCriterion("generate_requirement between", value1, value2, "generateRequirement");
            return (Criteria) this;
        }

        public Criteria andGenerateRequirementNotBetween(Boolean value1, Boolean value2) {
            addCriterion("generate_requirement not between", value1, value2, "generateRequirement");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andInitIsNull() {
            addCriterion("init is null");
            return (Criteria) this;
        }

        public Criteria andInitIsNotNull() {
            addCriterion("init is not null");
            return (Criteria) this;
        }

        public Criteria andInitEqualTo(Boolean value) {
            addCriterion("init =", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitNotEqualTo(Boolean value) {
            addCriterion("init <>", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitGreaterThan(Boolean value) {
            addCriterion("init >", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitGreaterThanOrEqualTo(Boolean value) {
            addCriterion("init >=", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitLessThan(Boolean value) {
            addCriterion("init <", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitLessThanOrEqualTo(Boolean value) {
            addCriterion("init <=", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitIn(List<Boolean> values) {
            addCriterion("init in", values, "init");
            return (Criteria) this;
        }

        public Criteria andInitNotIn(List<Boolean> values) {
            addCriterion("init not in", values, "init");
            return (Criteria) this;
        }

        public Criteria andInitBetween(Boolean value1, Boolean value2) {
            addCriterion("init between", value1, value2, "init");
            return (Criteria) this;
        }

        public Criteria andInitNotBetween(Boolean value1, Boolean value2) {
            addCriterion("init not between", value1, value2, "init");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullIsNull() {
            addCriterion("item_cost_is_null is null");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullIsNotNull() {
            addCriterion("item_cost_is_null is not null");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullEqualTo(Integer value) {
            addCriterion("item_cost_is_null =", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullNotEqualTo(Integer value) {
            addCriterion("item_cost_is_null <>", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullGreaterThan(Integer value) {
            addCriterion("item_cost_is_null >", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullGreaterThanOrEqualTo(Integer value) {
            addCriterion("item_cost_is_null >=", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullLessThan(Integer value) {
            addCriterion("item_cost_is_null <", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullLessThanOrEqualTo(Integer value) {
            addCriterion("item_cost_is_null <=", value, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullIn(List<Integer> values) {
            addCriterion("item_cost_is_null in", values, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullNotIn(List<Integer> values) {
            addCriterion("item_cost_is_null not in", values, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullBetween(Integer value1, Integer value2) {
            addCriterion("item_cost_is_null between", value1, value2, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andItemCostIsNullNotBetween(Integer value1, Integer value2) {
            addCriterion("item_cost_is_null not between", value1, value2, "itemCostIsNull");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIsNull() {
            addCriterion("material_category is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIsNotNull() {
            addCriterion("material_category is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryEqualTo(String value) {
            addCriterion("material_category =", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotEqualTo(String value) {
            addCriterion("material_category <>", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryGreaterThan(String value) {
            addCriterion("material_category >", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("material_category >=", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLessThan(String value) {
            addCriterion("material_category <", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLessThanOrEqualTo(String value) {
            addCriterion("material_category <=", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryLike(String value) {
            addCriterion("material_category like", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotLike(String value) {
            addCriterion("material_category not like", value, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryIn(List<String> values) {
            addCriterion("material_category in", values, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotIn(List<String> values) {
            addCriterion("material_category not in", values, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryBetween(String value1, String value2) {
            addCriterion("material_category between", value1, value2, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andMaterialCategoryNotBetween(String value1, String value2) {
            addCriterion("material_category not between", value1, value2, "materialCategory");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassIsNull() {
            addCriterion("coding_middle_class is null");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassIsNotNull() {
            addCriterion("coding_middle_class is not null");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassEqualTo(String value) {
            addCriterion("coding_middle_class =", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassNotEqualTo(String value) {
            addCriterion("coding_middle_class <>", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassGreaterThan(String value) {
            addCriterion("coding_middle_class >", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassGreaterThanOrEqualTo(String value) {
            addCriterion("coding_middle_class >=", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassLessThan(String value) {
            addCriterion("coding_middle_class <", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassLessThanOrEqualTo(String value) {
            addCriterion("coding_middle_class <=", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassLike(String value) {
            addCriterion("coding_middle_class like", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassNotLike(String value) {
            addCriterion("coding_middle_class not like", value, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassIn(List<String> values) {
            addCriterion("coding_middle_class in", values, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassNotIn(List<String> values) {
            addCriterion("coding_middle_class not in", values, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassBetween(String value1, String value2) {
            addCriterion("coding_middle_class between", value1, value2, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andCodingMiddleClassNotBetween(String value1, String value2) {
            addCriterion("coding_middle_class not between", value1, value2, "codingMiddleClass");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNull() {
            addCriterion("figure_number is null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNotNull() {
            addCriterion("figure_number is not null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberEqualTo(String value) {
            addCriterion("figure_number =", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotEqualTo(String value) {
            addCriterion("figure_number <>", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThan(String value) {
            addCriterion("figure_number >", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThanOrEqualTo(String value) {
            addCriterion("figure_number >=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThan(String value) {
            addCriterion("figure_number <", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThanOrEqualTo(String value) {
            addCriterion("figure_number <=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLike(String value) {
            addCriterion("figure_number like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotLike(String value) {
            addCriterion("figure_number not like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIn(List<String> values) {
            addCriterion("figure_number in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotIn(List<String> values) {
            addCriterion("figure_number not in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberBetween(String value1, String value2) {
            addCriterion("figure_number between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotBetween(String value1, String value2) {
            addCriterion("figure_number not between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andChartVersionIsNull() {
            addCriterion("chart_version is null");
            return (Criteria) this;
        }

        public Criteria andChartVersionIsNotNull() {
            addCriterion("chart_version is not null");
            return (Criteria) this;
        }

        public Criteria andChartVersionEqualTo(String value) {
            addCriterion("chart_version =", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotEqualTo(String value) {
            addCriterion("chart_version <>", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionGreaterThan(String value) {
            addCriterion("chart_version >", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionGreaterThanOrEqualTo(String value) {
            addCriterion("chart_version >=", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLessThan(String value) {
            addCriterion("chart_version <", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLessThanOrEqualTo(String value) {
            addCriterion("chart_version <=", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLike(String value) {
            addCriterion("chart_version like", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotLike(String value) {
            addCriterion("chart_version not like", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionIn(List<String> values) {
            addCriterion("chart_version in", values, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotIn(List<String> values) {
            addCriterion("chart_version not in", values, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionBetween(String value1, String value2) {
            addCriterion("chart_version between", value1, value2, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotBetween(String value1, String value2) {
            addCriterion("chart_version not between", value1, value2, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIsNull() {
            addCriterion("brand_material_code is null");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIsNotNull() {
            addCriterion("brand_material_code is not null");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeEqualTo(String value) {
            addCriterion("brand_material_code =", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotEqualTo(String value) {
            addCriterion("brand_material_code <>", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeGreaterThan(String value) {
            addCriterion("brand_material_code >", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("brand_material_code >=", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLessThan(String value) {
            addCriterion("brand_material_code <", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("brand_material_code <=", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeLike(String value) {
            addCriterion("brand_material_code like", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotLike(String value) {
            addCriterion("brand_material_code not like", value, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeIn(List<String> values) {
            addCriterion("brand_material_code in", values, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotIn(List<String> values) {
            addCriterion("brand_material_code not in", values, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeBetween(String value1, String value2) {
            addCriterion("brand_material_code between", value1, value2, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andBrandMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("brand_material_code not between", value1, value2, "brandMaterialCode");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskIsNull() {
            addCriterion("or_spare_parts_mask is null");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskIsNotNull() {
            addCriterion("or_spare_parts_mask is not null");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskEqualTo(String value) {
            addCriterion("or_spare_parts_mask =", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNotEqualTo(String value) {
            addCriterion("or_spare_parts_mask <>", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskGreaterThan(String value) {
            addCriterion("or_spare_parts_mask >", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskGreaterThanOrEqualTo(String value) {
            addCriterion("or_spare_parts_mask >=", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskLessThan(String value) {
            addCriterion("or_spare_parts_mask <", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskLessThanOrEqualTo(String value) {
            addCriterion("or_spare_parts_mask <=", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskLike(String value) {
            addCriterion("or_spare_parts_mask like", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNotLike(String value) {
            addCriterion("or_spare_parts_mask not like", value, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskIn(List<String> values) {
            addCriterion("or_spare_parts_mask in", values, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNotIn(List<String> values) {
            addCriterion("or_spare_parts_mask not in", values, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskBetween(String value1, String value2) {
            addCriterion("or_spare_parts_mask between", value1, value2, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andOrSparePartsMaskNotBetween(String value1, String value2) {
            addCriterion("or_spare_parts_mask not between", value1, value2, "orSparePartsMask");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateIsNull() {
            addCriterion("requirement_creat_date is null");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateIsNotNull() {
            addCriterion("requirement_creat_date is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateEqualTo(Date value) {
            addCriterionForJDBCDate("requirement_creat_date =", value, "requirementCreatDate");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("requirement_creat_date <>", value, "requirementCreatDate");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateGreaterThan(Date value) {
            addCriterionForJDBCDate("requirement_creat_date >", value, "requirementCreatDate");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("requirement_creat_date >=", value, "requirementCreatDate");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateLessThan(Date value) {
            addCriterionForJDBCDate("requirement_creat_date <", value, "requirementCreatDate");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("requirement_creat_date <=", value, "requirementCreatDate");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateIn(List<Date> values) {
            addCriterionForJDBCDate("requirement_creat_date in", values, "requirementCreatDate");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("requirement_creat_date not in", values, "requirementCreatDate");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("requirement_creat_date between", value1, value2, "requirementCreatDate");
            return (Criteria) this;
        }

        public Criteria andRequirementCreatDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("requirement_creat_date not between", value1, value2, "requirementCreatDate");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeIsNull() {
            addCriterion("perchasing_leadtime is null");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeIsNotNull() {
            addCriterion("perchasing_leadtime is not null");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeEqualTo(Long value) {
            addCriterion("perchasing_leadtime =", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeNotEqualTo(Long value) {
            addCriterion("perchasing_leadtime <>", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeGreaterThan(Long value) {
            addCriterion("perchasing_leadtime >", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeGreaterThanOrEqualTo(Long value) {
            addCriterion("perchasing_leadtime >=", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeLessThan(Long value) {
            addCriterion("perchasing_leadtime <", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeLessThanOrEqualTo(Long value) {
            addCriterion("perchasing_leadtime <=", value, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeIn(List<Long> values) {
            addCriterion("perchasing_leadtime in", values, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeNotIn(List<Long> values) {
            addCriterion("perchasing_leadtime not in", values, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeBetween(Long value1, Long value2) {
            addCriterion("perchasing_leadtime between", value1, value2, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andPerchasingLeadtimeNotBetween(Long value1, Long value2) {
            addCriterion("perchasing_leadtime not between", value1, value2, "perchasingLeadtime");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityIsNull() {
            addCriterion("min_perchase_quantity is null");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityIsNotNull() {
            addCriterion("min_perchase_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityEqualTo(Long value) {
            addCriterion("min_perchase_quantity =", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityNotEqualTo(Long value) {
            addCriterion("min_perchase_quantity <>", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityGreaterThan(Long value) {
            addCriterion("min_perchase_quantity >", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("min_perchase_quantity >=", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityLessThan(Long value) {
            addCriterion("min_perchase_quantity <", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityLessThanOrEqualTo(Long value) {
            addCriterion("min_perchase_quantity <=", value, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityIn(List<Long> values) {
            addCriterion("min_perchase_quantity in", values, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityNotIn(List<Long> values) {
            addCriterion("min_perchase_quantity not in", values, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityBetween(Long value1, Long value2) {
            addCriterion("min_perchase_quantity between", value1, value2, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPerchaseQuantityNotBetween(Long value1, Long value2) {
            addCriterion("min_perchase_quantity not between", value1, value2, "minPerchaseQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityIsNull() {
            addCriterion("min_package_quantity is null");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityIsNotNull() {
            addCriterion("min_package_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityEqualTo(Long value) {
            addCriterion("min_package_quantity =", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityNotEqualTo(Long value) {
            addCriterion("min_package_quantity <>", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityGreaterThan(Long value) {
            addCriterion("min_package_quantity >", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("min_package_quantity >=", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityLessThan(Long value) {
            addCriterion("min_package_quantity <", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityLessThanOrEqualTo(Long value) {
            addCriterion("min_package_quantity <=", value, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityIn(List<Long> values) {
            addCriterion("min_package_quantity in", values, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityNotIn(List<Long> values) {
            addCriterion("min_package_quantity not in", values, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityBetween(Long value1, Long value2) {
            addCriterion("min_package_quantity between", value1, value2, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andMinPackageQuantityNotBetween(Long value1, Long value2) {
            addCriterion("min_package_quantity not between", value1, value2, "minPackageQuantity");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNull() {
            addCriterion("wbs_summary_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNotNull() {
            addCriterion("wbs_summary_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeEqualTo(String value) {
            addCriterion("wbs_summary_code =", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotEqualTo(String value) {
            addCriterion("wbs_summary_code <>", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThan(String value) {
            addCriterion("wbs_summary_code >", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code >=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThan(String value) {
            addCriterion("wbs_summary_code <", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code <=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLike(String value) {
            addCriterion("wbs_summary_code like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotLike(String value) {
            addCriterion("wbs_summary_code not like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIn(List<String> values) {
            addCriterion("wbs_summary_code in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotIn(List<String> values) {
            addCriterion("wbs_summary_code not in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeBetween(String value1, String value2) {
            addCriterion("wbs_summary_code between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_summary_code not between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsLayerIsNull() {
            addCriterion("wbs_layer is null");
            return (Criteria) this;
        }

        public Criteria andWbsLayerIsNotNull() {
            addCriterion("wbs_layer is not null");
            return (Criteria) this;
        }

        public Criteria andWbsLayerEqualTo(String value) {
            addCriterion("wbs_layer =", value, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerNotEqualTo(String value) {
            addCriterion("wbs_layer <>", value, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerGreaterThan(String value) {
            addCriterion("wbs_layer >", value, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_layer >=", value, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerLessThan(String value) {
            addCriterion("wbs_layer <", value, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerLessThanOrEqualTo(String value) {
            addCriterion("wbs_layer <=", value, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerLike(String value) {
            addCriterion("wbs_layer like", value, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerNotLike(String value) {
            addCriterion("wbs_layer not like", value, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerIn(List<String> values) {
            addCriterion("wbs_layer in", values, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerNotIn(List<String> values) {
            addCriterion("wbs_layer not in", values, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerBetween(String value1, String value2) {
            addCriterion("wbs_layer between", value1, value2, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLayerNotBetween(String value1, String value2) {
            addCriterion("wbs_layer not between", value1, value2, "wbsLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerIsNull() {
            addCriterion("wbs_last_layer is null");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerIsNotNull() {
            addCriterion("wbs_last_layer is not null");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerEqualTo(Boolean value) {
            addCriterion("wbs_last_layer =", value, "wbsLastLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerNotEqualTo(Boolean value) {
            addCriterion("wbs_last_layer <>", value, "wbsLastLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerGreaterThan(Boolean value) {
            addCriterion("wbs_last_layer >", value, "wbsLastLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerGreaterThanOrEqualTo(Boolean value) {
            addCriterion("wbs_last_layer >=", value, "wbsLastLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerLessThan(Boolean value) {
            addCriterion("wbs_last_layer <", value, "wbsLastLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerLessThanOrEqualTo(Boolean value) {
            addCriterion("wbs_last_layer <=", value, "wbsLastLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerIn(List<Boolean> values) {
            addCriterion("wbs_last_layer in", values, "wbsLastLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerNotIn(List<Boolean> values) {
            addCriterion("wbs_last_layer not in", values, "wbsLastLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerBetween(Boolean value1, Boolean value2) {
            addCriterion("wbs_last_layer between", value1, value2, "wbsLastLayer");
            return (Criteria) this;
        }

        public Criteria andWbsLastLayerNotBetween(Boolean value1, Boolean value2) {
            addCriterion("wbs_last_layer not between", value1, value2, "wbsLastLayer");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionIsNull() {
            addCriterion("wbs_description is null");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionIsNotNull() {
            addCriterion("wbs_description is not null");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionEqualTo(String value) {
            addCriterion("wbs_description =", value, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionNotEqualTo(String value) {
            addCriterion("wbs_description <>", value, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionGreaterThan(String value) {
            addCriterion("wbs_description >", value, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_description >=", value, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionLessThan(String value) {
            addCriterion("wbs_description <", value, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionLessThanOrEqualTo(String value) {
            addCriterion("wbs_description <=", value, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionLike(String value) {
            addCriterion("wbs_description like", value, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionNotLike(String value) {
            addCriterion("wbs_description not like", value, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionIn(List<String> values) {
            addCriterion("wbs_description in", values, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionNotIn(List<String> values) {
            addCriterion("wbs_description not in", values, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionBetween(String value1, String value2) {
            addCriterion("wbs_description between", value1, value2, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsDescriptionNotBetween(String value1, String value2) {
            addCriterion("wbs_description not between", value1, value2, "wbsDescription");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagIsNull() {
            addCriterion("wbs_confirm_flag is null");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagIsNotNull() {
            addCriterion("wbs_confirm_flag is not null");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagEqualTo(Boolean value) {
            addCriterion("wbs_confirm_flag =", value, "wbsConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagNotEqualTo(Boolean value) {
            addCriterion("wbs_confirm_flag <>", value, "wbsConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagGreaterThan(Boolean value) {
            addCriterion("wbs_confirm_flag >", value, "wbsConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("wbs_confirm_flag >=", value, "wbsConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagLessThan(Boolean value) {
            addCriterion("wbs_confirm_flag <", value, "wbsConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("wbs_confirm_flag <=", value, "wbsConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagIn(List<Boolean> values) {
            addCriterion("wbs_confirm_flag in", values, "wbsConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagNotIn(List<Boolean> values) {
            addCriterion("wbs_confirm_flag not in", values, "wbsConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("wbs_confirm_flag between", value1, value2, "wbsConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andWbsConfirmFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("wbs_confirm_flag not between", value1, value2, "wbsConfirmFlag");
            return (Criteria) this;
        }

        public Criteria andDispatchIsIsNull() {
            addCriterion("dispatch_is is null");
            return (Criteria) this;
        }

        public Criteria andDispatchIsIsNotNull() {
            addCriterion("dispatch_is is not null");
            return (Criteria) this;
        }

        public Criteria andDispatchIsEqualTo(Boolean value) {
            addCriterion("dispatch_is =", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsNotEqualTo(Boolean value) {
            addCriterion("dispatch_is <>", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsGreaterThan(Boolean value) {
            addCriterion("dispatch_is >", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("dispatch_is >=", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsLessThan(Boolean value) {
            addCriterion("dispatch_is <", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsLessThanOrEqualTo(Boolean value) {
            addCriterion("dispatch_is <=", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsIn(List<Boolean> values) {
            addCriterion("dispatch_is in", values, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsNotIn(List<Boolean> values) {
            addCriterion("dispatch_is not in", values, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsBetween(Boolean value1, Boolean value2) {
            addCriterion("dispatch_is between", value1, value2, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("dispatch_is not between", value1, value2, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andExtIsIsNull() {
            addCriterion("ext_is is null");
            return (Criteria) this;
        }

        public Criteria andExtIsIsNotNull() {
            addCriterion("ext_is is not null");
            return (Criteria) this;
        }

        public Criteria andExtIsEqualTo(Boolean value) {
            addCriterion("ext_is =", value, "extIs");
            return (Criteria) this;
        }

        public Criteria andExtIsNotEqualTo(Boolean value) {
            addCriterion("ext_is <>", value, "extIs");
            return (Criteria) this;
        }

        public Criteria andExtIsGreaterThan(Boolean value) {
            addCriterion("ext_is >", value, "extIs");
            return (Criteria) this;
        }

        public Criteria andExtIsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("ext_is >=", value, "extIs");
            return (Criteria) this;
        }

        public Criteria andExtIsLessThan(Boolean value) {
            addCriterion("ext_is <", value, "extIs");
            return (Criteria) this;
        }

        public Criteria andExtIsLessThanOrEqualTo(Boolean value) {
            addCriterion("ext_is <=", value, "extIs");
            return (Criteria) this;
        }

        public Criteria andExtIsIn(List<Boolean> values) {
            addCriterion("ext_is in", values, "extIs");
            return (Criteria) this;
        }

        public Criteria andExtIsNotIn(List<Boolean> values) {
            addCriterion("ext_is not in", values, "extIs");
            return (Criteria) this;
        }

        public Criteria andExtIsBetween(Boolean value1, Boolean value2) {
            addCriterion("ext_is between", value1, value2, "extIs");
            return (Criteria) this;
        }

        public Criteria andExtIsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("ext_is not between", value1, value2, "extIs");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNull() {
            addCriterion("activity_code is null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNotNull() {
            addCriterion("activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualTo(String value) {
            addCriterion("activity_code =", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualTo(String value) {
            addCriterion("activity_code <>", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThan(String value) {
            addCriterion("activity_code >", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_code >=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThan(String value) {
            addCriterion("activity_code <", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("activity_code <=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLike(String value) {
            addCriterion("activity_code like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotLike(String value) {
            addCriterion("activity_code not like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIn(List<String> values) {
            addCriterion("activity_code in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotIn(List<String> values) {
            addCriterion("activity_code not in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeBetween(String value1, String value2) {
            addCriterion("activity_code between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotBetween(String value1, String value2) {
            addCriterion("activity_code not between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeIsNull() {
            addCriterion("project_budget_type is null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeIsNotNull() {
            addCriterion("project_budget_type is not null");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeEqualTo(String value) {
            addCriterion("project_budget_type =", value, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeNotEqualTo(String value) {
            addCriterion("project_budget_type <>", value, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeGreaterThan(String value) {
            addCriterion("project_budget_type >", value, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeGreaterThanOrEqualTo(String value) {
            addCriterion("project_budget_type >=", value, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeLessThan(String value) {
            addCriterion("project_budget_type <", value, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeLessThanOrEqualTo(String value) {
            addCriterion("project_budget_type <=", value, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeLike(String value) {
            addCriterion("project_budget_type like", value, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeNotLike(String value) {
            addCriterion("project_budget_type not like", value, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeIn(List<String> values) {
            addCriterion("project_budget_type in", values, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeNotIn(List<String> values) {
            addCriterion("project_budget_type not in", values, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeBetween(String value1, String value2) {
            addCriterion("project_budget_type between", value1, value2, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andProjectBudgetTypeNotBetween(String value1, String value2) {
            addCriterion("project_budget_type not between", value1, value2, "projectBudgetType");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerIsNull() {
            addCriterion("plan_designer is null");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerIsNotNull() {
            addCriterion("plan_designer is not null");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerEqualTo(String value) {
            addCriterion("plan_designer =", value, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNotEqualTo(String value) {
            addCriterion("plan_designer <>", value, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerGreaterThan(String value) {
            addCriterion("plan_designer >", value, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerGreaterThanOrEqualTo(String value) {
            addCriterion("plan_designer >=", value, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerLessThan(String value) {
            addCriterion("plan_designer <", value, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerLessThanOrEqualTo(String value) {
            addCriterion("plan_designer <=", value, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerLike(String value) {
            addCriterion("plan_designer like", value, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNotLike(String value) {
            addCriterion("plan_designer not like", value, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerIn(List<String> values) {
            addCriterion("plan_designer in", values, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNotIn(List<String> values) {
            addCriterion("plan_designer not in", values, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerBetween(String value1, String value2) {
            addCriterion("plan_designer between", value1, value2, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andPlanDesignerNotBetween(String value1, String value2) {
            addCriterion("plan_designer not between", value1, value2, "planDesigner");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIsNull() {
            addCriterion("design_release_lot_number is null");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIsNotNull() {
            addCriterion("design_release_lot_number is not null");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberEqualTo(String value) {
            addCriterion("design_release_lot_number =", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotEqualTo(String value) {
            addCriterion("design_release_lot_number <>", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberGreaterThan(String value) {
            addCriterion("design_release_lot_number >", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberGreaterThanOrEqualTo(String value) {
            addCriterion("design_release_lot_number >=", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLessThan(String value) {
            addCriterion("design_release_lot_number <", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLessThanOrEqualTo(String value) {
            addCriterion("design_release_lot_number <=", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLike(String value) {
            addCriterion("design_release_lot_number like", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotLike(String value) {
            addCriterion("design_release_lot_number not like", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIn(List<String> values) {
            addCriterion("design_release_lot_number in", values, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotIn(List<String> values) {
            addCriterion("design_release_lot_number not in", values, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberBetween(String value1, String value2) {
            addCriterion("design_release_lot_number between", value1, value2, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotBetween(String value1, String value2) {
            addCriterion("design_release_lot_number not between", value1, value2, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIsNull() {
            addCriterion("requirement_type is null");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIsNotNull() {
            addCriterion("requirement_type is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeEqualTo(Integer value) {
            addCriterion("requirement_type =", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotEqualTo(Integer value) {
            addCriterion("requirement_type <>", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeGreaterThan(Integer value) {
            addCriterion("requirement_type >", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("requirement_type >=", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeLessThan(Integer value) {
            addCriterion("requirement_type <", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeLessThanOrEqualTo(Integer value) {
            addCriterion("requirement_type <=", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIn(List<Integer> values) {
            addCriterion("requirement_type in", values, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotIn(List<Integer> values) {
            addCriterion("requirement_type not in", values, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeBetween(Integer value1, Integer value2) {
            addCriterion("requirement_type between", value1, value2, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("requirement_type not between", value1, value2, "requirementType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}