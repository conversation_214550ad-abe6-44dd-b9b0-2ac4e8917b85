package com.midea.pam.common.ctc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel(value = "MilepostDesignPlanDto", description = "里程碑详细设计方案查询信息")
public class MilepostDesignPlanDto {

    @ApiModelProperty("库存组织")
    private Long storageId;

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目Code")
    private String projectCode;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目状态")
    private Integer projectStatus;

    @ApiModelProperty("里程碑id")
    private Long milepostId;

    @ApiModelProperty("里程碑dto")
    private ProjectMilepostDto milepostDto;

    @ApiModelProperty("交付信息")
    private List<MilepostDesignPlanDetailDto> deliveryInformationDtos;

    @ApiModelProperty("设计信息")
    private List<MilepostDesignPlanDetailDto> designPlanDetailDtos;

    @ApiModelProperty("提交记录列表")
    private List<MilepostDesignPlanSubmitRecordDto> submitRecordDtos;

    @ApiModelProperty("确认记录列表")
    private List<MilepostDesignPlanConfirmRecordDto> confirmRecordDtos;

    @ApiModelProperty("提前采购记录列表")
    private List<MilepostDesignPlanPurchaseRecordDTO> purchaseRecordDTOS;

    @ApiModelProperty("变更记录列表")
    private List<MilepostDesignPlanChangeRecordDto> changeRecordDtos;

    @ApiModelProperty("提交记录")
    private MilepostDesignPlanSubmitRecordDto submitRecordDto;

    @ApiModelProperty("附件记录")
    private List<CtcAttachmentDto> attachmentDtos;

    @ApiModelProperty("详细设计单据列表")
    private List<ProjectWbsReceiptsDto> receiptsDtos;

    @ApiModelProperty("所处里程碑阶段")
    private String theLastestMilepostName;

    @ApiModelProperty("上传路径")
    private String uploadPath;

    @ApiModelProperty("提前采购流程的附件")
    private String attachId;

    private Long markId;

    private List<String> erpCodeList;

    @ApiModelProperty("详细设计单据")
    private ProjectWbsReceiptsDto receiptsDto;

    @ApiModelProperty("表单模板id")
    private String formTemplateId;

    @ApiModelProperty("详设变更审批中的模组的详设id列表")
    private List<Long> mdpChangeCheckingWhetherModelDesignPlanDetailIdList;

    @ApiModelProperty("详设发布审批中的模组的详设id列表")
    private List<Long> submitCheckingWhetherModelDesignPlanDetailIdList;

    @ApiModelProperty("页面完整的url")
    private String pageUrl;

    @ApiModelProperty("索引号")
    private String indexNum;
}