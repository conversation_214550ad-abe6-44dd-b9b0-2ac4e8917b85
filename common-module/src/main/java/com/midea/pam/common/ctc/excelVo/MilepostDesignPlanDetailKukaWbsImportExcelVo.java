package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class MilepostDesignPlanDetailKukaWbsImportExcelVo {
    @Excel(name = "序号")
    @ExcelField(title = "序号", order = 1)
    private String serialNumber;

    @Excel(name = "层级说明")
    @ExcelField(title = "层级说明", order = 2)
    private String parentNum;

    @Excel(name = "PAM物料编码")
    @ExcelField(title = "PAM物料编码", order = 2)
    private String pamCode;

    @Excel(name = "ERP物料编码")
    @ExcelField(title = "ERP物料编码", order = 3)
    private String erpCode;

    @Excel(name = "WBS")
    private String wbsSummaryCode;

    @Excel(name = "WBS层级")
    private String wbsLayer;

    @Excel(name = "物料类型")
    @ExcelField(title = "物料类型", order = 4)
    private String materialCategory;

    @Excel(name = "物料大类")
    @ExcelField(title = "物料大类", order = 5)
    private String materialClassification;

    @Excel(name = "物料中类")
    @ExcelField(title = "物料中类", order = 6)
    private String codingMiddleClass;

    @Excel(name = "物料小类")
    @ExcelField(title = "物料小类", order = 7)
    private String materielType;

    @Excel(name = "品牌")
    @ExcelField(title = "品牌", order = 8)
    private String brand;

    @Excel(name = "名称")
    @ExcelField(title = "名称", order = 9)
    private String name;

    @Excel(name = "型号/规格")
    @ExcelField(title = "型号/规格", order = 10)
    private String model;

    @Excel(name = "数量")
    @ExcelField(title = "数量", order = 11)
    private String numberStr;

    @Excel(name = "单位")
    @ExcelField(title = "单位", order = 12)
    private String unit;

    @Excel(name = "图号")
    @ExcelField(title = "图号", order = 13)
    private String figureNumber;

//    @Excel(name = "图纸版本号")
//    @ExcelField(title = "图纸版本号", order = 14)
    private String chartVersion;

//    @Excel(name = "加工分类")
//    @ExcelField(title = "加工分类", order = 15)
    private String machiningPartType;

//    @Excel(name = "材质")
//    @ExcelField(title = "材质", order = 16)
    private String material;

//    @Excel(name = "单件重量(Kg)")
//    @ExcelField(title = "单件重量(Kg)", order = 17)
    private String unitWeightStr;

//    @Excel(name = "表面处理")
//    @ExcelField(title = "表面处理", order = 18)
    private String materialProcessing;

    @Excel(name = "品牌商物料编码")
    @ExcelField(title = "品牌商物料编码", order = 19)
    private String brandMaterialCode;

//    @Excel(name = "备件标识")
//    @ExcelField(title = "备件标识", order = 20)
    private String orSparePartsMask;

//    @Excel(name = "采购提前期")
//    @ExcelField(title = "采购提前期", order = 21)
    private String perchasingLeadtimeStr;

//    @Excel(name = "最小采购量")
//    @ExcelField(title = "最小采购量", order = 21)
    private String minPerchaseQuantityStr;

//    @Excel(name = "最小包装量")
//    @ExcelField(title = "最小包装量", order = 22)
    private String minPackageQuantityStr;

    @Excel(name = "物料到货日期")
    @ExcelField(title = "物料到货日期", order = 23)
    private String deliveryTimeStr;

    @Excel(name = "是否外包")
    @ExcelField(title = "是否外包", order = 24)
    private String extIsStr;

    @Excel(name = "是否急件")
    @ExcelField(title = "是否急件", order = 25)
    private String dispatchIsStr;

    @Excel(name = "需求类型")  //需求类型：3：安装分包，4：调试、测量分包，5：设计分包，6：标准设备，7：定制设备整包，8：设备备件，9：项目整包，10：机加整包，11：机加散件，12：MRO，13：标准件
    @ExcelField(title = "需求类型", order = 26)
    private String requirementTypeStr;

//    @Excel(name = "设计人员")
//    @ExcelField(title = "设计人员", order = 27)
    private String planDesigner;

    @Excel(name = "备注")
    @ExcelField(title = "备注", order = 28)
    private String description;

    @Excel(name = "索引号")
    @ExcelField(title = "索引号", order = 29)
    private String indexNum;

    private String activityCode;

    private String projectBudgetType;

    private String designReleaseLotNumber;

    private Long maximumDrawVersionNum;

    @Excel(name = "结果")
    private String validResult;

    private Date deliveryTime;

    private List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo;

}
