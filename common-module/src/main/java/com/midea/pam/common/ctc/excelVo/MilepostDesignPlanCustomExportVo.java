package com.midea.pam.common.ctc.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 里程碑详细设计方案自定义导出Excel实体
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Getter
@Setter
public class MilepostDesignPlanCustomExportVo {

    @Excel(name = "序号")
    private String orderNo;

    @Excel(name = "集成外包", replace = {"是_true", "否_false", "_null"})
    private Boolean ext;

    @Excel(name = "物料描述")
    private String materielDescr;

    @Excel(name = "PAM物料编码")
    private String pamCode;

    @Excel(name = "ERP物料编码")
    private String erpCode;

    @Excel(name = "单位")
    private String unit;

    @Excel(name = "物料类型")
    private String materialCategory;

    @Excel(name = "采购需求总数")
    private String totalNum_dt;

    @Excel(name = "单套数量")
    private String number_dt;

    @Excel(name = "已生成采购需求数量")
    private String requirementNum_dt;

    @Excel(name = "设计成本")
    private String designCost_dt;

    @Excel(name = "设计小计")
    private String designTotal_dt;

    @Excel(name = "预算单价")
    private String budgetUnitPrice_dt;

    @Excel(name = "预算小计")
    private String budgetSubtotal_dt;

    @Excel(name = "交货日期", format = "yyyy-MM-dd")
    private Date deliveryTime;

    @Excel(name = "设计成本是否来源估价", replace = {"是_true", "否_false", "_null"})
    private Boolean itemCostIsNull;

    @Excel(name = "名称")
    private String name;

    @Excel(name = "型号/规格/图号")
    private String model;

    @Excel(name = "品牌")
    private String brand;

    @Excel(name = "物料大类")
    private String materialClassification;

    @Excel(name = "物料中类")
    private String codingMiddleClass;

    @Excel(name = "物料小类")
    private String materielType;

    @Excel(name = "加工件分类")
    private String machiningPartType;

    @Excel(name = "材质")
    private String material;

    @Excel(name = "图号")
    private String figureNumber;

    @Excel(name = "图纸版本号")
    private String chartVersion;

    @Excel(name = "备件标识")
    private String orSparePartsMask;

    @Excel(name = "品牌商物料编码")
    private String brandMaterialCode;

    @Excel(name = "单位重量(Kg)")
    private String unitWeight_dt;

    @Excel(name = "材质处理")
    private String materialProcessing;

    @Excel(name = "来源")
    private String source;

    // 原始数值字段，用于数据转换
    private BigDecimal totalNum;
    private BigDecimal number;
    private BigDecimal requirementNum;
    private BigDecimal designCost;
    private BigDecimal designTotal;
    private BigDecimal budgetUnitPrice;
    private BigDecimal budgetSubtotal;
    private BigDecimal unitWeight;

    // 数据转换方法
    public String getTotalNum_dt() {
        if (this.totalNum != null) {
            return this.totalNum.stripTrailingZeros().toPlainString();
        }
        return this.totalNum_dt;
    }

    public String getNumber_dt() {
        if (this.number != null) {
            return this.number.stripTrailingZeros().toPlainString();
        }
        return this.number_dt;
    }

    public String getRequirementNum_dt() {
        if (this.requirementNum != null) {
            return this.requirementNum.stripTrailingZeros().toPlainString();
        }
        return this.requirementNum_dt;
    }

    public String getDesignCost_dt() {
        if (this.designCost != null) {
            return this.designCost.stripTrailingZeros().toPlainString();
        }
        return this.designCost_dt;
    }

    public String getDesignTotal_dt() {
        if (this.designTotal != null) {
            return this.designTotal.stripTrailingZeros().toPlainString();
        }
        return this.designTotal_dt;
    }

    public String getBudgetUnitPrice_dt() {
        if (this.budgetUnitPrice != null) {
            return this.budgetUnitPrice.stripTrailingZeros().toPlainString();
        }
        return this.budgetUnitPrice_dt;
    }

    public String getBudgetSubtotal_dt() {
        if (this.budgetSubtotal != null) {
            return this.budgetSubtotal.stripTrailingZeros().toPlainString();
        }
        return this.budgetSubtotal_dt;
    }

    public String getUnitWeight_dt() {
        if (this.unitWeight != null) {
            return this.unitWeight.stripTrailingZeros().toPlainString();
        }
        return this.unitWeight_dt;
    }
}
