package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "详细设计单据")
public class ProjectWbsReceipts extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "详细设计单据编号")
    private String requirementCode;

    @ApiModelProperty(value = "单据状态 0-草稿、1-待处理、2-审核中、3-驳回、4-生效、5-作废、6-变更中")
    private Integer requirementStatus;

    @ApiModelProperty(value = "单据类型 1-详细设计发布、2-详细设计变更、3-需求发布")
    private Integer requirementType;

    @ApiModelProperty(value = "确认方式 1-部分确认、2-进度确认")
    private Integer confirmMode;

    @ApiModelProperty(value = "处理人id")
    private Long handleBy;

    @ApiModelProperty(value = "处理人名字")
    private String handleName;

    @ApiModelProperty(value = "提交原因")
    private String reason;

    @ApiModelProperty(value = "处理日期")
    private Date handleAt;

    @ApiModelProperty(value = "设计发布批次号")
    private String designReleaseLotNumber;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "制单人id")
    private Long producerId;

    @ApiModelProperty(value = "制单人名字")
    private String producerName;

    @ApiModelProperty(value = "流程名称")
    private String processName;

    @ApiModelProperty(value = "是否立项时确认")
    private Boolean projectSubmit;

    @ApiModelProperty(value = "使用单位id")
    private Long unitId;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "原因备注")
    private String rensonRemark;

    @ApiModelProperty(value = "提交原因")
    private String submitReason;

    @ApiModelProperty(value = "关联单据id（需求发布必定关联详细设计发布）")
    private Long relReceiptsId;

    @ApiModelProperty(value = "是否为初始化导入: 0-否 1-是")
    private Boolean init;

    @ApiModelProperty(value = "初始化序列")
    private String initSequence;

    @ApiModelProperty(value = "采购员id")
    private Long buyerId;

    @ApiModelProperty(value = "采购员名称")
    private String buyerName;

    @ApiModelProperty(value = "页面类型：0-通用页面，1-新页面")
    private Integer webType;

    @ApiModelProperty(value = "批量上传路径")
    private String batchUploadPaths;

    @ApiModelProperty(value = "批量导入上传id")
    private String batchUploadPathIds;

    private static final long serialVersionUID = 1L;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode == null ? null : requirementCode.trim();
    }

    public Integer getRequirementStatus() {
        return requirementStatus;
    }

    public void setRequirementStatus(Integer requirementStatus) {
        this.requirementStatus = requirementStatus;
    }

    public Integer getRequirementType() {
        return requirementType;
    }

    public void setRequirementType(Integer requirementType) {
        this.requirementType = requirementType;
    }

    public Integer getConfirmMode() {
        return confirmMode;
    }

    public void setConfirmMode(Integer confirmMode) {
        this.confirmMode = confirmMode;
    }

    public Long getHandleBy() {
        return handleBy;
    }

    public void setHandleBy(Long handleBy) {
        this.handleBy = handleBy;
    }

    public String getHandleName() {
        return handleName;
    }

    public void setHandleName(String handleName) {
        this.handleName = handleName == null ? null : handleName.trim();
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public Date getHandleAt() {
        return handleAt;
    }

    public void setHandleAt(Date handleAt) {
        this.handleAt = handleAt;
    }

    public String getDesignReleaseLotNumber() {
        return designReleaseLotNumber;
    }

    public void setDesignReleaseLotNumber(String designReleaseLotNumber) {
        this.designReleaseLotNumber = designReleaseLotNumber == null ? null : designReleaseLotNumber.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Long getProducerId() {
        return producerId;
    }

    public void setProducerId(Long producerId) {
        this.producerId = producerId;
    }

    public String getProducerName() {
        return producerName;
    }

    public void setProducerName(String producerName) {
        this.producerName = producerName == null ? null : producerName.trim();
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName == null ? null : processName.trim();
    }

    public Boolean getProjectSubmit() {
        return projectSubmit;
    }

    public void setProjectSubmit(Boolean projectSubmit) {
        this.projectSubmit = projectSubmit;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getRensonRemark() {
        return rensonRemark;
    }

    public void setRensonRemark(String rensonRemark) {
        this.rensonRemark = rensonRemark == null ? null : rensonRemark.trim();
    }

    public String getSubmitReason() {
        return submitReason;
    }

    public void setSubmitReason(String submitReason) {
        this.submitReason = submitReason == null ? null : submitReason.trim();
    }

    public Long getRelReceiptsId() {
        return relReceiptsId;
    }

    public void setRelReceiptsId(Long relReceiptsId) {
        this.relReceiptsId = relReceiptsId;
    }

    public Boolean getInit() {
        return init;
    }

    public void setInit(Boolean init) {
        this.init = init;
    }

    public String getInitSequence() {
        return initSequence;
    }

    public void setInitSequence(String initSequence) {
        this.initSequence = initSequence == null ? null : initSequence.trim();
    }

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId = buyerId;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    public Integer getWebType() {
        return webType;
    }

    public void setWebType(Integer webType) {
        this.webType = webType;
    }

    public String getBatchUploadPaths() {
        return batchUploadPaths;
    }

    public void setBatchUploadPaths(String batchUploadPaths) {
        this.batchUploadPaths = batchUploadPaths == null ? null : batchUploadPaths.trim();
    }

    public String getBatchUploadPathIds() {
        return batchUploadPathIds;
    }

    public void setBatchUploadPathIds(String batchUploadPathIds) {
        this.batchUploadPathIds = batchUploadPathIds == null ? null : batchUploadPathIds.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", requirementCode=").append(requirementCode);
        sb.append(", requirementStatus=").append(requirementStatus);
        sb.append(", requirementType=").append(requirementType);
        sb.append(", confirmMode=").append(confirmMode);
        sb.append(", handleBy=").append(handleBy);
        sb.append(", handleName=").append(handleName);
        sb.append(", reason=").append(reason);
        sb.append(", handleAt=").append(handleAt);
        sb.append(", designReleaseLotNumber=").append(designReleaseLotNumber);
        sb.append(", remark=").append(remark);
        sb.append(", producerId=").append(producerId);
        sb.append(", producerName=").append(producerName);
        sb.append(", processName=").append(processName);
        sb.append(", projectSubmit=").append(projectSubmit);
        sb.append(", unitId=").append(unitId);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", version=").append(version);
        sb.append(", rensonRemark=").append(rensonRemark);
        sb.append(", submitReason=").append(submitReason);
        sb.append(", relReceiptsId=").append(relReceiptsId);
        sb.append(", init=").append(init);
        sb.append(", initSequence=").append(initSequence);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", webType=").append(webType);
        sb.append(", batchUploadPaths=").append(batchUploadPaths);
        sb.append(", batchUploadPathIds=").append(batchUploadPathIds);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}