package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.MilepostTemplateStage;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBudgetTarget;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravel;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectProduct;
import com.midea.pam.common.ctc.entity.ProjectProgressPredict;
import com.midea.pam.common.ctc.entity.ProjectProgressPredictChangeHistory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 立项数据dto.
 * chengzy
 */
@Getter
@Setter
@ApiModel(value = "ProjectDto", description = "立项数据")
public class ProjectDto extends Project {
    /**
     * 头表id
     * 传了就变更变更操作
     */
    @ApiModelProperty(value = "头表id")
    private Long headerId;

    @ApiModelProperty("项目金额（不含税）-本位币")
    private BigDecimal standardAmount;

    private List<Long> projectIds;

    @ApiModelProperty(value = "项目类型")
    private ProjectTypeDto projectType;

    @ApiModelProperty(value = "产品名")
    private String productName;

    @ApiModelProperty(value = "排序")
    private String orderParam;

    @ApiModelProperty(value = "项目变更原因")
    private String reason;

    @ApiModelProperty(value = "项目变更类型")
    private String reasonType;

    private String ouName;

    private String ouCode;

    /**
     * 财务人员mip.
     */
    private String financialMip;
    /**
     * 财务人员名称.
     */
    private String financialName;

    /**
     * 项目经理mip
     */
    private String managerMip;

    /**
     * 质保项目项目经理mip
     */
    private String transferManagerMip;

    /**
     * 质保项目项目经理id
     */
    private Long transferManager;

    /**
     * 项目经理名称
     */
    private String managerName;
    /**
     * 业务分类
     */
    private String unitName;
    /**
     * 项目单价类型名称.
     */
    private String priceTypeName;
    /**
     * 项目类型名称.
     */
    private String typeName;
    /**
     * 事业部
     */
    private String departmentName;

    private List<String> departmentNameStr;

    /**
     * 预算部门名称
     */
    private String budgetDepName;

    /**
     * 预算部门code
     */
    private String budgetDepCode;

    /**
     * 预算部门Ems ID
     */
    private String emsDepId;

    // 目标成本变更原因大类
    private String reasonClassification;

    // 是否同步变更目标成本(1是/0否)
    private Boolean budgetTargetFlag;

    /**
     * 预算树头 EMS id
     */
    private String emsHeaderId;

    /**
     * 是否重置里程碑
     */
    private Boolean projectTypeIsChange;

    /**
     * 费用预算.
     */
    private List<ProjectBudgetFeeDto> fees;

    /**
     * 人力预算.
     */
    private List<ProjectBudgetHumanDto> humans;

    /**
     * 物料预算.
     */
    private List<ProjectBudgetMaterialDto> materials;

    /**
     * 差旅预算.
     */
    private List<ProjectBudgetTravel> travels;

    /**
     * 资产预算.
     */
    private List<ProjectBudgetAssetDto> assets;

    /**
     * 项目成员.
     */
    private List<ProjectMemberDto> members;

    /**
     * 当前结转节点名称(里程碑名称或月度名称)
     */
    private String projectMilepostName;

    /**
     * 辅里程碑.
     */
    private List<ProjectMilepostDto> helpMileposts;

    /**
     * 主里程碑.
     */
    private List<ProjectMilepostDto> mainMileposts;

    @ApiModelProperty(value = "变更后的目标成本")
    private ProjectBudgetTargetDTO budgetTargetDTO;

    @ApiModelProperty(value = "预算变更信息")
    private ProjectBudgetChangeDTO budgetChangeDTO;

    /**
     * 待结转的辅里程碑.
     */
    private List<ProjectMilepostDto> waitForCarryoverHelpMileposts;

    /**
     * 待结转的主里程碑.
     */
    private List<ProjectMilepostDto> waitForCarryoverMainMileposts;

    /**
     * 待结转的轨迹日期区间开始.
     */
    private Date waitForCarryoverCollectionDateStart;

    /**
     * 待结转的轨迹日期区间结束.
     */
    private Date waitForCarryoverCollectionDateEnd;

    /**
     * 业务分类一级部门ID.
     */
    private Long topUnitId;

    /**
     * 业务分类一级部门name.
     */
    private String topUnitName;

    /**
     * 项目产品.
     */
    private ProjectProduct product;

    /**
     * 项目目标成本
     */
    private ProjectBudgetTarget projectBudgetTarget;

    /**
     * 主成本方法名称.
     */
    private String costMethodMainName;
    /**
     * 主成本方法
     */
    private String costMethodMain;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 关联商机集合
     */
    private List<ProjectBusinessRsDto> projectBusinessRsDtoList;

    /**
     * 关联合同集合
     */
    private List<ProjectContractRsDto> projectContractRsDtoList;

    /**
     * 关联资产集合
     */
    private List<ProjectAssetRsDto> projectAssetRsDtoList;

    /**
     * 关联采购合同集合
     */
    private List<PurchaseContractRsDto> purchaseContractRsDtoList;
    /**
     * 主合同销售经理id
     */
    private Long parentContractSalesManagerId;
    /**
     * 合同销售经理id
     */
    private Long contractSalesManagerId;
    /**
     * 主合同销售经理名称
     */
    private String parentContractSalesManagerName;
    /**
     * 合同销售经理名称
     */
    private String contractSalesManagerName;

    /**
     * 虚拟部门
     */
    private List<Long> unitIds;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 填报待审工时数
     */
    private Long approveCount;

    /**
     * 修改待审工时数
     */
    private Long updateCount;

    /**
     * 收入成本计划.
     */
    private ProjectProfitDto profit;

    /**
     * 详细设计提交记录
     */
    private MilepostDesignPlanDto milepostDesignPlanSubmitRecord;

    /**
     * 工时填报限制时间起点
     */
    private Date limitDate;
    /**
     * 工时填报上限
     */
    private Integer limitHour;

    /**
     * 附件记录
     */
    private List<CtcAttachmentDto> attachmentDtos;

    /**
     * 成本收入计划类型（1表示saas服务，2表示非saas服务，月度确认类型，3表示其他服务）
     */
    private String incomeCostPlanType;


    /**
     * 项目经理ID
     */
    private Long managerId;

    /**
     * 项目类型ID
     */
    private Long projectTypeId;

    //是否查询我的 true 是,false 否
    private Boolean isMe;

    //是否查询列表 true 是,false 否
    private Boolean isList;

    /**
     * 项目列表 合同详情链接是否可点击 true：是，false:否
     */
    private Boolean contractIsView;

    /**
     * 成本收入计划类型（1表示saas服务，2表示非saas服务，月度确认类型，3表示其他服务）
     */
    private List<ProjectIncomeCostPlan> incomeCostPlans;

    @ApiModelProperty("当前里程碑id")
    private Long currentMilepostId;

    @ApiModelProperty("最后一个里程碑id")
    private Long lastMilepostId;

    /**
     * 当前里程碑名称
     */
    private String currentMilepostName;

    /**
     * 当前里程碑负责人
     */
    private Long currentMilepostResponsible;

    /**
     * 当前里程碑负责人名称
     */
    private String currentMilepostResponsibleName;

    /**
     * 当前里程碑结束时间
     */
    private Date startTime;
    private Date endTime;

    /**
     * 立项审批通过日期
     */
    private Date approvalDateStart;
    private Date approvalDateEnd;

    /**
     * 项目类型
     */
    private List<Long> types;

    private String typesStr;

    /**
     * 项目属性
     */
    private List<Integer> priceTypes;

    private String priceTypesStr;

    /**
     * 项目属性
     */
    private List<Integer> statuses;

    private String statusesStr;

    /**
     * 客户ID列表
     */
    private List<Long> customerIds;

    /**
     * 商机ID列表
     */
    private List<Long> businessIds;

    /**
     * 合同ID列表
     */
    private List<Long> contractIds;

    private Long userId;

    private List<Integer> resourceStatuses;

    private List<Long> ouIds;

    private String ouIdsStr;

    private String resourceStatusStr;

    private List<Integer> previewFlags;

    private String previewFlagsStr;

    private String businessCode;

    private String costMethod;

    private String createByName;

    /**
     * 物料预算
     */
    private BigDecimal materialBudgetAmount;

    private String materialBudgetAmountStr;

    /**
     * 差旅预算
     */
    private BigDecimal travelBudgetAmount;

    /**
     * 人力预算
     */
    private BigDecimal humanBudgetAmount;

    private String humanBudgetAmountStr;

    /**
     * 非差旅预算
     */
    private BigDecimal feeBudgetAmount;

    private String feeBudgetAmountStr;

    private String invalidName;

    private String resourceCode;

    private String fuzzyLike;

    private Boolean greaterThanThreeMonth; //预立项超时是否大于3个月

    private String projectTypeName;

    /**
     * 门户预警字段
     */
    private Integer remindTypeCode;

    private String remindTypeName;

    private String title;

    private Date remindDate;

    private Date approvalDate1;

    private Date approvalDate2;

    private String projectNameOrCode;

    //是否提交预立项标识
    private Boolean previewToFormal;

    private List<Long> secondUnits;

    private String isView;

    //WAIT_FOR("0", "待同步"),
    //ABNORMAL("1", "同步异常"),
    //SUCCESS("2", "同步成功");
    private String emsPushStatus;// ems预算同步状态

    private Date emsPushDate;// ems预算同步时间

    private String emsPushMsg;// ems预算同步消息

    private Long productUnitId; //产品部门id

    private String productUnitName; //产品部门名称

    // 应用行业名称
    private String applicationIndustryName;

    private Long associatedProjectId;
    private String associatedProjectCode;
    private String associatedProjectName;
    private BigDecimal associatedAmount;

    private List<MilepostTemplateStage> milepostTemplateStages;

    // 用于项目基本信息初始化数据导入
    @ApiModelProperty("校验结果")
    private String validResult;

    private Long index;

    private String statusName;

    private String customerCRMCode;

    private String projectLevelName;

    private String salesManagerMip;

    private String technologyLeaderMip;

    private String planDesignerMip;

    private String budgetHeaderName;

    private String currencyName;

    private BigDecimal budgetMaterialTotalPrice;

    private BigDecimal budgetHumanTotalPrice;

    private BigDecimal budgetTravelTotalPrice;

    private BigDecimal budgetFeeTotalPrice;

    private BigDecimal totalPrice;

    private String relatedContractCode;

    private String relatedContractName;

    private String relatedBusinessCode;

    private String relatedBusinessName;
    // 用于项目基本信息初始化数据导入

    @ApiModelProperty("是否启用里程碑基准日期")
    private Boolean milestoneBaseDate;

    @ApiModelProperty("是否转质保项目")
    private Boolean transferProject;

    private Date baseStartTime;

    private Date baseEndTime;

    @ApiModelProperty(value = "质保开始时间起始")
    private Date qualityAssuranceStartTimeStart;

    @ApiModelProperty(value = "质保开始时间结束")
    private Date qualityAssuranceStartTimeEnd;

    @ApiModelProperty(value = "质保结束时间起始")
    private Date qualityAssuranceEndTimeStart;

    @ApiModelProperty(value = "质保结束时间结束")
    private Date qualityAssuranceEndTimeEnd;

    @ApiModelProperty("转质保状态")
    private String transferProjectStateName;

    @ApiModelProperty("结项时间")
    private Date postProjectTime;

    @ApiModelProperty("不含税金额")
    private BigDecimal excludingTaxAmount;

    private String amountStr;

    private String budgetCostStr;

    @ApiModelProperty("wbs预算对象")
    private ProjectWbsBudgetObjectDto projectWbsBudgetObjectDTO = new ProjectWbsBudgetObjectDto();

    @ApiModelProperty("wbs预算列表")
    private List<ProjectWbsBudgetDto> projectWbsBudgetList;

    private String wbsCode;

    private String wbsName;

    @ApiModelProperty("已入账税票金额(不含税)")
    private BigDecimal amountOfTaxReceipts;

    @ApiModelProperty("采购合同进度执行金额（不含税）")
    private BigDecimal purchasingContractProgressAmount;

    @ApiModelProperty("驳回类型 1、正常提交；2、驳回后提交")
    private Integer refuseType = 1;

    @ApiModelProperty("项目关联合同传参")
    private List<ProjectContractRsDto> contractTableData;

    @ApiModelProperty("项目关联商机传参")
    private List<ProjectBusinessRsDto> projectBusinessRsList;

    @ApiModelProperty("当前项目id")
    private Long currentProjectId;

    @ApiModelProperty("查询条件：区域")
    private List<String> areaList;

    @ApiModelProperty("查询条件：事业部")
    private List<String> businessDepartmentList;

    private boolean isMultiRole = Boolean.TRUE; //功能合并后，此字段恒为true返回给前端，方便兼容前端历史代码

    @ApiModelProperty(value = "本位币")
    private String localCurrency;

    @ApiModelProperty(value = "项目满意度平均值")
    private BigDecimal satisfactionAvg;

    @ApiModelProperty("项目产品id,多个逗号分隔")
    private String productIdStr;

    @ApiModelProperty("项目产品id")
    private List<String> productIds;

    @ApiModelProperty("转质保项目提交标识")
    private Boolean isTransferSubmit;

    @ApiModelProperty("项目里程碑是否有“可转质保节点”标识")
    private Boolean ifFinalAcceptanceTag = false;

    @ApiModelProperty("“可转质保节点”标识为是的项目里程碑")
    private ProjectMilepost finalAcceptanceMilepost;

    private Long formInstanceId;

    private String fdInstanceId;

    private String formUrl;

    private String eventName;

    private String handlerId;

    private Long companyId;

    private Long createUserId;

    private Integer pageSize;

    private Integer pageNum;

    @ApiModelProperty("计划结项日期")
    private Date planCloseDate;

    @ApiModelProperty("距离计划天数")
    private Integer dayToPlan;

    @ApiModelProperty("结项检查结果：1-未检查 2-检查中 3-检查通过 4-检查不通过 5-检查有警告 6-检查异常")
    private Integer checkResult;

    @ApiModelProperty("检查时间")
    private Date checkTime;

    @ApiModelProperty("附件Id")
    private Long attamentId;

    @ApiModelProperty("提醒对象：1-项目经理 2-项目财务，多选逗号分隔")
    private String remindTo;

    @ApiModelProperty("提醒频率类型，按展示顺序1,2,3，以此类推")
    private Integer frequencyType;

    @ApiModelProperty("提醒频率输入值")
    private String frequencyValue;

    @ApiModelProperty("提醒阈值：距离计划结项日期小于X天")
    private Integer threshold;

    @ApiModelProperty("待结项项目列表")
    private List<ProjectDto> pendingCloseProjectList;

    @ApiModelProperty("流程信息")
    private Map<String, Object> workflowInfo;

    private Date planCloseDateStart;

    private Date planCloseDateEnd;

    private Integer dayToPlanMin;

    private Integer dayToPlanMax;

    private String checkResultStr;

    private List<String> checkResultList;

    private String projectTypeIdStr;

    private List<String> projectTypeIdList;

    /**
     * 项目进度预测信息
     */
    private List<ProjectProgressPredictDto> projectProgressPredictList;

    /**
     *项目进度预测变更信息
     */
    private List<ProjectProgressPredictChangeHistory> projectProgressPredictChangeHistoryList;


    public Boolean getList() {
        return isList;
    }

    public void setList(Boolean list) {
        isList = list;
    }

    public Boolean getMe() {
        return isMe;
    }

    public void setMe(Boolean me) {
        isMe = me;
    }

    public String getFrequencyValue() {
        return frequencyValue == null ? null : frequencyValue.trim();
    }
}

