package com.midea.pam.common.ctc.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PassInterfaceInvokeLogExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PassInterfaceInvokeLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andApplyNoIsNull() {
            addCriterion("apply_no is null");
            return (Criteria) this;
        }

        public Criteria andApplyNoIsNotNull() {
            addCriterion("apply_no is not null");
            return (Criteria) this;
        }

        public Criteria andApplyNoEqualTo(String value) {
            addCriterion("apply_no =", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoNotEqualTo(String value) {
            addCriterion("apply_no <>", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoGreaterThan(String value) {
            addCriterion("apply_no >", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoGreaterThanOrEqualTo(String value) {
            addCriterion("apply_no >=", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoLessThan(String value) {
            addCriterion("apply_no <", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoLessThanOrEqualTo(String value) {
            addCriterion("apply_no <=", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoLike(String value) {
            addCriterion("apply_no like", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoNotLike(String value) {
            addCriterion("apply_no not like", value, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoIn(List<String> values) {
            addCriterion("apply_no in", values, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoNotIn(List<String> values) {
            addCriterion("apply_no not in", values, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoBetween(String value1, String value2) {
            addCriterion("apply_no between", value1, value2, "applyNo");
            return (Criteria) this;
        }

        public Criteria andApplyNoNotBetween(String value1, String value2) {
            addCriterion("apply_no not between", value1, value2, "applyNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoIsNull() {
            addCriterion("serial_no is null");
            return (Criteria) this;
        }

        public Criteria andSerialNoIsNotNull() {
            addCriterion("serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andSerialNoEqualTo(String value) {
            addCriterion("serial_no =", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotEqualTo(String value) {
            addCriterion("serial_no <>", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoGreaterThan(String value) {
            addCriterion("serial_no >", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("serial_no >=", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoLessThan(String value) {
            addCriterion("serial_no <", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoLessThanOrEqualTo(String value) {
            addCriterion("serial_no <=", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoLike(String value) {
            addCriterion("serial_no like", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotLike(String value) {
            addCriterion("serial_no not like", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoIn(List<String> values) {
            addCriterion("serial_no in", values, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotIn(List<String> values) {
            addCriterion("serial_no not in", values, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoBetween(String value1, String value2) {
            addCriterion("serial_no between", value1, value2, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotBetween(String value1, String value2) {
            addCriterion("serial_no not between", value1, value2, "serialNo");
            return (Criteria) this;
        }

        public Criteria andServiceSystemIsNull() {
            addCriterion("service_system is null");
            return (Criteria) this;
        }

        public Criteria andServiceSystemIsNotNull() {
            addCriterion("service_system is not null");
            return (Criteria) this;
        }

        public Criteria andServiceSystemEqualTo(String value) {
            addCriterion("service_system =", value, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemNotEqualTo(String value) {
            addCriterion("service_system <>", value, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemGreaterThan(String value) {
            addCriterion("service_system >", value, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemGreaterThanOrEqualTo(String value) {
            addCriterion("service_system >=", value, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemLessThan(String value) {
            addCriterion("service_system <", value, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemLessThanOrEqualTo(String value) {
            addCriterion("service_system <=", value, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemLike(String value) {
            addCriterion("service_system like", value, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemNotLike(String value) {
            addCriterion("service_system not like", value, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemIn(List<String> values) {
            addCriterion("service_system in", values, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemNotIn(List<String> values) {
            addCriterion("service_system not in", values, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemBetween(String value1, String value2) {
            addCriterion("service_system between", value1, value2, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andServiceSystemNotBetween(String value1, String value2) {
            addCriterion("service_system not between", value1, value2, "serviceSystem");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlIsNull() {
            addCriterion("invoke_url is null");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlIsNotNull() {
            addCriterion("invoke_url is not null");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlEqualTo(String value) {
            addCriterion("invoke_url =", value, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlNotEqualTo(String value) {
            addCriterion("invoke_url <>", value, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlGreaterThan(String value) {
            addCriterion("invoke_url >", value, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlGreaterThanOrEqualTo(String value) {
            addCriterion("invoke_url >=", value, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlLessThan(String value) {
            addCriterion("invoke_url <", value, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlLessThanOrEqualTo(String value) {
            addCriterion("invoke_url <=", value, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlLike(String value) {
            addCriterion("invoke_url like", value, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlNotLike(String value) {
            addCriterion("invoke_url not like", value, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlIn(List<String> values) {
            addCriterion("invoke_url in", values, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlNotIn(List<String> values) {
            addCriterion("invoke_url not in", values, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlBetween(String value1, String value2) {
            addCriterion("invoke_url between", value1, value2, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeUrlNotBetween(String value1, String value2) {
            addCriterion("invoke_url not between", value1, value2, "invokeUrl");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusIsNull() {
            addCriterion("invoke_status is null");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusIsNotNull() {
            addCriterion("invoke_status is not null");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusEqualTo(Integer value) {
            addCriterion("invoke_status =", value, "invokeStatus");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusNotEqualTo(Integer value) {
            addCriterion("invoke_status <>", value, "invokeStatus");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusGreaterThan(Integer value) {
            addCriterion("invoke_status >", value, "invokeStatus");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("invoke_status >=", value, "invokeStatus");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusLessThan(Integer value) {
            addCriterion("invoke_status <", value, "invokeStatus");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusLessThanOrEqualTo(Integer value) {
            addCriterion("invoke_status <=", value, "invokeStatus");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusIn(List<Integer> values) {
            addCriterion("invoke_status in", values, "invokeStatus");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusNotIn(List<Integer> values) {
            addCriterion("invoke_status not in", values, "invokeStatus");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusBetween(Integer value1, Integer value2) {
            addCriterion("invoke_status between", value1, value2, "invokeStatus");
            return (Criteria) this;
        }

        public Criteria andInvokeStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("invoke_status not between", value1, value2, "invokeStatus");
            return (Criteria) this;
        }

        public Criteria andInvokeResultIsNull() {
            addCriterion("invoke_result is null");
            return (Criteria) this;
        }

        public Criteria andInvokeResultIsNotNull() {
            addCriterion("invoke_result is not null");
            return (Criteria) this;
        }

        public Criteria andInvokeResultEqualTo(String value) {
            addCriterion("invoke_result =", value, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultNotEqualTo(String value) {
            addCriterion("invoke_result <>", value, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultGreaterThan(String value) {
            addCriterion("invoke_result >", value, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultGreaterThanOrEqualTo(String value) {
            addCriterion("invoke_result >=", value, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultLessThan(String value) {
            addCriterion("invoke_result <", value, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultLessThanOrEqualTo(String value) {
            addCriterion("invoke_result <=", value, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultLike(String value) {
            addCriterion("invoke_result like", value, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultNotLike(String value) {
            addCriterion("invoke_result not like", value, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultIn(List<String> values) {
            addCriterion("invoke_result in", values, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultNotIn(List<String> values) {
            addCriterion("invoke_result not in", values, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultBetween(String value1, String value2) {
            addCriterion("invoke_result between", value1, value2, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeResultNotBetween(String value1, String value2) {
            addCriterion("invoke_result not between", value1, value2, "invokeResult");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeIsNull() {
            addCriterion("invoke_time is null");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeIsNotNull() {
            addCriterion("invoke_time is not null");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeEqualTo(Date value) {
            addCriterion("invoke_time =", value, "invokeTime");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeNotEqualTo(Date value) {
            addCriterion("invoke_time <>", value, "invokeTime");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeGreaterThan(Date value) {
            addCriterion("invoke_time >", value, "invokeTime");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("invoke_time >=", value, "invokeTime");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeLessThan(Date value) {
            addCriterion("invoke_time <", value, "invokeTime");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeLessThanOrEqualTo(Date value) {
            addCriterion("invoke_time <=", value, "invokeTime");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeIn(List<Date> values) {
            addCriterion("invoke_time in", values, "invokeTime");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeNotIn(List<Date> values) {
            addCriterion("invoke_time not in", values, "invokeTime");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeBetween(Date value1, Date value2) {
            addCriterion("invoke_time between", value1, value2, "invokeTime");
            return (Criteria) this;
        }

        public Criteria andInvokeTimeNotBetween(Date value1, Date value2) {
            addCriterion("invoke_time not between", value1, value2, "invokeTime");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}