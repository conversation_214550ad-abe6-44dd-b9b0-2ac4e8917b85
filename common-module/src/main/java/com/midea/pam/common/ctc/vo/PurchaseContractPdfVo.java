package com.midea.pam.common.ctc.vo;

import com.midea.pam.common.ctc.dto.PurchaseContractStandardTermsDto;
import com.midea.pam.common.ctc.entity.PurchaseContractStamp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "PurchaseContractPdfVO", description = "采购合同PDF数据封装")
public class PurchaseContractPdfVo {

    @ApiModelProperty(value = "使用单位名称")         //定值，缓存
    private String unitName;

    @ApiModelProperty(value = "合同版本号")           //定值，purchase_contract_change_header.version_code
    private String versionCode;

    @ApiModelProperty(value = "采购合同编号")         //定值，purchase_contract.code
    private String contractCode;

    @ApiModelProperty(value = "币种")               //定值，purchase_contract.currency
    private String currency;

    @ApiModelProperty(value = "合同日期：下载PDF的日期")
    private Date contractDate;

    @ApiModelProperty(value = "承诺交期")
    private Date deliveryDate;

    @ApiModelProperty(value = "卖方：供应商编号 供应商名称")                   //基本信息变更，purchase_contract.vendor_code+vendor_name
    private String vendorName;

    @ApiModelProperty(value = "买方：采购跟进人")                            //基本信息变更，purchase_contract.purchasing_follower_name
    private String purchasingFollowerName;

    @ApiModelProperty(value = "买方邮箱：采购跟进人的kuka邮箱没有的话取MIP邮箱")  //基本信息变更，purchase_contract.purchasing_follower，再查缓存
    private String purchasingFollowerEmail;

    @ApiModelProperty(value = "合同条款")                                   //基本信息变更，purchase_contract.contract_terms
    private String contractTerms;

    @ApiModelProperty(value = "交货方式")                                   //基本信息变更，purchase_contract.delivery_type
    private String deliveryType;

    @ApiModelProperty(value = "交货条款")                                   //基本信息变更，purchase_contract.delivery_clause
    private String deliveryClause;

    @ApiModelProperty(value = "不含税总价：合同总额（不含税）")              //合同内容变更，purchase_contract.excluding_tax_amount
    private BigDecimal excludingTaxAmount;

    @ApiModelProperty(value = "税：合同总额（含税）-合同总额（不含税）")       //amount-excludingTaxAmount
    private BigDecimal tax;

    @ApiModelProperty(value = "总价：合同总额（含税）")                     //合同内容变更，purchase_contract.amount
    private BigDecimal amount;

    @ApiModelProperty(value = "买方盖章：盖章attachId")                       //页面维护
    private Long purchaserAttachId;

    @ApiModelProperty(value = "公司信息")
    private String companyInfo;

    @ApiModelProperty(value = "收货地址")
    private String deliveryAddress;

    @ApiModelProperty(value = "合同版本创建时间")
    private Date versionCreateTime;

    //按审批顺序取审批人的签名图片变更时，如果金额增加了，取变更流程的审批人的签名图片金额没有增加，取合同新建流程的审批人的签名图
    @ApiModelProperty(value = "核准：核准人签名信息列表")                       //页面维护
    private List<PurchaseContractStamp> approveInfoList;

    @ApiModelProperty(value = "付款比例/条件/方式列表：取付款计划中的：计划付款比例/付款条件/付款方式")  //付款计划变更，payment_plan.proportion/requirement/payment_method_name
    private List<String> payPlanInfoList;

    @ApiModelProperty(value = "预算信息列表")
    private List<PurchaseContractBudgetPdfVo> budgetInfoList;

    @ApiModelProperty(value = "合同内容列表")
    private List<PurchaseContractDetailPdfVo> detailInfoList;

    private String contractTermsFlg;

    @ApiModelProperty(value = "标准条款信息")
    private List<PurchaseContractStandardTermsDto> standardTermsDtoList;

}
