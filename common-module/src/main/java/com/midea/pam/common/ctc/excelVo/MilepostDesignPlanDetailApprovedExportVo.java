package com.midea.pam.common.ctc.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 审批通过的详细设计BOM
 *
 * <AUTHOR> @ meicloud.com)
 * @since 2019-11-11
 */
@Getter
@Setter
public class MilepostDesignPlanDetailApprovedExportVo {

    @Excel(name = "序号")
    private String serialNumber;

    @Excel(name = "集成外包", replace = {"是_true", "否_false", "_null"})
    private Boolean ext;

    @Excel(name = "物料描述")
    private String materielDescr;

    @Excel(name = "PAM编码")
    private String pamCode;

    @Excel(name = "ERP物料编码")
    private String erpCode;

    @Excel(name = "WBS")
    private String wbsSummaryCode;

    @Excel(name = "WBS层级")
    private String wbsLayer;

    @Excel(name = "单位")
    private String unit;

    @Excel(name = "采购需求总数", width = 20)
    private String totalNum_dt;

    @Excel(name = "单套数量")
    private String number_dt;

    private BigDecimal number;

    private BigDecimal totalNum;

    @Excel(name = "需求日期", format = "yyyy-MM-dd")
    private Date deliveryTime;

    @Excel(name = "名称")
    private String name;

    @Excel(name = "型号/规格/图号")
    private String model;

    @Excel(name = "品牌")
    private String brand;

    private Integer requirementType;

    @Excel(name = "需求类型", width = 20)
    private String requirementTypeExport;

    @Excel(name = "物料大类")
    private String materialClassification;

    @Excel(name = "物料中类")
    private String codingMiddleClass;

    @Excel(name = "物料小类")
    private String materielType;

    @Excel(name = "加工件分类")
    private String machiningPartType;

    @Excel(name = "材质")
    private String material;

    private BigDecimal unitWeight;

    @Excel(name = "单位重量(Kg)")
    private String unitWeight_dt;

    @Excel(name = "材质处理")
    private String materialProcessing;

    private BigDecimal budgetUnitPrice;

    @Excel(name = "预算单价")
    private String budgetUnitPrice_dt;

    private BigDecimal designCost;

    @Excel(name = "设计成本")
    private String designCost_dt;

    private BigDecimal budgetSubtotal;

    //@Excel(name = "预算小计")
    private String budgetSubtotal_dt;

    private BigDecimal designSubtotal;


    @Excel(name = "活动事项编码")
    private String activityCode;

    @Excel(name = "项目预算类别属性")
    private String projectBudgetType;

    @Excel(name = "设计小计")
    private String designSubtotal_dt;

    @Excel(name = "设计成本是否来源估价", replace = {"是_1", "否_0", "_null"})
    private Integer itemCostIsNull;

    @Excel(name = "品牌商物料编码")
    private String brandMaterialCode;

    @Excel(name = "备件标识")
    private String orSparePartsMask;

    @Excel(name = "设计人员")
    private String planDesigner;

    @Excel(name = "设计发布批次号")
    private String designReleaseLotNumber;

    @Excel(name = "备注")
    private String description;

    @Excel(name = "来源")
    private String source;

    @Excel(name = "模组状态", replace = {"未确认_0", "已确认_1", "确认中_2", "_null"})
    private String moduleStatus;

    private BigDecimal requirementNum;

    @Excel(name = "已生成采购需求数量")
    private String requirementNum_dt;

    private Boolean generateRequirement;

    @Excel(name = "需求发布单据号")
    private String purchaseMaterialRequirementReceiptsCode;

    private Long id;

    public Boolean getGenerateRequirement() {
        return generateRequirement;
    }

    public void setGenerateRequirement(Boolean generateRequirement) {
        this.generateRequirement = generateRequirement;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Boolean getExt() {
        return ext;
    }

    public void setExt(Boolean ext) {
        this.ext = ext;
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getMaterialClassification() {
        return materialClassification;
    }

    public void setMaterialClassification(String materialClassification) {
        this.materialClassification = materialClassification;
    }

    public String getCodingMiddleClass() {
        return codingMiddleClass;
    }

    public void setCodingMiddleClass(String codingMiddleClass) {
        this.codingMiddleClass = codingMiddleClass;
    }

    public String getMaterielType() {
        return materielType;
    }

    public void setMaterielType(String materielType) {
        this.materielType = materielType;
    }

    public BigDecimal getBudgetUnitPrice() {
        return budgetUnitPrice;
    }

    public void setBudgetUnitPrice(BigDecimal budgetUnitPrice) {
        this.budgetUnitPrice = budgetUnitPrice;
    }

    public BigDecimal getDesignCost() {
        return designCost;
    }

    public void setDesignCost(BigDecimal designCost) {
        this.designCost = designCost;
    }

    public BigDecimal getBudgetSubtotal() {
        return budgetSubtotal;
    }

    public void setBudgetSubtotal(BigDecimal budgetSubtotal) {
        this.budgetSubtotal = budgetSubtotal;
    }

    public BigDecimal getDesignSubtotal() {
        return designSubtotal;
    }

    public void setDesignSubtotal(BigDecimal designSubtotal) {
        this.designSubtotal = designSubtotal;
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getModuleStatus() {
        return moduleStatus;
    }

    public void setModuleStatus(String moduleStatus) {
        this.moduleStatus = moduleStatus;
    }

    public String getMachiningPartType() {
        return machiningPartType;
    }

    public void setMachiningPartType(String machiningPartType) {
        this.machiningPartType = machiningPartType;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public BigDecimal getUnitWeight() {
        return unitWeight;
    }

    public void setUnitWeight(BigDecimal unitWeight) {
        this.unitWeight = unitWeight;
    }

    public String getMaterialProcessing() {
        return materialProcessing;
    }

    public void setMaterialProcessing(String materialProcessing) {
        this.materialProcessing = materialProcessing;
    }

    public String getNumber_dt() {
        if (Objects.nonNull(this.number)) {
            return this.number.stripTrailingZeros().toPlainString();
        } else {
            return this.number_dt;
        }
    }

    public void setNumber_dt(String number_dt) {
        this.number_dt = number_dt;
    }

    public String getBudgetUnitPrice_dt() {
        if (Objects.nonNull(this.budgetUnitPrice)) {
            return this.budgetUnitPrice.stripTrailingZeros().toPlainString();
        } else {
            return this.budgetUnitPrice_dt;
        }
    }

    public void setBudgetUnitPrice_dt(String budgetUnitPrice_dt) {
        this.budgetUnitPrice_dt = budgetUnitPrice_dt;
    }

    public String getDesignCost_dt() {
        if (Objects.nonNull(this.designCost)) {
            return this.designCost.stripTrailingZeros().toPlainString();
        } else {
            return this.designCost_dt;
        }
    }

    public void setDesignCost_dt(String designCost_dt) {
        this.designCost_dt = designCost_dt;
    }

    public String getBudgetSubtotal_dt() {
        if (Objects.nonNull(this.budgetSubtotal)) {
            return this.budgetSubtotal.stripTrailingZeros().toPlainString();
        } else {
            return this.budgetSubtotal_dt;
        }
    }

    public void setBudgetSubtotal_dt(String budgetSubtotal_dt) {
        this.budgetSubtotal_dt = budgetSubtotal_dt;
    }

    public String getDesignSubtotal_dt() {
        if (Objects.nonNull(this.designSubtotal)) {
            return this.designSubtotal.stripTrailingZeros().toPlainString();
        } else {
            return this.designSubtotal_dt;
        }
    }

    public void setDesignSubtotal_dt(String designSubtotal_dt) {
        this.designSubtotal_dt = designSubtotal_dt;
    }

    public String getUnitWeight_dt() {
        if (Objects.nonNull(this.unitWeight)) {
            return this.unitWeight.stripTrailingZeros().toPlainString();
        } else {
            return this.unitWeight_dt;
        }
    }

    public void setUnitWeight_dt(String unitWeight_dt) {
        this.unitWeight_dt = unitWeight_dt;
    }

    public BigDecimal getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(BigDecimal totalNum) {
        this.totalNum = totalNum;
    }

    public BigDecimal getRequirementNum() {
        return requirementNum;
    }

    public void setRequirementNum(BigDecimal requirementNum) {
        this.requirementNum = requirementNum;
    }

    public String getTotalNum_dt() {
        if (Objects.nonNull(this.totalNum)) {
            return this.totalNum.stripTrailingZeros().toPlainString();
        } else {
            return this.totalNum_dt;
        }
    }

    public void setTotalNum_dt(String totalNum_dt) {
        this.totalNum_dt = totalNum_dt;
    }

    public String getRequirementNum_dt() {
        if (Objects.nonNull(this.requirementNum)) {
            return this.requirementNum.stripTrailingZeros().toPlainString();
        } else {
            return this.requirementNum_dt;
        }
    }

    public void setRequirementNum_dt(String requirementNum_dt) {
        this.requirementNum_dt = requirementNum_dt;
    }

    public Integer getItemCostIsNull() {
        return itemCostIsNull;
    }

    public void setItemCostIsNull(Integer itemCostIsNull) {
        this.itemCostIsNull = itemCostIsNull;
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode;
    }

    public String getWbsLayer() {
        return wbsLayer;
    }

    public void setWbsLayer(String wbsLayer) {
        this.wbsLayer = wbsLayer;
    }
}