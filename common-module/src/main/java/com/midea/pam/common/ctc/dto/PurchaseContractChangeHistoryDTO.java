package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-23
 * @description 采购合同数据传输对象
 */
@Getter
@Setter
public class PurchaseContractChangeHistoryDTO extends PurchaseContractChangeHistory {

    private static final long serialVersionUID = -4537973045603661472L;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String projectCode;

    @ApiModelProperty("采购跟进人")
    private String purchasingFollowerName;

    /**
     * 归档开始时间
     */
    private Date filingStartDate;

    /**
     * 归档结束时间
     */
    private Date filingEndDate;

    private String ouName;

    @ApiModelProperty(value = "供应商类型")
    private String vendorTypeLookupCode;

    @ApiModelProperty(value = "地点状态")
    private String pvsStatus;

    @ApiModelProperty(value = "采购合同标准条款信息")
    private List<PurchaseContractStandardTermsDto> standardTermsDtoList;

}
