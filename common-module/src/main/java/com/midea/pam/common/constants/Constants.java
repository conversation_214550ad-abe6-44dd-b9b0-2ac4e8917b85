package com.midea.pam.common.constants;

/**
 * 公共常量类.
 * chengzy7.
 */
public class Constants {
    /**
     * mip号.
     */
    public static final String FD_LOGIN_NAME = "fdLoginName";
    /**
     * 用户id
     */
    public static final String FD_LOGIN_ID = "fdLoginId";
    /**
     * 用户语言.
     */
    public static final String FD_LANGUAGE = "fdLanguage";
    /**
     * 用户当前业务分类(虚拟部门).
     */
    public static final String CURRENT_UNIT = "currentUnit";

    /**
     * 当前top虚拟部门id.
     */
    public static final String CURRENT_UNIT_ID = "currentUnitId";

    /**
     * 上下文使用单位id.
     */
    public static final String CENTEST_UNIT_ID = "contestUnitId";

    /**
     * 当前top虚拟部门name.
     */
    public static final String CURRENT_UNIT_NAME = "currentUnitName";

    /**
     * 用户当前二级单位(虚拟部门).
     */
    public static final String CURRENT_SECOND_UNIT = "currentSecondUnit";

    /**
     * 用户ou.
     */
    public static final String ANTHORITY_OU = "anthorityOu";

    public static final String APPSYSTEM = "G-ERP";

    public static final String SUBSYSTME = "C-PAM";

    public static final String SKIPSECURITYINTERCEPTOR = "skipSecurityInterceptor";

    /**
     * PAM编码编号组成:流水号长度
     */
    public static final Integer CODE_PAM_CODE_LENGTH = 6;
    /**
     * SPD流水号组成:流水号长度
     */
    public static final Integer SPD_TOPIC_CODE_LENGTH = 8;
    /**
     * 品牌编号组成:流水号长度
     */
    public static final Integer CODE_BR_CODE_LENGTH = 3;
    /**
     * 单据号组成:流水号长度
     */
    public static final Integer CODE_BRAND_CODE_LENGTH = 3;

    /**
     * ERP编码编号组成:流水号长度
     */
    public static final Integer CODE_ERP_CODE_LENGTH = 9;

    /**
     * ERP编码编号组成:流水号长度
     */
    public static final Integer CODE_KUNSHAN_ERP_CODE_LENGTH = 6;

    /**
     * 组织参数前缀
     */
    public static final String ORGANIZATION_CUSTOM_DICT_PREFIX = "PZ";

    /**
     * 物料分类配置参数前缀
     */
    public static final String MATERIAL_CUSTOM_DICT_PREFIX = "MA";

    /**
     * 商机编号组成:序号长度
     */
    public static final Integer CODE_BUSINESS_NUM_LENGTH = 4;

    /**
     * 线索编号组成:序号长度
     */
    public static final Integer CODE_LEAD_NUM_LENGTH = 4;

    /**
     * 领料单编号组成:序号长度
     */
    public static final Integer MATERIAL_GET_CODE_LENGTH = 3;

    /**
     * 应收发票编号组成:序号长度
     */
    public static final Integer INVOICE_CODE_LENGTH = 3;

    /**
     * 退料单编号组成:序号长度
     */
    public static final Integer MATERIAL_RETURN_CODE_LENGTH = 3;

    /**
     * 转移单编号组成:序号长度
     */
    public static final Integer MATERIAL_TRANSFER_CODE_LENGTH = 3;

    /**
     * 商机方案编号组成:序号长度
     */
    public static final Integer CODE_BUSINESS_PLAN_NUM_LENGTH = 4;

    /**
     * 商机方案报价编号编号组成:序号长度
     */
    public static final Integer CODE_BUSINESS_PLAN_QUOTATION_NUM_LENGTH = 4;

    /**
     * 报价单:报价有效期key值
     */
    public static final String SYSTEM_PARAM_KEY_QUOTATION_VALIDITY_PERIOD = "报价有效期";

    /**
     * esb系统:MM0组织id
     */
    public static final Long MMO_ORGANIZATION_ID = 137L;

    /**
     * 确认记录流程名:redis类别
     */
    public static final String SEQUENCE_TYPE_CONFIRM_RECORD_PROCESS = "CONFIRMRECORDPROCESS";

    /**
     * 确认记录流程名组成:流水号长度
     */
    public static final Integer CODE_CONFIRM_RECORD_PROCESS_LENGTH = 3;

    /**
     * 详细设计单据编号:流水号长度
     */
    public static final Integer CODE_REQUIREMENT_CODE_LENGTH = 5;

    /**
     * 客户间转款编号:redis类别
     */
    public static final String CUSTOMER_TRANSFER = "CUSTOMERTRANSFER";

    /**
     * 客户间转款编号:流水号长度
     */
    public static final Integer CODE_CUSTOMER_TRANSFER_LENGTH = 4;

    /**
     * 客户pam编码编号组成:流水号长度
     */
    public static final Integer CODE_CUSTOMER_PAM_CODE_LENGTH = 6;

    /**
     * 订单编号组成:流水号长度
     */
    public static final Integer CODE_ORDER_NUM_LENGTH = 3;

    /**
     * 结转单编码组成:流水号长度
     */
    public static final Integer CODE_CARRYOVER_BILL_CODE_LENGTH = 3;

    /**
     * 工时变更单编码组成:流水号长度
     */
    public static final Integer WORKING_HOUR_COST_CODE = 3;

    /**
     * 项目收入确认清单导出文件名组成:流水号长度
     */
    public static final Integer CODE_INCOME_COLLECTION_FILE_NAME_LENGTH = 3;

    /**
     * 结转确认计划导出文件名组成:流水号长度
     */
    public static final Integer CODE_WAIT_CARRYOVER_BILL_FILE_NAME_LENGTH = 3;

    /**
     * 结转清单导出文件名组成:流水号长度
     */
    public static final Integer CODE_CARRYOVER_BILL_FILE_NAME_LENGTH = 3;

    /**
     * 采购合同关联预算信息导出文件名组成:流水号长度
     */
    public static final Integer CODE_PURCHASE_CONTRACT_MATERIAL_BUDGET_FILE_NAME_LENGTH = 3;

    /**
     * 品牌清单导出文件名组成:流水号长度
     */
    public static final Integer PPQD = 3;

    /**
     * 差异分摊编码组成:流水号长度
     */
    public static final Integer DIFFERENCE_SHARE_ACCOUNT_LENGTH = 3;

    /**
     * 重分类编码组成：流水号长度
     */
    public static final Integer CONTRACT_CLASSIFICATION_LENGTH = 3;

    /**
     * 汇兑损益编码组成：流水号长度
     */
    public static final Integer EXCHANGE_ACCOUNT_LENGTH = 2;

    /**
     * 资产折旧成本入帐单编码组成：流水号长度
     */
    public static final Integer ASSET_DEPRN_ACCOUNTING_LENGTH = 3;

    /**
     * 详设发布批次号：流水号长度
     */
    public static final Integer DESIGN_DETAIL_PUBLISH_LENGTH = 5;

    /**
     * 激活.
     */
    public static final String ACTIVE = "Active";

    /**
     * OU短码
     */
    public static final String OU_CODE = "OU_CODE";

    /**
     * h5应用标识，工时填报
     */
    public static final String MC_WIDGET_IDENTIFIER_MOBILE = "com.meicloud.pam.mobile";

    /**
     * h5应用标识，工时审批
     */
    public static final String MC_WIDGET_IDENTIFIER_APPROVE = "com.meicloud.pam.approve";

    /**
     * 工时审批pc端跳转链接
     */
    public static final String APPROVE_JUMP_URL = "/#/workhour/audit";

    /**
     * 工时填报pc端跳转链接
     */
    public static final String SUBMIT_JUMP_URL = "/#/workhour/submit";


    /**
     * 备料方式
     */
    public static final String SPAREPARTS_MASK = "备件标识";

    public static final String MILEPOST_DESIGN_PLAN_NOT_PUBLISH_REQUIREMENT = "详设未发布需求邮件提醒";

    public static final String FORBIDDEN_SUBINVENTORY = "禁用子库";

    public static final String REQUIREMENTTYPE_EXTIS = "需求类型关联是否外包";

    public static final String MATERIAL_CODE_RULE = "物料编码规则";

    public static final String PURCHASE_ORDER_GROUP_RULE = "采购订单分组规则";

    public static final String PURCHASE_CONTRACT_IF_LEGAL = "PAM采购合同写入法务系统审批";

    public static final String WBS_PURCHASE_ORDER_MATERIAL_PRICE_CONFIG = "WBS采购订单历史价价格类型";

    public static final String IF_INVOICE_RECEIVABLE_DUE_DATE_PUSH_ERP = "应收发票到期日是否同步ERP";

    public static final String WORKING_HOUR_LIMIT_DAY = "工时填报限制天数";

    public static final String WBS_TEMPLATE_NEW_WEB_TYPE = "详细设计单据预算不区分外包WBS模板";

    public static final String COMPANY_FILTER_ORG = "组织架构用户同步";

    /**
     * 借方科目
     */
    public static final String SUBJECT = "129701.0.5401030101.0.0.0.0";

    /**
     * 商机号传EMS
     */
    public static final String GEMS_BUSINESS_NUMBER = "商机号传EMS";

    /**
     * 用于昆山测试环境项目数据初始化导入 begin
     */
    public static final String PROFIT_CENTER = "利润中心";

    public static final String PRODUCT_CENTER = "产品中心";

    public static final Long YL = 884455544528568320L;
    /**
     * 用于昆山测试环境项目数据初始化导入 end
     */


    /**
     * 前缀.
     */
    public static class Prefix {
        /**
         * 用户.
         */
        public static final String USER = "BASEDATA:USER:";
        /**
         * 部门.
         */
        public static final String ORGANIZATION = "BASEDATA:ORGANIZATION:";
        /**
         * 省市区域.
         */
        public static final String AREA = "BASEDATA:AREA:";

        /**
         * 数据字典+id.
         */
        public static final String DICT_ID = "BASEDATA:DICT:ID:";

        /**
         * 数据字典+name.
         */
        public static final String DICT_NAME = "BASEDATA:DICT:NAME:";

        /**
         * 数据字典+code.
         */
        public static final String DICT_CODE = "BASEDATA:DICT:CODE:";

        /**
         * 虚拟单位..
         */
        public static final String UNIT = "BASEDATA:UNIT:";

        /**
         * 费用类型..
         */
        public static final String FEEITEM = "BASEDATA:FEEITEM:";


        /**
         * 预算部门.
         */
        public static final String BUDGET_DEP = "BASEDATA:BUDGET_DEP:";

        /**
         * 业务实体.
         */
        public static final String OU = "BASEDATA:OU:";

        /**
         * 虚拟部门与业务实体关系.
         */
        public static final String OU_UNIT_REL = "BASEDATA:OU_UNIT_REL:";

        /**
         * 虚拟部门与业务实体关系集合
         */
        public static final String UNIT_OU_LIST = "BASEDATA:UNIT_OU_LIST:";

        /**
         * 虚拟部门关联子集合
         */
        public static final String UNIT_CHILD_LIST = "BASEDATA:UNIT_CHILD_LIST:";

        /**
         * 客户.
         */
        public static final String CUSTOMER = "CRM:CUSTOMER:";
        /**
         * 角色.
         */
        public static final String LABOR_COST = "BASEDATA:LABOR_COST:";

        /**
         * 会计区间.
         * 多key值不要以：结尾
         */
        public static final String PERIOD = "BASEDATA:PERIOD";

        /**
         * 库存会计区间.
         * 多key值不要以：结尾
         */
        public static final String ORG_PERIOD = "BASEDATA:ORG_PERIOD";

        /**
         * 用户对应的ou.
         */
        public static final String USER_OU = "BASEDATA:USER_OU:";

        /**
         * 用户对应的ou.
         */
        public static final String SYSTEM_CONTEXT = "SYSTEM:CONTEXT:";

        /**
         * 罚扣类型字典名称
         */
        public static final String PENALTY_TYPE = "PENALTY_TYPE";

        /**
         * 常规发票字典CODE
         */
        public static final String INVOICE_INV001 = "INV001";

        /**
         * 罚扣发票字典CODE
         */
        public static final String INVOICE_INV002 = "INV002";

        /**
         * 返利发票字典CODE
         */
        public static final String INVOICE_INV003 = "INV003";

        /**
         * 罚扣款类型-罚扣不开票
         */
        public static final String PENALTY_TYPE_FK0001 = "FK0001";

        /**
         * 外部人力费率新增（查询当前使用单位的供应商）
         */
        public static final String VENDOR_SITE_BANK = "BASEDATA:VENDOR_SITE_BANK:";

        /**
         * FAT+里程碑类型的成本方法
         */
        public static final String FAT_INCOME = "CTC:FAT_INCOME:";

    }

    /**
     * 分布式锁前缀.
     */
    public static class DistributedLockKey {

        /**
         * 详设回调
         */
        public static final String DISTRIBUTED_LOCK_DESIGN_PLAN_CALLBACK = "DISTRIBUTED_LOCK_DESIGN_PLAN_CALLBACK";

        public static final String DISTRIBUTED_LOCK_DESIGN_PLAN_CONFIRM_CALLBACK = "DISTRIBUTED_LOCK_DESIGN_PLAN_CONFIRM_CALLBACK";

        /**
         * 确认归集
         */
        public static final String DISTRIBUTED_LOCK_COLLECTION_SUMMARY = "DISTRIBUTED_LOCK_COLLECTION_SUMMARY";

        /**
         * 确认结转
         */
        public static final String DISTRIBUTED_LOCK_CONFIRM_CARRY_BILLOVER = "DISTRIBUTED_LOCK_CONFIRM_CARRY_BILLOVER";

        /**
         * 入账批次号
         */
        public static final String DISTRIBUTED_LOCK_ACCOUNTING= "DISTRIBUTED_LOCK_ACCOUNTING";

        /**
         * 资产折旧成本入账
         */
        public static final String DISTRIBUTED_LOCK_ASSET_DEPRN_ACCOUNTING= "DISTRIBUTED_LOCK_ASSET_DEPRN_ACCOUNTING";

        /**
         * 收入结转计算
         */
        public static final String DISTRIBUTED_LOCK_INCOMECALCULATION = "DISTRIBUTED_LOCK_INCOMECALCULATION_";

        /**
         * 接口卡推送
         */
        public static final String DISTRIBUTED_LOCK_RESENDEXECUTE = "DISTRIBUTED_LOCK_RESENDEXECUTE_";

        /**
         * 客户PAM编码
         */
        public static final String DISTRIBUTED_LOCK_CUSTOMER = "DISTRIBUTED_LOCK_CUSTOMER";

        /**
         * 工时填报
         */
        public static final String DISTRIBUTED_LOCK_WORKINGHOUR_SUBMIT = "DISTRIBUTED_LOCK_WORKINGHOUR_SUBMIT";

        /**
         * 客户同步crmcode
         */
        public static final String GCRMACCOUNTPUBLIC_LOCK_CRMCODE = "GCRMACCOUNTPUBLIC_LOCK_CRMCODE";

        /**
         * 回款同步
         */
        public static final String DISTRIBUTED_LOCK_RECEIPTCLAIMASYC = "DISTRIBUTED_LOCK_RECEIPTCLAIMASYC";

        /**
         * 采购需求关闭
         */
        public static final String DISTRIBUTED_LOCK_REQUIREMENT_CLOSE = "DISTRIBUTED_LOCK_REQUIREMENT_CLOSE";

        public static final String DISTRIBUTED_LOCK_REQUIREMENT_CLOSE_COMMON = "DISTRIBUTED_LOCK_REQUIREMENT_CLOSE_COMMON";

        /**
         * 手工同步RDM数据
         */
        public static final String HANDLE_RDM_SYNC = "HANDLE_RDM_SYNC";

        /**
         * 手工同步EAM数据
         */
        public static final String HANDLE_EAM_SYNC = "HANDLE_EAM_SYNC";

        /**
         * 物料编码流水号
         */
        public static final String ERP_CODE_RULE_DEFAULT_SERIAL_NUMBER_NEW = "ERP_CODE_RULE_DEFAULT_SERIAL_NUMBER_NEW";

        public static final String ERP_CODE_RULE_KUKA2022_SERIAL_NUMBER_NEW = "ERP_CODE_RULE_KUKA2022_SERIAL_NUMBER_NEW";

        public static final String ERP_CODE_RULE_KUNSHAN_SERIAL_NUMBER_NEW = "ERP_CODE_RULE_KUNSHAN_SERIAL_NUMBER_NEW";

        /**
         * 详细设计单据编号
         */
        public static final String REQUIREMENT_CODE = "REQUIREMENT_CODE";

        /**
         * 添加详细设计方案发布
         */
        public static final String SAVE_RECURSIVE_DETAIL = "SAVE_RECURSIVE_DETAIL";

        /**
         * 添加详细设计关联表
         */
        public static final String SAVE_PROJECT_WBS_RECEIPTS_DESIGN_PLAN_REL = "SAVE_PROJECT_WBS_RECEIPTS_DESIGN_PLAN_REL";

        /**
         * 添加工作流回调
         */
        public static final String SAVE_WORK_FLOW_CALLBACK = "SAVE_WORK_FLOW_CALLBACK";

        /**
         * 添加物料估价
         */
        public static final String SAVE_MATERIAL_COST = "SAVE_MATERIAL_COST";

        /**
         * 添加工作流回调
         */
        public static final String RETRY_MIP_CALLBACK_LOG = "RETRY_MIP_CALLBACK_LOG";

    }

    /**
     * 异步请求结果业务类别.
     */
    public static class AsyncRequestResultBusinessType {

        /**
         * 详设发布
         */
        public static final Integer DESIGN_PLAN_SUBMIT = 1;

        /**
         * 详设变更
         */
        public static final Integer DESIGN_PLAN_CHANGE = 2;

        /**
         * 详设模组确认
         */
        public static final Integer DESIGN_PLAN_CONFIRM = 3;

        /**
         * 确认结转
         */
        public static final Integer CONFIRM_CARRY_BILLOVER = 4;

    }


    /**
     * 分页参数.
     */
    public static class Page {
        /**
         * 页码.
         */
        public static final String PAGE_NUM = "pageNum";
        /**
         * 每页记录数.
         */
        public static final String PAGE_SIZE = "pageSize";
        /**
         * 名称.
         */
        public static final String NAME = "name";
        /**
         * 代码.
         */
        public static final String CODE = "code";
        /**
         * 状态.
         */
        public static final String STATUS = "status";
        /**
         * 客户名称.
         */
        public static final String CUSTOMER_NAME = "customerName";
        /**
         * id.
         */
        public static final String ID = "id";
        /**
         * 项目id.
         */
        public static final String PROJECT_ID = "projectId";

        /**
         * 用户id.
         */
        public static final String USER_ID = "userId";

        /**
         * 项目经理id
         */
        public static final String MANAGER_ID = "managerId";

        /**
         * 项目类型ID
         */
        public static final String PROJECT_TYPE_ID = "projectTypeId";

    }

    /**
     * 字典类型
     */
    public static class DictType {

        /**
         * 企业性质
         */
        public static final String COMPANY_NATURE = "company_nature";

        /**
         * 纳税人类型
         */
        public static final String TAXPAYER_TYPE = "taxpayer_type";

    }

    public static final String YES = "Y";

    public static final String NO = "N";

    // 邮件推送
    public static final String REDIS_NOTICE_EMAIL_KEY = "NOTICE_EMAIL_SEND_LIST";

    // 移动提醒推送
    public static final String REDIS_NOTICE_MOBILE_KEY = "NOTICE_MOBILE_SEND_LIST";

    // 流程事件处理
    public static final String REDIS_WF_EVENT_KEY = "WF_EVENT_LIST";

    /**
     * rdm接口名，工时
     */
    public static final String RDM_WORKING_HOURS = "/open/pam/workingHours";

    /**
     * rdm接口名，资源计划
     */
    public static final String RDM_RESPLAN = "/open/pam/resPlan";


    /**
     * eam接口名，PAM开票校验接口
     */
    public static final String EAM_CHECKINVOICE = "/openapi/checkInvoice";


    /**
     * eam接口名，PAM开票状态同步接口
     */
    public static final String EAM_SYNCINVOICESTATUR = "/openapi/syncInvoice";

    /**
     * Swift国际银行接口名，根据时间查询
     */
    public static final String FDP_SYNCSWIFT = "/swift/querySwiftToSync";

    public static final String FDP_CNAPS = "/cnaps/querypage";

    public static final String FDP_BUDGET_ITEM = "/budget/queryBudgetItem";
    /**
     * rdm接口名，结算单
     */
    public static final String RDM_SETTLEMENT_SHEET = "/open/pam/settlementSheet";

    public static final String REDIS_CHECK_SUBMIT_PREFIX = "CHECK_SUBMIT_METHOD:";

    public static final String COMMA = ",";

    public static final String SEMICOLON = ";";

    //系统人员id
    public static final Long ADMIM = 88888888L;

    // sql日志
    public static final String SQL_LOG_KEY = "SQL_LOG_LIST";

    //调剂申请编号规则
    public static final String SWAP_APPLY_CODE = "TA";

    //调剂执行编号规则
    public static final String SWAP_APPLY_EXE_CODE = "TC";


    /**
     * 流程常量
     */
    public static class Workflow {

        public final static String NODE_PRODUCT_MANAGER = "事业部产品总监";
        public final static String NODE_UNIT_MANAGER = "事业部总监";
        public final static String NODE_PMO = "PMO";
        public final static String NODE_FINANCE = "财经";
        public final static String NODE_COMPANY_MANAGER = "总经理";
        public final static String NODE_MARKETING_CENTER_MANAGER = "营销中心总经理";
        public final static String NODE_MARKETING_CENTER_AREA_MANAGER = "营销中心区域总监";

    }

    /*
     * 用户id常量
     * */
    public static class UserId {

        public final static Long SALES_MINISTER = 609694939684536320L;
        public final static Long CENTER_MINISTER = 740766096650403840L;
    }

    /*
     * 结转单其他检查项
     * */
    public static class OtherCheck01 {

        public final static String CODE = "CB01";
        public final static String NAME = "项目预算总额为0，不允许结转";
    }

    /*
     * 结转单其他检查项
     * */
    public static class OtherCheck02 {

        public final static String CODE = "CB02";
        public final static String NAME = "项目无客户，无法获取往来段";
    }

    /*
     * 结转单其他检查项
     * */
    public static class OtherCheck03 {

        public final static String CODE = "CB03";
        public final static String NAME = "外币项目无结转汇率，无法结转";
    }

    /*
     * 结转单其他检查项
     * */
    public static class OtherCheck04 {

        public final static String CODE = "CB04";
        public final static String NAME = "客户企业性质为空，不允许结转";
    }

    /**
     * 服务idb
     */
    public static class ServiceId {
        public final static String PAM_CTC = "PAM-CTC";
        public final static String PAM_STATISTICS = "PAM-STATISTICS";
        public final static String PAM_BASEDATA = "PAM-BASEDATA";
        public final static String PAM_CRM = "PAM-CRM";
        public final static String PAM_MDW = "PAM-MDW";
    }

    /**
     * idm接口
     */
    public static class Idm {
        public final static String ACCOUNT = "/idm/sync/account"; // 用户同步
        public final static String UNIT = "/idm/sync/unit"; // 组织同步
        public final static String POSITION = "/idm/sync/position"; // 岗位同步
        public final static String QUERY_USER_INFO = "/idm/account/queryUserInfo"; // 用户基本信息查询
        public final static String QUERY_BASE = "/idm/org/query-base"; // 查询组织信息
        public final static String QUERY_INFO_BY_DEPARTMENT_NUMBER = "/idm/position/queryInfoByDepartmentNumber"; // 根据部门查询岗位信息（接口未授权暂时不用）
    }

}
