package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "员工发薪记录")
public class CnbEmpBasicData extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "统计年月")
    private String statisticDate;

    @ApiModelProperty(value = "pam用户id")
    private Long userId;

    @ApiModelProperty(value = "mip账号")
    private String mipName;

    @ApiModelProperty(value = "工号")
    private String employeeCode;

    @ApiModelProperty(value = "发薪单位ID")
    private String fullPayUnitId;

    @ApiModelProperty(value = "发薪单位")
    private String localFullUnitName;

    @ApiModelProperty(value = "hr系统同步ouId")
    private Long hrOuId;

    @ApiModelProperty(value = "ouId")
    private Long ouId;

    @ApiModelProperty(value = "OU名称")
    private String ouName;

    @ApiModelProperty(value = "删除标志")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public String getStatisticDate() {
        return statisticDate;
    }

    public void setStatisticDate(String statisticDate) {
        this.statisticDate = statisticDate == null ? null : statisticDate.trim();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getMipName() {
        return mipName;
    }

    public void setMipName(String mipName) {
        this.mipName = mipName == null ? null : mipName.trim();
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode == null ? null : employeeCode.trim();
    }

    public String getFullPayUnitId() {
        return fullPayUnitId;
    }

    public void setFullPayUnitId(String fullPayUnitId) {
        this.fullPayUnitId = fullPayUnitId == null ? null : fullPayUnitId.trim();
    }

    public String getLocalFullUnitName() {
        return localFullUnitName;
    }

    public void setLocalFullUnitName(String localFullUnitName) {
        this.localFullUnitName = localFullUnitName == null ? null : localFullUnitName.trim();
    }

    public Long getHrOuId() {
        return hrOuId;
    }

    public void setHrOuId(Long hrOuId) {
        this.hrOuId = hrOuId;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", statisticDate=").append(statisticDate);
        sb.append(", userId=").append(userId);
        sb.append(", mipName=").append(mipName);
        sb.append(", employeeCode=").append(employeeCode);
        sb.append(", fullPayUnitId=").append(fullPayUnitId);
        sb.append(", localFullUnitName=").append(localFullUnitName);
        sb.append(", hrOuId=").append(hrOuId);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}