package com.midea.pam.common.ctc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-06-30
 * @description 需求预算明细
 */
@Getter
@Setter
public class ProjectWbsCostOnTheWayVO implements Serializable {

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "WBS")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "在途成本汇总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "po的未接收数量*po单价")
    List<ProjectWbsPoVO> poOnTheWay;

    @ApiModelProperty(value = "采购合同分配金额-进度执行金额")
    List<ProjectWbsPurchaseVO> purchaseOnTheWay;

    @ApiModelProperty(value = "已填报待审批的工时*标准费率")
    List<ProjectWbsWorkingHoursVO> workingHoursOnTheWay;

    @ApiModelProperty(value = "费用报销（EC单）已申请未报销")
    List<ProjectWbsEcVO> ecOnTheWay;

}
