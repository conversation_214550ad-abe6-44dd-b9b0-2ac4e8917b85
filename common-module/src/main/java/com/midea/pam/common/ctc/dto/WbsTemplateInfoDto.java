package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.WbsTemplateInfo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
public class WbsTemplateInfoDto extends WbsTemplateInfo {

    /**
     * 分页
     */
    private Integer pageNum;
    /**
     * 分页
     */
    private Integer pageSize;

    /**
     * 创建人
     */
    private String createByCn;

    /**
     * 模板规则
     */
    private String templateRule;

    /**
     * 虚拟组织id List
     */
    private List<Long> unitIds;

    /**
     * wbs模板规则列表
     */
    private List<WbsTemplateRuleDto> templateRuleList;

    /**
     * 模板名称
     * 费用类型下拉查询的时候用
     */
    private String name;

    /**
     * WBS模版自定义描述列表
     */
    private List<WbsCustomizeRuleDto> customizeRuleList;

    /**
     *描述显示规则(0：默认,1:自定义)
     */
    private Integer describeDisplayFlag;
}
