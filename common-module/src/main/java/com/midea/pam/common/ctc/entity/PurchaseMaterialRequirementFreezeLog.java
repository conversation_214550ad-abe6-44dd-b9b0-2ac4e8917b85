package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "物料采购需求发布单据冻结/解冻记录表")
public class PurchaseMaterialRequirementFreezeLog extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "物料采购需求id")
    private Long purchaseMaterialRequirementId;

    @ApiModelProperty(value = "操作类型（0：冻结，1：解冻）")
    private Integer operationType;

    @ApiModelProperty(value = "冻结原因")
    private String freezeReason;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "删除标识（0：有效， 1：删除）")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getPurchaseMaterialRequirementId() {
        return purchaseMaterialRequirementId;
    }

    public void setPurchaseMaterialRequirementId(Long purchaseMaterialRequirementId) {
        this.purchaseMaterialRequirementId = purchaseMaterialRequirementId;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getFreezeReason() {
        return freezeReason;
    }

    public void setFreezeReason(String freezeReason) {
        this.freezeReason = freezeReason == null ? null : freezeReason.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", purchaseMaterialRequirementId=").append(purchaseMaterialRequirementId);
        sb.append(", operationType=").append(operationType);
        sb.append(", freezeReason=").append(freezeReason);
        sb.append(", remark=").append(remark);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}