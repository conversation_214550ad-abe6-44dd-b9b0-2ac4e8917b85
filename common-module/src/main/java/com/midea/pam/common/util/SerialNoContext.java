package com.midea.pam.common.util;

/**
 * 交易流水号上下文工具类
 * 使用 ThreadLocal 存储当前线程的交易流水号
 * 
 * <AUTHOR>
 */
public class SerialNoContext {
    
    private static final ThreadLocal<String> SERIAL_NO_HOLDER = new ThreadLocal<>();
    
    /**
     * 设置当前线程的交易流水号
     * 
     * @param serialNo 交易流水号
     */
    public static void set(String serialNo) {
        SERIAL_NO_HOLDER.set(serialNo);
    }
    
    /**
     * 获取当前线程的交易流水号
     * 
     * @return 交易流水号，如果未设置则返回 null
     */
    public static String get() {
        return SERIAL_NO_HOLDER.get();
    }
    
    /**
     * 清除当前线程的交易流水号
     * 防止内存泄漏，应该在请求结束时调用
     */
    public static void clear() {
        SERIAL_NO_HOLDER.remove();
    }
    
    /**
     * 获取当前线程的交易流水号，如果为空则返回默认值
     * 
     * @param defaultValue 默认值
     * @return 交易流水号或默认值
     */
    public static String getOrDefault(String defaultValue) {
        String serialNo = get();
        return serialNo != null ? serialNo : defaultValue;
    }
}
