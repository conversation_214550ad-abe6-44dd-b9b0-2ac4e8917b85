package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.PaymentPlanChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectContractBudgetMaterial;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractDetailChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseContractPenaltyChangeHistory;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-6-3
 * @description 采购合同数据传输对象
 */
@Getter
@Setter
public class PurchaseContractDTO extends PurchaseContract {

    private static final long serialVersionUID = -4537973045603661472L;

    /**
     * 头表id
     * 传了就变更变更操作
     */
    @ApiModelProperty(value = "头表id")
    private Long headerId;

    @ApiModelProperty(value = "项目变更原因")
    private String reason;

    @ApiModelProperty(value = "变更原因-类型ID")
    private Long changeRessonType;

    @ApiModelProperty(value = "变更原因-类型名称")
    private String changeRessonTypeName;

    @ApiModelProperty("附件记录")
    private List<CtcAttachmentDto> attachmentDtos;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String projectCode;

    @ApiModelProperty("采购跟进人")
    private String purchasingFollowerName;

    @ApiModelProperty("业务实体")
    private String ouName;

    @ApiModelProperty("合同状态(多选)")
    private String manyStatus;

    @ApiModelProperty("法务合同状态(多选)")
    private String contractStatusStr;

    @ApiModelProperty("业务实体(多选)")
    private String manyOuName;

    @ApiModelProperty("已入账发票金额（含税）")
    private BigDecimal paymentInvoiceAmount;

    @ApiModelProperty("罚扣金额（含税）")
    private BigDecimal penaltyAmount;

    @ApiModelProperty("采购内容")
    private List<PurchaseContractDetailDTO> purchaseContractDetails;

    @ApiModelProperty("付款计划")
    private List<PaymentPlanDTO> paymentPlans;

    @ApiModelProperty("采购内容变更项")
    private List<PurchaseContractDetailChangeHistory> purchaseContractDetailChangeHistoryList;

    @ApiModelProperty("付款计划变更项")
    private List<PaymentPlanChangeHistory> paymentPlanChangeHistoryList;

    @ApiModelProperty("罚扣变更项")
    private List<PurchaseContractPenaltyChangeHistory> purchaseContractPenaltyChangeHistoryList;

    @ApiModelProperty("模组信息")
    private List<ProjectContractBudgetMaterial> projectContractBudgetMaterials;

    @ApiModelProperty("wbs关联预算")
    private List<PurchaseContractBudgetDto> purchaseContractWbsBudgets;

    @ApiModelProperty("wbs关联预算变更项")
    private List<PurchaseContractBudgetChangeHistoryDto> purchaseContractBudgetChangeHistoryList;

    @ApiModelProperty("归档开始时间")
    private Date filingStartDate;

    @ApiModelProperty("归档结束时间")
    private Date filingEndDate;

    @ApiModelProperty("创建开始日期")
    private Date createStartDate;

    @ApiModelProperty("创建结束日期")
    private Date createEndDate;

    @ApiModelProperty("承诺交期开始日期")
    private Date deliveryStartDate;

    @ApiModelProperty("承诺交期结束日期")
    private Date deliveryEndDate;


    @ApiModelProperty("追踪货期开始日期")
    private Date trackLeadStartTime;

    @ApiModelProperty("追踪货期结束日期")
    private Date trackLeadEndTime;

    @ApiModelProperty("货期跟进备注")
    private String trackLeadTimeRemark;


    private String changeProtocolFile;

    private List<Long> projectIds;

    private List<Long> ouIds;

    @ApiModelProperty("采购合同累计进度执行百分比最小值")
    private BigDecimal executeContractPercentTotalMin;

    @ApiModelProperty("采购合同累计进度执行百分比最大值")
    private BigDecimal executeContractPercentTotalMax;

    @ApiModelProperty("是否完成质检")
    private Integer qualityFlag;

    @ApiModelProperty("查看我的合同")
    private Boolean showMyContract ;

    @ApiModelProperty("是否更新fileAttachmentId")
    private Boolean isUpdate ;

    private String typeNameStr;

    private List<PurchaseMaterialRequirementDto> dataList;

    @ApiModelProperty(value = "罚扣记录id集合")
    private List<Long> punishmentIds;

    @ApiModelProperty(value = "合同印章管理员id 多个人员-变更时")
    private String sealAdministratorChangeIds;

    @ApiModelProperty(value = "预算类型是否项目 1:项目 2:非项目")
    private Boolean noCtrlModel;

    @ApiModelProperty(value = "是否罚扣查询")
    private Integer punishmentFlag;

    @ApiModelProperty(value = "合同罚扣金额(含税)")
    private BigDecimal punishmentAmount;

    @ApiModelProperty(value = "是否已双签")
    private Boolean isGleSign;

    @ApiModelProperty(value = "采购合同标准条款信息")
    private List<PurchaseContractStandardTermsDto> standardTermsDtoList;
}
