package com.midea.pam.common.ctc.vo;

import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDto;
import com.midea.pam.common.ctc.dto.PaymentPlanDTO;
import com.midea.pam.common.ctc.dto.PurchaseContractBudgetDto;
import com.midea.pam.common.ctc.dto.PurchaseContractDetailDTO;
import com.midea.pam.common.ctc.dto.PurchaseContractStandardTermsDto;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractPenalty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-6-3
 * @description 采购合同视图对象
 */
@Getter
@Setter
public class PurchaseContractVO extends PurchaseContract implements Serializable {

    private static final long serialVersionUID = -3382930132025064680L;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("项目ID")
    private Long projectId;

    @ApiModelProperty("合同编号")
    private String code;

    @ApiModelProperty("合同名称")
    private String name;

    @ApiModelProperty("所属业务实体ID")
    private Long ouId;

    @ApiModelProperty("合同类型ID")
    private Long typeId;

    @ApiModelProperty("合同类型名称")
    private String typeName;

    @ApiModelProperty("法务合同编号")
    private String legalAffairsCode;

    @ApiModelProperty("供应商ID")
    private Long vendorId;

    @ApiModelProperty("供应商Code")
    private String vendorCode;

    @ApiModelProperty("供应商名称")
    private String vendorName;

    @ApiModelProperty("供应商类型")
    private String vendorTypeLookupCode;

    @ApiModelProperty("供应商类型名称")
    private String vendorTypeName;

    @ApiModelProperty("供应商地点ID")
    private String erpVendorSiteId;

    @ApiModelProperty("供应商地点")
    private String vendorSiteCode;

    @ApiModelProperty("合同含税金额")
    private BigDecimal amount;

    @ApiModelProperty("合同不含税金额")
    private BigDecimal excludingTaxAmount;

    @ApiModelProperty(value = "未入账应付发票金额（含税）", notes = "采购合同列表查询、导出")
    private BigDecimal unEntrypayableInvoiceAmount;

    @ApiModelProperty(value = "已入账应付发票金额（含税）", notes = "采购合同列表查询、导出")
    private BigDecimal entrypayableInvoiceAmount;

    @ApiModelProperty(value = "付款比例", notes = "采购合同列表查询、导出")
    private BigDecimal paymentRatio;

    @ApiModelProperty(value = "欠款金额", notes = "采购合同列表查询、导出")
    private BigDecimal amountInArrear;

    @ApiModelProperty("已付款")
    private BigDecimal actualAmount;

    @ApiModelProperty("罚扣")
    private BigDecimal punishmentAmount;

    @ApiModelProperty("剩余付款")
    private BigDecimal remainingAmount;

    @ApiModelProperty("合同有效期")
    private String effectiveDate;

    @ApiModelProperty("币种")
    private String currency;

    @ApiModelProperty("合同开始时间")
    private Date startTime;

    @ApiModelProperty("合同结束时间")
    private Date endTime;

    @ApiModelProperty("项目经理")
    private Long manager;

    @ApiModelProperty("采购跟进人")
    private Long purchasingFollower;

    @ApiModelProperty("附件，多个用,隔开")
    private String annex;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String projectCode;

    @ApiModelProperty("项目状态")
    private Integer projectStatus;

    @ApiModelProperty("项目经理名称")
    private String managerName;

    @ApiModelProperty("法务合同编号")
    private String fdNumber;

    @ApiModelProperty("法务合同名称")
    private String docSubject;

    @ApiModelProperty("采购跟进人名称")
    private String purchasingFollowerName;

    @ApiModelProperty("所属业务实体名称")
    private String ouName;

    @ApiModelProperty("合同状态")
    private Integer status;

    @ApiModelProperty("归档日期")
    private Date filingDate;

    @ApiModelProperty("是否参与结转")
    private Boolean carryoverFlag;

    @ApiModelProperty("税率ID")
    private Long taxId;

    @ApiModelProperty("税率")
    private String taxRate;

    @ApiModelProperty("是否存在付款申请")
    private Boolean isExitPaymentApply;

    @ApiModelProperty("是否存在结转")
    private Boolean isExitCarryBill;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建人名称")
    private String createByName;

    @ApiModelProperty("创建日期")
    private Date createAt;

    @ApiModelProperty(value = "供应商账户失效日期")
    private Date accountInactiveDate;

    @ApiModelProperty(value = "供应商分行失效日期")
    private Date bankInactiveDate;

    @ApiModelProperty(value = "供应商地点状态")
    private String pvsStatus;

    @ApiModelProperty(value = "供应商生效日期")
    private Date startDateActive;

    @ApiModelProperty(value = "供应商失效日期")
    private Date endDateActive;

    @ApiModelProperty("供应商统一社会信用代码")
    private String uniteCreditCode;

    @ApiModelProperty("采购内容")
    private List<PurchaseContractDetailDTO> purchaseContractDetails;

    @ApiModelProperty("付款计划")
    private List<PaymentPlanDTO> paymentPlans;

    @ApiModelProperty("罚扣信息")
    private List<PurchaseContractPenalty> purchaseContractPenalties;

    @ApiModelProperty("关联预算")
    private List<ProjectContractBudgetMaterialVo> projectContractBudgetMaterialVos;

    @ApiModelProperty("wbs关联预算")
    private List<PurchaseContractBudgetDto> purchaseContractWbsBudgets;

    @ApiModelProperty("关联税票")
    private List<PaymentInvoiceDetailDto> paymentInvoiceDetails;

    @ApiModelProperty("关联应付发票")
    private List<PaymentInvoiceDto> paymentInvoices;

    @ApiModelProperty("关联付款")
    private List<PaymentApplyDto> paymentApplies;

    @ApiModelProperty("预算材料总价格")
    private BigDecimal budgetTotalBalance;

    @ApiModelProperty("关联预算")
    private String relevanceBudget;

    @ApiModelProperty("已开税票")
    private BigDecimal paymentInvoiceMoney;

    @ApiModelProperty(value = "未开税票金额（含税）", notes = "采购合同列表查询、导出")
    private BigDecimal unPaymentInvoiceMoney;

    @ApiModelProperty("预算占用总额（不含税）-本位币")
    private BigDecimal excludingTaxTotalBudgetAmount;

    @ApiModelProperty("已入账税票金额(不含税)")
    private BigDecimal amountOfTaxReceipts;

    @ApiModelProperty("本次入账税票金额（不含税）")
    private BigDecimal theAmountOfTheTaxReceipt;

    @ApiModelProperty("剩余可入账税票金额（不含税）")
    private BigDecimal remainingCreditableTaxInvoiceAmount;

    @ApiModelProperty("采购合同进度执行金额（不含税）")
    private BigDecimal purchasingContractProgressAmount;

    @ApiModelProperty(value = "累计进度执行金额（不含税）", notes = "采购合同列表查询")
    private BigDecimal executeAmountTotal;

    @ApiModelProperty(value = "质检报告数量", notes = "采购合同列表查询")
    private Integer qualityReportNumber;

    @ApiModelProperty(value = "是否完成质检", notes = "采购合同列表查询")
    private Boolean qualityReportFlag;

    @ApiModelProperty("是否启用wbs")
    private Boolean wbsEnabled;

    @ApiModelProperty(value = "付款比例", notes = "采购合同列表查询、导出")
    private String paymentRate;

    @ApiModelProperty(value = "罚扣列表")
    private List<PurchaseContractPunishmentVo> punishmentList;

    @ApiModelProperty(value = "采购合同金额(减去罚扣金额) 原币")
    private BigDecimal exclusionPunishmentAmount;

    @ApiModelProperty(value = "采购合同金额(减去罚扣金额) 本位币")
    private BigDecimal localExclusionPunishmentAmount;

    @ApiModelProperty(value = "合同原件Id，取最后一个")
    private String originalContractAnnexId;

    @ApiModelProperty(value = "合同原件名称，取最后一个")
    private String originalContractAnnexName;

    @ApiModelProperty(value = "是否已双签")
    private Boolean isGleSign;

    @ApiModelProperty(value = "标准条款信息")
    private List<PurchaseContractStandardTermsDto> standardTermsDtoList;

    public String getPaymentRate() {
        if (Objects.nonNull(this.getPaymentRatio())) {
            return getPaymentRatio().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%";
        }
        return this.paymentRate;
    }
}