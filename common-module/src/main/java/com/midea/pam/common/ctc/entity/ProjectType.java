package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "")
public class ProjectType extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "项目类型")
    private String name;

    private String code;

    @ApiModelProperty(value = "成本方法")
    private String costMethod;

    private Integer orderNum;

    private String description;

    @ApiModelProperty(value = "预算部门ID")
    private Long budgetDepId;

    @ApiModelProperty(value = "预算部门名称")
    private String budgetDepName;

    @ApiModelProperty(value = "是否自动生成里程碑")
    private Boolean autoMilepost;

    @ApiModelProperty(value = "是否校验预算人力")
    private Boolean validateBudgetHumans;

    @ApiModelProperty(value = "是否校验预算差旅")
    private Boolean validateBudgetTravels;

    @ApiModelProperty(value = "是否校验预算非差旅")
    private Boolean validateBudgetFees;

    @ApiModelProperty(value = "校验预算物料")
    private Boolean validateBudgetMaterials;

    @ApiModelProperty(value = "是否校验合同")
    private Boolean validateContract;

    @ApiModelProperty(value = "是否校验辅里程碑")
    private Boolean validateMiplepostHelp;

    @ApiModelProperty(value = "是否校验里程碑主")
    private Boolean validateMiplepostMain;

    @ApiModelProperty(value = "是否校验商机")
    private Boolean validateBusiness;

    @ApiModelProperty(value = "是否校验财务")
    private Boolean validateFinancal;

    @ApiModelProperty(value = "控制年度预算")
    private Boolean validateYearBudget;

    @ApiModelProperty(value = "校验是否走mip")
    private Boolean validateWf;

    @ApiModelProperty(value = "校验金额")
    private Boolean validateAmount;

    @ApiModelProperty(value = "收入确认时点")
    private String incomeMethod;

    @ApiModelProperty(value = "收入确认点，0001表示一次性确认，0002表示里程碑节点确认，0003表示月度确认")
    private String incomePoint;

    private Long milepostTemplateId;

    @ApiModelProperty(value = "虚拟单位(业务分类)")
    private Long unitId;

    private BigDecimal taxRate;

    private String categoryName;

    private String categoryTag;

    @ApiModelProperty(value = "是否删除 0否/1是")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "是否创建货位")
    private Boolean locatorFlag;

    @ApiModelProperty(value = "是否校验预立项")
    private Boolean validatePreview;

    @ApiModelProperty(value = "是否校验项目类型")
    private Boolean validateProjectType;

    @ApiModelProperty(value = "是否校验项目编号")
    private Boolean validateCode;

    @ApiModelProperty(value = "是否校验项目名称")
    private Boolean validateName;

    @ApiModelProperty(value = "是否校验项目经理")
    private Boolean validateManager;

    @ApiModelProperty(value = "是否校验项目属性")
    private Boolean validatePriceType;

    @ApiModelProperty(value = "是否校验客户")
    private Boolean validateCustomer;

    @ApiModelProperty(value = "是否校验产品")
    private Boolean validateProduct;

    @ApiModelProperty(value = "是否校验业务分类")
    private Boolean validateOu;

    @ApiModelProperty(value = "是否校验项目概述")
    private Boolean validateSummary;

    @ApiModelProperty(value = "是否校验项目类型")
    private Boolean validateType;

    @ApiModelProperty(value = "主收入确认标志")
    private Boolean mainIncomeFlag;

    @ApiModelProperty(value = "辅收入确认标志")
    private Boolean helpIncomeFlag;

    @ApiModelProperty(value = "主收入科目组合")
    private String mainIncomeSubject;

    @ApiModelProperty(value = "辅收入科目组合")
    private String helpIncomeSubject;

    @ApiModelProperty(value = "允许立项时上传详细设计方案")
    private Boolean milepostDesignCanSubmit;

    @ApiModelProperty(value = "启用里程碑基准日期")
    private Boolean milestoneBaseDate;

    @ApiModelProperty(value = "人员一天只能在一个项目")
    private Boolean projectMemberDistinct;

    @ApiModelProperty(value = "是否校验战略投入项目")
    private Boolean validateObjectiveProject;

    @ApiModelProperty(value = "是否校验项目级别")
    private Boolean validateProjectLevel;

    @ApiModelProperty(value = "是否校验资源计划")
    private Boolean validateResourceCode;

    @ApiModelProperty(value = "是否校验人员投入区间")
    private Boolean validateProjectMember;

    @ApiModelProperty(value = "（弃用，参考budget_config字段）预算控制: 1-严控、2-提醒、3-不控")
    private Integer budgetControlFlag;

    @ApiModelProperty(value = "（弃用，参考budget_config字段）项目预算控制比例(%)")
    private BigDecimal budgetControlRatio;

    @ApiModelProperty(value = "是否手工立项")
    private Boolean validateProjectManual;

    @ApiModelProperty(value = "需求传递MRP，默认否，0否，1是")
    private Boolean requriementDeliverMrp;

    @ApiModelProperty(value = "转质保项目，0否，1是")
    private Boolean transferProject;

    @ApiModelProperty(value = "转质保项目类型id")
    private Long transferProjectTypeId;

    @ApiModelProperty(value = "启用项目成员利润贡献率")
    private Boolean projectProfitContributionRate;

    @ApiModelProperty(value = "采购合同类型")
    private String purchaseContractType;

    @ApiModelProperty(value = "启用WBS（0否1是）")
    private Boolean wbsEnabled;

    @ApiModelProperty(value = "wbs模板信息id")
    private Long wbsTemplateInfoId;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "事业部")
    private String businessDepartment;

    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "关联资产，0否，1是")
    private Boolean relateAsset;

    @ApiModelProperty(value = "是否启用项目进度预测")
    private Boolean projectProgressPredictFlag;

    @ApiModelProperty(value = "立项后详细设计模组默认状态，0未确认，1已确认")
    private Integer designPlanModuleStatusDefault;

    @ApiModelProperty(value = "按WBS自动部分确认")
    private Boolean wbsDesignPlanAutoConfirm;

    @ApiModelProperty(value = "项目字段")
    private String fieldConfig;

    @ApiModelProperty(value = "预算设置")
    private String budgetConfig;

    @ApiModelProperty(value = "编码规则")
    private String codeRuleConfig;

    @ApiModelProperty(value = "工单任务设置")
    private String workOrderTaskConfig;

    @ApiModelProperty(value = "项目字段（会计科目）")
    private String accountingConfig;

    @ApiModelProperty(value = "科目设置")
    private String subjectConfig;

    private static final long serialVersionUID = 1L;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getCostMethod() {
        return costMethod;
    }

    public void setCostMethod(String costMethod) {
        this.costMethod = costMethod == null ? null : costMethod.trim();
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Long getBudgetDepId() {
        return budgetDepId;
    }

    public void setBudgetDepId(Long budgetDepId) {
        this.budgetDepId = budgetDepId;
    }

    public String getBudgetDepName() {
        return budgetDepName;
    }

    public void setBudgetDepName(String budgetDepName) {
        this.budgetDepName = budgetDepName == null ? null : budgetDepName.trim();
    }

    public Boolean getAutoMilepost() {
        return autoMilepost;
    }

    public void setAutoMilepost(Boolean autoMilepost) {
        this.autoMilepost = autoMilepost;
    }

    public Boolean getValidateBudgetHumans() {
        return validateBudgetHumans;
    }

    public void setValidateBudgetHumans(Boolean validateBudgetHumans) {
        this.validateBudgetHumans = validateBudgetHumans;
    }

    public Boolean getValidateBudgetTravels() {
        return validateBudgetTravels;
    }

    public void setValidateBudgetTravels(Boolean validateBudgetTravels) {
        this.validateBudgetTravels = validateBudgetTravels;
    }

    public Boolean getValidateBudgetFees() {
        return validateBudgetFees;
    }

    public void setValidateBudgetFees(Boolean validateBudgetFees) {
        this.validateBudgetFees = validateBudgetFees;
    }

    public Boolean getValidateBudgetMaterials() {
        return validateBudgetMaterials;
    }

    public void setValidateBudgetMaterials(Boolean validateBudgetMaterials) {
        this.validateBudgetMaterials = validateBudgetMaterials;
    }

    public Boolean getValidateContract() {
        return validateContract;
    }

    public void setValidateContract(Boolean validateContract) {
        this.validateContract = validateContract;
    }

    public Boolean getValidateMiplepostHelp() {
        return validateMiplepostHelp;
    }

    public void setValidateMiplepostHelp(Boolean validateMiplepostHelp) {
        this.validateMiplepostHelp = validateMiplepostHelp;
    }

    public Boolean getValidateMiplepostMain() {
        return validateMiplepostMain;
    }

    public void setValidateMiplepostMain(Boolean validateMiplepostMain) {
        this.validateMiplepostMain = validateMiplepostMain;
    }

    public Boolean getValidateBusiness() {
        return validateBusiness;
    }

    public void setValidateBusiness(Boolean validateBusiness) {
        this.validateBusiness = validateBusiness;
    }

    public Boolean getValidateFinancal() {
        return validateFinancal;
    }

    public void setValidateFinancal(Boolean validateFinancal) {
        this.validateFinancal = validateFinancal;
    }

    public Boolean getValidateYearBudget() {
        return validateYearBudget;
    }

    public void setValidateYearBudget(Boolean validateYearBudget) {
        this.validateYearBudget = validateYearBudget;
    }

    public Boolean getValidateWf() {
        return validateWf;
    }

    public void setValidateWf(Boolean validateWf) {
        this.validateWf = validateWf;
    }

    public Boolean getValidateAmount() {
        return validateAmount;
    }

    public void setValidateAmount(Boolean validateAmount) {
        this.validateAmount = validateAmount;
    }

    public String getIncomeMethod() {
        return incomeMethod;
    }

    public void setIncomeMethod(String incomeMethod) {
        this.incomeMethod = incomeMethod == null ? null : incomeMethod.trim();
    }

    public String getIncomePoint() {
        return incomePoint;
    }

    public void setIncomePoint(String incomePoint) {
        this.incomePoint = incomePoint == null ? null : incomePoint.trim();
    }

    public Long getMilepostTemplateId() {
        return milepostTemplateId;
    }

    public void setMilepostTemplateId(Long milepostTemplateId) {
        this.milepostTemplateId = milepostTemplateId;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag == null ? null : categoryTag.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Boolean getLocatorFlag() {
        return locatorFlag;
    }

    public void setLocatorFlag(Boolean locatorFlag) {
        this.locatorFlag = locatorFlag;
    }

    public Boolean getValidatePreview() {
        return validatePreview;
    }

    public void setValidatePreview(Boolean validatePreview) {
        this.validatePreview = validatePreview;
    }

    public Boolean getValidateProjectType() {
        return validateProjectType;
    }

    public void setValidateProjectType(Boolean validateProjectType) {
        this.validateProjectType = validateProjectType;
    }

    public Boolean getValidateCode() {
        return validateCode;
    }

    public void setValidateCode(Boolean validateCode) {
        this.validateCode = validateCode;
    }

    public Boolean getValidateName() {
        return validateName;
    }

    public void setValidateName(Boolean validateName) {
        this.validateName = validateName;
    }

    public Boolean getValidateManager() {
        return validateManager;
    }

    public void setValidateManager(Boolean validateManager) {
        this.validateManager = validateManager;
    }

    public Boolean getValidatePriceType() {
        return validatePriceType;
    }

    public void setValidatePriceType(Boolean validatePriceType) {
        this.validatePriceType = validatePriceType;
    }

    public Boolean getValidateCustomer() {
        return validateCustomer;
    }

    public void setValidateCustomer(Boolean validateCustomer) {
        this.validateCustomer = validateCustomer;
    }

    public Boolean getValidateProduct() {
        return validateProduct;
    }

    public void setValidateProduct(Boolean validateProduct) {
        this.validateProduct = validateProduct;
    }

    public Boolean getValidateOu() {
        return validateOu;
    }

    public void setValidateOu(Boolean validateOu) {
        this.validateOu = validateOu;
    }

    public Boolean getValidateSummary() {
        return validateSummary;
    }

    public void setValidateSummary(Boolean validateSummary) {
        this.validateSummary = validateSummary;
    }

    public Boolean getValidateType() {
        return validateType;
    }

    public void setValidateType(Boolean validateType) {
        this.validateType = validateType;
    }

    public Boolean getMainIncomeFlag() {
        return mainIncomeFlag;
    }

    public void setMainIncomeFlag(Boolean mainIncomeFlag) {
        this.mainIncomeFlag = mainIncomeFlag;
    }

    public Boolean getHelpIncomeFlag() {
        return helpIncomeFlag;
    }

    public void setHelpIncomeFlag(Boolean helpIncomeFlag) {
        this.helpIncomeFlag = helpIncomeFlag;
    }

    public String getMainIncomeSubject() {
        return mainIncomeSubject;
    }

    public void setMainIncomeSubject(String mainIncomeSubject) {
        this.mainIncomeSubject = mainIncomeSubject == null ? null : mainIncomeSubject.trim();
    }

    public String getHelpIncomeSubject() {
        return helpIncomeSubject;
    }

    public void setHelpIncomeSubject(String helpIncomeSubject) {
        this.helpIncomeSubject = helpIncomeSubject == null ? null : helpIncomeSubject.trim();
    }

    public Boolean getMilepostDesignCanSubmit() {
        return milepostDesignCanSubmit;
    }

    public void setMilepostDesignCanSubmit(Boolean milepostDesignCanSubmit) {
        this.milepostDesignCanSubmit = milepostDesignCanSubmit;
    }

    public Boolean getMilestoneBaseDate() {
        return milestoneBaseDate;
    }

    public void setMilestoneBaseDate(Boolean milestoneBaseDate) {
        this.milestoneBaseDate = milestoneBaseDate;
    }

    public Boolean getProjectMemberDistinct() {
        return projectMemberDistinct;
    }

    public void setProjectMemberDistinct(Boolean projectMemberDistinct) {
        this.projectMemberDistinct = projectMemberDistinct;
    }

    public Boolean getValidateObjectiveProject() {
        return validateObjectiveProject;
    }

    public void setValidateObjectiveProject(Boolean validateObjectiveProject) {
        this.validateObjectiveProject = validateObjectiveProject;
    }

    public Boolean getValidateProjectLevel() {
        return validateProjectLevel;
    }

    public void setValidateProjectLevel(Boolean validateProjectLevel) {
        this.validateProjectLevel = validateProjectLevel;
    }

    public Boolean getValidateResourceCode() {
        return validateResourceCode;
    }

    public void setValidateResourceCode(Boolean validateResourceCode) {
        this.validateResourceCode = validateResourceCode;
    }

    public Boolean getValidateProjectMember() {
        return validateProjectMember;
    }

    public void setValidateProjectMember(Boolean validateProjectMember) {
        this.validateProjectMember = validateProjectMember;
    }

    public Integer getBudgetControlFlag() {
        return budgetControlFlag;
    }

    public void setBudgetControlFlag(Integer budgetControlFlag) {
        this.budgetControlFlag = budgetControlFlag;
    }

    public BigDecimal getBudgetControlRatio() {
        return budgetControlRatio;
    }

    public void setBudgetControlRatio(BigDecimal budgetControlRatio) {
        this.budgetControlRatio = budgetControlRatio;
    }

    public Boolean getValidateProjectManual() {
        return validateProjectManual;
    }

    public void setValidateProjectManual(Boolean validateProjectManual) {
        this.validateProjectManual = validateProjectManual;
    }

    public Boolean getRequriementDeliverMrp() {
        return requriementDeliverMrp;
    }

    public void setRequriementDeliverMrp(Boolean requriementDeliverMrp) {
        this.requriementDeliverMrp = requriementDeliverMrp;
    }

    public Boolean getTransferProject() {
        return transferProject;
    }

    public void setTransferProject(Boolean transferProject) {
        this.transferProject = transferProject;
    }

    public Long getTransferProjectTypeId() {
        return transferProjectTypeId;
    }

    public void setTransferProjectTypeId(Long transferProjectTypeId) {
        this.transferProjectTypeId = transferProjectTypeId;
    }

    public Boolean getProjectProfitContributionRate() {
        return projectProfitContributionRate;
    }

    public void setProjectProfitContributionRate(Boolean projectProfitContributionRate) {
        this.projectProfitContributionRate = projectProfitContributionRate;
    }

    public String getPurchaseContractType() {
        return purchaseContractType;
    }

    public void setPurchaseContractType(String purchaseContractType) {
        this.purchaseContractType = purchaseContractType == null ? null : purchaseContractType.trim();
    }

    public Boolean getWbsEnabled() {
        return wbsEnabled;
    }

    public void setWbsEnabled(Boolean wbsEnabled) {
        this.wbsEnabled = wbsEnabled;
    }

    public Long getWbsTemplateInfoId() {
        return wbsTemplateInfoId;
    }

    public void setWbsTemplateInfoId(Long wbsTemplateInfoId) {
        this.wbsTemplateInfoId = wbsTemplateInfoId;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area == null ? null : area.trim();
    }

    public String getBusinessDepartment() {
        return businessDepartment;
    }

    public void setBusinessDepartment(String businessDepartment) {
        this.businessDepartment = businessDepartment == null ? null : businessDepartment.trim();
    }

    public String getDetailedAddress() {
        return detailedAddress;
    }

    public void setDetailedAddress(String detailedAddress) {
        this.detailedAddress = detailedAddress == null ? null : detailedAddress.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public Boolean getRelateAsset() {
        return relateAsset;
    }

    public void setRelateAsset(Boolean relateAsset) {
        this.relateAsset = relateAsset;
    }

    public Boolean getProjectProgressPredictFlag() {
        return projectProgressPredictFlag;
    }

    public void setProjectProgressPredictFlag(Boolean projectProgressPredictFlag) {
        this.projectProgressPredictFlag = projectProgressPredictFlag;
    }

    public Integer getDesignPlanModuleStatusDefault() {
        return designPlanModuleStatusDefault;
    }

    public void setDesignPlanModuleStatusDefault(Integer designPlanModuleStatusDefault) {
        this.designPlanModuleStatusDefault = designPlanModuleStatusDefault;
    }

    public Boolean getWbsDesignPlanAutoConfirm() {
        return wbsDesignPlanAutoConfirm;
    }

    public void setWbsDesignPlanAutoConfirm(Boolean wbsDesignPlanAutoConfirm) {
        this.wbsDesignPlanAutoConfirm = wbsDesignPlanAutoConfirm;
    }

    public String getFieldConfig() {
        return fieldConfig;
    }

    public void setFieldConfig(String fieldConfig) {
        this.fieldConfig = fieldConfig == null ? null : fieldConfig.trim();
    }

    public String getBudgetConfig() {
        return budgetConfig;
    }

    public void setBudgetConfig(String budgetConfig) {
        this.budgetConfig = budgetConfig == null ? null : budgetConfig.trim();
    }

    public String getCodeRuleConfig() {
        return codeRuleConfig;
    }

    public void setCodeRuleConfig(String codeRuleConfig) {
        this.codeRuleConfig = codeRuleConfig == null ? null : codeRuleConfig.trim();
    }

    public String getWorkOrderTaskConfig() {
        return workOrderTaskConfig;
    }

    public void setWorkOrderTaskConfig(String workOrderTaskConfig) {
        this.workOrderTaskConfig = workOrderTaskConfig == null ? null : workOrderTaskConfig.trim();
    }

    public String getAccountingConfig() {
        return accountingConfig;
    }

    public void setAccountingConfig(String accountingConfig) {
        this.accountingConfig = accountingConfig == null ? null : accountingConfig.trim();
    }

    public String getSubjectConfig() {
        return subjectConfig;
    }

    public void setSubjectConfig(String subjectConfig) {
        this.subjectConfig = subjectConfig == null ? null : subjectConfig.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", name=").append(name);
        sb.append(", code=").append(code);
        sb.append(", costMethod=").append(costMethod);
        sb.append(", orderNum=").append(orderNum);
        sb.append(", description=").append(description);
        sb.append(", budgetDepId=").append(budgetDepId);
        sb.append(", budgetDepName=").append(budgetDepName);
        sb.append(", autoMilepost=").append(autoMilepost);
        sb.append(", validateBudgetHumans=").append(validateBudgetHumans);
        sb.append(", validateBudgetTravels=").append(validateBudgetTravels);
        sb.append(", validateBudgetFees=").append(validateBudgetFees);
        sb.append(", validateBudgetMaterials=").append(validateBudgetMaterials);
        sb.append(", validateContract=").append(validateContract);
        sb.append(", validateMiplepostHelp=").append(validateMiplepostHelp);
        sb.append(", validateMiplepostMain=").append(validateMiplepostMain);
        sb.append(", validateBusiness=").append(validateBusiness);
        sb.append(", validateFinancal=").append(validateFinancal);
        sb.append(", validateYearBudget=").append(validateYearBudget);
        sb.append(", validateWf=").append(validateWf);
        sb.append(", validateAmount=").append(validateAmount);
        sb.append(", incomeMethod=").append(incomeMethod);
        sb.append(", incomePoint=").append(incomePoint);
        sb.append(", milepostTemplateId=").append(milepostTemplateId);
        sb.append(", unitId=").append(unitId);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", categoryName=").append(categoryName);
        sb.append(", categoryTag=").append(categoryTag);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", locatorFlag=").append(locatorFlag);
        sb.append(", validatePreview=").append(validatePreview);
        sb.append(", validateProjectType=").append(validateProjectType);
        sb.append(", validateCode=").append(validateCode);
        sb.append(", validateName=").append(validateName);
        sb.append(", validateManager=").append(validateManager);
        sb.append(", validatePriceType=").append(validatePriceType);
        sb.append(", validateCustomer=").append(validateCustomer);
        sb.append(", validateProduct=").append(validateProduct);
        sb.append(", validateOu=").append(validateOu);
        sb.append(", validateSummary=").append(validateSummary);
        sb.append(", validateType=").append(validateType);
        sb.append(", mainIncomeFlag=").append(mainIncomeFlag);
        sb.append(", helpIncomeFlag=").append(helpIncomeFlag);
        sb.append(", mainIncomeSubject=").append(mainIncomeSubject);
        sb.append(", helpIncomeSubject=").append(helpIncomeSubject);
        sb.append(", milepostDesignCanSubmit=").append(milepostDesignCanSubmit);
        sb.append(", milestoneBaseDate=").append(milestoneBaseDate);
        sb.append(", projectMemberDistinct=").append(projectMemberDistinct);
        sb.append(", validateObjectiveProject=").append(validateObjectiveProject);
        sb.append(", validateProjectLevel=").append(validateProjectLevel);
        sb.append(", validateResourceCode=").append(validateResourceCode);
        sb.append(", validateProjectMember=").append(validateProjectMember);
        sb.append(", budgetControlFlag=").append(budgetControlFlag);
        sb.append(", budgetControlRatio=").append(budgetControlRatio);
        sb.append(", validateProjectManual=").append(validateProjectManual);
        sb.append(", requriementDeliverMrp=").append(requriementDeliverMrp);
        sb.append(", transferProject=").append(transferProject);
        sb.append(", transferProjectTypeId=").append(transferProjectTypeId);
        sb.append(", projectProfitContributionRate=").append(projectProfitContributionRate);
        sb.append(", purchaseContractType=").append(purchaseContractType);
        sb.append(", wbsEnabled=").append(wbsEnabled);
        sb.append(", wbsTemplateInfoId=").append(wbsTemplateInfoId);
        sb.append(", area=").append(area);
        sb.append(", businessDepartment=").append(businessDepartment);
        sb.append(", detailedAddress=").append(detailedAddress);
        sb.append(", city=").append(city);
        sb.append(", relateAsset=").append(relateAsset);
        sb.append(", projectProgressPredictFlag=").append(projectProgressPredictFlag);
        sb.append(", designPlanModuleStatusDefault=").append(designPlanModuleStatusDefault);
        sb.append(", wbsDesignPlanAutoConfirm=").append(wbsDesignPlanAutoConfirm);
        sb.append(", fieldConfig=").append(fieldConfig);
        sb.append(", budgetConfig=").append(budgetConfig);
        sb.append(", codeRuleConfig=").append(codeRuleConfig);
        sb.append(", workOrderTaskConfig=").append(workOrderTaskConfig);
        sb.append(", accountingConfig=").append(accountingConfig);
        sb.append(", subjectConfig=").append(subjectConfig);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}