package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WorkingHourExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WorkingHourExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andWeekIsNull() {
            addCriterion("week is null");
            return (Criteria) this;
        }

        public Criteria andWeekIsNotNull() {
            addCriterion("week is not null");
            return (Criteria) this;
        }

        public Criteria andWeekEqualTo(String value) {
            addCriterion("week =", value, "week");
            return (Criteria) this;
        }

        public Criteria andWeekNotEqualTo(String value) {
            addCriterion("week <>", value, "week");
            return (Criteria) this;
        }

        public Criteria andWeekGreaterThan(String value) {
            addCriterion("week >", value, "week");
            return (Criteria) this;
        }

        public Criteria andWeekGreaterThanOrEqualTo(String value) {
            addCriterion("week >=", value, "week");
            return (Criteria) this;
        }

        public Criteria andWeekLessThan(String value) {
            addCriterion("week <", value, "week");
            return (Criteria) this;
        }

        public Criteria andWeekLessThanOrEqualTo(String value) {
            addCriterion("week <=", value, "week");
            return (Criteria) this;
        }

        public Criteria andWeekLike(String value) {
            addCriterion("week like", value, "week");
            return (Criteria) this;
        }

        public Criteria andWeekNotLike(String value) {
            addCriterion("week not like", value, "week");
            return (Criteria) this;
        }

        public Criteria andWeekIn(List<String> values) {
            addCriterion("week in", values, "week");
            return (Criteria) this;
        }

        public Criteria andWeekNotIn(List<String> values) {
            addCriterion("week not in", values, "week");
            return (Criteria) this;
        }

        public Criteria andWeekBetween(String value1, String value2) {
            addCriterion("week between", value1, value2, "week");
            return (Criteria) this;
        }

        public Criteria andWeekNotBetween(String value1, String value2) {
            addCriterion("week not between", value1, value2, "week");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNull() {
            addCriterion("apply_date is null");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNotNull() {
            addCriterion("apply_date is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDateEqualTo(Date value) {
            addCriterion("apply_date =", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotEqualTo(Date value) {
            addCriterion("apply_date <>", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThan(Date value) {
            addCriterion("apply_date >", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThanOrEqualTo(Date value) {
            addCriterion("apply_date >=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThan(Date value) {
            addCriterion("apply_date <", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThanOrEqualTo(Date value) {
            addCriterion("apply_date <=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateIn(List<Date> values) {
            addCriterion("apply_date in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotIn(List<Date> values) {
            addCriterion("apply_date not in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateBetween(Date value1, Date value2) {
            addCriterion("apply_date between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotBetween(Date value1, Date value2) {
            addCriterion("apply_date not between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursIsNull() {
            addCriterion("apply_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursIsNotNull() {
            addCriterion("apply_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours =", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours <>", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("apply_working_hours >", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours >=", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursLessThan(BigDecimal value) {
            addCriterion("apply_working_hours <", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("apply_working_hours <=", value, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("apply_working_hours in", values, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("apply_working_hours not in", values, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_working_hours between", value1, value2, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andApplyWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("apply_working_hours not between", value1, value2, "applyWorkingHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIsNull() {
            addCriterion("ihr_attend_hours is null");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIsNotNull() {
            addCriterion("ihr_attend_hours is not null");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours =", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours <>", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursGreaterThan(BigDecimal value) {
            addCriterion("ihr_attend_hours >", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours >=", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursLessThan(BigDecimal value) {
            addCriterion("ihr_attend_hours <", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ihr_attend_hours <=", value, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursIn(List<BigDecimal> values) {
            addCriterion("ihr_attend_hours in", values, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotIn(List<BigDecimal> values) {
            addCriterion("ihr_attend_hours not in", values, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ihr_attend_hours between", value1, value2, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andIhrAttendHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ihr_attend_hours not between", value1, value2, "ihrAttendHours");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserMipIsNull() {
            addCriterion("user_mip is null");
            return (Criteria) this;
        }

        public Criteria andUserMipIsNotNull() {
            addCriterion("user_mip is not null");
            return (Criteria) this;
        }

        public Criteria andUserMipEqualTo(String value) {
            addCriterion("user_mip =", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotEqualTo(String value) {
            addCriterion("user_mip <>", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipGreaterThan(String value) {
            addCriterion("user_mip >", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipGreaterThanOrEqualTo(String value) {
            addCriterion("user_mip >=", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLessThan(String value) {
            addCriterion("user_mip <", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLessThanOrEqualTo(String value) {
            addCriterion("user_mip <=", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipLike(String value) {
            addCriterion("user_mip like", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotLike(String value) {
            addCriterion("user_mip not like", value, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipIn(List<String> values) {
            addCriterion("user_mip in", values, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotIn(List<String> values) {
            addCriterion("user_mip not in", values, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipBetween(String value1, String value2) {
            addCriterion("user_mip between", value1, value2, "userMip");
            return (Criteria) this;
        }

        public Criteria andUserMipNotBetween(String value1, String value2) {
            addCriterion("user_mip not between", value1, value2, "userMip");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("org_id is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(Long value) {
            addCriterion("org_id =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(Long value) {
            addCriterion("org_id <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(Long value) {
            addCriterion("org_id >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(Long value) {
            addCriterion("org_id >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(Long value) {
            addCriterion("org_id <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(Long value) {
            addCriterion("org_id <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<Long> values) {
            addCriterion("org_id in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<Long> values) {
            addCriterion("org_id not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(Long value1, Long value2) {
            addCriterion("org_id between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(Long value1, Long value2) {
            addCriterion("org_id not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNull() {
            addCriterion("org_name is null");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNotNull() {
            addCriterion("org_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNameEqualTo(String value) {
            addCriterion("org_name =", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotEqualTo(String value) {
            addCriterion("org_name <>", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThan(String value) {
            addCriterion("org_name >", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("org_name >=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThan(String value) {
            addCriterion("org_name <", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThanOrEqualTo(String value) {
            addCriterion("org_name <=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLike(String value) {
            addCriterion("org_name like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotLike(String value) {
            addCriterion("org_name not like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameIn(List<String> values) {
            addCriterion("org_name in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotIn(List<String> values) {
            addCriterion("org_name not in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameBetween(String value1, String value2) {
            addCriterion("org_name between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotBetween(String value1, String value2) {
            addCriterion("org_name not between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIsNull() {
            addCriterion("apply_org is null");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIsNotNull() {
            addCriterion("apply_org is not null");
            return (Criteria) this;
        }

        public Criteria andApplyOrgEqualTo(String value) {
            addCriterion("apply_org =", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotEqualTo(String value) {
            addCriterion("apply_org <>", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgGreaterThan(String value) {
            addCriterion("apply_org >", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgGreaterThanOrEqualTo(String value) {
            addCriterion("apply_org >=", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLessThan(String value) {
            addCriterion("apply_org <", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLessThanOrEqualTo(String value) {
            addCriterion("apply_org <=", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgLike(String value) {
            addCriterion("apply_org like", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotLike(String value) {
            addCriterion("apply_org not like", value, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgIn(List<String> values) {
            addCriterion("apply_org in", values, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotIn(List<String> values) {
            addCriterion("apply_org not in", values, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgBetween(String value1, String value2) {
            addCriterion("apply_org between", value1, value2, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNotBetween(String value1, String value2) {
            addCriterion("apply_org not between", value1, value2, "applyOrg");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProcessIdIsNull() {
            addCriterion("process_id is null");
            return (Criteria) this;
        }

        public Criteria andProcessIdIsNotNull() {
            addCriterion("process_id is not null");
            return (Criteria) this;
        }

        public Criteria andProcessIdEqualTo(Long value) {
            addCriterion("process_id =", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdNotEqualTo(Long value) {
            addCriterion("process_id <>", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdGreaterThan(Long value) {
            addCriterion("process_id >", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdGreaterThanOrEqualTo(Long value) {
            addCriterion("process_id >=", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdLessThan(Long value) {
            addCriterion("process_id <", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdLessThanOrEqualTo(Long value) {
            addCriterion("process_id <=", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdIn(List<Long> values) {
            addCriterion("process_id in", values, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdNotIn(List<Long> values) {
            addCriterion("process_id not in", values, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdBetween(Long value1, Long value2) {
            addCriterion("process_id between", value1, value2, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdNotBetween(Long value1, Long value2) {
            addCriterion("process_id not between", value1, value2, "processId");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdIsNull() {
            addCriterion("invoice_apply_header_id is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdIsNotNull() {
            addCriterion("invoice_apply_header_id is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdEqualTo(Long value) {
            addCriterion("invoice_apply_header_id =", value, "invoiceApplyHeaderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdNotEqualTo(Long value) {
            addCriterion("invoice_apply_header_id <>", value, "invoiceApplyHeaderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdGreaterThan(Long value) {
            addCriterion("invoice_apply_header_id >", value, "invoiceApplyHeaderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("invoice_apply_header_id >=", value, "invoiceApplyHeaderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdLessThan(Long value) {
            addCriterion("invoice_apply_header_id <", value, "invoiceApplyHeaderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdLessThanOrEqualTo(Long value) {
            addCriterion("invoice_apply_header_id <=", value, "invoiceApplyHeaderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdIn(List<Long> values) {
            addCriterion("invoice_apply_header_id in", values, "invoiceApplyHeaderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdNotIn(List<Long> values) {
            addCriterion("invoice_apply_header_id not in", values, "invoiceApplyHeaderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdBetween(Long value1, Long value2) {
            addCriterion("invoice_apply_header_id between", value1, value2, "invoiceApplyHeaderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceApplyHeaderIdNotBetween(Long value1, Long value2) {
            addCriterion("invoice_apply_header_id not between", value1, value2, "invoiceApplyHeaderId");
            return (Criteria) this;
        }

        public Criteria andLevelIsNull() {
            addCriterion("level is null");
            return (Criteria) this;
        }

        public Criteria andLevelIsNotNull() {
            addCriterion("level is not null");
            return (Criteria) this;
        }

        public Criteria andLevelEqualTo(String value) {
            addCriterion("level =", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotEqualTo(String value) {
            addCriterion("level <>", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThan(String value) {
            addCriterion("level >", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThanOrEqualTo(String value) {
            addCriterion("level >=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThan(String value) {
            addCriterion("level <", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThanOrEqualTo(String value) {
            addCriterion("level <=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLike(String value) {
            addCriterion("level like", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotLike(String value) {
            addCriterion("level not like", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelIn(List<String> values) {
            addCriterion("level in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotIn(List<String> values) {
            addCriterion("level not in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelBetween(String value1, String value2) {
            addCriterion("level between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotBetween(String value1, String value2) {
            addCriterion("level not between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeIsNull() {
            addCriterion("labor_cost_type is null");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeIsNotNull() {
            addCriterion("labor_cost_type is not null");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeEqualTo(String value) {
            addCriterion("labor_cost_type =", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotEqualTo(String value) {
            addCriterion("labor_cost_type <>", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeGreaterThan(String value) {
            addCriterion("labor_cost_type >", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeGreaterThanOrEqualTo(String value) {
            addCriterion("labor_cost_type >=", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeLessThan(String value) {
            addCriterion("labor_cost_type <", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeLessThanOrEqualTo(String value) {
            addCriterion("labor_cost_type <=", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeLike(String value) {
            addCriterion("labor_cost_type like", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotLike(String value) {
            addCriterion("labor_cost_type not like", value, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeIn(List<String> values) {
            addCriterion("labor_cost_type in", values, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotIn(List<String> values) {
            addCriterion("labor_cost_type not in", values, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeBetween(String value1, String value2) {
            addCriterion("labor_cost_type between", value1, value2, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeNotBetween(String value1, String value2) {
            addCriterion("labor_cost_type not between", value1, value2, "laborCostType");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdIsNull() {
            addCriterion("labor_cost_type_set_id is null");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdIsNotNull() {
            addCriterion("labor_cost_type_set_id is not null");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdEqualTo(Long value) {
            addCriterion("labor_cost_type_set_id =", value, "laborCostTypeSetId");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdNotEqualTo(Long value) {
            addCriterion("labor_cost_type_set_id <>", value, "laborCostTypeSetId");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdGreaterThan(Long value) {
            addCriterion("labor_cost_type_set_id >", value, "laborCostTypeSetId");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdGreaterThanOrEqualTo(Long value) {
            addCriterion("labor_cost_type_set_id >=", value, "laborCostTypeSetId");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdLessThan(Long value) {
            addCriterion("labor_cost_type_set_id <", value, "laborCostTypeSetId");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdLessThanOrEqualTo(Long value) {
            addCriterion("labor_cost_type_set_id <=", value, "laborCostTypeSetId");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdIn(List<Long> values) {
            addCriterion("labor_cost_type_set_id in", values, "laborCostTypeSetId");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdNotIn(List<Long> values) {
            addCriterion("labor_cost_type_set_id not in", values, "laborCostTypeSetId");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdBetween(Long value1, Long value2) {
            addCriterion("labor_cost_type_set_id between", value1, value2, "laborCostTypeSetId");
            return (Criteria) this;
        }

        public Criteria andLaborCostTypeSetIdNotBetween(Long value1, Long value2) {
            addCriterion("labor_cost_type_set_id not between", value1, value2, "laborCostTypeSetId");
            return (Criteria) this;
        }

        public Criteria andCostMoneyIsNull() {
            addCriterion("cost_money is null");
            return (Criteria) this;
        }

        public Criteria andCostMoneyIsNotNull() {
            addCriterion("cost_money is not null");
            return (Criteria) this;
        }

        public Criteria andCostMoneyEqualTo(BigDecimal value) {
            addCriterion("cost_money =", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyNotEqualTo(BigDecimal value) {
            addCriterion("cost_money <>", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyGreaterThan(BigDecimal value) {
            addCriterion("cost_money >", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_money >=", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyLessThan(BigDecimal value) {
            addCriterion("cost_money <", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_money <=", value, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyIn(List<BigDecimal> values) {
            addCriterion("cost_money in", values, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyNotIn(List<BigDecimal> values) {
            addCriterion("cost_money not in", values, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_money between", value1, value2, "costMoney");
            return (Criteria) this;
        }

        public Criteria andCostMoneyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_money not between", value1, value2, "costMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyIsNull() {
            addCriterion("actual_cost_money is null");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyIsNotNull() {
            addCriterion("actual_cost_money is not null");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyEqualTo(BigDecimal value) {
            addCriterion("actual_cost_money =", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyNotEqualTo(BigDecimal value) {
            addCriterion("actual_cost_money <>", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyGreaterThan(BigDecimal value) {
            addCriterion("actual_cost_money >", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_cost_money >=", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyLessThan(BigDecimal value) {
            addCriterion("actual_cost_money <", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_cost_money <=", value, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyIn(List<BigDecimal> values) {
            addCriterion("actual_cost_money in", values, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyNotIn(List<BigDecimal> values) {
            addCriterion("actual_cost_money not in", values, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_cost_money between", value1, value2, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualCostMoneyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_cost_money not between", value1, value2, "actualCostMoney");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursIsNull() {
            addCriterion("actual_working_hours is null");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursIsNotNull() {
            addCriterion("actual_working_hours is not null");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursEqualTo(BigDecimal value) {
            addCriterion("actual_working_hours =", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursNotEqualTo(BigDecimal value) {
            addCriterion("actual_working_hours <>", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursGreaterThan(BigDecimal value) {
            addCriterion("actual_working_hours >", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_working_hours >=", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursLessThan(BigDecimal value) {
            addCriterion("actual_working_hours <", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursLessThanOrEqualTo(BigDecimal value) {
            addCriterion("actual_working_hours <=", value, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursIn(List<BigDecimal> values) {
            addCriterion("actual_working_hours in", values, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursNotIn(List<BigDecimal> values) {
            addCriterion("actual_working_hours not in", values, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_working_hours between", value1, value2, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andActualWorkingHoursNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("actual_working_hours not between", value1, value2, "actualWorkingHours");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNull() {
            addCriterion("remarks is null");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNotNull() {
            addCriterion("remarks is not null");
            return (Criteria) this;
        }

        public Criteria andRemarksEqualTo(String value) {
            addCriterion("remarks =", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotEqualTo(String value) {
            addCriterion("remarks <>", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThan(String value) {
            addCriterion("remarks >", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThanOrEqualTo(String value) {
            addCriterion("remarks >=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThan(String value) {
            addCriterion("remarks <", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThanOrEqualTo(String value) {
            addCriterion("remarks <=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLike(String value) {
            addCriterion("remarks like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotLike(String value) {
            addCriterion("remarks not like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksIn(List<String> values) {
            addCriterion("remarks in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotIn(List<String> values) {
            addCriterion("remarks not in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksBetween(String value1, String value2) {
            addCriterion("remarks between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotBetween(String value1, String value2) {
            addCriterion("remarks not between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(Integer value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(Integer value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(Integer value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(Integer value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(Integer value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<Integer> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<Integer> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(Integer value1, Integer value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdIsNull() {
            addCriterion("approve_user_id is null");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdIsNotNull() {
            addCriterion("approve_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdEqualTo(Long value) {
            addCriterion("approve_user_id =", value, "approveUserId");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdNotEqualTo(Long value) {
            addCriterion("approve_user_id <>", value, "approveUserId");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdGreaterThan(Long value) {
            addCriterion("approve_user_id >", value, "approveUserId");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("approve_user_id >=", value, "approveUserId");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdLessThan(Long value) {
            addCriterion("approve_user_id <", value, "approveUserId");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdLessThanOrEqualTo(Long value) {
            addCriterion("approve_user_id <=", value, "approveUserId");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdIn(List<Long> values) {
            addCriterion("approve_user_id in", values, "approveUserId");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdNotIn(List<Long> values) {
            addCriterion("approve_user_id not in", values, "approveUserId");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdBetween(Long value1, Long value2) {
            addCriterion("approve_user_id between", value1, value2, "approveUserId");
            return (Criteria) this;
        }

        public Criteria andApproveUserIdNotBetween(Long value1, Long value2) {
            addCriterion("approve_user_id not between", value1, value2, "approveUserId");
            return (Criteria) this;
        }

        public Criteria andApproveTimeIsNull() {
            addCriterion("approve_time is null");
            return (Criteria) this;
        }

        public Criteria andApproveTimeIsNotNull() {
            addCriterion("approve_time is not null");
            return (Criteria) this;
        }

        public Criteria andApproveTimeEqualTo(Date value) {
            addCriterion("approve_time =", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeNotEqualTo(Date value) {
            addCriterion("approve_time <>", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeGreaterThan(Date value) {
            addCriterion("approve_time >", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("approve_time >=", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeLessThan(Date value) {
            addCriterion("approve_time <", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeLessThanOrEqualTo(Date value) {
            addCriterion("approve_time <=", value, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeIn(List<Date> values) {
            addCriterion("approve_time in", values, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeNotIn(List<Date> values) {
            addCriterion("approve_time not in", values, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeBetween(Date value1, Date value2) {
            addCriterion("approve_time between", value1, value2, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveTimeNotBetween(Date value1, Date value2) {
            addCriterion("approve_time not between", value1, value2, "approveTime");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameIsNull() {
            addCriterion("approve_user_name is null");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameIsNotNull() {
            addCriterion("approve_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameEqualTo(String value) {
            addCriterion("approve_user_name =", value, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameNotEqualTo(String value) {
            addCriterion("approve_user_name <>", value, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameGreaterThan(String value) {
            addCriterion("approve_user_name >", value, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("approve_user_name >=", value, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameLessThan(String value) {
            addCriterion("approve_user_name <", value, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameLessThanOrEqualTo(String value) {
            addCriterion("approve_user_name <=", value, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameLike(String value) {
            addCriterion("approve_user_name like", value, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameNotLike(String value) {
            addCriterion("approve_user_name not like", value, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameIn(List<String> values) {
            addCriterion("approve_user_name in", values, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameNotIn(List<String> values) {
            addCriterion("approve_user_name not in", values, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameBetween(String value1, String value2) {
            addCriterion("approve_user_name between", value1, value2, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andApproveUserNameNotBetween(String value1, String value2) {
            addCriterion("approve_user_name not between", value1, value2, "approveUserName");
            return (Criteria) this;
        }

        public Criteria andUserTypeIsNull() {
            addCriterion("user_type is null");
            return (Criteria) this;
        }

        public Criteria andUserTypeIsNotNull() {
            addCriterion("user_type is not null");
            return (Criteria) this;
        }

        public Criteria andUserTypeEqualTo(String value) {
            addCriterion("user_type =", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotEqualTo(String value) {
            addCriterion("user_type <>", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeGreaterThan(String value) {
            addCriterion("user_type >", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeGreaterThanOrEqualTo(String value) {
            addCriterion("user_type >=", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeLessThan(String value) {
            addCriterion("user_type <", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeLessThanOrEqualTo(String value) {
            addCriterion("user_type <=", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeLike(String value) {
            addCriterion("user_type like", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotLike(String value) {
            addCriterion("user_type not like", value, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeIn(List<String> values) {
            addCriterion("user_type in", values, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotIn(List<String> values) {
            addCriterion("user_type not in", values, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeBetween(String value1, String value2) {
            addCriterion("user_type between", value1, value2, "userType");
            return (Criteria) this;
        }

        public Criteria andUserTypeNotBetween(String value1, String value2) {
            addCriterion("user_type not between", value1, value2, "userType");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdIsNull() {
            addCriterion("biz_unit_id is null");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdIsNotNull() {
            addCriterion("biz_unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdEqualTo(Long value) {
            addCriterion("biz_unit_id =", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdNotEqualTo(Long value) {
            addCriterion("biz_unit_id <>", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdGreaterThan(Long value) {
            addCriterion("biz_unit_id >", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("biz_unit_id >=", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdLessThan(Long value) {
            addCriterion("biz_unit_id <", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("biz_unit_id <=", value, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdIn(List<Long> values) {
            addCriterion("biz_unit_id in", values, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdNotIn(List<Long> values) {
            addCriterion("biz_unit_id not in", values, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdBetween(Long value1, Long value2) {
            addCriterion("biz_unit_id between", value1, value2, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andBizUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("biz_unit_id not between", value1, value2, "bizUnitId");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdIsNull() {
            addCriterion("labor_cost_source_unit_id is null");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdIsNotNull() {
            addCriterion("labor_cost_source_unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdEqualTo(Long value) {
            addCriterion("labor_cost_source_unit_id =", value, "laborCostSourceUnitId");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdNotEqualTo(Long value) {
            addCriterion("labor_cost_source_unit_id <>", value, "laborCostSourceUnitId");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdGreaterThan(Long value) {
            addCriterion("labor_cost_source_unit_id >", value, "laborCostSourceUnitId");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("labor_cost_source_unit_id >=", value, "laborCostSourceUnitId");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdLessThan(Long value) {
            addCriterion("labor_cost_source_unit_id <", value, "laborCostSourceUnitId");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("labor_cost_source_unit_id <=", value, "laborCostSourceUnitId");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdIn(List<Long> values) {
            addCriterion("labor_cost_source_unit_id in", values, "laborCostSourceUnitId");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdNotIn(List<Long> values) {
            addCriterion("labor_cost_source_unit_id not in", values, "laborCostSourceUnitId");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdBetween(Long value1, Long value2) {
            addCriterion("labor_cost_source_unit_id between", value1, value2, "laborCostSourceUnitId");
            return (Criteria) this;
        }

        public Criteria andLaborCostSourceUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("labor_cost_source_unit_id not between", value1, value2, "laborCostSourceUnitId");
            return (Criteria) this;
        }

        public Criteria andIsImportIsNull() {
            addCriterion("is_import is null");
            return (Criteria) this;
        }

        public Criteria andIsImportIsNotNull() {
            addCriterion("is_import is not null");
            return (Criteria) this;
        }

        public Criteria andIsImportEqualTo(Boolean value) {
            addCriterion("is_import =", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportNotEqualTo(Boolean value) {
            addCriterion("is_import <>", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportGreaterThan(Boolean value) {
            addCriterion("is_import >", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_import >=", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportLessThan(Boolean value) {
            addCriterion("is_import <", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportLessThanOrEqualTo(Boolean value) {
            addCriterion("is_import <=", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportIn(List<Boolean> values) {
            addCriterion("is_import in", values, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportNotIn(List<Boolean> values) {
            addCriterion("is_import not in", values, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportBetween(Boolean value1, Boolean value2) {
            addCriterion("is_import between", value1, value2, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_import not between", value1, value2, "isImport");
            return (Criteria) this;
        }

        public Criteria andRdmFlagIsNull() {
            addCriterion("rdm_flag is null");
            return (Criteria) this;
        }

        public Criteria andRdmFlagIsNotNull() {
            addCriterion("rdm_flag is not null");
            return (Criteria) this;
        }

        public Criteria andRdmFlagEqualTo(Integer value) {
            addCriterion("rdm_flag =", value, "rdmFlag");
            return (Criteria) this;
        }

        public Criteria andRdmFlagNotEqualTo(Integer value) {
            addCriterion("rdm_flag <>", value, "rdmFlag");
            return (Criteria) this;
        }

        public Criteria andRdmFlagGreaterThan(Integer value) {
            addCriterion("rdm_flag >", value, "rdmFlag");
            return (Criteria) this;
        }

        public Criteria andRdmFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("rdm_flag >=", value, "rdmFlag");
            return (Criteria) this;
        }

        public Criteria andRdmFlagLessThan(Integer value) {
            addCriterion("rdm_flag <", value, "rdmFlag");
            return (Criteria) this;
        }

        public Criteria andRdmFlagLessThanOrEqualTo(Integer value) {
            addCriterion("rdm_flag <=", value, "rdmFlag");
            return (Criteria) this;
        }

        public Criteria andRdmFlagIn(List<Integer> values) {
            addCriterion("rdm_flag in", values, "rdmFlag");
            return (Criteria) this;
        }

        public Criteria andRdmFlagNotIn(List<Integer> values) {
            addCriterion("rdm_flag not in", values, "rdmFlag");
            return (Criteria) this;
        }

        public Criteria andRdmFlagBetween(Integer value1, Integer value2) {
            addCriterion("rdm_flag between", value1, value2, "rdmFlag");
            return (Criteria) this;
        }

        public Criteria andRdmFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("rdm_flag not between", value1, value2, "rdmFlag");
            return (Criteria) this;
        }

        public Criteria andSourceFlagIsNull() {
            addCriterion("source_flag is null");
            return (Criteria) this;
        }

        public Criteria andSourceFlagIsNotNull() {
            addCriterion("source_flag is not null");
            return (Criteria) this;
        }

        public Criteria andSourceFlagEqualTo(Integer value) {
            addCriterion("source_flag =", value, "sourceFlag");
            return (Criteria) this;
        }

        public Criteria andSourceFlagNotEqualTo(Integer value) {
            addCriterion("source_flag <>", value, "sourceFlag");
            return (Criteria) this;
        }

        public Criteria andSourceFlagGreaterThan(Integer value) {
            addCriterion("source_flag >", value, "sourceFlag");
            return (Criteria) this;
        }

        public Criteria andSourceFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_flag >=", value, "sourceFlag");
            return (Criteria) this;
        }

        public Criteria andSourceFlagLessThan(Integer value) {
            addCriterion("source_flag <", value, "sourceFlag");
            return (Criteria) this;
        }

        public Criteria andSourceFlagLessThanOrEqualTo(Integer value) {
            addCriterion("source_flag <=", value, "sourceFlag");
            return (Criteria) this;
        }

        public Criteria andSourceFlagIn(List<Integer> values) {
            addCriterion("source_flag in", values, "sourceFlag");
            return (Criteria) this;
        }

        public Criteria andSourceFlagNotIn(List<Integer> values) {
            addCriterion("source_flag not in", values, "sourceFlag");
            return (Criteria) this;
        }

        public Criteria andSourceFlagBetween(Integer value1, Integer value2) {
            addCriterion("source_flag between", value1, value2, "sourceFlag");
            return (Criteria) this;
        }

        public Criteria andSourceFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("source_flag not between", value1, value2, "sourceFlag");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusIsNull() {
            addCriterion("write_off_status is null");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusIsNotNull() {
            addCriterion("write_off_status is not null");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusEqualTo(Integer value) {
            addCriterion("write_off_status =", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusNotEqualTo(Integer value) {
            addCriterion("write_off_status <>", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusGreaterThan(Integer value) {
            addCriterion("write_off_status >", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("write_off_status >=", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusLessThan(Integer value) {
            addCriterion("write_off_status <", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusLessThanOrEqualTo(Integer value) {
            addCriterion("write_off_status <=", value, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusIn(List<Integer> values) {
            addCriterion("write_off_status in", values, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusNotIn(List<Integer> values) {
            addCriterion("write_off_status not in", values, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusBetween(Integer value1, Integer value2) {
            addCriterion("write_off_status between", value1, value2, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andWriteOffStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("write_off_status not between", value1, value2, "writeOffStatus");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdIsNull() {
            addCriterion("stay_approve_user_id is null");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdIsNotNull() {
            addCriterion("stay_approve_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdEqualTo(Long value) {
            addCriterion("stay_approve_user_id =", value, "stayApproveUserId");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdNotEqualTo(Long value) {
            addCriterion("stay_approve_user_id <>", value, "stayApproveUserId");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdGreaterThan(Long value) {
            addCriterion("stay_approve_user_id >", value, "stayApproveUserId");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("stay_approve_user_id >=", value, "stayApproveUserId");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdLessThan(Long value) {
            addCriterion("stay_approve_user_id <", value, "stayApproveUserId");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdLessThanOrEqualTo(Long value) {
            addCriterion("stay_approve_user_id <=", value, "stayApproveUserId");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdIn(List<Long> values) {
            addCriterion("stay_approve_user_id in", values, "stayApproveUserId");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdNotIn(List<Long> values) {
            addCriterion("stay_approve_user_id not in", values, "stayApproveUserId");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdBetween(Long value1, Long value2) {
            addCriterion("stay_approve_user_id between", value1, value2, "stayApproveUserId");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserIdNotBetween(Long value1, Long value2) {
            addCriterion("stay_approve_user_id not between", value1, value2, "stayApproveUserId");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipIsNull() {
            addCriterion("stay_approve_user_mip is null");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipIsNotNull() {
            addCriterion("stay_approve_user_mip is not null");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipEqualTo(String value) {
            addCriterion("stay_approve_user_mip =", value, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipNotEqualTo(String value) {
            addCriterion("stay_approve_user_mip <>", value, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipGreaterThan(String value) {
            addCriterion("stay_approve_user_mip >", value, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipGreaterThanOrEqualTo(String value) {
            addCriterion("stay_approve_user_mip >=", value, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipLessThan(String value) {
            addCriterion("stay_approve_user_mip <", value, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipLessThanOrEqualTo(String value) {
            addCriterion("stay_approve_user_mip <=", value, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipLike(String value) {
            addCriterion("stay_approve_user_mip like", value, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipNotLike(String value) {
            addCriterion("stay_approve_user_mip not like", value, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipIn(List<String> values) {
            addCriterion("stay_approve_user_mip in", values, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipNotIn(List<String> values) {
            addCriterion("stay_approve_user_mip not in", values, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipBetween(String value1, String value2) {
            addCriterion("stay_approve_user_mip between", value1, value2, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andStayApproveUserMipNotBetween(String value1, String value2) {
            addCriterion("stay_approve_user_mip not between", value1, value2, "stayApproveUserMip");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNull() {
            addCriterion("vendor_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNotNull() {
            addCriterion("vendor_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeEqualTo(String value) {
            addCriterion("vendor_code =", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotEqualTo(String value) {
            addCriterion("vendor_code <>", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThan(String value) {
            addCriterion("vendor_code >", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_code >=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThan(String value) {
            addCriterion("vendor_code <", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_code <=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLike(String value) {
            addCriterion("vendor_code like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotLike(String value) {
            addCriterion("vendor_code not like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIn(List<String> values) {
            addCriterion("vendor_code in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotIn(List<String> values) {
            addCriterion("vendor_code not in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeBetween(String value1, String value2) {
            addCriterion("vendor_code between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_code not between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andRoleNameIsNull() {
            addCriterion("role_name is null");
            return (Criteria) this;
        }

        public Criteria andRoleNameIsNotNull() {
            addCriterion("role_name is not null");
            return (Criteria) this;
        }

        public Criteria andRoleNameEqualTo(String value) {
            addCriterion("role_name =", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotEqualTo(String value) {
            addCriterion("role_name <>", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameGreaterThan(String value) {
            addCriterion("role_name >", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameGreaterThanOrEqualTo(String value) {
            addCriterion("role_name >=", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameLessThan(String value) {
            addCriterion("role_name <", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameLessThanOrEqualTo(String value) {
            addCriterion("role_name <=", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameLike(String value) {
            addCriterion("role_name like", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotLike(String value) {
            addCriterion("role_name not like", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameIn(List<String> values) {
            addCriterion("role_name in", values, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotIn(List<String> values) {
            addCriterion("role_name not in", values, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameBetween(String value1, String value2) {
            addCriterion("role_name between", value1, value2, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotBetween(String value1, String value2) {
            addCriterion("role_name not between", value1, value2, "roleName");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeIsNull() {
            addCriterion("wbs_budget_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeIsNotNull() {
            addCriterion("wbs_budget_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeEqualTo(String value) {
            addCriterion("wbs_budget_code =", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeNotEqualTo(String value) {
            addCriterion("wbs_budget_code <>", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeGreaterThan(String value) {
            addCriterion("wbs_budget_code >", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_budget_code >=", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeLessThan(String value) {
            addCriterion("wbs_budget_code <", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_budget_code <=", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeLike(String value) {
            addCriterion("wbs_budget_code like", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeNotLike(String value) {
            addCriterion("wbs_budget_code not like", value, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeIn(List<String> values) {
            addCriterion("wbs_budget_code in", values, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeNotIn(List<String> values) {
            addCriterion("wbs_budget_code not in", values, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeBetween(String value1, String value2) {
            addCriterion("wbs_budget_code between", value1, value2, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsBudgetCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_budget_code not between", value1, value2, "wbsBudgetCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNull() {
            addCriterion("wbs_summary_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNotNull() {
            addCriterion("wbs_summary_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeEqualTo(String value) {
            addCriterion("wbs_summary_code =", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotEqualTo(String value) {
            addCriterion("wbs_summary_code <>", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThan(String value) {
            addCriterion("wbs_summary_code >", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code >=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThan(String value) {
            addCriterion("wbs_summary_code <", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code <=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLike(String value) {
            addCriterion("wbs_summary_code like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotLike(String value) {
            addCriterion("wbs_summary_code not like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIn(List<String> values) {
            addCriterion("wbs_summary_code in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotIn(List<String> values) {
            addCriterion("wbs_summary_code not in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeBetween(String value1, String value2) {
            addCriterion("wbs_summary_code between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_summary_code not between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdIsNull() {
            addCriterion("labor_wbs_cost_id is null");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdIsNotNull() {
            addCriterion("labor_wbs_cost_id is not null");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdEqualTo(Long value) {
            addCriterion("labor_wbs_cost_id =", value, "laborWbsCostId");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdNotEqualTo(Long value) {
            addCriterion("labor_wbs_cost_id <>", value, "laborWbsCostId");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdGreaterThan(Long value) {
            addCriterion("labor_wbs_cost_id >", value, "laborWbsCostId");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdGreaterThanOrEqualTo(Long value) {
            addCriterion("labor_wbs_cost_id >=", value, "laborWbsCostId");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdLessThan(Long value) {
            addCriterion("labor_wbs_cost_id <", value, "laborWbsCostId");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdLessThanOrEqualTo(Long value) {
            addCriterion("labor_wbs_cost_id <=", value, "laborWbsCostId");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdIn(List<Long> values) {
            addCriterion("labor_wbs_cost_id in", values, "laborWbsCostId");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdNotIn(List<Long> values) {
            addCriterion("labor_wbs_cost_id not in", values, "laborWbsCostId");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdBetween(Long value1, Long value2) {
            addCriterion("labor_wbs_cost_id between", value1, value2, "laborWbsCostId");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostIdNotBetween(Long value1, Long value2) {
            addCriterion("labor_wbs_cost_id not between", value1, value2, "laborWbsCostId");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameIsNull() {
            addCriterion("labor_wbs_cost_name is null");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameIsNotNull() {
            addCriterion("labor_wbs_cost_name is not null");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameEqualTo(String value) {
            addCriterion("labor_wbs_cost_name =", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameNotEqualTo(String value) {
            addCriterion("labor_wbs_cost_name <>", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameGreaterThan(String value) {
            addCriterion("labor_wbs_cost_name >", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameGreaterThanOrEqualTo(String value) {
            addCriterion("labor_wbs_cost_name >=", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameLessThan(String value) {
            addCriterion("labor_wbs_cost_name <", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameLessThanOrEqualTo(String value) {
            addCriterion("labor_wbs_cost_name <=", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameLike(String value) {
            addCriterion("labor_wbs_cost_name like", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameNotLike(String value) {
            addCriterion("labor_wbs_cost_name not like", value, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameIn(List<String> values) {
            addCriterion("labor_wbs_cost_name in", values, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameNotIn(List<String> values) {
            addCriterion("labor_wbs_cost_name not in", values, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameBetween(String value1, String value2) {
            addCriterion("labor_wbs_cost_name between", value1, value2, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andLaborWbsCostNameNotBetween(String value1, String value2) {
            addCriterion("labor_wbs_cost_name not between", value1, value2, "laborWbsCostName");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeIsNull() {
            addCriterion("project_activity_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeIsNotNull() {
            addCriterion("project_activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeEqualTo(String value) {
            addCriterion("project_activity_code =", value, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeNotEqualTo(String value) {
            addCriterion("project_activity_code <>", value, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeGreaterThan(String value) {
            addCriterion("project_activity_code >", value, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_activity_code >=", value, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeLessThan(String value) {
            addCriterion("project_activity_code <", value, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("project_activity_code <=", value, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeLike(String value) {
            addCriterion("project_activity_code like", value, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeNotLike(String value) {
            addCriterion("project_activity_code not like", value, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeIn(List<String> values) {
            addCriterion("project_activity_code in", values, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeNotIn(List<String> values) {
            addCriterion("project_activity_code not in", values, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeBetween(String value1, String value2) {
            addCriterion("project_activity_code between", value1, value2, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andProjectActivityCodeNotBetween(String value1, String value2) {
            addCriterion("project_activity_code not between", value1, value2, "projectActivityCode");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdIsNull() {
            addCriterion("full_pay_ou_id is null");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdIsNotNull() {
            addCriterion("full_pay_ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdEqualTo(Long value) {
            addCriterion("full_pay_ou_id =", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdNotEqualTo(Long value) {
            addCriterion("full_pay_ou_id <>", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdGreaterThan(Long value) {
            addCriterion("full_pay_ou_id >", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("full_pay_ou_id >=", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdLessThan(Long value) {
            addCriterion("full_pay_ou_id <", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdLessThanOrEqualTo(Long value) {
            addCriterion("full_pay_ou_id <=", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdIn(List<Long> values) {
            addCriterion("full_pay_ou_id in", values, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdNotIn(List<Long> values) {
            addCriterion("full_pay_ou_id not in", values, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdBetween(Long value1, Long value2) {
            addCriterion("full_pay_ou_id between", value1, value2, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdNotBetween(Long value1, Long value2) {
            addCriterion("full_pay_ou_id not between", value1, value2, "fullPayOuId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}