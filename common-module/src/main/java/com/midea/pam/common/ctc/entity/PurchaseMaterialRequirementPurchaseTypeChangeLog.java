package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "物料采购需求需求类型变更记录表")
public class PurchaseMaterialRequirementPurchaseTypeChangeLog extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "物料采购需求id")
    private Long purchaseMaterialRequirementId;

    @ApiModelProperty(value = "操作类型（0： 物料采购需求（WBS），1：外包（整包）需求 ）")
    private Integer operationType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "0：有效， 1：删除")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "变更前物料采购需求信息")
    private String purchaseMaterialRequirementChangeBeforeInfo;

    @ApiModelProperty(value = "变更后物料采购需求信息")
    private String purchaseMaterialRequirementChangeAfterInfo;

    private static final long serialVersionUID = 1L;

    public Long getPurchaseMaterialRequirementId() {
        return purchaseMaterialRequirementId;
    }

    public void setPurchaseMaterialRequirementId(Long purchaseMaterialRequirementId) {
        this.purchaseMaterialRequirementId = purchaseMaterialRequirementId;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getPurchaseMaterialRequirementChangeBeforeInfo() {
        return purchaseMaterialRequirementChangeBeforeInfo;
    }

    public void setPurchaseMaterialRequirementChangeBeforeInfo(String purchaseMaterialRequirementChangeBeforeInfo) {
        this.purchaseMaterialRequirementChangeBeforeInfo = purchaseMaterialRequirementChangeBeforeInfo == null ? null : purchaseMaterialRequirementChangeBeforeInfo.trim();
    }

    public String getPurchaseMaterialRequirementChangeAfterInfo() {
        return purchaseMaterialRequirementChangeAfterInfo;
    }

    public void setPurchaseMaterialRequirementChangeAfterInfo(String purchaseMaterialRequirementChangeAfterInfo) {
        this.purchaseMaterialRequirementChangeAfterInfo = purchaseMaterialRequirementChangeAfterInfo == null ? null : purchaseMaterialRequirementChangeAfterInfo.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", purchaseMaterialRequirementId=").append(purchaseMaterialRequirementId);
        sb.append(", operationType=").append(operationType);
        sb.append(", remark=").append(remark);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", purchaseMaterialRequirementChangeBeforeInfo=").append(purchaseMaterialRequirementChangeBeforeInfo);
        sb.append(", purchaseMaterialRequirementChangeAfterInfo=").append(purchaseMaterialRequirementChangeAfterInfo);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}