package com.midea.pam.common.ctc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel(value = "PurchaseOrderDetailPdfVO", description = "采购订单明细行PDF数据封装")
public class PurchaseOrderDetailPdfVO implements Serializable {

    @ApiModelProperty(value = "序号")
    private Integer row;

    @ApiModelProperty(value = "合同约定交期(承诺日期)")
    private Date contractAppointDate;

    @ApiModelProperty(value = "物料编码")
    private String erpCode;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "设计发布批次号")
    private String designReleaseLotNumber;

    @ApiModelProperty(value = "项目号")
    private String projectNum;

    @ApiModelProperty(value = "下单数量")
    private BigDecimal orderNum;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "折后价(不含税)")
    private BigDecimal discountPrice;

    @ApiModelProperty(value = "折后金额(不含税) ")
    private BigDecimal discountMoney;

    @ApiModelProperty(value = "物料备注")
    private String materialRemark;

}
