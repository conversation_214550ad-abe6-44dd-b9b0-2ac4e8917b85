package com.midea.pam.common.ctc.excelVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Objects;

@Getter
@Setter
public class ProjectWbsReceiptsBudgetChangeHistorySumApproveExportVo {

    @Excel(name = "序号")
    private Integer num;

    @Excel(name = "WBS", width = 22)
    private String wbsSummaryCode;

    private BigDecimal price;

    @Excel(name = "预算金额")
    private String price_dt;

    private BigDecimal budgetOccupiedAmountBefore;

    @Excel(name = "变更前预算占用金额")
    private String budgetOccupiedAmountBefore_dt;

    private BigDecimal budgetOccupiedAmountAfter;

    @Excel(name = "变更后预算占用金额")
    private String budgetOccupiedAmountAfter_dt;


    public String getPrice_dt() {
        if(Objects.nonNull(this.price)){
            return this.price.stripTrailingZeros().toPlainString();
        }else{
            return this.price_dt;
        }
    }

    public String getBudgetOccupiedAmountBefore_dt() {
        if(Objects.nonNull(this.budgetOccupiedAmountBefore)){
            return this.budgetOccupiedAmountBefore.stripTrailingZeros().toPlainString();
        }else{
            return this.budgetOccupiedAmountBefore_dt;
        }
    }

    public String getBudgetOccupiedAmountAfter_dt() {
        if(Objects.nonNull(this.budgetOccupiedAmountAfter)){
            return this.budgetOccupiedAmountAfter.stripTrailingZeros().toPlainString();
        }else{
            return this.budgetOccupiedAmountAfter_dt;
        }
    }
}