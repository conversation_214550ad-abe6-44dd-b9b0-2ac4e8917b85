package com.midea.pam.common.ctc.dto;

import com.alibaba.fastjson.JSON;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetChangeHistory;
import com.midea.pam.common.util.FastjsonUtils;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Getter
@Setter
public class ProjectWbsBudgetChangeHistoryDto extends ProjectWbsBudgetChangeHistory {

    @ApiModelProperty(value = "变更后预算" ,notes = "change.price")
    private BigDecimal afterChangePrice;

    @ApiModelProperty(value = "变更后剩余可用预算" ,notes = "change.changeRemainingCost")
    private BigDecimal afterChangeRemainingCost;

    @ApiModelProperty(value = "变更金额" ,notes = "change.price - history.price")
    private BigDecimal changePrice;

    @ApiModelProperty(value = "累计变更金额" ,notes = "change.changeAccumulateCost")
    private BigDecimal afterChangeChangeAccumulateCost;

    public static Map<String, Object> dto2Map(ProjectWbsBudgetChangeHistoryDto dto) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map result = FastjsonUtils.toMap(dto);
        if (StringUtils.isNotBlank(dto.getDynamicFields()) && StringUtils.isNotBlank(dto.getDynamicValues())) {
            String[] dynamicFieldArr = dto.getDynamicFields().split(",");
            String[] dynamicValueArr = dto.getDynamicValues().split(",");
            for (int i = 0; i < dynamicFieldArr.length; i++) {
                result.put(dynamicFieldArr[i], dynamicValueArr[i]);
            }
        }
        if (!Objects.isNull(dto.getCreateAt())) {
            result.put(WbsBudgetFieldConstant.CREATE_AT, simpleDateFormat.format(dto.getCreateAt()));
        }
        if (!Objects.isNull(dto.getUpdateAt())) {
            result.put(WbsBudgetFieldConstant.UPDATE_AT, simpleDateFormat.format(dto.getUpdateAt()));
        }

        // 四舍五入金额
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.PRICE, dto.getPrice());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.BASELINE_COST, dto.getBaselineCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.DEMAND_COST, dto.getDemandCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.ON_THE_WAY_COST, dto.getOnTheWayCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.INCURRED_COST, dto.getIncurredCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.REMAINING_COST, dto.getRemainingCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST, dto.getChangeAccumulateCost());

        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, dto.getAfterChangePrice());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.AFTER_CHANGE_REMAINING_COST, dto.getAfterChangeRemainingCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.AFTER_CHANGE_CHANGE_ACCUMULATE_COST, dto.getAfterChangeRemainingCost());


        return result;
    }

    public static List<Map<String, Object>> dto2MapBatch(List<ProjectWbsBudgetChangeHistoryDto> list){
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (ProjectWbsBudgetChangeHistoryDto dto : list) {
            result.add(dto2Map(dto));
        }
        return result;
    }

    /**
     * entity转泛型map
     *
     * @param entity
     * @return
     */
    public static Map<String, Object> entity2GenericityMap(ProjectWbsBudgetChangeHistory entity) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map result = FastjsonUtils.toMap(JSON.toJSONString(entity));

        if (!Objects.isNull(entity.getCreateAt())) {
            result.put(WbsBudgetFieldConstant.CREATE_AT, simpleDateFormat.format(entity.getCreateAt()));
        }
        if (!Objects.isNull(entity.getUpdateAt())) {
            result.put(WbsBudgetFieldConstant.UPDATE_AT, simpleDateFormat.format(entity.getUpdateAt()));
        }
        if (StringUtils.isNotBlank(entity.getDynamicFields()) && StringUtils.isNotBlank(entity.getDynamicValues())) {
            String[] dynamicFieldArr = entity.getDynamicFields().split(",");
            String[] dynamicValueArr = entity.getDynamicValues().split(",");
            for (int i = 0; i < dynamicFieldArr.length; i++) {
                result.put(dynamicFieldArr[i], dynamicValueArr[i]);
            }
        }

        // 四舍五入金额
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.PRICE, entity.getPrice());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.BASELINE_COST, entity.getBaselineCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.DEMAND_COST, entity.getDemandCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.ON_THE_WAY_COST, entity.getOnTheWayCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.INCURRED_COST, entity.getIncurredCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.REMAINING_COST, entity.getRemainingCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST, entity.getChangeAccumulateCost());
        return result;
    }

    /**
     * entity转泛型map
     *
     * @param list
     * @return
     */
    public static List<Map<String, Object>> entity2GenericityMapBatch(List<ProjectWbsBudgetChangeHistory> list) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (ProjectWbsBudgetChangeHistory entity : list) {
            result.add(entity2GenericityMap(entity));
        }
        return result;
    }


    /**
     * entity转map
     *
     * @param entity
     * @return
     */
    public static Map entity2Map(ProjectWbsBudgetChangeHistory entity) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map result = FastjsonUtils.toMap(JSON.toJSONString(entity));

        if (!Objects.isNull(entity.getCreateAt())) {
            result.put(WbsBudgetFieldConstant.CREATE_AT, simpleDateFormat.format(entity.getCreateAt()));
        }
        if (!Objects.isNull(entity.getUpdateAt())) {
            result.put(WbsBudgetFieldConstant.UPDATE_AT, simpleDateFormat.format(entity.getUpdateAt()));
        }
        if (StringUtils.isNotBlank(entity.getDynamicFields()) && StringUtils.isNotBlank(entity.getDynamicValues())) {
            String[] dynamicFieldArr = entity.getDynamicFields().split(",");
            String[] dynamicValueArr = entity.getDynamicValues().split(",");
            for (int i = 0; i < dynamicFieldArr.length; i++) {
                result.put(dynamicFieldArr[i], dynamicValueArr[i]);
            }
        }

        // 四舍五入金额
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.PRICE, entity.getPrice());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.BASELINE_COST, entity.getBaselineCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.DEMAND_COST, entity.getDemandCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.ON_THE_WAY_COST, entity.getOnTheWayCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.INCURRED_COST, entity.getIncurredCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.REMAINING_COST, entity.getRemainingCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST, entity.getChangeAccumulateCost());
        return result;
    }

    /**
     * entity转map
     *
     * @param list
     * @return
     */
    public static List<Map> entity2MapBatch(List<ProjectWbsBudgetChangeHistory> list) {
        List<Map> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (ProjectWbsBudgetChangeHistory entity : list) {
            result.add(entity2GenericityMap(entity));
        }
        return result;
    }



    public static ProjectWbsBudgetChangeHistory map2Entity(Map<String, Object> map,  List<WbsDynamicFieldsDto> wbsDynamicFields) throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ProjectWbsBudgetChangeHistory entity = new ProjectWbsBudgetChangeHistory();

        entity.setId(MapUtils.getLong(map, "id"));
        entity.setProjectId(MapUtils.getLong(map, WbsBudgetFieldConstant.PROJECT_ID));
        entity.setProjectCode(MapUtils.getString(map, WbsBudgetFieldConstant.PROJECT_CODE));
        entity.setDescription(MapUtils.getString(map, WbsBudgetFieldConstant.DESCRIPTION));
        entity.setActivityOrderNo(MapUtils.getString(map, WbsBudgetFieldConstant.ACTIVITY_ORDER_NO));
        entity.setActivityCode(MapUtils.getString(map, WbsBudgetFieldConstant.ACTIVITY_CODE));
        entity.setActivityName(MapUtils.getString(map, WbsBudgetFieldConstant.ACTIVITY_NAME));
        entity.setActivityType(MapUtils.getString(map, WbsBudgetFieldConstant.ACTIVITY_TYPE));

        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.PRICE))) {
            entity.setPrice(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.PRICE)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.BASELINE_COST))) {
            entity.setBaselineCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.BASELINE_COST)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.DEMAND_COST))) {
            entity.setDemandCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.DEMAND_COST)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.ON_THE_WAY_COST))) {
            entity.setOnTheWayCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.ON_THE_WAY_COST)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.INCURRED_COST))) {
            entity.setIncurredCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.INCURRED_COST)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.REMAINING_COST))) {
            entity.setRemainingCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.REMAINING_COST)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST))) {
            entity.setChangeAccumulateCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST)));
        }
        entity.setParentWbsId(MapUtils.getLong(map, WbsBudgetFieldConstant.PARENT_WBS_ID));
        entity.setParentActivityId(MapUtils.getLong(map, WbsBudgetFieldConstant.PARENT_ACTIVITY_ID));
        entity.setOriginId(MapUtils.getLong(map, "originId"));
        entity.setHeaderId(MapUtils.getLong(map, "headerId"));
        entity.setHistoryType(MapUtils.getInteger(map, "historyType"));
        entity.setShowCase(MapUtils.getBoolean(map, "showCase"));

        entity.setCreateBy(MapUtils.getLong(map, WbsBudgetFieldConstant.CREATE_BY));
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.CREATE_AT))) {
            entity.setCreateAt(simpleDateFormat.parse(MapUtils.getString(map, WbsBudgetFieldConstant.CREATE_AT)));
        }
        entity.setUpdateBy(MapUtils.getLong(map, WbsBudgetFieldConstant.UPDATE_BY));
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.UPDATE_AT))) {
            entity.setUpdateAt(simpleDateFormat.parse(MapUtils.getString(map, WbsBudgetFieldConstant.UPDATE_AT)));
        }
        entity.setVersion(MapUtils.getLong(map, "version"));
        entity.setDeletedFlag(MapUtils.getBoolean(map, "deletedFlag"));

        StringBuilder wbsFullCode = new StringBuilder();
        StringBuilder wbsLastCode = new StringBuilder();
        StringBuilder dynamicWbsTemplateRuleIds = new StringBuilder();
        StringBuilder dynamicFields = new StringBuilder();
        StringBuilder dynamicValues = new StringBuilder();
        // 动态列获取wbs
        for (int i = 0; i < wbsDynamicFields.size(); i++) {
            WbsDynamicFieldsDto dynamicFieldDto = wbsDynamicFields.get(i);
            // wbs编码
            String wbsCode = MapUtils.getString(map, dynamicFieldDto.getKey());

            if (i == 0) {
                wbsFullCode.append(wbsCode);
                dynamicValues.append(wbsCode);
                dynamicWbsTemplateRuleIds.append(dynamicFieldDto.getWbsTemplateRuleId());
                dynamicFields.append(dynamicFieldDto.getKey());
            } else {
                wbsFullCode.append("-" + wbsCode);
                dynamicValues.append("," + wbsCode);
                dynamicWbsTemplateRuleIds.append("," + dynamicFieldDto.getWbsTemplateRuleId());
                dynamicFields.append("," + dynamicFieldDto.getKey());
            }

            if (i == wbsDynamicFields.size() - 1) {
                wbsLastCode.append(wbsCode);
            }
        }
        entity.setWbsFullCode(wbsFullCode.toString());
        if (StringUtils.isNotEmpty(entity.getProjectCode()) && StringUtils.isNotEmpty(entity.getWbsFullCode())) {
            entity.setWbsSummaryCode(String.format("%s-%s", entity.getProjectCode(), entity.getWbsFullCode()));
        }
        entity.setWbsLastCode(wbsLastCode.toString());
        entity.setDynamicWbsTemplateRuleIds(dynamicWbsTemplateRuleIds.toString());
        entity.setDynamicFields(dynamicFields.toString());
        entity.setDynamicValues(dynamicValues.toString());
        entity.setProjectWbsReceiptsId(MapUtils.getLong(map, WbsBudgetFieldConstant.PROJECT_WBS_RECEIPTS_ID));
        return entity;
    }

    public static List<ProjectWbsBudgetChangeHistory> map2EntityBatch(List<Map<String, Object>> list, List<WbsDynamicFieldsDto> wbsDynamicFields) throws Exception {
        List<ProjectWbsBudgetChangeHistory> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (Map<String, Object> map : list) {
            result.add(map2Entity(map, wbsDynamicFields));
        }
        return result;
    }

    public Boolean equlas(ProjectWbsBudgetChangeHistoryDto vo) {
        return this.getProjectId().equals(vo.getProjectId()) &&
                this.getProjectCode().equals(vo.getProjectCode()) &&
                this.getDescription().equals(vo.getDescription()) &&
                this.getActivityOrderNo().equals(vo.getActivityOrderNo()) &&
                this.getActivityCode().equals(vo.getActivityCode()) &&
                this.getActivityName().equals(vo.getActivityName()) &&
                this.getActivityType().equals(vo.getActivityType()) &&
                this.getPrice().compareTo(vo.getPrice()) == 0 &&
                this.getBaselineCost().compareTo(vo.getBaselineCost()) == 0 &&
                this.getDemandCost().compareTo(vo.getDemandCost()) == 0 &&
                this.getOnTheWayCost().compareTo(vo.getOnTheWayCost()) == 0 &&
                this.getIncurredCost().compareTo(vo.getIncurredCost()) == 0 &&
                this.getRemainingCost().compareTo(vo.getRemainingCost()) == 0 &&
                this.getChangeAccumulateCost().compareTo(vo.getChangeAccumulateCost()) == 0 &&
                this.getWbsFullCode().equals(vo.getWbsFullCode()) &&
                this.getWbsLastCode().equals(vo.getWbsLastCode()) &&
                this.getDynamicWbsTemplateRuleIds().equals(vo.getDynamicWbsTemplateRuleIds()) &&
                this.getDeletedFlag().equals(vo.getDeletedFlag());
    }

    public static Boolean equlas(Map<String, Object> history, Map<String, Object> change){
        return Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.PROJECT_ID), MapUtils.getString(change, WbsBudgetFieldConstant.PROJECT_ID))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.PROJECT_CODE), MapUtils.getString(change, WbsBudgetFieldConstant.PROJECT_CODE))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.DESCRIPTION), MapUtils.getString(change, WbsBudgetFieldConstant.DESCRIPTION))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.ACTIVITY_CODE), MapUtils.getString(change, WbsBudgetFieldConstant.ACTIVITY_CODE))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.ACTIVITY_NAME), MapUtils.getString(change, WbsBudgetFieldConstant.ACTIVITY_NAME))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.ACTIVITY_TYPE), MapUtils.getString(change, WbsBudgetFieldConstant.ACTIVITY_TYPE))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.ACTIVITY_ORDER_NO), MapUtils.getString(change, WbsBudgetFieldConstant.ACTIVITY_ORDER_NO))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.PRICE), MapUtils.getString(change, WbsBudgetFieldConstant.PRICE))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.BASELINE_COST), MapUtils.getString(change, WbsBudgetFieldConstant.BASELINE_COST))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.DEMAND_COST), MapUtils.getString(change, WbsBudgetFieldConstant.DEMAND_COST))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.ON_THE_WAY_COST), MapUtils.getString(change, WbsBudgetFieldConstant.ON_THE_WAY_COST))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.INCURRED_COST), MapUtils.getString(change, WbsBudgetFieldConstant.INCURRED_COST))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.REMAINING_COST), MapUtils.getString(change, WbsBudgetFieldConstant.REMAINING_COST))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST), MapUtils.getString(change, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.WBS_FULL_CODE), MapUtils.getString(change, WbsBudgetFieldConstant.WBS_FULL_CODE))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.WBS_LAST_CODE), MapUtils.getString(change, WbsBudgetFieldConstant.WBS_LAST_CODE))
                && Objects.equals(MapUtils.getString(history, WbsBudgetFieldConstant.DELETED_FLAG), MapUtils.getString(change, WbsBudgetFieldConstant.DELETED_FLAG));
    }
}
