package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "wbs预算编制change表")
public class ProjectWbsBudgetChangeHistory extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "活动事项序号")
    private String activityOrderNo;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "活动类别名称")
    private String activityName;

    @ApiModelProperty(value = "活动类别属性")
    private String activityType;

    @ApiModelProperty(value = "预算金额")
    private BigDecimal price;

    @ApiModelProperty(value = "预算基线")
    private BigDecimal baselineCost;

    @ApiModelProperty(value = "需求预算")
    private BigDecimal demandCost;

    @ApiModelProperty(value = "在途成本")
    private BigDecimal onTheWayCost;

    @ApiModelProperty(value = "已发生成本")
    private BigDecimal incurredCost;

    @ApiModelProperty(value = "剩余可用预算")
    private BigDecimal remainingCost;

    @ApiModelProperty(value = "累计变更金额")
    private BigDecimal changeAccumulateCost;

    @ApiModelProperty(value = "父级id（wbs）")
    private Long parentWbsId;

    @ApiModelProperty(value = "父级id（activity）")
    private Long parentActivityId;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "wbs短码")
    private String wbsFullCode;

    @ApiModelProperty(value = "wbs全码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "最底层wbs编码")
    private String wbsLastCode;

    @ApiModelProperty(value = "动态列wbs模板规则id数组")
    private String dynamicWbsTemplateRuleIds;

    @ApiModelProperty(value = "动态列字段名数组")
    private String dynamicFields;

    @ApiModelProperty(value = "动态列编码数组")
    private String dynamicValues;

    @ApiModelProperty(value = "原id")
    private Long originId;

    @ApiModelProperty(value = "头id")
    private Long headerId;

    @ApiModelProperty(value = "历史变更类型（0历史，1变更项）")
    private Integer historyType;

    @ApiModelProperty(value = "是否展示（0不展示，1展示）")
    private Boolean showCase;

    @ApiModelProperty(value = "经济事项id")
    private Long feeTypeId;

    @ApiModelProperty(value = "经济事项名称")
    private String feeTypeName;

    @ApiModelProperty(value = "经济事项是否同步ems（0不同步/1同步）")
    private Boolean feeSyncEms;

    @ApiModelProperty(value = "WBS变更单id")
    private Long projectWbsReceiptsId;

    private static final long serialVersionUID = 1L;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getActivityOrderNo() {
        return activityOrderNo;
    }

    public void setActivityOrderNo(String activityOrderNo) {
        this.activityOrderNo = activityOrderNo == null ? null : activityOrderNo.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName == null ? null : activityName.trim();
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType == null ? null : activityType.trim();
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getBaselineCost() {
        return baselineCost;
    }

    public void setBaselineCost(BigDecimal baselineCost) {
        this.baselineCost = baselineCost;
    }

    public BigDecimal getDemandCost() {
        return demandCost;
    }

    public void setDemandCost(BigDecimal demandCost) {
        this.demandCost = demandCost;
    }

    public BigDecimal getOnTheWayCost() {
        return onTheWayCost;
    }

    public void setOnTheWayCost(BigDecimal onTheWayCost) {
        this.onTheWayCost = onTheWayCost;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getRemainingCost() {
        return remainingCost;
    }

    public void setRemainingCost(BigDecimal remainingCost) {
        this.remainingCost = remainingCost;
    }

    public BigDecimal getChangeAccumulateCost() {
        return changeAccumulateCost;
    }

    public void setChangeAccumulateCost(BigDecimal changeAccumulateCost) {
        this.changeAccumulateCost = changeAccumulateCost;
    }

    public Long getParentWbsId() {
        return parentWbsId;
    }

    public void setParentWbsId(Long parentWbsId) {
        this.parentWbsId = parentWbsId;
    }

    public Long getParentActivityId() {
        return parentActivityId;
    }

    public void setParentActivityId(Long parentActivityId) {
        this.parentActivityId = parentActivityId;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getWbsFullCode() {
        return wbsFullCode;
    }

    public void setWbsFullCode(String wbsFullCode) {
        this.wbsFullCode = wbsFullCode == null ? null : wbsFullCode.trim();
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getWbsLastCode() {
        return wbsLastCode;
    }

    public void setWbsLastCode(String wbsLastCode) {
        this.wbsLastCode = wbsLastCode == null ? null : wbsLastCode.trim();
    }

    public String getDynamicWbsTemplateRuleIds() {
        return dynamicWbsTemplateRuleIds;
    }

    public void setDynamicWbsTemplateRuleIds(String dynamicWbsTemplateRuleIds) {
        this.dynamicWbsTemplateRuleIds = dynamicWbsTemplateRuleIds == null ? null : dynamicWbsTemplateRuleIds.trim();
    }

    public String getDynamicFields() {
        return dynamicFields;
    }

    public void setDynamicFields(String dynamicFields) {
        this.dynamicFields = dynamicFields == null ? null : dynamicFields.trim();
    }

    public String getDynamicValues() {
        return dynamicValues;
    }

    public void setDynamicValues(String dynamicValues) {
        this.dynamicValues = dynamicValues == null ? null : dynamicValues.trim();
    }

    public Long getOriginId() {
        return originId;
    }

    public void setOriginId(Long originId) {
        this.originId = originId;
    }

    public Long getHeaderId() {
        return headerId;
    }

    public void setHeaderId(Long headerId) {
        this.headerId = headerId;
    }

    public Integer getHistoryType() {
        return historyType;
    }

    public void setHistoryType(Integer historyType) {
        this.historyType = historyType;
    }

    public Boolean getShowCase() {
        return showCase;
    }

    public void setShowCase(Boolean showCase) {
        this.showCase = showCase;
    }

    public Long getFeeTypeId() {
        return feeTypeId;
    }

    public void setFeeTypeId(Long feeTypeId) {
        this.feeTypeId = feeTypeId;
    }

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName == null ? null : feeTypeName.trim();
    }

    public Boolean getFeeSyncEms() {
        return feeSyncEms;
    }

    public void setFeeSyncEms(Boolean feeSyncEms) {
        this.feeSyncEms = feeSyncEms;
    }

    public Long getProjectWbsReceiptsId() {
        return projectWbsReceiptsId;
    }

    public void setProjectWbsReceiptsId(Long projectWbsReceiptsId) {
        this.projectWbsReceiptsId = projectWbsReceiptsId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", description=").append(description);
        sb.append(", activityOrderNo=").append(activityOrderNo);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", activityName=").append(activityName);
        sb.append(", activityType=").append(activityType);
        sb.append(", price=").append(price);
        sb.append(", baselineCost=").append(baselineCost);
        sb.append(", demandCost=").append(demandCost);
        sb.append(", onTheWayCost=").append(onTheWayCost);
        sb.append(", incurredCost=").append(incurredCost);
        sb.append(", remainingCost=").append(remainingCost);
        sb.append(", changeAccumulateCost=").append(changeAccumulateCost);
        sb.append(", parentWbsId=").append(parentWbsId);
        sb.append(", parentActivityId=").append(parentActivityId);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", version=").append(version);
        sb.append(", wbsFullCode=").append(wbsFullCode);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", wbsLastCode=").append(wbsLastCode);
        sb.append(", dynamicWbsTemplateRuleIds=").append(dynamicWbsTemplateRuleIds);
        sb.append(", dynamicFields=").append(dynamicFields);
        sb.append(", dynamicValues=").append(dynamicValues);
        sb.append(", originId=").append(originId);
        sb.append(", headerId=").append(headerId);
        sb.append(", historyType=").append(historyType);
        sb.append(", showCase=").append(showCase);
        sb.append(", feeTypeId=").append(feeTypeId);
        sb.append(", feeTypeName=").append(feeTypeName);
        sb.append(", feeSyncEms=").append(feeSyncEms);
        sb.append(", projectWbsReceiptsId=").append(projectWbsReceiptsId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}