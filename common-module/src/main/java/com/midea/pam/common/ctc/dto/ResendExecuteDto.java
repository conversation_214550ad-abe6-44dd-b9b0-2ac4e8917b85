package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.ResendExecute;

import java.util.Date;
import java.util.List;

/**
 * @program: pam
 * @description: ResendExecuteDto
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
public class ResendExecuteDto extends ResendExecute {

    private Integer pageNum;

    private Integer pageSize;

    private List<String> businessTypeList;

    private List<String> applyNoList;
    private List<String> subApplyNoList;

    private Date createAtStart;

    private Date createAtEnd;

    private Date updateAtStart;

    private Date updateAtEnd;

    private String pamCode;

    private String erpCode;

    private Long organizationId;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getBusinessTypeList() {
        return businessTypeList;
    }

    public void setBusinessTypeList(List<String> businessTypeList) {
        this.businessTypeList = businessTypeList;
    }

    public List<String> getApplyNoList() {
        return applyNoList;
    }

    public void setApplyNoList(List<String> applyNoList) {
        this.applyNoList = applyNoList;
    }

    public Date getCreateAtStart() {
        return createAtStart;
    }

    public void setCreateAtStart(Date createAtStart) {
        this.createAtStart = createAtStart;
    }

    public Date getCreateAtEnd() {
        return createAtEnd;
    }

    public void setCreateAtEnd(Date createAtEnd) {
        this.createAtEnd = createAtEnd;
    }

    public Date getUpdateAtStart() {
        return updateAtStart;
    }

    public void setUpdateAtStart(Date updateAtStart) {
        this.updateAtStart = updateAtStart;
    }

    public Date getUpdateAtEnd() {
        return updateAtEnd;
    }

    public void setUpdateAtEnd(Date updateAtEnd) {
        this.updateAtEnd = updateAtEnd;
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    public List<String> getSubApplyNoList() {
        return subApplyNoList;
    }

    public void setSubApplyNoList(List<String> subApplyNoList) {
        this.subApplyNoList = subApplyNoList;
    }
}
