package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PurchaseContractChangeHistoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PurchaseContractChangeHistoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andHeaderIdIsNull() {
            addCriterion("header_id is null");
            return (Criteria) this;
        }

        public Criteria andHeaderIdIsNotNull() {
            addCriterion("header_id is not null");
            return (Criteria) this;
        }

        public Criteria andHeaderIdEqualTo(Long value) {
            addCriterion("header_id =", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdNotEqualTo(Long value) {
            addCriterion("header_id <>", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdGreaterThan(Long value) {
            addCriterion("header_id >", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("header_id >=", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdLessThan(Long value) {
            addCriterion("header_id <", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdLessThanOrEqualTo(Long value) {
            addCriterion("header_id <=", value, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdIn(List<Long> values) {
            addCriterion("header_id in", values, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdNotIn(List<Long> values) {
            addCriterion("header_id not in", values, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdBetween(Long value1, Long value2) {
            addCriterion("header_id between", value1, value2, "headerId");
            return (Criteria) this;
        }

        public Criteria andHeaderIdNotBetween(Long value1, Long value2) {
            addCriterion("header_id not between", value1, value2, "headerId");
            return (Criteria) this;
        }

        public Criteria andOriginIdIsNull() {
            addCriterion("origin_id is null");
            return (Criteria) this;
        }

        public Criteria andOriginIdIsNotNull() {
            addCriterion("origin_id is not null");
            return (Criteria) this;
        }

        public Criteria andOriginIdEqualTo(Long value) {
            addCriterion("origin_id =", value, "originId");
            return (Criteria) this;
        }

        public Criteria andOriginIdNotEqualTo(Long value) {
            addCriterion("origin_id <>", value, "originId");
            return (Criteria) this;
        }

        public Criteria andOriginIdGreaterThan(Long value) {
            addCriterion("origin_id >", value, "originId");
            return (Criteria) this;
        }

        public Criteria andOriginIdGreaterThanOrEqualTo(Long value) {
            addCriterion("origin_id >=", value, "originId");
            return (Criteria) this;
        }

        public Criteria andOriginIdLessThan(Long value) {
            addCriterion("origin_id <", value, "originId");
            return (Criteria) this;
        }

        public Criteria andOriginIdLessThanOrEqualTo(Long value) {
            addCriterion("origin_id <=", value, "originId");
            return (Criteria) this;
        }

        public Criteria andOriginIdIn(List<Long> values) {
            addCriterion("origin_id in", values, "originId");
            return (Criteria) this;
        }

        public Criteria andOriginIdNotIn(List<Long> values) {
            addCriterion("origin_id not in", values, "originId");
            return (Criteria) this;
        }

        public Criteria andOriginIdBetween(Long value1, Long value2) {
            addCriterion("origin_id between", value1, value2, "originId");
            return (Criteria) this;
        }

        public Criteria andOriginIdNotBetween(Long value1, Long value2) {
            addCriterion("origin_id not between", value1, value2, "originId");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeIsNull() {
            addCriterion("history_type is null");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeIsNotNull() {
            addCriterion("history_type is not null");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeEqualTo(Integer value) {
            addCriterion("history_type =", value, "historyType");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeNotEqualTo(Integer value) {
            addCriterion("history_type <>", value, "historyType");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeGreaterThan(Integer value) {
            addCriterion("history_type >", value, "historyType");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("history_type >=", value, "historyType");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeLessThan(Integer value) {
            addCriterion("history_type <", value, "historyType");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeLessThanOrEqualTo(Integer value) {
            addCriterion("history_type <=", value, "historyType");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeIn(List<Integer> values) {
            addCriterion("history_type in", values, "historyType");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeNotIn(List<Integer> values) {
            addCriterion("history_type not in", values, "historyType");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeBetween(Integer value1, Integer value2) {
            addCriterion("history_type between", value1, value2, "historyType");
            return (Criteria) this;
        }

        public Criteria andHistoryTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("history_type not between", value1, value2, "historyType");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andTypeIdIsNull() {
            addCriterion("type_id is null");
            return (Criteria) this;
        }

        public Criteria andTypeIdIsNotNull() {
            addCriterion("type_id is not null");
            return (Criteria) this;
        }

        public Criteria andTypeIdEqualTo(Long value) {
            addCriterion("type_id =", value, "typeId");
            return (Criteria) this;
        }

        public Criteria andTypeIdNotEqualTo(Long value) {
            addCriterion("type_id <>", value, "typeId");
            return (Criteria) this;
        }

        public Criteria andTypeIdGreaterThan(Long value) {
            addCriterion("type_id >", value, "typeId");
            return (Criteria) this;
        }

        public Criteria andTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("type_id >=", value, "typeId");
            return (Criteria) this;
        }

        public Criteria andTypeIdLessThan(Long value) {
            addCriterion("type_id <", value, "typeId");
            return (Criteria) this;
        }

        public Criteria andTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("type_id <=", value, "typeId");
            return (Criteria) this;
        }

        public Criteria andTypeIdIn(List<Long> values) {
            addCriterion("type_id in", values, "typeId");
            return (Criteria) this;
        }

        public Criteria andTypeIdNotIn(List<Long> values) {
            addCriterion("type_id not in", values, "typeId");
            return (Criteria) this;
        }

        public Criteria andTypeIdBetween(Long value1, Long value2) {
            addCriterion("type_id between", value1, value2, "typeId");
            return (Criteria) this;
        }

        public Criteria andTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("type_id not between", value1, value2, "typeId");
            return (Criteria) this;
        }

        public Criteria andTypeNameIsNull() {
            addCriterion("type_name is null");
            return (Criteria) this;
        }

        public Criteria andTypeNameIsNotNull() {
            addCriterion("type_name is not null");
            return (Criteria) this;
        }

        public Criteria andTypeNameEqualTo(String value) {
            addCriterion("type_name =", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotEqualTo(String value) {
            addCriterion("type_name <>", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameGreaterThan(String value) {
            addCriterion("type_name >", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("type_name >=", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameLessThan(String value) {
            addCriterion("type_name <", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameLessThanOrEqualTo(String value) {
            addCriterion("type_name <=", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameLike(String value) {
            addCriterion("type_name like", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotLike(String value) {
            addCriterion("type_name not like", value, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameIn(List<String> values) {
            addCriterion("type_name in", values, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotIn(List<String> values) {
            addCriterion("type_name not in", values, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameBetween(String value1, String value2) {
            addCriterion("type_name between", value1, value2, "typeName");
            return (Criteria) this;
        }

        public Criteria andTypeNameNotBetween(String value1, String value2) {
            addCriterion("type_name not between", value1, value2, "typeName");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeIsNull() {
            addCriterion("legal_affairs_code is null");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeIsNotNull() {
            addCriterion("legal_affairs_code is not null");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeEqualTo(String value) {
            addCriterion("legal_affairs_code =", value, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeNotEqualTo(String value) {
            addCriterion("legal_affairs_code <>", value, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeGreaterThan(String value) {
            addCriterion("legal_affairs_code >", value, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("legal_affairs_code >=", value, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeLessThan(String value) {
            addCriterion("legal_affairs_code <", value, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeLessThanOrEqualTo(String value) {
            addCriterion("legal_affairs_code <=", value, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeLike(String value) {
            addCriterion("legal_affairs_code like", value, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeNotLike(String value) {
            addCriterion("legal_affairs_code not like", value, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeIn(List<String> values) {
            addCriterion("legal_affairs_code in", values, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeNotIn(List<String> values) {
            addCriterion("legal_affairs_code not in", values, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeBetween(String value1, String value2) {
            addCriterion("legal_affairs_code between", value1, value2, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andLegalAffairsCodeNotBetween(String value1, String value2) {
            addCriterion("legal_affairs_code not between", value1, value2, "legalAffairsCode");
            return (Criteria) this;
        }

        public Criteria andVendorIdIsNull() {
            addCriterion("vendor_id is null");
            return (Criteria) this;
        }

        public Criteria andVendorIdIsNotNull() {
            addCriterion("vendor_id is not null");
            return (Criteria) this;
        }

        public Criteria andVendorIdEqualTo(Long value) {
            addCriterion("vendor_id =", value, "vendorId");
            return (Criteria) this;
        }

        public Criteria andVendorIdNotEqualTo(Long value) {
            addCriterion("vendor_id <>", value, "vendorId");
            return (Criteria) this;
        }

        public Criteria andVendorIdGreaterThan(Long value) {
            addCriterion("vendor_id >", value, "vendorId");
            return (Criteria) this;
        }

        public Criteria andVendorIdGreaterThanOrEqualTo(Long value) {
            addCriterion("vendor_id >=", value, "vendorId");
            return (Criteria) this;
        }

        public Criteria andVendorIdLessThan(Long value) {
            addCriterion("vendor_id <", value, "vendorId");
            return (Criteria) this;
        }

        public Criteria andVendorIdLessThanOrEqualTo(Long value) {
            addCriterion("vendor_id <=", value, "vendorId");
            return (Criteria) this;
        }

        public Criteria andVendorIdIn(List<Long> values) {
            addCriterion("vendor_id in", values, "vendorId");
            return (Criteria) this;
        }

        public Criteria andVendorIdNotIn(List<Long> values) {
            addCriterion("vendor_id not in", values, "vendorId");
            return (Criteria) this;
        }

        public Criteria andVendorIdBetween(Long value1, Long value2) {
            addCriterion("vendor_id between", value1, value2, "vendorId");
            return (Criteria) this;
        }

        public Criteria andVendorIdNotBetween(Long value1, Long value2) {
            addCriterion("vendor_id not between", value1, value2, "vendorId");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNull() {
            addCriterion("vendor_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNotNull() {
            addCriterion("vendor_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeEqualTo(String value) {
            addCriterion("vendor_code =", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotEqualTo(String value) {
            addCriterion("vendor_code <>", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThan(String value) {
            addCriterion("vendor_code >", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_code >=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThan(String value) {
            addCriterion("vendor_code <", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_code <=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLike(String value) {
            addCriterion("vendor_code like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotLike(String value) {
            addCriterion("vendor_code not like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIn(List<String> values) {
            addCriterion("vendor_code in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotIn(List<String> values) {
            addCriterion("vendor_code not in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeBetween(String value1, String value2) {
            addCriterion("vendor_code between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_code not between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNull() {
            addCriterion("vendor_name is null");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNotNull() {
            addCriterion("vendor_name is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNameEqualTo(String value) {
            addCriterion("vendor_name =", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotEqualTo(String value) {
            addCriterion("vendor_name <>", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThan(String value) {
            addCriterion("vendor_name >", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_name >=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThan(String value) {
            addCriterion("vendor_name <", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanOrEqualTo(String value) {
            addCriterion("vendor_name <=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLike(String value) {
            addCriterion("vendor_name like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotLike(String value) {
            addCriterion("vendor_name not like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameIn(List<String> values) {
            addCriterion("vendor_name in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotIn(List<String> values) {
            addCriterion("vendor_name not in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameBetween(String value1, String value2) {
            addCriterion("vendor_name between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotBetween(String value1, String value2) {
            addCriterion("vendor_name not between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdIsNull() {
            addCriterion("erp_vendor_site_id is null");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdIsNotNull() {
            addCriterion("erp_vendor_site_id is not null");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdEqualTo(String value) {
            addCriterion("erp_vendor_site_id =", value, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdNotEqualTo(String value) {
            addCriterion("erp_vendor_site_id <>", value, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdGreaterThan(String value) {
            addCriterion("erp_vendor_site_id >", value, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdGreaterThanOrEqualTo(String value) {
            addCriterion("erp_vendor_site_id >=", value, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdLessThan(String value) {
            addCriterion("erp_vendor_site_id <", value, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdLessThanOrEqualTo(String value) {
            addCriterion("erp_vendor_site_id <=", value, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdLike(String value) {
            addCriterion("erp_vendor_site_id like", value, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdNotLike(String value) {
            addCriterion("erp_vendor_site_id not like", value, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdIn(List<String> values) {
            addCriterion("erp_vendor_site_id in", values, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdNotIn(List<String> values) {
            addCriterion("erp_vendor_site_id not in", values, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdBetween(String value1, String value2) {
            addCriterion("erp_vendor_site_id between", value1, value2, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andErpVendorSiteIdNotBetween(String value1, String value2) {
            addCriterion("erp_vendor_site_id not between", value1, value2, "erpVendorSiteId");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIsNull() {
            addCriterion("vendor_site_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIsNotNull() {
            addCriterion("vendor_site_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeEqualTo(String value) {
            addCriterion("vendor_site_code =", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotEqualTo(String value) {
            addCriterion("vendor_site_code <>", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeGreaterThan(String value) {
            addCriterion("vendor_site_code >", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_site_code >=", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLessThan(String value) {
            addCriterion("vendor_site_code <", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_site_code <=", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeLike(String value) {
            addCriterion("vendor_site_code like", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotLike(String value) {
            addCriterion("vendor_site_code not like", value, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeIn(List<String> values) {
            addCriterion("vendor_site_code in", values, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotIn(List<String> values) {
            addCriterion("vendor_site_code not in", values, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeBetween(String value1, String value2) {
            addCriterion("vendor_site_code between", value1, value2, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andVendorSiteCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_site_code not between", value1, value2, "vendorSiteCode");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountIsNull() {
            addCriterion("excluding_tax_amount is null");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountIsNotNull() {
            addCriterion("excluding_tax_amount is not null");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount =", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountNotEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount <>", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountGreaterThan(BigDecimal value) {
            addCriterion("excluding_tax_amount >", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount >=", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountLessThan(BigDecimal value) {
            addCriterion("excluding_tax_amount <", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("excluding_tax_amount <=", value, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountIn(List<BigDecimal> values) {
            addCriterion("excluding_tax_amount in", values, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountNotIn(List<BigDecimal> values) {
            addCriterion("excluding_tax_amount not in", values, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("excluding_tax_amount between", value1, value2, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andExcludingTaxAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("excluding_tax_amount not between", value1, value2, "excludingTaxAmount");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andManagerIsNull() {
            addCriterion("manager is null");
            return (Criteria) this;
        }

        public Criteria andManagerIsNotNull() {
            addCriterion("manager is not null");
            return (Criteria) this;
        }

        public Criteria andManagerEqualTo(Long value) {
            addCriterion("manager =", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNotEqualTo(Long value) {
            addCriterion("manager <>", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerGreaterThan(Long value) {
            addCriterion("manager >", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerGreaterThanOrEqualTo(Long value) {
            addCriterion("manager >=", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerLessThan(Long value) {
            addCriterion("manager <", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerLessThanOrEqualTo(Long value) {
            addCriterion("manager <=", value, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerIn(List<Long> values) {
            addCriterion("manager in", values, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNotIn(List<Long> values) {
            addCriterion("manager not in", values, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerBetween(Long value1, Long value2) {
            addCriterion("manager between", value1, value2, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNotBetween(Long value1, Long value2) {
            addCriterion("manager not between", value1, value2, "manager");
            return (Criteria) this;
        }

        public Criteria andManagerNameIsNull() {
            addCriterion("manager_name is null");
            return (Criteria) this;
        }

        public Criteria andManagerNameIsNotNull() {
            addCriterion("manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andManagerNameEqualTo(String value) {
            addCriterion("manager_name =", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotEqualTo(String value) {
            addCriterion("manager_name <>", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameGreaterThan(String value) {
            addCriterion("manager_name >", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("manager_name >=", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLessThan(String value) {
            addCriterion("manager_name <", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLessThanOrEqualTo(String value) {
            addCriterion("manager_name <=", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameLike(String value) {
            addCriterion("manager_name like", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotLike(String value) {
            addCriterion("manager_name not like", value, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameIn(List<String> values) {
            addCriterion("manager_name in", values, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotIn(List<String> values) {
            addCriterion("manager_name not in", values, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameBetween(String value1, String value2) {
            addCriterion("manager_name between", value1, value2, "managerName");
            return (Criteria) this;
        }

        public Criteria andManagerNameNotBetween(String value1, String value2) {
            addCriterion("manager_name not between", value1, value2, "managerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerIsNull() {
            addCriterion("purchasing_follower is null");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerIsNotNull() {
            addCriterion("purchasing_follower is not null");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerEqualTo(Long value) {
            addCriterion("purchasing_follower =", value, "purchasingFollower");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNotEqualTo(Long value) {
            addCriterion("purchasing_follower <>", value, "purchasingFollower");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerGreaterThan(Long value) {
            addCriterion("purchasing_follower >", value, "purchasingFollower");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerGreaterThanOrEqualTo(Long value) {
            addCriterion("purchasing_follower >=", value, "purchasingFollower");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerLessThan(Long value) {
            addCriterion("purchasing_follower <", value, "purchasingFollower");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerLessThanOrEqualTo(Long value) {
            addCriterion("purchasing_follower <=", value, "purchasingFollower");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerIn(List<Long> values) {
            addCriterion("purchasing_follower in", values, "purchasingFollower");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNotIn(List<Long> values) {
            addCriterion("purchasing_follower not in", values, "purchasingFollower");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerBetween(Long value1, Long value2) {
            addCriterion("purchasing_follower between", value1, value2, "purchasingFollower");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNotBetween(Long value1, Long value2) {
            addCriterion("purchasing_follower not between", value1, value2, "purchasingFollower");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameIsNull() {
            addCriterion("purchasing_follower_name is null");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameIsNotNull() {
            addCriterion("purchasing_follower_name is not null");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameEqualTo(String value) {
            addCriterion("purchasing_follower_name =", value, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameNotEqualTo(String value) {
            addCriterion("purchasing_follower_name <>", value, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameGreaterThan(String value) {
            addCriterion("purchasing_follower_name >", value, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameGreaterThanOrEqualTo(String value) {
            addCriterion("purchasing_follower_name >=", value, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameLessThan(String value) {
            addCriterion("purchasing_follower_name <", value, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameLessThanOrEqualTo(String value) {
            addCriterion("purchasing_follower_name <=", value, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameLike(String value) {
            addCriterion("purchasing_follower_name like", value, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameNotLike(String value) {
            addCriterion("purchasing_follower_name not like", value, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameIn(List<String> values) {
            addCriterion("purchasing_follower_name in", values, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameNotIn(List<String> values) {
            addCriterion("purchasing_follower_name not in", values, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameBetween(String value1, String value2) {
            addCriterion("purchasing_follower_name between", value1, value2, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andPurchasingFollowerNameNotBetween(String value1, String value2) {
            addCriterion("purchasing_follower_name not between", value1, value2, "purchasingFollowerName");
            return (Criteria) this;
        }

        public Criteria andClassesIsNull() {
            addCriterion("classes is null");
            return (Criteria) this;
        }

        public Criteria andClassesIsNotNull() {
            addCriterion("classes is not null");
            return (Criteria) this;
        }

        public Criteria andClassesEqualTo(Integer value) {
            addCriterion("classes =", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesNotEqualTo(Integer value) {
            addCriterion("classes <>", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesGreaterThan(Integer value) {
            addCriterion("classes >", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesGreaterThanOrEqualTo(Integer value) {
            addCriterion("classes >=", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesLessThan(Integer value) {
            addCriterion("classes <", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesLessThanOrEqualTo(Integer value) {
            addCriterion("classes <=", value, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesIn(List<Integer> values) {
            addCriterion("classes in", values, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesNotIn(List<Integer> values) {
            addCriterion("classes not in", values, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesBetween(Integer value1, Integer value2) {
            addCriterion("classes between", value1, value2, "classes");
            return (Criteria) this;
        }

        public Criteria andClassesNotBetween(Integer value1, Integer value2) {
            addCriterion("classes not between", value1, value2, "classes");
            return (Criteria) this;
        }

        public Criteria andBelongAreaIsNull() {
            addCriterion("belong_area is null");
            return (Criteria) this;
        }

        public Criteria andBelongAreaIsNotNull() {
            addCriterion("belong_area is not null");
            return (Criteria) this;
        }

        public Criteria andBelongAreaEqualTo(Integer value) {
            addCriterion("belong_area =", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaNotEqualTo(Integer value) {
            addCriterion("belong_area <>", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaGreaterThan(Integer value) {
            addCriterion("belong_area >", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaGreaterThanOrEqualTo(Integer value) {
            addCriterion("belong_area >=", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaLessThan(Integer value) {
            addCriterion("belong_area <", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaLessThanOrEqualTo(Integer value) {
            addCriterion("belong_area <=", value, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaIn(List<Integer> values) {
            addCriterion("belong_area in", values, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaNotIn(List<Integer> values) {
            addCriterion("belong_area not in", values, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaBetween(Integer value1, Integer value2) {
            addCriterion("belong_area between", value1, value2, "belongArea");
            return (Criteria) this;
        }

        public Criteria andBelongAreaNotBetween(Integer value1, Integer value2) {
            addCriterion("belong_area not between", value1, value2, "belongArea");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIsNull() {
            addCriterion("payment_method is null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIsNotNull() {
            addCriterion("payment_method is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodEqualTo(String value) {
            addCriterion("payment_method =", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotEqualTo(String value) {
            addCriterion("payment_method <>", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodGreaterThan(String value) {
            addCriterion("payment_method >", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodGreaterThanOrEqualTo(String value) {
            addCriterion("payment_method >=", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLessThan(String value) {
            addCriterion("payment_method <", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLessThanOrEqualTo(String value) {
            addCriterion("payment_method <=", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodLike(String value) {
            addCriterion("payment_method like", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotLike(String value) {
            addCriterion("payment_method not like", value, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodIn(List<String> values) {
            addCriterion("payment_method in", values, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotIn(List<String> values) {
            addCriterion("payment_method not in", values, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodBetween(String value1, String value2) {
            addCriterion("payment_method between", value1, value2, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andPaymentMethodNotBetween(String value1, String value2) {
            addCriterion("payment_method not between", value1, value2, "paymentMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNull() {
            addCriterion("invoice_type is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNotNull() {
            addCriterion("invoice_type is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeEqualTo(Integer value) {
            addCriterion("invoice_type =", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotEqualTo(Integer value) {
            addCriterion("invoice_type <>", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThan(Integer value) {
            addCriterion("invoice_type >", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("invoice_type >=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThan(Integer value) {
            addCriterion("invoice_type <", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("invoice_type <=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIn(List<Integer> values) {
            addCriterion("invoice_type in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotIn(List<Integer> values) {
            addCriterion("invoice_type not in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeBetween(Integer value1, Integer value2) {
            addCriterion("invoice_type between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("invoice_type not between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractIsNull() {
            addCriterion("is_electronic_contract is null");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractIsNotNull() {
            addCriterion("is_electronic_contract is not null");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractEqualTo(Boolean value) {
            addCriterion("is_electronic_contract =", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractNotEqualTo(Boolean value) {
            addCriterion("is_electronic_contract <>", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractGreaterThan(Boolean value) {
            addCriterion("is_electronic_contract >", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_electronic_contract >=", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractLessThan(Boolean value) {
            addCriterion("is_electronic_contract <", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractLessThanOrEqualTo(Boolean value) {
            addCriterion("is_electronic_contract <=", value, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractIn(List<Boolean> values) {
            addCriterion("is_electronic_contract in", values, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractNotIn(List<Boolean> values) {
            addCriterion("is_electronic_contract not in", values, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractBetween(Boolean value1, Boolean value2) {
            addCriterion("is_electronic_contract between", value1, value2, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andIsElectronicContractNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_electronic_contract not between", value1, value2, "isElectronicContract");
            return (Criteria) this;
        }

        public Criteria andOtherNameIsNull() {
            addCriterion("other_name is null");
            return (Criteria) this;
        }

        public Criteria andOtherNameIsNotNull() {
            addCriterion("other_name is not null");
            return (Criteria) this;
        }

        public Criteria andOtherNameEqualTo(String value) {
            addCriterion("other_name =", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameNotEqualTo(String value) {
            addCriterion("other_name <>", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameGreaterThan(String value) {
            addCriterion("other_name >", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameGreaterThanOrEqualTo(String value) {
            addCriterion("other_name >=", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameLessThan(String value) {
            addCriterion("other_name <", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameLessThanOrEqualTo(String value) {
            addCriterion("other_name <=", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameLike(String value) {
            addCriterion("other_name like", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameNotLike(String value) {
            addCriterion("other_name not like", value, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameIn(List<String> values) {
            addCriterion("other_name in", values, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameNotIn(List<String> values) {
            addCriterion("other_name not in", values, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameBetween(String value1, String value2) {
            addCriterion("other_name between", value1, value2, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherNameNotBetween(String value1, String value2) {
            addCriterion("other_name not between", value1, value2, "otherName");
            return (Criteria) this;
        }

        public Criteria andOtherIdIsNull() {
            addCriterion("other_id is null");
            return (Criteria) this;
        }

        public Criteria andOtherIdIsNotNull() {
            addCriterion("other_id is not null");
            return (Criteria) this;
        }

        public Criteria andOtherIdEqualTo(String value) {
            addCriterion("other_id =", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdNotEqualTo(String value) {
            addCriterion("other_id <>", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdGreaterThan(String value) {
            addCriterion("other_id >", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdGreaterThanOrEqualTo(String value) {
            addCriterion("other_id >=", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdLessThan(String value) {
            addCriterion("other_id <", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdLessThanOrEqualTo(String value) {
            addCriterion("other_id <=", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdLike(String value) {
            addCriterion("other_id like", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdNotLike(String value) {
            addCriterion("other_id not like", value, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdIn(List<String> values) {
            addCriterion("other_id in", values, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdNotIn(List<String> values) {
            addCriterion("other_id not in", values, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdBetween(String value1, String value2) {
            addCriterion("other_id between", value1, value2, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherIdNotBetween(String value1, String value2) {
            addCriterion("other_id not between", value1, value2, "otherId");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneIsNull() {
            addCriterion("other_phone is null");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneIsNotNull() {
            addCriterion("other_phone is not null");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneEqualTo(String value) {
            addCriterion("other_phone =", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneNotEqualTo(String value) {
            addCriterion("other_phone <>", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneGreaterThan(String value) {
            addCriterion("other_phone >", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("other_phone >=", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneLessThan(String value) {
            addCriterion("other_phone <", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneLessThanOrEqualTo(String value) {
            addCriterion("other_phone <=", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneLike(String value) {
            addCriterion("other_phone like", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneNotLike(String value) {
            addCriterion("other_phone not like", value, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneIn(List<String> values) {
            addCriterion("other_phone in", values, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneNotIn(List<String> values) {
            addCriterion("other_phone not in", values, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneBetween(String value1, String value2) {
            addCriterion("other_phone between", value1, value2, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherPhoneNotBetween(String value1, String value2) {
            addCriterion("other_phone not between", value1, value2, "otherPhone");
            return (Criteria) this;
        }

        public Criteria andOtherEmailIsNull() {
            addCriterion("other_email is null");
            return (Criteria) this;
        }

        public Criteria andOtherEmailIsNotNull() {
            addCriterion("other_email is not null");
            return (Criteria) this;
        }

        public Criteria andOtherEmailEqualTo(String value) {
            addCriterion("other_email =", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailNotEqualTo(String value) {
            addCriterion("other_email <>", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailGreaterThan(String value) {
            addCriterion("other_email >", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailGreaterThanOrEqualTo(String value) {
            addCriterion("other_email >=", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailLessThan(String value) {
            addCriterion("other_email <", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailLessThanOrEqualTo(String value) {
            addCriterion("other_email <=", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailLike(String value) {
            addCriterion("other_email like", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailNotLike(String value) {
            addCriterion("other_email not like", value, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailIn(List<String> values) {
            addCriterion("other_email in", values, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailNotIn(List<String> values) {
            addCriterion("other_email not in", values, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailBetween(String value1, String value2) {
            addCriterion("other_email between", value1, value2, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andOtherEmailNotBetween(String value1, String value2) {
            addCriterion("other_email not between", value1, value2, "otherEmail");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateIsNull() {
            addCriterion("public_or_private is null");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateIsNotNull() {
            addCriterion("public_or_private is not null");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateEqualTo(Integer value) {
            addCriterion("public_or_private =", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateNotEqualTo(Integer value) {
            addCriterion("public_or_private <>", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateGreaterThan(Integer value) {
            addCriterion("public_or_private >", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateGreaterThanOrEqualTo(Integer value) {
            addCriterion("public_or_private >=", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateLessThan(Integer value) {
            addCriterion("public_or_private <", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateLessThanOrEqualTo(Integer value) {
            addCriterion("public_or_private <=", value, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateIn(List<Integer> values) {
            addCriterion("public_or_private in", values, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateNotIn(List<Integer> values) {
            addCriterion("public_or_private not in", values, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateBetween(Integer value1, Integer value2) {
            addCriterion("public_or_private between", value1, value2, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andPublicOrPrivateNotBetween(Integer value1, Integer value2) {
            addCriterion("public_or_private not between", value1, value2, "publicOrPrivate");
            return (Criteria) this;
        }

        public Criteria andSealCategoryIsNull() {
            addCriterion("seal_category is null");
            return (Criteria) this;
        }

        public Criteria andSealCategoryIsNotNull() {
            addCriterion("seal_category is not null");
            return (Criteria) this;
        }

        public Criteria andSealCategoryEqualTo(Integer value) {
            addCriterion("seal_category =", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryNotEqualTo(Integer value) {
            addCriterion("seal_category <>", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryGreaterThan(Integer value) {
            addCriterion("seal_category >", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("seal_category >=", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryLessThan(Integer value) {
            addCriterion("seal_category <", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryLessThanOrEqualTo(Integer value) {
            addCriterion("seal_category <=", value, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryIn(List<Integer> values) {
            addCriterion("seal_category in", values, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryNotIn(List<Integer> values) {
            addCriterion("seal_category not in", values, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryBetween(Integer value1, Integer value2) {
            addCriterion("seal_category between", value1, value2, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealCategoryNotBetween(Integer value1, Integer value2) {
            addCriterion("seal_category not between", value1, value2, "sealCategory");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsIsNull() {
            addCriterion("seal_admin_account_ids is null");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsIsNotNull() {
            addCriterion("seal_admin_account_ids is not null");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsEqualTo(String value) {
            addCriterion("seal_admin_account_ids =", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsNotEqualTo(String value) {
            addCriterion("seal_admin_account_ids <>", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsGreaterThan(String value) {
            addCriterion("seal_admin_account_ids >", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsGreaterThanOrEqualTo(String value) {
            addCriterion("seal_admin_account_ids >=", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsLessThan(String value) {
            addCriterion("seal_admin_account_ids <", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsLessThanOrEqualTo(String value) {
            addCriterion("seal_admin_account_ids <=", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsLike(String value) {
            addCriterion("seal_admin_account_ids like", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsNotLike(String value) {
            addCriterion("seal_admin_account_ids not like", value, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsIn(List<String> values) {
            addCriterion("seal_admin_account_ids in", values, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsNotIn(List<String> values) {
            addCriterion("seal_admin_account_ids not in", values, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsBetween(String value1, String value2) {
            addCriterion("seal_admin_account_ids between", value1, value2, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andSealAdminAccountIdsNotBetween(String value1, String value2) {
            addCriterion("seal_admin_account_ids not between", value1, value2, "sealAdminAccountIds");
            return (Criteria) this;
        }

        public Criteria andAnnexIsNull() {
            addCriterion("annex is null");
            return (Criteria) this;
        }

        public Criteria andAnnexIsNotNull() {
            addCriterion("annex is not null");
            return (Criteria) this;
        }

        public Criteria andAnnexEqualTo(String value) {
            addCriterion("annex =", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexNotEqualTo(String value) {
            addCriterion("annex <>", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexGreaterThan(String value) {
            addCriterion("annex >", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexGreaterThanOrEqualTo(String value) {
            addCriterion("annex >=", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexLessThan(String value) {
            addCriterion("annex <", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexLessThanOrEqualTo(String value) {
            addCriterion("annex <=", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexLike(String value) {
            addCriterion("annex like", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexNotLike(String value) {
            addCriterion("annex not like", value, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexIn(List<String> values) {
            addCriterion("annex in", values, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexNotIn(List<String> values) {
            addCriterion("annex not in", values, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexBetween(String value1, String value2) {
            addCriterion("annex between", value1, value2, "annex");
            return (Criteria) this;
        }

        public Criteria andAnnexNotBetween(String value1, String value2) {
            addCriterion("annex not between", value1, value2, "annex");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andFilingDateIsNull() {
            addCriterion("filing_date is null");
            return (Criteria) this;
        }

        public Criteria andFilingDateIsNotNull() {
            addCriterion("filing_date is not null");
            return (Criteria) this;
        }

        public Criteria andFilingDateEqualTo(Date value) {
            addCriterion("filing_date =", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateNotEqualTo(Date value) {
            addCriterion("filing_date <>", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateGreaterThan(Date value) {
            addCriterion("filing_date >", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateGreaterThanOrEqualTo(Date value) {
            addCriterion("filing_date >=", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateLessThan(Date value) {
            addCriterion("filing_date <", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateLessThanOrEqualTo(Date value) {
            addCriterion("filing_date <=", value, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateIn(List<Date> values) {
            addCriterion("filing_date in", values, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateNotIn(List<Date> values) {
            addCriterion("filing_date not in", values, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateBetween(Date value1, Date value2) {
            addCriterion("filing_date between", value1, value2, "filingDate");
            return (Criteria) this;
        }

        public Criteria andFilingDateNotBetween(Date value1, Date value2) {
            addCriterion("filing_date not between", value1, value2, "filingDate");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagIsNull() {
            addCriterion("carryover_flag is null");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagIsNotNull() {
            addCriterion("carryover_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagEqualTo(Boolean value) {
            addCriterion("carryover_flag =", value, "carryoverFlag");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagNotEqualTo(Boolean value) {
            addCriterion("carryover_flag <>", value, "carryoverFlag");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagGreaterThan(Boolean value) {
            addCriterion("carryover_flag >", value, "carryoverFlag");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("carryover_flag >=", value, "carryoverFlag");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagLessThan(Boolean value) {
            addCriterion("carryover_flag <", value, "carryoverFlag");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("carryover_flag <=", value, "carryoverFlag");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagIn(List<Boolean> values) {
            addCriterion("carryover_flag in", values, "carryoverFlag");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagNotIn(List<Boolean> values) {
            addCriterion("carryover_flag not in", values, "carryoverFlag");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("carryover_flag between", value1, value2, "carryoverFlag");
            return (Criteria) this;
        }

        public Criteria andCarryoverFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("carryover_flag not between", value1, value2, "carryoverFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagIsNull() {
            addCriterion("is_synchronize_legal_system_flag is null");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagIsNotNull() {
            addCriterion("is_synchronize_legal_system_flag is not null");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagEqualTo(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag =", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagNotEqualTo(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag <>", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagGreaterThan(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag >", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag >=", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagLessThan(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag <", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("is_synchronize_legal_system_flag <=", value, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagIn(List<Boolean> values) {
            addCriterion("is_synchronize_legal_system_flag in", values, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagNotIn(List<Boolean> values) {
            addCriterion("is_synchronize_legal_system_flag not in", values, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("is_synchronize_legal_system_flag between", value1, value2, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andIsSynchronizeLegalSystemFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_synchronize_legal_system_flag not between", value1, value2, "isSynchronizeLegalSystemFlag");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagIsNull() {
            addCriterion("gle_sign_flag is null");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagIsNotNull() {
            addCriterion("gle_sign_flag is not null");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagEqualTo(Boolean value) {
            addCriterion("gle_sign_flag =", value, "gleSignFlag");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagNotEqualTo(Boolean value) {
            addCriterion("gle_sign_flag <>", value, "gleSignFlag");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagGreaterThan(Boolean value) {
            addCriterion("gle_sign_flag >", value, "gleSignFlag");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("gle_sign_flag >=", value, "gleSignFlag");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagLessThan(Boolean value) {
            addCriterion("gle_sign_flag <", value, "gleSignFlag");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("gle_sign_flag <=", value, "gleSignFlag");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagIn(List<Boolean> values) {
            addCriterion("gle_sign_flag in", values, "gleSignFlag");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagNotIn(List<Boolean> values) {
            addCriterion("gle_sign_flag not in", values, "gleSignFlag");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("gle_sign_flag between", value1, value2, "gleSignFlag");
            return (Criteria) this;
        }

        public Criteria andGleSignFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("gle_sign_flag not between", value1, value2, "gleSignFlag");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeIsNull() {
            addCriterion("notsync_type is null");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeIsNotNull() {
            addCriterion("notsync_type is not null");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeEqualTo(String value) {
            addCriterion("notsync_type =", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeNotEqualTo(String value) {
            addCriterion("notsync_type <>", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeGreaterThan(String value) {
            addCriterion("notsync_type >", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeGreaterThanOrEqualTo(String value) {
            addCriterion("notsync_type >=", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeLessThan(String value) {
            addCriterion("notsync_type <", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeLessThanOrEqualTo(String value) {
            addCriterion("notsync_type <=", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeLike(String value) {
            addCriterion("notsync_type like", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeNotLike(String value) {
            addCriterion("notsync_type not like", value, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeIn(List<String> values) {
            addCriterion("notsync_type in", values, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeNotIn(List<String> values) {
            addCriterion("notsync_type not in", values, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeBetween(String value1, String value2) {
            addCriterion("notsync_type between", value1, value2, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncTypeNotBetween(String value1, String value2) {
            addCriterion("notsync_type not between", value1, value2, "notsyncType");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonIsNull() {
            addCriterion("notsync_reason is null");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonIsNotNull() {
            addCriterion("notsync_reason is not null");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonEqualTo(String value) {
            addCriterion("notsync_reason =", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonNotEqualTo(String value) {
            addCriterion("notsync_reason <>", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonGreaterThan(String value) {
            addCriterion("notsync_reason >", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonGreaterThanOrEqualTo(String value) {
            addCriterion("notsync_reason >=", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonLessThan(String value) {
            addCriterion("notsync_reason <", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonLessThanOrEqualTo(String value) {
            addCriterion("notsync_reason <=", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonLike(String value) {
            addCriterion("notsync_reason like", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonNotLike(String value) {
            addCriterion("notsync_reason not like", value, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonIn(List<String> values) {
            addCriterion("notsync_reason in", values, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonNotIn(List<String> values) {
            addCriterion("notsync_reason not in", values, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonBetween(String value1, String value2) {
            addCriterion("notsync_reason between", value1, value2, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andNotsyncReasonNotBetween(String value1, String value2) {
            addCriterion("notsync_reason not between", value1, value2, "notsyncReason");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andTaxIdIsNull() {
            addCriterion("tax_id is null");
            return (Criteria) this;
        }

        public Criteria andTaxIdIsNotNull() {
            addCriterion("tax_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaxIdEqualTo(Long value) {
            addCriterion("tax_id =", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdNotEqualTo(Long value) {
            addCriterion("tax_id <>", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdGreaterThan(Long value) {
            addCriterion("tax_id >", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tax_id >=", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdLessThan(Long value) {
            addCriterion("tax_id <", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdLessThanOrEqualTo(Long value) {
            addCriterion("tax_id <=", value, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdIn(List<Long> values) {
            addCriterion("tax_id in", values, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdNotIn(List<Long> values) {
            addCriterion("tax_id not in", values, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdBetween(Long value1, Long value2) {
            addCriterion("tax_id between", value1, value2, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxIdNotBetween(Long value1, Long value2) {
            addCriterion("tax_id not between", value1, value2, "taxId");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNull() {
            addCriterion("tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNotNull() {
            addCriterion("tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualTo(String value) {
            addCriterion("tax_rate =", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualTo(String value) {
            addCriterion("tax_rate <>", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThan(String value) {
            addCriterion("tax_rate >", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualTo(String value) {
            addCriterion("tax_rate >=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThan(String value) {
            addCriterion("tax_rate <", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualTo(String value) {
            addCriterion("tax_rate <=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLike(String value) {
            addCriterion("tax_rate like", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotLike(String value) {
            addCriterion("tax_rate not like", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateIn(List<String> values) {
            addCriterion("tax_rate in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotIn(List<String> values) {
            addCriterion("tax_rate not in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateBetween(String value1, String value2) {
            addCriterion("tax_rate between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotBetween(String value1, String value2) {
            addCriterion("tax_rate not between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdIsNull() {
            addCriterion("legal_business_id is null");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdIsNotNull() {
            addCriterion("legal_business_id is not null");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdEqualTo(String value) {
            addCriterion("legal_business_id =", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdNotEqualTo(String value) {
            addCriterion("legal_business_id <>", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdGreaterThan(String value) {
            addCriterion("legal_business_id >", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdGreaterThanOrEqualTo(String value) {
            addCriterion("legal_business_id >=", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdLessThan(String value) {
            addCriterion("legal_business_id <", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdLessThanOrEqualTo(String value) {
            addCriterion("legal_business_id <=", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdLike(String value) {
            addCriterion("legal_business_id like", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdNotLike(String value) {
            addCriterion("legal_business_id not like", value, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdIn(List<String> values) {
            addCriterion("legal_business_id in", values, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdNotIn(List<String> values) {
            addCriterion("legal_business_id not in", values, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdBetween(String value1, String value2) {
            addCriterion("legal_business_id between", value1, value2, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalBusinessIdNotBetween(String value1, String value2) {
            addCriterion("legal_business_id not between", value1, value2, "legalBusinessId");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumIsNull() {
            addCriterion("legal_contract_num is null");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumIsNotNull() {
            addCriterion("legal_contract_num is not null");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumEqualTo(String value) {
            addCriterion("legal_contract_num =", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotEqualTo(String value) {
            addCriterion("legal_contract_num <>", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumGreaterThan(String value) {
            addCriterion("legal_contract_num >", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumGreaterThanOrEqualTo(String value) {
            addCriterion("legal_contract_num >=", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumLessThan(String value) {
            addCriterion("legal_contract_num <", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumLessThanOrEqualTo(String value) {
            addCriterion("legal_contract_num <=", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumLike(String value) {
            addCriterion("legal_contract_num like", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotLike(String value) {
            addCriterion("legal_contract_num not like", value, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumIn(List<String> values) {
            addCriterion("legal_contract_num in", values, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotIn(List<String> values) {
            addCriterion("legal_contract_num not in", values, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumBetween(String value1, String value2) {
            addCriterion("legal_contract_num between", value1, value2, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andLegalContractNumNotBetween(String value1, String value2) {
            addCriterion("legal_contract_num not between", value1, value2, "legalContractNum");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalIsNull() {
            addCriterion("execute_contract_percent_total is null");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalIsNotNull() {
            addCriterion("execute_contract_percent_total is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalEqualTo(BigDecimal value) {
            addCriterion("execute_contract_percent_total =", value, "executeContractPercentTotal");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalNotEqualTo(BigDecimal value) {
            addCriterion("execute_contract_percent_total <>", value, "executeContractPercentTotal");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalGreaterThan(BigDecimal value) {
            addCriterion("execute_contract_percent_total >", value, "executeContractPercentTotal");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("execute_contract_percent_total >=", value, "executeContractPercentTotal");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalLessThan(BigDecimal value) {
            addCriterion("execute_contract_percent_total <", value, "executeContractPercentTotal");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("execute_contract_percent_total <=", value, "executeContractPercentTotal");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalIn(List<BigDecimal> values) {
            addCriterion("execute_contract_percent_total in", values, "executeContractPercentTotal");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalNotIn(List<BigDecimal> values) {
            addCriterion("execute_contract_percent_total not in", values, "executeContractPercentTotal");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("execute_contract_percent_total between", value1, value2, "executeContractPercentTotal");
            return (Criteria) this;
        }

        public Criteria andExecuteContractPercentTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("execute_contract_percent_total not between", value1, value2, "executeContractPercentTotal");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIsNull() {
            addCriterion("delivery_type is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIsNotNull() {
            addCriterion("delivery_type is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeEqualTo(String value) {
            addCriterion("delivery_type =", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotEqualTo(String value) {
            addCriterion("delivery_type <>", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeGreaterThan(String value) {
            addCriterion("delivery_type >", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_type >=", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLessThan(String value) {
            addCriterion("delivery_type <", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLessThanOrEqualTo(String value) {
            addCriterion("delivery_type <=", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLike(String value) {
            addCriterion("delivery_type like", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotLike(String value) {
            addCriterion("delivery_type not like", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIn(List<String> values) {
            addCriterion("delivery_type in", values, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotIn(List<String> values) {
            addCriterion("delivery_type not in", values, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeBetween(String value1, String value2) {
            addCriterion("delivery_type between", value1, value2, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotBetween(String value1, String value2) {
            addCriterion("delivery_type not between", value1, value2, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseIsNull() {
            addCriterion("delivery_clause is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseIsNotNull() {
            addCriterion("delivery_clause is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseEqualTo(String value) {
            addCriterion("delivery_clause =", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseNotEqualTo(String value) {
            addCriterion("delivery_clause <>", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseGreaterThan(String value) {
            addCriterion("delivery_clause >", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_clause >=", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseLessThan(String value) {
            addCriterion("delivery_clause <", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseLessThanOrEqualTo(String value) {
            addCriterion("delivery_clause <=", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseLike(String value) {
            addCriterion("delivery_clause like", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseNotLike(String value) {
            addCriterion("delivery_clause not like", value, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseIn(List<String> values) {
            addCriterion("delivery_clause in", values, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseNotIn(List<String> values) {
            addCriterion("delivery_clause not in", values, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseBetween(String value1, String value2) {
            addCriterion("delivery_clause between", value1, value2, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryClauseNotBetween(String value1, String value2) {
            addCriterion("delivery_clause not between", value1, value2, "deliveryClause");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateIsNull() {
            addCriterion("delivery_date is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateIsNotNull() {
            addCriterion("delivery_date is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateEqualTo(Date value) {
            addCriterion("delivery_date =", value, "deliveryDate");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateNotEqualTo(Date value) {
            addCriterion("delivery_date <>", value, "deliveryDate");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateGreaterThan(Date value) {
            addCriterion("delivery_date >", value, "deliveryDate");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateGreaterThanOrEqualTo(Date value) {
            addCriterion("delivery_date >=", value, "deliveryDate");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateLessThan(Date value) {
            addCriterion("delivery_date <", value, "deliveryDate");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateLessThanOrEqualTo(Date value) {
            addCriterion("delivery_date <=", value, "deliveryDate");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateIn(List<Date> values) {
            addCriterion("delivery_date in", values, "deliveryDate");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateNotIn(List<Date> values) {
            addCriterion("delivery_date not in", values, "deliveryDate");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateBetween(Date value1, Date value2) {
            addCriterion("delivery_date between", value1, value2, "deliveryDate");
            return (Criteria) this;
        }

        public Criteria andDeliveryDateNotBetween(Date value1, Date value2) {
            addCriterion("delivery_date not between", value1, value2, "deliveryDate");
            return (Criteria) this;
        }

        public Criteria andPaymentTermIsNull() {
            addCriterion("payment_term is null");
            return (Criteria) this;
        }

        public Criteria andPaymentTermIsNotNull() {
            addCriterion("payment_term is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentTermEqualTo(Integer value) {
            addCriterion("payment_term =", value, "paymentTerm");
            return (Criteria) this;
        }

        public Criteria andPaymentTermNotEqualTo(Integer value) {
            addCriterion("payment_term <>", value, "paymentTerm");
            return (Criteria) this;
        }

        public Criteria andPaymentTermGreaterThan(Integer value) {
            addCriterion("payment_term >", value, "paymentTerm");
            return (Criteria) this;
        }

        public Criteria andPaymentTermGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_term >=", value, "paymentTerm");
            return (Criteria) this;
        }

        public Criteria andPaymentTermLessThan(Integer value) {
            addCriterion("payment_term <", value, "paymentTerm");
            return (Criteria) this;
        }

        public Criteria andPaymentTermLessThanOrEqualTo(Integer value) {
            addCriterion("payment_term <=", value, "paymentTerm");
            return (Criteria) this;
        }

        public Criteria andPaymentTermIn(List<Integer> values) {
            addCriterion("payment_term in", values, "paymentTerm");
            return (Criteria) this;
        }

        public Criteria andPaymentTermNotIn(List<Integer> values) {
            addCriterion("payment_term not in", values, "paymentTerm");
            return (Criteria) this;
        }

        public Criteria andPaymentTermBetween(Integer value1, Integer value2) {
            addCriterion("payment_term between", value1, value2, "paymentTerm");
            return (Criteria) this;
        }

        public Criteria andPaymentTermNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_term not between", value1, value2, "paymentTerm");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgIsNull() {
            addCriterion("contract_terms_flg is null");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgIsNotNull() {
            addCriterion("contract_terms_flg is not null");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgEqualTo(String value) {
            addCriterion("contract_terms_flg =", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgNotEqualTo(String value) {
            addCriterion("contract_terms_flg <>", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgGreaterThan(String value) {
            addCriterion("contract_terms_flg >", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgGreaterThanOrEqualTo(String value) {
            addCriterion("contract_terms_flg >=", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgLessThan(String value) {
            addCriterion("contract_terms_flg <", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgLessThanOrEqualTo(String value) {
            addCriterion("contract_terms_flg <=", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgLike(String value) {
            addCriterion("contract_terms_flg like", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgNotLike(String value) {
            addCriterion("contract_terms_flg not like", value, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgIn(List<String> values) {
            addCriterion("contract_terms_flg in", values, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgNotIn(List<String> values) {
            addCriterion("contract_terms_flg not in", values, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgBetween(String value1, String value2) {
            addCriterion("contract_terms_flg between", value1, value2, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsFlgNotBetween(String value1, String value2) {
            addCriterion("contract_terms_flg not between", value1, value2, "contractTermsFlg");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsIsNull() {
            addCriterion("contract_terms_ids is null");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsIsNotNull() {
            addCriterion("contract_terms_ids is not null");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsEqualTo(String value) {
            addCriterion("contract_terms_ids =", value, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsNotEqualTo(String value) {
            addCriterion("contract_terms_ids <>", value, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsGreaterThan(String value) {
            addCriterion("contract_terms_ids >", value, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsGreaterThanOrEqualTo(String value) {
            addCriterion("contract_terms_ids >=", value, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsLessThan(String value) {
            addCriterion("contract_terms_ids <", value, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsLessThanOrEqualTo(String value) {
            addCriterion("contract_terms_ids <=", value, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsLike(String value) {
            addCriterion("contract_terms_ids like", value, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsNotLike(String value) {
            addCriterion("contract_terms_ids not like", value, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsIn(List<String> values) {
            addCriterion("contract_terms_ids in", values, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsNotIn(List<String> values) {
            addCriterion("contract_terms_ids not in", values, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsBetween(String value1, String value2) {
            addCriterion("contract_terms_ids between", value1, value2, "contractTermsIds");
            return (Criteria) this;
        }

        public Criteria andContractTermsIdsNotBetween(String value1, String value2) {
            addCriterion("contract_terms_ids not between", value1, value2, "contractTermsIds");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}