package com.midea.pam.common.basedata.dto;

import com.midea.pam.common.basedata.entity.MaterialCost;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "MaterialCostDto", description = "物料成本查询信息")
public class MaterialCostDto extends MaterialCost {

    @ApiModelProperty("库存组织")
    private String organizationName;

    @ApiModelProperty("库存组织Id集合")
    private List<Long> organizationIdList;

    @ApiModelProperty("单位名称")
    private String unitName;

    @ApiModelProperty("物料类型名称")
    private String materielTypeName;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "模糊:pam编码")
    private String fuzzyPamCode;

    @ApiModelProperty(value = "模糊:erp编码")
    private String fuzzyErpCode;

    @ApiModelProperty(value = "模糊:物料描述")
    private String fuzzyDescr;

    @ApiModelProperty(value = "物料成本ids")
    private List<Long> ids;

    @ApiModelProperty(value = "配套人员名字")
    private String nuclearPriceUserName;

    @ApiModelProperty(value = "配套人员mip")
    private String nuclearPriceUserMip;

    @ApiModelProperty(value = "库存组织id(多选)")
    private String organizationIds;

    private Date startDate;
    private Date endDate;

    @ApiModelProperty(value = "核价开始时间")
    private Date nuclearPriceStartDate;

    private String nuclearPriceStartDateStr;

    @ApiModelProperty(value = "核价结束时间")
    private Date nuclearPriceEndDate;
    private String nuclearPriceEndDateStr;

    @ApiModelProperty(value = "批准供应商数量")
    private int approvedSupplierNum;

    @ApiModelProperty(value = "创建人名称")
    private String creator;

    @ApiModelProperty(value = "ERP同步状态: 0-待同步, 1-同步成功, 2-同步异常, 3-同步中")
    private String erpCode;

    @ApiModelProperty(value = "ERP消息")
    private String erpMessage;

    @ApiModelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "最后定价区间")
    private String pricingDate;

    @ApiModelProperty(value = "最后定价区间开始")
    private Date pricingDateStart;

    @ApiModelProperty(value = "最后定价区间结束")
    private Date pricingDateEnd;

    @ApiModelProperty(value = "最后更新日期开始")
    private Date lastUpdateDateStart;

    @ApiModelProperty(value = "最后更新日期结束")
    private Date lastUpdateDateEnd;

    @ApiModelProperty(value = "是否来源估价")
    private Boolean itemCostOrNot;

    @ApiModelProperty(value = "物料创建日期")
    private Date materialCreateAt;

    @ApiModelProperty(value = "物料创建日期开始")
    private Date materialCreateAtStart;

    @ApiModelProperty(value = "物料创建日期结束")
    private Date materialCreateAtEnd;

    private List<String> materialClassifications;

    private List<String> materielTypes;

    private List<String> machiningPartTypes;

    private List<String> erpCodes;

    private List<Long> oIgs;// 库存组织

    private List<Long> ouIds;// 业务实体

    private String resouce;

    @ApiModelProperty(value = "erp编码创建日期开始")
    private Date erpCodeCreateAtStart;

    @ApiModelProperty(value = "erp编码创建日期结束")
    private Date erpCodeCreateAtEnd;

    @ApiModelProperty(value = "物料创建人名字")
    private String createByName;

    private List<String> pamCodeList;

    private Long ouId;// 业务实体

}
