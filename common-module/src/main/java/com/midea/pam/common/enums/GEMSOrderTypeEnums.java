package com.midea.pam.common.enums;

import java.util.Objects;

public enum GEMSOrderTypeEnums {

    CREATE_BUDGET("CREATE_BUDGET", "创建预算单元"),    //PAM-EMS-006
    BESIDE_BUDGET("BESIDE_BUDGET", "创建预算调整单"),   //PAM-EMS-007
    ;


    private String code;
    private String name;

    GEMSOrderTypeEnums(final String code, final String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getValue(final String code) {
        for (GEMSOrderTypeEnums type : GEMSOrderTypeEnums.values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type.getName();
            }
        }
        return null;
    }
}
