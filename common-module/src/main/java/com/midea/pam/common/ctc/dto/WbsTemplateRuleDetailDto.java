package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.WbsTemplateRuleDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Getter
@Setter
public class WbsTemplateRuleDetailDto extends WbsTemplateRuleDetail implements Comparable<WbsTemplateRuleDetailDto> {

    @ApiModelProperty("最后更新人")
    private String updateByCn;

    @ApiModelProperty("是否父级（多选）")
    private List<Boolean> parentStateList;

    @ApiModelProperty("保存标记")
    private Boolean saveFlag;

    @ApiModelProperty("序号（多选）")
    private List<String> orderNoList;

    @ApiModelProperty("下级")
    private List<WbsTemplateRuleDetailDto> childs;

    @ApiModelProperty("wbs模板id")
    private Long wbsTemplateInfoId;

    private String dynamicCombination;

    @Override
    public int compareTo(WbsTemplateRuleDetailDto compareObj) {
        if (StringUtils.isBlank(this.getOrderNo()) || StringUtils.isBlank(compareObj.getOrderNo())) {
            return 0;
        }
        int thisOrderNoNumber = Integer.parseInt(this.getOrderNo().contains(".") ?
                StringUtils.substringAfterLast(this.getOrderNo(),
                        ".") : this.getOrderNo());
        int compareOrderNoNumber = Integer.parseInt(compareObj.getOrderNo().contains(".") ?
                StringUtils.substringAfterLast(compareObj.getOrderNo(),
                        ".") : compareObj.getOrderNo());
        // 升序
        return thisOrderNoNumber - compareOrderNoNumber;
    }
}
