package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.ctc.entity.PurchaseOrder;
import com.midea.pam.common.ctc.entity.PurchaseOrderChangeRecord;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms;
import com.midea.pam.common.ctc.entity.VendorAslCtc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "PurchaseOrderDto", description = "采购订单")
public class PurchaseOrderDto extends PurchaseOrder {

    @ApiModelProperty("采购订单题头")
    private PurchaseOrderTitleDto purchaseOrderTitle;

    @ApiModelProperty("文件列表")
    private List<CtcAttachmentDto> ctcAttachmentList;

    @ApiModelProperty("物料需求")
    private PurchaseMaterialRequirementDto materialRequirementDto;

    @ApiModelProperty("项目")
    private ProjectDto projectDto;

    @ApiModelProperty("库存组织id")
    private Long organizationId;

    @ApiModelProperty("项目业务实体")
    private OperatingUnitDto operatingUnitDto;

    @ApiModelProperty("项目业务实体名字")
    private String projectOuName;

    @ApiModelProperty("项目业务实体id")
    private Long projectOuId;

    @ApiModelProperty("项目名字")
    private String projectName;

    @ApiModelProperty("项目类型")
    private String projectType;

    @ApiModelProperty("项目编号")
    private String projectNum;

    @ApiModelProperty("物料")
    private MaterialDto materialDto;

    @ApiModelProperty("批准供应商")
    private VendorAslCtc vendorAsl;

    @ApiModelProperty("供应商主数据")
    private VendorSiteBankDto vendorSiteBank;

    @ApiModelProperty("订单详情")
    private List<PurchaseOrderDetailDto> purchaseOrderDetailDtos;

    @ApiModelProperty("erp采购员名字")
    private String erpBuyerName;

    @ApiModelProperty("状态")
    private String statusName;

    @ApiModelProperty("同步ERP状态")
    private String syncStatusName;

    @ApiModelProperty("子库存代码")
    private String secondaryInventoryName;

    @ApiModelProperty("链接来源")
    private String resouce;

    @ApiModelProperty(value = "批准供应商id")
    private Long vendorId;
    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;
    @ApiModelProperty(value = "批准供应商地点id")
    private Long erpVendorSiteId;

    @ApiModelProperty(value = "跟踪日期")
    private Date trackDate;

    private String esbDataSource;//接口类型

    @ApiModelProperty("订单行状态")
    private String statusStr;

    @ApiModelProperty("是否合并")
    private String isMerge;


    /*筛选条件*/

    private Integer pageNum;

    private Integer pageSize;

    private Integer number;

    @ApiModelProperty("模糊采购订单号")
    private String fuzzyOrderNum;

    @ApiModelProperty("模糊供应商名称")
    private String fuzzyVendorName;

    @ApiModelProperty("订单状态(多选)")
    private String manyStatus;

    @ApiModelProperty("项目业务实体名字(多选)")
    private String manyProjectOuName;

    @ApiModelProperty("同步ERP状态(多选)")
    private String manySyncStatus;

    @ApiModelProperty("erp订单状态(1-未发布 2-已发布 3-已回签)")
    private String erpOrderStatusStr;

    @ApiModelProperty("模糊供应商编码")
    private String fuzzyVendorNum;

    @ApiModelProperty("模糊供应商地点")
    private String fuzzyVendorSiteCode;

    @ApiModelProperty("模糊项目名称")
    private String fuzzyProjectName;

    @ApiModelProperty("物料描述")
    private String materielDescr;

    @ApiModelProperty("ERP物料编码")
    private String erpCode;

    @ApiModelProperty("PAM物料编码")
    private String pamCode;

    @ApiModelProperty("模糊项目编号")
    private String fuzzyProjectNum;

    @ApiModelProperty("模糊业务实体(暂停使用)")
    private String fuzzyProjectOuName;

    @ApiModelProperty("创建日期开始")
    private Date orderCreateAtBegin;

    @ApiModelProperty("创建日期结束")
    private Date orderCreateAtEnd;

    @ApiModelProperty("计划交货开始日期")
    private Date deliveryTimeBegin;

    @ApiModelProperty("计划交货结束日期")
    private Date deliveryTimeEnd;

    @ApiModelProperty("修改计划交货日期")
    private Date changeDeliveryTime;

    private List<Long> ids;

    private List<Long> ouIdList;

    @ApiModelProperty(value = "wbs")
    private String WbsSummaryCode;
    @ApiModelProperty(value = "图纸版本号")
    private String ChartVersion;
    @ApiModelProperty(value = "品牌")
    private String brand;
    @ApiModelProperty(value = "图号/型号")
    private String model;
    @ApiModelProperty(value = "发布开始日期")
    private Date publishStartTime;
    @ApiModelProperty(value = "发布结束日期")
    private Date publishEndTime;
    @ApiModelProperty(value = "设计发布批次号")
    private String designReleaseLotNumber;
    @ApiModelProperty(value = "详细设计单据编号")
    private String requirementCode;

    @ApiModelProperty(value = "采购订单变更记录")
    private List<PurchaseOrderChangeRecord> purchaseOrderChangeRecordList;


    @ApiModelProperty("是否满足一揽子协议的有效期内，默认是true")
    private Boolean purchaseBpaPrice = true;

    @ApiModelProperty("是否查询我的采购订单:true 是,false 否")
    private Boolean me;

    @ApiModelProperty(value = "审批通过日期开始")
    private Date approvalStartTime;

    @ApiModelProperty(value = "审批通过日期结束")
    private Date approvalEndTime;

    @ApiModelProperty(value = "ERP下达数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "采购订单变更记录")
    private List<PurchaseOrderChangeRecordDto> purchaseOrderChangeRecordDtos;

    @ApiModelProperty("订单行创建时间开始")
    private Date orderDetailCreateAtBegin;

    @ApiModelProperty("订单行创建时间结束")
    private Date orderDetailCreateAtEnd;

    @ApiModelProperty("订单行创建日期")
    private Date orderDetailCreateAt;

    @ApiModelProperty("订单行创建人名称")
    private String orderDetailCreateByName;

    @ApiModelProperty("类型：0=已创建；1=待下达；2=待变更；3=创建中")
    private String manyType;

    @ApiModelProperty("采购订单类型，ZC02:正常订单")
    private String orderType;

    @ApiModelProperty("供方承诺交期开始时间 ")
    private Date promisedDateStart;

    @ApiModelProperty("供方承诺交期结束时间")
    private Date promisedDateEnd;

    @ApiModelProperty("跟踪日期开始时间")
    private Date trackDateStart;

    @ApiModelProperty("跟踪日期结束时间")
    private Date trackDateEnd;

    private List<PurchaseOrderStandardTermsDto> purchaseOrderStandardTermsDtoList;


    public Integer getPageNum() {
        if (pageNum == null) {
            return 1;
        }
        return pageNum;
    }

    public Integer getPageSize() {
        if (pageSize == null) {
            return 20;
        }
        return pageSize;
    }
}