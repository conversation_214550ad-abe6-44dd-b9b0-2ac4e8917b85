package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "wbs变更单明细行")
public class ProjectWbsChangeReceiptDetail extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "WBS变更单id")
    private Long projectWbsReceiptsId;

    @ApiModelProperty(value = "WBS预算id")
    private Long projectWbsBudgetId;

    @ApiModelProperty(value = "标识：New/Set0")
    private String tag;

    @ApiModelProperty(value = "变更后预算（设计估计金额）")
    private BigDecimal afterChangePrice;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "活动事项序号")
    private String activityOrderNo;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "活动类别名称")
    private String activityName;

    @ApiModelProperty(value = "活动类别属性")
    private String activityType;

    @ApiModelProperty(value = "wbs短码")
    private String wbsFullCode;

    @ApiModelProperty(value = "wbs全码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "最底层wbs编码")
    private String wbsLastCode;

    @ApiModelProperty(value = "动态列wbs模板规则id数组")
    private String dynamicWbsTemplateRuleIds;

    @ApiModelProperty(value = "动态列字段名数组")
    private String dynamicFields;

    @ApiModelProperty(value = "动态列编码数组")
    private String dynamicValues;

    @ApiModelProperty(value = "预算金额")
    private BigDecimal price;

    @ApiModelProperty(value = "预算基线")
    private BigDecimal baselineCost;

    @ApiModelProperty(value = "需求预算")
    private BigDecimal demandCost;

    @ApiModelProperty(value = "在途成本")
    private BigDecimal onTheWayCost;

    @ApiModelProperty(value = "已发生成本")
    private BigDecimal incurredCost;

    @ApiModelProperty(value = "剩余可用预算")
    private BigDecimal remainingCost;

    @ApiModelProperty(value = "累计变更金额")
    private BigDecimal changeAccumulateCost;

    @ApiModelProperty(value = "父级id（wbs）")
    private Long parentWbsId;

    @ApiModelProperty(value = "父级id（activity）")
    private Long parentActivityId;

    @ApiModelProperty(value = "经济事项id")
    private Long feeTypeId;

    @ApiModelProperty(value = "经济事项名称")
    private String feeTypeName;

    @ApiModelProperty(value = "是否同步ems（0不同步/1同步）")
    private Boolean feeSyncEms;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "版本号")
    private Long version;

    private static final long serialVersionUID = 1L;

    public Long getProjectWbsReceiptsId() {
        return projectWbsReceiptsId;
    }

    public void setProjectWbsReceiptsId(Long projectWbsReceiptsId) {
        this.projectWbsReceiptsId = projectWbsReceiptsId;
    }

    public Long getProjectWbsBudgetId() {
        return projectWbsBudgetId;
    }

    public void setProjectWbsBudgetId(Long projectWbsBudgetId) {
        this.projectWbsBudgetId = projectWbsBudgetId;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag == null ? null : tag.trim();
    }

    public BigDecimal getAfterChangePrice() {
        return afterChangePrice;
    }

    public void setAfterChangePrice(BigDecimal afterChangePrice) {
        this.afterChangePrice = afterChangePrice;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getActivityOrderNo() {
        return activityOrderNo;
    }

    public void setActivityOrderNo(String activityOrderNo) {
        this.activityOrderNo = activityOrderNo == null ? null : activityOrderNo.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName == null ? null : activityName.trim();
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType == null ? null : activityType.trim();
    }

    public String getWbsFullCode() {
        return wbsFullCode;
    }

    public void setWbsFullCode(String wbsFullCode) {
        this.wbsFullCode = wbsFullCode == null ? null : wbsFullCode.trim();
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getWbsLastCode() {
        return wbsLastCode;
    }

    public void setWbsLastCode(String wbsLastCode) {
        this.wbsLastCode = wbsLastCode == null ? null : wbsLastCode.trim();
    }

    public String getDynamicWbsTemplateRuleIds() {
        return dynamicWbsTemplateRuleIds;
    }

    public void setDynamicWbsTemplateRuleIds(String dynamicWbsTemplateRuleIds) {
        this.dynamicWbsTemplateRuleIds = dynamicWbsTemplateRuleIds == null ? null : dynamicWbsTemplateRuleIds.trim();
    }

    public String getDynamicFields() {
        return dynamicFields;
    }

    public void setDynamicFields(String dynamicFields) {
        this.dynamicFields = dynamicFields == null ? null : dynamicFields.trim();
    }

    public String getDynamicValues() {
        return dynamicValues;
    }

    public void setDynamicValues(String dynamicValues) {
        this.dynamicValues = dynamicValues == null ? null : dynamicValues.trim();
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getBaselineCost() {
        return baselineCost;
    }

    public void setBaselineCost(BigDecimal baselineCost) {
        this.baselineCost = baselineCost;
    }

    public BigDecimal getDemandCost() {
        return demandCost;
    }

    public void setDemandCost(BigDecimal demandCost) {
        this.demandCost = demandCost;
    }

    public BigDecimal getOnTheWayCost() {
        return onTheWayCost;
    }

    public void setOnTheWayCost(BigDecimal onTheWayCost) {
        this.onTheWayCost = onTheWayCost;
    }

    public BigDecimal getIncurredCost() {
        return incurredCost;
    }

    public void setIncurredCost(BigDecimal incurredCost) {
        this.incurredCost = incurredCost;
    }

    public BigDecimal getRemainingCost() {
        return remainingCost;
    }

    public void setRemainingCost(BigDecimal remainingCost) {
        this.remainingCost = remainingCost;
    }

    public BigDecimal getChangeAccumulateCost() {
        return changeAccumulateCost;
    }

    public void setChangeAccumulateCost(BigDecimal changeAccumulateCost) {
        this.changeAccumulateCost = changeAccumulateCost;
    }

    public Long getParentWbsId() {
        return parentWbsId;
    }

    public void setParentWbsId(Long parentWbsId) {
        this.parentWbsId = parentWbsId;
    }

    public Long getParentActivityId() {
        return parentActivityId;
    }

    public void setParentActivityId(Long parentActivityId) {
        this.parentActivityId = parentActivityId;
    }

    public Long getFeeTypeId() {
        return feeTypeId;
    }

    public void setFeeTypeId(Long feeTypeId) {
        this.feeTypeId = feeTypeId;
    }

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName == null ? null : feeTypeName.trim();
    }

    public Boolean getFeeSyncEms() {
        return feeSyncEms;
    }

    public void setFeeSyncEms(Boolean feeSyncEms) {
        this.feeSyncEms = feeSyncEms;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectWbsReceiptsId=").append(projectWbsReceiptsId);
        sb.append(", projectWbsBudgetId=").append(projectWbsBudgetId);
        sb.append(", tag=").append(tag);
        sb.append(", afterChangePrice=").append(afterChangePrice);
        sb.append(", remark=").append(remark);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", description=").append(description);
        sb.append(", activityOrderNo=").append(activityOrderNo);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", activityName=").append(activityName);
        sb.append(", activityType=").append(activityType);
        sb.append(", wbsFullCode=").append(wbsFullCode);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", wbsLastCode=").append(wbsLastCode);
        sb.append(", dynamicWbsTemplateRuleIds=").append(dynamicWbsTemplateRuleIds);
        sb.append(", dynamicFields=").append(dynamicFields);
        sb.append(", dynamicValues=").append(dynamicValues);
        sb.append(", price=").append(price);
        sb.append(", baselineCost=").append(baselineCost);
        sb.append(", demandCost=").append(demandCost);
        sb.append(", onTheWayCost=").append(onTheWayCost);
        sb.append(", incurredCost=").append(incurredCost);
        sb.append(", remainingCost=").append(remainingCost);
        sb.append(", changeAccumulateCost=").append(changeAccumulateCost);
        sb.append(", parentWbsId=").append(parentWbsId);
        sb.append(", parentActivityId=").append(parentActivityId);
        sb.append(", feeTypeId=").append(feeTypeId);
        sb.append(", feeTypeName=").append(feeTypeName);
        sb.append(", feeSyncEms=").append(feeSyncEms);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", version=").append(version);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}