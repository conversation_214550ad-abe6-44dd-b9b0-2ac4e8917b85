package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.StandardTerms;
import com.midea.pam.common.ctc.entity.StandardTermsDeviation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class StandardTermsDto extends StandardTerms {

    /**
     * 是否弃用标识
     */
    private Boolean deprecatedFlag;

    /**
     * 标准条款内容集合
     */
    private List<StandardTermsContentDto> standardTermsContentList;

    /**
     * 附件信息结合
     */
    private List<CtcAttachmentDto> ctcAttachmentList;

    /**
     * 开始更新时间
     */
    private String updateAtStart;

    /**
     * 结束更新时间
     */
    private String updateAtEnd;

    /**
     * 条款状态集合
     */
    private String termsStatusListStr;

    private List<Integer> statusList;

    /**
     * 偏离项集合
     */
    private List<StandardTermsDeviation> standardTermsDeviationList;

    private String scopeApplicationLitStr;

    private List<String> scopeApplicationStrList;

    @ApiModelProperty(value = "采购订单Id")
    private Long purchaseOrderId;

    @ApiModelProperty(value = "关联标准条款ID")
    private Long associationTermsId;


    private Integer pageNum;

    private  Integer pageSize;



}
