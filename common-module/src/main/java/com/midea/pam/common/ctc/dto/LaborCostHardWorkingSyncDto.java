package com.midea.pam.common.ctc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 人工成本明细hard_working字段同步DTO
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Getter
@Setter
@ApiModel(value = "人工成本明细hard_working字段同步DTO")
public class LaborCostHardWorkingSyncDto implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "统计日期")
    private String statisticDate;

    @ApiModelProperty(value = "发薪OU")
    private Long fullPayOuId;

    @ApiModelProperty(value = "项目ou")
    private Long ouId;

    @ApiModelProperty(value = "工时入账结转科目")
    private String hardWorking;

    @ApiModelProperty(value = "工时ID")
    private Long workingHourId;

    @ApiModelProperty(value = "人工成本明细ID")
    private Long laborCostDetailId;
}
