package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "采购订单标准条款偏离信息表")
public class StandardTermsDeviation extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "关联采购订单标准条款ID")
    private Long associationPurchaseTermsId;

    @ApiModelProperty(value = "偏离项信息")
    private String deviationInfo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getAssociationPurchaseTermsId() {
        return associationPurchaseTermsId;
    }

    public void setAssociationPurchaseTermsId(Long associationPurchaseTermsId) {
        this.associationPurchaseTermsId = associationPurchaseTermsId;
    }

    public String getDeviationInfo() {
        return deviationInfo;
    }

    public void setDeviationInfo(String deviationInfo) {
        this.deviationInfo = deviationInfo == null ? null : deviationInfo.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", associationPurchaseTermsId=").append(associationPurchaseTermsId);
        sb.append(", deviationInfo=").append(deviationInfo);
        sb.append(", remark=").append(remark);
        sb.append(", sort=").append(sort);
        sb.append(", version=").append(version);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}