package com.midea.pam.common.sdp.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 主题【CUSTOMER_CRM】sdp_crm_customer_head
 */
@Getter
@Setter
public class CustomerCrmHeader {

    private BigDecimal custId;
    private String custRowId;
    private String customerUnitCode;
    private String customerType;
    private String registrationAddress;
    private String capital;
    private String country;
    private String state;
    private String city;
    private String county;
    private String town;
    private String corpRepresentative;
    private String creditLevel;
    private String customerName;
    private String customerEnName;
    private String enterpriseNature;
    private String fax;
    private String mainPhone;
    private String nacao;
    private String parCustomerId;
    private String registrationNumber;
    private String sinoBuyerName;
    private String sinoNumber;
    private String statusCode; //状态：valid-有效 invalid-废弃 frozen-冻结
    private String supplierNumber;
    private String supplierFlag;
    private String innerCustFlag;
    private String taxpayerType;
    private String taxRegistration;
    private String taxRegNum;
    private String url;
    private String zipCode;
    private String uscc;
    private String integrationId;
    private String threeCerInOneFlg;
    private String custDomestFactory;
    private String centralPurFlag;
    private String centralPurCustNumber;
    private String busiInfoVeriStatus;
    private Date busiInfoVeriDate;
    private Date busiInfoRegDate;
    private Date mideaAccountDate;
    private String created;
    private Date createDate;
    private String updated;
    private Date updateDate;
    private String enterpriseStatus;
    private String afterSaleType;
    private String registrationCode;
    private String blackList;
    private String stateOwnedEnterprise;
    private String customerGroup;
    private String decisionType;
    private String decisionName;
    private String decisionUpdateDate;
    private String decisionUpdatedBy;
    private String customerFreightGrade;
    private String duplicateCheckFlag;
    private String internationalCustomerCode;
    private String unitPartnerCode;
    private String targetAccountNumber;
    private String companyEmail;
    private BigDecimal shopNumber;
    private String groupAssessmentLevel;
    private String parCustomerCode;
    private Date effectiveFrom;
    private String commonName;
    private String comments;
    private String sysType;
    private List<CustomerCrmCustomerTenant> customertenant;
}
