package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "标准条款表")
public class StandardTerms extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "标准条款状态(1:生效，2:草稿，3:审批中,4:驳回,5:弃用)")
    private Integer termsStatus;

    @ApiModelProperty(value = "标准条款编码")
    private String termsCode;

    @ApiModelProperty(value = "标准条款名称")
    private String termsName;

    @ApiModelProperty(value = "附件ID集合")
    private String attachIdListStr;

    @ApiModelProperty(value = "适用范围（0：采购订单，1：采购合同，2：采购订单+采购合同）")
    private String scopeApplication;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人名称")
    private String createByName;

    @ApiModelProperty(value = "最后修改人名称")
    private String updateByName;

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "虚拟单位(业务分类)")
    private Long unitId;

    @ApiModelProperty(value = "标准条款展示内容")
    private String termsDisplayContent;

    private static final long serialVersionUID = 1L;

    public Integer getTermsStatus() {
        return termsStatus;
    }

    public void setTermsStatus(Integer termsStatus) {
        this.termsStatus = termsStatus;
    }

    public String getTermsCode() {
        return termsCode;
    }

    public void setTermsCode(String termsCode) {
        this.termsCode = termsCode == null ? null : termsCode.trim();
    }

    public String getTermsName() {
        return termsName;
    }

    public void setTermsName(String termsName) {
        this.termsName = termsName == null ? null : termsName.trim();
    }

    public String getAttachIdListStr() {
        return attachIdListStr;
    }

    public void setAttachIdListStr(String attachIdListStr) {
        this.attachIdListStr = attachIdListStr == null ? null : attachIdListStr.trim();
    }

    public String getScopeApplication() {
        return scopeApplication;
    }

    public void setScopeApplication(String scopeApplication) {
        this.scopeApplication = scopeApplication == null ? null : scopeApplication.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getCreateByName() {
        return createByName;
    }

    public void setCreateByName(String createByName) {
        this.createByName = createByName == null ? null : createByName.trim();
    }

    public String getUpdateByName() {
        return updateByName;
    }

    public void setUpdateByName(String updateByName) {
        this.updateByName = updateByName == null ? null : updateByName.trim();
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getTermsDisplayContent() {
        return termsDisplayContent;
    }

    public void setTermsDisplayContent(String termsDisplayContent) {
        this.termsDisplayContent = termsDisplayContent == null ? null : termsDisplayContent.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", termsStatus=").append(termsStatus);
        sb.append(", termsCode=").append(termsCode);
        sb.append(", termsName=").append(termsName);
        sb.append(", attachIdListStr=").append(attachIdListStr);
        sb.append(", scopeApplication=").append(scopeApplication);
        sb.append(", remark=").append(remark);
        sb.append(", createByName=").append(createByName);
        sb.append(", updateByName=").append(updateByName);
        sb.append(", version=").append(version);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", unitId=").append(unitId);
        sb.append(", termsDisplayContent=").append(termsDisplayContent);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}