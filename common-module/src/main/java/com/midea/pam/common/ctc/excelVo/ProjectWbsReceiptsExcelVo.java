package com.midea.pam.common.ctc.excelVo;

import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

@Getter
@Setter
public class ProjectWbsReceiptsExcelVo {

    @Excel(name = "序号", width = 10)
    private int num;

    @Excel(name = "详细设计单据编号", width = 20)
    private String requirementCode;

    @Excel(name = "详细设计单据状态", width = 20, replace = {"草稿_0", "待处理_1", "审核中_2", "驳回_3", "生效_4", "作废_5"})
    private Integer requirementStatus;

    @Excel(name = "详细设计单据类型", width = 20, replace = {"详细设计发布_1", "详细设计变更_2", "需求发布_3", "WBS变更_5"})
    private Integer requirementType;

    @Excel(name = "详细设计单据确认方式", width = 20, replace = {"部分确认_1", "进度确认_2","--_0"})
    private Integer confirmMode;

    @Excel(name = "制单人", width = 15)
    private String producerName;

    @Excel(name = "处理人", width = 15)
    private String handleName;

    @Excel(name = "项目编号", width = 15)
    private String projectCode;

    @Excel(name = "项目名称", width = 30)
    private String projectName;

    @Excel(name = "备注", width = 50)
    private String remark;

    @Excel(name = "创建日期", width = 15, format = "yyyy-MM-dd")
    private Date createAt;

}
