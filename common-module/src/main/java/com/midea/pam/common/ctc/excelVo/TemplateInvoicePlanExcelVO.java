package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.ctc.entity.InvoicePlan;
import com.midea.pam.common.ctc.entity.InvoicePlanDetail;
import com.midea.pam.common.util.excel4j.ExcelDate2DateConverter;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/9
 */
@Data
public class TemplateInvoicePlanExcelVO {

    @Excel(name = "序号")
    @ExcelField(title = "序号", order = 1)
    private Integer index;

    @Excel(name = "子合同编号")
    @ExcelField(title = "子合同编号", order = 2)
    @NotNull(message = "子合同编号不能为空")
    private String contractCode;

    /**
     * 合同ID
     */
    private Long contractId;

    @Excel(name = "子合同名称")
    @ExcelField(title = "子合同名称", order = 3)
    @NotNull(message = "子合同名称不能为空")
    private String name;

    @Excel(name = "服务类型")
    @ExcelField(title = "服务类型", order = 4)
    @NotNull(message = "服务类型不能为空")
    private String productTypeName;

    @Excel(name = "发票类型")
    @ExcelField(title = "发票类型", order = 5)
    @NotNull(message = "发票类型不能为空")
    private String typeName;

    @Excel(name = "开票计划日期", format = "yyyy/MM/dd")
    @ExcelField(title = "开票计划日期", order = 6, readConverter = ExcelDate2DateConverter.class)
    @NotNull(message = "开票计划日期不能为空")
    private Date date;

    @Excel(name = "开票计划金额（含税）")
    @ExcelField(title = "开票计划金额（含税）", order = 7)
    @NotNull(message = "开票计划金额（含税）不能为空")
    private BigDecimal amount;

    @Excel(name = "开票计划金额（不含税）")
    @ExcelField(title = "开票计划金额（不含税）", order = 7)
    @NotNull(message = "开票计划金额（不含税）不能为空")
    private BigDecimal excludingTaxAmount;

    @Excel(name = "税率")
    @ExcelField(title = "税率", order = 8)
    @NotNull(message = "税率不能为空")
    private BigDecimal rate;

    private Long taxId;

    @Excel(name = "开票条件")
    @ExcelField(title = "开票条件", order = 9)
    private String requirement;

    @Excel(name = "里程碑名称")
    @ExcelField(title = "里程碑名称", order = 10)
    private String milestoneName;

    @Excel(name = "实际开票金额")
    @ExcelField(title = "实际开票金额", order = 11)
    private BigDecimal actualAmount;

    @Excel(name = "开票计划编号")
    @ExcelField(title = "开票计划编号", order = 12)
    @NotNull(message = "开票计划编号不能为空")
    private String detailCode;

    @Excel(name = "里程碑ID")
    @ExcelField(title = "里程碑ID", order = 13)
    private Long milestoneId;

    @Excel(name = "校验结果")
    private String validResult;

    /**
     * 开票计划ID
     */
    private Long planId;

    /**
     * 开票计划明细ID
     */
    private Long planDetailId;

    private Long contractProductId;

    public InvoicePlan generateInvoicePlan() {
        InvoicePlan invoicePlan = new InvoicePlan();
        invoicePlan.setId(planId);
        invoicePlan.setContractId(contractId);
        invoicePlan.setContractProductId(contractProductId);
        invoicePlan.setProductTypeName(productTypeName);
        invoicePlan.setType(getType());
        invoicePlan.setRate(rate.multiply(new BigDecimal("100")));
        invoicePlan.setTaxId(taxId);
        invoicePlan.setTaxRate(rate.multiply(new BigDecimal("100")).intValue() + "%");
        invoicePlan.setDeletedFlag(false);
        return invoicePlan;
    }

    public InvoicePlanDetail generateInvoicePlanDetail() {
        InvoicePlanDetail invoicePlanDetail = new InvoicePlanDetail();
        invoicePlanDetail.setId(planDetailId);
        invoicePlanDetail.setContractId(contractId);
        invoicePlanDetail.setDate(date);
        invoicePlanDetail.setAmount(amount);
        invoicePlanDetail.setExcludingTaxAmount(excludingTaxAmount!=null?excludingTaxAmount:amount.divide(rate.add(new BigDecimal("1")), 3, RoundingMode.HALF_UP));
        invoicePlanDetail.setActualAmount(actualAmount!=null?actualAmount:BigDecimal.ZERO);
        invoicePlanDetail.setTaxIncludedPrice(actualAmount!=null?actualAmount:BigDecimal.ZERO);
        if (actualAmount != null) {
            invoicePlanDetail.setExclusiveOfTax(actualAmount.divide(rate.add(new BigDecimal("1")), 3, RoundingMode.HALF_UP));
        }
        invoicePlanDetail.setRequirement(requirement);
        invoicePlanDetail.setMilestoneId(milestoneId);
        invoicePlanDetail.setCode(detailCode);
        invoicePlanDetail.setNum(1);
        invoicePlanDetail.setDeletedFlag(false);
        invoicePlanDetail.setIsImport(Boolean.TRUE);
        invoicePlanDetail.setPaymentTerm("60（最新税票日期）");
        return invoicePlanDetail;
    }

    public Integer getType() {
        if ("纸质专票".equals(typeName)) {
            return 0;
        } else if ("纸质普票".equals(typeName)) {
            return 1;
        } else if ("电子专票".equals(typeName)) {
            return 2;
        } else if ("电子普票".equals(typeName)) {
            return 3;
        } else {
            validResult += "，发票类型错误";
            return null;
        }
    }
}
