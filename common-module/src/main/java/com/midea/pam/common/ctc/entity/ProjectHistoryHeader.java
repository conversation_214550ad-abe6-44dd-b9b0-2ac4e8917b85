package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "项目历史变更头表")
public class ProjectHistoryHeader extends LongIdEntity implements Serializable {
    private Boolean deletedFlag;

    @ApiModelProperty(value = "变更状态 0:草稿 3:审批中 4:审批通过 6:作废 11:审批撤回 -2:审批驳回")
    private Integer status;

    @ApiModelProperty(value = "变更类型，0-项目基本信息变更;1-项目里程碑变更,2-预算变更;3-收入成本计划变更;4-批量变更项目经理;5-批量变更项目财务;6-预立项转正（包含转正前后数据快照）;7-正式立项数据快照;8-预立项立项数据快照;9-目标成本变更;10-项目合同关系变更")
    private Integer changeType;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "变更原因/说明")
    private String reason;

    @ApiModelProperty(value = "变更原因类型")
    private String reasonType;

    @ApiModelProperty(value = "目标成本变更原因大类")
    private String reasonClassification;

    @ApiModelProperty(value = "是否同步变更目标成本(1是/0否)")
    private Boolean budgetTargetFlag;

    @ApiModelProperty(value = "WBS变更单id")
    private Long projectWbsReceiptsId;

    private static final long serialVersionUID = 1L;

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public String getReasonType() {
        return reasonType;
    }

    public void setReasonType(String reasonType) {
        this.reasonType = reasonType == null ? null : reasonType.trim();
    }

    public String getReasonClassification() {
        return reasonClassification;
    }

    public void setReasonClassification(String reasonClassification) {
        this.reasonClassification = reasonClassification == null ? null : reasonClassification.trim();
    }

    public Boolean getBudgetTargetFlag() {
        return budgetTargetFlag;
    }

    public void setBudgetTargetFlag(Boolean budgetTargetFlag) {
        this.budgetTargetFlag = budgetTargetFlag;
    }

    public Long getProjectWbsReceiptsId() {
        return projectWbsReceiptsId;
    }

    public void setProjectWbsReceiptsId(Long projectWbsReceiptsId) {
        this.projectWbsReceiptsId = projectWbsReceiptsId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", status=").append(status);
        sb.append(", changeType=").append(changeType);
        sb.append(", projectId=").append(projectId);
        sb.append(", reason=").append(reason);
        sb.append(", reasonType=").append(reasonType);
        sb.append(", reasonClassification=").append(reasonClassification);
        sb.append(", budgetTargetFlag=").append(budgetTargetFlag);
        sb.append(", projectWbsReceiptsId=").append(projectWbsReceiptsId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}