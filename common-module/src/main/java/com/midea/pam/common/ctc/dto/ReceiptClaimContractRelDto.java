package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.ReceiptClaimContractRel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @program: pam
 * @description: ReceiptClaimContractRelDto
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
@Getter
@Setter
public class ReceiptClaimContractRelDto extends ReceiptClaimContractRel {

    @ApiModelProperty(value = "收款编号")
    private String receiptCode;
    @ApiModelProperty(value = "合同名称")
    private String contractName;
    @ApiModelProperty(value = "合同编号")
    private String contractCode;
    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractAmount;
    @ApiModelProperty(value = "分配人")
    private String allocatedByName;
    @ApiModelProperty(value = "合同币种")
    private String currencyCode;
    @ApiModelProperty(value = "开票计划行编号")
    private String invoicePlanDetailCode;
    @ApiModelProperty(value = "开票计划行金额")
    private BigDecimal invoicePlanDetailAmount;
    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    @ApiModelProperty(value = "剩余已分配合同金额")
    private BigDecimal remainingContractAmount;

    @ApiModelProperty(value = "id（查询条件）")
    private List<Long> ids;
    @ApiModelProperty(value = "本次回款金额")
    private BigDecimal thisAmount;
    @ApiModelProperty(value = "累计汇款金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "业务分类id")
    private Long profitDepartmentId;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "到款日期")
    private Date payDate;

    @ApiModelProperty(value = "项目经理id")
    private Long managerId;

    @ApiModelProperty(value = "销售id")
    private Long salesManagerId;

    @ApiModelProperty(value = "产品总监id")
    private Long ownerId;

    @ApiModelProperty(value = "事业部总监id")
    private Long departmentManagerId;

    @ApiModelProperty(value = "区域总监id")
    private Long directorId;

    @ApiModelProperty(value = "主合同名称")
    private String parentContractName;

    private BigDecimal claimAmount;

    private Long projectId;

    private BigDecimal contractRefundedAmount;

    private Long receiptClaimId;

    // 里程碑id
    private Long milestoneId;

    // 关联里程碑
    private String milestoneName;

    // 开票条件
    private String requirement;

    // 实际开票金额
    private BigDecimal taxIncludedPrice;

    // 其它拆款已分配金额
    private BigDecimal otherAllocatedAmount;

    private String invoiceType;

    private String managerName;

    private String salesManagerName;

    @ApiModelProperty("本笔回款认领对应的退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("开票计划可分配额")
    private BigDecimal allocatableAmount;

    @ApiModelProperty(value = "业务实体ID")
    private Long ouId;

    @ApiModelProperty("开票计划可分配额标识,0：小于零;1：等于零;2：大于零")
    private String allocatableTag;

    private List<Integer> allocatableTagList;

    @ApiModelProperty(value = "是否初始化导入: 0-否 1-是")
    private Boolean isImport;

}

