package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "工时填报")
public class WorkingHour extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "周")
    private String week;

    @ApiModelProperty(value = "申请日期")
    private Date applyDate;

    @ApiModelProperty(value = "申请工时数")
    private BigDecimal applyWorkingHours;

    @ApiModelProperty(value = "考勤时长")
    private BigDecimal ihrAttendHours;

    @ApiModelProperty(value = "申请状态：1未提交、2审批中、3被驳回、4通过、5变更中、6变更驳回")
    private Integer status;

    @ApiModelProperty(value = "项目成员id")
    private Long userId;

    @ApiModelProperty(value = "项目成员mip")
    private String userMip;

    @ApiModelProperty(value = "部门id")
    private Long orgId;

    @ApiModelProperty(value = "部门名称")
    private String orgName;

    @ApiModelProperty(value = "填报人部门")
    private String applyOrg;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "流程id")
    private Long processId;

    @ApiModelProperty(value = "开票申请id")
    private Long invoiceApplyHeaderId;

    @ApiModelProperty(value = "等级")
    private String level;

    @ApiModelProperty(value = "费率类型:position 职级; role 角色")
    private String laborCostType;

    @ApiModelProperty(value = "人力费率角色设置ID")
    private Long laborCostTypeSetId;

    @ApiModelProperty(value = "人天价格")
    private BigDecimal costMoney;

    @ApiModelProperty(value = "实际人工费率")
    private BigDecimal actualCostMoney;

    @ApiModelProperty(value = "确认工时数")
    private BigDecimal actualWorkingHours;

    @ApiModelProperty(value = "审批备注")
    private String remarks;

    @ApiModelProperty(value = "删除标志；0或空：有效， 1：删除")
    private Integer deleteFlag;

    @ApiModelProperty(value = "审批人id")
    private Long approveUserId;

    @ApiModelProperty(value = "审批时间")
    private Date approveTime;

    @ApiModelProperty(value = "审批人姓名")
    private String approveUserName;

    @ApiModelProperty(value = "用户类型，1内部2人力点工3自招外包")
    private String userType;

    @ApiModelProperty(value = "使用单位id(一级unit.id)")
    private Long bizUnitId;

    @ApiModelProperty(value = "人力费率来源单位id")
    private Long laborCostSourceUnitId;

    @ApiModelProperty(value = "是否为切换数据: 0-否 1-是")
    private Boolean isImport;

    @ApiModelProperty(value = "是否来源RDM, 1-是；0-否")
    private Integer rdmFlag;

    @ApiModelProperty(value = "来源标记: pc-1, mobile-2, 工时转移-3, 工单工时导入-4, WBS工时导入-5")
    private Integer sourceFlag;

    @ApiModelProperty(value = "是否冲销(0否，1是)")
    private Integer writeOffStatus;

    @ApiModelProperty(value = "待审批人id")
    private Long stayApproveUserId;

    @ApiModelProperty(value = "待审批人mip")
    private String stayApproveUserMip;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "角色")
    private String roleName;

    @ApiModelProperty(value = "wbs短码，关联project_wbs_budget表的wbs_full_code字段")
    private String wbsBudgetCode;

    @ApiModelProperty(value = "wbs全码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "业务角色（wbs）id")
    private Long laborWbsCostId;

    @ApiModelProperty(value = "业务角色（wbs）name")
    private String laborWbsCostName;

    @ApiModelProperty(value = "项目活动事项编码")
    private String projectActivityCode;

    @ApiModelProperty(value = "项目OU")
    private Long ouId;

    @ApiModelProperty(value = "发薪OU")
    private Long fullPayOuId;

    private static final long serialVersionUID = 1L;

    public String getWeek() {
        return week;
    }

    public void setWeek(String week) {
        this.week = week == null ? null : week.trim();
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public BigDecimal getApplyWorkingHours() {
        return applyWorkingHours;
    }

    public void setApplyWorkingHours(BigDecimal applyWorkingHours) {
        this.applyWorkingHours = applyWorkingHours;
    }

    public BigDecimal getIhrAttendHours() {
        return ihrAttendHours;
    }

    public void setIhrAttendHours(BigDecimal ihrAttendHours) {
        this.ihrAttendHours = ihrAttendHours;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserMip() {
        return userMip;
    }

    public void setUserMip(String userMip) {
        this.userMip = userMip == null ? null : userMip.trim();
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    public String getApplyOrg() {
        return applyOrg;
    }

    public void setApplyOrg(String applyOrg) {
        this.applyOrg = applyOrg == null ? null : applyOrg.trim();
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    public Long getInvoiceApplyHeaderId() {
        return invoiceApplyHeaderId;
    }

    public void setInvoiceApplyHeaderId(Long invoiceApplyHeaderId) {
        this.invoiceApplyHeaderId = invoiceApplyHeaderId;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level == null ? null : level.trim();
    }

    public String getLaborCostType() {
        return laborCostType;
    }

    public void setLaborCostType(String laborCostType) {
        this.laborCostType = laborCostType == null ? null : laborCostType.trim();
    }

    public Long getLaborCostTypeSetId() {
        return laborCostTypeSetId;
    }

    public void setLaborCostTypeSetId(Long laborCostTypeSetId) {
        this.laborCostTypeSetId = laborCostTypeSetId;
    }

    public BigDecimal getCostMoney() {
        return costMoney;
    }

    public void setCostMoney(BigDecimal costMoney) {
        this.costMoney = costMoney;
    }

    public BigDecimal getActualCostMoney() {
        return actualCostMoney;
    }

    public void setActualCostMoney(BigDecimal actualCostMoney) {
        this.actualCostMoney = actualCostMoney;
    }

    public BigDecimal getActualWorkingHours() {
        return actualWorkingHours;
    }

    public void setActualWorkingHours(BigDecimal actualWorkingHours) {
        this.actualWorkingHours = actualWorkingHours;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Long getApproveUserId() {
        return approveUserId;
    }

    public void setApproveUserId(Long approveUserId) {
        this.approveUserId = approveUserId;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public String getApproveUserName() {
        return approveUserName;
    }

    public void setApproveUserName(String approveUserName) {
        this.approveUserName = approveUserName == null ? null : approveUserName.trim();
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType == null ? null : userType.trim();
    }

    public Long getBizUnitId() {
        return bizUnitId;
    }

    public void setBizUnitId(Long bizUnitId) {
        this.bizUnitId = bizUnitId;
    }

    public Long getLaborCostSourceUnitId() {
        return laborCostSourceUnitId;
    }

    public void setLaborCostSourceUnitId(Long laborCostSourceUnitId) {
        this.laborCostSourceUnitId = laborCostSourceUnitId;
    }

    public Boolean getIsImport() {
        return isImport;
    }

    public void setIsImport(Boolean isImport) {
        this.isImport = isImport;
    }

    public Integer getRdmFlag() {
        return rdmFlag;
    }

    public void setRdmFlag(Integer rdmFlag) {
        this.rdmFlag = rdmFlag;
    }

    public Integer getSourceFlag() {
        return sourceFlag;
    }

    public void setSourceFlag(Integer sourceFlag) {
        this.sourceFlag = sourceFlag;
    }

    public Integer getWriteOffStatus() {
        return writeOffStatus;
    }

    public void setWriteOffStatus(Integer writeOffStatus) {
        this.writeOffStatus = writeOffStatus;
    }

    public Long getStayApproveUserId() {
        return stayApproveUserId;
    }

    public void setStayApproveUserId(Long stayApproveUserId) {
        this.stayApproveUserId = stayApproveUserId;
    }

    public String getStayApproveUserMip() {
        return stayApproveUserMip;
    }

    public void setStayApproveUserMip(String stayApproveUserMip) {
        this.stayApproveUserMip = stayApproveUserMip == null ? null : stayApproveUserMip.trim();
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName == null ? null : roleName.trim();
    }

    public String getWbsBudgetCode() {
        return wbsBudgetCode;
    }

    public void setWbsBudgetCode(String wbsBudgetCode) {
        this.wbsBudgetCode = wbsBudgetCode == null ? null : wbsBudgetCode.trim();
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public Long getLaborWbsCostId() {
        return laborWbsCostId;
    }

    public void setLaborWbsCostId(Long laborWbsCostId) {
        this.laborWbsCostId = laborWbsCostId;
    }

    public String getLaborWbsCostName() {
        return laborWbsCostName;
    }

    public void setLaborWbsCostName(String laborWbsCostName) {
        this.laborWbsCostName = laborWbsCostName == null ? null : laborWbsCostName.trim();
    }

    public String getProjectActivityCode() {
        return projectActivityCode;
    }

    public void setProjectActivityCode(String projectActivityCode) {
        this.projectActivityCode = projectActivityCode == null ? null : projectActivityCode.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public Long getFullPayOuId() {
        return fullPayOuId;
    }

    public void setFullPayOuId(Long fullPayOuId) {
        this.fullPayOuId = fullPayOuId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", week=").append(week);
        sb.append(", applyDate=").append(applyDate);
        sb.append(", applyWorkingHours=").append(applyWorkingHours);
        sb.append(", ihrAttendHours=").append(ihrAttendHours);
        sb.append(", status=").append(status);
        sb.append(", userId=").append(userId);
        sb.append(", userMip=").append(userMip);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgName=").append(orgName);
        sb.append(", applyOrg=").append(applyOrg);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", processId=").append(processId);
        sb.append(", invoiceApplyHeaderId=").append(invoiceApplyHeaderId);
        sb.append(", level=").append(level);
        sb.append(", laborCostType=").append(laborCostType);
        sb.append(", laborCostTypeSetId=").append(laborCostTypeSetId);
        sb.append(", costMoney=").append(costMoney);
        sb.append(", actualCostMoney=").append(actualCostMoney);
        sb.append(", actualWorkingHours=").append(actualWorkingHours);
        sb.append(", remarks=").append(remarks);
        sb.append(", deleteFlag=").append(deleteFlag);
        sb.append(", approveUserId=").append(approveUserId);
        sb.append(", approveTime=").append(approveTime);
        sb.append(", approveUserName=").append(approveUserName);
        sb.append(", userType=").append(userType);
        sb.append(", bizUnitId=").append(bizUnitId);
        sb.append(", laborCostSourceUnitId=").append(laborCostSourceUnitId);
        sb.append(", isImport=").append(isImport);
        sb.append(", rdmFlag=").append(rdmFlag);
        sb.append(", sourceFlag=").append(sourceFlag);
        sb.append(", writeOffStatus=").append(writeOffStatus);
        sb.append(", stayApproveUserId=").append(stayApproveUserId);
        sb.append(", stayApproveUserMip=").append(stayApproveUserMip);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", roleName=").append(roleName);
        sb.append(", wbsBudgetCode=").append(wbsBudgetCode);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", laborWbsCostId=").append(laborWbsCostId);
        sb.append(", laborWbsCostName=").append(laborWbsCostName);
        sb.append(", projectActivityCode=").append(projectActivityCode);
        sb.append(", ouId=").append(ouId);
        sb.append(", fullPayOuId=").append(fullPayOuId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}