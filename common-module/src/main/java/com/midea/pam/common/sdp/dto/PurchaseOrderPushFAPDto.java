package com.midea.pam.common.sdp.dto;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderPushFAPDto {

    // ========== 必填字段 ==========

    /**
     * 流水号 - 外围系统写入
     */
    private String serialNumber;

    /**
     * 来源单据的唯一ID - ISCP采购订单头ID
     */
    private Long sourceId;

    /**
     * 来源系统编码 - ISCP
     */
    private String sourceSysCode;

    /**
     * 来源系统单据号 - 填写SCP系统的单据号-能把业务串联起来的编号且有唯一性，SCP_PO_NO
     */
    private String sourceSysBusCode;

    /**
     * 来源系统单据ID - 保证业务唯一性的ID--GSCP采购订单头ID
     */
    private Long sourceSysBusId;

    /**
     * 接口编号 - 默认：ISCP-FAP-A4
     */
    private String interfaceCode;

    /**
     * 场景编码 - 传入A412 表示单边采购，值包括：：A411 --双边贸易，A412–单边采购
     */
    private String sceneCode;

    /**
     * 单据状态 - 默认值：CONFIRM
     */
    private String orderStatus;

    /**
     * 供方组织源系统标识 - 如："GERP"，根据供方组织所在的系统赋值
     */
    private String suOrgOriginCode;

    /**
     * 需方组织源系统标识 - 如："GERP"，根据需方组织所在的系统赋值
     */
    private String reOrgOriginCode;

    /**
     * 退货标识(1=退货，0=非退货） - 默认值：0
     */
    private Integer returnFlag;

    /**
     * 无源单标识(1=无源单，0=非无源单) - 默认值：0
     */
    private Integer noLogistReturnFlag;

    /**
     * 红冲标识 - 默认值：0
     */
    private Integer reversalFlag;

    /**
     * 销货类型 - 如：GOODS：成品，MATERIAL：材料，如果传入的值是空时根据传入的供方销售订单类型查找场景与订单类型匹配出销售类型，如果供方的销售订单类型为空时必传
     */
    private String saleType;

    /**
     * 创建人名称 - 传入创建人MIP账号
     */
    private String createdBy;

    /**
     * 创建时间 - 落表时自动生成
     */
    private String creationDate;

    /**
     * 最后更新人名称 - 传入创建人MIP账号
     */
    private String lastUpdatedBy;

    /**
     * 最后更新时间 - 落表时自动生成
     */
    private String lastUpdateDate;

    /**
     * 来源系统业务属性 - 默认值：EXPORT   值包括：DOMESTIC：内销，EXPORT：外销
     */
    private String sourceSysBusType;

    /**
     * 采购订单类型 - 如：ZC01，业务系统传入
     */
    private String purchaseOrderType;

    // ========== 非必填字段 ==========

    /**
     * 主键
     */
    private Long id;

    /**
     * 链路编码
     */
    private String linkCode;

    /**
     * 供方库存组织ID - 二者必填一个
     */
    private Long suInvOrgId;

    /**
     * 供方库存组织编码
     */
    private String suInvOrgCode;

    /**
     * 需方库存组织ID - 二者必填一个
     */
    private Long reInvOrgId;

    /**
     * 需方库存组织编码
     */
    private String reInvOrgCode;

    /**
     * 来源单据分类
     */
    private String sourceDocumentType;

    /**
     * 业务模式
     */
    private String businessModel;

    /**
     * 税率代码 - 二者必填一个
     */
    private String taxRateCode;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 销售订单类型ID
     */
    private Long orderTypeId;

    /**
     * 销售订单类型 - 传入供方的销售订单类型
     */
    private String suOrderType;

    /**
     * 应收事务处理类型
     */
    private String custTrxType;

    /**
     * 跟踪号
     */
    private String traceCode;

    /**
     * 处理状态
     */
    private String processStatus;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 处理结果信息
     */
    private String processMessage;

    /**
     * 版本
     */
    private Long dataVersion;

    /**
     * 删除标识
     */
    private Integer deleteFlag;

    /**
     * 回调标识
     */
    private Integer callbackFlag;

    /**
     * 代销标识
     */
    private Integer consignmentFlag;

    /**
     * 客户签收标识
     */
    private Integer signForFlag;

    /**
     * 关联订单号
     */
    private String orderHeadCode;

    /**
     * 关联订单头ID
     */
    private Long orderHeadId;

    /**
     * 关联物流号
     */
    private String logistHeadCode;

    /**
     * 分组标识编码
     */
    private String groupCode;

    /**
     * 送货单头ID
     */
    private String targetBusId;

    /**
     * 目标单号
     */
    private String targetBusCode;

    /**
     * 币种 - 有传值时，生成关联订单时使用，没有传值时按现行的逻辑赋值
     */
    private String currencyCode;

    /**
     * 汇率类型 - 有传值时，生成关联订单时使用，没有传值时按现行的逻辑赋值
     */
    private String conversionType;

    /**
     * 汇率日期 - 有传值时，生成关联订单时使用，没有传值时按现行的逻辑赋值
     */
    private String conversionDate;

    /**
     * 供方汇率 - 当CONVERSION_TYPE为"USER"或"用户"时需要传值
     */
    private BigDecimal conversionRate;

    /**
     * 需方汇率 - 当CONVERSION_TYPE为"USER"或"用户"时需要传值
     */
    private BigDecimal conversionRateAp;

    /**
     * PO分组名称 - PO分组名称
     */
    private String poGroupName;

    /**
     * PO分组CODE - PO分组CODE，当前存在PO头ATTRIBUTE6
     */
    private String poGroupCode;

    /**
     * 价格来源 - 价格来源（根据弹性域1确定结算方式是BPA还是PO）,当前存在PO头的ATTRIBUTE2
     */
    private String settlementMethod;

    /**
     * 国内库存组织ID - 国内库存组织ID
     */
    private Long gerpOrganizationId;

    /**
     * 采购订单推送FAP行表列表
     */
    private List<PurchaseOrderPushFAPLineDto> lines;
}
