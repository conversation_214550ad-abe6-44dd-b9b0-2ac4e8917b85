package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.util.excel4j.ExcelDate2DateConverter;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 工单工时任务明细导入及导出ExcelVo
 * 重写了equals和hashCode方法，用于判断重复数据
 * <AUTHOR>
 * @date 2021/3/13
 */
@Getter
@Setter
public class TicketWorkingHourImportDetailExcelVo {

    @Excel(name = "出勤日期", format = "yyyy/MM/dd")
    @ExcelField(title = "出勤日期", order = 1, readConverter = ExcelDate2DateConverter.class)
    private Date attendanceDate;

    @Excel(name = "项目号")
    @ExcelField(title = "项目号", order = 2)
    private String projectCode;

    @Excel(name = "项目名称")
    @ExcelField(title = "项目名称", order = 3)
    private String projectName;

    @Excel(name = "工单任务号")
    @ExcelField(title = "工单任务号", order = 4)
    private String ticketTaskCode;

    @Excel(name = "员工类型")
    @ExcelField(title = "员工类型", order = 5)
    private String employeeType;

    @Excel(name = "MIP账号/供应商编码")
    @ExcelField(title = "MIP账号/供应商编码", order = 6)
    private String userMip;

    @Excel(name = "姓名/供应商名称")
    @ExcelField(title = "姓名/供应商名称", order = 7)
    private String userName;

    @Excel(name = "角色")
    @ExcelField(title = "角色", order = 8)
    private String roleName;

    @Excel(name = "工时")
    @ExcelField(title = "工时", order = 9)
    private BigDecimal workingHour;

    @Excel(name = "备注")
    @ExcelField(title = "备注", order = 10)
    private String remark;

    @Excel(name = "失败原因")
    private String failureReason;

    /**
     * 用户数字ID，冗余用于前端数据返传
     */
    private Long userId;

    /**
     * 项目ID，冗余用于前端数据返传
     */
    private Long projectId;

    /**
     * 工单任务ID，冗余用户前端回传数据
     */
    private Long ticketTasksId;

    /**
     * 外部人力费率成本入账价格
     */
    private BigDecimal externalProjectCost;

    /**
     * 考勤工时数
     */
    private BigDecimal ihrAttendHours;

    /**
     * 业务角色id
     */
    private Long laborWbsCostId;

    /**
     * 业务角色名称
     */
    private String laborWbsCostName;


    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName == null ? null : projectName.trim();
    }

    public void setTicketTaskCode(String ticketTaskCode) {
        this.ticketTaskCode = ticketTaskCode == null ? null : ticketTaskCode.trim();
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType == null ? null : employeeType.trim();
    }

    public void setUserMip(String userMip) {
        this.userMip = userMip == null ? null : userMip.trim();
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName == null ? null : roleName.trim();
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TicketWorkingHourImportDetailExcelVo that = (TicketWorkingHourImportDetailExcelVo) o;
        return Objects.equals(attendanceDate, that.attendanceDate) && Objects.equals(projectCode, that.projectCode) && Objects.equals(ticketTaskCode, that.ticketTaskCode) && Objects.equals(employeeType, that.employeeType) && Objects.equals(userMip, that.userMip) && Objects.equals(roleName, that.roleName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(attendanceDate, projectCode, ticketTaskCode, employeeType, userMip, roleName);
    }

    @Override
    public String toString() {
        return "TicketWorkingHourImportDetailExcelVo{" +
                "attendanceDate=" + attendanceDate +
                ", projectCode='" + projectCode + '\'' +
                ", projectName='" + projectName + '\'' +
                ", ticketTaskCode='" + ticketTaskCode + '\'' +
                ", employeeType='" + employeeType + '\'' +
                ", userMip='" + userMip + '\'' +
                ", userName='" + userName + '\'' +
                ", roleName='" + roleName + '\'' +
                ", workingHour=" + workingHour +
                ", remark='" + remark + '\'' +
                ", failureReason='" + failureReason + '\'' +
                ", userId=" + userId +
                ", projectId=" + projectId +
                ", ticketTasksId=" + ticketTasksId +
                ", externalProjectCost=" + externalProjectCost +
                '}';
    }
}
