package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectTypeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectTypeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCostMethodIsNull() {
            addCriterion("cost_method is null");
            return (Criteria) this;
        }

        public Criteria andCostMethodIsNotNull() {
            addCriterion("cost_method is not null");
            return (Criteria) this;
        }

        public Criteria andCostMethodEqualTo(String value) {
            addCriterion("cost_method =", value, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodNotEqualTo(String value) {
            addCriterion("cost_method <>", value, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodGreaterThan(String value) {
            addCriterion("cost_method >", value, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodGreaterThanOrEqualTo(String value) {
            addCriterion("cost_method >=", value, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodLessThan(String value) {
            addCriterion("cost_method <", value, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodLessThanOrEqualTo(String value) {
            addCriterion("cost_method <=", value, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodLike(String value) {
            addCriterion("cost_method like", value, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodNotLike(String value) {
            addCriterion("cost_method not like", value, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodIn(List<String> values) {
            addCriterion("cost_method in", values, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodNotIn(List<String> values) {
            addCriterion("cost_method not in", values, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodBetween(String value1, String value2) {
            addCriterion("cost_method between", value1, value2, "costMethod");
            return (Criteria) this;
        }

        public Criteria andCostMethodNotBetween(String value1, String value2) {
            addCriterion("cost_method not between", value1, value2, "costMethod");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNull() {
            addCriterion("order_num is null");
            return (Criteria) this;
        }

        public Criteria andOrderNumIsNotNull() {
            addCriterion("order_num is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNumEqualTo(Integer value) {
            addCriterion("order_num =", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotEqualTo(Integer value) {
            addCriterion("order_num <>", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThan(Integer value) {
            addCriterion("order_num >", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_num >=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThan(Integer value) {
            addCriterion("order_num <", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumLessThanOrEqualTo(Integer value) {
            addCriterion("order_num <=", value, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumIn(List<Integer> values) {
            addCriterion("order_num in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotIn(List<Integer> values) {
            addCriterion("order_num not in", values, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumBetween(Integer value1, Integer value2) {
            addCriterion("order_num between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andOrderNumNotBetween(Integer value1, Integer value2) {
            addCriterion("order_num not between", value1, value2, "orderNum");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdIsNull() {
            addCriterion("budget_dep_Id is null");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdIsNotNull() {
            addCriterion("budget_dep_Id is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdEqualTo(Long value) {
            addCriterion("budget_dep_Id =", value, "budgetDepId");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdNotEqualTo(Long value) {
            addCriterion("budget_dep_Id <>", value, "budgetDepId");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdGreaterThan(Long value) {
            addCriterion("budget_dep_Id >", value, "budgetDepId");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdGreaterThanOrEqualTo(Long value) {
            addCriterion("budget_dep_Id >=", value, "budgetDepId");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdLessThan(Long value) {
            addCriterion("budget_dep_Id <", value, "budgetDepId");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdLessThanOrEqualTo(Long value) {
            addCriterion("budget_dep_Id <=", value, "budgetDepId");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdIn(List<Long> values) {
            addCriterion("budget_dep_Id in", values, "budgetDepId");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdNotIn(List<Long> values) {
            addCriterion("budget_dep_Id not in", values, "budgetDepId");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdBetween(Long value1, Long value2) {
            addCriterion("budget_dep_Id between", value1, value2, "budgetDepId");
            return (Criteria) this;
        }

        public Criteria andBudgetDepIdNotBetween(Long value1, Long value2) {
            addCriterion("budget_dep_Id not between", value1, value2, "budgetDepId");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameIsNull() {
            addCriterion("budget_dep_name is null");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameIsNotNull() {
            addCriterion("budget_dep_name is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameEqualTo(String value) {
            addCriterion("budget_dep_name =", value, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameNotEqualTo(String value) {
            addCriterion("budget_dep_name <>", value, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameGreaterThan(String value) {
            addCriterion("budget_dep_name >", value, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameGreaterThanOrEqualTo(String value) {
            addCriterion("budget_dep_name >=", value, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameLessThan(String value) {
            addCriterion("budget_dep_name <", value, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameLessThanOrEqualTo(String value) {
            addCriterion("budget_dep_name <=", value, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameLike(String value) {
            addCriterion("budget_dep_name like", value, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameNotLike(String value) {
            addCriterion("budget_dep_name not like", value, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameIn(List<String> values) {
            addCriterion("budget_dep_name in", values, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameNotIn(List<String> values) {
            addCriterion("budget_dep_name not in", values, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameBetween(String value1, String value2) {
            addCriterion("budget_dep_name between", value1, value2, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andBudgetDepNameNotBetween(String value1, String value2) {
            addCriterion("budget_dep_name not between", value1, value2, "budgetDepName");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostIsNull() {
            addCriterion("auto_milepost is null");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostIsNotNull() {
            addCriterion("auto_milepost is not null");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostEqualTo(Boolean value) {
            addCriterion("auto_milepost =", value, "autoMilepost");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostNotEqualTo(Boolean value) {
            addCriterion("auto_milepost <>", value, "autoMilepost");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostGreaterThan(Boolean value) {
            addCriterion("auto_milepost >", value, "autoMilepost");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostGreaterThanOrEqualTo(Boolean value) {
            addCriterion("auto_milepost >=", value, "autoMilepost");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostLessThan(Boolean value) {
            addCriterion("auto_milepost <", value, "autoMilepost");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostLessThanOrEqualTo(Boolean value) {
            addCriterion("auto_milepost <=", value, "autoMilepost");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostIn(List<Boolean> values) {
            addCriterion("auto_milepost in", values, "autoMilepost");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostNotIn(List<Boolean> values) {
            addCriterion("auto_milepost not in", values, "autoMilepost");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostBetween(Boolean value1, Boolean value2) {
            addCriterion("auto_milepost between", value1, value2, "autoMilepost");
            return (Criteria) this;
        }

        public Criteria andAutoMilepostNotBetween(Boolean value1, Boolean value2) {
            addCriterion("auto_milepost not between", value1, value2, "autoMilepost");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansIsNull() {
            addCriterion("validate_budget_humans is null");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansIsNotNull() {
            addCriterion("validate_budget_humans is not null");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansEqualTo(Boolean value) {
            addCriterion("validate_budget_humans =", value, "validateBudgetHumans");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansNotEqualTo(Boolean value) {
            addCriterion("validate_budget_humans <>", value, "validateBudgetHumans");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansGreaterThan(Boolean value) {
            addCriterion("validate_budget_humans >", value, "validateBudgetHumans");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_budget_humans >=", value, "validateBudgetHumans");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansLessThan(Boolean value) {
            addCriterion("validate_budget_humans <", value, "validateBudgetHumans");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_budget_humans <=", value, "validateBudgetHumans");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansIn(List<Boolean> values) {
            addCriterion("validate_budget_humans in", values, "validateBudgetHumans");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansNotIn(List<Boolean> values) {
            addCriterion("validate_budget_humans not in", values, "validateBudgetHumans");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_budget_humans between", value1, value2, "validateBudgetHumans");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetHumansNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_budget_humans not between", value1, value2, "validateBudgetHumans");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsIsNull() {
            addCriterion("validate_budget_travels is null");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsIsNotNull() {
            addCriterion("validate_budget_travels is not null");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsEqualTo(Boolean value) {
            addCriterion("validate_budget_travels =", value, "validateBudgetTravels");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsNotEqualTo(Boolean value) {
            addCriterion("validate_budget_travels <>", value, "validateBudgetTravels");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsGreaterThan(Boolean value) {
            addCriterion("validate_budget_travels >", value, "validateBudgetTravels");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_budget_travels >=", value, "validateBudgetTravels");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsLessThan(Boolean value) {
            addCriterion("validate_budget_travels <", value, "validateBudgetTravels");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_budget_travels <=", value, "validateBudgetTravels");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsIn(List<Boolean> values) {
            addCriterion("validate_budget_travels in", values, "validateBudgetTravels");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsNotIn(List<Boolean> values) {
            addCriterion("validate_budget_travels not in", values, "validateBudgetTravels");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_budget_travels between", value1, value2, "validateBudgetTravels");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetTravelsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_budget_travels not between", value1, value2, "validateBudgetTravels");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesIsNull() {
            addCriterion("validate_budget_fees is null");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesIsNotNull() {
            addCriterion("validate_budget_fees is not null");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesEqualTo(Boolean value) {
            addCriterion("validate_budget_fees =", value, "validateBudgetFees");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesNotEqualTo(Boolean value) {
            addCriterion("validate_budget_fees <>", value, "validateBudgetFees");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesGreaterThan(Boolean value) {
            addCriterion("validate_budget_fees >", value, "validateBudgetFees");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_budget_fees >=", value, "validateBudgetFees");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesLessThan(Boolean value) {
            addCriterion("validate_budget_fees <", value, "validateBudgetFees");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_budget_fees <=", value, "validateBudgetFees");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesIn(List<Boolean> values) {
            addCriterion("validate_budget_fees in", values, "validateBudgetFees");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesNotIn(List<Boolean> values) {
            addCriterion("validate_budget_fees not in", values, "validateBudgetFees");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_budget_fees between", value1, value2, "validateBudgetFees");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetFeesNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_budget_fees not between", value1, value2, "validateBudgetFees");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsIsNull() {
            addCriterion("validate_budget_materials is null");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsIsNotNull() {
            addCriterion("validate_budget_materials is not null");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsEqualTo(Boolean value) {
            addCriterion("validate_budget_materials =", value, "validateBudgetMaterials");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsNotEqualTo(Boolean value) {
            addCriterion("validate_budget_materials <>", value, "validateBudgetMaterials");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsGreaterThan(Boolean value) {
            addCriterion("validate_budget_materials >", value, "validateBudgetMaterials");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_budget_materials >=", value, "validateBudgetMaterials");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsLessThan(Boolean value) {
            addCriterion("validate_budget_materials <", value, "validateBudgetMaterials");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_budget_materials <=", value, "validateBudgetMaterials");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsIn(List<Boolean> values) {
            addCriterion("validate_budget_materials in", values, "validateBudgetMaterials");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsNotIn(List<Boolean> values) {
            addCriterion("validate_budget_materials not in", values, "validateBudgetMaterials");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_budget_materials between", value1, value2, "validateBudgetMaterials");
            return (Criteria) this;
        }

        public Criteria andValidateBudgetMaterialsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_budget_materials not between", value1, value2, "validateBudgetMaterials");
            return (Criteria) this;
        }

        public Criteria andValidateContractIsNull() {
            addCriterion("validate_contract is null");
            return (Criteria) this;
        }

        public Criteria andValidateContractIsNotNull() {
            addCriterion("validate_contract is not null");
            return (Criteria) this;
        }

        public Criteria andValidateContractEqualTo(Boolean value) {
            addCriterion("validate_contract =", value, "validateContract");
            return (Criteria) this;
        }

        public Criteria andValidateContractNotEqualTo(Boolean value) {
            addCriterion("validate_contract <>", value, "validateContract");
            return (Criteria) this;
        }

        public Criteria andValidateContractGreaterThan(Boolean value) {
            addCriterion("validate_contract >", value, "validateContract");
            return (Criteria) this;
        }

        public Criteria andValidateContractGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_contract >=", value, "validateContract");
            return (Criteria) this;
        }

        public Criteria andValidateContractLessThan(Boolean value) {
            addCriterion("validate_contract <", value, "validateContract");
            return (Criteria) this;
        }

        public Criteria andValidateContractLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_contract <=", value, "validateContract");
            return (Criteria) this;
        }

        public Criteria andValidateContractIn(List<Boolean> values) {
            addCriterion("validate_contract in", values, "validateContract");
            return (Criteria) this;
        }

        public Criteria andValidateContractNotIn(List<Boolean> values) {
            addCriterion("validate_contract not in", values, "validateContract");
            return (Criteria) this;
        }

        public Criteria andValidateContractBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_contract between", value1, value2, "validateContract");
            return (Criteria) this;
        }

        public Criteria andValidateContractNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_contract not between", value1, value2, "validateContract");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpIsNull() {
            addCriterion("validate_miplepost_help is null");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpIsNotNull() {
            addCriterion("validate_miplepost_help is not null");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpEqualTo(Boolean value) {
            addCriterion("validate_miplepost_help =", value, "validateMiplepostHelp");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpNotEqualTo(Boolean value) {
            addCriterion("validate_miplepost_help <>", value, "validateMiplepostHelp");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpGreaterThan(Boolean value) {
            addCriterion("validate_miplepost_help >", value, "validateMiplepostHelp");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_miplepost_help >=", value, "validateMiplepostHelp");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpLessThan(Boolean value) {
            addCriterion("validate_miplepost_help <", value, "validateMiplepostHelp");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_miplepost_help <=", value, "validateMiplepostHelp");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpIn(List<Boolean> values) {
            addCriterion("validate_miplepost_help in", values, "validateMiplepostHelp");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpNotIn(List<Boolean> values) {
            addCriterion("validate_miplepost_help not in", values, "validateMiplepostHelp");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_miplepost_help between", value1, value2, "validateMiplepostHelp");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostHelpNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_miplepost_help not between", value1, value2, "validateMiplepostHelp");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainIsNull() {
            addCriterion("validate_miplepost_main is null");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainIsNotNull() {
            addCriterion("validate_miplepost_main is not null");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainEqualTo(Boolean value) {
            addCriterion("validate_miplepost_main =", value, "validateMiplepostMain");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainNotEqualTo(Boolean value) {
            addCriterion("validate_miplepost_main <>", value, "validateMiplepostMain");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainGreaterThan(Boolean value) {
            addCriterion("validate_miplepost_main >", value, "validateMiplepostMain");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_miplepost_main >=", value, "validateMiplepostMain");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainLessThan(Boolean value) {
            addCriterion("validate_miplepost_main <", value, "validateMiplepostMain");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_miplepost_main <=", value, "validateMiplepostMain");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainIn(List<Boolean> values) {
            addCriterion("validate_miplepost_main in", values, "validateMiplepostMain");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainNotIn(List<Boolean> values) {
            addCriterion("validate_miplepost_main not in", values, "validateMiplepostMain");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_miplepost_main between", value1, value2, "validateMiplepostMain");
            return (Criteria) this;
        }

        public Criteria andValidateMiplepostMainNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_miplepost_main not between", value1, value2, "validateMiplepostMain");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessIsNull() {
            addCriterion("validate_business is null");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessIsNotNull() {
            addCriterion("validate_business is not null");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessEqualTo(Boolean value) {
            addCriterion("validate_business =", value, "validateBusiness");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessNotEqualTo(Boolean value) {
            addCriterion("validate_business <>", value, "validateBusiness");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessGreaterThan(Boolean value) {
            addCriterion("validate_business >", value, "validateBusiness");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_business >=", value, "validateBusiness");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessLessThan(Boolean value) {
            addCriterion("validate_business <", value, "validateBusiness");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_business <=", value, "validateBusiness");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessIn(List<Boolean> values) {
            addCriterion("validate_business in", values, "validateBusiness");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessNotIn(List<Boolean> values) {
            addCriterion("validate_business not in", values, "validateBusiness");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_business between", value1, value2, "validateBusiness");
            return (Criteria) this;
        }

        public Criteria andValidateBusinessNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_business not between", value1, value2, "validateBusiness");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalIsNull() {
            addCriterion("validate_financal is null");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalIsNotNull() {
            addCriterion("validate_financal is not null");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalEqualTo(Boolean value) {
            addCriterion("validate_financal =", value, "validateFinancal");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalNotEqualTo(Boolean value) {
            addCriterion("validate_financal <>", value, "validateFinancal");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalGreaterThan(Boolean value) {
            addCriterion("validate_financal >", value, "validateFinancal");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_financal >=", value, "validateFinancal");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalLessThan(Boolean value) {
            addCriterion("validate_financal <", value, "validateFinancal");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_financal <=", value, "validateFinancal");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalIn(List<Boolean> values) {
            addCriterion("validate_financal in", values, "validateFinancal");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalNotIn(List<Boolean> values) {
            addCriterion("validate_financal not in", values, "validateFinancal");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_financal between", value1, value2, "validateFinancal");
            return (Criteria) this;
        }

        public Criteria andValidateFinancalNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_financal not between", value1, value2, "validateFinancal");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetIsNull() {
            addCriterion("validate_year_budget is null");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetIsNotNull() {
            addCriterion("validate_year_budget is not null");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetEqualTo(Boolean value) {
            addCriterion("validate_year_budget =", value, "validateYearBudget");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetNotEqualTo(Boolean value) {
            addCriterion("validate_year_budget <>", value, "validateYearBudget");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetGreaterThan(Boolean value) {
            addCriterion("validate_year_budget >", value, "validateYearBudget");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_year_budget >=", value, "validateYearBudget");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetLessThan(Boolean value) {
            addCriterion("validate_year_budget <", value, "validateYearBudget");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_year_budget <=", value, "validateYearBudget");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetIn(List<Boolean> values) {
            addCriterion("validate_year_budget in", values, "validateYearBudget");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetNotIn(List<Boolean> values) {
            addCriterion("validate_year_budget not in", values, "validateYearBudget");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_year_budget between", value1, value2, "validateYearBudget");
            return (Criteria) this;
        }

        public Criteria andValidateYearBudgetNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_year_budget not between", value1, value2, "validateYearBudget");
            return (Criteria) this;
        }

        public Criteria andValidateWfIsNull() {
            addCriterion("validate_wf is null");
            return (Criteria) this;
        }

        public Criteria andValidateWfIsNotNull() {
            addCriterion("validate_wf is not null");
            return (Criteria) this;
        }

        public Criteria andValidateWfEqualTo(Boolean value) {
            addCriterion("validate_wf =", value, "validateWf");
            return (Criteria) this;
        }

        public Criteria andValidateWfNotEqualTo(Boolean value) {
            addCriterion("validate_wf <>", value, "validateWf");
            return (Criteria) this;
        }

        public Criteria andValidateWfGreaterThan(Boolean value) {
            addCriterion("validate_wf >", value, "validateWf");
            return (Criteria) this;
        }

        public Criteria andValidateWfGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_wf >=", value, "validateWf");
            return (Criteria) this;
        }

        public Criteria andValidateWfLessThan(Boolean value) {
            addCriterion("validate_wf <", value, "validateWf");
            return (Criteria) this;
        }

        public Criteria andValidateWfLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_wf <=", value, "validateWf");
            return (Criteria) this;
        }

        public Criteria andValidateWfIn(List<Boolean> values) {
            addCriterion("validate_wf in", values, "validateWf");
            return (Criteria) this;
        }

        public Criteria andValidateWfNotIn(List<Boolean> values) {
            addCriterion("validate_wf not in", values, "validateWf");
            return (Criteria) this;
        }

        public Criteria andValidateWfBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_wf between", value1, value2, "validateWf");
            return (Criteria) this;
        }

        public Criteria andValidateWfNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_wf not between", value1, value2, "validateWf");
            return (Criteria) this;
        }

        public Criteria andValidateAmountIsNull() {
            addCriterion("validate_amount is null");
            return (Criteria) this;
        }

        public Criteria andValidateAmountIsNotNull() {
            addCriterion("validate_amount is not null");
            return (Criteria) this;
        }

        public Criteria andValidateAmountEqualTo(Boolean value) {
            addCriterion("validate_amount =", value, "validateAmount");
            return (Criteria) this;
        }

        public Criteria andValidateAmountNotEqualTo(Boolean value) {
            addCriterion("validate_amount <>", value, "validateAmount");
            return (Criteria) this;
        }

        public Criteria andValidateAmountGreaterThan(Boolean value) {
            addCriterion("validate_amount >", value, "validateAmount");
            return (Criteria) this;
        }

        public Criteria andValidateAmountGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_amount >=", value, "validateAmount");
            return (Criteria) this;
        }

        public Criteria andValidateAmountLessThan(Boolean value) {
            addCriterion("validate_amount <", value, "validateAmount");
            return (Criteria) this;
        }

        public Criteria andValidateAmountLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_amount <=", value, "validateAmount");
            return (Criteria) this;
        }

        public Criteria andValidateAmountIn(List<Boolean> values) {
            addCriterion("validate_amount in", values, "validateAmount");
            return (Criteria) this;
        }

        public Criteria andValidateAmountNotIn(List<Boolean> values) {
            addCriterion("validate_amount not in", values, "validateAmount");
            return (Criteria) this;
        }

        public Criteria andValidateAmountBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_amount between", value1, value2, "validateAmount");
            return (Criteria) this;
        }

        public Criteria andValidateAmountNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_amount not between", value1, value2, "validateAmount");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodIsNull() {
            addCriterion("income_method is null");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodIsNotNull() {
            addCriterion("income_method is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodEqualTo(String value) {
            addCriterion("income_method =", value, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodNotEqualTo(String value) {
            addCriterion("income_method <>", value, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodGreaterThan(String value) {
            addCriterion("income_method >", value, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodGreaterThanOrEqualTo(String value) {
            addCriterion("income_method >=", value, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodLessThan(String value) {
            addCriterion("income_method <", value, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodLessThanOrEqualTo(String value) {
            addCriterion("income_method <=", value, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodLike(String value) {
            addCriterion("income_method like", value, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodNotLike(String value) {
            addCriterion("income_method not like", value, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodIn(List<String> values) {
            addCriterion("income_method in", values, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodNotIn(List<String> values) {
            addCriterion("income_method not in", values, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodBetween(String value1, String value2) {
            addCriterion("income_method between", value1, value2, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomeMethodNotBetween(String value1, String value2) {
            addCriterion("income_method not between", value1, value2, "incomeMethod");
            return (Criteria) this;
        }

        public Criteria andIncomePointIsNull() {
            addCriterion("income_point is null");
            return (Criteria) this;
        }

        public Criteria andIncomePointIsNotNull() {
            addCriterion("income_point is not null");
            return (Criteria) this;
        }

        public Criteria andIncomePointEqualTo(String value) {
            addCriterion("income_point =", value, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointNotEqualTo(String value) {
            addCriterion("income_point <>", value, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointGreaterThan(String value) {
            addCriterion("income_point >", value, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointGreaterThanOrEqualTo(String value) {
            addCriterion("income_point >=", value, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointLessThan(String value) {
            addCriterion("income_point <", value, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointLessThanOrEqualTo(String value) {
            addCriterion("income_point <=", value, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointLike(String value) {
            addCriterion("income_point like", value, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointNotLike(String value) {
            addCriterion("income_point not like", value, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointIn(List<String> values) {
            addCriterion("income_point in", values, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointNotIn(List<String> values) {
            addCriterion("income_point not in", values, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointBetween(String value1, String value2) {
            addCriterion("income_point between", value1, value2, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andIncomePointNotBetween(String value1, String value2) {
            addCriterion("income_point not between", value1, value2, "incomePoint");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdIsNull() {
            addCriterion("milepost_template_id is null");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdIsNotNull() {
            addCriterion("milepost_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdEqualTo(Long value) {
            addCriterion("milepost_template_id =", value, "milepostTemplateId");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdNotEqualTo(Long value) {
            addCriterion("milepost_template_id <>", value, "milepostTemplateId");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdGreaterThan(Long value) {
            addCriterion("milepost_template_id >", value, "milepostTemplateId");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("milepost_template_id >=", value, "milepostTemplateId");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdLessThan(Long value) {
            addCriterion("milepost_template_id <", value, "milepostTemplateId");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("milepost_template_id <=", value, "milepostTemplateId");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdIn(List<Long> values) {
            addCriterion("milepost_template_id in", values, "milepostTemplateId");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdNotIn(List<Long> values) {
            addCriterion("milepost_template_id not in", values, "milepostTemplateId");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdBetween(Long value1, Long value2) {
            addCriterion("milepost_template_id between", value1, value2, "milepostTemplateId");
            return (Criteria) this;
        }

        public Criteria andMilepostTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("milepost_template_id not between", value1, value2, "milepostTemplateId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNull() {
            addCriterion("tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNotNull() {
            addCriterion("tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualTo(BigDecimal value) {
            addCriterion("tax_rate =", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("tax_rate <>", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThan(BigDecimal value) {
            addCriterion("tax_rate >", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate >=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThan(BigDecimal value) {
            addCriterion("tax_rate <", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate <=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateIn(List<BigDecimal> values) {
            addCriterion("tax_rate in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("tax_rate not in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate not between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryTagIsNull() {
            addCriterion("category_tag is null");
            return (Criteria) this;
        }

        public Criteria andCategoryTagIsNotNull() {
            addCriterion("category_tag is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryTagEqualTo(String value) {
            addCriterion("category_tag =", value, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagNotEqualTo(String value) {
            addCriterion("category_tag <>", value, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagGreaterThan(String value) {
            addCriterion("category_tag >", value, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagGreaterThanOrEqualTo(String value) {
            addCriterion("category_tag >=", value, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagLessThan(String value) {
            addCriterion("category_tag <", value, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagLessThanOrEqualTo(String value) {
            addCriterion("category_tag <=", value, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagLike(String value) {
            addCriterion("category_tag like", value, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagNotLike(String value) {
            addCriterion("category_tag not like", value, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagIn(List<String> values) {
            addCriterion("category_tag in", values, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagNotIn(List<String> values) {
            addCriterion("category_tag not in", values, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagBetween(String value1, String value2) {
            addCriterion("category_tag between", value1, value2, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andCategoryTagNotBetween(String value1, String value2) {
            addCriterion("category_tag not between", value1, value2, "categoryTag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagIsNull() {
            addCriterion("locator_flag is null");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagIsNotNull() {
            addCriterion("locator_flag is not null");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagEqualTo(Boolean value) {
            addCriterion("locator_flag =", value, "locatorFlag");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagNotEqualTo(Boolean value) {
            addCriterion("locator_flag <>", value, "locatorFlag");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagGreaterThan(Boolean value) {
            addCriterion("locator_flag >", value, "locatorFlag");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("locator_flag >=", value, "locatorFlag");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagLessThan(Boolean value) {
            addCriterion("locator_flag <", value, "locatorFlag");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("locator_flag <=", value, "locatorFlag");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagIn(List<Boolean> values) {
            addCriterion("locator_flag in", values, "locatorFlag");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagNotIn(List<Boolean> values) {
            addCriterion("locator_flag not in", values, "locatorFlag");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("locator_flag between", value1, value2, "locatorFlag");
            return (Criteria) this;
        }

        public Criteria andLocatorFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("locator_flag not between", value1, value2, "locatorFlag");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewIsNull() {
            addCriterion("validate_preview is null");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewIsNotNull() {
            addCriterion("validate_preview is not null");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewEqualTo(Boolean value) {
            addCriterion("validate_preview =", value, "validatePreview");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewNotEqualTo(Boolean value) {
            addCriterion("validate_preview <>", value, "validatePreview");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewGreaterThan(Boolean value) {
            addCriterion("validate_preview >", value, "validatePreview");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_preview >=", value, "validatePreview");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewLessThan(Boolean value) {
            addCriterion("validate_preview <", value, "validatePreview");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_preview <=", value, "validatePreview");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewIn(List<Boolean> values) {
            addCriterion("validate_preview in", values, "validatePreview");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewNotIn(List<Boolean> values) {
            addCriterion("validate_preview not in", values, "validatePreview");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_preview between", value1, value2, "validatePreview");
            return (Criteria) this;
        }

        public Criteria andValidatePreviewNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_preview not between", value1, value2, "validatePreview");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeIsNull() {
            addCriterion("validate_project_type is null");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeIsNotNull() {
            addCriterion("validate_project_type is not null");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeEqualTo(Boolean value) {
            addCriterion("validate_project_type =", value, "validateProjectType");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeNotEqualTo(Boolean value) {
            addCriterion("validate_project_type <>", value, "validateProjectType");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeGreaterThan(Boolean value) {
            addCriterion("validate_project_type >", value, "validateProjectType");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_project_type >=", value, "validateProjectType");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeLessThan(Boolean value) {
            addCriterion("validate_project_type <", value, "validateProjectType");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_project_type <=", value, "validateProjectType");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeIn(List<Boolean> values) {
            addCriterion("validate_project_type in", values, "validateProjectType");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeNotIn(List<Boolean> values) {
            addCriterion("validate_project_type not in", values, "validateProjectType");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_project_type between", value1, value2, "validateProjectType");
            return (Criteria) this;
        }

        public Criteria andValidateProjectTypeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_project_type not between", value1, value2, "validateProjectType");
            return (Criteria) this;
        }

        public Criteria andValidateCodeIsNull() {
            addCriterion("validate_code is null");
            return (Criteria) this;
        }

        public Criteria andValidateCodeIsNotNull() {
            addCriterion("validate_code is not null");
            return (Criteria) this;
        }

        public Criteria andValidateCodeEqualTo(Boolean value) {
            addCriterion("validate_code =", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeNotEqualTo(Boolean value) {
            addCriterion("validate_code <>", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeGreaterThan(Boolean value) {
            addCriterion("validate_code >", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_code >=", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeLessThan(Boolean value) {
            addCriterion("validate_code <", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_code <=", value, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeIn(List<Boolean> values) {
            addCriterion("validate_code in", values, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeNotIn(List<Boolean> values) {
            addCriterion("validate_code not in", values, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_code between", value1, value2, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateCodeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_code not between", value1, value2, "validateCode");
            return (Criteria) this;
        }

        public Criteria andValidateNameIsNull() {
            addCriterion("validate_name is null");
            return (Criteria) this;
        }

        public Criteria andValidateNameIsNotNull() {
            addCriterion("validate_name is not null");
            return (Criteria) this;
        }

        public Criteria andValidateNameEqualTo(Boolean value) {
            addCriterion("validate_name =", value, "validateName");
            return (Criteria) this;
        }

        public Criteria andValidateNameNotEqualTo(Boolean value) {
            addCriterion("validate_name <>", value, "validateName");
            return (Criteria) this;
        }

        public Criteria andValidateNameGreaterThan(Boolean value) {
            addCriterion("validate_name >", value, "validateName");
            return (Criteria) this;
        }

        public Criteria andValidateNameGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_name >=", value, "validateName");
            return (Criteria) this;
        }

        public Criteria andValidateNameLessThan(Boolean value) {
            addCriterion("validate_name <", value, "validateName");
            return (Criteria) this;
        }

        public Criteria andValidateNameLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_name <=", value, "validateName");
            return (Criteria) this;
        }

        public Criteria andValidateNameIn(List<Boolean> values) {
            addCriterion("validate_name in", values, "validateName");
            return (Criteria) this;
        }

        public Criteria andValidateNameNotIn(List<Boolean> values) {
            addCriterion("validate_name not in", values, "validateName");
            return (Criteria) this;
        }

        public Criteria andValidateNameBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_name between", value1, value2, "validateName");
            return (Criteria) this;
        }

        public Criteria andValidateNameNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_name not between", value1, value2, "validateName");
            return (Criteria) this;
        }

        public Criteria andValidateManagerIsNull() {
            addCriterion("validate_manager is null");
            return (Criteria) this;
        }

        public Criteria andValidateManagerIsNotNull() {
            addCriterion("validate_manager is not null");
            return (Criteria) this;
        }

        public Criteria andValidateManagerEqualTo(Boolean value) {
            addCriterion("validate_manager =", value, "validateManager");
            return (Criteria) this;
        }

        public Criteria andValidateManagerNotEqualTo(Boolean value) {
            addCriterion("validate_manager <>", value, "validateManager");
            return (Criteria) this;
        }

        public Criteria andValidateManagerGreaterThan(Boolean value) {
            addCriterion("validate_manager >", value, "validateManager");
            return (Criteria) this;
        }

        public Criteria andValidateManagerGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_manager >=", value, "validateManager");
            return (Criteria) this;
        }

        public Criteria andValidateManagerLessThan(Boolean value) {
            addCriterion("validate_manager <", value, "validateManager");
            return (Criteria) this;
        }

        public Criteria andValidateManagerLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_manager <=", value, "validateManager");
            return (Criteria) this;
        }

        public Criteria andValidateManagerIn(List<Boolean> values) {
            addCriterion("validate_manager in", values, "validateManager");
            return (Criteria) this;
        }

        public Criteria andValidateManagerNotIn(List<Boolean> values) {
            addCriterion("validate_manager not in", values, "validateManager");
            return (Criteria) this;
        }

        public Criteria andValidateManagerBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_manager between", value1, value2, "validateManager");
            return (Criteria) this;
        }

        public Criteria andValidateManagerNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_manager not between", value1, value2, "validateManager");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeIsNull() {
            addCriterion("validate_price_type is null");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeIsNotNull() {
            addCriterion("validate_price_type is not null");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeEqualTo(Boolean value) {
            addCriterion("validate_price_type =", value, "validatePriceType");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeNotEqualTo(Boolean value) {
            addCriterion("validate_price_type <>", value, "validatePriceType");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeGreaterThan(Boolean value) {
            addCriterion("validate_price_type >", value, "validatePriceType");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_price_type >=", value, "validatePriceType");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeLessThan(Boolean value) {
            addCriterion("validate_price_type <", value, "validatePriceType");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_price_type <=", value, "validatePriceType");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeIn(List<Boolean> values) {
            addCriterion("validate_price_type in", values, "validatePriceType");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeNotIn(List<Boolean> values) {
            addCriterion("validate_price_type not in", values, "validatePriceType");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_price_type between", value1, value2, "validatePriceType");
            return (Criteria) this;
        }

        public Criteria andValidatePriceTypeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_price_type not between", value1, value2, "validatePriceType");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerIsNull() {
            addCriterion("validate_customer is null");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerIsNotNull() {
            addCriterion("validate_customer is not null");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerEqualTo(Boolean value) {
            addCriterion("validate_customer =", value, "validateCustomer");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerNotEqualTo(Boolean value) {
            addCriterion("validate_customer <>", value, "validateCustomer");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerGreaterThan(Boolean value) {
            addCriterion("validate_customer >", value, "validateCustomer");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_customer >=", value, "validateCustomer");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerLessThan(Boolean value) {
            addCriterion("validate_customer <", value, "validateCustomer");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_customer <=", value, "validateCustomer");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerIn(List<Boolean> values) {
            addCriterion("validate_customer in", values, "validateCustomer");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerNotIn(List<Boolean> values) {
            addCriterion("validate_customer not in", values, "validateCustomer");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_customer between", value1, value2, "validateCustomer");
            return (Criteria) this;
        }

        public Criteria andValidateCustomerNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_customer not between", value1, value2, "validateCustomer");
            return (Criteria) this;
        }

        public Criteria andValidateProductIsNull() {
            addCriterion("validate_product is null");
            return (Criteria) this;
        }

        public Criteria andValidateProductIsNotNull() {
            addCriterion("validate_product is not null");
            return (Criteria) this;
        }

        public Criteria andValidateProductEqualTo(Boolean value) {
            addCriterion("validate_product =", value, "validateProduct");
            return (Criteria) this;
        }

        public Criteria andValidateProductNotEqualTo(Boolean value) {
            addCriterion("validate_product <>", value, "validateProduct");
            return (Criteria) this;
        }

        public Criteria andValidateProductGreaterThan(Boolean value) {
            addCriterion("validate_product >", value, "validateProduct");
            return (Criteria) this;
        }

        public Criteria andValidateProductGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_product >=", value, "validateProduct");
            return (Criteria) this;
        }

        public Criteria andValidateProductLessThan(Boolean value) {
            addCriterion("validate_product <", value, "validateProduct");
            return (Criteria) this;
        }

        public Criteria andValidateProductLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_product <=", value, "validateProduct");
            return (Criteria) this;
        }

        public Criteria andValidateProductIn(List<Boolean> values) {
            addCriterion("validate_product in", values, "validateProduct");
            return (Criteria) this;
        }

        public Criteria andValidateProductNotIn(List<Boolean> values) {
            addCriterion("validate_product not in", values, "validateProduct");
            return (Criteria) this;
        }

        public Criteria andValidateProductBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_product between", value1, value2, "validateProduct");
            return (Criteria) this;
        }

        public Criteria andValidateProductNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_product not between", value1, value2, "validateProduct");
            return (Criteria) this;
        }

        public Criteria andValidateOuIsNull() {
            addCriterion("validate_ou is null");
            return (Criteria) this;
        }

        public Criteria andValidateOuIsNotNull() {
            addCriterion("validate_ou is not null");
            return (Criteria) this;
        }

        public Criteria andValidateOuEqualTo(Boolean value) {
            addCriterion("validate_ou =", value, "validateOu");
            return (Criteria) this;
        }

        public Criteria andValidateOuNotEqualTo(Boolean value) {
            addCriterion("validate_ou <>", value, "validateOu");
            return (Criteria) this;
        }

        public Criteria andValidateOuGreaterThan(Boolean value) {
            addCriterion("validate_ou >", value, "validateOu");
            return (Criteria) this;
        }

        public Criteria andValidateOuGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_ou >=", value, "validateOu");
            return (Criteria) this;
        }

        public Criteria andValidateOuLessThan(Boolean value) {
            addCriterion("validate_ou <", value, "validateOu");
            return (Criteria) this;
        }

        public Criteria andValidateOuLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_ou <=", value, "validateOu");
            return (Criteria) this;
        }

        public Criteria andValidateOuIn(List<Boolean> values) {
            addCriterion("validate_ou in", values, "validateOu");
            return (Criteria) this;
        }

        public Criteria andValidateOuNotIn(List<Boolean> values) {
            addCriterion("validate_ou not in", values, "validateOu");
            return (Criteria) this;
        }

        public Criteria andValidateOuBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_ou between", value1, value2, "validateOu");
            return (Criteria) this;
        }

        public Criteria andValidateOuNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_ou not between", value1, value2, "validateOu");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryIsNull() {
            addCriterion("validate_summary is null");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryIsNotNull() {
            addCriterion("validate_summary is not null");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryEqualTo(Boolean value) {
            addCriterion("validate_summary =", value, "validateSummary");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryNotEqualTo(Boolean value) {
            addCriterion("validate_summary <>", value, "validateSummary");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryGreaterThan(Boolean value) {
            addCriterion("validate_summary >", value, "validateSummary");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_summary >=", value, "validateSummary");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryLessThan(Boolean value) {
            addCriterion("validate_summary <", value, "validateSummary");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_summary <=", value, "validateSummary");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryIn(List<Boolean> values) {
            addCriterion("validate_summary in", values, "validateSummary");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryNotIn(List<Boolean> values) {
            addCriterion("validate_summary not in", values, "validateSummary");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_summary between", value1, value2, "validateSummary");
            return (Criteria) this;
        }

        public Criteria andValidateSummaryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_summary not between", value1, value2, "validateSummary");
            return (Criteria) this;
        }

        public Criteria andValidateTypeIsNull() {
            addCriterion("validate_type is null");
            return (Criteria) this;
        }

        public Criteria andValidateTypeIsNotNull() {
            addCriterion("validate_type is not null");
            return (Criteria) this;
        }

        public Criteria andValidateTypeEqualTo(Boolean value) {
            addCriterion("validate_type =", value, "validateType");
            return (Criteria) this;
        }

        public Criteria andValidateTypeNotEqualTo(Boolean value) {
            addCriterion("validate_type <>", value, "validateType");
            return (Criteria) this;
        }

        public Criteria andValidateTypeGreaterThan(Boolean value) {
            addCriterion("validate_type >", value, "validateType");
            return (Criteria) this;
        }

        public Criteria andValidateTypeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_type >=", value, "validateType");
            return (Criteria) this;
        }

        public Criteria andValidateTypeLessThan(Boolean value) {
            addCriterion("validate_type <", value, "validateType");
            return (Criteria) this;
        }

        public Criteria andValidateTypeLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_type <=", value, "validateType");
            return (Criteria) this;
        }

        public Criteria andValidateTypeIn(List<Boolean> values) {
            addCriterion("validate_type in", values, "validateType");
            return (Criteria) this;
        }

        public Criteria andValidateTypeNotIn(List<Boolean> values) {
            addCriterion("validate_type not in", values, "validateType");
            return (Criteria) this;
        }

        public Criteria andValidateTypeBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_type between", value1, value2, "validateType");
            return (Criteria) this;
        }

        public Criteria andValidateTypeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_type not between", value1, value2, "validateType");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagIsNull() {
            addCriterion("main_income_flag is null");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagIsNotNull() {
            addCriterion("main_income_flag is not null");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagEqualTo(Boolean value) {
            addCriterion("main_income_flag =", value, "mainIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagNotEqualTo(Boolean value) {
            addCriterion("main_income_flag <>", value, "mainIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagGreaterThan(Boolean value) {
            addCriterion("main_income_flag >", value, "mainIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("main_income_flag >=", value, "mainIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagLessThan(Boolean value) {
            addCriterion("main_income_flag <", value, "mainIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("main_income_flag <=", value, "mainIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagIn(List<Boolean> values) {
            addCriterion("main_income_flag in", values, "mainIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagNotIn(List<Boolean> values) {
            addCriterion("main_income_flag not in", values, "mainIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("main_income_flag between", value1, value2, "mainIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("main_income_flag not between", value1, value2, "mainIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagIsNull() {
            addCriterion("help_income_flag is null");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagIsNotNull() {
            addCriterion("help_income_flag is not null");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagEqualTo(Boolean value) {
            addCriterion("help_income_flag =", value, "helpIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagNotEqualTo(Boolean value) {
            addCriterion("help_income_flag <>", value, "helpIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagGreaterThan(Boolean value) {
            addCriterion("help_income_flag >", value, "helpIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("help_income_flag >=", value, "helpIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagLessThan(Boolean value) {
            addCriterion("help_income_flag <", value, "helpIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("help_income_flag <=", value, "helpIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagIn(List<Boolean> values) {
            addCriterion("help_income_flag in", values, "helpIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagNotIn(List<Boolean> values) {
            addCriterion("help_income_flag not in", values, "helpIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("help_income_flag between", value1, value2, "helpIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("help_income_flag not between", value1, value2, "helpIncomeFlag");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectIsNull() {
            addCriterion("main_income_subject is null");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectIsNotNull() {
            addCriterion("main_income_subject is not null");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectEqualTo(String value) {
            addCriterion("main_income_subject =", value, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectNotEqualTo(String value) {
            addCriterion("main_income_subject <>", value, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectGreaterThan(String value) {
            addCriterion("main_income_subject >", value, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectGreaterThanOrEqualTo(String value) {
            addCriterion("main_income_subject >=", value, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectLessThan(String value) {
            addCriterion("main_income_subject <", value, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectLessThanOrEqualTo(String value) {
            addCriterion("main_income_subject <=", value, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectLike(String value) {
            addCriterion("main_income_subject like", value, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectNotLike(String value) {
            addCriterion("main_income_subject not like", value, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectIn(List<String> values) {
            addCriterion("main_income_subject in", values, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectNotIn(List<String> values) {
            addCriterion("main_income_subject not in", values, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectBetween(String value1, String value2) {
            addCriterion("main_income_subject between", value1, value2, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMainIncomeSubjectNotBetween(String value1, String value2) {
            addCriterion("main_income_subject not between", value1, value2, "mainIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectIsNull() {
            addCriterion("help_income_subject is null");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectIsNotNull() {
            addCriterion("help_income_subject is not null");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectEqualTo(String value) {
            addCriterion("help_income_subject =", value, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectNotEqualTo(String value) {
            addCriterion("help_income_subject <>", value, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectGreaterThan(String value) {
            addCriterion("help_income_subject >", value, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectGreaterThanOrEqualTo(String value) {
            addCriterion("help_income_subject >=", value, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectLessThan(String value) {
            addCriterion("help_income_subject <", value, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectLessThanOrEqualTo(String value) {
            addCriterion("help_income_subject <=", value, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectLike(String value) {
            addCriterion("help_income_subject like", value, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectNotLike(String value) {
            addCriterion("help_income_subject not like", value, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectIn(List<String> values) {
            addCriterion("help_income_subject in", values, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectNotIn(List<String> values) {
            addCriterion("help_income_subject not in", values, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectBetween(String value1, String value2) {
            addCriterion("help_income_subject between", value1, value2, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andHelpIncomeSubjectNotBetween(String value1, String value2) {
            addCriterion("help_income_subject not between", value1, value2, "helpIncomeSubject");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitIsNull() {
            addCriterion("milepost_design_can_submit is null");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitIsNotNull() {
            addCriterion("milepost_design_can_submit is not null");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitEqualTo(Boolean value) {
            addCriterion("milepost_design_can_submit =", value, "milepostDesignCanSubmit");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitNotEqualTo(Boolean value) {
            addCriterion("milepost_design_can_submit <>", value, "milepostDesignCanSubmit");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitGreaterThan(Boolean value) {
            addCriterion("milepost_design_can_submit >", value, "milepostDesignCanSubmit");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitGreaterThanOrEqualTo(Boolean value) {
            addCriterion("milepost_design_can_submit >=", value, "milepostDesignCanSubmit");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitLessThan(Boolean value) {
            addCriterion("milepost_design_can_submit <", value, "milepostDesignCanSubmit");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitLessThanOrEqualTo(Boolean value) {
            addCriterion("milepost_design_can_submit <=", value, "milepostDesignCanSubmit");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitIn(List<Boolean> values) {
            addCriterion("milepost_design_can_submit in", values, "milepostDesignCanSubmit");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitNotIn(List<Boolean> values) {
            addCriterion("milepost_design_can_submit not in", values, "milepostDesignCanSubmit");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitBetween(Boolean value1, Boolean value2) {
            addCriterion("milepost_design_can_submit between", value1, value2, "milepostDesignCanSubmit");
            return (Criteria) this;
        }

        public Criteria andMilepostDesignCanSubmitNotBetween(Boolean value1, Boolean value2) {
            addCriterion("milepost_design_can_submit not between", value1, value2, "milepostDesignCanSubmit");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateIsNull() {
            addCriterion("milestone_base_date is null");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateIsNotNull() {
            addCriterion("milestone_base_date is not null");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateEqualTo(Boolean value) {
            addCriterion("milestone_base_date =", value, "milestoneBaseDate");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateNotEqualTo(Boolean value) {
            addCriterion("milestone_base_date <>", value, "milestoneBaseDate");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateGreaterThan(Boolean value) {
            addCriterion("milestone_base_date >", value, "milestoneBaseDate");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateGreaterThanOrEqualTo(Boolean value) {
            addCriterion("milestone_base_date >=", value, "milestoneBaseDate");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateLessThan(Boolean value) {
            addCriterion("milestone_base_date <", value, "milestoneBaseDate");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateLessThanOrEqualTo(Boolean value) {
            addCriterion("milestone_base_date <=", value, "milestoneBaseDate");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateIn(List<Boolean> values) {
            addCriterion("milestone_base_date in", values, "milestoneBaseDate");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateNotIn(List<Boolean> values) {
            addCriterion("milestone_base_date not in", values, "milestoneBaseDate");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateBetween(Boolean value1, Boolean value2) {
            addCriterion("milestone_base_date between", value1, value2, "milestoneBaseDate");
            return (Criteria) this;
        }

        public Criteria andMilestoneBaseDateNotBetween(Boolean value1, Boolean value2) {
            addCriterion("milestone_base_date not between", value1, value2, "milestoneBaseDate");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctIsNull() {
            addCriterion("project_member_distinct is null");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctIsNotNull() {
            addCriterion("project_member_distinct is not null");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctEqualTo(Boolean value) {
            addCriterion("project_member_distinct =", value, "projectMemberDistinct");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctNotEqualTo(Boolean value) {
            addCriterion("project_member_distinct <>", value, "projectMemberDistinct");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctGreaterThan(Boolean value) {
            addCriterion("project_member_distinct >", value, "projectMemberDistinct");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctGreaterThanOrEqualTo(Boolean value) {
            addCriterion("project_member_distinct >=", value, "projectMemberDistinct");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctLessThan(Boolean value) {
            addCriterion("project_member_distinct <", value, "projectMemberDistinct");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctLessThanOrEqualTo(Boolean value) {
            addCriterion("project_member_distinct <=", value, "projectMemberDistinct");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctIn(List<Boolean> values) {
            addCriterion("project_member_distinct in", values, "projectMemberDistinct");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctNotIn(List<Boolean> values) {
            addCriterion("project_member_distinct not in", values, "projectMemberDistinct");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctBetween(Boolean value1, Boolean value2) {
            addCriterion("project_member_distinct between", value1, value2, "projectMemberDistinct");
            return (Criteria) this;
        }

        public Criteria andProjectMemberDistinctNotBetween(Boolean value1, Boolean value2) {
            addCriterion("project_member_distinct not between", value1, value2, "projectMemberDistinct");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectIsNull() {
            addCriterion("validate_objective_project is null");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectIsNotNull() {
            addCriterion("validate_objective_project is not null");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectEqualTo(Boolean value) {
            addCriterion("validate_objective_project =", value, "validateObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectNotEqualTo(Boolean value) {
            addCriterion("validate_objective_project <>", value, "validateObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectGreaterThan(Boolean value) {
            addCriterion("validate_objective_project >", value, "validateObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_objective_project >=", value, "validateObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectLessThan(Boolean value) {
            addCriterion("validate_objective_project <", value, "validateObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_objective_project <=", value, "validateObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectIn(List<Boolean> values) {
            addCriterion("validate_objective_project in", values, "validateObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectNotIn(List<Boolean> values) {
            addCriterion("validate_objective_project not in", values, "validateObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_objective_project between", value1, value2, "validateObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andValidateObjectiveProjectNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_objective_project not between", value1, value2, "validateObjectiveProject");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelIsNull() {
            addCriterion("validate_project_level is null");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelIsNotNull() {
            addCriterion("validate_project_level is not null");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelEqualTo(Boolean value) {
            addCriterion("validate_project_level =", value, "validateProjectLevel");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelNotEqualTo(Boolean value) {
            addCriterion("validate_project_level <>", value, "validateProjectLevel");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelGreaterThan(Boolean value) {
            addCriterion("validate_project_level >", value, "validateProjectLevel");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_project_level >=", value, "validateProjectLevel");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelLessThan(Boolean value) {
            addCriterion("validate_project_level <", value, "validateProjectLevel");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_project_level <=", value, "validateProjectLevel");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelIn(List<Boolean> values) {
            addCriterion("validate_project_level in", values, "validateProjectLevel");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelNotIn(List<Boolean> values) {
            addCriterion("validate_project_level not in", values, "validateProjectLevel");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_project_level between", value1, value2, "validateProjectLevel");
            return (Criteria) this;
        }

        public Criteria andValidateProjectLevelNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_project_level not between", value1, value2, "validateProjectLevel");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeIsNull() {
            addCriterion("validate_resource_code is null");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeIsNotNull() {
            addCriterion("validate_resource_code is not null");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeEqualTo(Boolean value) {
            addCriterion("validate_resource_code =", value, "validateResourceCode");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeNotEqualTo(Boolean value) {
            addCriterion("validate_resource_code <>", value, "validateResourceCode");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeGreaterThan(Boolean value) {
            addCriterion("validate_resource_code >", value, "validateResourceCode");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_resource_code >=", value, "validateResourceCode");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeLessThan(Boolean value) {
            addCriterion("validate_resource_code <", value, "validateResourceCode");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_resource_code <=", value, "validateResourceCode");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeIn(List<Boolean> values) {
            addCriterion("validate_resource_code in", values, "validateResourceCode");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeNotIn(List<Boolean> values) {
            addCriterion("validate_resource_code not in", values, "validateResourceCode");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_resource_code between", value1, value2, "validateResourceCode");
            return (Criteria) this;
        }

        public Criteria andValidateResourceCodeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_resource_code not between", value1, value2, "validateResourceCode");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberIsNull() {
            addCriterion("validate_project_member is null");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberIsNotNull() {
            addCriterion("validate_project_member is not null");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberEqualTo(Boolean value) {
            addCriterion("validate_project_member =", value, "validateProjectMember");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberNotEqualTo(Boolean value) {
            addCriterion("validate_project_member <>", value, "validateProjectMember");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberGreaterThan(Boolean value) {
            addCriterion("validate_project_member >", value, "validateProjectMember");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_project_member >=", value, "validateProjectMember");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberLessThan(Boolean value) {
            addCriterion("validate_project_member <", value, "validateProjectMember");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_project_member <=", value, "validateProjectMember");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberIn(List<Boolean> values) {
            addCriterion("validate_project_member in", values, "validateProjectMember");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberNotIn(List<Boolean> values) {
            addCriterion("validate_project_member not in", values, "validateProjectMember");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_project_member between", value1, value2, "validateProjectMember");
            return (Criteria) this;
        }

        public Criteria andValidateProjectMemberNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_project_member not between", value1, value2, "validateProjectMember");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagIsNull() {
            addCriterion("budget_control_flag is null");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagIsNotNull() {
            addCriterion("budget_control_flag is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagEqualTo(Integer value) {
            addCriterion("budget_control_flag =", value, "budgetControlFlag");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagNotEqualTo(Integer value) {
            addCriterion("budget_control_flag <>", value, "budgetControlFlag");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagGreaterThan(Integer value) {
            addCriterion("budget_control_flag >", value, "budgetControlFlag");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("budget_control_flag >=", value, "budgetControlFlag");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagLessThan(Integer value) {
            addCriterion("budget_control_flag <", value, "budgetControlFlag");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagLessThanOrEqualTo(Integer value) {
            addCriterion("budget_control_flag <=", value, "budgetControlFlag");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagIn(List<Integer> values) {
            addCriterion("budget_control_flag in", values, "budgetControlFlag");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagNotIn(List<Integer> values) {
            addCriterion("budget_control_flag not in", values, "budgetControlFlag");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagBetween(Integer value1, Integer value2) {
            addCriterion("budget_control_flag between", value1, value2, "budgetControlFlag");
            return (Criteria) this;
        }

        public Criteria andBudgetControlFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("budget_control_flag not between", value1, value2, "budgetControlFlag");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioIsNull() {
            addCriterion("budget_control_ratio is null");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioIsNotNull() {
            addCriterion("budget_control_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioEqualTo(BigDecimal value) {
            addCriterion("budget_control_ratio =", value, "budgetControlRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioNotEqualTo(BigDecimal value) {
            addCriterion("budget_control_ratio <>", value, "budgetControlRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioGreaterThan(BigDecimal value) {
            addCriterion("budget_control_ratio >", value, "budgetControlRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_control_ratio >=", value, "budgetControlRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioLessThan(BigDecimal value) {
            addCriterion("budget_control_ratio <", value, "budgetControlRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioLessThanOrEqualTo(BigDecimal value) {
            addCriterion("budget_control_ratio <=", value, "budgetControlRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioIn(List<BigDecimal> values) {
            addCriterion("budget_control_ratio in", values, "budgetControlRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioNotIn(List<BigDecimal> values) {
            addCriterion("budget_control_ratio not in", values, "budgetControlRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_control_ratio between", value1, value2, "budgetControlRatio");
            return (Criteria) this;
        }

        public Criteria andBudgetControlRatioNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("budget_control_ratio not between", value1, value2, "budgetControlRatio");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualIsNull() {
            addCriterion("validate_project_manual is null");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualIsNotNull() {
            addCriterion("validate_project_manual is not null");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualEqualTo(Boolean value) {
            addCriterion("validate_project_manual =", value, "validateProjectManual");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualNotEqualTo(Boolean value) {
            addCriterion("validate_project_manual <>", value, "validateProjectManual");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualGreaterThan(Boolean value) {
            addCriterion("validate_project_manual >", value, "validateProjectManual");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualGreaterThanOrEqualTo(Boolean value) {
            addCriterion("validate_project_manual >=", value, "validateProjectManual");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualLessThan(Boolean value) {
            addCriterion("validate_project_manual <", value, "validateProjectManual");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualLessThanOrEqualTo(Boolean value) {
            addCriterion("validate_project_manual <=", value, "validateProjectManual");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualIn(List<Boolean> values) {
            addCriterion("validate_project_manual in", values, "validateProjectManual");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualNotIn(List<Boolean> values) {
            addCriterion("validate_project_manual not in", values, "validateProjectManual");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_project_manual between", value1, value2, "validateProjectManual");
            return (Criteria) this;
        }

        public Criteria andValidateProjectManualNotBetween(Boolean value1, Boolean value2) {
            addCriterion("validate_project_manual not between", value1, value2, "validateProjectManual");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpIsNull() {
            addCriterion("requriement_deliver_mrp is null");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpIsNotNull() {
            addCriterion("requriement_deliver_mrp is not null");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpEqualTo(Boolean value) {
            addCriterion("requriement_deliver_mrp =", value, "requriementDeliverMrp");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpNotEqualTo(Boolean value) {
            addCriterion("requriement_deliver_mrp <>", value, "requriementDeliverMrp");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpGreaterThan(Boolean value) {
            addCriterion("requriement_deliver_mrp >", value, "requriementDeliverMrp");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpGreaterThanOrEqualTo(Boolean value) {
            addCriterion("requriement_deliver_mrp >=", value, "requriementDeliverMrp");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpLessThan(Boolean value) {
            addCriterion("requriement_deliver_mrp <", value, "requriementDeliverMrp");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpLessThanOrEqualTo(Boolean value) {
            addCriterion("requriement_deliver_mrp <=", value, "requriementDeliverMrp");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpIn(List<Boolean> values) {
            addCriterion("requriement_deliver_mrp in", values, "requriementDeliverMrp");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpNotIn(List<Boolean> values) {
            addCriterion("requriement_deliver_mrp not in", values, "requriementDeliverMrp");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpBetween(Boolean value1, Boolean value2) {
            addCriterion("requriement_deliver_mrp between", value1, value2, "requriementDeliverMrp");
            return (Criteria) this;
        }

        public Criteria andRequriementDeliverMrpNotBetween(Boolean value1, Boolean value2) {
            addCriterion("requriement_deliver_mrp not between", value1, value2, "requriementDeliverMrp");
            return (Criteria) this;
        }

        public Criteria andTransferProjectIsNull() {
            addCriterion("transfer_project is null");
            return (Criteria) this;
        }

        public Criteria andTransferProjectIsNotNull() {
            addCriterion("transfer_project is not null");
            return (Criteria) this;
        }

        public Criteria andTransferProjectEqualTo(Boolean value) {
            addCriterion("transfer_project =", value, "transferProject");
            return (Criteria) this;
        }

        public Criteria andTransferProjectNotEqualTo(Boolean value) {
            addCriterion("transfer_project <>", value, "transferProject");
            return (Criteria) this;
        }

        public Criteria andTransferProjectGreaterThan(Boolean value) {
            addCriterion("transfer_project >", value, "transferProject");
            return (Criteria) this;
        }

        public Criteria andTransferProjectGreaterThanOrEqualTo(Boolean value) {
            addCriterion("transfer_project >=", value, "transferProject");
            return (Criteria) this;
        }

        public Criteria andTransferProjectLessThan(Boolean value) {
            addCriterion("transfer_project <", value, "transferProject");
            return (Criteria) this;
        }

        public Criteria andTransferProjectLessThanOrEqualTo(Boolean value) {
            addCriterion("transfer_project <=", value, "transferProject");
            return (Criteria) this;
        }

        public Criteria andTransferProjectIn(List<Boolean> values) {
            addCriterion("transfer_project in", values, "transferProject");
            return (Criteria) this;
        }

        public Criteria andTransferProjectNotIn(List<Boolean> values) {
            addCriterion("transfer_project not in", values, "transferProject");
            return (Criteria) this;
        }

        public Criteria andTransferProjectBetween(Boolean value1, Boolean value2) {
            addCriterion("transfer_project between", value1, value2, "transferProject");
            return (Criteria) this;
        }

        public Criteria andTransferProjectNotBetween(Boolean value1, Boolean value2) {
            addCriterion("transfer_project not between", value1, value2, "transferProject");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdIsNull() {
            addCriterion("transfer_project_type_id is null");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdIsNotNull() {
            addCriterion("transfer_project_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdEqualTo(Long value) {
            addCriterion("transfer_project_type_id =", value, "transferProjectTypeId");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdNotEqualTo(Long value) {
            addCriterion("transfer_project_type_id <>", value, "transferProjectTypeId");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdGreaterThan(Long value) {
            addCriterion("transfer_project_type_id >", value, "transferProjectTypeId");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("transfer_project_type_id >=", value, "transferProjectTypeId");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdLessThan(Long value) {
            addCriterion("transfer_project_type_id <", value, "transferProjectTypeId");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("transfer_project_type_id <=", value, "transferProjectTypeId");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdIn(List<Long> values) {
            addCriterion("transfer_project_type_id in", values, "transferProjectTypeId");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdNotIn(List<Long> values) {
            addCriterion("transfer_project_type_id not in", values, "transferProjectTypeId");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdBetween(Long value1, Long value2) {
            addCriterion("transfer_project_type_id between", value1, value2, "transferProjectTypeId");
            return (Criteria) this;
        }

        public Criteria andTransferProjectTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("transfer_project_type_id not between", value1, value2, "transferProjectTypeId");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateIsNull() {
            addCriterion("project_profit_contribution_rate is null");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateIsNotNull() {
            addCriterion("project_profit_contribution_rate is not null");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateEqualTo(Boolean value) {
            addCriterion("project_profit_contribution_rate =", value, "projectProfitContributionRate");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateNotEqualTo(Boolean value) {
            addCriterion("project_profit_contribution_rate <>", value, "projectProfitContributionRate");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateGreaterThan(Boolean value) {
            addCriterion("project_profit_contribution_rate >", value, "projectProfitContributionRate");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateGreaterThanOrEqualTo(Boolean value) {
            addCriterion("project_profit_contribution_rate >=", value, "projectProfitContributionRate");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateLessThan(Boolean value) {
            addCriterion("project_profit_contribution_rate <", value, "projectProfitContributionRate");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateLessThanOrEqualTo(Boolean value) {
            addCriterion("project_profit_contribution_rate <=", value, "projectProfitContributionRate");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateIn(List<Boolean> values) {
            addCriterion("project_profit_contribution_rate in", values, "projectProfitContributionRate");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateNotIn(List<Boolean> values) {
            addCriterion("project_profit_contribution_rate not in", values, "projectProfitContributionRate");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateBetween(Boolean value1, Boolean value2) {
            addCriterion("project_profit_contribution_rate between", value1, value2, "projectProfitContributionRate");
            return (Criteria) this;
        }

        public Criteria andProjectProfitContributionRateNotBetween(Boolean value1, Boolean value2) {
            addCriterion("project_profit_contribution_rate not between", value1, value2, "projectProfitContributionRate");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeIsNull() {
            addCriterion("purchase_contract_type is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeIsNotNull() {
            addCriterion("purchase_contract_type is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeEqualTo(String value) {
            addCriterion("purchase_contract_type =", value, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeNotEqualTo(String value) {
            addCriterion("purchase_contract_type <>", value, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeGreaterThan(String value) {
            addCriterion("purchase_contract_type >", value, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeGreaterThanOrEqualTo(String value) {
            addCriterion("purchase_contract_type >=", value, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeLessThan(String value) {
            addCriterion("purchase_contract_type <", value, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeLessThanOrEqualTo(String value) {
            addCriterion("purchase_contract_type <=", value, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeLike(String value) {
            addCriterion("purchase_contract_type like", value, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeNotLike(String value) {
            addCriterion("purchase_contract_type not like", value, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeIn(List<String> values) {
            addCriterion("purchase_contract_type in", values, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeNotIn(List<String> values) {
            addCriterion("purchase_contract_type not in", values, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeBetween(String value1, String value2) {
            addCriterion("purchase_contract_type between", value1, value2, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andPurchaseContractTypeNotBetween(String value1, String value2) {
            addCriterion("purchase_contract_type not between", value1, value2, "purchaseContractType");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledIsNull() {
            addCriterion("wbs_enabled is null");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledIsNotNull() {
            addCriterion("wbs_enabled is not null");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledEqualTo(Boolean value) {
            addCriterion("wbs_enabled =", value, "wbsEnabled");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledNotEqualTo(Boolean value) {
            addCriterion("wbs_enabled <>", value, "wbsEnabled");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledGreaterThan(Boolean value) {
            addCriterion("wbs_enabled >", value, "wbsEnabled");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledGreaterThanOrEqualTo(Boolean value) {
            addCriterion("wbs_enabled >=", value, "wbsEnabled");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledLessThan(Boolean value) {
            addCriterion("wbs_enabled <", value, "wbsEnabled");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledLessThanOrEqualTo(Boolean value) {
            addCriterion("wbs_enabled <=", value, "wbsEnabled");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledIn(List<Boolean> values) {
            addCriterion("wbs_enabled in", values, "wbsEnabled");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledNotIn(List<Boolean> values) {
            addCriterion("wbs_enabled not in", values, "wbsEnabled");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledBetween(Boolean value1, Boolean value2) {
            addCriterion("wbs_enabled between", value1, value2, "wbsEnabled");
            return (Criteria) this;
        }

        public Criteria andWbsEnabledNotBetween(Boolean value1, Boolean value2) {
            addCriterion("wbs_enabled not between", value1, value2, "wbsEnabled");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdIsNull() {
            addCriterion("wbs_template_info_id is null");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdIsNotNull() {
            addCriterion("wbs_template_info_id is not null");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdEqualTo(Long value) {
            addCriterion("wbs_template_info_id =", value, "wbsTemplateInfoId");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdNotEqualTo(Long value) {
            addCriterion("wbs_template_info_id <>", value, "wbsTemplateInfoId");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdGreaterThan(Long value) {
            addCriterion("wbs_template_info_id >", value, "wbsTemplateInfoId");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdGreaterThanOrEqualTo(Long value) {
            addCriterion("wbs_template_info_id >=", value, "wbsTemplateInfoId");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdLessThan(Long value) {
            addCriterion("wbs_template_info_id <", value, "wbsTemplateInfoId");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdLessThanOrEqualTo(Long value) {
            addCriterion("wbs_template_info_id <=", value, "wbsTemplateInfoId");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdIn(List<Long> values) {
            addCriterion("wbs_template_info_id in", values, "wbsTemplateInfoId");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdNotIn(List<Long> values) {
            addCriterion("wbs_template_info_id not in", values, "wbsTemplateInfoId");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdBetween(Long value1, Long value2) {
            addCriterion("wbs_template_info_id between", value1, value2, "wbsTemplateInfoId");
            return (Criteria) this;
        }

        public Criteria andWbsTemplateInfoIdNotBetween(Long value1, Long value2) {
            addCriterion("wbs_template_info_id not between", value1, value2, "wbsTemplateInfoId");
            return (Criteria) this;
        }

        public Criteria andAreaIsNull() {
            addCriterion("area is null");
            return (Criteria) this;
        }

        public Criteria andAreaIsNotNull() {
            addCriterion("area is not null");
            return (Criteria) this;
        }

        public Criteria andAreaEqualTo(String value) {
            addCriterion("area =", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotEqualTo(String value) {
            addCriterion("area <>", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThan(String value) {
            addCriterion("area >", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThanOrEqualTo(String value) {
            addCriterion("area >=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThan(String value) {
            addCriterion("area <", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThanOrEqualTo(String value) {
            addCriterion("area <=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLike(String value) {
            addCriterion("area like", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotLike(String value) {
            addCriterion("area not like", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaIn(List<String> values) {
            addCriterion("area in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotIn(List<String> values) {
            addCriterion("area not in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaBetween(String value1, String value2) {
            addCriterion("area between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotBetween(String value1, String value2) {
            addCriterion("area not between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentIsNull() {
            addCriterion("business_department is null");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentIsNotNull() {
            addCriterion("business_department is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentEqualTo(String value) {
            addCriterion("business_department =", value, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentNotEqualTo(String value) {
            addCriterion("business_department <>", value, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentGreaterThan(String value) {
            addCriterion("business_department >", value, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("business_department >=", value, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentLessThan(String value) {
            addCriterion("business_department <", value, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentLessThanOrEqualTo(String value) {
            addCriterion("business_department <=", value, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentLike(String value) {
            addCriterion("business_department like", value, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentNotLike(String value) {
            addCriterion("business_department not like", value, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentIn(List<String> values) {
            addCriterion("business_department in", values, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentNotIn(List<String> values) {
            addCriterion("business_department not in", values, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentBetween(String value1, String value2) {
            addCriterion("business_department between", value1, value2, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andBusinessDepartmentNotBetween(String value1, String value2) {
            addCriterion("business_department not between", value1, value2, "businessDepartment");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressIsNull() {
            addCriterion("detailed_address is null");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressIsNotNull() {
            addCriterion("detailed_address is not null");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressEqualTo(String value) {
            addCriterion("detailed_address =", value, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressNotEqualTo(String value) {
            addCriterion("detailed_address <>", value, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressGreaterThan(String value) {
            addCriterion("detailed_address >", value, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressGreaterThanOrEqualTo(String value) {
            addCriterion("detailed_address >=", value, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressLessThan(String value) {
            addCriterion("detailed_address <", value, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressLessThanOrEqualTo(String value) {
            addCriterion("detailed_address <=", value, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressLike(String value) {
            addCriterion("detailed_address like", value, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressNotLike(String value) {
            addCriterion("detailed_address not like", value, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressIn(List<String> values) {
            addCriterion("detailed_address in", values, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressNotIn(List<String> values) {
            addCriterion("detailed_address not in", values, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressBetween(String value1, String value2) {
            addCriterion("detailed_address between", value1, value2, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andDetailedAddressNotBetween(String value1, String value2) {
            addCriterion("detailed_address not between", value1, value2, "detailedAddress");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andRelateAssetIsNull() {
            addCriterion("relate_asset is null");
            return (Criteria) this;
        }

        public Criteria andRelateAssetIsNotNull() {
            addCriterion("relate_asset is not null");
            return (Criteria) this;
        }

        public Criteria andRelateAssetEqualTo(Boolean value) {
            addCriterion("relate_asset =", value, "relateAsset");
            return (Criteria) this;
        }

        public Criteria andRelateAssetNotEqualTo(Boolean value) {
            addCriterion("relate_asset <>", value, "relateAsset");
            return (Criteria) this;
        }

        public Criteria andRelateAssetGreaterThan(Boolean value) {
            addCriterion("relate_asset >", value, "relateAsset");
            return (Criteria) this;
        }

        public Criteria andRelateAssetGreaterThanOrEqualTo(Boolean value) {
            addCriterion("relate_asset >=", value, "relateAsset");
            return (Criteria) this;
        }

        public Criteria andRelateAssetLessThan(Boolean value) {
            addCriterion("relate_asset <", value, "relateAsset");
            return (Criteria) this;
        }

        public Criteria andRelateAssetLessThanOrEqualTo(Boolean value) {
            addCriterion("relate_asset <=", value, "relateAsset");
            return (Criteria) this;
        }

        public Criteria andRelateAssetIn(List<Boolean> values) {
            addCriterion("relate_asset in", values, "relateAsset");
            return (Criteria) this;
        }

        public Criteria andRelateAssetNotIn(List<Boolean> values) {
            addCriterion("relate_asset not in", values, "relateAsset");
            return (Criteria) this;
        }

        public Criteria andRelateAssetBetween(Boolean value1, Boolean value2) {
            addCriterion("relate_asset between", value1, value2, "relateAsset");
            return (Criteria) this;
        }

        public Criteria andRelateAssetNotBetween(Boolean value1, Boolean value2) {
            addCriterion("relate_asset not between", value1, value2, "relateAsset");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagIsNull() {
            addCriterion("project_progress_predict_flag is null");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagIsNotNull() {
            addCriterion("project_progress_predict_flag is not null");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagEqualTo(Boolean value) {
            addCriterion("project_progress_predict_flag =", value, "projectProgressPredictFlag");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagNotEqualTo(Boolean value) {
            addCriterion("project_progress_predict_flag <>", value, "projectProgressPredictFlag");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagGreaterThan(Boolean value) {
            addCriterion("project_progress_predict_flag >", value, "projectProgressPredictFlag");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("project_progress_predict_flag >=", value, "projectProgressPredictFlag");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagLessThan(Boolean value) {
            addCriterion("project_progress_predict_flag <", value, "projectProgressPredictFlag");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("project_progress_predict_flag <=", value, "projectProgressPredictFlag");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagIn(List<Boolean> values) {
            addCriterion("project_progress_predict_flag in", values, "projectProgressPredictFlag");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagNotIn(List<Boolean> values) {
            addCriterion("project_progress_predict_flag not in", values, "projectProgressPredictFlag");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("project_progress_predict_flag between", value1, value2, "projectProgressPredictFlag");
            return (Criteria) this;
        }

        public Criteria andProjectProgressPredictFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("project_progress_predict_flag not between", value1, value2, "projectProgressPredictFlag");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultIsNull() {
            addCriterion("design_plan_module_status_default is null");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultIsNotNull() {
            addCriterion("design_plan_module_status_default is not null");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultEqualTo(Integer value) {
            addCriterion("design_plan_module_status_default =", value, "designPlanModuleStatusDefault");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultNotEqualTo(Integer value) {
            addCriterion("design_plan_module_status_default <>", value, "designPlanModuleStatusDefault");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultGreaterThan(Integer value) {
            addCriterion("design_plan_module_status_default >", value, "designPlanModuleStatusDefault");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultGreaterThanOrEqualTo(Integer value) {
            addCriterion("design_plan_module_status_default >=", value, "designPlanModuleStatusDefault");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultLessThan(Integer value) {
            addCriterion("design_plan_module_status_default <", value, "designPlanModuleStatusDefault");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultLessThanOrEqualTo(Integer value) {
            addCriterion("design_plan_module_status_default <=", value, "designPlanModuleStatusDefault");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultIn(List<Integer> values) {
            addCriterion("design_plan_module_status_default in", values, "designPlanModuleStatusDefault");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultNotIn(List<Integer> values) {
            addCriterion("design_plan_module_status_default not in", values, "designPlanModuleStatusDefault");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultBetween(Integer value1, Integer value2) {
            addCriterion("design_plan_module_status_default between", value1, value2, "designPlanModuleStatusDefault");
            return (Criteria) this;
        }

        public Criteria andDesignPlanModuleStatusDefaultNotBetween(Integer value1, Integer value2) {
            addCriterion("design_plan_module_status_default not between", value1, value2, "designPlanModuleStatusDefault");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmIsNull() {
            addCriterion("wbs_design_plan_auto_confirm is null");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmIsNotNull() {
            addCriterion("wbs_design_plan_auto_confirm is not null");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmEqualTo(Boolean value) {
            addCriterion("wbs_design_plan_auto_confirm =", value, "wbsDesignPlanAutoConfirm");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmNotEqualTo(Boolean value) {
            addCriterion("wbs_design_plan_auto_confirm <>", value, "wbsDesignPlanAutoConfirm");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmGreaterThan(Boolean value) {
            addCriterion("wbs_design_plan_auto_confirm >", value, "wbsDesignPlanAutoConfirm");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmGreaterThanOrEqualTo(Boolean value) {
            addCriterion("wbs_design_plan_auto_confirm >=", value, "wbsDesignPlanAutoConfirm");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmLessThan(Boolean value) {
            addCriterion("wbs_design_plan_auto_confirm <", value, "wbsDesignPlanAutoConfirm");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmLessThanOrEqualTo(Boolean value) {
            addCriterion("wbs_design_plan_auto_confirm <=", value, "wbsDesignPlanAutoConfirm");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmIn(List<Boolean> values) {
            addCriterion("wbs_design_plan_auto_confirm in", values, "wbsDesignPlanAutoConfirm");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmNotIn(List<Boolean> values) {
            addCriterion("wbs_design_plan_auto_confirm not in", values, "wbsDesignPlanAutoConfirm");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmBetween(Boolean value1, Boolean value2) {
            addCriterion("wbs_design_plan_auto_confirm between", value1, value2, "wbsDesignPlanAutoConfirm");
            return (Criteria) this;
        }

        public Criteria andWbsDesignPlanAutoConfirmNotBetween(Boolean value1, Boolean value2) {
            addCriterion("wbs_design_plan_auto_confirm not between", value1, value2, "wbsDesignPlanAutoConfirm");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}