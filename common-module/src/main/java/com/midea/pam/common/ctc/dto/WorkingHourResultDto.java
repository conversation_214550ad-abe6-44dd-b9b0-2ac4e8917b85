package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.WorkingHour;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2019-4-11
 */
@ApiModel(value = "WorkingHourResultDto", description = "工时信息")
public class WorkingHourResultDto extends WorkingHour {

	@ApiModelProperty(value = "用户名")
	private String name;

	@Excel(name ="用户名")
	@ApiModelProperty(value = "用户名")
	private String userName;

	@Excel(name ="用户MIP")
	@ApiModelProperty(value = "用户MIP")
	private String mipName;

	@Excel(name ="用户类型")
	@ApiModelProperty(value = "用户类型")
	private String userType;

	@Excel(name ="HR部门")
	private String hrDepartment;

	@Excel(name ="供应商名称")
	private String vendorName;

	@Excel(name ="业务实体id")
	private Long ouId;

	@Excel(name ="项目编号")
	@ApiModelProperty(value = "项目编号")
	private String projectCode;

	@Excel(name ="项目名")
	@ApiModelProperty(value = "项目名")
	private String projectName;

	@ApiModelProperty(value = "项目经理名")
	private String managerName;

	@ApiModelProperty(value = "项目经理")
	private Long managerId;

	@Excel(name ="价格类型")
	@ApiModelProperty(value = "价格类型：1:内部;2:外部;3:研发")
	private String priceType;

	@Excel(name ="项目创建时间")
	@ApiModelProperty(value = "项目创建时间")
	private Date projectCreateTime;

	@ApiModelProperty(value = "序号")
	private Integer index;

	@ApiModelProperty(value = "是否已结转或入账")
	private Boolean isCollection;

	@ApiModelProperty(value = "工时填报小时数上限")
	private BigDecimal maxHour;

	@ApiModelProperty(value = "当天已申请工时数")
	private BigDecimal totalApplyWorkingHours;

	@ApiModelProperty(value = "当天已审核工时数")
	private BigDecimal totalConfirmWorkingHours;

	@ApiModelProperty(value = "当天已审批通过的工时")
	private BigDecimal totalPass;

	@ApiModelProperty(value = "当天待审批的工时")
	private BigDecimal totalApproval;

	@ApiModelProperty(value = "待审批人名称")
	private String stayApproveUserName;

	@ApiModelProperty(value = "是否允许填报工时")
	private Integer overDateFlag;

	@ApiModelProperty(value = "wbs工时列表")
	private List<WorkingHourResultDto> workingHourResultDtoList;

	@ApiModelProperty(value = "统计年月")
	private String statisticDate;

	private String hardWorking;
	private Long laborCostDetailId;

	private Date applyDateStart;
	private Date applyDateEnd;

	private Boolean isWbs;
	private Boolean isMultiRole;

	public Boolean getWbs() {
		return isWbs;
	}

	public void setWbs(Boolean wbs) {
		isWbs = wbs;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getMipName() {
		return mipName;
	}

	public void setMipName(String mipName) {
		this.mipName = mipName;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getPriceType() {
		return priceType;
	}

	public void setPriceType(String priceType) {
		this.priceType = priceType;
	}

	public Date getProjectCreateTime() {
		return projectCreateTime;
	}

	public void setProjectCreateTime(Date projectCreateTime) {
		this.projectCreateTime = projectCreateTime;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public String getHrDepartment() {
		return hrDepartment;
	}

	public void setHrDepartment(String hrDepartment) {
		this.hrDepartment = hrDepartment;
	}

	public String getVendorName() {
		return vendorName;
	}

	public void setVendorName(String vendorName) {
		this.vendorName = vendorName;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getProjectCode() {
		return projectCode;
	}

	public void setProjectCode(String projectCode) {
		this.projectCode = projectCode;
	}

	public Boolean getIsCollection() {
		return isCollection;
	}

	public void setIsCollection(Boolean isCollection) {
		this.isCollection = isCollection;
	}

	public String getManagerName() {
		return managerName;
	}

	public void setManagerName(String managerName) {
		this.managerName = managerName;
	}

	public Long getManagerId() {
		return managerId;
	}

	public void setManagerId(Long managerId) {
		this.managerId = managerId;
	}

	public Boolean getCollection() {
		return isCollection;
	}

	public void setCollection(Boolean collection) {
		isCollection = collection;
	}

	public BigDecimal getTotalApplyWorkingHours() {
		return totalApplyWorkingHours;
	}

	public void setTotalApplyWorkingHours(BigDecimal totalApplyWorkingHours) {
		this.totalApplyWorkingHours = totalApplyWorkingHours;
	}

	public BigDecimal getTotalConfirmWorkingHours() {
		return totalConfirmWorkingHours;
	}

	public void setTotalConfirmWorkingHours(BigDecimal totalConfirmWorkingHours) {
		this.totalConfirmWorkingHours = totalConfirmWorkingHours;
	}

	public String getStayApproveUserName() {
		return stayApproveUserName;
	}

	public void setStayApproveUserName(String stayApproveUserName) {
		this.stayApproveUserName = stayApproveUserName;
	}

	public BigDecimal getMaxHour() {
		return maxHour;
	}

	public void setMaxHour(BigDecimal maxHour) {
		this.maxHour = maxHour;
	}

	public Integer getOverDateFlag() {
		return overDateFlag;
	}

	public void setOverDateFlag(Integer overDateFlag) {
		this.overDateFlag = overDateFlag;
	}

	public List<WorkingHourResultDto> getWorkingHourResultDtoList() {
		return workingHourResultDtoList;
	}

	public void setWorkingHourResultDtoList(List<WorkingHourResultDto> workingHourResultDtoList) {
		this.workingHourResultDtoList = workingHourResultDtoList;
	}

	public BigDecimal getTotalPass() {
		return totalPass;
	}

	public void setTotalPass(BigDecimal totalPass) {
		this.totalPass = totalPass;
	}

	public BigDecimal getTotalApproval() {
		return totalApproval;
	}

	public void setTotalApproval(BigDecimal totalApproval) {
		this.totalApproval = totalApproval;
	}

	public Boolean getMultiRole() {
		return isMultiRole;
	}

	public void setMultiRole(Boolean multiRole) {
		isMultiRole = multiRole;
	}

	public Long getOuId() {
		return ouId;
	}

	public void setOuId(Long ouId) {
		this.ouId = ouId;
	}

	public String getHardWorking() {
		return hardWorking;
	}

	public void setHardWorking(String hardWorking) {
		this.hardWorking = hardWorking;
	}

	public Long getLaborCostDetailId() {
		return laborCostDetailId;
	}

	public void setLaborCostDetailId(Long laborCostDetailId) {
		this.laborCostDetailId = laborCostDetailId;
	}

	public Date getApplyDateStart() {
		return applyDateStart;
	}

	public void setApplyDateStart(Date applyDateStart) {
		this.applyDateStart = applyDateStart;
	}

	public Date getApplyDateEnd() {
		return applyDateEnd;
	}

	public void setApplyDateEnd(Date applyDateEnd) {
		this.applyDateEnd = applyDateEnd;
	}

	public String getStatisticDate() {
		return statisticDate;
	}

	public void setStatisticDate(String statisticDate) {
		this.statisticDate = statisticDate;
	}
}
