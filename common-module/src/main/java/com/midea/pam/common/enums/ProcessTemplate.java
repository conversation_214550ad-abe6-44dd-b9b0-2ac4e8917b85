package com.midea.pam.common.enums;

public enum ProcessTemplate {
    CUSTOMER_APP("customerApp","客户审批"),
    CUSTOMER_NO_CW_APP("customerNoCwApp","客户审批（跳过财务）"),
    BUSINESS_APP("businessApproval","商机审批"),
    PLAN_APP("planApp","方案审批"),
    PROJECT_APP("projectApp","立项审批"),
    PROJECT_KS_YF_APP("projectKsYfApp","项目立项（昆山研发项目类型）"),
    INVOICE_APPLY("invoiceApply","开票申请"),
    CONTRACT_APP("contractApp","合同审批"),
    QUOTATION_APP("quotationApp","报价单审批"),
    QUOTATION_HJ_APP("quotationHjApp","报价单审批（焊接业务）"),
    QUOTATION_Zz_APP("quotationZzApp","报价单审批（铸造业务）"),
    QUOTATION_Yb_APP("quotationYbApp","报价单审批（一般工业）"),
    QUOTATION_Sf_APP("quotationSfApp","报价单审批（售服）"),
    QUOTATION_Zd_APP("quotationZdApp","报价单审批（自动化内部转包）"),
    PROJECT_DELIVERIES_APP("projectDeliveriesApp","里程碑附件交付审批"),
    WINNING_APP("winningApp","赢单审批"),
    MILEPOST_DESIGN_PLAN_PURCHASE_APP("milepostDesignPlanPurchaseApp","详细设计方案发布审批(采购)"),
    PURCHASE_IN_ADVANCE_APP("purchaseInAdvanceApp","提前采购"),
    MILEPOST_DESIGN_PLAN_NO_PURCHASE_APP("milepostDesignPlanNoPurchaseApp","详细设计方案发布审批(非采购)"),
    MILEPOST_DESIGN_PLAN_CONFIRM_APP("milepostDesignPlanConfirmApp","里程碑详细设计方案确认审批"),
    MATERIAL_GET_APP("materialGetApp","领料审批流程"),
    MATERIAL_RETURN_APP("materialReturnApp","退料审批流程"),
    CUSTOMER_MODIFY_APP("customerModifyApp","客户变更"),
    CUSTOMER_BUSINESS_MODIFY_APP("customerBusinessModifyApp","客户业务信息变更"),
    CUSTOMER_BASE_INFO_MODIFY_APP("customerBaseInfoModifyApp","客户基本信息变更"),
    MATERIAL_TRANSFER_APP("materialTransferApp","子库转移流程"),
    PROJECT_BASE_INFO_CHANGE_APP("projectBaseInfoChangeApp","项目基本信息变更流程"),
    PROJECT_MILEPOST_CHANGE_APP("projectMilepostChangeApp","项目里程碑信息变更流程"),
    PURCHASE_CONTRACT_APP("purchaseContractApp","采购合同审批"),
    PURCHASE_CONTRACT_INFO_CHANGE_APP("purchaseContractInfoChangeApp","采购合同基本信息变更审批"),
    PURCHASE_CONTRACT_DETAIL_CHANGE_APP("purchaseContractDetailChangeApp","采购合同内容变更审批"),
    PURCHASE_CONTRACT_PLAN_CHANGE_APP("purchaseContractPlanChangeApp","采购合同付款计划变更审批"),
    PROJECT_PREVIEW_APP("projectPreviewApp","项目预立项转正"),
    PROFIT_CHANGE_APP("profitChangeApp","收入成本计划变更"),
    MILEPOST_DESIGN_PLAN_CHANGE_APP("milepostDesignPlanChangeApp","里程碑详细设计方案变更审批"),
    PROJECT_BUDGET_CHANGE_APP("projectBudgetChangeApp","项目预算变更"),
    PROJECT_WBS_BUDGET_CHANGE_APP("projectWbsBudgetChangeApp","项目WBS预算变更"),
    REVENUE_COST_ORDER_APP("revenueCostOrderApp","收入成本工单"),
    PAYMENT_APPLY_APP("paymentApplyApp","付款申请单"),
    PRE_PROJECT_APP("preProjectApp","预立项审批"),
    LABOR_COST_REAL_MONTH_APP("laborCostRealMonthApp","月份实际人力费用审批"),
    PROJECT_CLOSE_MILESTONE_APP("projectCloseMilestoneApp","项目结项里程碑交付审批"),
    CONTRACT_BASE_INFO_CHANAGE_APP("contractBaseInfoChanageApp","PAM销售合同基本信息变更"),
    CONTRACT_PRODUCT_CHANAGE_APP("contractProductChanageApp","PAM销售合同服务内容变更"),
    CONTRACT_RECEIPT_PLAN_CHANAGE_APP("contractReceiptPlanChanageApp","PAM销售合同开票回款计划变更"),
    PROJECT_TERMINATE_APP("projectTerminiationApp","项目终止审批"),
    REFUND_APPLY_APP("refundApplyApp","退款申请"),
    BUSSINESS_RISK_STRATEGY("businessRiskStrategyApp","商机风险及策略分析审批"),
    MATERIAL_TYPE_CHANGE("materialChangeTypeApp","物料类型变更审批"),
    TICKET_WORKING_HOUR_IMPORT_APP("ticketWorkingHourImportApp","工单任务工时导入"),
    WBS_WORKING_HOUR_IMPORT_APP("wbsWorkingHourImportApp","WBS工时导入"),
    PAYMENT_APPLY_GB_APP("paymentApplyGBApp","库卡工业服务付款申请（昆山）"),
    PROJECT_REOPEN_APP("projectReopenApp","项目重新打开审批"),
    BUSSINESS_QUOTATION_APP("businessQuotationApp","商机报价单文件审批"),
    WBS_RECEIPTS_APP("wbsReceiptsApp","需求发布审批"),
    HRO_REQUIREMENT_APP("hroRequirementApp","人力点工需求单据审批"),
    PURCHASE_ORDER_APP("purchaseOrderApp","采购订单下达审批"),
    PURCHASE_ORDER_CHANGE_APP("purchaseOrderChangeApp","采购订单变更审批"),
    PROJECT_TRANSFER_QUALITY_APP("projectTransferQualityApp","转质保立项"),
    PROJECT_CONTRACT_CHANGE_APP("projectContractChangeApp","关联合同变更审批"),
    PURCHASE_CONTRACT_PROGRESS_APP("purchaseContractProgressApp","采购合同进度执行审批"),
    HRO_WORKING_HOUR_BILL_APP("hroWorkingHourBillApp","人力点工工时对账单审批"),
    WBS_DESIGN_PLAN_NO_PURCHASE_SUBMIT_APP("wbsDesignPlanNoPurchaseSubmitApp","详细设计发布审批"),
    MDP_CHANGE_THE_CHANGE_APP("mdpChangeTheChangeApp","详细设计变更审批"),
    PAYMENT_INVOICE_APP("paymentInvoiceApp","应付发票审批"),
    BRAND_ADD_APP("brandAddApp","品牌新增"),
    BRAND_CHANGE_APP("brandChangeApp","品牌变更"),
    MATERIAL_ADJUST_ADD_APP("materialAdjustAddApp","物料新增"),
    CUSTOMER_TRANSFER_APP("customerTransferApp","客户间转款"),
    CUSTOMER_TRANSFER_REVERSE_APP("customerTransferReverseApp","客户间转款冲销"),
    PROJECT_BUDGET_TARGET_CHANGE_APP("projectBudgetTargetChangeApp","项目目标成本变更"),
    PROJECT_MANAGER_BATCH_CHANGE_APP("projectManagerBatchChangeApp","项目经理批量变更"),
    PROJECT_FINANCIAL_BATCH_CHANGE_APP("projectFinancialBatchChangeApp","项目财务批量变更"),
    VENDOR_PENALTY_APP("vendorPenaltyApp","供应商罚扣"),
    VENDOR_PENALTY_CHANGE_APP("vendorPenaltyChangeApp","供应商罚扣变更"),
    MATERIAL_PRICE_RECEIPT_APP("materialPriceReceiptApp","物料价格手工单据"),
    MATERIAL_DELIST_APP("materialDelistApp","物料退市"),

    RECEIPT_WORK_ORDER_APP("receiptWorkOrderApp","回款工单"),

    MATERIAL_COST_TRANSFER_APP("materialCostTransferApp","成本转移单"),

    RECEIPTS_REQUIREMENT_CHANGE_APP("receiptsRequirementChangeApp","需求预算变更"),

    PURCHASE_CONTRACT_PUNISHMENT_NEW_APP("purchaseContractPunishmentApp","采购合同罚扣"),
    CONTRACT_TERMINATION_APP("contractTerminationApp","合同终止"),

    BUDGET_BASELINE_CHANGE_APP("budgetBaselineChangeApp","wbs预算基线变更"),
    REVERSE_INVOICE_APP("reverseInvoiceApp","应收红冲审批"),
    GSC_PAYMENT_INVOICE_ISP_APP("gscPaymentInvoiceIspApp","GSC开票申请"),
    GSC_PAYMENT_INVOICE_DETAIL_APP("gscPaymentInvoiceDetailApp","GSC税票申请"),
    STANDARD_TERMS_APP("standardTermsApp","标准条款")

    ;
    /**
     * 代码.
     */
    private String code;
    /**
     * 名称.
     */
    private String name;

    // 构造方法
    private ProcessTemplate(String code, String name) {
        this.name = name;
        this.code = code;
    }

    //覆盖方法
    @Override
    public String toString() {
        return this.code + "_" + this.name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    public static String getValue(final String code) {
        for (ProcessTemplate processTemplate : ProcessTemplate.values()) {
            if (processTemplate.getCode().equals(code)) {
                return processTemplate.getName();
            }
        }
        return null;
    }
}
