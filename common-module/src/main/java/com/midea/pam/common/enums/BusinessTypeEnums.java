package com.midea.pam.common.enums;

import java.util.Objects;

public enum BusinessTypeEnums {
    CREATE_PROJECT_CODE_EMS("PAM-EMS-004", "写入GEMS项目号"),
    CREATE_BUDGET_ITEM("PAM-EMS-006", "预算单元写入"),
    MODIFY_BUDGET_ITEM("PAM-EMS-007", "预算单元调整"),

    CANCEL_THE_CLAIM("PAM-GFP-006", "销售回款认领撤回资金"),
    CREATE_PROJECT_CODE_GFP("PAM-GFP-007", "写入项目号到资金"),
    PURCHASE_ORDER("PAM-ERP-034", "采购订单写入"),
    PURCHASE_ORDER_CHANGE("PAM-ERP-034-1", "采购订单变更写入"),
    PURCHASE_ORDER_FAP("PAM-FAP-ITC-A4","采购订单写入FAP"),
    CREATE_PROJECT_CODE_ERP("PAM-ERP-007", "写入项目到GERP"),
    CREATE_STORAGE_LOCATOR("PAM-ERP-037", "创建子库货位"),
    MATERIAL_CREATE("PAM-ERP-003", "物料主数据创建"),
    MATERIAL_UPDATE("PAM-ERP-003-1", "物料主数据更新"),
    MATERIAL_VALUATION("PAM-ERP-003-2", "物料估价"),
    GET_MATERIAL("PAM-ERP-033-1", "领料erp同步"),
    PAYMENT_INVOICE("PAM-ERP-023", "应付发票写入"),
    PAYMENT_INVOICE_DL("PAM-ERP-023-2", "第三方票据-迪链写入"),
    PAYMENT_INVOICE_THIRD("PAM-ERP-023-2", "第三方票据写入"),
    CANCEL_INVOICE("PAM-ERP-024", "应付发票取消[收款认领场景]"),
    WRITEOFF_INVOICE("PAM-ERP-054", "应收贷项发票核销写入"),
    REFUND_PAYMENT_INVOICE("PAM-ERP-023-1", "退款应付发票写入"),
    PAYMENT_PLAN("PAM-ERP-028", "付款计划写入"),
    PAYMENT_APPLY("PAM-GFP-003", "预付款申请写入"),
    PREPAY("PAM-ERP-025", "预付款核销"),
    CANCEL_PREPAY("PAM-ERP-025-1", "预付款撤销核销"),
    TRANSFER_MATERIAL("PAM-ERP-032", "子库存转移写入"),
    RETURN_MATERIAL("PAM-ERP-033-2", "退料erp同步"),
    RECEIPT_CLAIM("PAM-ERP-019", "收款写入"),
    RECEIPT_CLAIM_CHANGE("PAM-ERP-070", "收款合同分配变更同步"),
    CANCEL_INVALID_PAYMENT_INVOICE("PAM-ERP-071", "应付发票取消[作废发票场景]"),
    NONRELATER_PAYMENT_APPLY("PAM-ERP-072", "写入关联交易台账"),
    APPAYMENTS_PAYMENT_APPLY("PAM-ERP-073", "应付付款（预付款付款,迪链票据接口卡）"),
    INVOICE_RECEIVABLE_DUE_DATE_SYNC("PAM-ERP-074", "应收发票到期日同步到ERP"),
    RECEIPT_CLAIM_REVERSAL("PAM-ERP-020", "收款冲销"),
    RECEIPT_CLAIM_WRITEOFF("PAM-ERP-021", "收款核销"),
    RECEIPT_CLAIM_CANCEL("PAM-ERP-022", "核销撤销"),
    CARRYOVER_INCOME("PAM-ERP-018-1", "收入结转应收发票写入"),
    INVOICE_RECEIVABLE("PAM-ERP-018-2", "应收发票写入"),
    INVOICE_RECEIVABLE_CANCEL("PAM-ERP-018-4", "应收发票金税导入覆盖作废写入"),
    INVOICE_REFUND_APPLY("PAM-ERP-018-3", "退款发票入账"),
    REFUND_APPLY("PAM-GFP-008", "退款申请"),
    CARRYOVER_COST("PAM-ERP-029-1", "成本结转总账日记账写入"),
    WORKING_HOUR_ACCOUNTING("PAM-ERP-029-2", "工时成本工单总账日记账写入"),
    PROJECT_BUDGET_MODIFY_EMS("PAM-EMS-010", "项目预算变更"),
    PROJECT_WBS_BUDGET_MODIFY_EMS("PAM-EMS-011", "项目wbs预算变更"),
    DIFFERENCE_SHARE_ACCOUNT_SYNC_ERP("PAM-ERP-029-3", "差异分摊入账单同步到erp"),
    INVOICE_PRICE_DIFFERENCE_RECORD("PAM-ERP-046", "发票价差明细写入"),
    MATERIAL_UPDATE_DIFFERENCE_RECORD("PAM-ERP-047", "物料成本更新差异写入"),
    MATERIAL_UPDATE_DIFFERENCE_RECORD_01("ERP-PAM-047-01", "物料成本更新差异写入"),
    PURCHASE_PRICE_DIFFERENCE_RECORD("PAM-ERP-045", "采购价差明细写入"),
    SUBJECT_BALANCE_RECORD("PAM-ERP-048", "科目余额写入"),
    CRM_CUSTOMER_ADD("PAM-CRM-001", "客户新增"),
    RDM_RESOURCE_PLAN("PAM-RDM-001", "rdm资源计划"),
    EAM_PURCHASE_INFO("PAM-EAM-001", "eam采购申请"),
    WBS_CODE("PAM-ERP-057", "WBS号写入"),
    CREATE_BUSINESS_CODE_EMS("PAM-EMS-004-1", "写入GEMS商机号"),
    CONTRACT_CLASSIFICATION("PAM-ERP-029-4", "合同资产重分类入账写入"),
    CUSTOMER_TRANSFER("PAM-ERP-072", "客户间转款写入"),
    CUSTOMER_TRANSFER_REVERSE("PAM-ERP-072-1", "客户间转款冲销写入"),
    PURCHASE_CONTRACT_PROGRESS("PAM-ERP-029-5", "采购合同进度记账写入"),
    PURCHASE_CONTRACT_BILL("PAM-ERP-029-6", "采购合同对账单入账写入"),
    POSITIVE_AND_NEGATIVE_RECORD("PAM-ERP-073-1", "正负发票核销"),
    RECEIPT_WORK_ORDER_ACCOUNTING("PAM-ERP-029-7", "回款工单日记账写入"),
    EXCHANGE_ACCOUNT("PAM-ERP-029-8", "汇兑损益入账"),
    ASSET_DEPRN_ACCOUNTING("PAM-ERP-029-9", "资产折旧成本入账"),
    ;


    private String code;
    private String name;

    BusinessTypeEnums(final String code, final String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getValue(final String code) {
        for (BusinessTypeEnums type : BusinessTypeEnums.values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type.getName();
            }
        }
        return null;
    }
}
