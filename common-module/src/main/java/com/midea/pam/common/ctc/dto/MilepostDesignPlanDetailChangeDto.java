package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "MilepostDesignPlanDetailChangeDto", description = "里程碑详细设计方案设计信息变更dto")
@Getter
@Setter
public class MilepostDesignPlanDetailChangeDto extends MilepostDesignPlanDetailChange {

    @ApiModelProperty("设计信息子层")
    private List<MilepostDesignPlanDetailChangeDto> sonDtos;

    /*筛选条件*/

    private Boolean projectBudgetMaterialIdIsNull;

    private boolean importErpCodeIsNotEmpty = false;

    private boolean importFindCurrentMaterialByErpCode = false;

    private boolean importFindMaterialPamCodeIsNotExist = false;

    private Boolean hasConfirmed;

    private Boolean editable;

    @ApiModelProperty("是否是需求发布后")
    private Boolean afterRequirementPublish = false;

    @ApiModelProperty(value = "库存分类")
    private String inventoryType;

    @ApiModelProperty("柔性详设变更前端新增数据的new标识")
    private Boolean isNew;

    @ApiModelProperty("柔性详设变更前端修改数据的update标识")
    private Boolean isUpdate;

    @ApiModelProperty("柔性详设变更前端删除数据的delete标识")
    private Boolean isDelete;

    // 其他组织137补充的数据
    private List<MaterialDto> fillMaterialDtoList;

    @ApiModelProperty("交货时间字符串")
    private String deliveryTimeStr;

    @ApiModelProperty("柔性详设变更前数据_采购需求总数")
    private BigDecimal oldNumber;

    @ApiModelProperty("柔性详设变更前数据_图纸版本号")
    private String oldChartVersion;

    @ApiModelProperty("详设父级id")
    private Long designPlanParentId;

    @ApiModelProperty("新增详设时前端生成的ID")
    private String uid;

    @ApiModelProperty("前端-详设变更-重新编辑-新建的详设变更表ID-用于子级查找父级")
    private String tempId;

    @ApiModelProperty("父级物料描述")
    private String parentMaterielDescr;

}