package com.midea.pam.common.ctc.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectWbsReceiptsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectWbsReceiptsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNull() {
            addCriterion("project_code is null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIsNotNull() {
            addCriterion("project_code is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCodeEqualTo(String value) {
            addCriterion("project_code =", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotEqualTo(String value) {
            addCriterion("project_code <>", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThan(String value) {
            addCriterion("project_code >", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeGreaterThanOrEqualTo(String value) {
            addCriterion("project_code >=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThan(String value) {
            addCriterion("project_code <", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLessThanOrEqualTo(String value) {
            addCriterion("project_code <=", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeLike(String value) {
            addCriterion("project_code like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotLike(String value) {
            addCriterion("project_code not like", value, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeIn(List<String> values) {
            addCriterion("project_code in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotIn(List<String> values) {
            addCriterion("project_code not in", values, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeBetween(String value1, String value2) {
            addCriterion("project_code between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectCodeNotBetween(String value1, String value2) {
            addCriterion("project_code not between", value1, value2, "projectCode");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNull() {
            addCriterion("requirement_code is null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNotNull() {
            addCriterion("requirement_code is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeEqualTo(String value) {
            addCriterion("requirement_code =", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotEqualTo(String value) {
            addCriterion("requirement_code <>", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThan(String value) {
            addCriterion("requirement_code >", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThanOrEqualTo(String value) {
            addCriterion("requirement_code >=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThan(String value) {
            addCriterion("requirement_code <", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThanOrEqualTo(String value) {
            addCriterion("requirement_code <=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLike(String value) {
            addCriterion("requirement_code like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotLike(String value) {
            addCriterion("requirement_code not like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIn(List<String> values) {
            addCriterion("requirement_code in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotIn(List<String> values) {
            addCriterion("requirement_code not in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeBetween(String value1, String value2) {
            addCriterion("requirement_code between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotBetween(String value1, String value2) {
            addCriterion("requirement_code not between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusIsNull() {
            addCriterion("requirement_status is null");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusIsNotNull() {
            addCriterion("requirement_status is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusEqualTo(Integer value) {
            addCriterion("requirement_status =", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusNotEqualTo(Integer value) {
            addCriterion("requirement_status <>", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusGreaterThan(Integer value) {
            addCriterion("requirement_status >", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("requirement_status >=", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusLessThan(Integer value) {
            addCriterion("requirement_status <", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusLessThanOrEqualTo(Integer value) {
            addCriterion("requirement_status <=", value, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusIn(List<Integer> values) {
            addCriterion("requirement_status in", values, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusNotIn(List<Integer> values) {
            addCriterion("requirement_status not in", values, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusBetween(Integer value1, Integer value2) {
            addCriterion("requirement_status between", value1, value2, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("requirement_status not between", value1, value2, "requirementStatus");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIsNull() {
            addCriterion("requirement_type is null");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIsNotNull() {
            addCriterion("requirement_type is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeEqualTo(Integer value) {
            addCriterion("requirement_type =", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotEqualTo(Integer value) {
            addCriterion("requirement_type <>", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeGreaterThan(Integer value) {
            addCriterion("requirement_type >", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("requirement_type >=", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeLessThan(Integer value) {
            addCriterion("requirement_type <", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeLessThanOrEqualTo(Integer value) {
            addCriterion("requirement_type <=", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIn(List<Integer> values) {
            addCriterion("requirement_type in", values, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotIn(List<Integer> values) {
            addCriterion("requirement_type not in", values, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeBetween(Integer value1, Integer value2) {
            addCriterion("requirement_type between", value1, value2, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("requirement_type not between", value1, value2, "requirementType");
            return (Criteria) this;
        }

        public Criteria andConfirmModeIsNull() {
            addCriterion("confirm_mode is null");
            return (Criteria) this;
        }

        public Criteria andConfirmModeIsNotNull() {
            addCriterion("confirm_mode is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmModeEqualTo(Integer value) {
            addCriterion("confirm_mode =", value, "confirmMode");
            return (Criteria) this;
        }

        public Criteria andConfirmModeNotEqualTo(Integer value) {
            addCriterion("confirm_mode <>", value, "confirmMode");
            return (Criteria) this;
        }

        public Criteria andConfirmModeGreaterThan(Integer value) {
            addCriterion("confirm_mode >", value, "confirmMode");
            return (Criteria) this;
        }

        public Criteria andConfirmModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("confirm_mode >=", value, "confirmMode");
            return (Criteria) this;
        }

        public Criteria andConfirmModeLessThan(Integer value) {
            addCriterion("confirm_mode <", value, "confirmMode");
            return (Criteria) this;
        }

        public Criteria andConfirmModeLessThanOrEqualTo(Integer value) {
            addCriterion("confirm_mode <=", value, "confirmMode");
            return (Criteria) this;
        }

        public Criteria andConfirmModeIn(List<Integer> values) {
            addCriterion("confirm_mode in", values, "confirmMode");
            return (Criteria) this;
        }

        public Criteria andConfirmModeNotIn(List<Integer> values) {
            addCriterion("confirm_mode not in", values, "confirmMode");
            return (Criteria) this;
        }

        public Criteria andConfirmModeBetween(Integer value1, Integer value2) {
            addCriterion("confirm_mode between", value1, value2, "confirmMode");
            return (Criteria) this;
        }

        public Criteria andConfirmModeNotBetween(Integer value1, Integer value2) {
            addCriterion("confirm_mode not between", value1, value2, "confirmMode");
            return (Criteria) this;
        }

        public Criteria andHandleByIsNull() {
            addCriterion("handle_by is null");
            return (Criteria) this;
        }

        public Criteria andHandleByIsNotNull() {
            addCriterion("handle_by is not null");
            return (Criteria) this;
        }

        public Criteria andHandleByEqualTo(Long value) {
            addCriterion("handle_by =", value, "handleBy");
            return (Criteria) this;
        }

        public Criteria andHandleByNotEqualTo(Long value) {
            addCriterion("handle_by <>", value, "handleBy");
            return (Criteria) this;
        }

        public Criteria andHandleByGreaterThan(Long value) {
            addCriterion("handle_by >", value, "handleBy");
            return (Criteria) this;
        }

        public Criteria andHandleByGreaterThanOrEqualTo(Long value) {
            addCriterion("handle_by >=", value, "handleBy");
            return (Criteria) this;
        }

        public Criteria andHandleByLessThan(Long value) {
            addCriterion("handle_by <", value, "handleBy");
            return (Criteria) this;
        }

        public Criteria andHandleByLessThanOrEqualTo(Long value) {
            addCriterion("handle_by <=", value, "handleBy");
            return (Criteria) this;
        }

        public Criteria andHandleByIn(List<Long> values) {
            addCriterion("handle_by in", values, "handleBy");
            return (Criteria) this;
        }

        public Criteria andHandleByNotIn(List<Long> values) {
            addCriterion("handle_by not in", values, "handleBy");
            return (Criteria) this;
        }

        public Criteria andHandleByBetween(Long value1, Long value2) {
            addCriterion("handle_by between", value1, value2, "handleBy");
            return (Criteria) this;
        }

        public Criteria andHandleByNotBetween(Long value1, Long value2) {
            addCriterion("handle_by not between", value1, value2, "handleBy");
            return (Criteria) this;
        }

        public Criteria andHandleNameIsNull() {
            addCriterion("handle_name is null");
            return (Criteria) this;
        }

        public Criteria andHandleNameIsNotNull() {
            addCriterion("handle_name is not null");
            return (Criteria) this;
        }

        public Criteria andHandleNameEqualTo(String value) {
            addCriterion("handle_name =", value, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameNotEqualTo(String value) {
            addCriterion("handle_name <>", value, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameGreaterThan(String value) {
            addCriterion("handle_name >", value, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameGreaterThanOrEqualTo(String value) {
            addCriterion("handle_name >=", value, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameLessThan(String value) {
            addCriterion("handle_name <", value, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameLessThanOrEqualTo(String value) {
            addCriterion("handle_name <=", value, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameLike(String value) {
            addCriterion("handle_name like", value, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameNotLike(String value) {
            addCriterion("handle_name not like", value, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameIn(List<String> values) {
            addCriterion("handle_name in", values, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameNotIn(List<String> values) {
            addCriterion("handle_name not in", values, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameBetween(String value1, String value2) {
            addCriterion("handle_name between", value1, value2, "handleName");
            return (Criteria) this;
        }

        public Criteria andHandleNameNotBetween(String value1, String value2) {
            addCriterion("handle_name not between", value1, value2, "handleName");
            return (Criteria) this;
        }

        public Criteria andReasonIsNull() {
            addCriterion("reason is null");
            return (Criteria) this;
        }

        public Criteria andReasonIsNotNull() {
            addCriterion("reason is not null");
            return (Criteria) this;
        }

        public Criteria andReasonEqualTo(String value) {
            addCriterion("reason =", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotEqualTo(String value) {
            addCriterion("reason <>", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThan(String value) {
            addCriterion("reason >", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThanOrEqualTo(String value) {
            addCriterion("reason >=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThan(String value) {
            addCriterion("reason <", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThanOrEqualTo(String value) {
            addCriterion("reason <=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLike(String value) {
            addCriterion("reason like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotLike(String value) {
            addCriterion("reason not like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonIn(List<String> values) {
            addCriterion("reason in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotIn(List<String> values) {
            addCriterion("reason not in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonBetween(String value1, String value2) {
            addCriterion("reason between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotBetween(String value1, String value2) {
            addCriterion("reason not between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andHandleAtIsNull() {
            addCriterion("handle_at is null");
            return (Criteria) this;
        }

        public Criteria andHandleAtIsNotNull() {
            addCriterion("handle_at is not null");
            return (Criteria) this;
        }

        public Criteria andHandleAtEqualTo(Date value) {
            addCriterion("handle_at =", value, "handleAt");
            return (Criteria) this;
        }

        public Criteria andHandleAtNotEqualTo(Date value) {
            addCriterion("handle_at <>", value, "handleAt");
            return (Criteria) this;
        }

        public Criteria andHandleAtGreaterThan(Date value) {
            addCriterion("handle_at >", value, "handleAt");
            return (Criteria) this;
        }

        public Criteria andHandleAtGreaterThanOrEqualTo(Date value) {
            addCriterion("handle_at >=", value, "handleAt");
            return (Criteria) this;
        }

        public Criteria andHandleAtLessThan(Date value) {
            addCriterion("handle_at <", value, "handleAt");
            return (Criteria) this;
        }

        public Criteria andHandleAtLessThanOrEqualTo(Date value) {
            addCriterion("handle_at <=", value, "handleAt");
            return (Criteria) this;
        }

        public Criteria andHandleAtIn(List<Date> values) {
            addCriterion("handle_at in", values, "handleAt");
            return (Criteria) this;
        }

        public Criteria andHandleAtNotIn(List<Date> values) {
            addCriterion("handle_at not in", values, "handleAt");
            return (Criteria) this;
        }

        public Criteria andHandleAtBetween(Date value1, Date value2) {
            addCriterion("handle_at between", value1, value2, "handleAt");
            return (Criteria) this;
        }

        public Criteria andHandleAtNotBetween(Date value1, Date value2) {
            addCriterion("handle_at not between", value1, value2, "handleAt");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIsNull() {
            addCriterion("design_release_lot_number is null");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIsNotNull() {
            addCriterion("design_release_lot_number is not null");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberEqualTo(String value) {
            addCriterion("design_release_lot_number =", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotEqualTo(String value) {
            addCriterion("design_release_lot_number <>", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberGreaterThan(String value) {
            addCriterion("design_release_lot_number >", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberGreaterThanOrEqualTo(String value) {
            addCriterion("design_release_lot_number >=", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLessThan(String value) {
            addCriterion("design_release_lot_number <", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLessThanOrEqualTo(String value) {
            addCriterion("design_release_lot_number <=", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLike(String value) {
            addCriterion("design_release_lot_number like", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotLike(String value) {
            addCriterion("design_release_lot_number not like", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIn(List<String> values) {
            addCriterion("design_release_lot_number in", values, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotIn(List<String> values) {
            addCriterion("design_release_lot_number not in", values, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberBetween(String value1, String value2) {
            addCriterion("design_release_lot_number between", value1, value2, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotBetween(String value1, String value2) {
            addCriterion("design_release_lot_number not between", value1, value2, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andProducerIdIsNull() {
            addCriterion("producer_id is null");
            return (Criteria) this;
        }

        public Criteria andProducerIdIsNotNull() {
            addCriterion("producer_id is not null");
            return (Criteria) this;
        }

        public Criteria andProducerIdEqualTo(Long value) {
            addCriterion("producer_id =", value, "producerId");
            return (Criteria) this;
        }

        public Criteria andProducerIdNotEqualTo(Long value) {
            addCriterion("producer_id <>", value, "producerId");
            return (Criteria) this;
        }

        public Criteria andProducerIdGreaterThan(Long value) {
            addCriterion("producer_id >", value, "producerId");
            return (Criteria) this;
        }

        public Criteria andProducerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("producer_id >=", value, "producerId");
            return (Criteria) this;
        }

        public Criteria andProducerIdLessThan(Long value) {
            addCriterion("producer_id <", value, "producerId");
            return (Criteria) this;
        }

        public Criteria andProducerIdLessThanOrEqualTo(Long value) {
            addCriterion("producer_id <=", value, "producerId");
            return (Criteria) this;
        }

        public Criteria andProducerIdIn(List<Long> values) {
            addCriterion("producer_id in", values, "producerId");
            return (Criteria) this;
        }

        public Criteria andProducerIdNotIn(List<Long> values) {
            addCriterion("producer_id not in", values, "producerId");
            return (Criteria) this;
        }

        public Criteria andProducerIdBetween(Long value1, Long value2) {
            addCriterion("producer_id between", value1, value2, "producerId");
            return (Criteria) this;
        }

        public Criteria andProducerIdNotBetween(Long value1, Long value2) {
            addCriterion("producer_id not between", value1, value2, "producerId");
            return (Criteria) this;
        }

        public Criteria andProducerNameIsNull() {
            addCriterion("producer_name is null");
            return (Criteria) this;
        }

        public Criteria andProducerNameIsNotNull() {
            addCriterion("producer_name is not null");
            return (Criteria) this;
        }

        public Criteria andProducerNameEqualTo(String value) {
            addCriterion("producer_name =", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameNotEqualTo(String value) {
            addCriterion("producer_name <>", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameGreaterThan(String value) {
            addCriterion("producer_name >", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameGreaterThanOrEqualTo(String value) {
            addCriterion("producer_name >=", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameLessThan(String value) {
            addCriterion("producer_name <", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameLessThanOrEqualTo(String value) {
            addCriterion("producer_name <=", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameLike(String value) {
            addCriterion("producer_name like", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameNotLike(String value) {
            addCriterion("producer_name not like", value, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameIn(List<String> values) {
            addCriterion("producer_name in", values, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameNotIn(List<String> values) {
            addCriterion("producer_name not in", values, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameBetween(String value1, String value2) {
            addCriterion("producer_name between", value1, value2, "producerName");
            return (Criteria) this;
        }

        public Criteria andProducerNameNotBetween(String value1, String value2) {
            addCriterion("producer_name not between", value1, value2, "producerName");
            return (Criteria) this;
        }

        public Criteria andProcessNameIsNull() {
            addCriterion("process_name is null");
            return (Criteria) this;
        }

        public Criteria andProcessNameIsNotNull() {
            addCriterion("process_name is not null");
            return (Criteria) this;
        }

        public Criteria andProcessNameEqualTo(String value) {
            addCriterion("process_name =", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameNotEqualTo(String value) {
            addCriterion("process_name <>", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameGreaterThan(String value) {
            addCriterion("process_name >", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameGreaterThanOrEqualTo(String value) {
            addCriterion("process_name >=", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameLessThan(String value) {
            addCriterion("process_name <", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameLessThanOrEqualTo(String value) {
            addCriterion("process_name <=", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameLike(String value) {
            addCriterion("process_name like", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameNotLike(String value) {
            addCriterion("process_name not like", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameIn(List<String> values) {
            addCriterion("process_name in", values, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameNotIn(List<String> values) {
            addCriterion("process_name not in", values, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameBetween(String value1, String value2) {
            addCriterion("process_name between", value1, value2, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameNotBetween(String value1, String value2) {
            addCriterion("process_name not between", value1, value2, "processName");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitIsNull() {
            addCriterion("project_submit is null");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitIsNotNull() {
            addCriterion("project_submit is not null");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitEqualTo(Boolean value) {
            addCriterion("project_submit =", value, "projectSubmit");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitNotEqualTo(Boolean value) {
            addCriterion("project_submit <>", value, "projectSubmit");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitGreaterThan(Boolean value) {
            addCriterion("project_submit >", value, "projectSubmit");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitGreaterThanOrEqualTo(Boolean value) {
            addCriterion("project_submit >=", value, "projectSubmit");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitLessThan(Boolean value) {
            addCriterion("project_submit <", value, "projectSubmit");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitLessThanOrEqualTo(Boolean value) {
            addCriterion("project_submit <=", value, "projectSubmit");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitIn(List<Boolean> values) {
            addCriterion("project_submit in", values, "projectSubmit");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitNotIn(List<Boolean> values) {
            addCriterion("project_submit not in", values, "projectSubmit");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitBetween(Boolean value1, Boolean value2) {
            addCriterion("project_submit between", value1, value2, "projectSubmit");
            return (Criteria) this;
        }

        public Criteria andProjectSubmitNotBetween(Boolean value1, Boolean value2) {
            addCriterion("project_submit not between", value1, value2, "projectSubmit");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Long value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Long value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Long value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Long value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Long value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Long> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Long> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Long value1, Long value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Long value1, Long value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkIsNull() {
            addCriterion("renson_remark is null");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkIsNotNull() {
            addCriterion("renson_remark is not null");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkEqualTo(String value) {
            addCriterion("renson_remark =", value, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkNotEqualTo(String value) {
            addCriterion("renson_remark <>", value, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkGreaterThan(String value) {
            addCriterion("renson_remark >", value, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("renson_remark >=", value, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkLessThan(String value) {
            addCriterion("renson_remark <", value, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkLessThanOrEqualTo(String value) {
            addCriterion("renson_remark <=", value, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkLike(String value) {
            addCriterion("renson_remark like", value, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkNotLike(String value) {
            addCriterion("renson_remark not like", value, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkIn(List<String> values) {
            addCriterion("renson_remark in", values, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkNotIn(List<String> values) {
            addCriterion("renson_remark not in", values, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkBetween(String value1, String value2) {
            addCriterion("renson_remark between", value1, value2, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andRensonRemarkNotBetween(String value1, String value2) {
            addCriterion("renson_remark not between", value1, value2, "rensonRemark");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonIsNull() {
            addCriterion("submit_reason is null");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonIsNotNull() {
            addCriterion("submit_reason is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonEqualTo(String value) {
            addCriterion("submit_reason =", value, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonNotEqualTo(String value) {
            addCriterion("submit_reason <>", value, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonGreaterThan(String value) {
            addCriterion("submit_reason >", value, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonGreaterThanOrEqualTo(String value) {
            addCriterion("submit_reason >=", value, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonLessThan(String value) {
            addCriterion("submit_reason <", value, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonLessThanOrEqualTo(String value) {
            addCriterion("submit_reason <=", value, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonLike(String value) {
            addCriterion("submit_reason like", value, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonNotLike(String value) {
            addCriterion("submit_reason not like", value, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonIn(List<String> values) {
            addCriterion("submit_reason in", values, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonNotIn(List<String> values) {
            addCriterion("submit_reason not in", values, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonBetween(String value1, String value2) {
            addCriterion("submit_reason between", value1, value2, "submitReason");
            return (Criteria) this;
        }

        public Criteria andSubmitReasonNotBetween(String value1, String value2) {
            addCriterion("submit_reason not between", value1, value2, "submitReason");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdIsNull() {
            addCriterion("rel_receipts_id is null");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdIsNotNull() {
            addCriterion("rel_receipts_id is not null");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdEqualTo(Long value) {
            addCriterion("rel_receipts_id =", value, "relReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdNotEqualTo(Long value) {
            addCriterion("rel_receipts_id <>", value, "relReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdGreaterThan(Long value) {
            addCriterion("rel_receipts_id >", value, "relReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rel_receipts_id >=", value, "relReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdLessThan(Long value) {
            addCriterion("rel_receipts_id <", value, "relReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdLessThanOrEqualTo(Long value) {
            addCriterion("rel_receipts_id <=", value, "relReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdIn(List<Long> values) {
            addCriterion("rel_receipts_id in", values, "relReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdNotIn(List<Long> values) {
            addCriterion("rel_receipts_id not in", values, "relReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdBetween(Long value1, Long value2) {
            addCriterion("rel_receipts_id between", value1, value2, "relReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRelReceiptsIdNotBetween(Long value1, Long value2) {
            addCriterion("rel_receipts_id not between", value1, value2, "relReceiptsId");
            return (Criteria) this;
        }

        public Criteria andInitIsNull() {
            addCriterion("init is null");
            return (Criteria) this;
        }

        public Criteria andInitIsNotNull() {
            addCriterion("init is not null");
            return (Criteria) this;
        }

        public Criteria andInitEqualTo(Boolean value) {
            addCriterion("init =", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitNotEqualTo(Boolean value) {
            addCriterion("init <>", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitGreaterThan(Boolean value) {
            addCriterion("init >", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitGreaterThanOrEqualTo(Boolean value) {
            addCriterion("init >=", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitLessThan(Boolean value) {
            addCriterion("init <", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitLessThanOrEqualTo(Boolean value) {
            addCriterion("init <=", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitIn(List<Boolean> values) {
            addCriterion("init in", values, "init");
            return (Criteria) this;
        }

        public Criteria andInitNotIn(List<Boolean> values) {
            addCriterion("init not in", values, "init");
            return (Criteria) this;
        }

        public Criteria andInitBetween(Boolean value1, Boolean value2) {
            addCriterion("init between", value1, value2, "init");
            return (Criteria) this;
        }

        public Criteria andInitNotBetween(Boolean value1, Boolean value2) {
            addCriterion("init not between", value1, value2, "init");
            return (Criteria) this;
        }

        public Criteria andInitSequenceIsNull() {
            addCriterion("init_sequence is null");
            return (Criteria) this;
        }

        public Criteria andInitSequenceIsNotNull() {
            addCriterion("init_sequence is not null");
            return (Criteria) this;
        }

        public Criteria andInitSequenceEqualTo(String value) {
            addCriterion("init_sequence =", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceNotEqualTo(String value) {
            addCriterion("init_sequence <>", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceGreaterThan(String value) {
            addCriterion("init_sequence >", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceGreaterThanOrEqualTo(String value) {
            addCriterion("init_sequence >=", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceLessThan(String value) {
            addCriterion("init_sequence <", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceLessThanOrEqualTo(String value) {
            addCriterion("init_sequence <=", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceLike(String value) {
            addCriterion("init_sequence like", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceNotLike(String value) {
            addCriterion("init_sequence not like", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceIn(List<String> values) {
            addCriterion("init_sequence in", values, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceNotIn(List<String> values) {
            addCriterion("init_sequence not in", values, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceBetween(String value1, String value2) {
            addCriterion("init_sequence between", value1, value2, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceNotBetween(String value1, String value2) {
            addCriterion("init_sequence not between", value1, value2, "initSequence");
            return (Criteria) this;
        }

        public Criteria andBuyerIdIsNull() {
            addCriterion("buyer_id is null");
            return (Criteria) this;
        }

        public Criteria andBuyerIdIsNotNull() {
            addCriterion("buyer_id is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerIdEqualTo(Long value) {
            addCriterion("buyer_id =", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdNotEqualTo(Long value) {
            addCriterion("buyer_id <>", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdGreaterThan(Long value) {
            addCriterion("buyer_id >", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("buyer_id >=", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdLessThan(Long value) {
            addCriterion("buyer_id <", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdLessThanOrEqualTo(Long value) {
            addCriterion("buyer_id <=", value, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdIn(List<Long> values) {
            addCriterion("buyer_id in", values, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdNotIn(List<Long> values) {
            addCriterion("buyer_id not in", values, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdBetween(Long value1, Long value2) {
            addCriterion("buyer_id between", value1, value2, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerIdNotBetween(Long value1, Long value2) {
            addCriterion("buyer_id not between", value1, value2, "buyerId");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIsNull() {
            addCriterion("buyer_name is null");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIsNotNull() {
            addCriterion("buyer_name is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerNameEqualTo(String value) {
            addCriterion("buyer_name =", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotEqualTo(String value) {
            addCriterion("buyer_name <>", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThan(String value) {
            addCriterion("buyer_name >", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_name >=", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThan(String value) {
            addCriterion("buyer_name <", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThanOrEqualTo(String value) {
            addCriterion("buyer_name <=", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLike(String value) {
            addCriterion("buyer_name like", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotLike(String value) {
            addCriterion("buyer_name not like", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIn(List<String> values) {
            addCriterion("buyer_name in", values, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotIn(List<String> values) {
            addCriterion("buyer_name not in", values, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameBetween(String value1, String value2) {
            addCriterion("buyer_name between", value1, value2, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotBetween(String value1, String value2) {
            addCriterion("buyer_name not between", value1, value2, "buyerName");
            return (Criteria) this;
        }

        public Criteria andWebTypeIsNull() {
            addCriterion("web_type is null");
            return (Criteria) this;
        }

        public Criteria andWebTypeIsNotNull() {
            addCriterion("web_type is not null");
            return (Criteria) this;
        }

        public Criteria andWebTypeEqualTo(Integer value) {
            addCriterion("web_type =", value, "webType");
            return (Criteria) this;
        }

        public Criteria andWebTypeNotEqualTo(Integer value) {
            addCriterion("web_type <>", value, "webType");
            return (Criteria) this;
        }

        public Criteria andWebTypeGreaterThan(Integer value) {
            addCriterion("web_type >", value, "webType");
            return (Criteria) this;
        }

        public Criteria andWebTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("web_type >=", value, "webType");
            return (Criteria) this;
        }

        public Criteria andWebTypeLessThan(Integer value) {
            addCriterion("web_type <", value, "webType");
            return (Criteria) this;
        }

        public Criteria andWebTypeLessThanOrEqualTo(Integer value) {
            addCriterion("web_type <=", value, "webType");
            return (Criteria) this;
        }

        public Criteria andWebTypeIn(List<Integer> values) {
            addCriterion("web_type in", values, "webType");
            return (Criteria) this;
        }

        public Criteria andWebTypeNotIn(List<Integer> values) {
            addCriterion("web_type not in", values, "webType");
            return (Criteria) this;
        }

        public Criteria andWebTypeBetween(Integer value1, Integer value2) {
            addCriterion("web_type between", value1, value2, "webType");
            return (Criteria) this;
        }

        public Criteria andWebTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("web_type not between", value1, value2, "webType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}