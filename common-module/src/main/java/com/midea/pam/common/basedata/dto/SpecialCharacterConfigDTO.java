package com.midea.pam.common.basedata.dto;

import com.midea.pam.common.basedata.entity.SpecialCharacterConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;



/**
 * 特殊字符配置DTO
 * 继承实体类，用于接口参数接收和响应
 * 
 * <AUTHOR> Team
 */
@ApiModel(value = "特殊字符配置DTO")
public class SpecialCharacterConfigDTO extends SpecialCharacterConfig {

    private static final long serialVersionUID = 1L;

    // ==================== 分页参数 ====================
    @ApiModelProperty(value = "页码，默认1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小，默认20")
    private Integer pageSize = 20;

    // ==================== 查询条件 ====================
    @ApiModelProperty(value = "配置名称（模糊查询）")
    private String configNameLike;

    @ApiModelProperty(value = "数据库名称（精确查询）")
    private String databaseNameQuery;

    @ApiModelProperty(value = "表名称（模糊查询）")
    private String tableNameLike;

    @ApiModelProperty(value = "字段名称（模糊查询）")
    private String columnNameLike;

    @ApiModelProperty(value = "启用状态查询")
    private Boolean enabledQuery;

    @ApiModelProperty(value = "处理规则查询")
    private String processingRuleQuery;

    // ==================== 扩展字段 ====================
    @ApiModelProperty(value = "处理规则描述（只读）")
    private String processingRuleDesc;

    @ApiModelProperty(value = "创建人姓名（只读）")
    private String createUserName;

    @ApiModelProperty(value = "更新人姓名（只读）")
    private String updateUserName;

    // ==================== Getter/Setter ====================
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getConfigNameLike() {
        return configNameLike;
    }

    public void setConfigNameLike(String configNameLike) {
        this.configNameLike = configNameLike;
    }

    public String getDatabaseNameQuery() {
        return databaseNameQuery;
    }

    public void setDatabaseNameQuery(String databaseNameQuery) {
        this.databaseNameQuery = databaseNameQuery;
    }

    public String getTableNameLike() {
        return tableNameLike;
    }

    public void setTableNameLike(String tableNameLike) {
        this.tableNameLike = tableNameLike;
    }

    public String getColumnNameLike() {
        return columnNameLike;
    }

    public void setColumnNameLike(String columnNameLike) {
        this.columnNameLike = columnNameLike;
    }

    public Boolean getEnabledQuery() {
        return enabledQuery;
    }

    public void setEnabledQuery(Boolean enabledQuery) {
        this.enabledQuery = enabledQuery;
    }

    public String getProcessingRuleQuery() {
        return processingRuleQuery;
    }

    public void setProcessingRuleQuery(String processingRuleQuery) {
        this.processingRuleQuery = processingRuleQuery;
    }

    public String getProcessingRuleDesc() {
        return processingRuleDesc;
    }

    public void setProcessingRuleDesc(String processingRuleDesc) {
        this.processingRuleDesc = processingRuleDesc;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }
}
