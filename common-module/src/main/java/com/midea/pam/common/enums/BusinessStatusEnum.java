package com.midea.pam.common.enums;

public enum BusinessStatusEnum  {
    WAIT_ACTIVE(-1, "审批中"),
    DISABLE(0, "失效"),
    ENABLE(1, "生效"), // 这时前端显示跟进中
    WIN_CONTRACT(3, "赢单"),
    REJECT(4,"驳回"),
    DRAFT(5,"草稿"),
    ABANDON(6,"作废"),
    ;

    private BusinessStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;
    private String msg;


    public Integer getCode() {
        return code;
    }

    public String getName() {
        return msg;
    }
}