package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.util.excel4j.ExcelDate2DateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PurchaseOrderImportVO {

    private Long id;

    @NotNull(message = "采购订单号不能为空")
    @ExcelField(title = "采购订单号")
    private String num;

    @ExcelField(title = "项目编号")
    private String projectCode;

    private Long ouId;

    private Long orgId;

    @NotNull(message = "业务实体不能为空")
    @ExcelField(title = "业务实体")
    private String orgName;

    @NotNull(message = "定价类型不能为空")
    @ExcelField(title = "定价类型")
    private String pricingTypeStr;

    private Integer syncStatus;

    private Integer pricingType;

    @NotNull(message = "是否急件不能为空")
    @ExcelField(title = "是否急件")
    private String dispatchIsStr;

    private Boolean dispatchIs;

    @NotNull(message = "采购员不能为空")
    @ExcelField(title = "采购员")
    private Long buyerId;

    private String buyerName;

    private String approveInfo;

    private Long vendorAslId;

    @NotNull(message = "供应商编码不能为空")
    @ExcelField(title = "供应商编码")
    private String vendorNum;

    @NotNull(message = "供应商名称不能为空")
    @ExcelField(title = "供应商名称")
    private String vendorName;

    @NotNull(message = "供应商地点不能为空")
    @ExcelField(title = "供应商地点")
    private String vendorSiteCode;

    @NotNull(message = "币种不能为空")
    @ExcelField(title = "币种")
    private String currencyCode;

    @NotNull(message = "汇率类型不能为空")
    @ExcelField(title = "汇率类型")
    private String conversionType;

    @NotNull(message = "汇率日期不能为空")
    @ExcelField(title = "汇率日期", readConverter = ExcelDate2DateConverter.class)
    private Date conversionDate;

    @NotNull(message = "汇率不能为空")
    @ExcelField(title = "汇率")
    private BigDecimal conversionRate;

    @ExcelField(title = "承诺日期", readConverter = ExcelDate2DateConverter.class)
    private Date promisedDate;

    @ExcelField(title = "跟踪日期", readConverter = ExcelDate2DateConverter.class)
    private Date trackingDate;

    @ExcelField(title = "付款方式")
    private String paymentMethodName;

    @ExcelField(title = "付款方法")
    private String paymentWay;

    @ExcelField(title = "交货方式")
    private String deliveryType;

    @ExcelField(title = "交货条款")
    private String deliveryClause;

    @ExcelField(title = "税率")
    private String taxRate;

    @NotNull(message = "备注不能为空")
    @ExcelField(title = "备注")
    private String remark;

    @NotNull(message = "合同条款不能为空")
    @ExcelField(title = "合同条款")
    private String contractTerms;

    @NotNull(message = "接收子库不能为空")
    @ExcelField(title = "接收子库")
    private String secondaryInventory;

    @NotNull(message = "接收子库代码不能为空")
    @ExcelField(title = "接收子库代码")
    private String secondaryInventoryName;

    private String errMsg;
}
