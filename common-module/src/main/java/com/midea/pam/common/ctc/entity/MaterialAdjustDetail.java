package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "物料管理行信息")
public class MaterialAdjustDetail extends LongIdEntity implements Serializable {
    private Long headerId;

    @ApiModelProperty(value = "pam物料编码")
    private String pamCode;

    @ApiModelProperty(value = "erp物料编码")
    private String erpCode;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "物料描述")
    private String itemDes;

    @ApiModelProperty(value = "单位中文")
    private String unit;

    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    @ApiModelProperty(value = "型号/规格")
    private String model;

    @ApiModelProperty(value = "物料大类id")
    private Long materialClassId;

    @ApiModelProperty(value = "物料大类")
    private String materialClass;

    @ApiModelProperty(value = "物料中类id")
    private Long materialMiddleClassId;

    @ApiModelProperty(value = "物料中类")
    private String materialMiddleClass;

    @ApiModelProperty(value = "物料小类id")
    private Long materialSmallClassId;

    @ApiModelProperty(value = "物料小类")
    private String materialSmallClass;

    @ApiModelProperty(value = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料类型(新)")
    private String materialTypeNew;

    @ApiModelProperty(value = "物料id(关联物料表)")
    private Long materialId;

    @ApiModelProperty(value = "图号")
    private String figureNumber;

    @ApiModelProperty(value = "图纸版本号")
    private String chartVersion;

    @ApiModelProperty(value = "图纸版本号（新）")
    private String chartVersionNew;

    @ApiModelProperty(value = "单件重量（kg）")
    private BigDecimal unitWeight;

    @ApiModelProperty(value = "单位重量（kg 新）")
    private BigDecimal unitWeightNew;

    @ApiModelProperty(value = "加工件分类")
    private String machiningPartType;

    @ApiModelProperty(value = "加工件分类（新）")
    private String machiningPartTypeNew;

    @ApiModelProperty(value = "表面处理")
    private String surfaceHandle;

    @ApiModelProperty(value = "表面处理（新）")
    private String surfaceHandleNew;

    @ApiModelProperty(value = "材质")
    private String material;

    @ApiModelProperty(value = "材质（新）")
    private String materialNew;

    @ApiModelProperty(value = "是否备件标识（是/否）")
    private String orSparePartsMask;

    @ApiModelProperty(value = "是否备件标识（新）")
    private String orSparePartsMaskNew;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "品牌商物料编码")
    private String brandMaterialCode;

    @ApiModelProperty(value = "提前采购期")
    private Long perchasingLeadtime;

    @ApiModelProperty(value = "最小采购量")
    private Long minPerchaseQuantity;

    @ApiModelProperty(value = "最小包装量")
    private Long minPackageQuantity;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "ERP同步状态: 0-待同步, 1-同步成功, 2-同步异常, 3-同步中")
    private Integer syncStatus;

    @ApiModelProperty(value = "ERP失败原因")
    private String syncMes;

    private String attribute1;

    private String attribute2;

    private String attribute3;

    private Boolean deletedFlag;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "物料属性")
    private String materialAttribute;

    @ApiModelProperty(value = "库存分类")
    private String inventoryType;

    private static final long serialVersionUID = 1L;

    public Long getHeaderId() {
        return headerId;
    }

    public void setHeaderId(Long headerId) {
        this.headerId = headerId;
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getItemDes() {
        return itemDes;
    }

    public void setItemDes(String itemDes) {
        this.itemDes = itemDes == null ? null : itemDes.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode == null ? null : unitCode.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public Long getMaterialClassId() {
        return materialClassId;
    }

    public void setMaterialClassId(Long materialClassId) {
        this.materialClassId = materialClassId;
    }

    public String getMaterialClass() {
        return materialClass;
    }

    public void setMaterialClass(String materialClass) {
        this.materialClass = materialClass == null ? null : materialClass.trim();
    }

    public Long getMaterialMiddleClassId() {
        return materialMiddleClassId;
    }

    public void setMaterialMiddleClassId(Long materialMiddleClassId) {
        this.materialMiddleClassId = materialMiddleClassId;
    }

    public String getMaterialMiddleClass() {
        return materialMiddleClass;
    }

    public void setMaterialMiddleClass(String materialMiddleClass) {
        this.materialMiddleClass = materialMiddleClass == null ? null : materialMiddleClass.trim();
    }

    public Long getMaterialSmallClassId() {
        return materialSmallClassId;
    }

    public void setMaterialSmallClassId(Long materialSmallClassId) {
        this.materialSmallClassId = materialSmallClassId;
    }

    public String getMaterialSmallClass() {
        return materialSmallClass;
    }

    public void setMaterialSmallClass(String materialSmallClass) {
        this.materialSmallClass = materialSmallClass == null ? null : materialSmallClass.trim();
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType == null ? null : materialType.trim();
    }

    public String getMaterialTypeNew() {
        return materialTypeNew;
    }

    public void setMaterialTypeNew(String materialTypeNew) {
        this.materialTypeNew = materialTypeNew == null ? null : materialTypeNew.trim();
    }

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    public String getFigureNumber() {
        return figureNumber;
    }

    public void setFigureNumber(String figureNumber) {
        this.figureNumber = figureNumber == null ? null : figureNumber.trim();
    }

    public String getChartVersion() {
        return chartVersion;
    }

    public void setChartVersion(String chartVersion) {
        this.chartVersion = chartVersion == null ? null : chartVersion.trim();
    }

    public String getChartVersionNew() {
        return chartVersionNew;
    }

    public void setChartVersionNew(String chartVersionNew) {
        this.chartVersionNew = chartVersionNew == null ? null : chartVersionNew.trim();
    }

    public BigDecimal getUnitWeight() {
        return unitWeight;
    }

    public void setUnitWeight(BigDecimal unitWeight) {
        this.unitWeight = unitWeight;
    }

    public BigDecimal getUnitWeightNew() {
        return unitWeightNew;
    }

    public void setUnitWeightNew(BigDecimal unitWeightNew) {
        this.unitWeightNew = unitWeightNew;
    }

    public String getMachiningPartType() {
        return machiningPartType;
    }

    public void setMachiningPartType(String machiningPartType) {
        this.machiningPartType = machiningPartType == null ? null : machiningPartType.trim();
    }

    public String getMachiningPartTypeNew() {
        return machiningPartTypeNew;
    }

    public void setMachiningPartTypeNew(String machiningPartTypeNew) {
        this.machiningPartTypeNew = machiningPartTypeNew == null ? null : machiningPartTypeNew.trim();
    }

    public String getSurfaceHandle() {
        return surfaceHandle;
    }

    public void setSurfaceHandle(String surfaceHandle) {
        this.surfaceHandle = surfaceHandle == null ? null : surfaceHandle.trim();
    }

    public String getSurfaceHandleNew() {
        return surfaceHandleNew;
    }

    public void setSurfaceHandleNew(String surfaceHandleNew) {
        this.surfaceHandleNew = surfaceHandleNew == null ? null : surfaceHandleNew.trim();
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material == null ? null : material.trim();
    }

    public String getMaterialNew() {
        return materialNew;
    }

    public void setMaterialNew(String materialNew) {
        this.materialNew = materialNew == null ? null : materialNew.trim();
    }

    public String getOrSparePartsMask() {
        return orSparePartsMask;
    }

    public void setOrSparePartsMask(String orSparePartsMask) {
        this.orSparePartsMask = orSparePartsMask == null ? null : orSparePartsMask.trim();
    }

    public String getOrSparePartsMaskNew() {
        return orSparePartsMaskNew;
    }

    public void setOrSparePartsMaskNew(String orSparePartsMaskNew) {
        this.orSparePartsMaskNew = orSparePartsMaskNew == null ? null : orSparePartsMaskNew.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getBrandMaterialCode() {
        return brandMaterialCode;
    }

    public void setBrandMaterialCode(String brandMaterialCode) {
        this.brandMaterialCode = brandMaterialCode == null ? null : brandMaterialCode.trim();
    }

    public Long getPerchasingLeadtime() {
        return perchasingLeadtime;
    }

    public void setPerchasingLeadtime(Long perchasingLeadtime) {
        this.perchasingLeadtime = perchasingLeadtime;
    }

    public Long getMinPerchaseQuantity() {
        return minPerchaseQuantity;
    }

    public void setMinPerchaseQuantity(Long minPerchaseQuantity) {
        this.minPerchaseQuantity = minPerchaseQuantity;
    }

    public Long getMinPackageQuantity() {
        return minPackageQuantity;
    }

    public void setMinPackageQuantity(Long minPackageQuantity) {
        this.minPackageQuantity = minPackageQuantity;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getSyncMes() {
        return syncMes;
    }

    public void setSyncMes(String syncMes) {
        this.syncMes = syncMes == null ? null : syncMes.trim();
    }

    public String getAttribute1() {
        return attribute1;
    }

    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1 == null ? null : attribute1.trim();
    }

    public String getAttribute2() {
        return attribute2;
    }

    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2 == null ? null : attribute2.trim();
    }

    public String getAttribute3() {
        return attribute3;
    }

    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3 == null ? null : attribute3.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getMaterialAttribute() {
        return materialAttribute;
    }

    public void setMaterialAttribute(String materialAttribute) {
        this.materialAttribute = materialAttribute == null ? null : materialAttribute.trim();
    }

    public String getInventoryType() {
        return inventoryType;
    }

    public void setInventoryType(String inventoryType) {
        this.inventoryType = inventoryType == null ? null : inventoryType.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", headerId=").append(headerId);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", name=").append(name);
        sb.append(", itemDes=").append(itemDes);
        sb.append(", unit=").append(unit);
        sb.append(", unitCode=").append(unitCode);
        sb.append(", model=").append(model);
        sb.append(", materialClassId=").append(materialClassId);
        sb.append(", materialClass=").append(materialClass);
        sb.append(", materialMiddleClassId=").append(materialMiddleClassId);
        sb.append(", materialMiddleClass=").append(materialMiddleClass);
        sb.append(", materialSmallClassId=").append(materialSmallClassId);
        sb.append(", materialSmallClass=").append(materialSmallClass);
        sb.append(", materialType=").append(materialType);
        sb.append(", materialTypeNew=").append(materialTypeNew);
        sb.append(", materialId=").append(materialId);
        sb.append(", figureNumber=").append(figureNumber);
        sb.append(", chartVersion=").append(chartVersion);
        sb.append(", chartVersionNew=").append(chartVersionNew);
        sb.append(", unitWeight=").append(unitWeight);
        sb.append(", unitWeightNew=").append(unitWeightNew);
        sb.append(", machiningPartType=").append(machiningPartType);
        sb.append(", machiningPartTypeNew=").append(machiningPartTypeNew);
        sb.append(", surfaceHandle=").append(surfaceHandle);
        sb.append(", surfaceHandleNew=").append(surfaceHandleNew);
        sb.append(", material=").append(material);
        sb.append(", materialNew=").append(materialNew);
        sb.append(", orSparePartsMask=").append(orSparePartsMask);
        sb.append(", orSparePartsMaskNew=").append(orSparePartsMaskNew);
        sb.append(", brand=").append(brand);
        sb.append(", brandMaterialCode=").append(brandMaterialCode);
        sb.append(", perchasingLeadtime=").append(perchasingLeadtime);
        sb.append(", minPerchaseQuantity=").append(minPerchaseQuantity);
        sb.append(", minPackageQuantity=").append(minPackageQuantity);
        sb.append(", remark=").append(remark);
        sb.append(", syncStatus=").append(syncStatus);
        sb.append(", syncMes=").append(syncMes);
        sb.append(", attribute1=").append(attribute1);
        sb.append(", attribute2=").append(attribute2);
        sb.append(", attribute3=").append(attribute3);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", materialAttribute=").append(materialAttribute);
        sb.append(", inventoryType=").append(inventoryType);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}