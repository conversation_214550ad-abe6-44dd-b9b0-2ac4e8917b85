package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.util.excel4j.ExcelDate2DateConverter;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 工时明细初始化数据导入ExcelVo
 * @author: ex_xuwj4
 * @create: 2021-05-12
 **/
@Data
public class WorkingHourExcelVo {

    @Excel(name = "序号")
    @ExcelField(title = "序号", order = 1 )
    private Long index;

    @Excel(name = "项目编号-旧")
    @ExcelField(title = "项目编号-旧", order = 2 )
    private String ProjectCodeOld;

    @Excel(name = "项目名称-旧")
    @ExcelField(title = "项目名称-旧", order = 4 )
    private String projectNameOld;

    @Excel(name = "项目号-新")
    @ExcelField(title = "项目号-新", order = 3 )
    @NotNull(message = "项目号-新不能为空")
    private String projectCode;

    @Excel(name = "出勤日期",format = "yyyy/MM/dd")
    @ExcelField(title = "出勤日期", order = 5,readConverter = ExcelDate2DateConverter.class )
    @NotNull(message = "出勤日期不能为空")
    private Date applyDate;

    @Excel(name = "填报成员姓名")
    @ExcelField(title = "填报成员姓名", order = 6 )
    @NotNull(message = "填报成员姓名不能为空")
    private String userName;

    @Excel(name = "填报成员mip")
    @ExcelField(title = "填报成员mip", order = 7 )
    @NotNull(message = "填报成员mip不能为空")
    private String userMip;

    @Excel(name = "用户类型")
    @ExcelField(title = "用户类型", order = 8 )
    @NotNull(message = "用户类型不能为空")
    private String userTypeName;

    @Excel(name = "币种")
    @ExcelField(title = "币种", order = 10 )
    @NotNull(message = "币种不能为空")
    private String currencyName;

    @Excel(name = "人天单价")
    @ExcelField(title = "人天单价", order = 11 )
    @NotNull(message = "人天单价不能为空")
    private BigDecimal costMoney;

    @Excel(name = "填报工时数")
    @ExcelField(title = "填报工时数", order = 12 )
    @NotNull(message = "填报工时数不能为空")
    private BigDecimal applyWorkingHours;

    @Excel(name = "实际工时数")
    @ExcelField(title = "实际工时数", order = 13 )
    @NotNull(message = "实际工时数不能为空")
    private BigDecimal actualWorkingHours;

    @Excel(name = "工时费用合计")
    @ExcelField(title = "工时费用合计", order = 14 )
    @NotNull(message = "工时费用合计不能为空")
    private BigDecimal totalPrice;

    @Excel(name = "年度")
    @ExcelField(title = "年度", order = 15 )
    @NotNull(message = "年度不能为空")
    private String years;

    @Excel(name = "角色分类")
    @ExcelField(title = "角色分类")
//    @NotNull(message = "角色分类不能为空")
    private String roleType;

    @Excel(name = "费率类型")
    @ExcelField(title = "费率类型")
    private String laborCostType;

    @Excel(name = "创建日期")
    @ExcelField(title = "创建日期",readConverter = ExcelDate2DateConverter.class)
//    @NotNull(message = "创建日期不能为空")
    private Date createAt;

    @Excel(name = "使用单位id")
    @ExcelField(title = "使用单位id")
//    @NotNull(message = "使用单位id不能为空")
    private Long bizUnitId;

    @Excel(name = "人力费率来源单位id")
    @ExcelField(title = "人力费率来源单位id")
//    @NotNull(message = "人力费率来源单位id不能为空")
    private Long laborCostSourceUnitId;

    @Excel(name = "wbs工时角色")
    @ExcelField(title = "wbs工时角色")
    private String laborWbsCostName;

    @Excel(name = "WBS号")
    @ExcelField(title = "WBS号")
    private String wbsBudgetCode;

    private String wbsSummaryCode;

    @Excel(name = "活动事项编号")
    @ExcelField(title = "活动事项编号")
    private String projectActivityCode;

    @Excel(name = "备注")
    @ExcelField(title = "备注")
    private String remarks;

    @Excel(name = "校验结果")
    private String validResult;

    private Integer status;
    private Boolean isImport;
    private Integer writeOffStatus;
    private Integer deleteFlag;
    protected Long createBy;
    private Date approveTime;
    private Integer rdmFlag;
    private Integer sourceFlag;
    private Long projectId;
    private Long userId;
    private String applyOrg;
    private String userType;
    private Long laborWbsCostId;
}
