package com.midea.pam.common.ctc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: pam
 * @description: 资金联行号同步接口返回类
 * @author: chenchong
 * @create: 2025-05-23
 **/
@Getter
@Setter
public class CnapsInfoReturnDto {
    @ApiModelProperty(value = "银行编号")
    private String bankCode;
    @ApiModelProperty(value = "银行名称")
    private String bankName;
    @ApiModelProperty(value = "联行号")
    private String cnapsCode;
    @ApiModelProperty(value = "清算行号")
    private String parentCnaps;
    @ApiModelProperty(value = "清算行名")
    private String parentCnapsName;
    @ApiModelProperty(value = "机构号")
    private String orgCode;
    @ApiModelProperty(value = "总行代码")
    private String bankTypeCode;
    @ApiModelProperty(value = "总行名称")
    private String bankTypeName;
    @ApiModelProperty(value = "电票接入点代码")
    private String bankAcceptanceBillApnCode;
    @ApiModelProperty(value = "电票接入点名称")
    private String bankAcceptanceBillApnName;
    @ApiModelProperty(value = "国家编码")
    private String countryCode;
    @ApiModelProperty(value = "国家名称")
    private String countryName;
    @ApiModelProperty(value = "省市编码")
    private String provincesCode;
    @ApiModelProperty(value = "省")
    private String province;
    @ApiModelProperty(value = "市")
    private String city;
    @ApiModelProperty(value = "区、县")
    private String country;
    @ApiModelProperty(value = "地址")
    private String address;
    @ApiModelProperty(value = "状态: 1有效 2无效")
    private String status;
    @ApiModelProperty(value = "创建时间")
    private Long createTime;
    @ApiModelProperty(value = "最后修改时间")
    private Long updateTime;
    @ApiModelProperty(value = "创建时间mip")
    private String createBy;
    @ApiModelProperty(value = "最后修改mip")
    private String updateBy;
}
