package com.midea.pam.common.sdp.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CustomerHead {
    private String customerType;              // 客户类型
    private String customerNumber;            // 客户编号
    private String customerName;              // 客户名称(中文)
    private String customerEnName;            // 客户名称(英文)
    private String statusCode;                // 状态码
    private String uscc;                      // 统一社会信用代码
    private String registrationNumber;        // 注册号
    private String nacao;                     // 组织机构代码
    private String taxRegNum;                 // 税务登记号
    private String country;                   // 国家
    private String state;                     // 省/州
    private String city;                      // 城市
    private String county;                    // 县
    private String town;                      // 镇
    private String registrationAddress;       // 注册地址
    private String supplierFlag;              // 供应商标志
    private String supplierNumber;            // 供应商编号
    private String enterpriseNature;          // 企业性质
    private String taxpayerType;              // 纳税人类型
    private String taxRegistration;           // 税务登记人
    private String corpRepresentative;        // 法人代表
    private String zipCode;                   // 邮编
    private String fax;                       // 传真
    private String mainPhone;                 // 主要电话
    private String customerGroup;             // 客户组
    private String threeCerInOneFlg;          // 三证合一标志
    private String busiInfoVeriStatus;        // 工商信息验证状态
    private String busiInfoVeriDate;          // 工商信息验证日期
    private String innerCustFlag;             // 内部客户标志
    private String internationalCustomerCode; // 国际客户代码
}
