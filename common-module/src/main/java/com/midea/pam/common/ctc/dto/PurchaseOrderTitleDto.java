package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.PurchaseOrderTitle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class PurchaseOrderTitleDto extends PurchaseOrderTitle {

    @ApiModelProperty(value = "采购员名称")
    private String buyerName;
    @ApiModelProperty(value = "采购员id")
    private Long buyerId;
    @ApiModelProperty(value = "订单编号")
    private String num;
    @ApiModelProperty(value = "erp采购员名称")
    private String erpBuyerName;
    @ApiModelProperty(value = "业务实体id")
    private Long projectOuId;
    @ApiModelProperty(value = "批准供应商id")
    private Long vendorAslId;
    @ApiModelProperty(value = "超预算说明")
    private String overBudgetDes;

    @ApiModelProperty(value = "接收子库")
    private String secondaryInventory;
    @ApiModelProperty(value = "接收子库代码")
    private String secondaryInventoryName;

    /**
     * 合同条款类型(0：非标准条款；1：标准条款)
     */
    private String contractTermsFlg;

    private List<StandardTermsDto> standardTermsDtoList;
}
