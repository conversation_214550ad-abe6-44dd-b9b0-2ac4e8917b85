package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.ProjectBudgetChangeSummaryHistory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@ApiModel(value = "wbs预算变更对象")
public class ProjectWbsBudgetChangeDTO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 变更原因
     */
    private String reason;

    /**
     * 变更原因
     */
    private String reasonType;

    /**
     * 变更头表ID
     */
    private Long headerId;

    /**
     * 保存类型 1、正常提交；2、驳回后提交
     */
    private Integer type;

    /**
     * 项目汇总变更信息
     */
    private ProjectBudgetChangeSummaryHistory projectBudgetChangeSummaryHistory;

    /**
     * 附件记录
     */
    private List<CtcAttachmentDto> attachmentDtos;

    /**
     * 项目wbs预算变更信息
     * ProjectWbsBudgetChangeHistory
     */
    private List<Map<String, Object>> projectWbsBudgetChangeHistories;

    /**
     * 项目当前更新时间（用于校验页面操作期间项目信息是否有更新）
     */
    private Date projectCurrentUpdateAt;

    @ApiModelProperty(value = "详细设计单据id")
    private Long projectWbsReceiptsId;
}
