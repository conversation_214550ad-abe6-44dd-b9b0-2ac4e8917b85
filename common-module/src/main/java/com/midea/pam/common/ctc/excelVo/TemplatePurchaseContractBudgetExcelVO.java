package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.util.excel4j.ExcelDate2DateConverter;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class TemplatePurchaseContractBudgetExcelVO {

    @ExcelField(title = "序号", order = 0)
    private Integer num;

    @ExcelField(title = "PAM合同编号-新", order = 1)
    @NotNull(message = "PAM合同编号-新不能为空")
    private String code;

    /**
     * 采购合同id
     */
    private Long purchaseContractId;

    /**
     * 采购合同金额（不含税）
     */
    private BigDecimal excludingTaxAmount;

    @ExcelField(title = "合同编号-旧", order = 2)
    @NotNull(message = "合同编号-旧不能为空")
    private String codeOld;

    @ExcelField(title = "数量", order = 3)
    @NotNull(message = "数量不能为空")
    private BigDecimal number;

    @ExcelField(title = "单价（不含税）", order = 4)
    @NotNull(message = "单价（不含税）不能为空")
    private BigDecimal price;

    @ExcelField(title = "总价（不含税）", order = 5)
    @NotNull(message = "总价（不含税）不能为空")
    private BigDecimal totalPrice;

    @ExcelField(title = "累计进度执行金额（不含税）", order = 6)
    @NotNull(message = "累计进度执行金额（不含税）不能为空")
    private BigDecimal budgetExecuteAmountTotal;

    @ExcelField(title = "项目号-新", order = 7)
    @NotNull(message = "项目号-新不能为空")
    private String projectCode;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    @ExcelField(title = "PAM编码-新", order = 8)
    @NotNull(message = "PAM编码-新不能为空")
    private String pamCode;

    @ExcelField(title = "ERP编码-新", order = 9)
    private String erpCode;

    @ExcelField(title = "单位-新", order = 10)
    @NotNull(message = "单位-新不能为空")
    private String unit;

    /**
     * 单位编码
     */
    private String unitCode;

    @ExcelField(title = "项目号-旧", order = 11)
    @NotNull(message = "项目号-旧不能为空")
    private String projectCodeOld;

    @ExcelField(title = "物料编码-旧", order = 12)
    @NotNull(message = "物料编码-旧不能为空")
    private String pamCodeOld;

    @ExcelField(title = "单位-旧", order = 13)
    @NotNull(message = "单位-旧不能为空")
    private String unitOld;

    @ExcelField(title = "图纸版本号", order = 14)
    private String chartVersion;

    @ExcelField(title = "WBS号", order = 15)
    @NotNull(message = "WBS号不能为空")
    private String wbsSummaryCode;

    @ExcelField(title = "未下达量", order = 16)
    @NotNull(message = "不能为空")
    private BigDecimal unreleasedAmount;

    @ExcelField(title = "计划交货日期", order = 17, readConverter = ExcelDate2DateConverter.class)
    @NotNull(message = "计划交货日期不能为空")
    private Date deliveryTime;

    @ExcelField(title = "需求发布单据编号", order = 18)
    @NotNull(message = "需求发布单据编号不能为空")
    private String requirementCode;

    @ExcelField(title = "是否急件", order = 19)
    private String dispatchIsStr;

    private Boolean dispatchIs;

    @ExcelField(title = "是否外包", order = 20)
    private String extIsStr;

    private Boolean extIs;

    /**
     * 采购类型(1：默认、2：外购物料、3：wbs)
     */
    private Integer purchaseType;

    @ExcelField(title = "设计发布批次号", order = 21)
    @NotNull(message = "设计发布批次号不能为空")
    private String designReleaseLotNumber;

    @ExcelField(title = "最新发布日期", order = 22, readConverter = ExcelDate2DateConverter.class)
    @NotNull(message = "最新发布日期不能为空")
    private Date publishTime;

    @ExcelField(title = "组织代码", order = 23)
    @NotNull(message = "组织代码不能为空")
    private String organizationCode;

    @ExcelField(title = "活动事项", order = 24)
    @NotNull(message = "活动事项不能为空")
    private String activityCode;

    @ExcelField(title = "预算排序", order = 25)
    @NotNull(message = "预算排序不能为空")
    private Integer row;

    @Excel(name = "校验结果")
    private String validResult;

    /**
     * 采购合同预算id
     */
    private Long id;

    /**
     * 物料id
     */
    private Long materialId;

    /**
     * 物料描述
     */
    private String materielDescr;

    /**
     * 库存组织id
     */
    private Long organizationId;

}
