package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.ctc.entity.ReceiptPlan;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetail;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.excel4j.ExcelDate2DateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/9
 */
@Data
public class TemplateReceiptPlanExcelVO {

    @Excel(name = "序号")
    @ExcelField(title = "序号")
    private Integer index;

    @Excel(name = "子合同编号")
    @ExcelField(title = "子合同编号")
    @NotNull(message = "子合同编号不能为空")
    private String subContractCode;

    @Excel(name = "子合同名称")
    @ExcelField(title = "子合同名称")
    @NotNull(message = "子合同名称不能为空")
    private String subContractName;

    @Excel(name = "回款计划日期", format = "yyyy/MM/dd")
    @ExcelField(title = "回款计划日期",readConverter = ExcelDate2DateConverter.class)
    @NotNull(message = "回款计划日期不能为空")
    private Date date;

    @Excel(name = "回款计划金额（含税）")
    @ExcelField(title = "回款计划金额（含税）")
    @NotNull(message = "回款计划金额（含税）不能为空")
    private BigDecimal amount;

    @Excel(name = "回款条件")
    @ExcelField(title = "回款条件")
    private String requirement;

    @Excel(name = "里程碑名称")
    @ExcelField(title = "里程碑名称")
    private String milestoneName;

    @Excel(name = "实际回款金额")
    @ExcelField(title = "实际回款金额")
    private BigDecimal actualAmount;

    @Excel(name = "回款计划编号")
    @ExcelField(title = "回款计划编号")
    @NotNull(message = "回款计划编号不能为空")
    private String detailCode;

    private Long milestoneId;

    @Excel(name = "校验结果")
    private String validResult;

    @ApiModelProperty(value = "合同ID")
    private Long contractId;

    private Long planId;
    private Long detailId;

    @ApiModelProperty(value = "回款类型")
    private String receiptType;

    @ApiModelProperty(value = "来源：自动生成/手工")
    private String source;

    public ReceiptPlanDetail getReceiptPlanDetail() {
        ReceiptPlanDetail receiptPlanDetail = new ReceiptPlanDetail();
        receiptPlanDetail.setId(detailId);
        receiptPlanDetail.setDeletedFlag(Boolean.FALSE);
        receiptPlanDetail.setContractId(contractId);
        receiptPlanDetail.setAmount(amount);
        receiptPlanDetail.setDate(date);
        receiptPlanDetail.setCode(detailCode);
        receiptPlanDetail.setNum(1);
        receiptPlanDetail.setActualAmount(actualAmount != null ? actualAmount : BigDecimal.ZERO);
        receiptPlanDetail.setRequirement(requirement);
        receiptPlanDetail.setMilestoneId(milestoneId);
        receiptPlanDetail.setReceiptType(null); // 2022-08-08庆坤确认导入不用加字段，默认值null
        receiptPlanDetail.setSource("自动生成"); // 2022-08-08庆坤确认导入不用加字段，默认值自动生成
        return receiptPlanDetail;
    }
}
