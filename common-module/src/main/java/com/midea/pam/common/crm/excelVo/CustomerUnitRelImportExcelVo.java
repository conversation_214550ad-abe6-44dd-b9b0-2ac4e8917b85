package com.midea.pam.common.crm.excelVo;

import com.github.crab2died.annotation.ExcelField;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class CustomerUnitRelImportExcelVo {
    private Long customerId;

    @Excel(name ="客户CRM编码")
    @ExcelField(title = "客户CRM编码", order = 2)
    @NotNull(message = "客户CRM编码不能为空")
    private String crmCode;

    private Long id;

    @Excel(name ="行业")
    @ExcelField(title = "行业", order = 3)
    @NotNull(message = "行业不能为空")
    private String industry;

    @Excel(name ="行业编码")
    @ExcelField(title = "行业编码", order = 4)
    @NotNull(message = "行业编码不能为空")
    private String industryCode;

    @Excel(name ="虚拟部门ID")
    @ExcelField(title = "虚拟部门ID", order = 5)
    private Long unitId;

    @Excel(name ="虚拟部门名称")
    @ExcelField(title = "虚拟部门名称", order = 6)
    private String unitName;

    @Excel(name ="区域")
    @ExcelField(title = "区域", order = 7)
    @NotNull(message = "区域不能为空")
    private String region;

    @Excel(name ="区域名称")
    @ExcelField(title = "区域名称", order = 8)
    @NotNull(message = "区域名称不能为空")
    private String regionName;

    @Excel(name ="客户分组")
    @ExcelField(title = "客户分组")
    private String userGroup;
    private String userGroupCode;

    @Excel(name ="信用评级")
    @ExcelField(title = "信用评级")
    private String creditRating;
    private String creditRatingCode;

    @Excel(name ="单位性质")
    @ExcelField(title = "单位性质", order = 9)
    private String enterpriseNatureName;
    @ExcelField(title = "enterpriseNature")
    private String enterpriseNature;

    @Excel(name ="纳税人类型")
    @ExcelField(title = "纳税人类型", order = 10)
    private String taxpayerTypeName;
    @ExcelField(title = "taxpayerType")
    private String taxpayerType;

    @Excel(name ="客户类型")
    @ExcelField(title = "客户类型", order = 11)
    private String isInnerName;

    @Excel(name ="开户行联行编号")
    @ExcelField(title = "开户行联行编号", order = 12)
    private String cnapsCode;

    @Excel(name ="开户银行名称")
    @ExcelField(title = "开户银行名称", order = 13)
    private String bankAccountName;

    @Excel(name ="银行账号")
    @ExcelField(title = "银行账号", order = 14)
    private String bankAccountNum;

    @Excel(name ="开户行编码（PAM）")
    @ExcelField(title = "开户行编码（PAM）", order = 15)
    private String bankAccountCode;

    @Excel(name ="开票人电话")
    @ExcelField(title = "开票人电话", order = 16)
    private String cellphone;

    @Excel(name ="国家")
    @ExcelField(title = "国家", order = 17)
    private String countryName;

    @Excel(name ="省")
    @ExcelField(title = "省", order = 18)
    private String provinceName;
    private String province;

    @Excel(name ="城市")
    @ExcelField(title = "城市", order = 19)
    private String cityName;
    private String city;

    @Excel(name ="开票地址（税局备案）")
    @ExcelField(title = "开票地址（税局备案）", order = 20)
    private String bankAddress;

    @Excel(name ="客户开票名称（PAM）")
    @ExcelField(title = "客户开票名称（PAM）", order = 21)
    private String invoiceName;

    @Excel(name ="开票类型（PAM）")
    @ExcelField(title = "开票类型（PAM）", order = 22)
    private String invoiceTypeName;

    private String errorHint;

}
