package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.basedata.entity.VendorSiteBankForDisplay;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "发票信息头数据传输对象")
public class PaymentInvoiceDto extends PaymentInvoice {

    private Integer pageNum;

    private Integer pageSize;

    @ApiModelProperty("本次消耗金额")
    private Integer isRel;

    @ApiModelProperty("本次消耗金额")
    private BigDecimal useAmount;

    @ApiModelProperty("值不为空，则代表剩余可用金额大于0")
    private String surplusAmountMoreThan;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty("汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty("汇率时间")
    private Date conversionDate;

    @ApiModelProperty("汇率类型")
    private String conversionType;

    @ApiModelProperty("供应商信息")
    private VendorSiteBankForDisplay vendorSiteBank;

    private String contractIdsStr;
    private String purchaseContractName;
    private Long projectId;
    private String projectCode;
    private String projectName;
    private String vendorSiteCode;
    private String ouName;
    private String vendorName;
    private Long vendorId;
    private String vendorCode;
    private Date glDate;
    private PurchaseContract purchaseContract;

    private String createByName;
    private String statusStr;
    private String erpStatusStr;
    private String erpCancelStatusStr;
    private String freezeStatusStr;
    private String createAtStart;
    private String createAtEnd;
    private String glDateStart;
    private String glDateEnd;

    @ApiModelProperty("GSC发票申请ID")
    private Long gscPaymentInvoiceId;

    @ApiModelProperty("发票到期日开始")
    private String dueDateStart;

    @ApiModelProperty("发票到期日结束")
    private String dueDateEnd;

    @ApiModelProperty(value = "发票信息行", name = "paymentInvoiceDetailList")
    private List<PaymentInvoiceDetailDto> paymentInvoiceDetailList;

    private PaymentInvoiceDetailDto paymentInvoiceDetailDto;
    private List<PaymentInvoiceDetailDto> paymentInvoiceDetailDtoList;

    private BigDecimal taxExcludedPrice;

    @ApiModelProperty("已入账税票金额（不含税）")
    private BigDecimal amountOfTaxReceipts;

    @ApiModelProperty("采购合同进度执行金额（不含税）")
    private BigDecimal purchasingContractProgressAmount;

    @ApiModelProperty("采购合同")
    private PurchaseContractDTO purchaseContractDto;

    @ApiModelProperty(value = "冻结人名字")
    private String freezeName;

    @ApiModelProperty(value = "冻结原因")
    private String freezeReason;

    @ApiModelProperty(value = "状态排除")
    private String statusExclude;

    @ApiModelProperty(value = "发票金额(不含税)")
    private BigDecimal totalInvoiceExcludedPrice;

    /**
     * 前端展示使用,不存发票头数据库
     */
    @ApiModelProperty(value = "发票类型")
    private String invoiceType;

    @ApiModelProperty(value = "ERP取消人名称")
    private String erpCancelName;

    /**
     * 付款申请明细
     */
    private List<PaymentApplyDetailsDto> paymentApplyDetailsDtos;

    /**
     * 核销明细
     */
    private List<PaymentWriteOffRecordDetailsDto> paymentWriteOffRecordDetailsDtos;

    @ApiModelProperty(value = "供应商地点ID")
    private String erpVendorSiteId;

    @ApiModelProperty(value = "入账类型 多选")
    private String accountEntryTypeStr;

    @ApiModelProperty(value = "关联的罚扣集合")
    private List<PunishmentInvoiceVO> punishmentInvoiceViewList;

    @ApiModelProperty(value = "罚扣集合")
    private List<PunishmentInvoiceSubmit> punishmentInvoiceSubmitList;

    @ApiModelProperty(value = "是否应付发票提交前单据检查")
    private boolean isDraftSubmitCheck = false;

    @ApiModelProperty(value = "来源系统名称")
    private String sourceSystemName;


    @Getter
    @Setter
    public static class PunishmentInvoiceSubmit{

        @ApiModelProperty(value = "应付发票ID")
        private Long id;

        @ApiModelProperty(value = "罚扣ID")
        private Long punishmentId;

        @ApiModelProperty(value = "是否删除")
        private boolean deletedFlag;
    }
}


