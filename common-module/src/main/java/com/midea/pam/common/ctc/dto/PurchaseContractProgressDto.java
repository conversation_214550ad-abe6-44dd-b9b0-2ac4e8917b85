package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.entity.PurchaseContractProgress;
import com.midea.pam.common.util.CacheDataUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "采购合同进度")
public class PurchaseContractProgressDto extends PurchaseContractProgress {

    private String progressStatusStr;

    private List<Integer> progressStatusList;

    private String frontStartTimeStr;

    private String frontEndTimeStr;

    private String backStartTimeStr;

    private String backEndTimeStr;

    private Integer num;

    @ApiModelProperty(value = "提交人名称")
    private String createByName;

    @ApiModelProperty(value = "采购合同编号")
    private String purchaseContractCode;

    @ApiModelProperty(value = "采购合同名称")
    private String purchaseContractName;

    @ApiModelProperty(value = "承诺交期")
    private Date deliveryTime;

    @ApiModelProperty(value = "采购合同预算列表")
    private List<PurchaseContractBudgetDto> purchaseContractWbsBudgets;

    @ApiModelProperty(value = "采购合同进度预算关联列表")
    private List<PurchaseContractProgressBudgetRelDto> progressBudgetRelDtos;

    @ApiModelProperty(value = "附件列表")
    private List<CtcAttachmentDto> attachmentDtos;

    @ApiModelProperty(value = "入账信息明细列表")
    private List<PurchaseContractProgressDetailSubjectDto> progressDetailSubjectDtos;

    @ApiModelProperty(value = "采购合同")
    private PurchaseContractDTO purchaseContractDTO;

    @ApiModelProperty(value = "累计进度执行金额（不含税）-本位币")
    private BigDecimal localBudgetExecuteAmountTotal;

    @Override
    public void setCreateBy(Long createBy) {
        super.setCreateBy(createBy);
        if (createBy != null) {
            UserInfo userInfo = CacheDataUtils.findUserById(createBy);
            if (userInfo != null) {
                setCreateByName(userInfo.getName());
            }
        }
    }
}