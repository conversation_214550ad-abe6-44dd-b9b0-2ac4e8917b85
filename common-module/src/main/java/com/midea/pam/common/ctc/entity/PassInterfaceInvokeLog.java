package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "pass接口调用日志表")
public class PassInterfaceInvokeLog extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "单据编号")
    private String applyNo;

    @ApiModelProperty(value = "流水号")
    private String serialNo;

    @ApiModelProperty(value = "服务系统(EMS,CEB等)")
    private String serviceSystem;

    @ApiModelProperty(value = "调用接口url")
    private String invokeUrl;

    @ApiModelProperty(value = "状态: 0:待调用; 1:成功; 2:失败")
    private Integer invokeStatus;

    @ApiModelProperty(value = "接口返回内容或失败原因")
    private String invokeResult;

    @ApiModelProperty(value = "调用时间")
    private Date invokeTime;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "调用接口参数")
    private String invokeParams;

    private static final long serialVersionUID = 1L;

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo == null ? null : applyNo.trim();
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo == null ? null : serialNo.trim();
    }

    public String getServiceSystem() {
        return serviceSystem;
    }

    public void setServiceSystem(String serviceSystem) {
        this.serviceSystem = serviceSystem == null ? null : serviceSystem.trim();
    }

    public String getInvokeUrl() {
        return invokeUrl;
    }

    public void setInvokeUrl(String invokeUrl) {
        this.invokeUrl = invokeUrl == null ? null : invokeUrl.trim();
    }

    public Integer getInvokeStatus() {
        return invokeStatus;
    }

    public void setInvokeStatus(Integer invokeStatus) {
        this.invokeStatus = invokeStatus;
    }

    public String getInvokeResult() {
        return invokeResult;
    }

    public void setInvokeResult(String invokeResult) {
        this.invokeResult = invokeResult == null ? null : invokeResult.trim();
    }

    public Date getInvokeTime() {
        return invokeTime;
    }

    public void setInvokeTime(Date invokeTime) {
        this.invokeTime = invokeTime;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getInvokeParams() {
        return invokeParams;
    }

    public void setInvokeParams(String invokeParams) {
        this.invokeParams = invokeParams == null ? null : invokeParams.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", applyNo=").append(applyNo);
        sb.append(", serialNo=").append(serialNo);
        sb.append(", serviceSystem=").append(serviceSystem);
        sb.append(", invokeUrl=").append(invokeUrl);
        sb.append(", invokeStatus=").append(invokeStatus);
        sb.append(", invokeResult=").append(invokeResult);
        sb.append(", invokeTime=").append(invokeTime);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", invokeParams=").append(invokeParams);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}