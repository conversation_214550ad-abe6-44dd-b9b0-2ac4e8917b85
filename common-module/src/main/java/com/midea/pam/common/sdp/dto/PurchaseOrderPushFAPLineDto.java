package com.midea.pam.common.sdp.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PurchaseOrderPushFAPLineDto {

    // ========== 必填字段 ==========

    /**
     * 流水号 - 与头表保持一致
     */
    private String serialNumber;

    /**
     * 来源系统单据ID - 与头表保持一致
     */
    private String sourceSysBusId;

    /**
     * 来源系统单据号 - 与头表保持一致
     */
    private String sourceSysBusCode;

    /**
     * 来源系统单据行ID - ISCP的唯一行ID，ISCP采购订单行ID
     */
    private String sourceSysBusLineId;

    /**
     * 来源系统单据行号 - ISCP行号
     */
    private Integer sourceSysBusLineNum;

    /**
     * 免费标识 - 默认值：0
     */
    private Integer freeFlag;

    /**
     * 请求数量 - 传入订单行对应的数量
     */
    private BigDecimal requestQuantity;

    /**
     * 创建人名称 - 传入创建人MIP账号
     */
    private String createdBy;

    /**
     * 创建时间 - 落表时自动生成
     */
    private Date creationDate;

    /**
     * 最后更新人名称 - 传入创建人MIP账号
     */
    private String lastUpdatedBy;

    /**
     * 最后更新时间 - 落表时自动生成
     */
    private Date lastUpdateDate;

    /**
     * 销货类型 - 继承头上的销售类型
     */
    private String saleType;

    /**
     * 需求日期 - 传入需求日期
     */
    private Date needDate;

    /**
     * 来源系统单据行的来源行ID
     */
    private String sourceSysBusLineSourceLineId;

    // ========== 非必填字段 ==========

    /**
     * 主键
     */
    private Long id;

    /**
     * 头表ID
     */
    private Long headId;

    /**
     * 需方物料编码 - 需方物料编码
     */
    private String reItemCode;

    /**
     * 需方物料主单位 - 物料单位
     */
    private String reItemUom;

    /**
     * 供方物料编码
     */
    private String suItemCode;

    /**
     * 供方物料主单位
     */
    private String suItemUom;

    /**
     * 套散类型
     */
    private String kitType;

    /**
     * 选件行号
     */
    private String optionNumber;

    /**
     * 选件对应套机行ID
     */
    private String kitLineId;

    /**
     * 不含税单价 - 传入订单行不含税单价
     */
    private BigDecimal noTaxUnitPrice;

    /**
     * 发出子库编码
     */
    private String sourceSubinvCode;

    /**
     * 发出货位编码
     */
    private String sourceLocationCode;

    /**
     * 待结算子库编码
     */
    private String shipSubinvCode;

    /**
     * 待结算货位编码
     */
    private String shipLocationCode;

    /**
     * 接收子库编码 - 当来源系统编码在快码ITC_ORDER_SUBINV_CODE里有维护时，当接口字段接收子库<RECEIVE_SUBINV_CODE>有值时，这个值需要接入到关联订单行上接收子库<RECEIVE_SUBINV_CODE>
     */
    private String receiveSubinvCode;

    /**
     * 接收货位编码
     */
    private String receiveLocationCode;

    /**
     * 发出批次
     */
    private String issueLotNumber;

    /**
     * 发出日期
     */
    private Date issueDate;

    /**
     * 发出记账日期
     */
    private Date issueGlDate;

    /**
     * 接收批次
     */
    private String receiveLotNumber;

    /**
     * 接收日期
     */
    private Date receiveDate;

    /**
     * 接收记账日期
     */
    private Date receiveGlDate;

    /**
     * 退货对应原始正向业务来源行号
     */
    private String parentLogistOriginLineNum;

    /**
     * 退货对应原始正向业务来源行ID
     */
    private String parentLogistOriginLineId;

    /**
     * 退货对应原始正向业务系统编码
     */
    private String parentLogistOriginSysCode;



    /**
     * 来源系统单据行的来源行系统编码
     */
    private String sourceSysBusLineSourceSysCode;

    /**
     * 备注
     */
    private String comments;

    /**
     * 跟踪号
     */
    private String traceCode;

    /**
     * 处理状态
     */
    private String processStatus;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 处理结果信息
     */
    private String processMessage;

    /**
     * 版本
     */
    private Long dataVersion;

    /**
     * 删除标识
     */
    private Integer deleteFlag;

    /**
     * 退货对应正向关联物流行ID
     */
    private Long erpParentLogistLineId;

    /**
     * 关联订单号
     */
    private String orderHeadCode;

    /**
     * 关联订单行号
     */
    private Long orderLineNum;

    /**
     * 源关联物流行ID
     */
    private Long parentLogistLineId;

    /**
     * 关联物流行ID
     */
    private Long logistLineId;

    /**
     * 项目号 - ISCP传入项目号
     */
    private String projectCode;

    /**
     * 项目号id - 记录在IERP发运行
     */
    private Long projectId;

    /**
     * 计划需求组名称 - planDemandName
     */
    private String planDemandName;

    /**
     * 计划需求组CODE - shipmentAttribute13
     */
    private String planDemandCode;

    /**
     * 保税类型 - IERP存在ATTRIBUTE10
     */
    private String bondType;
}
