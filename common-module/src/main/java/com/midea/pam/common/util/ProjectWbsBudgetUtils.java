package com.midea.pam.common.util;

import com.midea.pam.common.constants.ProjectWbsCostConstant;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.ProjectActivityCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleDetailCache;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto;
import com.midea.pam.common.ctc.dto.WbsDynamicFieldsDto;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.cache.ProjectActivityCacheUtils;
import com.midea.pam.common.util.cache.WbsTemplateRuleCacheUtils;
import com.midea.pam.common.util.cache.WbsTemplateRuleDetailCacheUtils;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ProjectWbsBudgetUtils {

    /**
     * 每个属性之间添加分割符号，避免前后属性拼写重复
     */
    public static final String DEFAULT_SPLIT = "v!#@%v!#@%";

    private static Logger logger = LoggerFactory.getLogger(ProjectWbsBudgetUtils.class);

    /**
     * 获取wbs最后一层
     *
     * @param excelMap
     * @param wbsDynamicFieldList
     * @return
     */
    public static String getWbsLastCode(Map excelMap, List<WbsDynamicFieldsDto> wbsDynamicFieldList) {
        if (CollectionUtils.isEmpty(wbsDynamicFieldList)) {
            return null;
        }
        return MapUtils.getString(excelMap, wbsDynamicFieldList.get(wbsDynamicFieldList.size() - 1).getKey());
    }

    /**
     * 获取wbs最后一层
     *
     * @param excelMap
     * @param wbsTemplateRuleCacheList
     * @return
     */
    public static String getCacheWbsLastCode(Map excelMap, List<WbsTemplateRuleCache> wbsTemplateRuleCacheList) {
        if (CollectionUtils.isEmpty(wbsTemplateRuleCacheList)) {
            return null;
        }
        return MapUtils.getString(excelMap, wbsTemplateRuleCacheList.get(wbsTemplateRuleCacheList.size() - 1).getKey());
    }

    /**
     * 获取动态列wbs模板规则id数组
     *
     * @param wbsTemplateRuleCacheList
     * @return
     */
    public static String getCacheDynamicWbsTemplateRuleIds(List<WbsTemplateRuleCache> wbsTemplateRuleCacheList) {
        if (CollectionUtils.isEmpty(wbsTemplateRuleCacheList)) {
            return "";
        }
        StringBuilder value = new StringBuilder();
        for (WbsTemplateRuleCache cache : wbsTemplateRuleCacheList) {
            if (value.length() > 0) {
                value.append("," + cache.getId());
            } else {
                value.append(cache.getId());
            }
        }
        return value.toString();
    }

    /**
     * 获取动态列字段名数组
     *
     * @param wbsTemplateRuleCacheList
     * @return
     */
    public static String getCacheDynamicFields(List<WbsTemplateRuleCache> wbsTemplateRuleCacheList) {
        if (CollectionUtils.isEmpty(wbsTemplateRuleCacheList)) {
            return "";
        }
        StringBuilder value = new StringBuilder();
        for (WbsTemplateRuleCache cache : wbsTemplateRuleCacheList) {
            if (value.length() > 0) {
                value.append("," + cache.getKey());
            } else {
                value.append(cache.getKey());
            }
        }
        return value.toString();
    }

    /**
     * 获取动态列编码数组
     *
     * @param wbsTemplateRuleCacheList
     * @return
     */
    public static String getCacheDynamicValues(Map excelMap, List<WbsTemplateRuleCache> wbsTemplateRuleCacheList) {
        if (CollectionUtils.isEmpty(wbsTemplateRuleCacheList)) {
            return "";
        }
        StringBuilder value = new StringBuilder();
        for (WbsTemplateRuleCache cache : wbsTemplateRuleCacheList) {
            if (value.length() > 0) {
                value.append("," + MapUtils.getString(excelMap, cache.getKey()));
            } else {
                value.append(MapUtils.getString(excelMap, cache.getKey()));
            }
        }
        return value.toString();
    }


    /**
     * 获取wbs预算唯一键
     * wbs动态列 + activity活动事项
     *
     * @param excelMap            excel记录（基线）
     * @param wbsDynamicFieldList wbs动态列
     * @param split               分割符号
     * @return
     */
    public static String getWbsBudgetUniqueKey(Map excelMap, List<WbsDynamicFieldsDto> wbsDynamicFieldList, String split) {
        if (StringUtils.isBlank(split)) {
            split = DEFAULT_SPLIT;
        }
        // 唯一key = wbs动态列 + activity活动事项
        StringBuilder groupKey = new StringBuilder();
        for (WbsDynamicFieldsDto wbsDynamicField : wbsDynamicFieldList) {
            groupKey.append(excelMap.get(wbsDynamicField.getKey()));
            groupKey.append(split);
        }
        groupKey.append(excelMap.get(WbsBudgetFieldConstant.ACTIVITY_CODE));
        return groupKey.toString();
    }

    /**
     * 获取wbs预算唯一键
     * wbs动态列 + activity活动事项
     *
     * @param excelMap                 excel记录（基线）
     * @param wbsTemplateRuleCacheList wbs动态列
     * @param split                    分割符号
     * @return
     */
    public static String getWbsBudgetCacheUniqueKey(Map excelMap, List<WbsTemplateRuleCache> wbsTemplateRuleCacheList, String split) {
        if (StringUtils.isBlank(split)) {
            split = DEFAULT_SPLIT;
        }
        // 唯一key = wbs动态列 + activity活动事项
        StringBuilder groupKey = new StringBuilder();
        for (WbsTemplateRuleCache wbsDynamicField : wbsTemplateRuleCacheList) {
            groupKey.append(excelMap.get(wbsDynamicField.getKey()));
            groupKey.append(split);
        }
        groupKey.append(excelMap.get(WbsBudgetFieldConstant.ACTIVITY_CODE));
        return groupKey.toString();
    }

    /**
     * 获取wbs基线唯一键
     * wbs动态列 + activity活动事项 + 批次号
     *
     * @param excelMap            excel记录（基线）
     * @param wbsDynamicFieldList wbs动态列
     * @param batchCode           批次号
     * @param split               分割符号
     * @return
     */
    public static String getWbsBaselineUniqueKey(Map excelMap, List<WbsDynamicFieldsDto> wbsDynamicFieldList, String batchCode, String split) {
        if (StringUtils.isBlank(split)) {
            split = DEFAULT_SPLIT;
        }
        // 唯一key = wbs动态列 + activity活动事项 + 批次号
        StringBuilder groupKey = new StringBuilder();
        for (WbsDynamicFieldsDto wbsDynamicField : wbsDynamicFieldList) {
            groupKey.append(excelMap.get(wbsDynamicField.getKey()));
            groupKey.append(split);
        }
        groupKey.append(excelMap.get(WbsBudgetFieldConstant.ACTIVITY_CODE));
        groupKey.append(split);
        if (StringUtils.isNotBlank(batchCode)) {
            groupKey.append(batchCode);
        } else {
            groupKey.append(excelMap.get(WbsBudgetFieldConstant.PROJECT_BASELINE_BATCH_CODE));
        }

        return groupKey.toString();
    }

    /**
     * 获取wbs基线唯一键
     * wbs动态列 + activity活动事项 + 批次号
     *
     * @param excelMap                 excel记录（基线）
     * @param wbsTemplateRuleCacheList wbs动态列
     * @param batchCode                批次号
     * @param split                    分割符号
     * @return
     */
    public static String getWbsBaselineCacheUniqueKey(Map excelMap, List<WbsTemplateRuleCache> wbsTemplateRuleCacheList, String batchCode, String split) {
        if (StringUtils.isBlank(split)) {
            split = DEFAULT_SPLIT;
        }
        // 唯一key = wbs动态列 + activity活动事项 + 批次号
        StringBuilder groupKey = new StringBuilder();
        for (WbsTemplateRuleCache wbsDynamicField : wbsTemplateRuleCacheList) {
            groupKey.append(excelMap.get(wbsDynamicField.getKey()));
            groupKey.append(split);
        }
        groupKey.append(excelMap.get(WbsBudgetFieldConstant.ACTIVITY_CODE));
        groupKey.append(split);
        if (StringUtils.isNotBlank(batchCode)) {
            groupKey.append(batchCode);
        } else {
            groupKey.append(excelMap.get(WbsBudgetFieldConstant.PROJECT_BASELINE_BATCH_CODE));
        }

        return groupKey.toString();
    }

    /**
     * 获取wbs编码
     *
     * @param excelMap            excel记录（基线）
     * @param wbsDynamicFieldList wbs动态列
     * @return
     */
    public static String getWbsFullCode(Map excelMap, List<WbsDynamicFieldsDto> wbsDynamicFieldList) {
        // 唯一key = wbs动态列 + activity活动事项
        StringBuilder groupKey = new StringBuilder();
        for (int i = 0; i < wbsDynamicFieldList.size(); i++) {
            WbsDynamicFieldsDto wbsDynamicField = wbsDynamicFieldList.get(i);
            if (i != 0) {
                groupKey.append("-");
            }
            groupKey.append(excelMap.get(wbsDynamicField.getKey()));
        }
        return groupKey.toString();
    }

    /**
     * 获取wbs编码
     *
     * @param excelMap            excel记录（基线）
     * @param wbsDynamicFieldList wbs动态列
     * @return
     */
    public static String getCacheWbsFullCode(Map excelMap, List<WbsTemplateRuleCache> wbsDynamicFieldList) {
        // 唯一key = wbs动态列 + activity活动事项
        StringBuilder groupKey = new StringBuilder();
        for (int i = 0; i < wbsDynamicFieldList.size(); i++) {
            WbsTemplateRuleCache wbsDynamicField = wbsDynamicFieldList.get(i);
            if (i != 0) {
                groupKey.append("-");
            }
            String wbsDynamic = "" + excelMap.get(wbsDynamicField.getKey());
            groupKey.append(wbsDynamic.trim());
        }
        return groupKey.toString();
    }

    /**
     * 前端wbs预算合并到uniqueBudgetMap
     * 已删除的wbs预算合并到removeBudgetMapList
     *
     * @param pageBudgetMapList   前端wbs预算记录
     * @param uniqueBudgetMap     wbs预算Map，只存有效记录（增量或覆盖导入）
     * @param removeBudgetMapList 已删除的wbs预算
     * @param wbsDynamicFieldList wbs动态列
     */
    public static void wbsBudget2UniqueMap(List<Map<String, Object>> pageBudgetMapList, Map<String, Map> uniqueBudgetMap, List<Map> removeBudgetMapList, List<WbsDynamicFieldsDto> wbsDynamicFieldList) {
        if (!CollectionUtils.isEmpty(pageBudgetMapList)) {
            for (Map wbsBudget : pageBudgetMapList) {
                // 记录已被删除的wbs预算
                if (Boolean.TRUE.equals(MapUtils.getBoolean(wbsBudget, WbsBudgetFieldConstant.DELETED_FLAG))) {
                    removeBudgetMapList.add(wbsBudget);
                    continue;
                }

                String uniqueBudgetKey = getWbsBudgetUniqueKey(wbsBudget, wbsDynamicFieldList, null);
                if (uniqueBudgetMap.containsKey(uniqueBudgetKey)) {
                    String errorUniqueKey = getWbsBudgetUniqueKey(wbsBudget, wbsDynamicFieldList, "-");
                    logger.error("页面wbs预算数据异常，存在重复记录，请联系管理员：" + errorUniqueKey);
                    throw new ApplicationBizException("页面wbs预算数据异常，存在重复记录，请联系管理员：" + errorUniqueKey);
                }
                // 默认rowId
                wbsBudget.put(WbsBudgetFieldConstant.ROW_ID, wbsBudget.containsKey(WbsBudgetFieldConstant.ID) ? wbsBudget.get(WbsBudgetFieldConstant.ID) : UUID.randomUUID().toString());
                uniqueBudgetMap.put(uniqueBudgetKey, wbsBudget);
            }
        }
    }

    /**
     * 前端wbs基线合并到uniqueBaselineMap
     * 已删除的wbs基线合并到removeBaselineMapList
     *
     * @param pageBaselineMapList   前端wbs基线记录
     * @param uniqueBudgetMap       wbs预算Map，只存有效记录（增量或覆盖导入）
     * @param uniqueBaselineMap     wbs基线Map，只存有效记录（增量或覆盖导入）
     * @param removeBaselineMapList 已删除的wbs基线
     * @param wbsDynamicFieldList   wbs动态列
     */
    public static void wbsBaseline2UniqueMap(List<Map<String, Object>> pageBaselineMapList,
                                             Map<String, Map> uniqueBudgetMap,
                                             Map<String, Map> uniqueBaselineMap,
                                             List<Map> removeBaselineMapList,
                                             List<WbsDynamicFieldsDto> wbsDynamicFieldList) {
        if (!CollectionUtils.isEmpty(pageBaselineMapList)) {
            for (Map wbsBaseline : pageBaselineMapList) {
                // 记录已被删除的wbs预算
                if (Boolean.TRUE.equals(MapUtils.getBoolean(wbsBaseline, WbsBudgetFieldConstant.DELETED_FLAG))) {
                    removeBaselineMapList.add(wbsBaseline);
                    continue;
                }

                String uniqueBaselineKey = getWbsBaselineUniqueKey(wbsBaseline, wbsDynamicFieldList, MapUtils.getString(wbsBaseline, WbsBudgetFieldConstant.PROJECT_BASELINE_BATCH_CODE), null);
                if (uniqueBaselineMap.containsKey(uniqueBaselineKey)) {
                    String errorUniqueKey = getWbsBaselineUniqueKey(wbsBaseline, wbsDynamicFieldList, MapUtils.getString(wbsBaseline, WbsBudgetFieldConstant.PROJECT_BASELINE_BATCH_CODE), "-");
                    logger.error("页面wbs基线数据异常，存在重复记录，请联系管理员：" + errorUniqueKey);
                    throw new ApplicationBizException("页面wbs基线数据异常，存在重复记录，请联系管理员：" + errorUniqueKey);
                }
                // 默认rowId
                wbsBaseline.put(WbsBudgetFieldConstant.ROW_ID, wbsBaseline.containsKey(WbsBudgetFieldConstant.ID) ?
                        wbsBaseline.get(WbsBudgetFieldConstant.ID) : UUID.randomUUID().toString());

                // 设置wbs预算id
                String uniqueBudgetKey = getWbsBudgetUniqueKey(wbsBaseline, wbsDynamicFieldList, null);
                Map wbsBudget = uniqueBudgetMap.get(uniqueBudgetKey);
                if (StringUtils.isNotBlank(MapUtils.getString(wbsBudget, WbsBudgetFieldConstant.ROW_ID))) {
                    wbsBaseline.put(WbsBudgetFieldConstant.PROJECT_WBS_BUDGET_ID, MapUtils.getString(wbsBudget, WbsBudgetFieldConstant.ROW_ID));
                }
                uniqueBaselineMap.put(uniqueBaselineKey, wbsBaseline);
            }
        }
    }

    /**
     * excel记录转HashMap
     *
     * @param importExcelRows   excel行记录
     * @param activityCacheList activity活动事项
     * @param wbsCacheList      wbs动态列
     * @param batchCode         批次号
     * @param projectName       项目名称
     * @param projectCode       项目编码
     * @return
     */
    public static List<Map> wbsExcelRows2Map(List<List<String>> importExcelRows,
                                             List<ProjectActivityCache> activityCacheList,
                                             List<WbsTemplateRuleCache> wbsCacheList,
                                             String batchCode,
                                             String projectName,
                                             String projectCode) {
        List<Map> result = new ArrayList<>();
        Collections.sort(wbsCacheList);
        Map<String, ProjectActivityCache> activityCacheMap = activityCacheList.stream().collect(Collectors.toMap(ProjectActivityCache::getCode, Function.identity(), (key1, key2) -> key2));

        // 从excel第3行开始读，第3行是activity活动事项编码列
        List<String> activityTitle = importExcelRows.get(0);

        for (int i = 1; i < importExcelRows.size(); i++) {
            Map wbsMap = new HashMap();

            // excel行记录 标题（wbs+activity） 内容（wbs+price）
            List<String> excelRow = importExcelRows.get(i);

            // 读取wbs动态列
            for (int j = 0; j < wbsCacheList.size(); j++) {
                // key=动态列key:field_2  value=wbs编码
                wbsMap.put(wbsCacheList.get(j).getKey(), StringUtils.isNotBlank(excelRow.get(j)) ? excelRow.get(j).trim() : null);
            }
            Map excelMap;
            // 读取activity活动事项
            for (int k = wbsCacheList.size(); k < excelRow.size(); k++) {
                excelMap = new HashMap();
                excelMap.putAll(wbsMap);

                // 价格为空直接跳过
                if (StringUtils.isEmpty(excelRow.get(k))) {
                    continue;
                }
                if (activityCacheMap.containsKey(activityTitle.get(k))) {
                    ProjectActivityCache activityCache = activityCacheMap.get(activityTitle.get(k));
                    // 活动事项编码
                    excelMap.put(WbsBudgetFieldConstant.ACTIVITY_CODE, activityCache.getCode());
                    // 活动类别名称
                    excelMap.put(WbsBudgetFieldConstant.ACTIVITY_NAME, activityCache.getName());
                    // 活动类别属性
                    excelMap.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, activityCache.getType());
                    // 活动事项序号
                    excelMap.put(WbsBudgetFieldConstant.ACTIVITY_ORDER_NO, activityCache.getOrderNo());

                    excelMap.put(activityCache.getName(), k + 1);//根据标题存入列
                }
                // 预算金额
                excelMap.put(WbsBudgetFieldConstant.BASELINE_COST, excelRow.get(k));
                // 项目名称
                excelMap.put(WbsBudgetFieldConstant.PROJECT_NAME, projectName);
                // 项目编码
                excelMap.put(WbsBudgetFieldConstant.PROJECT_CODE, projectCode);
                // 批次号
                excelMap.put(WbsBudgetFieldConstant.PROJECT_BASELINE_BATCH_CODE, batchCode);
                // excel行数
                excelMap.put(WbsBudgetFieldConstant.EXCEL_ROW_NUM, (i + 3));
                result.add(excelMap);
            }
        }
        return result;
    }

    /**
     * 获取符合条件的活动事项缓存
     * 预算事项=是 当前时间<结束时间
     *
     * @return
     */
    public static List<ProjectActivityCache> getEligibilityActivityCache() {
        List<ProjectActivityCache> projectActivityCaches = ProjectActivityCacheUtils.listCache(SystemContext.getUnitId());
        if (!CollectionUtils.isEmpty(projectActivityCaches)) {
            projectActivityCaches = projectActivityCaches.stream().filter(a -> {
                        if (ProjectWbsCostConstant.SYSTEM_ACTIVITY.equals(a.getCode())) {
                            return false;
                        }
                        if (!Boolean.TRUE.equals(a.getBudgetMattersState())) {
                            return false;
                        }
                        if (null != a.getEndTime() && a.getEndTime().before(new Date())) {
                            return false;
                        }
                        return true;
                    }
            ).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(projectActivityCaches)) {
            throw new ApplicationBizException("当前单位无法匹配有效的activity活动事项，请检查系统配置");
        }
        // 按orderNo升序
        Collections.sort(projectActivityCaches);

        return projectActivityCaches;
    }

    /**
     * 获取符合条件的wbs缓存
     *
     * @param wbsTemplateInfoId
     * @return
     */
    public static List<WbsTemplateRuleCache> getEligibilityWbsCache(Long wbsTemplateInfoId) {
        // 获取wbs动态列
        List<WbsTemplateRuleCache> wbsTemplateRuleCaches = WbsTemplateRuleCacheUtils.listCache(wbsTemplateInfoId);
        if (CollectionUtils.isEmpty(wbsTemplateRuleCaches)) {
            throw new ApplicationBizException("当前项目无法匹配有效的wbs模板，请检查系统配置");
        }
        Collections.sort(wbsTemplateRuleCaches);
        // 删除项目层
        wbsTemplateRuleCaches.remove(0);
        return wbsTemplateRuleCaches;
    }

    /**
     * 四舍五入金额
     *
     * @param data  预算记录
     * @param field 四舍五入字段
     */
    public static void roundingHalfUp(List<Map> data, String field) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        for (Map map : data) {
            BigDecimal roundingHalfUpAmount = !map.containsKey(field) ? BigDecimal.ZERO : new BigDecimal(MapUtils.getString(map, field));
            map.put(field, roundingHalfUpAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        }
    }

    /**
     * 四舍五入金额
     *
     * @param map    预算记录
     * @param field  四舍五入字段
     * @param amount 原金额
     */
    public static void roundingHalfUp(Map map, String field, BigDecimal amount) {
        BigDecimal roundingHalfUpAmount = BigDecimal.ZERO;
        if (null != amount) {
            roundingHalfUpAmount = amount;
        }
        map.put(field, roundingHalfUpAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
    }

    /**
     * WBS预算变更-批量修改预算金额 专用
     *
     * @param importExcelRows
     * @param wbsCacheList
     * @param projectWbsBudgetDtoList
     * @return
     */
    public static Map<String, Object> wbsExcelRows2Map(List<List<String>> importExcelRows,
                                                       List<WbsTemplateRuleCache> wbsCacheList,
                                                       List<ProjectWbsBudgetDto> projectWbsBudgetDtoList) {
        Map<String, List<ProjectWbsBudgetDto>> projectWbsBudgetDtoMap = projectWbsBudgetDtoList.stream().collect(Collectors.groupingBy(s -> buildProjectWbsBudgetKey(s.getWbsSummaryCode(), s.getActivityCode())));
        List<String> titleList = importExcelRows.get(0);

        List<Map<String, Object>> dataList = new ArrayList<>();
        List<String> errMsgList = new ArrayList<>();
        List<List<String>> validExcelRows = new ArrayList<>();
        for (int i = 1; i < importExcelRows.size(); i++) {
            List<String> excelRow = importExcelRows.get(i);
            //序号
            String orderNo = excelRow.get(0);
            //项目号
            String projectCode = excelRow.get(1);
            //WBS全码
            String wbsSummaryCode = excelRow.get(2 + wbsCacheList.size());
            //活动事项编码
            String activityCode = excelRow.get(2 + wbsCacheList.size() + 2);
            //变更后预算金额
            String afterChangePriceStr = excelRow.get(excelRow.size() - 1);
            if (StringUtils.isEmpty(afterChangePriceStr)) {
                continue;
            }
            validExcelRows.add(excelRow);

            if (StringUtils.isEmpty(projectCode)) {
                errMsgList.add(String.format("序号【%s】，项目号不能为空", orderNo));
                continue;
            }
            if (StringUtils.isEmpty(activityCode)) {
                errMsgList.add(String.format("序号【%s】，活动事项编码不能为空", orderNo));
                continue;
            }
            if (StringUtils.isEmpty(wbsSummaryCode)) {
                errMsgList.add(String.format("序号【%s】，WBS号不能为空", orderNo));
                continue;
            }
            String[] wbsCodeSplit = wbsSummaryCode.split("-");
            if (wbsCodeSplit.length < wbsCacheList.size()) {
                errMsgList.add(String.format("序号【%s】，WBS号填写有误", orderNo));
                continue;
            }

            // 读取wbs动态列
            Map wbsMap = new HashMap();
            for (int j = 0; j < wbsCacheList.size(); j++) {
                // key=动态列key:field_2  value=wbs编码
                String wbsCode = excelRow.get(j + 2);
                if (StringUtils.isEmpty(wbsCode)) {
                    errMsgList.add(String.format("序号【%s】，%s不能为空", orderNo, titleList.get(j + 2)));
                } else {
                    if (!Objects.equals(wbsCode, wbsCodeSplit[wbsCodeSplit.length - wbsCacheList.size() + j])) {
                        errMsgList.add(String.format("序号【%s】，%s填写有误", orderNo, titleList.get(j + 2)));
                    }
                    wbsMap.put(wbsCacheList.get(j).getKey(), wbsCode.trim());
                }
            }

            for (int j = 0; j < wbsCacheList.size(); j++) {
                // key=动态列key:field_2  value=wbs编码
                wbsMap.put(wbsCacheList.get(j).getKey(), StringUtils.isNotBlank(excelRow.get(j + 2)) ? excelRow.get(j + 2).trim() : null);
            }

            String key = buildProjectWbsBudgetKey(wbsSummaryCode, activityCode);
            List<ProjectWbsBudgetDto> lists = projectWbsBudgetDtoMap.get(key);
            if (CollectionUtils.isEmpty(lists)) {
                errMsgList.add(String.format("序号【%s】，WBS+活动事项不存在", orderNo));
            } else if (lists.size() > 1) {
                errMsgList.add(String.format("序号【%s】，WBS+活动事项存在2条及以上", orderNo));
            } else {
                Map<String, Object> data = BeanMapTool.beanToMap(lists.get(0));
                if (BigDecimalUtils.isBigDecimal(afterChangePriceStr)) {
                    BigDecimal afterChangePrice = new BigDecimal(afterChangePriceStr);
                    if (afterChangePrice.compareTo(BigDecimal.ZERO) < 0 || afterChangePrice.scale() > 2) {
                        errMsgList.add(String.format("序号【%s】，只能填写2位小数的正数", orderNo));
                    } else {
                        data.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, afterChangePrice);
                    }
                } else {
                    errMsgList.add(String.format("序号【%s】，只能填写2位小数的正数", orderNo));
                }
                data.putAll(wbsMap);
                dataList.add(data);
            }
        }
        if (CollectionUtils.isEmpty(validExcelRows)) {
            throw new BizException(Code.ERROR, "当前导入Excel模板中无法匹配有效的记录，请检查");
        }

        Map<String, Object> resultMap = new HashMap();
        resultMap.put("flag", CollectionUtils.isEmpty(errMsgList));
        resultMap.put("errMsg", errMsgList);
        if (CollectionUtils.isEmpty(errMsgList)) {
            resultMap.put("dataList", dataList);
        }
        return resultMap;
    }

    private static String buildProjectWbsBudgetKey(String wbsSummaryCode, String activityCode) {
        return String.format("%s_buildProjectWbsBudgetKey_%s", wbsSummaryCode, activityCode);
    }
}
