package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.PurchaseContractStandardTerms;
import com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviation;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class PurchaseContractStandardTermsDto extends PurchaseContractStandardTerms {

    /**
     * 采购订单遍历项信息
     */
    private List<PurchaseContractStandardTermsDeviation> standardTermsDeviationList;
}
