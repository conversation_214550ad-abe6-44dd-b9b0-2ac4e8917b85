package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "采购订单")
public class PurchaseOrder extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "订单编号")
    private String num;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "批准供应商id")
    private Long vendorAslId;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商地点")
    private String vendorSiteCode;

    @ApiModelProperty(value = "采购员id")
    private Long buyerId;

    @ApiModelProperty(value = "采购员名称")
    private String buyerName;

    @ApiModelProperty(value = "订单状态=1：生效，2：取消，3：关闭，4：冻结，5：暂挂，6：草稿，7：审批中，8：驳回，9：作废，10：撤回，11：变更中")
    private Integer orderStatus;

    @ApiModelProperty(value = "同步ERP=0：未同步，1：已同步，2：同步失败，3：同步中，4：变更同步中，5：变更同步失败")
    private Integer syncStatus;

    @ApiModelProperty(value = "0：有效， 1：删除")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "ERP失败原因")
    private String erpMessage;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "汇率类型")
    private String conversionType;

    @ApiModelProperty(value = "汇率时间")
    private Date conversionDate;

    @ApiModelProperty(value = "汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "来源:非wbs:1;wbs:2")
    private String source;

    @ApiModelProperty(value = "（弃用！！）")
    private Integer status;

    @ApiModelProperty(value = "erp订单状态=1-未发布 2-已发布 3-已回签")
    private Integer erpOrderStatus;

    @ApiModelProperty(value = "是否急件(0:否;1是)")
    private Boolean dispatchIs;

    @ApiModelProperty(value = "单据id")
    private Long receiptsId;

    @ApiModelProperty(value = "承诺日期")
    private Date promisedDate;

    @ApiModelProperty(value = "跟踪日期")
    private Date trackingDate;

    @ApiModelProperty(value = "（弃用！！）")
    private Long orgId;

    @ApiModelProperty(value = "业务实体名称")
    private String orgName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorNum;

    @ApiModelProperty(value = "付款方式")
    private Long paymentMethodId;

    @ApiModelProperty(value = "付款方式名称")
    private String paymentMethodName;

    @ApiModelProperty(value = "付款方法")
    private String paymentWay;

    @ApiModelProperty(value = "交货方式")
    private String deliveryType;

    @ApiModelProperty(value = "交货条款")
    private String deliveryClause;

    @ApiModelProperty(value = "税率ID")
    private Long taxId;

    @ApiModelProperty(value = "税率")
    private String taxRate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "定价类型(1:一单一价;2:一揽子协议价)")
    private Integer pricingType;

    @ApiModelProperty(value = "（弃用！！）")
    private String currencyCode;

    @ApiModelProperty(value = "（弃用！！）")
    private BigDecimal exchangeRate;

    @ApiModelProperty(value = "审批信息(格式：节点@mipName) 多个,分割")
    private String approveInfo;

    @ApiModelProperty(value = "erp采购员id")
    private Long erpBuyerId;

    @ApiModelProperty(value = "是否初始化标识：1-是，0或null-否")
    private Integer initFlag;

    @ApiModelProperty(value = "业务实体id")
    private Long ouId;

    @ApiModelProperty(value = "接收子库")
    private String secondaryInventory;

    @ApiModelProperty(value = "接收子库代码")
    private String secondaryInventoryName;

    @ApiModelProperty(value = "审批日期")
    private Date approvalTime;

    @ApiModelProperty(value = "同步来源系统")
    private String syncSourceSystem;

    @ApiModelProperty(value = "合同条款类型(0：非标准条款；1：标准条款)")
    private String contractTermsFlg;

    @ApiModelProperty(value = "同步到FAP系统 0-否，1-是")
    private Boolean syncToFap;

    @ApiModelProperty(value = "合同条款")
    private String contractTerms;

    @ApiModelProperty(value = "超预算说明")
    private String overBudgetDes;

    private static final long serialVersionUID = 1L;

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num == null ? null : num.trim();
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getVendorAslId() {
        return vendorAslId;
    }

    public void setVendorAslId(Long vendorAslId) {
        this.vendorAslId = vendorAslId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    public String getVendorSiteCode() {
        return vendorSiteCode;
    }

    public void setVendorSiteCode(String vendorSiteCode) {
        this.vendorSiteCode = vendorSiteCode == null ? null : vendorSiteCode.trim();
    }

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId = buyerId;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getErpMessage() {
        return erpMessage;
    }

    public void setErpMessage(String erpMessage) {
        this.erpMessage = erpMessage == null ? null : erpMessage.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getConversionType() {
        return conversionType;
    }

    public void setConversionType(String conversionType) {
        this.conversionType = conversionType == null ? null : conversionType.trim();
    }

    public Date getConversionDate() {
        return conversionDate;
    }

    public void setConversionDate(Date conversionDate) {
        this.conversionDate = conversionDate;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getErpOrderStatus() {
        return erpOrderStatus;
    }

    public void setErpOrderStatus(Integer erpOrderStatus) {
        this.erpOrderStatus = erpOrderStatus;
    }

    public Boolean getDispatchIs() {
        return dispatchIs;
    }

    public void setDispatchIs(Boolean dispatchIs) {
        this.dispatchIs = dispatchIs;
    }

    public Long getReceiptsId() {
        return receiptsId;
    }

    public void setReceiptsId(Long receiptsId) {
        this.receiptsId = receiptsId;
    }

    public Date getPromisedDate() {
        return promisedDate;
    }

    public void setPromisedDate(Date promisedDate) {
        this.promisedDate = promisedDate;
    }

    public Date getTrackingDate() {
        return trackingDate;
    }

    public void setTrackingDate(Date trackingDate) {
        this.trackingDate = trackingDate;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    public String getVendorNum() {
        return vendorNum;
    }

    public void setVendorNum(String vendorNum) {
        this.vendorNum = vendorNum == null ? null : vendorNum.trim();
    }

    public Long getPaymentMethodId() {
        return paymentMethodId;
    }

    public void setPaymentMethodId(Long paymentMethodId) {
        this.paymentMethodId = paymentMethodId;
    }

    public String getPaymentMethodName() {
        return paymentMethodName;
    }

    public void setPaymentMethodName(String paymentMethodName) {
        this.paymentMethodName = paymentMethodName == null ? null : paymentMethodName.trim();
    }

    public String getPaymentWay() {
        return paymentWay;
    }

    public void setPaymentWay(String paymentWay) {
        this.paymentWay = paymentWay == null ? null : paymentWay.trim();
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType == null ? null : deliveryType.trim();
    }

    public String getDeliveryClause() {
        return deliveryClause;
    }

    public void setDeliveryClause(String deliveryClause) {
        this.deliveryClause = deliveryClause == null ? null : deliveryClause.trim();
    }

    public Long getTaxId() {
        return taxId;
    }

    public void setTaxId(Long taxId) {
        this.taxId = taxId;
    }

    public String getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(String taxRate) {
        this.taxRate = taxRate == null ? null : taxRate.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getPricingType() {
        return pricingType;
    }

    public void setPricingType(Integer pricingType) {
        this.pricingType = pricingType;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode == null ? null : currencyCode.trim();
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getApproveInfo() {
        return approveInfo;
    }

    public void setApproveInfo(String approveInfo) {
        this.approveInfo = approveInfo == null ? null : approveInfo.trim();
    }

    public Long getErpBuyerId() {
        return erpBuyerId;
    }

    public void setErpBuyerId(Long erpBuyerId) {
        this.erpBuyerId = erpBuyerId;
    }

    public Integer getInitFlag() {
        return initFlag;
    }

    public void setInitFlag(Integer initFlag) {
        this.initFlag = initFlag;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getSecondaryInventory() {
        return secondaryInventory;
    }

    public void setSecondaryInventory(String secondaryInventory) {
        this.secondaryInventory = secondaryInventory == null ? null : secondaryInventory.trim();
    }

    public String getSecondaryInventoryName() {
        return secondaryInventoryName;
    }

    public void setSecondaryInventoryName(String secondaryInventoryName) {
        this.secondaryInventoryName = secondaryInventoryName == null ? null : secondaryInventoryName.trim();
    }

    public Date getApprovalTime() {
        return approvalTime;
    }

    public void setApprovalTime(Date approvalTime) {
        this.approvalTime = approvalTime;
    }

    public String getSyncSourceSystem() {
        return syncSourceSystem;
    }

    public void setSyncSourceSystem(String syncSourceSystem) {
        this.syncSourceSystem = syncSourceSystem == null ? null : syncSourceSystem.trim();
    }

    public String getContractTermsFlg() {
        return contractTermsFlg;
    }

    public void setContractTermsFlg(String contractTermsFlg) {
        this.contractTermsFlg = contractTermsFlg == null ? null : contractTermsFlg.trim();
    }

    public Boolean getSyncToFap() {
        return syncToFap;
    }

    public void setSyncToFap(Boolean syncToFap) {
        this.syncToFap = syncToFap;
    }

    public String getContractTerms() {
        return contractTerms;
    }

    public void setContractTerms(String contractTerms) {
        this.contractTerms = contractTerms == null ? null : contractTerms.trim();
    }

    public String getOverBudgetDes() {
        return overBudgetDes;
    }

    public void setOverBudgetDes(String overBudgetDes) {
        this.overBudgetDes = overBudgetDes == null ? null : overBudgetDes.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", num=").append(num);
        sb.append(", projectId=").append(projectId);
        sb.append(", vendorAslId=").append(vendorAslId);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", vendorSiteCode=").append(vendorSiteCode);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", syncStatus=").append(syncStatus);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", erpMessage=").append(erpMessage);
        sb.append(", currency=").append(currency);
        sb.append(", conversionType=").append(conversionType);
        sb.append(", conversionDate=").append(conversionDate);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", source=").append(source);
        sb.append(", status=").append(status);
        sb.append(", erpOrderStatus=").append(erpOrderStatus);
        sb.append(", dispatchIs=").append(dispatchIs);
        sb.append(", receiptsId=").append(receiptsId);
        sb.append(", promisedDate=").append(promisedDate);
        sb.append(", trackingDate=").append(trackingDate);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgName=").append(orgName);
        sb.append(", vendorNum=").append(vendorNum);
        sb.append(", paymentMethodId=").append(paymentMethodId);
        sb.append(", paymentMethodName=").append(paymentMethodName);
        sb.append(", paymentWay=").append(paymentWay);
        sb.append(", deliveryType=").append(deliveryType);
        sb.append(", deliveryClause=").append(deliveryClause);
        sb.append(", taxId=").append(taxId);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", remark=").append(remark);
        sb.append(", pricingType=").append(pricingType);
        sb.append(", currencyCode=").append(currencyCode);
        sb.append(", exchangeRate=").append(exchangeRate);
        sb.append(", approveInfo=").append(approveInfo);
        sb.append(", erpBuyerId=").append(erpBuyerId);
        sb.append(", initFlag=").append(initFlag);
        sb.append(", ouId=").append(ouId);
        sb.append(", secondaryInventory=").append(secondaryInventory);
        sb.append(", secondaryInventoryName=").append(secondaryInventoryName);
        sb.append(", approvalTime=").append(approvalTime);
        sb.append(", syncSourceSystem=").append(syncSourceSystem);
        sb.append(", contractTermsFlg=").append(contractTermsFlg);
        sb.append(", syncToFap=").append(syncToFap);
        sb.append(", contractTerms=").append(contractTerms);
        sb.append(", overBudgetDes=").append(overBudgetDes);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}