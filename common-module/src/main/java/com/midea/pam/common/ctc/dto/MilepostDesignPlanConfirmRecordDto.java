package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel(value = "MilepostDesignPlanConfirmRecordDto", description = "里程碑详细设计方案提交记录")
@Getter
@Setter
public class MilepostDesignPlanConfirmRecordDto extends MilepostDesignPlanConfirmRecord {

    @ApiModelProperty("确认人")
    private String confirmName;

    private Boolean statusIsNull;

    @ApiModelProperty("设计信息关联")
    private List<MilepostDesignPlanConfirmRecordRelationDto> milepostDesignPlanConfirmRecordRelationDtos;

    private Long formInstanceId;

    private String fdInstanceId;

    private String formUrl;

    private String eventName;

    private String handlerId;

    private Long companyId;

    private Long createUserId;

    @ApiModelProperty(value = "详细设计是否已完成(是：立项通过则模组确认)")
    private Boolean confirmRecordFlag;
}