package com.midea.pam.common.basedata.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "")
public class DiffCompanyLaborCostSet extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "项目使用单位ID")
    private Long projectCompanyId;

    @ApiModelProperty(value = "出勤人使用单位ID")
    private Long userCompanyId;

    @ApiModelProperty(value = "业务实体id")
    private Long ouId;

    @ApiModelProperty(value = "业务实体名称")
    private String ouName;

    @ApiModelProperty(value = "工时入账结转科目")
    private String hardWorking;

    @ApiModelProperty(value = "出勤人发薪OU")
    private Long fullPayOuId;

    @ApiModelProperty(value = "出勤人发薪OU名称")
    private String fullPayOuName;

    @ApiModelProperty(value = "删除标示")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getProjectCompanyId() {
        return projectCompanyId;
    }

    public void setProjectCompanyId(Long projectCompanyId) {
        this.projectCompanyId = projectCompanyId;
    }

    public Long getUserCompanyId() {
        return userCompanyId;
    }

    public void setUserCompanyId(Long userCompanyId) {
        this.userCompanyId = userCompanyId;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public String getHardWorking() {
        return hardWorking;
    }

    public void setHardWorking(String hardWorking) {
        this.hardWorking = hardWorking == null ? null : hardWorking.trim();
    }

    public Long getFullPayOuId() {
        return fullPayOuId;
    }

    public void setFullPayOuId(Long fullPayOuId) {
        this.fullPayOuId = fullPayOuId;
    }

    public String getFullPayOuName() {
        return fullPayOuName;
    }

    public void setFullPayOuName(String fullPayOuName) {
        this.fullPayOuName = fullPayOuName == null ? null : fullPayOuName.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectCompanyId=").append(projectCompanyId);
        sb.append(", userCompanyId=").append(userCompanyId);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", hardWorking=").append(hardWorking);
        sb.append(", fullPayOuId=").append(fullPayOuId);
        sb.append(", fullPayOuName=").append(fullPayOuName);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}