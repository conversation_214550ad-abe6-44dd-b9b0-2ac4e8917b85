package com.midea.pam.common.ctc.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DetailByProjectIdAndMilepostIdNewWbsQuery {

    //项目id
    private Long projectId;

    //物料描述
    private String materielDescr;

    //pam物料编码
    private String pamCode;

    //erp物料编码
    private String erpCode;

    //wbs层级
    private String wbsLayer;

    //wbs
    private String wbsSummaryCode;

    @ApiModelProperty(value = "是否急件(0否 1是)")
    private Boolean dispatchIs;

    @ApiModelProperty(value = "是否外包")
    private Boolean extIs;

    @ApiModelProperty(value = "模组状态:0:未确认;1:已确认")
    private Integer moduleStatus;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "需求类型,多个逗号切割")  //需求类型：3：安装分包，4：调试、测量分包，5：设计分包，6：标准设备，7：定制设备整包，8：设备备件，9：项目整包，10：机加整包，11：机加散件，12：MRO，13：标准件
    private String requirementTypeStr;

    @ApiModelProperty("采购需求表的requirement_code")
    private String purchaseMaterialRequirementReceiptsCode;

    @ApiModelProperty("设计人员")
    private String planDesigner;

    @ApiModelProperty("是否变更查询")
    private Integer editQuery;

    @ApiModelProperty("需求编号")
    private String requirementCode;
}
