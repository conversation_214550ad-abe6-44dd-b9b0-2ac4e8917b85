package com.midea.pam.common.basedata.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "")
public class SpecialCharacterConfig extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "配置名称，用于标识配置用途")
    private String configName;

    @ApiModelProperty(value = "数据库名称，如：pam_basedata、pam_ctc等")
    private String databaseName;

    @ApiModelProperty(value = "表名称，需要进行特殊字符处理的表")
    private String tableName;

    @ApiModelProperty(value = "字段名称，需要进行特殊字符处理的字段")
    private String columnName;

    @ApiModelProperty(value = "是否启用该配置：1-启用，0-禁用")
    private Boolean enabled;

    @ApiModelProperty(value = "处理规则：REPLACE_WITH_SPACE-替换为空格，REMOVE-删除，CUSTOM-自定义")
    private String processingRule;

    @ApiModelProperty(value = "替换值，当processing_rule为CUSTOM时使用")
    private String replacementValue;

    @ApiModelProperty(value = "是否处理XML不兼容字符：1-是，0-否")
    private Boolean handleXmlInvalid;

    @ApiModelProperty(value = "是否处理4字节UTF8字符（如emoji）：1-是，0-否")
    private Boolean handleUtf84byte;

    @ApiModelProperty(value = "是否处理其他特殊字符：1-是，0-否")
    private Boolean handleOtherSpecial;

    @ApiModelProperty(value = "优先级，数值越小优先级越高，用于排序")
    private Integer priority;

    @ApiModelProperty(value = "配置描述，说明该配置的用途和注意事项")
    private String description;

    @ApiModelProperty(value = "删除标示")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName == null ? null : configName.trim();
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName == null ? null : databaseName.trim();
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName == null ? null : tableName.trim();
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName == null ? null : columnName.trim();
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getProcessingRule() {
        return processingRule;
    }

    public void setProcessingRule(String processingRule) {
        this.processingRule = processingRule == null ? null : processingRule.trim();
    }

    public String getReplacementValue() {
        return replacementValue;
    }

    public void setReplacementValue(String replacementValue) {
        this.replacementValue = replacementValue == null ? null : replacementValue.trim();
    }

    public Boolean getHandleXmlInvalid() {
        return handleXmlInvalid;
    }

    public void setHandleXmlInvalid(Boolean handleXmlInvalid) {
        this.handleXmlInvalid = handleXmlInvalid;
    }

    public Boolean getHandleUtf84byte() {
        return handleUtf84byte;
    }

    public void setHandleUtf84byte(Boolean handleUtf84byte) {
        this.handleUtf84byte = handleUtf84byte;
    }

    public Boolean getHandleOtherSpecial() {
        return handleOtherSpecial;
    }

    public void setHandleOtherSpecial(Boolean handleOtherSpecial) {
        this.handleOtherSpecial = handleOtherSpecial;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", configName=").append(configName);
        sb.append(", databaseName=").append(databaseName);
        sb.append(", tableName=").append(tableName);
        sb.append(", columnName=").append(columnName);
        sb.append(", enabled=").append(enabled);
        sb.append(", processingRule=").append(processingRule);
        sb.append(", replacementValue=").append(replacementValue);
        sb.append(", handleXmlInvalid=").append(handleXmlInvalid);
        sb.append(", handleUtf84byte=").append(handleUtf84byte);
        sb.append(", handleOtherSpecial=").append(handleOtherSpecial);
        sb.append(", priority=").append(priority);
        sb.append(", description=").append(description);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}