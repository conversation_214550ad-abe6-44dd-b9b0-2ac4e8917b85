package com.midea.pam.common.ctc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 里程碑详细设计方案自定义导出请求DTO
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Getter
@Setter
@ApiModel(value = "MilepostDesignPlanExportRequestDto", description = "里程碑详细设计方案自定义导出请求")
public class MilepostDesignPlanExportRequestDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("序号")
    private String orderNo;

    @ApiModelProperty("集成外包")
    private Boolean ext;

    @ApiModelProperty("物料描述")
    private String materielDescr;

    @ApiModelProperty("PAM物料编码")
    private String pamCode;

    @ApiModelProperty("ERP物料编码")
    private String erpCode;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("物料类型")
    private String materialCategory;

    @ApiModelProperty("采购需求总数")
    private BigDecimal totalNum;

    @ApiModelProperty("单套数量")
    private BigDecimal number;

    @ApiModelProperty("已生成采购需求数量")
    private BigDecimal requirementNum;

    @ApiModelProperty("设计成本")
    private BigDecimal designCost;

    @ApiModelProperty("设计小计")
    private BigDecimal designTotal;

    @ApiModelProperty("预算单价")
    private BigDecimal budgetUnitPrice;

    @ApiModelProperty("预算小计")
    private BigDecimal budgetSubtotal;

    @ApiModelProperty("交货日期")
    private Date deliveryTime;

    @ApiModelProperty("设计成本是否来源估价")
    private Boolean itemCostIsNull;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("型号/规格/图号")
    private String model;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("物料大类")
    private String materialClassification;

    @ApiModelProperty("物料中类")
    private String codingMiddleClass;

    @ApiModelProperty("物料小类")
    private String materielType;

    @ApiModelProperty("加工件分类")
    private String machiningPartType;

    @ApiModelProperty("材质")
    private String material;

    @ApiModelProperty("图号")
    private String figureNumber;

    @ApiModelProperty("图纸版本号")
    private String chartVersion;

    @ApiModelProperty("备件标识")
    private String orSparePartsMask;

    @ApiModelProperty("品牌商物料编码")
    private String brandMaterialCode;

    @ApiModelProperty("单位重量(Kg)")
    private BigDecimal unitWeight;

    @ApiModelProperty("材质处理")
    private String materialProcessing;

    @ApiModelProperty("来源")
    private String source;

    @ApiModelProperty("删除标识")
    private Boolean deletedFlag;
}
