package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MaterialTransferDetailTempExcelVo {

        @ExcelField(title = "物料编码", order = 1)
        private String erpCode;

        @ExcelField(title = "申请转移数量", order = 2)
        private String applyNum;

        @ExcelField(title = "备注", order = 3)
        private String remark;

        //申请领料数量
        private BigDecimal applyAmount;
}
