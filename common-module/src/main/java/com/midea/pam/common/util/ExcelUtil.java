package com.midea.pam.common.util;

import com.alibaba.fastjson.JSONArray;
import net.sf.json.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jeecgframework.poi.exception.excel.ExcelExportException;
import org.jeecgframework.poi.exception.excel.enums.ExcelExportEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * daigen
 */
public class ExcelUtil {

    private static Logger logger = LoggerFactory.getLogger(ExcelUtil.class);

    public static Workbook createWorkBook(String fileName) {

        Workbook workbook;
        workbook = new HSSFWorkbook();
        if (StringUtils.isNotEmpty(fileName)) {
            if (fileName.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(); // Excel 2007
            } else if (fileName.endsWith(".xls")) {
                workbook = new HSSFWorkbook(); // Excel 2003
            }
        }
        return workbook;
    }

    public static Sheet createSheet(Workbook workbook, String sheetName) {
        if (workbook == null) {
            throw new ExcelExportException(ExcelExportEnum.PARAMETER_ERROR);
        }
        Sheet sheet = null;
        try {
            sheet = workbook.createSheet(sheetName);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new ExcelExportException(ExcelExportEnum.EXPORT_ERROR, e.getCause());
        }
        return sheet;
    }

    public static CellStyle creatTitleStyle(Workbook workbook) {
        CellStyle titleStyle = workbook.createCellStyle();
        // 设置字体大小 位置
        titleStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        Font font_title = workbook.createFont();
        //设置字体
        font_title.setFontName("微软雅黑");
        font_title.setColor(HSSFColor.BLACK.index);// HSSFColor.VIOLET.index
        font_title.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        //字体颜色
        font_title.setFontHeightInPoints((short) 10);

        //font_title.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD); // 字体增粗
        titleStyle.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色
        titleStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        titleStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        titleStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        titleStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中
        titleStyle.setFont(font_title);
        return titleStyle;
    }

    public static CellStyle createTitleStyle(Workbook workbook, Short color) {
        CellStyle titleStyle = workbook.createCellStyle();
        // 设置字体大小 位置
        titleStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        Font font_title = workbook.createFont();
        //设置字体
        font_title.setFontName("微软雅黑");
        font_title.setColor(HSSFColor.BLACK.index);// HSSFColor.VIOLET.index
        font_title.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        //字体颜色
        if (color != null) {
            font_title.setColor(color);
        }
        font_title.setFontHeightInPoints((short) 10);

        //font_title.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD); // 字体增粗
        titleStyle.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色
        titleStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        titleStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        titleStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        titleStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中
        titleStyle.setFont(font_title);
        return titleStyle;
    }

    public static CellStyle creatCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        Font font_type = workbook.createFont();
        //设置字体
        font_type.setFontName("微软雅黑");
        //style.setWrapText(true); // 自动换行
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中
        style.setDataFormat(workbook.createDataFormat().getFormat("@")); //文本格式

        return style;
    }

    public static void setExcelData(Sheet sheet, JSONArray dataAraay, LinkedHashMap<String, String> map, String title, String title2, String[] title2Array, CellStyle titleStyle, CellStyle style) {
        int rowNum = 0;
        //标题
        LinkedList list = new LinkedList();
        Iterator<String> iterator = map.keySet().iterator();


        if (StringUtils.isNotEmpty(title)) {
            Row row = sheet.createRow(rowNum);
            Cell cell = row.createCell(0);
            cell.setCellValue(title);
            cell.setCellStyle(titleStyle);
            rowNum++;
        }
        if (StringUtils.isNotEmpty(title2)) {
            Row row = sheet.createRow(rowNum);
            Cell cell = row.createCell(0);
            cell.setCellValue(title2);
            cell.setCellStyle(titleStyle);
            rowNum++;
        }
        if (null != title2Array) {
            if (title2Array.length > 0) {
                Row row = sheet.createRow(rowNum);
                String beforCellVal = "";
                int beforCellValNumber = 0;
                for (int i = 0; i < title2Array.length; i++) {
                    Cell cell = row.createCell(i);
                    cell.setCellValue(title2Array[i]);
                    cell.setCellStyle(titleStyle);
                    if (beforCellVal.equals(title2Array[i])) {
                        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, beforCellValNumber, i));
                    } else {
                        beforCellValNumber = i;
                        beforCellVal = title2Array[i];
                    }

                }
                rowNum++;
            }
        }
        Row row = sheet.createRow(rowNum);
        int i = 0;

        while (iterator.hasNext()) {
            String key = iterator.next();
            Cell cell = row.createCell(i);
            cell.setCellValue(map.get(key));
            cell.setCellStyle(titleStyle);
            list.add(key);
            i++;
        }
        int cellNum = list.size() - 1;
        int titleRow = 0;
        if (StringUtils.isNotEmpty(title)) {
            Row cellRow = sheet.getRow(titleRow);
            for (int j = 1; j < cellNum; j++) {
                Cell cell = cellRow.createCell(j);
                cell.setCellStyle(titleStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(titleRow, titleRow, 0, cellNum));
            titleRow++;
        }
        if (StringUtils.isNotEmpty(title2)) {
            Row cellRow = sheet.getRow(titleRow);
            for (int j = 1; j < cellNum; j++) {
                Cell cell = cellRow.createCell(j);
                cell.setCellStyle(titleStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(titleRow, titleRow, 0, cellNum));
            titleRow++;
        }
        rowNum++;
        //数据
        setJsonData(0, rowNum, sheet, list, dataAraay, style);
    }

    public static void setExcelData(Sheet sheet, JSONArray dataAraay, LinkedHashMap<String, String> map, String title, String title2, String[] title2Array, CellStyle titleStyle, CellStyle style, Integer startRowNum) {
        int rowNum = startRowNum == null ? 0 : startRowNum;

        //标题
        LinkedList list = new LinkedList();
        Iterator<String> iterator = map.keySet().iterator();


        if (StringUtils.isNotEmpty(title)) {
            Row row = sheet.createRow(rowNum);
            Cell cell = row.createCell(0);
            cell.setCellValue(title);
            cell.setCellStyle(titleStyle);
            rowNum++;
        }
        if (StringUtils.isNotEmpty(title2)) {
            Row row = sheet.createRow(rowNum);
            Cell cell = row.createCell(0);
            cell.setCellValue(title2);
            cell.setCellStyle(titleStyle);
            rowNum++;
        }
        if (null != title2Array) {
            if (title2Array.length > 0) {
                Row row = sheet.createRow(rowNum);
                String beforCellVal = "";
                int beforCellValNumber = 0;
                for (int i = 0; i < title2Array.length; i++) {
                    Cell cell = row.createCell(i);
                    cell.setCellValue(title2Array[i]);
                    cell.setCellStyle(titleStyle);
                    if (beforCellVal.equals(title2Array[i])) {
                        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, beforCellValNumber, i));
                    } else {
                        beforCellValNumber = i;
                        beforCellVal = title2Array[i];
                    }

                }
                rowNum++;
            }
        }

        if (null != titleStyle) {
            Row row = sheet.createRow(rowNum);
            int i = 0;
            while (iterator.hasNext()) {
                String key = iterator.next();
                Cell cell = row.createCell(i);
                cell.setCellValue(map.get(key));
                cell.setCellStyle(titleStyle);
                list.add(key);
                i++;
            }
        } else {
            while (iterator.hasNext()) {
                list.add(iterator.next());
            }
        }

        int cellNum = list.size() - 1;
        int titleRow = 0;
        if (StringUtils.isNotEmpty(title)) {
            Row cellRow = sheet.getRow(titleRow);
            for (int j = 1; j < cellNum; j++) {
                Cell cell = cellRow.createCell(j);
                cell.setCellStyle(titleStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(titleRow, titleRow, 0, cellNum));
            titleRow++;
        }
        if (StringUtils.isNotEmpty(title2)) {
            Row cellRow = sheet.getRow(titleRow);
            for (int j = 1; j < cellNum; j++) {
                Cell cell = cellRow.createCell(j);
                cell.setCellStyle(titleStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(titleRow, titleRow, 0, cellNum));
            titleRow++;
        }
        rowNum++;
        //数据
        setJsonData(0, rowNum, sheet, list, dataAraay, style);
    }


    public static void setJsonData(int beginCell, int rowNum, Sheet sheet, LinkedList list, JSONArray dataAraay, CellStyle style) {
        for (int j = 0; j < dataAraay.size(); j++) {
            JSONObject jsonObject = JSONObject.fromObject(dataAraay.get(j));
            Row dataRow = sheet.createRow(rowNum);
            for (int k = 0; k < list.size(); k++) {
                Cell cell = dataRow.createCell(k + beginCell);
                cell.setCellStyle(style);
                Object data = jsonObject.get(list.get(k));
                Boolean isNum = false;//data是否为数值型
                Boolean isInteger = false;//data是否为整数
                Boolean isPercent = false;//data是否为百分数
                if (data != null || "".equals(data)) {
                    //判断data是否为数值型
                    isNum = data.toString().matches("^(-?\\d+)(\\.\\d+)?$");
                    //判断data是否为整数（小数部分是否为0）
                    isInteger = data.toString().matches("^[-\\+]?[\\d]*$");
                    //判断data是否为百分数（是否包含“%”）
                    isPercent = data.toString().contains("%");

                    //如果单元格内容是数值类型，涉及到金钱（金额、本、利），则设置cell的类型为数值型，设置data的类型为数值类型
                    if (data instanceof String) {
                        cell.setCellValue(data.toString());
                    } else if (data instanceof BigDecimal) {
                        if (data.toString().indexOf(".") > 0) {
                            data = ((BigDecimal) data).setScale(2, BigDecimal.ROUND_HALF_UP);
                        }
                        cell.setCellValue(data.toString());
                    } else if (data instanceof Integer || data instanceof Long) {
                        cell.setCellValue(data.toString());
                    } else if (isNum && !isPercent) {
                        // 设置单元格内容为double类型
                        cell.setCellValue(Double.parseDouble(data.toString()));
                        CellStyle cellStyle = cell.getCellStyle();
                        cellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
                    } else {
                        // 设置单元格内容为字符型
                        cell.setCellValue(data.toString());
                    }
                }

            }
            rowNum++;
        }
        setSheetWidth(sheet);
    }

    public static void setExcelMapData(Sheet sheet, List<Map<String, Object>> contents, LinkedHashMap<String, String> map, String title, String title2, String[] title2Array, CellStyle titleStyle, CellStyle style) {
        int rowNum = 0;
        //标题
        LinkedList list = new LinkedList();
        Iterator<String> iterator = map.keySet().iterator();


        if (StringUtils.isNotEmpty(title)) {
            Row row = sheet.createRow(rowNum);
            Cell cell = row.createCell(0);
            cell.setCellValue(title);
            cell.setCellStyle(titleStyle);
            rowNum++;
        }
        if (StringUtils.isNotEmpty(title2)) {
            Row row = sheet.createRow(rowNum);
            Cell cell = row.createCell(0);
            cell.setCellValue(title2);
            cell.setCellStyle(titleStyle);
            rowNum++;
        }
        if (null != title2Array) {
            if (title2Array.length > 0) {
                Row row = sheet.createRow(rowNum);
                String beforCellVal = "";
                int beforCellValNumber = 0;
                for (int i = 0; i < title2Array.length; i++) {
                    Cell cell = row.createCell(i);
                    cell.setCellValue(title2Array[i]);
                    cell.setCellStyle(titleStyle);
                    if (beforCellVal.equals(title2Array[i])) {
                        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, beforCellValNumber, i));
                    } else {
                        beforCellValNumber = i;
                        beforCellVal = title2Array[i];
                    }

                }
                rowNum++;
            }
        }
        Row row = sheet.createRow(rowNum);
        int i = 0;

        while (iterator.hasNext()) {
            String key = iterator.next();
            Cell cell = row.createCell(i);
            cell.setCellValue(map.get(key));
            cell.setCellStyle(titleStyle);
            list.add(key);
            i++;
        }
        int cellNum = list.size() - 1;
        int titleRow = 0;
        if (StringUtils.isNotEmpty(title)) {
            Row cellRow = sheet.getRow(titleRow);
            for (int j = 1; j < cellNum; j++) {
                Cell cell = cellRow.createCell(j);
                cell.setCellStyle(titleStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(titleRow, titleRow, 0, cellNum));
            titleRow++;
        }
        if (StringUtils.isNotEmpty(title2)) {
            Row cellRow = sheet.getRow(titleRow);
            for (int j = 1; j < cellNum; j++) {
                Cell cell = cellRow.createCell(j);
                cell.setCellStyle(titleStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(titleRow, titleRow, 0, cellNum));
            titleRow++;
        }
        rowNum++;
        //数据
        for (int j = 0; j < contents.size(); j++) {
            //数据内容从第三行开始
            Row dataRow = sheet.createRow(rowNum);
            //在当前行创建第i个单元格
            for (int k = 0; k < list.size(); k++) {
                Cell cell = dataRow.createCell(k);
                String value = String.valueOf(contents.get(j).get(list.get(k)));
                if (StringUtils.isEmpty(value) || "null".equals(value)) {
                    value = "";
                }
                cell.setCellValue(value);
                cell.setCellStyle(style);
            }
            rowNum++;
        }
        setSheetWidth(sheet);
    }

    public static void setMapData(int beginCell, int rowNum, Sheet sheet, LinkedList list, List<Map<String, Object>> contents, CellStyle style) {
        for (int j = 0; j < contents.size(); j++) {
            //数据内容从第三行开始
            Row dataRow = sheet.createRow(rowNum);
            //在当前行创建第i个单元格
            for (int k = 0; k < list.size(); k++) {
                Cell cell = dataRow.createCell(k + beginCell);

                Object data = contents.get(j).get(list.get(k));
                Boolean isNum = false;//data是否为数值型
                Boolean isInteger = false;//data是否为整数
                Boolean isPercent = false;//data是否为百分数
                if (data != null || "".equals(data)) {
                    //判断data是否为数值型
                    isNum = data.toString().matches("^(-?\\d+)(\\.\\d+)?$");
                    //判断data是否为整数（小数部分是否为0）
                    isInteger = data.toString().matches("^[-\\+]?[\\d]*$");
                    //判断data是否为百分数（是否包含“%”）
                    isPercent = data.toString().contains("%");

                    //如果单元格内容是数值类型，涉及到金钱（金额、本、利），则设置cell的类型为数值型，设置data的类型为数值类型
                    if (data instanceof String) {
                        cell.setCellValue(data.toString());
                    } else if (data instanceof BigDecimal) {
                        if (data.toString().indexOf(".") > 0) {
                            data = ((BigDecimal) data).setScale(2, BigDecimal.ROUND_HALF_UP);
                        }
                        cell.setCellValue(data.toString());
                    } else if (data instanceof Integer || data instanceof Long) {
                        cell.setCellValue(data.toString());
                    } else if (isNum && !isPercent) {
                        // 设置单元格内容为double类型
                        cell.setCellValue(Double.parseDouble(data.toString()));
                        CellStyle cellStyle = cell.getCellStyle();
                        cellStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
                    } else {
                        // 设置单元格内容为字符型
                        cell.setCellValue(data.toString());
                    }
                }

                cell.setCellStyle(style);
            }
            rowNum++;
        }
        setSheetWidth(sheet);
    }

    public static void setSheetWidth(Sheet sheet) {
        int maxColumn = 10;
        if (null != sheet.getRow(0)) {
            maxColumn = sheet.getRow(0).getPhysicalNumberOfCells();
        } else {
            for (int i = 1; i < 26; i++) {
                if (null != sheet.getRow(i)) {
                    maxColumn = sheet.getRow(i).getPhysicalNumberOfCells();
                    break;
                }
            }
        }
        for (int i = 0; i < maxColumn; i++) {
            sheet.autoSizeColumn(i);
        }
        for (int columnNum = 0; columnNum <= maxColumn; columnNum++) {
            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
            if (columnWidth < 11) {
                columnWidth = 11;
            }
            for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row currentRow;
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }

                if (currentRow.getCell(columnNum) != null) {
                    Cell currentCell = currentRow.getCell(columnNum);
                    int length = getCellString(currentCell).length();
                    if (columnWidth < length + 1) {
                        columnWidth = length + 8;
                    }
                }
            }
            if (columnWidth > 200) {
                logger.info("获取的行号为:{},长度为:{}", columnNum, columnWidth);
                columnWidth = 30;
            }
            sheet.setColumnWidth(columnNum, columnWidth * 256);
        }
    }

    public static Workbook writeExcel(JSONArray dataAraay, LinkedHashMap<String, String> map, String sheetName, String title, String title2, String[] title2Array, String fileName) {
        Workbook workbook = createWorkBook(fileName);
        Sheet sheet = createSheet(workbook, sheetName);
        CellStyle titleStyle = creatTitleStyle(workbook);
        CellStyle style = creatCellStyle(workbook);
        setExcelData(sheet, dataAraay, map, title, title2, title2Array, titleStyle, style);
        return workbook;
    }

    //合并指定列的重复值
    public static void mergedRegion(Workbook workbook, int sheetIndx, int cellNumber, int firstRow, int lastRow, Integer typeRow) {
        Sheet sheet = workbook.getSheetAt(sheetIndx);
        if (null != sheet) {
            String beforCellVal = "";
            int beforCellValNumber = cellNumber;
            for (int i = firstRow; i <= lastRow; i++) {
                Row row = sheet.getRow(i);
                if (null != row) {
                    String cellValue = getCellString(row.getCell(cellNumber));
                    if (StringUtils.isEmpty(cellValue)) {
                        cellValue = "";
                    }
                    if (beforCellVal.equals(cellValue)) {
                        if (typeRow != null) {//以哪一列为主进行合并
                            String cellTypeTop = "";
                            if (i > 0) {
                                Row beforRow = sheet.getRow(i - 1);
                                cellTypeTop = getCellString(beforRow.getCell(typeRow));
                            }
                            String cellTypeNow = getCellString(row.getCell(typeRow));
                            if (cellTypeTop.equals(cellTypeNow)) {
                                removeMergedRegion(sheet, beforCellValNumber, cellNumber);
                                sheet.addMergedRegion(new CellRangeAddress(beforCellValNumber, i, cellNumber, cellNumber));
                            } else {
                                beforCellValNumber = i;
                                beforCellVal = cellValue;
                            }
                        } else {
                            removeMergedRegion(sheet, beforCellValNumber, cellNumber);
                            sheet.addMergedRegion(new CellRangeAddress(beforCellValNumber, i, cellNumber, cellNumber));
                        }
                    } else {
                        beforCellValNumber = i;
                        beforCellVal = cellValue;
                    }
                }

            }
        }
    }

    public static String getCellString(Cell cell) {
        String str = "";
        int cellType = cell.getCellType();
        if (cellType == 0) {
            Double val = cell.getNumericCellValue();
            str = val.toString();
        } else if (cellType == 1) {
            str = cell.getStringCellValue().toString();
        }
        return str;
    }

    //移除单元格合并
    public static void removeMergedRegion(Sheet sheet, int row, int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();//获取所有的单元格
        int index = 0;//用于保存要移除的那个单元格序号
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress ca = sheet.getMergedRegion(i); //获取第i个单元格
            int firstColumn = ca.getFirstColumn();
            int lastColumn = ca.getLastColumn();
            int firstRow = ca.getFirstRow();
            int lastRow = ca.getLastRow();
            if (row >= firstRow && row <= lastRow) {
                if (column >= firstColumn && column <= lastColumn) {
                    index = i;
                    sheet.removeMergedRegion(index);//移除合并单元格
                }
            }
        }

    }

    public static void writeAndDownloadExcel(HttpServletResponse response, JSONArray dataAraay, LinkedHashMap<String, String> map, String sheetName, String title, String title2, String[] title2Array, String fileName) {
        Workbook workbook = writeExcel(dataAraay, map, sheetName, title, title2, title2Array, fileName);
        downLoadExcel(fileName, response, workbook);
    }

    public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            //-------------重点------------ 解决with root cause报错
            response.flushBuffer();
            //--------------重点---------- 解决with root cause报错
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            logger.error("导出失败：" + e.getMessage(), e);
            throw new RuntimeException("导出失败");
        }
    }

    //边框样式
    //上左边框
    public static CellStyle topLeftCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        Font font_type = workbook.createFont();
        //设置字体
        font_type.setFontName("微软雅黑");
        //style.setWrapText(true); // 自动换行
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中

        return style;
    }

    //上右边框
    public static CellStyle topRightCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        Font font_type = workbook.createFont();
        //设置字体
        font_type.setFontName("微软雅黑");
        //style.setWrapText(true); // 自动换行
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中
        return style;
    }

    //下左边框
    public static CellStyle bottomLeftCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        Font font_type = workbook.createFont();
        //设置字体
        font_type.setFontName("微软雅黑");
        //style.setWrapText(true); // 自动换行
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中

        return style;
    }

    //下右边框
    public static CellStyle bottomRightCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        Font font_type = workbook.createFont();
        //设置字体
        font_type.setFontName("微软雅黑");
        //style.setWrapText(true); // 自动换行
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中

        return style;
    }

    //左边框
    public static CellStyle leftCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        Font font_type = workbook.createFont();
        //设置字体
        font_type.setFontName("微软雅黑");
        //style.setWrapText(true); // 自动换行
        //style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        // style.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中

        return style;
    }

    //右边框
    public static CellStyle rightCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        Font font_type = workbook.createFont();
        //设置字体
        font_type.setFontName("微软雅黑");
        //style.setWrapText(true); // 自动换行
        //style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        //style.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中

        return style;
    }

    //上边框
    public static CellStyle topCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 生成一个字体
        Font font_type = workbook.createFont();
        //设置字体
        font_type.setFontName("微软雅黑");
        //style.setWrapText(true); // 自动换行
        //style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        style.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中
        return style;
    }


}