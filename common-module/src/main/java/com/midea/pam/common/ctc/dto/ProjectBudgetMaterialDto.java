package com.midea.pam.common.ctc.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.midea.pam.common.crm.excelVo.ProjectBudgetMaterialImportExcelVO;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterial;
import com.midea.pam.common.ctc.entity.TicketTasks;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.ALWAYS)
public class ProjectBudgetMaterialDto extends ProjectBudgetMaterial {

    private TicketTasks tasks;

    private MilepostDesignPlanDetail milepostDesignPlanDetail;

    private List<ProjectBudgetMaterialDto> children;

    public List<ProjectBudgetMaterialDto> getChildren() {
        return children;
    }

    // 前端序号
    private String orderNo;

    //下层是否有详细设计
    private boolean hasDesign;
    //判断
    private boolean showCheckbox = false;
    //刪除确认
    private boolean deleteConfirm = false;
    //禁用的
    private boolean disabled;
    //提示语
    private String tips;

    //用于项目物料预算初始化数据导入校验
    @ApiModelProperty("校验结果")
    private String validResult;

    private Long index;

    private String projectCode;

    private String projectName;

    private String ProjectCodeOld;

    private String projectNameOld;

    private String projectBudgetMaterialLevel;

    private String extName;

    // 变更前ID
    private Long originId;

    private Long projectBudgetMaterialId;

    /**
     * 项目预算物料导入Excel（附件内容）
     */
    private List<ProjectBudgetMaterialImportExcelVO> importExcelVos;

    /**
     * 项目预算物料导入Excel（前端物料）
     */
    private ProjectBudgetMaterialDto treeNodes;

    @ApiModelProperty("详细设计id")
    private Long designPlanDetailId;


    public Long getOriginId() {
        return originId;
    }

    public void setOriginId(Long originId) {
        this.originId = originId;
    }

    public Long getProjectBudgetMaterialId() {
        return projectBudgetMaterialId;
    }

    public void setProjectBudgetMaterialId(Long projectBudgetMaterialId) {
        this.projectBudgetMaterialId = projectBudgetMaterialId;
    }

    public String getValidResult() {
        return validResult;
    }

    public void setValidResult(String validResult) {
        this.validResult = validResult;
    }

    public Long getIndex() {
        return index;
    }

    public void setIndex(Long index) {
        this.index = index;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectCodeOld() {
        return ProjectCodeOld;
    }

    public void setProjectCodeOld(String projectCodeOld) {
        ProjectCodeOld = projectCodeOld;
    }

    public String getProjectNameOld() {
        return projectNameOld;
    }

    public void setProjectNameOld(String projectNameOld) {
        this.projectNameOld = projectNameOld;
    }

    public String getProjectBudgetMaterialLevel() {
        return projectBudgetMaterialLevel;
    }

    public void setProjectBudgetMaterialLevel(String projectBudgetMaterialLevel) {
        this.projectBudgetMaterialLevel = projectBudgetMaterialLevel;
    }

    public String getExtName() {
        return extName;
    }

    public void setExtName(String extName) {
        this.extName = extName;
    }

    public boolean isDeleteConfirm() {
        return deleteConfirm;
    }

    public void setDeleteConfirm(boolean deleteConfirm) {
        this.deleteConfirm = deleteConfirm;
    }

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public void setChildren(List<ProjectBudgetMaterialDto> children) {
        this.children = children;
    }

    public boolean isHasDesign() {
        return hasDesign;
    }

    public void setHasDesign(boolean hasDesign) {
        this.hasDesign = hasDesign;
    }

    public boolean isShowCheckbox() {
        return showCheckbox;
    }

    public void setShowCheckbox(boolean showCheckbox) {
        this.showCheckbox = showCheckbox;
    }

    public TicketTasks getTasks() {
        return tasks;
    }

    public void setTasks(TicketTasks tasks) {
        this.tasks = tasks;
    }

    public MilepostDesignPlanDetail getMilepostDesignPlanDetail() {
        return milepostDesignPlanDetail;
    }

    public void setMilepostDesignPlanDetail(MilepostDesignPlanDetail milepostDesignPlanDetail) {
        this.milepostDesignPlanDetail = milepostDesignPlanDetail;
    }

    public List<ProjectBudgetMaterialImportExcelVO> getImportExcelVos() {
        return importExcelVos;
    }

    public void setImportExcelVos(List<ProjectBudgetMaterialImportExcelVO> importExcelVos) {
        this.importExcelVos = importExcelVos;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public ProjectBudgetMaterialDto getTreeNodes() {
        return treeNodes;
    }

    public void setTreeNodes(ProjectBudgetMaterialDto treeNodes) {
        this.treeNodes = treeNodes;
    }

    public Long getDesignPlanDetailId() {
        return designPlanDetailId;
    }

    public void setDesignPlanDetailId(Long designPlanDetailId) {
        this.designPlanDetailId = designPlanDetailId;
    }
}
