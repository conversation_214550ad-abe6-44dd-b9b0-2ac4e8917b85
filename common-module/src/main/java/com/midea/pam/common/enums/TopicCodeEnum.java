package com.midea.pam.common.enums;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

@Getter
public enum TopicCodeEnum {
    /**GCRM**/
    CRM_CUSTOMER_SAVE("PAM-CRM-001","内销客户保存","GCRM_DOMESTIC_CUS_INFO_NEW","GCRM",null),
    /**
     * GERP
     **/
    PURCHASE_ORDER("PAM-ERP-034", "采购订单写入(收货地址)", "PO", "GERP", null),
    PURCHASE_ORDER_CHANGE("PAM-ERP-034-1", "采购订单变更写入(收货地址)", "PO", "GERP", null),
    PURCHASE_PRICE_DIFFERENCE_RECORD("PAM-ERP-045", "采购价差明细写入", "GERPPOCOMMONREQ", "GERP", null),
    INVOICE_PRICE_DIFFERENCE_RECORD("PAM-ERP-046", "物料成本更新差异写入", "GERPPOCOMMONREQ", "GERP", null),
    MATERIAL_UPDATE_DIFFERENCE_RECORD("PAM-ERP-047", "物料成本更新差异写入", "GERPPOCOMMONREQ", "GERP", null),
    SUBJECT_BALANCE_RECORD("PAM-ERP-048", "科目余额写入", "GERPPOCOMMONREQ", "GERP", null),
    PAYMENT_INVOICE("PAM-ERP-023", "应付发票写入", "GERPAPINVOICE", "GERP", null),
    PAYMENT_INVOICE_THIRD("PAM-ERP-023-2", "第三方票据写入", "GERPAPINVOICE", "GERP", null),
    RECEIPT_CLAIM("PAM-ERP-019", "收款写入", "GERPCUXARRECEIPT", "GERP", null),
    MATERIAL_CREATE("PAM-ERP-003", "物料主数据创建", "GERPINVITEMIMPORT", "GERP", null),
    MATERIAL_UPDATE("PAM-ERP-003-1", "物料主数据更新", "GERPINVITEMIMPORT", "GERP", null),
    MATERIAL_VALUATION("PAM-ERP-003-2", "物料估价", "GERPINVITEMIMPORT", "GERP", null),
    RECEIPT_CLAIM_REVERSAL("PAM-ERP-020", "收款冲销", "GERPCUXARRECEIPTREVERSE", "GERP", null),
    ACCOUNTING("PAM-ERP-029", "记账写入", "GERPGLJOURNALSIMPORT", "GERP", null),
    INV_TRANSATION_TRANSFER("PAM-ERP-032", "库存转移写入", "GERPINVTRANSATION", "GERP", null),
    INV_TRANSATION("PAM-ERP-033", "库存领料/退料写入", "GERPINVTRANSATION", "GERP", null),
    PURCHASE_ORDER_STANDARD("PAM-ERP-034", "采购订单写入", "GERPPOSTANDARDPO", "GERP", null),
    PURCHASE_ORDER_CHANGE_STANDARD("PAM-ERP-034-1", "采购订单变更写入", "GERPPOSTANDARDPO", "GERP", null),
    CANCEL_INVOICE("PAM-ERP-024", "应收发票取消", "GERPAPCANCE", "GERP", null),
    CANCEL_INVALID_PAYMENT_INVOICE("PAM-ERP-071", "应付发票取消[作废发票场景]", "GERPAPCANCE", "GERP", null),
    PAYMENT_PLAN("PAM-ERP-028", "付款计划写入", "GERPCUXAPAYMENTPLAN", "GERP", null),
    PREPAY("PAM-ERP-025", "预付款核销", "GERPAPPREPAY", "GERP", null),
    CANCEL_PREPAY("PAM-ERP-025-1", "预付款撤销核销", "GERPAPPREPAY", "GERP", null),
    APPAYMENTS_PAYMENT_APPLY("PAM-ERP-073", "应付付款（预付款付款,迪链票据接口卡）", "GERPAPPAYMENTS", "GERP", null),
    POSITIVE_AND_NEGATIVE_RECORD("PAM-ERP-073-1", "正负发票核销", "GERPAPPAYMENTS", "GERP", null),
    CUXICP_GET_SEQ_PKG_PORT_TYPE("PAM-ERP-055", "获取关联交易号", "ICPGETSEQ", "GERP",null),
    ICP_NONRELATE("PAM-ERP-072", "客户间转款写入", "NONRELATEREQ", "GERP",null),
    CUX_AR_UNAPPLY("PAM-ERP-022","核销关系冲销","GERPCUXARUNAPPLY","GERP",null),
    /**
     * FAP
     **/
    CUSTOMER_TRANSFER("C-PAM-FAP-AC", "客户间转款写入", "FAP_AR_TRANSFER", "FAP", null),  //对应GERP的PAM-ERP-072

    FAP_ORDER_CREATE_INTERFACE("PAM-FAP-ITC-A4", "FAP关联交易", "FAP_ORDER_CREATE_INTERFACE", "FAP", null),

    /**
     * GSC
     **/
    PURCHASE_CONTRACT("PAM-GSC-001", "采购合同信息推送", "PAM_PURCHASE_CONTRACT", "GSC", "com.midea.pam.ctc.service.impl.SdpPurchaseContractImpl#pushPurchaseContractInfoForReplay"),
    PAYMENT_APPLY("GSC-PAM-002", "付款申请写入", "PAM_PAYMENT_APPLY", "PAM", null),
    PAYMENT_INVOICE_REQUEST("PAM-GSC-003", "发票申请写入", "PAM_PAYMENT_ISP", "GSC", null),
    PAYMENT_INVOICE_DETAIL_REQUEST("PAM-GSC-004", "税票申请", "PAM_PAYMENT_TAX_INVOICE", "GSC", null),

    GSC_PAM_CALLBACK("PAM-GSC-005", "回调GSC单据审批状态", "GSC_PAM_CALLBACK", "GSC", null),
    AR_INVOICE("PAM-ERP-018", "AR应收发票写入", "GERPARINVOICE", "GERP", null),
    AR_TRX_APPLY("PAM-ERP-054", "应收正负发票核销", "GERPARTRXAPPLY", "GERP", null),
    AR_APPLY("PAM-ERP-021", "收款核销", "GERPARAPPLY", "GERP", null),
    ;


    @ApiModelProperty(value = "接口卡")
    private String interfaceCode;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "SDP主题编码")
    private String TopicCode;

    @ApiModelProperty(value = "交易发起的目标系统")
    private String targetSystemCode;

    @ApiModelProperty(value = "重放处理方法")
    private final String replayHandleMethodPath;


    TopicCodeEnum(String interfaceCode, String name, String topicCode, String targetSystemCode, String replayHandleMethodPath) {
        this.interfaceCode = interfaceCode;
        this.name = name;
        TopicCode = topicCode;
        this.targetSystemCode = targetSystemCode;
        this.replayHandleMethodPath = replayHandleMethodPath;
    }

    public static TopicCodeEnum getEnumByInterfaceCode(String interfaceCode) {
        for (TopicCodeEnum emun : values()) {
            if (emun.interfaceCode.equals(interfaceCode)) {
                return emun;
            }
        }
        return null;
    }

    public static TopicCodeEnum getEnumByTopicCode(String topicCode) {
        for (TopicCodeEnum emun : values()) {
            if (emun.TopicCode.equals(topicCode)) {
                return emun;
            }
        }
        return null;
    }
}
