package com.midea.pam.common.basedata.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DiffCompanyLaborCostSetExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DiffCompanyLaborCostSetExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdIsNull() {
            addCriterion("project_company_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdIsNotNull() {
            addCriterion("project_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdEqualTo(Long value) {
            addCriterion("project_company_id =", value, "projectCompanyId");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdNotEqualTo(Long value) {
            addCriterion("project_company_id <>", value, "projectCompanyId");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdGreaterThan(Long value) {
            addCriterion("project_company_id >", value, "projectCompanyId");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_company_id >=", value, "projectCompanyId");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdLessThan(Long value) {
            addCriterion("project_company_id <", value, "projectCompanyId");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("project_company_id <=", value, "projectCompanyId");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdIn(List<Long> values) {
            addCriterion("project_company_id in", values, "projectCompanyId");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdNotIn(List<Long> values) {
            addCriterion("project_company_id not in", values, "projectCompanyId");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdBetween(Long value1, Long value2) {
            addCriterion("project_company_id between", value1, value2, "projectCompanyId");
            return (Criteria) this;
        }

        public Criteria andProjectCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("project_company_id not between", value1, value2, "projectCompanyId");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdIsNull() {
            addCriterion("user_company_id is null");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdIsNotNull() {
            addCriterion("user_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdEqualTo(Long value) {
            addCriterion("user_company_id =", value, "userCompanyId");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdNotEqualTo(Long value) {
            addCriterion("user_company_id <>", value, "userCompanyId");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdGreaterThan(Long value) {
            addCriterion("user_company_id >", value, "userCompanyId");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_company_id >=", value, "userCompanyId");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdLessThan(Long value) {
            addCriterion("user_company_id <", value, "userCompanyId");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("user_company_id <=", value, "userCompanyId");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdIn(List<Long> values) {
            addCriterion("user_company_id in", values, "userCompanyId");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdNotIn(List<Long> values) {
            addCriterion("user_company_id not in", values, "userCompanyId");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdBetween(Long value1, Long value2) {
            addCriterion("user_company_id between", value1, value2, "userCompanyId");
            return (Criteria) this;
        }

        public Criteria andUserCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("user_company_id not between", value1, value2, "userCompanyId");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andHardWorkingIsNull() {
            addCriterion("hard_working is null");
            return (Criteria) this;
        }

        public Criteria andHardWorkingIsNotNull() {
            addCriterion("hard_working is not null");
            return (Criteria) this;
        }

        public Criteria andHardWorkingEqualTo(String value) {
            addCriterion("hard_working =", value, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingNotEqualTo(String value) {
            addCriterion("hard_working <>", value, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingGreaterThan(String value) {
            addCriterion("hard_working >", value, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingGreaterThanOrEqualTo(String value) {
            addCriterion("hard_working >=", value, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingLessThan(String value) {
            addCriterion("hard_working <", value, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingLessThanOrEqualTo(String value) {
            addCriterion("hard_working <=", value, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingLike(String value) {
            addCriterion("hard_working like", value, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingNotLike(String value) {
            addCriterion("hard_working not like", value, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingIn(List<String> values) {
            addCriterion("hard_working in", values, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingNotIn(List<String> values) {
            addCriterion("hard_working not in", values, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingBetween(String value1, String value2) {
            addCriterion("hard_working between", value1, value2, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andHardWorkingNotBetween(String value1, String value2) {
            addCriterion("hard_working not between", value1, value2, "hardWorking");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdIsNull() {
            addCriterion("full_pay_ou_id is null");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdIsNotNull() {
            addCriterion("full_pay_ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdEqualTo(Long value) {
            addCriterion("full_pay_ou_id =", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdNotEqualTo(Long value) {
            addCriterion("full_pay_ou_id <>", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdGreaterThan(Long value) {
            addCriterion("full_pay_ou_id >", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("full_pay_ou_id >=", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdLessThan(Long value) {
            addCriterion("full_pay_ou_id <", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdLessThanOrEqualTo(Long value) {
            addCriterion("full_pay_ou_id <=", value, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdIn(List<Long> values) {
            addCriterion("full_pay_ou_id in", values, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdNotIn(List<Long> values) {
            addCriterion("full_pay_ou_id not in", values, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdBetween(Long value1, Long value2) {
            addCriterion("full_pay_ou_id between", value1, value2, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuIdNotBetween(Long value1, Long value2) {
            addCriterion("full_pay_ou_id not between", value1, value2, "fullPayOuId");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameIsNull() {
            addCriterion("full_pay_ou_name is null");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameIsNotNull() {
            addCriterion("full_pay_ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameEqualTo(String value) {
            addCriterion("full_pay_ou_name =", value, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameNotEqualTo(String value) {
            addCriterion("full_pay_ou_name <>", value, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameGreaterThan(String value) {
            addCriterion("full_pay_ou_name >", value, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("full_pay_ou_name >=", value, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameLessThan(String value) {
            addCriterion("full_pay_ou_name <", value, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameLessThanOrEqualTo(String value) {
            addCriterion("full_pay_ou_name <=", value, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameLike(String value) {
            addCriterion("full_pay_ou_name like", value, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameNotLike(String value) {
            addCriterion("full_pay_ou_name not like", value, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameIn(List<String> values) {
            addCriterion("full_pay_ou_name in", values, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameNotIn(List<String> values) {
            addCriterion("full_pay_ou_name not in", values, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameBetween(String value1, String value2) {
            addCriterion("full_pay_ou_name between", value1, value2, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andFullPayOuNameNotBetween(String value1, String value2) {
            addCriterion("full_pay_ou_name not between", value1, value2, "fullPayOuName");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}