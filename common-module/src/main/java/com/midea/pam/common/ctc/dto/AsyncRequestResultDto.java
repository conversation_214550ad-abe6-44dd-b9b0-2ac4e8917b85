package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AsyncRequestResultDto extends AsyncRequestResult {

    @ApiModelProperty("修改前的处理人MIP")
    private String oldHandleUserMip;

    @ApiModelProperty("详细设计变更记录表id")
    private Long milepostDesignPlanDetailChangeRecordId;

    @ApiModelProperty("是否有已确认的")
    private Boolean hasConfirmed;
}
