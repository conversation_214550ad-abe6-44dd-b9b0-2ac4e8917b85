package com.midea.pam.common.crm.dto;

import com.midea.pam.common.basedata.dto.TeamUserDto;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.crm.entity.Business;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 创建时间 2017-11-14 8:32
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "BusinessDto", description = "商机信息")
public class BusinessDto extends Business implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "商机名称")
    private String name;

    @ApiModelProperty(value = "商机阶段")
    private Long state;

    @ApiModelProperty(value = "商机阶段名称")
    private String stateName;

    @ApiModelProperty(value = "1:3个月内成单 2:6个月内成单 3:9个月内成单 4:暂未确定")
    private Integer level;

    @ApiModelProperty(value = "赢单率")
    private BigDecimal winRate;

    @ApiModelProperty(value = "客户预算")
    private BigDecimal budget;

    @ApiModelProperty(value = "需求意向")
    private String intention;

    @ApiModelProperty(value = "商机来源")
    private Long source;

    @ApiModelProperty(value = "商机来源名称")
    private String sourceName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态 0：作废 1：生效 2：赢单")
    private Integer status;

    private Integer followStatus;

    private String createUserId;

    private String updateUserId;

    private Date createAt;

    private Date updateAt;

    @ApiModelProperty(value = "所有者uid")
    private String ownerId;

    @ApiModelProperty(value = "所有者id")
    private Long ownerUserId;

    @ApiModelProperty(value = "所有者姓名")
    private String ownerName;

    @ApiModelProperty("方案设计人id")
    private Long planDesignerId;

    @ApiModelProperty("方案设计人名字")
    private String planDesignerName;

    @ApiModelProperty(value = "团队ID")
    private Long teamId;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    @ApiModelProperty(value = "售前负责人")
    private String preSalerName;

    @ApiModelProperty(value = "线索ID")
    private Long leadId;

    @ApiModelProperty(value = "赢单时间")
    private Date targetWinDate;

    @ApiModelProperty(value = "审批通过时间")
    private Date approvalDate;

    private Date approvalDateBegin;

    private Date approvalDateEnd;

    private List<BusinessExtraDto> businessExtras;

    private Integer modelId;

    private Boolean saasBusinessFlag;

    private CustomerDto customer;

    private LeadDto lead;

    private List<BusinessProductDto> businessProducts;

    private List<TeamUserDto> teamUsers;

    @ApiModelProperty(value = "附件")
    private List<BusinessAttachmentRelationDto> attachmentRelations;

    private UserInfo owner;

    @ApiModelProperty(value = "销售部门ID")
    private Long salesDepartmentId;

    @ApiModelProperty(value = "使用单位ID")
    private Long parentUnitId;

    private RoleDeptDto salesDepartment;

    @ApiModelProperty(value = "销售部门entity")
    private Unit unit;

    @ApiModelProperty(value = "销售部门名称")
    private String salesDepartmentName;

    private List<CompetitionDto> competitions;

    private List<ContactDto> contacts;

    private List<ResourceDto> resourceVos;

    @ApiModelProperty(value = "跟进时间")
    private Date followTime;

    @ApiModelProperty(value = "是否已删除 0：未删除 1：已删除")
    private Boolean hasDelete;

    @ApiModelProperty(value = "是否置顶 1：置顶  0：非置顶")
    private Boolean top;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "累计跟进")
    private Long totalFollowTime;

    @ApiModelProperty(value = "赢单金额")
    private BigDecimal winAmount;

    @ApiModelProperty(value = "商机关闭原因分类")
    private String closeClass;

    @ApiModelProperty(value = "商机关闭原因")
    private String content;

    @ApiModelProperty(value = "启用或禁用原因")
    private String enableOrDisableReason;

    @ApiModelProperty("商机编号")
    private String businessCode;

    @ApiModelProperty("商机方案")
    private List<PlanDto> planDtos;

    @ApiModelProperty("商机报价单")
    private List<QuotationDto> quotationDtos;

    @ApiModelProperty("是否允许置顶,true是，false 否")
    private Boolean isAllowedToTop;


    @ApiModelProperty("是否为简化商机")
    private Boolean orNotSimple;

    @ApiModelProperty(value = "质保期")
    private String guaranteePeriod;

    /**
     * 商机列表，是否可点击客户链接 true：是，false:否
     */
    private Boolean customerIsView;

    /**
     * 客户明细商机列表，是否可点击商机链接 true：是，false:否
     */
    private Boolean businessIsView;

    //列表多选查询字段
    private String statusStr;
    private String followStatusStr;
    private String stateStr;
    private String levelStr;
    private String sourceStr;
    private String unitIdStr;
    private String roleDeptStr;
    private String businessTypeStr;
    private String closeClassStr;
    //后台遍历字段
    private List<Long> statuses;
    private List<String> followStatuses;
    private List<Long> states;
    private List<Long> levels;
    private List<Long> sources;
    private List<Long> unitIds;
    private List<Long> roleDepts;
    private List<String> businessTypes;
    private List<Long> saleDepts;
    private List<String> closeClassList;
    //时间查询字段
    private String createAtStart;
    private String createAtEnd;

    private String followTimeStart;
    private String followTimeEnd;

    private String crmCode;

    private String customerStatus;

    private String roleDeptName;

    private String parentUid;

    private String customerStatusName;

    @ApiModelProperty(value = "客户群组")
    private String customerGroup;

    private String customerGroupStr;
    private List<String> customerGroups;

    private String allowedName;

    private String userId;

    private List<Long> secondUnits;

    private String uId;

    private String followName;

    //是否查询我的 true 是,false 否
    private Boolean me;

    private Boolean list;

    private String businessDeptId;

    private String saleDeptId;

    private String authorStrUnitIds;

    private List<Long> authors;

    private String productIntention;

    private String productIntentionName;

    @ApiModelProperty("流程分支入参")
    private int branch;

    @ApiModelProperty("冲突列表")
    private List<BusinessConflictDto> conflictDtos;

    @ApiModelProperty("是否商机项目,true是，false 否")
    private Boolean isBusinessProject;

    @ApiModelProperty("项目id")
    private Long projectId;

    private Integer riskStrategyStatus;

    private String riskStrategyStatusStr;

    private Integer businessQuotationFileStatus;

    private String businessQuotationFileStatusStr;

    private List<Integer> riskStrategyStatusList;

    private Integer replayStatus;

    private String replayStatusStr;

    private List<Integer> replayStatusList;

    private List<BusinessRiskStrategyFileInfoDto> businessRiskStrategyFileInfoDtos;

    private List<BusinessQuotationFileInfoDto> businessQuotationFileInfoDtos;

    private String createUserStr;

    //add by xuweijian begiin
    @ApiModelProperty("评估表，多个用,隔开")
    private String assessmentForm;
    //add by xuweijian end

    // add by sunheng2 应用行业中文名
    private String applicationIndustryName;
    private Long parenetUnitId;
    private String operatingUnitName;
    // 对应赢单方案中的销售经理
    private Long salesManagerId;
    private String salesManagerName;

    //
    private List<Long> ouIdList;

    private String ouCode;

    private String subApplyNo;

    @ApiModelProperty("竞争对手，多个用,隔开")
    private String competitionCompany;

    @ApiModelProperty("商机未税金额-最低")
    private BigDecimal amountMin;
    @ApiModelProperty("商机未税金额-最高")
    private BigDecimal amountMax;

    @ApiModelProperty("方案启动节点-开始")
    private String planBeginNodeStart;
    @ApiModelProperty("方案启动节点-开始")
    private String planBeginNodeEnd;

    @ApiModelProperty("预计投标时间-开始")
    private String estimateBidAtStart;
    @ApiModelProperty("预计投标时间-开始")
    private String estimateBidAtEnd;

    @ApiModelProperty("预计发货时间-开始")
    private String estimateDeliveryAtStart;
    @ApiModelProperty("预计发货时间-开始")
    private String estimateDeliveryAtEnd;

    @ApiModelProperty("预计量产时间-开始")
    private String expectedProductionTimeStart;
    @ApiModelProperty("预计量产时间-开始")
    private String expectedProductionTimeEnd;

    @ApiModelProperty(value = "中标方")
    private String successfulBidder;

    @ApiModelProperty(value = "中标金额")
    private BigDecimal bidderAmount;

    /**
     * 组织参数销售部门负责人ID对应name
     */
    private String salesOrganizationName;

    /**
     * 赢丢单查询开始时间
     */
    private String winLossStartTime;

    /**
     * 赢丢单查询结束时间
     */
    private String winLossEndTime;

    /**
     * 销售部门总负责人组织参数配置对应的name
     */
    private String salesOrganizationTopName;
}
