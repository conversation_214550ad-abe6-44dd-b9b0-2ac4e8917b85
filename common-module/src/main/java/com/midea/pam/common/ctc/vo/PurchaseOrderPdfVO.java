package com.midea.pam.common.ctc.vo;

import com.midea.pam.common.ctc.dto.PurchaseOrderStandardTermsDto;
import com.midea.pam.common.ctc.entity.PurchaseContractStamp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "PurchaseOrderPdfVO", description = "采购订单PDF数据封装")
public class PurchaseOrderPdfVO {

    @ApiModelProperty(value = "使用单位名称")
    private String unitName;

    @ApiModelProperty(value = "订单版本号")
    private String versionCode;

    @ApiModelProperty(value = "采购订单编号")
    private String contractCode;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "订单日期：下载PDF的日期")
    private Date contractDate;

    @ApiModelProperty(value = "卖方：供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "买方：订单跟进人")
    private String buyerName;

    @ApiModelProperty(value = "买方邮箱：订单跟进人的MIP邮箱只取kuka邮箱")
    private String buyerNameEmail;

    @ApiModelProperty(value = "付款条件/方法")
    private String paymentConditionAndWay;

    @ApiModelProperty(value = "交货方式")
    private String deliveryType;

    @ApiModelProperty(value = "交货条款")
    private String deliveryClause;

    @ApiModelProperty(value = "不含税总价：订单总额（不含税）")
    private BigDecimal excludingTaxAmount;

    @ApiModelProperty(value = "税：订单总额（含税）-订单总额（不含税）")
    private BigDecimal tax;

    @ApiModelProperty(value = "总价：订单总额（含税）")
    private BigDecimal amount;

    @ApiModelProperty(value = "买方盖章：盖章attachId")
    private Long attachId;

    //按审批顺序取审批人的签名图片变更时，如果金额增加了，取变更流程的审批人的签名图片金额没有增加，取订单新建流程的审批人的签名图
    @ApiModelProperty(value = "核准：核准人签名信息列表")
    private List<PurchaseContractStamp> approveInfoList;

    @ApiModelProperty(value = "预算信息列表")
    private List<PurchaseOrderDetailPdfVO> purchaseOrderDetailPdfVOList;

    @ApiModelProperty(value = "公司信息")
    private String companyInfo;

    @ApiModelProperty(value = "收货地址")
    private String deliveryAddress;

    @ApiModelProperty(value = "合同版本创建时间")
    private Date versionCreateTime;

    @ApiModelProperty(value = "标准条款标识")
    private String contractTermsFlg;

    @ApiModelProperty(value = "合同条款")
    private String contractTerms;

    @ApiModelProperty(value = "标准条款信息")
    private List<PurchaseOrderStandardTermsDto> standardTermsDtoList;

}
