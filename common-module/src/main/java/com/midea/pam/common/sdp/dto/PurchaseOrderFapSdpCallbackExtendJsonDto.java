package com.midea.pam.common.sdp.dto;

import lombok.Data;

@Data
public class PurchaseOrderFapSdpCallbackExtendJsonDto {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 数据源
     */
    private String dataSource;

    /**
     * 源系统
     */
    private String sourceSystem;

    /**
     * 源ID1
     */
    private String sourceId1;

    /**
     * 源ID2
     */
    private String sourceId2;

    /**
     * 源ID3
     */
    private String sourceId3;

    /**
     * 回调状态码
     */
    private String callBackCode;

    /**
     * 回调消息
     */
    private String callBackMsg;

    private String targetId1;

    private String targetId2;

    private String targetId3;

    private String targetId4;

    private String targetId5;

}
