package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "非销售业务场景配置详细")
public class BusiSceneNonSaleDetail extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "非销售业务场景配置列表ID（busi_scene_non_sale.id）")
    private Long busiSceneNonSaleId;

    @ApiModelProperty(value = "顺序")
    private Long seqCode;

    @ApiModelProperty(value = "模块")
    private String module;

    @ApiModelProperty(value = "单据类型")
    private String type;

    @ApiModelProperty(value = "接口编号")
    private String interfaceCode;

    @ApiModelProperty(value = "应收款活动ID（receivables_trx.id）")
    private Long receivablesTrxId;

    @ApiModelProperty(value = "应收款活动名称（收款小类）")
    private String receivablesTrxName;

    @ApiModelProperty(value = "应收事务处理类型ID（cust_trx_type.id）")
    private Long custTrxTypeId;

    @ApiModelProperty(value = "事务处理类型名称（应收发票类型）")
    private String custTrxTypeIdName;

    @ApiModelProperty(value = "科目组合（借方）")
    private String accountGroupDebit;

    @ApiModelProperty(value = "科目组合（贷方）")
    private String accountGroupCredit;

    @ApiModelProperty(value = "银行账户ID（bank_account.id）")
    private Long bankAccountId;

    @ApiModelProperty(value = "银行账号")
    private String bankAccountNum;

    @ApiModelProperty(value = "标识")
    private String flag;

    @ApiModelProperty(value = "启用日期")
    private Date startDate;

    @ApiModelProperty(value = "失效日期")
    private Date endDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否同步单据(1:是 0否)")
    private Integer isSyncReceipt;

    @ApiModelProperty(value = "税率集合")
    private String taxRateSet;

    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getBusiSceneNonSaleId() {
        return busiSceneNonSaleId;
    }

    public void setBusiSceneNonSaleId(Long busiSceneNonSaleId) {
        this.busiSceneNonSaleId = busiSceneNonSaleId;
    }

    public Long getSeqCode() {
        return seqCode;
    }

    public void setSeqCode(Long seqCode) {
        this.seqCode = seqCode;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module == null ? null : module.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getInterfaceCode() {
        return interfaceCode;
    }

    public void setInterfaceCode(String interfaceCode) {
        this.interfaceCode = interfaceCode == null ? null : interfaceCode.trim();
    }

    public Long getReceivablesTrxId() {
        return receivablesTrxId;
    }

    public void setReceivablesTrxId(Long receivablesTrxId) {
        this.receivablesTrxId = receivablesTrxId;
    }

    public String getReceivablesTrxName() {
        return receivablesTrxName;
    }

    public void setReceivablesTrxName(String receivablesTrxName) {
        this.receivablesTrxName = receivablesTrxName == null ? null : receivablesTrxName.trim();
    }

    public Long getCustTrxTypeId() {
        return custTrxTypeId;
    }

    public void setCustTrxTypeId(Long custTrxTypeId) {
        this.custTrxTypeId = custTrxTypeId;
    }

    public String getCustTrxTypeIdName() {
        return custTrxTypeIdName;
    }

    public void setCustTrxTypeIdName(String custTrxTypeIdName) {
        this.custTrxTypeIdName = custTrxTypeIdName == null ? null : custTrxTypeIdName.trim();
    }

    public String getAccountGroupDebit() {
        return accountGroupDebit;
    }

    public void setAccountGroupDebit(String accountGroupDebit) {
        this.accountGroupDebit = accountGroupDebit == null ? null : accountGroupDebit.trim();
    }

    public String getAccountGroupCredit() {
        return accountGroupCredit;
    }

    public void setAccountGroupCredit(String accountGroupCredit) {
        this.accountGroupCredit = accountGroupCredit == null ? null : accountGroupCredit.trim();
    }

    public Long getBankAccountId() {
        return bankAccountId;
    }

    public void setBankAccountId(Long bankAccountId) {
        this.bankAccountId = bankAccountId;
    }

    public String getBankAccountNum() {
        return bankAccountNum;
    }

    public void setBankAccountNum(String bankAccountNum) {
        this.bankAccountNum = bankAccountNum == null ? null : bankAccountNum.trim();
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getIsSyncReceipt() {
        return isSyncReceipt;
    }

    public void setIsSyncReceipt(Integer isSyncReceipt) {
        this.isSyncReceipt = isSyncReceipt;
    }

    public String getTaxRateSet() {
        return taxRateSet;
    }

    public void setTaxRateSet(String taxRateSet) {
        this.taxRateSet = taxRateSet == null ? null : taxRateSet.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", busiSceneNonSaleId=").append(busiSceneNonSaleId);
        sb.append(", seqCode=").append(seqCode);
        sb.append(", module=").append(module);
        sb.append(", type=").append(type);
        sb.append(", interfaceCode=").append(interfaceCode);
        sb.append(", receivablesTrxId=").append(receivablesTrxId);
        sb.append(", receivablesTrxName=").append(receivablesTrxName);
        sb.append(", custTrxTypeId=").append(custTrxTypeId);
        sb.append(", custTrxTypeIdName=").append(custTrxTypeIdName);
        sb.append(", accountGroupDebit=").append(accountGroupDebit);
        sb.append(", accountGroupCredit=").append(accountGroupCredit);
        sb.append(", bankAccountId=").append(bankAccountId);
        sb.append(", bankAccountNum=").append(bankAccountNum);
        sb.append(", flag=").append(flag);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", remark=").append(remark);
        sb.append(", isSyncReceipt=").append(isSyncReceipt);
        sb.append(", taxRateSet=").append(taxRateSet);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}