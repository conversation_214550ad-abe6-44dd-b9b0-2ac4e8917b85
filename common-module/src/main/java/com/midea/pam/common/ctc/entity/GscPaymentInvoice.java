package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "")
public class GscPaymentInvoice extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "ISP_发票申请编号")
    private String invoiceNumber;

    @ApiModelProperty(value = "业务实体ID")
    private Long ouId;

    @ApiModelProperty(value = "采购合同ID")
    private Long purchaseContractId;

    @ApiModelProperty(value = "采购合同编号")
    private String purchaseCode;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "供应商ID")
    private Long erpVendorId;

    @ApiModelProperty(value = "供应商编号")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String erpVendorName;

    @ApiModelProperty(value = "供应商地点ID")
    private String erpVendorSiteId;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "应付发票号")
    private String apInvoiceCode;

    @ApiModelProperty(value = "应付发票ID")
    private Long apInvoiceId;

    @ApiModelProperty(value = "发票属性")
    private String invoiceAttribute;

    @ApiModelProperty(value = "发票总金额(含税)")
    private BigDecimal totalInvoiceAmount;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal amountWithoutTax;

    @ApiModelProperty(value = "审批通过日期")
    private Date approvalDate;

    @ApiModelProperty(value = "取消原因")
    private String cancelReasons;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "票据状态(1草稿/2审批中/3/驳回/4生效/5作废)")
    private Integer status;

    @ApiModelProperty(value = "关联税票状态(1:未开票,2:开票审批中,3:开票驳回,4:开票审批通过)")
    private Integer taxInvoiceStatus;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber == null ? null : invoiceNumber.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public Long getPurchaseContractId() {
        return purchaseContractId;
    }

    public void setPurchaseContractId(Long purchaseContractId) {
        this.purchaseContractId = purchaseContractId;
    }

    public String getPurchaseCode() {
        return purchaseCode;
    }

    public void setPurchaseCode(String purchaseCode) {
        this.purchaseCode = purchaseCode == null ? null : purchaseCode.trim();
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public Long getErpVendorId() {
        return erpVendorId;
    }

    public void setErpVendorId(Long erpVendorId) {
        this.erpVendorId = erpVendorId;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public String getErpVendorName() {
        return erpVendorName;
    }

    public void setErpVendorName(String erpVendorName) {
        this.erpVendorName = erpVendorName == null ? null : erpVendorName.trim();
    }

    public String getErpVendorSiteId() {
        return erpVendorSiteId;
    }

    public void setErpVendorSiteId(String erpVendorSiteId) {
        this.erpVendorSiteId = erpVendorSiteId == null ? null : erpVendorSiteId.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getApInvoiceCode() {
        return apInvoiceCode;
    }

    public void setApInvoiceCode(String apInvoiceCode) {
        this.apInvoiceCode = apInvoiceCode == null ? null : apInvoiceCode.trim();
    }

    public Long getApInvoiceId() {
        return apInvoiceId;
    }

    public void setApInvoiceId(Long apInvoiceId) {
        this.apInvoiceId = apInvoiceId;
    }

    public String getInvoiceAttribute() {
        return invoiceAttribute;
    }

    public void setInvoiceAttribute(String invoiceAttribute) {
        this.invoiceAttribute = invoiceAttribute == null ? null : invoiceAttribute.trim();
    }

    public BigDecimal getTotalInvoiceAmount() {
        return totalInvoiceAmount;
    }

    public void setTotalInvoiceAmount(BigDecimal totalInvoiceAmount) {
        this.totalInvoiceAmount = totalInvoiceAmount;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getAmountWithoutTax() {
        return amountWithoutTax;
    }

    public void setAmountWithoutTax(BigDecimal amountWithoutTax) {
        this.amountWithoutTax = amountWithoutTax;
    }

    public Date getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(Date approvalDate) {
        this.approvalDate = approvalDate;
    }

    public String getCancelReasons() {
        return cancelReasons;
    }

    public void setCancelReasons(String cancelReasons) {
        this.cancelReasons = cancelReasons == null ? null : cancelReasons.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getTaxInvoiceStatus() {
        return taxInvoiceStatus;
    }

    public void setTaxInvoiceStatus(Integer taxInvoiceStatus) {
        this.taxInvoiceStatus = taxInvoiceStatus;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", invoiceNumber=").append(invoiceNumber);
        sb.append(", ouId=").append(ouId);
        sb.append(", purchaseContractId=").append(purchaseContractId);
        sb.append(", purchaseCode=").append(purchaseCode);
        sb.append(", projectId=").append(projectId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", erpVendorId=").append(erpVendorId);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", erpVendorName=").append(erpVendorName);
        sb.append(", erpVendorSiteId=").append(erpVendorSiteId);
        sb.append(", currency=").append(currency);
        sb.append(", apInvoiceCode=").append(apInvoiceCode);
        sb.append(", apInvoiceId=").append(apInvoiceId);
        sb.append(", invoiceAttribute=").append(invoiceAttribute);
        sb.append(", totalInvoiceAmount=").append(totalInvoiceAmount);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", taxAmount=").append(taxAmount);
        sb.append(", amountWithoutTax=").append(amountWithoutTax);
        sb.append(", approvalDate=").append(approvalDate);
        sb.append(", cancelReasons=").append(cancelReasons);
        sb.append(", remark=").append(remark);
        sb.append(", status=").append(status);
        sb.append(", taxInvoiceStatus=").append(taxInvoiceStatus);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}