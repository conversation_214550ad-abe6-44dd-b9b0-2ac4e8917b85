package com.midea.pam.common.sdp.enums;

public enum CustomerCrmStatusCode {

    FROZEN("frozen", "冻结"),
    INVALID("invalid", "废弃"),
    VALID("valid", "有效"),
    ;

    private String code;
    private String name;

    private CustomerCrmStatusCode(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
