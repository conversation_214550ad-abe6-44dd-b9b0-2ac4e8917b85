package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "收款认领")
public class ReceiptClaim extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "来源系统编码(收款来源)")
    private String sourceSystemCode;

    @ApiModelProperty(value = "资金流水号")
    private String cashReceiptCode;

    @ApiModelProperty(value = "资金状态")
    private Integer cashStatus;

    @ApiModelProperty(value = "拆款状态(1未拆款 2已拆)")
    private Integer divideStatus;

    @ApiModelProperty(value = "付款方名称（客户名称/上手背书人）")
    private String payName;

    @ApiModelProperty(value = "付款方账号（付方银行账号）")
    private String payBankCode;

    @ApiModelProperty(value = "付款银行名称")
    private String payBankName;

    @ApiModelProperty(value = "票据号码")
    private String billCode;

    @ApiModelProperty(value = "付款金额（到账金额）")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "币种编码")
    private String currencyCode;

    @ApiModelProperty(value = "票据类型")
    private String billType;

    @ApiModelProperty(value = "结算方式")
    private String settleWay;

    @ApiModelProperty(value = "收款方法")
    private String recMethod;

    @ApiModelProperty(value = "收款方银行账号ID")
    private Long recBankId;

    @ApiModelProperty(value = "收款方银行帐号")
    private String recBankCode;

    @ApiModelProperty(value = "收款账号")
    private String recAccountNo;

    @ApiModelProperty(value = "收款单位名称")
    private String recOrgName;

    @ApiModelProperty(value = "收款单位编号")
    private String recOrgCode;

    @ApiModelProperty(value = "预算项目编号")
    private String budgetItemCode;

    @ApiModelProperty(value = "OU组织ID（业务实体ID）")
    private Long ouId;

    @ApiModelProperty(value = "到账日期(对应到期日)")
    private Date payDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "关联交易号")
    private String conntransCode;

    @ApiModelProperty(value = "中转关联号(等于收款编号)")
    private String transferCode;

    @ApiModelProperty(value = "出纳台账号")
    private String serialNumber;

    @ApiModelProperty(value = "crm客户信息编号")
    private String crmCustomerCode;

    @ApiModelProperty(value = "crm客户信息名称")
    private String crmCustomerName;

    @ApiModelProperty(value = "attribute1")
    private String attribute1;

    @ApiModelProperty(value = "attribute2")
    private String attribute2;

    @ApiModelProperty(value = "attribute3")
    private String attribute3;

    @ApiModelProperty(value = "attribute4")
    private String attribute4;

    @ApiModelProperty(value = "attribute5")
    private String attribute5;

    @ApiModelProperty(value = "attribute6")
    private String attribute6;

    @ApiModelProperty(value = "是否初始化导入: 0-否 1-是")
    private Boolean isImport;

    private Integer deletedFlag;

    @ApiModelProperty(value = "汇率类型")
    private String conversionType;

    @ApiModelProperty(value = "汇率时间（资金入账时间）")
    private Date conversionDate;

    @ApiModelProperty(value = "汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "客户间转款单据id")
    private Long customerTransferId;

    @ApiModelProperty(value = "来源 0-开票申请,1-人工转款,2-回款工单")
    private Integer source;

    private static final long serialVersionUID = 1L;

    public String getSourceSystemCode() {
        return sourceSystemCode;
    }

    public void setSourceSystemCode(String sourceSystemCode) {
        this.sourceSystemCode = sourceSystemCode == null ? null : sourceSystemCode.trim();
    }

    public String getCashReceiptCode() {
        return cashReceiptCode;
    }

    public void setCashReceiptCode(String cashReceiptCode) {
        this.cashReceiptCode = cashReceiptCode == null ? null : cashReceiptCode.trim();
    }

    public Integer getCashStatus() {
        return cashStatus;
    }

    public void setCashStatus(Integer cashStatus) {
        this.cashStatus = cashStatus;
    }

    public Integer getDivideStatus() {
        return divideStatus;
    }

    public void setDivideStatus(Integer divideStatus) {
        this.divideStatus = divideStatus;
    }

    public String getPayName() {
        return payName;
    }

    public void setPayName(String payName) {
        this.payName = payName == null ? null : payName.trim();
    }

    public String getPayBankCode() {
        return payBankCode;
    }

    public void setPayBankCode(String payBankCode) {
        this.payBankCode = payBankCode == null ? null : payBankCode.trim();
    }

    public String getPayBankName() {
        return payBankName;
    }

    public void setPayBankName(String payBankName) {
        this.payBankName = payBankName == null ? null : payBankName.trim();
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode == null ? null : billCode.trim();
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode == null ? null : currencyCode.trim();
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType == null ? null : billType.trim();
    }

    public String getSettleWay() {
        return settleWay;
    }

    public void setSettleWay(String settleWay) {
        this.settleWay = settleWay == null ? null : settleWay.trim();
    }

    public String getRecMethod() {
        return recMethod;
    }

    public void setRecMethod(String recMethod) {
        this.recMethod = recMethod == null ? null : recMethod.trim();
    }

    public Long getRecBankId() {
        return recBankId;
    }

    public void setRecBankId(Long recBankId) {
        this.recBankId = recBankId;
    }

    public String getRecBankCode() {
        return recBankCode;
    }

    public void setRecBankCode(String recBankCode) {
        this.recBankCode = recBankCode == null ? null : recBankCode.trim();
    }

    public String getRecAccountNo() {
        return recAccountNo;
    }

    public void setRecAccountNo(String recAccountNo) {
        this.recAccountNo = recAccountNo == null ? null : recAccountNo.trim();
    }

    public String getRecOrgName() {
        return recOrgName;
    }

    public void setRecOrgName(String recOrgName) {
        this.recOrgName = recOrgName == null ? null : recOrgName.trim();
    }

    public String getRecOrgCode() {
        return recOrgCode;
    }

    public void setRecOrgCode(String recOrgCode) {
        this.recOrgCode = recOrgCode == null ? null : recOrgCode.trim();
    }

    public String getBudgetItemCode() {
        return budgetItemCode;
    }

    public void setBudgetItemCode(String budgetItemCode) {
        this.budgetItemCode = budgetItemCode == null ? null : budgetItemCode.trim();
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getConntransCode() {
        return conntransCode;
    }

    public void setConntransCode(String conntransCode) {
        this.conntransCode = conntransCode == null ? null : conntransCode.trim();
    }

    public String getTransferCode() {
        return transferCode;
    }

    public void setTransferCode(String transferCode) {
        this.transferCode = transferCode == null ? null : transferCode.trim();
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber == null ? null : serialNumber.trim();
    }

    public String getCrmCustomerCode() {
        return crmCustomerCode;
    }

    public void setCrmCustomerCode(String crmCustomerCode) {
        this.crmCustomerCode = crmCustomerCode == null ? null : crmCustomerCode.trim();
    }

    public String getCrmCustomerName() {
        return crmCustomerName;
    }

    public void setCrmCustomerName(String crmCustomerName) {
        this.crmCustomerName = crmCustomerName == null ? null : crmCustomerName.trim();
    }

    public String getAttribute1() {
        return attribute1;
    }

    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1 == null ? null : attribute1.trim();
    }

    public String getAttribute2() {
        return attribute2;
    }

    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2 == null ? null : attribute2.trim();
    }

    public String getAttribute3() {
        return attribute3;
    }

    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3 == null ? null : attribute3.trim();
    }

    public String getAttribute4() {
        return attribute4;
    }

    public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4 == null ? null : attribute4.trim();
    }

    public String getAttribute5() {
        return attribute5;
    }

    public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5 == null ? null : attribute5.trim();
    }

    public String getAttribute6() {
        return attribute6;
    }

    public void setAttribute6(String attribute6) {
        this.attribute6 = attribute6 == null ? null : attribute6.trim();
    }

    public Boolean getIsImport() {
        return isImport;
    }

    public void setIsImport(Boolean isImport) {
        this.isImport = isImport;
    }

    public Integer getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Integer deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getConversionType() {
        return conversionType;
    }

    public void setConversionType(String conversionType) {
        this.conversionType = conversionType == null ? null : conversionType.trim();
    }

    public Date getConversionDate() {
        return conversionDate;
    }

    public void setConversionDate(Date conversionDate) {
        this.conversionDate = conversionDate;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public Long getCustomerTransferId() {
        return customerTransferId;
    }

    public void setCustomerTransferId(Long customerTransferId) {
        this.customerTransferId = customerTransferId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", sourceSystemCode=").append(sourceSystemCode);
        sb.append(", cashReceiptCode=").append(cashReceiptCode);
        sb.append(", cashStatus=").append(cashStatus);
        sb.append(", divideStatus=").append(divideStatus);
        sb.append(", payName=").append(payName);
        sb.append(", payBankCode=").append(payBankCode);
        sb.append(", payBankName=").append(payBankName);
        sb.append(", billCode=").append(billCode);
        sb.append(", payAmount=").append(payAmount);
        sb.append(", currencyCode=").append(currencyCode);
        sb.append(", billType=").append(billType);
        sb.append(", settleWay=").append(settleWay);
        sb.append(", recMethod=").append(recMethod);
        sb.append(", recBankId=").append(recBankId);
        sb.append(", recBankCode=").append(recBankCode);
        sb.append(", recAccountNo=").append(recAccountNo);
        sb.append(", recOrgName=").append(recOrgName);
        sb.append(", recOrgCode=").append(recOrgCode);
        sb.append(", budgetItemCode=").append(budgetItemCode);
        sb.append(", ouId=").append(ouId);
        sb.append(", payDate=").append(payDate);
        sb.append(", remark=").append(remark);
        sb.append(", conntransCode=").append(conntransCode);
        sb.append(", transferCode=").append(transferCode);
        sb.append(", serialNumber=").append(serialNumber);
        sb.append(", crmCustomerCode=").append(crmCustomerCode);
        sb.append(", crmCustomerName=").append(crmCustomerName);
        sb.append(", attribute1=").append(attribute1);
        sb.append(", attribute2=").append(attribute2);
        sb.append(", attribute3=").append(attribute3);
        sb.append(", attribute4=").append(attribute4);
        sb.append(", attribute5=").append(attribute5);
        sb.append(", attribute6=").append(attribute6);
        sb.append(", isImport=").append(isImport);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", conversionType=").append(conversionType);
        sb.append(", conversionDate=").append(conversionDate);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", customerTransferId=").append(customerTransferId);
        sb.append(", source=").append(source);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}