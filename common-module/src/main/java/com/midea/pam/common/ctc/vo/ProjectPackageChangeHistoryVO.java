package com.midea.pam.common.ctc.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectHistoryHeaderDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectProfitChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectTypeDto;
import com.midea.pam.common.ctc.entity.Project;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "ProjectPackageChangeHistoryVO", description = "项目变更包装类对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProjectPackageChangeHistoryVO<T extends IHistoryType> extends Project {

    @ApiModelProperty(value = "项目变更原因")
    private String reason;

    @ApiModelProperty(value = "业务实体")
    private String ouName;

    @ApiModelProperty(value = "项目类型名称")
    private String typeName;

    @ApiModelProperty(value = "预算部门名称")
    private String budgetDepName;

    @ApiModelProperty(value = "预算部门code")
    private String budgetDepCode;

    @ApiModelProperty(value = "库存组织id")
    private String storageId;

    @ApiModelProperty(value = "库存组织code")
    private String storageCode;

    @ApiModelProperty(value = "财务人员mip")
    private String financialMip;

    @ApiModelProperty(value = "财务人员名称")
    private String financialName;

    @ApiModelProperty(value = "项目经理mip")
    private String managerMip;

    @ApiModelProperty(value = "库存组织名称")
    private String storageName;

    @ApiModelProperty(value = "创建人名称")
    private String projectHistoryHeaderCreateName;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "头信息")
    private ProjectHistoryHeaderDto header;

    @ApiModelProperty(value = "辅里程碑")
    private List<ProjectChangeHistoryVO<ProjectMilepostChangeHistoryDto>> helpMileposts;

    @ApiModelProperty(value = "主里程碑")
    private List<ProjectChangeHistoryVO<ProjectMilepostChangeHistoryDto>> mainMileposts;

    @ApiModelProperty(value = "基础信息变更")
    private ProjectChangeHistoryVO<T> baseInfo;

    @ApiModelProperty(value = "目标成本变更")
    private ProjectChangeHistoryVO<T> budgetTargetInfo;

    @ApiModelProperty(value = "关联合同变更")
    private List<ProjectChangeHistoryVO> contractRsChangeInfo;

    @ApiModelProperty(value = "关联商机变更")
    private List<ProjectChangeHistoryVO> businessRsChangeInfo;

    @ApiModelProperty(value = "关联资产变更")
    private List<ProjectChangeHistoryVO> assetRsChangeInfo;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "变更后收入成本计划")
    private ProjectProfitChangeHistoryDto changeProfit;

    @ApiModelProperty(value = "变更前收入成本计划")
    private ProjectProfitChangeHistoryDto historyProfit;

    @ApiModelProperty("附件记录")
    private List<CtcAttachmentDto> attachmentDtos;

    @ApiModelProperty("是否启用里程碑基准日期")
    private Boolean milestoneBaseDate;

    /**
     * 项目类型校验
     */
    @ApiModelProperty(value = "项目类型")
    private ProjectTypeDto projectTypeDto;

    @ApiModelProperty(value = "项目")
    private ProjectDto projectDto;
}
