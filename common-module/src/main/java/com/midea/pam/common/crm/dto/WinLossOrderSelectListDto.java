package com.midea.pam.common.crm.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class WinLossOrderSelectListDto {

    /**
     * 商机ID
     */
    private Long businessId;

    /**
     * 商机状态
     */
    private Integer status;

    /**
     * 赢单/丢单日期
     */
    private Date winCreateAt;

    /**
     * 商机编号
     */
    private String businessCode;

    /**
     * 销售部门
     */
    private String unitName;

    /**
     * 客户群组
     */
    private String customerGroup;

    /**
     * 商机名称
     */
    private String name;

    /**
     * 赢单商机未税金额
     */
    private BigDecimal winAmount;

    /**
     * 丢单商机未税金额
     */
    private BigDecimal amount;

    /**
     * 预计发货时间
     */
    private Date estimateDeliveryAt;

    /**
     * 跟进人
     */
    private String ownerName;

    /**
     * 销售部门id
     */
    private Long roleDeptId;

    /**
     * 关闭原因分类
     */
    private String closeClass;

    /**
     * 中标方
     */
    private String successfulBidder;

    /**
     * 中标金额
     */
    private BigDecimal bidderAmount;

    /**
     * 销售部门负责人MIP
     */
    private String salesDepartmentMip;

    /**
     * 项目所在地
     */
    private String projectLocation;

    /**
     * 总负责人MIP
     */
    private String salesDepartmentTopMip;
}
