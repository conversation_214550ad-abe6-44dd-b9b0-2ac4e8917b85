package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PurchaseMaterialRequirementExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PurchaseMaterialRequirementExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNull() {
            addCriterion("erp_code is null");
            return (Criteria) this;
        }

        public Criteria andErpCodeIsNotNull() {
            addCriterion("erp_code is not null");
            return (Criteria) this;
        }

        public Criteria andErpCodeEqualTo(String value) {
            addCriterion("erp_code =", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotEqualTo(String value) {
            addCriterion("erp_code <>", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThan(String value) {
            addCriterion("erp_code >", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("erp_code >=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThan(String value) {
            addCriterion("erp_code <", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLessThanOrEqualTo(String value) {
            addCriterion("erp_code <=", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeLike(String value) {
            addCriterion("erp_code like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotLike(String value) {
            addCriterion("erp_code not like", value, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeIn(List<String> values) {
            addCriterion("erp_code in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotIn(List<String> values) {
            addCriterion("erp_code not in", values, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeBetween(String value1, String value2) {
            addCriterion("erp_code between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andErpCodeNotBetween(String value1, String value2) {
            addCriterion("erp_code not between", value1, value2, "erpCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNull() {
            addCriterion("pam_code is null");
            return (Criteria) this;
        }

        public Criteria andPamCodeIsNotNull() {
            addCriterion("pam_code is not null");
            return (Criteria) this;
        }

        public Criteria andPamCodeEqualTo(String value) {
            addCriterion("pam_code =", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotEqualTo(String value) {
            addCriterion("pam_code <>", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThan(String value) {
            addCriterion("pam_code >", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pam_code >=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThan(String value) {
            addCriterion("pam_code <", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLessThanOrEqualTo(String value) {
            addCriterion("pam_code <=", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeLike(String value) {
            addCriterion("pam_code like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotLike(String value) {
            addCriterion("pam_code not like", value, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeIn(List<String> values) {
            addCriterion("pam_code in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotIn(List<String> values) {
            addCriterion("pam_code not in", values, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeBetween(String value1, String value2) {
            addCriterion("pam_code between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andPamCodeNotBetween(String value1, String value2) {
            addCriterion("pam_code not between", value1, value2, "pamCode");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNull() {
            addCriterion("delivery_time is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNotNull() {
            addCriterion("delivery_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeEqualTo(Date value) {
            addCriterion("delivery_time =", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotEqualTo(Date value) {
            addCriterion("delivery_time <>", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThan(Date value) {
            addCriterion("delivery_time >", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("delivery_time >=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThan(Date value) {
            addCriterion("delivery_time <", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThanOrEqualTo(Date value) {
            addCriterion("delivery_time <=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIn(List<Date> values) {
            addCriterion("delivery_time in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotIn(List<Date> values) {
            addCriterion("delivery_time not in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeBetween(Date value1, Date value2) {
            addCriterion("delivery_time between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotBetween(Date value1, Date value2) {
            addCriterion("delivery_time not between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andMaterielIdIsNull() {
            addCriterion("materiel_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterielIdIsNotNull() {
            addCriterion("materiel_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielIdEqualTo(Long value) {
            addCriterion("materiel_id =", value, "materielId");
            return (Criteria) this;
        }

        public Criteria andMaterielIdNotEqualTo(Long value) {
            addCriterion("materiel_id <>", value, "materielId");
            return (Criteria) this;
        }

        public Criteria andMaterielIdGreaterThan(Long value) {
            addCriterion("materiel_id >", value, "materielId");
            return (Criteria) this;
        }

        public Criteria andMaterielIdGreaterThanOrEqualTo(Long value) {
            addCriterion("materiel_id >=", value, "materielId");
            return (Criteria) this;
        }

        public Criteria andMaterielIdLessThan(Long value) {
            addCriterion("materiel_id <", value, "materielId");
            return (Criteria) this;
        }

        public Criteria andMaterielIdLessThanOrEqualTo(Long value) {
            addCriterion("materiel_id <=", value, "materielId");
            return (Criteria) this;
        }

        public Criteria andMaterielIdIn(List<Long> values) {
            addCriterion("materiel_id in", values, "materielId");
            return (Criteria) this;
        }

        public Criteria andMaterielIdNotIn(List<Long> values) {
            addCriterion("materiel_id not in", values, "materielId");
            return (Criteria) this;
        }

        public Criteria andMaterielIdBetween(Long value1, Long value2) {
            addCriterion("materiel_id between", value1, value2, "materielId");
            return (Criteria) this;
        }

        public Criteria andMaterielIdNotBetween(Long value1, Long value2) {
            addCriterion("materiel_id not between", value1, value2, "materielId");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNull() {
            addCriterion("materiel_descr is null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIsNotNull() {
            addCriterion("materiel_descr is not null");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrEqualTo(String value) {
            addCriterion("materiel_descr =", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotEqualTo(String value) {
            addCriterion("materiel_descr <>", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThan(String value) {
            addCriterion("materiel_descr >", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrGreaterThanOrEqualTo(String value) {
            addCriterion("materiel_descr >=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThan(String value) {
            addCriterion("materiel_descr <", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLessThanOrEqualTo(String value) {
            addCriterion("materiel_descr <=", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrLike(String value) {
            addCriterion("materiel_descr like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotLike(String value) {
            addCriterion("materiel_descr not like", value, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrIn(List<String> values) {
            addCriterion("materiel_descr in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotIn(List<String> values) {
            addCriterion("materiel_descr not in", values, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrBetween(String value1, String value2) {
            addCriterion("materiel_descr between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andMaterielDescrNotBetween(String value1, String value2) {
            addCriterion("materiel_descr not between", value1, value2, "materielDescr");
            return (Criteria) this;
        }

        public Criteria andNeedTotalIsNull() {
            addCriterion("need_total is null");
            return (Criteria) this;
        }

        public Criteria andNeedTotalIsNotNull() {
            addCriterion("need_total is not null");
            return (Criteria) this;
        }

        public Criteria andNeedTotalEqualTo(BigDecimal value) {
            addCriterion("need_total =", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalNotEqualTo(BigDecimal value) {
            addCriterion("need_total <>", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalGreaterThan(BigDecimal value) {
            addCriterion("need_total >", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("need_total >=", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalLessThan(BigDecimal value) {
            addCriterion("need_total <", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("need_total <=", value, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalIn(List<BigDecimal> values) {
            addCriterion("need_total in", values, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalNotIn(List<BigDecimal> values) {
            addCriterion("need_total not in", values, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("need_total between", value1, value2, "needTotal");
            return (Criteria) this;
        }

        public Criteria andNeedTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("need_total not between", value1, value2, "needTotal");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIsNull() {
            addCriterion("unit_code is null");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIsNotNull() {
            addCriterion("unit_code is not null");
            return (Criteria) this;
        }

        public Criteria andUnitCodeEqualTo(String value) {
            addCriterion("unit_code =", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotEqualTo(String value) {
            addCriterion("unit_code <>", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeGreaterThan(String value) {
            addCriterion("unit_code >", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeGreaterThanOrEqualTo(String value) {
            addCriterion("unit_code >=", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLessThan(String value) {
            addCriterion("unit_code <", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLessThanOrEqualTo(String value) {
            addCriterion("unit_code <=", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLike(String value) {
            addCriterion("unit_code like", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotLike(String value) {
            addCriterion("unit_code not like", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIn(List<String> values) {
            addCriterion("unit_code in", values, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotIn(List<String> values) {
            addCriterion("unit_code not in", values, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeBetween(String value1, String value2) {
            addCriterion("unit_code between", value1, value2, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotBetween(String value1, String value2) {
            addCriterion("unit_code not between", value1, value2, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberIsNull() {
            addCriterion("approved_supplier_number is null");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberIsNotNull() {
            addCriterion("approved_supplier_number is not null");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberEqualTo(Integer value) {
            addCriterion("approved_supplier_number =", value, "approvedSupplierNumber");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberNotEqualTo(Integer value) {
            addCriterion("approved_supplier_number <>", value, "approvedSupplierNumber");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberGreaterThan(Integer value) {
            addCriterion("approved_supplier_number >", value, "approvedSupplierNumber");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("approved_supplier_number >=", value, "approvedSupplierNumber");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberLessThan(Integer value) {
            addCriterion("approved_supplier_number <", value, "approvedSupplierNumber");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberLessThanOrEqualTo(Integer value) {
            addCriterion("approved_supplier_number <=", value, "approvedSupplierNumber");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberIn(List<Integer> values) {
            addCriterion("approved_supplier_number in", values, "approvedSupplierNumber");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberNotIn(List<Integer> values) {
            addCriterion("approved_supplier_number not in", values, "approvedSupplierNumber");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberBetween(Integer value1, Integer value2) {
            addCriterion("approved_supplier_number between", value1, value2, "approvedSupplierNumber");
            return (Criteria) this;
        }

        public Criteria andApprovedSupplierNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("approved_supplier_number not between", value1, value2, "approvedSupplierNumber");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andReasonIsNull() {
            addCriterion("reason is null");
            return (Criteria) this;
        }

        public Criteria andReasonIsNotNull() {
            addCriterion("reason is not null");
            return (Criteria) this;
        }

        public Criteria andReasonEqualTo(String value) {
            addCriterion("reason =", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotEqualTo(String value) {
            addCriterion("reason <>", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThan(String value) {
            addCriterion("reason >", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonGreaterThanOrEqualTo(String value) {
            addCriterion("reason >=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThan(String value) {
            addCriterion("reason <", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLessThanOrEqualTo(String value) {
            addCriterion("reason <=", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonLike(String value) {
            addCriterion("reason like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotLike(String value) {
            addCriterion("reason not like", value, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonIn(List<String> values) {
            addCriterion("reason in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotIn(List<String> values) {
            addCriterion("reason not in", values, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonBetween(String value1, String value2) {
            addCriterion("reason between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andReasonNotBetween(String value1, String value2) {
            addCriterion("reason not between", value1, value2, "reason");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIsNull() {
            addCriterion("conversion_type is null");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIsNotNull() {
            addCriterion("conversion_type is not null");
            return (Criteria) this;
        }

        public Criteria andConversionTypeEqualTo(String value) {
            addCriterion("conversion_type =", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotEqualTo(String value) {
            addCriterion("conversion_type <>", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeGreaterThan(String value) {
            addCriterion("conversion_type >", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("conversion_type >=", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLessThan(String value) {
            addCriterion("conversion_type <", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLessThanOrEqualTo(String value) {
            addCriterion("conversion_type <=", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLike(String value) {
            addCriterion("conversion_type like", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotLike(String value) {
            addCriterion("conversion_type not like", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIn(List<String> values) {
            addCriterion("conversion_type in", values, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotIn(List<String> values) {
            addCriterion("conversion_type not in", values, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeBetween(String value1, String value2) {
            addCriterion("conversion_type between", value1, value2, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotBetween(String value1, String value2) {
            addCriterion("conversion_type not between", value1, value2, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionDateIsNull() {
            addCriterion("conversion_date is null");
            return (Criteria) this;
        }

        public Criteria andConversionDateIsNotNull() {
            addCriterion("conversion_date is not null");
            return (Criteria) this;
        }

        public Criteria andConversionDateEqualTo(Date value) {
            addCriterion("conversion_date =", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotEqualTo(Date value) {
            addCriterion("conversion_date <>", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateGreaterThan(Date value) {
            addCriterion("conversion_date >", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateGreaterThanOrEqualTo(Date value) {
            addCriterion("conversion_date >=", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateLessThan(Date value) {
            addCriterion("conversion_date <", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateLessThanOrEqualTo(Date value) {
            addCriterion("conversion_date <=", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateIn(List<Date> values) {
            addCriterion("conversion_date in", values, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotIn(List<Date> values) {
            addCriterion("conversion_date not in", values, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateBetween(Date value1, Date value2) {
            addCriterion("conversion_date between", value1, value2, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotBetween(Date value1, Date value2) {
            addCriterion("conversion_date not between", value1, value2, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNull() {
            addCriterion("conversion_rate is null");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNotNull() {
            addCriterion("conversion_rate is not null");
            return (Criteria) this;
        }

        public Criteria andConversionRateEqualTo(BigDecimal value) {
            addCriterion("conversion_rate =", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <>", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThan(BigDecimal value) {
            addCriterion("conversion_rate >", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate >=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThan(BigDecimal value) {
            addCriterion("conversion_rate <", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateIn(List<BigDecimal> values) {
            addCriterion("conversion_rate in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotIn(List<BigDecimal> values) {
            addCriterion("conversion_rate not in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate not between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeIsNull() {
            addCriterion("purchase_type is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeIsNotNull() {
            addCriterion("purchase_type is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeEqualTo(Integer value) {
            addCriterion("purchase_type =", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeNotEqualTo(Integer value) {
            addCriterion("purchase_type <>", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeGreaterThan(Integer value) {
            addCriterion("purchase_type >", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("purchase_type >=", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeLessThan(Integer value) {
            addCriterion("purchase_type <", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeLessThanOrEqualTo(Integer value) {
            addCriterion("purchase_type <=", value, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeIn(List<Integer> values) {
            addCriterion("purchase_type in", values, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeNotIn(List<Integer> values) {
            addCriterion("purchase_type not in", values, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeBetween(Integer value1, Integer value2) {
            addCriterion("purchase_type between", value1, value2, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andPurchaseTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("purchase_type not between", value1, value2, "purchaseType");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdIsNull() {
            addCriterion("project_wbs_receipts_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdIsNotNull() {
            addCriterion("project_wbs_receipts_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id =", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdNotEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id <>", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdGreaterThan(Long value) {
            addCriterion("project_wbs_receipts_id >", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id >=", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdLessThan(Long value) {
            addCriterion("project_wbs_receipts_id <", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdLessThanOrEqualTo(Long value) {
            addCriterion("project_wbs_receipts_id <=", value, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdIn(List<Long> values) {
            addCriterion("project_wbs_receipts_id in", values, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdNotIn(List<Long> values) {
            addCriterion("project_wbs_receipts_id not in", values, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdBetween(Long value1, Long value2) {
            addCriterion("project_wbs_receipts_id between", value1, value2, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andProjectWbsReceiptsIdNotBetween(Long value1, Long value2) {
            addCriterion("project_wbs_receipts_id not between", value1, value2, "projectWbsReceiptsId");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNull() {
            addCriterion("requirement_code is null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIsNotNull() {
            addCriterion("requirement_code is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeEqualTo(String value) {
            addCriterion("requirement_code =", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotEqualTo(String value) {
            addCriterion("requirement_code <>", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThan(String value) {
            addCriterion("requirement_code >", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeGreaterThanOrEqualTo(String value) {
            addCriterion("requirement_code >=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThan(String value) {
            addCriterion("requirement_code <", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLessThanOrEqualTo(String value) {
            addCriterion("requirement_code <=", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeLike(String value) {
            addCriterion("requirement_code like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotLike(String value) {
            addCriterion("requirement_code not like", value, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeIn(List<String> values) {
            addCriterion("requirement_code in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotIn(List<String> values) {
            addCriterion("requirement_code not in", values, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeBetween(String value1, String value2) {
            addCriterion("requirement_code between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andRequirementCodeNotBetween(String value1, String value2) {
            addCriterion("requirement_code not between", value1, value2, "requirementCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNull() {
            addCriterion("activity_code is null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIsNotNull() {
            addCriterion("activity_code is not null");
            return (Criteria) this;
        }

        public Criteria andActivityCodeEqualTo(String value) {
            addCriterion("activity_code =", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotEqualTo(String value) {
            addCriterion("activity_code <>", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThan(String value) {
            addCriterion("activity_code >", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("activity_code >=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThan(String value) {
            addCriterion("activity_code <", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLessThanOrEqualTo(String value) {
            addCriterion("activity_code <=", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeLike(String value) {
            addCriterion("activity_code like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotLike(String value) {
            addCriterion("activity_code not like", value, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeIn(List<String> values) {
            addCriterion("activity_code in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotIn(List<String> values) {
            addCriterion("activity_code not in", values, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeBetween(String value1, String value2) {
            addCriterion("activity_code between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andActivityCodeNotBetween(String value1, String value2) {
            addCriterion("activity_code not between", value1, value2, "activityCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNull() {
            addCriterion("wbs_summary_code is null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIsNotNull() {
            addCriterion("wbs_summary_code is not null");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeEqualTo(String value) {
            addCriterion("wbs_summary_code =", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotEqualTo(String value) {
            addCriterion("wbs_summary_code <>", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThan(String value) {
            addCriterion("wbs_summary_code >", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code >=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThan(String value) {
            addCriterion("wbs_summary_code <", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLessThanOrEqualTo(String value) {
            addCriterion("wbs_summary_code <=", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeLike(String value) {
            addCriterion("wbs_summary_code like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotLike(String value) {
            addCriterion("wbs_summary_code not like", value, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeIn(List<String> values) {
            addCriterion("wbs_summary_code in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotIn(List<String> values) {
            addCriterion("wbs_summary_code not in", values, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeBetween(String value1, String value2) {
            addCriterion("wbs_summary_code between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andWbsSummaryCodeNotBetween(String value1, String value2) {
            addCriterion("wbs_summary_code not between", value1, value2, "wbsSummaryCode");
            return (Criteria) this;
        }

        public Criteria andDispatchIsIsNull() {
            addCriterion("dispatch_is is null");
            return (Criteria) this;
        }

        public Criteria andDispatchIsIsNotNull() {
            addCriterion("dispatch_is is not null");
            return (Criteria) this;
        }

        public Criteria andDispatchIsEqualTo(Boolean value) {
            addCriterion("dispatch_is =", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsNotEqualTo(Boolean value) {
            addCriterion("dispatch_is <>", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsGreaterThan(Boolean value) {
            addCriterion("dispatch_is >", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("dispatch_is >=", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsLessThan(Boolean value) {
            addCriterion("dispatch_is <", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsLessThanOrEqualTo(Boolean value) {
            addCriterion("dispatch_is <=", value, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsIn(List<Boolean> values) {
            addCriterion("dispatch_is in", values, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsNotIn(List<Boolean> values) {
            addCriterion("dispatch_is not in", values, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsBetween(Boolean value1, Boolean value2) {
            addCriterion("dispatch_is between", value1, value2, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andDispatchIsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("dispatch_is not between", value1, value2, "dispatchIs");
            return (Criteria) this;
        }

        public Criteria andClosedAmountIsNull() {
            addCriterion("closed_amount is null");
            return (Criteria) this;
        }

        public Criteria andClosedAmountIsNotNull() {
            addCriterion("closed_amount is not null");
            return (Criteria) this;
        }

        public Criteria andClosedAmountEqualTo(BigDecimal value) {
            addCriterion("closed_amount =", value, "closedAmount");
            return (Criteria) this;
        }

        public Criteria andClosedAmountNotEqualTo(BigDecimal value) {
            addCriterion("closed_amount <>", value, "closedAmount");
            return (Criteria) this;
        }

        public Criteria andClosedAmountGreaterThan(BigDecimal value) {
            addCriterion("closed_amount >", value, "closedAmount");
            return (Criteria) this;
        }

        public Criteria andClosedAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("closed_amount >=", value, "closedAmount");
            return (Criteria) this;
        }

        public Criteria andClosedAmountLessThan(BigDecimal value) {
            addCriterion("closed_amount <", value, "closedAmount");
            return (Criteria) this;
        }

        public Criteria andClosedAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("closed_amount <=", value, "closedAmount");
            return (Criteria) this;
        }

        public Criteria andClosedAmountIn(List<BigDecimal> values) {
            addCriterion("closed_amount in", values, "closedAmount");
            return (Criteria) this;
        }

        public Criteria andClosedAmountNotIn(List<BigDecimal> values) {
            addCriterion("closed_amount not in", values, "closedAmount");
            return (Criteria) this;
        }

        public Criteria andClosedAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("closed_amount between", value1, value2, "closedAmount");
            return (Criteria) this;
        }

        public Criteria andClosedAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("closed_amount not between", value1, value2, "closedAmount");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityIsNull() {
            addCriterion("closed_quantity is null");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityIsNotNull() {
            addCriterion("closed_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityEqualTo(BigDecimal value) {
            addCriterion("closed_quantity =", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityNotEqualTo(BigDecimal value) {
            addCriterion("closed_quantity <>", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityGreaterThan(BigDecimal value) {
            addCriterion("closed_quantity >", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("closed_quantity >=", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityLessThan(BigDecimal value) {
            addCriterion("closed_quantity <", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityLessThanOrEqualTo(BigDecimal value) {
            addCriterion("closed_quantity <=", value, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityIn(List<BigDecimal> values) {
            addCriterion("closed_quantity in", values, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityNotIn(List<BigDecimal> values) {
            addCriterion("closed_quantity not in", values, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("closed_quantity between", value1, value2, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andClosedQuantityNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("closed_quantity not between", value1, value2, "closedQuantity");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostIsNull() {
            addCriterion("wbs_demand_os_cost is null");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostIsNotNull() {
            addCriterion("wbs_demand_os_cost is not null");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostEqualTo(BigDecimal value) {
            addCriterion("wbs_demand_os_cost =", value, "wbsDemandOsCost");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostNotEqualTo(BigDecimal value) {
            addCriterion("wbs_demand_os_cost <>", value, "wbsDemandOsCost");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostGreaterThan(BigDecimal value) {
            addCriterion("wbs_demand_os_cost >", value, "wbsDemandOsCost");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("wbs_demand_os_cost >=", value, "wbsDemandOsCost");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostLessThan(BigDecimal value) {
            addCriterion("wbs_demand_os_cost <", value, "wbsDemandOsCost");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("wbs_demand_os_cost <=", value, "wbsDemandOsCost");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostIn(List<BigDecimal> values) {
            addCriterion("wbs_demand_os_cost in", values, "wbsDemandOsCost");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostNotIn(List<BigDecimal> values) {
            addCriterion("wbs_demand_os_cost not in", values, "wbsDemandOsCost");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("wbs_demand_os_cost between", value1, value2, "wbsDemandOsCost");
            return (Criteria) this;
        }

        public Criteria andWbsDemandOsCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("wbs_demand_os_cost not between", value1, value2, "wbsDemandOsCost");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNull() {
            addCriterion("figure_number is null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIsNotNull() {
            addCriterion("figure_number is not null");
            return (Criteria) this;
        }

        public Criteria andFigureNumberEqualTo(String value) {
            addCriterion("figure_number =", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotEqualTo(String value) {
            addCriterion("figure_number <>", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThan(String value) {
            addCriterion("figure_number >", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberGreaterThanOrEqualTo(String value) {
            addCriterion("figure_number >=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThan(String value) {
            addCriterion("figure_number <", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLessThanOrEqualTo(String value) {
            addCriterion("figure_number <=", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberLike(String value) {
            addCriterion("figure_number like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotLike(String value) {
            addCriterion("figure_number not like", value, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberIn(List<String> values) {
            addCriterion("figure_number in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotIn(List<String> values) {
            addCriterion("figure_number not in", values, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberBetween(String value1, String value2) {
            addCriterion("figure_number between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andFigureNumberNotBetween(String value1, String value2) {
            addCriterion("figure_number not between", value1, value2, "figureNumber");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateIsNull() {
            addCriterion("receipts_publish_date is null");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateIsNotNull() {
            addCriterion("receipts_publish_date is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateEqualTo(Date value) {
            addCriterion("receipts_publish_date =", value, "receiptsPublishDate");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateNotEqualTo(Date value) {
            addCriterion("receipts_publish_date <>", value, "receiptsPublishDate");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateGreaterThan(Date value) {
            addCriterion("receipts_publish_date >", value, "receiptsPublishDate");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateGreaterThanOrEqualTo(Date value) {
            addCriterion("receipts_publish_date >=", value, "receiptsPublishDate");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateLessThan(Date value) {
            addCriterion("receipts_publish_date <", value, "receiptsPublishDate");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateLessThanOrEqualTo(Date value) {
            addCriterion("receipts_publish_date <=", value, "receiptsPublishDate");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateIn(List<Date> values) {
            addCriterion("receipts_publish_date in", values, "receiptsPublishDate");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateNotIn(List<Date> values) {
            addCriterion("receipts_publish_date not in", values, "receiptsPublishDate");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateBetween(Date value1, Date value2) {
            addCriterion("receipts_publish_date between", value1, value2, "receiptsPublishDate");
            return (Criteria) this;
        }

        public Criteria andReceiptsPublishDateNotBetween(Date value1, Date value2) {
            addCriterion("receipts_publish_date not between", value1, value2, "receiptsPublishDate");
            return (Criteria) this;
        }

        public Criteria andHasAmountIsNull() {
            addCriterion("has_amount is null");
            return (Criteria) this;
        }

        public Criteria andHasAmountIsNotNull() {
            addCriterion("has_amount is not null");
            return (Criteria) this;
        }

        public Criteria andHasAmountEqualTo(Integer value) {
            addCriterion("has_amount =", value, "hasAmount");
            return (Criteria) this;
        }

        public Criteria andHasAmountNotEqualTo(Integer value) {
            addCriterion("has_amount <>", value, "hasAmount");
            return (Criteria) this;
        }

        public Criteria andHasAmountGreaterThan(Integer value) {
            addCriterion("has_amount >", value, "hasAmount");
            return (Criteria) this;
        }

        public Criteria andHasAmountGreaterThanOrEqualTo(Integer value) {
            addCriterion("has_amount >=", value, "hasAmount");
            return (Criteria) this;
        }

        public Criteria andHasAmountLessThan(Integer value) {
            addCriterion("has_amount <", value, "hasAmount");
            return (Criteria) this;
        }

        public Criteria andHasAmountLessThanOrEqualTo(Integer value) {
            addCriterion("has_amount <=", value, "hasAmount");
            return (Criteria) this;
        }

        public Criteria andHasAmountIn(List<Integer> values) {
            addCriterion("has_amount in", values, "hasAmount");
            return (Criteria) this;
        }

        public Criteria andHasAmountNotIn(List<Integer> values) {
            addCriterion("has_amount not in", values, "hasAmount");
            return (Criteria) this;
        }

        public Criteria andHasAmountBetween(Integer value1, Integer value2) {
            addCriterion("has_amount between", value1, value2, "hasAmount");
            return (Criteria) this;
        }

        public Criteria andHasAmountNotBetween(Integer value1, Integer value2) {
            addCriterion("has_amount not between", value1, value2, "hasAmount");
            return (Criteria) this;
        }

        public Criteria andDueAmountIsNull() {
            addCriterion("due_amount is null");
            return (Criteria) this;
        }

        public Criteria andDueAmountIsNotNull() {
            addCriterion("due_amount is not null");
            return (Criteria) this;
        }

        public Criteria andDueAmountEqualTo(Integer value) {
            addCriterion("due_amount =", value, "dueAmount");
            return (Criteria) this;
        }

        public Criteria andDueAmountNotEqualTo(Integer value) {
            addCriterion("due_amount <>", value, "dueAmount");
            return (Criteria) this;
        }

        public Criteria andDueAmountGreaterThan(Integer value) {
            addCriterion("due_amount >", value, "dueAmount");
            return (Criteria) this;
        }

        public Criteria andDueAmountGreaterThanOrEqualTo(Integer value) {
            addCriterion("due_amount >=", value, "dueAmount");
            return (Criteria) this;
        }

        public Criteria andDueAmountLessThan(Integer value) {
            addCriterion("due_amount <", value, "dueAmount");
            return (Criteria) this;
        }

        public Criteria andDueAmountLessThanOrEqualTo(Integer value) {
            addCriterion("due_amount <=", value, "dueAmount");
            return (Criteria) this;
        }

        public Criteria andDueAmountIn(List<Integer> values) {
            addCriterion("due_amount in", values, "dueAmount");
            return (Criteria) this;
        }

        public Criteria andDueAmountNotIn(List<Integer> values) {
            addCriterion("due_amount not in", values, "dueAmount");
            return (Criteria) this;
        }

        public Criteria andDueAmountBetween(Integer value1, Integer value2) {
            addCriterion("due_amount between", value1, value2, "dueAmount");
            return (Criteria) this;
        }

        public Criteria andDueAmountNotBetween(Integer value1, Integer value2) {
            addCriterion("due_amount not between", value1, value2, "dueAmount");
            return (Criteria) this;
        }

        public Criteria andChartVersionIsNull() {
            addCriterion("chart_version is null");
            return (Criteria) this;
        }

        public Criteria andChartVersionIsNotNull() {
            addCriterion("chart_version is not null");
            return (Criteria) this;
        }

        public Criteria andChartVersionEqualTo(String value) {
            addCriterion("chart_version =", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotEqualTo(String value) {
            addCriterion("chart_version <>", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionGreaterThan(String value) {
            addCriterion("chart_version >", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionGreaterThanOrEqualTo(String value) {
            addCriterion("chart_version >=", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLessThan(String value) {
            addCriterion("chart_version <", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLessThanOrEqualTo(String value) {
            addCriterion("chart_version <=", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionLike(String value) {
            addCriterion("chart_version like", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotLike(String value) {
            addCriterion("chart_version not like", value, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionIn(List<String> values) {
            addCriterion("chart_version in", values, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotIn(List<String> values) {
            addCriterion("chart_version not in", values, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionBetween(String value1, String value2) {
            addCriterion("chart_version between", value1, value2, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andChartVersionNotBetween(String value1, String value2) {
            addCriterion("chart_version not between", value1, value2, "chartVersion");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIsNull() {
            addCriterion("design_release_lot_number is null");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIsNotNull() {
            addCriterion("design_release_lot_number is not null");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberEqualTo(String value) {
            addCriterion("design_release_lot_number =", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotEqualTo(String value) {
            addCriterion("design_release_lot_number <>", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberGreaterThan(String value) {
            addCriterion("design_release_lot_number >", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberGreaterThanOrEqualTo(String value) {
            addCriterion("design_release_lot_number >=", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLessThan(String value) {
            addCriterion("design_release_lot_number <", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLessThanOrEqualTo(String value) {
            addCriterion("design_release_lot_number <=", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberLike(String value) {
            addCriterion("design_release_lot_number like", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotLike(String value) {
            addCriterion("design_release_lot_number not like", value, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberIn(List<String> values) {
            addCriterion("design_release_lot_number in", values, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotIn(List<String> values) {
            addCriterion("design_release_lot_number not in", values, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberBetween(String value1, String value2) {
            addCriterion("design_release_lot_number between", value1, value2, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andDesignReleaseLotNumberNotBetween(String value1, String value2) {
            addCriterion("design_release_lot_number not between", value1, value2, "designReleaseLotNumber");
            return (Criteria) this;
        }

        public Criteria andInitIsNull() {
            addCriterion("init is null");
            return (Criteria) this;
        }

        public Criteria andInitIsNotNull() {
            addCriterion("init is not null");
            return (Criteria) this;
        }

        public Criteria andInitEqualTo(Boolean value) {
            addCriterion("init =", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitNotEqualTo(Boolean value) {
            addCriterion("init <>", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitGreaterThan(Boolean value) {
            addCriterion("init >", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitGreaterThanOrEqualTo(Boolean value) {
            addCriterion("init >=", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitLessThan(Boolean value) {
            addCriterion("init <", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitLessThanOrEqualTo(Boolean value) {
            addCriterion("init <=", value, "init");
            return (Criteria) this;
        }

        public Criteria andInitIn(List<Boolean> values) {
            addCriterion("init in", values, "init");
            return (Criteria) this;
        }

        public Criteria andInitNotIn(List<Boolean> values) {
            addCriterion("init not in", values, "init");
            return (Criteria) this;
        }

        public Criteria andInitBetween(Boolean value1, Boolean value2) {
            addCriterion("init between", value1, value2, "init");
            return (Criteria) this;
        }

        public Criteria andInitNotBetween(Boolean value1, Boolean value2) {
            addCriterion("init not between", value1, value2, "init");
            return (Criteria) this;
        }

        public Criteria andInitSequenceIsNull() {
            addCriterion("init_sequence is null");
            return (Criteria) this;
        }

        public Criteria andInitSequenceIsNotNull() {
            addCriterion("init_sequence is not null");
            return (Criteria) this;
        }

        public Criteria andInitSequenceEqualTo(String value) {
            addCriterion("init_sequence =", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceNotEqualTo(String value) {
            addCriterion("init_sequence <>", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceGreaterThan(String value) {
            addCriterion("init_sequence >", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceGreaterThanOrEqualTo(String value) {
            addCriterion("init_sequence >=", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceLessThan(String value) {
            addCriterion("init_sequence <", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceLessThanOrEqualTo(String value) {
            addCriterion("init_sequence <=", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceLike(String value) {
            addCriterion("init_sequence like", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceNotLike(String value) {
            addCriterion("init_sequence not like", value, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceIn(List<String> values) {
            addCriterion("init_sequence in", values, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceNotIn(List<String> values) {
            addCriterion("init_sequence not in", values, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceBetween(String value1, String value2) {
            addCriterion("init_sequence between", value1, value2, "initSequence");
            return (Criteria) this;
        }

        public Criteria andInitSequenceNotBetween(String value1, String value2) {
            addCriterion("init_sequence not between", value1, value2, "initSequence");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIsNull() {
            addCriterion("requirement_type is null");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIsNotNull() {
            addCriterion("requirement_type is not null");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeEqualTo(Integer value) {
            addCriterion("requirement_type =", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotEqualTo(Integer value) {
            addCriterion("requirement_type <>", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeGreaterThan(Integer value) {
            addCriterion("requirement_type >", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("requirement_type >=", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeLessThan(Integer value) {
            addCriterion("requirement_type <", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeLessThanOrEqualTo(Integer value) {
            addCriterion("requirement_type <=", value, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeIn(List<Integer> values) {
            addCriterion("requirement_type in", values, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotIn(List<Integer> values) {
            addCriterion("requirement_type not in", values, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeBetween(Integer value1, Integer value2) {
            addCriterion("requirement_type between", value1, value2, "requirementType");
            return (Criteria) this;
        }

        public Criteria andRequirementTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("requirement_type not between", value1, value2, "requirementType");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressIsNull() {
            addCriterion("delivery_address is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressIsNotNull() {
            addCriterion("delivery_address is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressEqualTo(String value) {
            addCriterion("delivery_address =", value, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressNotEqualTo(String value) {
            addCriterion("delivery_address <>", value, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressGreaterThan(String value) {
            addCriterion("delivery_address >", value, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_address >=", value, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressLessThan(String value) {
            addCriterion("delivery_address <", value, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressLessThanOrEqualTo(String value) {
            addCriterion("delivery_address <=", value, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressLike(String value) {
            addCriterion("delivery_address like", value, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressNotLike(String value) {
            addCriterion("delivery_address not like", value, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressIn(List<String> values) {
            addCriterion("delivery_address in", values, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressNotIn(List<String> values) {
            addCriterion("delivery_address not in", values, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressBetween(String value1, String value2) {
            addCriterion("delivery_address between", value1, value2, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andDeliveryAddressNotBetween(String value1, String value2) {
            addCriterion("delivery_address not between", value1, value2, "deliveryAddress");
            return (Criteria) this;
        }

        public Criteria andConsigneeIsNull() {
            addCriterion("consignee is null");
            return (Criteria) this;
        }

        public Criteria andConsigneeIsNotNull() {
            addCriterion("consignee is not null");
            return (Criteria) this;
        }

        public Criteria andConsigneeEqualTo(String value) {
            addCriterion("consignee =", value, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeNotEqualTo(String value) {
            addCriterion("consignee <>", value, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeGreaterThan(String value) {
            addCriterion("consignee >", value, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeGreaterThanOrEqualTo(String value) {
            addCriterion("consignee >=", value, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeLessThan(String value) {
            addCriterion("consignee <", value, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeLessThanOrEqualTo(String value) {
            addCriterion("consignee <=", value, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeLike(String value) {
            addCriterion("consignee like", value, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeNotLike(String value) {
            addCriterion("consignee not like", value, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeIn(List<String> values) {
            addCriterion("consignee in", values, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeNotIn(List<String> values) {
            addCriterion("consignee not in", values, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeBetween(String value1, String value2) {
            addCriterion("consignee between", value1, value2, "consignee");
            return (Criteria) this;
        }

        public Criteria andConsigneeNotBetween(String value1, String value2) {
            addCriterion("consignee not between", value1, value2, "consignee");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNull() {
            addCriterion("contact_phone is null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNotNull() {
            addCriterion("contact_phone is not null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneEqualTo(String value) {
            addCriterion("contact_phone =", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotEqualTo(String value) {
            addCriterion("contact_phone <>", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThan(String value) {
            addCriterion("contact_phone >", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("contact_phone >=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThan(String value) {
            addCriterion("contact_phone <", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("contact_phone <=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLike(String value) {
            addCriterion("contact_phone like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotLike(String value) {
            addCriterion("contact_phone not like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIn(List<String> values) {
            addCriterion("contact_phone in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotIn(List<String> values) {
            addCriterion("contact_phone not in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneBetween(String value1, String value2) {
            addCriterion("contact_phone between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotBetween(String value1, String value2) {
            addCriterion("contact_phone not between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagIsNull() {
            addCriterion("freeze_flag is null");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagIsNotNull() {
            addCriterion("freeze_flag is not null");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagEqualTo(Integer value) {
            addCriterion("freeze_flag =", value, "freezeFlag");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagNotEqualTo(Integer value) {
            addCriterion("freeze_flag <>", value, "freezeFlag");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagGreaterThan(Integer value) {
            addCriterion("freeze_flag >", value, "freezeFlag");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("freeze_flag >=", value, "freezeFlag");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagLessThan(Integer value) {
            addCriterion("freeze_flag <", value, "freezeFlag");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagLessThanOrEqualTo(Integer value) {
            addCriterion("freeze_flag <=", value, "freezeFlag");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagIn(List<Integer> values) {
            addCriterion("freeze_flag in", values, "freezeFlag");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagNotIn(List<Integer> values) {
            addCriterion("freeze_flag not in", values, "freezeFlag");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagBetween(Integer value1, Integer value2) {
            addCriterion("freeze_flag between", value1, value2, "freezeFlag");
            return (Criteria) this;
        }

        public Criteria andFreezeFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("freeze_flag not between", value1, value2, "freezeFlag");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonIsNull() {
            addCriterion("freeze_reason is null");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonIsNotNull() {
            addCriterion("freeze_reason is not null");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonEqualTo(String value) {
            addCriterion("freeze_reason =", value, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonNotEqualTo(String value) {
            addCriterion("freeze_reason <>", value, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonGreaterThan(String value) {
            addCriterion("freeze_reason >", value, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonGreaterThanOrEqualTo(String value) {
            addCriterion("freeze_reason >=", value, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonLessThan(String value) {
            addCriterion("freeze_reason <", value, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonLessThanOrEqualTo(String value) {
            addCriterion("freeze_reason <=", value, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonLike(String value) {
            addCriterion("freeze_reason like", value, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonNotLike(String value) {
            addCriterion("freeze_reason not like", value, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonIn(List<String> values) {
            addCriterion("freeze_reason in", values, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonNotIn(List<String> values) {
            addCriterion("freeze_reason not in", values, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonBetween(String value1, String value2) {
            addCriterion("freeze_reason between", value1, value2, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeReasonNotBetween(String value1, String value2) {
            addCriterion("freeze_reason not between", value1, value2, "freezeReason");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameIsNull() {
            addCriterion("freeze_by_name is null");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameIsNotNull() {
            addCriterion("freeze_by_name is not null");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameEqualTo(String value) {
            addCriterion("freeze_by_name =", value, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameNotEqualTo(String value) {
            addCriterion("freeze_by_name <>", value, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameGreaterThan(String value) {
            addCriterion("freeze_by_name >", value, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameGreaterThanOrEqualTo(String value) {
            addCriterion("freeze_by_name >=", value, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameLessThan(String value) {
            addCriterion("freeze_by_name <", value, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameLessThanOrEqualTo(String value) {
            addCriterion("freeze_by_name <=", value, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameLike(String value) {
            addCriterion("freeze_by_name like", value, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameNotLike(String value) {
            addCriterion("freeze_by_name not like", value, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameIn(List<String> values) {
            addCriterion("freeze_by_name in", values, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameNotIn(List<String> values) {
            addCriterion("freeze_by_name not in", values, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameBetween(String value1, String value2) {
            addCriterion("freeze_by_name between", value1, value2, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeByNameNotBetween(String value1, String value2) {
            addCriterion("freeze_by_name not between", value1, value2, "freezeByName");
            return (Criteria) this;
        }

        public Criteria andFreezeAtIsNull() {
            addCriterion("freeze_at is null");
            return (Criteria) this;
        }

        public Criteria andFreezeAtIsNotNull() {
            addCriterion("freeze_at is not null");
            return (Criteria) this;
        }

        public Criteria andFreezeAtEqualTo(Date value) {
            addCriterion("freeze_at =", value, "freezeAt");
            return (Criteria) this;
        }

        public Criteria andFreezeAtNotEqualTo(Date value) {
            addCriterion("freeze_at <>", value, "freezeAt");
            return (Criteria) this;
        }

        public Criteria andFreezeAtGreaterThan(Date value) {
            addCriterion("freeze_at >", value, "freezeAt");
            return (Criteria) this;
        }

        public Criteria andFreezeAtGreaterThanOrEqualTo(Date value) {
            addCriterion("freeze_at >=", value, "freezeAt");
            return (Criteria) this;
        }

        public Criteria andFreezeAtLessThan(Date value) {
            addCriterion("freeze_at <", value, "freezeAt");
            return (Criteria) this;
        }

        public Criteria andFreezeAtLessThanOrEqualTo(Date value) {
            addCriterion("freeze_at <=", value, "freezeAt");
            return (Criteria) this;
        }

        public Criteria andFreezeAtIn(List<Date> values) {
            addCriterion("freeze_at in", values, "freezeAt");
            return (Criteria) this;
        }

        public Criteria andFreezeAtNotIn(List<Date> values) {
            addCriterion("freeze_at not in", values, "freezeAt");
            return (Criteria) this;
        }

        public Criteria andFreezeAtBetween(Date value1, Date value2) {
            addCriterion("freeze_at between", value1, value2, "freezeAt");
            return (Criteria) this;
        }

        public Criteria andFreezeAtNotBetween(Date value1, Date value2) {
            addCriterion("freeze_at not between", value1, value2, "freezeAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}