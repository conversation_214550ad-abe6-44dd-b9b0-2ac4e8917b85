package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.PaymentPlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-6-4
 * @description 付款计划数据传输对象
 */
@Getter
@Setter
@ApiModel(value = "付款计划数据传输对象")
public class PaymentPlanDTO extends PaymentPlan {

    private static final long serialVersionUID = -8952400687358987686L;

    @ApiModelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "关联里程碑名称")
    private String milestoneName;

    @ApiModelProperty(value = "关联里程碑计划完成时间")
    private Date milestoneEndTime;

    @ApiModelProperty(value = "里程碑状态")
    private Integer milestoneStatus;

    @ApiModelProperty(value = "里程碑负责人")
    private Long responsible;

    @ApiModelProperty("责任人名称")
    private String responsibleName;

    @ApiModelProperty("币种")
    private String currency;

    @ApiModelProperty("汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty("汇率时间")
    private Date conversionDate;

    @ApiModelProperty("汇率类型")
    private String conversionType;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "计划付款开始日期")
    private Date startDate;

    @ApiModelProperty(value = "计划付款结束日期")
    private Date endDate;

    @ApiModelProperty(value = "计划付款日期")
    private Date date;

    @ApiModelProperty(value = "计划付款比例")
    private BigDecimal proportion;

    @ApiModelProperty(value = "计划付款比例展示")
    private String proportionDisplay;

    @ApiModelProperty(value = "计划付款金额(含税)")
    private BigDecimal amount;

    @ApiModelProperty(value = "累计罚扣")
    private BigDecimal penalty_amount;

    @ApiModelProperty(value = "采购合同编码")
    private String purchaseContractCode;

    @ApiModelProperty(value = "采购跟进人名称")
    private String purchasingFollowerName;

    @ApiModelProperty(value = "法务合同编号")
    private String legalAffairsCode;

    @ApiModelProperty(value = "付款计划编号")
    private String code;

    @ApiModelProperty(value = "付款条件")
    private String requirement;

    @ApiModelProperty(value = "付款方式")
    private String payment_method_name;

    @ApiModelProperty(value = "采购合同名称")
    private String purchaseContractName;

    @ApiModelProperty(value = "采购合同状态")
    private String purchaseContractStatus;

    @ApiModelProperty(value = "该值不为空默认查合同状态为4,5的数据")
    private String allContractStatus;

    @ApiModelProperty(value = "采购合同状态名称")
    private String purchaseContractStatusName;

    @ApiModelProperty(value = "业务实体ID")
    private Long ouId;

    @ApiModelProperty(value = "业务实体名称")
    private String ouName;

    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商地点ID")
    private String erpVendorSiteId;

    @ApiModelProperty(value = "供应商地点")
    private String vendorSiteCode;

    @ApiModelProperty(value = "项目ID")
    private Long projectId;
    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    @ApiModelProperty(value = "项目状态")
    private Integer projectStatus;

    @ApiModelProperty(value = "累计已经申请金额（含税）")
    private BigDecimal totalApplyAmount;

    @ApiModelProperty(value = "累计在途金额（含税）")
    private BigDecimal totalOnTheWayAmount;

    @ApiModelProperty(value = "累计罚扣款（含税）")
    private BigDecimal totalpenaltyAmount;

    @ApiModelProperty(value = "累计付款金额")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "剩余付款金额")
    private BigDecimal remainingAmount;

    @ApiModelProperty(value = "剩余待付款金额（含税）")
    private BigDecimal surplusAmount;

    @ApiModelProperty(value = "笔数")
    private Integer num;

    private List<Long> ouIds;

    @ApiModelProperty(value = "付款申请单集合")
    private List<PaymentApplyDto> paymentApplyDtoList;

    @ApiModelProperty(value = "多个里程碑状态")
    private List<Integer> milestoneStatuses;

    private String milestoneStatusStr;

    @ApiModelProperty(value = "预算项目号")
    private String itemNumber;

    @ApiModelProperty(value = "预算项目号名称")
    private String itemNumberName;

    @ApiModelProperty(value = "是否有审批中付款申请记录 true 是 false 否 ")
    private boolean alread;

    @ApiModelProperty(value = "采购合同状态集合")
    private List<Integer> contractStatusList;

    private String contractStatusStr;

    @ApiModelProperty(value = "付款计划状态集合")
    private List<Integer> statusList;

    private String statusStr;

    @ApiModelProperty("在途金额")
    private BigDecimal transitAmount;

    @ApiModelProperty("已入账税票金额（不含税）")
    private BigDecimal amountOfTaxReceipts;

    @ApiModelProperty("采购合同进度执行金额（不含税）")
    private BigDecimal purchasingContractProgressAmount;

    @ApiModelProperty(value = "累计申请付款金额（含税）")
    private BigDecimal actualApplyAmount;

    private String billCode;

    private String contractTypeName;

    private Integer contractStatus;

    private BigDecimal totalActualAmount;

    @ApiModelProperty(value = "是否显示外汇付款信息区域")
    private Boolean foreignFlag;

    @ApiModelProperty(value = "供应商地址-国家代码")
    private String territoryCode;

    @ApiModelProperty(value = "供应商地址-国家")
    private String territoryName;

    @ApiModelProperty(value = "供应商地址-详细地址")
    private String fullAddress;

    @ApiModelProperty(value = "银行swiftcode")
    private String swiftCode;

    @ApiModelProperty(value = "已入账应付发票金额（含税）", notes = "采购合同详情-整体情况")
    private BigDecimal entrypayableInvoiceAmount;

    @ApiModelProperty(value = "业务实体ID字符串")
    private String ouIdStr;

    @ApiModelProperty(value = "是否已双签")
    private Boolean isGleSign;

    public String getPurchasingFollowerName() {
        return purchasingFollowerName;
    }

    public void setPurchasingFollowerName(String purchasingFollowerName) {
        this.purchasingFollowerName = purchasingFollowerName;
    }
}
