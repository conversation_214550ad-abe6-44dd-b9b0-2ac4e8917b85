package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.PurchaseContractBudget;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel(value = "采购合同预算")
public class PurchaseContractBudgetDto extends PurchaseContractBudget {

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "物料中类")
    private String codingMiddleclass;

    @ApiModelProperty(value = "物料小类")
    private String materialType;

    @ApiModelProperty(value = "图号/型号")
    private String model;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "图纸版本号")
    private String chartVersion;

    @ApiModelProperty(value = "详细设计单据id")
    private Long receiptsId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectNum;

    @ApiModelProperty(value = "详细设计单据编号")
    private String requirementCode;

    @ApiModelProperty("wbs需求预算占用")
    private BigDecimal wbsDemandTotalCost;

    @ApiModelProperty("wbs需求预算占用（外包）")
    private BigDecimal wbsDemandOsCost;

    @ApiModelProperty("剩余wbs需求预算占用（外包）")
    private BigDecimal wbsRemainingDemandOsCost;

    @ApiModelProperty("累计采购合同占用金额")
    private BigDecimal contractTotalAmount;

    @ApiModelProperty(value = "总需求量")
    private BigDecimal needTotal;

    @ApiModelProperty(value = "未下达量",notes = "未签订采购合同数量")
    private BigDecimal unreleasedAmount;

    @ApiModelProperty(value = "已下达量",notes = "已签订采购合同数量")
    private BigDecimal releasedQuantity;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty("需求预算")
    private BigDecimal demandCost;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "本次进度执行金额（不含税）分配")
    private BigDecimal budgetExecuteAmount;

    @ApiModelProperty(value = "本次进度执行金额（不含税）分配-本位币")
    private BigDecimal localBudgetExecuteAmount;

    @ApiModelProperty(value = "采购合同进度预算关联id")
    private Long purchaseContractProgressBudgetRelId;

    @ApiModelProperty(value = "需求类型")
    private Integer requirementType;

    @ApiModelProperty(value = "页面类型：0-通用页面，1-新页面")
    private Integer webType;

    @ApiModelProperty(value = "合同不含税金额")
    private BigDecimal excludingTaxAmount;

    private String roleName;
    private Date startDate;
    private Date endDate;
    private String contractCode;
    private Integer contractStatus;

    private BigDecimal fillWorkingHour;
    private BigDecimal passWorkingHour;
    private BigDecimal windWorkingHour;
    private String windPercent;

    public void setWbsDemandOsCost(BigDecimal wbsDemandOsCost) {
        this.wbsDemandOsCost = null == wbsDemandOsCost ? null : new BigDecimal(wbsDemandOsCost.stripTrailingZeros().toPlainString());
    }

    public void setWbsRemainingDemandOsCost(BigDecimal wbsRemainingDemandOsCost) {
        this.wbsRemainingDemandOsCost = null == wbsRemainingDemandOsCost ? null : new BigDecimal(wbsRemainingDemandOsCost.stripTrailingZeros().toPlainString());
    }

    public void setContractTotalAmount(BigDecimal contractTotalAmount) {
        this.contractTotalAmount = null == contractTotalAmount ? null :  new BigDecimal(contractTotalAmount.stripTrailingZeros().toPlainString());
    }

    public void setNeedTotal(BigDecimal needTotal) {
        this.needTotal = null == needTotal ? null :  new BigDecimal(needTotal.stripTrailingZeros().toPlainString());
    }

    public void setUnreleasedAmount(BigDecimal unreleasedAmount) {
        this.unreleasedAmount = null == unreleasedAmount ? null : new BigDecimal(unreleasedAmount.stripTrailingZeros().toPlainString());
    }

    public void setReleasedQuantity(BigDecimal releasedQuantity) {
        this.releasedQuantity = null == releasedQuantity ? null : new BigDecimal(releasedQuantity.stripTrailingZeros().toPlainString());
    }

    public void setDemandCost(BigDecimal demandCost) {
        this.demandCost = null == demandCost ? null : new BigDecimal(demandCost.stripTrailingZeros().toPlainString());
    }

    public void setBudgetExecuteAmount(BigDecimal budgetExecuteAmount) {
        this.budgetExecuteAmount = null == budgetExecuteAmount ? null : new BigDecimal(budgetExecuteAmount.stripTrailingZeros().toPlainString());
    }

    public void setLocalBudgetExecuteAmount(BigDecimal localBudgetExecuteAmount) {
        this.localBudgetExecuteAmount = null == localBudgetExecuteAmount ? null : new BigDecimal(localBudgetExecuteAmount.stripTrailingZeros().toPlainString());
    }

    public void setPrice(BigDecimal price) {
        super.setPrice(null == price ? null : new BigDecimal(price.stripTrailingZeros().toPlainString()));
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        super.setTotalPrice(null == totalPrice ? null : new BigDecimal(totalPrice.stripTrailingZeros().toPlainString()));
    }

    public void setLocalTotalPrice(BigDecimal localTotalPrice) {
        super.setLocalTotalPrice(null == localTotalPrice ? null : new BigDecimal(localTotalPrice.stripTrailingZeros().toPlainString()));
    }

    public void setNumber(BigDecimal number) {
        super.setNumber(null == number ? null : new BigDecimal(number.stripTrailingZeros().toPlainString()));
    }

    public void setbudgetExecuteAmountTotal(BigDecimal budgetExecuteAmountTotal) {
        super.setBudgetExecuteAmountTotal(null == budgetExecuteAmountTotal ? null : new BigDecimal(budgetExecuteAmountTotal.stripTrailingZeros().toPlainString()));
    }

}