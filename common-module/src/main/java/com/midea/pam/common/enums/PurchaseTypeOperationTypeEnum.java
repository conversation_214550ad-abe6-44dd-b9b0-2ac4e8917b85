package com.midea.pam.common.enums;

public enum PurchaseTypeOperationTypeEnum {
    WBS(0, "物料采购需求（WBS）"),
    OUTSOURCE(1, "外包（整包）需求 ）"),
    ;

    private int code;
    private String name;

    PurchaseTypeOperationTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
