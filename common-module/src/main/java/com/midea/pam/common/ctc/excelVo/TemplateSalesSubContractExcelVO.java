package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.ctc.entity.ContractProduct;
import com.midea.pam.common.ctc.entity.InvoicePlan;
import com.midea.pam.common.ctc.entity.ReceiptPlan;
import com.midea.pam.common.util.excel4j.ExcelDate2DateConverter;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/9
 */
@Data
public class TemplateSalesSubContractExcelVO {

    @Excel(name = "序号")
    @ExcelField(title = "序号", order = 1)
    private Integer index;

    @Excel(name = "PAM主合同编号")
    @ExcelField(title = "PAM主合同编号", order = 2)
    @NotNull(message = "PAM主合同编号不能为空")
    private String mainCode;

    /**
     * 主合同ID
     */
    @ExcelField(title = "parentId")
    private Long parentId;

    @Excel(name = "子合同编号")
    @ExcelField(title = "子合同编号", order = 3)
    @NotNull(message = "子合同编号不能为空")
    private String code;

    @Excel(name = "子合同名称")
    @ExcelField(title = "子合同名称", order = 4)
    @NotNull(message = "子合同名称不能为空")
    private String name;

    @Excel(name = "子合同业务分类")
    @ExcelField(title = "子合同业务分类", order = 5)
    @NotNull(message = "子合同业务分类不能为空")
    private String profitDepartmentName;

    /**
     * 业务分类ID
     */
    @ExcelField(title = "profitDepartmentId")
    private Long profitDepartmentId;

    @Excel(name = "业务模式")
    @ExcelField(title = "业务模式", order = 6)
    @NotNull(message = "业务模式不能为空")
    private String businessTypeName;

    /**
     * 项目类型ID
     */
    @ExcelField(title = "businessTypeId")
    private Long businessTypeId;

    @Excel(name = "项目经理")
    @ExcelField(title = "项目经理", order = 7)
    @NotNull(message = "项目经理不能为空")
    private String managerMip;

    /**
     * 项目经理MIP
     */
    @ExcelField(title = "manager")
    private Long manager;

    @Excel(name = "子合同生效日期", format = "yyyy/MM/dd")
    @ExcelField(title = "子合同生效日期", order = 8, readConverter = ExcelDate2DateConverter.class)
    @NotNull(message = "子合同生效日期不能为空")
    private Date startTime;

    @Excel(name = "子合同失效日期", format = "yyyy/MM/dd")
    @ExcelField(title = "子合同失效日期", order = 9, readConverter = ExcelDate2DateConverter.class)
    private Date endTime;

    @Excel(name = "客户CRM编码")
    @ExcelField(title = "客户CRM编码", order = 10)
    @NotNull(message = "客户CRM编码不能为空")
    private String customerCode;

    @Excel(name = "开票客户")
    @ExcelField(title = "开票客户", order = 11)
    @NotNull(message = "开票客户不能为空")
    private String customerName;

    @Excel(name = "币种")
    @ExcelField(title = "币种")
    @NotNull(message = "币种不能为空")
    private String currency;
    /**
     * 客户ID
     */
    @ExcelField(title = "customerId")
    private Long customerId;

    @Excel(name = "子合同-总金额")
    @ExcelField(title = "子合同-总金额", order = 12)
    @NotNull(message = "子合同-总金额不能为空")
    private BigDecimal amount;

    @Excel(name = "服务类型")
    @ExcelField(title = "服务类型", order = 13)
    @NotNull(message = "服务类型不能为空")
    private String productTypeName;

    @Excel(name = "服务金额（含税）")
    @ExcelField(title = "服务金额（含税）", order = 14)
    @NotNull(message = "服务金额（含税）不能为空")
    private BigDecimal productAmount;

    @Excel(name = "服务金额（不含税）")
    @ExcelField(title = "服务金额（不含税）", order = 14)
    @NotNull(message = "服务金额（不含税）不能为空")
    private BigDecimal excludingTaxAmount;

    @Excel(name = "税率")
    @ExcelField(title = "税率", order = 15)
    @NotNull(message = "税率不能为空")
    private String taxRate;

    @ExcelField(title = "taxId")
    private Long taxId;

    @ExcelField(title = "taxValue")
    private BigDecimal taxValue;

    @Excel(name = "业务实体")
    @ExcelField(title = "业务实体")
    @NotNull(message = "业务实体不能为空")
    private String ouName;

    @Excel(name = "业务分部")
    @ExcelField(title = "业务分部")
    private String businessSegment;

    @Excel(name = "审批通过日期",format = "yyyy/MM/dd")
    @ExcelField(title = "审批通过日期",readConverter = ExcelDate2DateConverter.class)
    @NotNull(message = "审批通过日期不能为空")
    private Date approvalDate;


    @Excel(name = "校验结果")
    private String validResult;

    /**
     * 子合同ID
     */
    private Long id;

    /**
     * 子合同产品ID
     */
    private Long productId;

    @ExcelField(title = "frameFlag")
    private Boolean frameFlag;

    @ExcelField(title = "ouId")
    private Long ouId;

    @ExcelField(title = "status")
    private Integer status;

    @ExcelField(title = "unitId")
    private Long unitId;


    /**
     * 封装合同产品
     *
     * @return
     */
    public ContractProduct generateContractProduct() {
        ContractProduct contractProduct = new ContractProduct();
        contractProduct.setId(productId);
        contractProduct.setProductTypeName(productTypeName);
        contractProduct.setAmount(productAmount);
        contractProduct.setExcludingTaxAmount(excludingTaxAmount);
        contractProduct.setTaxRate(taxRate);
        contractProduct.setTaxId(taxId);
        contractProduct.setTaxValue(taxValue);
        contractProduct.setDeletedFlag(false);
        return contractProduct;
    }

    public InvoicePlan generateInvoicePlan() {
        InvoicePlan invoicePlan = new InvoicePlan();
        invoicePlan.setProductTypeName(productTypeName);
        invoicePlan.setRate(taxValue);
        invoicePlan.setTaxRate(taxRate);
        invoicePlan.setTaxId(taxId);
        invoicePlan.setAmount(productAmount);
        invoicePlan.setExcludingTaxAmount(excludingTaxAmount);
        invoicePlan.setDeletedFlag(false);
        return invoicePlan;
    }

    public ReceiptPlan generateReceiptPlan() {
        ReceiptPlan receiptPlan = new ReceiptPlan();
        receiptPlan.setAmount(amount);
        receiptPlan.setDeletedFlag(false);
        return receiptPlan;
    }
}
