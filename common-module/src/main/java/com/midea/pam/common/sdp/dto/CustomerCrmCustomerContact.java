package com.midea.pam.common.sdp.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 主题【CUSTOMER_CRM】sdp_crm_customer_contact
 */
@Getter
@Setter
public class CustomerCrmCustomerContact {

    private String contactRowId;
    private String parentRowId;
    private String fullName;
    private String gender;
    private String title;
    private String role;
    private String email;
    private String mip;
    private String phone;
    private String cellphone;
    private String businessUnitId;
    private String socialAccount;
    private String interest;
    private String nationality;
    private String addressId;
    private String prohibition;
    private String religion;
    private String remarks;
    private String card;
    private String standPoint;
    private String contactState;
    private String customerRelationship;
    private String ownerId;
    private String created;
    private Date createDate;
    private String updated;
    private Date updateDate;
    private String conType;
    private String shareFlg;
    private String status; //状态：1-有效 2-失效 3-删除
    private String source;
    private String companyName;
    private String department;
    private String addr;
    private String primaryOrganizationId;
    private String sourceType;
    private String specialty;
    private String bidEvaluationExpertFlag;
}
