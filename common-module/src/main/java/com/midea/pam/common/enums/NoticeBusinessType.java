package com.midea.pam.common.enums;

/**
 * <AUTHOR>
 * @date 2020/3/25
 * @description 通知业务类型
 */
public enum NoticeBusinessType {

    MILEPOST_HANDLE(101, "里程碑待处理"),
    WORKING_REMIND(102, "工时审批待处理"),
    EAMPURCHASE_REMIND(103, "采购申请变更提醒"),
    RESOURCEPLAN_REMIND(104, "资源计划变更提醒"),
    LEAD_REMIND(105, "待跟进线索提醒"),
    PROJECT_TIMEOUT(106, "预立项项目超时未转正"),
    INVOICE_TIMEOUT(107, "超期未开票"),
    RECRIVE_TIMEOUT(108, "超期未回款"),
    PROJECT_MORETHEN(109, "成本超预算"),
    PROJECT_CARRYOVERBILL(110, "项目收入确认后"),
    RECEIPT_CLAIM_CONTRACT(111, "销售回款认领到合同提醒"),
    DELETED_RECEIPT_CLAIM_CONTRACT(112, "销售回款删除合同提醒"),
    REFUND_APPLY(113, "退款提醒"),
    OVER_BUDGET_APPLY(114, "即将超成本提醒"),
    INCOME_CALCULATR_TASK(115, "收入预测任务"),
    PROJECT_TASK(116, "收入预测项目经理任务门户预警"),
    PRODUCT_TASK(117, "收入预测产品总监任务门户预警"),
    WORKING_SUBMIT_REMIND(118, "工时填报待处理"),
    PROJECT_AWARD_PMO(119, "PMO项目奖确认"),
    PROJECT_AWARD_HR(120, "HR项目奖确认"),
    PROJECT_AWARD_DEPARTMENT(121, "事业部总确认"),
    PROJECT_AWARD_FINANC(122, "财务复核中"),
    WORKINGHOUR_BUDGET_REMIND(123, "工时超预算提醒"),
    BUSINESS_REPLAY_REMIND(201, "商机复盘提醒"),
    PROJECT_PROBLEM_REMIND(202, "项目问题提醒"),
    PAYMENT_APPLY_REMIND(203, "付款申请支付成功邮件提醒"),
    PROJECT_REOPEN_REMIND(204, "项目重新打开提醒"),
    PREPAYMENT_REMINDER(205, "预付款提醒"),
    PREPAYMENT_CHANGE_REMINDER(206, "预付款变更提醒"),
    WORKING_REJECT_REMIND(207, "工时驳回邮件提醒"),
    MATERIAL_ADJUST_TODO(208, "物料新增和变更待处理提醒"),
    MATERIAL_ADJUST_SUBMIT_STANDARD(209, "物料新增和变更提交标准化提醒"),
    PROJECT_WBS_RECEIPTS_TODO(210, "详细设计单据待处理提醒"),
    PROJECT_WBS_RECEIPTS_DRAFT(211, "详细设计单据退回提醒"),

    MIP_CALLBACK_LOG_SEND(213, "审批回调日志失败记录自动提醒"),
    MILEPOST_DESIGN_PLAN_NOT_PUBLISH_REQUIREMENT(212, "详设未发布需求提醒"),
    PROJECT_PENDING_CLOSE_REMIND(213, "项目待结项提醒"),

    MATERIAL_DELIST_REMIND(214, "物料退市提醒"),

    PROJECT_WBS_RECEIPTS_PENDING(215, "需求发布后变更详细设计单待处理超2天提醒"),

    PROJECT_WBS_RECEIPTS_APPROVAL(216, "需求发布后变更详细设计单审批中超2天提醒"),

    PURCHASING_FOLLOWER_DOCUMENT_UPLOAD(217, "不同步法务系统采购合同采购跟进人原件上传提醒"),

    BUSINESS_WIN_LOSS(218,"赢丢单提醒"),
    ;

    NoticeBusinessType(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    private Integer type;

    private String name;

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }


    public static NoticeBusinessType getEnumByName(String name) {
        for (NoticeBusinessType svcEnm : values()) {
            if (svcEnm.name.equals(name)) return svcEnm;
        }
        return null;
    }

    public static NoticeBusinessType getEnumByType(Integer type) {
        for (NoticeBusinessType svcEnm : values()) {
            if (svcEnm.type.equals(type)) return svcEnm;
        }
        return null;
    }

}
