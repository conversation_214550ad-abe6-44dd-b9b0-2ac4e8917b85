package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.query.DetailByProjectIdAndMilepostIdNewWbsQuery;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.util.CacheDataUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "MilepostDesignPlanDetailDto", description = "里程碑详细设计方案设计信息")
public class MilepostDesignPlanDetailDto extends MilepostDesignPlanDetail {

    private Long noIncludeId;

    @ApiModelProperty("匹配结果数")
    private Integer numberOfMatchingResults;

    @ApiModelProperty("匹配条件,1:型号/规格/图号 2:名称")
    private Integer matchingCondition;

    @ApiModelProperty(value = "设计信息子层")
    private List<MilepostDesignPlanDetailDto> sonDtos;

    @ApiModelProperty("采购订单下达数量")
    private BigDecimal orderNumSum;

    @ApiModelProperty("需求数量")
    private BigDecimal totalNum;

    @ApiModelProperty("需求数量-前端要求")
    private BigDecimal proDemandTotalNum;

    @ApiModelProperty("已生成采购需求数量")
    private BigDecimal requirementNum;

    @ApiModelProperty("接受/入库数量")
    private BigDecimal warehousingNumSum;

    @ApiModelProperty("领用数量")
    private BigDecimal recipientsNumSum;

    private List<Long> parentIds;

    private List<Long> idList;

    @ApiModelProperty("序号")
    private String serialNumber;

    @ApiModelProperty("设计成本已发生比例")
    private BigDecimal designCostedRatio;

    @ApiModelProperty("提前采购审批状态")
    private Integer purchaseStatus;

    @ApiModelProperty("提前采购数量")
    private BigDecimal purchaseNum;

    @ApiModelProperty("提前采购审批中的数量")
    private BigDecimal approvalNum;

    @ApiModelProperty("设计小计")
    private BigDecimal designSubtotal;

    private List<Long> designPlanDetailIds;

    private List<Integer> statusList;

    @ApiModelProperty("是否已经发布暂存")
    private Boolean orStorage;
    @ApiModelProperty("成本小计")
    private BigDecimal designTotal;

    //工单任务号(该详细设计关联的工单任务号)
    private String ticketTaskCode;

    //货架(取自于基础物料表)
    private String shelves;

    @ApiModelProperty("校验结果")
    private String validResult;

    @ApiModelProperty("剩余领料数量")
    private BigDecimal needNum;

    @ApiModelProperty("物料到货日期")
    private String deliveryTimeStr;

    @ApiModelProperty("已存在的PAM物料编码")
    private String existedPamCode;

    @ApiModelProperty("已存在的ERP物料编码")
    private String existedErpCode;

    private MilepostDesignPlanOldDto milepostDesignPlanOldDto;

    private Long markId;

    private boolean importErpCodeIsNotEmpty = false;

    private boolean importFindCurrentMaterialByErpCode = false;

    private boolean importFindMaterialPamCodeIsNotExist = false;

    @ApiModelProperty("是否子节点")
    private boolean layerMax;

    public boolean isLayerMax() {
        return layerMax;
    }

    public void setLayerMax(boolean layerMax) {
        this.layerMax = layerMax;
    }

    @ApiModelProperty("更新标识")
    private Boolean updateFlag;

    @ApiModelProperty("需求发布单据编号")
    private String requirementCode;

    @ApiModelProperty("单据状态 0-草稿、1-待处理、2-审核中、3-驳回、4-生效、5-作废")
    private Integer requirementStatus;

    @ApiModelProperty("确认方式 1-部分确认、2-进度确认")
    private Integer confirmMode;

    @ApiModelProperty("附件Id，多个逗号分割")
    private String attachs;

    @ApiModelProperty("标识勾选")
    private Boolean isCheck;

    private Boolean change;

    private Boolean isNew;

    @ApiModelProperty("创建人姓名")
    private String createName;

    @ApiModelProperty("更新人项目")
    private String updateName;

    @ApiModelProperty(value = "详设id")
    private Long designPlanDetailId;

    @ApiModelProperty(value = "原id")
    private Long originId;

    @ApiModelProperty(value = "BOOM层级")
    private Integer boomLevel;

    @ApiModelProperty("pam编码")
    private String pamCode;

    @ApiModelProperty("操作类型，详细Excel导入时使用")
    private String batchImportOperationType;
    @ApiModelProperty("WBS路径，详细Excel导入时使用")
    private String wbsPath;
    @ApiModelProperty("层级说明，用于多层级导入场景，格式：正整数.正整数.正整数...")
    private String parentNum;

    @ApiModelProperty("多层级导入时生成的UID")
    private String uid;

    private DetailByProjectIdAndMilepostIdNewWbsQuery detailByProjectIdAndMilepostIdNewWbsQuery;

    @ApiModelProperty(value = "历史变更类型（0历史，1变更项）")
    private Boolean historyType;

    @ApiModelProperty("是否显示标记")
    private Boolean isShowSign;

    /**
     * 操作类型，手动增加还是excel导入,设置为1，专为手动增加提供
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "最大图纸版本号")
    private Long maximumDrawVersionNum;

    @ApiModelProperty(value = "是否急件")
    private String dispatchIsStr;

    @ApiModelProperty(value = "是否外包")
    private String extIsStr;

    @ApiModelProperty(value = "需求类型")
    private String requirementTypeStr;

    @ApiModelProperty(value = "数量")
    private String numberStr;

    @ApiModelProperty(value = "单件重量(Kg)")
    private String unitWeightStr;

    @ApiModelProperty("采购提前期")
    private String perchasingLeadtimeStr;

    @ApiModelProperty("最小采购量")
    private String minPerchaseQuantityStr;

    @ApiModelProperty("最小包装量")
    private String minPackageQuantityStr;

    @ApiModelProperty("库存分类")
    private String inventoryType;

    @ApiModelProperty(value = "物料状态")
    private String itemStatus;

    // 其他组织137补充的数据
    private List<MaterialDto> fillMaterialDtoList;

    @ApiModelProperty("柔性详设发布前端新增数据的new标识")
    private Boolean _isNew;

    //允许增删改标识
    private Boolean editable;

    //是否有已确认的
    private Boolean hasConfirmed;

    private String importMaterialType;

    private List<String> erpCodeList;


    //导出时自动填充数据
    private String requirementTypeExport;

    @ApiModelProperty("设行新增时（可以是详设发布新增、也可以是详设变更新增）提交审批流程的提交时间")
    private Date publishAt;

    @ApiModelProperty("设行新增时（可以是详设发布新增、也可以是详设变更新增）提交审批流程的提交人")
    private Long publishBy;

    private Long formInstanceId;

    private String fdInstanceId;

    private String formUrl;

    private String eventName;

    private String handlerId;

    private Long companyId;

    private Long createUserId;

    private Integer checkStatus;

    private boolean pamCodeImportFlag = false;

    private boolean erpCodeImportFlag = false;

    private boolean notPamCodeAndNotErpCodeImportFlag = false;

    private String projectCode;

    private String projectName;


    private Long projectType;


    private Long unitId;

    /**
     * 项目经理名称
     */
    private String managerName;
    /**
     * 业务分类
     */
    private String unitName;
    /**
     * 项目类型名称.
     */
    private String typeName;

    @ApiModelProperty("父节点WBS编码")
    private String parentWbsSummaryCode;

    @ApiModelProperty("父级物料描述")
    private String parentMaterielDescr;

    @ApiModelProperty("变更前数量")
    private Integer changeOldNumber;

    @ApiModelProperty("发布历史id")
    private Long submitId;

    @ApiModelProperty("发布历史父级id")
    private Long submitParentId;

    /**
     * 项目经理ID
     */
    private Long managerId;

    private Boolean wbsEnabled;

    private Long ouId;// 业务实体


    @ApiModelProperty("采购需求表的requirement_code")
    private String purchaseMaterialRequirementReceiptsCode;
    @Override
    public Integer getRequirementType() {
        if (super.getRequirementType() != null) {
            DictDto dictDto = CacheDataUtils.findDictByTypeAndCode(DictType.DESIGN_REQUIREMENT_TYPE.code(), super.getRequirementType().toString());
            if (dictDto != null) {
                this.setRequirementTypeExport(dictDto.getName());
            }
        }
        return super.getRequirementType();
    }

}