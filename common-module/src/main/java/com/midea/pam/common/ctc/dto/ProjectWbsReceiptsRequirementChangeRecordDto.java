package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsRequirementChangeRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ProjectWbsReceiptsRequirementChangeRecordDto extends ProjectWbsReceiptsRequirementChangeRecord {

    @ApiModelProperty(value = "详细设计单据编号")
    private String requirementCode;

    @ApiModelProperty(value = "详细设计需求预算变更列表")
    List<ProjectWbsReceiptsRequirementChangeRecordRelationDto> relationDtoList;

    @ApiModelProperty(value = "附件列表")
    private List<CtcAttachmentDto> attachmentList;

    @ApiModelProperty(value = "页面类型：0-通用页面，1-新页面")
    private Integer webType = 0;

    private Long projectId;

    private boolean projectWbsReceiptsRequirementIsChange = false;
}
