package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "物料采购需求")
public class PurchaseMaterialRequirement extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "物料编码")
    private String erpCode;

    @ApiModelProperty(value = "PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "交货时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "物料id")
    private Long materielId;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "总需求量")
    private BigDecimal needTotal;

    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "批准供应商数量(erp同步或创建时统计)")
    private Integer approvedSupplierNumber;

    @ApiModelProperty(value = "0：待下达， 1：已下达，2：已关闭")
    private Integer status;

    @ApiModelProperty(value = "关闭原因")
    private String reason;

    @ApiModelProperty(value = "0或空：有效， 1：删除")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "汇率类型")
    private String conversionType;

    @ApiModelProperty(value = "汇率时间")
    private Date conversionDate;

    @ApiModelProperty(value = "汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "采购类型(1默认；2外购物料；3wbs)")
    private Integer purchaseType;

    @ApiModelProperty(value = "详细设计单据id")
    private Long projectWbsReceiptsId;

    @ApiModelProperty(value = "详细设计单据编号")
    private String requirementCode;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "wbs编码（拼接）")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "是否急件(0否 1是)")
    private Boolean dispatchIs;

    @ApiModelProperty(value = "关闭金额")
    private BigDecimal closedAmount;

    @ApiModelProperty(value = "关闭数量")
    private BigDecimal closedQuantity;

    @ApiModelProperty(value = "wbs需求预算占用（外包）")
    private BigDecimal wbsDemandOsCost;

    @ApiModelProperty(value = "图号")
    private String figureNumber;

    @ApiModelProperty(value = "需求发布时间")
    private Date receiptsPublishDate;

    @ApiModelProperty(value = "已下达量")
    private Integer hasAmount;

    @ApiModelProperty(value = "未下达量")
    private Integer dueAmount;

    @ApiModelProperty(value = "图纸版本号")
    private String chartVersion;

    @ApiModelProperty(value = "设计发布批次号")
    private String designReleaseLotNumber;

    @ApiModelProperty(value = "是否为初始化导入: 0-否 1-是")
    private Boolean init;

    @ApiModelProperty(value = "初始化序列")
    private String initSequence;

    @ApiModelProperty(value = "需求类型，具体值参考数据字典design_requirement_type")
    private Integer requirementType;

    @ApiModelProperty(value = "收货地址")
    private String deliveryAddress;

    @ApiModelProperty(value = "收货人")
    private String consignee;

    @ApiModelProperty(value = "联系方式")
    private String contactPhone;

    @ApiModelProperty(value = "冻结标志（0：未冻结，1：冻结）")
    private Integer freezeFlag;

    @ApiModelProperty(value = "冻结原因")
    private String freezeReason;

    @ApiModelProperty(value = "冻结操作人")
    private String freezeByName;

    @ApiModelProperty(value = "冻结操作时间")
    private Date freezeAt;

    private static final long serialVersionUID = 1L;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Long getMaterielId() {
        return materielId;
    }

    public void setMaterielId(Long materielId) {
        this.materielId = materielId;
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    public BigDecimal getNeedTotal() {
        return needTotal;
    }

    public void setNeedTotal(BigDecimal needTotal) {
        this.needTotal = needTotal;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode == null ? null : unitCode.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public Integer getApprovedSupplierNumber() {
        return approvedSupplierNumber;
    }

    public void setApprovedSupplierNumber(Integer approvedSupplierNumber) {
        this.approvedSupplierNumber = approvedSupplierNumber;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getConversionType() {
        return conversionType;
    }

    public void setConversionType(String conversionType) {
        this.conversionType = conversionType == null ? null : conversionType.trim();
    }

    public Date getConversionDate() {
        return conversionDate;
    }

    public void setConversionDate(Date conversionDate) {
        this.conversionDate = conversionDate;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    public Long getProjectWbsReceiptsId() {
        return projectWbsReceiptsId;
    }

    public void setProjectWbsReceiptsId(Long projectWbsReceiptsId) {
        this.projectWbsReceiptsId = projectWbsReceiptsId;
    }

    public String getRequirementCode() {
        return requirementCode;
    }

    public void setRequirementCode(String requirementCode) {
        this.requirementCode = requirementCode == null ? null : requirementCode.trim();
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public Boolean getDispatchIs() {
        return dispatchIs;
    }

    public void setDispatchIs(Boolean dispatchIs) {
        this.dispatchIs = dispatchIs;
    }

    public BigDecimal getClosedAmount() {
        return closedAmount;
    }

    public void setClosedAmount(BigDecimal closedAmount) {
        this.closedAmount = closedAmount;
    }

    public BigDecimal getClosedQuantity() {
        return closedQuantity;
    }

    public void setClosedQuantity(BigDecimal closedQuantity) {
        this.closedQuantity = closedQuantity;
    }

    public BigDecimal getWbsDemandOsCost() {
        return wbsDemandOsCost;
    }

    public void setWbsDemandOsCost(BigDecimal wbsDemandOsCost) {
        this.wbsDemandOsCost = wbsDemandOsCost;
    }

    public String getFigureNumber() {
        return figureNumber;
    }

    public void setFigureNumber(String figureNumber) {
        this.figureNumber = figureNumber == null ? null : figureNumber.trim();
    }

    public Date getReceiptsPublishDate() {
        return receiptsPublishDate;
    }

    public void setReceiptsPublishDate(Date receiptsPublishDate) {
        this.receiptsPublishDate = receiptsPublishDate;
    }

    public Integer getHasAmount() {
        return hasAmount;
    }

    public void setHasAmount(Integer hasAmount) {
        this.hasAmount = hasAmount;
    }

    public Integer getDueAmount() {
        return dueAmount;
    }

    public void setDueAmount(Integer dueAmount) {
        this.dueAmount = dueAmount;
    }

    public String getChartVersion() {
        return chartVersion;
    }

    public void setChartVersion(String chartVersion) {
        this.chartVersion = chartVersion == null ? null : chartVersion.trim();
    }

    public String getDesignReleaseLotNumber() {
        return designReleaseLotNumber;
    }

    public void setDesignReleaseLotNumber(String designReleaseLotNumber) {
        this.designReleaseLotNumber = designReleaseLotNumber == null ? null : designReleaseLotNumber.trim();
    }

    public Boolean getInit() {
        return init;
    }

    public void setInit(Boolean init) {
        this.init = init;
    }

    public String getInitSequence() {
        return initSequence;
    }

    public void setInitSequence(String initSequence) {
        this.initSequence = initSequence == null ? null : initSequence.trim();
    }

    public Integer getRequirementType() {
        return requirementType;
    }

    public void setRequirementType(Integer requirementType) {
        this.requirementType = requirementType;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress == null ? null : deliveryAddress.trim();
    }

    public String getConsignee() {
        return consignee;
    }

    public void setConsignee(String consignee) {
        this.consignee = consignee == null ? null : consignee.trim();
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    public Integer getFreezeFlag() {
        return freezeFlag;
    }

    public void setFreezeFlag(Integer freezeFlag) {
        this.freezeFlag = freezeFlag;
    }

    public String getFreezeReason() {
        return freezeReason;
    }

    public void setFreezeReason(String freezeReason) {
        this.freezeReason = freezeReason == null ? null : freezeReason.trim();
    }

    public String getFreezeByName() {
        return freezeByName;
    }

    public void setFreezeByName(String freezeByName) {
        this.freezeByName = freezeByName == null ? null : freezeByName.trim();
    }

    public Date getFreezeAt() {
        return freezeAt;
    }

    public void setFreezeAt(Date freezeAt) {
        this.freezeAt = freezeAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectId=").append(projectId);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", materielId=").append(materielId);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", needTotal=").append(needTotal);
        sb.append(", unitCode=").append(unitCode);
        sb.append(", unit=").append(unit);
        sb.append(", approvedSupplierNumber=").append(approvedSupplierNumber);
        sb.append(", status=").append(status);
        sb.append(", reason=").append(reason);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", currency=").append(currency);
        sb.append(", conversionType=").append(conversionType);
        sb.append(", conversionDate=").append(conversionDate);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", purchaseType=").append(purchaseType);
        sb.append(", projectWbsReceiptsId=").append(projectWbsReceiptsId);
        sb.append(", requirementCode=").append(requirementCode);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", dispatchIs=").append(dispatchIs);
        sb.append(", closedAmount=").append(closedAmount);
        sb.append(", closedQuantity=").append(closedQuantity);
        sb.append(", wbsDemandOsCost=").append(wbsDemandOsCost);
        sb.append(", figureNumber=").append(figureNumber);
        sb.append(", receiptsPublishDate=").append(receiptsPublishDate);
        sb.append(", hasAmount=").append(hasAmount);
        sb.append(", dueAmount=").append(dueAmount);
        sb.append(", chartVersion=").append(chartVersion);
        sb.append(", designReleaseLotNumber=").append(designReleaseLotNumber);
        sb.append(", init=").append(init);
        sb.append(", initSequence=").append(initSequence);
        sb.append(", requirementType=").append(requirementType);
        sb.append(", deliveryAddress=").append(deliveryAddress);
        sb.append(", consignee=").append(consignee);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", freezeFlag=").append(freezeFlag);
        sb.append(", freezeReason=").append(freezeReason);
        sb.append(", freezeByName=").append(freezeByName);
        sb.append(", freezeAt=").append(freezeAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}