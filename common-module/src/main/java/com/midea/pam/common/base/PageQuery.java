package com.midea.pam.common.base;

import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Created by BAIBG1 on 2017-4-13.
 */
public class PageQuery<T extends PageQuery> implements Serializable {
    
    private static final long serialVersionUID = 7083766499673323749L;
    
    /**
     * 默认起始页
     */
    public static final int DEFAULT_PAGE_NUM = 1;
    /**
     * 默认分页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 99;
    /**
     * 防止误传入过大分页数 影响性能
     */
    private static final int MAX_PAGE_SIZE = 1000;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date updateBeginDate;
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date updateEndDate;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date createBeginDate;
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date createEndDate;

    /**
     * 当前页
     */
    private Integer pageNum;
    /**
     * 每页的数量
     */
    private Integer pageSize;

    /**
     * 请求路径
     */
    private String requestUrl;

    public Integer getPageNum() {
        if (pageNum == null)
            pageNum = DEFAULT_PAGE_NUM;
//        else if (pageNum > MAX_PAGE_SIZE)
//            return MAX_PAGE_SIZE;
        return pageNum;
    }

    public T setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
        return (T) this;
    }

    public Integer getPageSize() {
        if (pageSize == null)
            pageSize = DEFAULT_PAGE_SIZE;
        return pageSize;
    }


    public T setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
        return (T) this;
    }

    public Date getUpdateBeginDate() {
        return updateBeginDate;
    }

    public void setUpdateBeginDate(Date updateBeginDate) {
        this.updateBeginDate = updateBeginDate;
    }

    public Date getUpdateEndDate() {
        return updateEndDate;
    }

    public void setUpdateEndDate(Date updateEndDate) {
        this.updateEndDate = updateEndDate;
    }

    public Date getCreateBeginDate() {
        return createBeginDate;
    }

    public void setCreateBeginDate(Date createBeginDate) {
        this.createBeginDate = createBeginDate;
    }

    public Date getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(Date createEndDate) {
        this.createEndDate = createEndDate;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public T setRequestUrl(String requestUrl) {
        if(null != requestUrl && requestUrl.length()>0) {
            int i = requestUrl.indexOf("?");
            if (i > -1) {
                StringBuffer paramstr = new StringBuffer();
                List<String> params = Arrays.asList(requestUrl.substring(i + 1).split("&"));
                for (Iterator<String> iterator = params.iterator(); iterator.hasNext(); ) {
                    String param = iterator.next();
                    if (!param.startsWith("pageNum"))
                        paramstr.append("&").append(param);
                }
                if (paramstr.length() > 0)
                    paramstr.deleteCharAt(0);

                requestUrl = requestUrl.substring(0, i + 1) + paramstr.toString();
            }
            this.requestUrl = requestUrl;
        }
        return (T) this;
    }
}
