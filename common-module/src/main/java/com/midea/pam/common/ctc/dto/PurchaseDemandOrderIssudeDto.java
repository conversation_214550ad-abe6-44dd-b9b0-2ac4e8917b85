package com.midea.pam.common.ctc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Objects;

@Getter
@Setter
public class PurchaseDemandOrderIssudeDto {

    //"业务实体+是否急单+供应商编码+供应商地点+币种"维度分组生成采购订单

    private String id;
    private Integer approvedSupplierNumber;
    private Integer belongArea;
    private Integer pricingType;
    private BigDecimal price;
    private Integer closedAmount;
    private Integer closedQuantity;
    private Integer contractTotalAmount;
    private Long erpVendorId;
    private String createAt;
    private String createBy;
    private String currencyCode;
    private Boolean deletedFlag;
    private String deliveryTime;
    private Integer demandCost;
    private Boolean dispatchIs;      //是否急单
    private Long projectOuId; //业务实体id
   // private String endDateActive: undefined
    private String  erpCode;  //erp编码
    private Integer erpCodeIs;  //是否同步erp
    private String  erpVendorSiteId;  //供应商地点id
    private String materielDescr;
    private String materielId;
    private Integer needTotal;
    private Integer orderNum;
    private String pamCode;
    private String projectId;
    private String projectName;
    private String projectNum;
    private String projectOuName;
    private String projectWbsReceiptsId;
    private String publishTime;
    private Integer purchaseType;
    private String pvsStatus;
    private String receiptsId;
    private Integer releasedQuantity;
    private String requirementCode;
    private String requirementId;
    private Integer status;
    private String unit;
    private String unitCode;
    private Integer unreleasedAmount;
    private String updateBy;
    private String vendorCode;
    private String vendorId;
    private String vendorName;
    private String vendorSiteCode;
    private Integer wbsDemandOsCost;
    private Integer wbsRemainingDemandOsCost;
    private String wbsSummaryCode;

    private String model;
    private Long materialId;
    private String codingMiddleclass;
    private String materialType;
    private String brand;
    private String chartVersion;
    private String designReleaseLotNumber;

    //剩余需求预算占用金额（不含税）
    private String remainMoney;
    //预算占用金额
    private BigDecimal budgetOccupiedAmount;
    private BigDecimal budgetOccupiedAmountTotal;

    private Long materialPurchaseRequirementId;

    private String activityCode;

    private Long orgId;

    //合并条件
    private String data;

    private String figureNumber;

    private Long buyerId;

    private Integer wbsSummaryCodeStar;
    private Integer requirementCodeStar;
    private Integer designReleaseLotNumberStar;

    /**
     * 接收子库
     */
    private String secondaryInventory;

    /**
     * 接收子库代码
     */
    private String secondaryInventoryName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PurchaseDemandOrderIssudeDto that = (PurchaseDemandOrderIssudeDto) o;
        return  Objects.equals(data, that.data);
    }

    @Override
    public int hashCode() {
        return Objects.hash(data);
    }
}
