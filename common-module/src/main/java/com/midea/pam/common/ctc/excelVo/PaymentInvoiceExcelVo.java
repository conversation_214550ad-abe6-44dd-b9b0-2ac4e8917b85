package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetail;
import com.midea.pam.common.enums.PaymentInvoiceErpStatusEnum;
import com.midea.pam.common.enums.PaymentInvoiceSourceEnum;
import com.midea.pam.common.util.Builder;
import com.midea.pam.common.util.excel4j.ExcelDate2DateConverter;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Optional;

@Data
public class PaymentInvoiceExcelVo {

    @ExcelField(title = "序号", order = 1)
    @Excel(name = "序号")
    private Integer serialNumber;

    @ExcelField(title = "付款计划编号", order = 2)
    @Excel(name = "付款计划编号")
    private String paymentPlanCode;

    private Long paymentPlanId;

    @ExcelField(title = "PAM合同号-新", order = 4)
    @Excel(name = "PAM合同号-新")
    @NotNull(message = "PAM合同号-新不能为空")
    private String purchaseContractCode;  //还有个法务合同号,发票表中没有体现

    private Long purchaseContractId;
    private String purchaseContractName;

    @ExcelField(title = "项目号-新", order = 6)
    @Excel(name = "项目号-新")
    @NotNull(message = "项目号-新不能为空")
    private String projectCode;

    private Long projectId;
    private String projectName;

    @ExcelField(title = "供应商编码-新", order = 7)
    @Excel(name = "供应商编码-新")
    @NotNull(message = "供应商编码-新不能为空")
    private String vendorCode;

    @ExcelField(title = "供应商名称-新", order = 8)
    @Excel(name = "供应商名称-新")
    @NotNull(message = "供应商名称-新不能为空")
    private String vendorName;

    @ExcelField(title = "供应商地点-新", order = 9)
    @Excel(name = "供应商地点-新")
    @NotNull(message = "供应商地点-新不能为空")
    private String vendorSiteCode;

    private Long vendorId;

    @ExcelField(title = "业务实体", order = 10)
    @Excel(name = "业务实体")
    @NotNull(message = "业务实体不能为空")
    private String ouIdName;  //operating_unit 表的id

    private Long ouId;

    @ExcelField(title = "发票状态", order = 11)
    @Excel(name = "发票状态")
    @NotNull(message = "发票状态不能为空")
    private String erpStatu;  //PaymentInvoiceErpStatusEnum

    @ExcelField(title = "发票类型", order = 12)
    @Excel(name = "发票类型")
    @NotNull(message = "发票类型不能为空")
    private String invoiceType;

    @ExcelField(title = "发票号", order = 13)
    @Excel(name = "发票号")
    @NotNull(message = "发票号不能为空")
    private String apInvoiceCode; //ap_invoice_code ap发票编号

    @ExcelField(title = "币种", order = 14)
    @Excel(name = "币种")
    @NotNull(message = "币种不能为空")
    private String currency;

    @ExcelField(title = "发票金额（含税）", order = 15)
    @Excel(name = "发票金额（含税）")
    @NotNull(message = "发票金额（含税）不能为空")
    private BigDecimal totalInvoiceIncludedPrice;

    @ExcelField(title = "税率", order = 16)
    @Excel(name = "税率")
    @NotNull(message = "税率不能为空")
    private BigDecimal taxRate;

    @ExcelField(title = "已付金额", order = 17)
    @Excel(name = "已付金额")
    @NotNull(message = "已付金额不能为空")
    private BigDecimal totalPayIncludedPrice;

    @ExcelField(title = "发票日期", readConverter = ExcelDate2DateConverter.class)
    @Excel(name = "发票日期")
    @NotNull(message = "发票日期不能为空")
    private Date invoiceDate;

    @ExcelField(title = "GL日期", readConverter = ExcelDate2DateConverter.class)
    @Excel(name = "GL日期")
    @NotNull(message = "GL日期不能为空")
    private Date auditDate;

    @ExcelField(title = "erp发票ID")
    @Excel(name = "erp发票ID")
    private String erpInvoiceCode;

    @ExcelField(title = "摘要", order = 20)
    @Excel(name = "摘要")
    private String description;

    @Excel(name = "错误信息")
    private String errMsg;

    private Long invoiceId;
    private Long invoiceDetailId;

    public PaymentInvoice buildHeader() {
        return Builder.of(PaymentInvoice::new)
                .with(PaymentInvoice::setId, invoiceId)
                .with(PaymentInvoice::setPaymentPlanCode, paymentPlanCode)
                .with(PaymentInvoice::setPaymentPlanId, paymentPlanId)
                .with(PaymentInvoice::setPurchaseContractCode, purchaseContractCode)
                .with(PaymentInvoice::setPurchaseContractId, purchaseContractId)
                .with(PaymentInvoice::setOuId, ouId)
                .with(PaymentInvoice::setApInvoiceCode, apInvoiceCode)
                .with(PaymentInvoice::setInvoiceDate, invoiceDate)
                .with(PaymentInvoice::setAuditDate, auditDate)
                .with(PaymentInvoice::setAmount, BigDecimal.ZERO)
                .with(PaymentInvoice::setTotalAmount, totalInvoiceIncludedPrice)
                .with(PaymentInvoice::setErpInvoiceCode, erpInvoiceCode)
                .with(PaymentInvoice::setTotalInvoiceIncludedPrice, totalInvoiceIncludedPrice)
                .with(PaymentInvoice::setTotalPayIncludedPrice, totalPayIncludedPrice)
                .with(PaymentInvoice::setSurplusAmount, totalInvoiceIncludedPrice.subtract(totalPayIncludedPrice))
                .with(PaymentInvoice::setTaxAmount, getTaxAmount(totalInvoiceIncludedPrice, this.getTaxRate()))
                .with(PaymentInvoice::setErpStatus, PaymentInvoiceErpStatusEnum.SUCCESS.code())
                .with(PaymentInvoice::setSource, PaymentInvoiceSourceEnum.PAYMENT.getCode())
                .with(PaymentInvoice::setDeletedFlag, false)
                .with(PaymentInvoice::setStatus, 2)
                .with(PaymentInvoice::setAvailable, 1)
                .with(PaymentInvoice::setAccountEntryType, 1)
                .with(PaymentInvoice::setErpCancelStatus, 0)
                .with(PaymentInvoice::setErpCancelStatus, 0)
                .build();
    }


    public PaymentInvoiceDetail buildDetail(PaymentInvoice paymentInvoice) {
        return Builder.of(PaymentInvoiceDetail::new)
                .with(PaymentInvoiceDetail::setId, invoiceDetailId)
                .with(PaymentInvoiceDetail::setPaymentInvoiceId, paymentInvoice.getId())
                .with(PaymentInvoiceDetail::setInvoiceType, "0")
                .with(PaymentInvoiceDetail::setPurchaseContractId, purchaseContractId)
                .with(PaymentInvoiceDetail::setPurchaseContractCode, purchaseContractCode)
                .with(PaymentInvoiceDetail::setPurchaseContractName, purchaseContractName)
                .with(PaymentInvoiceDetail::setOuId, ouId)
                .with(PaymentInvoiceDetail::setVendorId, vendorId)
                .with(PaymentInvoiceDetail::setVendorCode, vendorCode)
                .with(PaymentInvoiceDetail::setVendorName, vendorName)
                .with(PaymentInvoiceDetail::setVendorSiteCode, vendorSiteCode)
                .with(PaymentInvoiceDetail::setProjectId, projectId)
                .with(PaymentInvoiceDetail::setProjectCode, projectCode)
                .with(PaymentInvoiceDetail::setProjectName, projectName)
                .with(PaymentInvoiceDetail::setCurrency, currency)
                .with(PaymentInvoiceDetail::setTaxIncludedPrice, totalInvoiceIncludedPrice)
                .with(PaymentInvoiceDetail::setInvoiceDate, invoiceDate)
                .with(PaymentInvoiceDetail::setLineNum, 1)
                .with(PaymentInvoiceDetail::setInvoiceDetailCode, apInvoiceCode)
                .with(PaymentInvoiceDetail::setTaxRate, this.getTaxRate().multiply(new BigDecimal(100)))
                .with(PaymentInvoiceDetail::setTaxAmount, getTaxAmount(totalInvoiceIncludedPrice, this.getTaxRate()))
                .with(PaymentInvoiceDetail::setTaxExcludedPrice, getTaxExcludedPrice(totalInvoiceIncludedPrice, this.getTaxRate()))
                .with(PaymentInvoiceDetail::setDeletedFlag, false)
                .with(PaymentInvoiceDetail::setAttribute, "INV001")
                .with(PaymentInvoiceDetail::setInvoiceStatus, "QUOTE")
                .with(PaymentInvoiceDetail::setRemark, description)
                .build();
    }

    //入参 含税totalInvoiceIncludedPrice 税率taxRate，返回值 税额=含税-含税/（1+税率） 税额四舍五入保留两位小数
    private BigDecimal getTaxAmount(BigDecimal totalInvoiceIncludedPrice, BigDecimal taxRate) {
        return Optional.ofNullable(totalInvoiceIncludedPrice).orElse(BigDecimal.ZERO)
                .subtract(getTaxExcludedPrice(totalInvoiceIncludedPrice, taxRate)).setScale(2, RoundingMode.HALF_UP);
    }

    //入参 含税totalInvoiceIncludedPrice 税率taxRate，返回值 不含税=含税/（1+税率） 不含税四舍五入保留两位小数
    private BigDecimal getTaxExcludedPrice(BigDecimal totalInvoiceIncludedPrice, BigDecimal taxRate) {
        if (totalInvoiceIncludedPrice == null || taxRate == null || taxRate.compareTo(BigDecimal.ZERO) == 0) {
            return Optional.ofNullable(totalInvoiceIncludedPrice).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);
        }
        return totalInvoiceIncludedPrice.divide(BigDecimal.ONE.add(taxRate), 2, RoundingMode.HALF_UP);
    }
}