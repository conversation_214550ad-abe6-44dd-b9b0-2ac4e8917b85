package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.util.CacheDataUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ProjectHistoryHeaderDto extends ProjectHistoryHeader {

    @ApiModelProperty(value = "创建人名称")
    private String createByName;

    private Long batch;

    @ApiModelProperty(value = "ems预算同步状态", notes = "0:待同步，1:同步异常，2:同步成功")
    private String emsPushStatus;

    @ApiModelProperty("ems预算同步时间")
    private Date emsPushDate;

    @ApiModelProperty("ems预算同步消息")
    private String emsPushMsg;

    @ApiModelProperty("项目流程说明")
    private String projectNameDes;

    @ApiModelProperty("当前基线")
    private Boolean currentBaseline = false;

    @ApiModelProperty("提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "项目编号")
    private String code;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "创建人MIP")
    private String createByMip;

    @Override
    public void setCreateBy(Long createBy) {
        super.setCreateBy(createBy);
        if (createBy != null) {
            UserInfo userInfo = CacheDataUtils.findUserById(createBy);
            if (userInfo != null) {
                setCreateByName(userInfo.getName());
            }
        }
    }
}
