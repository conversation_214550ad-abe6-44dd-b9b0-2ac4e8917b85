package com.midea.pam.common.ctc.excelVo;

import com.github.crab2died.annotation.ExcelField;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * 物料新增导入ImportExcelVo
 * <AUTHOR>
 * @date 2021/3/1
 */
@Getter
@Setter
public class MaterialAdjustDetailImportExcelVo {

    @ExcelField(title = "序号", order = 1 )
    private Long index;

    @ExcelField(title = "物料类型", order = 2)
    private String materialType;

    @Excel(name = "物料大类")
    @ExcelField(title = "物料大类", order = 3)
    private String materialClass;

    @ExcelField(title = "物料中类", order = 4)
    private String materialMiddleClass;

    @ExcelField(title = "物料小类", order = 5)
    private String materialSmallClass;

    @ExcelField(title = "品牌", order = 6)
    private String brand;

    @ExcelField(title = "名称", order = 7)
    private String name;

    @ExcelField(title = "型号/规格", order = 8)
    private String model;

    @ExcelField(title = "单位", order = 9)
    private String unit;

    @ExcelField(title = "图号", order = 10)
    private String figureNumber;

    @ExcelField(title = "图纸版本号", order = 11)
    private String chartVersion;

    @ExcelField(title = "加工分类", order = 12)
    private String machiningPartType;

    @ExcelField(title = "材质", order = 13)
    private String material;

    @ExcelField(title = "单件重量(Kg)", order = 14)
    private BigDecimal unitWeight;

    @ExcelField(title = "表面处理", order = 15)
    private String surfaceHandle;

    @ExcelField(title = "品牌商物料编码", order = 16)
    private String brandMaterialCode;

    @ExcelField(title = "备件标识", order = 17)
    private String orSparePartsMask;

    @ExcelField(title = "采购提前期", order = 18)
    private Long perchasingLeadtime;

    @ExcelField(title = "最小采购量", order = 19)
    private Long minPerchaseQuantity;

    @ExcelField(title = "最小包装量", order = 20)
    private Long minPackageQuantity;

    @ExcelField(title = "备注", order = 21)
    private String remark;

    public Long getIndex() {
        return index;
    }

    public void setIndex(Long index) {
        this.index = index;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public String getMaterialMiddleClass() {
        return materialMiddleClass;
    }

    public void setMaterialMiddleClass(String materialMiddleClass) {
        this.materialMiddleClass = materialMiddleClass;
    }

    public String getMaterialSmallClass() {
        return materialSmallClass;
    }

    public void setMaterialSmallClass(String materialSmallClass) {
        this.materialSmallClass = materialSmallClass;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getFigureNumber() {
        return figureNumber;
    }

    public void setFigureNumber(String figureNumber) {
        this.figureNumber = figureNumber;
    }

    public String getChartVersion() {
        return chartVersion;
    }

    public void setChartVersion(String chartVersion) {
        this.chartVersion = chartVersion;
    }

    public String getMachiningPartType() {
        return machiningPartType;
    }

    public void setMachiningPartType(String machiningPartType) {
        this.machiningPartType = machiningPartType;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public BigDecimal getUnitWeight() {
        return unitWeight;
    }

    public void setUnitWeight(BigDecimal unitWeight) {
        this.unitWeight = unitWeight;
    }

    public String getSurfaceHandle() {
        return surfaceHandle;
    }

    public void setSurfaceHandle(String surfaceHandle) {
        this.surfaceHandle = surfaceHandle;
    }

    public String getBrandMaterialCode() {
        return brandMaterialCode;
    }

    public void setBrandMaterialCode(String brandMaterialCode) {
        this.brandMaterialCode = brandMaterialCode;
    }

    public String getOrSparePartsMask() {
        return orSparePartsMask;
    }

    public void setOrSparePartsMask(String orSparePartsMask) {
        this.orSparePartsMask = orSparePartsMask;
    }

    public Long getPerchasingLeadtime() {
        return perchasingLeadtime;
    }

    public void setPerchasingLeadtime(Long perchasingLeadtime) {
        this.perchasingLeadtime = perchasingLeadtime;
    }

    public Long getMinPerchaseQuantity() {
        return minPerchaseQuantity;
    }

    public void setMinPerchaseQuantity(Long minPerchaseQuantity) {
        this.minPerchaseQuantity = minPerchaseQuantity;
    }

    public Long getMinPackageQuantity() {
        return minPackageQuantity;
    }

    public void setMinPackageQuantity(Long minPackageQuantity) {
        this.minPackageQuantity = minPackageQuantity;
    }
}
