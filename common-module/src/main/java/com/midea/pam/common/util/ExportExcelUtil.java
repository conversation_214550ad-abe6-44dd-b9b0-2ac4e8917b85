package com.midea.pam.common.util;

import com.midea.pam.common.ctc.vo.ProjectCheckExcelVo;
import com.midea.pam.common.ctc.vo.ProjectCheckReturnVo;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.export.ExcelExportServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExportExcelUtil {

    private static Logger logger = LoggerFactory.getLogger(ExportExcelUtil.class);

    public static void exportExcel(List<?> list, String title, String sheetName, Class<?> pojoClass, String fileName, boolean isCreateHeader,
                                   HttpServletResponse response) {
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setCreateHeadRows(isCreateHeader);
        defaultExport(list, pojoClass, fileName, response, exportParams);
    }

    public static void exportExcel(List<?> list, String title, String sheetName, Class<?> pojoClass, String fileName, HttpServletResponse response) {
        defaultExport(list, pojoClass, fileName, response, new ExportParams(title, sheetName));
    }

    private static void defaultExport(List<?> list, Class<?> pojoClass, String fileName, HttpServletResponse response, ExportParams exportParams) {
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);

        if (workbook != null) ;
        downLoadExcel(fileName, response, workbook);
    }

    public static void exportExcel(List<?> list, String title, String sheetName, Class<?> pojoClass, boolean isCreateHeader) {
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setCreateHeadRows(isCreateHeader);
        defaultExport(list, pojoClass, exportParams);
    }

    public static Workbook defaultExport(List<?> list, Class<?> pojoClass, ExportParams exportParams) {
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        return workbook;
    }

    /**
     * 构建默认sheet
     *
     * @param list
     * @param pojoClass
     * @param title
     * @param sheetName
     * @param isCreateHeader
     */
    public static Workbook buildDefaultSheet(List<?> list, Class<?> pojoClass, String title, String sheetName, boolean isCreateHeader) {
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setCreateHeadRows(isCreateHeader);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        return workbook;
    }

    /**
     * 新增sheet页
     *
     * @param workbook
     * @param list
     * @param pojoClass
     * @param title
     * @param sheetName
     * @param isCreateHeader
     */
    public static void addSheet(Workbook workbook, List<?> list, Class<?> pojoClass, String title, String sheetName, boolean isCreateHeader) {
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setCreateHeadRows(isCreateHeader);

        addSheet((Workbook) workbook, list, pojoClass, exportParams);
    }

    /**
     * 新增sheet页 支持 xls与xlsx
     *
     * @param workbook
     * @param dataList
     * @param clazz
     * @param sheetName
     */
    public static void addCommonSheet(Workbook workbook, List<?> dataList, Class<?> clazz, String sheetName) {
        Sheet sheet = workbook.createSheet(sheetName);
        // 设置表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        headerStyle.setFont(font);

        // 创建表头行
        Row headerRow = sheet.createRow(0);
        Map<String, Integer> fieldColumnMap = new HashMap<>();
        // 设置表头数据
        List<String> headers = getHeadersFromAnnotations(clazz, sheet);
        for (int i = 0; i < headers.size(); i++) {
            String header = headers.get(i);
            Cell headerCell = headerRow.createCell(i);
            headerCell.setCellValue(header);
            fieldColumnMap.put(header, i);
        }
        // 设置数据行
        for (int i = 0; i < dataList.size(); i++) {
            Row dataRow = sheet.createRow(i + 1);
            Object data = dataList.get(i);
            // 设置数据列
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                Excel excelAnnotation = field.getAnnotation(Excel.class);
                if (excelAnnotation != null) {
                    String fieldName = excelAnnotation.name();
                    int columnIndex = fieldColumnMap.get(fieldName);
                    field.setAccessible(true);
                    Cell dataCell = dataRow.createCell(columnIndex);
                    try {
                        Object value = field.get(data);
                        dataCell.setCellValue(value != null ? value.toString() : "");
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    private static List<String> getHeadersFromAnnotations(Class<?> clazz, Sheet sheet) {
        List<String> headers = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            Excel excelAnnotation = field.getAnnotation(Excel.class);
            if (excelAnnotation != null) {
                headers.add(excelAnnotation.name());
                double width = excelAnnotation.width();
                sheet.setColumnWidth(headers.size() - 1, (int) Math.round(width * 256));
            }
        }
        return headers;
    }

    /**
     * 新增sheet页
     *
     * @param workbook
     * @param list
     * @param pojoClass
     * @param exportParams
     */
    public static void addSheet(Workbook workbook, List<?> list, Class<?> pojoClass, ExportParams exportParams) {
        (new ExcelExportServer()).createSheet((Workbook) workbook, exportParams, pojoClass, list);
    }

    public static HSSFRow createCell(HSSFSheet sheet, String value, int rowNum, int columnIndex, HSSFCellStyle cellRightStyle) {
        HSSFRow row = sheet.createRow(rowNum);
        createCell(row, value, columnIndex, cellRightStyle);
        return row;
    }

    public static HSSFCell createCell(HSSFRow row, String value, int columnIndex, HSSFCellStyle cellRightStyle) {
        // 在索引0的位置创建单元格（左上端）
        HSSFCell cell = row.createCell(columnIndex);
        // 定义单元格为字符串类型
        cell.setCellType(XSSFCell.CELL_TYPE_STRING);
        cell.setCellStyle(cellRightStyle);
        cell.setCellValue(value);

        return cell;
    }

    public static HSSFCell createCell(HSSFRow row, String value, int columnIndex) {
        // 在索引0的位置创建单元格（左上端）
        HSSFCell cell = row.createCell(columnIndex);
        // 定义单元格为字符串类型
        cell.setCellType(XSSFCell.CELL_TYPE_STRING);
        cell.setCellValue(value);

        return cell;
    }

    public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            logger.error("导出失败：" + e.getMessage(), e);
            throw new RuntimeException("导出失败");
        }
    }

    public static void downLoadExcel(String fileName, HttpServletResponse response, SXSSFWorkbook workbook) {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 创建输出流
            OutputStream outputStream = response.getOutputStream();
            // 写入响应输出流
            workbook.write(outputStream);

            // 关闭workbook和输出流
            workbook.dispose(); // 释放SXSSFWorkbook占用的内存
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            logger.error("导出失败：" + e.getMessage(), e);
            throw new RuntimeException("导出失败");
        }
    }

    /**
     * 结项检查
     *
     * @param workbook
     * @param sheetNum            sheet的位置，0表示第一个表格中的第一个sheet
     * @param projectCheckExcelVo
     * @param out                 输出流
     * @throws Exception
     */
    public void exportExcel(HSSFWorkbook workbook, int sheetNum, ProjectCheckExcelVo projectCheckExcelVo,
                            OutputStream out) throws Exception {

        String[] headers = projectCheckExcelVo.getHeaders();
        List<ProjectCheckReturnVo> datas = projectCheckExcelVo.getReturnList();
        if (headers == null || datas == null) {
            return;
        }
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet();
        workbook.setSheetName(sheetNum, String.valueOf(sheetNum));
        // 设置表格默认列宽度为20个字节
        sheet.setDefaultColumnWidth((short) 20);
        // 生成一个样式
        HSSFCellStyle style = workbook.createCellStyle();
        // 设置这些样式

        // 指定当单元格内容显示不下时自动换行
        style.setWrapText(true);
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 产生表格标题行
        for (int i = 0; i < 4; i++) {
            HSSFRow row = sheet.createRow(i);
            if (i == 0) {
                HSSFCell cell0 = row.createCell((short) 0);
                cell0.setCellStyle(style);
                HSSFRichTextString text0 = new HSSFRichTextString("输出时间");
                cell0.setCellValue(text0.toString());
                HSSFCell cell1 = row.createCell((short) 1);
                cell1.setCellStyle(style);
                HSSFRichTextString text1 = new HSSFRichTextString(df.format(projectCheckExcelVo.getDate()));
                cell1.setCellValue(text1.toString());
            } else if (i == 1) {
                HSSFCell cell0 = row.createCell((short) 0);
                cell0.setCellStyle(style);
                HSSFRichTextString text0 = new HSSFRichTextString("项目编号");
                cell0.setCellValue(text0.toString());
                HSSFCell cell1 = row.createCell((short) 1);
                cell1.setCellStyle(style);
                HSSFRichTextString text1 = new HSSFRichTextString(projectCheckExcelVo.getProjectCode());
                cell1.setCellValue(text1.toString());
            } else if (i == 2) {
                HSSFCell cell0 = row.createCell((short) 0);
                cell0.setCellStyle(style);
                HSSFRichTextString text0 = new HSSFRichTextString("项目名称");
                cell0.setCellValue(text0.toString());
                HSSFCell cell1 = row.createCell((short) 1);
                cell1.setCellStyle(style);
                HSSFRichTextString text1 = new HSSFRichTextString(projectCheckExcelVo.getProjectName());
                cell1.setCellValue(text1.toString());
            } else if (i == 3) {
                HSSFCell cell0 = row.createCell((short) 0);
                cell0.setCellStyle(style);
                HSSFRichTextString text0 = new HSSFRichTextString("检查点");
                cell0.setCellValue(text0.toString());
                HSSFCell cell1 = row.createCell((short) 1);
                cell1.setCellStyle(style);
                HSSFRichTextString text1 = new HSSFRichTextString(projectCheckExcelVo.getCheckName());
                cell1.setCellValue(text1.toString());
            }
        }

        HSSFRow row5 = sheet.createRow(5);
        for (int i = 0; i < headers.length; i++) {
            HSSFCell cell = row5.createCell((short) i);
            cell.setCellStyle(style);
            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
            cell.setCellValue(text.toString());
        }

        // 遍历集合数据，产生数据行
        int index = 6;
        for (ProjectCheckReturnVo o : datas) {
            HSSFRow row = sheet.createRow(index);
            for (int i = 0; i < headers.length; i++) {
                Method method = o.getClass().getMethod("getC" + i);//得到方法对象
                Object value = method.invoke(o);//得到返回值
                HSSFCell cell = row.createCell((short) i);
                cell.setCellValue(String.valueOf(ObjectUtils.isEmpty(value) ? "" : value));
            }
            index++;
        }

    }

    public static HSSFCellStyle getCellStyle(HSSFWorkbook workbook, Short align, Short color, Short foregroundColor, boolean ifBold) {
        // 设置字体
        HSSFFont boldFont = workbook.createFont();
        boldFont.setFontName("宋体");
        boldFont.setFontHeightInPoints((short) 10);
        if (color != null) {
            boldFont.setColor(color);
        }
        if (ifBold) {
            boldFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        }

        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(align);//水平居中等
        cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
        cellStyle.setWrapText(Boolean.TRUE);//换行
        cellStyle.setFont(boldFont);
        cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        cellStyle.setRightBorderColor(HSSFColor.BLACK.index);
        cellStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        cellStyle.setTopBorderColor(HSSFColor.BLACK.index);

        // 设置背景色（使用IndexedColors枚举中的一个颜色）
        if (foregroundColor != null) {
            cellStyle.setFillForegroundColor(foregroundColor);
            cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        }
        return cellStyle;
    }

    /**
     * 创建标题样式(含边框)
     *
     * @param workbook
     * @param align    对齐类型
     * @param index
     * @param red      调色盘-红
     * @param green    调色盘-绿
     * @param blue     调色盘-蓝
     * @param ifBold   是否加粗
     * @return
     */
    public static HSSFCellStyle getHeaderCellStyle(HSSFWorkbook workbook, short align, short index, byte red, byte green, byte blue, boolean ifBold) {
        HSSFPalette palette = workbook.getCustomPalette(); //调色板实例
        palette.setColorAtIndex(index, red, green, blue);
        HSSFColor color = palette.findColor(red, green, blue);
        // 设置字体
        HSSFFont boldFont = workbook.createFont();
        boldFont.setFontName("宋体");
        boldFont.setFontHeightInPoints((short) 10);
        boldFont.setColor(color.getIndex());
        if (ifBold) {
            boldFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        }

        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillForegroundColor(color.getIndex());
        cellStyle.setAlignment(align);//水平居中等
        cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
        cellStyle.setWrapText(Boolean.TRUE);//换行
        cellStyle.setFont(boldFont);
        cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        cellStyle.setRightBorderColor(HSSFColor.BLACK.index);
        cellStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        cellStyle.setTopBorderColor(HSSFColor.BLACK.index);
        return cellStyle;
    }

    public static CellStyle getCellStyle(Workbook workbook, Short align, Short color, Short foregroundColor, boolean ifBold, boolean hasBorder) {
        // 设置字体
        Font boldFont = workbook.createFont();
        boldFont.setFontName("宋体");
        boldFont.setFontHeightInPoints((short) 10);
        if (color != null) {
            boldFont.setColor(color);
        }
        if (ifBold) {
            boldFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        }

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(align);//水平居中等
        cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
        cellStyle.setWrapText(Boolean.TRUE);//换行
        cellStyle.setFont(boldFont);
        if (hasBorder) {
            cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
            cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
            cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
            cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
            cellStyle.setLeftBorderColor(HSSFColor.BLACK.index);
            cellStyle.setRightBorderColor(HSSFColor.BLACK.index);
            cellStyle.setBottomBorderColor(HSSFColor.BLACK.index);
            cellStyle.setTopBorderColor(HSSFColor.BLACK.index);
        }

        // 设置背景色（使用IndexedColors枚举中的一个颜色）
        if (foregroundColor != null) {
            cellStyle.setFillForegroundColor(foregroundColor);
            cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        }
        return cellStyle;
    }

    /**
     * 创建下拉列表选项【适用长度超过255】
     *
     * @param workbook
     * @param values   下拉框的选项值
     * @param firstRow 起始行（从0开始）
     * @param lastRow  终止行（从0开始）
     * @param firstCol 起始列（从0开始）
     * @param lastCol  终止列（从0开始）
     */
    public static void createDropDownExpandList(Workbook workbook, String[] values, int firstRow, int lastRow, int firstCol, int lastCol) {
        Sheet sheet = workbook.getSheetAt(0);
        //创建名为"hidden"的新Sheet页
        Sheet hidden = workbook.createSheet("hidden");

        //遍历选项值填充到"hidden"页的第一列
        Cell cell = null;
        for (int i = 0; i < values.length; i++) {
            String value = values[i];
            Row row = hidden.createRow(i); //第i行
            cell = row.createCell(0);      //第1列
            cell.setCellValue(value);
        }

        //指定"hidden"页面选项值的坐标，上面均填充到第一列 所以从A1开始到A values的个数
        Name name = workbook.createName();
        name.setNameName("hidden");
        name.setRefersToFormula("hidden!$A$1:$A$" + values.length);

        //将"hidden"的数据加载
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint("hidden");

        //设置下拉框作用区域，起始行 终止行 起始列 终止列
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        DataValidation dataValidation = helper.createValidation(constraint, addressList);

        //设置第二个sheet页为隐藏
        workbook.setSheetHidden(1, true);

        // Excel兼容性问题
        if (dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }
        sheet.addValidationData(dataValidation);
    }


    @SuppressWarnings("unchecked")
    public static void main(String[] args) {
        try (OutputStream out = new FileOutputStream("D:\\test.xls");) {
            List<ProjectCheckReturnVo> data = new ArrayList<ProjectCheckReturnVo>();
            ProjectCheckReturnVo pvo = new ProjectCheckReturnVo();
            pvo.setC0("10");
            pvo.setC1("11");
            pvo.setC2("12");
            pvo.setC3("13");
            pvo.setC4("14");
            pvo.setC5("15");
            pvo.setC6("16");
            pvo.setC7("17");
            pvo.setC8("18");
            pvo.setC9("19");
            pvo.setC10("100");
            pvo.setC11("101");
            pvo.setC12("102");
            pvo.setC13("103");
            pvo.setC14("104");


            ProjectCheckReturnVo pvo1 = new ProjectCheckReturnVo();
            pvo1.setC0("10");
            pvo1.setC1("11");
            pvo1.setC2("12");
            pvo1.setC3("13");
            pvo1.setC4("14");
            pvo1.setC5("15");
            pvo1.setC6("16");
            pvo1.setC7("17");
            pvo1.setC8("18");
            pvo1.setC9("19");
            pvo1.setC10("100");
            pvo1.setC11("101");
            pvo1.setC12("102");
            pvo1.setC13("103");
            pvo1.setC14("104");
            data.add(pvo);
            data.add(pvo1);

            String[] headers0 = {"序号", "主题", "创建人", "当前处理人", "创建时间", "状态"};
            String[] headers1 = {"序号", "填报人姓名", "填报人MIP账号", "出勤日期", "填报日期", "填报工时数", "状态"};
            String[] headers2 = {"序号", "子合同编号", "子合同名称", "合同金额（含税）", "开票总金额（含税）", "差异金额"};

            ProjectCheckExcelVo projectCheckExcelVo0 = new ProjectCheckExcelVo();
            projectCheckExcelVo0.setHeaders(headers0);
            projectCheckExcelVo0.setReturnList(data);
            ProjectCheckExcelVo projectCheckExcelVo1 = new ProjectCheckExcelVo();
            projectCheckExcelVo1.setHeaders(headers1);
            projectCheckExcelVo1.setReturnList(data);
            ProjectCheckExcelVo projectCheckExcelVo2 = new ProjectCheckExcelVo();
            projectCheckExcelVo2.setHeaders(headers2);
            projectCheckExcelVo2.setReturnList(data);

            ExportExcelUtil eeu = new ExportExcelUtil();
            HSSFWorkbook workbook = new HSSFWorkbook();
            eeu.exportExcel(workbook, 0, projectCheckExcelVo0, out);
            eeu.exportExcel(workbook, 1, projectCheckExcelVo1, out);
            eeu.exportExcel(workbook, 2, projectCheckExcelVo2, out);
            //原理就是将所有的数据一起写入，然后再关闭输入流。

            workbook.write(out);
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
}
