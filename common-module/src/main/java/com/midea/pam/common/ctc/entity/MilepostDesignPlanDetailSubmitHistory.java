package com.midea.pam.common.ctc.entity;

import com.midea.pam.common.base.LongIdEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "详细设计方案发布历史表")
public class MilepostDesignPlanDetailSubmitHistory extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "项目物料预算id")
    private Long projectBudgetMaterialId;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "父节点id")
    private Long parentId;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "序号")
    private String num;

    @ApiModelProperty(value = "是否为模组")
    private Boolean whetherModel;

    @ApiModelProperty(value = "模组状态:0:未确认;1:已确认")
    private Integer moduleStatus;

    @ApiModelProperty(value = "集成外包")
    private Boolean ext;

    @ApiModelProperty(value = "物料描述")
    private String materielDescr;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    @ApiModelProperty(value = "数量")
    private BigDecimal number;

    @ApiModelProperty(value = "交货时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "型号/规格/图号")
    private String model;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "业务分类")
    private String businessClassification;

    @ApiModelProperty(value = "物料大类")
    private String materialClassification;

    @ApiModelProperty(value = "物料小类")
    private String materielType;

    @ApiModelProperty(value = "“加工件分类”类型有围栏、底座，机加件、焊接钣金件四类")
    private String machiningPartType;

    @ApiModelProperty(value = "材质")
    private String material;

    @ApiModelProperty(value = "单位重量(Kg)")
    private BigDecimal unitWeight;

    @ApiModelProperty(value = "材质处理")
    private String materialProcessing;

    @ApiModelProperty(value = "预算单价")
    private BigDecimal budgetUnitPrice;

    @ApiModelProperty(value = "设计成本id")
    private Long designCostId;

    @ApiModelProperty(value = "设计成本")
    private BigDecimal designCost;

    @ApiModelProperty(value = "预算小计")
    private BigDecimal budgetSubtotal;

    @ApiModelProperty(value = "PAM编码")
    private String pamCode;

    @ApiModelProperty(value = "ERP物料编码")
    private String erpCode;

    @ApiModelProperty(value = "ERP物料编码来源:1=库存组织;2=主组织")
    private Integer erpCodeSource;

    @ApiModelProperty(value = "物料同步状态:0=未同步;1=已同步")
    private Integer materielStatus;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "索引号")
    private String indexNum;

    @ApiModelProperty(value = "审批状态")
    private Integer status;

    @ApiModelProperty(value = "是否产生物料需求")
    private Boolean generateRequirement;

    @ApiModelProperty(value = "导入状态,1为初始化导入数据；0表示系统生成")
    private Boolean init;

    @ApiModelProperty(value = "设计成本是否来源估价")
    private Integer itemCostIsNull;

    @ApiModelProperty(value = "物料类型")
    private String materialCategory;

    @ApiModelProperty(value = "物料中类")
    private String codingMiddleClass;

    @ApiModelProperty(value = "图号")
    private String figureNumber;

    @ApiModelProperty(value = "图纸版本号")
    private String chartVersion;

    @ApiModelProperty(value = "品牌商物料编码")
    private String brandMaterialCode;

    @ApiModelProperty(value = "是否备件标识")
    private String orSparePartsMask;

    @ApiModelProperty(value = "物料需求创建日期")
    private Date requirementCreatDate;

    @ApiModelProperty(value = "提前采购期")
    private Long perchasingLeadtime;

    @ApiModelProperty(value = "最小采购量")
    private Long minPerchaseQuantity;

    @ApiModelProperty(value = "最小包装量")
    private Long minPackageQuantity;

    @ApiModelProperty(value = "项目预算的WBS的上层WBS或;项目预算的WBS;")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "WBS层级")
    private String wbsLayer;

    @ApiModelProperty(value = "WBS进度确认标记（wbs最底层及以下为1，上层为0）")
    private Boolean wbsConfirmFlag;

    @ApiModelProperty(value = "是否急件")
    private Boolean dispatchIs;

    @ApiModelProperty(value = "是否外包")
    private Boolean extIs;

    @ApiModelProperty(value = "活动事项编码")
    private String activityCode;

    @ApiModelProperty(value = "项目预算类别属性")
    private String projectBudgetType;

    @ApiModelProperty(value = "设计人员")
    private String planDesigner;

    @ApiModelProperty(value = "设计发布批次号")
    private String designReleaseLotNumber;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "历史变更类型（0历史，1变更项）")
    private Boolean historyType;

    @ApiModelProperty(value = "单据id")
    private Long projectWbsReceiptsId;

    @ApiModelProperty(value = "详设id")
    private Long designPlanDetailId;

    @ApiModelProperty(value = "0或空：有效， 1：删除")
    private Boolean deletedFlag;

    @ApiModelProperty(value = "是否WBS最底层(0:否;1:是)")
    private Boolean wbsLastLayer;

    @ApiModelProperty(value = "需求类型，具体值参考数据字典design_requirement_type")
    private Integer requirementType;

    private static final long serialVersionUID = 1L;

    public Long getProjectBudgetMaterialId() {
        return projectBudgetMaterialId;
    }

    public void setProjectBudgetMaterialId(Long projectBudgetMaterialId) {
        this.projectBudgetMaterialId = projectBudgetMaterialId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num == null ? null : num.trim();
    }

    public Boolean getWhetherModel() {
        return whetherModel;
    }

    public void setWhetherModel(Boolean whetherModel) {
        this.whetherModel = whetherModel;
    }

    public Integer getModuleStatus() {
        return moduleStatus;
    }

    public void setModuleStatus(Integer moduleStatus) {
        this.moduleStatus = moduleStatus;
    }

    public Boolean getExt() {
        return ext;
    }

    public void setExt(Boolean ext) {
        this.ext = ext;
    }

    public String getMaterielDescr() {
        return materielDescr;
    }

    public void setMaterielDescr(String materielDescr) {
        this.materielDescr = materielDescr == null ? null : materielDescr.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode == null ? null : unitCode.trim();
    }

    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getBusinessClassification() {
        return businessClassification;
    }

    public void setBusinessClassification(String businessClassification) {
        this.businessClassification = businessClassification == null ? null : businessClassification.trim();
    }

    public String getMaterialClassification() {
        return materialClassification;
    }

    public void setMaterialClassification(String materialClassification) {
        this.materialClassification = materialClassification == null ? null : materialClassification.trim();
    }

    public String getMaterielType() {
        return materielType;
    }

    public void setMaterielType(String materielType) {
        this.materielType = materielType == null ? null : materielType.trim();
    }

    public String getMachiningPartType() {
        return machiningPartType;
    }

    public void setMachiningPartType(String machiningPartType) {
        this.machiningPartType = machiningPartType == null ? null : machiningPartType.trim();
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material == null ? null : material.trim();
    }

    public BigDecimal getUnitWeight() {
        return unitWeight;
    }

    public void setUnitWeight(BigDecimal unitWeight) {
        this.unitWeight = unitWeight;
    }

    public String getMaterialProcessing() {
        return materialProcessing;
    }

    public void setMaterialProcessing(String materialProcessing) {
        this.materialProcessing = materialProcessing == null ? null : materialProcessing.trim();
    }

    public BigDecimal getBudgetUnitPrice() {
        return budgetUnitPrice;
    }

    public void setBudgetUnitPrice(BigDecimal budgetUnitPrice) {
        this.budgetUnitPrice = budgetUnitPrice;
    }

    public Long getDesignCostId() {
        return designCostId;
    }

    public void setDesignCostId(Long designCostId) {
        this.designCostId = designCostId;
    }

    public BigDecimal getDesignCost() {
        return designCost;
    }

    public void setDesignCost(BigDecimal designCost) {
        this.designCost = designCost;
    }

    public BigDecimal getBudgetSubtotal() {
        return budgetSubtotal;
    }

    public void setBudgetSubtotal(BigDecimal budgetSubtotal) {
        this.budgetSubtotal = budgetSubtotal;
    }

    public String getPamCode() {
        return pamCode;
    }

    public void setPamCode(String pamCode) {
        this.pamCode = pamCode == null ? null : pamCode.trim();
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public Integer getErpCodeSource() {
        return erpCodeSource;
    }

    public void setErpCodeSource(Integer erpCodeSource) {
        this.erpCodeSource = erpCodeSource;
    }

    public Integer getMaterielStatus() {
        return materielStatus;
    }

    public void setMaterielStatus(Integer materielStatus) {
        this.materielStatus = materielStatus;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getIndexNum() {
        return indexNum;
    }

    public void setIndexNum(String indexNum) {
        this.indexNum = indexNum == null ? null : indexNum.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getGenerateRequirement() {
        return generateRequirement;
    }

    public void setGenerateRequirement(Boolean generateRequirement) {
        this.generateRequirement = generateRequirement;
    }

    public Boolean getInit() {
        return init;
    }

    public void setInit(Boolean init) {
        this.init = init;
    }

    public Integer getItemCostIsNull() {
        return itemCostIsNull;
    }

    public void setItemCostIsNull(Integer itemCostIsNull) {
        this.itemCostIsNull = itemCostIsNull;
    }

    public String getMaterialCategory() {
        return materialCategory;
    }

    public void setMaterialCategory(String materialCategory) {
        this.materialCategory = materialCategory == null ? null : materialCategory.trim();
    }

    public String getCodingMiddleClass() {
        return codingMiddleClass;
    }

    public void setCodingMiddleClass(String codingMiddleClass) {
        this.codingMiddleClass = codingMiddleClass == null ? null : codingMiddleClass.trim();
    }

    public String getFigureNumber() {
        return figureNumber;
    }

    public void setFigureNumber(String figureNumber) {
        this.figureNumber = figureNumber == null ? null : figureNumber.trim();
    }

    public String getChartVersion() {
        return chartVersion;
    }

    public void setChartVersion(String chartVersion) {
        this.chartVersion = chartVersion == null ? null : chartVersion.trim();
    }

    public String getBrandMaterialCode() {
        return brandMaterialCode;
    }

    public void setBrandMaterialCode(String brandMaterialCode) {
        this.brandMaterialCode = brandMaterialCode == null ? null : brandMaterialCode.trim();
    }

    public String getOrSparePartsMask() {
        return orSparePartsMask;
    }

    public void setOrSparePartsMask(String orSparePartsMask) {
        this.orSparePartsMask = orSparePartsMask == null ? null : orSparePartsMask.trim();
    }

    public Date getRequirementCreatDate() {
        return requirementCreatDate;
    }

    public void setRequirementCreatDate(Date requirementCreatDate) {
        this.requirementCreatDate = requirementCreatDate;
    }

    public Long getPerchasingLeadtime() {
        return perchasingLeadtime;
    }

    public void setPerchasingLeadtime(Long perchasingLeadtime) {
        this.perchasingLeadtime = perchasingLeadtime;
    }

    public Long getMinPerchaseQuantity() {
        return minPerchaseQuantity;
    }

    public void setMinPerchaseQuantity(Long minPerchaseQuantity) {
        this.minPerchaseQuantity = minPerchaseQuantity;
    }

    public Long getMinPackageQuantity() {
        return minPackageQuantity;
    }

    public void setMinPackageQuantity(Long minPackageQuantity) {
        this.minPackageQuantity = minPackageQuantity;
    }

    public String getWbsSummaryCode() {
        return wbsSummaryCode;
    }

    public void setWbsSummaryCode(String wbsSummaryCode) {
        this.wbsSummaryCode = wbsSummaryCode == null ? null : wbsSummaryCode.trim();
    }

    public String getWbsLayer() {
        return wbsLayer;
    }

    public void setWbsLayer(String wbsLayer) {
        this.wbsLayer = wbsLayer == null ? null : wbsLayer.trim();
    }

    public Boolean getWbsConfirmFlag() {
        return wbsConfirmFlag;
    }

    public void setWbsConfirmFlag(Boolean wbsConfirmFlag) {
        this.wbsConfirmFlag = wbsConfirmFlag;
    }

    public Boolean getDispatchIs() {
        return dispatchIs;
    }

    public void setDispatchIs(Boolean dispatchIs) {
        this.dispatchIs = dispatchIs;
    }

    public Boolean getExtIs() {
        return extIs;
    }

    public void setExtIs(Boolean extIs) {
        this.extIs = extIs;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    public String getProjectBudgetType() {
        return projectBudgetType;
    }

    public void setProjectBudgetType(String projectBudgetType) {
        this.projectBudgetType = projectBudgetType == null ? null : projectBudgetType.trim();
    }

    public String getPlanDesigner() {
        return planDesigner;
    }

    public void setPlanDesigner(String planDesigner) {
        this.planDesigner = planDesigner == null ? null : planDesigner.trim();
    }

    public String getDesignReleaseLotNumber() {
        return designReleaseLotNumber;
    }

    public void setDesignReleaseLotNumber(String designReleaseLotNumber) {
        this.designReleaseLotNumber = designReleaseLotNumber == null ? null : designReleaseLotNumber.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Boolean getHistoryType() {
        return historyType;
    }

    public void setHistoryType(Boolean historyType) {
        this.historyType = historyType;
    }

    public Long getProjectWbsReceiptsId() {
        return projectWbsReceiptsId;
    }

    public void setProjectWbsReceiptsId(Long projectWbsReceiptsId) {
        this.projectWbsReceiptsId = projectWbsReceiptsId;
    }

    public Long getDesignPlanDetailId() {
        return designPlanDetailId;
    }

    public void setDesignPlanDetailId(Long designPlanDetailId) {
        this.designPlanDetailId = designPlanDetailId;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    public Boolean getWbsLastLayer() {
        return wbsLastLayer;
    }

    public void setWbsLastLayer(Boolean wbsLastLayer) {
        this.wbsLastLayer = wbsLastLayer;
    }

    public Integer getRequirementType() {
        return requirementType;
    }

    public void setRequirementType(Integer requirementType) {
        this.requirementType = requirementType;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectBudgetMaterialId=").append(projectBudgetMaterialId);
        sb.append(", projectId=").append(projectId);
        sb.append(", parentId=").append(parentId);
        sb.append(", level=").append(level);
        sb.append(", num=").append(num);
        sb.append(", whetherModel=").append(whetherModel);
        sb.append(", moduleStatus=").append(moduleStatus);
        sb.append(", ext=").append(ext);
        sb.append(", materielDescr=").append(materielDescr);
        sb.append(", unit=").append(unit);
        sb.append(", unitCode=").append(unitCode);
        sb.append(", number=").append(number);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", name=").append(name);
        sb.append(", model=").append(model);
        sb.append(", brand=").append(brand);
        sb.append(", businessClassification=").append(businessClassification);
        sb.append(", materialClassification=").append(materialClassification);
        sb.append(", materielType=").append(materielType);
        sb.append(", machiningPartType=").append(machiningPartType);
        sb.append(", material=").append(material);
        sb.append(", unitWeight=").append(unitWeight);
        sb.append(", materialProcessing=").append(materialProcessing);
        sb.append(", budgetUnitPrice=").append(budgetUnitPrice);
        sb.append(", designCostId=").append(designCostId);
        sb.append(", designCost=").append(designCost);
        sb.append(", budgetSubtotal=").append(budgetSubtotal);
        sb.append(", pamCode=").append(pamCode);
        sb.append(", erpCode=").append(erpCode);
        sb.append(", erpCodeSource=").append(erpCodeSource);
        sb.append(", materielStatus=").append(materielStatus);
        sb.append(", source=").append(source);
        sb.append(", remark=").append(remark);
        sb.append(", indexNum=").append(indexNum);
        sb.append(", status=").append(status);
        sb.append(", generateRequirement=").append(generateRequirement);
        sb.append(", init=").append(init);
        sb.append(", itemCostIsNull=").append(itemCostIsNull);
        sb.append(", materialCategory=").append(materialCategory);
        sb.append(", codingMiddleClass=").append(codingMiddleClass);
        sb.append(", figureNumber=").append(figureNumber);
        sb.append(", chartVersion=").append(chartVersion);
        sb.append(", brandMaterialCode=").append(brandMaterialCode);
        sb.append(", orSparePartsMask=").append(orSparePartsMask);
        sb.append(", requirementCreatDate=").append(requirementCreatDate);
        sb.append(", perchasingLeadtime=").append(perchasingLeadtime);
        sb.append(", minPerchaseQuantity=").append(minPerchaseQuantity);
        sb.append(", minPackageQuantity=").append(minPackageQuantity);
        sb.append(", wbsSummaryCode=").append(wbsSummaryCode);
        sb.append(", wbsLayer=").append(wbsLayer);
        sb.append(", wbsConfirmFlag=").append(wbsConfirmFlag);
        sb.append(", dispatchIs=").append(dispatchIs);
        sb.append(", extIs=").append(extIs);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", projectBudgetType=").append(projectBudgetType);
        sb.append(", planDesigner=").append(planDesigner);
        sb.append(", designReleaseLotNumber=").append(designReleaseLotNumber);
        sb.append(", description=").append(description);
        sb.append(", historyType=").append(historyType);
        sb.append(", projectWbsReceiptsId=").append(projectWbsReceiptsId);
        sb.append(", designPlanDetailId=").append(designPlanDetailId);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", wbsLastLayer=").append(wbsLastLayer);
        sb.append(", requirementType=").append(requirementType);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}