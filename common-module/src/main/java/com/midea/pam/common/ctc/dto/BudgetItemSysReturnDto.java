package com.midea.pam.common.ctc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: pam
 * @description: 预算项目号同步接口返回类
 * @author: chenchong
 * @create: 2025-05-23
 **/
@Getter
@Setter
public class BudgetItemSysReturnDto {
    @ApiModelProperty(value = "预算系统编码(唯一键)")
    private String budgetSystemCode;
    @ApiModelProperty(value = "预算项目编码")
    private String budgetItemCode;
    @ApiModelProperty(value = "预算项目名称")
    private String budgetItemName;
    @ApiModelProperty(value = "预算项目类型:1-收入2-支出3-无")
    private String budgetItemType;
    @ApiModelProperty(value = "所属类别:1-成员单位预算项目2-结算中心预算项目3-财务公司预算项目")
    private String category;
    @ApiModelProperty(value = "公式")
    private String expression;
    @ApiModelProperty(value = "是否子叶节点标识 (1、是 2、否)")
    private String isLeaf;
    @ApiModelProperty(value = "上级节点编码")
    private String parentNode;
    @ApiModelProperty(value = "上级节点名称")
    private String parentNodeName;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "状态: 1有效 2无效")
    private String status;
    @ApiModelProperty(value = "创建时间")
    private Long createTime;
    @ApiModelProperty(value = "最后修改时间")
    private Long updateTime;
    @ApiModelProperty(value = "创建时间mip")
    private String createBy;
    @ApiModelProperty(value = "最后修改mip")
    private String updateBy;

}
