package com.midea.pam.common.esb.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class EmsPassRequest {

    /**   通用字段   **/
    private String sourceSystem;
    private String sourceOrderType;
    private String sourceOrderId;
    private String sourceOrderCode;
    private Date syncDate;
    private String syncStatus;
    private String ioOrderId;
    private String orderId;
    private String orderCode;
    private String orderStatus;
    private String bizStatus;
    private String emsVerifInvoice;
    private String emsVerifMoney;
    private String invoiceBizStatus;
    private String importErpStatus;
    private String applyAmount;
    private String erpPayAmount;
    private String glDate;
    private String invoiceId;
    private String invoiceCode;
    private String invoiceAmount;
    private String verifCode;
    private Date errorDate;
    private String errorReason;
    private String errorType;
    private String refuseFlag;
    private String orderType;
    private String emsOrderNumber;
    private String emsOrderStatus;
    private String redVoucherStatus;
    private String jumpUrl;
    private String attribute1;
    private String attribute2;
    private String attribute3;
    private String attribute4;
    private String attribute5;
    private String attribute6;
    private String attribute7;
    private String attribute8;
    private String attribute9;
    private String attribute10;
    private String attribute11;
    private String attribute12;
    private String attribute13;
    private String attribute14;
    private String attribute15;
    private String attribute16;
    private String attribute17;
    private String attribute18;
    private String attribute19;
    private String attribute20;
    private Date applyDate;
    private String erpInvoiceId;
    private String erpInvoiceNum;
    private Date erpPayDate;
    private Date impErpDate;
    private String impErpBy;
    private String impErpName;
    private String impErpAccount;
    private String isRefuse;
    private String refuseNote;
    private String companyName;
    private String releasedBy;
    private String releasedReason;
    private Date completedTime;

    /**   PAM-EMS-006字段   **/
    private BigDecimal budgetAmount;
    private Long budgetHeadersId;
    private Long budgetNodeId;
    private String busiOrgCode;
    private String busiOrgName;
    private String bussinessTypeCode;
    private String bussinessTypeName;
    private Date creationDate;
    private String feeTypeCode;
    private String feeTypeName;
    private Long ioBudgetNodeId;
    private String isCreatedFlag;
    private Date lastUpdateDate;
    private Long tenantId;
    private String yearCode;
}

