package com.midea.pam.common.constants;

import io.swagger.annotations.ApiModelProperty;

public class WbsBudgetFieldConstant {


    public static final String WBS = "wbs";
    public static final String ACTIVITY = "activity";

    public static final String ID = "id";
    public static final String ROW_ID = "rowId";
    public static final String ORIGIN_ID = "originId";
    public static final String PARENT_ID = "parentId";
    public static final String PARENT_WBS_ID = "parentWbsId";
    public static final String PARENT_ACTIVITY_ID = "parentActivityId";
    @ApiModelProperty(value = "变更头id")
    public static final String HEADER_ID = "headerId";

    @ApiModelProperty(value = "费用类型设置方式")
    public static final String FEE_SETTING_MODE = "feeSettingMode";

    @ApiModelProperty(value = "是否同步EMS")
    public static final String SYNC_EMS = "syncEMS";
    @ApiModelProperty(value = "经济事项id")
    public static final String FEE_TYPE_ID = "feeTypeId";
    @ApiModelProperty(value = "经济事项名称")
    public static final String FEE_TYPE_NAME = "feeTypeName";
    @ApiModelProperty(value = "经济事项编码")
    public static final String FEE_TYPE_CODE = "feeTypeCode";
    @ApiModelProperty(value = "经济事项是否同步ems")
    public static final String FEE_SYNC_EMS = "feeSyncEms";

    @ApiModelProperty(value = "活动事项编码")
    public static final String ACTIVITY_CODE = "activityCode";
    @ApiModelProperty(value = "活动类别名称")
    public static final String ACTIVITY_NAME = "activityName";
    @ApiModelProperty(value = "活动类别属性")
    public static final String ACTIVITY_TYPE = "activityType";
    @ApiModelProperty(value = "活动事项序号")
    public static final String ACTIVITY_ORDER_NO = "activityOrderNo";

    @ApiModelProperty(value = "附件导入方式：ADD增量导入")
    public static final String ADD = "ADD";
    @ApiModelProperty(value = "附件导入方式：COVER覆盖导入")
    public static final String COVER = "COVER";
    @ApiModelProperty(value = "附件导入方式：ADD增量导入 & COVER覆盖导入")
    public static final String TYPE = "type";

    @ApiModelProperty(value = "预算金额")
    public static final String PRICE = "price";
    @ApiModelProperty(value = "预算基线")
    public static final String BASELINE_COST = "baselineCost";
    @ApiModelProperty(value = "预算基线-变更后")
    public static final String AFTER_CHANGE_BASELINE_COST = "afterChangeBaselineCost";
    @ApiModelProperty(value = "预算基线差异")
    public static final String DIFF_BASELINE_COST = "diffBaselineCost";
    @ApiModelProperty(value = "需求预算")
    public static final String DEMAND_COST = "demandCost";
    @ApiModelProperty(value = "在途成本")
    public static final String ON_THE_WAY_COST = "onTheWayCost";
    @ApiModelProperty(value = "已发生成本")
    public static final String INCURRED_COST = "incurredCost";
    @ApiModelProperty(value = "剩余可用预算", notes = "= 预算金额 - 需求预算 - 在途成本 - 已发生成本")
    public static final String REMAINING_COST = "remainingCost";
    @ApiModelProperty(value = "累计变更金额", notes = "= 预算金额 - 预算基线")
    public static final String CHANGE_ACCUMULATE_COST = "changeAccumulateCost";
    @ApiModelProperty(value = "执行时间,项目详情中项目成本页面的更新时间")
    public static final String EXECUTE_TIME = "executeTime";
    @ApiModelProperty(value = "累计变更金额", notes = "= 累计变更金额")

    public static final String AFTER_CHANGE_PRICE = "afterChangePrice";
    public static final String AFTER_CHANGE_REMAINING_COST = "afterChangeRemainingCost";
    public static final String AFTER_CHANGE_CHANGE_ACCUMULATE_COST = "afterChangeChangeAccumulateCost";

    public static final String CHANGE_PRICE = "changePrice";

    @ApiModelProperty(value = "wbs全码")
    public static final String WBS_SUMMARY_CODE = "wbsSummaryCode";
    @ApiModelProperty(value = "拼接全wbs编码")
    public static final String WBS_FULL_CODE = "wbsFullCode";
    @ApiModelProperty(value = "最底层wbs编码")
    public static final String WBS_LAST_CODE = "wbsLastCode";

    @ApiModelProperty(value = "动态列wbs模板规则id数组")
    public static final String DYNAMIC_WBS_TEMPLATE_RULE_ISD = "dynamicWbsTemplateRuleIds";
    @ApiModelProperty(value = "动态列字段名数组")
    public static final String DYNAMIC_FIELDS = "dynamicFields";
    @ApiModelProperty(value = "动态列编码数组")
    public static final String DYNAMIC_VALUES = "dynamicValues";


    @ApiModelProperty(value = "excel行数")
    public static final String EXCEL_ROW_NUM = "excelRowNum";

    @ApiModelProperty("汇总明细查询标记")
    public static final String PROJECT_DETAIL_SELECT_FLAG = "projectDetailSelectFlag";

    public static final String CODE = "code";

    @ApiModelProperty(value = "汇总查询编码")
    public static final String SUMMARY_CODE = "summaryCode";
    public static final String SUMMARY_TYPE = "summaryType";
    public static final String REMARK = "remark";
    public static final String PROJECT_ID = "projectId";
    public static final String PROJECT_CODE = "projectCode";
    public static final String PROJECT_NAME = "projectName";
    public static final String PROJECT_TYPE = "projectType";
    public static final String PROJECT_BASELINE_BATCH_ID = "projectBaselineBatchId";
    public static final String PROJECT_BASELINE_BATCH_CODE = "projectBaselineBatchCode";
    public static final String WBS_TEMPLATE_INFO_ID = "wbsTemplateInfoId";
    public static final String WBS_TEMPLATE_INFO_NAME = "wbsTemplateInfoName";
    public static final String WBS_TEMPLATE_RULE_ID = "wbsTemplateRuleId";
    public static final String DELETED_FLAG = "deletedFlag";
    public static final String DESCRIPTION = "description";
    public static final String PROJECT_WBS_BUDGET_ID = "projectWbsBudgetId";
    public static final String PROJECT_WBS_RECEIPTS_ID = "projectWbsReceiptsId";
    public static final String TAG = "tag";

    public static final String INDEX = "index";
    public static final String ORDER_NO = "orderNo";

    public static final String DETAIL = "detail";
    public static final String CHILDS = "childs";
    public static final String DETAILS = "details";

    public static final String CREATE_BY = "createBy";
    public static final String CREATE_AT = "createAt";
    public static final String UPDATE_BY = "updateBy";
    public static final String UPDATE_AT = "updateAt";
}
