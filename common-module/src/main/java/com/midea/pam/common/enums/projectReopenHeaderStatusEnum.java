package com.midea.pam.common.enums;

public enum projectReopenHeaderStatusEnum {
    DRAFT(0, "草稿"),
    PENDING(1, "审批中"),
    PASS_THROUGH(2, "审批通过"),
    DELETE(3, "作废"),
    RETURN(4, "撤回"),
    REFUSE(5, "驳回"),
    ;

    projectReopenHeaderStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;
    private String msg;

    public int getCode() {
        return code;
    }

    public String getName() {
        return msg;
    }


}
