package com.midea.pam.common.sdp.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SdpResponse {

    /**  Pam请求第三方，对方响应 场景：CRM **/
    @ApiModelProperty(value = "响应码")
    private String resultCode;

    @ApiModelProperty(value = "响应信息")
    private String resultMsg;

    @ApiModelProperty(value = "响应数据")
    private GcrmResult data;


    /**  第三方回调，Pam响应 **/
    @ApiModelProperty(value = "响应类型")
    private String responseType;

    @ApiModelProperty(value = "响应码")
    private String responseCode;

    @ApiModelProperty(value = "响应信息")
    private String responseMessage;

}
