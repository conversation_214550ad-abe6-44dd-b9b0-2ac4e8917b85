package com.midea.pam.common.ctc.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BusiSceneNonSaleDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public BusiSceneNonSaleDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdIsNull() {
            addCriterion("busi_scene_non_sale_id is null");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdIsNotNull() {
            addCriterion("busi_scene_non_sale_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdEqualTo(Long value) {
            addCriterion("busi_scene_non_sale_id =", value, "busiSceneNonSaleId");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdNotEqualTo(Long value) {
            addCriterion("busi_scene_non_sale_id <>", value, "busiSceneNonSaleId");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdGreaterThan(Long value) {
            addCriterion("busi_scene_non_sale_id >", value, "busiSceneNonSaleId");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("busi_scene_non_sale_id >=", value, "busiSceneNonSaleId");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdLessThan(Long value) {
            addCriterion("busi_scene_non_sale_id <", value, "busiSceneNonSaleId");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdLessThanOrEqualTo(Long value) {
            addCriterion("busi_scene_non_sale_id <=", value, "busiSceneNonSaleId");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdIn(List<Long> values) {
            addCriterion("busi_scene_non_sale_id in", values, "busiSceneNonSaleId");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdNotIn(List<Long> values) {
            addCriterion("busi_scene_non_sale_id not in", values, "busiSceneNonSaleId");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdBetween(Long value1, Long value2) {
            addCriterion("busi_scene_non_sale_id between", value1, value2, "busiSceneNonSaleId");
            return (Criteria) this;
        }

        public Criteria andBusiSceneNonSaleIdNotBetween(Long value1, Long value2) {
            addCriterion("busi_scene_non_sale_id not between", value1, value2, "busiSceneNonSaleId");
            return (Criteria) this;
        }

        public Criteria andSeqCodeIsNull() {
            addCriterion("seq_code is null");
            return (Criteria) this;
        }

        public Criteria andSeqCodeIsNotNull() {
            addCriterion("seq_code is not null");
            return (Criteria) this;
        }

        public Criteria andSeqCodeEqualTo(Long value) {
            addCriterion("seq_code =", value, "seqCode");
            return (Criteria) this;
        }

        public Criteria andSeqCodeNotEqualTo(Long value) {
            addCriterion("seq_code <>", value, "seqCode");
            return (Criteria) this;
        }

        public Criteria andSeqCodeGreaterThan(Long value) {
            addCriterion("seq_code >", value, "seqCode");
            return (Criteria) this;
        }

        public Criteria andSeqCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("seq_code >=", value, "seqCode");
            return (Criteria) this;
        }

        public Criteria andSeqCodeLessThan(Long value) {
            addCriterion("seq_code <", value, "seqCode");
            return (Criteria) this;
        }

        public Criteria andSeqCodeLessThanOrEqualTo(Long value) {
            addCriterion("seq_code <=", value, "seqCode");
            return (Criteria) this;
        }

        public Criteria andSeqCodeIn(List<Long> values) {
            addCriterion("seq_code in", values, "seqCode");
            return (Criteria) this;
        }

        public Criteria andSeqCodeNotIn(List<Long> values) {
            addCriterion("seq_code not in", values, "seqCode");
            return (Criteria) this;
        }

        public Criteria andSeqCodeBetween(Long value1, Long value2) {
            addCriterion("seq_code between", value1, value2, "seqCode");
            return (Criteria) this;
        }

        public Criteria andSeqCodeNotBetween(Long value1, Long value2) {
            addCriterion("seq_code not between", value1, value2, "seqCode");
            return (Criteria) this;
        }

        public Criteria andModuleIsNull() {
            addCriterion("module is null");
            return (Criteria) this;
        }

        public Criteria andModuleIsNotNull() {
            addCriterion("module is not null");
            return (Criteria) this;
        }

        public Criteria andModuleEqualTo(String value) {
            addCriterion("module =", value, "module");
            return (Criteria) this;
        }

        public Criteria andModuleNotEqualTo(String value) {
            addCriterion("module <>", value, "module");
            return (Criteria) this;
        }

        public Criteria andModuleGreaterThan(String value) {
            addCriterion("module >", value, "module");
            return (Criteria) this;
        }

        public Criteria andModuleGreaterThanOrEqualTo(String value) {
            addCriterion("module >=", value, "module");
            return (Criteria) this;
        }

        public Criteria andModuleLessThan(String value) {
            addCriterion("module <", value, "module");
            return (Criteria) this;
        }

        public Criteria andModuleLessThanOrEqualTo(String value) {
            addCriterion("module <=", value, "module");
            return (Criteria) this;
        }

        public Criteria andModuleLike(String value) {
            addCriterion("module like", value, "module");
            return (Criteria) this;
        }

        public Criteria andModuleNotLike(String value) {
            addCriterion("module not like", value, "module");
            return (Criteria) this;
        }

        public Criteria andModuleIn(List<String> values) {
            addCriterion("module in", values, "module");
            return (Criteria) this;
        }

        public Criteria andModuleNotIn(List<String> values) {
            addCriterion("module not in", values, "module");
            return (Criteria) this;
        }

        public Criteria andModuleBetween(String value1, String value2) {
            addCriterion("module between", value1, value2, "module");
            return (Criteria) this;
        }

        public Criteria andModuleNotBetween(String value1, String value2) {
            addCriterion("module not between", value1, value2, "module");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeIsNull() {
            addCriterion("interface_code is null");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeIsNotNull() {
            addCriterion("interface_code is not null");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeEqualTo(String value) {
            addCriterion("interface_code =", value, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeNotEqualTo(String value) {
            addCriterion("interface_code <>", value, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeGreaterThan(String value) {
            addCriterion("interface_code >", value, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("interface_code >=", value, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeLessThan(String value) {
            addCriterion("interface_code <", value, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeLessThanOrEqualTo(String value) {
            addCriterion("interface_code <=", value, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeLike(String value) {
            addCriterion("interface_code like", value, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeNotLike(String value) {
            addCriterion("interface_code not like", value, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeIn(List<String> values) {
            addCriterion("interface_code in", values, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeNotIn(List<String> values) {
            addCriterion("interface_code not in", values, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeBetween(String value1, String value2) {
            addCriterion("interface_code between", value1, value2, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andInterfaceCodeNotBetween(String value1, String value2) {
            addCriterion("interface_code not between", value1, value2, "interfaceCode");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdIsNull() {
            addCriterion("receivables_trx_id is null");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdIsNotNull() {
            addCriterion("receivables_trx_id is not null");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdEqualTo(Long value) {
            addCriterion("receivables_trx_id =", value, "receivablesTrxId");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdNotEqualTo(Long value) {
            addCriterion("receivables_trx_id <>", value, "receivablesTrxId");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdGreaterThan(Long value) {
            addCriterion("receivables_trx_id >", value, "receivablesTrxId");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdGreaterThanOrEqualTo(Long value) {
            addCriterion("receivables_trx_id >=", value, "receivablesTrxId");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdLessThan(Long value) {
            addCriterion("receivables_trx_id <", value, "receivablesTrxId");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdLessThanOrEqualTo(Long value) {
            addCriterion("receivables_trx_id <=", value, "receivablesTrxId");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdIn(List<Long> values) {
            addCriterion("receivables_trx_id in", values, "receivablesTrxId");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdNotIn(List<Long> values) {
            addCriterion("receivables_trx_id not in", values, "receivablesTrxId");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdBetween(Long value1, Long value2) {
            addCriterion("receivables_trx_id between", value1, value2, "receivablesTrxId");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxIdNotBetween(Long value1, Long value2) {
            addCriterion("receivables_trx_id not between", value1, value2, "receivablesTrxId");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameIsNull() {
            addCriterion("receivables_trx_name is null");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameIsNotNull() {
            addCriterion("receivables_trx_name is not null");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameEqualTo(String value) {
            addCriterion("receivables_trx_name =", value, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameNotEqualTo(String value) {
            addCriterion("receivables_trx_name <>", value, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameGreaterThan(String value) {
            addCriterion("receivables_trx_name >", value, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameGreaterThanOrEqualTo(String value) {
            addCriterion("receivables_trx_name >=", value, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameLessThan(String value) {
            addCriterion("receivables_trx_name <", value, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameLessThanOrEqualTo(String value) {
            addCriterion("receivables_trx_name <=", value, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameLike(String value) {
            addCriterion("receivables_trx_name like", value, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameNotLike(String value) {
            addCriterion("receivables_trx_name not like", value, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameIn(List<String> values) {
            addCriterion("receivables_trx_name in", values, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameNotIn(List<String> values) {
            addCriterion("receivables_trx_name not in", values, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameBetween(String value1, String value2) {
            addCriterion("receivables_trx_name between", value1, value2, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andReceivablesTrxNameNotBetween(String value1, String value2) {
            addCriterion("receivables_trx_name not between", value1, value2, "receivablesTrxName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdIsNull() {
            addCriterion("cust_trx_type_id is null");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdIsNotNull() {
            addCriterion("cust_trx_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdEqualTo(Long value) {
            addCriterion("cust_trx_type_id =", value, "custTrxTypeId");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNotEqualTo(Long value) {
            addCriterion("cust_trx_type_id <>", value, "custTrxTypeId");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdGreaterThan(Long value) {
            addCriterion("cust_trx_type_id >", value, "custTrxTypeId");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("cust_trx_type_id >=", value, "custTrxTypeId");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdLessThan(Long value) {
            addCriterion("cust_trx_type_id <", value, "custTrxTypeId");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdLessThanOrEqualTo(Long value) {
            addCriterion("cust_trx_type_id <=", value, "custTrxTypeId");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdIn(List<Long> values) {
            addCriterion("cust_trx_type_id in", values, "custTrxTypeId");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNotIn(List<Long> values) {
            addCriterion("cust_trx_type_id not in", values, "custTrxTypeId");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdBetween(Long value1, Long value2) {
            addCriterion("cust_trx_type_id between", value1, value2, "custTrxTypeId");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNotBetween(Long value1, Long value2) {
            addCriterion("cust_trx_type_id not between", value1, value2, "custTrxTypeId");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameIsNull() {
            addCriterion("cust_trx_type_id_name is null");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameIsNotNull() {
            addCriterion("cust_trx_type_id_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameEqualTo(String value) {
            addCriterion("cust_trx_type_id_name =", value, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameNotEqualTo(String value) {
            addCriterion("cust_trx_type_id_name <>", value, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameGreaterThan(String value) {
            addCriterion("cust_trx_type_id_name >", value, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameGreaterThanOrEqualTo(String value) {
            addCriterion("cust_trx_type_id_name >=", value, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameLessThan(String value) {
            addCriterion("cust_trx_type_id_name <", value, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameLessThanOrEqualTo(String value) {
            addCriterion("cust_trx_type_id_name <=", value, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameLike(String value) {
            addCriterion("cust_trx_type_id_name like", value, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameNotLike(String value) {
            addCriterion("cust_trx_type_id_name not like", value, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameIn(List<String> values) {
            addCriterion("cust_trx_type_id_name in", values, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameNotIn(List<String> values) {
            addCriterion("cust_trx_type_id_name not in", values, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameBetween(String value1, String value2) {
            addCriterion("cust_trx_type_id_name between", value1, value2, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andCustTrxTypeIdNameNotBetween(String value1, String value2) {
            addCriterion("cust_trx_type_id_name not between", value1, value2, "custTrxTypeIdName");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitIsNull() {
            addCriterion("account_group_debit is null");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitIsNotNull() {
            addCriterion("account_group_debit is not null");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitEqualTo(String value) {
            addCriterion("account_group_debit =", value, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitNotEqualTo(String value) {
            addCriterion("account_group_debit <>", value, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitGreaterThan(String value) {
            addCriterion("account_group_debit >", value, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitGreaterThanOrEqualTo(String value) {
            addCriterion("account_group_debit >=", value, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitLessThan(String value) {
            addCriterion("account_group_debit <", value, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitLessThanOrEqualTo(String value) {
            addCriterion("account_group_debit <=", value, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitLike(String value) {
            addCriterion("account_group_debit like", value, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitNotLike(String value) {
            addCriterion("account_group_debit not like", value, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitIn(List<String> values) {
            addCriterion("account_group_debit in", values, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitNotIn(List<String> values) {
            addCriterion("account_group_debit not in", values, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitBetween(String value1, String value2) {
            addCriterion("account_group_debit between", value1, value2, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupDebitNotBetween(String value1, String value2) {
            addCriterion("account_group_debit not between", value1, value2, "accountGroupDebit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditIsNull() {
            addCriterion("account_group_credit is null");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditIsNotNull() {
            addCriterion("account_group_credit is not null");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditEqualTo(String value) {
            addCriterion("account_group_credit =", value, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditNotEqualTo(String value) {
            addCriterion("account_group_credit <>", value, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditGreaterThan(String value) {
            addCriterion("account_group_credit >", value, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditGreaterThanOrEqualTo(String value) {
            addCriterion("account_group_credit >=", value, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditLessThan(String value) {
            addCriterion("account_group_credit <", value, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditLessThanOrEqualTo(String value) {
            addCriterion("account_group_credit <=", value, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditLike(String value) {
            addCriterion("account_group_credit like", value, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditNotLike(String value) {
            addCriterion("account_group_credit not like", value, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditIn(List<String> values) {
            addCriterion("account_group_credit in", values, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditNotIn(List<String> values) {
            addCriterion("account_group_credit not in", values, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditBetween(String value1, String value2) {
            addCriterion("account_group_credit between", value1, value2, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andAccountGroupCreditNotBetween(String value1, String value2) {
            addCriterion("account_group_credit not between", value1, value2, "accountGroupCredit");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdIsNull() {
            addCriterion("bank_account_id is null");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdIsNotNull() {
            addCriterion("bank_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdEqualTo(Long value) {
            addCriterion("bank_account_id =", value, "bankAccountId");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdNotEqualTo(Long value) {
            addCriterion("bank_account_id <>", value, "bankAccountId");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdGreaterThan(Long value) {
            addCriterion("bank_account_id >", value, "bankAccountId");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdGreaterThanOrEqualTo(Long value) {
            addCriterion("bank_account_id >=", value, "bankAccountId");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdLessThan(Long value) {
            addCriterion("bank_account_id <", value, "bankAccountId");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdLessThanOrEqualTo(Long value) {
            addCriterion("bank_account_id <=", value, "bankAccountId");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdIn(List<Long> values) {
            addCriterion("bank_account_id in", values, "bankAccountId");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdNotIn(List<Long> values) {
            addCriterion("bank_account_id not in", values, "bankAccountId");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdBetween(Long value1, Long value2) {
            addCriterion("bank_account_id between", value1, value2, "bankAccountId");
            return (Criteria) this;
        }

        public Criteria andBankAccountIdNotBetween(Long value1, Long value2) {
            addCriterion("bank_account_id not between", value1, value2, "bankAccountId");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumIsNull() {
            addCriterion("bank_account_num is null");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumIsNotNull() {
            addCriterion("bank_account_num is not null");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumEqualTo(String value) {
            addCriterion("bank_account_num =", value, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumNotEqualTo(String value) {
            addCriterion("bank_account_num <>", value, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumGreaterThan(String value) {
            addCriterion("bank_account_num >", value, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumGreaterThanOrEqualTo(String value) {
            addCriterion("bank_account_num >=", value, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumLessThan(String value) {
            addCriterion("bank_account_num <", value, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumLessThanOrEqualTo(String value) {
            addCriterion("bank_account_num <=", value, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumLike(String value) {
            addCriterion("bank_account_num like", value, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumNotLike(String value) {
            addCriterion("bank_account_num not like", value, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumIn(List<String> values) {
            addCriterion("bank_account_num in", values, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumNotIn(List<String> values) {
            addCriterion("bank_account_num not in", values, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumBetween(String value1, String value2) {
            addCriterion("bank_account_num between", value1, value2, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andBankAccountNumNotBetween(String value1, String value2) {
            addCriterion("bank_account_num not between", value1, value2, "bankAccountNum");
            return (Criteria) this;
        }

        public Criteria andFlagIsNull() {
            addCriterion("flag is null");
            return (Criteria) this;
        }

        public Criteria andFlagIsNotNull() {
            addCriterion("flag is not null");
            return (Criteria) this;
        }

        public Criteria andFlagEqualTo(String value) {
            addCriterion("flag =", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotEqualTo(String value) {
            addCriterion("flag <>", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagGreaterThan(String value) {
            addCriterion("flag >", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagGreaterThanOrEqualTo(String value) {
            addCriterion("flag >=", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagLessThan(String value) {
            addCriterion("flag <", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagLessThanOrEqualTo(String value) {
            addCriterion("flag <=", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagLike(String value) {
            addCriterion("flag like", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotLike(String value) {
            addCriterion("flag not like", value, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagIn(List<String> values) {
            addCriterion("flag in", values, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotIn(List<String> values) {
            addCriterion("flag not in", values, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagBetween(String value1, String value2) {
            addCriterion("flag between", value1, value2, "flag");
            return (Criteria) this;
        }

        public Criteria andFlagNotBetween(String value1, String value2) {
            addCriterion("flag not between", value1, value2, "flag");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Date value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Date value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Date value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Date value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Date value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Date> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Date> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Date value1, Date value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Date value1, Date value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptIsNull() {
            addCriterion("is_sync_receipt is null");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptIsNotNull() {
            addCriterion("is_sync_receipt is not null");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptEqualTo(Integer value) {
            addCriterion("is_sync_receipt =", value, "isSyncReceipt");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptNotEqualTo(Integer value) {
            addCriterion("is_sync_receipt <>", value, "isSyncReceipt");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptGreaterThan(Integer value) {
            addCriterion("is_sync_receipt >", value, "isSyncReceipt");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_sync_receipt >=", value, "isSyncReceipt");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptLessThan(Integer value) {
            addCriterion("is_sync_receipt <", value, "isSyncReceipt");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptLessThanOrEqualTo(Integer value) {
            addCriterion("is_sync_receipt <=", value, "isSyncReceipt");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptIn(List<Integer> values) {
            addCriterion("is_sync_receipt in", values, "isSyncReceipt");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptNotIn(List<Integer> values) {
            addCriterion("is_sync_receipt not in", values, "isSyncReceipt");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptBetween(Integer value1, Integer value2) {
            addCriterion("is_sync_receipt between", value1, value2, "isSyncReceipt");
            return (Criteria) this;
        }

        public Criteria andIsSyncReceiptNotBetween(Integer value1, Integer value2) {
            addCriterion("is_sync_receipt not between", value1, value2, "isSyncReceipt");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetIsNull() {
            addCriterion("tax_rate_set is null");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetIsNotNull() {
            addCriterion("tax_rate_set is not null");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetEqualTo(String value) {
            addCriterion("tax_rate_set =", value, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetNotEqualTo(String value) {
            addCriterion("tax_rate_set <>", value, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetGreaterThan(String value) {
            addCriterion("tax_rate_set >", value, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetGreaterThanOrEqualTo(String value) {
            addCriterion("tax_rate_set >=", value, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetLessThan(String value) {
            addCriterion("tax_rate_set <", value, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetLessThanOrEqualTo(String value) {
            addCriterion("tax_rate_set <=", value, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetLike(String value) {
            addCriterion("tax_rate_set like", value, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetNotLike(String value) {
            addCriterion("tax_rate_set not like", value, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetIn(List<String> values) {
            addCriterion("tax_rate_set in", values, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetNotIn(List<String> values) {
            addCriterion("tax_rate_set not in", values, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetBetween(String value1, String value2) {
            addCriterion("tax_rate_set between", value1, value2, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andTaxRateSetNotBetween(String value1, String value2) {
            addCriterion("tax_rate_set not between", value1, value2, "taxRateSet");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}