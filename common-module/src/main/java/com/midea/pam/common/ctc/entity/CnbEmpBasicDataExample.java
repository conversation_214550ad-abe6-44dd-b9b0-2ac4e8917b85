package com.midea.pam.common.ctc.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CnbEmpBasicDataExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CnbEmpBasicDataExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStatisticDateIsNull() {
            addCriterion("statistic_date is null");
            return (Criteria) this;
        }

        public Criteria andStatisticDateIsNotNull() {
            addCriterion("statistic_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatisticDateEqualTo(String value) {
            addCriterion("statistic_date =", value, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateNotEqualTo(String value) {
            addCriterion("statistic_date <>", value, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateGreaterThan(String value) {
            addCriterion("statistic_date >", value, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateGreaterThanOrEqualTo(String value) {
            addCriterion("statistic_date >=", value, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateLessThan(String value) {
            addCriterion("statistic_date <", value, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateLessThanOrEqualTo(String value) {
            addCriterion("statistic_date <=", value, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateLike(String value) {
            addCriterion("statistic_date like", value, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateNotLike(String value) {
            addCriterion("statistic_date not like", value, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateIn(List<String> values) {
            addCriterion("statistic_date in", values, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateNotIn(List<String> values) {
            addCriterion("statistic_date not in", values, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateBetween(String value1, String value2) {
            addCriterion("statistic_date between", value1, value2, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andStatisticDateNotBetween(String value1, String value2) {
            addCriterion("statistic_date not between", value1, value2, "statisticDate");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andMipNameIsNull() {
            addCriterion("mip_name is null");
            return (Criteria) this;
        }

        public Criteria andMipNameIsNotNull() {
            addCriterion("mip_name is not null");
            return (Criteria) this;
        }

        public Criteria andMipNameEqualTo(String value) {
            addCriterion("mip_name =", value, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameNotEqualTo(String value) {
            addCriterion("mip_name <>", value, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameGreaterThan(String value) {
            addCriterion("mip_name >", value, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameGreaterThanOrEqualTo(String value) {
            addCriterion("mip_name >=", value, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameLessThan(String value) {
            addCriterion("mip_name <", value, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameLessThanOrEqualTo(String value) {
            addCriterion("mip_name <=", value, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameLike(String value) {
            addCriterion("mip_name like", value, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameNotLike(String value) {
            addCriterion("mip_name not like", value, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameIn(List<String> values) {
            addCriterion("mip_name in", values, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameNotIn(List<String> values) {
            addCriterion("mip_name not in", values, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameBetween(String value1, String value2) {
            addCriterion("mip_name between", value1, value2, "mipName");
            return (Criteria) this;
        }

        public Criteria andMipNameNotBetween(String value1, String value2) {
            addCriterion("mip_name not between", value1, value2, "mipName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeIsNull() {
            addCriterion("employee_code is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeIsNotNull() {
            addCriterion("employee_code is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeEqualTo(String value) {
            addCriterion("employee_code =", value, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeNotEqualTo(String value) {
            addCriterion("employee_code <>", value, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeGreaterThan(String value) {
            addCriterion("employee_code >", value, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("employee_code >=", value, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeLessThan(String value) {
            addCriterion("employee_code <", value, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeLessThanOrEqualTo(String value) {
            addCriterion("employee_code <=", value, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeLike(String value) {
            addCriterion("employee_code like", value, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeNotLike(String value) {
            addCriterion("employee_code not like", value, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeIn(List<String> values) {
            addCriterion("employee_code in", values, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeNotIn(List<String> values) {
            addCriterion("employee_code not in", values, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeBetween(String value1, String value2) {
            addCriterion("employee_code between", value1, value2, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeCodeNotBetween(String value1, String value2) {
            addCriterion("employee_code not between", value1, value2, "employeeCode");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdIsNull() {
            addCriterion("full_pay_unit_id is null");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdIsNotNull() {
            addCriterion("full_pay_unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdEqualTo(String value) {
            addCriterion("full_pay_unit_id =", value, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdNotEqualTo(String value) {
            addCriterion("full_pay_unit_id <>", value, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdGreaterThan(String value) {
            addCriterion("full_pay_unit_id >", value, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdGreaterThanOrEqualTo(String value) {
            addCriterion("full_pay_unit_id >=", value, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdLessThan(String value) {
            addCriterion("full_pay_unit_id <", value, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdLessThanOrEqualTo(String value) {
            addCriterion("full_pay_unit_id <=", value, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdLike(String value) {
            addCriterion("full_pay_unit_id like", value, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdNotLike(String value) {
            addCriterion("full_pay_unit_id not like", value, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdIn(List<String> values) {
            addCriterion("full_pay_unit_id in", values, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdNotIn(List<String> values) {
            addCriterion("full_pay_unit_id not in", values, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdBetween(String value1, String value2) {
            addCriterion("full_pay_unit_id between", value1, value2, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andFullPayUnitIdNotBetween(String value1, String value2) {
            addCriterion("full_pay_unit_id not between", value1, value2, "fullPayUnitId");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameIsNull() {
            addCriterion("local_full_unit_name is null");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameIsNotNull() {
            addCriterion("local_full_unit_name is not null");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameEqualTo(String value) {
            addCriterion("local_full_unit_name =", value, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameNotEqualTo(String value) {
            addCriterion("local_full_unit_name <>", value, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameGreaterThan(String value) {
            addCriterion("local_full_unit_name >", value, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameGreaterThanOrEqualTo(String value) {
            addCriterion("local_full_unit_name >=", value, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameLessThan(String value) {
            addCriterion("local_full_unit_name <", value, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameLessThanOrEqualTo(String value) {
            addCriterion("local_full_unit_name <=", value, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameLike(String value) {
            addCriterion("local_full_unit_name like", value, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameNotLike(String value) {
            addCriterion("local_full_unit_name not like", value, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameIn(List<String> values) {
            addCriterion("local_full_unit_name in", values, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameNotIn(List<String> values) {
            addCriterion("local_full_unit_name not in", values, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameBetween(String value1, String value2) {
            addCriterion("local_full_unit_name between", value1, value2, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andLocalFullUnitNameNotBetween(String value1, String value2) {
            addCriterion("local_full_unit_name not between", value1, value2, "localFullUnitName");
            return (Criteria) this;
        }

        public Criteria andHrOuIdIsNull() {
            addCriterion("hr_ou_id is null");
            return (Criteria) this;
        }

        public Criteria andHrOuIdIsNotNull() {
            addCriterion("hr_ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andHrOuIdEqualTo(Long value) {
            addCriterion("hr_ou_id =", value, "hrOuId");
            return (Criteria) this;
        }

        public Criteria andHrOuIdNotEqualTo(Long value) {
            addCriterion("hr_ou_id <>", value, "hrOuId");
            return (Criteria) this;
        }

        public Criteria andHrOuIdGreaterThan(Long value) {
            addCriterion("hr_ou_id >", value, "hrOuId");
            return (Criteria) this;
        }

        public Criteria andHrOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("hr_ou_id >=", value, "hrOuId");
            return (Criteria) this;
        }

        public Criteria andHrOuIdLessThan(Long value) {
            addCriterion("hr_ou_id <", value, "hrOuId");
            return (Criteria) this;
        }

        public Criteria andHrOuIdLessThanOrEqualTo(Long value) {
            addCriterion("hr_ou_id <=", value, "hrOuId");
            return (Criteria) this;
        }

        public Criteria andHrOuIdIn(List<Long> values) {
            addCriterion("hr_ou_id in", values, "hrOuId");
            return (Criteria) this;
        }

        public Criteria andHrOuIdNotIn(List<Long> values) {
            addCriterion("hr_ou_id not in", values, "hrOuId");
            return (Criteria) this;
        }

        public Criteria andHrOuIdBetween(Long value1, Long value2) {
            addCriterion("hr_ou_id between", value1, value2, "hrOuId");
            return (Criteria) this;
        }

        public Criteria andHrOuIdNotBetween(Long value1, Long value2) {
            addCriterion("hr_ou_id not between", value1, value2, "hrOuId");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNull() {
            addCriterion("ou_name is null");
            return (Criteria) this;
        }

        public Criteria andOuNameIsNotNull() {
            addCriterion("ou_name is not null");
            return (Criteria) this;
        }

        public Criteria andOuNameEqualTo(String value) {
            addCriterion("ou_name =", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotEqualTo(String value) {
            addCriterion("ou_name <>", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThan(String value) {
            addCriterion("ou_name >", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameGreaterThanOrEqualTo(String value) {
            addCriterion("ou_name >=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThan(String value) {
            addCriterion("ou_name <", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLessThanOrEqualTo(String value) {
            addCriterion("ou_name <=", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameLike(String value) {
            addCriterion("ou_name like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotLike(String value) {
            addCriterion("ou_name not like", value, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameIn(List<String> values) {
            addCriterion("ou_name in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotIn(List<String> values) {
            addCriterion("ou_name not in", values, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameBetween(String value1, String value2) {
            addCriterion("ou_name between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andOuNameNotBetween(String value1, String value2) {
            addCriterion("ou_name not between", value1, value2, "ouName");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Boolean value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Boolean value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Boolean value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Boolean value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Boolean> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Boolean> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}