package com.midea.pam.common.ctc.dto;

import com.alibaba.fastjson.JSON;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.entity.ProjectWbsBudget;
import com.midea.pam.common.util.FastjsonUtils;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Getter
@Setter
public class ProjectWbsBudgetDto extends ProjectWbsBudget {

    @ApiModelProperty("项目成本系统生成标记")
    private boolean systemFlag = false;

    @ApiModelProperty("汇总展示编码")
    private String summaryCode;

    @ApiModelProperty(value = "wbs编码")
    private String wbsSummaryCode;

    @ApiModelProperty(value = "预算占用")
    private BigDecimal occupyBudget;

    @ApiModelProperty(value = "变更前的预算占用")
    private BigDecimal occupyBudgetOld;

    @ApiModelProperty(value = "是否是wbs项目")
    private Boolean wbsEnabled;

    @ApiModelProperty(value = "变更后预算")
    private BigDecimal afterChangePrice;


    /**
     * 获取格式化后的变更后价格，避免科学计数法显示
     */
    public BigDecimal getAfterChangePrice() {
        if (afterChangePrice != null) {
            // 设置精度为8位小数，去除尾随零
            return afterChangePrice.setScale(8, RoundingMode.HALF_UP).stripTrailingZeros();
        }
        return afterChangePrice;
    }

    /**
     * map转dto
     *
     * @param map
     * @param dynamicFields 动态列
     * @return
     * @throws ParseException 日期转换异常
     */
    public static ProjectWbsBudgetDto map2Dto(Map map, List<WbsDynamicFieldsDto> dynamicFields) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        ProjectWbsBudgetDto result = new ProjectWbsBudgetDto();
        result.setId(MapUtils.getLong(map, "id"));
        result.setProjectId(MapUtils.getLong(map, WbsBudgetFieldConstant.PROJECT_ID));
        result.setProjectCode(MapUtils.getString(map, WbsBudgetFieldConstant.PROJECT_CODE));
        result.setDescription(MapUtils.getString(map, WbsBudgetFieldConstant.DESCRIPTION));
        result.setActivityCode(MapUtils.getString(map, WbsBudgetFieldConstant.ACTIVITY_CODE));
        result.setActivityName(MapUtils.getString(map, WbsBudgetFieldConstant.ACTIVITY_NAME));
        result.setActivityType(MapUtils.getString(map, WbsBudgetFieldConstant.ACTIVITY_TYPE));
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.PRICE))) {
            result.setPrice(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.PRICE)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.BASELINE_COST))) {
            result.setBaselineCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.BASELINE_COST)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.DEMAND_COST))) {
            result.setDemandCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.DEMAND_COST)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.ON_THE_WAY_COST))) {
            result.setOnTheWayCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.ON_THE_WAY_COST)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.INCURRED_COST))) {
            result.setIncurredCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.INCURRED_COST)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.REMAINING_COST))) {
            result.setRemainingCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.REMAINING_COST)));
        }
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST))) {
            result.setChangeAccumulateCost(new BigDecimal(MapUtils.getString(map, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST)));
        }
        result.setParentWbsId(MapUtils.getLong(map, WbsBudgetFieldConstant.PARENT_WBS_ID));
        result.setParentActivityId(MapUtils.getLong(map, WbsBudgetFieldConstant.PARENT_ACTIVITY_ID));
        result.setCreateBy(MapUtils.getLong(map, WbsBudgetFieldConstant.CREATE_BY));
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.CREATE_AT))) {
            result.setCreateAt(simpleDateFormat.parse(MapUtils.getString(map, WbsBudgetFieldConstant.CREATE_AT)));
        }
        result.setUpdateBy(MapUtils.getLong(map, WbsBudgetFieldConstant.UPDATE_BY));
        if (StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.UPDATE_AT))) {
            result.setUpdateAt(simpleDateFormat.parse(MapUtils.getString(map, WbsBudgetFieldConstant.UPDATE_AT)));
        }
        result.setVersion(MapUtils.getLong(map, "version"));
        result.setDeletedFlag(MapUtils.getBoolean(map, "deletedFlag"));

        return result;
    }

    /**
     * 批量map转dto
     *
     * @param list
     * @param dynamicFields 动态列
     * @return
     * @throws ParseException 日期转换异常
     */
    public static List<ProjectWbsBudgetDto> map2DtoBatch(List<Map> list, List<WbsDynamicFieldsDto> dynamicFields) throws ParseException {
        List<ProjectWbsBudgetDto> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (Map map : list) {
                result.add(map2Dto(map, dynamicFields));
            }
        }
        return result;
    }

    /**
     * entity转Map
     *
     * @param entity
     * @return
     */
    public static Map<String, Object> entity2Map(ProjectWbsBudget entity) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map result = FastjsonUtils.toMap(entity);
        if (StringUtils.isNotBlank(entity.getDynamicFields()) && StringUtils.isNotBlank(entity.getDynamicValues())) {
            String[] dynamicFieldArr = entity.getDynamicFields().split(",");
            String[] dynamicValueArr = entity.getDynamicValues().split(",");
            for (int i = 0; i < dynamicFieldArr.length; i++) {
                result.put(dynamicFieldArr[i], dynamicValueArr[i]);
            }
        }
        // 四舍五入金额
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.PRICE, entity.getPrice());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.BASELINE_COST, entity.getBaselineCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.DEMAND_COST, entity.getDemandCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.ON_THE_WAY_COST, entity.getOnTheWayCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.INCURRED_COST, entity.getIncurredCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.REMAINING_COST, entity.getRemainingCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST, entity.getChangeAccumulateCost());
        if (!Objects.isNull(entity.getCreateAt())) {
            result.put(WbsBudgetFieldConstant.CREATE_AT, simpleDateFormat.format(entity.getCreateAt()));
        }
        if (!Objects.isNull(entity.getUpdateAt())) {
            result.put(WbsBudgetFieldConstant.UPDATE_AT, simpleDateFormat.format(entity.getUpdateAt()));
        }
        return result;
    }

    /**
     * 批量entity转Map
     *
     * @param list
     * @return
     */
    public static List<Map<String, Object>> entity2MapBatch(List<ProjectWbsBudget> list) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (ProjectWbsBudget entity : list) {
            result.add(entity2Map(entity));
        }
        return result;
    }

    public static Map<String, Object> dtt2Map(ProjectWbsBudgetDto dto) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map result = FastjsonUtils.toMap(JSON.toJSONString(dto));
        if (StringUtils.isNotBlank(dto.getDynamicFields()) && StringUtils.isNotBlank(dto.getDynamicValues())) {
            String[] dynamicFieldArr = dto.getDynamicFields().split(",");
            String[] dynamicValueArr = dto.getDynamicValues().split(",");
            for (int i = 0; i < dynamicFieldArr.length; i++) {
                result.put(dynamicFieldArr[i], dynamicValueArr[i]);
            }
        }

        // 四舍五入金额
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.PRICE, dto.getPrice());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.BASELINE_COST, dto.getBaselineCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.DEMAND_COST, dto.getDemandCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.ON_THE_WAY_COST, dto.getOnTheWayCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.INCURRED_COST, dto.getIncurredCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.REMAINING_COST, dto.getRemainingCost());
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST, dto.getChangeAccumulateCost());

        if (!Objects.isNull(dto.getCreateAt())) {
            result.put(WbsBudgetFieldConstant.CREATE_AT, simpleDateFormat.format(dto.getCreateAt()));
        }
        if (!Objects.isNull(dto.getUpdateAt())) {
            result.put(WbsBudgetFieldConstant.UPDATE_AT, simpleDateFormat.format(dto.getUpdateAt()));
        }
        return result;
    }

    public static List<Map<String, Object>> dto2MapBatch(List<ProjectWbsBudgetDto> list) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (ProjectWbsBudgetDto dto : list) {
            result.add(dtt2Map(dto));
        }
        return result;
    }

    /**
     * entity与map比较(wbs动态列、activity活动事项)
     *
     * @param entity        wbs预算（已保存）
     * @param map           wbs预算或wbs预算基线
     * @param dynamicFields wbs动态列
     * @return
     */
    public static boolean entityEqulasWbsActivityByMap(ProjectWbsBudget entity, Map<String, Object> map, List<WbsDynamicFieldsDto> dynamicFields) {
        if(null == entity || MapUtils.isEmpty(map) || CollectionUtils.isEmpty(dynamicFields)){
            return false;
        }
        // 校验activity活动事项
        if (!entity.getActivityCode().equals(MapUtils.getString(map, WbsBudgetFieldConstant.ACTIVITY_CODE))) {
            return false;
        }
        // 校验wbs动态列
        if(StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.WBS_FULL_CODE))){
            return entity.getWbsFullCode().equals(MapUtils.getString(map, WbsBudgetFieldConstant.WBS_FULL_CODE));
        }else{
            StringBuilder dynamicField = new StringBuilder();
            for (int i = 0; i < dynamicFields.size(); i++) {
                WbsDynamicFieldsDto dynamic = dynamicFields.get(i);
                dynamicField.append(MapUtils.getString(map, dynamic.getKey()));
                if((i + 1) != dynamicFields.size()) {
                    dynamicField.append("-");
                }
            }
            return entity.getWbsFullCode().equals(dynamicField.toString());
        }
    }
}