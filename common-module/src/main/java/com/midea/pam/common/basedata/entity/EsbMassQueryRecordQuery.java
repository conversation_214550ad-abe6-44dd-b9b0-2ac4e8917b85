package com.midea.pam.common.basedata.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "接口日志查询参数")
public class EsbMassQueryRecordQuery implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;
    
    @ApiModelProperty(value = "每页记录数", example = "10")
    private Integer pageSize = 10;
    
    @ApiModelProperty(value = "ESB流水号")
    private String esbSerialNo;
    
    @ApiModelProperty(value = "业务类型")
    private String businessType;
    
    @ApiModelProperty(value = "状态")
    private Integer status;
    
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "请求参数1")
    private String erpIpP01;

    @ApiModelProperty(value = "请求参数2")
    private String erpIpP02;

    @ApiModelProperty(value = "请求参数3")
    private String erpIpP03;

    @ApiModelProperty(value = "请求参数4")
    private String erpIpP04;
}
