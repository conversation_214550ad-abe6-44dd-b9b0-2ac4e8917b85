package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.util.CacheDataUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "物料采购需求")
public class PurchaseMaterialRequirementDto extends PurchaseMaterialRequirement {

    @ApiModelProperty(value = "物料采购需求id", notes = "采购合同查询使用")
    private Long requirementId;

    @ApiModelProperty(value = "详细设计单据id")
    private Long receiptsId;

    @ApiModelProperty("序号")
    private Integer num;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目编号")
    private String projectNum;

    @ApiModelProperty("项目类型")
    private String projectType;

    @ApiModelProperty("项目状态")
    private String projectStatus;

    @ApiModelProperty("需求预算")
    private BigDecimal demandCost;

    @ApiModelProperty("剩余wbs需求预算占用（外包）")
    private BigDecimal wbsRemainingDemandOsCost;

    @ApiModelProperty("累计采购合同占用金额")
    private BigDecimal contractTotalAmount;

    @ApiModelProperty("状态集合")
    private List<Integer> statusList;

    @ApiModelProperty("未下达量")
    private BigDecimal unreleasedAmount;

    @ApiModelProperty("已下达量")
    private BigDecimal releasedQuantity;

    @ApiModelProperty("订单量")
    private BigDecimal orderQuantity;

    @ApiModelProperty("项目现有量")
    private BigDecimal projectHavedQuantity;

    @ApiModelProperty("非项目现有量")
    private BigDecimal noProjectHavedQuantity;

    @ApiModelProperty(value = "项目业务实体")
    private String projectOuName;

    @ApiModelProperty(value = "项目库存组织id")
    private Long projectOrganizationId;

    @ApiModelProperty(value = "项目库存组织编码")
    private String projectOrganizationCode;

    @ApiModelProperty("项目")
    private ProjectDto projectDto;

    @ApiModelProperty("物料")
    private MaterialDto materialDto;

    @ApiModelProperty("系统建议下达量")
    private BigDecimal systemQuantity;

    @ApiModelProperty("下达采购需求")
    private List<PurchaseMaterialRequirementDto> requirementDtos;

    @ApiModelProperty("订单行详情")
    private List<PurchaseOrderDetailDto> purchaseOrderDetailDtos;

    @ApiModelProperty("业务实体ID")
    private Long ouId;

    @ApiModelProperty("详细设计ID")
    private Long designPlanDetailId;

    /*筛选条件*/
    private Integer pageNum;

    private Integer pageSize;

    @ApiModelProperty(value = "模糊物料编码")
    private String fuzzyErpCode;

    @ApiModelProperty(value = "模糊物料描述")
    private String fuzzyMaterielDescr;

    @ApiModelProperty(value = "模糊PAM编码")
    private String fuzzyPamCode;

    @ApiModelProperty(value = "模糊项目名称")
    private String fuzzyProjectName;

    @ApiModelProperty(value = "模糊项目编号")
    private String fuzzyProjectNum;

    @ApiModelProperty(value = "业务实体id(多选)")
    private String projectOuId;

    @ApiModelProperty(value = "交货开始日期")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "交货开始日期")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "状态(多选)")
    private String[] manyStatus;

    @ApiModelProperty(value = "业务实体(多选)")
    private String[] manyProjectOuName;

    @ApiModelProperty(value = "业务实体id(多选)")
    private String[] manyProjectOuId;

    @ApiModelProperty(value = "状态")
    private String statusStr;

    @ApiModelProperty(value = "发布日期")
    private Date publishTime;

    @ApiModelProperty(value = "发布开始日期")
    private Date publishStartTime;

    @ApiModelProperty(value = "发布结束日期")
    private Date publishEndTime;

    @ApiModelProperty(value = "物料id")
    private Long materialId;

    @ApiModelProperty(value = "物料中类")
    private String codingMiddleclass;

    @ApiModelProperty(value = "物料小类")
    private String materialType;

    @ApiModelProperty(value = "图号/型号")
    private String model;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "图号")
    private String figureNumber;

    @ApiModelProperty(value = "更新开始日期")
    private Date updateStartDate;

    @ApiModelProperty(value = "更新结束日期")
    private Date updateEndDate;

    private List<Long> ouList;

    @ApiModelProperty(value = "批准供应商数量(erp同步或创建时统计)")
    private Integer approvedSupplierNumber;

    @ApiModelProperty(value = "预算占用金额")
    private BigDecimal budgetOccupiedAmountTotal;

    @ApiModelProperty(value = "预算占用金额(wbs)")
    private BigDecimal budgetOccupiedAmount;

    @ApiModelProperty(value = "剩余需求预算占用金额(wbs)")
    private BigDecimal remainMoney;

    @ApiModelProperty(value = "物料是否同步wbs")
    private Integer erpCodeIs;

    @ApiModelProperty(value = "库存组织id")
    private Long orgId;

    @ApiModelProperty(value = "品牌/型号拼接参数")
    private String modelParam;

    @ApiModelProperty(value = "需求类型")
    private String requirementTypeStr;
    private List<String> requirementTypeList;

    private List<Long> requirementIdList;

    private List<String> erpCodeList;

    @ApiModelProperty(value = "角色名称")
    private String roleName;
    @ApiModelProperty(value = "投入开始时间")
    private Date startDate;
    @ApiModelProperty(value = "投入结束时间")
    private Date endDate;

    @ApiModelProperty(value = "是否点工(0：否，1：是)")
    private Integer ifHro;

    @ApiModelProperty(value = "采购合同签订数量")
    private BigDecimal number;

    @ApiModelProperty(value = "单价（不含税）-原币")
    private BigDecimal price;

    @ApiModelProperty(value = "库存组织代码")
    private String organizationCode;

    @ApiModelProperty(value = "关闭数量")
    private BigDecimal closeNum;

    @ApiModelProperty(value = "有效定价数量")
    private Integer validPriceQuantity;

    private List<Long> idList;

    @ApiModelProperty(value = "关闭或打开数量")
    private BigDecimal closedOrOpenQuantity;

    @ApiModelProperty(value = "需求在当前用户待下达订单里的数量")
    private BigDecimal existNum;

    @ApiModelProperty(value = "wbs需求预算占用")
    private BigDecimal wbsDemandTotalCost;

    @ApiModelProperty(value = "页面类型：0-通用页面，1-新页面")
    private Integer webType;

    @ApiModelProperty(value = "物料采购需求ID集合")
    private List<String> ids;


    public Integer getPageNum() {
        if (pageNum == null) {
            return 1;
        }
        return pageNum;
    }

    public Integer getPageSize() {
        if (pageSize == null) {
            return 20;
        }
        return pageSize;
    }

    public void setDemandCost(BigDecimal demandCost) {
        this.demandCost = null == demandCost ? BigDecimal.ZERO
                : new BigDecimal(demandCost.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
    }

    public void setWbsRemainingDemandOsCost(BigDecimal wbsRemainingDemandOsCost) {
        this.wbsRemainingDemandOsCost = null == wbsRemainingDemandOsCost ? BigDecimal.ZERO
                : new BigDecimal(wbsRemainingDemandOsCost.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
    }

    public void setContractTotalAmount(BigDecimal contractTotalAmount) {
        this.contractTotalAmount = null == contractTotalAmount ? BigDecimal.ZERO
                : new BigDecimal(contractTotalAmount.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
    }

    public void setClosedAmount(BigDecimal closedAmount) {
        super.setClosedAmount(null == closedAmount ? BigDecimal.ZERO
                : new BigDecimal(closedAmount.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
    }

    public void setWbsDemandOsCost(BigDecimal wbsDemandOsCost) {
        super.setWbsDemandOsCost(null == wbsDemandOsCost ? BigDecimal.ZERO
                : new BigDecimal(wbsDemandOsCost.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
    }

    public void setUnreleasedAmount(BigDecimal unreleasedAmount) {
        this.unreleasedAmount = null == unreleasedAmount ? BigDecimal.ZERO :
                new BigDecimal(unreleasedAmount.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
    }

    public void setReleasedQuantity(BigDecimal releasedQuantity) {
        this.releasedQuantity = null == releasedQuantity ? BigDecimal.ZERO :
                new BigDecimal(releasedQuantity.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
    }

    public void setOrderQuantity(BigDecimal orderQuantity) {
        this.orderQuantity = null == orderQuantity ? BigDecimal.ZERO :
                new BigDecimal(orderQuantity.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
    }

    public void setProjectHavedQuantity(BigDecimal projectHavedQuantity) {
        this.projectHavedQuantity = null == projectHavedQuantity ? BigDecimal.ZERO
                : new BigDecimal(projectHavedQuantity.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
    }

    public void setClosedQuantity(BigDecimal closedQuantity) {
        super.setClosedQuantity(null == closedQuantity ? BigDecimal.ZERO :
                new BigDecimal(closedQuantity.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
    }

    public void setNeedTotal(BigDecimal needTotal) {
        super.setNeedTotal(null == needTotal ? BigDecimal.ZERO
                : new BigDecimal(needTotal.setScale(6, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }


    //导出时自动填充数据
    private String requirementTypeExport;

    @Override
    public Integer getRequirementType() {
        if (super.getRequirementType() != null) {
            DictDto dictDto = CacheDataUtils.findDictByTypeAndCode(DictType.DESIGN_REQUIREMENT_TYPE.code(), super.getRequirementType().toString());
            if (dictDto != null) {
                this.setRequirementTypeExport(dictDto.getName());
            }
        }
        return super.getRequirementType();
    }

}