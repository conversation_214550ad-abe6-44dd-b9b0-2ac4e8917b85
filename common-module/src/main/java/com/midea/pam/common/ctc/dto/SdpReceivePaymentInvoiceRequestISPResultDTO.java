package com.midea.pam.common.ctc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SdpReceivePaymentInvoiceRequestISPResultDTO {

    @ApiModelProperty("isp开票申请ID")
    private Long ispInvoiceApplyId;

    @ApiModelProperty("项目经理MIP")
    private String managerMip;

    @ApiModelProperty("采购跟进人Mip")
    private String purchasingFollowerMip;

    @ApiModelProperty("项目财务")
    private String projectFinance;

    @ApiModelProperty("开票总额标识 1:开票总额小于等于50W,2:开票总额大于50W")
    private Integer totalInvoiceAmountFlag;

    @ApiModelProperty("单位ID")
    private Long unitId;

    private Boolean isSuccess ;

    @ApiModelProperty("重新发起流程")
    private Boolean reSubmitFlow = false;

    @ApiModelProperty("废弃流程")
    private Boolean abandonFlow = false;

    private String errorMsg ;

    private String invoiceNumber;

    private String formUrl = "gscPaymentInvoiceIspApp";

    private String defaultSubmitUserName;
}
