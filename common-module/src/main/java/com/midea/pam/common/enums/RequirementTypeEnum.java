package com.midea.pam.common.enums;

import lombok.Getter;

/**
 * 详细设计单据类型
 */
@Getter
public enum RequirementTypeEnum {

    DESIGN_PLAN_SUBMIT(1, "详细设计发布"),
    DESIGN_PLAN_CHANGE(2, "详细设计变更"),
    REQUIREMENT_PUBLISH(3, "需求发布"),
    RECEIPTS_REQUIREMENT_CHANGE(4, "需求预算变更"),
    WBS_CHANGE(5, "WBS变更"),
    ;

    private int code;
    private String name;

    RequirementTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RequirementTypeEnum getByCode(int code) {
        for (RequirementTypeEnum value : RequirementTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        for (RequirementTypeEnum value : RequirementTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getName();
            }
        }
        return null;
    }

    public static RequirementTypeEnum getEnumByCode(Integer code) {
        for (RequirementTypeEnum value : RequirementTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
