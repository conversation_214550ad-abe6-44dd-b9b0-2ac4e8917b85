package com.midea.pam.common.ctc.dto;

import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;

@ApiModel(value = "MilepostDesignPlanSubmitRecordDto", description = "里程碑详细设计方案提交记录")
@Getter
@Setter
public class MilepostDesignPlanSubmitRecordDto extends MilepostDesignPlanSubmitRecord {

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("里程碑名称")
    private String milepostName;

    @ApiModelProperty("里程碑阶段")
    private Integer orderNum;

    @ApiModelProperty("发布人")
    private String publisherName;

    @ApiModelProperty("设计信息")
    private List<MilepostDesignPlanDetailDto> designPlanDetailDtos;

    private Boolean statusIsNull;

    private Boolean uploadPathIdIsNull;


    @ApiModelProperty("是否已经发布暂存")
    private Boolean orStorage;

    @ApiModelProperty("原因后缀")
    private String reasonSuffix;

    @ApiModelProperty("详细设计单据")
    private ProjectWbsReceiptsDto projectWbsReceiptsDto;

    @ApiModelProperty(value = "上传路径id(模组id)，多个逗号间隔")
    private String uploadPathIds;

    private Set<Long> uploadPathIdSet;

    private Long formInstanceId;

    private String fdInstanceId;

    private String formUrl;

    private String eventName;

    private String handlerId;

    private Long companyId;

    private Long createUserId;
}