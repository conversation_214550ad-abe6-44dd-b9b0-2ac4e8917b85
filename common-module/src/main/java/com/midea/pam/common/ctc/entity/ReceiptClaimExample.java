package com.midea.pam.common.ctc.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReceiptClaimExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ReceiptClaimExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeIsNull() {
            addCriterion("source_system_code is null");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeIsNotNull() {
            addCriterion("source_system_code is not null");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeEqualTo(String value) {
            addCriterion("source_system_code =", value, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeNotEqualTo(String value) {
            addCriterion("source_system_code <>", value, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeGreaterThan(String value) {
            addCriterion("source_system_code >", value, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeGreaterThanOrEqualTo(String value) {
            addCriterion("source_system_code >=", value, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeLessThan(String value) {
            addCriterion("source_system_code <", value, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeLessThanOrEqualTo(String value) {
            addCriterion("source_system_code <=", value, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeLike(String value) {
            addCriterion("source_system_code like", value, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeNotLike(String value) {
            addCriterion("source_system_code not like", value, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeIn(List<String> values) {
            addCriterion("source_system_code in", values, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeNotIn(List<String> values) {
            addCriterion("source_system_code not in", values, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeBetween(String value1, String value2) {
            addCriterion("source_system_code between", value1, value2, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andSourceSystemCodeNotBetween(String value1, String value2) {
            addCriterion("source_system_code not between", value1, value2, "sourceSystemCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeIsNull() {
            addCriterion("cash_receipt_code is null");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeIsNotNull() {
            addCriterion("cash_receipt_code is not null");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeEqualTo(String value) {
            addCriterion("cash_receipt_code =", value, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeNotEqualTo(String value) {
            addCriterion("cash_receipt_code <>", value, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeGreaterThan(String value) {
            addCriterion("cash_receipt_code >", value, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cash_receipt_code >=", value, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeLessThan(String value) {
            addCriterion("cash_receipt_code <", value, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeLessThanOrEqualTo(String value) {
            addCriterion("cash_receipt_code <=", value, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeLike(String value) {
            addCriterion("cash_receipt_code like", value, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeNotLike(String value) {
            addCriterion("cash_receipt_code not like", value, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeIn(List<String> values) {
            addCriterion("cash_receipt_code in", values, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeNotIn(List<String> values) {
            addCriterion("cash_receipt_code not in", values, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeBetween(String value1, String value2) {
            addCriterion("cash_receipt_code between", value1, value2, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashReceiptCodeNotBetween(String value1, String value2) {
            addCriterion("cash_receipt_code not between", value1, value2, "cashReceiptCode");
            return (Criteria) this;
        }

        public Criteria andCashStatusIsNull() {
            addCriterion("cash_status is null");
            return (Criteria) this;
        }

        public Criteria andCashStatusIsNotNull() {
            addCriterion("cash_status is not null");
            return (Criteria) this;
        }

        public Criteria andCashStatusEqualTo(Integer value) {
            addCriterion("cash_status =", value, "cashStatus");
            return (Criteria) this;
        }

        public Criteria andCashStatusNotEqualTo(Integer value) {
            addCriterion("cash_status <>", value, "cashStatus");
            return (Criteria) this;
        }

        public Criteria andCashStatusGreaterThan(Integer value) {
            addCriterion("cash_status >", value, "cashStatus");
            return (Criteria) this;
        }

        public Criteria andCashStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("cash_status >=", value, "cashStatus");
            return (Criteria) this;
        }

        public Criteria andCashStatusLessThan(Integer value) {
            addCriterion("cash_status <", value, "cashStatus");
            return (Criteria) this;
        }

        public Criteria andCashStatusLessThanOrEqualTo(Integer value) {
            addCriterion("cash_status <=", value, "cashStatus");
            return (Criteria) this;
        }

        public Criteria andCashStatusIn(List<Integer> values) {
            addCriterion("cash_status in", values, "cashStatus");
            return (Criteria) this;
        }

        public Criteria andCashStatusNotIn(List<Integer> values) {
            addCriterion("cash_status not in", values, "cashStatus");
            return (Criteria) this;
        }

        public Criteria andCashStatusBetween(Integer value1, Integer value2) {
            addCriterion("cash_status between", value1, value2, "cashStatus");
            return (Criteria) this;
        }

        public Criteria andCashStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("cash_status not between", value1, value2, "cashStatus");
            return (Criteria) this;
        }

        public Criteria andDivideStatusIsNull() {
            addCriterion("divide_status is null");
            return (Criteria) this;
        }

        public Criteria andDivideStatusIsNotNull() {
            addCriterion("divide_status is not null");
            return (Criteria) this;
        }

        public Criteria andDivideStatusEqualTo(Integer value) {
            addCriterion("divide_status =", value, "divideStatus");
            return (Criteria) this;
        }

        public Criteria andDivideStatusNotEqualTo(Integer value) {
            addCriterion("divide_status <>", value, "divideStatus");
            return (Criteria) this;
        }

        public Criteria andDivideStatusGreaterThan(Integer value) {
            addCriterion("divide_status >", value, "divideStatus");
            return (Criteria) this;
        }

        public Criteria andDivideStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("divide_status >=", value, "divideStatus");
            return (Criteria) this;
        }

        public Criteria andDivideStatusLessThan(Integer value) {
            addCriterion("divide_status <", value, "divideStatus");
            return (Criteria) this;
        }

        public Criteria andDivideStatusLessThanOrEqualTo(Integer value) {
            addCriterion("divide_status <=", value, "divideStatus");
            return (Criteria) this;
        }

        public Criteria andDivideStatusIn(List<Integer> values) {
            addCriterion("divide_status in", values, "divideStatus");
            return (Criteria) this;
        }

        public Criteria andDivideStatusNotIn(List<Integer> values) {
            addCriterion("divide_status not in", values, "divideStatus");
            return (Criteria) this;
        }

        public Criteria andDivideStatusBetween(Integer value1, Integer value2) {
            addCriterion("divide_status between", value1, value2, "divideStatus");
            return (Criteria) this;
        }

        public Criteria andDivideStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("divide_status not between", value1, value2, "divideStatus");
            return (Criteria) this;
        }

        public Criteria andPayNameIsNull() {
            addCriterion("pay_name is null");
            return (Criteria) this;
        }

        public Criteria andPayNameIsNotNull() {
            addCriterion("pay_name is not null");
            return (Criteria) this;
        }

        public Criteria andPayNameEqualTo(String value) {
            addCriterion("pay_name =", value, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameNotEqualTo(String value) {
            addCriterion("pay_name <>", value, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameGreaterThan(String value) {
            addCriterion("pay_name >", value, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameGreaterThanOrEqualTo(String value) {
            addCriterion("pay_name >=", value, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameLessThan(String value) {
            addCriterion("pay_name <", value, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameLessThanOrEqualTo(String value) {
            addCriterion("pay_name <=", value, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameLike(String value) {
            addCriterion("pay_name like", value, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameNotLike(String value) {
            addCriterion("pay_name not like", value, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameIn(List<String> values) {
            addCriterion("pay_name in", values, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameNotIn(List<String> values) {
            addCriterion("pay_name not in", values, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameBetween(String value1, String value2) {
            addCriterion("pay_name between", value1, value2, "payName");
            return (Criteria) this;
        }

        public Criteria andPayNameNotBetween(String value1, String value2) {
            addCriterion("pay_name not between", value1, value2, "payName");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeIsNull() {
            addCriterion("pay_bank_code is null");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeIsNotNull() {
            addCriterion("pay_bank_code is not null");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeEqualTo(String value) {
            addCriterion("pay_bank_code =", value, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeNotEqualTo(String value) {
            addCriterion("pay_bank_code <>", value, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeGreaterThan(String value) {
            addCriterion("pay_bank_code >", value, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pay_bank_code >=", value, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeLessThan(String value) {
            addCriterion("pay_bank_code <", value, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeLessThanOrEqualTo(String value) {
            addCriterion("pay_bank_code <=", value, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeLike(String value) {
            addCriterion("pay_bank_code like", value, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeNotLike(String value) {
            addCriterion("pay_bank_code not like", value, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeIn(List<String> values) {
            addCriterion("pay_bank_code in", values, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeNotIn(List<String> values) {
            addCriterion("pay_bank_code not in", values, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeBetween(String value1, String value2) {
            addCriterion("pay_bank_code between", value1, value2, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankCodeNotBetween(String value1, String value2) {
            addCriterion("pay_bank_code not between", value1, value2, "payBankCode");
            return (Criteria) this;
        }

        public Criteria andPayBankNameIsNull() {
            addCriterion("pay_bank_name is null");
            return (Criteria) this;
        }

        public Criteria andPayBankNameIsNotNull() {
            addCriterion("pay_bank_name is not null");
            return (Criteria) this;
        }

        public Criteria andPayBankNameEqualTo(String value) {
            addCriterion("pay_bank_name =", value, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameNotEqualTo(String value) {
            addCriterion("pay_bank_name <>", value, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameGreaterThan(String value) {
            addCriterion("pay_bank_name >", value, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameGreaterThanOrEqualTo(String value) {
            addCriterion("pay_bank_name >=", value, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameLessThan(String value) {
            addCriterion("pay_bank_name <", value, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameLessThanOrEqualTo(String value) {
            addCriterion("pay_bank_name <=", value, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameLike(String value) {
            addCriterion("pay_bank_name like", value, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameNotLike(String value) {
            addCriterion("pay_bank_name not like", value, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameIn(List<String> values) {
            addCriterion("pay_bank_name in", values, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameNotIn(List<String> values) {
            addCriterion("pay_bank_name not in", values, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameBetween(String value1, String value2) {
            addCriterion("pay_bank_name between", value1, value2, "payBankName");
            return (Criteria) this;
        }

        public Criteria andPayBankNameNotBetween(String value1, String value2) {
            addCriterion("pay_bank_name not between", value1, value2, "payBankName");
            return (Criteria) this;
        }

        public Criteria andBillCodeIsNull() {
            addCriterion("bill_code is null");
            return (Criteria) this;
        }

        public Criteria andBillCodeIsNotNull() {
            addCriterion("bill_code is not null");
            return (Criteria) this;
        }

        public Criteria andBillCodeEqualTo(String value) {
            addCriterion("bill_code =", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotEqualTo(String value) {
            addCriterion("bill_code <>", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeGreaterThan(String value) {
            addCriterion("bill_code >", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bill_code >=", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeLessThan(String value) {
            addCriterion("bill_code <", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeLessThanOrEqualTo(String value) {
            addCriterion("bill_code <=", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeLike(String value) {
            addCriterion("bill_code like", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotLike(String value) {
            addCriterion("bill_code not like", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeIn(List<String> values) {
            addCriterion("bill_code in", values, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotIn(List<String> values) {
            addCriterion("bill_code not in", values, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeBetween(String value1, String value2) {
            addCriterion("bill_code between", value1, value2, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotBetween(String value1, String value2) {
            addCriterion("bill_code not between", value1, value2, "billCode");
            return (Criteria) this;
        }

        public Criteria andPayAmountIsNull() {
            addCriterion("pay_amount is null");
            return (Criteria) this;
        }

        public Criteria andPayAmountIsNotNull() {
            addCriterion("pay_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPayAmountEqualTo(BigDecimal value) {
            addCriterion("pay_amount =", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountNotEqualTo(BigDecimal value) {
            addCriterion("pay_amount <>", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountGreaterThan(BigDecimal value) {
            addCriterion("pay_amount >", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("pay_amount >=", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountLessThan(BigDecimal value) {
            addCriterion("pay_amount <", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("pay_amount <=", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountIn(List<BigDecimal> values) {
            addCriterion("pay_amount in", values, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountNotIn(List<BigDecimal> values) {
            addCriterion("pay_amount not in", values, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pay_amount between", value1, value2, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pay_amount not between", value1, value2, "payAmount");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andBillTypeIsNull() {
            addCriterion("bill_type is null");
            return (Criteria) this;
        }

        public Criteria andBillTypeIsNotNull() {
            addCriterion("bill_type is not null");
            return (Criteria) this;
        }

        public Criteria andBillTypeEqualTo(String value) {
            addCriterion("bill_type =", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotEqualTo(String value) {
            addCriterion("bill_type <>", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeGreaterThan(String value) {
            addCriterion("bill_type >", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bill_type >=", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLessThan(String value) {
            addCriterion("bill_type <", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLessThanOrEqualTo(String value) {
            addCriterion("bill_type <=", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLike(String value) {
            addCriterion("bill_type like", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotLike(String value) {
            addCriterion("bill_type not like", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeIn(List<String> values) {
            addCriterion("bill_type in", values, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotIn(List<String> values) {
            addCriterion("bill_type not in", values, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeBetween(String value1, String value2) {
            addCriterion("bill_type between", value1, value2, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotBetween(String value1, String value2) {
            addCriterion("bill_type not between", value1, value2, "billType");
            return (Criteria) this;
        }

        public Criteria andSettleWayIsNull() {
            addCriterion("settle_way is null");
            return (Criteria) this;
        }

        public Criteria andSettleWayIsNotNull() {
            addCriterion("settle_way is not null");
            return (Criteria) this;
        }

        public Criteria andSettleWayEqualTo(String value) {
            addCriterion("settle_way =", value, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayNotEqualTo(String value) {
            addCriterion("settle_way <>", value, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayGreaterThan(String value) {
            addCriterion("settle_way >", value, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayGreaterThanOrEqualTo(String value) {
            addCriterion("settle_way >=", value, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayLessThan(String value) {
            addCriterion("settle_way <", value, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayLessThanOrEqualTo(String value) {
            addCriterion("settle_way <=", value, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayLike(String value) {
            addCriterion("settle_way like", value, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayNotLike(String value) {
            addCriterion("settle_way not like", value, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayIn(List<String> values) {
            addCriterion("settle_way in", values, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayNotIn(List<String> values) {
            addCriterion("settle_way not in", values, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayBetween(String value1, String value2) {
            addCriterion("settle_way between", value1, value2, "settleWay");
            return (Criteria) this;
        }

        public Criteria andSettleWayNotBetween(String value1, String value2) {
            addCriterion("settle_way not between", value1, value2, "settleWay");
            return (Criteria) this;
        }

        public Criteria andRecMethodIsNull() {
            addCriterion("rec_method is null");
            return (Criteria) this;
        }

        public Criteria andRecMethodIsNotNull() {
            addCriterion("rec_method is not null");
            return (Criteria) this;
        }

        public Criteria andRecMethodEqualTo(String value) {
            addCriterion("rec_method =", value, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodNotEqualTo(String value) {
            addCriterion("rec_method <>", value, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodGreaterThan(String value) {
            addCriterion("rec_method >", value, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodGreaterThanOrEqualTo(String value) {
            addCriterion("rec_method >=", value, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodLessThan(String value) {
            addCriterion("rec_method <", value, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodLessThanOrEqualTo(String value) {
            addCriterion("rec_method <=", value, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodLike(String value) {
            addCriterion("rec_method like", value, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodNotLike(String value) {
            addCriterion("rec_method not like", value, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodIn(List<String> values) {
            addCriterion("rec_method in", values, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodNotIn(List<String> values) {
            addCriterion("rec_method not in", values, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodBetween(String value1, String value2) {
            addCriterion("rec_method between", value1, value2, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecMethodNotBetween(String value1, String value2) {
            addCriterion("rec_method not between", value1, value2, "recMethod");
            return (Criteria) this;
        }

        public Criteria andRecBankIdIsNull() {
            addCriterion("rec_bank_id is null");
            return (Criteria) this;
        }

        public Criteria andRecBankIdIsNotNull() {
            addCriterion("rec_bank_id is not null");
            return (Criteria) this;
        }

        public Criteria andRecBankIdEqualTo(Long value) {
            addCriterion("rec_bank_id =", value, "recBankId");
            return (Criteria) this;
        }

        public Criteria andRecBankIdNotEqualTo(Long value) {
            addCriterion("rec_bank_id <>", value, "recBankId");
            return (Criteria) this;
        }

        public Criteria andRecBankIdGreaterThan(Long value) {
            addCriterion("rec_bank_id >", value, "recBankId");
            return (Criteria) this;
        }

        public Criteria andRecBankIdGreaterThanOrEqualTo(Long value) {
            addCriterion("rec_bank_id >=", value, "recBankId");
            return (Criteria) this;
        }

        public Criteria andRecBankIdLessThan(Long value) {
            addCriterion("rec_bank_id <", value, "recBankId");
            return (Criteria) this;
        }

        public Criteria andRecBankIdLessThanOrEqualTo(Long value) {
            addCriterion("rec_bank_id <=", value, "recBankId");
            return (Criteria) this;
        }

        public Criteria andRecBankIdIn(List<Long> values) {
            addCriterion("rec_bank_id in", values, "recBankId");
            return (Criteria) this;
        }

        public Criteria andRecBankIdNotIn(List<Long> values) {
            addCriterion("rec_bank_id not in", values, "recBankId");
            return (Criteria) this;
        }

        public Criteria andRecBankIdBetween(Long value1, Long value2) {
            addCriterion("rec_bank_id between", value1, value2, "recBankId");
            return (Criteria) this;
        }

        public Criteria andRecBankIdNotBetween(Long value1, Long value2) {
            addCriterion("rec_bank_id not between", value1, value2, "recBankId");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeIsNull() {
            addCriterion("rec_bank_code is null");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeIsNotNull() {
            addCriterion("rec_bank_code is not null");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeEqualTo(String value) {
            addCriterion("rec_bank_code =", value, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeNotEqualTo(String value) {
            addCriterion("rec_bank_code <>", value, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeGreaterThan(String value) {
            addCriterion("rec_bank_code >", value, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeGreaterThanOrEqualTo(String value) {
            addCriterion("rec_bank_code >=", value, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeLessThan(String value) {
            addCriterion("rec_bank_code <", value, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeLessThanOrEqualTo(String value) {
            addCriterion("rec_bank_code <=", value, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeLike(String value) {
            addCriterion("rec_bank_code like", value, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeNotLike(String value) {
            addCriterion("rec_bank_code not like", value, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeIn(List<String> values) {
            addCriterion("rec_bank_code in", values, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeNotIn(List<String> values) {
            addCriterion("rec_bank_code not in", values, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeBetween(String value1, String value2) {
            addCriterion("rec_bank_code between", value1, value2, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecBankCodeNotBetween(String value1, String value2) {
            addCriterion("rec_bank_code not between", value1, value2, "recBankCode");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoIsNull() {
            addCriterion("rec_account_no is null");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoIsNotNull() {
            addCriterion("rec_account_no is not null");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoEqualTo(String value) {
            addCriterion("rec_account_no =", value, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoNotEqualTo(String value) {
            addCriterion("rec_account_no <>", value, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoGreaterThan(String value) {
            addCriterion("rec_account_no >", value, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoGreaterThanOrEqualTo(String value) {
            addCriterion("rec_account_no >=", value, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoLessThan(String value) {
            addCriterion("rec_account_no <", value, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoLessThanOrEqualTo(String value) {
            addCriterion("rec_account_no <=", value, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoLike(String value) {
            addCriterion("rec_account_no like", value, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoNotLike(String value) {
            addCriterion("rec_account_no not like", value, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoIn(List<String> values) {
            addCriterion("rec_account_no in", values, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoNotIn(List<String> values) {
            addCriterion("rec_account_no not in", values, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoBetween(String value1, String value2) {
            addCriterion("rec_account_no between", value1, value2, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecAccountNoNotBetween(String value1, String value2) {
            addCriterion("rec_account_no not between", value1, value2, "recAccountNo");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameIsNull() {
            addCriterion("rec_org_name is null");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameIsNotNull() {
            addCriterion("rec_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameEqualTo(String value) {
            addCriterion("rec_org_name =", value, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameNotEqualTo(String value) {
            addCriterion("rec_org_name <>", value, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameGreaterThan(String value) {
            addCriterion("rec_org_name >", value, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("rec_org_name >=", value, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameLessThan(String value) {
            addCriterion("rec_org_name <", value, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameLessThanOrEqualTo(String value) {
            addCriterion("rec_org_name <=", value, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameLike(String value) {
            addCriterion("rec_org_name like", value, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameNotLike(String value) {
            addCriterion("rec_org_name not like", value, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameIn(List<String> values) {
            addCriterion("rec_org_name in", values, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameNotIn(List<String> values) {
            addCriterion("rec_org_name not in", values, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameBetween(String value1, String value2) {
            addCriterion("rec_org_name between", value1, value2, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgNameNotBetween(String value1, String value2) {
            addCriterion("rec_org_name not between", value1, value2, "recOrgName");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeIsNull() {
            addCriterion("rec_org_code is null");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeIsNotNull() {
            addCriterion("rec_org_code is not null");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeEqualTo(String value) {
            addCriterion("rec_org_code =", value, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeNotEqualTo(String value) {
            addCriterion("rec_org_code <>", value, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeGreaterThan(String value) {
            addCriterion("rec_org_code >", value, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeGreaterThanOrEqualTo(String value) {
            addCriterion("rec_org_code >=", value, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeLessThan(String value) {
            addCriterion("rec_org_code <", value, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeLessThanOrEqualTo(String value) {
            addCriterion("rec_org_code <=", value, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeLike(String value) {
            addCriterion("rec_org_code like", value, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeNotLike(String value) {
            addCriterion("rec_org_code not like", value, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeIn(List<String> values) {
            addCriterion("rec_org_code in", values, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeNotIn(List<String> values) {
            addCriterion("rec_org_code not in", values, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeBetween(String value1, String value2) {
            addCriterion("rec_org_code between", value1, value2, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andRecOrgCodeNotBetween(String value1, String value2) {
            addCriterion("rec_org_code not between", value1, value2, "recOrgCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeIsNull() {
            addCriterion("budget_item_code is null");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeIsNotNull() {
            addCriterion("budget_item_code is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeEqualTo(String value) {
            addCriterion("budget_item_code =", value, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeNotEqualTo(String value) {
            addCriterion("budget_item_code <>", value, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeGreaterThan(String value) {
            addCriterion("budget_item_code >", value, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeGreaterThanOrEqualTo(String value) {
            addCriterion("budget_item_code >=", value, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeLessThan(String value) {
            addCriterion("budget_item_code <", value, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeLessThanOrEqualTo(String value) {
            addCriterion("budget_item_code <=", value, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeLike(String value) {
            addCriterion("budget_item_code like", value, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeNotLike(String value) {
            addCriterion("budget_item_code not like", value, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeIn(List<String> values) {
            addCriterion("budget_item_code in", values, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeNotIn(List<String> values) {
            addCriterion("budget_item_code not in", values, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeBetween(String value1, String value2) {
            addCriterion("budget_item_code between", value1, value2, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andBudgetItemCodeNotBetween(String value1, String value2) {
            addCriterion("budget_item_code not between", value1, value2, "budgetItemCode");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNull() {
            addCriterion("ou_id is null");
            return (Criteria) this;
        }

        public Criteria andOuIdIsNotNull() {
            addCriterion("ou_id is not null");
            return (Criteria) this;
        }

        public Criteria andOuIdEqualTo(Long value) {
            addCriterion("ou_id =", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotEqualTo(Long value) {
            addCriterion("ou_id <>", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThan(Long value) {
            addCriterion("ou_id >", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ou_id >=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThan(Long value) {
            addCriterion("ou_id <", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdLessThanOrEqualTo(Long value) {
            addCriterion("ou_id <=", value, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdIn(List<Long> values) {
            addCriterion("ou_id in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotIn(List<Long> values) {
            addCriterion("ou_id not in", values, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdBetween(Long value1, Long value2) {
            addCriterion("ou_id between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andOuIdNotBetween(Long value1, Long value2) {
            addCriterion("ou_id not between", value1, value2, "ouId");
            return (Criteria) this;
        }

        public Criteria andPayDateIsNull() {
            addCriterion("pay_date is null");
            return (Criteria) this;
        }

        public Criteria andPayDateIsNotNull() {
            addCriterion("pay_date is not null");
            return (Criteria) this;
        }

        public Criteria andPayDateEqualTo(Date value) {
            addCriterion("pay_date =", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateNotEqualTo(Date value) {
            addCriterion("pay_date <>", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateGreaterThan(Date value) {
            addCriterion("pay_date >", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateGreaterThanOrEqualTo(Date value) {
            addCriterion("pay_date >=", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateLessThan(Date value) {
            addCriterion("pay_date <", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateLessThanOrEqualTo(Date value) {
            addCriterion("pay_date <=", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateIn(List<Date> values) {
            addCriterion("pay_date in", values, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateNotIn(List<Date> values) {
            addCriterion("pay_date not in", values, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateBetween(Date value1, Date value2) {
            addCriterion("pay_date between", value1, value2, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateNotBetween(Date value1, Date value2) {
            addCriterion("pay_date not between", value1, value2, "payDate");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andConntransCodeIsNull() {
            addCriterion("conntrans_code is null");
            return (Criteria) this;
        }

        public Criteria andConntransCodeIsNotNull() {
            addCriterion("conntrans_code is not null");
            return (Criteria) this;
        }

        public Criteria andConntransCodeEqualTo(String value) {
            addCriterion("conntrans_code =", value, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeNotEqualTo(String value) {
            addCriterion("conntrans_code <>", value, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeGreaterThan(String value) {
            addCriterion("conntrans_code >", value, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeGreaterThanOrEqualTo(String value) {
            addCriterion("conntrans_code >=", value, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeLessThan(String value) {
            addCriterion("conntrans_code <", value, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeLessThanOrEqualTo(String value) {
            addCriterion("conntrans_code <=", value, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeLike(String value) {
            addCriterion("conntrans_code like", value, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeNotLike(String value) {
            addCriterion("conntrans_code not like", value, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeIn(List<String> values) {
            addCriterion("conntrans_code in", values, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeNotIn(List<String> values) {
            addCriterion("conntrans_code not in", values, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeBetween(String value1, String value2) {
            addCriterion("conntrans_code between", value1, value2, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andConntransCodeNotBetween(String value1, String value2) {
            addCriterion("conntrans_code not between", value1, value2, "conntransCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeIsNull() {
            addCriterion("transfer_code is null");
            return (Criteria) this;
        }

        public Criteria andTransferCodeIsNotNull() {
            addCriterion("transfer_code is not null");
            return (Criteria) this;
        }

        public Criteria andTransferCodeEqualTo(String value) {
            addCriterion("transfer_code =", value, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeNotEqualTo(String value) {
            addCriterion("transfer_code <>", value, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeGreaterThan(String value) {
            addCriterion("transfer_code >", value, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeGreaterThanOrEqualTo(String value) {
            addCriterion("transfer_code >=", value, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeLessThan(String value) {
            addCriterion("transfer_code <", value, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeLessThanOrEqualTo(String value) {
            addCriterion("transfer_code <=", value, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeLike(String value) {
            addCriterion("transfer_code like", value, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeNotLike(String value) {
            addCriterion("transfer_code not like", value, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeIn(List<String> values) {
            addCriterion("transfer_code in", values, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeNotIn(List<String> values) {
            addCriterion("transfer_code not in", values, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeBetween(String value1, String value2) {
            addCriterion("transfer_code between", value1, value2, "transferCode");
            return (Criteria) this;
        }

        public Criteria andTransferCodeNotBetween(String value1, String value2) {
            addCriterion("transfer_code not between", value1, value2, "transferCode");
            return (Criteria) this;
        }

        public Criteria andSerialNumberIsNull() {
            addCriterion("serial_number is null");
            return (Criteria) this;
        }

        public Criteria andSerialNumberIsNotNull() {
            addCriterion("serial_number is not null");
            return (Criteria) this;
        }

        public Criteria andSerialNumberEqualTo(String value) {
            addCriterion("serial_number =", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotEqualTo(String value) {
            addCriterion("serial_number <>", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberGreaterThan(String value) {
            addCriterion("serial_number >", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberGreaterThanOrEqualTo(String value) {
            addCriterion("serial_number >=", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberLessThan(String value) {
            addCriterion("serial_number <", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberLessThanOrEqualTo(String value) {
            addCriterion("serial_number <=", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberLike(String value) {
            addCriterion("serial_number like", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotLike(String value) {
            addCriterion("serial_number not like", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberIn(List<String> values) {
            addCriterion("serial_number in", values, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotIn(List<String> values) {
            addCriterion("serial_number not in", values, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberBetween(String value1, String value2) {
            addCriterion("serial_number between", value1, value2, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotBetween(String value1, String value2) {
            addCriterion("serial_number not between", value1, value2, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeIsNull() {
            addCriterion("crm_customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeIsNotNull() {
            addCriterion("crm_customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeEqualTo(String value) {
            addCriterion("crm_customer_code =", value, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeNotEqualTo(String value) {
            addCriterion("crm_customer_code <>", value, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeGreaterThan(String value) {
            addCriterion("crm_customer_code >", value, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("crm_customer_code >=", value, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeLessThan(String value) {
            addCriterion("crm_customer_code <", value, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("crm_customer_code <=", value, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeLike(String value) {
            addCriterion("crm_customer_code like", value, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeNotLike(String value) {
            addCriterion("crm_customer_code not like", value, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeIn(List<String> values) {
            addCriterion("crm_customer_code in", values, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeNotIn(List<String> values) {
            addCriterion("crm_customer_code not in", values, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeBetween(String value1, String value2) {
            addCriterion("crm_customer_code between", value1, value2, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("crm_customer_code not between", value1, value2, "crmCustomerCode");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameIsNull() {
            addCriterion("crm_customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameIsNotNull() {
            addCriterion("crm_customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameEqualTo(String value) {
            addCriterion("crm_customer_name =", value, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameNotEqualTo(String value) {
            addCriterion("crm_customer_name <>", value, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameGreaterThan(String value) {
            addCriterion("crm_customer_name >", value, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("crm_customer_name >=", value, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameLessThan(String value) {
            addCriterion("crm_customer_name <", value, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("crm_customer_name <=", value, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameLike(String value) {
            addCriterion("crm_customer_name like", value, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameNotLike(String value) {
            addCriterion("crm_customer_name not like", value, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameIn(List<String> values) {
            addCriterion("crm_customer_name in", values, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameNotIn(List<String> values) {
            addCriterion("crm_customer_name not in", values, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameBetween(String value1, String value2) {
            addCriterion("crm_customer_name between", value1, value2, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andCrmCustomerNameNotBetween(String value1, String value2) {
            addCriterion("crm_customer_name not between", value1, value2, "crmCustomerName");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNull() {
            addCriterion("attribute1 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute1IsNotNull() {
            addCriterion("attribute1 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute1EqualTo(String value) {
            addCriterion("attribute1 =", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotEqualTo(String value) {
            addCriterion("attribute1 <>", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThan(String value) {
            addCriterion("attribute1 >", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1GreaterThanOrEqualTo(String value) {
            addCriterion("attribute1 >=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThan(String value) {
            addCriterion("attribute1 <", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1LessThanOrEqualTo(String value) {
            addCriterion("attribute1 <=", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Like(String value) {
            addCriterion("attribute1 like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotLike(String value) {
            addCriterion("attribute1 not like", value, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1In(List<String> values) {
            addCriterion("attribute1 in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotIn(List<String> values) {
            addCriterion("attribute1 not in", values, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1Between(String value1, String value2) {
            addCriterion("attribute1 between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute1NotBetween(String value1, String value2) {
            addCriterion("attribute1 not between", value1, value2, "attribute1");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNull() {
            addCriterion("attribute2 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute2IsNotNull() {
            addCriterion("attribute2 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute2EqualTo(String value) {
            addCriterion("attribute2 =", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotEqualTo(String value) {
            addCriterion("attribute2 <>", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThan(String value) {
            addCriterion("attribute2 >", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2GreaterThanOrEqualTo(String value) {
            addCriterion("attribute2 >=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThan(String value) {
            addCriterion("attribute2 <", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2LessThanOrEqualTo(String value) {
            addCriterion("attribute2 <=", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Like(String value) {
            addCriterion("attribute2 like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotLike(String value) {
            addCriterion("attribute2 not like", value, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2In(List<String> values) {
            addCriterion("attribute2 in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotIn(List<String> values) {
            addCriterion("attribute2 not in", values, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2Between(String value1, String value2) {
            addCriterion("attribute2 between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute2NotBetween(String value1, String value2) {
            addCriterion("attribute2 not between", value1, value2, "attribute2");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNull() {
            addCriterion("attribute3 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute3IsNotNull() {
            addCriterion("attribute3 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute3EqualTo(String value) {
            addCriterion("attribute3 =", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotEqualTo(String value) {
            addCriterion("attribute3 <>", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThan(String value) {
            addCriterion("attribute3 >", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3GreaterThanOrEqualTo(String value) {
            addCriterion("attribute3 >=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThan(String value) {
            addCriterion("attribute3 <", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3LessThanOrEqualTo(String value) {
            addCriterion("attribute3 <=", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Like(String value) {
            addCriterion("attribute3 like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotLike(String value) {
            addCriterion("attribute3 not like", value, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3In(List<String> values) {
            addCriterion("attribute3 in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotIn(List<String> values) {
            addCriterion("attribute3 not in", values, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3Between(String value1, String value2) {
            addCriterion("attribute3 between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute3NotBetween(String value1, String value2) {
            addCriterion("attribute3 not between", value1, value2, "attribute3");
            return (Criteria) this;
        }

        public Criteria andAttribute4IsNull() {
            addCriterion("attribute4 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute4IsNotNull() {
            addCriterion("attribute4 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute4EqualTo(String value) {
            addCriterion("attribute4 =", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotEqualTo(String value) {
            addCriterion("attribute4 <>", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4GreaterThan(String value) {
            addCriterion("attribute4 >", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4GreaterThanOrEqualTo(String value) {
            addCriterion("attribute4 >=", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4LessThan(String value) {
            addCriterion("attribute4 <", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4LessThanOrEqualTo(String value) {
            addCriterion("attribute4 <=", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4Like(String value) {
            addCriterion("attribute4 like", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotLike(String value) {
            addCriterion("attribute4 not like", value, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4In(List<String> values) {
            addCriterion("attribute4 in", values, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotIn(List<String> values) {
            addCriterion("attribute4 not in", values, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4Between(String value1, String value2) {
            addCriterion("attribute4 between", value1, value2, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute4NotBetween(String value1, String value2) {
            addCriterion("attribute4 not between", value1, value2, "attribute4");
            return (Criteria) this;
        }

        public Criteria andAttribute5IsNull() {
            addCriterion("attribute5 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute5IsNotNull() {
            addCriterion("attribute5 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute5EqualTo(String value) {
            addCriterion("attribute5 =", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotEqualTo(String value) {
            addCriterion("attribute5 <>", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5GreaterThan(String value) {
            addCriterion("attribute5 >", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5GreaterThanOrEqualTo(String value) {
            addCriterion("attribute5 >=", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5LessThan(String value) {
            addCriterion("attribute5 <", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5LessThanOrEqualTo(String value) {
            addCriterion("attribute5 <=", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5Like(String value) {
            addCriterion("attribute5 like", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotLike(String value) {
            addCriterion("attribute5 not like", value, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5In(List<String> values) {
            addCriterion("attribute5 in", values, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotIn(List<String> values) {
            addCriterion("attribute5 not in", values, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5Between(String value1, String value2) {
            addCriterion("attribute5 between", value1, value2, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute5NotBetween(String value1, String value2) {
            addCriterion("attribute5 not between", value1, value2, "attribute5");
            return (Criteria) this;
        }

        public Criteria andAttribute6IsNull() {
            addCriterion("attribute6 is null");
            return (Criteria) this;
        }

        public Criteria andAttribute6IsNotNull() {
            addCriterion("attribute6 is not null");
            return (Criteria) this;
        }

        public Criteria andAttribute6EqualTo(String value) {
            addCriterion("attribute6 =", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6NotEqualTo(String value) {
            addCriterion("attribute6 <>", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6GreaterThan(String value) {
            addCriterion("attribute6 >", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6GreaterThanOrEqualTo(String value) {
            addCriterion("attribute6 >=", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6LessThan(String value) {
            addCriterion("attribute6 <", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6LessThanOrEqualTo(String value) {
            addCriterion("attribute6 <=", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6Like(String value) {
            addCriterion("attribute6 like", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6NotLike(String value) {
            addCriterion("attribute6 not like", value, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6In(List<String> values) {
            addCriterion("attribute6 in", values, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6NotIn(List<String> values) {
            addCriterion("attribute6 not in", values, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6Between(String value1, String value2) {
            addCriterion("attribute6 between", value1, value2, "attribute6");
            return (Criteria) this;
        }

        public Criteria andAttribute6NotBetween(String value1, String value2) {
            addCriterion("attribute6 not between", value1, value2, "attribute6");
            return (Criteria) this;
        }

        public Criteria andIsImportIsNull() {
            addCriterion("is_import is null");
            return (Criteria) this;
        }

        public Criteria andIsImportIsNotNull() {
            addCriterion("is_import is not null");
            return (Criteria) this;
        }

        public Criteria andIsImportEqualTo(Boolean value) {
            addCriterion("is_import =", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportNotEqualTo(Boolean value) {
            addCriterion("is_import <>", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportGreaterThan(Boolean value) {
            addCriterion("is_import >", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_import >=", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportLessThan(Boolean value) {
            addCriterion("is_import <", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportLessThanOrEqualTo(Boolean value) {
            addCriterion("is_import <=", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportIn(List<Boolean> values) {
            addCriterion("is_import in", values, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportNotIn(List<Boolean> values) {
            addCriterion("is_import not in", values, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportBetween(Boolean value1, Boolean value2) {
            addCriterion("is_import between", value1, value2, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_import not between", value1, value2, "isImport");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNull() {
            addCriterion("deleted_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIsNotNull() {
            addCriterion("deleted_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagEqualTo(Integer value) {
            addCriterion("deleted_flag =", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotEqualTo(Integer value) {
            addCriterion("deleted_flag <>", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThan(Integer value) {
            addCriterion("deleted_flag >", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("deleted_flag >=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThan(Integer value) {
            addCriterion("deleted_flag <", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagLessThanOrEqualTo(Integer value) {
            addCriterion("deleted_flag <=", value, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagIn(List<Integer> values) {
            addCriterion("deleted_flag in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotIn(List<Integer> values) {
            addCriterion("deleted_flag not in", values, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagBetween(Integer value1, Integer value2) {
            addCriterion("deleted_flag between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andDeletedFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("deleted_flag not between", value1, value2, "deletedFlag");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIsNull() {
            addCriterion("conversion_type is null");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIsNotNull() {
            addCriterion("conversion_type is not null");
            return (Criteria) this;
        }

        public Criteria andConversionTypeEqualTo(String value) {
            addCriterion("conversion_type =", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotEqualTo(String value) {
            addCriterion("conversion_type <>", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeGreaterThan(String value) {
            addCriterion("conversion_type >", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeGreaterThanOrEqualTo(String value) {
            addCriterion("conversion_type >=", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLessThan(String value) {
            addCriterion("conversion_type <", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLessThanOrEqualTo(String value) {
            addCriterion("conversion_type <=", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeLike(String value) {
            addCriterion("conversion_type like", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotLike(String value) {
            addCriterion("conversion_type not like", value, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeIn(List<String> values) {
            addCriterion("conversion_type in", values, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotIn(List<String> values) {
            addCriterion("conversion_type not in", values, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeBetween(String value1, String value2) {
            addCriterion("conversion_type between", value1, value2, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionTypeNotBetween(String value1, String value2) {
            addCriterion("conversion_type not between", value1, value2, "conversionType");
            return (Criteria) this;
        }

        public Criteria andConversionDateIsNull() {
            addCriterion("conversion_date is null");
            return (Criteria) this;
        }

        public Criteria andConversionDateIsNotNull() {
            addCriterion("conversion_date is not null");
            return (Criteria) this;
        }

        public Criteria andConversionDateEqualTo(Date value) {
            addCriterion("conversion_date =", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotEqualTo(Date value) {
            addCriterion("conversion_date <>", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateGreaterThan(Date value) {
            addCriterion("conversion_date >", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateGreaterThanOrEqualTo(Date value) {
            addCriterion("conversion_date >=", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateLessThan(Date value) {
            addCriterion("conversion_date <", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateLessThanOrEqualTo(Date value) {
            addCriterion("conversion_date <=", value, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateIn(List<Date> values) {
            addCriterion("conversion_date in", values, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotIn(List<Date> values) {
            addCriterion("conversion_date not in", values, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateBetween(Date value1, Date value2) {
            addCriterion("conversion_date between", value1, value2, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionDateNotBetween(Date value1, Date value2) {
            addCriterion("conversion_date not between", value1, value2, "conversionDate");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNull() {
            addCriterion("conversion_rate is null");
            return (Criteria) this;
        }

        public Criteria andConversionRateIsNotNull() {
            addCriterion("conversion_rate is not null");
            return (Criteria) this;
        }

        public Criteria andConversionRateEqualTo(BigDecimal value) {
            addCriterion("conversion_rate =", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <>", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThan(BigDecimal value) {
            addCriterion("conversion_rate >", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate >=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThan(BigDecimal value) {
            addCriterion("conversion_rate <", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("conversion_rate <=", value, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateIn(List<BigDecimal> values) {
            addCriterion("conversion_rate in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotIn(List<BigDecimal> values) {
            addCriterion("conversion_rate not in", values, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andConversionRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("conversion_rate not between", value1, value2, "conversionRate");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdIsNull() {
            addCriterion("customer_transfer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdIsNotNull() {
            addCriterion("customer_transfer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdEqualTo(Long value) {
            addCriterion("customer_transfer_id =", value, "customerTransferId");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdNotEqualTo(Long value) {
            addCriterion("customer_transfer_id <>", value, "customerTransferId");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdGreaterThan(Long value) {
            addCriterion("customer_transfer_id >", value, "customerTransferId");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdGreaterThanOrEqualTo(Long value) {
            addCriterion("customer_transfer_id >=", value, "customerTransferId");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdLessThan(Long value) {
            addCriterion("customer_transfer_id <", value, "customerTransferId");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdLessThanOrEqualTo(Long value) {
            addCriterion("customer_transfer_id <=", value, "customerTransferId");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdIn(List<Long> values) {
            addCriterion("customer_transfer_id in", values, "customerTransferId");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdNotIn(List<Long> values) {
            addCriterion("customer_transfer_id not in", values, "customerTransferId");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdBetween(Long value1, Long value2) {
            addCriterion("customer_transfer_id between", value1, value2, "customerTransferId");
            return (Criteria) this;
        }

        public Criteria andCustomerTransferIdNotBetween(Long value1, Long value2) {
            addCriterion("customer_transfer_id not between", value1, value2, "customerTransferId");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}