package com.midea.pam.common.ctc.excelVo;


import com.github.crab2died.annotation.ExcelField;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.crm.entity.Customer;
import com.midea.pam.common.ctc.entity.ReceiptClaim;
import com.midea.pam.common.ctc.entity.ReceiptClaimDetail;
import com.midea.pam.common.util.Builder;
import com.midea.pam.common.util.excel4j.ExcelDate2DateConverter;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.Setter;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiOperation("数据初始化-收款导入-回款分配")
public class ReceiptClaimExcelVo {

    @ExcelField(title = "序号", order = 2)
    @Excel(name = "序号")
    private String serialNumber;

    @ExcelField(title = "来源系统编码", order = 3)
    @Excel(name = "来源系统编码")
    private String sourceSystemCode;

    @ExcelField(title = "资金流水号", order = 4)
    @Excel(name = "资金流水号")
    private String cashReceiptCode;

    @ExcelField(title = "到账日期(对应到期日)", order = 5, readConverter = ExcelDate2DateConverter.class)
    @Excel(name = "到账日期(对应到期日)")
    private Date payDate;

    @ExcelField(title = "付款金额", order = 6)
    @Excel(name = "付款金额")
    private BigDecimal payAmount;

    @ExcelField(title = "回款计划编号", order = 7)
    @Excel(name = "回款计划编号")
    private String receiptPlanDetailCode;

    @ExcelField(title = "币种", order = 8)
    @Excel(name = "币种")
    private String currencyCode;

    @ExcelField(title = "客户编号", order = 9)
    @Excel(name = "客户编号")
    private String crmCustomerCode;

    @ExcelField(title = "客户名称", order = 10)
    @Excel(name = "客户名称")
    private String crmCustomerName;

    @ExcelField(title = "PAM合同编号", order = 11)
    @Excel(name = "PAM合同编号")
    private String pamContractCode;

    @ExcelField(title = "合同名称", order = 12)
    @Excel(name = "合同名称")
    private String contractName;

    @ExcelField(title = "付款方名称=客户名称", order = 13)
    @Excel(name = "付款方名称=客户名称")
    private String payName;

    @ExcelField(title = "付款方账号", order = 14)
    @Excel(name = "付款方账号")
    private String payBankCode;

    @ExcelField(title = "付款银行名称", order = 15)
    @Excel(name = "付款银行名称")
    private String payBankName;

    @ExcelField(title = "收款方法", order = 16)
    @Excel(name = "收款方法")
    private String recMethod; //初始化

    @ExcelField(title = "收款方银行账号ID", order = 17)
    @Excel(name = "收款方银行账号ID")
    private Long recBankId;

    @ExcelField(title = "收款方银行帐号", order = 18)
    @Excel(name = "收款方银行帐号")
    private String recBankCode;

    @ExcelField(title = "收款账号", order = 19)
    @Excel(name = "收款账号")
    private String recAccountNo;

    @ExcelField(title = "收款单位名称", order = 20)
    @Excel(name = "收款单位名称")
    private String recOrgName;

    @ExcelField(title = "收款单位编号", order = 21)
    @Excel(name = "收款单位编号")
    private String recOrgCode;

    @ExcelField(title = "资金状态", order = 22)
    @Excel(name = "资金状态")
    private String cashStatus;

    @ExcelField(title = "拆款状态", order = 23)
    @Excel(name = "拆款状态")
    private String divideStatus;

    @ExcelField(title = "业务实体", order = 24)
    @Excel(name = "业务实体")
    private String ouId;

    @ExcelField(title = "预算项目名称", order = 25)
    @Excel(name = "预算项目名称")
    private String budgetItemCode;

    @ExcelField(title = "备注", order = 26)
    @Excel(name = "备注")
    private String remark;

    @ExcelField(title = "开票计划行号", order = 27)
    @Excel(name = "开票计划行号")
    private String invoicePlanDetailCode;

    private String errMsg;

    public void checkNull() {
        errMsg = "";  //TODO
    }

    public ReceiptClaim buildHeader(
            OperatingUnit operatingUnit
    ) {
        return Builder.of(ReceiptClaim::new) //
                .with(ReceiptClaim::setSourceSystemCode, sourceSystemCode)
                .with(ReceiptClaim::setCashReceiptCode, cashReceiptCode)
                .with(ReceiptClaim::setCashStatus, 2)
                .with(ReceiptClaim::setDivideStatus, 2)
                .with(ReceiptClaim::setPayName, payName)
                .with(ReceiptClaim::setPayBankCode, payBankCode)
                .with(ReceiptClaim::setPayBankName, payBankName)
//                .with(ReceiptClaim::setBillCode, billCode)
                .with(ReceiptClaim::setPayAmount, payAmount)
                .with(ReceiptClaim::setCurrencyCode, currencyCode)
//                .with(ReceiptClaim::setBillType,)
//                .with(ReceiptClaim::setSettleWay, settleWay)
                .with(ReceiptClaim::setRecMethod, recMethod)
                .with(ReceiptClaim::setRecBankId, recBankId)
                .with(ReceiptClaim::setRecBankCode, recBankCode)
                .with(ReceiptClaim::setRecAccountNo, recAccountNo)
                .with(ReceiptClaim::setRecOrgCode, recOrgCode)
                .with(ReceiptClaim::setRecOrgName, recOrgName)
                .with(ReceiptClaim::setBudgetItemCode, budgetItemCode)
                .with(ReceiptClaim::setOuId, operatingUnit.getId())
                .with(ReceiptClaim::setPayDate, payDate)
                .with(ReceiptClaim::setRemark, remark)
//                .with(ReceiptClaim::setConntransCode,)
//                .with(ReceiptClaim::setTransferCode,)
                .with(ReceiptClaim::setCrmCustomerCode, crmCustomerCode)
                .with(ReceiptClaim::setCrmCustomerName, crmCustomerName)
                .with(ReceiptClaim::setCreateBy, SystemContext.getUserId())
                .with(ReceiptClaim::setCreateAt, new Date())
                .with(ReceiptClaim::setIsImport, Boolean.TRUE)
                .with(ReceiptClaim::setDeletedFlag, 0)
                .build();

    }

    public ReceiptClaimDetail buildDetail(
            ReceiptClaim receiptClaim
            , Customer customer
    ) {
        return Builder.of(ReceiptClaimDetail::new)
                .with(ReceiptClaimDetail::setReceiptClaimId, receiptClaim.getId())
                .with(ReceiptClaimDetail::setReceiptCode, receiptClaim.getCashReceiptCode())
                .with(ReceiptClaimDetail::setBusinessType, 1)
                .with(ReceiptClaimDetail::setAccountingDate, payDate)
                .with(ReceiptClaimDetail::setAmountReceived, payAmount)
                .with(ReceiptClaimDetail::setClaimAmount, payAmount)
                .with(ReceiptClaimDetail::setClaimStatus, 2)
                .with(ReceiptClaimDetail::setClaimDate, payDate)
//                .with(ReceiptClaimDetail::setClaimBy) //认款人
                .with(ReceiptClaimDetail::setCustomerCode, crmCustomerCode)
                .with(ReceiptClaimDetail::setCustomerId, customer.getId())
                .with(ReceiptClaimDetail::setCustomerName, customer.getName())
//                .with(ReceiptClaimDetail::setBusiSceneId,) //业务场景id
//                .with(ReceiptClaimDetail::setBusiScene) //业务场景
//                .with(ReceiptClaimDetail::setVendorCode) //供应商
//                .with(ReceiptClaimDetail::setApInvoiceSubject)
//                .with(ReceiptClaimDetail::setPaymentApplyId)
                .with(ReceiptClaimDetail::setCreateBy, SystemContext.getUserId())
                .with(ReceiptClaimDetail::setCreateAt, new Date())
                .with(ReceiptClaimDetail::setDeletedFlag, 0)
                .with(ReceiptClaimDetail::setErpStatus, 3)
                .with(ReceiptClaimDetail::setContractSyncStatus, 1)
                .with(ReceiptClaimDetail::setErpMessage, null)
                .with(ReceiptClaimDetail::setContractStatus, 3)
                .build();
    }

    @Override
    public String toString() {
        return "ReceiptClaimExcelVo{" +
                "serialNumber='" + serialNumber + '\'' +
                ", sourceSystemCode='" + sourceSystemCode + '\'' +
                ", cashReceiptCode='" + cashReceiptCode + '\'' +
                ", payDate=" + payDate +
                ", payAmount=" + payAmount +
                ", currencyCode='" + currencyCode + '\'' +
                ", crmCustomerCode='" + crmCustomerCode + '\'' +
                ", crmCustomerName='" + crmCustomerName + '\'' +
                ", pamContractCode='" + pamContractCode + '\'' +
                ", contractName='" + contractName + '\'' +
                ", payName='" + payName + '\'' +
                ", payBankCode='" + payBankCode + '\'' +
                ", payBankName='" + payBankName + '\'' +
                ", recMethod='" + recMethod + '\'' +
                ", recBankId=" + recBankId +
                ", recBankCode='" + recBankCode + '\'' +
                ", recAccountNo='" + recAccountNo + '\'' +
                ", recOrgName='" + recOrgName + '\'' +
                ", recOrgCode='" + recOrgCode + '\'' +
                ", cashStatus='" + cashStatus + '\'' +
                ", divideStatus='" + divideStatus + '\'' +
                ", ouId='" + ouId + '\'' +
                ", budgetItemCode='" + budgetItemCode + '\'' +
                ", remark='" + remark + '\'' +
                ", invoicePlanDetailCode='" + invoicePlanDetailCode + '\'' +
                ", errMsg='" + errMsg + '\'' +
                '}';
    }

}
