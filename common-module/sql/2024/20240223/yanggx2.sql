-- 修复原因：数据查看时发现有领退料单缺少库存组织导致界面无法查找
-- 临时方案：当前未找到原因，先加入到数据监控中，后期新数据产生后立即查找原因
-- 备份脚本
CREATE TABLE pam_backup.material_get_header_20240223_1_ygx_1 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '问题数据deleted_flag=1' as modify_remark,
  'AAIG004963，领料单：SLL240125054数据库有，但是列表查询没有返回' as flow_title,
  t.*
FROM pam_ctc.material_get_header t
WHERE id = 1022190516021755904
;
-- 更新脚本，影响行数1
UPDATE pam_ctc.material_get_header
SET deleted_flag=1
WHERE id=1022190516021755904;


-- 修复原因：BUG2024022356232 PAM-运维-高-里程碑驳回重新编辑会出现序号错误里程碑无法删除。
-- 长期计划：BUG预计在下迭代240308解决。
-- 备份脚本
CREATE TABLE pam_backup.project_milepost_20240223_1_ygx_14 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '其中12条deleted_flag=1，2条修改order_num=2' as modify_remark,
  'BUG2024022356232，里程碑驳回重新编辑会出现序号错误里程碑无法删除' as flow_title,
  t.*
FROM pam_ctc.project_milepost t
WHERE id in
(
1041744684998721536,
1041744685040664576,
1041744685082607616,
1041744685116162048,
1041744685153910784,
1041744685187465216,
1209452611745480704,
1041765086546362368,
1041765086579916800,
1041765086621859840,
1041765086655414272,
1041765086688968704,
1041765086718328832,
1209452658918817792
)
;
-- 更新脚本，影响行数14
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1041744684998721536;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1041744685040664576;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1041744685082607616;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1041744685116162048;
UPDATE pam_ctc.project_milepost
	SET order_num=2
	WHERE id=1041744685153910784;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1041744685187465216;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1209452611745480704;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1041765086546362368;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1041765086579916800;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1041765086621859840;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1041765086655414272;
UPDATE pam_ctc.project_milepost
	SET order_num=2
	WHERE id=1041765086688968704;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1041765086718328832;
UPDATE pam_ctc.project_milepost
	SET deleted_flag=1
	WHERE id=1209452658918817792;


-- 修复原因：项目：QB2021093021初始化里程碑结束日期有误，导致里程碑变更受阻
-- 临时方案：找出系统初始化里程碑结束日期有误的数据修复
-- 长期方案：BUG2024022356910跟进优化里程碑初始化程序的校验点
-- 备份脚本
CREATE TABLE pam_backup.material_return_header_20240223_2_ygx_53 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '里程碑计划结束日期修正' as modify_remark,
  'BUG2024022356910，跟进优化里程碑初始化程序的校验点' as flow_title,
  t.*
FROM pam_ctc.material_return_header t
WHERE id in
(
848343950673575936,
848343950623244288,
848343949947961344,
848343950740684800,
850679705718751232,
848343950757462016,
848343950774239232,
848343950791016448,
848343950841348096,
848343950858125312,
848343951004925952,
848343950103150592,
850679703193780224,
848343951051063296,
848343951067840512,
848343951084617728,
848343951101394944,
848343951118172160,
848343950182842368,
848343951134949376,
848343951151726592,
848343951181086720,
848343951197863936,
848343951214641152,
848343951231418368,
848343951357247488,
893226303480659968,
895634358616981504,
895634362907754496,
1047891577575309312,
1062776947752304640,
1125797925834719232,
1187826549169713152,
850679705798443008,
850679705861357568,
850679706603749376,
850679706775715840,
850679704028446720,
850679704422711296,
850679704603066368,
850679715889938432,
850679715906715648,
850679717773180928,
850679717802541056,
895634362979057664,
20221026160900095,
20221026160900096,
20221026160900097,
202210261609000103,
202210261609000105,
202210261609000106,
202210261609000109,
1187826549241016320
)
;
-- 更新脚本，影响行数53
update pam_ctc.project_milepost set end_time = "2021-05-31 00:00:00" where id = 848343950673575936;
update pam_ctc.project_milepost set end_time = "2021-03-11 00:00:00" where id = 848343950623244288;
update pam_ctc.project_milepost set end_time = "2021-04-10 00:00:00" where id = 848343949947961344;
update pam_ctc.project_milepost set end_time = "2021-04-30 00:00:00" where id = 848343950740684800;
update pam_ctc.project_milepost set end_time = "2021-02-04 00:00:00" where id = 850679705718751232;
update pam_ctc.project_milepost set end_time = "2020-03-28 00:00:00" where id = 848343950757462016;
update pam_ctc.project_milepost set end_time = "2017-08-11 00:00:00" where id = 848343950774239232;
update pam_ctc.project_milepost set end_time = "2017-03-31 00:00:00" where id = 848343950791016448;
update pam_ctc.project_milepost set end_time = "2016-12-07 00:00:00" where id = 848343950841348096;
update pam_ctc.project_milepost set end_time = "2017-05-30 00:00:00" where id = 848343950858125312;
update pam_ctc.project_milepost set end_time = "2020-08-10 00:00:00" where id = 848343951004925952;
update pam_ctc.project_milepost set end_time = "2019-11-03 00:00:00" where id = 848343950103150592;
update pam_ctc.project_milepost set end_time = "2020-09-06 00:00:00" where id = 850679703193780224;
update pam_ctc.project_milepost set end_time = "2020-09-08 00:00:00" where id = 848343951051063296;
update pam_ctc.project_milepost set end_time = "2021-05-31 00:00:00" where id = 848343951067840512;
update pam_ctc.project_milepost set end_time = "2019-02-02 00:00:00" where id = 848343951084617728;
update pam_ctc.project_milepost set end_time = "2020-11-13 00:00:00" where id = 848343951101394944;
update pam_ctc.project_milepost set end_time = "2018-11-03 00:00:00" where id = 848343951118172160;
update pam_ctc.project_milepost set end_time = "2019-12-20 00:00:00" where id = 848343950182842368;
update pam_ctc.project_milepost set end_time = "2020-05-13 00:00:00" where id = 848343951134949376;
update pam_ctc.project_milepost set end_time = "2020-05-14 00:00:00" where id = 848343951151726592;
update pam_ctc.project_milepost set end_time = "2017-10-30 00:00:00" where id = 848343951181086720;
update pam_ctc.project_milepost set end_time = "2017-11-02 00:00:00" where id = 848343951197863936;
update pam_ctc.project_milepost set end_time = "2017-08-15 00:00:00" where id = 848343951214641152;
update pam_ctc.project_milepost set end_time = "2017-08-15 00:00:00" where id = 848343951231418368;
update pam_ctc.project_milepost set end_time = "2021-05-31 00:00:00" where id = 848343951357247488;
update pam_ctc.project_milepost set end_time = "2020-05-10 00:00:00" where id = 893226303480659968;
update pam_ctc.project_milepost set end_time = "2021-05-10 00:00:00" where id = 895634358616981504;
update pam_ctc.project_milepost set end_time = "2020-09-01 00:00:00" where id = 895634362907754496;
update pam_ctc.project_milepost set end_time = "2022-07-10 00:00:00" where id = 1047891577575309312;
update pam_ctc.project_milepost set end_time = "2023-01-13 00:00:00" where id = 1062776947752304640;
update pam_ctc.project_milepost set end_time = "2023-03-31 00:00:00" where id = 1125797925834719232;
update pam_ctc.project_milepost set end_time = "2023-05-10 00:00:00" where id = 1187826549169713152;
update pam_ctc.project_milepost set end_time = "2021-08-26 00:00:00" where id = 850679705798443008;
update pam_ctc.project_milepost set end_time = "2022-03-30 00:00:00" where id = 850679705861357568;
update pam_ctc.project_milepost set end_time = "2021-03-20 00:00:00" where id = 850679706603749376;
update pam_ctc.project_milepost set end_time = "2021-03-20 00:00:00" where id = 850679706775715840;
update pam_ctc.project_milepost set end_time = "2021-01-25 00:00:00" where id = 850679704028446720;
update pam_ctc.project_milepost set end_time = "2020-11-11 00:00:00" where id = 850679704422711296;
update pam_ctc.project_milepost set end_time = "2020-11-11 00:00:00" where id = 850679704603066368;
update pam_ctc.project_milepost set end_time = "2017-11-02 00:00:00" where id = 850679715889938432;
update pam_ctc.project_milepost set end_time = "2017-11-02 00:00:00" where id = 850679715906715648;
update pam_ctc.project_milepost set end_time = "2022-05-30 00:00:00" where id = 850679717773180928;
update pam_ctc.project_milepost set end_time = "2022-05-30 00:00:00" where id = 850679717802541056;
update pam_ctc.project_milepost set end_time = "2021-09-05 00:00:00" where id = 895634362979057664;
update pam_ctc.project_milepost set end_time = "2022-08-31 00:00:00" where id = 20221026160900095;
update pam_ctc.project_milepost set end_time = "2022-09-15 00:00:00" where id = 20221026160900096;
update pam_ctc.project_milepost set end_time = "2022-10-26 00:00:00" where id = 20221026160900097;
update pam_ctc.project_milepost set end_time = "2023-01-17 00:00:00" where id = 202210261609000103;
update pam_ctc.project_milepost set end_time = "2023-01-18 00:00:00" where id = 202210261609000105;
update pam_ctc.project_milepost set end_time = "2023-01-19 00:00:00" where id = 202210261609000106;
update pam_ctc.project_milepost set end_time = "2023-05-10 00:00:00" where id = 202210261609000109;
update pam_ctc.project_milepost set end_time = "2023-05-10 00:00:00" where id = 1187826549241016320;