-- 单位瑞士格下,因配置了是否提交确认订单材料为启用，需要对该单位下销售合同表的whether_customer_confirm数据进行初始化
-- contract备份脚本
CREATE TABLE pam_backup.contract_20240315_1_chenchong_796 (
	`id` bigint(20) NOT NULL COMMENT 'ID',
	PRIMARY KEY (`id`)
) AS
select
  '销售合同表调整whether_customer_confirm数据初始化' as modify_remark,
  '关联需求AAIG004768, 合同“是否提交确认订单材料”初始配置时，销售合同表数据初始化' as flow_title,
	c.*
from pam_ctc.contract c
where
	c.ou_id = 100205
	and c.deleted_flag = 0 and c.parent_id is null;
-- 初始化脚本，影响行数796
update
	pam_ctc.contract
set
	whether_customer_confirm =
	case
		when length(annex) > 1 then 1
		else 0
	end
where
	ou_id = 100205
	and deleted_flag = 0
	and parent_id is null;


-- 单位瑞士格下,因配置了是否提交确认订单材料为启用，需要对该单位下销售合同变更历史表的whether_customer_confirm数据进行初始化
-- contract_his备份脚本
CREATE TABLE pam_backup.contract_his_20240303_1_chenchong_693 (
	`id` bigint(20) NOT NULL COMMENT 'ID',
	PRIMARY KEY (`id`)
) AS
select
  '销售合同变更历史表主合同调整whether_customer_confirm数据初始化' as modify_remark,
  '关联需求AAIG004768, 合同“是否提交确认订单材料”初始配置时，销售合同变更历史表主合同数据初始化' as flow_title,
	ch.*
from
	 pam_ctc.contract_his ch
left join pam_ctc.contract c  on
	c.id = ch.contract_id
	and c.deleted_flag = 0
where
	c.ou_id = 100205
	and c.parent_id is null;
-- 初始化脚本，影响行数693
update
	 pam_ctc.contract_his ch
left join pam_ctc.contract c  on
	c.id = ch.contract_id
	and c.deleted_flag = 0
set
	ch.whether_customer_confirm = c.whether_customer_confirm
where
	c.ou_id = 100205
	and c.parent_id is null;

