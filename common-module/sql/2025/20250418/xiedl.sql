-- 备份脚本
CREATE TABLE pam_backup.milepost_design_plan_material_detail_20240418_xiedl_1 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '逻辑失效历史重复mrp需求数量' as modify_remark,
  'PAM-Sprint5入库脚本_DML(增删改数据)_20250418' as flow_title,
  t.*
FROM pam_ctc.milepost_design_plan_material_detail t
where id = 915554702840561664;

-- 更新脚本，影响行数: 1
update pam_ctc.milepost_design_plan_material_detail set deleted_flag = 1 where id = 915554702840561664;