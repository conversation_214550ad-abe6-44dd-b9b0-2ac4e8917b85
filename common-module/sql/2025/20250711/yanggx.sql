-- 备份脚本
CREATE TABLE pam_backup.diff_company_labor_cost_set_20250711_1_ygx_2 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】表逻辑变更初始化，逻辑删除历史数据' as modify_remark,
  'AAIG037799，【开发】增加hr接口获取员工的发薪ou和费用科目 2、调整工时入账科目获取逻辑' as flow_title,
  t.*
FROM pam_basedata.diff_company_labor_cost_set t
WHERE id in (1134584453155258368, 1134584600501157888)
;
-- 更新脚本，影响行数2
UPDATE pam_basedata.diff_company_labor_cost_set
SET deleted_flag = 1, update_at = now()
WHERE id in (1134584453155258368, 1134584600501157888)
;

-- pam_basedata.diff_company_labor_cost_set初始化
INSERT INTO pam_basedata.diff_company_labor_cost_set
(id, project_company_id, user_company_id, ou_id, ou_name, hard_working, full_pay_ou_id, full_pay_ou_name, deleted_flag, create_by, create_at, update_by, update_at)
VALUES(20250702001, 581144287652085760, NULL, 100022, 'OU_132902_库卡机器人（广东）有限公司-自动化', '132902.13290200500.5101990101.0.0.0.0', 100132, 'OU_133703_库卡工业自动化(昆山)有限公司-项目', 0, 20250702, '2025-07-02 10:00:00', NULL, NULL);
INSERT INTO pam_basedata.diff_company_labor_cost_set
(id, project_company_id, user_company_id, ou_id, ou_name, hard_working, full_pay_ou_id, full_pay_ou_name, deleted_flag, create_by, create_at, update_by, update_at)
VALUES(20250702002, 581144287652085760, NULL, 100022, 'OU_132902_库卡机器人（广东）有限公司-自动化', '132902.13290200500.5101990101.0.0.0.0', 100133, 'OU_133704_库卡工业自动化(昆山)有限公司-制造', 0, 20250702, '2025-07-02 10:00:00', NULL, NULL);
INSERT INTO pam_basedata.diff_company_labor_cost_set
(id, project_company_id, user_company_id, ou_id, ou_name, hard_working, full_pay_ou_id, full_pay_ou_name, deleted_flag, create_by, create_at, update_by, update_at)
VALUES(20250702003, 814428597413478400, NULL, 100132, 'OU_133703_库卡工业自动化(昆山)有限公司-项目', '133703.13370330020.5101990101.0.0.0.0', 3081, 'OU_129701_广东美的智能机器人有限公司', 0, 20250702, '2025-07-02 10:00:00', NULL, NULL);
INSERT INTO pam_basedata.diff_company_labor_cost_set
(id, project_company_id, user_company_id, ou_id, ou_name, hard_working, full_pay_ou_id, full_pay_ou_name, deleted_flag, create_by, create_at, update_by, update_at)
VALUES(20250702004, 814428597413478400, NULL, 100132, 'OU_133703_库卡工业自动化(昆山)有限公司-项目', '133703.13370330020.5101990101.0.0.0.0', 100022, 'OU_132902_库卡机器人（广东）有限公司-自动化', 0, 20250702, '2025-07-02 10:00:00', NULL, NULL);




-- 备份脚本
CREATE TABLE pam_backup.working_hour_20250711_1_ygx_all (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】新增字段初始化' as modify_remark,
  'AAIG037799，【开发】增加hr接口获取员工的发薪ou和费用科目 2、调整工时入账科目获取逻辑' as flow_title,
  wh.*
FROM pam_ctc.working_hour wh
AND wh.full_pay_ou_id IS NULL
AND wh.delete_flag = 0;