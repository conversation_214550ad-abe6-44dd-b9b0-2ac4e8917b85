-- 备份脚本
create table pam_backup.purchase_order_20250523_ex_wuyh42 (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  PRIMARY KEY (`id`)
) as
select
  '初始化瑞士格医疗的订单头同步来源系统字段' as modify_remark,
  '初始化瑞士格医疗的订单头同步来源系统字段' as flow_title,
  t.*
from pam_ctc.purchase_order t where ou_id = 100205;

-- 更新脚本
update pam_ctc.purchase_order set sync_source_system = 'PAM' where ou_id = 100205 and sync_source_system is null;


-- 初始化收货地址菜单
INSERT INTO pam_basedata.light_menu
(id, parent_id, code, name, context, uri, `method`, icon, `rank`, available, description, editable, show_side, create_by, create_at, update_by, update_at)
VALUES(1361304179824820224, 813436739967254528, '28000028', '收货地址管理', NULL, 'deliveryAddressList', NULL, '', 1000, 1, NULL, 1, 1, 1354412465929781248, '2025-06-10 11:36:48', 1354412465929781248, '2025-06-10 13:36:17');