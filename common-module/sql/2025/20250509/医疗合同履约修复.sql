-- 备份脚本(cost_collection)
CREATE TABLE pam_backup.cost_collection_20250509_ex_liangjy41_2 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗合同履约修复' as modify_remark,
  'AAIG035768，【数据修复】医疗合同履约修复' as flow_title,
  t.*
FROM pam_ctc.cost_collection t
where id in
(
202502250001,
202502250002
);

-- 更新脚本，影响行数2
UPDATE pam_ctc.cost_collection SET deleted_flag = 1 WHERE id=202502250001;
UPDATE pam_ctc.cost_collection SET deleted_flag = 1 WHERE id=202502250002;
-- 新增脚本
INSERT INTO pam_ctc.cost_collection (id,collection_date,cost_date,`type`,ou_id,ou_name,project_id,project_code,project_name,project_type,status,cost_method_main,currency,material_actual_cost,material_penalty_cost,material_outsource_cost,material_difference_cost,inner_labor_cost,outer_labor_cost,fee_cost,asset_deprn_cost,carry_status,gl_period,create_by,create_at,update_by,update_at,deleted_flag)
	VALUES (20250426101,'2023-02-28','2021-09-30',1,100205,'OU_134301_上海瑞仕格医疗科技有限公司',894147659516870656,'QB2021093020','HCQB 广西前海人寿医院 P-014723','瑞仕格医疗全包',0,'0410','CNY',0.000000,0.000000,-376000.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0,'',-2,'2025-04-26 14:13:57',-2,'2025-04-26 14:13:57',0);
INSERT INTO pam_ctc.cost_collection (id,collection_date,cost_date,`type`,ou_id,ou_name,project_id,project_code,project_name,project_type,status,cost_method_main,currency,material_actual_cost,material_outsource_cost,material_difference_cost,inner_labor_cost,outer_labor_cost,fee_cost,asset_deprn_cost,carry_status,gl_period,create_by,create_at,update_by,update_at,deleted_flag)
	VALUES (20250426102,'2024-02-21','2021-03-22',1,100205,'OU_134301_上海瑞仕格医疗科技有限公司',893907764714143744,'QB2021093002','HCQB 淮北人民医院 P-016044','瑞仕格医疗全包',0,'0410','CNY',0.000000,-132790.840000,0.000000,0.000000,0.000000,0.000000,0.000000,0,'',-2,'2025-04-26 14:13:57',-2,'2025-04-26 14:13:57',0);
INSERT INTO pam_ctc.cost_collection (id,collection_date,cost_date,`type`,ou_id,ou_name,project_id,project_code,project_name,project_type,cost_method_main,currency,material_actual_cost,material_penalty_cost,material_outsource_cost,material_difference_cost,inner_labor_cost,outer_labor_cost,fee_cost,asset_deprn_cost,carry_status,gl_period,create_by,create_at,update_by,update_at,deleted_flag)
	VALUES (20250426103,'2022-12-01','2022-11-30',1,100205,'OU_134301_上海瑞仕格医疗科技有限公司',991278426528677888,'CSSPS2022062801','烟台山医院气动物流备件合同','自动化售后服务-质保外备件','0409','CNY',310.68,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0,'',-2,'2025-04-26 15:03:58',-2,'2025-04-26 15:03:58',0);
INSERT INTO pam_ctc.cost_collection (id,collection_date,cost_date,`type`,ou_id,ou_name,project_id,project_code,project_name,project_type,cost_method_main,currency,material_actual_cost,material_penalty_cost,material_outsource_cost,material_difference_cost,inner_labor_cost,outer_labor_cost,fee_cost,asset_deprn_cost,carry_status,gl_period,create_by,create_at,update_by,update_at,deleted_flag)
	VALUES (20250426104,'2023-11-30','2023-11-29',1,100205,'OU_134301_上海瑞仕格医疗科技有限公司',1160592068053417984,'CSSSA2023100801','达实久信授权服务合同','自动化售后服务-质保外维保','0413','CNY',0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-24.98,0.000000,0,'',-2,'2025-04-26 13:05:20',-2,'2025-04-26 13:05:20',0);
INSERT INTO pam_ctc.cost_collection (id,collection_date,cost_date,`type`,ou_id,ou_name,project_id,project_code,project_name,project_type,cost_method_main,currency,material_actual_cost,material_penalty_cost,material_outsource_cost,material_difference_cost,inner_labor_cost,outer_labor_cost,fee_cost,asset_deprn_cost,carry_status,gl_period,create_by,create_at,update_by,update_at,deleted_flag)
	VALUES (20250426106,'2024-01-30','2024-01-24',1,100205,'OU_134301_上海瑞仕格医疗科技有限公司',1090558493494083584,'CSSSA2023032901','福建档案馆轨道物流维保','自动化售后服务-质保外维保','0413','CNY',0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-280,0.000000,0,'',-2,'2025-04-26 13:05:20',-2,'2025-04-26 13:05:20',0);
INSERT INTO pam_ctc.cost_collection (id,collection_date,cost_date,`type`,ou_id,ou_name,project_id,project_code,project_name,project_type,cost_method_main,currency,material_actual_cost,material_penalty_cost,material_outsource_cost,material_difference_cost,inner_labor_cost,outer_labor_cost,fee_cost,asset_deprn_cost,carry_status,gl_period,create_by,create_at,update_by,update_at,deleted_flag)
	VALUES (20250426105,'2024-01-30','2024-01-18',1,100205,'OU_134301_上海瑞仕格医疗科技有限公司',1090558493494083584,'CSSSA2023032901','福建档案馆轨道物流维保','自动化售后服务-质保外维保','0413','CNY',0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-217.15,0.000000,0,'',-2,'2025-04-26 13:05:20',-2,'2025-04-26 13:05:20',0);



-- 备份脚本(material_outsource_cost_detail)
CREATE TABLE pam_backup.material_outsource_cost_detail_20250509_ex_liangjy41_2 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗合同履约修复' as modify_remark,
  'AAIG035768，【数据修复】医疗合同履约修复' as flow_title,
  t.*
FROM pam_ctc.material_outsource_cost_detail t
where id in
(
20250225202,
20250225203
);

-- 更新脚本，影响行数2
UPDATE pam_ctc.material_outsource_cost_detail SET deleted_flag = 1 WHERE id=20250225202;
UPDATE pam_ctc.material_outsource_cost_detail SET deleted_flag = 1 WHERE id=20250225203;
-- 新增脚本
INSERT INTO pam_ctc.material_outsource_cost_detail (id,cost_collection_id,`type`,vendor_id,vendor_code,vendor_name,vendor_site_code,purchase_contract_code,purchase_contract_name,invoice_currency,payment_invoice_id,invoice_date,ap_invoice_code,invoice_amount,tax_amount,local_currency,local_currency_amount,conversion_rate,create_by,create_at,deleted_flag)
	VALUES (20250426001,20250426101,1,913565228640239616,'Z2168528','常州莱恩科技有限公司-其他','常州莱恩','HCG22070014','广西前海人寿医院 安装调试及维护合同','CNY',1080123693507543040,'2021-09-30 00:00:00','HFP20210930108',-400000.000000,-24000.000000,'CNY',-376000.000000,1.000000,-2,'2025-04-26 14:13:57',0);
INSERT INTO pam_ctc.material_outsource_cost_detail (id,cost_collection_id,`type`,vendor_id,vendor_code,vendor_name,vendor_site_code,purchase_contract_code,purchase_contract_name,invoice_currency,payment_invoice_id,invoice_date,ap_invoice_code,invoice_amount,tax_amount,local_currency,local_currency_amount,conversion_rate,create_by,create_at,deleted_flag)
	VALUES (20250426002,20250426102,1,891157355616534528,'Z0042532','武汉隆睿达建筑劳务有限公司-其他','武汉隆睿达','HCG21090034','P-016044 淮北 武汉隆睿达 安装合同','CNY',1209536755111587840,'2021-03-22 00:00:00','HFP20231227001',-145924.000000,-13133.160000,'CNY',-132790.840000,1.000000,-2,'2025-04-26 14:13:57',0);



-- 备份脚本(material_get_detail)
CREATE TABLE pam_backup.material_get_detail_20250509_ex_liangjy41_3 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗合同履约修复' as modify_remark,
  'AAIG035768，【数据修复】医疗合同履约修复' as flow_title,
  t.*
FROM pam_ctc.material_get_detail t
where id in
(
991470521755369472,
991470521767952384,
991470521788923904
);

-- 更新脚本，影响行数3
UPDATE pam_ctc.material_get_detail SET actual_cost = 313.8875 WHERE id=991470521755369472;
UPDATE pam_ctc.material_get_detail SET actual_cost = 161.64 WHERE id=991470521767952384;
UPDATE pam_ctc.material_get_detail SET actual_cost = 647.375 WHERE id=991470521788923904;



-- 新增脚本(material_actual_cost_detail)
INSERT INTO pam_ctc.material_actual_cost_detail (id,cost_collection_id,material_form_id,`type`,code,material_code,material_name,unit,actual_amount,actual_date,actual_cost,actual_price,actual_price_date,trace_flag,create_by,create_at,deleted_flag)
	VALUES (20250426003,20250426103,991470521755369472,1,'HLL220628023','A0035102000458','Velcro ring #loading diameter 120 mm - 50 pcs NW160#Transponet##0006085','PC',4.000000,'2022-11-30 10:06:14',-251.022385,0.000000,'2022-12-02',1,-2,'2025-04-26 15:03:58',0);
INSERT INTO pam_ctc.material_actual_cost_detail (id,cost_collection_id,material_form_id,`type`,code,material_code,material_name,unit,actual_amount,actual_date,actual_cost,actual_price,actual_price_date,trace_flag,create_by,create_at,deleted_flag)
	VALUES (20250426004,20250426103,991470521767952384,1,'HLL220628023','A0031106000041','Gear Set Motor#for New Type Front load station  top load station  Divert#Transponet##0005814','PC',1.000000,'2022-11-30 10:06:14',-159.124413,0.000000,'2022-12-02',1,-2,'2025-04-26 15:03:58',0);
INSERT INTO pam_ctc.material_actual_cost_detail (id,cost_collection_id,material_form_id,`type`,code,material_code,material_name,unit,actual_amount,actual_date,actual_cost,actual_price,actual_price_date,trace_flag,create_by,create_at,deleted_flag)
	VALUES (20250426005,20250426103,991470521788923904,1,'HLL220628023','A0033105000211','PCB Unit 202#Diverter PCB#Transponet##0016365','PC',2.000000,'2022-11-30 10:06:14',-619.024560,0.000000,'2022-12-02',1,-2,'2025-04-26 15:03:58',0);
INSERT INTO pam_ctc.material_actual_cost_detail (id,cost_collection_id,material_form_id,`type`,code,material_code,material_name,unit,actual_amount,actual_date,actual_cost,actual_price,actual_price_date,trace_flag,create_by,create_at,deleted_flag)
	VALUES (20250426006,20250426103,991470521755369472,1,'HLL220628023','A0035102000458','Velcro ring #loading diameter 120 mm - 50 pcs NW160#Transponet##0006085','PC',4.000000,'2022-11-30 10:06:14',313.8875,0.000000,'2022-12-02',1,-2,'2025-04-26 15:03:58',0);
INSERT INTO pam_ctc.material_actual_cost_detail (id,cost_collection_id,material_form_id,`type`,code,material_code,material_name,unit,actual_amount,actual_date,actual_cost,actual_price,actual_price_date,trace_flag,create_by,create_at,deleted_flag)
	VALUES (20250426007,20250426103,991470521767952384,1,'HLL220628023','A0031106000041','Gear Set Motor#for New Type Front load station  top load station  Divert#Transponet##0005814','PC',1.000000,'2022-11-30 10:06:14',161.64,0.000000,'2022-12-02',1,-2,'2025-04-26 15:03:58',0);
INSERT INTO pam_ctc.material_actual_cost_detail (id,cost_collection_id,material_form_id,`type`,code,material_code,material_name,unit,actual_amount,actual_date,actual_cost,actual_price,actual_price_date,trace_flag,create_by,create_at,deleted_flag)
	VALUES (20250426008,20250426103,991470521788923904,1,'HLL220628023','A0033105000211','PCB Unit 202#Diverter PCB#Transponet##0016365','PC',2.000000,'2022-11-30 10:06:14',647.375,0.000000,'2022-12-02',1,-2,'2025-04-26 15:03:58',0);



-- 备份脚本(formal_material_get_and_return)
CREATE TABLE pam_backup.formal_material_get_and_return_20250509_ex_liangjy41_2 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗合同履约修复' as modify_remark,
  'AAIG035768，【数据修复】医疗合同履约修复' as flow_title,
  t.*
FROM pam_ctc.formal_material_get_and_return t
where id in
(
1177594516497461248,
1255205953469833216
);

-- 更新脚本，影响行数2
UPDATE pam_ctc.formal_material_get_and_return SET project_id = 1329508450469101568,project_code = 'CSSPS2025011601',project_name='中易建设有限公司德国气动备件采购' WHERE id=1177594516497461248;
UPDATE pam_ctc.formal_material_get_and_return SET project_id = 1329508450469101568,project_code = 'CSSPS2025011601',project_name='中易建设有限公司德国气动备件采购' WHERE id=1255205953469833216;


-- 新增脚本(fee_cost_detail)
INSERT INTO pam_ctc.fee_cost_detail (id,cost_collection_id,`type`,type_code,gl_date,invoice_currency,invoice_amount,local_currency,local_currency_amount,create_by,create_at,deleted_flag,order_line_id,order_code)
	VALUES (20250426300,20250426104,'增值税-已交税金','JJSX0239','2023-11-29 00:00:00','人民币元',-24.980000,'人民币元',-24.98,-2,'2025-04-26 23:06:28',0,'1004180820061208577','EC231124614624');
INSERT INTO pam_ctc.fee_cost_detail (id,cost_collection_id,`type`,type_code,gl_date,invoice_currency,invoice_amount,local_currency,local_currency_amount,create_by,create_at,deleted_flag,order_line_id,order_code)
	VALUES (20250426301,20250426105,'差旅费','JJSX0420','2024-01-18 00:00:00','人民币元',-223.660000,'人民币元',-223.66,-2,'2025-04-26 23:06:28',0,'1023403152692621312','DD1023081054929690624');
INSERT INTO pam_ctc.fee_cost_detail (id,cost_collection_id,`type`,type_code,gl_date,invoice_currency,invoice_amount,local_currency,local_currency_amount,create_by,create_at,deleted_flag,order_line_id,order_code)
	VALUES (20250426302,20250426105,'差旅费','JJSX0420','2024-01-18 00:00:00','人民币元',6.510000,'人民币元',6.51,-2,'2025-04-26 23:06:28',0,'1023403152843616257','DD1023081054929690624');
INSERT INTO pam_ctc.fee_cost_detail (id,cost_collection_id,`type`,type_code,gl_date,invoice_currency,invoice_amount,local_currency,local_currency_amount,create_by,create_at,deleted_flag,order_line_id,order_code)
	VALUES (20250426303,20250426106,'差旅费','JJSX0420','2024-01-24 00:00:00','人民币元',-296.800000,'人民币元',-296.8,-2,'2025-04-26 23:06:28',0,'1024150492806262784','XC1022957892539514880');
INSERT INTO pam_ctc.fee_cost_detail (id,cost_collection_id,`type`,type_code,gl_date,invoice_currency,invoice_amount,local_currency,local_currency_amount,create_by,create_at,deleted_flag,order_line_id,order_code)
	VALUES (20250426304,20250426106,'差旅费','JJSX0420','2024-01-24 00:00:00','人民币元',16.800000,'人民币元',16.8,-2,'2025-04-26 23:06:28',0,'1024150492890148865','XC1022957892539514880');
