-- 备份脚本
CREATE TABLE pam_backup.project_20250509_1_ygx_3 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】PAM-楼宇科技研究院组织因业务需求重新启用，原相应设置不完整。原预算主体已失效，申请关联新的预算主体：楼宇科技事业部-内销-PAM项目专用，同时历史项目的预算树以及预算单元需要切换到新的预算主体。' as modify_remark,
  'AAIG032562，【预算树切换】楼研主体历史项目的预算树切换' as flow_title,
  t.*
FROM pam_ctc.project t
where id in
(
1124353454051950592,
1164574969044459520,
1196497779951095808
)
;
-- 更新脚本，影响行数3
update pam_ctc.project set update_at = now(), budget_dept_id = 979027417588826112, budget_header_id = 1175952383763693568, budget_header_name = '解决方案与交付-PAM专用预算'
where id in
(
1124353454051950592,
1164574969044459520,
1196497779951095808
)
;



-- 备份脚本
CREATE TABLE pam_backup.project_fee_collection_20250509_1_ygx_10 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】PAM-楼宇科技研究院组织因业务需求重新启用，原相应设置不完整。原预算主体已失效，申请关联新的预算主体：楼宇科技事业部-内销-PAM项目专用，同时历史项目的预算树以及预算单元需要切换到新的预算主体。' as modify_remark,
  'AAIG032562，【预算树切换】楼研主体历史项目的预算树切换' as flow_title,
  t.*
FROM pam_ctc.project_fee_collection t
where id in
(
1130875840121274368,
1130875840192577536,
1130875840255492096,
1156963534734999552,
1166010360268185600,
1166010360381431808,
1166010360414986240,
1308769207379783680,
1308769207488835584,
1308769207514001408
)
;
-- 更新脚本，影响行数10
update pam_ctc.project_fee_collection set update_at = now(), ems_budget_id = null, push_status = 0
where id in
(
1130875840121274368,
1130875840192577536,
1130875840255492096,
1156963534734999552,
1166010360268185600,
1166010360381431808,
1166010360414986240,
1308769207379783680,
1308769207488835584,
1308769207514001408
)
;



-- 备份脚本
CREATE TABLE pam_backup.resend_execute_20250509_1_ygx_10 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】PAM-楼宇科技研究院组织因业务需求重新启用，原相应设置不完整。原预算主体已失效，申请关联新的预算主体：楼宇科技事业部-内销-PAM项目专用，同时历史项目的预算树以及预算单元需要切换到新的预算主体。' as modify_remark,
  'AAIG032562，【预算树切换】楼研主体历史项目的预算树切换' as flow_title,
  t.*
FROM pam_ctc.resend_execute t
where id in
(
1130875840171606016,
1130875840205160448,
1130875840268075008,
1156963534764359680,
1166010360301740032,
1166010360398209024,
1166010360431763456,
1308769207472058368,
1308769207501418496,
1308769207526584320
)
;
-- 更新脚本，影响行数10
update pam_ctc.resend_execute set update_at = now(), esb_serial_no = null, status = 0
where id in
(
1130875840171606016,
1130875840205160448,
1130875840268075008,
1156963534764359680,
1166010360301740032,
1166010360398209024,
1166010360431763456,
1308769207472058368,
1308769207501418496,
1308769207526584320
)
;



