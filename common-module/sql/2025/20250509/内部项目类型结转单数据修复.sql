-- 备份脚本(carryover_cost_accounting)
CREATE TABLE pam_backup.carryover_cost_accounting_20250509_ex_liangjy41_6 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】内部项目类型结转单数据修复' as modify_remark,
  'AAIG035768，【数据修复】内部项目类型结转单数据修复' as flow_title,
  t.*
FROM pam_ctc.carryover_cost_accounting t
where id in
(
1356303378771476480,
1356303437739196416,
1356303438016020480,
1356303488863567872,
1366003125771292672,
1366003148059824128
);

-- 更新脚本，影响行数6
update
	pam_ctc.carryover_cost_accounting
set
	gl_period = '2025-04',
	gl_date = '2025-04-27 00:00:00',
	cost_subject = '133602.0.**********.0.K27.0.0'
where id in
(
1356303378771476480,
1356303437739196416,
1356303438016020480,
1356303488863567872,
1366003125771292672,
1366003148059824128
);



-- 备份脚本(carryover_bill)
CREATE TABLE pam_backup.carryover_bill_20250509_ex_liangjy41_3 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】内部项目类型结转单数据修复' as modify_remark,
  'AAIG035768，【数据修复】内部项目类型结转单数据修复' as flow_title,
  t.*
FROM pam_ctc.carryover_bill t
where id in
(
1356300033814114304,
1356300034908827648,
1356300036624297984
);

-- 更新脚本，影响行数3
update
	pam_ctc.carryover_bill
set
	period_name = '2025-04'
where id in
(
1356300033814114304,
1356300034908827648,
1356300036624297984
);