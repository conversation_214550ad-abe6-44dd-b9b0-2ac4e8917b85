-- 备份脚本(project_income_cost_plan)
CREATE TABLE pam_backup.project_income_cost_plan_20250509_ex_liangjy41_5 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目收入计划&初始化工单' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目收入计划&初始化工单' as flow_title,
  t.*
FROM pam_ctc.project_income_cost_plan t
where id in
(
1226888583752114176,
1226888583773085696,
1226888583798251520,
1226888583815028736,
1227197897993568256
);

-- 更新脚本，影响行数5
update
	pam_ctc.project_income_cost_plan
set
	carryover_bill_id = null,
	carryover_batch_num = null,
	carry_status = '0'
where id in
(
1226888583752114176,
1226888583773085696,
1226888583798251520,
1226888583815028736,
1227197897993568256
);



-- 备份脚本(revenue_cost_orde)
CREATE TABLE pam_backup.revenue_cost_order_2_20250509_ex_liangjy41_4 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目收入计划&初始化工单' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目收入计划&初始化工单' as flow_title,
  t.*
FROM pam_ctc.revenue_cost_order t
where id in
(
1226935664507133952,
1226935664570048512,
1226935664591020032,
1226935664611991552
);

-- 更新脚本，影响行数4
update
	pam_ctc.revenue_cost_order
set
	deleted_flag = 1
where id in
(
1226935664507133952,
1226935664570048512,
1226935664591020032,
1226935664611991552
);



-- 备份脚本(revenue_cost_order_detail)
CREATE TABLE pam_backup.revenue_cost_order_detail_2_20250509_ex_liangjy41_4 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目收入计划&初始化工单' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目收入计划&初始化工单' as flow_title,
  t.*
FROM pam_ctc.revenue_cost_order_detail t
where id in
(
1226935664540688384,
1226935664582631424,
1226935664603602944,
1226935664624574464
);


-- 更新脚本，影响行数4
update
	pam_ctc.revenue_cost_order_detail
set
	deleted_flag = 1
where id in
(
1226935664540688384,
1226935664582631424,
1226935664603602944,
1226935664624574464
);




