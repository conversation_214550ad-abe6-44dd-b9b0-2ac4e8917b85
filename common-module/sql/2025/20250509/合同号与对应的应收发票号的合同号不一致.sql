-- 备份脚本(invoice_apply_header)
CREATE TABLE pam_backup.invoice_apply_header_20250509_ex_liangjy41_3 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】HFP24090008 的合同号与对应的应收发票号的合同号不一致' as modify_remark,
  'AAIG036051，【数据修复】HFP24090008 的合同号与对应的应收发票号的合同号不一致' as flow_title,
  t.*
FROM pam_ctc.invoice_apply_header t
where id in
(
1283137682239127552,
1288123780623212544,
1288149382229925888
);

-- 更新脚本，影响行数3
update
	pam_ctc.invoice_apply_header
set
	contract_id = 1217785920339046400
where id in
(
1283137682239127552,
1288123780623212544,
1288149382229925888
);


-- 备份脚本(invoice_apply_details)
CREATE TABLE pam_backup.invoice_apply_details_20250509_ex_liangjy41_3 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】HFP24090008 的合同号与对应的应收发票号的合同号不一致' as modify_remark,
  'AAIG036051，【数据修复】HFP24090008 的合同号与对应的应收发票号的合同号不一致' as flow_title,
  t.*
FROM pam_ctc.invoice_apply_details t
where id in
(
1283137682260099072,
1288123780652572672,
1288149382246703104
);

-- 更新脚本，影响行数3
update
	pam_ctc.invoice_apply_details
set
	contract_id = 1217785920339046400,
	contract_code = 'HSZ24030017',
	contract_name = '龙岗区妇幼保健院一期-PTS-子合同2',
	plan_id = 1220008535090511872,
	plan_detail_id = 1220008535098900480,
	code = 'HKP240314006'
where id in
(
1283137682260099072,
1288123780652572672,
1288149382246703104
);


-- 备份脚本(invoice_apply_receivable_ages)
CREATE TABLE pam_backup.invoice_apply_receivable_ages_20250509_ex_liangjy41_1 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】HFP24090008 的合同号与对应的应收发票号的合同号不一致' as modify_remark,
  'AAIG036051，【数据修复】HFP24090008 的合同号与对应的应收发票号的合同号不一致' as flow_title,
  t.*
FROM pam_ctc.invoice_apply_receivable_ages t
where id = 1299468758666149888;

-- 更新脚本，影响行数1
update
	pam_ctc.invoice_apply_receivable_ages
set
	contract_id = 1217785920339046400,
	contract_code = 'HSZ24030017'
where
	id = 1299468758666149888;