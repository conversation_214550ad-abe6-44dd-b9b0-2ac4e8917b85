-- 类型：数据修复
-- 修复原因：历史初始化数据的收款分配金额错误，需更正分配金额。
-- receipt_claim_contract_rel备份脚本
create TABLE pam_backup.receipt_claim_contract_rel_20250509_chenchong_4 (
	`id` bigint(20) NOT NULL comment 'ID',
	PRIMARY KEY (`id`)
) AS
select
	'历史初始化数据的收款分配金额错误，需更正分配金额。' as modify_remark,
	'关联需求AAIG035057,【数据修复】开票计划的actual_receipt_amount值错误' as flow_title,
	t.*
from
	pam_ctc.receipt_claim_contract_rel t
where
	t.id in (
	    1016436299273338880,1016436299302699008,1016436299340447744,1016436299374002176
	);

-- 依据产品提供的数据进行修复 ,影响行数【4】,无需更新时间
update pam_ctc.receipt_claim_contract_rel set local_allocated_amount = 72007667.54, allocated_amount = 72007667.54
where id = 1016436299273338880 and local_allocated_amount = 77456896.55 and allocated_amount = 77456896.55;
update pam_ctc.receipt_claim_contract_rel set local_allocated_amount = 17970000.01, allocated_amount = 17970000.01
where id = 1016436299302699008 and local_allocated_amount = 1758082.36 and allocated_amount = 1758082.36;
update pam_ctc.receipt_claim_contract_rel set local_allocated_amount = 1670034.18, allocated_amount = 1670034.18
where id = 1016436299340447744 and local_allocated_amount = 1477906.35 and allocated_amount = 1477906.35;
update pam_ctc.receipt_claim_contract_rel set local_allocated_amount = 6313519.55, allocated_amount = 6313519.55
where id = 1016436299374002176 and local_allocated_amount = 5587185.44 and allocated_amount = 5587185.44;
