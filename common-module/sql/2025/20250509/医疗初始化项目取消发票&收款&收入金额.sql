-- 备份脚本(invoice_receivable)
CREATE TABLE pam_backup.invoice_receivable_20250509_ex_liangjy41_17 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.invoice_receivable t
where id in
(
1222646606378672128,
1222646606424809472,
1222646606462558208,
1222646606500306944,
1222646606538055680,
1222646606575804416,
1222646606659690496,
1222646606705827840,
1222646606739382272,
1222646606772936704,
1222646606802296832,
1222646606840045568,
1222646606869405696,
1222646606907154432,
1222646606974263296,
1222646607012012032,
1222646607045566464
);

-- 更新脚本，影响行数17
update
	pam_ctc.invoice_receivable
set
	deleted_flag = 1
where
	id in
(
1222646606378672128,
1222646606424809472,
1222646606462558208,
1222646606500306944,
1222646606538055680,
1222646606575804416,
1222646606659690496,
1222646606705827840,
1222646606739382272,
1222646606772936704,
1222646606802296832,
1222646606840045568,
1222646606869405696,
1222646606907154432,
1222646606974263296,
1222646607012012032,
1222646607045566464
);



-- 备份脚本(invoice_apply_header)
CREATE TABLE pam_backup.invoice_apply_header_20250509_ex_liangjy41_17 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.invoice_apply_header t
where id in
(
1222646264936992768,
1222646265108959232,
1222646265259954176,
1222646265410949120,
1222646265578721280,
1222646265729716224,
1222646265893294080,
1222646266040094720,
1222646266191089664,
1222646266358861824,
1222646266522439680,
1222646266660851712,
1222646266795069440,
1222646266933481472,
1222646267235471360,
1222646267382272000,
1222646267529072640
);

-- 更新脚本，影响行数17
update
	pam_ctc.invoice_apply_header
set
	deleted_flag = 1
where id in
(
1222646264936992768,
1222646265108959232,
1222646265259954176,
1222646265410949120,
1222646265578721280,
1222646265729716224,
1222646265893294080,
1222646266040094720,
1222646266191089664,
1222646266358861824,
1222646266522439680,
1222646266660851712,
1222646266795069440,
1222646266933481472,
1222646267235471360,
1222646267382272000,
1222646267529072640
);


-- 备份脚本(invoice_plan_detail)
CREATE TABLE pam_backup.invoice_plan_detail_20250509_ex_liangjy41_8 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.invoice_plan_detail t
where id in
(
1222575810167808000,
1222575810197168128,
1222630568756879360,
1222630568794628096,
1222640802064515072,
1222640802077097984,
1222640802165178368,
1222640802173566976
);

-- 更新脚本，影响行数8
update
	pam_ctc.invoice_plan_detail
set
	tax_included_price = 0,exclusive_of_tax = 0,actual_receipt_amount = 0
where id in
(
1222575810167808000,
1222575810197168128,
1222630568756879360,
1222630568794628096,
1222640802064515072,
1222640802077097984,
1222640802165178368,
1222640802173566976
);



-- 备份脚本(receipt_claim_contract_rel)
CREATE TABLE pam_backup.receipt_claim_contract_rel_20250509_ex_liangjy41_17 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.receipt_claim_contract_rel t
where id in
(
1222647096746897408,
1222647096801423360,
1222647096834977792,
1222647096868532224,
1222647096902086656,
1222647096935641088,
1222647096973389824,
1222647097006944256,
1222647097036304384,
1222647097095024640,
1222647097149550592,
1222647097195687936,
1222647097246019584,
1222647097292156928,
1222647097417986048,
1222647097472512000,
1222647097527037952
);

-- 更新脚本，影响行数17
update
	pam_ctc.receipt_claim_contract_rel
set
	deleted_flag = 1
where id in
(
1222647096746897408,
1222647096801423360,
1222647096834977792,
1222647096868532224,
1222647096902086656,
1222647096935641088,
1222647096973389824,
1222647097006944256,
1222647097036304384,
1222647097095024640,
1222647097149550592,
1222647097195687936,
1222647097246019584,
1222647097292156928,
1222647097417986048,
1222647097472512000,
1222647097527037952
);


-- 备份脚本(receipt_claim_detail)
CREATE TABLE pam_backup.receipt_claim_detail_20250509_ex_liangjy41_17 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.receipt_claim_detail t
where id in
(
1222647096725925888,
1222647096788840448,
1222647096826589184,
1222647096860143616,
1222647096893698048,
1222647096927252480,
1222647096960806912,
1222647096994361344,
1222647097027915776,
1222647097082441728,
1222647097132773376,
1222647097183105024,
1222647097229242368,
1222647097279574016,
1222647097401208832,
1222647097459929088,
1222647097514455040
);

-- 更新脚本，影响行数17
update
	pam_ctc.receipt_claim_detail
set
	deleted_flag = 1
where id in
(
1222647096725925888,
1222647096788840448,
1222647096826589184,
1222647096860143616,
1222647096893698048,
1222647096927252480,
1222647096960806912,
1222647096994361344,
1222647097027915776,
1222647097082441728,
1222647097132773376,
1222647097183105024,
1222647097229242368,
1222647097279574016,
1222647097401208832,
1222647097459929088,
1222647097514455040
);



-- 备份脚本(receipt_claim)
CREATE TABLE pam_backup.receipt_claim_20250509_ex_liangjy41_17 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.receipt_claim t
where id in
(
1222647096663011328,
1222647096772063232,
1222647096814006272,
1222647096847560704,
1222647096881115136,
1222647096914669568,
1222647096952418304,
1222647096985972736,
1222647097019527168,
1222647097048887296,
1222647097115996160,
1222647097166327808,
1222647097216659456,
1222647097262796800,
1222647097380237312,
1222647097443151872,
1222647097497677824
);

-- 更新脚本，影响行数17
update
	pam_ctc.receipt_claim
set
	deleted_flag = 1
where id in
(
1222647096663011328,
1222647096772063232,
1222647096814006272,
1222647096847560704,
1222647096881115136,
1222647096914669568,
1222647096952418304,
1222647096985972736,
1222647097019527168,
1222647097048887296,
1222647097115996160,
1222647097166327808,
1222647097216659456,
1222647097262796800,
1222647097380237312,
1222647097443151872,
1222647097497677824
);



-- 备份脚本(receipt_plan)
CREATE TABLE pam_backup.receipt_plan_20250509_ex_liangjy41_4 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.receipt_plan t
where id in
(
1222576376863977472,
1222631199236100096,
1222641642418487296,
1222641642506567680
);


-- 更新脚本，影响行数4
update
	pam_ctc.receipt_plan
set
	actual_amount = 0
where id in
(
1222576376863977472,
1222631199236100096,
1222641642418487296,
1222641642506567680
);



-- 备份脚本(receipt_plan_detail)
CREATE TABLE pam_backup.receipt_plan_detail_20250509_ex_liangjy41_17 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.receipt_plan_detail t
where id in
(
1222576376876560384,
1222576376889143296,
1222576376901726208,
1222576376910114816,
1222576376922697728,
1222576376935280640,
1222576376947863552,
1222576376960446464,
1222576376973029376,
1222576376981417984,
1222576376994000896,
1222576377006583808,
1222576377019166720,
1222576377031749632,
1222631199257071616,
1222641642431070208,
1222641642514956288
);

-- 更新脚本，影响行数17
update
	pam_ctc.receipt_plan_detail
set
	actual_amount = 0
where id in
(
1222576376876560384,
1222576376889143296,
1222576376901726208,
1222576376910114816,
1222576376922697728,
1222576376935280640,
1222576376947863552,
1222576376960446464,
1222576376973029376,
1222576376981417984,
1222576376994000896,
1222576377006583808,
1222576377019166720,
1222576377031749632,
1222631199257071616,
1222641642431070208,
1222641642514956288
);



-- 备份脚本(carryover_bill)
CREATE TABLE pam_backup.carryover_bill_20250509_ex_liangjy41_5 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.carryover_bill t
where id in
(
1226935042013700096,
1226935043213271040,
1226935044417036288,
1226935045545304064,
1226935046681960448
);

-- 更新脚本，影响行数4
update
	pam_ctc.carryover_bill
set
	deleted_flag = 1
where id in
(
1226935042013700096,
1226935043213271040,
1226935044417036288,
1226935045545304064
);

-- 更新脚本，影响行数1
update
	pam_ctc.carryover_bill
set
	current_income_percent = 1,
	standard_current_income_total_amount = 1155280.78,
	total_income = 1155280.78,
	current_income_amount = 1155280.78,
	current_income_total_amount = 1155280.78,
	cumulative_income_total = 1155280.78,
	gross_profit_rate =-107.4,
	cumulative_income_percent = 1
where
	id = 1226935046681960448;



-- 备份脚本(revenue_cost_order)
CREATE TABLE pam_backup.revenue_cost_order_20250509_ex_liangjy41_4 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.revenue_cost_order t
where id in
(
1226935664507133952,
1226935664570048512,
1226935664591020032,
1226935664611991552
);

-- 更新脚本，影响行数4
update
	pam_ctc.revenue_cost_order
set
	carryover_status = 0
where id in
(
1226935664507133952,
1226935664570048512,
1226935664591020032,
1226935664611991552
);



-- 备份脚本(revenue_cost_order_detail)
CREATE TABLE pam_backup.revenue_cost_order_detail_20250509_ex_liangjy41_4 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】医疗初始化项目取消发票&收款&收入金额' as modify_remark,
  'AAIG035768，【数据修复】医疗初始化项目取消发票&收款&收入金额' as flow_title,
  t.*
FROM pam_ctc.revenue_cost_order_detail t
where id in
(
1226935664540688384,
1226935664582631424,
1226935664603602944,
1226935664624574464
);

-- 更新脚本，影响行数4
update
	pam_ctc.revenue_cost_order_detail
set
	carryover_status = 0,
	carryover_bill_id = null
where id in
(
1226935664540688384,
1226935664582631424,
1226935664603602944,
1226935664624574464
);




