-- 备份脚本
create table pam_backup.carryover_bill_income_collection_detail_20250704_xiedl_4 (
       `id` bigint(20) NOT NULL COMMENT 'ID',
       PRIMARY KEY (`id`)
) as
select
    '【一致性】收入明细表与结转表核对-原币：carryover_bill_income_collection_detail 与carryover_bill 的本期收入原币不一致的数据' as modify_remark,
    'PAM-Sprint11入库脚本_DML(增删改数据)_20250701' as flow_title,
    t.*
from pam_ctc.carryover_bill_income_collection_detail t
where id in (1168841732390072320,1190330180162846720,1244642753145864192,1284558918349377536);

-- 更新脚本
UPDATE pam_ctc.carryover_bill_income_collection_detail
	SET current_income_amount=32031.0
	WHERE id=1168841732390072320;
UPDATE pam_ctc.carryover_bill_income_collection_detail
	SET current_income_amount=2004.49
	WHERE id=1190330180162846720;
UPDATE pam_ctc.carryover_bill_income_collection_detail
	SET current_income_amount=43164.28
	WHERE id=1244642753145864192;
UPDATE pam_ctc.carryover_bill_income_collection_detail
	SET current_income_amount=32031.0
	WHERE id=1284558918349377536;

-- 备份脚本
create table pam_backup.carryover_bill_income_collection_20250704_xiedl_4 (
     `id` bigint(20) NOT NULL COMMENT 'ID',
     PRIMARY KEY (`id`)
) as
select
    '【一致性】收入明细表与结转表核对-原币：carryover_bill_income_collection_detail 与carryover_bill 的本期收入原币不一致的数据' as modify_remark,
    'PAM-Sprint11入库脚本_DML(增删改数据)_20250701' as flow_title,
    t.*
from pam_ctc.carryover_bill_income_collection t
where id in (1168841732369100800,1190330180120903680,1244642753124892672,1284558918324211712);

-- 更新脚本
update pam_ctc.carryover_bill_income_collection set current_income_amount = 32031 where id = 1168841732369100800;
update pam_ctc.carryover_bill_income_collection set current_income_amount = 2004.49 where id = 1190330180120903680;
update pam_ctc.carryover_bill_income_collection set current_income_amount = 43164.28 where id = 1244642753124892672;
update pam_ctc.carryover_bill_income_collection set current_income_amount = 32031 where id = 1284558918324211712;


-- 备份脚本
create table pam_backup.carryover_bill_20250704_xiedl_4 (
     `id` bigint(20) NOT NULL COMMENT 'ID',
     PRIMARY KEY (`id`)
) as
select
    '【一致性】收入明细表与结转表核对-原币：carryover_bill_income_collection_detail 与carryover_bill 的本期收入原币不一致的数据' as modify_remark,
    'PAM-Sprint11入库脚本_DML(增删改数据)_20250701' as flow_title,
    t.*
from pam_ctc.carryover_bill t
where id in (1366085807054245888,
    1377958224096284672,
1377958454040612864,
1377974740767698944,
1377974930730610688,
1378099233456566272);

-- 更新脚本
update pam_ctc.carryover_bill set current_cost_actual = 0
where id in (1366085807054245888,
             1377958224096284672,
             1377958454040612864,
             1377974740767698944,
             1377974930730610688,
             1378099233456566272);

--  Auto-generated SQL script #************
INSERT INTO pam_ctc.material_get_header (id,status,get_code,fill_user_name,get_user_name,project_id,project_code,project_name,organization_id,organization_code,organization_name,account_alias_concat,total_apply_amount,total_actual_amount,total_difference_amount,inventory_id,inventory_code,inventory_name,remark,material_get_type,apply_time,erp_code,deleted_flag,create_at,create_by,is_import)
	VALUES (***********,5,'SLL220901143137','ERP','ERP',1014573048243683328,'F70074','Hofer_Wuxi Hofer EDU EOL Assembly Line',100282,'MK3','INV_MK3_库卡柔性系统（上海）有限公司','133602.0.**********.0.0.0.0',1.00000,1.00000,0.00000,1011281519538339840,'MA1107','费用仓库','初始化,采购合同进度',2,'2022-09-22 11:36:54','000000',0,'2022-09-30 09:02:00',********,1);

INSERT INTO pam_ctc.material_get_detail (id,header_id,project_id,project_code,inventory_id,inventory_code,inventory_name,material_id,material_erp_id,material_code,material_name,unit,apply_amount,actual_amount,actual_cost,difference_amount,erp_code,remark,trade_time,collection_date,deleted_flag,create_at,create_by,update_at,item_cost_is_null,transition_date,org_id,wbs_full_code,wbs_summary_code,wbs_description,activity_code,is_import)
	VALUES (20250702002,***********,1014573048243683328,'F70074',1011281519538339840,'MA1107','费用仓库',1012319835750662144,10800360,'A9900101002619','Commercial Material Credits to be Received - Invoice Only#CreditMchMtl','Ge',1.00000,1.00000,39587.1,0.00000,'2','初始化,采购合同进度','2021-05-07 00:00:00','2021-05-07',0,'2022-09-07 15:46:31',********,'2022-09-07 15:46:31',0,'2021-05-07 00:00:00',100282,'00-025-500','F70074-00-025-500','OP2080 Basic Machine','0.1.1',1);


-- 备份脚本
create table pam_backup.material_return_header_20250704_xiedl_1 (
     `id` bigint(20) NOT NULL COMMENT 'ID',
     PRIMARY KEY (`id`)
) as
select
    'F70074 柔性初始化退料单错误' as modify_remark,
    'PAM-Sprint11入库脚本_DML(增删改数据)_20250704' as flow_title,
    t.*
from pam_ctc.material_return_header t
WHERE id=1017095536248881152;

-- 更新脚本，影响行数1
UPDATE pam_ctc.material_return_header
	SET deleted_flag=1
	WHERE id=1017095536248881152;

-- 备份脚本
create table pam_backup.material_return_detail_20250704_xiedl_1 (
     `id` bigint(20) NOT NULL COMMENT 'ID',
     PRIMARY KEY (`id`)
) as
select
    'F70074 柔性初始化退料单错误' as modify_remark,
    'PAM-Sprint11入库脚本_DML(增删改数据)_20250704' as flow_title,
    t.*
from pam_ctc.material_return_detail t
WHERE id=1017098555451506689;

-- 更新脚本，影响行数1
UPDATE pam_ctc.material_return_detail
	SET deleted_flag=1
	WHERE id=1017098555451506689;


-- 备份脚本
create table pam_backup.material_actual_cost_detail_20250704_xiedl_1 (
     `id` bigint(20) NOT NULL COMMENT 'ID',
     PRIMARY KEY (`id`)
) as
select
    'F70074 柔性初始化退料单错误' as modify_remark,
    'PAM-Sprint11入库脚本_DML(增删改数据)_20250704' as flow_title,
    t.*
from pam_ctc.material_actual_cost_detail t
WHERE id=1017111785523642369;

-- 更新脚本，影响行数1
UPDATE pam_ctc.material_actual_cost_detail
	SET `type`=1,material_form_id=20250702002,actual_cost=39587.100000,code='SLL220901143137'
	WHERE id=1017111785523642369;

-- 备份脚本
create table pam_backup.invoice_plan_20250704_xiedl_2 (
       `id` bigint(20) NOT NULL COMMENT 'ID',
       PRIMARY KEY (`id`)
) as
select
    '修改税率' as modify_remark,
    'PAM-上海瑞仕格科技有限公司上线-初始化脚本-20250704' as flow_title,
    t.*
from pam_ctc.invoice_plan t
WHERE id in (1388489077620707328,1388489077721370624);

-- 更新脚本，影响行数2
UPDATE pam_ctc.invoice_plan
	SET rate=6
	WHERE id=1388489077620707328;
UPDATE pam_ctc.invoice_plan
	SET rate=6
	WHERE id=1388489077721370624;

-- 备份脚本
create table pam_backup.receipt_plan_20250704_xiedl_3 (
       `id` bigint(20) NOT NULL COMMENT 'ID',
       PRIMARY KEY (`id`)
) as
select
    '修改税率' as modify_remark,
    'PAM-上海瑞仕格科技有限公司上线-初始化脚本-20250704' as flow_title,
    t.*
from pam_ctc.receipt_plan t
WHERE id in (1388507397682044928,1388507398462185472,1389990510526443520);

-- 更新脚本，影响行数3
UPDATE pam_ctc.receipt_plan
	SET amount=9983.000000,actual_amount=0
	WHERE id=1388507397682044928;
UPDATE pam_ctc.receipt_plan
	SET amount=39386500.000000,actual_amount=0
	WHERE id=1388507398462185472;
UPDATE pam_ctc.receipt_plan
	SET amount=228500.0,actual_amount=0.0
	WHERE id=1389990510526443520;


-- 备份脚本
create table pam_backup.contract_product_20250704_xiedl_1 (
       `id` bigint(20) NOT NULL COMMENT 'ID',
       PRIMARY KEY (`id`)
) as
select
    '修改不含税金额' as modify_remark,
    'PAM-上海瑞仕格科技有限公司上线-初始化脚本-20250704' as flow_title,
    t.*
from pam_ctc.contract_product t
WHERE id=1388489073665478656;

-- 更新脚本，影响行数1
UPDATE pam_ctc.contract_product
	SET excluding_tax_amount = 34855309.73
WHERE id=1388489073665478656;