-- 类型：数据修复
-- 修复原因：数据一致性监控检查发现的问题，逻辑调整待后续跟进，错误数据先修复
-- invoice_plan_detail备份脚本
create TABLE pam_backup.invoice_plan_detail_20250703_chenchong_1 (
	`id` bigint(20) NOT NULL comment 'ID',
	PRIMARY KEY (`id`)
) AS
select
	'数据一致性监控检查发现的问题，逻辑调整待后续跟进，错误数据先修复' as modify_remark,
	'关联需求AAIG041026,【数据一致性】合同的已开票金额与按发票合计不一致的数据' as flow_title,
	t.*
from
	pam_ctc.invoice_plan_detail t
where
	t.id  = 1088480852507099136;
-- 依据产品提供的数据进行修复 ,影响行数【1】,无需更新时间
update pam_ctc.invoice_plan_detail set tax_included_price = 160000.54, exclusive_of_tax = 141593.4
where id = 1088480852507099136 and tax_included_price = 140005.25 and exclusive_of_tax = 123898.46;