-- 类型：数据修复
-- 修复原因：数据一致性监控检查发现的问题，逻辑调整待后续跟进，错误数据先修复
-- customer备份脚本
create TABLE pam_backup.customer_20250703_chenchong_1 (
	`id` bigint(20) NOT NULL comment 'ID',
	PRIMARY KEY (`id`)
) AS
select
	'数据一致性监控检查发现的问题，逻辑调整待后续跟进，错误数据先修复' as modify_remark,
	'关联需求AAIG041044,【数据一致性】CRM编码E0开头，inner=0的客户' as flow_title,
	t.*
from
	pam_crm.customer t
where
	t.id  = 639445726392221696;
-- 依据产品提供的数据进行修复 ,影响行数【1】,无需更新时间
update pam_crm.customer set is_inner = 1 where id = 639445726392221696;