-- 采购订单写入FAP
INSERT INTO pam_ctc.interface_param
(id, sys_code, business_type, sub_business_type, component, sync, version, deleted_flag, remark, create_at, create_by, update_at, update_by)
VALUES(9753273341779623, 'FAP', 'PAM-FAP-ITC-A4', '', 'purchaseOrderInputFapService', 1, NULL, 0, '采购订单写入FAP', NULL, NULL, NULL, NULL);

-- 数据字典初始化[采购订单启用关联交易供应商范围]
INSERT INTO pam_basedata.ltc_dict
(id, code, name, `type`, order_num, description, children_codes, tax_rate, create_by, create_at, update_by, update_at, deleted_flag, create_user_id, update_user_id)
VALUES(20250627093141507, 'purchase_order_related_transaction_vendor_scope', '采购订单启用关联交易供应商范围', 'organization_custom_dict', NULL, NULL, NULL, NULL, 1293506204390584320, '2025-06-27 15:27:57', NULL, NULL, 0, NULL, NULL);