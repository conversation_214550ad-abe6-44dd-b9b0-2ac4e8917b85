-- 备份脚本
CREATE TABLE pam_backup.project_profit_ex_wuyh42_1 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】项目类型配置变更 历史终止的项目有成本无法结转 临时调整' as modify_remark,
  '【运维】PD642492 荆州楼宇叉车改造项目  -成本方法修改' as flow_title,
  t.*
FROM pam_ctc.project_profit t
where id = 1319597904621924352 ;


-- 更新脚本
UPDATE pam_ctc.project_profit SET cost_method_main='0410' WHERE id=1319597904621924352;



-- 备份脚本
CREATE TABLE pam_backup.project_wbs_receipts_budget_ex_wuyh42_2 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【数据修复】REL2025060900078提交预算报错' as modify_remark,
  '【数据修复】REL2025060900078提交预算报错' as flow_title,
  t.*
FROM pam_ctc.project_wbs_receipts_budget t
where id in (1381598737761808384, 1381599214111694848);

-- 更新脚本
UPDATE pam_ctc.project_wbs_receipts_budget SET budget_occupied_amount=36000 WHERE id=1381598737761808384;
UPDATE pam_ctc.project_wbs_receipts_budget SET budget_occupied_amount=36000 WHERE id=1381599214111694848;