--insert into pam_basedata.ltc_dict (id, code, name, `type`, order_num, description, children_codes, tax_rate, create_by, create_at, deleted_flag) values
--(20231222002, '128', '不同步法务原因(采购合同)', 'organization_custom_dict', 128, null, null, null, 605063933920804864, '2025-06-06 17:52:03', 0);
--
--INSERT INTO pam_ctc.organization_custom_dict (id, code, name, order_num, description, create_by, create_at, update_by, update_at, deleted_flag, valid_at, expiry_at, value, org_id, org_name, org_from) values
--(202506060000000001, 'PZ20250606001', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 581144287652085760, '广东美的智能机器人', 'company'),
--(202506060000000002, 'PZ20250606002', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 844572813460242432, '广东美控智慧建筑有限公司', 'company'),
--(202506060000000003, 'PZ20250606003', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 846396138532634624, '库卡AMR', 'company'),
--(202506060000000004, 'PZ20250606004', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 814428597413478400, '库卡工业自动化（昆山）有限公司', 'company'),
--(202506060000000005, 'PZ20250606005', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 884455544528568320, '上海瑞仕格医疗科技有限公司', 'company'),
--(202506060000000006, 'PZ20250606006', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 976842952863121408, '广东美的暖通设备有限公司', 'company'),
--(202506060000000007, 'PZ20250606007', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 789091426037137408, '美通能源', 'company'),
--(202506060000000008, 'PZ20250606008', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 1105533683340673024, '广东美的智能机器人有限公司-MRC', 'company'),
--(202506060000000009, 'PZ20250606009', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 1008730763140530176, '库卡柔性系统上海有限公司', 'company'),
--(202506060000000010, 'PZ20250606010', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 1107730770287394816, '楼宇-解决方案与交付', 'company'),
--(202506060000000011, 'PZ20250606011', '不同步法务原因(采购合同)', NULL, NULL, 605063933920804864, '2025-06-06 17:52:03', NULL, NULL, 0, '2025-05-28 00:00:00', NULL, '已在法务系统双签_该合同已在法务系统完成双签,订单式销售无合同_订单式销售，无需签订合同,其它特殊情况_ ,已在法务系统签订框架合同_该合同为基于框架合同创建的订单合同，框架合同已在法务系统完成双签', 1296044147280900096, '广东美的电气有限公司-Systems', 'company');

-- 付款申请合同原件控制 和 PAM采购合同写入法务系统审批
-- 上述配置不随本迭代发版

-- 备份脚本
CREATE TABLE pam_ctc.purchase_contract_20250606_chenchong_all (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '新增字段是否同步法务系统初始化' as modify_remark,
  '关联需求AAIG037524，【迭代开发】WDS合同双签功能' as flow_title,
  t.*
FROM pam_ctc.purchase_contract t;
-- 依据是否存在法务合同编号初始化,影响行数【20039】,无需更新时间
update pam_ctc.purchase_contract set is_synchronize_legal_system_flag = 0 where legal_contract_num is null ;
update pam_ctc.purchase_contract set is_synchronize_legal_system_flag = 1 where legal_contract_num is not null;



