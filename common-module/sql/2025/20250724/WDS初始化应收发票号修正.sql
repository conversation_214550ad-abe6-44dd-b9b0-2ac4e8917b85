-- 备份脚本
create table pam_backup.invoice_receivable_ex_liangjy41_21 (
       `id` bigint(20) not null COMMENT 'ID',
       primary key (`id`)
) as
select
	'WDS初始化应收发票号修正' as modify_remark,
	'AAIG043042,WDS初始化应收发票号修正' as flow_title,
	t.*
from
	pam_ctc.invoice_receivable t
where
	t.id in(1392187779090620416,
            1392187779111591936,
            1392187779136757760,
            1392187779161923584,
            1392187779187089408,
            1392187779208060928,
            1392187779233226752,
            1392187779258392576,
            1392187779279364096,
            1392187779304529920,
            1392187779329695744,
            1392187779354861568,
            1392187779380027392,
            1392187779405193216,
            1392187779426164736,
            1392187779451330560,
            1392187779476496384,
            1392187779497467904,
            1392187779522633728,
            1392187779547799552,
            1392187779568771072);

-- 更新语句
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007337' WHERE id = 1392187779090620416;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007426' WHERE id = 1392187779111591936;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007424' WHERE id = 1392187779136757760;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '100077310' WHERE id = 1392187779161923584;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007484' WHERE id = 1392187779187089408;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007364' WHERE id = 1392187779208060928;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '1400006966' WHERE id = 1392187779233226752;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007183' WHERE id = 1392187779258392576;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '1400006983' WHERE id = 1392187779279364096;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007543' WHERE id = 1392187779304529920;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007407' WHERE id = 1392187779329695744;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007406' WHERE id = 1392187779354861568;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007539' WHERE id = 1392187779380027392;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007534' WHERE id = 1392187779405193216;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007515' WHERE id = 1392187779426164736;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007045' WHERE id = 1392187779451330560;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '1400005891' WHERE id = 1392187779476496384;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007340' WHERE id = 1392187779497467904;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921006954' WHERE id = 1392187779522633728;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '1400006721' WHERE id = 1392187779547799552;
UPDATE pam_ctc.invoice_receivable SET invoice_code = '921007446' WHERE id = 1392187779568771072;

