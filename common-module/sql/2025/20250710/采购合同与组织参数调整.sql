INSERT INTO pam_basedata.ltc_dict
(id, code, name, `type`, order_num, description, children_codes, tax_rate, create_by, create_at, update_by, update_at, deleted_flag, create_user_id, update_user_id)
VALUES(20250710001, '128', '不同步法务原因(采购合同)', 'organization_custom_dict', 98, NULL, NULL, NULL, 605063933920804864, '2025-07-10 17:52:03', NULL, NULL, 0, NULL, NULL);



-- 备份脚本
CREATE TABLE pam_ctc.purchase_contract_20250710_chenchong_24017 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '新增字段是否已双签初始化' as modify_remark,
  '关联需求AAIG041774，【迭代开发】付款申请控制调整' as flow_title,
  t.*
FROM pam_ctc.purchase_contract t
  where deleted_flag = 0;
-- 依据是否存在法务合同编号初始化,影响行数【24017】,无需更新时间
update pam_ctc.purchase_contract set gle_sign_flag = 1 where deleted_flag = 0;




