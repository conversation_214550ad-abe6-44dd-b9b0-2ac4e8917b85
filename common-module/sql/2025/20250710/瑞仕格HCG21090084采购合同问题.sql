-- 备份脚本
create table pam_backup.payment_apply_20250710_ex_liangjy41_1 (
       `id` bigint(20) not null COMMENT 'ID',
       primary key (`id`)
) as
select
	'【修复原因】历史初始化采购合同数据错误，经财务高海涛查账，HCG21090084的付款金额错误，PAM系统多了一笔虚拟的付款申请，需要删除掉。' as modify_remark,
	'关联需求AAIG038106,瑞仕格HCG21090084采购合同问题' as flow_title,
	t.*
from
	pam_ctc.payment_apply t
where
	id = 1058439691293949952;

-- 更新脚本
update
	pam_ctc.payment_apply
set
	deleted_flag = 1
WHERE
    id = 1058439691293949952;


-- 备份脚本
create table pam_backup.payment_apply_detail_rel_20250710_ex_liangjy41_1 (
       `id` bigint(20) not null COMMENT 'ID',
       primary key (`id`)
) as
select
	'【修复原因】历史初始化采购合同数据错误，经财务高海涛查账，HCG21090084的付款金额错误，PAM系统多了一笔虚拟的付款申请，需要删除掉。' as modify_remark,
	'关联需求AAIG038106,瑞仕格HCG21090084采购合同问题' as flow_title,
	t.*
from
	pam_ctc.payment_apply_detail_rel t
where
	id=1058439691314921472;


-- 更新脚本
update
	pam_ctc.payment_apply_detail_rel
set
	deleted_flag = 1
where
	id = 1058439691314921472;



-- 备份脚本
create table pam_backup.payment_apply_invoice_rel_20250710_ex_liangjy41_1 (
       `id` bigint(20) not null COMMENT 'ID',
       primary key (`id`)
) as
select
	'【修复原因】历史初始化采购合同数据错误，经财务高海涛查账，HCG21090084的付款金额错误，PAM系统多了一笔虚拟的付款申请，需要删除掉。' as modify_remark,
	'关联需求AAIG038106,瑞仕格HCG21090084采购合同问题' as flow_title,
	t.*
from
	pam_ctc.payment_apply_invoice_rel t
where
	id = 1058439691310727168;

-- 更新脚本
update
	pam_ctc.payment_apply_invoice_rel
set
	deleted_flag = 1
where
	id = 1058439691310727168;


-- 备份脚本
create table pam_backup.payment_record_20250710_ex_liangjy41_1 (
       `id` bigint(20) not null COMMENT 'ID',
       primary key (`id`)
) as
select
	'【修复原因】历史初始化采购合同数据错误，经财务高海涛查账，HCG21090084的付款金额错误，PAM系统多了一笔虚拟的付款申请，需要删除掉。' as modify_remark,
	'关联需求AAIG038106,瑞仕格HCG21090084采购合同问题' as flow_title,
	t.*
from
	pam_ctc.payment_record t
where
	id=1058439691335892992;

-- 更新脚本
update
	pam_ctc.payment_record
set
	deleted_flag = 1
where
	id = 1058439691335892992;





