-- 备份脚本
CREATE TABLE pam_backup.project_income_cost_plan_20250620_1_ygx_1 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】界面同时打开导致里程碑更新状态错误，已提BUG跟进' as modify_remark,
  'AAIG038511，【数据修复】生产环境，项目：F60084，4月已结转的数据没有正确显示在收入成本计划页面' as flow_title,
  t.*
FROM pam_ctc.project_income_cost_plan t
WHERE id = 1356419312455938048
;
-- 更新脚本，影响行数1
UPDATE pam_ctc.project_income_cost_plan
SET carry_status='1'
WHERE id = 1356419312455938048
;