-- 类型：数据修复
-- 修复原因：昆山航特应收发票初始化核销关系错误修复，需更正
-- receipt_claim_invoice_rel备份脚本
create TABLE pam_backup.receipt_claim_invoice_rel_20250530_chenchong_2 (
	`id` bigint(20) NOT NULL comment 'ID',
	PRIMARY KEY (`id`)
) AS
select
	'昆山航特应收发票初始化核销关系错误修复，需更正' as modify_remark,
	'关联需求AAIG037838,【数据修复】昆山航特应收发票初始化核销关系错误修复' as flow_title,
	t.*
from
	pam_ctc.receipt_claim_invoice_rel t
where
	t.id in (
	    908766017067417600, 908766017147109376
	);

-- 依据产品提供的数据进行修复 ,影响行数【2】,无需更新时间
update pam_ctc.receipt_claim_invoice_rel set deleted_flag = 1 where id = 908766017067417600;
update pam_ctc.receipt_claim_invoice_rel set write_off_amount = 891134.08, receipt_write_off_amount = 891134.08
where id = 908766017147109376 and write_off_amount = 725679.19 and receipt_write_off_amount = 725679.19;



-- 类型：数据修复
-- 修复原因：昆山航特应收发票初始化核销关系错误修复，需更正
-- contract备份脚本
create TABLE pam_backup.contract_20250530_chenchong_2 (
	`id` bigint(20) NOT NULL comment 'ID',
	PRIMARY KEY (`id`)
) AS
select
	'昆山航特应收发票初始化核销关系错误修复，需更正' as modify_remark,
	'关联需求AAIG037838,【数据修复】昆山航特应收发票初始化核销关系错误修复' as flow_title,
	t.*
from
	pam_ctc.contract t
where
	t.id in (
	    1376552846410346496, 1376554747400253440
	);
-- 依据产品提供的数据进行修复 ,影响行数【2】,无需更新时间
update pam_ctc.contract set conversion_rate = 0.000434 where id = 1376552846410346496 and conversion_rate = 0.0004284;
update pam_ctc.contract set conversion_rate = 0.000434 where id = 1376554747400253440 and conversion_rate = 0.0004284;
