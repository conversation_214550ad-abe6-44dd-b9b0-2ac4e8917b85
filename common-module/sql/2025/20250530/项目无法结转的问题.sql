-- 备份脚本(project_profit)
CREATE TABLE pam_backup.project_profit_20250530_ex_liangjy41_2 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】项目类型配置切换时部分进行中项目受影响未正常生成收入成本计划' as modify_remark,
  'AAIG037852，【数据修复】项目类型配置切换时部分进行中项目受影响未正常生成收入成本计划' as flow_title,
  t.*
FROM pam_ctc.project_profit t
where id in
(
1243899208809140224,
1310929088429903872
);

-- 更新脚本，影响行数2
UPDATE pam_ctc.project_profit
SET income_point_main='0002',cost_method_main='0410'
WHERE id=1243899208809140224;

UPDATE pam_ctc.project_profit
SET cost_method_main='0410'
WHERE id=1310929088429903872;


-- 备份脚本(project_milepost)
CREATE TABLE pam_backup.project_milepost_20250530_ex_liangjy41_2 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】项目类型配置切换时部分进行中项目受影响未正常生成收入成本计划' as modify_remark,
  'AAIG037852，【数据修复】项目类型配置切换时部分进行中项目受影响未正常生成收入成本计划' as flow_title,
  t.*
FROM pam_ctc.project_milepost t
where id in
(
1243899208737837056,
1310929088387960832
);

-- 更新脚本，影响行数2
UPDATE pam_ctc.project_milepost
SET income_flag=1
WHERE id=1243899208737837056;


UPDATE pam_ctc.project_milepost
SET income_flag=1
WHERE id=1310929088387960832;

