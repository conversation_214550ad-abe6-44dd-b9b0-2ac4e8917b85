-- 备份脚本
CREATE TABLE pam_backup.purchase_contract_progress_20250530_1_ygx_39 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】数据一致性平台预警' as modify_remark,
  'AAIG038063，【数据一致性】 采购合同进展同步ERP失败' as flow_title,
  t.*
FROM pam_ctc.purchase_contract_progress t
where id in
(
1357758737008807936,
1371480603636469760,
1372163897256517632,
1372166576835084288,
1372167150360018944,
1372167477686378496,
1372167755266736128,
1372167996513390592,
1372168309785956352,
1372168664808628224,
1372168885236047872,
1372197924340408320,
1372198544933851136,
1372198982080962560,
1372199780919742464,
1372200039083319296,
1372200188035670016,
1372200418231623680,
1372200663787180032,
1372201649529634816,
1372201835818295296,
1372202167361277952,
1372202404234600448,
1372202629418393600,
1372203127346511872,
1372203522964496384,
1372203980304367616,
1372204496491847680,
1372205163353948160,
1372205498985029632,
1372281129391300608,
1372521438712864768,
1372574640331505664,
1372861582461845504,
1372899080171593728,
1372946977139396608,
1372950492989558784,
1372973546075496448,
1372973993746173952
)
;
-- 更新脚本，影响行数39
update pam_ctc.purchase_contract_progress
set update_at = now(), erp_status = 1, erp_message = null
where id in
(
1357758737008807936,
1371480603636469760,
1372163897256517632,
1372166576835084288,
1372167150360018944,
1372167477686378496,
1372167755266736128,
1372167996513390592,
1372168309785956352,
1372168664808628224,
1372168885236047872,
1372197924340408320,
1372198544933851136,
1372198982080962560,
1372199780919742464,
1372200039083319296,
1372200188035670016,
1372200418231623680,
1372200663787180032,
1372201649529634816,
1372201835818295296,
1372202167361277952,
1372202404234600448,
1372202629418393600,
1372203127346511872,
1372203522964496384,
1372203980304367616,
1372204496491847680,
1372205163353948160,
1372205498985029632,
1372281129391300608,
1372521438712864768,
1372574640331505664,
1372861582461845504,
1372899080171593728,
1372946977139396608,
1372950492989558784,
1372973546075496448,
1372973993746173952
)
;



