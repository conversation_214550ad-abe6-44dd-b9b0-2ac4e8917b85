-- 备份脚本
CREATE TABLE pam_backup.project_income_cost_plan_20250724_1_ygx_189 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT 
  '【修复原因】WDS上线初始化数据修复' as modify_remark,
  'AAIG041812，月度+计划收入百分比，收入成本计划节点生成逻辑优化' as flow_title,
  t.*
FROM pam_ctc.project_income_cost_plan t
WHERE id in 
(
1391874274533814272,
1391874274479288320,
1391874274504454144,
1391874274370236416,
1391874274290544640,
1391874273774645248,
1391874273724313600,
1391874273619456000,
1391874273669787648,
1391874273514598400,
1391874273594290176,
1391874273384574976,
1391874273564930048,
1391874273359409152,
1391874272747040768,
1391874273464266752,
1391874273254551552,
1391874273229385728,
1391874273145499648,
1391874273095168000,
1391874272717680640,
1391874272692514816,
1391874272482799616,
1391874272449245184,
1391874272239529984,
1391874272189198336,
1391874272029814784,
1391874272000454656,
1391874271031570432,
1391874271539081216,
1391874271870431232,
1391874270578585600,
1391874271325171712,
1391874271274840064,
1391874271434223616,
1391874270524059648,
1391874271006404608,
1391874270981238784,
1391874274588340224,
1391874277037813760,
1391874276907790336,
1391874277092339712,
1391874276853264384,
1391874276744212480,
1391874276639354880,
1391874276589023232,
1391874276530302976,
1391874276479971328,
1391874276429639680,
1391874276375113728,
1391874276324782080,
1391874276140232704,
1391874276240896000,
1391874276056346624,
1391874276031180800,
1391874275980849152,
1391874275792105472,
1391874275896963072,
1391874275737579520,
1391874275657887744,
1391874276987482112,
1391874275414618112,
1391874275334926336,
1391874275280400384,
1391874275200708608,
1391874278149304320,
1391874278233190400,
1391874278203830272,
1391874278124138496,
1391874277964754944,
1391874278069612544,
1391874275041325056,
1391874277809565696,
1391874277704708096,
1391874277650182144,
1391874277599850496,
1391874275494309888,
1391874274919690240,
1391874274835804160,
1391874274781278208,
1391874278098972672,
1391874277994115072,
1391874278044446720,
1391874278019280896,
1391874277939589120,
1391874277914423296,
1391874277859897344,
1391874277889257472,
1391874277834731520,
1391874277465632768,
1391874277490798592,
1391874277411106816,
1391874277755039744,
1391874277780205568,
1391874277729873920,
1391874277675347968,
1391874277625016320,
1391874277570490368,
1391874277515964416,
1391874277545324544,
1391874277436272640,
1391874277012647936,
1391874277381746688,
1391874277356580864,
1391874277331415040,
1391874276958121984,
1391874277302054912,
1391874277276889088,
1391874277247528960,
1391874276932956160,
1391874277222363136,
1391874276878430208,
1391874277067173888,
1391874276828098560,
1391874276798738432,
1391874277197197312,
1391874276719046656,
1391874277142671360,
1391874277172031488,
1391874277117505536,
1391874276773572608,
1391874276693880832,
1391874276664520704,
1391874276614189056,
1391874276559663104,
1391874276505137152,
1391874276454805504,
1391874276400279552,
1391874276349947904,
1391874276295421952,
1391874275150376960,
1391874275121016832,
1391874276110872576,
1391874276270256128,
1391874276190564352,
1391874276165398528,
1391874276215730176,
1391874276085706752,
1391874276006014976,
1391874275951489024,
1391874275766939648,
1391874275871797248,
1391874275926323200,
1391874275846631424,
1391874275817271296,
1391874275708219392,
1391874275683053568,
1391874275628527616,
1391874275603361792,
1391874275574001664,
1391874275548835840,
1391874275439783936,
1391874275469144064,
1391874275519475712,
1391874275360092160,
1391874275385257984,
1391874275305566208,
1391874275255234560,
1391874275230068736,
1391874275175542784,
1391874275095851008,
1391874275066490880,
1391874275011964928,
1391874274986799104,
1391874274860969984,
1391874274806444032,
1391874274756112384,
1391874274726752256,
1391874274701586432,
1391874274672226304,
1391874274647060480,
1391874271060930560,
1391874271195148288,
1391874271379697664,
1391874271593607168,
1391874271140622336,
1391874270817660928,
1391874270712803328,
1391874270872186880,
1391874270406619136,
1391874270461145088,
1391874270297567232,
1391874270163349504,
1391874270029131776,
1391874269999771648,
1391874270054297600,
1391874269970411520,
1391874271459389440,
1391874272407302144
)
;
-- 更新脚本，影响行数189
update pam_ctc.project_income_cost_plan   
set update_at = now(), remark = "初始化导入"
where id in 
(
1391874274533814272,
1391874274479288320,
1391874274504454144,
1391874274370236416,
1391874274290544640,
1391874273774645248,
1391874273724313600,
1391874273619456000,
1391874273669787648,
1391874273514598400,
1391874273594290176,
1391874273384574976,
1391874273564930048,
1391874273359409152,
1391874272747040768,
1391874273464266752,
1391874273254551552,
1391874273229385728,
1391874273145499648,
1391874273095168000,
1391874272717680640,
1391874272692514816,
1391874272482799616,
1391874272449245184,
1391874272239529984,
1391874272189198336,
1391874272029814784,
1391874272000454656,
1391874271031570432,
1391874271539081216,
1391874271870431232,
1391874270578585600,
1391874271325171712,
1391874271274840064,
1391874271434223616,
1391874270524059648,
1391874271006404608,
1391874270981238784,
1391874274588340224,
1391874277037813760,
1391874276907790336,
1391874277092339712,
1391874276853264384,
1391874276744212480,
1391874276639354880,
1391874276589023232,
1391874276530302976,
1391874276479971328,
1391874276429639680,
1391874276375113728,
1391874276324782080,
1391874276140232704,
1391874276240896000,
1391874276056346624,
1391874276031180800,
1391874275980849152,
1391874275792105472,
1391874275896963072,
1391874275737579520,
1391874275657887744,
1391874276987482112,
1391874275414618112,
1391874275334926336,
1391874275280400384,
1391874275200708608,
1391874278149304320,
1391874278233190400,
1391874278203830272,
1391874278124138496,
1391874277964754944,
1391874278069612544,
1391874275041325056,
1391874277809565696,
1391874277704708096,
1391874277650182144,
1391874277599850496,
1391874275494309888,
1391874274919690240,
1391874274835804160,
1391874274781278208,
1391874278098972672,
1391874277994115072,
1391874278044446720,
1391874278019280896,
1391874277939589120,
1391874277914423296,
1391874277859897344,
1391874277889257472,
1391874277834731520,
1391874277465632768,
1391874277490798592,
1391874277411106816,
1391874277755039744,
1391874277780205568,
1391874277729873920,
1391874277675347968,
1391874277625016320,
1391874277570490368,
1391874277515964416,
1391874277545324544,
1391874277436272640,
1391874277012647936,
1391874277381746688,
1391874277356580864,
1391874277331415040,
1391874276958121984,
1391874277302054912,
1391874277276889088,
1391874277247528960,
1391874276932956160,
1391874277222363136,
1391874276878430208,
1391874277067173888,
1391874276828098560,
1391874276798738432,
1391874277197197312,
1391874276719046656,
1391874277142671360,
1391874277172031488,
1391874277117505536,
1391874276773572608,
1391874276693880832,
1391874276664520704,
1391874276614189056,
1391874276559663104,
1391874276505137152,
1391874276454805504,
1391874276400279552,
1391874276349947904,
1391874276295421952,
1391874275150376960,
1391874275121016832,
1391874276110872576,
1391874276270256128,
1391874276190564352,
1391874276165398528,
1391874276215730176,
1391874276085706752,
1391874276006014976,
1391874275951489024,
1391874275766939648,
1391874275871797248,
1391874275926323200,
1391874275846631424,
1391874275817271296,
1391874275708219392,
1391874275683053568,
1391874275628527616,
1391874275603361792,
1391874275574001664,
1391874275548835840,
1391874275439783936,
1391874275469144064,
1391874275519475712,
1391874275360092160,
1391874275385257984,
1391874275305566208,
1391874275255234560,
1391874275230068736,
1391874275175542784,
1391874275095851008,
1391874275066490880,
1391874275011964928,
1391874274986799104,
1391874274860969984,
1391874274806444032,
1391874274756112384,
1391874274726752256,
1391874274701586432,
1391874274672226304,
1391874274647060480,
1391874271060930560,
1391874271195148288,
1391874271379697664,
1391874271593607168,
1391874271140622336,
1391874270817660928,
1391874270712803328,
1391874270872186880,
1391874270406619136,
1391874270461145088,
1391874270297567232,
1391874270163349504,
1391874270029131776,
1391874269999771648,
1391874270054297600,
1391874269970411520,
1391874271459389440,
1391874272407302144
)
;


-- 备份脚本
CREATE TABLE pam_backup.project_income_cost_plan_20250724_2_ygx_3 (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT 
  '【修复原因】WDS上线初始化数据修复' as modify_remark,
  'AAIG041812，月度+计划收入百分比，收入成本计划节点生成逻辑优化' as flow_title,
  t.*
FROM pam_ctc.project_income_cost_plan t
WHERE id in 
(
1391874278178664448,
1391874278258356224,
1391874274957438976
)
;
update pam_ctc.project_income_cost_plan set update_at = now(), remark = "初始化导入", income_ratio =0, income_amount =0, cost_ratio =0, cost_amount =0  where id =1391874278178664448 and income_ratio =100 and income_amount =768186.76 and cost_ratio =100 and cost_amount =571034.05;
update pam_ctc.project_income_cost_plan set update_at = now(), remark = "初始化导入", income_ratio =70.52, income_amount =10973933.2, cost_ratio =70.52, cost_amount =8348925.67  where id =1391874278258356224 and income_ratio =5.18 and income_amount =124768.97 and cost_ratio =5.18 and cost_amount =592446.66;
update pam_ctc.project_income_cost_plan set update_at = now(), remark = "初始化导入", income_ratio =72.45, income_amount =12315243.64, cost_ratio =72.45, cost_amount =9437882.92  where id =1391874274957438976 and income_ratio =5.18 and income_amount =136269.76 and cost_ratio =5.18 and cost_amount =660326.02;


