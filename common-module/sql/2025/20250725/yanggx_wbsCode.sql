-- 备份脚本
CREATE TABLE pam_backup.project_wbs_budget_20250725_1_ygx_all (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】project_wbs_budget 加 wbs_summary_code 字段补全-场景遗漏' as modify_remark,
  'AAIG039829，project_wbs_budget 加 wbs_summary_code 字段补全-场景遗漏' as flow_title,
  t.*
FROM pam_ctc.project_wbs_budget t
WHERE (wbs_summary_code IS NULL OR wbs_summary_code = '')
  AND project_code IS NOT NULL
  AND wbs_full_code IS NOT NULL
  AND deleted_flag = 0;
-- 更新脚本，影响行数ALL
UPDATE pam_ctc.project_wbs_budget
SET wbs_summary_code = CONCAT(project_code, '-', wbs_full_code)
WHERE (wbs_summary_code IS NULL OR wbs_summary_code = '')
  AND project_code IS NOT NULL
  AND wbs_full_code IS NOT NULL
  AND deleted_flag = 0;



-- 备份脚本
CREATE TABLE pam_backup.project_wbs_budget_change_history_20250725_1_ygx_all (
  `id` bigint(20) NOT NULL COMMENT 'id',
  PRIMARY KEY (`id`)
) AS
SELECT
  '【修复原因】project_wbs_budget 加 wbs_summary_code 字段补全-场景遗漏' as modify_remark,
  'AAIG039829，project_wbs_budget 加 wbs_summary_code 字段补全-场景遗漏' as flow_title,
  t.*
FROM pam_ctc.project_wbs_budget_change_history t
WHERE (wbs_summary_code IS NULL OR wbs_summary_code = '')
  AND project_code IS NOT NULL
  AND wbs_full_code IS NOT NULL
  AND deleted_flag = 0;
-- 更新脚本，影响行数ALL
UPDATE pam_ctc.project_wbs_budget_change_history
SET wbs_summary_code = CONCAT(project_code, '-', wbs_full_code)
WHERE (wbs_summary_code IS NULL OR wbs_summary_code = '')
  AND project_code IS NOT NULL
  AND wbs_full_code IS NOT NULL
  AND deleted_flag = 0;