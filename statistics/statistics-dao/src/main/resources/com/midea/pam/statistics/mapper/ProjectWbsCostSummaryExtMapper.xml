<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ProjectWbsCostSummaryExtMapper">
    <resultMap id="summaryMap" type="com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="activity_order_no" jdbcType="VARCHAR" property="activityOrderNo"/>
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode"/>
        <result column="activity_name" jdbcType="VARCHAR" property="activityName"/>
        <result column="activity_type" jdbcType="VARCHAR" property="activityType"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="baseline_cost" jdbcType="DECIMAL" property="baselineCost"/>
        <result column="demand_cost" jdbcType="DECIMAL" property="demandCost"/>
        <result column="on_the_way_cost" jdbcType="DECIMAL" property="onTheWayCost"/>
        <result column="incurred_cost" jdbcType="DECIMAL" property="incurredCost"/>
        <result column="remaining_cost" jdbcType="DECIMAL" property="remainingCost"/>
        <result column="change_accumulate_cost" jdbcType="DECIMAL" property="changeAccumulateCost"/>
        <result column="parent_wbs_id" jdbcType="BIGINT" property="parentWbsId"/>
        <result column="parent_activity_id" jdbcType="BIGINT" property="parentActivityId"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag"/>
        <result column="version" jdbcType="BIGINT" property="version"/>
        <result column="wbs_full_code" jdbcType="VARCHAR" property="wbsFullCode"/>
        <result column="wbs_last_code" jdbcType="VARCHAR" property="wbsLastCode"/>
        <result column="dynamic_wbs_template_rule_ids" jdbcType="VARCHAR" property="dynamicWbsTemplateRuleIds"/>
        <result column="dynamic_fields" jdbcType="VARCHAR" property="dynamicFields"/>
        <result column="dynamic_values" jdbcType="VARCHAR" property="dynamicValues"/>
        <result column="fee_type_id" jdbcType="BIGINT" property="feeTypeId"/>
        <result column="fee_type_name" jdbcType="VARCHAR" property="feeTypeName"/>
        <result column="fee_sync_ems" jdbcType="TINYINT" property="feeSyncEms"/>
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode"/>
    </resultMap>

    <select id="summary" resultMap="summaryMap" parameterType="list">
        with t as (
        select
        null as id,
        project_id,
        project_code,
        null as description,
        '99999' as activity_order_no,
        'System' as activity_code,
        null as activity_name,
        '系统生成' as activity_type,
        wbs_full_code,
        wbs_last_code,
        dynamic_wbs_template_rule_ids,
        dynamic_fields,
        dynamic_values,
        0.00 as price,
        0.00 as baseline_cost,
        0.00 as demand_cost,
        0.00 as on_the_way_cost,
        0.00 as incurred_cost,
        0.00 as remaining_cost,
        0.00 as change_accumulate_cost,
        null as parent_wbs_id,
        null as parent_activity_id,
        create_by,
        create_at,
        update_by,
        update_at,
        deleted_flag,
        version,
        fee_type_id,
        fee_type_name,
        fee_sync_ems,
        concat(project_code, '-', wbs_full_code) as wbs_summary_code
        from
        pam_ctc.project_wbs_budget
        where
        project_id in
        <foreach collection="list" index="index" item="ides0" open="(" separator="," close=")">
            #{ides0}
        </foreach>
        and deleted_flag = 0
        group by
        project_id,
        wbs_full_code)
        select * from t where not exists (
        select 1 from pam_ctc.project_wbs_budget pwb
        where pwb.project_id = t.project_id and pwb.wbs_full_code = t.wbs_full_code and pwb.activity_code = 'System' )
        union all
        select
            id,
            project_id,
            project_code,
            description,
            activity_order_no,
            activity_code,
            activity_name,
            activity_type,
            wbs_full_code,
            wbs_last_code,
            dynamic_wbs_template_rule_ids,
            dynamic_fields,
            dynamic_values,
            price,
            baseline_cost,
            demand_cost,
            on_the_way_cost,
            incurred_cost,
            remaining_cost,
            change_accumulate_cost,
            parent_wbs_id,
            parent_activity_id,
            create_by,
            create_at,
            update_by,
            update_at,
            deleted_flag,
            version,
            fee_type_id,
            fee_type_name,
            fee_sync_ems,
            concat(project_code, '-', wbs_full_code) as wbs_summary_code
        from pam_ctc.project_wbs_budget
        where project_id in
        <foreach collection="list" index="index" item="ides1" open="(" separator="," close=")">
            #{ides1}
        </foreach>
        and deleted_flag = 0
    </select>

    <!-- 查询预算汇总列表 -->
    <select id="listByParam" parameterType="java.util.Map" resultMap="summaryMap">
        SELECT
        *
        FROM
        pam_ctc.project_wbs_budget t1
        WHERE t1.deleted_flag = 0
        <if test="projectId != null and projectId != ''">
            AND t1.project_id = #{projectId}
        </if>
        <if test="description != null and description != ''">
            AND t1.description LIKE concat('%', #{description, jdbcType=VARCHAR}, '%')
        </if>
        <if test="activityCode != null and activityCode != ''">
            AND t1.activity_code = #{activityCode}
        </if>
        <if test="activityType != null and activityType != ''">
            AND t1.activity_type LIKE concat('%', #{activityType, jdbcType=VARCHAR}, '%')
        </if>
        <if test="activityName != null and activityName != ''">
            AND t1.activity_name LIKE concat('%', #{activityName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="dynamicFieldList != null and dynamicFieldList.size > 0">
            <foreach collection="dynamicFieldList" item="item" index="index">
                AND t1.id IN (
                SELECT project_wbs_budget_id
                FROM pam_ctc.project_wbs_budget_dynamic
                WHERE wbs_template_rule_detail_code = #{item.value}
                AND field_name = #{item.key}
                <if test="projectId != null and projectId != ''">
                    AND project_id = #{projectId}
                </if>
                )
            </foreach>
        </if>
        <if test="projectIdList != null and projectIdList.size() > 0">
            and t1.project_id in
            <foreach collection="projectIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.wbs_full_code,t1.activity_code
    </select>

    <resultMap id="RequirementBudgetMap" type="com.midea.pam.common.statistics.entity.RequirementBudget">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId" />
        <result column="requirement_code" jdbcType="VARCHAR" property="requirementCode" />
        <result column="unreleased_quantity" jdbcType="INTEGER" property="unreleasedQuantity" />
        <result column="receipts_type" jdbcType="VARCHAR" property="receiptsType" />
        <result column="budget_occupied_amount" jdbcType="DECIMAL" property="budgetOccupiedAmount" />
        <result column="down_amount" jdbcType="DECIMAL" property="downAmount" />
        <result column="remaining_cost_amount" jdbcType="DECIMAL" property="remainingCostAmount" />
        <result column="budget_type" jdbcType="INTEGER" property="budgetType" />
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="data_time" jdbcType="TIMESTAMP" property="dataTime" />
    </resultMap>
    <select id="requirementBudget" resultMap="RequirementBudgetMap" parameterType="list">
        with t as (
        select
            pmr.project_id,
            pwr.project_code,
            pmr.project_wbs_receipts_id,
            pmr.requirement_code,
            <!-- 状态为待下达时未完成,已下达与已关闭为已完成 -->
            (case pmr.status when 0 then 1 else 0 end ) as unreleased_quantity,
            0 as budget_type,
            IFNULL(pwrb.budget_occupied_amount, 0) as budget_occupied_amount,
            0.00 as down_amount,
            'System' as activity_code,
            pmr.wbs_summary_code,pwrb.create_at as data_time
        from
            pam_ctc.project_wbs_receipts pwr
        inner join pam_ctc.purchase_material_requirement pmr on
            pwr.id = pmr.project_wbs_receipts_id
            and pmr.purchase_type = 3
            and pmr.deleted_flag = 0
        inner join pam_ctc.project_wbs_receipts_budget pwrb on
            pwr.id = pwrb.project_wbs_receipts_id
            and pmr.wbs_summary_code = pwrb.wbs_summary_code
            and pwrb.demand_type = 1
            and pwrb.deleted_flag = 0
        where
            pmr.project_id in
            <foreach collection="list" index="index" item="ides0" open="(" separator="," close=")">
                #{ides0}
            </foreach>
            and pwr.requirement_type = 3
            and pwr.requirement_status not in (3, 5)
            and pwr.deleted_flag = 0

        union all
        select
            pmr.project_id,
            pwr.project_code,
            pmr.project_wbs_receipts_id,
            pmr.requirement_code,
            <!-- 状态为已关闭为已完成,其他为未完成 -->
            (case pmr.status when 2 then 0 else 1 end ) as unreleased_quantity,
            1 as budget_type,
            IFNULL(pwrb.budget_occupied_amount, 0) as budget_occupied_amount,
            0.00 as down_amount,
            'System' as activity_code,
            pmr.wbs_summary_code,pwrb.create_at as data_time
        from
            pam_ctc.project_wbs_receipts pwr
        inner join pam_ctc.purchase_material_requirement pmr on
            pwr.id = pmr.project_wbs_receipts_id
            and pmr.purchase_type = 2
            and pmr.deleted_flag = 0
        inner join pam_ctc.project_wbs_receipts_budget pwrb on
            pwr.id = pwrb.project_wbs_receipts_id
            and pmr.wbs_summary_code = pwrb.wbs_summary_code
            and pwrb.demand_type = 2
            and pwrb.deleted_flag = 0
        where
            pmr.project_id in
            <foreach collection="list" index="index" item="ides1" open="(" separator="," close=")">
                #{ides1}
            </foreach>
            and pwr.requirement_type = 3
            and pwr.requirement_status not in (3, 5)
            and pwr.deleted_flag = 0

        union all
        select
            pwr.project_id,
            pwr.project_code,
            pwr.id as project_wbs_receipts_id,
            pwr.requirement_code,
            null as unreleased_quantity,
            2 as budget_type,
            IFNULL(pwrb.budget_occupied_amount, 0) as budget_occupied_amount,
            0.00 as down_amount,
            'System' as activity_code,
            pwrb.wbs_summary_code,pwrb.create_at as data_time
        from
            pam_ctc.project_wbs_receipts pwr
        inner join pam_ctc.project_wbs_receipts_budget pwrb on
            pwr.id = pwrb.project_wbs_receipts_id
            and pwrb.demand_type != 0
            and pwrb.deleted_flag = 0
        where
            pwr.project_id in
            <foreach collection="list" index="index" item="ides2" open="(" separator="," close=")">
                #{ides2}
            </foreach>
            and pwr.requirement_status = 2
            and pwr.requirement_type = 3
            and pwr.deleted_flag = 0
        ) select project_id,project_code,project_wbs_receipts_id,requirement_code
            ,sum(unreleased_quantity) as unreleased_quantity,budget_type,
            budget_occupied_amount,down_amount,activity_code,wbs_summary_code,data_time
        from t
        group by t.wbs_summary_code,t.project_wbs_receipts_id,t.budget_type
    </select>

    <resultMap id="RequirementPoDetailMap" type="com.midea.pam.common.statistics.entity.RequirementPoDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="purchase_order_id" jdbcType="BIGINT" property="purchaseOrderId" />
        <result column="num" jdbcType="VARCHAR" property="num" />
        <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId" />
        <result column="requirement_code" jdbcType="VARCHAR" property="requirementCode" />
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
        <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
        <result column="order_num" jdbcType="DECIMAL" property="orderNum" />
        <result column="storage_count" jdbcType="DECIMAL" property="storageCount" />
        <result column="discount_price" jdbcType="DECIMAL" property="discountPrice" />
        <result column="po_total_amount" jdbcType="DECIMAL" property="poTotalAmount" />
        <result column="po_type" jdbcType="INTEGER" property="poType" />
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="data_time" jdbcType="TIMESTAMP" property="dataTime" />
        <result column="cancel_num" jdbcType="DECIMAL" property="cancelNum" />
        <result column="vendor_name" jdbcType="VARCHAR" property="vendorName" />
        <result column="materiel_descr" jdbcType="VARCHAR" property="materielDescr" />
    </resultMap>

    <select id="requirementPoDetail" resultMap="RequirementPoDetailMap" parameterType="list">
        -- 使用 CTE 预先计算 storage_count
        WITH filtered_progress AS (
            SELECT
                item_code,
                po_line_no,
                po_number,
                SUM(CASE
                    WHEN transaction_type = 'DELIVER' THEN transaction_quantity
                    WHEN transaction_type = 'RETURN TO RECEIVING' THEN -transaction_quantity
                    ELSE 0
                END) as storage_count
            FROM pam_ctc.purchase_progress
            WHERE transaction_type IN ('DELIVER', 'RETURN TO RECEIVING')
            GROUP BY item_code, po_line_no, po_number
        )
        SELECT
            od.project_id,
            od.project_num as project_code,
            od.purchase_order_id,
            od.project_wbs_receipts_id,
            o.num,
            od.requirement_code,
            od.pam_code,
            od.erp_code,
            IFNULL(od.order_num, 0) as order_num,
            IFNULL(pp.storage_count, 0) as storage_count,
            IFNULL(od.cancel_num, 0) as cancel_num,
            convert(od.discount_price * IFNULL(o.conversion_rate, 1), decimal(20,2)) as discount_price,
            convert((IFNULL(od.order_num, 0) - IFNULL(pp.storage_count, 0) - IFNULL(od.cancel_num, 0))
                   * od.discount_price * IFNULL(o.conversion_rate, 1), decimal(20,2)) as po_total_amount,
            1 as po_type,
            SUBSTRING_INDEX(od.activity_code, '.', 2) as activity_code,
            od.wbs_summary_code,
            od.create_at as data_time,
            o.vendor_name,
            od.materiel_descr
        FROM pam_ctc.purchase_order_detail od
        INNER JOIN pam_ctc.purchase_order o ON
            od.purchase_order_id = o.id
            AND o.order_status NOT IN (2, 6, 8, 9, 10)
            AND o.deleted_flag = 0
        LEFT JOIN filtered_progress pp ON
            od.erp_code = pp.item_code
            AND pp.po_line_no = od.line_number
            AND o.num = pp.po_number
        where
            od.project_id in
            <foreach collection="list" index="index" item="ides2" open="(" separator="," close=")">
                #{ides2}
            </foreach>
        AND od.deleted_flag = 0
        GROUP BY
            od.project_id,
            od.purchase_order_id,
            od.line_number
    </select>

    <resultMap id="ProjectPurchaseContractDetailMap"
               type="com.midea.pam.common.statistics.entity.ProjectPurchaseContractDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId" />
        <result column="purchase_contract_code" jdbcType="VARCHAR" property="purchaseContractCode" />
        <result column="purchase_contract_name" jdbcType="VARCHAR" property="purchaseContractName" />
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
        <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
        <result column="budget_execute_amount_total" jdbcType="DECIMAL" property="budgetExecuteAmountTotal" />
        <result column="allocation_execute_amount" jdbcType="DECIMAL" property="allocationExecuteAmount" />
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="budget_type" jdbcType="INTEGER" property="budgetType" />
        <result column="data_time" jdbcType="TIMESTAMP" property="dataTime" />
        <result column="contract_create_time" jdbcType="TIMESTAMP" property="contractCreateTime" />
        <result column="progress_code" jdbcType="VARCHAR" property="progressCode" />
        <result column="vendor_name" jdbcType="VARCHAR" property="vendorName" />
        <result column="materiel_descr" jdbcType="VARCHAR" property="materielDescr" />
    </resultMap>
    <select id="projectPurchaseContractDetail" resultMap="ProjectPurchaseContractDetailMap" parameterType="list">
        select
        pc.project_id,
        pwr.project_code,
        pwr.id as project_wbs_receipts_id,
        pc.code as purchase_contract_code,
        null as progress_code,
        pmr.pam_code,
        convert(IFNULL(pcb.total_price, 0)*IFNULL(pc.conversion_rate, 1),decimal(20,2)) as total_price,
        convert(IFNULL(pcb.local_budget_execute_amount_total, 0),decimal(20,2)) as budget_execute_amount_total,
        convert((IFNULL(pcb.total_price, 0)*IFNULL(pc.conversion_rate,1)-IFNULL(pcb.local_budget_execute_amount_total, 0)),decimal(20,2)) as allocation_execute_amount,
        <!-- 此处活动事项后续优化 TODO -->
        SUBSTRING_INDEX(pcb.activity_code, '.', 2) as activity_code,
        pmr.wbs_summary_code,
        1 as budget_type,
        pc.create_at as data_time,
        null as contract_create_time,
        pc.name as purchase_contract_name,
        pc.vendor_name,
        pmr.materiel_descr
        from
        pam_ctc.project_wbs_receipts pwr
        inner join pam_ctc.purchase_material_requirement pmr on
        pwr.id = pmr.project_wbs_receipts_id
        and pmr.purchase_type = 2
        and pmr.deleted_flag = 0
        inner join pam_ctc.purchase_contract_budget pcb on
        pmr.id = pcb.purchase_requirement_id
        and pcb.deleted_flag = 0
        inner join pam_ctc.purchase_contract pc on
        pcb.purchase_contract_id = pc.id
        and pc.deleted_flag = 0
        where
        1 = 1
        and pc.project_id in
        <foreach collection="list" index="index" item="ides1" open="(" separator="," close=")">
            #{ides1}
        </foreach>
        and pc.status in (2, 5, 10)
        and pwr.deleted_flag = 0
        union all
        select
        pc.project_id,
        pwr.project_code,
        pwr.id as project_wbs_receipts_id,
        pc.code as purchase_contract_code,
        pcp.progress_code,
        pmr.pam_code,
        convert(IFNULL(pcb.total_price, 0)* IFNULL(pc.conversion_rate, 1),decimal(20,2)) as total_price,
        convert(IFNULL(pcpbr.local_budget_execute_amount, 0),decimal(20,2)) as budget_execute_amount_total,
        0.00 as allocation_execute_amount,
        <!-- 此处活动事项后续优化 TODO -->
        SUBSTRING_INDEX(pcb.activity_code, '.', 2) as activity_code,
        pmr.wbs_summary_code,
        2 as budget_type,
        pcp.progress_end_time as data_time,
        pcp.create_at as contract_create_time,
        pc.name as purchase_contract_name,
        pc.vendor_name,
        pmr.materiel_descr
        from
        pam_ctc.project_wbs_receipts pwr
        inner join pam_ctc.purchase_material_requirement pmr on
        pwr.id = pmr.project_wbs_receipts_id
        and pmr.purchase_type = 2
        and pmr.deleted_flag = 0
        inner join pam_ctc.purchase_contract_budget pcb on
        pmr.id = pcb.purchase_requirement_id
        and pcb.deleted_flag = 0
        inner join pam_ctc.purchase_contract pc on
        pcb.purchase_contract_id = pc.id
        and pc.deleted_flag = 0
        inner join pam_ctc.purchase_contract_progress pcp on
        pcp.purchase_contract_id = pc.id
        and pcp.progress_status = 2
        and pcp.deleted_flag = 0
        inner join pam_ctc.purchase_contract_progress_budget_rel pcpbr on
        pcp.id = pcpbr.purchase_contract_progress_id
        and pcpbr.purchase_contract_budget_id = pcb.id
        and pcpbr.deleted_flag = 0
        where
        1 = 1
        and pc.project_id in
        <foreach collection="list" index="index" item="ides2" open="(" separator="," close=")">
            #{ides2}
        </foreach>
        and pc.status in (2, 5, 10)
        and pwr.deleted_flag = 0
    </select>

    <resultMap id="RequirementPurchaseContractDetailMap"
               type="com.midea.pam.common.statistics.entity.RequirementPurchaseContractDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId"/>
        <result column="requirement_code" jdbcType="VARCHAR" property="requirementCode"/>
        <result column="purchase_contract_code" jdbcType="VARCHAR" property="purchaseContractCode"/>
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode"/>
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag"/>
    </resultMap>
    <select id="requirementPurchaseContractDetail" resultMap="RequirementPurchaseContractDetailMap"
            parameterType="list">
        select
        pmr.project_id,
        pmr.project_wbs_receipts_id,
        pmr.requirement_code,
        pc.code as purchase_contract_code,
        convert(IFNULL(pcb.total_price, 0)*IFNULL(pc.conversion_rate, 1),decimal(20,2)) as total_amount,
        2 as budget_type,
        'System' as activity_code,
        pmr.wbs_summary_code
        from
        pam_ctc.project_wbs_receipts pwr
        inner join pam_ctc.purchase_material_requirement pmr on
        pwr.id = pmr.project_wbs_receipts_id
        and pmr.purchase_type = 2
        and pmr.deleted_flag = 0
        inner join pam_ctc.purchase_contract_budget pcb on
        pmr.id = pcb.purchase_requirement_id
        and pcb.deleted_flag = 0
        inner join pam_ctc.purchase_contract pc on
        pcb.purchase_contract_id = pc.id
        and pc.deleted_flag = 0
        where
        1 = 1
        and pmr.project_id in
        <foreach collection="list" index="index" item="ides1" open="(" separator="," close=")">
            #{ides1}
        </foreach>
        and pc.status in (2, 5, 10)
        and pwr.deleted_flag = 0
    </select>

    <resultMap id="ProjectMaterialDetailMap" type="com.midea.pam.common.statistics.entity.ProjectMaterialDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="material_id" jdbcType="BIGINT" property="materialId" />
        <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
        <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
        <result column="actual_amount" jdbcType="DECIMAL" property="actualAmount" />
        <result column="actual_cost" jdbcType="DECIMAL" property="actualCost" />
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
        <result column="budget_type" jdbcType="INTEGER" property="budgetType" />
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="data_time" jdbcType="TIMESTAMP" property="dataTime" />
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
        <result column="materiel_descr" jdbcType="VARCHAR" property="materielDescr" />
    </resultMap>
    <select id="projectMaterialDetail" resultMap="ProjectMaterialDetailMap" parameterType="list">
        select
        mgh.project_id,
        mgh.project_code,
        mgd.material_id,
        mgh.get_code as bill_code,
        mgd.material_code,
        m.pam_code,
        m.item_info as materiel_descr,
        ifnull(mgd.actual_amount, 0) as actual_amount,
        ifnull(mgd.actual_cost, 0) as actual_cost,
        convert(ifnull(mgd.actual_amount, 0) * IFNULL(mgd.actual_cost, 0),decimal(20,2)) as total_amount,
        1 as budget_type,
        <!-- 此处活动事项后续优化 TODO -->
        SUBSTRING_INDEX(mgd.activity_code, '.', 2) as activity_code,
        mgd.wbs_summary_code,mgd.trade_time as  data_time
        from
        pam_ctc.material_get_header mgh
        left join pam_ctc.material_get_detail mgd
        on
        mgh.id = mgd.header_id
        and mgd.deleted_flag = 0
        left join pam_basedata.material m on mgd.material_id = m.id
        where
        1 = 1
        and mgh.project_id in
        <foreach collection="list" index="index" item="ides1" open="(" separator="," close=")">
            #{ides1}
        </foreach>
        and mgh.material_get_type = 2
        and mgh.status = 5
        and mgh.deleted_flag = 0
        union all
        select
        mrh.project_id,
        mrh.project_code,
        mrd.material_id,
        mrh.return_code as bill_code,
        mrd.material_code,
        m.pam_code,
        m.item_info as materiel_descr,
        ifnull(mrd.actual_amount, 0) as actual_amount,
        ifnull(mrd.actual_cost, 0) as actual_cost,
        convert(ifnull(mrd.actual_amount, 0) * IFNULL(mrd.actual_cost, 0),decimal(20,2)) as total_amount,
        2 as budget_type,
        <!-- 此处活动事项后续优化 TODO -->
        SUBSTRING_INDEX(mrd.activity_code, '.', 2) as activity_code,
        mrd.wbs_summary_code,mrd.actual_return_at as  data_time
        from
        pam_ctc.material_return_header mrh
        left join pam_ctc.material_return_detail mrd
        on
        mrh.id = mrd.header_id
        and mrd.deleted_flag = 0
        left join pam_basedata.material m on mrd.material_id = m.id
        where
        1 = 1
        and mrh.project_id in
        <foreach collection="list" index="index" item="ides2" open="(" separator="," close=")">
            #{ides2}
        </foreach>
        and mrh.material_get_type = 2
        and mrh.status = 5
        and mrh.deleted_flag = 0
    </select>

    <resultMap id="ProjectEaDetailMap" type="com.midea.pam.common.statistics.entity.ProjectEaDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="fee_apply_code" jdbcType="VARCHAR" property="feeApplyCode" />
        <result column="amount" jdbcType="DECIMAL" property="amount" />
        <result column="budget_type" jdbcType="INTEGER" property="budgetType" />
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="data_time" jdbcType="TIMESTAMP" property="dataTime" />
        <result column="vendor_name" jdbcType="VARCHAR" property="vendorName" />
    </resultMap>
    <select id="projectEaDetail" resultMap="ProjectEaDetailMap" parameterType="list">
        select
        pwb.project_id,
        pwb.project_code,
        ebod.fee_apply_code ,
        convert(ifnull(ebod.overplus_ea_amount, 0) * IFNULL(ebod.conversion_rate, 1),decimal(20,2)) as amount,
        0 as budget_type,
        pwb.activity_code,
        concat(pwb.project_code, '-', pwb.wbs_full_code) as wbs_summary_code,ebod.submited_time as data_time,
        ebod.apply_name as vendor_name
        from
        pam_ctc.project_wbs_budget pwb
        inner join pam_ctc.project_fee_collection pfc on
        pwb.id = pfc.source_id
        and pfc.deleted_flag = 0
        inner join pam_ctc.ems_budget_occupy_detail ebod on
        pfc.ems_budget_id = ebod.budget_node_id
        where
        pwb.project_id in
        <foreach collection="list" index="index" item="ides0" open="(" separator="," close=")">
            #{ides0}
        </foreach>
        and ebod.module_type = 'EA'
        and pwb.deleted_flag = 0
        union all
        select
        pwb.project_id,
        pwb.project_code,
        epfd.order_code ,
        convert(ifnull(epfd.fee_amount, 0) * IFNULL(epfd.erp_exchange_rate, 1),decimal(20,2)) as amount,
        1 as budget_type,
        pwb.activity_code,
        concat(pwb.project_code, '-', pwb.wbs_full_code) as wbs_summary_code,epfd.gl_date as data_time,
        epfd.apply_name as vendor_name
        from
        pam_ctc.project_wbs_budget pwb
        inner join pam_ctc.project_fee_collection pfc on
        pwb.id = pfc.source_id
        and pfc.deleted_flag = 0
        inner join pam_ctc.ems_pam_fee_detail epfd on
        pfc.ems_budget_id = epfd.budget_node_id
        where
        pwb.project_id in
        <foreach collection="list" index="index" item="ides1" open="(" separator="," close=")">
            #{ides1}
        </foreach>
        and epfd.import_erp_status != 'SUCCESS'
        and pwb.deleted_flag = 0
        union all
        select
        pwb.project_id,
        pwb.project_code,
        pbc.order_code ,
        ifnull(pbc.amount, 0) as amount,
        2 as budget_type,
        pwb.activity_code,
        concat(pwb.project_code, '-', pwb.wbs_full_code) as wbs_summary_code,epfd.gl_date as data_time,
        epfd.apply_name as vendor_name
        from
        pam_ctc.project_wbs_budget pwb
        inner join pam_ctc.project_fee_collection pfc on
        pwb.id = pfc.source_id
        and pfc.deleted_flag = 0
        inner join pam_ctc.ems_pam_fee_detail epfd on
        pfc.ems_budget_id = epfd.budget_node_id
        inner join pam_ctc.project_budget_cost pbc on
        epfd.order_id = pbc.attribute1
        where
        pwb.project_id in
        <foreach collection="list" index="index" item="ides2" open="(" separator="," close=")">
            #{ides2}
        </foreach>
        and pwb.deleted_flag = 0
    </select>

    <resultMap id="ProjectWorkingHourDetailMap" type="com.midea.pam.common.statistics.entity.ProjectWorkingHourDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="user_mip" jdbcType="VARCHAR" property="userMip" />
        <result column="level" jdbcType="VARCHAR" property="level" />
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate" />
        <result column="working_hours" jdbcType="DECIMAL" property="workingHours" />
        <result column="cost_money" jdbcType="DECIMAL" property="costMoney" />
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
        <result column="budget_type" jdbcType="INTEGER" property="budgetType" />
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="data_time" jdbcType="TIMESTAMP" property="dataTime" />
    </resultMap>
    <select id="projectWorkingHourDetail" resultMap="ProjectWorkingHourDetailMap" parameterType="list">
        select
        wh.project_id,
        p.code as project_code,
        ui.username as user_mip,
        wh.labor_wbs_cost_name as level,
        DATE_FORMAT(wh.apply_date, '%Y-%m-%d') as apply_date,
        ROUND(wh.apply_working_hours, 2) as working_hours,
        IFNULL(wh.actual_cost_money, wh.cost_money) as cost_money,
        case
        when wh.actual_cost_money is not null then ROUND(wh.actual_cost_money *(if(wh.status = 4,
        wh.actual_working_hours, wh.apply_working_hours))/ 8, 2)
        else ROUND(wh.cost_money *(if(wh.status = 4, wh.actual_working_hours, wh.apply_working_hours))/ 8, 2)
        end as total_amount,
        1 as budget_type,
        <!-- 此处活动事项后续优化 TODO -->
        SUBSTRING_INDEX(wh.project_activity_code, '.', 2) as activity_code,
        concat(p.code, '-', wh.wbs_budget_code) as wbs_summary_code,wh.create_at as data_time
        from
        pam_ctc.working_hour wh
        inner join pam_basedata.ltc_user_info ui on
        wh.user_id = ui.id
        inner join pam_ctc.project p on
        wh.project_id = p.id
        and p.deleted_flag = 0
        where
        1 = 1
        and wh.project_id in
        <foreach collection="list" index="index" item="ides1" open="(" separator="," close=")">
            #{ides1}
        </foreach>
        and wh.status = 2
        and wh.user_type in (1,3)
        and wh.delete_flag = 0
        union all
        select
        wh.project_id,
        p.code as project_code,
        ui.username as user_mip,
        wh.labor_wbs_cost_name as level,
        DATE_FORMAT(wh.apply_date, '%Y-%m-%d') as apply_date,
        ROUND(wh.actual_working_hours, 2) as working_hours,
        IFNULL(wh.actual_cost_money, wh.cost_money) as cost_money,
        case
        when wh.actual_cost_money is not null then ROUND(wh.actual_cost_money *(if(wh.status = 4,
        wh.actual_working_hours, wh.apply_working_hours))/ 8, 2)
        else ROUND(wh.cost_money *(if(wh.status = 4, wh.actual_working_hours, wh.apply_working_hours))/ 8, 2)
        end as total_amount,
        2 as budget_type,
        <!-- 此处活动事项后续优化 TODO -->
        SUBSTRING_INDEX(wh.project_activity_code, '.', 2) as activity_code,
        concat(p.code, '-', wh.wbs_budget_code) as wbs_summary_code,wh.create_at as data_time
        from
        pam_ctc.working_hour wh
        inner join pam_basedata.ltc_user_info ui on
        wh.user_id = ui.id
        inner join pam_ctc.project p on
        wh.project_id = p.id
        and p.deleted_flag = 0
        where
        1 = 1
        and wh.project_id in
        <foreach collection="list" index="index" item="ides2" open="(" separator="," close=")">
            #{ides2}
        </foreach>
        and wh.status = 4
        and wh.user_type in (1,3)
        and wh.delete_flag = 0
    </select>

    <insert id="insertBatch" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsCostSummary">
        insert into project_wbs_cost_summary (id, project_id, project_code,
        project_name, amount, line_boby,
        station, tool, description,
        activity_code, activity_name, activity_type,
        wbs_summary_code, wbs_full_code, price, baseline_cost,
        demand_cost, on_the_way_cost, incurred_cost,
        remaining_cost, change_accumulate_cost, unit_id, create_by,
        create_at, update_by, update_at)
        values
        <foreach collection="list" item="obj" separator=",">
            (#{obj.id,jdbcType=BIGINT}, #{obj.projectId,jdbcType=BIGINT}, #{obj.projectCode,jdbcType=VARCHAR},
            #{obj.projectName,jdbcType=VARCHAR}, #{obj.amount,jdbcType=DECIMAL}, #{obj.lineBoby,jdbcType=VARCHAR},
            #{obj.station,jdbcType=VARCHAR}, #{obj.tool,jdbcType=VARCHAR}, #{obj.description,jdbcType=VARCHAR},
            #{obj.activityCode,jdbcType=VARCHAR}, #{obj.activityName,jdbcType=VARCHAR},
            #{obj.activityType,jdbcType=VARCHAR},
            #{obj.wbsSummaryCode,jdbcType=VARCHAR}, #{obj.wbsFullCode,jdbcType=VARCHAR}, #{obj.price,jdbcType=DECIMAL},
            #{obj.baselineCost,jdbcType=DECIMAL},
            #{obj.demandCost,jdbcType=DECIMAL}, #{obj.onTheWayCost,jdbcType=DECIMAL},
            #{obj.incurredCost,jdbcType=DECIMAL},
            #{obj.remainingCost,jdbcType=DECIMAL}, #{obj.changeAccumulateCost,jdbcType=DECIMAL},
            #{obj.unitId,jdbcType=BIGINT}, #{obj.createBy,jdbcType=BIGINT},
            #{obj.createAt,jdbcType=TIMESTAMP}, #{obj.updateBy,jdbcType=BIGINT}, #{obj.updateAt,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <select id="getConfirmedAmount" resultType="com.midea.pam.common.statistics.excelVo.ProjectWbsCostExcelVo">
        select
            p.id as projectId,
            p.code as projectCode,
            p.name as projectName,
            p.amount,
            p.currency,
            rel.currency as localCurrency,
            round(ifnull(pcis.confirmed_income_total_amount, 0), 2) as confirmedIncome,
            round(ifnull(pcis.standard_confirmed_income_total_amount, 0), 2) as standardConfirmedIncome,
            round(ifnull(pcis.confirmed_cost_total_amount, 0), 2) as confirmedCost,
            round(ifnull(pcis.confirmed_gross_profit_ratio, 0), 2) as confirmedRate,
            round(ifnull(pcis.confirmed_exchange_amount, 0), 2) as confirmedExchangeAmount,
            pwbs.price as projectTotalBudget,
            pwbs.demand_cost as totalDemandBudget,
            pwbs.on_the_way_cost as totalCostInTransit,
            pwbs.incurred_cost as totalCostIncurred,
            pwbs.remaining_cost as remainingTotalBudget,
            p.wbs_enabled as wbsEnabled,
            p.wbs_template_info_id as wbsTemplateInfoId
        from
            pam_ctc.project p
        left join pam_statistics.project_current_income_summary pcis on
            p.id = pcis.project_id
            and pcis.execute_id = #{executeId}
            and pcis.deleted_flag = 0
        left join pam_basedata.organization_rel rel on rel.operating_unit_id = p.ou_id and rel.pam_enabled = 0
        left join pam_ctc.project_wbs_budget_summary pwbs on
            p.id = pwbs.project_id
            and pwbs.summary_type = 'wbs'
            and ( pwbs.parent_id = -1
            or pwbs.parent_id is null )
            and pwbs.deleted_flag = 0
        where
            p.id = #{projectId}
    </select>

    <resultMap id="ProjectCostExecuteRecordMap" type="com.midea.pam.common.statistics.entity.ProjectCostExecuteRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ou_id" jdbcType="BIGINT" property="ouId"/>
        <result column="batch_num" jdbcType="BIGINT" property="batchNum"/>
        <result column="start_time" jdbcType="DATE" property="startTime"/>
        <result column="num" jdbcType="INTEGER" property="num"/>
        <result column="success_num" jdbcType="INTEGER" property="successNum"/>
        <result column="failure_num" jdbcType="INTEGER" property="failureNum"/>
        <result column="end_time" jdbcType="DATE" property="endTime"/>
        <result column="cost_time" jdbcType="BIGINT" property="costTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="or_not_wbs" jdbcType="TINYINT" property="orNotWbs"/>
    </resultMap>
    <select id="getLatestSuccessRecord" resultMap="ProjectCostExecuteRecordMap">
        select
        er.*
        from pam_statistics.project_cost_execute_record er
        where er.deleted_flag = 0 and er.status = 2 and er.or_not_wbs = 1
        order by er.create_at desc
        limit 1
    </select>

    <resultMap id="ProjectWbsCostSummaryMap" type="com.midea.pam.common.statistics.entity.ProjectWbsCostSummary">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="line_boby" jdbcType="VARCHAR" property="lineBoby"/>
        <result column="station" jdbcType="VARCHAR" property="station"/>
        <result column="tool" jdbcType="VARCHAR" property="tool"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode"/>
        <result column="activity_name" jdbcType="VARCHAR" property="activityName"/>
        <result column="activity_type" jdbcType="VARCHAR" property="activityType"/>
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="baseline_cost" jdbcType="DECIMAL" property="baselineCost"/>
        <result column="demand_cost" jdbcType="DECIMAL" property="demandCost"/>
        <result column="on_the_way_cost" jdbcType="DECIMAL" property="onTheWayCost"/>
        <result column="incurred_cost" jdbcType="DECIMAL" property="incurredCost"/>
        <result column="remaining_cost" jdbcType="DECIMAL" property="remainingCost"/>
        <result column="change_accumulate_cost" jdbcType="DECIMAL" property="changeAccumulateCost"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag"/>
    </resultMap>
    <select id="getListView" resultMap="ProjectWbsCostSummaryMap"
            parameterType="com.midea.pam.common.statistics.entity.ProjectWbsCostSummary">
        select * from pam_statistics.project_wbs_cost_summary pwcs
        where
        pwcs.project_id = #{projectId}
        <if test="lineBoby != null">
            and pwcs.line_boby =#{lineBoby}
        </if>
        <if test="station != null">
            and pwcs.station =#{station}
        </if>
        <if test="tool != null">
            and pwcs.tool =#{tool}
        </if>
        <if test="description != null and description != ''">
            and pwcs.description like concat('%', #{description}, '%')
        </if>
        <if test="activityCode != null">
            and pwcs.activity_code =#{activityCode}
        </if>
        <if test="activityName != null and activityName != ''">
            and pwcs.activity_name like concat('%', #{activityName}, '%')
        </if>
        <if test="activityType != null and activityType != ''">
            and pwcs.activity_type like concat('%', #{activityType}, '%')
        </if>
        and pwcs.deleted_flag = 0
    </select>

    <select id="getNeedCalculateRange" resultType="java.lang.Long">
        select
            p.id as project_id
        from
        pam_ctc.project p
        where
        p.deleted_flag = 0
        <if test="ouId != null and ouId != ''">
            and p.ou_id = #{ouId}
        </if>
        <if test="statuses!=null and statuses.size>0">
            and p.status in
            <foreach collection="statuses" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        and exists ( select 1 from pam_ctc.project_wbs_budget pwb where pwb.project_id = p.id and pwb.deleted_flag = 0)
        order by p.id
    </select>

    <select id="requirementCodeBudget" resultType="com.midea.pam.common.statistics.vo.ProjectWbsCostByReceiptsVO">
        with t as (
        select
        sum(rpd.po_total_amount) as onTheWayCost,
        rpd.project_wbs_receipts_id,
        1 as budget_type
        from
        pam_statistics.requirement_po_detail rpd
        where
        rpd.execute_id = #{executeId}
        and rpd.project_id = #{projectId}
        and rpd.wbs_summary_code = #{wbsSummaryCode}
        and rpd.po_type = 1
        and rpd.deleted_flag = 0
        group by
        rpd.project_wbs_receipts_id
        union all
        select
        sum(ppcd.allocation_execute_amount) as onTheWayCost,
        ppcd.project_wbs_receipts_id,
        2 as budget_type
        from
        pam_statistics.project_purchase_contract_detail ppcd
        where
        ppcd.execute_id = #{executeId}
        and ppcd.project_id = #{projectId}
        and ppcd.wbs_summary_code = #{wbsSummaryCode}
        and ppcd.budget_type = 1
        and ppcd.deleted_flag = 0
        group by
        ppcd.project_wbs_receipts_id
        ),t1 as (
        select
        sum(money) as incurredCost ,
        od.project_wbs_receipts_id,
        1 as budget_type
        from
        pam_ctc.purchase_order o
        inner join pam_ctc.purchase_order_detail od on
        o.id = od.purchase_order_id
        inner join pam_ctc.formal_material_get_and_return fm on
        o.num = fm.source
        and concat(od.line_number,'') = fm.po_line_number
        and fm.change_material_get_and_return = 1
        where
        od.project_id = #{projectId}
        and od.deleted_flag = 0
        and od.wbs_summary_code = #{wbsSummaryCode}
        group by
        od.project_wbs_receipts_id
        union all
        select
        sum(ppcd.budget_execute_amount_total) as incurredCost,
        ppcd.project_wbs_receipts_id,
        2 as budget_type
        from
        pam_statistics.project_purchase_contract_detail ppcd
        where
        ppcd.execute_id = #{executeId}
        and ppcd.project_id = #{projectId}
        and ppcd.wbs_summary_code = #{wbsSummaryCode}
        and ppcd.budget_type = 2
        and ppcd.deleted_flag = 0
        group by
        ppcd.project_wbs_receipts_id
        ),
        t2 as (
        select
        sum(rb.remaining_cost_amount) as demandCost,
        rb.project_wbs_receipts_id,
        (case when rb.budget_type=0 then 1 else 2 end) as budget_type
        from
        pam_statistics.requirement_budget rb
        where
        rb.execute_id = #{executeId}
        and rb.project_id = #{projectId}
        and rb.wbs_summary_code = #{wbsSummaryCode}
        and rb.budget_type in (0,1)
        and rb.deleted_flag = 0
        group by
        rb.project_wbs_receipts_id,rb.budget_type)
        select
        pmr.project_id as projectId,
        pmr.wbs_summary_code as wbsSummaryCode,
        pmr.project_wbs_receipts_id as projectWbsReceiptsId,
        pmr.requirement_code as requirementCode,
        IFNULL(t2.demandCost, 0) as demandCost,
        IFNULL(t.onTheWayCost, 0) as onTheWayCost ,
        IFNULL(t1.incurredCost, 0) as incurredCost ,
        pwrb.demand_type as demandType
        from
        pam_ctc.project_wbs_receipts pwr
        inner join pam_ctc.purchase_material_requirement pmr
        on
        pwr.id = pmr.project_wbs_receipts_id
        and pmr.purchase_type in (2,3)
        and pmr.deleted_flag = 0
        left join pam_ctc.project_wbs_receipts_budget pwrb on
        pwr.id = pwrb.project_wbs_receipts_id
        and pmr.wbs_summary_code = pwrb.wbs_summary_code
        and pwrb.demand_type in (1, 2)
        and pwrb.deleted_flag = 0
        left join t on
        pmr.project_wbs_receipts_id = t.project_wbs_receipts_id
        and
        pwrb.demand_type = t.budget_type
        left join t1 on
        pmr.project_wbs_receipts_id = t1.project_wbs_receipts_id
        and
        pwrb.demand_type = t1.budget_type
        left join t2 on
        pmr.project_wbs_receipts_id = t2.project_wbs_receipts_id
        and pwrb.demand_type = t2.budget_type
        where
        1 = 1
        and pmr.project_id = #{projectId}
        and pmr.wbs_summary_code = #{wbsSummaryCode}
        and pwr.requirement_type = 3
        and pwr.requirement_status not in (3, 5)
        and pwr.deleted_flag = 0
        group by
        pmr.project_wbs_receipts_id ,
        pwrb.demand_type
    </select>

    <select id="getChangeData" resultType="java.lang.Long">
        select
            project_id
        from pam_ctc.project_wbs_receipts
        where project_id in
        <foreach collection="list" index="index" item="ides1" open="(" separator="," close=")">
            #{ides1}
        </foreach>
        and deleted_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
        union
        select
            project_id
        from pam_ctc.purchase_material_requirement
        where project_id in
        <foreach collection="list" index="index" item="ides2" open="(" separator="," close=")">
            #{ides2}
        </foreach>
        and deleted_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
        union
        select
            project_id
        from pam_ctc.purchase_contract
        where project_id in
        <foreach collection="list" index="index" item="ides3" open="(" separator="," close=")">
            #{ides3}
        </foreach>
        and deleted_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
        union
        select
            project_id
        from pam_ctc.purchase_contract_progress
        where project_id in
        <foreach collection="list" index="index" item="ides4" open="(" separator="," close=")">
            #{ides4}
        </foreach>
        and deleted_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
        union
        select
            project_id
        from pam_ctc.purchase_order
        where project_id in
        <foreach collection="list" index="index" item="ides5" open="(" separator="," close=")">
            #{ides5}
        </foreach>
        and deleted_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
        union
        select
            project_id
        from pam_ctc.purchase_order_detail
        where project_id in
        <foreach collection="list" index="index" item="ides6" open="(" separator="," close=")">
            #{ides6}
        </foreach>
        and deleted_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
        union
        select
            project_id
        from pam_ctc.material_get_header
        where project_id in
        <foreach collection="list" index="index" item="ides7" open="(" separator="," close=")">
            #{ides7}
        </foreach>
        and deleted_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
        union
        select
            project_id
        from pam_ctc.material_return_header
        where project_id in
        <foreach collection="list" index="index" item="ides8" open="(" separator="," close=")">
            #{ides8}
        </foreach>
        and deleted_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
        union
        select
            project_id
        from pam_ctc.project_fee_collection
        where project_id in
        <foreach collection="list" index="index" item="ides9" open="(" separator="," close=")">
            #{ides9}
        </foreach>
        and deleted_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
        union
        select
            project_id
        from pam_ctc.working_hour
        where project_id in
        <foreach collection="list" index="index" item="ides10" open="(" separator="," close=")">
            #{ides10}
        </foreach>
        and delete_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
        union
        select
            id as project_id
        from pam_ctc.project
        where id in
        <foreach collection="list" index="index" item="ides11" open="(" separator="," close=")">
            #{ides11}
        </foreach>
        and deleted_flag = 0
        and (create_at >= #{queryDate} or update_at >= #{queryDate})
    </select>

    <update id="updateBatch" parameterType="java.util.List">
        update project_wbs_cost_summary set deleted_flag = 1 where project_id in
        <foreach collection="projectIds" index="index" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </update>

    <update id="updateBatchProjectWbsBudget" parameterType="java.util.List">
        update pam_ctc.project_wbs_budget
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="demand_cost =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.demandCost !=null">
                        when id=#{item.id} then #{item.demandCost}
                    </if>
                </foreach>
            </trim>
            <trim prefix="on_the_way_cost =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.onTheWayCost !=null">
                        when id=#{item.id} then #{item.onTheWayCost}
                    </if>
                </foreach>
            </trim>
            <trim prefix="incurred_cost =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.incurredCost !=null">
                        when id=#{item.id} then #{item.incurredCost}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remaining_cost =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.remainingCost !=null">
                        when id=#{item.id} then #{item.remainingCost}
                    </if>
                </foreach>
            </trim>
            <trim prefix="change_accumulate_cost =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.changeAccumulateCost !=null">
                        when id=#{item.id} then #{item.changeAccumulateCost}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="findContractRs" resultType="com.midea.pam.common.ctc.dto.ContractDTO">
        select
          c.id, c.name, c.code, c.status, c.amount, c.excluding_tax_amount as excludingTaxAmount
        from pam_ctc.project_contract_rs pcr
        left join pam_ctc.contract c on pcr.contract_id = c.id
        where pcr.deleted_flag = 0 and pcr.project_id = #{projectId}
    </select>

    <select id="summaryInvoicePlanAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(tax_included_price),0) from pam_ctc.invoice_plan_detail where deleted_flag = 0 and contract_id = #{contactId}
    </select>

    <select id="summaryReceiptPlanAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(actual_amount),0) from pam_ctc.receipt_plan_detail where deleted_flag = 0 and contract_id = #{contactId}
    </select>

    <select id="selectContractRsList" resultType="com.midea.pam.common.ctc.dto.ContractDTO">
        select pcr.project_id as projectId,
        c.id, c.name, c.code, c.status, c.amount, c.excluding_tax_amount as excludingTaxAmount
        from pam_ctc.project_contract_rs pcr
        left join pam_ctc.contract c on pcr.contract_id = c.id
        where pcr.deleted_flag = 0
        <if test="list != null">
            and pcr.project_id in
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>