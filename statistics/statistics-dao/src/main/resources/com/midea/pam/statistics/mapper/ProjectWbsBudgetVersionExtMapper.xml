<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ProjectWbsBudgetVersionExtMapper">


    <select id="listByParam" parameterType="java.util.Map" resultType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersionDetail">
        select
            id,
            version_id as versionId,
            project_wbs_budget_id as projectWbsBudgetId,
            project_id as projectId,
            project_code as projectCode,
            description,
            activity_order_no as activityOrderNo,
            activity_code as activityCode,
            activity_name as activityName,
            activity_type as activityType,
            wbs_full_code as wbsFullCode,
            wbs_summary_code as wbsSummaryCode,
            wbs_last_code as wbsLastCode,
            dynamic_wbs_template_rule_ids as dynamicWbsTemplateRuleIds,
            dynamic_fields as dynamicFields,
            dynamic_values as dynamicValues,
            price,
            parent_wbs_id as parentWbsId,
            parent_activity_id as parentActivityId,
            `version`,
            fee_type_id as feeTypeId,
            fee_type_name as feeTypeName,
            fee_sync_ems as feeSyncEms,
            create_by as createBy,
            create_at as createAt,
            update_by as updateBy,
            update_at as updateAt,
            deleted_flag as deletedFlag
        from
            pam_statistics.project_wbs_budget_version_detail t1
        where
            t1.version_id = #{versionId,jdbcType=BIGINT}
        <if test="projectId != null and projectId != ''">
            AND t1.project_id = #{projectId}
        </if>
        <if test="description != null and description != ''">
            AND t1.description LIKE concat('%', #{description, jdbcType=VARCHAR}, '%')
        </if>
        <if test="activityCode != null and activityCode != ''">
            AND t1.activity_code = #{activityCode}
        </if>
        <if test="activityType != null and activityType != ''">
            AND t1.activity_type LIKE concat('%', #{activityType, jdbcType=VARCHAR}, '%')
        </if>
        <if test="activityName != null and activityName != ''">
            AND t1.activity_name LIKE concat('%', #{activityName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="dynamicFieldList != null and dynamicFieldList.size > 0">
            <foreach collection="dynamicFieldList" item="item" index="index">
                AND t1.project_wbs_budget_id IN (
                SELECT project_wbs_budget_id
                FROM pam_ctc.project_wbs_budget_dynamic
                WHERE wbs_template_rule_detail_code = #{item.value}
                AND field_name = #{item.key}
                <if test="projectId != null and projectId != ''">
                    AND project_id = #{projectId}
                </if>
                )
            </foreach>
        </if>
            AND t1.deleted_flag = 0
        ORDER BY t1.wbs_full_code,t1.activity_code
    </select>


    <select id="getHeaderById" resultType="com.midea.pam.common.ctc.dto.ProjectHistoryHeaderDto">
        select
            phh.id,
            phh.project_id as projectId,
            p.code as code,
            phh.change_type as changeType,
            lui.username as createByMip,
            lui.name as createByName,
            phh.create_by as createBy
        from
            pam_ctc.project_history_header phh
        left join pam_ctc.project p on
            phh.project_id = p.id
        left join
            pam_basedata.ltc_user_info lui on
            phh.update_by = lui.id
        where
            phh.id = #{projectHistoryHeaderId,jdbcType=BIGINT}
        	and phh.change_type in (6, 10, 11)
	        and phh.status = 4
    </select>

    <select id="getVersionListByJob" resultType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersion">
        select
            null as projectHistoryHeaderId,
            p.id as projectId,
            p.code as projectCode,
            1 as businessType,
            null as createByMip,
            null as createByName,
            -1 as createBy
        from
            pam_ctc.project p
        where
            p.deleted_flag = 0
        <if test="ouIds != null and ouIds.size > 0">
            and p.ou_id in
            <foreach collection="ouIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and exists ( select 1 from pam_ctc.project_wbs_budget pwb where pwb.project_id = p.id and pwb.deleted_flag = 0)
        order by p.id
    </select>

    <insert id="insertDetail" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersion">
        INSERT INTO pam_statistics.project_wbs_budget_version_detail (
            id,
            version_id,
            project_wbs_budget_id,
            project_id,
            project_code,
            description,
            activity_order_no,
            activity_code,
            activity_name,
            activity_type,
            wbs_full_code,
            wbs_summary_code,
            wbs_last_code,
            dynamic_wbs_template_rule_ids,
            dynamic_fields,
            dynamic_values,
            price,
            parent_wbs_id,
            parent_activity_id,
            fee_type_id,
            fee_type_name,
            fee_sync_ems,
            create_by,
            create_at,
            update_by,
            update_at,
            deleted_flag
        )
        SELECT
            null,
            #{id},
            id,
            project_id,
            project_code,
            description,
            activity_order_no,
            activity_code,
            activity_name,
            activity_type,
            wbs_full_code,
            wbs_summary_code,
            wbs_last_code,
            dynamic_wbs_template_rule_ids,
            dynamic_fields,
            dynamic_values,
            price,
            parent_wbs_id,
            parent_activity_id,
            fee_type_id,
            fee_type_name,
            fee_sync_ems,
            create_by,
            create_at,
            update_by,
            update_at,
            deleted_flag
        FROM
            pam_ctc.project_wbs_budget
        WHERE
            project_id = #{projectId} and deleted_flag = 0
    </insert>

    <insert id="batchInsertVersion" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersion">
        insert into project_wbs_budget_version (id, project_history_header_id, project_id,
          project_code, business_type, version_time,
          version_code, create_by_mip, create_by_name,
          create_by, create_at, deleted_flag)
        values
        <foreach collection="list" item="obj" separator=",">
            (#{obj.id,jdbcType=BIGINT}, #{obj.projectHistoryHeaderId,jdbcType=BIGINT}, #{obj.projectId,jdbcType=BIGINT},
              #{obj.projectCode,jdbcType=VARCHAR}, #{obj.businessType,jdbcType=TINYINT}, #{obj.versionTime,jdbcType=TIMESTAMP},
              #{obj.versionCode,jdbcType=VARCHAR}, #{obj.createByMip,jdbcType=VARCHAR}, #{obj.createByName,jdbcType=VARCHAR},
              #{obj.createBy,jdbcType=BIGINT}, #{obj.createAt,jdbcType=TIMESTAMP}, #{obj.deletedFlag,jdbcType=TINYINT})
        </foreach>
  </insert>

    <insert id="batchInsertVersionDetail" useGeneratedKeys="true" keyProperty="id" parameterType="java.lang.Long">
        insert into
            pam_statistics.project_wbs_budget_version_detail (
            id,
            version_id,
            project_wbs_budget_id,
            project_id,
            project_code,
            description,
            activity_order_no,
            activity_code,
            activity_name,
            activity_type,
            wbs_full_code,
            wbs_summary_code,
            wbs_last_code,
            dynamic_wbs_template_rule_ids,
            dynamic_fields,
            dynamic_values,
            price,
            parent_wbs_id,
            parent_activity_id,
            fee_type_id,
            fee_type_name,
            fee_sync_ems,
            create_by,
            create_at,
            update_by,
            update_at,
            deleted_flag)
        select
            null,
            pwbv.id as version_id,
            wb.id as project_wbs_budget_id,
            wb.project_id,
            wb.project_code,
            wb.description,
            wb.activity_order_no,
            wb.activity_code,
            wb.activity_name,
            wb.activity_type,
            wb.wbs_full_code,
            wb.wbs_summary_code,
            wb.wbs_last_code,
            wb.dynamic_wbs_template_rule_ids,
            wb.dynamic_fields,
            wb.dynamic_values,
            wb.price,
            wb.parent_wbs_id,
            wb.parent_activity_id,
            wb.fee_type_id,
            wb.fee_type_name,
            wb.fee_sync_ems,
            wb.create_by,
            wb.create_at,
            wb.update_by,
            wb.update_at,
            wb.deleted_flag
        from
            pam_statistics.project_wbs_budget_version pwbv
        left join pam_ctc.project_wbs_budget wb on
            pwbv.project_id = wb.project_id
            and wb.deleted_flag = 0
        where
            pwbv.project_history_header_id = #{id,jdbcType=BIGINT}
    </insert>

</mapper>