<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.MaterialBudgetDetailExtMapper">

    <!-- 批量插入物料预算明细数据 -->
    <insert id="batchInsert" parameterType="com.midea.pam.common.statistics.entity.MaterialBudgetDetail">
        INSERT INTO pam_statistics.material_budget_detail (
            id, report_id, execute_id, project_id, project_code, project_name, ou_id,
            num, boom_level, ext, materiel_type, materiel_descr,
            pam_code, erp_code, name, unit, number,
            total_num, requirement_num, purchase_order_placed, inventory_quantity,
            material_received, not_ordered, package_price_estimate_unit_price, purchase_unit_price,
            average_unit_cost_material, expected_pending_cost, total_purchase_cost, total_material_cost,
            remark, delivery_time, model, brand,
            create_by, create_at, update_by, update_at, deleted_flag
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id,jdbcType=BIGINT},
                #{item.reportId,jdbcType=BIGINT},
                #{item.executeId,jdbcType=BIGINT},
                #{item.projectId,jdbcType=BIGINT},
                #{item.projectCode,jdbcType=VARCHAR},
                #{item.projectName,jdbcType=VARCHAR},
                #{item.ouId,jdbcType=BIGINT},
                #{item.num,jdbcType=VARCHAR},
                #{item.boomLevel,jdbcType=INTEGER},
                #{item.ext,jdbcType=VARCHAR},
                #{item.materielType,jdbcType=VARCHAR},
                #{item.materielDescr,jdbcType=VARCHAR},
                #{item.pamCode,jdbcType=VARCHAR},
                #{item.erpCode,jdbcType=VARCHAR},
                #{item.name,jdbcType=VARCHAR},
                #{item.unit,jdbcType=VARCHAR},
                #{item.number,jdbcType=DECIMAL},
                #{item.totalNum,jdbcType=DECIMAL},
                #{item.requirementNum,jdbcType=DECIMAL},
                #{item.purchaseOrderPlaced,jdbcType=DECIMAL},
                #{item.inventoryQuantity,jdbcType=DECIMAL},
                #{item.materialReceived,jdbcType=DECIMAL},
                #{item.notOrdered,jdbcType=DECIMAL},
                #{item.packagePriceEstimateUnitPrice,jdbcType=DECIMAL},
                #{item.purchaseUnitPrice,jdbcType=DECIMAL},
                #{item.averageUnitCostMaterial,jdbcType=DECIMAL},
                #{item.expectedPendingCost,jdbcType=DECIMAL},
                #{item.totalPurchaseCost,jdbcType=DECIMAL},
                #{item.totalMaterialCost,jdbcType=DECIMAL},
                #{item.remark,jdbcType=VARCHAR},
                #{item.deliveryTime,jdbcType=DATE},
                #{item.model,jdbcType=VARCHAR},
                #{item.brand,jdbcType=VARCHAR},
                #{item.createBy,jdbcType=BIGINT},
                #{item.createAt,jdbcType=TIMESTAMP},
                #{item.updateBy,jdbcType=BIGINT},
                #{item.updateAt,jdbcType=TIMESTAMP},
                #{item.deletedFlag,jdbcType=TINYINT}
            )
        </foreach>
    </insert>

    <!-- 根据参数查询项目列表 -->
    <select id="queryProjectsByParams" resultType="com.midea.pam.common.ctc.entity.Project">
        SELECT
            id,
            code,
            name,
            ou_id as ouId,
            status,
            deleted_flag as deletedFlag
        FROM pam_ctc.project
        WHERE deleted_flag = 0
        <if test="projectStatusList != null and projectStatusList.size() > 0">
            AND status IN
            <foreach collection="projectStatusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="ouIdList != null and ouIdList.size() > 0">
            AND ou_id IN
            <foreach collection="ouIdList" item="ouId" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>
        <if test="projectCode != null and projectCode != ''">
            AND code = #{projectCode}
        </if>
        <!-- 数据权限 -->
        <if test="unitIds != null">
            and unit_id in
            <foreach collection="unitIds" item="unitId" index="index" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>
        <!-- 我的权限 -->
        <if test="personal != null">
            and (id in (
            select pm.project_id from pam_ctc.project_member pm where pm.user_id = #{personal} and pm.deleted_flag = 0)
            or manager_id = #{personal}
            or create_by = #{personal}
            or financial = #{personal}
            or technology_leader_id = #{personal}
            or plan_designer_id = #{personal}
            or sales_manager_id = #{personal}
            )
        </if>
        ORDER BY id DESC
    </select>

    <!-- 根据项目ID查询库存组织ID -->
    <select id="queryOrgIdByProjectId" resultType="java.lang.Long">
        select storage_id from pam_ctc.project_profit where project_id = #{projectId} limit 1
    </select>

    <!-- 根据项目ID查询详设数据，包含requirement_num的正确计算逻辑 -->
    <select id="queryDesignPlanDetailsByProjectId" resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto">
        SELECT
            mdpd.id,
            mdpd.project_id as projectId,
            mdpd.parent_id as parentId,
            mdpd.project_budget_material_id as projectBudgetMaterialId,
            mdpd.ext,
            mdpd.materiel_type as materielType,
            mdpd.materiel_descr as materielDescr,
            mdpd.pam_code as pamCode,
            mdpd.erp_code as erpCode,
            mdpd.name,
            mdpd.unit,
            mdpd.number,
            ifnull(if(pt.requriement_deliver_mrp = 1, mpmd.number, t2.publish_num), 0) as requirementNum,
            mdpd.remark,
            mdpd.delivery_time as deliveryTime,
            mdpd.model,
            mdpd.brand,
            mdpd.status,
            mdpd.deleted_flag as deletedFlag
        FROM pam_ctc.milepost_design_plan_detail mdpd
        INNER JOIN pam_ctc.project p ON mdpd.project_id = p.id
        INNER JOIN pam_ctc.project_type pt ON p.type = pt.id
        LEFT JOIN pam_ctc.milepost_design_plan_material_detail mpmd ON mpmd.design_plan_detail_id = mdpd.id AND mpmd.deleted_flag = 0
        LEFT JOIN (
            SELECT
                releaseDetail.design_plan_detail_id,
                IFNULL(SUM(releaseDetail.publish_num), 0) as publish_num
            FROM pam_ctc.purchase_material_release_detail releaseDetail
            WHERE releaseDetail.deleted_flag = 0
              AND releaseDetail.project_id = #{projectId}
            GROUP BY releaseDetail.design_plan_detail_id
        ) t2 ON t2.design_plan_detail_id = mdpd.id
        WHERE mdpd.project_id = #{projectId}
          AND mdpd.deleted_flag = 0
    </select>

    <!-- 批量查询采购订单数据 -->
    <select id="batchQueryPurchaseOrderData" resultType="java.util.Map">
        SELECT
            pod.erp_code,
            SUM(order_num - cancel_num) as purchase_order_placed,
            SUM(pod.cost * ifnull(po.conversion_rate, 1) * (order_num - cancel_num)) as total_purchase_cost
        FROM pam_ctc.purchase_order_detail pod
        INNER JOIN pam_ctc.purchase_order po on pod.purchase_order_id = po.id AND po.order_status not in (2,9) AND po.deleted_flag = 0
        WHERE pod.project_id = #{params.projectId}
          AND pod.status in (1, 2, 4)
          AND pod.deleted_flag = 0
        GROUP BY erp_code
    </select>

    <!-- 批量查询库存数据 -->
    <select id="batchQueryStorageData" resultType="java.util.Map">
        SELECT
            segment1 as erp_code,
            SUM(transaction_quantity) as inventory_quantity
        FROM pam_statistics.project_cost_storage_record
        WHERE project_id = #{params.projectId}
          AND execute_id = #{params.executeId}
          AND deleted_flag = 0
        GROUP BY segment1
    </select>

    <!-- 批量查询领料数据 -->
    <select id="batchQueryMaterialData" resultType="java.util.Map">
        SELECT
            erp_code,
            SUM(CASE WHEN order_type = '领料' THEN total_actual_amount ELSE 0 END) -
            SUM(CASE WHEN order_type = '退料' THEN total_actual_amount ELSE 0 END) as material_received,
            SUM(CASE WHEN order_type = '领料' THEN get_return_amount ELSE 0 END) -
            SUM(CASE WHEN order_type = '退料' THEN get_return_amount ELSE 0 END) as total_material_cost
        FROM pam_statistics.project_cost_getreturn_material_record
        WHERE project_id = #{params.projectId}
          AND execute_id = #{params.executeId}
          AND order_type IN ('领料', '退料')
          AND status = '已处理'
          AND deleted_flag = 0
        GROUP BY erp_code
    </select>

    <!-- 批量查询一揽子价格数据 -->
    <select id="batchQueryBpaPriceData" resultType="java.util.Map">
        SELECT
            material_code as erp_code,
            MAX(price_override) as bpa_price
        FROM pam_ctc.purchase_bpa_price
        WHERE ou_id = #{params.ouId}
          AND deleted_flag = 0
          AND price_override IS NOT NULL
          AND start_date &lt;= NOW()
          AND (end_date IS NULL OR end_date &gt;= NOW())
          AND material_code IN
          <foreach collection="params.erpCodeList" item="erpCode" open="(" separator="," close=")">
              #{erpCode}
          </foreach>
        GROUP BY material_code
    </select>

    <!-- 批量查询物料成本数据 -->
    <select id="batchQueryMaterialCostData" resultType="java.util.Map">
        SELECT
            item_code as erp_code,
            item_cost as material_cost
        FROM (
            SELECT
                item_code,
                item_cost,
                ROW_NUMBER() OVER (PARTITION BY item_code ORDER BY create_at DESC) as rn
            FROM pam_basedata.material_cost
            WHERE organization_id = #{params.orgId}
              AND material_cost_type = 3
              AND item_cost IS NOT NULL
              AND deleted_flag = 0
              AND item_code IN
              <foreach collection="params.erpCodeList" item="erpCode" open="(" separator="," close=")">
                  #{erpCode}
              </foreach>
        ) ranked
        WHERE rn = 1
    </select>

    <!-- 查询项目预算创建日期 -->
    <select id="queryBudgetCreateDates" resultType="java.util.Map">
        SELECT
            id,
            create_at
        FROM pam_ctc.project_budget_material
        WHERE project_id = #{params.projectId}
          AND deleted_flag = 0
          AND id IN
          <foreach collection="params.budgetMaterialIds" item="budgetMaterialId" open="(" separator="," close=")">
              #{budgetMaterialId}
          </foreach>
    </select>

</mapper>
