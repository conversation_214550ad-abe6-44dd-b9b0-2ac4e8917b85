<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.MaterialTransferStatisticsExtMapper">
    <resultMap id="BaseResultDtoMap" type="com.midea.pam.common.ctc.dto.MaterialTransferDto">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="transfer_code" jdbcType="VARCHAR" property="transferCode" />
        <result column="apply_user_id" jdbcType="BIGINT" property="applyUserId" />
        <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
        <result column="fill_user_id" jdbcType="BIGINT" property="fillUserId" />
        <result column="fill_user_name" jdbcType="VARCHAR" property="fillUserName" />
        <result column="finance_user_id" jdbcType="BIGINT" property="financeUserId" />
        <result column="finance_user_name" jdbcType="VARCHAR" property="financeUserName" />
        <result column="organization_id" jdbcType="BIGINT" property="organizationId" />
        <result column="organization_code" jdbcType="VARCHAR" property="organizationCode" />
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName" />
        <result column="send_project_id" jdbcType="BIGINT" property="sendProjectId" />
        <result column="send_project_code" jdbcType="VARCHAR" property="sendProjectCode" />
        <result column="send_project_name" jdbcType="VARCHAR" property="sendProjectName" />
        <result column="send_inventory_id" jdbcType="BIGINT" property="sendInventoryId" />
        <result column="send_inventory_code" jdbcType="VARCHAR" property="sendInventoryCode" />
        <result column="send_inventory_name" jdbcType="VARCHAR" property="sendInventoryName" />
        <result column="send_location_id" jdbcType="BIGINT" property="sendLocationId" />
        <result column="send_location" jdbcType="VARCHAR" property="sendLocation" />
        <result column="receive_project_id" jdbcType="BIGINT" property="receiveProjectId" />
        <result column="receive_project_code" jdbcType="VARCHAR" property="receiveProjectCode" />
        <result column="receive_project_name" jdbcType="VARCHAR" property="receiveProjectName" />
        <result column="receive_inventory_id" jdbcType="BIGINT" property="receiveInventoryId" />
        <result column="receive_inventory_code" jdbcType="VARCHAR" property="receiveInventoryCode" />
        <result column="receive_inventory_name" jdbcType="VARCHAR" property="receiveInventoryName" />
        <result column="receive_location_id" jdbcType="BIGINT" property="receiveLocationId" />
        <result column="receive_location" jdbcType="VARCHAR" property="receiveLocation" />
        <result column="account_alias_id" jdbcType="BIGINT" property="accountAliasId" />
        <result column="account_alias_name" jdbcType="VARCHAR" property="accountAliasName" />
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate" />
        <result column="erp_status" jdbcType="TINYINT" property="erpStatus" />
        <result column="erp_msg" jdbcType="VARCHAR" property="erpMsg" />
        <result column="workflow_flag" jdbcType="TINYINT" property="workflowFlag" />
        <result column="print_count" jdbcType="INTEGER" property="printCount" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="sendManagerName" jdbcType="VARCHAR" property="sendManagerName" />
        <result column="receiveManagerName" jdbcType="VARCHAR" property="receiveManagerName" />
        <result column="totalApplyAmount" jdbcType="DECIMAL" property="totalApplyAmount" />
        <result column="totalActualAmount" jdbcType="DECIMAL" property="totalActualAmount" />
    </resultMap>

    <sql id="Base_Column_List">
        h.id, h.status, h.transfer_code, h.apply_user_id, h.apply_user_name, h.fill_user_id, h.fill_user_name, h.
    finance_user_id, h.finance_user_name, h.organization_id, h.organization_code, h.organization_name, h.
    send_project_id, h.send_project_code, h.send_project_name, h.send_inventory_id, h.send_inventory_code, h.
    send_inventory_name, h.send_location_id, h.send_location, h.receive_project_id, h.receive_project_code, h.
    receive_project_name, h.receive_inventory_id, h.receive_inventory_code, h.receive_inventory_name, h.
    receive_location_id, h.receive_location, h.apply_date, h.erp_status, h.erp_msg, h.deleted_flag, h.remark,
    h.create_at, h.create_by, h.update_at, h.update_by,
    h.workflow_flag, h.account_alias_id, h.account_alias_name, h.print_count,o.operating_unit_name as operatingUnitName,
    o.operating_unit_id as operatingUnitId
    </sql>

    <sql id="Dto_Column">
      p1.manager_name as sendManagerName,
      p2.manager_name as receiveManagerName,
      (select sum(d.apply_amount) from pam_ctc.material_transfer_detail d where d.header_id = h.id and (d.deleted_flag is null or d.deleted_flag = 0)) as totalApplyAmount,
      (select sum(d.actual_amount) from pam_ctc.material_transfer_detail d where d.header_id = h.id and (d.deleted_flag is null or d.deleted_flag = 0)) as totalActualAmount
    </sql>

    <sql id="Where_Clause">
        <if test="id != null">
            and h.id = #{id}
        </if>
        <if test="status != null">
            and h.status = #{status}
        </if>
        <if test="transferCode != null and transferCode != ''">
            and h.transfer_code like CONCAT('%',#{transferCode},'%')
        </if>
        <if test="applyUserName != null and applyUserName != ''">
            and h.apply_user_name like CONCAT('%',#{applyUserName},'%')
        </if>
        <if test="fillUserName != null and fillUserName != ''">
            and h.fill_user_name like CONCAT('%',#{fillUserName},'%')
        </if>
        <if test="applyUserId != null">
            and h.apply_user_id = #{applyUserId}
        </if>
        <if test="fillUserId != null">
            and h.fill_user_id = #{fillUserId}
        </if>
        <if test="organizationName != null and organizationName != ''">
            and h.organization_name like CONCAT('%',#{organizationName},'%')
        </if>
        <if test="sendProjectCode != null and sendProjectCode != ''">
            and h.send_project_code like CONCAT('%',#{sendProjectCode},'%')
        </if>
        <if test="sendInventoryCode != null and sendInventoryCode != ''">
            and h.send_inventory_code like CONCAT('%',#{sendInventoryCode},'%')
        </if>
        <if test="sendLocation != null and sendLocation != ''">
            and h.send_location like CONCAT('%',#{sendLocation},'%')
        </if>
        <if test="sendManagerName != null and sendManagerName != ''">
            and p1.manager_name like concat('%', #{sendManagerName}, '%')
        </if>
        <if test="receiveProjectCode != null and receiveProjectCode != ''">
            and h.receive_project_code like CONCAT('%',#{receiveProjectCode},'%')
        </if>
        <if test="receiveInventoryCode != null and receiveInventoryCode != ''">
            and h.receive_inventory_code like CONCAT('%',#{receiveInventoryCode},'%')
        </if>
        <if test="receiveLocation != null and receiveLocation != ''">
            and h.receive_location like CONCAT('%',#{receiveLocation},'%')
        </if>
        <if test="receiveManagerName != null and receiveManagerName != ''">
            and p2.manager_name like concat('%', #{receiveManagerName}, '%')
        </if>
        <if test="applyDateStart != null">
            <![CDATA[ and (h.apply_date is null or h.apply_date >= #{applyDateStart}) ]]>
        </if>
        <if test="applyDateEnd != null">
            <![CDATA[ and (h.apply_date is null or h.apply_date <= #{applyDateEnd}) ]]>
        </if>
        <if test="organizationId != null">
            and h.organization_id = #{organizationId}
        </if>
        <if test="statusList != null">
            and h.status in
            <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="erpCodes != null">
            and h.erp_status in
            <foreach collection="erpCodes" item="erpCode" index="index" open="(" separator="," close=")">
                #{erpCode}
            </foreach>
        </if>
        <if test="resouce != null and resouce != ''">
            and h.status = 5
            and (select ifnull(sum(d.actual_amount),0) from pam_ctc.material_transfer_detail d where d.header_id = h.id and (d.deleted_flag is null or d.deleted_flag = 0)) <![CDATA[ <> ]]> 0
            and ((h.erp_status = 3) or (h.erp_status = 1 and a.syn_start_time &lt; date_sub(sysdate(),interval 2 hour)) or (h.erp_status = -1 and h.create_at &lt; date_sub(sysdate(),interval 2 hour)))
        </if>
        <if test="organizationCodeList != null">
            and
            <foreach collection="organizationCodeList" item="organizationCode" index="index" open="(" separator=" or " close=")">
                h.organization_code=#{organizationCode}
            </foreach>
        </if>
        <if test="operatingUnitIdList != null">
            and
            <foreach collection="operatingUnitIdList" item="operatingUnitId" index="index" open="(" separator=" or " close=")">
                o.operating_unit_id = #{operatingUnitId}
            </foreach>
        </if>
        and h.organization_id IS NOT NULL
    </sql>

    <select id="materialTransferHeaderList" resultMap="BaseResultDtoMap">
        select distinct
        <include refid="Base_Column_List"/>
        ,
        <include refid="Dto_Column"/>
        from pam_ctc.material_transfer_header h
        left join pam_ctc.agency_syn_info a on a.apply_no = concat(h.id,'') and a.deleted_flag = 0
        left join pam_ctc.project p1 on h.send_project_id = p1.id
        left join pam_ctc.project p2 on h.receive_project_id = p2.id
        left join pam_basedata.organization_rel o on h.organization_id = o.organization_id and h.deleted_flag = 0
        where (h.deleted_flag is null or h.deleted_flag = 0)
        <include refid="Where_Clause"></include>
        order by h.create_at desc
    </select>

    <select id="materialTransferDetailList" resultType="com.midea.pam.common.ctc.vo.MaterialTransferExcelVO">
        select distinct
        d.id as id,
        h.transfer_code as transferCode,
        h.status as status,
        h.apply_user_name as applyUserName,
        h.fill_user_name as fillUserName,
        h.send_project_code as sendProjectCode,
        h.send_project_name as sendProjectName,
        h.send_inventory_code as sendInventoryCode,
        h.send_location as sendLocation,
        h.receive_project_code as receiveProjectCode,
        h.receive_project_name as receiveProjectName,
        h.receive_inventory_code as receiveInventoryCode,
        h.receive_location as receiveLocation,
        d.material_code as materialCode,
        d.material_name as materialName,
        d.unit as unit,
        h.apply_date as applyDate,
        d.apply_amount as applyAmount,
        d.actual_amount as actualAmount,
        d.process_date as processDate,
        d.erp_status as erpStatus,
        d.erp_msg as erpMsg,
        d.remark as remark,
        h.organization_code as organizationCode,
        p1.manager_name as sendManagerName,
        p2.manager_name as receiveManagerName,
        o.operating_unit_name as operatingUnitName,
        o.operating_unit_id as operatingUnitId,
        m.material_classification as materialClassification,
        m.coding_middleclass as codingMiddleclass,
        m.material_type as materialType
        from pam_ctc.material_transfer_header h
        left join pam_ctc.material_transfer_detail d
        on d.header_id = h.id

        left join pam_ctc.project p1 on h.send_project_id = p1.id
        left join pam_ctc.project p2 on h.receive_project_id = p2.id
        left join pam_basedata.organization_rel o on h.organization_id = o.organization_id and h.deleted_flag = 0
        left join pam_basedata.material m on h.organization_id = m.organization_id and d.material_code = m.item_code and m.delete_flag = 0
        where (h.deleted_flag is null or h.deleted_flag = 0) and (d.deleted_flag is null or d.deleted_flag = 0)
        <include refid="Where_Clause"></include>
        order by h.create_at desc
    </select>

    <select id="getPortalRemindCount" parameterType="java.util.ArrayList" resultType="java.lang.Long">
        SELECT
          COUNT(0)
        FROM
          pam_ctc.material_transfer_header h
          LEFT JOIN  pam_basedata.organization_rel  o  ON h.organization_id = o.organization_id
        WHERE h.`deleted_flag` = 0
          AND h.`status` = 4
        <if test="ouIds != null and ouIds.size &gt; 0">
            and
            <foreach collection="ouIds" item="operatingUnitId" index="index" open="(" separator=" or " close=")">
                o.operating_unit_id = #{operatingUnitId}
            </foreach>
        </if>
    </select>

</mapper>