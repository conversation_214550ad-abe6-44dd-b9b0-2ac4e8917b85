<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.MaterialBudgetDetailMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.statistics.entity.MaterialBudgetDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_id" jdbcType="BIGINT" property="reportId" />
    <result column="execute_id" jdbcType="BIGINT" property="executeId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="ou_id" jdbcType="BIGINT" property="ouId" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="boom_level" jdbcType="INTEGER" property="boomLevel" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="materiel_type" jdbcType="VARCHAR" property="materielType" />
    <result column="materiel_descr" jdbcType="VARCHAR" property="materielDescr" />
    <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
    <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="number" jdbcType="DECIMAL" property="number" />
    <result column="total_num" jdbcType="DECIMAL" property="totalNum" />
    <result column="requirement_num" jdbcType="DECIMAL" property="requirementNum" />
    <result column="purchase_order_placed" jdbcType="DECIMAL" property="purchaseOrderPlaced" />
    <result column="inventory_quantity" jdbcType="DECIMAL" property="inventoryQuantity" />
    <result column="material_received" jdbcType="DECIMAL" property="materialReceived" />
    <result column="not_ordered" jdbcType="DECIMAL" property="notOrdered" />
    <result column="package_price_estimate_unit_price" jdbcType="DECIMAL" property="packagePriceEstimateUnitPrice" />
    <result column="purchase_unit_price" jdbcType="DECIMAL" property="purchaseUnitPrice" />
    <result column="average_unit_cost_material" jdbcType="DECIMAL" property="averageUnitCostMaterial" />
    <result column="expected_pending_cost" jdbcType="DECIMAL" property="expectedPendingCost" />
    <result column="total_purchase_cost" jdbcType="DECIMAL" property="totalPurchaseCost" />
    <result column="total_material_cost" jdbcType="DECIMAL" property="totalMaterialCost" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_id, execute_id, project_id, project_code, project_name, ou_id, num, boom_level, 
    ext, materiel_type, materiel_descr, pam_code, erp_code, name, unit, number, total_num, 
    requirement_num, purchase_order_placed, inventory_quantity, material_received, not_ordered, 
    package_price_estimate_unit_price, purchase_unit_price, average_unit_cost_material, 
    expected_pending_cost, total_purchase_cost, total_material_cost, remark, delivery_time, 
    model, brand, create_by, create_at, update_by, update_at, deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.statistics.entity.MaterialBudgetDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from material_budget_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from material_budget_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from material_budget_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.statistics.entity.MaterialBudgetDetail">
    insert into material_budget_detail (id, report_id, execute_id, 
      project_id, project_code, project_name, 
      ou_id, num, boom_level, 
      ext, materiel_type, materiel_descr, 
      pam_code, erp_code, name, 
      unit, number, total_num, 
      requirement_num, purchase_order_placed, inventory_quantity, 
      material_received, not_ordered, package_price_estimate_unit_price, 
      purchase_unit_price, average_unit_cost_material, 
      expected_pending_cost, total_purchase_cost, 
      total_material_cost, remark, delivery_time, 
      model, brand, create_by, 
      create_at, update_by, update_at, 
      deleted_flag)
    values (#{id,jdbcType=BIGINT}, #{reportId,jdbcType=BIGINT}, #{executeId,jdbcType=BIGINT}, 
      #{projectId,jdbcType=BIGINT}, #{projectCode,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR}, 
      #{ouId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, #{boomLevel,jdbcType=INTEGER}, 
      #{ext,jdbcType=VARCHAR}, #{materielType,jdbcType=VARCHAR}, #{materielDescr,jdbcType=VARCHAR}, 
      #{pamCode,jdbcType=VARCHAR}, #{erpCode,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{unit,jdbcType=VARCHAR}, #{number,jdbcType=DECIMAL}, #{totalNum,jdbcType=DECIMAL}, 
      #{requirementNum,jdbcType=DECIMAL}, #{purchaseOrderPlaced,jdbcType=DECIMAL}, #{inventoryQuantity,jdbcType=DECIMAL}, 
      #{materialReceived,jdbcType=DECIMAL}, #{notOrdered,jdbcType=DECIMAL}, #{packagePriceEstimateUnitPrice,jdbcType=DECIMAL}, 
      #{purchaseUnitPrice,jdbcType=DECIMAL}, #{averageUnitCostMaterial,jdbcType=DECIMAL}, 
      #{expectedPendingCost,jdbcType=DECIMAL}, #{totalPurchaseCost,jdbcType=DECIMAL}, 
      #{totalMaterialCost,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{deliveryTime,jdbcType=DATE}, 
      #{model,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, 
      #{deletedFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.statistics.entity.MaterialBudgetDetail">
    insert into material_budget_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportId != null">
        report_id,
      </if>
      <if test="executeId != null">
        execute_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="ouId != null">
        ou_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="boomLevel != null">
        boom_level,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="materielType != null">
        materiel_type,
      </if>
      <if test="materielDescr != null">
        materiel_descr,
      </if>
      <if test="pamCode != null">
        pam_code,
      </if>
      <if test="erpCode != null">
        erp_code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="requirementNum != null">
        requirement_num,
      </if>
      <if test="purchaseOrderPlaced != null">
        purchase_order_placed,
      </if>
      <if test="inventoryQuantity != null">
        inventory_quantity,
      </if>
      <if test="materialReceived != null">
        material_received,
      </if>
      <if test="notOrdered != null">
        not_ordered,
      </if>
      <if test="packagePriceEstimateUnitPrice != null">
        package_price_estimate_unit_price,
      </if>
      <if test="purchaseUnitPrice != null">
        purchase_unit_price,
      </if>
      <if test="averageUnitCostMaterial != null">
        average_unit_cost_material,
      </if>
      <if test="expectedPendingCost != null">
        expected_pending_cost,
      </if>
      <if test="totalPurchaseCost != null">
        total_purchase_cost,
      </if>
      <if test="totalMaterialCost != null">
        total_material_cost,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportId != null">
        #{reportId,jdbcType=BIGINT},
      </if>
      <if test="executeId != null">
        #{executeId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="ouId != null">
        #{ouId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="boomLevel != null">
        #{boomLevel,jdbcType=INTEGER},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="materielType != null">
        #{materielType,jdbcType=VARCHAR},
      </if>
      <if test="materielDescr != null">
        #{materielDescr,jdbcType=VARCHAR},
      </if>
      <if test="pamCode != null">
        #{pamCode,jdbcType=VARCHAR},
      </if>
      <if test="erpCode != null">
        #{erpCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=DECIMAL},
      </if>
      <if test="totalNum != null">
        #{totalNum,jdbcType=DECIMAL},
      </if>
      <if test="requirementNum != null">
        #{requirementNum,jdbcType=DECIMAL},
      </if>
      <if test="purchaseOrderPlaced != null">
        #{purchaseOrderPlaced,jdbcType=DECIMAL},
      </if>
      <if test="inventoryQuantity != null">
        #{inventoryQuantity,jdbcType=DECIMAL},
      </if>
      <if test="materialReceived != null">
        #{materialReceived,jdbcType=DECIMAL},
      </if>
      <if test="notOrdered != null">
        #{notOrdered,jdbcType=DECIMAL},
      </if>
      <if test="packagePriceEstimateUnitPrice != null">
        #{packagePriceEstimateUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchaseUnitPrice != null">
        #{purchaseUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="averageUnitCostMaterial != null">
        #{averageUnitCostMaterial,jdbcType=DECIMAL},
      </if>
      <if test="expectedPendingCost != null">
        #{expectedPendingCost,jdbcType=DECIMAL},
      </if>
      <if test="totalPurchaseCost != null">
        #{totalPurchaseCost,jdbcType=DECIMAL},
      </if>
      <if test="totalMaterialCost != null">
        #{totalMaterialCost,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=DATE},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.statistics.entity.MaterialBudgetDetailExample" resultType="java.lang.Long">
    select count(*) from material_budget_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.statistics.entity.MaterialBudgetDetail">
    update material_budget_detail
    <set>
      <if test="reportId != null">
        report_id = #{reportId,jdbcType=BIGINT},
      </if>
      <if test="executeId != null">
        execute_id = #{executeId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        project_code = #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="ouId != null">
        ou_id = #{ouId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=VARCHAR},
      </if>
      <if test="boomLevel != null">
        boom_level = #{boomLevel,jdbcType=INTEGER},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="materielType != null">
        materiel_type = #{materielType,jdbcType=VARCHAR},
      </if>
      <if test="materielDescr != null">
        materiel_descr = #{materielDescr,jdbcType=VARCHAR},
      </if>
      <if test="pamCode != null">
        pam_code = #{pamCode,jdbcType=VARCHAR},
      </if>
      <if test="erpCode != null">
        erp_code = #{erpCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=DECIMAL},
      </if>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=DECIMAL},
      </if>
      <if test="requirementNum != null">
        requirement_num = #{requirementNum,jdbcType=DECIMAL},
      </if>
      <if test="purchaseOrderPlaced != null">
        purchase_order_placed = #{purchaseOrderPlaced,jdbcType=DECIMAL},
      </if>
      <if test="inventoryQuantity != null">
        inventory_quantity = #{inventoryQuantity,jdbcType=DECIMAL},
      </if>
      <if test="materialReceived != null">
        material_received = #{materialReceived,jdbcType=DECIMAL},
      </if>
      <if test="notOrdered != null">
        not_ordered = #{notOrdered,jdbcType=DECIMAL},
      </if>
      <if test="packagePriceEstimateUnitPrice != null">
        package_price_estimate_unit_price = #{packagePriceEstimateUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchaseUnitPrice != null">
        purchase_unit_price = #{purchaseUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="averageUnitCostMaterial != null">
        average_unit_cost_material = #{averageUnitCostMaterial,jdbcType=DECIMAL},
      </if>
      <if test="expectedPendingCost != null">
        expected_pending_cost = #{expectedPendingCost,jdbcType=DECIMAL},
      </if>
      <if test="totalPurchaseCost != null">
        total_purchase_cost = #{totalPurchaseCost,jdbcType=DECIMAL},
      </if>
      <if test="totalMaterialCost != null">
        total_material_cost = #{totalMaterialCost,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=DATE},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.statistics.entity.MaterialBudgetDetail">
    update material_budget_detail
    set report_id = #{reportId,jdbcType=BIGINT},
      execute_id = #{executeId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      project_code = #{projectCode,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      ou_id = #{ouId,jdbcType=BIGINT},
      num = #{num,jdbcType=VARCHAR},
      boom_level = #{boomLevel,jdbcType=INTEGER},
      ext = #{ext,jdbcType=VARCHAR},
      materiel_type = #{materielType,jdbcType=VARCHAR},
      materiel_descr = #{materielDescr,jdbcType=VARCHAR},
      pam_code = #{pamCode,jdbcType=VARCHAR},
      erp_code = #{erpCode,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      number = #{number,jdbcType=DECIMAL},
      total_num = #{totalNum,jdbcType=DECIMAL},
      requirement_num = #{requirementNum,jdbcType=DECIMAL},
      purchase_order_placed = #{purchaseOrderPlaced,jdbcType=DECIMAL},
      inventory_quantity = #{inventoryQuantity,jdbcType=DECIMAL},
      material_received = #{materialReceived,jdbcType=DECIMAL},
      not_ordered = #{notOrdered,jdbcType=DECIMAL},
      package_price_estimate_unit_price = #{packagePriceEstimateUnitPrice,jdbcType=DECIMAL},
      purchase_unit_price = #{purchaseUnitPrice,jdbcType=DECIMAL},
      average_unit_cost_material = #{averageUnitCostMaterial,jdbcType=DECIMAL},
      expected_pending_cost = #{expectedPendingCost,jdbcType=DECIMAL},
      total_purchase_cost = #{totalPurchaseCost,jdbcType=DECIMAL},
      total_material_cost = #{totalMaterialCost,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      delivery_time = #{deliveryTime,jdbcType=DATE},
      model = #{model,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>