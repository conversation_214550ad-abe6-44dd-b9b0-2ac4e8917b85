<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ProjectStatisticsExtMapper">

    <select id="list" resultType="com.midea.pam.common.ctc.dto.ProjectDto">
        select
        distinct p.id,
        p.code,
        p.name,
        p.status,
        p.currency,
        p.price_type as priceType,
        p.manager_name as managerName,
        pt.name as typeName,
        pt.cost_method as costMethod,
        pt.milestone_base_date as milestoneBaseDate,
        p.preview_flag as previewFlag,
        p.customer_name as customerName,
        c.crm_code as customerCode,
        p.amount,
        p.budget_cost as budgetCost,
        contract.code as contractCode,
        contract.name as contractName,
        p.business_id as businessId,
        p.business_id as businessCode,
        business.name as businessName,
        ou.operating_unit_name as ouName,
        p.ou_id as ouId,
        p.start_date as startDate,
        p.end_date as endDate,
        product.project_product_name as productName,
        unit.unit_name as unitName,
        p.financial as financial,
        ui.name as financialName,
        product.id as productId,
        p.create_by as createBy,
        p.create_at as createAt,
        p.close_time as closeTime,
        p.preview_approval_date as previewApprovalDate,
        ui2.name as createByName,
        p.approval_date as approvalDate1,
        p.preview_approval_date as approvalDate2,
        p.project_source as projectSource,
        p.is_objective_project as isObjectiveProject,
        p.project_level as projectLevel,
        p.approval_date as approvalDate,
        rdi.name as departmentName,
        p.unit_id as unitId,
        p.business_segment as businessSegment,
        p.transfer_project_state as transferProjectState,
        p.quality_assurance_start_time as qualityAssuranceStartTime,
        p.quality_assurance_end_time as qualityAssuranceEndTime,
        p.manager_id as managerId,
        pp.cost_method_main as costMethodMain,
        p.wbs_enabled as wbsEnabled,
        p.wbs_template_info_id as wbsTemplateInfoId,
        p.area,
        p.business_department as businessDepartment,
        p.detailed_address as detailedAddress,
        p.city
        from
        pam_ctc.project p
        left join pam_ctc.project_contract_rs pcr on
        pcr.project_id = p.id and pcr.deleted_flag = 0
        left join pam_ctc.contract contract on
        contract.id = pcr.contract_id
        left join pam_crm.customer c on
        p.customer_id = c.id
        left join pam_crm.business business on
        business.business_code = p.business_id
        left join pam_basedata.operating_unit ou on
        ou.id = p.ou_id
        left join pam_basedata.project_product_maintenance product on
        product.id = p.product_id
        left join pam_ctc.project_type pt on
        pt.id = p.`type`
        left join pam_basedata.unit unit on
        unit.id = p.unit_id
        left join pam_basedata.ltc_user_info ui on
        ui.id = p.financial
        left join pam_basedata.ltc_user_info ui2 on
        ui2.id = p.create_by
        left join pam_statistics.report_department_unit_rel rdur on
            rdur.unit_id = p.unit_id and rdur.deleted_flag =0
        left join pam_statistics.report_department_info rdi on
            rdi.id = rdur.department_id and rdi.deleted_flag =0
        left join pam_ctc.project_profit pp on
            pp.project_id = p.id
        where
        p.deleted_flag = 0
        <if test="areaList != null and areaList.size > 0">
            and p.area in
            <foreach collection="areaList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessDepartmentList != null and businessDepartmentList.size > 0">
            and p.business_department in
            <foreach collection="businessDepartmentList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="productIds != null and productIds.size > 0">
            and product.id in
            <foreach collection="productIds" item="productId" open="(" separator="," close=")">
                #{productId}
            </foreach>
        </if>
        <if test="city != null">
            and p.city like concat('%', #{city}, '%')
        </if>
        <if test="detailedAddress != null">
            and p.detailed_address like concat('%', #{detailedAddress}, '%')
        </if>
        <if test="isObjectiveProject != null ">
            and p.is_objective_project = #{isObjectiveProject}
        </if>

        <if test="projectLevel != null">
            and p.project_level = #{projectLevel}
        </if>

        <if test="code != null and code != ''">
            and p.code like concat('%', #{code}, '%')
        </if>

        <if test="name != null and name != ''">
            and p.name like concat('%', #{name}, '%')
        </if>

        <if test="unitName != null and unitName != ''">
            and unit.unit_name like concat('%', #{unitName}, '%')
        </if>

        <if test="fuzzyLike != null and fuzzyLike != ''">
            and (p.name like concat('%', #{fuzzyLike}, '%') or p.code like concat('%', #{fuzzyLike}, '%'))
        </if>

        <if test="customerCode != null and customerCode != ''">
            and c.crm_code like concat('%', #{customerCode}, '%')
        </if>

        <if test="customerName != null and customerName != ''">
            and p.customer_name like concat('%', #{customerName}, '%')
        </if>

        <if test="managerName != null and managerName != ''">
            and p.manager_name like concat('%', #{managerName}, '%')
        </if>

        <if test="status != null">
            and p.status = #{status}
        </if>

        <if test="priceType != null">
            and p.price_type = #{priceType}
        </if>

        <if test="type != null">
            and p.type = #{type}
        </if>

        <if test="ouId != null">
            and p.ou_id = #{ouId}
        </if>

        <if test="previewFlag != null">
            and p.preview_flag = #{previewFlag}
        </if>

        <if test="approvalDateStart != null">
            and p.approval_date &gt;= #{approvalDateStart}
        </if>

        <if test="approvalDateEnd != null">
            and p.approval_date &lt;= #{approvalDateEnd}
        </if>

        <if test="customerIds != null">
            and p.customer_id in
            <foreach collection="customerIds" item="customerId" index="index" open="(" separator="," close=")">
                #{customerId}
            </foreach>
        </if>

        <if test="businessId != null and businessId != ''">
            and p.business_id like concat('%', #{businessId}, '%')
        </if>

        <if test="contractCode != null and contractCode != ''">
            and contract.code like concat('%', #{contractCode}, '%')
        </if>

        <if test="ouIds != null">
            and p.ou_id in
            <foreach collection="ouIds" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>

        <if test="departmentNameStr != null">
            and rdi.name in
            <foreach collection="departmentNameStr" item="departmentName" index="index" open="(" separator="," close=")">
                #{departmentName}
            </foreach>
        </if>

        <if test="types != null">
            and p.type in
            <foreach collection="types" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>

        <!-- 项目属性 -->
        <if test="priceTypes != null">
            and p.price_type in
            <foreach collection="priceTypes" item="priceType" index="index" open="(" separator="," close=")">
                #{priceType}
            </foreach>
        </if>

        <if test="statuses != null">
            and p.status in
            <foreach collection="statuses" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <if test="resourceStatuses != null">
            and p.resource_status in
            <foreach collection="resourceStatuses" item="resourceStatus" index="index" open="(" separator="," close=")">
                #{resourceStatus}
            </foreach>
        </if>

        <if test="previewFlags != null">
            and p.preview_flag in
            <foreach collection="previewFlags" item="previewFlag" index="index" open="(" separator="," close=")">
                #{previewFlag}
            </foreach>
        </if>

        <if test="unitIds != null">
            and p.unit_id in
            <foreach collection="unitIds" item="unitId" index="index" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>

        <!-- 我的项目 数据权限 -->
        <if test="me">
            and (p.id in (
            select
            pm.project_id
            from
            pam_ctc.project_member pm
            where
            pm.user_id = #{userId}
            and pm.deleted_flag = 0)
            or p.manager_id = #{userId}
            or p.create_by = #{userId}
            or p.financial = #{userId}
            or p.technology_leader_id = #{userId}
            or p.plan_designer_id = #{userId}
            or p.sales_manager_id = #{userId})
        </if>
        <!-- 项目执行展示菜单 展示权限 -->
        <if test="isView != null">
            and (p.status in (4,9,10,16))
            and (pp.income_point_main = '0002')
            and p.preview_flag = 0
        </if>
        group by p.id
        <if test="orderParam != null">
            order by p.${orderParam}
        </if>

    </select>

    <select id="selectProjectBatchList" resultType="com.midea.pam.common.ctc.dto.ProjectDto">
        select
        p.id,
        p.code,
        p.name,
        p.status,
        p.price_type as priceType,
        p.manager_name as managerName,
        p.manager_id as managerId,
        pt.name as typeName,
        pt.cost_method as costMethod,
        p.preview_flag as previewFlag,
        p.customer_name as customerName,
        c.crm_code as customerCode,
        p.amount,
        p.budget_cost as budgetCost,
        contract.code as contractCode,
        p.business_id as businessId,
        p.business_id as businessCode,
        business.name as businessName,
        ou.operating_unit_name as ouName,
        p.ou_id as ouId,
        p.start_date as startDate,
        p.end_date as endDate,
        product.project_product_name as productName,
        unit.unit_name as unitName,
        p.financial as financial,
        ui.name as financialName,
        product.id as productId,
        p.create_by as createBy,
        p.create_at as createAt,
        ui2.name as createByName,
        p.approval_date as approvalDate1,
        p.preview_approval_date as approvalDate2,
        p.project_source as projectSource,
        p.is_objective_project as isObjectiveProject,
        p.project_level as projectLevel,
        p.approval_date as approvalDate
        from
        pam_ctc.project p
        left join pam_ctc.project_contract_rs pcr on
        pcr.project_id = p.id and pcr.deleted_flag = 0
        left join pam_ctc.contract contract on
        contract.id = pcr.contract_id
        left join pam_crm.customer c on
        p.customer_id = c.id
        left join pam_crm.business business on
        business.business_code = p.business_id
        left join pam_basedata.operating_unit ou on
        ou.id = p.ou_id
        left join pam_basedata.project_product_maintenance product on
        product.id = p.product_id
        left join pam_ctc.project_type pt on
        pt.id = p.`type`
        left join pam_basedata.unit unit on
        unit.id = p.unit_id
        left join pam_basedata.ltc_user_info ui on
        ui.id = p.financial
        left join pam_basedata.ltc_user_info ui2 on
        ui2.id = p.create_by
        where
        p.deleted_flag = 0

        <if test="projectNameOrCode != null and projectNameOrCode != ''">
            and (p.code like concat('%', #{projectNameOrCode}, '%') or p.name like concat('%', #{projectNameOrCode}, '%'))
        </if>
        <if test="managerId != null and managerId != ''">
            and p.manager_id = #{managerId}
        </if>
        <if test="managerName != null and managerName != ''">
            and p.manager_name like concat('%', #{managerName}, '%')
        </if>

        <if test="financialName != null and financialName != ''">
            and ui.name like concat('%', #{financialName}, '%')
        </if>

        <if test="financial != null and financial != ''">
            and p.financial = #{financial}
        </if>
            and p.status in (4,9)

        <!-- 我的项目 数据权限 -->
        <if test="userId != null and userId != ''">
            and (p.id in (
            select
            pm.project_id
            from
            pam_ctc.project_member pm
            where
            pm.user_id = #{userId}
            and pm.deleted_flag = 0)
            or p.manager_id = #{userId}
            or p.create_by = #{userId}
            or p.financial = #{userId} )
        </if>

        <if test="unitIds != null">
            and p.unit_id in
            <foreach collection="unitIds" item="unitId" index="index" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>

        order by p.create_at desc
    </select>

    <select id="getProjectMaterialAmounts" resultType="com.midea.pam.common.ctc.dto.ProjectDto" parameterType="list">
        select
        project_id as id,
        truncate(sum(ifnull(price_total, 0)), 2) as materialBudgetAmount
        from pam_ctc.project_budget_material
        where deleted_flag = 0
        and parent_id is null
        and project_id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by project_id
    </select>

    <select id="getProjectHumanAmounts" resultType="com.midea.pam.common.ctc.dto.ProjectDto" parameterType="list">
        select
        project_id as id,
        truncate(sum(ifnull(price, 0) * ifnull(number, 0) * ifnull(days, 0)), 2) as humanBudgetAmount
        from pam_ctc.project_budget_human
        where deleted_flag = 0
        and project_id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by project_id
    </select>

    <select id="getProjectTravelAmounts" resultType="com.midea.pam.common.ctc.dto.ProjectDto" parameterType="list">
        select
        project_id as id,
        truncate(sum(ifnull(come_back_traffic, 0) + ifnull(other, 0) + (ifnull(traffic, 0)+ifnull(hotel, 0)+ifnull(subsidy, 0))*ifnull(number, 0)), 2) as travelBudgetAmount
        from pam_ctc.project_budget_travel
        where deleted_flag = 0
        and project_id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by project_id
    </select>

    <select id="getProjectFeeAmounts" resultType="com.midea.pam.common.ctc.dto.ProjectDto" parameterType="list">
        select
        project_id as id,
        truncate(sum(ifnull(amount, 0)), 2) as feeBudgetAmount
        from pam_ctc.project_budget_fee
        where deleted_flag = 0
        and project_id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by project_id
    </select>

    <select id="listProjectMilepostsByProjectIds" resultType="com.midea.pam.common.ctc.vo.ProjectMilepostExcelVO" parameterType="list">
        select
        p.name as projectName,
        p.code as projectCode,
        pm.order_num as orderNum,
        pm.name as name,
        pm.status as status,
        case pm.`status`
            when -2 then '草稿'
            when 0 then '进行中'
            when 1 then '评审中'
            when 2 then '评审通过'
            when 3 then '驳回'
            when 4 then '废弃'
            when 5 then '模组确认中'
            when 6 then '模组确认通过'
            when 7 then '模组确认驳回'
            when 8 then '评审通过'
            when 9 then '模组确认驳回'
            when 10 then '撤回'
            when 26 then '终止'
            else ''
        end as statusName,
        pm.base_start_time as baseStartTime,
        pm.base_end_time as baseEndTime,
        pm.start_time as startTime,
        pm.end_time as endTime,
        pm.actual_start_time as actualStartTime,
        pm.actual_end_time as actualEndTime,
        pm.annex,
        pm.annex_type as annexType,
        pm.responsible as responsible,
        ui.name as responsibleName,
        pr.name as notice,
        pm.help_flag as helpFlag
        from pam_ctc.project_milepost pm
        inner join pam_ctc.project p on pm.project_id = p.id
        left join pam_ctc.project_role pr on pr.id = pm.notice
        left join pam_basedata.ltc_user_info ui on ui.id = pm.responsible
        where
        pm.deleted_flag = 0
        and p.deleted_flag = 0
        and p.id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        order by p.create_at desc, pm.help_flag asc, pm.order_num asc
    </select>

    <select id="listProjectMembersByProjectIds" resultType="com.midea.pam.common.ctc.vo.ProjectMemberExcelVO" parameterType="list">
        select
        p.name as projectName,
        p.code as projectCode,
        pm.user_id as userId,
        pm.profit_contribution_rate as profitContributionRate,
        pm.user_name as userName,
        ui.name as name,
        ui.type as userType,
        pm.telphone as telphone,
        pr.name as projectMemberRoleName,
        pm.start_date as startDate,
        pm.end_date as endDate
        from pam_ctc.project_member pm
        inner join pam_ctc.project p on pm.project_id = p.id
        left join pam_ctc.project_role pr on pr.id = pm.project_member_role
        left join pam_basedata.ltc_user_info ui on ui.id = pm.user_id
        where pm.deleted_flag = 0
        and p.deleted_flag = 0
        and p.id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        order by p.create_at desc
    </select>

    <select id="getDelayAndExpireProjects" resultType="com.midea.pam.common.ctc.dto.ProjectDto">
        SELECT
            p.id,
            p.code,
            p.name,
            p.status,
            p.manager_id as managerId,
            p.manager_name as managerName,
            p.approval_date as approvalDate
        FROM
            pam_ctc.project p inner join pam_basedata.unit u on p.unit_id = u.id
        WHERE p.preview_flag = 1
          and p.deleted_flag = 0
          and p.manager_id = #{userId}
          AND p.`approval_date` <![CDATA[ <= ]]> DATE_SUB(CURDATE(),INTERVAL 23 DAY)
          and p.status in (4,-3)
          and u.parent_id = #{roleDept}
    </select>

    <select id="countRdmProject" resultType="java.lang.Long">
        select
        count(p.id)
        from
        pam_ctc.project p
        where
        p.deleted_flag = 0
        <if test="type != null">
            and p.type = #{type}
        </if>
        <if test="ouIds != null">
            and p.ou_id in
            <foreach collection="ouIds" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>
        <if test="types != null">
            and p.type in
            <foreach collection="types" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="statuses != null">
            and p.status in
            <foreach collection="statuses" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="unitIds != null">
            and p.unit_id in
            <foreach collection="unitIds" item="unitId" index="index" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>
        <!-- 我的项目 数据权限 -->
        <if test="me">
            and (p.id in (
            select
            pm.project_id
            from
            pam_ctc.project_member pm
            where
            pm.user_id = #{userId}
            and pm.deleted_flag = 0)
            or p.manager_id = #{userId}
            or p.create_by = #{userId}
            or p.financial = #{userId} )
        </if>
    </select>

    <select id="getRdmBudgetChangeProjects" resultType="com.midea.pam.common.ctc.dto.ProjectDto">
        SELECT
            p.id,
            p.code,
            p.name,
            p.status,
            p.manager_id as managerId,
            p.manager_name as managerName,
            p.approval_date as approvalDate
        FROM
            pam_ctc.project p
                inner join pam_basedata.unit u on p.unit_id = u.id
                inner join pam_ctc.project_type pt on p.type = pt.id
        WHERE p.deleted_flag = 0
          and p.manager_id = #{userId}
          and pt.name = '人力外包（RDM）'
          and p.status in (4,-3,9)
          and u.parent_id = #{roleDept}
          and p.resource_status in (1,2,3)
    </select>
    <select id="listProjectMemberConflict" resultType="com.midea.pam.common.ctc.dto.ProjectMemberConflictDto">
        select
            b.code projectCode,
            b.name projectName,
            b.manager_name projectManagerName,
            a.start_date startDate,
            a.end_date endDate,
            a.conflict
        from (
        select project_id, start_date, end_date,
            <choose>
                <when test="startDate != null and endDate != null">
                    case when (start_date between #{startDate} and #{endDate}) or
                              (end_date between #{startDate} and #{endDate}) or
                              (start_date &lt; #{startDate} and end_date &gt; #{endDate})
                    then '冲突' else '不冲突' end
                </when>
                <otherwise>'不冲突'</otherwise>
            </choose>
            as conflict
        from pam_ctc.project_member
        where deleted_flag = 0
        and user_name = #{userId}
        <if test="projectId != null"> and project_id != #{projectId}</if>
        ) a inner join (
            select p.id, p.code, p.name, p.manager_name
            from pam_ctc.project p
            left join pam_basedata.unit u
            on p.unit_id = u.id
            where p.status in (4, 7, 9, -3)
            and u.parent_id = #{parentId}
        ) b on a.project_id = b.id
    </select>

    <select id="listProjectProgressPredict" resultType="com.midea.pam.common.ctc.dto.ProjectProgressPredictDto" parameterType="list">
        select
        p.id as projectId,
        p.name AS projectName,
        p.code AS projectCode,
        p.create_at AS projectCreateAt,
        pp.Month AS month,
        pp.cumulative_progress_predict AS cumulativeProgressPredict,
        pp.remark AS remark,
        pp.create_by AS createBy,
        pp.create_at AS createAt,
        pp.update_by AS updateBy,
        pp.update_at AS updateAt,
        COALESCE(c.excluding_tax_amount, 0) AS excludingTaxAmount
        FROM pam_ctc.project_progress_predict pp
        LEFT JOIN pam_ctc.project p ON p.id = pp.project_id AND p.deleted_flag = 0
        LEFT JOIN pam_ctc.contract c ON c.id = p.contract_id AND c.deleted_flag = 0
        WHERE pp.deleted_flag = 0
        and p.id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        order by p.create_at desc ,pp.month
    </select>

    <select id="selectByProjectIncomeCostPlan" parameterType="com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan"
            resultType="com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan">
        select
        id,
        carryover_bill_id as carryoverBillId
        from pam_ctc.project_income_cost_plan p
        where p.project_id = #{projectId}
        and p.deleted_flag = #{deletedFlag}
        order by order_num asc
    </select>


    <select id="selectCarryoverBillInfo" parameterType="com.midea.pam.common.ctc.entity.CarryoverBill" resultType="com.midea.pam.common.ctc.entity.CarryoverBill">
        select
        id,
        period_name as periodName,
        current_income_percent as currentIncomePercent,
        current_income_amount as currentIncomeAmount
        from pam_ctc.carryover_bill p
        where p.deleted_flag = 0
        and p.status = 1
        and p.reverse_status = 1
        and p.project_id = #{projectId}
    </select>

    <select id="selectById" parameterType="com.midea.pam.common.ctc.entity.Project" resultType="com.midea.pam.common.ctc.entity.Project">
        select
        id,
        amount
        from pam_ctc.project p
        where p.deleted_flag = 0
        and p.id = #{id}
    </select>
</mapper>
