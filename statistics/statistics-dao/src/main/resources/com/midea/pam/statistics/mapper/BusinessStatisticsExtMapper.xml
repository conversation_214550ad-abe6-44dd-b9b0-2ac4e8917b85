<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.BusinessStatisticsExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.crm.entity.Business">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="state" jdbcType="BIGINT" property="state" />
        <result column="level" jdbcType="INTEGER" property="level" />
        <result column="win_rate" jdbcType="DECIMAL" property="winRate" />
        <result column="budget" jdbcType="DECIMAL" property="budget" />
        <result column="intention" jdbcType="VARCHAR" property="intention" />
        <result column="source" jdbcType="BIGINT" property="source" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="follow_status" jdbcType="TINYINT" property="followStatus" />
        <result column="owner_id" jdbcType="VARCHAR" property="ownerId" />
        <result column="owner_user_id" jdbcType="BIGINT" property="ownerUserId" />
        <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
        <result column="plan_designer_id" jdbcType="BIGINT" property="planDesignerId" />
        <result column="plan_designer_name" jdbcType="VARCHAR" property="planDesignerName" />
        <result column="team_id" jdbcType="BIGINT" property="teamId" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
        <result column="lead_id" jdbcType="BIGINT" property="leadId" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="target_win_date" jdbcType="TIMESTAMP" property="targetWinDate" />
        <result column="approval_date" jdbcType="TIMESTAMP" property="approvalDate" />
        <result column="follow_time" jdbcType="TIMESTAMP" property="followTime" />
        <result column="has_delete" jdbcType="TINYINT" property="hasDelete" />
        <result column="top" jdbcType="TINYINT" property="top" />
        <result column="win_amount" jdbcType="DECIMAL" property="winAmount" />
        <result column="quotation_budget" jdbcType="DECIMAL" property="quotationBudget" />
        <result column="profit_percent_without_tax" jdbcType="DECIMAL" property="profitPercentWithoutTax" />
        <result column="product_intention" jdbcType="VARCHAR" property="productIntention" />
        <result column="product_intention_name" jdbcType="VARCHAR" property="productIntentionName" />
        <result column="or_not_simple" jdbcType="TINYINT" property="orNotSimple" />
        <result column="saas_business_flag" jdbcType="TINYINT" property="saasBusinessFlag" />
        <result column="application_industry" jdbcType="VARCHAR" property="applicationIndustry" />
        <result column="estimate_project_cost" jdbcType="DECIMAL" property="estimateProjectCost" />
        <result column="estimate_project_currency" jdbcType="VARCHAR" property="estimateProjectCurrency" />
        <result column="before_project_decision" jdbcType="VARCHAR" property="beforeProjectDecision" />
        <result column="now_project_decision" jdbcType="VARCHAR" property="nowProjectDecision" />
        <result column="payment_number" jdbcType="VARCHAR" property="paymentNumber" />
        <result column="special_business_treaty" jdbcType="VARCHAR" property="specialBusinessTreaty" />
        <result column="mass_production" jdbcType="TINYINT" property="massProduction" />
        <result column="estimate_bid_at" jdbcType="TIMESTAMP" property="estimateBidAt" />
        <result column="estimate_delivery_at" jdbcType="TIMESTAMP" property="estimateDeliveryAt" />
        <result column="estimate_check_at" jdbcType="TIMESTAMP" property="estimateCheckAt" />
        <result column="capital_source" jdbcType="VARCHAR" property="capitalSource" />
        <result column="grade_explain" jdbcType="VARCHAR" property="gradeExplain" />
        <result column="technology_describe" jdbcType="VARCHAR" property="technologyDescribe" />
        <result column="project_implementation_background" jdbcType="VARCHAR" property="projectImplementationBackground" />
        <result column="customer_focus" jdbcType="VARCHAR" property="customerFocus" />
        <result column="capital_situation" jdbcType="VARCHAR" property="capitalSituation" />
        <result column="business_level" jdbcType="VARCHAR" property="businessLevel" />
        <result column="policy_share" jdbcType="INTEGER" property="policyShare" />
        <result column="support_share" jdbcType="INTEGER" property="supportShare" />
        <result column="profit_center" jdbcType="VARCHAR" property="profitCenter" />
        <result column="product_center" jdbcType="VARCHAR" property="productCenter" />
        <result column="assessment_form" jdbcType="VARCHAR" property="assessmentForm" />
        <result column="ems_status" jdbcType="TINYINT" property="emsStatus" />
        <result column="ems_messgae" jdbcType="VARCHAR" property="emsMessgae" />
        <result column="operating_unit_id" jdbcType="BIGINT" property="operatingUnitId" />
        <result column="gems_status" jdbcType="INTEGER" property="gemsStatus" />
        <result column="agent_c" jdbcType="VARCHAR" property="agentC" />
        <result column="agent_b" jdbcType="VARCHAR" property="agentB" />
        <result column="agent_a" jdbcType="VARCHAR" property="agentA" />
        <result column="station" jdbcType="VARCHAR" property="station" />
        <result column="amount" jdbcType="DECIMAL" property="amount" />
        <result column="history_amount" jdbcType="DECIMAL" property="historyAmount" />
        <result column="business_type" jdbcType="VARCHAR" property="businessType" />
        <result column="project_location" jdbcType="VARCHAR" property="projectLocation" />
        <result column="plan_begin_node" jdbcType="TIMESTAMP" property="planBeginNode" />
        <result column="expected_win_time" jdbcType="TIMESTAMP" property="expectedWinTime" />
        <result column="expected_production_time" jdbcType="TIMESTAMP" property="expectedProductionTime" />
    </resultMap>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select *
        from pam_crm.business
        where id = #{id,jdbcType=BIGINT} and has_delete=0
    </select>

    <select id="getByBusinessId" resultType="com.midea.pam.common.crm.dto.BusinessProductDto">
        select * from pam_crm.business_product
         where  business_id = #{businessId} and has_remove=0
    </select>

    <select id="getBusinessCompetitionList" resultType="com.midea.pam.common.crm.entity.BusinessCompetition">
        select
            product_id as productId,
            business_id as businessId
        from pam_crm.business_competition
    </select>

    <select id="getCompetitionList" resultType="com.midea.pam.common.crm.entity.Competition">
        select
            id,
            name,
            company
        from pam_crm.competition
    </select>

    <select id="list" resultType="com.midea.pam.common.crm.dto.BusinessDto">
        select
            b.id as id,
            b.business_code as businessCode,
            b.name as name,
            b.status as status,
            b.follow_status as followStatus,
            c.name as customerName,
            c.crm_code as crmCode,
            cur.status as customerStatus,
            cur.parent_unit_id as parenetUnitId,
            case cur.status when -1 then '作废' when 0 then '未生效' when 1 then '生效' when 2 then '驳回' when 3 then '已失效' when 4 then '已冻结' when 5 then '审批中' when 6 then '生效已同步' when 7 then '撤回' else '' end as customerStatusName,
            cur.customer_group as customerGroup,
            b.customer_id as customerId,
            b.intention as intention,
            b.remark as remark,
            b.team_id as teamId,
            b.budget as budget,
            b.win_amount as winAmount,
            b.quotation_budget as quotationBudget,
            b.profit_percent_without_tax as profitPercentWithoutTax,
            b.win_rate as winRate,
            b.state as state,
            b.level as level,
            b.source as `source`,
            lui.name as createUserStr,
            b.create_at as createAt,
            b.update_at as updateAt,
            u.unit_name  as salesDepartmentName,
            u.id  as salesDepartmentId,
            u.parent_id as parentUnitId,
            b.top as isAllowedToTop,
            case b.top when 1 then '置顶' when 0 then '非置顶' else '' end as allowedName,
            b.owner_name as ownerName,
            b.owner_id as ownerId,
            b.follow_time as followTime,
            b.product_intention as productIntention,
            b.product_intention_name as productIntentionName,
            d.name as sourceName,
            d2.name as stateName,
            b.owner_user_id as ownerUserId,
            b.or_not_simple as orNotSimple,
            b.saas_business_flag as saasBusinessFlag,
            b.application_industry as applicationIndustry,
            b.estimate_project_cost as estimateProjectCost,
            b.before_project_decision as beforeProjectDecision,
            b.now_project_decision as nowProjectDecision,
            b.payment_number as paymentNumber,
            b.special_business_treaty as specialBusinessTreaty,
            b.mass_production as massProduction,
            b.estimate_bid_at as estimateBidAt,
            b.estimate_delivery_at as estimateDeliveryAt,
            b.estimate_check_at as estimateCheckAt,
            b.capital_source as capitalSource,
            b.grade_explain as gradeExplain,
            b.technology_describe as technologyDescribe,
            b.project_implementation_background as projectImplementationBackground,
            b.customer_focus as customerFocus,
            b.capital_situation as capitalSituation,
            b.agent_c as agentC,
            b.agent_b as agentB,
            b.agent_a as agentA,
            b.station as station,
            b.amount as amount,
            b.history_amount as historyAmount,
            b.business_type as businessType,
            b.project_location as projectLocation,
            b.plan_begin_node as planBeginNode,
            b.expected_win_time as expectedWinTime,
            b.expected_production_time as expectedProductionTime,
            ai.name as applicationIndustryName,
            case when br.id  is null then 0 else 1 end as replayStatus,
            CASE
            WHEN
            (SELECT
            COUNT(1)
            FROM
            pam_crm.business_risk_strategy rs
            WHERE rs.business_id = b.id
            AND rs.status = 4) > 0
            THEN 1
            ELSE 0
            end as riskStrategyStatus,
            if(b.status in (0,6) ,bfr2.close_class,null)  as closeClass,
            if(b.status in (0,6) ,bfr2.content,null)  as content,
            if(b.status in (0,6) ,bfr2.successful_bidder,null)  as successfulBidder,
            if(b.status in (0,6) ,bfr2.bidder_amount,null)  as bidderAmount
        from
            pam_crm.business b
            left join pam_crm.business_replay br on br.business_id=b.id and br.deleted_flag = 0
            left join pam_crm.business_risk_strategy brs on brs.business_id=b.id and brs.deleted_flag = 0
            LEFT JOIN pam_crm.customer c ON c.id = b.customer_id
            AND ( c.delete_flag IS NULL OR c.delete_flag = 0 )
            LEFT JOIN pam_basedata.ltc_user_info lui ON lui.id = b.create_by
            left join pam_crm.business_role_dept_rel r on r.business_id=b.id and r.type = 1 and r.has_delete = 0
            left join pam_basedata.unit u on r.role_dept_id = u.id
            LEFT JOIN pam_crm.customer_unit_rel cur ON cur.customer_id = c.id and cur.parent_unit_id = u.parent_id and cur.delete_flag = 0
            left join pam_basedata.ltc_dict d on d.`type` = 'crm_lead_source' and d.code = b.source
            left join pam_basedata.ltc_dict d2 on d2.id = b.state
            left join pam_ctc.application_industry ai on b.application_industry = ai.id and ai.enabled = 1
            left join (
                SELECT bfr.business_id, bfr.close_class, bfr.content, bfr.successful_bidder, bfr.bidder_amount
                    FROM pam_crm.business_follow_record bfr
                    INNER JOIN (
                    SELECT business_id, MAX(create_at) AS latest_record
                        FROM pam_crm.business_follow_record
                        WHERE title = '将商机弃单'
                        GROUP BY business_id
                    ) AS latest_records ON bfr.business_id = latest_records.business_id AND bfr.create_at = latest_records.latest_record
            ) bfr2 on b.id  = bfr2.business_id
        where
        b.has_delete = 0
        and !(IFNULL(b.or_not_simple, 0) = 1 and b.status = 5)
        <if test="businessDeptId != null and businessDeptId != ''">
            and b.id in (select business_id id from pam_crm.business_role_dept_rel where role_dept_id = #{businessDeptId} and type = 1 )
        </if>
        <if test="saleDeptId != null and saleDeptId != ''">
            and b.id in (select business_id id from pam_crm.business_role_dept_rel where role_dept_id = #{saleDeptId})
        </if>
        <if test="businessCode != null and businessCode != ''">
            and b.business_code like concat('%', #{businessCode}, '%')
        </if>
        <if test="name != null and name != ''">
            and b.name like concat('%', #{name}, '%')
        </if>

        <if test="customerName != null and customerName != ''">
            and c.name like concat('%', #{customerName}, '%')
        </if>

        <if test="crmCode != null and crmCode != ''">
            and c.crm_code like concat('%', #{crmCode}, '%')
        </if>

        <if test="ownerName != null and ownerName != ''">
            and b.owner_name like concat('%', #{ownerName}, '%')
        </if>

        <if test="projectLocation != null and projectLocation != ''">
            and b.project_location like concat('%', #{projectLocation}, '%')
        </if>

        <if test="createAtStart != null and createAtStart != ''">
            and b.create_at &gt;= #{createAtStart}
        </if>

        <if test="createAtEnd != null and createAtEnd != ''">
            and b.create_at &lt;= #{createAtEnd}
        </if>

        <if test="followTimeStart != null and followTimeStart != ''">
            and b.follow_time &gt;= #{followTimeStart}
        </if>

        <if test="followTimeEnd != null and followTimeEnd != ''">
            and b.follow_time &lt;= #{followTimeEnd}
        </if>

        <if test="customerGroups!=null">
            and cur.customer_group in
            <foreach collection="customerGroups" item="customerGroup" open="(" separator="," close=")">
                #{customerGroup}
            </foreach>
        </if>

        <if test="closeClassList != null and closeClassList.size > 0 ">
            and bfr2.close_class in
            <foreach collection="closeClassList" item="closeClass" index="index" open="(" separator="," close=")">
                #{closeClass}
            </foreach>
            and b.status in (0,6)
        </if>

        <if test="statuses != null">
            and b.status in
            <foreach collection="statuses" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <if test="saasBusinessFlag != null">
            and b.saas_business_flag = #{saasBusinessFlag}
        </if>

        <if test="states != null">
            and b.state in
            <foreach collection="states" item="state" index="index" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
        <if test="followStatuses != null">
            and
            <foreach collection="followStatuses" item="followStatus" index="index" open="(" separator=" or " close=")">
                <choose>
                    <when test="followStatus == 'null'">
                        b.follow_status is null
                    </when>
                    <otherwise>
                        b.follow_status = #{followStatus}
                    </otherwise>
                </choose>
            </foreach>
        </if>
        <if test="levels != null">
            and b.level in
            <foreach collection="levels" item="level" index="index" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>

        <if test="sources != null">
            and b.source in
            <foreach collection="sources" item="source" index="index" open="(" separator="," close=")">
                #{source}
            </foreach>
        </if>

        <if test="unitIds != null">
            and u.id in
            <foreach collection="unitIds" item="unitId" index="index" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>
        <if test="roleDepts != null">
            and r.role_dept_id in
            <foreach collection="roleDepts" item="roleDept" index="index" open="(" separator="," close=")">
                #{roleDept}
            </foreach>
        </if>
        <if test="businessTypes != null and businessTypes.size > 0 ">
            and b.business_type in
            <foreach collection="businessTypes" item="businessType" index="index" open="(" separator="," close=")">
                #{businessType}
            </foreach>
        </if>

        <if test="riskStrategyStatusList != null">
            <foreach collection="riskStrategyStatusList" item="status" index="index" open="" separator="," close="">
                <if test="status == 1">
                    and brs.id is not null and brs.status =4
                </if>
                <if test="status == 0">
                    and (brs.id is null or brs.status !=4)
                </if>
            </foreach>
        </if>

        <if test="replayStatusList != null">
            <foreach collection="replayStatusList" item="status" index="index" open="" separator="," close="">
                <if test="status == 1">
                    and br.id is not null
                </if>
                <if test="status == 0">
                    and br.id is null
                </if>
            </foreach>
        </if>

        <if test="saleDepts != null">
            and (r.role_dept_id is null or r.role_dept_id in
            <foreach collection="saleDepts" item="saleDept" index="index" open="(" separator="," close=")">
                #{saleDept}
            </foreach>
            )
        </if>

        <if test="createBy != null">
            and b.create_by = #{createBy}
        </if>

        <!-- 我的项目 数据权限 -->

        <if test="me">
            and (
            b.owner_user_id = #{userId}
            or b.create_by = #{userId}
            or b.team_id in ( select u.team_id from pam_crm.team_user u where u.user_id = #{userId})
            )
        </if>

        <if test="authors != null">
            and r.role_dept_id in
            <foreach collection="authors" item="author" index="index" open="(" separator="," close=")">
                #{author}
            </foreach>
        </if>

        <if test="approvalDateBegin != null">
            and ifnull(b.approval_date, b.create_at) &gt;= #{approvalDateBegin}
        </if>

        <if test="approvalDateEnd != null">
            and ifnull(b.approval_date, b.create_at) &lt;= #{approvalDateEnd}
        </if>

        <if test="amountMin != null">
            and b.amount &gt;= #{amountMin}
        </if>

        <if test="amountMax != null">
            and b.amount &lt;= #{amountMax}
        </if>

        <if test="planBeginNodeStart != null and planBeginNodeStart != ''">
            and b.plan_begin_node &gt;= #{planBeginNodeStart}
        </if>

        <if test="planBeginNodeEnd != null and planBeginNodeEnd != ''">
            and b.plan_begin_node &lt;= #{planBeginNodeEnd}
        </if>

        <if test="estimateBidAtStart != null and estimateBidAtStart != ''">
            and b.estimate_bid_at &gt;= #{estimateBidAtStart}
        </if>

        <if test="estimateBidAtEnd != null and estimateBidAtEnd != ''">
            and b.estimate_bid_at &lt;= #{estimateBidAtEnd}
        </if>

        <if test="estimateDeliveryAtStart != null and estimateDeliveryAtStart != ''">
            and b.estimate_delivery_at &gt;= #{estimateDeliveryAtStart}
        </if>

        <if test="estimateDeliveryAtEnd != null and estimateDeliveryAtEnd != ''">
            and b.estimate_delivery_at &lt;= #{estimateDeliveryAtEnd}
        </if>

        <if test="expectedProductionTimeStart != null and expectedProductionTimeStart != ''">
            and b.expected_production_time &gt;= #{expectedProductionTimeStart}
        </if>

        <if test="expectedProductionTimeEnd != null and expectedProductionTimeEnd != ''">
            and b.expected_production_time &lt;= #{expectedProductionTimeEnd}
        </if>

        group by b.id
        order by b.create_at desc

    </select>


    <select id="listBusinessFollowRecordByBusinessIds" resultType="com.midea.pam.common.ctc.vo.BusinessFollowRecordExcelVo" parameterType="list">
        select
        b.name as name,
        b.business_code as businessCode,
        r.create_user_name as createUserName,
        r.create_at as createAt,
        if(isnull(r.close_class),r.content, CONCAT('关闭原因分类：', r.close_class,'\n', r.content)) as content
        from pam_crm.business_follow_record r
        inner join pam_crm.business b on b.id = r.business_id and b.has_delete=0
        where
        r.business_id in
        <foreach collection="businessIds" item="businessId" index="index" open="(" separator="," close=")">
            #{businessId}
        </foreach>
        order by b.create_at desc
    </select>

    <select id="listBusinessContactByBusinessIds" resultType="com.midea.pam.common.ctc.vo.BusinessContactExcelVo" parameterType="list">
        select
        b.name as businessName,
        b.business_code as businessCode,
        c.name as name,
        b.customer_id,
        c.position as position,
        c.sex as sex,
        case c.sex when 1 then '男' when 2 then '女' else '' end as sexName,
        c.cellphone as cellphone,
        c.email as email,
        c.wechat as wechat,
        c.qq as qq,
        c.level as level,
        c.decision_ratio as decisionRatio,
        c.support_ratio as supportRatio
        from pam_crm.contact c
        left join pam_crm.business_contact_rel r on c.id=r.contact_id
        left join pam_crm.business b on b.id=r.business_id and b.has_delete=0
        where c.id in (SELECT r.contact_id FROM pam_crm.business_contact_rel r
        where
        r.business_id in
        <foreach collection="businessIds" item="businessId" index="index" open="(" separator="," close=")">
            #{businessId}
        </foreach>)
        and c.has_delete=0
        order by b.create_at desc
    </select>


    <select id="listTeamByTeamIds" resultType="com.midea.pam.common.ctc.vo.BusinessTeamExcelVo" parameterType="list">
        select
        b.name as name,
        b.customer_id,
        b.business_code as businessCode,
        u.user_id as userId,
        u.user_name as userName,
        u.is_leader as roleName,
        cc.position as position
        from pam_crm.team_user u
        left join pam_crm.business b on b.team_id = u.team_id and b.has_delete=0
        left join pam_crm.role r on u.role_id = r.id
        left join pam_crm.business_contact_rel bcr on b.id=bcr.business_id
        left join pam_crm.contact cc on cc.id=bcr.contact_id and cc.has_delete=0
        where
        u.team_id in
        <foreach collection="teamIds" item="teamId" index="index" open="(" separator="," close=")">
            #{teamId}
        </foreach>
        and u.biz_unit_id = #{operatingUnitId}
        order by b.create_at desc
    </select>

    <select id="listByleadId" resultType="com.midea.pam.common.crm.entity.Business">
        select * from pam_crm.business
        where lead_id = #{leadId} and has_delete=0
    </select>

    <select id="selectIds" resultType="java.lang.Long" parameterType="list">

        select brdr.business_id id from pam_crm.business_role_dept_rel brdr where 1=1
        <if test="authorStrUnitIds!=null">
            and brdr.role_dept_id in
            <foreach collection="authorStrUnitIds" item="authorStrUnitId" index="index" open="(" separator="," close=")">
                #{authorStrUnitId}
            </foreach>
        </if>

    </select>

    <select id = "filterBusinessIsViews" resultType="long">
        select c.id
        from pam_crm.business c
        where (c.create_at = #{userId} or c.owner_user_id = #{userId}) and c.has_delete=0
        and id in
        <foreach collection="businessIds" item="businessId" index="index" open="(" separator="," close=")">
            #{businessId}
        </foreach>
        union
        select business_id
        from pam_crm.business_role_dept_rel
        where has_delete=0
        and role_dept_id in
        <foreach collection="secondUnitIds" item="secondUnitId" index="index" open="(" separator="," close=")">
            #{secondUnitId}
        </foreach>
        and business_id in
        <foreach collection="businessIds" item="businessId" index="index" open="(" separator="," close=")">
            #{businessId}
        </foreach>
        group by business_id
        union
        select c.id from pam_crm.team_user tu, pam_crm.business c, pam_crm.team t
        where t.id = c.team_id and t.id = tu.team_id and tu.is_leader = 1
        and (tu.user_id = #{userId} or tu.user_id = #{userMip})
        and c.id in
        <foreach collection="businessIds" item="businessId" index="index" open="(" separator="," close=")">
            #{businessId}
        </foreach>

    </select>

    <select id="getFollowerBusiness" resultType="com.midea.pam.common.crm.dto.BusinessDto">
        select distinct b.*
        from pam_crm.business b left join pam_crm.business_role_dept_rel r on r.business_id = b.id and r.type = 1
        where b.has_delete = 0
          and b.owner_user_id = #{userId}
          and b.follow_status = 0
          and b.status = 1
          and r.has_delete = 0
          and r.role_dept_id in
              (select id from pam_basedata.unit where parent_id = #{roleDept} and delete_flag = 0)
    </select>

    <select id="needAnalyseLevel" resultType="com.midea.pam.common.basedata.entity.FormConfig">
        SELECT
          fc.config_value AS configValue
        FROM
          pam_basedata.form_config fc
        WHERE fc.unit_id = #{unitId}
          AND fc.module = #{module}
          AND fc.code = #{code}
    </select>
    <select id="bussinessDoRemind" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM
        pam_crm.business b
        LEFT JOIN pam_crm.business_replay br
        ON b.id = br.business_id
        AND br.deleted_flag = 0
        left join pam_crm.business_role_dept_rel r
        on r.business_id=b.id and r.type = 1
        WHERE b.has_delete = 0
        and !(IFNULL(b.or_not_simple, 0) = 1 and b.status = 5)
        AND b.status IN (0, 3)
        AND br.id IS NULL
        and (
        b.owner_user_id = #{userId}
        or b.create_by = #{userId}
        or b.team_id in ( select u.team_id from pam_crm.team_user u where u.user_id = #{userId})
        )
        AND b.level in
        <foreach collection="levelList" item="level" index="index" open="(" separator="," close=")">
            #{level}
        </foreach>
        and r.role_dept_id in
        <foreach collection="authors" item="author" index="index" open="(" separator="," close=")">
            #{author}
        </foreach>
    </select>

    <select id="getUnitIdByBusinessId" resultType="java.lang.Long">
        select parent_id from pam_basedata.unit
        where id in (
            select role_dept_id
            from pam_crm.business_role_dept_rel
            where business_id = #{businessId}
        ) limit 1
    </select>

    <select id="listBaseBusinessInfo" resultType="com.midea.pam.common.crm.dto.BusinessDto">
        select distinct
        b.id,
        b.name,
        b.business_code businessCode,
        b.customer_id customerId,
        b.customer_name customerName,
        p.designer_id planDesignerId,
        p.designer_name planDesignerName,
        p.sales_manager_id salesManagerId,
        p.sales_manager_name salesManagerName,
        b.application_industry applicationIndustry,
        b.product_center productCenter,
        b.profit_center profitCenter,
        b.level
        from pam_crm.business b
        left join (select * from pam_crm.plan where status = 3) p on b.id = p.business_id
        where b.status in (3) and has_delete = 0
        and b.id in (
        select business_id
        from pam_crm.business_role_dept_rel
        where has_delete = 0
        and role_dept_id in (
        select id
        from pam_basedata.unit
        where parent_id = #{unitId})
        )
        <if test="fuzzyLike != null and fuzzyLike != ''">
            and (b.name like concat('%', #{fuzzyLike}, '%') or b.customer_name like concat('%', #{fuzzyLike}, '%') or
            b.business_code like concat('%', #{fuzzyLike}, '%'))
        </if>
        order by b.create_at desc
    </select>

    <select id="winlossOrderSelect" resultType="com.midea.pam.common.crm.dto.WinLossOrderSelectListDto">
        SELECT
        b.id AS businessId,
        b.status as status,
        latest_bfr.create_at AS winCreateAt,
        b.business_code AS businessCode,
        u.unit_name AS unitName,
        cur.customer_group AS customerGroup,
        b.name AS name,
        b.win_amount AS winAmount,
        b.amount as amount,
        b.estimate_delivery_at AS estimateDeliveryAt,
        b.project_location as projectLocation,
        b.owner_name AS ownerName,
        brdr.role_dept_id AS roleDeptId,
        latest_bfr.close_class as closeClass,
        latest_bfr.successful_bidder as successfulBidder,
        latest_bfr.bidder_amount as bidderAmount,
        ocd.value as salesDepartmentMip,
        ocd2.value as salesDepartmentTopMip
        FROM pam_crm.business b
        LEFT JOIN pam_crm.business_role_dept_rel brdr ON
        b.id = brdr.business_id
        AND brdr.type = 1
        LEFT JOIN pam_basedata.unit u ON
        brdr.role_dept_id = u.id
        LEFT JOIN (
        SELECT
        customer_id,
        parent_unit_id,
        customer_group,
        ROW_NUMBER() OVER (PARTITION BY customer_id, parent_unit_id ORDER BY customer_id) as rn
        FROM pam_crm.customer_unit_rel
        ) cur ON u.parent_id = cur.parent_unit_id
        AND b.customer_id = cur.customer_id
        AND cur.rn = 1
        LEFT JOIN pam_ctc.organization_custom_dict ocd on
        ocd.org_id = brdr.role_dept_id
        and ocd.name = #{salesOrganizationName}
        LEFT JOIN pam_ctc.organization_custom_dict ocd2 on
        u.parent_id = ocd2.org_id
        and ocd2.name = #{salesOrganizationTopName}
        LEFT JOIN (
        SELECT
        bfr1.business_id,
        bfr1.create_at,
        bfr1.close_class,
        bfr1.successful_bidder,
        bfr1.bidder_amount
        FROM pam_crm.business_follow_record bfr1
        INNER JOIN (
        SELECT
        business_id,
        MAX(create_at) AS max_create_at
        FROM pam_crm.business_follow_record
        WHERE create_at &gt;= #{winLossStartTime}
        AND create_at &lt;= #{winLossEndTime}
        GROUP BY business_id
        ) bfr2 ON bfr1.business_id = bfr2.business_id
        AND bfr1.create_at = bfr2.max_create_at
        ) latest_bfr ON b.id = latest_bfr.business_id
        WHERE
        b.status = #{status}
        AND b.operating_unit_id = 100281
        AND b.has_delete = 0
        AND latest_bfr.create_at IS NOT NULL
    </select>
</mapper>
