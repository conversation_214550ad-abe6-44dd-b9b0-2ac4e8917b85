<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ProjectCostExtMapper">

    <select id="getNeedCalculateRange" resultType="com.midea.pam.common.ctc.entity.Project">
        select distinct p.id, p.budget_cost as budgetCost, p.code, p.name, p.status as status, p.amount as amount
        from pam_ctc.project p
        where p.deleted_flag = 0
            and p.status in
            <foreach collection="statuses" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
            <if test="ouId != null">
                and p.ou_id = #{ouId}
            </if>
        union
        select distinct p.id, p.budget_cost as budgetCost, p.code, p.name, p.status as status, p.amount as amount
        from pam_ctc.project p
        left join
        (select project_id, max(create_at) as create_at from pam_statistics.project_cost_summary_record
        where 1 = 1
        group by project_id) r on p.id = r.project_id
        where p.deleted_flag = 0
            and p.status = 10
            <if test="ouId != null">
                and p.ou_id = #{ouId}
            </if>
            and (r.project_id is null or p.update_at > r.create_at)
    </select>

    <select id="getProjectCalculateRange" resultType="com.midea.pam.common.ctc.entity.Project">
        select distinct p.id, p.budget_cost as budgetCost, p.code, p.name, p.status as status, p.amount as amount
        from pam_ctc.project p
        where p.deleted_flag = 0
        <if test="ouId != null">
            and p.ou_id = #{ouId}
        </if>
        <if test="projectIdList != null and projectIdList.size() > 0">
            and p.id in
            <foreach collection="projectIdList" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
    </select>


    <select id="getCurrentFeeDetailCost" resultType="com.midea.pam.common.statistics.entity.ProjectCostFeeDetailRecord">
        select
        p.project_id as projectId,
        p.project_code as projectCode,
        p.project_name as projectName,
        epfd.apply_date as applyDate,
        epfd.apply_name as applyName,
        pbc.amount as feeAmount,
        pbc.currency as currencyName,
        pbc.vendor_name as vendorName,
        pbc.gl_date as glDate,
        epfd.import_erp_status as importErpStatus,
        epfd.is_cross_year as isCrossYear,
        epfd.apportion_type as apportionType,
        pbc.order_code as orderCode,
        pfc.fee_item_id as feeItemId,
        pfc.fee_item_name as feeItemName,
        pfc.fee_type_id as feeTypeId,
        pfc.fee_type_name as feeTypeName,
        case when fi.fee_flag = 2 then 1 else 2 end as type
        from pam_statistics.project_cost_summary_record p
        inner join pam_ctc.ems_pam_fee_detail epfd on p.project_code = epfd.bussiness_type_code and p.execute_id = #{executeId}
        inner join pam_ctc.project_budget_cost pbc on pbc.attribute1 = epfd.order_id and pbc.deleted_flag = 0
        inner join pam_ctc.project_fee_collection pfc on pfc.ems_budget_id = epfd.budget_node_id
        inner join pam_basedata.fee_item fi on fi.id = pfc.fee_item_id

        union all

        select
        p.project_id as projectId,
        p.project_code as projectCode,
        p.project_name as projectName,
        epfd.apply_date as applyDate,
        epfd.apply_name as applyName,
        epfd.fee_amount as feeAmount,
        epfd.currency_name as currencyName,
        epfd.vendor_name as vendorName,
        epfd.gl_date as glDate,
        epfd.import_erp_status as importErpStatus,
        epfd.is_cross_year as isCrossYear,
        epfd.apportion_type as apportionType,
        epfd.order_code as orderCode,
        pfc.fee_item_id as feeItemId,
        pfc.fee_item_name as feeItemName,
        pfc.fee_type_id as feeTypeId,
        pfc.fee_type_name as feeTypeName,
        case when fi.fee_flag = 2 then 1 else 2 end as type
        from pam_statistics.project_cost_summary_record p
        inner join pam_ctc.project_fee_collection prc on
            prc.project_id = p.project_id
            and prc.deleted_flag = 0
        inner join pam_ctc.ems_pam_fee_detail epfd on p.project_code = epfd.bussiness_type_code and p.execute_id = #{executeId}
                 and epfd.budget_node_id = prc.ems_budget_id
                 and epfd.import_erp_status!='SUCCESS'
        inner join pam_ctc.project_fee_collection pfc on pfc.ems_budget_id = epfd.budget_node_id
        inner join pam_basedata.fee_item fi on fi.id = pfc.fee_item_id
    </select>

    <select id="getCurrentEaDetailCost" resultType="com.midea.pam.common.statistics.entity.ProjectCostEaDetailRecord">
        select
        p.project_id as projectId,
        p.project_code as projectCode,
        p.project_name as projectName,
        ebod.fee_apply_code as feeApplyCode,
        ebod.submited_time as submitedTime,
        ebod.apply_name as applyName,
        pfc.fee_item_name as feeItemName,
        ebod.fee_type_name as feeTypeName,
        ebod.approve_amount as approveAmount,
        ebod.overplus_ea_amount as overplusEaAmount,
        ebod.currency_code as currencyName, <!--要改成币种的名字-->
        ebod.sensitive_info as sensitiveInfo,
        ebod.order_status as orderStatus

        from pam_statistics.project_cost_summary_record p
        inner join pam_ctc.ems_budget_occupy_detail ebod on p.project_code = ebod.project_code and p.execute_id = #{executeId}
        inner join pam_ctc.project_fee_collection pfc on pfc.ems_budget_id = ebod.budget_node_id
        inner join pam_basedata.fee_item fi on fi.id = pfc.fee_item_id
        where ebod.overplus_ea_amount!=0
        and ebod.is_ndyt != "Y"
        <!--left  join pam_basedata.currency as cc on ebod.currency_code=cc.currency_code-->
        <!--todo: 过滤掉可用金额为0的记录-->

    </select>

    <select id="getCurrentRevenueOrderCost"
            resultType="com.midea.pam.common.statistics.dto.ProjectCostRevenueOrderRecordDto">
        select
        p.project_id as projectId,
        p.project_code as projectCode,
        p.project_name as projectName,
        rco.order_code as orderCode,
        rco.input_date as inputDate,
        rcod.milepost_id as milepostId,
        rcod.milepost_name as milepostName,
        ifnull(rcod.material_cost, 0) as materialCost,
        ifnull(rcod.material_outsource_cost, 0) as materialOutsourceCost,
        ifnull(rcod.inner_labor_cost, 0) as innerLaborCost,
        ifnull(rcod.outer_labor_cost, 0) as outerLaborCost,
        ifnull(rcod.fee_cost, 0) as feeCost,
        ifnull(rcod.other_cost, 0) as otherCost,
        ifnull(rcod.sub_total, 0) as subTotal,
        rcod.is_import as isImport
        from
        pam_statistics.project_cost_summary_record p
        inner join pam_ctc.revenue_cost_order rco on
        p.project_id = rco.project_id
        and rco.`status` = 3
        and rco.deleted_flag = 0
        and (rco.type is null or rco.type <![CDATA[ <> ]]> '自动成本补差')
        inner join pam_ctc.revenue_cost_order_detail rcod on
        rcod.order_id = rco.id
        and rcod.deleted_flag = 0
        where p.execute_id = #{executeId}
    </select>

    <select id="getCurrentFeeItemCost" resultType="com.midea.pam.common.statistics.entity.ProjectCostFeeItemRecord">
        select
            p.project_id as projectId,
            p.project_code as projectCode,
            p.project_name as projectName,
            <!-- AAIG023661：因预算误删，导致页面数据行丢失，应产品要求兼容，修改点：delete flag的数据预算替换成零 -->
            SUM(if(bf.deleted_flag = 1 , 0 , ifnull(bf.amount, 0))) as budget,
            ifnull(t1.incurredCost, 0) as incurredCost,
            ifnull(t2.pendingCost, 0) as pendingCost,
            ifnull(t3.eaAvailableAmount, 0) as eaAvailableAmount,
            4 as `type`,
            fi.name as feeItemName,
            fi.id as feeItemId
        from pam_statistics.project_cost_summary_record p
        left join pam_ctc.project_budget_fee bf on
            p.project_id = bf.project_id
        left join pam_basedata.fee_item fi on
            fi.id = bf.fee_item_id and fi.deleted_flag = 0
        left join (
            select
            p.project_id as projectId,
            sum(ifnull(pbc.amount, 0)) as incurredCost,
            prc.fee_item_id as feeItemId
            from
            pam_statistics.project_cost_summary_record p
            inner join pam_ctc.project_fee_collection prc on
            prc.project_id = p.project_id
            and prc.deleted_flag = 0
            inner join pam_ctc.ems_pam_fee_detail fd on
            fd.budget_node_id = prc.ems_budget_id
            inner join pam_ctc.project_budget_cost pbc on
            fd.order_id = pbc.attribute1
            inner join pam_basedata.fee_item fi on fi.id = prc.fee_item_id
            where p.execute_id = #{executeId}
            group by p.project_id, prc.fee_item_id
        ) t1 on p.project_id = t1.projectId and fi.id = t1.feeItemId
        left join (
            select
            p.project_id as projectId,
            ROUND(sum(ifnull(fd.fee_amount, 0)*ifnull(fd.erp_exchange_rate,1)),2) as pendingCost,
            prc.fee_item_id as feeItemId
            from
            pam_statistics.project_cost_summary_record p
            inner join pam_ctc.project_fee_collection prc on
            prc.project_id = p.project_id
            and prc.deleted_flag = 0
            inner join pam_ctc.ems_pam_fee_detail fd on
            fd.budget_node_id = prc.ems_budget_id and fd.import_erp_status!='SUCCESS'
            inner join pam_basedata.fee_item fi on fi.id = prc.fee_item_id
            where p.execute_id = #{executeId}
            group by p.project_id, prc.fee_item_id
        ) t2 on p.project_id = t2.projectId and fi.id = t2.feeItemId
        left join (
            select
            p.project_id as projectId,
            sum(ifnull(od.overplus_ea_amount, 0)) as eaAvailableAmount,
            prc.fee_item_id as feeItemId
            from
            pam_statistics.project_cost_summary_record p
            inner join pam_ctc.project_fee_collection prc on
            prc.project_id = p.project_id
            and prc.deleted_flag = 0
            inner join pam_ctc.ems_budget_occupy_detail od on
            od.budget_node_id = prc.ems_budget_id and od.is_ndyt != "Y"
            inner join pam_basedata.fee_item fi on fi.id = prc.fee_item_id
            where p.execute_id = #{executeId}
            group by p.project_id, prc.fee_item_id
        ) t3 on p.project_id = t3.projectId and fi.id = t3.feeItemId
        where p.execute_id = #{executeId} and bf.project_id is not null
        group by p.project_id, fi.id
    </select>

    <select id="getTravelAndHumanItemCost" resultType="com.midea.pam.common.statistics.entity.ProjectCostFeeItemRecord">
        select
            p.project_id as projectId,
            p.project_code as projectCode,
            p.project_name as projectName,
            bf.budget,
            ifnull(t1.incurredCost, 0) as incurredCost,
            ifnull(t2.pendingCost, 0) as pendingCost,
            ifnull(t3.eaAvailableAmount, 0) as eaAvailableAmount,
            case
                when fi.fee_flag = 2 then 3
                else 2
            end as `type`,
            case
                when fi.fee_flag = 2 then fi.name
                else '外部员工'
            end as feeItemName,
            fi.id as feeItemId
        from
            pam_statistics.project_cost_summary_record p
        left join (
            <!-- AAIG023661：因预算误删，导致页面数据行丢失，应产品要求兼容，修改点：delete flag的数据预算替换成零 -->
            select
                pbh.project_id,
                1 as fee_flag,
                SUM(if (pbh.deleted_flag = 1 , 0 , IFNULL(pbh.days * pbh.price * pbh.`number`, 0))) as budget
            from
                pam_ctc.project_budget_human pbh
            where
                pbh.type = 2
            group by
                pbh.project_id
            union all
            select
                pbt.project_id,
                2 as fee_flag,
                SUM(if(pbt.deleted_flag = 1 , 0 ,(IFNULL(pbt.hotel, 0) + IFNULL(pbt.subsidy, 0) + IFNULL(pbt.traffic, 0)) * IFNULL(pbt.number, 0) + IFNULL(pbt.come_back_traffic, 0) + IFNULL(pbt.other, 0))) as budget
            from
                pam_ctc.project_budget_travel pbt
            group by
                pbt.project_id
        ) bf on p.project_id = bf.project_id
        left join (
            select
            p.id as project_id,
            fi.id,
            fi.name,
            fi.fee_flag
            from
            pam_ctc.project p
            left join pam_basedata.unit u on
            p.unit_id = u.id
            and u.delete_flag = 0
            left join pam_basedata.fee_item fi on
            fi.unit_id = u.parent_id
            and fi.deleted_flag = 0
            where
            fi.fee_flag in (1, 2)
            and u.parent_id is not null
            and p.deleted_flag = 0
            group by
            p.id,
            fi.fee_flag
        ) fi on fi.project_id = bf.project_id and fi.fee_flag = bf.fee_flag
        left join (
            select
            p.project_id as projectId,
            sum(ifnull(pbc.amount, 0)) as incurredCost,
            prc.fee_item_id as feeItemId
            from
            pam_statistics.project_cost_summary_record p
            inner join pam_ctc.project_fee_collection prc on
            prc.project_id = p.project_id
            and prc.deleted_flag = 0
            inner join pam_ctc.ems_pam_fee_detail fd on
            fd.budget_node_id = prc.ems_budget_id
            inner join pam_ctc.project_budget_cost pbc on
            fd.order_id = pbc.attribute1
            inner join pam_basedata.fee_item fi on fi.id = prc.fee_item_id
            where p.execute_id = #{executeId}
            group by p.project_id, prc.fee_item_id
        ) t1 on p.project_id = t1.projectId and fi.id = t1.feeItemId
        left join (
            select
            p.project_id as projectId,
            ROUND(sum(ifnull(fd.fee_amount, 0)*ifnull(fd.erp_exchange_rate,1)),2) as pendingCost,
            prc.fee_item_id as feeItemId
            from
            pam_statistics.project_cost_summary_record p
            inner join pam_ctc.project_fee_collection prc on
            prc.project_id = p.project_id
            and prc.deleted_flag = 0
            inner join pam_ctc.ems_pam_fee_detail fd on
            fd.budget_node_id = prc.ems_budget_id and fd.import_erp_status!='SUCCESS'
            inner join pam_basedata.fee_item fi on fi.id = prc.fee_item_id
            where p.execute_id = #{executeId}
            group by p.project_id, prc.fee_item_id
        ) t2 on p.project_id = t2.projectId and fi.id = t2.feeItemId
        left join (
            select
            p.project_id as projectId,
            sum(ifnull(od.overplus_ea_amount, 0)) as eaAvailableAmount,
            prc.fee_item_id as feeItemId
            from
            pam_statistics.project_cost_summary_record p
            inner join pam_ctc.project_fee_collection prc on
            prc.project_id = p.project_id
            and prc.deleted_flag = 0
            inner join pam_ctc.ems_budget_occupy_detail od on
            od.budget_node_id = prc.ems_budget_id and od.is_ndyt != "Y"
            inner join pam_basedata.fee_item fi on fi.id = prc.fee_item_id
            where p.execute_id = #{executeId}
            group by p.project_id, prc.fee_item_id
        ) t3 on p.project_id = t3.projectId and fi.id = t3.feeItemId
        where p.execute_id = #{executeId} and bf.project_id is not null
        group by p.project_id, fi.id
    </select>

    <select id="listProjectCostPurchaseOrderRecord"
            resultType="com.midea.pam.common.statistics.entity.ProjectCostPurchaseOrderRecord">
        select
            sr.project_id projectId,
            sr.project_code projectCode,
            sr.project_name projectName,
            po.num purchaseNum, -- 采购订单号
            po.vendor_name vendorName, -- 供应商名称
            po.vendor_num vendorCode, -- 供应商编码
            pod.materiel_descr materielDescr, -- 物料描述
            pod.erp_code erpCode,
            m.brand,
            m.model,
            m.name ,
            pod.order_num orderNum, -- 下单数量
            (select sum(case when pp.transaction_type = 'DELIVER' then pp.transaction_quantity when pp.transaction_type = 'RETURN TO VENDOR' then pp.transaction_quantity*(-1) end ) from pam_ctc.purchase_progress pp where pp.item_code= pod.erp_code
            and pp.po_number = po.num and pp.po_line_no = pod.line_number and pp.transaction_type in ('DELIVER','RETURN TO VENDOR')) storageCount, -- 已入库数量
            pod.cost * ifnull(po.conversion_rate, 1) purchaseUnitPrice -- 采购单价（CNY）
        from
            pam_statistics.project_cost_summary_record sr
            left join pam_ctc.project p on sr.project_id = p.id and  p.deleted_flag = 0
            left join pam_ctc.project_profit pp on pp.project_id = p.id and pp.deleted_flag = 0
            left join pam_ctc.purchase_order_detail pod on p.id = pod.project_id and pod.deleted_flag = 0
            inner join pam_ctc.purchase_order po on pod.purchase_order_id = po.id and po.order_status not in (2,3) and po.deleted_flag = 0
            left join pam_basedata.material m on m.item_code = pod.erp_code and m.delete_flag = 0 and m.organization_id = pp.storage_id
        where
            sr.execute_id = #{executeId} and pod.status in (1,2)
    </select>

    <select id="listProjectCostStorageRecord"
            resultType="com.midea.pam.common.statistics.entity.ProjectCostStorageRecord">
        select distinct sr.project_id as projectId,
        sr.project_code as projectCode,
        sr.project_name as projectName,
        st.segment1, -- ERP物料编码
        st.description, -- 物料描述
        m.brand,
        m.model,
        m.name,
        st.transaction_quantity as transactionQuantity, -- 库存现有量
        st.primary_unit_of_measure as primaryUnitOfMeasure, -- 单位
        if((select count(*) from pam_basedata.material_cost mc where mc.item_code = st.segment1 and mc.cost_type_id
        =1 and mc.deleted_flag = 0)>0,
        (select mc.item_cost from pam_basedata.material_cost mc where mc.item_code = st.segment1 and mc.cost_type_id =1
        and mc.deleted_flag = 0
        limit 0,1),
        (select mc.item_cost from pam_basedata.material_cost mc where mc.item_code = st.segment1 and mc.deleted_flag = 0
        and mc.cost_type_id is
        null limit 0,1) ) as itemCost, -- 物料成本
        if((select mc.cost_type_id from pam_basedata.material_cost mc where mc.item_code = st.segment1 and
        mc.deleted_flag = 0 limit 0,1)=1,
        '标准','估价' ) as costType, -- 成本类型
        st.subinventory_code subinventoryCode, -- 子库
        st.subinventory_description subinventoryDescription, -- 子库描述
        st.locator, -- 货位
        st.locator_description locatorDescription -- 货位描述
        from pam_basedata.storage st
        inner join pam_statistics.project_cost_summary_record sr on sr.project_code = substring_index(st.locator,'.',1)
        left join pam_basedata.material m on st.segment1 = m.item_code and st.organization_id = m.organization_id
        where st.deleted_flag = 0
        and sr.execute_id = #{executeId}
    </select>
    <select id="listProjectCostGetMaterialRecord"
            resultType="com.midea.pam.common.statistics.entity.ProjectCostGetreturnMaterialRecord">
        select distinct mgh.project_id projectId,
        mgh.project_code projectCode,
        mgh.project_name projectName,
        mgh.remark as headRemark,
        mgd.remark as detailRemark,
        mgd.source as `source`,
        mgd.transition_id as transitionId,
        mgd.material_code AS erpCode,
        m.item_info AS itemInfo,
        m.brand AS brand,
        m.model AS model,
        m.name AS name,
        mgd.apply_amount totalApplyAmount,
        IFNULL(mgd.actual_cost,0) as materialCost, -- 物料成本
        IFNULL(mgd.actual_amount,0) as totalActualAmount, -- 实际数量
        round(IFNULL(mgd.actual_cost,0) * IFNULL(mgd.actual_amount,0), 2) as getReturnAmount,
        mgd.trade_time getReturnTime,
        '领料' as orderType,
        mgh.get_code getReturnCode,
        (case
        when mgh.status=1 then '草稿'
        when mgh.status=2 then '待审批'
        when mgh.status=3 then '驳回'
        when mgh.status=4 then '待处理'
        when mgh.status=5 then '已处理'
        when mgh.status=6 then '废弃'
        else '未知'
        end
        ) as status,
        mgd.item_cost_is_null as itemCostIsNull,
        mgd.ticket_task_code as ticketTaskCode,
        mgd.id as materialDetailId
        from pam_ctc.material_get_header mgh
        inner join pam_statistics.project_cost_summary_record sc
              on mgh.project_id = sc.project_id and mgh.status in (2,4,5) and mgh.deleted_flag = 0
        left join pam_ctc.material_get_detail mgd on mgh.id = mgd.header_id and mgd.deleted_flag = 0
        left join pam_basedata.material m on m.organization_id = mgh.organization_id
        and m.item_code = mgd.material_code and delete_flag  = 0
        where sc.execute_id = #{executeId}
        and not exists ( select 1 from pam_ctc.project_wbs_budget pwb where pwb.project_id = sc.project_id and pwb.deleted_flag = 0)
        <if test="projectIdList != null and projectIdList.size>0">
            and mgh.project_id in
            <foreach collection="projectIdList" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
    </select>
    <select id="listProjectCostReturnMaterialRecord"
            resultType="com.midea.pam.common.statistics.entity.ProjectCostGetreturnMaterialRecord">
        select distinct mrh.project_id projectId,
        mrh.project_code projectCode,
        mrh.project_name projectName,
        mrh.remark as headRemark,
        mrd.remark as detailRemark,
        mrd.source as `source`,
        mrd.transition_id as transitionId,
        mrd.material_code AS erpCode, -- 物料编码
        m.item_info AS itemInfo, -- 物料描述
        m.brand AS brand, -- 品牌
        m.model AS model, -- 型号
        m.name AS name, -- 物料名称
        mrd.apply_amount totalApplyAmount, -- 实际申请数量
        IFNULL(mrd.actual_cost,0) as materialCost, -- 事务处理成本（单位成本）
        IFNULL(mrd.actual_amount,0) totalActualAmount, -- 实际退回数量
        round(IFNULL(mrd.actual_cost,0) * IFNULL(mrd.actual_amount,0), 2) as getReturnAmount,
        mrd.actual_return_at getReturnTime,
        '退料' as orderType,
        mrh.return_code getReturnCode,
        (case
        when mrh.status=1 then '草稿'
        when mrh.status=2 then '待审批'
        when mrh.status=3 then '驳回'
        when mrh.status=4 then '待处理'
        when mrh.status=5 then '已处理'
        when mrh.status=6 then '废弃'
        else '未知'
        end
        ) as status,
        mrd.item_cost_is_null as itemCostIsNull,
        mrd.id as materialDetailId,
        mRd.ticket_task_code as ticketTaskCode
        from pam_ctc.material_return_header mrh
        inner join pam_statistics.project_cost_summary_record sc
        on sc.execute_id = #{executeId} and mrh.project_id = sc.project_id and mrh.status in (2,4,5) and mrh.deleted_flag = 0
        left join pam_ctc.material_return_detail mrd on mrh.id = mrd.header_id and mrd.deleted_flag = 0
        left join pam_basedata.material m on m.organization_id = mrh.organization_id
        and m.item_code = mrd.material_code and delete_flag  = 0
        <if test="projectIdList != null and projectIdList.size>0">
            where mrh.project_id in
            <foreach collection="projectIdList" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
    </select>

    <select id="listProjectWbsCostGetMaterialRecord"
            resultType="com.midea.pam.common.statistics.entity.ProjectCostGetreturnMaterialRecord">
        select distinct mgh.project_id projectId,
        mgh.project_code projectCode,
        mgh.project_name projectName,
        mgh.remark as headRemark,
        mgd.remark as detailRemark,
        mgd.source as `source`,
        mgd.transition_id as transitionId,
        mgd.material_code AS erpCode,
        m.item_info AS itemInfo,
        m.brand AS brand,
        m.model AS model,
        m.name AS name,
        mgd.apply_amount totalApplyAmount,
        IFNULL(mgd.actual_cost,0) as materialCost, -- 物料成本
        IFNULL(mgd.actual_amount,0) as totalActualAmount, -- 实际数量
        round(IFNULL(mgd.actual_cost,0) * IFNULL(mgd.actual_amount,0), 2) as getReturnAmount,
        mgd.trade_time getReturnTime,
        '领料' as orderType,
        mgh.get_code getReturnCode,
        (case
        when mgh.status=1 then '草稿'
        when mgh.status=2 then '待审批'
        when mgh.status=3 then '驳回'
        when mgh.status=4 then '待处理'
        when mgh.status=5 then '已处理'
        when mgh.status=6 then '废弃'
        else '未知'
        end
        ) as status,
        mgd.item_cost_is_null as itemCostIsNull,
        mgd.ticket_task_code as ticketTaskCode,
        mgd.id as materialDetailId
        from pam_ctc.material_get_header mgh
        inner join pam_statistics.project_cost_summary_record sc
        on mgh.project_id = sc.project_id and mgh.status in (2,4,5) and mgh.deleted_flag = 0
        left join pam_ctc.material_get_detail mgd on mgh.id = mgd.header_id and mgd.deleted_flag = 0
        left join pam_basedata.material m on m.organization_id = mgh.organization_id
        and m.item_code = mgd.material_code and delete_flag  = 0
        where sc.execute_id = #{executeId}
        and exists ( select 1 from pam_ctc.project_wbs_budget pwb where pwb.project_id = sc.project_id and pwb.deleted_flag = 0)
        <if test="projectIdList != null and projectIdList.size>0">
            and mgh.project_id in
            <foreach collection="projectIdList" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
    </select>

    <select id="listProjectCostOutsourcePurchaseRecord"
            resultType="com.midea.pam.common.statistics.entity.ProjectCostOutsourcePurchaseRecord">
        select distinct sc.project_id as projectId,
        sc.project_code as projectCode,
        sc.project_name as projectName,
        c.code as contractCode,
        c.name as contractName,
        c.amount as contractAmount,
        c.excluding_tax_amount as excludingTaxAmount,
        round(punishment.amount * ifnull(c.conversion_rate, 1), 2) as localPunishmentAmount,
        c.currency as currency,
        ifnull(c.conversion_rate, 1) as conversionRate,
        t.currency as localCurrency,
        c.start_time as startTime,
        c.end_time as endTime,
        c.purchasing_follower_name as buyerName, -- 采购跟进人
        c.type_name typeName, -- 合同类型
        c.vendor_code as vendorCode, -- 供应商编号
        c.vendor_name as vendorName, -- 供应商名称
        c.vendor_site_code as vendorSiteCode, -- 供应商地点
        (case
        when c.status=1 then '草稿'
        when c.status=2 then '审核中'
        when c.status=3 then '驳回'
        when c.status=4 then '待生效'
        when c.status=5 then '生效'
        when c.status=6 then '失效'
        when c.status=7 then '已结束'
        when c.status=8 then '冻结'
        when c.status=9 then '作废'
        when c.status=10 then '变更中'
        else '未知'
        end
        ) as status -- 采购合同状态
        from pam_ctc.purchase_contract c
        inner join pam_statistics.project_cost_summary_record sc on c.project_id = sc.project_id
        left join (
            select operating_unit_id, currency
            from pam_basedata.organization_rel
            where pam_enabled = 0
            group by operating_unit_id
        ) t on t.operating_unit_id = c.ou_id
        left join (
            select
                purchase_contract_id ,
                sum(amount) as amount
            from
                pam_ctc.purchase_contract_punishment
            where
                status = 6
                and deleted_flag = 0
            group by
                purchase_contract_id
        ) punishment on punishment.purchase_contract_id = c.id
        where c.status in (4,5,8,2,10) and c.deleted_flag = 0
        and sc.execute_id = #{executeId}
    </select>
    <select id="listProjectCostDifferenceRecord"
            resultType="com.midea.pam.common.statistics.entity.ProjectCostDifferenceRecord">
        select distinct
        sr.project_id as projectId,
        sr.project_code projectCode,
        sr.project_name projectName,
        (case
        when dsrd.difference_type=3 then '直接差异'
        when dsrd.difference_type=4 then '间接差异'
        else '未知'
        end ) as differenceBigtype,
        dic.name differenceType, -- 分摊类型
        dsrd.share_radio shareRadio , -- 分摊率
        dsrd.amount amount, -- 分摊金额
        dsrd.cost_date costDate, -- 分摊日期
        c.material_difference_cost materialDifferenceCost -- 物料差异成本
        from pam_ctc.difference_share_result_detail dsrd
        inner join pam_statistics.project_cost_summary_record sr on dsrd.project_id = sr.project_id
        left join pam_ctc.cost_collection c on dsrd.cost_collection_id=c.id and c.deleted_flag = 0
        left join pam_basedata.ltc_dict dic on dsrd.segment3 = dic.code and dic.type = 'CostElements_Type' and
        dic.deleted_flag = 0
        where dsrd.deleted_flag = 0
        and sr.execute_id = #{executeId}
    </select>

    <select id="getCurrentHumanDetailCost"
            resultType="com.midea.pam.common.statistics.entity.ProjectCostHumanDetailRecord">
        SELECT
        sr.project_id AS projectId,
        sr.project_code AS projectCode,
        sr.project_name AS projectName,
        (case when wh.user_type = 1 then ui.name when wh.user_type = 2 then vm.vendor_name end) as applyName,
        (case when wh.user_type = 1 then ui.username when wh.user_type = 2 then vm.vendor_mip end) as applyMip,
        org.name_path as applyOrg,
        YEAR(wh.apply_date) as year,
        CASE MONTH(wh.apply_date)
        WHEN 1 THEN '一月'
        WHEN 2 THEN '二月'
        WHEN 3 THEN '三月'
        WHEN 4 THEN '四月'
        WHEN 5 THEN '五月'
        WHEN 6 THEN '六月'
        WHEN 7 THEN '七月'
        WHEN 8 THEN '八月'
        WHEN 9 THEN '九月'
        WHEN 10 THEN '十月'
        WHEN 11 THEN '十一月'
        WHEN 12 THEN '十二月'
        ELSE '' END as month,
        DATE_FORMAT(wh.apply_date,'%Y-%m-%d') as applyDate,
        ROUND(wh.apply_working_hours/8,2) as applyWorkingHours,
        case when wh.actual_cost_money is not null then wh.actual_cost_money
        else wh.cost_money end as costMoney,
        DATE_FORMAT(wh.create_at,'%Y-%m-%d') as fillDate,
        ROUND(wh.actual_working_hours/8,2) as actualWorkingHours,
        DATE_FORMAT(wh.approve_time,'%Y-%m-%d') as approveTime,
        case when wh.actual_cost_money is not null then  ROUND(wh.actual_cost_money*(if(wh.status = 4,wh.actual_working_hours,wh.apply_working_hours))/8,2)
		else ROUND(wh.cost_money*(if(wh.status = 4,wh.actual_working_hours,wh.apply_working_hours))/8,2) end as laborCost,
        wh.status,
        wh.user_type as userType,
        vm.vendor_code as vendorCode,
        vm.vendor_name as vendorName,
        wh.role_name as roleName
        FROM
        pam_statistics.project_cost_summary_record sr
        INNER JOIN pam_ctc.working_hour wh ON sr.project_id = wh.project_id
        LEFT JOIN pam_basedata.ltc_user_info ui ON wh.user_id = ui.id
        LEFT JOIN pam_basedata.vendor_mip vm ON wh.user_id = vm.vendor_mip_id
        LEFT JOIN (select user_id, max(org_id) as org_id, status from pam_basedata.ltc_org_user
            where status = 'Y' group by user_id) ou ON ou.user_id = wh.user_id
        AND ou.`status` = 'Y'
        LEFT JOIN pam_basedata.ltc_organization org ON org.id = ou.org_id
        where sr.execute_id = #{executeId}
        AND wh.status in (2,4,5)
        AND wh.delete_flag = 0
    </select>

    <select id="getCurrentHumanBudgetCost" resultType="decimal">
        select ifnull(sum(pbh.days * pbh.price * pbh.number),0) as budget
        from pam_ctc.project_budget_human pbh
        where pbh.project_id = #{projectId}
          and pbh.deleted_flag = 0
    </select>

    <select id="countWorkingHour" resultType="long">
        select
          count(0)
        from
          pam_ctc.working_hour
        where
            delete_flag = 0 and project_id = #{projectId} and is_import = 1
    </select>

    <select id="getProjectContractStandardAmount" resultType="com.midea.pam.common.statistics.entity.ProjectCostSummaryRecord">
        select
            rs.project_id as projectId,
            sum(round(ifnull(c.excluding_tax_amount, 0) * ifnull(p.conversion_rate, 1), 2)) as projectContractStandardAmount
        from pam_ctc.project_contract_rs rs
        left join pam_ctc.contract c on rs.contract_id = c.id
        left join pam_ctc.contract p on c.parent_id = p.id
        where rs.deleted_flag = 0 and c.deleted_flag = 0 and p.deleted_flag = 0
        and rs.project_id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by rs.project_id
    </select>

    <select id="getConfirmedCarryoverBill" resultType="com.midea.pam.common.ctc.entity.CarryoverBill">
        select project_id as projectId,
        sum(ifnull(cb.current_cost_actual, 0)) as currentCostActual,
        sum(ifnull(cb.current_income_amount, 0)) as currentIncomeAmount,
        sum(ifnull(cb.current_income_amount, 0) * ifnull(cb.conversion_rate, 1)) as standardCurrentIncomeTotalAmount
        from pam_ctc.carryover_bill cb
        where cb.deleted_flag = 0 and cb.status = 1
        and project_id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by project_id
    </select>

    <select id="getCurrentSummaryItemRecords"
            resultType="com.midea.pam.common.statistics.entity.ProjectCostSummaryItemRecord">
        select pcmsr.execute_id                        as executeId,
               pcmsr.project_id                        as projectId,
               '物料成本'                                  as itemName,
               1                                       as itemType,
               pcmsr.budget_price_total                as currentBudget,
               pcmsr.handled_cost                      as incurredCost,
               sum(ifnull(pcmsdr.todo_cost, 0))        as pendingCost,
               pcmsr.handled_cost                      as totalCost,
               pcmsr.rest_budget_price                 as remainderBudget,
               pcmsr.handled_cost_ratio                as incurredRatio,
               pbt.init_material_target_cost           as initTargetCost,
               pbt.inner_adjust_material_target_cost   as innerAdjustTargetCost,
               pbt.outer_adjust_material_target_cost   as outerAdjustTargetCost,
               pbt.current_material_target_cost        as currentTargetCost,
               pbt.inner_adjust_material_target_budget as innerAdjustTargetBudget,
               pbt.outer_adjust_material_target_budget as outerAdjustTargetBudget
        from pam_statistics.project_cost_materialcost_summary_record pcmsr
                 inner join pam_statistics.project_cost_materialcost_summary_detail_record pcmsdr on
                    pcmsr.id = pcmsdr.summary_id
                    and pcmsdr.deleted_flag = 0
                 left join pam_ctc.project_budget_target pbt on
                    pbt.project_id = pcmsr.project_id
                    and pbt.deleted_flag = 0
        where pcmsr.deleted_flag = 0
        and pcmsr.execute_id = #{executeId}
        group by pcmsr.project_id
        union all
        select ps.execute_id                        as executeId,
               ps.project_id                        as projectId,
               '人力成本'                               as itemName,
               2                                    as itemType,
               ps.budget                            as currentBudget,
               ps.incurred_cost                     as incurredCost,
               t.auditing_cost                      as pendingCost,
               ps.cost                              as totalCost,
               ps.remainder_budget                  as remainderBudget,
               ps.incurred_ratio                    as incurredRatio,
               pbt.init_human_target_cost           as initTargetCost,
               pbt.inner_adjust_human_target_cost   as innerAdjustTargetCost,
               pbt.outer_adjust_human_target_cost   as outerAdjustTargetCost,
               pbt.current_human_target_cost        as currentTargetCost,
               pbt.inner_adjust_human_target_budget as innerAdjustTargetBudget,
               pbt.outer_adjust_human_target_budget as outerAdjustTargetBudget
        from pam_statistics.project_cost_human_summary_record ps
                 left join pam_ctc.project_budget_target pbt on
                pbt.project_id = ps.project_id
                and pbt.deleted_flag = 0
                 inner join (select pi.project_id, sum(ifnull(pi.auditing_cost, 0)) as auditing_cost
                             from pam_statistics.project_cost_human_item_record pi
                             where execute_id = #{executeId}
                             group by project_id) t on
            ps.project_Id = t.project_id
        where ps.deleted_flag = 0
          and ps.execute_id =  #{executeId}
        union all
        (select pcfir.execute_id                                                                as executeId,
                pcfir.project_id                                                                as projectId,
                case
                    when (pcfir.`type` = 3 or pcfir.fee_item_name = '工单成本(差旅)') then '差旅成本'
                    when (pcfir.`type` = 4 and pcfir.fee_item_name <![CDATA[<> ]]> '工单成本(差旅)') then '费用成本（非差旅）小计'
                    else '' end                                                                 as itemName,
                pcfir.`type`                                                                    as itemType,
                sum(ifnull(pcfir.budget, 0))                                                    as currentBudget,
                sum(ifnull(pcfir.incurred_cost, 0))                                             as incurredCost,
                sum(ifnull(pcfir.pending_cost, 0))                                              as pendingCost,
                sum(ifnull(pcfir.total_cost, 0))                                                as totalCost,
                sum(ifnull(pcfir.budget, 0)) - sum(ifnull(pcfir.total_cost, 0))                 as remainderBudget,
                ROUND(sum(ifnull(pcfir.total_cost, 0)) / sum(ifnull(pcfir.budget, 0)) * 100, 2) as incurredRatio,
                if((pcfir.`type` = 1 or pcfir.fee_item_name = '工单成本(差旅)'), pbt.init_travel_target_cost,
                   pbt.init_other_target_cost)                                                  as initTargetCost,
                if((pcfir.`type` = 1 or pcfir.fee_item_name = '工单成本(差旅)'), pbt.inner_adjust_travel_target_cost,
                   pbt.inner_adjust_other_target_cost)                                          as innerAdjustTargetCost,
                if((pcfir.`type` = 1 or pcfir.fee_item_name = '工单成本(差旅)'), pbt.outer_adjust_travel_target_cost,
                   pbt.outer_adjust_other_target_cost)                                          as outerAdjustTargetCost,
                if((pcfir.`type` = 1 or pcfir.fee_item_name = '工单成本(差旅)'), pbt.current_travel_target_cost,
                   pbt.current_other_target_cost)                                               as currentTargetCost,
                if((pcfir.`type` = 1 or pcfir.fee_item_name = '工单成本(差旅)'), pbt.inner_adjust_travel_target_budget,
                   pbt.inner_adjust_other_target_budget)                                        as innerAdjustTargetBudget,
                if((pcfir.`type` = 1 or pcfir.fee_item_name = '工单成本(差旅)'), pbt.outer_adjust_travel_target_budget,
                   pbt.outer_adjust_other_target_budget)                                        as outerAdjustTargetBudget
         from pam_statistics.project_cost_fee_item_record pcfir
                  left join pam_ctc.project_budget_target pbt on
                    pbt.project_id = pcfir.project_id
                    and pbt.deleted_flag = 0
         where pcfir.deleted_flag = 0 and pcfir.`type` in (3,4)
           and pcfir.execute_id = #{executeId}
         group by pcfir.project_id, itemName
         order by pcfir.project_id, pcfir.`type` asc)
        union all
        select
            r.execute_id as executeId,
            r.project_id as projectId,
            r.fee_item_name as itemName,
            r.`type` as itemType,
            r.budget as currentBudget,
            r.incurred_cost as incurredCost,
            0 as pendingCost,
            r.total_cost as totalCost,
            r.remainder_budget as remainderBudget,
            r.incurred_ratio as incurredRatio,
            null as initTargetCost,
            null as innerAdjustTargetCost,
            null as outerAdjustTargetCost,
            null as currentTargetCost,
            null as innerAdjustTargetBudget,
            null as outerAdjustTargetBudget
        from
            pam_statistics.project_cost_fee_item_record r
        where r.deleted_flag = 0 and r.execute_id = #{executeId} and r.`type` = 4 and r.fee_item_name <![CDATA[<> ]]> '工单成本(差旅)'
        union all
        select
            r.execute_id                            as executeId,
            r.project_id                            as projectId,
            '资产折旧成本'                            as itemName,
            5                                       as itemType,
            r.budget                                as currentBudget,
            r.incurred_cost                 	    as incurredCost,
            null    							    as pendingCost,
            r.incurred_cost                         as totalCost,
            r.remainder_budget                      as remainderBudget,
            r.incurred_ratio                        as incurredRatio,
            null           						    as initTargetCost,
            null   								    as innerAdjustTargetCost,
            null   								    as outerAdjustTargetCost,
            null      							    as currentTargetCost,
            null									as innerAdjustTargetBudget,
            null									as outerAdjustTargetBudget
        from
            pam_statistics.project_cost_asset_summary_record r
        where r.deleted_flag = 0 and r.execute_id = #{executeId}
    </select>

    <select id="getCurrentGrossProfit" resultType="map">
        select
            sr.project_id as projectId,
            ifnull(current_budget,0) as currentTotalCost,
            win_budget as winTotalCost,
            case when win_budget is null then null else (sr.project_budget - ifnull(win_budget,0)) end as winProfitAmount,
            case when ifnull(sr.project_budget,0) != 0 and ifnull(win_budget,0) != 0 then round(((sr.project_budget - ifnull(win_budget,0))/ sr.project_budget) * 100, 2) else null end as winProfitRatio
        from
            pam_statistics.project_cost_summary_record sr
        left join (
            select
                project_id,
                sum(current_budget) as current_budget,
                sum(win_budget) as win_budget
            from
                pam_statistics.project_cost_summary_item_record
            where deleted_flag = 0
            and item_name in ('物料成本', '人力成本', '差旅成本', '费用成本（非差旅）小计')
            and execute_id = #{executeId}
            group by
                project_id) t on
            t.project_id = sr.project_id
        where
		sr.execute_id = #{executeId}
		and sr.deleted_flag = 0
    </select>

    <select id="getCurrentWinBusiness" resultType="map">
        select
            p.id as projectId, b.id as businessId,
        qd.fee_name as feeName, qd.cost
        from pam_ctc.project p
        left join pam_ctc.project_contract_rs pcr on p.id = pcr.project_id and pcr.deleted_flag = 0
        left join pam_ctc.contract c on c.id = pcr.contract_id
        left join pam_ctc.contract cp on cp.id = c.parent_id
        inner join pam_crm.business b on (b.id = cp.business_id or b.business_code = p.business_id) and b.status = 3
        inner join pam_crm.quotation q on b.id = q.business_id and q.deleted_flag = 0
        inner join pam_crm.quotation_detail qd on qd.quotation_id = q.id
        where qd.deleted_flag = 0
        and p.id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </select>

    <select id="getCurrentProjectCost" resultType="map">
       select p.id as projectId,
       ifnull(fs.cost, 0)+ifnull(hs.incurred_cost, 0)+ifnull(ms.handled_cost, 0)+ifnull(asr.incurred_cost, 0) as incurredCost
       from pam_ctc.project p
       left join pam_statistics.project_cost_fee_summary_record fs on fs.project_id = p.id and fs.execute_id = #{executeId}
       left join pam_statistics.project_cost_human_summary_record hs on hs.project_id = p.id and hs.execute_id = #{executeId}
       left join pam_statistics.project_cost_materialcost_summary_record ms on ms.project_id = p.id and ms.execute_id = #{executeId}
       left join pam_statistics.project_cost_asset_summary_record asr on asr.project_id = p.id and asr.execute_id = #{executeId}
       where p.id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </select>

    <select id="getCurrentFeeBudget" resultType="map">
        select
            p.id as projectId,
            (ifnull(t.feeBudget,0) + ifnull(travel.budget, 0)) as feeBudget
        from
            pam_ctc.project p
        left join (
            select
                sum(ifnull(pbf.amount, 0)) as feeBudget,
                project_id
            from
                pam_ctc.project_budget_fee pbf
            where
                pbf.deleted_flag = 0
            group by
                pbf.project_id) t on
            p.id = t.project_id
        left join (
            select
                pbt.project_id as project_id,
                sum((ifnull(pbt.hotel, 0) + ifnull(pbt.subsidy, 0) + ifnull(pbt.traffic, 0)) * pbt.number + ifnull(pbt.other, 0) + ifnull(pbt.come_back_traffic, 0)) as budget
            from
                pam_ctc.project_budget_travel pbt
            where
                pbt.deleted_flag = 0
            group by pbt.project_id ) travel on p.id = travel.project_id
        where p.id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </select>

    <select id="overProjectBudgetNotice" resultType="com.midea.pam.common.ctc.dto.ProjectDto">
        select distinct p.id,
                        p.code,
                        p.name,
                        p.status,
                        p.manager_id AS managerId,
                        p.manager_name AS managerName,
                        p.preview_flag as previewFlag,
                        pt.name AS projectTypeName, ui.username as managerMip
        from pam_ctc.project p
                 inner join pam_ctc.project_type pt on pt.id = p.type
                 inner join pam_statistics.project_cost_summary_record ps on ps.project_id = p.id
                 inner join pam_basedata.ltc_user_info ui on ui.id = p.manager_id
                 inner join (select er.id
                              from pam_statistics.project_cost_execute_record er
                              where er.deleted_flag = 0
                                and er.status = 2
                              order by er.create_at desc
                              limit 1) t on ps.execute_id = t.id
        where p.deleted_flag = 0 and ps.deleted_flag = 0
              and pt.budget_control_flag in (1,2) and p.status in (4,9,-3,-13,23,7)
              and ps.incurred_cost/ps.project_budget >= 0.9500
              and pt.name <![CDATA[<> ]]> '人力外包（RDM）'
              and ps.incurred_cost/ps.project_budget <![CDATA[ < ]]> 1.00
    </select>

    <select id="getVendorSiteBank" resultType="com.midea.pam.common.basedata.entity.VendorSiteBank">
        select * from pam_basedata.vendor_site_bank
        <where>
            <if test="vendorCode != null">
                and vendor_code = #{vendorCode}
            </if>
        </where>
    </select>

    <select id="statisticsHumanIncurredCostInner" resultType="java.math.BigDecimal">
        select
            sum(
            case when wh.actual_cost_money is not null then ROUND(wh.actual_cost_money *(if(wh.status = 4, wh.actual_working_hours, wh.apply_working_hours))/ 8, 2)
            else ROUND(wh.cost_money *(if(wh.status = 4, wh.actual_working_hours, wh.apply_working_hours))/ 8, 2) end
            ) as laborCost
        from pam_ctc.working_hour wh
        where
        wh.delete_flag = 0
        and wh.user_type = 1
        and wh.status in (2, 4, 5)
        and wh.project_id = #{projectId}
    </select>

    <select id="statisticsHumanIncurredCostOuter" resultType="java.math.BigDecimal">
        select
            sum(ifnull(t1.incurredCost, 0) + ifnull(t2.pendingCost, 0) + ifnull(t3.eaAvailableAmount, 0)) as feeCost
        from
        pam_ctc.project p
        left join (
            select
                pbh.project_id,
                1 as fee_flag,
                IFNULL(SUM(pbh.days * pbh.price * pbh.`number`), 0) as budget
            from
            pam_ctc.project_budget_human pbh
            where
            pbh.type = 2
            and pbh.deleted_flag = 0
            and pbh.project_id = #{projectId}
            group by
            pbh.project_id
            union all
            select
                pbt.project_id,
                2 as fee_flag,
                SUM((IFNULL(pbt.hotel, 0) + IFNULL(pbt.subsidy, 0) + IFNULL(pbt.traffic, 0)) * IFNULL(pbt.number, 0) +
                IFNULL(pbt.come_back_traffic, 0) + IFNULL(pbt.other, 0)) as budget
            from
            pam_ctc.project_budget_travel pbt
            where
            pbt.deleted_flag = 0
            and pbt.project_id = #{projectId}
            group by
            pbt.project_id
        ) bf on p.id = bf.project_id
        left join (
            select
                p.id as project_id,
                fi.id,
                fi.name,
                fi.fee_flag
            from
            pam_ctc.project p
            left join pam_basedata.unit u on
            p.unit_id = u.id
            and u.delete_flag = 0
            left join pam_basedata.fee_item fi on
            fi.unit_id = u.parent_id
            and fi.deleted_flag = 0
            where
            fi.fee_flag in (1, 2)
            and u.parent_id is not null
            and p.deleted_flag = 0
            and p.id = #{projectId}
            group by
            p.id,
            fi.fee_flag
        ) fi on fi.project_id = bf.project_id and fi.fee_flag = bf.fee_flag
        left join (
            select
                p.id as projectId,
                sum(ifnull(pbc.amount, 0)) as incurredCost,
                prc.fee_item_id as feeItemId
            from
            pam_ctc.project p
            inner join pam_ctc.project_fee_collection prc on
            prc.project_id = p.id
            and prc.deleted_flag = 0
            inner join pam_ctc.ems_pam_fee_detail fd on
            fd.budget_node_id = prc.ems_budget_id
            inner join pam_ctc.project_budget_cost pbc on
            fd.order_id = pbc.attribute1
            inner join pam_basedata.fee_item fi on
            fi.id = prc.fee_item_id
            and p.id = #{projectId}
            group by
            p.id,
            prc.fee_item_id
        ) t1 on p.id = t1.projectId and fi.id = t1.feeItemId
        left join (
            select
                p.id as projectId,
                ROUND(sum(ifnull(fd.fee_amount, 0)* ifnull(fd.erp_exchange_rate, 1)), 2) as pendingCost,
                prc.fee_item_id as feeItemId
            from
            pam_ctc.project p
            inner join pam_ctc.project_fee_collection prc on
            prc.project_id = p.id
            and prc.deleted_flag = 0
            inner join pam_ctc.ems_pam_fee_detail fd on
            fd.budget_node_id = prc.ems_budget_id
            and fd.import_erp_status != 'SUCCESS'
            inner join pam_basedata.fee_item fi on
            fi.id = prc.fee_item_id
            and p.id = #{projectId}
            group by
            p.id,
            prc.fee_item_id
        ) t2 on p.id = t2.projectId and fi.id = t2.feeItemId
        left join (
            select
                p.id as projectId,
                sum(ifnull(od.overplus_ea_amount, 0)) as eaAvailableAmount,
                prc.fee_item_id as feeItemId
            from
            pam_ctc.project p
            inner join pam_ctc.project_fee_collection prc on
            prc.project_id = p.id
            and prc.deleted_flag = 0
            inner join pam_ctc.ems_budget_occupy_detail od on
            od.budget_node_id = prc.ems_budget_id
            and od.is_ndyt != "Y"
            inner join pam_basedata.fee_item fi on
            fi.id = prc.fee_item_id
            and p.id = #{projectId}
            group by
            p.id,
            prc.fee_item_id
        ) t3 on p.id = t3.projectId and fi.id = t3.feeItemId
        where
        bf.project_id = #{projectId}
        and fi.fee_flag = 1
    </select>

    <select id="statisticsHumanIncurredCostRevenue" resultType="java.math.BigDecimal">
        with t as
        (
            select
                #{projectId} as project_id,
                count(0) as num
            from
            pam_ctc.working_hour
            where
            delete_flag = 0
            and is_import = 1
            and project_id = #{projectId}
        )
        select
            sum(if(rcod.is_import = 1 and t.num > 0, 0, ifnull(rcod.inner_labor_cost, 0) + ifnull(rcod.outer_labor_cost, 0))) as taskCost
        from
        pam_ctc.revenue_cost_order_detail rcod
        inner join pam_ctc.revenue_cost_order rco
        on rcod.order_id = rco.id
        and rco.`status` = 3
        and rco.deleted_flag = 0
        and (rco.type is null
        or rco.type <![CDATA[ <> ]]> '自动成本补差')
        left join t on t.project_id = rco.project_id
        where
        rcod.deleted_flag = 0
        and rco.project_id = #{projectId}
    </select>

    <select id="getCurrentAssetBudget" resultType="java.math.BigDecimal">
        select
            ifnull(sum(amount), 0) as budget
        from pam_ctc.project_budget_asset
        where deleted_flag = 0
        and project_id = #{projectId}
    </select>

    <select id="getCurrentAssetSummaryCost" resultType="com.midea.pam.common.statistics.entity.ProjectCostAssetSummaryRecord">
        select
            ifnull(sum(a.deprn_reserve), 0) as incurredCost,
            ifnull(sum(a.cost), 0) - ifnull(sum(a.deprn_reserve), 0) as remainderBudget
        from pam_ctc.project_asset_rs rs
        left join pam_ctc.asset a on rs.asset_number = a.asset_number
        where rs.deleted_flag = 0
        and rs.project_id = #{projectId}
    </select>

    <select id="getCurrentAssetDetailCost" resultType="com.midea.pam.common.statistics.entity.ProjectCostAssetDetailRecord">
        select
            pcasr.project_id as projectId,
            d.asset_number as assetNumber,
            a.description,
            d.date_placed_in_service as datePlacedInService,
            d.life_in_months as lifeInMonths,
            d.period_name as periodName,
            d.deprn_amount as deprnAmount,
            d.adjustment_amount as adjustmentAmount,
            d.create_at as deprnCreateAt
        from pam_statistics.project_cost_asset_summary_record pcasr
        inner join pam_ctc.project_asset_rs rs on pcasr.project_id = rs.project_id and rs.deleted_flag = 0
        left join pam_ctc.asset a on rs.asset_number = a.asset_number
        left join pam_ctc.asset_deprn d on rs.asset_number = d.asset_number
        where pcasr.execute_id = #{executeId}
        and d.asset_number is not null
        order by d.asset_number ,d.period_name
    </select>

    <select id="getCurrentAssetItemCost" resultType="com.midea.pam.common.statistics.entity.ProjectCostAssetItemRecord">
        select
            pcasr.project_id as projectId,
            a.asset_number as assetNumber,
            a.description,
            d.date_placed_in_service as datePlacedInService,
            d.life_in_months as lifeInMonths,
            d.period_name as lastPeriodName,
            a.cost,
            a.deprn_reserve as deprnReserve,
            d.salvage_value as salvageValue
        from pam_statistics.project_cost_asset_summary_record pcasr
        inner join pam_ctc.project_asset_rs rs on pcasr.project_id = rs.project_id and rs.deleted_flag = 0
        left join pam_ctc.asset a on rs.asset_number = a.asset_number
        left join pam_ctc.asset_deprn d on a.asset_number = d.asset_number
        left join pam_ctc.asset_deprn tem ON d.asset_number = tem.asset_number and d.period_name <![CDATA[ < ]]> tem.period_name
        where pcasr.execute_id = #{executeId}
        and tem.asset_number is null
    </select>

    <select id="getProjectEndList" resultType="com.midea.pam.common.ctc.entity.Project">
        select
            p.id, p.budget_cost as budgetCost, p.code, p.name, p.status as status, p.amount as amount
        from
            pam_ctc.project p
        where
            p.deleted_flag = 0
            and p.ou_id in
            <foreach collection="ouIdList" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
            and p.status = 10
    </select>

</mapper>