<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ProjectProblemStatisticsExtMapper">

    <sql id="Where_Clause">
        <if test="code != null and code != ''">
            and pp.code like concat('%', #{code}, '%')
        </if>
        <if test="title != null and title != ''">
            and pp.title like concat('%', #{title}, '%')
        </if>
        <if test="statusList != null and statusList.size>0">
            and pp.status in
            <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="auditorUserName != null and auditorUserName != ''">
            and pp.auditor_user_name like concat('%', #{auditorUserName}, '%')
        </if>
        <if test="currentHandlerUserName != null and currentHandlerUserName != ''">
            and pp.current_handler_user_name like concat('%', #{currentHandlerUserName}, '%')
        </if>
        <if test="severityLevel != null and severityLevel != ''">
            and pp.severity_level like concat('%', #{severityLevel}, '%')
        </if>
        <if test="severityLevelList != null and severityLevelList.size>0">
            and pp.severity_level in
            <foreach collection="severityLevelList" item="severityLevels" index="index" open="(" separator="," close=")">
                #{severityLevels}
            </foreach>
        </if>
        <if test="problemType != null and problemType != ''">
            and pp.problem_type like concat('%', #{problemType}, '%')
        </if>
        <if test="problemSolverUserName != null and problemSolverUserName != ''">
            and pp.problem_solver_user_name like concat('%', #{problemSolverUserName}, '%')
        </if>
        <if test="createByUserName != null and createByUserName != ''">
            and pp.create_by_user_name like concat('%', #{createByUserName}, '%')
        </if>
        <if test="projectCode != null and projectCode != ''">
            and p.code like concat('%', #{projectCode}, '%')
        </if>
        <if test="projectName != null and projectName != ''">
            and p.name like concat('%', #{projectName}, '%')
        </if>
        <if test="expectedSettlementStartDate != null">
             <![CDATA[and pp.expected_settlement_date >= ]]> #{expectedSettlementStartDate}
        </if>
        <if test="expectedSettlementEndDate != null">
             <![CDATA[and pp.expected_settlement_date <= ]]> #{expectedSettlementEndDate}
        </if>
        <if test="plannedFinishStartDate != null">
            <![CDATA[and pp.planned_finish_date  >= ]]> #{plannedFinishStartDate}
        </if>
        <if test="plannedFinishEndDate != null">
            <![CDATA[and pp.planned_finish_date  <= ]]> #{plannedFinishEndDate}
        </if>
        <if test="actualStartStartTime != null">
            <![CDATA[and pp.actual_start_time  >= ]]> #{actualStartStartTime}
        </if>
        <if test="actualStartEndTime != null">
            <![CDATA[and pp.actual_start_time  <= ]]> #{actualStartEndTime}
        </if>
        <if test="actualFinishStartDate != null">
            <![CDATA[and pp.actual_finish_time  >= ]]> #{actualFinishStartDate}
        </if>
        <if test="actualFinishEndDate != null">
            <![CDATA[and pp.actual_finish_time  <= ]]> #{actualFinishEndDate}
        </if>
        <if test="createAtStartDate != null">
            <![CDATA[and pp.create_at  >= ]]> #{createAtStartDate}
        </if>
        <if test="createAtEndDate != null">
            <![CDATA[and pp.create_at  <= ]]> #{createAtEndDate}
        </if>
        <if test="updateAtStartDate != null">
            <![CDATA[and pp.update_at  >= ]]> #{updateAtStartDate}
        </if>
        <if test="updateAtEndDate != null">
            <![CDATA[and pp.update_at  <= ]]> #{updateAtEndDate}
        </if>
        <if test="overdueMinDays != null">
            and ((to_days(pp.actual_finish_time) - to_days(pp.actual_start_time) + 1) <![CDATA[ >= ]]> #{overdueMinDays})
        </if>
        <if test="overdueMaxDays != null">
            and ((to_days(pp.actual_finish_time) - to_days(pp.actual_start_time) + 1) <![CDATA[ <= ]]> #{overdueMaxDays})
        </if>
        <if test="residenceMinTime != null">
            and ((pp.status = 4 or pp.status = 8) and round(TIMESTAMPDIFF(HOUR, pp.last_update_at, now()) / 24, 1) <![CDATA[ >= ]]> #{residenceMinTime})
        </if>
        <if test="residenceMaxTime != null">
            and ((pp.status = 4 or pp.status = 8) and round(TIMESTAMPDIFF(HOUR, pp.last_update_at, now()) / 24, 1) <![CDATA[ <= ]]> #{residenceMaxTime})
        </if>
        <if test="unitId != null">
            and pp.unit_id = #{unitId}
        </if>
        <if test="userId != null">
            and pp.auditor = #{userId}
        </if>
        <if test="projectId != null and projectId != ''">
            and pp.project_id = #{projectId}
        </if>
        <if test="createByUserName != null and createByUserName != ''">
            and pp.create_by_user_name like concat('%', #{createByUserName}, '%')
        </if>
        and pp.delete_flag = 0 and p.deleted_flag =0
    </sql>

    <select id="projectProblemList" resultType="com.midea.pam.common.ctc.dto.ProjectProblemStatisticsDto">
        select distinct
         pp.id as id,
         pp.title as title,
         pp.project_id as projectId,
         pp.problem_type as problemType,
         pp.severity_level as severityLevel,
         pp.expected_settlement_date as expectedSettlementDate,
         pp.problem_describe as problemDescribe,
         pp.problem_solver as problemSolver,
         pp.auditor as auditor,
         pp.status as status,
         pp.create_by as createBy,
         pp.create_at as createAt,
         pp.update_by as updateBy,
         pp.update_at as updateAt,
         pp.code as code,
         pp.planned_finish_date as plannedFinishDate,
         pp.actual_finish_time as actualFinishTime,
         pp.actual_start_time as actualStartTime,
         pp.unit_id as unitId,
         pp.auditor_user_name as auditorUserName,
         pp.problem_solver_user_name as problemSolverUserName,
         pp.create_by_user_name as createByUserName,
         pp.current_handler as currentHandler,
         pp.current_handler_user_name as currentHandlerUserName,
         p.name as projectName,
         p.code as projectCode,
         case
            when to_days(pp.actual_finish_time) - to_days(pp.actual_start_time) &lt; 0 then 0
            else to_days(pp.actual_finish_time) - to_days(pp.actual_start_time) + 1 end
	     as overdueDays,
         case
            when pp.status = 4 or pp.status = 8 then round(TIMESTAMPDIFF(second, pp.last_update_at, now()) / (24 * 3600), 1)
            else null end
	    as residenceTime
        from pam_ctc.project_problem pp
        left join pam_ctc.project p on pp.project_id = p.id
        where 1=1
        <include refid="Where_Clause"></include>
        order by pp.code desc
        <if test="expectedSettlementDateAsc != null and expectedSettlementDateAsc != ''">
            , pp.expected_settlement_date asc
        </if>
        <if test="expectedSettlementDateDesc != null and expectedSettlementDateDesc != ''">
            , pp.expected_settlement_date desc
        </if>
        <if test="plannedFinishDateAsc != null and plannedFinishDateAsc != ''">
            , pp.planned_finish_date asc
        </if>
        <if test="plannedFinishDateDesc != null and plannedFinishDateDesc != ''">
            , pp.planned_finish_date desc
        </if>
        <if test="actualFinishDateAsc != null and actualFinishDateAsc != ''">
            , pp.actual_finish_time asc
        </if>
        <if test="actualFinishDateDesc != null and actualFinishDateDesc != ''">
            , pp.actual_finish_time desc
        </if>
        <if test="createAtDateAsc != null and createAtDateAsc != ''">
            , pp.create_at asc
        </if>
        <if test="createAtDateDesc != null and createAtDateDesc != ''">
            , pp.create_at desc
        </if>
    </select>

    <select id="getPortalRemindCount" parameterType="java.util.ArrayList" resultType="java.lang.Long">
        SELECT
          COUNT(0)
        FROM
          pam_ctc.project_problem pp
        WHERE pp.delete_flag = 0
          AND pp.status in (2,4,8)
        <if test="userId != null">
           and pp.auditor = #{userId}
        </if>
        <if test="unitId != null">
            and pp.unit_id = #{unitId}
        </if>
    </select>

    <select id="getOrgName" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT lo.name FROM pam_basedata.ltc_user_info l
        LEFT JOIN pam_basedata.ltc_org_user lou ON l.id = lou.user_id
        LEFT JOIN pam_basedata.ltc_organization lo on lou.org_id = lo.id
        WHERE l.status ='Y' AND lou.status = 'Y' AND lo.status = 'Y' AND l.type =1
    </select>

    <select id="groupByStatus" resultType="java.util.HashMap">
        select 1 as id, '提交' as name, (select count(*) from pam_ctc.project_problem where delete_flag = false and status in (2, 4, 8, 16) and project_id = #{projectId}) as count
        union
        select 2 as id, '审核分配' as name, (select count(*) from pam_ctc.project_problem where delete_flag = false and status = 2 and project_id = #{projectId}) as count
        union
        select 3 as id, '解决中' as name, (select count(*) from pam_ctc.project_problem where delete_flag = false and status = 4 and project_id = #{projectId}) as count
        union
        select 4 as id, '验证中' as name, (select count(*) from pam_ctc.project_problem where delete_flag = false and status = 8 and project_id = #{projectId}) as count
        union
        select 5 as id, '关闭' as name, (select count(*) from pam_ctc.project_problem where delete_flag = false and status = 16 and project_id = #{projectId}) as count
    </select>
</mapper>