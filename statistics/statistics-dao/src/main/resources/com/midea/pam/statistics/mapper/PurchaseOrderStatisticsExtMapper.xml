<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.PurchaseOrderStatisticsExtMapper">

    <sql id="queryCondition">
        <!-- 模糊采购订单号-->
        <if test="fuzzyOrderNum != null and fuzzyOrderNum != ''">
            AND o.num like concat('%', #{fuzzyOrderNum, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊供应商名称-->
        <if test="fuzzyVendorName != null and fuzzyVendorName != ''">
            AND o.vendor_name like concat('%', #{fuzzyVendorName, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊供应商编码-->
        <if test="fuzzyVendorNum != null and fuzzyVendorNum != ''">
            AND o.vendor_num like concat('%', #{fuzzyVendorNum, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 同步状态-->
        <if test="syncStatusList != null">
            AND o.sync_status in
            <foreach collection="syncStatusList" item="syncStatus" index="index" open="(" separator="," close=")">
                #{syncStatus}
            </foreach>
        </if>

        <!-- 业务实体-->
        <if test="projectOuNameList != null">
            AND ou.operating_unit_name in
            <foreach collection="projectOuNameList" item="OuName" index="index" open="(" separator="," close=")">
                #{OuName}
            </foreach>
        </if>

        <!-- 模糊物料描述-->
        <if test="materielDescr != null and materielDescr != ''">
            AND od.`materiel_descr` like concat('%', #{materielDescr, jdbcType=CHAR}, '%')
        </if>

        <!-- 模糊ERP物料编码-->
        <if test="erpCode != null and erpCode != ''">
            AND od.`erp_code` like concat('%', #{erpCode, jdbcType=CHAR}, '%')
        </if>

        <!-- 模糊PAM物料编码-->
        <if test="pamCode != null and pamCode != ''">
            AND od.`pam_code` like concat('%', #{pamCode, jdbcType=CHAR}, '%')
        </if>

        <!-- 模糊采购人-->
        <if test="buyerName != null and buyerName != ''">
            AND o.buyer_name like concat('%',#{buyerName},'%')
        </if>

        <!-- 模糊订单行创建人-->
        <if test="orderDetailCreateByName != null and orderDetailCreateByName != ''">
            AND lui.name like concat('%',#{orderDetailCreateByName},'%')
        </if>

        <!--订单行创建日期检索-->
        <if test="orderDetailCreateAtBegin != null" >
            AND od.create_at &gt;= #{orderDetailCreateAtBegin, jdbcType=TIMESTAMP}
        </if>
        <if test ="orderDetailCreateAtEnd != null">
            AND od.create_at &lt;= #{orderDetailCreateAtEnd, jdbcType=TIMESTAMP}
        </if>

        <!--创建日期检索-->
        <if test="orderCreateAtBegin != null" >
            AND o.create_at &gt;= #{orderCreateAtBegin, jdbcType=TIMESTAMP}
        </if>
        <if test ="orderCreateAtEnd != null">
            AND o.create_at &lt;= #{orderCreateAtEnd, jdbcType=TIMESTAMP}
        </if>


        <!--供方承诺交期检索-->
        <if test="promisedDateStart != null" >
            AND od.contract_appoint_date &gt;= #{promisedDateStart, jdbcType=TIMESTAMP}
        </if>
        <if test ="promisedDateEnd != null">
            AND od.contract_appoint_date &lt;= #{promisedDateEnd, jdbcType=TIMESTAMP}
        </if>

        <!--跟踪日期检索-->
        <if test="trackDateStart != null" >
            AND od.track_date &gt;= #{trackDateStart, jdbcType=TIMESTAMP}
        </if>
        <if test ="trackDateEnd != null">
            AND od.track_date &lt;= #{trackDateEnd, jdbcType=TIMESTAMP}
        </if>

        <if test="resouce != null and resouce != ''">
            and o.order_status = 1 and (o.sync_status = 2 or (o.sync_status = 0 and o.create_at &lt; date_sub(sysdate(),interval 6 hour)) or o.sync_status = 3)
        </if>

        <!--计划交货日期-->
        <if test="deliveryTimeBegin != null" >
            AND od.delivery_time &gt;= #{deliveryTimeBegin, jdbcType=TIMESTAMP}
        </if>
        <if test ="deliveryTimeEnd != null">
            AND od.delivery_time &lt;= #{deliveryTimeEnd, jdbcType=TIMESTAMP}
        </if>

        <if test="ouList != null">
            and o.ou_id in
            <foreach collection="ouList" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>
        <if test="portal != null">
            and o.order_status not in (2,3)
            and (IFNULL(round(t2.storage_count),0) - IFNULL(round(t3.return_count),0)) = 0
        </if>

        <!-- 订单状态-->
        <if test="statusList != null">
            AND o.order_status in
            <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <!-- erp订单状态-->
        <if test="erpOrderStatusList != null">
            AND o.erp_order_status in
            <foreach collection="erpOrderStatusList" item="erpOrderStatus" index="index" open="(" separator="," close=")">
                #{erpOrderStatus}
            </foreach>
        </if>
        <!-- 同步订单状态-->
        <if test="manySyncStatusArrList != null">
            AND o.sync_status in
            <foreach collection="manySyncStatusArrList" item="manySyncStatus" index="index" open="(" separator="," close=")">
                #{manySyncStatus}
            </foreach>
        </if>
    </sql>

    <select id="selectListWithDetail" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        select
        od.id,
        od.material_purchase_requirement_id as materialPurchaseRequirementId,
        od.purchase_order_id as purchaseOrderId,
        od.line_number as lineNumber,
        od.project_id as projectId,
        od.erp_code as erpCode,
        od.pam_code as pamCode,
        od.delivery_time as deliveryTime,
        od.materiel_id as materielId,
        od.materiel_descr as materielDescr,
        od.unit_code as unitCode,
        od.unit as unit,
        od.vendor_asl_id as vendorAslId,
        od.vendor_name as vendorName,
        od.vendor_site_code as vendorSiteCode,
        od.order_num as orderNum,
        IFNULL(od.quantity, 0) as quantity,
        IFNULL(od.cancel_num, 0) as cancelNum,
        od.status,
        od.deleted_flag as deletedFlag,
        od.create_by as createBy,
        o.create_at as createAt,
        od.update_by as updateBy,
        od.update_at as updateAt,
        o.num as num,
        o.order_status as orderStatus,
        p.name as projectName,
        p.code as projectCode,
        o.vendor_num as vendorCode,
        o.buyer_name as buyer,
        o.sync_status as syncStatus,
        ou.operating_unit_name as ouName,
        od.publish_time as publishTime,
        od.create_at as orderDetailCreateAt,
        lui.name as orderDetailCreateByName,
        od.delivery_address as deliveryAddress,
        od.consignee as consignee,
        od.contact_phone as contactPhone
        FROM
        pam_ctc.purchase_order o
        left join pam_ctc.purchase_order_detail od on od.purchase_order_id = o.id and od.deleted_flag = 0
        left join pam_ctc.project p on od.project_id = p.id
        left join pam_basedata.operating_unit ou on o.ou_id = ou.id
        left join pam_basedata.ltc_user_info lui on lui.id = od.create_by
        where
        o.deleted_flag = 0
        and od.id is not null
        <include refid="queryCondition" />
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            and p.name like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            and p.code like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>
        ORDER BY
        o.create_at desc
    </select>

    <select id="findList" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDto">
        SELECT
        o.id,
        o.num,
        o.project_id as 'projectId',
        o.vendor_asl_id as 'vendorAslId',
        o.vendor_name as 'vendorName',
        o.vendor_site_code as 'vendorSiteCode',
        o.buyer_id as 'buyerId',
        o.buyer_name as 'buyerName',
        o.order_status as 'orderStatus',
        o.sync_status as 'syncStatus',
        o.erp_message as 'erpMessage',
        o.deleted_flag as 'deletedFlag',
        o.create_by as 'createBy',
        o.create_at as 'createAt',
        o.update_by as 'updateBy',
        o.update_at as 'updateAt',
        (select group_concat(distinct pod.project_name) from pam_ctc.purchase_order_detail pod where pod.deleted_flag = 0 and pod.purchase_order_id = o.id) as 'projectName',
        (select group_concat(distinct pod.project_num) from pam_ctc.purchase_order_detail pod where pod.deleted_flag = 0 and pod.purchase_order_id = o.id) as 'projectNum',
        o.vendor_num as 'vendorNum',
        ou.operating_unit_name as projectOuName
        FROM
        pam_ctc.purchase_order o
        left join pam_basedata.operating_unit ou on o.ou_id = ou.id
        WHERE
        o.deleted_flag = 0
        and (o.source != '2' or o.source is null)
        <include refid="queryCondition" />
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            and (select group_concat(distinct pod.project_name) from pam_ctc.purchase_order_detail pod where pod.deleted_flag = 0 and pod.purchase_order_id = o.id) like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            and (select group_concat(distinct pod.project_num) from pam_ctc.purchase_order_detail pod where pod.deleted_flag = 0 and pod.purchase_order_id = o.id) like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>
        ORDER BY
        o.create_at desc
    </select>

    <select id="findWbsList" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDto">
        SELECT
        o.id,
        o.num,
        o.project_id as 'projectId',
        o.vendor_asl_id as 'vendorAslId',
        o.vendor_asl_id as 'vendorId',
        o.vendor_name as 'vendorName',
        o.vendor_site_code as 'vendorSiteCode',
        o.buyer_id as 'buyerId',
        o.buyer_name as 'buyerName',
        o.order_status as 'orderStatus',
        o.erp_order_status as 'erpOrderStatus',
        o.sync_status as 'syncStatus',
        o.erp_message as 'erpMessage',
        o.deleted_flag as 'deletedFlag',
        o.create_by as 'createBy',
        o.create_at as 'createAt',
        o.update_by as 'updateBy',
        o.update_at as 'updateAt',
        o.ou_id as 'ouId',
        ou.operating_unit_name as 'projectOuName',
        o.dispatch_is as 'dispatchIs',
        o.vendor_num as 'vendorNum',
        o.vendor_num as 'vendorCode',
        o.pricing_type as 'pricingType',
        o.currency_code as 'currencyCode'
        FROM
        pam_ctc.purchase_order o
        left join pam_basedata.operating_unit ou on o.ou_id = ou.id
        WHERE
        o.deleted_flag = 0
        and o.source = '2'
        <include refid="queryCondition" />
        ORDER BY
        o.create_at desc
    </select>

    <select id="selectPurchaseOrderProgress" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        select
            od.id,
            od.material_purchase_requirement_id as materialPurchaseRequirementId,
            od.purchase_order_id as purchaseOrderId,
            od.line_number as lineNumber,
            od.project_id as projectId,
            od.erp_code as erpCode,
            od.pam_code as pamCode,
            od.delivery_time as deliveryTime,
            od.materiel_id as materielId,
            od.materiel_descr as materielDescr,
            od.unit_code as unitCode,
            od.unit as unit,
            o.vendor_asl_id as vendorAslId,
            o.vendor_name as vendorName,
            o.vendor_site_code as vendorSiteCode,
            od.order_num as orderNum,
            IFNULL(od.quantity, 0) as quantity,
            IFNULL(od.cancel_num, 0) as cancelNum,
            (od.order_num - od.cancel_num) as actualOrderNum,
            od.status,
            od.deleted_flag as deletedFlag,
            od.create_by as createBy,
            o.create_at as createAt,
            od.update_by as updateBy,
            od.update_at as updateAt,
            o.num as num,
            o.order_status as orderStatus,
            p.name as projectName,
            p.code as projectCode,
            o.vendor_num as vendorCode,
            o.buyer_name as buyer,
            o.sync_status as syncStatus,
            ou.operating_unit_name as ouName,
            od.contract_appoint_date as promisedDate,
            od.track_date as trackDate,
            od.publish_time as publishTime,
            od.create_at as orderDetailCreateAt,
            lui.name as orderDetailCreateByName
        from
            pam_ctc.purchase_order_detail od
        inner join pam_ctc.purchase_order o on
            od.purchase_order_id = o.id
        inner join pam_ctc.project p on
            od.project_id = p.id
        inner join pam_basedata.operating_unit ou on
            p.ou_id = ou.id
        left join pam_basedata.ltc_user_info lui on
            lui.id = od.create_by
        where
            od.deleted_flag = 0
        <include refid="queryCondition" />
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            and od.`project_name` like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            and od.`project_num` like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>
        ORDER BY o.create_at DESC,od.erp_code ASC
    </select>

    <select id="selectPurchaseOrderProgressCount" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        select
            IFNULL(sum(if(pp.`transaction_type` = 'RECEIVE', pp.`transaction_quantity`, 0)), 0) receiveCount,
            IFNULL(sum(if(pp.`transaction_type` = 'DELIVER', pp.`transaction_quantity`, 0)), 0) storageCount,
            IFNULL(sum(if(pp.`transaction_type` = 'RETURN TO RECEIVING', pp.`transaction_quantity`, 0)), 0) returnCount,
            pp.`item_code` erpCode,
            pp.`po_number` num,
            od.`purchase_order_id` purchaseOrderId,
            od.`line_number` as lineNumber
        from
            pam_ctc.`purchase_progress` pp
            inner join pam_ctc.`purchase_order_detail` od on
            od.`erp_code` = pp.`item_code`
            and pp.`po_line_no` = od.`line_number`
        where
            (pp.`transaction_type` = 'RECEIVE' or pp.`transaction_type` = 'DELIVER' or pp.`transaction_type` = 'RETURN TO RECEIVING')
            and od.deleted_flag = 0
            <if test="erpCodeList != null and erpCodeList.size() > 0">
                AND pp.`item_code` in
                <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
            </if>
            <if test="numList != null and numList.size() > 0">
                AND pp.`po_number` in
                <foreach collection="numList" item="num" index="index" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>
            <if test="lineNumberList != null and lineNumberList.size() > 0">
                AND od.`line_number` in
                <foreach collection="lineNumberList" item="lineNumber" index="index" open="(" separator="," close=")">
                    #{lineNumber}
                </foreach>
            </if>
            <if test="purchaseOrderIdList != null and purchaseOrderIdList.size() > 0">
                AND od.`purchase_order_id` in
                <foreach collection="purchaseOrderIdList" item="purchaseOrderId" index="index" open="(" separator="," close=")">
                    #{purchaseOrderId}
                </foreach>
            </if>
        group by
            pp.`item_code`,
            od.`purchase_order_id`,
            pp.`po_number`,
            od.`line_number`
    </select>

    <select id="selectPurchaseOrderProgressMinDate" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        select
            pp.`item_code` erpCode,
            pp.`po_number` num,
            MIN(pp.`transaction_date`) earliestStorageDate,
            od.`purchase_order_id` purchaseOrderId,
            od.`line_number` lineNumber
        from
            pam_ctc.purchase_progress pp
            inner join pam_ctc.purchase_order po on
                    po.num = pp.`po_number`
                and po.deleted_flag = 0
            left join pam_ctc.`purchase_order_detail` od on
                    od.`purchase_order_id` = po.id
                and pp.`po_line_no` = od.`line_number`
                and od.deleted_flag = 0
        where
            pp.`transaction_type` = 'DELIVER'
            <if test="erpCodeList != null and erpCodeList.size() > 0">
                AND pp.`item_code` in
                <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
            </if>
            <if test="numList != null and numList.size() > 0">
                AND pp.`po_number` in
                <foreach collection="numList" item="num" index="index" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>
            <if test="lineNumberList != null and lineNumberList.size() > 0">
                AND od.`line_number` in
                <foreach collection="lineNumberList" item="lineNumber" index="index" open="(" separator="," close=")">
                    #{lineNumber}
                </foreach>
            </if>
            <if test="purchaseOrderIdList != null and purchaseOrderIdList.size() > 0">
                AND od.`purchase_order_id` in
                <foreach collection="purchaseOrderIdList" item="purchaseOrderId" index="index" open="(" separator="," close=")">
                    #{purchaseOrderId}
                </foreach>
            </if>
        group by
            pp.`item_code`,
            pp.`po_number`,
            od.`purchase_order_id`,
            od.`line_number`
    </select>

    <select id="selectPurchaseOrderRecord" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        with k as
        (
        select
            od.id,
            od.material_purchase_requirement_id as materialPurchaseRequirementId,
            od.purchase_order_id as purchaseOrderId,
            od.line_number as lineNumber,
            od.project_id as projectId,
            od.erp_code as erpCode,
            od.pam_code as pamCode,
            od.delivery_time as deliveryTime,
            od.materiel_id as materielId,
            od.materiel_descr as materielDescr,
            od.unit_code as unitCode,
            o.currency as currency,
            o.conversion_type as conversionType,
            o.conversion_rate as conversionRate,
            od.cost as cost,
            od.unit as unit,
            od.vendor_asl_id as vendorAslId,
            od.vendor_name as vendorName,
            od.vendor_site_code as vendorSiteCode,
            od.order_num as orderNum,
            IFNULL(od.quantity, 0) as quantity,
            IFNULL(od.cancel_num, 0) as cancelNum,
            (od.order_num - od.cancel_num ) as actualOrderNum,
            od.status,
            od.deleted_flag as deletedFlag,
            od.create_by as createBy,
            o.create_at as createAt,
            od.update_by as updateBy,
            od.update_at as updateAt,
            o.num as num,
            o.order_status as orderStatus,
            p.name as projectName,
            p.code as projectCode,
            od.vendor_num as vendorCode,
            o.buyer_name as buyer,
            o.sync_status as syncStatus,
            ou.operating_unit_name as ouName,
            0 as receiveCount,
            0 as storageCount,
            0 as returnCount,
            0 as actualStorageCount,
            null as earliestStorageDate,
            od.contract_appoint_date as promisedDate,
            od.track_date as trackDate,
            od.publish_time as publishTime,
            case when od.status = 1 then 1 else 0 end as 'type',
            od.create_at as orderDetailCreateAt,
            lui.name as orderDetailCreateByName,
            od.delivery_address as deliveryAddress,
            od.consignee as consignee,
            od.contact_phone as contactPhone
        from
            pam_ctc.purchase_order_detail od
        left join pam_ctc.purchase_order o on
            od.purchase_order_id = o.id
        left join pam_ctc.project p on
            od.project_id = p.id
        left join pam_basedata.operating_unit ou on
            p.ou_id = ou.id
        left join pam_basedata.ltc_user_info lui on
            lui.id = od.create_by
        where
            od.deleted_flag = 0
            and (o.source != '2' or o.source is null)
            <include refid="queryCondition" />
            <if test="me">
                and (o.buyer_id = #{userId}
                or o.id in (select origin_id from pam_ctc.purchase_order_change_history poch where poch.origin_id = od.purchase_order_id and poch.deleted_flag = 0 and poch.history_type = 1 and poch.order_status = 11)
                or o.create_by = #{userId}
                or od.create_by = #{userId}
                or p.manager_id =#{userId})
            </if>
            <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
                and od.`project_name` like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
            </if>
            <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
                and od.`project_num` like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
            </if>
        union all
        select
            od.id,
            od.material_purchase_requirement_id as materialPurchaseRequirementId,
            od.purchase_order_id as purchaseOrderId,
            od.line_number as lineNumber,
            od.project_id as projectId,
            od.erp_code as erpCode,
            od.pam_code as pamCode,
            od.delivery_time as deliveryTime,
            od.materiel_id as materielId,
            od.materiel_descr as materielDescr,
            od.unit_code as unitCode,
            o.currency as currency,
            o.conversion_type as conversionType,
            o.conversion_rate as conversionRate,
            od.cost as cost,
            od.unit as unit,
            o.vendor_asl_id as vendorAslId,
            o.vendor_name as vendorName,
            o.vendor_site_code as vendorSiteCode,
            od.order_num as orderNum,
            IFNULL(od.quantity, 0) as quantity,
            IFNULL(od.cancel_num, 0) as cancelNum,
            ( od.order_num - od.cancel_num ) as actualOrderNum,
            od.status,
            od.deleted_flag as deletedFlag,
            od.create_by as createBy,
            o.create_at as createAt,
            od.update_by as updateBy,
            od.update_at as updateAt,
            o.num as num,
            o.order_status as orderStatus,
            p.name as projectName,
            p.code as projectCode,
            o.vendor_num as vendorCode,
            o.buyer_name as buyer,
            o.sync_status as syncStatus,
            ou.operating_unit_name as ouName,
            0 as receiveCount,
            0 as storageCount,
            0 as returnCount,
            0 as actualStorageCount,
            null as earliestStorageDate,
            o.promised_date as promisedDate,
            o.tracking_date as trackDate,
            od.publish_time as publishTime,
            2 as 'type',
            od.create_at as orderDetailCreateAt,
            lui.name as orderDetailCreateByName,
            od.delivery_address as deliveryAddress,
            od.consignee as consignee,
            od.contact_phone as contactPhone
        from
            pam_ctc.purchase_order_detail_change_history od
        left join pam_ctc.purchase_order o on
            od.purchase_order_id = o.id
        left join pam_ctc.project p on
            od.project_id = p.id
        left join pam_basedata.operating_unit ou on
            p.ou_id = ou.id
        left join pam_basedata.ltc_user_info lui on
            lui.id = od.create_by
        where
            od.deleted_flag = 0
            and od.history_type = 1
            and od.status = 1
            and od.change_status = 0
            and od.origin_id is null
            and (o.source != '2' or o.source is null)
            <include refid="queryCondition" />
            <if test="me">
                and ( o.buyer_id = #{userId}
                or od.create_by = #{userId}
                or p.manager_id = #{userId} )
            </if>
            <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
                and od.`project_name` like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
            </if>
            <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
                and od.`project_num` like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
            </if>
        )
        select * from k
        where 1 = 1
        <if test="typeList != null and typeList.length > 0">
            AND k.type in
            <foreach collection="typeList" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        ORDER BY
            k.createAt DESC,
            k.erpCode ASC
    </select>

    <select id="selectPurchaseProgressByOrderId" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseProgressDTO">
        select
            pp.`transaction_type` transactionType,
            pp.`transaction_quantity` transactionQuantity,
            pp.`item_code` itemCode,
            pp.`po_number` poNumber,
            od.`purchase_order_id` purchaseOrderId,
            od.id purchaseOrderDetailId,
            od.`line_number` as lineNumber
        from
            pam_ctc.`purchase_progress` pp
        inner join pam_ctc.`purchase_order_detail` od on
            od.`erp_code` = pp.`item_code`
            and pp.`po_line_no` = od.`line_number`
            and od.deleted_flag = 0
        inner join pam_ctc.purchase_order po on
            po.id = od.purchase_order_id
            and po.num = pp.po_number
        inner join pam_ctc.project p on
            p.id = od.project_id
            and p.ou_id = pp.ou_id
        where po.id in
        <foreach collection="orderIdList" item="orderId" index="index" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        <if test="ouIdList != null">
            and pp.ou_id in
            <foreach collection="ouIdList" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>
    </select>

    <select id="selectPurchaseOrderRecordWbs" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        with k as
        (
        select
            od.id,
            od.merge_rows as mergeRows,
            o.erp_order_status as 'erpOrderStatus',
            od.material_purchase_requirement_id as materialPurchaseRequirementId,
            od.purchase_order_id as purchaseOrderId,
            od.line_number as lineNumber,
            od.project_id as projectId,
            od.erp_code as erpCode,
            od.pam_code as pamCode,
            od.delivery_time as deliveryTime,
            od.materiel_id as materielId,
            od.materiel_descr as materielDescr,
            od.unit_code as unitCode,
            o.currency as currencyCode,
            o.conversion_type as conversionType,
            o.conversion_rate as conversionRate,
            od.cost as cost,
            od.unit as unit,
            o.vendor_asl_id as vendorAslId,
            o.vendor_name as vendorName,
            o.vendor_site_code as vendorSiteCode,
            od.order_num as orderNum,
            IFNULL(od.quantity, 0) as quantity,
            IFNULL(od.cancel_num, 0) as cancelNum,
            od.status,
            od.deleted_flag as deletedFlag,
            od.create_by as createBy,
            o.create_at as createAt,
            od.update_by as updateBy,
            od.update_at as updateAt,
            o.num as num,
            o.order_status as orderStatus,
            p.name as projectName,
            p.code as projectCode,
            p.ou_id as ouId,
            o.vendor_num as vendorCode,
            o.buyer_name as buyer,
            o.sync_status as syncStatus,
            ou.operating_unit_name as ouName,
            t4.earliestStorageDate as earliestStorageDate ,
            od.contract_appoint_date as promisedDate,
            od.track_date as trackDate,
            od.chart_version as chartVersion,
            od.model,
            od.brand,
            case
            od.wbs_summary_code_star when '1' then concat('*',od.wbs_summary_code)
            else od.wbs_summary_code
            end wbsSummaryCode,
            o.dispatch_is as dispatchIs,
            od.unit_price as unitPrice,
            od.discount_price as discountPrice,
            od.discount,
            od.discount_money as discountMoney,
            o.tax_rate as taxRate,
            od.contract_appoint_date as contractAppointDate,
            od.publish_time as publishTime,
            od.project_wbs_receipts_id as projectWbsReceiptsId,
            case
            od.requirement_code_star when '1' then concat('*',od.requirement_code)
            else od.requirement_code
            end requirementCode,
            case
            od.design_release_lot_number_star when '1' then concat('*',od.design_release_lot_number)
            else od.design_release_lot_number
            end designReleaseLotNumber,
            o.pricing_type as pricingType,
            0 as storageCount,
            0 as returnCount,
            0 as rejectCount,
            0 as receiveCount,
            0 as acceptCount,
            0 as actualStorageCount,
            0 as 'type',
            if(o.order_status in (1,2,3,4,5,11), fi.update_at, null) as approvalTime
        from
            pam_ctc.purchase_order_detail od
        left join pam_ctc.purchase_order o on
            od.purchase_order_id = o.id
        left join pam_ctc.project p on
            od.project_id = p.id
        left join pam_system.form_instance fi on
            o.receipts_id = fi.form_instance_id
            and fi.form_url = 'purchaseOrderApp' and fi.deleted_flag = 0 and fi.wf_status = '30'
        left join pam_basedata.operating_unit ou on
            p.ou_id = ou.id
        left join (
            select
            pp.`item_code`,
            pp.`po_number`,
            MIN(pp.`transaction_date`) earliestStorageDate,
            od.`line_number`
            from
                pam_ctc.purchase_progress pp
            inner join pam_ctc.purchase_order po on
                po.num = pp.`po_number`
                and po.deleted_flag = 0
            left join pam_ctc.`purchase_order_detail` od on
                od.`purchase_order_id` = po.id
                and pp.`po_line_no` = od.`line_number`
                and od.deleted_flag = 0
            where
            pp.`transaction_type` = 'DELIVER'
            group by
            pp.`item_code`,
            pp.`po_number`,
            od.`purchase_order_id`,
            od.`line_number`) t4 on
        od.`erp_code` = t4.item_code
        and o.`num` = t4.po_number
        and t4.line_number = od.line_number
        where
        od.deleted_flag = 0
        and o.source = '2'
        <if test="wbsSummaryCode != null and wbsSummaryCode != ''">
            AND od.wbs_summary_code like concat('%', #{wbsSummaryCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="model != null and model != ''">
            AND od.model like concat('%', #{model, jdbcType=VARCHAR}, '%')
        </if>
        <if test="brand != null and brand != ''">
            AND od.brand like concat('%', #{brand, jdbcType=VARCHAR}, '%')
        </if>
        <if test="chartVersion != null and chartVersion != ''">
            AND od.chart_version like concat('%', #{chartVersion, jdbcType=VARCHAR}, '%')
        </if>
        <if test="designReleaseLotNumber != null and designReleaseLotNumber != ''">
            AND od.design_release_lot_number like concat('%', #{designReleaseLotNumber, jdbcType=VARCHAR}, '%')
        </if>
        <if test="requirementCode != null and requirementCode != ''">
            AND od.requirement_code like concat('%', #{requirementCode, jdbcType=VARCHAR}, '%')
        </if>
        <!-- 最新发布日期 -->
        <if test="publishStartTime != null">
            and od.publish_time <![CDATA[>= ]]> #{publishStartTime}
        </if>
        <if test="publishEndTime != null">
            and od.publish_time <![CDATA[<= ]]> #{publishEndTime}
        </if>
        <!-- 审批通过时间 -->
        <if test="approvalStartTime != null or approvalEndTime != null ">
            and o.order_status in (1,2,3,4,5,11)
        </if>
        <if test="approvalStartTime != null">
            and fi.update_at <![CDATA[>= ]]> #{approvalStartTime}
        </if>
        <if test="approvalEndTime != null">
            and fi.update_at <![CDATA[<= ]]> #{approvalEndTime}
        </if>
        <if test="statusStrList != null ">
        AND od.status in
        <foreach collection="statusStrList" item="status" index="index" open="(" separator="," close=")">
            #{status}
        </foreach>
        </if>
        <if test="isMerge != null">
            and od.merge_rows = #{isMerge}
        </if>
        <if test="pricingType != null">
            and o.pricing_type = #{pricingType}
        </if>
        <if test="me">
            and (
            o.buyer_id = #{userId}
            or p.manager_id =#{userId}
            or o.id in (
                select origin_id from pam_ctc.purchase_order_change_history poch
                inner join pam_ctc.purchase_order_change_record pocr on poch.record_id = pocr.id and pocr.deleted_flag = 0 and pocr.status = 2
                where poch.origin_id = od.purchase_order_id and poch.deleted_flag = 0 and poch.history_type = 1 )
            )
        </if>
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            and od.`project_name` like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            and od.`project_num` like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyVendorSiteCode != null and fuzzyVendorSiteCode != ''">
            and o.`vendor_site_code` like concat('%', #{fuzzyVendorSiteCode, jdbcType=CHAR}, '%')
        </if>
        <if test="modelParam != null and modelParam != ''">
            and (od.model like concat('%', #{modelParam}, '%')
            or od.figure_number like concat('%', #{modelParam}, '%'))
        </if>
        <include refid = "queryCondition" />
        union all
        select
            od.id,
            od.merge_rows as mergeRows,
            o.erp_order_status as 'erpOrderStatus',
            od.material_purchase_requirement_id as materialPurchaseRequirementId,
            od.purchase_order_id as purchaseOrderId,
            od.line_number as lineNumber,
            od.project_id as projectId,
            od.erp_code as erpCode,
            od.pam_code as pamCode,
            od.delivery_time as deliveryTime,
            od.materiel_id as materielId,
            od.materiel_descr as materielDescr,
            od.unit_code as unitCode,
            o.currency as currencyCode,
            o.conversion_type as conversionType,
            o.conversion_rate as conversionRate,
            od.cost as cost,
            od.unit as unit,
            o.vendor_asl_id as vendorAslId,
            o.vendor_name as vendorName,
            o.vendor_site_code as vendorSiteCode,
            od.order_num as orderNum,
            IFNULL(od.quantity, 0) as quantity,
            IFNULL(od.cancel_num, 0) as cancelNum,
            od.status,
            od.deleted_flag as deletedFlag,
            od.create_by as createBy,
            o.create_at as createAt,
            od.update_by as updateBy,
            od.update_at as updateAt,
            o.num as num,
            o.order_status as orderStatus,
            p.name as projectName,
            p.code as projectCode,
            p.ou_id as ouId,
            o.vendor_num as vendorCode,
            o.buyer_name as buyer,
            o.sync_status as syncStatus,
            ou.operating_unit_name as ouName,
            null as earliestStorageDate ,
            o.promised_date as promisedDate,
            o.tracking_date as trackDate,
            od.chart_version as chartVersion,
            od.model,
            od.brand,
            case
            od.wbs_summary_code_star when '1' then concat('*', od.wbs_summary_code)
            else od.wbs_summary_code
            end wbsSummaryCode,
            o.dispatch_is as dispatchIs,
            od.unit_price as unitPrice,
            od.discount_price as discountPrice,
            od.discount,
            od.discount_money as discountMoney,
            o.tax_rate as taxRate,
            od.contract_appoint_date as contractAppointDate,
            od.publish_time as publishTime,
            od.project_wbs_receipts_id as projectWbsReceiptsId,
            case
            od.requirement_code_star when '1' then concat('*', od.requirement_code)
            else od.requirement_code
            end requirementCode,
            case
            od.design_release_lot_number_star when '1' then concat('*', od.design_release_lot_number)
            else od.design_release_lot_number
            end designReleaseLotNumber,
            o.pricing_type as pricingType,
            0 as storageCount,
            0 as returnCount,
            0 as rejectCount,
            0 as receiveCount,
            0 as acceptCount,
            0 as actualStorageCount,
            3 as 'type',
            if(o.order_status in (1,2,3,4,5,11), fi.update_at, null) as approvalTime
        from
            pam_ctc.purchase_order_detail_change_history od
        left join pam_ctc.purchase_order_change_history oh on
            od.purchase_order_id = oh.id
        left join pam_ctc.purchase_order o on
            oh.origin_id = o.id
        left join pam_ctc.project p on
            od.project_id = p.id
        left join pam_system.form_instance fi on
            o.receipts_id = fi.form_instance_id
            and fi.form_url = 'purchaseOrderApp' and fi.deleted_flag = 0 and fi.wf_status = '30'
        left join pam_basedata.operating_unit ou on
            p.ou_id = ou.id
        left join pam_ctc.purchase_order_change_record r on
            od.record_id = r.id
        where
        od.deleted_flag = 0
        and od.history_type = 1
        and od.status = 1
        and od.origin_id is null
        and r.status = 2
        and o.source = '2'
        <if test="wbsSummaryCode != null and wbsSummaryCode != ''">
            AND od.wbs_summary_code like concat('%', #{wbsSummaryCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="model != null and model != ''">
            AND od.model like concat('%', #{model, jdbcType=VARCHAR}, '%')
        </if>
        <if test="brand != null and brand != ''">
            AND od.brand like concat('%', #{brand, jdbcType=VARCHAR}, '%')
        </if>
        <if test="chartVersion != null and chartVersion != ''">
            AND od.chart_version like concat('%', #{chartVersion, jdbcType=VARCHAR}, '%')
        </if>
        <if test="designReleaseLotNumber != null and designReleaseLotNumber != ''">
            AND od.design_release_lot_number like concat('%', #{designReleaseLotNumber, jdbcType=VARCHAR}, '%')
        </if>
        <if test="requirementCode != null and requirementCode != ''">
            AND od.requirement_code like concat('%', #{requirementCode, jdbcType=VARCHAR}, '%')
        </if>
        <!-- 最新发布日期 -->
        <if test="publishStartTime != null">
            and od.publish_time <![CDATA[>= ]]> #{publishStartTime}
        </if>
        <if test="publishEndTime != null">
            and od.publish_time <![CDATA[<= ]]> #{publishEndTime}
        </if>
        <!-- 审批通过时间 -->
        <if test="approvalStartTime != null or approvalEndTime != null ">
            and o.order_status in (1,2,3,4,5,11)
        </if>
        <if test="approvalStartTime != null">
            and fi.update_at <![CDATA[>= ]]> #{approvalStartTime}
        </if>
        <if test="approvalEndTime != null">
            and fi.update_at <![CDATA[<= ]]> #{approvalEndTime}
        </if>
        <if test="statusStrList != null ">
            AND od.status in
            <foreach collection="statusStrList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="isMerge != null">
            and od.merge_rows = #{isMerge}
        </if>
        <if test="pricingType != null">
            and o.pricing_type = #{pricingType}
        </if>
        <if test="me">
            and (o.buyer_id = #{userId}
            or od.create_by = #{userId}
            or p.manager_id =#{userId})
        </if>
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            and od.`project_name` like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            and od.`project_num` like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyVendorSiteCode != null and fuzzyVendorSiteCode != ''">
            and o.`vendor_site_code` like concat('%', #{fuzzyVendorSiteCode, jdbcType=CHAR}, '%')
        </if>
        <if test="modelParam != null and modelParam != ''">
            and (od.model like concat('%', #{modelParam}, '%')
            or od.figure_number like concat('%', #{modelParam}, '%'))
        </if>
        <include refid = "queryCondition" />
        )
        select * from k
        where 1 = 1
        <if test="typeList != null and typeList.length > 0">
            AND k.type in
            <foreach collection="typeList" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        order by k.createAt desc
    </select>

    <select id="selectPurchaseOrderRecordWbs1" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        <include refid="selectNormalWbsOrderDetailFull"/>
        order by createAt desc
    </select>

    <select id="selectPurchaseOrderRecordWbs2" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        <include refid="selectHistoryWbsOrderDetailFull"/>
        order by createAt desc
    </select>

    <select id="selectPurchaseProgress" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        select
        pp.`item_code` erpCode,
        pp.`po_number` num,
        MIN(pp.`transaction_date`) earliestStorageDate,
        od.`line_number` lineNumber
        from
        pam_ctc.purchase_progress pp
        inner join pam_ctc.purchase_order po on
        po.num = pp.`po_number`
        and po.deleted_flag = 0
        left join pam_ctc.`purchase_order_detail` od on
        od.`purchase_order_id` = po.id
        and pp.`po_line_no` = od.`line_number`
        and od.deleted_flag = 0
        where
        pp.`transaction_type` = 'DELIVER'
        <if test="erpCodeList != null and erpCodeList.size() > 0">
            AND pp.`item_code` in
            <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                #{erpCode}
            </foreach>
        </if>
        <if test="numList != null and numList.size() > 0">
            AND pp.`po_number` in
            <foreach collection="numList" item="num" index="index" open="(" separator="," close=")">
                #{num}
            </foreach>
        </if>
        <if test="lineNumberList != null and lineNumberList.size() > 0">
            AND od.line_number in
            <foreach collection="lineNumberList" item="lineNumber" index="index" open="(" separator="," close=")">
                #{lineNumber}
            </foreach>
        </if>
        group by
        pp.`item_code`,
        pp.`po_number`,
        od.`purchase_order_id`,
        od.`line_number`
    </select>

    <select id="getPortalRemindCount" resultType="java.lang.Long">
        select
            count(od.id)
        from
            pam_ctc.purchase_order_detail od
        inner join pam_ctc.purchase_order o on
            od.purchase_order_id = o.id
            and o.deleted_flag = 0
        left join pam_ctc.`purchase_progress` pp on
            od.`erp_code` = pp.item_code
            and o.`num` = pp.po_number
            and od.`line_number` = pp.`po_line_no`
            and pp.`transaction_type` in ('DELIVER', 'RETURN TO RECEIVING')
            <if test="ouList != null">
                and pp.ou_id in
                <foreach collection="ouList" item="ouId" index="index" open="(" separator="," close=")">
                    #{ouId}
                </foreach>
            </if>
        where
            od.deleted_flag = 0
            and o.order_status in (1, 11)
            and pp.id is null
            <if test="ouList != null">
                and o.ou_id in
                <foreach collection="ouList" item="ouId" index="index" open="(" separator="," close=")">
                    #{ouId}
                </foreach>
            </if>
    </select>

    <select id="getPortalRemindExistCount" resultType="java.lang.Long">
        select
            count(t.id)
        from
            (
            select
                pp.id,
                IFNULL(SUM(if(pp.`transaction_type` = 'DELIVER', pp.`transaction_quantity`, 0)), 0) storage_count,
                IFNULL(SUM(if(pp.`transaction_type` = 'RETURN TO RECEIVING', pp.`transaction_quantity`, 0)), 0) return_count
            from
                pam_ctc.`purchase_progress` pp
            inner join pam_ctc.purchase_order o on
                o.`num` = pp.po_number
            where
                pp.`transaction_type` in ('DELIVER', 'RETURN TO RECEIVING')
                <if test="ouList != null">
                    and pp.ou_id in
                    <foreach collection="ouList" item="ouId" index="index" open="(" separator="," close=")">
                        #{ouId}
                    </foreach>
                </if>
                and o.deleted_flag = 0
                and o.order_status in (1, 11)
                <if test="ouList != null">
                    and o.ou_id in
                    <foreach collection="ouList" item="ouId" index="index" open="(" separator="," close=")">
                        #{ouId}
                    </foreach>
                </if>
            group by
                pp.item_code,
                pp.po_number,
                pp.`po_line_no`
            having
                storage_count-return_count = 0) t
    </select>

    <select id="getErrorPurchaseOrderDetailCount"  resultType="java.lang.Long">
        select
        	COUNT(distinct pod.purchase_order_id)
        from
        	(
        	select
        		purchase_order_id
        	from
        		pam_ctc.purchase_order_detail
        	where
        		deleted_flag = 0
        ) pod
        where
        	not exists (
        	select
        		1
        	from
        		pam_ctc.purchase_order po
        	where
        		po.id = pod.purchase_order_id
        );

    </select>

    <select id="selectErrorPurchaseOrderDetailPage"  resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
    SELECT pod.purchase_order_id AS purchaseOrderId, pod.erp_code AS erpCode, pod.pam_code AS pamCode,
     pod.vendor_name as vendorName, pod.vendor_num as vendorNum, pod.vendor_site_code AS vendorSiteCode,
     pod.project_num AS projectNum, pod.create_at AS createAt, pod.update_at AS updateAt
    FROM pam_ctc.purchase_order_detail pod WHERE pod.purchase_order_id NOT IN
    (SELECT id FROM pam_ctc.purchase_order) AND pod.deleted_flag=0 GROUP BY pod.purchase_order_id
    </select>

    <select id="selectAllList" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        select
        od.id,
        od.material_purchase_requirement_id as materialPurchaseRequirementId,
        od.purchase_order_id as purchaseOrderId,
        od.line_number as lineNumber,
        od.project_id as projectId,
        od.erp_code as erpCode,
        od.pam_code as pamCode,
        od.delivery_time as deliveryTime,
        od.materiel_id as materielId,
        od.materiel_descr as materielDescr,
        od.unit_code as unitCode,
        od.unit as unit,
        od.vendor_asl_id as vendorAslId,
        od.vendor_name as vendorName,
        od.vendor_site_code as vendorSiteCode,
        ROUND(od.order_num) as orderNum,
        od.cancel_num as cancelNum,
        od.status,
        od.deleted_flag as deletedFlag,
        od.create_by as createBy,
        o.create_at as createAt,
        od.update_by as updateBy,
        od.update_at as updateAt,
        o.num as num,
        o.order_status as orderStatus,
        o.buyer_name as buyer,
        o.sync_status as syncStatus
        from  pam_ctc.purchase_order o
        join pam_ctc.purchase_order_detail od on od.purchase_order_id = o.id
        where
        (o.deleted_flag = 0 or o.deleted_flag is null) and (od.deleted_flag = 0 or od.deleted_flag is null)
        <if test="materialPurchaseRequirementIdList != null and materialPurchaseRequirementIdList.size() > 0">
            AND od.material_purchase_requirement_id in
            <foreach collection="materialPurchaseRequirementIdList" item="materialPurchaseRequirementId" index="index" open="(" separator="," close=")">
                #{materialPurchaseRequirementId}
            </foreach>
        </if>
    </select>
    <select id="selectPurchaseProgressInfo" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        select
            IFNULL(sum(if(pp.`transaction_type` = 'RECEIVE', pp.`transaction_quantity`, 0)), 0) receiveCount,
            IFNULL(sum(if(pp.`transaction_type` = 'DELIVER', pp.`transaction_quantity`, 0)), 0) storageCount,
            IFNULL(sum(if(pp.`transaction_type` = 'RETURN TO RECEIVING', pp.`transaction_quantity`, 0)), 0) returnCount,
            MIN(pp.`transaction_date`) earliestStorageDate,
            pp.`item_code` as erpCode,
            pp.`po_number` as num,
            pp.`po_line_no` as lineNumber
        from
            pam_ctc.`purchase_progress` pp
        where
            pp.`transaction_type` in ('RECEIVE', 'DELIVER', 'RETURN TO RECEIVING')
            and (pp.deleted_flag is null or pp.deleted_flag = 0)
            <if test="ouList != null">
                and pp.ou_id in
                <foreach collection="ouList" item="ouId" index="index" open="(" separator="," close=")">
                    #{ouId}
                </foreach>
            </if>
            <if test="numList != null">
                and pp.po_number in
                <foreach collection="numList" item="num" index="index" open="(" separator="," close=")">
                    #{num}
                </foreach>
            </if>
        group by
            pp.`item_code`,
            pp.`po_number`,
            pp.`po_line_no`
    </select>

    <select id="selectPurchaseOrderRecordWbsMultiOrderType"  resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto">
        <include refid="selectNormalWbsOrderDetailFull"/>
        UNION ALL
        <include refid="selectHistoryWbsOrderDetailFull"/>
        order by createAt desc
    </select>


    <!-- WBS已创建订单查询片段 -->
    <sql id="selectNormalWbsOrderDetailFull">
        select
        od.id,
        od.merge_rows as mergeRows,
        o.erp_order_status as 'erpOrderStatus',
        od.material_purchase_requirement_id as materialPurchaseRequirementId,
        od.purchase_order_id as purchaseOrderId,
        od.line_number as lineNumber,
        od.project_id as projectId,
        od.erp_code as erpCode,
        od.pam_code as pamCode,
        od.delivery_time as deliveryTime,
        od.materiel_id as materielId,
        od.materiel_descr as materielDescr,
        od.unit_code as unitCode,
        o.currency as currencyCode,
        o.conversion_type as conversionType,
        o.conversion_rate as conversionRate,
        od.cost as cost,
        od.unit as unit,
        o.vendor_asl_id as vendorAslId,
        o.vendor_name as vendorName,
        o.vendor_site_code as vendorSiteCode,
        od.order_num as orderNum,
        IFNULL(od.quantity, 0) as quantity,
        IFNULL(od.cancel_num, 0) as cancelNum,
        od.status,
        od.deleted_flag as deletedFlag,
        od.create_by as createBy,
        o.create_at as createAt,
        od.update_by as updateBy,
        od.update_at as updateAt,
        o.num as num,
        o.order_status as orderStatus,
        p.name as projectName,
        p.code as projectCode,
        p.ou_id as ouId,
        o.vendor_num as vendorCode,
        o.buyer_name as buyer,
        o.sync_status as syncStatus,
        ou.operating_unit_name as ouName,
        null as earliestStorageDate,
        od.contract_appoint_date as promisedDate,
        od.track_date as trackDate,
        od.chart_version as chartVersion,
        od.model,
        od.brand,
        case
        od.wbs_summary_code_star when '1' then concat('*',od.wbs_summary_code)
        else od.wbs_summary_code
        end wbsSummaryCode,
        o.dispatch_is as dispatchIs,
        od.unit_price as unitPrice,
        od.discount_price as discountPrice,
        od.discount,
        od.discount_money as discountMoney,
        o.tax_rate as taxRate,
        od.contract_appoint_date as contractAppointDate,
        od.publish_time as publishTime,
        od.project_wbs_receipts_id as projectWbsReceiptsId,
        case
        od.requirement_code_star when '1' then concat('*',od.requirement_code)
        else od.requirement_code
        end requirementCode,
        case
        od.design_release_lot_number_star when '1' then concat('*',od.design_release_lot_number)
        else od.design_release_lot_number
        end designReleaseLotNumber,
        o.pricing_type as pricingType,
        0 as storageCount,
        0 as returnCount,
        0 as rejectCount,
        0 as receiveCount,
        0 as acceptCount,
        0 as actualStorageCount,
        0 as 'type',
        o.approval_time as approvalTime
        from
        pam_ctc.purchase_order_detail od
        left join pam_ctc.purchase_order o on od.purchase_order_id = o.id
        left join pam_ctc.project p on od.project_id = p.id
        left join pam_basedata.operating_unit ou on p.ou_id = ou.id
        where
        od.deleted_flag = 0
        and o.source = '2'
        <if test="wbsSummaryCode != null and wbsSummaryCode != ''">
            AND od.wbs_summary_code like concat('%', #{wbsSummaryCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="model != null and model != ''">
            AND od.model like concat('%', #{model, jdbcType=VARCHAR}, '%')
        </if>
        <if test="brand != null and brand != ''">
            AND od.brand like concat('%', #{brand, jdbcType=VARCHAR}, '%')
        </if>
        <if test="chartVersion != null and chartVersion != ''">
            AND od.chart_version like concat('%', #{chartVersion, jdbcType=VARCHAR}, '%')
        </if>
        <if test="designReleaseLotNumber != null and designReleaseLotNumber != ''">
            AND od.design_release_lot_number like concat('%', #{designReleaseLotNumber, jdbcType=VARCHAR}, '%')
        </if>
        <if test="requirementCode != null and requirementCode != ''">
            AND od.requirement_code like concat('%', #{requirementCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="publishStartTime != null">
            and od.publish_time <![CDATA[>= ]]> #{publishStartTime}
        </if>
        <if test="publishEndTime != null">
            and od.publish_time <![CDATA[<= ]]> #{publishEndTime}
        </if>
        <if test="approvalStartTime != null or approvalEndTime != null">
            and o.order_status in (1,2,3,4,5,11)
        </if>
        <if test="approvalStartTime != null">
            and o.approval_time <![CDATA[>= ]]> #{approvalStartTime}
        </if>
        <if test="approvalEndTime != null">
            and o.approval_time <![CDATA[<= ]]> #{approvalEndTime}
        </if>
        <if test="statusStrList != null">
            AND od.status in
            <foreach collection="statusStrList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="isMerge != null">
            and od.merge_rows = #{isMerge}
        </if>
        <if test="pricingType != null">
            and o.pricing_type = #{pricingType}
        </if>
        <if test="me">
            and (o.buyer_id = #{userId} or p.manager_id =#{userId})
        </if>
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            and od.`project_name` like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            and od.`project_num` like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyVendorSiteCode != null and fuzzyVendorSiteCode != ''">
            and o.`vendor_site_code` like concat('%', #{fuzzyVendorSiteCode, jdbcType=CHAR}, '%')
        </if>
        <if test="modelParam != null and modelParam != ''">
            and (od.model like concat('%', #{modelParam}, '%')
            or od.figure_number like concat('%', #{modelParam}, '%'))
        </if>
        <include refid="queryCondition"/>
    </sql>

    <!-- WBS创建中订单查询片段 -->
    <sql id="selectHistoryWbsOrderDetailFull">
        select
        od.id,
        od.merge_rows as mergeRows,
        o.erp_order_status as 'erpOrderStatus',
        od.material_purchase_requirement_id as materialPurchaseRequirementId,
        od.purchase_order_id as purchaseOrderId,
        od.line_number as lineNumber,
        od.project_id as projectId,
        od.erp_code as erpCode,
        od.pam_code as pamCode,
        od.delivery_time as deliveryTime,
        od.materiel_id as materielId,
        od.materiel_descr as materielDescr,
        od.unit_code as unitCode,
        o.currency as currencyCode,
        o.conversion_type as conversionType,
        o.conversion_rate as conversionRate,
        od.cost as cost,
        od.unit as unit,
        o.vendor_asl_id as vendorAslId,
        o.vendor_name as vendorName,
        o.vendor_site_code as vendorSiteCode,
        od.order_num as orderNum,
        IFNULL(od.quantity, 0) as quantity,
        IFNULL(od.cancel_num, 0) as cancelNum,
        od.status,
        od.deleted_flag as deletedFlag,
        od.create_by as createBy,
        o.create_at as createAt,
        od.update_by as updateBy,
        od.update_at as updateAt,
        o.num as num,
        o.order_status as orderStatus,
        p.name as projectName,
        p.code as projectCode,
        p.ou_id as ouId,
        o.vendor_num as vendorCode,
        o.buyer_name as buyer,
        o.sync_status as syncStatus,
        ou.operating_unit_name as ouName,
        null as earliestStorageDate,
        o.promised_date as promisedDate,
        o.tracking_date as trackDate,
        od.chart_version as chartVersion,
        od.model,
        od.brand,
        case
        od.wbs_summary_code_star when '1' then concat('*', od.wbs_summary_code)
        else od.wbs_summary_code
        end wbsSummaryCode,
        o.dispatch_is as dispatchIs,
        od.unit_price as unitPrice,
        od.discount_price as discountPrice,
        od.discount,
        od.discount_money as discountMoney,
        o.tax_rate as taxRate,
        od.contract_appoint_date as contractAppointDate,
        od.publish_time as publishTime,
        od.project_wbs_receipts_id as projectWbsReceiptsId,
        case
        od.requirement_code_star when '1' then concat('*', od.requirement_code)
        else od.requirement_code
        end requirementCode,
        case
        od.design_release_lot_number_star when '1' then concat('*', od.design_release_lot_number)
        else od.design_release_lot_number
        end designReleaseLotNumber,
        o.pricing_type as pricingType,
        0 as storageCount,
        0 as returnCount,
        0 as rejectCount,
        0 as receiveCount,
        0 as acceptCount,
        0 as actualStorageCount,
        3 as 'type',
        o.approval_time as approvalTime
        from
        pam_ctc.purchase_order_detail_change_history od
        left join pam_ctc.purchase_order_change_history oh on od.purchase_order_id = oh.id
        left join pam_ctc.purchase_order o on oh.origin_id = o.id
        left join pam_ctc.project p on od.project_id = p.id
        left join pam_basedata.operating_unit ou on p.ou_id = ou.id
        left join pam_ctc.purchase_order_change_record r on od.record_id = r.id
        where
        od.deleted_flag = 0
        and od.history_type = 1
        and od.status = 1
        and od.origin_id is null
        and r.status = 2
        and o.source = '2'
        <if test="wbsSummaryCode != null and wbsSummaryCode != ''">
            AND od.wbs_summary_code like concat('%', #{wbsSummaryCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="model != null and model != ''">
            AND od.model like concat('%', #{model, jdbcType=VARCHAR}, '%')
        </if>
        <if test="brand != null and brand != ''">
            AND od.brand like concat('%', #{brand, jdbcType=VARCHAR}, '%')
        </if>
        <if test="chartVersion != null and chartVersion != ''">
            AND od.chart_version like concat('%', #{chartVersion, jdbcType=VARCHAR}, '%')
        </if>
        <if test="designReleaseLotNumber != null and designReleaseLotNumber != ''">
            AND od.design_release_lot_number like concat('%', #{designReleaseLotNumber, jdbcType=VARCHAR}, '%')
        </if>
        <if test="requirementCode != null and requirementCode != ''">
            AND od.requirement_code like concat('%', #{requirementCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="publishStartTime != null">
            and od.publish_time <![CDATA[>= ]]> #{publishStartTime}
        </if>
        <if test="publishEndTime != null">
            and od.publish_time <![CDATA[<= ]]> #{publishEndTime}
        </if>
        <if test="approvalStartTime != null or approvalEndTime != null">
            and o.order_status in (1,2,3,4,5,11)
        </if>
        <if test="approvalStartTime != null">
            and o.approval_time <![CDATA[>= ]]> #{approvalStartTime}
        </if>
        <if test="approvalEndTime != null">
            and o.approval_time <![CDATA[<= ]]> #{approvalEndTime}
        </if>
        <if test="statusStrList != null">
            AND od.status in
            <foreach collection="statusStrList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="isMerge != null">
            and od.merge_rows = #{isMerge}
        </if>
        <if test="pricingType != null">
            and o.pricing_type = #{pricingType}
        </if>
        <if test="me">
            and (o.buyer_id = #{userId} or od.create_by = #{userId} or p.manager_id =#{userId})
        </if>
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            and od.`project_name` like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            and od.`project_num` like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>
        <if test="fuzzyVendorSiteCode != null and fuzzyVendorSiteCode != ''">
            and o.`vendor_site_code` like concat('%', #{fuzzyVendorSiteCode, jdbcType=CHAR}, '%')
        </if>
        <if test="modelParam != null and modelParam != ''">
            and (od.model like concat('%', #{modelParam}, '%')
            or od.figure_number like concat('%', #{modelParam}, '%'))
        </if>
        <include refid="queryCondition"/>
    </sql>
</mapper>
