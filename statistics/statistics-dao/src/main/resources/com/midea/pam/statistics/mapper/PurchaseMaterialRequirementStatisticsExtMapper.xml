<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.PurchaseMaterialRequirementStatisticsExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
        <result column="materiel_id" jdbcType="BIGINT" property="materielId" />
        <result column="materiel_descr" jdbcType="VARCHAR" property="materielDescr" />
        <result column="need_total" jdbcType="DECIMAL" property="needTotal" />
        <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="approved_supplier_number" jdbcType="INTEGER" property="approvedSupplierNumber" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="currency" jdbcType="VARCHAR" property="currency" />
        <result column="conversion_type" jdbcType="VARCHAR" property="conversionType" />
        <result column="conversion_date" jdbcType="TIMESTAMP" property="conversionDate" />
        <result column="conversion_rate" jdbcType="DECIMAL" property="conversionRate" />
        <result column="purchase_type" jdbcType="INTEGER" property="purchaseType" />
        <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId" />
        <result column="requirement_code" jdbcType="VARCHAR" property="requirementCode" />
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
        <result column="dispatch_is" jdbcType="TINYINT" property="dispatchIs" />
        <result column="closed_amount" jdbcType="DECIMAL" property="closedAmount" />
        <result column="closed_quantity" jdbcType="DECIMAL" property="closedQuantity" />
        <result column="wbs_demand_os_cost" jdbcType="DECIMAL" property="wbsDemandOsCost" />
        <result column="receipts_publish_date" jdbcType="TIMESTAMP" property="receiptsPublishDate" />
        <result column="has_amount" jdbcType="INTEGER" property="hasAmount" />
        <result column="due_amount" jdbcType="INTEGER" property="dueAmount" />
        <result column="design_release_lot_number" jdbcType="VARCHAR" property="designReleaseLotNumber" />
        <result column="figure_number" jdbcType="VARCHAR" property="figureNumber" />
        <result column="chart_version" jdbcType="VARCHAR" property="chartVersion" />
        <result column="init" jdbcType="TINYINT" property="init" />
        <result column="init_sequence" jdbcType="VARCHAR" property="initSequence" />
        <result column="requirement_type" jdbcType="TINYINT" property="requirementType" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="New_Base_Column_List">
    id, project_id, erp_code, pam_code, delivery_time, materiel_id, materiel_descr, need_total,
    unit_code, unit, approved_supplier_number, status, reason, deleted_flag, create_by,
    create_at, update_by, update_at, currency, conversion_type, conversion_date, conversion_rate,
    purchase_type, project_wbs_receipts_id, requirement_code, wbs_summary_code, activity_code,
    dispatch_is, closed_amount, closed_quantity, wbs_demand_os_cost, receipts_publish_date,
    has_amount, due_amount, design_release_lot_number, figure_number, chart_version,
    init, init_sequence, requirement_type
  </sql>

    <sql id="Base_Column_List">
        requirement.id,
        requirement.project_id as projectId,
        requirement.erp_code as erpCode,
        requirement.pam_code as pamCode,
        requirement.delivery_time as deliveryTime,
        requirement.materiel_id as materielId,
        requirement.materiel_descr as materielDescr,
        requirement.need_total as needTotal,
        requirement.unit_code as unitCode,
        requirement.unit,
        requirement.approved_supplier_number as approvedSupplierNumber,
        requirement.status,
        requirement.reason,
        requirement.deleted_flag as deletedFlag,
        requirement.create_by as createBy,
        requirement.create_at as createAt,
        requirement.update_by as updateBy,
        requirement.update_at as updateAt,
        requirement.currency,
        requirement.conversion_type as conversionType,
        requirement.conversion_date as conversionDate,
        requirement.conversion_rate as conversionRate,
        requirement.purchase_type as purchaseType,
        requirement.project_wbs_receipts_id as projectWbsReceiptsId,
        requirement.requirement_code as requirementCode,
        requirement.wbs_summary_code as wbsSummaryCode,
        requirement.activity_code as activityCode,
        requirement.dispatch_is as dispatchIs,
        requirement.closed_amount as closedAmount,
        requirement.wbs_demand_os_cost as wbsDemandOsCost,
        requirement.receipts_publish_date as receiptsPublishDate,
        requirement.design_release_lot_number as designReleaseLotNumber,
        requirement.requirement_type as requirementType,
        requirement.freeze_flag as freezeFlag,
        requirement.freeze_reason as freezeReason,
        requirement.freeze_by_name as freezeByName,
        requirement.freeze_at as freezeAt,
    </sql>

    <sql id="queryCondition">
        <!-- 模糊物料编码-->
        <if test="fuzzyErpCode != null and fuzzyErpCode != ''">
            AND requirement.erp_code like concat('%', #{fuzzyErpCode, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊物料描述-->
        <if test="fuzzyMaterielDescr != null and fuzzyMaterielDescr != ''">
            AND requirement.materiel_descr like concat('%', #{fuzzyMaterielDescr, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊PAM编码-->
        <if test="fuzzyPamCode != null and fuzzyPamCode != ''">
            AND requirement.pam_code like concat('%', #{fuzzyPamCode, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊项目名称-->
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            AND project.name like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>

        <!-- 模糊项目编号-->
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            AND project.code like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>

        <!-- 项目业务实体-->
        <if test="manyProjectOuId != null">
            AND ou.id in
            <foreach collection="manyProjectOuId" item="projectOuId" index="index" open="(" separator="," close=")">
                #{projectOuId}
            </foreach>
        </if>

        <!-- 物料需求项目id-->
        <if test="projectId != null">
            AND requirement.project_id = #{projectId, jdbcType=BIGINT}
        </if>

        <!-- 物料需求物料erp编码-->
        <if test="erpCode != null">
            AND requirement.erp_code = #{erpCode, jdbcType=VARCHAR}
        </if>

        <!-- 物料需求交付时间-->
        <if test="deliveryTime != null">
            and date_format(requirement.delivery_time,'%Y-%m-%d') = #{deliveryTime}
        </if>

        <!-- 物料需求id-->
        <if test="id != null">
            AND requirement.id = #{id, jdbcType=BIGINT}
        </if>

        <if test="approvedSupplierNumber != null">
            and (requirement.approved_supplier_number = 0 or requirement.approved_supplier_number is null)
        </if>

        <if test="manyStatus != null">
            AND requirement.status in
            <foreach collection="manyStatus" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <if test="requirementTypeList != null and requirementTypeList.size() > 0">
            and requirement.requirement_type in
            <foreach collection="requirementTypeList" item="requirementType" open="(" separator="," close=")">
                #{requirementType}
            </foreach>
        </if>

        <if test="deliveryStartTime != null">
            and requirement.delivery_time <![CDATA[>= ]]> #{deliveryStartTime}
        </if>
        <if test="deliveryEndTime != null">
            and requirement.delivery_time <![CDATA[<= ]]> #{deliveryEndTime}
        </if>
        <if test="ouList != null">
            and project.ou_id in
            <foreach collection="ouList" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>
        <if test="idList != null and idList.size() > 0">
            and requirement.id in
            <foreach collection="idList" item="id"  index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="deliveryAddress!=null and deliveryAddress!=''">
            and requirement.delivery_address like concat('%', #{deliveryAddress, jdbcType=VARCHAR}, '%')
        </if>
        <if test="consignee!=null and consignee!=''">
            and requirement.consignee like concat('%', #{consignee}, '%')
        </if>
        <if test="contactPhone!=null and contactPhone!=''">
            and requirement.contact_phone like concat('%', #{contactPhone}, '%')
        </if>
    </sql>

    <select id="getByProCodeAndOrgIdAndMaIdAndType" resultType="com.midea.pam.common.basedata.entity.Storage">
        select
            storage.id,
            storage.organization_id as organizationId,
            storage.subinventory_code as subinventoryCode,
            storage.subinventory_description as subinventoryDescription,
            storage.locator,
            storage.locator_description as locatorDescription,
            storage.segment1,
            storage.description,
            storage.primary_unit_of_measure as primaryUnitOfMeasure,
            storage.inventory_item_status_code as inventoryItemStatusCode,
            storage.transaction_quantity as transactionQuantity,
            storage.deleted_flag as deletedFlag,
            storage.create_at as createAt,
            storage.create_by as createBy,
            storage.update_at as updateAt,
            storage.update_by as updateBy
        from pam_basedata.storage storage
        left join pam_basedata.storage_inventory storageInventory on storage.subinventory_code = storageInventory.secondary_inventory_name
        where
        1=1
        <if test="projectCode != null and projectCode != ''">
            AND storage.locator like concat('%', #{projectCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="organizationId != null and organizationId != ''">
            and storage.organization_id = #{organizationId, jdbcType=BIGINT}
        </if>
        <if test="materialCode != null and materialCode != ''">
            and storage.segment1 = #{materialCode, jdbcType=VARCHAR}
        </if>
        <if test="type != null and type != ''">
            and storageInventory.type = #{type, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getPortalRemindCount" resultType="java.lang.Long">
        select
          count(0)
        from
          pam_ctc.purchase_material_requirement requirement
          LEFT JOIN pam_ctc.project project
            ON requirement.project_id = project.id
            and project.`deleted_flag` = 0
        where 1 = 1
          AND requirement.deleted_flag = 0
         <include refid="queryCondition" />
    </select>
    <select id="getsupplierRemindCount" resultType="java.lang.Long">
       select
          count(0)
        from
          pam_ctc.purchase_material_requirement requirement
          LEFT JOIN pam_ctc.project project
            ON requirement.project_id = project.id
            and project.`deleted_flag` = 0
        where 1 = 1
          AND requirement.deleted_flag = 0
          and requirement.status = 0
          and requirement.approved_supplier_number = 0
        <include refid="queryCondition" />
    </select>

    <sql id="wbsRequirementCondition">
        where
        requirement.deleted_flag = 0
        <!-- 作废的单据不能查出来 -->
        and receipts.requirement_status != 5
        <include refid="queryCondition" />
        <!-- 采购类型 -->
        <if test="purchaseType != null and purchaseType !=''">
            and requirement.purchase_type = #{purchaseType}
        </if>
        <!-- 是否急件 -->
        <if test="dispatchIs != null">
            and requirement.dispatch_is = #{dispatchIs}
        </if>
        <if test="brand != null and brand !=''">
            and material.brand like concat('%', #{brand}, '%')
        </if>
        <if test="figureNumber != null and figureNumber !=''">
            and material.figure_number like concat('%', #{figureNumber}, '%')
        </if>
        <if test="chartVersion != null and chartVersion !=''">
            and material.chart_version like concat('%', #{chartVersion}, '%')
        </if>
        <if test="wbsSummaryCode != null and wbsSummaryCode !=''">
            and requirement.wbs_summary_code like concat('%', #{wbsSummaryCode}, '%')
        </if>
        <if test="requirementCode != null and requirementCode !=''">
            and requirement.requirement_code like concat('%', #{requirementCode}, '%')
        </if>
        <if test="designReleaseLotNumber != null and designReleaseLotNumber !=''">
            and requirement.design_release_lot_number like concat('%', #{designReleaseLotNumber}, '%')
        </if>
        <if test="activityCode != null and activityCode !=''">
            and requirement.activity_code like concat('%', #{activityCode}, '%')
        </if>
        <!-- 最新发布日期 -->
        <if test="publishStartTime != null">
            and (select max(pmrd.publish_time) from pam_ctc.purchase_material_release_detail pmrd
            where pmrd.deleted_flag = 0 and pmrd.purchase_requirement_id = requirement.id and pmrd.publish_num <![CDATA[<> ]]> 0) <![CDATA[>= ]]> #{publishStartTime}
        </if>
        <if test="publishEndTime != null">
            and (select max(pmrd.publish_time) from pam_ctc.purchase_material_release_detail pmrd
            where pmrd.deleted_flag = 0 and pmrd.purchase_requirement_id = requirement.id and pmrd.publish_num <![CDATA[<> ]]> 0) <![CDATA[<= ]]> #{publishEndTime}
        </if>
        <!-- 最后更新日期 -->
        <if test="updateStartDate != null">
            and requirement.update_at <![CDATA[>= ]]> #{updateStartDate}
        </if>
        <if test="updateEndDate != null">
            and requirement.update_at <![CDATA[<= ]]> #{updateEndDate}
        </if>
        <!-- 计划交货日期 -->
        <if test="deliveryStartTime != null">
            and requirement.delivery_time <![CDATA[>= ]]> #{deliveryStartTime}
        </if>
        <if test="deliveryEndTime != null">
            and requirement.delivery_time <![CDATA[<= ]]> #{deliveryEndTime}
        </if>
        <if test="codingMiddleclass != null and codingMiddleclass != ''">
            and material.coding_middleclass like concat('%', #{codingMiddleclass}, '%')
        </if>
        <if test="materialType != null and materialType != ''">
            and material.material_type like concat('%', #{materialType}, '%')
        </if>
        <if test="modelParam != null and modelParam != ''">
            and (material.model like concat('%', #{modelParam},'%')
            or material.figure_number like concat('%', #{modelParam},'%'))
        </if>
        <if test="freezeFlag != null">
            and requirement.freeze_flag = #{freezeFlag}
        </if>
    </sql>


    <sql id="selectBudgetAmount">
        select
        CASE WHEN changes.local_total_price is null THEN coalesce(sum(coalesce(pcb.local_total_price, 0)), 0)
        ELSE coalesce(sum(GREATEST(coalesce(pcb.local_total_price, 0), coalesce(changes.local_total_price, 0))), 0) END
        from pam_ctc.purchase_contract_budget pcb
        left join (
        select
        budget_change.origin_id,
        budget_change.total_price,
        budget_change.local_total_price,
        budget_change.number
        from
        pam_ctc.purchase_contract_change_header header,
        pam_ctc.purchase_contract_budget_change_history budget_change
        where header.id = budget_change .header_id
        and header.status = 2
        and budget_change.history_type = 1 ) as changes
        on pcb.id = changes.origin_id
        left join pam_ctc.purchase_contract contract on pcb.purchase_contract_id = contract.id
        left join pam_ctc.purchase_material_requirement pmr on pcb.purchase_requirement_id = pmr.id
        where pcb.deleted_flag = 0
        <!-- 草稿/驳回/作废的采购合同累计采购合同占用金额释放 -->
        and contract.status not in (1,3,9)
    </sql>

    <!-- wbs物料采购需求查询(外包整包) -->
    <select id="findWbsList" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            <include refid="Base_Column_List" />
            requirement.id as requirementId,
            project.name as projectName,
            project.code as projectNum,
            project.status as projectStatus,
            receipts.id as receiptsId,
            material.id as materialId,
            material.coding_middleclass as codingMiddleclass,
            material.material_type as materialType,
            material.model,
            material.brand,
            material.figure_number as figureNumber,
            material.chart_version as chartVersion,
            ou.operating_unit_name as projectOuName,
            ou.operating_unit_id as projectOuId,
            <!-- 发布日期 -->
            (select max(pmrd.publish_time) from pam_ctc.purchase_material_release_detail pmrd
            where pmrd.deleted_flag = 0 and pmrd.purchase_requirement_id = requirement.id and pmrd.publish_num <![CDATA[<> ]]> 0) as publishTime,
            <!-- 需求预算 -->
            (select COALESCE(sum(pwrb.budget_occupied_amount), 0) from pam_ctc.project_wbs_receipts_budget pwrb where pwrb.deleted_flag = 0 and pwrb.project_wbs_receipts_id = receipts.id and pwrb.demand_type = 0) as demandCost,
            <!-- 累计采购合同占用金额 （合同或合同预算变更审批中，取最大），取本位币 -->
            @contractTotalAmount :=
            case requirement.requirement_type
                when 2 then ( <include refid="selectBudgetAmount" />
                            and pmr.id = requirement.id )
                else ( <include refid="selectBudgetAmount" />
                            and pmr.project_wbs_receipts_id = receipts.id
                            and pcb.wbs_summary_code = requirement.wbs_summary_code )
            end as contractTotalAmount,
            <!-- 剩余WBS需求预算占用（外包），= WBS需求预算占用（外包）- 累计采购合同占用金额 -->
            COALESCE((requirement.wbs_demand_os_cost - @contractTotalAmount), 0) as wbsRemainingDemandOsCost,
            <!-- 已采购量 （合同或合同预算变更审批中，取最大） -->
            @releasedQuantity :=(
                select
                    ifnull(sum(GREATEST(ifnull(pcb.number, 0), ifnull(changes.number, 0))), 0)
                from pam_ctc.purchase_contract_budget pcb
                left join (
                    select
                        budget_change.origin_id,
                        budget_change.total_price,
                        budget_change.number
                    from
                        pam_ctc.purchase_contract_change_header header,
                        pam_ctc.purchase_contract_budget_change_history budget_change
                    where header.id = budget_change .header_id
                    and header.status = 2
                    and budget_change.history_type = 1 ) as changes
                on pcb.id = changes.origin_id
                left join pam_ctc.purchase_contract contract on pcb.purchase_contract_id = contract.id
                where pcb.deleted_flag = 0
                <!-- 草稿/驳回/作废的采购合同已采购量释放 -->
                and contract.status <![CDATA[ <> ]]> 9
                and contract.deleted_flag = 0
                and pcb.purchase_requirement_id = requirement.id) as releasedQuantity,
            <!-- 关闭数量 -->
            @closedQuantity := CASE WHEN requirement.closed_quantity <![CDATA[ < ]]> 0 THEN 0 ELSE IFNULL(requirement.closed_quantity,0) END as closedQuantity,
			<!-- 未采购量 = 总需求量 - 关闭数量 - 已采购量 -->
            COALESCE((requirement.need_total - @closedQuantity - @releasedQuantity), 0) as unreleasedAmount
        from pam_ctc.purchase_material_requirement requirement
        left join pam_ctc.project project
            on project.id = requirement.project_id
            and project.deleted_flag = 0
        left join pam_basedata.operating_unit ou
            on project.ou_id = ou.id
        left join pam_basedata.material material
            on material.id = requirement.materiel_id
        left join pam_ctc.project_wbs_receipts receipts
            on receipts.id = requirement.project_wbs_receipts_id
            and receipts.deleted_flag = 0
        where requirement.deleted_flag = 0
        <include refid="queryCondition" />
        <!-- 采购类型 -->
        <if test="purchaseType != null and purchaseType !=''">
            and requirement.purchase_type = #{purchaseType}
        </if>
        <!-- 是否急件 -->
        <if test="dispatchIs != null">
            and requirement.dispatch_is = #{dispatchIs}
        </if>
        <if test="ifHro != null and ifHro == 0">
            and ( requirement.requirement_type <![CDATA[<> ]]> 2 or requirement.requirement_type is null )
        </if>
        <if test="brand != null and brand !=''">
            and material.brand like concat('%', #{brand}, '%')
        </if>
        <if test="chartVersion != null and chartVersion !=''">
            and material.chart_version like concat('%', #{chartVersion}, '%')
        </if>
        <if test="wbsSummaryCode != null and wbsSummaryCode !=''">
            and requirement.wbs_summary_code like concat('%', #{wbsSummaryCode}, '%')
        </if>
        <if test="requirementCode != null and requirementCode !=''">
            and requirement.requirement_code like concat('%', #{requirementCode}, '%')
        </if>
        <if test="designReleaseLotNumber != null and designReleaseLotNumber !=''">
            and requirement.design_release_lot_number like concat('%', #{designReleaseLotNumber}, '%')
        </if>
        <if test="activityCode != null and activityCode !=''">
            and requirement.activity_code like concat('%', #{activityCode}, '%')
        </if>
        <!-- 最新发布日期 -->
        <if test="publishStartTime != null">
            and (select max(pmrd.publish_time) from pam_ctc.purchase_material_release_detail pmrd
            where pmrd.deleted_flag = 0 and pmrd.purchase_requirement_id = requirement.id and pmrd.publish_num <![CDATA[<> ]]> 0) <![CDATA[>= ]]> #{publishStartTime}
        </if>
        <if test="publishEndTime != null">
            and (select max(pmrd.publish_time) from pam_ctc.purchase_material_release_detail pmrd
            where pmrd.deleted_flag = 0 and pmrd.purchase_requirement_id = requirement.id and pmrd.publish_num <![CDATA[<> ]]> 0) <![CDATA[<= ]]> #{publishEndTime}
        </if>
        <!-- 最后更新日期 -->
        <if test="updateStartDate != null">
            and requirement.update_at <![CDATA[>= ]]> #{updateStartDate}
        </if>
        <if test="updateEndDate != null">
            and requirement.update_at <![CDATA[<= ]]> #{updateEndDate}
        </if>
        <if test="codingMiddleclass != null and codingMiddleclass != ''">
            and material.coding_middleclass like concat('%', #{codingMiddleclass}, '%')
        </if>
        <if test="materialType != null and materialType != ''">
            and material.material_type like concat('%', #{materialType}, '%')
        </if>
        <if test="modelParam != null and modelParam != ''">
            and (material.model like concat('%', #{modelParam},'%')
            or material.figure_number like concat('%', #{modelParam},'%'))
        </if>
        ORDER BY requirement.requirement_code desc,requirement.erp_code desc
    </select>


    <!-- wbs物料采购需求查询 post-->
    <select id="wbsRequirementPage" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            <include refid="Base_Column_List" />
            requirement.id as requirementId,
            project.name as projectName,
            project.code as projectNum,
            project.status as projectStatus,
            projectType.name as projectType,
            receipts.web_type as webType,
            material.id as materialId,
            material.coding_middleclass as codingMiddleclass,
            material.material_type as materialType,
            material.model,
            material.brand,
            material.figure_number as figureNumber,
            requirement.chart_version as chartVersion,
            material.erp_code as erpCodeIs,
            ou.operating_unit_name as projectOuName,
            ou.operating_unit_id as projectOuId,
            projectProfit.storage_id as orgId,
            <!-- 发布日期 -->
            (select max(pmrd.publish_time) from pam_ctc.purchase_material_release_detail pmrd
            where pmrd.deleted_flag = 0 and pmrd.purchase_requirement_id = requirement.id and pmrd.publish_num <![CDATA[<> ]]> 0) as publishTime
        from
            pam_ctc.purchase_material_requirement requirement
        left join pam_ctc.project project
            on project.id = requirement.project_id and project.deleted_flag = 0
        left join pam_ctc.project_profit projectProfit
            on projectProfit.project_id = project.id and projectProfit.deleted_flag = 0
        left join pam_basedata.operating_unit ou
            on project.ou_id = ou.id
        left join pam_basedata.material material
            on material.id = requirement.materiel_id and material.delete_flag = 0
        left join pam_ctc.project_type projectType
            on project.`type` = projectType.id
        left join pam_ctc.project_wbs_receipts receipts
            on receipts.id = requirement.project_wbs_receipts_id
        <include refid="wbsRequirementCondition" />
            ORDER BY requirement.requirement_code desc,requirement.erp_code
    </select>

    <!-- wbs物料采购需求查询 post-->
    <select id="countWbsRequirement" resultType="java.lang.Long">
        select
            count(requirement.id)
        from
            pam_ctc.purchase_material_requirement requirement
        left join pam_ctc.project project
            on project.id = requirement.project_id and project.deleted_flag = 0
        left join pam_ctc.project_profit projectProfit
            on projectProfit.project_id = project.id and projectProfit.deleted_flag = 0
        left join pam_basedata.operating_unit ou
            on project.ou_id = ou.id
        left join pam_basedata.material material
            on material.id = requirement.materiel_id and material.delete_flag = 0
        left join pam_ctc.project_type projectType
            on project.`type` = projectType.id
        left join pam_ctc.project_wbs_receipts receipts
            on receipts.id = requirement.project_wbs_receipts_id
        <include refid="wbsRequirementCondition" />
    </select>

    <select id="getDiscountMoneyByRequirementCode"
            resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto" parameterType="java.util.List">
        select
            po.id,
            po.order_status as orderStatus,
            po.num,
            pod.requirement_code as requirementCode,
            pod.project_wbs_receipts_id as projectWbsReceiptsId,
            pod.wbs_summary_code as wbsSummaryCode,
            (ifnull(pod.discount_money, 0)-ifnull(pod.cancel_num, 0)* ifnull(pod.discount_price, 0))*ifnull(po.conversion_rate,1) as discountMoney
        from
            pam_ctc.purchase_order_detail pod
        inner join pam_ctc.purchase_order po on
            po.id = pod.purchase_order_id
        where
            pod.deleted_flag = 0
            and po.deleted_flag = 0
            and po.order_status in (1, 2, 3, 4, 5, 7, 11)
            and (pod.merge_rows = 0 or pod.merge_rows is null)
            and (pod.project_wbs_receipts_id,pod.wbs_summary_code) in
            <foreach collection="list" item="item" separator="," close=")" open="(">
                (#{item.projectWbsReceiptsId},#{item.wbsSummaryCode})
            </foreach>
        union all
        select
            po.id,
            po.order_status as orderStatus,
            po.num,
            pom.requirement_code as requirementCode,
            pod.project_wbs_receipts_id as projectWbsReceiptsId,
            pom.wbs_summary_code as wbsSummaryCode,
            (ifnull(pom.discount_money, 0) - ifnull(pom.cancel_num, 0)* ifnull(pom.discount_price, 0))*ifnull(po.conversion_rate,1) as discountMoney
        from
            pam_ctc.purchase_order_merge pom
        inner join pam_ctc.purchase_order_detail pod on
            pod.id = pom.purchase_order_id
        inner join pam_ctc.purchase_order po on
            po.id = pod.purchase_order_id
        where
            pod.deleted_flag = 0
            and pom.deleted_flag = 0
            and po.deleted_flag = 0
            and pod.merge_rows = 1
            and po.order_status in (1, 2, 3, 4, 5, 7, 11)
            and (pod.project_wbs_receipts_id,pom.wbs_summary_code) in
            <foreach collection="list" item="item" separator="," close=")" open="(">
                (#{item.projectWbsReceiptsId},#{item.wbsSummaryCode})
            </foreach>
        union all
        select
            po.id,
            po.order_status as orderStatus,
            po.num,
            pod.requirement_code as requirementCode,
            pod.project_wbs_receipts_id as projectWbsReceiptsId,
            pod.wbs_summary_code as wbsSummaryCode,
            (ifnull(pod.discount_money, 0)-ifnull(pod.cancel_num, 0)* ifnull(pod.discount_price, 0))*ifnull(po.conversion_rate,1) as discountMoney
        from
            pam_ctc.purchase_order_detail_change_history pod
        inner join pam_ctc.purchase_order_change_history poch on
            poch.id = pod.purchase_order_id
        inner join pam_ctc.purchase_order po on
            po.id = poch.origin_id
        inner join pam_ctc.purchase_order_change_record pocr on
            pocr.id = poch.record_id
            and pocr.id = pod.record_id
        where
            pod.deleted_flag = 0
            and po.deleted_flag = 0
            and poch.deleted_flag = 0
            and pocr.deleted_flag = 0
            and pod.history_type = 1
            and poch.history_type = 1
            and pod.origin_id is null
            and pocr.status = 2
            and (pod.merge_rows = 0 or pod.merge_rows is null)
            and (pod.project_wbs_receipts_id,pod.wbs_summary_code) in
            <foreach collection="list" item="item" separator="," close=")" open="(">
                (#{item.projectWbsReceiptsId},#{item.wbsSummaryCode})
            </foreach>
        union all
        select
            po.id,
            po.order_status as orderStatus,
            po.num,
            pom.requirement_code as requirementCode,
            pod.project_wbs_receipts_id as projectWbsReceiptsId,
            pom.wbs_summary_code as wbsSummaryCode,
            (ifnull(pom.discount_money, 0) - ifnull(pom.cancel_num, 0)* ifnull(pom.discount_price, 0))*ifnull(po.conversion_rate,1) as discountMoney
        from
            pam_ctc.purchase_order_merge_change_history pom
        inner join pam_ctc.purchase_order_detail_change_history pod on
            pod.id = pom.purchase_order_id
        inner join pam_ctc.purchase_order_change_history poch on
            poch.id = pod.purchase_order_id
        inner join pam_ctc.purchase_order po on
            po.id = poch.origin_id
        inner join pam_ctc.purchase_order_change_record pocr on
            pocr.id = poch.record_id
            and pocr.id = pom.record_id
            and pocr.id = pod.record_id
        where
            pod.deleted_flag = 0
            and pom.deleted_flag = 0
            and po.deleted_flag = 0
            and poch.deleted_flag = 0
            and pod.history_type = 1
            and poch.history_type = 1
            and pom.history_type = 1
            and pocr.status = 2
            and pod.origin_id is null
            and pom.origin_id is null
            and pod.merge_rows = 1
            and (pod.project_wbs_receipts_id,pom.wbs_summary_code) in
            <foreach collection="list" item="item" separator="," close=")" open="(">
                (#{item.projectWbsReceiptsId},#{item.wbsSummaryCode})
            </foreach>
    </select>

    <select id="statisticsQuantityAndCost" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            requirement.id,
            receipts.id as receiptsId,
            <!-- 需求预算 -->
            (select COALESCE(sum(pwrb.budget_occupied_amount), 0) from pam_ctc.project_wbs_receipts_budget pwrb where pwrb.deleted_flag = 0 and pwrb.project_wbs_receipts_id = receipts.id and pwrb.demand_type = 0) as demandCost,
            <!-- 预算占用金额(wbs) -->
            IFNULL(perbudget.budget_occupied_amount,0) * IFNULL(requirement.conversion_rate,1) as budgetOccupiedAmount,
            <!-- 预算占用金额 -->
            IFNULL(perbudget0.budget_occupied_amount,0) * IFNULL(requirement.conversion_rate,1) as budgetOccupiedAmountTotal,
            IFNULL(o.releasedQuantity, 0) as releasedQuantity,
            IFNULL(o2.orderQuantity, 0) as orderQuantity,
            IFNULL(t.transactionQuantity, 0) as projectHavedQuantity,
            IFNULL(t2.noprojectHavedQuantity, 0) + IFNULL(t3.noprojectHavedQuantity, 0) as noprojectHavedQuantity,
            <!-- 系统建议下达量 -->
            CASE WHEN((
            requirement.need_total - IFNULL(o.releasedQuantity, 0) - IFNULL(t.transactionQuantity, 0)
            ))<![CDATA[<]]>0 THEN 0 ELSE (requirement.need_total - IFNULL(o.releasedQuantity, 0) - IFNULL(t.transactionQuantity, 0))  END AS systemQuantity,
            <!-- 关闭数量 -->
            IFNULL(o3.closedQuantity,0) as closedQuantity,
            <!-- 未采购量 = 总需求量 - 已采购量(已下达+已下单) - 关闭数量 -->
            (IFNULL(requirement.need_total,0) - IFNULL(o.releasedQuantity,0) - IFNULL(o3.closedQuantity,0)) AS unreleasedAmount
        from
        pam_ctc.purchase_material_requirement requirement
        left join (
            select
            t.material_purchase_requirement_id as purchase_requirement_id,
            sum(t.releasedQuantity) as releasedQuantity
            from
            (
                select
                ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                pod.material_purchase_requirement_id
                from
                pam_ctc.purchase_order_detail pod
                inner join pam_ctc.purchase_order po on
                pod.purchase_order_id = po.id
                where
                pod.`status` != 3
                and po.order_status  <![CDATA[ <> ]]> 9
                and po.deleted_flag = 0
                and pod.deleted_flag = 0
                and (pod.merge_rows = 0
                or pod.merge_rows is null)
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and pod.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
                union all
                select
                ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
                pom.material_purchase_requirement_id
                from
                pam_ctc.purchase_order_merge pom
                inner join pam_ctc.purchase_order_detail pod on
                pod.id = pom.purchase_order_id
                inner join pam_ctc.purchase_order po on
                po.id = pod.purchase_order_id
                where
                pod.deleted_flag = 0
                and pom.deleted_flag = 0
                and po.deleted_flag = 0
                and pod.merge_rows = 1
                and pod.`status` != 3
                and po.order_status  <![CDATA[ <> ]]> 9
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and pom.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
                union all
                select
                ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                pod.material_purchase_requirement_id
                from
                pam_ctc.purchase_order_detail_change_history pod
                inner join pam_ctc.purchase_order_change_record pocr on
                pocr.id = pod.record_id
                where
                pod.deleted_flag = 0
                and pocr.deleted_flag = 0
                and pod.history_type = 1
                and pod.origin_id is null
                and pocr.status = 2
                and (pod.merge_rows = 0
                or pod.merge_rows is null)
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and pod.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
                union all
                select
                ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
                pom.material_purchase_requirement_id
                from
                pam_ctc.purchase_order_merge_change_history pom
                inner join pam_ctc.purchase_order_detail_change_history pod on
                pod.id = pom.purchase_order_id
                inner join pam_ctc.purchase_order_change_record pocr on
                pocr.id = pom.record_id
                and pocr.id = pod.record_id
                where
                pod.deleted_flag = 0
                and pom.deleted_flag = 0
                and pod.history_type = 1
                and pom.history_type = 1
                and pocr.status = 2
                and pod.origin_id is null
                and pom.origin_id is null
                and pod.merge_rows = 1
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and pom.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
                ) t
            where
            t.material_purchase_requirement_id is not null
            group by
            t.material_purchase_requirement_id ) o on
            o.purchase_requirement_id = requirement.id
        left join (
            select
            t.material_purchase_requirement_id as purchase_requirement_id,
            sum(t.releasedQuantity) as orderQuantity
            from
            (
                select
                ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                pod.material_purchase_requirement_id
                from
                pam_ctc.purchase_order_detail pod
                inner join pam_ctc.purchase_order po on
                pod.purchase_order_id = po.id
                where
                pod.`status` in (2, 4)
                and po.order_status  <![CDATA[ <> ]]> 9
                and po.deleted_flag = 0
                and pod.deleted_flag = 0
                and (pod.merge_rows = 0
                or pod.merge_rows is null)
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and pod.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
                union all
                select
                ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
                pom.material_purchase_requirement_id
                from
                pam_ctc.purchase_order_merge pom
                inner join pam_ctc.purchase_order_detail pod on
                pod.id = pom.purchase_order_id
                inner join pam_ctc.purchase_order po on
                po.id = pod.purchase_order_id
                where
                pod.deleted_flag = 0
                and pom.deleted_flag = 0
                and po.deleted_flag = 0
                and pod.merge_rows = 1
                and pod.`status` in (2, 4)
                and po.order_status  <![CDATA[ <> ]]> 9
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and pom.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
                ) t
            where
            t.material_purchase_requirement_id is not null
            group by
            t.material_purchase_requirement_id ) o2 on
            o2.purchase_requirement_id = requirement.id
        left join (
            select
            IFNULL(sum(if(close_type = 1, close_num, -1 * close_num)), 0)as closedQuantity,
            purchase_requirement_id
            from
            pam_ctc.purchase_material_close_detail
            where
            deleted_flag = 0
            <if test="requirementIdList != null and requirementIdList.size() > 0">
                and purchase_requirement_id in
                <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                    #{requirementId}
                </foreach>
            </if>
            group by
            purchase_requirement_id ) o3 on
            o3.purchase_requirement_id = requirement.id
        left join pam_ctc.project project on
            project.id = requirement.project_id
            and project.deleted_flag = 0
        left join pam_ctc.project_profit projectProfit on
            projectProfit.project_id = project.id
            and projectProfit.deleted_flag = 0
        left join pam_ctc.project_wbs_receipts receipts on
            receipts.id = requirement.project_wbs_receipts_id
            and receipts.deleted_flag = 0
        left join pam_ctc.project_wbs_receipts_budget perbudget on
            ( perbudget.project_wbs_receipts_id = requirement.project_wbs_receipts_id
            and perbudget.deleted_flag = 0
            and perbudget.demand_type = 1
            and perbudget.wbs_summary_code = requirement.wbs_summary_code )
        left join pam_ctc.project_wbs_receipts_budget perbudget0 on
            ( perbudget0.project_wbs_receipts_id = requirement.project_wbs_receipts_id
            and perbudget0.deleted_flag = 0
            and perbudget0.demand_type = 0
            and perbudget0.wbs_summary_code = requirement.wbs_summary_code )
        left join (
            select
            storage.locator,
            IFNULL(storage.transaction_quantity, 0) as transactionQuantity,
            storage.organization_id,
            storage.segment1
            from
            pam_basedata.storage storage
            inner join pam_basedata.storage_inventory storageInventory on
            storage.subinventory_code = storageInventory.secondary_inventory_name
            where
            storage.deleted_flag = 0
            AND storage.locator <![CDATA[ <> ]]> ''
            and storageInventory.type = '01'
            <if test="erpCodeList != null and erpCodeList.size() > 0">
                and storage.segment1 in
                <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
            </if>
            group by
            storage.locator,
            storage.organization_id,
            storage.segment1
            ) t
            on t.locator like concat(project.code, '%')
            and t.organization_id = projectProfit.storage_id and t.segment1 = requirement.erp_code
        LEFT JOIN (
            SELECT
            sum(IFNULL(storage.transaction_quantity, 0)) AS noprojectHavedQuantity,
            storage.organization_id,
            storage.segment1
            FROM
            pam_basedata.storage storage
            INNER JOIN pam_basedata.storage_inventory storageInventory
            ON storage.subinventory_code = storageInventory.secondary_inventory_name
            WHERE storage.deleted_flag = 0
            AND storage.locator = ''
            AND storageInventory.type IN ('03', '04')
            <if test="erpCodeList != null and erpCodeList.size() > 0">
                and storage.segment1 in
                <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
            </if>
            GROUP BY
            storage.organization_id,
            storage.segment1
            ) t2
            ON t2.organization_id = projectProfit.storage_id AND t2.segment1 = requirement.erp_code
        LEFT JOIN (
            SELECT
            storage.locator,
            IFNULL(storage.transaction_quantity, 0) AS noprojectHavedQuantity,
            storage.organization_id,
            storage.segment1
            FROM
            pam_basedata.storage storage
            INNER JOIN pam_basedata.storage_inventory storageInventory
            ON storage.subinventory_code = storageInventory.secondary_inventory_name
            WHERE storage.deleted_flag = 0
            AND storage.locator <![CDATA[ <> ]]> ''
            AND storageInventory.type = '02'
            <if test="erpCodeList != null and erpCodeList.size() > 0">
                and storage.segment1 in
                <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
            </if>
            GROUP BY
            storage.locator,
            storage.organization_id,
            storage.segment1
            ) t3
            ON t3.locator like concat(project.code, '%')
            AND t3.organization_id = projectProfit.storage_id
            AND t3.segment1 = requirement.erp_code
        where 1 = 1
        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and requirement.id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
        group by requirement.id
    </select>

    <select id="getHroPurchaseRequirement"
            resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            pmr.id,
            pmr.pam_code pamCode,
            hri.role_id materialId,
            hri.role_name roleName,
            hri.start_date startDate,
            hri.end_date endDate,
            pmr.requirement_code requirementCode,
            pmr.wbs_summary_code wbsSummaryCode,
            pmr.need_total needTotal,
            pmr.unit,
            pmr.wbs_demand_os_cost wbsDemandOsCost,
            pmr.activity_code activityCode
        from pam_ctc.purchase_material_requirement pmr
        inner join pam_ctc.hro_requirement_item hri on hri.id = pmr.project_wbs_receipts_id
        where pmr.requirement_type = 2
        and pmr.status = 0
        and pmr.project_id = #{projectId}
        <if test="pamCode!=null">
            and pmr.pam_code = #{pamCode}
        </if>
        <if test="roleName!=null">
            and hri.role_name = #{roleName}
        </if>
        <if test="startDate!=null">
            and hri.start_date &lt;= #{startDate}
        </if>
        <if test="endDate!=null">
            and hri.end_date &gt;= #{endDate}
        </if>
        <if test="requirementCode!=null">
            and pmr.requirement_code = #{requirementCode}
        </if>
        <if test="wbsSummaryCode!=null">
            and pmr.wbs_summary_code = #{wbsSummaryCode}
        </if>
        <if test="activityCode!=null">
            and pmr.activity_code = #{activityCode}
        </if>
        <if test="freezeFlag!=null">
            and pmr.freeze_flag = #{freezeFlag}
        </if>
    </select>

    <select id="selectRequirementBudget" resultType="com.midea.pam.common.ctc.dto.HroRequirementDto">
        select
            hr.requirement_code requirementCode,
            sum(hri.total_budget) totalBudget
        from pam_ctc.hro_requirement hr
        inner join pam_ctc.hro_requirement_item hri on hri.requirement_id = hr.id and hri.deleted_flag = 0
        where hr.requirement_code in
        <foreach collection="requirementCodes" item="requirementCode" open="(" separator="," close=")">
            #{requirementCode}
        </foreach>
        group by hr.requirement_code
    </select>

    <select id="selectRequirementBudgetCostInfo"
            resultType="com.midea.pam.common.ctc.dto.PurchaseContractBudgetDto">
        select
            pcb.purchase_requirement_id purchaseRequirementId,
            pc.status contractStatus,
            if(history.local_total_price is null,
                pcb.local_total_price ,
                GREATEST(pcb.local_total_price,history.local_total_price)) localTotalPrice,
            if(history.`number` is null ,
                pcb.`number` ,
                GREATEST(pcb.`number`,history.`number`)) `number`
        from pam_ctc.purchase_contract_budget pcb
        inner join pam_ctc.purchase_contract pc
            on pc.id = pcb.purchase_contract_id
        left join (
            select
                history.origin_id,
                history.local_total_price,
                history.`number`
            from pam_ctc.purchase_contract_budget_change_history history
            inner join pam_ctc.purchase_contract_change_header header
                on header.id = history.header_id
            where history.purchase_requirement_id in
                <foreach collection="purchaseRequirementIds" item="requirementId" open="(" separator="," close=")">
                    #{requirementId}
                </foreach>
            and history.history_type = 1
            and history.deleted_flag = 0
            and header.change_type = 2
            and header.status = 2
        ) history on history.origin_id = pcb.id
        where pcb.purchase_requirement_id in
            <foreach collection="purchaseRequirementIds" item="requirementId" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        and pc.status in (1,2,3,5,10)
        and pcb.deleted_flag = 0
    </select>

    <select id="statisticsValidPriceQuantity" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            pmr.id,
            count(pbp.id) as validPriceQuantity
        from
        pam_ctc.purchase_material_requirement pmr
        inner join pam_ctc.project p on pmr.project_id = p.id
        inner join pam_basedata.vendor_asl va on va.material_code = pmr.erp_code and asl_status in ("Approved","ONETIME") and erp_disable_flag != 'Y'
        inner join pam_ctc.purchase_bpa_price pbp
            on pbp.material_code = pmr.erp_code
            and pbp.vendor_code = va.vendor_code
            and pbp.vendor_site_code = va.vendor_site_code
            and pbp.ou_id = p.ou_id
            and pbp.deleted_flag = 0
            and pbp.price_override > 0
            <!--失效日期,需要包含当前日期,数据库时间存储时分秒均为0,导致实际失效日期少一天-->
            and ( pbp.end_date is null or now() between pbp.start_date and DATE_ADD(pbp.end_date,interval 1 DAY)  )
        where 1=1
        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and pmr.id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
        group by pmr.id
    </select>

    <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="New_Base_Column_List" />
        from pam_ctc.purchase_material_requirement
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="statisticsProjectHavedQuantity" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
        requirement.id,
        IFNULL(t.transactionQuantity, 0) as projectHavedQuantity,
        IFNULL(t2.noprojectHavedQuantity, 0) + IFNULL(t3.noprojectHavedQuantity, 0) as noprojectHavedQuantity
        from
        pam_ctc.purchase_material_requirement requirement
        left join pam_ctc.project project on project.id = requirement.project_id and project.deleted_flag = 0
        left join pam_ctc.project_profit projectProfit on projectProfit.project_id = project.id and projectProfit.deleted_flag = 0
        left join (
            select
            storage.locator,
            IFNULL(storage.transaction_quantity, 0) as transactionQuantity,
            storage.organization_id,
            storage.segment1
            from
            pam_basedata.storage storage
            inner join pam_basedata.storage_inventory storageInventory on
            storage.subinventory_code = storageInventory.secondary_inventory_name
            where
            storage.deleted_flag = 0
            AND storage.locator <![CDATA[ <> ]]> ''
            and storageInventory.type = '01'
            <if test="erpCodeList != null and erpCodeList.size() > 0">
                and storage.segment1 in
                <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
            </if>
            group by
            storage.locator,
            storage.organization_id,
            storage.segment1
        ) t
        on t.locator like concat(project.code, '%')
        and t.organization_id = projectProfit.storage_id and t.segment1 = requirement.erp_code
        LEFT JOIN (
            SELECT
            sum(IFNULL(storage.transaction_quantity, 0)) AS noprojectHavedQuantity,
            storage.organization_id,
            storage.segment1
            FROM
            pam_basedata.storage storage
            INNER JOIN pam_basedata.storage_inventory storageInventory
            ON storage.subinventory_code = storageInventory.secondary_inventory_name
            WHERE storage.deleted_flag = 0
            AND storage.locator = ''
            AND storageInventory.type IN ('03', '04')
            <if test="erpCodeList != null and erpCodeList.size() > 0">
                and storage.segment1 in
                <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
            </if>
            GROUP BY
            storage.organization_id,
            storage.segment1
        ) t2
        ON t2.organization_id = projectProfit.storage_id AND t2.segment1 = requirement.erp_code
        LEFT JOIN (
            SELECT
            storage.locator,
            IFNULL(storage.transaction_quantity, 0) AS noprojectHavedQuantity,
            storage.organization_id,
            storage.segment1
            FROM
            pam_basedata.storage storage
            INNER JOIN pam_basedata.storage_inventory storageInventory
            ON storage.subinventory_code = storageInventory.secondary_inventory_name
            WHERE storage.deleted_flag = 0
            AND storage.locator <![CDATA[ <> ]]> ''
            AND storageInventory.type = '02'
            <if test="erpCodeList != null and erpCodeList.size() > 0">
                and storage.segment1 in
                <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator="," close=")">
                    #{erpCode}
                </foreach>
            </if>
            GROUP BY
            storage.locator,
            storage.organization_id,
            storage.segment1
        ) t3
        ON t3.locator like concat(project.code, '%')
        AND t3.organization_id = projectProfit.storage_id
        AND t3.segment1 = requirement.erp_code
        where 1 = 1
        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and requirement.id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
        group by requirement.id
    </select>

    <!-- wbs物料采购需求查询(外包整包) 修改 -->
    <select id="findWbsMaterialList" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            <include refid="Base_Column_List" />
            requirement.id as requirementId,
            project.name as projectName,
            project.code as projectNum,
            project.status as projectStatus,
            receipts.id as receiptsId,
            receipts.web_type as webType,
            material.id as materialId,
            material.coding_middleclass as codingMiddleclass,
            material.material_type as materialType,
            material.model,
            material.brand,
            material.figure_number as figureNumber,
            material.chart_version as chartVersion,
            ou.operating_unit_name as projectOuName,
            ou.operating_unit_id as projectOuId,
        <!-- 发布日期 -->
        (select max(pmrd.publish_time) from pam_ctc.purchase_material_release_detail pmrd
        where pmrd.deleted_flag = 0 and pmrd.purchase_requirement_id = requirement.id and pmrd.publish_num <![CDATA[<> ]]> 0) as publishTime,
        <!-- 需求预算 -->
        (select COALESCE(sum(pwrb.budget_occupied_amount), 0) from pam_ctc.project_wbs_receipts_budget pwrb where pwrb.deleted_flag = 0 and pwrb.project_wbs_receipts_id = receipts.id and pwrb.demand_type = 0) as demandCost,
        <!-- 需求预算(WBS维度) -->
        if(receipts.web_type = 1
        , (select COALESCE(sum(pwrb.budget_occupied_amount), 0) from pam_ctc.project_wbs_receipts_budget pwrb where pwrb.deleted_flag = 0 and pwrb.project_wbs_receipts_id = receipts.id and pwrb.demand_type = 0 and pwrb.wbs_summary_code = requirement.wbs_summary_code)
        , 0) as wbsDemandTotalCost,
        <!-- 关闭数量 -->
        @closedQuantity := CASE WHEN requirement.closed_quantity <![CDATA[ < ]]> 0 THEN 0 ELSE IFNULL(requirement.closed_quantity,0) END as closedQuantity

        from pam_ctc.purchase_material_requirement requirement
        left join pam_ctc.project project
            on project.id = requirement.project_id
            and project.deleted_flag = 0
        left join pam_basedata.operating_unit ou
            on project.ou_id = ou.id
        left join pam_basedata.material material
            on material.id = requirement.materiel_id
        left join pam_ctc.project_wbs_receipts receipts
            on receipts.id = requirement.project_wbs_receipts_id
            and receipts.deleted_flag = 0
        where requirement.deleted_flag = 0
        <include refid="queryCondition" />
        <!-- 采购类型 -->
        <if test="purchaseType != null and purchaseType !=''">
            and requirement.purchase_type = #{purchaseType}
        </if>
        <!-- 是否急件 -->
        <if test="dispatchIs != null">
            and requirement.dispatch_is = #{dispatchIs}
        </if>
        <if test="ifHro != null and ifHro == 0">
            and ( requirement.requirement_type <![CDATA[<> ]]> 2 or requirement.requirement_type is null )
        </if>
        <if test="brand != null and brand !=''">
            and material.brand like concat('%', #{brand}, '%')
        </if>
        <if test="chartVersion != null and chartVersion !=''">
            and material.chart_version like concat('%', #{chartVersion}, '%')
        </if>
        <if test="wbsSummaryCode != null and wbsSummaryCode !=''">
            and requirement.wbs_summary_code like concat('%', #{wbsSummaryCode}, '%')
        </if>
        <if test="requirementCode != null and requirementCode !=''">
            and requirement.requirement_code like concat('%', #{requirementCode}, '%')
        </if>
        <if test="designReleaseLotNumber != null and designReleaseLotNumber !=''">
            and requirement.design_release_lot_number like concat('%', #{designReleaseLotNumber}, '%')
        </if>
        <if test="activityCode != null and activityCode !=''">
            and requirement.activity_code like concat('%', #{activityCode}, '%')
        </if>
        <!-- 最新发布日期 -->
        <if test="publishStartTime != null">
            and (select max(pmrd.publish_time) from pam_ctc.purchase_material_release_detail pmrd where pmrd.deleted_flag = 0 and pmrd.purchase_requirement_id = requirement.id) <![CDATA[>= ]]> #{publishStartTime}
        </if>
        <if test="publishEndTime != null">
            and (select max(pmrd.publish_time) from pam_ctc.purchase_material_release_detail pmrd where pmrd.deleted_flag = 0 and pmrd.purchase_requirement_id = requirement.id) <![CDATA[<= ]]> #{publishEndTime}
        </if>
        <!-- 最后更新日期 -->
        <if test="updateStartDate != null">
            and requirement.update_at <![CDATA[>= ]]> #{updateStartDate}
        </if>
        <if test="updateEndDate != null">
            and requirement.update_at <![CDATA[<= ]]> #{updateEndDate}
        </if>
        <if test="codingMiddleclass != null and codingMiddleclass != ''">
            and material.coding_middleclass like concat('%', #{codingMiddleclass}, '%')
        </if>
        <if test="materialType != null and materialType != ''">
            and material.material_type like concat('%', #{materialType}, '%')
        </if>
        <if test="modelParam != null and modelParam != ''">
            and (material.model like concat('%', #{modelParam},'%')
            or material.figure_number like concat('%', #{modelParam},'%'))
        </if>
        <if test="freezeFlag != null">
            and requirement.freeze_flag = #{freezeFlag}
        </if>
        ORDER BY requirement.requirement_code desc,requirement.erp_code desc
    </select>

    <select id="findWbsMaterialList2" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementContractTotalAmountDto" parameterType="java.util.List">
        select
            requirement.id as requirementId,
            receipts.id as receiptsId,
            case
                when requirement.requirement_type = 2 then 2
                else 0
            end
                as requirementType,
            requirement.wbs_summary_code as wbsSummaryCode
        from pam_ctc.purchase_material_requirement requirement
        left join pam_ctc.project project
            on project.id = requirement.project_id
            and project.deleted_flag = 0
        left join pam_basedata.operating_unit ou
            on project.ou_id = ou.id
        left join pam_basedata.material material
            on material.id = requirement.materiel_id
        left join pam_ctc.project_wbs_receipts receipts
            on receipts.id = requirement.project_wbs_receipts_id
            and receipts.deleted_flag = 0
        where requirement.deleted_flag = 0
        and requirement.id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.requirementId}
        </foreach>
    </select>

    <select id="selectContractTotalAmount" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementContractTotalAmountDto"
            parameterType="java.util.List">
        select
            CASE
                WHEN changes.local_total_price is null THEN coalesce(sum(coalesce(pcb.local_total_price, 0)), 0)
                ELSE coalesce(sum(GREATEST(coalesce(pcb.local_total_price, 0), coalesce(changes.local_total_price, 0))), 0)
                END
                    as contractTotalAmount,
                pmr.id as requirementId
        from pam_ctc.purchase_contract_budget pcb
        left join (
        select
            budget_change.origin_id,
            budget_change.total_price,
            budget_change.local_total_price,
            budget_change.number
        from
        pam_ctc.purchase_contract_change_header header,
        pam_ctc.purchase_contract_budget_change_history budget_change
        where header.id = budget_change .header_id
            and header.status = 2
            and budget_change.history_type = 1 ) as changes
            on pcb.id = changes.origin_id
        left join pam_ctc.purchase_contract contract on pcb.purchase_contract_id = contract.id
        left join pam_ctc.purchase_material_requirement pmr on pcb.purchase_requirement_id = pmr.id
            where pcb.deleted_flag = 0
            <!-- 草稿/驳回/作废的采购合同累计采购合同占用金额释放 -->
            and contract.status not in (1,3,9)
            and pmr.id in (
            <foreach collection="list" item="item" separator=",">
                (#{item.requirementId})
            </foreach>
            )
            group by pmr.id
    </select>


    <select id="selectContractTotalAmount2" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementContractTotalAmountDto" parameterType="java.util.List">
        select
        CASE
            WHEN changes.local_total_price is null THEN coalesce(sum(coalesce(pcb.local_total_price, 0)), 0)
            ELSE coalesce(sum(GREATEST(coalesce(pcb.local_total_price, 0), coalesce(changes.local_total_price, 0))), 0)
            END
                as contractTotalAmount,
                pmr.project_wbs_receipts_id as receiptsId,
                pcb.wbs_summary_code as wbsSummaryCode
        from pam_ctc.purchase_contract_budget pcb
        left join (
        select
            budget_change.origin_id,
            budget_change.total_price,
            budget_change.local_total_price,
            budget_change.number
        from
            pam_ctc.purchase_contract_change_header header,
            pam_ctc.purchase_contract_budget_change_history budget_change
            where header.id = budget_change .header_id
            and header.status = 2
            and budget_change.history_type = 1 ) as changes
            on pcb.id = changes.origin_id
        left join pam_ctc.purchase_contract contract on pcb.purchase_contract_id = contract.id
        left join pam_ctc.purchase_material_requirement pmr on pcb.purchase_requirement_id = pmr.id
            where pcb.deleted_flag = 0
            <!-- 草稿/驳回/作废的采购合同累计采购合同占用金额释放 -->
            and contract.status not in (1,3,9)
            and (pmr.project_wbs_receipts_id,pcb.wbs_summary_code) in (
            <foreach collection="list" item="item" separator=",">
                (#{item.receiptsId},#{item.wbsSummaryCode})
            </foreach>
        )
        group by pmr.project_wbs_receipts_id,
        pcb.wbs_summary_code
    </select>

    <select id="selectReleasedQuantity" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementContractTotalAmountDto" parameterType="java.util.List">
        select
        ifnull(sum(GREATEST(ifnull(pcb.number, 0), ifnull(changes.number, 0))), 0) as releasedQuantity,
        pcb.purchase_requirement_id as requirementId
        from pam_ctc.purchase_contract_budget pcb
        left join (
            select
                budget_change.origin_id,
                budget_change.total_price,
                budget_change.number
            from
            pam_ctc.purchase_contract_change_header header,
            pam_ctc.purchase_contract_budget_change_history budget_change
            where header.id = budget_change .header_id
                and header.status = 2
                and budget_change.history_type = 1 ) as changes
            on pcb.id = changes.origin_id
        left join pam_ctc.purchase_contract contract on pcb.purchase_contract_id = contract.id
            where pcb.deleted_flag = 0
            <!-- 草稿/驳回/作废的采购合同已采购量释放 -->
            and contract.status <![CDATA[ <> ]]> 9
            and contract.deleted_flag = 0
            and pcb.purchase_requirement_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.requirementId}
        </foreach>
        group by pcb.purchase_requirement_id
    </select>

    <select id="selectListWithDetailBase" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
          select
                requirement.id,
                requirement.project_id as 'projectId',
                requirement.erp_code as 'erpCode',
                requirement.pam_code as 'pamCode',
                requirement.delivery_time as 'deliveryTime',
                requirement.materiel_id as 'materielId',
                requirement.materiel_descr as 'materielDescr',
                requirement.need_total as 'needTotal',
                requirement.unit_code as 'unitCode',
                requirement.unit as 'unit',
                requirement.STATUS as 'status',
                requirement.reason as 'reason',
                requirement.purchase_type as 'purchaseType',
                requirement.deleted_flag as 'deletedFlag',
                requirement.create_by as 'createBy',
                requirement.create_at as 'createAt',
                requirement.update_by as 'updateBy',
                requirement.update_at as 'updateAt',
                requirement.currency as 'currency',
                requirement.conversion_type as 'conversionType',
                requirement.conversion_date as 'conversionDate',
                requirement.conversion_rate as 'conversionRate',
                requirement.delivery_address as 'deliveryAddress',
                requirement.consignee as 'consignee',
                requirement.contact_phone as 'contactPhone',
                project.NAME as 'projectName',
                project.`code` as 'projectNum',
                project.`STATUS` as 'projectStatus',
                pty.`name` as 'projectType',
                requirement.approved_supplier_number as 'approvedSupplierNumber',
                project.ou_id as 'ouId',
                projectProfit.storage_id as 'projectOrganizationId',
                pmrd.publish_time as publishTime,
                material.coding_middleclass as codingMiddleclass,
                material.material_type as materialType,
                material.brand as brand,
                ou.operating_unit_name as projectOuName
            from
                pam_ctc.purchase_material_requirement requirement
            inner join pam_ctc.project project on
                requirement.project_id = project.id
            inner join pam_ctc.project_type pty on
                project.type = pty.id
            inner join pam_ctc.project_profit projectProfit on
                projectProfit.project_id = project.id
            inner join pam_basedata.operating_unit ou on
                project.ou_id = ou.id
            left join (
                select
                    max(pmrd.publish_time) as publish_time,
                    pmrd.purchase_requirement_id
                from
                    pam_ctc.purchase_material_release_detail pmrd
                where
                    pmrd.deleted_flag = 0
                    and pmrd.publish_num <![CDATA[<> ]]> 0
                group by
                    pmrd.purchase_requirement_id
                    ) pmrd on
                pmrd.purchase_requirement_id = requirement.id
            left join pam_basedata.material material
                    on
                material.item_code = requirement.erp_code
                and material.pam_code = requirement.pam_code
                and material.organization_id = projectProfit.storage_id
                and material.delete_flag = 0
            where
                requirement.deleted_flag = 0
                    <include refid = "queryCondition" />
                    <if test = "publishStartTime != null">
                and pmrd.publish_time <![CDATA[>= ]]>
                #{publishStartTime} and pmrd.publish_time is not null
                    </if>
                    <if test = "publishEndTime != null">
                and pmrd.publish_time <![CDATA[<= ]]>
                #{publishEndTime} and pmrd.publish_time is not null
                    </if>
                    <if test = "purchaseType != null and purchaseType !=''">
                and requirement.purchase_type =
                #{purchaseType}
                    </if>
                    <if test = "brand != null and brand !=''">
                and material.brand like concat('%', #{brand}, '%')
                    </if>
                and (project.wbs_enabled != 1 or project.wbs_enabled is null)
                    order by approvedSupplierNumber desc, project.`code` asc, requirement.erp_code asc
    </select>

    <select id="selectListWithDetailStatic" parameterType="java.util.List" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
                SELECT
                    requirement.id,
                    -- 已发布数量：统计未删除、非取消状态的有效订单数量
                    IFNULL((
                        SELECT SUM(IFNULL(pod.order_num, 0) - IFNULL(pod.cancel_num, 0))
                        FROM pam_ctc.purchase_order_detail pod
                        WHERE pod.material_purchase_requirement_id = requirement.id
                            AND pod.deleted_flag = 0
                            AND pod.material_purchase_requirement_id IS NOT NULL
                            AND pod.status <![CDATA[<> ]]> 3
                            AND (
                                pod.purchase_order_id IS NULL
                                OR EXISTS (
                                    SELECT 1
                                    FROM pam_ctc.purchase_order po
                                    WHERE po.id = pod.purchase_order_id
                                        AND po.order_status <![CDATA[<> ]]> 9
                                        AND po.deleted_flag = 0
                                )
                            )
                    ), 0) AS releasedQuantity,

                    -- 订单数量：统计已下单或已关闭的有效订单数量
                    IFNULL((
                        SELECT SUM(IFNULL(pod.order_num, 0) - IFNULL(pod.cancel_num, 0))
                        FROM pam_ctc.purchase_order_detail pod
                        WHERE pod.material_purchase_requirement_id = requirement.id
                            AND pod.deleted_flag = 0
                            AND pod.status IN (2, 4)
                            AND EXISTS (
                                SELECT 1
                                FROM pam_ctc.purchase_order po
                                WHERE po.id = pod.purchase_order_id
                                    AND po.order_status <![CDATA[<> ]]> 9
                                    AND po.deleted_flag = 0
                            )
                    ), 0) AS orderQuantity,

                    -- 项目已有数量：统计项目接收仓的库存量
                    IFNULL((
                        SELECT SUM(s.transaction_quantity)
                        FROM pam_basedata.storage s
                        INNER JOIN pam_basedata.storage_inventory si
                            ON s.subinventory_code = si.secondary_inventory_name
                        WHERE s.deleted_flag = 0
                            AND s.locator <![CDATA[<> ]]> ''
                            AND si.type = '01'
                            AND s.locator LIKE CONCAT(project.code, '%')
                            AND s.organization_id = projectProfit.storage_id
                            AND s.segment1 = requirement.erp_code
                    ), 0) AS projectHavedQuantity,

                    -- 非项目已有数量：统计公共库存仓和清理仓的库存量
                    (
                        IFNULL((
                            SELECT SUM(s.transaction_quantity)
                            FROM pam_basedata.storage s
                            INNER JOIN pam_basedata.storage_inventory si
                                ON s.subinventory_code = si.secondary_inventory_name
                            WHERE s.deleted_flag = 0
                                AND s.locator = ''
                                AND si.type IN ('03', '04')
                                AND s.organization_id = projectProfit.storage_id
                                AND s.segment1 = requirement.erp_code
                        ), 0) +
                        IFNULL((
                            SELECT SUM(s.transaction_quantity)
                            FROM pam_basedata.storage s
                            INNER JOIN pam_basedata.storage_inventory si
                                ON s.subinventory_code = si.secondary_inventory_name
                            WHERE s.deleted_flag = 0
                                AND s.locator <![CDATA[<> ]]> ''
                                AND si.type = '02'
                                AND s.locator LIKE CONCAT(project.code, '%')
                                AND s.organization_id = projectProfit.storage_id
                                AND s.segment1 = requirement.erp_code
                        ), 0)
                    ) AS noprojectHavedQuantity,

                    -- 系统数量：计算实际可用数量
                    CASE
                        WHEN (
                            requirement.need_total -
                            IFNULL((
                                SELECT SUM(IFNULL(pod.order_num, 0) - IFNULL(pod.cancel_num, 0))
                                FROM pam_ctc.purchase_order_detail pod
                                WHERE pod.material_purchase_requirement_id = requirement.id
                                    AND pod.deleted_flag = 0
                                    AND pod.material_purchase_requirement_id IS NOT NULL
                                    AND pod.status <![CDATA[<> ]]> 3
                                    AND (
                                        pod.purchase_order_id IS NULL
                                        OR EXISTS (
                                            SELECT 1
                                            FROM pam_ctc.purchase_order po
                                            WHERE po.id = pod.purchase_order_id
                                                AND po.order_status <![CDATA[<> ]]> 9
                                                AND po.deleted_flag = 0
                                        )
                                    )
                            ), 0) -
                            IFNULL((
                                SELECT SUM(s.transaction_quantity)
                                FROM pam_basedata.storage s
                                INNER JOIN pam_basedata.storage_inventory si
                                    ON s.subinventory_code = si.secondary_inventory_name
                                WHERE s.deleted_flag = 0
                                    AND s.locator <![CDATA[<> ]]> ''
                                    AND si.type = '01'
                                    AND s.locator LIKE CONCAT(project.code, '%')
                                    AND s.organization_id = projectProfit.storage_id
                                    AND s.segment1 = requirement.erp_code
                            ), 0) -
                            IFNULL((
                                SELECT SUM(CASE WHEN close_type = 1 THEN close_num ELSE -close_num END)
                                FROM pam_ctc.purchase_material_close_detail
                                WHERE purchase_requirement_id = requirement.id
                                    AND deleted_flag = 0
                            ), 0)
                        ) <![CDATA[<]]> 0 THEN 0
                        ELSE (
                            requirement.need_total -
                            IFNULL((
                                SELECT SUM(IFNULL(pod.order_num, 0) - IFNULL(pod.cancel_num, 0))
                                FROM pam_ctc.purchase_order_detail pod
                                WHERE pod.material_purchase_requirement_id = requirement.id
                                    AND pod.deleted_flag = 0
                                    AND pod.material_purchase_requirement_id IS NOT NULL
                                    AND pod.status <![CDATA[<>]]> 3
                                    AND (
                                        pod.purchase_order_id IS NULL
                                        OR EXISTS (
                                            SELECT 1
                                            FROM pam_ctc.purchase_order po
                                            WHERE po.id = pod.purchase_order_id
                                                AND po.order_status <![CDATA[<> ]]> 9
                                                AND po.deleted_flag = 0
                                        )
                                    )
                            ), 0) -
                            IFNULL((
                                SELECT SUM(s.transaction_quantity)
                                FROM pam_basedata.storage s
                                INNER JOIN pam_basedata.storage_inventory si
                                    ON s.subinventory_code = si.secondary_inventory_name
                                WHERE s.deleted_flag = 0
                                    AND s.locator <![CDATA[<>]]> ''
                                    AND si.type = '01'
                                    AND s.locator LIKE CONCAT(project.code, '%')
                                    AND s.organization_id = projectProfit.storage_id
                                    AND s.segment1 = requirement.erp_code
                            ), 0) -
                            IFNULL((
                                SELECT SUM(CASE WHEN close_type = 1 THEN close_num ELSE -close_num END)
                                FROM pam_ctc.purchase_material_close_detail
                                WHERE purchase_requirement_id = requirement.id
                                    AND deleted_flag = 0
                            ), 0)
                        )
                    END AS systemQuantity,

                    -- 未释放数量：总需求量减去已发布和关闭的数量
                    (
                        IFNULL(requirement.need_total, 0) -
                        IFNULL((
                            SELECT SUM(IFNULL(pod.order_num, 0) - IFNULL(pod.cancel_num, 0))
                            FROM pam_ctc.purchase_order_detail pod
                            WHERE pod.material_purchase_requirement_id = requirement.id
                                AND pod.deleted_flag = 0
                                AND pod.material_purchase_requirement_id IS NOT NULL
                                AND pod.status <![CDATA[<>]]> 3
                                AND (
                                    pod.purchase_order_id IS NULL
                                    OR EXISTS (
                                        SELECT 1
                                        FROM pam_ctc.purchase_order po
                                        WHERE po.id = pod.purchase_order_id
                                            AND po.order_status <![CDATA[<>]]> 9
                                            AND po.deleted_flag = 0
                                    )
                                )
                        ), 0) -
                        IFNULL((
                            SELECT SUM(CASE WHEN close_type = 1 THEN close_num ELSE -close_num END)
                            FROM pam_ctc.purchase_material_close_detail
                            WHERE purchase_requirement_id = requirement.id
                                AND deleted_flag = 0
                        ), 0)
                    ) AS unreleasedAmount,

                    -- 关闭数量：统计采购需求的关闭数量
                    IFNULL((
                        SELECT SUM(CASE WHEN close_type = 1 THEN close_num ELSE -close_num END)
                        FROM pam_ctc.purchase_material_close_detail
                        WHERE purchase_requirement_id = requirement.id
                            AND deleted_flag = 0
                    ), 0) AS closedQuantity
                FROM
                    pam_ctc.purchase_material_requirement requirement
                    INNER JOIN pam_ctc.project project ON requirement.project_id = project.id
                    INNER JOIN pam_ctc.project_profit projectProfit ON projectProfit.project_id = project.id
                WHERE
                    requirement.id IN
                    <foreach collection="list" item="id" open="(" separator="," close=")">
                            #{id}
                    </foreach>
    </select>
</mapper>
