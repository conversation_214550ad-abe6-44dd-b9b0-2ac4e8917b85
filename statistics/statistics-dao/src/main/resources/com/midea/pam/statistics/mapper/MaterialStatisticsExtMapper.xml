<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.MaterialStatisticsExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.basedata.entity.Material">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="item_id" jdbcType="BIGINT" property="itemId"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="item_info" jdbcType="VARCHAR" property="itemInfo"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="item_type" jdbcType="VARCHAR" property="itemType"/>
        <result column="item_status" jdbcType="VARCHAR" property="itemStatus"/>
        <result column="delist_flag" jdbcType="BIT" property="delistFlag" />
        <result column="buyer_number" jdbcType="VARCHAR" property="buyerNumber"/>
        <result column="buyer_id" jdbcType="VARCHAR" property="buyerId"/>
        <result column="buyer_round" jdbcType="VARCHAR" property="buyerRound"/>
        <result column="bond_flag" jdbcType="VARCHAR" property="bondFlag"/>
        <result column="code_name" jdbcType="VARCHAR" property="codeName"/>
        <result column="item_code_old" jdbcType="VARCHAR" property="itemCodeOld"/>
        <result column="fixed_lot_multiplier" jdbcType="VARCHAR" property="fixedLotMultiplier"/>
        <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType"/>
        <result column="pur_type" jdbcType="VARCHAR" property="purType"/>
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode"/>
        <result column="business_categories_id" jdbcType="BIGINT" property="businessCategoriesId"/>
        <result column="business_categories" jdbcType="VARCHAR" property="businessCategories"/>
        <result column="material_classification_id" jdbcType="BIGINT" property="materialClassificationId"/>
        <result column="material_classification" jdbcType="VARCHAR" property="materialClassification"/>
        <result column="material_type_id" jdbcType="BIGINT" property="materialTypeId"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="coding_middleclass" jdbcType="VARCHAR" property="codingMiddleclass" />
        <result column="coding_middleclass_id" jdbcType="BIGINT" property="codingMiddleclassId" />
        <result column="machining_part_type" jdbcType="VARCHAR" property="machiningPartType" />
        <result column="material" jdbcType="VARCHAR" property="material" />
        <result column="unit_weight" jdbcType="DECIMAL" property="unitWeight" />
        <result column="material_processing" jdbcType="VARCHAR" property="materialProcessing" />
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="delete_flag" jdbcType="BIT" property="deleteFlag"/>
        <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
        <result column="erp_message" jdbcType="VARCHAR" property="erpMessage"/>
        <result column="material_category" jdbcType="VARCHAR" property="materialCategory" />
        <result column="figure_number" jdbcType="VARCHAR" property="figureNumber" />
        <result column="chart_version" jdbcType="VARCHAR" property="chartVersion" />
        <result column="brand_material_code" jdbcType="VARCHAR" property="brandMaterialCode" />
        <result column="or_spare_parts_mask" jdbcType="VARCHAR" property="orSparePartsMask" />
    </resultMap>
    <resultMap id="BaseResultDtoMap" type="com.midea.pam.common.basedata.dto.MaterialDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="organization_id" jdbcType="BIGINT" property="organizationId"/>
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName"/>
        <result column="item_id" jdbcType="BIGINT" property="itemId"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="item_info" jdbcType="VARCHAR" property="itemInfo"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="item_type" jdbcType="VARCHAR" property="itemType"/>
        <result column="item_type_name" jdbcType="VARCHAR" property="itemTypeName"/>
        <result column="item_status" jdbcType="VARCHAR" property="itemStatus"/>
        <result column="delist_flag" jdbcType="BIT" property="delistFlag" />
        <result column="item_status_name" jdbcType="VARCHAR" property="itemStatusName"/>
        <result column="buyer_number" jdbcType="VARCHAR" property="buyerNumber"/>
        <result column="buyer_id" jdbcType="VARCHAR" property="buyerId"/>
        <result column="buyer_round" jdbcType="VARCHAR" property="buyerRound"/>
        <result column="bond_flag" jdbcType="VARCHAR" property="bondFlag"/>
        <result column="code_name" jdbcType="VARCHAR" property="codeName"/>
        <result column="item_cost" jdbcType="DECIMAL" property="itemCost"/>
        <result column="material_cost_type" jdbcType="INTEGER" property="materialCostType"/>
        <result column="item_code_old" jdbcType="VARCHAR" property="itemCodeOld"/>
        <result column="fixed_lot_multiplier" jdbcType="VARCHAR" property="fixedLotMultiplier"/>
        <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType"/>
        <result column="pur_type" jdbcType="VARCHAR" property="purType"/>
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode"/>
        <result column="business_categories_id" jdbcType="BIGINT" property="businessCategoriesId"/>
        <result column="business_categories" jdbcType="VARCHAR" property="businessCategories"/>
        <result column="material_classification_id" jdbcType="BIGINT" property="materialClassificationId"/>
        <result column="material_classification" jdbcType="VARCHAR" property="materialClassification"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="minimum_order_quantity" jdbcType="BIGINT" property="minimumOrderQuantity" />
        <result column="receving_subinventory" jdbcType="VARCHAR" property="recevingSubinventory" />
        <result column="shelves" jdbcType="VARCHAR" property="shelves" />
        <result column="sourcer" jdbcType="VARCHAR" property="sourcer" />
        <result column="safety_stock_quantity" jdbcType="BIGINT" property="safetyStockQuantity" />
        <result column="material_type_id" jdbcType="BIGINT" property="materialTypeId"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="coding_middleclass" jdbcType="VARCHAR" property="codingMiddleclass" />
        <result column="coding_middleclass_id" jdbcType="BIGINT" property="codingMiddleclassId" />
        <result column="machining_part_type" jdbcType="VARCHAR" property="machiningPartType" />
        <result column="material" jdbcType="VARCHAR" property="material" />
        <result column="unit_weight" jdbcType="DECIMAL" property="unitWeight" />
        <result column="material_processing" jdbcType="VARCHAR" property="materialProcessing" />
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="delete_flag" jdbcType="BIT" property="deleteFlag"/>
        <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
        <result column="erp_message" jdbcType="VARCHAR" property="erpMessage"/>
        <result column="material_category" jdbcType="VARCHAR" property="materialCategory" />
        <result column="figure_number" jdbcType="VARCHAR" property="figureNumber" />
        <result column="chart_version" jdbcType="VARCHAR" property="chartVersion" />
        <result column="brand_material_code" jdbcType="VARCHAR" property="brandMaterialCode" />
        <result column="or_spare_parts_mask" jdbcType="VARCHAR" property="orSparePartsMask" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, organization_id, item_id, item_code, item_info, unit, item_type, item_status,delist_flag,
        buyer_number, buyer_id, buyer_round, bond_flag, code_name, item_code_old, fixed_lot_multiplier,
        inventory_type, pur_type, pam_code, business_categories_id, business_categories,
        material_classification_id, material_classification, material_type_id, material_type,
        machining_part_type, material, unit_weight, material_processing,
        name, model, brand, create_by, create_at, update_by, update_at, delete_flag, erp_code, erp_message
    </sql>

    <sql id="Where_Clause">
        ma.delete_flag=0
        <if test="itemCode != null">
            and ma.item_code like concat('%', #{itemCode}, '%')
        </if>
        <if test="pamCode != null">
            and ma.pam_code like concat('%', #{pamCode}, '%')
        </if>
        <if test="itemInfo != null">
            and ma.item_info like concat('%', #{itemInfo}, '%')
        </if>
        <if test="itemTypes != null and itemTypes.size() > 0">
            and ma.item_type is not null and ma.item_type in
            <foreach collection="itemTypes" item="itemtype" index="index" open="(" separator="," close=")">
                #{itemtype}
            </foreach>
        </if>
        <if test="itemStatuses != null and itemStatuses.size() > 0">
            and  ma.item_status is not null and ma.item_status in
            <foreach collection="itemStatuses" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="materialClassificationNames != null and materialClassificationNames.size() > 0">
            and ma.material_classification is not null and ma.material_classification in
            <foreach collection="materialClassificationNames" item="cfname" index="index" open="(" separator="," close=")">
                #{cfname}
            </foreach>
        </if>
        <if test="codingMiddleclassNames != null and codingMiddleclassNames.size() > 0">
            and ma.coding_middleclass is not null and ma.coding_middleclass in
            <foreach collection="codingMiddleclassNames" item="codingMiddleclassId" index="index" open="(" separator="," close=")">
                #{codingMiddleclassId}
            </foreach>
        </if>
        <if test="materialTypeIds != null and materialTypeIds.size() > 0">
            and ma.material_type is not null and ecr.id in
            <foreach collection="materialTypeIds" item="typeid" index="index" open="(" separator="," close=")">
                #{typeid}
            </foreach>
        </if>
        <if test="name != null">
            and ma.name like concat('%', #{name}, '%')
        </if>
        <if test="model != null">
            and ma.model like concat('%', #{model}, '%')
        </if>
        <if test="brand != null">
            and ma.brand like concat('%', #{brand}, '%')
        </if>
        <if test="figureNumber != null and figureNumber != ''">
            and ma.figure_number like concat('%', #{figureNumber}, '%')
        </if>
        <if test="brandMaterialCode != null and brandMaterialCode != ''">
            and ma.brand_material_code like concat('%', #{brandMaterialCode}, '%')
        </if>
        <if test="remark != null and remark != ''">
            and ma.remark like concat('%', #{remark}, '%')
        </if>
        <if test="delistFlag != null">
            and ma.delist_flag = #{delistFlag,jdbcType=BIT}
        </if>
        <if test="machiningPartTypes != null and machiningPartTypes.size() > 0">
            and d3.id is not null and d3.id in
            <foreach collection="machiningPartTypes" item="mptype" index="index" open="(" separator="," close=")">
                #{mptype}
            </foreach>
        </if>
        <if test="material != null">
            and ma.material like concat('%', #{material}, '%')
        </if>
        <if test="unitWeight != null">
            and ma.unit_weight like concat('%', #{unitWeight}, '%')
        </if>
        <if test="materialProcessing != null">
            and ma.material_processing like concat('%', #{materialProcessing}, '%')
        </if>
        <if test="ouIds != null and ouIds.size() > 0">
            and org.operating_unit_id in
            <foreach collection="ouIds" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>
        <if test="organizationIds != null and organizationIds.size() > 0">
            and ma.organization_id is not null and ma.organization_id in
            <foreach collection="organizationIds" item="orgid" index="index" open="(" separator="," close=")">
                #{orgid}
            </foreach>
        </if>
        <if test="createDateFrom != null">
            AND ma.create_at <![CDATA[ >= ]]> #{createDateFrom}
        </if>
        <if test="createDateTo != null">
            AND ma.create_at <![CDATA[ <= ]]> #{createDateTo}
        </if>
        <if test="creatorName != null">
            and creator.name like concat('%', #{creatorName}, '%')
        </if>
        <if test="updateDateFrom != null">
            AND ma.update_at <![CDATA[ >= ]]> #{updateDateFrom}
        </if>
        <if test="updateDateTo != null">
            AND ma.update_at <![CDATA[ <= ]]> #{updateDateTo}
        </if>
        <if test="updaterName != null">
            and updater.name like concat('%', #{updaterName}, '%')
        </if>
        <if test="recevingSubinventory != null">
            and ma.receving_subinventory like concat('%', #{recevingSubinventory}, '%')
        </if>
        <if test="shelves != null">
            and ma.shelves like concat('%', #{shelves}, '%')
        </if>
        <if test="sourcer != null">
            and ma.sourcer like concat('%', #{sourcer}, '%')
        </if>
        <if test="delistFlags != null and delistFlags.size() > 0">
            and ma.delist_flag in
            <foreach collection="delistFlags" item="delistFlag" index="index" open="(" separator="," close=")">
                #{delistFlag}
            </foreach>
        </if>

        <if test="pamCodeList != null and pamCodeList.size() > 0">
            and ma.pam_code in
            <foreach collection="pamCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </sql>

    <sql id="Join_Clause">
        left join pam_basedata.organization_rel org ON org.organization_id=ma.organization_id and ma.organization_id is not null and org.pam_enabled = 0
        left join pam_basedata.ltc_dict d3 on d3.name=ma.machining_part_type and d3.type='jiagongjian_type' and ma.machining_part_type is not null
        left join pam_basedata.ltc_user_info creator on ma.create_by=creator.id and ma.create_by is not null
        left join pam_basedata.ltc_user_info updater on ma.update_by=updater.id and ma.update_by is not null
        left join pam_ctc.erp_code_rule ecr on ma.material_type=ecr.coding_subclass and ma.material_type is not null
    </sql>

    <select id="materialList" parameterType="com.midea.pam.common.statistics.query.MaterialQuery" resultMap="BaseResultDtoMap">
        select  DISTINCT
        ma.id,
        org.organization_name,
        ma.organization_id,
        ma.item_id,
        ma.item_code,
        ma.item_info,
        ma.unit,
        ma.item_type,
        ma.item_status,
        ma.delist_flag,
        ma.buyer_number,
        ma.buyer_id,
        ma.buyer_round,
        ma.bond_flag,
        ma.code_name,
        ma.item_code_old,
        ma.fixed_lot_multiplier,
        ma.inventory_type,
        ma.pur_type,
        ma.pam_code,
        ma.business_categories_id,
        ma.business_categories,
        ma.material_classification_id,
        ma.material_classification,
        ma.material_type_id,
        ma.material_type,
        ma.coding_middleclass,
        ma.coding_middleclass_id,
        ma.machining_part_type,
        ma.material,
        ma.unit_weight,
        ma.material_processing,
        ma.name,
        ma.model,
        ma.brand,
        ma.create_by,
        creator.name as creator_name,
        ma.create_at,
        ma.update_by,
        updater.name as updater_name,
        ma.update_at,
        ma.delete_flag,
        ma.erp_code,
        ma.erp_message,
        ma.material_category,
        ma.minimum_order_quantity as minimum_order_quantity,
        ma.receving_subinventory as receving_subinventory,
        ma.shelves,
        ma.sourcer,
        ma.safety_stock_quantity as safety_stock_quantity,
        ma.remark,
        ma.figure_number,
        ma.chart_version,
        ma.brand_material_code,
        ma.or_spare_parts_mask
        from pam_basedata.material ma
        <include refid="Join_Clause" />

        where
        <include refid="Where_Clause" />

        <if test="firstMaterialId != null">
            and ma.id <![CDATA[ >= ]]> #{firstMaterialId} limit #{pageSize}
        </if>
    </select>

    <select id="getMaterialIdDtoList" parameterType="com.midea.pam.common.statistics.query.MaterialQuery" resultMap="BaseResultDtoMap">
        select
        DISTINCT ma.id
        from pam_basedata.material ma
        <include refid="Join_Clause" />
        where
        <include refid="Where_Clause" />
    </select>

    <select id="getNotDeletedFlagMaterialDtoList" parameterType="com.midea.pam.common.statistics.query.MaterialQuery" resultMap="BaseResultDtoMap">
        select
        id,
        organization_id,
        material_classification,
        delist_flag
        from pam_basedata.material
        where
        delete_flag = 0
        <if test="organizationIds != null and organizationIds.size() > 0">
            and organization_id is not null and organization_id in
            <foreach collection="organizationIds" item="orgid" index="index" open="(" separator="," close=")">
                #{orgid}
            </foreach>
        </if>
        <if test="pamCode != null">
            and pam_code = #{pamCode}
        </if>
    </select>

    <select id="materialListExport" parameterType="com.midea.pam.common.statistics.query.MaterialQuery" resultType="com.midea.pam.common.statistics.excelVo.MaterialExcelVO">
        select  DISTINCT
        ma.id,
        ma.pam_code as pamCode,

        case when TRIM(ma.item_code)=''
        then null
        else
        TRIM(ma.item_code) end as itemCode,

        ma.item_info as itemInfo,
        ma.delist_flag as delistFlag,
        ma.unit as unit,
        ma.item_status as itemStatus,
        ma.material_classification as materialClassification,
        ma.material_type as materialType,
        ma.coding_middleclass as codingMiddleclass,
        ma.coding_middleclass_id as codingMiddleclassId,
        ma.name as name,
        ma.model as model,
        ma.brand as brand,
        ma.machining_part_type as machiningPartType,
        ma.material as material,
        ma.unit_weight as unitWeight,
        ma.material_processing as materialProcessing,
        ma.minimum_order_quantity as minimumOrderQuantity,
        ma.receving_subinventory as recevingSubinventory,
        ma.shelves,
        ma.sourcer,
        ma.safety_stock_quantity as safetyStockQuantity,
        ma.item_type as itemType,
        ma.buyer_number as buyerNumber,
        ma.buyer_round as buyerRound,
        ma.inventory_type as inventoryType,
        ma.pur_type as purType,
        org.organization_name as organizationName,
        ma.create_at as create_at,
        creator.name as creatorName,
        ma.update_at,
        updater.name as updaterName,
        ma.remark,
        ma.figure_number as figureNumber,
        ma.chart_version as chartVersion,
        ma.brand_material_code as brandMaterialCode
        from pam_basedata.material ma
        <include refid="Join_Clause" />

        where
        <include refid="Where_Clause" />

    </select>

    <select id="count" parameterType="com.midea.pam.common.statistics.query.MaterialQuery" resultType="java.lang.Long">
        select
        count(0)
        from pam_basedata.material ma
        <include refid="Join_Clause" />

        where
        <include refid="Where_Clause" />

    </select>

    <select id="selectDictList" parameterType="com.midea.pam.common.basedata.query.LtcDictQuery" resultType="com.midea.pam.common.basedata.entity.Dict">
        select  DISTINCT
        dict.*
        from pam_basedata.ltc_dict dict

        where
        1=1
        <if test="type != null">
            and dict.type = #{type}
        </if>
        <if test="deletedFlag != null">
            and dict.deleted_flag = #{deletedFlag}
        </if>
        and dict.code in (
            <if test='type == "material_item_type"'  >
                select DISTINCT ma.item_type
                from pam_basedata.material ma
                where ma.delete_flag=0
            </if>
            <if test='type == "material_item_status"'  >
                select DISTINCT ma.item_status
                from pam_basedata.material ma
                where ma.delete_flag=0
            </if>
        )

    </select>

    <select id="getMaterialCodeRuleConfig" resultType="java.lang.String">
        select code from pam_basedata.ltc_dict
        where type = 'ITEM_RULE' and deleted_flag != 1 and code is not null
        and name = (
            select value from pam_ctc.organization_custom_dict
            where org_from = 'company' and deleted_flag != 1 and org_id = #{unitId}
            and name = (
                select name from pam_basedata.ltc_dict
                where type = 'organization_custom_dict'
                  and code = 'ITEM_RULE' and deleted_flag != 1
                )
        )
    </select>

    <select id="sameMaterialCodeRuleUnits" resultType="java.lang.Long">
        select org_id from pam_ctc.organization_custom_dict
        where deleted_flag = false and (expiry_at is null or current_timestamp() &lt; expiry_at)
        and value = (
            select value from pam_ctc.organization_custom_dict
            where org_from = 'company' and deleted_flag != 1 and org_id = #{unitId}
            and name = (
                select name from pam_basedata.ltc_dict
                where type = 'organization_custom_dict'
                and code = 'ITEM_RULE' and deleted_flag != 1
            )
        )
    </select>

    <select id="getAllUnitByUsingMaterialCodeRule" resultType="com.midea.pam.common.basedata.entity.Unit">
        select
        u.id,
        u.unit_name unitName
        from
        pam_basedata.unit u left join pam_ctc.organization_custom_dict ocd on u.id = ocd.org_id
        where
        u.parent_id is null and ocd.name ='物料编码规则' and ocd.value =#{materialCodeRule}
    </select>

    <select id="countMaterialAdjustTodo" parameterType="com.midea.pam.common.ctc.dto.MaterialAdjustHeaderDTO" resultType="java.lang.Long">
        select count(id) from pam_ctc.material_adjust_header
        where status = #{status} and create_at <![CDATA[ >= ]]> #{createAtBegin} and create_at <![CDATA[<= ]]> #{createAtEnd} and deleted_flag = 0
    </select>

    <select id="getPamCodeCrossOrganizationalMaterialRequirement" resultType="java.lang.String">
        select
            distinct m.pam_code
        from
            pam_basedata.material m
        inner join pam_basedata.material m2 on
            m2.pam_code = m.pam_code
            and m2.delete_flag = 0
            and m2.organization_id <![CDATA[<> ]]> m.organization_id
            and m2.delist_flag = 0
        where
            m.delete_flag = 0
            and m.organization_id is not null
            and m.pam_code is not null
            and m.pam_code <![CDATA[<> ]]> ''
            and m.delist_flag = 1
    </select>

    <select id="countCrossOrganizationalMaterialRequirement" resultType="java.lang.Long">
        select
        count(id)
        from pam_basedata.material
        where delete_flag = 0
        and pam_code in
        <foreach collection="pamCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>