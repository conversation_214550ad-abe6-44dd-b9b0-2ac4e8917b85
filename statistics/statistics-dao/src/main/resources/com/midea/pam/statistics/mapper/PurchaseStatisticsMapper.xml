<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.PurchaseStatisticsMapper">

    <resultMap id="ProgressMap" type="com.midea.pam.common.ctc.dto.PurchaseContractProgressDto">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="purchase_contract_id" jdbcType="BIGINT" property="purchaseContractId" />
        <result column="progress_code" jdbcType="VARCHAR" property="progressCode" />
        <result column="progress_status" jdbcType="INTEGER" property="progressStatus" />
        <result column="excluding_tax_amount" jdbcType="DECIMAL" property="excludingTaxAmount" />
        <result column="execute_amount" jdbcType="DECIMAL" property="executeAmount" />
        <result column="execute_percent" jdbcType="DECIMAL" property="executePercent" />
        <result column="execute_amount_total" jdbcType="DECIMAL" property="executeAmountTotal" />
        <result column="execute_percent_total" jdbcType="DECIMAL" property="executePercentTotal" />
        <result column="progress_start_time" jdbcType="TIMESTAMP" property="progressStartTime" />
        <result column="progress_end_time" jdbcType="TIMESTAMP" property="progressEndTime" />
        <result column="approve_by" jdbcType="BIGINT" property="approveBy" />
        <result column="approve_at" jdbcType="TIMESTAMP" property="approveAt" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="collection_status" jdbcType="INTEGER" property="collectionStatus" />
        <result column="conversion_type" jdbcType="VARCHAR" property="conversionType" />
        <result column="conversion_date" jdbcType="TIMESTAMP" property="conversionDate" />
        <result column="conversion_rate" jdbcType="DECIMAL" property="conversionRate" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="erp_status" jdbcType="INTEGER" property="erpStatus" />
        <result column="erp_message" jdbcType="VARCHAR" property="erpMessage" />
        <result column="gl_period" jdbcType="VARCHAR" property="glPeriod" />
        <result column="gl_date" jdbcType="TIMESTAMP" property="glDate" />
        <result column="daily_batch_num" jdbcType="VARCHAR" property="dailyBatchNum" />
        <result column="daily_batch_name" jdbcType="VARCHAR" property="dailyBatchName" />
        <result column="if_delivery_node" jdbcType="INTEGER" property="ifDeliveryNode" />
        <result column="if_delivery_delay" jdbcType="INTEGER" property="ifDeliveryDelay" />
        <result column="delivery_delay_days" jdbcType="DECIMAL" property="deliveryDelayDays" />
        <result column="delivery_date" jdbcType="TIMESTAMP" property="deliveryDate" />
    </resultMap>

    <!-- 查询采购合同列表 -->
    <select id="listPurchaseContract" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.vo.PurchaseContractVO">
            select
            	pc.id,
            	pc.code,
            	pc.name,
            	pc.status,
            	pc.project_id as projectId,
            	pc.ou_id as ouId,
            	pc.type_id as typeId,
            	pc.type_name as typeName,
            	pc.legal_affairs_code as legalAffairsCode,
            	pc.vendor_id as vendorId,
            	pc.vendor_code as vendorCode,
            	pc.vendor_name as vendorName,
            	pc.erp_vendor_site_id as erpVendorSiteId,
            	pc.vendor_site_code as vendorSiteCode,
            	pc.amount,
            	pc.excluding_tax_amount as excludingTaxAmount,
            	pc.currency,
            	pc.start_time as startTime,
            	pc.end_time as endTime,
            	pc.manager,
            	pc.manager_name as managerName,
            	pc.purchasing_follower as purchasingFollower,
            	pc.purchasing_follower_name as purchasingFollowerName,
            	pc.annex,
            	pc.filing_date as filingDate,
            	pc.carryover_flag as carryoverFlag,
            	pc.deleted_flag as deletedFlag,
            	pc.create_by as createBy,
            	pc.create_at as createAt,
            	pc.update_by as updateBy,
            	pc.update_at as updateAt,
            	pc.legal_contract_num as legalContractNum,
            	pc.tax_id as taxId,
            	pc.conversion_rate as conversionRate,
            	pc.tax_rate as taxRate,
            	pc.delivery_date as deliveryDate,
            	pc.contract_status as contractStatus,
            	pc.track_lead_time as trackLeadTime,
            	pc.track_lead_time_remark as trackLeadTimeRemark,
                pc.is_synchronize_legal_system_flag as isSynchronizeLegalSystemFlag,
                pc.original_contract_annex as originalContractAnnex,
                pc.file_attachment_id as fileAttachmentId,
                pc.gle_sign_flag as gleSignFlag,
                pc.contract_terms_flg as contractTermsFlg,
                pc.contract_terms_ids as contractTermsIds,
            	p.name as projectName,
            	p.code as projectCode,
            	pc.conversion_type as conversionType,
            	lc.doc_subject as docSubject,
            	ou.operating_unit_name as ouName,
            	pc.conversion_date as conversionDate,
            	IFNULL(ROUND(t.punishmentAmount, 2), 0) as punishmentAmount,
            	IFNULL(ROUND(t2.actualAmount, 2), 0) as actualAmount,
            	(IFNULL(ROUND(t2.remainingAmount, 2), 0) - IFNULL(ROUND(t.punishmentAmount, 2), 0) - IFNULL(ROUND(t3.transitAmount, 2), 0)) as remainingAmount,
            	<!-- 已开税票金额（含税） -->
            	ROUND(IFNULL(pid.tax_included_price, 0), 2) as paymentInvoiceMoney,
            	<!-- 累计进度执行金额（不含税） -->
            	IFNULL((select sum(pcp.execute_amount) from pam_ctc.purchase_contract_progress pcp where pcp.purchase_contract_id = pc.id and pcp.progress_status = 2 and pcp.deleted_flag = 0), 0) as executeAmountTotal,
            	<!-- 累计进度执行百分比 -->
            	IFNULL(ROUND(pc.execute_contract_percent_total, 2), 0) as executeContractPercentTotal,
            	<!-- 质检报告数量 -->
            (
            	select
            		count(1)
            	from
            		pam_ctc.purchase_contract_quality_report qr
            	where
            		qr.deleted_flag = 0
            		and qr.purchase_contract_id = pc.id) as qualityReportNumber,
            	ROUND(IFNULL(pin.total_invoice_included_price, 0), 2) as entrypayableInvoiceAmount,
            	ROUND(IFNULL(pc.amount, 0) - IFNULL(pid.tax_included_price, 0), 2) as unPaymentInvoiceMoney,
            	ROUND(IFNULL(pid.tax_included_price, 0) - IFNULL(pin.total_invoice_included_price, 0), 2) as unEntrypayableInvoiceAmount,
            	(IFNULL(t2.actualAmount, 0) + IFNULL(t.punishmentAmount, 0))/ IFNULL(pc.amount, 0) as paymentRatio,
            	ROUND(IFNULL(pin.total_invoice_included_price, 0) - (IFNULL(t2.actualAmount, 0)), 2) as amountInArrear
            from
            	pam_ctc.purchase_contract pc
            left join pam_ctc.project p
            on
            	pc.project_id = p.id
            	and p.deleted_flag = 0
            left join pam_ctc.legal_contract lc
            on
            	pc.legal_affairs_code = lc.fd_number
            	and lc.deleted_flag = 0
            left join pam_basedata.operating_unit ou
            on
            	pc.ou_id = ou.id
            left join
            (
            	select
            		SUM(IFNULL(pr.penalty_amount, 0)) + punishment.total_amount_with_tax as punishmentAmount,
            		pc.id
            	from
            		pam_ctc.purchase_contract pc
            	left join pam_ctc.payment_apply pr
            on
            		pc.id = pr.purchase_contract_id
            	left join (
            		select
            			purchase_contract_id,
            			SUM(
            case
            when tax_rate is not null and tax_rate != ''
            then amount * (1 + cast(replace(tax_rate, '%', '') as DECIMAL(10, 2))/ 100)
            else amount
            end
            ) as total_amount_with_tax
            		from
            			pam_ctc.purchase_contract_punishment
            		where
            			deleted_flag = 0
            			and status = 6
            		group by
            			purchase_contract_id
            		order by
            			purchase_contract_id) punishment on
            		pc.id = punishment.purchase_contract_id
            	where
            		pc.deleted_flag = 0
            		and pr.audit_status in (1, 2, 3, 4)
            			and pr.gceb_status <![CDATA[ <> ]]> 2
            			and pr.erp_status <![CDATA[ <> ]]> 2
            			and (pr.deleted_flag = 0
            				or pr.deleted_flag is null)
            		group by
            			pc.id) t
            on
            	t.id = pc.id
            left join
            (
            	select
            		SUM(IFNULL(pp.actual_amount, 0)) as actualAmount,
            		(pc.amount - SUM(IFNULL(pp.actual_amount, 0))) as remainingAmount,
            		pc.id
            	from
            		pam_ctc.purchase_contract pc
            	left join pam_ctc.payment_plan pp
            on
            		pc.id = pp.contract_id
            		and pp.deleted_flag = 0
            	where
            		pc.deleted_flag = 0
            	group by
            		pc.id) t2
            on
            	t2.id = pc.id
            left join (
            	select
            		IFNULL(SUM(pid.tax_included_price), 0) as tax_included_price,
            		pid.purchase_contract_id
            	from
            		pam_ctc.payment_invoice_detail pid
            	where
            		pid.deleted_flag = 0
            	group by
            		pid.purchase_contract_id
            ) pid on
            	pid.purchase_contract_id = pc.id
            left join (
            	select
            		IFNULL(SUM(pin.total_invoice_included_price), 0) as total_invoice_included_price,
            		pin.purchase_contract_id
            	from
            		pam_ctc.payment_invoice pin
            	where
            		pin.status = 2
            		and pin.erp_status = 1
            		and pin.source = 0
            		and pin.deleted_flag = 0
            	group by
            		pin.purchase_contract_id
            ) pin on
            	pin.purchase_contract_id = pc.id
            left join
            (
            	select
            		IFNULL(sum(pr.tax_pay_included_price-ifnull(pr.total_cancel_amount, 0)-ifnull(pr.really_pay_included_price, 0)), 0) as transitAmount,
            		pc.id
            	from
            		pam_ctc.purchase_contract pc
            	left join pam_ctc.payment_apply pr
            on
            		pc.id = pr.purchase_contract_id
            	where
            		pc.deleted_flag = 0
            		and pr.audit_status != 5
            		and (pr.deleted_flag = 0
            			or pr.deleted_flag is null)
            	group by
            		pc.id) t3
            on
            	t3.id = pc.id
            where
            	pc.deleted_flag = 0
            <if test="code != null">
                and pc.code like concat('%', #{code}, '%')
            </if>
            <if test="typeNames!=null and typeNames.size>0">
                and pc.type_name in
                <foreach collection="typeNames" item="typeName" open="(" separator="," close=")">
                    #{typeName}
                </foreach>
            </if>
            <if test="legalContractNum != null">
                and pc.legal_contract_num like concat('%', #{legalContractNum}, '%')
            </if>
            <if test="name != null">
                and pc.name like concat('%', #{name}, '%')
            </if>
            <if test="vendorName != null">
                and pc.vendor_name like concat('%', #{vendorName}, '%')
            </if>
            <if test="vendorCode != null">
                and pc.vendor_code like concat('%', #{vendorCode}, '%')
            </if>
            <if test="projectName != null">
                and p.name like concat('%', #{projectName}, '%')
            </if>
            <if test="projectCode != null">
                and p.code like concat('%', #{projectCode}, '%')
            </if>
            <if test="purchasingFollowerName != null">
                and pc.purchasing_follower_name like concat('%', #{purchasingFollowerName}, '%')
            </if>
            <if test="statusList != null">
                and pc.status in
                <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="contractStatusList != null">
                and pc.contract_status in
                <foreach collection="contractStatusList" item="contractStatus" index="index" open="(" separator="," close=")">
                    #{contractStatus}
                </foreach>
            </if>
            <if test="ouNameList != null">
                and ou.operating_unit_name in
                <foreach collection="ouNameList" item="ouName" index="index" open="(" separator="," close=")">
                    #{ouName}
                </foreach>
            </if>
            <if test="filingStartDate != null">
                and pc.filing_date <![CDATA[>= ]]> #{filingStartDate}
            </if>
            <if test="filingEndDate != null">
                and pc.filing_date <![CDATA[<= ]]> #{filingEndDate}
            </if>
            <if test="createStartDate != null">
                and pc.create_at <![CDATA[>= ]]> #{createStartDate}
            </if>
            <if test="createEndDate != null">
                and pc.create_at <![CDATA[<= ]]> #{createEndDate}
            </if>
            <if test="deliveryStartDate != null">
                and pc.delivery_date <![CDATA[>= ]]> #{deliveryStartDate}
            </if>
            <if test="deliveryEndDate != null">
                and pc.delivery_date <![CDATA[<= ]]> #{deliveryEndDate}
            </if>
            <if test="trackLeadStartTime != null">
                and pc.track_lead_time <![CDATA[>= ]]> #{trackLeadStartTime}
            </if>
            <if test="trackLeadEndTime != null">
                and pc.track_lead_time <![CDATA[<= ]]> #{trackLeadEndTime}
            </if>
            <if test="trackLeadTimeRemark != null">
                and pc.track_lead_time_remark like concat('%', #{trackLeadTimeRemark}, '%')
            </if>
            <if test="executeContractPercentTotalMin != null">
                and pc.execute_contract_percent_total <![CDATA[>= ]]> #{executeContractPercentTotalMin}
            </if>
            <if test="executeContractPercentTotalMax != null">
                and pc.execute_contract_percent_total <![CDATA[<= ]]> #{executeContractPercentTotalMax}
            </if>
            <if test="qualityCompleted != null">
                and (select count(1) from pam_ctc.purchase_contract_quality_report qr where qr.deleted_flag = 0 and qr.purchase_contract_id = pc.id) > 0
            </if>
            <if test="qualityUncompleted != null">
                and (select count(1) from pam_ctc.purchase_contract_quality_report qr where qr.deleted_flag = 0 and qr.purchase_contract_id = pc.id) = 0
            </if>
            <if test="ouList != null">
                 and pc.ou_id in
                <foreach collection="ouList" item="ouId" index="index" open="(" separator="," close=")">
                   #{ouId}
                </foreach>
            </if>
            <if test="showMyContract">
                and (pc.purchasing_follower = #{userId}
                    or pc.create_by = #{userId}
                    or pc.manager = #{userId})
                <!--
                and p.unit_id in
                <foreach collection="authorUnits" item="unitId" index="index" open="(" separator="," close=")">
                    #{unitId}
                </foreach>
                -->
            </if>
            <if test="managerName!=null">
                and pc.manager_name like concat('%',#{managerName},'%')
            </if>
            <if test="punishmentFlag != null and punishmentFlag == 1">
                and pc.type_name != '点工采购'
            </if>
            <if test="isGleSign != null">
                <if test="isGleSign == true">
                    AND pc.gle_sign_flag = 1
                </if>
                <if test="isGleSign == false">
                    AND (pc.gle_sign_flag = 0 OR pc.gle_sign_flag IS NULL)
                </if>
            </if>
            order by pc.create_at desc
    </select>

    <select id="findDetailList" resultType="com.midea.pam.common.ctc.dto.PurchaseContractDetailDTO">
        select
        cd.id,
        cd.contract_id as contractId,
        cd.name,
        cd.model,
        cd.brand,
        cd.tax_id as taxId,
        cd.tax_rate as taxRate,
        cd.unit_price as unitPrice,
        cd.unit_code as unitCode,
        cd.unit_name as unitName,
        cd.number,
        cd.total_price as totalPrice,
        cd.no_tax_total_price as noTaxTotalPrice,
        cd.remark,
        cd.deleted_flag as deletedFlag,
        cd.create_by as createBy,
        cd.create_at as createAt,
        cd.update_by as updateBy,
        cd.update_at as updateAt,
        c.code as contractCode,
        c.name as contractName,
        p.code as projectCode,
        p.name as projectName
        from pam_ctc.purchase_contract_detail cd
        left join pam_ctc.purchase_contract c on cd.contract_id = c.id and c.deleted_flag = 0
        left join pam_ctc.project p on c.project_id = p.id and p.deleted_flag = 0
        where cd.deleted_flag = 0
        and cd.contract_id in
        <foreach collection="contractIds" item="contractId" index="index" open="(" separator="," close=")">
            #{contractId}
        </foreach>
    </select>
    <select id="findPaymentPlan" resultType="com.midea.pam.common.ctc.dto.PaymentPlanDTO">
        select
        p.id,
        p.contract_id as contractId,
        IFNULL(ROUND(p.proportion, 0), 0) AS proportion,
        p.amount,
        p.date,
        p.requirement,
        p.payment_method_id as paymentMethodId,
        p.payment_method_name as paymentMethodName,
        p.advance_payment_flag as advancePaymentFlag,
        p.milestone_id as milestoneId,
        p.actual_amount as actualAmount,
        p.code,
        p.num,
        p.status,
        p.deleted_flag as deletedFlag,
        p.create_by as createBy,
        p.create_at as createAt,
        p.update_by as updateBy,
        p.update_at as updateAt,
        c.name as purchaseContractName,
        c.code as purchaseContractCode,
        pt.name as milestoneName,
        pt.start_time as milestoneEndTime,
        pt.status as milestoneStatus,
        IFNULL(t.penalty_amount, 0) AS totalpenaltyAmount,
        (p.amount-p.actual_amount -IFNULL(t.penalty_amount, 0) -IFNULL(t3.transitAmount, 0)) as remainingAmount,
        c.status as purchaseContractStatusName,
        c.vendor_name as vendorName,
        c.vendor_code as vendorCode,
        pj.name as projectName,
        pj.code as projectCode,
        (p.amount-p.actual_amount) as surplusAmount,
        IFNULL(t3.transitAmount, 0) as transitAmount,
        p.allocation_punishment_amount_with_tax as allocationPunishmentAmountWithTax
        from pam_ctc.payment_plan p
        left join pam_ctc.purchase_contract c on p.contract_id = c.id and c.deleted_flag = 0
        left join pam_ctc.project_milepost pt on p.milestone_id = pt.id and pt.deleted_flag = 0
        LEFT JOIN (select sum( ifnull( pa.penalty_amount, 0 ) ) as penalty_amount, payment_plan_id from pam_ctc.payment_apply pa
        where pa.audit_status in (1,2,3,4)
        AND pa.gceb_status <![CDATA[ <> ]]> 2
        AND pa.erp_status <![CDATA[ <> ]]> 2
        AND (pa.deleted_flag = 0 or pa.deleted_flag is null) group by pa.payment_plan_id ) t ON p.id = t.payment_plan_id
        left join (
        select
        IFNULL(sum(pr.tax_pay_included_price-ifnull(pr.total_cancel_amount, 0)-ifnull(pr.really_pay_included_price, 0)), 0) as transitAmount,
        pr.payment_plan_id
        from
        pam_ctc.payment_apply pr
        where
        pr.audit_status != 5
        and (pr.deleted_flag = 0
        or pr.deleted_flag is null)
        group by
        pr.payment_plan_id) t3 on 
        t3.payment_plan_id = p.id
        left join pam_ctc.project pj on c.project_id = pj.id and pj.deleted_flag = 0
        where 1 = 1
        and p.deleted_flag = 0
        and c.id in
        <foreach collection="contractIds" item="contractId" index="index" open="(" separator="," close=")">
            #{contractId}
        </foreach>
    </select>
    <select id="findByContractMaterialList" resultType="com.midea.pam.common.ctc.entity.ProjectContractBudgetMaterial">
        select
            p.id,
            p.project_id as projectId,
            p.code,
            p.material_id as materialId,
            p.excluding_tax_amount as excludingTaxAmount,
            p.name,
            p.unit,
            p.number,
            p.money,
            p.status
        from
            pam_ctc.project_contract_budget_material p
        where
            p.project_id = #{projectId,jdbcType=BIGINT} and p.code = #{code,jdbcType=VARCHAR} and p.deleted_flag = false and p.status = false
    </select>
    <select id="findById" resultType="com.midea.pam.common.ctc.entity.ProjectBudgetMaterial">
        select p.price_total as priceTotal from pam_ctc.project_budget_material p where p.id = #{id,jdbcType=BIGINT} and p.deleted_flag = false
    </select>

    <select id="listPurchaseContractMaterialBudget" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.vo.PurchaseContractMaterialBudgetVo">
        with t as (
        <!-- BUG2022072034193 按pam编码能反向查找下在哪个项目和合同号 -->
        select
            requirement.pam_code as pamCode,
            requirement.materiel_descr as materialName,
            requirement.requirement_code as requirementCode,
            requirement.id as requirementId,
            budget.number,
            null  as price,
            null as priceTotal,
            <if test="isShowAmount">
                budget.total_price as money,
            </if>
            pc.vendor_name as vendorName,
            pc.vendor_code as vendorCode,
            project.id as projectId,
            project.code as projectCode,
            project.name as projectName,
            pc.id as purchaseContractId,
            pc.code as purchaseContactCode,
            pc.name as purchaseContractName,
            pc.status as purchaseContractStatus,
            pc.type_name as contractType,
            <if test="isShowAmount">
                cast(pc.amount as decimal(10, 2)) as purchaseContractAmount,
            </if>
            budget.purchase_contract_create_at as purchaseContractCreateAt,
            ou.id as ouId,
            ou.operating_unit_name as ouName,
            pc.purchasing_follower_name as purchasingFollowerName,
            budget.row,
            budget.budget_execute_percent_total as budgetExecutePercentTotal,
            project.wbs_enabled as wbsEnabled,
            budget.contract_approval_time as contractApprovalTime,
            budget.publish_time as publishTime
        from
            pam_ctc.purchase_contract_budget budget
            inner join pam_ctc.purchase_material_requirement requirement on
            requirement.id = budget.purchase_requirement_id
            and requirement.deleted_flag = 0
            left join pam_ctc.purchase_contract pc on
            pc.id = budget.purchase_contract_id
            and pc.deleted_flag = 0
            left join pam_ctc.project project on
            project.id = requirement.project_id
            and project.deleted_flag = 0
            left join pam_basedata.operating_unit ou on
            pc.ou_id = ou.id
        where
            budget.deleted_flag = 0
            and requirement.purchase_type = 2
        union all
        select
            pbm1.code as PamCode,
            pbm1.name as materialName,
            null as requirementCode,
            null as requirementId,
            pbm1.number,
            cast(pbm1.price as decimal(10,2)) as price,
            cast(pbm1.price_total as decimal(10,2)) as priceTotal,
            <if test="isShowAmount">
                pmd.money,
            </if>
            pmd.vendor_name as vendorName,
            pmd.vendor_code as vendorCode,
            p.id as projectId,
            p.code as projectCode,
            p.name as projectName,
            pmd.id as purchaseContractId,
            pmd.code as purchaseContactCode,
            pmd.name as purchaseContractName,
            pmd.status as purchaseContractStatus,
            pmd.type_name as contractType,
            <if test="isShowAmount">
                cast(pmd.amount as decimal(10,2)) as purchaseContractAmount,
            </if>
            pmd.create_at as purchaseContractCreateAt,
            ou.id as ouId,
            ou.operating_unit_name as ouName,
            pmd.purchasing_follower_name as purchasingFollowerName,
            null as row,
            null as budgetExecutePercentTotal,
            0 as wbsEnabled,
            pmd.contractApprovalTime,
            null publishTime
        from
            (select
                pcbm.material_id,
                pcbm.money,
                fi.update_at contractApprovalTime,
                pc.*
            from
                pam_ctc.project_contract_budget_material pcbm
            left join pam_ctc.purchase_contract pc
                on pc.id = pcbm.purchase_contract_id
            left join pam_system.form_instance fi
                on fi.form_instance_id = pc.id
                and fi.wf_status = 30
            where pcbm.deleted_flag = 0
                and pc.status in (2, 4, 5, 10, 7)
                and pc.deleted_flag = 0) pmd
        left join pam_ctc.project_budget_material pbm1
            on pmd.material_id = pbm1.id
        left join pam_ctc.project p
            on pmd.project_id = p.id
        left join pam_basedata.operating_unit ou
            on pmd.ou_id = ou.id
        )
        select t.* from t where 1=1
        <if test="pamCode != null">
            and t.pamCode like concat('%', #{pamCode}, '%')
        </if>
        <if test="materialName != null">
            and t.materialName like concat('%', #{materialName}, '%')
        </if>
        <if test="vendorName != null">
            and t.vendorName like concat('%', #{vendorName}, '%')
        </if>
        <if test="vendorCode != null">
            and t.vendorCode like concat('%', #{vendorCode}, '%')
        </if>
        <if test="projectCode != null">
            and t.projectCode like concat('%', #{projectCode}, '%')
        </if>
        <if test="projectName != null">
            and t.projectName like concat('%', #{projectName}, '%')
        </if>
        <if test="contractCode != null">
            and t.purchaseContactCode like concat('%', #{contractCode}, '%')
        </if>
        <if test="contractName != null">
            and t.purchaseContractName like concat('%', #{contractName}, '%')
        </if>
        <if test="purchasingFollowerName != null">
            and t.purchasingFollowerName like concat('%', #{purchasingFollowerName}, '%')
        </if>
        <if test="budgetExecutePercentTotalMin != null">
            and t.budgetExecutePercentTotal <![CDATA[>= ]]> #{budgetExecutePercentTotalMin}
        </if>
        <if test="budgetExecutePercentTotalMax != null">
            and t.budgetExecutePercentTotal <![CDATA[<= ]]> #{budgetExecutePercentTotalMax}
        </if>
        <if test="startDate != null">
            and t.purchaseContractCreateAt <![CDATA[>= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and t.purchaseContractCreateAt <![CDATA[<= ]]> #{endDate}
        </if>
        <if test="statusList != null">
            and t.purchaseContractStatus in
            <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="ouNameList != null">
            and t.ouName in
            <foreach collection="ouNameList" item="ouName" index="index" open="(" separator="," close=")">
                #{ouName}
            </foreach>
        </if>
        <if test="ouList != null">
            and t.ouId in
            <foreach collection="ouList" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>
        <if test="contractApprovalStartTime!=null">
            and t.contractApprovalTime &gt;= #{contractApprovalStartTime}
        </if>
        <if test="contractApprovalEndTime!=null">
            and t.contractApprovalTime &lt;= #{contractApprovalEndTime}
        </if>
        <if test="publishStartTime!=null">
            and t.publishTime &gt;= #{publishStartTime}
        </if>
        <if test="publishEndTime!=null">
            and t.publishTime &lt;= #{publishEndTime}
        </if>
        <if test="requirementCode!=null">
            and t.requirementCode like concat('%',#{requirementCode},'%')
        </if>
        order by t.purchaseContractCreateAt desc
    </select>

    <!-- 获取采购合同关联预算的WBS号 -->
    <select id="findWbsByPurchaseContract" parameterType="long" resultType="string">
        select
            distinct t2.wbs_summary_code
        from
            pam_ctc.purchase_contract_budget t1,
            pam_ctc.purchase_material_requirement t2
        where
            t1.purchase_requirement_id = t2.id
            and t1.deleted_flag = 0
            and t2.deleted_flag = 0
            and t1.purchase_contract_id = #{purchaseContractId}
    </select>

    <select id="findProgressList" resultMap="ProgressMap">
        select * from pam_ctc.purchase_contract_progress where 1=1
        <if test="contractIds != null and contractIds.size > 0">
            and purchase_contract_id in
            <foreach collection="contractIds" item="contractId" index="index" open="(" separator="," close=")">
                #{contractId}
            </foreach>
        </if>
    </select>

    <select id="findPunishmentTotalAmountByContractId" resultType="com.midea.pam.common.ctc.entity.PurchaseContractPunishment">
        select
          pcp.purchase_contract_id as purchaseContractId,
          sum(pcp.amount) as amount
        from
          pam_ctc.purchase_contract_punishment pcp
        where
          pcp.deleted_flag = 0
          and
          pcp.status = 6
          <if test="contractIds != null and contractIds.size() != 0">
            and pcp.purchase_contract_id in
            <foreach collection="contractIds" item="contractId" index="index" open="(" separator="," close=")">
                #{contractId}
            </foreach>
          </if>
         group by pcp.purchase_contract_id
    </select>

    <select id="findPayMentPenaltyAmountByContractIds" resultType="java.util.Map">
          select
            pc.id as contractId,
          	sum(ppp.amount) as amount
          from
          	pam_ctc.purchase_contract pc
          left join pam_ctc.payment_apply pa on
          	pc.id = pa.purchase_contract_id
          left join pam_ctc.payment_penalty_profit ppp on
          	ppp.payment_apply_id = pa.id
          where
           pa.deleted_flag = 0
           and
           ppp.deleted_flag  = 0
           <if test="contractIds != null and contractIds.size() != 0">
               and pc.id in
               <foreach collection="contractIds" item="contractId" index="index" open="(" separator="," close=")">
                   #{contractId}
               </foreach>
           </if>
           group by ppp.payment_apply_id
    </select>

    <select id="findPunishmentTotalAmountByContractIds" resultType="java.util.Map">
             select
                pcp.purchase_contract_id as contractId,
             	sum(pcp.amount) as amount
             from
             	pam_ctc.purchase_contract_punishment pcp
             where
             	pcp.deleted_flag = 0
             	and pcp.status = 6
                <if test="contractIds != null and contractIds.size() != 0">
                    and pcp.purchase_contract_id in
                    <foreach collection="contractIds" item="contractId" index="index" open="(" separator="," close=")">
                        #{contractId}
                    </foreach>
                </if>
             group by
             	pcp.purchase_contract_id
    </select>


</mapper>