<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ProjectWbsBudgetVersionDetailMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersionDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="version_id" jdbcType="BIGINT" property="versionId" />
    <result column="project_wbs_budget_id" jdbcType="BIGINT" property="projectWbsBudgetId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="activity_order_no" jdbcType="VARCHAR" property="activityOrderNo" />
    <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="activity_type" jdbcType="VARCHAR" property="activityType" />
    <result column="wbs_full_code" jdbcType="VARCHAR" property="wbsFullCode" />
    <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
    <result column="wbs_last_code" jdbcType="VARCHAR" property="wbsLastCode" />
    <result column="dynamic_wbs_template_rule_ids" jdbcType="VARCHAR" property="dynamicWbsTemplateRuleIds" />
    <result column="dynamic_fields" jdbcType="VARCHAR" property="dynamicFields" />
    <result column="dynamic_values" jdbcType="VARCHAR" property="dynamicValues" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="parent_wbs_id" jdbcType="BIGINT" property="parentWbsId" />
    <result column="parent_activity_id" jdbcType="BIGINT" property="parentActivityId" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="fee_type_id" jdbcType="BIGINT" property="feeTypeId" />
    <result column="fee_type_name" jdbcType="VARCHAR" property="feeTypeName" />
    <result column="fee_sync_ems" jdbcType="TINYINT" property="feeSyncEms" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, version_id, project_wbs_budget_id, project_id, project_code, description, activity_order_no, 
    activity_code, activity_name, activity_type, wbs_full_code, wbs_summary_code, wbs_last_code, 
    dynamic_wbs_template_rule_ids, dynamic_fields, dynamic_values, price, parent_wbs_id, 
    parent_activity_id, version, fee_type_id, fee_type_name, fee_sync_ems, create_by, 
    create_at, update_by, update_at, deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersionDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_wbs_budget_version_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_wbs_budget_version_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_wbs_budget_version_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersionDetail">
    insert into project_wbs_budget_version_detail (id, version_id, project_wbs_budget_id, 
      project_id, project_code, description, 
      activity_order_no, activity_code, activity_name, 
      activity_type, wbs_full_code, wbs_summary_code, 
      wbs_last_code, dynamic_wbs_template_rule_ids, 
      dynamic_fields, dynamic_values, price, 
      parent_wbs_id, parent_activity_id, version, 
      fee_type_id, fee_type_name, fee_sync_ems, 
      create_by, create_at, update_by, 
      update_at, deleted_flag)
    values (#{id,jdbcType=BIGINT}, #{versionId,jdbcType=BIGINT}, #{projectWbsBudgetId,jdbcType=BIGINT}, 
      #{projectId,jdbcType=BIGINT}, #{projectCode,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{activityOrderNo,jdbcType=VARCHAR}, #{activityCode,jdbcType=VARCHAR}, #{activityName,jdbcType=VARCHAR}, 
      #{activityType,jdbcType=VARCHAR}, #{wbsFullCode,jdbcType=VARCHAR}, #{wbsSummaryCode,jdbcType=VARCHAR}, 
      #{wbsLastCode,jdbcType=VARCHAR}, #{dynamicWbsTemplateRuleIds,jdbcType=VARCHAR}, 
      #{dynamicFields,jdbcType=VARCHAR}, #{dynamicValues,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, 
      #{parentWbsId,jdbcType=BIGINT}, #{parentActivityId,jdbcType=BIGINT}, #{version,jdbcType=BIGINT}, 
      #{feeTypeId,jdbcType=BIGINT}, #{feeTypeName,jdbcType=VARCHAR}, #{feeSyncEms,jdbcType=TINYINT}, 
      #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP}, #{deletedFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersionDetail">
    insert into project_wbs_budget_version_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="versionId != null">
        version_id,
      </if>
      <if test="projectWbsBudgetId != null">
        project_wbs_budget_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="activityOrderNo != null">
        activity_order_no,
      </if>
      <if test="activityCode != null">
        activity_code,
      </if>
      <if test="activityName != null">
        activity_name,
      </if>
      <if test="activityType != null">
        activity_type,
      </if>
      <if test="wbsFullCode != null">
        wbs_full_code,
      </if>
      <if test="wbsSummaryCode != null">
        wbs_summary_code,
      </if>
      <if test="wbsLastCode != null">
        wbs_last_code,
      </if>
      <if test="dynamicWbsTemplateRuleIds != null">
        dynamic_wbs_template_rule_ids,
      </if>
      <if test="dynamicFields != null">
        dynamic_fields,
      </if>
      <if test="dynamicValues != null">
        dynamic_values,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="parentWbsId != null">
        parent_wbs_id,
      </if>
      <if test="parentActivityId != null">
        parent_activity_id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="feeTypeId != null">
        fee_type_id,
      </if>
      <if test="feeTypeName != null">
        fee_type_name,
      </if>
      <if test="feeSyncEms != null">
        fee_sync_ems,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="versionId != null">
        #{versionId,jdbcType=BIGINT},
      </if>
      <if test="projectWbsBudgetId != null">
        #{projectWbsBudgetId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="activityOrderNo != null">
        #{activityOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null">
        #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="activityType != null">
        #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="wbsFullCode != null">
        #{wbsFullCode,jdbcType=VARCHAR},
      </if>
      <if test="wbsSummaryCode != null">
        #{wbsSummaryCode,jdbcType=VARCHAR},
      </if>
      <if test="wbsLastCode != null">
        #{wbsLastCode,jdbcType=VARCHAR},
      </if>
      <if test="dynamicWbsTemplateRuleIds != null">
        #{dynamicWbsTemplateRuleIds,jdbcType=VARCHAR},
      </if>
      <if test="dynamicFields != null">
        #{dynamicFields,jdbcType=VARCHAR},
      </if>
      <if test="dynamicValues != null">
        #{dynamicValues,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="parentWbsId != null">
        #{parentWbsId,jdbcType=BIGINT},
      </if>
      <if test="parentActivityId != null">
        #{parentActivityId,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="feeTypeId != null">
        #{feeTypeId,jdbcType=BIGINT},
      </if>
      <if test="feeTypeName != null">
        #{feeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="feeSyncEms != null">
        #{feeSyncEms,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersionDetailExample" resultType="java.lang.Long">
    select count(*) from project_wbs_budget_version_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersionDetail">
    update project_wbs_budget_version_detail
    <set>
      <if test="versionId != null">
        version_id = #{versionId,jdbcType=BIGINT},
      </if>
      <if test="projectWbsBudgetId != null">
        project_wbs_budget_id = #{projectWbsBudgetId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        project_code = #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="activityOrderNo != null">
        activity_order_no = #{activityOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null">
        activity_code = #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="activityName != null">
        activity_name = #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="activityType != null">
        activity_type = #{activityType,jdbcType=VARCHAR},
      </if>
      <if test="wbsFullCode != null">
        wbs_full_code = #{wbsFullCode,jdbcType=VARCHAR},
      </if>
      <if test="wbsSummaryCode != null">
        wbs_summary_code = #{wbsSummaryCode,jdbcType=VARCHAR},
      </if>
      <if test="wbsLastCode != null">
        wbs_last_code = #{wbsLastCode,jdbcType=VARCHAR},
      </if>
      <if test="dynamicWbsTemplateRuleIds != null">
        dynamic_wbs_template_rule_ids = #{dynamicWbsTemplateRuleIds,jdbcType=VARCHAR},
      </if>
      <if test="dynamicFields != null">
        dynamic_fields = #{dynamicFields,jdbcType=VARCHAR},
      </if>
      <if test="dynamicValues != null">
        dynamic_values = #{dynamicValues,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="parentWbsId != null">
        parent_wbs_id = #{parentWbsId,jdbcType=BIGINT},
      </if>
      <if test="parentActivityId != null">
        parent_activity_id = #{parentActivityId,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="feeTypeId != null">
        fee_type_id = #{feeTypeId,jdbcType=BIGINT},
      </if>
      <if test="feeTypeName != null">
        fee_type_name = #{feeTypeName,jdbcType=VARCHAR},
      </if>
      <if test="feeSyncEms != null">
        fee_sync_ems = #{feeSyncEms,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersionDetail">
    update project_wbs_budget_version_detail
    set version_id = #{versionId,jdbcType=BIGINT},
      project_wbs_budget_id = #{projectWbsBudgetId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      project_code = #{projectCode,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      activity_order_no = #{activityOrderNo,jdbcType=VARCHAR},
      activity_code = #{activityCode,jdbcType=VARCHAR},
      activity_name = #{activityName,jdbcType=VARCHAR},
      activity_type = #{activityType,jdbcType=VARCHAR},
      wbs_full_code = #{wbsFullCode,jdbcType=VARCHAR},
      wbs_summary_code = #{wbsSummaryCode,jdbcType=VARCHAR},
      wbs_last_code = #{wbsLastCode,jdbcType=VARCHAR},
      dynamic_wbs_template_rule_ids = #{dynamicWbsTemplateRuleIds,jdbcType=VARCHAR},
      dynamic_fields = #{dynamicFields,jdbcType=VARCHAR},
      dynamic_values = #{dynamicValues,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      parent_wbs_id = #{parentWbsId,jdbcType=BIGINT},
      parent_activity_id = #{parentActivityId,jdbcType=BIGINT},
      version = #{version,jdbcType=BIGINT},
      fee_type_id = #{feeTypeId,jdbcType=BIGINT},
      fee_type_name = #{feeTypeName,jdbcType=VARCHAR},
      fee_sync_ems = #{feeSyncEms,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>