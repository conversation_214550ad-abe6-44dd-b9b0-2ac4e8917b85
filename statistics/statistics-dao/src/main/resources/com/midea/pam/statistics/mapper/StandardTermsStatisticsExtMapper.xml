<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.StandardTermsStatisticsExtMapper">

    <select id="standardTermsDtoList" resultType="com.midea.pam.common.ctc.dto.StandardTermsDto">
        select
        t.id as id,
        t.terms_status as termsStatus,
        t.terms_code as termsCode,
        t.terms_name as termsName,
        t.terms_display_content as termsDisplayContent,
        t.attach_id_list_str as attachIdListStr,
        t.scope_application as scopeApplication,
        t.remark as remark,
        t.create_by as createBy,
        t.create_by_name as createByName,
        t.create_at as createAt,
        t.update_by as updateBy,
        t.update_by_name as updateByName,
        t.update_at as updateAt,
        t.version as version,
        t.deleted_flag as deletedFlag
        from pam_ctc.standard_terms t
        where t.deleted_flag = 0
        and unit_id = #{unitId}
        <if test="statusList != null and statusList.size() > 0">
            and t.terms_status in
            <foreach collection="statusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="scopeApplicationStrList != null and scopeApplicationStrList.size() > 0">
            and t.scope_application in
            <foreach collection="scopeApplicationStrList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="termsCode != null and termsCode != ''">
            and t.terms_code like concat('%', #{termsCode}, '%')
        </if>
        <if test="termsName != null and termsName != ''">
            and t.terms_name like concat('%', #{termsName}, '%')
        </if>
        <if test="remark != null and remark != ''">
            and t.remark like concat('%', #{remark}, '%')
        </if>
        <if test="createByName != null and createByName != ''">
            and t.create_by_name like concat('%', #{createByName}, '%')
        </if>
        <if test="updateByName != null and updateByName != ''">
            and t.update_by_name like concat('%', #{updateByName}, '%')
        </if>
        <if test="scopeApplication != null and scopeApplication != ''">
            and t.scope_application = #{scopeApplication}
        </if>
        <if test="updateAtStart != null and updateAtStart != ''">
            and t.update_at &gt;= #{updateAtStart}
        </if>
        <if test="updateAtEnd != null and updateAtEnd != ''">
            and t.update_at &lt;= #{updateAtEnd}
        </if>
        order by t.create_at desc
    </select>

    <select id="standardTermsContentList" resultType="com.midea.pam.common.ctc.dto.StandardTermsContentDto">
        select
        t.id as id,
        t.association_terms_id as associationTermsId,
        t.text_box_key as textBoxKey,
        t.text_box_title as textBoxTitle,
        t.default_value as defaultValue,
        t.remark as remark,
        t.sort as sort,
        t.create_by as createBy,
        t.create_at as createAt,
        t.update_by as updateBy,
        t.update_at as updateAt,
        t.version as version,
        t.deleted_flag as deletedFlag
        from pam_ctc.standard_terms_content t
        where t.deleted_flag = 0
        and t.association_terms_id = #{associationTermsId}
    </select>
</mapper>
