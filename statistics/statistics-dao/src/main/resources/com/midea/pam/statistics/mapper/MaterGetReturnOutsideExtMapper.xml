<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.MaterGetReturnOutsideExtMapper">

    <resultMap id="materialGetReturnOutsideResultMap" type="com.midea.pam.common.ctc.vo.MaterialGetReturnOutsideVO">
        <!-- 主对象的基本属性映射 -->
        <id column="form_id" property="formId"/>
        <result column="ou_id" property="ouId"/>
        <result column="form_code" property="formCode"/>
        <result column="form_type" property="formType"/>
        <result column="project_name" property="projectName"/>
        <result column="project_code" property="projectCode"/>
        <result column="legal_entity_name" property="legalEntityName"/>
        <result column="warehouse_number" property="warehouseNumber"/>
        <result column="warehouse_location_no" property="warehouseLocationNo"/>
        <result column="apply_date" property="applyDate"/>
        <result column="form_status" property="formStatus"/>

        <!-- 使用collection映射List集合属性 -->
        <collection property="detailList" ofType="com.midea.pam.common.ctc.vo.MaterialGetReturnOutsideVO$MaterialGetReturnOutsideDetailVO">
            <id column="detail_id" property="detailId"/>
            <result column="material_number" property="materialNumber"/>
            <result column="material_description" property="materialDescription"/>
            <result column="apply_quantity" property="applyQuantity"/>
            <result column="actual_quantity" property="actualQuantity"/>
            <result column="delivery_date" property="deliveryDate"/>
        </collection>
    </resultMap>


    <select id="materialGetOutsideList" resultMap="materialGetReturnOutsideResultMap">
          select
            mgh.id as form_id,
            mgh.organization_id as ou_id,
            mgh.get_code as form_code,
            '领料单' as form_type,
            p.name as project_name,
            p.code as project_code,
            o.legal_entity_name as legal_entity_name,
            mgd.inventory_code as warehouse_number,
            mgd.location_code as warehouse_location_no,
            mgh.apply_time as apply_date,
            mgd.id as detail_id,
            mgd.material_code as material_number,
            mgd.material_name as material_description,
            mgd.apply_amount as apply_quantity,
            case when mgh.status = 5 or mgh.status = 6 then mgd.actual_amount else null end as actual_quantity,
            case when mgh.status = 5 or mgh.status = 6 then mgd.trade_time else null end as delivery_date,
            mgh.status as form_status
          from
            pam_ctc.material_get_header mgh
            left join pam_ctc.material_get_detail mgd on mgd.header_id = mgh.id
            left join pam_ctc.project p on p.id = mgh.project_id
            left join pam_basedata.organization_rel o on o.organization_id = mgh.organization_id
            where
                mgh.deleted_flag = 0
                and mgd.deleted_flag = 0
                and mgh.get_code = #{formNumber}
                and mgh.status in (4,5,6)
    </select>

    <select id="materialReturnOutsideList" resultMap="materialGetReturnOutsideResultMap">
        select
            mrh.id as form_id,
            mrh.organization_id as ou_id,
            mrh.return_code as form_code,
            '退料单' as form_type,
            p.name as project_name,
            p.code as project_code,
            o.legal_entity_name as legal_entity_name,
            mrd.inventory_code as warehouse_number,
            mrd.location_code as warehouse_location_no,
            mrh.apply_time as apply_date,
            mrd.id as detail_id,
            mrd.material_code as material_number,
            mrd.material_name as material_description,
            mrd.apply_amount as apply_quantity,
            case when mrh.status = 5 or mrh.status = 6 then mrd.actual_amount else null end as actual_quantity,
            case when mrh.status = 5 or mrh.status = 6 then mrd.actual_return_at else null end as delivery_date,
            mrh.status as form_status
        from
            pam_ctc.material_return_header mrh
            left join pam_ctc.material_return_detail mrd on mrd.header_id = mrh.id
            left join pam_ctc.project p on p.id = mrh.project_id
            left join pam_basedata.organization_rel o on o.organization_id = mrh.organization_id
            where
                mrh.deleted_flag = 0
                and mrd.deleted_flag = 0
                and mrh.return_code = #{formNumber}
                and mrh.status in (4,5,6)
    </select>


</mapper>