<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ProjectWbsReceiptsStatisticsExtMapper">

    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ProjectWbsReceipts">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="requirement_code" jdbcType="VARCHAR" property="requirementCode"/>
        <result column="requirement_status" jdbcType="INTEGER" property="requirementStatus"/>
        <result column="requirement_type" jdbcType="INTEGER" property="requirementType"/>
        <result column="confirm_mode" jdbcType="INTEGER" property="confirmMode"/>
        <result column="handle_by" jdbcType="BIGINT" property="handleBy"/>
        <result column="handle_name" jdbcType="VARCHAR" property="handleName"/>
        <result column="handle_at" jdbcType="TIMESTAMP" property="handleAt"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="producer_id" jdbcType="BIGINT" property="producerId"/>
        <result column="producer_name" jdbcType="VARCHAR" property="producerName"/>
        <result column="process_name" jdbcType="VARCHAR" property="processName"/>
        <result column="project_submit" jdbcType="TINYINT" property="projectSubmit"/>
        <result column="unit_id" jdbcType="BIGINT" property="unitId"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag"/>
        <result column="version" jdbcType="BIGINT" property="version"/>
    </resultMap>

    <select id="list" parameterType="com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto" resultMap="BaseResultMap">
        select
        id,
        project_id,
        project_code,
        project_name,
        requirement_status,
        requirement_type,
        ifnull(confirm_mode,0) as confirm_mode,
        requirement_code,
        handle_name,
        producer_name,
        remark,
        create_at
        from
        pam_ctc.project_wbs_receipts
        where
        deleted_flag = 0

        <if test="projectWbsReceiptsId != null">
            and id = #{projectWbsReceiptsId}
        </if>

        <if test="projectId != null and projectId != ''">
            and project_id = #{projectId}
        </if>

        <if test="projectCode != null and projectCode != ''">
            and project_code like concat('%', #{projectCode}, '%')
        </if>

        <if test="projectName != null and projectName != ''">
            and project_name like concat('%', #{projectName}, '%')
        </if>

        <if test="requirementStatusList != null and requirementStatusList.size() > 0">
            and requirement_status in
            <foreach collection="requirementStatusList" item="requirementStatus" index="index" open="(" separator=","
                     close=")">
                #{requirementStatus}
            </foreach>
        </if>

        <if test="requirementTypeList != null and requirementTypeList.size() > 0">
            and requirement_type in
            <foreach collection="requirementTypeList" item="requirementType" index="index" open="(" separator=","
                     close=")">
                #{requirementType}
            </foreach>
        </if>

        <if test="confirmMode != null">
            and confirm_mode = #{confirmMode}
        </if>

        <if test="requirementCode != null and requirementCode != ''">
            and requirement_code like concat('%', #{requirementCode}, '%')
        </if>

        <if test="handleName != null and handleName != ''">
            and handle_name like concat('%', #{handleName}, '%')
        </if>

        <if test="handleBy != null and handleBy != ''">
            and handle_by = #{handleBy}
        </if>

        <if test="producerName != null and producerName != ''">
            and producer_name like concat('%', #{producerName}, '%')
        </if>

        <if test="remark != null and remark != ''">
            and remark like concat('%', #{remark}, '%')
        </if>

        <if test="startTimeStr != null and startTimeStr != ''">
            and create_at &gt;= #{startTimeStr}
        </if>

        <if test="endTimeStr != null and endTimeStr != ''">
            and create_at &lt;= #{endTimeStr}
        </if>

        <if test="unitId != null">
            and unit_id = #{unitId}
        </if>

        <if test="unitIds != null and unitIds.size() > 0">
            and unit_id in
            <foreach collection="unitIds" item="unitId" index="index" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>

        <!-- 我的需求发布单据 数据权限 -->
        <if test="me">
            and ( handle_by = #{userId} or producer_id = #{userId} )
        </if>
        ORDER BY create_at DESC
    </select>

    <select id="selectProcessReceiptsOnPendingProcessing" resultType="com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto">
        select
        pwr.id as id,
        pwr.requirement_code as requirementCode,
        pwr.handle_by as handleBy,
        pwr.update_at as updateAt,
        p.code as projectCode,
        p.name as projectName,
        p.unit_id as unitId ,
        u.unit_name as unitName,
        ocd.value as projectLeaderListStr
        from pam_ctc.project_wbs_receipts pwr
        left join pam_ctc.project p on pwr.project_id =p.id
        left join pam_basedata.unit u on p.unit_id = u.id
        left join pam_ctc.organization_custom_dict ocd on ocd.org_id = p.unit_id and ocd.name = #{name}
        where
        pwr.deleted_flag = 0
        and p.deleted_flag  = 0
        and pwr.requirement_status = 1
        and pwr.requirement_type = 2
    </select>

    <select id="selectProcessReceiptsOnApproval" resultType="com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto">
        select
        pwr.id as id,
        pwr.requirement_code as requirementCode,
        p.code as projectCode,
        p.name as projectName,
        p.unit_id as unitId ,
        u.unit_name as unitName,
        fi.fd_instance_id as fdInstanceId,
        fi.form_instance_id as formInstanceIdChange,
        ocd.value as projectLeaderListStr
        from pam_ctc.project_wbs_receipts pwr
        left join pam_ctc.project p on pwr.project_id =p.id
        left join pam_basedata.unit u on p.unit_id = u.id
        left join pam_ctc.milepost_design_plan_detail_change_record mdpdcr on pwr.id = mdpdcr.origin_form_instance_id
        left join pam_system.form_instance fi on mdpdcr.id = fi.form_instance_id
        left join pam_ctc.organization_custom_dict ocd on ocd.org_id = p.unit_id and ocd.name = #{name}
        where
        pwr.deleted_flag = 0
        and fi.deleted_flag = 0
        and p.deleted_flag  = 0
        and pwr.requirement_status = 2
        and pwr.requirement_type = 2
        and fi.form_url = 'mdpChangeTheChangeApp'
        and fi.wf_status='20'
    </select>


</mapper>