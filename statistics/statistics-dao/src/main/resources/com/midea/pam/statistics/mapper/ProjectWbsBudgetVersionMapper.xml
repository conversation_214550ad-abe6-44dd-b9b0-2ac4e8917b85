<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ProjectWbsBudgetVersionMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersion">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_history_header_id" jdbcType="BIGINT" property="projectHistoryHeaderId" />
    <result column="form_url" jdbcType="VARCHAR" property="formUrl" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="version_time" jdbcType="TIMESTAMP" property="versionTime" />
    <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
    <result column="create_by_mip" jdbcType="VARCHAR" property="createByMip" />
    <result column="create_by_name" jdbcType="VARCHAR" property="createByName" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_history_header_id, form_url, project_id, project_code, business_type, 
    version_time, version_code, create_by_mip, create_by_name, create_by, create_at, 
    update_by, update_at, deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_wbs_budget_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_wbs_budget_version
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_wbs_budget_version
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersion">
    insert into project_wbs_budget_version (id, project_history_header_id, form_url, 
      project_id, project_code, business_type, 
      version_time, version_code, create_by_mip, 
      create_by_name, create_by, create_at, 
      update_by, update_at, deleted_flag
      )
    values (#{id,jdbcType=BIGINT}, #{projectHistoryHeaderId,jdbcType=BIGINT}, #{formUrl,jdbcType=VARCHAR}, 
      #{projectId,jdbcType=BIGINT}, #{projectCode,jdbcType=VARCHAR}, #{businessType,jdbcType=TINYINT}, 
      #{versionTime,jdbcType=TIMESTAMP}, #{versionCode,jdbcType=VARCHAR}, #{createByMip,jdbcType=VARCHAR}, 
      #{createByName,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{deletedFlag,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersion">
    insert into project_wbs_budget_version
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectHistoryHeaderId != null">
        project_history_header_id,
      </if>
      <if test="formUrl != null">
        form_url,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="versionTime != null">
        version_time,
      </if>
      <if test="versionCode != null">
        version_code,
      </if>
      <if test="createByMip != null">
        create_by_mip,
      </if>
      <if test="createByName != null">
        create_by_name,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectHistoryHeaderId != null">
        #{projectHistoryHeaderId,jdbcType=BIGINT},
      </if>
      <if test="formUrl != null">
        #{formUrl,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="versionTime != null">
        #{versionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="versionCode != null">
        #{versionCode,jdbcType=VARCHAR},
      </if>
      <if test="createByMip != null">
        #{createByMip,jdbcType=VARCHAR},
      </if>
      <if test="createByName != null">
        #{createByName,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersionExample" resultType="java.lang.Long">
    select count(*) from project_wbs_budget_version
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersion">
    update project_wbs_budget_version
    <set>
      <if test="projectHistoryHeaderId != null">
        project_history_header_id = #{projectHistoryHeaderId,jdbcType=BIGINT},
      </if>
      <if test="formUrl != null">
        form_url = #{formUrl,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        project_code = #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="versionTime != null">
        version_time = #{versionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="versionCode != null">
        version_code = #{versionCode,jdbcType=VARCHAR},
      </if>
      <if test="createByMip != null">
        create_by_mip = #{createByMip,jdbcType=VARCHAR},
      </if>
      <if test="createByName != null">
        create_by_name = #{createByName,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersion">
    update project_wbs_budget_version
    set project_history_header_id = #{projectHistoryHeaderId,jdbcType=BIGINT},
      form_url = #{formUrl,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      project_code = #{projectCode,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=TINYINT},
      version_time = #{versionTime,jdbcType=TIMESTAMP},
      version_code = #{versionCode,jdbcType=VARCHAR},
      create_by_mip = #{createByMip,jdbcType=VARCHAR},
      create_by_name = #{createByName,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>