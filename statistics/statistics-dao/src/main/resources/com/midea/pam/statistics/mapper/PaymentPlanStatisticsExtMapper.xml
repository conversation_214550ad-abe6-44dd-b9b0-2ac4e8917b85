<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.PaymentPlanStatisticsExtMapper">

    <sql id="Base_Column_List">
        plan.id as id,
        plan.contract_id as contractId,
        pur.code as purchaseContractCode,
        pur.name as purchaseContractName,
        pur.legal_affairs_code as legalAffairsCode,
        pur.`status` as purchaseContractStatus,
        pur.ou_id as ouId,
        pur.currency as currency,
        pur.erp_vendor_site_id as erpVendorSiteId,
        pur.vendor_site_code as vendorSiteCode,
        p.id as projectId,
        p.code as projectCode,
        p.name as projectName,
        p.status as projectStatus,
        pur.vendor_id  as vendorId,
        pur.vendor_name as vendorName,
        pur.vendor_code as vendorCode,
        pur.purchasing_follower_name as purchasingFollowerName,
        pur.gle_sign_flag AS isGleSign,
        plan.proportion as proportion,
        plan.amount as amount,
        plan.date as date,
        plan.requirement as requirement,
        plan.payment_method_id as paymentMethodId ,
        plan.payment_method_name as paymentMethodName,
        plan.advance_payment_flag as advancePaymentFlag,
        plan.milestone_id as milestoneId,
        plan.code as code,
        plan.num as num,
        plan.status as status,
        plan.deleted_flag as deletedFlag,
        plan.create_by as createBy,
        plan.create_at as createAt,
        plan.update_by as updateBy,
        plan.update_at as updateAt,
	    pm.`status` as milestoneStatus,
	    pm.name as milestoneName,
        plan.allocation_punishment_amount_with_tax as allocationPunishmentAmountWithTax,
        plan.payment_apply_source as paymentApplySource
    </sql>

    <sql id="queryCondition">

        <!-- id-->
        <if test="id != null">
            AND plan.id = #{id}
        </if>

        <!-- 付款计划编号-->
        <if test="code != null and code != ''">
            AND plan.code like concat('%', #{code}, '%')
        </if>

        <!-- 采购跟进人-->
        <if test="purchasingFollowerName != null and purchasingFollowerName != ''">
            AND pur.purchasing_follower_name like concat('%', #{purchasingFollowerName}, '%')
        </if>

        <!-- 计划付款开始日期-->
        <if test="startDate != null">
            AND plan.date <![CDATA[ >= ]]> #{startDate}
        </if>

        <!-- endDate-->
        <if test="endDate != null">
            AND plan.date <![CDATA[ <= ]]> #{endDate}
        </if>

        <!-- purchaseContractName-->
        <if test="purchaseContractName != null and purchaseContractName != ''">
            AND pur.name like concat('%', #{purchaseContractName}, '%')
        </if>

        <!-- purchaseContractCode-->
        <if test="purchaseContractCode != null and purchaseContractCode != ''">
            AND pur.code like concat('%', #{purchaseContractCode}, '%')
        </if>

        <!-- currency-->
        <if test="currency != null and currency != ''">
            AND pur.currency like concat('%', #{currency}, '%')
        </if>

        <!-- purchaseContractStatus-->
        <if test="purchaseContractStatus != null and purchaseContractStatus != ''">
            AND pur.status = #{purchaseContractStatus}
        </if>

        <!-- vendorName-->
        <if test="vendorName != null and vendorName != ''">
            AND pur.vendor_name like concat('%', #{vendorName}, '%')
        </if>

        <!-- vendorName-->
        <if test="vendorCode != null and vendorCode != ''">
            AND pur.vendor_code like concat('%', #{vendorCode}, '%')
        </if>

        <!-- vendorName-->
        <if test="paymentMethodName != null and paymentMethodName != ''">
            AND plan.payment_method_name like concat('%', #{paymentMethodName}, '%')
        </if>

        <!-- projectName-->
        <if test="projectName != null and projectName != ''">
            AND p.name like concat('%', #{projectName}, '%')
        </if>

        <!-- projectCode-->
        <if test="projectCode != null and projectCode != ''">
            AND p.code like concat('%', #{projectCode}, '%')
        </if>

        <if test="milestoneStatus != null">
            AND pm.`status` = #{milestoneStatus}
        </if>

        <if test="milestoneName != null">
            AND pm.`name` like concat('%', #{milestoneName}, '%')
        </if>

        <if test="milestoneStatuses != null">
            and pm.`status` in
            <foreach collection="milestoneStatuses" item="milestoneStatus" index="index" open="(" separator="," close=")">
                #{milestoneStatus}
            </foreach>
        </if>

        <if test="contractStatusList != null">
            and pur.status in
            <foreach collection="contractStatusList" item="contractStatus" index="index" open="(" separator="," close=")">
                #{contractStatus}
            </foreach>
        </if>

        <if test="statusList != null">
            and plan.status in
            <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        AND pur.ou_id in
        <foreach collection="ouIds" item="ouId" index="index" open="(" separator="," close=")">
            #{ouId}
        </foreach>
    </sql>

    <select id="paymentPlanList" resultType="com.midea.pam.common.ctc.dto.PaymentPlanDTO">
        SELECT <include refid="Base_Column_List"/>
             , if((
                      SELECT COUNT(0)
                      FROM pam_ctc.payment_apply pa
                      WHERE (pa.deleted_flag = 0
                          AND pa.payment_plan_id = plan.id
                          AND pa.audit_status = 2)
                  ) > 0, true, false) AS alread
             , (
            SELECT IFNULL(SUM(tax_pay_included_price - ifnull(total_cancel_amount, 0)), 0)
            FROM pam_ctc.payment_apply
            WHERE (audit_status != 5
                AND (deleted_flag = 0
                    OR deleted_flag IS NULL)
                AND payment_plan_id = plan.id)
        ) AS totalApplyAmount
             , (
            SELECT IFNULL(SUM(tax_pay_included_price - ifnull(total_cancel_amount, 0) - ifnull(really_pay_included_price, 0)), 0)
            FROM pam_ctc.payment_apply
            WHERE (audit_status != 5
                AND (deleted_flag = 0
                    OR deleted_flag IS NULL)
                AND payment_plan_id = plan.id)
        ) AS totalOnTheWayAmount
             , IFNULL(allocation_punishment_amount_with_tax,0) + (
            SELECT IFNULL(SUM(penalty_amount), 0)
            FROM pam_ctc.payment_apply
            WHERE (audit_status IN (1,2,3,4)  AND (erp_status!='2' and gceb_status!='2')
                AND (deleted_flag = 0
                    OR deleted_flag IS NULL)
                AND payment_plan_id = plan.id)
        ) AS totalpenaltyAmount,
        IFNULL((SELECT
        SUM(
        IFNULL(really_pay_included_price, 0)
        )
        FROM
        pam_ctc.payment_apply
        WHERE (erp_status = 1
        OR gceb_status = 1)
        AND (
        deleted_flag = 0
        OR deleted_flag IS NULL
        ) and audit_status in (1,2,3,4)
        AND payment_plan_id = plan.id), 0) as actualAmount
        FROM pam_ctc.payment_plan plan
                 INNER JOIN pam_ctc.purchase_contract pur ON pur.id = plan.contract_id AND pur.status in (4,5,10)
                 LEFT JOIN pam_ctc.project p ON pur.project_id = p.id
                 LEFT JOIN pam_ctc.project_milepost pm ON pm.id = plan.milestone_id
                 LEFT JOIN pam_basedata.vendor_site_bank ven ON ven.id = pur.vendor_id
        WHERE 1 = 1
          AND (plan.deleted_flag IS NULL
            OR plan.deleted_flag = 0)
        <include refid="queryCondition"/>
        order by plan.`date` desc
    </select>

    <select id="paymentPlanListByOuIdsAndContractCode" resultType="com.midea.pam.common.ctc.dto.PaymentPlanDTO">
        SELECT <include refid="Base_Column_List"/>
             , if((
                      SELECT COUNT(0)
                      FROM pam_ctc.payment_apply pa
                      WHERE (pa.deleted_flag = 0
                          AND pa.payment_plan_id = plan.id
                          AND pa.audit_status = 2)
                  ) > 0, true, false) AS alread
             , (
            SELECT IFNULL(SUM(tax_pay_included_price - ifnull(total_cancel_amount, 0)), 0)
            FROM pam_ctc.payment_apply
            WHERE (audit_status != 5
                AND (deleted_flag = 0
                    OR deleted_flag IS NULL)
                AND payment_plan_id = plan.id)
        ) AS totalApplyAmount
             , (
            SELECT IFNULL(SUM(tax_pay_included_price - ifnull(total_cancel_amount, 0) - ifnull(really_pay_included_price, 0)), 0)
            FROM pam_ctc.payment_apply
            WHERE (audit_status != 5
                AND (deleted_flag = 0
                    OR deleted_flag IS NULL)
                AND payment_plan_id = plan.id)
        ) AS totalOnTheWayAmount
             , IFNULL(allocation_punishment_amount_with_tax,0) + (
            SELECT IFNULL(SUM(penalty_amount), 0)
            FROM pam_ctc.payment_apply
            WHERE (audit_status IN (1,2,3,4) AND (erp_status!='2' and gceb_status!='2')
                AND (deleted_flag = 0
                    OR deleted_flag IS NULL)
                AND payment_plan_id = plan.id)
        ) AS totalpenaltyAmount,
        IFNULL((SELECT
        SUM(
        IFNULL(really_pay_included_price, 0)
        )
        FROM
        pam_ctc.payment_apply
        WHERE (erp_status = 1
        OR gceb_status = 1)
        AND (
        deleted_flag = 0
        OR deleted_flag IS NULL
        ) and audit_status in (1,2,3,4)
        AND payment_plan_id = plan.id), 0) as actualAmount
        FROM pam_ctc.payment_plan plan
                 INNER JOIN pam_ctc.purchase_contract pur ON pur.id = plan.contract_id AND pur.status in (4,5,10)
                 LEFT JOIN pam_ctc.project p ON pur.project_id = p.id
                 LEFT JOIN pam_ctc.project_milepost pm ON pm.id = plan.milestone_id
                 LEFT JOIN pam_basedata.vendor_site_bank ven ON ven.id = pur.vendor_id
        WHERE 1 = 1
          AND (plan.deleted_flag IS NULL
            OR plan.deleted_flag = 0)
          AND pur.ou_id in
          <foreach collection="ouIdList" item="ouId" index="index" open="(" separator="," close=")">
              #{ouId}
          </foreach>
          <if test= "purchaseContractCode != null and purchaseContractCode != ''">
              AND pur.code = #{purchaseContractCode}
          </if>
        order by plan.`date` desc
    </select>

</mapper>