<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ProjectCurrentIncomeSummaryExtMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.statistics.entity.ProjectCurrentIncomeSummary">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="execute_id" jdbcType="BIGINT" property="executeId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="confirmed_income_total_amount" jdbcType="DECIMAL" property="confirmedIncomeTotalAmount" />
    <result column="standard_confirmed_income_total_amount" jdbcType="DECIMAL" property="standardConfirmedIncomeTotalAmount" />
    <result column="confirmed_cost_total_amount" jdbcType="DECIMAL" property="confirmedCostTotalAmount" />
    <result column="confirmed_gross_profit_ratio" jdbcType="DECIMAL" property="confirmedGrossProfitRatio" />
    <result column="confirmed_exchange_amount" jdbcType="DECIMAL" property="confirmedExchangeAmount" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>

  <select id="getAllByProjectIDs" resultMap="BaseResultMap" parameterType="list">
    select
        p.id as project_id,
        p.code as project_code,
        SUM(IFNULL(cb.current_income_amount, 0)) as confirmed_income_total_amount,
        SUM(IFNULL(cb.current_income_amount, 0) * IFNULL(cb.conversion_rate, 1)) as standard_confirmed_income_total_amount ,
        SUM(IFNULL(cb.current_cost_actual, 0)) as confirmed_cost_total_amount,
        round((SUM(IFNULL(cb.current_income_amount, 0) * IFNULL(cb.conversion_rate, 1)) -SUM(IFNULL(cb.current_cost_actual, 0)))/ SUM(IFNULL(cb.current_income_amount, 0) * IFNULL(cb.conversion_rate, 1)) * 100, 2) as confirmed_gross_profit_ratio,
        (
        select
            coalesce(SUM(d.exchange_amount), 0)
        from
            pam_ctc.exchange_account_detail d
        left join pam_ctc.exchange_account_head h on
            d.exchange_account_head_id = h.id
        where
            d.deleted_flag = 0
            and d.project_id = p.id
            and h.deleted_flag = 0
            and h.reverse_status = 1
            and h.exchange_account_deal_record_id = (
            select
                r.id
            from
                pam_ctc.exchange_account_deal_record r
            where
                r.deleted_flag = 0
                and r.record_type = 1
                and r.status = 2
                and exists (
                select
                    1
                from
                    pam_ctc.exchange_account_detail dd
                left join pam_ctc.exchange_account_head hh on
                    dd.exchange_account_head_id = hh.id
                where
                    hh.deleted_flag = 0
                    and hh.reverse_status = 1
                    and hh.exchange_account_deal_record_id = r.id
                    and dd.project_id = p.id
                )
            order by
                r.create_at desc
            limit 1
            )
        ) as confirmed_exchange_amount
    from
        pam_ctc.project p
    left join
        pam_ctc.carryover_bill cb on
        p.id = cb.project_id
        and cb.status = 1
        and cb.deleted_flag = 0
    where
        p.id in
      <foreach collection="list" index="index" item="ides1" open="(" separator="," close=")">
          #{ides1}
      </foreach>
    group by
        p.id
  </select>

  <insert id="insertBatch" parameterType="com.midea.pam.common.statistics.entity.ProjectCurrentIncomeSummary">
  insert into project_current_income_summary (id, execute_id, project_id,
      project_code, confirmed_income_total_amount,
      standard_confirmed_income_total_amount, confirmed_cost_total_amount,
      confirmed_gross_profit_ratio, confirmed_exchange_amount,
      create_by, create_at)
    values
    <foreach collection="list" item="obj" separator=",">
       (#{obj.id,jdbcType=BIGINT}, #{obj.executeId,jdbcType=BIGINT}, #{obj.projectId,jdbcType=BIGINT},
        #{obj.projectCode,jdbcType=VARCHAR}, #{obj.confirmedIncomeTotalAmount,jdbcType=DECIMAL},
        #{obj.standardConfirmedIncomeTotalAmount,jdbcType=DECIMAL}, #{obj.confirmedCostTotalAmount,jdbcType=DECIMAL},
        #{obj.confirmedGrossProfitRatio,jdbcType=DECIMAL}, #{obj.confirmedExchangeAmount,jdbcType=DECIMAL},
        #{obj.createBy,jdbcType=BIGINT}, #{obj.createAt,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

</mapper>