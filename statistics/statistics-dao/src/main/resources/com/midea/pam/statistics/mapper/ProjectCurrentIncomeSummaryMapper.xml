<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.statistics.mapper.ProjectCurrentIncomeSummaryMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.statistics.entity.ProjectCurrentIncomeSummary">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="execute_id" jdbcType="BIGINT" property="executeId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="confirmed_income_total_amount" jdbcType="DECIMAL" property="confirmedIncomeTotalAmount" />
    <result column="standard_confirmed_income_total_amount" jdbcType="DECIMAL" property="standardConfirmedIncomeTotalAmount" />
    <result column="confirmed_cost_total_amount" jdbcType="DECIMAL" property="confirmedCostTotalAmount" />
    <result column="confirmed_gross_profit_ratio" jdbcType="DECIMAL" property="confirmedGrossProfitRatio" />
    <result column="confirmed_exchange_amount" jdbcType="DECIMAL" property="confirmedExchangeAmount" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, execute_id, project_id, project_code, confirmed_income_total_amount, standard_confirmed_income_total_amount, 
    confirmed_cost_total_amount, confirmed_gross_profit_ratio, confirmed_exchange_amount, 
    create_by, create_at, update_by, update_at, deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.statistics.entity.ProjectCurrentIncomeSummaryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_current_income_summary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_current_income_summary
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_current_income_summary
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.statistics.entity.ProjectCurrentIncomeSummary">
    insert into project_current_income_summary (id, execute_id, project_id, 
      project_code, confirmed_income_total_amount, 
      standard_confirmed_income_total_amount, confirmed_cost_total_amount, 
      confirmed_gross_profit_ratio, confirmed_exchange_amount, 
      create_by, create_at, update_by, 
      update_at, deleted_flag)
    values (#{id,jdbcType=BIGINT}, #{executeId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, 
      #{projectCode,jdbcType=VARCHAR}, #{confirmedIncomeTotalAmount,jdbcType=DECIMAL}, 
      #{standardConfirmedIncomeTotalAmount,jdbcType=DECIMAL}, #{confirmedCostTotalAmount,jdbcType=DECIMAL}, 
      #{confirmedGrossProfitRatio,jdbcType=DECIMAL}, #{confirmedExchangeAmount,jdbcType=DECIMAL}, 
      #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP}, #{deletedFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.statistics.entity.ProjectCurrentIncomeSummary">
    insert into project_current_income_summary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="executeId != null">
        execute_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="confirmedIncomeTotalAmount != null">
        confirmed_income_total_amount,
      </if>
      <if test="standardConfirmedIncomeTotalAmount != null">
        standard_confirmed_income_total_amount,
      </if>
      <if test="confirmedCostTotalAmount != null">
        confirmed_cost_total_amount,
      </if>
      <if test="confirmedGrossProfitRatio != null">
        confirmed_gross_profit_ratio,
      </if>
      <if test="confirmedExchangeAmount != null">
        confirmed_exchange_amount,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="executeId != null">
        #{executeId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="confirmedIncomeTotalAmount != null">
        #{confirmedIncomeTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="standardConfirmedIncomeTotalAmount != null">
        #{standardConfirmedIncomeTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmedCostTotalAmount != null">
        #{confirmedCostTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmedGrossProfitRatio != null">
        #{confirmedGrossProfitRatio,jdbcType=DECIMAL},
      </if>
      <if test="confirmedExchangeAmount != null">
        #{confirmedExchangeAmount,jdbcType=DECIMAL},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.statistics.entity.ProjectCurrentIncomeSummaryExample" resultType="java.lang.Long">
    select count(*) from project_current_income_summary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.statistics.entity.ProjectCurrentIncomeSummary">
    update project_current_income_summary
    <set>
      <if test="executeId != null">
        execute_id = #{executeId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        project_code = #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="confirmedIncomeTotalAmount != null">
        confirmed_income_total_amount = #{confirmedIncomeTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="standardConfirmedIncomeTotalAmount != null">
        standard_confirmed_income_total_amount = #{standardConfirmedIncomeTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmedCostTotalAmount != null">
        confirmed_cost_total_amount = #{confirmedCostTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmedGrossProfitRatio != null">
        confirmed_gross_profit_ratio = #{confirmedGrossProfitRatio,jdbcType=DECIMAL},
      </if>
      <if test="confirmedExchangeAmount != null">
        confirmed_exchange_amount = #{confirmedExchangeAmount,jdbcType=DECIMAL},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.statistics.entity.ProjectCurrentIncomeSummary">
    update project_current_income_summary
    set execute_id = #{executeId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      project_code = #{projectCode,jdbcType=VARCHAR},
      confirmed_income_total_amount = #{confirmedIncomeTotalAmount,jdbcType=DECIMAL},
      standard_confirmed_income_total_amount = #{standardConfirmedIncomeTotalAmount,jdbcType=DECIMAL},
      confirmed_cost_total_amount = #{confirmedCostTotalAmount,jdbcType=DECIMAL},
      confirmed_gross_profit_ratio = #{confirmedGrossProfitRatio,jdbcType=DECIMAL},
      confirmed_exchange_amount = #{confirmedExchangeAmount,jdbcType=DECIMAL},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>