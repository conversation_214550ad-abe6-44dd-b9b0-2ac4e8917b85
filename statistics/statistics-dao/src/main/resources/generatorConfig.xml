<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>

    <context id="Mysql" targetRuntime="MyBatis3" defaultModelType="flat">

        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="com.midea.pam.common.util.OverWriteXmlPlugin" />
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>

        <commentGenerator type="com.midea.pam.statistics.config.ApiCommentGenerator">
            <property name="suppressDate" value="false"/>
            <property name="suppressAllComments" value="false"/>
        </commentGenerator>
        <!--数据库链接地址账号密码-->

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="**************************************************************"
                        userId="pam_sit">
            <property name="useInformationSchema" value="true" />
        </jdbcConnection>

<!--		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="**************************************************************"
                        userId="pam_sit">
        </jdbcConnection>-->


        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!--生成Model类存放位置-->
        <javaModelGenerator targetPackage="com.midea.pam.common.statistics.entity"
                            targetProject="common-module/src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
            <!--设置所有实体类的基类。如果设置，需要使用类的全限定名称。并且如果MBG能够加载rootClass，那么MBG不会覆盖和父类中完全匹配的属性。-->
            <property name="rootClass" value="com.midea.pam.common.base.LongIdEntity"/>
        </javaModelGenerator>
        <!--生成映射文件存放位置-->
        <sqlMapGenerator targetPackage="com.midea.pam.statistics.mapper"
                         targetProject="statistics/statistics-dao/src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!--生成Mapper.xml存放位置-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.midea.pam.statistics.mapper"
                             targetProject="statistics/statistics-dao/src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="rootInterface" value="com.meicloud.light.mapper.Mapper"/>
        </javaClientGenerator>

<!--        <table tableName="hro_requirement_report" domainObjectName="HroRequirementReport">-->
<!--            <columnOverride column="total_num" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="purchased_num" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="fill_num" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="approve_num" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="bill_num" javaType="java.math.BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="report_budget_change" domainObjectName="ReportBudgetChange"-->
<!--            enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--             <columnOverride column="original_price" javaType="java.math.BigDecimal" />-->
<!--             <columnOverride column="current_price" javaType="java.math.BigDecimal" />-->
<!--             <columnOverride column="offset_price" javaType="java.math.BigDecimal" />-->
<!--             <columnOverride column="preview_flag" javaType="Boolean" />-->
<!--             <columnOverride column="project_status" javaType="Integer" />-->
<!--             <columnOverride column="month" javaType="Integer" />-->
<!--         </table>-->

<!--        <table tableName="vendor_penalty_project_cost_summary" domainObjectName="VendorPenaltyProjectCostSummary"-->
<!--            enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--             <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--             <columnOverride column="conversion_rate" javaType="java.math.BigDecimal" />-->
<!--             <columnOverride column="amount" javaType="java.math.BigDecimal" />-->
<!--             <columnOverride column="project_cost" javaType="java.math.BigDecimal" />-->
<!--             <columnOverride column="occur_cost" javaType="java.math.BigDecimal" />-->
<!--         </table>-->

<!--        <table tableName="report_project_monthly" domainObjectName="ReportProjectMonthly" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="project_status" javaType="Integer" />-->
<!--        </table>-->

      <!--  <table tableName="working_hour_submit_error_temporary" domainObjectName="WorkingHourSubmitErrorTemporary"
              enableUpdateByExample="false" enableDeleteByExample="false">
           <columnOverride column="deleted_flag" javaType="Boolean" />
           <columnOverride column="status" javaType="Integer" />
           <columnOverride column="invoice_apply_flag" javaType="Integer" />
           <columnOverride column="cost_collection_flag" javaType="Integer" />
       </table>-->

<!--        <table tableName="working_hour_collection_error_temporary" domainObjectName="WorkingHourCollectionErrorTemporary"-->
<!--              enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--           <columnOverride column="deleted_flag" javaType="Integer" />-->
<!--           <columnOverride column="carry_status" javaType="Integer" />-->
<!--       </table>-->

        <!--<table tableName="count_swap_data_detail" domainObjectName="CountSwapDataDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_cost_execute_record" domainObjectName="ProjectCostExecuteRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="report_interface_param" domainObjectName="ReportInterfaceParam"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="sync" javaType="Integer"/>-->
<!--            <columnOverride column="max_runner" javaType="Integer"/>-->
<!--        </table>-->

        <!--<table tableName="project_cost_summary_record" domainObjectName="ProjectCostSummaryRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_cost_fee_summary_record" domainObjectName="ProjectCostFeeSummaryRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->


        <!--<table tableName="portal_target" domainObjectName="PortalTarget"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="enable_flag" javaType="Boolean" />
            <columnOverride column="display_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="report_contract_process" domainObjectName="ReportContractProcess"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="process_type" javaType="Integer" />
            <columnOverride column="frame_flag" javaType="Integer" />
            <columnOverride column="customer_type" javaType="Integer" />
        </table>-->

<!--        <table tableName="report_invoice_apply_receivable_ages" domainObjectName="ReportInvoiceApplyReceivableAges"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="customer_type" javaType="Integer" />
        </table>-->

<!--        <table tableName="report_receivable_receipt_claim" domainObjectName="ReportReceivableReceiptClaim"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="customer_type" javaType="Integer" />
        </table>-->

<!--        <table tableName="report_project_no_carryover_cost" domainObjectName="ReportProjectNoCarryoverCost"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="project_status" javaType="Integer" />
        </table>-->

<!--        <table tableName="report_project_no_carryover_cost_detail" domainObjectName="ReportProjectNoCarryoverCostDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="project_status" javaType="Integer" />
        </table>-->

        <!--<table tableName="role_portal_target_rel" domainObjectName="RolePortalTargetRel"
                enableUpdateByExample="false" enableDeleteByExample="false">
             <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="enable_flag" javaType="Boolean" />
            <columnOverride column="display_flag" javaType="Boolean" />
         </table>-->

<!--        <table tableName="project_cost_fee_detail_record" domainObjectName="ProjectCostFeeDetailRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--        <table tableName="project_cost_revenue_order_record" domainObjectName="ProjectCostRevenueOrderRecord"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--        </table>-->

        <!--        <table tableName="project_cost_purchase_order_record" domainObjectName="ProjectCostPurchaseOrderRecord"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                </table>

                <table tableName="project_cost_storage_record" domainObjectName="ProjectCostStorageRecord"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                </table>

                <table tableName="project_cost_getreturn_material_record" domainObjectName="ProjectCostGetreturnMaterialRecord"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                </table>

                <table tableName="project_cost_outsource_purchase_record" domainObjectName="ProjectCostOutsourcePurchaseRecord"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                </table>

                <table tableName="project_cost_difference_record" domainObjectName="ProjectCostDifferenceRecord"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                </table>

                <table tableName="project_cost_materialcost_summary_record" domainObjectName="ProjectCostMaterialcostSummaryRecord"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                </table>

                <table tableName="project_cost_materialcost_summary_detail_record" domainObjectName="ProjectCostMaterialcostSummaryDetailRecord"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                </table>-->

<!--        <table tableName="project_cost_fee_item_record" domainObjectName="ProjectCostFeeItemRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--        <table tableName="project_cost_human_summary_record" domainObjectName="ProjectCostHumanSummaryRecord"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
        <!--        </table>-->
        <!--<table tableName="working_hour_detail" domainObjectName="WorkingHourDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
            <columnOverride column="project_status" javaType="Integer"/>
            <columnOverride column="cost_collection_flag" javaType="Boolean"/>
            <columnOverride column="invoice_apply_flag" javaType="Boolean"/>
            <columnOverride column="cross_unit_labor_cost" javaType="Boolean"/>
        </table>-->

<!--                <table tableName="project_cost_human_detail_record" domainObjectName="ProjectCostHumanDetailRecord"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                    <columnOverride column="status" javaType="Integer" />
                </table>-->

        <!--        <table tableName="project_cost_human_item_record" domainObjectName="ProjectCostHumanItemRecord"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--        </table>-->

        <!--<table tableName="project_cost_summary_item_record" domainObjectName="ProjectCostSummaryItemRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

        <!--<table tableName="report_department_info" domainObjectName="ReportDepartmentInfo"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

        <!--<table tableName="report_department_unit_rel" domainObjectName="ReportDepartmentUnitRel"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

        <!--<table tableName="report_info" domainObjectName="ReportInfo"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

        <!--<table tableName="report_group_info" domainObjectName="ReportGroupInfo"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

        <!--<table tableName="report_group_formation" domainObjectName="ReportGroupFormation"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

        <!--<table tableName="report_group_grant_user" domainObjectName="ReportGroupGrantUser"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

<!--        <table tableName="report_execute_record" domainObjectName="ReportExecuteRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="report_execute_parameter" domainObjectName="ReportExecuteParameter"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

       <!-- <table tableName="project_cost_detail" domainObjectName="ProjectCostDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->
        <!--<table tableName="report_project_dimension" domainObjectName="ReportProjectDimension"
               enableUpdateByExample="false" enableDeleteByExample="false">
               <columnOverride column="status" javaType="Integer"/>
        </table>-->
        <!--<table tableName="report_income_dimension" domainObjectName="ReportIncomeDimension"
               enableUpdateByExample="false" enableDeleteByExample="false">
               <columnOverride column="status" javaType="Integer"/>
               <columnOverride column="help_flag" javaType="Integer"/>
               <columnOverride column="order_num" javaType="Integer"/>
               <columnOverride column="milepost_status" javaType="Integer"/>
        </table>-->
		<!--<table tableName="report_business_win" domainObjectName="ReportBusinessWin"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->
        <!--
		<table tableName="report_project_profit" domainObjectName="ReportProjectProfit"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->
        <!--<table tableName="report_project_budget_manager" domainObjectName="ReportProjectBudgetManager"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="project_status" javaType="Integer"/>
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
        <!--<table tableName="report_project_budget_manager_detail" domainObjectName="ReportProjectBudgetManagerDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->

        <!--<table tableName="report_working_hour_accounting" domainObjectName="ReportWorkingHourAccounting"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
		
		<!--<table tableName="project_cost_ea_detail_record" domainObjectName="ProjectCostEaDetailRecord"
              enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
       <!-- <table tableName="not_remind_record" domainObjectName="NotRemindRecord"
              enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
		<!-- 
		<table tableName="department_unit_incoming_target" domainObjectName="DepartmentUnitIncomingTarget"
              enableUpdateByExample="false" enableDeleteByExample="false">
			<columnOverride column="unit_type" javaType="Integer" />
			<columnOverride column="inner_flag" javaType="Integer" />
			<columnOverride column="target_type" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>
		-->

       <!-- <table tableName="project_cost_clear_record" domainObjectName="ProjectCostClearRecord"
              enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->

        <!--<table tableName="project_cost_materialcost_summary_detail_record" domainObjectName="ProjectCostMaterialcostSummaryDetailRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="income_calculate" domainObjectName="IncomeCalculate"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="income_calculate_project" domainObjectName="IncomeCalculateProject"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="extra_flag" javaType="Boolean" />
        </table>
-->
        <!--

        <table tableName="income_calculate_product" domainObjectName="IncomeCalculateProduct"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>

        <table tableName="income_calculate_project_task" domainObjectName="IncomeCalculateProjectTask"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="help_flag" javaType="Boolean" />
            <columnOverride column="carry_status" javaType="Boolean" />
        </table>

        <table tableName="income_calculate_product_task" domainObjectName="IncomeCalculateProductTask"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="income_calculate_project_task" domainObjectName="IncomeCalculateProjectTask"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="help_flag" javaType="Boolean" />
            <columnOverride column="carry_status" javaType="Boolean" />
        </table>-->


        <!--        <table tableName="report_income_product_task" domainObjectName="ReportIncomeProductTask"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        </table>-->

<!--        <table tableName="report_income_project_task" domainObjectName="ReportIncomeProjectTask"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        </table>-->

<!--        <table tableName="report_income_summary" domainObjectName="ReportIncomeSummary"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        </table>-->

<!--        <table tableName="report_income_calculate" domainObjectName="ReportIncomeCalculate"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        </table>-->

<!--        <table tableName="report_milepost_design_plan_change" domainObjectName="ReportMilepostDesignPlanChange"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->
<!--        <table tableName="report_milepost_design_plan_change_kunshan" domainObjectName="ReportMilepostDesignPlanChangeKunShan"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
<!--            <table tableName="working_hour_exception_rdm" domainObjectName="WorkingHourExceptionRdm"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                <columnOverride column="resource_flag" javaType="Integer" />-->
<!--            </table>-->
<!--            <table tableName="working_hour_exception_accounting" domainObjectName="WorkingHourExceptionAccounting"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                <columnOverride column="accounting_status" javaType="Integer" />-->
<!--                <columnOverride column="erp_status" javaType="Integer" />-->
<!--            </table>-->

<!--            <table tableName="report_project_purchase_order" domainObjectName="ReportProjectPurchaseOrder"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                <columnOverride column="contract_flag" javaType="Integer" />-->
<!--            </table>-->

        <!--<table tableName="report_material_requriement" domainObjectName="ReportMaterialRequriement"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="task_status" javaType="Integer" />
        </table>-->

        <!--<table tableName="report_unconfirmed_income" domainObjectName="ReportUnconfirmedIncome"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->

        <!--<table tableName="report_project_milepost" domainObjectName="ReportProjectMilepost"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="report_due_date_receivable_detail" domainObjectName="ReportDueDateReceivableDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="customer_type" javaType="Integer" />
        </table>-->

<!--        <table tableName="report_project_process_profit" domainObjectName="ReportProjectProcessProfit"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="project_level" javaType="Integer" />-->
<!--            <columnOverride column="project_status" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="report_project_change_trace_summary" domainObjectName="ReportProjectChangeTraceSummary"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="report_project_change_base_info" domainObjectName="ReportProjectChangeBaseInfo"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="report_project_change_milepost" domainObjectName="ReportProjectChangeMilepost"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->


<!--        <table tableName="report_project_change_budget" domainObjectName="ReportProjectChangeBudget"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="requirement_po_detail" domainObjectName="RequirementPoDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_purchase_contract_detail" domainObjectName="ProjectPurchaseContractDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="hro_purchase_contract_total" domainObjectName="HroPurchaseContractTotal"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_material_detail" domainObjectName="ProjectMaterialDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_material_get" domainObjectName="ProjectMaterialGet"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_cost_saving" domainObjectName="ProjectCostSaving"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="material_history_price" domainObjectName="MaterialHistoryPrice"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="material_purchase_frequency_count" domainObjectName="MaterialPurchaseFrequencyCount"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="report_milepost_design_plan_detail_follow" domainObjectName="ReportMilepostDesignPlanDetailFollow"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
<!--        <table tableName="working_hour_ihr_attend" domainObjectName="WorkingHourIhrAttend"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--                <table tableName="project_wbs_execute_detail" domainObjectName="ProjectWbsExecuteDetail"-->
<!--                       enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                    <columnOverride column="execute_result" javaType="Integer" />-->
<!--                    <columnOverride column="next_time_execute_flag" javaType="Integer" />-->
<!--                    <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                </table>-->

<!--        <table tableName="report_purchase_contract_debt_ages" domainObjectName="ReportPurchaseContractDebtAges"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--                <table tableName="report_project_contract_receivable" domainObjectName="ReportProjectContractReceivable"-->
<!--                       enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                    <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                </table>-->

<!--                <table tableName="temp_project_monthly" domainObjectName="TempProjectMonthly"-->
<!--                       enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                    <columnOverride column="project_status" javaType="Integer" />-->
<!--                    <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                </table>-->

<!--        <table tableName="report_project_margin" domainObjectName="ReportProjectMargin"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="report_sample" domainObjectName="ReportSample"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_cost_asset_detail_record" domainObjectName="ProjectCostAssetDetailRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_cost_asset_item_record" domainObjectName="ProjectCostAssetItemRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_cost_asset_summary_record" domainObjectName="ProjectCostAssetSummaryRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_current_income_summary" domainObjectName="ProjectCurrentIncomeSummary"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="material_budget_detail" domainObjectName="MaterialBudgetDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

    </context>
</generatorConfiguration>