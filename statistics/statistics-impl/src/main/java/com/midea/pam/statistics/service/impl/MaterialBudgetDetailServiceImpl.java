package com.midea.pam.statistics.service.impl;


import com.google.common.collect.Lists;
import com.midea.pam.common.basedata.dto.UnitDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.statistics.dto.ExportResponse;
import com.midea.pam.common.statistics.dto.ReportExecuteRecordDTO;
import com.midea.pam.common.statistics.entity.MaterialBudgetDetail;
import com.midea.pam.common.statistics.entity.MaterialBudgetDetailExample;
import com.midea.pam.common.statistics.entity.ProjectCostExecuteRecord;
import com.midea.pam.common.statistics.entity.ReportExecuteParameter;
import com.midea.pam.common.statistics.entity.ReportExecuteParameterExample;
import com.midea.pam.common.statistics.entity.ReportExecuteRecord;
import com.midea.pam.common.statistics.entity.ReportGroupInfo;
import com.midea.pam.common.statistics.excelVo.MaterialBudgetDetailExcelVO;
import com.midea.pam.common.statistics.query.MaterialBudgetDetailQuery;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.statistics.mapper.MaterialBudgetDetailExtMapper;
import com.midea.pam.statistics.mapper.MaterialBudgetDetailMapper;
import com.midea.pam.statistics.mapper.ReportExecuteParameterMapper;
import com.midea.pam.statistics.mapper.ReportGroupInfoMapper;
import com.midea.pam.statistics.mapper.UnitExtMapper;
import com.midea.pam.statistics.project.service.ProjectCostExecuteRecordService;
import com.midea.pam.statistics.service.MaterialBudgetDetailService;
import com.midea.pam.statistics.service.ReportExecuteRecordService;
import com.midea.pam.support.utils.BeanConverter;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:物料预算明细报表
 * @author:ygx
 * @create:2025-07-09 13:48
 **/
@Service
public class MaterialBudgetDetailServiceImpl extends AbstractCommonReportService implements MaterialBudgetDetailService {

    private static final Logger logger = LoggerFactory.getLogger(MaterialBudgetDetailServiceImpl.class);

    @Resource
    private ReportExecuteParameterMapper reportExecuteParameterMapper;
    @Resource
    private ReportExecuteRecordService reportExecuteRecordService;
    @Resource
    private ReportGroupInfoMapper reportGroupInfoMapper;
    @Resource
    private MaterialBudgetDetailMapper materialBudgetDetailMapper;
    @Resource
    private MaterialBudgetDetailExtMapper materialBudgetDetailExtMapper;
    @Resource
    private UnitExtMapper unitExtMapper;
    @Resource
    private ProjectCostExecuteRecordService projectCostExecuteRecordService;


    /**
     * 分配配置类，封装不同分配类型的配置参数
     */
    private static class AllocationConfig {
        private final String dataFieldName;           // 数据字段名
        private final String description;             // 分配描述
        private final Function<MilepostDesignPlanDetailDto, BigDecimal> requirementExtractor; // 需求量提取器

        public AllocationConfig(String dataFieldName, String description,
                                Function<MilepostDesignPlanDetailDto, BigDecimal> requirementExtractor) {
            this.dataFieldName = dataFieldName;
            this.description = description;
            this.requirementExtractor = requirementExtractor;
        }

        public String getDataFieldName() {
            return dataFieldName;
        }

        public String getDescription() {
            return description;
        }

        public Function<MilepostDesignPlanDetailDto, BigDecimal> getRequirementExtractor() {
            return requirementExtractor;
        }
    }

    /**
     * 详设和实际需求量的包装类（移到类级别以便复用）
     */
    private static class DetailWithNeed {
        final MilepostDesignPlanDetailDto detail;
        final BigDecimal actualNeed;

        DetailWithNeed(MilepostDesignPlanDetailDto detail, BigDecimal actualNeed) {
            this.detail = detail;
            this.actualNeed = actualNeed;
        }
    }

    /**
     * 分配结果类，封装所有分配类型的结果
     */
    private static class AllocationResults {
        final Map<String, BigDecimal> allocatedMaterialReceivedMap;
        final Map<String, BigDecimal> allocatedInventoryQuantityMap;
        final Map<String, BigDecimal> allocatedPurchaseOrderPlacedMap;

        AllocationResults(Map<String, BigDecimal> allocatedMaterialReceivedMap,
                          Map<String, BigDecimal> allocatedInventoryQuantityMap,
                          Map<String, BigDecimal> allocatedPurchaseOrderPlacedMap) {
            this.allocatedMaterialReceivedMap = allocatedMaterialReceivedMap;
            this.allocatedInventoryQuantityMap = allocatedInventoryQuantityMap;
            this.allocatedPurchaseOrderPlacedMap = allocatedPurchaseOrderPlacedMap;
        }
    }

    @Override
    public void parameterCheck(ReportExecuteRecordDTO reportExecuteRecordDTO) {
        List<ReportExecuteParameter> executeParameterList = reportExecuteRecordDTO.getExecuteParameters();
        if (CollectionUtils.isNotEmpty(executeParameterList)) {
            executeParameterList.forEach(reportExecuteParameter -> reportExecuteParameter.setReportId(reportExecuteRecordDTO.getReportId()));
        }
        buildParam(executeParameterList);
    }

    public void dataAuthor(MaterialBudgetDetailQuery query, Long companyId) {
        if (companyId != null) {
            Map<String, Object> unitMap = new HashMap<>();
            unitMap.put("parentId", companyId);
            List<UnitDto> unitDtos = unitExtMapper.queryUnitDtoByParams(unitMap);
            if (ListUtils.isNotEmpty(unitDtos)) {
                List<Long> unitIds = unitDtos.stream().map(UnitDto::getId).collect(Collectors.toList());
                query.setUnitIds(unitIds);
            }
        } else {
            query.setUnitIds(SystemContext.getSecondUnits());
        }

        // 避免空值，增加不存在的值
        if (CollectionUtils.isEmpty(query.getUnitIds())) {
            query.setUnitIds(Lists.newArrayList(-1L));
        }
    }

    @Override
    public Boolean execute(ReportExecuteRecord executeRecord) {
        ReportExecuteParameterExample example = new ReportExecuteParameterExample();
        example.createCriteria().andExecuteIdEqualTo(executeRecord.getId());
        List<ReportExecuteParameter> list = reportExecuteParameterMapper.selectByExample(example);

        if (CollectionUtils.isNotEmpty(list)) {
            ReportExecuteRecord reportExecuteRecord = reportExecuteRecordService.selectByPrimaryKey(executeRecord.getId());
            Long companyId = reportExecuteRecord.getCompanyId();
            Long createBy = reportExecuteRecord.getCreateBy();
            MaterialBudgetDetailQuery query = buildParam(list);
            query.setExecuteId(executeRecord.getId());
            query.setReportId(executeRecord.getReportId());
            //数据权限
            ReportGroupInfo reportGroupInfo = reportGroupInfoMapper.selectByPrimaryKey(executeRecord.getReportGroupId());
            if (reportGroupInfo != null) {
                // 如果报表组的权限控制“数据权限”，报表能导出的项目范围受用户的数据权限控制，参照“项目列表”的数据权限控制逻辑
                dataAuthor(query, companyId);

                // 如果报表组的权限控制“我的权限”，报表能导出的项目范围受用户的数据权限控制，参照“我的项目”的数据权限控制逻辑
                if ("personal".equals(reportGroupInfo.getPermission())) {
                    query.setPersonal(createBy);
                }
            }

            logger.info("物料预算明细报表-start-execute_id:{},param:{}", executeRecord.getId(), query.toString());

            // 根据参数查询唯一项目
            Project project = getUniqueProject(query);
            if (project == null) {
                logger.info("物料预算明细报表-end-execute_id:{},未找到匹配的项目", executeRecord.getId());
                return true;
            }

            // 查询详设设计方案数据
            List<MilepostDesignPlanDetailDto> designPlanDetails = queryDesignPlanDetails(project.getId());
            if (CollectionUtils.isEmpty(designPlanDetails)) {
                logger.info("物料预算明细报表-end-execute_id:{},项目{}无详设数据", executeRecord.getId(), project.getCode());
                return true;
            }

            // 构建树形结构并平铺排序
            List<MilepostDesignPlanDetailDto> flattenedDetails = buildTreeAndFlatten(designPlanDetails);

            // 构建报表数据
            List<MaterialBudgetDetail> reportDataList = buildReportData(flattenedDetails, project, query);

            // 批量插入数据
            if (CollectionUtils.isNotEmpty(reportDataList)) {
                List<List<MaterialBudgetDetail>> insertLists = ListUtils.splistList(reportDataList, 500);
                insertLists.forEach(materialBudgetDetailExtMapper::batchInsert);
            }

            logger.info("物料预算明细报表-end-execute_id:{},数据条数:{}", executeRecord.getId(), reportDataList.size());
        }
        return true;
    }

    @Override
    public ExportResponse export(ReportExecuteRecord executeRecord) {
        ExportResponse exportResponse = new ExportResponse();

        MaterialBudgetDetailExample example = new MaterialBudgetDetailExample();
        example.createCriteria().andExecuteIdEqualTo(executeRecord.getId());
        List<MaterialBudgetDetail> list = materialBudgetDetailMapper.selectByExample(example);
        logger.info("物料预算明细报表-export:{},count:{}", executeRecord.getId(), list.size());

        List<ExportResponse.Sheet> sheetList = new ArrayList<>(1);
        ExportResponse.Sheet sheet = new ExportResponse.Sheet<MaterialBudgetDetailExcelVO>();
        sheet.setEntityClass(MaterialBudgetDetailExcelVO.class);
        sheet.setDataList(BeanConverter.copy(list, MaterialBudgetDetailExcelVO.class));
        sheet.setCreateHeader(true);
        sheet.setSheetName("物料预算明细报表");
        sheetList.add(sheet);
        exportResponse.setSheetList(sheetList);
        return exportResponse;
    }

    private MaterialBudgetDetailQuery buildParam(List<ReportExecuteParameter> list) {
        MaterialBudgetDetailQuery query = new MaterialBudgetDetailQuery();
        if (CollectionUtils.isNotEmpty(list)) {
            for (ReportExecuteParameter reportExecuteParameter : list) {
                if (!StringUtils.isNotEmpty(reportExecuteParameter.getValue())) {
                    continue;
                }

                // 项目状态
                if (reportExecuteParameter.getName().equals("statusStr") && StringUtils.isNotEmpty(reportExecuteParameter.getValue())) {
                    List<Integer> projectStatusList = new ArrayList<>();
                    String[] arrStr = reportExecuteParameter.getValue().split(",");
                    for (String s : arrStr) {
                        if (StringUtils.isNotEmpty(s)) {
                            projectStatusList.add(Integer.valueOf(s));
                        }
                    }
                    query.setProjectStatusList(projectStatusList);
                }

                // 项目编号/名称
                if (reportExecuteParameter.getName().equals("codeOrName") && StringUtils.isNotEmpty(reportExecuteParameter.getValue())) {
                    String[] arr = reportExecuteParameter.getValue().split("_");
                    query.setProjectCode(arr[0]);
                }

                // 业务实体
                if (reportExecuteParameter.getName().equals("ouIdStr") && StringUtils.isNotEmpty(reportExecuteParameter.getValue())) {
                    List<Long> ouIdList = new ArrayList<>();
                    String[] arrStr = reportExecuteParameter.getValue().split(",");
                    for (String s : arrStr) {
                        if (StringUtils.isNotEmpty(s)) {
                            ouIdList.add(Long.valueOf(s));
                        }
                    }
                    query.setOuIdList(ouIdList);
                }
            }
        }

        // 参数校验
        if (CollectionUtils.isEmpty(query.getProjectStatusList())) {
            throw new BizException(Code.ERROR, "参数【项目状态】为必填项");
        }
        if (StringUtils.isEmpty(query.getProjectCode())) {
            throw new BizException(Code.ERROR, "参数【项目编号/名称】为必填项");
        }

        return query;
    }

    private Project getUniqueProject(MaterialBudgetDetailQuery query) {
        // 根据项目状态、项目编号/名称、业务实体查询项目
        List<Project> projects = materialBudgetDetailExtMapper.queryProjectsByParams(query);

        if (CollectionUtils.isEmpty(projects)) {
            return null;
        }

        if (projects.size() > 1) {
            throw new BizException(Code.ERROR, "查询到多个项目，请精确项目条件");
        }

        return projects.get(0);
    }

    private List<MilepostDesignPlanDetailDto> queryDesignPlanDetails(Long projectId) {
        return materialBudgetDetailExtMapper.queryDesignPlanDetailsByProjectId(projectId);
    }

    private List<MilepostDesignPlanDetailDto> buildTreeAndFlatten(List<MilepostDesignPlanDetailDto> designPlanDetails) {
        // 构建正确的层级编号以及BOOM层级
        setNumFieldManually(designPlanDetails);

        // 构建树形结构并平铺排序
        return buildTreeAndFlattenWithNum(designPlanDetails);
    }

    /**
     * 手工设置num字段，构建层级编号
     * parent_id = -1 是第一层，设值1，2，3
     * 假设A为parent_id = -1，parent_id为A的id的详设行就是第二层，设值为1.1，1.2，如此类推
     */
    private void setNumFieldManually(List<MilepostDesignPlanDetailDto> allDetails) {
        // 构建父子关系映射
        Map<Long, List<MilepostDesignPlanDetailDto>> parentChildMap = new HashMap<>();
        Map<Long, MilepostDesignPlanDetailDto> idDetailMap = new HashMap<>();

        for (MilepostDesignPlanDetailDto detail : allDetails) {
            idDetailMap.put(detail.getId(), detail);
            Long parentId = detail.getParentId() != null ? detail.getParentId() : -1L;
            parentChildMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(detail);
        }

        // 从根节点开始递归设置编号
        List<MilepostDesignPlanDetailDto> rootNodes = parentChildMap.getOrDefault(-1L, new ArrayList<>());

        // 对根节点按ID排序，确保编号的一致性
        rootNodes.sort(Comparator.comparing(MilepostDesignPlanDetailDto::getId));

        for (int i = 0; i < rootNodes.size(); i++) {
            String rootNum = String.valueOf(i + 1);
            setNumRecursively(rootNodes.get(i), rootNum, parentChildMap);
        }
    }

    /**
     * 递归设置编号
     */
    private void setNumRecursively(MilepostDesignPlanDetailDto detail, String num,
                                   Map<Long, List<MilepostDesignPlanDetailDto>> parentChildMap) {
        // 设置当前节点的编号
        detail.setNum(num);
        // 设置BOOM层级
        detail.setBoomLevel(calculateBoomLevel(detail.getNum()));

        // 获取子节点并排序
        List<MilepostDesignPlanDetailDto> children = parentChildMap.getOrDefault(detail.getId(), new ArrayList<>());
        children.sort(Comparator.comparing(MilepostDesignPlanDetailDto::getId));

        // 递归设置子节点编号
        for (int i = 0; i < children.size(); i++) {
            String childNum = num + "." + (i + 1);
            setNumRecursively(children.get(i), childNum, parentChildMap);
        }
    }

    /**
     * 根据序号计算BOOM层级
     */
    private Integer calculateBoomLevel(String serialNumber) {
        if (StringUtils.isEmpty(serialNumber)) {
            return 0;
        }

        // 根据序号格式计算层级：X=0级，X.X=1级，X.X.X=2级，以此类推
        String[] parts = serialNumber.split("\\.");
        return parts.length - 1;
    }

    /**
     * 构建树形结构并按编号平铺排序
     * 确保按数字逻辑排序，支持1.1.1, 1.1.2, ..., 1.1.10, 1.1.11的正确排序
     */
    private List<MilepostDesignPlanDetailDto> buildTreeAndFlattenWithNum(List<MilepostDesignPlanDetailDto> allDetails) {
        // 按照设置好的num字段排序，使用改进的数字比较逻辑
        allDetails.sort((a, b) -> {
            String numA = a.getNum() != null ? a.getNum().trim() : "";
            String numB = b.getNum() != null ? b.getNum().trim() : "";
            return compareNumString(numA, numB);
        });

        return allDetails;
    }

    /**
     * 比较编号字符串，支持多层级比较
     * 例如：1 < 1.1 < 1.2 < 1.10 < 2 < 2.1
     * 修复：确保按数字逻辑排序，而不是字符串排序
     */
    private int compareNumString(String num1, String num2) {
        if (StringUtils.isEmpty(num1) && StringUtils.isEmpty(num2)) {
            return 0;
        }
        if (StringUtils.isEmpty(num1)) {
            return -1;
        }
        if (StringUtils.isEmpty(num2)) {
            return 1;
        }

        String[] parts1 = num1.split("\\.");
        String[] parts2 = num2.split("\\.");

        int maxLength = Math.max(parts1.length, parts2.length);

        for (int i = 0; i < maxLength; i++) {
            // 安全解析数字，处理异常情况
            int val1 = 0;
            int val2 = 0;

            if (i < parts1.length) {
                try {
                    val1 = Integer.parseInt(parts1[i].trim());
                } catch (NumberFormatException e) {
                    // 如果解析失败，记录警告并使用0作为默认值
                    logger.warn("无法解析编号部分为数字: {}, 将使用0作为默认值", parts1[i]);
                    // val1已经初始化为0，无需重新赋值
                }
            }

            if (i < parts2.length) {
                try {
                    val2 = Integer.parseInt(parts2[i].trim());
                } catch (NumberFormatException e) {
                    // 如果解析失败，记录警告并使用0作为默认值
                    logger.warn("无法解析编号部分为数字: {}, 将使用0作为默认值", parts2[i]);
                    // val2已经初始化为0，无需重新赋值
                }
            }

            if (val1 != val2) {
                return Integer.compare(val1, val2);
            }
        }

        return 0;
    }

    private List<MaterialBudgetDetail> buildReportData(List<MilepostDesignPlanDetailDto> designDetails,
                                                       Project project, MaterialBudgetDetailQuery query) {
        List<MaterialBudgetDetail> reportDataList = new ArrayList<>();
        Long projectId = project.getId();

        // 收集所有ERP编码，然后分别查询各个表
        List<String> erpCodeList = designDetails.stream()
                .map(MilepostDesignPlanDetailDto::getErpCode)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(erpCodeList)) {
            // 如果没有ERP编码，直接返回空数据
            return new ArrayList<>();
        }

        // 查询库存组织Id
        Long orgId = materialBudgetDetailExtMapper.queryOrgIdByProjectId(projectId);

        // 查询成本最新执行记录Id
        ProjectCostExecuteRecord latestRecord = projectCostExecuteRecordService.getLatestRecord(projectId);
        Long executeId = latestRecord != null ? latestRecord.getId() : -1L;

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("projectId", projectId);
        queryParams.put("ouId", project.getOuId());
        queryParams.put("orgId", orgId);
        queryParams.put("executeId", executeId);
        queryParams.put("erpCodeList", erpCodeList);

        // 批量查询采购订单数据
        long startTime1 = System.currentTimeMillis();
        Map<String, Map<String, Object>> purchaseOrderDataMap = batchQueryToMap(
                materialBudgetDetailExtMapper.batchQueryPurchaseOrderData(queryParams), "erp_code");
        long endTime1 = System.currentTimeMillis();
        logger.info("批量查询采购订单数据耗时: {}ms, ERP编码数量: {}", (endTime1 - startTime1), erpCodeList.size());

        // 批量查询库存数据
        long startTime2 = System.currentTimeMillis();
        Map<String, Map<String, Object>> storageDataMap = batchQueryToMap(
                materialBudgetDetailExtMapper.batchQueryStorageData(queryParams), "erp_code");
        long endTime2 = System.currentTimeMillis();
        logger.info("批量查询库存数据耗时: {}ms, ERP编码数量: {}", (endTime2 - startTime2), erpCodeList.size());

        // 批量查询领料数据
        long startTime3 = System.currentTimeMillis();
        Map<String, Map<String, Object>> materialDataMap = batchQueryToMap(
                materialBudgetDetailExtMapper.batchQueryMaterialData(queryParams), "erp_code");
        long endTime3 = System.currentTimeMillis();
        logger.info("批量查询领料数据耗时: {}ms, ERP编码数量: {}", (endTime3 - startTime3), erpCodeList.size());

        // 批量查询一揽子价格数据
        long startTime4 = System.currentTimeMillis();
        Map<String, Map<String, Object>> bpaPriceDataMap = batchQueryToMap(
                materialBudgetDetailExtMapper.batchQueryBpaPriceData(queryParams), "erp_code");
        long endTime4 = System.currentTimeMillis();
        logger.info("批量查询一揽子价格数据耗时: {}ms, ERP编码数量: {}", (endTime4 - startTime4), erpCodeList.size());

        // 批量查询物料估价数据
        long startTime5 = System.currentTimeMillis();
        Map<String, Map<String, Object>> materialCostDataMap = batchQueryToMap(
                materialBudgetDetailExtMapper.batchQueryMaterialCostData(queryParams), "erp_code");
        long endTime5 = System.currentTimeMillis();
        logger.info("批量查询物料估价数据耗时: {}ms, ERP编码数量: {}", (endTime5 - startTime5), erpCodeList.size());

        // 统一进行所有类型的分配，减少重复的ERP编码分组和循环
        AllocationResults allocationResults = allocateAllQuantitiesOptimized(designDetails,
                materialDataMap, storageDataMap, purchaseOrderDataMap, projectId, designDetails);

        for (MilepostDesignPlanDetailDto detail : designDetails) {
            MaterialBudgetDetail reportData = new MaterialBudgetDetail();

            // 基础信息
            reportData.setReportId(query.getReportId());
            reportData.setExecuteId(query.getExecuteId());
            reportData.setProjectId(projectId);
            reportData.setProjectCode(project.getCode());
            reportData.setProjectName(project.getName());
            reportData.setOuId(project.getOuId());

            // 详设数据映射
            reportData.setNum(detail.getNum());
            reportData.setBoomLevel(detail.getBoomLevel());
            if (detail.getExt() != null) {
                reportData.setExt(detail.getExt() ? "是" : "否");
            }
            reportData.setMaterielType(detail.getMaterielType());
            reportData.setMaterielDescr(detail.getMaterielDescr());
            reportData.setPamCode(detail.getPamCode());
            reportData.setErpCode(detail.getErpCode());
            reportData.setName(detail.getName());
            reportData.setUnit(detail.getUnit());
            reportData.setNumber(detail.getNumber());
            // 计算totalNum：采购需求总数 = 当前节点数量 × 所有父级数量的乘积
            reportData.setTotalNum(calculateTotalNum(detail, designDetails));
            // requirementNum：已发布需求数量，来自查询结果的计算逻辑
            reportData.setRequirementNum(detail.getRequirementNum());
            reportData.setRemark(detail.getRemark());
            reportData.setDeliveryTime(detail.getDeliveryTime());
            reportData.setModel(detail.getModel());
            reportData.setBrand(detail.getBrand());

            // 从批量查询结果中获取成本和价格数据，使用分配后的已领料数据、库存数据和已下采购订单数据
            calculateAdditionalFieldsOptimized(reportData, purchaseOrderDataMap, materialDataMap, bpaPriceDataMap, materialCostDataMap,
                    allocationResults.allocatedMaterialReceivedMap,
                    allocationResults.allocatedInventoryQuantityMap,
                    allocationResults.allocatedPurchaseOrderPlacedMap, detail.getId());

            // 设置审计字段
            reportData.setCreateAt(new Date());
            reportData.setDeletedFlag(Boolean.FALSE);

            reportDataList.add(reportData);
        }

        return reportDataList;
    }

    /**
     * 统一分配方法，一次性处理所有分配类型，减少重复的ERP编码分组和循环
     */
    private AllocationResults allocateAllQuantitiesOptimized(List<MilepostDesignPlanDetailDto> designDetails,
                                                             Map<String, Map<String, Object>> materialDataMap,
                                                             Map<String, Map<String, Object>> storageDataMap,
                                                             Map<String, Map<String, Object>> purchaseOrderDataMap,
                                                             Long projectId,
                                                             List<MilepostDesignPlanDetailDto> allProjectDetails) {
        // 初始化结果Map
        Map<String, BigDecimal> allocatedMaterialReceivedMap = new HashMap<>();
        Map<String, BigDecimal> allocatedInventoryQuantityMap = new HashMap<>();
        Map<String, BigDecimal> allocatedPurchaseOrderPlacedMap = new HashMap<>();

        // 一次性按ERP编码分组详设数据，避免重复分组
        Map<String, List<MilepostDesignPlanDetailDto>> erpCodeGroupMap = designDetails.stream()
                .filter(detail -> StringUtils.isNotEmpty(detail.getErpCode()))
                .collect(Collectors.groupingBy(MilepostDesignPlanDetailDto::getErpCode));

        // 创建分配配置
        AllocationConfig materialReceivedConfig = new AllocationConfig(
                "material_received", "已领料", detail -> detail.getTotalNum());
        AllocationConfig purchaseOrderConfig = new AllocationConfig(
                "purchase_order_placed", "已下采购订单", detail -> detail.getRequirementNum());
        AllocationConfig inventoryConfig = new AllocationConfig(
                "inventory_quantity", "库存", detail -> detail.getTotalNum());

        // 遍历每个ERP编码组，一次性处理所有分配类型
        for (Map.Entry<String, List<MilepostDesignPlanDetailDto>> entry : erpCodeGroupMap.entrySet()) {
            String erpCode = entry.getKey();
            List<MilepostDesignPlanDetailDto> sameErpCodeDetails = entry.getValue();

            // 如果该ERP编码只有一个详设行，直接分配，不需要复杂的分配逻辑
            if (sameErpCodeDetails.size() == 1) {
                MilepostDesignPlanDetailDto detail = sameErpCodeDetails.get(0);
                String detailId = detail.getId().toString();

                // 直接分配已领料
                BigDecimal materialReceived = getQuantityFromDataMap(materialDataMap, erpCode, "material_received");
                allocatedMaterialReceivedMap.put(detailId, materialReceived);

                // 直接分配已下采购订单
                BigDecimal purchaseOrderPlaced = getQuantityFromDataMap(purchaseOrderDataMap, erpCode, "purchase_order_placed");
                allocatedPurchaseOrderPlacedMap.put(detailId, purchaseOrderPlaced);

                // 直接分配库存
                BigDecimal inventoryQuantity = getQuantityFromDataMap(storageDataMap, erpCode, "inventory_quantity");
                allocatedInventoryQuantityMap.put(detailId, inventoryQuantity);

                continue;
            }

            // 对于多个详设行的情况，需要执行分配算法
            // 1. 先进行已领料分配
            BigDecimal totalMaterialReceived = getQuantityFromDataMap(materialDataMap, erpCode, "material_received");
            if (totalMaterialReceived.compareTo(BigDecimal.ZERO) > 0) {
                allocateForSameErpCodeGeneric(sameErpCodeDetails, totalMaterialReceived,
                        allocatedMaterialReceivedMap, projectId, allProjectDetails, materialReceivedConfig);
            } else {
                // 没有已领料，所有详设行都分配0
                for (MilepostDesignPlanDetailDto detail : sameErpCodeDetails) {
                    allocatedMaterialReceivedMap.put(detail.getId().toString(), BigDecimal.ZERO);
                }
            }

            // 2. 再进行已下采购订单分配
            BigDecimal totalPurchaseOrderPlaced = getQuantityFromDataMap(purchaseOrderDataMap, erpCode, "purchase_order_placed");
            if (totalPurchaseOrderPlaced.compareTo(BigDecimal.ZERO) > 0) {
                allocateForSameErpCodeGeneric(sameErpCodeDetails, totalPurchaseOrderPlaced,
                        allocatedPurchaseOrderPlacedMap, projectId, allProjectDetails, purchaseOrderConfig);
            } else {
                // 没有已下采购订单，所有详设行都分配0
                for (MilepostDesignPlanDetailDto detail : sameErpCodeDetails) {
                    allocatedPurchaseOrderPlacedMap.put(detail.getId().toString(), BigDecimal.ZERO);
                }
            }

            // 3. 最后进行库存分配（依赖已领料分配结果）
            BigDecimal totalInventoryQuantity = getQuantityFromDataMap(storageDataMap, erpCode, "inventory_quantity");
            if (totalInventoryQuantity.compareTo(BigDecimal.ZERO) > 0) {
                allocateInventoryQuantityForSameErpCodeGeneric(sameErpCodeDetails, totalInventoryQuantity,
                        allocatedInventoryQuantityMap, projectId, allProjectDetails, allocatedMaterialReceivedMap);
            } else {
                // 没有库存，所有详设行都分配0
                for (MilepostDesignPlanDetailDto detail : sameErpCodeDetails) {
                    allocatedInventoryQuantityMap.put(detail.getId().toString(), BigDecimal.ZERO);
                }
            }
        }

        return new AllocationResults(allocatedMaterialReceivedMap, allocatedInventoryQuantityMap, allocatedPurchaseOrderPlacedMap);
    }

    /**
     * 从分别查询的结果中获取成本和价格数据
     */
    private void calculateAdditionalFieldsOptimized(MaterialBudgetDetail reportData,
                                                    Map<String, Map<String, Object>> purchaseOrderDataMap,
                                                    Map<String, Map<String, Object>> materialDataMap,
                                                    Map<String, Map<String, Object>> bpaPriceDataMap,
                                                    Map<String, Map<String, Object>> materialCostDataMap,
                                                    Map<String, BigDecimal> allocatedMaterialReceivedMap,
                                                    Map<String, BigDecimal> allocatedInventoryQuantityMap,
                                                    Map<String, BigDecimal> allocatedPurchaseOrderPlacedMap,
                                                    Long detailId) {
        String erpCode = reportData.getErpCode();
        if (StringUtils.isEmpty(erpCode)) {
            return;
        }

        // 从采购订单数据中获取信息，使用分配后的已下采购订单数量
        BigDecimal purchaseOrderPlaced = allocatedPurchaseOrderPlacedMap.get(detailId.toString());
        if (purchaseOrderPlaced == null) {
            purchaseOrderPlaced = BigDecimal.ZERO;
        }
        reportData.setPurchaseOrderPlaced(purchaseOrderPlaced);

        // 计算总采购成本和采购单价
        Map<String, Object> purchaseOrderData = purchaseOrderDataMap.get(erpCode);
        if (purchaseOrderData != null && purchaseOrderPlaced.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal originalPurchaseOrderPlaced = getBigDecimalFromMap(purchaseOrderData, "purchase_order_placed");
            BigDecimal originalTotalPurchaseCost = getBigDecimalFromMap(purchaseOrderData, "total_purchase_cost");

            // 按比例计算分配后的总采购成本
            BigDecimal totalPurchaseCost = BigDecimal.ZERO;
            if (originalPurchaseOrderPlaced.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = purchaseOrderPlaced.divide(originalPurchaseOrderPlaced, 6, RoundingMode.HALF_UP);
                totalPurchaseCost = originalTotalPurchaseCost.multiply(ratio);
            }
            reportData.setTotalPurchaseCost(totalPurchaseCost);

            // 计算采购单价
            if (totalPurchaseCost.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal purchaseUnitPrice = totalPurchaseCost.divide(purchaseOrderPlaced, 6, RoundingMode.HALF_UP);
                reportData.setPurchaseUnitPrice(purchaseUnitPrice);
            }
        }

        // 从库存数据中获取信息，使用分配后的库存数量
        BigDecimal inventoryQuantity = allocatedInventoryQuantityMap.get(detailId.toString());
        if (inventoryQuantity == null) {
            inventoryQuantity = BigDecimal.ZERO;
        }
        reportData.setInventoryQuantity(inventoryQuantity);

        // 从领料数据中获取信息，使用分配后的已领料数量
        BigDecimal materialReceived = allocatedMaterialReceivedMap.get(detailId.toString());
        if (materialReceived == null) {
            materialReceived = BigDecimal.ZERO;
        }

        Map<String, Object> materialData = materialDataMap.get(erpCode);
        BigDecimal totalMaterialCost = BigDecimal.ZERO;
        if (materialData != null) {
            BigDecimal originalMaterialReceived = getBigDecimalFromMap(materialData, "material_received");
            BigDecimal originalTotalMaterialCost = getBigDecimalFromMap(materialData, "total_material_cost");

            // 按比例计算分配后的总成本
            if (originalMaterialReceived.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal ratio = materialReceived.divide(originalMaterialReceived, 6, RoundingMode.HALF_UP);
                totalMaterialCost = originalTotalMaterialCost.multiply(ratio);
            }
        }

        reportData.setMaterialReceived(materialReceived);
        reportData.setTotalMaterialCost(totalMaterialCost);

        // 计算领料平均单位成本
        if (materialReceived != null && materialReceived.compareTo(BigDecimal.ZERO) > 0 && totalMaterialCost != null) {
            BigDecimal averageUnitCost = totalMaterialCost.divide(materialReceived, 6, RoundingMode.HALF_UP);
            reportData.setAverageUnitCostMaterial(averageUnitCost);
        }

        // 从价格数据中获取信息（优先一揽子价格，备选物料成本）
        BigDecimal packagePrice = null;
        Map<String, Object> bpaPriceData = bpaPriceDataMap.get(erpCode);
        if (bpaPriceData != null) {
            packagePrice = getBigDecimalFromMap(bpaPriceData, "bpa_price");
        }

        if (packagePrice == null || packagePrice.compareTo(BigDecimal.ZERO) == 0) {
            Map<String, Object> materialCostData = materialCostDataMap.get(erpCode);
            if (materialCostData != null) {
                packagePrice = getBigDecimalFromMap(materialCostData, "material_cost");
            }
        }

        reportData.setPackagePriceEstimateUnitPrice(packagePrice);

        // 计算未下单
        BigDecimal notOrdered = (reportData.getTotalNum() != null ? reportData.getTotalNum() : BigDecimal.ZERO)
                .subtract(reportData.getRequirementNum() != null ? reportData.getRequirementNum() : BigDecimal.ZERO);
        reportData.setNotOrdered(notOrdered);

        // 计算预计待发生成本
        if (packagePrice != null && notOrdered != null) {
            BigDecimal expectedPendingCost = packagePrice.multiply(notOrdered);
            reportData.setExpectedPendingCost(expectedPendingCost);
        }
    }

    /**
     * 计算totalNum：需求数量 = 当前节点数量 × 所有父级数量的乘积
     * 参考MilepostDesignPlanDetailServiceImpl#countRequirementNum的逻辑
     */
    private BigDecimal calculateTotalNum(MilepostDesignPlanDetailDto detail, List<MilepostDesignPlanDetailDto> allDetails) {
        if (detail.getNumber() == null) {
            return BigDecimal.ZERO;
        }

        // 构建ID到详设对象的映射
        Map<Long, MilepostDesignPlanDetailDto> idDetailMap = allDetails.stream()
                .collect(Collectors.toMap(MilepostDesignPlanDetailDto::getId, d -> d));

        // 计算所有父级数量的乘积
        BigDecimal parentMultiplier = getParentMultiplier(detail.getParentId(), idDetailMap);

        // 需求数量 = 当前节点数量 × 父级乘积
        return detail.getNumber().multiply(parentMultiplier);
    }

    /**
     * 递归计算父级数量的乘积
     */
    private BigDecimal getParentMultiplier(Long parentId, Map<Long, MilepostDesignPlanDetailDto> idDetailMap) {
        if (parentId == null || parentId == -1L) {
            return BigDecimal.ONE;
        }

        MilepostDesignPlanDetailDto parent = idDetailMap.get(parentId);
        if (parent == null || parent.getNumber() == null) {
            return BigDecimal.ONE;
        }

        // 递归计算：当前父级数量 × 上级父级乘积
        BigDecimal upperParentMultiplier = getParentMultiplier(parent.getParentId(), idDetailMap);
        return parent.getNumber().multiply(upperParentMultiplier);
    }

    /**
     * 通用的相同ERP编码详设行分配方法（用于已领料和已下采购订单）
     */
    private void allocateForSameErpCodeGeneric(List<MilepostDesignPlanDetailDto> sameErpCodeDetails,
                                               BigDecimal totalQuantity,
                                               Map<String, BigDecimal> allocatedMap,
                                               Long projectId,
                                               List<MilepostDesignPlanDetailDto> allProjectDetails,
                                               AllocationConfig config) {
        // 查询项目预算创建日期
        Map<Long, Date> budgetCreateDateMap = queryBudgetCreateDates(sameErpCodeDetails, projectId, allProjectDetails);

        // 为每个详设计算totalNum（采购需求总数）
        for (MilepostDesignPlanDetailDto detail : sameErpCodeDetails) {
            if (detail.getTotalNum() == null) {
                BigDecimal totalNum = calculateTotalNum(detail, allProjectDetails);
                detail.setTotalNum(totalNum);
            }
        }

        // 按物料到货日期排序，日期相同的按已发布需求数量（requirementNum）排序
        sameErpCodeDetails.sort((a, b) -> {
            Date deliveryTimeA = a.getDeliveryTime();
            Date deliveryTimeB = b.getDeliveryTime();

            // 先按物料到货日期排序（空值排在最后）
            if (deliveryTimeA == null && deliveryTimeB == null) {
                // 都为空，按已发布需求数量排序
                return compareRequirementNum(a, b);
            } else if (deliveryTimeA == null) {
                return 1; // A为空，排在后面
            } else if (deliveryTimeB == null) {
                return -1; // B为空，排在后面
            } else {
                int dateCompare = deliveryTimeA.compareTo(deliveryTimeB);
                if (dateCompare == 0) {
                    // 到货日期相同，按已发布需求数量排序
                    return compareRequirementNum(a, b);
                }
                return dateCompare;
            }
        });

        // 执行分配算法
        // 计算总需求数量（使用配置的需求量提取器）
        BigDecimal totalRequirementNum = sameErpCodeDetails.stream()
                .map(detail -> {
                    BigDecimal requirement = config.getRequirementExtractor().apply(detail);
                    return requirement != null ? requirement : BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalQuantity.compareTo(totalRequirementNum) <= 0) {
            // 情况1和2：总量 <= 总需求，按物料到货日期优先分配
            BigDecimal remainingQuantity = totalQuantity;

            for (MilepostDesignPlanDetailDto detail : sameErpCodeDetails) {
                BigDecimal requirementNum = config.getRequirementExtractor().apply(detail);
                if (requirementNum == null) {
                    requirementNum = BigDecimal.ZERO;
                }

                if (remainingQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                    // 没有剩余数量，分配0
                    allocatedMap.put(detail.getId().toString(), BigDecimal.ZERO);
                } else if (remainingQuantity.compareTo(requirementNum) >= 0) {
                    // 剩余数量足够，分配需求数量
                    allocatedMap.put(detail.getId().toString(), requirementNum);
                    remainingQuantity = remainingQuantity.subtract(requirementNum);
                } else {
                    // 剩余数量不足，全部分配给当前详设
                    allocatedMap.put(detail.getId().toString(), remainingQuantity);
                    remainingQuantity = BigDecimal.ZERO;
                }
            }
        } else {
            // 情况3：总量 > 总需求，先满足所有需求，剩余分配给项目预算创建日期最早的详设
            BigDecimal surplus = totalQuantity.subtract(totalRequirementNum);

            // 找到项目预算创建日期最早的详设
            MilepostDesignPlanDetailDto earliestBudgetDetail = sameErpCodeDetails.stream()
                    .min((a, b) -> compareBudgetCreateDate(a, b, budgetCreateDateMap))
                    .orElse(sameErpCodeDetails.get(0));

            for (MilepostDesignPlanDetailDto detail : sameErpCodeDetails) {
                BigDecimal requirementNum = config.getRequirementExtractor().apply(detail);
                if (requirementNum == null) {
                    requirementNum = BigDecimal.ZERO;
                }

                if (detail.getId().equals(earliestBudgetDetail.getId())) {
                    // 项目预算创建日期最早的详设，分配需求数量+剩余量
                    allocatedMap.put(detail.getId().toString(), requirementNum.add(surplus));
                } else {
                    // 其他详设，只分配需求数量
                    allocatedMap.put(detail.getId().toString(), requirementNum);
                }
            }
        }
    }

    /**
     * 通用的库存分配方法（需要已领料依赖）
     */
    private void allocateInventoryQuantityForSameErpCodeGeneric(List<MilepostDesignPlanDetailDto> sameErpCodeDetails,
                                                                BigDecimal totalInventoryQuantity,
                                                                Map<String, BigDecimal> allocatedMap,
                                                                Long projectId,
                                                                List<MilepostDesignPlanDetailDto> allProjectDetails,
                                                                Map<String, BigDecimal> allocatedMaterialReceivedMap) {
        // 查询项目预算创建日期
        Map<Long, Date> budgetCreateDateMap = queryBudgetCreateDates(sameErpCodeDetails, projectId, allProjectDetails);

        // 为每个详设计算totalNum（采购需求总数）和实际需求量
        for (MilepostDesignPlanDetailDto detail : sameErpCodeDetails) {
            if (detail.getTotalNum() == null) {
                BigDecimal totalNum = calculateTotalNum(detail, allProjectDetails);
                detail.setTotalNum(totalNum);
            }
        }

        // 计算每个详设的实际需求量（totalNum - materialReceived）
        List<DetailWithNeed> detailsWithNeed = new ArrayList<>();
        boolean hasValidNeed = true;

        for (MilepostDesignPlanDetailDto detail : sameErpCodeDetails) {
            BigDecimal totalNum = detail.getTotalNum() != null ? detail.getTotalNum() : BigDecimal.ZERO;
            BigDecimal materialReceived = allocatedMaterialReceivedMap.get(detail.getId().toString());
            if (materialReceived == null) {
                materialReceived = BigDecimal.ZERO;
            }

            BigDecimal actualNeed = totalNum.subtract(materialReceived);
            detailsWithNeed.add(new DetailWithNeed(detail, actualNeed));

            if (actualNeed.compareTo(BigDecimal.ZERO) <= 0) {
                hasValidNeed = false;
                break;
            }
        }

        // 如果其一实际需求量（totalNum - materialReceived <= 0），不进行分配逻辑
        if (!hasValidNeed) {
            for (MilepostDesignPlanDetailDto detail : sameErpCodeDetails) {
                allocatedMap.put(detail.getId().toString(), BigDecimal.ZERO);
            }
            return;
        }

        // 按物料到货日期排序，日期相同的按已发布需求数量（requirementNum）排序
        detailsWithNeed.sort((a, b) -> {
            Date deliveryTimeA = a.detail.getDeliveryTime();
            Date deliveryTimeB = b.detail.getDeliveryTime();

            // 先按物料到货日期排序（空值排在最后）
            if (deliveryTimeA == null && deliveryTimeB == null) {
                // 都为空，按已发布需求数量排序
                return compareRequirementNum(a.detail, b.detail);
            } else if (deliveryTimeA == null) {
                return 1; // A为空，排在后面
            } else if (deliveryTimeB == null) {
                return -1; // B为空，排在后面
            } else {
                int dateCompare = deliveryTimeA.compareTo(deliveryTimeB);
                if (dateCompare == 0) {
                    // 到货日期相同，按已发布需求数量排序
                    return compareRequirementNum(a.detail, b.detail);
                }
                return dateCompare;
            }
        });

        // 执行库存分配算法
        executeInventoryAllocation(detailsWithNeed, totalInventoryQuantity, allocatedMap, budgetCreateDateMap);
    }

    /**
     * 执行库存分配算法
     */
    private void executeInventoryAllocation(List<DetailWithNeed> detailsWithNeed,
                                            BigDecimal totalInventoryQuantity,
                                            Map<String, BigDecimal> allocatedMap,
                                            Map<Long, Date> budgetCreateDateMap) {
        // 过滤出有效需求的详设（actualNeed > 0）
        List<DetailWithNeed> validDetails = detailsWithNeed.stream()
                .filter(item -> item.actualNeed.compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());

        if (validDetails.isEmpty()) {
            // 没有有效需求，所有详设分配0
            for (DetailWithNeed item : detailsWithNeed) {
                allocatedMap.put(item.detail.getId().toString(), BigDecimal.ZERO);
            }
            return;
        }

        BigDecimal remainingInventory = totalInventoryQuantity;

        // 按优先级顺序分配
        for (DetailWithNeed item : validDetails) {
            if (remainingInventory.compareTo(BigDecimal.ZERO) <= 0) {
                // 没有剩余库存，分配0
                allocatedMap.put(item.detail.getId().toString(), BigDecimal.ZERO);
                continue;
            }

            if (remainingInventory.compareTo(item.actualNeed) >= 0) {
                // 库存足够，分配实际需求量
                allocatedMap.put(item.detail.getId().toString(), item.actualNeed);
                remainingInventory = remainingInventory.subtract(item.actualNeed);
            } else {
                // 库存不足，全部分配给当前详设
                allocatedMap.put(item.detail.getId().toString(), remainingInventory);
                remainingInventory = BigDecimal.ZERO;
            }
        }

        // 如果还有剩余库存，分配给项目预算创建日期最早的详设
        if (remainingInventory.compareTo(BigDecimal.ZERO) > 0 && !validDetails.isEmpty()) {
            // 找到项目预算创建日期最早的详设
            DetailWithNeed earliestBudgetDetail = validDetails.stream()
                    .min((a, b) -> compareBudgetCreateDate(a.detail, b.detail, budgetCreateDateMap))
                    .orElse(validDetails.get(0));

            String detailId = earliestBudgetDetail.detail.getId().toString();
            BigDecimal currentAllocated = allocatedMap.get(detailId);
            allocatedMap.put(detailId, currentAllocated.add(remainingInventory));
        }

        // 为没有有效需求的详设分配0
        for (DetailWithNeed item : detailsWithNeed) {
            if (item.actualNeed.compareTo(BigDecimal.ZERO) <= 0) {
                allocatedMap.put(item.detail.getId().toString(), BigDecimal.ZERO);
            }
        }
    }

    /**
     * 查询项目预算创建日期
     * 利用boomLevel字段直接找到第二层详设行，简化查询逻辑
     */
    private Map<Long, Date> queryBudgetCreateDates(List<MilepostDesignPlanDetailDto> details, Long projectId,
                                                   List<MilepostDesignPlanDetailDto> allProjectDetails) {
        // 为所有详设计构建boomLevel映射
        Map<Long, MilepostDesignPlanDetailDto> allDetailsMap = new HashMap<>();
        Map<Long, Integer> detailToBoomLevelMap = new HashMap<>();

        // 构建第二层详设行的映射（boomLevel=1且有project_budget_material_id的详设行）
        Map<Long, Long> secondLevelToBudgetMaterialIdMap = new HashMap<>();
        for (MilepostDesignPlanDetailDto detail : allProjectDetails) {
            allDetailsMap.put(detail.getId(), detail);
            Integer boomLevel = detail.getBoomLevel();
            detailToBoomLevelMap.put(detail.getId(), boomLevel);
            if (boomLevel != null && boomLevel == 1 && detail.getProjectBudgetMaterialId() != null) {
                secondLevelToBudgetMaterialIdMap.put(detail.getId(), detail.getProjectBudgetMaterialId());
            }
        }

        // 为每个详设行找到对应的第二层详设行
        Set<Long> budgetMaterialIds = new HashSet<>();
        Map<Long, Long> detailToBudgetMaterialIdMap = new HashMap<>();

        for (MilepostDesignPlanDetailDto detail : details) {
            Long secondLevelDetailId = findSecondLevelDetailIdByBoomLevel(detail, allDetailsMap, detailToBoomLevelMap);
            if (secondLevelDetailId != null) {
                Long budgetMaterialId = secondLevelToBudgetMaterialIdMap.get(secondLevelDetailId);
                if (budgetMaterialId != null) {
                    budgetMaterialIds.add(budgetMaterialId);
                    detailToBudgetMaterialIdMap.put(detail.getId(), budgetMaterialId);
                }
            }
        }

        if (budgetMaterialIds.isEmpty()) {
            return new HashMap<>();
        }

        // 查询项目预算创建日期
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("budgetMaterialIds", new ArrayList<>(budgetMaterialIds));

        List<Map<String, Object>> budgetCreateDates = materialBudgetDetailExtMapper.queryBudgetCreateDates(params);
        Map<Long, Date> budgetMaterialIdToCreateDateMap = budgetCreateDates.stream()
                .collect(Collectors.toMap(
                        item -> (Long) item.get("id"),
                        item -> (Date) item.get("create_at"),
                        (existing, replacement) -> existing
                ));

        // 构建详设ID到项目预算创建日期的映射
        Map<Long, Date> result = new HashMap<>();
        for (MilepostDesignPlanDetailDto detail : details) {
            Long budgetMaterialId = detailToBudgetMaterialIdMap.get(detail.getId());
            if (budgetMaterialId != null) {
                Date createDate = budgetMaterialIdToCreateDateMap.get(budgetMaterialId);
                if (createDate != null) {
                    result.put(detail.getId(), createDate);
                }
            }
        }

        return result;
    }

    /**
     * 利用boomLevel字段查找详设行对应的第二层详设行ID
     * 直接利用计算出的boomLevel，避免复杂的层级计算
     */
    private Long findSecondLevelDetailIdByBoomLevel(MilepostDesignPlanDetailDto detail,
                                                    Map<Long, MilepostDesignPlanDetailDto> allDetailsMap,
                                                    Map<Long, Integer> detailToBoomLevelMap) {
        Integer currentBoomLevel = detailToBoomLevelMap.get(detail.getId());

        // 如果当前详设行就是第二层（boomLevel=1），直接返回
        if (currentBoomLevel != null && currentBoomLevel == 1) {
            return detail.getId();
        }

        // 如果是第一层或更上层（boomLevel=0），返回null（不应该有这种情况）
        if (currentBoomLevel != null && currentBoomLevel <= 0) {
            return null;
        }

        // 如果是第三层或更下层（boomLevel>=2），通过parent_id往上找到第二层（boomLevel=1）
        MilepostDesignPlanDetailDto current = detail;
        while (current != null && current.getParentId() != null && current.getParentId() != -1L) {
            MilepostDesignPlanDetailDto parent = allDetailsMap.get(current.getParentId());
            if (parent != null) {
                Integer parentBoomLevel = detailToBoomLevelMap.get(parent.getId());
                if (parentBoomLevel != null && parentBoomLevel == 1) {
                    return parent.getId();
                }
            }
            current = parent;
        }

        return null;
    }

    /**
     * 比较项目预算创建日期
     * 现在budgetCreateDateMap的key是详设ID，不是project_budget_material_id
     */
    private int compareBudgetCreateDate(MilepostDesignPlanDetailDto a, MilepostDesignPlanDetailDto b,
                                        Map<Long, Date> budgetCreateDateMap) {
        Date createDateA = budgetCreateDateMap.get(a.getId());
        Date createDateB = budgetCreateDateMap.get(b.getId());

        if (createDateA == null && createDateB == null) {
            return 0;
        } else if (createDateA == null) {
            return 1; // A为空，排在后面
        } else if (createDateB == null) {
            return -1; // B为空，排在后面
        } else {
            return createDateA.compareTo(createDateB);
        }
    }

    /**
     * 比较两个详设的已发布需求数量（requirementNum）
     * 按需求数量从大到小排序，需求数量大的排在前面
     */
    private int compareRequirementNum(MilepostDesignPlanDetailDto a, MilepostDesignPlanDetailDto b) {
        BigDecimal numA = a.getRequirementNum();
        BigDecimal numB = b.getRequirementNum();

        if (numA == null && numB == null) {
            return 0;
        } else if (numA == null) {
            return 1; // A为空，排在后面
        } else if (numB == null) {
            return -1; // B为空，排在后面
        } else {
            // 按需求数量从大到小排序，所以用B比较A
            return numB.compareTo(numA);
        }
    }

    /**
     * 将查询结果列表转换为Map，便于快速查找
     */
    private Map<String, Map<String, Object>> batchQueryToMap(List<Map<String, Object>> dataList, String keyField) {
        if (CollectionUtils.isEmpty(dataList)) {
            return new HashMap<>();
        }
        return dataList.stream()
                .collect(Collectors.toMap(
                        item -> (String) item.get(keyField),
                        item -> item,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 从数据Map中安全获取数量值的辅助方法
     */
    private BigDecimal getQuantityFromDataMap(Map<String, Map<String, Object>> dataMap, String erpCode, String fieldName) {
        Map<String, Object> data = dataMap.get(erpCode);
        return data != null ? getBigDecimalFromMap(data, fieldName) : BigDecimal.ZERO;
    }

    /**
     * 安全地从Map中获取BigDecimal值
     */
    private BigDecimal getBigDecimalFromMap(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }
}
