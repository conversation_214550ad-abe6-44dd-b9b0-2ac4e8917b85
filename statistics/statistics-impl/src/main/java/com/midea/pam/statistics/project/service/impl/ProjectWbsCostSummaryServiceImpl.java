package com.midea.pam.statistics.project.service.impl;

import com.google.common.collect.Lists;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.constants.ProjectWbsCostConstant;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.dto.ContractDTO;
import com.midea.pam.common.ctc.dto.ProjectHroWbsSummaryDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsSummaryDto;
import com.midea.pam.common.ctc.dto.WbsDynamicFieldsDto;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ProjectCostExecuteStatus;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.statistics.dto.VendorPenaltyProjectCostSummaryDto;
import com.midea.pam.common.statistics.entity.HroPurchaseContractTotal;
import com.midea.pam.common.statistics.entity.HroPurchaseContractTotalExample;
import com.midea.pam.common.statistics.entity.HroRequirementBudget;
import com.midea.pam.common.statistics.entity.HroRequirementBudgetExample;
import com.midea.pam.common.statistics.entity.HroRequirementContractDetail;
import com.midea.pam.common.statistics.entity.HroRequirementContractDetailExample;
import com.midea.pam.common.statistics.entity.ProjectCostExecuteRecord;
import com.midea.pam.common.statistics.entity.ProjectCurrentIncomeSummary;
import com.midea.pam.common.statistics.entity.ProjectEaDetail;
import com.midea.pam.common.statistics.entity.ProjectEaDetailExample;
import com.midea.pam.common.statistics.entity.ProjectMaterialDetail;
import com.midea.pam.common.statistics.entity.ProjectMaterialDetailExample;
import com.midea.pam.common.statistics.entity.ProjectPurchaseContractDetail;
import com.midea.pam.common.statistics.entity.ProjectPurchaseContractDetailExample;
import com.midea.pam.common.statistics.entity.ProjectWbsExecuteDetail;
import com.midea.pam.common.statistics.entity.ProjectWorkingHourDetail;
import com.midea.pam.common.statistics.entity.ProjectWorkingHourDetailExample;
import com.midea.pam.common.statistics.entity.RequirementBudget;
import com.midea.pam.common.statistics.entity.RequirementBudgetExample;
import com.midea.pam.common.statistics.entity.RequirementPoDetail;
import com.midea.pam.common.statistics.entity.RequirementPoDetailExample;
import com.midea.pam.common.statistics.entity.RequirementPurchaseContractDetail;
import com.midea.pam.common.statistics.entity.RequirementPurchaseContractDetailExample;
import com.midea.pam.common.statistics.entity.VendorPenaltyProjectCostSummary;
import com.midea.pam.common.statistics.entity.VendorPenaltyProjectCostSummaryExample;
import com.midea.pam.common.statistics.excelVo.ProjectWbsCostExcelVo;
import com.midea.pam.common.statistics.vo.ProjectWbsCostBatchVo;
import com.midea.pam.common.statistics.vo.ProjectWbsCostByReceiptsVO;
import com.midea.pam.common.statistics.vo.ProjectWbsCostCompleteVO;
import com.midea.pam.common.statistics.vo.ProjectWbsCostOnTheWayVO;
import com.midea.pam.common.statistics.vo.ProjectWbsCostSystemVO;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.statistics.mapper.DictExtMapper;
import com.midea.pam.statistics.mapper.HroPurchaseContractTotalExtMapper;
import com.midea.pam.statistics.mapper.HroPurchaseContractTotalMapper;
import com.midea.pam.statistics.mapper.HroRequirementBudgetExtMapper;
import com.midea.pam.statistics.mapper.HroRequirementBudgetMapper;
import com.midea.pam.statistics.mapper.HroRequirementContractDetailExtMapper;
import com.midea.pam.statistics.mapper.HroRequirementContractDetailMapper;
import com.midea.pam.statistics.mapper.ProjectCostExecuteRecordMapper;
import com.midea.pam.statistics.mapper.ProjectCurrentIncomeSummaryExtMapper;
import com.midea.pam.statistics.mapper.ProjectEaDetailExtMapper;
import com.midea.pam.statistics.mapper.ProjectEaDetailMapper;
import com.midea.pam.statistics.mapper.ProjectMapper;
import com.midea.pam.statistics.mapper.ProjectMaterialDetailExtMapper;
import com.midea.pam.statistics.mapper.ProjectMaterialDetailMapper;
import com.midea.pam.statistics.mapper.ProjectPurchaseContractDetailExtMapper;
import com.midea.pam.statistics.mapper.ProjectPurchaseContractDetailMapper;
import com.midea.pam.statistics.mapper.ProjectWbsCostSummaryExtMapper;
import com.midea.pam.statistics.mapper.ProjectWbsCostSummaryMapper;
import com.midea.pam.statistics.mapper.ProjectWbsExecuteDetailExtMapper;
import com.midea.pam.statistics.mapper.ProjectWorkingHourDetailExtMapper;
import com.midea.pam.statistics.mapper.ProjectWorkingHourDetailMapper;
import com.midea.pam.statistics.mapper.RequirementBudgetExtMapper;
import com.midea.pam.statistics.mapper.RequirementBudgetMapper;
import com.midea.pam.statistics.mapper.RequirementPoDetailExtMapper;
import com.midea.pam.statistics.mapper.RequirementPoDetailMapper;
import com.midea.pam.statistics.mapper.RequirementPurchaseContractDetailExtMapper;
import com.midea.pam.statistics.mapper.RequirementPurchaseContractDetailMapper;
import com.midea.pam.statistics.mapper.VendorPenaltyProjectCostSummaryExtMapper;
import com.midea.pam.statistics.mapper.VendorPenaltyProjectCostSummaryMapper;
import com.midea.pam.statistics.project.service.ProjectCostService;
import com.midea.pam.statistics.project.service.ProjectWbsBudgetSummaryService;
import com.midea.pam.statistics.project.service.ProjectWbsCostSummaryService;
import com.midea.pam.statistics.project.service.ProjectWbsExecuteDetailService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022/07/21
 * @description 项目Wbs成本
 */
public class ProjectWbsCostSummaryServiceImpl implements ProjectWbsCostSummaryService {

    private static final Integer EACH_THREAD_DATA_NUM = 5;

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectEaDetailMapper projectEaDetailMapper;
    @Resource
    private ProjectEaDetailExtMapper projectEaDetailExtMapper;
    @Resource
    private RequirementBudgetMapper requirementBudgetMapper;
    @Resource
    private RequirementBudgetExtMapper requirementBudgetExtMapper;
    @Resource
    private RequirementPurchaseContractDetailMapper requirementPurchaseContractDetailMapper;
    @Resource
    private RequirementPurchaseContractDetailExtMapper requirementPurchaseContractDetailExtMapper;
    @Resource
    private RequirementPoDetailMapper requirementPoDetailMapper;
    @Resource
    private RequirementPoDetailExtMapper requirementPoDetailExtMapper;
    @Resource
    private ProjectMaterialDetailMapper projectMaterialDetailMapper;
    @Resource
    private ProjectMaterialDetailExtMapper projectMaterialDetailExtMapper;
    @Resource
    private ProjectPurchaseContractDetailMapper projectPurchaseContractDetailMapper;
    @Resource
    private ProjectPurchaseContractDetailExtMapper projectPurchaseContractDetailExtMapper;
    @Resource
    private ProjectWorkingHourDetailMapper projectWorkingHourDetailMapper;
    @Resource
    private ProjectWorkingHourDetailExtMapper projectWorkingHourDetailExtMapper;
    @Resource
    private ProjectWbsCostSummaryMapper projectWbsCostSummaryMapper;
    @Resource
    private ProjectWbsCostSummaryExtMapper projectWbsCostSummaryExtMapper;
    @Resource
    private HroRequirementBudgetMapper hroRequirementBudgetMapper;
    @Resource
    private HroRequirementBudgetExtMapper hroRequirementBudgetExtMapper;
    @Resource
    private HroRequirementContractDetailMapper hroRequirementContractDetailMapper;
    @Resource
    private HroRequirementContractDetailExtMapper hroRequirementContractDetailExtMapper;
    @Resource
    private HroPurchaseContractTotalMapper hroPurchaseContractTotalMapper;
    @Resource
    private HroPurchaseContractTotalExtMapper hroPurchaseContractTotalExtMapper;
    @Resource
    private DictExtMapper dictExtMapper;
    @Resource
    private ProjectWbsBudgetSummaryService projectWbsBudgetSummaryService;
    @Resource
    private ProjectCostExecuteRecordMapper projectCostExecuteRecordMapper;
    @Resource
    private ProjectWbsExecuteDetailService projectWbsExecuteDetailService;
    @Resource
    private VendorPenaltyProjectCostSummaryMapper vendorPenaltyProjectCostSummaryMapper;
    @Resource
    private VendorPenaltyProjectCostSummaryExtMapper vendorPenaltyProjectCostSummaryExtMapper;
    @Resource
    private ProjectWbsExecuteDetailExtMapper projectWbsExecuteDetailExtMapper;
    @Resource
    private ProjectCostService projectCostService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectCurrentIncomeSummaryExtMapper projectCurrentIncomeSummaryExtMapper;
    @Qualifier("projectWbsCostJobExecutor")
    @Resource
    private ThreadPoolTaskExecutor projectWbsCostJobExecutor;

    @Qualifier("getProjectWbsCostSummaryExecutor")
    @Resource
    private ThreadPoolTaskExecutor getProjectWbsCostSummaryExecutor;


    /**
     * 实时查
     *
     * @param projectIds
     * @return
     * @description
     */
    public Map<Long, List<ProjectWbsBudgetDto>> getProjectWbsCostSummaryBatch(List<Long> projectIds) {
        Map<Long, List<ProjectWbsBudgetDto>> projectWbsBudgetMap = new HashMap<>();
        if (CollectionUtils.isEmpty(projectIds)) {
            return projectWbsBudgetMap;
        }

        int threadNum = projectIds.size();
        final CountDownLatch countDownLatch = new CountDownLatch(threadNum);
        for (int i = 0; i < threadNum; i++) {
            Long projectId = projectIds.get(i);
            getProjectWbsCostSummaryExecutor.execute(() -> {
                try {
                    // 多线程异步执行
                    getProjectWbsCostSummary(projectId, projectWbsBudgetMap);
                } catch (Exception e) {
                    logger.error("getProjectWbsCostSummaryBatch操作记录失败", e);
                } finally {
                    countDownLatch.countDown();
                    if (Objects.equals(countDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after getProjectWbsCostSummaryBatch", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("getReleasedOrderDetailByRequirementId的await失败", ex);
            Thread.currentThread().interrupt();
        }
        return projectWbsBudgetMap;
    }

    public void getProjectWbsCostSummary(Long projectId, Map<Long, List<ProjectWbsBudgetDto>> projectWbsBudgetMap) {
        List<ProjectWbsBudgetDto> list = new ArrayList<>();
        //用于统计成功数据
        try {
            //查询项目ID下所有的数据
            ProjectWbsCostBatchVo projectWbsCostBatchVo = getProjectWbsCostBatchVo(Arrays.asList(projectId));
            //计算各列表数据的值
            calculation(projectWbsCostBatchVo, null);
            list = projectWbsCostBatchVo.getProjectWbsBudgetDtos();
        } catch (Exception e) {
            logger.error(String.format("查询剩余可用预算出现异常，异常项目id为【%s】", projectId), e);
            throw e;
        }
        projectWbsBudgetMap.put(projectId, list);
    }

    /**
     * 定时任务-查询并入库
     *
     * @param projectIds
     * @return
     * @description 定时任务-查询并入库
     */
    public List<ProjectWbsBudgetDto> getProjectWbsCostSummaryForJob(List<Long> projectIds, Long executeId) {
        List<ProjectWbsBudgetDto> list = new ArrayList<>();
        //用于统计成功数据
        try {
            //查询项目ID下所有的数据
            ProjectWbsCostBatchVo projectWbsCostBatchVo = getProjectWbsCostBatchVo(projectIds);
            //计算各列表数据的值并给executeId赋值
            calculation(projectWbsCostBatchVo, executeId);
            //生成其他无需参与计算的数据
            generateOtherData(projectIds, executeId);
            //原数据逻辑删除
//            deleteBatchAll(projectIds);
            //数据落表
            insertBatchAll(projectWbsCostBatchVo);
            list = projectWbsCostBatchVo.getProjectWbsBudgetDtos();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        }
        return list;
    }

    private void generateOtherData(List<Long> projectIds, Long executeId) {
        List<ProjectCurrentIncomeSummary> incomeSummaryList = projectCurrentIncomeSummaryExtMapper.getAllByProjectIDs(projectIds);
        //已确认成本汇总
        if (ListUtils.isNotEmpty(incomeSummaryList)) {
            incomeSummaryList.forEach(e -> e.setExecuteId(executeId));
            List<List<ProjectCurrentIncomeSummary>> lists = ListUtils.splistList(incomeSummaryList, 1000);
            for (List<ProjectCurrentIncomeSummary> list : lists) {
                projectCurrentIncomeSummaryExtMapper.insertBatch(list);
            }
        }
    }

    /**
     * @param projectIds
     * @return
     * @description 查询项目ID下所有的数据
     */
    private ProjectWbsCostBatchVo getProjectWbsCostBatchVo(List<Long> projectIds) {
        List<ProjectWbsBudgetDto> projectWbsCostSummaries = projectWbsCostSummaryExtMapper.summary(projectIds);
        List<RequirementBudget> requirementBudgets = projectWbsCostSummaryExtMapper.requirementBudget(projectIds);
        List<RequirementPoDetail> requirementPoDetails = projectWbsCostSummaryExtMapper.requirementPoDetail(projectIds);
        //复制并初始数据-需求单据中的物料已下po金额
        List<RequirementPoDetail> poDetailList2 = requirementPoDetails.stream()
                .map(e -> {
                    RequirementPoDetail newDetail = BeanConverter.copy(e, RequirementPoDetail.class);
                    newDetail.setStorageCount(BigDecimal.ZERO);//入库数量
                    //折后金额(不含税),poType=1时为单价,因源数据是poType=1的值，故数量*单价
                    BigDecimal decimal = e.getOrderNum().multiply(e.getDiscountPrice(), new MathContext(2, RoundingMode.HALF_UP));
                    newDetail.setDiscountPrice(decimal);
                    newDetail.setPoTotalAmount(BigDecimal.ZERO);//po费用总金额
                    newDetail.setPoType(0);
                    newDetail.setActivityCode(ProjectWbsCostConstant.SYSTEM_ACTIVITY);
                    return newDetail;
                }).collect(Collectors.toList());
        requirementPoDetails.addAll(poDetailList2);
        List<ProjectPurchaseContractDetail> projectPurchaseContractDetails = projectWbsCostSummaryExtMapper.projectPurchaseContractDetail(projectIds);
        List<ProjectMaterialDetail> projectMaterialDetails = projectWbsCostSummaryExtMapper.projectMaterialDetail(projectIds);
        List<ProjectEaDetail> projectEaDetails = projectWbsCostSummaryExtMapper.projectEaDetail(projectIds);
        List<ProjectWorkingHourDetail> projectWorkingHourDetails = projectWbsCostSummaryExtMapper.projectWorkingHourDetail(projectIds);
        List<RequirementPurchaseContractDetail> requirementPurchaseContractDetails = projectWbsCostSummaryExtMapper.requirementPurchaseContractDetail(projectIds);
        List<HroRequirementBudget> hroRequirementBudgets = hroRequirementBudgetExtMapper.getHroRequirementBudgetDetail(projectIds);
        List<HroRequirementContractDetail> hroRequirementContractDetails = hroRequirementContractDetailExtMapper.getHroRequirementContractDetail(projectIds);
        List<HroPurchaseContractTotal> hroPurchaseContractTotals = hroPurchaseContractTotalExtMapper.getHroPurchaseContractTotal(projectIds);
        List<VendorPenaltyProjectCostSummary> vendorPenaltyProjectCostSummaries = vendorPenaltyProjectCostSummaryExtMapper.getVendorPenaltyProjectCostSummary(projectIds);
//        List<VendorPenaltyProjectCostSummary> vendorPenaltyProjectCostSummaries = getExistPenaltyProjectCostSummary(vendorPenaltyProjectCostSummaryDtoList, projectIds);

        List<HroPurchaseContractTotal> hroPurchaseContractTotalList = new ArrayList<>();
        for (HroPurchaseContractTotal entity : hroPurchaseContractTotals) {
            entity.setSurplusAmount(entity.getTotalAmount().subtract(entity.getBillMhAmount()).subtract(entity.getBillCostAmount()));
            hroPurchaseContractTotalList.add(entity);
            HroPurchaseContractTotal po = BeanConverter.copy(entity, HroPurchaseContractTotal.class);
            po.setBudgetType(2);
            po.setSurplusAmount(entity.getBillCostAmount().add(entity.getBillMhAmount()));
            hroPurchaseContractTotalList.add(po);
        }
        //过滤po数据，下单=取消，订单行状态中取消和关闭有问题,过滤脏数据
        List<RequirementPoDetail> requirementPoDetailList = requirementPoDetails.stream().filter(Objects::nonNull).filter(e -> Objects.nonNull(e.getProjectWbsReceiptsId()) && (e.getOrderNum().compareTo(e.getCancelNum()) != 0)).collect(Collectors.toList());
        List<ProjectMaterialDetail> projectMaterialDetailList = projectMaterialDetails.stream().filter(Objects::nonNull).filter(e -> StringUtils.isNotBlank(e.getMaterialCode())).collect(Collectors.toList());
        //过滤不存在需要预算的System行
        List<ProjectWbsBudgetDto> wbsCostSummaryList;
        if (ListUtils.isNotEmpty(requirementBudgets)) {
            //收集保留System的条件
            final List<String> filterList = requirementBudgets.stream()
                    .map(entity -> getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())).collect(Collectors.toList());
            wbsCostSummaryList = projectWbsCostSummaries.stream().map(e -> {
                if (ProjectWbsCostConstant.SYSTEM_ACTIVITY.equals(e.getActivityCode()) && !filterList.contains(getExportKey(e.getProjectId(), e.getWbsSummaryCode(), e.getActivityCode()))) {
                    e.setDemandCost(BigDecimal.ZERO);
                }
                return e;
            }).filter(entity -> !Objects.isNull(entity.getId()) || filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode()))).collect(Collectors.toList());
        } else {
            wbsCostSummaryList = projectWbsCostSummaries.stream().filter(entity -> !Objects.isNull(entity.getId())).collect(Collectors.toList());
        }
        return new ProjectWbsCostBatchVo(wbsCostSummaryList, requirementBudgets, requirementPoDetailList, projectPurchaseContractDetails
                , projectMaterialDetailList, projectEaDetails, projectWorkingHourDetails, requirementPurchaseContractDetails,
                hroRequirementBudgets, hroRequirementContractDetails, hroPurchaseContractTotalList
                , vendorPenaltyProjectCostSummaries);
    }

    private List<VendorPenaltyProjectCostSummary> getExistPenaltyProjectCostSummary(List<VendorPenaltyProjectCostSummaryDto> vendorPenaltyProjectCostSummaryDtoList, List<Long> projectIds) {
        List<Long> penaltyDetailIds = vendorPenaltyProjectCostSummaryDtoList.stream()
                .map(VendorPenaltyProjectCostSummaryDto::getPenaltyDetailId).collect(Collectors.toList());
        if (!penaltyDetailIds.isEmpty()) {
            vendorPenaltyProjectCostSummaryExtMapper.deletePenaltyDetailIds(penaltyDetailIds);
        }
        // 对罚扣状态为生效的未删除的罚扣明细进行项目成本汇总
        List<VendorPenaltyProjectCostSummary> vendorPenaltyProjectCostSummaries = vendorPenaltyProjectCostSummaryDtoList
                .stream().filter(e -> !e.getDeletedFlag() && e.getVendorPenaltyStatus() == 2).collect(Collectors.toList());
        // 将之前已汇总的查出来
        VendorPenaltyProjectCostSummaryExample summaryExample = new VendorPenaltyProjectCostSummaryExample();
        summaryExample.createCriteria()
                .andProjectIdIn(projectIds)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        vendorPenaltyProjectCostSummaries.addAll(vendorPenaltyProjectCostSummaryMapper.selectByExample(summaryExample));
        return vendorPenaltyProjectCostSummaries;
    }

    /**
     * @param batchVo
     * @description 计算数据的值
     */
    private void calculation(ProjectWbsCostBatchVo batchVo, Long executeId) {
        Map<String, BigDecimal> getMaterial = new HashMap<>();
        Map<String, BigDecimal> returnMaterial = new HashMap<>();
        Map<String, BigDecimal> allocationExecute = new HashMap<>();
        Map<String, BigDecimal> budgetExecute = new HashMap<>();
        Map<String, BigDecimal> wkComplete = new HashMap<>();
        Map<String, BigDecimal> wkOnTheWay = new HashMap<>();
        Map<String, BigDecimal> ea = new HashMap<>();
        Map<String, BigDecimal> eaOnTheWay = new HashMap<>();
        Map<String, BigDecimal> eaComplete = new HashMap<>();
        Map<String, BigDecimal> poDown = new HashMap<>();
        Map<String, BigDecimal> poOnTheWay = new HashMap<>();
        Map<String, BigDecimal> contractDown = new HashMap<>();
        Map<String, BigDecimal> requirementBudget = new HashMap<>();
        Map<String, BigDecimal> hroContractDown = new HashMap<>();
        Map<String, BigDecimal> hrRequirementBudget = new HashMap<>();
        Map<String, BigDecimal> hroPurchaseContractOnTheWay = new HashMap<>();
        Map<String, BigDecimal> hroPurchaseContractComplete = new HashMap<>();
        Map<String, BigDecimal> vendorPenaltyProjectCostOnTheWay = new HashMap<>();
        Map<String, BigDecimal> vendorPenaltyProjectCost = new HashMap<>();
        List<ProjectMaterialDetail> projectMaterialDetails = batchVo.getProjectMaterialDetails();
        List<ProjectPurchaseContractDetail> projectPurchaseContractDetails = batchVo.getProjectPurchaseContractDetails();
        List<ProjectWorkingHourDetail> projectWorkingHourDetails = batchVo.getProjectWorkingHourDetails();
        List<ProjectEaDetail> projectEaDetails = batchVo.getProjectEaDetails();
        List<RequirementPoDetail> requirementPoDetails = batchVo.getRequirementPoDetails();
        List<RequirementPurchaseContractDetail> requirementPurchaseContractDetails = batchVo.getRequirementPurchaseContractDetails();
        List<RequirementBudget> requirementBudgets = batchVo.getRequirementBudgets();
        List<ProjectWbsBudgetDto> projectWbsCostSummaries = batchVo.getProjectWbsBudgetDtos();
        List<HroRequirementBudget> hroRequirementBudgets = batchVo.getHroRequirementBudgets();
        List<HroRequirementContractDetail> hroRequirementContractDetails = batchVo.getHroRequirementContractDetails();
        List<HroPurchaseContractTotal> hroPurchaseContractTotals = batchVo.getHroPurchaseContractTotals();
        List<VendorPenaltyProjectCostSummary> vendorPenaltyProjectCostSummaries = batchVo.getVendorPenaltyProjectCostSummaries();

        //人力点工需求-关联采购合同详情
        if (ListUtils.isNotEmpty(hroRequirementContractDetails)) {
            hroRequirementContractDetails.forEach(e -> e.setExecuteId(executeId));
            hroContractDown = hroRequirementContractDetails.stream()
                    .collect(Collectors.groupingBy(n -> getContractDownKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode(), n.getRequirementCode(), n.getRoleName())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().map(HroRequirementContractDetail::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        }
        //人力点工需求预算
        if (ListUtils.isNotEmpty(hroRequirementBudgets)) {
            for (HroRequirementBudget entity : hroRequirementBudgets) {
                entity.setExecuteId(executeId);
                if (entity.getRowRecordType() == 0) {
                    /**
                     * 该WBS下一个需求单据的所有行数据，如果存在一条数据“未下达量”不为0，
                     * 则取这个需求单据的“物料采购需求预算” = 这个需求单据分配到这个WBS的“物料采购”需求预算 - 这个需求单据中的物料已下po金额的合计金额
                     */
                    BigDecimal downAmount = toFilterNull(hroContractDown.isEmpty() ? BigDecimal.ZERO : hroContractDown.get(getContractDownKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode(), entity.getRequirementCode(), entity.getRoleName())));
                    if (Objects.equals(entity.getReceiptsType(), "已完成")) {
                        entity.setBudgetOccupiedAmount(null);
                        entity.setDownAmount(null);
                        entity.setRemainingCostAmount(BigDecimal.ZERO);
                    } else {
                        //已下po金额的合计金额汇总
                        entity.setDownAmount(downAmount);
                        //剩余预算金额
                        entity.setRemainingCostAmount(entity.getBudgetOccupiedAmount().subtract(downAmount));
                    }
                }
            }

            hrRequirementBudget = hroRequirementBudgets.stream()
                    .collect(Collectors.groupingBy(n -> getRequirementBudgetKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().map(HroRequirementBudget::getRemainingCostAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        }

        /**
         * 统计领退料维度的值
         */
        if (ListUtils.isNotEmpty(projectMaterialDetails)) {
            projectMaterialDetails.forEach(e -> e.setExecuteId(executeId));
            getMaterial = projectMaterialDetails.stream()
                    .collect(Collectors.groupingBy(n -> getGetMaterialKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 1)
                                    .map(ProjectMaterialDetail::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
            returnMaterial = projectMaterialDetails.stream()
                    .collect(Collectors.groupingBy(n -> getReturnMaterialKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 2)
                                    .map(ProjectMaterialDetail::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        }
        //分配进度执行金额
        if (ListUtils.isNotEmpty(projectPurchaseContractDetails)) {
            projectPurchaseContractDetails.forEach(e -> e.setExecuteId(executeId));
            allocationExecute = projectPurchaseContractDetails.stream()
                    .collect(Collectors.groupingBy(n -> getAllocationExecuteAmountKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 1)
                                    .map(ProjectPurchaseContractDetail::getAllocationExecuteAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
            //累计进度执行金额
            budgetExecute = projectPurchaseContractDetails.stream()
                    .collect(Collectors.groupingBy(n -> getBudgetExecuteAmountTotalVOKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 2)
                                    .map(ProjectPurchaseContractDetail::getBudgetExecuteAmountTotal).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        }
        /**
         * 统计工时维度的值
         */
        if (ListUtils.isNotEmpty(projectWorkingHourDetails)) {
            projectWorkingHourDetails.forEach(e -> e.setExecuteId(executeId));
            wkComplete = projectWorkingHourDetails.stream()
                    .collect(Collectors.groupingBy(n -> getWkCompleteKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 2)
                                    .map(ProjectWorkingHourDetail::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
            wkOnTheWay = projectWorkingHourDetails.stream()
                    .collect(Collectors.groupingBy(n -> getWkOnTheWayKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 1)
                                    .map(ProjectWorkingHourDetail::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        }
        /**
         * 统计EA单维度的值
         */
        if (ListUtils.isNotEmpty(projectEaDetails)) {
            projectEaDetails.forEach(e -> e.setExecuteId(executeId));
            ea = projectEaDetails.stream()
                    .collect(Collectors.groupingBy(n -> getEaKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 0)
                                    .map(ProjectEaDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
            eaOnTheWay = projectEaDetails.stream()
                    .collect(Collectors.groupingBy(n -> getEaOnTheWayKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 1)
                                    .map(ProjectEaDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
            eaComplete = projectEaDetails.stream()
                    .collect(Collectors.groupingBy(n -> getEaCompleteKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 2)
                                    .map(ProjectEaDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        }
        //po
        if (ListUtils.isNotEmpty(requirementPoDetails)) {
            requirementPoDetails.forEach(e -> e.setExecuteId(executeId));
            poDown = requirementPoDetails.stream()
                    .collect(Collectors.groupingBy(n -> getPoDownKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode(), n.getRequirementCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getPoType() == 0)
                                    .map(RequirementPoDetail::getDiscountPrice).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
            poOnTheWay = requirementPoDetails.stream()
                    .collect(Collectors.groupingBy(n -> getPoOnTheWayKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getPoType() == 1)
                                    .map(RequirementPoDetail::getPoTotalAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        }
        //已下采购合同
        if (ListUtils.isNotEmpty(requirementPurchaseContractDetails)) {
            requirementPurchaseContractDetails.forEach(e -> e.setExecuteId(executeId));
            contractDown = requirementPurchaseContractDetails.stream()
                    .collect(Collectors.groupingBy(n -> getContractDownKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode(), n.getRequirementCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().map(RequirementPurchaseContractDetail::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        }
        /**
         * 统计项目成本人力点工采购合同对账统计的值
         */
        if (ListUtils.isNotEmpty(hroPurchaseContractTotals)) {
            hroPurchaseContractTotals.forEach(e -> e.setExecuteId(executeId));
            hroPurchaseContractOnTheWay = hroPurchaseContractTotals.stream()
                    .collect(Collectors.groupingBy(n -> getHroPurchaseContractOnTheWay(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 1)
                                    .map(HroPurchaseContractTotal::getSurplusAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
            hroPurchaseContractComplete = hroPurchaseContractTotals.stream()
                    .collect(Collectors.groupingBy(n -> getHroPurchaseContractCompleteKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().filter(obj -> obj.getBudgetType() == 2)
                                    .map(HroPurchaseContractTotal::getSurplusAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        }

        // 供应商罚扣项目成本统计
        if (ListUtils.isNotEmpty(vendorPenaltyProjectCostSummaries)) {
            vendorPenaltyProjectCostSummaries.forEach(e -> e.setExecuteId(executeId));
            vendorPenaltyProjectCost = vendorPenaltyProjectCostSummaries.stream().collect(Collectors.toMap(
                    e -> getHroPurchaseContractCompleteKey(e.getProjectId(), e.getWbsCode(), e.getActivityCode()),
                    VendorPenaltyProjectCostSummary::getOccurCost, BigDecimalUtils::add));
            vendorPenaltyProjectCostOnTheWay = vendorPenaltyProjectCostSummaries.stream().collect(Collectors.toMap(
                    e -> getHroPurchaseContractCompleteKey(e.getProjectId(), e.getWbsCode(), e.getActivityCode()),
                    e -> e.getProjectCost().subtract(e.getOccurCost()), BigDecimalUtils::add));
        }
        //计算需求预算汇总
        if (ListUtils.isNotEmpty(requirementBudgets)) {
            for (RequirementBudget entity : requirementBudgets) {
                if (entity.getBudgetType() == 0) {
                    /**
                     * 该WBS下一个需求单据的所有行数据，如果存在一条数据“未下达量”不为0，
                     * 则取这个需求单据的“物料采购需求预算” = 这个需求单据分配到这个WBS的“物料采购”需求预算 - 这个需求单据中的物料已下po金额的合计金额
                     */
                    BigDecimal downAmount = toFilterNull(poDown.isEmpty() ? BigDecimal.ZERO : poDown.get(getPoDownKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode(), entity.getRequirementCode())));
                    if (entity.getUnreleasedQuantity() > 0) {
                        //已下po金额的合计金额汇总
                        entity.setReceiptsType("未完成");
                        entity.setDownAmount(downAmount);
                        //剩余预算金额
                        entity.setRemainingCostAmount(entity.getBudgetOccupiedAmount().subtract(downAmount));
                    } else {
                        entity.setReceiptsType("已完成");
                        entity.setBudgetOccupiedAmount(null);
                        entity.setDownAmount(null);
                        entity.setRemainingCostAmount(BigDecimal.ZERO);
                    }
                } else if (entity.getBudgetType() == 1) {
                    //计算需求单据中的物料已下采购合同金额
                    BigDecimal downAmount = toFilterNull(contractDown.isEmpty() ? BigDecimal.ZERO : contractDown.get(getContractDownKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode(), entity.getRequirementCode())));
                    /**
                     * 该WBS下一个需求单据的所有行数据，如果存在一条数据“未下达量”不为0，
                     * 则取这个需求单据的“物料采购需求预算” = 这个需求单据分配到这个WBS的“物料采购”需求预算 - 这个需求单据中的物料已下po金额的合计金额
                     */
                    if (entity.getUnreleasedQuantity() > 0) {
                        //当前物料采购需求预算
                        entity.setReceiptsType("未完成");
                        entity.setDownAmount(downAmount);
                        //剩余预算金额
                        entity.setRemainingCostAmount(entity.getBudgetOccupiedAmount().subtract(downAmount));
                    } else {
                        entity.setReceiptsType("已完成");
                        entity.setBudgetOccupiedAmount(null);
                        entity.setDownAmount(null);
                        entity.setRemainingCostAmount(BigDecimal.ZERO);
                    }
                } else {
                    entity.setRemainingCostAmount(entity.getBudgetOccupiedAmount());
                }
            }

        }
        //需求预算
        if (ListUtils.isNotEmpty(requirementBudgets)) {
            requirementBudgets.forEach(e -> e.setExecuteId(executeId));
            requirementBudget = requirementBudgets.stream()
                    .collect(Collectors.groupingBy(n -> getRequirementBudgetKey(n.getProjectId(), n.getWbsSummaryCode(), n.getActivityCode())
                            , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().map(RequirementBudget::getRemainingCostAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
        }
        //计算成本汇总
        if (ListUtils.isNotEmpty(projectWbsCostSummaries)) {
            //计算各项费用
            for (ProjectWbsBudgetDto projectWbsCostSummaryVo : projectWbsCostSummaries) {
                if (StringUtils.isNotBlank(projectWbsCostSummaryVo.getActivityCode()) && ProjectWbsCostConstant.SYSTEM_ACTIVITY.equals(projectWbsCostSummaryVo.getActivityCode())) {
                    BigDecimal demandCost = requirementBudget.isEmpty() ? BigDecimal.ZERO : requirementBudget.get(getRequirementBudgetKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode()));
                    projectWbsCostSummaryVo.setDemandCost(toFilterNull(demandCost));//需求预算
                    projectWbsCostSummaryVo.setRemainingCost(projectWbsCostSummaryVo.getDemandCost().negate());//剩余可用预算
                } else {
                    //需求预算
                    BigDecimal eaDemandCost = toFilterNull(ea.isEmpty() ? BigDecimal.ZERO : ea.get(getEaKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal hrDemandCost = toFilterNull(hrRequirementBudget.isEmpty() ? BigDecimal.ZERO : hrRequirementBudget.get(getRequirementBudgetKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal demandCost = eaDemandCost.add(hrDemandCost);
                    //在途
                    BigDecimal poOnTheWayAmount = toFilterNull(poOnTheWay.isEmpty() ? BigDecimal.ZERO : poOnTheWay.get(getPoOnTheWayKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal allocationExecuteAmount = toFilterNull(allocationExecute.isEmpty() ? BigDecimal.ZERO : allocationExecute.get(getAllocationExecuteAmountKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal wkOnTheWayAmount = toFilterNull(wkOnTheWay.isEmpty() ? BigDecimal.ZERO : wkOnTheWay.get(getWkOnTheWayKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal eaOnTheWayAmount = toFilterNull(eaOnTheWay.isEmpty() ? BigDecimal.ZERO : eaOnTheWay.get(getEaOnTheWayKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal hroPurchaseContractOnTheWayAmount = toFilterNull(hroPurchaseContractOnTheWay.isEmpty() ? BigDecimal.ZERO : hroPurchaseContractOnTheWay.get(getHroPurchaseContractOnTheWay(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal vendorPenaltyAmountOnTheWay = toFilterNull(vendorPenaltyProjectCostOnTheWay.get(getHroPurchaseContractCompleteKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal onTheWayCost = poOnTheWayAmount.add(allocationExecuteAmount).add(wkOnTheWayAmount).add(eaOnTheWayAmount).add(hroPurchaseContractOnTheWayAmount).add(vendorPenaltyAmountOnTheWay);

                    //已发生
                    BigDecimal getMaterialAmount = toFilterNull(getMaterial.isEmpty() ? BigDecimal.ZERO : getMaterial.get(getGetMaterialKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal returnMaterialAmount = toFilterNull(returnMaterial.isEmpty() ? BigDecimal.ZERO : returnMaterial.get(getReturnMaterialKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal budgetExecuteAmount = toFilterNull(budgetExecute.isEmpty() ? BigDecimal.ZERO : budgetExecute.get(getBudgetExecuteAmountTotalVOKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal wkCompleteAmount = toFilterNull(wkComplete.isEmpty() ? BigDecimal.ZERO : wkComplete.get(getWkCompleteKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal eaCompleteAmount = toFilterNull(eaComplete.isEmpty() ? BigDecimal.ZERO : eaComplete.get(getEaCompleteKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal hroPurchaseContractCompleteAmount = toFilterNull(hroPurchaseContractComplete.isEmpty() ? BigDecimal.ZERO : hroPurchaseContractComplete.get(getHroPurchaseContractCompleteKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal vendorPenaltyAmount = toFilterNull(vendorPenaltyProjectCost.get(getHroPurchaseContractCompleteKey(projectWbsCostSummaryVo.getProjectId(), projectWbsCostSummaryVo.getWbsSummaryCode(), projectWbsCostSummaryVo.getActivityCode())));
                    BigDecimal incurredCost = getMaterialAmount.add(returnMaterialAmount.negate()).add(budgetExecuteAmount).add(wkCompleteAmount).add(eaCompleteAmount).add(hroPurchaseContractCompleteAmount).add(vendorPenaltyAmount);

                    projectWbsCostSummaryVo.setDemandCost(demandCost);//需求预算
                    projectWbsCostSummaryVo.setOnTheWayCost(onTheWayCost);//在途成本
                    projectWbsCostSummaryVo.setIncurredCost(incurredCost);//已发生成本

                    // 剩余可用预算 = 预算金额 - 需求预算 - 在途成本 - 已发生成本
                    BigDecimal remainingCost = projectWbsCostSummaryVo.getPrice().subtract(demandCost).subtract(onTheWayCost).subtract(incurredCost);
                    projectWbsCostSummaryVo.setRemainingCost(remainingCost);
                    // 累计变更金额 = 预算金额 - 预算基线
                    BigDecimal changeAccumulateCost = projectWbsCostSummaryVo.getPrice().subtract(projectWbsCostSummaryVo.getBaselineCost());
                    projectWbsCostSummaryVo.setChangeAccumulateCost(changeAccumulateCost);
                }
            }

        }
    }

    /**
     * @param projectIds
     * @description 原数据逻辑删除
     */
    private void deleteBatchAll(List<Long> projectIds) {
        projectWorkingHourDetailExtMapper.updateBatch(projectIds);
        projectPurchaseContractDetailExtMapper.updateBatch(projectIds);
        projectMaterialDetailExtMapper.updateBatch(projectIds);
        requirementPoDetailExtMapper.updateBatch(projectIds);
        requirementPurchaseContractDetailExtMapper.updateBatch(projectIds);
        requirementBudgetExtMapper.updateBatch(projectIds);
        projectEaDetailExtMapper.updateBatch(projectIds);
    }

    /**
     * @param batchVo
     * @description 数据落表
     */
    private void insertBatchAll(ProjectWbsCostBatchVo batchVo) {
        List<ProjectMaterialDetail> projectMaterialDetails = batchVo.getProjectMaterialDetails();
        List<ProjectPurchaseContractDetail> projectPurchaseContractDetails = batchVo.getProjectPurchaseContractDetails();
        List<ProjectWorkingHourDetail> projectWorkingHourDetails = batchVo.getProjectWorkingHourDetails();
        List<ProjectEaDetail> projectEaDetails = batchVo.getProjectEaDetails();
        List<RequirementPoDetail> requirementPoDetails = batchVo.getRequirementPoDetails();
        List<RequirementPurchaseContractDetail> requirementPurchaseContractDetails = batchVo.getRequirementPurchaseContractDetails();
        List<RequirementBudget> requirementBudgets = batchVo.getRequirementBudgets();
        List<HroRequirementBudget> hroRequirementBudgets = batchVo.getHroRequirementBudgets();
        List<HroRequirementContractDetail> hroRequirementContractDetails = batchVo.getHroRequirementContractDetails();
        List<HroPurchaseContractTotal> hroPurchaseContractTotals = batchVo.getHroPurchaseContractTotals();
        List<VendorPenaltyProjectCostSummary> vendorPenaltyProjectCostSummaries = batchVo.getVendorPenaltyProjectCostSummaries();

        //需求预算
        if (ListUtils.isNotEmpty(requirementBudgets)) {
            List<List<RequirementBudget>> lists = ListUtils.splistList(requirementBudgets, 1000);
            for (List<RequirementBudget> list : lists) {
                requirementBudgetExtMapper.insertBatch(list);
            }
        }
        //po
        if (ListUtils.isNotEmpty(requirementPoDetails)) {
            List<List<RequirementPoDetail>> lists = ListUtils.splistList(requirementPoDetails, 1000);
            for (List<RequirementPoDetail> list : lists) {
                requirementPoDetailExtMapper.insertBatch(list);
            }
        }
        //采购合同
        if (ListUtils.isNotEmpty(projectPurchaseContractDetails)) {
            List<List<ProjectPurchaseContractDetail>> lists = ListUtils.splistList(projectPurchaseContractDetails, 1000);
            for (List<ProjectPurchaseContractDetail> list : lists) {
                projectPurchaseContractDetailExtMapper.insertBatch(list);
            }
        }
        //领退料
        if (ListUtils.isNotEmpty(projectMaterialDetails)) {
            List<List<ProjectMaterialDetail>> lists = ListUtils.splistList(projectMaterialDetails, 1000);
            for (List<ProjectMaterialDetail> list : lists) {
                projectMaterialDetailExtMapper.insertBatch(list);
            }
        }
        //ea
        if (ListUtils.isNotEmpty(projectEaDetails)) {
            List<List<ProjectEaDetail>> lists = ListUtils.splistList(projectEaDetails, 1000);
            for (List<ProjectEaDetail> list : lists) {
                projectEaDetailExtMapper.insertBatch(list);
            }
        }
        //工时
        if (ListUtils.isNotEmpty(projectWorkingHourDetails)) {
            List<List<ProjectWorkingHourDetail>> lists = ListUtils.splistList(projectWorkingHourDetails, 1000);
            for (List<ProjectWorkingHourDetail> list : lists) {
                projectWorkingHourDetailExtMapper.insertBatch(list);
            }
        }
        //需求预算已下采购合同
        if (ListUtils.isNotEmpty(requirementPurchaseContractDetails)) {
            List<List<RequirementPurchaseContractDetail>> lists = ListUtils.splistList(requirementPurchaseContractDetails, 1000);
            for (List<RequirementPurchaseContractDetail> list : lists) {
                requirementPurchaseContractDetailExtMapper.insertBatch(list);
            }
        }
        //人力点工需求预算
        if (ListUtils.isNotEmpty(hroRequirementBudgets)) {
            List<List<HroRequirementBudget>> lists = ListUtils.splistList(hroRequirementBudgets, 1000);
            for (List<HroRequirementBudget> list : lists) {
                hroRequirementBudgetExtMapper.insertBatch(list);
            }
        }
        //项目成本人力点工采购合同对账统计
        if (ListUtils.isNotEmpty(hroRequirementContractDetails)) {
            List<List<HroRequirementContractDetail>> lists = ListUtils.splistList(hroRequirementContractDetails, 1000);
            for (List<HroRequirementContractDetail> list : lists) {
                hroRequirementContractDetailExtMapper.insertBatch(list);
            }
        }
        //项目成本人力点工采购合同对账统计
        if (ListUtils.isNotEmpty(hroPurchaseContractTotals)) {
            List<List<HroPurchaseContractTotal>> lists = ListUtils.splistList(hroPurchaseContractTotals, 1000);
            for (List<HroPurchaseContractTotal> list : lists) {
                hroPurchaseContractTotalExtMapper.insertBatch(list);
            }
        }
        // 供应商罚扣项目成本统计
        if (ListUtils.isNotEmpty(vendorPenaltyProjectCostSummaries)) {
            List<List<VendorPenaltyProjectCostSummary>> lists = ListUtils.splistList(vendorPenaltyProjectCostSummaries, 1000);
            for (List<VendorPenaltyProjectCostSummary> list : lists) {
                vendorPenaltyProjectCostSummaryExtMapper.batchInsert(list);
            }
        }
    }

    @Override
    public ProjectWbsCostExcelVo exportProjectWbsCostSummaryByProjectId(Map<String, Object> param) {
        // 先查询汇总信息
        ProjectWbsCostExcelVo excelVo = this.summary(param);
        //查询其他导出sheet
        Long projectId = MapUtils.getLong(param, ProjectWbsCostConstant.PROJECT_ID);
        Long executeId = excelVo.getExecuteId();
        if (Objects.isNull(executeId)) {
            return excelVo;
        }
        List<Map<String, Object>> projectWbsCostSummaries = excelVo.getMapList();
        //收集过滤条件
        final List<String> filterList = projectWbsCostSummaries.stream()
                .map(entity -> getExportKey(
                        MapUtils.getLong(entity, ProjectWbsCostConstant.PROJECT_ID),
                        MapUtils.getString(entity, ProjectWbsCostConstant.PROJECT_CODE) + "-" + MapUtils.getString(entity, ProjectWbsCostConstant.WBS_FULL_CODE),
                        MapUtils.getString(entity, ProjectWbsCostConstant.ACTIVITY_CODE)
                )).collect(Collectors.toList());

        //需求预算
        RequirementBudgetExample example = new RequirementBudgetExample();
        RequirementBudgetExample.Criteria criteria = example.createCriteria();
        criteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<RequirementBudget> requirementBudgets = requirementBudgetMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(requirementBudgets)) {
            List<RequirementBudget> purchaseList = requirementBudgets.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 0).collect(Collectors.toList());
            List<RequirementBudget> outsourceList = requirementBudgets.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 1).collect(Collectors.toList());
            List<RequirementBudget> approvalList = requirementBudgets.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 2).collect(Collectors.toList());
            excelVo.setPurchaseList(purchaseList);
            excelVo.setOutsourceList(outsourceList);
            excelVo.setApprovalList(approvalList);
        }
        //po
        RequirementPoDetailExample poExample = new RequirementPoDetailExample();
        RequirementPoDetailExample.Criteria poCriteria = poExample.createCriteria();
        poCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<RequirementPoDetail> requirementPoDetails = requirementPoDetailMapper.selectByExample(poExample);
        if (ListUtils.isNotEmpty(requirementPoDetails)) {
            List<RequirementPoDetail> poOnTheWay = requirementPoDetails.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getPoType() == 1).collect(Collectors.toList());
            excelVo.setPoOnTheWay(poOnTheWay);
        }
        //采购合同
        ProjectPurchaseContractDetailExample contractExample = new ProjectPurchaseContractDetailExample();
        ProjectPurchaseContractDetailExample.Criteria contractCriteria = contractExample.createCriteria();
        contractCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectPurchaseContractDetail> projectPurchaseContractDetails = projectPurchaseContractDetailMapper.selectByExample(contractExample);
        if (ListUtils.isNotEmpty(projectPurchaseContractDetails)) {
            List<ProjectPurchaseContractDetail> contractOnTheWay = projectPurchaseContractDetails.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 1).collect(Collectors.toList());
            List<ProjectPurchaseContractDetail> contractComplete = projectPurchaseContractDetails.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 2).collect(Collectors.toList());
            excelVo.setContractOnTheWay(contractOnTheWay);
            excelVo.setContractComplete(contractComplete);
        }

        //物料
        ProjectMaterialDetailExample materialExample = new ProjectMaterialDetailExample();
        ProjectMaterialDetailExample.Criteria materialCriteria = materialExample.createCriteria();
        materialCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectMaterialDetail> projectMaterialDetails = projectMaterialDetailMapper.selectByExample(materialExample);
        if (ListUtils.isNotEmpty(projectMaterialDetails)) {
            List<ProjectMaterialDetail> getMaterial = projectMaterialDetails.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode()))).collect(Collectors.toList());
            getMaterial.stream().forEach(entity -> {
                if (entity.getBudgetType() == 2) entity.setTotalAmount(entity.getTotalAmount().negate());
            });
            excelVo.setGetMaterial(getMaterial);
        }
        //EA,EC
        ProjectEaDetailExample eaExample = new ProjectEaDetailExample();
        ProjectEaDetailExample.Criteria eaCriteria = eaExample.createCriteria();
        eaCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectEaDetail> projectEaDetails = projectEaDetailMapper.selectByExample(eaExample);
        if (ListUtils.isNotEmpty(projectEaDetails)) {
            List<ProjectEaDetail> ea = projectEaDetails.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 0).collect(Collectors.toList());
            List<ProjectEaDetail> ecOnTheWay = projectEaDetails.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 1).collect(Collectors.toList());
            List<ProjectEaDetail> ecComplete = projectEaDetails.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 2).collect(Collectors.toList());
            excelVo.setEa(ea);
            excelVo.setEcOnTheWay(ecOnTheWay);
            excelVo.setEcComplete(ecComplete);
        }

        //工时
        ProjectWorkingHourDetailExample wkExample = new ProjectWorkingHourDetailExample();
        ProjectWorkingHourDetailExample.Criteria wkCriteria = wkExample.createCriteria();
        wkCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWorkingHourDetail> projectWorkingHourDetails = projectWorkingHourDetailMapper.selectByExample(wkExample);
        if (ListUtils.isNotEmpty(projectWorkingHourDetails)) {
            List<ProjectWorkingHourDetail> wkOnTheWay = projectWorkingHourDetails.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 1).collect(Collectors.toList());
            List<ProjectWorkingHourDetail> wkComplete = projectWorkingHourDetails.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 2).collect(Collectors.toList());
            excelVo.setWkOnTheWay(wkOnTheWay);
            excelVo.setWkComplete(wkComplete);
        }

        //人力点工
        HroRequirementBudgetExample hroExample = new HroRequirementBudgetExample();
        HroRequirementBudgetExample.Criteria hroCriteria = hroExample.createCriteria();
        hroCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<HroRequirementBudget> hroRequirementBudgetList = hroRequirementBudgetMapper.selectByExample(hroExample);
        if (ListUtils.isNotEmpty(hroRequirementBudgetList)) {
            List<HroRequirementBudget> hroRequirementBudgets = hroRequirementBudgetList.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getRowRecordType() == 0).collect(Collectors.toList());
            List<HroRequirementBudget> hroRequirementBudgetApprovals = hroRequirementBudgetList.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getRowRecordType() == 1).collect(Collectors.toList());
            excelVo.setHroRequirementBudgets(hroRequirementBudgets);
            excelVo.setHroRequirementBudgetApprovals(hroRequirementBudgetApprovals);
        }

        //人力点工-采购合同-对账
        HroPurchaseContractTotalExample hroPurchaseContractExample = new HroPurchaseContractTotalExample();
        HroPurchaseContractTotalExample.Criteria hroPurchaseContractCriteria = hroPurchaseContractExample.createCriteria();
        hroPurchaseContractCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<HroPurchaseContractTotal> hroPurchaseContractList = hroPurchaseContractTotalMapper.selectByExample(hroPurchaseContractExample);
        if (ListUtils.isNotEmpty(hroPurchaseContractList)) {
            List<HroPurchaseContractTotal> hroPurchaseContractOnTheWay = hroPurchaseContractList.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 1).collect(Collectors.toList());
            List<HroPurchaseContractTotal> hroPurchaseContractComplete = hroPurchaseContractList.stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsSummaryCode(), entity.getActivityCode())) && entity.getBudgetType() == 2).collect(Collectors.toList());
            excelVo.setHroPurchaseContractOnTheWay(hroPurchaseContractOnTheWay);
            excelVo.setHroPurchaseContractComplete(hroPurchaseContractComplete);
        }

        //开票回款信息
        List<ContractDTO> invoiceReceiptInfoList = projectWbsCostSummaryExtMapper.findContractRs(projectId);
        if (CollectionUtils.isNotEmpty(invoiceReceiptInfoList)) {
            for (ContractDTO contractDTO : invoiceReceiptInfoList) {
                BigDecimal amount = contractDTO.getAmount();
                BigDecimal billed = projectWbsCostSummaryExtMapper.summaryInvoicePlanAmount(contractDTO.getId());       //已开票
                BigDecimal moneyBacked = projectWbsCostSummaryExtMapper.summaryReceiptPlanAmount(contractDTO.getId());  //已回款

                contractDTO.setBilled(billed);
                contractDTO.setMoneyBacked(moneyBacked);
                if (amount != null) {
                    contractDTO.setSurplusBill(amount.subtract(billed));
                    contractDTO.setSurplusMoneyBack(amount.subtract(moneyBacked));
                    contractDTO.setBillRate(billed.divide(amount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
                    contractDTO.setMoneyBackRate(moneyBacked.divide(amount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
                }
            }
        }
        excelVo.setInvoiceReceiptInfoList(invoiceReceiptInfoList);

        // 供应商罚扣
        VendorPenaltyProjectCostSummaryExample penaltySummaryExample = new VendorPenaltyProjectCostSummaryExample();
        penaltySummaryExample.createCriteria().andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorPenaltyProjectCostSummary> vendorPenaltyProjectCostSummaries = vendorPenaltyProjectCostSummaryMapper.selectByExample(penaltySummaryExample)
                .stream().filter(entity -> filterList.contains(getExportKey(entity.getProjectId(), entity.getWbsCode(), entity.getActivityCode()))).collect(Collectors.toList());
        excelVo.setVendorPenaltyProjectCostSummaries(vendorPenaltyProjectCostSummaries);

        return excelVo;
    }

    @Override
    public ProjectWbsCostExcelVo summary(Map<String, Object> param) {
        Long projectId = MapUtils.getLong(param, ProjectWbsCostConstant.PROJECT_ID);
        if (Objects.isNull(projectId)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", ProjectWbsCostConstant.PROJECT_ID));
        }
        //查询项目成本定时任务执行明细
        ProjectWbsExecuteDetail projectWbsExecuteDetail = getWbsExecuteDetail(projectId);
        Long executeId = null;
        Date executeTime = null;
        if (Objects.nonNull(projectWbsExecuteDetail)) {
            executeId = projectWbsExecuteDetail.getExecuteId();
            executeTime = projectWbsExecuteDetail.getExecuteTime();
        }
        //查询已确认汇总金额
        ProjectWbsCostExcelVo project = projectWbsCostSummaryExtMapper.getConfirmedAmount(projectId, executeId);
        if (Objects.isNull(project)) {
            throw new ApplicationBizException("项目不存在");
        }
        if (!Boolean.TRUE.equals(project.getWbsEnabled()) || Objects.isNull(project.getWbsTemplateInfoId())) {
            throw new ApplicationBizException("项目类型对应wbs模板未启用或不存在");
        }
        project.setExecuteId(executeId);
        project.setExecuteTime(executeTime);
        param.put(ProjectWbsCostConstant.WBS_TEMPLATE_INFO_ID, project.getWbsTemplateInfoId());
        List<ProjectWbsBudgetDto> details = selectDetailByParam(param);
        if (CollectionUtils.isNotEmpty(details)) {
            project.setMapList(ProjectWbsBudgetDto.dto2MapBatch(details));
        }
        project.setUpdating(Boolean.FALSE);
        ProjectWbsExecuteDetail latestExecute = projectWbsExecuteDetailExtMapper.getTheLatestUpdate(projectId);
        if (Objects.nonNull(latestExecute) && !Objects.equals(executeId, latestExecute.getExecuteId())) {
            if (Objects.equals(-1, latestExecute.getExecuteResult())) {
                project.setUpdating(Boolean.TRUE);
            } else {
                //非最新记录且不处于更新中，即更新失败
                project.setUpdating(Boolean.FALSE);
                project.setFailMsg(String.format("执行Id：%s更新失败，请稍后重试！", latestExecute.getExecuteId()));
            }
        }
        return project;
    }

    @Override
    public List<Long> queryAllProjectForJob() {
        /**
         * 1、先读取配置获取需更新的项目状态
         * 2、补充场景：定时执行间隔时间存在：项目其他状态 -> 项目结项
         */
        // 读取配置   【项目状态: 项目进行中、项目变更中、预立项转正审批中、预立项转正驳回、终止】
        List<Integer> statuses = getCostCalculateStatus();
        List<Long> projectIds = projectWbsCostSummaryExtMapper.getNeedCalculateRange(null, statuses);
        //取结项的数据
        List<Integer> closeStatus = Stream.of(ProjectStatus.CLOSE.getCode()).collect(Collectors.toList());
        List<Long> closeProjectIds = projectWbsCostSummaryExtMapper.getNeedCalculateRange(null, closeStatus);
        //明细表中已执行的结项项目
        List<Long> nextNotExecuteList = projectWbsExecuteDetailExtMapper.getNextProjectExecute();
        //获取差集 所有结项项目-已执行的结项项目
        Collection<Long> subtractList = CollectionUtils.subtract(closeProjectIds, nextNotExecuteList);
        //获取并集  场景1 + 场景2
        Collection<Long> unionList = CollectionUtils.union(projectIds, subtractList);
        if (CollectionUtils.isNotEmpty(unionList)) {
            return new ArrayList<>(unionList);
        }
        logger.info("项目成本计算待处理数据为空，不做处理");
        return Lists.newArrayList();
    }

    @Override
    public List<Long> queryEndProjectForJob() {
        //取结项的数据
        List<Integer> closeStatus = Stream.of(ProjectStatus.CLOSE.getCode()).collect(Collectors.toList());
        List<Long> closeProjectIds = projectWbsCostSummaryExtMapper.getNeedCalculateRange(null, closeStatus);
        if (CollectionUtils.isNotEmpty(closeProjectIds)) {
            return closeProjectIds;
        }
        logger.info("已结项项目成本计算待处理数据为空，不做处理");
        return Lists.newArrayList();
    }

    @Override
    public List<Long> queryAllDelProject() {
        // 查询所有待更新的数据
        List<Long> projectIds = getAllDelProject();
        if (ListUtils.isNotEmpty(projectIds)) {
            return projectIds;
        }
        logger.info("项目成本计算待处理数据为空，不做处理");
        return Lists.newArrayList();
    }

    @Override
    public ProjectWbsCostSystemVO system(ProjectWbsSummaryDto projectWbsSummaryDto) {
        //查询项目成本的执行ID
        ProjectWbsExecuteDetail projectWbsExecuteDetail = getWbsExecuteDetail(projectWbsSummaryDto.getProjectId());
        Long executeId = projectWbsExecuteDetail.getExecuteId();
        ProjectWbsCostSystemVO projectWbsCostSystemVO = new ProjectWbsCostSystemVO();
        //需求预算
        RequirementBudgetExample example = new RequirementBudgetExample();
        RequirementBudgetExample.Criteria criteria = example.createCriteria();
        criteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectWbsSummaryDto.getProjectId()).andWbsSummaryCodeEqualTo(projectWbsSummaryDto.getWbsSummaryCode()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<RequirementBudget> requirementBudgets = requirementBudgetMapper.selectByExample(example);
        List<RequirementBudget> purchaseList = requirementBudgets.stream().filter(entity -> entity.getBudgetType() == 0).collect(Collectors.toList());
        List<RequirementBudget> outsourceList = requirementBudgets.stream().filter(entity -> entity.getBudgetType() == 1).collect(Collectors.toList());
        List<RequirementBudget> approvalList = requirementBudgets.stream().filter(entity -> entity.getBudgetType() == 2).collect(Collectors.toList());
        BigDecimal demandCost = requirementBudgets.stream().map(e -> e.getRemainingCostAmount()).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        projectWbsCostSystemVO.setProjectId(projectWbsSummaryDto.getProjectId());
        projectWbsCostSystemVO.setWbsSummaryCode(projectWbsSummaryDto.getWbsSummaryCode());
        projectWbsCostSystemVO.setActivityCode(projectWbsSummaryDto.getActivityCode());
        projectWbsCostSystemVO.setDemandCost(demandCost);
        projectWbsCostSystemVO.setPurchaseMaterialRequirement(purchaseList);
        projectWbsCostSystemVO.setOutsourcingMaterialRequirement(outsourceList);
        projectWbsCostSystemVO.setProjectRequirementPublish(approvalList);
        return projectWbsCostSystemVO;
    }

    private ProjectWbsExecuteDetail getWbsExecuteDetail(Long projectId) {
        return projectWbsExecuteDetailService.getProjectWbsExecuteDetailByProjectId(projectId);
    }

    @Override
    public ProjectWbsCostCompleteVO complete(ProjectWbsSummaryDto projectWbsSummaryDto) {
        //查询项目成本的执行ID
        ProjectWbsExecuteDetail projectWbsExecuteDetail = getWbsExecuteDetail(projectWbsSummaryDto.getProjectId());
        Long executeId = projectWbsExecuteDetail.getExecuteId();
        Long projectId = projectWbsSummaryDto.getProjectId();
        String wbsSummaryCode = projectWbsSummaryDto.getWbsSummaryCode();
        String activityCode = projectWbsSummaryDto.getActivityCode();
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(wbsSummaryCode, ErrorCode.CTC_WBSSUMMARY_CODE_NULL);
        Asserts.notEmpty(activityCode, ErrorCode.CTC_ACTIVITY_CODE_NULL);
        ProjectWbsCostCompleteVO projectWbsCostCompleteVO = new ProjectWbsCostCompleteVO();
        //物料
        ProjectMaterialDetailExample materialExample = new ProjectMaterialDetailExample();
        ProjectMaterialDetailExample.Criteria materialCriteria = materialExample.createCriteria();
        materialCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectMaterialDetail> projectMaterialDetails = projectMaterialDetailMapper.selectByExample(materialExample);
        List<ProjectMaterialDetail> materialGetComplete = projectMaterialDetails.stream().filter(e -> e.getBudgetType() == 1).collect(Collectors.toList());
        List<ProjectMaterialDetail> materialReturnComplete = projectMaterialDetails.stream().filter(e -> e.getBudgetType() == 2).collect(Collectors.toList());
        BigDecimal total = materialGetComplete.stream().map(e -> Optional.ofNullable(e.getTotalAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal total1 = materialReturnComplete.stream().map(e -> Optional.ofNullable(e.getTotalAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //采购合同
        ProjectPurchaseContractDetailExample contractExample = new ProjectPurchaseContractDetailExample();
        ProjectPurchaseContractDetailExample.Criteria contractCriteria = contractExample.createCriteria();
        contractCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andBudgetTypeEqualTo(2).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectPurchaseContractDetail> projectPurchaseContractDetails = projectPurchaseContractDetailMapper.selectByExample(contractExample);
        BigDecimal total2 = projectPurchaseContractDetails.stream().map(e -> Optional.ofNullable(e.getBudgetExecuteAmountTotal()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //工时
        ProjectWorkingHourDetailExample wkExample = new ProjectWorkingHourDetailExample();
        ProjectWorkingHourDetailExample.Criteria wkCriteria = wkExample.createCriteria();
        wkCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andBudgetTypeEqualTo(2).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWorkingHourDetail> projectWorkingHourDetails = projectWorkingHourDetailMapper.selectByExample(wkExample);
        BigDecimal total3 = projectWorkingHourDetails.stream().map(e -> Optional.ofNullable(e.getTotalAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //EC
        ProjectEaDetailExample eaExample = new ProjectEaDetailExample();
        ProjectEaDetailExample.Criteria eaCriteria = eaExample.createCriteria();
        eaCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andBudgetTypeEqualTo(2).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectEaDetail> projectEaDetails = projectEaDetailMapper.selectByExample(eaExample);
        BigDecimal total4 = projectEaDetails.stream().map(e -> Optional.ofNullable(e.getAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);

        //项目成本人力点工采购合同对账统计
        HroPurchaseContractTotalExample hroPurchaseContractTotalExample = new HroPurchaseContractTotalExample();
        HroPurchaseContractTotalExample.Criteria hroPurchaseContractTotalExampleCriteria = hroPurchaseContractTotalExample.createCriteria();
        hroPurchaseContractTotalExampleCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andBudgetTypeEqualTo(2).andDeletedFlagEqualTo(Boolean.FALSE);
        List<HroPurchaseContractTotal> hroPurchaseContractTotals = hroPurchaseContractTotalMapper.selectByExample(hroPurchaseContractTotalExample);
        BigDecimal total5 = hroPurchaseContractTotals.stream().map(e -> Optional.ofNullable(e.getSurplusAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);

        VendorPenaltyProjectCostSummaryExample vendorPenaltyProjectCostSummaryExample = new VendorPenaltyProjectCostSummaryExample();
        vendorPenaltyProjectCostSummaryExample.createCriteria().andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorPenaltyProjectCostSummary> vendorPenaltyProjectCostSummaries = vendorPenaltyProjectCostSummaryMapper.selectByExample(vendorPenaltyProjectCostSummaryExample);
        BigDecimal total6 = vendorPenaltyProjectCostSummaries.stream().map(VendorPenaltyProjectCostSummary::getOccurCost).reduce(BigDecimal.ZERO, BigDecimalUtils::add);

        BigDecimal bigDecimal = total.add(total1.negate()).add(total2).add(total3).add(total4).add(total5).add(total6);
        projectWbsCostCompleteVO.setProjectId(projectId);
        projectWbsCostCompleteVO.setWbsSummaryCode(wbsSummaryCode);
        projectWbsCostCompleteVO.setActivityCode(activityCode);
        projectWbsCostCompleteVO.setTotalAmount(bigDecimal);
        projectWbsCostCompleteVO.setMaterialGetComplete(materialGetComplete);
        projectWbsCostCompleteVO.setMaterialReturnComplete(materialReturnComplete);
        projectWbsCostCompleteVO.setPurchaseComplete(projectPurchaseContractDetails);
        projectWbsCostCompleteVO.setWorkingHoursComplete(projectWorkingHourDetails);
        projectWbsCostCompleteVO.setEcComplete(projectEaDetails);
        projectWbsCostCompleteVO.setHroPurchaseContractComplete(hroPurchaseContractTotals);
        projectWbsCostCompleteVO.setVendorPenaltyProjectCostSummaryList(vendorPenaltyProjectCostSummaries);
        return projectWbsCostCompleteVO;
    }

    @Override
    public ProjectWbsCostOnTheWayVO onTheWay(ProjectWbsSummaryDto projectWbsSummaryDto) {
        //查询项目成本的执行ID
        ProjectWbsCostOnTheWayVO projectWbsCostOnTheWayVO = new ProjectWbsCostOnTheWayVO();
        ProjectWbsExecuteDetail projectWbsExecuteDetail = getWbsExecuteDetail(projectWbsSummaryDto.getProjectId());
        if (projectWbsExecuteDetail == null) {
            return projectWbsCostOnTheWayVO;
        }
        Long executeId = projectWbsExecuteDetail.getExecuteId();
        Long projectId = projectWbsSummaryDto.getProjectId();
        String wbsSummaryCode = projectWbsSummaryDto.getWbsSummaryCode();
        String activityCode = projectWbsSummaryDto.getActivityCode();
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(wbsSummaryCode, ErrorCode.CTC_WBSSUMMARY_CODE_NULL);
        Asserts.notEmpty(activityCode, ErrorCode.CTC_ACTIVITY_CODE_NULL);
        //PO在途
        RequirementPoDetailExample poExample = new RequirementPoDetailExample();
        RequirementPoDetailExample.Criteria poCriteria = poExample.createCriteria();
        poCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andPoTypeEqualTo(1).andDeletedFlagEqualTo(Boolean.FALSE);
        List<RequirementPoDetail> requirementPoDetails = requirementPoDetailMapper.selectByExample(poExample);
        BigDecimal total1 = requirementPoDetails.stream().map(e -> Optional.ofNullable(e.getPoTotalAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //采购合同
        ProjectPurchaseContractDetailExample contractExample = new ProjectPurchaseContractDetailExample();
        ProjectPurchaseContractDetailExample.Criteria contractCriteria = contractExample.createCriteria();
        contractCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andBudgetTypeEqualTo(1).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectPurchaseContractDetail> projectPurchaseContractDetails = projectPurchaseContractDetailMapper.selectByExample(contractExample);
        BigDecimal total2 = projectPurchaseContractDetails.stream().map(e -> Optional.ofNullable(e.getAllocationExecuteAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //工时
        ProjectWorkingHourDetailExample wkExample = new ProjectWorkingHourDetailExample();
        ProjectWorkingHourDetailExample.Criteria wkCriteria = wkExample.createCriteria();
        wkCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andBudgetTypeEqualTo(1).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWorkingHourDetail> projectWorkingHourDetails = projectWorkingHourDetailMapper.selectByExample(wkExample);
        BigDecimal total3 = projectWorkingHourDetails.stream().map(e -> Optional.ofNullable(e.getTotalAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //EC
        ProjectEaDetailExample eaExample = new ProjectEaDetailExample();
        ProjectEaDetailExample.Criteria eaCriteria = eaExample.createCriteria();
        eaCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andBudgetTypeEqualTo(1).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectEaDetail> projectEaDetails = projectEaDetailMapper.selectByExample(eaExample);
        BigDecimal total4 = projectEaDetails.stream().map(e -> Optional.ofNullable(e.getAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);

        //项目成本人力点工采购合同对账统计
        HroPurchaseContractTotalExample hroPurchaseContractTotalExample = new HroPurchaseContractTotalExample();
        HroPurchaseContractTotalExample.Criteria hroPurchaseContractTotalExampleCriteria = hroPurchaseContractTotalExample.createCriteria();
        hroPurchaseContractTotalExampleCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andBudgetTypeEqualTo(1).andDeletedFlagEqualTo(Boolean.FALSE);
        List<HroPurchaseContractTotal> hroPurchaseContractTotals = hroPurchaseContractTotalMapper.selectByExample(hroPurchaseContractTotalExample);
        BigDecimal total5 = hroPurchaseContractTotals.stream().map(e -> Optional.ofNullable(e.getSurplusAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 供应商罚扣
        VendorPenaltyProjectCostSummaryExample vendorPenaltyProjectCostSummaryExample = new VendorPenaltyProjectCostSummaryExample();
        vendorPenaltyProjectCostSummaryExample.createCriteria().andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorPenaltyProjectCostSummary> vendorPenaltyProjectCostSummaries = vendorPenaltyProjectCostSummaryMapper.selectByExample(vendorPenaltyProjectCostSummaryExample);
        BigDecimal total6 = vendorPenaltyProjectCostSummaries.stream().map(e -> e.getProjectCost().subtract(e.getOccurCost())).reduce(BigDecimal.ZERO, BigDecimalUtils::add);

        BigDecimal bigDecimal = total1.add(total2).add(total3).add(total4).add(total5).add(total6);

        projectWbsCostOnTheWayVO.setProjectId(projectId);
        projectWbsCostOnTheWayVO.setWbsSummaryCode(wbsSummaryCode);
        projectWbsCostOnTheWayVO.setActivityCode(activityCode);
        projectWbsCostOnTheWayVO.setTotalAmount(bigDecimal);

        projectWbsCostOnTheWayVO.setPoOnTheWay(requirementPoDetails);
        projectWbsCostOnTheWayVO.setPurchaseOnTheWay(projectPurchaseContractDetails);
        projectWbsCostOnTheWayVO.setWorkingHoursOnTheWay(projectWorkingHourDetails);
        projectWbsCostOnTheWayVO.setEcOnTheWay(projectEaDetails);
        projectWbsCostOnTheWayVO.setHroPurchaseContractOnTheWay(hroPurchaseContractTotals);
        projectWbsCostOnTheWayVO.setVendorPenaltyProjectCostOnTheWay(vendorPenaltyProjectCostSummaries);

        return projectWbsCostOnTheWayVO;
    }

    @Override
    public ProjectWbsCostSystemVO emsBudgetOccupyDetail(ProjectWbsSummaryDto projectWbsSummaryDto) {
        Long projectId = projectWbsSummaryDto.getProjectId();
        String wbsSummaryCode = projectWbsSummaryDto.getWbsSummaryCode();
        String activityCode = projectWbsSummaryDto.getActivityCode();
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(wbsSummaryCode, ErrorCode.CTC_WBSSUMMARY_CODE_NULL);
        Asserts.notEmpty(activityCode, ErrorCode.CTC_ACTIVITY_CODE_NULL);
        //查询项目成本的执行ID
        ProjectWbsExecuteDetail projectWbsExecuteDetail = getWbsExecuteDetail(projectWbsSummaryDto.getProjectId());
        Long executeId = projectWbsExecuteDetail.getExecuteId();
        ProjectWbsCostSystemVO projectWbsCostSystemVO = new ProjectWbsCostSystemVO();
        //人力点工需求预算
        HroRequirementBudgetExample example = new HroRequirementBudgetExample();
        example.createCriteria().andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andDeletedFlagEqualTo(Boolean.FALSE);
        List<HroRequirementBudget> hroRequirementBudgetList = hroRequirementBudgetMapper.selectByExample(example);
        //需求预算
        List<HroRequirementBudget> hroRequirementBudgets = hroRequirementBudgetList.stream().filter(entity -> entity.getRowRecordType() == 0).collect(Collectors.toList());
        List<HroRequirementBudget> hroRequirementBudgetApprovals = hroRequirementBudgetList.stream().filter(entity -> entity.getRowRecordType() == 1).collect(Collectors.toList());
        BigDecimal total1 = hroRequirementBudgetList.stream().map(e -> Optional.ofNullable(e.getRemainingCostAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        //EA
        ProjectEaDetailExample eaExample = new ProjectEaDetailExample();
        ProjectEaDetailExample.Criteria eaCriteria = eaExample.createCriteria();
        eaCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andBudgetTypeEqualTo(0).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectEaDetail> projectEaDetails = projectEaDetailMapper.selectByExample(eaExample);
        BigDecimal total2 = projectEaDetails.stream().map(e -> Optional.ofNullable(e.getAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal demandCost = total1.add(total2);
        projectWbsCostSystemVO.setProjectId(projectId);
        projectWbsCostSystemVO.setWbsSummaryCode(wbsSummaryCode);
        projectWbsCostSystemVO.setActivityCode(activityCode);
        projectWbsCostSystemVO.setDemandCost(demandCost);
        projectWbsCostSystemVO.setProjectEaDetails(projectEaDetails);
        projectWbsCostSystemVO.setHroRequirementBudgets(hroRequirementBudgets);
        projectWbsCostSystemVO.setHroRequirementBudgetApprovals(hroRequirementBudgetApprovals);
        return projectWbsCostSystemVO;
    }

    @Override
    public List<RequirementPoDetail> amountByPurchaseOrder(ProjectWbsSummaryDto projectWbsSummaryDto) {
        //查询项目成本的执行ID
        ProjectWbsExecuteDetail projectWbsExecuteDetail = getWbsExecuteDetail(projectWbsSummaryDto.getProjectId());
        Long executeId = projectWbsExecuteDetail.getExecuteId();
        Long projectId = projectWbsSummaryDto.getProjectId();
        String wbsSummaryCode = projectWbsSummaryDto.getWbsSummaryCode();
        Long projectWbsReceiptsId = projectWbsSummaryDto.getProjectWbsReceiptsId();
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(wbsSummaryCode, ErrorCode.CTC_WBSSUMMARY_CODE_NULL);
        String activityCode = ProjectWbsCostConstant.SYSTEM_ACTIVITY;

        //PO在途
        RequirementPoDetailExample poExample = new RequirementPoDetailExample();
        RequirementPoDetailExample.Criteria poCriteria = poExample.createCriteria();
        poCriteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andProjectWbsReceiptsIdEqualTo(projectWbsReceiptsId).andPoTypeEqualTo(0).andDeletedFlagEqualTo(Boolean.FALSE);
        return requirementPoDetailMapper.selectByExample(poExample);
    }

    @Override
    public List<HroRequirementContractDetail> hroRequirementContractDetail(ProjectHroWbsSummaryDto projectHroWbsSummaryDto) {
        //查询项目成本的执行ID
        ProjectWbsExecuteDetail projectWbsExecuteDetail = getWbsExecuteDetail(projectHroWbsSummaryDto.getProjectId());
        Long executeId = projectWbsExecuteDetail.getExecuteId();
        Long projectId = projectHroWbsSummaryDto.getProjectId();
        String wbsSummaryCode = projectHroWbsSummaryDto.getWbsSummaryCode();
        String activityCode = projectHroWbsSummaryDto.getActivityCode();
        String requirementCode = projectHroWbsSummaryDto.getRequirementCode();
        String roleName = projectHroWbsSummaryDto.getRoleName();
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(wbsSummaryCode, ErrorCode.CTC_WBSSUMMARY_CODE_NULL);

        //项目成本人力点工合同占用明细
        HroRequirementContractDetailExample example = new HroRequirementContractDetailExample();
        HroRequirementContractDetailExample.Criteria criteria = example.createCriteria();
        criteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode)
                .andRequirementCodeEqualTo(requirementCode).andRoleNameEqualTo(roleName).andBudgetTypeEqualTo(0).andDeletedFlagEqualTo(Boolean.FALSE);
        return hroRequirementContractDetailMapper.selectByExample(example);
    }

    @Override
    public List<RequirementPurchaseContractDetail> purchaseContract(ProjectWbsSummaryDto projectWbsSummaryDto) {
        //查询项目成本的执行ID
        ProjectWbsExecuteDetail projectWbsExecuteDetail = getWbsExecuteDetail(projectWbsSummaryDto.getProjectId());
        Long executeId = projectWbsExecuteDetail.getExecuteId();
        Long projectId = projectWbsSummaryDto.getProjectId();
        String wbsSummaryCode = projectWbsSummaryDto.getWbsSummaryCode();
        Long projectWbsReceiptsId = projectWbsSummaryDto.getProjectWbsReceiptsId();
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(wbsSummaryCode, ErrorCode.CTC_WBSSUMMARY_CODE_NULL);
        String activityCode = ProjectWbsCostConstant.SYSTEM_ACTIVITY;

        //PO在途
        RequirementPurchaseContractDetailExample example = new RequirementPurchaseContractDetailExample();
        RequirementPurchaseContractDetailExample.Criteria criteria = example.createCriteria();
        criteria.andExecuteIdEqualTo(executeId).andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andActivityCodeEqualTo(activityCode).andProjectWbsReceiptsIdEqualTo(projectWbsReceiptsId).andDeletedFlagEqualTo(Boolean.FALSE);
        return requirementPurchaseContractDetailMapper.selectByExample(example);
    }

    @Override
    public void handleList(List<Long> projectIds, Long executeId) {
        List<Boolean> booleanList = new ArrayList<>();
        //按项目id升序
        Collections.sort(projectIds);
        // 批量处理所有数据
        int threadNum = projectIds.size() / EACH_THREAD_DATA_NUM + (projectIds.size() % EACH_THREAD_DATA_NUM == 0 ? 0 : 1);
        CountDownLatch addCountDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * EACH_THREAD_DATA_NUM;
            int endIndex = i == threadNum ? projectIds.size() : i * EACH_THREAD_DATA_NUM;
            List<Long> splitList = projectIds.subList(startIndex, endIndex);
            // 多线程执行
            projectWbsCostJobExecutor.execute(() -> {
                StringBuilder errorMsg = new StringBuilder();
                try {
                    projectWbsExecuteDetailService.insert(executeId, splitList);
                    List<ProjectWbsBudgetDto> projectWbsBudgetList = getProjectWbsCostSummaryForJob(splitList, executeId);
                    projectWbsBudgetSummaryService.updateBatchAll(projectWbsBudgetList, splitList);
                } catch (Exception e) {
                    logger.error("项目成本wbs定时任务执行失败", e);
                    errorMsg.append(e.getMessage());
                    booleanList.add(false);
                } finally {
                    String errorMsgStr = errorMsg.toString();
                    if (errorMsg.length() > 1000) {
                        errorMsgStr = errorMsg.substring(0, 1000);
                    }
                    for (Long projectId : splitList) {
                        projectWbsExecuteDetailService.updateResultInfo(executeId, projectId, errorMsgStr, StringUtils.isNotBlank(errorMsgStr) ? 1 : 0);
                    }
                    addCountDownLatch.countDown();
                    if (Objects.equals(addCountDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after projectWbsCostJobExecutor 项目成本统计", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            });
        }
        try {
            addCountDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("projectWbsCostJobExecutor项目成本统计的await失败", ex);
            Thread.currentThread().interrupt();
        }
        if (booleanList.contains(false)) {
            throw new BizException(Code.ERROR, "项目成本统计执行批量失败");
        }
    }

    @Override
    public List<Long> queryProjectForJobByDate(String halfAnHourAgo) {
        return Lists.newArrayList();
    }

    @Override
    public void deleteBatchAllDataForJob(List<ProjectWbsExecuteDetail> list) {
        if (ListUtils.isNotEmpty(list)) {
            List<List<ProjectWbsExecuteDetail>> lists = ListUtils.splistList(list, 100);
            for (List<ProjectWbsExecuteDetail> po : lists) {
                projectWorkingHourDetailExtMapper.deleteBatch(po);
                projectPurchaseContractDetailExtMapper.deleteBatch(po);
                projectMaterialDetailExtMapper.deleteBatch(po);
                requirementPurchaseContractDetailExtMapper.deleteBatch(po);
                requirementBudgetExtMapper.deleteBatch(po);
                projectEaDetailExtMapper.deleteBatch(po);
                projectWbsExecuteDetailExtMapper.deleteBatch(po);
            }
            //单独处理表requirement_po_detail的数据清除,list已限制最大1000条
            deleteBatchRequirementPoDetail(list);
        }
    }

    private void deleteBatchRequirementPoDetail(List<ProjectWbsExecuteDetail> projectWbsExecuteDetails) {
        //查询并收集所有待删减记录的id
        List<Long> list = requirementPoDetailExtMapper.queryAllByProjectWbsExecuteDetail(projectWbsExecuteDetails);
        if (ListUtils.isNotEmpty(list)) {
            List<List<Long>> lists = ListUtils.splistList(list, 1000);
            for (List<Long> ids : lists) {
                try {
                    // 睡眠200毫秒
                    Thread.sleep(200);
                    // 根据表id批量删除
                    requirementPoDetailExtMapper.deleteBatchByIds(ids);
                } catch (InterruptedException e) {
                    logger.error("deleteBatchRequirementPoDetail加sleep失败", e);
                }
            }
        }
    }

    @Override
    public List<ProjectWbsCostByReceiptsVO> requirementCodeBudget(ProjectWbsSummaryDto projectWbsSummaryDto) {
        //查询项目成本的执行ID
        ProjectWbsExecuteDetail projectWbsExecuteDetail = getWbsExecuteDetail(projectWbsSummaryDto.getProjectId());
        if (Objects.isNull(projectWbsExecuteDetail)) {
            return new ArrayList<>();
        }
        Long executeId = projectWbsExecuteDetail.getExecuteId();
        projectWbsSummaryDto.setExecuteId(executeId);
        //计算已发生成本 已经Sql实现
        List<ProjectWbsCostByReceiptsVO> list = projectWbsCostSummaryExtMapper.requirementCodeBudget(projectWbsSummaryDto);
        //数据过滤：只取指定需求发布单据的数据 BUG2023033151607
        List<Long> projectWbsPublishReceiptsIdList = projectWbsSummaryDto.getProjectWbsPublishReceiptsIdList();
        if (projectWbsPublishReceiptsIdList != null) {
            return list.stream().filter(s -> projectWbsPublishReceiptsIdList.contains(s.getProjectWbsReceiptsId())).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public ProjectCostExecuteRecord generateExecuteRecord() {
        ProjectCostExecuteRecord projectCostExecuteRecord = new ProjectCostExecuteRecord();
        projectCostExecuteRecord.setStartTime(new Date());
        projectCostExecuteRecord.setStatus(ProjectCostExecuteStatus.NEW.getCode());
        projectCostExecuteRecord.setDeletedFlag(Boolean.FALSE);
        projectCostExecuteRecord.setOrNotWbs(1);
        projectCostExecuteRecordMapper.insert(projectCostExecuteRecord);
        return projectCostExecuteRecord;
    }

    /**
     * 查询明细
     *
     * @param param
     * @return
     */
    private List<ProjectWbsBudgetDto> selectDetailByParam(Map<String, Object> param) {
        Long wbsTemplateInfoId = MapUtils.getLong(param, ProjectWbsCostConstant.WBS_TEMPLATE_INFO_ID);
        // 动态列参数组装
        List<WbsTemplateRuleCache> dynamicFields = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);
        if (!CollectionUtils.isEmpty(dynamicFields)) {
            List<WbsDynamicFieldsDto> dynamicFieldList = new ArrayList<>();
            for (WbsTemplateRuleCache field : dynamicFields) {
                if (StringUtils.isNotBlank(MapUtils.getString(param, field.getKey()))) {
                    WbsDynamicFieldsDto wbsDynamicParam = new WbsDynamicFieldsDto();
                    wbsDynamicParam.setKey(field.getKey());
                    wbsDynamicParam.setValue(MapUtils.getString(param, field.getKey()));
                    dynamicFieldList.add(wbsDynamicParam);
                }
            }
            if (!CollectionUtils.isEmpty(dynamicFieldList)) {
                param.put("dynamicFieldList", dynamicFieldList);
            }
        }
        return projectWbsCostSummaryExtMapper.listByParam(param);
    }

    private List<Long> getCostCalculateRange(Long ouId) {
        // 项目状态: 项目进行中、项目变更中、预立项转正审批中、预立项转正驳回、终止
        // date  2023-01 增加 结项 状态的统计
        List<Integer> statuses = getCostCalculateStatus();
        statuses.add(ProjectStatus.CLOSE.getCode());
        return projectWbsCostSummaryExtMapper.getNeedCalculateRange(ouId, statuses);
    }

    private List<Long> getAllDelProject() {
        //增加 结项 状态的统计
        List<Integer> statuses = getCostCalculateStatus();
        statuses.add(ProjectStatus.CLOSE.getCode());
        return projectWbsCostSummaryExtMapper.getNeedCalculateRange(null, statuses);
    }

    private List<Integer> getCostCalculateStatus() {
        Dict query = new Dict();
        query.setType(ProjectWbsCostConstant.PROJECT_COST_REGION);
        List<Dict> dicts = dictExtMapper.list(query);
        // 项目状态
        List<Integer> statuses = new ArrayList<>();
        if (ListUtils.isNotEmpty(dicts)) {
            for (Dict dict : dicts) {
                String code = dict.getCode();
                statuses.add(Integer.valueOf(code));
            }
        } else {
            statuses.add(-1);
        }
        return statuses;
    }

    private String getGetMaterialKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("getGetMaterialKey_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getReturnMaterialKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("getReturnMaterialKey_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getAllocationExecuteAmountKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("allocationExecuteAmount_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getBudgetExecuteAmountTotalVOKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("budgetExecuteAmountTotal_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getWkCompleteKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("wkComplete_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getWkOnTheWayKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("wkOnTheWay_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getEaKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("ea_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getEaOnTheWayKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("eaOnTheWay_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getEaCompleteKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("eaComplete_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getPoDownKey(Long projectId, String wbsSummaryCode, String activityCode, String requirementCode) {
        return String.format("poDown_%d_%s_%s_%s", projectId, wbsSummaryCode, activityCode, requirementCode);
    }

    private String getPoOnTheWayKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("poOnTheWay_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getContractDownKey(Long projectId, String wbsSummaryCode, String activityCode, String requirementCode) {
        return String.format("contractDown_%d_%s_%s_%s", projectId, wbsSummaryCode, activityCode, requirementCode);
    }

    private String getRequirementBudgetKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("requirementBudget_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getExportKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("exportKey_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getContractDownKey(Long projectId, String wbsSummaryCode, String activityCode, String requirementCode, String roleName) {
        return String.format("contractDown_%d_%s_%s_%s_%s", projectId, wbsSummaryCode, activityCode, requirementCode, roleName);
    }

    private String getHroPurchaseContractOnTheWay(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("hroPurchaseContractOnTheWay_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private String getHroPurchaseContractCompleteKey(Long projectId, String wbsSummaryCode, String activityCode) {
        return String.format("hroPurchaseContractComplete_%d_%s_%s", projectId, wbsSummaryCode, activityCode);
    }

    private BigDecimal toFilterNull(BigDecimal bigDecimal) {
        return Objects.nonNull(bigDecimal) ? bigDecimal : BigDecimal.ZERO;
    }


}
