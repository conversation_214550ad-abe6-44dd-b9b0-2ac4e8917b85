package com.midea.pam.support.mybatis.specialchar.config;

import com.midea.pam.support.mybatis.specialchar.loader.DefaultSpecialCharacterConfigLoader;
import com.midea.pam.support.mybatis.specialchar.loader.SpecialCharacterConfigLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Collection;
import java.util.Comparator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 特殊字符处理配置管理
 * 基于接口抽象的动态配置模式，支持60秒定时刷新
 * 通过SpecialCharacterConfigLoader接口实现配置加载的解耦
 */
public class SpecialCharacterConfig implements ApplicationContextAware {
    
    private static final Logger logger = LoggerFactory.getLogger(SpecialCharacterConfig.class);

    private static final long CACHE_REFRESH_INTERVAL = 60000; // 硬编码60秒刷新间隔

    // Spring上下文，用于动态获取Bean避免循环依赖
    private ApplicationContext applicationContext;

    // 配置加载器，通过接口抽象实现解耦
    private volatile SpecialCharacterConfigLoader configLoader;

    // 缓存配置，避免频繁查询
    private volatile Boolean cachedEnabled = null;
    private volatile long lastRefreshTime = 0;

    // 表字段配置缓存：tableName -> Set<fieldName>
    private volatile Map<String, Set<String>> tableFieldMap = new ConcurrentHashMap<>();

    // 定时刷新线程池
    private volatile ScheduledExecutorService scheduledExecutor;

    // 标记是否已启动定时刷新
    private volatile boolean scheduledRefreshStarted = false;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * Spring组件初始化完成后的回调方法
     * 记录组件加载日志并启动定时刷新
     */
    @PostConstruct
    public void init() {
        logger.info("SpecialCharacterConfig组件初始化完成 - 基于接口抽象的动态配置管理器已加载");
        logger.info("配置刷新间隔: {}ms", CACHE_REFRESH_INTERVAL);

        // 初始化时检查一次配置状态
        try {
            refreshConfig(); // 立即执行一次配置加载
            SpecialCharacterConfigLoader loader = getConfigLoader();
            logger.info("特殊字符处理功能初始状态: {}, 配置加载器类型: {}",
                       cachedEnabled ? "启用" : "禁用", loader.getLoaderType());
            logger.info("已加载表配置数量: {}", tableFieldMap.size());

            // 启动定时刷新
            startScheduledRefresh();

        } catch (Exception e) {
            logger.warn("初始化时检查配置状态异常: {}", e.getMessage());
        }
    }

    /**
     * 启动定时刷新任务
     */
    private void startScheduledRefresh() {
        if (!scheduledRefreshStarted) {
            synchronized (this) {
                if (!scheduledRefreshStarted) {
                    scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                        Thread thread = new Thread(r, "SpecialCharacterConfig-Refresh");
                        thread.setDaemon(true); // 设置为守护线程
                        return thread;
                    });

                    // 延迟60秒后开始，然后每60秒执行一次
                    scheduledExecutor.scheduleAtFixedRate(
                        this::performScheduledRefresh,
                        CACHE_REFRESH_INTERVAL,
                        CACHE_REFRESH_INTERVAL,
                        TimeUnit.MILLISECONDS
                    );

                    scheduledRefreshStarted = true;
                    logger.info("特殊字符配置定时刷新任务已启动，间隔: {}ms", CACHE_REFRESH_INTERVAL);
                }
            }
        }
    }

    /**
     * 执行定时刷新
     */
    private void performScheduledRefresh() {
        try {
            refreshConfig();
            lastRefreshTime = System.currentTimeMillis();
            logger.debug("定时刷新特殊字符处理配置完成");
        } catch (Exception e) {
            logger.error("定时刷新特殊字符处理配置异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 组件销毁时的清理方法
     */
    @PreDestroy
    public void destroy() {
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
                logger.info("特殊字符配置定时刷新任务已停止");
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    public boolean isEnabled() {
        refreshConfigIfNeeded();
        return cachedEnabled != null ? cachedEnabled : false;
    }
    
    public boolean isTableConfigured(String tableName) {
        if (StringUtils.isEmpty(tableName)) {
            return false;
        }
        refreshConfigIfNeeded();
        return tableFieldMap.containsKey(tableName);
    }

    /**
     * 检查指定表是否配置了全表特殊字符处理
     *
     * @param tableName 表名
     * @return true-配置了全表处理，false-未配置全表处理
     */
    public boolean isTableConfiguredForAllFields(String tableName) {
        if (StringUtils.isEmpty(tableName)) {
            return false;
        }
        refreshConfigIfNeeded();
        Set<String> fields = tableFieldMap.get(tableName);
        return fields != null && fields.contains("*");
    }

    /**
     * 检查指定表的字段是否配置了特殊字符处理
     * 支持驼峰命名和下划线命名的双向匹配
     * 支持全表配置（使用 * 标识符）
     *
     * @param tableName 表名
     * @param fieldName 字段名（支持驼峰或下划线格式）
     * @return true-已配置，false-未配置
     */
    public boolean isFieldConfigured(String tableName, String fieldName) {
        if (StringUtils.isEmpty(tableName) || StringUtils.isEmpty(fieldName)) {
            return false;
        }
        refreshConfigIfNeeded();
        Set<String> fields = tableFieldMap.get(tableName);
        if (fields == null) {
            return false;
        }

        // 1. 检查全表配置（优先级最高）
        if (fields.contains("*")) {
            if (logger.isDebugEnabled()) {
                logger.debug("字段配置匹配成功 - 表: {}, 字段: {} (全表配置)", tableName, fieldName);
            }
            return true;
        }

        // 2. 检查具体字段配置（字段名已在加载时预处理，包含驼峰和下划线两种格式）
        boolean isConfigured = fields.contains(fieldName);

        if (logger.isDebugEnabled()) {
            if (isConfigured) {
                logger.debug("字段配置匹配成功 - 表: {}, 字段: {} (字段级配置)", tableName, fieldName);
            } else {
                logger.debug("字段配置匹配失败 - 表: {}, 字段: {}, 可用字段: {}",
                           tableName, fieldName, fields);
            }
        }

        return isConfigured;
    }
    
    private void refreshConfigIfNeeded() {
        // 由于已经有定时刷新任务，这里只做简单检查
        // 如果配置从未加载过，则立即加载一次
        if (cachedEnabled == null) {
            synchronized (this) {
                if (cachedEnabled == null) {
                    logger.debug("首次访问，立即加载配置");
                    refreshConfig();
                    lastRefreshTime = System.currentTimeMillis();
                }
            }
        }
    }
    
    private void refreshConfig() {
        try {
            SpecialCharacterConfigLoader loader = getConfigLoader();

            // 检查功能是否启用
            boolean enabled = loader.isEnabled();
            this.cachedEnabled = enabled;

            if (enabled) {
                // 加载表字段配置（配置加载器自行决定使用哪个数据库）
                Map<String, Set<String>> rawTableFieldMap = loader.loadTableFieldConfigurations();

                // 预处理字段名，同时存储驼峰和下划线格式
                Map<String, Set<String>> processedTableFieldMap = preprocessFieldNames(rawTableFieldMap);
                this.tableFieldMap = new ConcurrentHashMap<>(processedTableFieldMap);

                logger.debug("刷新特殊字符处理配置完成 - 加载器类型: {}, 原始配置数量: {}, 预处理后配置数量: {}, 功能状态: 启用",
                            loader.getLoaderType(), rawTableFieldMap.size(),
                            processedTableFieldMap.values().stream().mapToInt(Set::size).sum());
            } else {
                // 功能禁用时清空配置
                this.tableFieldMap.clear();
                logger.debug("刷新特殊字符处理配置完成 - 加载器类型: {}, 功能状态: 禁用", loader.getLoaderType());
            }

        } catch (Exception e) {
            logger.error("刷新特殊字符处理配置异常: {}", e.getMessage(), e);
            // 异常时默认禁用功能，但保持现有配置不变
            this.cachedEnabled = false;
        }
    }
    
    /**
     * 获取配置加载器实例
     * 优先使用Spring容器中注册的实现，如果没有则使用默认实现
     */
    private SpecialCharacterConfigLoader getConfigLoader() {
        if (configLoader != null) {
            return configLoader;
        }

        synchronized (this) {
            if (configLoader != null) {
                return configLoader;
            }

            try {
                // 尝试从Spring容器中获取所有配置加载器实现
                Collection<SpecialCharacterConfigLoader> loaders =
                    applicationContext.getBeansOfType(SpecialCharacterConfigLoader.class).values();

                if (!loaders.isEmpty()) {
                    // 按优先级排序，选择优先级最高的实现
                    configLoader = loaders.stream()
                        .min(Comparator.comparingInt(SpecialCharacterConfigLoader::getPriority))
                        .orElse(new DefaultSpecialCharacterConfigLoader());

                    logger.info("发现配置加载器实现: {}, 优先级: {}",
                               configLoader.getLoaderType(), configLoader.getPriority());
                } else {
                    configLoader = new DefaultSpecialCharacterConfigLoader();
                    logger.info("未发现配置加载器实现，使用默认实现");
                }

            } catch (NoSuchBeanDefinitionException e) {
                configLoader = new DefaultSpecialCharacterConfigLoader();
                logger.info("Spring容器中无配置加载器Bean，使用默认实现");
            } catch (Exception e) {
                configLoader = new DefaultSpecialCharacterConfigLoader();
                logger.warn("获取配置加载器失败，使用默认实现: {}", e.getMessage());
            }
        }

        return configLoader;
    }



    /**
     * 手动设置启用状态（用于外部配置注入）
     */
    public void setEnabled(boolean enabled) {
        this.cachedEnabled = enabled;
        logger.info("手动设置特殊字符处理功能状态: {}", enabled);
    }

    /**
     * 手动刷新配置（用于外部触发）
     */
    public void manualRefresh() {
        synchronized (this) {
            refreshConfig();
            lastRefreshTime = System.currentTimeMillis();
        }
        logger.info("手动刷新特殊字符处理配置完成");
    }

    /**
     * 获取当前配置的表数量（用于监控）
     */
    public int getConfiguredTableCount() {
        return tableFieldMap.size();
    }

    /**
     * 获取指定表的配置字段数量（用于监控）
     */
    public int getConfiguredFieldCount(String tableName) {
        Set<String> fields = tableFieldMap.get(tableName);
        return fields != null ? fields.size() : 0;
    }

    /**
     * 获取所有配置的表名（用于监控和调试）
     */
    public Set<String> getConfiguredTableNames() {
        return tableFieldMap.keySet();
    }

    /**
     * 预处理字段名，同时存储驼峰和下划线格式
     * 提高查询性能，避免运行时转换
     *
     * @param rawTableFieldMap 原始配置映射
     * @return 预处理后的配置映射（包含两种格式的字段名）
     */
    private Map<String, Set<String>> preprocessFieldNames(Map<String, Set<String>> rawTableFieldMap) {
        Map<String, Set<String>> processedMap = new ConcurrentHashMap<>();

        for (Map.Entry<String, Set<String>> entry : rawTableFieldMap.entrySet()) {
            String tableName = entry.getKey();
            Set<String> originalFields = entry.getValue();
            Set<String> processedFields = ConcurrentHashMap.newKeySet();

            for (String fieldName : originalFields) {
                // 添加原始字段名
                processedFields.add(fieldName);

                // 全表配置标识符不需要进行命名转换
                if ("*".equals(fieldName)) {
                    continue;
                }

                // 添加驼峰格式（如果原始不是驼峰）
                String camelFormat = underscoreToCamel(fieldName);
                if (!camelFormat.equals(fieldName)) {
                    processedFields.add(camelFormat);
                }

                // 添加下划线格式（如果原始不是下划线）
                String underscoreFormat = camelToUnderscore(fieldName);
                if (!underscoreFormat.equals(fieldName)) {
                    processedFields.add(underscoreFormat);
                }
            }

            processedMap.put(tableName, processedFields);

            // 记录预处理详情
            if (logger.isDebugEnabled() && processedFields.size() > originalFields.size()) {
                logger.debug("表 {} 字段名预处理完成 - 原始: {}, 预处理后: {}",
                           tableName, originalFields, processedFields);
            }
        }

        return processedMap;
    }

    /**
     * 驼峰命名转下划线命名
     * 例如：materialName -> material_name
     *
     * @param camelCase 驼峰格式字符串
     * @return 下划线格式字符串
     */
    private String camelToUnderscore(String camelCase) {
        if (StringUtils.isEmpty(camelCase)) {
            return camelCase;
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char c = camelCase.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 下划线命名转驼峰命名
     * 例如：material_name -> materialName
     *
     * @param underscore 下划线格式字符串
     * @return 驼峰格式字符串
     */
    private String underscoreToCamel(String underscore) {
        if (StringUtils.isEmpty(underscore)) {
            return underscore;
        }

        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;

        for (int i = 0; i < underscore.length(); i++) {
            char c = underscore.charAt(i);
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    result.append(c);
                }
            }
        }
        return result.toString();
    }
}
