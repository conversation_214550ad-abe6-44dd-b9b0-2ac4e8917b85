package com.midea.pam.support.mybatis.specialchar.loader;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * 默认特殊字符配置加载器
 * 当没有其他实现时使用，功能默认禁用
 * 
 * <AUTHOR> Team
 */
public class DefaultSpecialCharacterConfigLoader implements SpecialCharacterConfigLoader {
    
    private static final Logger logger = LoggerFactory.getLogger(DefaultSpecialCharacterConfigLoader.class);
    
    @Override
    public Map<String, Set<String>> loadTableFieldConfigurations() {
        logger.debug("使用默认配置加载器，返回空配置");
        return Collections.emptyMap();
    }
    
    @Override
    public boolean isEnabled() {
        logger.debug("默认配置加载器 - 特殊字符处理功能已禁用");
        return false;
    }
    
    @Override
    public String getLoaderType() {
        return "default";
    }
    
    @Override
    public int getPriority() {
        return Integer.MAX_VALUE; // 最低优先级
    }
}
