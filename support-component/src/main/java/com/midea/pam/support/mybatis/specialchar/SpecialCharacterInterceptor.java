package com.midea.pam.support.mybatis.specialchar;

import com.midea.pam.support.mybatis.specialchar.processor.SpecialCharacterProcessor;
import com.midea.pam.support.mybatis.specialchar.config.SpecialCharacterConfig;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.defaults.DefaultSqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Properties;

/**
 * MySQL数据库特殊字符处理拦截器
 * 基于AuditInterceptor的实现模式，拦截INSERT和UPDATE操作
 * 自动处理实体对象中String类型字段的特殊字符
 * 

 */
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
public class SpecialCharacterInterceptor implements Interceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(SpecialCharacterInterceptor.class);
    
    private SpecialCharacterProcessor processor;
    private SpecialCharacterConfig config;
    
    public void setSpecialCharacterProcessor(SpecialCharacterProcessor processor) {
        this.processor = processor;
    }
    
    public void setSpecialCharacterConfig(SpecialCharacterConfig config) {
        this.config = config;
    }
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 检查功能是否启用
        if (config == null || !config.isEnabled()) {
            return invocation.proceed();
        }
        
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object parameter = args[1];
        
        // 只处理INSERT和UPDATE操作
        SqlCommandType sqlCommandType = ms.getSqlCommandType();
        if (sqlCommandType != SqlCommandType.INSERT && sqlCommandType != SqlCommandType.UPDATE) {
            return invocation.proceed();
        }
        
        try {
            // 处理特殊字符
            processSpecialCharacters(ms, parameter, sqlCommandType);
        } catch (Exception e) {
            logger.error("特殊字符处理异常，继续执行原始操作: {}", e.getMessage(), e);
        }
        
        return invocation.proceed();
    }
    
    /**
     * 处理特殊字符
     * 支持单个对象、List集合、数组等多种参数类型
     */
    private void processSpecialCharacters(MappedStatement ms, Object parameter, SqlCommandType sqlCommandType) {
        if (parameter == null || processor == null) {
            return;
        }
        
        // 处理批量操作的参数包装
        if (parameter instanceof DefaultSqlSession.StrictMap) {
            @SuppressWarnings("rawtypes")
            DefaultSqlSession.StrictMap map = (DefaultSqlSession.StrictMap) parameter;

            // 处理List集合
            if (map.containsKey("list")) {
                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) map.get("list");
                if (list != null && !list.isEmpty()) {
                    for (Object obj : list) {
                        processor.processEntity(obj, ms, sqlCommandType);
                    }
                }
            }
            // 处理数组
            else if (map.containsKey("array")) {
                Object[] array = (Object[]) map.get("array");
                if (array != null) {
                    for (Object obj : array) {
                        processor.processEntity(obj, ms, sqlCommandType);
                    }
                }
            }
            // 处理其他包装参数
            else {
                for (Object value : map.values()) {
                    if (value != null) {
                        processor.processEntity(value, ms, sqlCommandType);
                    }
                }
            }
        } else {
            // 处理单个对象
            processor.processEntity(parameter, ms, sqlCommandType);
        }
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 预留配置属性设置接口
        logger.info("SpecialCharacterInterceptor initialized with properties: {}", properties);
    }
}
