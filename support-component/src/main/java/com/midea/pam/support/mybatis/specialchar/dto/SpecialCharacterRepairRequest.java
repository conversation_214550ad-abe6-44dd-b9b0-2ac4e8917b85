package com.midea.pam.support.mybatis.specialchar.dto;

import java.io.Serializable;

/**
 * 特殊字符修复请求对象
 * 

 */
public class SpecialCharacterRepairRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 数据库名称
     */
    private String databaseName;
    
    /**
     * 数据表名称
     */
    private String tableName;
    
    /**
     * 字段名称
     */
    private String columnName;
    
    /**
     * 记录ID（可选，为空则处理整表）
     */
    private String recordId;
    
    public SpecialCharacterRepairRequest() {
    }
    
    public SpecialCharacterRepairRequest(String databaseName, String tableName, String columnName) {
        this.databaseName = databaseName;
        this.tableName = tableName;
        this.columnName = columnName;
    }
    
    public SpecialCharacterRepairRequest(String databaseName, String tableName, String columnName, String recordId) {
        this.databaseName = databaseName;
        this.tableName = tableName;
        this.columnName = columnName;
        this.recordId = recordId;
    }
    
    public String getDatabaseName() {
        return databaseName;
    }
    
    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }
    
    public String getTableName() {
        return tableName;
    }
    
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
    
    public String getColumnName() {
        return columnName;
    }
    
    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }
    
    public String getRecordId() {
        return recordId;
    }
    
    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }
    
    @Override
    public String toString() {
        return "SpecialCharacterRepairRequest{" +
                "databaseName='" + databaseName + '\'' +
                ", tableName='" + tableName + '\'' +
                ", columnName='" + columnName + '\'' +
                ", recordId='" + recordId + '\'' +
                '}';
    }
}
