package com.midea.pam.support.mybatis.specialchar.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 特殊字符修复记录对象
 * 

 */
public class SpecialCharacterRepairRecord implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 记录ID
     */
    private String recordId;
    
    /**
     * 字段名称
     */
    private String columnName;
    
    /**
     * 修改前内容
     */
    private String originalValue;
    
    /**
     * 修改后内容
     */
    private String modifiedValue;
    
    /**
     * 检测到的特殊字符列表
     */
    private List<String> specialCharsFound;
    
    public SpecialCharacterRepairRecord() {
    }
    
    public SpecialCharacterRepairRecord(String recordId, String columnName, String originalValue, String modifiedValue) {
        this.recordId = recordId;
        this.columnName = columnName;
        this.originalValue = originalValue;
        this.modifiedValue = modifiedValue;
    }
    
    public String getRecordId() {
        return recordId;
    }
    
    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }
    
    public String getColumnName() {
        return columnName;
    }
    
    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }
    
    public String getOriginalValue() {
        return originalValue;
    }
    
    public void setOriginalValue(String originalValue) {
        this.originalValue = originalValue;
    }
    
    public String getModifiedValue() {
        return modifiedValue;
    }
    
    public void setModifiedValue(String modifiedValue) {
        this.modifiedValue = modifiedValue;
    }
    
    public List<String> getSpecialCharsFound() {
        return specialCharsFound;
    }
    
    public void setSpecialCharsFound(List<String> specialCharsFound) {
        this.specialCharsFound = specialCharsFound;
    }
    
    @Override
    public String toString() {
        return "SpecialCharacterRepairRecord{" +
                "recordId='" + recordId + '\'' +
                ", columnName='" + columnName + '\'' +
                ", originalValue='" + (originalValue != null ? originalValue.length() + " chars" : "null") + '\'' +
                ", modifiedValue='" + (modifiedValue != null ? modifiedValue.length() + " chars" : "null") + '\'' +
                ", specialCharsFound=" + specialCharsFound +
                '}';
    }
}
