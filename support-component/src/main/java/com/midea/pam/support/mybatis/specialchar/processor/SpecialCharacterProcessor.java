package com.midea.pam.support.mybatis.specialchar.processor;

import com.midea.pam.support.mybatis.specialchar.config.SpecialCharacterConfig;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.util.CacheDataUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 特殊字符处理核心逻辑
 * 负责检测和处理实体对象中的特殊字符
 * 

 */
public class SpecialCharacterProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(SpecialCharacterProcessor.class);
    
    private SpecialCharacterConfig config;
    
    // XML 1.0 不兼容字符正则模式
    // 根据XML 1.0规范，以下字符在XML文档中是禁止的或有问题的
    // 排除了合法的制表符(U+0009)、换行符(U+000A)、回车符(U+000D)
    // \\u0000-\\u0008: C0控制字符范围（除了合法的tab）
    //   \\u0000: NULL字符 - XML严格禁止
    //   \\u0001-\\u0008: 各种控制字符 - 在XML中无意义且可能导致解析错误
    // \\u000B: 垂直制表符(VT) - XML 1.0不允许
    // \\u000C: 换页符(FF) - XML 1.0不允许
    // \\u000E-\\u001F: 其余C0控制字符范围
    //   \\u000E-\\u001F: 包括各种控制字符如SO、SI、DLE、DC1-DC4、NAK等
    //   这些字符在现代系统中很少使用，在XML中会导致兼容性问题
    // \\u007F-\\u009F: C1控制字符范围
    //   \\u007F: DEL删除字符
    //   \\u0080-\\u009F: C1控制字符集，包括各种控制序列
    //   这些字符在不同编码间转换时容易出现问题
    // \\uFFFE: 非字符 - Unicode标准定义的永久非字符
    // \\uFFFF: 非字符 - Unicode标准定义的永久非字符
    // 注意：合法字符 \\u0009(TAB)、\\u000A(LF)、\\u000D(CR) 被排除在外
    // 这些字符可能导致：XML解析失败、数据传输错误、编码转换问题、安全漏洞
    private static final Pattern XML_INVALID_CHARS = Pattern.compile(
        "[\\u0000-\\u0008\\u000B\\u000C\\u000E-\\u001F\\u007F-\\u009F\\uFFFE\\uFFFF]"
    );
    

    // 4字节UTF-8字符正则模式 (emoji等补充平面字符)
    // 匹配超出Unicode基本多文种平面(BMP)范围的字符
    // BMP范围: U+0000 到 U+FFFF (65536个码点)
    // 补充平面: U+10000 到 U+10FFFF (需要UTF-16代理对表示)
    // [^\\u0000-\\uFFFF] 表示匹配不在BMP范围内的所有字符
    // 这些字符在UTF-8中需要4个字节编码，在UTF-16中需要代理对表示
    // 典型例子: 😀(U+1F600), 𝕏(U+1D54F), 古埃及象形文字等
    private static final Pattern UTF8_4BYTE_CHARS = Pattern.compile(
        "[^\\u0000-\\uFFFF]"
    );
    
    // 其他有问题的特殊字符正则模式
    // 主要包含零宽字符和双向文本控制字符，这些字符不可见但会影响文本处理
    // \\u200B-\\u200F: 零宽字符和格式字符范围
    //   \\u200B: 零宽空格 (ZWSP) - 不可见的断行提示符
    //   \\u200C: 零宽非连字符 (ZWNJ) - 阻止字符连接
    //   \\u200D: 零宽连字符 (ZWJ) - 强制字符连接，常用于emoji组合
    //   \\u200E: 左到右标记 (LRM) - 强制文本从左到右显示
    //   \\u200F: 右到左标记 (RLM) - 强制文本从右到左显示
    // \\u202A-\\u202E: 双向文本控制字符范围
    //   \\u202A: 左到右嵌入 (LRE) - 开始左到右文本块
    //   \\u202B: 右到左嵌入 (RLE) - 开始右到左文本块
    //   \\u202C: 弹出方向格式 (PDF) - 结束方向嵌入
    //   \\u202D: 左到右覆盖 (LRO) - 强制左到右覆盖
    //   \\u202E: 右到左覆盖 (RLO) - 强制右到左覆盖
    // \\u2060-\\u206F: 格式字符和不可见字符范围
    //   \\u2060: 词连接符 (WJ) - 防止在此处断行
    //   \\u2061-\\u2064: 不可见数学运算符
    //   \\u206A-\\u206F: 各种格式控制字符
    // 这些字符可能导致：文本显示异常、复制粘贴问题、安全漏洞、数据库存储问题
    private static final Pattern OTHER_SPECIAL_CHARS = Pattern.compile(
        "[\\u200B-\\u200F\\u202A-\\u202E\\u2060-\\u206F]" // Zero-width characters etc.
    );

    // 自定义字符替换规则的正则表达式（动态加载）
    private Pattern customReplacementPattern;

    // 字典配置缓存时间戳，用于检测配置更新
    private long lastConfigLoadTime = 0;

    // 配置刷新间隔（毫秒）- 与现有缓存机制保持一致
    private static final long CONFIG_REFRESH_INTERVAL = 60000; // 60秒

    public void setSpecialCharacterConfig(SpecialCharacterConfig config) {
        this.config = config;
    }
    
    /**
     * 处理实体对象中的特殊字符
     */
    public void processEntity(Object entity, MappedStatement ms, SqlCommandType sqlCommandType) {
        if (entity == null || config == null) {
            return;
        }
        
        String tableName = extractTableName(ms);
        if (StringUtils.isEmpty(tableName) || !config.isTableConfigured(tableName)) {
            return;
        }
        
        Class<?> entityClass = entity.getClass();
        
        // 跳过基本类型和包装类型
        if (isBasicType(entityClass)) {
            return;
        }
        
        List<Field> fields = getAllFields(entityClass);

        // 检查是否配置了全表处理，优化性能
        boolean isAllFieldsConfigured = config.isTableConfiguredForAllFields(tableName);

        for (Field field : fields) {
            // 只处理String类型字段
            if (field.getType() != String.class) {
                continue;
            }

            String fieldName = field.getName();

            // 性能优化：如果配置了全表处理，直接处理；否则检查字段级配置
            boolean shouldProcess = isAllFieldsConfigured || config.isFieldConfigured(tableName, fieldName);
            if (!shouldProcess) {
                continue;
            }

            try {
                field.setAccessible(true);
                String originalValue = (String) field.get(entity);

                if (originalValue != null && hasSpecialCharacters(originalValue)) {
                    String processedValue = processSpecialCharacters(originalValue);
                    field.set(entity, processedValue);

                    // 记录处理日志，区分全表配置和字段级配置
                    logSpecialCharacterProcessing(tableName, fieldName, originalValue, processedValue, entity, isAllFieldsConfigured);
                }
            } catch (Exception e) {
                logger.error("处理字段特殊字符异常: {}.{}, 错误: {}", tableName, fieldName, e.getMessage(), e);
            }
        }
    }
    
    /**
     * 检测字符串是否包含特殊字符
     * 提供给外部类使用，确保检测逻辑的一致性
     */
    public boolean hasSpecialCharacters(String value) {
        if (StringUtils.isEmpty(value)) {
            return false;
        }

        // 刷新自定义字符替换规则配置
        loadCustomReplacementRulesIfNeeded();

        return XML_INVALID_CHARS.matcher(value).find() ||
               UTF8_4BYTE_CHARS.matcher(value).find() ||
               OTHER_SPECIAL_CHARS.matcher(value).find() ||
               hasCustomCharacters(value);
    }
    
    /**
     * 处理特殊字符，替换为空格
     */
    public String processSpecialCharacters(String value) {
        if (StringUtils.isEmpty(value)) {
            return value;
        }

        String processed = value;

        // 替换XML不兼容字符为空格
        processed = XML_INVALID_CHARS.matcher(processed).replaceAll(" ");

        // 替换4字节UTF8字符为空格
        processed = UTF8_4BYTE_CHARS.matcher(processed).replaceAll(" ");

        // 替换其他特殊字符为空格
        processed = OTHER_SPECIAL_CHARS.matcher(processed).replaceAll(" ");

        // 替换自定义字符为空格
        processed = processCustomCharacters(processed);

        return processed;
    }
    
    /**
     * 获取被处理的特殊字符列表
     */
    public List<String> getSpecialCharacters(String value) {
        List<String> specialChars = new ArrayList<>();
        
        if (StringUtils.isEmpty(value)) {
            return specialChars;
        }
        
        // 查找XML不兼容字符
        Matcher xmlMatcher = XML_INVALID_CHARS.matcher(value);
        while (xmlMatcher.find()) {
            String found = xmlMatcher.group();
            if (!specialChars.contains(found)) {
                specialChars.add(String.format("XML不兼容字符: \\u%04X", (int) found.charAt(0)));
            }
        }
        
        // 查找4字节UTF8字符
        Matcher utf8Matcher = UTF8_4BYTE_CHARS.matcher(value);
        while (utf8Matcher.find()) {
            String found = utf8Matcher.group();
            if (!specialChars.contains(found)) {
                specialChars.add("4字节UTF8字符: " + found);
            }
        }
        
        // 查找其他特殊字符
        Matcher otherMatcher = OTHER_SPECIAL_CHARS.matcher(value);
        while (otherMatcher.find()) {
            String found = otherMatcher.group();
            if (!specialChars.contains(found)) {
                specialChars.add(String.format("特殊字符: \\u%04X", (int) found.charAt(0)));
            }
        }

        // 查找自定义字符
        if (customReplacementPattern != null) {
            Matcher customMatcher = customReplacementPattern.matcher(value);
            while (customMatcher.find()) {
                String found = customMatcher.group();
                if (!specialChars.contains(found)) {
                    specialChars.add("自定义字符: " + found);
                }
            }
        }

        return specialChars;
    }
    
    /**
     * 检测字符串是否包含自定义字符
     */
    private boolean hasCustomCharacters(String value) {
        if (StringUtils.isEmpty(value) || customReplacementPattern == null) {
            return false;
        }
        return customReplacementPattern.matcher(value).find();
    }

    /**
     * 处理自定义字符，替换为空格
     */
    private String processCustomCharacters(String value) {
        if (StringUtils.isEmpty(value) || customReplacementPattern == null) {
            return value;
        }
        return customReplacementPattern.matcher(value).replaceAll(" ");
    }

    /**
     * 根据需要加载自定义字符替换规则
     * 使用缓存机制，避免频繁查询字典
     */
    private void loadCustomReplacementRulesIfNeeded() {
        long currentTime = System.currentTimeMillis();

        // 检查是否需要刷新配置
        if (currentTime - lastConfigLoadTime < CONFIG_REFRESH_INTERVAL) {
            return;
        }

        try {
            loadCustomReplacementRules();
            lastConfigLoadTime = currentTime;
        } catch (Exception e) {
            logger.error("加载自定义字符替换规则异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 从字典缓存加载自定义字符替换规则
     * 使用 CacheDataUtils.findDictByTypeAndCode API 读取配置
     * 支持逗号分隔的字符配置，例如: "$,#,("
     */
    private void loadCustomReplacementRules() {
        try {
            // 从字典缓存读取自定义字符替换配置
            DictDto dictConfig = CacheDataUtils.findDictByTypeAndCode("custom_character_replacement", "规则配置");

            if (dictConfig != null && !StringUtils.isEmpty(dictConfig.getDescription())) {
                String customChars = dictConfig.getDescription().trim();

                // 验证配置格式
                if (isValidCustomCharConfig(customChars)) {
                    // 解析逗号分隔的字符配置
                    String regexPattern = buildCustomCharacterRegex(customChars);

                    if (!StringUtils.isEmpty(regexPattern)) {
                        customReplacementPattern = Pattern.compile(regexPattern);

                        logger.info("[特殊字符处理] 成功加载自定义字符替换规则: {}, 正则表达式: {}",
                                   customChars, regexPattern);
                    } else {
                        logger.warn("[特殊字符处理] 解析自定义字符配置后为空: {}", customChars);
                        customReplacementPattern = null;
                    }
                } else {
                    logger.warn("[特殊字符处理] 自定义字符替换规则格式无效: {}", customChars);
                    customReplacementPattern = null;
                }
            } else {
                // 字典配置不存在或为空，清空自定义规则
                if (customReplacementPattern != null) {
                    logger.info("[特殊字符处理] 清空自定义字符替换规则");
                    customReplacementPattern = null;
                }
            }
        } catch (Exception e) {
            logger.error("[特殊字符处理] 加载自定义字符替换规则异常: {}", e.getMessage(), e);
            // 异常时保持现有配置，不影响正常处理流程
        }
    }

    /**
     * 构建自定义字符的正则表达式
     * 正确处理逗号分隔的字符配置，例如: "$,#,(" -> "[\$#\(]"
     *
     * @param customChars 逗号分隔的字符配置，如 "$,#,("
     * @return 正则表达式字符串，如 "[\$#\(]"
     */
    private String buildCustomCharacterRegex(String customChars) {
        if (StringUtils.isEmpty(customChars)) {
            return "";
        }

        // 按逗号分割配置字符串
        String[] charArray = customChars.split(",");
        StringBuilder regexBuilder = new StringBuilder("[");
        int validCharCount = 0;

        for (String singleChar : charArray) {
            String trimmedChar = singleChar.trim();

            // 跳过空字符
            if (trimmedChar.isEmpty()) {
                continue;
            }

            // 验证单个字符的有效性
            if (isValidSingleChar(trimmedChar)) {
                // 转义单个字符并添加到正则表达式
                String escapedChar = escapeRegexSpecialChars(trimmedChar);
                regexBuilder.append(escapedChar);
                validCharCount++;

                logger.debug("[特殊字符处理] 解析字符: '{}' -> '{}'", trimmedChar, escapedChar);
            } else {
                logger.warn("[特殊字符处理] 跳过无效字符: '{}'", trimmedChar);
            }
        }

        regexBuilder.append("]");

        // 如果没有有效字符，返回空字符串
        if (validCharCount == 0) {
            logger.warn("[特殊字符处理] 配置中没有有效字符: {}", customChars);
            return "";
        }

        String result = regexBuilder.toString();
        logger.debug("[特殊字符处理] 构建正则表达式完成: {} -> {}, 有效字符数: {}",
                    customChars, result, validCharCount);

        return result;
    }

    /**
     * 验证单个字符是否有效
     *
     * @param singleChar 单个字符
     * @return true-有效，false-无效
     */
    private boolean isValidSingleChar(String singleChar) {
        if (StringUtils.isEmpty(singleChar)) {
            return false;
        }

        // 单个字符长度不应超过1（考虑到可能的转义字符）
        if (singleChar.length() > 5) {
            return false;
        }

        // 检查是否包含不可见控制字符（除了常见的制表符、换行符等）
        for (char c : singleChar.toCharArray()) {
            if (Character.isISOControl(c) && c != '\t' && c != '\n' && c != '\r') {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证自定义字符配置格式是否有效
     */
    private boolean isValidCustomCharConfig(String customChars) {
        if (StringUtils.isEmpty(customChars)) {
            return false;
        }

        // 基本格式验证：长度合理
        if (customChars.length() > 100) {
            return false;
        }

        // 检查是否包含不可见控制字符（允许逗号作为分隔符）
        for (char c : customChars.toCharArray()) {
            if (Character.isISOControl(c) && c != '\t' && c != '\n' && c != '\r') {
                return false;
            }
        }

        return true;
    }

    /**
     * 转义正则表达式中的特殊字符
     */
    private String escapeRegexSpecialChars(String input) {
        // 需要转义的正则表达式特殊字符
        String[] regexSpecialChars = {"\\", "^", "$", ".", "|", "?", "*", "+", "(", ")", "[", "]", "{", "}"};

        String result = input;
        for (String specialChar : regexSpecialChars) {
            result = result.replace(specialChar, "\\" + specialChar);
        }

        return result;
    }

    /**
     * 记录特殊字符处理日志
     */
    private void logSpecialCharacterProcessing(String tableName, String fieldName,
                                             String originalValue, String processedValue, Object entity, boolean isAllFieldsConfigured) {
        try {
            String recordId = extractRecordId(entity);
            List<String> specialChars = getSpecialCharacters(originalValue);
            String configType = isAllFieldsConfigured ? "全表配置" : "字段级配置";

            logger.info("[特殊字符处理] 表:{}, 字段:{}, 记录ID:{}, 配置类型:{}, 原始值长度:{}, 处理后长度:{}, 特殊字符:{}",
                       tableName, fieldName, recordId, configType, originalValue.length(), processedValue.length(),
                       specialChars);
        } catch (Exception e) {
            logger.error("记录特殊字符处理日志异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录特殊字符处理日志（兼容旧版本）
     */
    private void logSpecialCharacterProcessing(String tableName, String fieldName,
                                             String originalValue, String processedValue, Object entity) {
        logSpecialCharacterProcessing(tableName, fieldName, originalValue, processedValue, entity, false);
    }
    
    /**
     * 从MappedStatement中提取表名
     */
    private String extractTableName(MappedStatement ms) {
        try {
            String id = ms.getId();
            // 从Mapper方法ID中提取表名信息
            // 例如: com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper.insert
            if (id.contains("Mapper.")) {
                String mapperClass = id.substring(0, id.lastIndexOf('.'));
                String className = mapperClass.substring(mapperClass.lastIndexOf('.') + 1);
                
                // 将驼峰命名转换为下划线命名
                return camelToUnderscore(className.replace("Mapper", ""));
            }
        } catch (Exception e) {
            logger.debug("提取表名异常: {}", e.getMessage());
        }
        return "";
    }
    
    /**
     * 提取记录ID
     */
    private String extractRecordId(Object entity) {
        try {
            // 尝试从当前类及父类中获取id字段
            List<Field> allFields = getAllFields(entity.getClass());
            for (Field field : allFields) {
                if ("id".equals(field.getName())) {
                    field.setAccessible(true);
                    Object idValue = field.get(entity);
                    return idValue != null ? idValue.toString() : "unknown";
                }
            }
        } catch (Exception e) {
            logger.debug("提取记录ID异常: {}", e.getMessage());
        }
        return "unknown";
    }
    
    /**
     * 驼峰命名转下划线命名
     */
    private String camelToUnderscore(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
    
    /**
     * 获取类及其所有父类的字段
     * 递归获取包括继承字段在内的所有字段
     */
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> allFields = new ArrayList<>();

        // 递归获取当前类及所有父类的字段
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            Field[] declaredFields = currentClass.getDeclaredFields();
            for (Field field : declaredFields) {
                allFields.add(field);
            }
            currentClass = currentClass.getSuperclass();
        }

        return allFields;
    }

    /**
     * 判断是否为基本类型
     */
    private boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() ||
               clazz == String.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Double.class ||
               clazz == Float.class ||
               clazz == Boolean.class ||
               clazz == Character.class ||
               clazz == Byte.class ||
               clazz == Short.class;
    }
}
