package com.midea.pam.support.mybatis.specialchar.loader;

import java.util.Map;
import java.util.Set;

/**
 * 特殊字符配置加载器接口
 * 用于解耦公共模块与业务模块的依赖关系
 * 
 * <AUTHOR> Team
 */
public interface SpecialCharacterConfigLoader {
    
    /**
     * 加载表字段配置
     * 配置加载器实现类自行决定使用哪个数据库
     *
     * @return 配置映射：tableName -> Set<fieldName>
     */
    Map<String, Set<String>> loadTableFieldConfigurations();
    
    /**
     * 检查特殊字符处理功能是否启用
     * 
     * @return true-启用，false-禁用
     */
    boolean isEnabled();
    
    /**
     * 获取配置加载器的类型标识
     * 
     * @return 配置加载器类型，如："database", "yaml", "default"
     */
    default String getLoaderType() {
        return "default";
    }
    
    /**
     * 获取配置加载器的优先级
     * 数值越小优先级越高
     * 
     * @return 优先级值
     */
    default int getPriority() {
        return Integer.MAX_VALUE;
    }
}
