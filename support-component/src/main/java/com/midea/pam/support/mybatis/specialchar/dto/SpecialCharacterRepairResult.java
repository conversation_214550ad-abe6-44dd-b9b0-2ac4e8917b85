package com.midea.pam.support.mybatis.specialchar.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 特殊字符修复结果对象
 * 

 */
public class SpecialCharacterRepairResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 处理是否成功
     */
    private Boolean success;
    
    /**
     * 处理的记录列表
     */
    private List<SpecialCharacterRepairRecord> processedRecords;
    
    /**
     * 处理记录总数
     */
    private Integer totalProcessed;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    public SpecialCharacterRepairResult() {
    }
    
    public SpecialCharacterRepairResult(Boolean success) {
        this.success = success;
    }
    
    public Boolean getSuccess() {
        return success;
    }
    
    public void setSuccess(Boolean success) {
        this.success = success;
    }
    
    public List<SpecialCharacterRepairRecord> getProcessedRecords() {
        return processedRecords;
    }
    
    public void setProcessedRecords(List<SpecialCharacterRepairRecord> processedRecords) {
        this.processedRecords = processedRecords;
    }
    
    public Integer getTotalProcessed() {
        return totalProcessed;
    }
    
    public void setTotalProcessed(Integer totalProcessed) {
        this.totalProcessed = totalProcessed;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    @Override
    public String toString() {
        return "SpecialCharacterRepairResult{" +
                "success=" + success +
                ", processedRecords=" + (processedRecords != null ? processedRecords.size() : 0) +
                ", totalProcessed=" + totalProcessed +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
