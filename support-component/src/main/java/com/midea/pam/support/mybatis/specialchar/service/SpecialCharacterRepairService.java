package com.midea.pam.support.mybatis.specialchar.service;

import com.midea.pam.support.mybatis.specialchar.dto.SpecialCharacterRepairRequest;
import com.midea.pam.support.mybatis.specialchar.dto.SpecialCharacterRepairResult;
import com.midea.pam.support.mybatis.specialchar.dto.SpecialCharacterRepairRecord;
import com.midea.pam.support.mybatis.specialchar.processor.SpecialCharacterProcessor;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 特殊字符历史数据修复服务
 * 

 */
public class SpecialCharacterRepairService {
    
    private static final Logger logger = LoggerFactory.getLogger(SpecialCharacterRepairService.class);
    
    private SqlSessionFactory sqlSessionFactory;
    private SpecialCharacterProcessor processor;
    
    public void setSqlSessionFactory(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }
    
    public void setSpecialCharacterProcessor(SpecialCharacterProcessor processor) {
        this.processor = processor;
    }
    
    /**
     * 修复历史数据中的特殊字符
     */
    public SpecialCharacterRepairResult repairHistoryData(SpecialCharacterRepairRequest request) {
        SpecialCharacterRepairResult result = new SpecialCharacterRepairResult();
        result.setSuccess(true);
        result.setProcessedRecords(new ArrayList<>());
        
        try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
            String sql = buildRepairSql(request);

            // 使用同一个连接处理所有操作，确保事务一致性
            try (java.sql.Connection connection = sqlSession.getConnection()) {
                // 查询需要修复的记录
                List<Map<String, Object>> records = executeQuery(connection, sql);

                for (Map<String, Object> record : records) {
                    SpecialCharacterRepairRecord repairRecord = processRecord(record, request, connection);
                    if (repairRecord != null) {
                        result.getProcessedRecords().add(repairRecord);
                    }
                }

                sqlSession.commit();
                result.setTotalProcessed(result.getProcessedRecords().size());
            }
            
            logger.info("特殊字符修复完成，表: {}, 字段: {}, 处理记录数: {}", 
                       request.getTableName(), request.getColumnName(), result.getTotalProcessed());
            
        } catch (Exception e) {
            logger.error("历史数据修复异常: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 构建修复SQL
     * 简化SQL逻辑，只过滤非空记录，特殊字符检测在Java中进行以确保与拦截器逻辑一致
     */
    private String buildRepairSql(SpecialCharacterRepairRequest request) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT id, ").append(request.getColumnName())
           .append(" FROM ").append(request.getTableName());

        if (StringUtils.hasText(request.getRecordId())) {
            sql.append(" WHERE id = '").append(request.getRecordId()).append("'");
        } else {
            // 简化SQL，只过滤非空记录，特殊字符检测在Java中进行
            sql.append(" WHERE ").append(request.getColumnName()).append(" IS NOT NULL");
        }

        return sql.toString();
    }
    
    /**
     * 处理单条记录
     * 使用SpecialCharacterProcessor的hasSpecialCharacters方法检测特殊字符
     */
    private SpecialCharacterRepairRecord processRecord(Map<String, Object> record,
                                                      SpecialCharacterRepairRequest request,
                                                      java.sql.Connection connection) {
        try {
            Object idValue = record.get("id");
            Object columnValue = record.get(request.getColumnName());

            if (!(columnValue instanceof String)) {
                return null;
            }

            String originalValue = (String) columnValue;

            // 使用SpecialCharacterProcessor的hasSpecialCharacters方法检测特殊字符
            if (!processor.hasSpecialCharacters(originalValue)) {
                logger.debug("记录ID: {} 字段: {} 未检测到特殊字符，跳过处理", idValue, request.getColumnName());
                return null; // 没有特殊字符，跳过
            }

            String processedValue = processor.processSpecialCharacters(originalValue);

            // 如果没有变化，跳过
            if (originalValue.equals(processedValue)) {
                logger.debug("记录ID: {} 字段: {} 处理后值未发生变化，跳过更新", idValue, request.getColumnName());
                return null;
            }
            
            // 更新数据库
            String updateSql = "UPDATE " + request.getTableName() +
                              " SET " + request.getColumnName() + " = ? WHERE id = ?";

            int updateCount = executeUpdate(connection, updateSql, processedValue, idValue);
            
            if (updateCount > 0) {
                SpecialCharacterRepairRecord repairRecord = new SpecialCharacterRepairRecord();
                repairRecord.setRecordId(String.valueOf(idValue));
                repairRecord.setColumnName(request.getColumnName());
                repairRecord.setOriginalValue(originalValue);
                repairRecord.setModifiedValue(processedValue);
                repairRecord.setSpecialCharsFound(processor.getSpecialCharacters(originalValue));
                
                return repairRecord;
            }
            
        } catch (Exception e) {
            logger.error("处理记录异常: {}", e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 检查表和字段是否存在
     */
    public boolean validateTableAndColumn(SpecialCharacterRepairRequest request) {
        try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
            // 使用原生JDBC连接执行SQL，避免依赖Mapped Statement
            String sql = "SELECT COUNT(*) as count FROM information_schema.COLUMNS " +
                        "WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = ?";

            try (java.sql.Connection connection = sqlSession.getConnection();
                 java.sql.PreparedStatement statement = connection.prepareStatement(sql)) {

                statement.setString(1, request.getDatabaseName());
                statement.setString(2, request.getTableName());
                statement.setString(3, request.getColumnName());

                try (java.sql.ResultSet resultSet = statement.executeQuery()) {
                    if (resultSet.next()) {
                        int count = resultSet.getInt("count");
                        return count > 0;
                    }
                }
            }

        } catch (Exception e) {
            logger.error("验证表和字段异常: {}", e.getMessage(), e);
        }

        return false;
    }

    /**
     * 执行查询SQL，返回结果列表
     */
    private List<Map<String, Object>> executeQuery(java.sql.Connection connection, String sql) throws Exception {
        List<Map<String, Object>> results = new ArrayList<>();

        try (java.sql.PreparedStatement statement = connection.prepareStatement(sql);
             java.sql.ResultSet resultSet = statement.executeQuery()) {

            java.sql.ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (resultSet.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnLabel(i);
                    Object value = resultSet.getObject(i);
                    row.put(columnName, value);
                }
                results.add(row);
            }
        }

        return results;
    }

    /**
     * 执行更新SQL，返回影响行数
     */
    private int executeUpdate(java.sql.Connection connection, String sql, Object... params) throws Exception {
        try (java.sql.PreparedStatement statement = connection.prepareStatement(sql)) {

            for (int i = 0; i < params.length; i++) {
                statement.setObject(i + 1, params[i]);
            }

            return statement.executeUpdate();
        }
    }
}
