package com.midea.pam.ctc.wbs.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.WbsConstraintExample;
import com.midea.pam.common.ctc.cache.WbsTemplateInfoCache;
import com.midea.pam.common.ctc.dto.WbsCustomizeRuleDto;
import com.midea.pam.common.ctc.dto.WbsDynamicFieldsDto;
import com.midea.pam.common.ctc.dto.WbsTemplateInfoDto;
import com.midea.pam.common.ctc.dto.WbsTemplateRuleDto;
import com.midea.pam.common.ctc.entity.WbsCustomizeRule;
import com.midea.pam.common.ctc.entity.WbsCustomizeRuleExample;
import com.midea.pam.common.ctc.entity.WbsTemplateInfo;
import com.midea.pam.common.ctc.entity.WbsTemplateInfoExample;
import com.midea.pam.common.ctc.vo.FeeItemFeeSettingModeDropdownBoxVO;
import com.midea.pam.common.enums.DescribeDisplayEnum;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.cache.WbsTemplateInfoCacheUtils;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.WbsCustomizeRuleMapper;
import com.midea.pam.ctc.mapper.WbsTemplateInfoExtMapper;
import com.midea.pam.ctc.mapper.WbsTemplateInfoMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleExtMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.wbs.service.WbsTemplateInfoService;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.system.SystemContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class WbsTemplateInfoServiceImpl implements WbsTemplateInfoService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private WbsTemplateInfoExtMapper wbsTemplateInfoExtMapper;
    @Resource
    private WbsTemplateInfoMapper wbsTemplateInfoMapper;
    @Resource
    private WbsTemplateRuleService wbsTemplateRuleService;
    @Resource
    private WbsTemplateRuleMapper wbsTemplateRuleMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private WbsTemplateRuleExtMapper wbsTemplateRuleExtMapper;

    @Resource
    private WbsCustomizeRuleMapper wbsCustomizeRuleMapper;


    /**
     * wbs模板信息分页查询
     *
     * @param param
     * @return
     */
    @Override
    public PageInfo<WbsTemplateInfoDto> page(WbsTemplateInfoDto param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());

        // 获取当前虚拟部门id
//        final List<Long> unitIds = basedataExtService.findUnitIds(SystemContext.getUnitId());
        List<Long> unitIds = new ArrayList<>();
        unitIds.add(SystemContext.getUnitId());
        param.setUnitIds(unitIds);

        List<WbsTemplateInfoDto> transferList = wbsTemplateInfoExtMapper.selectList(param);
        if (!CollectionUtils.isEmpty(transferList)) {
            for (WbsTemplateInfoDto wbsTemplateInfoDto : transferList) {
                // 模板规则
                wbsTemplateInfoDto.setTemplateRule(wbsTemplateRuleExtMapper.getGroupConcatRuleName(wbsTemplateInfoDto.getId()));
                // 创建人
                UserInfo userInfo = CacheDataUtils.findUserById(wbsTemplateInfoDto.getCreateBy());
                if (null != userInfo) {
                    wbsTemplateInfoDto.setCreateByCn(userInfo.getName());
                }
            }
        }
        PageInfo<WbsTemplateInfoDto> page = BeanConverter.convertPage(transferList, WbsTemplateInfoDto.class);
        return page;
    }

    /**
     * 根据id查询wbs模板信息、模板规则
     *
     * @param wbsTemplateInfoId
     * @return
     */
    @Override
    public WbsTemplateInfoDto findInfoRuleById(Long wbsTemplateInfoId) {
        WbsTemplateInfoDto result = wbsTemplateInfoExtMapper.findById(wbsTemplateInfoId);
        if (Objects.isNull(result)) {
            throw new ApplicationBizException("wbs模板信息不存在");
        }
        WbsTemplateRuleDto ruleParam = new WbsTemplateRuleDto();
        ruleParam.setWbsTemplateInfoId(wbsTemplateInfoId);
        List<WbsTemplateRuleDto> ruleDtoList = wbsTemplateRuleService.selectList(ruleParam);
        if (!CollectionUtils.isEmpty(ruleDtoList)) {
            result.setTemplateRuleList(ruleDtoList);
            // 模板规则
            result.setTemplateRule(wbsTemplateRuleExtMapper.getGroupConcatRuleName(wbsTemplateInfoId));
        }
        return result;
    }

    /**
     * wbs模板信息保存 + 模板规则保存
     *
     * @param wbsTemplateInfoDto
     * @return
     */
    @Override
    @Transactional
    public boolean save(WbsTemplateInfoDto wbsTemplateInfoDto) {
        if (StringUtils.isBlank(wbsTemplateInfoDto.getTemplateName())) {
            throw new ApplicationBizException("模板名称不能为空");
        }
        if (wbsTemplateInfoDto.getTemplateName().length() > 50) {
            throw new ApplicationBizException("模板名称长度不能大于50个字符");
        }
        if (Objects.isNull(wbsTemplateInfoDto.getTemplateEnabled())) {
            throw new ApplicationBizException("是否启用不能为空");
        }
        if (Objects.isNull(wbsTemplateInfoDto.getUnitId())) {
            throw new ApplicationBizException("虚拟部门id不能为空");
        }
        if (CollectionUtils.isEmpty(wbsTemplateInfoDto.getTemplateRuleList())) {
            throw new ApplicationBizException("模板规则不能为空");
        }
        // 新增
        if (null == wbsTemplateInfoDto.getId()) {
            wbsTemplateInfoDto.setDeletedFlag(false);
            wbsTemplateInfoDto.setVersion(1L);
            wbsTemplateInfoDto.setCreateBy(SystemContext.getUserId());
            wbsTemplateInfoDto.setCreateAt(new Date());
            wbsTemplateInfoDto.setUpdateBy(SystemContext.getUserId());
            wbsTemplateInfoDto.setUpdateAt(new Date());
            wbsTemplateInfoMapper.insert(wbsTemplateInfoDto);
            // 新增wbs模板规则
            wbsTemplateRuleService.insertBatch(wbsTemplateInfoDto.getId(), wbsTemplateInfoDto.getTemplateRuleList());
        }
        // 修改
        else {
            // 只更新部分字段
            WbsTemplateInfo wbsTemplateInfo = new WbsTemplateInfo();
            wbsTemplateInfo.setId(wbsTemplateInfoDto.getId());
            wbsTemplateInfo.setTemplateName(wbsTemplateInfoDto.getTemplateName());
            wbsTemplateInfo.setTemplateEnabled(wbsTemplateInfoDto.getTemplateEnabled());
            wbsTemplateInfo.setTemplateDescription(wbsTemplateInfoDto.getTemplateDescription());
            wbsTemplateInfo.setCreateBy(SystemContext.getUserId());
            wbsTemplateInfo.setCreateAt(new Date());
            wbsTemplateInfoMapper.updateByPrimaryKeySelective(wbsTemplateInfo);
            // 更新wbs模板规则
            wbsTemplateRuleService.updateBatch(wbsTemplateInfoDto.getTemplateRuleList());
        }
        // 更新缓存
        return saveCache(wbsTemplateInfoDto.getId());
    }

    /**
     * 费用类型下拉查询（WBS方式）
     *
     * @param param
     * @return
     */
    @Override
    public List<FeeItemFeeSettingModeDropdownBoxVO> feeItemJoinQuery(FeeItemFeeSettingModeDropdownBoxVO param) {
        param.setUnitId(SystemContext.getUnitId());
        return wbsTemplateInfoExtMapper.feeItemJoinQuery(param);
    }

    @Override
    public List<WbsTemplateInfo> getWBSTemplate(Long unitId) {
        WbsTemplateInfoExample example =new WbsTemplateInfoExample();
        WbsTemplateInfoExample.Criteria criteria = example.createCriteria();
        criteria.andUnitIdEqualTo(unitId).andDeletedFlagEqualTo(false).andTemplateEnabledEqualTo(true);
        return wbsTemplateInfoMapper.selectByExample(example);
    }

    /**
     * 更新缓存
     *
     * @param wbsTemplateInfoId wbs模板信息id
     * @return
     */
    public boolean saveCache(Long wbsTemplateInfoId) {
        WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(wbsTemplateInfoId);
        if (null != wbsTemplateInfo) {
            WbsTemplateInfoCache cache = BeanConverter.copy(wbsTemplateInfo, WbsTemplateInfoCache.class);
            return WbsTemplateInfoCacheUtils.putCache(cache);
        }
        logger.warn("更新wbsTemplateInfo缓存失败，wbsTemplateInfoId={}匹配不到模板");
        return false;
    }

    /**
     * wbs自定义描述规则新增
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WbsCustomizeRuleDto> customizeRuleAdd(WbsTemplateInfoDto dto) {
        WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(dto.getId());
        if (null == wbsTemplateInfo) {
            throw new ApplicationBizException("wbs模板信息不存在");
        }

        //先删除操作
        List<WbsCustomizeRuleDto> customizeRuleList = dto.getCustomizeRuleList();
        for (WbsCustomizeRuleDto wbsCustomizeRuleDto : customizeRuleList) {
            if (Boolean.TRUE.equals(wbsCustomizeRuleDto.getDeletedFlag()) && null != wbsCustomizeRuleDto.getId()) {
                WbsCustomizeRule wbsCustomizeRule = wbsCustomizeRuleMapper.selectByPrimaryKey(wbsCustomizeRuleDto.getId());
                if (null != wbsCustomizeRule) {
                    wbsCustomizeRule.setDeletedFlag(true);
                    wbsCustomizeRuleMapper.updateByPrimaryKey(wbsCustomizeRule);
                }
            }
        }

        //获取动态列,校验规则段数与实际动态列是否一致
        wbsTemplateRuleCheck(dto, customizeRuleList);

        //校验是否有重复数据
        List<WbsCustomizeRuleDto> checkList = customizeRuleList.stream()
                .filter(rule -> rule.getDeletedFlag() == null)
                .collect(Collectors.toList());
        for (WbsCustomizeRuleDto wbsCustomizeRuleDto : checkList) {
            String dynamicCombination = wbsCustomizeRuleDto.getDynamicCombination();
            Long wbsTemplateInfoId = dto.getId();
            WbsCustomizeRuleExample example = new WbsCustomizeRuleExample();
            WbsCustomizeRuleExample.Criteria criteria = example.createCriteria()
                    .andDynamicCombinationEqualTo(dynamicCombination)
                    .andWbsTemplateInfoIdEqualTo(wbsTemplateInfoId)
                    .andDeletedFlagEqualTo(false);
            if (wbsCustomizeRuleDto.getId() != null) {
                criteria.andIdNotEqualTo(wbsCustomizeRuleDto.getId());
            }
            List<WbsCustomizeRule> wbsCustomizeRules = wbsCustomizeRuleMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(wbsCustomizeRules)) {
                throw new ApplicationBizException(String.format("层级数据不允许重复，请重新选择，%s", dynamicCombination));
            }
        }

        String describeDisplay = dto.getDescribeDisplay();
        wbsTemplateInfo.setDescribeDisplay(describeDisplay);
        wbsTemplateInfoMapper.updateByPrimaryKey(wbsTemplateInfo);

        if (DescribeDisplayEnum.CUSTOMIZE.getCode().equals(dto.getDescribeDisplay()) && !CollectionUtils.isEmpty(customizeRuleList)) {
            for (WbsCustomizeRuleDto wbsCustomizeRuleDto : customizeRuleList) {
                if (null != wbsCustomizeRuleDto.getId() && null == wbsCustomizeRuleDto.getDeletedFlag()) {
                    WbsCustomizeRule wbsCustomizeRule = wbsCustomizeRuleMapper.selectByPrimaryKey(wbsCustomizeRuleDto.getId());
                    if (null != wbsCustomizeRule) {
                        wbsCustomizeRule.setDynamicCombination(wbsCustomizeRuleDto.getDynamicCombination());
                        wbsCustomizeRule.setDescription(wbsCustomizeRuleDto.getDescription());
                        wbsCustomizeRuleDto.setRemark(wbsCustomizeRuleDto.getRemark());
                        wbsCustomizeRuleDto.setUpdateBy(SystemContext.getUserId());
                        wbsCustomizeRuleMapper.updateByPrimaryKey(wbsCustomizeRule);
                    }
                } else if(null == wbsCustomizeRuleDto.getId()) {
                    WbsCustomizeRule newWbsCustomizeRule = new WbsCustomizeRule();
                    newWbsCustomizeRule.setWbsTemplateInfoId(dto.getId());
                    newWbsCustomizeRule.setDynamicCombination(wbsCustomizeRuleDto.getDynamicCombination());
                    newWbsCustomizeRule.setDescription(wbsCustomizeRuleDto.getDescription());
                    newWbsCustomizeRule.setRemark(wbsCustomizeRuleDto.getRemark());
                    newWbsCustomizeRule.setDeletedFlag(false);
                    wbsCustomizeRuleMapper.insert(newWbsCustomizeRule);
                }
            }
        }
        return customizeRuleList;
    }

    private void wbsTemplateRuleCheck(WbsTemplateInfoDto dto, List<WbsCustomizeRuleDto> customizeRuleList) {
        WbsTemplateRuleDto param = new WbsTemplateRuleDto();
        param.setDeletedFlag(false);
        param.setWbsTemplateInfoId(dto.getId());
        List<WbsTemplateRuleDto> wbsRuleList = wbsTemplateRuleExtMapper.selectList(param);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wbsRuleList)) {
            Collections.sort(wbsRuleList);
            List<WbsDynamicFieldsDto> resultList = new ArrayList<>();
            WbsDynamicFieldsDto dynamicFieldDto;
            for (int i = 0; i < wbsRuleList.size(); i++) {
                if (wbsRuleList.get(i).getOrderNo().equals("1")
                        && wbsRuleList.get(i).getRuleName().equals("项目")) {
                    // 动态列不需要展示项目
                    continue;
                }
                dynamicFieldDto = new WbsDynamicFieldsDto();
                dynamicFieldDto.setKey("field_" + wbsRuleList.get(i).getOrderNo());
                dynamicFieldDto.setWbsTemplateRuleId(wbsRuleList.get(i).getId());
                dynamicFieldDto.setLable(wbsRuleList.get(i).getRuleName());
                resultList.add(dynamicFieldDto);
            }

            // 校验 dynamicCombination 格式
            if (!CollectionUtils.isEmpty(customizeRuleList)) {
                for (WbsCustomizeRuleDto rule : customizeRuleList) {
                    String dynamicCombination = rule.getDynamicCombination();
                    if (dynamicCombination != null) {
                        String[] parts = dynamicCombination.split("-");
                        // 检查分段数量是否等于 resultList 的 field 数量
                        if (parts.length != resultList.size()) {
                            throw new ApplicationBizException(
                                    String.format(
                                            "动态列编码组合与模版格式不符，请检查！当前组合应为 %d 段，实际为 %d 段（%s）",
                                            resultList.size(),
                                            parts.length,
                                            dynamicCombination
                                    )
                            );
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<Map<String, Object>> customizeRuleSelect(Long wbsTemplateInfoId) {
        WbsTemplateInfoDto result = wbsTemplateInfoExtMapper.findById(wbsTemplateInfoId);
        if (Objects.isNull(result)) {
            throw new ApplicationBizException("wbs模板信息不存在");
        }
        WbsCustomizeRuleExample example = new WbsCustomizeRuleExample();
        example.createCriteria().andWbsTemplateInfoIdEqualTo(wbsTemplateInfoId).andDeletedFlagEqualTo(false);
        List<WbsCustomizeRule> wbsCustomizeRules = wbsCustomizeRuleMapper.selectByExample(example);
        List<Map<String, Object>> returnMapList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(wbsCustomizeRules)) {
            List<WbsDynamicFieldsDto> wbsDynamicFields = wbsTemplateRuleService.getWbsDynamicFields(wbsTemplateInfoId);
            // 按field_后面的数字排序动态字段
            wbsDynamicFields.sort(Comparator.comparingInt(field -> {
                String key = field.getKey();
                if (key.startsWith("field_")) {
                    try {
                        return Integer.parseInt(key.substring(6));
                    } catch (NumberFormatException e) {
                        return Integer.MAX_VALUE;
                    }
                }
                return Integer.MAX_VALUE;
            }));

            for (WbsCustomizeRule rule : wbsCustomizeRules) {
                Map<String, Object> returnMap = new LinkedHashMap<>();
                String dynamicCombination = rule.getDynamicCombination();
                if (StringUtils.isNotBlank(dynamicCombination)) {
                    String[] parts = dynamicCombination.split("-");
                    for (int i = 0; i < Math.min(parts.length, wbsDynamicFields.size()); i++) {
                        WbsDynamicFieldsDto field = wbsDynamicFields.get(i);
                        returnMap.put(field.getKey(), parts[i]);
                    }
                    returnMap.put("id",rule.getId());
                    returnMap.put("wbsTemplateInfoId", rule.getWbsTemplateInfoId());
                    returnMap.put("dynamicCombination", rule.getDynamicCombination());
                    returnMap.put("description", rule.getDescription());
                    returnMap.put("remark", rule.getRemark());
                    returnMap.put("createAt", rule.getCreateAt());
                    returnMap.put("createBy", rule.getCreateBy());
                    returnMap.put("updateAt", rule.getUpdateAt());
                    returnMap.put("updateBy", rule.getUpdateBy());
                    returnMapList.add(returnMap);
                }
            }
            return returnMapList;
        }
        return returnMapList;
    }

}
