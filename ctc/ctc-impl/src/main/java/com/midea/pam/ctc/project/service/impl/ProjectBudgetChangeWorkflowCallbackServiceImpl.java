package com.midea.pam.ctc.project.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.ProjectProfitDto;
import com.midea.pam.common.ctc.dto.WorkingHourDto;
import com.midea.pam.common.ctc.entity.CostCollection;
import com.midea.pam.common.ctc.entity.CostCollectionWorkingHour;
import com.midea.pam.common.ctc.entity.LaborCostDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanChangeRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanChangeRecordExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecordExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterial;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetTarget;
import com.midea.pam.common.ctc.entity.ProjectBudgetTargetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetTargetChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.ctc.entity.ProjectMemberChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectMemberChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectMilepostExample;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectResourceRel;
import com.midea.pam.common.ctc.entity.ProjectResourceRelExample;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.RdmWorkingHour;
import com.midea.pam.common.ctc.entity.RdmWorkingHourExample;
import com.midea.pam.common.ctc.entity.TicketTasks;
import com.midea.pam.common.ctc.entity.TicketTasksDetail;
import com.midea.pam.common.ctc.entity.TicketTasksDetailExample;
import com.midea.pam.common.ctc.entity.TicketTasksExample;
import com.midea.pam.common.ctc.entity.TicketTasksRecord;
import com.midea.pam.common.ctc.entity.TicketTasksRecordExample;
import com.midea.pam.common.ctc.entity.WorkingHour;
import com.midea.pam.common.ctc.entity.WorkingHourCostChangeDetail;
import com.midea.pam.common.ctc.entity.WorkingHourCostChangeHeader;
import com.midea.pam.common.ctc.entity.WorkingHourWriteoffBudgetChangeRel;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.DeleteFlagEnum;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.MilepostStatus;
import com.midea.pam.common.enums.ProjectChangeStatus;
import com.midea.pam.common.enums.ProjectResourceStatus;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.TicketTasksEnum;
import com.midea.pam.common.enums.WorkingHourCostChangeEnum;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.CollectionsUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.CostCollectionEnum;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.MaterialCategoryEnum;
import com.midea.pam.ctc.common.enums.ProjectMilepostAnnexType;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.CostCollectionMapper;
import com.midea.pam.ctc.mapper.CostCollectionWorkingHourMapper;
import com.midea.pam.ctc.mapper.LaborCostDetailMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanChangeRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanConfirmRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetMaterialMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetTargetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetTargetMapper;
import com.midea.pam.ctc.mapper.ProjectHistoryHeaderMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectMemberChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectResourceRelMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.RdmWorkingHourMapper;
import com.midea.pam.ctc.mapper.TicketTasksDetailExtMapper;
import com.midea.pam.ctc.mapper.TicketTasksDetailMapper;
import com.midea.pam.ctc.mapper.TicketTasksMapper;
import com.midea.pam.ctc.mapper.TicketTasksRecordMapper;
import com.midea.pam.ctc.mapper.WorkingHourCostChangeDetailMapper;
import com.midea.pam.ctc.mapper.WorkingHourExtMapper;
import com.midea.pam.ctc.mapper.WorkingHourMapper;
import com.midea.pam.ctc.mapper.WorkingHourWriteoffBudgetChangeRelMapper;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeService;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeSummaryHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeWorkflowCallbackService;
import com.midea.pam.ctc.service.DesignPlanMaterialDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.WorkingHourCostChangeHeaderService;
import com.midea.pam.ctc.share.service.event.ProjectBudgetChangeWorkflowCallbackEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-6-27
 * @description 预算变更回调
 */
public class ProjectBudgetChangeWorkflowCallbackServiceImpl extends ProjectBudgetChangeWorkflowCallbackService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectBudgetChangeWorkflowCallbackServiceImpl.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    private final static Long MAX_WAIT_TIME_SYNC = (long) 1000 * 60 * 10;//10分钟

    // 是否生成工单 与工单类型配置有关
    private final static String OR_CREATE = "orderTask";

    @Resource
    private ProjectBudgetChangeService projectBudgetChangeService;

    @Resource
    private ProjectBudgetTargetChangeHistoryMapper projectBudgetTargetChangeHistoryMapper;

    @Resource
    private ProjectBudgetTargetMapper projectBudgetTargetMapper;

    @Resource
    private WorkingHourCostChangeHeaderService workingHourCostChangeHeaderService;

    @Resource
    private LaborCostDetailMapper laborCostDetailMapper;

    @Resource
    private RdmWorkingHourMapper rdmWorkingHourMapper;

    @Resource
    private CostCollectionWorkingHourMapper costCollectionWorkingHourMapper;

    @Resource
    private WorkingHourWriteoffBudgetChangeRelMapper workingHourWriteoffBudgetChangeRelMapper;

    @Resource
    private WorkingHourCostChangeDetailMapper workingHourCostChangeDetailMapper;

    @Resource
    private WorkingHourExtMapper workingHourExtMapper;

    @Resource
    private WorkingHourMapper workingHourMapper;

    @Resource
    private CostCollectionMapper costCollectionMapper;

    @Resource
    private ProjectBudgetChangeSummaryHistoryService summaryHistoryService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private ProjectProfitService projectProfitService;

    @Resource
    private ProjectHistoryHeaderMapper projectHistoryHeaderMapper;

    @Resource
    private ProjectResourceRelMapper projectResourceRelMapper;

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectMilepostService projectMilepostService;

    @Resource
    private ProjectMemberChangeHistoryMapper projectMemberChangeHistoryMapper;

    @Resource
    private ProjectBudgetMaterialMapper projectBudgetMaterialMapper;

    @Resource
    private TicketTasksMapper ticketTasksMapper;

    @Resource
    private MilepostDesignPlanChangeRecordMapper milepostDesignPlanChangeRecordMapper;

    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;

    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;

    @Resource
    private MilepostDesignPlanConfirmRecordMapper milepostDesignPlanConfirmRecordMapper;

    @Resource
    private TicketTasksRecordMapper ticketTasksRecordMapper;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private ProjectTypeMapper projectTypeMapper;

    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;

    @Resource
    private TicketTasksDetailMapper ticketTasksDetailMapper;

    @Resource
    private DesignPlanMaterialDetailService designPlanMaterialDetailService;

    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;

    @Resource
    private ProjectBusinessService projectBusinessService;

    @Resource
    private TicketTasksDetailExtMapper ticketTasksDetailExtMapper;

    @Resource
    private WorkflowCallbackService workflowCallbackService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftSubmit(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目预算变更审批提交回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectBudgetChangeWorkflowCallback_draftSubmit_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(projectHistoryHeader, "项目预算变更审批提交审批回调 formInstanceId对应合同不存在，不处理");

                    projectHistoryHeader.setStatus(ProjectChangeStatus.APPROVALING.getCode());
                    projectHistoryHeaderMapper.updateByPrimaryKey(projectHistoryHeader);

                    // 更新项目状态为变更中
                    Project project = new Project();
                    project.setId(projectHistoryHeader.getProjectId());
                    project.setStatus(ProjectStatus.CHANGING.getCode());
                    projectService.updateByPrimaryKeySelective(project);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目预算变更审批提交回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目预算变更审批提交回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目预算变更审批提交回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void asynfromEvent(Long formInstanceId, Long createUserId, Long companyId) {
        logger.info("项目预算变更审批通过审批回调 formInstanceId:{}, createUserId:{}, companyId:{}", formInstanceId, createUserId, companyId);
        Guard.notNull(formInstanceId, "项目预算变更审批通过审批回调 formInstanceId为空，不处理");

        final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
        Guard.notNull(projectHistoryHeader, "项目预算变更审批通过审批回调 formInstanceId对应变更头表记录不存在，不处理");

        String lockName = String.format("DesignPlanDetailChangeApprovalListener.handleMdpChangePassed_%s", projectHistoryHeader.getProjectId());
        String value = String.valueOf(projectHistoryHeader.getProjectId());
        try {
            if (DistributedCASLock.lock(lockName, value, MAX_WAIT_TIME_SYNC, MAX_WAIT_TIME_SYNC * 2)) {

                projectHistoryHeader.setStatus(ProjectChangeStatus.APPROVALED.getCode());
                projectHistoryHeaderMapper.updateByPrimaryKey(projectHistoryHeader);

                final ProjectBudgetChangeSummaryHistory summaryHistory = summaryHistoryService.getByHeaderId(formInstanceId);
                final BigDecimal afterAmount = summaryHistory.getAfterAmount();

                // 更新项目状态为进行中及项目预算
                Project project = new Project();
                project.setId(projectHistoryHeader.getProjectId());
                project.setStatus(ProjectStatus.APPROVALED.getCode());
                project.setBudgetCost(afterAmount);
                project.setResourceStatus(ProjectResourceStatus.NOCHANGE.getCode());
                projectService.updateByPrimaryKeySelective(project);
                project = projectService.selectByPrimaryKey(project.getId());
                logger.info("项目预算变更更新项目信息：{}", JSONObject.toJSONString(project));

                // 更新收入成本计划
                updateProfitDetail(projectHistoryHeader.getProjectId(), afterAmount);

                // 同步变更信息至正式表
                projectBudgetChangeService.changeSuccess(formInstanceId);

                // 同步更新目标成本变更
                if (Objects.equals(Boolean.TRUE, projectHistoryHeader.getBudgetTargetFlag())) {
                    updateTargetCost(formInstanceId);
                }

                // 更新关联合同和商机报价单
                projectBusinessService.projectContractChangePass(formInstanceId);

                // 处理缩减的人天工时（针对人力外包RDM类型）--冲销成本/收入
                dealWithWorkingHourByProjectMember(formInstanceId, companyId, projectHistoryHeader.getProjectId(),
                        projectHistoryHeader.getCreateBy());

                // 同步预算信息至EMS
                if (BooleanUtils.isTrue(project.getWbsEnabled())) {
                    HandleDispatcher.route(BusinessTypeEnums.PROJECT_WBS_BUDGET_MODIFY_EMS.getCode(),
                            String.valueOf(formInstanceId), null, null, false, companyId);
                } else {
                    HandleDispatcher.route(BusinessTypeEnums.PROJECT_BUDGET_MODIFY_EMS.getCode(), String.valueOf(formInstanceId), null,
                            null, false, companyId);
                }

                logger.info("项目预算变更同步预算信息至EMS：{}, {}", BusinessTypeEnums.PROJECT_BUDGET_MODIFY_EMS.getCode(), formInstanceId);
                logger.info("项目预算变更审批通过审批回调，状态更新为审批通过");
                // 物料预算变更审批通过之后，会相应的变更工单任务中的数据，ks迭代5，工单任务生成，产品 yanyouwei addedby pengzhong addedat 20210317
                //获取已删除的节点
                ProjectBudgetMaterialExample deleteExample = new ProjectBudgetMaterialExample();
                deleteExample.createCriteria().andDeletedFlagEqualTo(Boolean.TRUE).andProjectIdEqualTo(projectHistoryHeader.getProjectId());
                List<ProjectBudgetMaterial> deleteList = projectBudgetMaterialMapper.selectByExample(deleteExample);
                if (ListUtils.isNotEmpty(deleteList)) {
                    for (ProjectBudgetMaterial pb : deleteList) {
                        TicketTasksExample example = new TicketTasksExample();
                        example.createCriteria().andProjectBudgetMaterialIdEqualTo(pb.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                        List<TicketTasks> list = ticketTasksMapper.selectByExample(example);

                        if (ListUtils.isNotEmpty(list)) {
                            for (TicketTasks tickTasks : list) {
                                if (TicketTasksEnum.PUBLISHED.code() == tickTasks.getTaskStatus()) {
                                    tickTasks.setTaskStatus(TicketTasksEnum.TERMINATION.code());
                                } else {
                                    tickTasks.setTaskStatus(TicketTasksEnum.CANCEL.code());
                                }
                                ticketTasksMapper.updateByPrimaryKeySelective(tickTasks);
                            }
                        }
                    }
                }

                //查询项目是否需要生成工单
                String isCreateTicket = this.isCreateTicketRule(project.getType());
                // 如果项目类型=仅按项目生成, 预算变更预算不转工单
                if (("1").equals(isCreateTicket)) {
                    //校验是否有里程碑
                    ProjectMilepostDto projectMilepostQuery = new ProjectMilepostDto();
                    projectMilepostQuery.setProjectId(project.getId());
                    projectMilepostQuery.setAnnexType(ProjectMilepostAnnexType.BOM.code());
                    List<ProjectMilepostDto> milepostDtos = projectMilepostService.selectList(projectMilepostQuery);

                    ProjectBudgetMaterialExample projectBudgetMaterialExample = new ProjectBudgetMaterialExample();
                    projectBudgetMaterialExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(projectHistoryHeader.getProjectId());
                    List<ProjectBudgetMaterial> projectBudgetMaterialList = projectBudgetMaterialMapper.selectByExample(projectBudgetMaterialExample);
                    if (ListUtils.isEmpty(milepostDtos)) {
                        //走预算转工单逻辑
                        for (ProjectBudgetMaterial m : projectBudgetMaterialList) {
                            //装配件 或者 项目预算层 或者 详细最顶层
                            TicketTasks tickTasks = new TicketTasks();
                            ProjectBudgetMaterial ticketTasksHearder = null;
                            if (null != m.getParentId() && -1L != m.getParentId()) {
                                for (ProjectBudgetMaterial h : projectBudgetMaterialList) {
                                    if (h.getId().equals(m.getParentId())) {
                                        ticketTasksHearder = h;
                                    }
                                }
                            } else {
                                tickTasks.setModuleCode(m.getCode());  // 模组层
                                tickTasks.setModuleName(m.getName()); // 模组层
                            }

                            tickTasks.setTheassemblyCode(m.getCode());  // 装配件编号就是PAM编码
                            tickTasks.setTheassemblyDes(m.getName()); // 装配件描述就是物料描述
                            tickTasks.setProjectBudgetMaterialId(m.getId());//预算id

                            //详细设计生成的工单，取发布操作的人员
                            tickTasks.setTicketPublishBy(m.getUpdateBy());
                            if (null != ticketTasksHearder) {
                                tickTasks.setModuleName(ticketTasksHearder.getName());//模组名 materiel_descr
                                tickTasks.setModuleCode(ticketTasksHearder.getCode());//模组号
                            }

                            tickTasks.setUpdateAt(new Date());//工单更新时间
                            tickTasks.setUpdateBy(m.getUpdateBy());
                            tickTasks.setMilepostDesignDetailId(m.getId());//预算id
                            tickTasks.setProjectId(project.getId());//项目id
                            Long unitId = CacheDataUtils.getTopUnitIdByUnitId(project.getUnitId());
                            tickTasks.setUnitId(unitId);//使用单位id

                            //tickTasks.setMaterialCategory(p.getMaterialCategory());//物料类型
                            tickTasks.setUnit(m.getUnit());//单位
                            tickTasks.setNumber(m.getNumber());

                            //单价
                            tickTasks.setUnitPrice(m.getPrice());//单价
                            tickTasks.setNumber(m.getNumber());//数量
                            tickTasks.setTicketPlanStartTime(m.getPlannedDeliveryStartDate());//开工日期
                            tickTasks.setModuleDeliveryTime(m.getPlannedDeliveryDate());//交货日期
                            //预算已删除
                            if (null != m.getDeletedFlag() && m.getDeletedFlag()) {
                                tickTasks.setTaskStatus(TicketTasksEnum.CANCEL.code());
                            }

                            //父节点
                            if (null != ticketTasksHearder) {
                                tickTasks.setParentId(ticketTasksHearder.getId());//父节点
                            }
                            tickTasks.setLastOperateTime(new Date());

                            TicketTasksExample example = new TicketTasksExample();
                            example.createCriteria().andProjectBudgetMaterialIdEqualTo(m.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                            List<TicketTasks> list = ticketTasksMapper.selectByExample(example);

                            if (ListUtils.isNotEmpty(list)) {
                                TicketTasks t = list.get(0);
                                tickTasks.setId(t.getId());
                                if (null != tickTasks.getTaskStatus() || TicketTasksEnum.CANCEL.code().equals(t.getTaskStatus()) || TicketTasksEnum.TERMINATION.code().equals(t.getTaskStatus())) {
                                    tickTasks.setTaskStatus(TicketTasksEnum.UNPUBLISH.code());
                                }
                                if (null != m.getDeletedFlag() && m.getDeletedFlag()) {
                                    //已发布
                                    if (TicketTasksEnum.PUBLISHED.code() == tickTasks.getTaskStatus()) {
                                        tickTasks.setTaskStatus(TicketTasksEnum.TERMINATION.code());
                                    } else {
                                        tickTasks.setTaskStatus(TicketTasksEnum.CANCEL.code());
                                    }
                                }

                                ticketTasksMapper.updateByPrimaryKeySelective(tickTasks);
                                // 操作记录
                                if (null == tickTasks.getUpdateBy()) {
                                    tickTasks.setUpdateBy(m.getUpdateBy());
                                }
                                updateRecord(tickTasks, formInstanceId, "物料预算变更");
                            } else {
                                Date dd = new Date();
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(dd);
                                calendar.add(Calendar.DATE, 14);
                                if (null != m.getPlannedDeliveryStartDate()) {
                                    //条件1：当前日期-工单计划开始日期>14天，取工单计划开始日期
                                    //条件2：当前日期-工单计划开始日期<14天，取当前日期+14天
                                    if (calendar.getTime().getTime() > m.getPlannedDeliveryStartDate().getTime()) {
                                        tickTasks.setModuleDeliveryTime(calendar.getTime());
                                    } else {
                                        tickTasks.setModuleDeliveryTime(m.getPlannedDeliveryStartDate());
                                    }
                                }
                                //工单做了删除操作
                                if (null != m.getDeletedFlag() && m.getDeletedFlag()) {
                                    logger.info("工单已删除：" + m.getId());
                                } else {
                                    tickTasks.setTicketTaskCode(project.getCode() + CacheDataUtils.generateSequence(4, project.getCode(), ""));
                                    tickTasks.setDeletedFlag(Boolean.FALSE);
                                    tickTasks.setCreateAt(new Date());//工单创建时间

                                    tickTasks.setTaskStatus(TicketTasksEnum.UNPUBLISH.code());//未发布
                                    //立项审批通过后
                                    if (null != project.getStatus() && ProjectStatus.APPROVALED.getCode() == project.getStatus()) {
                                        tickTasks.setTaskStatus(TicketTasksEnum.PUBLISHED.code());
                                        tickTasks.setTicketPubishTime(new Date());
                                    }

                                    ticketTasksMapper.insert(tickTasks);
                                    // 操作记录
                                    saveRecord(tickTasks, formInstanceId, "物料预算变更");
                                }

                            }

                        }
                    } else {
                        if (ListUtils.isNotEmpty(projectBudgetMaterialList)) {
                            List<Long> projectBudgetMaterialIdList =
                                    projectBudgetMaterialList.stream().map(ProjectBudgetMaterial::getId).collect(Collectors.toList());
                            logger.info("项目预算变更审批通过的projectBudgetMaterialList：{}", JsonUtils.toString(projectBudgetMaterialList));
                            MilepostDesignPlanDetailExample designPlanDetailExample = new MilepostDesignPlanDetailExample();
                            designPlanDetailExample.createCriteria().andProjectIdEqualTo(project.getId())
                                    .andProjectBudgetMaterialIdIn(projectBudgetMaterialIdList).andDeletedFlagEqualTo(false);
                            List<MilepostDesignPlanDetail> milepostDesignPlanDetailList =
                                    milepostDesignPlanDetailMapper.selectByExample(designPlanDetailExample);
                            List<MilepostDesignPlanDetailDto> list = BeanConverter.copy(milepostDesignPlanDetailList,
                                    MilepostDesignPlanDetailDto.class);
                            logger.info("项目预算变更审批通过的详设list：{}，project：{}，formInstanceId：{}",
                                    JsonUtils.toString(list), JsonUtils.toString(project), formInstanceId);
                            createTicketTasksByDesignPlanDetail(list, project, formInstanceId, "物料预算变更");


                            //模组状态都是“集成外包”的模组，且状态不等于2(通过)，需要将对应里程碑状态变更为：8
                            List<ProjectBudgetMaterial> collect = projectBudgetMaterialList.stream().filter(p -> p.getParentId() != null
                                    && (p.getExt() == null || p.getExt() == Boolean.FALSE)).collect(Collectors.toList());
                            if (ListUtils.isEmpty(collect)) {
                                List<ProjectMilepost> projectMilepostList = BeanConverter.copy(milepostDtos, ProjectMilepost.class);
                                projectMilepostList.forEach(new Consumer<ProjectMilepost>() {
                                    @Override
                                    public void accept(ProjectMilepost projectMilepost) {
                                        if (!Objects.equals(projectMilepost.getStatus(), MilepostStatus.PASSED.getCode())) {
                                            projectMilepost.setStatus(MilepostStatus.CAN_PASS.getCode()); //详细方案类型里程碑允许评审通过
                                            projectMilepostService.updateByPrimaryKeySelective(projectMilepost);
                                        }
                                    }
                                });
                            }
                        }

                        //同步更新工单任务详情装配需求总数
                        List<TicketTasksDetail> updateTicketTasksDetailList = new ArrayList<>();
                        for (ProjectMilepostDto milepostDto : milepostDtos) {
                            List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                                    CollectionsUtil.ofNullable(milepostDesignPlanDetailService.getTheFirstLevelDetailByMilepostId(milepostDto.getId()))
                                            .changeToOptional().map(s -> s.parallelStream().map(MilepostDesignPlanDetail::getId)
                                                    .map(i -> milepostDesignPlanDetailService.getDesignPlanDetailForBusinessNew(null, i, null, null))
                                                    .collect(Collectors.toList())).orElse(new ArrayList<>());

                            if (ListUtils.isNotEmpty(milepostDesignPlanDetailDtos)) {
                                for (MilepostDesignPlanDetailDto result : milepostDesignPlanDetailDtos) {
                                    if (null != result) {
                                        //统计装配需求总数
                                        countTheassemblyNumber(result, BigDecimal.ONE, updateTicketTasksDetailList);
                                    }
                                }
                            }
                        }
                        if (ListUtils.isNotEmpty(updateTicketTasksDetailList)) {
                            List<List<TicketTasksDetail>> lists = ListUtils.splistList(updateTicketTasksDetailList, 1000);
                            for (List<TicketTasksDetail> list : lists) {
                                ticketTasksDetailExtMapper.batchUpdate(list);
                            }
                        }
                    }
                }

                //【项目类型】立项后详细设计模组默认状态，设置为：已确认
                projectBusinessService.updateModuleStatusByProjectType(project);
            }
        } catch (Exception e) {
            logger.error("handlePurchaseRequirement生成采购需求加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, value);
        }
    }

    /**
     * 统计装配需求总数/剩余领料数量
     *
     * @param dto
     * @param preNumber
     */
    public void countTheassemblyNumber(MilepostDesignPlanDetailDto dto, BigDecimal preNumber, List<TicketTasksDetail> updateTicketTasksDetailList) {
        BigDecimal parentNumber = dto.getNumber() != null ? dto.getNumber().multiply(preNumber) : new BigDecimal("0");
        if (ListUtil.isPresent(dto.getSonDtos())) {
            for (MilepostDesignPlanDetailDto son : dto.getSonDtos()) {
                //根据物料类型走昆山逻辑否则走机器人原有逻辑20210511
                if (ListUtil.isPresent(son.getSonDtos()) || ("装配件".equals(son.getMaterialCategory())) || ("虚拟件".equals(son.getMaterialCategory()))) {
                    countTheassemblyNumber(son, parentNumber, updateTicketTasksDetailList);
                } else {
                    TicketTasksDetailExample example = new TicketTasksDetailExample();
                    example.createCriteria().andMilepostDesignDetailIdEqualTo(son.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                    List<TicketTasksDetail> ticketTasksDetailList = ticketTasksDetailMapper.selectByExample(example);
                    if (ListUtil.isPresent(ticketTasksDetailList)) {
                        for (TicketTasksDetail ticketTasksDetail : ticketTasksDetailList) {
                            // 装配需求总数
                            ticketTasksDetail.setTheassemblyNumber(son.getNumber().multiply(parentNumber));
                            // 剩余领料数量
                            ticketTasksDetail.setRestPickingNumber(ticketTasksDetail.getTheassemblyNumber().subtract(Optional.ofNullable(ticketTasksDetail.getApplyedNumber()).orElse(BigDecimal.ZERO)));
//                            ticketTasksDetailMapper.updateByPrimaryKeySelective(ticketTasksDetail);
                            updateTicketTasksDetailList.add(ticketTasksDetail);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void pass(ProjectDto projectDto) {
        //防止超时，异步向第三方推送数据
        applicationEventPublisher.publishEvent(new ProjectBudgetChangeWorkflowCallbackEvent(this, projectDto));
    }

    private void updateTargetCost(Long headerId) {
        ProjectBudgetTargetChangeHistoryExample example = new ProjectBudgetTargetChangeHistoryExample();
        example.createCriteria().andHistoryTypeEqualTo(HistoryType.CHANGE.getCode()).andHeaderIdEqualTo(headerId);
        List<ProjectBudgetTargetChangeHistory> changeHistoryList = projectBudgetTargetChangeHistoryMapper.selectByExample(example);
        if (PublicUtil.isNotEmpty(changeHistoryList)) {
            ProjectBudgetTargetChangeHistory changeHistory = changeHistoryList.get(0);
            ProjectBudgetTarget projectBudgetTarget = BeanConverter.copy(changeHistory, ProjectBudgetTarget.class);
            projectBudgetTarget.setId(changeHistory.getOriginId());
            projectBudgetTargetMapper.updateByPrimaryKeySelective(projectBudgetTarget);
        }
    }

    private void dealWithWorkingHourByProjectMember(Long headerId, Long companyId, Long projectId, Long createBy) {
        //新增工时变更记录表
        WorkingHourCostChangeHeader costChangeHeader = new WorkingHourCostChangeHeader();
        costChangeHeader.setCode(workingHourCostChangeHeaderService.generateCode(companyId));
        costChangeHeader.setChangeType(WorkingHourCostChangeEnum.WRITE_OFF.getCode());
        costChangeHeader.setResourceFlag(WorkingHourCostChangeEnum.BUDGET_CHANGE.getCode());
        costChangeHeader.setResourceId(headerId);
        costChangeHeader.setInnerTotalCost(BigDecimal.ZERO);
        costChangeHeader.setOuterTotalCost(BigDecimal.ZERO);
        costChangeHeader.setInnerTotalWorkingHour(BigDecimal.ZERO);
        costChangeHeader.setOuterTotalWorkingHour(BigDecimal.ZERO);
        costChangeHeader.setHistoryProjectId(projectId);
        costChangeHeader.setChangeProjectId(projectId);
        costChangeHeader.setCreateBy(createBy);
        ProjectDto project = projectService.findDetail(projectId);
        if (project != null) {
            costChangeHeader.setOuId(project.getOuId());
            costChangeHeader.setOuName(project.getOuName());
            costChangeHeader.setHistoryProjectCode(project.getCode());
            costChangeHeader.setChangeProjectCode(project.getCode());
        }
        costChangeHeader.setReason("预算变更自动冲销");
        //查询项目成员变更记录
        ProjectMemberChangeHistoryExample memberChangeHistoryExample = new ProjectMemberChangeHistoryExample();
        memberChangeHistoryExample.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        List<ProjectMemberChangeHistory> memberChangeHistoryList = projectMemberChangeHistoryMapper.selectByExample(memberChangeHistoryExample);
        if (ListUtils.isNotEmpty(memberChangeHistoryList)) {
            // 存放插入后的冲销单对应的工时成本明细数据
            List<LaborCostDetail> laborCostDetailList = new ArrayList<>();
            // 按人分组
            Map<Long, List<ProjectMemberChangeHistory>> memberChangeHistoryMap =
                    memberChangeHistoryList.stream().collect(Collectors.groupingBy(ProjectMemberChangeHistory::getUserId));
            Set<Map.Entry<Long, List<ProjectMemberChangeHistory>>> entries = memberChangeHistoryMap.entrySet();
            // 查询预算变更影响的审批通过工时明细
            for (Map.Entry<Long, List<ProjectMemberChangeHistory>> entry : entries) {
                List<ProjectMemberChangeHistory> changeHistoryList = entry.getValue();
                ProjectMemberChangeHistory changeHistory = changeHistoryList.get(0);
                if (changeHistory.getIsFromResource() == null || changeHistory.getIsFromResource() == Boolean.FALSE) {
                    // 非rdm引入无需自动冲销
                    continue;
                }
                // 查询所有审批通过的工时（RDM）
                List<WorkingHourDto> approvedWorkingHourDtoList = workingHourExtMapper.getApprovedWorkingHours(changeHistory.getProjectId(),
                        changeHistory.getUserId());
                if (ListUtils.isEmpty(approvedWorkingHourDtoList)) {
                    continue;
                }
                for (WorkingHourDto workingHourDto : approvedWorkingHourDtoList) {
                    // 循环判断工时是否在最新的投入区间
                    Boolean haveToWriteOff = checkBudgetChangeWorkingHour(changeHistoryList, workingHourDto);
                    if (haveToWriteOff) {
                        if (workingHourDto.getRdmFlag() != null && workingHourDto.getRdmFlag() == 1) {
                            // 冲销对应的工时收入
                            writeOffCarryBill(workingHourDto, headerId);
                        }
                        //如果没有被归集，直接删除
                        if (workingHourDto.getLaborCostDetailId() == null) {
                            WorkingHour deleteWorkingHour = new WorkingHour();
                            deleteWorkingHour.setId(workingHourDto.getId());
                            deleteWorkingHour.setDeleteFlag(DeleteFlagEnum.DELETED.getIntValue());
                            workingHourMapper.updateByPrimaryKeySelective(deleteWorkingHour);
                        } else {
                            if (costChangeHeader.getId() == null) {
                                costChangeHeader.setId(workingHourCostChangeHeaderService.save(costChangeHeader));
                            }
                            // 冲销对应的工时成本
                            LaborCostDetail laborCostDetail = writeOffWorkingHour(workingHourDto.getLaborCostDetailId(), headerId,
                                    costChangeHeader.getId());
                            laborCostDetailList.add(laborCostDetail);
                            BigDecimal costTotal = laborCostDetail.getCostTotal() == null ? BigDecimal.ZERO :
                                    BigDecimalUtils.getOppositeNumber(laborCostDetail.getCostTotal());
                            BigDecimal actualWorkingHours = laborCostDetail.getActualWorkingHours() == null ? BigDecimal.ZERO :
                                    BigDecimalUtils.getOppositeNumber(laborCostDetail.getActualWorkingHours());
                            //更新变更工时数
                            if ("1".equals(laborCostDetail.getUserType())) {
                                // 内部
                                costChangeHeader.setInnerTotalCost(costChangeHeader.getInnerTotalCost().add(costTotal));
                                costChangeHeader.setInnerTotalWorkingHour(costChangeHeader.getInnerTotalWorkingHour().add(actualWorkingHours));
                            } else {
                                // 外部
                                costChangeHeader.setOuterTotalCost(costChangeHeader.getOuterTotalCost().add(costTotal));
                                costChangeHeader.setOuterTotalWorkingHour(costChangeHeader.getOuterTotalWorkingHour().add(actualWorkingHours));
                            }
                        }
                    }
                }
            }
            if (costChangeHeader.getId() != null) {
                // 存在变更工时，保存记录
                workingHourCostChangeHeaderService.save(costChangeHeader);
            }
            if (CollectionUtils.isNotEmpty(laborCostDetailList)) {
                //根据结转单id进行分组
                Map<Long, List<LaborCostDetail>> costCollectionListMap =
                        laborCostDetailList.stream().collect(Collectors.groupingBy(LaborCostDetail::getCostCollectionId));

                for (Map.Entry<Long, List<LaborCostDetail>> mapEntry : costCollectionListMap.entrySet()) {
                    CostCollection costCollection = costCollectionMapper.selectByPrimaryKey(mapEntry.getKey());
                    if (!ObjectUtils.isEmpty(costCollection)) {
                        List<LaborCostDetail> laborCostDetailList1 = mapEntry.getValue();
                        BigDecimal innerLaborCost = BigDecimal.ZERO;
                        for (LaborCostDetail laborCostDetail : laborCostDetailList1) {
                            innerLaborCost = innerLaborCost.add(laborCostDetail.getCostTotal() == null ? BigDecimal.ZERO :
                                    laborCostDetail.getCostTotal());
                        }
                        costCollection.setId(null);
                        costCollection.setInnerLaborCost(innerLaborCost);
                        costCollection.setCostDate(laborCostDetailList1.get(0).getFillInDate());
                        costCollection.setCollectionDate(laborCostDetailList1.get(0).getFillInDate());
                        costCollection.setCarryStatus(0);//未结转
                        costCollection.setStatus(0);//未入账
                        costCollection.setCollectionDate(new Date());
                        costCollection.setType(CostCollectionEnum.BUDGET_TYPE.getCode());
                        costCollectionMapper.insert(costCollection);
                        logger.info("结转单costCollection插入成功id:{}", costCollection.getId());
                        //对人工成本明细和成本归集建立新的关联，需要关联新的成本归集
                        for (LaborCostDetail laborCostDetail : laborCostDetailList1) {
                            laborCostDetail.setCostCollectionId(costCollection.getId());
                            laborCostDetailMapper.updateByPrimaryKey(laborCostDetail);
                            logger.info("人工成本明细和成本归集成功建立新的关联costCollectionId:{}--laborCostDetailId{}",
                                    costCollection.getId(), laborCostDetail.getId());
                        }
                    }
                }

            }
        }
    }


    /**
     * 判断工时是否在最新的投入区间
     *
     * @param changeHistoryDtoList 变更后的最新投入区间
     * @param workingHourDto       rdm引入工时
     * @return true-需要被冲销，即不在最新的投入区间
     */
    private Boolean checkBudgetChangeWorkingHour(List<ProjectMemberChangeHistory> changeHistoryDtoList, WorkingHourDto workingHourDto) {
        for (ProjectMemberChangeHistory changeHistory : changeHistoryDtoList) {
            Date applyDate = DateUtil.parseDate(workingHourDto.getApplyDate());
            if (Objects.equals(changeHistory.getIsFromResource(), Boolean.TRUE) && !applyDate.before(changeHistory.getStartTime()) && !applyDate.after(changeHistory.getEndTime()) && !Objects.equals(changeHistory.getDeletedFlag(), Boolean.TRUE)) {
                return false;
            }
        }
        return true;
    }

    private void writeOffCarryBill(WorkingHourDto workingHourDto, Long headerId) {
        RdmWorkingHourExample rdmWorkingHourExample = new RdmWorkingHourExample();
        rdmWorkingHourExample.createCriteria().andAttDateEqualTo(DateUtils.parse(workingHourDto.getApplyDate(), DateUtils.FORMAT_SHORT)).andMipnameEqualTo(workingHourDto.getMipName());
        List<RdmWorkingHour> rdmWorkingHourList = rdmWorkingHourMapper.selectByExample(rdmWorkingHourExample);
        if (ListUtils.isNotEmpty(rdmWorkingHourList)) {
            CostCollectionWorkingHour costCollectionWorkingHour = BeanConverter.copy(rdmWorkingHourList.get(0), CostCollectionWorkingHour.class);
            costCollectionWorkingHour.setId(null);
            costCollectionWorkingHour.setProjectId(workingHourDto.getProjectId());
            costCollectionWorkingHour.setWorkingHourId(workingHourDto.getId());
            costCollectionWorkingHour.setResourceFlag(2);//来源预算变更
            costCollectionWorkingHour.setResourceId(headerId);//来源预算变更
            costCollectionWorkingHour.setReportDate(new Date());
            costCollectionWorkingHour.setConfirmHour(BigDecimalUtils.getOppositeNumber(costCollectionWorkingHour.getConfirmHour()));
            costCollectionWorkingHourMapper.insert(costCollectionWorkingHour);
        }
    }

    private LaborCostDetail writeOffWorkingHour(Long laborCostDetailId, Long headerId, Long costChangeHeaderId) {
        LaborCostDetail laborCostDetail = laborCostDetailMapper.selectByPrimaryKey(laborCostDetailId);
        if (laborCostDetail == null) {
            return null;
        }
        Long workingHourId = laborCostDetail.getWorkingHourId();

        //填报工时
        BigDecimal actualWorkingHours = laborCostDetail.getActualWorkingHours() == null ? BigDecimal.ZERO : laborCostDetail.getActualWorkingHours();
        //人工成本
        BigDecimal costTotal = laborCostDetail.getCostTotal() == null ? BigDecimal.ZERO : laborCostDetail.getCostTotal();
        //人工费率
        BigDecimal costMoney = laborCostDetail.getCostMoney() == null ? BigDecimal.ZERO : laborCostDetail.getCostMoney();
        //生成冲销labor_cost_detail，cost_collection以及失效working_hour同时在working_hour中增加冲销标识
        laborCostDetail.setId(null);
        laborCostDetail.setWorkingHourAccountingId(null);
        laborCostDetail.setAccountingFlag(0);
        laborCostDetail.setFillInDate(new Date());
        laborCostDetail.setActualWorkingHours(actualWorkingHours.multiply(BigDecimal.valueOf(-1l)));
        laborCostDetail.setCostTotal(costTotal.multiply(BigDecimal.valueOf(-1l)));
        laborCostDetail.setCostMoney(costMoney);
        laborCostDetail.setStatus(0);//未入账
        laborCostDetailMapper.insert(laborCostDetail);
        logger.info("工时成本明细laborCostDetail插入成功id:{}", laborCostDetail.getId());
        if (workingHourId != null) {
            WorkingHour workingHour = workingHourMapper.selectByPrimaryKey(workingHourId);
            if (!ObjectUtils.isEmpty(workingHour)) {
                workingHour.setDeleteFlag(1);
                workingHour.setWriteOffStatus(1);
                workingHourMapper.updateByPrimaryKeySelective(workingHour);
                logger.info("工时workingHour更新成功id:{}", workingHour.getId());
            }
        }
        //增加冲销关联关系
        WorkingHourWriteoffBudgetChangeRel budgetChangeRel = new WorkingHourWriteoffBudgetChangeRel();
        budgetChangeRel.setBudgetChangeHistoryId(headerId);
        budgetChangeRel.setLaborCostDetailId(laborCostDetail.getId());
        budgetChangeRel.setOriginLaborCostDetailId(laborCostDetailId);
        budgetChangeRel.setDeletedFlag(Boolean.FALSE);
        workingHourWriteoffBudgetChangeRelMapper.insert(budgetChangeRel);
        //新增工时变更明细
        WorkingHourCostChangeDetail costChangeDetail = new WorkingHourCostChangeDetail();
        costChangeDetail.setHeaderId(costChangeHeaderId);
        costChangeDetail.setOriginLaborCostDetailId(laborCostDetailId);
        costChangeDetail.setLaborCostDetailId(laborCostDetail.getId());
        costChangeDetail.setDeletedFlag(Boolean.FALSE);
        workingHourCostChangeDetailMapper.insert(costChangeDetail);
        return laborCostDetail;
    }

    private void updateProfitDetail(Long projectId, BigDecimal afterAmount) {
        // 更新收入成本计划
        final ProjectProfitDto profitDetail = projectProfitService.findProfitDetail(projectId);
        if (profitDetail != null && profitDetail.getId() != null) {
            final BigDecimal mainBudget = profitDetail.getMainBudget();

            final Long profitId = profitDetail.getId();
            ProjectProfit projectProfit = new ProjectProfit();
            projectProfit.setId(profitId);
            projectProfit.setMainBudget(afterAmount);
            projectProfitService.updateByPrimaryKeySelective(projectProfit);
            logger.info("项目预算变更更新收入成本计划信息：{}", JSONObject.toJSONString(projectProfit));

            if (mainBudget.compareTo(afterAmount) != 0) {
                logger.info("项目预算变更更新收入成本计划明细");
                final ProjectMilepostExample milepostExample = new ProjectMilepostExample();
                milepostExample.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(false).andHelpFlagEqualTo(Boolean.FALSE);
                final List<ProjectMilepost> mileposts = projectMilepostService.selectByExample(milepostExample);
                if (ListUtils.isNotEmpty(mileposts)) {
                    BigDecimal hundred = new BigDecimal("100");
                    mileposts.forEach(milepost -> {
                        final BigDecimal costRatio = milepost.getCostRatio() == null ? BigDecimal.ZERO : milepost.getCostRatio();
                        final BigDecimal cost = milepost.getCost() == null ? BigDecimal.ZERO : milepost.getCost();

                        final BigDecimal newCost = afterAmount.multiply(costRatio).divide(hundred);
                        milepost.setCost(newCost);
                        projectMilepostService.updateByPrimaryKeySelective(milepost);
                        logger.info("项目预算变更更新收入成本计划明细，{}-{}，{} 变更为 {}", milepost.getId(), milepost.getName(), cost, newCost);
                    });
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refuse(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目预算变更审批驳回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectBudgetChangeWorkflowCallback_refuse_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(projectHistoryHeader, "项目预算变更驳回回调 formInstanceId对应不存在，不处理");

                    projectHistoryHeader.setStatus(ProjectChangeStatus.REFUSE.getCode());
                    projectHistoryHeaderMapper.updateByPrimaryKey(projectHistoryHeader);

                    //更新资源计划项目的关联关系
                    updateProjectResourceRel(projectHistoryHeader);

                    // 更新项目状态为进行中
                    Project project = new Project();
                    project.setId(projectHistoryHeader.getProjectId());
                    project.setStatus(ProjectStatus.APPROVALED.getCode());
                    projectService.updateByPrimaryKeySelective(project);

                    ProjectMemberChangeHistoryExample example = new ProjectMemberChangeHistoryExample();
                    example.createCriteria().andHeaderIdEqualTo(formInstanceId);
                    List<ProjectMemberChangeHistory> histories = projectMemberChangeHistoryMapper.selectByExample(example);
                    if (ListUtils.isNotEmpty(histories)) {
                        histories.forEach(changeHistory -> {
                            changeHistory.setDeletedFlag(Boolean.TRUE);
                            projectMemberChangeHistoryMapper.updateByPrimaryKeySelective(changeHistory);
                        });
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目预算变更审批驳回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目预算变更审批驳回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目预算变更审批驳回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    public void updateProjectResourceRel(ProjectHistoryHeader projectHistoryHeader) {
        Project project = projectService.selectByPrimaryKey(projectHistoryHeader.getProjectId());
        if (project == null || StringUtils.isEmpty(project.getResourceCode())) {
            return;
        }
        ProjectMemberChangeHistoryExample example = new ProjectMemberChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(projectHistoryHeader.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        final List<ProjectMemberChangeHistory> projectMemberChangeHistories = projectMemberChangeHistoryMapper.selectByExample(example);

        //更新资源计划项目的关联关系
        if (ListUtils.isNotEmpty(projectMemberChangeHistories)) {
            //根据项目删除资源计划关联关系
            deletedProjectResourceRelByProject(projectHistoryHeader.getProjectId());
            for (ProjectMemberChangeHistory change : projectMemberChangeHistories) {
                //恢复变更前的项目资源计划关联关系
                if (Objects.equals(HistoryType.HISTORY.getCode(), change.getHistoryType()) && change.getIsFromResource() != null && change.getIsFromResource()) {
                    ProjectResourceRel projectResourceRel = new ProjectResourceRel();
                    projectResourceRel.setProjectId(projectHistoryHeader.getProjectId());
                    projectResourceRel.setResourceCode(project.getResourceCode());
                    projectResourceRel.setDeletedFlag(Boolean.FALSE);
                    projectResourceRel.setMipname(change.getMipname());
                    projectResourceRel.setStartTime(change.getStartTime());
                    projectResourceRel.setEndTime(change.getEndTime());
                    projectResourceRelMapper.insertSelective(projectResourceRel);
                }
            }
        }
    }

    private void deletedProjectResourceRelByProject(Long projectId) {
        if (projectId == null) {
            return;
        }
        ProjectResourceRelExample resourceRelExample = new ProjectResourceRelExample();
        resourceRelExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(projectId);
        List<ProjectResourceRel> resourceRels = projectResourceRelMapper.selectByExample(resourceRelExample);
        if (ListUtils.isNotEmpty(resourceRels)) {
            for (ProjectResourceRel resourceRel : resourceRels) {
                projectResourceRelMapper.deleteByPrimaryKey(resourceRel.getId());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void abandon(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目预算变更审批作废回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectBudgetChangeWorkflowCallback_abandon_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(projectHistoryHeader, "项目预算变更作废回调 formInstanceId对应不存在，不处理");

                    //提交后直接移动端作废
                    if (Objects.equals(projectHistoryHeader.getStatus(), ProjectChangeStatus.APPROVALING.getCode())) {
                        // 更新项目状态为进行中
                        Project project = new Project();
                        project.setId(projectHistoryHeader.getProjectId());
                        project.setStatus(ProjectStatus.APPROVALED.getCode());
                        projectService.updateByPrimaryKeySelective(project);
                    }

                    projectHistoryHeader.setStatus(ProjectChangeStatus.ABANDON.getCode());
                    projectHistoryHeaderMapper.updateByPrimaryKey(projectHistoryHeader);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目预算变更审批作废回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目预算变更审批作废回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目预算变更审批作废回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftReturn(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目预算变更审批撤回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectBudgetChangeWorkflowCallback_draftReturn_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(projectHistoryHeader, "项目预算变更撤回回调 formInstanceId对应不存在，不处理");

                    projectHistoryHeader.setStatus(ProjectChangeStatus.RETURN.getCode());
                    projectHistoryHeaderMapper.updateByPrimaryKey(projectHistoryHeader);

                    // 更新项目状态为进行中
                    Project project = new Project();
                    project.setId(projectHistoryHeader.getProjectId());
                    project.setStatus(ProjectStatus.APPROVALED.getCode());
                    projectService.updateByPrimaryKeySelective(project);

                    //更新资源计划项目的关联关系
                    updateProjectResourceRel(projectHistoryHeader);

                    ProjectMemberChangeHistoryExample example = new ProjectMemberChangeHistoryExample();
                    example.createCriteria().andHeaderIdEqualTo(formInstanceId);
                    List<ProjectMemberChangeHistory> histories = projectMemberChangeHistoryMapper.selectByExample(example);
                    if (ListUtils.isNotEmpty(histories)) {
                        histories.forEach(changeHistory -> {
                            changeHistory.setDeletedFlag(Boolean.TRUE);
                            projectMemberChangeHistoryMapper.updateByPrimaryKeySelective(changeHistory);
                        });
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目预算变更审批撤回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目预算变更审批撤回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目预算变更审批撤回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目预算变更审批删除回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectBudgetChangeWorkflowCallback_delete_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(projectHistoryHeader, "项目预算变更删除回调 formInstanceId对应不存在，不处理");

                    projectHistoryHeader.setDeletedFlag(true);
                    projectHistoryHeaderMapper.updateByPrimaryKey(projectHistoryHeader);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目预算变更审批删除回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目预算变更审批删除回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目预算变更审批删除回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    public void updateRecord(TicketTasks t, Long resource, String lookAt) {
        Long userId = SystemContext.getUserId();
        TicketTasksRecord ticketTasksRecord = new TicketTasksRecord();
        ticketTasksRecord.setCreateAt(new Date());
        ticketTasksRecord.setTicketTasksId(t.getId());

        ticketTasksRecord.setUpdateAt(new Date());
        if (null != userId && -1L != userId) {
            ticketTasksRecord.setCreateBy(userId);
            ticketTasksRecord.setUpdateBy(userId);
        } else {
            ticketTasksRecord.setCreateBy(t.getUpdateBy());
            ticketTasksRecord.setUpdateBy(t.getUpdateBy());
        }

        ticketTasksRecord.setProjectId(t.getProjectId());
        if (null != resource) {
            ticketTasksRecord.setResource(resource.toString());
        }
        if (StringUtils.isNotEmpty(lookAt)) {
            setTicketTasksRecordType(lookAt, ticketTasksRecord);//类型设置
            ticketTasksRecord.setLookAt(lookAt);
            insertTicketTasksRecord(ticketTasksRecord);
        }
    }

    public void setTicketTasksRecordType(String lookAt, TicketTasksRecord ticketTasksRecord) {
        if ("立项变更".equals(lookAt)) {
            ticketTasksRecord.setOperationType(TicketTasksEnum.OPERATION_GEN.code());
            ticketTasksRecord.setOperationTypeName(TicketTasksEnum.OPERATION_GEN.msg());
        }
        if ("详细设计变更".equals(lookAt)) {
            ticketTasksRecord.setOperationType(TicketTasksEnum.OPERATION_CHANGE.code());
            ticketTasksRecord.setOperationTypeName(TicketTasksEnum.OPERATION_CHANGE.msg());
            //
            if (StringUtils.isNotEmpty(ticketTasksRecord.getResource())) {
                MilepostDesignPlanChangeRecordExample example = new MilepostDesignPlanChangeRecordExample();
                example.createCriteria().andIdEqualTo(Long.parseLong(ticketTasksRecord.getResource()));
                List<MilepostDesignPlanChangeRecord> list = milepostDesignPlanChangeRecordMapper.selectByExample(example);
                if (ListUtils.isNotEmpty(list)) {
                    MilepostDesignPlanChangeRecord cr = list.get(0);
                    if (null != cr.getCreateBy()) {
                        ticketTasksRecord.setUpdateBy(cr.getCreateBy());
                    }
                }
            }
        }
        if ("进度确认变更".equals(lookAt)) {
            ticketTasksRecord.setOperationType(TicketTasksEnum.PROGRESSCONFIRMED.code());
            ticketTasksRecord.setOperationTypeName(TicketTasksEnum.PROGRESSCONFIRMED.msg());
            //
            if (StringUtils.isNotEmpty(ticketTasksRecord.getResource())) {
                MilepostDesignPlanConfirmRecordExample example = new MilepostDesignPlanConfirmRecordExample();
                example.createCriteria().andIdEqualTo(Long.parseLong(ticketTasksRecord.getResource()));
                List<MilepostDesignPlanConfirmRecord> list = milepostDesignPlanConfirmRecordMapper.selectByExample(example);
                if (ListUtils.isNotEmpty(list)) {
                    MilepostDesignPlanConfirmRecord cr = list.get(0);
                    if (null != cr.getCreateBy()) {
                        ticketTasksRecord.setUpdateBy(cr.getCreateBy());
                    }
                }
            }
        }
        if ("发布".equals(lookAt)) {
            ticketTasksRecord.setOperationType(TicketTasksEnum.OPERATION_PUB.code());
            ticketTasksRecord.setOperationTypeName(TicketTasksEnum.OPERATION_PUB.msg());
        }
        if ("工时导入".equals(lookAt)) {
            ticketTasksRecord.setOperationType(TicketTasksEnum.OPERATION_IMPORT.code());
            ticketTasksRecord.setOperationTypeName(TicketTasksEnum.OPERATION_IMPORT.msg());
        }
        if ("领料申请".equals(lookAt)) {
            ticketTasksRecord.setOperationType(TicketTasksEnum.OPERATION_MATERIAL_REQUISITION.code());
            ticketTasksRecord.setOperationTypeName(TicketTasksEnum.OPERATION_MATERIAL_REQUISITION.msg());
        }
        if ("物料预算变更".equals(lookAt)) {
            ticketTasksRecord.setOperationType(TicketTasksEnum.BUDGET_CHANGE.code());
            ticketTasksRecord.setOperationTypeName(TicketTasksEnum.BUDGET_CHANGE.msg());
        }
    }

    public int insertTicketTasksRecord(TicketTasksRecord ticketTasksRecord) {
        if (null != ticketTasksRecord.getUpdateBy()) {
            UserInfo userInfo = CacheDataUtils.findUserById(ticketTasksRecord.getUpdateBy());
            if (null != userInfo) {
                ticketTasksRecord.setAttribute1(userInfo.getName());
            }
        }
        //判斷記錄是否已存在存在則更新否則插入
        TicketTasksRecordExample example = new TicketTasksRecordExample();
        if (null != ticketTasksRecord.getResource()) {
            example.createCriteria().andLookAtEqualTo(ticketTasksRecord.getLookAt()).andResourceEqualTo(ticketTasksRecord.getResource());
        } else {
            example.createCriteria().andLookAtEqualTo(ticketTasksRecord.getLookAt());
        }
        List<TicketTasksRecord> list = ticketTasksRecordMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(list)) {
            ticketTasksRecord.setId(list.get(0).getId());
            return ticketTasksRecordMapper.updateByPrimaryKey(ticketTasksRecord);
        } else {
            return ticketTasksRecordMapper.insert(ticketTasksRecord);
        }

    }

    public void saveRecord(TicketTasks t, Long resource, String lookAt) {
        Long userId = SystemContext.getUserId();
        TicketTasksRecord ticketTasksRecord = new TicketTasksRecord();
        ticketTasksRecord.setCreateAt(new Date());
        ticketTasksRecord.setTicketTasksId(t.getId());
        ticketTasksRecord.setCreateBy(userId);

        ticketTasksRecord.setUpdateAt(new Date());
        if (null != userId && -1L != userId) {
            ticketTasksRecord.setUpdateBy(userId);
        } else {
            ticketTasksRecord.setCreateBy(t.getUpdateBy());
            ticketTasksRecord.setUpdateBy(t.getUpdateBy());
        }
        ticketTasksRecord.setProjectId(t.getProjectId());
        if (null != resource) {
            ticketTasksRecord.setResource(resource.toString());
        }
        if (StringUtils.isNotEmpty(lookAt)) {
            setTicketTasksRecordType(lookAt, ticketTasksRecord);//类型设置
            ticketTasksRecord.setLookAt(lookAt);
        }
        insertTicketTasksRecord(ticketTasksRecord);
    }

    //根据详细生成工单任务
    public void createTicketTasksByDesignPlanDetail(List<MilepostDesignPlanDetailDto> plan, Project project, Long resource, String lookAt) {
        for (MilepostDesignPlanDetailDto p : plan) {
            //没有项目信息则查询项目信息,保持尽量只查一次满足全局使用
            if (null == project) {
                project = projectMapper.selectByPrimaryKey(p.getProjectId());
                //如果项目为空则是来自详细设计推送(状态不用已发布)
                //project.setPreviewFlag(false);
                project.setStatus(null);
            }
            //查询项目是否需要生成工单
            String isCreateTicket = this.isCreateTicketRule(project.getType());
            //项目需要生成工单 且 是否已进度确认
            if (("1").equals(isCreateTicket) && (CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code().equals(p.getStatus())
                    || CheckStatus.MDP_CHANGE_PASSED.code().equals(p.getStatus()) || CheckStatus.PASS.code().equals(p.getStatus()))) {
                setTicketTasksByDesignPlanDetail(p, p.getSonDtos(), project, null, null, resource, lookAt);
            } else if (null != p.getDeletedFlag() && p.getDeletedFlag()) {
                //详设删除
                setTicketTasksByDesignPlanDetail(p, p.getSonDtos(), project, null, null, resource, lookAt);
            }

        }
    }

    /**
     * 通过项目类型去找项目工单任务类型配置 是否生成工单任务
     *
     * @param projectTypeId
     * @return
     */
    public String isCreateTicketRule(Long projectTypeId) {
        String orCreate = "";
        if (null == projectTypeId) {
            throw new MipException("该项目没有项目类型");
        }
        final ProjectType projectType = projectTypeMapper.selectByPrimaryKey(projectTypeId);
        if (null == projectType) {
            throw new MipException("找不到该项目项目类型");
        }
        final String workOrderTaskConfig = projectType.getWorkOrderTaskConfig();
        if (StringUtils.isNotEmpty(workOrderTaskConfig)) {

            try {
                final JSONObject jsonObject = JSONObject.parseObject(workOrderTaskConfig);
                if (jsonObject.isEmpty()) {
                    throw new MipException("该项目类型的工单任务类型配置没有配");
                }
                for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                    if (entry.getKey().equals(OR_CREATE)) {
                        orCreate = entry.getValue().toString();
                    }
                }
            } catch (Exception e) {
                throw new MipException("项目类型工单任务类型配置的参数有问题");
            }
        }
        return orCreate;
    }

    public void setTicketTasksByDesignPlanDetail(MilepostDesignPlanDetailDto p, List<MilepostDesignPlanDetailDto> plan, Project project,
                                                 TicketTasks ticketTasksHearder, MilepostDesignPlanDetailDto model, Long resource, String lookAt) {
        logger.info("生成工单：" + p.getId());
        //模组层
        if (null != p.getProjectBudgetMaterialId() && null != p.getWhetherModel() && p.getWhetherModel()) {
            model = p;
        } else if (null == model && p.getParentId() != null) {
            //反推模组
            model = getModel(p);
        }
        //父节点
        if (null == ticketTasksHearder) {
            //详细父节点
            if (null != p.getParentId()) {
                TicketTasksExample example = new TicketTasksExample();
                example.createCriteria().andMilepostDesignDetailIdEqualTo(p.getParentId());
                List<TicketTasks> ticketTasksHearders = ticketTasksMapper.selectByExample(example);
                if (ListUtils.isNotEmpty(ticketTasksHearders)) {
                    ticketTasksHearder = ticketTasksHearders.get(0);

                }
            } else {
                //数据异常
            }
        }
        //详细设计已删除

        if (null != p.getDeletedFlag() && p.getDeletedFlag()) {
            //工单状态
            TicketTasksExample example = new TicketTasksExample();
            example.createCriteria().andMilepostDesignDetailIdEqualTo(p.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            List<TicketTasks> list = ticketTasksMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(list)) {
                TicketTasks tickTasks = list.get(0);
                //已发布
                if (TicketTasksEnum.PUBLISHED.code() == tickTasks.getTaskStatus()) {
                    tickTasks.setTaskStatus(TicketTasksEnum.TERMINATION.code());
                } else {
                    tickTasks.setTaskStatus(TicketTasksEnum.CANCEL.code());
                }
                tickTasks.setDeletedFlag(true);
                ticketTasksMapper.updateByPrimaryKeySelective(tickTasks);
            }
            //外购物料
            TicketTasksDetailExample detailExample = new TicketTasksDetailExample();
            detailExample.createCriteria().andMilepostDesignDetailIdEqualTo(p.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            List<TicketTasksDetail> ticketTasksDetailList = ticketTasksDetailMapper.selectByExample(detailExample);
            if (ListUtils.isNotEmpty(ticketTasksDetailList)) {
                for (TicketTasksDetail td : ticketTasksDetailList) {
                    td.setDeletedFlag(true);
                    ticketTasksDetailMapper.updateByPrimaryKey(td);
                }
            }

            //虚拟件
        } else if (MaterialCategoryEnum.VIRTUAL_PARTS.msg().equals(p.getMaterialCategory())) {
            TicketTasksExample example = new TicketTasksExample();
            example.createCriteria().andMilepostDesignDetailIdEqualTo(p.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            List<TicketTasks> list = ticketTasksMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(list)) {
                TicketTasks tickTasks = list.get(0);
                tickTasks.setMaterialCategory(MaterialCategoryEnum.VIRTUAL_PARTS.msg());
                //已发布
                if (TicketTasksEnum.PUBLISHED.code() == tickTasks.getTaskStatus()) {
                    //tickTasks.setTaskStatus(TicketTasksEnum.TERMINATION.code());
                } else {
                    tickTasks.setTaskStatus(TicketTasksEnum.CANCEL.code());
                }
                ticketTasksMapper.updateByPrimaryKeySelective(tickTasks);
            }
            //物料明细往上推
            if (MaterialCategoryEnum.VIRTUAL_PARTS.msg().equals(p.getMaterialCategory())) {
                TicketTasksDetailExample detailExample = new TicketTasksDetailExample();
                detailExample.createCriteria().andMilepostDesignDetailIdEqualTo(p.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<TicketTasksDetail> ticketTasksDetailList = ticketTasksDetailMapper.selectByExample(detailExample);
                for (TicketTasksDetail t : ticketTasksDetailList) {
                    t.setTicketTasksId(ticketTasksHearder.getId());
                    ticketTasksDetailMapper.updateByPrimaryKeySelective(t);
                }
            }
        } else if (MaterialCategoryEnum.ASSEMBLY_PARTS.msg().equals(p.getMaterialCategory()) || (null != p.getProjectBudgetMaterialId() && null != p.getWhetherModel() && p.getWhetherModel()) || (-1 == p.getParentId())) {
            //装配件 或者 项目预算层 或者 详细最顶层
            TicketTasks tickTasks = new TicketTasks();

            tickTasks.setTheassemblyCode(p.getPamCode());  // 装配件编号就是PAM编码
            tickTasks.setTheassemblyDes(p.getMaterielDescr()); // 装配件描述就是物料描述
            tickTasks.setProjectBudgetMaterialId(p.getProjectBudgetMaterialId());//预算id

            //详细设计生成的工单，取发布操作的人员
            tickTasks.setTicketPublishBy(p.getUpdateBy());
            if (null != model) {
                tickTasks.setModuleName(model.getMaterielDescr());//模组名 materiel_descr
                tickTasks.setModuleCode(model.getPamCode());//模组号
            }

            tickTasks.setUpdateAt(new Date());//工单更新时间
            tickTasks.setUpdateBy(p.getUpdateBy());
            tickTasks.setMilepostDesignDetailId(p.getId());//详细设计id
            tickTasks.setProjectId(project.getId());//项目id
            Long unitId = CacheDataUtils.getTopUnitIdByUnitId(project.getUnitId());
            tickTasks.setUnitId(unitId);//使用单位id

            tickTasks.setMaterialCategory(p.getMaterialCategory());//物料类型
            tickTasks.setUnit(p.getUnit());//单位
            tickTasks.setNumber(p.getNumber().intValue());
            //装配需求总数
            BigDecimal number = designPlanMaterialDetailService.selectDetailNumber(p.getId());
            if (null != number) {
                tickTasks.setTheassemblyNumber(number);
            }
            //单价
            if (null != p.getProjectBudgetMaterialId()) {
                ProjectBudgetMaterialExample projectBudgetMaterialExample = new ProjectBudgetMaterialExample();
                projectBudgetMaterialExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId()).andIdEqualTo(p.getProjectBudgetMaterialId());
                List<ProjectBudgetMaterial> projectBudgetMaterialList = projectBudgetMaterialMapper.selectByExample(projectBudgetMaterialExample);
                if (ListUtils.isNotEmpty(projectBudgetMaterialList)) {
                    ProjectBudgetMaterial projectBudgetMaterial = projectBudgetMaterialList.get(0);
                    tickTasks.setUnitPrice(projectBudgetMaterial.getPrice());//单价
                    tickTasks.setNumber(projectBudgetMaterial.getNumber());//数量
                    tickTasks.setTicketPlanStartTime(projectBudgetMaterial.getPlannedDeliveryStartDate());//开工日期
                    tickTasks.setModuleDeliveryTime(projectBudgetMaterial.getPlannedDeliveryDate());//交货日期
                    //预算已删除
                    if (null != projectBudgetMaterial.getDeletedFlag() && projectBudgetMaterial.getDeletedFlag()) {
                        tickTasks.setTaskStatus(TicketTasksEnum.TERMINATION.code());
                    }
                    //发布状态?疑问点
                    //tickTasks.setTaskStatus(TicketTasksEnum.PUBLISHED.code());
                }
            }

            //详细设计做了删除操作
            if (null != p.getDeletedFlag() && p.getDeletedFlag()) {
                tickTasks.setTaskStatus(TicketTasksEnum.CANCEL.code());
            }

            //父节点
            if (null != ticketTasksHearder) {
                tickTasks.setParentId(ticketTasksHearder.getId());//父节点
                //父节点是否被删除
                if (null != ticketTasksHearder.getTaskStatus()) {
                    //已发布
                    if (TicketTasksEnum.PUBLISHED.code() == ticketTasksHearder.getTaskStatus()) {
                        //tickTasks.setTaskStatus(TicketTasksEnum.PUBLISHED.code());
                    } else if (TicketTasksEnum.CANCEL.code() == ticketTasksHearder.getTaskStatus()) {
                        tickTasks.setTaskStatus(TicketTasksEnum.CANCEL.code());
                    } else if (TicketTasksEnum.TERMINATION.code() == ticketTasksHearder.getTaskStatus()) {
                        tickTasks.setTaskStatus(TicketTasksEnum.TERMINATION.code());
                    }
                }
            }
            tickTasks.setLastOperateTime(new Date());


            TicketTasksExample example = new TicketTasksExample();
            example.createCriteria().andMilepostDesignDetailIdEqualTo(p.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            List<TicketTasks> list = ticketTasksMapper.selectByExample(example);

            if (ListUtils.isNotEmpty(list)) {
                TicketTasks t = list.get(0);
                tickTasks.setId(t.getId());
                if (null != tickTasks.getTaskStatus() || TicketTasksEnum.CANCEL.code().equals(t.getTaskStatus()) || TicketTasksEnum.TERMINATION.code().equals(t.getTaskStatus())) {
                    tickTasks.setTaskStatus(TicketTasksEnum.UNPUBLISH.code());
                }
                if (null != p.getDeletedFlag() && p.getDeletedFlag()) {
                    //已发布
                    if (TicketTasksEnum.PUBLISHED.code() == tickTasks.getTaskStatus()) {
                        tickTasks.setTaskStatus(TicketTasksEnum.TERMINATION.code());
                    } else {
                        tickTasks.setTaskStatus(TicketTasksEnum.CANCEL.code());
                    }
                }
                if (null != project.getStatus() && ProjectStatus.APPROVALED.getCode() == project.getStatus()) {
                    tickTasks.setTaskStatus(TicketTasksEnum.PUBLISHED.code());
                    tickTasks.setTicketPubishTime(new Date());
                }
                if (null != t.getTicketPlanStartTime() && ("详细设计变更".equals(lookAt) || "工时导入".equals(lookAt))) {
                    tickTasks.setTicketPlanStartTime(t.getTicketPlanStartTime());
                }
                ticketTasksMapper.updateByPrimaryKeySelective(tickTasks);
                // 操作记录
                if (null == tickTasks.getUpdateBy()) {
                    tickTasks.setUpdateBy(p.getUpdateBy());
                }
                updateRecord(tickTasks, resource, lookAt);
            } else {
                Date dd = new Date();
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(dd);
                calendar.add(Calendar.DATE, 14);
                if (null != p.getDeliveryTime()) {
                    //条件1：当前日期-工单计划开始日期>14天，取工单计划开始日期
                    //条件2：当前日期-工单计划开始日期<14天，取当前日期+14天
                    if (calendar.getTime().getTime() > p.getDeliveryTime().getTime()) {
                        tickTasks.setModuleDeliveryTime(calendar.getTime());
                    } else {
                        tickTasks.setModuleDeliveryTime(p.getDeliveryTime());
                    }
                }
                //工单做了删除操作
                if (null != p.getDeletedFlag() && p.getDeletedFlag()) {
                    logger.info("工单已删除：" + p.getId());
                } else {
                    tickTasks.setTicketTaskCode(project.getCode() + CacheDataUtils.generateSequence(4, project.getCode(), ""));
                    tickTasks.setDeletedFlag(Boolean.FALSE);
                    tickTasks.setCreateAt(new Date());//工单创建时间

                    tickTasks.setTaskStatus(TicketTasksEnum.UNPUBLISH.code());//未发布
                    //立项审批通过后
                    if (null != project.getStatus() && ProjectStatus.APPROVALED.getCode() == project.getStatus()) {
                        tickTasks.setTaskStatus(TicketTasksEnum.PUBLISHED.code());
                        tickTasks.setTicketPubishTime(new Date());
                    }

                    ticketTasksMapper.insert(tickTasks);
                    // 操作记录

                    saveRecord(tickTasks, resource, lookAt);
                }

            }

            ticketTasksHearder = tickTasks;
            //采购层出现在模组层
            TicketTasksDetailExample detailExample = new TicketTasksDetailExample();
            detailExample.createCriteria().andMilepostDesignDetailIdEqualTo(p.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            List<TicketTasksDetail> ticketTasksDetailList = ticketTasksDetailMapper.selectByExample(detailExample);
            if (ListUtils.isNotEmpty(ticketTasksDetailList)) {
                for (TicketTasksDetail td : ticketTasksDetailList) {
                    td.setDeletedFlag(true);
                    ticketTasksDetailMapper.updateByPrimaryKey(td);
                }
            }

        } else {
            //外购物料  -- 看板物料 --虚拟件
            if (null == p.getStatus() || -1 != p.getStatus()) {//非草稿状态
                TicketTasksDetailExample detailExample = new TicketTasksDetailExample();
                detailExample.createCriteria().andMilepostDesignDetailIdEqualTo(p.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<TicketTasksDetail> ticketTasksDetailList = ticketTasksDetailMapper.selectByExample(detailExample);

                TicketTasksDetail ticketTasksDetail = new TicketTasksDetail();
                ticketTasksDetail.setDeletedFlag(Boolean.FALSE);
                ticketTasksDetail.setPlanDeliveryTime(p.getDeliveryTime());//物料交货日期
                //父节点
                if (null != ticketTasksHearder) {
                    //ticketTasksDetail.setParentId(ticketTasksHearder.getId());//没父节点
                    ticketTasksDetail.setTicketTasksId(ticketTasksHearder.getId());
                    //父节点是否被删除
                    if (null != ticketTasksHearder.getTaskStatus()) {
                        //已发布
                        if (TicketTasksEnum.PUBLISHED.code() == ticketTasksHearder.getTaskStatus()) {

                        }
                        if (TicketTasksEnum.CANCEL.code() == ticketTasksHearder.getTaskStatus()) {
                            ticketTasksDetail.setDeletedFlag(true);
                        } else if (TicketTasksEnum.TERMINATION.code() == ticketTasksHearder.getTaskStatus()) {
                            ticketTasksDetail.setDeletedFlag(true);
                        }
                    }
                    ticketTasksDetail.setProjectBudgetMaterialId(ticketTasksHearder.getProjectBudgetMaterialId());//物料预算id
                    //物料跟随工单交货日期
                    if (null != ticketTasksHearder.getTicketPlanStartTime()) {
                        ticketTasksDetail.setPlanDeliveryTime(ticketTasksHearder.getTicketPlanStartTime());
                    }
                }
                ticketTasksDetail.setMilepostDesignDetailId(p.getId());
                ticketTasksDetail.setPamCode(p.getPamCode());
                ticketTasksDetail.setErpCode(p.getErpCode());
                ticketTasksDetail.setMaterielDescr(p.getMaterielDescr());
                ticketTasksDetail.setMaterialCategory(p.getMaterialCategory());
                ticketTasksDetail.setNumber(p.getNumber());

                ticketTasksDetail.setCreateAt(new Date());//工单创建时间
                ticketTasksDetail.setUpdateAt(new Date());//工单更新时间

                ticketTasksDetail.setUnit(p.getUnit());
                //装配需求总数
                BigDecimal number = designPlanMaterialDetailService.selectDetailNumber(p.getId());
                if (null != number) {
                    ticketTasksDetail.setTheassemblyNumber(number);
                } else {
                    number = BigDecimal.ZERO;
                }
                //已申请数量
                BigDecimal applyedNumber = BigDecimal.ZERO;
                if (StringUtils.isNotEmpty(p.getErpCode())) {
                    BigDecimal getNumber = designPlanMaterialDetailService.getTicketTasksGetNumber(ticketTasksDetail.getMilepostDesignDetailId());
                    if (null != getNumber) {
                        applyedNumber = getNumber;
                        ticketTasksDetail.setApplyedNumber(applyedNumber);
                    }
                    BigDecimal returnNumber =
                            designPlanMaterialDetailService.getTicketTasksReturnNumber(ticketTasksDetail.getMilepostDesignDetailId());
                    if (null != returnNumber) {
                        applyedNumber = applyedNumber.subtract(returnNumber);
                    }
                }
                //剩余领料数量
                BigDecimal restPickingNumber = number.subtract(applyedNumber);
                ticketTasksDetail.setRestPickingNumber(restPickingNumber);

                if (ListUtils.isNotEmpty(ticketTasksDetailList)) { // 对已经存在的任务工单行进行更新
                    ticketTasksDetail.setId(ticketTasksDetailList.get(0).getId());
                    if (ticketTasksDetailList.get(0).getDeletedFlag()) {
                        ticketTasksDetail.setDeletedFlag(Boolean.TRUE);
                    }
                    if (MaterialCategoryEnum.KANBAN_MATERIALS.msg().equals(p.getMaterialCategory())) {
                        ticketTasksDetail.setPlanDeliveryTime(null);
                        ticketTasksDetailMapper.updateByPrimaryKey(ticketTasksDetail);
                        //详设看板物料清空到货日期
                        MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
                        example2.createCriteria().andIdEqualTo(p.getId());
                        List<MilepostDesignPlanDetail> details = milepostDesignPlanDetailMapper.selectByExample(example2);
                        if (ListUtils.isNotEmpty(details)) {
                            logger.info("*************** ProjectBudgetChangeWorkflowCallbackServiceImpl.setTicketTasksByDesignPlanDetail入参1:{} " +
                                    "***************", JSONObject.toJSONString(details));
                            for (MilepostDesignPlanDetail d : details) {
                                d.setDeliveryTime(null);
                                milepostDesignPlanDetailMapper.updateByPrimaryKey(d);
                            }
                        }
                    } else {
                        //如果是物料类型变更--看板物料变外购物料
                        if (StringUtils.isNotEmpty(ticketTasksDetailList.get(0).getMaterialCategory())) {
                            if (MaterialCategoryEnum.PURCHASED_PARTS.msg().equals(ticketTasksDetail.getMaterialCategory())) {
                                //物料类型变更中--看板转外购件详细设计模组日期没有在＋14天
                                Date dd = new Date();
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(dd);
                                calendar.add(Calendar.DATE, 14);
                                //详设变更不修改原有时间 -- && !DateUtils.format(p.getDeliveryTime()).equals(DateUtils.format(ticketTasksDetailList.get(0)
                                // .getPlanDeliveryTime()))
                                if (null != ticketTasksDetailList.get(0).getPlanDeliveryTime() && ("详细设计变更".equals(lookAt) || StringUtils.isEmpty(lookAt))) {
                                    ticketTasksDetail.setPlanDeliveryTime(ticketTasksDetailList.get(0).getPlanDeliveryTime());
                                    updateDesignPlanDetailDeliveryTime(p.getId(), ticketTasksDetailList.get(0).getPlanDeliveryTime());
                                } else if (null != p.getDeliveryTime() && null == ticketTasksDetail.getPlanDeliveryTime()) {
                                    //条件1：当前日期-工单计划开始日期>14天，取工单计划开始日期
                                    //条件2：当前日期-工单计划开始日期<14天，取当前日期+14天
                                    //取模组层
                                    if (null != ticketTasksDetail.getParentId()) {
                                        TicketTasksExample example = new TicketTasksExample();
                                        example.createCriteria().andIdEqualTo(ticketTasksDetail.getParentId());
                                        List<TicketTasks> list = ticketTasksMapper.selectByExample(example);
                                        if (ListUtils.isNotEmpty(list)) {
                                            TicketTasks modelTicketTasks = list.get(0);
                                            if (null != modelTicketTasks.getTicketPlanStartTime()) {
                                                p.setDeliveryTime(modelTicketTasks.getTicketPlanStartTime());
                                            }
                                        }
                                    }

                                    if (calendar.getTime().getTime() > p.getDeliveryTime().getTime()) {
                                        ticketTasksDetail.setPlanDeliveryTime(calendar.getTime());
                                        //反改详细计划日期
                                        updateDesignPlanDetailDeliveryTime(p.getId(), calendar.getTime());
                                    } else {
                                        ticketTasksDetail.setPlanDeliveryTime(p.getDeliveryTime());
                                        //反改详细计划日期
                                        updateDesignPlanDetailDeliveryTime(p.getId(), p.getDeliveryTime());
                                    }
                                } else if (null != ticketTasksDetail.getPlanDeliveryTime()) {
                                    if (calendar.getTime().getTime() > ticketTasksDetail.getPlanDeliveryTime().getTime()) {
                                        ticketTasksDetail.setPlanDeliveryTime(calendar.getTime());
                                        //反改详细计划日期
                                        updateDesignPlanDetailDeliveryTime(p.getId(), calendar.getTime());
                                    } else {
                                        ticketTasksDetail.setPlanDeliveryTime(ticketTasksDetail.getPlanDeliveryTime());
                                        //反改详细计划日期
                                        updateDesignPlanDetailDeliveryTime(p.getId(), ticketTasksDetail.getPlanDeliveryTime());
                                    }
                                } else {

                                    ticketTasksDetail.setPlanDeliveryTime(calendar.getTime());
                                    updateDesignPlanDetailDeliveryTime(p.getId(), calendar.getTime());
                                }
                            }
                        }
                        ticketTasksDetailMapper.updateByPrimaryKeySelective(ticketTasksDetail);
                    }
                } else { // 非装配件的物料生成任务工单行
                    Date dd = new Date();
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(dd);
                    calendar.add(Calendar.DATE, 14);

                    //看板物料 明细行的类型变更，物料到货日期清空
                    if (MaterialCategoryEnum.KANBAN_MATERIALS.msg().equals(p.getMaterialCategory())) {
                        ticketTasksDetail.setPlanDeliveryTime(null);
                        //详设看板物料清空到货日期
                        MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
                        example2.createCriteria().andIdEqualTo(p.getId());
                        List<MilepostDesignPlanDetail> details = milepostDesignPlanDetailMapper.selectByExample(example2);
                        if (ListUtils.isNotEmpty(details)) {
                            logger.info("*************** ProjectBudgetChangeWorkflowCallbackServiceImpl.setTicketTasksByDesignPlanDetail入参2:{} " +
                                    "***************", JSONObject.toJSONString(details));
                            for (MilepostDesignPlanDetail d : details) {
                                d.setDeliveryTime(null);
                                milepostDesignPlanDetailMapper.updateByPrimaryKey(d);
                            }
                        }
                    } else if (null != ticketTasksDetail.getPlanDeliveryTime()) {
                        if (calendar.getTime().getTime() > ticketTasksDetail.getPlanDeliveryTime().getTime()) {
                            ticketTasksDetail.setPlanDeliveryTime(calendar.getTime());
                            //反改详细计划日期
                            updateDesignPlanDetailDeliveryTime(p.getId(), calendar.getTime());
                        } else {
                            ticketTasksDetail.setPlanDeliveryTime(ticketTasksDetail.getPlanDeliveryTime());
                        }
                    } else {
                        ticketTasksDetail.setPlanDeliveryTime(calendar.getTime());
                        //反改详细计划日期
                        updateDesignPlanDetailDeliveryTime(p.getId(), calendar.getTime());
                    }

                    ticketTasksDetailMapper.insert(ticketTasksDetail);
                }
                //采购件出现在模组层
                TicketTasksExample example = new TicketTasksExample();
                example.createCriteria().andMilepostDesignDetailIdEqualTo(p.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<TicketTasks> list = ticketTasksMapper.selectByExample(example);
                for (TicketTasks l : list) {
                    l.setDeletedFlag(true);
                    ticketTasksMapper.updateByPrimaryKeySelective(l);
                }
            }

        }
        List<MilepostDesignPlanDetailDto> sonDtos = p.getSonDtos();
        if (ListUtils.isNotEmpty(sonDtos)) {
            for (MilepostDesignPlanDetailDto s : sonDtos) {
                setTicketTasksByDesignPlanDetail(s, s.getSonDtos(), project, ticketTasksHearder, model, resource, lookAt);
            }
        }
    }

    //获取估价模组信息
    public MilepostDesignPlanDetailDto getModel(MilepostDesignPlanDetailDto p) {
        MilepostDesignPlanDetailDto model = new MilepostDesignPlanDetailDto();
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos = milepostDesignPlanDetailExtMapper.selectModel(p.getId());
        if (ListUtils.isNotEmpty(milepostDesignPlanDetailDtos)) {
            return milepostDesignPlanDetailDtos.get(0);
        }
        return model;
    }

    public void updateDesignPlanDetailDeliveryTime(Long designPlanDetailId, Date deliveryTime) {
        logger.info("详设添加交货日期" + designPlanDetailId);
        if (null != designPlanDetailId && null != deliveryTime) {
            deliveryTime = DateUtils.resetDate(deliveryTime); //重置日期时分秒为0
            MilepostDesignPlanDetail designPlanDetail = new MilepostDesignPlanDetail();
            designPlanDetail.setId(designPlanDetailId);
            designPlanDetail.setDeliveryTime(deliveryTime);
            milepostDesignPlanDetailMapper.updateByPrimaryKeySelective(designPlanDetail);
            updateDesignPlanDetailSonDeliveryTime(designPlanDetailId, deliveryTime);
            //mrp
            milepostDesignPlanService.setMRP(designPlanDetailId);
        }
    }

    public void updateDesignPlanDetailSonDeliveryTime(Long parentId, Date deliveryTime) {
        if (null != parentId && null != deliveryTime) {
            MilepostDesignPlanDetailExample example3 = new MilepostDesignPlanDetailExample();
            example3.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andParentIdEqualTo(parentId).andMaterialCategoryEqualTo(MaterialCategoryEnum.PURCHASED_PARTS.msg());
            List<MilepostDesignPlanDetail> detailson = milepostDesignPlanDetailMapper.selectByExample(example3);
            if (ListUtils.isNotEmpty(detailson)) {
                MilepostDesignPlanDetail updateItem = new MilepostDesignPlanDetail();
                updateItem.setDeliveryTime(deliveryTime);
                for (MilepostDesignPlanDetail son : detailson) {
                    updateItem.setId(son.getId());
                    milepostDesignPlanDetailMapper.updateByPrimaryKeySelective(updateItem);
                }
            }
        }

    }
}
