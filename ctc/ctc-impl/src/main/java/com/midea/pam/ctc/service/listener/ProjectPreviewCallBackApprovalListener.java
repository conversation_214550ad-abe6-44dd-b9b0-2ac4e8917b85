package com.midea.pam.ctc.service.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.ctc.dto.PCInvRecDTO;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.entity.DocumentLibraryNew;
import com.midea.pam.common.ctc.entity.FileListInfo;
import com.midea.pam.common.ctc.entity.InvoicePlanDetail;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeaderExample;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectMilepostExample;
import com.midea.pam.common.ctc.entity.ProjectProgressPredict;
import com.midea.pam.common.ctc.entity.ProjectProgressPredictChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectProgressPredictChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetail;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.CtcProjectChangeType;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.TicketTasksEnum;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.DocumentLibraryNewExtMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailExtMapper;
import com.midea.pam.ctc.mapper.ProjectChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.ProjectHistoryHeaderMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanExtMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostMapper;
import com.midea.pam.ctc.mapper.ProjectProfitMapper;
import com.midea.pam.ctc.mapper.ProjectProgressPredictChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectProgressPredictMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailExtMapper;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeWorkflowCallbackService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.TicketTasksService;
import com.midea.pam.ctc.service.WbsPushToErpService;
import com.midea.pam.ctc.service.event.ProjectBudgetTargetEvent;
import com.midea.pam.ctc.service.event.ProjectPreviewCallBackApprovalEvent;
import com.midea.pam.ctc.service.event.ProjectWbsBudgetFeeEvent;
import com.midea.pam.ctc.wbs.service.PreviewWbsBudgetInfoChangeService;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

public class ProjectPreviewCallBackApprovalListener implements ApplicationListener<ProjectPreviewCallBackApprovalEvent> {

    private final static Logger logger = LoggerFactory.getLogger(ProjectPreviewCallBackApprovalListener.class);

    private final static Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private ProjectBudgetChangeWorkflowCallbackService projectBudgetChangeWorkflowCallbackService;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private ProjectIncomeCostPlanMapper projectIncomeCostPlanMapper;
    @Resource
    private ProjectProfitMapper projectProfitMapper;
    @Resource
    private TicketTasksService ticketTasksService;
    @Resource
    private WbsPushToErpService wbsPushToErpService;
    @Resource
    private ProjectIncomeCostPlanExtMapper projectIncomeCostPlanExtMapper;
    @Resource
    private DocumentLibraryNewExtMapper documentLibraryNewExtMapper;
    @Resource
    private ProjectMilepostMapper projectMilepostMapper;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private ProjectHistoryHeaderMapper projectHistoryHeaderMapper;
    @Resource
    private ProjectChangeHistoryMapper projectChangeHistoryMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectExtMapper projectExtMapper;
    @Resource
    private InvoicePlanDetailExtMapper invoicePlanDetailExtMapper;
    @Resource
    private ReceiptPlanDetailExtMapper receiptPlanDetailExtMapper;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private PreviewWbsBudgetInfoChangeService previewWbsBudgetInfoChangeService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private WorkflowCallbackService workflowCallbackService;

    @Resource
    private ProjectProgressPredictChangeHistoryMapper projectProgressPredictChangeHistoryMapper;

    @Resource
    private ProjectProgressPredictMapper projectProgressPredictMapper;
    @Resource
    private ProjectService projectService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @Async
    public void onApplicationEvent(ProjectPreviewCallBackApprovalEvent event) {
        logger.info("项目预立项转正审批通过回调的异步处理参数为:{}", JsonUtils.toString(event));

        ProjectDto projectDto = event.getProjectDto();
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        Long createUserId = projectDto.getCreateUserId();
        String lockName = String.format("ProjectPreviewCallBackApproval_previewApprovedHandler_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    previewApprovedHandler(projectDto);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            true);
                } catch (ApplicationBizException e) {
                    logger.info("项目预立项转正审批通过回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目预立项转正审批通过回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目预立项转正审批通过回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void previewApprovedHandler(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        // 先查询是否存在转正的变更详情
        ProjectHistoryHeader projectHistoryHeader = findHistoryHeaderByProjectId(formInstanceId);
        // 如果不存在转正变更记录,执行立项审批通过操作(兼容历史转正数据)
        if (projectHistoryHeader == null) {
            projectBusinessService.approvedHandler(projectDto);
            return;
        }
        Long headerId = projectHistoryHeader.getId();
        final ProjectChangeHistory projectChangeHistory = getProjectChangeByHeaderId(headerId);
        // 如果不存在转正变更记录,执行立项审批通过操作(兼容历史转正数据)
        if (projectChangeHistory == null) {
            projectBusinessService.approvedHandler(projectDto);
            return;
        }
        // 转正变更明细覆盖正式表数据
        logger.info("{}转正变更明细覆盖正式表数据BEGIN", headerId);
        historyInfoCallback(headerId, formInstanceId, companyId, createUserId);
        logger.info("{}转正变更明细覆盖正式表数据DONE", formInstanceId);
        final Project project = projectBusinessService.selectByPrimaryKey(formInstanceId);
        project.setPreviewFlag(false);
        project.setUpdateBy(createUserId);
        project.setPreviewApprovalDate(new Date());//预立项转正审批通过日期
        project.setStatus(ProjectStatus.APPROVALED.getCode());
        project.setEmsStatus(2);//ems同步中
        projectBusinessService.updateByPrimaryKeySelective(project);

        //立项审批通过时（正式立项、预立项），根据其值建立根目录文件夹
        if (!isCreateFixFolder(project, companyId)) {
            createFixFolder(project, companyId);
        }
        //查询关联的合同开票和回款的里程碑信息 added by huangna added at 20210105 bug:BUG2020121177845
        final List<PCInvRecDTO> pcInvRecMilestoneInf = projectExtMapper.getPCInvRecMilestoneInf(formInstanceId);
        if (ListUtils.isNotEmpty(pcInvRecMilestoneInf)) {
            List<InvoicePlanDetail> invoicePlanDetailList = new ArrayList<>();
            List<ReceiptPlanDetail> receiptPlanDetailList = new ArrayList<>();
            for (PCInvRecDTO pcInvRecDTO : pcInvRecMilestoneInf) {
                InvoicePlanDetail invoicePlanDetail = new InvoicePlanDetail();
                ReceiptPlanDetail receiptPlanDetail = new ReceiptPlanDetail();
                //合同的开票或回款计划关联的里程碑ID更换成项目的里程碑ID
                if (StringUtils.isNotEmpty(pcInvRecDTO.getInvMilepostStage())) {
                    ProjectMilepostExample projectMilepostExample = new ProjectMilepostExample();
                    projectMilepostExample.createCriteria().andNameEqualTo(pcInvRecDTO.getInvMilepostStage()).andProjectIdEqualTo(formInstanceId)
                            .andDeletedFlagEqualTo(Boolean.FALSE);
                    final List<ProjectMilepost> projectMilepostList = projectMilepostMapper.selectByExample(projectMilepostExample);
                    if (ListUtils.isNotEmpty(projectMilepostList)) {
                        invoicePlanDetail.setId(pcInvRecDTO.getInvoicePlanDetailId());
                        invoicePlanDetail.setMilestoneId(projectMilepostList.get(0).getId());
                        invoicePlanDetailList.add(invoicePlanDetail);
                    }
                }

                if (StringUtils.isNotEmpty(pcInvRecDTO.getRecMilepostStage())) {
                    ProjectMilepostExample projectMilepostExample = new ProjectMilepostExample();
                    projectMilepostExample.createCriteria().andNameEqualTo(pcInvRecDTO.getRecMilepostStage()).andProjectIdEqualTo(formInstanceId)
                            .andDeletedFlagEqualTo(Boolean.FALSE);
                    final List<ProjectMilepost> projectMilepostList = projectMilepostMapper.selectByExample(projectMilepostExample);
                    if (ListUtils.isNotEmpty(projectMilepostList)) {
                        receiptPlanDetail.setId(pcInvRecDTO.getReceiptPlanDetailId());
                        receiptPlanDetail.setMilestoneId(projectMilepostList.get(0).getId());
                        receiptPlanDetailList.add(receiptPlanDetail);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(invoicePlanDetailList)) {
                invoicePlanDetailExtMapper.batchUpdate(invoicePlanDetailList);
            }
            if (CollectionUtils.isNotEmpty(receiptPlanDetailList)) {
                receiptPlanDetailExtMapper.batchUpdate(receiptPlanDetailList);
            }
        }

        applicationEventPublisher.publishEvent(new ProjectBudgetTargetEvent(this, formInstanceId));

        //将物料、wbs预算写入详细设计方案
        if (Boolean.TRUE.equals(project.getWbsEnabled())) {
            logger.info("将WBS写入详细设计方案BEGIN");
            milepostDesignPlanService.milepostDesignPlanJoinWbs(formInstanceId);
            logger.info("将WBS写入详细设计方案DONE");
        } else {
            logger.info("将物料预算写入详细设计方案BEGIN");
            milepostDesignPlanService.projectBudgetMaterialToMilepostDesignPlanDetail(formInstanceId, createUserId);
            logger.info("将物料预算写入详细设计方案DONE");
        }

        //项目类型配置为“允许立项时上传详细设计方案”为是的项目
//        projectBusinessService.updateProjectDesign(BeanConverter.copy(project, ProjectDto.class), CheckStatus.PASS.code());

        //查找组织参数，是否写入wbs
        String url = "";
        String res = "";
        final Map<String, Object> query = new HashMap<>();
        if (companyId != null) {
            query.put("orgId", companyId);
        } else {
            query.put("orgId", SystemContext.getUnitId());
        }
        query.put("orgFrom", "company");
        query.put("name", "WBS号写入");
        url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query);
        res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationCustomDict>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
                });
        if (ListUtils.isNotEmpty(response.getData())) {
            //项目号同步ems插入resend表
            wbsPushToErpService.wbsPushToErp("project", project.getId());
        }
        //在正式立项/预立项转正流程通过后如果没有收入节点，按照当前月份创建第一个收入节点
        if (project.getStatus() == 4 && !project.getPreviewFlag()) {
            projectBusinessService.createFirstMonth(project, new Date());
        }

        // wbs项目暂时不走工单业务场景 2022-06-18
        if (!Boolean.TRUE.equals(project.getWbsEnabled())) {
            //正式立项或预立项转正审批通过后，项目物料预算各层级自动生成已发布状态的工单任务 addedBy zhongpeng addedAt 20210311 昆山迭代5 产品负责人 yanyouwei
            ticketTasksService.generateByProjectMBorPC(project.getId(), TicketTasksEnum.FORMALPROJECT.code(), null, "立项变更");

        }
        //如果涉及 业务分类、业务分布、项目经理的变更，回写销售合同
        projectBusinessService.writeBackContractInfo(headerId);

        //保存项目进度预测信息
        ProjectProgressPredictChangeHistoryExample example = new ProjectProgressPredictChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headerId).andDeletedFlagEqualTo(false);
        List<ProjectProgressPredictChangeHistory> projectProgressPredictChangeHistories = projectProgressPredictChangeHistoryMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(projectProgressPredictChangeHistories)){
            List<ProjectProgressPredict> projectProgressPredicts = new ArrayList<>();
            for (ProjectProgressPredictChangeHistory projectProgressPredictChangeHistory : projectProgressPredictChangeHistories) {
                ProjectProgressPredict predict = new ProjectProgressPredict();
                predict.setProjectId(projectProgressPredictChangeHistory.getProjectId());
                predict.setMonth(projectProgressPredictChangeHistory.getMonth());
                predict.setCumulativeProgressPredict(projectProgressPredictChangeHistory.getCumulativeProgressPredict());
                predict.setRemark(projectProgressPredictChangeHistory.getRemark());
                predict.setDeletedFlag(false);
                projectProgressPredicts.add(predict);
                //修改历史变更状态
                projectProgressPredictChangeHistory.setHistoryType(0);
                projectProgressPredictChangeHistoryMapper.updateByPrimaryKey(projectProgressPredictChangeHistory);
            }
            if (CollectionUtils.isNotEmpty(projectProgressPredicts)){
                for (ProjectProgressPredict projectProgressPredict : projectProgressPredicts) {
                    projectProgressPredictMapper.insert(projectProgressPredict);
                }
            }
        }

        //【项目类型】立项后详细设计模组默认状态，设置为：已确认
        projectBusinessService.updateModuleStatusByProjectType(project);
    }

    private ProjectHistoryHeader findHistoryHeaderByProjectId(Long projectId) {
        ProjectHistoryHeaderExample historyHeaderExample = new ProjectHistoryHeaderExample();
        historyHeaderExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(projectId)
                .andChangeTypeEqualTo(CtcProjectChangeType.PREPROJECT.getCode());
        historyHeaderExample.setOrderByClause("create_at desc");
        List<ProjectHistoryHeader> projectHistoryHeaders = projectHistoryHeaderMapper.selectByExample(historyHeaderExample);
        return ListUtils.isNotEmpty(projectHistoryHeaders) ? projectHistoryHeaders.get(0) : null;
    }

    private ProjectChangeHistory getProjectChangeByHeaderId(Long headerId) {
        ProjectChangeHistoryExample example = new ProjectChangeHistoryExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        List<ProjectChangeHistory> projectChangeHistories = projectChangeHistoryMapper.selectByExample(example);
        return ListUtils.isNotEmpty(projectChangeHistories) ? projectChangeHistories.get(0) : null;
    }

    /**
     * @param headerId     转正变更头id
     * @param projectId    项目id
     * @param companyId    使用单位id
     * @param createUserId 发起人
     */
    private void historyInfoCallback(Long headerId, Long projectId, Long companyId, Long createUserId) {
        // 基本信息变更
        projectBusinessService.baseInfoChangePass(headerId);

        // 更新前的项目
        Project beforeProject = projectMapper.selectByPrimaryKey(projectId);
        if (Boolean.TRUE.equals(beforeProject.getWbsEnabled())) {
            // 关联合同和商机变更（非WBS预算变更，已包含合同商机商机变更）
            projectBusinessService.projectContractChangePass(headerId);
            // wbs预算变更
            previewWbsBudgetInfoChangeService.previewPassCoverageWbsBudget(headerId, projectId, companyId, createUserId);
            logger.info("项目预算变更同步预算信息至EMS：{}, {}", BusinessTypeEnums.PROJECT_BUDGET_MODIFY_EMS.getCode(), headerId);
            //项目号同步ems插入resend表（同步WBS号）
            wbsPushToErpService.wbsPushToErp("project", projectId);
            // 同步预算信息至EMS
            applicationEventPublisher.publishEvent(new ProjectWbsBudgetFeeEvent(this, projectId, createUserId, headerId));
            //更新wbs预算快照事件
            projectService.publishProjectWbsBudgetVersion(headerId);
        } else {
            // 物料预算信息变更 改为同步调用
            projectBudgetChangeWorkflowCallbackService.asynfromEvent(headerId, createUserId, companyId);

        }
        //里程碑变更/收入成本计划变更
        projectBusinessService.milepostChangePass(headerId);
    }

    private Boolean isCreateFixFolder(Project project, Long companyId) {
        Boolean flag = false;
        Integer num = 0;
        //查找组织参数
        String url = "";
        String res = "";
        final Map<String, Object> query = new HashMap<>();
        if (companyId != null) {
            query.put("orgId", companyId);
        } else {
            query.put("orgId", SystemContext.getUnitId());
        }
        query.put("orgFrom", "company");
        query.put("name", "文档库固定文件夹名称");
        url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query);
        res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationCustomDict>> fixRes = JSON.parseObject(res,
                new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
                });
        if (ListUtils.isNotEmpty(fixRes.getData())) {
            String[] strFolderName = fixRes.getData().get(0).getValue().split(",");
            Long projectId = project.getId();
            for (int i = 0; i < strFolderName.length; i++) {
                Map<String, Object> folderName = new HashMap<>();
                folderName.put("strFolderName", strFolderName[i]);
                folderName.put("projectId", projectId);
                FileListInfo folderName1 = documentLibraryNewExtMapper.getFolderName(folderName);
                if (folderName1 != null) {
                    num++;
                }
            }
        }
        if (ListUtils.isNotEmpty(fixRes.getData())) {
            if (num == fixRes.getData().get(0).getValue().split(",").length) {
                return true;
            }
        }
        return flag;
    }

    private void createFixFolder(Project project, Long companyId) {
        //查找组织参数
        final Map<String, Object> query = new HashMap<>();
        if (companyId != null) {
            query.put("orgId", companyId);
        } else {
            query.put("orgId", SystemContext.getUnitId());
        }
        query.put("orgFrom", "company");
        query.put("name", "文档库固定文件夹名称");
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationCustomDict>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
                });
        if (ListUtils.isNotEmpty(response.getData())) {
            List<DocumentLibraryNew> documentLibraryNewList = new ArrayList<>();
            Long projectId = project.getId();
            String[] folders = response.getData().get(0).getValue().split(",");
            for (int i = 0; i < folders.length; i++) {
                Map<String, Object> folderName = new HashMap<>();
                folderName.put("strFolderName", folders[i]);
                folderName.put("projectId", projectId);
                FileListInfo folderName1 = documentLibraryNewExtMapper.getFolderName(folderName);
                if (folderName1 == null) {
                    DocumentLibraryNew documentLibraryNew = new DocumentLibraryNew();
                    documentLibraryNew.setProjectId(project.getId());
                    documentLibraryNew.setFolderName(folders[i]);
                    documentLibraryNew.setFolderDes("文档库固定文件夹名称");
                    documentLibraryNew.setParentId(-1L);
                    documentLibraryNew.setFolderProperties(0);
                    documentLibraryNew.setCreateAt(new Date());
                    Long userId = SystemContext.getUserId();
                    documentLibraryNew.setCreateBy(userId);
                    documentLibraryNew.setDeletedFlag(DeletedFlag.VALID.code());
                    documentLibraryNew.setSource(0);
                    documentLibraryNewList.add(documentLibraryNew);
                }
            }

            if (CollectionUtils.isNotEmpty(documentLibraryNewList)) {
                documentLibraryNewExtMapper.batchInsert(documentLibraryNewList);
            }
        }
    }

    private String addMonth(String applyMonth) {
        if (StringUtils.isEmpty(applyMonth)) {
            return null;
        }

        String[] split = applyMonth.split("-");
        if (split.length == 2) {
            int year = Integer.parseInt(split[0]);
            int month = Integer.parseInt(split[1]);

            if (month == 12) {
                year = year + 1;
                month = 1;
            } else {
                month = month + 1;
            }
            String monthStr = month < 10 ? "0" + month : "" + month;
            return year + "-" + monthStr;
        }
        return null;
    }

}
