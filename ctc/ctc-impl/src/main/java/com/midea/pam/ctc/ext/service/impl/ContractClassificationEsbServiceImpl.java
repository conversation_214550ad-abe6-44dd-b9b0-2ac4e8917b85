package com.midea.pam.ctc.ext.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.ctc.dto.CostAccountingErpDto;
import com.midea.pam.common.ctc.entity.ContractClassificationDetail;
import com.midea.pam.common.ctc.entity.ContractClassificationDetailExample;
import com.midea.pam.common.ctc.entity.ContractClassificationHead;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.ContractClassificationSyncStatus;
import com.midea.pam.common.enums.ContractClassificationType;
import com.midea.pam.common.enums.LoanType;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.ContractClassificationDetailMapper;
import com.midea.pam.ctc.mapper.ContractClassificationHeadMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.OrganizationRelExtService;
import com.midea.pam.ctc.service.SdpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description: 重分类入账单写入到ERP
 * @author: ex_xuwj4
 * @create: 2021-10-14
 **/
public class ContractClassificationEsbServiceImpl extends AbstractCommonBusinessService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private EsbService esbService;
    @Resource
    private ContractClassificationHeadMapper contractClassificationHeadMapper;
    @Resource
    private ContractClassificationDetailMapper contractClassificationDetailMapper;
    @Resource
    OrganizationRelExtService organizationRelExtService;
    @Resource
    private ContractClassificationHeadMapper headMapper;
    @Resource
    private SdpService sdpService;


    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        logger.info("------------------- 重分类入账单写入到ERP推送信息开始 -------------------");
        logger.info("待推送信息：{}", JSONObject.toJSONString(resendExecute));
        final String applyNo = resendExecute.getApplyNo();
        Assert.notNull(applyNo, "applyNo不能为空");
        final Long id = Long.valueOf(applyNo);
        final ContractClassificationHead contractClassificationHead = contractClassificationHeadMapper.selectByPrimaryKey(id);
        if (contractClassificationHead == null) {
            logger.warn("重分类入账单写入到ERP，ID：{}不存在，推送结束", id);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode("000000");
            esbResponse.setResponsemessage("处理完成");
            return esbResponse;
        }

        ContractClassificationDetailExample example = new ContractClassificationDetailExample();
        ContractClassificationDetailExample.Criteria criteria = example.createCriteria();
        criteria.andContractClassificationHeadIdEqualTo(id).andDeletedFlagEqualTo(false);
        final List<ContractClassificationDetail> details = contractClassificationDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(details)) {
            logger.warn("重分类入账单写入到ERP，{}对应明细数据不存在，推送结束", id);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode("000000");
            esbResponse.setResponsemessage("处理完成");
            return esbResponse;
        }

        List<CostAccountingErpDto> dtos = new ArrayList<>();
        final String endingOfMonth = getEndingOfMonth(contractClassificationHead.getGlPeriod());
        for (int i = 0; i < details.size(); i++) {
            final CostAccountingErpDto costAccountingErpDto = handleDto(contractClassificationHead, details.get(i), i + 1, endingOfMonth);
            dtos.add(costAccountingErpDto);
        }

//        return esbService.callCUXGLJOURNALSIMPORTAPIPKGPortType(dtos);
        return sdpService.callGERPGlJournalsImport(dtos);
    }

    private CostAccountingErpDto handleDto(ContractClassificationHead contractClassificationHead, ContractClassificationDetail contractClassificationDetail,
                                           Integer lineNum, String endingOfMonth) {
        CostAccountingErpDto dto = new CostAccountingErpDto();
        dto.setId(contractClassificationHead.getId());//头Id
        dto.setLedgerId(new BigDecimal(this.getLedgerId(contractClassificationHead.getOuId()))); //根据ouId查找ledgerId
        dto.setStatus("U");
        dto.setAccountingDate(endingOfMonth);
        dto.setActualFlag("A");
        dto.setUserJesourceName("PAM");
        dto.setUserJeCategoryName(".记账凭证");
        dto.setCurrencyCode(contractClassificationHead.getCurrency());
        ContractClassificationType classificationType = ContractClassificationType.getType(contractClassificationHead.getType());
        dto.setBathName("PAM_日记账导入_科目重分类_" + classificationType.getName() + "入账");
        dto.setJournalName(contractClassificationHead.getAccountNum());
        String[] subject = StringUtils.split(contractClassificationDetail.getSubject(), ".");
        dto.setSegment1(subject[0]);
        dto.setSegment2(subject[1]);
        dto.setSegment3(subject[2]);
        dto.setSegment4(subject[3]);
        dto.setSegment5(subject[4]);
        dto.setSegment6(subject[5]);
        dto.setSegment7(subject[6]);
        if (LoanType.DEBIT.getCode() == contractClassificationDetail.getLoanType()) {//借方
            dto.setEnterEddr(contractClassificationDetail.getAmount());
            dto.setAccountEddr(contractClassificationDetail.getAmount());
        } else {//贷方
            dto.setEnterEdcr(contractClassificationDetail.getAmount());
            dto.setAccountEdcr(contractClassificationDetail.getAmount());
        }
        dto.setReference5("PAM_日记账导入_科目重分类_" + classificationType.getName() + "入账_" + contractClassificationHead.getAccountNum());
        dto.setReference10(contractClassificationDetail.getRemark());
        dto.setLineNumber(new BigDecimal(lineNum));
        dto.setJgzzreconref(contractClassificationHead.getAccountNum());
        dto.setSourcecode("PAM");
        dto.setSourceNum(contractClassificationHead.getAccountNum());
        dto.setEsbDataSource(BusinessTypeEnums.CONTRACT_CLASSIFICATION.getCode());
        dto.setOperationType("CREATE");
        return dto;
    }

    /**
     * 根据ouId查找ledgerId
     *
     * @param ouId
     * @return
     */
    private Long getLedgerId(Long ouId) {
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        query.setPamEnabled(0);
        PageInfo<OrganizationRelDto> orgDtoPage = organizationRelExtService.invokeApiList(query);
        return orgDtoPage.getList().get(0).getLedgerId();
    }

    /**
     * 获取月份的最后一天
     *
     * @param yearAndMonth
     * @return
     */
    private String getEndingOfMonth(String yearAndMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Date month = null;
        try {
            month = sdf.parse(yearAndMonth);
        } catch (ParseException e) {
            logger.error(e.getMessage(), e);
        }
        final Date endingOfMonth = DateUtil.getEndingOfMonth(month);
        final String day = DateUtil.format(endingOfMonth, DateUtil.DATE_PATTERN);
        return day;
    }

    /**
     * 回调.
     *
     * @param resendExecute
     */
    @Override
    public void callback(ResendExecute resendExecute) {
        logger.info("------------------- 重分类入账单同步回调 -------------------");
        logger.info("推送信息：{}", JSONObject.toJSONString(resendExecute));
        final String applyNo = resendExecute.getApplyNo();
        Assert.notNull(applyNo, "applyNo不能为空");
        final Long id = Long.valueOf(applyNo);
        final ContractClassificationHead contractClassificationHead = contractClassificationHeadMapper.selectByPrimaryKey(id);
        if (contractClassificationHead == null) {
            return;
        }
        // 只处理失败结果
        if (!Objects.equals(resendExecute.getResponCode(), ResponseCodeEnums.SUCESS.getCode())) {
            contractClassificationHead.setSyncMessage(resendExecute.getResponMsg());
            contractClassificationHead.setSyncStatus(ContractClassificationSyncStatus.FAILURE.getCode());
            headMapper.updateByPrimaryKeySelective(contractClassificationHead);
        }
    }
}
