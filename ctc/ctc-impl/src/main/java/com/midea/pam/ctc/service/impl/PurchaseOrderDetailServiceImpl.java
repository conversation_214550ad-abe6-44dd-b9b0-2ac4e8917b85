
package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.base.IdEntity;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetail;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailExample;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.entity.ResendExecuteExample;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.enums.SyncDeliveryInfoStatusEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.PurchaseOrderDetailStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.ProjectProfitMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderMapper;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.PurchaseOrderDetailDeliveryAddressHistoryService;
import com.midea.pam.ctc.service.PurchaseOrderDetailService;
import com.midea.pam.ctc.service.PurchaseOrderService;
import com.midea.pam.ctc.service.ResendExecuteService;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.ctc.service.StorageInventoryExtService;
import com.midea.pam.ctc.service.VendorAslService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

public class PurchaseOrderDetailServiceImpl implements PurchaseOrderDetailService {

    private final static Logger logger = LoggerFactory.getLogger(PurchaseOrderDetailServiceImpl.class);

    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;
    @Resource
    private PurchaseMaterialRequirementService purchaseMaterialRequirementService;
    @Resource
    private PurchaseOrderDetailExtMapper purchaseOrderDetailExtMapper;
    @Resource
    private VendorAslService vendorAslService;
    @Resource
    private ProjectProfitMapper projectProfitMapper;
    @Resource
    private StorageInventoryExtService storageInventoryExtService;
    @Resource
    private PurchaseOrderDetailDeliveryAddressHistoryService purchaseOrderDetailDeliveryAddressHistoryService;
    @Resource
    private SdpService sdpService;
    @Resource
    private ResendExecuteService resendExecuteService;
    @Resource
    private PurchaseOrderService purchaseOrderService;



    @Override
    public PurchaseOrderDetailDto add(PurchaseOrderDetailDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        dto.setCancelNum(BigDecimal.ZERO);//取消数量
        dto.setSyncDeliveryInfoStatus(SyncDeliveryInfoStatusEnum.UNSYNC.code());//同步收货信息状态
        PurchaseOrderDetail entity = BeanConverter.copy(dto, PurchaseOrderDetail.class);
        purchaseOrderDetailMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public PurchaseOrderDetailDto update(PurchaseOrderDetailDto dto) {
        PurchaseOrderDetail entity = BeanConverter.copy(dto, PurchaseOrderDetail.class);
        purchaseOrderDetailMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public PurchaseOrderDetailDto save(PurchaseOrderDetailDto dto, Long userBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
            return this.add(dto);
        } else {
            dto.setUpdateBy(userBy);
            return this.update(dto);
        }
    }

    @Override
    public PurchaseOrderDetailDto getById(Long id) {
        Asserts.notEmpty(id, ErrorCode.ID_NOT_NULL);
        PurchaseOrderDetail entity = purchaseOrderDetailMapper.selectByPrimaryKey(id);
        PurchaseOrderDetailDto dto = BeanConverter.copy(entity, PurchaseOrderDetailDto.class);
        return dto;
    }

    @Override
    public List<PurchaseOrderDetailDto> selectList(PurchaseOrderDetailDto query) {
        List<PurchaseOrderDetail> list = purchaseOrderDetailMapper.selectByExample(buildCondition(query));
        List<PurchaseOrderDetailDto> dtos = BeanConverter.copy(list, PurchaseOrderDetailDto.class);
        for (PurchaseOrderDetailDto dto : dtos) {
            packageDto(dto);
        }
        return dtos;
    }

    @Override
    public PageInfo<PurchaseOrderDetailDto> selectPage(PurchaseOrderDetailDto query) {
        return null;
    }

    private PurchaseOrderDetailExample buildCondition(PurchaseOrderDetailDto query) {
        PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
        PurchaseOrderDetailExample.Criteria criteria = example.createCriteria();
        example.setOrderByClause("line_number asc");
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());

        if (query.getMaterialPurchaseRequirementId() != null) {
            criteria.andMaterialPurchaseRequirementIdEqualTo(query.getMaterialPurchaseRequirementId());
        }
        if (query.getPurchaseOrderId() != null) {
            criteria.andPurchaseOrderIdEqualTo(query.getPurchaseOrderId());
        }
        if (query.getCreateBy() != null) {
            criteria.andCreateByEqualTo(query.getCreateBy());
        }
        if (query.getVendorAslId() != null) {
            criteria.andVendorAslIdEqualTo(query.getVendorAslId());
        }
        if (query.getStatus() != null) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (query.getRecordId() != null) {
            criteria.andRecordIdEqualTo(query.getRecordId());
        }

        return example;
    }

    private void packageDto(PurchaseOrderDetailDto dto) {
        if (dto != null) {
            //供应商编号
            dto.setVendorCode(dto.getVendorNum());
            //创建人名称
            dto.setCreateByName(CacheDataUtils.findUserNameById(dto.getCreateBy()));
        }
    }

    @Override
    public PurchaseOrderDetailDto getReleasedDataByRequirementIdAndVendorAslIdAndBuyerBy(Long requirementId, Long vendorAslId, Long buyerBy) {
        PurchaseOrderDetailDto query = new PurchaseOrderDetailDto();
        query.setMaterialPurchaseRequirementId(requirementId);
        query.setVendorAslId(vendorAslId);
        query.setCreateBy(buyerBy);
        query.setStatus(PurchaseOrderDetailStatus.ORDER_RELEASED.code());
        List<PurchaseOrderDetailDto> dtos = this.selectList(query);
        if (ListUtil.isPresent(dtos)) {
            return dtos.get(0);
        }
        return null;
    }

    @Override
    @Transactional
    public Boolean saveBatch(List<PurchaseOrderDetailDto> dtos, Long userBy) {
        for (PurchaseOrderDetailDto dto : dtos) {
            checkOrderDetailCanSave(dto.getId(), dto.getMaterialPurchaseRequirementId(), dto.getOrderNum());
            this.save(dto, userBy);
            batchUpdateDeliveryAddress(dtos.stream().map(IdEntity::getId).collect(Collectors.toList())
                    , dto.getDeliveryAddress(), dto.getConsignee(), dto.getContactPhone(),false);
        }

        return true;
    }

    @Override
    public Boolean checkOrderDetailCanSave(Long orderDetailId, Long requirementId, BigDecimal orderDetailOrderNum) {
        PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getDetailById(requirementId);
        Asserts.notEmpty(requirementDto, ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_NOT_FIND);
        Asserts.notEmpty(requirementDto.getUnreleasedAmount(), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_UNRELEASED_AMOUNT_NULL);
        Asserts.notEmpty(orderDetailOrderNum, ErrorCode.CTC_ORDER_DETAILS_ORDER_NUM_NULL);

        if (orderDetailId != null) {
            PurchaseOrderDetailDto existDto = this.getById(orderDetailId);
            Asserts.notEmpty(existDto, ErrorCode.CTC_ORDER_DETAILS_NOT_NULL);
            Asserts.notEmpty(existDto.getOrderNum(), ErrorCode.CTC_ORDER_DETAILS_ORDER_NUM_NULL);
            if (requirementDto.getUnreleasedAmount().add(existDto.getOrderNum()).compareTo(orderDetailOrderNum) > -1) {
                return true;
            } else {
                Asserts.notEmpty(null, ErrorCode.CTC_REQUIREMENT_UNRELEASED_AMOUNT_OUT);
            }
        } else if (requirementDto.getUnreleasedAmount().compareTo(orderDetailOrderNum) > -1) {
            return true;
        } else {
            Asserts.notEmpty(null, ErrorCode.CTC_REQUIREMENT_UNRELEASED_AMOUNT_OUT);
        }
        return false;
    }

    @Override
    @Transactional
    public Boolean deleteBatch(List<Long> ids, Long userBy) {
        Asserts.notEmpty(ids, ErrorCode.IDS_NOT_NULL);
        for (Long id : ids) {
            PurchaseOrderDetailDto dto = new PurchaseOrderDetailDto();
            dto.setId(id);
            dto.setDeletedFlag(DeletedFlag.INVALID.code());
            this.save(dto, userBy);

        }
        //更新采购需求状态
        for (Long id : ids) {
            PurchaseOrderDetail orderDetail = purchaseOrderDetailMapper.selectByPrimaryKey(id);
            purchaseMaterialRequirementService.updateStatus(orderDetail.getMaterialPurchaseRequirementId());
        }
        return true;
    }

    @Override
    public PageInfo<PurchaseOrderDetailDto> pagePurchaseOrderProgress(Integer pageNum, Integer pageSize, PurchaseOrderDetailDto condition) {
        PageHelper.startPage(pageNum, pageSize);
        final PageInfo<PurchaseOrderDetailDto> page = new PageInfo<PurchaseOrderDetailDto>(purchaseOrderDetailExtMapper.queryPurchaseOrderProgress(condition));
        if (null != page && ListUtils.isNotEmpty(page.getList())) {
            for (PurchaseOrderDetailDto dto : page.getList()) {
                final OperatingUnit ou = CacheDataUtils.findOuById(dto.getOuId());
                dto.setOuName(null != ou ? ou.getOperatingUnitName() : null);
                final UserInfo user = CacheDataUtils.findUserById(dto.getBuyerId());
                dto.setBuyer(null != user ? user.getName() : null);
            }
        }
        return page;
    }

    /**
     * 详细设计单据部分确认时,已下达采购订单后,重新变更时需要修改的请求
     *
     * @param materialPurchaseRequirementId
     */
    @Override
    public void updatePurchaseOrderDetailByMaterialPurchaseRequirementId(Long materialPurchaseRequirementId, String requirementCode, Long projectWbsReceiptsId) {
        PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
        example.createCriteria().andMaterialPurchaseRequirementIdEqualTo(materialPurchaseRequirementId).andDeletedFlagEqualTo(false);
        List<PurchaseOrderDetail> purchaseOrderDetails = purchaseOrderDetailMapper.selectByExample(example);
        logger.info("变更后,待修改的数据为:{}", JSON.toJSONString(purchaseOrderDetails));
        if (!CollectionUtils.isEmpty(purchaseOrderDetails)) {
            List<PurchaseOrderDetail> list = purchaseOrderDetails.stream().map(x -> {
                PurchaseOrderDetail detail = new PurchaseOrderDetail();
                detail.setId(x.getId());
                detail.setProjectWbsReceiptsId(projectWbsReceiptsId);
                detail.setRequirementCode(requirementCode);
                return detail;
            }).collect(Collectors.toList());
            purchaseOrderDetailExtMapper.batchUpdate(list);
        }
    }

    @Override
    public void batchUpdateDeliveryAddress(List<Long> detailIds, String deliveryAddress, String consignee, String contactPhone, Boolean manually) {
        logger.info("开始批量更新订单收货地址信息，订单明细IDs={}, 收货地址={}, 收货人={}, 联系电话={}", 
            detailIds, deliveryAddress, consignee, contactPhone);

        if (ListUtils.isEmpty(detailIds)) {
            logger.warn("订单明细ID列表为空，无需更新");
            return;
        }

        try {
            // 1. 查询有效的订单明细记录
            List<PurchaseOrderDetail> purchaseOrderDetails = queryValidOrderDetails(detailIds);
            if (ListUtils.isEmpty(purchaseOrderDetails)) {
                logger.warn("未找到有效的订单明细记录，订单明细IDs={}", detailIds);
                return;
            }

            if(Objects.equals(Boolean.TRUE,manually)){
                // 检查订单明细是否可以更新
                if (!checkOrderDetailsDeliveryInfoSyncStatusCanUpdate(purchaseOrderDetails)) {
                    throw new BizException(Code.ERROR,"存在收货信息正在同步中或已同步的订单明细，无法批量更新");
                }
            }

            logger.info("查询到{}条需要处理的订单明细记录", purchaseOrderDetails.size());

            // 2. 处理每条订单明细
            int updateCount = processOrderDetails(purchaseOrderDetails, deliveryAddress, consignee, contactPhone, manually);

            logger.info("批量更新订单收货地址完成，成功更新{}条记录", updateCount);
        } catch (BizException e){
            throw e;
        }catch (Exception e) {
            logger.error("批量更新订单收货地址发生异常: {}", e.getMessage(), e);
            throw new BizException(Code.ERROR, "批量更新订单收货地址失败", e);
        }
    }

    private List<PurchaseOrderDetail> queryValidOrderDetails(List<Long> detailIds) {
        PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
        example.createCriteria()
               .andDeletedFlagEqualTo(Boolean.FALSE)
               .andIdIn(detailIds);
        return purchaseOrderDetailMapper.selectByExample(example);
    }

    private int processOrderDetails(List<PurchaseOrderDetail> purchaseOrderDetails, 
                                  String newDeliveryAddress, 
                                  String newConsignee, 
                                  String newContactPhone, 
                                  Boolean manually) {
        int updateCount = 0;
        for (PurchaseOrderDetail detail : purchaseOrderDetails) {
            try {
                updateCount += processOrderDetail(detail, newDeliveryAddress, newConsignee, newContactPhone, manually);

            } catch (Exception e) {
                logger.error("处理订单明细[{}]时发生异常: {}", detail.getId(), e.getMessage(), e);
                throw new BizException(Code.ERROR, String.format("订单明细[%s]更新失败", detail.getId()), e);
            }
        }
        if(ListUtils.isNotEmpty(purchaseOrderDetails) && updateCount > 0){
            // 添加到订单修改待同步队列
            insertResendExecuteRecordByDeliveryInfoEdit(purchaseOrderDetails.get(0).getPurchaseOrderId());
        }
        return updateCount;
    }

    private int processOrderDetail(PurchaseOrderDetail detail, 
                                   String newDeliveryAddress, 
                                   String newConsignee, 
                                   String newContactPhone, 
                                   Boolean manually) {
        Long detailId = detail.getId();
        long historyCount = purchaseOrderDetailDeliveryAddressHistoryService.getHistoryCountByOrderDetailId(detailId);

        // 首次更新时保存初始记录
        if (historyCount == 0) {
            logger.info("订单明细[{}]首次记录收货信息，保存初始记录", detailId);
            // 保存初始记录，原始值使用当前值（因为是首次记录）
            saveDeliveryHistory(detailId, newDeliveryAddress, newConsignee, newContactPhone,
                    detail.getDeliveryAddress(), detail.getConsignee(), detail.getContactPhone());
            
            // 如果是首次记录且当前值与新值相同，则不需要进一步更新
            boolean needUpdate = !Objects.equals(detail.getDeliveryAddress(), newDeliveryAddress)
                    || !Objects.equals(detail.getConsignee(), newConsignee)
                    || !Objects.equals(detail.getContactPhone(), newContactPhone);
                    
            if (!needUpdate) {
                logger.info("订单明细[{}]收货信息未发生变更，仅保存初始记录", detailId);
                return 0;
            }
            
            logger.info("订单明细[{}]首次记录且需要更新收货信息", detailId);
            
            // 首次记录且需要更新，直接进入更新流程
            if (!Boolean.TRUE.equals(manually)) {
                return 0;
            }
            
            // 更新订单明细
            return updateOrderDetail(detail, newDeliveryAddress, newConsignee, newContactPhone);
        }

        // 非首次更新，检查是否需要更新
        boolean needUpdate = !Objects.equals(detail.getDeliveryAddress(), newDeliveryAddress)
                || !Objects.equals(detail.getConsignee(), newConsignee)
                || !Objects.equals(detail.getContactPhone(), newContactPhone);

        if (!needUpdate) {
            logger.info("订单明细[{}]收货信息未发生变更，无需更新", detailId);
            return 0;
        }

        logger.info("订单明细[{}]收货信息发生变更，准备更新", detailId);
        
        // 保存变更历史
        saveDeliveryHistory(detailId, newDeliveryAddress, newConsignee, newContactPhone,
                detail.getDeliveryAddress(), detail.getConsignee(), detail.getContactPhone());

        if (!Boolean.TRUE.equals(manually)) {
            return 0;
        }

        // 更新订单明细
        return updateOrderDetail(detail, newDeliveryAddress, newConsignee, newContactPhone);
    }

    private void saveDeliveryHistory(Long detailId, 
                                   String newAddress, String newConsignee, String newPhone,
                                   String oldAddress, String oldConsignee, String oldPhone) {
        purchaseOrderDetailDeliveryAddressHistoryService.save(
                Lists.newArrayList(detailId),
                newAddress, newConsignee, newPhone,
                oldAddress, oldConsignee, oldPhone
        );
    }

    private int updateOrderDetail(PurchaseOrderDetail detail,
                                  String newDeliveryAddress,
                                  String newConsignee,
                                  String newContactPhone) {

        // 更新订单明细信息
        detail.setSyncDeliveryInfoStatus(SyncDeliveryInfoStatusEnum.SYNC_PREPARE.code());
        detail.setDeliveryAddress(newDeliveryAddress);
        detail.setConsignee(newConsignee);
        detail.setContactPhone(newContactPhone);

        int result = purchaseOrderDetailMapper.updateByPrimaryKeySelective(detail);
        if (result > 0) {
            logger.info("订单明细[{}]更新成功", detail.getId());
            return 1;
        } else {
            logger.error("订单明细[{}]更新失败", detail.getId());
            return 0;
        }

    }

    @Override
    public void batchUpdateSyncDeliveryInfoStatusByOrderId(List<Long> detailIds, Integer syncDeliveryInfoStatus,Long userId,Integer preDeliveryInfoStatus) {
        purchaseOrderDetailExtMapper.batchUpdateSyncDeliveryInfoStatusByOrderId(detailIds,syncDeliveryInfoStatus, userId,preDeliveryInfoStatus);
    }

    /**
     * 检查订单明细是否可以更新收货信息
     *
     * @param purchaseOrderDetails 订单明细列表
     * @return true-可以更新，false-不可更新
     */
    private boolean checkOrderDetailsDeliveryInfoSyncStatusCanUpdate(List<PurchaseOrderDetail> purchaseOrderDetails) {
        if (ListUtils.isEmpty(purchaseOrderDetails)) {
            logger.warn("订单明细列表为空，无法进行更新检查");
            return false;
        }

        List<PurchaseOrderDetail> invalidDetails = purchaseOrderDetails.stream()
                .filter(detail -> {
                    Integer syncStatus = detail.getSyncDeliveryInfoStatus();
                    if (syncStatus == null) {
                        logger.warn("订单明细[{}]的同步状态为空", detail.getId());
                        return true;
                    }

                    boolean isValidStatus = SyncDeliveryInfoStatusEnum.UNSYNC.code().equals(syncStatus)
                            || SyncDeliveryInfoStatusEnum.SYNC_FAILED.code().equals(syncStatus)
                            || SyncDeliveryInfoStatusEnum.SYNCED.code().equals(syncStatus);

                    if (!isValidStatus) {
                        String statusDesc = SyncDeliveryInfoStatusEnum.getValueByCode(syncStatus);
                        logger.warn("订单明细[{}]当前同步状态为[{}]，不允许更新收货信息",
                            detail.getId(),
                            statusDesc != null ? statusDesc : syncStatus);
                    }

                    return !isValidStatus;
                })
                .collect(Collectors.toList());

        if (!invalidDetails.isEmpty()) {
            String invalidIds = invalidDetails.stream()
                    .map(detail -> String.valueOf(detail.getId()))
                    .collect(Collectors.joining(","));

            logger.warn("以下订单明细的收货信息正在同步中，终止批量更新操作，订单明细IDs={}", invalidIds);
            return false;
        }

        return true;
    }

    @Override
    public void syncOrderDetailDeliveryInfo(Long id) {
        PurchaseOrderDetail purchaseOrderDetail = purchaseOrderDetailMapper.selectByPrimaryKey(id);
        purchaseOrderDetail.setSyncDeliveryInfoStatus(SyncDeliveryInfoStatusEnum.SYNC_PREPARE.getCode());
        purchaseOrderDetailMapper.updateByPrimaryKeySelective(purchaseOrderDetail);
        // 添加到订单修改待同步队列
        insertResendExecuteRecordByDeliveryInfoEdit(purchaseOrderDetail.getPurchaseOrderId());
    }

    private void insertResendExecuteRecordByDeliveryInfoEdit(Long id){

        List<PurchaseOrderDto> dtos = new ArrayList<>();
        ResendExecute resendExecute = new ResendExecute();
        resendExecute.setBusinessType(BusinessTypeEnums.PURCHASE_ORDER_CHANGE.getCode());
        resendExecute.setApplyNo(id.toString());
        resendExecute.setBatch(Boolean.TRUE);
        resendExecute.setDeletedFlag(Boolean.FALSE);
        PurchaseOrderDto waitPushOrderDto = purchaseOrderService.buildPushOrderDto(id, null);
        waitPushOrderDto.setEsbDataSource(BusinessTypeEnums.PURCHASE_ORDER_CHANGE.getCode());
        String errorMsg = checkWaitPushOrderDto(waitPushOrderDto);
        if (StringUtils.isEmpty(errorMsg)) {
            dtos.add(waitPushOrderDto);
            //同步到SDP
            EsbResponse esbResponse = sdpService.callGERPPoStandardPo(dtos, true);
            resendExecute.setEsbSerialNo(null != esbResponse.getData() ? String.valueOf(esbResponse.getData()) : null);
            resendExecute.setStatus(Objects.equals(esbResponse.getResponsecode(), ResponseCodeEnums.SUCESS.getCode()) ?
                    CommonStatus.DONE.getCode() : CommonStatus.ERROR.getCode());
            List<PurchaseOrderDetailDto> purchaseOrderDetailDtos = waitPushOrderDto.getPurchaseOrderDetailDtos();
            for (PurchaseOrderDetailDto purchaseOrderDetailDto : purchaseOrderDetailDtos) {
                purchaseOrderDetailDto.setSyncDeliveryInfoStatus(Objects.equals(esbResponse.getResponsecode(), ResponseCodeEnums.SUCESS.getCode())?
                        SyncDeliveryInfoStatusEnum.SYNCED.getCode():SyncDeliveryInfoStatusEnum.SYNC_FAILED.getCode());
                purchaseOrderDetailMapper.updateByPrimaryKeySelective(purchaseOrderDetailDto);
            }
        } else {
            resendExecute.setStatus(CommonStatus.ERROR.getCode());
            resendExecute.setResponMsg(errorMsg);
        }
        resendExecuteService.insert(resendExecute);
    }


    private String checkWaitPushOrderDto(PurchaseOrderDto waitPushOrderDto) {
        String errorMsg = "";
        if (waitPushOrderDto.getOuId() == null) {
            errorMsg = errorMsg + "无法查获业务实体ID；";
        }
        if (StringUtils.isEmpty(waitPushOrderDto.getOrderType())) {
            errorMsg = errorMsg + "请联系IT核对组织参数“采购订单类型的配置”；";
        }

        List<PurchaseOrderDetailDto> waitPushOrderDetailDtos = waitPushOrderDto.getPurchaseOrderDetailDtos();
        if (CollectionUtils.isEmpty(waitPushOrderDto.getPurchaseOrderDetailDtos())) {
            errorMsg = errorMsg + "订单行丢失；";
            return errorMsg;
        }
        if (waitPushOrderDetailDtos.stream().anyMatch(s -> StringUtils.isEmpty(s.getSecondaryInventoryName()))) {
            errorMsg = errorMsg + "无法查获订单行对应接受仓子库存代码；";
        }
        if (waitPushOrderDetailDtos.stream().anyMatch(s -> s.getOrganizationId() == null)) {
            errorMsg = errorMsg + "无法查获订单行库存组织ID；";
        }
        if (waitPushOrderDetailDtos.stream().anyMatch(s -> StringUtils.isEmpty(s.getProjectNum()))) {
            errorMsg = errorMsg + "无法查获订单行项目号；";
        }
        return errorMsg;
    }
}
