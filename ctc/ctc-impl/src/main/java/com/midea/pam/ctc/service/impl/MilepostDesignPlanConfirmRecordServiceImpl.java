package com.midea.pam.ctc.service.impl;

import cn.hutool.core.bean.copier.BeanCopier;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.IdEntity;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanConfirmRecordDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanConfirmRecordRelationDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDto;
import com.midea.pam.common.ctc.entity.ErpCodeRule;
import com.midea.pam.common.ctc.entity.ErpCodeRuleExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanChangeRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanChangeRecordExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecordExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecordRelation;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecordExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ReceiptClaimContractRel;
import com.midea.pam.common.ctc.entity.ReceiptClaimContractRelExample;
import com.midea.pam.common.ctc.entity.TicketTasks;
import com.midea.pam.common.ctc.entity.TicketTasksDetail;
import com.midea.pam.common.ctc.entity.TicketTasksDetailExample;
import com.midea.pam.common.ctc.entity.TicketTasksExample;
import com.midea.pam.common.enums.*;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.MaterialCategoryEnum;
import com.midea.pam.ctc.common.enums.MilepostDesignPlanDetailModelStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.ErpCodeRuleMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanChangeRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanConfirmRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanPurchaseRecordRelationExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanSubmitRecordMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimContractRelMapper;
import com.midea.pam.ctc.mapper.TicketTasksDetailMapper;
import com.midea.pam.ctc.mapper.TicketTasksMapper;
import com.midea.pam.ctc.service.*;
import com.midea.pam.ctc.service.event.DesignPlanDetailConfirmApprovalEvent;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import com.sun.org.apache.xpath.internal.operations.Bool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class MilepostDesignPlanConfirmRecordServiceImpl implements MilepostDesignPlanConfirmRecordService {

    private static Logger logger = LoggerFactory.getLogger(MilepostDesignPlanConfirmRecordServiceImpl.class);

    private final static Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    private final static Long MAX_WAIT_TIME_SYNC = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private MilepostDesignPlanConfirmRecordMapper milepostDesignPlanConfirmRecordMapper;

    @Resource
    private MilepostDesignPlanConfirmRecordRelationService milepostDesignPlanConfirmRecordRelationService;

    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;

    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;

    @Resource
    private ProjectMilepostService projectMilepostService;

    @Resource
    private ProjectService projectService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private MaterialExtService materialExtService;

    @Resource
    private MilepostDesignPlanSubmitRecordMapper milepostDesignPlanSubmitRecordMapper;

    @Resource
    private MilepostDesignPlanChangeRecordMapper milepostDesignPlanChangeRecordMapper;

    @Resource
    private ReceiptClaimContractRelMapper receiptClaimContractRelMapper;

    @Resource
    private ProjectTypeMapper projectTypeMapper;

    @Resource
    OrganizationCustomDictService organizationCustomDictService;

    @Resource
    private TicketTasksMapper ticketTasksMapper;

    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private TicketTasksDetailMapper ticketTasksDetailMapper;

    @Resource
    private ErpCodeRuleMapper erpCodeRuleMapper;


    @Resource
    private ProjectTypeService projectTypeService;

    @Resource
    private MilepostDesignPlanPurchaseRecordRelationExtMapper milepostDesignPlanPurchaseRecordRelationExtMapper;

    @Override
    public MilepostDesignPlanConfirmRecordDto add(MilepostDesignPlanConfirmRecordDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        MilepostDesignPlanConfirmRecord entity = BeanConverter.copy(dto, MilepostDesignPlanConfirmRecord.class);
        milepostDesignPlanConfirmRecordMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public MilepostDesignPlanConfirmRecordDto update(MilepostDesignPlanConfirmRecordDto dto) {
        if (!dto.getStatus().equals(CheckStatus.MODEL_CONFIRMED_DELETE.code())) {
            dto.setDeletedFlag(DeletedFlag.VALID.code());
        }
        MilepostDesignPlanConfirmRecord entity = BeanConverter.copy(dto, MilepostDesignPlanConfirmRecord.class);
        milepostDesignPlanConfirmRecordMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public MilepostDesignPlanConfirmRecordDto save(MilepostDesignPlanConfirmRecordDto dto, Long userBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
            return this.add(dto);
        } else {
            dto.setUpdateBy(userBy);
            return this.update(dto);
        }
    }

    @Override
    public MilepostDesignPlanConfirmRecordDto getById(Long id) {
        if (id == null) {
            return null;
        }
        MilepostDesignPlanConfirmRecord entity = milepostDesignPlanConfirmRecordMapper.selectByPrimaryKey(id);
        MilepostDesignPlanConfirmRecordDto dto = BeanConverter.copy(entity, MilepostDesignPlanConfirmRecordDto.class);
        packageDto(dto);
        return dto;
    }

    @Override
    public List<MilepostDesignPlanConfirmRecordDto> selectList(MilepostDesignPlanConfirmRecordDto query) {
//        List<MilepostDesignPlanConfirmRecord> list = milepostDesignPlanConfirmRecordMapper.selectByExample(buildCondition(query));
//        List<MilepostDesignPlanConfirmRecordDto> dtos = BeanConverter.copy(list, MilepostDesignPlanConfirmRecordDto.class);
//        for (MilepostDesignPlanConfirmRecordDto dto : dtos) {
//            packageDto(dto);
//        }
//        return dtos;

        return milepostDesignPlanConfirmRecordMapper.selectByExample(buildCondition(query)).stream().map(m -> {
            MilepostDesignPlanConfirmRecordDto copy = BeanCopier.create(m, new MilepostDesignPlanConfirmRecordDto(), CopyOptions.create()).copy();
            packageDto(copy);
            return copy;
        }).collect(Collectors.toList());
    }

    @Override
    public PageInfo<MilepostDesignPlanConfirmRecordDto> selectPage(MilepostDesignPlanConfirmRecordDto query) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MilepostDesignPlanConfirmRecordDto saveConfirmRecordWithRecursiveDetail(MilepostDesignPlanConfirmRecordDto dto, Long userBy) {

        Long projectId = dto.getProjectId();
        Long milepostId = dto.getMilepostId();
        List<Long> designPlanDetailIds = dto.getMilepostDesignPlanConfirmRecordRelationDtos().stream()
                        .map(MilepostDesignPlanConfirmRecordRelationDto::getDesignPlanDetailId).collect(Collectors.toList());
        Guard.notNull(projectId, "项目ID不能为空");
        Guard.notNull(milepostId, "里程牌ID不能为空");
        Guard.notNullOrEmpty(designPlanDetailIds, "详细设计ID不能为空");
        //分布式锁KEY
        String lockName = String.format("MilepostDesignPlanPurchaseRecordServiceImpl_savePurchaseRecordWithRecursiveDetail_%s_%s", projectId, milepostId);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");

        try {
            if(DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 校验该里程牌的设计方案中是否存在已确认的物料
                List<String> pamCodeList = milepostDesignPlanPurchaseRecordRelationExtMapper.checkMaterialConfirmation(projectId, milepostId, designPlanDetailIds);
                if(ListUtils.isNotEmpty(pamCodeList)){
                    String result = String.join(",", pamCodeList);
                    logger.error("该里程牌的设计方案中存在已确认的物料，提交参数:{}，已确认的物料：{}",JSON.toJSONString(dto),result);
                    throw new BizException(500,"物料PAM编码："+ result +"，已确认或确认中，请勿重复确认");
                }
                pamCodeList = milepostDesignPlanPurchaseRecordRelationExtMapper.checkDesignPlanConfirmation(projectId, milepostId, designPlanDetailIds);
                if(ListUtils.isNotEmpty(pamCodeList)){
                    String result = String.join(",", pamCodeList);
                    logger.error("进度确认检查-该里程牌的设计方案中存在已确认的物料，提交参数:{}，已确认的物料：{}",JSON.toJSONString(dto),result);
                    throw new BizException(500,"物料PAM编码："+ result +"，已确认或确认中，请勿重复确认");
                }

                checkParams(dto);
                List<MilepostDesignPlanConfirmRecordRelationDto> relationDtos = dto.getMilepostDesignPlanConfirmRecordRelationDtos();
                dto.setProcessName(generateConfirmRecordProcessName(dto.getProjectId()));
                dto.setConfirmBy(userBy);
                if (dto.getProjectSubmit() == null) {
                    dto.setProjectSubmit(Boolean.FALSE);
                }
                MilepostDesignPlanConfirmRecordDto resultDto = this.save(dto, userBy);

                // 查询当前使用单位的物料编码规则
                String materialCodeRule = materialExtService.getMaterialCodeRule(SystemContext.getUnitId());
                ErpCodeRuleExample erpCodeRuleExample = new ErpCodeRuleExample();
                ErpCodeRuleExample.Criteria criteria = erpCodeRuleExample.createCriteria();
                criteria.andDeletedFlagEqualTo(Boolean.FALSE).andRuleNameEqualTo(materialCodeRule);
                List<ErpCodeRule> erpCodeRuleList = erpCodeRuleMapper.selectByExample(erpCodeRuleExample);
                Map<String, List<ErpCodeRule>> erpCodeRuleMap = new HashMap<>();

                if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
                    erpCodeRuleMap.putAll(erpCodeRuleList.stream().collect(Collectors.groupingBy(e ->
                            getConfirmRecordMaterialERPCodeRuleKeyKUNSHANKey(e.getCodingMiddleclass(), e.getCodingSubclass()))));
                } else if (MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
                    erpCodeRuleMap.putAll(erpCodeRuleList.stream().collect(Collectors.groupingBy(e ->
                            getConfirmRecordMaterialERPCodeRuleKeyKUKA2022Key(e.getCodingClass(), e.getCodingMiddleclass(), e.getCodingSubclass()))));
                }

                //20230823 qile  fix BUG2023081879967
                //1、非WBS项目
                //2、项目类型设置中 没有勾选：计划交货日期的项目类型 的项目；满足这2个条件
                //在做部分确认和进度确认提交审批时，后端校验前端传过来的外购物料和看板物料是否有计划交货日期，没有，则报提示：物料：XXX缺少计划到货日期，请调整
                Project project = projectService.selectByPrimaryKey(dto.getProjectId());
                boolean wbsEnabled = project.getWbsEnabled();
                ProjectType projectType = projectTypeService.selectByPrimaryKey(project.getType());
                String budgetConfig = projectType.getBudgetConfig();
                JSONArray materiel = JSON.parseObject(budgetConfig).getJSONArray(BudgetConfigEnums.MATERIEL.getCode());
                // 项目类型设置的预算设置的字段设置的计划交付日期 的开关是否开启
                boolean isOpenDeliveryTime = false;
                for (int i = 0; i < materiel.size(); i++) {
                    JSONObject jsonObject = materiel.getJSONObject(i);
                    if (null == jsonObject) {
                        continue;
                    }
                    String code = Optional.ofNullable(jsonObject.get("code")).map(Object::toString).orElse(null);
                    String value = Optional.ofNullable(jsonObject.get("value")).map(Object::toString).orElse(null);
                    if (Objects.equals("fieldSetByMaterial", code) && Objects.equals("[1]", value)) {
                        isOpenDeliveryTime = true;
                        break;
                    }
                }

                if (ListUtils.isNotEmpty(relationDtos)) {
                    for (MilepostDesignPlanConfirmRecordRelationDto relationDto : relationDtos) {
                        String pamCode = relationDto.getPamCode();
                        String materialCategory = relationDto.getMaterialCategory();
                        String materialClassification = relationDto.getMaterialClassification();
                        String codingMiddleClass = relationDto.getCodingMiddleClass();
                        String materielType = relationDto.getMaterielType();
                        Long designPlanDetailId = relationDto.getDesignPlanDetailId();
                        MilepostDesignPlanDetail designPlanDetail = milepostDesignPlanDetailMapper.selectByPrimaryKey(designPlanDetailId);
                        Boolean generateRequirement = designPlanDetail.getGenerateRequirement();

                        // 校验是否在编码规则维护的大中小类不存在
                        materialERPCodeRuleCheck(pamCode, materialCategory, materialClassification, codingMiddleClass, materielType, generateRequirement, erpCodeRuleMap,
                                materialCodeRule, wbsEnabled, isOpenDeliveryTime, designPlanDetail.getDeliveryTime());

                        List<MilepostDesignPlanDetail> sonPlanDetailList = milepostDesignPlanDetailExtMapper.selectByParentId(designPlanDetailId);
                        // 递归校验所有的下级以及下下级
                        milepostDesignPlanDetailCheck(sonPlanDetailList, erpCodeRuleMap, materialCodeRule, wbsEnabled, isOpenDeliveryTime);
                        relationDto.setConfirmRecordId(resultDto.getId());
                    }
                    milepostDesignPlanConfirmRecordRelationService.batchSave(relationDtos, userBy);
                }
                return resultDto;
            }
        }catch (Exception e){
            logger.error("详细设计方案进度确认提前采购提交加锁失败", e);
            throw e;
        }finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
        return null;
    }

    private void milepostDesignPlanDetailCheck(List<MilepostDesignPlanDetail> planDetailList, Map<String, List<ErpCodeRule>> erpCodeRuleMap, String materialCodeRule,
                                             Boolean wbsEnabled,Boolean isOpenDeliveryTime) {
        if (CollectionUtils.isEmpty(planDetailList)) {
            return;
        }
        for (MilepostDesignPlanDetail detail : planDetailList) {
            String pamCode = detail.getPamCode();
            String materialCategory = detail.getMaterialCategory();
            String materialClassification = detail.getMaterialClassification();
            String codingMiddleClass = detail.getCodingMiddleClass();
            String materielType = detail.getMaterielType();
            Boolean generateRequirement = detail.getGenerateRequirement();
            materialERPCodeRuleCheck(pamCode, materialCategory, materialClassification, codingMiddleClass, materielType, generateRequirement, erpCodeRuleMap,
                    materialCodeRule,wbsEnabled,isOpenDeliveryTime,detail.getDeliveryTime());
            List<MilepostDesignPlanDetail> sonPlanDetailList = milepostDesignPlanDetailExtMapper.selectByParentId(detail.getId());
            milepostDesignPlanDetailCheck(sonPlanDetailList, erpCodeRuleMap, materialCodeRule,wbsEnabled,isOpenDeliveryTime);
        }
    }

    private void materialERPCodeRuleCheck(String pamCode, String materialCategory, String materialClassification, String codingMiddleClass,
                                          String materielType, Boolean generateRequirement, Map<String, List<ErpCodeRule>> erpCodeRuleMap,
                                          String materialCodeRule, Boolean wbsEnabled,Boolean isOpenDeliveryTime,Date deliveryTime) {
        if ((Objects.equals(materialCategory, "外购物料") || Objects.equals(materialCategory, "看板物料")) && (!Boolean.TRUE.equals(generateRequirement))) {
            if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
                if (StringUtils.isNotEmpty(codingMiddleClass) && StringUtils.isNotEmpty(materielType)
                        && !erpCodeRuleMap.containsKey(getConfirmRecordMaterialERPCodeRuleKeyKUNSHANKey(codingMiddleClass, materielType))) {
                    throw new ApplicationBizException(String.format("详细设计数据的pam编码：%s的物料中类：%s，物料小类：%s的物料编码规则：%s不存在",
                            pamCode, codingMiddleClass, materielType, materialCodeRule));
                }
            } else if (MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
                if (StringUtils.isNotEmpty(materialClassification) && StringUtils.isNotEmpty(codingMiddleClass) && StringUtils.isNotEmpty(materielType)
                        && !erpCodeRuleMap.containsKey(getConfirmRecordMaterialERPCodeRuleKeyKUKA2022Key(materialClassification, codingMiddleClass, materielType))) {
                    throw new ApplicationBizException(String.format("详细设计数据的pam编码：%s的物料大类：%s，物料中类：%s，物料小类：%s的物料编码规则：%s不存在",
                            pamCode, materialClassification, codingMiddleClass, materielType, materialCodeRule));
                }
            }
            if(!wbsEnabled && !isOpenDeliveryTime && Objects.isNull(deliveryTime)){
                throw new ApplicationBizException(String.format("物料：%s缺少物料到货日期，请调整",
                        pamCode));
            }
        }
    }

    private String getConfirmRecordMaterialERPCodeRuleKeyKUKA2022Key(String codingClass, String codingMiddleclass, String codingSubclass) {
        return String.format("getConfirmRecordMaterialERPCodeRuleKeyKUKA2022Key_%s_%s_%s", codingClass, codingMiddleclass, codingSubclass);
    }

    private String getConfirmRecordMaterialERPCodeRuleKeyKUNSHANKey(String codingMiddleclass, String codingSubclass) {
        return String.format("getConfirmRecordMaterialERPCodeRuleKeyKUNSHANKey_%s_%s_", codingMiddleclass, codingSubclass);
    }

    private void checkParams(MilepostDesignPlanConfirmRecordDto dto) {
        Asserts.notEmpty(dto.getProjectId(), ErrorCode.CTC_PROJECT_ID_NULL);

        Asserts.notEmpty(dto.getMilepostDesignPlanConfirmRecordRelationDtos(), ErrorCode.CTC_DESIGN_PLAN_CONFIRM_NOT_NULL);
        //判断是否还有详细设计中物料有审批中的，如果有这不能进行模组确认
        List<Long> designPlanDetailIds = dto.getMilepostDesignPlanConfirmRecordRelationDtos().stream().map(MilepostDesignPlanConfirmRecordRelation::getDesignPlanDetailId).collect(Collectors.toList());
        Asserts.notEmpty(designPlanDetailIds, ErrorCode.CTC_DESIGN_PLAN_CONFIRM_NOT_NULL);
        Project project = projectService.selectByPrimaryKey(dto.getProjectId());
        List<Long> listId = new ArrayList<Long>();
        Set<String> valueSet = organizationCustomDictService.queryByName("模组确认时需检查回款的项目类型", SystemContext.getUnitId(),
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.COMPANY.code()));
        ProjectType projectType = projectTypeMapper.selectByPrimaryKey(project.getType());
        if (!valueSet.isEmpty() && valueSet.size() > 0) {
            for (String value : valueSet) {
                if (value.contains(projectType.getName())) {
                    if (StringUtils.isNotEmpty(project.getContractId() + "")) {
                        listId = this.findReceiptClaimContractRel(project.getContractId());
                    }
                    Asserts.isTrue(ListUtils.isNotEmpty(listId), ErrorCode.CTC_DESIGN_PLAN_RECEIPTCLAIMCONTRACT);
                }
            }
        }
        List<MilepostDesignPlanSubmitRecord> submitRecords = this.findMilepostDesignPlanSubmitRecords(designPlanDetailIds);
        List<MilepostDesignPlanChangeRecord> changeRecords = this.findMilepostDesignPlanChangeRecords(designPlanDetailIds);
        Asserts.isTrue(CollectionUtils.isEmpty(submitRecords), ErrorCode.CTC_DESIGN_PLAN_APPROVALING_NOT_NULL);
        Asserts.isTrue(CollectionUtils.isEmpty(changeRecords), ErrorCode.CTC_DESIGN_PLAN_APPROVALING_NOT_NULL);
        for (Long designPlanDetailId : designPlanDetailIds) {
            List<MilepostDesignPlanDetailDto> designPlanDetailDtoList = milepostDesignPlanDetailExtMapper.selectModule(designPlanDetailId);
            if (ListUtils.isNotEmpty(designPlanDetailDtoList)) {
                if (!designPlanDetailDtoList.get(0).getDeletedFlag()) {
                    Asserts.isTrue(!ObjectUtils.isEmpty(designPlanDetailDtoList.get(0).getDeliveryTime()), ErrorCode.CTC_DELIVERY_TIME_IS_NOT_NULL);
                }
            }
            /** 【项目类型】详细设计是否已完成(是：立项通过则模组确认)，这种场景下，有可能是立项时新增的模组，不校验模组层审批状态 **/
            if (!Objects.equals(dto.getConfirmRecordFlag(), Boolean.TRUE)) {
                List<MilepostDesignPlanDetailDto> designPlanDetails = milepostDesignPlanDetailExtMapper.selectById(designPlanDetailId);
                if (ListUtils.isNotEmpty(designPlanDetails)) {
                    for (MilepostDesignPlanDetailDto plan : designPlanDetails) {
                        //草稿
                        Asserts.isTrue(CheckStatus.DRAFT.code() != plan.getStatus(), ErrorCode.CTC_DESIGNPLAN_DRFT_IS_NOT_NULL);
                        //草稿审批中
                        Asserts.isTrue(CheckStatus.CHECKING.code() != plan.getStatus(), ErrorCode.CTC_DESIGNPLAN_DRFT_IS_NOT_NULL);
                        //草稿被驳回
                        //Asserts.isTrue(CheckStatus.REJECT.code() != plan.getStatus(), ErrorCode.CTC_DESIGNPLAN_DRFT_IS_NOT_NULL);
                    }
                }
            }
        }

    }

    public List<Long> findReceiptClaimContractRel(Long contractId) {
        ReceiptClaimContractRelExample example = new ReceiptClaimContractRelExample();
        ReceiptClaimContractRelExample.Criteria criteria = example.createCriteria();
        criteria.andContractIdEqualTo(contractId);
        criteria.andDeletedFlagEqualTo(0);
        List<ReceiptClaimContractRel> list = receiptClaimContractRelMapper.selectByExample(example);
        List<Long> listId = new ArrayList<Long>();
        if (ListUtils.isNotEmpty(list)) {
            for (ReceiptClaimContractRel rel : list) {
                BigDecimal allocatedAmount = rel.getAllocatedAmount();
                if (allocatedAmount != null) {
                    if (allocatedAmount.compareTo(BigDecimal.ZERO) > 0) {
                        listId.add(rel.getId());
                    }
                }
            }
        }
        return listId;
    }

    public List<MilepostDesignPlanChangeRecord> findMilepostDesignPlanChangeRecords(List<Long> designPlanDetailIds) {
        MilepostDesignPlanChangeRecordExample example1 = new MilepostDesignPlanChangeRecordExample();
        MilepostDesignPlanChangeRecordExample.Criteria criteria = example1.createCriteria();
        criteria.andUploadPathIdIn(designPlanDetailIds);
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andStatusIn(Arrays.asList(CheckStatus.CHECKING.code(), CheckStatus.MDP_CHANGE_CHECKING.code(), CheckStatus.REJECT.code(), CheckStatus.RETURN.code()));
        return milepostDesignPlanChangeRecordMapper.selectByExample(example1);
    }

    public List<MilepostDesignPlanSubmitRecord> findMilepostDesignPlanSubmitRecords(List<Long> designPlanDetailIds) {
        MilepostDesignPlanSubmitRecordExample example = new MilepostDesignPlanSubmitRecordExample();
        MilepostDesignPlanSubmitRecordExample.Criteria criteria = example.createCriteria();
        criteria.andUploadPathIdIn(designPlanDetailIds);
        criteria.andProjectSubmitEqualTo(Boolean.FALSE);
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andStatusIn(Arrays.asList(CheckStatus.CHECKING.code(), CheckStatus.MDP_CHANGE_CHECKING.code(), CheckStatus.REJECT.code(), CheckStatus.RETURN.code()));
        return milepostDesignPlanSubmitRecordMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public MilepostDesignPlanConfirmRecordDto getConfirmRecordWithRelationById(Long id) {
        MilepostDesignPlanConfirmRecordDto dto = this.getById(id);
        packageDto(dto);

        MilepostDesignPlanConfirmRecordRelationDto relationQuery = new MilepostDesignPlanConfirmRecordRelationDto();
        relationQuery.setConfirmRecordId(dto.getId());
        dto.setMilepostDesignPlanConfirmRecordRelationDtos(milepostDesignPlanConfirmRecordRelationService.selectListWithDesignPlan(relationQuery));

        return dto;
    }

    //仅用于进度确认
    public void updateDeliveryTimeById(Date deliveryTime, Long id, Integer checkStatus) {
        logger.info("根据进度确认详细设计id设置交货时间" + id);
        milepostDesignPlanDetailExtMapper.updateDeliveryTimeById(deliveryTime, id);
        //状态改为确认状态
        if (null != checkStatus) {
            updateMilepostDesignPlanDetailModuleStatus(id, MilepostDesignPlanDetailModelStatus.CONFIRMED.code(), CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code());
        }
        /*List<MilepostDesignPlanDetailDto> designPlanDetailSons = milepostDesignPlanDetailService.findNotGenerateRequireDesignPlanDetails(id);
        for (MilepostDesignPlanDetailDto designPlanDetailSon : designPlanDetailSons) {
            if(null == designPlanDetailSon.getDeliveryTime()){
                updateDeliveryTimeById(deliveryTime, designPlanDetailSon.getId(),checkStatus);
            }else if(deliveryTime.getTime() < designPlanDetailSon.getDeliveryTime().getTime()){
                updateDeliveryTimeById(deliveryTime, designPlanDetailSon.getId(),checkStatus);
            }
        }*/
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andParentIdEqualTo(id).andDeletedFlagEqualTo(false);
        List<MilepostDesignPlanDetail> list = milepostDesignPlanDetailMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(list)) {
            List<MilepostDesignPlanDetailDto> dtos = BeanConverter.copy(list, MilepostDesignPlanDetailDto.class);
            for (MilepostDesignPlanDetailDto dto : dtos) {
                if (null == dto.getDeliveryTime()) {
                    updateDeliveryTimeById(deliveryTime, dto.getId(), checkStatus);
                } else {
                    updateDeliveryTimeById(dto.getDeliveryTime(), dto.getId(), checkStatus);
                }
            }
        }

    }

    //获取估价模组信息
    public MilepostDesignPlanDetailDto getModel(MilepostDesignPlanDetailDto p) {
        MilepostDesignPlanDetailDto model = new MilepostDesignPlanDetailDto();
        if (null != p.getParentId()) {
            MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
            example.createCriteria().andIdEqualTo(p.getParentId());
            List<MilepostDesignPlanDetail> list = milepostDesignPlanDetailMapper.selectByExample(example);
            MilepostDesignPlanDetailDto m = BeanConverter.copy(list.get(0), MilepostDesignPlanDetailDto.class);
            if (null != m.getProjectBudgetMaterialId() && null != m.getWhetherModel() && m.getWhetherModel()) {

            } else {
                m = getModel(m);
            }
        }

        return model;
    }

    public void setTicketTasksByDesignPlanDetail(MilepostDesignPlanDetailDto p, List<MilepostDesignPlanDetailDto> plan, Project project, TicketTasks ticketTasksHearder, MilepostDesignPlanDetailDto model) {
        //模组层
        if (null != p.getProjectBudgetMaterialId() && null != p.getWhetherModel() && p.getWhetherModel()) {
            model = p;
        } else if (null == model && p.getParentId() != null) {
            //反推模组
            model = getModel(p);
        }
        //装配件
        if (MaterialCategoryEnum.ASSEMBLY_PARTS.msg().equals(p.getMaterialCategory())) {
            TicketTasks tickTasks = new TicketTasks();
            tickTasks.setTicketTaskCode(project.getCode() + CacheDataUtils.generateSequence(4, project.getCode(), null));
            tickTasks.setTaskStatus(TicketTasksEnum.UNPUBLISH.code());
            tickTasks.setTheassemblyCode(p.getPamCode());  // 装配件编号就是PAM编码
            tickTasks.setTheassemblyDes(p.getMaterielDescr()); // 装配件描述就是物料描述
            tickTasks.setProjectBudgetMaterialId(p.getProjectBudgetMaterialId());
            tickTasks.setTicketPlanStartTime(p.getRequirementCreatDate()); // 如果是预算层的工单，取物料预算的计划开始日期
            tickTasks.setModuleDeliveryTime(p.getDeliveryTime()); // 取所属模组的计划交付日期
            tickTasks.setTicketPubishTime(new Date()); // 详细设计生成的工单需要维护开始日期才可以发布，取发布操作的日期
            tickTasks.setTicketCompleteTime(null);// 结项后自动完成工单，取结项审批通过的日期
            //详细设计生成的工单，取发布操作的人员
            ticketTasksHearder.setTicketPublishBy(p.getUpdateBy());
            if (null != model) {
                tickTasks.setModuleName(model.getName());//模组名
                tickTasks.setModuleCode(model.getPamCode());//模组号
            }
            tickTasks.setCreateAt(new Date());//工单创建时间
            tickTasks.setUpdateAt(new Date());//工单更新时间
            ticketTasksMapper.insert(ticketTasksHearder);
            ticketTasksHearder = tickTasks;
        }
        //如果是虚拟件
        if (MaterialCategoryEnum.VIRTUAL_PARTS.msg().equals(p.getMaterialCategory())) {
            //虚拟件沿用上级装配件
        }
        //外购物料
        if (MaterialCategoryEnum.PURCHASED_PARTS.msg().equals(p.getMaterialCategory())) {
            TicketTasksDetailExample detailExample = new TicketTasksDetailExample();
            detailExample.createCriteria().andMilepostDesignDetailIdEqualTo(p.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            List<TicketTasksDetail> ticketTasksDetailList = ticketTasksDetailMapper.selectByExample(detailExample);
            TicketTasksDetail ticketTasksDetail = new TicketTasksDetail();
            if (ListUtils.isNotEmpty(ticketTasksDetailList)) { // 对已经存在的任务工单行进行更新
                ticketTasksDetail = ticketTasksDetailList.get(0);
                ticketTasksDetail.setPamCode(p.getPamCode());
                ticketTasksDetail.setErpCode(p.getErpCode());
                ticketTasksDetail.setMaterielDescr(p.getMaterielDescr());
                ticketTasksDetail.setMaterialCategory(p.getMaterialCategory());
                ticketTasksDetail.setNumber(p.getNumber());
                ticketTasksDetail.setDeletedFlag(Boolean.FALSE);

                ticketTasksDetail.setUpdateAt(new Date());//工单更新时间
                ticketTasksDetailMapper.updateByPrimaryKeySelective(ticketTasksDetail);

            } else { // 非装配件的物料生成任务工单行
                ticketTasksDetail.setTicketTasksId(ticketTasksHearder.getId());
                ticketTasksDetail.setMilepostDesignDetailId(p.getId());
                ticketTasksDetail.setPamCode(p.getPamCode());
                ticketTasksDetail.setErpCode(p.getErpCode());
                ticketTasksDetail.setMaterielDescr(p.getMaterielDescr());
                ticketTasksDetail.setMaterialCategory(p.getMaterialCategory());
                ticketTasksDetail.setNumber(p.getNumber());
                ticketTasksDetail.setProjectBudgetMaterialId(p.getProjectBudgetMaterialId());
                ticketTasksDetail.setDeletedFlag(Boolean.FALSE);
                ticketTasksDetail.setCreateAt(new Date());//工单创建时间
                ticketTasksDetail.setUpdateAt(new Date());//工单更新时间
                ticketTasksDetailMapper.insert(ticketTasksDetail);
            }
        }
        List<MilepostDesignPlanDetailDto> sonDtos = p.getSonDtos();
        if (ListUtils.isNotEmpty(sonDtos)) {
            for (MilepostDesignPlanDetailDto s : sonDtos) {
                setTicketTasksByDesignPlanDetail(s, s.getSonDtos(), project, ticketTasksHearder, model);
            }
        }
    }

    public void createTicketTasksByDesignPlanDetail(List<MilepostDesignPlanDetailDto> plan, Project project) {
        for (MilepostDesignPlanDetailDto p : plan) {
            //没有项目信息则查询项目信息,保持尽量只查一次满足全局使用
            if (null == project) {
                project = projectMapper.selectByPrimaryKey(p.getProjectId());
            }
            //生成工单任务
            // 只有做完进度确认之后 再进行详设变更才会影响任务工单头和行
            if (null != p.getModuleStatus() && p.getModuleStatus() == 1) {
                TicketTasksExample tasksExample = new TicketTasksExample();
                tasksExample.createCriteria().andMilepostDesignDetailIdEqualTo(p.getId())
                        .andDeletedFlagEqualTo(Boolean.FALSE);
                List<TicketTasks> ticketTasks = ticketTasksMapper.selectByExample(tasksExample);

                TicketTasks ticketTasksHearder = new TicketTasks();
                if (ListUtils.isNotEmpty(ticketTasks)) {
                    ticketTasksHearder = ticketTasks.get(0);
                }
                setTicketTasksByDesignPlanDetail(p, p.getSonDtos(), project, ticketTasksHearder, null);
            }
        }

    }

    @Override
    public void updateBatchStatusForConfirmAsync(MilepostDesignPlanConfirmRecordDto dto, Long userBy) {
        applicationEventPublisher.publishEvent(new DesignPlanDetailConfirmApprovalEvent(this, dto, userBy));
    }

    @Override
    public String generateConfirmRecordProcessName(Long projectId) {
        Project project = projectService.selectByPrimaryKey(projectId);
        StringBuffer processName = new StringBuffer();
        processName.append(project.getName());
        processName.append("_模组确认_");
        processName.append(CacheDataUtils.generateSequence(Constants.CODE_CONFIRM_RECORD_PROCESS_LENGTH, Constants.SEQUENCE_TYPE_CONFIRM_RECORD_PROCESS));
        return processName.toString();
    }

    @Override
    public MilepostDesignPlanConfirmRecordDto saveMilepostDesignPlanConfirmRecord(MilepostDesignPlanDto dto) {
        //模组确认
        MilepostDesignPlanConfirmRecordDto confirmRecordDto = new MilepostDesignPlanConfirmRecordDto();
        List<MilepostDesignPlanConfirmRecordRelationDto> recordRelationDtos = new ArrayList<>();
        MilepostDesignPlanDetailExample planDetailExample = new MilepostDesignPlanDetailExample();
        planDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andWhetherModelEqualTo(Boolean.TRUE)
                .andProjectIdEqualTo(dto.getProjectId());
        List<MilepostDesignPlanDetail> parentPlanDetails = milepostDesignPlanDetailMapper.selectByExample(planDetailExample);
        if (ListUtils.isNotEmpty(parentPlanDetails)) {
            for (MilepostDesignPlanDetail designPlanDetailDto : parentPlanDetails) {
                MilepostDesignPlanConfirmRecordRelationDto confirmRecordRelationDto = new MilepostDesignPlanConfirmRecordRelationDto();
                confirmRecordRelationDto.setDesignPlanDetailId(designPlanDetailDto.getId());
                recordRelationDtos.add(confirmRecordRelationDto);
            }
            confirmRecordDto.setMilepostDesignPlanConfirmRecordRelationDtos(recordRelationDtos);
            confirmRecordDto.setMilepostId(dto.getMilepostId());
            confirmRecordDto.setProjectId(dto.getProjectId());
            confirmRecordDto.setProjectSubmit(Boolean.TRUE);
            return confirmRecordDto;
        }
        return null;
    }

    private MilepostDesignPlanConfirmRecordExample buildCondition(MilepostDesignPlanConfirmRecordDto query) {
        MilepostDesignPlanConfirmRecordExample example = new MilepostDesignPlanConfirmRecordExample();
        MilepostDesignPlanConfirmRecordExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());


        if (query.getId() != null) {
            criteria.andIdEqualTo(query.getId());
        }
        if (query.getProjectId() != null) {
            criteria.andProjectIdEqualTo(query.getProjectId());
        }
        if (query.getMilepostId() != null) {
            criteria.andMilepostIdEqualTo(query.getMilepostId());
        }
        if (query.getStatus() != null) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (query.getProjectSubmit() != null) {
            criteria.andProjectSubmitEqualTo(query.getProjectSubmit());
        }
        if (query.getStatusIsNull() != null) {
            if (query.getStatusIsNull()) {
                criteria.andStatusIsNull();
            } else {
                criteria.andStatusIsNotNull();
            }
        }

        return example;
    }

    void packageDto(MilepostDesignPlanConfirmRecordDto dto) {
        if (dto.getConfirmBy() != null) {
            UserInfo confirmer = CacheDataUtils.findUserById(dto.getConfirmBy());
            if (confirmer != null) {
                dto.setConfirmName(confirmer.getName());
            }
        }
    }

    /**
     * 更新里程碑状态
     *
     * @param milepostId
     * @param status
     */
    private void updateMilepostStatus(Long milepostId, Integer status) {
        ProjectMilepost projectMilepost = new ProjectMilepost();
        projectMilepost.setId(milepostId);
        projectMilepost.setStatus(status);
        projectMilepostService.updateByPrimaryKeySelective(projectMilepost);
    }

    /**
     * 更新里程碑详细方案状态
     *
     * @param milepostDesignPlanDetailId
     * @param moduleStatus
     */
    public void updateMilepostDesignPlanDetailModuleStatus(Long milepostDesignPlanDetailId, Integer moduleStatus, Integer status) {
        MilepostDesignPlanDetailDto designPlanDetailDto = new MilepostDesignPlanDetailDto();
        designPlanDetailDto.setId(milepostDesignPlanDetailId);
        designPlanDetailDto.setModuleStatus(moduleStatus);
        designPlanDetailDto.setStatus(status);
        designPlanDetailDto.setUpdateAt(new Date());
        //计进度确认，流程审批通过后 产生需求创建时间
        if (CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code().equals(status)) {
            designPlanDetailDto.setRequirementCreatDate(new Date());
        }
        milepostDesignPlanDetailService.save(designPlanDetailDto, null);
    }
}
