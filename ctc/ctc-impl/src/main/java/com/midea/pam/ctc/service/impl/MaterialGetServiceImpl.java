package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.dto.StorageInventoryDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.basedata.entity.StorageInventory;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.BookingCountDto;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.MaterialGetDetailDto;
import com.midea.pam.common.ctc.dto.MaterialGetDto;
import com.midea.pam.common.ctc.dto.MaterialGetProjectDto;
import com.midea.pam.common.ctc.dto.MaterialGetStatDto;
import com.midea.pam.common.ctc.dto.MaterialReturnDetailDto;
import com.midea.pam.common.ctc.dto.MaterialUnfetchQuantityDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetSummaryDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.dto.TicketTasksTreeDto;
import com.midea.pam.common.ctc.entity.FormalMaterialGet;
import com.midea.pam.common.ctc.entity.FormalMaterialGetAndReturn;
import com.midea.pam.common.ctc.entity.FormalMaterialGetAndReturnExample;
import com.midea.pam.common.ctc.entity.FormalMaterialGetExample;
import com.midea.pam.common.ctc.entity.MaterialGetDetail;
import com.midea.pam.common.ctc.entity.MaterialGetDetailExample;
import com.midea.pam.common.ctc.entity.MaterialGetHeader;
import com.midea.pam.common.ctc.entity.MaterialGetHeaderExample;
import com.midea.pam.common.ctc.entity.MaterialReturnDetail;
import com.midea.pam.common.ctc.entity.MaterialReturnHeader;
import com.midea.pam.common.ctc.entity.MaterialReturnHeaderExample;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectActivity;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.entity.TicketTasks;
import com.midea.pam.common.ctc.entity.TicketTasksDetail;
import com.midea.pam.common.ctc.entity.TicketTasksDetailExample;
import com.midea.pam.common.ctc.entity.TicketTasksExample;
import com.midea.pam.common.ctc.excelVo.MaterialGetDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.MaterialGetHeaderExcelVo;
import com.midea.pam.common.ctc.vo.PurchaseContractVO;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.MaterialGetStatus;
import com.midea.pam.common.enums.MaterialTransferEnums;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ProjectSource;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.enums.StorageInventoryType;
import com.midea.pam.common.enums.SubBusinessTypeEnums;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.Builder;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.MapUtil;
import com.midea.pam.common.util.ObjUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.impl.OssService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.job.MaterialActualCostSyncJob;
import com.midea.pam.ctc.mapper.FormalMaterialGetAndReturnExtMapper;
import com.midea.pam.ctc.mapper.FormalMaterialGetAndReturnMapper;
import com.midea.pam.ctc.mapper.FormalMaterialGetExtMapper;
import com.midea.pam.ctc.mapper.FormalMaterialGetMapper;
import com.midea.pam.ctc.mapper.MaterialGetDetailExtMapper;
import com.midea.pam.ctc.mapper.MaterialGetDetailMapper;
import com.midea.pam.ctc.mapper.MaterialGetHeaderExtMapper;
import com.midea.pam.ctc.mapper.MaterialGetHeaderMapper;
import com.midea.pam.ctc.mapper.MaterialReturnDetailMapper;
import com.midea.pam.ctc.mapper.MaterialReturnHeaderMapper;
import com.midea.pam.ctc.mapper.MaterialTransferExtMapper;
import com.midea.pam.ctc.mapper.ProjectActivityExtMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectProfitMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryMapper;
import com.midea.pam.ctc.mapper.TemporaryMaterialGetAndReturnExtMapper;
import com.midea.pam.ctc.mapper.TemporaryMaterialGetAndReturnMapper;
import com.midea.pam.ctc.mapper.TicketTasksDetailMapper;
import com.midea.pam.ctc.mapper.TicketTasksMapper;
import com.midea.pam.ctc.remote.RemoteBaseDataService;
import com.midea.pam.ctc.service.AgencySynService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MaterialGetService;
import com.midea.pam.ctc.service.MaterialReturnService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.OrganizationRelExtService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.service.ResendExecuteService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.ctc.service.TicketTasksService;
import com.midea.pam.ctc.service.event.MaterialGetWorkflowCallbackApprovalEvent;
import com.midea.pam.ctc.wbs.service.ProjectActivityService;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import com.midea.sdp.dto.common.SdpTradeResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description 领料模块服务
 * @date 2019-5-17
 */
public class MaterialGetServiceImpl implements MaterialGetService {

    private static Logger logger = LoggerFactory.getLogger(WorkingHourServiceImpl.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    OrganizationRelExtService organizationRelExtService;
    @Resource
    SdpCarrierServicel sdpCarrierServicel;
    @Resource
    private MaterialGetHeaderMapper headerMapper;
    @Resource
    private ProjectActivityService projectActivityService;
    @Resource
    private MaterialGetDetailMapper detailMapper;
    @Resource
    private MaterialGetDetailExtMapper detailExtMapper;
    @Resource
    private MaterialTransferExtMapper materialTransferExtMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private OssService ossService;
    @Resource
    private ProjectProfitMapper projectProfitMapper;
    @Resource
    private ProjectProfitService projectProfitService;
    @Autowired
    private BasedataExtService basedataExtService;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private RemoteBaseDataService remoteBaseDataService;
    @Resource
    ResendExecuteService resendExecuteService;
    @Resource
    private AgencySynService agencySynService;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectTypeService projectTypeService;
    @Resource
    private TicketTasksMapper ticketTasksMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MaterialGetHeaderMapper materialGetHeaderMapper;
    @Resource
    private MaterialGetHeaderExtMapper materialGetHeaderExtMapper;
    @Resource
    private MaterialGetDetailMapper materialGetDetailMapper;
    @Resource
    private TicketTasksDetailMapper ticketTasksDetailMapper;
    @Resource
    private TicketTasksService ticketTasksService;
    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private EsbService esbService;
    @Resource
    private FormalMaterialGetAndReturnExtMapper formalMaterialGetAndReturnExtMapper;
    @Resource
    private TemporaryMaterialGetAndReturnExtMapper temporaryMaterialGetAndReturnExtMapper;
    @Resource
    private MaterialGetDetailExtMapper materialGetDetailExtMapper;
    @Resource
    private MaterialReturnHeaderMapper materialReturnHeaderMapper;
    @Resource
    private FormalMaterialGetAndReturnMapper formalMaterialGetAndReturnMapper;
    @Resource
    private TemporaryMaterialGetAndReturnMapper temporaryMaterialGetAndReturnMapper;
    @Resource
    private MaterialReturnDetailMapper materialReturnDetailMapper;
    @Resource
    private FormalMaterialGetMapper formalMaterialGetMapper;
    @Resource
    private FormalMaterialGetExtMapper formalMaterialGetExtMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private MaterialReturnService materialReturnService;
    @Resource
    private ProjectWbsBudgetSummaryMapper projectWbsBudgetSummaryMapper;
    @Resource
    private ProjectWbsBudgetSummaryExtMapper projectWbsBudgetSummaryExtMapper;
    @Resource
    private ProjectActivityExtMapper projectActivityExtMapper;
    @Resource
    private Validator validator;
    @Resource
    private MaterialActualCostSyncJob materialActualCostSyncJob;
    @Resource
    private MilepostDesignPlanServiceImpl milepostDesignPlanService;
    @Resource
    private WorkflowCallbackService workflowCallbackService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;


    @Override
    public Boolean callBack(Long id, Boolean isSuccess, String msg, String actualCost) {
        //重复处理的数据,不做处理
        if (msg.contains("处理过")) {
            isSuccess = Boolean.TRUE;
            msg = "";
        }
        MaterialGetDetail backDetail = detailMapper.selectByPrimaryKey(id);
        MaterialGetDetailExample detailsExample = new MaterialGetDetailExample();
        detailsExample.createCriteria()
                .andHeaderIdEqualTo(backDetail.getHeaderId())
                .andDeletedFlagEqualTo(0)
                .andActualAmountGreaterThan(BigDecimal.ZERO);
        //无论成功与否都需要遍历全部的数据,判断状态,如果全成功则更新头表的状态数据
        //如果全部失败,则需要根据错误信息判断,头表的同步信息展示
        List<MaterialGetDetail> details = detailMapper.selectByExample(detailsExample);
        logger.info("获取的领料单详情为:{}", JSON.toJSONString(details));
        MaterialGetHeader header = new MaterialGetHeader();
        header.setId(backDetail.getHeaderId());
        AtomicInteger size = new AtomicInteger(0);
        if (!isSuccess) {

            if (PublicUtil.isNotEmpty(details)) {
                for (MaterialGetDetail detail : details) {
                    if (detail.getId().equals(backDetail.getId())) {
                        detail.setErpCode("3");//ERP同步状态(2-同步成功,3-同步失败,null-未同步)
                        detail.setErpMsg(msg);
                        detailMapper.updateByPrimaryKeySelective(detail);
                    }

                    //记录同步失败的数据大小
                    if (Objects.equals(detail.getErpCode(), "3")) {
                        size.incrementAndGet();
                    }
                }

            }

            header.setErpCode(ResponseCodeEnums.FAULT.getCode());
            if (details.size() == size.get()) {
                Map<String, List<MaterialGetDetail>> listMap = details.stream().
                        collect(Collectors.groupingBy(MaterialGetDetail::getErpMsg));
                //判断map集合的key值集合大小
                Set<String> keySet = listMap.keySet();
                int count = keySet.size();
                logger.info("获取领料单明细数据错误信息集合的错误类型的数量为:{},错误信息为:{}", count, JSON.toJSONString(keySet));
                if (count > 1) { //存在多个错误信息
                    header.setErpMsg("单据行存在同步异常信息，详见单据详情行明细");
                } else {
                    header.setErpMsg(msg);
                }
            } else {
                //有一条失败的,则处理为失败
                header.setErpMsg(msg);
            }

        } else {
            if (PublicUtil.isNotEmpty(details)) {
                details.forEach(detail -> {
                    if (detail.getId().equals(backDetail.getId())) {
                        detail.setErpCode("2");
                        detail.setErpMsg("");
                        //事务处理价格回传
                        if (!StringUtils.isEmpty(actualCost)) {
                            String[] resultArr = actualCost.split(",");
                            detail.setActualCost(new BigDecimal(resultArr[0]));
                            if (resultArr.length > 1) {
                                detail.setItemCostIsNull(1);
                            } else {
                                detail.setItemCostIsNull(0);
                            }
                        }
                        detailMapper.updateByPrimaryKeySelective(detail);
                        //
                        if (null != detail.getTicketTaskId()) {
                            //刷新工单领料
                            TicketTasksDetail ticketTasksDetail = ticketTasksDetailMapper.selectByPrimaryKey(detail.getTicketTaskId());
                            if (null != ticketTasksDetail) {
                                materialExtService.updateNumber(ticketTasksDetail);
                            }
                        }
                    }
                    if (Objects.equals(detail.getErpCode(), "2")) {
                        size.incrementAndGet();
                    }
                });
            }
            if (details.size() == size.get()) {
                header.setErpCode(ResponseCodeEnums.SUCESS.getCode());
                header.setErpMsg(msg);
            }
        }
        headerMapper.updateByPrimaryKeySelective(header);
        return true;
    }

    private String buildMaterialGetDetailKey(String inventoryCode, String materialCode) {
        return String.format("buildMaterialGetDetailKey_%s_%s", inventoryCode, materialCode);
    }

    @Override
    public void projectStatusCheck(Long projectId) {
        Project project = projectMapper.selectByPrimaryKey(projectId);
        if (project == null) {
            throw new BizException(Code.ERROR, "项目不存在");
        }
        List<Integer> allowProjectStatusList = Arrays.asList(ProjectStatus.APPROVALED.getCode(), ProjectStatus.CHANGING.getCode());
        if (!allowProjectStatusList.contains(project.getStatus())) {
            throw new BizException(Code.ERROR, "只能选择项目状态为：项目进行中、项目变更中的项目");
        }
    }

    @Override
    public Boolean initMaterialGetDetailColumn() {
        //每次查询5万条
        List<MaterialGetDetail> details = detailExtMapper.selectAll();
        if (ListUtils.isEmpty(details)) {
            return Boolean.FALSE;
        }
        List<List<MaterialGetDetail>> lists = ListUtils.splistList(details, 1000);
        for (List<MaterialGetDetail> list : lists) {
            //更新
            detailExtMapper.batchUpdate(list);
        }
        return Boolean.TRUE;
    }

    public void paramCheck(MaterialGetDto dto) {
        Long projectId = dto.getProjectId();
        String projectCode = dto.getProjectCode();
        String projectName = dto.getProjectName();

        if (dto.getOrganizationId() == null || StringUtils.isBlank(dto.getOrganizationCode())) {
            throw new BizException(Code.ERROR, "库存组织信息不能为空");
        }
        if (projectId == null || StringUtils.isBlank(projectCode) || StringUtils.isBlank(projectName)) {
            throw new BizException(Code.ERROR, "项目信息不能为空");
        }
        Project project = projectMapper.selectByPrimaryKey(projectId);
        if (project == null) {
            throw new BizException(Code.ERROR, "项目不存在");
        }
        if (!Objects.equals(project.getCode(), projectCode)) {
            throw new BizException(Code.ERROR, String.format("项目id：%s，项目编号：%s，不匹配", projectId, projectCode));
        }
        if (!Objects.equals(project.getName(), projectName)) {
            throw new BizException(Code.ERROR, String.format("项目id：%s，项目名称：%s，不匹配", projectId, projectName));
        }
        List<MaterialGetDetailDto> details = dto.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            throw new BizException(Code.ERROR, "领料明细不能为空");
        } else {
            //检验已制单未使用量的数据
            Map<String, MaterialGetDetailDto> detailDtoMap = new HashMap<>();
            List<MaterialGetStatDto> getStatDtoList = new ArrayList<>();
            for (MaterialGetDetailDto detail : details) {
                detailDtoMap.put(detail.getMaterialCode(), detail);
                MaterialGetStatDto getStatDto = new MaterialGetStatDto();
                getStatDto.setOrganizationId(dto.getOrganizationId());
                getStatDto.setInventoryCode(dto.getInventoryCode());
                //这里的货位编号没有赋值,所以改用去物料明细中的货位
                MaterialGetDetailDto detailDto = details.get(0);
                getStatDto.setLocationCode(detailDto.getLocationCode());
                getStatDto.setMaterialCode(detail.getMaterialCode());
                getStatDtoList.add(getStatDto);
            }
            MaterialGetStatDto[] dtos = getStatDtoList.toArray(new MaterialGetStatDto[getStatDtoList.size()]);
            //获取返回值,校验已制单未使用量的数据是否超过了
            List<MaterialGetStatDto> rstList = statBookingCount(dtos);
            for (MaterialGetStatDto getStatDto : rstList) {
                String materialCode = getStatDto.getMaterialCode();
                if (detailDtoMap.containsKey(materialCode)) {
                    MaterialGetDetailDto materialGetDetailDto = detailDtoMap.get(materialCode);
                    //截止到提交前这一刻,系统已制单未使用的量
                    BigDecimal bookingCount = getStatDto.getBookingCount();
                    //现有量
                    BigDecimal existAmount = materialGetDetailDto.getExistAmount();
                    //申请数量
                    BigDecimal applyAmount = materialGetDetailDto.getApplyAmount();
                    //如果现有量小于申请数量和此时的已制单未使用量之和,则不允许这样的数据通过
                    int result = existAmount.compareTo(applyAmount.add(bookingCount));
                    if (result < 0) {
                        throw new BizException(Code.ERROR, String.format("物料编码：%s，库存现有量: %s，小于申请数量和此时的已制单未使用量之和：%s，请刷新页面", materialCode, existAmount, applyAmount.add(bookingCount)));
                    }
                }
            }
        }
        //根据项目id获取WBS最底层
        List<String> wbsSummaryCodeList = milepostDesignPlanService.getWbsLastLayer(projectId).stream().map(s -> s.getWbsSummaryCode()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(wbsSummaryCodeList)) {
            for (MaterialGetDetailDto detail : details) {
                if (StringUtils.isBlank(detail.getWbsSummaryCode())) {
                    throw new BizException(Code.ERROR, "WBS号不能为空");
                }
                if (!wbsSummaryCodeList.contains(detail.getWbsSummaryCode())) {
                    throw new BizException(Code.ERROR, String.format("项目id：%s，项目编号：%s，WBS号：%s，不匹配", projectId, projectCode, detail.getWbsSummaryCode()));
                }
            }
        }
    }

    @Override
    @Transactional
    public MaterialGetDto draft(MaterialGetDto dto) {
        //参数校验
        paramCheck(dto);
        projectStatusCheck(dto.getProjectId());
        Set<String> forbiddenSubinventoryCodeList = organizationCustomDictService.queryByName(Constants.FORBIDDEN_SUBINVENTORY, dto.getOrganizationId(), OrgCustomDictOrgFrom.INVENTORY_ORG);
        if (forbiddenSubinventoryCodeList.contains(dto.getInventoryCode())) {
            throw new BizException(Code.ERROR, String.format("子库：%s，已禁用", dto.getInventoryCode()));
        }
        //单据状态
        if (null != dto.getApprove() && dto.getApprove()) {
            dto.setStatus(MaterialGetStatus.DRAFT.code());
        } else {
            dto.setStatus(MaterialGetStatus.PENDING.code());
        }
        if (dto.getId() == null) {
            dto.setGetCode(generateGetCode(SystemContext.getUnitId()));
            dto.setTotalActualAmount(BigDecimal.ZERO);//总实际领取数量
            dto.setTotalDifferenceAmount(BigDecimal.ZERO);//总差异数量
            dto.setApplyTime(new Date());
            headerMapper.insertSelective(dto);
        } else {
            headerMapper.updateByPrimaryKeySelective(dto);
            //逻辑删除历史明细数据
            materialGetDetailExtMapper.logicalDeleteByHeaderId(dto.getId());
        }

        BigDecimal totalApplyAmount = BigDecimal.ZERO;
        List<MaterialGetDetailDto> detailList = dto.getDetails();
        for (MaterialGetDetailDto detail : detailList) {
            // 活动事项校验
            projectActivityService.checkActivityCode(dto.getProjectId(), dto.getProjectCode(), detail.getActivityCode(), detail.getWbsSummaryCode());
            detail.setProjectId(dto.getProjectId());
            detail.setProjectCode(dto.getProjectCode());
            detail.setWbsFullCode(fromatWbsFullCode(dto.getProjectCode(), detail.getWbsSummaryCode()));
            //申请数量
            detail.setApplyAmount(detail.getApplyAmount() == null ? BigDecimal.ZERO : detail.getApplyAmount());
            totalApplyAmount = totalApplyAmount.add(detail.getApplyAmount());

            //兼容导入
            if (StringUtils.isNotEmpty(detail.getTicketTaskIdStr())) {
                detail.setTicketTaskId(Long.parseLong(detail.getTicketTaskIdStr()));//工单任务id
            }
            detail.setActualAmount(Optional.ofNullable(detail.getActualAmount()).orElse(BigDecimal.ZERO));//实际领取数量
            detail.setDifferenceAmount(Optional.ofNullable(detail.getDifferenceAmount()).orElse(BigDecimal.ZERO));//差异数量
            detail.setInventoryId(dto.getInventoryId());//仓库ID(跟头表取值)
            detail.setInventoryCode(dto.getInventoryCode());//仓库编号(跟头表取值)
            detail.setInventoryName(dto.getInventoryName());//仓库名称(跟头表取值)
            detail.setHeaderId(dto.getId());
            detail.setDeletedFlag(DeletedFlagEnum.VALID.code());
            detail.setId(null);
        }

        //批量插入
        if (CollectionUtils.isNotEmpty(detailList)) {
            List<MaterialGetDetail> details = BeanConverter.copy(detailList, MaterialGetDetail.class);
            List<List<MaterialGetDetail>> lists = ListUtils.splistList(details, 200);
            lists.forEach(list -> detailExtMapper.batchInsertMaterialGetDetail(list));
        }

        //刷新工单领料
        List<Long> ticketTaskDetailList = detailList.stream().map(MaterialGetDetail::getTicketTaskId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ticketTaskDetailList)) {
            TicketTasksDetailExample ticketTasksDetailExample = new TicketTasksDetailExample();
            ticketTasksDetailExample.createCriteria().andIdIn(ticketTaskDetailList);
            List<TicketTasksDetail> ticketTasksDetailList = ticketTasksDetailMapper.selectByExample(ticketTasksDetailExample);
            for (TicketTasksDetail ticketTasksDetail : ticketTasksDetailList) {
                materialExtService.updateNumber(ticketTasksDetail);
            }
        }

        //更新单据总申请数量
        dto.setTotalApplyAmount(totalApplyAmount);
        headerMapper.updateByPrimaryKeySelective(dto);

        return queryById(dto.getId());
    }

    @Override
    public void pass(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                     Long createUserId, Long handlerUserId, String nodeId, String timestamp) {
        applicationEventPublisher.publishEvent(new MaterialGetWorkflowCallbackApprovalEvent(this, formInstanceId, fdInstanceId, formUrl,
                eventName, handlerId, companyId, createUserId, handlerUserId, nodeId, timestamp));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submit(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                       Long createUserId, Long handlerUserId, String fileId, String fileName, String fileSize) {
        logger.info("项目领料审批提交审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}, handlerUserId:{}, fileId:{}, fileName:{}, fileSize:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, handlerUserId, fileId, fileName, fileSize);
        String lockName = String.format("MaterialGetWorkflowCallback_draftSubmit_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    MaterialGetHeader header = headerMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(header, " 项目领料审批提交审批回调 formInstanceId对应的领料单不存在，不处理");

                    //提交审批，补充制单人信息
                    UserInfo userInfo = CacheDataUtils.findUserById(handlerUserId);
                    if (userInfo != null) {
                        header.setFillUserId(handlerUserId);
                        header.setFillUserName(userInfo.getName());
                    }

                    if (header.getApplyTime() == null) {
                        header.setApplyTime(new Date());//申请时间
                    }
                    header.setStatus(MaterialGetStatus.APPROVING.code());//待审批
                    headerMapper.updateByPrimaryKeySelective(header);
                    //保存领料明细附件
                    if (StringUtils.isNotBlank(fileId) && StringUtils.isNotBlank(fileName) && StringUtils.isNotBlank(fileSize)) {
                        Long currentUserId = SystemContext.getUserId();
                        CtcAttachmentDto dto = new CtcAttachmentDto();
                        dto.setModule(CtcAttachmentModule.MATERIAL_GET_ITEM.code());
                        dto.setDeletedFlag(false);
                        dto.setModuleId(formInstanceId);
                        dto.setAttachId(Long.valueOf(fileId));
                        dto.setFileName(fileName);
                        dto.setFileSize(Long.valueOf(fileSize).intValue());
                        dto.setUpdateAt(new Date());
                        dto.setUpdateBy(currentUserId);
                        dto.setCreateAt(new Date());
                        dto.setCreateBy(currentUserId);
                        dto.setStatus(1);
                        ctcAttachmentService.add(dto);
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目领料审批提交审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目领料审批提交审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目领料审批提交审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refuse(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                       Long createUserId, Long handlerUserId) {
        logger.info("项目领料审批驳回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}, handlerUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, handlerUserId);
        String lockName = String.format("MaterialGetWorkflowCallback_refuse_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    this.approve(formInstanceId, handlerUserId, MaterialGetStatus.REJECT.code());

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目领料审批驳回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目领料审批驳回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目领料审批驳回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftReturn(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                            Long createUserId, Long handlerUserId) {
        logger.info("项目领料审批撤回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}, handlerUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, handlerUserId);
        String lockName = String.format("MaterialGetWorkflowCallback_draftReturn_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    this.approve(formInstanceId, handlerUserId, MaterialGetStatus.DRAFT.code());

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目领料审批撤回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目领料审批撤回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目领料审批撤回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reject(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                       Long createUserId, Long handlerUserId) {
        logger.info("项目领料审批作废回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}, handlerUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, handlerUserId);
        String lockName = String.format("MaterialGetWorkflowCallback_abandonCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    this.approve(formInstanceId, handlerUserId, MaterialGetStatus.DISCARD.code());

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目领料审批作废回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目领料审批作废回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目领料审批作废回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteFlow(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                           Long createUserId, Long handlerUserId) {
        logger.info("项目领料审批删除回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}, handlerUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, handlerUserId);
        String lockName = String.format("MaterialGetWorkflowCallback_deleteCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    MaterialGetHeader header = headerMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(header, " 项目领料审批删除回调 formInstanceId对应的领料单不存在，不处理");

                    UserInfo userInfo = CacheDataUtils.findUserById(handlerUserId);
                    if (userInfo != null) {
                        header.setAuditingUserId(handlerUserId);
                        header.setAuditingUserName(userInfo.getName());
                    }
                    header.setStatus(MaterialGetStatus.DISCARD.code());
                    headerMapper.updateByPrimaryKeySelective(header);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目领料审批删除回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目领料审批删除回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目领料审批删除回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long approve(Long id, Long auditingUserId, Integer status) {
        if (id != null) {
            MaterialGetHeader header = headerMapper.selectByPrimaryKey(id);

            if (header != null) {
                //审批回调，补充审批人信息
                UserInfo userInfo = CacheDataUtils.findUserById(auditingUserId);
                if (userInfo != null) {
                    header.setAuditingUserId(auditingUserId);
                    header.setAuditingUserName(userInfo.getName());
                }

                header.setStatus(status);//通过 或 驳回
                headerMapper.updateByPrimaryKeySelective(header);
                //同步mrp
                /*Long ticketTaskId = header.getTicketTaskId();
                if (null != ticketTaskId) {
                    TicketTasks ticketTasks = ticketTasksMapper.selectByPrimaryKey(ticketTaskId);
                    if (null != ticketTasks) {
                        if (null != ticketTasks.getMilepostDesignDetailId()) {
                            milepostDesignPlanService.setMRP(ticketTasks.getMilepostDesignDetailId());
                        }
                    }
                }*/
                MaterialGetDetailExample example = new MaterialGetDetailExample();
                example.createCriteria().andHeaderIdEqualTo(id);
                List<MaterialGetDetail> list = detailMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(list)) {
                    for (MaterialGetDetail details : list) {
                        if (null != details.getTicketTaskId()) {
                            //刷新工单领料
                            TicketTasksDetail ticketTasksDetail = ticketTasksDetailMapper.selectByPrimaryKey(details.getTicketTaskId());
                            if (null != ticketTasksDetail) {
                                materialExtService.updateNumber(ticketTasksDetail);
                            }

                        }
                    }
                }

            }
        }
        return id;
    }

    @Override
    public List<MaterialGetHeader> queryMaterialGetHeaderByMaterialCode(String materialCode, String getCode, String projectCode, String fillUserName,
                                                                        String getUserName, String org, Long organizationId,
                                                                        Integer difference, Date applyDateStart, Date applyDateEnd, String materialCodes) {

        //查询领料单明细包含该物料编码的
        MaterialGetDetailExample detailExample = new MaterialGetDetailExample();
        List<String> materialCodeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(materialCode)) {
            materialCodeList.add(materialCode);
        }
        if (StringUtils.isNotEmpty(materialCodes)) {
            List<String> list = Arrays.stream(materialCodes.split(",")).collect(Collectors.toList());
            materialCodeList.addAll(list);
        }
        detailExample.createCriteria().andMaterialCodeIn(materialCodeList)
                .andDeletedFlagEqualTo(0);
        List<MaterialGetDetail> materialGetDetails = detailMapper.selectByExample(detailExample);
        List<Long> headerIds = materialGetDetails.stream().map(x -> x.getHeaderId()).distinct().collect(Collectors.toList());
        logger.info("获取包含该物料编号:{}的表头id为:{}", materialCode, JSON.toJSONString(headerIds));
        if (!CollectionUtils.isEmpty(headerIds)) {
            MaterialGetHeaderExample example = new MaterialGetHeaderExample();
            MaterialGetHeaderExample.Criteria criteria = example.createCriteria();
            criteria.andStatusEqualTo(MaterialGetStatus.PROCESSED.code())//已处理
                    .andErpCodeEqualTo("000000")//已同步
                    .andIdIn(headerIds);//包含该物料的id集合
//            if (StringUtils.isNotBlank(getCode)) criteria.andGetCodeLike("%" + getCode + "%");
//            if (StringUtils.isNotBlank(projectCode)) criteria.andProjectCodeLike("%" + projectCode + "%");
//            if (StringUtils.isNotBlank(fillUserName)) criteria.andFillUserNameLike("%" + fillUserName + "%");
//            if (StringUtils.isNotBlank(getUserName)) criteria.andGetUserNameLike("%" + getUserName + "%");
//            if (StringUtils.isNotBlank(org)) criteria.andOrganizationCodeOrOrganizationNameLike("%" + org + "%");
//            if (PublicUtil.isNotEmpty(organizationId)) criteria.andOrganizationIdEqualTo(organizationId);
//            if (difference != null) {
//                if (difference.equals(0)) {//没有差异
//                    criteria.andTotalDifferenceAmountEqualTo(BigDecimal.ZERO);
//                } else {//有差异
//                    criteria.andTotalDifferenceAmountGreaterThan(BigDecimal.ZERO);
//                }
//            }
//            if (applyDateStart != null) criteria.andApplyTimeGreaterThanOrEqualTo(applyDateStart);
//            if (applyDateEnd != null) criteria.andApplyTimeLessThanOrEqualTo(applyDateEnd);
            example.setOrderByClause("create_at desc");
            List<MaterialGetHeader> headerList = headerMapper.selectByExample(example);
            logger.info("获取的领料单表头数据为:{}", headerList.size());
            //获取已处理的id集合
            return headerList;
        } else {
            throw new BizException(Code.ERROR, "不存在包含该物料的领料单信息");
        }
    }

    @Override
    public List<MaterialGetDetailDto> exportDetails(Long id) {
        MaterialGetDetailExample detailsExample = new MaterialGetDetailExample();
        detailsExample.setOrderByClause("material_code asc");
        detailsExample.createCriteria().andHeaderIdEqualTo(id).andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
        List<MaterialGetDetail> details = detailMapper.selectByExample(detailsExample);
        List<MaterialGetDetailDto> detailsData = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(details)) {
            // 获取计量单位
            Map<String, String> unitMap = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null)
                    .stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName, (key1, key2) -> key2));
            detailsData = details.stream().map(x -> {
                MaterialGetDetailDto detailDto = BeanConverter.copy(x, MaterialGetDetailDto.class);
                detailDto.setUnitName(unitMap.get(x.getUnit()));
                return detailDto;
            }).collect(Collectors.toList());
        }
        return detailsData;
    }

    /**
     * 已制单未领用量单据信息查询
     *
     * @param organizationId
     * @param inventoryCode
     * @param locationCode
     * @param materialCode
     * @return
     */
    @Override
    public List<BookingCountDto> selectBookingCountInfo(Long organizationId, String inventoryCode, String locationCode, String materialCode) {
        List<BookingCountDto> list = new ArrayList<>();
        String location = StringUtils.isNotEmpty(locationCode) ? locationCode : null;
        List<BookingCountDto> dtoList1 = detailExtMapper.selectMaterialGetBookingCount(organizationId, inventoryCode, location, materialCode);
        List<BookingCountDto> dtoList2 = materialTransferExtMapper.selectMaterialTransferBookingCount(organizationId, inventoryCode, location, materialCode);
        logger.info("获取领料单已制单未领用量的待处理数据为:{}", JSON.toJSONString(dtoList1));
        logger.info("获取转移单已制单未领用量的待处理数据为:{}", JSON.toJSONString(dtoList2));
        if (CollectionUtils.isNotEmpty(dtoList1)) {
            List<BookingCountDto> collect = dtoList1.stream().map(x -> {
                BookingCountDto dto = new BookingCountDto();
                dto.setMaterialCode(x.getMaterialCode());
                dto.setInventoryCode(x.getInventoryCode());
                dto.setLocationCode(x.getLocationCode());
                dto.setType("0");//0-领料单,1-转移单
                dto.setCode(x.getCode());
                dto.setId(x.getId());
                dto.setSyncStatus(x.getSyncStatus());
                if (Objects.equals(x.getSyncStatus(), MaterialGetStatus.PROCESSED.code())) {
                    dto.setBookingCount(x.getTotalActualAmount());
                } else {
                    dto.setBookingCount(x.getTotalApplyAmount());
                }
                return dto;
            }).collect(Collectors.toList());
            list.addAll(collect);
        }

        if (CollectionUtils.isNotEmpty(dtoList2)) {
            List<BookingCountDto> collect = dtoList2.stream().map(x -> {
                BookingCountDto dto = new BookingCountDto();
                dto.setMaterialCode(x.getMaterialCode());
                dto.setInventoryCode(x.getInventoryCode());
                dto.setLocationCode(x.getLocationCode());
                dto.setType("1");//0-领料单,1-转移单
                dto.setCode(x.getCode());
                dto.setId(x.getId());
                dto.setSyncStatus(x.getSyncStatus());
                if (Objects.equals(x.getSyncStatus(), MaterialTransferEnums.PROCESSED.code())) {
                    dto.setBookingCount(x.getTotalActualAmount());
                } else {
                    dto.setBookingCount(x.getTotalApplyAmount());
                }
                return dto;
            }).collect(Collectors.toList());
            list.addAll(collect);
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processed(MaterialGetDto[] dtos, Long keeperUserId) {
        this.done(dtos, keeperUserId, MaterialGetStatus.PROCESSED.code());
        for (MaterialGetDto dto : dtos) {
            MaterialGetDto header = this.queryById(dto.getId());
            if (header != null) {
                //
                if (StringUtils.isNotEmpty(header.getCostTransferCode())) {
                    //领料单来源于成本转移单,此时需要判断退料单是否已经处理完成了,如果退料单处理完成,则可以发起,否则不能发起
                    MaterialReturnHeaderExample example = new MaterialReturnHeaderExample();
                    example.createCriteria().andCostTransferCodeEqualTo(header.getCostTransferCode())
                            .andStatusEqualTo(MaterialGetStatus.PROCESSED.code()) //状态为已处理
                            .andErpCodeEqualTo("000000")//已同步  ERP同步状态(000000-已同步,999999-同步失败,3-同步中,-1-未同步,null-未同步)
                            .andDeletedFlagEqualTo(false);
                    List<MaterialReturnHeader> materialReturnHeaders = materialReturnHeaderMapper.selectByExample(example);
                    if (!CollectionUtils.isEmpty(materialReturnHeaders)) {
                        logger.info("成本转移单:{}生成的退料单:{}已同步完成,可以手动操作领料单的操作", header.getCostTransferCode(), materialReturnHeaders.get(0).getReturnCode());
                    } else {
                        logger.info("领料单单据:{}来源于成本转移单:{},退料单还未处理完成,不生成同步物料发送报文", header.getGetCode(), header.getCostTransferCode());
                        continue;
                        //throw new BizException(Code.ERROR, "成本转移单对应的退料单还未同步完成,该领料单不能进行此操作!");
                    }

                }
                if (header.getDetails() != null) {
                    for (MaterialGetDetail detail : header.getDetails()) {
                        if (detail == null)
                            continue;
                        if (detail.getActualAmount().compareTo(BigDecimal.ZERO) <= 0)
                            continue;
                        //已同步成功的领料明细行不再重新同步
                        if (Objects.equals(detail.getErpCode(), "2")) {
                            continue;
                        }
                        ResendExecute resendExecute = new ResendExecute();
                        resendExecute.setApplyNo(detail.getHeaderId().toString());
                        resendExecute.setSubApplyNo(detail.getId().toString());
                        resendExecute.setBusinessType(BusinessTypeEnums.GET_MATERIAL.getCode());
                        resendExecute.setSubBusinessType(SubBusinessTypeEnums.GET_MATERIAL.getCode());
                        resendExecute.setBatch(true);
                        HandleDispatcher.route(resendExecute);
                    }
                }
                //刷新工单领料

                MaterialGetDetailExample example = new MaterialGetDetailExample();
                example.createCriteria().andHeaderIdEqualTo(header.getId());
                List<MaterialGetDetail> list = detailMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(list)) {
                    for (MaterialGetDetail details : list) {
                        if (null != details.getTicketTaskId()) {
                            //刷新工单领料
                            TicketTasksDetail ticketTasksDetail = ticketTasksDetailMapper.selectByPrimaryKey(details.getTicketTaskId());
                            if (null != ticketTasksDetail) {
                                materialExtService.updateNumber(ticketTasksDetail);
                            }
                        }
                    }
                }
            }
        }

        return true;
    }

    @Override
    public Boolean discard(MaterialGetDto[] dtos, Long keeperUserId) {
        return this.done(dtos, keeperUserId, MaterialGetStatus.DISCARD.code());
    }

    private Boolean done(MaterialGetDto[] dtos, Long keeperUserId, Integer status) {
        if (dtos != null) {
            UserInfo userInfo = CacheDataUtils.findUserById(keeperUserId);
            BigDecimal totalActualAmount;
            for (int i = 0; i < dtos.length; i++) {
                MaterialGetDto dto = dtos[i];
                MaterialGetHeader header = headerMapper.selectByPrimaryKey(dto.getId());
                if (header != null) {
                    //废弃操作，如果单据状态为废弃，无需处理
                    if (Objects.equals(status, MaterialGetStatus.DISCARD.code())) {
                        if (Objects.equals(header.getStatus(), status)) {
                            dtos = ArrayUtils.remove(dtos, i--);//恢复数组下标
                            continue;
                        }
                        //处理操作，如果单据状态为已处理，且同步状态不为同步失败，无需处理
                    } else if (Objects.equals(status, MaterialGetStatus.PROCESSED.code())) {
                        if (Objects.equals(header.getStatus(), status) && !Objects.equals(header.getErpCode(), ResponseCodeEnums.FAULT.getCode())) {
                            dtos = ArrayUtils.remove(dtos, i--);//恢复数组下标
                            continue;
                        }
                    }
                    header.setStatus(status);
                    if (userInfo != null) {
                        header.setKeeperUserId(keeperUserId);
                        header.setKeeperUserName(userInfo.getName());
                    }
                    totalActualAmount = BigDecimal.ZERO;

                    if (dto.getDetails() != null) {//有传入明细信息
                        for (MaterialGetDetail det : dto.getDetails()) {
                            //作废状态不需要校验会计日期 账务处理才需要
                            if (MaterialGetStatus.PROCESSED.code().equals(header.getStatus())) {
                                //检查处理日期是否在打开会计期间内
                                this.checkGlPeriod(det, header.getOrganizationId());
                            }

                            MaterialGetDetail detail = detailMapper.selectByPrimaryKey(det.getId());
                            if (detail != null) {
                                if(!Objects.equals(status, MaterialGetStatus.DISCARD.code())){
                                    detail.setActualAmount(det.getActualAmount() == null ? BigDecimal.ZERO : det.getActualAmount());
                                }
                                detail.setDifferenceAmount(detail.getApplyAmount().subtract(detail.getActualAmount()));
                                if (StringUtils.isNotBlank(det.getRemark())) detail.setRemark(det.getRemark());

                                detail.setTradeTime(det.getTradeTime() == null ? new Date() : det.getTradeTime());//交易时间

                                //累计总实际领取数量
                                totalActualAmount = totalActualAmount.add(detail.getActualAmount());
                                detail.setCollectionDate(DateUtils.getCurrentDate());//记录处理的系统日期，用于成本归集
                                detailMapper.updateByPrimaryKeySelective(detail);//保存明细信息
                            }
                        }
                        if (dto.getDetails().stream().allMatch(detail -> detail.getActualAmount().compareTo(BigDecimal.ZERO) == 0)) {
                            header.setErpCode(ResponseCodeEnums.SUCESS.getCode());
                        }
                    } else {//没有传明细信息（账务批处理操作页面无法填日期，前端不传details）
                        MaterialGetDetailExample detailExample = new MaterialGetDetailExample();
                        detailExample.createCriteria().andHeaderIdEqualTo(header.getId());
                        List<MaterialGetDetail> details = detailMapper.selectByExample(detailExample);

                        for (MaterialGetDetail detail : details) {
                            detail.setActualAmount(detail.getApplyAmount());//实际领取数量 等于 申请量
                            detail.setDifferenceAmount(BigDecimal.ZERO);
                            Date glDate = this.getGlPeriod(header.getOrganizationId());
                            if (glDate != null) {
                                detail.setTradeTime(glDate.after(new Date()) ? new Date() : glDate);//交易时间
                            }
                            //累计总实际领取数量
                            totalActualAmount = totalActualAmount.add(detail.getActualAmount());
                            detail.setCollectionDate(DateUtils.getCurrentDate());//记录处理的系统日期，用于成本归集
                            detailMapper.updateByPrimaryKeySelective(detail);//保存明细信息
                        }
                        if (details.stream().allMatch(detail -> detail.getActualAmount().compareTo(BigDecimal.ZERO) == 0)) {
                            header.setErpCode(ResponseCodeEnums.SUCESS.getCode());
                        }
                    }

                    header.setTotalActualAmount(totalActualAmount);
                    header.setTotalDifferenceAmount(totalActualAmount.subtract(header.getTotalApplyAmount()));
                    if (MaterialGetStatus.PROCESSED.code().equals(header.getStatus()) &&
                            (StringUtils.isEmpty(header.getErpCode()) || Objects.equals(header.getErpCode(), ResponseCodeEnums.FAULT.getCode()))) {
                        header.setErpCode("3");// 更新领料表的状态 为同步中  ERP同步状态(000000-已同步,999999-同步失败,3-同步中,-1-未同步,null-未同步)
                    }
                    headerMapper.updateByPrimaryKeySelective(header);//修改头表信息

                    //刷新工单领料
                    MaterialGetDetailExample example = new MaterialGetDetailExample();
                    example.createCriteria().andHeaderIdEqualTo(header.getId());
                    List<MaterialGetDetail> list = detailMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (MaterialGetDetail details : list) {
                            if (null != details.getTicketTaskId()) {
                                TicketTasksDetail ticketTasksDetail = ticketTasksDetailMapper.selectByPrimaryKey(details.getTicketTaskId());
                                if (null != ticketTasksDetail) {
                                    materialExtService.updateNumber(ticketTasksDetail);
                                }
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    public MaterialGetDto queryById(Long id) {
        MaterialGetDto dto = new MaterialGetDto();

        if (id != null) {
            MaterialGetHeader header = headerMapper.selectByPrimaryKey(id);

            BeanUtils.copyProperties(header, dto);

            //查询创建者名称
            if (header.getCreateBy() != null) {
                UserInfo user = CacheDataUtils.findUserById(header.getCreateBy());
                if (user != null) dto.setCreateUserName(user.getName());
            }

            //查询修改者名称
            if (header.getUpdateBy() != null) {
                UserInfo user = CacheDataUtils.findUserById(header.getUpdateBy());
                if (user != null) dto.setUpdateUserName(user.getName());
            }

            //查询项目经理 & 项目业务分类
            if (header.getProjectId() != null) {
                Project project = projectMapper.selectByPrimaryKey(header.getProjectId());
                if (project != null) {
                    dto.setManagerName(project.getManagerName());
                    dto.setProjectUnitName(CacheDataUtils.findUnitNameById(project.getUnitId()));
                }
            }

            //获取明细信息
            MaterialGetDetailExample detailsExample = new MaterialGetDetailExample();
            detailsExample.setOrderByClause("material_code asc");
            detailsExample.createCriteria().andHeaderIdEqualTo(id).andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
            List<MaterialGetDetail> details = detailMapper.selectByExample(detailsExample);
            // 获取计量单位
            Map<String, String> unitMap = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null)
                    .stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName, (key1, key2) -> key2));
            List<MaterialGetDetailDto> detailsData = BeanConverter.copy(details, MaterialGetDetailDto.class);
            List<MaterialGetDetailDto> newDetailsData = detailsData.stream().peek(e -> e.setUnitName(unitMap.get(e.getUnit()))).collect(Collectors.toList());
            //获取物料货架
            if(ListUtils.isNotEmpty(newDetailsData)){
                Long orgId = newDetailsData.get(0).getOrgId();
                if(orgId == null){
                    orgId = header.getOrganizationId();
                }
                Map<String, String> shelvesMap = basedataExtService.getMaterialShelvesByOrgIdAndItemCode(orgId, newDetailsData.stream().map(MaterialGetDetailDto::getMaterialCode).collect(Collectors.toList()));
                if(cn.hutool.core.map.MapUtil.isNotEmpty(shelvesMap)){
                    for (MaterialGetDetail materialGetDetail : newDetailsData) {
                        if (shelvesMap.containsKey(materialGetDetail.getMaterialCode())) {
                            materialGetDetail.setShelves(shelvesMap.get(materialGetDetail.getMaterialCode()));
                        }
                    }
                }
            }
            dto.setDetails(newDetailsData);

            UserInfo user = CacheDataUtils.findUserById(dto.getCreateBy());
            if (user != null) dto.setCreateUserName(user.getName());

            if (dto.getStatus().equals(MaterialGetStatus.DRAFT.code()))
                dto.setStatusName(MaterialGetStatus.DRAFT.msg());
            if (dto.getStatus().equals(MaterialGetStatus.APPROVING.code()))
                dto.setStatusName(MaterialGetStatus.APPROVING.msg());
            if (dto.getStatus().equals(MaterialGetStatus.REJECT.code()))
                dto.setStatusName(MaterialGetStatus.REJECT.msg());
            if (dto.getStatus().equals(MaterialGetStatus.PENDING.code()))
                dto.setStatusName(MaterialGetStatus.PENDING.msg());
            if (dto.getStatus().equals(MaterialGetStatus.PROCESSED.code()))
                dto.setStatusName(MaterialGetStatus.PROCESSED.msg());
            if (dto.getStatus().equals(MaterialGetStatus.DISCARD.code()))
                dto.setStatusName(MaterialGetStatus.DISCARD.msg());
            //获取最近的gl打开日期
            GlPeriodDto periodDto = CacheDataUtils.getOpenOrgGlPeriod(dto.getOrganizationId(), LocalDate.now(), 6);
            if (!ObjectUtils.isEmpty(periodDto)) {
                dto.setActualGetAtBegin(periodDto.getStartDate());
                dto.setActualGetAtEnd(periodDto.getEndDate());
            }
        }

        return dto;
    }

    @Override
    public MaterialGetDto print(Long id) {
        MaterialGetDto dto = queryById(id);

        if (dto != null) {
            MaterialGetHeader header = headerMapper.selectByPrimaryKey(id);

            header.setPrintCount(header.getPrintCount() == null ? 1 : header.getPrintCount() + 1);
            headerMapper.updateByPrimaryKeySelective(header);
        }

        return dto;
    }

    @Override
    public PageInfo<MaterialGetDto> query(Integer status, String getCode, String projectCode, String fillUserName,
                                          String getUserName, String org, Long organizationId,
                                          Integer difference, Date applyDateStart, Date applyDateEnd,
                                          Integer page, Integer pageSize) {

        MaterialGetHeaderExample example = new MaterialGetHeaderExample();
        MaterialGetHeaderExample.Criteria criteria = example.createCriteria();

        if (status != null) criteria.andStatusEqualTo(status);
        if (StringUtils.isNotBlank(getCode)) criteria.andGetCodeLike("%" + getCode + "%");
        if (StringUtils.isNotBlank(projectCode)) criteria.andProjectCodeLike("%" + projectCode + "%");
        if (StringUtils.isNotBlank(fillUserName)) criteria.andFillUserNameLike("%" + fillUserName + "%");
        if (StringUtils.isNotBlank(getUserName)) criteria.andGetUserNameLike("%" + getUserName + "%");
        if (StringUtils.isNotBlank(org)) criteria.andOrganizationCodeOrOrganizationNameLike("%" + org + "%");
        if (PublicUtil.isNotEmpty(organizationId)) criteria.andOrganizationIdEqualTo(organizationId);
        if (difference != null) {
            if (difference.equals(0)) {//没有差异
                criteria.andTotalDifferenceAmountEqualTo(BigDecimal.ZERO);
            } else {//有差异
                criteria.andTotalDifferenceAmountGreaterThan(BigDecimal.ZERO);
            }
        }
        if (applyDateStart != null) criteria.andApplyTimeGreaterThanOrEqualTo(applyDateStart);
        if (applyDateEnd != null) criteria.andApplyTimeLessThanOrEqualTo(applyDateEnd);

        PageHelper.startPage(page, pageSize);

        example.setOrderByClause("create_at desc");
        final List<MaterialGetHeader> list1 = headerMapper.selectByExample(example);
        PageInfo<MaterialGetDto> pageInfo = BeanConverter.convertPage(list1, MaterialGetDto.class);

        if (CollectionUtils.isEmpty(pageInfo.getList())) return pageInfo;

        UserInfo user;
        for (MaterialGetDto dto : pageInfo.getList()) {
            user = null;
            user = CacheDataUtils.findUserById(dto.getCreateBy());
            if (user != null) dto.setCreateUserName(user.getName());

            if (dto.getStatus().equals(MaterialGetStatus.DRAFT.code()))
                dto.setStatusName(MaterialGetStatus.DRAFT.msg());
            if (dto.getStatus().equals(MaterialGetStatus.APPROVING.code()))
                dto.setStatusName(MaterialGetStatus.APPROVING.msg());
            if (dto.getStatus().equals(MaterialGetStatus.REJECT.code()))
                dto.setStatusName(MaterialGetStatus.REJECT.msg());
            if (dto.getStatus().equals(MaterialGetStatus.PENDING.code()))
                dto.setStatusName(MaterialGetStatus.PENDING.msg());
            if (dto.getStatus().equals(MaterialGetStatus.PROCESSED.code()))
                dto.setStatusName(MaterialGetStatus.PROCESSED.msg());
            if (dto.getStatus().equals(MaterialGetStatus.DISCARD.code()))
                dto.setStatusName(MaterialGetStatus.DISCARD.msg());

            GlPeriodDto periodDto = CacheDataUtils.getOpenOrgGlPeriod(dto.getOrganizationId(), LocalDate.now(), 6);
            if (!ObjectUtils.isEmpty(periodDto)) {
                dto.setActualGetAtBegin(periodDto.getStartDate());
                dto.setActualGetAtEnd(periodDto.getEndDate());
            }
            //查询领料单货位
            MaterialGetDetailExample detailExample = new MaterialGetDetailExample();
            MaterialGetDetailExample.Criteria detailCriteria = detailExample.createCriteria();
            detailCriteria.andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code()).andHeaderIdEqualTo(dto.getId()).andLocationCodeIsNotNull();
            List<MaterialGetDetail> materialGetDetailList = detailMapper.selectByExample(detailExample);
            if (!CollectionUtils.isEmpty(materialGetDetailList)) {
                dto.setLocationCode(materialGetDetailList.get(0).getLocationCode());
            }
        }
        return pageInfo;
    }

    @Override
    public List<MaterialGetProjectDto> queryProjectByUserId(Long userId) {
        List<MaterialGetProjectDto> resultList = new ArrayList<>();
        List<Long> ouIds = SystemContext.getOus();
        if (CollectionUtils.isEmpty(ouIds)) {
            return resultList;
        }

        //查询收入成本计划
        Map<Long, ProjectProfit> projectProfitMap = projectProfitService.selectAllToMap();

        //查询项目（不为商机项目）
        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andOuIdIn(ouIds).andProjectSourceNotEqualTo(ProjectSource.BUSINESS.getCode()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<Project> projectList = projectMapper.selectByExample(projectExample);

        Map<Long, String> userMipMap = new HashMap<>();
        Map<Long, String> unitNameMap = new HashMap<>();
        Map<Long, String> organizationNameMap = new HashMap<>();
        for (Project project : projectList) {
            MaterialGetProjectDto dto = new MaterialGetProjectDto();
            //项目信息
            dto.setProjectId(project.getId());
            dto.setProjectCode(project.getCode());
            dto.setProjectName(project.getName());
            dto.setProjectStatus(project.getStatus());
            //项目经理
            dto.setManagerName(project.getManagerName());
            Long managerId = project.getManagerId();
            if (managerId != null) {
                String userMip = userMipMap.get(managerId);
                if (StringUtils.isEmpty(userMip)) {
                    userMip = CacheDataUtils.findUserMipById(managerId);
                    userMipMap.put(managerId, userMip);
                }
                dto.setManagerMip(userMip);
            }
            //虚拟部门
            Long unitId = project.getUnitId();
            dto.setUnitId(unitId);
            if (unitId != null) {
                String unitName = unitNameMap.get(unitId);
                if (StringUtils.isEmpty(unitName)) {
                    unitName = CacheDataUtils.findUnitNameById(unitId);
                    unitNameMap.put(unitId, unitName);
                }
                dto.setUnitName(unitName);
            }
            //库存信息
            ProjectProfit projectProfit = projectProfitMap.get(project.getId());
            if (projectProfit != null) {
                Long organizationId = projectProfit.getStorageId();
                dto.setProfitId(projectProfit.getId());
                dto.setOrganizationId(organizationId);
                dto.setOrganizationCode(projectProfit.getStorageCode());
                if (organizationId != null) {
                    String organizationName = organizationNameMap.get(organizationId);
                    if (StringUtils.isEmpty(organizationName)) {
                        organizationName = CacheDataUtils.findOrganizationNameById(organizationId);
                        organizationNameMap.put(organizationId, organizationName);
                    }
                    dto.setOrganizationName(organizationName);
                }
            }
            //排序
            if (Objects.equals(dto.getProjectStatus(), ProjectStatus.APPROVALED.getCode()) || Objects.equals(dto.getProjectStatus(), ProjectStatus.CHANGING.getCode())) {
                dto.setSort(1);
            }
            resultList.add(dto);
        }
        resultList.sort(Comparator.comparing(MaterialGetProjectDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        return resultList;
    }

    @Override
    public List<MaterialGetStatDto> statBookingCount(MaterialGetStatDto[] dtos) {
        return statBookingCountImprove(dtos);
        /*List<MaterialGetStatDto> list = new ArrayList<>();
        //查询“待审批”或“待处理”的头表记录
        List<Integer> statusList = new ArrayList<>();
        statusList.add(MaterialGetStatus.APPROVING.code());//待审批
        statusList.add(MaterialGetStatus.PENDING.code());//待处理
        MaterialGetHeaderExample headerExample = new MaterialGetHeaderExample();
        headerExample.createCriteria().andStatusIn(statusList);
        List<MaterialGetHeader> headerList = headerMapper.selectByExample(headerExample);
        List<Long> headerIds = new ArrayList<>();
        if (headerList != null && headerList.size() > 0) {
            for (MaterialGetHeader header : headerList) {
                headerIds.add(header.getId());
            }
        }

        MaterialGetDetailExample detailExample1 = new MaterialGetDetailExample();
        if (headerIds.size() > 0) {
            for (MaterialGetStatDto dto : dtos) {
                MaterialGetDetailExample detailExample2 = new MaterialGetDetailExample();
                MaterialGetDetailExample.Criteria criteria2 = detailExample2.createCriteria();
                criteria2.andHeaderIdIn(headerIds);
                if (StringUtils.isNotBlank(dto.getInventoryCode())) {
                    criteria2.andInventoryCodeEqualTo(dto.getInventoryCode());
                }
                if (StringUtils.isNotBlank(dto.getLocationCode())) {
                    criteria2.andLocationCodeEqualTo(dto.getLocationCode());
                }
                if (StringUtils.isNotBlank(dto.getMaterialCode())) {
                    criteria2.andMaterialCodeEqualTo(dto.getMaterialCode());
                }
                detailExample1.or(criteria2);
            }
            list = detailExtMapper.statByExample(detailExample1);
        }
        list.addAll(statBookingCountNew(dtos));
        Map<String, BigDecimal> map = list.stream()
                .collect(Collectors.groupingBy(n -> toJsonKey(n.getInventoryCode(), n.getLocationCode(),
                                n.getMaterialCode())
                        , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream()
                                .map(MaterialGetStatDto::getBookingCount).reduce(BigDecimal.ZERO, BigDecimal::add))));
        List<MaterialGetStatDto> materialGetStatDtos = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
            MaterialGetStatDto materialGetStatDto = JSONObject.parseObject(entry.getKey(), MaterialGetStatDto.class);
            materialGetStatDto.setBookingCount(entry.getValue());
            materialGetStatDtos.add(materialGetStatDto);
        }
        return materialGetStatDtos;*/
    }

    /**
     * 查询已制单未领用的所有数据
     *
     * @return
     */
    @Override
    public List<MaterialUnfetchQuantityDto> selectMaterialUnfetchQuantity(Long organizationId) {
        List<MaterialUnfetchQuantityDto> dtoList1 = detailExtMapper.selectMaterialGetUnfetchQuantity(organizationId, null, null, null);
        List<MaterialUnfetchQuantityDto> dtoList2 = materialTransferExtMapper.selectMaterialTransferUnfetchQuantity(organizationId, null, null, null);
        logger.info("获取领料单已制单未领用量的待处理数据为:{}", JSON.toJSONString(dtoList1));
        logger.info("获取转移单已制单未领用量的待处理数据为:{}", JSON.toJSONString(dtoList2));
        Map<Boolean, List<MaterialUnfetchQuantityDto>> listMap1 = dtoList1.stream()
                .collect(Collectors.groupingBy(x -> x.getStatus().equals(MaterialGetStatus.PROCESSED.code())));
        Map<Boolean, List<MaterialUnfetchQuantityDto>> listMap2 = dtoList2.stream()
                .collect(Collectors.groupingBy(x -> x.getStatus().equals(MaterialTransferEnums.PROCESSED.code())));
        //这里需要计算总申请数,状态为2.4 总实际领取数,状态为5
        //计算申请总数
        List<String> keyList = Stream.concat(dtoList1.stream().map(x -> getStorageKey(x.getMaterialCode(), x.getOrganizationCode(), x.getLocator(), x.getSubinventoryCode())),
                dtoList2.stream().map(x -> getStorageKey(x.getMaterialCode(), x.getOrganizationCode(), x.getLocator(), x.getSubinventoryCode()))).distinct().collect(Collectors.toList());
        //领料单
        Map<String, List<MaterialUnfetchQuantityDto>> getMap1 = new HashMap<>();
        Map<String, List<MaterialUnfetchQuantityDto>> getMap2 = new HashMap<>();
        Map<String, List<MaterialUnfetchQuantityDto>> transferMap1 = new HashMap<>();
        Map<String, List<MaterialUnfetchQuantityDto>> transferMap2 = new HashMap<>();
        for (Boolean flag : listMap1.keySet()) {
            if (Boolean.TRUE.equals(flag)) {
                getMap2 = listMap1.get(true).stream().collect(Collectors.groupingBy(x -> getStorageKey(x.getMaterialCode(), x.getOrganizationCode(), x.getLocator(), x.getSubinventoryCode())));
                ;
            } else if (Boolean.FALSE.equals(flag)) {
                getMap1 = listMap1.get(false).stream().collect(Collectors.groupingBy(x -> getStorageKey(x.getMaterialCode(), x.getOrganizationCode(), x.getLocator(), x.getSubinventoryCode())));
            }
        }
        for (Boolean flag : listMap2.keySet()) {
            if (Boolean.TRUE.equals(flag)) {
                transferMap2 = listMap2.get(true).stream().collect(Collectors.groupingBy(x -> getStorageKey(x.getMaterialCode(), x.getOrganizationCode(), x.getLocator(), x.getSubinventoryCode())));
                ;
            } else if (Boolean.FALSE.equals(flag)) {
                transferMap1 = listMap2.get(false).stream().collect(Collectors.groupingBy(x -> getStorageKey(x.getMaterialCode(), x.getOrganizationCode(), x.getLocator(), x.getSubinventoryCode())));
            }
        }
        //转移单
        List<MaterialUnfetchQuantityDto> list = new ArrayList<>();
        for (String key : keyList) {
            MaterialUnfetchQuantityDto dto = new MaterialUnfetchQuantityDto();
            dto.setKeyId(key);
            List<MaterialUnfetchQuantityDto> getQuantityDtos1 = getMap1.containsKey(key) ? getMap1.get(key) : new ArrayList<>();
            List<MaterialUnfetchQuantityDto> getQuantityDtos2 = getMap2.containsKey(key) ? getMap2.get(key) : new ArrayList<>();
            List<MaterialUnfetchQuantityDto> transferQuantityDtos1 = transferMap1.containsKey(key) ? transferMap1.get(key) : new ArrayList<>();
            List<MaterialUnfetchQuantityDto> transferQuantityDtos2 = transferMap2.containsKey(key) ? transferMap2.get(key) : new ArrayList<>();
            //总申请数
            BigDecimal totalApplyAmount = BigDecimal.ZERO;
            //总实际领取数
            BigDecimal totalActualAmount = BigDecimal.ZERO;
            BigDecimal bookingCount = BigDecimal.ZERO;
            //领料单申请总数
            if (CollectionUtils.isNotEmpty(getQuantityDtos1)) {
                BigDecimal reduce = getQuantityDtos1.stream().map(MaterialUnfetchQuantityDto::getTotalApplyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                totalApplyAmount = totalApplyAmount.add(reduce);
            }
            //领料单总实际领取数
            if (CollectionUtils.isNotEmpty(getQuantityDtos2)) {
                BigDecimal reduce = getQuantityDtos2.stream().map(MaterialUnfetchQuantityDto::getTotalActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                totalActualAmount = totalActualAmount.add(reduce);
            }
            //转移单申请总数
            if (CollectionUtils.isNotEmpty(transferQuantityDtos1)) {
                BigDecimal reduce = transferQuantityDtos1.stream().map(MaterialUnfetchQuantityDto::getTotalApplyAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                totalApplyAmount = totalApplyAmount.add(reduce);
            }
            //转移单总实际领取数
            if (CollectionUtils.isNotEmpty(transferQuantityDtos2)) {
                BigDecimal reduce = transferQuantityDtos2.stream().map(MaterialUnfetchQuantityDto::getTotalActualAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                totalActualAmount = totalActualAmount.add(reduce);
            }
            //总已制单未领取数
            bookingCount = totalApplyAmount.add(totalActualAmount);
            dto.setTotalApplyAmount(totalApplyAmount);
            dto.setTotalActualAmount(totalActualAmount);
            dto.setBookingCount(bookingCount);
            list.add(dto);
        }
        logger.info("处理后的数据为:{}", JSON.toJSONString(list));
        return list;
    }

    private String getStorageKey(String materialCode, String organizationCode, String locator, String subinventoryCode) {
        if (com.midea.pam.common.util.StringUtils.isEmpty(locator)) { //数据库存的有null或者空字符串的情况
            locator = "";
        }
        return "getStorageKey_" + materialCode + "_" + organizationCode + "_" + subinventoryCode + "_" + locator;
    }


    private List<MaterialGetStatDto> statBookingCountImprove(MaterialGetStatDto[] dtos) {
        List<String> materialCodeList = Arrays.stream(dtos).map(x -> x.getMaterialCode()).collect(Collectors.toList());
        //获取领料单已制单未使用的数据
        List<MaterialGetStatDto> rstList = new ArrayList<>();
        if (dtos.length > 0) {
            Long organizationId = dtos[0].getOrganizationId();
            String inventoryCode = dtos[0].getInventoryCode();
            String locationCode = StringUtils.isNotEmpty(dtos[0].getLocationCode()) ? dtos[0].getLocationCode() : null; //货位在数据库中有的地方存储为null,有的为空字符串
            List<MaterialUnfetchQuantityDto> dtoList1 = detailExtMapper.selectMaterialGetUnfetchQuantity(organizationId, inventoryCode, locationCode, materialCodeList);
            List<MaterialUnfetchQuantityDto> dtoList2 = materialTransferExtMapper.selectMaterialTransferUnfetchQuantity(organizationId, inventoryCode, locationCode, materialCodeList);
            logger.info("获取领料单已制单未领用量的待处理数据为:{}", JSON.toJSONString(dtoList1));
            logger.info("获取转移单已制单未领用量的待处理数据为:{}", JSON.toJSONString(dtoList2));
            //根据物料进行分组,领料或者转移物料都在同一个组织参数和仓库编号下
            Map<String, List<MaterialUnfetchQuantityDto>> listMap1 = dtoList1.stream().collect(Collectors.groupingBy(MaterialUnfetchQuantityDto::getMaterialCode));
            //获取转移单单已制单未使用的数据
            Map<String, List<MaterialUnfetchQuantityDto>> listMap2 = dtoList2.stream().collect(Collectors.groupingBy(MaterialUnfetchQuantityDto::getMaterialCode));
            //符合集合的数据中物料编码可能重复
            rstList = materialCodeList.stream().map(x -> {
                MaterialGetStatDto dto = new MaterialGetStatDto();
                List<MaterialUnfetchQuantityDto> quantityDtos1 = listMap1.get(x);
                List<MaterialUnfetchQuantityDto> quantityDtos2 = listMap2.get(x);
                dto.setOrganizationId(organizationId);
                dto.setInventoryCode(inventoryCode);
                dto.setLocationCode(locationCode);
                dto.setMaterialCode(x);
                BigDecimal bookingCount = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(quantityDtos1)) {
                    BigDecimal reduce = quantityDtos1.stream().map(a -> Objects.equals(a.getStatus(), MaterialGetStatus.PROCESSED.code()) ? a.getTotalActualAmount() : a.getTotalApplyAmount()
                    ).reduce(BigDecimal.ZERO, BigDecimal::add);
                    logger.info("获取的数据为:{}", reduce);
                    bookingCount = bookingCount.add(reduce);
                }
                if (CollectionUtils.isNotEmpty(quantityDtos2)) {
                    BigDecimal reduce = quantityDtos2.stream().map(a -> Objects.equals(a.getStatus(), MaterialTransferEnums.PROCESSED.code()) ? a.getTotalActualAmount() : a.getTotalApplyAmount()
                    ).reduce(BigDecimal.ZERO, BigDecimal::add);
                    logger.info("获取的数据2为:{}", reduce);
                    bookingCount = bookingCount.add(reduce);
                }
                dto.setBookingCount(bookingCount);
                return dto;
            }).collect(Collectors.toList());
            logger.info("处理后的已制单未领用量的数据为:{}", JSON.toJSONString(rstList));
        }
        return rstList;
    }

    private String toJsonKey(String inventoryCode, String locationCode, String materialCode) {
        MaterialGetStatDto po = new MaterialGetStatDto();
        po.setInventoryCode(inventoryCode);
        po.setLocationCode(locationCode);
        po.setMaterialCode(materialCode);
        return JSONObject.toJSONString(po);
    }

    public List<MaterialGetStatDto> statBookingCountNew(MaterialGetStatDto[] dtos) {
        List<String> erpCodes = new ArrayList<>();
        erpCodes.add("999999");//同步失败
        erpCodes.add("3");//同步中
        MaterialGetHeaderExample headerExample = new MaterialGetHeaderExample();
        headerExample.createCriteria().andStatusEqualTo(5).andErpCodeIn(erpCodes);
        List<MaterialGetHeader> headerList = headerMapper.selectByExample(headerExample);
        List<Long> headerIds = new ArrayList<>();
        if (headerList != null && headerList.size() > 0) {
            for (MaterialGetHeader header : headerList) {
                headerIds.add(header.getId());
            }
        }

        MaterialGetDetailExample detailExample1 = new MaterialGetDetailExample();
        if (headerIds.size() > 0) {
            for (MaterialGetStatDto dto : dtos) {
                MaterialGetDetailExample detailExample2 = new MaterialGetDetailExample();
                MaterialGetDetailExample.Criteria criteria2 = detailExample2.createCriteria();
                criteria2.andHeaderIdIn(headerIds);
                if (StringUtils.isNotBlank(dto.getInventoryCode())) {
                    criteria2.andInventoryCodeEqualTo(dto.getInventoryCode());
                }
                if (StringUtils.isNotBlank(dto.getLocationCode())) {
                    criteria2.andLocationCodeEqualTo(dto.getLocationCode());
                }
                if (StringUtils.isNotBlank(dto.getMaterialCode())) {
                    criteria2.andMaterialCodeEqualTo(dto.getMaterialCode());
                }
                detailExample1.or(criteria2);
            }
            return detailExtMapper.statByExampleNew(detailExample1);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public ResponseMap getMaterialGetApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> detailMapList = new ArrayList<>();
        MaterialGetDto dto = queryById(id);
        if (ObjectUtils.isEmpty(dto)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("头信息不能为空");
        }
        Asserts.notEmpty(dto, ErrorCode.BASEDATA_MATERIAL_NOT_FIND);
        headMap.put("getCode", dto.getGetCode()); //领料单编号
        headMap.put("projectName", dto.getProjectName()); // 领料项目名称
        headMap.put("projectCode", dto.getProjectCode());// 领料项目编号
        headMap.put("inventoryName", dto.getInventoryName()); // 领料仓库名称
        headMap.put("totalApplyAmount", dto.getTotalApplyAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); // 申请总量
        headMap.put("remark", dto.getRemark()); // 备注
        headMap.put("getUserName", dto.getGetUserName()); // 领料人
        headMap.put("fillUserName", dto.getFillUserName()); //制单人
        headMap.put("organizationName", dto.getOrganizationName()); // 库存组织
        headMap.put("ticketTaskCode", dto.getTicketTaskCode()); // 工单任务编号
        headMap.put("materialGetType", dto.getMaterialGetType() == 0 ? "工单领料" : "项目领料"); // 领料类型
        headMap.put("theassemblyDes", dto.getTheassemblyDes()); // 装配件描述
        headMap.put("moduleCode", dto.getModuleCode()); // 模组编号
        headMap.put("moduleName", dto.getModuleName()); // 模组名称
        headMap.put("lookMaterialGet", dto.getLookMaterialGet()); // 看板物料领料
        responseMap.setHeadMap(headMap);
        //附件
        List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(id, CtcAttachmentModule.MATERIAL_GET_ITEM.code(), null);
        if (!CollectionUtils.isEmpty(attachmentDtos)) {
            CtcAttachmentDto ctcAttachmentDto = attachmentDtos.get(0);
            List<AduitAtta> fileList = new ArrayList<>();
            AduitAtta aduitAtta = new AduitAtta();
            aduitAtta.setFdId(ctcAttachmentDto.getAttachId().toString());
            aduitAtta.setFileName(ctcAttachmentDto.getFileName());
            aduitAtta.setFileSize(ctcAttachmentDto.getFileSize().toString());
            fileList.add(aduitAtta);
            responseMap.setFileList(fileList);
        }
        responseMap.setStatus("success");
        responseMap.setMsg("success");
        return responseMap;
    }

    @Override
    public MaterialGetDetail getDetailById(Long id) {
        return detailMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateDetail(MaterialGetDetail record) {
        return detailMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public BigDecimal getPickingQuantity(Long projectId, String materialCode) {
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(materialCode, ErrorCode.CTC_ITEM_CODE_NOT_NULL);

        BigDecimal sum = new BigDecimal(0);

        List<MaterialGetDetail> materialGetDetails = detailExtMapper.getMaterialGetDetailByProjecAndMaterialCode(projectId, materialCode);
        for (MaterialGetDetail materialGetDetail : materialGetDetails) {
            sum = sum.add(materialGetDetail.getActualAmount());
        }
        return sum;
    }

    /**
     * 检查物料可用量
     *
     * @param dto
     */
    @Override
    public void checkStorage(MaterialGetDto dto) {
//		if(CollectionUtils.isNotEmpty(dto.getDetails())) {
//			for(MaterialGetDetail detail :dto.getDetails()) {
//				BigDecimal pendingProcessAmount = this.getPendingProcessAmount(detail.getMaterialCode(), detail.getLocationCode());
//				//可用量 = 现有量 - 已制单未领用量
//				BigDecimal availableAmount = detail.getExistAmount().subtract(pendingProcessAmount);
//				//可用量 > 申请数量 -> 通过
//				if(!(availableAmount.compareTo(detail.getApplyAmount()) >= 0)) {
//					System.out.println(availableAmount + ":" + detail.getApplyAmount());
//				}
//				Assert.isTrue(availableAmount.compareTo(detail.getApplyAmount()) >= 0, detail.getMaterialCode() + "可领料数量不足!");
//			}
//		}
    }

    /**
     * 物料所在仓库(或+货位)的已制单未领用量
     *
     * @return
     */
    @Override
    public List<MaterialGetStatDto> getBookingDetail() {
        List<MaterialGetStatDto> statDtoList = detailExtMapper.getBookingDetail(null);
        return statDtoList;
    }

    @Override
    @Transactional
    public void sync(String businessType, Long applyNo) {
        MaterialGetHeader header = headerMapper.selectByPrimaryKey(applyNo);
        if (Objects.equals(header.getErpCode(), ResponseCodeEnums.IN_SYNC.getCode())
                || Objects.equals(header.getErpCode(), ResponseCodeEnums.SUCESS.getCode())) {
            throw new BizException(Code.ERROR, "同步中或已同步的领料单不能进行此操作!");
        }
        if (StringUtils.isNotEmpty(header.getCostTransferCode())) {
            //领料单来源于成本转移单,此时需要判断退料单是否已经处理完成了,如果退料单处理完成,则可以发起,否则不能发起
            MaterialReturnHeaderExample example = new MaterialReturnHeaderExample();
            example.createCriteria().andCostTransferCodeEqualTo(header.getCostTransferCode())
                    .andStatusEqualTo(MaterialGetStatus.PROCESSED.code()) //状态为已处理
                    .andErpCodeEqualTo("000000")//已同步  ERP同步状态(000000-已同步,999999-同步失败,3-同步中,-1-未同步,null-未同步)
                    .andDeletedFlagEqualTo(false);
            List<MaterialReturnHeader> materialReturnHeaders = materialReturnHeaderMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(materialReturnHeaders)) {
                logger.info("成本转移单:{}生成的退料单:{}已同步完成,可以手动操作领料单的操作", header.getCostTransferCode(), materialReturnHeaders.get(0).getReturnCode());
            } else {
                logger.info("领料单单据:{}来源于成本转移单:{},退料单还未处理完成,不生成同步物料发送报文", header.getGetCode(), header.getCostTransferCode());
                throw new BizException(Code.ERROR, "成本转移单对应的退料单还未同步完成,该领料单不能进行此操作!");
            }

        }
        header.setErpCode(ResponseCodeEnums.IN_SYNC.getCode());
        header.setErpMsg(null);
        MaterialGetDetailExample example = new MaterialGetDetailExample();
        example.createCriteria().andDeletedFlagEqualTo(0).andHeaderIdEqualTo(applyNo);
        List<MaterialGetDetail> details = detailMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(details)) {
            for (MaterialGetDetail detail : details) {
                //已同步成功的领料明细行不再重新同步
                if (Objects.equals(detail.getErpCode(), "2")) {
                    continue;
                }
                ResendExecute resendExecute = new ResendExecute();
                resendExecute.setApplyNo(applyNo.toString());
                resendExecute.setSubApplyNo(detail.getId().toString());
                resendExecute.setBusinessType(businessType);
                resendExecute.setSubBusinessType(SubBusinessTypeEnums.GET_MATERIAL.getCode());
                resendExecute.setBatch(true);
                HandleDispatcher.route(resendExecute);
            }
        }
        headerMapper.updateByPrimaryKey(header);
        agencySynService.syndataToErp(businessType, applyNo);
    }

    @Override
    public List<TicketTasksTreeDto> getTicketTasksTree(Long projectId) {
        Long unitId = SystemContext.getUnitId();
        TicketTasksExample ticketTasksExample = new TicketTasksExample();
        ticketTasksExample.createCriteria().andDeletedFlagEqualTo(false).andUnitIdEqualTo(unitId).andParentIdIsNull().andProjectIdEqualTo(projectId);
        List<TicketTasks> ticketTasks = ticketTasksMapper.selectByExample(ticketTasksExample);
        //获取到父节点ids
        List<Long> ids = ticketTasks.stream().map(TicketTasks::getId).collect(Collectors.toList());
        List<TicketTasksTreeDto> tasksTreeDtos = BeanConverter.copy(detailExtMapper.selectByIds(ids), TicketTasksTreeDto.class);

        List<TicketTasksTreeDto> ticketTasksTreeDtos = buildTree(tasksTreeDtos);
        return ticketTasksTreeDtos;
    }

    @Transactional
    @Override
    public void materialGetJob() {
        //1.查所有工单类型为领料单、状态已完成、erpcode已同步、项目进行中
        //2.根据领料单id查领料单明细工单号
        //3.过滤重复明细工单号
        //4.根据工单号查询工单明细（只查看看板物料：需要领料-已领料大于0）
        List<TicketTasksDetail> ticketTasksDetail = detailExtMapper.getTicketTasksDetail2();
        UserInfo admin = CacheDataUtils.findUserByMip("admin");

        if (CollectionUtils.isNotEmpty(ticketTasksDetail)) {
            ticketTasksDetail
//                    .stream().collect(Collectors.toMap(
//                    TicketTasksDetail::getTicketTasksId
//                    , Collections::singletonList
//                    , ListUtil::addAllByImmutable))
                    .forEach(i -> {
                        // TODO getAttribute3 == project_id  还是过后再新建个dto接吧..
                        TicketTasks ticketTasks = ticketTasksMapper.selectByPrimaryKey(i.getTicketTasksId());

                        //根据库存组织到“子库配置”里面取“看板物料仓”，如果有多个或者没有找到，就报错，不处理
                        Project project = projectMapper.selectByPrimaryKey(Long.parseLong(i.getAttribute3())); //project_id
                        List<MaterialGetHeader> materialGetHeaders = detailExtMapper.selectMaterialGetHeaderByProjectId(project.getId());
                        String url = ModelsEnum.BASEDATA.getBaseUrl() + "storageInventory/pageInventory";
                        HashMap<String, String> objectObjectHashMap = MapUtil.ofNullable(new HashMap<String, String>())
                                .putToMap("organizationCode", materialGetHeaders.get(0).getOrganizationCode())
                                .putToMap("description", "看板物料仓")
                                .get();
                        ResponseEntity<String> forEntity = restTemplate.getForEntity(url, String.class, objectObjectHashMap);
                        DataResponse<PageInfo<StorageInventory>> pageInfoDataResponse = JSON.parseObject(forEntity.getBody(), new TypeReference<DataResponse<PageInfo<StorageInventory>>>() {
                        });
                        StorageInventory storageInventory = ObjUtils.ofNullable(pageInfoDataResponse)
                                .accept(DataResponse::getData)
                                .ifNullThenThrow(() -> new BizException(Code.ERROR, "远程调用BASEDATA异常!"))
                                .accept(PageInfo::getList)
                                .ifTureThrow(t -> t.size() != 1, () -> new BizException(Code.ERROR, "看板物料仓异常!"))
                                .accept(t -> t.get(0))
                                .get();
                        //生成领料单头
                        MaterialGetHeader y = Builder.of(MaterialGetHeader::new)
                                .with(MaterialGetHeader::setStatus, 5) //状态 -已处理
                                .with(MaterialGetHeader::setGetCode, generateGetCode(project.getUnitId())) //根据unit_id 生成的单据号
                                .with(MaterialGetHeader::setFillUserId, admin.getId()) //制单人
                                .with(MaterialGetHeader::setFillUserName, admin.getUsername())
                                .with(MaterialGetHeader::setGetUserId, admin.getId())  //领料人
                                .with(MaterialGetHeader::setGetUserName, admin.getUsername())
                                .with(MaterialGetHeader::setProjectId, project.getId()) //project信息
                                .with(MaterialGetHeader::setProjectCode, project.getCode())
                                .with(MaterialGetHeader::setProjectName, project.getName())
//                            .with(MaterialGetHeader::setGetDeptId) //部门id
//                            .with(MaterialGetHeader::setGetDeptName) //部门名称
                                .with(MaterialGetHeader::setAuditingUserId, admin.getId()) //审核人
                                .with(MaterialGetHeader::setAuditingUserName, admin.getUsername())
                                .with(MaterialGetHeader::setInventoryId, storageInventory.getId())  //仓库
                                .with(MaterialGetHeader::setInventoryCode, storageInventory.getSecondaryInventoryName())
                                .with(MaterialGetHeader::setInventoryName, storageInventory.getDescription())
                                .with(MaterialGetHeader::setLookMaterialGet, "Y") //是否看板物料
                                .with(MaterialGetHeader::setTheassemblyCode, ticketTasks.getTheassemblyCode()) //装配件编号
                                .with(MaterialGetHeader::setTheassemblyDes, ticketTasks.getTheassemblyDes())  //装配件描述
                                .with(MaterialGetHeader::setModuleCode, ticketTasks.getModuleCode()) //模组名称
                                .with(MaterialGetHeader::setModuleName, ticketTasks.getModuleName())  //模组名称
                                .with(MaterialGetHeader::setTicketTaskId, ticketTasks.getId())
                                .with(MaterialGetHeader::setTicketTaskCode, ticketTasks.getTicketTaskCode())
                                .with(MaterialGetHeader::setApplyTime, new Date()) //申请时间
                                .with(MaterialGetHeader::setCreateAt, new Date())
                                .with(MaterialGetHeader::setCreateBy, admin.getId())
                                .build();
                        //插入领料单头
                        materialGetHeaderMapper.insertSelective(y);
                        //生成领料单明细  -> 查找物料
                        Material material = detailExtMapper.selectMaterialByPamCode(i.getPamCode(), y.getOrganizationId());
                        MaterialGetDetail getDetail = Builder.of(MaterialGetDetail::new)
                                .with(MaterialGetDetail::setHeaderId, y.getId()) //领料单头ID
                                .with(MaterialGetDetail::setInventoryId, storageInventory.getId())
                                .with(MaterialGetDetail::setInventoryCode, storageInventory.getSecondaryInventoryName())
                                .with(MaterialGetDetail::setInventoryName, storageInventory.getDescription())
//                            .with(MaterialGetDetail::setLocationId) //货位ID
//                            .with(MaterialGetDetail::setLocationCode) //货位编号
//                            .with(MaterialGetDetail::setLocationName) //货位名称
                                .with(MaterialGetDetail::setMaterialId, material.getId()) //物料id
                                .with(MaterialGetDetail::setMaterialErpId, material.getItemId()) //物料ERPid
                                .with(MaterialGetDetail::setMaterialCode, material.getItemCode()) //物料code
                                .with(MaterialGetDetail::setMaterialName, material.getName()) //物料名称
                                .with(MaterialGetDetail::setUnit, material.getUnit()) //单位
                                .with(MaterialGetDetail::setApplyAmount, i.getRestPickingNumber()) //领料数量 = 工单剩余领料数量
                                .with(MaterialGetDetail::setActualAmount, i.getRestPickingNumber()) //实际领料数量 = 工单剩余领料数量
                                .with(MaterialGetDetail::setErpCode, "0") //TODO erp同步状态 先写个0吧...
//                            .with(MaterialGetDetail::setErpMsg)
                                .with(MaterialGetDetail::setDeletedFlag, 0)
                                .with(MaterialGetDetail::setCreateAt, new Date())
                                .with(MaterialGetDetail::setCreateBy, admin.getId())
                                .build();
                        materialGetDetailMapper.insertSelective(getDetail);
                        i.setRestPickingNumber(BigDecimal.ZERO); //剩余数量置零
                        ticketTasksDetailMapper.updateByPrimaryKeySelective(i);
                    });

        }
        //5.获取admin的账号id作为制单人，领料人；
        //6.生成领料单头（状态=“账户处理完成”）
        //7.生成领料单明细
        //8.修改工单明细剩余领料数量为0
    }


    private void checkGlPeriod(MaterialGetDetail detail, Long organizationId) {
        if (detail.getTradeTime() != null) {
            String yearMonth = DateUtils.format(detail.getTradeTime(), "yyyy-MM");
            GlPeriodDto glPeriodDto = CacheDataUtils.findCacheOrgPeriod(organizationId, yearMonth);
            Guard.isFalse(ObjectUtils.isEmpty(glPeriodDto) || !GlPeriodDto.OPEN_STATUS.equals(glPeriodDto.getShowStatus()), "处理日期不在打开的会计期间内");
        }
    }

    private Date getGlPeriod(Long organizationId) {
        GlPeriodDto glPeriodDto = CacheDataUtils.getOpenOrgGlPeriod(organizationId, LocalDate.now(), 6);
        if (null == glPeriodDto) {
            throw new BizException(Code.ERROR, "无打开的会计期间!");
        } else {
            return glPeriodDto.getEndDate();
        }
    }

    /**
     * 领料单编号规则： 代码公司段(美云:M，机器人:R)+LL+YY+MM+DD+3位流水码
     *
     * @return
     */
    private String generateGetCode(Long unitId) {
        StringBuffer getCode = new StringBuffer();
        String seqPerfix = basedataExtService.getUnitSeqPerfix(unitId);
        getCode.append(seqPerfix);
        getCode.append(CodePrefix.MATERIALGET.code());
        getCode.append(CacheDataUtils.generateSequence(Constants.MATERIAL_GET_CODE_LENGTH, seqPerfix + CodePrefix.MATERIALGET.code(), DateUtil.DATE_YYMMDD_PATTERN));
        return getCode.toString();
    }

    private List<TicketTasksTreeDto> buildTree(List<TicketTasksTreeDto> ticketTasksTreeDtos) {
        // 根节点
        List<TicketTasksTreeDto> tasksTreeDtos = new ArrayList<TicketTasksTreeDto>();

        // 组装所有的根节点
        for (TicketTasksTreeDto ticketTasksTreeDto : tasksTreeDtos) {
            if (ticketTasksTreeDto.getParentId() == null) {
                tasksTreeDtos.add(ticketTasksTreeDto);
            }
        }
        //为根节点设置子节点，getClild递归调用
        for (TicketTasksTreeDto ticketTasksTreeDetailDto : tasksTreeDtos) {
            // 获取根节点下面的所有子节点，使用getClild方法
            List<TicketTasksTreeDto> childList = getSons(ticketTasksTreeDetailDto.getId(), ticketTasksTreeDtos);
            //给根节点设置子节点
            ticketTasksTreeDetailDto.setTicketTasksTreeDtoList(childList);
        }

        return tasksTreeDtos;

    }

    private List<TicketTasksTreeDto> getSons(Long id, List<TicketTasksTreeDto> ticketTasksTreeDtos) {
        // 子节点
        List<TicketTasksTreeDto> childList = new ArrayList<>();
        for (TicketTasksTreeDto detailDtoAll : ticketTasksTreeDtos) {
            // 遍历所有节点，将所有节点的父id与传过来的根节点的id进行比较，相等则说明：为该根节点的子节点
            if (detailDtoAll.getParentId().equals(id)) {
                childList.add(detailDtoAll);
            }
        }

        // 进行递归
        for (TicketTasksTreeDto dto : childList) {
            dto.setTicketTasksTreeDtoList(getSons(dto.getId(), ticketTasksTreeDtos));
        }

        //如果节点下没有子节点，返回一个空List（递归退出)
        if (childList.size() == 0) {
            return new ArrayList<>();
        }
        return childList;
    }

    /**
     * 根据指定时间或前一天0点拉取数据
     *
     * @param lastUpdateDate
     * @param orgId
     * @param inventoryCode
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getMaterialGetAndReturnFromErp(String lastUpdateDate, String lastUpdateDateEnd, Long orgId, String inventoryCode) {
        String lockKey = "getMaterialGetAndReturnFromErp";
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockKey, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {

                Map<String, String> paramMap = new HashMap();
                Date yesterday = DateUtils.subtractDay(new Date(), 1);

                if (ObjectUtils.isEmpty(lastUpdateDate)) {
                    lastUpdateDate = DateUtils.format(yesterday, "yyyy-MM-dd 00:00:00");
                }
                if (ObjectUtils.isEmpty(lastUpdateDateEnd)) {
                    lastUpdateDateEnd = DateUtils.format(new Date(), "yyyy-MM-dd 23:59:59");
                }

                List<DictDto> inventoryTypeList = basedataExtService.getLtcDict(DictType.INVENTORY_TYPE.code(), null, null);
                String inventoryType = inventoryTypeList.stream().map(DictDto::getCode).collect(Collectors.joining(","));
                String assetInventory = "2";
                logger.info("获取的请求参数inventoryType为:{},assetInventory:{}", inventoryType, assetInventory);
                List<StorageInventoryDto> storageInventoryDtoList = basedataExtService.selectStorageInventoryListByTypeAndAssetInventory(inventoryType, assetInventory);
                //如果无有效的子库配置，退出
                if (CollectionUtils.isNotEmpty(storageInventoryDtoList)) {
                    // 获取计量单位
                    Map<String, String> unitMap = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null)
                            .stream().collect(Collectors.toMap(DictDto::getName, DictDto::getCode, (key1, key2) -> key2));

                    logger.info("调取erp拉取领退料数据!");
                    for (StorageInventoryDto storageInventoryDto : storageInventoryDtoList) {
                        paramMap.clear();
//                        paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(storageInventoryDto.getOrganizationId()));//库存组织id
//                        paramMap.put(EsbConstant.ERP_IP_P02, storageInventoryDto.getSecondaryInventoryName());//子库
//                        paramMap.put(EsbConstant.ERP_IP_P03, lastUpdateDate);//最后更新時間
//                        paramMap.put(EsbConstant.ERP_IP_P04, lastUpdateDateEnd);//最后更新时间结束
                        paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(storageInventoryDto.getOrganizationId()));//库存组织id
                        paramMap.put(EsbConstant.ERP_SDP_P02, storageInventoryDto.getSecondaryInventoryName());//子库
                        paramMap.put(EsbConstant.ERP_SDP_P03, lastUpdateDate);//最后更新時間
                        paramMap.put(EsbConstant.ERP_SDP_P04, lastUpdateDateEnd);//最后更新时间结束

                        //调erp接口
//                        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_068, paramMap);
                        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_068, paramMap);
                        logger.info("本次拉取的数据量大小：" + returnItemList.size());
                        //解析数据-按子库遍历，找到数据后退出本次循环
                        List<FormalMaterialGetAndReturn> list = setValueByStorageInventory(returnItemList, storageInventoryDtoList, unitMap);
                        //入表-更新或者插入
                        handleDataFormalMaterialGetAndReturn(list);
                    }

                }
            }
        } catch (Exception e) {
            logger.error("调取erp拉取领退料数据异常", e);
        } finally {
            DistributedCASLock.unLock(lockKey, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleDataFormalMaterialGetAndReturn(List<FormalMaterialGetAndReturn> list) {
        for (FormalMaterialGetAndReturn entry : list) {
            //查重
            Long transitionId = entry.getTransitionId();
            FormalMaterialGetAndReturnExample example = new FormalMaterialGetAndReturnExample();
            example.createCriteria().andTransitionIdEqualTo(transitionId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<FormalMaterialGetAndReturn> formalMaterialGetAndReturnList = formalMaterialGetAndReturnMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(formalMaterialGetAndReturnList)) {
                //插入领退料单接收正式表
                entry.setChangeMaterialGetAndReturn(false);
                formalMaterialGetAndReturnMapper.insert(entry);
            } else {
                FormalMaterialGetAndReturn formalMaterialGetAndReturn = formalMaterialGetAndReturnList.get(0);
                //如果领退料单接收正式表数据已经处理，即使erp接口数据有更新也不做处理
                if (Objects.equals(formalMaterialGetAndReturn.getChangeMaterialGetAndReturn(), Boolean.FALSE)) {
                    entry.setId(formalMaterialGetAndReturn.getId());
                    entry.setCreateAt(null);
                    formalMaterialGetAndReturnMapper.updateByPrimaryKeySelective(entry);
                }
            }

        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void batchInsertMaterialReturn(List<MaterialReturnDetailDto> materialReturnDetailDtoList, StorageInventoryDto storageInventoryDto) {
        Map<String, List<MaterialReturnDetailDto>> map = new HashMap<>();
        for (MaterialReturnDetailDto materialReturnDetailDto : materialReturnDetailDtoList) {
            //汇总维度：库存组织ID+事务处理类型+接收子库+事务处理日期+采购订单号 + 项目号
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(materialReturnDetailDto.getOrgId())
                    .append(materialReturnDetailDto.getInventoryCode())
                    .append(DateUtils.format(materialReturnDetailDto.getTransitionDate(), DateUtils.FORMAT_SHORT))
                    .append(materialReturnDetailDto.getSource())
                    .append(materialReturnDetailDto.getProjectId());
            List<MaterialReturnDetailDto> materialReturnDetailDtos = map.get(stringBuilder.toString());
            if (CollectionUtils.isEmpty(materialReturnDetailDtos)) {
                map.put(stringBuilder.toString(), Lists.newArrayList(materialReturnDetailDto));
            } else {
                materialReturnDetailDtos.add(materialReturnDetailDto);
            }
        }
        Set<String> keySet = map.keySet();
        Iterator<String> it = keySet.iterator();
        while (it.hasNext()) {
            String key = it.next();
            List<MaterialReturnDetailDto> materialReturnDetailDtos = map.get(key);
            /**
             * 生成退料单头/退料单行数据 begin
             */
            MaterialReturnHeader materialReturnHeader = new MaterialReturnHeader();
            BigDecimal applyAmount = BigDecimal.ZERO;
            BigDecimal actualAmount = BigDecimal.ZERO;
            String generateReturnCode = materialReturnService.generateReturnCode(storageInventoryDto.getParentUnitId()); // 退料单号
            for (MaterialReturnDetailDto dto : materialReturnDetailDtos) {
                applyAmount = applyAmount.add(dto.getApplyAmount() == null ? BigDecimal.ZERO : dto.getApplyAmount().abs());
                actualAmount = actualAmount.add(dto.getActualAmount() == null ? BigDecimal.ZERO : dto.getActualAmount().abs());

                // 退料单的成本和数量不需要带负号
                dto.setActualAmount(dto.getApplyAmount() == null ? BigDecimal.ZERO : dto.getApplyAmount().abs());
                dto.setActualAmount(dto.getActualAmount() == null ? BigDecimal.ZERO : dto.getActualAmount().abs());
                dto.setActualCost(dto.getActualCost() == null ? BigDecimal.ZERO : dto.getActualCost().abs());
                // 物料ERP ID
                dto.setMaterialErpId(dto.getMaterialId() == null ? null : dto.getMaterialId());
                // 物料ID
                if (dto.getOrgId() != null && dto.getMaterialCode() != null) {
                    Long materialId = basedataExtService.getIdByOrganizationIdAndItemCode(dto.getOrgId(), dto.getMaterialCode());
                    dto.setMaterialId(materialId);
                }
                //实际发料日期
                dto.setActualReturnAt(dto.getTransitionDate());
            }
            materialReturnHeader.setId(null);
            materialReturnHeader.setStatus(5);
            materialReturnHeader.setReturnCode(generateReturnCode);
            materialReturnHeader.setFillUserName("ERP");
            materialReturnHeader.setReturnUserName("ERP");
            materialReturnHeader.setProjectId(materialReturnDetailDtos.get(0).getProjectId() == null ? -1 : materialReturnDetailDtos.get(0).getProjectId());
            materialReturnHeader.setProjectCode(materialReturnDetailDtos.get(0).getProjectCode());
            materialReturnHeader.setProjectName(materialReturnDetailDtos.get(0).getProjectName());
            materialReturnHeader.setOrganizationId(storageInventoryDto.getOrganizationId());
            materialReturnHeader.setOrganizationCode(storageInventoryDto.getOrganizationCode());
            materialReturnHeader.setOrganizationName(storageInventoryDto.getOrganizationName());
            materialReturnHeader.setAccountAliasConcat(materialReturnDetailDtos.get(0).getAccountAliasConcat());
            materialReturnHeader.setTotalApplyAmount(applyAmount);
            materialReturnHeader.setTotalActualAmount(actualAmount);
            materialReturnHeader.setTotalDifferenceAmount(new BigDecimal(0));
            materialReturnHeader.setInventoryId(materialReturnDetailDtos.get(0).getInventoryId());
            materialReturnHeader.setInventoryCode(materialReturnDetailDtos.get(0).getInventoryCode());
            materialReturnHeader.setInventoryName(materialReturnDetailDtos.get(0).getInventoryName());
            materialReturnHeader.setRemark("自动退料，来源：" + materialReturnDetailDtos.get(0).getSource());
            //获取项目类型
            Integer materialGetType = getMaterialGetType(materialReturnHeader.getProjectId());
            materialReturnHeader.setMaterialGetType(materialGetType);
            materialReturnHeader.setApplyTime(new Date());
            materialReturnHeader.setErpCode("000000");
            materialReturnHeader.setDeletedFlag(false);
            materialReturnHeader.setCreateAt(new Date());

            // 保存头数据
            logger.info("插入退料头信息：{}", materialReturnHeader.toString());
            materialReturnHeaderMapper.insert(materialReturnHeader);

            List<MaterialReturnDetail> materialReturnDetails = BeanConverter.copy(materialReturnDetailDtos, MaterialReturnDetail.class);
            for (MaterialReturnDetail materialReturnDetail : materialReturnDetails) {
                materialReturnDetail.setHeaderId(materialReturnHeader.getId());
                //保存行数据
                logger.info("插入退料行信息：{}", materialReturnDetail.toString());
                materialReturnDetailMapper.insert(materialReturnDetail);
                // 更新“是否已转换领退料单” 为是
                logger.info("更新领/退料单接收正式表事务处理id：{}", materialReturnDetail.getTransitionId());
                materialGetDetailExtMapper.updateChangeGetAndReturn(materialReturnDetail.getTransitionId());
            }

        }

    }

    /**
     * 根据项目号找到项目类型,定义400为错误类型
     *
     * @param projectId
     * @return Integer
     */
    private Integer getMaterialGetType(Long projectId) {
        Integer i = 1;
        Project project = projectService.selectByPrimaryKey(projectId);
        if (project != null) {
            ProjectType projectType = projectTypeService.selectByPrimaryKey(project.getType());
            Boolean wbsEnabled = projectType.getWbsEnabled();
            //int workOrderTaskConfig = Integer.parseInt(projectType.getWorkOrderTaskConfig());
            if (wbsEnabled) {
                i = 2;//领料类型的值为"项目领料（WBS），走WBS逻辑处理接口表数据
            }
        }
        return i;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsertMaterialGet(List<MaterialGetDetailDto> materialGetDetailDtoList, StorageInventoryDto storageInventoryDto) {
        Map<String, List<MaterialGetDetailDto>> map = new HashMap<>();
        for (MaterialGetDetailDto materialGetDetailDto : materialGetDetailDtoList) {
            //汇总维度：库存组织ID+事务处理类型+接收子库+事务处理日期+采购订单号 + 项目号
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(materialGetDetailDto.getOrgId())
                    .append(materialGetDetailDto.getInventoryCode())
                    .append(DateUtils.format(materialGetDetailDto.getTransitionDate(), DateUtils.FORMAT_SHORT))
                    .append(materialGetDetailDto.getSource())
                    .append(materialGetDetailDto.getProjectId());
            List<MaterialGetDetailDto> materialGetDetailDtos = map.get(stringBuilder.toString());
            if (CollectionUtils.isEmpty(materialGetDetailDtos)) {
                map.put(stringBuilder.toString(), Lists.newArrayList(materialGetDetailDto));
            } else {
                materialGetDetailDtos.add(materialGetDetailDto);
            }
        }
        Set<String> keySet = map.keySet();
        Iterator<String> it = keySet.iterator();
        while (it.hasNext()) {
            String key = it.next();
            List<MaterialGetDetailDto> materialGetDetailDtos = map.get(key);
            /**
             * 生成领料单头/领料单行数据 begin
             */
            MaterialGetHeader materialGetHeader = new MaterialGetHeader();
            BigDecimal applyAmount = BigDecimal.ZERO;
            BigDecimal actualAmount = BigDecimal.ZERO;
            String generateGetCode = generateGetCode(storageInventoryDto.getParentUnitId()); // 领料单号
            for (MaterialGetDetailDto dto : materialGetDetailDtos) {
                applyAmount = applyAmount.add(dto.getApplyAmount());
                actualAmount = actualAmount.add(dto.getActualAmount());
                // 物料ERP ID
                dto.setMaterialErpId(dto.getMaterialId() == null ? null : dto.getMaterialId());
                // 物料ID
                if (dto.getOrgId() != null && dto.getMaterialCode() != null) {
                    Long materialId = basedataExtService.getIdByOrganizationIdAndItemCode(dto.getOrgId(), dto.getMaterialCode());
                    dto.setMaterialId(materialId);
                }
            }
            materialGetHeader.setId(null);
            materialGetHeader.setStatus(5);
            materialGetHeader.setGetCode(generateGetCode);
            materialGetHeader.setFillUserName("ERP");
            materialGetHeader.setGetUserName("ERP");
            materialGetHeader.setProjectId(materialGetDetailDtos.get(0).getProjectId() == null ? -1 : materialGetDetailDtos.get(0).getProjectId());
            materialGetHeader.setProjectCode(materialGetDetailDtos.get(0).getProjectCode());
            materialGetHeader.setProjectName(materialGetDetailDtos.get(0).getProjectName());
            materialGetHeader.setOrganizationId(storageInventoryDto.getOrganizationId());
            materialGetHeader.setOrganizationCode(storageInventoryDto.getOrganizationCode());
            materialGetHeader.setOrganizationName(storageInventoryDto.getOrganizationName());
            materialGetHeader.setAccountAliasConcat(materialGetDetailDtos.get(0).getAccountAliasConcat());
            materialGetHeader.setTotalApplyAmount(applyAmount);
            materialGetHeader.setTotalActualAmount(actualAmount);
            materialGetHeader.setTotalDifferenceAmount(new BigDecimal(0));
            materialGetHeader.setInventoryId(materialGetDetailDtos.get(0).getInventoryId());
            materialGetHeader.setInventoryCode(materialGetDetailDtos.get(0).getInventoryCode());
            materialGetHeader.setInventoryName(materialGetDetailDtos.get(0).getInventoryName());
            materialGetHeader.setRemark("自动领料，来源：" + materialGetDetailDtos.get(0).getSource());
            //获取项目类型
            Integer materialGetType = getMaterialGetType(materialGetHeader.getProjectId());
            materialGetHeader.setMaterialGetType(materialGetType);
            materialGetHeader.setApplyTime(new Date());
            materialGetHeader.setErpCode("000000");
            materialGetHeader.setDeletedFlag(0);
            materialGetHeader.setCreateAt(new Date());

            // 保存头数据
            logger.info("插入领料头信息：{}", materialGetHeader.toString());
            materialGetHeaderMapper.insert(materialGetHeader);

            List<MaterialGetDetail> materialGetDetails = BeanConverter.copy(materialGetDetailDtos, MaterialGetDetail.class);
            for (MaterialGetDetail materialGetDetail : materialGetDetails) {
                materialGetDetail.setHeaderId(materialGetHeader.getId());
                //materialGetDetail.setWbsDescription(getWbsDescriptionByWbs(materialGetDetail.getWbsSummaryCode()));获取WBS描述
                //保存行数据
                logger.info("插入领料行信息：{}", materialGetDetail.toString());
                materialGetDetailMapper.insert(materialGetDetail);
                // 更新“是否已转换领退料单” 为是
                logger.info("更新领/退料单接收正式表 事务处理id：{}", materialGetDetail.getTransitionId());
                materialGetDetailExtMapper.updateChangeGetAndReturn(materialGetDetail.getTransitionId());
            }
        }

    }


    @Override
    public void testErp(List<SdpTradeResultResponseEleDto> returnItemList) {
        Map<String, String> paramMap = new HashMap();
        Date yesterday = DateUtils.subtractDay(new Date(), 1);

        String lastUpdateDate = DateUtils.format(yesterday, "yyyy-MM-dd 00:00:00");
        String lastUpdateDateEnd = DateUtils.format(new Date(), "yyyy-MM-dd 23:59:59");

        //查询有效的领/退料的子库配置
        logger.info("查询有效的项目费用仓的子库配置!");
        String inventoryType = StorageInventoryType.FEE_WAREHOUSE.code();
        List<StorageInventoryDto> storageInventoryDtoList = basedataExtService.selectStorageInventoryList(inventoryType);
        //如果无有效的子库配置，退出
        if (CollectionUtils.isNotEmpty(storageInventoryDtoList)) {
            // 获取计量单位
            Map<String, String> unitMap = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null)
                    .stream().collect(Collectors.toMap(DictDto::getName, DictDto::getCode, (key1, key2) -> key2));

            logger.info("调取erp拉取领退料数据!");
            //List<ERPMassQueryReturnVo> returnItemList =
            logger.info("本次拉取的数据量大小：" + returnItemList.size());
            //解析数据-按子库遍历，找到数据后退出本次循环
            List<FormalMaterialGetAndReturn> list = setValueByStorageInventory(returnItemList, storageInventoryDtoList, unitMap);
            //入表-更新或者插入
            handleDataFormalMaterialGetAndReturn(list);
        }
    }

    public List<FormalMaterialGetAndReturn> setValueByStorageInventory(List<SdpTradeResultResponseEleDto> returnItemList, List<StorageInventoryDto> storageInventoryDtoList, Map<String, String> unitMap) {
        List<FormalMaterialGetAndReturn> list = new ArrayList<>();
        for (SdpTradeResultResponseEleDto returnVo : returnItemList) {
            FormalMaterialGetAndReturn getAndReturn = new FormalMaterialGetAndReturn();
            logger.info("领退料数据：{}", JSONObject.toJSONString(returnVo));
            getAndReturn.setTransitionId(returnVo.getC1() == null ? null : Long.parseLong(returnVo.getC1()));
            logger.info("事物处理日期：{}", returnVo.getC2());
            getAndReturn.setTransitionDate(returnVo.getC2() == null ? null : DateUtil.parseDate(returnVo.getC2()));
            getAndReturn.setSource(returnVo.getC3());
            getAndReturn.setOrgId(returnVo.getC4() == null ? null : Long.parseLong(returnVo.getC4()));
            getAndReturn.setMaterialId(returnVo.getC5() == null ? null : Long.parseLong(returnVo.getC5()));
            getAndReturn.setMaterialCode(returnVo.getC6());
            getAndReturn.setMaterialName(returnVo.getC7());
            getAndReturn.setWbsOrCode(returnVo.getC8()); //这个字段专门存项目号,这个字段必存在
            getAndReturn.setNumber(returnVo.getC10() == null ? new BigDecimal(0) : new BigDecimal(returnVo.getC10()));
            getAndReturn.setMoney(returnVo.getC11() == null ? new BigDecimal(0) : new BigDecimal(returnVo.getC11()));
            getAndReturn.setInventoryCode(returnVo.getC12());
            getAndReturn.setAccountAliasConcat(returnVo.getC13());
            getAndReturn.setTransitionType(returnVo.getC14());
            getAndReturn.setPoLineNumber(returnVo.getC15());
            getAndReturn.setWbsNo(returnVo.getC16()); //这个字段存wbs号
            getAndReturn.setCreateAt(new Date());
            getAndReturn.setDeletedFlag(false);
            getAndReturn.setPullTime(new Date());
            getAndReturn.setId(null);
            if (null != returnVo.getC9()) {
                getAndReturn.setUnit(unitMap.get(returnVo.getC9()));
            }

            if (null != returnVo.getC8() && !returnVo.getC8().isEmpty()) {
                for (StorageInventoryDto storageInventoryDto : storageInventoryDtoList) {
                    ProjectExample projectExample = new ProjectExample();
                    ProjectExample.Criteria criteria = projectExample.createCriteria();
                    criteria.andCodeEqualTo(returnVo.getC8()).andOuIdEqualTo(storageInventoryDto.getOuId()).andDeletedFlagEqualTo(false);
                    List<Project> projects = projectMapper.selectByExample(projectExample);
                    if (CollectionUtils.isNotEmpty(projects)) {
                        getAndReturn.setProjectId(projects.get(0).getId());
                        getAndReturn.setProjectCode(projects.get(0).getCode());
                        getAndReturn.setProjectName(projects.get(0).getName());
                    }
                    if (null != returnVo.getC16() && !returnVo.getC16().isEmpty()) { //WBS
                        getAndReturn.setWbsOrCodeType(2);
                    } else {//非WBS
                        getAndReturn.setWbsOrCodeType(1);
                    }
                    if (null != getAndReturn.getProjectId()) {
                        getAndReturn.setOrganizationCode(storageInventoryDto.getOrganizationCode());
                        getAndReturn.setOrganizationName(storageInventoryDto.getOrganizationName());
                        getAndReturn.setInventoryId(storageInventoryDto.getId());
                        getAndReturn.setInventoryName(storageInventoryDto.getDescription());
                        break;
                    }
                }
            }

//            if (null != returnVo.getC8()) {
//                /** 步骤1：接口表"库存组织ID"+"ATTRIBUTE8"找到PAM项目号（接口表新增字段，分别记录，
//                 * 1.从ERP同步过来的项目号/WBS；2.项目号；3.WBS；4.通过项目号/WBS，查找到的项目号）
//                 先根据"库存组织ID"+"ATTRIBUTE8"直接查找项目
//                 找不到再根据"库存组织ID"+"ATTRIBUTE8"直接查找项目的WBS，再通过WBS查找到项目号
//                 *
//                 */
//                for (StorageInventoryDto storageInventoryDto : storageInventoryDtoList) {
//                    //当成项目号查询
//                    ProjectExample projectExample = new ProjectExample();
//                    ProjectExample.Criteria criteria = projectExample.createCriteria();
//                    criteria.andCodeEqualTo(returnVo.getC8()).andOuIdEqualTo(storageInventoryDto.getOuId()).andDeletedFlagEqualTo(false);
//                    List<Project> projects = projectMapper.selectByExample(projectExample);
//                    if (CollectionUtils.isNotEmpty(projects)) {
//                        getAndReturn.setProjectId(projects.get(0).getId());
//                        getAndReturn.setProjectCode(projects.get(0).getCode());
//                        getAndReturn.setProjectName(projects.get(0).getName());
//                        getAndReturn.setWbsOrCodeType(1);
//                    } else {
//                        //当成WBS查询
//                        Map<String, Object> param = new HashMap<>();
//                        param.put("wbs", returnVo.getC8());
//                        param.put("ouId", storageInventoryDto.getOuId());
//                        List<ProjectWbsBudgetSummary> projectWbsBudgetSummaries = projectWbsBudgetSummaryMapper.selectByWbsAndOuId(param);
//                        if (CollectionUtils.isNotEmpty(projectWbsBudgetSummaries)) {
//                            Long projectId = projectWbsBudgetSummaries.get(0).getProjectId();
//                            Project project = projectService.selectByPrimaryKey(projectId);
//                            //Asserts.notEmpty(project, ErrorCode.CTC_PROJECT_NOT_FIND);
//                            getAndReturn.setProjectId(project.getId());
//                            getAndReturn.setProjectCode(project.getCode());
//                            getAndReturn.setProjectName(project.getName());
//                            getAndReturn.setWbsOrCodeType(2);
//                        }
//                    }
//                    if (null != getAndReturn.getProjectId()) {
//                        getAndReturn.setOrganizationCode(storageInventoryDto.getOrganizationCode());
//                        getAndReturn.setOrganizationName(storageInventoryDto.getOrganizationName());
//                        getAndReturn.setInventoryId(storageInventoryDto.getId());
//                        getAndReturn.setInventoryName(storageInventoryDto.getDescription());
//                        break;
//                    }
//                }
//            }
            list.add(getAndReturn);
        }
        return list;
    }


    @Override
    public void getMaterialGetFromErp(String updateDateFrom, String updateDateTo) {
        Map<String, String> paramMap = new HashMap<>();
        // 配置了组织参数[是否拉取ERP工单领料报表]的使用单位所属的生效ou,并且值为1 才拉取数据
        List<OrganizationCustomDict> organizationCustomDicts = organizationCustomDictService.queryDataByName("是否拉取ERP工单领料报表", OrgCustomDictOrgFrom.OU);
        if (CollectionUtils.isEmpty(organizationCustomDicts)) {
            return;
        }
        for (OrganizationCustomDict organizationCustomDict : organizationCustomDicts) {
            if ("1".equals(organizationCustomDict.getValue())) {
                //取有效的库存组织ID作为P01参数
                OrganizationRelQuery query = new OrganizationRelQuery();
                query.setPamEnabled(0);
                query.setOperatingUnitId(organizationCustomDict.getOrgId());
                List<OrganizationRelDto> orgRelList = organizationRelExtService.invokeApiList(query).getList();
                OrganizationRelDto organizationRelDto = orgRelList.get(0);
                paramMap.clear();
//                paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(organizationRelDto.getOrganizationId()));
//                paramMap.put(EsbConstant.ERP_IP_P02, String.valueOf(organizationRelDto.getOperatingUnitId()));
//                paramMap.put(EsbConstant.ERP_IP_P03, String.valueOf(updateDateFrom));
//                paramMap.put(EsbConstant.ERP_IP_P04, String.valueOf(updateDateTo));
                paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(organizationRelDto.getOrganizationId()));
                paramMap.put(EsbConstant.ERP_SDP_P02, String.valueOf(organizationRelDto.getOperatingUnitId()));
                paramMap.put(EsbConstant.ERP_SDP_P03, String.valueOf(updateDateFrom));
                paramMap.put(EsbConstant.ERP_SDP_P04, String.valueOf(updateDateTo));
                this.getMaterialGetFromErp(paramMap);
            }
        }

    }

    @Override
    public SdpTradeResult getMaterialActualCost(String getOrReturnCode, String organizationId, String codeAndDetailId) {
        Map<String, String> requestParam = new HashMap<>();
        requestParam.put(EsbConstant.ERP_SDP_P01, organizationId);
        requestParam.put(EsbConstant.ERP_SDP_P02, getOrReturnCode);
        if (StringUtils.isNotEmpty(codeAndDetailId)) {
            requestParam.put(EsbConstant.ERP_SDP_P03, codeAndDetailId);
        }
        requestParam.put("sdpPIfaceCode", "ERP-PAM-071");
        return sdpCarrierServicel.callERPQuery(requestParam);
    }

    public void getMaterialGetFromErp(Map<String, String> paramMap) {
        //调erp接口
//        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_069, paramMap);
        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_069, paramMap);
        if (CollectionUtils.isEmpty(returnItemList)) {
            return;
        }

        List<FormalMaterialGet> materialGetList = new ArrayList<>();
        List<Long> transitionIdList = new ArrayList<>();
        for (SdpTradeResultResponseEleDto item : returnItemList) {
            logger.info("领料数据：{}", JSONObject.toJSONString(item));
            FormalMaterialGet materialGet = new FormalMaterialGet();
            materialGet.setTransitionId(item.getC1() == null ? null : Long.valueOf(item.getC1()));
            materialGet.setOrganizationId(item.getC2() == null ? null : Long.valueOf(item.getC2()));
            materialGet.setOrganizationCode(item.getC3());
            materialGet.setSaleOrder(item.getC4());
            materialGet.setErpCode(item.getC5());
            materialGet.setPamCode(item.getC6());
            materialGet.setOrderCode(item.getC7());
            materialGet.setOrderStatus(item.getC8());
            materialGet.setAssembleCode(item.getC9());
            materialGet.setAssembleInfo(item.getC10());
            materialGet.setComponentId(item.getC11() == null ? null : Long.valueOf(item.getC11()));
            materialGet.setComponentCode(item.getC12());
            materialGet.setComponentInfo(item.getC13());
            materialGet.setTransitionDate(DateUtils.parse(item.getC14(), DateUtils.FORMAT_LONG));
            materialGet.setTransitionCost(item.getC15() == null ? new BigDecimal(0) : new BigDecimal(item.getC15()));
            materialGet.setTransitionAmount(item.getC16() == null ? new BigDecimal(0) : new BigDecimal(item.getC16()));
            materialGet.setNumber(item.getC17() == null ? new BigDecimal(0) : new BigDecimal(item.getC17()));
            materialGet.setUnit(item.getC19());
            materialGet.setDeletedFlag(Boolean.FALSE);
            materialGet.setPullTime(new Date());
            materialGet.setId(null);

            if (null != materialGet.getTransitionId()) {
                transitionIdList.add(materialGet.getTransitionId());
            }
            materialGetList.add(materialGet);
        }

        Map<Long, List<FormalMaterialGet>> materialGetMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(transitionIdList)) {
            FormalMaterialGetExample example = new FormalMaterialGetExample();
            example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andTransitionIdIn(transitionIdList);
            List<FormalMaterialGet> formalMaterialGetList = formalMaterialGetMapper.selectByExample(example);
            materialGetMap.putAll(formalMaterialGetList.stream().collect(Collectors.groupingBy(FormalMaterialGet::getTransitionId)));
        }

        List<FormalMaterialGet> insertFormalMaterialGetList = new ArrayList<>();
        List<FormalMaterialGet> updateFormalMaterialGetList = new ArrayList<>();
        for (FormalMaterialGet materialGet : materialGetList) {
            List<FormalMaterialGet> list = null == materialGet.getTransitionId() ? new ArrayList<>() : materialGetMap.get(materialGet.getTransitionId());
            if (CollectionUtils.isNotEmpty(list)) {
                for (FormalMaterialGet formalMaterialGet : list) {
                    materialGet.setId(formalMaterialGet.getId());
                    updateFormalMaterialGetList.add(materialGet);
                }
            } else {
                insertFormalMaterialGetList.add(materialGet);
            }
        }

        logger.info("领料数据的insertFormalMaterialGetList：{}", JSONObject.toJSONString(insertFormalMaterialGetList));
        logger.info("领料数据的updateFormalMaterialGetList：{}", JSONObject.toJSONString(updateFormalMaterialGetList));
        if (CollectionUtils.isNotEmpty(insertFormalMaterialGetList)) {
            formalMaterialGetExtMapper.batchInsert(insertFormalMaterialGetList);
        }
        if (CollectionUtils.isNotEmpty(updateFormalMaterialGetList)) {
            formalMaterialGetExtMapper.batchUpdate(updateFormalMaterialGetList);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getMaterialGetAndReturnFromErpAccept(String lastUpdateDate, String lastUpdateDateEnd) {
        Date yesterday = DateUtils.subtractDay(new Date(), 1);
        if (ObjectUtils.isEmpty(lastUpdateDate)) {
            lastUpdateDate = DateUtils.format(yesterday, "yyyy-MM-dd 00:00:00");
        }
        if (ObjectUtils.isEmpty(lastUpdateDateEnd)) {
            lastUpdateDateEnd = DateUtils.format(new Date(), "yyyy-MM-dd 23:59:59");
        }

        //查询有效的领/退料的子库配置
//        logger.info("查询有效的项目费用仓的子库配置!");
//        String inventoryType = StorageInventoryType.FEE_WAREHOUSE.code();
//        List<StorageInventoryDto> storageInventoryDtoList = basedataExtService.selectStorageInventoryList(inventoryType);
        List<DictDto> inventoryTypeList = basedataExtService.getLtcDict(DictType.INVENTORY_TYPE.code(), null, null);
        String inventoryType = inventoryTypeList.stream().map(DictDto::getCode).collect(Collectors.joining(","));
        String assetInventory = "2";
        logger.info("获取的请求参数inventoryType为:{},assetInventory:{}", inventoryType, assetInventory);
        List<StorageInventoryDto> storageInventoryDtoList = basedataExtService.selectStorageInventoryListByTypeAndAssetInventory(inventoryType, assetInventory);
        logger.info("获取数据为:{}", storageInventoryDtoList.size());
        if (CollectionUtils.isEmpty(storageInventoryDtoList)) {
            return;
        }

        for (StorageInventoryDto storageInventoryDto : storageInventoryDtoList) {
            // 获取两天之内领料单接收表的数据
            List<MaterialGetDetailDto> materialGetDetailDtoList = materialGetDetailExtMapper.selectFromTransitionMaterialGet();
            logger.info("两天之内领料单接收表的数据materialGetDetailDtoList：{}", JSON.toJSONString(materialGetDetailDtoList));
            if (CollectionUtils.isNotEmpty(materialGetDetailDtoList)) {
                List<MaterialGetDetailDto> getDetailDtoList = new ArrayList<>();
                for (MaterialGetDetailDto getDetailDto : materialGetDetailDtoList) {
                    // 启用下,wbs如果活动事项编码、项目编号和项目ID 有一个为空 则不做处理
                    if (getDetailDto.getWbsEnabled()) {
                        if (StringUtils.isNotBlank(getDetailDto.getActivityCode()) && StringUtils.isNotBlank(getDetailDto.getProjectCode()) && Objects.nonNull(getDetailDto.getProjectId())
                                && Objects.equals(getDetailDto.getOrgId(), storageInventoryDto.getOrganizationId())) {
                            getDetailDto.setWbsFullCode(fromatWbsFullCode(getDetailDto.getProjectCode(), getDetailDto.getWbsSummaryCode()));
                            getDetailDtoList.add(getDetailDto);
                        }
                        // 如果活动事项编码、项目编号和项目ID 有一个为空 则不做处理
                    } else if (StringUtils.isNotBlank(getDetailDto.getProjectCode()) && Objects.nonNull(getDetailDto.getProjectId())
                            && Objects.equals(getDetailDto.getOrgId(), storageInventoryDto.getOrganizationId())) {
                        getDetailDtoList.add(getDetailDto);
                    }
                }
                if (CollectionUtils.isEmpty(getDetailDtoList)) {
                    continue;
                }
                logger.info("生成领料单的数据： {}", JSON.toJSONString(getDetailDtoList));
                batchInsertMaterialGet(getDetailDtoList, storageInventoryDto);
            }
            logger.info("从ERP同步领料接收表，生成的领料数据入库成功!");
        }

        for (StorageInventoryDto storageInventoryDto : storageInventoryDtoList) {
            // 获取两天之内退料单接收表的数据
            List<MaterialReturnDetailDto> materialReturnDetailDtoList = materialGetDetailExtMapper.selectFromTransitionMaterialReturn();
            logger.info("两天之内退料单接收表的数据materialReturnDetailDtoList：{}", JSON.toJSONString(materialReturnDetailDtoList));
            if (CollectionUtils.isNotEmpty(materialReturnDetailDtoList)) {
                List<MaterialReturnDetailDto> returnDetailDtoList = new ArrayList<>();
                for (MaterialReturnDetailDto returnDetailDto : materialReturnDetailDtoList) {
                    // 启用下,wbs如果活动事项编码、项目编号和项目ID 有一个为空 则不做处理
                    if (returnDetailDto.getWbsEnabled()) {
                        if (StringUtils.isNotBlank(returnDetailDto.getActivityCode()) && StringUtils.isNotBlank(returnDetailDto.getProjectCode()) && Objects.nonNull(returnDetailDto.getProjectId())
                                && Objects.equals(returnDetailDto.getOrgId(), storageInventoryDto.getOrganizationId())) {
                            returnDetailDto.setWbsFullCode(fromatWbsFullCode(returnDetailDto.getProjectCode(), returnDetailDto.getWbsSummaryCode()));
                            returnDetailDtoList.add(returnDetailDto);
                        }
                        // 如果项目编号和项目ID 有一个为空 则不做处理
                    } else if (StringUtils.isNotBlank(returnDetailDto.getProjectCode()) && Objects.nonNull(returnDetailDto.getProjectId())
                            && Objects.equals(returnDetailDto.getOrgId(), storageInventoryDto.getOrganizationId())) {
                        returnDetailDtoList.add(returnDetailDto);
                    }
                }
                if (CollectionUtils.isEmpty(returnDetailDtoList)) {
                    continue;
                }
                logger.info("生成退料单的数据： {}", JSON.toJSONString(returnDetailDtoList));
                batchInsertMaterialReturn(returnDetailDtoList, storageInventoryDto);
            }
            logger.info("从ERP同步退料接收表，生成的退料数据入库成功!");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<MaterialGetHeaderExcelVo> importMaterialGetHeader(List<MaterialGetHeaderExcelVo> excelVoList) {

        boolean isPass = validMaterialGetHeaderExcelVo(excelVoList);

        if (isPass) {
            List<List<MaterialGetHeader>> materialGetHeaderNewLists = new ArrayList<>(100);
            List<MaterialGetHeader> materialGetHeaderNewList = new ArrayList<>(500);
            for (MaterialGetHeaderExcelVo vo : excelVoList) {
                MaterialGetHeader materialGetHeader = BeanConverter.copy(vo, MaterialGetHeader.class);
                materialGetHeader.setIsImport(1);
                materialGetHeader.setStatus(5);
                materialGetHeader.setTotalActualAmount(materialGetHeader.getTotalApplyAmount());
                materialGetHeader.setTotalDifferenceAmount(BigDecimal.ZERO);
                materialGetHeader.setErpCode("000000");
                materialGetHeader.setDeletedFlag(0);
                materialGetHeader.setAccountAliasConcat("133602.0.**********.0.0.0.0");
                if (materialGetHeader.getId() == null) {
                    materialGetHeader.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    materialGetHeader.setCreateAt(new Date());
                    materialGetHeaderNewList.add(materialGetHeader);
                    if (materialGetHeaderNewList.size() >= 500) {
                        materialGetHeaderNewLists.add(materialGetHeaderNewList);
                        materialGetHeaderNewList = new ArrayList<>(500);
                    }
                } else {
                    materialGetHeader.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    materialGetHeader.setUpdateAt(new Date());
                    materialGetHeaderMapper.updateByPrimaryKeySelective(materialGetHeader);
                }
            }
            if (materialGetHeaderNewList.size() > 0) {
                materialGetHeaderNewLists.add(materialGetHeaderNewList);
            }
            materialGetHeaderNewLists.forEach(subList ->
                    materialGetHeaderExtMapper.batchInsert(subList)
            );
            return Collections.emptyList();
        }
        return excelVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList());
    }

    /**
     * 校验导入的领料单数据
     *
     * @param excelVoList 领料单数据集合
     * @return 校验通过返回true
     */
    private boolean validMaterialGetHeaderExcelVo(List<MaterialGetHeaderExcelVo> excelVoList) {

        excelVoList.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (excelVoList.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getErrMsg()))) {
            return false;
        }

        List<String> returnUserMip = excelVoList.stream().map(MaterialGetHeaderExcelVo::getGetUserMip)
                .distinct().collect(Collectors.toList());
        List<String> fillUserMip = excelVoList.stream().map(MaterialGetHeaderExcelVo::getFillUserMip)
                .distinct().collect(Collectors.toList());
        Set<String> usernames = new HashSet<>(returnUserMip);
        usernames.addAll(fillUserMip);
        Map<String, UserInfoDto> userMap = getUserByUsernames(usernames).stream()
                .collect(Collectors.toMap(UserInfoDto::getUsername, e -> e));

        List<String> projectCodes = excelVoList.stream().map(MaterialGetHeaderExcelVo::getProjectCode).distinct().collect(Collectors.toList());
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andCodeIn(projectCodes);
        Map<String, Project> projectMap = projectMapper.selectByExample(projectExample).stream()
                .collect(Collectors.toMap(Project::getCode, e -> e));

        List<String> orgNames = excelVoList.stream().map(MaterialGetHeaderExcelVo::getOrganizationName)
                .distinct().collect(Collectors.toList());
        Map<String, OrganizationRelDto> organizationRelMap = getOrganizationRelByOrgNames(orgNames).stream()
                .collect(Collectors.toMap(OrganizationRelDto::getOrganizationName, e -> e));

        List<String> inventoryCodes = excelVoList.stream().map(MaterialGetHeaderExcelVo::getInventoryCode)
                .distinct().collect(Collectors.toList());
        Map<String, Map<Long, StorageInventoryDto>> storageInventoryMap = new HashMap<>();
        getStorageInventoryByInventoryCodes(inventoryCodes).forEach(e -> {
            storageInventoryMap.computeIfAbsent(e.getSecondaryInventoryName(), k -> new HashMap<>()).put(e.getOrganizationId(), e);
        });

        List<String> getCodes = excelVoList.stream().map(MaterialGetHeaderExcelVo::getGetCode).distinct().collect(Collectors.toList());
        MaterialGetHeaderExample materialGetHeaderExample = new MaterialGetHeaderExample();
        materialGetHeaderExample.createCriteria().andDeletedFlagEqualTo(0)
                .andGetCodeIn(getCodes);
        Map<String, Long> materialGetHeaderMap = materialGetHeaderMapper.selectByExample(materialGetHeaderExample).stream()
                .collect(Collectors.toMap(MaterialGetHeader::getGetCode, MaterialGetHeader::getId));

        excelVoList.forEach(vo -> {
            List<String> errMsgs = new ArrayList<>();

            vo.setId(materialGetHeaderMap.get(vo.getGetCode()));

            switch (vo.getMaterialGetTypeName()) {
                case "工单领料":
                    vo.setMaterialGetType(0);
                    break;
                case "项目领料":
                    vo.setMaterialGetType(1);
                    break;
                case "项目领料（WBS）":
                    vo.setMaterialGetType(2);
                    break;
                default:
                    errMsgs.add("不支持的领料类型");
            }
            UserInfoDto user = userMap.get(vo.getGetUserMip());
            if (user != null) {
                vo.setGetUserName(user.getName());
                vo.setGetUserId(user.getId());
            } else {
                vo.setGetUserName(vo.getGetUserMip());
            }

            user = userMap.get(vo.getFillUserMip());
            if (user != null) {
                vo.setFillUserName(user.getName());
                vo.setFillUserId(user.getId());
            } else {
                vo.setFillUserName(vo.getFillUserMip());
            }

            Project project = projectMap.get(vo.getProjectCode());
            if (project != null) {
                vo.setProjectId(project.getId());
                vo.setProjectName(project.getName());
            } else {
                errMsgs.add("项目不存在");
            }

            OrganizationRelDto orgRel = organizationRelMap.get(vo.getOrganizationName());
            if (orgRel != null) {
                vo.setOrganizationId(orgRel.getOrganizationId());
                vo.setOrganizationCode(orgRel.getOrganizationCode());
                vo.setOrganizationName(orgRel.getOrganizationName());

                StorageInventoryDto storageInventory = storageInventoryMap.get(vo.getInventoryCode()).get(orgRel.getOrganizationId());
                if (storageInventory != null) {
                    vo.setInventoryId(storageInventory.getId());
                    if (!storageInventory.getDescription().equals(vo.getInventoryName())) {
                        errMsgs.add("仓库名称不匹配");
                    }
                }
            } else {
                errMsgs.add("库存组织不存在");
            }

            vo.setErrMsg(String.join(",", errMsgs));
        });
        return excelVoList.stream().allMatch(e -> StringUtils.isEmpty(e.getErrMsg()));
    }

    private List<OrganizationRelDto> getOrganizationRelByOrgNames(List<String> orgNames) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "organizationRel/listByOrgNames";
        String res = restTemplate.postForObject(url, orgNames, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<OrganizationRelDto>>() {
        });
    }

    private List<StorageInventoryDto> getStorageInventoryByInventoryCodes(List<String> inventoryCodes) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "storageInventory/listByInventoryNames";
        String res = restTemplate.postForObject(url, inventoryCodes, String.class);
        Asserts.notNull(res, Code.ERROR);
        DataResponse<List<StorageInventoryDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<StorageInventoryDto>>>() {
        });
        if (response != null && response.getCode() == 0) {
            return response.getData();
        }
        return new ArrayList<>();
    }

    private List<UserInfoDto> getUserByUsernames(Set<String> usernames) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "employeeInfo/listUserByUserNames";
        String res = restTemplate.postForObject(url, usernames, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<UserInfoDto>>() {
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<MaterialGetDetailExcelVo> importMaterialGetDetail(List<MaterialGetDetailExcelVo> excelVoList, Long unitId) {
        unitId = unitId != null ? unitId : SystemContext.getUnitId();
        boolean isPass = validMaterialGetDetailExcelVo(excelVoList, unitId);
        if (isPass) {
            List<List<MaterialGetDetail>> materialGetDetailLists = new ArrayList<>(100);
            ArrayList<MaterialGetDetail> materialGetDetails = new ArrayList<>(500);
            for (MaterialGetDetailExcelVo vo : excelVoList) {
                MaterialGetDetail materialGetDetail = BeanConverter.copy(vo, MaterialGetDetail.class);
                materialGetDetail.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                materialGetDetail.setCreateAt(new Date());
                materialGetDetail.setIsImport(1);
                materialGetDetail.setItemCostIsNull(0);
                materialGetDetail.setDifferenceAmount(materialGetDetail.getApplyAmount().subtract(materialGetDetail.getActualAmount()));
                materialGetDetail.setErpCode("2");
                materialGetDetail.setDeletedFlag(0);
                materialGetDetail.setTransitionDate(materialGetDetail.getTradeTime());
                materialGetDetail.setCollectionDate(DateUtils.getShortDate(materialGetDetail.getTradeTime()));
                materialGetDetails.add(materialGetDetail);
                if (materialGetDetails.size() >= 500) {
                    materialGetDetailLists.add(materialGetDetails);
                    materialGetDetails = new ArrayList<>(excelVoList.size());
                }
            }
            if (materialGetDetails.size() > 0) {
                materialGetDetailLists.add(materialGetDetails);
            }
            materialGetDetailLists.forEach(subList ->
                    materialGetDetailExtMapper.batchInsertMaterialGetDetail(subList)
            );
            return Collections.emptyList();
        }
        return excelVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList());
    }

    @Override
    public Map<String, BigDecimal> queryGetNumMapByProjectId(Long projectId) {
        Map<String, BigDecimal> getNumMap = new HashMap<>();
        List<MaterialGetDetail> materialGetDetails = detailExtMapper.sumMaterialGetDetailByProjecAndMaterialCode(projectId);
        if (ListUtils.isNotEmpty(materialGetDetails)) {
            getNumMap = materialGetDetails.stream().collect(Collectors.toMap(
                    MaterialGetDetail::getMaterialCode, MaterialGetDetail::getActualAmount, (e1, e2) -> e1));
        }
        return getNumMap;
    }

    private boolean validMaterialGetDetailExcelVo(List<MaterialGetDetailExcelVo> excelVoList, Long unitId) {
        excelVoList.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (excelVoList.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getErrMsg()))) {
            return false;
        }

        List<String> getCodes = excelVoList.stream().map(MaterialGetDetailExcelVo::getGetCode).distinct().collect(Collectors.toList());
        MaterialGetHeaderExample materialGetHeaderExample = new MaterialGetHeaderExample();
        materialGetHeaderExample.createCriteria().andDeletedFlagEqualTo(0)
                .andGetCodeIn(getCodes);
        Map<String, MaterialGetHeader> materialGetHeaderMap = materialGetHeaderMapper.selectByExample(materialGetHeaderExample).stream()
                .collect(Collectors.toMap(MaterialGetHeader::getGetCode, e -> e));

        List<String> materialCode = excelVoList.stream().map(MaterialGetDetailExcelVo::getMaterialCode).distinct().collect(Collectors.toList());
        Map<String, MaterialDto> materialMap = getMaterialByItemCodes(materialCode).stream()
                .collect(Collectors.toMap(e -> e.getItemCode() + "_" + e.getOrganizationId(), e -> e, (e1, e2) -> e1));

        List<String> summaryCodes = excelVoList.stream().map(MaterialGetDetailExcelVo::getWbsSummaryCode).distinct().collect(Collectors.toList());
        Map<String, String> projectWbsBudgetSummaryMap = projectWbsBudgetSummaryExtMapper.selectBySummaryCodes(summaryCodes).stream()
                .collect(Collectors.toMap(ProjectWbsBudgetSummaryDto::getSummaryCode, ProjectWbsBudgetSummaryDto::getDescription));

        List<String> activityCodes = excelVoList.stream().map(MaterialGetDetailExcelVo::getActivityCode).distinct().collect(Collectors.toList());
        Map<String, ProjectActivity> projectActivityMap = projectActivityExtMapper.selectByCodeAndUnitId(activityCodes, unitId).stream()
                .collect(Collectors.toMap(ProjectActivity::getCode, e -> e));

        excelVoList.forEach(vo -> {
            List<String> errMsgList = new ArrayList<>();

            MaterialGetHeader header = materialGetHeaderMap.get(vo.getGetCode());
            if (header != null) {
                vo.setHeaderId(header.getId());
                vo.setOrgId(header.getOrganizationId());
                vo.setInventoryId(header.getInventoryId());
                vo.setInventoryCode(header.getInventoryCode());
                vo.setInventoryName(header.getInventoryName());
            } else {
                errMsgList.add("领料单头信息不存在");
            }

            MaterialDto material = materialMap.get(vo.getMaterialCode() + "_" + vo.getOrgId());
            if (material != null) {
                vo.setMaterialId(material.getId());
                vo.setMaterialErpId(material.getItemId());
                vo.setMaterialName(material.getItemInfo());
                vo.setUnit(material.getUnit());
            } else {
                errMsgList.add("物料不存在");
            }

            String description = projectWbsBudgetSummaryMap.get(vo.getWbsSummaryCode());
            if (description != null) {
                vo.setWbsDescription(description);
            } else {
                errMsgList.add("项目wbs预算汇总不存在");
            }

            ProjectActivity projectActivity = projectActivityMap.get(vo.getActivityCode());
            if (projectActivity == null) {
                errMsgList.add("活动事项不存在");
            }

            vo.setErrMsg(String.join(",", errMsgList));
        });
        return excelVoList.stream().allMatch(e -> StringUtils.isEmpty(e.getErrMsg()));
    }

    private List<MaterialDto> getMaterialByItemCodes(List<String> materialCodes) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "material/listByItemCodes";
        String res = restTemplate.postForObject(url, materialCodes, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<MaterialDto>>() {
        });
    }

    @Override
    public void syncMaterialActualCost(String getOrReturnCodes) {
        materialActualCostSyncJob.execute(getOrReturnCodes);
    }

    @Override
    public SdpTradeResult test(String lastUpdateDate, String lastUpdateDateEnd, String orgId) {
        if (ObjectUtils.isEmpty(lastUpdateDate)) {
            Date yesterday = DateUtils.subtractDay(new Date(), 1);
            lastUpdateDate = DateUtils.format(yesterday, "yyyy-MM-dd 00:00:00");
        }
        if (ObjectUtils.isEmpty(lastUpdateDateEnd)) {
            lastUpdateDateEnd = DateUtils.format(new Date(), "yyyy-MM-dd 23:59:59");
        }
        SdpTradeResult sdpTradeResult = null;
        List<Integer> orgList = new ArrayList<>();
        if (StringUtils.isEmpty(orgId)) {
            orgList = Arrays.asList(802, 3082, 804, 100023, 100030, 100042, 100136, 100137, 100149, 100161, 100206, 100289, 100282, 100422);
        } else {
            orgList.add(Integer.valueOf(orgId));
        }
        for (Integer org : orgList) {
            Map<String, String> requestParam = new HashMap<>();
            requestParam.put(EsbConstant.ERP_SDP_P01, String.valueOf(org));
            requestParam.put(EsbConstant.ERP_SDP_P02, lastUpdateDate);
            requestParam.put(EsbConstant.ERP_SDP_P03, lastUpdateDateEnd);
            requestParam.put("sdpPIfaceCode", "ERP-PAM-031");
            sdpTradeResult = sdpCarrierServicel.callERPQuery(requestParam);
            Object responseObj = sdpTradeResult.getResponseObj();
            System.out.println(JSONObject.toJSONString(responseObj));
        }
        return sdpTradeResult;
    }

    private String fromatWbsFullCode(String projectCode, String wbsSummaryCode) {
        // 检查 projectCode 和 wbsSummaryCode 是否为空
        if (StringUtils.isBlank(projectCode) || StringUtils.isBlank(wbsSummaryCode)) {
            return null;
        }
        // 检查 wbsSummaryCode 是否以 projectCode 开头，并且包含 "-"
        if (wbsSummaryCode.startsWith(projectCode + "-")) {
            // 从 wbsSummaryCode 中提取 wbsFullCode
            return wbsSummaryCode.substring(projectCode.length() + 1);
        }
        // 如果格式不正确，返回 null 或者抛出异常
        return null; // 或者  throw new BizException(Code.ERROR, "转换错误");
    }

}
