package com.midea.pam.ctc.api.service.impl;

import com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog;
import com.midea.pam.ctc.api.server.PassInterfaceInvokeLogService;
import com.midea.pam.ctc.mapper.PassInterfaceInvokeLogMapper;

import javax.annotation.Resource;
import java.util.Date;

public class PassInterfaceInvokeLogServiceImpl implements PassInterfaceInvokeLogService {

    @Resource
    private PassInterfaceInvokeLogMapper passInterfaceInvokeLogMapper;


    @Override
    public Long insertLog(String applyNo, String serialNo, String invokeParams, String url, String serviceSystem) {
        PassInterfaceInvokeLog log = new PassInterfaceInvokeLog();
        log.setApplyNo(applyNo);
        log.setSerialNo(serialNo);
        log.setServiceSystem(serviceSystem);
        log.setInvokeUrl(url);
        log.setInvokeParams(invokeParams);
        log.setInvokeStatus(0);
        log.setInvokeTime(new Date());
        log.setDeletedFlag(Boolean.FALSE);
        passInterfaceInvokeLogMapper.insert(log);
        return log.getId();
    }

    @Override
    public void updateLog(PassInterfaceInvokeLog passInterfaceInvokeLog) {
        passInterfaceInvokeLogMapper.updateByPrimaryKeySelective(passInterfaceInvokeLog);
    }
}
