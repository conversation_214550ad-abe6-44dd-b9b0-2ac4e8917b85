package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.ctc.dto.PurchaseBpaPriceDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.entity.MaterialPurchaseBpaPrice;
import com.midea.pam.common.ctc.entity.PurchaseBpaPrice;
import com.midea.pam.common.ctc.entity.PurchaseBpaPriceExample;
import com.midea.pam.common.ctc.entity.PurchaseBpaPriceRecord;
import com.midea.pam.common.ctc.entity.TransitionPurchaseBpaPrice;
import com.midea.pam.common.ctc.excelVo.PurchaseBpaPriceExcelVO;
import com.midea.pam.common.ctc.query.PurchaseBpaPriceQuery;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.PurchaseBpaPriceExtMapper;
import com.midea.pam.ctc.mapper.PurchaseBpaPriceMapper;
import com.midea.pam.ctc.mapper.PurchaseBpaPriceRecordMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.PurchaseBpaPriceService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.system.SystemContext;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @program: pam
 * @description: PurchaseBpaPriceServiceImpl
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
public class PurchaseBpaPriceServiceImpl implements PurchaseBpaPriceService {

    @Resource
    private EsbService esbService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    PurchaseBpaPriceMapper purchaseBpaPriceMapper;
    @Resource
    private PurchaseBpaPriceRecordMapper purchaseBpaPriceRecordMapper;
    @Resource
    private PurchaseBpaPriceExtMapper purchaseBpaPriceExtMapper;
    @Resource
    private SdpCarrierServicel sdpCarrierServicel;


    private PurchaseBpaPriceExample buildExample(PurchaseBpaPriceDto query) {
        PurchaseBpaPriceExample example = new PurchaseBpaPriceExample();
        PurchaseBpaPriceExample.Criteria criteria = example.createCriteria();
        if (!ObjectUtils.isEmpty(query.getMaterialCode())) {
            criteria.andMaterialCodeEqualTo(query.getMaterialCode());
        }
        if (!ObjectUtils.isEmpty(query.getPriceDate())) {
            criteria.andStartDateGreaterThanOrEqualTo(query.getPriceDate());
            criteria.andEndDateLessThanOrEqualTo(query.getPriceDate());
            example.setOrderByClause("price desc");
        }
        return example;
    }

    @Override
    public PurchaseBpaPrice getTopPurchaseBpaPrice(Date priceDate, String materialCode) {
        PurchaseBpaPrice topPrice = null;
        PurchaseBpaPriceDto query = new PurchaseBpaPriceDto();
        query.setPriceDate(priceDate);
        query.setMaterialCode(materialCode);
        PurchaseBpaPriceExample example = this.buildExample(query);
        List<PurchaseBpaPrice> bpaPriceList = purchaseBpaPriceMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(bpaPriceList)) {
            topPrice = bpaPriceList.get(0);
        }
        return topPrice;
    }

    /**
     * PAM-ERP-041 一揽子协议查询
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param isManual  是否手动  true是  false否 区分页面调用还是自动任务
     */
    @Override
    public void asynFromErp(String startDate, String endDate, Boolean isManual) {
        String executeDate = startDate;
        if (null == executeDate) {
            executeDate = DateUtils.format(new Date(), "yyyy-MM-dd 00:00:00");
        }
        if (ObjectUtils.isEmpty(endDate)) {
            endDate = DateUtils.format(new Date(), "yyyy-MM-dd 23:59:59");
        }
        Set<Long> opUnitIdSet = new HashSet<Long>();
        final List<OrganizationRel> rels = getValidOrganization();
        if (isManual) {
            opUnitIdSet = new HashSet(SystemContext.getOus());
        } else {
            for (OrganizationRel orgRel : rels) {
                opUnitIdSet.add(orgRel.getOperatingUnitId());
            }
        }

        for (Long ouId : opUnitIdSet) {
            addOrUpdatePrice(ouId, executeDate, endDate);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public void addOrUpdatePrice(Long ouId, String startDate, String endDate) {
        final Map<String, String> params = new HashMap();
//        params.put(EsbConstant.ERP_IP_P01, String.valueOf(ouId));
//        params.put(EsbConstant.ERP_IP_P02, startDate);
//        params.put(EsbConstant.ERP_IP_P03, endDate);
        params.put(EsbConstant.ERP_SDP_P01, String.valueOf(ouId));
        params.put(EsbConstant.ERP_SDP_P02, startDate);
        params.put(EsbConstant.ERP_SDP_P03, endDate);
//        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_041, params);
        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_041, params);
        if (ListUtils.isEmpty(returnItemList)) {
            return;
        }
        //先增量插入记录表中，可供日志查询
        savePurchaseBpaPriceRecord(returnItemList);

        //先删除临时表记录
        purchaseBpaPriceExtMapper.batchDeleteTransition(ouId);
        List<TransitionPurchaseBpaPrice> transitionPurchaseBpaPriceList = new ArrayList<>();
        for (SdpTradeResultResponseEleDto erpMassQueryReturnVo : returnItemList) {
            TransitionPurchaseBpaPrice purchaseBpaPrice = new TransitionPurchaseBpaPrice();
            purchaseBpaPrice.setOuName(erpMassQueryReturnVo.getC1());//业务实体名称
            purchaseBpaPrice.setAgentName(erpMassQueryReturnVo.getC2());//采购员
            purchaseBpaPrice.setVendorCode(erpMassQueryReturnVo.getC3());
            purchaseBpaPrice.setVendorSiteCode(erpMassQueryReturnVo.getC4());
            purchaseBpaPrice.setCurrency(erpMassQueryReturnVo.getC5());
            purchaseBpaPrice.setRateType(erpMassQueryReturnVo.getC6());
            if (PublicUtil.isNotEmpty(erpMassQueryReturnVo.getC7())) {
                purchaseBpaPrice.setRateDate(DateUtils.getShortDate(erpMassQueryReturnVo.getC7()));
            }
            purchaseBpaPrice.setRate(BigDecimalUtils.stringToBigDecimal(erpMassQueryReturnVo.getC8()));
            purchaseBpaPrice.setBpaType(erpMassQueryReturnVo.getC9());
            purchaseBpaPrice.setPoNumber(erpMassQueryReturnVo.getC10());
            purchaseBpaPrice.setPoLineNumber(erpMassQueryReturnVo.getC11());
            purchaseBpaPrice.setMaterialCode(erpMassQueryReturnVo.getC12());
            purchaseBpaPrice.setPrice(BigDecimalUtils.stringToBigDecimal(erpMassQueryReturnVo.getC13()));
            purchaseBpaPrice.setUnit(erpMassQueryReturnVo.getC14());
            purchaseBpaPrice.setPriceOverride(BigDecimalUtils.stringToBigDecimal(erpMassQueryReturnVo.getC15()));
            purchaseBpaPrice.setStartDate(DateUtil.parseDate(erpMassQueryReturnVo.getC16()));
            purchaseBpaPrice.setEndDate(DateUtil.parseDate(erpMassQueryReturnVo.getC17()));
            purchaseBpaPrice.setQuantity(BigDecimalUtils.stringToBigDecimal(erpMassQueryReturnVo.getC18()));
            purchaseBpaPrice.setErpLineLocationId(erpMassQueryReturnVo.getC20());
            purchaseBpaPrice.setSrmLineLocationId(erpMassQueryReturnVo.getC21());
            purchaseBpaPrice.setDeletedFlag(Boolean.FALSE);
            purchaseBpaPrice.setUpdateAt(new Date());
            VendorSiteBankDto vendor = getVendor(erpMassQueryReturnVo.getC3(), erpMassQueryReturnVo.getC4());
            if (null != vendor) {
                purchaseBpaPrice.setVendorName(vendor.getVendorName());
            }
            OperatingUnit operatingUnit = getOperatingUnit(erpMassQueryReturnVo.getC1());
            if (null != operatingUnit) {
                purchaseBpaPrice.setOuId(operatingUnit.getId());
            }
            transitionPurchaseBpaPriceList.add(purchaseBpaPrice);
        }
        List<List<TransitionPurchaseBpaPrice>> lists = ListUtils.splistList(transitionPurchaseBpaPriceList, 1000);
        for (List<TransitionPurchaseBpaPrice> list : lists) {
            purchaseBpaPriceExtMapper.batchInsertTransition(list);
        }
        // 根据临时表更新正式表数据
        purchaseBpaPriceExtMapper.updateFromTransition(ouId, SystemContext.getUserId());
        // 根据临时表插入正式表
        List<PurchaseBpaPrice> purchaseBpaPriceList = purchaseBpaPriceExtMapper.selectFromInsertTransition(ouId);
        if (ListUtils.isNotEmpty(purchaseBpaPriceList)) {
            List<List<PurchaseBpaPrice>> splistList = ListUtils.splistList(purchaseBpaPriceList, 1000);
            for (List<PurchaseBpaPrice> list : splistList) {
                purchaseBpaPriceExtMapper.batchInsert(list);
            }
        }
    }

    @Override
    public PageInfo<PurchaseBpaPrice> selectPage(PurchaseBpaPriceQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<PurchaseBpaPrice> purchaseBpaPriceList = purchaseBpaPriceExtMapper.getPurchaseBpaPriceList(query);
        return new PageInfo<>(purchaseBpaPriceList);
    }

    @Override
    public Map<String, Object> listExport(PurchaseBpaPriceQuery query) {
        Map<String, Object> resultMap = new HashMap<>();

        final List<PurchaseBpaPriceExcelVO> list = purchaseBpaPriceExtMapper.listExport(query);

        if (ListUtils.isNotEmpty(list)) {
            resultMap.put("purchaseBpaPriceList", list);
        } else {
            resultMap.put("purchaseBpaPriceList", Lists.newArrayList());
        }

        return resultMap;
    }

    @Override
    public MaterialPurchaseBpaPrice getMaterialPurchaseBpaPrice(PurchaseBpaPriceQuery query) {
        return purchaseBpaPriceExtMapper.getMaterialPurchaseBpaPrice(query);
    }

    @Override
    public PurchaseBpaPrice getPurchaseBpaPrice(PurchaseBpaPriceQuery query) {
        return purchaseBpaPriceExtMapper.getPurchaseBpaPrice(query);
    }


    /**
     * 获取系统有效的库存组织.
     *
     * @return 有效的库存组织
     */
    private List<OrganizationRel> getValidOrganization() {
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/organizationRel/getValidOrganization", new HashMap<>());
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<OrganizationRel>> data = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationRel>>>() {
        });
        return data != null ? data.getData() : null;
    }

    /**
     * 获取供应商信息
     *
     * @return 供应商信息
     */
    private VendorSiteBankDto getVendor(String vendorCode, String vendorSiteCode) {
        Map map = new HashMap();
        map.put("vendorCode", vendorCode);
        map.put("vendorSiteCode", vendorSiteCode);
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/vendorSiteBank/getByVendorCodeAndVendorSiteCode", map);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<VendorSiteBankDto> data = JSON.parseObject(res, new TypeReference<DataResponse<VendorSiteBankDto>>() {
        });
        return data != null ? data.getData() : null;
    }

    /**
     * 获取业务实体信息
     *
     * @return 业务实体信息
     */
    private OperatingUnit getOperatingUnit(String name) {
        Map map = new HashMap();
        map.put("name", name);
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operatingUnit/findOperatingUnitByName", map);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<OperatingUnit> data = JSON.parseObject(res, new TypeReference<DataResponse<OperatingUnit>>() {
        });
        return data != null ? data.getData() : null;
    }


    private Long checkRepeat(ERPMassQueryReturnVo erpMassQueryReturnVo) {
        Long result = null;
        PurchaseBpaPriceExample purchaseBpaPriceExample = new PurchaseBpaPriceExample();
        purchaseBpaPriceExample.createCriteria().andErpLineLocationIdEqualTo(erpMassQueryReturnVo.getC20()).andDeletedFlagEqualTo(false);
        List<PurchaseBpaPrice> purchaseBpaPrices = purchaseBpaPriceMapper.selectByExample(purchaseBpaPriceExample);
        if (ListUtils.isNotEmpty(purchaseBpaPrices)) {
            result = purchaseBpaPrices.get(0).getId();
        }
        return result;
    }

    private void savePurchaseBpaPriceRecord(List<SdpTradeResultResponseEleDto> returnItemList) {
        //先删除7天前的记录
        purchaseBpaPriceExtMapper.batchDeleteRecord();
        List<PurchaseBpaPriceRecord> purchaseBpaPriceRecordList = new ArrayList<>();
        for (SdpTradeResultResponseEleDto erpMassQueryReturnVo : returnItemList) {
            PurchaseBpaPriceRecord purchaseBpaPriceRecord = new PurchaseBpaPriceRecord();
            purchaseBpaPriceRecord.setOuName(erpMassQueryReturnVo.getC1());//业务实体名称
            purchaseBpaPriceRecord.setAgentName(erpMassQueryReturnVo.getC2());//采购员
            purchaseBpaPriceRecord.setVendorCode(erpMassQueryReturnVo.getC3());
            purchaseBpaPriceRecord.setVendorSiteCode(erpMassQueryReturnVo.getC4());
            purchaseBpaPriceRecord.setCurrency(erpMassQueryReturnVo.getC5());
            purchaseBpaPriceRecord.setRateType(erpMassQueryReturnVo.getC6());
            if (PublicUtil.isNotEmpty(erpMassQueryReturnVo.getC7())) {
                purchaseBpaPriceRecord.setRateDate(DateUtils.getShortDate(erpMassQueryReturnVo.getC7()));
            }
            purchaseBpaPriceRecord.setRate(BigDecimalUtils.stringToBigDecimal(erpMassQueryReturnVo.getC8()));
            purchaseBpaPriceRecord.setBpaType(erpMassQueryReturnVo.getC9());
            purchaseBpaPriceRecord.setPoNumber(erpMassQueryReturnVo.getC10());
            purchaseBpaPriceRecord.setPoLineNumber(erpMassQueryReturnVo.getC11());
            purchaseBpaPriceRecord.setMaterialCode(erpMassQueryReturnVo.getC12());
            purchaseBpaPriceRecord.setPrice(BigDecimalUtils.stringToBigDecimal(erpMassQueryReturnVo.getC13()));
            purchaseBpaPriceRecord.setUnit(erpMassQueryReturnVo.getC14());
            purchaseBpaPriceRecord.setPriceOverride(BigDecimalUtils.stringToBigDecimal(erpMassQueryReturnVo.getC15()));
            purchaseBpaPriceRecord.setStartDate(DateUtil.parseDate(erpMassQueryReturnVo.getC16()));
            purchaseBpaPriceRecord.setEndDate(DateUtil.parseDate(erpMassQueryReturnVo.getC17()));
            purchaseBpaPriceRecord.setQuantity(BigDecimalUtils.stringToBigDecimal(erpMassQueryReturnVo.getC18()));
            purchaseBpaPriceRecord.setErpLineLocationId(erpMassQueryReturnVo.getC20());
            purchaseBpaPriceRecord.setSrmLineLocationId(erpMassQueryReturnVo.getC21());
            purchaseBpaPriceRecord.setDeletedFlag(Boolean.FALSE);
            purchaseBpaPriceRecord.setUpdateAt(new Date());
            VendorSiteBankDto vendor = getVendor(erpMassQueryReturnVo.getC3(), erpMassQueryReturnVo.getC4());
            if (null != vendor) {
                purchaseBpaPriceRecord.setVendorName(vendor.getVendorName());
            }
            OperatingUnit operatingUnit = getOperatingUnit(erpMassQueryReturnVo.getC1());
            if (null != operatingUnit) {
                purchaseBpaPriceRecord.setOuId(operatingUnit.getId());
            }
            purchaseBpaPriceRecordList.add(purchaseBpaPriceRecord);
        }
        List<List<PurchaseBpaPriceRecord>> lists = ListUtils.splistList(purchaseBpaPriceRecordList, 1000);
        for (List<PurchaseBpaPriceRecord> list : lists) {
            purchaseBpaPriceExtMapper.batchInsertRecord(list);
        }
    }
}
