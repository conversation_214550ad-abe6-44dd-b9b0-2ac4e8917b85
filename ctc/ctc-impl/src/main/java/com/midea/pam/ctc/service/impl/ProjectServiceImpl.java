package com.midea.pam.ctc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.midea.mcomponent.core.util.Assert;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.mip.core.exception.MipException;
import com.midea.pam.cache.ValueCache;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.BudgetDepDto;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.FeeItemDto;
import com.midea.pam.common.basedata.dto.GlDailyRateDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.BudgetDep;
import com.midea.pam.common.basedata.entity.BudgetTree;
import com.midea.pam.common.basedata.entity.FeeItem;
import com.midea.pam.common.basedata.entity.LaborCost;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.ProjectProductMaintenance;
import com.midea.pam.common.basedata.entity.Storage;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.VendorSiteBank;
import com.midea.pam.common.basedata.query.GlDailyRateQuery;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.AssertErrorMessage;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.crm.dto.ProductDto;
import com.midea.pam.common.crm.dto.ProjectSummaryBudget;
import com.midea.pam.common.crm.entity.Business;
import com.midea.pam.common.crm.entity.Customer;
import com.midea.pam.common.crm.entity.Plan;
import com.midea.pam.common.crm.entity.Quotation;
import com.midea.pam.common.ctc.dto.CarryoverBillDto;
import com.midea.pam.common.ctc.dto.CheckStatusDto;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.DeliverChangeProject;
import com.midea.pam.common.ctc.dto.DeliveryInspectionDetailsDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetFeeDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetHumanDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetMaterialDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetTravelDto;
import com.midea.pam.common.ctc.dto.ProjectChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectHistoryHeaderDto;
import com.midea.pam.common.ctc.dto.ProjectMemberDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostGroupDto;
import com.midea.pam.common.ctc.dto.ProjectProfitChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectProfitDto;
import com.midea.pam.common.ctc.dto.ProjectQuery;
import com.midea.pam.common.ctc.dto.ProjectTerminationCheckRelDTO;
import com.midea.pam.common.ctc.dto.ProjectTypeCheckRelDto;
import com.midea.pam.common.ctc.dto.ProjectTypeDto;
import com.midea.pam.common.ctc.dto.PurchaseContractDTO;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.ReceiptPlanDetailDTO;
import com.midea.pam.common.ctc.dto.TerminationCheckStatusDTO;
import com.midea.pam.common.ctc.entity.ApplicationIndustry;
import com.midea.pam.common.ctc.entity.ApplicationIndustryExample;
import com.midea.pam.common.ctc.entity.CarryoverBill;
import com.midea.pam.common.ctc.entity.CarryoverBillExample;
import com.midea.pam.common.ctc.entity.CarryoverBillIncomeCollection;
import com.midea.pam.common.ctc.entity.CheckItem;
import com.midea.pam.common.ctc.entity.CodeRule;
import com.midea.pam.common.ctc.entity.CodeRuleExample;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.ContractChangeway;
import com.midea.pam.common.ctc.entity.ContractChangewayExample;
import com.midea.pam.common.ctc.entity.ContractExample;
import com.midea.pam.common.ctc.entity.ContractPaymentSummary;
import com.midea.pam.common.ctc.entity.CtcAttachment;
import com.midea.pam.common.ctc.entity.CtcAttachmentExample;
import com.midea.pam.common.ctc.entity.DeliveryInspection;
import com.midea.pam.common.ctc.entity.DeliveryInspectionDetails;
import com.midea.pam.common.ctc.entity.DeliveryInspectionDetailsExample;
import com.midea.pam.common.ctc.entity.DeliveryInspectionExample;
import com.midea.pam.common.ctc.entity.EmsPamFeeDetail;
import com.midea.pam.common.ctc.entity.EmsPamFeeDetailExample;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetails;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetailsExample;
import com.midea.pam.common.ctc.entity.InvoiceApplyHeader;
import com.midea.pam.common.ctc.entity.InvoicePlanDetail;
import com.midea.pam.common.ctc.entity.InvoicePlanDetailExample;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.InvoiceReceivableExample;
import com.midea.pam.common.ctc.entity.MaterialGetHeader;
import com.midea.pam.common.ctc.entity.MaterialGetHeaderExample;
import com.midea.pam.common.ctc.entity.MaterialReturnHeader;
import com.midea.pam.common.ctc.entity.MaterialReturnHeaderExample;
import com.midea.pam.common.ctc.entity.MaterialTransferDetail;
import com.midea.pam.common.ctc.entity.MaterialTransferDetailExample;
import com.midea.pam.common.ctc.entity.MaterialTransferHeader;
import com.midea.pam.common.ctc.entity.MaterialTransferHeaderExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanChangeRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanChangeRecordExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecordExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChange;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecordExample;
import com.midea.pam.common.ctc.entity.MilepostTemplateStage;
import com.midea.pam.common.ctc.entity.MilepostTemplateStageExample;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.OrganizationCustomDictExample;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetailExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectAssetRsChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetAsset;
import com.midea.pam.common.ctc.entity.ProjectBudgetAssetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetAssetExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangePushEms;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangePushEmsExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangeSummaryHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetFee;
import com.midea.pam.common.ctc.entity.ProjectBudgetFeeChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetFeeExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetHuman;
import com.midea.pam.common.ctc.entity.ProjectBudgetHumanChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetHumanExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterial;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetTargetChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravel;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravelChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravelExample;
import com.midea.pam.common.ctc.entity.ProjectBusinessRsChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBusinessRsChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectBusinessRsExample;
import com.midea.pam.common.ctc.entity.ProjectChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectContractRs;
import com.midea.pam.common.ctc.entity.ProjectContractRsChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectContractRsChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectContractRsExample;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ProjectFeeCollection;
import com.midea.pam.common.ctc.entity.ProjectFeeCollectionExample;
import com.midea.pam.common.ctc.entity.ProjectHistoryBatchHeader;
import com.midea.pam.common.ctc.entity.ProjectHistoryBatchHeaderExample;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeaderExample;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlanExample;
import com.midea.pam.common.ctc.entity.ProjectMember;
import com.midea.pam.common.ctc.entity.ProjectMemberChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectMemberExample;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectMilepostChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectMilepostExample;
import com.midea.pam.common.ctc.entity.ProjectMilepostGroupChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectProblemExample;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectProfitChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectProfitChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectProfitExample;
import com.midea.pam.common.ctc.entity.ProjectResourceRel;
import com.midea.pam.common.ctc.entity.ProjectResourceRelExample;
import com.midea.pam.common.ctc.entity.ProjectRole;
import com.midea.pam.common.ctc.entity.ProjectRoleExample;
import com.midea.pam.common.ctc.entity.ProjectTerminationCheckRel;
import com.midea.pam.common.ctc.entity.ProjectTerminationCheckRelExample;
import com.midea.pam.common.ctc.entity.ProjectTerminationType;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ProjectTypeExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetBaseline;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetBaselineChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetBaselineExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetExample;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractExample;
import com.midea.pam.common.ctc.entity.PurchaseOrder;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetail;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailExample;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetail;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailExample;
import com.midea.pam.common.ctc.entity.RevenueCostOrder;
import com.midea.pam.common.ctc.entity.RevenueCostOrderDetail;
import com.midea.pam.common.ctc.entity.TerminationInspection;
import com.midea.pam.common.ctc.entity.TerminationInspectionDetails;
import com.midea.pam.common.ctc.entity.TerminationInspectionDetailsExample;
import com.midea.pam.common.ctc.entity.TerminationInspectionExample;
import com.midea.pam.common.ctc.entity.WorkingHour;
import com.midea.pam.common.ctc.entity.WorkingHourExample;
import com.midea.pam.common.ctc.excelVo.CarryoverBillExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectBudgetFeeExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectBudgetHumanExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectBudgetMaterialExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectBudgetTravelExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectFeeCollectionExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectImportExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectIncomeOrProjectProfitExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectMemberExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectProfitExcelVo;
import com.midea.pam.common.ctc.excelVo.RevenueCostOrderExcelVo;
import com.midea.pam.common.ctc.vo.EsbProjectMemberVo;
import com.midea.pam.common.ctc.vo.EsbProjectMilepostVo;
import com.midea.pam.common.ctc.vo.EsbProjectTypeVo;
import com.midea.pam.common.ctc.vo.EsbProjectVo;
import com.midea.pam.common.ctc.vo.ProjectChangeHistoryVO;
import com.midea.pam.common.ctc.vo.ProjectCheckExcelVo;
import com.midea.pam.common.ctc.vo.ProjectCheckReturnVo;
import com.midea.pam.common.ctc.vo.ProjectCostDetailVO;
import com.midea.pam.common.ctc.vo.ProjectPackageChangeHistoryVO;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentVo;
import com.midea.pam.common.ctc.vo.WorkflowInfoVo;
import com.midea.pam.common.enums.CarryoverBillResourceType;
import com.midea.pam.common.enums.CheckItemEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ContractChangewayStatus;
import com.midea.pam.common.enums.CostMethod;
import com.midea.pam.common.enums.CtcProjectChangeType;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErpOrganizationId;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.FeeFlagEnum;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.IncomePoint;
import com.midea.pam.common.enums.InvoiceApplyStatusEnum;
import com.midea.pam.common.enums.MaterialGetStatus;
import com.midea.pam.common.enums.MaterialTransferEnums;
import com.midea.pam.common.enums.MilepostStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PriceTypeEnum;
import com.midea.pam.common.enums.ProjectChangeStatus;
import com.midea.pam.common.enums.ProjectSource;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.PurchaseContractPunishmentApproveEnums;
import com.midea.pam.common.enums.PurchaseContractPunishmentSupplierStatusEnums;
import com.midea.pam.common.enums.PurchaseContractStatus;
import com.midea.pam.common.enums.PushStatus;
import com.midea.pam.common.enums.TransferProjectState;
import com.midea.pam.common.enums.WorkflowOperationType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FormInstanceEvent;
import com.midea.pam.common.sdp.dto.SdpQuery;
import com.midea.pam.common.sdp.vo.ProjectFeeCollectionSdpVo;
import com.midea.pam.common.sdp.vo.ProjectSdpVo;
import com.midea.pam.common.statistics.dto.ProjectCostFeeItemRecordDto;
import com.midea.pam.common.statistics.entity.ProjectCostEaDetailRecord;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.Builder;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.CollectionsUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.EnumUtil;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.common.util.Symbol;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.CorUserStatus;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.enums.MilepostDesignPlanDetailModelStatus;
import com.midea.pam.ctc.common.enums.ProjectMilepostAnnexType;
import com.midea.pam.ctc.common.enums.ProjectProblemStatusEnum;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderDetailStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.ContractService;
import com.midea.pam.ctc.contract.service.impl.OssService;
import com.midea.pam.ctc.feign.basedata.feign.BasedataLtcDictFeignClient;
import com.midea.pam.ctc.feign.statistics.feign.StatisticsProjectCostViewFeignClient;
import com.midea.pam.ctc.mapper.ApplicationIndustryMapper;
import com.midea.pam.ctc.mapper.CarryoverBillIncomeCollectionMapper;
import com.midea.pam.ctc.mapper.CarryoverBillMapper;
import com.midea.pam.ctc.mapper.CheckItemMapper;
import com.midea.pam.ctc.mapper.CodeRuleMapper;
import com.midea.pam.ctc.mapper.ContractChangewayMapper;
import com.midea.pam.ctc.mapper.ContractExtMapper;
import com.midea.pam.ctc.mapper.ContractMapper;
import com.midea.pam.ctc.mapper.CtcAttachmentMapper;
import com.midea.pam.ctc.mapper.DeliveryInspectionDetailsMapper;
import com.midea.pam.ctc.mapper.DeliveryInspectionMapper;
import com.midea.pam.ctc.mapper.EmsPamFeeDetailExtMapper;
import com.midea.pam.ctc.mapper.EmsPamFeeDetailMapper;
import com.midea.pam.ctc.mapper.EsbQueryVoMapper;
import com.midea.pam.ctc.mapper.FileInfoMapper;
import com.midea.pam.ctc.mapper.FormInstanceExtMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailsMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyHeaderMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailMapper;
import com.midea.pam.ctc.mapper.InvoiceReceivableMapper;
import com.midea.pam.ctc.mapper.MaterialGetHeaderMapper;
import com.midea.pam.ctc.mapper.MaterialReturnHeaderMapper;
import com.midea.pam.ctc.mapper.MaterialTransferDetailMapper;
import com.midea.pam.ctc.mapper.MaterialTransferHeaderMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanChangeRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanConfirmRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailChangeMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanSubmitRecordMapper;
import com.midea.pam.ctc.mapper.MilepostTemplateMapper;
import com.midea.pam.ctc.mapper.MilepostTemplateStageMapper;
import com.midea.pam.ctc.mapper.OrganizationCustomDictMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailMapper;
import com.midea.pam.ctc.mapper.ProjectAssetRsChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetAssetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetAssetMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetChangeSummaryHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetFeeChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetFeeMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetHumanChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetHumanMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetMaterialChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetMaterialMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetTargetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetTravelChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetTravelMapper;
import com.midea.pam.ctc.mapper.ProjectBusinessRsChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBusinessRsMapper;
import com.midea.pam.ctc.mapper.ProjectChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectCheckReturnVoMapper;
import com.midea.pam.ctc.mapper.ProjectContractRsChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectContractRsMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.ProjectFeeCollectionMapper;
import com.midea.pam.ctc.mapper.ProjectHistoryBatchHeaderMapper;
import com.midea.pam.ctc.mapper.ProjectHistoryHeaderMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanExtMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectMemberChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectMemberMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostExtMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostGroupChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostMapper;
import com.midea.pam.ctc.mapper.ProjectProblemMapper;
import com.midea.pam.ctc.mapper.ProjectProfitChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectProfitMapper;
import com.midea.pam.ctc.mapper.ProjectResourceRelExtMapper;
import com.midea.pam.ctc.mapper.ProjectResourceRelMapper;
import com.midea.pam.ctc.mapper.ProjectRoleMapper;
import com.midea.pam.ctc.mapper.ProjectTerminationCheckRelExtMapper;
import com.midea.pam.ctc.mapper.ProjectTerminationCheckRelMapper;
import com.midea.pam.ctc.mapper.ProjectTerminationTypeMapper;
import com.midea.pam.ctc.mapper.ProjectTypeCheckRelExtMapper;
import com.midea.pam.ctc.mapper.ProjectTypeCheckRelMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetBaselineChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetBaselineMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetMapper;
import com.midea.pam.ctc.mapper.PurchaseContractExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPunishmentExtMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailMapper;
import com.midea.pam.ctc.mapper.RevenueCostOrderDetailMapper;
import com.midea.pam.ctc.mapper.RevenueCostOrderMapper;
import com.midea.pam.ctc.mapper.TerminationInspectionDetailsMapper;
import com.midea.pam.ctc.mapper.TerminationInspectionMapper;
import com.midea.pam.ctc.mapper.WorkingHourMapper;
import com.midea.pam.ctc.project.service.ProjectBudgetChangePushEmsService;
import com.midea.pam.ctc.project.service.ProjectProfitChangeHistoryService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CrmExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectBudgetFeeService;
import com.midea.pam.ctc.service.ProjectBudgetHumanService;
import com.midea.pam.ctc.service.ProjectBudgetMaterialService;
import com.midea.pam.ctc.service.ProjectBudgetTravelService;
import com.midea.pam.ctc.service.ProjectContractRsService;
import com.midea.pam.ctc.service.ProjectFeeCollectionService;
import com.midea.pam.ctc.service.ProjectIncomeCostPlanService;
import com.midea.pam.ctc.service.ProjectMemberSerice;
import com.midea.pam.ctc.service.ProjectMilepostChangeHistoryService;
import com.midea.pam.ctc.service.ProjectMilepostDeliveryStandardsService;
import com.midea.pam.ctc.service.ProjectMilepostGroupService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectRoleService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.WorkingHourService;
import com.midea.pam.ctc.service.event.ProjectPendingCloseCheckEvent;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

public class ProjectServiceImpl implements ProjectService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String DELIVER_STAGE = "DELIVER_CHANGE_STAGE:";

    @Resource
    private OssService ossService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectBudgetTargetChangeHistoryMapper projectBudgetTargetChangeHistoryMapper;
    @Resource
    private ProjectBudgetChangePushEmsService projectBudgetChangePushEmsService;
    @Resource
    private ProjectFeeCollectionService projectFeeCollectionService;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private ContractExtMapper contractExtMapper;
    @Resource
    private ContractChangewayMapper contractChangewayMapper;
    @Resource
    private EsbQueryVoMapper esbQueryVoMapper;
    @Resource
    private EmsPamFeeDetailMapper emsPamFeeDetailMapper;
    @Resource
    private EmsPamFeeDetailExtMapper emsPamFeeDetailExtMapper;
    @Resource
    private ProjectContractRsService projectContractRsService;
    @Resource
    private InvoiceReceivableMapper invoiceReceivableMapper;
    @Resource
    private CodeRuleMapper codeRuleMapper;
    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;
    @Resource
    private InvoiceApplyDetailsMapper invoiceApplyDetailsMapper;
    @Resource
    private ContractService contractService;
    @Resource
    private FileInfoMapper fileInfoMapper;
    @Resource
    private CheckItemMapper checkItemMapper;
    @Resource
    private ProjectExtMapper projectExtMapper;
    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private ProjectBudgetFeeMapper projectBudgetFeeMapper;
    @Resource
    private ProjectBudgetTravelMapper projectBudgetTravelMapper;
    @Resource
    private WorkingHourMapper workingHourMapper;
    @Resource
    private ProjectCheckReturnVoMapper projectCheckReturnVoMapper;
    @Resource
    private MilepostDesignPlanSubmitRecordMapper submitRecordMapper;
    @Resource
    private PurchaseMaterialRequirementService requirementService;
    @Resource
    private MilepostDesignPlanChangeRecordMapper changeRecordMapper;
    @Resource
    private MilepostDesignPlanConfirmRecordMapper confirmRecordMapper;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private PurchaseContractExtMapper purchaseContractExtMapper;
    @Resource
    private MaterialGetHeaderMapper getHeaderMapper;
    @Resource
    private MaterialReturnHeaderMapper returnHeaderMapper;
    @Resource
    private MilepostDesignPlanDetailMapper designPlanDetailMapper;
    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;
    @Resource
    private PurchaseMaterialRequirementMapper requirementMapper;
    @Resource
    private PurchaseOrderDetailMapper orderDetailMapper;
    @Resource
    private ProjectMilepostMapper projectMilepostMapper;
    @Resource
    private MaterialTransferHeaderMapper transferHeaderMapper;
    @Resource
    private MaterialTransferDetailMapper transferDetailMapper;
    @Resource
    private ProjectMemberMapper projectMemberMapper;
    @Resource
    private ProjectTypeService projectTypeService;
    @Resource
    private MilepostTemplateMapper milepostTemplateMapper;
    @Resource
    private MilepostTemplateStageMapper milepostTemplateStageMapper;
    @Resource
    private CrmExtService crmExtService;
    @Resource
    private ProjectTypeCheckRelMapper projectTypeCheckRelMapper;
    @Resource
    private ProjectTypeCheckRelExtMapper projectTypeCheckRelExtMapper;
    @Resource
    private DeliveryInspectionDetailsMapper deliveryInspectionDetailsMapper;
    @Resource
    private DeliveryInspectionMapper deliveryInspectionMapper;
    @Resource
    private CtcAttachmentMapper ctcAttachmentMapper;
    @Resource
    private ProjectIncomeCostPlanMapper projectIncomeCostPlanMapper;
    @Resource
    private ProjectProfitService projectProfitService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ProjectRoleService projectRoleService;
    @Resource
    private ProjectHistoryHeaderMapper projectHistoryHeaderMapper;
    @Resource
    private ProjectChangeHistoryMapper projectChangeHistoryMapper;
    @Resource
    private ProjectMemberChangeHistoryMapper projectMemberChangeHistoryMapper;
    @Resource
    private ProjectMilepostChangeHistoryMapper projectMilepostChangeHistoryMapper;
    @Resource
    private ProjectProfitChangeHistoryMapper projectProfitChangeHistoryMapper;
    @Resource
    private ProjectBudgetTravelChangeHistoryMapper projectBudgetTravelChangeHistoryMapper;
    @Resource
    private MilepostDesignPlanDetailChangeMapper milepostDesignPlanDetailChangeMapper;
    @Resource
    private ProjectBudgetMaterialChangeHistoryMapper projectBudgetMaterialChangeHistoryMapper;
    @Resource
    private ProjectBudgetHumanChangeHistoryMapper projectBudgetHumanChangeHistoryMapper;
    @Resource
    private ProjectBudgetFeeChangeHistoryMapper projectBudgetFeeChangeHistoryMapper;
    @Resource
    private ProjectBudgetHumanMapper projectBudgetHumanMapper;
    @Resource
    private ProjectBudgetMaterialMapper projectBudgetMaterialMapper;
    @Resource
    private ProjectProfitChangeHistoryService projectProfitChangeHistoryService;
    @Resource
    private ProjectService projectService;
    @Resource
    private WorkingHourService workingHourService;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ProjectResourceRelMapper projectResourceRelMapper;
    @Resource
    private ProjectIncomeCostPlanService projectIncomeCostPlanService;
    @Resource
    private TerminationInspectionMapper terminationInspectionMapper;
    @Resource
    private TerminationInspectionDetailsMapper terminationInspectionDetailsMapper;
    @Resource
    private ProjectTerminationCheckRelExtMapper projectTerminationCheckRelExtMapper;
    @Resource
    private ProjectTerminationCheckRelMapper projectTerminationCheckRelMapper;
    @Resource
    private ProjectTerminationTypeMapper projectTerminationTypeMapper;
    @Resource
    private InvoiceApplyHeaderMapper invoiceApplyHeaderMapper;
    @Resource
    private OrganizationCustomDictMapper organizationCustomDictMapper;
    @Resource
    private ProjectHistoryBatchHeaderMapper projectHistoryBatchHeaderMapper;
    @Resource
    private ApplicationIndustryMapper applicationIndustryMapper;
    @Resource
    private ProjectMemberSerice projectMemberSerice;
    @Resource
    private ProjectTypeMapper projectTypeMapper;
    @Resource
    private ValueCache valueCache;
    @Resource
    private PaymentInvoiceDetailMapper paymentInvoiceDetailMapper;
    @Resource
    private ProjectProblemMapper projectProblemMapper;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private ProjectBudgetMaterialService projectBudgetMaterialService;
    @Resource
    private ProjectRoleMapper projectRoleMapper;
    @Resource
    private ProjectBudgetHumanService projectBudgetHumanService;
    @Resource
    private ProjectBudgetTravelService projectBudgetTravelService;
    @Resource
    private ProjectBudgetFeeService projectBudgetFeeService;
    @Resource
    private ProjectProfitMapper projectProfitMapper;
    @Resource
    private ProjectMilepostExtMapper projectMilepostExtMapper;
    @Resource
    private CarryoverBillMapper carryoverBillMapper;
    @Resource
    private RevenueCostOrderMapper revenueCostOrderMapper;
    @Resource
    private RevenueCostOrderDetailMapper revenueCostOrderDetailMapper;
    @Resource
    private CarryoverBillIncomeCollectionMapper carryoverBillIncomeCollectionMapper;
    @Resource
    private ProjectIncomeCostPlanExtMapper projectIncomeCostPlanExtMapper;
    @Resource
    private ProjectFeeCollectionMapper projectFeeCollectionMapper;
    @Resource
    private ReceiptPlanDetailMapper receiptPlanDetailMapper;
    @Resource
    private BasedataLtcDictFeignClient basedataLtcDictFeignClient;
    @Resource
    private StatisticsProjectCostViewFeignClient statisticsProjectCostViewFeignClient;
    @Resource
    private ProjectContractRsMapper projectContractRsMapper;
    @Resource
    private ProjectBusinessRsMapper projectBusinessRsMapper;
    @Resource
    private ProjectContractRsChangeHistoryMapper projectContractRsChangeHistoryMapper;
    @Resource
    private ProjectBusinessRsChangeHistoryMapper projectBusinessRsChangeHistoryMapper;
    @Resource
    private ProjectAssetRsChangeHistoryMapper projectAssetRsChangeHistoryMapper;
    @Resource
    private ProjectMilepostGroupService projectMilepostGroupService;
    @Resource
    private ProjectMilepostDeliveryStandardsService projectMilepostDeliveryStandardsService;
    @Resource
    private ProjectMilepostGroupChangeHistoryMapper projectMilepostGroupChangeHistoryMapper;
    @Resource
    private ProjectMilepostChangeHistoryService projectMilepostChangeHistoryService;
    @Resource
    private ProjectWbsBudgetMapper projectWbsBudgetMapper;
    @Resource
    private ProjectWbsBudgetBaselineMapper projectWbsBudgetBaselineMapper;
    @Resource
    private ProjectWbsBudgetChangeHistoryMapper projectWbsBudgetChangeHistoryMapper;
    @Resource
    private ProjectWbsBudgetBaselineChangeHistoryMapper projectWbsBudgetBaselineChangeHistoryMapper;
    @Resource
    private ProjectBudgetChangeSummaryHistoryMapper projectBudgetChangeSummaryHistoryMapper;
    @Resource
    private FormInstanceExtMapper formInstanceExtMapper;
    @Resource
    private ProjectResourceRelExtMapper projectResourceRelExtMapper;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private PurchaseContractPunishmentExtMapper purchaseContractPunishmentExtMapper;
    @Resource
    private ProjectBudgetAssetMapper projectBudgetAssetMapper;
    @Resource
    private ProjectBudgetAssetChangeHistoryMapper projectBudgetAssetChangeHistoryMapper;


    @Override
    public long countByExample(ProjectExample example) {
        return projectMapper.countByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return projectMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(Project record) {
        return projectMapper.insert(record);
    }

    @Override
    public int insertSelective(Project record) {
        return projectMapper.insertSelective(record);
    }

    @Override
    public List<Project> selectByExample(ProjectExample example) {
        return projectMapper.selectByExample(example);
    }

    @Override
    public Project selectByPrimaryKey(Long id) {
        return projectMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(Project record) {
        return projectMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByProject(Project record) {

        ProjectContractRsExample example = new ProjectContractRsExample();
        ProjectContractRsExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(record.getId());
        criteria.andDeletedFlagEqualTo(false);
        projectMapper.updateByPrimaryKeySelective(record);


        Project dbProject = projectMapper.selectByPrimaryKey(record.getId());
        List<ProjectContractRs> list = projectContractRsService.selectByExample(example);
        if (list == null || list.size() == 0) {//合同被删除
            dbProject.setContractId(null);
            dbProject.setAmount(null);

            ProjectProfitExample projectProfitExample = new ProjectProfitExample();
            ProjectProfitExample.Criteria projectProfitCriteria = projectProfitExample.createCriteria();
            projectProfitCriteria.andProjectIdEqualTo(record.getId()).andDeletedFlagEqualTo(Boolean.FALSE);

            List<ProjectProfit> list1 = projectProfitService.selectByExample(projectProfitExample);
            if (list1 != null && list1.size() != 0) {
                ProjectProfit projectProfit = list1.get(0);
                if (projectProfit != null) {
                    projectProfit.setMainIncome(null);
                    projectProfit.setUpdateAt(new Date());
                    projectProfitService.updateByPrimaryKey(projectProfit);
                }
            }


            if (dbProject.getType() != null) {
                ProjectType projectType = projectTypeService.selectByPrimaryKey(dbProject.getType());
                if (projectType != null && StringUtils.isNotEmpty(projectType.getName())
                        && ("人力外包".equals(projectType.getName()) || "运维服务".equals(projectType.getName()))) {
                    ProjectIncomeCostPlanExample projectIncomeCostPlanExample = new ProjectIncomeCostPlanExample();
                    ProjectIncomeCostPlanExample.Criteria projectIncomeCostPlanCriteria =
                            projectIncomeCostPlanExample.createCriteria();
                    projectIncomeCostPlanCriteria.andProjectIdEqualTo(record.getId());
                    projectIncomeCostPlanCriteria.andDeletedFlagEqualTo(false);
                    List<ProjectIncomeCostPlan> list2 =
                            projectIncomeCostPlanMapper.selectByExample(projectIncomeCostPlanExample);
                    if (list2 != null) {
                        for (ProjectIncomeCostPlan projectIncomeCostPlan : list2) {
                            projectIncomeCostPlan.setDeletedFlag(true);
                            projectIncomeCostPlan.setUpdateAt(new Date());
                            projectIncomeCostPlanMapper.updateByPrimaryKey(projectIncomeCostPlan);
                        }
                    }
                }
            }


        }
        dbProject.setUpdateAt(new Date());
        return projectMapper.updateByPrimaryKey(dbProject);


    }


    @Override
    public int updateByPrimaryKey(Project record) {
        return projectMapper.updateByPrimaryKey(record);
    }

    @Resource
    private BasedataExtService basedataExtService;

    @Override
    public List<Long> getProjectIdsByUserId(Long userId) {
        if (userId == null) {
            return null;
        }

        ProjectMemberExample projectMemberExample = new ProjectMemberExample();
        projectMemberExample.createCriteria().andUserIdEqualTo(userId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<ProjectMember> list = projectMemberMapper.selectByExample(projectMemberExample);

        if (list == null || list.size() == 0) {
            return null;
        }

        List<Long> ids = new ArrayList<>();
        for (ProjectMember projectMember : list) {
            ids.add(projectMember.getProjectId());
        }
        return ids;
    }

    public String getStrProjectIdsByUserId(Long userId) {
        String projectIdStr = "";
        List<Long> ids = this.getProjectIdsByUserId(userId);
        if (null != ids) {
            for (Long pid : ids) {
                projectIdStr = pid + "," + projectIdStr;
            }
        }
        return projectIdStr.length() > 0 ? projectIdStr.substring(0, projectIdStr.length() - 1) : "''";
    }

    @Override
    public List<ProjectDto> getProjectByUserId(Long userId, String status, Integer businessFlag, String managerName, Long curUnitId) {
        if (userId == null) {
            return null;
        }

        ProjectMemberExample projectMemberExample = new ProjectMemberExample();
        projectMemberExample.createCriteria().andUserIdEqualTo(userId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<ProjectMember> list = projectMemberMapper.selectByExample(projectMemberExample);

        if (list == null || list.size() == 0) {
            return null;
        }

        List<Long> ids = new ArrayList<>();
        for (ProjectMember projectMember : list) {
            ids.add(projectMember.getProjectId());
        }

        if (StringUtils.isEmpty(status)) {
            WorkingHourExample workingHourExample = new WorkingHourExample();
            workingHourExample.createCriteria().andStayApproveUserIdEqualTo(userId).andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code());
            List<WorkingHour> workingHours = workingHourService.selectByExample(workingHourExample);
            if (ListUtils.isNotEmpty(workingHours)) {
                for (WorkingHour workingHour : workingHours) {
                    ids.add(workingHour.getProjectId());
                }
            }
        }

        ProjectExample projectExample = new ProjectExample();
        List<Integer> statusList = new ArrayList<>();
        if (StringUtil.isNotNull(status)) {
            String[] statusStr = status.split(",");
            for (String s : statusStr) {
                if (StringUtil.isNotNull(s)) {
                    statusList.add(Integer.valueOf(s));
                }
            }
        }
        //查询当前使用单位下的项目
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
//        List<Long> units = SystemContext.getSecondUnits();
        if (curUnitId == null) {
            curUnitId = SystemContext.getUnitId();
        }
        List<Long> units = basedataExtService.findUnitIds(curUnitId);
        if (units != null && units.size() > 0) {
            criteria.andUnitIdIn(units);
        }
        if (StringUtils.isNotEmpty(managerName)) {
            criteria.andManagerNameLike("%" + managerName + "%");
        }
        if (businessFlag != null) {
            if (businessFlag == 1) {
                criteria.andProjectSourceNotEqualTo(ProjectSource.BUSINESS.getCode());
            } else {
                criteria.andProjectSourceEqualTo(ProjectSource.BUSINESS.getCode());
            }
        }
        criteria.andIdIn(ids)
                .andStatusNotEqualTo(12)//非作废状态
                .andStatusGreaterThanOrEqualTo(4);//大于等于4，表示非草稿，非财务确认中，非财务确认通过，非审批中
        if (statusList.size() > 0) {
            criteria.andStatusIn(statusList);
        }
        List<Project> projects = projectMapper.selectByExample(projectExample);
        List<ProjectDto> projectDtos = BeanConverter.copy(projects, ProjectDto.class);

        List<Long> projectIds = projectDtos.stream().map(ProjectDto::getId).collect(Collectors.toList());

        if (projectIds == null) {
            projectIds = new ArrayList<>();
        }

        if (projectIds.size() == 0) {
            projectIds.add(-1L);
        }

        WorkingHourExample workingHourExample1 = new WorkingHourExample();
        //填报待审批数量
        workingHourExample1.createCriteria().andDeleteFlagEqualTo(0).andStatusEqualTo(Integer.valueOf((byte) 2))
                .andProjectIdIn(projectIds).andRdmFlagEqualTo(0);
        List<WorkingHour> workingHours1 = workingHourMapper.selectByExample(workingHourExample1);

        //修改待审批数量
        WorkingHourExample workingHourExample2 = new WorkingHourExample();
        workingHourExample2.createCriteria().andDeleteFlagEqualTo(0).andStatusEqualTo(Integer.valueOf((byte) 5))
                .andProjectIdIn(projectIds);
        List<WorkingHour> workingHours2 = workingHourMapper.selectByExample(workingHourExample2);

        /*
         * map1 填报待审批数量
         * map2 修改待审批数量
         * map3 是否配置了限制时间
         * map4 工时填报上限
         */

        Map<Long, Integer> map1 = new HashMap<>();
        Map<Long, Integer> map2 = new HashMap<>();
        Map<Long, String> map3 = new HashMap<>();
        Map<Long, String> map4 = new HashMap<>();
        if (ListUtils.isNotEmpty(workingHours1)) {
            workingHours1.forEach(workingHour -> {
                if (map1.containsKey(workingHour.getProjectId())) {
                    map1.put(workingHour.getProjectId(), map1.get(workingHour.getProjectId()) + 1);
                } else {
                    map1.put(workingHour.getProjectId(), 1);
                }

            });
        }
        if (ListUtils.isNotEmpty(workingHours2)) {
            workingHours2.forEach(workingHour -> {
                if (map2.containsKey(workingHour.getProjectId())) {
                    map2.put(workingHour.getProjectId(), map2.get(workingHour.getProjectId()) + 1);
                } else {
                    map2.put(workingHour.getProjectId(), 1);
                }
            });
        }

        List<Long> ouList = new ArrayList<>();
        List<Long> unList = new ArrayList<>();

        projectDtos.forEach(project -> {
            if (project.getIsImport() != null && project.getIsImport() && project.getOuId() != null) {
                //查询是否配置了限制时间
                ouList.add(project.getOuId());
            }
            Long unitId = SystemContext.getUnitId();
            //根据项目销售部门查询使用单位
            Unit unit = CacheDataUtils.findUnitById(project.getUnitId());
            if (null != unit && null != unit.getParentId()) {
                unitId = unit.getParentId();
            }
            unList.add(unitId);
        });

        //list集合去重
        List<Long> oucollect = ouList.stream().distinct().collect(Collectors.toList());
        List<Long> uncollect = unList.stream().distinct().collect(Collectors.toList());
        List<OrganizationCustomDict> list1 = new ArrayList<>();
        List<OrganizationCustomDict> list2 = new ArrayList<>();
        OrganizationCustomDictExample example1 = new OrganizationCustomDictExample();
        OrganizationCustomDictExample example2 = new OrganizationCustomDictExample();

        if (ListUtils.isNotEmpty(oucollect)) {
            example1.createCriteria().andOrgIdIn(oucollect).andOrgFromEqualTo(OrgCustomDictOrgFrom.OU.code()).
                    andNameEqualTo("工时填报起始日").andDeletedFlagEqualTo(Boolean.FALSE);
            list1 = organizationCustomDictMapper.selectByExample(example1);
        }

        if (ListUtils.isNotEmpty(uncollect)) {
            example2.createCriteria().andOrgIdIn(uncollect).andOrgFromEqualTo(OrgCustomDictOrgFrom.COMPANY.code())
                    .andNameEqualTo("工时填报小时数上限").andDeletedFlagEqualTo(Boolean.FALSE);
            list2 = organizationCustomDictMapper.selectByExample(example2);
        }

        if (ListUtils.isNotEmpty(list1)) {
            for (OrganizationCustomDict organization : list1) {
                Date now = new Date();
                if (organization.getValidAt() != null && organization.getValidAt().after(now)) {
                    continue;
                }
                if (organization.getExpiryAt() != null && organization.getExpiryAt().before(now)) {
                    continue;
                }
                map3.put(organization.getOrgId(), organization.getValue());
            }
        }

        if (ListUtils.isNotEmpty(list2)) {
            list2.forEach(organizationCustomDict -> {
                map4.put(organizationCustomDict.getOrgId(), organizationCustomDict.getValue());
            });
        }

        projectDtos.forEach(dto -> {
            if (map1.containsKey(dto.getId())) {
                dto.setApproveCount(Long.valueOf(map1.get(dto.getId())));
            } else {
                dto.setApproveCount(0L);
            }
            if (map2.containsKey(dto.getId())) {
                dto.setUpdateCount(Long.valueOf(map2.get(dto.getId())));
            } else {
                dto.setUpdateCount(0L);
            }
            if (map3.containsKey(dto.getOuId())) {
                dto.setLimitDate(DateUtil.parseDate(map3.get(dto.getOuId())));
            }
            Unit unit = CacheDataUtils.findUnitById(dto.getUnitId());
            if (map4.containsKey(SystemContext.getUnitId())) {
                if (StringUtil.isNotNull(map4.get(SystemContext.getUnitId()))) {
                    dto.setLimitHour(Integer.parseInt(map4.get(SystemContext.getUnitId())));
                } else {
                    dto.setLimitHour(8);
                }
            }
            if (unit != null && map4.containsKey(unit.getParentId())) {
                if (StringUtil.isNotNull(map4.get(unit.getParentId()))) {
                    dto.setLimitHour(Integer.parseInt(map4.get(unit.getParentId())));
                } else {
                    dto.setLimitHour(8);
                }
            }
        });

//        boolean isEnableMultiRole = businessProjectEnableMultipleRole();
//        projectDtos.forEach(e -> e.setMultiRole(isEnableMultiRole && Integer.valueOf(3).equals(e.getProjectSource())));

        return projectDtos;
    }

    private boolean businessProjectEnableMultipleRole() {
        Set<String> values = organizationCustomDictService.queryByName("商机项目填报工时是否支持多角色",
                SystemContext.getUnitId(),
                OrgCustomDictOrgFrom.COMPANY);
        if (values != null && values.size() > 0) {
            return values.contains("1");
        }
        return false;
    }

    @Override
    public List<ProjectDto> getMyChargeProjects(Long userId, String status) {
        if (userId == null) {
            return null;
        }

        ProjectExample projectExample = new ProjectExample();
        List<Integer> statusList = new ArrayList<>();
        if (StringUtil.isNotNull(status)) {
            String[] statusStr = status.split(",");
            for (String s : statusStr) {
                if (StringUtil.isNotNull(s)) {
                    statusList.add(Integer.valueOf(s));
                }
            }
        }
        //查询当前使用单位下的项目
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
//        List<Long> units = SystemContext.getSecondUnits();
        List<Long> units = basedataExtService.findUnitIds(SystemContext.getUnitId());
        if (units != null && units.size() > 0) {
            criteria.andUnitIdIn(units);
        }
        criteria.andManagerIdEqualTo(userId)
                .andStatusGreaterThanOrEqualTo(4);//大于等于4，表示非草稿，非财务确认中，非财务确认通过，非审批中
        if (statusList.size() > 0) {
            criteria.andStatusIn(statusList);
        }
        List<Project> projects = projectMapper.selectByExample(projectExample);
        List<ProjectDto> projectDtos = BeanConverter.copy(projects, ProjectDto.class);
        for (ProjectDto project : projectDtos) {
            packageDto(project);
        }
        return projectDtos;
    }

    @Override
    public List<ProjectDto> getProjectByUnit(Long userId) {
        if (userId == null) {
            return null;
        }

        ProjectExample projectExample = new ProjectExample();
        //查询当前使用单位下有权限的虚拟部门的项目
        List<Long> ouIds = SystemContext.getOus();
        ouIds.add(-1L);//防止空值
        projectExample.createCriteria().andOuIdIn(ouIds).andDeletedFlagEqualTo(Boolean.FALSE)
                .andStatusGreaterThanOrEqualTo(4);//大于等于4，表示非草稿，非财务确认中，非财务确认通过，非审批中
        List<Project> projects = projectMapper.selectByExample(projectExample);
        List<ProjectDto> projectDtos = BeanConverter.copy(projects, ProjectDto.class);
//        for (ProjectDto project : projectDtos) {
//            packageDto(project);
//        }
        return projectDtos;
    }

    @Override
    public PageInfo<ProjectDto> pageProjectByUserId(ProjectQuery query) {
        if (query.getUserId() == null) {
            return null;
        }
        //查询当前使用单位下的项目
        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        List<Long> units = basedataExtService.findUnitIds(SystemContext.getUnitId());
        if (units != null && units.size() > 0) {
            criteria.andUnitIdIn(units);
        }
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code())
                .andManagerIdEqualTo(query.getUserId())
                .andStatusGreaterThanOrEqualTo(4);//大于等于4，表示非草稿，非财务确认中，非财务确认通过，非审批中

        //是否查询全部(false则查询有审批工时的项目)
        if (Objects.equals(query.getIsAll(), Boolean.FALSE)) {
            WorkingHourExample workingHourExample = new WorkingHourExample();
            WorkingHourExample.Criteria workingHourExampleCriteria = workingHourExample.createCriteria();
            List<Integer> status = new ArrayList<>();
            status.add(2);
            status.add(5);
            workingHourExampleCriteria.andStatusIn(status).andDeleteFlagEqualTo(0);
            List<WorkingHour> workingHours = workingHourMapper.selectByExample(workingHourExample);
            Set<Long> ids = new HashSet<>();
            ids.add(-1L);//防止空值
            if (ListUtils.isNotEmpty(workingHours)) {
                for (WorkingHour workingHour : workingHours) {
                    ids.add(workingHour.getProjectId());
                }
            }
            criteria.andIdIn(new ArrayList<>(ids));
        }

        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<Project> projects = projectMapper.selectByExample(projectExample);
        PageInfo<ProjectDto> projectList = BeanConverter.convertPage(projects, ProjectDto.class);
        for (ProjectDto project : projectList.getList()) {
            packageDto(project);
        }
        return projectList;
    }

    public void packageDto(ProjectDto project) {
        WorkingHourExample workingHourExample = new WorkingHourExample();
        //填报待审批数量
        workingHourExample.createCriteria().andDeleteFlagEqualTo(0).andStatusEqualTo(Integer.valueOf((byte) 2))
                .andProjectIdEqualTo(project.getId()).andRdmFlagEqualTo(0);
        Long approveCount = workingHourMapper.countByExample(workingHourExample);
        project.setApproveCount(approveCount);

        //修改待审批数量
        workingHourExample.clear();
        workingHourExample.createCriteria().andDeleteFlagEqualTo(0).andStatusEqualTo(Integer.valueOf((byte) 5))
                .andProjectIdEqualTo(project.getId());
        Long updateCount = workingHourMapper.countByExample(workingHourExample);
        project.setUpdateCount(updateCount);
        //如果是导入数据
        if (project.getIsImport() != null && project.getIsImport() && project.getOuId() != null) {
            //查询是否配置了限制时间
            project.setLimitDate(getWorkingHourStartDate(project.getOuId()));
        }
        Long unitId = SystemContext.getUnitId();
        //根据项目销售部门查询使用单位
        Unit unit = CacheDataUtils.findUnitById(project.getUnitId());
        if (null != unit && null != unit.getParentId()) {
            unitId = unit.getParentId();
        }
        //查询工时填报上限
        project.setLimitHour(getLimitHours(unitId));
    }

    @Override
    public ProjectDto findDetail(final Long id) {
        final ProjectDto info = new ProjectDto();
        final Project project = selectByPrimaryKey(id);
        Assert.notNull(project, "项目不存在");
        BeanUtils.copyProperties(project, info);

        setDetailData(info);
        return info;
    }

    @Override
    public List<ProjectDto> findDetailBatch(List<Long> projectIdList) {
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andIdIn(projectIdList);
        List<Project> projectList = projectMapper.selectByExample(projectExample);

        Map<Long, ProjectType> projectTypeMap = new HashMap<>();
        List<Long> projectTypeIdList = projectList.stream().map(Project::getType).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(projectTypeIdList)) {
            ProjectTypeExample projectTypeExample = new ProjectTypeExample();
            projectTypeExample.createCriteria().andIdIn(projectTypeIdList);
            projectTypeMap = projectTypeMapper.selectByExample(projectTypeExample).stream().collect(Collectors.toMap(ProjectType::getId,
                    Function.identity()));
        }

        ProjectProfitExample profitExample = new ProjectProfitExample();
        profitExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andProjectIdIn(projectIdList);
        Map<Long, List<ProjectProfit>> projectProfitMap =
                projectProfitService.selectByExample(profitExample).stream().collect(Collectors.groupingBy(ProjectProfit::getProjectId));

        Map<Long, ApplicationIndustry> applicationIndustryMap = new HashMap<>();
        List<Long> applicationIndustryIdList =
                projectList.stream().map(Project::getApplicationIndustry).filter(StringUtils::isNotEmpty).distinct().map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(applicationIndustryIdList)) {
            ApplicationIndustryExample industryExample = new ApplicationIndustryExample();
            industryExample.createCriteria().andIdIn(applicationIndustryIdList);
            applicationIndustryMap =
                    applicationIndustryMapper.selectByExample(industryExample).stream().collect(Collectors.toMap(ApplicationIndustry::getId,
                            Function.identity()));
        }

        List<ProjectDto> projectDtoList = BeanConverter.copy(projectList, ProjectDto.class);
        for (ProjectDto info : projectDtoList) {
//            final ProjectType projectType = projectTypeService.selectByPrimaryKey(info.getType());
            ProjectType projectType = projectTypeMap.get(info.getType());
            info.setTypeName(null != projectType ? projectType.getName() : null);
            info.setPriceTypeName(PriceTypeEnum.getValue(info.getPriceType()));

            final UserInfo invalid = CacheDataUtils.findUserById(info.getInvalidId());
            info.setInvalidName(null != invalid ? invalid.getName() : null);

            final UserInfo financial = CacheDataUtils.findUserById(info.getFinancial());
            info.setFinancialName(null != financial ? financial.getName() : null);
            info.setFinancialMip(null != financial ? financial.getUsername() : null);

            final UserInfo manager = CacheDataUtils.findUserById(info.getManagerId());
            info.setManagerMip(null != manager ? manager.getUsername() : null);
            info.setManagerName(null != manager ? manager.getName() : null);

            final Unit unit = CacheDataUtils.findUnitById(info.getUnitId());
            info.setUnitName(null != unit ? unit.getUnitName() : null);

            final BudgetDepDto budgetDepDto = CacheDataUtils.findBudgetDepById(info.getBudgetDeptId());
            info.setBudgetDepName(null != budgetDepDto ? budgetDepDto.getBudgetDepName() : null);
            info.setBudgetDepCode(null != budgetDepDto ? budgetDepDto.getBudgetDepCode() : null);
            final OperatingUnit ou = CacheDataUtils.findOuById(info.getOuId());
            if (ou != null) {
                final String[] ouCodeArray = ou.getOperatingUnitName().split(Symbol.UNDER_LINE);
                info.setOuCode(ouCodeArray != null && ouCodeArray.length > 1 ? ouCodeArray[1] : null);
                info.setOuName(ou.getOperatingUnitName());
            }
//            final ProjectProfitExample example = new ProjectProfitExample();
//            example.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(info.getId());
//            final List<ProjectProfit> profits = projectProfitService.selectByExample(example);
            List<ProjectProfit> profits = projectProfitMap.get(info.getId());
            if (ListUtils.isNotEmpty(profits)) {
                info.setCostMethodMainName(CostMethod.getValue(profits.get(0).getCostMethodMain()));
            }

            if (StringUtils.isNotEmpty(info.getApplicationIndustry())) {
//                ApplicationIndustry applicationIndustry =
//                        applicationIndustryMapper.selectByPrimaryKey(Long.valueOf(info.getApplicationIndustry()));
                ApplicationIndustry applicationIndustry = applicationIndustryMap.get(Long.valueOf(info.getApplicationIndustry()));
                String applicationIndustryName =
                        applicationIndustry == null || StringUtils.isEmpty(applicationIndustry.getName()) ? "" :
                                applicationIndustry.getName();
                info.setApplicationIndustryName(applicationIndustryName);
            }
        }
        return projectDtoList;
    }

    @Override
    public ProjectDto findSnapShotDetail(final Long projectId, final Long headerId, Integer historyType) {
        final ProjectDto info = new ProjectDto();
        ProjectChangeHistoryExample example = new ProjectChangeHistoryExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andHistoryTypeEqualTo(historyType)
                .andHeaderIdEqualTo(headerId).andProjectIdEqualTo(projectId);
        List<ProjectChangeHistory> changeHistoryList = projectChangeHistoryMapper.selectByExample(example);
        Assert.notNull(changeHistoryList, "项目不存在");
        ProjectChangeHistory changeHistory = changeHistoryList.get(0);
        BeanUtils.copyProperties(changeHistory, info);
        info.setId(projectId);
        setDetailData(info);
        return info;
    }

    @Override
    public void setDetailData(ProjectDto info) {
        final ProjectType projectType = projectTypeService.selectByPrimaryKey(info.getType());
        info.setProjectType(BeanConverter.copy(projectType, ProjectTypeDto.class));
        info.setTypeName(null != projectType ? projectType.getName() : null);
        info.setPriceTypeName(PriceTypeEnum.getValue(info.getPriceType()));

        final UserInfo invalid = CacheDataUtils.findUserById(info.getInvalidId());
        info.setInvalidName(null != invalid ? invalid.getName() : null);

        final UserInfo financial = CacheDataUtils.findUserById(info.getFinancial());
        info.setFinancialName(null != financial ? financial.getName() : null);
        info.setFinancialMip(null != financial ? financial.getUsername() : null);

        final UserInfo manager = CacheDataUtils.findUserById(info.getManagerId());
        info.setManagerMip(null != manager ? manager.getUsername() : null);
        info.setManagerName(null != manager ? manager.getName() : null);

        final Unit unit = CacheDataUtils.findUnitById(info.getUnitId());
        info.setUnitName(null != unit ? unit.getUnitName() : null);

        final BudgetDepDto budgetDepDto = CacheDataUtils.findBudgetDepById(info.getBudgetDeptId());
        info.setBudgetDepName(null != budgetDepDto ? budgetDepDto.getBudgetDepName() : null);
        info.setBudgetDepCode(null != budgetDepDto ? budgetDepDto.getBudgetDepCode() : null);
        final OperatingUnit ou = CacheDataUtils.findOuById(info.getOuId());
        if (ou != null) {
            final String[] ouCodeArray = ou.getOperatingUnitName().split(Symbol.UNDER_LINE);
            info.setOuCode(ouCodeArray != null && ouCodeArray.length > 1 ? ouCodeArray[1] : null);
            info.setOuName(ou.getOperatingUnitName());
        }
        final ProjectProfitExample example = new ProjectProfitExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(info.getId());
        final List<ProjectProfit> profits = projectProfitService.selectByExample(example);
        if (ListUtils.isNotEmpty(profits)) {
            info.setCostMethodMainName(CostMethod.getValue(profits.get(0).getCostMethodMain()));
        }

        if (StringUtils.isNotEmpty(info.getApplicationIndustry())) {
            ApplicationIndustry applicationIndustry =
                    applicationIndustryMapper.selectByPrimaryKey(Long.valueOf(info.getApplicationIndustry()));
            String applicationIndustryName =
                    applicationIndustry == null || StringUtils.isEmpty(applicationIndustry.getName()) ? "" :
                            applicationIndustry.getName();
            info.setApplicationIndustryName(applicationIndustryName);
        }
    }

    @Override
    public List<ProjectMember> getMembersByProjectId(Long id, Long userId) {
        ProjectMemberExample example = new ProjectMemberExample();
        ProjectMemberExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(id).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        if (userId != null) {
            criteria.andUserIdEqualTo(userId);
        }
        List<ProjectMember> list = projectMemberMapper.selectByExample(example);
        UserInfo user = null;
        for (ProjectMember member : list) {
            if (member.getUserId() != null) {
                user = CacheDataUtils.findUserById(member.getUserId());
                member.setUserName(user.getName());
            }
        }
        return list;
    }

    @Override
    public List<ProjectMember> getMembersForProject(Long id) {
        //先判断当前用户是否是项目经理
        Long userId = SystemContext.getUserId();
        if (userId == null) {
            return null;
        }
        Project project = selectByPrimaryKey(id);
        //当前登录用户为项目经理，查询所有
        return getMembersByProjectId(id, userId.equals(project.getManagerId()) ? null : userId);
    }

    /**
     * 增加字段
     * 获取产品名
     */
    private List<ProjectChangeHistoryDto> toBaseChangeHistoryDto(List<ProjectChangeHistory> voList) {
        List<ProjectChangeHistoryDto> dtoList = BeanConverter.copy(voList, ProjectChangeHistoryDto.class);
        if (PublicUtil.isNotEmpty(dtoList)) {
            // 查询应用行业名称
            Map<Long, String> applicationIndustryMap = new HashMap<>();
            List<Long> applicationIndustryIdList =
                    dtoList.stream().map(ProjectChangeHistoryDto::getApplicationIndustry).filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(applicationIndustryIdList)) {
                ApplicationIndustryExample industryExample = new ApplicationIndustryExample();
                industryExample.createCriteria().andIdIn(applicationIndustryIdList);
                applicationIndustryMap =
                        applicationIndustryMapper.selectByExample(industryExample).stream().collect(Collectors.toMap(ApplicationIndustry::getId,
                                ApplicationIndustry::getName));
            }
            for (ProjectChangeHistoryDto dto : dtoList) {
                try {
                    if (StringUtils.isNotBlank(dto.getApplicationIndustry())) {
                        dto.setApplicationIndustryName(applicationIndustryMap.get(Long.valueOf(dto.getApplicationIndustry())));
                    }
                    if (dto.getProductId() != null) {
                        String url =
                                ModelsEnum.CRM.getBaseUrl() + "/product/getProductBaseInfoById?id=" + dto.getProductId();
                        String res = restTemplate.getForEntity(url, String.class).getBody();
                        DataResponse<ProductDto> productResponse = JSON.parseObject(res,
                                new TypeReference<DataResponse<ProductDto>>() {
                                });
                        if (productResponse != null && productResponse.getData() != null) {
                            dto.setProductName(productResponse.getData().getName());
                        } else {
                            final Map<String, Object> param = new HashMap<>();
                            param.put("id", dto.getProductId());
                            String url1 =
                                    com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(),
                                            "/projectProductMaintenance/detail", param);
                            String res1 = restTemplate.getForEntity(url1, String.class).getBody();
                            DataResponse<ProjectProductMaintenance> maintenanceResponse = JSON.parseObject(res1,
                                    new TypeReference<DataResponse<ProjectProductMaintenance>>() {
                                    });
                            if (maintenanceResponse != null && maintenanceResponse.getData() != null) {
                                dto.setProductName(maintenanceResponse.getData().getProjectProductName());
                            }
                        }
                    }
                } catch (Exception e) {
                    //do nothing
                }
            }
        }
        return dtoList;
    }

    @Override
    public ProjectPackageChangeHistoryVO getBaseChangeHistory(Long headId) {
        ProjectHistoryHeader header = projectHistoryHeaderMapper.selectByPrimaryKey(headId);
        Assert.notNull(header, AssertErrorMessage.HEADER_NOT_EXIST);
        ProjectChangeHistoryExample example = new ProjectChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headId);
        ProjectPackageChangeHistoryVO returnVO =
                ProjectChangeHistoryVO.projectChangeHistoryToVO(toBaseChangeHistoryDto(projectChangeHistoryMapper.selectByExample(example)));
        returnVO.setHeader(BeanConverter.copy(header, ProjectHistoryHeaderDto.class));
        //获取附件信息
        returnVO.setAttachmentDtos(this.findCtcAttachmentDto(headId, CtcAttachmentModule.PROJECT_BASE_INFO_CHANGE.code()));

        Project project = selectByPrimaryKey(header.getProjectId());
        Assert.notNull(project, "项目不存在");
        ProjectType projectType = projectTypeService.selectByPrimaryKey(project.getType());
        Guard.notNull(projectType, String.format("项目类型ID：%s对应的项目类型不存在", project.getType()));
        returnVO.setProjectTypeDto(BeanConverter.copy(projectType, ProjectTypeDto.class));

        return returnVO;
    }

    @Override
    public ProjectPackageChangeHistoryVO getContractRsChangeHistory(Long headId) {
        ProjectHistoryHeader header = projectHistoryHeaderMapper.selectByPrimaryKey(headId);
        Assert.notNull(header, AssertErrorMessage.HEADER_NOT_EXIST);
        ProjectContractRsChangeHistoryExample example = new ProjectContractRsChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        ProjectPackageChangeHistoryVO returnVO =
                ProjectChangeHistoryVO.projectContractRsChangeHistoryToVO(projectContractRsChangeHistoryMapper.selectByExample(example));
        returnVO.setHeader(BeanConverter.copy(header, ProjectHistoryHeaderDto.class));

        return returnVO;
    }

    @Override
    public ProjectPackageChangeHistoryVO getBusinessRsChangeHistory(Long headId) {
        ProjectHistoryHeader header = projectHistoryHeaderMapper.selectByPrimaryKey(headId);
        Assert.notNull(header, AssertErrorMessage.HEADER_NOT_EXIST);
        ProjectBusinessRsChangeHistoryExample example = new ProjectBusinessRsChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        ProjectPackageChangeHistoryVO returnVO =
                ProjectChangeHistoryVO.projectBusinessRsChangeHistoryToVO(projectBusinessRsChangeHistoryMapper.selectByExample(example));
        returnVO.setHeader(BeanConverter.copy(header, ProjectHistoryHeaderDto.class));

        return returnVO;
    }

    @Override
    public ProjectPackageChangeHistoryVO getAssetRsChangeHistory(Long headId) {
        ProjectHistoryHeader header = projectHistoryHeaderMapper.selectByPrimaryKey(headId);
        Assert.notNull(header, AssertErrorMessage.HEADER_NOT_EXIST);
        ProjectAssetRsChangeHistoryExample example = new ProjectAssetRsChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        ProjectPackageChangeHistoryVO returnVO =
                ProjectChangeHistoryVO.projectAssetRsChangeHistoryToVO(projectAssetRsChangeHistoryMapper.selectByExample(example));
        returnVO.setHeader(BeanConverter.copy(header, ProjectHistoryHeaderDto.class));

        Project project = projectMapper.selectByPrimaryKey(header.getProjectId());
        returnVO.setProjectDto(BeanConverter.copy(project, ProjectDto.class));

        return returnVO;
    }

    @Override
    public ProjectPackageChangeHistoryVO getBudgetTargetChangeHistory(Long headId) {
        ProjectHistoryHeader header = projectHistoryHeaderMapper.selectByPrimaryKey(headId);
        Assert.notNull(header, AssertErrorMessage.HEADER_NOT_EXIST);
        ProjectBudgetTargetChangeHistoryExample example = new ProjectBudgetTargetChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headId);
        ProjectPackageChangeHistoryVO returnVO =
                ProjectChangeHistoryVO.projectBudgetTargetChangeHistoryToVO(projectBudgetTargetChangeHistoryMapper.selectByExample(example));
        returnVO.setHeader(BeanConverter.copy(header, ProjectHistoryHeaderDto.class));
        BeanConverter.copy(projectMapper.selectByPrimaryKey(header.getProjectId()), returnVO);
        //获取附件信息
        returnVO.setAttachmentDtos(this.findCtcAttachmentDto(headId,
                CtcAttachmentModule.PROJECT_TARGET_COST_CHANGE.code()));

        return returnVO;
    }

    @Override
    public List<ProjectPackageChangeHistoryVO> getBaseBatchChangeHistory(Long batch) {
        List<ProjectPackageChangeHistoryVO> projectPackageChangeHistoryVOS = new ArrayList<>();
        ProjectHistoryBatchHeaderExample projectHistoryBatchHeaderExample = new ProjectHistoryBatchHeaderExample();
        projectHistoryBatchHeaderExample.createCriteria().andBatchEqualTo(batch).andDeletedFlagEqualTo(false);
        List<ProjectHistoryBatchHeader> projectHistoryBatchHeaders =
                projectHistoryBatchHeaderMapper.selectByExample(projectHistoryBatchHeaderExample);
        projectHistoryBatchHeaders.forEach(p -> {
            ProjectHistoryHeader header = projectHistoryHeaderMapper.selectByPrimaryKey(p.getHeaderId());
            Assert.notNull(header, AssertErrorMessage.HEADER_NOT_EXIST);
            ProjectChangeHistoryExample example = new ProjectChangeHistoryExample();
            example.createCriteria().andHeaderIdEqualTo(p.getHeaderId());
            ProjectPackageChangeHistoryVO returnVO =
                    ProjectChangeHistoryVO.projectChangeHistoryToVO(toBaseChangeHistoryDto(projectChangeHistoryMapper.selectByExample(example)));
            if (header.getCreateBy() != null) {
                returnVO.setProjectHistoryHeaderCreateName(CacheDataUtils.findUserById(header.getCreateBy()).getName());
            }
            String projectType = getProjectType(header.getProjectId());
            returnVO.setProjectType(projectType);
            returnVO.setHeader(BeanConverter.copy(header, ProjectHistoryHeaderDto.class));
            Object managerId = null;
            Object financialId = null;
            try {
                managerId =
                        ProjectChangeHistory.class.getMethod("getManagerId").invoke(returnVO.getBaseInfo().getHistory());
                financialId =
                        ProjectChangeHistory.class.getMethod("getFinancial").invoke(returnVO.getBaseInfo().getHistory());
            } catch (NoSuchMethodException e) {
                logger.info("获取项目经理Id和项目财务Id失败！", e);
            } catch (IllegalAccessException e) {
                logger.info("获取项目经理Id和项目财务Id失败！", e);
            } catch (InvocationTargetException e) {
                logger.info("获取项目经理Id和项目财务Id失败！", e);
            }
            if (managerId != null) {
                returnVO.setManagerMip(CacheDataUtils.findUserById((Long) managerId).getUsername());
            }
            if (financialId != null) {
                returnVO.setFinancialName(CacheDataUtils.findUserById((Long) financialId).getName());
                returnVO.setFinancialMip(CacheDataUtils.findUserById((Long) financialId).getUsername());
            }
            //获取附件信息
            returnVO.setAttachmentDtos(this.findCtcAttachmentDto(p.getHeaderId(),
                    CtcAttachmentModule.PROJECT_BASE_INFO_CHANGE.code()));
            projectPackageChangeHistoryVOS.add(returnVO);
        });
        return projectPackageChangeHistoryVOS;
    }

    private String getProjectType(Long projectId) {
        String projectType = null;
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andIdEqualTo(projectId);
        List<Project> projects = projectService.selectByExample(projectExample);
        if (ListUtils.isNotEmpty(projects)) {
            Long type = projects.get(0).getType();
            ProjectTypeExample projectTypeExample = new ProjectTypeExample();
            projectTypeExample.createCriteria().andIdEqualTo(type);
            List<ProjectType> projectTypes = projectTypeService.selectByExample(projectTypeExample);
            if (ListUtils.isNotEmpty(projectTypes)) {
                projectType = projectTypes.get(0).getName();
            }
        }
        return projectType;
    }

    @Override
    public ResponseMap getProjectBaseInfoChangeApp(Long headId) {
        ResponseMap result = new ResponseMap();
        result.setStatus("fail");
        if (headId == null || headId == 0) {
            result.setMsg("单据ID不能为空");
            return result;
        }
        ProjectPackageChangeHistoryVO projectPackageChangeHistoryVO = this.getBaseChangeHistory(headId);
        if (projectPackageChangeHistoryVO == null) {
            result.setMsg("单据ID=" + headId + "不存在");
            return result;
        }

        ProjectChangeHistoryVO projectChangeHistoryVO = projectPackageChangeHistoryVO.getBaseInfo();
        if (projectChangeHistoryVO == null) {
            result.setMsg("单据ID=" + headId + "变更信息不存在");
            return result;
        }

        ProjectChangeHistoryDto change =
                (ProjectChangeHistoryDto) projectPackageChangeHistoryVO.getBaseInfo().getChange();
        ProjectChangeHistoryDto history =
                (ProjectChangeHistoryDto) projectPackageChangeHistoryVO.getBaseInfo().getHistory();
        ProjectHistoryHeader header = projectPackageChangeHistoryVO.getHeader();
        if (header == null) {
            result.setMsg("单据ID=" + headId + "变更头信息不存在");
            return result;
        }
        if (change == null) {
            result.setMsg("单据ID=" + headId + "变更后信息不存在");
            return result;
        }
        if (history == null) {
            result.setMsg("单据ID=" + headId + "变更前信息不存在");
            return result;
        }
        Map<String, String> headMap = new HashMap<String, String>();
        headMap.put("name", history.getName());
        headMap.put("code", history.getCode());
        headMap.put("reasonType", header.getReasonType());
        headMap.put("reason", header.getReason());

        String[] ignoreFiles = new String[]{"id", "createBy", "createAt", "updateBy", "updateAt",
                "code", "customerId", "customerName", "version", "budgetCost",
                "deletedFlag", "status", "type", "productId", "previewFlag",
                "riskStatus", "priceType", "unitId", "ouId", "financial", "historyType", "headerId", "projectId"};

        Map<String, String> fieldNameMap = new HashMap<>();
        fieldNameMap.put("name", "项目名称");
        fieldNameMap.put("managerName", "项目经理");
        fieldNameMap.put("productName", "产品");
        fieldNameMap.put("financialName", "项目财务");
        fieldNameMap.put("summary", "项目概述");
        fieldNameMap.put("startDate", "项目开始时间");
        fieldNameMap.put("endDate", "项目结束时间");

        fieldNameMap.put("unitName", "业务分类");
        fieldNameMap.put("salesManagerName", "销售经理");
        fieldNameMap.put("planDesignerName", "方案设计员");
        fieldNameMap.put("technologyLeaderName", "技术负责人");


        Map<String, List<Object>> compareResult = PublicUtil.compareFields(history, change, ignoreFiles);
        Set<String> keySet = compareResult.keySet();
        List<Map<String, String>> list1 = new ArrayList<Map<String, String>>();
        for (String key : keySet) {
            Map<String, String> detailMap = new HashMap<>();
            List<Object> list = compareResult.get(key);
            String fieldName = fieldNameMap.get(key);
            detailMap.put("fieldName", fieldName);
            switch (key) {
                case "startDate":
                    String changeBefore = list.get(0) == null ? null : DateUtils.format((Date) list.get(0), "yyyy-MM" +
                            "-dd");
                    String changeAfter = list.get(1) == null ? null : DateUtils.format((Date) list.get(1), "yyyy-MM" +
                            "-dd");
                    if (!Objects.equals(changeBefore, changeAfter)) {
                        detailMap.put("changeBefore", changeBefore);
                        detailMap.put("changeAfter", changeAfter);
                        list1.add(detailMap);
                    }
                    break;
                case "endDate":
                    String changeBefore1 = list.get(0) == null ? null : DateUtils.format((Date) list.get(0), "yyyy-MM" +
                            "-dd");
                    String changeAfter1 = list.get(1) == null ? null : DateUtils.format((Date) list.get(1), "yyyy-MM" +
                            "-dd");
                    if (!Objects.equals(changeBefore1, changeAfter1)) {
                        detailMap.put("changeBefore", changeBefore1);
                        detailMap.put("changeAfter", changeAfter1);
                        list1.add(detailMap);
                    }
                    break;
                case "unitId":
                    String changeBefore3 = list.get(0) == null ? null :
                            basedataExtService.selectByPrimaryKey(Long.valueOf(String.valueOf(list.get(0))).longValue()).getUnitName();
                    String changeAfter3 = list.get(1) == null ? null :
                            basedataExtService.selectByPrimaryKey(Long.valueOf(String.valueOf(list.get(1))).longValue()).getUnitName();
                    if (!Objects.equals(changeBefore3, changeAfter3)) {
                        detailMap.put("changeBefore", changeBefore3);
                        detailMap.put("changeAfter", changeAfter3);
                        list1.add(detailMap);
                    }
                    break;
                default:
                    String changeBefore2 = (list.get(0) == null ? null : list.get(0) + "");
                    String changeAfter2 = (list.get(1) == null ? null : list.get(1) + "");
                    if (!Objects.equals(changeBefore2, changeAfter2)) {
                        detailMap.put("changeBefore", changeBefore2);
                        detailMap.put("changeAfter", changeAfter2);
                        list1.add(detailMap);
                    }
            }
        }

        List<Map<String, String>> list2 = new ArrayList<Map<String, String>>();
        for (Map<String, String> map : list1) {
            if (null != map.get("fieldName")) {
                list2.add(map);
            }
        }


        List<CtcAttachmentDto> attachmentDtos = projectPackageChangeHistoryVO.getAttachmentDtos();
        if (attachmentDtos != null) {
            List<AduitAtta> fileList = new ArrayList<>();
            for (CtcAttachmentDto ctcAttachmentDto : attachmentDtos) {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(ctcAttachmentDto.getAttachId() + "");
                aduitAtta.setFileName(ctcAttachmentDto.getAttachName());
                aduitAtta.setFileSize(ctcAttachmentDto.getFileSize() + "");
                fileList.add(aduitAtta);
            }
            result.setFileList(fileList);
        }
        result.setHeadMap(headMap);
        result.setList1(list2);
        result.setStatus("success");
        result.setMsg("请求成功");
        return result;
    }


    /**
     * 构建附件信息
     */
    private List<CtcAttachmentDto> findCtcAttachmentDto(Long moduleId, Integer module) {
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(module);
        attachmentQuery.setModuleId(moduleId);
        List<CtcAttachmentDto> ctcAttachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
        if (CollectionUtils.isEmpty(ctcAttachmentDtos)) {
            return Collections.emptyList();
        }
        return ctcAttachmentDtos;
    }

    @Override
    public ProjectPackageChangeHistoryVO getMilepostChangeHistory(Long headId) {
        ProjectHistoryHeaderDto header = BeanConverter.copy(projectHistoryHeaderMapper.selectByPrimaryKey(headId), ProjectHistoryHeaderDto.class);
        boolean milestoneBaseDate = false;
        if (header != null) {
            Long projectId = header.getProjectId();
            if (projectId != null) {
                Project project = projectMapper.selectByPrimaryKey(projectId);
                if (project != null) {
                    header.setCode(project.getCode());
                    header.setName(project.getName());
                    ProjectType projectType = projectTypeMapper.selectByPrimaryKey(project.getType());
                    milestoneBaseDate = Objects.nonNull(projectType) ? projectType.getMilestoneBaseDate() : false;
                }
            }
        }
        Assert.notNull(header, AssertErrorMessage.HEADER_NOT_EXIST);

        ProjectPackageChangeHistoryVO returnVO = new ProjectPackageChangeHistoryVO();
        // 是否启用里程碑基准日期
        returnVO.setMilestoneBaseDate(milestoneBaseDate);

        // 主里程碑
        List<ProjectChangeHistoryVO<ProjectMilepostChangeHistoryDto>> mainVoLsit =
                projectMilepostChangeHistoryService.buildParentMilepostChangeTree(headId, false);
        // 辅里程碑
        List<ProjectChangeHistoryVO<ProjectMilepostChangeHistoryDto>> helpVoLsit =
                projectMilepostChangeHistoryService.buildParentMilepostChangeTree(headId, true);
        /* 组装里程碑change树 */
        returnVO.setHelpMileposts(helpVoLsit);
        returnVO.setMainMileposts(mainVoLsit);

        returnVO.setHeader(header);
        if (returnVO.getHeader() != null && returnVO.getHeader().getProjectId() != null) {
            Project project = projectService.selectByPrimaryKey(returnVO.getHeader().getProjectId());
            returnVO.setProjectName(project.getName());
        }
        Integer module = CtcAttachmentModule.PROJECT_MILEPOST_CHANGE.code();//里程碑变更模块类型
        if (header != null && Objects.equals(header.getChangeType(), CtcProjectChangeType.PROFIT.getCode())) {
            module = CtcAttachmentModule.PROJECT_INCOME_COST_PLAN_CHANGE.code();//收入成本计划变更模块类型
            final ProjectProfitChangeHistoryExample condition = new ProjectProfitChangeHistoryExample();
            condition.createCriteria().andHeaderIdEqualTo(headId);
            final List<ProjectProfitChangeHistory> projectProfits = projectProfitChangeHistoryService.selectByExample(condition);
            projectProfits.forEach(profit -> {
                final ProjectProfitChangeHistoryDto dto = new ProjectProfitChangeHistoryDto();
                BeanUtils.copyProperties(profit, dto);
                dto.setCostMethodHelpName(CostMethod.getValue(dto.getCostMethodHelp()));
                dto.setIncomePointHelpName(IncomePoint.getValue(dto.getIncomePointHelp()));
                dto.setCostMethodMainName(CostMethod.getValue(dto.getCostMethodMain()));
                dto.setIncomePointMainName(IncomePoint.getValue(dto.getIncomePointMain()));
                if (Objects.equals(dto.getHistoryType(), HistoryType.CHANGE.getCode())) {
                    returnVO.setChangeProfit(dto);
                } else {
                    returnVO.setHistoryProfit(dto);
                }
            });
            returnVO.setReason(header.getReason());

            final ProjectDto dto = findDetail(header.getProjectId());
            if (null != dto) {
                returnVO.setTypeName(dto.getTypeName());
                returnVO.setAmount(dto.getAmount());
                returnVO.setOuName(dto.getOuName());
                returnVO.setBudgetDepName(dto.getBudgetDepName());
                returnVO.setBudgetCost(dto.getBudgetCost());
            }
            final ProjectProfitDto profitDto = projectProfitService.findProfitDetail(header.getProjectId());
            if (null != profitDto) {
                returnVO.setStorageCode(profitDto.getStorageCode());
                returnVO.setStorageName(profitDto.getStorageName());
                returnVO.setCurrency(profitDto.getCurrency());
            }
        }
        //获取附件
        returnVO.setAttachmentDtos(this.findCtcAttachmentDto(headId, module));

        return returnVO;
    }

    @Override
    public ResponseMap getProjectMilepostChangeApp(Long id) {
        ResponseMap result = new ResponseMap();
        result.setStatus("fail");
        if (id == null || id == 0) {
            result.setMsg("单据ID不能为空");
            return result;
        }
        ProjectPackageChangeHistoryVO projectPackageChangeHistoryVO = this.getMilepostChangeHistory(id);
        if (projectPackageChangeHistoryVO == null) {
            result.setMsg("单据ID=" + id + "不存在");
            return result;
        }
        List<ProjectChangeHistoryVO<ProjectMilepostChangeHistoryDto>> helpMileposts = projectPackageChangeHistoryVO.getHelpMileposts();//辅里程碑
        List<ProjectChangeHistoryVO<ProjectMilepostChangeHistoryDto>> mainMileposts = projectPackageChangeHistoryVO.getMainMileposts();//主里程碑
        ProjectHistoryHeader header = projectPackageChangeHistoryVO.getHeader();
        if (header == null) {
            result.setMsg("单据ID=" + id + "变更头信息不存在");
            return result;
        }
        Map<String, String> headMap = new HashMap<String, String>();
        Long projectId = header.getProjectId();
        if (projectId != null) {
            Project project = projectMapper.selectByPrimaryKey(projectId);
            if (project != null) {
                headMap.put("name", project.getName());
                headMap.put("code", project.getCode());
            }
        }
        headMap.put("reason", header.getReason());
        headMap.put("reasonType", header.getReasonType());
        List<ProjectMilepostChangeHistory> add = new ArrayList<>();
        List<ProjectMilepostChangeHistory> del = new ArrayList<>();
        List<Map<String, ProjectMilepostChangeHistory>> update = new ArrayList<>();

        if (mainMileposts != null) {
            for (ProjectChangeHistoryVO vo : mainMileposts) {
                //变更类型 1新增/2删除/3修改/4未更改
                Integer type = vo.getType();
                ProjectMilepostChangeHistory history = (ProjectMilepostChangeHistory) vo.getHistory();
                ProjectMilepostChangeHistory change = (ProjectMilepostChangeHistory) vo.getChange();
                if (type != null && type == 1) {
                    add.add(change);
                } else if (type != null && type == 2) {
                    del.add(history);
                } else if (type != null && type == 3) {
                    Map<String, ProjectMilepostChangeHistory> up = new HashMap<>();
                    up.put("history", history);
                    up.put("change", change);
                    update.add(up);
                }
            }
        }
        if (helpMileposts != null) {
            for (ProjectChangeHistoryVO vo : helpMileposts) {
                //变更类型 1新增/2删除/3修改/4未更改
                Integer type = vo.getType();
                ProjectMilepostChangeHistory history = (ProjectMilepostChangeHistory) vo.getHistory();
                ProjectMilepostChangeHistory change = (ProjectMilepostChangeHistory) vo.getChange();
                if (type != null && type == 1) {
                    add.add(change);
                } else if (type != null && type == 2) {
                    del.add(history);
                } else if (type != null && type == 3) {
                    Map<String, ProjectMilepostChangeHistory> up = new HashMap<>();
                    up.put("history", history);
                    up.put("change", change);
                    update.add(up);
                }
            }
        }

        List<CtcAttachmentDto> attachmentDtos = projectPackageChangeHistoryVO.getAttachmentDtos();
        if (attachmentDtos != null) {
            List<AduitAtta> fileList = new ArrayList<>();
            for (CtcAttachmentDto ctcAttachmentDto : attachmentDtos) {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(ctcAttachmentDto.getAttachId() + "");
                aduitAtta.setFileName(ctcAttachmentDto.getAttachName());
                aduitAtta.setFileSize(ctcAttachmentDto.getFileSize() + "");
                fileList.add(aduitAtta);
            }
            result.setFileList(fileList);
        }
        result.setHeadMap(headMap);

        List<Map<String, String>> list1 = new ArrayList<>();
        List<Map<String, String>> list2 = new ArrayList<>();
        List<Map<String, String>> list3 = new ArrayList<>();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        if (update != null && update.size() != 0) {
            for (Map<String, ProjectMilepostChangeHistory> up : update) {
                if (up == null) {
                    continue;
                }
                Map<String, String> map = new HashMap<>();
                ProjectMilepostChangeHistory history = up.get("history");
                ProjectMilepostChangeHistory change = up.get("change");
                if (change.getHelpFlag() != null && change.getHelpFlag()) {
                    map.put("helpFlag", "辅里程碑");
                } else {
                    map.put("helpFlag", "主里程碑");
                }
                map.put("orderNum", change.getOrderNum() + "");
                map.put("name", change.getName());


                String startTimeBefore = "";
                String startTimeChange = "";
                if (history.getStartTime() != null) {
                    startTimeBefore = df.format(history.getStartTime());
                }
                if (change.getStartTime() != null) {
                    startTimeChange = df.format(change.getStartTime());
                }
                //if(!startTimeBefore.equals(startTimeChange)){
                if (StringUtils.isNotEmpty(startTimeBefore)) {
                    map.put("startTimeBefore", startTimeBefore);
                }
                if (StringUtils.isNotEmpty(startTimeChange)) {
                    map.put("startTimeChange", startTimeChange);
                }
                //}


                String endTimeBefore = "";
                String endTimeChange = "";
                if (history.getEndTime() != null) {
                    endTimeBefore = df.format(history.getEndTime());
                }
                if (change.getEndTime() != null) {
                    endTimeChange = df.format(change.getEndTime());
                }

                //if(!endTimeBefore.equals(endTimeChange)){
                if (StringUtils.isNotEmpty(endTimeBefore)) {
                    map.put("endTimeBefore", endTimeBefore);
                }
                if (StringUtils.isNotEmpty(endTimeChange)) {
                    map.put("endTimeChange", endTimeChange);
                }
                //}


                String responsibleNameBefore = "";
                String responsibleNamechange = "";
                if (history.getResponsible() != null) {
                    final UserInfo user = CacheDataUtils.findUserById(history.getResponsible());
                    responsibleNameBefore = (user != null ? user.getName() : null);
                }
                if (change.getResponsible() != null) {
                    final UserInfo user = CacheDataUtils.findUserById(change.getResponsible());
                    responsibleNamechange = (user != null ? user.getName() : null);
                }

                //if(!responsibleNameBefore.equals(responsibleNamechange)){
                if (StringUtils.isNotEmpty(responsibleNameBefore)) {
                    map.put("responsibleNameBefore", responsibleNameBefore);
                }
                if (StringUtils.isNotEmpty(responsibleNamechange)) {
                    map.put("responsibleNamechange", responsibleNamechange);
                }
                //}


                String noticeNameBefore = "";
                String noticeNameChange = "";
                if (history.getNotice() != null) {
                    ProjectRole projectRole =
                            projectRoleService.selectByPrimaryKey(Long.parseLong(history.getNotice()));
                    noticeNameBefore = (projectRole != null ? projectRole.getName() : "");
                }

                if (change.getNotice() != null) {
                    ProjectRole projectRole = projectRoleService.selectByPrimaryKey(Long.parseLong(change.getNotice()));
                    noticeNameChange = (projectRole != null ? projectRole.getName() : "");
                }
                //if(!noticeNameBefore.equals(noticeNameChange)){
                if (StringUtils.isNotEmpty(noticeNameBefore)) {
                    map.put("noticeNameBefore", noticeNameBefore);
                }
                if (StringUtils.isNotEmpty(noticeNameChange)) {
                    map.put("noticeNameChange", noticeNameChange);
                }
                //}
                list1.add(map);
            }
        }

        if (add != null && add.size() != 0) {
            for (ProjectMilepostChangeHistory change : add) {
                if (change == null) {
                    continue;
                }
                Map<String, String> map = new HashMap<>();
                if (change.getHelpFlag() != null && change.getHelpFlag()) {
                    map.put("helpFlag", "辅里程碑");
                } else {
                    map.put("helpFlag", "主里程碑");
                }
                map.put("orderNum", change.getOrderNum() + "");
                map.put("name", change.getName());
                if (change.getStartTime() != null) {
                    map.put("startTime", df.format(change.getStartTime()));
                } else {
                    map.put("startTime", "");
                }
                if (change.getEndTime() != null) {
                    map.put("endTime", df.format(change.getEndTime()));
                } else {
                    map.put("endTime", "");
                }

                if (change.getResponsible() != null) {
                    final UserInfo user = CacheDataUtils.findUserById(change.getResponsible());
                    map.put("responsibleName", user != null ? user.getName() : "");
                }


                if (change.getNotice() != null) {
                    ProjectRole projectRole = projectRoleService.selectByPrimaryKey(Long.parseLong(change.getNotice()));
                    String noticeNameChange = (projectRole != null ? projectRole.getName() : null);
                    map.put("noticeName", noticeNameChange);
                }

                if (StringUtils.isNotEmpty(change.getAnnexType())) {
                    if (change.getAnnexType().equals(ProjectMilepostAnnexType.BOM.code())) {
                        map.put("annexType", ProjectMilepostAnnexType.BOM.msg());
                    } else {
                        map.put("annexType", ProjectMilepostAnnexType.ATTACHMENT.msg());
                    }
                }
                map.put("deliverablesName", change.getAnnex());
                list2.add(map);
            }
        }

        if (del != null && del.size() != 0) {
            for (ProjectMilepostChangeHistory de : del) {
                if (de == null) {
                    continue;
                }
                Map<String, String> map = new HashMap<>();
                if (de.getHelpFlag() != null && de.getHelpFlag()) {
                    map.put("helpFlag", "辅里程碑");
                } else {
                    map.put("helpFlag", "主里程碑");
                }
                map.put("orderNum", de.getOrderNum() + "");
                map.put("name", de.getName());
                if (de.getStartTime() != null) {
                    map.put("startTime", df.format(de.getStartTime()));
                } else {
                    map.put("startTime", "");
                }
                if (de.getStartTime() != null) {
                    map.put("endTime", df.format(de.getStartTime()));
                } else {
                    map.put("endTime", "");
                }

                if (de.getResponsible() != null) {
                    final UserInfo user = CacheDataUtils.findUserById(de.getResponsible());
                    map.put("responsibleName", user != null ? user.getName() : "");
                }

                //map.put("noticeName",de.getNotice());
                if (de.getNotice() != null) {
                    ProjectRole projectRole = projectRoleService.selectByPrimaryKey(Long.parseLong(de.getNotice()));
                    String noticeNameChange = (projectRole != null ? projectRole.getName() : null);
                    map.put("noticeName", noticeNameChange);
                }

                if (StringUtils.isNotEmpty(de.getAnnexType())) {
                    if (de.getAnnexType().equals(ProjectMilepostAnnexType.BOM.code())) {
                        map.put("annexType", ProjectMilepostAnnexType.BOM.msg());
                    } else {
                        map.put("annexType", ProjectMilepostAnnexType.ATTACHMENT.msg());
                    }
                }
                map.put("deliverablesName", de.getAnnex());
                list3.add(map);
            }
        }

        result.setList1(list1);
        result.setList2(list2);
        result.setList3(list3);
        result.setStatus("success");
        result.setMsg("请求成功");
        return result;
    }

    @Override
    public PageInfo<ProjectHistoryHeaderDto> getChangeHistoryList(Long projectId, String changeType,
                                                                  Date beginTime, Date endTime, Integer status,
                                                                  Integer pageNum, Integer pageSize) {
        List<ProjectHistoryHeader> projectHistoryHeaders;
        CtcProjectChangeType ctcProjectChangeType = CtcProjectChangeType.searchByName(changeType);
        if ((status != null && status == 0) || (StringUtils.isNotEmpty(changeType) && ctcProjectChangeType == null)) {
            projectHistoryHeaders = Collections.emptyList();
        } else {
            if (ctcProjectChangeType == CtcProjectChangeType.PROJECTSNAPSHOT ||
                    ctcProjectChangeType == CtcProjectChangeType.PROJECTPRESNAPSHOT) {
                projectHistoryHeaders = Collections.emptyList();
            } else {
                ProjectHistoryHeaderExample example = new ProjectHistoryHeaderExample();
                example.setOrderByClause("create_at desc");
                ProjectHistoryHeaderExample.Criteria criteria = example.createCriteria()
                        .andProjectIdEqualTo(projectId)
                        .andDeletedFlagEqualTo(Boolean.FALSE);
                if (ctcProjectChangeType != null) {
                    criteria.andChangeTypeEqualTo(ctcProjectChangeType.getCode());
                } else {
                    criteria.andChangeTypeNotIn(Arrays.asList(CtcProjectChangeType.PROJECTSNAPSHOT.getCode(),
                            CtcProjectChangeType.PROJECTPRESNAPSHOT.getCode()));
                }
                if (beginTime != null) {
                    criteria.andCreateAtGreaterThanOrEqualTo(beginTime);
                }
                if (endTime != null) {
                    criteria.andCreateAtLessThanOrEqualTo(endTime);
                }
                if (status != null) {
                    criteria.andStatusEqualTo(status);
                } else {
                    //现在做的逻辑是要把status为0(草稿)的给过滤掉
                    criteria.andStatusNotEqualTo(0);
                }
                PageHelper.startPage(pageNum, pageSize);
                projectHistoryHeaders = projectHistoryHeaderMapper.selectByExample(example);
            }
        }
        PageInfo<ProjectHistoryHeaderDto> projectHistoryHeaderDtoPageInfo =
                BeanConverter.convertPage(projectHistoryHeaders, ProjectHistoryHeaderDto.class);

        //查询预算变更的ems推送状态
        this.getEmsPushStatus(projectHistoryHeaderDtoPageInfo.getList());
        getBatch(projectHistoryHeaderDtoPageInfo.getList());
        return projectHistoryHeaderDtoPageInfo;
    }

    @Override
    public void getEmsPushStatus(List<ProjectHistoryHeaderDto> projectHistoryHeaderDtos) {
        if (ListUtils.isNotEmpty(projectHistoryHeaderDtos)) {
            for (ProjectHistoryHeaderDto projectHistoryHeaderDto : projectHistoryHeaderDtos) {
                if (Objects.equals(projectHistoryHeaderDto.getChangeType(), CtcProjectChangeType.BUDGET.code())
                        || Objects.equals(projectHistoryHeaderDto.getChangeType(),
                        CtcProjectChangeType.PREPROJECT.code())
                        || Objects.equals(projectHistoryHeaderDto.getChangeType(),
                        CtcProjectChangeType.PROJECTCONTRACTRS.code())
                        || Objects.equals(projectHistoryHeaderDto.getChangeType(),
                        CtcProjectChangeType.WBS_BUDGET.code())) {
                    //查询项目预算变更费用
                    final ProjectBudgetChangePushEmsExample changePushEmsExample =
                            new ProjectBudgetChangePushEmsExample();
                    changePushEmsExample.createCriteria()
                            .andProjectIdEqualTo(projectHistoryHeaderDto.getProjectId())
                            .andDeletedFlagEqualTo(false)
                            .andHeaderIdEqualTo(projectHistoryHeaderDto.getId());
                    final List<ProjectBudgetChangePushEms> changePushEms =
                            projectBudgetChangePushEmsService.selectByExample(changePushEmsExample);
                    if (ListUtils.isNotEmpty(changePushEms)) {
                        for (ProjectBudgetChangePushEms budgetChangePushEms : changePushEms) {
                            if (PushStatus.SUCCESS.getCode().equals(budgetChangePushEms.getPushStatus())) {
                                projectHistoryHeaderDto.setEmsPushStatus(budgetChangePushEms.getPushStatus());
                                projectHistoryHeaderDto.setEmsPushMsg(budgetChangePushEms.getPushMsg());
                                projectHistoryHeaderDto.setEmsPushDate(budgetChangePushEms.getPushDate());
                            }
                            if (PushStatus.ABNORMAL.getCode().equals(budgetChangePushEms.getPushStatus())
                                    || PushStatus.WAIT_FOR.getCode().equals(budgetChangePushEms.getPushStatus())) {
                                projectHistoryHeaderDto.setEmsPushStatus(budgetChangePushEms.getPushStatus());
                                projectHistoryHeaderDto.setEmsPushMsg(budgetChangePushEms.getPushMsg());
                                projectHistoryHeaderDto.setEmsPushDate(budgetChangePushEms.getPushDate());
                                break;
                            }
                        }
                    }

                    //查询项目预算费用
                    ProjectFeeCollectionExample example = new ProjectFeeCollectionExample();
                    final ProjectFeeCollectionExample.Criteria criteria = example.createCriteria();
                    criteria.andDeletedFlagEqualTo(Boolean.FALSE);
                    criteria.andProjectIdEqualTo(projectHistoryHeaderDto.getProjectId())
                            .andHeaderIdEqualTo(projectHistoryHeaderDto.getId());
                    final List<ProjectFeeCollection> projectFeeCollections =
                            projectFeeCollectionService.selectByExample(example);
                    if (ListUtils.isNotEmpty(projectFeeCollections)) {
                        for (ProjectFeeCollection projectFeeCollection : projectFeeCollections) {
                            if (PushStatus.SUCCESS.getCode().equals(projectFeeCollection.getPushStatus())) {
                                projectHistoryHeaderDto.setEmsPushStatus(projectFeeCollection.getPushStatus());
                                projectHistoryHeaderDto.setEmsPushMsg(projectFeeCollection.getPushMsg());
                                projectHistoryHeaderDto.setEmsPushDate(projectFeeCollection.getPushDate());
                            }
                            if (PushStatus.ABNORMAL.getCode().equals(projectFeeCollection.getPushStatus())
                                    || PushStatus.WAIT_FOR.getCode().equals(projectFeeCollection.getPushStatus())) {
                                projectHistoryHeaderDto.setEmsPushStatus(projectFeeCollection.getPushStatus());
                                projectHistoryHeaderDto.setEmsPushMsg(projectFeeCollection.getPushMsg());
                                projectHistoryHeaderDto.setEmsPushDate(projectFeeCollection.getPushDate());
                                break;
                            }
                        }
                    }

                }
            }
        }
    }

    private void getBatch(List<ProjectHistoryHeaderDto> projectHistoryHeaderDtos) {
        projectHistoryHeaderDtos.forEach(p -> {
            ProjectHistoryBatchHeaderExample projectHistoryBatchHeaderExample = new ProjectHistoryBatchHeaderExample();
            projectHistoryBatchHeaderExample.createCriteria()
                    .andHeaderIdEqualTo(p.getId())
                    .andStatusNotEqualTo(0)
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            List<ProjectHistoryBatchHeader> projectHistoryBatchHeaders =
                    projectHistoryBatchHeaderMapper.selectByExample(projectHistoryBatchHeaderExample);
            if (ListUtils.isNotEmpty(projectHistoryBatchHeaders)) {
                p.setBatch(projectHistoryBatchHeaders.get(0).getBatch());
            }
        });
    }


    //关联批量修改项目经理，财务经理关联表；；查询历史关联变更的其他项目信息
    private List<ProjectHistoryHeader> getProjectHistoryHeaders(List<ProjectHistoryHeader> projectHistoryHeaders) {
        Long headerId = projectHistoryHeaders.get(0).getId();
        ProjectHistoryBatchHeaderExample projectHistoryBatchHeaderExample = new ProjectHistoryBatchHeaderExample();
        projectHistoryBatchHeaderExample.createCriteria().andHeaderIdEqualTo(headerId).andStatusNotEqualTo(0).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectHistoryBatchHeader> projectHistoryBatchHeaders =
                projectHistoryBatchHeaderMapper.selectByExample(projectHistoryBatchHeaderExample);
        if (ListUtils.isNotEmpty(projectHistoryBatchHeaders)) {
            ProjectHistoryBatchHeaderExample projectHistoryBatchHeaderBatchExample =
                    new ProjectHistoryBatchHeaderExample();
            projectHistoryBatchHeaderBatchExample.createCriteria().andBatchEqualTo(projectHistoryBatchHeaders.get(0).getBatch()).andStatusNotEqualTo(0).andDeletedFlagEqualTo(Boolean.FALSE);
            List<Long> headerIds =
                    projectHistoryBatchHeaderMapper.selectByExample(projectHistoryBatchHeaderBatchExample).stream().map(ProjectHistoryBatchHeader::getHeaderId).collect(Collectors.toList());
            ProjectHistoryHeaderExample example = new ProjectHistoryHeaderExample();
            example.setOrderByClause("create_at desc");
            example.createCriteria().andIdIn(headerIds).andStatusNotEqualTo(0) //现在做的逻辑是要把status为0(草稿)的给过滤掉
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            projectHistoryHeaders = projectHistoryHeaderMapper.selectByExample(example);

        }
        return projectHistoryHeaders;
    }


    private void addDeliveryInspection(DeliveryInspection deliveryInspection) {
        deliveryInspectionMapper.insert(deliveryInspection);
    }

    /**
     * @param deliveryInspectionId 交付检查任务项ID
     * @param checkItemId          检查项ID
     * @param checkName            检查项名称
     * @param yesOrNo              是否严控(1是 0否)
     * @param returnList           检查项返回数据列表
     * @param currentDate          当前时间
     * @param currentUserId        当前登录账号ID
     * @return DeliveryInspectionDetails
     */
    private DeliveryInspectionDetails getDeliveryInspectionDetails(Long deliveryInspectionId, Long checkItemId,
                                                                   String checkName, boolean yesOrNo,
                                                                   List<ProjectCheckReturnVo> returnList,
                                                                   Date currentDate, Long currentUserId,
                                                                   String remark) {
        String[] str = checkName.split("-");
        checkName = str[0];
        Long projectId = null;
        if (str.length > 1) {
            projectId = Long.valueOf(str[1]);
        }
        DeliveryInspectionDetails deliveryInspectionDetails = new DeliveryInspectionDetails();
        deliveryInspectionDetails.setCheckItemId(checkItemId);
        deliveryInspectionDetails.setCheckName(checkName);
        deliveryInspectionDetails.setYesOrNo(yesOrNo);
        deliveryInspectionDetails.setDeliveryInspectionId(deliveryInspectionId);
        deliveryInspectionDetails.setCreateAt(currentDate);
        deliveryInspectionDetails.setCreateBy(currentUserId);
        deliveryInspectionDetails.setUpdateBy(currentUserId);
        deliveryInspectionDetails.setUpdateAt(currentDate);
        //增加 存在EA可用金额不等于0，则不通过检查
        List<ProjectCostFeeItemRecordDto> collect = null;
        if (projectId != null) {
            DataResponse<ProjectCostDetailVO> dataResponse = statisticsProjectCostViewFeignClient.find(projectId);
            List<ProjectCostFeeItemRecordDto> records = dataResponse.getData().getProjectCostFeeItemRecords();
            collect = records.stream().filter(obj -> {
                if (obj.getEaAvailableAmount() != null) {
                    if (obj.getEaAvailableAmount().compareTo(BigDecimal.ZERO) == 1) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
        }
        if ((returnList != null && returnList.size() != 0) || ListUtils.isNotEmpty(collect)) {
            if (yesOrNo) {
                deliveryInspectionDetails.setStatus(2);//检查状态(1通过 2不通过 3警告)
            } else {
                deliveryInspectionDetails.setStatus(3);//检查状态(1通过 2不通过 3警告)
                deliveryInspectionDetails.setRemark(StringUtils.isNotEmpty(remark) ? remark : "警告");
            }
        } else {
            deliveryInspectionDetails.setStatus(1);//检查状态(1通过 2不通过 3警告)
        }
        return deliveryInspectionDetails;
    }

    private TerminationInspectionDetails getTerminationInspectionDetails(Long terminationInspectionId, Long checkItemId,
                                                                         String checkName, Integer yesOrNo,
                                                                         List<ProjectCheckReturnVo> returnList,
                                                                         String remark) {
        TerminationInspectionDetails terminationInspectionDetails = new TerminationInspectionDetails();
        terminationInspectionDetails.setCheckItemId(checkItemId);
        terminationInspectionDetails.setCheckName(checkName);
        if (yesOrNo == 1 || yesOrNo == 2) {
            terminationInspectionDetails.setYesOrNo(Boolean.TRUE);
        } else {
            terminationInspectionDetails.setYesOrNo(Boolean.FALSE);
        }
        terminationInspectionDetails.setTerminationInspectionId(terminationInspectionId);
        if (ListUtils.isNotEmpty(returnList)) {
            if (yesOrNo == 1) {
                terminationInspectionDetails.setStatus(2);//检查状态(1通过 2不通过 3警告)
                terminationInspectionDetails.setRemark(remark);
            }
            if (yesOrNo == 2) {
                terminationInspectionDetails.setStatus(3);//检查状态(1通过 2不通过 3警告)
                terminationInspectionDetails.setRemark(remark);
            }
        } else {
            terminationInspectionDetails.setStatus(1);//检查状态(1通过 2不通过 3警告)
        }
        return terminationInspectionDetails;
    }

    /**
     * 根据项目id以及里程碑ID检查项目
     *
     * @param projectId  项目id
     * @param milepostId 里程碑ID
     */
    @Override
    public DeliveryInspection checkItemAddTask(Long projectId, Long milepostId) {

        DeliveryInspectionExample example = new DeliveryInspectionExample();
        DeliveryInspectionExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andMilepostIdEqualTo(milepostId);
        criteria.andStatusEqualTo(2);
        example.setOrderByClause("id desc");
        List<DeliveryInspection> deliveryInspectionList = deliveryInspectionMapper.selectByExample(example);
        if (deliveryInspectionList != null && deliveryInspectionList.size() != 0) {
            throw new BizException(ErrorCode.CTC_DELIVERYINSPECTION_IS_NOT_NULL);
        }

        Date currentDate = new Date();
        Long currentUserId = SystemContext.getUserId();
        //新增交付检查任务
        DeliveryInspection deliveryInspection = new DeliveryInspection();
        deliveryInspection.setProjectId(projectId);
        deliveryInspection.setMilepostId(milepostId);
        deliveryInspection.setStatus(2);//交付检查(1未开始,2进行中 3通过 4不通过 5警告)
        deliveryInspection.setUpdateAt(currentDate);
        deliveryInspection.setUpdateBy(currentUserId);
        deliveryInspection.setCreateAt(currentDate);
        deliveryInspection.setCreateBy(currentUserId);
        addDeliveryInspection(deliveryInspection);
        return deliveryInspection;
    }

    @Override
    public Map<String, Object> checkItem(Long projectId, Long milepostId, Long deliveryInspectionId, Map<String,
            Object> workflowInfo) throws IOException {
        Map<String, Object> result = new HashMap<>();

        Date currentDate = new Date();
        Long currentUserId = SystemContext.getUserId();
        if (projectId == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_ID_NULL);
        }
        if (milepostId == null) {
            throw new BizException(ErrorCode.CTC_MILEPOST_ID_NULL);
        }
        Project project = this.projectMapper.selectByPrimaryKey(projectId);
        if (project == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_NOT_FIND);
        }
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        String filePath = "/apps/pam/ctc/file/" + project.getCode() + "结项检查结果" + df.format(currentDate) + ".xls";
        Long projectTypeId = project.getType();//项目类型ID
        if (projectTypeId == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_TYPE_NOT_NULL);
        }

        //新增交付检查任务
        DeliveryInspection deliveryInspection = deliveryInspectionMapper.selectByPrimaryKey(deliveryInspectionId);
        if (deliveryInspection == null) {
            throw new BizException(ErrorCode.CTC_DELIVERYINSPECTION_NOT_NULL);
        }
        int deliveryInspectionStatus = 3;//默认等于通过
        List<DeliveryInspectionDetails> deliveryInspectionDetailsList = new ArrayList<>();
        FileInputStream fileInputStream = null;
        OutputStream out = null;
        try {
            ExportExcelUtil eeu = new ExportExcelUtil();
            HSSFWorkbook workbook = new HSSFWorkbook();
            out = Files.newOutputStream(Paths.get(filePath));
            List<ProjectTypeCheckRelDto> list = projectTypeCheckRelExtMapper.selectProjectTypeCheckRel(projectTypeId);
            if (list != null && list.size() != 0) {
                int i = 0;
                for (ProjectTypeCheckRelDto projectTypeCheckRelDto : list) {
                    String checkCode = projectTypeCheckRelDto.getCheckCode();//检查点CODE
                    String checkName = projectTypeCheckRelDto.getCheckName();//检查点名称
                    Boolean yesOrNo = projectTypeCheckRelDto.getYesOrNo();//是否严控(1是 0否)
                    Long checkItemId = projectTypeCheckRelDto.getCheckItemId();//交付检查项ID
                    DeliveryInspectionDetails deliveryInspectionDetails = null;
                    //项目是否存在审批中的流程
                    if (CheckItemEnums.A01.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectItem(project, workflowInfo);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目是否存在审批中、变更中的工时
                    } else if (CheckItemEnums.A02.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectChangeWorkingHourItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //销售合同实际开票金额与合同金额（含税）是否一致
                    } else if (CheckItemEnums.A03.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkContractInvoiceItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //销售合同实际回款金额与实际开票金额是否一致
                    } else if (CheckItemEnums.A04.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkContractMoneyBackItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目工时填报天数是否与合同天数一致
                    } else if (CheckItemEnums.A05.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectWorkingHourItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目辅里程碑节点是否已全部交付
                    } else if (CheckItemEnums.A06.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectMilepostItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目是否存在在途的费用申请/报销单
                    } else if (CheckItemEnums.A07.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectFeeItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        checkName = checkName + "-" + projectId;
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目详细设计是否存在审批中的流程
                    } else if (CheckItemEnums.A08.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectDesignItem(project, workflowInfo);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目外包采购是否存在审批中的流程
                    } else if (CheckItemEnums.A09.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectPurchaseItem(project, workflowInfo);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目库存是否存在审批中的流程
                    } else if (CheckItemEnums.A10.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectStockApprovalItem(project, workflowInfo);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目是否已完成详细设计方案的进度确认
                    } else if (CheckItemEnums.A11.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectPlanItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目详细设计方案的采购物料是否已全部生成采购需求
                    } else if (CheckItemEnums.A12.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectMaterielItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目采购需求是否已全部生成采购订单
                    } else if (CheckItemEnums.A13.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectRequirementItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目采购订单物料是否已全部接收入库
                    } else if (CheckItemEnums.A14.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectPurchaseOrderItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目子库的库存是否为0
                    } else if (CheckItemEnums.A15.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectStockItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目库存是否存在未处理完的单据
                    } else if (CheckItemEnums.A16.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectStorageItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //  项目是否存在未开票的工时
                    } else if (CheckItemEnums.A24.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectUninvoiceWorking(project);

                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                        //项目是否存在未关闭的问题
                    } else if (CheckItemEnums.A25.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = projectService.checkProjectProblem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A27.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo =
                                projectService.checkProjectPurchaseInvoiceItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A28.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkContractInvoice(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId, checkName,
                                yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A29.getCode().equals(checkCode)) {
                        if (null == project.getWbsEnabled() || Boolean.FALSE.equals(project.getWbsEnabled())) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = milepostDesignPlanService.checkProjectPurchaseMaterialRequirementItem(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId, checkName,
                                yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A32.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectPurchaseContractEffectPunishment(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().isEmpty()) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A33.getCode().equals(checkCode)) {
                        ProjectCheckExcelVo projectCheckExcelVo = isPaidAmountExceedingContractAmount(project);
                        if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().isEmpty()) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        deliveryInspectionDetails = getDeliveryInspectionDetails(deliveryInspectionId, checkItemId,
                                checkName, yesOrNo, returnList, currentDate, currentUserId, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    }
                    if (deliveryInspectionDetails != null) {
                        deliveryInspectionDetailsMapper.insert(deliveryInspectionDetails);
                        deliveryInspectionDetailsList.add(deliveryInspectionDetails);
                    }
                }

                if (CollectionUtils.isNotEmpty(deliveryInspectionDetailsList)) {
                    for (DeliveryInspectionDetails deliveryInspectionDetails : deliveryInspectionDetailsList) {
                        //检查状态(1通过 2不通过 3警告)
                        if (deliveryInspectionDetails.getStatus() == 2) { //检查项有一项不通过即不通过
                            deliveryInspectionStatus = 4;//交付检查(1未开始,2进行中 3通过 4不通过 5警告 6系统异常)
                            break;
                            //检查状态(1通过 2不通过 3警告)
                        } else if (deliveryInspectionDetails.getStatus() == 3) {//检查项有一项警告即是警告
                            deliveryInspectionStatus = 5;//交付检查(1未开始,2进行中 3通过 4不通过 5警告 6系统异常)
                        }
                    }
                }

                //更新任务状态
                deliveryInspection.setStatus(deliveryInspectionStatus);
                deliveryInspection.setUpdateAt(new Date());
                deliveryInspection.setUpdateBy(currentUserId);
                deliveryInspectionMapper.updateByPrimaryKey(deliveryInspection);

                workbook.write(out);
                out.close();
                result.put("isSuccess", true);
                result.put("filePath", filePath);


                File pdfFile = new File(filePath);
                fileInputStream = new FileInputStream(pdfFile);
                MultipartFile multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                        ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                //{"code":0,"msg":"SUCCESS","data":[{"fileName":"8888.png","uploadDate":"2019-11-26 19:23:44",
                // "upload":true,"fileId":"648967143655735296"}]}
                Map<String, Object> map = ossService.upload(multipartFile);
                CtcAttachmentDto dto = new CtcAttachmentDto();
                dto.setModule(CtcAttachmentModule.PROJECT_CHECK_ITEM.code());
                dto.setDeletedFlag(false);
                dto.setModuleId(deliveryInspection.getId());
                dto.setAttachId((Long) map.get("fileId"));
                dto.setFileName((String) map.get("fileName"));
                dto.setUpdateAt(new Date());
                dto.setUpdateBy(currentUserId);
                dto.setCreateAt(new Date());
                dto.setCreateBy(currentUserId);
                dto.setStatus(1);
                ctcAttachmentService.add(dto);

            }
        } catch (Exception ex) {
            result.put("isSuccess", false);
            result.put("error", ex.getMessage());
            logger.error(ex.getMessage(), ex);

            deliveryInspection.setStatus(6);//系统异常
            deliveryInspection.setUpdateAt(new Date());
            deliveryInspection.setUpdateBy(currentUserId);
            deliveryInspectionMapper.updateByPrimaryKey(deliveryInspection);
        } finally {
            if (fileInputStream != null) {
                fileInputStream.close();
            }
            if (out != null) {
                out.close();
            }
        }
        return result;
    }

    @Override
    public ProjectCheckExcelVo checkItemByNum(Long projectId, Long milepostId, Integer number,
                                              Map<String, Object> workflowInfo) {
        Map<String, Object> result = new HashMap<String, Object>();
        if (projectId == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_ID_NULL);
        }
        if (milepostId == null) {
            throw new BizException(ErrorCode.CTC_MILEPOST_ID_NULL);
        }
        Project project = this.projectMapper.selectByPrimaryKey(projectId);
        if (project == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_NOT_FIND);
        }
        Long projectTypeId = project.getType();//项目类型ID
        if (projectTypeId == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_TYPE_NOT_NULL);
        }
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        switch (number) {
            case 0:
                projectCheckExcelVo = checkProjectItem(project, workflowInfo);
                break;
            case 1:
                projectCheckExcelVo = checkProjectChangeWorkingHourItem(project);
                break;
            case 2:
                projectCheckExcelVo = checkContractInvoiceItem(project);
                break;
            case 3:
                projectCheckExcelVo = checkContractMoneyBackItem(project);
                break;
            case 4:
                projectCheckExcelVo = checkProjectWorkingHourItem(project);
                break;
            case 5:
                projectCheckExcelVo = checkProjectMilepostItem(project);
                break;
            case 6:
                projectCheckExcelVo = checkProjectFeeItem(project);
                break;
            case 7:
                projectCheckExcelVo = checkProjectDesignItem(project, workflowInfo);
                break;
            case 8:
                projectCheckExcelVo = checkProjectPurchaseItem(project, workflowInfo);
                break;
            case 9:
                projectCheckExcelVo = checkProjectStockApprovalItem(project, workflowInfo);
                break;
            case 10:
                projectCheckExcelVo = checkProjectPlanItem(project);
                break;
            case 11:
                projectCheckExcelVo = checkProjectMaterielItem(project);
                break;
            case 12:
                projectCheckExcelVo = checkProjectRequirementItem(project);
                break;
            case 13:
                projectCheckExcelVo = checkProjectPurchaseOrderItem(project);
                break;
            case 14:
                projectCheckExcelVo = checkProjectStockItem(project);
                break;
            case 15:
                projectCheckExcelVo = checkProjectStorageItem(project);
                break;
            default:
                break;
        }
        return projectCheckExcelVo;
    }

    @Override
    public String deliveryInspectionDetails(Long deliveryInspectionId) {
        // deliveryInspectionDetailsMapper.selectByExample()
        return null;
    }

    /**
     * 项目是否存在审批中的流程(01)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectItem(Project project, Map<String, Object> workflowInfo) {
        ProjectCheckExcelVo excelVo = new ProjectCheckExcelVo();
        if (ObjectUtils.isEmpty(project)) {
            return null;
        }
        String[] headers = {"序号", "主题", "创建人", "当前处理人", "创建时间", "状态"};
        excelVo.setHeaders(headers);
        excelVo.setDate(new Date());
        excelVo.setCheckName("项目是否存在审批中的流程");
        excelVo.setProjectCode(project.getCode());
        excelVo.setProjectName(project.getName());
        excelVo.setPass(Boolean.TRUE);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        List<WorkflowInfoVo> workflowInfoVoList = new ArrayList<>();


        if ("-1".equals(workflowInfo.get("code") + "")) {
            ProjectCheckReturnVo pcr = new ProjectCheckReturnVo();
            pcr.setC0("1");
            pcr.setC1(workflowInfo.get("msg") + "");
            return excelVo;
        }

        List<WorkflowInfoVo> changeApps = getWorkflowInfoByApp(workflowInfo, "projectBaseInfoChangeApp");//项目基本信息变更流程
        changeApps.addAll(getWorkflowInfoByApp(workflowInfo, "projectMilepostChangeApp"));//项目里程碑信息变更流程
        changeApps.addAll(getWorkflowInfoByApp(workflowInfo, "projectBudgetChangeApp"));//项目预算变更
        ProjectHistoryHeaderExample historyHeaderExample = new ProjectHistoryHeaderExample();
        List<Integer> changeType = new ArrayList<>();
        changeType.add(CtcProjectChangeType.BASEINFO.getCode());//基本信息变更
        changeType.add(CtcProjectChangeType.MILEPOST.getCode());//项目里程碑变更
        changeType.add(CtcProjectChangeType.BUDGET.getCode());//预算变更
        changeType.add(CtcProjectChangeType.WBS_BUDGET.getCode());//wbs预算变更
        historyHeaderExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andChangeTypeIn(changeType)
                .andProjectIdEqualTo(project.getId()).andStatusEqualTo(ProjectChangeStatus.APPROVALING.getCode());//审批中
        List<ProjectHistoryHeader> historyHeaderList = projectHistoryHeaderMapper.selectByExample(historyHeaderExample);
        List<Long> historyHeaderIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(historyHeaderList)) {
            for (ProjectHistoryHeader historyHeader : historyHeaderList) {
                historyHeaderIds.add(historyHeader.getId());
            }
            //判断该工作流是否属于本项目
            for (WorkflowInfoVo workflowInfoVo : changeApps) {
                if (historyHeaderIds.contains(workflowInfoVo.getFormInstanceId())) {
                    workflowInfoVoList.add(workflowInfoVo);
                }
            }
        }
        List<Contract> contractList = contractExtMapper.getChangingContract(project.getId());
        if (!CollectionUtils.isEmpty(contractList)) {
            List<Long> contractIds = new ArrayList<>();
            for (Contract contract : contractList) {
                contractIds.add(contract.getId());
            }
            List<WorkflowInfoVo> contractChangeApps = getWorkflowInfoByApp(workflowInfo,
                    "contractBaseInfoChanageApp");//PAM销售合同基本信息变更
            contractChangeApps.addAll(getWorkflowInfoByApp(workflowInfo, "contractProductChanageApp"));//PAM销售合同服务内容变更
            contractChangeApps.addAll(getWorkflowInfoByApp(workflowInfo, "contractReceiptPlanChanageApp"));//PAM
            // 销售合同开票回款计划变更
            ContractChangewayExample contractChangewayExample = new ContractChangewayExample();
            contractChangewayExample.createCriteria()
                    .andContractIdIn(contractIds).andApprovalStatusEqualTo(ContractChangewayStatus.PENDING.getCode());//审批中
            List<ContractChangeway> contractChangewayList =
                    contractChangewayMapper.selectByExample(contractChangewayExample);
            List<Long> contractChangewayIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(contractChangewayList)) {
                for (ContractChangeway contractChangeway : contractChangewayList) {
                    contractChangewayIds.add(contractChangeway.getId());
                }
                //判断该工作流是否属于本项目
                for (WorkflowInfoVo workflowInfoVo : contractChangeApps) {
                    if (contractChangewayIds.contains(workflowInfoVo.getFormInstanceId())) {
                        workflowInfoVoList.add(workflowInfoVo);
                    }
                }
            }
        }

        //如果项目关联了商机号
        if (StringUtils.isNotEmpty(project.getBusinessId())) {
            //商机方案
            List<WorkflowInfoVo> planApps = getWorkflowInfoByApp(workflowInfo, "planApp");
            //根据商机查询审批中的方案
            List<Plan> planList = crmExtService.getPlanByBusinessCode(project.getBusinessId());
            List<Long> planIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(planList)) {
                for (Plan plan : planList) {
                    planIds.add(plan.getId());
                }
                //判断该工作流是否属于本项目关联商机
                for (WorkflowInfoVo workflowInfoVo : planApps) {
                    if (planIds.contains(workflowInfoVo.getFormInstanceId())) {
                        workflowInfoVoList.add(workflowInfoVo);
                    }
                }
            }

            //商机报价
            List<WorkflowInfoVo> quotationApps = getWorkflowInfoByApp(workflowInfo, "quotationApp");
            quotationApps.addAll(getWorkflowInfoByApp(workflowInfo, "winningApp"));//商机赢单
            //根据商机查询审批中的方案
            List<Quotation> quotationList = crmExtService.getQuotationByBusinessCode(project.getBusinessId());
            List<Long> quotationIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(quotationList)) {
                for (Quotation quotation : quotationList) {
                    quotationIds.add(quotation.getId());
                }
                //判断该工作流是否属于本项目关联商机
                for (WorkflowInfoVo workflowInfoVo : quotationApps) {
                    if (quotationIds.contains(workflowInfoVo.getFormInstanceId())) {
                        workflowInfoVoList.add(workflowInfoVo);
                    }
                }
            }
        }

        //开票申请
        if (null != project.getContractId()) {
            List<WorkflowInfoVo> invoiceApplys = getWorkflowInfoByApp(workflowInfo, "invoiceApply");
            InvoiceApplyDetailsExample applyDetailsExample = new InvoiceApplyDetailsExample();
            applyDetailsExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andContractIdEqualTo(project.getContractId())
                    .andStatusEqualTo(InvoiceApplyStatusEnum.DETAILS_CHECKING.code());//审批中
            List<InvoiceApplyDetails> applyDetailsList = invoiceApplyDetailsMapper.selectByExample(applyDetailsExample);
            List<Long> applyHeaderIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(applyDetailsList)) {
                for (InvoiceApplyDetails applyDetail : applyDetailsList) {
                    applyHeaderIds.add(applyDetail.getApplyHeaderId());
                }
                //判断该工作流是否属于本项目
                for (WorkflowInfoVo workflowInfoVo : invoiceApplys) {
                    if (applyHeaderIds.contains(workflowInfoVo.getFormInstanceId())) {
                        workflowInfoVoList.add(workflowInfoVo);
                    }
                }
            }
        }

        returnVoList.addAll(workflowInfoToExcelVo(workflowInfoVoList));
        if (returnVoList.size() > 0) {
            excelVo.setPass(false);
        }
        excelVo.setReturnList(returnVoList);
        return excelVo;
    }

    private List<WorkflowInfoVo> getWorkflowInfoByApp(Map<String, Object> workflowInfo, String formUrl) {
        List<WorkflowInfoVo> infoVoList = new ArrayList<>();
        Object object = workflowInfo.get(formUrl);
        if (!ObjectUtils.isEmpty(object)) {
            infoVoList = JSONObject.parseArray(JSONObject.toJSONString(object)).toJavaList(WorkflowInfoVo.class);
        }
        return infoVoList;
    }

    /**
     * 项目是否存在审批中、变更中的工时(02)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectChangeWorkingHourItem(Project project) {
        String[] headers = {"序号", "填报人姓名", "填报人MIP账号", "出勤日期", "填报日期", "填报工时数", "状态", "审批人", "审批人MIP"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        List<ProjectCheckReturnVo> list = projectCheckReturnVoMapper.checkProjectChangeWorkingHourItem(project.getId());
        if (list != null) {
            for (ProjectCheckReturnVo projectCheckReturnVo : list) {
                if (StringUtils.isNotEmpty(projectCheckReturnVo.getC9())) {
                    Long userId = Long.parseLong(projectCheckReturnVo.getC9());
                    UserInfo userInfo = CacheDataUtils.findUserById(userId);
                    if (userInfo != null) {
                        projectCheckReturnVo.setC1(userInfo.getName());
                        projectCheckReturnVo.setC2(userInfo.getUsername());
                    }
                }
                if (StringUtils.isNotEmpty(projectCheckReturnVo.getC7())) {
                    Long approveUserId = Long.parseLong(projectCheckReturnVo.getC7());
                    UserInfo approveUser = CacheDataUtils.findUserById(approveUserId);
                    if (approveUser != null) {
                        projectCheckReturnVo.setC7(approveUser.getName());
                    }
                }
            }
        }
        projectCheckExcelVo.setReturnList(list);
        return projectCheckExcelVo;
    }

    /**
     * 销售金额与合同金额（含税）是否一致(03)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkContractInvoiceItem(Project project) {
        String[] headers = {"序号", "子合同编号", "子合同名称", "合同金额（含税）", "开票总金额（含税）", "差异金额"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        ProjectCheckReturnVo returnVo = getCheckexcelVo(project);
        if (!ObjectUtils.isEmpty(returnVo)) {
            String remark = "警告，实际开票金额：" + returnVo.getC4() + "，合同金额：" + returnVo.getC3()
                    + "，币种：" + returnVo.getC6();
            projectCheckExcelVo.setRemark(remark);
            returnVoList.add(returnVo);
            projectCheckExcelVo.setReturnList(returnVoList);
        }
//      projectCheckExcelVo.setReturnList(projectCheckReturnVoMapper.checkContractInvoiceItem(project.getId()));
        return projectCheckExcelVo;
    }

    /**
     * 销售合同实际回款金额与实际开票金额是否一致(04)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkContractMoneyBackItem(Project project) {
        String[] headers = {"序号", "子合同编号", "子合同名称", "合同金额（含税）", "开票总金额（含税）", "回款总金额（含税）", "差异金额"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);

        final List<ProjectContractRs> projectContractRsList =
                projectContractRsService.selectByProjectId(project.getId());
        if (ListUtils.isNotEmpty(projectContractRsList)) {

            final Long contractId = projectContractRsList.get(0).getContractId();
            //开票计划行
            InvoicePlanDetailExample invoicePlanDetailExample = new InvoicePlanDetailExample();
            invoicePlanDetailExample.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE);
            List<InvoicePlanDetail> detailList = invoicePlanDetailMapper.selectByExample(invoicePlanDetailExample);
            if (ListUtils.isNotEmpty(detailList)) {
                List<ProjectCheckReturnVo> returnVoList;
                final List<InvoicePlanDetail> isImportInvoicePlanDetails =
                        detailList.stream().filter(d -> d.getIsImport() != null && d.getIsImport().equals(Boolean.TRUE)).collect(Collectors.toList());
                // 如果有导入的开票计划那么就取开票计划里面的实际开票金额之和
                if (ListUtils.isNotEmpty(isImportInvoicePlanDetails)) {
                    returnVoList = projectCheckReturnVoMapper.checkContractMoneyBackItem1(project.getId());
                } else {
                    returnVoList = projectCheckReturnVoMapper.checkContractMoneyBackItem(project.getId());
                }
                //业务需求：在1元的差异内，结项都可以过
                List<ProjectCheckReturnVo> returnVoList1 = returnVoList.stream().filter(e -> filterAllowDifference(e.getC6())).collect(Collectors.toList());
                projectCheckExcelVo.setReturnList(returnVoList1);
            }

        }
        return projectCheckExcelVo;

    }

    public ProjectCheckReturnVo getCheckexcelVo(Project project) {
        ProjectCheckReturnVo returnVo = null;
        BigDecimal invoiceAmount = BigDecimal.ZERO;//开票总金额
        BigDecimal differenceAmount = BigDecimal.ZERO;//差异金额
        //合同编号
        final List<ProjectContractRs> projectContractRs = projectContractRsService.selectByProjectId(project.getId());
        if (ListUtils.isNotEmpty(projectContractRs)) {
            final Long contractId = projectContractRs.get(0).getContractId();
            if (contractId != null) {
                //子合同
                final Contract contract = contractService.findById(contractId);
                //开票计划行
                InvoicePlanDetailExample invoicePlanDetailExample = new InvoicePlanDetailExample();
                invoicePlanDetailExample.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE);
                List<InvoicePlanDetail> detailList = invoicePlanDetailMapper.selectByExample(invoicePlanDetailExample);
                if (ListUtils.isNotEmpty(detailList)) {
                    final List<InvoicePlanDetail> isImportInvoicePlanDetails =
                            detailList.stream().filter(d -> d.getIsImport() != null && d.getIsImport().equals(Boolean.TRUE)).collect(Collectors.toList());
                    // 如果有导入的开票计划那么就取开票计划里面的实际开票金额之和
                    if (ListUtils.isNotEmpty(isImportInvoicePlanDetails)) {
                        for (InvoicePlanDetail planDetail : detailList) {
                            final BigDecimal taxIncludedPrice = planDetail.getTaxIncludedPrice() == null ?
                                    BigDecimal.ZERO : planDetail.getTaxIncludedPrice();
                            invoiceAmount = invoiceAmount.add(taxIncludedPrice);
                        }
                    } else { // 否则就取应收开票里面的开票金额之和
                        InvoiceReceivableExample invoiceReceivableExample = new InvoiceReceivableExample();
                        invoiceReceivableExample.createCriteria().andContractIdEqualTo(contractId).andStatusEqualTo(
                                "开票成功").andDeletedFlagEqualTo(0);
                        List<InvoiceReceivable> invoiceReceivableList =
                                invoiceReceivableMapper.selectByExampleWithBLOBs(invoiceReceivableExample);
                        for (InvoiceReceivable invoiceReceivable : invoiceReceivableList) {
                            final BigDecimal taxIncludedPrice = invoiceReceivable.getTaxIncludedPrice() == null ?
                                    BigDecimal.ZERO : invoiceReceivable.getTaxIncludedPrice();
                            invoiceAmount = invoiceAmount.add(taxIncludedPrice);
                        }

                    }
                }

                differenceAmount = invoiceAmount.subtract(contract.getAmount());
                //允差+-1
                if (differenceAmount.compareTo(new BigDecimal("1")) > 0 || differenceAmount.compareTo(new BigDecimal("-1")) < 0) {
                    returnVo = new ProjectCheckReturnVo();
                    returnVo.setC0("1");
                    returnVo.setC1(contract.getCode());
                    returnVo.setC2(contract.getName());
                    returnVo.setC3(contract.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    returnVo.setC4(invoiceAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    returnVo.setC5(differenceAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    returnVo.setC6(contract.getCurrency());//币种，字段不需要导出
                }
            }
        }
        return returnVo;
    }

    public ProjectCheckReturnVo getCheckexcelInvoice(Project project) {
        ProjectCheckReturnVo returnVo = null;
        BigDecimal invoiceAmount = BigDecimal.ZERO;//开票总金额
        BigDecimal warrantyAmount = BigDecimal.ZERO; //质保款金额
        BigDecimal differenceAmount = BigDecimal.ZERO;//差异金额

        //合同编号
        final List<ProjectContractRs> projectContractRs = projectContractRsService.selectByProjectId(project.getId());
        if (ListUtils.isNotEmpty(projectContractRs)) {
            final Long contractId = projectContractRs.get(0).getContractId();
            if (contractId != null) {
                //子合同
                final Contract contract = contractService.findById(contractId);
                //开票计划行
                InvoicePlanDetailExample invoicePlanDetailExample = new InvoicePlanDetailExample();
                invoicePlanDetailExample.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE);
                List<InvoicePlanDetail> detailList = invoicePlanDetailMapper.selectByExample(invoicePlanDetailExample);
                if (ListUtils.isNotEmpty(detailList)) {
                    final List<InvoicePlanDetail> isImportInvoicePlanDetails =
                            detailList.stream().filter(d -> d.getIsImport() != null && d.getIsImport().equals(Boolean.TRUE)).collect(Collectors.toList());
                    for (InvoicePlanDetail detail : detailList) {
                        if (detail.getAmount() != null) {
                            final BigDecimal warrantyTypeAmount = ("质保款").equals(detail.getInvoiceType()) ? detail.getAmount() : BigDecimal.ZERO;
                            warrantyAmount = warrantyAmount.add(warrantyTypeAmount);
                        }
                    }
                    // 如果有导入的开票计划那么就取开票计划里面的实际开票金额之和
                    if (ListUtils.isNotEmpty(isImportInvoicePlanDetails)) {
                        for (InvoicePlanDetail planDetail : detailList) {
                            final BigDecimal taxIncludedPrice = planDetail.getTaxIncludedPrice() == null ? BigDecimal.ZERO :
                                    planDetail.getTaxIncludedPrice();
                            invoiceAmount = invoiceAmount.add(taxIncludedPrice);
                        }
                    } else { // 否则就取应收开票里面的开票金额之和
                        InvoiceReceivableExample invoiceReceivableExample = new InvoiceReceivableExample();
                        invoiceReceivableExample.createCriteria().andContractIdEqualTo(contractId).andStatusEqualTo("开票成功").andDeletedFlagEqualTo(0);
                        List<InvoiceReceivable> invoiceReceivableList = invoiceReceivableMapper.selectByExampleWithBLOBs(invoiceReceivableExample);
                        for (InvoiceReceivable invoiceReceivable : invoiceReceivableList) {
                            final BigDecimal taxIncludedPrice = invoiceReceivable.getTaxIncludedPrice() == null ? BigDecimal.ZERO :
                                    invoiceReceivable.getTaxIncludedPrice();
                            invoiceAmount = invoiceAmount.add(taxIncludedPrice);
                        }
                    }
                }
                differenceAmount = invoiceAmount.subtract(contract.getAmount().subtract(warrantyAmount));
                //销售合同实际开票金额 - （合同金额（含税）- 质保款） < 0 ，不允许通过
                if (differenceAmount.compareTo(BigDecimal.ZERO) < 0) {
                    returnVo = new ProjectCheckReturnVo();
                    returnVo.setC0("1");
                    returnVo.setC1(contract.getCode());
                    returnVo.setC2(contract.getName());
                    returnVo.setC3(contract.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    returnVo.setC4(String.valueOf(warrantyAmount));
                    returnVo.setC5(invoiceAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    returnVo.setC6(String.valueOf(differenceAmount));
                    returnVo.setC7(contract.getCurrency());//币种，字段不需要导出
                }
            }
        }
        return returnVo;
    }

    @Override
    public List<EsbProjectVo> queryEsbProject(String code, String name, String updateAt) {
        return this.esbQueryVoMapper.queryEsbProject(code, name, updateAt);
    }

    @Override
    public List<EsbProjectMilepostVo> queryEsbProjectMilepost(Long projectId, String updateAt) {
        return this.queryEsbProjectMilepost(projectId, updateAt);
    }

    @Override
    public List<EsbProjectMemberVo> queryEsbProjectMember(Long projectId, String updateAt) {
        return this.esbQueryVoMapper.queryEsbProjectMember(projectId, updateAt);
    }

    @Override
    public List<EsbProjectTypeVo> queryEsbEsbProjectType(Long unitId, String updateAt) {
        return this.esbQueryVoMapper.queryEsbEsbProjectType(unitId, updateAt);
    }

    /**
     * 项目工时填报天数是否与合同天数一致(05)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectWorkingHourItem(Project project) {
        String[] headers = {"序号", "子合同编号", "子合同名称", "子合同生效日期", "子合同失效日期", "子合同总天数", "已审核工时天数", "差异天数"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(projectCheckReturnVoMapper.checkProjectWorkingHourItem(project.getId()));
        return projectCheckExcelVo;
    }

    /**
     * 项目辅里程碑节点是否已全部交付(06)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectMilepostItem(Project project) {
        String[] headers = {"序号", "里程碑节点", "计划开始日期", "计划结束日期", "责任人姓名", "责任人MIP账号", "状态"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        List<ProjectCheckReturnVo> list = projectCheckReturnVoMapper.checkProjectMilepostItem(project.getId());
        if (list != null) {
            projectCheckExcelVo.setReturnList(list);
            for (ProjectCheckReturnVo projectCheckReturnVo : list) {
                if (StringUtils.isNotEmpty(projectCheckReturnVo.getC7())) {
                    Long userId = Long.parseLong(projectCheckReturnVo.getC7());
                    UserInfo userInfo = CacheDataUtils.findUserById(userId);
                    if (userInfo != null) {
                        projectCheckReturnVo.setC1(userInfo.getName());
                        projectCheckReturnVo.setC2(userInfo.getUsername());
                    }
                }
            }
        }
        return projectCheckExcelVo;
    }

    /**
     * 项目是否存在在途的费用申请/报销单(07)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectFeeItem(Project project) {
        String[] headers = {"序号", "单据编号", "申请人", "申请日期", "经济事项", "金额(不含税)", "币种", "供应商编码", "供应商名称", "单据业务描述"};
        int number = 1;
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setCheckName("项目是否存在在途的费用申请/报销单");
        projectCheckExcelVo.setHeaders(headers);
        List<ProjectCheckReturnVo> returnList = new ArrayList<>();

        //查询在途的费用申请
        List<EmsPamFeeDetail> emsPamFeeDetailList = new ArrayList<>();
        if (Objects.equals(project.getWbsEnabled(), Boolean.TRUE)) {
            // 根据wbs编号查询wbs费用成本
            emsPamFeeDetailList = emsPamFeeDetailExtMapper.selectWbsUnprocessCost(project.getCode());
        } else {
            EmsPamFeeDetailExample emsPamFeeDetailExample = new EmsPamFeeDetailExample();
            emsPamFeeDetailExample.createCriteria().andImportErpStatusEqualTo("UNPROCESS").andBussinessTypeCodeEqualTo(project.getCode());
            emsPamFeeDetailList = emsPamFeeDetailMapper.selectByExample(emsPamFeeDetailExample);
        }
        if (!CollectionUtils.isEmpty(emsPamFeeDetailList)) {
            for (int i = 0; i < emsPamFeeDetailList.size(); i++) {
                EmsPamFeeDetail emsPamFeeDetail = emsPamFeeDetailList.get(i);
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0(Integer.toString(number++));
                returnVo.setC1(emsPamFeeDetail.getOrderCode());
                returnVo.setC2(emsPamFeeDetail.getApplyName());
                returnVo.setC3(DateUtil.format(emsPamFeeDetail.getApplyDate(), DateUtil.TIMESTAMP_PATTERN));
                returnVo.setC4(emsPamFeeDetail.getFeeTypeName());
                returnVo.setC5(emsPamFeeDetail.getFeeAmount() != null ? emsPamFeeDetail.getFeeAmount().toString() : "");
                returnVo.setC6(emsPamFeeDetail.getCurrencyName());
                returnVo.setC7(emsPamFeeDetail.getVendorCode());
                returnVo.setC8(emsPamFeeDetail.getVendorName());
                returnVo.setC9(emsPamFeeDetail.getSensitiveInfo());
                returnList.add(returnVo);
            }
        }

        //查询在途的ea单
        DataResponse<ProjectCostDetailVO> projectCostDetailVODataResponse =
                statisticsProjectCostViewFeignClient.find(project.getId());
        List<ProjectCostEaDetailRecord> projectCostEaDetailRecords =
                projectCostDetailVODataResponse.getData().getProjectCostEaDetailRecords();
        if (ListUtils.isNotEmpty(projectCostEaDetailRecords)) {
            // 按照EA单据号分组汇总金额
            Map<String, List<ProjectCostEaDetailRecord>> voGroup = projectCostEaDetailRecords.stream()
                    .collect(Collectors.groupingBy(ProjectCostEaDetailRecord::getFeeApplyCode));

            for (List<ProjectCostEaDetailRecord> voList : voGroup.values()) {
                // 判断累加金额是否大于0
                BigDecimal sumAmount = voList.stream().map(ProjectCostEaDetailRecord::getOverplusEaAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                if (sumAmount.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                for (int i = 0; i < voList.size(); i++) {
                    ProjectCostEaDetailRecord projectCostEaDetailRecord = voList.get(i);
                    ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                    returnVo.setC0(Integer.toString(number++));
                    returnVo.setC1(projectCostEaDetailRecord.getFeeApplyCode());
                    returnVo.setC2(projectCostEaDetailRecord.getApplyName());
                    returnVo.setC3(DateUtil.format(projectCostEaDetailRecord.getSubmitedTime(),
                            DateUtil.TIMESTAMP_PATTERN));
                    returnVo.setC4(projectCostEaDetailRecord.getFeeTypeName());
                    returnVo.setC5(projectCostEaDetailRecord.getOverplusEaAmount().toString());
                    returnVo.setC6(projectCostEaDetailRecord.getCurrencyName());
                    returnVo.setC9(projectCostEaDetailRecord.getSensitiveInfo());
                    returnList.add(returnVo);
                }
            }
        }
        projectCheckExcelVo.setReturnList(returnList);
        return projectCheckExcelVo;
    }


    /**
     * 项目详细设计是否存在审批中的流程(08)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectDesignItem(Project project, Map<String, Object> workflowInfo) {
        ProjectCheckExcelVo excelVo = new ProjectCheckExcelVo();
        if (ObjectUtils.isEmpty(project)) {
            return null;
        }
        String[] headers = {"序号", "主题", "创建人", "当前处理人", "创建时间", "状态"};
        excelVo.setHeaders(headers);
        excelVo.setDate(new Date());
        excelVo.setCheckName("项目详细设计是否存在审批中的流程");
        excelVo.setProjectCode(project.getCode());
        excelVo.setProjectName(project.getName());
        excelVo.setPass(Boolean.TRUE);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        List<WorkflowInfoVo> workflowInfoVoList = new ArrayList<>();//当前项目待审流程

        if ("-1".equals(workflowInfo.get("code") + "")) {
            ProjectCheckReturnVo pcr = new ProjectCheckReturnVo();
            pcr.setC0("1");
            pcr.setC1(workflowInfo.get("msg") + "");
            return excelVo;
        }

        //详细设计发布
        List<WorkflowInfoVo> purchaseApps = getWorkflowInfoByApp(workflowInfo, "milepostDesignPlanPurchaseApp");
        //详细设计方案发布审批(采购)
        purchaseApps.addAll(getWorkflowInfoByApp(workflowInfo, "milepostDesignPlanNoPurchaseApp"));//详细设计方案发布审批(非采购)
        MilepostDesignPlanSubmitRecordExample submitRecordExample = new MilepostDesignPlanSubmitRecordExample();
        submitRecordExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andProjectIdEqualTo(project.getId()).andStatusEqualTo(CheckStatus.CHECKING.code());//审批中
        List<MilepostDesignPlanSubmitRecord> submitRecordList = submitRecordMapper.selectByExampleWithBLOBs(submitRecordExample);
        List<Long> submitRecordIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(submitRecordList)) {
            for (MilepostDesignPlanSubmitRecord submitRecord : submitRecordList) {
                submitRecordIds.add(submitRecord.getId());
            }
            //判断该工作流是否属于本项目
            for (WorkflowInfoVo workflowInfoVo : purchaseApps) {
                if (submitRecordIds.contains(workflowInfoVo.getFormInstanceId())) {
                    workflowInfoVoList.add(workflowInfoVo);
                }
            }
        }

        //详细设计变更
        List<WorkflowInfoVo> planChangeApps = getWorkflowInfoByApp(workflowInfo, "milepostDesignPlanChangeApp");
        //里程碑详细设计方案变更审批
        MilepostDesignPlanChangeRecordExample changeRecordExample = new MilepostDesignPlanChangeRecordExample();
        changeRecordExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andProjectIdEqualTo(project.getId()).andStatusEqualTo(CheckStatus.MDP_CHANGE_CHECKING.code());//审批中
        List<MilepostDesignPlanChangeRecord> changeRecordList = changeRecordMapper.selectByExample(changeRecordExample);
        List<Long> planChangeRecordIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(changeRecordList)) {
            for (MilepostDesignPlanChangeRecord planChangeRecord : changeRecordList) {
                planChangeRecordIds.add(planChangeRecord.getId());
            }
            //判断该工作流是否属于本项目
            for (WorkflowInfoVo workflowInfoVo : planChangeApps) {
                if (planChangeRecordIds.contains(workflowInfoVo.getFormInstanceId())) {
                    workflowInfoVoList.add(workflowInfoVo);
                }
            }
        }

        //进度确认
        List<WorkflowInfoVo> planConfirmApps = getWorkflowInfoByApp(workflowInfo, "milepostDesignPlanConfirmApp");
        //里程碑详细设计方案确认审批
        MilepostDesignPlanConfirmRecordExample confirmRecordExample = new MilepostDesignPlanConfirmRecordExample();
        confirmRecordExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andProjectIdEqualTo(project.getId()).andStatusEqualTo(CheckStatus.CHECKING.code());//审批中
        List<MilepostDesignPlanConfirmRecord> confirmRecordList =
                confirmRecordMapper.selectByExample(confirmRecordExample);
        List<Long> confirmRecordIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(confirmRecordList)) {
            for (MilepostDesignPlanConfirmRecord confirmRecord : confirmRecordList) {
                confirmRecordIds.add(confirmRecord.getId());
            }
            //判断该工作流是否属于本项目
            for (WorkflowInfoVo workflowInfoVo : planConfirmApps) {
                if (confirmRecordIds.contains(workflowInfoVo.getFormInstanceId())) {
                    workflowInfoVoList.add(workflowInfoVo);
                }
            }
        }

        returnVoList.addAll(workflowInfoToExcelVo(workflowInfoVoList));
        if (returnVoList.size() > 0) {
            excelVo.setPass(false);
        }
        excelVo.setReturnList(returnVoList);
        return excelVo;
    }

    /**
     * 项目外包采购是否存在审批中的流程(09)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectPurchaseItem(Project project, Map<String, Object> workflowInfo) {
        ProjectCheckExcelVo excelVo = new ProjectCheckExcelVo();
        if (ObjectUtils.isEmpty(project)) {
            return null;
        }
        String[] headers = {"序号", "主题", "创建人", "当前处理人", "创建时间", "状态"};
        excelVo.setHeaders(headers);
        excelVo.setDate(new Date());
        excelVo.setCheckName("项目外包采购是否存在审批中的流程");
        excelVo.setProjectCode(project.getCode());
        excelVo.setProjectName(project.getName());
        excelVo.setPass(Boolean.TRUE);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        List<WorkflowInfoVo> workflowInfoVoList = new ArrayList<>();//当前项目待审流程

        if ("-1".equals(workflowInfo.get("code") + "")) {
            ProjectCheckReturnVo pcr = new ProjectCheckReturnVo();
            pcr.setC0("1");
            pcr.setC1(workflowInfo.get("msg") + "");
            return excelVo;
        }

        //采购合同新增
        List<WorkflowInfoVo> contractApps = getWorkflowInfoByApp(workflowInfo, "purchaseContractApp");//采购合同审批
        PurchaseContractExample contractExample = new PurchaseContractExample();
        contractExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andProjectIdEqualTo(project.getId()).andStatusEqualTo(PurchaseContractStatus.PENDING.getCode());//审批中
        List<PurchaseContract> contractList = purchaseContractMapper.selectByExample(contractExample);
        List<Long> contractIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(contractList)) {
            for (PurchaseContract purchaseContract : contractList) {
                contractIds.add(purchaseContract.getId());
            }
            //判断该工作流是否属于本项目
            for (WorkflowInfoVo workflowInfoVo : contractApps) {
                if (contractIds.contains(workflowInfoVo.getFormInstanceId())) {
                    workflowInfoVoList.add(workflowInfoVo);
                }
            }
        }

        //todo 外包采购合同变更
        returnVoList.addAll(workflowInfoToExcelVo(workflowInfoVoList));
        if (returnVoList.size() > 0) {
            excelVo.setPass(false);
        }
        excelVo.setReturnList(returnVoList);
        return excelVo;
    }

    /**
     * 项目库存是否存在审批中的流程(10)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectStockApprovalItem(Project project, Map<String, Object> workflowInfo) {
        ProjectCheckExcelVo excelVo = new ProjectCheckExcelVo();
        if (ObjectUtils.isEmpty(project)) {
            return null;
        }
        String[] headers = {"序号", "主题", "创建人", "当前处理人", "创建时间", "状态"};
        excelVo.setHeaders(headers);
        excelVo.setDate(new Date());
        excelVo.setCheckName("项目库存是否存在审批中的流程");
        excelVo.setProjectCode(project.getCode());
        excelVo.setProjectName(project.getName());
        excelVo.setPass(Boolean.TRUE);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        List<WorkflowInfoVo> workflowInfoVoList = new ArrayList<>();//当前项目待审流程

        if ("-1".equals(workflowInfo.get("code") + "")) {
            ProjectCheckReturnVo pcr = new ProjectCheckReturnVo();
            pcr.setC0("1");
            pcr.setC1(workflowInfo.get("msg") + "");
            return excelVo;
        }

        List<WorkflowInfoVo> materialGetApps = getWorkflowInfoByApp(workflowInfo, "materialGetApp");//领料审批流程
        MaterialGetHeaderExample materialGetHeaderExample = new MaterialGetHeaderExample();
        materialGetHeaderExample.createCriteria().andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code())
                .andProjectIdEqualTo(project.getId()).andStatusEqualTo(MaterialGetStatus.APPROVING.code());//审批中
        List<MaterialGetHeader> materialGetHeaderList = getHeaderMapper.selectByExample(materialGetHeaderExample);
        List<Long> materialGetHeaderIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(materialGetHeaderList)) {
            for (MaterialGetHeader materialGetHeader : materialGetHeaderList) {
                materialGetHeaderIds.add(materialGetHeader.getId());
            }
            //判断该工作流是否属于本项目
            for (WorkflowInfoVo workflowInfoVo : materialGetApps) {
                if (materialGetHeaderIds.contains(workflowInfoVo.getFormInstanceId())) {
                    List<MaterialGetHeader> materialGetHeaderList1 =
                            materialGetHeaderList.stream().filter(m -> m.getId().equals(workflowInfoVo.getFormInstanceId())).collect(Collectors.toList());
                    workflowInfoVo.setFormCode(materialGetHeaderList1.get(0).getGetCode());
                    workflowInfoVoList.add(workflowInfoVo);
                }
            }
        }

        List<WorkflowInfoVo> materialReturnApps = getWorkflowInfoByApp(workflowInfo, "materialReturnApp");//退料审批流程
        MaterialReturnHeaderExample materialReturnHeaderExample = new MaterialReturnHeaderExample();
        materialReturnHeaderExample.createCriteria().andDeletedFlagEqualTo(Boolean.valueOf(String.valueOf(DeletedFlagEnum.VALID.code())))
                .andProjectIdEqualTo(project.getId()).andStatusEqualTo(MaterialGetStatus.APPROVING.code());//审批中
        List<MaterialReturnHeader> materialReturnHeaderList =
                returnHeaderMapper.selectByExample(materialReturnHeaderExample);
        List<Long> materialReturnHeaderIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(materialReturnHeaderList)) {
            for (MaterialReturnHeader materialReturnHeader : materialReturnHeaderList) {
                materialReturnHeaderIds.add(materialReturnHeader.getId());
            }
            //判断该工作流是否属于本项目
            for (WorkflowInfoVo workflowInfoVo : materialReturnApps) {
                if (materialReturnHeaderIds.contains(workflowInfoVo.getFormInstanceId())) {
                    List<MaterialReturnHeader> materialReturnHeaderList1 =
                            materialReturnHeaderList.stream().filter(m -> m.getId().equals(workflowInfoVo.getFormInstanceId())).collect(Collectors.toList());
                    workflowInfoVo.setFormCode(materialReturnHeaderList1.get(0).getReturnCode());
                    workflowInfoVoList.add(workflowInfoVo);
                }
            }
        }

        List<WorkflowInfoVo> materialTransferApps = getWorkflowInfoByApp(workflowInfo, "materialTransferApp");//子库转移流程
        MaterialTransferHeaderExample transferHeaderExample = new MaterialTransferHeaderExample();
        MaterialTransferHeaderExample.Criteria criteria = transferHeaderExample.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code()).andStatusEqualTo(MaterialTransferEnums.APPROVING.code())//审批中
                .andSendProjectIdEqualTo(project.getId());
        MaterialTransferHeaderExample.Criteria or = transferHeaderExample.or();
        or.andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code()).andStatusEqualTo(MaterialTransferEnums.APPROVING.code())//审批中
                .andReceiveProjectIdEqualTo(project.getId());
        List<MaterialTransferHeader> transferHeaderList = transferHeaderMapper.selectByExample(transferHeaderExample);
        List<Long> transferHeaderIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(transferHeaderList)) {
            for (MaterialTransferHeader transferHeader : transferHeaderList) {
                transferHeaderIds.add(transferHeader.getId());
            }
            //判断该工作流是否属于本项目
            for (WorkflowInfoVo workflowInfoVo : materialTransferApps) {
                if (transferHeaderIds.contains(workflowInfoVo.getFormInstanceId())) {
                    List<MaterialTransferHeader> transferHeaderList1 =
                            transferHeaderList.stream().filter(t -> t.getId().equals(workflowInfoVo.getFormInstanceId())).collect(Collectors.toList());
                    workflowInfoVo.setFormCode(transferHeaderList1.get(0).getTransferCode());
                    workflowInfoVoList.add(workflowInfoVo);
                }
            }
        }

        returnVoList.addAll(workflowInfoToExcelVo(workflowInfoVoList));
        if (returnVoList.size() > 0) {
            excelVo.setPass(false);
        }
        excelVo.setReturnList(returnVoList);
        return excelVo;
    }

    /**
     * 项目是否已完成详细设计方案的进度确认(11)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectPlanItem(Project project) {
        ProjectCheckExcelVo excelVo = new ProjectCheckExcelVo();
        if (ObjectUtils.isEmpty(project)) {
            return null;
        }
        String[] headers = {"序号", "物料编码", "物料描述", "单位", "数量", "状态"};
        excelVo.setHeaders(headers);
        excelVo.setDate(new Date());
        excelVo.setCheckName("项目是否已完成详细设计方案的进度确认");
        excelVo.setProjectCode(project.getCode());
        excelVo.setProjectName(project.getName());
        excelVo.setPass(Boolean.TRUE);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();

        //查询设计BOM的里程碑
        List<Long> milePostIds = new ArrayList<>();
        milePostIds.add(-1L);//防止空值
        ProjectMilepostExample milepostExample = new ProjectMilepostExample();
        milepostExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andProjectIdEqualTo(project.getId()).andAnnexTypeEqualTo(ProjectMilepostAnnexType.BOM.code());//设计bom
        List<ProjectMilepost> milepostList = projectMilepostMapper.selectByExample(milepostExample);
        for (ProjectMilepost projectMilepost : milepostList) {
            milePostIds.add(projectMilepost.getId());
        }
        //修复数据问题
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andProjectIdEqualTo(project.getId())
                .andDeletedFlagEqualTo(Boolean.FALSE).andStatusEqualTo(CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code())
                .andWhetherModelEqualTo(Boolean.TRUE).andModuleStatusNotEqualTo(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());
        //这个慢sql出现的原因是因为这里没有携带项目ID,导致全表扫描了
        long start = System.currentTimeMillis();
        List<MilepostDesignPlanDetail> approvedList = designPlanDetailMapper.selectByExample(example);
        long end = System.currentTimeMillis();
        logger.info("designPlanDetailMapper 查询耗时为:{}", end - start);
        if (ListUtils.isNotEmpty(approvedList)) {
            logger.info("*************** ProjectServiceImpl.checkProjectPlanItem入参:{} ***************", JSONObject.toJSONString(approvedList));
            for (MilepostDesignPlanDetail m : approvedList) {
                m.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());
                designPlanDetailMapper.updateByPrimaryKeySelective(m);
            }
        }
        //查询模组是否确认
        MilepostDesignPlanDetailExample designPlanDetailExample = new MilepostDesignPlanDetailExample();
        designPlanDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andMilepostIdIn(milePostIds)
                .andProjectIdEqualTo(project.getId()).andWhetherModelEqualTo(Boolean.TRUE)//模组
                .andModuleStatusNotEqualTo(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());//不是已确认
        List<MilepostDesignPlanDetail> designPlanDetailList =
                designPlanDetailMapper.selectByExample(designPlanDetailExample);
        if (!CollectionUtils.isEmpty(designPlanDetailList)) {
            excelVo.setPass(Boolean.FALSE);
            for (int i = 0; i < designPlanDetailList.size(); i++) {
                MilepostDesignPlanDetail designPlanDetail = designPlanDetailList.get(i);
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0(Integer.toString(i + 1));
                returnVo.setC1(designPlanDetail.getPamCode());
                returnVo.setC2(designPlanDetail.getMaterielDescr());
                returnVo.setC3(designPlanDetail.getUnit());
                returnVo.setC4(designPlanDetail.getNumber().toString());
                returnVo.setC5(EnumUtil.getByCode(designPlanDetail.getModuleStatus(),
                        MilepostDesignPlanDetailModelStatus.class));
                returnVoList.add(returnVo);
            }
        }
        excelVo.setReturnList(returnVoList);
        return excelVo;
    }

    /**
     * 项目详细设计方案的采购物料是否已全部生成采购需求(12)
     * 检查项目详细设计方案中的采购物料是否已全部生成采购需求（不包含成品、模组、外包物料）
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectMaterielItem(Project project) {
        ProjectCheckExcelVo excelVo = new ProjectCheckExcelVo();
        if (ObjectUtils.isEmpty(project)) {
            return null;
        }
        String[] headers = {"序号", "物料编码", "ERP物料编码", "物料描述", "单位", "数量"};
        excelVo.setHeaders(headers);
        excelVo.setDate(new Date());
        excelVo.setCheckName("项目详细设计方案的采购物料是否已全部生成采购需求");
        excelVo.setProjectCode(project.getCode());
        excelVo.setProjectName(project.getName());
        excelVo.setPass(Boolean.TRUE);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();

        //检查项目详细设计方案中的采购物料是否已全部生成采购需求（不包含成品、模组、外包物料）
        List<MilepostDesignPlanDetail> designPlanDetailList =
                milepostDesignPlanDetailExtMapper.getNoGenerateRequirement(project.getId());
        if (!CollectionUtils.isEmpty(designPlanDetailList)) {
            excelVo.setPass(Boolean.FALSE);
            for (int i = 0; i < designPlanDetailList.size(); i++) {
                MilepostDesignPlanDetail designPlanDetail = designPlanDetailList.get(i);
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0(Integer.toString(i + 1));
                returnVo.setC1(designPlanDetail.getPamCode());
                returnVo.setC2(designPlanDetail.getErpCode());
                returnVo.setC3(designPlanDetail.getMaterielDescr());
                returnVo.setC4(designPlanDetail.getUnit());
                returnVo.setC5(designPlanDetail.getNumber().toString());
                returnVoList.add(returnVo);
            }
        }
        excelVo.setReturnList(returnVoList);
        return excelVo;
    }

    /**
     * 项目采购需求是否已全部生成采购订单(13)
     * 检查项目生成的采购需求是否已全部同步GERP生成采购订单
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectRequirementItem(Project project) {
        ProjectCheckExcelVo excelVo = new ProjectCheckExcelVo();
        if (ObjectUtils.isEmpty(project)) {
            return null;
        }
        String[] headers = {"序号", "物料编码", "ERP物料编码", "物料描述", "单位", "需求数量", "已下单量", "状态"};
        excelVo.setHeaders(headers);
        excelVo.setDate(new Date());
        excelVo.setCheckName("项目采购需求是否已全部生成采购订单");
        excelVo.setProjectCode(project.getCode());
        excelVo.setProjectName(project.getName());
        excelVo.setPass(Boolean.TRUE);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();

        //检查项目生成的采购需求是否已全部同步GERP生成采购订单
        List<Integer> statusList = new ArrayList<>();
        statusList.add(0);
        statusList.add(1);//待下达/已下达
        PurchaseMaterialRequirementDto requirementQuery = new PurchaseMaterialRequirementDto();
        requirementQuery.setProjectId(project.getId());
        requirementQuery.setStatusList(statusList);
        List<PurchaseMaterialRequirementDto> requirementDtoList =
                requirementService.selectListWithDetail(requirementQuery);
        if (!CollectionUtils.isEmpty(requirementDtoList)) {
            excelVo.setPass(Boolean.FALSE);
            for (int i = 0; i < requirementDtoList.size(); i++) {
                PurchaseMaterialRequirementDto requirementDto = requirementDtoList.get(i);
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0(Integer.toString(i + 1));
                returnVo.setC1(requirementDto.getPamCode());
                returnVo.setC2(requirementDto.getErpCode());
                returnVo.setC3(requirementDto.getMaterielDescr());
                returnVo.setC4(requirementDto.getUnit());
                returnVo.setC5(requirementDto.getNeedTotal().toString());
                returnVo.setC6(requirementDto.getOrderQuantity().toString());
                returnVo.setC7(EnumUtil.getByCode(requirementDto.getStatus(), PurchaseMaterialRequirementStatus.class));
                returnVoList.add(returnVo);
            }
        }

        excelVo.setReturnList(returnVoList);
        return excelVo;
    }

    /**
     * 项目采购订单物料是否已全部接收入库(14)
     * 检查项目生成的采购需求是否已全部同步GERP生成采购订单
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectPurchaseOrderItem(Project project) {
        ProjectCheckExcelVo excelVo = new ProjectCheckExcelVo();
        if (ObjectUtils.isEmpty(project)) {
            return null;
        }
        String[] headers = {"序号", "采购订单编号", "行号", "ERP物料编码", "物料描述", "单位", "订单数量", "已入库数量", "订单状态"};
        excelVo.setHeaders(headers);
        excelVo.setDate(new Date());
        excelVo.setCheckName("项目采购订单物料是否已全部接收入库");
        excelVo.setProjectCode(project.getCode());
        excelVo.setProjectName(project.getName());
        excelVo.setPass(Boolean.TRUE);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();

        //检查项目生成的采购需求是否已全部同步GERP生成采购订单
        PurchaseOrderDetailExample orderDetailExample = new PurchaseOrderDetailExample();
        List<Integer> statusList = new ArrayList<>();
        statusList.add(1);
        statusList.add(2);//已下达/已下单
        orderDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andStatusIn(statusList)
                .andProjectIdEqualTo(project.getId());
        List<PurchaseOrderDetail> orderDetailList = orderDetailMapper.selectByExample(orderDetailExample);
        if (!CollectionUtils.isEmpty(orderDetailList)) {
            excelVo.setPass(Boolean.FALSE);
            for (int i = 0; i < orderDetailList.size(); i++) {
                PurchaseOrderDetail purchaseOrderDetail = orderDetailList.get(i);
                PurchaseOrder purchaseOrder =
                        purchaseOrderMapper.selectByPrimaryKey(purchaseOrderDetail.getPurchaseOrderId());
                /**存在有订单详细信息,此时还没有生成订单行的信息请求,因此碰到到这种情况直接过滤掉 */
                if (purchaseOrder == null) {
                    logger.info("订单行信息为空,跳出当前循环,当前的订单详细信息id为:{}", purchaseOrderDetail.getId());
                    continue;
                }
                if (Objects.equals(purchaseOrder.getOrderStatus(), PurchaseOrderStatus.ABANDON.code())) {
                    logger.info("订单已作废,跳出当前循环,当前的订单id为:{}", purchaseOrder.getId());
                    continue;
                }
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0(Integer.toString(i + 1));
                returnVo.setC1(purchaseOrder.getNum());
                if (null != purchaseOrderDetail.getLineNumber()) {
                    returnVo.setC2(Integer.toString(purchaseOrderDetail.getLineNumber()));
                }
                returnVo.setC3(purchaseOrderDetail.getErpCode());
                returnVo.setC4(purchaseOrderDetail.getMaterielDescr());
                returnVo.setC5(purchaseOrderDetail.getUnit());
                returnVo.setC6(purchaseOrderDetail.getOrderNum().toString());
                returnVo.setC7(purchaseOrderDetail.getOrderNum().toString());
                returnVo.setC8(EnumUtil.getByCode(purchaseOrderDetail.getStatus(), PurchaseOrderDetailStatus.class));
                returnVoList.add(returnVo);
            }
        }
        excelVo.setReturnList(returnVoList);
        return excelVo;
    }

    /**
     * 项目子库的库存是否为0(15)
     * <p>
     * a) 如果是费用仓（asset_inventory）：要求除了项目现场仓（type=07）以外的子库，数量必须=0
     * b) 如果是资产仓（asset_inventory）：数量都必须=0
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectStockItem(Project project) {
        String[] headers = {"序号", "子库", "子库描述", "货位", "货位描述", "ERP物料编码", "物料描述", "单位", "现有量"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();

        List<Storage> storageList = basedataExtService.getNotNullByProCode(project.getCode(), project.getOuId());
        if (!CollectionUtils.isEmpty(storageList)) {
            for (int i = 0; i < storageList.size(); i++) {
                Storage storage = storageList.get(i);
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0(Integer.toString(i + 1));
                returnVo.setC1(storage.getSubinventoryCode());
                returnVo.setC2(storage.getSubinventoryDescription());
                returnVo.setC3(storage.getLocator());
                returnVo.setC4(storage.getLocatorDescription());
                returnVo.setC5(storage.getSegment1());
                returnVo.setC6(storage.getDescription());
                returnVo.setC7(storage.getPrimaryUnitOfMeasure());
                if (null != storage.getTransactionQuantity()) {
                    returnVo.setC8(storage.getTransactionQuantity().toString());
                }
                returnVoList.add(returnVo);
            }
        }
        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    /**
     * 项目库存是否存在未处理完的单据(16)
     * 检查项目关联的领料单、退料单、转移单是否存在草稿、待处理状态的单据，领料单检查领料项目，退料单检查退料项目，转移单检查发料项目和收料项目
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectStorageItem(Project project) {
        String[] headers = {"序号", "单据", "单据编号", "单据状态", "制单人", "申请人", "申请数量", "申请日期", "领料/退料/发料项目编号", "领料/退料/发料项目名称",
                "收料项目编号", "收料项目名称"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        int num = 1;//导出明细计数

        //检查项目关联的领料单
        List<Integer> getStatus = new ArrayList<>();
        getStatus.add(MaterialGetStatus.DRAFT.code());//草稿
        getStatus.add(MaterialGetStatus.PENDING.code());//待处理
        MaterialGetHeaderExample materialGetHeaderExample = new MaterialGetHeaderExample();
        materialGetHeaderExample.createCriteria().andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code())
                .andProjectIdEqualTo(project.getId()).andStatusIn(getStatus);
        List<MaterialGetHeader> materialGetHeaderList = getHeaderMapper.selectByExample(materialGetHeaderExample);
        if (!CollectionUtils.isEmpty(materialGetHeaderList)) {
            projectCheckExcelVo.setPass(Boolean.FALSE);
            for (int i = 0; i < materialGetHeaderList.size(); i++) {
                MaterialGetHeader materialGetHeader = materialGetHeaderList.get(i);
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0("" + num++);
                returnVo.setC1("领料单");
                returnVo.setC2(materialGetHeader.getGetCode());
                String statusStr = com.midea.pam.common.util.EnumUtil.getByCode(materialGetHeader.getStatus(),
                        MaterialGetStatus.class);
                returnVo.setC3(statusStr);
                returnVo.setC4(materialGetHeader.getFillUserName());
                UserInfo user = CacheDataUtils.findUserById(materialGetHeader.getCreateBy());
                if (user != null) {
                    returnVo.setC5(user.getName());
                }
                if (null != materialGetHeader.getTotalApplyAmount()) {
                    returnVo.setC6(materialGetHeader.getTotalApplyAmount().toString());
                }
                returnVo.setC7(DateUtil.format(materialGetHeader.getCreateAt(), DateUtil.TIMESTAMP_PATTERN));
                returnVo.setC8(materialGetHeader.getProjectCode());
                returnVo.setC9(materialGetHeader.getProjectName());
                returnVo.setC10("");
                returnVo.setC11("");
                returnVoList.add(returnVo);
            }
        }

        //检查项目关联的退料单
        MaterialReturnHeaderExample materialReturnHeaderExample = new MaterialReturnHeaderExample();
        materialReturnHeaderExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andProjectIdEqualTo(project.getId()).andStatusIn(getStatus);
        List<MaterialReturnHeader> materialReturnHeaderList =
                returnHeaderMapper.selectByExample(materialReturnHeaderExample);
        if (!CollectionUtils.isEmpty(materialReturnHeaderList)) {
            projectCheckExcelVo.setPass(Boolean.FALSE);
            for (int i = 0; i < materialReturnHeaderList.size(); i++) {
                MaterialReturnHeader materialReturnHeader = materialReturnHeaderList.get(i);
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0("" + num++);
                returnVo.setC1("退料单");
                returnVo.setC2(materialReturnHeader.getReturnCode());
                String statusStr = com.midea.pam.common.util.EnumUtil.getByCode(materialReturnHeader.getStatus(),
                        MaterialGetStatus.class);
                returnVo.setC3(statusStr);
                returnVo.setC4(materialReturnHeader.getFillUserName());
                UserInfo user = CacheDataUtils.findUserById(materialReturnHeader.getCreateBy());
                if (user != null) {
                    returnVo.setC5(user.getName());
                }
                if (null != materialReturnHeader.getTotalApplyAmount()) {
                    returnVo.setC6(materialReturnHeader.getTotalApplyAmount().toString());
                }
                returnVo.setC7(DateUtil.format(materialReturnHeader.getCreateAt(), DateUtil.TIMESTAMP_PATTERN));
                returnVo.setC8(materialReturnHeader.getProjectCode());
                returnVo.setC9(materialReturnHeader.getProjectName());
                returnVo.setC10("");
                returnVo.setC11("");
                returnVoList.add(returnVo);
            }
        }

        //检查项目关联的转移单
        List<Integer> transferStatus = new ArrayList<>();
        transferStatus.add(MaterialTransferEnums.DRAFT.code());//草稿
        transferStatus.add(MaterialTransferEnums.PENDING.code());//待处理
        MaterialTransferHeaderExample transferHeaderExample = new MaterialTransferHeaderExample();
        MaterialTransferHeaderExample.Criteria criteria = transferHeaderExample.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code()).andStatusIn(transferStatus)
                .andSendProjectIdEqualTo(project.getId());
        MaterialTransferHeaderExample.Criteria or = transferHeaderExample.or();
        or.andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code()).andStatusIn(transferStatus)
                .andReceiveProjectIdEqualTo(project.getId());
        List<MaterialTransferHeader> transferHeaderList = transferHeaderMapper.selectByExample(transferHeaderExample);
        if (!CollectionUtils.isEmpty(transferHeaderList)) {
            projectCheckExcelVo.setPass(Boolean.FALSE);
            for (int i = 0; i < transferHeaderList.size(); i++) {
                MaterialTransferHeader transferHeader = transferHeaderList.get(i);
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0("" + num++);
                returnVo.setC1("转移单");
                returnVo.setC2(transferHeader.getTransferCode());
                String statusStr = com.midea.pam.common.util.EnumUtil.getByCode(transferHeader.getStatus(),
                        MaterialTransferEnums.class);
                returnVo.setC3(statusStr);
                returnVo.setC4(transferHeader.getFillUserName());
                UserInfo user = CacheDataUtils.findUserById(transferHeader.getCreateBy());
                if (user != null) {
                    returnVo.setC5(user.getName());
                }
                //汇总转移单明细表申请数量
                MaterialTransferDetailExample transferDetailExample = new MaterialTransferDetailExample();
                transferDetailExample.createCriteria().andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code())
                        .andHeaderIdEqualTo(transferHeader.getId());
                List<MaterialTransferDetail> transferDetailList =
                        transferDetailMapper.selectByExample(transferDetailExample);
                BigDecimal totalApplyAmount = BigDecimal.ZERO;
                for (MaterialTransferDetail transferDetail : transferDetailList) {
                    totalApplyAmount = totalApplyAmount.add(transferDetail.getApplyAmount());
                }
                returnVo.setC6(totalApplyAmount.toString());
                returnVo.setC7(DateUtil.format(transferHeader.getCreateAt(), DateUtil.TIMESTAMP_PATTERN));
                returnVo.setC8(transferHeader.getSendProjectCode());
                returnVo.setC9(transferHeader.getSendProjectName());
                returnVo.setC10(transferHeader.getReceiveProjectCode());
                returnVo.setC11(transferHeader.getReceiveProjectName());
                returnVoList.add(returnVo);
            }
        }

        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    /**
     * 项目关联采购合同是否完全开票(27)
     * 1.检查项目关联的采购合同的合同总额(含税)是否小于等于采购合同已入账的发票 + 采购合同下所有状态为：生效（支付中）、支付成功的开票申请下的罚扣金额（含税） + 采购合同下所有状态为：生效的采购合同罚扣的金额（含税）
     * 2.小于等于则为完全开票
     * 3.大于则不为完全开票，检查不通过
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectPurchaseInvoiceItem(Project project) {
        String[] headers = {"序号", "采购合同编号", "采购合同名称", "合同总额（含税）", "已入账发票金额（含税）", "付款申请罚扣金额（含税）", "采购合同罚扣金额（含税）"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        int num = 1;//导出明细计数

        //检查项目关联采购合同是否完全开票
        List<PurchaseContractDTO> purchaseContractDTOList =
                purchaseContractExtMapper.sumProjectPurchaseInvoiceItem(project.getId());
        if (!CollectionUtils.isEmpty(purchaseContractDTOList)) {
            projectCheckExcelVo.setPass(Boolean.FALSE);
            for (int i = 0; i < purchaseContractDTOList.size(); i++) {
                PurchaseContractDTO purchaseContractDTO = purchaseContractDTOList.get(i);
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0("" + num++);
                returnVo.setC1(purchaseContractDTO.getCode());
                returnVo.setC2(purchaseContractDTO.getName());
                returnVo.setC3("" + purchaseContractDTO.getAmount());
                returnVo.setC4("" + purchaseContractDTO.getPaymentInvoiceAmount());
                returnVo.setC5("" + purchaseContractDTO.getPenaltyAmount());
                returnVo.setC6(BigDecimalUtils.scaleAndToString(purchaseContractDTO.getPunishmentAmount()));

                returnVoList.add(returnVo);
            }
        }

        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    /**
     * 销售金额与合同金额（含税）是否一致(28)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkContractInvoice(Project project) {
        String[] headers = {"序号", "子合同编号", "子合同名称", "合同金额（含税）", "质保款金额（含税）", "开票总金额（含税）", "差异金额"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        ProjectCheckReturnVo returnVo = getCheckexcelInvoice(project);
        if (!ObjectUtils.isEmpty(returnVo)) {
            String remark = "警告，实际开票金额：" + returnVo.getC5() + "，合同金额：" + returnVo.getC3()
                    + "，币种：" + returnVo.getC7();
            projectCheckExcelVo.setRemark(remark);
            returnVoList.add(returnVo);
            projectCheckExcelVo.setReturnList(returnVoList);
        }
        return projectCheckExcelVo;
    }

    /**
     * 合同是否有开票金额（17）
     * 检查项目关联的合同是否有过开票
     *
     * @param project
     * @return
     */
    @Override
    public ProjectCheckExcelVo chckProjectContractInvoiceItem(Project project) {

        String[] headers = {"序号", "应收发票号", "应收发票状态", "发票金额", "开票申请号", "子合同编号", "子合同名称"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList =
                projectCheckReturnVoMapper.chckProjectContractInvoiceItem(project.getId());
        List<ProjectContractRs> projectContractRsList = projectContractRsService.selectByProjectId(project.getId());
        Boolean isInvoice = Boolean.FALSE;
        if (ListUtils.isNotEmpty(projectContractRsList)) {
            ProjectContractRs projectContractRs = projectContractRsList.get(0);
            Long contractId = projectContractRs.getContractId();
            isInvoice = isInvoice(contractId);
        }
        int i = 0;
        if (ListUtils.isNotEmpty(returnVoList) && isInvoice) {
            for (ProjectCheckReturnVo projectCheckReturnVo : returnVoList) {
                projectCheckReturnVo.setC0("" + i);
                i++;
            }
            projectCheckExcelVo.setReturnList(returnVoList);
        }

        return projectCheckExcelVo;
    }

    /**
     * 合同是否有回款(18)
     * 检查项目关联的合同是否有过回款
     *
     * @param project
     * @return
     */
    @Override
    public ProjectCheckExcelVo chckProjectContractReceiptItem(Project project) {
        String[] headers = {"序号", "收款/退款编号", "资金单据号", "分配至合同金额/退款金额", "开票计划编号", "子合同编号", "子合同名称", "类型"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = projectCheckReturnVoMapper.chckProjectContractReceiptItem(project.getId());
        int i = 0;
        if (ListUtils.isNotEmpty(returnVoList)) {
            //判断回款-退款金额是否等于0
            BigDecimal reduce = returnVoList.stream()
                    .map(s -> "收款".equals(s.getC7()) ? new BigDecimal(s.getC3()) : new BigDecimal(s.getC3()).negate())
                    .reduce(BigDecimal.ZERO, BigDecimalUtils::add);
            if (reduce.compareTo(BigDecimal.ZERO) == 0) {
                return projectCheckExcelVo;
            }
            for (ProjectCheckReturnVo projectCheckReturnVo : returnVoList) {
                projectCheckReturnVo.setC0("" + i);
                i++;
            }
        }
        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    /**
     * 项目是否有结转单(19)
     * 检查项目是否有结转单
     *
     * @param project
     * @return
     */
    @Override
    public ProjectCheckExcelVo chckProjectCarryBillItem(Project project) {
        String[] headers = {"序号", "结转单编号", "会计期间", "本期确认收入总额", "本期确认成本总额", "冲销状态", "创建日期"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList =
                projectCheckReturnVoMapper.chckProjectContractCarryBillItem(project.getId());
        int i = 0;
        if (ListUtils.isNotEmpty(returnVoList)) {
            for (ProjectCheckReturnVo projectCheckReturnVo : returnVoList) {
                projectCheckReturnVo.setC0("" + i);
                i++;
            }
        }
        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    @Override
    public ProjectCheckExcelVo chckProjectWorkingHourCost(Project project) {
        String[] headers = {"序号", "姓名", "MIP账号", "出勤日期", "工时（H）", "状态", "RDM导入"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList =
                projectCheckReturnVoMapper.chckProjectWorkingHourCost(project.getId());
        int i = 0;
        if (ListUtils.isNotEmpty(returnVoList)) {
            BigDecimal costMoney = BigDecimal.ZERO;
            BigDecimal actualWorkingHours = BigDecimal.ZERO;
            for (ProjectCheckReturnVo projectCheckReturnVo : returnVoList) {
                projectCheckReturnVo.setC0("" + i);
                final UserInfo userInfo = CacheDataUtils.findUserById(Long.valueOf(projectCheckReturnVo.getC1()));
                if (userInfo != null) {
                    projectCheckReturnVo.setC1(userInfo.getName());
                }
                i++;
                final String actualWorkingHourStr = projectCheckReturnVo.getC4();
                final String actualCostMoneyStr = projectCheckReturnVo.getC7();
                if (StringUtils.isNotEmpty(actualCostMoneyStr)) {
                    BigDecimal actualCostMoney = new BigDecimal(actualCostMoneyStr);

                    costMoney = costMoney.add(actualCostMoney);

                }
                if (StringUtils.isNotEmpty(actualWorkingHourStr)) {
                    BigDecimal actualWorkingHour = new BigDecimal(actualWorkingHourStr);
                    actualWorkingHours = actualWorkingHours.add(actualWorkingHour);
                }
            }

            projectCheckExcelVo.setRemark("不通过。审批通过工时数：" + actualWorkingHours.stripTrailingZeros().toPlainString() +
                    "(H)。");
        }
        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    @Override
    public ProjectCheckExcelVo chckProjectFeeCost(Project project) {
        String[] headers = {"序号", "费用类型", "已发生费用金额", "币种", "费用发生日期", "状态"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = projectCheckReturnVoMapper.chckProjectFeeCost(project.getId());
        int i = 0;
        if (ListUtils.isNotEmpty(returnVoList)) {
            BigDecimal costAmount = BigDecimal.ZERO;
            for (ProjectCheckReturnVo projectCheckReturnVo : returnVoList) {
                projectCheckReturnVo.setC0("" + i);
                i++;
                final String amountStr = projectCheckReturnVo.getC2();
                if (StringUtils.isNotEmpty(amountStr)) {
                    BigDecimal amount = new BigDecimal(amountStr);
                    costAmount = costAmount.add(amount);
                }
            }
            // added at 202011261022 added by hongfang bug:BUG2020112667677 项目终止时没有费用、或者费用发成合计为0时都视作此项目没有费用发生
            if (BigDecimalUtils.isEquals(costAmount, BigDecimal.ZERO)) {
                logger.info("项目终止时没有费用、或者费用发成合计为0时都视作此项目没有费用发生!");
            } else {
                projectCheckExcelVo.setRemark("不通过。已发生费用成本：" + costAmount.stripTrailingZeros().toPlainString() +
                        "，币种：CNY");
                projectCheckExcelVo.setReturnList(returnVoList);
            }
        }

        return projectCheckExcelVo;
    }

    @Override
    public ProjectCheckExcelVo chckProjectIncomeCarry(Project project) {
        String[] headers = {"序号", "收入节点", "结转状态", "确认收入比例（%）", "确认收入金额", "确认成本金额"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = projectCheckReturnVoMapper.chckProjectIncomeCarry(project.getId());
        int i = 0;
        if (ListUtils.isNotEmpty(returnVoList)) {
            BigDecimal currentIncomeAmountTotal = BigDecimal.ZERO;
            for (ProjectCheckReturnVo projectCheckReturnVo : returnVoList) {
                projectCheckReturnVo.setC0("" + i);
                i++;
                final String currentIncomeAmountStr = projectCheckReturnVo.getC4();
                if (StringUtils.isNotEmpty(currentIncomeAmountStr)) {
                    BigDecimal currentIncomeAmount = new BigDecimal(currentIncomeAmountStr);
                    currentIncomeAmountTotal = currentIncomeAmountTotal.add(currentIncomeAmount);
                }

            }
            projectCheckExcelVo.setRemark("不通过。已结转收入：" + currentIncomeAmountTotal.stripTrailingZeros().toPlainString() + "，币种：CNY");
        }
        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    @Override
    public ProjectCheckExcelVo chckProjectRelContract(Project project) {
        String[] headers = {"序号", "子合同编号", "子合同名称", "合同金额（含税）", "币种", "状态"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = projectCheckReturnVoMapper.chckProjectRelContract(project.getId());
        int i = 0;
        if (ListUtils.isNotEmpty(returnVoList)) {
            for (ProjectCheckReturnVo projectCheckReturnVo : returnVoList) {
                projectCheckReturnVo.setC0("" + i);
                i++;
            }
            projectCheckExcelVo.setRemark("警告：项目关联合同" + returnVoList.get(0).getC1() + ",项目废弃将放弃收入确认");
        }
        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    /**
     * 项目是否存在未开票的工时
     *
     * @param project
     * @return
     */
    @Override
    public ProjectCheckExcelVo checkProjectUninvoiceWorking(Project project) {
        String[] headers = {"序号", "项目编号", "项目名称", "MIP账号", "姓名", "出勤日期", "填报工时", "状态", "工时审批通过日期"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList =
                projectCheckReturnVoMapper.checkProjectUninvoiceWorking(project.getId());

        int i = 0;
        if (ListUtils.isNotEmpty(returnVoList)) {
            for (ProjectCheckReturnVo projectCheckReturnVo : returnVoList) {
                projectCheckReturnVo.setC0("" + i);
                final UserInfo userInfo = CacheDataUtils.findUserById(Long.valueOf(projectCheckReturnVo.getC4()));
                if (userInfo != null) {
                    projectCheckReturnVo.setC4(userInfo.getName());
                }
                i++;
            }
            projectCheckExcelVo.setRemark("警告：请项目经理及时跟进开票");
        }
        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    @Override
    public String download(Long id) {
        return fileInfoMapper.selectByPrimaryKey(id).getDocId();
    }


    private Map<String, Integer> getNotPassWarningCount(List<DeliveryInspectionDetails> deliveryInspectionList) {
        Map<String, Integer> result = new HashMap<String, Integer>();
        Integer notPass = 0;
        Integer warning = 0;
        if (deliveryInspectionList != null) {
            for (DeliveryInspectionDetails deliveryInspectionDetails : deliveryInspectionList) {
                //检查状态(1通过 2不通过 3警告)
                if (deliveryInspectionDetails.getStatus() == 2) {
                    notPass = notPass + 1;
                } else if (deliveryInspectionDetails.getStatus() == 3) {
                    warning = warning + 1;
                }
            }
        }
        result.put("notPass", notPass);
        result.put("warning", warning);
        return result;
    }

    public ProjectCheckExcelVo checkProjectContractInvoice(Project project) {

        String[] headers = {"序号", "应收发票号", "应收发票状态", "发票金额", "开票申请号", "子合同编号", "子合同名称"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = projectCheckReturnVoMapper.checkProjectContractInvoice(project.getId());
        List<ProjectContractRs> projectContractRsList = projectContractRsService.selectByProjectId(project.getId());

        Boolean isInvoice = Boolean.FALSE;
        if (ListUtils.isNotEmpty(projectContractRsList)) {
            ProjectContractRs projectContractRs = projectContractRsList.get(0);
            Long contractId = projectContractRs.getContractId();
            isInvoice = isInvoicePrice(contractId);
        }
        int i = 0;
        if (ListUtils.isNotEmpty(returnVoList) && isInvoice) {
            for (ProjectCheckReturnVo projectCheckReturnVo : returnVoList) {
                projectCheckReturnVo.setC0("" + i);
                i++;
            }
            projectCheckExcelVo.setReturnList(returnVoList);
        }

        return projectCheckExcelVo;
    }

    @Override
    public CheckStatusDto deliver(Long projectId, Long milepostId) {
        if (projectId == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_ID_NULL);
        }
        if (milepostId == null) {
            throw new BizException(ErrorCode.CTC_MILEPOST_ID_NULL);
        }
        //项目
        Project project = this.projectMapper.selectByPrimaryKey(projectId);
        if (project == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_NOT_FIND);
        }
        Long projectTypeId = project.getType();//项目类型ID
        if (projectTypeId == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_TYPE_NOT_NULL);
        }
        CheckStatusDto result = new CheckStatusDto();
        //检查条目
        List<ProjectTypeCheckRelDto> projectTypeCheckRelDtoList =
                projectTypeCheckRelExtMapper.selectProjectTypeCheckRel(projectTypeId);
        if (projectTypeCheckRelDtoList == null || projectTypeCheckRelDtoList.size() == 0) {
            result.setCheck(false);
            return result;
        } else {
            result.setCheck(true);
            result.setTotalCount(projectTypeCheckRelDtoList.size());
        }

        DeliveryInspectionExample example = new DeliveryInspectionExample();
        DeliveryInspectionExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andMilepostIdEqualTo(milepostId);
        example.setOrderByClause("id desc");
        //展示有问题
        List<DeliveryInspection> deliveryInspectionList = deliveryInspectionMapper.selectByExample(example);

        //有交付检查任务
        if (deliveryInspectionList != null && deliveryInspectionList.size() != 0) {
            DeliveryInspection deliveryInspection = deliveryInspectionList.get(0);
            Long deliveryInspectionId = deliveryInspection.getId();
            result.setDeliveryInspectionId(deliveryInspectionId);//交付检查任务ID
            //交付检查(1未开始,2进行中 3通过 4不通过 5警告)
            Integer status = deliveryInspection.getStatus();
            result.setStatus(status);
            result.setDeliveryInspectionRemarks(deliveryInspection.getRemarks());
            result.setLastDateTime(deliveryInspection.getUpdateAt());
            CtcAttachmentExample ctcAttachmentExample = new CtcAttachmentExample();
            CtcAttachmentExample.Criteria ctcAttachmentExampleCriteria1 = ctcAttachmentExample.createCriteria();
            ctcAttachmentExampleCriteria1.andModuleEqualTo(CtcAttachmentModule.PROJECT_CHECK_ITEM.code());
            ctcAttachmentExampleCriteria1.andDeletedFlagEqualTo(false);
            ctcAttachmentExampleCriteria1.andModuleIdEqualTo(deliveryInspection.getId());
            List<CtcAttachment> attachments = ctcAttachmentMapper.selectByExample(ctcAttachmentExample);
            if (attachments != null && attachments.size() != 0) {
                result.setAttamentId(attachments.get(0).getAttachId());
            }
            //交付检查处理状态
            if (status == 3 || status == 4 || status == 5) {
                try {
                    Date updateAt = deliveryInspection.getUpdateAt();
                    Date currentTime = new Date();
                    Long milis = DateUtil.dateDiff(updateAt, currentTime);
                    //如果检查时间完成时间超过5分钟，设置超时
                    if (milis >= 5) {
                        result.setTimeout(true);
                    } else {
                        result.setTimeout(false);
                    }
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
                //如果不通过或者警告需要把详细信息展示出来
                if (status == 4 || status == 5) {
                    DeliveryInspectionDetailsExample example1 = new DeliveryInspectionDetailsExample();
                    DeliveryInspectionDetailsExample.Criteria criteria1 = example1.createCriteria();
                    criteria1.andDeliveryInspectionIdEqualTo(deliveryInspectionId);
                    List<DeliveryInspectionDetails> deliveryInspectionDetailsList =
                            deliveryInspectionDetailsMapper.selectByExample(example1);
                    logger.info("deliveryInspectionDetailsList ==== {}",
                            JSONObject.toJSONString(deliveryInspectionDetailsList));
                    if (deliveryInspectionDetailsList != null && deliveryInspectionDetailsList.size() != 0) {
                        Map<String, Integer> map = getNotPassWarningCount(deliveryInspectionDetailsList);
                        //不通过数量
                        result.setNotPassCount(map.get("notPass"));
                        //警告数量
                        result.setWarningCount(map.get("warning"));

                        List<DeliveryInspectionDetailsDto> list = BeanConverter.convert(deliveryInspectionDetailsList
                                , DeliveryInspectionDetailsDto.class);
                        for (DeliveryInspectionDetailsDto deliveryInspectionDetailsDto : list) {
                            Long checkItemId = deliveryInspectionDetailsDto.getCheckItemId();
                            if (checkItemId != null) {
                                CheckItem checkItem = checkItemMapper.selectByPrimaryKey(checkItemId);
                                if (checkItem != null) {
                                    //检查项CODE
                                    deliveryInspectionDetailsDto.setCheckCode(checkItem.getCheckCode());
                                    deliveryInspectionDetailsDto.setCheckDesc(checkItem.getCheckDesc());
                                    deliveryInspectionDetailsDto.setCheckDisplayName(checkItem.getCheckDisplayName());
                                    deliveryInspectionDetailsDto.setEndDate(checkItem.getEndDate());
                                    deliveryInspectionDetailsDto.setStartDate(checkItem.getStartDate());
                                }
                            }
                        }
                        result.setList(list);
                    }
                }
            }
            return result;
        } else {
            result.setStatus(1);
            return result;
        }

    }

    @Override
    public TerminationCheckStatusDTO termination(Long terminationInspectionId, Long terminationTypeId, Long unitId) {
        TerminationCheckStatusDTO terminationCheckStatusDTO = new TerminationCheckStatusDTO();
        terminationCheckStatusDTO.setTerminationInspectionId(terminationInspectionId);
        TerminationInspection terminationInspection =
                terminationInspectionMapper.selectByPrimaryKey(terminationInspectionId);

        terminationCheckStatusDTO.setTerminationInspectionId(terminationInspectionId);
        terminationCheckStatusDTO.setLastDateTime(terminationInspection.getCreateAt());
        //项目检查关联表
        ProjectTerminationCheckRelExample projectTerminationCheckRelExample = new ProjectTerminationCheckRelExample();
        projectTerminationCheckRelExample.createCriteria().andTerminationTypeIdEqualTo(terminationTypeId).andUnitIdEqualTo(unitId);
        List<ProjectTerminationCheckRel> projectTerminationCheckRelList =
                projectTerminationCheckRelMapper.selectByExample(projectTerminationCheckRelExample);
        if (ListUtils.isNotEmpty(projectTerminationCheckRelList)) {
            terminationCheckStatusDTO.setTotalCount(projectTerminationCheckRelList.size());
        }

        //检查结果明细信息
        TerminationInspectionDetailsExample inspectionDetailsExample = new TerminationInspectionDetailsExample();
        inspectionDetailsExample.createCriteria().andTerminationInspectionIdEqualTo(terminationInspectionId);
        List<TerminationInspectionDetails> terminationInspectionDetailsList =
                terminationInspectionDetailsMapper.selectByExample(inspectionDetailsExample);

        // 检查结果项附件id
        CtcAttachmentExample ctcAttachmentExample = new CtcAttachmentExample();
        CtcAttachmentExample.Criteria ctcAttachmentExampleCriteria1 = ctcAttachmentExample.createCriteria();
        ctcAttachmentExampleCriteria1.andModuleEqualTo(CtcAttachmentModule.PROJECT_TERMINATION_CHECK_ITEM.code());
        ctcAttachmentExampleCriteria1.andDeletedFlagEqualTo(false);
        ctcAttachmentExampleCriteria1.andModuleIdEqualTo(terminationInspectionId);
        List<CtcAttachment> attachments = ctcAttachmentMapper.selectByExample(ctcAttachmentExample);
        if (attachments != null && attachments.size() != 0) {
            terminationCheckStatusDTO.setAttamentId(attachments.get(0).getAttachId());
        }

        if (ListUtils.isNotEmpty(terminationInspectionDetailsList)) {
            terminationCheckStatusDTO.setCheck(Boolean.FALSE);
            if (terminationInspection != null) {
                terminationCheckStatusDTO.setStatus(terminationInspection.getStatus());
            }
            terminationCheckStatusDTO.setLastDateTime(terminationInspectionDetailsList.get(0).getCreateAt());
            terminationCheckStatusDTO.setList(terminationInspectionDetailsList);
            //检查失败的项
            List<TerminationInspectionDetails> failTerminationInspectionDetailsList =
                    terminationInspectionDetailsList.stream().filter(terminationInspectionDetails -> 2 == terminationInspectionDetails.getStatus()).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(failTerminationInspectionDetailsList)) {
                terminationCheckStatusDTO.setNotPassCount(failTerminationInspectionDetailsList.size());
            } else {
                terminationCheckStatusDTO.setNotPassCount(0);
            }
            //检查警告的项
            List<TerminationInspectionDetails> warningTerminationInspectionDetailsList =
                    terminationInspectionDetailsList.stream().filter(terminationInspectionDetails -> 3 == terminationInspectionDetails.getStatus()).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(warningTerminationInspectionDetailsList)) {
                terminationCheckStatusDTO.setWarningCount(warningTerminationInspectionDetailsList.size());
            } else {
                terminationCheckStatusDTO.setWarningCount(0);
            }

        }

        return terminationCheckStatusDTO;
    }

    /**
     * 根据ouid取工时填报起始日
     *
     * @param ouId
     * @return
     */
    private Date getWorkingHourStartDate(Long ouId) {
        String startDate = "";
        Set<String> valueSet = organizationCustomDictService.queryByName("工时填报起始日", ouId,
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
        if (!CollectionUtils.isEmpty(valueSet)) {
            startDate = valueSet.iterator().next();
        }
        if (StringUtil.isNotNull(startDate)) {
            return DateUtil.parseDate(startDate);
        }
        return null;
    }

    /**
     * 根据使用单位取工时填报上限
     *
     * @param unitId
     * @return
     */
    private int getLimitHours(Long unitId) {
        String limitHour = "";
        Set<String> valueSet = organizationCustomDictService.queryByName("工时填报小时数上限", unitId,
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.COMPANY.code()));
        if (!CollectionUtils.isEmpty(valueSet)) {
            limitHour = valueSet.iterator().next();
        }
        if (StringUtil.isNotNull(limitHour)) {
            return Integer.parseInt(limitHour);
        }
        return 8;
    }

    public List<ProjectCheckReturnVo> workflowInfoToExcelVo(List<WorkflowInfoVo> workflowInfoVoList) {
        List<ProjectCheckReturnVo> checkReturnVoList = new ArrayList<>();
        //详细设计发布
        if (!CollectionUtils.isEmpty(workflowInfoVoList)) {
            for (int i = 0; i < workflowInfoVoList.size(); i++) {
                WorkflowInfoVo workflowInfoVo = workflowInfoVoList.get(i);
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0(Integer.toString(i + 1));
                returnVo.setC1(workflowInfoVo.getFdSubject());
                returnVo.setC2(workflowInfoVo.getCreateBy());
                returnVo.setC3(workflowInfoVo.getFdHandlerName());
                returnVo.setC4(DateUtil.format(workflowInfoVo.getCreatAt(), DateUtil.TIMESTAMP_PATTERN));
                returnVo.setC5(workflowInfoVo.getStatus());
                if (StringUtils.isNotEmpty(workflowInfoVo.getFormCode())) {
                    returnVo.setC1(returnVo.getC1() + "_" + workflowInfoVo.getFormCode());
                }
                checkReturnVoList.add(returnVo);
            }
        }
        return checkReturnVoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean disable(ProjectDto projectDto) {
        Guard.notNullOrEmpty(projectDto.getInvalidReason(), "作废原因不能为空");

        Project project = projectService.selectByPrimaryKey(projectDto.getId());
        Integer status = project.getStatus();
        project.setInvalidId(SystemContext.getUserId());
        project.setStatus(ProjectStatus.INVALID.getCode());
        project.setInvalidTime(new Date());
        project.setInvalidReason(projectDto.getInvalidReason());

        FormInstanceEvent formInstanceEvent = new FormInstanceEvent();
        formInstanceEvent.setFormInstanceId(project.getId());
        formInstanceEvent.setFormUrl(getFormUrl(status));
        formInstanceEvent.setCompanyId(SystemContext.getUnitId());
        formInstanceEvent.setCreateAt(new Date());
        if (StringUtils.isNotEmpty(formInstanceEvent.getFormUrl())) {
            if (Objects.equals(ProjectStatus.SAVE.getCode(), status)) {
                // 项目草稿作废相当于要删除流程实例
                formInstanceEvent.setEventType(WorkflowOperationType.DELETE.getCode());
                String jsonString = JSONObject.toJSONString(formInstanceEvent);
                stringRedisTemplate.opsForList().leftPushAll(Constants.REDIS_WF_EVENT_KEY, jsonString);
            } else {
                formInstanceEvent.setEventType(WorkflowOperationType.DRAFT_ABANDON.getCode());
                String jsonString = JSONObject.toJSONString(formInstanceEvent);
                stringRedisTemplate.opsForList().leftPushAll(Constants.REDIS_WF_EVENT_KEY, jsonString);
            }
        }

        //释放关联合同和商机
        projectContractRsService.updateContractBusinessDel(project.getId());
        //作废项目资源计划表
        List<ProjectResourceRel> updateList = new ArrayList<>();
        ProjectResourceRelExample example = new ProjectResourceRelExample();
        ProjectResourceRelExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(project.getId());
        List<ProjectResourceRel> projectResourceRelList = projectResourceRelMapper.selectByExample(example);
        for (ProjectResourceRel projectResourceRel : projectResourceRelList) {
            projectResourceRel.setDeletedFlag(Boolean.TRUE);
            updateList.add(projectResourceRel);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            projectResourceRelExtMapper.batchUpdate(updateList);
        }
        projectService.updateByPrimaryKey(project);
        //转质保项目作废处理
        if (project.getOriginalProject() != null) {
            dealWithTransferProjectAbandon(project.getId());
        }
        return true;
    }

    /**
     * 转质保项目作废处理
     *
     * @param projectId
     */
    @Override
    public void dealWithTransferProjectAbandon(Long projectId) {
        Project project = projectMapper.selectByPrimaryKey(projectId);
        //回写原项目转质保状态
        Project originalProject = projectService.selectByPrimaryKey(project.getOriginalProject());
        if (originalProject != null) {
            originalProject.setTransferProjectState(TransferProjectState.WAIT_FOR.getCode());
            projectService.updateByPrimaryKeySelective(originalProject);
        }
        //作废的转质保项目添加时间戳，防止项目号重复
        project.setCode(project.getCode() + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        //取消与原项目的关联
        project.setOriginalProject(null);
        projectService.updateByPrimaryKey(project);
    }

    @Override
    public void projectPendingCloseCheck(ProjectDto dto) {
        applicationEventPublisher.publishEvent(new ProjectPendingCloseCheckEvent(this, dto.getCompanyId(), dto.getPendingCloseProjectList(),
                dto.getWorkflowInfo()));
    }

    @Override
    public Map<String, BigDecimal> getSummaryBudgetByBusinessId(Long businessId, String businessCode,
                                                                Long currentProjectId) {
        Map<String, BigDecimal> summaryBudgetMap = new HashMap<>();
        List<Long> projectList = projectExtMapper.getProjectByBusinessId(businessCode, businessId, currentProjectId);
        if (!CollectionUtils.isEmpty(projectList)) {
            //汇总商机物料成本占用
            BigDecimal materialTotal = projectExtMapper.getSummaryBudgetMaterial(projectList);
            //汇总商机人力成本占用
            BigDecimal humanTotal = projectExtMapper.getSummaryBudgetHuman(projectList);
            //汇总商机差旅成本占用
            BigDecimal travelTotal = projectExtMapper.getSummaryBudgetTravel(projectList);
            //汇总商机非差旅成本占用
            BigDecimal feeTotal = projectExtMapper.getSummaryBudgetfee(projectList);
            summaryBudgetMap.put("materialBudget", materialTotal);
            summaryBudgetMap.put("humanBudget", humanTotal);
            summaryBudgetMap.put("travelBudget", travelTotal);
            summaryBudgetMap.put("feeBudget", feeTotal);
        } else {
            summaryBudgetMap.put("materialBudget", BigDecimal.ZERO);
            summaryBudgetMap.put("humanBudget", BigDecimal.ZERO);
            summaryBudgetMap.put("travelBudget", BigDecimal.ZERO);
            summaryBudgetMap.put("feeBudget", BigDecimal.ZERO);
        }
        return summaryBudgetMap;
    }

    @Override
    public List<ProjectSummaryBudget> getDetailBudgetByBusinessId(Long businessId, String businessCode,
                                                                  Long currentProjectId) {
        List<ProjectSummaryBudget> projectList = projectExtMapper.getDetailBudgetByBusinessId(businessCode,
                businessId, currentProjectId);
        return projectList;
    }

    /**
     * 项目终止时进行的项目检查项，并且根据项目检查项的结果生成检查结果信息Excel
     *
     * @param projectId
     * @param terminationTypeId
     * @param terminationTypeName
     * @param workflowInfo
     * @return
     */
    @Override
    @Transactional
    public Map<String, Object> terminationInspectionAddTask(Long projectId, Long terminationTypeId,
                                                            String terminationTypeName,
                                                            Map<String, Object> workflowInfo) {
        Map<String, Object> result = new HashMap<String, Object>();
        TerminationInspectionExample terminationInspectionExample = new TerminationInspectionExample();
        terminationInspectionExample.createCriteria().andProjectIdEqualTo(projectId).andStatusEqualTo(2).andTerminationTypeIdEqualTo(terminationTypeId);
        terminationInspectionExample.setOrderByClause("id desc");
        List<TerminationInspection> terminationInspectionList =
                terminationInspectionMapper.selectByExample(terminationInspectionExample);
        if (ListUtils.isNotEmpty(terminationInspectionList)) {
            throw new BizException(ErrorCode.CTC_TERMINATIONINSPECTION_IS_NOT_NULL);
        }
        ProjectTerminationType projectTerminationType =
                projectTerminationTypeMapper.selectByPrimaryKey(terminationTypeId);
        TerminationInspection terminationInspection = new TerminationInspection();
        terminationInspection.setProjectId(projectId);
//        terminationInspection.setStatus(2);//交付检查(1未开始,2进行中 3通过 4不通过 5警告)
        terminationInspection.setTerminationTypeId(terminationTypeId);
        terminationInspection.setTerminationTypeName(projectTerminationType.getTerminationName());
        terminationInspection.setTerminationTypeCode(projectTerminationType.getTerminationCode());
        terminationInspectionMapper.insert(terminationInspection);
        Long terminationInspectionId = terminationInspection.getId();
        List<Long> secondUnits = SystemContext.getSecondUnits();
        if (projectId == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_ID_NULL);
        }
        Project project = this.projectMapper.selectByPrimaryKey(projectId);
        if (project == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_NOT_FIND);
        }
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        String filePath = "/apps/pam/ctc/file/" + project.getCode() + "项目终止检查结果" + df.format(new Date()) + ".xls";
        Integer terminationStatus = 3;
        List<TerminationInspectionDetails> terminationInspectionDetailsList = new ArrayList<>();
        FileInputStream fileInputStream = null;
        OutputStream out = null;
        try {
            ExportExcelUtil eeu = new ExportExcelUtil();
            HSSFWorkbook workbook = new HSSFWorkbook();
            out = new FileOutputStream(filePath);
            //获取登录人的二级使用单位
            List<Long> units = new ArrayList<>();
            Long unitId = SystemContext.getUnitId();
            units.add(unitId);


            List<ProjectTerminationCheckRelDTO> projectTerminationCheckRelDTOList =
                    projectTerminationCheckRelExtMapper.selectList(terminationTypeId, units);
            if (ListUtils.isNotEmpty(projectTerminationCheckRelDTOList)) {
                int i = 0;
                for (ProjectTerminationCheckRelDTO checkRelDTO : projectTerminationCheckRelDTOList) {
                    String checkCode = checkRelDTO.getCheckCode();//检查点CODE
                    String checkName = checkRelDTO.getCheckName();//检查点名称
                    Integer yesOrNo = checkRelDTO.getYesOrNo();//严控类型(1是，2警告， 0否)
                    Long checkItemId = checkRelDTO.getCheckItemId();//交付检查项ID
                    //检查结果项
                    TerminationInspectionDetails terminationInspectionDetails = null;
                    //检查项目中是否存在审批中的流程
                    if (CheckItemEnums.A01.getCode().equals(checkCode)) {
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectItem(project, workflowInfo);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();

                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A02.getCode().equals(checkCode)) {  //项目是否存在审批中、变更中的工时
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectChangeWorkingHourItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();

                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A03.getCode().equals(checkCode)) { //销售合同实际开票金额与合同金额（含税）是否一致
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkContractInvoiceItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A04.getCode().equals(checkCode)) { //销售合同实际回款金额与实际开票金额是否一致
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkContractMoneyBackItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A05.getCode().equals(checkCode)) { //项目工时填报天数是否与合同天数一致
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectWorkingHourItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A06.getCode().equals(checkCode)) { //项目辅里程碑节点是否已全部交付
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectMilepostItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A07.getCode().equals(checkCode)) { //项目是否存在在途的费用申请/报销单
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectFeeItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A08.getCode().equals(checkCode)) { //项目详细设计是否存在审批中的流程
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectDesignItem(project, workflowInfo);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A09.getCode().equals(checkCode)) { //项目外包采购是否存在审批中的流程
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectPurchaseItem(project, workflowInfo);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A10.getCode().equals(checkCode)) { //项目库存是否存在审批中的流程
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectStockApprovalItem(project, workflowInfo);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A11.getCode().equals(checkCode)) { //项目是否已完成详细设计方案的进度确认
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectPlanItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A12.getCode().equals(checkCode)) { //项目详细设计方案的采购物料是否已全部生成采购需求
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectMaterielItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A13.getCode().equals(checkCode)) { //项目采购需求是否已全部生成采购订单
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectRequirementItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A14.getCode().equals(checkCode)) { //项目采购订单物料是否已全部接收入库
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectPurchaseOrderItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A15.getCode().equals(checkCode)) {//项目子库的库存是否为0
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectStockItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A16.getCode().equals(checkCode)) {//项目库存是否存在未处理完的单据
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkProjectStorageItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A17.getCode().equals(checkCode)) { // 合同是否有开票金额
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = chckProjectContractInvoiceItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A18.getCode().equals(checkCode)) { // 合同是否有回款
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = chckProjectContractReceiptItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A19.getCode().equals(checkCode)) { // 项目是否有结转单
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = chckProjectCarryBillItem(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A20.getCode().equals(checkCode)) { // 项目是否存在已发生的工时成本
                        if (yesOrNo == 0) {
                            continue;
                        }

                        ProjectCheckExcelVo projectCheckExcelVo = chckProjectWorkingHourCost(project);

                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    } else if (CheckItemEnums.A21.getCode().equals(checkCode)) { //项目是否存在已发生的费用成本
                        if (yesOrNo == 0) {
                            continue;
                        }

                        ProjectCheckExcelVo projectCheckExcelVo = chckProjectFeeCost(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A22.getCode().equals(checkCode)) { // 项目是否存在已结转收入
                        if (yesOrNo == 0) {
                            continue;
                        }

                        ProjectCheckExcelVo projectCheckExcelVo = chckProjectIncomeCarry(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A23.getCode().equals(checkCode)) { // 项目是否关联合同
                        if (yesOrNo == 0) {
                            continue;
                        }

                        ProjectCheckExcelVo projectCheckExcelVo = chckProjectRelContract(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId,
                                checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;

                    } else if (CheckItemEnums.A28.getCode().equals(checkCode)) { //合同开票金额减去开票金额（除去质保款）= 0
                        if (yesOrNo == 0) {
                            continue;
                        }
                        ProjectCheckExcelVo projectCheckExcelVo = checkContractInvoice(project);
                        if (!ListUtils.isNotEmpty(projectCheckExcelVo.getReturnList())) {
                            continue;
                        }
                        projectCheckExcelVo.setCheckName(checkName);
                        List<ProjectCheckReturnVo> returnList = projectCheckExcelVo.getReturnList();
                        terminationInspectionDetails = getTerminationInspectionDetails(terminationInspectionId, checkItemId, checkName,
                                yesOrNo, returnList, projectCheckExcelVo.getRemark());
                        eeu.exportExcel(workbook, i, projectCheckExcelVo, out);
                        i++;
                    }

                    if (terminationInspectionDetails != null) {
                        terminationInspectionDetailsMapper.insert(terminationInspectionDetails);
                        terminationInspectionDetailsList.add(terminationInspectionDetails);
                    }
                }

                if (ListUtils.isNotEmpty(terminationInspectionDetailsList)) {
                    for (TerminationInspectionDetails inspectionDetails : terminationInspectionDetailsList) {
                        //检查状态(1通过 2不通过 3警告)
                        if (inspectionDetails.getStatus() == 2) { //只要有一项检查不通过，就是检查不通过
                            terminationStatus = 4;//交付检查(1未开始,2进行中 3通过 4不通过 5警告 6系统异常)
                        }
                    }
                }

                //更新任务状态
                terminationInspection.setStatus(terminationStatus);
                terminationInspectionMapper.updateByPrimaryKey(terminationInspection);

                workbook.write(out);
                out.close();
                result.put("isSuccess", true);
                result.put("filePath", filePath);
                File pdfFile = new File(filePath);
                fileInputStream = new FileInputStream(pdfFile);
                MultipartFile multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                        ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                Map<String, Object> map = ossService.upload(multipartFile);
                CtcAttachmentDto dto = new CtcAttachmentDto();
                dto.setModule(CtcAttachmentModule.PROJECT_TERMINATION_CHECK_ITEM.code());
                dto.setDeletedFlag(false);
                dto.setModuleId(terminationInspectionId);
                dto.setAttachId((Long) map.get("fileId"));
                dto.setFileName((String) map.get("fileName"));
                dto.setStatus(1);
                ctcAttachmentService.add(dto);
                result.put("attachId", (Long) map.get("fileId"));
                result.put("createAt", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                result.put("terminationInspectionId", terminationInspectionId);
            } else {
                result.put("isSuccess", true);
                result.put("filePath", filePath);
                result.put("createAt", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                result.put("terminationInspectionId", terminationInspectionId);
                //更新任务状态
                terminationInspection.setStatus(3);
                terminationInspectionMapper.updateByPrimaryKey(terminationInspection);
            }
        } catch (Exception e) {
//            result.put("isSuccess", false);
//            result.put("error", e.getMessage());
//            result.put("terminationInspectionId",terminationInspectionId);
//            logger.error(e.getMessage(), e);
//            terminationInspection.setStatus(6);//系统异常
//            terminationInspectionMapper.updateByPrimaryKey(terminationInspection);
            throw new MipException("项目终止检查异常", e);
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    logger.info(e.getMessage(), e);
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.info(e.getMessage(), e);
                }
            }
        }

        if (!(Boolean) result.get("isSuccess")) {
            throw new MipException("项目终止检查失败，数据存在错误，请确认");
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void releaseResourcePlan(Project project) {
        //作废项目资源关系表
        List<ProjectResourceRel> projectResourceRelUpdateList = new ArrayList<>();
        ProjectResourceRelExample resourceRelExample = new ProjectResourceRelExample();
        resourceRelExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
        List<ProjectResourceRel> projectResourceRelList = projectResourceRelMapper.selectByExample(resourceRelExample);
        for (ProjectResourceRel projectResourceRel : projectResourceRelList) {
            projectResourceRel.setDeletedFlag(Boolean.TRUE);
            projectResourceRelUpdateList.add(projectResourceRel);
        }
        if (CollectionUtils.isNotEmpty(projectResourceRelUpdateList)) {
            projectResourceRelExtMapper.batchUpdate(projectResourceRelUpdateList);
        }
    }

    public String getFormUrl(Integer status) {
        String url = "";
        switch (status) {
            case -4:
                url = "preProjectApp";
                break;
            case -2:
                url = "projectApp";
                break;
            case 0:
                url = "projectApp";
                break;
            case 3:
                url = "projectApp";
                break;
            case 4:
                url = "projectBudgetChangeApp";
                break;
            case 13:
                url = "preProjectApp";
                break;
            default:
                break;
        }
        return url;
    }

    @Override
    public Boolean checkIncomeCompleted(Long id) {
        Project project = projectService.selectByPrimaryKey(id);
        if (project != null) {
            // 预立项项目不生成收入成本计划
            if (Boolean.TRUE.equals(project.getPreviewFlag())) {
                return Boolean.FALSE;
            }

            Long type = project.getType();
            if (type != null) {
                ProjectType projectType = projectTypeService.selectByPrimaryKey(type);
                if (projectType != null && projectType.getCostMethod() == null) {
                    return Boolean.FALSE;
                }

                // 月度确认项目，未生成收入节点，不做检验
                if (projectType != null) {
                    String incomePoint = projectType.getIncomePoint();

                    if (Objects.equals(incomePoint, IncomePoint.MONTH.getCode())) {
                        ProjectIncomeCostPlanExample example = new ProjectIncomeCostPlanExample();
                        ProjectIncomeCostPlanExample.Criteria criteria = example.createCriteria();
                        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
                        criteria.andProjectIdEqualTo(id);
                        criteria.andIncomeFlagEqualTo(Boolean.TRUE);

                        List<ProjectIncomeCostPlan> projectIncomeCostPlans =
                                projectIncomeCostPlanMapper.selectByExample(example);
                        if (ListUtils.isEmpty(projectIncomeCostPlans)) {
                            return Boolean.FALSE;
                        }
                    }

                    if (Objects.equals(incomePoint, IncomePoint.MILEPOST.getCode())) {
                        ProjectMilepostExample projectMilepostExample = new ProjectMilepostExample();
                        ProjectMilepostExample.Criteria criteria = projectMilepostExample.createCriteria();
                        criteria.andProjectIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE).andIncomeFlagEqualTo(Boolean.TRUE)
                                .andStatusNotEqualTo(MilepostStatus.PASSED.getCode());

                        List<ProjectMilepost> projectMileposts =
                                projectMilepostMapper.selectByExample(projectMilepostExample);
                        if (ListUtils.isEmpty(projectMileposts)) {
                            return Boolean.TRUE;
                        }
                    }
                }
            }
        }

        return Boolean.FALSE;
    }


    @Override
    public Boolean checkIncomeCompletedSec(Long id) {
        Project project = projectService.selectByPrimaryKey(id);
        if (project != null) {
            // 预立项项目不生成收入成本计划
            if (project.getPreviewFlag()) {
                return Boolean.FALSE;
            }

            Long type = project.getType();
            if (type != null) {
                ProjectType projectType = projectTypeService.selectByPrimaryKey(type);
                if (projectType != null && projectType.getCostMethod() == null) {
                    return Boolean.FALSE;
                }

                // 月度确认项目，未生成收入节点，不做检验
                if (projectType != null) {
                    String incomePoint = projectType.getIncomePoint();

                    if (Objects.equals(incomePoint, IncomePoint.MONTH.getCode())) {
                        ProjectIncomeCostPlanExample example = new ProjectIncomeCostPlanExample();
                        ProjectIncomeCostPlanExample.Criteria criteria = example.createCriteria();
                        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
                        criteria.andProjectIdEqualTo(id);
                        criteria.andIncomeFlagEqualTo(Boolean.TRUE);

                        List<ProjectIncomeCostPlan> projectIncomeCostPlans =
                                projectIncomeCostPlanMapper.selectByExample(example);
                        if (ListUtils.isEmpty(projectIncomeCostPlans)) {
                            return Boolean.FALSE;
                        }
                    }

                    if (Objects.equals(incomePoint, IncomePoint.MILEPOST.getCode())) {
                        ProjectMilepostExample projectMilepostExample = new ProjectMilepostExample();
                        ProjectMilepostExample.Criteria criteria = projectMilepostExample.createCriteria();
                        criteria.andProjectIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE).andIncomeFlagEqualTo(Boolean.TRUE)
                                .andCarryoverBillIdIsNull();
                        // 有没有结转的里程碑信息
                        List<ProjectMilepost> projectMileposts =
                                projectMilepostMapper.selectByExample(projectMilepostExample);
                        // 全部都结转
                        if (ListUtils.isEmpty(projectMileposts)) {
                            return Boolean.TRUE;
                        }
                    }
                }
            }
        }

        return Boolean.FALSE;
    }


    private Boolean isInvoice(Long contractId) {
        List<InvoiceApplyDetails> invoiceApplyDetails = this.getInvoiceApplyDetailsById(contractId);
        if (ListUtils.isNotEmpty(invoiceApplyDetails)) {
            BigDecimal totalTaxIncludedPrice = BigDecimal.ZERO;
            for (InvoiceApplyDetails invoiceApplyDetail : invoiceApplyDetails) {
                BigDecimal taxIncludedPrice = invoiceApplyDetail.getTaxIncludedPrice() == null ? BigDecimal.ZERO :
                        invoiceApplyDetail.getTaxIncludedPrice();
                InvoiceApplyHeader invoiceApplyHeader =
                        invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyDetail.getApplyHeaderId());
                if (invoiceApplyHeader.getStatus() != -1 && invoiceApplyHeader.getDeletedFlag() != Boolean.TRUE && invoiceApplyDetail.getStatus() != -1) {
                    totalTaxIncludedPrice = totalTaxIncludedPrice.add(taxIncludedPrice);
                }
            }
            if (!BigDecimalUtils.equals(totalTaxIncludedPrice, BigDecimal.ZERO)) {
                return true;
            } else {
                return false;
            }

        }
        return false;
    }

    private Boolean isInvoicePrice(Long contractId) {
        List<InvoicePlanDetail> invoicePlanDetails = this.getInvoicePlandetails(contractId);
        if (ListUtils.isNotEmpty(invoicePlanDetails)) {
            BigDecimal totalTaxIncludedPrice = BigDecimal.ZERO;
            BigDecimal actualPrice = BigDecimal.ZERO;
            for (InvoicePlanDetail invoicePlanDetail : invoicePlanDetails) {
                BigDecimal taxIncludedPrice = invoicePlanDetail.getTaxIncludedPrice() == null ? BigDecimal.ZERO :
                        invoicePlanDetail.getTaxIncludedPrice();
                BigDecimal price = invoicePlanDetail.getTaxIncludedPrice() == null ? BigDecimal.ZERO : invoicePlanDetail.getTaxIncludedPrice();
                if (invoicePlanDetail.getDeletedFlag() != Boolean.TRUE && invoicePlanDetail.getStatus() != -1) {
                    totalTaxIncludedPrice = totalTaxIncludedPrice.add(taxIncludedPrice);
                    if ("质保款".equals(invoicePlanDetail.getInvoiceType())) {
                        actualPrice = actualPrice.add(price);
                    }
                }
            }
            if (BigDecimal.ZERO.equals(totalTaxIncludedPrice.subtract(actualPrice))) {
                return true;
            } else {
                return false;
            }

        }
        return false;
    }

    private List<InvoiceApplyDetails> getInvoiceApplyDetailsById(Long contractId) {
        InvoiceApplyDetailsExample invoiceApplyDetailsExample = new InvoiceApplyDetailsExample();
        InvoiceApplyDetailsExample.Criteria criteria1 = invoiceApplyDetailsExample.createCriteria();
        criteria1.andContractIdEqualTo(contractId);
        List<InvoiceApplyDetails> invoiceApplyDetails =
                invoiceApplyDetailsMapper.selectByExample(invoiceApplyDetailsExample);
        return invoiceApplyDetails;
    }

    private List<InvoicePlanDetail> getInvoicePlandetails(Long contractId) {
        InvoicePlanDetailExample invoicePlanDetailExample = new InvoicePlanDetailExample();
        InvoicePlanDetailExample.Criteria criteria = invoicePlanDetailExample.createCriteria();
        criteria.andContractIdEqualTo(contractId);
        List<InvoicePlanDetail> invoicePlanDetails = invoicePlanDetailMapper.selectByExample(invoicePlanDetailExample);
        return invoicePlanDetails;
    }

    private List<ReceiptPlanDetail> getReceiptPlanDetail(Long contractId) {
        ReceiptPlanDetailExample receiptPlanDetailExample = new ReceiptPlanDetailExample();
        ReceiptPlanDetailExample.Criteria criteria = receiptPlanDetailExample.createCriteria();
        criteria.andContractIdEqualTo(contractId);
        List<ReceiptPlanDetail> receiptPlanDetails = receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample);
        return receiptPlanDetails;
    }


    @Override
    public Long getOrganizationIdByProjectId(Long projectId) {
        Long storageId = ErpOrganizationId.ROBOT.code();
        if (projectId == null) {
            return storageId;
        }
        Project project = projectService.selectByPrimaryKey(projectId);
        //查询库存组织
        if (project != null) {
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null && projectProfit.getStorageId() != null) {
                storageId = projectProfit.getStorageId();
            }
        }
        return storageId;
    }

    @Override
    public List<Long> getSecondUnits(Long unitId) {
        return null;
    }

    @Override
    public Long ksGetOrganizationIdByProjectId(Long projectId) {
        Long storageId = ErpOrganizationId.ROBOT.code();
        if (projectId == null) {
            return storageId;
        }
        Project project = projectService.selectByPrimaryKey(projectId);
        //查询库存组织
        if (project != null) {
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null && projectProfit.getStorageId() != null) {
                storageId = projectProfit.getStorageId();
            }
        }
        return storageId;
    }

    @Override
    public ProjectDto deliverChangeBefore(Long projectId) {
        // 查询对应项目基本信息
        ProjectDto detail = this.findDetail(projectId);
        // 修改为质保期项目类型
        CodeRuleExample codeRuleExample = new CodeRuleExample();
        codeRuleExample.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                .andRuleTypeEqualTo(3).andDeletedFlagEqualTo(false);

        //编码规则
        CodeRule codeRule = CollectionsUtil.ofNullable(
                codeRuleMapper.selectByExample(codeRuleExample)
        ).ifNullThenThrow(() -> new BizException(Code.ERROR, "请先设置质保期内专用编码规则!")).get().get(0);

        //项目类型
        ProjectType projectType = Optional.of(projectTypeMapper.selectByPrimaryKey(codeRule.getProjectTypeId())).get();
        detail.setType(projectType.getId());

        // 修改为质保期项目名称
        detail.setName(detail.getName() + "（质保期内）");

        // 对应项目类型的里程碑
        MilepostTemplateStageExample milepostTemplateStageExample = new MilepostTemplateStageExample();
        milepostTemplateStageExample.createCriteria().andMilepostTemplateIdEqualTo(projectType.getMilepostTemplateId());
        List<MilepostTemplateStage> milepostTemplateStages =
                milepostTemplateStageMapper.selectByExample(milepostTemplateStageExample);

        // 设置里程碑
        detail.setMilepostTemplateStages(milepostTemplateStages);
        return detail;
    }


    @Override
    public void deliverChangeStage(DeliverChangeProject deliverChangeProject) {
        String key = DELIVER_STAGE + deliverChangeProject.getMilepostId() + ":" + deliverChangeProject.getProjectId();
        valueCache.set(
                key
                , JsonUtils.toString(deliverChangeProject),
                20,
                TimeUnit.DAYS);
    }

    @Override
    public DeliverChangeProject deliverChangeQuery(Long milepostId, Long projectId) {
        String s = valueCache.get(DELIVER_STAGE + milepostId + ":" + projectId, String.class);
        return JSONObject.parseObject(s, DeliverChangeProject.class);
    }

    @Override
    public Boolean queryRequirementDeliverMrp(Long projectId) {
        return projectExtMapper.queryRequirementDeliverMrp(projectId);
    }


    @Override
    public ProjectCheckExcelVo checkProjectContractInvoiceStatus(Project project) {
        String[] headers = {"序号", "税票编号", "发票属性"
                , "采购合同名称", "供应商名称", "供应商编码", "币种", "发票类型", "税率"
                , "发票金额(含税)", "发票金额(不含税)", "发票状态", "业务实体"
        };
        Builder<ProjectCheckExcelVo> projectCheckExcelVoBuilder = Builder.of(ProjectCheckExcelVo::new)
                .with(ProjectCheckExcelVo::setDate, new Date())
                .with(ProjectCheckExcelVo::setProjectCode, project.getCode())
                .with(ProjectCheckExcelVo::setProjectName, project.getName())
                .with(ProjectCheckExcelVo::setHeaders, headers)
                .with(ProjectCheckExcelVo::setPass, Boolean.TRUE);

        //项目下的采购合同是否存在未入账的发票或罚扣
        PaymentInvoiceDetailExample paymentInvoiceDetailExample = new PaymentInvoiceDetailExample();
        paymentInvoiceDetailExample.createCriteria().andDeletedFlagEqualTo(false).andInvoiceStatusNotEqualTo("QUOTE");
        AtomicInteger atomicInteger = new AtomicInteger();
        List<ProjectCheckReturnVo> collect =
                CollectionsUtil.ofNullable(paymentInvoiceDetailMapper.selectByExample(paymentInvoiceDetailExample))
                        .orElseGet(Collections::emptyList)
                        .stream()
                        .map(i -> Builder.of(ProjectCheckReturnVo::new)
                                .with(ProjectCheckReturnVo::setC0, atomicInteger.addAndGet(1) + "")
                                .with(ProjectCheckReturnVo::setC1, i.getInvoiceDetailCode())
                                .with(ProjectCheckReturnVo::setC2, i.getAttribute())
                                .with(ProjectCheckReturnVo::setC3, i.getPurchaseContractName())
                                .with(ProjectCheckReturnVo::setC4, i.getVendorName())
                                .with(ProjectCheckReturnVo::setC5, i.getVendorCode())
                                .with(ProjectCheckReturnVo::setC6, i.getCurrency())
                                .with(ProjectCheckReturnVo::setC7, i.getInvoiceType())
                                .with(ProjectCheckReturnVo::setC8, i.getTaxRate().toString())
                                .with(ProjectCheckReturnVo::setC9, i.getTaxIncludedPrice().toString())
                                .with(ProjectCheckReturnVo::setC10, i.getTaxExcludedPrice().toString())
                                .with(ProjectCheckReturnVo::setC11, i.getInvoiceStatus())
                                .with(ProjectCheckReturnVo::setC12, i.getOuId().toString())
                                .build())
                        .collect(Collectors.toList());
        return projectCheckExcelVoBuilder
                .with(ProjectCheckExcelVo::setPass, collect.size() > 0 ? Boolean.FALSE : Boolean.TRUE)
                .with(ProjectCheckExcelVo::setReturnList, collect)
                .build();
    }

    @Override
    public ProjectCheckExcelVo checkProjectProblem(Project project) {
        String[] headers = {"序号", "问题编号", "问题标题", "问题类别", "问题状态"};
        Builder<ProjectCheckExcelVo> projectCheckExcelVoBuilder = Builder.of(ProjectCheckExcelVo::new)
                .with(ProjectCheckExcelVo::setDate, new Date())
                .with(ProjectCheckExcelVo::setProjectCode, project.getCode())
                .with(ProjectCheckExcelVo::setProjectName, project.getName())
                .with(ProjectCheckExcelVo::setHeaders, headers)
                .with(ProjectCheckExcelVo::setPass, Boolean.TRUE);

        ProjectProblemExample example = new ProjectProblemExample();
        example.createCriteria()
                .andProjectIdEqualTo(project.getId())
                .andDeleteFlagEqualTo(Boolean.FALSE)
                .andStatusNotIn(Arrays.asList(ProjectProblemStatusEnum.ABANDONED.code(),
                        ProjectProblemStatusEnum.TEMPORARY_STORAGE.code(),
                        ProjectProblemStatusEnum.CLOSED.code()));
        AtomicInteger atomicInteger = new AtomicInteger();
        List<ProjectCheckReturnVo> collect = CollectionsUtil.ofNullable(projectProblemMapper.selectByExample(example))
                .orElseGet(Collections::emptyList)
                .stream()
                .map(m -> Builder.of(ProjectCheckReturnVo::new)
                        .with(ProjectCheckReturnVo::setC0, atomicInteger.addAndGet(1) + "")
                        .with(ProjectCheckReturnVo::setC1, m.getCode())
                        .with(ProjectCheckReturnVo::setC2, m.getTitle())
                        .with(ProjectCheckReturnVo::setC3, m.getProblemType())
                        .with(ProjectCheckReturnVo::setC4, ProjectProblemStatusEnum.convertStatusName(m.getStatus()))
                        .build())
                .collect(Collectors.toList());
        return projectCheckExcelVoBuilder
                .with(ProjectCheckExcelVo::setPass, collect.size() > 0 ? Boolean.FALSE : Boolean.TRUE)
                .with(ProjectCheckExcelVo::setReturnList, collect)
                .build();
    }

    @Override
    public ProjectCheckExcelVo checkProjectCustomer(Project project) {
        String[] headers = {"序号", "项目编号", "项目名称", "客户CRM编码", "客户名称", "客户类型"};
        Builder<ProjectCheckExcelVo> projectCheckExcelVoBuilder = Builder.of(ProjectCheckExcelVo::new)
                .with(ProjectCheckExcelVo::setDate, new Date())
                .with(ProjectCheckExcelVo::setProjectCode, project.getCode())
                .with(ProjectCheckExcelVo::setProjectName, project.getName())
                .with(ProjectCheckExcelVo::setHeaders, headers)
                .with(ProjectCheckExcelVo::setPass, Boolean.TRUE);

        ProjectContractRsExample example = new ProjectContractRsExample();
        example.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectContractRs> projectContractRs = projectContractRsMapper.selectByExample(example);
        if (ListUtils.isEmpty(projectContractRs)) {
            return projectCheckExcelVoBuilder.build();
        }
        // 内部客户数量
        AtomicInteger innerAtomic = new AtomicInteger();
        // 外部客户数量
        AtomicInteger outerAtomic = new AtomicInteger();
        List<ProjectCheckReturnVo> returnList = new ArrayList<>();
        for (ProjectContractRs projectContractR : projectContractRs) {
            Contract contract = contractMapper.selectByPrimaryKey(projectContractR.getContractId());
            if (contract == null) {
                continue;
            }
            CustomerDto customer = CacheDataUtils.findCustomerById(contract.getCustomerId());
            if (customer == null) {
                continue;
            }
            if (Objects.equals(Boolean.TRUE, customer.getInner())) {
                innerAtomic.addAndGet(1);
            } else {
                outerAtomic.addAndGet(1);
            }
            ProjectCheckReturnVo projectCheckReturnVo = new ProjectCheckReturnVo();
            projectCheckReturnVo.setC0((innerAtomic.get() + outerAtomic.get()) + "");
            projectCheckReturnVo.setC1(project.getCode());
            projectCheckReturnVo.setC2(project.getName());
            projectCheckReturnVo.setC3(customer.getCrmCode());
            projectCheckReturnVo.setC4(customer.getName());
            projectCheckReturnVo.setC5(Objects.equals(Boolean.TRUE, customer.getInner()) ? "内部客户" : "外部客户");
            returnList.add(projectCheckReturnVo);
        }
        if (innerAtomic.get() > 1 && outerAtomic.get() > 1) {
            return projectCheckExcelVoBuilder.build();
        }
        return projectCheckExcelVoBuilder
                .with(ProjectCheckExcelVo::setPass, Boolean.FALSE)
                .with(ProjectCheckExcelVo::setReturnList, returnList)
                .build();
    }

    @Override
    public ProjectCheckExcelVo checkInnerCustomer(Project project) {
        String[] headers = {"序号", "项目编号", "项目名称", "客户CRM编码", "客户名称", "客户类型"};
        Builder<ProjectCheckExcelVo> projectCheckExcelVoBuilder = Builder.of(ProjectCheckExcelVo::new)
                .with(ProjectCheckExcelVo::setDate, new Date())
                .with(ProjectCheckExcelVo::setProjectCode, project.getCode())
                .with(ProjectCheckExcelVo::setProjectName, project.getName())
                .with(ProjectCheckExcelVo::setHeaders, headers)
                .with(ProjectCheckExcelVo::setPass, Boolean.TRUE);

        ProjectContractRsExample example = new ProjectContractRsExample();
        example.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectContractRs> projectContractRs = projectContractRsMapper.selectByExample(example);
        if (ListUtils.isEmpty(projectContractRs)) {
            return projectCheckExcelVoBuilder.build();
        }
        // 内部客户数量
        AtomicInteger atomicInteger = new AtomicInteger();
        List<ProjectCheckReturnVo> returnList = new ArrayList<>();
        for (ProjectContractRs projectContractR : projectContractRs) {
            Contract contract = contractMapper.selectByPrimaryKey(projectContractR.getContractId());
            if (contract == null) {
                continue;
            }
            CustomerDto customer = CacheDataUtils.findCustomerById(contract.getCustomerId());
            if (customer == null) {
                continue;
            }
            if (Objects.equals(Boolean.TRUE, customer.getInner())) {
                ProjectCheckReturnVo projectCheckReturnVo = new ProjectCheckReturnVo();
                projectCheckReturnVo.setC0(atomicInteger.addAndGet(1) + "");
                projectCheckReturnVo.setC1(project.getCode());
                projectCheckReturnVo.setC2(project.getName());
                projectCheckReturnVo.setC3(customer.getCrmCode());
                projectCheckReturnVo.setC4(customer.getName());
                projectCheckReturnVo.setC5("内部客户");
                returnList.add(projectCheckReturnVo);
            }
        }
        if (atomicInteger.get() <= 1) {
            return projectCheckExcelVoBuilder.build();
        }
        return projectCheckExcelVoBuilder
                .with(ProjectCheckExcelVo::setPass, Boolean.FALSE)
                .with(ProjectCheckExcelVo::setReturnList, returnList)
                .build();
    }

    @Override
    public List<Project> getProjectByIds(List<Long> projectIds) {
        if (ListUtils.isEmpty(projectIds)) {
            return Collections.emptyList();
        }
        return projectExtMapper.selectByIds(projectIds);
    }

    @Deprecated
    @Override
    public Response validImportProjectDetail(List<ProjectImportExcelVo> detailImportExcelVoList, Long unitId) {
        //项目基本信息导入校验
        List<ProjectDto> projectDtoList = BeanConverter.copy(detailImportExcelVoList, ProjectDto.class);
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidDetailResults(projectDtoList, unitId);

        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            //批量插入项目数据
            List<Project> projectList = BeanConverter.copy(projectDtoList, Project.class);

            saveProjectDetail(projectList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(projectDtoList);
    }

    @Override
    public Response validImportProjectMemberDetail(List<ProjectMemberExcelVo> detailImportExcelVoList, Long unitId) { //项目成员导入校验
        List<ProjectMemberDto> projectMemberDtoList = BeanConverter.copy(detailImportExcelVoList,
                ProjectMemberDto.class);
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidProjectMemberDetailResults(projectMemberDtoList, unitId);

        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            List<ProjectMember> projectMemberList = BeanConverter.copy(projectMemberDtoList, ProjectMember.class);
            projectMemberSerice.saveProjectMemberDetail(projectMemberList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(projectMemberDtoList);
    }

    @Override
    public Response validImportProjectBudgetMaterialDetail(List<ProjectBudgetMaterialExcelVo> detailImportExcelVoList
            , Long unitId) { //项目物料预算导入校验
        List<ProjectBudgetMaterialDto> projectBudgetMaterialDtoList = BeanConverter.copy(detailImportExcelVoList,
                ProjectBudgetMaterialDto.class);
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidProjectBudgetMaterialDetailResults(projectBudgetMaterialDtoList, unitId);
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            projectBudgetMaterialService.saveProjectBudgetMaterialDetail(projectBudgetMaterialDtoList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(projectBudgetMaterialDtoList);
    }

    @Override
    public Response validImportProjectBudgetHumanDetail(List<ProjectBudgetHumanExcelVo> detailImportExcelVoList,
                                                        Long unitId) { //项目人力预算初始化数据校验
        List<ProjectBudgetHumanDto> projectBudgetHumanDtoList = BeanConverter.copy(detailImportExcelVoList,
                ProjectBudgetHumanDto.class);
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidProjectBudgetHumanDetailResults(projectBudgetHumanDtoList, unitId);
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            List<ProjectBudgetHuman> projectBudgetHumanList = BeanConverter.copy(projectBudgetHumanDtoList,
                    ProjectBudgetHuman.class);
            projectBudgetHumanService.saveProjectBudgetHumanDetail(projectBudgetHumanList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(projectBudgetHumanDtoList);
    }

    @Override
    public Response validImportProjectBudgetTravelDetail(List<ProjectBudgetTravelExcelVo> detailImportExcelVoList,
                                                         Long unitId) { //项目差旅预算初始化数据校验
        List<ProjectBudgetTravelDto> projectBudgetTravelDtoList = BeanConverter.copy(detailImportExcelVoList,
                ProjectBudgetTravelDto.class);
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidProjectBudgetTravelDetailResults(projectBudgetTravelDtoList, unitId);
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            List<ProjectBudgetTravel> projectBudgetTravelList = BeanConverter.copy(projectBudgetTravelDtoList,
                    ProjectBudgetTravel.class);
            projectBudgetTravelService.saveProjectBudgetTravelDetail(projectBudgetTravelList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(projectBudgetTravelDtoList);
    }

    @Override
    public Response validImportProjectBudgetFeeDetail(List<ProjectBudgetFeeExcelVo> detailImportExcelVoList,
                                                      Long unitId) { //项目其他费用预算初始化数据校验
        List<ProjectBudgetFeeDto> projectBudgetFeeDtoList = BeanConverter.copy(detailImportExcelVoList,
                ProjectBudgetFeeDto.class);
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidProjectBudgetFeeDetailResults(projectBudgetFeeDtoList, unitId);
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            List<ProjectBudgetFee> projectBudgetFeeList = BeanConverter.copy(projectBudgetFeeDtoList,
                    ProjectBudgetFee.class);
            projectBudgetFeeService.saveProjectBudgetFeeDetail(projectBudgetFeeList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(projectBudgetFeeDtoList);
    }

    @Override
    public Response validProjectIncomeOrProjectProfit(List<ProjectIncomeOrProjectProfitExcelVo> detailImportExcelVoList, Long unitId) {
        //收入成本计划初始化数据校验
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidProjectIncomeOrProjectProfitResults(detailImportExcelVoList, unitId);
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            saveProjectIncomeOrProjectProfit(detailImportExcelVoList);
        } else {
            dataResponse.setMsg("FAIL");
            return dataResponse.setData(detailImportExcelVoList.stream().filter(a -> StringUtils.isNotBlank(a.getValidResult())).collect(Collectors.toList()));
        }
        return dataResponse.setData(true);
    }

    @Override
    public Response validCarryoverBill(List<CarryoverBillExcelVo> detailImportExcelVoList, Long unitId) { //项目结转单初始化数据校验
        List<CarryoverBillDto> carryoverBillDtoList = BeanConverter.copy(detailImportExcelVoList,
                CarryoverBillDto.class);
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidCarryoverBillResults(carryoverBillDtoList, unitId);
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            saveCarryoverBill(carryoverBillDtoList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(carryoverBillDtoList);
    }

    @Override
    public Response validProjectProfitTotal(List<ProjectProfitExcelVo> detailImportExcelVoList, Long unitId) {
        //项目收入成本计划汇总初始化数据校验
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidProjectProfitResults(detailImportExcelVoList, unitId);
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            saveProjectProfitDetail(detailImportExcelVoList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(detailImportExcelVoList);
    }

    @Override
    public Response validRevenueCostOrder(List<RevenueCostOrderExcelVo> detailImportExcelVoList, Long unitId) {
        //成本工单初始化数据校验
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidRevenueCostOrderResults(detailImportExcelVoList, unitId);
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            saveRevenueCostOrderDetail(detailImportExcelVoList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(detailImportExcelVoList);
    }

    @Override
    public Response validProjectFeeCollection(List<ProjectFeeCollectionExcelVo> detailImportExcelVoList, Long unitId) { //项目预算费用汇总数据校验
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidProjectFeeCollectionResults(detailImportExcelVoList, unitId);
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            saveProjectFeeCollectionDetail(detailImportExcelVoList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(detailImportExcelVoList);
    }

    private void saveProjectFeeCollectionDetail(List<ProjectFeeCollectionExcelVo> detailImportExcelVoList) {
        List<ProjectFeeCollection> projectFeeCollections = BeanConverter.copy(detailImportExcelVoList,
                ProjectFeeCollection.class);

        for (ProjectFeeCollection projectFeeCollection : projectFeeCollections) {

            if (null == projectFeeCollection.getId()) {
                projectFeeCollection.setCreateAt(new Date());
                projectFeeCollection.setCreateBy(SystemContext.getUserId());
                projectFeeCollection.setDeletedFlag(false);
                projectFeeCollectionMapper.insert(projectFeeCollection);
            } else {
                projectFeeCollection.setUpdateAt(new Date());
                projectFeeCollection.setUpdateBy(SystemContext.getUserId());
                projectFeeCollectionMapper.updateByPrimaryKeySelective(projectFeeCollection);
            }
        }

    }

    private boolean getValidProjectFeeCollectionResults(List<ProjectFeeCollectionExcelVo> detailImportExcelVoList,
                                                        Long company) {
        //获取使用单位下所有的项目编号
        List<Project> projects = getAllProjectCode(company);
        Map<String, Long> projectId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getId,
                (key1, key2) -> key2));
        //查询当前使用单位下所有的费用类型
        List<FeeItem> feeItemList = getAllFeeItem(company);
        Map<String, Long> itemId = feeItemList.stream().collect(Collectors.toMap(FeeItem::getName, FeeItem::getId,
                (key1, key2) -> key2));
        List<String> nameList = feeItemList.stream().map(FeeItem::getName).collect(Collectors.toList());

        boolean result = true;
        for (ProjectFeeCollectionExcelVo projectFeeCollectionExcelVo : detailImportExcelVoList) {
            List<String> validResultList = new ArrayList<>();

            if (StringUtils.isEmpty(projectFeeCollectionExcelVo.getProjectCode())) {
                result = false;
                validResultList.add("(项目编号-新)不能为空");
            } else {
                if (ObjectUtils.isEmpty(projectId.get(projectFeeCollectionExcelVo.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目编号-新)不存在");
                } else {
                    projectFeeCollectionExcelVo.setProjectId(projectId.get(projectFeeCollectionExcelVo.getProjectCode()));
                }
            }

            if (StringUtils.isEmpty(projectFeeCollectionExcelVo.getFeeItemName())) {
                result = false;
                validResultList.add("费用类型名称不能为空");
            } else {
                if (!nameList.contains(projectFeeCollectionExcelVo.getFeeItemName())) {
                    result = false;
                    validResultList.add("费用类型名称错误");
                } else {
                    if (StringUtils.isNotEmpty(projectFeeCollectionExcelVo.getProjectCode()) && !Objects.isNull(projectId.get(projectFeeCollectionExcelVo.getProjectCode()))) {

                        if ("差旅费".equals(projectFeeCollectionExcelVo.getFeeItemName())) {
                            //根据项目id查询差旅费
                            List<ProjectBudgetTravel> projectBudgetTravels =
                                    getProjectBudgetTravel(projectId.get(projectFeeCollectionExcelVo.getProjectCode()));
                            if (ListUtils.isEmpty(projectBudgetTravels)) {
                                result = false;
                                validResultList.add("该项目不存在差旅费");
                            } else {
                                ProjectBudgetTravel projectBudgetTravel = projectBudgetTravels.get(0);
                                //计算差旅金额
                                BigDecimal travelCost = BigDecimalUtils.add(projectBudgetTravel.getHotel(),
                                                projectBudgetTravel.getSubsidy(), projectBudgetTravel.getTraffic())
                                        .multiply(new BigDecimal(projectBudgetTravel.getNumber()))
                                        .add(projectBudgetTravel.getComeBackTraffic())
                                        .add(projectBudgetTravel.getOther());

                                FeeItemDto feeItemTravel = CacheDataUtils.findFeeItemByFeeFlag(company,
                                        FeeFlagEnum.TRAVEL.getCode());
                                projectFeeCollectionExcelVo.setSourceId(projectBudgetTravel.getId());
                                projectFeeCollectionExcelVo.setFeeItemId(feeItemTravel.getId());
                                projectFeeCollectionExcelVo.setAmount(travelCost);
                                projectFeeCollectionExcelVo.setFeeTypeId(feeItemTravel.getFeeTypeId());
                                projectFeeCollectionExcelVo.setFeeTypeName(feeItemTravel.getFeeTypeName());
                                projectFeeCollectionExcelVo.setPushStatus("2");

                            }
                        } else {
                            //根据项目id、feeItemId查询项目其他预算
                            List<ProjectBudgetFee> projectBudgetFees =
                                    getProjectBudgetFee(projectId.get(projectFeeCollectionExcelVo.getProjectCode()),
                                            itemId.get(projectFeeCollectionExcelVo.getFeeItemName()));
                            if (ListUtils.isEmpty(projectBudgetFees)) {
                                result = false;
                                validResultList.add("该项目不存在其他费用预算");
                            } else {
                                ProjectBudgetFee projectBudgetFee = projectBudgetFees.get(0);
                                projectFeeCollectionExcelVo.setSourceId(projectBudgetFee.getId());
                                projectFeeCollectionExcelVo.setFeeItemId(projectBudgetFee.getFeeItemId());
                                projectFeeCollectionExcelVo.setAmount(projectBudgetFee.getAmount());
                                projectFeeCollectionExcelVo.setPushStatus("2");
                                FeeItemDto feeItem = CacheDataUtils.findFeeItemById(projectBudgetFee.getFeeItemId());
                                if (null != feeItem) {
                                    projectFeeCollectionExcelVo.setFeeItemName(feeItem.getName());
                                    projectFeeCollectionExcelVo.setFeeTypeId(feeItem.getFeeTypeId());
                                    projectFeeCollectionExcelVo.setFeeTypeName(feeItem.getFeeTypeName());
                                }
                            }

                        }
                    }
                }
            }

            projectFeeCollectionExcelVo.setValidResult(Joiner.on("，").join(validResultList));
        }


        return result;
    }

    private List<ProjectBudgetFee> getProjectBudgetFee(Long projectId, Long feeItemId) {
        ProjectBudgetFeeExample projectBudgetFeeExample = new ProjectBudgetFeeExample();
        ProjectBudgetFeeExample.Criteria criteria = projectBudgetFeeExample.createCriteria();
        criteria.andProjectIdEqualTo(projectId).andFeeItemIdEqualTo(feeItemId);

        return projectBudgetFeeMapper.selectByExample(projectBudgetFeeExample);

    }

    private List<ProjectBudgetTravel> getProjectBudgetTravel(Long projectId) {
        ProjectBudgetTravelExample projectBudgetTravelExample = new ProjectBudgetTravelExample();
        ProjectBudgetTravelExample.Criteria criteria = projectBudgetTravelExample.createCriteria();
        criteria.andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(false);
        return projectBudgetTravelMapper.selectByExample(projectBudgetTravelExample);
    }

    private void saveRevenueCostOrderDetail(List<RevenueCostOrderExcelVo> detailImportExcelVoList) {

        for (RevenueCostOrderExcelVo revenueCostOrderExcelVo : detailImportExcelVoList) {

            RevenueCostOrder revenueCostOrder = BeanConverter.copy(revenueCostOrderExcelVo, RevenueCostOrder.class);
            RevenueCostOrderDetail revenueCostOrderDetail = BeanConverter.copy(revenueCostOrderExcelVo,
                    RevenueCostOrderDetail.class);

            //保存工单头数据
            if (null == revenueCostOrder.getId()) {
                revenueCostOrder.setSource("手工");
                revenueCostOrder.setStatus(3);
                //revenueCostOrder.setCarryoverStatus(true);
                revenueCostOrder.setType("初始化");
                revenueCostOrder.setCreateAt(new Date());
                revenueCostOrder.setCreateBy(SystemContext.getUserId());
                revenueCostOrder.setDeletedFlag(false);
                revenueCostOrderMapper.insert(revenueCostOrder);
            } else {
                revenueCostOrder.setUpdateAt(new Date());
                revenueCostOrder.setUpdateBy(SystemContext.getUserId());
                revenueCostOrderMapper.updateByPrimaryKeySelective(revenueCostOrder);
            }

            //保存工单详细
            if (null == revenueCostOrderDetail.getId()) {
                //revenueCostOrderDetail.setCarryoverStatus(true);
                revenueCostOrderDetail.setIsImport(true);
                revenueCostOrderDetail.setCreateAt(new Date());
                revenueCostOrderDetail.setOrderId(revenueCostOrder.getId());
                revenueCostOrderDetail.setCreateBy(SystemContext.getUserId());
                revenueCostOrderDetail.setDeletedFlag(false);
                revenueCostOrderDetailMapper.insert(revenueCostOrderDetail);
            } else {
                revenueCostOrderDetail.setUpdateAt(new Date());
                revenueCostOrderDetail.setUpdateBy(SystemContext.getUserId());
                revenueCostOrderDetailMapper.updateByPrimaryKeySelective(revenueCostOrderDetail);
            }

        }
    }

    private boolean getValidRevenueCostOrderResults(List<RevenueCostOrderExcelVo> detailImportExcelVoList,
                                                    Long company) {
        //获取昆山使用单位下所有的项目编号
        List<Project> projects = getAllProjectCode(company);
        Map<String, Long> projectId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getId,
                (key1, key2) -> key2));
        Map<String, BigDecimal> amount = projects.stream().filter(project -> {
            if (project.getAmount() == null) {
                project.setAmount(new BigDecimal(0));
            }
            return true;
        }).collect(Collectors.toMap(Project::getCode, Project::getAmount, (key1, key2) -> key2));
        Map<String, BigDecimal> budgetCost = projects.stream().filter(project -> {
            if (null == project.getBudgetCost()) {
                project.setBudgetCost(new BigDecimal(0));
            }
            return true;
        }).collect(Collectors.toMap(Project::getCode, Project::getBudgetCost));

        // 查询当前使用单位下的业务实体
        List<OperatingUnitDto> operatingUnitDtos = basedataExtService.queryCurrentUnitOu(company);
        Map<String, Long> ouMap =
                operatingUnitDtos.stream().collect(Collectors.toMap(OperatingUnitDto::getOperatingUnitName,
                        OperatingUnitDto::getOperatingUnitId, (key1, key2) -> key2));

        //获取使用单位下所有项目类型
        Map<String, Long> projectType = getProjectTypeByUnitId(company);

        boolean result = true;
        for (RevenueCostOrderExcelVo revenueCostOrderExcelVo : detailImportExcelVoList) {
            List<String> validResultList = new ArrayList<>();

            if (StringUtils.isEmpty(revenueCostOrderExcelVo.getOrderCode())) {
                result = false;
                validResultList.add("收入成本工单号不能为空");
            }

            if (StringUtils.isEmpty(revenueCostOrderExcelVo.getBillNum())) {
               /* result = false;
                validResultList.add("对应结转单号不能为空");*/
            } else {
                //根据结转单号查询结转单信息
                if (StringUtils.isNotEmpty(revenueCostOrderExcelVo.getProjectCode()) && projectId.containsKey(revenueCostOrderExcelVo.getProjectCode())) {

                    List<CarryoverBill> carryoverBills = getCarryoverBill(revenueCostOrderExcelVo.getBillNum(),
                            projectId.get(revenueCostOrderExcelVo.getProjectCode()));
                    if (ListUtils.isEmpty(carryoverBills)) {
                        result = false;
                        validResultList.add("对应结转单号错误");
                    } else {
                        revenueCostOrderExcelVo.setCarryoverBillId(carryoverBills.get(0).getId());
                    }
                }
            }

            if (StringUtils.isEmpty(revenueCostOrderExcelVo.getProjectCode())) {
                result = false;
                validResultList.add("(项目编号-新)不能为空");
            } else {
                if (ObjectUtils.isEmpty(projectId.get(revenueCostOrderExcelVo.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目编号-新)不存在");
                } else {
                    revenueCostOrderExcelVo.setProjectId(projectId.get(revenueCostOrderExcelVo.getProjectCode()));
                }
            }

            if (StringUtils.isEmpty(revenueCostOrderExcelVo.getProjectType())) {
                result = false;
                validResultList.add("项目类型不能为空");
            } else {
                if (!projectType.containsKey(revenueCostOrderExcelVo.getProjectType())) {
                    result = false;
                    validResultList.add("项目类型错误");
                }
            }

            if (StringUtils.isEmpty(revenueCostOrderExcelVo.getCustomerNum())) {
                result = false;
                validResultList.add("客户编码不能为空");
            } else {
                //根据客户编码查询客户名称
                List<Customer> customerByCode = getCustomerByCode(revenueCostOrderExcelVo.getCustomerNum());
                if (ListUtils.isEmpty(customerByCode)) {
                    result = false;
                    validResultList.add("该CRM客户编码不存在");
                } else {
                    if (StringUtils.isNotEmpty(revenueCostOrderExcelVo.getCustomerName())) {
                        if (!Objects.equals(revenueCostOrderExcelVo.getCustomerName(),
                                customerByCode.get(0).getName())) {
                            result = false;
                            validResultList.add("客户名称错误");
                        } else {
                            revenueCostOrderExcelVo.setCustomerId(customerByCode.get(0).getId());
                        }
                    }
                }
            }

            if (StringUtils.isEmpty(revenueCostOrderExcelVo.getCustomerName())) {
                result = false;
                validResultList.add("客户名称不能为空");
            }

            if (StringUtils.isEmpty(revenueCostOrderExcelVo.getCurrency())) {
                result = false;
                validResultList.add("币种不能为空");
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getMainIncome())) {
                result = false;
                validResultList.add("收入总额不能为空");
            } else {
                if (StringUtils.isNotEmpty(revenueCostOrderExcelVo.getProjectCode())) {
                    if (!Objects.equals(amount.get(revenueCostOrderExcelVo.getProjectCode()).setScale(2,
                                    BigDecimal.ROUND_DOWN),
                            revenueCostOrderExcelVo.getMainIncome().setScale(2, BigDecimal.ROUND_HALF_UP))) {
                        result = false;
                        validResultList.add("收入总额错误");
                    } else {
                        revenueCostOrderExcelVo.setMainIncome(revenueCostOrderExcelVo.getMainIncome().setScale(2,
                                BigDecimal.ROUND_HALF_UP));
                    }
                }
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getMainBudget())) {
                result = false;
                validResultList.add("预算总额不能为空");
            } else {

                if (StringUtils.isNotEmpty(revenueCostOrderExcelVo.getProjectCode())) {
                    if (!Objects.equals(budgetCost.get(revenueCostOrderExcelVo.getProjectCode()).setScale(2,
                                    BigDecimal.ROUND_DOWN),
                            revenueCostOrderExcelVo.getMainBudget().setScale(2, BigDecimal.ROUND_HALF_UP))) {
                        result = false;
                        validResultList.add("预算总额错误");
                    } else {
                        revenueCostOrderExcelVo.setMainBudget(revenueCostOrderExcelVo.getMainBudget().setScale(2,
                                BigDecimal.ROUND_HALF_UP));
                    }
                }
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getRevenue())) {
                result = false;
                validResultList.add("工单收入总额不能为空");
            } else {
                revenueCostOrderExcelVo.setRevenue(revenueCostOrderExcelVo.getRevenue().setScale(2,
                        BigDecimal.ROUND_UP));
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getCost())) {
                result = false;
                validResultList.add("工单成本总额不能为空");
            } else {
                revenueCostOrderExcelVo.setCost(revenueCostOrderExcelVo.getCost().setScale(2, BigDecimal.ROUND_UP));
            }

            if (StringUtils.isEmpty(revenueCostOrderExcelVo.getMilepostName())) {
                result = false;
                validResultList.add("节点名称不能为空");
            } else {
                //根据项目id查询收入成本计划汇总信息
                if (StringUtils.isNotEmpty(revenueCostOrderExcelVo.getProjectCode()) && projectId.containsKey(revenueCostOrderExcelVo.getProjectCode())) {
                    ProjectProfit projectProfit =
                            getProjectProfit(projectId.get(revenueCostOrderExcelVo.getProjectCode()));
                    String value = CostMethod.getValue(projectProfit.getCostMethodMain());
                    revenueCostOrderExcelVo.setCostMethodMain(value);
                    if (null != projectProfit) {
                        if (IncomePoint.MILEPOST.getCode().equals(projectProfit.getIncomePointMain())) { //此次导入数据只针对主里程碑
                            //根据项目id和节点名称查询项目里程碑
                            //根据收入节点名称和项目id查询里程碑信息
                            List<ProjectMilepost> projectMileposts =
                                    getProjectMilepostDetail(projectId.get(revenueCostOrderExcelVo.getProjectCode()),
                                            revenueCostOrderExcelVo.getMilepostName());
                            if (ListUtils.isEmpty(projectMileposts)) {
                                result = false;
                                validResultList.add("节点名称错误");
                            } else {
                                revenueCostOrderExcelVo.setMilepostId(projectMileposts.get(0).getId());
                            }
                        } else if (IncomePoint.MONTH.getCode().equals(projectProfit.getIncomePointMain())) {
                            //根据收入确认节点和项目id查询收入成本计划信息
                            List<ProjectIncomeCostPlan> projectIncomeCostPlans =
                                    getProjectIncomeCostPlanDetail(projectId.get(revenueCostOrderExcelVo.getProjectCode()),
                                            revenueCostOrderExcelVo.getMilepostName());
                            if (ListUtils.isEmpty(projectIncomeCostPlans)) {
                                result = false;
                                validResultList.add("节点名称错误");
                            } else {
                                revenueCostOrderExcelVo.setMilepostId(projectIncomeCostPlans.get(0).getId());
                            }

                        }
                    }
                }
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getMaterialOutsourceCost())) {
                result = false;
                validResultList.add("物料外包-成本不能为空");
            } else {
                revenueCostOrderExcelVo.setMaterialOutsourceCost(revenueCostOrderExcelVo.getMaterialOutsourceCost().setScale(2, BigDecimal.ROUND_UP));
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getMaterialCost())) {
                result = false;
                validResultList.add("物料-成本不能为空");
            } else {
                revenueCostOrderExcelVo.setMaterialCost(revenueCostOrderExcelVo.getMaterialCost().setScale(2,
                        BigDecimal.ROUND_UP));
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getInnerLaborCost())) {
                result = false;
                validResultList.add("内部人工-成本不能为空");
            } else {
                revenueCostOrderExcelVo.setInnerLaborCost(revenueCostOrderExcelVo.getInnerLaborCost().setScale(2,
                        BigDecimal.ROUND_UP));
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getOuterLaborCost())) {
                result = false;
                validResultList.add("外部人工-成本不能为空");
            } else {
                revenueCostOrderExcelVo.setOuterLaborCost(revenueCostOrderExcelVo.getOuterLaborCost().setScale(2,
                        BigDecimal.ROUND_UP));
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getFeeCost())) {
                result = false;
                validResultList.add("差旅费用不能为空");
            } else {
                revenueCostOrderExcelVo.setFeeCost(revenueCostOrderExcelVo.getFeeCost().setScale(2,
                        BigDecimal.ROUND_UP));
            }
            if (Objects.isNull(revenueCostOrderExcelVo.getOtherCost())) {
                result = false;
                validResultList.add("其他费用不能为空");
            } else {
                revenueCostOrderExcelVo.setOtherCost(revenueCostOrderExcelVo.getOtherCost().setScale(2,
                        BigDecimal.ROUND_UP));
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getSubTotal())) {
                result = false;
                validResultList.add("成本小计不能为空");
            } else {
                revenueCostOrderExcelVo.setSubTotal(revenueCostOrderExcelVo.getSubTotal().setScale(2,
                        BigDecimal.ROUND_UP));
            }

            if (StringUtils.isEmpty(revenueCostOrderExcelVo.getOuName())) {
                result = false;
                validResultList.add("业务实体不能为空");
            } else {
                if (!ouMap.containsKey(revenueCostOrderExcelVo.getOuName())) {
                    result = false;
                    validResultList.add("业务实体错误");
                } else {
                    revenueCostOrderExcelVo.setOuId(ouMap.get(revenueCostOrderExcelVo.getOuName()));
                }
            }

            if (StringUtils.isEmpty(revenueCostOrderExcelVo.getCarryoverStatusName())) {
                result = false;
                validResultList.add("是否结转不能为空");
            } else {
                if ("是".equals(revenueCostOrderExcelVo.getCarryoverStatusName())) {
                    revenueCostOrderExcelVo.setCarryoverStatus(true);
                } else if ("否".equals(revenueCostOrderExcelVo.getCarryoverStatusName())) {
                    revenueCostOrderExcelVo.setCarryoverStatus(false);
                } else {
                    result = false;
                    validResultList.add("是否结转错误");
                }
            }

            if (Objects.isNull(revenueCostOrderExcelVo.getInputDate())) {
                result = false;
                validResultList.add("录入时间不能为空");
            }

            revenueCostOrderExcelVo.setValidResult(Joiner.on("，").join(validResultList));
        }

        return result;
    }

    private List<CarryoverBill> getCarryoverBill(String billNum, Long projectId) {
        CarryoverBillExample carryoverBillExample = new CarryoverBillExample();
        CarryoverBillExample.Criteria criteria = carryoverBillExample.createCriteria();
        criteria.andBillNumEqualTo(billNum).andProjectIdEqualTo(projectId);

        return carryoverBillMapper.selectByExample(carryoverBillExample);
    }

    private ProjectProfit getProjectProfit(Long projectId) {
        ProjectProfitExample projectProfitExample = new ProjectProfitExample();
        ProjectProfitExample.Criteria criteria = projectProfitExample.createCriteria();
        criteria.andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(false);
        List<ProjectProfit> projectProfits = projectProfitMapper.selectByExample(projectProfitExample);
        if (ListUtils.isNotEmpty(projectProfits)) {
            return projectProfits.get(0);
        }
        return null;
    }

    private void saveProjectProfitDetail(List<ProjectProfitExcelVo> detailImportExcelVoList) {

        List<ProjectProfit> projectProfits = BeanConverter.copy(detailImportExcelVoList, ProjectProfit.class);
        List<Long> projectIds = new ArrayList<>();
        for (ProjectProfit projectProfit : projectProfits) {
            if (!projectIds.contains(projectProfit.getProjectId())) {
                saveProjectProfit(projectProfit); //保存收入成本计划
                projectIds.add(projectProfit.getProjectId());
            }
        }
    }

    private boolean getValidProjectProfitResults(List<ProjectProfitExcelVo> detailImportExcelVoList, Long company) {
        //获取昆山使用单位下所有的项目编号
        List<Project> projects = getAllProjectCode(company);
        Map<String, Long> projectId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getId,
                (key1, key2) -> key2));
        boolean result = true;
        List<String> projectCodeList = new ArrayList<>();
        for (ProjectProfitExcelVo projectProfitExcelVo : detailImportExcelVoList) {
            List<String> validResultList = new ArrayList<>();

            if (StringUtils.isEmpty(projectProfitExcelVo.getProjectCode())) {
                result = false;
                validResultList.add("项目编码不能为空");
            } else {
                if (ObjectUtils.isEmpty(projectId.get(projectProfitExcelVo.getProjectCode()))) {
                    result = false;
                    validResultList.add("项目编码不存在");
                } else {
                    projectProfitExcelVo.setProjectId(projectId.get(projectProfitExcelVo.getProjectCode()));
                }
            }

            if (StringUtils.isNotEmpty(projectProfitExcelVo.getProjectCode())) {
                if (ListUtils.isNotEmpty(projectCodeList) && projectCodeList.contains(projectProfitExcelVo.getProjectCode())) {
                    result = false;
                    validResultList.add("项目编码:" + projectProfitExcelVo.getProjectCode() + "重复");
                }
                projectCodeList.add(projectProfitExcelVo.getProjectCode());
            }

            if (Objects.isNull(projectProfitExcelVo.getOuId())) {
                result = false;
                validResultList.add("业务实体不能为空");
            }

            if (Objects.isNull(projectProfitExcelVo.getStorageId())) {
                result = false;
                validResultList.add("库存组织不能为空");
            }

            if (StringUtils.isEmpty(projectProfitExcelVo.getStorageCode())) {
                result = false;
                validResultList.add("库存组织编码不能为空");
            }

            if (Objects.isNull(projectProfitExcelVo.getBudgetDepId())) {
                result = false;
                validResultList.add("预算部门不能为空");
            }

            if (StringUtils.isEmpty(projectProfitExcelVo.getCurrency())) {
                result = false;
                validResultList.add("币种不能为空");
            }

            if (StringUtils.isEmpty(projectProfitExcelVo.getCostMethodMain())) {
                result = false;
                validResultList.add("成本方法不能为空");
            }

            if (Objects.isNull(projectProfitExcelVo.getMainIncome())) {
                result = false;
                validResultList.add("主-收入总额不能为空");
            } else {
                projectProfitExcelVo.setMainIncome(projectProfitExcelVo.getMainIncome().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(projectProfitExcelVo.getMainBudget())) {
                result = false;
                validResultList.add("主-预算总额不能为空");
            } else {
                projectProfitExcelVo.setMainBudget(projectProfitExcelVo.getMainBudget().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            projectProfitExcelVo.setValidResult(Joiner.on("，").join(validResultList));

        }
        return result;
    }

    private void saveCarryoverBill(List<CarryoverBillDto> carryoverBillDtoList) {
        List<CarryoverBill> carryoverBills = BeanConverter.copy(carryoverBillDtoList, CarryoverBill.class);

        for (CarryoverBill carryoverBill : carryoverBills) {

            CarryoverBillExample carryoverBillExample = new CarryoverBillExample();
            CarryoverBillExample.Criteria criteria = carryoverBillExample.createCriteria();
            criteria.andBillNumEqualTo(carryoverBill.getBillNum());

            List<CarryoverBill> carryoverBillList = carryoverBillMapper.selectByExample(carryoverBillExample);

            // 导入结转单
            DataResponse<DictDto> dictDtoDataResponse = basedataLtcDictFeignClient.getByTypeAndName("cost_method",
                    carryoverBill.getCostMethodName());
            String costMethod = dictDtoDataResponse.getData().getCode();
            carryoverBill.setCostMethod(costMethod);

            if (ListUtils.isNotEmpty(carryoverBillList)) {
                carryoverBill.setId(carryoverBillList.get(0).getId());
                carryoverBill.setUpdateAt(new Date());
                carryoverBill.setUpdateBy(SystemContext.getUserId());

                carryoverBillMapper.updateByPrimaryKeySelective(carryoverBill);
            } else {
                if (null == carryoverBill.getId()) {
                    carryoverBill.setCreateAt(new Date());
                    carryoverBill.setCreateBy(SystemContext.getUserId());
                    carryoverBill.setDeletedFlag(false);
                    carryoverBillMapper.insert(carryoverBill);
                    //保存结转单收入明细
                    saveCarryoverBillIncomeColllection(carryoverBill);
                    //将结转单信息写入到收入成本计划（月度）+收入成本计划（里程碑）
                    if (Objects.equals("月度确认", carryoverBill.getIncomePointName())) {
                        projectIncomeCostPlanService.updateByProjectIdAndName(carryoverBill);
                    } else if (Objects.equals("里程碑节点确认", carryoverBill.getIncomePointName())) {
                        projectMilepostService.updateByProjectIdAndName(carryoverBill);
                    }
                } else {
                    carryoverBill.setUpdateBy(SystemContext.getUserId());
                    carryoverBill.setUpdateAt(new Date());
                    carryoverBillMapper.updateByPrimaryKeySelective(carryoverBill);
                }
            }

        }

    }

    private void saveCarryoverBillIncomeColllection(CarryoverBill carryoverBill) {
        CarryoverBillIncomeCollection carryoverBillIncomeCollection = BeanConverter.copy(carryoverBill,
                CarryoverBillIncomeCollection.class);
        carryoverBillIncomeCollection.setId(null);
        carryoverBillIncomeCollection.setCarryoverBillId(carryoverBill.getId());
        carryoverBillIncomeCollection.setStandardCurrentIncomeAmount(carryoverBill.getStandardCurrentIncomeTotalAmount());
        carryoverBillIncomeCollection.setCarryStatus(true);
        carryoverBillIncomeCollection.setDeletedFlag(false);
        carryoverBillIncomeCollection.setType(1);

        carryoverBillIncomeCollectionMapper.insert(carryoverBillIncomeCollection);
    }

    private boolean getValidCarryoverBillResults(List<CarryoverBillDto> carryoverBillDtoList, Long company) {
        //获取使用单位下所有的项目编号
        List<Project> projects = getAllProjectCode(company);
        Map<String, Long> projectId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getId,
                (key1, key2) -> key2));
        Map<String, String> projectName = projects.stream().collect(Collectors.toMap(Project::getCode,
                Project::getName, (key1, key2) -> key2));
        Map<String, BigDecimal> amount = projects.stream().filter(project -> {
            if (null == project.getAmount()) {
                project.setAmount(new BigDecimal(0));
            }
            return true;
        }).collect(Collectors.toMap(Project::getCode, Project::getAmount, (key1, key2) -> key2));
/*        Map<String,BigDecimal> budgetCost = projects.stream().filter(project -> {
            if (null == project.getBudgetCost()) {
                project.setBudgetCost(new BigDecimal(0));
            }
            return true;
        }).collect(Collectors.toMap(Project::getCode, Project::getBudgetCost));*/

        //获取使用单位下所有项目类型
        Map<String, Long> projectType = getProjectTypeByUnitId(company);

        // 查询当前使用单位下的业务实体
        List<OperatingUnitDto> operatingUnitDtos = basedataExtService.queryCurrentUnitOu(company);
        Map<String, Long> ouMap =
                operatingUnitDtos.stream().collect(Collectors.toMap(OperatingUnitDto::getOperatingUnitName,
                        OperatingUnitDto::getOperatingUnitId, (key1, key2) -> key2));

        boolean result = true;
        for (CarryoverBillDto carryoverBillDto : carryoverBillDtoList) {
            List<String> validResultList = new ArrayList<>();

            if (StringUtils.isEmpty(carryoverBillDto.getCarryoverBatchNum())) {
               /* result = false;
                validResultList.add("执行批次不能为空");*/
            }

            if (StringUtils.isEmpty(carryoverBillDto.getBillNum())) {
                result = false;
                validResultList.add("结转单号不能为空");
            }

            if (StringUtils.isEmpty(carryoverBillDto.getPeriodName())) {
                result = false;
                validResultList.add("会计期间不能为空");
            }

            if (StringUtils.isEmpty(carryoverBillDto.getProjectNum())) {
                result = false;
                validResultList.add("(项目编号-新)不能为空");
                carryoverBillDto.setValidResult(Joiner.on("，").join(validResultList));
                continue;
            } else {
                if (ObjectUtils.isEmpty(projectId.get(carryoverBillDto.getProjectNum()))) {
                    result = false;
                    validResultList.add("(项目编号-新)不存在");
                    carryoverBillDto.setValidResult(Joiner.on("，").join(validResultList));
                    continue;
                } else {
                    carryoverBillDto.setProjectId(projectId.get(carryoverBillDto.getProjectNum()));
                    carryoverBillDto.setProjectName(projectName.get(carryoverBillDto.getProjectNum()));
                }
            }

            if (StringUtils.isEmpty(carryoverBillDto.getProjectTypeName())) {
                result = false;
                validResultList.add("项目类型不能为空");
            } else {
                if (!projectType.containsKey(carryoverBillDto.getProjectTypeName())) {
                    result = false;
                    validResultList.add("项目类型错误");
                } else {
                    carryoverBillDto.setProjectType(projectType.get(carryoverBillDto.getProjectTypeName()));
                }
            }

            if (StringUtils.isEmpty(carryoverBillDto.getCostMethodName())) {
                result = false;
                validResultList.add("收入成本方法不能为空");
            } else {
                if (CostMethod.COST.getName().equals(carryoverBillDto.getCostMethodName())) {
                    carryoverBillDto.setCostMethod(CostMethod.COST.getCode());
                } else if (CostMethod.INCOME.getName().equals(carryoverBillDto.getCostMethodName())) {
                    carryoverBillDto.setCostMethod(CostMethod.INCOME.getCode());
                } else if (CostMethod.FAT_INCOME.getName().equals(carryoverBillDto.getCostMethodName())) {
                    if (Objects.isNull(carryoverBillDto.getAccumulatedCarryoverCost())) {
                        result = false;
                        validResultList.add("累计应结转成本(用于FAT+里程碑)不能为空");
                    }
                    if (Objects.isNull(carryoverBillDto.getShouldCarryoverCost())) {
                        result = false;
                        validResultList.add("累计成本发生额(用于FAT+里程碑)不能为空");
                    }
                    if (Objects.isNull(carryoverBillDto.getSeq())) {
                        result = false;
                        validResultList.add("排序不能为空");
                    }
                    carryoverBillDto.setCostMethod(CostMethod.FAT_INCOME.getCode());
                } else if (CostMethod.PLAN_INCOME.getName().equals(carryoverBillDto.getCostMethodName())) {
                    if (Objects.isNull(carryoverBillDto.getAccumulatedCarryoverCost())) {
                        result = false;
                        validResultList.add("累计应结转成本(用于FAT+里程碑)不能为空");
                    }
                    if (Objects.isNull(carryoverBillDto.getShouldCarryoverCost())) {
                        result = false;
                        validResultList.add("累计成本发生额(用于FAT+里程碑)不能为空");
                    }
                    if (Objects.isNull(carryoverBillDto.getSeq())) {
                        result = false;
                        validResultList.add("排序不能为空");
                    }
                    carryoverBillDto.setCostMethod(CostMethod.PLAN_INCOME.getCode());
                } else {
                    result = false;
                    validResultList.add("收入成本方法错误");
                }
            }

            if (StringUtils.isEmpty(carryoverBillDto.getIncomePointName())) {
                result = false;
                validResultList.add("收入确认时点不能为空");
            } else {
                if ("里程碑节点确认".equals(carryoverBillDto.getIncomePointName())) {
                    carryoverBillDto.setIncomePoint(IncomePoint.MILEPOST.getCode());
                    carryoverBillDto.setResourceType(CarryoverBillResourceType.MILEPOST.getCode());
                } else if ("月度确认".equals(carryoverBillDto.getIncomePointName())) {
                    carryoverBillDto.setIncomePoint(IncomePoint.MONTH.getCode());
                    carryoverBillDto.setResourceType(CarryoverBillResourceType.INCOME_COST_PLAN.getCode());
                } else {
                    result = false;
                    validResultList.add("收入确认时点错误");
                }
            }

            if (StringUtils.isEmpty(carryoverBillDto.getProjectMilepostName())) {
                result = false;
                validResultList.add("收入确认节点不能为空");
            } else {
                if (StringUtils.isNotEmpty(carryoverBillDto.getProjectNum())) {
                    if (Objects.equals("里程碑节点确认", carryoverBillDto.getIncomePointName())) { //里程碑节点确认时
                        //根据收入节点名称和项目id查询里程碑信息
                        List<ProjectMilepost> projectMileposts =
                                getProjectMilepostDetail(projectId.get(carryoverBillDto.getProjectNum()),
                                        carryoverBillDto.getProjectMilepostName());
                        //查询总确认期数
                        Integer confirmationFromPM =
                                getConfirmationFromPM(projectId.get(carryoverBillDto.getProjectNum()));
                        carryoverBillDto.setPeriodTotalConfirmation(confirmationFromPM);
                        if (ListUtils.isEmpty(projectMileposts)) {
                            result = false;
                            validResultList.add("收入确认节点错误");
                        } else {
                            carryoverBillDto.setProjectMilepostId(projectMileposts.get(0).getId());
                        }
                    } else if (Objects.equals("月度确认", carryoverBillDto.getIncomePointName())) { //月度确认节点时
                        //根据收入确认节点和项目id查询收入成本计划信息
                        List<ProjectIncomeCostPlan> projectIncomeCostPlans =
                                getProjectIncomeCostPlanDetail(projectId.get(carryoverBillDto.getProjectNum()),
                                        carryoverBillDto.getProjectMilepostName());
                        //查询总确认期数
                        Integer confirmationFromCp =
                                getConfirmationFromCp(projectId.get(carryoverBillDto.getProjectNum()));
                        carryoverBillDto.setPeriodTotalConfirmation(confirmationFromCp);
                        if (ListUtils.isEmpty(projectIncomeCostPlans)) {
                            result = false;
                            validResultList.add("收入确认节点错误");
                        } else {
                            carryoverBillDto.setProjectMilepostId(projectIncomeCostPlans.get(0).getId());
                        }
                    } else {
                        result = false;
                        validResultList.add("收入确认节点错误");
                    }
                }
            }

            if (StringUtils.isEmpty(carryoverBillDto.getCustomerNum())) {
                result = false;
                validResultList.add("客户编码不能为空");
            } else {
                //根据客户编码查询客户名称
                List<Customer> customerByCode = getCustomerByCode(carryoverBillDto.getCustomerNum());
                if (ListUtils.isEmpty(customerByCode)) {
                    result = false;
                    validResultList.add("该CRM客户编码不存在");
                } else {
                    if (StringUtils.isNotEmpty(carryoverBillDto.getCustomerName())) {
                        if (!Objects.equals(carryoverBillDto.getCustomerName(), customerByCode.get(0).getName())) {
                            result = false;
                            validResultList.add("客户名称错误");
                        } else {
                            carryoverBillDto.setCustomerId(customerByCode.get(0).getId());
                        }
                    }
                }
            }

            if (StringUtils.isEmpty(carryoverBillDto.getCustomerName())) {
                result = false;
                validResultList.add("客户名称不能为空");
            }

            if (StringUtils.isEmpty(carryoverBillDto.getLocalCurrency())) {
                result = false;
                validResultList.add("币种不能为空");
            }

            if (Objects.isNull(carryoverBillDto.getTotalIncome())) {
                result = false;
                validResultList.add("收入总额不能为空");
            } else {
                if (StringUtils.isNotEmpty(carryoverBillDto.getProjectNum())) {
                    BigDecimal projectAmount = amount.get(carryoverBillDto.getProjectNum());
                    projectAmount = projectAmount != null ? projectAmount : BigDecimal.ZERO;
                    if (!Objects.equals(projectAmount.setScale(2, BigDecimal.ROUND_DOWN),
                            carryoverBillDto.getTotalIncome().setScale(2, BigDecimal.ROUND_HALF_UP))) {
                        result = false;
                        validResultList.add("结转单收入总额与项目金额不一致");
                    } else {
                        carryoverBillDto.setTotalIncome(carryoverBillDto.getTotalIncome().setScale(2,
                                BigDecimal.ROUND_HALF_UP));
                    }
                }
            }

            if (Objects.isNull(carryoverBillDto.getTotalBudget())) {
                result = false;
                validResultList.add("预算总额不能为空");
            } else {
                if (StringUtils.isNotEmpty(carryoverBillDto.getProjectNum())) {
                    ProjectExample projectExample = new ProjectExample();
                    ProjectExample.Criteria criteria = projectExample.createCriteria();
                    criteria.andCodeEqualTo(carryoverBillDto.getProjectNum()).andDeletedFlagEqualTo(false);
                    List<Project> projectList = projectMapper.selectByExample(projectExample);
                    BigDecimal budgetCost = BigDecimal.ZERO;
                    if (ListUtils.isNotEmpty(projectList)) {
                        Project project = projectList.get(0);
                        if (null != project.getBudgetCost()) {
                            budgetCost = project.getBudgetCost();
                        }
                    }
                    if (!Objects.equals(budgetCost.setScale(2, BigDecimal.ROUND_DOWN),
                            carryoverBillDto.getTotalBudget().setScale(2, BigDecimal.ROUND_HALF_UP))) {
                        result = false;
                        validResultList.add("预算总额错误");
                    } else {
                        carryoverBillDto.setTotalBudget(carryoverBillDto.getTotalBudget().setScale(2,
                                BigDecimal.ROUND_HALF_UP));
                    }
                }
            }

            if (Objects.isNull(carryoverBillDto.getCurrentIncomeAmount())) {
                result = false;
                validResultList.add("本期确认收入金额不能为空");
            } else {
                carryoverBillDto.setCurrentIncomeAmount(carryoverBillDto.getCurrentIncomeAmount().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(carryoverBillDto.getCurrentCostActual())) {
                result = false;
                validResultList.add("本期确认实际成本不能为空");
            } else {
                carryoverBillDto.setCurrentCostActual(carryoverBillDto.getCurrentCostActual().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(carryoverBillDto.getCurrentIncomeTotalAmount())) {
                result = false;
                validResultList.add("本期确认收入总额不能为空");
            } else {
                carryoverBillDto.setCurrentIncomeTotalAmount(carryoverBillDto.getCurrentIncomeTotalAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(carryoverBillDto.getCumulativeIncomeTotal())) {
                result = false;
                validResultList.add("累计确认收入总额不能为空");
            } else {
                carryoverBillDto.setCumulativeIncomeTotal(carryoverBillDto.getCumulativeIncomeTotal().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(carryoverBillDto.getCurrentIncomePercent())) {
                result = false;
                validResultList.add("本期确认收入比例不能为空");
            } else {
                carryoverBillDto.setCurrentIncomePercent(carryoverBillDto.getCurrentIncomePercent().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(carryoverBillDto.getCumulativeIncomePercent())) {
                result = false;
                validResultList.add("累计收入比例不能为空");
            } else {
                carryoverBillDto.setCumulativeIncomePercent(carryoverBillDto.getCumulativeIncomePercent().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

/*            if (StringUtils.isEmpty(carryoverBillDto.getPeriodTotalConfirmation())) {
                result = false;
                validResultList.add("总确认期数不能为空");
            }*/

            if (Objects.isNull(carryoverBillDto.getPeriodConfirmed())) {
                result = false;
                validResultList.add("已确认期数不能为空");
            }

            if (Objects.isNull(carryoverBillDto.getIncomeMilepostActualEndTime())) {
                result = false;
                validResultList.add("节点实际结束日期不能为空");
            }

            if (Objects.isNull(carryoverBillDto.getSource())) {
                /*result = false;
                validResultList.add("来源不能为空");*/
            }

            if (StringUtils.isEmpty(carryoverBillDto.getOuName())) {
                result = false;
                validResultList.add("业务实体不能为空");
            } else {
                if (!ouMap.containsKey(carryoverBillDto.getOuName())) {
                    result = false;
                    validResultList.add("业务实体错误");
                } else {
                    carryoverBillDto.setOuId(ouMap.get(carryoverBillDto.getOuName()));
                }
            }

            if (Objects.isNull(carryoverBillDto.getGrossProfitRate())) {
                result = false;
                validResultList.add("毛利率不能为空");
            } else {
                carryoverBillDto.setGrossProfitRate(carryoverBillDto.getGrossProfitRate().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            carryoverBillDto.setSpreadAdjustmentAmount(new BigDecimal(0)); //默认给0
            carryoverBillDto.setSource(3); //默认“手工”
            carryoverBillDto.setStatus(1); //默认“正式”
            carryoverBillDto.setErpSyncStatus(1); //默认“已同步”
            carryoverBillDto.setReverseStatus(1); //默认“未冲销”

            carryoverBillDto.setValidResult(Joiner.on("，").join(validResultList));

        }

        return result;
    }

    private Integer getConfirmationFromCp(Long projectId) {
        return projectIncomeCostPlanExtMapper.getConfirmationFromCp(projectId);
    }

    private Integer getConfirmationFromPM(Long projectId) {
        return projectMilepostExtMapper.getConfirmationFromPM(projectId);
    }

    private List<ProjectIncomeCostPlan> getProjectIncomeCostPlanDetail(Long projectId, String name) {
        return projectIncomeCostPlanService.getProjectIncomeCostPlanDetail(projectId, name);

    }

    private List<ProjectMilepost> getProjectMilepostDetail(Long projectId, String name) {
        return projectMilepostService.getProjectMilepostDetail(projectId, name);
    }

    private void saveProjectIncomeOrProjectProfit(List<ProjectIncomeOrProjectProfitExcelVo> detailImportExcelVoList) {

        for (ProjectIncomeOrProjectProfitExcelVo projectIncomeOrProjectProfitExcelVo : detailImportExcelVoList) {
            if (IncomePoint.MONTH.getCode().equals(projectIncomeOrProjectProfitExcelVo.getIncomePoint())) { //月度
                ProjectIncomeCostPlan projectIncomeCostPlan = BeanConverter.copy(projectIncomeOrProjectProfitExcelVo, ProjectIncomeCostPlan.class);
                ProjectIncomeCostPlanExample projectIncomeCostPlanExample = new ProjectIncomeCostPlanExample();
                ProjectIncomeCostPlanExample.Criteria criteria = projectIncomeCostPlanExample.createCriteria();
                criteria.andProjectIdEqualTo(projectIncomeCostPlan.getProjectId()).andNameEqualTo(projectIncomeCostPlan.getName()).andDeletedFlagEqualTo(false);
                List<ProjectIncomeCostPlan> projectIncomeCostPlans = projectIncomeCostPlanMapper.selectByExample(projectIncomeCostPlanExample);
                if (ListUtils.isNotEmpty(projectIncomeCostPlans)) {
                    projectIncomeCostPlan.setId(projectIncomeCostPlans.get(0).getId());
                    projectIncomeCostPlan.setUpdateAt(new Date());
                    projectIncomeCostPlan.setUpdateBy(SystemContext.getUserId());
                    projectIncomeCostPlanMapper.updateByPrimaryKeySelective(projectIncomeCostPlan);
                } else {
                    saveProjectIncomePlan(projectIncomeCostPlan); //保存收入成本计划
                }

            } else if (IncomePoint.MILEPOST.getCode().equals(projectIncomeOrProjectProfitExcelVo.getIncomePoint())) {  //里程碑

                //修改项目里程碑
                ProjectMilepost projectMilepost = new ProjectMilepost();
                projectMilepost.setProjectId(projectIncomeOrProjectProfitExcelVo.getProjectId());
                projectMilepost.setIncomeFlag(projectIncomeOrProjectProfitExcelVo.getIncomeFlag());
                projectMilepost.setIncomeRatio(projectIncomeOrProjectProfitExcelVo.getIncomeRatio());
                projectMilepost.setIncome(projectIncomeOrProjectProfitExcelVo.getIncomeAmount());
                projectMilepost.setCostRatio(projectIncomeOrProjectProfitExcelVo.getCostRatio());
                projectMilepost.setCost(projectIncomeOrProjectProfitExcelVo.getCostAmount());
                projectMilepost.setName(projectIncomeOrProjectProfitExcelVo.getName());
                projectMilepost.setUpdateBy(SystemContext.getUserId());
                projectMilepost.setUpdateAt(new Date());
                if ("0".equals(projectIncomeOrProjectProfitExcelVo.getCarryStatus())) {
                    projectMilepost.setCarryStatus(false);
                } else {
                    projectMilepost.setCarryStatus(true);
                }
                projectMilepostExtMapper.updateByProjectId(projectMilepost);

            }

        }
    }

    private void saveProjectProfit(ProjectProfit projectProfit) {

        ProjectProfitExample projectProfitExample = new ProjectProfitExample();
        ProjectProfitExample.Criteria criteria = projectProfitExample.createCriteria();
        criteria.andProjectIdEqualTo(projectProfit.getProjectId());

        List<ProjectProfit> projectProfits = projectProfitMapper.selectByExample(projectProfitExample);

        if (ListUtils.isNotEmpty(projectProfits)) {
            projectProfit.setId(projectProfits.get(0).getId());
            projectProfit.setUpdateAt(new Date());
            projectProfit.setUpdateBy(SystemContext.getUserId());

            projectProfitMapper.updateByPrimaryKeySelective(projectProfit);
        } else {
            if (null == projectProfit.getId()) {
                projectProfit.setCreateAt(new Date());
                projectProfit.setCreateBy(SystemContext.getUserId());
                projectProfit.setDeletedFlag(false);
                projectProfitMapper.insert(projectProfit);
            } else {
                projectProfit.setUpdateAt(new Date());
                projectProfit.setUpdateBy(SystemContext.getUserId());
                projectProfitMapper.updateByPrimaryKey(projectProfit);
            }
        }
    }

    private void saveProjectIncomePlan(ProjectIncomeCostPlan incomeCostPlan) {
        if (null == incomeCostPlan.getId()) {
            incomeCostPlan.setCreateBy(SystemContext.getUserId());
            incomeCostPlan.setCreateAt(new Date());
            incomeCostPlan.setDeletedFlag(false);
            projectIncomeCostPlanMapper.insert(incomeCostPlan);
        } else {
            incomeCostPlan.setUpdateAt(new Date());
            incomeCostPlan.setUpdateBy(SystemContext.getUserId());
            projectIncomeCostPlanMapper.updateByPrimaryKey(incomeCostPlan);
        }
    }

    private boolean getValidProjectIncomeOrProjectProfitResults(List<ProjectIncomeOrProjectProfitExcelVo> detailImportExcelVoList, Long company) {
        //获取昆山使用单位下所有的项目编号
        List<Project> projects = getAllProjectCode(company);
        Map<String, Long> projectId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getId,
                (key1, key2) -> key2));
        //Map<String,String> projectName = projects.stream().collect(Collectors.toMap(Project::getCode,
        // Project::getName, (key1, key2) -> key2));
        Map<String, Long> ouId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getOuId, (key1
                , key2) -> key2));
        //查询当前使用单位业务实体和库存组织
        List<OperatingUnitDto> operatingUnitDtos = basedataExtService.queryCurrentUnitOu(company);
        //Map<String, Long> ouMap = operatingUnitDtos.stream().collect(Collectors.toMap
        // (OperatingUnitDto::getOperatingUnitName, OperatingUnitDto::getOperatingUnitId, (key1, key2) -> key2));
        Map<Long, List<OrganizationRel>> orgDetail =
                operatingUnitDtos.stream().collect(Collectors.toMap(OperatingUnitDto::getOperatingUnitId,
                        OperatingUnitDto::getRels, (key1, key2) -> key2));


        boolean result = true;
        List<String> projectCodeAndName = new ArrayList<>();
        for (ProjectIncomeOrProjectProfitExcelVo projectIncomeOrProjectProfitExcelVo : detailImportExcelVoList) {
            List<String> validResultList = new ArrayList<>();

            if (StringUtils.isEmpty(projectIncomeOrProjectProfitExcelVo.getProjectCode())) {
                result = false;
                validResultList.add("(项目编号-新)不能为空");
            } else {
                if (ObjectUtils.isEmpty(projectId.get(projectIncomeOrProjectProfitExcelVo.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目编号-新)不存在");
                } else {
                    projectIncomeOrProjectProfitExcelVo.setProjectId(projectId.get(projectIncomeOrProjectProfitExcelVo.getProjectCode()));
                }
                Long operatingUnitId = ouId.get(projectIncomeOrProjectProfitExcelVo.getProjectCode());
                List<OrganizationRel> organizationRels = null;
                if (null != operatingUnitId) {
                    organizationRels = orgDetail.get(operatingUnitId);
                }
                if (ListUtils.isNotEmpty(organizationRels)) {
                    projectIncomeOrProjectProfitExcelVo.setOrId(organizationRels.get(0).getOrganizationId());
                    projectIncomeOrProjectProfitExcelVo.setOrCode(organizationRels.get(0).getOrganizationCode());
                }
            }

            if (StringUtils.isEmpty(projectIncomeOrProjectProfitExcelVo.getName())) {
                result = false;
                validResultList.add("里程碑名称不能为空");
            } else {
                if (StringUtils.isNotEmpty(projectIncomeOrProjectProfitExcelVo.getIncomePointName()) && "里程碑".equals(projectIncomeOrProjectProfitExcelVo.getIncomePointName())) {
                    if (StringUtils.isNotEmpty(projectIncomeOrProjectProfitExcelVo.getProjectCode())) {
                        //根据项目id查询里程碑信息
                        if (!ObjectUtils.isEmpty(projectId.get(projectIncomeOrProjectProfitExcelVo.getProjectCode()))) {
                            List<ProjectMilepost> projectMilepostList =
                                    projectMilepostService.selectByProjectId(projectId.get(projectIncomeOrProjectProfitExcelVo.getProjectCode()));
                            if (ListUtils.isNotEmpty(projectMilepostList)) {
                                Map<String, Integer> orderNumBer =
                                        projectMilepostList.stream().collect(Collectors.toMap(ProjectMilepost::getName,
                                                ProjectMilepost::getOrderNum, (key1, key2) -> key2));
                                if (!orderNumBer.containsKey(projectIncomeOrProjectProfitExcelVo.getName())) {
                                    result = false;
                                    validResultList.add("里程碑节点名称不存在");
                                } else {
                                    if (!Objects.equals(Integer.parseInt(projectIncomeOrProjectProfitExcelVo.getMilepostOrderNum()),
                                            orderNumBer.get(projectIncomeOrProjectProfitExcelVo.getName()))) {
                                        result = false;
                                        validResultList.add("里程碑节点名称错误," + "导入的里程碑序号：" + projectIncomeOrProjectProfitExcelVo.getMilepostOrderNum() + ",配置的序号：" + orderNumBer.get(projectIncomeOrProjectProfitExcelVo.getName()));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (StringUtils.isNotEmpty(projectIncomeOrProjectProfitExcelVo.getProjectCode()) && StringUtils.isNotEmpty(projectIncomeOrProjectProfitExcelVo.getName())) {
                if (ListUtils.isNotEmpty(projectCodeAndName) && projectCodeAndName.contains(projectIncomeOrProjectProfitExcelVo.getProjectCode() +
                        ":" + projectIncomeOrProjectProfitExcelVo.getName())) {
                    result = false;
                    validResultList.add("(项目编号-新):" + projectIncomeOrProjectProfitExcelVo.getProjectCode() + "," +
                            "里程碑名称为:" + projectIncomeOrProjectProfitExcelVo.getName() + ",重复");
                }
                projectCodeAndName.add(projectIncomeOrProjectProfitExcelVo.getProjectCode() + ":" + projectIncomeOrProjectProfitExcelVo.getName());
            }

            if (StringUtils.isEmpty(projectIncomeOrProjectProfitExcelVo.getMilepostOrderNum())) {
                result = false;
                validResultList.add("里程碑序号不能为空");
            } else {
                projectIncomeOrProjectProfitExcelVo.setOrderNum(Integer.parseInt(projectIncomeOrProjectProfitExcelVo.getMilepostOrderNum()));
            }

            if (StringUtils.isEmpty(projectIncomeOrProjectProfitExcelVo.getCostMethodName())) {
                result = false;
                validResultList.add("收入成本方法不能为空");
            } /*else {
                if ("成本百分比".equals(projectIncomeOrProjectProfitExcelVo.getCostMethodName())) {
                    projectIncomeOrProjectProfitExcelVo.setCostMethod(CostMethod.COST.getCode());
                } else if ("收入百分比".equals(projectIncomeOrProjectProfitExcelVo.getCostMethodName())) {
                    projectIncomeOrProjectProfitExcelVo.setCostMethod(CostMethod.INCOME.getCode());
                } else if ("FAT收入百分比".equals(projectIncomeOrProjectProfitExcelVo.getCostMethodName())) {
                    projectIncomeOrProjectProfitExcelVo.setCostMethod(CostMethod.FAT_INCOME.getCode());
                } else {
                    result = false;
                    validResultList.add("收入成本方法错误");
                }
            }*/

            if (StringUtils.isEmpty(projectIncomeOrProjectProfitExcelVo.getIncomePointName())) {
                result = false;
                validResultList.add("收入确认时点不能为空");
            } else {
                if ("里程碑".equals(projectIncomeOrProjectProfitExcelVo.getIncomePointName())) {
                    projectIncomeOrProjectProfitExcelVo.setIncomePoint(IncomePoint.MILEPOST.getCode());
                } else if ("月度".equals(projectIncomeOrProjectProfitExcelVo.getIncomePointName())) {
                    projectIncomeOrProjectProfitExcelVo.setIncomePoint(IncomePoint.MONTH.getCode());
                } else {
                    result = false;
                    validResultList.add("收入确认时点错误");
                }
            }

            if (StringUtils.isEmpty(projectIncomeOrProjectProfitExcelVo.getIncomeFlagName())) {
                result = false;
                validResultList.add("是否收入节点不能为空");
            } else {
                if ("是".equals(projectIncomeOrProjectProfitExcelVo.getIncomeFlagName())) {
                    projectIncomeOrProjectProfitExcelVo.setIncomeFlag(true);
                } else if ("否".equals(projectIncomeOrProjectProfitExcelVo.getIncomeFlagName())) {
                    projectIncomeOrProjectProfitExcelVo.setIncomeFlag(false);
                } else {
                    result = false;
                    validResultList.add("是否收入节点错误");
                }
            }

            if (StringUtils.isEmpty(projectIncomeOrProjectProfitExcelVo.getCarryStatusName())) {
                result = false;
                validResultList.add("实际结束时间不能为空");
            } else {
                if ("是".equals(projectIncomeOrProjectProfitExcelVo.getCarryStatusName())) {
                    projectIncomeOrProjectProfitExcelVo.setCarryStatus("1");
                } else if ("否".equals(projectIncomeOrProjectProfitExcelVo.getCarryStatusName())) {
                    projectIncomeOrProjectProfitExcelVo.setCarryStatus("0");
                } else {
                    result = false;
                    validResultList.add("是否已结转错误");
                }
            }

            if (!Objects.isNull(projectIncomeOrProjectProfitExcelVo.getIncomeRatio())) {
                projectIncomeOrProjectProfitExcelVo.setIncomeRatio(projectIncomeOrProjectProfitExcelVo.getIncomeRatio().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (!Objects.isNull(projectIncomeOrProjectProfitExcelVo.getIncomeAmount())) {
                projectIncomeOrProjectProfitExcelVo.setIncomeAmount(projectIncomeOrProjectProfitExcelVo.getIncomeAmount().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (!Objects.isNull(projectIncomeOrProjectProfitExcelVo.getCostRatio())) {
                projectIncomeOrProjectProfitExcelVo.setCostRatio(projectIncomeOrProjectProfitExcelVo.getCostRatio().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (!Objects.isNull(projectIncomeOrProjectProfitExcelVo.getCostAmount())) {
                projectIncomeOrProjectProfitExcelVo.setCostAmount(projectIncomeOrProjectProfitExcelVo.getCostAmount().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            projectIncomeOrProjectProfitExcelVo.setValidResult(Joiner.on("，").join(validResultList));
        }
        return result;
    }

    private boolean getValidProjectBudgetFeeDetailResults(List<ProjectBudgetFeeDto> projectBudgetFeeDtoList,
                                                          Long company) {
        //获取使用单位下所有的项目编号
        List<Project> projects = getAllProjectCode(company);
        Map<String, Long> projectId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getId,
                (key1, key2) -> key2));
        Map<String, String> projectName = projects.stream().collect(Collectors.toMap(Project::getCode,
                Project::getName, (key1, key2) -> key2));
        //获取使用单位下所有的费用类型
        Map<String, Long> feeItem = getAllFeeItem(company).stream().collect(Collectors.toMap(FeeItem::getName,
                FeeItem::getId, (key1, key2) -> key2));

        boolean result = true;
        List<String> projectCodeAndFeeItem = new ArrayList<>();
        for (ProjectBudgetFeeDto projectBudgetFeeDto : projectBudgetFeeDtoList) {
            List<String> validResultList = new ArrayList<>();

            if (Objects.isNull(projectBudgetFeeDto.getProjectCode())) {
                result = false;
                validResultList.add("(项目编号-新)不能为空");
            } else {
                if (ObjectUtils.isEmpty(projectId.get(projectBudgetFeeDto.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目编号-新)不存在");
                } else {
                    projectBudgetFeeDto.setProjectId(projectId.get(projectBudgetFeeDto.getProjectCode()));
                }
            }

            if (StringUtils.isEmpty(projectBudgetFeeDto.getProjectName())) {
                result = false;
                validResultList.add("(项目名称-新)不能为空");
            } else {
                if (StringUtils.isEmpty(projectName.get(projectBudgetFeeDto.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目名称-新)错误");
                }
            }

            if (StringUtils.isEmpty(projectBudgetFeeDto.getFeeItemName())) {
                result = false;
                validResultList.add("费用类型不能为空");
            } else {
                if (ObjectUtils.isEmpty(feeItem.get(projectBudgetFeeDto.getFeeItemName()))) {
                    result = false;
                    validResultList.add("不存在该费用类型");
                } else {
                    projectBudgetFeeDto.setFeeItemId(feeItem.get(projectBudgetFeeDto.getFeeItemName()));
                }
            }

            if (StringUtils.isNotEmpty(projectBudgetFeeDto.getProjectCode()) && StringUtils.isNotEmpty(projectBudgetFeeDto.getFeeItemName())) {
                if (ListUtils.isNotEmpty(projectCodeAndFeeItem) && projectCodeAndFeeItem.contains(projectBudgetFeeDto.getProjectCode() + ":" + projectBudgetFeeDto.getFeeItemName())) {
                    result = false;
                    validResultList.add("(项目编号-新):" + projectBudgetFeeDto.getProjectCode() + "存在重复的费用类型:" + projectBudgetFeeDto.getFeeItemName());
                }
            }

            if (Objects.isNull(projectBudgetFeeDto.getAmount())) {
                result = false;
                validResultList.add("金额不能为空");
            } else {
                projectBudgetFeeDto.setAmount(projectBudgetFeeDto.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            projectBudgetFeeDto.setValidResult(Joiner.on("，").join(validResultList));

        }
        return result;
    }

    private List<FeeItem> getAllFeeItem(Long company) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", company);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/feeItem/selectListByUnitId", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<FeeItem>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<FeeItem>>>() {
        });
        return response.getData();
    }

    private boolean getValidProjectBudgetTravelDetailResults(List<ProjectBudgetTravelDto> projectBudgetTravelDtoList,
                                                             Long company) {
        //获取昆山使用单位下所有的项目编号
        List<Project> projects = getAllProjectCode(company);
        Map<String, Long> projectId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getId,
                (key1, key2) -> key2));
        Map<String, String> projectName = projects.stream().collect(Collectors.toMap(Project::getCode,
                Project::getName, (key1, key2) -> key2));
        boolean result = true;
        List<String> projectCodeList = new ArrayList<>();
        for (ProjectBudgetTravelDto projectBudgetTravelDto : projectBudgetTravelDtoList) {
            List<String> validResultList = new ArrayList<>();

            if (StringUtils.isEmpty(projectBudgetTravelDto.getProjectCode())) {
                result = false;
                validResultList.add("(项目编号-新)不能为空");
            } else {
                if (ObjectUtils.isEmpty(projectId.get(projectBudgetTravelDto.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目编号-新)不存在");
                } else {
                    projectBudgetTravelDto.setProjectId(projectId.get(projectBudgetTravelDto.getProjectCode()));
                }
            }

            if (StringUtils.isNotEmpty(projectBudgetTravelDto.getProjectCode())) {
                if (ListUtils.isNotEmpty(projectCodeList) && projectCodeList.contains(projectBudgetTravelDto.getProjectCode())) {
                    result = false;
                    validResultList.add("(项目编号-新):" + projectBudgetTravelDto.getProjectCode() + "重复");
                }
            }

            if (StringUtils.isEmpty(projectBudgetTravelDto.getProjectName())) {
                result = false;
                validResultList.add("(项目名称-新)不能为空");
            } else {
                if (StringUtils.isEmpty(projectName.get(projectBudgetTravelDto.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目名称-新)错误");
                }
            }

            if (StringUtils.isEmpty(projectBudgetTravelDto.getTypeName())) {
                result = false;
                validResultList.add("类型不能为空");
            } else {
                if ("内部".equals(projectBudgetTravelDto.getTypeName())) {
                    projectBudgetTravelDto.setType(1);
                } else if ("外部".equals(projectBudgetTravelDto.getTypeName())) {
                    projectBudgetTravelDto.setType(2);
                } else {
                    result = false;
                    validResultList.add("类型错误");
                }
            }

            if (Objects.isNull(projectBudgetTravelDto.getHotel())) {
                result = false;
                validResultList.add("住宿费/天不能为空");
            } else {
                projectBudgetTravelDto.setHotel(projectBudgetTravelDto.getHotel().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(projectBudgetTravelDto.getSubsidy())) {
                result = false;
                validResultList.add("补助费/天不能为空");
            } else {
                projectBudgetTravelDto.setSubsidy(projectBudgetTravelDto.getSubsidy().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(projectBudgetTravelDto.getComeBackTraffic())) {
                result = false;
                validResultList.add("往返交通费不能为空");
            } else {
                projectBudgetTravelDto.setComeBackTraffic(projectBudgetTravelDto.getComeBackTraffic().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(projectBudgetTravelDto.getTraffic())) {
                result = false;
                validResultList.add("市内交通费/天不能为空");
            } else {
                projectBudgetTravelDto.setTraffic(projectBudgetTravelDto.getTraffic().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(projectBudgetTravelDto.getOther())) {
                result = false;
                validResultList.add("其他费用不能为空");
            } else {
                projectBudgetTravelDto.setOther(projectBudgetTravelDto.getOther().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            projectBudgetTravelDto.setValidResult(Joiner.on("，").join(validResultList));
        }
        return result;
    }

    private boolean getValidProjectBudgetHumanDetailResults(List<ProjectBudgetHumanDto> projectBudgetHumanDtoList,
                                                            Long company) {
        //获取使用单位下所有的项目
        List<Project> projects = getAllProjectCode(company);
        Map<String, Long> projectId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getId,
                (key1, key2) -> key2));
        Map<String, String> projectName = projects.stream().collect(Collectors.toMap(Project::getCode,
                Project::getName, (key1, key2) -> key2));
        //获取人力费率详情
        Map<String, Long> laborCostId = getLaborCostDetail().stream().collect(Collectors.toMap(LaborCost::getName,
                LaborCost::getId, (key1, key2) -> key2));
        //获取当前使用单位的业务实体
        List<OperatingUnitDto> operatingUnitDtos = basedataExtService.queryCurrentUnitOu(company);
        Map<String, Long> ouMap =
                operatingUnitDtos.stream().collect(Collectors.toMap(OperatingUnitDto::getOperatingUnitName,
                        OperatingUnitDto::getOperatingUnitId, (key1, key2) -> key2));
        List<Long> ouIds =
                operatingUnitDtos.stream().map(OperatingUnitDto::getOperatingUnitId).collect(Collectors.toList());
        //获取使用单位下所有的供应商信息
        List<VendorSiteBank> vendorSiteBanks = getAllVendorSiteBank(ouIds);
        Map<String, String> vendorSiteBank =
                vendorSiteBanks.stream().collect(Collectors.toMap(VendorSiteBank::getVendorCode,
                        VendorSiteBank::getVendorName, (key1, key2) -> key2));
        Map<String, Long> vendorSiteBankId =
                vendorSiteBanks.stream().collect(Collectors.toMap(VendorSiteBank::getVendorCode, VendorSiteBank::getId,
                        (key1, key2) -> key2));

        boolean result = true;
        List<String> projectCodeAndTypeAndRole = new ArrayList<>();
        for (ProjectBudgetHumanDto projectBudgetHumanDto : projectBudgetHumanDtoList) {
            List<String> validResultList = new ArrayList<>();

            if (StringUtils.isEmpty(projectBudgetHumanDto.getProjectCode())) {
                result = false;
                validResultList.add("(项目编号-新)不能为空");
            } else {
                if (ObjectUtils.isEmpty(projectId.get(projectBudgetHumanDto.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目编号-新)不存在");
                } else {
                    projectBudgetHumanDto.setProjectId(projectId.get(projectBudgetHumanDto.getProjectCode()));
                    if (StringUtils.isNotEmpty(projectBudgetHumanDto.getLevel())) {
                        //根据项目id查询项目物料预算
                        Map<String, Long> budgetMaterialProjaectId =
                                projectBudgetMaterialService.selectBudgetMaterial(projectId.get(projectBudgetHumanDto.getProjectCode()))
                                        .stream().collect(Collectors.toMap(ProjectBudgetMaterial::getName,
                                                ProjectBudgetMaterial::getProjectId, (key1, key2) -> key2));
                        if (budgetMaterialProjaectId.containsKey(projectBudgetHumanDto.getLevel())) {
                            result = false;
                            validResultList.add("所属层级不存在");
                        }
                    }
                }
            }

            if (StringUtils.isEmpty(projectBudgetHumanDto.getProjectName())) {
                result = false;
                validResultList.add("(项目名称-新)不能为空");
            } else {
                if (StringUtils.isEmpty(projectName.get(projectBudgetHumanDto.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目名称-新)错误");
                }
            }

            if (StringUtils.isEmpty(projectBudgetHumanDto.getTypeName())) {
                result = false;
                validResultList.add("类型不能为空");
            } else {
                if ("内部".equals(projectBudgetHumanDto.getTypeName())) {
                    projectBudgetHumanDto.setType(1);
                } else if ("外部".equals(projectBudgetHumanDto.getTypeName())) {
                    projectBudgetHumanDto.setType(2);
                } else {
                    result = false;
                    validResultList.add("类型错误");
                }
            }

            if (StringUtils.isEmpty(projectBudgetHumanDto.getFunctionName())) {
                result = false;
                validResultList.add("角色不能为空");
            } else {
                if (!laborCostId.containsKey(projectBudgetHumanDto.getFunctionName())) {
                    result = false;
                    validResultList.add("角色不存在");
                } else {
                    projectBudgetHumanDto.setFunctionId(laborCostId.get(projectBudgetHumanDto.getFunctionName()));
                }
            }

            if (StringUtils.isNotEmpty(projectBudgetHumanDto.getProjectCode()) && StringUtils.isNotEmpty(projectBudgetHumanDto.getTypeName()) && StringUtils.isNotEmpty(projectBudgetHumanDto.getFunctionName())) {
                if (ListUtils.isNotEmpty(projectCodeAndTypeAndRole)
                        && projectCodeAndTypeAndRole.contains(projectBudgetHumanDto.getProjectCode() + ":" + projectBudgetHumanDto.getTypeName() +
                        ":" + projectBudgetHumanDto.getFunctionName())) {
                    result = false;
                    validResultList.add("项目:" + projectBudgetHumanDto.getProjectCode() + ",类型:" + projectBudgetHumanDto.getTypeName() + ",角色名为:" + projectBudgetHumanDto.getFunctionName() + "重复");
                }
                projectCodeAndTypeAndRole.add(projectBudgetHumanDto.getProjectCode() + ":" + projectBudgetHumanDto.getTypeName() + ":" + projectBudgetHumanDto.getFunctionName());
            }

            if ("外部".equals(projectBudgetHumanDto.getTypeName())) {
                if (StringUtils.isEmpty(projectBudgetHumanDto.getVendorCode())) {
                    result = false;
                    validResultList.add("供应商编码不能为空");
                }
                if (StringUtils.isEmpty(projectBudgetHumanDto.getVendorName())) {
                    result = false;
                    validResultList.add("供应商名称不能为空");
                }
            }

            if (StringUtils.isNotEmpty(projectBudgetHumanDto.getVendorCode())) {
                if (!vendorSiteBank.containsKey(projectBudgetHumanDto.getVendorCode())) {
                    result = false;
                    validResultList.add("供应商编码不存在");
                } else {
                    projectBudgetHumanDto.setVendorId(vendorSiteBankId.get(projectBudgetHumanDto.getVendorCode()));
                }
            }

            if (StringUtils.isNotEmpty(projectBudgetHumanDto.getVendorName())) {
                if (!Objects.equals(projectBudgetHumanDto.getVendorName(),
                        vendorSiteBank.get(projectBudgetHumanDto.getVendorCode()))) {
                    result = false;
                    validResultList.add("供应商名称错误");
                }
            }

            if (Objects.isNull(projectBudgetHumanDto.getDays())) {
                result = false;
                validResultList.add("单人投入天数不能为空");
            } else {
                projectBudgetHumanDto.setDays(projectBudgetHumanDto.getDays().setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(projectBudgetHumanDto.getPrice())) {
                result = false;
                validResultList.add("人天成本单价不能为空");
            } else {
                projectBudgetHumanDto.setPrice(projectBudgetHumanDto.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            projectBudgetHumanDto.setValidResult(Joiner.on("，").join(validResultList));
        }
        return result;
    }

    private boolean getValidProjectBudgetMaterialDetailResults(List<ProjectBudgetMaterialDto> projectBudgetMaterialDtoList, Long company) {
        //获取昆山使用单位下所有的项目编号
        List<Project> projects = getAllProjectCode(company);
        Map<String, Long> projectId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getId,
                (key1, key2) -> key2));
        Map<String, String> projectName = projects.stream().collect(Collectors.toMap(Project::getCode,
                Project::getName, (key1, key2) -> key2));
        Map<String, Long> ouId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getOuId, (key1
                , key2) -> key2));
        //获取昆山项目下的物料信息
        //Map<Long, String> pamCode = getMaterialDetail().stream().collect(Collectors.toMap
        // (Material::getOrganizationId, Material::getPamCode, (key1, key2) -> key2));
        // 获取计量单位
        Map<String, String> unitMap =
                basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));

        boolean result = true;
        List<String> pamCodeList = new ArrayList<>();
        for (ProjectBudgetMaterialDto projectBudgetMaterialDto : projectBudgetMaterialDtoList) {
            List<String> validResultList = new ArrayList<>();

            if (StringUtils.isEmpty(projectBudgetMaterialDto.getProjectCode())) {
                result = false;
                validResultList.add("(项目编号-新)不能为空");
            } else {
                if (ObjectUtils.isEmpty(projectId.get(projectBudgetMaterialDto.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目编号-新)不存在");
                } else {
                    projectBudgetMaterialDto.setProjectId(projectId.get(projectBudgetMaterialDto.getProjectCode()));
                }
            }

            if (StringUtils.isEmpty(projectBudgetMaterialDto.getProjectName())) {
                result = false;
                validResultList.add("(项目名称-新)不能为空");
            } else {
                if (StringUtils.isEmpty(projectName.get(projectBudgetMaterialDto.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目名称-新)错误");
                }
            }

            if (StringUtils.isEmpty(projectBudgetMaterialDto.getProjectBudgetMaterialLevel())) {
                result = false;
                validResultList.add("物料预算层级不能为空");
            }

            if (StringUtils.isEmpty(projectBudgetMaterialDto.getName())) {
                result = false;
                validResultList.add("物料描述不能为空");
            }

            if (StringUtils.isEmpty(projectBudgetMaterialDto.getCode())) {
                result = false;
                validResultList.add("PAM物料编码不能为空");
            } else {
                if (ListUtils.isNotEmpty(pamCodeList) && pamCodeList.contains(projectBudgetMaterialDto.getCode())) {
                    result = false;
                    validResultList.add("PAM物料编码不能重复：" + projectBudgetMaterialDto.getCode());
                }
                pamCodeList.add(projectBudgetMaterialDto.getCode());
            }

            if (StringUtils.isEmpty(projectBudgetMaterialDto.getUnitName())) {
                result = false;
                validResultList.add("单位不能为空");
            } else {
                if (StringUtils.isEmpty(unitMap.get(projectBudgetMaterialDto.getUnitName()))) {
                    result = false;
                    validResultList.add("单位错误");
                } else {
                    projectBudgetMaterialDto.setUnit(unitMap.get(projectBudgetMaterialDto.getUnitName()));
                }

            }

            if (Objects.isNull(projectBudgetMaterialDto.getNumber())) {
                result = false;
                validResultList.add("数量不能为空");
            }

            if (Objects.isNull(projectBudgetMaterialDto.getPlannedDeliveryStartDate())) {
                result = false;
                validResultList.add("计划开工日期不能为空");
            }

            if (Objects.isNull(projectBudgetMaterialDto.getPlannedDeliveryDate())) {
                result = false;
                validResultList.add("计划交付日期不能为空");
            }

            if (StringUtils.isEmpty(projectBudgetMaterialDto.getSource())) {
                result = false;
                validResultList.add("来源不能为空");
            } /*else {
             if (!"手工".equals(projectBudgetMaterialDto.getSource())) {
                 result = false;
                 validResultList.add("来源错误");
             }
         }*/

            if (Objects.isNull(projectBudgetMaterialDto.getPrice())) {
                result = false;
                validResultList.add("预算单价不能为空");
            } else {
                projectBudgetMaterialDto.setPrice(projectBudgetMaterialDto.getPrice().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (Objects.isNull(projectBudgetMaterialDto.getPriceTotal())) {
                result = false;
                validResultList.add("预算总价不能为空");
            } else {
                projectBudgetMaterialDto.setPriceTotal(projectBudgetMaterialDto.getPriceTotal().setScale(2,
                        BigDecimal.ROUND_HALF_UP));
            }

            if (StringUtils.isEmpty(projectBudgetMaterialDto.getExtName())) {
                result = false;
                validResultList.add("是否外包不能为空");
            } else {
                if ("是".equals(projectBudgetMaterialDto.getExtName())) {
                    projectBudgetMaterialDto.setExt(true);
                } else if ("否".equals(projectBudgetMaterialDto.getExtName())) {
                    projectBudgetMaterialDto.setExt(false);
                } else {
                    result = false;
                    validResultList.add("是否外包填写错误");
                }
            }
            projectBudgetMaterialDto.setValidResult(Joiner.on("，").join(validResultList));
        }

        return result;
    }


    private boolean getValidProjectMemberDetailResults(List<ProjectMemberDto> projectMemberDtoList, Long company) {
        //获取昆山使用单位下所有的项目编号
        List<Project> projects = getAllProjectCode(company);
        Map<String, Long> projectId = projects.stream().collect(Collectors.toMap(Project::getCode, Project::getId,
                (key1, key2) -> key2));
        Map<String, String> projectName = projects.stream().collect(Collectors.toMap(Project::getCode,
                Project::getName, (key1, key2) -> key2));
        //查询使用单位下的项目角色
        Map<String, Long> projectMemberRole = getProjectMemberRole(company);

        boolean result = true;
        List<String> projectCodeAndMip = new ArrayList<>();
        for (ProjectMemberDto projectMemberDto : projectMemberDtoList) {
            List<String> validResultList = new ArrayList<>();

            if (StringUtils.isEmpty(projectMemberDto.getProjectCode())) {
                result = false;
                validResultList.add("(项目编号-新)不能为空");
            } else {
                if (ObjectUtils.isEmpty(projectId.get(projectMemberDto.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目编号-新)不存在");
                } else {
                    projectMemberDto.setProjectId(projectId.get(projectMemberDto.getProjectCode()));
                }
            }

            if (StringUtils.isEmpty(projectMemberDto.getProjectName())) {
                result = false;
                validResultList.add("(项目名称-新)不能为空");
            } else {
                if (StringUtils.isEmpty(projectName.get(projectMemberDto.getProjectCode()))) {
                    result = false;
                    validResultList.add("(项目名称-新)错误");
                }
            }

            if (StringUtils.isEmpty(projectMemberDto.getProjectMemberMip())) {
                result = false;
                validResultList.add("项目成员MIP不能为空");
            } else {
                UserInfo userInfo = CacheDataUtils.findUserByMip(projectMemberDto.getProjectMemberMip());
                if (userInfo == null) {
                    result = false;
                    validResultList.add("项目成员MIP账号不存在");
                } else {
                    if ("N".equals(userInfo.getStatus())) {
                        result = false;
                        validResultList.add("项目成员MIP账号已失效");
                    } else {
                        projectMemberDto.setUserId(userInfo.getId());
                        projectMemberDto.setUserName(userInfo.getUsername());
                    }
                }
            }

            if (StringUtils.isNotEmpty(projectMemberDto.getProjectCode()) && StringUtils.isNotEmpty(projectMemberDto.getProjectMemberMip())) {
                if (ListUtils.isNotEmpty(projectCodeAndMip) && projectCodeAndMip.contains(projectMemberDto.getProjectCode() + ":" + projectMemberDto.getProjectMemberMip() + ":" + projectMemberDto.getProjectMemberRoleName())) {
                    result = false;
                    validResultList.add("项目:" + projectMemberDto.getProjectCode() + ",模板中存在两条或以上相同的项目成员数据:" + projectMemberDto.getProjectMemberMip() + ":" + projectMemberDto.getProjectMemberRoleName());
                }
                projectCodeAndMip.add(projectMemberDto.getProjectCode() + ":" + projectMemberDto.getProjectMemberMip() + ":" + projectMemberDto.getProjectMemberRoleName());
            }

            if (StringUtils.isEmpty(projectMemberDto.getProjectMemberName())) {
                result = false;
                validResultList.add("成员名称不能为空");
            }

            if (StringUtils.isEmpty(projectMemberDto.getProjectMemberRoleName())) {
              /*  result = false;
                validResultList.add("项目角色不能为空");*/
            } else {
                if (ObjectUtils.isEmpty(projectMemberRole.get(projectMemberDto.getProjectMemberRoleName()))) {
                    result = false;
                    validResultList.add("项目角色表不存在该角色");
                } else {
                    projectMemberDto.setProjectMemberRole(projectMemberRole.get(projectMemberDto.getProjectMemberRoleName()));
                }
            }
            projectMemberDto.setValidResult(Joiner.on("，").join(validResultList));

        }
        return result;
    }

    private Map<String, Long> getProjectMemberRole(Long company) {
        ProjectRoleExample projectRoleExample = new ProjectRoleExample();
        ProjectRoleExample.Criteria criteria = projectRoleExample.createCriteria();
        criteria.andUnitIdEqualTo(company).andDeletedFlagEqualTo(false);
        return projectRoleMapper.selectByExample(projectRoleExample).stream().collect(Collectors.toMap(ProjectRole::getName, ProjectRole::getId,
                (key1, key2) -> key2));
    }

    /**
     * 保存项目
     *
     * @param projectList
     */
    @Deprecated
    private void saveProjectDetail(List<Project> projectList) {
        for (Project project : projectList) {
            //根据项目号查询项目信息
            ProjectExample projectExample = new ProjectExample();
            ProjectExample.Criteria criteria = projectExample.createCriteria();
            criteria.andCodeEqualTo(project.getCode());
            List<Project> projects = projectMapper.selectByExample(projectExample);
            if (ListUtils.isNotEmpty(projects)) {
                project.setId(projects.get(0).getId());
                project.setUpdateAt(new Date());
                project.setUpdateBy(SystemContext.getUserId());
                projectMapper.updateByPrimaryKeySelective(project);
            } else {
                if (null == project.getId()) {
                    project.setDeletedFlag(DeletedFlag.VALID.code());
                    project.setProjectSource(1);
                    project.setIsImport(true);
                    project.setIsObjectiveProject(0);
                    project.setCreateBy(SystemContext.getUserId());
                    project.setCreateAt(new Date());
                    project.setPreviewFlag(false);
                    projectMapper.insert(project);
                    //合同
                    if (null != project.getContractId()) {
                        ProjectContractRs projectContractRs = new ProjectContractRs();
                        projectContractRs.setProjectId(project.getId());
                        projectContractRs.setContractId(project.getContractId());
                        projectContractRs.setDeletedFlag(Boolean.FALSE);
                        projectContractRsService.insertSelective(projectContractRs);
                    }
                } else {
                    project.setUpdateAt(new Date());
                    project.setUpdateBy(SystemContext.getUserId());
                    projectMapper.updateByPrimaryKeySelective(project);
                }
            }
        }
    }

    @Deprecated
    private boolean getValidDetailResults(List<ProjectDto> projectDtoList, Long company) {
        // 查询当前使用单位下的业务实体
        List<OperatingUnitDto> operatingUnitDtos = basedataExtService.queryCurrentUnitOu(company);
        Map<String, Long> ouMap =
                operatingUnitDtos.stream().collect(Collectors.toMap(OperatingUnitDto::getOperatingUnitName,
                        OperatingUnitDto::getOperatingUnitId, (key1, key2) -> key2));
        List<Long> ouIds =
                operatingUnitDtos.stream().map(OperatingUnitDto::getOperatingUnitId).collect(Collectors.toList());
        //获取使用单位下所有的项目编号
        List<String> codeList = getProjectCode(company).stream().map(Project::getCode).collect(Collectors.toList());
        //获取使用单位下所有项目类型
        Map<String, Long> projectType = getProjectTypeByUnitId(company);
        //获取当前使用单位下所有业务分类
        //根据业务分类名称和使用单位查询业务分类id
        List<Unit> unitList = getUnitId(company);
        Map<String, Long> unitId = unitList.stream().collect(Collectors.toMap(Unit::getUnitName, Unit::getId, (key1,
                                                                                                               key2) -> key2));

/*      //查询所有客户信息详情
        List<Customer>  customerDetail = getAllCustomerDetail();
        //获取crm编码对应的客户id
        Map<String, Long> customerId = customerDetail.stream().collect(Collectors.toMap(Customer::getCrmCode,
        Customer::getId, (key1, key2) -> key2));
        //获取crm编码对应的客户名称
        Map<String, String> customerName = customerDetail.stream().collect(Collectors.toMap(Customer::getCrmCode,
        Customer::getName, (key1, key2) -> key2));*/

        //查询使用单位下的应用行业详情
        Map<String, Long> applicationIndustry = getApplicationIndustry(company);
        //查询使用单位下组织参数“利润中心”对应的值
        Set<String> profitCenterSet = organizationCustomDictService.queryByName(Constants.PROFIT_CENTER, company,
                OrgCustomDictOrgFrom.COMPANY);
        //查询使用单位下组织参数“产品中心”对应的值
        Set<String> productCenterSet = organizationCustomDictService.queryByName(Constants.PRODUCT_CENTER, company,
                OrgCustomDictOrgFrom.COMPANY);
        //获取使用单位下的所有合同信息
        List<Contract> contracts = getAllContract(ouIds);
        //获取合同编号对应的合同id
        Map<String, Long> contractId = contracts.stream().collect(Collectors.toMap(Contract::getCode, Contract::getId
                , (key1, key2) -> key2));
        //获取合同编号对应的合同名称
        Map<String, String> contractName = contracts.stream().collect(Collectors.toMap(Contract::getCode,
                Contract::getName, (key1, key2) -> key2));
        //获取使用单位下所有的商机信息
        List<Business> business = getAllBusiness(ouIds);
        //获取商机编号对应的商机id
        Map<String, Long> businessId = business.stream().collect(Collectors.toMap(Business::getBusinessCode,
                Business::getId, (key1, key2) -> key2));
        //获取商机编号对应的商机名称
        Map<String, String> businessName = business.stream().collect(Collectors.toMap(Business::getBusinessCode,
                Business::getName, (key1, key2) -> key2));
        //查询所有有效的预算部门信息
        Map<String, Long> BudgetDepId = getAllBudgetDep().stream().collect(Collectors.toMap(BudgetDep::getEmsDepId,
                BudgetDep::getId, (key1, key2) -> key2));
        //查询所有的预算树头信息
        List<BudgetTree> budgetTree = getBudgetTree();
        Map<String, Long> budgetHeadersId = budgetTree.stream().collect(Collectors.toMap(BudgetTree::getBudgetName,
                BudgetTree::getBudgetHeadersId, (key1, key2) -> key2));
        //查询产品信息
        List<ProjectProductMaintenance> projectProduct = getProjectProduct(company);
        Map<String, Long> projectProductId =
                projectProduct.stream().collect(Collectors.toMap(ProjectProductMaintenance::getProjectProductName,
                        ProjectProductMaintenance::getId, (key1, key2) -> key2));


        boolean result = true;
        for (ProjectDto projectDto : projectDtoList) {
            List<String> validResultList = new ArrayList<>();
            if (StringUtils.isEmpty(projectDto.getCode())) {
                result = false;
                validResultList.add("(项目编号-新)不能为空");
            } else {
                if (codeList.contains(projectDto.getCode())) {
                    result = false;
                    validResultList.add("(项目编号-新)已存在");
                }
            }

            if (StringUtils.isEmpty(projectDto.getName())) {
                result = false;
                validResultList.add("(项目名称-新)不能为空");
            }

            if (StringUtils.isEmpty(projectDto.getStatusName())) {
                result = false;
                validResultList.add("项目状态不能为空");
            } else {
                if ("结项".equals(projectDto.getStatusName())) {
                    projectDto.setStatus(10);
                } else if ("进行中".equals(projectDto.getStatusName())) {
                    projectDto.setStatus(4);
                } else {
                    result = false;
                    validResultList.add("项目状态值不正确");
                }
            }

            if (StringUtils.isEmpty(projectDto.getTypeName())) {
                result = false;
                validResultList.add("项目类型不能为空");
            } else {
                if (ObjectUtils.isEmpty(projectType.get(projectDto.getTypeName()))) {
                    result = false;
                    validResultList.add("项目类型不存在");
                } else {
                    projectDto.setType(projectType.get(projectDto.getTypeName()));
                }
            }

            if (StringUtils.isEmpty(projectDto.getSummary())) {
                result = false;
                validResultList.add("项目概述不能为空");
            }

            if (StringUtils.isEmpty(projectDto.getCustomerCRMCode())) {
                result = false;
                validResultList.add("客户编码不能为空");
            } else {
                //根据客户编码查询客户名称
                List<Customer> customerByCode = getCustomerByCode(projectDto.getCustomerCRMCode());
                if (ListUtils.isEmpty(customerByCode)) {
                    result = false;
                    validResultList.add("该CRM客户编码不存在");
                } else {
                    if (StringUtils.isNotEmpty(projectDto.getCustomerName())) {
                        if (!Objects.equals(projectDto.getCustomerName(), customerByCode.get(0).getName())) {
                            result = false;
                            validResultList.add("客户名称错误");
                        } else {
                            projectDto.setCustomerId(customerByCode.get(0).getId());
                        }
                    }
                }
            }

            if (StringUtils.isEmpty(projectDto.getCustomerName())) {
                result = false;
                validResultList.add("客户名称不能为空");
            }

            if (StringUtils.isEmpty(projectDto.getProjectLevelName())) {
/*                result = false;
                validResultList.add("项目级别不能为空");*/
            } else {
                if ("A".equals(projectDto.getProjectLevelName())) {
                    projectDto.setProjectLevel(1);
                } else if ("B".equals(projectDto.getProjectLevelName())) {
                    projectDto.setProjectLevel(2);
                } else if ("C".equals(projectDto.getProjectLevelName())) {
                    projectDto.setProjectLevel(3);
                } else {
                    result = false;
                    validResultList.add("项目级别错误 ");
                }
            }

            if (Objects.isNull(projectDto.getStartDate())) {
                result = false;
                validResultList.add("项目开始日期不能为空");
            }

            if (Objects.isNull(projectDto.getEndDate())) {
                result = false;
                validResultList.add("项目结束日期不能为空");
            }

            if (StringUtils.isEmpty(projectDto.getManagerMip())) {
                result = false;
                validResultList.add("项目经理MIP账号不能为空");
            } else {
                UserInfo userInfo = CacheDataUtils.findUserByMip(projectDto.getManagerMip());
                if (userInfo == null) {
                    result = false;
                    validResultList.add("项目经理MIP账号不存在");
                } else {
                    if ("N".equals(userInfo.getStatus())) {
                        result = false;
                        validResultList.add("项目经理MIP账号已失效");
                    } else {
                        projectDto.setManagerId(userInfo.getId());
                    }
                }
            }

            if (StringUtils.isEmpty(projectDto.getManagerName())) {
                result = false;
                validResultList.add("项目经理姓名不能为空");
            }

            if (StringUtils.isEmpty(projectDto.getSalesManagerMip())) {
                result = false;
                validResultList.add("销售经理MIP账号不能为空");
            } else {
                UserInfo userInfo = CacheDataUtils.findUserByMip(projectDto.getSalesManagerMip());
                if (userInfo == null) {
                    result = false;
                    validResultList.add("销售经理MIP账号不存在");
                } else {
                    if ("N".equals(userInfo.getStatus())) {
                        result = false;
                        validResultList.add("销售经理MIP账号已失效");
                    } else {
                        projectDto.setSalesManagerId(userInfo.getId());
                    }
                }
            }

            if (StringUtils.isEmpty(projectDto.getSalesManagerName())) {
                result = false;
                validResultList.add("销售经理姓名不能为空");
            }

            if (StringUtils.isEmpty(projectDto.getFinancialMip())) {
                result = false;
                validResultList.add("项目财务MIP账号不能为空");
            } else {
                UserInfo userInfo = CacheDataUtils.findUserByMip(projectDto.getFinancialMip());
                if (userInfo == null) {
                    result = false;
                    validResultList.add("项目财务MIP账号不存在");
                } else {
                    if ("N".equals(userInfo.getStatus())) {
                        result = false;
                        validResultList.add("项目财务MIP账号已失效");
                    } else {
                        projectDto.setFinancial(userInfo.getId());
                    }
                }
            }

            if (StringUtils.isEmpty(projectDto.getFinancialName())) {
                result = false;
                validResultList.add("项目财务姓名不能为空");
            }

            if (StringUtils.isEmpty(projectDto.getTechnologyLeaderMip())) {
/*                result = false;
                validResultList.add("技术负责人MIP账号不能为空");*/
            } else {
                UserInfo userInfo = CacheDataUtils.findUserByMip(projectDto.getTechnologyLeaderMip());
                if (userInfo == null) {
                    result = false;
                    validResultList.add("技术负责人MIP账号不存在");
                } else {
                    if ("N".equals(userInfo.getStatus())) {
                        result = false;
                        validResultList.add("技术负责人MIP账号已失效");
                    } else {
                        projectDto.setTechnologyLeaderId(userInfo.getId());
                    }
                }
            }

            if (StringUtils.isEmpty(projectDto.getPlanDesignerMip())) {
                /*result = false;
                validResultList.add("方案设计人MIP账号不能为空");*/
            } else {
                UserInfo userInfo = CacheDataUtils.findUserByMip(projectDto.getPlanDesignerMip());
                if (userInfo == null) {
                    result = false;
                    validResultList.add("方案设计人MIP账号MIP账号不存在");
                } else {
                    if ("N".equals(userInfo.getStatus())) {
                        result = false;
                        validResultList.add("方案设计人MIP账号MIP账号已失效");
                    } else {
                        projectDto.setPlanDesignerId(userInfo.getId());
                    }
                }
            }


            if (Constants.YL.equals(company)) {
                if (StringUtils.isNotEmpty(projectDto.getApplicationIndustryName())) {
                    if (ObjectUtils.isEmpty(applicationIndustry.get(projectDto.getApplicationIndustryName()))) {
                        result = false;
                        validResultList.add("应用行业不存在");
                    } else {
                        projectDto.setApplicationIndustry(applicationIndustry.get(projectDto.getApplicationIndustryName()).toString());
                    }
                }
            } else {
                if (StringUtils.isEmpty(projectDto.getApplicationIndustryName())) {
                    result = false;
                    validResultList.add("应用行业不能为空");
                } else {
                    if (ObjectUtils.isEmpty(applicationIndustry.get(projectDto.getApplicationIndustryName()))) {
                        result = false;
                        validResultList.add("应用行业不存在");
                    } else {
                        projectDto.setApplicationIndustry(applicationIndustry.get(projectDto.getApplicationIndustryName()).toString());
                    }
                }
            }

            if (StringUtils.isEmpty(projectDto.getUnitName())) {
                result = false;
                validResultList.add("业务分类不能为空");
            } else {
                if (!unitId.containsKey(projectDto.getUnitName())) {
                    result = false;
                    validResultList.add("业务分类不存在");
                } else {
                    projectDto.setUnitId(unitId.get(projectDto.getUnitName()));
                }
            }

            if (Constants.YL.equals(company)) {
                if (StringUtils.isNotEmpty(projectDto.getProfitCenter())) {
                    if (StringUtils.isNotEmpty(projectDto.getProfitCenter()) && !profitCenterSet.contains(projectDto.getProfitCenter())) {
                        result = false;
                        validResultList.add("利润中心不存在");
                    }
                }
            } else {
                if (StringUtils.isEmpty(projectDto.getProfitCenter())) {
                    result = false;
                    validResultList.add("利润中心名称不能为空");
                } else if (StringUtils.isNotEmpty(projectDto.getProfitCenter()) && !profitCenterSet.contains(projectDto.getProfitCenter())) {
                    result = false;
                    validResultList.add("利润中心不存在");
                }
            }

            if (Constants.YL.equals(company)) {
                if (StringUtils.isNotEmpty(projectDto.getProductCenter())) {
                    if (StringUtils.isNotEmpty(projectDto.getProductCenter()) && !productCenterSet.contains(projectDto.getProductCenter())) {
                        result = false;
                        validResultList.add("产品中心不存在");
                    }
                }
            } else {
                if (StringUtils.isEmpty(projectDto.getProductCenter())) {
                    result = false;
                    validResultList.add("产品中心名称不能为空");
                } else if (StringUtils.isNotEmpty(projectDto.getProductCenter()) && !productCenterSet.contains(projectDto.getProductCenter())) {
                    result = false;
                    validResultList.add("产品中心不存在");
                }
            }

            if (StringUtils.isEmpty(projectDto.getPriceTypeName())) {
                result = false;
                validResultList.add("项目单价类型不能为空");
            } else {
                if ("内部".equals(projectDto.getPriceTypeName())) {
                    projectDto.setPriceType("1");
                } else if ("外部".equals(projectDto.getPriceTypeName())) {
                    projectDto.setPriceType("2");
                } else if ("研发".equals(projectDto.getPriceTypeName())) {
                    projectDto.setPriceType("3");
                } else {
                    result = false;
                    validResultList.add("项目类型错误");
                }
            }

            if (StringUtils.isEmpty(projectDto.getOuName())) {
                result = false;
                validResultList.add("业务实体不能为空");
            } else {
                if (!ouMap.containsKey(projectDto.getOuName())) {
                    result = false;
                    validResultList.add("业务实体错误");
                } else {
                    projectDto.setOuId(ouMap.get(projectDto.getOuName()));
                }
            }

            if (StringUtils.isEmpty(projectDto.getBudgetDepName())) {
                result = false;
                validResultList.add("预算部门不能为空");
            } else {
                if (!BudgetDepId.containsKey(projectDto.getBudgetDepName())) {
                    result = false;
                    validResultList.add("预算部门不存在");
                } else {
                    projectDto.setBudgetDeptId(BudgetDepId.get(projectDto.getBudgetDepName()));
                }
            }

            if (StringUtils.isNotEmpty(projectDto.getBudgetHeaderName())) {
                if (!budgetHeadersId.containsKey(projectDto.getBudgetHeaderName())) {
                    result = false;
                    validResultList.add("预算树头不存在");
                } else {
                    projectDto.setBudgetHeaderId(budgetHeadersId.get(projectDto.getBudgetHeaderName()));
                }
            }

            if (StringUtils.isEmpty(projectDto.getCurrencyName())) {
                result = false;
                validResultList.add("币种不能为空");
            }

            if (Objects.isNull(projectDto.getAmount())) {
                result = false;
                validResultList.add("项目金额（不含税）不能为空");
            } else {
                projectDto.setAmount(projectDto.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            if (StringUtils.isEmpty(projectDto.getRelatedContractCode())) {
                /*result = false;
                validResultList.add("关联合同编号不能为空");*/
            } else {
                if (StringUtils.isEmpty(contractName.get(projectDto.getRelatedContractCode()))) {
                    result = false;
                    validResultList.add("关联合同编号不存在");
                } else if (!projectDto.getRelatedContractName().equals(contractName.get(projectDto.getRelatedContractCode()))) {
                    result = false;
                    validResultList.add("关联合同名称错误");
                } else {
                    projectDto.setContractId(contractId.get(projectDto.getRelatedContractCode()));
                }
            }

            if (StringUtils.isEmpty(projectDto.getRelatedBusinessCode())) {
               /* result = false;
                validResultList.add("关联商机编号不能为空");*/
            } else {
                if (StringUtils.isEmpty(contractName.get(projectDto.getRelatedBusinessCode()))) {
                    result = false;
                    validResultList.add("关联商机编号不存在");
                } else if (!projectDto.getRelatedBusinessName().equals(businessName.get(projectDto.getRelatedBusinessCode()))) {
                    result = false;
                    validResultList.add("关联商机名称错误");
                } else {
                    projectDto.setBusinessId(projectDto.getRelatedBusinessCode());
                    projectDto.setBusinessName(projectDto.getRelatedBusinessName());
                }
            }

            if (Objects.isNull(projectDto.getTotalPrice())) {
                result = false;
                validResultList.add("预算合计不能为空");
            } else {
                projectDto.setBudgetCost(projectDto.getTotalPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            if (StringUtils.isNotEmpty(projectDto.getProductName())) {
                if (!projectProductId.containsKey(projectDto.getProductName())) {
                    result = false;
                    validResultList.add("产品名错误");
                } else {
                    projectDto.setProductId(projectProductId.get(projectDto.getProductName()));
                }
            }

            projectDto.setValidResult(Joiner.on("，").join(validResultList));
        }
        return result;
    }

    private List<ProjectProductMaintenance> getProjectProduct(Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/projectProductMaintenance/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectProductMaintenance>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<ProjectProductMaintenance>>>() {
                });
        return response.getData();
    }

    private List<BudgetTree> getBudgetTree() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "budgetTree/getBudgetTree", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<List<BudgetTree>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<BudgetTree>>>() {
                });
        return response.getData();

    }

    private List<BudgetDep> getAllBudgetDep() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "budgetDep/getAllBudgetDep", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<List<BudgetDep>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<BudgetDep>>>() {
                });
        return response.getData();
    }

    private List<Unit> getUnitId(Long company) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", company);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/selectUnitId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<List<Unit>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<Unit>>>() {
                });
        return response.getData();
    }

    private List<Business> getAllBusiness(List<Long> ouIds) {
        BusinessDto businessDto = new BusinessDto();
        businessDto.setOuIdList(ouIds);
        final String url = String.format("%sbusiness/selectIdByOuId", ModelsEnum.CRM.getBaseUrl());
        String res = restTemplate.postForEntity(url, businessDto, String.class).getBody();
        //final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.CRM.getBaseUrl(),
        // "/business/selectIdByOuId", param);
        DataResponse<List<Business>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<Business>>>() {
                });
        return response.getData();


    }

    private List<Contract> getAllContract(List<Long> ouIds) {
        ContractExample contractExample = new ContractExample();
        ContractExample.Criteria criteria = contractExample.createCriteria();
        criteria.andOuIdIn(ouIds).andDeletedFlagEqualTo(false);
        return contractMapper.selectByExample(contractExample);
    }

    private Map<String, Long> getApplicationIndustry(Long company) {
        ApplicationIndustryExample applicationIndustryExample = new ApplicationIndustryExample();
        ApplicationIndustryExample.Criteria criteria = applicationIndustryExample.createCriteria();
        criteria.andEnabledEqualTo(1).andUnitIdEqualTo(company);
        return applicationIndustryMapper.selectByExample(applicationIndustryExample).stream().collect(Collectors.toMap(ApplicationIndustry::getName
                , ApplicationIndustry::getId, (key1, key2) -> key2));

    }

    private Map<String, Long> getProjectTypeByUnitId(Long company) {
        ProjectTypeExample projectTypeExample = new ProjectTypeExample();
        ProjectTypeExample.Criteria criteria = projectTypeExample.createCriteria();
        criteria.andUnitIdEqualTo(company).andDeletedFlagEqualTo(false);
        return projectTypeMapper.selectByExampleWithBLOBs(projectTypeExample).stream().collect(Collectors.toMap(ProjectType::getName,
                ProjectType::getId, (key1, key2) -> key2));
    }

    private List<Project> getProjectCode(Long company) {
        //查询当前使用单位下的项目
        ArrayList<Integer> status = new ArrayList<>();
        status.add(10);
        status.add(12);
        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE).andStatusNotIn(status);
        List<Long> units = basedataExtService.findUnitIds(company);
        if (units != null && units.size() > 0) {
            criteria.andUnitIdIn(units);
        }
        return projectMapper.selectByExample(projectExample);
    }

    private List<Project> getAllProjectCode(Long company) {
        //查询当前使用单位下的项目
        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        List<Long> units = basedataExtService.findUnitIds(company);
        if (CollectionUtils.isNotEmpty(units)) {
            criteria.andUnitIdIn(units);
        }
        return projectMapper.selectByExample(projectExample);
    }

    private List<LaborCost> getLaborCostDetail() {

        final Map<String, Object> param = new HashMap<>();
        //根据角色查询
        param.put("type", 1);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborCost/getLaborCostDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<List<LaborCost>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<LaborCost>>>() {
                });
        return response.getData();
    }

    private List<VendorSiteBank> getAllVendorSiteBank(List<Long> ouIds) {
        VendorSiteBankDto vendorSiteBankDto = new VendorSiteBankDto();
        vendorSiteBankDto.setOperatingUnitIds(ouIds);
        final String url = String.format("%svendorSiteBank/getVendorSiteBankByOrgId", ModelsEnum.BASEDATA.getBaseUrl());
        String res = restTemplate.postForEntity(url, vendorSiteBankDto, String.class).getBody();
        DataResponse<List<VendorSiteBank>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<VendorSiteBank>>>() {
                });
        return response.getData();
    }

    private List<Customer> getCustomerByCode(String customerNum) {
        final Map<String, Object> param = new HashMap<>();
        param.put("customerNum", customerNum);
        final String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/getCustomerDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<List<Customer>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<Customer>>>() {
                });
        return response.getData();
    }

    /**
     * 生成立项项目快照数据
     * ·
     *
     * @param projectIdStr
     * @param projectChangeType
     * @return
     */
    @Override
    public Integer autoBuildProjectSnapshot(String projectIdStr, Integer projectChangeType) {
        projectChangeType = projectChangeType == null ? CtcProjectChangeType.PROJECTSNAPSHOT.getCode() : projectChangeType;
        String reason = Objects.equals(CtcProjectChangeType.PROJECTSNAPSHOT.getCode(), projectChangeType) ? "_立项快照" : "_预立项转正快照";
        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        if (StringUtils.isEmpty(projectIdStr)) {
            return 0;
        }
        List<Long> projectIds = Arrays.stream(projectIdStr.split(",")).map(s -> Long.parseLong(s)).collect(Collectors.toList());
        criteria.andIdIn(projectIds);
        List<Project> projectList = selectByExample(projectExample);
        if (ListUtils.isEmpty(projectList)) {
            return 0;
        }
        for (Project project : projectList) {
            // 先查询是否已存在立项快照
            ProjectHistoryHeaderExample historyHeaderExample = new ProjectHistoryHeaderExample();
            historyHeaderExample.createCriteria()
                    .andDeletedFlagEqualTo(Boolean.FALSE)
                    .andChangeTypeEqualTo(projectChangeType)
                    .andProjectIdEqualTo(project.getId());
            List<ProjectHistoryHeader> projectHistoryHeaderList = projectHistoryHeaderMapper.selectByExample(historyHeaderExample);
            if (ListUtils.isNotEmpty(projectHistoryHeaderList)) {
                continue;
            }

            ProjectHistoryHeader historyHeader = new ProjectHistoryHeader();
            historyHeader.setChangeType(projectChangeType);
            historyHeader.setProjectId(project.getId());
            historyHeader.setReason(project.getName() + reason);
            historyHeader.setReasonType("其他原因");
            historyHeader.setStatus(ProjectChangeStatus.APPROVALED.getCode());
            historyHeader.setDeletedFlag(Boolean.FALSE);
            historyHeader.setCreateBy(project.getCreateBy());
            projectHistoryHeaderMapper.insert(historyHeader);

            // Project
            ProjectChangeHistory projectChangeHistory = BeanConverter.copy(project, ProjectChangeHistory.class);
            projectChangeHistory.setProjectId(project.getId());
            projectChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
            projectChangeHistory.setHeaderId(historyHeader.getId());
            projectChangeHistory.setId(null);
            projectChangeHistoryMapper.insert(projectChangeHistory);

            // ProjectMember
            ProjectMemberExample projectMemberExample = new ProjectMemberExample();
            projectMemberExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
            List<ProjectMember> projectMemberList = projectMemberMapper.selectByExample(projectMemberExample);
            if (ListUtils.isNotEmpty(projectMemberList)) {
                for (ProjectMember projectMember : projectMemberList) {
                    ProjectMemberChangeHistory projectMemberChangeHistory = BeanConverter.copy(projectMember, ProjectMemberChangeHistory.class);
                    projectMemberChangeHistory.setOriginId(projectMember.getId());
                    projectMemberChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                    projectMemberChangeHistory.setHeaderId(historyHeader.getId());
                    projectMemberChangeHistory.setId(null);
                    projectMemberChangeHistoryMapper.insert(projectMemberChangeHistory);
                }
            }

            // 保存关联合同快照
            ProjectContractRsExample contractRsExample = new ProjectContractRsExample();
            contractRsExample.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<ProjectContractRsChangeHistory> contractRsHistoryList =
                    BeanConverter.copy(projectContractRsMapper.selectByExample(contractRsExample), ProjectContractRsChangeHistory.class);

            if (CollectionUtils.isNotEmpty(contractRsHistoryList)) {
                contractRsHistoryList.forEach(history -> {
                    history.setHeaderId(historyHeader.getId());
                    history.setHistoryType(HistoryType.HISTORY.getCode());
                    history.setOriginId(history.getId());
                    history.setDeletedFlag(DeletedFlag.VALID.code());
                    history.setId(null);
                    //合同信息
                    if (history.getContractId() != null) {
                        Contract contract = contractMapper.selectByPrimaryKey(history.getContractId());
                        if (Objects.nonNull(contract)) {
                            history.setContractCode(contract.getCode());
                            history.setContractName(contract.getName());
                            history.setCustomerId(contract.getCustomerId());
                            history.setCustomerName(contract.getCustomerName());
                            //销售经理, 取主合同的销售经理
                            if (contract.getParentId() != null) {
                                Contract parentContract = contractMapper.selectByPrimaryKey(contract.getParentId());
                                history.setSalesManager(parentContract.getSalesManager());
                                if (parentContract.getSalesManager() != null) {
                                    final UserInfo userInfo = CacheDataUtils.findUserById(parentContract.getSalesManager());
                                    if (userInfo != null) {
                                        history.setSalesManagerName(userInfo.getName());
                                    }
                                }
                            }
                        }
                    }
                    projectContractRsChangeHistoryMapper.insertSelective(history);
                });
            }

            // 保存关联商机报价单快照
            ProjectBusinessRsExample businessRsExample = new ProjectBusinessRsExample();
            businessRsExample.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<ProjectBusinessRsChangeHistory> businessRsHistoryList =
                    BeanConverter.copy(projectBusinessRsMapper.selectByExample(businessRsExample), ProjectBusinessRsChangeHistory.class);

            if (CollectionUtils.isNotEmpty(businessRsHistoryList)) {
                businessRsHistoryList.forEach(history -> {
                    history.setHeaderId(historyHeader.getId());
                    history.setHistoryType(HistoryType.HISTORY.getCode());
                    history.setDeletedFlag(DeletedFlag.VALID.code());
                    history.setOriginId(history.getId());
                    history.setId(null);
                    projectBusinessRsChangeHistoryMapper.insertSelective(history);
                });
            }

            // 保存项目里程碑快照
            List<ProjectMilepostDto> milepostDtos = projectMilepostService.getProjectMilepostTree(project.getId(), null, false);

            if (CollectionUtils.isNotEmpty(milepostDtos)) {
                /* 记录里程碑快照 */
                for (ProjectMilepostDto parentMilepostDto : milepostDtos) {
                    ProjectMilepostChangeHistory parentHistory = BeanConverter.copy(parentMilepostDto, ProjectMilepostChangeHistory.class);
                    parentHistory.setOriginId(parentMilepostDto.getId());
                    parentHistory.setHistoryType(HistoryType.HISTORY.getCode());
                    parentHistory.setHeaderId(historyHeader.getId());
                    parentHistory.setId(null);
                    parentHistory.setChangeOrderNum(parentMilepostDto.getOrderNum());
                    projectMilepostChangeHistoryMapper.insert(parentHistory);

                    /* 记录里程碑并行交付线快照 */
                    if (Boolean.TRUE.equals(parentMilepostDto.getParallelDeliveryLineFlag()) && CollectionUtil.isNotEmpty(parentMilepostDto.getGroupList())) {
                        List<ProjectMilepostGroupDto> projectMilepostGroups = parentMilepostDto.getGroupList();
                        projectMilepostGroups.forEach(groupDto -> {
                            /* 默认项目里程碑并行交付线状态 */
                            ProjectMilepostGroupDto.initStartStatus(groupDto);
                            ProjectMilepostGroupChangeHistory groupHistory = BeanConverter.copy(groupDto, ProjectMilepostGroupChangeHistory.class);
                            groupHistory.setProjectMilepostId(parentHistory.getOriginId());
                            groupHistory.setDeletedFlag(false);
                            groupHistory.setHistoryType(HistoryType.HISTORY.getCode());
                            groupHistory.setHeaderId(historyHeader.getId());
                            groupHistory.setOriginId(groupDto.getId());
                            groupHistory.setId(null);
                            projectMilepostGroupChangeHistoryMapper.insert(groupHistory);

                            /* 记录子里程碑快照 */
                            if (CollectionUtils.isNotEmpty(groupDto.getProjectMilepostDtoList())) {
                                groupDto.getProjectMilepostDtoList().forEach(sonProjectMilepostDto -> {
                                    ProjectMilepostChangeHistory childHistory = BeanConverter.copy(sonProjectMilepostDto,
                                            ProjectMilepostChangeHistory.class);
                                    childHistory.setOriginId(sonProjectMilepostDto.getId());
                                    childHistory.setGroupId(groupHistory.getOriginId());
                                    childHistory.setParentId(parentHistory.getOriginId());
                                    childHistory.setHistoryType(HistoryType.HISTORY.getCode());
                                    childHistory.setHeaderId(historyHeader.getId());
                                    childHistory.setId(null);
                                    childHistory.setChangeOrderNum(sonProjectMilepostDto.getOrderNum());
                                    projectMilepostChangeHistoryMapper.insert(childHistory);
                                });
                            }
                        });
                    }
                }
            }

            // ProjectProfit
            ProjectProfitExample projectProfitExample = new ProjectProfitExample();
            projectProfitExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
            List<ProjectProfit> projectProfitList = projectProfitMapper.selectByExample(projectProfitExample);
            if (ListUtils.isNotEmpty(projectProfitList)) {
                for (ProjectProfit projectProfit : projectProfitList) {
                    ProjectProfitChangeHistory projectProfitChangeHistory = BeanConverter.copy(projectProfit, ProjectProfitChangeHistory.class);
                    projectProfitChangeHistory.setProfitId(projectProfit.getId());
                    projectProfitChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                    projectProfitChangeHistory.setHeaderId(historyHeader.getId());
                    projectProfitChangeHistory.setId(null);
                    projectProfitChangeHistoryMapper.insert(projectProfitChangeHistory);
                }
            }

            // ProjectIncomeCostPlan todo

            // ProjectBudgetFee
            ProjectBudgetFeeExample projectBudgetFeeExample = new ProjectBudgetFeeExample();
            projectBudgetFeeExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
            List<ProjectBudgetFee> projectBudgetFeeList =
                    projectBudgetFeeMapper.selectByExample(projectBudgetFeeExample);
            if (ListUtils.isNotEmpty(projectBudgetFeeList)) {
                for (ProjectBudgetFee projectBudgetFee : projectBudgetFeeList) {
                    ProjectBudgetFeeChangeHistory projectBudgetFeeChangeHistory = BeanConverter.copy(projectBudgetFee,
                            ProjectBudgetFeeChangeHistory.class);
                    projectBudgetFeeChangeHistory.setOriginId(projectBudgetFee.getId());
                    projectBudgetFeeChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                    projectBudgetFeeChangeHistory.setHeaderId(historyHeader.getId());
                    projectBudgetFeeChangeHistory.setId(null);
                    projectBudgetFeeChangeHistoryMapper.insert(projectBudgetFeeChangeHistory);
                }
            }

            // ProjectBudgetHuman
            ProjectBudgetHumanExample projectBudgetHumanExample = new ProjectBudgetHumanExample();
            projectBudgetHumanExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
            List<ProjectBudgetHuman> projectBudgetHumanList =
                    projectBudgetHumanMapper.selectByExample(projectBudgetHumanExample);
            if (ListUtils.isNotEmpty(projectBudgetHumanList)) {
                for (ProjectBudgetHuman projectBudgetHuman : projectBudgetHumanList) {
                    ProjectBudgetHumanChangeHistory projectBudgetHumanChangeHistory = BeanConverter.copy(projectBudgetHuman,
                            ProjectBudgetHumanChangeHistory.class);
                    projectBudgetHumanChangeHistory.setOriginId(projectBudgetHuman.getId());
                    projectBudgetHumanChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                    projectBudgetHumanChangeHistory.setHeaderId(historyHeader.getId());
                    projectBudgetHumanChangeHistory.setId(null);
                    projectBudgetHumanChangeHistoryMapper.insert(projectBudgetHumanChangeHistory);
                }
            }

            // ProjectBudgetMaterial
            ProjectBudgetMaterialExample projectBudgetMaterialExample = new ProjectBudgetMaterialExample();
            projectBudgetMaterialExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
            List<ProjectBudgetMaterial> projectBudgetMaterialList =
                    projectBudgetMaterialMapper.selectByExample(projectBudgetMaterialExample);
            if (ListUtils.isNotEmpty(projectBudgetMaterialList)) {
                for (ProjectBudgetMaterial projectBudgetMaterial : projectBudgetMaterialList) {
                    ProjectBudgetMaterialChangeHistory projectBudgetMaterialChangeHistory = BeanConverter.copy(projectBudgetMaterial,
                            ProjectBudgetMaterialChangeHistory.class);
                    projectBudgetMaterialChangeHistory.setOriginId(projectBudgetMaterial.getId());
                    projectBudgetMaterialChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                    projectBudgetMaterialChangeHistory.setHeaderId(historyHeader.getId());
                    projectBudgetMaterialChangeHistory.setId(null);
                    projectBudgetMaterialChangeHistoryMapper.insert(projectBudgetMaterialChangeHistory);
                }
            }

            // ProjectBudgetTravel
            ProjectBudgetTravelExample projectBudgetTravelExample = new ProjectBudgetTravelExample();
            projectBudgetTravelExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
            List<ProjectBudgetTravel> projectBudgetTravelList =
                    projectBudgetTravelMapper.selectByExample(projectBudgetTravelExample);
            if (ListUtils.isNotEmpty(projectBudgetTravelList)) {
                for (ProjectBudgetTravel projectBudgetTravel : projectBudgetTravelList) {
                    ProjectBudgetTravelChangeHistory projectBudgetTravelChangeHistory =
                            BeanConverter.copy(projectBudgetTravel, ProjectBudgetTravelChangeHistory.class);
                    projectBudgetTravelChangeHistory.setOriginId(projectBudgetTravel.getId());
                    projectBudgetTravelChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                    projectBudgetTravelChangeHistory.setHeaderId(historyHeader.getId());
                    projectBudgetTravelChangeHistory.setId(null);
                    projectBudgetTravelChangeHistoryMapper.insert(projectBudgetTravelChangeHistory);
                }
            }

            // ProjectBudgetAsset
            ProjectBudgetAssetExample projectBudgetAssetExample = new ProjectBudgetAssetExample();
            projectBudgetAssetExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
            List<ProjectBudgetAsset> projectBudgetAssetList =
                    projectBudgetAssetMapper.selectByExample(projectBudgetAssetExample);
            if (ListUtils.isNotEmpty(projectBudgetAssetList)) {
                for (ProjectBudgetAsset projectBudgetAsset : projectBudgetAssetList) {
                    ProjectBudgetAssetChangeHistory projectBudgetAssetChangeHistory =
                            BeanConverter.copy(projectBudgetAsset, ProjectBudgetAssetChangeHistory.class);
                    projectBudgetAssetChangeHistory.setOriginId(projectBudgetAsset.getId());
                    projectBudgetAssetChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                    projectBudgetAssetChangeHistory.setHeaderId(historyHeader.getId());
                    projectBudgetAssetChangeHistory.setId(null);
                    projectBudgetAssetChangeHistoryMapper.insert(projectBudgetAssetChangeHistory);
                }
            }

            // wbs预算
            BigDecimal sumBudgetCost = BigDecimal.ZERO;
            ProjectWbsBudgetExample projectWbsBudgetExample = new ProjectWbsBudgetExample();
            projectWbsBudgetExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
            List<ProjectWbsBudget> projectWbsBudgetList = projectWbsBudgetMapper.selectByExample(projectWbsBudgetExample);
            if (CollectionUtils.isNotEmpty(projectWbsBudgetList)) {
                for (ProjectWbsBudget projectWbsBudget : projectWbsBudgetList) {
                    ProjectWbsBudgetChangeHistory history = BeanConverter.copy(projectWbsBudget, ProjectWbsBudgetChangeHistory.class);
                    sumBudgetCost = sumBudgetCost.add(history.getPrice());
                    history.setOriginId(projectWbsBudget.getId());
                    history.setHistoryType(HistoryType.HISTORY.getCode());
                    history.setHeaderId(historyHeader.getId());
                    history.setId(null);
                    projectWbsBudgetChangeHistoryMapper.insert(history);
                }
            }
            // wbs预算基线
            ProjectWbsBudgetBaselineExample projectWbsBudgetBaselineExample = new ProjectWbsBudgetBaselineExample();
            projectWbsBudgetBaselineExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
            List<ProjectWbsBudgetBaseline> projectWbsBudgetBaselineList =
                    projectWbsBudgetBaselineMapper.selectByExample(projectWbsBudgetBaselineExample);
            if (CollectionUtils.isNotEmpty(projectWbsBudgetBaselineList)) {
                for (ProjectWbsBudgetBaseline baseline : projectWbsBudgetBaselineList) {
                    ProjectWbsBudgetBaselineChangeHistory history = BeanConverter.copy(baseline, ProjectWbsBudgetBaselineChangeHistory.class);
                    history.setOriginId(baseline.getId());
                    history.setHistoryType(HistoryType.HISTORY.getCode());
                    history.setHeaderId(historyHeader.getId());
                    history.setId(null);
                    projectWbsBudgetBaselineChangeHistoryMapper.insert(history);
                }
            }

            // 记录金额汇总（wbs历史基线查询用到）
            ProjectBudgetChangeSummaryHistoryExample summaryHistoryExample = new ProjectBudgetChangeSummaryHistoryExample();
            summaryHistoryExample.createCriteria().andHeaderIdEqualTo(historyHeader.getId());
            List<ProjectBudgetChangeSummaryHistory> summaryHistoryList =
                    projectBudgetChangeSummaryHistoryMapper.selectByExample(summaryHistoryExample);
            ProjectBudgetChangeSummaryHistory summaryHistory = new ProjectBudgetChangeSummaryHistory();
            if (CollectionUtils.isNotEmpty(summaryHistoryList)) {
                summaryHistory = summaryHistoryList.get(0);
            }
            summaryHistory.setProjectId(project.getId());
            summaryHistory.setAmount(projectContractRsService.calculateProjectAmount(project.getId()));
            summaryHistory.setExcludingTaxAmount(projectContractRsService.calculateProjectExcludingTaxAmount(project.getId()));
            summaryHistory.setBudgetCost(sumBudgetCost);
            summaryHistory.setHeaderId(historyHeader.getId());
            if (null == summaryHistory.getId()) {
                projectBudgetChangeSummaryHistoryMapper.insert(summaryHistory);
            } else {
                projectBudgetChangeSummaryHistoryMapper.updateByPrimaryKey(summaryHistory);
            }

            // milepostDesignPlanDetail
            MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
            milepostDesignPlanDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
            List<MilepostDesignPlanDetail> milepostDesignPlanDetailList =
                    milepostDesignPlanDetailMapper.selectByExample(milepostDesignPlanDetailExample);
            if (ListUtils.isNotEmpty(milepostDesignPlanDetailList)) {
                for (MilepostDesignPlanDetail milepostDesignPlanDetail : milepostDesignPlanDetailList) {
                    MilepostDesignPlanDetailChange milepostDesignPlanDetailChange =
                            BeanConverter.copy(milepostDesignPlanDetail, MilepostDesignPlanDetailChange.class);
                    milepostDesignPlanDetailChange.setMilepostId(milepostDesignPlanDetail.getId());
                    milepostDesignPlanDetailChange.setHistoryType(HistoryType.HISTORY.getCode());
                    milepostDesignPlanDetailChange.setChangeRecordId(historyHeader.getId());
                    milepostDesignPlanDetailChange.setId(null);
                    milepostDesignPlanDetailChangeMapper.insert(milepostDesignPlanDetailChange);
                }
            }
        }
        return projectList.size();
    }

    @Override
    public ReceiptPlanDetailDTO checkReceiptPlanDetail(Long contractId, Long milepostTemplateId, String milepostStage
            , Long projectId) {
        Assert.notNull(contractId, "合同id不能为空");
        Assert.notNull(milepostStage, "里程碑阶段名不能为空");

        List<MilepostTemplateStage> milepostTemplateStages;
        List<ReceiptPlanDetail> receiptPlanDetails = new ArrayList<>();
        if (null != projectId) {

            // 项目变更、预立项转正时，当回款计划关联的里程碑不是当前项目的里程碑(产生原因：项目立项->作废，之后未重置回款计划详情表)
            ReceiptPlanDetailExample receiptPlanDetailExample2 = new ReceiptPlanDetailExample();
            receiptPlanDetailExample2.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(false);
            List<ReceiptPlanDetail> detailList = receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample2);
            if (ListUtils.isNotEmpty(detailList)) {
                List<Long> mileoneIds = detailList.stream().map(ReceiptPlanDetail::getMilestoneId).collect(Collectors.toList());
                ProjectMilepostExample projectMilepostExample = new ProjectMilepostExample();
                projectMilepostExample.createCriteria().andIdIn(mileoneIds).andNameEqualTo(milepostStage);
                List<ProjectMilepost> projectMileposts = projectMilepostMapper.selectByExample(projectMilepostExample);
                if (ListUtils.isNotEmpty(projectMileposts)) {
                    ReceiptPlanDetailExample receiptPlanDetailExample = new ReceiptPlanDetailExample();
                    receiptPlanDetailExample.createCriteria().andContractIdEqualTo(contractId).andMilestoneIdEqualTo(projectMileposts.get(0).getId()).andDeletedFlagEqualTo(false);
                    receiptPlanDetails = receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample);
                }
            }

            // 项目变更、预立项转正时，当回款计划关联的里程碑是当前项目的里程碑
            if (ListUtils.isEmpty(receiptPlanDetails)) {
                Project project = projectMapper.selectByPrimaryKey(projectId);
                ProjectMilepostExample projectMilepostExample = new ProjectMilepostExample();
                projectMilepostExample.createCriteria().andProjectIdEqualTo(project.getId()).andNameEqualTo(milepostStage).andDeletedFlagEqualTo(false);
                List<ProjectMilepost> projectMileposts = projectMilepostMapper.selectByExample(projectMilepostExample);

                if (ListUtils.isNotEmpty(projectMileposts)) {
                    ReceiptPlanDetailExample receiptPlanDetailExample = new ReceiptPlanDetailExample();
                    receiptPlanDetailExample.createCriteria().andContractIdEqualTo(contractId).andMilestoneIdEqualTo(projectMileposts.get(0).getId()).andDeletedFlagEqualTo(false);
                    receiptPlanDetails = receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample);
                }
            }

            //当项目变更、预立项转正时，回款计划关联的是里程碑模板id
            if (ListUtils.isEmpty(receiptPlanDetails)) {
                ReceiptPlanDetailExample receiptPlanDetailExample = new ReceiptPlanDetailExample();
                receiptPlanDetailExample.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(false);
                List<ReceiptPlanDetail> receiptPlanDetailList =
                        receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample);
                if (ListUtils.isNotEmpty(receiptPlanDetailList)) {
                    List<Long> templateId =
                            receiptPlanDetailList.stream().map(ReceiptPlanDetail::getMilestoneId).collect(Collectors.toList());
                    MilepostTemplateStageExample milepostTemplateStageExample = new MilepostTemplateStageExample();
                    milepostTemplateStageExample.createCriteria().andIdIn(templateId).andMilepostStageEqualTo(milepostStage).andDeletedFlagEqualTo(false);
                    milepostTemplateStages = milepostTemplateStageMapper.selectByExample(milepostTemplateStageExample);
                    if (ListUtils.isNotEmpty(milepostTemplateStages)) {
                        ReceiptPlanDetailExample receiptPlanDetailExample1 = new ReceiptPlanDetailExample();
                        receiptPlanDetailExample1.createCriteria().andContractIdEqualTo(contractId).andMilestoneIdEqualTo(milepostTemplateStages.get(0).getId()).andDeletedFlagEqualTo(false);
                        receiptPlanDetails = receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample1);
                    }

                }
            }

        } else {
            com.midea.mcomponent.core.util.Assert.notNull(milepostTemplateId, "里程碑阶段id不能为空");

            // 当项目立项、预立项，回款计划关联的是里程碑模板id时
            MilepostTemplateStageExample milepostTemplateStageExample = new MilepostTemplateStageExample();
            milepostTemplateStageExample.createCriteria().andMilepostTemplateIdEqualTo(milepostTemplateId).andMilepostStageEqualTo(milepostStage).andDeletedFlagEqualTo(false);
            milepostTemplateStages = milepostTemplateStageMapper.selectByExample(milepostTemplateStageExample);

            if (ListUtils.isNotEmpty(milepostTemplateStages)) {
                List<Long> mileoneIds =
                        milepostTemplateStages.stream().map(MilepostTemplateStage::getId).collect(Collectors.toList());
                ReceiptPlanDetailExample receiptPlanDetailExample = new ReceiptPlanDetailExample();
                receiptPlanDetailExample.createCriteria().andContractIdEqualTo(contractId).andMilestoneIdIn(mileoneIds).andDeletedFlagEqualTo(false);
                receiptPlanDetails = receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample);
            }

            //当项目立项、项目预立项，回款计划关联的是里程碑id时
            if (ListUtils.isEmpty(receiptPlanDetails)) {
                ReceiptPlanDetailExample receiptPlanDetailExample = new ReceiptPlanDetailExample();
                receiptPlanDetailExample.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(false);
                List<ReceiptPlanDetail> receiptPlanDetailList =
                        receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample);
                if (ListUtils.isNotEmpty(receiptPlanDetailList)) {
                    List<Long> milepostIds = receiptPlanDetailList.stream().map(ReceiptPlanDetail::getMilestoneId).collect(Collectors.toList());

                    ProjectMilepostExample projectMilepostExample = new ProjectMilepostExample();
                    projectMilepostExample.createCriteria().andIdIn(milepostIds).andNameEqualTo(milepostStage);
                    List<ProjectMilepost> projectMileposts =
                            projectMilepostMapper.selectByExample(projectMilepostExample);

                    if (ListUtils.isNotEmpty(projectMileposts)) {
                        ReceiptPlanDetailExample example = new ReceiptPlanDetailExample();
                        example.createCriteria().andContractIdEqualTo(contractId).andMilestoneIdEqualTo(projectMileposts.get(0).getId()).andDeletedFlagEqualTo(false);
                        receiptPlanDetails = receiptPlanDetailMapper.selectByExample(example);
                    }
                }
            }
        }

        ReceiptPlanDetailDTO receiptPlanDetailDTO = new ReceiptPlanDetailDTO();
        receiptPlanDetailDTO.setHasRelevance(ListUtils.isNotEmpty(receiptPlanDetails) ? true : false);
        receiptPlanDetailDTO.setReceiptPlanDetails(receiptPlanDetails);
        return receiptPlanDetailDTO;
    }

    /**
     * 项目预算总额为0，不允许结转(CB01)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectBudgetItem(ProjectDto project) {
        String[] headers = {"序号", "项目类型", "收入确认节点", "实际开始日期", "实际结束日期"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        List<ProjectCheckReturnVo> list = new ArrayList<>();
        if (project.getBudgetCost() == null || project.getBudgetCost().compareTo(BigDecimal.ZERO) == 0) {
            ProjectCheckReturnVo projectCheckReturnVo = new ProjectCheckReturnVo();
            projectCheckReturnVo.setC0("1");
            if (project.getProjectType() != null) {
                projectCheckReturnVo.setC1(project.getProjectType().getName());
            }
            projectCheckReturnVo.setC2(project.getProjectMilepostName());
            projectCheckReturnVo.setC3(DateUtil.format(project.getWaitForCarryoverCollectionDateStart()));
            projectCheckReturnVo.setC4(DateUtil.format(project.getWaitForCarryoverCollectionDateEnd()));
            list.add(projectCheckReturnVo);
        }
        projectCheckExcelVo.setReturnList(list);
        return projectCheckExcelVo;
    }

    /**
     * 项目无客户，无法获取往来段(CB02)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectCustomerItem(ProjectDto project) {
        String[] headers = {"序号", "项目类型", "收入确认节点", "实际开始日期", "实际结束日期"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        List<ProjectCheckReturnVo> list = new ArrayList<>();
        // 先判断借方科目是否配置往来段
        ProjectType projectType = project.getProjectType();
        if (projectType != null && null != projectType.getSubjectConfig()) {
            JSONObject object = JSON.parseObject(projectType.getSubjectConfig());
            //获取成本科目-借方
            JSONObject costDebit = JSON.parseObject(object.get("costDebit").toString());
            //往来 第六段
            if (Objects.equals(costDebit.get("comeAndGo"), "客户")) {
                // 查询项目是否关联客户
                ProjectContractRsExample example = new ProjectContractRsExample();
                example.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<ProjectContractRs> projectContractRs = projectContractRsMapper.selectByExample(example);
                if (project.getCustomerId() == null && ListUtils.isEmpty(projectContractRs)) {
                    ProjectCheckReturnVo projectCheckReturnVo = new ProjectCheckReturnVo();
                    projectCheckReturnVo.setC0("1");
                    projectCheckReturnVo.setC1(projectType.getName());
                    projectCheckReturnVo.setC2(project.getProjectMilepostName());
                    projectCheckReturnVo.setC3(DateUtil.format(project.getWaitForCarryoverCollectionDateStart()));
                    projectCheckReturnVo.setC4(DateUtil.format(project.getWaitForCarryoverCollectionDateEnd()));
                    list.add(projectCheckReturnVo);
                } else if (project.getCustomerId() == null && ListUtils.isNotEmpty(projectContractRs)) {
                    Long contractId = projectContractRs.get(0).getContractId();
                    Assert.notNull(contractId, "项目关联合同不存在！");
                    Contract contract = contractMapper.selectByPrimaryKey(contractId);
                    project.setCustomerId(contract.getCustomerId());
                }
            }
        }
        projectCheckExcelVo.setReturnList(list);
        return projectCheckExcelVo;
    }

    /**
     * 外币项目无结转汇率，无法结转(CB03)
     *
     * @param projectDto 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkProjectCarryoverBillConversionRateItem(ProjectDto projectDto) {
        String[] headers = {"序号", "项目类型", "收入确认节点", "实际开始日期", "实际结束日期", "合同币种", "汇率类型"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(projectDto.getCode());
        projectCheckExcelVo.setProjectName(projectDto.getName());
        projectCheckExcelVo.setHeaders(headers);

        if (Objects.equals(projectDto.getCurrency(), projectDto.getLocalCurrency())) {
            return projectCheckExcelVo;
        }
        List<ProjectCheckReturnVo> list = new ArrayList<>();

        GlDailyRateQuery query = new GlDailyRateQuery();
        query.setFromCurrency(projectDto.getCurrency());
        query.setToCurrency(projectDto.getLocalCurrency());
        query.setExchangeType(CorUserStatus.Corporate.getCode());
        query.setExchangeDate(DateUtil.getBeginningOfDay(projectDto.getWaitForCarryoverCollectionDateEnd()));
        GlDailyRateDto glDailyRateDto = basedataExtService.queryGlDailyRate(query);
        if (glDailyRateDto == null || glDailyRateDto.getExchangeRate() == null) {
            ProjectCheckReturnVo projectCheckReturnVo = new ProjectCheckReturnVo();
            projectCheckReturnVo.setC0("1");
            if (projectDto.getProjectType() != null) {
                projectCheckReturnVo.setC1(projectDto.getProjectType().getName());
            }
            projectCheckReturnVo.setC2(projectDto.getProjectMilepostName());
            projectCheckReturnVo.setC3(DateUtil.format(projectDto.getWaitForCarryoverCollectionDateStart()));
            projectCheckReturnVo.setC4(DateUtil.format(projectDto.getWaitForCarryoverCollectionDateEnd()));
            projectCheckReturnVo.setC5(projectDto.getCurrency());
            projectCheckReturnVo.setC6(CorUserStatus.Corporate.getName());
            list.add(projectCheckReturnVo);
        }

        projectCheckExcelVo.setReturnList(list);
        return projectCheckExcelVo;
    }

    /**
     * 客户企业性质为空，不允许结转(CB04)
     *
     * @param project 项目
     * @return ProjectCheckExcelVo
     */
    @Override
    public ProjectCheckExcelVo checkCustomerEnterpriseNature(ProjectDto project) {
        String[] headers = {"序号", "项目类型", "收入确认节点", "实际开始日期", "实际结束日期"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        List<ProjectCheckReturnVo> list = new ArrayList<>();
        // 先判断借方科目是否配置往来段
        ProjectType projectType = project.getProjectType();
        if (projectType != null && null != projectType.getSubjectConfig()) {
            JSONObject object = JSON.parseObject(projectType.getSubjectConfig());
            //获取成本科目-借方
            JSONObject costDebit = JSON.parseObject(object.get("costDebit").toString());
            //往来 第六段
            if (Objects.equals(costDebit.get("comeAndGo"), "客户")) {
                // 查询项目关联客户的企业性质 是否为空
                if (project.getCustomerId() == null || isNUllCustomerEnterpriseNature(project.getCustomerId())) {
                    ProjectCheckReturnVo projectCheckReturnVo = new ProjectCheckReturnVo();
                    projectCheckReturnVo.setC0("1");
                    projectCheckReturnVo.setC1(projectType.getName());
                    projectCheckReturnVo.setC2(project.getProjectMilepostName());
                    projectCheckReturnVo.setC3(DateUtil.format(project.getWaitForCarryoverCollectionDateStart()));
                    projectCheckReturnVo.setC4(DateUtil.format(project.getWaitForCarryoverCollectionDateEnd()));
                    list.add(projectCheckReturnVo);
                }
            }
        }
        projectCheckExcelVo.setReturnList(list);
        return projectCheckExcelVo;
    }

    private boolean isNUllCustomerEnterpriseNature(Long customerId) {
        CustomerDto customerDto = crmExtService.getCustomerById(customerId);
        return Optional.ofNullable(customerDto)
                .map(CustomerDto::getEnterpriseNature)
                .orElse(null) == null;
    }

    @Override
    public Map<String, Object> getEmsBudgetInfoList(SdpQuery sdpQuery) {
        Long ouId = sdpQuery.getOuId();
        Guard.notNull(ouId, "业务实体ID不能为空");

        Map<Long, UserInfo> userInfoMap = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("offSet", sdpQuery.getOffset());
        resultMap.put("limit", sdpQuery.getLimit());

        if (sdpQuery.getProjectId() != null) {
            List<ProjectSdpVo> singleList = getEmsBudgetInfoListByProjectIds(ouId, Arrays.asList(sdpQuery.getProjectId()), userInfoMap);
            resultMap.put("total", singleList.size());
            resultMap.put("resultList", singleList);

            if (Objects.equals(sdpQuery.getIsExport(), 1) && CollectionUtils.isNotEmpty(singleList)) {
                List<ProjectFeeCollectionSdpVo> detailList = new ArrayList<>();
                singleList.forEach(s -> detailList.addAll(Optional.ofNullable(s.getEmsBudgetList()).orElse(new ArrayList<>())));
                resultMap.put("detailList", detailList);
            }
            return resultMap;
        }

        // 分页
        int total = projectExtMapper.countIdsProjectBudgetIncrementData(sdpQuery);
        List<Long> projectIdList = projectExtMapper.selectIdsProjectBudgetIncrementData(sdpQuery);
        if (total == 0 || CollectionUtils.isEmpty(projectIdList)) {
            resultMap.put("total", 0);
            resultMap.put("resultList", new ArrayList<>());
            return resultMap;
        }

        Collections.sort(projectIdList); //按项目id升序
        List<ProjectSdpVo> resultList = new ArrayList<>();
        List<List<Long>> lists = ListUtils.splistList(projectIdList, 500);
        for (List<Long> list : lists) {
            resultList.addAll(getEmsBudgetInfoListByProjectIds(ouId, list, userInfoMap));
        }
        resultMap.put("total", total);
        resultMap.put("resultList", resultList);

        if (Objects.equals(sdpQuery.getIsExport(), 1) && CollectionUtils.isNotEmpty(resultList)) {
            List<ProjectFeeCollectionSdpVo> detailList = new ArrayList<>();
            resultList.forEach(s -> detailList.addAll(Optional.ofNullable(s.getEmsBudgetList()).orElse(new ArrayList<>())));
            resultMap.put("detailList", detailList);
        }
        return resultMap;
    }

    private List<ProjectSdpVo> getEmsBudgetInfoListByProjectIds(Long ouId, List<Long> projectIdList, Map<Long, UserInfo> userInfoMap) {
        if (CollectionUtils.isEmpty(projectIdList)) {
            return new ArrayList<>();
        }
        //批量查询项目
        List<ProjectSdpVo> projectSdpVoList = projectExtMapper.selectSdpVoByIds(projectIdList, ouId);
        //批量查询预算单元信息
        Map<Long, List<ProjectFeeCollectionSdpVo>> projectFeeCollectionMap = projectExtMapper.selectEmsBudgetSdpVoByIds(projectIdList).stream().collect(Collectors.groupingBy(ProjectFeeCollectionSdpVo::getProjectId));

        List<ProjectSdpVo> resultList = new ArrayList<>();
        for (ProjectSdpVo projectSdpVo : projectSdpVoList) {
            //预算单元信息
            List<ProjectFeeCollectionSdpVo> projectFeeCollectionSdpVoList = projectFeeCollectionMap.get(projectSdpVo.getProjectId());
            if (CollectionUtils.isEmpty(projectFeeCollectionSdpVoList)) {
                continue;
            }
            projectSdpVo.setEmsBudgetList(projectFeeCollectionSdpVoList);

            //项目经理
            UserInfo manager = userInfoMap.get(projectSdpVo.getManagerId());
            if (manager == null) {
                manager = CacheDataUtils.findUserById(projectSdpVo.getManagerId());
                userInfoMap.put(projectSdpVo.getManagerId(), manager);
            }
            if (manager != null) {
                projectSdpVo.setManagerMip(manager.getUsername());
                projectSdpVo.setManagerName(manager.getName());
            }
            //财务
            UserInfo financial = userInfoMap.get(projectSdpVo.getFinancial());
            if (financial == null) {
                financial = CacheDataUtils.findUserById(projectSdpVo.getFinancial());
                userInfoMap.put(projectSdpVo.getFinancial(), financial);
            }
            if (financial != null) {
                projectSdpVo.setFinanceMip(financial.getUsername());
                projectSdpVo.setFinanceName(financial.getName());
            }
            //创建人
            UserInfo createBy = userInfoMap.get(projectSdpVo.getCreateBy());
            if (createBy == null) {
                createBy = CacheDataUtils.findUserById(projectSdpVo.getFinancial());
                userInfoMap.put(projectSdpVo.getCreateBy(), createBy);
            }
            if (createBy != null) {
                projectSdpVo.setCreateByMip(createBy.getUsername());
                projectSdpVo.setCreateByName(createBy.getName());
            }
            projectSdpVo.setManagerId(null);
            projectSdpVo.setFinancial(null);
            projectSdpVo.setCreateBy(null);
            resultList.add(projectSdpVo);
        }
        return resultList;
    }

    @Override
    public ProjectCheckExcelVo checkProjectPurchaseContractEffectPunishment(Project project) {

        String[] headers = {"序号", "罚扣编号", "罚扣标题", "状态", "供应商确认状态", "供应商编号"
                , "供应商名称", "罚扣类型", "币种", "罚扣金额（不含税）", "罚扣日期", "采购合同编号"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        int num = 1;//导出明细计数
        List<PurchaseContractPunishmentVo> punishments = purchaseContractPunishmentExtMapper.getNotValidPurchaseContractPunishmentsByProjectId(project.getId());
        if (ListUtils.isNotEmpty(punishments)) {
            projectCheckExcelVo.setPass(Boolean.FALSE);
            for (PurchaseContractPunishmentVo vo : punishments) {
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0(String.valueOf(num));
                returnVo.setC1(vo.getCode());
                returnVo.setC2(vo.getTitle());
                returnVo.setC3(PurchaseContractPunishmentApproveEnums.getMsgByCode(vo.getStatus()));
                returnVo.setC4(PurchaseContractPunishmentSupplierStatusEnums.getMsgByCode(vo.getSupplierConfirmStatus()));
                returnVo.setC5(vo.getVendorCode());
                returnVo.setC6(vo.getVendorName());
                returnVo.setC7(vo.getPunishmentType());
                returnVo.setC8(vo.getCurrency());
                returnVo.setC9(BigDecimalUtils.scaleAndToString(vo.getAmount()));
                returnVo.setC10(DateUtil.format(vo.getPunishmentDate(), DateUtil.DATE_PATTERN));
                returnVo.setC11(vo.getContractCode());
                returnVoList.add(returnVo);
                num++;
            }
        }
        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    @Override
    public ProjectCheckExcelVo isPaidAmountExceedingContractAmount(Project project) {
        String[] headers = {"序号", "合同编号", "合同名称", "总金额（含税）", "已付款", "在途金额"
                , "罚扣（含税）", "剩余付款"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setReturnList(null);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        int num = 1;//导出明细计数
        List<ContractPaymentSummary> contractPaymentSummaries = purchaseContractExtMapper.queryAvailableContractPaymentSummaryByProjectId(project.getId());
        if (ListUtils.isNotEmpty(contractPaymentSummaries)) {
            for (ContractPaymentSummary summary : contractPaymentSummaries) {
                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0(String.valueOf(num));
                returnVo.setC1(summary.getContractCode());
                returnVo.setC2(summary.getContractName());
                returnVo.setC3(BigDecimalUtils.scaleAndToString(summary.getContractAmount()));
                returnVo.setC4(BigDecimalUtils.scaleAndToString(summary.getFinalActualAmount()));
                returnVo.setC5(BigDecimalUtils.scaleAndToString(summary.getFinalOnTheWayAmount()));
                returnVo.setC6(BigDecimalUtils.scaleAndToString(summary.getFinalPenaltyAmount()));
                returnVo.setC7(BigDecimalUtils.scaleAndToString(summary.getSurplusAmount()));
                returnVoList.add(returnVo);
                num++;
            }
        }
        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    @Override
    public int publishProjectWbsBudgetVersion(Long headerId) {
        String url = String.format("%sstatistics/project/wbsBudgetVersion/publish/%d", ModelsEnum.STATISTICS.getBaseUrl(), headerId);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Boolean> response =   JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return 1;
    }

    private boolean filterAllowDifference(String differenceAmountStr) {
        BigDecimal differenceAmount = new BigDecimal(differenceAmountStr);
        //允差+-1
        if (differenceAmount.compareTo(new BigDecimal("1")) > 0 || differenceAmount.compareTo(new BigDecimal("-1")) < 0) {
            return true;
        }
        return false;
    }

}
