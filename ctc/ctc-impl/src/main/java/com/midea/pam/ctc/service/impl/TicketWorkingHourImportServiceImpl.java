package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.HashBasedTable;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.LaborCostDto;
import com.midea.pam.common.basedata.dto.LaborExternalCostDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.OrgLaborCostTypeSetDTO;
import com.midea.pam.common.basedata.entity.LaborCost;
import com.midea.pam.common.basedata.entity.OrgLaborCostTypeSet;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.TicketTasksDTO;
import com.midea.pam.common.ctc.dto.TicketWorkingHourImportDTO;
import com.midea.pam.common.ctc.dto.TicketWorkingHourImportDetailDTO;
import com.midea.pam.common.ctc.dto.TicketWorkingHourImportSummaryDTO;
import com.midea.pam.common.ctc.dto.WorkingHourDto;
import com.midea.pam.common.ctc.entity.CnbEmpBasicData;
import com.midea.pam.common.ctc.entity.CnbEmpBasicDataExample;
import com.midea.pam.common.ctc.entity.IhrAttendDetailExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.TicketTasksRecord;
import com.midea.pam.common.ctc.entity.TicketWorkingHourImport;
import com.midea.pam.common.ctc.entity.TicketWorkingHourImportDetail;
import com.midea.pam.common.ctc.entity.TicketWorkingHourImportDetailExample;
import com.midea.pam.common.ctc.entity.TicketWorkingHourImportExample;
import com.midea.pam.common.ctc.entity.TicketWorkingHourImportSummary;
import com.midea.pam.common.ctc.entity.WbsTemplateRule;
import com.midea.pam.common.ctc.entity.WbsTemplateRuleExample;
import com.midea.pam.common.ctc.entity.WorkingHour;
import com.midea.pam.common.ctc.entity.WorkingHourExample;
import com.midea.pam.common.ctc.excelVo.ProjectWorkingHourImportDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.ProjectWorkingHourImportExcelVo;
import com.midea.pam.common.ctc.excelVo.TicketWorkingHourImportDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourImportDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourTemplateDeptExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourTemplateRoleExcelVo;
import com.midea.pam.common.ctc.excelVo.WbsWorkingHourTemplateWbsExcelVo;
import com.midea.pam.common.ctc.query.TicketTasksQuery;
import com.midea.pam.common.ctc.query.TicketWorkingHourImportDetailQuery;
import com.midea.pam.common.ctc.query.TicketWorkingHourImportQuery;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.RoleType;
import com.midea.pam.common.enums.TicketTasksEnum;
import com.midea.pam.common.enums.TicketWorkingHourImportEnum;
import com.midea.pam.common.enums.UserType;
import com.midea.pam.common.enums.WorkingHourImportType;
import com.midea.pam.common.enums.WorkingHourSourceFlag;
import com.midea.pam.common.enums.WorkingHourStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.CnbEmpBasicDataMapper;
import com.midea.pam.ctc.mapper.IhrAttendDetailMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.TicketWorkingHourImportDetailMapper;
import com.midea.pam.ctc.mapper.TicketWorkingHourImportExtMapper;
import com.midea.pam.ctc.mapper.TicketWorkingHourImportMapper;
import com.midea.pam.ctc.mapper.TicketWorkingHourImportSummaryMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleMapper;
import com.midea.pam.ctc.mapper.WorkingHourExtMapper;
import com.midea.pam.ctc.mapper.WorkingHourMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.TicketTasksService;
import com.midea.pam.ctc.service.TicketWorkingHourImportService;
import com.midea.pam.ctc.service.WorkingHourService;
import com.midea.pam.ctc.service.config.CommonPropertiesConfig;
import com.midea.pam.ctc.service.helper.OrgLaborCostTypeSetHelper;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.midea.pam.common.enums.WorkingHourImportType.WBS;

/**
 * <AUTHOR>
 * @date 2021/3/11
 */
public class TicketWorkingHourImportServiceImpl implements TicketWorkingHourImportService {

    private static final Logger logger = LoggerFactory.getLogger(TicketWorkingHourImportServiceImpl.class);

    @Resource
    private TicketWorkingHourImportMapper ticketWorkingHourImportMapper;
    @Resource
    private TicketWorkingHourImportSummaryMapper ticketWorkingHourImportSummaryMapper;
    @Resource
    private TicketWorkingHourImportDetailMapper ticketWorkingHourImportDetailMapper;
    @Resource
    private TicketWorkingHourImportExtMapper ticketWorkingHourImportExtMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private WorkingHourMapper workingHourMapper;
    @Resource
    private WorkingHourExtMapper workingHourExtMapper;
    @Resource
    private TicketTasksService ticketTasksService;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private ProjectService projectService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private CommonPropertiesConfig commonPropertiesConfig;
    @Resource
    private OrgLaborCostTypeSetHelper orgLaborCostTypeSetHelper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private WbsTemplateRuleMapper wbsTemplateRuleMapper;
    @Resource
    private IhrAttendDetailMapper ihrAttendDetailMapper;
    @Resource
    private WorkingHourService workingHourService;
    @Resource
    private CnbEmpBasicDataMapper cnbEmpBasicDataMapper;


    @Override
    public PageInfo<TicketWorkingHourImportDTO> page(TicketWorkingHourImportQuery ticketWorkingHourImportQuery) {
        TicketWorkingHourImportExample example = buildTicketWorkingHourImportExample(ticketWorkingHourImportQuery);
        PageHelper.startPage(ticketWorkingHourImportQuery.getPageNum(), ticketWorkingHourImportQuery.getPageSize());
        List<TicketWorkingHourImport> importList = ticketWorkingHourImportMapper.selectByExample(example);
        return BeanConverter.convertPage(importList, TicketWorkingHourImportDTO.class);
    }

    @Override
    public TicketWorkingHourImportDTO getSummaryInfo(Long importId) {
        TicketWorkingHourImport ticketWorkingHourImport = ticketWorkingHourImportMapper.selectByPrimaryKey(importId);
        if (ticketWorkingHourImport == null) {
            throw new BizException(100, "参数错误");
        } else {
            //批量查询工时明细
            TicketWorkingHourImportDetailQuery query = new TicketWorkingHourImportDetailQuery();
            query.setImportId(importId);
            List<TicketWorkingHourImportDetailDTO> importDetailDTOList = ticketWorkingHourImportExtMapper.listTicketWorkingHourImportDetailDTO(query);
            for (TicketWorkingHourImportDetailDTO detailDTO : importDetailDTOList) {
                if (Objects.equals(ticketWorkingHourImport.getImportType(), WorkingHourImportType.WBS.getCode())) {
                    //前端工单工时导入和WBS工时导入的角色取的是同一个名称。对于后端业务来说是不同的取自不同的表，这里兼容一下
                    detailDTO.setRoleName(detailDTO.getLaborWbsCostName());
                }
            }
            Map<Long, List<TicketWorkingHourImportDetailDTO>> importDetailDTOMap = importDetailDTOList.stream().collect(Collectors.groupingBy(TicketWorkingHourImportDetailDTO::getProjectId));

            //查询按项目汇总行
            List<TicketWorkingHourImportSummaryDTO> importSummaryDTOList = ticketWorkingHourImportExtMapper.listTicketWorkingHourImportSummaryDTO(importId);
            for (TicketWorkingHourImportSummaryDTO importSummaryDTO : importSummaryDTOList) {
                if (importSummaryDTO.getManagerId() != null) {
                    UserInfo userInfo = CacheDataUtils.findUserById(importSummaryDTO.getManagerId());
                    if (userInfo != null) {
                        importSummaryDTO.setManagerMip(userInfo.getUsername()); //项目经理mip
                    }
                }
                importSummaryDTO.setImportDetailDTOlist(importDetailDTOMap.get(importSummaryDTO.getProjectId()));
            }
            TicketWorkingHourImportDTO importDTO = BeanConverter.copy(ticketWorkingHourImport, TicketWorkingHourImportDTO.class);
            importDTO.setSummaryDTOList(importSummaryDTOList);
            return importDTO;
        }
    }

    @Override
    public PageInfo<TicketWorkingHourImportDetailDTO> getDetailPage(TicketWorkingHourImportDetailQuery ticketWorkingHourImportDetailQuery) {
        PageHelper.startPage(ticketWorkingHourImportDetailQuery.getPageNum(), ticketWorkingHourImportDetailQuery.getPageSize());
        if (StringUtils.isNotBlank(ticketWorkingHourImportDetailQuery.getEmployeeTypeStr())) {
            List<String> employeeTypeList = Arrays.asList(ticketWorkingHourImportDetailQuery.getEmployeeTypeStr().split(","));
            ticketWorkingHourImportDetailQuery.setEmployeeTypeList(employeeTypeList);
        }
        List<TicketWorkingHourImportDetailDTO> importDetailDTOList = ticketWorkingHourImportExtMapper.listTicketWorkingHourImportDetailDTO(ticketWorkingHourImportDetailQuery);
        for (TicketWorkingHourImportDetailDTO detailDTO : importDetailDTOList) {
            if (Objects.equals(ticketWorkingHourImportDetailQuery.getImportType(), WorkingHourImportType.WBS.getCode())) {
                //前端工单工时导入和WBS工时导入的角色取的是同一个名称。对于后端业务来说是不同的取自不同的表，这里兼容一下
                detailDTO.setRoleName(detailDTO.getLaborWbsCostName());
            }
        }
        return new PageInfo<>(importDetailDTOList);
    }

    @Override
    public Response validImportDetail(List<TicketWorkingHourImportDetailExcelVo> detailImportExcelVoList, Long importId) {
        Map<String, Object> resultMap = new HashMap();
        // 校验数据并封装失败原因
        boolean result = doValidImportDetail(detailImportExcelVoList, importId);

        // 根据校验结果返回数据
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            List<TicketWorkingHourImportDetailDTO> detailDTOList = BeanConverter.copy(detailImportExcelVoList, TicketWorkingHourImportDetailDTO.class);
            resultMap.put("flag", Boolean.TRUE);
            resultMap.put("resultData", packageAndSummaryDetail(detailDTOList));
        } else {
            resultMap.put("flag", Boolean.FALSE);
            resultMap.put("errMsg", detailImportExcelVoList);
        }
        return dataResponse.setData(resultMap);
    }

    @Override
    public Response validImportDetailWbs(List<WbsWorkingHourImportDetailExcelVo> detailImportExcelVoList, Long importId) {
        Map<String, Object> resultMap = new HashMap();
        // 校验数据并封装失败原因
        boolean result = doValidImportDetailWbs(detailImportExcelVoList, importId);

        // 根据校验结果返回数据
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            List<TicketWorkingHourImportDetailDTO> detailDTOList = BeanConverter.copy(detailImportExcelVoList, TicketWorkingHourImportDetailDTO.class);
            resultMap.put("flag", Boolean.TRUE);
            resultMap.put("resultData", packageAndSummaryDetail(detailDTOList));
        } else {
            resultMap.put("flag", Boolean.FALSE);
            resultMap.put("errMsg", detailImportExcelVoList);
        }
        return dataResponse.setData(resultMap);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TicketWorkingHourImportDTO saveWithDetail(TicketWorkingHourImportDTO dto) {
        WorkingHourImportType workingHourImportType = WorkingHourImportType.getEnumByCode(dto.getImportType());
        Guard.notNull(workingHourImportType, "未定义的工时导入类型，请检查数据");

        if (StringUtils.isBlank(dto.getImportAnnex())) {
            throw new BizException(100, "导入数据文件不能为空");
        }

        List<TicketWorkingHourImportSummaryDTO> summaryDTOList = dto.getSummaryDTOList();
        if (ListUtils.isEmpty(summaryDTOList)) {
            throw new BizException(100, "导入工时数据不能为空");
        }

        //导入数据二次校验
        if (Objects.equals(workingHourImportType, WBS)) {
            this.wbsImportDataDoubleCheck(dto);
        }

        TicketWorkingHourImport ticketWorkingHourImport = BeanConverter.copy(dto, TicketWorkingHourImport.class);
        // 入参校验
        if (dto.getId() != null) {
            TicketWorkingHourImport oldImportDTO = ticketWorkingHourImportMapper.selectByPrimaryKey(dto.getId());
            if (oldImportDTO == null || !TicketWorkingHourImportEnum.DRAFT.getCode().equals(oldImportDTO.getStatus())) {
                throw new BizException(100, "参数错误或此状态不允许提交");
            } else if (oldImportDTO.getImportAnnex().equals(dto.getImportAnnex())) {
                ticketWorkingHourImportMapper.updateByPrimaryKeySelective(ticketWorkingHourImport);
                // 返回插入的数据
                return getSummaryInfo(ticketWorkingHourImport.getId());
            } else {
                // 软删除
                ticketWorkingHourImportExtMapper.deleteSummaryByImportId(dto.getId());
                ticketWorkingHourImportExtMapper.deleteDetailByImportId(dto.getId());
                int sourceFlag = WorkingHourSourceFlag.getSourceFlagByImportType(ticketWorkingHourImport.getImportType()); //来源
                workingHourExtMapper.batchUpdateDeletedFlag(dto.getId(), sourceFlag, true);
            }
        }

        if (ticketWorkingHourImport.getUnitId() == null) {
            ticketWorkingHourImport.setUnitId(SystemContext.getUnitId());
        }
        ticketWorkingHourImport.setStatus(TicketWorkingHourImportEnum.DRAFT.getCode());
        ticketWorkingHourImport.setDeletedFlag(false);

        if (dto.getId() == null) {
            ticketWorkingHourImport.setCreateName(SystemContext.getUserName());
            ticketWorkingHourImport.setCreateBy(SystemContext.getUserId());
            ticketWorkingHourImport.setUpdateBy(SystemContext.getUserId());
            ticketWorkingHourImport.setImportCode(generateImportCode(SystemContext.getUnitId()));
            ticketWorkingHourImportMapper.insert(ticketWorkingHourImport);
        } else {
            ticketWorkingHourImport.setUpdateAt(new Date());
            ticketWorkingHourImport.setUpdateBy(SystemContext.getUserId());
            ticketWorkingHourImportMapper.updateByPrimaryKeySelective(ticketWorkingHourImport);
        }

        ArrayList<TicketWorkingHourImportDetailDTO> detailDTOlist = Lists.newArrayList();
        for (TicketWorkingHourImportSummaryDTO importSummaryDTO : summaryDTOList) {
            TicketWorkingHourImportSummary importSummary = BeanConverter.copy(importSummaryDTO, TicketWorkingHourImportSummary.class);
            if (importSummaryDTO.getId() == null) {
                importSummary.setImportId(ticketWorkingHourImport.getId());
                importSummary.setDeletedFlag(false);
                importSummary.setCreateBy(SystemContext.getUserId());
                importSummary.setUpdateBy(SystemContext.getUserId());
                ticketWorkingHourImportSummaryMapper.insert(importSummary);
            } else {
                importSummary.setUpdateAt(new Date());
                importSummary.setUpdateBy(SystemContext.getUserId());
                ticketWorkingHourImportSummaryMapper.updateByPrimaryKeySelective(importSummary);
            }

            List<TicketWorkingHourImportDetailDTO> importDetailDTOlist = importSummaryDTO.getImportDetailDTOlist();
            if (ListUtils.isEmpty(importDetailDTOlist)) {
                throw new BizException(100, "导入工时数据不能为空");
            }
            detailDTOlist.addAll(importDetailDTOlist);
            List<TicketWorkingHourImportDetail> detailList = BeanConverter.copy(importDetailDTOlist, TicketWorkingHourImportDetail.class);
            for (TicketWorkingHourImportDetail importDetail : detailList) {
                if (importDetail.getId() == null) {
                    importDetail.setImportId(ticketWorkingHourImport.getId());
                    importDetail.setDeletedFlag(false);
                    importDetail.setCreateBy(SystemContext.getUserId());
                    importDetail.setUpdateBy(SystemContext.getUserId());
                    ticketWorkingHourImportDetailMapper.insert(importDetail);
                } else {
                    importDetail.setUpdateAt(new Date());
                    importDetail.setUpdateBy(SystemContext.getUserId());
                    ticketWorkingHourImportDetailMapper.updateByPrimaryKeySelective(importDetail);
                }
            }
        }

        // 插入导入工时的工单任务操作记录
        List<Long> ticketTasksIdList = detailDTOlist.stream().map(TicketWorkingHourImportDetailDTO::getTicketTasksId).distinct().collect(Collectors.toList());
        if (!ticketTasksIdList.isEmpty()) {
            for (Long ticketTasksId : ticketTasksIdList) {
                TicketTasksRecord ticketTasksRecord = new TicketTasksRecord();
                ticketTasksRecord.setTicketTasksId(ticketTasksId);
                ticketTasksRecord.setOperationType(TicketTasksEnum.OPERATION_IMPORT.code());
                ticketTasksRecord.setOperationTypeName(TicketTasksEnum.OPERATION_IMPORT.msg());
                ticketTasksRecord.setLookAt(TicketTasksEnum.OPERATION_IMPORT.msg());
                ticketTasksRecord.setResource(ticketWorkingHourImport.getId().toString());
                ticketTasksRecord.setDeletedFlag(false);
                ticketTasksRecord.setCreateAt(new Date());
                ticketTasksRecord.setCreateBy(SystemContext.getUserId());
                ticketTasksRecord.setUpdateAt(new Date());
                ticketTasksRecord.setUpdateBy(SystemContext.getUserId());
                ticketTasksService.insertTicketTasksRecord(ticketTasksRecord);
            }
        }

        // 批量查询项目OU
        List<Long> projectIdList = detailDTOlist.stream().map(TicketWorkingHourImportDetailDTO::getProjectId).distinct().collect(Collectors.toList());
        projectIdList.add(-1L); //防空
        Map<Long, Long> projectOuIdMap = projectService.getProjectByIds(projectIdList).stream().collect(Collectors.toMap(Project::getId, Project::getOuId));

        // 批量查询发薪OU
        List<Long> userIdList = detailDTOlist.stream().map(TicketWorkingHourImportDetailDTO::getUserId).distinct().collect(Collectors.toList());
        userIdList.add(-1L); //防空
        CnbEmpBasicDataExample cnbEmpBasicDataExample = new CnbEmpBasicDataExample();
        cnbEmpBasicDataExample.createCriteria().andUserIdIn(userIdList).andOuIdIsNotNull().andDeletedFlagEqualTo(Boolean.FALSE);
        cnbEmpBasicDataExample.setOrderByClause("create_at desc");
        List<CnbEmpBasicData> cnbEmpBasicDataList = cnbEmpBasicDataMapper.selectByExample(cnbEmpBasicDataExample);
        Map<String, Long> fullPayOuIdMap = cnbEmpBasicDataList.stream().collect(Collectors.toMap(s -> buildCnbEmpBasicDataKey(s.getUserId(), s.getStatisticDate()), s -> s.getOuId(), (a, b) -> a));

        // 保存数据到工时表
        for (TicketWorkingHourImportDetailDTO detailDTO : detailDTOlist) {
            WorkingHour workingHour = new WorkingHour();
            workingHour.setApplyDate(detailDTO.getAttendanceDate());
            workingHour.setApplyWorkingHours(detailDTO.getWorkingHour());
            workingHour.setStatus(WorkingHourStatus.DRAFT.getCode());
            workingHour.setUserId(detailDTO.getUserId());
            workingHour.setUserMip(detailDTO.getUserMip());
            UserInfo userInfo = CacheDataUtils.findUserById(detailDTO.getUserId());
            if (userInfo != null) {
                workingHour.setApplyOrg(cutOrgNamePath(userInfo));
                workingHour.setLaborCostTypeSetId(userInfo.getLaborCostTypeSetId());
            }
            workingHour.setProjectId(detailDTO.getProjectId());
            workingHour.setProjectCode(detailDTO.getProjectCode());
            workingHour.setProcessId(ticketWorkingHourImport.getId());
            // 应该是申请的多少确认的就是多少
            workingHour.setActualWorkingHours(detailDTO.getWorkingHour());
            workingHour.setRemarks("");
            workingHour.setDeleteFlag(0);
            workingHour.setCreateBy(SystemContext.getUserId());
            workingHour.setCreateAt(new Date());
            workingHour.setUpdateBy(SystemContext.getUserId());
            workingHour.setUpdateAt(new Date());
            // 原先封装就有误，原来取的是审批人类型
            workingHour.setUserType(UserType.getCodeByMsg(detailDTO.getEmployeeType()));
            workingHour.setBizUnitId(ticketWorkingHourImport.getUnitId());
            workingHour.setIsImport(false);
            workingHour.setRdmFlag(0);
            workingHour.setWriteOffStatus(0);
            // 业务角色
            workingHour.setLaborWbsCostId(detailDTO.getLaborWbsCostId());
            workingHour.setLaborWbsCostName(detailDTO.getLaborWbsCostName());
            workingHour.setLevel(detailDTO.getLaborWbsCostName()); //跟业务角色name一致
            // 下面两个字段未使用（已查看生产数据库确认）
            // workingHour.setStayApproveUserId(0L);
            // workingHour.setStayApproveUserMip("");
            // 封装level、laborCostType、costMoney
            if (UserType.INTERNAL.msg().equals(detailDTO.getEmployeeType()) || UserType.RECRUIT.msg().equals(detailDTO.getEmployeeType())) {
                packageLevelAndFee(workingHour);
            } else {
                // 外部人力
                // 外部人天价格
                workingHour.setCostMoney(detailDTO.getExternalProjectCost());
                // 外部level
                if (StringUtils.isBlank(workingHour.getLevel())) {
                    workingHour.setLevel(detailDTO.getRoleName());
                }
                // 外部费率类型
                if (StringUtils.isBlank(workingHour.getLaborCostType())) {
                    workingHour.setLaborCostType("role");
                }
            }
            workingHour.setVendorCode(detailDTO.getVendorCode());
            workingHour.setRoleName(detailDTO.getRoleName());
            // 人力费率来源单位id
            workingHour.setLaborCostSourceUnitId(orgLaborCostTypeSetHelper.getLaborCostSourceUnitIdByUserId(detailDTO.getUserId()));
            // 获取人力部门
            OrgLaborCostTypeSet orgLaborCostTypeSet = orgLaborCostTypeSetHelper.getLaborCostTypeByUserId(detailDTO.getUserId());
            workingHour.setOrgId(orgLaborCostTypeSet == null ? null : orgLaborCostTypeSet.getOrgId());
            workingHour.setOrgName(orgLaborCostTypeSet == null ? null : orgLaborCostTypeSet.getOrgName());
            // 考勤工时
            workingHour.setIhrAttendHours(detailDTO.getIhrAttendHours());

            // 项目OU
            workingHour.setOuId(projectOuIdMap.get(workingHour.getProjectId()));
            // 发薪OU
            Long fullPayOuId = fullPayOuIdMap.get(buildCnbEmpBasicDataKey(workingHour.getUserId(), DateUtil.format(workingHour.getApplyDate(), "yyyyMM")));
            if (fullPayOuId != null) {
                workingHour.setFullPayOuId(fullPayOuId);
            }

            switch (workingHourImportType) {
                case TICKET:
                    workingHour.setSourceFlag(WorkingHourSourceFlag.TICKET_IMPORT.getCode());
                    break;
                case WBS:
                    workingHour.setSourceFlag(WorkingHourSourceFlag.WBS_IMPORT.getCode());
                    //以下设值是为了和手工填写字段对其
                    workingHour.setWbsBudgetCode(detailDTO.getWbsSummaryCode());
                    if (StringUtils.isNotEmpty(detailDTO.getWbsSummaryCode())) {
                        workingHour.setWbsSummaryCode(String.format("%s-%s", detailDTO.getProjectCode(), detailDTO.getWbsSummaryCode()));
                    }
                    workingHour.setProjectActivityCode(detailDTO.getProjectActivityCode());
                    workingHour.setActualWorkingHours(null); //审批通过才回写确定工时数
                    workingHour.setCostMoney(detailDTO.getCostMoney());
                    break;
                default:
            }
            workingHourMapper.insert(workingHour);
        }

        // 返回插入的数据
        return getSummaryInfo(ticketWorkingHourImport.getId());
    }

    /**
     * WBS工时导入二次校验：
     * 1、项目的项目状态必须是：项目进行中、预立项转正审批中、项目变更中，否则，系统报错：项目状态必须是：项目进行中、预立项转正审批中、项目变更中
     * 2、一天最多可填工时校验，按组织参数：工时填报小时数上限，配置的数字，校验一个人一天最多可以填写在多个项目的小时数汇总，超过这个小时数，系统报错：YYYY/MM/DD填报的工时数合计：XX小时，超过了一天的最大可填报工时数XX小时
     * 3、使用单位层级的组织参数：工时填报是否受考勤工时的限制，参数值为：1时，校验一个人一天填报的工时汇总不超过考勤工时，超过时，系统报错：YYYY/MM/DD，填报的工时超过了考勤工时，参数值不为1时，不做校验。考勤工时可以通过hr系统获取
     * 4、校验填报的工时是否超预算：按项目类型中的“工时预算控制”配置为严控、提醒、还是不控，来控制工时填报超预算校验
     * 5、同一个项目、同一天、同一个角色+WBS的工时只允许有一条
     */
    private void wbsImportDataDoubleCheck(TicketWorkingHourImportDTO dto) {
        List<String> validResultList = new ArrayList<>();
        List<Long> projectIdList = new ArrayList<>();
        List<Long> userIdList = new ArrayList<>();
        List<Date> attendanceDateList = new ArrayList<>();
        List<WorkingHourDto> workingHourDtoList = new ArrayList<>();
        Map<String, BigDecimal> workingHourSumMap = new HashMap<>();
        for (TicketWorkingHourImportSummaryDTO importSummaryDTO : dto.getSummaryDTOList()) {
            List<TicketWorkingHourImportDetailDTO> importDetailDTOlist = importSummaryDTO.getImportDetailDTOlist();
            if (ListUtils.isEmpty(importDetailDTOlist)) {
                throw new BizException(100, "导入工时数据不能为空");
            }
            for (TicketWorkingHourImportDetailDTO importDetail : importDetailDTOlist) {
                projectIdList.add(importDetail.getProjectId());
                userIdList.add(importDetail.getUserId());
                attendanceDateList.add(importDetail.getAttendanceDate());
                //工时按日期+用户维度汇总
                String key = buildWorkingHourGroupKey(importDetail.getAttendanceDate(), importDetail.getUserId());
                workingHourSumMap.put(key, importDetail.getWorkingHour().add(workingHourSumMap.getOrDefault(key, BigDecimal.ZERO)));

                //唯一性校验
                this.workingHourUniqueCheck(importDetail, dto.getId(), validResultList);

                WorkingHourDto item = new WorkingHourDto();
                item.setProjectId(importDetail.getProjectId());
                item.setApplyDate(DateUtil.format(importDetail.getAttendanceDate(), "yyyy-MM-dd"));
                item.setWbsBudgetCode(importDetail.getWbsSummaryCode());
                item.setLaborWbsCostId(importDetail.getLaborWbsCostId());
                item.setLaborWbsCostName(importDetail.getLaborWbsCostName());
                item.setSourceFlag(5); //WBS工时导入
                if (importDetail.getWorkingHour() != null) {
                    item.setApplyWorkingHours(importDetail.getWorkingHour().floatValue());
                }
                workingHourDtoList.add(item);
            }
        }

        //组织参数校验
        Map<String, String> workingHourLimitCheckMap = this.organizationCustomDictCheck(userIdList, attendanceDateList, workingHourSumMap, null);
        if (!workingHourLimitCheckMap.isEmpty()) {
            validResultList.addAll(workingHourLimitCheckMap.values());
        }

        //预算校验
        Map<String, String> budgetValidCheckMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(workingHourDtoList)) {
            WorkingHourDto[] workingHourDtos = workingHourDtoList.toArray(new WorkingHourDto[workingHourDtoList.size()]);
            try {
                budgetValidCheckMap = workingHourService.budgetValid(workingHourDtos);
            } catch (Exception e) {
            }
        }
        if (!budgetValidCheckMap.isEmpty()) {
            validResultList.addAll(budgetValidCheckMap.values());
        }

        //可填报工时的项目状态
        List<Integer> projectStatusList = Arrays.asList(ProjectStatus.APPROVALED.getCode(), ProjectStatus.APPROVALING_PREVIEW.getCode(), ProjectStatus.CHANGING.getCode());
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andIdIn(projectIdList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<Project> projectList = projectMapper.selectByExample(projectExample);
        if (CollectionUtils.isNotEmpty(projectList) && projectList.stream().anyMatch(s -> !projectStatusList.contains(s.getStatus()))) {
            validResultList.add("项目状态必须是：项目进行中、预立项转正审批中、项目变更中");
        }

        if (CollectionUtils.isNotEmpty(validResultList)) {
            throw new BizException(100, Joiner.on("；").join(validResultList));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String updateStatusChecking(Long formInstanceId, Integer code) {
        final TicketWorkingHourImport ticketWorkingHourImport = ticketWorkingHourImportMapper.selectByPrimaryKey(formInstanceId);
        updateTicketWorkingHourImportStatus(formInstanceId, code);
        int sourceFlag = WorkingHourSourceFlag.getSourceFlagByImportType(ticketWorkingHourImport.getImportType()); //来源
        workingHourExtMapper.batchUpdateStatus(formInstanceId, sourceFlag, WorkingHourStatus.APPROVING.getCode());
        return "1";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String updateStatusReject(Long formInstanceId, Integer code) {
        final TicketWorkingHourImport ticketWorkingHourImport = ticketWorkingHourImportMapper.selectByPrimaryKey(formInstanceId);
        updateTicketWorkingHourImportStatus(formInstanceId, code);
        int sourceFlag = WorkingHourSourceFlag.getSourceFlagByImportType(ticketWorkingHourImport.getImportType()); //来源
        workingHourExtMapper.batchUpdateStatus(formInstanceId, sourceFlag, WorkingHourStatus.REJECT.getCode());
        return "1";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String updateStatusPass(Long formInstanceId, Integer code, String handlerId) {
        final TicketWorkingHourImport ticketWorkingHourImport = ticketWorkingHourImportMapper.selectByPrimaryKey(formInstanceId);
        updateTicketWorkingHourImportStatus(formInstanceId, code);
        UserInfo userInfo = CacheDataUtils.findUserByMip(handlerId);
        int sourceFlag = WorkingHourSourceFlag.getSourceFlagByImportType(ticketWorkingHourImport.getImportType()); //来源
        if (userInfo == null) {
            workingHourExtMapper.batchUpdateStatus(formInstanceId, sourceFlag, WorkingHourStatus.PASS.getCode());
        } else {
            workingHourExtMapper.batchUpdateStatusAndApproveInfo(formInstanceId, sourceFlag, WorkingHourStatus.PASS.getCode(), userInfo.getName(), userInfo.getId());
        }
        //WBS导入审批通过后把申请工时数回写到确定工时数
        if (Objects.equals(ticketWorkingHourImport.getImportType(), WorkingHourImportType.WBS.getCode())) {
            workingHourExtMapper.batchUpdateActualWorkingHours(formInstanceId, sourceFlag);
        }
        return "1";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String updateStatusDraftReturn(Long formInstanceId, Integer code) {
        final TicketWorkingHourImport ticketWorkingHourImport = ticketWorkingHourImportMapper.selectByPrimaryKey(formInstanceId);
        updateTicketWorkingHourImportStatus(formInstanceId, code);
        int sourceFlag = WorkingHourSourceFlag.getSourceFlagByImportType(ticketWorkingHourImport.getImportType()); //来源
        workingHourExtMapper.batchUpdateStatus(formInstanceId, sourceFlag, WorkingHourStatus.DRAFT.getCode());
        return "1";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String abandon(Long formInstanceId, Integer code) {
        final TicketWorkingHourImport ticketWorkingHourImport = ticketWorkingHourImportMapper.selectByPrimaryKey(formInstanceId);
        if (ticketWorkingHourImport == null) {
            return null;
        }
        if (!Objects.equals(ticketWorkingHourImport.getStatus(), TicketWorkingHourImportEnum.DRAFT.getCode())) {
            return null;
        }
        updateTicketWorkingHourImportStatus(formInstanceId, code);
        int sourceFlag = WorkingHourSourceFlag.getSourceFlagByImportType(ticketWorkingHourImport.getImportType()); //来源
        workingHourExtMapper.batchUpdateDeletedFlag(formInstanceId, sourceFlag, true);
        return "1";
    }

    @Override
    public String delete(Long formInstanceId, Integer code) {
        updateTicketWorkingHourImportStatus(formInstanceId, code);
        return "1";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String dropDraft(Long id) {
        TicketWorkingHourImport ticketWorkingHourImport = ticketWorkingHourImportMapper.selectByPrimaryKey(id);
        if (ticketWorkingHourImport == null) {
            throw new BizException(100, "参数错误");
        }
        if (!Objects.equals(ticketWorkingHourImport.getStatus(), TicketWorkingHourImportEnum.DRAFT.getCode())) {
            throw new BizException(100, "只能作废草稿状态的数据");
        }
        updateTicketWorkingHourImportStatus(id, TicketWorkingHourImportEnum.CANCEL.getCode());
        int sourceFlag = WorkingHourSourceFlag.getSourceFlagByImportType(ticketWorkingHourImport.getImportType()); //来源
        workingHourExtMapper.batchUpdateDeletedFlag(id, sourceFlag, true);
        return "1";
    }

    private void updateTicketWorkingHourImportStatus(Long formInstanceId, Integer code) {
        TicketWorkingHourImport updateItem = new TicketWorkingHourImport();
        updateItem.setId(formInstanceId);
        updateItem.setStatus(code);
        ticketWorkingHourImportMapper.updateByPrimaryKeySelective(updateItem);
    }

    private boolean doValidImportDetail(List<TicketWorkingHourImportDetailExcelVo> detailImportExcelVoList, Long importId) {
        boolean result = true;
        Long companyId = SystemContext.getUnitId();

        // 工单任务号与项目关联管理
        TicketTasksQuery ticketTasksQuery = new TicketTasksQuery();
        List<Integer> taskStatusList = Lists.newArrayList(TicketTasksEnum.PUBLISHED.code());
        ticketTasksQuery.setTaskStatusList(taskStatusList);
        Map<String, TicketTasksDTO> ticketTasksDTOMap = ticketTasksService.listTicketTask(ticketTasksQuery).stream().collect(Collectors.toMap(TicketTasksDTO::getTicketTaskCode, Function.identity(), (key1, key2) -> key2));
        if (ticketTasksDTOMap.isEmpty()) {
            throw new BizException(100, "当前使用单位下无已发布状态的工单任务");
        }

        //查询角色费率
        List<LaborCostDto> laborCostList = basedataExtService.getLaborCostList(companyId);
        Map<String, LaborCostDto> laborCostMap = laborCostList.stream().collect(Collectors.toMap(LaborCostDto::getName, Function.identity(), (k1, k2) -> k2));

        //查询人力点工费率
        final Map<String, Object> param = new HashMap<>(3);
        param.put("pageNum", 1);
        param.put("pageSize", Integer.MAX_VALUE);
        param.put("bizUnitId", companyId);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "laborExternalCost/selectLaborExternalCostPage", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<LaborExternalCostDto>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<LaborExternalCostDto>>>() {
        });
        List<LaborExternalCostDto> laborExternalCostDtoList = dataResponse.getData().getList();
        HashBasedTable<String, String, LaborExternalCostDto> laborExternalCostDtoTable = HashBasedTable.create();
        for (LaborExternalCostDto laborExternalCostDto : laborExternalCostDtoList) {
            laborExternalCostDtoTable.put(laborExternalCostDto.getVendorCode(), laborExternalCostDto.getName(), laborExternalCostDto);
        }

        //查询业务部门
        List<OrgLaborCostTypeSetDTO> orgLaborCostTypeSetList = basedataExtService.findOrgLaborCostTypes(companyId, null, null, null, null, null);
        Map<Long, OrgLaborCostTypeSetDTO> orgLaborCostTypeSetMap = orgLaborCostTypeSetList.stream().collect(Collectors.toMap(OrgLaborCostTypeSetDTO::getId, Function.identity(), (k1, k2) -> k2));

        //查询当前单位所有业务实体
        List<Long> ouIds = basedataExtService.queryCurrentUnitOu().stream().map(OperatingUnitDto::getOperatingUnitId).collect(Collectors.toList());

        //查询当前单位所有项目
        List<String> projectCodeList = detailImportExcelVoList.stream().map(TicketWorkingHourImportDetailExcelVo::getProjectCode).distinct().collect(Collectors.toList());
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andOuIdIn(ouIds).andCodeIn(projectCodeList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        Map<String, Project> projectMap = projectMapper.selectByExample(projectExample).stream().collect(Collectors.toMap(Project::getCode, Function.identity()));

        List<Long> userIdList = new ArrayList<>();
        List<Date> attendanceDateList = new ArrayList<>();
        Map<String, BigDecimal> workingHourSumMap = new HashMap<>();
        List<WorkingHourDto> workingHourCheckList = new ArrayList<>();
        HashSet<TicketWorkingHourImportDetailExcelVo> excelVoHashSet = new HashSet<>(detailImportExcelVoList.size());
        // 参数校验
        for (TicketWorkingHourImportDetailExcelVo detailExcelVo : detailImportExcelVoList) {
            Date attendanceDate = detailExcelVo.getAttendanceDate();
            String employeeType = detailExcelVo.getEmployeeType();
            String projectCode = detailExcelVo.getProjectCode();
            String ticketTaskCode = detailExcelVo.getTicketTaskCode();
            String userMip = detailExcelVo.getUserMip();
            String userName = detailExcelVo.getUserName();
            String roleName = detailExcelVo.getRoleName();
            BigDecimal workingHour = detailExcelVo.getWorkingHour();
            List<String> validResultList = new ArrayList<>();
            // 考勤日期校验
            if (attendanceDate == null) {
                validResultList.add("出勤日期不能为空");
            } else {
                if (attendanceDate.after(new Date())) {
                    validResultList.add("不能导入未来日期的工时");
                } else {
                    attendanceDateList.add(attendanceDate);
                }
            }
            // 工单任务号校验
            if (StringUtils.isBlank(ticketTaskCode)) {
                result = false;
                validResultList.add("工单任务号不能为空");
            } else {
                if (!ticketTasksDTOMap.containsKey(ticketTaskCode)) {
                    validResultList.add("工单任务号错误 当前使用单位无此工单任务或工单任务状态不等于已发布");
                } else {
                    // 项目号校验
                    if (StringUtils.isBlank(projectCode)) {
                        validResultList.add("项目号不能为空");
                    } else {
                        if (projectMap.get(projectCode) == null) {
                            validResultList.add("项目号不存在");
                        }
                        TicketTasksDTO ticketTasksDTO = ticketTasksDTOMap.get(ticketTaskCode);
                        if (ticketTasksDTO == null || !projectCode.equals(ticketTasksDTO.getProjectCode())) {
                            validResultList.add("工单任务号对应项目号错误");
                        } else {
                            detailExcelVo.setProjectId(ticketTasksDTO.getProjectId());
                            detailExcelVo.setTicketTasksId(ticketTasksDTO.getId());
                        }
                    }
                }
            }
            // 校验姓名
            if (StringUtils.isBlank(userName)) {
                validResultList.add("姓名/供应商名称不能为空");
            }
            // 工时初步校验
            if (Objects.isNull(workingHour) || workingHour.compareTo(BigDecimal.ZERO) <= 0) {
                validResultList.add("工时不能为空且不能小于0");
            }
            // 员工类型校验
            if (StringUtils.isBlank(employeeType) || UserType.getCodeByMsg(employeeType) == null) {
                validResultList.add("员工类型不能为空且只能为内部、人力点工、自招外包");
            } else {
                if (UserType.INTERNAL.msg().equals(employeeType) || UserType.RECRUIT.msg().equals(employeeType)) {
                    // MIP账号校验
                    if (StringUtils.isBlank(userMip)) {
                        validResultList.add("MIP账号不能为空");
                    } else {
                        UserInfo userInfo = CacheDataUtils.findUserByMip(userMip);
                        if (userInfo == null) {
                            validResultList.add("MIP账号不存在");
                        } else {
                            if ("N".equals(userInfo.getStatus())) {
                                validResultList.add("MIP账号已失效");
                            } else {
                                if (!userMip.equals(userInfo.getUsername())) {
                                    validResultList.add("姓名与MIP账号对应错误");
                                } else {
                                    detailExcelVo.setUserId(userInfo.getId());
                                    userIdList.add(userInfo.getId());
                                    // 检查人力费用是否配置
                                    try {
                                        checkLaborCost(userInfo.getId());
                                    } catch (ApplicationBizException e) {
                                        validResultList.add(e.getMessageText());
                                    } catch (Exception e) {
                                        validResultList.add(e.getMessage());
                                    }

                                    // 检查业务部门配置
                                    OrgLaborCostTypeSetDTO orgLaborCostTypeSet = orgLaborCostTypeSetMap.get(userInfo.getLaborCostTypeSetId());
                                    if (orgLaborCostTypeSet == null) {
                                        validResultList.add("该员工没有配置业务部门，请确认");
                                    } else {
                                        if (!Objects.equals(orgLaborCostTypeSet.getRoleType(), RoleType.getCodeByName(employeeType))) {
                                            validResultList.add(String.format("该员工所在的部门的类型不为'%s'，请确认", employeeType));
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // 校验角色
                    if (StringUtils.isBlank(roleName)) {
                        validResultList.add("角色不能为空");
                    } else {
                        LaborCostDto laborCostDto = laborCostMap.get(roleName);
                        if (laborCostDto == null || !Objects.equals(laborCostDto.getRoleType(), RoleType.getCodeByName(employeeType))) {
                            validResultList.add("角色不存在");
                        } else {
                            detailExcelVo.setLaborWbsCostId(laborCostDto.getId());
                            detailExcelVo.setLaborWbsCostName(laborCostDto.getName());
                        }
                    }
                } else {
                    // 校验供应商编码和角色
                    if (StringUtils.isBlank(userMip) || StringUtils.isBlank(roleName)) {
                        validResultList.add("员工类型为人力点工时供应商编码、角色必填");
                    } else {
                        LaborExternalCostDto laborExternalCostDto = laborExternalCostDtoTable.get(userMip, roleName);
                        if (laborExternalCostDto == null) {
                            validResultList.add("当前使用单位下不存在此供应商编码与角色对应关系");
                        } else {
                            detailExcelVo.setExternalProjectCost(laborExternalCostDto.getExternalProjectCost());
                        }
                    }
                }
            }

            // 判断工时唯一性
            List<WorkingHourDto> workingHourDtoList = new ArrayList<>();
            if (result && (UserType.INTERNAL.msg().equals(employeeType) || UserType.RECRUIT.msg().equals(employeeType))) {
                // 判断内部工时填报唯一性
                workingHourDtoList = workingHourExtMapper.selectProjectMemberInternalDistinct(detailExcelVo.getUserId(), DateUtils.format(attendanceDate, DateUtils.FORMAT_SHORT), detailExcelVo.getProjectId(), importId);
            } else if (result && UserType.HRO.msg().equals(employeeType)) {
                // 判断外部工时填报唯一性
                workingHourDtoList = workingHourExtMapper.selectProjectMemberExternalDistinct(userMip, DateUtils.format(attendanceDate, DateUtils.FORMAT_SHORT), detailExcelVo.getProjectId(), roleName, importId);
            }

            if (ListUtils.isNotEmpty(workingHourDtoList)) {
                String errorMsg = "当前出勤日期在项目【" + workingHourDtoList.get(0).getProjectCode() + "】已存在工时";
                validResultList.add(errorMsg);
            }

            // 判断数据是否有重复，添加失败则说明数据重复，重复则提示
            if (!excelVoHashSet.add(detailExcelVo)) {
                validResultList.add("按项目/人/天/角色维度存在重复数据");
            }

            //工时按日期+用户维度汇总
            if (attendanceDate != null && detailExcelVo.getUserId() != null) { //填的出勤日期和用户都是有效的
                String key = buildWorkingHourGroupKey(attendanceDate, detailExcelVo.getUserId());
                workingHourSumMap.put(key, workingHour.add(workingHourSumMap.getOrDefault(key, BigDecimal.ZERO)));
            }

            detailExcelVo.setFailureReason(Joiner.on("，").join(validResultList));

            // 校验结果不为空，
            if (!validResultList.isEmpty()) {
                result = false;
            } else {
                WorkingHourDto item = new WorkingHourDto();
                item.setProjectId(detailExcelVo.getProjectId());
                item.setApplyDate(DateUtil.format(attendanceDate, "yyyy-MM-dd"));
                item.setUserId(detailExcelVo.getUserId());
                item.setSourceFlag(4); //工单工时导入
                if (workingHour != null) {
                    item.setApplyWorkingHours(workingHour.floatValue());
                }
                workingHourCheckList.add(item);
            }
        }

        //所有字段校验通过进一步校验：
        //1.一天最多可填工时校验，按组织参数：工时填报小时数上限，配置的数字，校验一个人一天最多可以填写在多个项目的小时数汇总
        if (result) {
            Map<String, BigDecimal> ihrHourMap = new HashMap<>();

            //组织参数校验
            Map<String, String> workingHourLimitCheckMap = this.organizationCustomDictCheck(userIdList, attendanceDateList, workingHourSumMap, ihrHourMap);

            //预算校验
            Map<String, String> checkBudgetCanSubmitMapMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(workingHourCheckList)) {
                WorkingHourDto[] workingHourDtos = workingHourCheckList.toArray(new WorkingHourDto[workingHourCheckList.size()]);
                checkBudgetCanSubmitMapMap = workingHourService.checkBudgetCanSubmit(workingHourDtos);
            }

            for (TicketWorkingHourImportDetailExcelVo detailExcelVo : detailImportExcelVoList) {
                List<String> validResultList1 = new ArrayList<>();
                if (!checkBudgetCanSubmitMapMap.isEmpty()) {
                    //人天价格不存在
                    String errorBudgetNotExist = checkBudgetCanSubmitMapMap.get(buildCostMoneyNotExistKey(detailExcelVo.getProjectId(), detailExcelVo.getUserId()));
                    if (StringUtils.isNotBlank(errorBudgetNotExist)) {
                        validResultList1.add(errorBudgetNotExist);
                    }
                    //预算不足
                    String errorBudgetNotEnough = checkBudgetCanSubmitMapMap.get(buildBudgetCheckKey(detailExcelVo.getProjectId()));
                    if (StringUtils.isNotBlank(errorBudgetNotEnough)) {
                        validResultList1.add(errorBudgetNotEnough);
                    }
                }

                if ((UserType.INTERNAL.msg().equals(detailExcelVo.getEmployeeType()) || UserType.RECRUIT.msg().equals(detailExcelVo.getEmployeeType()))
                        && !workingHourLimitCheckMap.isEmpty()) { //员工类型为内部or自招外包才做校验
                    //工时填报上限
                    String errorOneDayLimit = workingHourLimitCheckMap.get(buildWorkingHourLimitKey(detailExcelVo.getUserId() + "", DateUtil.format(detailExcelVo.getAttendanceDate(), "yyyy/MM/dd"), "oneDayLimit"));
                    if (StringUtils.isNotBlank(errorOneDayLimit)) {
                        validResultList1.add(errorOneDayLimit);
                    }
                }

                //考勤工时
                detailExcelVo.setIhrAttendHours(ihrHourMap.get(buildWorkingHourGroupKey(detailExcelVo.getAttendanceDate(), detailExcelVo.getUserId())));
                detailExcelVo.setFailureReason(Joiner.on("；").join(validResultList1));

                // 校验结果不为空，
                if (!validResultList1.isEmpty()) {
                    result = false;
                }
            }
        }

        return result;
    }

    private String buildCostMoneyNotExistKey(Long projectId, Long userId) {
        return String.format("buildCostMoneyNotExistKey_%s_%s", projectId, userId);
    }

    private String buildBudgetCheckKey(Long projectId) {
        return String.format("buildBudgetCheckKey_%s", projectId);
    }

    /**
     * 校验逻辑说明：
     * 1、必填字段不能为空，如果为空，系统报错：必填字段不能为空
     * 2、项目编号必须在该使用单位下存在，否则，系统报错：项目编号不存在
     * 3、项目的项目状态必须是：项目进行中、预立项转正审批中、项目变更中，否则，系统报错：项目状态必须是：项目进行中、预立项转正审批中、项目变更中
     * 4、出勤日期的格式：YYYY/MM/DD，如：2021/04/03，并且是存在的日期，否则，系统报错：出勤日期有误
     * 5、姓名和mip必须存在，并且相匹配，否则，系统报错：填报成员的姓名和mip有误
     * 6、填报人所在部门必须存在，否则，系统报错：部门不存在（请下载最新模板，获取最新的部门、角色及WBS的相关信息！！！）
     * 7、角色名称，必须是：填报人所在部门，配置了可以选择用来填工时的角色，否则，系统报错：用户所在部门：XXX/XX/XXXXXX，没有配置角色：XXX，请联系相关财务进行配置（请下载最新模板，获取最新的部门、角色及WBS的相关信息！！！）
     * 8、WBS号，必须满足：填报人所在部门，配置了可以选择用来填工时的WBS，否则，系统报错：WBS号和用户所在部门：XXX/XX/XXXXXX，配置的可用WBS不匹配，请联系相关财务进行配置（请下载最新模板，获取最新的部门、角色及WBS的相关信息！！！）
     * 9、WBS号，必须在项目中存在，否则，系统报错：WBS在项目中不存在
     * 10、填报工时数，请填写正数，否则，系统报错：填报工时数有误
     * 11、填报工时数的校验：
     * （1）一天最多可填工时校验，按组织参数：工时填报小时数上限，配置的数字，校验一个人一天最多可以填写在多个项目的小时数汇总，超过这个小时数，系统报错：YYYY/MM/DD填报的工时数合计：XX小时，超过了一天的最大可填报工时数XX小时
     * （2）使用单位层级的组织参数：hr考勤校验，参数值为：1时，校验一个人一天填报的工时汇总不超过考勤工时，超过时，系统报错：YYYY/MM/DD，填报的工时超过了考勤工时，参数值不为1时，不做校验。考勤工时可以通过hr系统获取
     * （3）校验填报的工时是否超预算：按项目类型中的“工时预算控制”配置为严控、提醒、还是不控，来控制工时填报超预算校验
     *          a）配置为：严控，项目下该人员填写WBS+角色的工时，校验填写的工时*角色费率/8（角色费率取内部项目成本或内部研发成本，项目属性为研发项目的取内部研发成本，其他项目属性的取内部项目成本）汇总（按WBS+活动事项汇总）是否大于该WBS+活动事项的剩余可用预算，大于，系统报错：WBS：XXX，活动事项：XXX，预算不足
     *          b）配置为：提醒，校验填写的工时*角色费率/8（角色费率取内部项目成本或内部研发成本，项目属性为研发项目的取内部研发成本，其他项目属性的取内部项目成本）汇总（按WBS+活动事项汇总）+该WBS+活动事项的“在途成本”+“已发生成本”是否大于该WBS+活动事项的预算*配置的百分比（配置为：提醒后面填写的百分比），大于，流程提交审批时，邮件提醒项目经理
     *          c）配置为：不控，填报工时时不做超预算校验
     * 12、同一用户、同一个项目、同一天、同一个角色+WBS的工时只允许有一条，超过一条，系统报错：项目：XXX，WBS：XXX+角色：XXX不唯一，请合并填写
     * 13、活动事项校验：需要校验角色是否配置了活动事项，没有配置，系统报错：角色对应的活动事项缺失，请联系财务维护角色对应的活动事项
     * 14、人力费率校验：需要校验角色是否配置了人力费率，没有配置，系统报错：角色对应的人力费率缺失，请联系财务维护角色对应的人力费率
     * 15、最后一个收入节点对应的里程碑已交付，不允许继续填报工时
     */
    private boolean doValidImportDetailWbs(List<WbsWorkingHourImportDetailExcelVo> detailImportExcelVoList, Long importId) {
        Long unitId = SystemContext.getUnitId();
        boolean result = true;

        //查询当前单位所有业务实体
        List<Long> ouIds = basedataExtService.queryCurrentUnitOu().stream().map(OperatingUnitDto::getOperatingUnitId).collect(Collectors.toList());

        //查询当前单位所有项目
        List<String> projectCodeList = detailImportExcelVoList.stream().map(WbsWorkingHourImportDetailExcelVo::getProjectCode).distinct().collect(Collectors.toList());
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andOuIdIn(ouIds).andCodeIn(projectCodeList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        Map<String, Project> projectMap = projectMapper.selectByExample(projectExample).stream().collect(Collectors.toMap(Project::getCode, Function.identity()));

        //可填报工时的项目状态
        List<Integer> projectStatusList = Arrays.asList(ProjectStatus.APPROVALED.getCode(), ProjectStatus.APPROVALING_PREVIEW.getCode(), ProjectStatus.CHANGING.getCode());

        //查询所有wbs模板
        WbsTemplateRuleExample ruleExample = new WbsTemplateRuleExample();
        ruleExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        ruleExample.setOrderByClause("order_no asc");
        Map<Long, List<WbsTemplateRule>> wbsTemplateRuleMap = wbsTemplateRuleMapper.selectByExample(ruleExample).stream().collect(Collectors.groupingBy(e -> e.getWbsTemplateInfoId()));

        //查询部门/角色/wbs信息
        final Map<String, Object> params = new HashMap<>();
        params.put("companyId", unitId);
        String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgLaborCostTypeSet/getWbsWorkingHourImportTemplateInfo", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Map> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<Map>>() {
        });
        Map resultMap = dataResponse.getData();
        List<WbsWorkingHourTemplateDeptExcelVo> deptExcelVos = JSONObject.parseArray(resultMap.get("deptExcelVos").toString(), WbsWorkingHourTemplateDeptExcelVo.class);
        List<WbsWorkingHourTemplateRoleExcelVo> roleExcelVos = JSONObject.parseArray(resultMap.get("roleExcelVos").toString(), WbsWorkingHourTemplateRoleExcelVo.class);
        List<WbsWorkingHourTemplateWbsExcelVo> wbsExcelVos = JSONObject.parseArray(resultMap.get("wbsExcelVos").toString(), WbsWorkingHourTemplateWbsExcelVo.class);
        List<LaborCost> laborCosts = JSONObject.parseArray(resultMap.get("laborCosts").toString(), LaborCost.class);

        //可填报工时的部门
        List<String> deptNameList = deptExcelVos.stream().map(e -> e.getHrOrgName()).distinct().collect(Collectors.toList());
        //可填报工时的角色
        Map<String, List<WbsWorkingHourTemplateRoleExcelVo>> roleMap = roleExcelVos.stream().collect(Collectors.groupingBy(e -> e.getHrOrgName()));
        //可填报工时的wbs
        Map<String, List<WbsWorkingHourTemplateWbsExcelVo>> wbsMap = wbsExcelVos.stream().collect(Collectors.groupingBy(e -> buildWbsGroupKey(e.getHrOrgName(), e.getTemplateId(), e.getRuleId())));
        //角色费率
        Map<Long, LaborCost> laborCostMap = laborCosts.stream().collect(Collectors.toMap(LaborCost::getId, Function.identity(), (a, b) -> a));

        Map<String, BigDecimal> workingHourSumMap = new HashMap<>();
        List<Long> userIdList = new ArrayList<>();
        List<Date> attendanceDateList = new ArrayList<>();
        List<WorkingHourDto> workingHourDtoList = new ArrayList<>();
        Map<Long, Boolean> checkIncomeCompletedMap = new HashMap<>();
        HashSet<String> checkMergeHasSet = new HashSet<>(detailImportExcelVoList.size());
        //参数校验
        for (WbsWorkingHourImportDetailExcelVo detailExcelVo : detailImportExcelVoList) {
            List<String> validResultList = new ArrayList<>();
            // 项目编号
            Project project = null;
            String projectCode = detailExcelVo.getProjectCode();
            if (StringUtils.isBlank(projectCode)) {
                validResultList.add("项目编号不能为空");
            } else {
                project = projectMap.get(projectCode);
                if (project == null) {
                    validResultList.add("项目编号不存在");
                } else {
                    if (!projectStatusList.contains(project.getStatus())) {
                        validResultList.add("项目状态必须是：项目进行中、预立项转正审批中、项目变更中");
                    } else {
                        Long projectId = project.getId();
                        detailExcelVo.setProjectId(projectId);

                        //项目最后一个收入节点对应的里程碑已交付，不允许继续填报工时
                        Boolean completedFlag = checkIncomeCompletedMap.get(projectId);
                        if (completedFlag == null) {
                            completedFlag = projectService.checkIncomeCompleted(projectId);
                            checkIncomeCompletedMap.put(projectId, completedFlag);
                        }
                        if (completedFlag) {
                            validResultList.add(String.format("%s最后一个收入节点对应的里程碑已交付，不允许继续填报工时，如有疑问请联系项目财务", projectCode));
                        }
                    }
                }
            }

            // 出勤日期
            Date attendanceDate = detailExcelVo.getAttendanceDate();
            if (attendanceDate == null) {
                validResultList.add("出勤日期为空或格式不正确");
            } else {
                if (attendanceDate.after(new Date())) {
                    validResultList.add("不能导入未来日期的工时");
                } else {
                    attendanceDateList.add(attendanceDate);
                }
            }

            // 填报人员姓名
            String userName = detailExcelVo.getUserName();
            if (StringUtils.isBlank(userName)) {
                validResultList.add("填报人员姓名不能为空");
            }

            // 填报人员mip
            String userMip = detailExcelVo.getUserMip();
            if (StringUtils.isBlank(userMip)) {
                validResultList.add("填报人员mip不能为空");
            } else {
                UserInfo userInfo = CacheDataUtils.findUserByMip(userMip);
                if (userInfo == null) {
                    validResultList.add("填报人员mip不存在");
                } else {
                    if ("N".equals(userInfo.getStatus())) {
                        validResultList.add("填报人员mip已失效");
                    } else {
                        if (!Objects.equals(userInfo.getName(), userName)) {
                            validResultList.add("填报成员的姓名和mip有误");
                        } else {
                            detailExcelVo.setUserId(userInfo.getId());
                            userIdList.add(userInfo.getId());
                        }
                    }
                    detailExcelVo.setEmployeeType(Objects.equals(userInfo.getType(), "1") ? "内部" : "外部");
                }
            }

            // 角色名称
            String laborWbsCostName = detailExcelVo.getLaborWbsCostName();
            if (StringUtils.isBlank(laborWbsCostName)) {
                validResultList.add("角色名称不能为空");
            }

            // WBS号
            String wbsSummaryCode = detailExcelVo.getWbsSummaryCode();
            if (StringUtils.isBlank(wbsSummaryCode)) {
                validResultList.add("WBS号不能为空");
            }

            // 填报人员所在部门
            String orgName = detailExcelVo.getOrgName();
            if (StringUtils.isBlank(orgName)) {
                validResultList.add("填报人员所在部门不能为空");
            } else {
                if (!deptNameList.contains(orgName)) {
                    validResultList.add("部门不存在（请下载最新模板，获取最新的部门、角色及WBS的相关信息！！！）");
                } else {
                    //角色
                    List<WbsWorkingHourTemplateRoleExcelVo> roleExcelVoList = roleMap.get(orgName);
                    if (CollectionUtils.isEmpty(roleExcelVoList)) {
                        validResultList.add("填报人所在部门，没有配置可以选择用来填工时的角色");
                    } else {
                        Map<String, Long> roleExcelVoMap = roleExcelVoList.stream().collect(Collectors.toMap(e -> e.getLaborWbsCostName(), e -> e.getLaborWbsCostId(), (a, b) -> a));
                        Set<String> laborWbsCostNameSet = roleExcelVoMap.keySet();

                        if (StringUtils.isNotBlank(laborWbsCostName) && !laborWbsCostNameSet.contains(laborWbsCostName)) {
                            validResultList.add(String.format("用户所在部门：%s，没有配置角色：%s，请联系相关财务进行配置（请下载最新模板，获取最新的部门、角色及WBS的相关信息！！！）", orgName, laborWbsCostName));
                        } else if (laborWbsCostNameSet.contains(laborWbsCostName)) {
                            Long laborWbsCostId = roleExcelVoMap.get(laborWbsCostName);
                            if (laborWbsCostId != null) {
                                detailExcelVo.setLaborWbsCostId(laborWbsCostId);
                                LaborCost laborCost = laborCostMap.get(laborWbsCostId);
                                if (laborCost != null) {
                                    if (StringUtils.isEmpty(laborCost.getProjectActivityCode())) {
                                        validResultList.add("角色对应的活动事项缺失，请联系财务维护角色对应的活动事项");
                                    } else {
                                        detailExcelVo.setProjectActivityCode(laborCost.getProjectActivityCode());
                                    }

                                    if (project != null) {
                                        if (Objects.equals(project.getPriceType(), "3") && laborCost.getInternalDevelopmentCost() == null) {
                                            validResultList.add("角色对应的人力费率缺失，请联系财务维护角色对应的人力费率");
                                        } else if (!Objects.equals(project.getPriceType(), "3") && laborCost.getInternalProjectCost() == null) {
                                            validResultList.add("角色对应的人力费率缺失，请联系财务维护角色对应的人力费率");
                                        }
                                        detailExcelVo.setCostMoney(Objects.equals(project.getPriceType(), "3") ? laborCost.getInternalDevelopmentCost() : laborCost.getInternalProjectCost());
                                    }
                                } else {
                                    validResultList.add("角色对应的活动事项缺失，请联系财务维护角色对应的活动事项");
                                    validResultList.add("角色对应的人力费率缺失，请联系财务维护角色对应的人力费率");
                                }
                            }
                        }
                    }

                    //WBS
                    if (project != null) {
                        Long wbsTemplateInfoId = project.getWbsTemplateInfoId();
                        if (wbsTemplateInfoId == null) {
                            validResultList.add("项目没有配置wbs模板");
                        } else {
                            List<WbsTemplateRule> wbsTemplateRuleList = wbsTemplateRuleMap.get(wbsTemplateInfoId);
                            if (CollectionUtils.isEmpty(wbsTemplateRuleList)) {
                                validResultList.add("项目配置wbs模板不存在");
                            } else {
                                if (StringUtils.isNotBlank(wbsSummaryCode)) {
                                    String[] wbsArr = wbsSummaryCode.split("-");
                                    if (wbsTemplateRuleList.size() - 1 != wbsArr.length) { //因为填写wbs不填项目层，所以比对应的模板规则少1位
                                        validResultList.add("WBS在项目中不存在");
                                    } else {
                                        boolean wbsFlag = true;
                                        for (int i = 0; i < wbsTemplateRuleList.size(); i++) {
                                            if (i == 0) { //项目层不校验
                                                continue;
                                            }
                                            WbsTemplateRule wbsTemplateRule = wbsTemplateRuleList.get(i);
                                            List<WbsWorkingHourTemplateWbsExcelVo> wbsExcelVoList = wbsMap.get(buildWbsGroupKey(orgName, wbsTemplateInfoId, wbsTemplateRule.getId()));

                                            String wbs = wbsArr[i - 1]; //因为填写wbs不填项目层，所以wbs的第一位对应规则的第二位
                                            if (CollectionUtils.isNotEmpty(wbsExcelVoList)) {
                                                List<String> wbsList = wbsExcelVoList.stream().map(e -> e.getConstraint()).collect(Collectors.toList());
                                                if (!wbsList.contains(wbs)) {
                                                    wbsFlag = false;
                                                    break;
                                                }
                                            }
                                        }
                                        if (!wbsFlag) {
                                            validResultList.add(String.format("WBS号和用户所在部门：%s，配置的可用WBS不匹配，请联系相关财务进行配置（请下载最新模板，获取最新的部门、角色及WBS的相关信息！！！）", orgName));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 填报工时数
            if (BigDecimalUtils.isBigDecimal(detailExcelVo.getWorkingHour_dt())) {
                detailExcelVo.setWorkingHour(new BigDecimal(detailExcelVo.getWorkingHour_dt()));
                BigDecimal workingHour = detailExcelVo.getWorkingHour();
                if (workingHour == null || workingHour.compareTo(BigDecimal.ZERO) <= 0) {
                    validResultList.add("填报工时数有误");
                } else {
                    //工时按日期+用户维度汇总
                    if (attendanceDate != null && detailExcelVo.getUserId() != null) { //填的出勤日期和用户都是有效的
                        String key = buildWorkingHourGroupKey(attendanceDate, detailExcelVo.getUserId());
                        workingHourSumMap.put(key, workingHour.add(workingHourSumMap.getOrDefault(key, BigDecimal.ZERO)));
                    }
                }
            } else {
                validResultList.add("填报工时数有误");
            }

            if (validResultList.isEmpty()) {
                // 同一用户、同一个项目、同一天、同一个角色+WBS的工时只允许有一条，添加失败则说明数据重复
                if (!checkMergeHasSet.add(buildCheckMergeKey(detailExcelVo))) {
                    validResultList.add(String.format("项目：%s，WBS：%s+角色：%s不唯一，请合并填写", projectCode, wbsSummaryCode, laborWbsCostName));
                }

                // 判断工时唯一性：用户+项目+角色+日期+WBS
                this.workingHourUniqueCheck(BeanConverter.copy(detailExcelVo, TicketWorkingHourImportDetail.class), importId, validResultList);
            }

            detailExcelVo.setFailureReason(Joiner.on("；").join(validResultList));

            // 校验结果不为空，
            if (!validResultList.isEmpty()) {
                result = false;
            } else {
                WorkingHourDto item = new WorkingHourDto();
                item.setProjectId(detailExcelVo.getProjectId());
                item.setApplyDate(DateUtil.format(attendanceDate, "yyyy-MM-dd"));
                item.setWbsBudgetCode(wbsSummaryCode);
                item.setLaborWbsCostId(detailExcelVo.getLaborWbsCostId());
                item.setLaborWbsCostName(laborWbsCostName);
                item.setSourceFlag(5); //WBS工时导入
                if (detailExcelVo.getWorkingHour() != null) {
                    item.setApplyWorkingHours(detailExcelVo.getWorkingHour().floatValue());
                }
                workingHourDtoList.add(item);
            }

        }

        //所有字段校验通过进一步校验：
        //1.一天最多可填工时校验，按组织参数：工时填报小时数上限，配置的数字，校验一个人一天最多可以填写在多个项目的小时数汇总
        //2.使用单位层级的组织参数：hr考勤校验，参数值为：1时，校验一个人一天填报的工时汇总不超过考勤工时
        //3.校验填报的工时是否超预算
        if (result) {
            Map<String, BigDecimal> ihrHourMap = new HashMap<>();

            //组织参数校验
            Map<String, String> workingHourLimitCheckMap = this.organizationCustomDictCheck(userIdList, attendanceDateList, workingHourSumMap, ihrHourMap);

            //预算校验
            Map<String, String> budgetValidCheckMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(workingHourDtoList)) {
                WorkingHourDto[] workingHourDtos = workingHourDtoList.toArray(new WorkingHourDto[workingHourDtoList.size()]);
                try {
                    budgetValidCheckMap = workingHourService.budgetValid(workingHourDtos);
                } catch (Exception e) {
                }
            }

            for (WbsWorkingHourImportDetailExcelVo detailExcelVo : detailImportExcelVoList) {
                List<String> validResultList1 = new ArrayList<>();
                if (!budgetValidCheckMap.isEmpty()) {
                    //预算不存在
                    String errorBudgetNotExist = budgetValidCheckMap.get(buildBudgetNotExistKey(detailExcelVo.getProjectId()));
                    if (StringUtils.isNotBlank(errorBudgetNotExist)) {
                        validResultList1.add(errorBudgetNotExist);
                    }
                    //预算不足
                    String errorBudgetNotEnough = budgetValidCheckMap.get(buildBudgetNotEnoughKey(detailExcelVo.getProjectId(), detailExcelVo.getWbsSummaryCode(), detailExcelVo.getProjectActivityCode()));
                    if (StringUtils.isNotBlank(errorBudgetNotEnough)) {
                        validResultList1.add(errorBudgetNotEnough);
                    }
                }

                if (!workingHourLimitCheckMap.isEmpty()) {
                    //工时填报上限
                    String errorOneDayLimit = workingHourLimitCheckMap.get(buildWorkingHourLimitKey(detailExcelVo.getUserId() + "", DateUtil.format(detailExcelVo.getAttendanceDate(), "yyyy/MM/dd"), "oneDayLimit"));
                    if (StringUtils.isNotBlank(errorOneDayLimit)) {
                        validResultList1.add(errorOneDayLimit);
                    }
                    //考勤工时限制
                    String errorIhrLimit = workingHourLimitCheckMap.get(buildWorkingHourLimitKey(detailExcelVo.getUserId() + "", DateUtil.format(detailExcelVo.getAttendanceDate(), "yyyy/MM/dd"), "ihrLimit"));
                    if (StringUtils.isNotBlank(errorIhrLimit)) {
                        validResultList1.add(errorIhrLimit);
                    }
                }

                //考勤工时
                detailExcelVo.setIhrAttendHours(ihrHourMap.get(buildWorkingHourGroupKey(detailExcelVo.getAttendanceDate(), detailExcelVo.getUserId())));
                detailExcelVo.setFailureReason(Joiner.on("；").join(validResultList1));

                // 校验结果不为空，
                if (!validResultList1.isEmpty()) {
                    result = false;
                }
            }

        }

        return result;
    }

    /**
     * 唯一性校验：用户+项目+角色+日期+WBS
     *
     * @param detail
     * @param importId
     * @param validResultList
     * @return
     */
    private List<String> workingHourUniqueCheck(TicketWorkingHourImportDetail detail, Long importId, List<String> validResultList) {
        int count = workingHourExtMapper.selectProjectMemberInternalDistinctWbs(detail.getUserId(), detail.getProjectId(), detail.getLaborWbsCostId(), DateUtils.format(detail.getAttendanceDate(), DateUtils.FORMAT_SHORT), detail.getWbsSummaryCode(), importId);
        if (count > 0) {
            validResultList.add(String.format("项目：%s，WBS：%s+角色：%s已存在工时", detail.getProjectCode(), detail.getWbsSummaryCode(), detail.getLaborWbsCostName()));
        }
        return validResultList;
    }

    /**
     * 项目工时导入组织参数校验
     *
     * @param userIdList
     * @param attendanceDateList
     * @param workingHourSumMap
     * @param ihrHourMapOutside
     * @return
     */
    private Map<String, String> organizationCustomDictCheck(List<Long> userIdList,
                                                            List<Date> attendanceDateList,
                                                            Map<String, BigDecimal> workingHourSumMap,
                                                            Map<String, BigDecimal> ihrHourMapOutside) {
        Long unitId = SystemContext.getUnitId();

        // 查询组织参数：工时填报小时数上限
        Set<String> maxFillWorkingHourSet = organizationCustomDictService.queryByName("工时填报小时数上限", unitId, OrgCustomDictOrgFrom.COMPANY);
        if (maxFillWorkingHourSet == null || maxFillWorkingHourSet.isEmpty()) {
            throw new BizException(100, "工时填报小时数上限未配置，请在组织参数中维护");
        }
        BigDecimal maxFillWorkingHour = BigDecimal.ZERO;
        try {
            maxFillWorkingHour = new BigDecimal(maxFillWorkingHourSet.iterator().next());
        } catch (Exception e) {
            throw new BizException(100, "组织参数-工时填报小时数上限配置错误");
        }

        // 查询组织参数：hr考勤校验
        Set<String> workingHourIhrLimitSet = organizationCustomDictService.queryByName("hr考勤校验", unitId, OrgCustomDictOrgFrom.COMPANY);

        Map<String, BigDecimal> ihrHourMap = new HashMap<>();
        Map<String, BigDecimal> workingHourExistMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIdList) && CollectionUtils.isNotEmpty(attendanceDateList)) {
            //批量查询考勤工时
            IhrAttendDetailExample ihrExample = new IhrAttendDetailExample();
            ihrExample.createCriteria().andUserIdIn(userIdList).andAttendDateIn(attendanceDateList).andDeleteFlagEqualTo(DeletedFlag.VALID.code());
            ihrHourMap = ihrAttendDetailMapper.selectByExample(ihrExample).stream().collect(Collectors.toMap(e -> buildWorkingHourGroupKey(e.getAttendDate(), e.getUserId()), e -> e.getActualHours(), (a, b) -> a));
            if (ihrHourMapOutside != null && !ihrHourMap.isEmpty()) {
                ihrHourMapOutside.putAll(ihrHourMap);
            }

            //批量查询已填报工时
            WorkingHourExample workingHourExample = new WorkingHourExample();
            workingHourExample.createCriteria().andUserIdIn(userIdList)
                    .andApplyDateIn(attendanceDateList)
                    .andStatusNotIn(Arrays.asList(WorkingHourStatus.DRAFT.getCode(), WorkingHourStatus.REJECT.getCode())) //排除草稿&驳回
                    .andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code());
            List<WorkingHour> workingHourList = workingHourMapper.selectByExample(workingHourExample);
            for (WorkingHour workingHour : workingHourList) {
                BigDecimal applyWorkingHours = workingHour.getApplyWorkingHours();
                String key = buildWorkingHourGroupKey(workingHour.getApplyDate(), workingHour.getUserId());
                workingHourExistMap.put(key, applyWorkingHours.add(Optional.ofNullable(workingHourExistMap.get(key)).orElse(BigDecimal.ZERO)));
            }
        }

        //组织参数校验
        Map<String, String> workingHourLimitCheckMap = new HashMap<>();
        if (!workingHourSumMap.isEmpty()) {
            for (String key : workingHourSumMap.keySet()) {
                String attendanceDateStr = null;
                String userIdStr = null;
                String[] split = key.split("_");
                if (split.length == 3) {
                    attendanceDateStr = split[1];
                    userIdStr = split[2];
                }

                //日期+用户维度填报的工时数：导入的工时+已填报工时
                BigDecimal workingHourSum = BigDecimalUtils.add(workingHourSumMap.get(key), workingHourExistMap.get(key));
                if (workingHourSum != null && maxFillWorkingHour != null && workingHourSum.compareTo(maxFillWorkingHour) > 0) {
                    workingHourLimitCheckMap.put(buildWorkingHourLimitKey(userIdStr, attendanceDateStr, "oneDayLimit"), String.format("%s填报的工时数合计：%s小时，超过了一天的最大可填报工时数%s小时", attendanceDateStr, workingHourSum, maxFillWorkingHour));
                }

                if (CollectionUtils.isNotEmpty(workingHourIhrLimitSet) && workingHourIhrLimitSet.contains("1")) {
                    //日期+用户维度的考勤工时数
                    BigDecimal ihrHour = ihrHourMap.getOrDefault(key, BigDecimal.ZERO);
                    if (workingHourSum != null && ihrHour != null && workingHourSum.compareTo(ihrHour) > 0) {
                        workingHourLimitCheckMap.put(buildWorkingHourLimitKey(userIdStr, attendanceDateStr, "ihrLimit"), String.format("%s填报的工时数合计：%s小时，超过了当天的考勤工时，当天考勤工时为：%s小时", attendanceDateStr, workingHourSum, ihrHour));
                    }
                }
            }
        }

        return workingHourLimitCheckMap;
    }

    private String buildWbsGroupKey(String orgName, Long templateId, Long ruleId) {
        return String.format("buildWbsGroupKey_%s_%s_%s", orgName, templateId, ruleId);
    }

    private String buildWorkingHourGroupKey(Date attendanceDate, Long userId) {
        return String.format("buildWorkingHourGroupKey_%s_%s", DateUtil.format(attendanceDate, "yyyy/MM/dd"), userId);
    }

    private String buildCheckMergeKey(WbsWorkingHourImportDetailExcelVo excelVo) {
        return String.format("buildCheckMergeKey_%s_%s_%s_%s_%s", excelVo.getUserId(), excelVo.getProjectCode(), excelVo.getAttendanceDate(), excelVo.getLaborWbsCostId(), excelVo.getWbsSummaryCode());
    }

    private String buildWorkingHourLimitKey(String userIdStr, String attendanceDateStr, String prefix) {
        return String.format("buildWorkingHourGroupKey_%s_%s_%s", userIdStr, attendanceDateStr, prefix);
    }

    private String buildBudgetNotExistKey(Long projectId) {
        return String.format("buildBudgetNotExistKey_%s", projectId);
    }

    private String buildBudgetNotEnoughKey(Long projectId, String wbsSummaryCode, String projectActivityCode) {
        return String.format("buildBudgetNotEnoughKey_%s_%s_%s", projectId, wbsSummaryCode, projectActivityCode);
    }

    private String buildCnbEmpBasicDataKey(Long userId, String statisticDate) {
        return String.format("buildCnbEmpBasicDataKey_%s_%s", userId, statisticDate);
    }

    private TicketWorkingHourImportDTO packageAndSummaryDetail(List<TicketWorkingHourImportDetailDTO> detailDTOList) {
        // 封装记录层数据（首层）
        TicketWorkingHourImportDTO importDTO = new TicketWorkingHourImportDTO();
        importDTO.setUnitId(SystemContext.getUnitId());
        importDTO.setImportCode(generateImportCode(SystemContext.getUnitId()));

        List<TicketWorkingHourImportSummaryDTO> summaryDTOList = new ArrayList<>();

        Map<String, Long> projectCodeIdMap = detailDTOList.stream().collect(Collectors.toMap(TicketWorkingHourImportDetail::getProjectCode, TicketWorkingHourImportDetail::getProjectId, (key1, key2) -> key2));
        for (Map.Entry<String, Long> projectCodeId : projectCodeIdMap.entrySet()) {
            TicketWorkingHourImportSummaryDTO summaryDTO = new TicketWorkingHourImportSummaryDTO();
            summaryDTO.setProjectId(projectCodeId.getValue());
            summaryDTO.setProjectCode(projectCodeId.getKey());

            // 封装项目信息
            Project project = projectService.selectByPrimaryKey(projectCodeId.getValue());
            if (project != null) {
                summaryDTO.setProjectName(project.getName());
                summaryDTO.setProjectStatus(project.getStatus());
                summaryDTO.setManagerId(project.getManagerId());
                summaryDTO.setManagerName(project.getManagerName());
                if (summaryDTO.getManagerId() != null) {
                    UserInfo manager = CacheDataUtils.findUserById(summaryDTO.getManagerId());
                    if (manager != null) {
                        summaryDTO.setManagerMip(manager.getUsername()); //项目经理mip
                    }
                }
            }

            // 工时汇总及日期范围提取
            List<TicketWorkingHourImportDetailDTO> sameProjectList = detailDTOList.stream().filter(o -> projectCodeId.getKey().equals(o.getProjectCode())).collect(Collectors.toList());
            BigDecimal internalHour = detailDTOList.stream().filter(o -> projectCodeId.getKey().equals(o.getProjectCode()) && (UserType.INTERNAL.msg().equals(o.getEmployeeType()) || UserType.RECRUIT.msg().equals(o.getEmployeeType()))).map(TicketWorkingHourImportDetail::getWorkingHour).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
            BigDecimal externalHour = detailDTOList.stream().filter(o -> projectCodeId.getKey().equals(o.getProjectCode()) && (UserType.HRO.msg().equals(o.getEmployeeType()) || "外部".equals(o.getEmployeeType()))).map(TicketWorkingHourImportDetail::getWorkingHour).reduce(BigDecimal.ZERO, BigDecimalUtils::add); //TODO 外部非WBS已更名为人力点工，WBS暂不修改，作兼容
            Date startDate = detailDTOList.stream().filter(o -> projectCodeId.getKey().equals(o.getProjectCode())).map(TicketWorkingHourImportDetail::getAttendanceDate).min(Comparator.naturalOrder()).get();
            Date endDate = detailDTOList.stream().filter(o -> projectCodeId.getKey().equals(o.getProjectCode())).map(TicketWorkingHourImportDetail::getAttendanceDate).max(Comparator.naturalOrder()).get();

            summaryDTO.setImportDetailDTOlist(sameProjectList);
            summaryDTO.setInternalHour(internalHour);
            summaryDTO.setExternalHour(externalHour);
            summaryDTO.setTotalHour(internalHour.add(externalHour));
            summaryDTO.setAttendanceDateRange(DateUtils.format(startDate, DateUtils.FORMAT_SHORT) + " ~ " + DateUtils.format(endDate, DateUtils.FORMAT_SHORT));

            summaryDTOList.add(summaryDTO);
        }

        BigDecimal totalHour = summaryDTOList.stream().map(TicketWorkingHourImportSummary::getTotalHour).reduce(BigDecimal.ZERO, BigDecimal::add);

        importDTO.setTotalHour(totalHour);
        importDTO.setSummaryDTOList(summaryDTOList);
        return importDTO;
    }

    private TicketWorkingHourImportExample buildTicketWorkingHourImportExample(TicketWorkingHourImportQuery query) {
        TicketWorkingHourImportExample example = new TicketWorkingHourImportExample();
        example.setOrderByClause("create_at desc");
        TicketWorkingHourImportExample.Criteria criteria = example.createCriteria();
        // 根据当前使用单位过滤数据
        criteria.andDeletedFlagEqualTo(Boolean.FALSE).andUnitIdEqualTo(SystemContext.getUnitId());
        if (StringUtils.isNotBlank(query.getImportCode())) {
            criteria.andImportCodeLike("%" + query.getImportCode() + "%");
        }
        if (StringUtils.isNotBlank(query.getStatusStr())) {
            String[] split = query.getStatusStr().split(",");
            try {
                List<Integer> statusList = Stream.of(split).map(Integer::parseInt).collect(Collectors.toList());
                criteria.andStatusIn(statusList);
            } catch (Exception e) {
                throw new BizException(100, "审批状态参数错误");
            }
        }
        if (StringUtils.isNotBlank(query.getImportTypeStr())) {
            String[] split = query.getImportTypeStr().split(",");
            try {
                List<Integer> importTypeList = Stream.of(split).map(Integer::parseInt).collect(Collectors.toList());
                criteria.andImportTypeIn(importTypeList);
            } catch (Exception e) {
                throw new BizException(100, "类型参数错误");
            }
        }
        if (query.getStartDate() != null) {
            criteria.andCreateAtGreaterThanOrEqualTo(query.getStartDate());
        }
        if (query.getEndDate() != null) {
            criteria.andCreateAtLessThanOrEqualTo(query.getEndDate());
        }
        if (StringUtils.isNotBlank(query.getCreateName())) {
            criteria.andCreateNameLike("%" + query.getCreateName() + "%");
        }
        return example;
    }

    /**
     * 生成工时导入单号
     *
     * @param unitId 使用单位ID
     * @return 项目工时任务单号
     */
    private String generateImportCode(Long unitId) {
        StringBuffer importCode = new StringBuffer();
        String seqPerfix = basedataExtService.getUnitSeqPerfix(unitId);
        importCode.append(seqPerfix);
        importCode.append("GS");
        importCode.append(CacheDataUtils.generateSequence(3, seqPerfix + "GS", DateUtil.DATE_YYMMDD_PATTERN));
        return importCode.toString();
    }

    /**
     * 截取组织机构上两层(集团-公司)，取自工时填报的逻辑
     */
    private String cutOrgNamePath(UserInfo userInfo) {
        if (userInfo == null || userInfo.getOrginization() == null) {
            return null;
        }
        String orgNamePath = userInfo.getOrginization().getNamePath();
        if (Objects.nonNull(orgNamePath)) {

            if (orgNamePath.contains("/")) {
                orgNamePath = orgNamePath.substring(orgNamePath.indexOf("/") + 1);
            }

            if (orgNamePath.contains("/")) {
                orgNamePath = orgNamePath.substring(orgNamePath.indexOf("/") + 1);
            }

        }
        return orgNamePath;
    }

    private void checkLaborCost(Long userId) {
        UserInfo user = CacheDataUtils.findUserById(userId);
        Guard.notNull(user, "当前用户不存在");
        Long laborCostTypeSetId = user.getLaborCostTypeSetId();
        Guard.notNull(laborCostTypeSetId, user.getName() + "(" + user.getUsername() + ")所属部门的人力费率类型ID未维护，请联系项目财务处理");
        OrgLaborCostTypeSet orgLaborCostTypeSet = orgLaborCostTypeSetHelper.getByOrgLaborCostTypeSetById(laborCostTypeSetId);
        Guard.isTrue(orgLaborCostTypeSet != null && !Objects.equals(orgLaborCostTypeSet.getDeletedFlag(), Boolean.TRUE), user.getName() + "(" + user.getUsername() + ")所属部门的人力费率类型未维护，请联系项目财务处理");
    }

    /**
     * 封装职级、人天单价
     *
     * @param workingHour 工时信息
     */
    private void packageLevelAndFee(WorkingHour workingHour) {
        //获取项目信息
        Project project = projectService.selectByPrimaryKey(workingHour.getProjectId());
        if (project != null && StringUtils.isNotBlank(project.getPriceType())) {
            //项目单价类型1:内部;2:外部;3:研发
            String url = ModelsEnum.BASEDATA.getBaseUrl() + "employeeInfo/queryLevelAndFee/" + workingHour.getUserId() + "/" + project.getPriceType() + "/" + workingHour.getBizUnitId();
            if (workingHour.getLaborWbsCostId() != null) {
                url += "?laborWbsCostId=" + workingHour.getLaborWbsCostId();
            }
            String res = restTemplate.getForObject(url, String.class);
            logger.info("queryLevelAndFee:{}", res);

            JSONObject json = JSON.parseObject(res);
            if (json.containsKey("levelName")) {
                workingHour.setLevel(json.getString("levelName"));
            }
            if (json.containsKey("fee")) {
                workingHour.setCostMoney(json.getBigDecimal("fee"));
            }
            if (json.containsKey("levelType")) {
                workingHour.setLaborCostType(json.getString("levelType"));
            }
        }
    }

    @Override
    public ResponseMap getTicketWorkingHourImportApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> contractMapList = new ArrayList<>();
        TicketWorkingHourImportDTO summaryInfo = this.getSummaryInfo(id);
        if (ObjectUtils.isEmpty(summaryInfo)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("头信息不能为空");
        }

        if (null != summaryInfo.getAnnex() && !summaryInfo.getAnnex().equals("")) {
            headMap.put("annex", summaryInfo.getAnnex()); //用于附件
        }
        if (null != summaryInfo.getImportAnnex() && !summaryInfo.getImportAnnex().equals("")) {
            headMap.put("importAnnex", summaryInfo.getImportAnnex()); //用于导入数据文件
        }

        // 基本信息
        if (null != summaryInfo.getImportCode()) {
            headMap.put("importCode", summaryInfo.getImportCode()); // 单号
        }

        if (null != summaryInfo.getImportType()) {
            WorkingHourImportType workingHourImportType = WorkingHourImportType.getEnumByCode(summaryInfo.getImportType());
            String importType = "";
            switch (workingHourImportType) {
                case TICKET:
                    importType = "工单工时";
                    break;
                case WBS:
                    importType = "WBS工时";
                    break;
                default:
            }
            headMap.put("importType", importType); // 类型
        }

        if (null != summaryInfo.getCreateAt()) {
            headMap.put("createAt", DateUtils.format(summaryInfo.getCreateAt(), "yyyy-MM-dd")); //创建日期
        }

        if (null != summaryInfo.getCreateBy()) {
            final UserInfo userInfo = CacheDataUtils.findUserById(summaryInfo.getCreateBy());
            if (null != userInfo) {
                headMap.put("createBy", userInfo.getName()); //创建人姓名
            }
        }

        if (null != summaryInfo.getTotalHour()) {
            headMap.put("totalHour", summaryInfo.getTotalHour().toString()); // 导入总工时
        }

        //导入明细
        List<TicketWorkingHourImportSummaryDTO> summaryDTOList = summaryInfo.getSummaryDTOList();
        if (CollectionUtils.isNotEmpty(summaryDTOList)) {
            for (TicketWorkingHourImportSummaryDTO summaryDTO : summaryDTOList) {
                Map<String, String> map = new HashMap<>();
                if (null != summaryDTO.getProjectId()) {
                    Project project = projectService.selectByPrimaryKey(summaryDTO.getProjectId());
                    if (null != project) {
                        map.put("projectName", project.getName()); //项目
                        map.put("managerName", project.getManagerName()); //项目经理
                    }
                }
                if (null != summaryDTO.getInternalHour()) {
                    map.put("internalHour", summaryDTO.getInternalHour().toString()); //内部工时
                }
                if (null != summaryDTO.getExternalHour()) {
                    map.put("externalHour", summaryDTO.getExternalHour().toString()); //外部工时
                }
                contractMapList.add(map);
            }
        }


        responseMap.setHeadMap(headMap);
        responseMap.setList1(contractMapList);

        responseMap.setStatus("success");
        responseMap.setMsg("success");
        return responseMap;
    }

    @Override
    public Map<String, Object> exportList(TicketWorkingHourImportQuery ticketWorkingHourImportQuery) {
        Map<String, Object> resultMap = new HashMap<>();
        //单据
        TicketWorkingHourImportExample example = buildTicketWorkingHourImportExample(ticketWorkingHourImportQuery);
        List<ProjectWorkingHourImportExcelVo> excelVos = BeanConverter.copy(ticketWorkingHourImportMapper.selectByExample(example), ProjectWorkingHourImportExcelVo.class);
        if (CollectionUtils.isEmpty(excelVos)) {
            resultMap.put("excelVoList", new ArrayList<>());
            resultMap.put("detailExcelVo", new ArrayList<>());
            return resultMap;
        }
        for (int i = 0; i < excelVos.size(); i++) {
            excelVos.get(i).setIndex(i + 1);
        }
        Map<Long, ProjectWorkingHourImportExcelVo> excelVoMap = excelVos.stream().collect(Collectors.toMap(e -> e.getId(), e -> e, (a, b) -> a));

        //导入明细
        TicketWorkingHourImportDetailExample detailExample = new TicketWorkingHourImportDetailExample();
        detailExample.createCriteria().andImportIdIn(new ArrayList<>(excelVoMap.keySet())).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<ProjectWorkingHourImportDetailExcelVo> detailExcelVos = BeanConverter.copy(ticketWorkingHourImportDetailMapper.selectByExample(detailExample), ProjectWorkingHourImportDetailExcelVo.class);

        //查询项目信息
        List<Long> projectIdList = detailExcelVos.stream().map(e -> e.getProjectId()).collect(Collectors.toList());
        Map<Long, String> projectMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(projectIdList)) {
            ProjectExample projectExample = new ProjectExample();
            projectExample.createCriteria().andIdIn(projectIdList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            projectMap = projectMapper.selectByExample(projectExample).stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getName(), (a, b) -> a));
        }

        for (int i = 0; i < detailExcelVos.size(); i++) {
            ProjectWorkingHourImportDetailExcelVo detailExcelVo = detailExcelVos.get(i);
            ProjectWorkingHourImportExcelVo excelVo = excelVoMap.get(detailExcelVo.getImportId());
            if (excelVo != null) {
                detailExcelVo.setNum(i + 1);
                detailExcelVo.setImportCode(excelVo.getImportCode());
                detailExcelVo.setImportType(excelVo.getImportType());
                detailExcelVo.setStatus(excelVo.getStatus());
            }
            detailExcelVo.setProjectName(projectMap.get(detailExcelVo.getProjectId()));
        }
        resultMap.put("excelVos", excelVos);
        resultMap.put("detailExcelVos", detailExcelVos);
        return resultMap;
    }
}
