package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageInfo;
import com.midea.mip.core.util.StringUtil;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanSubmitRecordDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.entity.MaterialAdjustHeader;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecordExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.MaterialAdjustEnum;
import com.midea.pam.common.enums.WorkFlowDraftSubmitTemporaryRecordStatus;
import com.midea.pam.common.enums.WorkflowOperationType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceEvent;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.gateway.entity.RocketMQMsgParameter;
import com.midea.pam.common.gateway.entity.WorkFlowDraftSubmitTemporaryRecord;
import com.midea.pam.common.gateway.entity.WorkFlowDraftSubmitTemporaryRecordExample;
import com.midea.pam.common.rocketmq.dto.MsgSendDto;
import com.midea.pam.common.rocketmq.dto.SubmitHistoryWithRecursiveDetailDto;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanSubmitRecordMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.RocketMQMsgParameterExtMapper;
import com.midea.pam.ctc.mapper.WorkFlowDraftSubmitTemporaryRecordExtMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.rocketmq.service.CommonRocketMQProducerService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.MaterialAdjustService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanSubmitRecordService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.event.DesignPlanDetailSubmitApprovalEvent;
import com.midea.pam.ctc.service.event.UpdateMaterialCostRequirementEvent;
import com.midea.pam.ctc.service.helper.MilepostDesignPlanHelper;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;

public class MilepostDesignPlanSubmitRecordServiceImpl implements MilepostDesignPlanSubmitRecordService {

    private static final Logger logger = LoggerFactory.getLogger(MilepostDesignPlanSubmitRecordServiceImpl.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    private static final Long MAX_WAIT_TIME_SYNC = (long) 1000 * 60 * 10;//10分钟

    @Value("${rocketmq.ctc.flexible-mdp-submit.topic}")
    private String ctcFlexibleMDPSubmitTopic;

    @Resource
    private MilepostDesignPlanSubmitRecordMapper milepostDesignPlanSubmitRecordMapper;

    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectExtMapper projectExtMapper;

    @Resource
    private ProjectProfitService projectProfitService;

    @Resource
    private ProjectMilepostService projectMilepostService;

    @Resource
    private PurchaseMaterialRequirementService purchaseMaterialRequirementService;

    @Resource
    private CtcAttachmentService ctcAttachmentService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private MaterialExtService materialExtService;

    @Resource
    private MilepostDesignPlanHelper milepostDesignPlanHelper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MaterialAdjustService materialAdjustService;

    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;

    @Qualifier("saveSubmitRecordWithRecursiveDetailExecutor")
    @Resource
    private ThreadPoolTaskExecutor saveSubmitRecordWithRecursiveDetailExecutor;

    @Resource
    private WorkFlowDraftSubmitTemporaryRecordExtMapper workFlowDraftSubmitTemporaryRecordExtMapper;

    @Resource
    private WorkflowCallbackService workflowCallbackService;

    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;

    @Resource
    private CommonRocketMQProducerService commonRocketMQProducerService;

    @Resource
    private RocketMQMsgParameterExtMapper rocketMQMsgParameterExtMapper;

    @Override
    public MilepostDesignPlanSubmitRecordDto add(MilepostDesignPlanSubmitRecordDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        MilepostDesignPlanSubmitRecord entity = BeanConverter.copy(dto, MilepostDesignPlanSubmitRecord.class);
        milepostDesignPlanSubmitRecordMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public MilepostDesignPlanSubmitRecordDto update(MilepostDesignPlanSubmitRecordDto dto) {
        Asserts.notEmpty(dto.getId(), ErrorCode.ID_NOT_NULL);
        MilepostDesignPlanSubmitRecord entity = BeanConverter.copy(dto, MilepostDesignPlanSubmitRecord.class);
        milepostDesignPlanSubmitRecordMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public MilepostDesignPlanSubmitRecordDto save(MilepostDesignPlanSubmitRecordDto dto, Long userBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
            return this.add(dto);
        } else {
            dto.setUpdateBy(userBy);
            return this.update(dto);
        }
    }

    @Override
    public MilepostDesignPlanSubmitRecordDto getById(Long id) {
        if (id == null) {
            return null;
        }
        MilepostDesignPlanSubmitRecord entity = milepostDesignPlanSubmitRecordMapper.selectByPrimaryKey(id);
        MilepostDesignPlanSubmitRecordDto dto = BeanConverter.copy(entity, MilepostDesignPlanSubmitRecordDto.class);
        return dto;
    }

    @Override
    public List<MilepostDesignPlanSubmitRecordDto> selectList(MilepostDesignPlanSubmitRecordDto query) {
        List<MilepostDesignPlanSubmitRecord> list = milepostDesignPlanSubmitRecordMapper.selectByExampleWithBLOBs(buildCondition(query));
        List<MilepostDesignPlanSubmitRecordDto> dtos = BeanConverter.copy(list, MilepostDesignPlanSubmitRecordDto.class);
        for (MilepostDesignPlanSubmitRecordDto dto : dtos) {
            packageDto(dto);
        }
        return dtos;
    }

    @Override
    public PageInfo<MilepostDesignPlanSubmitRecordDto> selectPage(MilepostDesignPlanSubmitRecordDto Query) {
        return null;
    }

    private MilepostDesignPlanSubmitRecordExample buildCondition(MilepostDesignPlanSubmitRecordDto query) {
        MilepostDesignPlanSubmitRecordExample example = new MilepostDesignPlanSubmitRecordExample();
        MilepostDesignPlanSubmitRecordExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        example.setOrderByClause("create_at desc");

        if (query.getId() != null) {
            criteria.andIdEqualTo(query.getId());
        }
        if (query.getProjectId() != null) {
            criteria.andProjectIdEqualTo(query.getProjectId());
        }
        if (query.getMilepostId() != null) {
            criteria.andMilepostIdEqualTo(query.getMilepostId());
        }
        if (query.getProjectSubmit() != null) {
            criteria.andProjectSubmitEqualTo(query.getProjectSubmit());
        }
        if (query.getStatus() != null) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (query.getStatusIsNull() != null) {
            if (query.getStatusIsNull()) {
                criteria.andStatusIsNull();
            } else {
                criteria.andStatusIsNotNull();
            }
        }
        if (query.getUploadPathIdIsNull() != null) {
            if (query.getUploadPathIdIsNull()) {
                criteria.andUploadPathIdIsNull();
            } else {
                criteria.andUploadPathIdIsNotNull();
            }
        }
//        if (StringUtils.isNotEmpty(query.getBatchUploadPathIds())) {
//            criteria.andBatchUploadPathIdsEqualTo(query.getBatchUploadPathIds());
//        }

        return example;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MilepostDesignPlanSubmitRecordDto saveSubmitRecordWithRecursiveDetail(MilepostDesignPlanSubmitRecordDto dto, Long userBy) {
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = dto.getDesignPlanDetailDtos();
        Project project = projectService.selectByPrimaryKey(dto.getProjectId());
        //Long storageId = ErpOrganizationId.ROBOT.code();
        Long storageId = 0L;
        //查询库存组织
        if (project != null) {
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null && projectProfit.getStorageId() != null) {
                storageId = projectProfit.getStorageId();
            }
        }

        MilepostDesignPlanSubmitRecordDto resultDto = this.save(dto, userBy);
        if (CollectionUtils.isEmpty(designPlanDetailDtos)) {
            return resultDto;
        }

        Long uploadPathId = dto.getUploadPathId();
        Long finalStorageId = storageId;
        int threadNum = designPlanDetailDtos.size() / 50 + (designPlanDetailDtos.size() % 50 == 0 ? 0 : 1);
        final CountDownLatch countDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * 50;
            int endIndex = i == threadNum ? designPlanDetailDtos.size() : i * 50;
            List<MilepostDesignPlanDetailDto> eachDetailDtos = designPlanDetailDtos.subList(startIndex, endIndex);
            saveSubmitRecordWithRecursiveDetailExecutor.execute(() -> {
                try {
                    // 多线程异步执行主干导入方法
                    executeEachDetailDtos(eachDetailDtos, project, resultDto, userBy, finalStorageId, dto, uploadPathId);
                } catch (Exception e) {
                    logger.error("saveSubmitRecordWithRecursiveDetail操作记录失败", e);
                } finally {
                    countDownLatch.countDown();
                    if (Objects.equals(countDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after saveSubmitRecordWithRecursiveDetail", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("saveSubmitRecordWithRecursiveDetail的await失败", ex);
            Thread.currentThread().interrupt();
        }

        return resultDto;
    }

    private void executeEachDetailDtos(List<MilepostDesignPlanDetailDto> eachDetailDtos, Project project,
                                       MilepostDesignPlanSubmitRecordDto resultDto, Long userBy, Long storageId,
                                       MilepostDesignPlanSubmitRecordDto dto, Long uploadPathId) {
        for (MilepostDesignPlanDetailDto designPlanDetailDto : eachDetailDtos) {
            //补充存草稿的逻辑
            if (null == designPlanDetailDto.getId()) {
                designPlanDetailDto.setOrStorage(Boolean.TRUE);
            }
            //支持多模组导入
            Set<Long> uploadPathIdSet = CollectionUtils.isEmpty(dto.getUploadPathIdSet()) ? new HashSet<>(Collections.singleton(uploadPathId)) : dto.getUploadPathIdSet();
            milepostDesignPlanDetailService.recursiveSave(project, designPlanDetailDto, (long) -1,//开始默认一级物料父ID为-1
                    resultDto.getId(), resultDto.getMilepostId(), userBy, 1, //开始默认层级为一级
                    storageId, dto.getOrStorage(), uploadPathIdSet);
        }
    }

    @Override
    public MilepostDesignPlanSubmitRecordDto getSubmitRecordWithDetailById(Long id, Boolean deletedFlag) {
        MilepostDesignPlanSubmitRecordDto submitRecordDto = this.getById(id);
        packageDto(submitRecordDto);

        //获取该提交记录对应所有上传设计信息(模组下设计信息)
        MilepostDesignPlanDetailDto milepostDesignPlanDetailQuery = new MilepostDesignPlanDetailDto();
        milepostDesignPlanDetailQuery.setSubmitRecordId(id);
        milepostDesignPlanDetailQuery.setDeletedFlag(deletedFlag);
        List<MilepostDesignPlanDetailDto> designPlanDetailDtoList = milepostDesignPlanDetailService.selectList(milepostDesignPlanDetailQuery);
        submitRecordDto.setDesignPlanDetailDtos(designPlanDetailDtoList);

        return submitRecordDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBatchStatusForSubmitWithDetail(MilepostDesignPlanSubmitRecordDto dto, Long userBy) {
        logger.info("非柔性详设发布的审批通过updateBatchStatusForSubmitWithDetail的MilepostDesignPlanSubmitRecordDto：{}，userBy：{}",
                JsonUtils.toString(dto), userBy);
        MilepostDesignPlanSubmitRecordDto recordDto = this.getSubmitRecordWithDetailById(dto.getId(), Boolean.FALSE);
        Long organizationId = projectService.getOrganizationIdByProjectId(recordDto.getProjectId());
        try {
            BeanConverter.copy(dto, recordDto);
            this.save(recordDto, userBy);

            List<String> updateMaterialCostRequirementPamCodeList = new ArrayList<>();
            List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
            List<MilepostDesignPlanDetail> insertDesignPlanDetailList = new ArrayList<>();
            for (MilepostDesignPlanDetailDto designPlanDetailDto : recordDto.getDesignPlanDetailDtos()) {
                updateMaterialCostRequirementPamCodeList.add(designPlanDetailDto.getPamCode());

                designPlanDetailDto.setStatus(dto.getStatus());
                if (designPlanDetailDto.getId() == null) {
                    insertDesignPlanDetailList.add(designPlanDetailDto);
                } else {
                    updateDesignPlanDetailList.add(designPlanDetailDto);
                }
            }
            if (ListUtils.isNotEmpty(updateDesignPlanDetailList)) {
                milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
            }
            if (ListUtils.isNotEmpty(insertDesignPlanDetailList)) {
                milepostDesignPlanDetailExtMapper.batchInsert(insertDesignPlanDetailList);
            }

            //TODO 同步更新附件状态
            this.synCtcAttachmentStatusPass(recordDto);
            if (recordDto.getPurchase() && CheckStatus.PASS.code().equals(dto.getStatus())) {
                Map<Long, String> materialIdWithNewErpCode = new HashMap<Long, String>();
                milepostDesignPlanDetailService.handleRecursive(recordDto.getUploadPathId(), dto.getId(), materialIdWithNewErpCode);

                String lockName = String.format("DesignPlanDetailChangeApprovalListener.handleMdpChangePassed_%s", recordDto.getProjectId());
                String value = String.valueOf(recordDto.getProjectId());
                try {
                    if (DistributedCASLock.lock(lockName, value, MAX_WAIT_TIME_SYNC, MAX_WAIT_TIME_SYNC * 2)) {
                        List<Long> requirementIdList = new ArrayList<>();
                        purchaseMaterialRequirementService.recursiveGenerateRequirementByDetailPlanId(recordDto.getUploadPathId(), dto.getId(),
                                requirementIdList);
                        if (ListUtils.isNotEmpty(requirementIdList)) {
                            purchaseMaterialRequirementService.batchUpdateStatus(requirementIdList);
                        }
                    }
                } catch (Exception e) {
                    logger.error("recursiveGenerateRequirementByDetailPlanId生成采购需求加锁失败", e);
                    throw e;
                } finally {
                    DistributedCASLock.unLock(lockName, value);
                }

                //批量推送需要更新erp编码的物料
                materialExtService.saveBatchByIdWithNewErpCode(materialIdWithNewErpCode, userBy);
            }

            if (CheckStatus.PASS.code().equals(dto.getStatus())) {
                // 批量处理驳回/撤回后删除的物料
                milepostDesignPlanDetailService.deleteMaterialCost(recordDto.getUploadPathId(), dto.getId(), organizationId);
            }

            //批量更新物料估价的采购需求量
            applicationEventPublisher.publishEvent(new UpdateMaterialCostRequirementEvent(this, updateMaterialCostRequirementPamCodeList,
                    recordDto.getMilepostId(), organizationId));

            // 详细设计发布审批通过后，往物料新增添和变更行表插入记录
            if (CheckStatus.PASS.code().equals(dto.getStatus())) {
                MaterialAdjustHeader materialAdjustHeader = new MaterialAdjustHeader();
                materialAdjustHeader.setApplyBy(recordDto.getCreateBy());
                UserInfo userInfo = CacheDataUtils.findUserById(recordDto.getCreateBy());
                if (userInfo != null) {
                    materialAdjustHeader.setApplyByName(userInfo.getName());
                }
                materialAdjustHeader.setApplyTime(recordDto.getCreateAt());
                materialAdjustHeader.setCreateBy(recordDto.getCreateBy());
                materialAdjustHeader.setCreateAt(new Date());
                materialAdjustHeader.setUpdateBy(recordDto.getCreateBy());
                materialAdjustHeader.setUpdateAt(new Date());
                // 生成单据号
                Long unitId = projectExtMapper.getUnitIdByProjectId(recordDto.getProjectId());
                materialAdjustHeader.setAdjustCode(materialAdjustService.generateMaterialAdjustCode(unitId));
                materialAdjustHeader.setDeletedFlag(Boolean.FALSE);
                materialAdjustHeader.setStatus(MaterialAdjustEnum.APPROVED.code());
                // 封装库存组织id
                materialAdjustHeader.setOrganizationId(organizationId);
                // 封装业务实体id
                Long ouId = projectService.selectByPrimaryKey(recordDto.getProjectId()).getOuId();
                materialAdjustHeader.setOuId(ouId);
                materialAdjustHeader.setAdjustType(MaterialAdjustEnum.MATERIAL_ADJUST_ADDED.code());
                materialAdjustHeader.setResource(1);
                materialAdjustHeader.setSyncStatus(MaterialAdjustEnum.SYNC_STATUS_SUCCESS.code());
                materialAdjustHeader.setSyncMes("");
                materialAdjustHeader.setProjectId(recordDto.getProjectId());
                materialAdjustHeader.setMilepostId(recordDto.getMilepostId());
                materialAdjustService.insertHeader(materialAdjustHeader);
            }
        } catch (Exception e) {
            logger.error("更新详细方案提交记录异常", e);
            throw e;
        }

        return true;
    }

    /**
     * 同步更新附件状态
     */
    private void synCtcAttachmentStatusPass(MilepostDesignPlanSubmitRecordDto recordDto) {
        if (CheckStatus.PASS.code().equals(recordDto.getStatus())) {
            CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
            attachmentQuery.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code());
            attachmentQuery.setModuleId(recordDto.getId());
            attachmentQuery.setStatus(CheckStatus.CHECKING.code());
            List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
            if (!CollectionUtils.isEmpty(attachmentDtos)) {
                for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                    attachmentDto.setStatus(CheckStatus.PASS.code());
                    ctcAttachmentService.save(attachmentDto, -1L);
                }
            }
        }
    }

    @Override
    public void updateBatchStatusForSubmitWithDetailAsync(MilepostDesignPlanSubmitRecordDto dto, Long userBy) {
        applicationEventPublisher.publishEvent(new DesignPlanDetailSubmitApprovalEvent(this, dto, userBy));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void abandon(MilepostDesignPlanSubmitRecordDto dto, Integer type) {
        Long formInstanceId = dto.getFormInstanceId();
        String fdInstanceId = dto.getFdInstanceId();
        String formUrl = dto.getFormUrl();
        String eventName = dto.getEventName();
        String handlerId = dto.getHandlerId();
        Long companyId = dto.getCompanyId();
        Long createUserId = dto.getCreateUserId();
        logger.info("详细设计方案发布审批(非采购)审批作废回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("MilepostDesignPlanSubmitRecord_abandon_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    MilepostDesignPlanSubmitRecordDto recordDto = this.getSubmitRecordWithDetailById(formInstanceId, DeletedFlag.VALID.code());
                    Long organizationId = projectService.getOrganizationIdByProjectId(recordDto.getProjectId());
                    List<MilepostDesignPlanDetailDto> designPlanDetailDtos = recordDto.getDesignPlanDetailDtos();
                    try {
                        recordDto.setStatus(CheckStatus.PURCHASE_CANCEL.code());
                        this.save(recordDto, null);
                        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                            designPlanDetailDto.setDeletedFlag(DeletedFlag.INVALID.code());
                            milepostDesignPlanDetailService.save(designPlanDetailDto, null);
                        }
                        // 删除物料估价以及对应物料
                        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                            final String pamCode = designPlanDetailDto.getPamCode();
                            milepostDesignPlanHelper.handleMaterialCost(pamCode, organizationId);
                        }
                    } catch (Exception e) {
                        logger.error("详细设计方案发布审批(非采购)审批作废回调采购废弃异常", e);
                        throw new ApplicationBizException(ExceptionUtils.getExceptionMessage(e));
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("详细设计方案发布审批(非采购)审批作废回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("详细设计方案发布审批(非采购)审批作废回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("详细设计方案发布审批(非采购)审批作废回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(MilepostDesignPlanSubmitRecordDto dto, Integer type) {
        Long formInstanceId = dto.getFormInstanceId();
        String fdInstanceId = dto.getFdInstanceId();
        String formUrl = dto.getFormUrl();
        String eventName = dto.getEventName();
        String handlerId = dto.getHandlerId();
        Long companyId = dto.getCompanyId();
        Long createUserId = dto.getCreateUserId();
        logger.info("详细设计方案发布审批(非采购)审批删除回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("MilepostDesignPlanSubmitRecord_delete_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    MilepostDesignPlanSubmitRecordDto recordDto = this.getSubmitRecordWithDetailById(formInstanceId, DeletedFlag.VALID.code());
                    Long organizationId = projectService.getOrganizationIdByProjectId(recordDto.getProjectId());
                    List<MilepostDesignPlanDetailDto> designPlanDetailDtos = recordDto.getDesignPlanDetailDtos();
                    try {
                        recordDto.setDeletedFlag(DeletedFlag.INVALID.code());
                        this.save(recordDto, null);
                        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                            designPlanDetailDto.setDeletedFlag(DeletedFlag.INVALID.code());
                            milepostDesignPlanDetailService.save(designPlanDetailDto, null);
                        }
                        // 删除物料估价以及对应物料
                        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                            final String pamCode = designPlanDetailDto.getPamCode();
                            milepostDesignPlanHelper.handleMaterialCost(pamCode, organizationId);
                        }
                    } catch (Exception e) {
                        logger.error("详细设计方案发布审批(非采购)审批删除回调采购废弃异常", e);
                        throw new ApplicationBizException(ExceptionUtils.getExceptionMessage(e));
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("详细设计方案发布审批(非采购)审批删除回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("详细设计方案发布审批(非采购)审批删除回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("详细设计方案发布审批(非采购)审批删除回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unpublish(Long id) {
        MilepostDesignPlanSubmitRecordDto recordDto = this.getSubmitRecordWithDetailById(id, DeletedFlag.VALID.code());
        Guard.notNull(recordDto, "该详设不存在，作废失败");

        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = recordDto.getDesignPlanDetailDtos();
        FormInstanceEvent formInstanceEvent = new FormInstanceEvent();
        formInstanceEvent.setFormInstanceId(id);
        if (recordDto.getPurchase()) {
            formInstanceEvent.setFormUrl("milepostDesignPlanPurchaseApp");
        } else {
            formInstanceEvent.setFormUrl("milepostDesignPlanNoPurchaseApp");
        }
        formInstanceEvent.setStatus(0);
        formInstanceEvent.setEventType(WorkflowOperationType.DRAFT_ABANDON.getCode());
        formInstanceEvent.setCompanyId(SystemContext.getUnitId());
        formInstanceEvent.setCreateAt(new Date());
        if (StringUtil.isNotNull(formInstanceEvent.getFormUrl())) {
            String jsonString = JSONObject.toJSONString(formInstanceEvent);
            stringRedisTemplate.opsForList().leftPushAll(Constants.REDIS_WF_EVENT_KEY, jsonString);
        }
        Long organizationId = projectService.getOrganizationIdByProjectId(recordDto.getProjectId());
        if (recordDto.getStatus().equals(CheckStatus.REJECT.code())) { //驳回的时候流程只能做作废
            recordDto.setStatus(CheckStatus.PURCHASE_CANCEL.code());
            this.save(recordDto, null);
            for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                designPlanDetailDto.setDeletedFlag(DeletedFlag.INVALID.code());
                milepostDesignPlanDetailService.save(designPlanDetailDto, null);
            }
            // 删除物料估价以及对应物料
            for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                final String pamCode = designPlanDetailDto.getPamCode();
                milepostDesignPlanHelper.handleMaterialCost(pamCode, organizationId);
            }
        } else if (recordDto.getStatus().equals(CheckStatus.RETURN.code())) {  //撤回的时候流程智能做删除
            recordDto.setDeletedFlag(DeletedFlag.INVALID.code());
            this.save(recordDto, null);
            for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                designPlanDetailDto.setDeletedFlag(DeletedFlag.INVALID.code());
                milepostDesignPlanDetailService.save(designPlanDetailDto, null);
            }
            // 删除物料估价以及对应物料
            for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                final String pamCode = designPlanDetailDto.getPamCode();
                milepostDesignPlanHelper.handleMaterialCost(pamCode, organizationId);
            }
        }

        return true;
    }

    @Override
    public List<CtcAttachmentDto> allAttachFromAllSubmitRecord(Long projectId) {
        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();

        MilepostDesignPlanSubmitRecordDto submitRecordQuery = new MilepostDesignPlanSubmitRecordDto();
        submitRecordQuery.setProjectId(projectId);
        submitRecordQuery.setStatusIsNull(false);
        List<MilepostDesignPlanSubmitRecordDto> submitRecordDtos = this.selectList(submitRecordQuery);
        if (ListUtil.isPresent(submitRecordDtos)) {
            List<Long> submitRecordIds = ListUtil.map(submitRecordDtos, "id");
            attachmentDtos.addAll(this.allAttachFromSubmitRecordIds(submitRecordIds));
        }

        return attachmentDtos;
    }

    @Override
    public List<CtcAttachmentDto> allAttachFromSubmitRecordIds(List<Long> submitRecordIds) {
        if (ListUtil.isBlank(submitRecordIds)) {
            return new ArrayList<>();
        }
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code());
        attachmentQuery.setModuleIds(submitRecordIds);
        return ctcAttachmentService.selectList(attachmentQuery);
    }

    @Override
    public List<Long> idsForUploadPathIdIsNullByMilepostId(Long milepostId) {
        if (milepostId == null) {
            return new ArrayList<>();
        }
        MilepostDesignPlanSubmitRecordDto query = new MilepostDesignPlanSubmitRecordDto();
        query.setMilepostId(milepostId);
        query.setUploadPathIdIsNull(true);
        List<MilepostDesignPlanSubmitRecordDto> submitRecordDtos = this.selectList(query);
        List<Long> ids = ListUtil.map(submitRecordDtos, "id");

        return ids;
    }
//
//    @Override
//    public List<Long> idsByUploadPathIds(List<Long> uploadPathIds) {
//        if (ListUtil.isBlank(uploadPathIds)) {
//            return new ArrayList<>();
//        }
//        MilepostDesignPlanSubmitRecordDto query = new MilepostDesignPlanSubmitRecordDto();
//        query.setUploadPathIds(uploadPathIds);
//        List<MilepostDesignPlanSubmitRecordDto> submitRecordDtos = this.selectList(query);
//        List<Long> ids = ListUtil.map(submitRecordDtos, "id");
//
//        return ids;
//    }

    private void packageDto(MilepostDesignPlanSubmitRecordDto dto) {
        if (dto.getPublisherBy() != null) {
            UserInfo publisher = CacheDataUtils.findUserById(dto.getPublisherBy());
            if (publisher != null) {
                dto.setPublisherName(publisher.getName());
            }
        }
        if (dto.getProjectId() != null) {
            Project project = projectService.selectByPrimaryKey(dto.getProjectId());
            if (project != null) {
                dto.setProjectName(project.getName());
            }
        }
        if (dto.getMilepostId() != null) {
            ProjectMilepostDto projectMilepostDto = projectMilepostService.getById(dto.getMilepostId());
            if (projectMilepostDto != null) {
                dto.setMilepostName(projectMilepostDto.getName());
                dto.setOrderNum(projectMilepostDto.getOrderNum());
            }
        }
    }

    @Override
    public List<CtcAttachmentDto> allApprovedSubmitRecordAttachment(Long projectId) {
        //获取审批通过的提交记录
        List<MilepostDesignPlanSubmitRecordDto> records = this.findApprovedMilepostDesignPlanSubmitRecordDto(projectId);

        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
        if (ListUtil.isPresent(records)) {
            List<Long> submitRecordIds = ListUtil.map(records, "id");
            attachmentDtos.addAll(this.allAttachFromSubmitRecordIds(submitRecordIds));
        }
        return attachmentDtos;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean abandon(Long id, Integer type) {
        MilepostDesignPlanSubmitRecordDto recordDto = this.getSubmitRecordWithDetailById(id, DeletedFlag.VALID.code());
        Long organizationId = projectService.getOrganizationIdByProjectId(recordDto.getProjectId());
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = recordDto.getDesignPlanDetailDtos();
        try {
            recordDto.setStatus(CheckStatus.PURCHASE_CANCEL.code());
            this.save(recordDto, null);
            for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                designPlanDetailDto.setDeletedFlag(DeletedFlag.INVALID.code());
                milepostDesignPlanDetailService.save(designPlanDetailDto, null);
            }
            // 删除物料估价以及对应物料
            for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                final String pamCode = designPlanDetailDto.getPamCode();
                milepostDesignPlanHelper.handleMaterialCost(pamCode, organizationId);
            }
        } catch (Exception e) {
            logger.error("采购废弃异常", e);
            throw new BizException(Code.ERROR, "采购废弃异常");
        }

        workflowCallbackService.deleteWorkFlowCallbackRecord(id);
        return true;
    }

    /**
     * 获取审批通过的提交记录
     */
    private List<MilepostDesignPlanSubmitRecordDto> findApprovedMilepostDesignPlanSubmitRecordDto(Long projectId) {
        List<Integer> values = new ArrayList<>();
        values.add(CheckStatus.PASS.code());
        values.add(CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code());
        values.add(CheckStatus.MDP_PROJECT_MANAGER_APPROVED.code());
        values.add(CheckStatus.MDP_DESIGNER_SUPERIOR_APPROVED.code());

        MilepostDesignPlanSubmitRecordExample example = new MilepostDesignPlanSubmitRecordExample();
        MilepostDesignPlanSubmitRecordExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andStatusIn(values);
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        example.setOrderByClause("create_at desc");

        List<MilepostDesignPlanSubmitRecord> list = milepostDesignPlanSubmitRecordMapper.selectByExampleWithBLOBs(example);
        List<MilepostDesignPlanSubmitRecordDto> dtos = BeanConverter.copy(list, MilepostDesignPlanSubmitRecordDto.class);
        for (MilepostDesignPlanSubmitRecordDto dto : dtos) {
            packageDto(dto);
        }
        return dtos;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectWbsReceiptsDto saveSubmitHistoryWithRecursiveDetail(ProjectWbsReceiptsDto dto, Long userBy) {
        logger.info("saveSubmitHistoryWithRecursiveDetail的dto：{}，userBy：{}", JsonUtils.toString(dto), userBy);
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = dto.getDesignPlanDetailDtos();
        StringBuilder releaseLotNumber = new StringBuilder();
        Project project = projectService.selectByPrimaryKey(dto.getProjectId());
        Long storageId = 0L;
        //查询库存组织
        if (project != null) {
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null && projectProfit.getStorageId() != null) {
                storageId = projectProfit.getStorageId();
            }
            //生成发布批次号
            releaseLotNumber.append(project.getCode() + "_");
            releaseLotNumber.append(dto.getReasonSuffix() + "_");
            String sequence = CacheDataUtils.generateSequence(Constants.DESIGN_DETAIL_PUBLISH_LENGTH, project.getCode() + dto.getReasonSuffix());
            releaseLotNumber.append(sequence.substring(sequence.length() - 5));
            dto.setDesignReleaseLotNumber(releaseLotNumber.toString());
            dto.setProjectCode(project.getCode());
            dto.setProjectName(project.getName());
        }
        ProjectWbsReceiptsDto resultDto = projectWbsReceiptsService.saveReceiptsForSubmit(dto, userBy);

        // 提交是先插入一条提交逻辑没有完成的数据
        WorkFlowDraftSubmitTemporaryRecordExample example = new WorkFlowDraftSubmitTemporaryRecordExample();
        example.createCriteria().andFormUrlEqualTo(dto.getFormTemplateId()).andFormInstanceIdEqualTo(dto.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<WorkFlowDraftSubmitTemporaryRecord> recordList = workFlowDraftSubmitTemporaryRecordExtMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(recordList)) {
            WorkFlowDraftSubmitTemporaryRecord record = new WorkFlowDraftSubmitTemporaryRecord();
            record.setFormUrl(dto.getFormTemplateId());
            record.setFormInstanceId(dto.getId());
            record.setStatus(WorkFlowDraftSubmitTemporaryRecordStatus.DOING.getIndex());
            record.setSubmitBusinessFinished(Boolean.FALSE);
            record.setDeletedFlag(Boolean.FALSE);
            logger.info("saveSubmitHistoryWithRecursiveDetail的工作流提交临时记录add的record：{}", JSONObject.toJSONString(record));
            workFlowDraftSubmitTemporaryRecordExtMapper.insertSelective(record);
        } else {
            logger.info("saveSubmitHistoryWithRecursiveDetail的表单url：{}，表单实例id：{}对应的提交记录已存在", dto.getFormTemplateId(), dto.getId());
        }

        /*applicationEventPublisher.publishEvent(new SubmitHistoryWithRecursiveDetailEvent(this, storageId, designPlanDetailDtos, project,
                releaseLotNumber, userBy, dto));*/

        SubmitHistoryWithRecursiveDetailDto submitHistoryWithRecursiveDetailDto = new SubmitHistoryWithRecursiveDetailDto();
        submitHistoryWithRecursiveDetailDto.setStorageId(storageId);
        submitHistoryWithRecursiveDetailDto.setDesignPlanDetailDtos(designPlanDetailDtos);
        submitHistoryWithRecursiveDetailDto.setProject(project);
        submitHistoryWithRecursiveDetailDto.setReleaseLotNumber(releaseLotNumber);
        submitHistoryWithRecursiveDetailDto.setUserBy(userBy);
        submitHistoryWithRecursiveDetailDto.setDto(dto);

        RocketMQMsgParameter rocketMQMsgParameter = new RocketMQMsgParameter();
        rocketMQMsgParameter.setOperationId(String.valueOf(dto.getId()));
        rocketMQMsgParameter.setTopic(ctcFlexibleMDPSubmitTopic);
        rocketMQMsgParameter.setParameter(JSONObject.toJSONString(submitHistoryWithRecursiveDetailDto).getBytes(StandardCharsets.UTF_8));
        rocketMQMsgParameter.setDeletedFlag(Boolean.FALSE);
        rocketMQMsgParameterExtMapper.insertSelective(rocketMQMsgParameter);

        MsgSendDto msgSendDto = new MsgSendDto();
        msgSendDto.setOperationId(String.valueOf(dto.getId()));
        msgSendDto.setTopic(ctcFlexibleMDPSubmitTopic);
        msgSendDto.setMessages(rocketMQMsgParameter.getId());
        commonRocketMQProducerService.sendCommonMessages(msgSendDto);

        return resultDto;
    }
}
