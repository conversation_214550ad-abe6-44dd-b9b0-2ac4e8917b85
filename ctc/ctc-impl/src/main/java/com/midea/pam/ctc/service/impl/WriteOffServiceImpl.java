package com.midea.pam.ctc.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.CustomerTransferReverseDto;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimInvoiceRelDto;
import com.midea.pam.common.ctc.dto.RefundApplyDetailDTO;
import com.midea.pam.common.ctc.dto.WriteOffDto;
import com.midea.pam.common.ctc.dto.WriteOffInvoiceRelDto;
import com.midea.pam.common.ctc.entity.CustomerTransfer;
import com.midea.pam.common.ctc.entity.CustomerTransferExample;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.InvoiceReceivableExample;
import com.midea.pam.common.ctc.entity.MilepostTemplateStage;
import com.midea.pam.common.ctc.entity.MilepostTemplateStageExample;
import com.midea.pam.common.ctc.entity.ReceiptClaim;
import com.midea.pam.common.ctc.entity.ReceiptClaimContractRel;
import com.midea.pam.common.ctc.entity.ReceiptClaimContractRelExample;
import com.midea.pam.common.ctc.entity.ReceiptClaimDetail;
import com.midea.pam.common.ctc.entity.ReceiptClaimInvoiceRel;
import com.midea.pam.common.ctc.entity.ReceiptClaimInvoiceRelExample;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetail;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailExample;
import com.midea.pam.common.ctc.entity.RefundApplyDetail;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.entity.WriteOffInvoiceRel;
import com.midea.pam.common.ctc.vo.ReceiptClaimContractRelVO;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CustomerTransferStatus;
import com.midea.pam.common.enums.EmailStatus;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.GlPeriodType;
import com.midea.pam.common.enums.NoticeBusinessType;
import com.midea.pam.common.enums.ReceiptClaimDistributeContractType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.Backlog;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.CommonErpStatus;
import com.midea.pam.ctc.common.enums.CustomerTransferReverseSyncStatus;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.enums.ReceiptClaimEnum;
import com.midea.pam.ctc.common.enums.WriteOffEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.ContractService;
import com.midea.pam.ctc.contract.service.InvoicePlanService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.CustomerTransferExtMapper;
import com.midea.pam.ctc.mapper.CustomerTransferMapper;
import com.midea.pam.ctc.mapper.InvoiceReceivableExtMapper;
import com.midea.pam.ctc.mapper.MilepostTemplateStageMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimContractRelExtMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimContractRelMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimDetailMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimExtMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimInvoiceRelExtMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimInvoiceRelMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimMapper;
import com.midea.pam.ctc.mapper.ReceiptInvoiceRelationMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailMapper;
import com.midea.pam.ctc.mapper.RefundApplyDetailExtMapper;
import com.midea.pam.ctc.mapper.RefundApplyMapper;
import com.midea.pam.ctc.mapper.WriteOffInvoiceRelMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.InvoiceReceivableService;
import com.midea.pam.ctc.service.NoticeService;
import com.midea.pam.ctc.service.ReceiptClaimService;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.ctc.service.WriteOffService;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: pam
 * @description: WriteOffServiceImpl
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
public class WriteOffServiceImpl implements WriteOffService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${route.contractUrl}")
    private String contractUrl;

    @Resource
    EsbService esbService;
    @Resource
    private SdpService sdpService;
    @Resource
    ContractService contractService;
    @Resource
    InvoicePlanService invoicePlanService;
    @Resource
    ReceiptClaimMapper receiptClaimMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private ReceiptClaimExtMapper receiptClaimExtMapper;
    @Resource
    ReceiptClaimService receiptClaimService;
    @Resource
    ReceiptClaimContractRelExtMapper rcContractRelExtMapper;
    @Resource
    ReceiptClaimInvoiceRelMapper rcInvoiceRelMapper;
    @Resource
    ReceiptClaimInvoiceRelExtMapper rcInvoiceRelExtMapper;
    @Resource
    ReceiptClaimDetailMapper receiptClaimDetailMapper;
    @Resource
    private NoticeService noticeService;
    @Resource
    private WriteOffInvoiceRelMapper writeOffInvoiceRelMapper;
    @Resource
    ReceiptPlanDetailMapper receiptPlanDetailMapper;
    @Resource
    private ReceiptClaimContractRelMapper receiptClaimContractRelMapper;
    @Resource
    private InvoiceReceivableService invoiceReceivableService;
    @Resource
    private ReceiptInvoiceRelationMapper receiptInvoiceRelationMapper;
    @Resource
    private ReceiptClaimInvoiceRelMapper receiptClaimInvoiceRelMapper;
    @Resource
    private RefundApplyDetailExtMapper refundApplyDetailExtMapper;
    @Resource
    private MilepostTemplateStageMapper milepostTemplateStageMapper;
    @Resource
    private RefundApplyMapper refundApplyMapper;
    @Resource
    private ReceiptPlanDetailExtMapper receiptPlanDetailExtMapper;
    @Resource
    private ReceiptClaimInvoiceRelExtMapper receiptClaimInvoiceRelExtMapper;
    @Resource
    private InvoiceReceivableExtMapper invoiceReceivableExtMapper;
    @Resource
    private CustomerTransferMapper customerTransferMapper;
    @Resource
    private CustomerTransferExtMapper customerTransferExtMapper;


    /**
     * save
     *
     * @param dto
     */
    @Override
    public void save(ReceiptClaimInvoiceRelDto dto) {
        ReceiptClaimInvoiceRel rel = new ReceiptClaimInvoiceRel();
        BeanUtils.copyProperties(dto, rel);
        if (ObjectUtils.isEmpty(rel.getId())) {
            rel.setDeletedFlag(0);
            rcInvoiceRelMapper.insert(rel);
        } else {
            rcInvoiceRelMapper.updateByPrimaryKeySelective(rel);
        }
    }

    @Override
    public PageInfo<WriteOffDto> page(WriteOffDto paramDto) {
//        paramDto.setErpStatus(ReceiptClaimEnum.ERP_ACCOUNTED.getCode());//ERP已入账
        paramDto.setBusinessType(ReceiptClaimEnum.SALE.getCode());//销售类型
        paramDto.setClaimStatus(ReceiptClaimEnum.CLAIMED.getCode());//已认款
        paramDto.setDeletedFlag(0);
        PageInfo<ReceiptClaimDto> claimDtoPage = receiptClaimService.queryReceiptClaimDetailPage(paramDto);

        PageInfo<WriteOffDto> writeOffDtoPage = BeanConverter.convertPage(claimDtoPage.getList(), WriteOffDto.class);
        writeOffDtoPage.setTotal(claimDtoPage.getTotal());
        for (WriteOffDto writeOffDto : writeOffDtoPage.getList()) {
            //已分配合同金额
            writeOffDto.setContractedAmount(this.getContractedAmount(writeOffDto.getId()));
            //已核销发票金额
            ReceiptClaimInvoiceRelDto parm = new ReceiptClaimInvoiceRelDto();
            parm.setReceiptClaimDetailId(writeOffDto.getId());
            parm.setInvoiceWfStatus(WriteOffEnum.WRITTEN_OFF.getCode());//已核销
            List<ReceiptClaimInvoiceRelDto> invoiceRelDtoList = rcInvoiceRelExtMapper.getInvoicedList(parm);
            writeOffDto.setWrittenOffAmount(this.getWrittenOffAmount(invoiceRelDtoList));
        }
        return writeOffDtoPage;
    }


    /**
     * 已分配合同金额
     *
     * @param receiptClaimDetailId
     * @return
     */
    private BigDecimal getContractedAmount(Long receiptClaimDetailId) {
        BigDecimal amount = rcContractRelExtMapper.getTotalContractedAmount(receiptClaimDetailId);
        if (ObjectUtils.isEmpty(amount)) {
            amount = new BigDecimal(0);
        }
        return amount;
    }

    /**
     * 已核销发票金额
     *
     * @param dtoList
     * @return
     */
    private BigDecimal getWrittenOffAmount(List<ReceiptClaimInvoiceRelDto> dtoList) {
        BigDecimal totalAmount = new BigDecimal(0);
        for (ReceiptClaimInvoiceRelDto relDto : dtoList) {
            if (relDto.getCancelBy() == null) {
                totalAmount = BigDecimalUtils.add(totalAmount, relDto.getWriteOffAmount());
            }
        }
        return totalAmount;
    }

    /**
     * 检查收款是否已核销
     *
     * @param claimDetailId
     * @return
     */
    @Override
    public Boolean checkHasWriteOff(Long claimDetailId) {
        //存在核销关系不存在分配关系即已核销未分配的返回 true
        return rcInvoiceRelExtMapper.checkHasWriteOff(claimDetailId);
    }

    /**
     * 分配合同列表
     *
     * @param claimDetailId
     * @return
     */
    @Override
    public ReceiptClaimContractRelVO getContractedList(Long claimDetailId) {
        ReceiptClaimContractRelVO relVO = new ReceiptClaimContractRelVO();

        // 拆款金额
        ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(claimDetailId);
        BigDecimal claimAmount = Optional.ofNullable(receiptClaimDetail).map(ReceiptClaimDetail::getClaimAmount).orElse(null);
        relVO.setClaimAmount(claimAmount);
        relVO.setReceiptCode(receiptClaimDetail.getReceiptCode());
        relVO.setCustomerName(receiptClaimDetail.getCustomerName());
        ReceiptClaim receiptClaim = receiptClaimMapper.selectByPrimaryKey(receiptClaimDetail.getReceiptClaimId());
        relVO.setCurrencyCode(receiptClaim.getCurrencyCode());

        // 已转款金额 = 转款金额 + 手续费
        CustomerTransferExample transferExample = new CustomerTransferExample();
        transferExample.createCriteria().andOriginReceiptClaimDetailIdEqualTo(claimDetailId)
                .andTransferStatusNotEqualTo(CustomerTransferStatus.CANCEL.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<CustomerTransfer> customerTransferList = customerTransferMapper.selectByExample(transferExample);
        BigDecimal transferTotalAmount = customerTransferList.stream()
                .filter(e -> {
                    if (Objects.equals(e.getTransferStatus(), CustomerTransferStatus.REVERSED.getCode())) {
                        CustomerTransferReverseDto customerTransferReverseDto = customerTransferExtMapper.queryReverseInfoById(e.getId());
                        if (customerTransferReverseDto != null && Objects.equals(customerTransferReverseDto.getReverseSyncStatus(), CustomerTransferReverseSyncStatus.SUCCESS.getCode())) {
                            return false;
                        }
                    }
                    return true;
                })
                .flatMap(e -> Stream.of(e.getTransferAmount(), e.getCommission())).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        relVO.setTransferTotalAmount(transferTotalAmount);

        ReceiptClaimContractRelDto paramDto = new ReceiptClaimContractRelDto();
        paramDto.setReceiptClaimDetailId(claimDetailId);
        List<ReceiptClaimContractRelDto> dtoList = rcContractRelExtMapper.getContractedList(paramDto);
        if (CollectionUtils.isEmpty(dtoList)) {
            relVO.setAllocatedTotalAmount(BigDecimal.ZERO);
            relVO.setAllocatedTotalRemainAmount(BigDecimalUtils.subtract(relVO.getClaimAmount(), relVO.getTransferTotalAmount()));
            return relVO;
        }
        Map<Long, Long> invoicePlanDetailMap = new HashMap<>();
        List<Long> contractIdList = new ArrayList<>();
        List<Long> invoicePlanIdList = new ArrayList<>();
        List<Long> invoicePlanDetailIdList = new ArrayList<>();
        List<Long> milestoneIdList = new ArrayList<>();
        for (ReceiptClaimContractRelDto dto : dtoList) {
            contractIdList.add(dto.getContractId());
            //分配人
            if (Objects.nonNull(dto.getAllocatedBy())) {
                UserInfo user = CacheDataUtils.findUserById(dto.getAllocatedBy());
                if (Objects.nonNull(user)) {
                    dto.setAllocatedByName(user.getName());
                }
            }
            Long invoicePlanId = dto.getInvoicePlanId();
            if (Objects.nonNull(invoicePlanId)) {
                invoicePlanIdList.add(invoicePlanId);
            }
            Long invoicePlanDetailId = dto.getInvoicePlanDetailId();
            Long milestoneId = dto.getMilestoneId();
            if (Objects.nonNull(invoicePlanDetailId)) {
                invoicePlanDetailIdList.add(invoicePlanDetailId);
                invoicePlanDetailMap.put(invoicePlanDetailId, milestoneId);
            }
            if (Objects.nonNull(milestoneId)) {
                milestoneIdList.add(milestoneId);
            }
        }
        // 已分配金额
        BigDecimal allocatedTotalAmount = dtoList.stream().map(e -> Optional.ofNullable(e.getLocalAllocatedAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        relVO.setAllocatedTotalAmount(allocatedTotalAmount);
        // 本拆款剩余分配金额 = 拆款金额 - 本拆款已分配金额 - 已转款金额
        BigDecimal allocatedTotalRemainAmount = Optional.ofNullable(claimAmount).orElse(BigDecimal.ZERO).subtract(allocatedTotalAmount).subtract(transferTotalAmount);
        relVO.setAllocatedTotalRemainAmount(allocatedTotalRemainAmount);

        // 其它拆款已分配金额 contractId + invoicePlanId + invoicePlanDetailId 确定范围
        Map<String, List<ReceiptClaimContractRel>> contractRelMap = new HashMap<>();
        Map<String, List<RefundApplyDetail>> refundApplyDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(invoicePlanIdList)) {
            ReceiptClaimContractRelExample relExample = new ReceiptClaimContractRelExample();
            relExample.createCriteria().andContractIdIn(contractIdList).andInvoicePlanIdIn(invoicePlanIdList).andInvoicePlanDetailIdIn(invoicePlanDetailIdList)
                    .andDeletedFlagEqualTo(0);
            List<ReceiptClaimContractRel> relList = receiptClaimContractRelMapper.selectByExample(relExample);
            if (CollectionUtils.isNotEmpty(relList)) {
                contractRelMap.putAll(relList.stream().collect(Collectors.groupingBy(e ->
                        getReceiptClaimContractRelKey(e.getContractId(), e.getInvoicePlanId(), e.getInvoicePlanDetailId()))));

                //退款 contractId + receiptClaimDetailId 分组
                List<Long> receiptClaimDetailIdList = relList.stream().map(ReceiptClaimContractRel::getReceiptClaimDetailId).collect(Collectors.toList());
                List<RefundApplyDetailDTO> refundApplyDetailList = refundApplyDetailExtMapper.getRefundApplyDetailList(contractIdList, receiptClaimDetailIdList);
                if (CollectionUtils.isNotEmpty(refundApplyDetailList)) {
                    refundApplyDetailMap.putAll(refundApplyDetailList.stream().collect(Collectors.groupingBy(e ->
                            getRefundApplyDetailKey(e.getContractId(), e.getReceiptClaimDetailId()))));
                }
            }
        }
        // 开票计划详情
        Map<Long, String> milepostTemplateStageMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(milestoneIdList)) {
            MilepostTemplateStageExample stageExample = new MilepostTemplateStageExample();
            stageExample.createCriteria().andDeletedFlagEqualTo(false).andIdIn(milestoneIdList);
            List<MilepostTemplateStage> milepostTemplateStageList = milepostTemplateStageMapper.selectByExample(stageExample);
            for (MilepostTemplateStage milepostTemplateStage : milepostTemplateStageList) {
                milepostTemplateStageMap.put(milepostTemplateStage.getId(), milepostTemplateStage.getMilepostStage());
            }
        }

        dtoList.forEach(dto -> {
            // 关联里程碑
            Optional.ofNullable(dto.getInvoicePlanDetailId()).ifPresent(invoicePlanDetailId -> {
                Optional.ofNullable(invoicePlanDetailMap.get(invoicePlanDetailId)).ifPresent(milestoneId -> {
                    dto.setMilestoneName(milepostTemplateStageMap.get(milestoneId));
                });
            });
            // 其它拆款已分配金额 receipt_claim_contract_rel表中，本invoice_plan_id所关联receipt_claim_detail_id ≠当前receipt_claim_detail_id的累加值
            // 通过contractId + invoicePlanId + invoicePlanDetailId 找到不等于当前receipt_claim_detail_id 的所有数据的allocatedAmount累加值
            Optional.ofNullable(dto.getInvoicePlanDetailId()).ifPresent(invoicePlanDetailId -> {
                //回款
                List<ReceiptClaimContractRel> relList = contractRelMap.get(getReceiptClaimContractRelKey(dto.getContractId(), dto.getInvoicePlanId(), invoicePlanDetailId));
                BigDecimal allocatedAmount = CollectionUtils.isEmpty(relList) ? BigDecimal.ZERO : relList.stream()
                        .filter(e -> !Objects.equals(e.getReceiptClaimDetailId(), claimDetailId))
                        .map(e -> Optional.ofNullable(e.getAllocatedAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                //退款
                List<Long> receiptClaimDetailIds = CollectionUtils.isEmpty(relList) ? Lists.emptyList() : relList.stream()
                        .filter(e -> !Objects.equals(e.getReceiptClaimDetailId(), claimDetailId))
                        .map(ReceiptClaimContractRel::getReceiptClaimDetailId).collect(Collectors.toList());
                List<RefundApplyDetail> refundApplyDetailList = new ArrayList<>();
                for (Long receiptClaimDetailId : receiptClaimDetailIds) {
                    refundApplyDetailList.addAll(Optional.ofNullable(refundApplyDetailMap.get(getRefundApplyDetailKey(dto.getContractId(), receiptClaimDetailId))).orElse(Lists.emptyList()));
                }
                BigDecimal refundAmount = CollectionUtils.isEmpty(refundApplyDetailList) ? BigDecimal.ZERO : refundApplyDetailList.stream()
                        .map(RefundApplyDetail::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
                // 回款分配的时候，其它拆分金额需要减去退款金额
                dto.setOtherAllocatedAmount(allocatedAmount.subtract(refundAmount));
                // 开票计划可分配额
                dto.setAllocatableAmount(dto.getInvoicePlanDetailAmount().subtract(dto.getOtherAllocatedAmount()));
            });
        });
        setRefundAmount(dtoList);
        relVO.setReceiptClaimContractRelDtoList(dtoList);
        return relVO;
    }

    /**
     * 收款合同分配明细列表-按合同维度
     *
     * @param claimDetailId
     * @return
     */
    @Override
    public List<ReceiptClaimContractRelDto> getReceiptClaimContractRelByContract(Long claimDetailId) {
        return rcContractRelExtMapper.getReceiptClaimContractRelByContract(claimDetailId);
    }

    @Override
    public PageInfo<ReceiptClaimContractRelDto> getContractList(ReceiptClaimContractRelDto receiptClaimContractRelDto, Integer pageSize, Integer pageNum) {
        if (StringUtils.isNotEmpty(receiptClaimContractRelDto.getAllocatableTag())) {
            List<Integer> allocatableTagList = new ArrayList<>();
            String[] arrStr = receiptClaimContractRelDto.getAllocatableTag().split(",");
            for (String s : arrStr) {
                if (StringUtils.isNotEmpty(s)) {
                    allocatableTagList.add(Integer.valueOf(s));
                }
            }
            receiptClaimContractRelDto.setAllocatableTagList(allocatableTagList);
        }
        PageMethod.startPage(pageNum, pageSize);
        List<ReceiptClaimContractRelDto> dtoList = rcContractRelExtMapper.getContractList(receiptClaimContractRelDto);
        if (CollectionUtils.isEmpty(dtoList)) {
            return new PageInfo<>();
        }
        Map<Long, Long> invoicePlanDetailMap = new HashMap<>();
        List<Long> milestoneIdList = new ArrayList<>();
        for (ReceiptClaimContractRelDto dto : dtoList) {
            Long invoicePlanDetailId = dto.getInvoicePlanDetailId();
            Long milestoneId = dto.getMilestoneId();
            if (Objects.nonNull(invoicePlanDetailId)) {
                invoicePlanDetailMap.put(invoicePlanDetailId, milestoneId);
            }
            if (Objects.nonNull(milestoneId)) {
                milestoneIdList.add(milestoneId);
            }
        }
        // 开票计划详情
        Map<Long, String> milepostTemplateStageMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(milestoneIdList)) {
            MilepostTemplateStageExample stageExample = new MilepostTemplateStageExample();
            stageExample.createCriteria().andDeletedFlagEqualTo(false).andIdIn(milestoneIdList);
            List<MilepostTemplateStage> milepostTemplateStageList = milepostTemplateStageMapper.selectByExample(stageExample);
            for (MilepostTemplateStage milepostTemplateStage : milepostTemplateStageList) {
                milepostTemplateStageMap.put(milepostTemplateStage.getId(), milepostTemplateStage.getMilepostStage());
            }
        }
        dtoList.forEach(dto -> {
            // 关联里程碑
            Optional.ofNullable(dto.getInvoicePlanDetailId()).ifPresent(invoicePlanDetailId -> {
                Optional.ofNullable(invoicePlanDetailMap.get(invoicePlanDetailId)).ifPresent(milestoneId -> {
                    dto.setMilestoneName(milepostTemplateStageMap.get(milestoneId));
                });
            });
        });
        return new PageInfo<>(dtoList);
    }

    /**
     * 设置回款认领的退款金额
     *
     * @param dtoList 回款认领
     */
    private void setRefundAmount(List<ReceiptClaimContractRelDto> dtoList) {
        List<RefundApplyDetailDTO> refundApplyDetailDTOList = refundApplyDetailExtMapper.selectByPlanIdAndClaimId(null, dtoList.get(0).getReceiptClaimDetailId());
        // 开票计划行id—退款金额 Map
        Map<Long, BigDecimal> refundAmountMap = refundApplyDetailDTOList.stream().collect(Collectors.toMap(RefundApplyDetailDTO::getInvoicePlanDetailId, RefundApplyDetailDTO::getRefundAmount, BigDecimal::add));
        dtoList.forEach(e ->
                e.setRefundAmount(Optional.ofNullable(refundAmountMap.get(e.getInvoicePlanDetailId())).orElse(BigDecimal.ZERO))
        );
    }

    private String getReceiptClaimContractRelKey(Long contractId, Long invoicePlanId, Long invoicePlanDetailId) {
        return String.format("contractId_%s_invoicePlanId_%s_invoicePlanDetailId_%s", contractId, invoicePlanId, invoicePlanDetailId);
    }

    private String getRefundApplyDetailKey(Long contractId, Long receiptClaimDetailId) {
        return String.format("contractId_%s_receiptClaimDetailId_%s", contractId, receiptClaimDetailId);
    }

    /**
     * 合同拆分保存
     *
     * @param dtoList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveContractedList(List<ReceiptClaimContractRelDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            throw new BizException(Code.ERROR, "请选择合同！");
        }
        Long receiptClaimDetailId = dtoList.get(0).getReceiptClaimDetailId();
        CustomerTransfer customerTransfer = receiptClaimExtMapper.queryCustomerTransferByReceiptClaimDetailId(receiptClaimDetailId);
        if (Objects.nonNull(customerTransfer) && Objects.equals(customerTransfer.getTransferStatus(), CustomerTransferStatus.REVERSING.getCode())) {
            throw new BizException(Code.ERROR, "收款对应的转款单在冲销审批中，不允许做收款分配");
        }
        // 收集所有新分配的invoicePlanDetailId
        List<Long> invoicePlanDetailIds = dtoList.stream().filter(e -> Objects.isNull(e.getId()))
                .map(ReceiptClaimContractRel::getInvoicePlanDetailId)
                .collect(Collectors.toList());
        //同一收款下开票计划行不允许重复分配
        if (ListUtils.isNotEmpty(invoicePlanDetailIds)) {
            ReceiptClaimContractRelExample relExample = new ReceiptClaimContractRelExample();
            relExample.createCriteria()
                    .andReceiptClaimDetailIdEqualTo(receiptClaimDetailId).andInvoicePlanDetailIdIn(invoicePlanDetailIds).andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
            List<ReceiptClaimContractRel> receiptClaimContractRels = receiptClaimContractRelMapper.selectByExample(relExample);
            if (CollectionUtils.isNotEmpty(receiptClaimContractRels)) {
                throw new MipException("部分开票计划行已分配，请刷新页面重试");
            }
        }
        List<Long> needSendEmailIds = new ArrayList<>(dtoList.size());
        BigDecimal localAllocatedAmount = BigDecimal.ZERO;
        List<ReceiptClaimContractRel> needAllocations = new ArrayList<>(dtoList.size());
        for (ReceiptClaimContractRelDto relDto : dtoList) {
            ReceiptClaimContractRel rel = BeanConverter.copy(relDto, ReceiptClaimContractRel.class);
            if (rel.getId() == null) {
                rel.setAllocatedBy(SystemContext.getUserId());
                rel.setAllocatedDate(DateUtil.getCurrentDate());
                rel.setDeletedFlag(0);
                receiptClaimContractRelMapper.insert(rel);
                needAllocations.add(rel);
                needSendEmailIds.add(rel.getId());
                //更新开票计划可分配额allocatable_amount
                invoicePlanService.updateAllocatableAmount(rel.getInvoicePlanDetailId(), ReceiptClaimDistributeContractType.DISTRIBUTE);
            } else {
                receiptClaimContractRelMapper.updateByPrimaryKeySelective(rel);
            }
            localAllocatedAmount = localAllocatedAmount.add(rel.getLocalAllocatedAmount());
        }
        checkIsOverReceiptAmount(dtoList);
        //更新收款的合同状态
        if (BigDecimalUtils.isGreater(localAllocatedAmount, BigDecimal.ZERO)) {
            // 已转款金额 = 转款金额 + 手续费
            CustomerTransferExample transferExample = new CustomerTransferExample();
            transferExample.createCriteria().andOriginReceiptClaimDetailIdEqualTo(receiptClaimDetailId)
                    .andTransferStatusNotEqualTo(CustomerTransferStatus.CANCEL.getCode())
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<CustomerTransfer> customerTransferList = customerTransferMapper.selectByExample(transferExample);
            BigDecimal transferTotalAmount = customerTransferList.stream().flatMap(e -> Stream.of(e.getTransferAmount(), e.getCommission())).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
            // 分配金额的计算要加上已转款金额
            localAllocatedAmount = BigDecimalUtils.add(localAllocatedAmount, transferTotalAmount);
            ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(receiptClaimDetailId);
            if (localAllocatedAmount.compareTo(receiptClaimDetail.getClaimAmount()) > 0) {
                throw new MipException("分配金额合计不能大于认款总额！");
            } else if (BigDecimalUtils.isEquals(localAllocatedAmount, receiptClaimDetail.getClaimAmount())) {
                //完全分配
                receiptClaimDetail.setContractStatus(ReceiptClaimEnum.CONTRACT_ALLOCATED.getCode());
            } else {
                //部分分配
                receiptClaimDetail.setContractStatus(ReceiptClaimEnum.CONTRACT_PARTIAL_ALLOCATED.getCode());
            }
            receiptClaimDetailMapper.updateByPrimaryKeySelective(receiptClaimDetail);
        }
        //将回款金额分配到回款计划行
        reCalculateContractReceiptAmount(dtoList.stream().map(ReceiptClaimContractRelDto::getContractId).collect(Collectors.toList()));
        for (ReceiptClaimContractRel receiptClaimContractRel : needAllocations) {
            // PAM-ERP-070 PAM收款合同分配变更同步ERP
            syncContractCodeToErp(receiptClaimContractRel.getReceiptClaimDetailId());
        }
        if (!needSendEmailIds.isEmpty()) {
            ReceiptClaimContractRelDto query = new ReceiptClaimContractRelDto();
            query.setIds(needSendEmailIds);
            List<ReceiptClaimContractRelDto> needSendEmailData = rcContractRelExtMapper.getDataGroupByContracted(query);
            if (CollectionUtils.isNotEmpty(needSendEmailData)) {
                //发送邮件提醒
                this.sendNoticeEmail(needSendEmailData, false);
            }
        }
    }

    /**
     * 校验合同实际回款金额是否已超过计划回款金额
     *
     * @param dtoList 回款认领
     */
    private void checkIsOverReceiptAmount(List<ReceiptClaimContractRelDto> dtoList) {
        List<Long> contractIds = dtoList.stream().map(ReceiptClaimContractRelDto::getContractId).collect(Collectors.toList());
        //退款
        List<RefundApplyDetailDTO> refundApplyDetails = refundApplyDetailExtMapper.selectByContractId(contractIds);
        Map<Long, BigDecimal> refundApplyMap = refundApplyDetails.stream().collect(Collectors.toMap(
                RefundApplyDetailDTO::getContractId, RefundApplyDetailDTO::getRefundAmount, BigDecimal::add));
        //计划回款金额
        ReceiptPlanDetailExample rpdExample = new ReceiptPlanDetailExample();
        rpdExample.createCriteria()
                .andContractIdIn(contractIds)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<ReceiptPlanDetail> receiptPlanDetails = receiptPlanDetailMapper.selectByExample(rpdExample);
        Map<Long, BigDecimal> receiptPlanDetailMap = receiptPlanDetails.stream().collect(Collectors.toMap(
                ReceiptPlanDetail::getContractId, ReceiptPlanDetail::getAmount, BigDecimal::add));
        //本次开票计划分配额
        ReceiptClaimContractRelExample rcrExample = new ReceiptClaimContractRelExample();
        rcrExample.createCriteria()
                .andContractIdIn(contractIds)
                .andDeletedFlagEqualTo(0);
        List<ReceiptClaimContractRel> receiptClaimContractRelList = receiptClaimContractRelMapper.selectByExample(rcrExample);
        Map<Long, BigDecimal> receiptClaimContractRelMap = receiptClaimContractRelList.stream().collect(Collectors.toMap(
                ReceiptClaimContractRel::getContractId, ReceiptClaimContractRel::getAllocatedAmount, BigDecimal::add));

        receiptPlanDetailMap.forEach((contractId, receiptPlanAmountSum) -> {
            BigDecimal allocatedAmountSum = Optional.ofNullable(receiptClaimContractRelMap.get(contractId)).orElse(BigDecimal.ZERO);
            BigDecimal refundApplyAmountSum = Optional.ofNullable(refundApplyMap.get(contractId)).orElse(BigDecimal.ZERO);
            if (receiptPlanAmountSum.compareTo(allocatedAmountSum.subtract(refundApplyAmountSum)) < 0) {
                throw new MipException("合同实际回款总金额不得大于合同计划回款总金额！");
            }
        });
    }

    public void reCalculateContractReceiptAmount(List<Long> contractIds) {
        for (Long contractId : contractIds) {
            ReceiptClaimContractRelExample relExample = new ReceiptClaimContractRelExample();
            relExample.createCriteria().andContractIdEqualTo(contractId)
                    .andDeletedFlagEqualTo(0);
            // 所有涉及此合同的认款分配
            BigDecimal allocatedAmount = BigDecimal.ZERO;
            List<ReceiptClaimContractRel> receiptClaimContractRelList = receiptClaimContractRelMapper.selectByExample(relExample);
            if (!CollectionUtils.isEmpty(receiptClaimContractRelList)) {
                allocatedAmount = receiptClaimContractRelList.stream()
                        .map(ReceiptClaimContractRel::getAllocatedAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
            }
            // 所有涉及此合同的退款
            BigDecimal refundAmount = BigDecimal.ZERO;
            List<RefundApplyDetailDTO> refundApplyDetailDTOList = refundApplyDetailExtMapper.getRefundApplyDetailList(Collections.singletonList(contractId), null);
            if (!CollectionUtils.isEmpty(refundApplyDetailDTOList)) {
                refundAmount = refundApplyDetailDTOList.stream().map(RefundApplyDetailDTO::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
            }
            //计算实际回款总额=回款计划分配金额-退款金额
            BigDecimal actualAmount = allocatedAmount.subtract(refundAmount);
            if (BigDecimalUtils.isGreater(actualAmount, BigDecimal.ZERO)) {
                List<ReceiptPlanDetail> receiptPlanDetails = new ArrayList<>();
                //查询其所有的回款计划
                ReceiptPlanDetailExample rpdExample = new ReceiptPlanDetailExample();
                rpdExample.setOrderByClause("date");
                rpdExample.createCriteria().andContractIdEqualTo(contractId)
                        .andDeletedFlagEqualTo(Boolean.FALSE);
                // 所有涉及合同的回款计划行
                List<ReceiptPlanDetail> receiptPlanDetailList = receiptPlanDetailMapper.selectByExample(rpdExample);
                if (!CollectionUtils.isEmpty(receiptPlanDetailList)) {
                    // 将回款金额分配到回款计划行
                    for (ReceiptPlanDetail detail : receiptPlanDetailList) {
                        if (actualAmount.compareTo(detail.getAmount()) >= 0) {
                            detail.setActualAmount(detail.getAmount());
                            actualAmount = actualAmount.subtract(detail.getAmount());
                        } else {
                            detail.setActualAmount(actualAmount);
                            actualAmount = BigDecimal.ZERO;
                        }
                        receiptPlanDetails.add(detail);
                    }
                    receiptPlanDetailExtMapper.batchUpdate(receiptPlanDetails);
                }
            } else {
                ReceiptPlanDetail detail = new ReceiptPlanDetail();
                detail.setContractId(contractId);
                detail.setActualAmount(BigDecimal.ZERO);
                detail.setUpdateAt(new Date());
                detail.setUpdateBy(Objects.nonNull(SystemContext.getUserId()) ? SystemContext.getUserId() : -1L);
                receiptPlanDetailExtMapper.updateActualAmount(detail);
            }
        }
    }

    /**
     * 如果更换客户编码的时候需要更新receipt_claim_detail 然后把对应客户的receipt_claim_contract_rel中的deleteFlag置为1并更新回款金额
     * 如果是相同客户ID 不触发这个接口
     */
    @Override
    public void saveCustomer(ReceiptClaimDetail dto) {
        if (!ObjectUtils.isEmpty(dto.getId())) {
            dto.setContractStatus(ReceiptClaimEnum.CONTRACT_UNALLOCATED.getCode());
            receiptClaimDetailMapper.updateByPrimaryKeySelective(dto);
            ReceiptClaimContractRelExample example = new ReceiptClaimContractRelExample();
            example.createCriteria().andReceiptClaimDetailIdEqualTo(dto.getId()).andDeletedFlagEqualTo(0);
            List<ReceiptClaimContractRel> receiptClaimContractRels = receiptClaimContractRelMapper.selectByExample(example);
            // Long contractId =null;
            // BigDecimal allocatedAmount=BigDecimal.ZERO;
            for (ReceiptClaimContractRel receiptClaimContractRel : receiptClaimContractRels) {
                //contractId = receiptClaimContractRel.getContractId();
                //allocatedAmount = allocatedAmount.add(receiptClaimContractRel.getAllocatedAmount());
                receiptClaimService.unDoReceiptPlanDetails(receiptClaimContractRel.getContractId(), receiptClaimContractRel.getAllocatedAmount(), receiptClaimContractRel.getInvoicePlanDetailId());
                receiptClaimContractRel.setDeletedFlag(1);
                receiptClaimContractRelMapper.updateByPrimaryKeySelective(receiptClaimContractRel);
            }

        }
    }

    /**
     * 合同拆分删除
     *
     * @param dtoList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteContractRel(List<ReceiptClaimContractRelDto> dtoList) {
        Guard.notNullOrEmpty(dtoList, "删除的数据为空");

        List<Long> relIds = new ArrayList<>();
        for (ReceiptClaimContractRelDto relDto : dtoList) {
            //校验核销关系：该资金单下的发票对应的合同是否与传值一致
            ReceiptClaimInvoiceRelExample example = new ReceiptClaimInvoiceRelExample();
            example.createCriteria().andDeletedFlagEqualTo(0)
                    .andReceiptClaimDetailIdEqualTo(relDto.getReceiptClaimDetailId())
                    .andErpStatusNotEqualTo(WriteOffEnum.ERP_REVERSED.getCode());
            List<ReceiptClaimInvoiceRel> claimInvoiceRels = receiptClaimInvoiceRelMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(claimInvoiceRels)) {
                List<Long> invoiceIdList = claimInvoiceRels.stream().map(ReceiptClaimInvoiceRel::getInvoiceId).collect(Collectors.toList());
                InvoiceReceivableExample invoiceReceivableExample = new InvoiceReceivableExample();
                invoiceReceivableExample.createCriteria().andIdIn(invoiceIdList).andDeletedFlagEqualTo(0);
                List<InvoiceReceivable> invoiceReceivables = invoiceReceivableService.selectByExample(invoiceReceivableExample);
                if (invoiceReceivables.stream()
                        .anyMatch(e -> e.getContractId() != null && e.getContractId().equals(relDto.getContractId()))) {
                    throw new BizException(ErrorCode.ERROR, "该笔收款已做收款核销，如需操作请先撤回核销");
                }
            }
            //校验是否已经做过退款操作(已经退款了的收款不允许删除合同分配明细，也不允许撤销回款) added by dengfei added at 20210104 bug:BUG2020120976504
            final List<RefundApplyDetailDTO> refundRelReceipt = refundApplyDetailExtMapper.getRefundRelReceipt(relDto.getReceiptClaimDetailId(), relDto.getContractId());
            if (CollectionUtils.isNotEmpty(refundRelReceipt)) {
                throw new BizException(ErrorCode.ERROR, "该笔收款已做过退款操作，不能删除该笔收款!");
            }

            ReceiptClaimContractRel rel = new ReceiptClaimContractRel();
            BeanUtils.copyProperties(relDto, rel);
            ReceiptClaimContractRel rel1 = receiptClaimContractRelMapper.selectByPrimaryKey(relDto.getId());
            if (rel.getContractId() == null || rel.getAllocatedAmount() == null) {
                throw new BizException(ErrorCode.ERROR, "传入数据以及分配金额不能为空！");
            }
            rel.setDeletedFlag(1);
            // 删除关联关系
            receiptClaimContractRelMapper.updateByPrimaryKeySelective(rel);
            //更新开票计划可分配额allocatable_amount
            invoicePlanService.updateAllocatableAmount(rel.getInvoicePlanDetailId(), ReceiptClaimDistributeContractType.UN_DISTRIBUTE);
            relIds.add(rel.getId());
            // PAM-ERP-070 PAM收款合同分配变更同步ERP
            syncContractCodeToErp(rel.getReceiptClaimDetailId());
        }
        reCalculateContractReceiptAmount(dtoList.stream().map(ReceiptClaimContractRelDto::getContractId).collect(Collectors.toList()));
        BigDecimal contractAmout = this.getContractedAmount(dtoList.get(0).getReceiptClaimDetailId());  // 已经分配的金额
        ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(dtoList.get(0).getReceiptClaimDetailId());
        if (BigDecimalUtils.isEquals(contractAmout, BigDecimal.ZERO)) {
            receiptClaimDetail.setContractStatus(ReceiptClaimEnum.CONTRACT_UNALLOCATED.getCode());
        } else {
            receiptClaimDetail.setContractStatus(ReceiptClaimEnum.CONTRACT_PARTIAL_ALLOCATED.getCode());
        }
        receiptClaimDetailMapper.updateByPrimaryKeySelective(receiptClaimDetail);


        if (relIds.size() > 0) {
            ReceiptClaimContractRelDto query = new ReceiptClaimContractRelDto();
            query.setIds(relIds);
            List<ReceiptClaimContractRelDto> NeedSendEmailData = rcContractRelExtMapper.getDeletedRelData(query);
            this.sendNoticeEmail(NeedSendEmailData, true);//发送邮件提醒
        }

    }

    /**
     * 手工同步收款销售子合同编号
     *
     * @param claimDetailId
     */
    @Override
    @Transactional
    public void syncContractCode(Long claimDetailId) {
        ReceiptClaimDetail receiptPlanDetail = receiptClaimDetailMapper.selectByPrimaryKey(claimDetailId);
        if (receiptPlanDetail == null) {
            throw new BizException(Code.ERROR, "收款编号不存在");
        }
        if (!Objects.equals(receiptPlanDetail.getContractSyncStatus(), CommonErpStatus.PUSH_FAILED.code())) {
            throw new BizException(Code.ERROR, "请勿重复推送");
        }
        // PAM-ERP-070 PAM收款合同分配变更同步ERP
        syncContractCodeToErp(claimDetailId);
    }

    private void syncContractCodeToErp(Long receiptClaimDetailId) {
        // 更新同步状态
        ReceiptClaimDetail receiptClaimDetail = new ReceiptClaimDetail();
        receiptClaimDetail.setContractSyncStatus(CommonErpStatus.PUSHING.code());
        receiptClaimDetail.setContractSyncMessage("");
        receiptClaimDetail.setId(receiptClaimDetailId);
        int count = receiptClaimExtMapper.updateContractSyncStatusByErpStatus(receiptClaimDetail);
        // 收款认领明细表 已记账 才需要同步变更
        if (count > 0) {
            //  PAM收款合同分配变更同步ERP
            HandleDispatcher.route(BusinessTypeEnums.RECEIPT_CLAIM_CHANGE.getCode(), String.valueOf(receiptClaimDetailId), null, null, true);
        }
    }


    /**
     * 核销(发票)列表
     *
     * @param claimDetailId
     * @return
     */
    @Override
    public List<ReceiptClaimInvoiceRelDto> getInvoicedList(Long claimDetailId) {
        ReceiptClaimInvoiceRelDto param = new ReceiptClaimInvoiceRelDto();
        param.setReceiptClaimDetailId(claimDetailId);
        param.setDeletedFlag(0);
        List<ReceiptClaimInvoiceRelDto> dtoList = rcInvoiceRelExtMapper.getInvoicedList(param);
        for (ReceiptClaimInvoiceRelDto dto : dtoList) {
            //核销人
            if (Objects.nonNull(dto.getWriteOffBy())) {
                if (Objects.equals(dto.getWriteOffBy(), Constants.ADMIM)) {
                    dto.setWriteOffByName("PAM自动核销");
                } else {
                    UserInfo user = CacheDataUtils.findUserById(dto.getWriteOffBy());
                    if (Objects.nonNull(user)) {
                        dto.setWriteOffByName(user.getName());
                    }
                }
            }
            if (dto.getCancelBy() != null) {
                UserInfo userInfo = CacheDataUtils.findUserById(dto.getCancelBy());
                if (userInfo != null) {
                    dto.setCancelByName(userInfo.getName());
                }
            }
        }
        return dtoList;
    }

    /**
     * 手工核销
     *
     * @param dtoList
     */
    @Override
    public void saveInvoicedList(List<ReceiptClaimInvoiceRelDto> dtoList) {
        //因前端是全量传值，后端只需处理新增核销的数据即可
        List<ReceiptClaimInvoiceRelDto> dtos = dtoList.stream().filter(e -> Objects.isNull(e.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        Long receiptClaimDetailId = dtos.get(0).getReceiptClaimDetailId();
        ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(receiptClaimDetailId);
        ReceiptClaim receiptClaim = receiptClaimMapper.selectByPrimaryKey(receiptClaimDetail.getReceiptClaimId());
        Long ouId = receiptClaim.getOuId();
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
        if (ListUtils.isEmpty(organizationRelList)) {
            throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
        }
        OrganizationRelDto organizationRelDto = organizationRelList.get(0);
        Long ledgerId = organizationRelDto.getLedgerId();
        List<GlPeriodDto> glPeriod = basedataExtService.getGlPeriod(ledgerId, GlPeriodType.RECEIVABLES_PERIOD.getCode().toString(), null);
        List<GlPeriodDto> glPeriodOpen = basedataExtService.getGlPeriod2(ledgerId, GlPeriodType.RECEIVABLES_PERIOD.getCode().toString(), "O");
        if (ListUtils.isEmpty(glPeriodOpen)) {
            throw new MipException("erp分类账为" + ledgerId + "，期间类型为" + GlPeriodType.RECEIVABLES_PERIOD.getName() + "【会计期间未打开】");
        }
        Date date = null;
        glPeriodOpen.sort(Comparator.comparing(GlPeriodDto::getEndDate).reversed());
        if (ListUtils.isNotEmpty(glPeriod)) {
            for (GlPeriodDto glPeriodDto : glPeriod) {
                String status = glPeriodDto.getClosingStatus();
                Date startDate = glPeriodDto.getStartDate();
                Date endDate = glPeriodDto.getEndDate();
                if ((new Date()).after(startDate) && (new Date()).before(DateUtils.addDay(endDate, 1)) && "O".equals(status)) {
                    date = (new Date());
                } else {
                    date = (glPeriodOpen.get(0).getEndDate());
                }
            }
        }
        //收款ID与发票ID存在核销关系，不能再核销
        ReceiptClaimInvoiceRelExample invoiceRelExample = new ReceiptClaimInvoiceRelExample();
        invoiceRelExample.createCriteria().andErpStatusNotEqualTo(WriteOffEnum.ERP_REVERSED.getCode())
                .andReceiptClaimDetailIdEqualTo(receiptClaimDetailId)
                .andDeletedFlagEqualTo(0);
        List<ReceiptClaimInvoiceRel> receiptClaimInvoiceRels = receiptClaimInvoiceRelMapper.selectByExample(invoiceRelExample);
        if (ListUtils.isNotEmpty(receiptClaimInvoiceRels)) {
            Set<Long> invoiceIdList = receiptClaimInvoiceRels.stream().map(ReceiptClaimInvoiceRel::getInvoiceId).collect(Collectors.toSet());
            List<String> invoiceCodeError = dtos.stream()
                    .filter(dto -> invoiceIdList.contains(dto.getInvoiceId()))
                    .map(ReceiptClaimInvoiceRelDto::getInvoiceCode)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(invoiceCodeError)) {
                String errorMsg = String.join(",", invoiceCodeError);
                throw new BizException(Code.ERROR, String.format("应收发票号：%s已被收款编号：%s核销，请检查！", errorMsg, receiptClaimDetail.getReceiptCode()));
            }
        }
        List<ResendExecute> infoList = new ArrayList<>();
        //构建核销关系
        for (ReceiptClaimInvoiceRelDto dto : dtos) {
            ReceiptClaimInvoiceRel rel = new ReceiptClaimInvoiceRel();
            BeanUtils.copyProperties(dto, rel);
            //核销金额为零不处理
            if (BigDecimalUtils.isEquals(rel.getWriteOffAmount(), BigDecimal.ZERO)) {
                continue;
            }
            //核销日期未填写取总账日期
            if (ObjectUtils.isEmpty(rel.getWriteOffDate())) {
                rel.setWriteOffDate(date);
            }
            rel.setGlDate(rel.getWriteOffDate());
            rel.setWriteOffBy(SystemContext.getUserId());
            rel.setInvoiceWfStatus(WriteOffEnum.NON_WRITE_OFF.getCode());//未核销
            rel.setErpStatus(WriteOffEnum.ERP_PENDING.getCode());//同步中
            rel.setDeletedFlag(0);
            rcInvoiceRelMapper.insert(rel);

            ResendExecute info = new ResendExecute();
            info.setBusinessType(BusinessTypeEnums.RECEIPT_CLAIM_WRITEOFF.getCode());
            info.setApplyNo(String.valueOf(rel.getId()));
            info.setSubApplyNo(null);
            info.setVersion(null);
            info.setDeletedFlag(Boolean.FALSE);
            info.setBatch(true);
            info.setOuId(ouId);
            infoList.add(info);
        }
        //更新收款核销状态
        ReceiptClaimInvoiceRelDto param = new ReceiptClaimInvoiceRelDto();
        param.setReceiptClaimDetailId(receiptClaimDetailId);
        rcContractRelExtMapper.updateDetailStatus(param);

        if (CollectionUtils.isNotEmpty(infoList)) {
            HandleDispatcher.batchRoute(infoList);
        }
    }


    @Override
    public void saveRedDashedInvoicedList(List<WriteOffInvoiceRelDto> dtoList) {
        List<ResendExecute> infoList = new ArrayList<>();
        for (WriteOffInvoiceRelDto dto : dtoList) {
            Long ouId = dto.getOuId();
            OrganizationRelQuery query = new OrganizationRelQuery();
            query.setOperatingUnitId(ouId);
            List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
            if (ListUtils.isEmpty(organizationRelList)) {
                throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
            }
            OrganizationRelDto organizationRelDto = organizationRelList.get(0);
            Long ledgerId = organizationRelDto.getLedgerId();
            List<GlPeriodDto> glPeriod = basedataExtService.getGlPeriod(ledgerId, GlPeriodType.RECEIVABLES_PERIOD.getCode().toString(), null);
            List<GlPeriodDto> glPeriodOpen = basedataExtService.getGlPeriod2(ledgerId, GlPeriodType.RECEIVABLES_PERIOD.getCode().toString(), "O");
            if (ListUtils.isEmpty(glPeriodOpen)) {
                throw new MipException("erp分类账为" + ledgerId + "，期间类型为" + GlPeriodType.RECEIVABLES_PERIOD.getName() + "【会计期间未打开】");
            }
            Date date = null;
            glPeriodOpen.sort(Comparator.comparing(GlPeriodDto::getEndDate).reversed());
            if (ListUtils.isNotEmpty(glPeriod)) {
                for (GlPeriodDto glPeriodDto : glPeriod) {
                    String status = glPeriodDto.getClosingStatus();
                    Date startDate = glPeriodDto.getStartDate();
                    Date endDate = glPeriodDto.getEndDate();
                    if ((new Date()).after(startDate) && (new Date()).before(DateUtils.addDay(endDate, 1)) && "O".equals(status)) {
                        date = (new Date());
                    } else {
                        date = (glPeriodOpen.get(0).getEndDate());
                    }
                }
            }
            dto.setWriteOffDate(date);
            dto.setWriteOffBy(SystemContext.getUserId());
            dto.setGlDate(dto.getWriteOffDate());
            dto.setInvoiceWfStatus(WriteOffEnum.NON_WRITE_OFF.getCode());//未核销
            dto.setErpStatus(WriteOffEnum.ERP_PENDING.getCode());//同步中
            dto.setDeletedFlag(0);
            writeOffInvoiceRelMapper.insertSelective(dto);

            ResendExecute info = new ResendExecute();
            info.setBusinessType(BusinessTypeEnums.WRITEOFF_INVOICE.getCode());
            info.setApplyNo(String.valueOf(dto.getId()));
            info.setSubApplyNo(null);
            info.setVersion(null);
            info.setDeletedFlag(Boolean.FALSE);
            info.setBatch(false);
            infoList.add(info);
        }
        HandleDispatcher.batchRoute(infoList);
    }

    /**
     * 核销（推送erp）
     *
     * @param dtoList
     */
    @Override
    public EsbResponse writeOff(List<ReceiptClaimInvoiceRelDto> dtoList) {
        if (ListUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }
        Map<Long, List<ReceiptClaimInvoiceRelDto>> map =
                dtoList.stream().collect(Collectors.groupingBy(dto -> dto.getReceiptClaimDetailId()));

        Set<Map.Entry<Long, List<ReceiptClaimInvoiceRelDto>>> entries = map.entrySet();
        List<WriteOffDto> writeOffDtoList = new ArrayList<>();
        for (Map.Entry<Long, List<ReceiptClaimInvoiceRelDto>> entry : entries) {

            WriteOffDto paramDto = new WriteOffDto();
            paramDto.setPageNum(1);
            paramDto.setPageSize(1);
            paramDto.setId(entry.getKey());

            WriteOffDto writeOffDto = this.page(paramDto).getList().get(0);
       /* for (ReceiptClaimInvoiceRelDto dto : dtoList) {
            if (!WriteOffEnum.ERP_NOT_PUSH.getCode().equals(dto.getErpStatus()) &&
                    !WriteOffEnum.ERP_PUSH_FAILED.getCode().equals(dto.getErpStatus())) {
                throw new BizException(Code.ERROR, "勾选记录中存在ERP已核销数据，请检查再核销!");
            }
        }*/
            writeOffDto.setWriteOffInvoiceList(entry.getValue());
            //erp推送
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                writeOffDtoList.add(writeOffDto);
            }
        }
        return sdpService.callErpArApply(writeOffDtoList);
    }

    /**
     * 核销冲销（erp撤回）
     *
     * @param dtoList
     */
    @Override
    public EsbResponse writeOffReversal(List<ReceiptClaimInvoiceRelDto> dtoList) {
        if (ListUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }
        Map<Long, List<ReceiptClaimInvoiceRelDto>> map =
                dtoList.stream().collect(Collectors.groupingBy(dto -> dto.getReceiptClaimDetailId()));
        Set<Map.Entry<Long, List<ReceiptClaimInvoiceRelDto>>> entries = map.entrySet();
        List<WriteOffDto> writeOffDtoList = new ArrayList<>();
        for (Map.Entry<Long, List<ReceiptClaimInvoiceRelDto>> entry : entries) {
            WriteOffDto paramDto = new WriteOffDto();
            paramDto.setPageNum(1);
            paramDto.setPageSize(1);
            paramDto.setId(entry.getKey());
            WriteOffDto writeOffDto = this.page(paramDto).getList().get(0);
            writeOffDto.setWriteOffInvoiceList(entry.getValue());
            //erp撤回
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                writeOffDtoList.add(writeOffDto);
            }
        }
        return sdpService.callCuxArUnapplyApiPkgPortType(writeOffDtoList);
    }

    @Override
    public void pamResultReturnHandle(Long id, Boolean isSuccess, String msg) {
        ReceiptClaimInvoiceRel receiptClaimInvoiceRel = rcInvoiceRelMapper.selectByPrimaryKey(id);
        ReceiptClaimInvoiceRelDto dto = new ReceiptClaimInvoiceRelDto();
        dto.setId(id);
        if (isSuccess) {
            dto.setInvoiceWfStatus(WriteOffEnum.WRITTEN_OFF.getCode());
            dto.setErpStatus(WriteOffEnum.ERP_PUSHED.getCode());//推送成功
            dto.setErpMessage("");
        } else {
            dto.setErpStatus(WriteOffEnum.ERP_PUSH_FAILED.getCode());//推送失败
            dto.setInvoiceWfStatus(WriteOffEnum.NON_WRITE_OFF.getCode());
            dto.setErpMessage(msg);
        }
        //更新核销行状态
        this.save(dto);

        //更新核销头状态
        if (!ObjectUtils.isEmpty(receiptClaimInvoiceRel)) {
            ReceiptClaimInvoiceRelDto param = new ReceiptClaimInvoiceRelDto();
            param.setReceiptClaimDetailId(receiptClaimInvoiceRel.getReceiptClaimDetailId());
            param.setInvoiceWfStatus(WriteOffEnum.WRITTEN_OFF.getCode());//已核销
            List<ReceiptClaimInvoiceRelDto> invoiceRelDtoList = rcInvoiceRelExtMapper.getInvoicedList(param);
            BigDecimal writtenOffAmount = this.getWrittenOffAmount(invoiceRelDtoList);
            ReceiptClaimDetail detail = receiptClaimDetailMapper.selectByPrimaryKey(receiptClaimInvoiceRel.getReceiptClaimDetailId());

            if (!isSuccess) {
                // 更新头表数据为ERP同步更新失败
                detail.setInvoiceSyncStatus(ReceiptClaimEnum.INVOICE_SYNC_STATUS_FAILURE.getCode());
                receiptClaimDetailMapper.updateByPrimaryKeySelective(detail);
            } else {
                boolean failureFlag = false;
                for (ReceiptClaimInvoiceRelDto invoiceRelDto : invoiceRelDtoList) {
                    Integer erpStatus = invoiceRelDto.getErpStatus();
                    if (erpStatus != null && WriteOffEnum.ERP_PUSH_FAILED.getCode() == erpStatus) {
                        // 更新头表数据为ERP同步更新失败
                        detail.setInvoiceSyncStatus(ReceiptClaimEnum.INVOICE_SYNC_STATUS_FAILURE.getCode());
                        receiptClaimDetailMapper.updateByPrimaryKeySelective(detail);

                        failureFlag = true;
                        break;
                    }
                }

                if (!failureFlag) {
                    Integer invoiceSyncStatus = detail.getInvoiceSyncStatus();
                    if (invoiceSyncStatus == null || invoiceSyncStatus != ReceiptClaimEnum.INVOICE_SYNC_STATUS_SUCCESS.getCode()) {
                        // 更新头表数据为ERP同步更新失败
                        detail.setInvoiceSyncStatus(ReceiptClaimEnum.INVOICE_SYNC_STATUS_FAILURE.getCode());
                        receiptClaimDetailMapper.updateByPrimaryKeySelective(detail);
                    }
                }
            }
        }
    }

    @Override
    public int cancel(Long id) {
        ReceiptClaimInvoiceRel rel = new ReceiptClaimInvoiceRel();

        rel.setId(id);
        rel.setDeletedFlag(1);

        int i = rcInvoiceRelMapper.updateByPrimaryKeySelective(rel);

        //更新收款核销状态
        ReceiptClaimInvoiceRel receiptClaimInvoiceRel = rcInvoiceRelMapper.selectByPrimaryKey(id);
        if (receiptClaimInvoiceRel != null) {
            ReceiptClaimInvoiceRelDto param = new ReceiptClaimInvoiceRelDto();
            param.setReceiptClaimDetailId(receiptClaimInvoiceRel.getReceiptClaimDetailId());
            rcContractRelExtMapper.updateDetailStatus(param);
        }

        // 删除发票的已核销金额

        // 收款的已核销金额
        return i;
    }

    @Override
    public int redCancel(Long id) {
        WriteOffInvoiceRel rel = new WriteOffInvoiceRel();
        rel.setId(id);
        rel.setDeletedFlag(1);
        int i = writeOffInvoiceRelMapper.updateByPrimaryKeySelective(rel);
        return i;
    }

    @Override
    public int updateAccountingDate(Long id, Date accountingDate) {
        if (id == null) {
            throw new BizException(Code.ERROR, "单据号不能为空");
        }

        if (accountingDate == null) {
            throw new BizException(Code.ERROR, "入账日期不能为空");
        }

        ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(id);
        receiptClaimDetail.setAccountingDate(accountingDate);

        return receiptClaimDetailMapper.updateByPrimaryKeySelective(receiptClaimDetail);
    }

    @Override
    public Integer undo(ReceiptClaimInvoiceRel claimInvoiceRel) {
        claimInvoiceRel.setCancelBy(SystemContext.getUserId());
        claimInvoiceRel.setErpStatus(WriteOffEnum.ERP_REVERING.getCode());
        rcInvoiceRelMapper.updateByPrimaryKeySelective(claimInvoiceRel);

        claimInvoiceRel = rcInvoiceRelMapper.selectByPrimaryKey(claimInvoiceRel.getId());
        Long receiptClaimDetailId = claimInvoiceRel.getReceiptClaimDetailId();
        ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(receiptClaimDetailId);
        ReceiptClaim receiptClaim = receiptClaimMapper.selectByPrimaryKey(receiptClaimDetail.getReceiptClaimId());
        Long ouId = receiptClaim.getOuId();

        //更新收款核销状态
        ReceiptClaimInvoiceRelDto param = new ReceiptClaimInvoiceRelDto();
        param.setReceiptClaimDetailId(claimInvoiceRel.getReceiptClaimDetailId());
        rcContractRelExtMapper.updateDetailStatus(param);

        ResendExecute info = new ResendExecute();
        info.setBusinessType(BusinessTypeEnums.RECEIPT_CLAIM_CANCEL.getCode());
        info.setApplyNo(String.valueOf(claimInvoiceRel.getId()));
        info.setSubApplyNo(null);
        info.setVersion(null);
        info.setDeletedFlag(Boolean.FALSE);
        info.setBatch(true);
        info.setOuId(ouId);
        HandleDispatcher.route(info);

        return 1;
    }

    @Transactional
    @Override
    public void autoWriteOff() {
        /**
         * 查询所有符合自动核销的收款
         * 前提条件：合同币种与回款币种一致
         *
         */
        List<ReceiptClaimContractRelDto> receiptClaimContractRelDtoList = rcContractRelExtMapper.findCanAutoReceiptClaim();
        if (ListUtils.isEmpty(receiptClaimContractRelDtoList)) {
            return;
        }
        //查询所有有效组织代码
        List<OrganizationRel> organizationList = basedataExtService.getValidOrganization();
        Map<Long, Long> ouToLedgerIdMap = organizationList.stream().collect(Collectors.toMap(OrganizationRel::getOperatingUnitId, OrganizationRel::getLedgerId, (a, b) -> a));

        List<ResendExecute> infoList = new ArrayList<>();
        for (ReceiptClaimContractRelDto receiptClaimContractRelDto : receiptClaimContractRelDtoList) {
            /** 符合自动核销的应收发票
             *  按照应收发票invoice_receivable的总账日期gl_date，日期较早的先进行核销
             */
            Long receiptClaimDetailId = receiptClaimContractRelDto.getReceiptClaimDetailId();
            Long contractId = receiptClaimContractRelDto.getContractId();
            Long ouId = receiptClaimContractRelDto.getOuId();
            Long ledgerId = ouToLedgerIdMap.get(ouId);
            if (Objects.isNull(ledgerId)) {
                continue;
            }
            //获取核销日期并判断会计期间有没有打开
            Date date = getWriteOffDateByOuId(ledgerId);
            if (Objects.isNull(date)) {
//                throw new BizException(Code.ERROR, "erp分类账为" + ledgerId + "，期间类型为" + GlPeriodType.RECEIVABLES_PERIOD.getName() + "【会计期间未打开】");
                continue;
            }
            BigDecimal remainingContractAmount = receiptClaimContractRelDto.getRemainingContractAmount();//回款分配金额-回款已核销金额
            List<InvoiceReceivableDto> invoiceReceivableDtos = invoiceReceivableExtMapper.getListByClaimDetailId(receiptClaimDetailId, contractId, null);
            if (ListUtils.isNotEmpty(invoiceReceivableDtos)) {
                //构建核销关系
                for (InvoiceReceivableDto invoiceReceivableDto : invoiceReceivableDtos) {
                    String receiptClaimDetailIds = invoiceReceivableDto.getReceiptClaimDetailIds();
                    //收款ID与发票ID存在核销关系，不能再构建
                    if (StringUtils.isNotBlank(receiptClaimDetailIds)) {
                        Set<Long> receiptClaimDetailIdList = Arrays.stream(receiptClaimDetailIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
                        if (receiptClaimDetailIdList.contains(receiptClaimDetailId)) {
                            continue;
                        }
                    }
                    if (BigDecimalUtils.isEquals(remainingContractAmount, BigDecimal.ZERO)) {
                        break;
                    }
                    ReceiptClaimInvoiceRel rel = new ReceiptClaimInvoiceRel();
                    //应收发票可核销额
                    BigDecimal remainingPrice = invoiceReceivableDto.getTaxIncludedPrice().subtract(invoiceReceivableDto.getHasAmount());
                    // 将回款金额分配到回款计划行
                    if (remainingContractAmount.compareTo(remainingPrice) >= 0) {
                        rel.setWriteOffAmount(remainingPrice);
                        rel.setReceiptWriteOffAmount(remainingPrice);
                        remainingContractAmount = remainingContractAmount.subtract(remainingPrice);
                    } else {
                        rel.setWriteOffAmount(remainingContractAmount);
                        rel.setReceiptWriteOffAmount(remainingContractAmount);
                        remainingContractAmount = BigDecimal.ZERO;
                    }
                    rel.setReceiptClaimDetailId(receiptClaimDetailId);
                    rel.setInvoiceId(invoiceReceivableDto.getId());
                    rel.setWriteOffDate(date);
                    rel.setInvoiceWfStatus(WriteOffEnum.NON_WRITE_OFF.getCode());//未核销
                    rel.setErpStatus(WriteOffEnum.ERP_PENDING.getCode());//同步中
                    rel.setGlDate(date);
                    rel.setWriteOffBy(Constants.ADMIM);
                    rel.setDeletedFlag(0);
                    rcInvoiceRelMapper.insertSelective(rel);

                    ResendExecute info = new ResendExecute();
                    info.setBusinessType(BusinessTypeEnums.RECEIPT_CLAIM_WRITEOFF.getCode());
                    info.setApplyNo(String.valueOf(rel.getId()));
                    info.setSubApplyNo(null);
                    info.setVersion(null);
                    info.setDeletedFlag(Boolean.FALSE);
                    info.setBatch(true);
                    info.setOuId(ouId);
                    infoList.add(info);
                }
                //更新收款核销状态
                ReceiptClaimInvoiceRelDto param = new ReceiptClaimInvoiceRelDto();
                param.setReceiptClaimDetailId(receiptClaimDetailId);
                rcContractRelExtMapper.updateDetailStatus(param);
            }
        }
        if (CollectionUtils.isNotEmpty(infoList)) {
            HandleDispatcher.batchRoute(infoList);
        }
    }

    private Date getWriteOffDateByOuId(Long ledgerId) {
        Date date = null;
        String periodName = DateUtils.format(new Date(), "yyyy-MM");
        List<GlPeriodDto> glPeriodDtoList = basedataExtService.getGlPeriod(ledgerId, GlPeriodType.RECEIVABLES_PERIOD.getCode().toString(), periodName);
        List<GlPeriodDto> glPeriodOpenList = basedataExtService.getGlPeriod2(ledgerId, GlPeriodType.RECEIVABLES_PERIOD.getCode().toString(), "O");
        GlPeriodDto glPeriodOpen = null;
        if (CollectionUtils.isNotEmpty(glPeriodOpenList)) {
            glPeriodOpenList.sort(Comparator.comparing(GlPeriodDto::getEndDate).reversed());
            glPeriodOpen = glPeriodOpenList.get(0);
        }
        if (ListUtils.isNotEmpty(glPeriodDtoList)) {
            GlPeriodDto dto = glPeriodDtoList.get(0);
            if (dto.getPeriodName().equals(DateUtils.format(new Date(), "yyyy-MM"))) {
                String status = dto.getClosingStatus();
                Date startDate = dto.getStartDate();
                Date endDate = dto.getEndDate();
                if ((new Date()).after(startDate) && (new Date()).before(DateUtils.addDay(endDate, 1)) && "O".equals(status)) {
                    date = DateUtils.parse(DateUtils.format(new Date(), "yyyy-MM-dd 00:00:00"));
                } else {
                    date = Optional.ofNullable(glPeriodOpen).map(GlPeriodDto::getEndDate).orElse(null);
                }
            } else {
                date = Optional.ofNullable(glPeriodOpen).map(GlPeriodDto::getEndDate).orElse(null);
            }
        } else {
            date = Optional.ofNullable(glPeriodOpen).map(GlPeriodDto::getEndDate).orElse(null);
        }
        return date;
    }

    @Override
    public void autoWriteOffInvoice() {
        //查询未核销的发票
        List<WriteOffInvoiceRelDto> list = rcInvoiceRelExtMapper.getWriteOffInvoiceList();
        if (ListUtils.isNotEmpty(list)) {
            List<ResendExecute> infoList = new ArrayList<>();
            for (WriteOffInvoiceRelDto writeOffInvoiceRelDto : list) {
                OrganizationRelQuery query = new OrganizationRelQuery();
                query.setOperatingUnitId(writeOffInvoiceRelDto.getOuId());
                List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
                if (ListUtils.isEmpty(organizationRelList)) {
                    throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
                }
                OrganizationRelDto organizationRelDto = organizationRelList.get(0);
                Long ledgerId = organizationRelDto.getLedgerId();
                List<GlPeriodDto> glPeriod = basedataExtService.getGlPeriod(ledgerId, GlPeriodType.RECEIVABLES_PERIOD.getCode().toString(), null);
                List<GlPeriodDto> glPeriodOpen = basedataExtService.getGlPeriod2(ledgerId, GlPeriodType.RECEIVABLES_PERIOD.getCode().toString(), "O");
                Date date = null;
                glPeriodOpen.sort(Comparator.comparing(GlPeriodDto::getEndDate).reversed());
                if (ListUtils.isNotEmpty(glPeriod)) {
                    for (GlPeriodDto glPeriodDto : glPeriod) {
                        String status = glPeriodDto.getClosingStatus();
                        Date startDate = glPeriodDto.getStartDate();
                        Date endDate = glPeriodDto.getEndDate();
                        if ((new Date()).after(startDate) && (new Date()).before(DateUtils.addDay(endDate, 1)) && "O".equals(status)) {
                            date = (new Date());
                        } else {
                            date = (glPeriodOpen.get(0).getEndDate());
                        }
                    }
                }
                writeOffInvoiceRelDto.setWriteOffDate(date);
                writeOffInvoiceRelDto.setGlDate(writeOffInvoiceRelDto.getWriteOffDate());
                writeOffInvoiceRelDto.setInvoiceWfStatus(WriteOffEnum.NON_WRITE_OFF.getCode());//核销状态
                writeOffInvoiceRelDto.setErpStatus(WriteOffEnum.ERP_PENDING.getCode());//erp状态
                writeOffInvoiceRelDto.setWriteOffBy(Constants.ADMIM);
                writeOffInvoiceRelDto.setDeletedFlag(0);
                writeOffInvoiceRelMapper.insertSelective(writeOffInvoiceRelDto);

                ResendExecute info = new ResendExecute();
                info.setBusinessType(BusinessTypeEnums.WRITEOFF_INVOICE.getCode());
                info.setApplyNo(String.valueOf(writeOffInvoiceRelDto.getId()));
                info.setSubApplyNo(null);
                info.setVersion(null);
                info.setDeletedFlag(Boolean.FALSE);
                info.setBatch(false);
                infoList.add(info);
            }
            HandleDispatcher.batchRoute(infoList);
        }
    }


    @Override
    @Transactional
    public void sendNoticeEmail(List<ReceiptClaimContractRelDto> claimContractRelDtos, Boolean isDelete) {
        List<Map<Long, List<ReceiptClaimContractRelDto>>> mapList = new ArrayList<>();
        Map<Long, List<ReceiptClaimContractRelDto>> map1
                = claimContractRelDtos.stream().collect(Collectors.groupingBy(dto -> dto.getManagerId() == null ? -1L : dto.getManagerId()));

        Map<Long, List<ReceiptClaimContractRelDto>> map2
                = claimContractRelDtos.stream().collect(Collectors.groupingBy(dto -> dto.getSalesManagerId() == null ? -1L : dto.getSalesManagerId()));

        mapList.add(map1);
        mapList.add(map2);

        List<Email> emails = convertToEmail(mapList, isDelete);
        logger.info("回款认领到合同/删除合同提醒邮件待推送数据总数：{}", emails.size());
        // 邮件推送
        noticeService.sendMail(emails);

        // 待办通知
        List<Backlog> backlogs = convertToBacklog(mapList, isDelete);
        logger.info("回款认领到合同/删除合同提醒邮件待推送数据总数：{}", emails.size());
        // 待办推送
        noticeService.sendBacklog(backlogs);
    }

    @Override
    public List<WriteOffInvoiceRelDto> getRedDashedInvoicedList(Long invoiceId) {
        List<WriteOffInvoiceRelDto> dtoList = rcInvoiceRelExtMapper.getRedDashedInvoicedList(invoiceId);
        for (WriteOffInvoiceRelDto dto : dtoList) {
            //核销人
            if (!ObjectUtils.isEmpty(dto.getWriteOffBy())) {
                UserInfo user = CacheDataUtils.findUserById(dto.getWriteOffBy());
                if (!ObjectUtils.isEmpty(user)) {
                    dto.setWriteOffByName(user.getName());
                }
            }
        }
        return dtoList;
    }


    private List<Backlog> convertToBacklog(List<Map<Long, List<ReceiptClaimContractRelDto>>> maplist, Boolean isDelete) {
        List<Backlog> backlogs = new ArrayList<>();

        for (Map<Long, List<ReceiptClaimContractRelDto>> map : maplist) {

            if (map == null || map.size() == 0) {
                return backlogs;
            }
            Set<Map.Entry<Long, List<ReceiptClaimContractRelDto>>> entries = map.entrySet();
            for (Map.Entry<Long, List<ReceiptClaimContractRelDto>> entry : entries) {
                Long userId = entry.getKey();
                List<ReceiptClaimContractRelDto> list = entry.getValue();

                if (userId == null) {
                    continue;
                }
                for (ReceiptClaimContractRelDto dto : list) {

                    Backlog backlog = new Backlog();

                    backlog.setSubject(isDelete ?
                            dto.getParentContractName() + ",本次撤销金额:" + dto.getThisAmount()
                            : dto.getParentContractName() + ",本次回款金额:" + dto.getThisAmount());
                    backlog.setMobileFlag(Boolean.FALSE);
                    backlog.setDeletedFlag(Boolean.FALSE);
                    backlog.setStatus(EmailStatus.TO_DO.getCode());
                    UserInfo userInfo = CacheDataUtils.findUserById(userId);
                    if (userInfo == null) {
                        continue;
                    }
                    backlog.setReceiver(userInfo.getUsername());
                    backlog.setLongSubject(isDelete ?
                            dto.getParentContractName() + ",本次撤销金额:" + dto.getThisAmount()
                            : dto.getParentContractName() + ",本次回款金额:" + dto.getThisAmount());
                    backlog.setBusinessType(isDelete ?
                            NoticeBusinessType.DELETED_RECEIPT_CLAIM_CONTRACT.getType()
                            : NoticeBusinessType.RECEIPT_CLAIM_CONTRACT.getType());
                    backlog.setDeletedFlag(Boolean.FALSE);
                    backlog.setStatus(EmailStatus.TO_DO.getCode());
                    backlogs.add(backlog);
                }

            }
        }


        return backlogs;
    }

    private List<Email> convertToEmail(List<Map<Long, List<ReceiptClaimContractRelDto>>> mapList, Boolean isDelete) {
        List<Email> emails = new ArrayList<>();
        for (Map<Long, List<ReceiptClaimContractRelDto>> map : mapList) {
            if (map == null || map.size() == 0) {
                return emails;
            }
            Set<Map.Entry<Long, List<ReceiptClaimContractRelDto>>> entries = map.entrySet();
            for (Map.Entry<Long, List<ReceiptClaimContractRelDto>> entry : entries) {
                Long userId = entry.getKey();
                List<ReceiptClaimContractRelDto> list = entry.getValue();
                if (userId == null) {
                    continue;
                }

                Email email = new Email();
                email.setSubject(isDelete ? "PAM系统回款认领删除合同提醒" : "PAM系统回款认领到合同提醒");
                email.setLanguage("zh-CN");
                UserInfo userInfo = CacheDataUtils.findUserById(userId);
                if (userInfo == null) {
                    continue;
                }
                email.setReceiver(userInfo.getUsername());
                String content = isDelete ? buildDeletedContent(list) : buildContent(list);
                email.setContent(content);
                email.setBusinessType(isDelete ?
                        NoticeBusinessType.DELETED_RECEIPT_CLAIM_CONTRACT.getType()
                        : NoticeBusinessType.RECEIPT_CLAIM_CONTRACT.getType());
                email.setDeletedFlag(Boolean.FALSE);
                email.setStatus(EmailStatus.TO_DO.getCode());
                email.setFromAddress("pam");
                emails.add(email);
            }
        }
        return emails;
    }

    private String buildContent(List<ReceiptClaimContractRelDto> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        String header = "<div style='font-size: 12px;'>" + "您所负责的合同存在回款，请登录系统查阅详情" + "</div><br/>";

        String table = buildTable(list);
        sb.append(header);

        sb.append(table);
        sb.append("</html>");
        return sb.toString();
    }

    private String buildDeletedContent(List<ReceiptClaimContractRelDto> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        String header = "<div style='font-size: 12px;'>" + "您所负责的合同存在撤销回款。请登录系统查阅详情。" + "</div><br/>";

        String table = buildDeletedTable(list);
        sb.append(header);

        sb.append(table);
        sb.append("</html>");
        return sb.toString();
    }

    private String buildDeletedTable(List<ReceiptClaimContractRelDto> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("<table style='font-size: 12px;border-collapse:collapse;table-layout: fixed;word-wrap:break-word;width: 1000px;'>");
        sb.append("<tr>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:15%;'>序号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>项目编号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:70%;'>项目名称</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:100%;'>客户名称</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>本次撤销金额</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>到款日期</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:50%;'>业务分类</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>子合同编号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:70%;'>子合同名称</td>");
        sb.append("</tr>");
        int n = 1;
        for (ReceiptClaimContractRelDto dto : list) {
            sb.append("<tr>");
            String projectCode = dto.getProjectCode();
            String projectName = dto.getProjectName();
            String customerName = CacheDataUtils.findCustomerById(dto.getCustomerId()).getName();
            BigDecimal thisAmount = dto.getThisAmount();
            Date payDate = dto.getPayDate();
            String unitName = CacheDataUtils.findUnitById(dto.getProfitDepartmentId()).getUnitName();
            String contractCode = dto.getContractCode();
            String contractName = dto.getContractName();
            Long contractId = dto.getContractId();
            sb.append(buildTd(String.valueOf(n++)));
            sb.append(buildTd(projectCode));
            sb.append(buildTd(projectName));
            sb.append(buildTd(customerName));
            sb.append(buildTd(String.valueOf(thisAmount)));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sb.append(buildTd(sdf.format(payDate)));
            sb.append(buildTd(unitName));
            sb.append(buildTd(contractCode));
            sb.append(buildTdHref(contractName, contractUrl + contractId));
            sb.append("</tr>");
        }
        sb.append("</table>");
        return sb.toString();
    }


    private String buildTable(List<ReceiptClaimContractRelDto> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("<table style='font-size: 12px;border-collapse:collapse;table-layout: fixed;word-wrap:break-word;width: 1000px;'>");
        sb.append("<tr>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:15%;'>序号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>项目编号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:70%;'>项目名称</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:100%;'>客户名称</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>本次回款金额</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>累计回款金额</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>剩余待回款金额</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>到款日期</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:50%;'>业务分类</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>子合同编号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:70%;'>子合同名称</td>");
        sb.append("</tr>");
        int n = 1;
        for (ReceiptClaimContractRelDto dto : list) {
            sb.append("<tr>");
            Long contract = dto.getId();
            String projectCode = dto.getProjectCode();
            String projectName = dto.getProjectName();
            String customerName = CacheDataUtils.findCustomerById(dto.getCustomerId()).getName();
            BigDecimal thisAmount = dto.getThisAmount();
            BigDecimal totalAmount = dto.getTotalAmount();
            BigDecimal remainingContractAmount = dto.getRemainingContractAmount();
            Date payDate = dto.getPayDate();
            String unitName = CacheDataUtils.findUnitById(dto.getProfitDepartmentId()).getUnitName();
            String contractCode = dto.getContractCode();
            String contractName = dto.getContractName();
            Long contractId = dto.getContractId();
            sb.append(buildTd(String.valueOf(n++)));
            sb.append(buildTd(projectCode));
            sb.append(buildTd(projectName));
            sb.append(buildTd(customerName));
            sb.append(buildTd(String.valueOf(thisAmount)));
            sb.append(buildTd(String.valueOf(totalAmount)));
            sb.append(buildTd(String.valueOf(remainingContractAmount)));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sb.append(buildTd(sdf.format(payDate)));
            sb.append(buildTd(unitName));
            sb.append(buildTd(contractCode));
            sb.append(buildTdHref(contractName, contractUrl + contractId));
            sb.append("</tr>");
        }
        sb.append("</table>");
        return sb.toString();
    }

    private String buildTd(String value) {
        return "<td style='border: #999 1px solid; align:center;'>" + (value == null ? "" : value) + "</td>";
    }

    private String buildTdHref(String value, String url) {
        String urlStr = " <a href='" + url + "' target='_blank'>" + value + "</a>";
        return "<td style='border: #999 1px solid; align:center;'>" + urlStr + "</td>";
    }
}
