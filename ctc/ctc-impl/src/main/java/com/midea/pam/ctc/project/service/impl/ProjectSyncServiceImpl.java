package com.midea.pam.ctc.project.service.impl;

import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.entity.ProjectDetailSync;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.ext.service.impl.AbstractCommonBusinessService;
import com.midea.pam.ctc.mapper.ProjectDetailSyncMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.GEMSCarrierServicel;
import com.midea.pam.ctc.service.ProjectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/5/21
 * @description 项目预算是否可用信息同步
 */
public class ProjectSyncServiceImpl extends AbstractCommonBusinessService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectDetailSyncMapper projectDetailSyncMapper;
    @Resource
    private EsbService esbService;
    @Resource
    private ProjectService projectService;
    @Resource
    private GEMSCarrierServicel gemsCarrierServicel;


    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        String applyNo = resendExecute.getApplyNo();
        if (StringUtils.isNotEmpty(applyNo)) {
            ProjectDetailSync projectDetailSync = projectDetailSyncMapper.selectByPrimaryKey(Long.valueOf(applyNo));
            if (projectDetailSync != null) {
                ProjectDto project = projectService.findDetail(projectDetailSync.getProjectId());
                // 增加预算是否可用字段
     //           EsbResponse esbResponse = esbService.callGemsProjectNumberService(project, ProjectStatus.APPROVALED.getCode());
                EsbResponse esbResponse = gemsCarrierServicel.gemsProjectNumber(project, ProjectStatus.APPROVALED.getCode(), null);
                String responseCode = esbResponse.getResponsecode();
                if (Objects.equals(ResponseCodeEnums.SUCESS.getCode(), responseCode)) {
                    projectDetailSync.setSyncStatus(1);
                } else {
                    projectDetailSync.setSyncStatus(2);
                    projectDetailSync.setErrorMessage(esbResponse.getResponsemessage());
                }
                projectDetailSyncMapper.updateByPrimaryKey(projectDetailSync);
                return esbResponse;
            } else {
                logger.warn("项目预算是否可用信息同步:{}，未找到相关信息，同步失败", applyNo);
            }
        }

        return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), null, null);
    }

}
