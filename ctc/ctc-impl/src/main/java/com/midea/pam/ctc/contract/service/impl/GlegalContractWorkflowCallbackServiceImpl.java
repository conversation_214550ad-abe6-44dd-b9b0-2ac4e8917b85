package com.midea.pam.ctc.contract.service.impl;

import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.ContractChangewayDTO;
import com.midea.pam.common.ctc.dto.ContractHisDTO;
import com.midea.pam.common.ctc.dto.ContractProductDTO;
import com.midea.pam.common.ctc.dto.GlegalContractFileInfoDto;
import com.midea.pam.common.ctc.dto.InvoicePlanDTO;
import com.midea.pam.common.ctc.dto.InvoicePlanDetailDto;
import com.midea.pam.common.ctc.dto.InvoicePlanDetailHisDTO;
import com.midea.pam.common.ctc.dto.InvoicePlanHisDTO;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectProfitHisDTO;
import com.midea.pam.common.ctc.dto.ReceiptInvoiceRelationDTO;
import com.midea.pam.common.ctc.dto.ReceiptInvoiceRelationHisDTO;
import com.midea.pam.common.ctc.dto.ReceiptPlanDTO;
import com.midea.pam.common.ctc.dto.ReceiptPlanDetailDTO;
import com.midea.pam.common.ctc.dto.ReceiptPlanDetailHisDTO;
import com.midea.pam.common.ctc.dto.ReceiptPlanHisDTO;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.ContractChangeway;
import com.midea.pam.common.ctc.entity.ContractChangewayExample;
import com.midea.pam.common.ctc.entity.ContractExample;
import com.midea.pam.common.ctc.entity.ContractHis;
import com.midea.pam.common.ctc.entity.ContractHisExample;
import com.midea.pam.common.ctc.entity.ContractInventory;
import com.midea.pam.common.ctc.entity.ContractInventoryHis;
import com.midea.pam.common.ctc.entity.ContractInventoryHisExample;
import com.midea.pam.common.ctc.entity.ContractOrigin;
import com.midea.pam.common.ctc.entity.ContractProduct;
import com.midea.pam.common.ctc.entity.ContractProductHis;
import com.midea.pam.common.ctc.entity.ContractProductHisExample;
import com.midea.pam.common.ctc.entity.InvoicePlan;
import com.midea.pam.common.ctc.entity.InvoicePlanDetail;
import com.midea.pam.common.ctc.entity.InvoicePlanDetailExample;
import com.midea.pam.common.ctc.entity.InvoicePlanDetailHis;
import com.midea.pam.common.ctc.entity.InvoicePlanDetailHisExample;
import com.midea.pam.common.ctc.entity.InvoicePlanHis;
import com.midea.pam.common.ctc.entity.InvoicePlanHisExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectContractRs;
import com.midea.pam.common.ctc.entity.ProjectContractRsExample;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectMilepostExample;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectProfitHis;
import com.midea.pam.common.ctc.entity.ProjectProfitHisExample;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ReceiptInvoiceRelation;
import com.midea.pam.common.ctc.entity.ReceiptInvoiceRelationHis;
import com.midea.pam.common.ctc.entity.ReceiptInvoiceRelationHisExample;
import com.midea.pam.common.ctc.entity.ReceiptPlan;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetail;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailExample;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailHis;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailHisExample;
import com.midea.pam.common.ctc.entity.ReceiptPlanHis;
import com.midea.pam.common.ctc.entity.ReceiptPlanHisExample;
import com.midea.pam.common.ctc.vo.ContractVO;
import com.midea.pam.common.enums.ContractChangewayStatus;
import com.midea.pam.common.enums.ContractStatus;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.gateway.entity.GlegalFileInfo;
import com.midea.pam.common.gateway.entity.GlegalFileInfoExample;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.ContractChangewayService;
import com.midea.pam.ctc.contract.service.ContractService;
import com.midea.pam.ctc.contract.service.GlegalContractWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.InvoicePlanService;
import com.midea.pam.ctc.eam.service.service.EamPurchaseInfoService;
import com.midea.pam.ctc.mapper.ContractChangewayMapper;
import com.midea.pam.ctc.mapper.ContractExtMapper;
import com.midea.pam.ctc.mapper.ContractHisExtMapper;
import com.midea.pam.ctc.mapper.ContractHisMapper;
import com.midea.pam.ctc.mapper.ContractInventoryExtMapper;
import com.midea.pam.ctc.mapper.ContractInventoryHisMapper;
import com.midea.pam.ctc.mapper.ContractInventoryMapper;
import com.midea.pam.ctc.mapper.ContractMapper;
import com.midea.pam.ctc.mapper.ContractOriginMapper;
import com.midea.pam.ctc.mapper.ContractProductHisMapper;
import com.midea.pam.ctc.mapper.ContractProductMapper;
import com.midea.pam.ctc.mapper.GlegalFileInfoExtMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailExtMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailHisMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailMapper;
import com.midea.pam.ctc.mapper.InvoicePlanHisMapper;
import com.midea.pam.ctc.mapper.InvoicePlanMapper;
import com.midea.pam.ctc.mapper.ProjectContractRsMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostMapper;
import com.midea.pam.ctc.mapper.ProjectProfitHisMapper;
import com.midea.pam.ctc.mapper.ProjectProfitMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.ReceiptInvoiceRelationHisMapper;
import com.midea.pam.ctc.mapper.ReceiptInvoiceRelationMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailHisMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanHisMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectContractRsService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-10-09
 * @description 法务法务销售合同流程业务回调
 */
public class GlegalContractWorkflowCallbackServiceImpl extends GlegalContractWorkflowCallbackService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private ContractService contractService;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private ContractHisMapper contractHisMapper;
    @Resource
    private ContractChangewayMapper contractChangewayMapper;
    @Resource
    private ContractChangewayService contractChangewayService;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private ProjectContractRsService projectContractRsService;
    @Resource
    private InvoicePlanMapper invoicePlanMapper;
    @Resource
    private ProjectContractRsMapper projectContractRsMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ContractProductMapper contractProductMapper;
    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;
    @Resource
    private InvoicePlanDetailExtMapper invoicePlanDetailExtMapper;
    @Resource
    private ReceiptPlanMapper receiptPlanMapper;
    @Resource
    private ReceiptPlanDetailMapper receiptPlanDetailMapper;
    @Resource
    private ReceiptInvoiceRelationMapper receiptInvoiceRelationMapper;
    @Resource
    private ProjectProfitMapper projectProfitMapper;
    @Resource
    private ProjectMilepostMapper projectMilepostMapper;
    @Resource
    private ContractProductHisMapper contractProductHisMapper;
    @Resource
    private InvoicePlanService invoicePlanService;
    @Resource
    private InvoicePlanHisMapper invoicePlanHisMapper;
    @Resource
    private InvoicePlanDetailHisMapper invoicePlanDetailHisMapper;
    @Resource
    private ReceiptPlanHisMapper receiptPlanHisMapper;
    @Resource
    private ReceiptPlanDetailHisMapper receiptPlanDetailHisMapper;
    @Resource
    private ReceiptInvoiceRelationHisMapper receiptInvoiceRelationHisMapper;
    @Resource
    private ProjectProfitHisMapper projectProfitHisMapper;
    @Resource
    private ContractHisExtMapper contractHisExtMapper;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private EamPurchaseInfoService eamPurchaseInfoService;
    @Resource
    private ProjectTypeMapper projectTypeMapper;
    @Resource
    private GlegalFileInfoExtMapper glegalFileInfoExtMapper;
    @Resource
    private ReceiptPlanDetailExtMapper receiptPlanDetailExtMapper;
    @Resource
    private ContractExtMapper contractExtMapper;
    @Resource
    private ContractOriginMapper contractOriginMapper;
    @Resource
    private ContractInventoryMapper contractInventoryMapper;
    @Resource
    private ContractInventoryHisMapper contractInventoryHisMapper;
    @Resource
    private ContractInventoryExtMapper contractInventoryExtMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectProfitService projectProfitService;

    // 变更类型 1->基础信息变更 2->服务内容变更 3->开票回款计划变更
    private final static Integer CHANGE_TYPE_ONE = 1;
    private final static Integer CHANGE_TYPE_TWO = 2;

    //变更状态 1->审批中 2->驳回 3->审批通过 4->撤回 5->作废 6->删除
    private final static Integer APPROVAL_TYPE_ONE = ContractChangewayStatus.PENDING.getCode();
    private final static Integer APPROVAL_TYPE_TWO = ContractChangewayStatus.REFUSE.getCode();
    private final static Integer APPROVAL_TYPE_THREE = ContractChangewayStatus.APPROVED.getCode();
    private final static Integer APPROVAL_TYPE_FOURE = ContractChangewayStatus.WITHDRAW.getCode();
    private final static Integer APPROVAL_TYPE_FIVE = ContractChangewayStatus.ABANDON.getCode();
    private final static Integer APPROVAL_TYPE_SIX = ContractChangewayStatus.DELETE.getCode();

    //法务销售合同审批状态
    private final static Integer FLOW_STATE_CHANGING = ContractStatus.CHANGING.getCode();


    @Override
    public void draftSubmit(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        logger.info("法务销售合同审批提交审批回调 formInstanceId:{}, handlerId:{}", glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode());
        if (StringUtils.isEmpty(glegalContractFileInfoDto.getBusinessId())) {
            logger.error("法务销售合同审批提交审批回调 formInstanceId为空，不处理");
            return;
        }

        final Contract contract = contractService.findById(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
        if (contract == null) {
            logger.error("法务销售合同审批提交审批回调 formInstanceId对应法务销售合同不存在，不处理");
            return;
        }

        contract.setStatus(ContractStatus.PENDING.getCode());
        contractService.update(contract);

        final List<ContractVO> childContracts = contractService.getListByParentId(contract.getId());
        if (CollectionUtils.isNotEmpty(childContracts)) {
            childContracts.forEach(contractVO -> {
                Contract child = new Contract();
                BeanUtils.copyProperties(contractVO, child);

                child.setStatus(ContractStatus.PENDING.getCode());
                contractService.update(child);
            });
        }
        if (null != contract) {
            insertFileInfo(glegalContractFileInfoDto.getFileInfos(), contract);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pass(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String seqPerfix = basedataExtService.getUnitSeqPerfix(glegalContractFileInfoDto.getCompanyId());

        logger.info("法务销售合同审批通过回调 formInstanceId:{}, handlerId:{}, filingDate:{}", glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), glegalContractFileInfoDto.getFilingDate());
        if (StringUtils.isEmpty(glegalContractFileInfoDto.getBusinessId())) {
            logger.error("法务销售合同审批通过回调 formInstanceId为空，不处理");
            return;
        }

        final Contract contract = contractService.findById(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
        if (contract == null) {
            logger.error("法务销售合同审批通过回调 formInstanceId对应法务销售合同不存在，不处理");
            return;
        }
        //承接人赋值
        UserInfo userinfo = CacheDataUtils.findUserByMip(glegalContractFileInfoDto.getReceiverCode());
        if (Objects.nonNull(userinfo) && Objects.nonNull(userinfo.getId()) ) {
            contract.setDrafter(userinfo.getId());
        }
        //如果合同状态是审核中，说明这个回调是审批通过回调。需要处理业务逻辑
        if (Objects.equals(contract.getStatus(), ContractStatus.PENDING.getCode())) {
            int status = ContractStatus.EFFECTIVE.getCode();
            contract.setStatus(status);
            Date filingDate = glegalContractFileInfoDto.getFilingDate();
            contract.setFilingDate(filingDate);
            contract.setApprovalDate(filingDate);
            //重复回调的话，先逻辑删除父合同和子合同的备份
            contractExtMapper.updateContractAndChildDelete(contract.getId());

            final List<ContractVO> childContracts = contractService.getListByParentId(contract.getId());
            if (CollectionUtils.isNotEmpty(childContracts)) {
                for (int i = 0; i < childContracts.size(); i++) {
                    ContractVO contractVO = childContracts.get(i);
                    if (StringUtils.isEmpty(contractVO.getCode())) {
                        // 生成子法务销售合同编号
                        final String childrenCode = seqPerfix + "SZ" + CacheDataUtils.generateSequence(4, seqPerfix + "SZ", DateUtil.DATE_YYMM_PATTERN);
                        contractVO.setCode(childrenCode);
                    }

                    Contract child = new Contract();
                    BeanUtils.copyProperties(contractVO, child);

                    int childStatus = ContractStatus.EFFECTIVE.getCode();
                    child.setFilingDate(filingDate);
                    child.setApprovalDate(filingDate);
                    child.setStatus(childStatus);
                    contractService.update(child);
                    //备份子合同初始版本
                    ContractOrigin childContractOrigin = contractService.packageContractOrigin(child, contract);
                    contractOriginMapper.insert(childContractOrigin);
                }
            }
            insertFileInfo(glegalContractFileInfoDto.getFileInfos(), contract);
        } else {
            //起草人交接处理 - 更新
            contractService.update(contract);
        }
    }

    @Override
    public void refuse(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        logger.info("法务销售合同审批驳回回调 formInstanceId:{}, handlerId:{}", glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode());
        if (StringUtils.isEmpty(glegalContractFileInfoDto.getBusinessId())) {
            logger.error("法务销售合同审批驳回回调 formInstanceId为空，不处理");
            return;
        }

        final Contract contract = contractService.findById(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
        if (contract == null) {
            logger.error("法务销售合同审批驳回回调 formInstanceId对应法务销售合同不存在，不处理");
            return;
        }

        contract.setStatus(ContractStatus.REFUSE.getCode());
        contractService.update(contract);

        final List<ContractVO> childContracts = contractService.getListByParentId(contract.getId());
        if (CollectionUtils.isNotEmpty(childContracts)) {
            childContracts.forEach(contractVO -> {
                Contract child = new Contract();
                BeanUtils.copyProperties(contractVO, child);

                child.setStatus(ContractStatus.REFUSE.getCode());
                contractService.update(child);
            });
        }
        insertFileInfo(glegalContractFileInfoDto.getFileInfos(), contract);
    }

    @Override
    public void abandon(GlegalContractFileInfoDto glegalContractFileInfoDto, ContractStatus contractStatus) {
        changeContractFlow(glegalContractFileInfoDto.getBusinessId(), contractStatus);
    }


    @Override
    public void draftReturn(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        logger.info("法务销售合同审批撤回回调 formInstanceId:{}, handlerId:{}", glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode());
        if (StringUtils.isEmpty(glegalContractFileInfoDto.getBusinessId())) {
            logger.error("法务销售合同审批撤回回调 formInstanceId为空，不处理");
            return;
        }

        final Contract contract = contractService.findById(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
        if (contract == null) {
            logger.error("法务销售合同审批撤回回调 formInstanceId对应法务销售合同不存在，不处理");
            return;
        }
        contract.setStatus(ContractStatus.DRAFT.getCode());
        contractService.update(contract);

        final List<ContractVO> childContracts = contractService.getListByParentId(contract.getId());
        if (CollectionUtils.isNotEmpty(childContracts)) {
            childContracts.forEach(contractVO -> {
                Contract child = new Contract();
                BeanUtils.copyProperties(contractVO, child);

                child.setStatus(ContractStatus.DRAFT.getCode());
                contractService.update(child);
            });
        }
        insertFileInfo(glegalContractFileInfoDto.getFileInfos(), contract);
    }

    @Override
    public void draftChangeSubmit(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        flow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), CHANGE_TYPE_ONE, APPROVAL_TYPE_ONE, FLOW_STATE_CHANGING, glegalContractFileInfoDto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void passChange(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        //String seqPerfix = basedataExtService.getUnitSeqPerfix(companyId);
        logger.info("法务销售合同变更审批通过回调 formInstanceId:{}, handlerId:{}", glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode());
        if (StringUtils.isEmpty(glegalContractFileInfoDto.getBusinessId())) {
            logger.error("法务销售合同变更审批通过回调 formInstanceId为空，不处理");
            return;
        }
        /**
         * 审批通过之后 contract相关数据 和 contractHis 互换 数据互换开始.....
         */
        //取该法务销售合同变更前后的数据
        ContractChangewayDTO contractChangewayDTO = contractChangewayService.getChangeHisDetails(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
        //防止重复回调
        if (Objects.equals(contractChangewayDTO.getApprovalStatus(), ContractChangewayStatus.APPROVED.getCode())) {
            return;
        }
        Boolean isNullContractHisDTO = ObjectUtils.isEmpty(contractChangewayDTO.getContractHisDTO());
        Boolean isNullContractVO = ObjectUtils.isEmpty(contractChangewayDTO.getContractVO());
        if (isNullContractHisDTO && isNullContractVO) {
            return;
        } else {
            ContractChangeway contractChangeway = contractChangewayDTO.convertToContractChangeway();
            //法务销售合同变更前的数据
            ContractVO contractVO = contractChangewayDTO.getContractVO();
            Date createAt = contractVO.getCreateAt();
            Long createBy = contractVO.getCreateBy();
            Date approvalDate = contractVO.getApprovalDate();
            //法务销售合同变更后的数据
            ContractHisDTO contractHisDTO = contractChangewayDTO.getContractHisDTO();

            //判断变更类型
            if (contractChangeway.getChangeType() == 1) {
                /**
                 *  ---------------基础信息变更时，数据产生变动主要是contract表，开始修改法务销售合同表数据 ---------------
                 */
                //更新变更记录审批状态
                ContractChangeway contractChangeway1 = contractChangewayMapper.selectByPrimaryKey(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                contractChangeway1.setApprovalStatus(ContractChangewayStatus.APPROVED.getCode());
                contractChangeway1.setUpdateAt(new Date());
                //List<String> ids = glegalContractFileInfoDto.getFileInfos().stream().map(GlegalContractFileInfoDto.FileInfos::getId).collect(Collectors.toList());
                List<String> ids = glegalContractFileInfoDto.getFileInfos().stream().filter(i -> !i.getDeleteFlag() && ("StampArchive".equals(i.getFileSign()) || "ChangeProtocol".equals(i.getFileSign()))).map(GlegalContractFileInfoDto.FileInfos::getId).collect(Collectors.toList());
                String ifUploadChangeFile = String.join(",", ids);
                contractChangeway1.setChangeProtocolFile(ifUploadChangeFile);
                contractChangewayMapper.updateByPrimaryKey(contractChangeway1);

                Contract contract = new Contract();
                ContractHis contractHis = contractHisDTO.convertToContractHis();
                BeanUtils.copyProperties(contractHis, contract);
                contract.setId(contractHis.getContractId());
                Date now = new Date();
                int status = ContractStatus.EFFECTIVE.getCode();
                contract.setStatus(status);
                contract.setCreateAt(createAt);// 避免创建时间被覆盖
                contract.setCreateBy(createBy);
                contract.setApprovalDate(approvalDate);
//                contract.setFilingDate(now);
                contractMapper.updateByPrimaryKey(contract);
                //更新子法务销售合同上面的业务实体ID
                updateChildContractOuId(contract.getId(), contract.getOuId());
                logger.info("主法务销售合同基础变更审批已经通过，数据已经更改，" + contract.toString());
                List<ContractHisDTO> childrenContractHisDTOS = contractHisDTO.getChildrenContractHiss();
                List<ContractVO> contractVOInvList = contractVO.getChildrenContracts();
                Map<Long, ContractVO> oldContractVOMap = contractVOInvList.stream().collect(Collectors.toMap(ContractVO::getId, Function.identity(), (key1, key2) -> key2));
                if (CollectionUtils.isNotEmpty(childrenContractHisDTOS) && childrenContractHisDTOS.size() > 0) {
                    for (ContractHisDTO childrenContractHisDTO : childrenContractHisDTOS) {
                        Date afterStartTime = childrenContractHisDTO.getStartTime();
                        Date afterEndTime = childrenContractHisDTO.getEndTime() == null ? contractHisDTO.getEndTime() : childrenContractHisDTO.getEndTime();

                        // 收入成本计划变更
                        changeIncomeCostPlans(childrenContractHisDTO.getContractId(), childrenContractHisDTO.getExcludingTaxAmount(), afterStartTime, afterEndTime, null, null);

                        Contract childrenContract = new Contract();
                        ContractHis childrenContractHis = childrenContractHisDTO.convertToContractHis();
                        BeanUtils.copyProperties(childrenContractHis, childrenContract);
                        ContractVO oldContract = oldContractVOMap.get(childrenContractHis.getContractId());
                        if (oldContract != null) {
                            childrenContract.setCreateBy(oldContract.getCreateBy());
                            childrenContract.setCreateAt(oldContract.getCreateAt());
                            childrenContract.setApprovalDate(oldContract.getApprovalDate());
                            childrenContract.setFilingDate(oldContract.getFilingDate());
                        }
                        childrenContract.setId(childrenContractHis.getContractId());
                        int childStatus = ContractStatus.EFFECTIVE.getCode();

                        childrenContract.setOuId(contract.getOuId());
                        childrenContract.setStatus(childStatus);

                        // 主法务销售合同可以可能发生变更
                        /*childrenContract.setCustomerName(contract.getCustomerName());
                        childrenContract.setCustomerCode(contract.getCustomerCode());
                        childrenContract.setCustomerId(contract.getCustomerId());*/

                        childrenContract.setCustomerName(childrenContractHisDTO.getCustomerName());
                        childrenContract.setCustomerCode(childrenContractHisDTO.getCustomerCode());
                        childrenContract.setCustomerId(childrenContractHisDTO.getCustomerId());

                        //如果子法务销售合同变更过客户，那么需要更新开票计划上的开票客户
                        if (contractVOInvList != null) {
                            for (int i = 0; i < contractVOInvList.size(); i++) {
                                if (childrenContractHisDTO.getContractId().equals(contractVOInvList.get(i).getId())) {
                                    if (!childrenContractHisDTO.getCustomerId().equals(contractVOInvList.get(i).getCustomerId())) {
                                        InvoicePlan invoicePlan = new InvoicePlan();
                                        invoicePlan.setContractId(childrenContractHisDTO.getContractId());
                                        invoicePlan.setCustomerId(childrenContractHisDTO.getCustomerId());
                                        invoicePlan.setCustomerName(childrenContractHisDTO.getCustomerName());
                                        invoicePlanMapper.updateByClause(invoicePlan);

                                    }
                                }
                            }
                        }
                        contractMapper.updateByPrimaryKey(childrenContract);
                        logger.info("子法务销售合同基础变更审批已经通过，数据已经更改，" + childrenContract.toString());

                        //取该法务销售合同关联的项目,现在默认一个法务销售合同对应一个项目，更新项目上的客户
                        ProjectContractRsExample projectContractRsExample = new ProjectContractRsExample();
                        ProjectContractRsExample.Criteria criteria = projectContractRsExample.createCriteria();
                        criteria.andContractIdEqualTo(childrenContractHis.getContractId());
                        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
                        List<ProjectContractRs> projectContractRsList = projectContractRsMapper.selectByExample(projectContractRsExample);
                        if (CollectionUtils.isNotEmpty(projectContractRsList)) {
                            Project project = new Project();
                            project.setId(projectContractRsList.get(0).getProjectId());
                            project.setCustomerId(childrenContractHis.getCustomerId());
                            project.setCustomerName(childrenContractHis.getCustomerName());
                            projectMapper.updateByPrimaryKeySelective(project);
                            logger.info("法务销售合同关联的项目上的客户已被更新，project" + project.toString());
                        }
                    }
                }
                /**
                 *  ---------------------------修改法务销售合同表数据结束-------------------------------
                 *
                 *  --------------------开始修改法务销售合同记录表，存放原先主法务销售合同的数据---------------------
                 */
                ContractHis contractHis1 = new ContractHis();
                Contract contract1 = contractVO.convertToContract();
                BeanUtils.copyProperties(contract1, contractHis1);
                contractHis1.setId(contractHisDTO.getId());
                contractHis1.setContractId(contract1.getId());
                contractHis1.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                contractHis1.setAttachId(contractHisDTO.getAttachId());
                contractHis1.setStatus(status);
                contractHisMapper.updateByPrimaryKey(contractHis1);
                logger.info("主法务销售合同基础变更审批已经通过，主法务销售合同数据已经被记录，" + contractHis1.toString());
                List<ContractVO> contractVOList = contractVO.getChildrenContracts();
                int size = contractVOList.size();
                for (int i = 0; i < size; i++) {
                    ContractHis childrenContractHis = new ContractHis();
                    ContractVO childrenContractVO = contractVOList.get(i);
                    Contract childrenContract = childrenContractVO.convertToContract();
                    BeanUtils.copyProperties(childrenContract, childrenContractHis);
                    childrenContractHis.setContractId(childrenContract.getId());
                    Long childId = childrenContract.getId();
                    Long changewayId = contractChangewayDTO.getId();
                    //找出子法务销售合同记录上的id
                    ContractHisExample contractHisExample = new ContractHisExample();
                    ContractHisExample.Criteria criteria = contractHisExample.createCriteria();
                    criteria.andContractIdEqualTo(childId);
                    criteria.andChangewayIdEqualTo(changewayId);
                    List<ContractHis> contractHisList = contractHisMapper.selectByExample(contractHisExample);
                    //对应的子法务销售合同记录
                    ContractHis contractHis2 = new ContractHis();
                    if (CollectionUtils.isNotEmpty(contractHisList)) {
                        contractHis2 = contractHisList.get(0);
                        childrenContractHis.setId(contractHis2.getId());
                    } else {
                        throw new MipException("查询子法务销售合同记录出错！");
                    }
                    childrenContractHis.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                    childrenContractHis.setStatus(status);
                    contractHisMapper.updateByPrimaryKey(childrenContractHis);

                }
                // 更新合同原件
                if (null != contract) {
                    insertFileInfo(glegalContractFileInfoDto.getFileInfos(), contract);
                }
                /**
                 * --------------------修改法务销售合同记录表，存放原先主法务销售合同的数据,逻辑结束---------------------
                 */
            }
        }
    }

    @Override
    public void refuseChange(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        int status = ContractStatus.EFFECTIVE.getCode();
        flow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), CHANGE_TYPE_ONE, APPROVAL_TYPE_TWO, status, glegalContractFileInfoDto);
    }

    @Override
    public void draftChangeReturn(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        int status = ContractStatus.EFFECTIVE.getCode();
        flow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), CHANGE_TYPE_ONE, APPROVAL_TYPE_FOURE, status, glegalContractFileInfoDto);
    }

    @Override
    public void abandonChange(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        int status = ContractStatus.EFFECTIVE.getCode();
        flow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), CHANGE_TYPE_ONE, APPROVAL_TYPE_FIVE, status, glegalContractFileInfoDto);
    }

    @Override
    public void draftServiceChangeSubmit(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        flow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), CHANGE_TYPE_TWO, APPROVAL_TYPE_ONE, FLOW_STATE_CHANGING, glegalContractFileInfoDto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void passServiceChange(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        logger.info("服务合同变更审批通过回调 formInstanceId:{}, handlerId:{}", glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode());
        if (StringUtils.isEmpty(glegalContractFileInfoDto.getBusinessId())) {
            logger.error("服务合同变更审批通过回调 formInstanceId为空，不处理");
            return;
        }
        /**
         * 审批通过之后 contract相关数据 和 contractHis 互换 数据互换开始.....
         */
        ContractChangewayDTO contractChangewayDTO = contractChangewayService.getChangeHisDetails(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
        //防止重复回调
        if (Objects.equals(contractChangewayDTO.getApprovalStatus(), ContractChangewayStatus.APPROVED.getCode())) {
            return;
        }
        Boolean isNullContractHisDTO = ObjectUtils.isEmpty(contractChangewayDTO.getContractHisDTO());
        Boolean isNullContractVO = ObjectUtils.isEmpty(contractChangewayDTO.getContractVO());

        if (isNullContractHisDTO && isNullContractVO) {
            throw new MipException("提交的数据不完整，请确认数据准确性！");
        } else {
            ContractChangeway contractChangeway = contractChangewayDTO.convertToContractChangeway();
            //合同变更前的数据
            ContractVO contractVO = contractChangewayDTO.getContractVO();
            //合同变更后的数据
            ContractHisDTO contractHisDTO = contractChangewayDTO.getContractHisDTO();
            //判断变更类型
            if (contractChangeway.getChangeType() == 2) {
                /**
                 *  ========================>>服务合同内容信息变更时，数据产生变动有产品金额，开票，回款等表，更新合同系列表<<========================
                 */
                List<ContractHisDTO> childrenContractHisDTOList = contractHisDTO.getChildrenContractHiss();
                //过滤出新增的子合同
                List<ContractHisDTO> childrencontractHisDTOsTypeAdd = childrenContractHisDTOList.stream().filter(c -> null != c.getOperationType() && c.getOperationType() == 1).collect(Collectors.toList());
                //过滤出删除的子合同
                List<ContractHisDTO> childrencontractHisDTOsTypeDel = childrenContractHisDTOList.stream().filter(c -> null != c.getOperationType() && c.getOperationType() == 2).collect(Collectors.toList());
                //只做过更新的合同
                List<ContractHisDTO> childrenContractHisDTOS = childrenContractHisDTOList.stream().filter(c -> c.getOperationType() == null).collect(Collectors.toList());
                // 原始合同
                List<ContractVO> contractVOInvList = contractVO.getChildrenContracts();
                //合同总额（含税）
                BigDecimal amount = BigDecimal.ZERO;
                //合同总额（不含税）
                BigDecimal excludingTaxAmount = BigDecimal.ZERO;
//                if (CollectionUtils.isNotEmpty(childrenContractHisDTOS)){
//                    for (ContractHisDTO childrenContractHisDTO : childrenContractHisDTOS) {
//                        amount = amount.add(childrenContractHisDTO.getAmount());
//                        excludingTaxAmount = excludingTaxAmount.add(childrenContractHisDTO.getExcludingTaxAmount());
//                    }
//                }
                ContractChangeway contractChangeway1 = contractChangewayMapper.selectByPrimaryKey(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                contractChangeway1.setApprovalStatus(ContractChangewayStatus.APPROVED.getCode());
                contractChangeway1.setUpdateAt(new Date());
                //只处理变更协议的附近qile 20231026
                List<String> ids = glegalContractFileInfoDto.getFileInfos().stream().filter(i -> !i.getDeleteFlag() && ("StampArchive".equals(i.getFileSign()) || "ChangeProtocol".equals(i.getFileSign()))).map(GlegalContractFileInfoDto.FileInfos::getId).collect(Collectors.toList());
                String ifUploadChangeFile = String.join(",", ids);
                contractChangeway1.setChangeProtocolFile(ifUploadChangeFile);
                contractChangewayMapper.updateByPrimaryKey(contractChangeway1);
                logger.info("服务合同变更审批已经通过，合同变更记录表状态已经更改，" + contractChangeway1.toString());

                //更新该主合同的审批状态（生效或者待生效）
                Contract contract = contractMapper.selectByPrimaryKey(contractVO.getId());
                Date now = new Date();
                int status = ContractStatus.EFFECTIVE.getCode();
                contract.setAmount(contractHisDTO.getAmount());
                contract.setExcludingTaxAmount(contractHisDTO.getExcludingTaxAmount());
                contract.setCustomerId(contractHisDTO.getCustomerId());
                contract.setCustomerName(contractHisDTO.getCustomerName());
                contract.setStartTime(contractHisDTO.getStartTime());
                contract.setEndTime(contractHisDTO.getEndTime());
                contract.setName(contractHisDTO.getName());
                contract.setStatus(status);
//                contract.setFilingDate(now);

                //新增子合同总额（含税）
                BigDecimal amountAdd = BigDecimal.ZERO;
                //新增子合同总额（不含税）
                BigDecimal excludingTaxAmountAdd = BigDecimal.ZERO;
                //新增子合同场景下his表与正式表的对应关系map
                Map<Long, Long> contractIdMappingByAddMap = new HashMap<>();

                Date filingDate = glegalContractFileInfoDto.getFilingDate();
                //将新增的子合同写入原始表中
                if (CollectionUtils.isNotEmpty(childrencontractHisDTOsTypeAdd)) {
                    Map<Long, String> childrenContractIdAddMap = new HashMap<>();
                    for (ContractHisDTO contractHisDTOTypeAdd : childrencontractHisDTOsTypeAdd) {
                        amountAdd = amountAdd.add(contractHisDTOTypeAdd.getAmount() == null ? BigDecimal.ZERO : contractHisDTOTypeAdd.getAmount());
                        excludingTaxAmountAdd = excludingTaxAmountAdd.add(contractHisDTOTypeAdd.getExcludingTaxAmount() == null ? BigDecimal.ZERO : contractHisDTOTypeAdd.getExcludingTaxAmount());
                        Contract contractSon = new Contract();
                        Long contractHisDTOTypeAddId = contractHisDTOTypeAdd.getId();
                        BeanConverter.copy(contractHisDTOTypeAdd, contractSon);
                        contractSon.setId(null);
                        contractSon.setFilingDate(filingDate);
                        contractSon.setApprovalDate(filingDate);
                        contractSon.setStatus(contract.getStatus());
                        contractMapper.insert(contractSon);

                        // 插入到合同正式表中的子合同id
                        Long contractSonId = contractSon.getId();
                        //需要维护的子合同和采购申请的数据
                        childrenContractIdAddMap.put(contractSonId, contractHisDTOTypeAdd.getEampurchaseId());
                        contractIdMappingByAddMap.put(contractHisDTOTypeAdd.getContractId(), contractSonId);

                        Map<String, Long> relationMap = new HashMap<>();

                        ContractProductHisExample contractProductHisExample = new ContractProductHisExample();
                        contractProductHisExample.createCriteria().andChangewayIdEqualTo(Long.valueOf(glegalContractFileInfoDto.getBusinessId())).andContractIdEqualTo(contractHisDTOTypeAddId);
                        List<ContractProductHis> contractProductHisList = contractProductHisMapper.selectByExample(contractProductHisExample);
                        if (CollectionUtils.isNotEmpty(contractProductHisList)) {
                            for (ContractProductHis contractProductHis : contractProductHisList) {
                                ContractProduct contractProduct = new ContractProduct();
                                BeanConverter.copy(contractProductHis, contractProduct);
                                contractProduct.setId(null);
                                contractProduct.setContractId(contractSonId);
                                contractProductMapper.insert(contractProduct);

                                Long contractProductId = contractProduct.getId();
                                String key = contractProduct.getContractId() + "-"
                                        + (contractProduct.getProductTypeName() == null ? "null" : contractProduct.getProductTypeName()) + "-"
                                        + (contractProduct.getTaxId() == null ? "null" : String.valueOf(contractProduct.getTaxId())) + "-"
                                        + (contractProduct.getProductId() == null ? "null" : String.valueOf(contractProduct.getProductId()));
                                relationMap.put(key, contractProductId);
                            }
                        }
                        InvoicePlanHisExample invoicePlanHisExample = new InvoicePlanHisExample();
                        invoicePlanHisExample.createCriteria().andChangewayIdEqualTo(Long.valueOf(glegalContractFileInfoDto.getBusinessId())).andContractIdEqualTo(contractHisDTOTypeAddId);
                        List<InvoicePlanHis> invoicePlanHisList = invoicePlanHisMapper.selectByExample(invoicePlanHisExample);
                        Map<Long, Long> invoicePlanDetailIdReMap = new HashMap<>();
                        if (CollectionUtils.isNotEmpty(invoicePlanHisList)) {
                            for (InvoicePlanHis invoicePlanHis : invoicePlanHisList) {
                                InvoicePlan invoicePlan = new InvoicePlan();
                                BeanConverter.copy(invoicePlanHis, invoicePlan);
                                invoicePlan.setId(null);
                                invoicePlan.setContractId(contractSonId);
                                String invKey = invoicePlan.getContractId() + "-"
                                        + (invoicePlan.getProductTypeName() == null ? "null" : invoicePlan.getProductTypeName()) + "-"
                                        + (invoicePlan.getTaxId() == null ? "null" : String.valueOf(invoicePlan.getTaxId())) + "-"
                                        + (invoicePlan.getProductId() == null ? "null" : String.valueOf(invoicePlan.getProductId()));
                                //将服务类型一对一 对应关系写入
                                if (relationMap.containsKey(invKey)) {
                                    invoicePlan.setContractProductId(relationMap.get(invKey));
                                }
                                invoicePlanMapper.insert(invoicePlan);
                                Long planId = invoicePlan.getId();
                                Long planHisId = invoicePlanHis.getId();
                                InvoicePlanDetailHisExample invoicePlanDetailHisExample = new InvoicePlanDetailHisExample();
                                invoicePlanDetailHisExample.createCriteria().andChangewayIdEqualTo(Long.valueOf(glegalContractFileInfoDto.getBusinessId())).andContractIdEqualTo(contractHisDTOTypeAddId).andPlanIdEqualTo(planHisId);
                                List<InvoicePlanDetailHis> invoicePlanDetailHisList = invoicePlanDetailHisMapper.selectByExample(invoicePlanDetailHisExample);
                                if (CollectionUtils.isNotEmpty(invoicePlanDetailHisList)) {
                                    int num = 0;
                                    for (InvoicePlanDetailHis invoicePlanDetailHis : invoicePlanDetailHisList) {
                                        InvoicePlanDetail invoicePlanDetail = new InvoicePlanDetail();
                                        num = num + 1;
                                        BeanConverter.copy(invoicePlanDetailHis, invoicePlanDetail);
                                        invoicePlanDetail.setId(null);
                                        invoicePlanDetail.setContractId(contractSonId);
                                        invoicePlanDetail.setNum(num);
                                        invoicePlanDetail.setPlanId(planId);
                                        invoicePlanDetailMapper.insert(invoicePlanDetail);
                                        Long invoicePlanDetailId = invoicePlanDetail.getId();
                                        Long invoicePlanDetailHisId = invoicePlanDetailHis.getInvoicePlanDetailId();
                                        invoicePlanDetailIdReMap.put(invoicePlanDetailHisId, invoicePlanDetailId);
                                    }
                                }
                            }
                        }
                        ReceiptPlanHisExample receiptPlanHisExample = new ReceiptPlanHisExample();
                        receiptPlanHisExample.createCriteria().andChangewayIdEqualTo(Long.valueOf(glegalContractFileInfoDto.getBusinessId())).andContractIdEqualTo(contractHisDTOTypeAddId);
                        List<ReceiptPlanHis> receiptPlanHisList = receiptPlanHisMapper.selectByExample(receiptPlanHisExample);
                        Map<Long, Long> receiptPlanDetailIdReMap = new HashMap<>();
                        if (CollectionUtils.isNotEmpty(receiptPlanHisList)) {
                            for (ReceiptPlanHis receiptPlanHis : receiptPlanHisList) {
                                ReceiptPlan receiptPlan = new ReceiptPlan();
                                BeanConverter.copy(receiptPlanHis, receiptPlan);
                                receiptPlan.setId(null);
                                receiptPlan.setContractId(contractSonId);
                                receiptPlanMapper.insert(receiptPlan);
                                Long planId = receiptPlan.getId();
                                Long planHisId = receiptPlanHis.getId();
                                ReceiptPlanDetailHisExample receiptPlanDetailHisExample = new ReceiptPlanDetailHisExample();
                                receiptPlanDetailHisExample.createCriteria().andChangewayIdEqualTo(Long.valueOf(glegalContractFileInfoDto.getBusinessId())).andContractIdEqualTo(contractHisDTOTypeAddId).andPlanIdEqualTo(planHisId);
                                List<ReceiptPlanDetailHis> receiptPlanDetailHisList = receiptPlanDetailHisMapper.selectByExample(receiptPlanDetailHisExample);
                                if (CollectionUtils.isNotEmpty(receiptPlanDetailHisList)) {
                                    int num = 0;
                                    for (ReceiptPlanDetailHis receiptPlanDetailHis : receiptPlanDetailHisList) {
                                        ReceiptPlanDetail receiptPlanDetail = new ReceiptPlanDetail();
                                        num = num + 1;
                                        BeanConverter.copy(receiptPlanDetailHis, receiptPlanDetail);
                                        receiptPlanDetail.setId(null);
                                        receiptPlanDetail.setContractId(contractSonId);
                                        receiptPlanDetail.setNum(num);
                                        receiptPlanDetail.setPlanId(planId);
                                        receiptPlanDetailMapper.insert(receiptPlanDetail);
                                        Long receiptPlanDetailId = receiptPlanDetail.getId();
                                        Long receiptPlanDetailHisId = receiptPlanDetailHis.getId();
                                        receiptPlanDetailIdReMap.put(receiptPlanDetailHisId, receiptPlanDetailId);
                                    }
                                }

                            }
                        }
                        ReceiptInvoiceRelationHisExample receiptInvoiceRelationHisExample = new ReceiptInvoiceRelationHisExample();
                        receiptInvoiceRelationHisExample.createCriteria().andChangewayIdEqualTo(Long.valueOf(glegalContractFileInfoDto.getBusinessId())).andContractIdEqualTo(contractHisDTOTypeAddId);
                        List<ReceiptInvoiceRelationHis> receiptInvoiceRelationHisList = receiptInvoiceRelationHisMapper.selectByExample(receiptInvoiceRelationHisExample);

                        if (CollectionUtils.isNotEmpty(receiptInvoiceRelationHisList)) {
                            for (ReceiptInvoiceRelationHis receiptInvoiceRelationHis : receiptInvoiceRelationHisList) {
                                ReceiptInvoiceRelation receiptInvoiceRelation = new ReceiptInvoiceRelation();
                                Long hisInvoicePlanDetailId = receiptInvoiceRelationHis.getInvoicePlanDetailId();
                                Long hisReceiptPlanDetailId = receiptInvoiceRelationHis.getReceiptPlanDetailId();
                                receiptInvoiceRelation.setId(null);
                                receiptInvoiceRelation.setContractId(contractSonId);
                                receiptInvoiceRelation.setInvoicePlanDetailId(invoicePlanDetailIdReMap.get(hisInvoicePlanDetailId));
                                receiptInvoiceRelation.setReceiptPlanDetailId(receiptPlanDetailIdReMap.get(hisReceiptPlanDetailId));
                                receiptInvoiceRelation.setDeletedFlag(Boolean.FALSE);
                                receiptInvoiceRelationMapper.insert(receiptInvoiceRelation);
                            }
                        }
                    }
                    //维护服务内容新增的子合同和采购申请的关系
                    eamPurchaseInfoService.saveContractEamPurchaseRel(null, childrenContractIdAddMap, Boolean.FALSE);
                }

                //删除子合同总额（含税）
                BigDecimal amountDel = BigDecimal.ZERO;
                //删除子合同总额（不含税）
                BigDecimal excludingTaxAmountDel = BigDecimal.ZERO;
                //将删除的子合同写入原始表中
                if (CollectionUtils.isNotEmpty(childrencontractHisDTOsTypeDel)) {
                    for (ContractHisDTO contractHisDTOTypeDel : childrencontractHisDTOsTypeDel) {
                        amountDel = amountDel.add(contractHisDTOTypeDel.getAmount() == null ? BigDecimal.ZERO : contractHisDTOTypeDel.getAmount());
                        excludingTaxAmountDel = excludingTaxAmountDel.add(contractHisDTOTypeDel.getExcludingTaxAmount() == null ? BigDecimal.ZERO : contractHisDTOTypeDel.getExcludingTaxAmount());
                        Long contractId = contractHisDTOTypeDel.getContractId();
                        Contract contract1 = new Contract();
                        contract1.setId(contractId);
                        contract1.setDeletedFlag(Boolean.TRUE);
                        contractMapper.updateByPrimaryKeySelective(contract1);
                    }
                }

                if (CollectionUtils.isNotEmpty(childrenContractHisDTOS) && childrenContractHisDTOS.size() > 0) {
                    List<Long> childrenContractIdDelList = new ArrayList<>();
                    Map<Long, String> childrenContractIdAddMap = new HashMap<>();

                    for (ContractHisDTO childrenContractHisDTO : childrenContractHisDTOS) {
                        //更新子合同的审批状态（生效或者待生效）
                        Contract childrenContract = contractMapper.selectByPrimaryKey(childrenContractHisDTO.getContractId());
                        ProjectType projectType = null;
                        ProjectType projectTypeHis = null;
                        if (childrenContract.getBusinessTypeId() != null) {
                            projectType = projectTypeMapper.selectByPrimaryKey(childrenContract.getBusinessTypeId());
                        }
                        if (childrenContractHisDTO.getBusinessTypeId() != null) {
                            projectTypeHis = projectTypeMapper.selectByPrimaryKey(childrenContractHisDTO.getBusinessTypeId());
                        }
                        // 子合同总额（含税）
                        BigDecimal childrenAmount = BigDecimal.ZERO;
                        // 子合同总额（不含税）
                        BigDecimal childrenExcludingTaxAmount = BigDecimal.ZERO;
                        childrenContract.setStatus(status);
                        childrenContract.setBusinessTypeId(childrenContractHisDTO.getBusinessTypeId());
                        // 当业务模式从人力外包（RDM）变更为非RDM类型，RDM采购申请需要同步清除关系
                        if (projectType.getName().equals("人力外包（RDM）") && !projectTypeHis.getName().equals("人力外包（RDM）")) {
                            childrenContract.setEampurchaseId(null);
                            List<Long> childrenContractIdDelList1 = new ArrayList<>();
                            childrenContractIdDelList1.add(childrenContract.getId());
                            eamPurchaseInfoService.saveContractEamPurchaseRel(childrenContractIdDelList1, null, Boolean.FALSE);
                        } else if (childrenContractHisDTO.getEampurchaseId() != null && !childrenContractHisDTO.getEampurchaseId().equals(childrenContract.getEampurchaseId())) {
                            //当合同关联的采购申请发生变动的时候
                            childrenContract.setEampurchaseId(childrenContractHisDTO.getEampurchaseId());
                            List<Long> childrenContractIdDelList2 = new ArrayList<>();
                            childrenContractIdDelList2.add(childrenContract.getId());
                            Map<Long, String> childrenContractIdAddMap2 = new HashMap<>();
                            childrenContractIdAddMap2.put(childrenContract.getId(), childrenContractHisDTO.getEampurchaseId());
                            eamPurchaseInfoService.saveContractEamPurchaseRel(childrenContractIdDelList2, childrenContractIdAddMap2, Boolean.FALSE);
                        } else {
                            childrenContract.setEampurchaseId(childrenContractHisDTO.getEampurchaseId());
                        }

                        //更新合同类型产品,先删除掉旧数据
                        contractProductMapper.deleteByContractId(childrenContractHisDTO.getContractId());
                        List<ContractProductHis> contractProductHiss = childrenContractHisDTO.getContractProductHiss();
                        Map<String, Long> relationMap = new HashMap<>();
                        if (CollectionUtils.isNotEmpty(contractProductHiss)) {
                            for (ContractProductHis productHiss : contractProductHiss) {
                                ContractProduct contractProduct = new ContractProduct();
                                BeanUtils.copyProperties(productHiss, contractProduct);
                                childrenAmount = childrenAmount.add(contractProduct.getAmount());
                                childrenExcludingTaxAmount = childrenExcludingTaxAmount.add(contractProduct.getExcludingTaxAmount() == null ? BigDecimal.ZERO : contractProduct.getExcludingTaxAmount());
                                //原正式表id不空
                                Long oldContractProductId = productHiss.getContractProductId();
                                if (null != oldContractProductId) {
                                    contractProduct.setId(oldContractProductId);
                                    contractProduct.setUpdateAt(now);
                                } else {
                                    contractProduct.setId(null);
                                }
                                contractProductMapper.insert(contractProduct);
                                logger.info("服务合同变更审批已经通过，子合同服务计划数据已经被删除" + contractProduct.toString());
                                Long contractProductId = contractProduct.getId();
                                String key = contractProduct.getContractId() + "-"
                                        + (contractProduct.getProductTypeName() == null ? "null" : contractProduct.getProductTypeName()) + "-"
                                        + (contractProduct.getTaxId() == null ? "null" : String.valueOf(contractProduct.getTaxId())) + "-"
                                        + (contractProduct.getProductId() == null ? "null" : String.valueOf(contractProduct.getProductId()));
                                relationMap.put(key, contractProductId);
                            }
                        }
                        childrenContract.setAmount(childrenAmount);
                        childrenContract.setExcludingTaxAmount(childrenExcludingTaxAmount);
                        contractMapper.updateByPrimaryKey(childrenContract);
//                        childrenContractIdDelList.add(childrenContract.getId());
//                        childrenContractIdAddMap.put(childrenContract.getId(), childrenContract.getEampurchaseId());

                        logger.info("服务合同变更审批已经通过，子合同状态已经更改，" + childrenContract.toString());
                        //更新合同开票计划,开票计划详情
                        //删除原来的开票计划数据
                        invoicePlanMapper.deleteByContractId(childrenContractHisDTO.getContractId());
                        logger.info("服务合同变更审批已经通过，子合同开票计划数据已经被删除" + childrenContractHisDTO.toString());
                        InvoicePlanDetailExample invoicePlanDetailExample = new InvoicePlanDetailExample();
                        invoicePlanDetailExample.createCriteria()
                                .andContractIdEqualTo(childrenContractHisDTO.getContractId())
                                .andDeletedFlagEqualTo(Boolean.FALSE);
                        List<InvoicePlanDetail> invoicePlanDetailOlds = invoicePlanDetailMapper.selectByExample(invoicePlanDetailExample);
                        Map<Long, InvoicePlanDetail> invoicePlanDetailOldMap = invoicePlanDetailOlds.stream().collect(Collectors.toMap(InvoicePlanDetail::getId, Function.identity()));
                        //删除原来的开票计划详情
                        invoicePlanDetailExtMapper.deleteByContractId(childrenContractHisDTO.getContractId());
                        logger.info("服务合同变更审批已经通过，子合同开票计划详情数据已经被删除" + childrenContractHisDTO.toString());
                        Map<Long, Long> invoiceDetailIdMap = new HashMap<>();
                        List<InvoicePlanHisDTO> invoicePlanHisList = childrenContractHisDTO.getInvoicePlanHisDTOs();
                        if (CollectionUtils.isNotEmpty(invoicePlanHisList)) {
                            for (InvoicePlanHisDTO invoicePlanHisDTO : invoicePlanHisList) {
                                InvoicePlanHis invoicePlanHis = invoicePlanHisDTO.conventToInvoicePlanHis();
                                InvoicePlan invoicePlan = new InvoicePlan();
                                BeanUtils.copyProperties(invoicePlanHis, invoicePlan);
                                //原正式表id不空
                                Long oldInvoicePlanId = invoicePlanHisDTO.getInvoicePlanId();
                                if (null != oldInvoicePlanId) {
                                    invoicePlan.setId(oldInvoicePlanId);
                                    invoicePlan.setUpdateAt(now);
                                } else {
                                    invoicePlan.setId(null);
                                }
                                String invKey = invoicePlan.getContractId() + "-"
                                        + (invoicePlan.getProductTypeName() == null ? "null" : invoicePlan.getProductTypeName()) + "-"
                                        + (invoicePlan.getTaxId() == null ? "null" : String.valueOf(invoicePlan.getTaxId())) + "-"
                                        + (invoicePlan.getProductId() == null ? "null" : String.valueOf(invoicePlan.getProductId()));
                                //将服务类型一对一 对应关系写入
                                if (relationMap.containsKey(invKey)) {
                                    invoicePlan.setContractProductId(relationMap.get(invKey));
                                }
                                invoicePlanMapper.insert(invoicePlan);
                                logger.info("服务合同变更审批已经通过，子合同开票计数据已经被插入" + invoicePlan.toString());
                                Long invoicePlanId = invoicePlan.getId();
                                List<InvoicePlanDetailHisDTO> invoicePlanDetailHisList = invoicePlanHisDTO.getInvoicePlanDetailsHiss();
                                if (CollectionUtils.isNotEmpty(invoicePlanDetailHisList)) {
                                    for (InvoicePlanDetailHis invoicePlanDetailHis : invoicePlanDetailHisList) {
                                        InvoicePlanDetail invoicePlanDetail = new InvoicePlanDetail();
                                        BeanUtils.copyProperties(invoicePlanDetailHis, invoicePlanDetail);
                                        // 还原开票计划行ID
                                        if (invoicePlanDetailHis.getInvoicePlanDetailId() != null) {
                                            invoicePlanDetail.setId(invoicePlanDetailHis.getInvoicePlanDetailId());
                                            InvoicePlanDetail invoicePlanDetailOld = invoicePlanDetailOldMap.get(invoicePlanDetailHis.getInvoicePlanDetailId());
                                            // 撤回变更之后，历史表会存在新的开票计划id，此时正式表的旧开票计划id会获取不到
                                            if (invoicePlanDetailOld != null) {
                                                // 合同服务变更通过后覆盖了开票计划行的实际开票金额
                                                // 变更流程审批过程中，正式表字段TaxIncludedPrice有可能会发生变更，需要覆盖过来
                                                invoicePlanDetail.setTaxIncludedPrice(invoicePlanDetailOld.getTaxIncludedPrice());
                                                invoicePlanDetail.setExclusiveOfTax(invoicePlanDetailOld.getExclusiveOfTax());
                                                invoicePlanDetail.setActualReceiptAmount(invoicePlanDetailOld.getActualReceiptAmount());
                                            }
                                        }
                                        invoicePlanDetail.setPlanId(invoicePlanId);
                                        /* 开票计划的计划开票日期自动更新 */
                                        invoicePlanService.invoicePlanDateAutoUpdate(invoicePlanDetail);
                                        invoicePlanDetailMapper.insert(invoicePlanDetail);
                                        invoiceDetailIdMap.put(invoicePlanDetailHis.getId(), invoicePlanDetail.getId());
                                        logger.info("服务合同变更审批已经通过，子合同开票计划详情数据已经被插入" + invoicePlan.toString());
                                    }
                                }
                            }
                        }

                        //更新回款计划，回款计划详情，开票关联,先删除原来的数据
                        receiptPlanMapper.deleteByContractId(childrenContractHisDTO.getContractId());
                        ReceiptPlanDetailExample receiptPlanDetailExample = new ReceiptPlanDetailExample();
                        receiptPlanDetailExample.createCriteria()
                                .andContractIdEqualTo(childrenContractHisDTO.getContractId())
                                .andDeletedFlagEqualTo(Boolean.FALSE);
                        List<ReceiptPlanDetail> receiptPlanDetailOlds = receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample);
                        Map<Long, ReceiptPlanDetail> receiptPlanDetailOldMap = receiptPlanDetailOlds.stream().collect(Collectors.toMap(ReceiptPlanDetail::getId, Function.identity()));
                        receiptPlanDetailExtMapper.deleteByContractId(childrenContractHisDTO.getContractId());
                        receiptInvoiceRelationMapper.deleteByContractId(childrenContractHisDTO.getContractId());
                        List<ReceiptPlanHisDTO> receiptPlanHisDTOS = childrenContractHisDTO.getReceiptPlanHissDTOs();
                        if (CollectionUtils.isNotEmpty(receiptPlanHisDTOS)) {
                            for (ReceiptPlanHisDTO receiptPlanHisDTO : receiptPlanHisDTOS) {
                                ReceiptPlanHis receiptPlanHis = receiptPlanHisDTO.conventToReceiptPlanHis();
                                ReceiptPlan receiptPlan = new ReceiptPlan();
                                BeanUtils.copyProperties(receiptPlanHis, receiptPlan);
                                //原正式表id不空
                                Long oldReceiptPlanId = receiptPlanHisDTO.getReceiptPlanId();
                                if (null != oldReceiptPlanId) {
                                    receiptPlan.setId(oldReceiptPlanId);
                                    receiptPlan.setUpdateAt(now);
                                } else {
                                    receiptPlan.setId(null);
                                }
                                receiptPlanMapper.insert(receiptPlan);
                                Long receiptPlanId = receiptPlan.getId();
                                //回款详情
                                List<ReceiptPlanDetailHisDTO> receiptPlanDetailHisDTOS = receiptPlanHisDTO.getReceiptPlanDetailHisDTOS();
                                if (CollectionUtils.isNotEmpty(receiptPlanDetailHisDTOS)) {
                                    for (ReceiptPlanDetailHisDTO receiptPlanDetailHisDTO : receiptPlanDetailHisDTOS) {
                                        ReceiptPlanDetailHis receiptPlanDetailHis = receiptPlanDetailHisDTO.conventToReceiptPlanDetailHis();

                                        ReceiptPlanDetail receiptPlanDetail = new ReceiptPlanDetail();
                                        BeanUtils.copyProperties(receiptPlanDetailHis, receiptPlanDetail);
                                        // 还原回款计划行ID，历史表会生成新的回款计划id，此时正式表的旧回款计划id会获取不到
                                        if (receiptPlanDetailHis.getReceiptPlanDetailId() != null) {
                                            receiptPlanDetail.setId(receiptPlanDetailHis.getReceiptPlanDetailId());
                                            ReceiptPlanDetail receiptPlanDetailOld = receiptPlanDetailOldMap.get(receiptPlanDetailHis.getReceiptPlanDetailId());
                                            // 撤回变更之后再重新提交, 流程审批过程中，正式表字段actualAmount有可能会发生变更，需要覆盖过来
                                            if (receiptPlanDetailOld != null) {
                                                // 变更通过后覆盖了回款计划行的实际回款金额
                                                receiptPlanDetail.setActualAmount(receiptPlanDetailOld.getActualAmount());
                                            }
                                        }
                                        receiptPlanDetail.setPlanId(receiptPlanId);
                                        /* 回款计划的计划开票日期自动更新 */
                                        invoicePlanService.receiptPlanDateAutoUpdate(receiptPlanDetail);
                                        receiptPlanDetailMapper.insert(receiptPlanDetail);
                                        Long receiptPlanDetailId = receiptPlanDetail.getId();
                                        //开票回款联系
                                        List<ReceiptInvoiceRelationHisDTO> receiptInvoiceRelationHisDTOS = receiptPlanDetailHisDTO.getReceiptInvoiceRelationHisDTOs();
                                        if (CollectionUtils.isNotEmpty(receiptInvoiceRelationHisDTOS)) {
                                            for (ReceiptInvoiceRelationHisDTO receiptInvoiceRelationHisDTO : receiptInvoiceRelationHisDTOS) {
                                                ReceiptInvoiceRelationHis receiptInvoiceRelationHis = receiptInvoiceRelationHisDTO.conventToReceiptInvoiceRelationHis();
                                                ReceiptInvoiceRelation receiptInvoiceRelation = new ReceiptInvoiceRelation();
                                                BeanUtils.copyProperties(receiptInvoiceRelationHis, receiptInvoiceRelation);
                                                receiptInvoiceRelation.setId(null);
                                                receiptInvoiceRelation.setReceiptPlanDetailId(receiptPlanDetailId);

                                                Long invoicePlanDetailId = receiptInvoiceRelationHis.getInvoicePlanDetailId();
                                                Long newInvoicePlanDetailId = invoiceDetailIdMap.get(invoicePlanDetailId);
                                                receiptInvoiceRelation.setInvoicePlanDetailId(newInvoicePlanDetailId);

                                                receiptInvoiceRelationMapper.insert(receiptInvoiceRelation);
                                            }
                                        }
                                    }
                                }

                            }
                        }

                        Date afterStartTime = childrenContractHisDTO.getStartTime();
                        Date afterEndTime = childrenContractHisDTO.getEndTime() == null ? contractHisDTO.getEndTime() : childrenContractHisDTO.getEndTime();

                        //一旦有收入成本计划的数据，说明该合同有主辅里程碑，需要更新主辅里程碑的成本金额
                        ProjectProfitHisDTO projectProfitHisDTO = childrenContractHisDTO.getProjectProfitHisDTO();

                        BigDecimal mainIncome = null;
                        BigDecimal helpIncome = null;
                        if (projectProfitHisDTO != null) {
                            mainIncome = projectProfitHisDTO.getMainIncome();
                            helpIncome = projectProfitHisDTO.getHelpIncome();
                        } else {
                            mainIncome = childrenContractHisDTO.getExcludingTaxAmount();
                        }

                        // 收入成本计划变更
                        changeIncomeCostPlans(childrenContractHisDTO.getContractId(), childrenContractHisDTO.getExcludingTaxAmount(), afterStartTime, afterEndTime, mainIncome, helpIncome);

                        if (!ObjectUtils.isEmpty(projectProfitHisDTO)) {
                            //更新该子合同的含税总金额,不含税总额
                            Contract childrenContract1 = new Contract();
                            childrenContract1.setId(childrenContractHisDTO.getContractId());
                            childrenContract1.setAmount(projectProfitHisDTO.getProjectAmount());
                            childrenContract1.setExcludingTaxAmount(projectProfitHisDTO.getProjectCost());
                            contractMapper.updateByPrimaryKeySelective(childrenContract1);
                            //更新主辅收入总额
                            ProjectProfitHis projectProfitHis = projectProfitHisDTO.convertToProjectProfitHis();
                            ProjectProfit projectProfit = new ProjectProfit();
                            BeanUtils.copyProperties(projectProfitHis, projectProfit);
                            projectProfit.setId(projectProfitHis.getProjectProfitId());
                            projectProfitMapper.updateByPrimaryKey(projectProfit);

                            //更新主辅里程碑的收入
                            ProjectContractRsExample projectContractRsExample = new ProjectContractRsExample();
                            final ProjectContractRsExample.Criteria pcrCriteria = projectContractRsExample.createCriteria();
                            pcrCriteria.andContractIdEqualTo(childrenContractHisDTO.getContractId());
                            final List<ProjectContractRs> projectContractRsList = projectContractRsService.selectByExample(projectContractRsExample);
                            if (CollectionUtils.isNotEmpty(projectContractRsList)) {
                                final ProjectContractRs projectContractRs = projectContractRsList.get(0);
                                final Long projectId = projectContractRs.getProjectId();
                                // 更新辅里程碑收入金额
                                final ProjectMilepostExample helpMilepostExample = new ProjectMilepostExample();
                                helpMilepostExample.createCriteria().andProjectIdEqualTo(projectId).
                                        andDeletedFlagEqualTo(false).andHelpFlagEqualTo(true);
                                helpMilepostExample.setOrderByClause("order_num asc");
                                final List<ProjectMilepost> helpMileposts = projectMilepostService.selectByExample(helpMilepostExample);
                                for (ProjectMilepost helpMilepost : helpMileposts) {
                                    BigDecimal helpMilepostIncome = BigDecimal.ZERO;
                                    BigDecimal projectProfitHisHelpIncome = projectProfitHis.getHelpIncome() == null ? BigDecimal.ZERO : projectProfitHis.getHelpIncome();
                                    BigDecimal helpMilepostIncomeRatio = helpMilepost.getIncomeRatio() == null ? BigDecimal.ZERO : helpMilepost.getIncomeRatio();
                                    helpMilepostIncome = helpMilepostIncome.add(projectProfitHisHelpIncome.multiply(helpMilepostIncomeRatio).divide(BigDecimal.valueOf(100)));
                                    helpMilepost.setIncome(helpMilepostIncome);
                                    projectMilepostMapper.updateByPrimaryKeySelective(helpMilepost);
                                }
                                // 更新主里程碑收入金额
                                final ProjectMilepostExample mainMilepostExample = new ProjectMilepostExample();
                                mainMilepostExample.createCriteria().andProjectIdEqualTo(projectId).
                                        andDeletedFlagEqualTo(false).andHelpFlagEqualTo(false);
                                mainMilepostExample.setOrderByClause("order_num asc");
                                final List<ProjectMilepost> mainMileposts = projectMilepostService.selectByExample(mainMilepostExample);
                                for (ProjectMilepost mainMilepost : mainMileposts) {
                                    BigDecimal mainMilepostIncome = BigDecimal.ZERO;
                                    BigDecimal projectProfitHisMainIncome = projectProfitHis.getMainIncome() == null ? BigDecimal.ZERO : projectProfitHis.getMainIncome();
                                    BigDecimal mainMilepostIncomeRatio = mainMilepost.getIncomeRatio() == null ? BigDecimal.ZERO : mainMilepost.getIncomeRatio();
                                    mainMilepostIncome = mainMilepostIncome.add(projectProfitHisMainIncome.multiply(mainMilepostIncomeRatio).divide(BigDecimal.valueOf(100)));
                                    mainMilepost.setIncome(mainMilepostIncome);
                                    projectMilepostMapper.updateByPrimaryKeySelective(mainMilepost);
                                }
                            }

                        }

                        //合计子合同的合同总额（含税）和不含税总额
                        amount = amount.add(childrenContract.getAmount() == null ? BigDecimal.ZERO : childrenContract.getAmount());
                        excludingTaxAmount = excludingTaxAmount.add(childrenContract.getExcludingTaxAmount() == null ? BigDecimal.ZERO : childrenContract.getExcludingTaxAmount());
                    }
                    // 重新维护子合同关联采购申请
//                    eamPurchaseInfoService.saveContractEamPurchaseRel(childrenContractIdDelList, childrenContractIdAddMap, Boolean.FALSE);
                    //更新主合同
//                    BigDecimal finalAmount = amount.add(amountAdd).subtract(amountDel);
//                    BigDecimal finalExcludingTaxAmount = excludingTaxAmount.add(excludingTaxAmountAdd).subtract(excludingTaxAmountDel);
//                    contract.setAmount(finalAmount);
//                    contract.setExcludingTaxAmount(finalExcludingTaxAmount);
                    contractMapper.updateByPrimaryKeySelective(contract);
                    logger.info("服务合同变更审批已经通过，状态已经更改，" + contract.toString());
                }


                logger.info("合同相关表已经更新完毕！");
                /**
                 *  ====================================>>合同系列表已全部更新完毕<<==================================
                 *
                 *
                 *
                 *  ====================================>>合同记录系列表开始更新<<====================================
                 *
                 */

                //删除只做更新的数据
                if (CollectionUtils.isNotEmpty(childrenContractHisDTOS)) {
                    for (ContractHisDTO childrenContractHisDTO : childrenContractHisDTOS) {
                        ContractHis contractHisQuery = new ContractHis();
                        contractHisQuery.setContractId(childrenContractHisDTO.getContractId() == null ? childrenContractHisDTO.getId() : childrenContractHisDTO.getContractId());
                        contractHisQuery.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                        contractHisExtMapper.deleteContractProductHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteInvoicePlanHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteInvoicePlanDetailHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteReceiptPlanHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteReceiptPlanDetailHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteReceiptInvoiceRelationHisByChangewayId(contractHisQuery);
                    }
                }

                ContractHis contractHis = new ContractHis();
                contractHis.setContractId(contractVO.getId());
                contractHis.setChangewayId(contractVO.getChangewayId());
                contractHis.setAmount(contractVO.getAmount());
                contractHis.setExcludingTaxAmount(contractVO.getExcludingTaxAmount());
                contractHis.setCost(contractVO.getCost());
                contractHis.setBusinessTypeId(contractVO.getBusinessTypeId());
                contractHis.setEampurchaseId(contractVO.getEampurchaseId());
                contractHisExtMapper.updateByIdAndChangewayId(contractHis);

                if (CollectionUtils.isNotEmpty(contractVOInvList) && contractVOInvList.size() > 0) {
                    List<Long> childrenContractIdDelList = new ArrayList<>();
                    Map<Long, String> childrenContractIdAddMap = new HashMap<>();

                    for (ContractVO childrenContractVO : contractVOInvList) {
                        //更新子合同
                        ContractHis childrenContractHis = new ContractHis();
                        childrenContractHis.setContractId(childrenContractVO.getId());
                        childrenContractHis.setChangewayId(childrenContractVO.getChangewayId());
                        childrenContractHis.setAmount(childrenContractVO.getAmount());
                        childrenContractHis.setExcludingTaxAmount(childrenContractVO.getExcludingTaxAmount());
                        childrenContractHis.setCost(childrenContractVO.getCost());
                        childrenContractHis.setBusinessTypeId(childrenContractVO.getBusinessTypeId());
                        childrenContractHis.setEampurchaseId(childrenContractVO.getEampurchaseId());
                        childrenContractHis.setDeletedFlag(Boolean.FALSE);
                        contractHisExtMapper.updateByIdAndChangewayId(childrenContractHis);
                        ContractHisExample contractHisExample = new ContractHisExample();
                        contractHisExample.createCriteria().andContractIdEqualTo(childrenContractVO.getId()).andChangewayIdEqualTo(childrenContractVO.getChangewayId()).andDeletedFlagEqualTo(Boolean.FALSE);
                        List<ContractHis> sonContractHisList = contractHisMapper.selectByExample(contractHisExample);
                        ContractHis sonContractHis = null;
                        if (CollectionUtils.isNotEmpty(sonContractHisList)) {
                            sonContractHis = sonContractHisList.get(0);
                            childrenContractIdDelList.add(sonContractHis.getId());
                            childrenContractIdAddMap.put(sonContractHis.getId(), sonContractHis.getEampurchaseId());
                        }
                        //更新合同类型产品记录
                        List<ContractProductDTO> contractProductDTOS = childrenContractVO.getContractProducts();
                        if (CollectionUtils.isNotEmpty(contractProductDTOS)) {
                            for (ContractProductDTO contractProductDTO : contractProductDTOS) {

                                ContractProduct contractProduct = contractProductDTO.convertToContractProduct();
                                ContractProductHis contractProductHis = new ContractProductHis();
                                BeanUtils.copyProperties(contractProduct, contractProductHis);
                                contractProductHis.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                                contractProductHis.setId(null);
                                contractProductHis.setContractProductId(contractProduct.getId());
                                contractProductHisMapper.insert(contractProductHis);
                            }
                        }
                        //更新合同开票计划，开票计划详情记录
                        List<InvoicePlanDTO> invoicePlanDTOS = childrenContractVO.getInvoicePlans();
                        if (CollectionUtils.isNotEmpty(invoicePlanDTOS)) {
                            for (InvoicePlanDTO invoicePlanDTO : invoicePlanDTOS) {
                                InvoicePlan invoicePlan = invoicePlanDTO.conventToInvoicePlan();
                                InvoicePlanHis invoicePlanHis = new InvoicePlanHis();
                                BeanUtils.copyProperties(invoicePlan, invoicePlanHis);
                                invoicePlanHis.setId(null);
                                invoicePlanHis.setInvoicePlanId(invoicePlan.getId());
                                invoicePlanHis.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                                invoicePlanHisMapper.insert(invoicePlanHis);
                                //更新开票计划详情记录
                                List<InvoicePlanDetailDto> invoicePlanDetails = invoicePlanDTO.getInvoicePlanDetails();
                                if (CollectionUtils.isNotEmpty(invoicePlanDetails)) {
                                    for (InvoicePlanDetail invoicePlanDetail : invoicePlanDetails) {
                                        InvoicePlanDetailHis invoicePlanDetailHis = new InvoicePlanDetailHis();
                                        BeanUtils.copyProperties(invoicePlanDetail, invoicePlanDetailHis);
                                        invoicePlanDetailHis.setId(null);
                                        invoicePlanDetailHis.setInvoicePlanDetailId(invoicePlanDetail.getId());
                                        invoicePlanDetailHis.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                                        invoicePlanDetailHisMapper.insert(invoicePlanDetailHis);
                                    }
                                }

                            }
                        }

                        //更新回款计划，回款计划详情，开票关联记录
                        List<ReceiptPlanDTO> receiptPlanDTOS = childrenContractVO.getReceiptPlans();
                        if (CollectionUtils.isNotEmpty(receiptPlanDTOS)) {
                            for (ReceiptPlanDTO receiptPlanDTO : receiptPlanDTOS) {
                                ReceiptPlan receiptPlan = receiptPlanDTO.conventToReceiptPlan();
                                ReceiptPlanHis receiptPlanHis = new ReceiptPlanHis();
                                BeanUtils.copyProperties(receiptPlan, receiptPlanHis);
                                receiptPlanHis.setId(null);
                                receiptPlanHis.setReceiptPlanId(receiptPlan.getId());
                                receiptPlanHis.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                                receiptPlanHisMapper.insert(receiptPlanHis);
                                //更新回款详情记录
                                List<ReceiptPlanDetailDTO> receiptPlanDetailDTOS = receiptPlanDTO.getReceiptPlanDetails();
                                if (CollectionUtils.isNotEmpty(receiptPlanDetailDTOS)) {
                                    for (ReceiptPlanDetailDTO receiptPlanDetailDTO : receiptPlanDetailDTOS) {
                                        ReceiptPlanDetail receiptPlanDetail = receiptPlanDetailDTO.convertToReceiptPlanDetail();
                                        ReceiptPlanDetailHis receiptPlanDetailHis = new ReceiptPlanDetailHis();
                                        BeanUtils.copyProperties(receiptPlanDetail, receiptPlanDetailHis);
                                        receiptPlanDetailHis.setId(null);
                                        receiptPlanDetailHis.setReceiptPlanDetailId(receiptPlanDetail.getId());
                                        receiptPlanDetailHis.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                                        receiptPlanDetailHisMapper.insert(receiptPlanDetailHis);

                                        //更新开票回款联系记录表
                                        List<ReceiptInvoiceRelationDTO> receiptInvoiceRelationDTO = receiptPlanDetailDTO.getReceiptInvoiceRelations();
                                        if (CollectionUtils.isNotEmpty(receiptInvoiceRelationDTO)) {
                                            for (ReceiptInvoiceRelationDTO invoiceRelationDTO : receiptInvoiceRelationDTO) {
                                                ReceiptInvoiceRelation receiptInvoiceRelation = invoiceRelationDTO.conventToReceiptInvoiceRelation();
                                                ReceiptInvoiceRelationHis receiptInvoiceRelationHis = new ReceiptInvoiceRelationHis();
                                                BeanUtils.copyProperties(receiptInvoiceRelation, receiptInvoiceRelationHis);
                                                receiptInvoiceRelationHis.setId(null);
                                                receiptInvoiceRelationHis.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                                                receiptInvoiceRelationHis.setReceiptInvoiceRelationId(receiptInvoiceRelation.getId());
                                                receiptInvoiceRelationHis.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                                                receiptInvoiceRelationHis.setDeletedFlag(false);
                                                receiptInvoiceRelationHisMapper.insert(receiptInvoiceRelationHis);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        //一旦有收入成本计划的数据，说明该合同有主辅里程碑，需要更新收入成本计划记录表，主辅里程碑暂时不做更新
                        ProjectProfit projectProfit = childrenContractVO.getProjectProfitDTO();
                        if (!ObjectUtils.isEmpty(projectProfit)) {
                            ProjectProfitHis projectProfitHis = new ProjectProfitHis();
                            BeanUtils.copyProperties(projectProfit, projectProfitHis);
                            ProjectProfitHisExample projectProfitHisExample = new ProjectProfitHisExample();
                            ProjectProfitHisExample.Criteria criteria = projectProfitHisExample.createCriteria();
                            criteria.andChangewayIdEqualTo(Long.valueOf(glegalContractFileInfoDto.getBusinessId())).andProjectProfitIdEqualTo(projectProfit.getId());
                            List<ProjectProfitHis> projectProfitHiss = projectProfitHisMapper.selectByExample(projectProfitHisExample);
                            projectProfitHis.setId(projectProfitHiss.get(0).getId());
                            projectProfitHis.setChangewayId(Long.valueOf(glegalContractFileInfoDto.getBusinessId()));
                            projectProfitHis.setProjectProfitId(projectProfit.getId());
                            projectProfitHisMapper.updateByPrimaryKey(projectProfitHis);
                        }

                    }

                    eamPurchaseInfoService.saveContractEamPurchaseRel(childrenContractIdDelList, childrenContractIdAddMap, Boolean.TRUE);
                }

                //逻辑删除原来的产品清单
                contractInventoryExtMapper.logicalDeleteByParentContractId(contractChangewayDTO.getContractId());
                //插入变更后产品清单
                ContractInventoryHisExample contractInventoryHisExample = new ContractInventoryHisExample();
                contractInventoryHisExample.createCriteria().andChangewayIdEqualTo(contractChangewayDTO.getId())
                        .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode())
                        .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<ContractInventoryHis> contractInventoryHisList = contractInventoryHisMapper.selectByExample(contractInventoryHisExample);
                contractInventoryHisList.forEach(contractInventoryHis -> {
                    ContractInventory contractInventory = new ContractInventory();
                    BeanConverter.copy(contractInventoryHis, contractInventory);
                    contractInventory.setId(null);
                    if (!contractIdMappingByAddMap.isEmpty() && contractIdMappingByAddMap.containsKey(contractInventory.getContractId())) {
                        contractInventory.setContractId(contractIdMappingByAddMap.get(contractInventory.getContractId()));
                        //更新his表的合同id为合同实际id
                        contractInventoryHis.setContractId(contractInventory.getContractId());
                        contractInventoryHisMapper.updateByPrimaryKeySelective(contractInventoryHis);
                    }
                    contractInventoryMapper.insert(contractInventory);
                });

                if (null != contract) {
                    insertFileInfo(glegalContractFileInfoDto.getFileInfos(), contract);
                }
            }
        }
    }

    @Override
    public void refuseServiceChange(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        int status = ContractStatus.EFFECTIVE.getCode();
        flow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), CHANGE_TYPE_TWO, APPROVAL_TYPE_TWO, status, glegalContractFileInfoDto);
    }

    @Override
    public void draftServiceChangeReturn(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        int status = ContractStatus.EFFECTIVE.getCode();
        flow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), CHANGE_TYPE_TWO, APPROVAL_TYPE_FOURE, status, glegalContractFileInfoDto);
    }

    @Override
    public void serviceChangeAbandon(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        int status = ContractStatus.EFFECTIVE.getCode();
        flow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), CHANGE_TYPE_TWO, ContractChangewayStatus.ABANDON.getCode(), status, glegalContractFileInfoDto);
    }

    @Override
    public void draftContractTerminationSubmit(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        contractTerminationFlow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), APPROVAL_TYPE_ONE, ContractStatus.TERMINATIONING.getCode(), glegalContractFileInfoDto);
    }

    @Override
    public void passContractTermination(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        String formInstanceId = glegalContractFileInfoDto.getBusinessId();
        String glContractCode = glegalContractFileInfoDto.getContractCode();
        logger.info("法务销售合同终止审批回调 formInstanceId:{}, glContractCode:{}", formInstanceId, glContractCode);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("法务销售合同终止审批提交审批回调 formInstanceId为空，不处理");
            return;
        }
        // 更新变更记录状态
        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        Guard.notNull(contractChangeway, "销售合同终止审批通过回调接口失败，formInstanceId对应的终止记录不存在，不处理");
        //防止重复回调
//        if (Objects.equals(contractChangeway.getApprovalStatus(), ContractChangewayStatus.APPROVED.getCode())) {
//            return;
//        }
        contractChangeway.setApprovalStatus(ContractChangewayStatus.APPROVED.getCode());
        contractChangewayMapper.updateByPrimaryKey(contractChangeway);

        //查询主合同
        Contract contract = contractService.findById(contractChangeway.getContractId());
        Guard.notNull(contract, "销售合同终止审批通过回调接口失败，formInstanceId对应的主合同不存在，不处理");
        //更新主合同状态与金额
//        updateContractStatusAndAmount(contractChangeway.getCreateBy(), contractChangeway.getTerminationDate(), contract);

        List<ContractVO> subContracts = contractService.getListByParentId(contract.getId());
        for (ContractVO subContract : subContracts) {
            //更新子合同状态与金额
            updateContractStatusAndAmount(contractChangeway.getCreateBy(), contractChangeway.getTerminationDate(), subContract);
            // 收入成本计划变更
            changeIncomeCostPlans(subContract.getId(), null, contract.getStartTime(), contract.getEndTime(), null, null);
        }
        //文件信息处理 这里会更新主合同信息
        contract.setStatus(ContractStatus.TERMINATION.getCode());
        contract.setTerminationDate(contractChangeway.getTerminationDate());
        insertFileInfo(glegalContractFileInfoDto.getFileInfos(), contract);
    }

    @Override
    public void refuseContractTermination(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        int status = ContractStatus.EFFECTIVE.getCode();
        contractTerminationFlow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), APPROVAL_TYPE_TWO, status, glegalContractFileInfoDto);
    }

    @Override
    public void draftContractTerminationReturn(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        int status = ContractStatus.EFFECTIVE.getCode();
        contractTerminationFlow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), APPROVAL_TYPE_FOURE, status, glegalContractFileInfoDto);
    }

    @Override
    public void ContractTerminationAbandon(GlegalContractFileInfoDto glegalContractFileInfoDto) {
        int status = ContractStatus.EFFECTIVE.getCode();
        contractTerminationFlow(glegalContractFileInfoDto.getBusinessId(), glegalContractFileInfoDto.getContractCode(), ContractChangewayStatus.ABANDON.getCode(), status, glegalContractFileInfoDto);
    }

    /**
     * 销售法务销售合同新增流程作废和删除
     *
     * @param formInstanceId
     * @param contractStatus
     */
    private void changeContractFlow(String formInstanceId, ContractStatus contractStatus) {
        Contract contract = contractMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (contract != null) {
            if (contractStatus.equals(ContractStatus.CANCEL)) {  //作废
                contract.setStatus(ContractStatus.CANCEL.getCode());
            } else { //流程删除
                contract.setDeletedFlag(DeletedFlag.INVALID.code());
            }
            contractMapper.updateByPrimaryKey(contract);
            ContractExample contractExample = new ContractExample();
            contractExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andParentIdEqualTo(contract.getId());
            List<Contract> childrenContractList = contractMapper.selectByExample(contractExample);
            if (CollectionUtils.isNotEmpty(childrenContractList)) {
                for (Contract children : childrenContractList) {
                    if (contractStatus.equals(ContractStatus.CANCEL)) {
                        children.setStatus(ContractStatus.CANCEL.getCode());
                    } else {
                        children.setDeletedFlag(DeletedFlag.INVALID.code());
                    }
                    contractMapper.updateByPrimaryKey(children);
                }
            }
        }
    }

    /**
     * 同步更新子法务销售合同上面的业务实体ID
     *
     * @param parentId 主法务销售合同ID
     * @param ouId     业务实体ID
     */
    private void updateChildContractOuId(Long parentId, Long ouId) {
        if (parentId != null && ouId != null) {
            Long userId = SystemContext.getUserId();
            Date date = new Date();
            ContractExample example = new ContractExample();
            ContractExample.Criteria criteria = example.createCriteria();
            criteria.andParentIdEqualTo(parentId);
            List<Contract> list = contractMapper.selectByExample(example);
            if (list != null) {
                for (Contract contract : list) {
                    contract.setOuId(ouId);
                    contract.setUpdateBy(userId);
                    contract.setUpdateAt(date);
                    contractMapper.updateByPrimaryKey(contract);
                }
            }
        }
    }

    /**
     * 收入成本计划变更
     *
     * @param contractId
     * @param afterExcludingTaxAmount
     * @param afterContractStartDate
     * @param afterContractEndDate
     */
    private void changeIncomeCostPlans(Long contractId, BigDecimal afterExcludingTaxAmount, Date afterContractStartDate, Date afterContractEndDate,
                                       BigDecimal mainIncome, BigDecimal helpIncome) {
        ProjectContractRsExample example = new ProjectContractRsExample();
        ProjectContractRsExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andContractIdEqualTo(contractId);
        List<ProjectContractRs> projectContractRsList = projectContractRsService.selectByExample(example);
        if (CollectionUtils.isNotEmpty(projectContractRsList)) {
            ProjectContractRs projectContractRs = projectContractRsList.get(0);
            Long projectId = projectContractRs.getProjectId();
            //计算项目金额
            //之前是一个项目对应一个合同，所以合同金额等于项目金额，现在改成一个项目可以关联多个合同，打补丁
            ProjectDto query = new ProjectDto();
            query.setId(projectId);
            projectBusinessService.calculationContractAmount(query);
            projectBusinessService.changeIncomeCostPlans(projectId, query.getExcludingTaxAmount(), afterContractStartDate, afterContractEndDate, query.getExcludingTaxAmount(), helpIncome);
        }
    }

    /**
     * 提交审批,驳回，撤回公用方法
     *
     * @param formInstanceId
     * @param handlerId
     * @param changeType
     */
    private void flow(String formInstanceId, String handlerId, Integer changeType, Integer changesApprovalType, Integer flowState, GlegalContractFileInfoDto glegalContractFileInfoDto) {
        logger.info("法务销售合同变更回调 formInstanceId:{}, handlerId:{}", formInstanceId, handlerId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("法务销售合同变更回调 formInstanceId为空，不处理");
            return;
        }
        // 更新变更记录状态
        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (Objects.isNull(contractChangeway)) {
            logger.error("法务销售合同变更回调接口失败，formInstanceId对应的变更记录不存在，不处理");
            return;
        }
        contractChangeway.setApprovalStatus(changesApprovalType);
        contractChangewayMapper.updateByPrimaryKey(contractChangeway);
        //只查询主合同
        Contract contract = contractService.findById(contractChangeway.getContractId());
        if (Objects.isNull(contract)) {
            logger.error("法务销售合同变更回调 formInstanceId对应销售合同不存在，不处理");
        } else {
            contract.setStatus(flowState);
            contractService.update(contract);
            final List<ContractVO> childContracts = contractService.getListByParentId(contract.getId());

            if (CollectionUtils.isNotEmpty(childContracts)) {
                childContracts.forEach(contractVO -> {
                    Contract child = new Contract();
                    BeanUtils.copyProperties(contractVO, child);
                    child.setStatus(flowState);
                    contractService.update(child);
                });
            }
            insertFileInfo(glegalContractFileInfoDto.getFileInfos(), contract);
        }
    }

    /**
     * 销售合同终止-提交审批,驳回，撤回公用方法
     *
     * @param formInstanceId
     * @param glContractCode
     */
    private void contractTerminationFlow(String formInstanceId, String glContractCode, Integer changesApprovalType, Integer flowState, GlegalContractFileInfoDto glegalContractFileInfoDto) {
        logger.info("法务销售合同终止审批回调 formInstanceId:{}, glContractCode:{}", formInstanceId, glContractCode);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("法务销售合同终止审批提交审批回调 formInstanceId为空，不处理");
            return;
        }
        // 更新变更记录状态
        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        Guard.notNull(contractChangeway, "销售合同终止审批回调接口失败，formInstanceId对应的终止记录不存在，不处理");
        contractChangeway.setApprovalStatus(changesApprovalType);
        contractChangewayMapper.updateByPrimaryKey(contractChangeway);
        //只查询主合同
        Contract contract = contractService.findById(contractChangeway.getContractId());
        Guard.notNull(contract, "销售合同终止审批回调接口失败，formInstanceId对应的主合同不存在，不处理");
        //这里更新主合同与子合同
        contractExtMapper.updateContractByChange(flowState, null, contract.getId());

        //文件信息处理 这里会更新主合同信息
        contract.setStatus(flowState);
        insertFileInfo(glegalContractFileInfoDto.getFileInfos(), contract);
    }

    private void saveFileInfo(List<GlegalContractFileInfoDto.FileInfos> fileInfos, Contract contract) {
        if (CollectionUtils.isNotEmpty(fileInfos) && Objects.nonNull(contract)) {
            List<String> originalContractAnnexList = new ArrayList<>();
            List<String> changeProtocolFileList = new ArrayList<>();
            List<String> annexList = new ArrayList<>();
            for (GlegalContractFileInfoDto.FileInfos fileInfo : fileInfos) {
                //收集法务系统回调删除标识为false的附件信息
                if (fileInfo.getDeleteFlag()) {
                    continue;
                }
                String fileSign = fileInfo.getFileSign();
                String fileId = fileInfo.getId();
                //ContractStampArchive-ContractNeedStamp合同原件,StampArchive-ChangeProtocol 变更协议，ContractNotNeedStamp 合同附件
                if ("ContractStampArchive".equals(fileSign) || "ContractNeedStamp".equals(fileSign)) {
                    originalContractAnnexList.add(fileId);
                } else if ("StampArchive".equals(fileSign) || "ChangeProtocol".equals(fileSign)) {
                    changeProtocolFileList.add(fileId);
                } else if ("ContractNotNeedStamp".equals(fileSign)) {
                    annexList.add(fileId);
                }
            }
            //1.更新合同原件，附件信息
            String originalContractAnnexStr = String.join(",", originalContractAnnexList);
            String annexStr = String.join(",", annexList);
            contract.setOriginalContractAnnex(originalContractAnnexStr);
            contract.setAnnex(annexStr);
            contractService.update(contract);
            //备份父合同初始版本
            ContractOrigin contractOrigin = contractService.packageContractOrigin(contract, null);
            contractOriginMapper.insert(contractOrigin);
            //2.变更协议不为空，则更新变更协议
            String changeProtocolFileStr = String.join(",", changeProtocolFileList);
            if (StringUtils.isNotEmpty(changeProtocolFileStr)) {
                ContractChangewayExample changeWayExample = new ContractChangewayExample();
                changeWayExample.createCriteria().andContractIdEqualTo(contract.getId())
                        .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<ContractChangeway> changeWays = contractChangewayMapper.selectByExample(changeWayExample);
                if (CollectionUtils.isNotEmpty(changeWays)) {
                    ContractChangeway changeWay = changeWays.get(0);
                    changeWay.setChangeProtocolFile(changeProtocolFileStr);
                    contractChangewayMapper.updateByPrimaryKeySelective(changeWay);
                }
            }
        }
    }

    private void insertFileInfo(List<GlegalContractFileInfoDto.FileInfos> fileInfos, Contract contract) {
        logger.info("更新附件信息，fileInfos参数为：{}，contract参数为：{}", fileInfos, contract);
        //1.更新合同原件，附件信息，变更协议 ，合同状态
        saveFileInfo(fileInfos, contract);
        //2.更新附件表信息
        final UserInfo userInfo = CacheDataUtils.findUserById(SystemContext.getUserId());
        fileInfos.forEach(f -> {
            GlegalFileInfoExample glegalFileInfoExample = new GlegalFileInfoExample();
            glegalFileInfoExample.createCriteria().andDocIdEqualTo(f.getId());
            List<GlegalFileInfo> glegalFileInfos = glegalFileInfoExtMapper.selectByExample(glegalFileInfoExample);
            if (ListUtils.isNotEmpty(glegalFileInfos)) {
                //更新
                glegalFileInfos.get(0).setFileName(URLDecoder.decode(f.getFileName()));//文件名
                glegalFileInfos.get(0).setFileSize(Long.valueOf(f.getFileSize()));//文件大小
                glegalFileInfos.get(0).setFileType(f.getFileSurfix());//文件类型
                if (userInfo != null) {
                    glegalFileInfos.get(0).setMipAccount(userInfo.getUsername()); //mip账户
                }
                glegalFileInfos.get(0).setFileAttachmentId(f.getAttachmentId()); //模板库返回的附件attachmentId
                glegalFileInfos.get(0).setDeleteFlag(f.getDeleteFlag());
                glegalFileInfos.get(0).setDocId(f.getId());
                glegalFileInfoExtMapper.updateByPrimaryKeySelective(glegalFileInfos.get(0));
            } else {
                //插入
                GlegalFileInfo fileInfo = new GlegalFileInfo();
                fileInfo.setFileName(URLDecoder.decode(f.getFileName()));//文件名
                fileInfo.setFileSize(Long.valueOf(f.getFileSize()));//文件大小
                //String suffixName = f.getFileSurfix().substring(f.getFileSurfix().lastIndexOf("."));
                fileInfo.setFileType(f.getFileSurfix());//文件类型
                if (userInfo != null) {
                    fileInfo.setMipAccount(userInfo.getUsername()); //mip账户
                }
                fileInfo.setFileAttachmentId(f.getAttachmentId()); //模板库返回的附件attachmentId
                fileInfo.setDeleteFlag(f.getDeleteFlag());
                fileInfo.setDocId(f.getId());
                glegalFileInfoExtMapper.insert(fileInfo);
            }
        });
    }

    /**
     * 更新合同状态与金额
     *
     * @param updateBy
     * @param contract
     */
    private void updateContractStatusAndAmount(Long updateBy, Date terminationDate, Contract contract) {
        contract.setStatus(ContractStatus.TERMINATION.getCode());
        contract.setTerminationDate(terminationDate);
        contract.setUpdateAt(new Date());
        contract.setUpdateBy(updateBy);
        contractMapper.updateByPrimaryKeySelective(contract);
    }

}
