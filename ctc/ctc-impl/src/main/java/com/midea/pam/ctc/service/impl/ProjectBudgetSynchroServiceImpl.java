package com.midea.pam.ctc.service.impl;

import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.CarrierResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.FeeItemDto;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.ProjectBudgetFeeItemDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.entity.ProjectBudgetSynchro;
import com.midea.pam.common.ctc.entity.ProjectBudgetSynchroExample;
import com.midea.pam.common.ctc.entity.ProjectFeeCollection;
import com.midea.pam.common.ctc.entity.ProjectFeeCollectionExample;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.ext.service.impl.AbstractCommonBusinessService;
import com.midea.pam.ctc.mapper.ProjectBudgetSynchroMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.GEMSCarrierServicel;
import com.midea.pam.ctc.service.ProjectBudgetSynchroService;
import com.midea.pam.ctc.service.ProjectFeeCollectionService;
import com.midea.pam.ctc.service.ProjectService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

public class ProjectBudgetSynchroServiceImpl extends AbstractCommonBusinessService<ProjectBudgetFeeItemDto> implements ProjectBudgetSynchroService {

    @Resource
    private ProjectBudgetSynchroMapper projectBudgetSynchroMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private EsbService esbService;
    @Resource
    private ProjectFeeCollectionService projectFeeCollectionService;
    @Resource
    private GEMSCarrierServicel gemsCarrierServicel;


    @Override
    public int insert(ProjectBudgetSynchro record) {
        return projectBudgetSynchroMapper.insert(record);
    }

    @Override
    public int insertSelective(ProjectBudgetSynchro record) {
        return projectBudgetSynchroMapper.insertSelective(record);
    }

    @Override
    public List<ProjectBudgetSynchro> selectByExample(ProjectBudgetSynchroExample example) {
        return projectBudgetSynchroMapper.selectByExample(example);
    }

    @Override
    public ProjectBudgetSynchro selectByPrimaryKey(Long id) {
        return projectBudgetSynchroMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjectBudgetSynchro record) {
        return projectBudgetSynchroMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjectBudgetSynchro record) {
        return projectBudgetSynchroMapper.updateByPrimaryKey(record);
    }

    /**
     * 同步预算到外围系统.
     */
    @Override
    public void synchro() {
        final ProjectBudgetSynchroExample condition = new ProjectBudgetSynchroExample();
        condition.createCriteria().andStatusEqualTo(CommonStatus.TODO.getCode()).andDeletedFlagEqualTo(false);
        final List<ProjectBudgetSynchro> synchros = selectByExample(condition);
        if (ListUtils.isEmpty(synchros)) return;
        for (ProjectBudgetSynchro synchro : synchros) {
            final ProjectFeeCollectionExample feeCollectionExample = new ProjectFeeCollectionExample();
            feeCollectionExample.createCriteria().andProjectIdEqualTo(synchro.getProjectId()).andDeletedFlagEqualTo(false);
            final List<ProjectFeeCollection> collections = projectFeeCollectionService.selectByExample(feeCollectionExample);
            try {
                for (ProjectFeeCollection collection : collections) {
                    final ProjectDto projectDto = projectService.findDetail(synchro.getProjectId());
                    final ProjectBudgetFeeItemDto budgetFeeItemDto = new ProjectBudgetFeeItemDto();
                    final FeeItemDto feeItem = CacheDataUtils.findFeeItemById(collection.getFeeItemId());
                    BeanUtils.copyProperties(projectDto, budgetFeeItemDto);
                    budgetFeeItemDto.setAmount(collection.getAmount());
                    budgetFeeItemDto.setEmsFeeTypeId(feeItem.getEmsFeeTypeId());
                    budgetFeeItemDto.setFeeTypeCode(feeItem.getFeeTypeCode());
                    budgetFeeItemDto.setFeeTypeName(feeItem.getFeeTypeName());
                    budgetFeeItemDto.setId(collection.getId());
                    budgetFeeItemDto.setCreatedAt(collection.getCreateAt());
                    UserInfo userInfo = CacheDataUtils.findUserById(collection.getCreateBy());
                    if (userInfo != null) {
                        budgetFeeItemDto.setCreatedByCode(userInfo.getUsername());
                    }
//                    esbService.callGemsCreateBudgetNode(budgetFeeItemDto);
                    gemsCarrierServicel.createBudgetNode(budgetFeeItemDto);
                }
            } catch (Exception e) {
                synchro.setStatus(CommonStatus.ERROR.getCode());
            } finally {
                synchro.setStatus(CommonStatus.DONE.getCode());
                projectBudgetSynchroMapper.updateByPrimaryKey(synchro);
            }

        }
    }

    @Override
    public void init(final ResendExecute resendExecute) {
        ProjectFeeCollection feeCollection = projectFeeCollectionService.selectByPrimaryKey(Long.parseLong(resendExecute.getApplyNo()));
        Assert.notNull(feeCollection, "费用类型不存在");
        final ProjectDto projectDto = projectService.findDetail(feeCollection.getProjectId());
        Assert.notNull(projectDto, "项目不存在");
        final ProjectBudgetFeeItemDto budgetFeeItemDto = new ProjectBudgetFeeItemDto();
        final FeeItemDto feeItem = CacheDataUtils.findFeeItemById(feeCollection.getFeeItemId());
        BeanUtils.copyProperties(projectDto, budgetFeeItemDto);
        if (BooleanUtils.isTrue(projectDto.getWbsEnabled())) {// wbs
            budgetFeeItemDto.setWbsCode(feeCollection.getWbsCode());
            budgetFeeItemDto.setWbsName(feeCollection.getWbsName());
        }
        budgetFeeItemDto.setAmount(feeCollection.getAmount());
        budgetFeeItemDto.setEmsFeeTypeId(null != feeItem ? feeItem.getEmsFeeTypeId() : null);
        budgetFeeItemDto.setFeeTypeCode(null != feeItem ? feeItem.getFeeTypeCode() : null);
        budgetFeeItemDto.setFeeTypeName(null != feeItem ? feeItem.getFeeTypeName() : null);
        budgetFeeItemDto.setId(feeCollection.getId());
        budgetFeeItemDto.setCreatedAt(feeCollection.getCreateAt());
        UserInfo userInfo = CacheDataUtils.findUserById(feeCollection.getCreateBy());
        if (userInfo != null) {
            budgetFeeItemDto.setCreatedByCode(userInfo.getUsername());
        }
        setInfo(budgetFeeItemDto);
    }

    @Override
    public void validate(ResendExecute resendExecute) {
        Assert.notNull(info, "费用类型不存在");
        Assert.notNull(info.getFeeTypeCode(), "经济事项不能为空");
        Assert.notNull(info.getBudgetHeaderId(), "预算树头信息不能为空");
        Assert.notNull(info.getBudgetDepCode(), "预算树部门不能为空");
        Assert.notNull(info.getCode(), "项目号不能为空");
    }

    /**
     * 执行发送报文.
     *
     * @param resendExecute
     */
    @Override
    public EsbResponse execute(ResendExecute resendExecute) {

        if (BooleanUtils.isTrue(info.getWbsEnabled())) {
            // 判断009接口调用成功才执行
            CarrierResponse carrierResponse = gemsCarrierServicel.saveProjWbsRel(info);
            if (Objects.isNull(carrierResponse)) {
                EsbResponse esbResponse = new EsbResponse();
                esbResponse.setResponsecode("999999");
                esbResponse.setResponsemessage("wbs预算009接口推ems，返回为空");
                return esbResponse;
            }
            if (Objects.nonNull(carrierResponse) && !carrierResponse.getResponsecode().equals("0000")) {
                EsbResponse esbResponse = new EsbResponse();
                esbResponse.setResponsecode("999999");
                esbResponse.setResponsemessage(carrierResponse.getResponsemessage());
                return esbResponse;
            }
            if (Objects.nonNull(carrierResponse) && Objects.equals("0000", carrierResponse.getResponsecode())) {
//                return esbService.callGemsCreateBudgetNode(info);
                return gemsCarrierServicel.createBudgetNode(info);
            }
        } else {
//            return esbService.callGemsCreateBudgetNode(info);
            return gemsCarrierServicel.createBudgetNode(info);
        }
        return null;
    }
}
