package com.midea.pam.ctc.service.listener;

import com.midea.pam.common.ctc.dto.WorkflowCallbackCommonDto;
import com.midea.pam.common.ctc.entity.PurchaseOrder;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetail;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderExample;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.ctc.common.enums.PurchaseOrderDetailStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderStatus;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.impl.PurchaseOrderCallBackServiceImpl;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderMapper;
import com.midea.pam.ctc.service.PurchaseOrderService;
import com.midea.pam.ctc.service.event.PurchaseOrderCallBackApprovalEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class PurchaseOrderCallBackApprovalListener implements ApplicationListener<PurchaseOrderCallBackApprovalEvent> {

    private final static Logger logger = LoggerFactory.getLogger(PurchaseOrderCallBackApprovalListener.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    private final static Long MAX_WAIT_TIME_SYNC = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private PurchaseOrderExtMapper purchaseOrderExtMapper;
    @Resource
    private PurchaseOrderDetailExtMapper purchaseOrderDetailExtMapper;
    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;
    @Resource
    private PurchaseOrderCallBackServiceImpl purchaseOrderCallBackService;
    @Resource
    private WorkflowCallbackService workflowCallbackService;
    @Resource
    private PurchaseOrderService purchaseOrderService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @Async
    public void onApplicationEvent(final PurchaseOrderCallBackApprovalEvent event) {
        executeCommon(event.getWorkflowCallbackCommonDto());
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeCommon(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("采购订单审批公共回调的异步处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("PurchaseOrderCallBackApproval_executeCommon_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = Objects.equals(eventName, "processAbandonEvent") || Objects.equals(eventName, "delete")
                            ? workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl)
                            : workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    switch (eventName) {
                        case "draftSubmitEvent":
                            this.update(formInstanceId, PurchaseOrderStatus.PENDING.code(), PurchaseOrderDetailStatus.ORDER_RELEASED.code());
                            // 更新采购需求的关闭状态
                            purchaseOrderCallBackService.updateRequirementStatusByOrderId(formInstanceId.toString());
                            break;
                        case "processEndEvent":
                            PurchaseOrderExample example = new PurchaseOrderExample();
                            example.createCriteria().andReceiptsIdEqualTo(formInstanceId);
                            List<PurchaseOrder> purchaseOrderList = purchaseOrderMapper.selectByExample(example);
                            for (int j = 0; j < purchaseOrderList.size(); j++) {
                                PurchaseOrder purchaseOrder = purchaseOrderList.get(j);
                                Long ouId = purchaseOrder.getOuId();
                                String vendorNum = purchaseOrder.getVendorNum();
                                Boolean vendorMatchingConfigAndPushToFap = purchaseOrderService.isVendorMatchingConfigAndPushToFap(ouId, vendorNum);
                                if(vendorMatchingConfigAndPushToFap){
                                    HandleDispatcher.route(BusinessTypeEnums.PURCHASE_ORDER_FAP.getCode(), String.valueOf(purchaseOrderList.get(j).getId()),
                                            null, null, true, null, null);
                                    purchaseOrderService.updateSyncToFapStatus(purchaseOrderList.get(j).getId());
                                }else{
                                    HandleDispatcher.route(BusinessTypeEnums.PURCHASE_ORDER.getCode(), String.valueOf(purchaseOrderList.get(j).getId()),
                                            null, null, true, null, null);
                                }
                            }

                            this.update(formInstanceId, PurchaseOrderStatus.APPROVED.code(), PurchaseOrderDetailStatus.ORDER_PLACED.code());
                            // 更新采购需求的关闭状态
                            purchaseOrderCallBackService.updateRequirementStatusByOrderId(formInstanceId.toString());
                            break;
                        case "handleRefuseEvent":
                            this.update(formInstanceId, PurchaseOrderStatus.REFUSE.code(), PurchaseOrderDetailStatus.ORDER_RELEASED.code());
                            // 更新采购需求的关闭状态
                            purchaseOrderCallBackService.updateRequirementStatusByOrderId(formInstanceId.toString());
                            break;
                        case "processAbandonEvent":
                            this.update(formInstanceId, PurchaseOrderStatus.ABANDON.code(), null);
                            // 更新采购需求的关闭状态
                            purchaseOrderCallBackService.updateRequirementStatusByOrderId(formInstanceId.toString());
                            break;
                        case "draftReturnEvent":
                            this.update(formInstanceId, PurchaseOrderStatus.WAIT_ENABLE.code(), PurchaseOrderDetailStatus.ORDER_RELEASED.code());
                            // 更新采购需求的关闭状态
                            purchaseOrderCallBackService.updateRequirementStatusByOrderId(formInstanceId.toString());
                            break;
                        case "delete":
                            this.update(formInstanceId, PurchaseOrderStatus.ABANDON.code(), null);
                            // 更新采购需求的关闭状态
                            purchaseOrderCallBackService.updateRequirementStatusByOrderId(formInstanceId.toString());
                            break;
                        default:
                            break;
                    }

                    if (Objects.equals(eventName, "processEndEvent")) {
                        workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                                formInstance, true);
                    } else {
                        workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                                formInstance, false);
                    }
                } catch (ApplicationBizException e) {
                    logger.info("采购订单审批公共回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("采购订单审批公共回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("采购订单审批公共回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(Long formInstanceId, Integer status, Integer detailStatus) {
        if (null == formInstanceId) {
            return;
        }

        String lockName = String.format("PurchaseOrderCallBackApproval.updatePurchaseOrderAndPurchaseOrderDetail_%s", formInstanceId);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME_SYNC, MAX_WAIT_TIME_SYNC * 2)) {
                List<Long> updatePurchaseOrderIdList = new ArrayList<>();
                List<PurchaseOrder> updatePurchaseOrderList = new ArrayList<>();
                PurchaseOrderExample purchaseOrderExample = new PurchaseOrderExample();
                purchaseOrderExample.createCriteria().andReceiptsIdEqualTo(formInstanceId);
                List<PurchaseOrder> purchaseOrderList = purchaseOrderMapper.selectByExample(purchaseOrderExample);
                for (PurchaseOrder purchaseOrder : purchaseOrderList) {
                    updatePurchaseOrderIdList.add(purchaseOrder.getId());
                    PurchaseOrder updatePurchaseOrder = new PurchaseOrder();
                    updatePurchaseOrder.setId(purchaseOrder.getId());
                    updatePurchaseOrder.setOrderStatus(status);
                    if(Objects.equals(status,PurchaseOrderStatus.APPROVED.code())){
                        // 审批通过时间
                        updatePurchaseOrder.setApprovalTime(new Date());
                    }
                    updatePurchaseOrderList.add(updatePurchaseOrder);
                }

                List<PurchaseOrderDetail> updatePurchaseOrderDetailList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(updatePurchaseOrderIdList)) {
                    PurchaseOrderDetailExample purchaseOrderDetailExample = new PurchaseOrderDetailExample();
                    purchaseOrderDetailExample.createCriteria().andPurchaseOrderIdIn(updatePurchaseOrderIdList);
                    List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderDetailMapper.selectByExample(purchaseOrderDetailExample);
                    for (PurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetailList) {
                        PurchaseOrderDetail updatePurchaseOrderDetail = new PurchaseOrderDetail();
                        updatePurchaseOrderDetail.setId(purchaseOrderDetail.getId());
                        updatePurchaseOrderDetail.setStatus(detailStatus);
                        updatePurchaseOrderDetailList.add(updatePurchaseOrderDetail);
                    }
                }

                if (CollectionUtils.isNotEmpty(updatePurchaseOrderList)) {
                    purchaseOrderExtMapper.batchUpdate(updatePurchaseOrderList);
                }
                if (CollectionUtils.isNotEmpty(updatePurchaseOrderDetailList)) {
                    purchaseOrderDetailExtMapper.batchUpdateOnlyStatus(updatePurchaseOrderDetailList);
                }
            }
        } catch (Exception e) {
            logger.error("updatePurchaseOrderAndPurchaseOrderDetail修改采购订单以及详情的status加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }

    }

}
