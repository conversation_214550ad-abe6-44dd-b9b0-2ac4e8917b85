package com.midea.pam.ctc.wbs.service.impl;

import com.alibaba.fastjson.JSON;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleDetailCache;
import com.midea.pam.common.ctc.dto.WbsDynamicFieldsDto;
import com.midea.pam.common.ctc.dto.WbsTemplateRuleDetailDto;
import com.midea.pam.common.ctc.dto.WbsTemplateRuleDto;
import com.midea.pam.common.ctc.entity.WbsCustomizeRule;
import com.midea.pam.common.ctc.entity.WbsCustomizeRuleExample;
import com.midea.pam.common.ctc.entity.WbsTemplateInfo;
import com.midea.pam.common.ctc.entity.WbsTemplateInfoExample;
import com.midea.pam.common.ctc.entity.WbsTemplateRule;
import com.midea.pam.common.ctc.entity.WbsTemplateRuleDetail;
import com.midea.pam.common.ctc.entity.WbsTemplateRuleDetailExample;
import com.midea.pam.common.ctc.entity.WbsTemplateRuleExample;
import com.midea.pam.common.ctc.excelVo.WbsTemplateRuleDetailExcelVO;
import com.midea.pam.common.enums.DescribeDisplayEnum;
import com.midea.pam.common.enums.WbsTemplateRuleDetailParentStateEnums;
import com.midea.pam.common.util.BeanMapTool;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.cache.WbsTemplateRuleDetailCacheUtils;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.WbsCustomizeRuleMapper;
import com.midea.pam.ctc.mapper.WbsTemplateInfoExtMapper;
import com.midea.pam.ctc.mapper.WbsTemplateInfoMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleDetailExtMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleDetailMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleExtMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleMapper;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleDetailService;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description
 * Created by liuqing
 * Date 2022/4/1 14:37
 */
public class WbsTemplateRuleDetailServiceImpl implements WbsTemplateRuleDetailService {

    private static final Logger log = LoggerFactory.getLogger(WbsTemplateRuleDetailServiceImpl.class);

    @Resource
    private WbsTemplateInfoMapper wbsTemplateInfoMapper;
    @Resource
    private WbsTemplateRuleMapper wbsTemplateRuleMapper;
    @Resource
    private WbsTemplateRuleDetailMapper wbsTemplateRuleDetailMapper;
    @Resource
    private WbsTemplateRuleDetailExtMapper wbsTemplateRuleDetailExtMapper;
    @Resource
    private WbsTemplateInfoExtMapper wbsTemplateInfoExtMapper;
    @Resource
    private WbsTemplateRuleExtMapper wbsTemplateRuleExtMapper;
    @Resource
    private WbsTemplateRuleService wbsTemplateRuleService;
    @Resource
    private WbsCustomizeRuleMapper wbsCustomizeRuleMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importWbsTemplateRuleDetail(List<WbsTemplateRuleDetailExcelVO> detailExcelVOList) {
        Guard.notNullOrEmpty(detailExcelVOList, "导入数据为空");
        String ruleName = detailExcelVOList.get(0).getRuleName();
        Guard.notNullOrEmpty(ruleName, "导入的模板的层级定义为空");
        String templateName = detailExcelVOList.get(0).getTemplateName();
        Guard.notNullOrEmpty(templateName, "导入的模板名称为空");

        WbsTemplateInfoExample wbsTemplateInfoExample = new WbsTemplateInfoExample();
        wbsTemplateInfoExample.createCriteria()
                .andDeletedFlagEqualTo(false)
                .andTemplateNameEqualTo(templateName)
                .andUnitIdEqualTo(SystemContext.getUnitId());
        List<WbsTemplateInfo> wbsTemplateInfoList = wbsTemplateInfoMapper.selectByExample(wbsTemplateInfoExample);
        Guard.notNullOrEmpty(wbsTemplateInfoList, String.format("当前单位下模板名称：%s对应的wbs模板信息不存在", templateName));
        // templateName + unitId 唯一
        Long wbsTemplateInfoId = wbsTemplateInfoList.get(0).getId();

        WbsTemplateRuleExample wbsTemplateRuleExample = new WbsTemplateRuleExample();
        wbsTemplateRuleExample.createCriteria()
                .andDeletedFlagEqualTo(false)
                .andWbsTemplateInfoIdEqualTo(wbsTemplateInfoId)
                .andRuleNameEqualTo(ruleName);
        List<WbsTemplateRule> wbsTemplateRuleList = wbsTemplateRuleMapper.selectByExample(wbsTemplateRuleExample);
        Guard.notNullOrEmpty(wbsTemplateRuleList,
                String.format("当前单位下，模板名称：%s，层级定义：%s对应的wbs模板规则不存在", templateName, ruleName));
        // wbsTemplateInfoId + ruleName 唯一
        Long wbsTemplateRuleId = wbsTemplateRuleList.get(0).getId();

        //根据序号的长度groupingBy分组
        Map<Integer, List<WbsTemplateRuleDetailExcelVO>> excelVOMap = detailExcelVOList.stream()
                .collect(Collectors.groupingBy(WbsTemplateRuleDetailExcelVO::getOrderNoLength));
        List<WbsTemplateRuleDetail> wbsTemplateRuleDetailList = new ArrayList<>();
        for (WbsTemplateRuleDetailExcelVO wbsTemplateRuleDetailExcelVO : detailExcelVOList) {
            WbsTemplateRuleDetail wbsTemplateRuleDetail = BeanConverter.copy(wbsTemplateRuleDetailExcelVO,
                    WbsTemplateRuleDetail.class);
            wbsTemplateRuleDetail.setWbsTemplateRuleId(wbsTemplateRuleId);
            // 序号
            String orderNo = wbsTemplateRuleDetailExcelVO.getOrderNo();
            Integer orderNoLength = wbsTemplateRuleDetailExcelVO.getOrderNoLength();
            if (orderNoLength > 1) {
                // 找上一层的序号, 启用日期上层要包含下层	失效日v期上层要包含下层
                List<WbsTemplateRuleDetailExcelVO> voList = excelVOMap.get(orderNoLength - 1);
                Guard.notNullOrEmpty(voList, String.format("序号：%s找不到父级序号", orderNo));
                WbsTemplateRuleDetailExcelVO parentDetailExcelVO = voList.stream()
                        .filter(e -> orderNo.startsWith(e.getOrderNo()))
                        .findFirst()
                        .orElse(null);
                if (null == parentDetailExcelVO) {
                    throw new ApplicationBizException(String.format("序号：%s找不到父级序号", orderNo));
                }
                // 如果 excel填写的是否父级为”否“的数据不能有下层序号
                if (WbsTemplateRuleDetailParentStateEnums.NO.getCode().equals(wbsTemplateRuleDetailExcelVO.getParentStateStr())) {
                    List<WbsTemplateRuleDetailExcelVO> list = excelVOMap.get(orderNoLength + 1);
                    if (CollectionUtils.isNotEmpty(list)) {
                        WbsTemplateRuleDetailExcelVO childDetailExcelVO = list.stream()
                                .filter(e -> e.getOrderNo().startsWith(orderNo))
                                .findFirst()
                                .orElse(null);
                        if (Objects.nonNull(childDetailExcelVO)) {
                            throw new ApplicationBizException(String.format("序号：%s不是父级,但是找到了下层序号：%s",
                                    orderNo,
                                    childDetailExcelVO.getOrderNo()));
                        }
                    }
                }

                Date startTime = wbsTemplateRuleDetailExcelVO.getStartTime();
                Date endTime = wbsTemplateRuleDetailExcelVO.getEndTime();
                Date parentStartTime = parentDetailExcelVO.getStartTime();
                Date parentEndTime = parentDetailExcelVO.getEndTime();
                if (parentStartTime.compareTo(startTime) < 0) {
                    throw new ApplicationBizException(String.format("父级序号：%s的启动日期小于子序号：%s的启动日期",
                            parentDetailExcelVO.getOrderNo(),
                            orderNo));
                }
                if ((Objects.isNull(parentEndTime) && Objects.nonNull(endTime)) || (Objects.nonNull(parentEndTime) && Objects.nonNull(
                        endTime) && parentEndTime.compareTo(endTime) < 0)) {
                    throw new ApplicationBizException(String.format("父级序号：%s的失效日期小于子序号：%s的失效日期",
                            parentDetailExcelVO.getOrderNo(),
                            orderNo));
                }
            }

            // 编码
            String code = wbsTemplateRuleDetailExcelVO.getCode();
            WbsTemplateRuleDetailExample example = new WbsTemplateRuleDetailExample();
            example.createCriteria()
                    .andDeletedFlagEqualTo(false)
                    .andWbsTemplateRuleIdEqualTo(wbsTemplateRuleId)
                    .andCodeEqualTo(code);
            List<WbsTemplateRuleDetail> detailList = wbsTemplateRuleDetailMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(detailList)) {
                throw new ApplicationBizException(String.format("序号：%s这条数据的code:%s在wbs模板规则详情表中已存在", orderNo, code));
            }

            //层级
            wbsTemplateRuleDetail.setLevel(WbsTemplateRuleDetailParentStateEnums.YES.getMsg()
                    .equals(wbsTemplateRuleDetailExcelVO.getParentStateStr()) ? String.valueOf(orderNoLength) : "明细");

            // 最后更新人
            String userName = wbsTemplateRuleDetailExcelVO.getUpdateByCn();
            if (StringUtils.isNotEmpty(userName)) {
                UserInfo userInfo = CacheDataUtils.findUserByMip(userName);
                Guard.notNull(userInfo, String.format("最后更新人：%s对应的用户不存在", userName));
                wbsTemplateRuleDetail.setUpdateBy(userInfo.getId());
            } else {
                wbsTemplateRuleDetail.setUpdateBy(SystemContext.getUserId());
                wbsTemplateRuleDetail.setUpdateAt(new Date());
            }

            wbsTemplateRuleDetail.setCreateBy(SystemContext.getUserId());
            wbsTemplateRuleDetail.setCreateAt(new Date());
            wbsTemplateRuleDetail.setDeletedFlag(false);
            wbsTemplateRuleDetailList.add(wbsTemplateRuleDetail);
            log.info("导入的模板sheet名称：{}的导入数据：{}", ruleName, JSON.toJSONString(wbsTemplateRuleDetail));
        }
        if (CollectionUtils.isNotEmpty(wbsTemplateRuleDetailList)) {
            wbsTemplateRuleDetailExtMapper.batchInsert(wbsTemplateRuleDetailList);
        }
        // 更新缓存
        saveCache(wbsTemplateRuleId);

    }

    /**
     * 查询层级
     *
     * @param wbstemplateRuleId wbs模板规则id
     * @return
     */
    @Override
    public List<String> findLevelList(Long wbstemplateRuleId) {
        return wbsTemplateRuleDetailExtMapper.findLevelList(wbstemplateRuleId);
    }

    /**
     * 根据id查询wbs模板信息、模板规则详情
     *
     * @param param
     * @return
     */
    @Override
    public List<WbsTemplateRuleDetailDto> findList(WbsTemplateRuleDetailDto param) {
        param.setDeletedFlag(false);

        // 1、查询所有筛选过的数据
        List<WbsTemplateRuleDetailDto> ruleDetailDtoList = wbsTemplateRuleDetailExtMapper.selectList(param);
        if (CollectionUtils.isEmpty(ruleDetailDtoList)) {
            return new ArrayList<>();
        }
        // 2、转成Map，遍历查出缺失的上层序号做二次查询
        Map<String, WbsTemplateRuleDetailDto> orderNoMap = ruleDetailDtoList.stream()
                .collect(Collectors.toMap(WbsTemplateRuleDetailDto::getOrderNo, a -> a, (k1, k2) -> k1));
        // 缺失的上层orderNo
        List<String> missParentOrderNo = new ArrayList<>();
        for (Map.Entry<String, WbsTemplateRuleDetailDto> entry : orderNoMap.entrySet()) {
            checkParentOrderNo(entry.getKey(), orderNoMap, missParentOrderNo);
        }
        if (!CollectionUtils.isEmpty(missParentOrderNo)) {
            WbsTemplateRuleDetailDto param2 = new WbsTemplateRuleDetailDto();
            param2.setDeletedFlag(false);
            param2.setWbsTemplateRuleId(param.getWbsTemplateRuleId());
            param2.setOrderNoList(missParentOrderNo);
            List<WbsTemplateRuleDetailDto> missParentRuleDetailList = wbsTemplateRuleDetailExtMapper.selectList(param2);
            // 合并
            ruleDetailDtoList.addAll(missParentRuleDetailList);
            // 将数据再重新装成Map
            orderNoMap = ruleDetailDtoList.stream()
                    .collect(Collectors.toMap(WbsTemplateRuleDetailDto::getOrderNo, a -> a, (k1, k2) -> k1));
        }

        // 3、从第一层orderNo开始组装树结构
        List<WbsTemplateRuleDetailDto> firstNode = new ArrayList<>();
        for (Map.Entry<String, WbsTemplateRuleDetailDto> entry : orderNoMap.entrySet()) {
            if (entry.getValue().getOrderNo().indexOf(".") <= 0) {
                firstNode.add(buildTree(entry.getKey(), orderNoMap));
            }
        }
        // 排序
        Collections.sort(firstNode);
        return firstNode;
    }

    private void checkParentOrderNo(String orderNo,
                                    Map<String, WbsTemplateRuleDetailDto> orderNoMap,
                                    List<String> missParentOrderNo) {
        if (!orderNoMap.containsKey(orderNo)) {
            missParentOrderNo.add(orderNo);
        }
        if (orderNo.contains(".")) {
            checkParentOrderNo(StringUtils.substringBeforeLast(orderNo, "."), orderNoMap, missParentOrderNo);
        }
    }

    private WbsTemplateRuleDetailDto buildTree(String parentOrderNo, Map<String, WbsTemplateRuleDetailDto> orderNoMap) {

        WbsTemplateRuleDetailDto dto = orderNoMap.get(parentOrderNo);
        // 更新人
        UserInfo userInfo = CacheDataUtils.findUserById(dto.getUpdateBy());
        if (null != userInfo) {
            dto.setUpdateByCn(userInfo.getName());
        }

        List<WbsTemplateRuleDetailDto> childs = new ArrayList<>();
        orderNoMap.forEach((k, v) -> {
            if (k.indexOf(".") >= 0 && StringUtils.substringBeforeLast(k, ".").equals(parentOrderNo)) {
                childs.add(buildTree(k, orderNoMap));
            }
        });
        // 排序
        Collections.sort(childs);
        dto.setChilds(childs);
        return dto;
    }

    @Override
    @Transactional
    public Boolean save(WbsTemplateRuleDto wbsTemplateRuleDto) {
        recursiveSave(wbsTemplateRuleDto.getId(), wbsTemplateRuleDto.getTemplateRuleDetailList());
        // 更新缓存
        return saveCache(wbsTemplateRuleDto.getId());
    }

    /**
     * 递归保存
     *
     * @param wbsTemplateRuleId wbs规则id
     * @param ruleDetailDtoList
     */
    public void recursiveSave(Long wbsTemplateRuleId, List<WbsTemplateRuleDetailDto> ruleDetailDtoList) {
        // 同层级编码重复校验
        if (!CollectionUtils.isEmpty(ruleDetailDtoList)) {
            int codeSize = ruleDetailDtoList.stream().collect(Collectors.groupingBy(WbsTemplateRuleDetail::getCode)).size();
            if (codeSize != ruleDetailDtoList.size()) {
                throw new ApplicationBizException("编码不能重复");
            }
        }
        WbsTemplateRuleDetail entity;
        for (WbsTemplateRuleDetailDto ruleDetailDto : ruleDetailDtoList) {

            if (!CollectionUtils.isEmpty(ruleDetailDto.getChilds())) {
                recursiveSave(wbsTemplateRuleId, ruleDetailDto.getChilds());
            }

            // 保存标记为true，页面有变动才保存
            if (!Boolean.TRUE.equals(ruleDetailDto.getSaveFlag())) {
                continue;
            }
            if (!Boolean.TRUE.equals(ruleDetailDto.getParentState())) {
                ruleDetailDto.setLevel("明细");
            }
            entity = new WbsTemplateRuleDetailDto();
            // 新增
            if (null == ruleDetailDto.getId()) {
                BeanUtils.copyProperties(ruleDetailDto, entity);
                entity.setWbsTemplateRuleId(wbsTemplateRuleId);
                entity.setCreateBy(SystemContext.getUserId());
                entity.setCreateAt(new Date());
                entity.setUpdateBy(SystemContext.getUserId());
                entity.setUpdateAt(new Date());
                entity.setVersion(1L);
                entity.setDeletedFlag(false);
                wbsTemplateRuleDetailMapper.insert(entity);
            }
            // 删除
            else if (Boolean.TRUE.equals(ruleDetailDto.getDeletedFlag())) {
                BeanUtils.copyProperties(ruleDetailDto, entity);
                entity.setDeletedFlag(true);
                entity.setUpdateBy(SystemContext.getUserId());
                entity.setUpdateAt(new Date());
                wbsTemplateRuleDetailMapper.updateByPrimaryKey(entity);
            }
            // 修改
            else {
                BeanUtils.copyProperties(ruleDetailDto, entity);
                entity.setDeletedFlag(false);
                entity.setUpdateBy(SystemContext.getUserId());
                entity.setUpdateAt(new Date());
                wbsTemplateRuleDetailMapper.updateByPrimaryKey(entity);
            }
        }
    }

    /**
     * 导出
     *
     * @param wbsTemplateRuleId
     * @return
     */
    @Override
    public List<WbsTemplateRuleDetailExcelVO> export(Long wbsTemplateRuleId) {
        WbsTemplateRuleDetailDto param = new WbsTemplateRuleDetailDto();
        param.setDeletedFlag(false);
        param.setWbsTemplateRuleId(wbsTemplateRuleId);
        List<WbsTemplateRuleDetailExcelVO> result = new ArrayList<>();
        List<WbsTemplateRuleDetailDto> ruleDetailDtoList = wbsTemplateRuleDetailExtMapper.selectList(param);
        if (!CollectionUtils.isEmpty(ruleDetailDtoList)) {
            WbsTemplateRuleDetailExcelVO excelVO;
            for (WbsTemplateRuleDetailDto ruleDetailDto : ruleDetailDtoList) {
                excelVO = new WbsTemplateRuleDetailExcelVO();
                excelVO.setOrderNo(ruleDetailDto.getOrderNo());
                excelVO.setCode(ruleDetailDto.getCode());
                excelVO.setDescription(ruleDetailDto.getDescription());
                excelVO.setRemark(ruleDetailDto.getRemark());
                excelVO.setStartTime(ruleDetailDto.getStartTime());
                excelVO.setEndTime(ruleDetailDto.getEndTime());
                excelVO.setLevel(ruleDetailDto.getLevel());
                excelVO.setParentStateStr(Boolean.TRUE.equals(ruleDetailDto.getParentState()) ? WbsTemplateRuleDetailParentStateEnums.YES.getMsg() : WbsTemplateRuleDetailParentStateEnums.NO.getMsg());
                // 更新人
                UserInfo userInfo = CacheDataUtils.findUserById(ruleDetailDto.getUpdateBy());
                if (null != userInfo) {
                    excelVO.setUpdateByCn(userInfo.getName());
                    excelVO.setUpdateAt(userInfo.getUpdateAt());
                }
                result.add(excelVO);
            }
        }
        return result;

    }

    /**
     * 更新缓存
     *
     * @param wbsTemplateRuleId wbs模板规则id
     * @return
     */
    @Override
    public boolean saveCache(Long wbsTemplateRuleId) {
        WbsTemplateRuleDetailExample example = new WbsTemplateRuleDetailExample();
        example.createCriteria()
                .andDeletedFlagEqualTo(false)
                .andWbsTemplateRuleIdEqualTo(wbsTemplateRuleId);
        List<WbsTemplateRuleDetail> list = wbsTemplateRuleDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        List<WbsTemplateRuleDetailCache> cacheList = BeanConverter.copy(list, WbsTemplateRuleDetailCache.class);
        WbsTemplateRuleDetailCacheUtils.removeAllCache(wbsTemplateRuleId);
        return WbsTemplateRuleDetailCacheUtils.putCacheBatch(cacheList);
    }

    @Override
    public String getLastWbs(WbsTemplateInfo wbsTemplateInfo, String wbsFullCode, WbsTemplateRuleDetailCache wbsCache, Map<String, String> wbsCustomizeRuleMap) {
        String description = "";

        if (wbsCache != null) {
            description = wbsCache.getDescription();
        }

        if (DescribeDisplayEnum.CUSTOMIZE.getCode().equals(wbsTemplateInfo.getDescribeDisplay())
                && StringUtils.isNotEmpty(wbsFullCode) && wbsCustomizeRuleMap.get(wbsFullCode) != null) {
            description = wbsCustomizeRuleMap.get(wbsFullCode);
        }
        return description;
    }


    @Override
    public Map<String, Object> getLastWbs(Long wbsTemplateInfoId, String code, String dynamicCombination) {
        WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(wbsTemplateInfoId);

        List<WbsDynamicFieldsDto> wbsDynamicFieldsDtoList = wbsTemplateRuleService.getWbsDynamicFields(wbsTemplateInfoId);
        if (CollectionUtils.isEmpty(wbsDynamicFieldsDtoList)) {
            return new HashMap<>();
        }

        String[] levelValues = dynamicCombination.split("-");
        if (levelValues.length != wbsDynamicFieldsDtoList.size()) {
            throw new ApplicationBizException(dynamicCombination + "与字段层级不匹配");
        }

        String description = getDescriptionByLevels(wbsDynamicFieldsDtoList, levelValues);

        if (DescribeDisplayEnum.CUSTOMIZE.getCode().equals(wbsTemplateInfo.getDescribeDisplay())) {
            return handleCustomizeMode(wbsTemplateInfoId, dynamicCombination, description);
        } else {
            return handleStandardMode(description);
        }
    }

    /**
     * 从最后一级开始向上查询description
     */
    private String getDescriptionByLevels(List<WbsDynamicFieldsDto> fieldsDtoList, String[] levelValues) {
        List<WbsDynamicFieldsDto> orderedFields = fieldsDtoList.stream()
                .sorted(Comparator.comparing(dto -> Integer.parseInt(dto.getKey().substring(6))))
                .collect(Collectors.toList());

        for (int i = orderedFields.size() - 1; i >= 0; i--) {
            WbsDynamicFieldsDto currentDto = orderedFields.get(i);
            String currentLevelValue = levelValues[i];

            WbsTemplateRuleDetailExample example = new WbsTemplateRuleDetailExample();
            example.createCriteria()
                    .andDeletedFlagEqualTo(false)
                    .andLevelEqualTo("明细")
                    .andWbsTemplateRuleIdEqualTo(currentDto.getWbsTemplateRuleId())
                    .andCodeEqualTo(currentLevelValue);

            List<WbsTemplateRuleDetail> details = wbsTemplateRuleDetailMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(details)) {
                String desc = details.get(0).getDescription();
                if (StringUtils.isNotBlank(desc)) {
                    return desc;
                }
            }
        }
        return "";
    }

    private Map<String, Object> handleCustomizeMode(Long templateInfoId, String dynamicCombination, String description) {
        WbsCustomizeRuleExample example = new WbsCustomizeRuleExample();
        example.createCriteria()
                .andWbsTemplateInfoIdEqualTo(templateInfoId)
                .andDynamicCombinationEqualTo(dynamicCombination)
                .andDeletedFlagEqualTo(false);

        List<WbsCustomizeRule> rules = wbsCustomizeRuleMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(rules)) {
            return BeanMapTool.beanToMap(rules.get(0));
        }
        return Collections.singletonMap("description", description);
    }

    private Map<String, Object> handleStandardMode(String description) {
        if (StringUtils.isNotBlank(description)) {
            WbsTemplateRuleDetail detail = new WbsTemplateRuleDetail();
            detail.setDescription(description);
            return BeanMapTool.beanToMap(detail);
        }
        return new HashMap<>();
    }

}
