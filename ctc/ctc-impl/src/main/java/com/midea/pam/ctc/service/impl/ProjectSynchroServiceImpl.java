package com.midea.pam.ctc.service.impl;

import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.entity.ProjectSynchro;
import com.midea.pam.common.ctc.entity.ProjectSynchroExample;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.mapper.ProjectSynchroMapper;
import com.midea.pam.ctc.mapper.ResendExecuteMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.GEMSCarrierServicel;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectSynchroService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

public class ProjectSynchroServiceImpl implements ProjectSynchroService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProjectSynchroServiceImpl.class);
    @Resource
    private ProjectSynchroMapper projectSynchroMapper;
    @Resource
    private ResendExecuteMapper resendExecuteMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private EsbService esbService;
    @Resource
    private GEMSCarrierServicel gemsCarrierServicel;


    @Override
    public int insert(ProjectSynchro record) {
        return projectSynchroMapper.insert(record);
    }

    @Override
    public int insertSelective(ProjectSynchro record) {
        return projectSynchroMapper.insertSelective(record);
    }

    @Override
    public List<ProjectSynchro> selectByExample(ProjectSynchroExample example) {
        return projectSynchroMapper.selectByExample(example);
    }

    @Override
    public ProjectSynchro selectByPrimaryKey(Long id) {
        return projectSynchroMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjectSynchro record) {
        return projectSynchroMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjectSynchro record) {
        return projectSynchroMapper.updateByPrimaryKey(record);
    }

    /**
     * 同步项目号到外围系统.
     */
    @Override
    public void synchro() {
        final ProjectSynchroExample condition = new ProjectSynchroExample();
        condition.createCriteria().andStatusEqualTo(CommonStatus.TODO.getCode()).andDeletedFlagEqualTo(false);
        final List<ProjectSynchro> synchros = selectByExample(condition);
        if (ListUtils.isEmpty(synchros)) return;
        for (ProjectSynchro synchro : synchros) {
            EsbResponse response = null;
            // 保存resend记录，失败的话支持重推
            ResendExecute resendExecute = new ResendExecute();
            final ProjectDto project = projectService.findDetail(synchro.getProjectId());
            try {
                // 当项目终止审批已通过时，通过接口卡statue字段，传16给到EMS系统
                // 当项目最后一个里程碑交付审批已通过，项目变为结项状态，通过接口卡statue字段，传10给到EMS系统
                // 当项目最后一个收入节点的里程碑交付审批已通过，项目变为收入确认已完成状态，通过接口卡statue字段，传7给到EMS系统，
                // 当项目已变为收入确认已完成状态，用户又新增收入节点导致最后一个收入节点里程碑已交付这一条件不成立时，再次传输4给到EMS系统
//                response =esbService.callGemsProjectNumberService(project, synchro.getType());
                response = gemsCarrierServicel.gemsProjectNumber(project, synchro.getType(), null);
                synchro.setStatus(Objects.equals(response.getResponsecode(), ResponseCodeEnums.SUCESS.getCode()) ?
                        CommonStatus.DONE.getCode() : CommonStatus.ERROR.getCode());
                synchro.setErrMsg(response.getResponsemessage());
                synchro.setEsbSerialNo(null != response.getData() ? String.valueOf(response.getData()) : null);

                resendExecute.setApplyNo(Long.toString(project.getId()));
                resendExecute.setBusinessType(BusinessTypeEnums.CREATE_PROJECT_CODE_EMS.getCode());
                resendExecute.setSubApplyNo(Long.toString(synchro.getId()));
                resendExecute.setEsbSerialNo(null != response.getData() ? String.valueOf(response.getData()) : null);
                resendExecute.setStatus(Objects.equals(response.getResponsecode(), ResponseCodeEnums.SUCESS.getCode()) ?
                        CommonStatus.DONE.getCode() : CommonStatus.ERROR.getCode());
                resendExecute.setResponCode(response.getResponsecode());
                resendExecute.setResponMsg(response.getResponsemessage());
                resendExecute.setBatch(Boolean.FALSE);
                resendExecute.setOuId(project.getOuId());
                resendExecute.setDeletedFlag(Boolean.FALSE);
            } catch (Exception e) {
                synchro.setStatus(CommonStatus.ERROR.getCode());
                synchro.setErrMsg(e.toString());

                resendExecute.setStatus(CommonStatus.ERROR.getCode());
                resendExecute.setResponMsg(e.toString());
                LOGGER.error("项目编号:{}的状态同步EMS发送失败，单据编号为:{}", synchro.getProjectId(), synchro.getId(), e);
            } finally {
                projectSynchroMapper.updateByPrimaryKey(synchro);
                resendExecuteMapper.insert(resendExecute);
            }
        }
    }

}
