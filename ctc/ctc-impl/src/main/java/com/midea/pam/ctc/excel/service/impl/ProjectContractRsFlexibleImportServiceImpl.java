package com.midea.pam.ctc.excel.service.impl;

import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UnitExample;
import com.midea.pam.common.ctc.entity.*;
import com.midea.pam.common.ctc.excelVo.ProjectContractRsImportExcelVo;
import com.midea.pam.ctc.mapper.*;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据初始化-项目合同关系
 */
public class ProjectContractRsFlexibleImportServiceImpl implements ProjectContractRsFlexibleImportService {

    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private ProjectContractRsMapper projectContractRsMapper;
    @Resource
    private UnitExtMapper unitExtMapper;
    @Resource
    private ProjectMilepostFlexibleImportService projectMilepostFlexibleImportService;
    @Resource
    private ProjectBudgetChangeSummaryHistoryMapper projectBudgetChangeSummaryHistoryMapper;

    /**
     * 数据初始化-项目合同关系
     *
     * @param excelVoList
     * @return
     */
    @Override
    public Response importData(List<ProjectContractRsImportExcelVo> excelVoList) {
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (CollectionUtils.isEmpty(excelVoList)) {
            throw new ApplicationBizException("excel导入数据不能为空");
        }
        List<ProjectContractRs> projectContractRsList = new ArrayList<>();

        // 校验数据
        boolean check = validData(excelVoList, projectContractRsList);
        if (!check) {
            dataResponse.setMsg("FAIL");
            return dataResponse.setData(excelVoList.stream().filter(a -> StringUtils.isNotBlank(a.getValidResult())).collect(Collectors.toList()));
        }
        if(CollectionUtils.isEmpty(projectContractRsList)){
            throw new ApplicationBizException("填写序号的数据才会执行导入");
        }
        // 保存数据
        saveData(projectContractRsList);

        // 计算合同金额
        calculationContractAmount(projectContractRsList.stream().map(ProjectContractRs::getProjectId).collect(Collectors.toList()));

        dataResponse.setMsg("SUCCESS");
        return dataResponse;
    }

    /**
     * 检查数据
     *
     * @param excelVoList
     * @param projectContractRsList
     * @return
     */
    private boolean validData(List<ProjectContractRsImportExcelVo> excelVoList, List<ProjectContractRs> projectContractRsList) {
        boolean check = true;

        // 项目map  key=projectCode value=project
        Map<String, Project> projectMap = new HashMap<>();
        Set<String> contractCodeSet = new HashSet<>();

        for (ProjectContractRsImportExcelVo excelVo : excelVoList) {
            // 序号为空，跳过导入
            if(StringUtils.isBlank(excelVo.getIndex())){
                continue;
            }
            if (StringUtils.isBlank(excelVo.getProjcetCode())) {
                excelVo.setValidResult("项目不能为空");
                check = false;
                continue;
            }
            Project project;
            if (projectMap.containsKey(excelVo.getProjcetCode())) {
                project = projectMap.get(excelVo.getProjcetCode());
            } else {
                project = getProjectByCode(excelVo.getProjcetCode());
            }
            if (null == project) {
                excelVo.setValidResult(String.format("项目%s不存在", excelVo.getProjcetCode()));
                check = false;
                continue;
            }
            projectMap.put(excelVo.getProjcetCode(), project);

            if (StringUtils.isBlank(excelVo.getContractCode())) {
                excelVo.setValidResult("合同不能为空");
                check = false;
                continue;
            }
            if(contractCodeSet.contains(excelVo.getContractCode())){
                excelVo.setValidResult(String.format("合同%s重复导入", excelVo.getContractCode()));
                check = false;
                continue;
            }
            contractCodeSet.add(excelVo.getContractCode());
            Contract contract = getContractByCode(excelVo.getContractCode());
            if (null == contract) {
                excelVo.setValidResult(String.format("合同%s不存在", excelVo.getContractCode()));
                check = false;
                continue;
            }

            ProjectContractRsExample example = new ProjectContractRsExample();
            example.createCriteria().andDeletedFlagEqualTo(false).andContractIdEqualTo(contract.getId());
            if (projectContractRsMapper.countByExample(example) > 0) {
                excelVo.setValidResult(String.format("合同%s已经被项目引用", excelVo.getContractCode()));
                check = false;
                continue;
            }
            ProjectContractRs rs = new ProjectContractRs();
            rs.setProjectId(project.getId());
            rs.setContractId(contract.getId());
            projectContractRsList.add(rs);
        }
        return check;
    }

    /**
     * 保存
     *
     * @param projectContractRsList
     * @return
     */
    private boolean saveData(List<ProjectContractRs> projectContractRsList) {
        for (ProjectContractRs projectContractRs : projectContractRsList) {
            projectContractRs.setDeletedFlag(false);
            projectContractRsMapper.insert(projectContractRs);
        }
        return true;
    }

    /**
     * 计算合同金额
     *
     * @param projectIdList 项目id
     */
    private void calculationContractAmount(List<Long> projectIdList){
        for (Long projectId : projectIdList) {
            ProjectContractRsExample example = new ProjectContractRsExample();
            example.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(projectId);
            List<ProjectContractRs> contractRsList = projectContractRsMapper.selectByExample(example);
            // 合同金额（含税）
            BigDecimal contractAmount = BigDecimal.ZERO;
            // 合同金额（不含税）
            BigDecimal contractExcludingTaxAmount = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(contractRsList)) {
                Contract parentContract = null;
                for(ProjectContractRs contractRs : contractRsList){
                    // 子合同信息
                    Contract contract = contractMapper.selectByPrimaryKey(contractRs.getContractId());
                    if(null == parentContract){
                        // 父合同
                        parentContract = contractMapper.selectByPrimaryKey(contract.getParentId());
                    }
                    // 汇率
                    BigDecimal conversionRate = BigDecimal.ONE;
                    if (parentContract != null && parentContract.getConversionRate() != null) {
                        conversionRate = parentContract.getConversionRate();
                    }
                    // 项目金额按照原币更新
                    contractAmount = contractAmount.add(contract.getAmount());
                    contractExcludingTaxAmount = contractExcludingTaxAmount.add(contract.getExcludingTaxAmount());
                }
            }
            Project project = projectMapper.selectByPrimaryKey(projectId);
            // 项目收入 = 合同不含税金额
            project.setAmount(contractExcludingTaxAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
            projectMapper.updateByPrimaryKeySelective(project);

            // 获取项目快照变更头
            ProjectHistoryHeader historyHeader = projectMilepostFlexibleImportService.getProjectSnapshotHeader(project.getId(), project.getName(), project.getCreateBy());

            // 记录金额汇总（wbs历史基线查询用到）
            ProjectBudgetChangeSummaryHistoryExample summaryHistoryExample = new ProjectBudgetChangeSummaryHistoryExample();
            summaryHistoryExample.createCriteria().andHeaderIdEqualTo(historyHeader.getId());
            List<ProjectBudgetChangeSummaryHistory> summaryHistoryList = projectBudgetChangeSummaryHistoryMapper.selectByExample(summaryHistoryExample);
            ProjectBudgetChangeSummaryHistory summaryHistory = new ProjectBudgetChangeSummaryHistory();
            if (CollectionUtils.isNotEmpty(summaryHistoryList)) {
                summaryHistory = summaryHistoryList.get(0);
            }
            project.setAmount(null);
            summaryHistory.setProjectId(project.getId());
            summaryHistory.setAmount(contractAmount);
            summaryHistory.setExcludingTaxAmount(contractExcludingTaxAmount);
            if(null == summaryHistory.getBudgetCost()){
                summaryHistory.setBudgetCost(BigDecimal.ZERO);
            }
            summaryHistory.setHeaderId(historyHeader.getId());
            if (null == summaryHistory.getId()) {
                projectBudgetChangeSummaryHistoryMapper.insert(summaryHistory);
            } else {
                projectBudgetChangeSummaryHistoryMapper.updateByPrimaryKey(summaryHistory);
            }
        }
    }

    /**
     * 根据项目编码获取项目
     *
     * @param projectCode
     * @return
     */
    private Project getProjectByCode(String projectCode) {
        UnitExample unitExample = new UnitExample();
        unitExample.createCriteria().andDeleteFlagEqualTo(0).andParentIdEqualTo(SystemContext.getUnitId());
        List<Unit> unitList = unitExtMapper.selectByExample(unitExample);

        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria projectCriteria = projectExample.createCriteria();
        projectCriteria.andDeletedFlagEqualTo(false);
        projectCriteria.andCodeEqualTo(projectCode);
        if (CollectionUtils.isNotEmpty(unitList)) {
            List<Long> unitId = unitList.stream().map(a -> a.getId()).collect(Collectors.toList());
            projectCriteria.andUnitIdIn(unitId);
        }
        List<Project> projectList = projectMapper.selectByExample(projectExample);
        if (CollectionUtils.isNotEmpty(projectList)) {
            return projectList.get(0);
        }
        return null;
    }

    /**
     * 根据合同编码获取合同
     *
     * @param contractCode
     * @return
     */
    private Contract getContractByCode(String contractCode) {
        UnitExample unitExample = new UnitExample();
        unitExample.createCriteria().andDeleteFlagEqualTo(0).andParentIdEqualTo(SystemContext.getUnitId());
        List<Unit> unitList = unitExtMapper.selectByExample(unitExample);

        ContractExample contractExample = new ContractExample();
        ContractExample.Criteria contractCriteria = contractExample.createCriteria();
        contractCriteria.andDeletedFlagEqualTo(false);
        contractCriteria.andCodeEqualTo(contractCode);
        if (CollectionUtils.isNotEmpty(unitList)) {
            List<Long> unitId = unitList.stream().map(a -> a.getId()).collect(Collectors.toList());
            contractCriteria.andUnitIdIn(unitId);
        }
        List<Contract> contractList = contractMapper.selectByExample(contractExample);
        if (CollectionUtils.isNotEmpty(contractList)) {
            return contractList.get(0);
        }
        return null;
    }


}
