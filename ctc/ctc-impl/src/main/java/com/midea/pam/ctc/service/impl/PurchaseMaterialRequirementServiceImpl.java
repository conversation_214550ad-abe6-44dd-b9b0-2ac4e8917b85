package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.Storage;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.BatchEditRequirementDeliveryAddressDto;
import com.midea.pam.common.ctc.dto.MaterialRequirementStorageDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialCloseDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialQuantityDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialReleaseDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailChangeHistoryDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.RequirementDeliverAddressRepeatCheckDTO;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecord;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample;
import com.midea.pam.common.ctc.entity.PurchaseBpaPrice;
import com.midea.pam.common.ctc.entity.PurchaseBpaPriceExample;
import com.midea.pam.common.ctc.entity.PurchaseContractBudgetChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseContractBudgetChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseMaterialCloseDetail;
import com.midea.pam.common.ctc.entity.PurchaseMaterialReleaseDetail;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementExample;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementFreezeLog;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementPurchaseTypeChangeLog;
import com.midea.pam.common.ctc.entity.PurchaseOrder;
import com.midea.pam.common.ctc.entity.PurchaseOrderChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseOrderChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetail;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderExample;
import com.midea.pam.common.ctc.entity.VendorAslCtc;
import com.midea.pam.common.ctc.entity.VendorProportionCtc;
import com.midea.pam.common.ctc.entity.VendorProportionCtcExample;
import com.midea.pam.common.ctc.excelVo.ImportPurchaseMaterialRequirementAndCloseDetailExcelVO;
import com.midea.pam.common.ctc.excelVo.ImportPurchaseMaterialRequirementExcelVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.FreezeFlag;
import com.midea.pam.common.enums.FreezeOperationTypeEnum;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.PurchaseMaterialRequirementDeliveryAddressHistoryEditTypeEnum;
import com.midea.pam.common.enums.PurchaseTypeOperationTypeEnum;
import com.midea.pam.common.enums.RequirementDeliveryAddressCheckTypeEnum;
import com.midea.pam.common.enums.StorageInventoryType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DesignPlanDetailGenerateRequire;
import com.midea.pam.ctc.common.enums.ProjectWbsReceiptsBudgetDemandTypeEnums;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderDetailChangeHistoryChangeStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderDetailStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderStatus;
import com.midea.pam.ctc.common.enums.ReleaseDetailStatus;
import com.midea.pam.ctc.common.enums.RequirementCloseTypeEnum;
import com.midea.pam.ctc.common.redis.RedisLuaScriptServer;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanSubmitRecordMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsMapper;
import com.midea.pam.ctc.mapper.PurchaseBpaPriceMapper;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialCloseDetailExtMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialReleaseDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementExtMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementFreezeLogMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementPurchaseTypeChangeLogMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderMapper;
import com.midea.pam.ctc.mapper.VendorProportionCtcMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.PurchaseMaterialCloseDetailService;
import com.midea.pam.ctc.service.PurchaseMaterialReleaseDetailService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementDeliveryAddressHistoryService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.PurchaseOrderDetailService;
import com.midea.pam.ctc.service.StorageExtService;
import com.midea.pam.ctc.service.VendorAslService;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

public class PurchaseMaterialRequirementServiceImpl implements PurchaseMaterialRequirementService {

    private final static Logger logger = LoggerFactory.getLogger(PurchaseMaterialRequirementServiceImpl.class);

    @Resource
    private PurchaseMaterialRequirementMapper purchaseMaterialRequirementMapper;
    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private PurchaseOrderExtMapper purchaseOrderExtMapper;
    @Resource
    private PurchaseContractBudgetChangeHistoryMapper purchaseContractBudgetChangeHistoryMapper;
    @Resource
    private PurchaseMaterialReleaseDetailMapper purchaseMaterialReleaseDetailMapper;
    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;
    @Resource
    private PurchaseOrderDetailExtMapper purchaseOrderDetailExtMapper;
    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;
    @Resource
    private PurchaseMaterialReleaseDetailService purchaseMaterialReleaseDetailService;
    @Resource
    private PurchaseMaterialCloseDetailService purchaseMaterialCloseDetailService;
    @Resource
    private PurchaseMaterialRequirementExtMapper purchaseMaterialRequirementExtMapper;
    @Resource
    private PurchaseOrderDetailService purchaseOrderDetailService;
    @Resource
    private ProjectService projectService;
    @Resource
    private VendorAslService vendorAslService;
    @Resource
    private StorageExtService storageExtService;
    @Resource
    private PurchaseBpaPriceMapper purchaseBpaPriceMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ProjectWbsReceiptsMapper projectWbsReceiptsMapper;
    @Resource
    private ProjectWbsReceiptsBudgetExtMapper projectWbsReceiptsBudgetExtMapper;
    @Resource
    private PurchaseMaterialCloseDetailExtMapper purchaseMaterialCloseDetailExtMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private PurchaseOrderChangeHistoryMapper purchaseOrderChangeHistoryMapper;
    @Resource
    private PurchaseOrderChangeHistoryExtMapper purchaseOrderChangeHistoryExtMapper;
    @Resource
    private PurchaseOrderDetailChangeHistoryExtMapper purchaseOrderDetailChangeHistoryExtMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private VendorProportionCtcMapper vendorProportionCtcMapper;
    @Resource
    private MilepostDesignPlanSubmitRecordMapper milepostDesignPlanSubmitRecordMapper;
    @Resource
    private PurchaseMaterialRequirementDeliveryAddressHistoryService requirementDeliveryAddressHistoryService;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;

    // 需要生成采购需求的物料类型（物料基础表item_type字段，P外购物料，SA-P看板物料）
    private final List<String> itemTypeList = Lists.newArrayList("P");

    private static final Integer EACH_THREAD_DATA_NUM = 5;

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 5;//5分钟
    @Qualifier("getReleasedOrderDetailByRequirementIdExecutor")
    @Resource
    private ThreadPoolTaskExecutor getReleasedOrderDetailByRequirementIdExecutor;
    @Resource
    private PurchaseMaterialRequirementFreezeLogMapper purchaseMaterialRequirementFreezeLogMapper;
    @Resource
    private PurchaseMaterialRequirementPurchaseTypeChangeLogMapper purchaseMaterialRequirementPurchaseTypeChangeLogMapper;
    @Resource
    private RedisLuaScriptServer redisLuaScriptServer;

    @Override
    public PurchaseMaterialRequirementDto add(PurchaseMaterialRequirementDto dto) {
        // 根据项目id查询此项目类型配置：需求传递MRP，如果配置为true，项目需求不需要在‘采购需求列表’ 显示
        Boolean requirementDeliverMrp = projectService.queryRequirementDeliverMrp(dto.getProjectId());
        if (Boolean.TRUE.equals(requirementDeliverMrp)) {
            logger.info("此采购需求对应项目类型设置需要需求传递MRP，则不需要在‘采购需求列表’ 显示" + dto.toString());
            return dto;
        }
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        dto.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
//        dto.setRequirementType(1);
        PurchaseMaterialRequirement entity = BeanConverter.copy(dto, PurchaseMaterialRequirement.class);
        purchaseMaterialRequirementMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public PurchaseMaterialRequirementDto update(PurchaseMaterialRequirementDto dto) {
        // 根据项目id查询此项目类型配置：需求传递MRP，如果配置为true，项目需求不需要在‘采购需求列表’ 显示
        Boolean requirementDeliverMrp = projectService.queryRequirementDeliverMrp(dto.getProjectId());
        if (Boolean.TRUE.equals(requirementDeliverMrp)) {
            logger.info("此采购需求对应项目类型设置需要需求传递MRP，则不需要在‘采购需求列表’ 显示及更新" + dto.toString());
            return dto;
        }
        PurchaseMaterialRequirement entity = BeanConverter.copy(dto, PurchaseMaterialRequirement.class);
        purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public PurchaseMaterialRequirementDto save(PurchaseMaterialRequirementDto dto, Long userBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
            return this.add(dto);
        } else {
            dto.setUpdateBy(userBy);
            return this.update(dto);
        }
    }

    @Override
    public PurchaseMaterialRequirementDto getById(Long id) {
        Asserts.notEmpty(id, ErrorCode.ID_NOT_NULL);
        PurchaseMaterialRequirement entity = purchaseMaterialRequirementMapper.selectByPrimaryKey(id);
        PurchaseMaterialRequirementDto dto = BeanConverter.copy(entity, PurchaseMaterialRequirementDto.class);
        packageDto(dto);
        return dto;
    }

    @Override
    public List<PurchaseMaterialRequirementDto> selectList(PurchaseMaterialRequirementDto query) {
        List<PurchaseMaterialRequirement> list = purchaseMaterialRequirementMapper.selectByExample(buildCondition(query));
        List<PurchaseMaterialRequirementDto> dtos = BeanConverter.copy(list, PurchaseMaterialRequirementDto.class);
        for (PurchaseMaterialRequirementDto dto : dtos) {
            packageDto(dto);
        }
        return dtos;
    }

    @Override
    public PageInfo<PurchaseMaterialRequirementDto> selectPage(PurchaseMaterialRequirementDto query) {
        return null;
    }

    private PurchaseMaterialRequirementExample buildCondition(PurchaseMaterialRequirementDto query) {
        PurchaseMaterialRequirementExample example = new PurchaseMaterialRequirementExample();
        PurchaseMaterialRequirementExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());

        if (query.getProjectId() != null) {
            criteria.andProjectIdEqualTo(query.getProjectId());
        }
        if (query.getDeliveryTime() != null) {
            criteria.andDeliveryTimeEqualTo(query.getDeliveryTime());
        }
        if (query.getPamCode() != null) {
            criteria.andPamCodeEqualTo(query.getPamCode());
        }

        return example;
    }

    private void packageDto(PurchaseMaterialRequirementDto dto) {

        BigDecimal projectHavedQuantity = new BigDecimal(0);
        BigDecimal noProjectHavedQuantity = new BigDecimal(0);
        if (dto.getProjectNum() != null && dto.getProjectOrganizationId() != null && dto.getErpCode() != null) {

            //项目现有量(接收仓)
            Storage receivingStorage = storageExtService.invokeApigetByProCodeAndOrgIdAndMaIdAndType(dto.getProjectNum(),
                    dto.getProjectOrganizationId(), dto.getErpCode(), StorageInventoryType.RECEIVING_WAREHOUSE.code());
            if (receivingStorage != null && receivingStorage.getTransactionQuantity() != null) {
                projectHavedQuantity = projectHavedQuantity.add(receivingStorage.getTransactionQuantity());
            }

            //非项目现有量(公共仓&清理仓&退料仓)
            Storage publicStorage = storageExtService.invokeApigetByProCodeAndOrgIdAndMaIdAndType(dto.getProjectNum(),
                    dto.getProjectOrganizationId(), dto.getErpCode(), StorageInventoryType.PUBLIC_WAREHOUSE.code());
            Storage cleanStorage = storageExtService.invokeApigetByProCodeAndOrgIdAndMaIdAndType(dto.getProjectNum(),
                    dto.getProjectOrganizationId(), dto.getErpCode(), StorageInventoryType.CLEAN_UP_WAREHOUSE.code());
            Storage returnStorage = storageExtService.invokeApigetByProCodeAndOrgIdAndMaIdAndType(dto.getProjectNum(),
                    dto.getProjectOrganizationId(), dto.getErpCode(), StorageInventoryType.RETURN_WAREHOUSE.code());
            if (publicStorage != null && publicStorage.getTransactionQuantity() != null) {
                noProjectHavedQuantity = noProjectHavedQuantity.add(publicStorage.getTransactionQuantity());
            }
            if (cleanStorage != null && cleanStorage.getTransactionQuantity() != null) {
                noProjectHavedQuantity = noProjectHavedQuantity.add(cleanStorage.getTransactionQuantity());
            }
            if (returnStorage != null && returnStorage.getTransactionQuantity() != null) {
                noProjectHavedQuantity = noProjectHavedQuantity.add(returnStorage.getTransactionQuantity());
            }

        }
        dto.setProjectHavedQuantity(projectHavedQuantity);
        dto.setNoProjectHavedQuantity(noProjectHavedQuantity);

        //单位
        if (dto.getUnitCode() != null && dto.getUnit() == null) {
            DictDto unit = CacheDataUtils.findDictByTypeAndCode(DictType.MEASUREMENT_UNIT.code(), dto.getUnitCode());
            if (unit != null && unit.getName() != null) {
                dto.setUnit(unit.getName());
            }
        }

        //业务实体
        if (dto.getOuId() != null) {
            OperatingUnit ou = CacheDataUtils.findOuById(dto.getOuId());
            if (ou != null) {
                dto.setProjectOuName(ou.getOperatingUnitName());
            }
        }
    }

    private void packagePurchaseMaterialRequirementDto(PurchaseMaterialRequirementDto dto) {

        BigDecimal projectHavedQuantity = new BigDecimal(0);
        BigDecimal noProjectHavedQuantity = new BigDecimal(0);
        if (dto.getProjectNum() != null && dto.getProjectOrganizationId() != null && dto.getErpCode() != null) {

            List<String> typeList = new ArrayList<>();
            typeList.add(StorageInventoryType.RECEIVING_WAREHOUSE.code());
            typeList.add(StorageInventoryType.PUBLIC_WAREHOUSE.code());
            typeList.add(StorageInventoryType.CLEAN_UP_WAREHOUSE.code());
            typeList.add(StorageInventoryType.RETURN_WAREHOUSE.code());
            List<MaterialRequirementStorageDto> list = storageExtService.getByProCodeAndOrgIdAndMaIdAndTypeList(dto.getProjectNum(),
                    dto.getProjectOrganizationId(), dto.getErpCode(), typeList);
            logger.info("查询响应的数据为:{}", JSON.toJSONString(list));
            Map<String, BigDecimal> map = new HashMap<>();
            if (CollectionUtils.isNotEmpty(list)) {
                map = list.stream().collect(Collectors.toMap(MaterialRequirementStorageDto::getType,
                        MaterialRequirementStorageDto::getTransactionQuantity));
            }
            if (map.containsKey(StorageInventoryType.RECEIVING_WAREHOUSE.code())) {
                projectHavedQuantity = projectHavedQuantity.add(map.get(StorageInventoryType.RECEIVING_WAREHOUSE.code()));
            }
            if (map.containsKey(StorageInventoryType.PUBLIC_WAREHOUSE.code())) {
                noProjectHavedQuantity = noProjectHavedQuantity.add(map.get(StorageInventoryType.PUBLIC_WAREHOUSE.code()));
            }
            if (map.containsKey(StorageInventoryType.CLEAN_UP_WAREHOUSE.code())) {
                noProjectHavedQuantity = noProjectHavedQuantity.add(map.get(StorageInventoryType.CLEAN_UP_WAREHOUSE.code()));
            }
            if (map.containsKey(StorageInventoryType.RETURN_WAREHOUSE.code())) {
                noProjectHavedQuantity = noProjectHavedQuantity.add(map.get(StorageInventoryType.RETURN_WAREHOUSE.code()));
            }

        }
        dto.setProjectHavedQuantity(projectHavedQuantity);
        dto.setNoProjectHavedQuantity(noProjectHavedQuantity);

        //单位
        if (dto.getUnitCode() != null && dto.getUnit() == null) {
            DictDto unit = CacheDataUtils.findDictByTypeAndCode(DictType.MEASUREMENT_UNIT.code(), dto.getUnitCode());
            if (unit != null && unit.getName() != null) {
                dto.setUnit(unit.getName());
            }
        }

        //业务实体
        if (dto.getOuId() != null) {
            OperatingUnit ou = CacheDataUtils.findOuById(dto.getOuId());
            if (ou != null) {
                dto.setProjectOuName(ou.getOperatingUnitName());
            }
        }
    }


    @Override
    public List<PurchaseMaterialRequirementDto> selectListWithDetail(PurchaseMaterialRequirementDto query) {
        logger.info("请求参数为:{}", JSON.toJSONString(query));
        List<PurchaseMaterialRequirementDto> dtos = this.selectPurchaseMaterialRequirementList(query);
        // purchaseMaterialRequirementExtMapper.selectListWithDetail(query);
        logger.info("响应集合数为:{}", dtos.size());
//        for (PurchaseMaterialRequirementDto dto : dtos) {
//            packagePurchaseMaterialRequirementDto(dto);
//        }
        return dtos;
    }

    @Override
    public PageInfo<PurchaseMaterialRequirementDto> selectPageWithDetail(PurchaseMaterialRequirementDto query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<PurchaseMaterialRequirementDto> dtos = this.selectPurchaseMaterialRequirementList(query);
        PageInfo<PurchaseMaterialRequirementDto> page = BeanConverter.convertPage(dtos, PurchaseMaterialRequirementDto.class);
//        for (PurchaseMaterialRequirementDto dto : page.getList()) {
//            packageDto(dto);
//        }
        return page;
    }

    /**
     * 批量统计采购需求 未采购量/总需求量/已采购量/关闭数量
     *
     * @param ids
     * @return
     */
    @Override
    public List<PurchaseMaterialRequirementDto> getDetailByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        PurchaseMaterialRequirementDto requirementQuery = new PurchaseMaterialRequirementDto();
        requirementQuery.setRequirementIdList(ids);
        List<PurchaseMaterialRequirementDto> dtos = purchaseMaterialRequirementExtMapper.selectPurchaseMaterialRequirementList(requirementQuery);
        if (CollectionUtils.isNotEmpty(dtos)) {
            List<Long> requirementIds = dtos.stream().map(x -> x.getId()).collect(Collectors.toList());
            List<PurchaseMaterialQuantityDto> releasedQuantities =
                    purchaseMaterialRequirementExtMapper.selectPurchaseMaterialReleasedQuantityList(requirementIds);
            Map<Long, BigDecimal> releasedQuantitiesMap =
                    releasedQuantities.stream().collect(Collectors.toMap(PurchaseMaterialQuantityDto::getRequirementId,
                            PurchaseMaterialQuantityDto::getQuantity));
            List<PurchaseMaterialQuantityDto> orderQuantities =
                    purchaseMaterialRequirementExtMapper.selectPurchaseMaterialOrderQuantityList(requirementIds);
            Map<Long, BigDecimal> orderQuantitiesMap =
                    orderQuantities.stream().collect(Collectors.toMap(PurchaseMaterialQuantityDto::getRequirementId,
                            PurchaseMaterialQuantityDto::getQuantity));
            for (PurchaseMaterialRequirementDto dto : dtos) {
                Long id = dto.getId();
                BigDecimal releasedQuantity = releasedQuantitiesMap.keySet().contains(id) ? releasedQuantitiesMap.get(id) : new BigDecimal(0);
                BigDecimal orderQuantity = orderQuantitiesMap.keySet().contains(id) ? releasedQuantitiesMap.get(id) : new BigDecimal(0);
                dto.setReleasedQuantity(releasedQuantity);
                dto.setOrderQuantity(orderQuantity);
                //未下达量 = 总需求量 - 已下达量 - 关闭数量
                dto.setUnreleasedAmount(dto.getNeedTotal().subtract(releasedQuantity).subtract(dto.getClosedQuantity()));
                //单位
                if (dto.getUnitCode() != null && dto.getUnit() == null) {
                    DictDto unit = CacheDataUtils.findDictByTypeAndCode(DictType.MEASUREMENT_UNIT.code(), dto.getUnitCode());
                    if (unit != null && unit.getName() != null) {
                        dto.setUnit(unit.getName());
                    }
                }
                //业务实体
                if (dto.getProjectOuId() != null) {
                    OperatingUnit ou = CacheDataUtils.findOuById(dto.getOuId());
                    if (ou != null) {
                        dto.setProjectOuName(ou.getOperatingUnitName());
                    }
                }
            }
        }
        return dtos;
    }

    /**
     * 统计采购需求 未采购量/总需求量/已采购量/关闭数量
     *
     * @param id
     * @return
     */
    @Override
    public PurchaseMaterialRequirementDto getDetailById(Long id) {
        if (id == null) {
            return null;
        }
        PurchaseMaterialRequirementDto requirementQuery = new PurchaseMaterialRequirementDto();
        requirementQuery.setId(id);
        List<PurchaseMaterialRequirementDto> dtos = this.selectPurchaseMaterialRequirementList(requirementQuery);
        Asserts.notEmpty(dtos, ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_NOT_FIND);

        PurchaseMaterialRequirementDto requirementDto = dtos.get(0);
        return requirementDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recursiveGenerateRequirementByDetailPlanId(Long detailPlanParentId, Long submitRecordId, List<Long> requirementIdList) {
        logger.info("begin-确认发布(采购) 详设父节点id：{}，提交记录id：{}", detailPlanParentId, submitRecordId);
        MilepostDesignPlanDetailDto parentDto = milepostDesignPlanDetailService.getById(detailPlanParentId);
        Guard.notNull(parentDto, String.format("详设id：%s对应的详设信息不存在", detailPlanParentId));
        if (parentDto.getExt() != null && parentDto.getExt().equals(true)) {
            //集成外包不产生物料需求
            return;
        }
        //查询该设计信息下所有未产生物料采购需求的子设计信息
        MilepostDesignPlanDetailDto planDetailQuery = new MilepostDesignPlanDetailDto();
        planDetailQuery.setParentId(detailPlanParentId);
        planDetailQuery.setSubmitRecordId(submitRecordId);
        List<MilepostDesignPlanDetailDto> designPlanDetailSons = milepostDesignPlanDetailService.selectList(planDetailQuery);
        for (MilepostDesignPlanDetailDto designPlanDetailSon : designPlanDetailSons) {
            //递归
            recursiveGenerateRequirementByDetailPlanId(designPlanDetailSon.getId(), submitRecordId, requirementIdList);

            // 旧的生成采购需求的逻辑
            //未产生物料需求的设计信息(物料小类非外协半成品)
            // 详细设计status为空的，不做处理
            boolean canRequirement = designPlanDetailSon.getGenerateRequirement() != null
                    && designPlanDetailSon.getGenerateRequirement().equals(DesignPlanDetailGenerateRequire.NOT_PRODUCED.code())
                    // && !"外协半成品".equals(designPlanDetailSon.getMaterielType())
                    && !designPlanDetailSon.getDeletedFlag()
                    && designPlanDetailSon.getStatus() != null;
            if (!canRequirement) {
                continue;
            }

            Guard.notNull(designPlanDetailSon.getProjectId(), String.format("子级详细设计id：%s的项目id不能为空", designPlanDetailSon.getId()));
            Guard.notNullOrEmpty(designPlanDetailSon.getPamCode(), String.format("子级详细设计id：%s的pam编码不能为空", designPlanDetailSon.getId()));
            Guard.notNull(designPlanDetailSon.getDeliveryTime(), String.format("子级详细设计id：%s的交货时间不能为空", designPlanDetailSon.getId()));
            Long organizationId = projectService.getOrganizationIdByProjectId(designPlanDetailSon.getProjectId());
            MaterialDto materialDto = materialExtService.invokeMaterialApiGetByPamCode(designPlanDetailSon.getPamCode(), organizationId);//TODO
            if (materialDto == null || !itemTypeList.contains(materialDto.getItemType())) {
                continue;
            }

            // :3082,代表机器人的物料,将来可考虑获取上下文的ouId
            // 新的生成采购需求的逻辑，item_type为P（外购物料）的物料才生成采购需求
            logger.info("recursiveGenerateRequirementByDetailPlanId准备产生采购需求，库存组织：{}，designPlanDetailSon：{}",
                    organizationId, JsonUtils.toString(designPlanDetailSon));

            //查询该项目某个交货时间对应的物料采购需求
            PurchaseMaterialRequirementDto exitDto = getByProjectIdAndPamCodeAndDeliveryTime(
                    designPlanDetailSon.getProjectId(),
                    designPlanDetailSon.getDeliveryTime(),
                    designPlanDetailSon.getPamCode());
            PurchaseMaterialRequirementDto dto = new PurchaseMaterialRequirementDto();
            PurchaseMaterialRequirementDto resultDto = null;
            BigDecimal purchaseNum = Optional.ofNullable(designPlanDetailSon.getNumber()).orElse(BigDecimal.ZERO)
                    .multiply(milepostDesignPlanDetailService.getParentSigmaById(designPlanDetailSon.getParentId(), null));
            if (exitDto == null) {
                logger.info("recursiveGenerateRequirementByDetailPlanId生成物料采购需求");
                //生成物料采购需求
                dto.setProjectId(designPlanDetailSon.getProjectId());
                dto.setErpCode(designPlanDetailSon.getErpCode());
                dto.setPamCode(designPlanDetailSon.getPamCode());
                dto.setDeliveryTime(designPlanDetailSon.getDeliveryTime());
                dto.setMaterielId(materialDto.getId());
                dto.setMaterielDescr(materialDto.getItemInfo());

                String requirementRedisNumber = getRequirementRedisNumber(designPlanDetailSon.getProjectId(),
                        designPlanDetailSon.getDeliveryTime(), designPlanDetailSon.getPamCode(), null, purchaseNum);

                dto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                dto.setUnitCode(designPlanDetailSon.getUnitCode());
                dto.setUnit(designPlanDetailSon.getUnit());
                resultDto = this.add(dto);
            } else {
                dto.setId(exitDto.getId());
                //更新物料采购需求总需求量
                logger.info("recursiveGenerateRequirementByDetailPlanId更新物料采购需求总需求量");

                if (BigDecimal.ZERO.compareTo(purchaseNum) != 0) {
                    String requirementRedisNumber = getRequirementRedisNumber(designPlanDetailSon.getProjectId(),
                            designPlanDetailSon.getDeliveryTime(), designPlanDetailSon.getPamCode(), exitDto.getNeedTotal(), purchaseNum);

                    dto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                    resultDto = this.update(dto);
                    //updateStatus(dto.getId());//根据总需求量更新采购需求的状态
                    requirementIdList.add(dto.getId());
                }
            }
            if (BigDecimal.ZERO.compareTo(purchaseNum) != 0) {
                //生成物料采购需求发布明细
                addPurchaseMaterialReleaseDetail(materialDto.getId(),
                        resultDto == null ? null : resultDto.getId(),
                        designPlanDetailSon,
                        ReleaseDetailStatus.SUBMIT.code(),
                        designPlanDetailSon.getCreateBy(),
                        purchaseNum);
            }

            //更新设计信息的是否产生物料需求字段
            designPlanDetailSon.setGenerateRequirement(DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code());
            milepostDesignPlanDetailService.save(designPlanDetailSon, null);
            logger.info("生成或更新采购需求结束");
        }
        logger.info("end-确认发布(采购)");
    }

    @Override
    public PurchaseMaterialRequirementDto getByProjectIdAndPamCodeAndDeliveryTime(Long projectId, Date deliveryTime, String pamCode) {
        PurchaseMaterialRequirementDto query = new PurchaseMaterialRequirementDto();
        query.setProjectId(projectId);
        query.setDeliveryTime(deliveryTime);
        query.setPamCode(pamCode);
        List<PurchaseMaterialRequirementDto> dtos = this.selectList(query);
        if (ListUtil.isPresent(dtos)) {
            return dtos.get(0);
        }
        return null;
    }

    @Override
    public List<PurchaseMaterialRequirementDto> getReleasedOrderDetailByRequirementId(String requirementIdStr, Long buyerBy) {
        Asserts.notEmpty(requirementIdStr, ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_ID_NOT_NULL);
        Asserts.notEmpty(buyerBy, ErrorCode.CTC_BUYER_INFO_NOT_NULL);

        //查找组织参数，是否查询控制一揽子协议
        final Map<String, Object> query = new HashMap<>();
        query.put("orgId", SystemContext.getUnitId());
        query.put("orgFrom", "company");
        query.put("name", "无定价是否允许下采购订单");
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationCustomDict>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
        });

        //查找组织参数，采购订单下达按供货比例默认下达数量
        final Map<String, Object> query1 = new HashMap<>();
        query1.put("orgId", SystemContext.getUnitId());
        query1.put("orgFrom", "company");
        query1.put("name", "采购订单下达按供货比例默认下达数量");
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query1);
        String res1 = restTemplate.getForEntity(url1, String.class).getBody();
        DataResponse<List<OrganizationCustomDict>> response1 = JSON.parseObject(res1,
                new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
                });

        List<Long> requirementIdList = Arrays.stream(requirementIdStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(requirementIdList)) return new ArrayList<>();

        List<PurchaseMaterialRequirementDto> requirementDtoList = this.getDetailByIds(requirementIdList);
        packagePurchaseMaterialRequirement(requirementDtoList);
        Map<Long, PurchaseMaterialRequirementDto> requirementMap =
                requirementDtoList.stream().collect(Collectors.toMap(PurchaseMaterialRequirementDto::getId, Function.identity(), (a, b) -> a));
        List<PurchaseMaterialRequirementDto> resultList = new ArrayList<>();
        Date now = new Date();

        /** 优化达到瓶颈，因循环次数过多压测还是达不到3秒内，采用多线程异步执行的方式 **/
        int threadNum = requirementIdList.size() / EACH_THREAD_DATA_NUM + (requirementIdList.size() % EACH_THREAD_DATA_NUM == 0 ? 0 : 1);
        final CountDownLatch countDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * EACH_THREAD_DATA_NUM;
            int endIndex = i == threadNum ? requirementIdList.size() : i * EACH_THREAD_DATA_NUM;
            List<Long> eachDetailDtos = requirementIdList.subList(startIndex, endIndex);
            getReleasedOrderDetailByRequirementIdExecutor.execute(() -> {
                try {
                    // 多线程异步执行
                    executeEachDetailDtos(eachDetailDtos, requirementMap, buyerBy, response, response1, resultList, now);
                } catch (Exception e) {
                    logger.error("getReleasedOrderDetailByRequirementId操作记录失败", e);
                } finally {
                    countDownLatch.countDown();
                    if (Objects.equals(countDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after getReleasedOrderDetailByRequirementId", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("getReleasedOrderDetailByRequirementId的await失败", ex);
            Thread.currentThread().interrupt();
        }
        return requirementDtoList;
    }

    /**
     * @param eachDetailDtos
     * @param requirementMap
     * @param buyerBy
     * @param response       是否查询控制一揽子协议
     * @param response1      采购订单下达按供货比例默认下达数量
     * @param resultList
     */
    public void executeEachDetailDtos(List<Long> eachDetailDtos,
                                      Map<Long, PurchaseMaterialRequirementDto> requirementMap,
                                      Long buyerBy,
                                      DataResponse<List<OrganizationCustomDict>> response,
                                      DataResponse<List<OrganizationCustomDict>> response1,
                                      List<PurchaseMaterialRequirementDto> resultList,
                                      Date now) {

        for (Long requirementId : eachDetailDtos) {
            PurchaseMaterialRequirementDto dto = requirementMap.get(requirementId);
            Asserts.notEmpty(dto, ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_NOT_FIND);
            Asserts.notEmpty(dto.getProjectId(), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_PROJECT_ID_NULL);
            Asserts.notEmpty(dto.getPamCode(), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_PAM_CODE_NULL);

            // 按照供应商分组构建订单行信息
            List<PurchaseOrderDetailDto> purchaseOrderDetailDtos = new ArrayList<>();
            List<VendorAslCtc> vendorAslCtcs = vendorAslService.selectListByMaterialCode(dto.getErpCode(), dto.getProjectOrganizationCode());
            for (VendorAslCtc vendorAslCtc : vendorAslCtcs) {
                PurchaseOrderDetailDto purchaseOrderDetailDto = new PurchaseOrderDetailDto();
                purchaseOrderDetailDto.setVendorAslId(vendorAslCtc.getId());
                purchaseOrderDetailDto.setVendorName(vendorAslCtc.getVendorName());
                purchaseOrderDetailDto.setVendorNum(vendorAslCtc.getVendorCode());
                purchaseOrderDetailDto.setVendorCode(vendorAslCtc.getVendorCode());
                purchaseOrderDetailDto.setOrderNum(new BigDecimal(0));
                purchaseOrderDetailDto.setVendorSiteCode(vendorAslCtc.getVendorSiteCode());
                purchaseOrderDetailDto.setUnitCode(dto.getUnitCode());
                purchaseOrderDetailDto.setUnit(dto.getUnit());
                purchaseOrderDetailDto.setDeliveryTime(dto.getDeliveryTime());

                //检查是否已有对应的已下达的下达明细
                PurchaseOrderDetailDto existDto = purchaseOrderDetailService.getReleasedDataByRequirementIdAndVendorAslIdAndBuyerBy(dto.getId(),
                        vendorAslCtc.getId(), buyerBy);
                if (existDto != null) {
                    purchaseOrderDetailDto.setId(existDto.getId());
                    purchaseOrderDetailDto.setOrderNum(existDto.getOrderNum());
                    purchaseOrderDetailDto.setDeliveryTime(existDto.getDeliveryTime());
                    dto.setExistNum(BigDecimalUtils.add(dto.getExistNum(), existDto.getOrderNum()));
                    dto.setUnreleasedAmount(BigDecimalUtils.add(dto.getUnreleasedAmount(), existDto.getOrderNum()));      //未下达量要加上本人待下达订单里的数量
                    dto.setReleasedQuantity(BigDecimalUtils.subtract(dto.getReleasedQuantity(), existDto.getOrderNum())); //已下达量要减去本人待下达订单里的数量
                }

                //设置供货比例
                VendorProportionCtcExample proportionExample = new VendorProportionCtcExample();
                proportionExample.createCriteria().andOrganizationCodeEqualTo(vendorAslCtc.getOrganizationCode())
                        .andMaterialCodeEqualTo(vendorAslCtc.getMaterialCode())
                        .andVendorCodeEqualTo(vendorAslCtc.getVendorCode())
                        .andVendorSiteCodeEqualTo(vendorAslCtc.getVendorSiteCode())
                        .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<VendorProportionCtc> vendorProportionList = vendorProportionCtcMapper.selectByExample(proportionExample);
                //logger.info("获取的供货比例数据为:{}",JSON.toJSONString(vendorProportionList));
                if (CollectionUtils.isNotEmpty(vendorProportionList)) {
                    for (VendorProportionCtc vendorProportion : vendorProportionList) {
                        //生效日期或失效日期要比较到当前天,比如数据库存储的失效时间 2024-07-16 00:00:00.0
                        //下一条数据的有效开始时间为: 2024-07-17 00:00:00.0 那么此时是取不到符合要求的供货比例数据的
                        //因此在比较时间时,只能年月日参与比较
                        Date startDate = vendorProportion.getStartDate();//开始时间
                        Date endDate = vendorProportion.getEndDate(); //失效时间
                        if (startDate != null) {
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATE_PATTERN);
                            String startDateStr = DateUtil.format(startDate, DateUtil.DATE_PATTERN);
                            LocalDate startDateLocal = LocalDate.parse(startDateStr, formatter);
                            String nowStr = DateUtil.format(now, DateUtil.DATE_PATTERN);
                            LocalDate nowLocal = LocalDate.parse(nowStr, formatter);
                            if (startDateLocal.isBefore(nowLocal) || startDateLocal.isEqual(nowLocal)) { //生效日期在当前日期之前,失效日期为空,数据有效
                                if (endDate == null) {//没有失效日期
                                    purchaseOrderDetailDto.setProportion(vendorProportion.getProportion());
                                } else {
                                    String endDateStr = DateUtil.format(endDate, DateUtil.DATE_PATTERN);
                                    logger.info("获取的生效时间:{},失效时间:{},当前时间:{}", startDateStr, endDateStr, nowStr);
                                    LocalDate endDateLocal = LocalDate.parse(endDateStr, formatter);
                                    if (endDateLocal.isAfter(nowLocal) || endDateLocal.isEqual(nowLocal)) { //失效日期不为空,且在当前日期之后
                                        purchaseOrderDetailDto.setProportion(vendorProportion.getProportion());
                                    }

                                }

                            }
                        }
                    }
                }

                if (ListUtils.isNotEmpty(response.getData()) && "1".equals(response.getData().get(0).getValue())) {
                    //根据erp物料编码+供应商编码+供应商地点+ou，查询是否有满足有效期内
                    String erpCode = dto.getErpCode();
                    String vendorCode = purchaseOrderDetailDto.getVendorCode();
                    String vendorSiteCode = purchaseOrderDetailDto.getVendorSiteCode();
                    Long ouId = Long.valueOf(dto.getProjectOuId());
                    PurchaseBpaPriceExample purchaseBpaPriceExample = new PurchaseBpaPriceExample();
                    purchaseBpaPriceExample.createCriteria().andMaterialCodeEqualTo(erpCode)
                            .andVendorCodeEqualTo(vendorCode)
                            .andVendorSiteCodeEqualTo(vendorSiteCode)
                            .andOuIdEqualTo(ouId)
                            .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                    List<PurchaseBpaPrice> purchaseBpaPrices = purchaseBpaPriceMapper.selectByExample(purchaseBpaPriceExample);
                    logger.info("获取有效价为:{}", JSON.toJSONString(purchaseBpaPrices));

                    if (ListUtils.isNotEmpty(purchaseBpaPrices)) {
                        int size = purchaseBpaPrices.size();
                        AtomicInteger resultNum = new AtomicInteger();
                        purchaseBpaPrices.forEach(p -> {
                            boolean result;
                            //当失效日期为空则无限大，并且分段价格不能为空或者小于等于0
                            if (null == p.getEndDate() && null != p.getPriceOverride() && p.getPriceOverride().compareTo(BigDecimal.ZERO) > 0) {
                                result = true;
                            } else {
                                //分段价格不能为空或者小于等于0
                                if (null == p.getPriceOverride() || p.getPriceOverride().compareTo(BigDecimal.ZERO) <= 0) {
                                    result = false;
                                } else {
                                    //符合开始时间+失效时间区间内
                                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATE_PATTERN);
                                    String startDateStr = DateUtil.format(p.getStartDate(), DateUtil.DATE_PATTERN);
                                    LocalDate startDateLocal = LocalDate.parse(startDateStr, formatter);
                                    String nowStr = DateUtil.format(now, DateUtil.DATE_PATTERN);
                                    LocalDate nowLocal = LocalDate.parse(nowStr, formatter);
                                    String endDateStr = DateUtil.format(p.getEndDate(), DateUtil.DATE_PATTERN);
                                    logger.info("获取的生效时间:{},失效时间:{},当前时间:{}", startDateStr, endDateStr, nowStr);
                                    LocalDate endDateLocal = LocalDate.parse(endDateStr, formatter);
                                    if ((startDateLocal.isBefore(nowLocal) || startDateLocal.isEqual(nowLocal))
                                            && (endDateLocal.isAfter(nowLocal) || endDateLocal.isEqual(nowLocal))) {
                                        result = true;
                                    } else {
                                        result = false;
                                    }
                                }
                            }
                            if (!result) {
                                resultNum.getAndIncrement();
                            }
                        });
                        if (size == resultNum.intValue()) {
                            purchaseOrderDetailDto.setPurchaseBpaPrice(false);
                        }
                    } else {
                        purchaseOrderDetailDto.setPurchaseBpaPrice(false);
                    }
                }
                purchaseOrderDetailDtos.add(purchaseOrderDetailDto);
            }

            //计算建议下单量
            BigDecimal systemQuantity = BigDecimalUtils.subtract(BigDecimalUtils.subtract(BigDecimalUtils.subtract(dto.getNeedTotal(),
                    dto.getReleasedQuantity()), dto.getProjectHavedQuantity()), dto.getClosedQuantity());
            dto.setSystemQuantity(systemQuantity.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : systemQuantity);  //建议下单量
            //剩余分配数量
            BigDecimal surplus = dto.getSystemQuantity();
            if (CollectionUtils.isNotEmpty(purchaseOrderDetailDtos)) {
                //批准供应商的排序要按以下规则：先排null，再按分配比例从小到大排序
                purchaseOrderDetailDtos.sort(Comparator.comparing(PurchaseOrderDetailDto::getProportion,
                        Comparator.nullsFirst(BigDecimal::compareTo)));
                //维护的百分比汇总
                BigDecimal proportionTotal = purchaseOrderDetailDtos.stream().map(PurchaseOrderDetailDto::getProportion).reduce(BigDecimal.ZERO,
                        BigDecimalUtils::add);
                for (int i = 0; i < purchaseOrderDetailDtos.size(); i++) {
                    PurchaseOrderDetailDto detailDto = purchaseOrderDetailDtos.get(i);
                    if (purchaseOrderDetailDtos.size() == 1) { //批准供应商数量为1
                        detailDto.setOrderNum(dto.getSystemQuantity());
                    } else {
                        //组织参数：采购订单下达按供货比例默认下达数量 启用
                        if (ListUtils.isNotEmpty(response1.getData()) && "1".equals(response1.getData().get(0).getValue())) {
                            if (proportionTotal.compareTo(new BigDecimal(100)) != 0) { //维护的百分比汇总不为100%
                                detailDto.setOrderNum(BigDecimal.ZERO);
                            } else {
                                if (detailDto.getProportion() == null) { //未维护供货比例默认数量设为0
                                    detailDto.setOrderNum(BigDecimal.ZERO);
                                } else {
                                    if (i == purchaseOrderDetailDtos.size() - 1) { //最后一行倒减
                                        detailDto.setOrderNum(surplus);
                                    } else {
                                        detailDto.setOrderNum(BigDecimalUtils.multiply(dto.getSystemQuantity(),
                                                BigDecimalUtils.divide(detailDto.getProportion(), new BigDecimal(100))).setScale(0,
                                                RoundingMode.FLOOR));
                                        surplus = BigDecimalUtils.subtract(surplus, detailDto.getOrderNum());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            dto.setPurchaseOrderDetailDtos(purchaseOrderDetailDtos);

            resultList.add(dto);
        }
    }

    @Override
    public PurchaseMaterialRequirementDto updateStatus(Long requirementId) {
        if (requirementId == null) {
            return null;
        }
        PurchaseMaterialRequirementDto dto = this.getDetailById(requirementId);
        logger.info("updateStatus的requirementId：{}，dto：{}", requirementId, JSON.toJSONString(dto));
        Asserts.notEmpty(dto, ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_NOT_FIND);
        //如果项目已结项/终止，不再更新采购需求状态
        if (Objects.equals(String.valueOf(ProjectStatus.CLOSE.getCode()), dto.getProjectStatus())
                || Objects.equals(String.valueOf(ProjectStatus.TERMINATION.getCode()), dto.getProjectStatus())) {
            return dto;
        }
        PurchaseMaterialRequirementDto updateItem = new PurchaseMaterialRequirementDto();
        updateItem.setId(dto.getId());

        // 待下达:未下达量!=0
        if (dto.getUnreleasedAmount().compareTo(BigDecimal.ZERO) != 0) {
            //待下达--已下达量小于总需求量
            updateItem.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
        } else {
            //已下达--已下达量等于总需求量
            updateItem.setStatus(PurchaseMaterialRequirementStatus.ORDERED.code());
            if (dto.getOrderQuantity().add(dto.getClosedQuantity()).compareTo(dto.getNeedTotal()) >= 0) {
                //已关闭--订单量等于总需求量
                updateItem.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
            }
        }
        purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(updateItem);
        return dto;
    }

    @Override
    public void batchUpdateRequirementStatus(List<Long> requirementIdList) {
        if (ListUtils.isEmpty(requirementIdList)) {
            return;
        }
        PurchaseMaterialRequirementDto query = new PurchaseMaterialRequirementDto();
        query.setRequirementIdList(requirementIdList);
        List<PurchaseMaterialRequirementDto> items = this.selectListWithDetail(query);
        if (ListUtils.isEmpty(items)) {
            return;
        }
        logger.info("batchUpdateRequirementStatus的list：{}", JSON.toJSONString(items));
        Asserts.notEmpty(items, ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_NOT_FIND);
        List<PurchaseMaterialRequirement> requirementList = new ArrayList<>();
        for (PurchaseMaterialRequirementDto item : items) {
            PurchaseMaterialRequirement requirement = new PurchaseMaterialRequirement();
            requirement.setId(item.getId());
            //如果项目已结项/终止，不再更新采购需求状态
            if (Objects.equals(String.valueOf(ProjectStatus.CLOSE.getCode()), item.getProjectStatus())
                    || Objects.equals(String.valueOf(ProjectStatus.TERMINATION.getCode()), item.getProjectStatus())) {
                continue;
            }
            // 待下达:未下达量!=0
            if (item.getUnreleasedAmount().compareTo(BigDecimal.ZERO) != 0) {
                //待下达--已下达量小于总需求量
                requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
            } else {
                //已下达--已下达量等于总需求量
                requirement.setStatus(PurchaseMaterialRequirementStatus.ORDERED.code());
                if (item.getOrderQuantity().add(item.getClosedQuantity()).compareTo(item.getNeedTotal()) >= 0) {
                    //已关闭--订单量+关闭数量等于总需求量
                    requirement.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
                }
            }
            //purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(requirement);
            requirementList.add(requirement);
        }
        logger.info("获取待更新的数据为:{}", JSON.toJSONString(requirementList));
        if (ListUtils.isNotEmpty(requirementList)) {
            purchaseMaterialRequirementExtMapper.batchUpdateByPrimaryKey(requirementList);
        }

    }

    @Override
    public PurchaseMaterialRequirementDto updateStatus(PurchaseMaterialRequirementDto dto) {
        logger.info("updateStatus的dto：{}", JSON.toJSONString(dto));
        Asserts.notEmpty(dto, ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_NOT_FIND);
        //如果项目已结项/终止，不再更新采购需求状态
        if (Objects.equals(String.valueOf(ProjectStatus.CLOSE.getCode()), dto.getProjectStatus())
                || Objects.equals(String.valueOf(ProjectStatus.TERMINATION.getCode()), dto.getProjectStatus())) {
            return dto;
        }
        PurchaseMaterialRequirementDto updateItem = new PurchaseMaterialRequirementDto();
        updateItem.setId(dto.getId());

        // 待下达:未下达量!=0
        if (dto.getUnreleasedAmount().compareTo(BigDecimal.ZERO) != 0) {
            //待下达--已下达量小于总需求量
            updateItem.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
        } else {
            //已下达--已下达量等于总需求量
            updateItem.setStatus(PurchaseMaterialRequirementStatus.ORDERED.code());
            if (dto.getOrderQuantity().add(dto.getClosedQuantity()).compareTo(dto.getNeedTotal()) >= 0) {
                //已关闭--订单量等于总需求量
                updateItem.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
            }
        }
        purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(updateItem);
        return dto;
    }

    @Override
    public void batchUpdateStatus(List<Long> requirementIdList) {
        /**这个方法在很多方法中被调用,容易出现锁等待超时的问题,而且涉及循环修改,因而改造方法为:
         * 1.加一个固定名称的锁,只要调用这个方法就会加锁
         * 2.for循环里的修改,改为批量修改
         * */
        if (ListUtils.isEmpty(requirementIdList)) {
            return;
        }
        PurchaseMaterialRequirementDto query = new PurchaseMaterialRequirementDto();
        query.setRequirementIdList(requirementIdList);
        List<PurchaseMaterialRequirementDto> items = this.selectPurchaseMaterialRequirementList(query);
        if (ListUtils.isEmpty(items)) {
            return;
        }
        logger.info("batchUpdateStatus的list：{}", JSON.toJSONString(items));
        String lockName = String.format("batchUpdateStatus_requirementIdList");
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                logger.info("加锁成功,批量更新物料需求状态,锁名称为:{},时间为:{}", lockName, currentDateStr);
                List<PurchaseMaterialRequirement> requirements = new ArrayList<>();
                for (PurchaseMaterialRequirementDto item : items) {
                    PurchaseMaterialRequirement requirement = new PurchaseMaterialRequirement();
                    requirement.setId(item.getId());
                    //如果项目已结项/终止，不再更新采购需求状态
                    if (Objects.equals(String.valueOf(ProjectStatus.CLOSE.getCode()), item.getProjectStatus())
                            || Objects.equals(String.valueOf(ProjectStatus.TERMINATION.getCode()), item.getProjectStatus())) {
                        continue;
                    }
                    // 待下达:未下达量!=0
                    if (item.getUnreleasedAmount().compareTo(BigDecimal.ZERO) != 0) {
                        //待下达--已下达量小于总需求量
                        requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
                    } else {
                        //已下达--已下达量等于总需求量
                        requirement.setStatus(PurchaseMaterialRequirementStatus.ORDERED.code());
                        if (item.getOrderQuantity().add(item.getClosedQuantity()).compareTo(item.getNeedTotal()) >= 0) {
                            //已关闭--订单量+关闭数量等于总需求量
                            requirement.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
                        }
                    }
                    requirements.add(requirement);
                }
                logger.info("获取待更新的数据为:{}", JSON.toJSONString(requirements));
                if (CollectionUtils.isNotEmpty(requirements)) {
                    purchaseMaterialRequirementExtMapper.batchUpdateByPrimaryKey(requirements);
                }
            }
        } catch (Exception e) {
            logger.error("批量更新物料需求状态出现异常", e);
            throw e;
        } finally {
            logger.info("释放锁成功,批量更新物料需求状态,锁名称为:{},时间为:{}", lockName, currentDateStr);
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Override
    @Transactional
    public Boolean addPendingOrder(PurchaseMaterialRequirementDto dto, Long userBy) {
        Asserts.notEmpty(userBy, ErrorCode.CTC_BUYER_INFO_NOT_NULL);
        List<PurchaseMaterialRequirementDto> requirementDtos = dto.getRequirementDtos();
        Asserts.isTrue(requirementDtos.stream().allMatch(s -> s.getId() != null), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_ID_NOT_NULL);

        //校验供应商是否已失效
        String url1 = String.format("%svendorSiteBank/checkVendorSiteBankValid", ModelsEnum.BASEDATA.getBaseUrl());
        String res1 = restTemplate.postForEntity(url1, dto, String.class).getBody();
        DataResponse<String> checkResponse = JSON.parseObject(res1, new TypeReference<DataResponse<String>>() {
        });
        if (checkResponse != null && Objects.equals(checkResponse.getCode(), 0) && StringUtils.isNotEmpty(checkResponse.getData())) {
            throw new BizException(Code.ERROR, String.format("供应商：%s已退出，请重新引入供应商或调整批准供应商", checkResponse.getData()));
        }

        //查找组织参数：采购订单分组规则
        Set<String> purchaseOrderGroupRuleSet = organizationCustomDictService.queryByName(Constants.PURCHASE_ORDER_GROUP_RULE,
                SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
        if (CollectionUtils.isEmpty(purchaseOrderGroupRuleSet) || !(purchaseOrderGroupRuleSet.contains("1") || purchaseOrderGroupRuleSet.contains(
                "2"))) {
            throw new BizException(Code.ERROR, "未找到正确的采购订单分组规则，请检查使用单位层级的组织参数：采购订单分组规则的配置");
        }

        List<Long> requirementIdList = requirementDtos.stream().map(PurchaseMaterialRequirementDto::getId).collect(Collectors.toList());
        Map<Long, PurchaseMaterialRequirementDto> requirementMap =
                this.getDetailByIds(requirementIdList).stream().collect(Collectors.toMap(PurchaseMaterialRequirementDto::getId, Function.identity()
                        , (a,
                           b) -> a));

        for (PurchaseMaterialRequirementDto materialRequirementDto : requirementDtos) {
            PurchaseMaterialRequirementDto requirementDto = requirementMap.get(materialRequirementDto.getId());
//            PurchaseMaterialRequirementDto requirementDto = this.getDetailById(materialRequirementDto.getId());

            if (requirementDto != null) {
                List<PurchaseOrderDetailDto> orderDetailDtos = materialRequirementDto.getPurchaseOrderDetailDtos();
                for (PurchaseOrderDetailDto orderDetailDto : orderDetailDtos) {
                    orderDetailDto.setMaterialPurchaseRequirementId(requirementDto.getId());
                    orderDetailDto.setErpCode(requirementDto.getErpCode());
                    orderDetailDto.setMaterielDescr(requirementDto.getMaterielDescr());
                    orderDetailDto.setMaterielId(requirementDto.getMaterielId());
                    orderDetailDto.setPamCode(requirementDto.getPamCode());
                    orderDetailDto.setProjectId(requirementDto.getProjectId());
                    orderDetailDto.setUnit(requirementDto.getUnit());
                    orderDetailDto.setUnitCode(requirementDto.getUnitCode());
                    orderDetailDto.setDeliveryAddress(requirementDto.getDeliveryAddress());
                    orderDetailDto.setConsignee(requirementDto.getConsignee());
                    orderDetailDto.setContactPhone(requirementDto.getContactPhone());
                }
                purchaseOrderDetailService.saveBatch(orderDetailDtos, userBy);
            }
        }

        //更新采购需求状态
        if (CollectionUtils.isNotEmpty(requirementIdList)) {
            this.batchUpdateStatus(requirementIdList);
        }
        return true;
    }

    /**
     * 收集需求变更的采购订单id
     *
     * @param dto
     * @return
     */
    public List<Long> collectChangingPurchaseOrderId(PurchaseMaterialRequirementDto dto) {
        Set<Long> purchaseOrderIdSet = new HashSet<>();
        for (PurchaseMaterialRequirementDto materialRequirementDto : dto.getRequirementDtos()) {
            for (PurchaseOrderDetailDto orderDetail : materialRequirementDto.getPurchaseOrderDetailDtos()) {
                purchaseOrderIdSet.add(orderDetail.getPurchaseOrderId());
            }
        }
        return new ArrayList<>(purchaseOrderIdSet);
    }

    /**
     * 加入待变更列表订单行校验
     *
     * @param dto
     * @param purchaseOrderDetailDto
     * @param purchaseOrder
     * @param vendorCodeList
     * @param response
     */
    public String addChangingOrderCheck(PurchaseMaterialRequirementDto dto,
                                        PurchaseOrderDetailDto purchaseOrderDetailDto,
                                        PurchaseOrder purchaseOrder,
                                        List<String> vendorCodeList,
                                        DataResponse<List<OrganizationCustomDict>> response,
                                        Set<String> purchaseOrderGroupRuleSet,
                                        List<PurchaseOrderDetail> purchaseOrderDetailList) {

        // a)采购订单输入框是否有值
//        Guard.notNull(purchaseOrderDetailDto.getPurchaseOrderId(), "请选择已生效的采购订单");
        if (purchaseOrderDetailDto.getPurchaseOrderId() == null) {
            purchaseOrderDetailDto.setErrorMsg("请选择已生效的采购订单");
            return "请选择已生效的采购订单";
        }

        // b)下达数量输入框是否有不为0的值
//        Guard.isTrue(purchaseOrderDetailDto.getOrderNum().compareTo(BigDecimal.ZERO) != 0, "已经选择了生效的采购订单，下达数量不能为0");
        if (purchaseOrderDetailDto.getOrderNum().compareTo(BigDecimal.ZERO) == 0) {
            purchaseOrderDetailDto.setErrorMsg("已经选择了生效的采购订单，下达数量不能为0");
            return "已经选择了生效的采购订单，下达数量不能为0";
        }

        // c)物料的批准供应商中是否存在加入待变更订单的供应商
//        Guard.isTrue(vendorCodeList.contains(purchaseOrderDetailDto.getVendorNum()), String.format("物料编码：%s，的批准供应商没有供应商：%s", dto.getErpCode(),
//        purchaseOrderDetailDto.getVendorName()));
        if (!vendorCodeList.contains(purchaseOrderDetailDto.getVendorNum())) {
            purchaseOrderDetailDto.setErrorMsg(String.format("物料编码：%s，的批准供应商没有供应商：%s", dto.getErpCode(), purchaseOrderDetailDto.getVendorName()));
            return String.format("物料编码：%s，的批准供应商没有供应商：%s", dto.getErpCode(), purchaseOrderDetailDto.getVendorName());
        }

        // d)根据组织参数：无定价是否允许下采购订单，如果配置为：1，校验物料有没定价
        if (ListUtils.isNotEmpty(response.getData()) && "1".equals(response.getData().get(0).getValue())) {
            //根据erp物料编码+供应商编码+供应商地点+ou，查询是否有满足有效期内
            String erpCode = dto.getErpCode();
            String vendorCode = purchaseOrderDetailDto.getVendorNum();
            String vendorSiteCode = purchaseOrderDetailDto.getVendorSiteCode();
            Long ouId = Long.valueOf(dto.getProjectOuId());
            PurchaseBpaPriceExample purchaseBpaPriceExample = new PurchaseBpaPriceExample();
            purchaseBpaPriceExample.createCriteria().andMaterialCodeEqualTo(erpCode)
                    .andVendorCodeEqualTo(vendorCode)
                    .andVendorSiteCodeEqualTo(vendorSiteCode)
                    .andOuIdEqualTo(ouId)
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<PurchaseBpaPrice> purchaseBpaPrices = purchaseBpaPriceMapper.selectByExample(purchaseBpaPriceExample);
            if (ListUtils.isNotEmpty(purchaseBpaPrices)) {
                int size = purchaseBpaPrices.size();
                AtomicInteger resultNum = new AtomicInteger();
                purchaseBpaPrices.forEach(p -> {
                    boolean result;
                    //当失效日期为空则无限大，并且分段价格不能为空或者小于等于0
                    if (null == p.getEndDate() && null != p.getPriceOverride() && p.getPriceOverride().compareTo(BigDecimal.ZERO) > 0) {
                        result = true;
                    } else {
                        //分段价格不能为空或者小于等于0
                        if (null == p.getPriceOverride() || p.getPriceOverride().compareTo(BigDecimal.ZERO) <= 0) {
                            result = false;
                        } else {
                            //符合开始时间+失效时间区间内
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATE_PATTERN);
                            String startDateStr = DateUtil.format(p.getStartDate(), DateUtil.DATE_PATTERN);
                            LocalDate startDateLocal = LocalDate.parse(startDateStr, formatter);
                            String nowStr = DateUtil.format(new Date(), DateUtil.DATE_PATTERN);
                            LocalDate nowLocal = LocalDate.parse(nowStr, formatter);
                            String endDateStr = DateUtil.format(p.getEndDate(), DateUtil.DATE_PATTERN);
                            logger.info("获取的生效时间:{},失效时间:{},当前时间:{}", startDateStr, endDateStr, nowStr);
                            LocalDate endDateLocal = LocalDate.parse(endDateStr, formatter);
                            if ((startDateLocal.isBefore(nowLocal) || startDateLocal.isEqual(nowLocal))
                                    && (endDateLocal.isAfter(nowLocal) || endDateLocal.isEqual(nowLocal))) {
                                result = true;
                            } else {
                                result = false;
                            }
                        }
                    }
                    if (!result) {
                        resultNum.getAndIncrement();
                    }
                });
                if (size == resultNum.intValue()) {
                    purchaseOrderDetailDto.setPurchaseBpaPrice(false);
                }
            } else {
                purchaseOrderDetailDto.setPurchaseBpaPrice(false);
            }
        }
//        Guard.isTrue(purchaseOrderDetailDto.getPurchaseBpaPrice(), String.format("物料编码：%s，在供应商：%s下无有效定价，请联系定价人员在ERP维护定价信息", dto.getErpCode(),
        //        purchaseOrderDetailDto.getVendorName()));
        if (!purchaseOrderDetailDto.getPurchaseBpaPrice()) {
            purchaseOrderDetailDto.setErrorMsg(String.format("物料编码：%s，在供应商：%s下无有效定价，请联系定价人员在ERP维护定价信息", dto.getErpCode(),
                    purchaseOrderDetailDto.getVendorName()));
            return String.format("物料编码：%s，在供应商：%s下无有效定价，请联系定价人员在ERP维护定价信息", dto.getErpCode(), purchaseOrderDetailDto.getVendorName());
        }

        // e)采购订单和需求行的业务实体不一致时，报：采购订单和需求的业务实体不一致
//        Guard.isTrue(Objects.equals(dto.getProjectOuId(), String.valueOf(purchaseOrderDetailDto.getOuId())), "采购订单和需求的业务实体不一致");
        if (!Objects.equals(dto.getProjectOuId(), String.valueOf(purchaseOrderDetailDto.getOuId()))) {
            purchaseOrderDetailDto.setErrorMsg("采购订单和需求的业务实体不一致");
            return "采购订单和需求的业务实体不一致";
        }

        // f)校验订单状态是变更中的
        if (purchaseOrder != null && Objects.equals(purchaseOrder.getOrderStatus(), PurchaseOrderStatus.CHANGING.code())) {
            String changeName = "";
            PurchaseOrderChangeHistoryExample orderChangeExample = new PurchaseOrderChangeHistoryExample();
            orderChangeExample.createCriteria().andOriginIdEqualTo(purchaseOrder.getId())
                    .andOrderStatusEqualTo(PurchaseOrderStatus.CHANGING.code())
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<PurchaseOrderChangeHistory> orderChangeList = purchaseOrderChangeHistoryMapper.selectByExample(orderChangeExample);
            if (CollectionUtils.isNotEmpty(orderChangeList)) {
                changeName = CacheDataUtils.findUserNameById(orderChangeList.get(0).getCreateBy());
            }
//            throw new BizException(Code.ERROR, String.format("订单变更中，请联系变更人：%s确认", changeName));
            purchaseOrderDetailDto.setErrorMsg(String.format("订单变更中，请联系变更人：%s确认", changeName));
            return String.format("订单变更中，请联系变更人：%s确认", changeName);
        }

        // g)组织参数：采购订单分组规则，的参数值为：OU+供应商+项目 时，校验新增的订单行的项目和所选订单的原订单行的项目是否一致（和所有原订单行的项目都不一致才算是不一致）
        if (purchaseOrderGroupRuleSet.contains("1")) {
            List<Long> projectIdList = purchaseOrderDetailList.stream().map(PurchaseOrderDetail::getProjectId).collect(Collectors.toList());
            if (!projectIdList.contains(dto.getProjectId())) {
//                throw new BizException(Code.ERROR, String.format("物料编码：%s的项目和订单：%s的项目不一致，该物料需求不允许加入到该订单", dto.getErpCode(), purchaseOrder.getNum
                //                ()));
                purchaseOrderDetailDto.setErrorMsg(String.format("物料编码：%s的项目和订单：%s的项目不一致，该物料需求不允许加入到该订单", dto.getErpCode(), purchaseOrder.getNum()));
                return String.format("物料编码：%s的项目和订单：%s的项目不一致，该物料需求不允许加入到该订单", dto.getErpCode(), purchaseOrder.getNum());
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addChangingOrder(PurchaseMaterialRequirementDto dto, Long userBy) {
        Asserts.notEmpty(userBy, ErrorCode.CTC_BUYER_INFO_NOT_NULL);
        List<PurchaseMaterialRequirementDto> requirementDtos = dto.getRequirementDtos();
        Asserts.isTrue(requirementDtos.stream().allMatch(s -> s.getId() != null), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_ID_NOT_NULL);

        //查找组织参数：采购订单分组规则
        Set<String> purchaseOrderGroupRuleSet = organizationCustomDictService.queryByName(Constants.PURCHASE_ORDER_GROUP_RULE,
                SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
        if (CollectionUtils.isEmpty(purchaseOrderGroupRuleSet) || !(purchaseOrderGroupRuleSet.contains("1") || purchaseOrderGroupRuleSet.contains(
                "2"))) {
            throw new BizException(Code.ERROR, "未找到正确的采购订单分组规则，请检查使用单位层级的组织参数：采购订单分组规则的配置");
        }

        //查找组织参数：是否查询控制一揽子协议
        final Map<String, Object> query = new HashMap<>();
        query.put("orgId", SystemContext.getUnitId());
        query.put("orgFrom", "company");
        query.put("name", "无定价是否允许下采购订单");
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationCustomDict>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
        });

        //批量查询待变更的订单
        List<Long> purchaseOrderIdList = collectChangingPurchaseOrderId(dto);
        Guard.notNull(purchaseOrderIdList, "没有待变更的订单");
        PurchaseOrderExample example = new PurchaseOrderExample();
        example.createCriteria().andIdIn(purchaseOrderIdList);
        List<PurchaseOrder> purchaseOrderList = purchaseOrderMapper.selectByExample(example);
        Map<Long, PurchaseOrder> purchaseOrderMap = purchaseOrderList.stream().collect(Collectors.toMap(PurchaseOrder::getId, Function.identity()));
        // 校验订单是否已同步到FAP系统
        validateOrdersNotSyncedToFap(purchaseOrderList);

        //批量查询待变更的订单的原订单行
        PurchaseOrderDetailExample detailExample = new PurchaseOrderDetailExample();
        detailExample.createCriteria().andPurchaseOrderIdIn(purchaseOrderIdList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderDetailMapper.selectByExample(detailExample);
        Map<Long, List<PurchaseOrderDetail>> purchaseOrderDetailMap =
                purchaseOrderDetailList.stream().collect(Collectors.groupingBy(PurchaseOrderDetail::getPurchaseOrderId));

        //批量查询采购需求
        List<Long> requirementIdList = requirementDtos.stream().map(PurchaseMaterialRequirementDto::getId).collect(Collectors.toList());
        Map<Long, PurchaseMaterialRequirementDto> requirementMap =
                this.getDetailByIds(requirementIdList).stream().collect(Collectors.toMap(PurchaseMaterialRequirementDto::getId, Function.identity()
                        , (a,
                           b) -> a));

        List<PurchaseOrderDetailChangeHistory> orderDetailChangeHistoryNewList = new ArrayList<>();
        for (PurchaseMaterialRequirementDto materialRequirementDto : requirementDtos) {
            PurchaseMaterialRequirementDto requirementDto = requirementMap.get(materialRequirementDto.getId());
            //查询需求物料的批准供应商
            List<String> vendorCodeList = vendorAslService.selectListByMaterialCode(requirementDto.getErpCode(),
                    requirementDto.getProjectOrganizationCode()).stream().map(VendorAslCtc::getVendorCode).collect(Collectors.toList());

            if (requirementDto != null) {
                List<PurchaseOrderDetailDto> orderDetailList = materialRequirementDto.getPurchaseOrderDetailDtos();
                for (PurchaseOrderDetailDto orderDetail : orderDetailList) {
                    //订单行校验
                    String errorMsg = addChangingOrderCheck(requirementDto, orderDetail, purchaseOrderMap.get(orderDetail.getPurchaseOrderId()),
                            vendorCodeList, response, purchaseOrderGroupRuleSet, purchaseOrderDetailMap.get(orderDetail.getPurchaseOrderId()));
                    if (StringUtils.isNotEmpty(errorMsg)) return errorMsg;

                    PurchaseOrderDetailChangeHistoryDto orderDetailChangeHistory = BeanConverter.copy(orderDetail,
                            PurchaseOrderDetailChangeHistoryDto.class);
                    orderDetailChangeHistory.setMaterialPurchaseRequirementId(requirementDto.getId());
                    orderDetailChangeHistory.setErpCode(requirementDto.getErpCode());
                    orderDetailChangeHistory.setMaterielDescr(requirementDto.getMaterielDescr());
                    orderDetailChangeHistory.setMaterielId(requirementDto.getMaterielId());
                    orderDetailChangeHistory.setPamCode(requirementDto.getPamCode());
                    orderDetailChangeHistory.setProjectId(requirementDto.getProjectId());
                    orderDetailChangeHistory.setProjectNum(requirementDto.getProjectNum());
                    orderDetailChangeHistory.setProjectName(requirementDto.getProjectName());
                    orderDetailChangeHistory.setUnit(requirementDto.getUnit());
                    orderDetailChangeHistory.setUnitCode(requirementDto.getUnitCode());
                    orderDetailChangeHistory.setDeliveryTime(requirementDto.getDeliveryTime());
                    orderDetailChangeHistory.setCancelNum(BigDecimal.ZERO);
                    orderDetailChangeHistory.setStatus(PurchaseOrderDetailStatus.ORDER_RELEASED.code());
                    orderDetailChangeHistory.setChangeStatus(PurchaseOrderDetailChangeHistoryChangeStatus.CHANGING.code());
                    orderDetailChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
                    orderDetailChangeHistory.setDeletedFlag(DeletedFlag.VALID.code());
                    orderDetailChangeHistory.setDeliveryAddress(requirementDto.getDeliveryAddress());
                    orderDetailChangeHistory.setConsignee(requirementDto.getConsignee());
                    orderDetailChangeHistory.setContactPhone(requirementDto.getContactPhone());
                    orderDetailChangeHistoryNewList.add(orderDetailChangeHistory);
                }
            }
        }

        //更新订单状态
        for (PurchaseOrder purchaseOrder : purchaseOrderList) {
            purchaseOrder.setOrderStatus(PurchaseOrderStatus.CHANGING.code());
        }
        purchaseOrderExtMapper.batchUpdate(purchaseOrderList);

        //构建变更订单头
        List<PurchaseOrderChangeHistory> orderChangeHistoryList = BeanConverter.copy(purchaseOrderList, PurchaseOrderChangeHistory.class);
        for (PurchaseOrderChangeHistory orderChangeHistory : orderChangeHistoryList) {
            orderChangeHistory.setOriginId(orderChangeHistory.getId());
            orderChangeHistory.setOriginCreateAt(orderChangeHistory.getCreateAt());
            orderChangeHistory.setId(null);
            orderChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
            orderChangeHistory.setOrderStatus(PurchaseOrderStatus.CHANGING.code());
            orderChangeHistory.setCreateBy(userBy);
            orderChangeHistory.setUpdateBy(null);
            orderChangeHistory.setUpdateAt(null);
        }
        purchaseOrderChangeHistoryExtMapper.batchInsert(orderChangeHistoryList);

        //构建变更订单行
        List<PurchaseOrderDetailChangeHistory> orderDetailChangeHistoryList = BeanConverter.copy(purchaseOrderDetailList,
                PurchaseOrderDetailChangeHistory.class);
        for (PurchaseOrderDetailChangeHistory orderDetailChangeHistory : orderDetailChangeHistoryList) {
            orderDetailChangeHistory.setOriginId(orderDetailChangeHistory.getId());
            orderDetailChangeHistory.setOriginCreateAt(orderDetailChangeHistory.getCreateAt());
            orderDetailChangeHistory.setOriginCreateByName(CacheDataUtils.findUserNameById(orderDetailChangeHistory.getCreateBy()));
            orderDetailChangeHistory.setId(null);
            orderDetailChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
            orderDetailChangeHistory.setChangeStatus(PurchaseOrderDetailChangeHistoryChangeStatus.CHANGING.code());
            orderDetailChangeHistory.setRecordId(null);
            orderDetailChangeHistory.setCreateBy(userBy);
            orderDetailChangeHistory.setUpdateBy(null);
            orderDetailChangeHistory.setUpdateAt(null);
        }
        orderDetailChangeHistoryList.addAll(orderDetailChangeHistoryNewList);
        purchaseOrderDetailChangeHistoryExtMapper.batchInsert(orderDetailChangeHistoryList);

        //更新采购需求状态
        if (CollectionUtils.isNotEmpty(requirementIdList)) {
            this.batchUpdateStatus(requirementIdList);
        }
        return null;
    }

    @Override
    public String getRequirementRedisNumber(Long projectId, Date deliveryTime, String pamCode, BigDecimal needTotal, BigDecimal differenceNumber) {
        String requirementRedisKey = "requirementFinalNeedTotal_" + projectId + "_"
                + DateUtil.format(deliveryTime, "yyyyMMdd") + "_" + pamCode;
        StringBuffer requirementSB = new StringBuffer();
        requirementSB.append(String.format("if redis.call('exists', '%s') == 0 then \n", requirementRedisKey));
        requirementSB.append(String.format("redis.call('setnx', '%s', %s); \n", requirementRedisKey,
                Optional.ofNullable(needTotal).orElse(BigDecimal.ZERO)));
        requirementSB.append(String.format("redis.call('expire', '%s', %d); \n", requirementRedisKey, 3600L));
        requirementSB.append(String.format("redis.call('incrbyfloat', '%s', %s); \n", requirementRedisKey,
                Optional.ofNullable(differenceNumber).orElse(BigDecimal.ZERO)));
        requirementSB.append(String.format("return redis.call('get', '%s'); \n", requirementRedisKey));
        requirementSB.append("else \n");

        requirementSB.append(String.format("redis.call('incrbyfloat', '%s', %s); \n", requirementRedisKey,
                Optional.ofNullable(differenceNumber).orElse(BigDecimal.ZERO)));
        requirementSB.append(String.format("return redis.call('get', '%s'); \n", requirementRedisKey));

        requirementSB.append("end; \n");
        return redisLuaScriptServer.executeLuaScript(requirementSB.toString(), new ArrayList<>(), "");
    }

    @Override
    public List<PurchaseMaterialRequirementDto> selectPurchaseMaterialRequirementList(PurchaseMaterialRequirementDto query) {
        logger.info("selectPurchaseMaterialRequirementList请求参数为:{}", JSON.toJSONString(query));
        List<PurchaseMaterialRequirementDto> dtos = purchaseMaterialRequirementExtMapper.selectPurchaseMaterialRequirementList(query);
        if (CollectionUtils.isEmpty(dtos)) {
            return new ArrayList<>();
        }

        logger.info("selectPurchaseMaterialRequirementList响应为:{}", JSON.toJSONString(dtos));
        List<Long> requirementIds = dtos.stream().map(PurchaseMaterialRequirementDto::getId).collect(Collectors.toList());
        List<PurchaseMaterialQuantityDto> releasedQuantities =
                purchaseMaterialRequirementExtMapper.selectPurchaseMaterialReleasedQuantityList(requirementIds);
        Map<Long, BigDecimal> releasedQuantitiesMap = releasedQuantities.stream()
                .collect(Collectors.toMap(PurchaseMaterialQuantityDto::getRequirementId, PurchaseMaterialQuantityDto::getQuantity));
        List<PurchaseMaterialQuantityDto> orderQuantities =
                purchaseMaterialRequirementExtMapper.selectPurchaseMaterialOrderQuantityList(requirementIds);
        Map<Long, BigDecimal> orderQuantitiesMap = orderQuantities.stream()
                .collect(Collectors.toMap(PurchaseMaterialQuantityDto::getRequirementId, PurchaseMaterialQuantityDto::getQuantity));
        for (PurchaseMaterialRequirementDto dto : dtos) {
            Long id = dto.getId();
            BigDecimal releasedQuantity = Optional.ofNullable(releasedQuantitiesMap.get(id)).orElse(BigDecimal.ZERO);
            BigDecimal orderQuantity = Optional.ofNullable(orderQuantitiesMap.get(id)).orElse(BigDecimal.ZERO);
            dto.setReleasedQuantity(releasedQuantity);
            dto.setOrderQuantity(orderQuantity);
            //未下达量 = 总需求量 - 已下达量 - 关闭数量
            dto.setUnreleasedAmount(Optional.ofNullable(dto.getNeedTotal()).orElse(BigDecimal.ZERO).subtract(releasedQuantity).subtract(dto.getClosedQuantity()));
            packagePurchaseMaterialRequirementDto(dto);
        }

        return dtos;
    }

    @Override
    public Boolean correctRequirement(MilepostDesignPlanDetailDto designPlanDetailQuery) {
        if (ListUtils.isEmpty(designPlanDetailQuery.getIdList())
                && !(StringUtils.isNotEmpty(designPlanDetailQuery.getPamCode()) && designPlanDetailQuery.getDeliveryTime() != null && StringUtils.isNotEmpty(designPlanDetailQuery.getWbsSummaryCode()))) {
            throw new BizException(Code.ERROR, "查询参数不能为空");
        }

        // 按照详设表milepost_design_plan_detail字段：物料pam编码+WBS+需求日期汇总统计模组状态module_status=1的数量number总数
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos = milepostDesignPlanDetailExtMapper.calculateMilepostDesignPlanDetailNumber(designPlanDetailQuery);
        // 如果没有详设记录并且有采购需求发布明细，需要追加负数
        if (ListUtils.isEmpty(milepostDesignPlanDetailDtos)) {
            if (StringUtils.isNotEmpty(designPlanDetailQuery.getPamCode()) && designPlanDetailQuery.getDeliveryTime() != null && StringUtils.isNotEmpty(designPlanDetailQuery.getWbsSummaryCode())) {
                designPlanDetailQuery.setNumber(BigDecimal.ZERO); //找不到详设数量当0处理
                // 根据物料pam编码+WBS+需求日期汇总统计采购需求数量
                List<PurchaseMaterialRequirementDto> requirementList = purchaseMaterialRequirementExtMapper.calculateMaterialRequirementNumber(designPlanDetailQuery);
                correctMaterialRequirement(designPlanDetailQuery, requirementList);
            } else {
                throw new BizException(Code.ERROR, "未找到对应的详设数据");
            }
        } else {
            for (MilepostDesignPlanDetailDto designPlanDetail : milepostDesignPlanDetailDtos) {
                //查询是否有多个采购需求头
                PurchaseMaterialRequirementExample example = new PurchaseMaterialRequirementExample();
                example.createCriteria().andPamCodeEqualTo(designPlanDetail.getPamCode())
                        .andWbsSummaryCodeEqualTo(designPlanDetail.getWbsSummaryCode())
                        .andDeliveryTimeEqualTo(designPlanDetail.getDeliveryTime())
                        .andDeletedFlagEqualTo(Boolean.FALSE);
                long count = purchaseMaterialRequirementMapper.countByExample(example);
                if (count > 1) {
                    throw new BizException(Code.ERROR, String.format("pamcode【%s】、wbsCode【%s】、deliveryTime【%s】找到多个对应的采购需求头！",
                            designPlanDetail.getPamCode(), designPlanDetail.getWbsSummaryCode(), designPlanDetail.getDeliveryTime()));
                }
                // 按照详设表milepost_design_plan_detailid：物料pam编码+WBS+需求日期汇总统计采购需求发布明细数量
                List<PurchaseMaterialRequirementDto> requirementList = purchaseMaterialRequirementExtMapper.calculateMaterialRequirementNumber(designPlanDetail);
                correctMaterialRequirement(designPlanDetail, requirementList);
            }
        }
        return null;
    }

    private void correctMaterialRequirement(MilepostDesignPlanDetailDto designPlanDetail, List<PurchaseMaterialRequirementDto> requirementList) {
        if (ListUtils.isEmpty(requirementList)) {
            throw new BizException(Code.ERROR, String.format("pamcode【%s】、wbsCode【%s】、deliveryTime【%s】未找到对应的采购需求头！",
                    designPlanDetail.getPamCode(), designPlanDetail.getWbsSummaryCode(), designPlanDetail.getDeliveryTime()));
        }
        List<Long> requirementIdList = new ArrayList<>();
        requirementList.forEach(requirementDto -> {
            if (requirementDto.getNeedTotal().compareTo(designPlanDetail.getNumber()) == 0) {
                return;
            }
            BigDecimal diffrentNumber = designPlanDetail.getNumber().subtract(requirementDto.getNeedTotal());
            // 新增采购需求行，追加差异到采购需求头
            PurchaseMaterialReleaseDetail releaseDetail = new PurchaseMaterialReleaseDetail();
            releaseDetail.setDeletedFlag(Boolean.FALSE);
            releaseDetail.setPurchaseRequirementId(requirementDto.getId());
            releaseDetail.setProjectId(requirementDto.getProjectId());
            releaseDetail.setMaterialId(requirementDto.getMaterialId());
            releaseDetail.setDeliveryTime(designPlanDetail.getDeliveryTime());
            releaseDetail.setPublisherId(-1L);
            releaseDetail.setPublisherName("System");
            releaseDetail.setPublishNum(diffrentNumber);
            releaseDetail.setPublishTime(DateUtil.getCurrentDate());
            releaseDetail.setReleaseDetailStatus(2);
            releaseDetail.setMaterialPamCode(requirementDto.getPamCode());
            releaseDetail.setDesignPlanDetailId(requirementDto.getDesignPlanDetailId());
            purchaseMaterialReleaseDetailMapper.insertSelective(releaseDetail);

            // 按照行关联的头，查所有有效的行，累加数量回写到头表
            purchaseMaterialRequirementExtMapper.updataNeedTotal(requirementDto.getId());
            requirementIdList.add(requirementDto.getId());

        });
        batchUpdateStatus(requirementIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean closeOrOpenRequirement(PurchaseMaterialRequirementDto dto, Long userBy) {
        Asserts.notEmpty(dto.getId(), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_ID_NOT_NULL);
        Asserts.notEmpty(dto.getStatus(), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_STATUS_NOT_NULL);
        Asserts.notEmpty(userBy, ErrorCode.CTC_BUYER_INFO_NOT_NULL);
        // 并发时候的key值
        String lockKey = Constants.DistributedLockKey.DISTRIBUTED_LOCK_REQUIREMENT_CLOSE;
        try {
            if (DistributedCASLock.lock(lockKey, String.valueOf(dto.getId()), 1000 * 60L * 5, 1000 * 60L * 5)) {
                return dealWithPurchaseMaterialRequirement(dto, userBy);
            }
        } catch (Exception e) {
            logger.error("采购需求关闭/打开异常", e);
            throw new BizException(Code.ERROR, e.getMessage());
        } finally {
            DistributedCASLock.unLock(lockKey, String.valueOf(dto.getId()));
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean closeOrOpenRequirementWbs(PurchaseMaterialRequirementDto dto, Long userBy) {
        Asserts.notEmpty(dto.getId(), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_ID_NOT_NULL);
        Asserts.notEmpty(dto.getStatus(), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_STATUS_NOT_NULL);
        Asserts.notEmpty(userBy, ErrorCode.CTC_BUYER_INFO_NOT_NULL);
        // 并发时候的key值
        String lockKey = Constants.DistributedLockKey.DISTRIBUTED_LOCK_REQUIREMENT_CLOSE;
        try {
            if (DistributedCASLock.lock(lockKey, String.valueOf(dto.getId()), 1000 * 60L * 5, 1000 * 60L * 5)) {
                return dealWithPurchaseMaterialRequirementWbs(dto, userBy);
            }
        } catch (Exception e) {
            logger.error("采购需求关闭/打开异常", e);
            throw new BizException(Code.ERROR, e.getMessage());
        } finally {
            DistributedCASLock.unLock(lockKey, String.valueOf(dto.getId()));
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean closeOrOpenRequirementCommon(PurchaseMaterialRequirementDto dto, Long userBy) {
        Guard.notNull(dto.getId(), "物料采购需求id不能为空");
        Guard.notNull(dto.getStatus(), "物料采购需求状态不能为空");
        Guard.notNull(dto.getClosedOrOpenQuantity(), "关闭或者打开的数量不能为空");

        Guard.notNull(dto.getUnreleasedAmount(), "原未下达量不能为空");
        Guard.notNull(dto.getReleasedQuantity(), "原已下达量不能为空");
        Guard.notNull(dto.getClosedQuantity(), "原关闭数量不能为空");
        Guard.notNull(dto.getNeedTotal(), "原总需求量不能为空");
        // 并发时候的key值
        String lockKey = Constants.DistributedLockKey.DISTRIBUTED_LOCK_REQUIREMENT_CLOSE_COMMON;
        try {
            if (DistributedCASLock.lock(lockKey, String.valueOf(dto.getId()), 1000 * 60L * 5, 1000 * 60L * 5)) {
                return dealWithPurchaseMaterialRequirementCommon(dto, userBy);
            }
        } catch (Exception e) {
            logger.error("非柔性单位公共采购需求关闭/打开异常", e);
            throw new BizException(Code.ERROR, e.getMessage());
        } finally {
            DistributedCASLock.unLock(lockKey, String.valueOf(dto.getId()));
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean dealWithPurchaseMaterialRequirement(PurchaseMaterialRequirementDto dto, Long userBy) {
        Integer status = dto.getStatus();
        PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(dto.getId());
        PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();

        // 增加关闭/打开记录
        PurchaseMaterialCloseDetailDto materialCloseDetail = new PurchaseMaterialCloseDetailDto();
        materialCloseDetail.setCloseReason("");
        materialCloseDetail.setCloserId(userBy);
        UserInfo userInfo = CacheDataUtils.findUserById(userBy);
        if (userInfo != null) {
            materialCloseDetail.setCloserName(userInfo.getName());
        }
        materialCloseDetail.setMaterialId(requirement.getMaterielId());
        materialCloseDetail.setCloseTime(DateUtil.getCurrentDate());
        materialCloseDetail.setDeletedFlag(Boolean.FALSE);
        materialCloseDetail.setProjectId(requirement.getProjectId());
        materialCloseDetail.setPurchaseRequirementId(requirement.getId());
        materialCloseDetail.setMaterialPamCode(requirement.getPamCode());

        //如果是关闭
        if (status.intValue() == PurchaseMaterialRequirementStatus.CLOSED.code()) {
            if (requirement.getStatus().intValue() == PurchaseMaterialRequirementStatus.CLOSED.code()) {
                throw new BizException(Code.ERROR, "请勿重复关闭");
            }
            //先判断是否存在待下达订单
            example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andMaterialPurchaseRequirementIdEqualTo(dto.getId()).andStatusEqualTo(PurchaseOrderDetailStatus.ORDER_RELEASED.code());
            long count = purchaseOrderDetailMapper.countByExample(example);
            if (!ObjectUtils.isEmpty(count) && count > 0) {
                throw new BizException(Code.ERROR, "有待下达订单，不可以关闭");
            }
            requirement.setReason(dto.getReason());
            requirement.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
            // 关闭金额
//            requirement.setClosedAmount(dto.getWbsRemainingDemandOsCost());
            // 关闭数量
            requirement.setClosedQuantity(dto.getUnreleasedAmount());

            materialCloseDetail.setCloseReason(dto.getReason());
            materialCloseDetail.setCloseType(RequirementCloseTypeEnum.CLOSE.code());
            // 已下达数量
//            BigDecimal releasedQuantity = purchaseOrderDetailService.getReleasedQuantityByRequirementId(requirement.getId());
            // 已关闭数量
//            BigDecimal closedQuantity = purchaseMaterialCloseDetailService.getCloseNumSumByRequirementId(requirement.getId());
            // 等于关闭时采购需求界面待下达的数量
            materialCloseDetail.setCloseNum(dto.getUnreleasedAmount());

        } else if (status.intValue() == PurchaseMaterialRequirementStatus.WAIT_ORDER.code()) {//打开
            if (requirement.getStatus().intValue() == PurchaseMaterialRequirementStatus.WAIT_ORDER.code()) {
                throw new BizException(Code.ERROR, "请勿重复打开");
            }
            //项目是否已结项/终止
            Project project = projectService.selectByPrimaryKey(requirement.getProjectId());
            if (!ObjectUtils.isEmpty(project) && Objects.equals(project.getStatus(), ProjectStatus.CLOSE.getCode())) {
                throw new BizException(Code.ERROR, "项目已结项，不可以打开");
            }
            if (!ObjectUtils.isEmpty(project) && Objects.equals(project.getStatus(), ProjectStatus.TERMINATION.getCode())) {
                throw new BizException(Code.ERROR, "项目已终止，不可以打开");
            }
            //已下达量小于总需求量
            List<Integer> statusList = new ArrayList<>();
            statusList.add(PurchaseOrderDetailStatus.ORDER_CLOSED.code());//已关闭的采购订单
            statusList.add(PurchaseOrderDetailStatus.ORDER_PLACED.code());//已下单的采购订单
            example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andMaterialPurchaseRequirementIdEqualTo(dto.getId()).andStatusIn(statusList);
            List<PurchaseOrderDetail> orderDetails = purchaseOrderDetailMapper.selectByExample(example);
            BigDecimal orderNum = BigDecimal.ZERO;
            for (PurchaseOrderDetail orderDetail : orderDetails) {
                orderNum = orderNum.add(orderDetail.getOrderNum().subtract(orderDetail.getCancelNum()));
            }
            if (orderNum.compareTo(requirement.getNeedTotal()) >= 0) {
                throw new BizException(Code.ERROR, "已下单量大于等于总需求量，不可以打开");
            }
            requirement.setReason(null);
            requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
            // 关闭金额
//            requirement.setClosedAmount(BigDecimal.ZERO);
            // 关闭数量
            requirement.setClosedQuantity(BigDecimal.ZERO);

            // 已关闭数量
//            BigDecimal closedQuantity = purchaseMaterialCloseDetailService.getCloseNumSumByRequirementId(requirement.getId());
            // 打开的时候，将手工关闭的需求全部归0
            materialCloseDetail.setCloseNum(dto.getClosedQuantity());
            materialCloseDetail.setCloseType(RequirementCloseTypeEnum.OPEN.code());
        }
        // 增加关闭记录
        purchaseMaterialCloseDetailService.save(materialCloseDetail, userBy);
        //统计关闭数量，而非更新数量。
        BigDecimal closeNumSum = purchaseMaterialCloseDetailService.getCloseNumSumByRequirementId(requirement.getId());
        requirement.setClosedQuantity(closeNumSum);
        purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(requirement);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean dealWithPurchaseMaterialRequirementWbs(PurchaseMaterialRequirementDto dto, Long userBy) {
        Integer status = dto.getStatus();
        PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(dto.getId());
        PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();

        // 增加关闭/打开记录
        PurchaseMaterialCloseDetailDto materialCloseDetail = new PurchaseMaterialCloseDetailDto();
        materialCloseDetail.setCloseReason("");
        materialCloseDetail.setCloserId(userBy);
        UserInfo userInfo = CacheDataUtils.findUserById(userBy);
        if (userInfo != null) {
            materialCloseDetail.setCloserName(userInfo.getName());
        }
        materialCloseDetail.setMaterialId(requirement.getMaterielId());
        materialCloseDetail.setCloseTime(DateUtil.getCurrentDate());
        materialCloseDetail.setDeletedFlag(Boolean.FALSE);
        materialCloseDetail.setProjectId(requirement.getProjectId());
        materialCloseDetail.setPurchaseRequirementId(requirement.getId());
        materialCloseDetail.setMaterialPamCode(requirement.getPamCode());

        // 增加关闭记录
        // 修改 关闭为负数  减少未下达量，增加关闭数量 **逻辑参照文档
        BigDecimal closeQuantity = dto.getClosedQuantity() == null ? new BigDecimal(0) : dto.getClosedQuantity();

        //如果是关闭
        if (Objects.equals(status, PurchaseMaterialRequirementStatus.CLOSED.code())) {
            requirement.setReason(dto.getReason());
            // 关闭金额
            requirement.setClosedAmount(dto.getWbsRemainingDemandOsCost());
            // 关闭数量
            //requirement.setClosedQuantity(dto.getUnreleasedAmount());
            requirement.setClosedQuantity(Optional.ofNullable(requirement.getClosedQuantity()).orElse(BigDecimal.ZERO).add(closeQuantity));

            materialCloseDetail.setCloseReason(dto.getReason());

            PurchaseMaterialRequirementDto materialRequirementDto = purchaseOrderDetailExtMapper.getQuantityByRequirementId(requirement.getId());
            materialCloseDetail.setCloseNum(closeQuantity);
            if (closeQuantity.compareTo(materialRequirementDto.getUnreleasedAmount()) == 0) {
                requirement.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
            }

            materialCloseDetail.setCloseType(RequirementCloseTypeEnum.CLOSE.code());
        } else if (Objects.equals(status, PurchaseMaterialRequirementStatus.WAIT_ORDER.code())) {//打开
            //项目是否已结项/终止
            Project project = projectService.selectByPrimaryKey(requirement.getProjectId());
            if (!ObjectUtils.isEmpty(project) && Objects.equals(project.getStatus(), ProjectStatus.CLOSE.getCode())) {
                throw new BizException(Code.ERROR, "项目已结项，不可以打开");
            }
            if (!ObjectUtils.isEmpty(project) && Objects.equals(project.getStatus(), ProjectStatus.TERMINATION.getCode())) {
                throw new BizException(Code.ERROR, "项目已终止，不可以打开");
            }
            //已下达量小于总需求量
            List<Integer> statusList = new ArrayList<>();
            statusList.add(PurchaseOrderDetailStatus.ORDER_CLOSED.code());//已关闭的采购订单
            statusList.add(PurchaseOrderDetailStatus.ORDER_PLACED.code());//已下单的采购订单
            example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andMaterialPurchaseRequirementIdEqualTo(dto.getId()).andStatusIn(statusList);
            List<PurchaseOrderDetail> orderDetails = purchaseOrderDetailMapper.selectByExample(example);
            BigDecimal orderNum = BigDecimal.ZERO;
            for (PurchaseOrderDetail orderDetail : orderDetails) {
                orderNum = orderNum.add(orderDetail.getOrderNum().subtract(Optional.ofNullable(orderDetail.getCancelNum()).orElse(BigDecimal.ZERO)));
            }
            if (orderNum.compareTo(requirement.getNeedTotal()) >= 0) {
                throw new BizException(Code.ERROR, "已下单量大于等于总需求量，不可以打开");
            }
            requirement.setReason(null);
            requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
            // 关闭金额
            requirement.setClosedAmount(BigDecimal.ZERO);
            // 关闭数量
            //requirement.setClosedQuantity(BigDecimal.ZERO);
            requirement.setClosedQuantity(requirement.getClosedQuantity().subtract(closeQuantity));

            //打开时更新需求发布时间
            requirement.setReceiptsPublishDate(new Date());

            // 已关闭数量
            //BigDecimal closedQuantity = purchaseMaterialCloseDetailService.getCloseNumSumByRequirementId(requirement.getId());
            // 打开的时候，将手工关闭的需求全部归0
            materialCloseDetail.setCloseNum(closeQuantity);
            materialCloseDetail.setCloseType(RequirementCloseTypeEnum.OPEN.code());
        }
        purchaseMaterialCloseDetailService.save(materialCloseDetail, userBy);
        purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(requirement);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean dealWithPurchaseMaterialRequirementCommon(PurchaseMaterialRequirementDto dto, Long userBy) {
        Integer status = dto.getStatus();
        PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(dto.getId());

        // 增加关闭/打开记录
        PurchaseMaterialCloseDetailDto materialCloseDetail = new PurchaseMaterialCloseDetailDto();
        materialCloseDetail.setCloseReason("");
        materialCloseDetail.setCloserId(userBy);
        UserInfo userInfo = CacheDataUtils.findUserById(userBy);
        if (userInfo != null) {
            materialCloseDetail.setCloserName(userInfo.getName());
        }
        materialCloseDetail.setMaterialId(requirement.getMaterielId());
        materialCloseDetail.setCloseTime(DateUtil.getCurrentDate());
        materialCloseDetail.setDeletedFlag(Boolean.FALSE);
        materialCloseDetail.setProjectId(requirement.getProjectId());
        materialCloseDetail.setPurchaseRequirementId(requirement.getId());
        materialCloseDetail.setMaterialPamCode(requirement.getPamCode());

        BigDecimal closedOrOpenQuantity = dto.getClosedOrOpenQuantity();
        //如果是关闭
        if (Objects.equals(status, PurchaseMaterialRequirementStatus.CLOSED.code())) {
            requirement.setReason(dto.getReason());
            // 关闭金额
            requirement.setClosedAmount(dto.getWbsRemainingDemandOsCost());
            // 关闭数量
            requirement.setClosedQuantity(Optional.ofNullable(requirement.getClosedQuantity()).orElse(BigDecimal.ZERO).add(closedOrOpenQuantity));

            if (closedOrOpenQuantity.compareTo(dto.getUnreleasedAmount()) == 0) {
                requirement.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
            }

            materialCloseDetail.setCloseReason(dto.getReason());
            materialCloseDetail.setCloseNum(closedOrOpenQuantity);
            materialCloseDetail.setCloseType(RequirementCloseTypeEnum.CLOSE.code());
        } else if (Objects.equals(status, PurchaseMaterialRequirementStatus.WAIT_ORDER.code())) {//打开
            //项目是否已结项/终止
            Project project = projectService.selectByPrimaryKey(requirement.getProjectId());
            if (!ObjectUtils.isEmpty(project) && Objects.equals(project.getStatus(), ProjectStatus.CLOSE.getCode())) {
                throw new BizException(Code.ERROR, "项目已结项，不可以打开");
            }
            if (!ObjectUtils.isEmpty(project) && Objects.equals(project.getStatus(), ProjectStatus.TERMINATION.getCode())) {
                throw new BizException(Code.ERROR, "项目已终止，不可以打开");
            }
            //已下达量小于总需求量
            List<Integer> statusList = new ArrayList<>();
            statusList.add(PurchaseOrderDetailStatus.ORDER_CLOSED.code());//已关闭的采购订单
            statusList.add(PurchaseOrderDetailStatus.ORDER_PLACED.code());//已下单的采购订单
            PurchaseOrderDetailExample example = new PurchaseOrderDetailExample();
            example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andMaterialPurchaseRequirementIdEqualTo(dto.getId()).andStatusIn(statusList);
            List<PurchaseOrderDetail> orderDetails = purchaseOrderDetailMapper.selectByExample(example);
            BigDecimal orderNum = BigDecimal.ZERO;
            for (PurchaseOrderDetail orderDetail : orderDetails) {
                orderNum = orderNum.add(orderDetail.getOrderNum().subtract(Optional.ofNullable(orderDetail.getCancelNum()).orElse(BigDecimal.ZERO)));
            }
            if (orderNum.compareTo(requirement.getNeedTotal()) >= 0) {
                throw new BizException(Code.ERROR, "已下单量大于等于总需求量，不可以打开");
            }
            requirement.setReason(dto.getReason());
            requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
            // 关闭金额
            requirement.setClosedAmount(BigDecimal.ZERO);
            // 关闭数量
            requirement.setClosedQuantity(Optional.ofNullable(requirement.getClosedQuantity()).orElse(BigDecimal.ZERO).subtract(closedOrOpenQuantity));

            //打开时更新需求发布时间
            requirement.setReceiptsPublishDate(new Date());

            // 打开的时候，将手工关闭的需求全部归0
            materialCloseDetail.setCloseNum(closedOrOpenQuantity);
            materialCloseDetail.setCloseType(RequirementCloseTypeEnum.OPEN.code());
        }
        purchaseMaterialCloseDetailService.save(materialCloseDetail, userBy);
        purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(requirement);
        return true;
    }

    @Override
    public void addPurchaseMaterialReleaseDetail(Long materialId,
                                                 Long purchaseMaterialRequirementId,
                                                 MilepostDesignPlanDetailDto designPlanDetailSon,
                                                 Integer releaseDetailStatus,
                                                 Long publisherId,
                                                 BigDecimal publishNum) {

        PurchaseMaterialReleaseDetailDto purchaseMaterialReleaseDetailDto = new PurchaseMaterialReleaseDetailDto();
        purchaseMaterialReleaseDetailDto.setPurchaseRequirementId(purchaseMaterialRequirementId);
        purchaseMaterialReleaseDetailDto.setProjectId(designPlanDetailSon.getProjectId());
        purchaseMaterialReleaseDetailDto.setMaterialId(materialId);
        purchaseMaterialReleaseDetailDto.setDeliveryTime(designPlanDetailSon.getDeliveryTime());
        purchaseMaterialReleaseDetailDto.setPublisherId(publisherId);
        UserInfo publisher = CacheDataUtils.findUserById(publisherId);
        if (publisher != null) {
            purchaseMaterialReleaseDetailDto.setPublisherName(publisher.getName());
        }
        purchaseMaterialReleaseDetailDto.setPublishNum(publishNum);
        purchaseMaterialReleaseDetailDto.setPublishTime(DateUtil.getCurrentDate());
        purchaseMaterialReleaseDetailDto.setReleaseDetailStatus(releaseDetailStatus);
        purchaseMaterialReleaseDetailDto.setMaterialPamCode(designPlanDetailSon.getPamCode());
        purchaseMaterialReleaseDetailDto.setDesignPlanDetailId(designPlanDetailSon.getId());
        purchaseMaterialReleaseDetailService.save(purchaseMaterialReleaseDetailDto, null);
    }

    /**
     * 添加采购需求发布明细（WBS）
     *
     * @param requirement         采购需求
     * @param receipts            详细设计单据
     * @param planDetail          详细设计
     * @param releaseDetailStatus 发布明细状态（1=提交;2=变更）
     * @param publisherId         发布人
     * @param publishNum          当前设计信息的最新发布总量
     */
    @Override
    public void addWbsPurchaseMaterialReleaseDetail(PurchaseMaterialRequirement requirement, ProjectWbsReceipts receipts,
                                                    ProjectWbsReceipts changeReceipts, MilepostDesignPlanDetail planDetail,
                                                    Integer releaseDetailStatus, Long publisherId, BigDecimal publishNum, Long createBy) {
        PurchaseMaterialReleaseDetailDto purchaseMaterialReleaseDetailDto = new PurchaseMaterialReleaseDetailDto();
        purchaseMaterialReleaseDetailDto.setPurchaseRequirementId(requirement.getId());
        purchaseMaterialReleaseDetailDto.setProjectId(planDetail.getProjectId());
        purchaseMaterialReleaseDetailDto.setMaterialId(requirement.getMaterielId());
        purchaseMaterialReleaseDetailDto.setDeliveryTime(planDetail.getDeliveryTime());
        purchaseMaterialReleaseDetailDto.setPublisherId(publisherId);
        UserInfo publisher = CacheDataUtils.findUserById(publisherId);
        if (publisher != null) {
            purchaseMaterialReleaseDetailDto.setPublisherName(publisher.getName());
        }
        purchaseMaterialReleaseDetailDto.setProjectWbsReceiptsId(receipts.getId());
        purchaseMaterialReleaseDetailDto.setRequirementCode(receipts.getRequirementCode());
        // 如果需求变更单据不为空，则设置需求变更单据id
        if (changeReceipts != null) {
            purchaseMaterialReleaseDetailDto.setProjectWbsReceiptsChangeId(changeReceipts.getId());
            purchaseMaterialReleaseDetailDto.setChangeRequirementCode(changeReceipts.getRequirementCode());
        }
        purchaseMaterialReleaseDetailDto.setPublishNum(publishNum);
        purchaseMaterialReleaseDetailDto.setPublishTime(DateUtil.getCurrentDate());
        purchaseMaterialReleaseDetailDto.setReleaseDetailStatus(releaseDetailStatus);
        purchaseMaterialReleaseDetailDto.setMaterialPamCode(planDetail.getPamCode());
        purchaseMaterialReleaseDetailDto.setDesignPlanDetailId(planDetail.getId());
        purchaseMaterialReleaseDetailService.save(purchaseMaterialReleaseDetailDto, Optional.ofNullable(createBy).orElse(SystemContext.getUserId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseMaterialRequirementDto> recursiveGenerateRequirementByPurchase(List<MilepostDesignPlanDetailDto> detailDtoList, List<Long> requirementIdList) {
        logger.info("begin-提前采购生成需求");
        ArrayList<PurchaseMaterialRequirementDto> saveRequirementDtoList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto designPlanDetailSon : detailDtoList) {
            // 旧的生成采购需求的逻辑
            //未产生物料需求的设计信息(物料小类非外协半成品)
            // 详细设计status为空的，不做处理
            if (Boolean.TRUE.equals(designPlanDetailSon.getDeletedFlag()) || null == designPlanDetailSon.getStatus()) {
                continue;
            }

            Guard.notNull(designPlanDetailSon.getProjectId(), String.format("子级详细设计id：%s的项目id不能为空", designPlanDetailSon.getId()));
            Guard.notNullOrEmpty(designPlanDetailSon.getPamCode(), String.format("子级详细设计id：%s的pam编码不能为空", designPlanDetailSon.getId()));
            Guard.notNull(designPlanDetailSon.getDeliveryTime(), String.format("子级详细设计id：%s的交货时间不能为空", designPlanDetailSon.getId()));
            Long organizationId = projectService.getOrganizationIdByProjectId(designPlanDetailSon.getProjectId());
            MaterialDto materialDto = materialExtService.invokeMaterialApiGetByPamCode(designPlanDetailSon.getPamCode(), organizationId);//TODO
            if (materialDto == null || !itemTypeList.contains(materialDto.getItemType())) {
                continue;
            }

            // :3082,代表机器人的物料,将来可考虑获取上下文的ouId
            logger.info("recursiveGenerateRequirementByPurchase准备产生采购需求，库存组织：{}，designPlanDetailSon：{}",
                    organizationId, JsonUtils.toString(designPlanDetailSon));

            //查询该项目某个交货时间对应的物料采购需求
            PurchaseMaterialRequirementDto exitDto = getByProjectIdAndPamCodeAndDeliveryTime(
                    designPlanDetailSon.getProjectId(),
                    designPlanDetailSon.getDeliveryTime(),
                    designPlanDetailSon.getPamCode());
            PurchaseMaterialRequirementDto dto = new PurchaseMaterialRequirementDto();
            PurchaseMaterialRequirementDto resultDto = null;
            BigDecimal purchaseNum = Optional.ofNullable(designPlanDetailSon.getPurchaseNum()).orElse(BigDecimal.ZERO);
            if (exitDto == null) {
                //生成物料采购需求
                logger.info("recursiveGenerateRequirementByPurchase生成物料采购需求");
                dto.setProjectId(designPlanDetailSon.getProjectId());
                dto.setErpCode(designPlanDetailSon.getErpCode());
                dto.setPamCode(designPlanDetailSon.getPamCode());
                dto.setDeliveryTime(designPlanDetailSon.getDeliveryTime());
                dto.setMaterielId(materialDto.getId());
                dto.setMaterielDescr(materialDto.getItemInfo());

                String requirementRedisNumber = getRequirementRedisNumber(designPlanDetailSon.getProjectId(),
                        designPlanDetailSon.getDeliveryTime(), designPlanDetailSon.getPamCode(), null, purchaseNum);

                dto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                dto.setUnitCode(designPlanDetailSon.getUnitCode());
                dto.setUnit(designPlanDetailSon.getUnit());
                resultDto = this.add(dto);
                saveRequirementDtoList.add(resultDto);
            } else {
                dto.setId(exitDto.getId());
                //更新物料采购需求总需求量
                logger.info("recursiveGenerateRequirementByPurchase更新物料采购需求总需求量");

                if (BigDecimal.ZERO.compareTo(purchaseNum) != 0) {
                    String requirementRedisNumber = getRequirementRedisNumber(designPlanDetailSon.getProjectId(),
                            designPlanDetailSon.getDeliveryTime(), designPlanDetailSon.getPamCode(), exitDto.getNeedTotal(), purchaseNum);

                    dto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                    resultDto = this.update(dto);
                    //updateStatus(dto.getId());//根据总需求量更新采购需求的状态
                    requirementIdList.add(resultDto.getId());
                }
            }
            if (BigDecimal.ZERO.compareTo(purchaseNum) != 0) {
                //生成物料采购需求发布明细
                addPurchaseMaterialReleaseDetail(materialDto.getId(),
                        resultDto == null ? null : resultDto.getId(),
                        designPlanDetailSon,
                        ReleaseDetailStatus.SUBMIT.code(),
                        designPlanDetailSon.getCreateBy(),
                        purchaseNum);
            }

            //更新设计信息的是否产生物料需求字段
            designPlanDetailSon.setGenerateRequirement(DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code());
            milepostDesignPlanDetailService.save(designPlanDetailSon, null);

        }
        logger.info("end-提前采购生成需求");
        return saveRequirementDtoList;
    }

    @Override
    public List<PurchaseMaterialRequirementDto> recursiveGenerateRequirement(Long detailPlanParentId, List<Long> requirementIdList) {
        logger.info("begin-详细设计id:{}，模组确认产生采购需求", detailPlanParentId);
        //  初始化用于收集【新创建】DTO 的列表
        List<PurchaseMaterialRequirementDto> newlyCreatedRequirements = new ArrayList<>();
        MilepostDesignPlanDetailDto parentDto = milepostDesignPlanDetailService.getById(detailPlanParentId);
        Guard.notNull(parentDto, "父级详细设计不存在");

        logger.info("recursiveGenerateRequirement的parentDto：{}", JsonUtils.toString(parentDto));
        if (parentDto.getExt() != null && parentDto.getExt().equals(true)) {
            //集成外包不产生物料需求
            return Collections.emptyList();
        }

        Long organizationId = projectService.getOrganizationIdByProjectId(parentDto.getProjectId());
        //查询该设计信息下所有未删除且审批通过子详细设计信息
        List<MilepostDesignPlanDetailDto> designPlanDetailSons =
                milepostDesignPlanDetailService.findNotGenerateRequireDesignPlanDetails(detailPlanParentId);
        logger.info("子级详设数量：{}，详设数据：{}", designPlanDetailSons.size(), JsonUtils.toString(designPlanDetailSons));
        for (MilepostDesignPlanDetailDto designPlanDetailSon : designPlanDetailSons) {
            Long sonId = designPlanDetailSon.getId();
            //递归
            List<PurchaseMaterialRequirementDto> childCreatedRequirements = recursiveGenerateRequirement(sonId, requirementIdList);
            // 将子调用返回的新创建 DTO 添加到当前列表
            if (childCreatedRequirements != null && !childCreatedRequirements.isEmpty()) {
                newlyCreatedRequirements.addAll(childCreatedRequirements);
            }

            //当下级详设未产生物料需求且为外购物料时才需要处理产生采购需求
            Boolean generateRequirement = designPlanDetailSon.getGenerateRequirement();
            if (!Objects.equals(DesignPlanDetailGenerateRequire.NOT_PRODUCED.code(), generateRequirement)
                    || !Objects.equals("外购物料", designPlanDetailSon.getMaterialCategory())) {
                continue;
            }

            logger.info("准备产生采购需求的子级详细设计id：{}", sonId);
            Guard.notNull(designPlanDetailSon.getProjectId(), String.format("子级详细设计id：%s的项目id不能为空", sonId));
            Guard.notNullOrEmpty(designPlanDetailSon.getPamCode(), String.format("子级详细设计id：%s的pam编码不能为空", sonId));
            Guard.notNull(designPlanDetailSon.getDeliveryTime(), String.format("子级详细设计id：%s的交货时间不能为空", sonId));

            //TODO:3082,代表机器人的物料,将来可考虑获取上下文的ouId
            MaterialDto materialDto =
                    materialExtService.getNotDeletedMaterialByPamCodeOrErpCode(designPlanDetailSon.getPamCode(), null, organizationId);
            // 新的生成采购需求的逻辑，item_type为P（外购物料）的物料才生成采购需求
            if (null == materialDto || !Objects.equals(materialDto.getItemType(), "P") || StringUtils.isEmpty(designPlanDetailSon.getErpCode())) {
                logger.info("不能产生采购需求的P码：{}，库存组织：{},查询到materialDto：{}，designPlanDetailSon：{}",
                        designPlanDetailSon.getPamCode(), organizationId, JsonUtils.toString(materialDto), JsonUtils.toString(designPlanDetailSon));
                continue;
            }

            logger.info("recursiveGenerateRequirement准备产生采购需求，详设id：{}，库存组织：{}，designPlanDetailSon：{}",
                    sonId, organizationId, JsonUtils.toString(designPlanDetailSon));

            //查询该项目某个交货时间对应的物料采购需求
            PurchaseMaterialRequirementDto exitDto = getByProjectIdAndPamCodeAndDeliveryTime(
                    designPlanDetailSon.getProjectId(),
                    designPlanDetailSon.getDeliveryTime(),
                    designPlanDetailSon.getPamCode());
            PurchaseMaterialRequirementDto dto = new PurchaseMaterialRequirementDto();
            PurchaseMaterialRequirementDto resultDto = null;
            BigDecimal purchaseNum = Optional.ofNullable(designPlanDetailSon.getNumber()).orElse(BigDecimal.ZERO)
                    .multiply(milepostDesignPlanDetailService.getParentSigmaById(designPlanDetailSon.getParentId(), null));
            if (exitDto == null) {
                //生成物料采购需求
                logger.info("recursiveGenerateRequirement生成物料采购需求的子级详细设计id：{}", sonId);
                dto.setProjectId(designPlanDetailSon.getProjectId());
                dto.setErpCode(designPlanDetailSon.getErpCode());
                dto.setPamCode(designPlanDetailSon.getPamCode());
                dto.setDeliveryTime(designPlanDetailSon.getDeliveryTime());
                dto.setMaterielId(materialDto.getId());
                dto.setMaterielDescr(materialDto.getItemInfo());

                String requirementRedisNumber = getRequirementRedisNumber(designPlanDetailSon.getProjectId(),
                        designPlanDetailSon.getDeliveryTime(), designPlanDetailSon.getPamCode(), null, purchaseNum);

                dto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                dto.setUnitCode(designPlanDetailSon.getUnitCode());
                dto.setUnit(designPlanDetailSon.getUnit());
                resultDto = this.add(dto);

                if (resultDto != null && resultDto.getId() != null) {
                    //将新创建的 DTO 添加到 newlyCreatedRequirements 列表
                    logger.info("新增采购需求成功，DTO: {}，添加到 newlyCreatedRequirements 列表。", JsonUtils.toString(resultDto));
                    newlyCreatedRequirements.add(resultDto);
                } else {
                    logger.error("新增采购需求调用 this.add 返回 null 或 ID 为 null。");
                }

            } else {
                dto.setId(exitDto.getId());
                //更新物料采购需求总需求量
                logger.info("recursiveGenerateRequirement更新物料采购需求总需求量的子级详细设计id：{}", sonId);

                if (BigDecimal.ZERO.compareTo(purchaseNum) != 0) {
                    String requirementRedisNumber = getRequirementRedisNumber(designPlanDetailSon.getProjectId(),
                            designPlanDetailSon.getDeliveryTime(), designPlanDetailSon.getPamCode(), exitDto.getNeedTotal(), purchaseNum);

                    dto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                    resultDto = this.update(dto);
                    //updateStatus(dto.getId());//根据总需求量更新采购需求的状态
                    requirementIdList.add(dto.getId());
                }
            }
            if (BigDecimal.ZERO.compareTo(purchaseNum) != 0) {
                //生成物料采购需求发布明细
                logger.info("recursiveGenerateRequirement生成物料采购需求发布明细的子级详细设计id：{}", sonId);
                addPurchaseMaterialReleaseDetail(materialDto.getId(),
                        resultDto == null ? null : resultDto.getId(),
                        designPlanDetailSon,
                        ReleaseDetailStatus.SUBMIT.code(),
                        designPlanDetailSon.getCreateBy(),
                        purchaseNum);
            }

            //更新设计信息的是否产生物料需求字段
            designPlanDetailSon.setGenerateRequirement(DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code());
            milepostDesignPlanDetailService.save(designPlanDetailSon, null);
        }
        logger.info("end-详细设计模组确认产生采购需求");
        return newlyCreatedRequirements;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importPurchaseMaterialRequirement(List<ImportPurchaseMaterialRequirementExcelVO> detailExcelVOList) {
        List<String> projectCodeList = new ArrayList<>();
        List<String> requirementCodeList = new ArrayList<>();
        Map<String, List<ImportPurchaseMaterialRequirementExcelVO>> excelVOMap = new HashMap<>();
        for (ImportPurchaseMaterialRequirementExcelVO excelVO : detailExcelVOList) {
            String requirementCode = excelVO.getRequirementCode();
            requirementCodeList.add(requirementCode);
            String projectCode = excelVO.getProjectCode();
            projectCodeList.add(projectCode);
            String requirementKey = getImportPurchaseMaterialRequirementKey(projectCode, requirementCode, excelVO.getWbsSummaryCode());
            if (excelVOMap.containsKey(requirementKey)) {
                excelVOMap.get(requirementKey).add(excelVO);
            } else {
                excelVOMap.put(requirementKey, Lists.newArrayList(excelVO));
            }
        }

        ProjectExample example = new ProjectExample();
        example.createCriteria().andCodeIn(projectCodeList);
        List<Project> projectList = projectService.selectByExample(example);
        Guard.notNullOrEmpty(projectList, "所有的项目号-新都不存在不存在");
        Map<String, Long> projectMap = new HashMap<>();
        List<Long> projectIdList = new ArrayList<>();
        for (Project project : projectList) {
            projectMap.put(project.getCode(), project.getId());
            projectIdList.add(project.getId());
        }

        ProjectWbsReceiptsExample receiptsExample = new ProjectWbsReceiptsExample();
        receiptsExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdIn(projectIdList).andRequirementCodeIn(requirementCodeList);
        List<ProjectWbsReceipts> projectWbsReceiptsList = projectWbsReceiptsMapper.selectByExampleWithBLOBs(receiptsExample);
        Map<String, ProjectWbsReceipts> projectWbsReceiptsMap = new HashMap<>();
        for (ProjectWbsReceipts projectWbsReceipts : projectWbsReceiptsList) {
            String projectWbsReceiptsKey = getProjectWbsReceiptsKey(projectWbsReceipts.getProjectId(), projectWbsReceipts.getRequirementCode());
            projectWbsReceiptsMap.put(projectWbsReceiptsKey, projectWbsReceipts);
        }

        List<ProjectWbsReceiptsBudget> parentBudgetList = new ArrayList<>();
        excelVOMap.forEach((k, v) -> {
            ImportPurchaseMaterialRequirementExcelVO parentExcelVO = v.get(0);
            Integer serialNumber = parentExcelVO.getSerialNumber();
            // 项目号-新
            String projectCode = parentExcelVO.getProjectCode();
            Long projectId = projectMap.get(projectCode);
            Guard.notNull(projectId, String.format("序号：%s的项目号-新：%s不存在", serialNumber, projectCode));
            // WBS号
            String wbsSummaryCode = parentExcelVO.getWbsSummaryCode();
            Guard.isTrue(wbsSummaryCode.startsWith(projectCode), String.format("序号：%s的WBS号：%s对应的项目号-新：%s不正确",
                    serialNumber, wbsSummaryCode, projectCode));
            // 需求发布单据编号
            String requirementCode = parentExcelVO.getRequirementCode();
            ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMap.get(getProjectWbsReceiptsKey(projectId, requirementCode));
            Guard.notNull(projectWbsReceipts, String.format("序号：%s的项目号-新：%s，需求发布单据编号：%s对应的详细设计单据不存在",
                    serialNumber, projectCode, requirementCode));

            ProjectWbsReceiptsBudget parentBudget = new ProjectWbsReceiptsBudget();
            parentBudget.setProjectWbsReceiptsId(projectWbsReceipts.getId());
            parentBudget.setBudgetOccupiedAmount(v.stream().map(ImportPurchaseMaterialRequirementExcelVO::getBudgetOccupiedAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            parentBudget.setWbsSummaryCode(wbsSummaryCode);
            parentBudget.setDeletedFlag(false);
            parentBudget.setVersion(1L);
            parentBudget.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode());
            parentBudget.setInit(true);
            parentBudgetList.add(parentBudget);
        });
        if (CollectionUtils.isNotEmpty(parentBudgetList)) {
            projectWbsReceiptsBudgetExtMapper.batchInsert(parentBudgetList);
        }
        // 最上层的父级 projectWbsReceiptsId + wbsSummaryCode 唯一
        Map<String, Long> parentBudgetMap = new HashMap<>();
        for (ProjectWbsReceiptsBudget budget : parentBudgetList) {
            parentBudgetMap.put(getParentBudgetKey(budget.getProjectWbsReceiptsId(), budget.getWbsSummaryCode()), budget.getId());
        }

        List<ProjectWbsReceiptsBudget> sonBudgetList = new ArrayList<>();
        excelVOMap.forEach((k, v) -> {
            for (ImportPurchaseMaterialRequirementExcelVO excelVO : v) {
                Integer serialNumber = excelVO.getSerialNumber();
                // 项目号-新
                String projectCode = excelVO.getProjectCode();
                Long projectId = projectMap.get(projectCode);
                Guard.notNull(projectId, String.format("序号：%s的项目号-新：%s不存在", serialNumber, projectCode));
                // WBS号
                String wbsSummaryCode = excelVO.getWbsSummaryCode();
                Guard.isTrue(wbsSummaryCode.startsWith(projectCode), String.format("序号：%s的WBS号：%s对应的项目号-新：%s不正确",
                        serialNumber, wbsSummaryCode, projectCode));
                // 需求发布单据编号
                String requirementCode = excelVO.getRequirementCode();
                ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMap.get(getProjectWbsReceiptsKey(projectId, requirementCode));
                Guard.notNull(projectWbsReceipts, String.format("序号：%s的项目号-新：%s，需求发布单据编号：%s对应的详细设计单据不存在",
                        serialNumber, projectCode, requirementCode));

                ProjectWbsReceiptsBudget sonBudget = new ProjectWbsReceiptsBudget();
                sonBudget.setProjectWbsReceiptsId(projectWbsReceipts.getId());
                sonBudget.setBudgetOccupiedAmount(excelVO.getBudgetOccupiedAmount());
                sonBudget.setWbsSummaryCode(wbsSummaryCode);
                sonBudget.setParentid(parentBudgetMap.get(getParentBudgetKey(projectWbsReceipts.getId(), wbsSummaryCode)));
                sonBudget.setDeletedFlag(false);
                sonBudget.setVersion(1L);
                if (excelVO.getExtIs()) {
                    sonBudget.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode());
                } else {
                    sonBudget.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode());

                }
                sonBudget.setInit(true);
                sonBudgetList.add(sonBudget);
            }
        });
        if (CollectionUtils.isNotEmpty(sonBudgetList)) {
            projectWbsReceiptsBudgetExtMapper.batchInsert(sonBudgetList);
        }
    }

    @Override
    public List<PurchaseMaterialRequirementDto> getOutRequirementInfo(Long projectId, Long headerId) {
        List<Long> requirementIds = new ArrayList<>();
        if (headerId != null) {
            PurchaseContractBudgetChangeHistoryExample historyExample = new PurchaseContractBudgetChangeHistoryExample();
            historyExample.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
            final List<PurchaseContractBudgetChangeHistory> changeHistories =
                    purchaseContractBudgetChangeHistoryMapper.selectByExample(historyExample);
            requirementIds = changeHistories.stream().map(PurchaseContractBudgetChangeHistory::getPurchaseRequirementId).collect(Collectors.toList());
            // 如果指定变更记录的采购需求为空，则直接返回空
            if (ListUtils.isEmpty(requirementIds)) {
                return new ArrayList<>();
            }
        }
        PurchaseMaterialRequirementExample requirementExample = new PurchaseMaterialRequirementExample();
        PurchaseMaterialRequirementExample.Criteria criteria = requirementExample.createCriteria();
        criteria.andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        // 兼容不传headerId的查询
        if (ListUtils.isNotEmpty(requirementIds)) {
            criteria.andIdIn(requirementIds);
        }
        List<PurchaseMaterialRequirement> requirementList = purchaseMaterialRequirementMapper.selectByExample(requirementExample);
        if (CollectionUtils.isEmpty(requirementList)) {
            return new ArrayList<>();
        }
        requirementIds = requirementList.stream().map(PurchaseMaterialRequirement::getId).collect(Collectors.toList());
        Map<Long, PurchaseMaterialRequirementDto> budgetMap = purchaseMaterialRequirementExtMapper.selectOutRequirementInfo(requirementIds)
                .stream().collect(Collectors.toMap(PurchaseMaterialRequirementDto::getRequirementId, e -> e));
        return requirementList.stream().map(e -> {
            PurchaseMaterialRequirementDto requirementDto = new PurchaseMaterialRequirementDto();
            requirementDto.setId(e.getId());
            requirementDto.setNeedTotal(e.getNeedTotal());
            requirementDto.setWbsDemandOsCost(e.getWbsDemandOsCost());
            PurchaseMaterialRequirementDto budget = budgetMap.get(e.getId());
            if (budget != null) {
                requirementDto.setReleasedQuantity(budget.getReleasedQuantity());
                requirementDto.setContractTotalAmount(budget.getContractTotalAmount());
            } else {
                requirementDto.setReleasedQuantity(BigDecimal.ZERO);
                requirementDto.setContractTotalAmount(BigDecimal.ZERO);
            }
            if (requirementDto.getNeedTotal() != null) {
                requirementDto.setUnreleasedAmount(requirementDto.getNeedTotal().subtract(requirementDto.getReleasedQuantity()));
            }
            if (requirementDto.getWbsDemandOsCost() != null) {
                requirementDto.setWbsRemainingDemandOsCost(requirementDto.getWbsDemandOsCost().subtract(requirementDto.getContractTotalAmount()));
            }
            return requirementDto;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importPurchaseMaterialRequirementAndCloseDetail(List<ImportPurchaseMaterialRequirementAndCloseDetailExcelVO> detailExcelVOList) {
        if (CollectionUtils.isEmpty(detailExcelVOList)) {
            return;
        }

        logger.info("importPurchaseMaterialRequirementAndCloseDetail导入的数量为：{}", detailExcelVOList.size());

        List<PurchaseMaterialRequirementDto> dtoList = purchaseMaterialRequirementExtMapper.selectApprovedSupplierNumberFromMaterialDetail();
        Map<String, Integer> requirementDtoMap = dtoList.stream().collect(Collectors.toMap(e ->
                        String.format("requirementAndCloseDetail_%s_%s", e.getErpCode(), e.getOrganizationCode()),
                PurchaseMaterialRequirementDto::getApprovedSupplierNumber));
        // 获取计量单位
        List<DictDto> unitDicts = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Map<String, String> unitCodeNameMap = new HashMap<>();
        for (DictDto dictDto : unitDicts) {
            unitCodeNameMap.put(dictDto.getCode(), dictDto.getName());
        }

        Long createBy = Long.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));
        Date createAt = new Date();
        int threadNum = detailExcelVOList.size() / 3000 + (detailExcelVOList.size() % 3000 == 0 ? 0 : 1);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * 3000;
            int endIndex = i == threadNum ? detailExcelVOList.size() : i * 3000;
            List<ImportPurchaseMaterialRequirementAndCloseDetailExcelVO> eachTreadExcelVos = detailExcelVOList.subList(startIndex, endIndex);

            List<PurchaseMaterialRequirementDto> requirementDtoList = new ArrayList<>();
            for (ImportPurchaseMaterialRequirementAndCloseDetailExcelVO excelVO : eachTreadExcelVos) {
                Integer approvedSupplierNumber = Optional.ofNullable(requirementDtoMap.get(String.format("requirementAndCloseDetail_%s_%s",
                        excelVO.getErpCode(), excelVO.getOrganizationCode()))).orElse(0);
                PurchaseMaterialRequirementDto requirementDto = BeanConverter.copy(excelVO, PurchaseMaterialRequirementDto.class);
                requirementDto.setApprovedSupplierNumber(approvedSupplierNumber);
                requirementDto.setUnit(unitCodeNameMap.get(requirementDto.getUnitCode()));
                requirementDto.setReason("MRP需求切换初始化");
                requirementDto.setDeletedFlag(Boolean.FALSE);
                requirementDto.setCreateBy(createBy);
                requirementDto.setCreateAt(createAt);
                requirementDto.setClosedQuantity(requirementDto.getCloseNum());
                requirementDto.setInit(Boolean.TRUE);

                requirementDtoList.add(requirementDto);
            }
            purchaseMaterialRequirementExtMapper.batchInsert1(requirementDtoList);

            List<PurchaseMaterialCloseDetail> closeDetailList = new ArrayList<>();
            for (PurchaseMaterialRequirementDto requirementDto : requirementDtoList) {
                PurchaseMaterialCloseDetail closeDetail = new PurchaseMaterialCloseDetail();
                closeDetail.setPurchaseRequirementId(requirementDto.getId());
                closeDetail.setProjectId(requirementDto.getProjectId());
                closeDetail.setMaterialId(requirementDto.getMaterielId());
                closeDetail.setCloseTime(createAt);
                closeDetail.setCloserId(773380894177099776L);
                closeDetail.setCloserName("温达浩");
                closeDetail.setCloseType(RequirementCloseTypeEnum.CLOSE.code());
                closeDetail.setCloseNum(requirementDto.getCloseNum());
                closeDetail.setCloseReason("MRP需求切换初始化");
                closeDetail.setMaterialPamCode(requirementDto.getPamCode());
                closeDetail.setDeletedFlag(Boolean.FALSE);
                closeDetail.setCreateBy(createBy);
                closeDetail.setCreateAt(createAt);

                closeDetailList.add(closeDetail);
            }
            purchaseMaterialCloseDetailExtMapper.batchInsert(closeDetailList);

            logger.info("importPurchaseMaterialRequirementAndCloseDetail第[{}]次执行成功，执行了[{}]条数据", i, requirementDtoList.size());
        }

    }

    @Override
    public List<PurchaseMaterialRequirementDto> getChangeOrderDetailByRequirementId(String requirementIdStr, Long buyerBy) {
        Asserts.notEmpty(requirementIdStr, ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_ID_NOT_NULL);
        Asserts.notEmpty(buyerBy, ErrorCode.CTC_BUYER_INFO_NOT_NULL);

        List<Long> requirementIdList = Arrays.stream(requirementIdStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(requirementIdList)) return new ArrayList<>();

        //排除批准供应商数量为0的数据
        List<PurchaseMaterialRequirementDto> requirementList = this.getDetailByIds(requirementIdList).stream().collect(Collectors.toList());
        packagePurchaseMaterialRequirement(requirementList);
        Map<Long, PurchaseMaterialRequirementDto> requirementMap =
                requirementList.stream().collect(Collectors.toMap(PurchaseMaterialRequirementDto::getId, Function.identity(), (a, b) -> a));
        List<PurchaseMaterialRequirementDto> requirementDtoList = new ArrayList<>();

        for (Long requirementId : requirementIdList) {
            PurchaseMaterialRequirementDto dto = requirementMap.get(requirementId);
            Asserts.notEmpty(dto, ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_NOT_FIND);
            Asserts.notEmpty(dto.getProjectId(), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_PROJECT_ID_NULL);
            Asserts.notEmpty(dto.getPamCode(), ErrorCode.CTC_PURCHASE_MATERIAL_REQUIREMENT_PAM_CODE_NULL);
            if (dto.getProjectOuId() != null) {
                dto.setProjectOuName(CacheDataUtils.findOuNameById(Long.valueOf(dto.getProjectOuId())));
            }
            //构建订单行
            PurchaseOrderDetailDto purchaseOrderDetailDto = new PurchaseOrderDetailDto();
            purchaseOrderDetailDto.setUnitCode(dto.getUnitCode());
            purchaseOrderDetailDto.setUnit(dto.getUnit());
            dto.setPurchaseOrderDetailDtos(Arrays.asList(purchaseOrderDetailDto));

            requirementDtoList.add(dto);
        }
        return requirementDtoList;
    }

    public void packagePurchaseMaterialRequirement(List<PurchaseMaterialRequirementDto> requirementList) {
        PurchaseMaterialRequirementDto dto = new PurchaseMaterialRequirementDto();
        dto.setRequirementIdList(requirementList.stream().map(PurchaseMaterialRequirementDto::getId).collect(Collectors.toList()));
        dto.setErpCodeList(requirementList.stream().map(PurchaseMaterialRequirementDto::getErpCode).collect(Collectors.toList()));
        final String url = String.format("%sstatistics/purchaseMaterialRequirement/statisticsProjectHavedQuantity",
                ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<List<PurchaseMaterialRequirementDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<PurchaseMaterialRequirementDto>>>() {
                });
        Map<Long, PurchaseMaterialRequirementDto> dataMap =
                response.getData().stream().collect(Collectors.toMap(PurchaseMaterialRequirementDto::getId, Function.identity()));
        for (PurchaseMaterialRequirementDto requirementDto : requirementList) {
            PurchaseMaterialRequirementDto item = dataMap.get(requirementDto.getId());
            if (item != null) {
                requirementDto.setProjectHavedQuantity(item.getProjectHavedQuantity());     //项目现有量
                requirementDto.setNoProjectHavedQuantity(item.getNoProjectHavedQuantity()); //非项目现有量
                BigDecimal systemQuantity =
                        BigDecimalUtils.subtract(BigDecimalUtils.subtract(BigDecimalUtils.subtract(requirementDto.getNeedTotal(),
                                requirementDto.getReleasedQuantity()), item.getProjectHavedQuantity()), requirementDto.getClosedQuantity());
                requirementDto.setSystemQuantity(systemQuantity.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : systemQuantity);  //建议下单量
            }
        }
    }

    private String getImportPurchaseMaterialRequirementKey(String projectCode, String requirementCode, String wbsSummaryCode) {
        return String.format("getImportPurchaseMaterialRequirementKey_%s_%s_%s", projectCode, requirementCode, wbsSummaryCode);
    }

    private String getProjectWbsReceiptsKey(Long projectId, String requirementCode) {
        return String.format("getProjectWbsReceiptsKey_%s_%s", projectId, requirementCode);
    }

    private String getParentBudgetKey(Long projectWbsReceiptsId, String wbsSummaryCode) {
        return String.format("getParentBudgetKey_%s_%s", projectWbsReceiptsId, wbsSummaryCode);
    }

    @Override
    public String checkRequirementDeliveryAddress(RequirementDeliverAddressRepeatCheckDTO repeatCheckDTO) {
        // 获取收货地址相关信息
        String deliveryAddress = repeatCheckDTO.getDeliveryAddress();
        String consignee = repeatCheckDTO.getConsignee();
        String contactPhone = repeatCheckDTO.getContactPhone();

        // 获取详细计划信息列表，并提取项目ID、物料编码和交货时间
        List<RequirementDeliverAddressRepeatCheckDTO.DetailPlanInfo> detailPlanInfos = repeatCheckDTO.getDetailPlanInfos();
        List<Long> projectIds = detailPlanInfos.stream().map(RequirementDeliverAddressRepeatCheckDTO.DetailPlanInfo::getProjectId).collect(Collectors.toList());
        List<String> pamCodes = detailPlanInfos.stream().map(RequirementDeliverAddressRepeatCheckDTO.DetailPlanInfo::getPamCode).collect(Collectors.toList());
        List<String> deliveryTimeStrList = detailPlanInfos.stream().map(d -> DateUtil.format(d.getDeliveryTime(), DateUtil.TIMESTAMP_PATTERN)).collect(Collectors.toList());
        List<PurchaseMaterialRequirement> purchaseMaterialRequirements = new ArrayList<>();

        // 根据检查类型进行不同的处理逻辑
        if (String.valueOf(RequirementDeliveryAddressCheckTypeEnum.DETAIL_PROGRESS_CONFIRM.getCode()).equals(repeatCheckDTO.getCheckType())) {
            // 详细进度确认模式：需要考虑子级详细设计
            Long projectId = projectIds.get(0);
            for (String pamCode : pamCodes) {
                // 根据项目ID和物料编码查询详细设计信息
                MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
                milepostDesignPlanDetailExample.createCriteria()
                        .andDeletedFlagEqualTo(false)
                        .andProjectIdEqualTo(projectId)
                        .andPamCodeEqualTo(pamCode);
                List<MilepostDesignPlanDetail> milepostDesignPlanDetailDtos = milepostDesignPlanDetailMapper.selectByExample(milepostDesignPlanDetailExample);

                // 遍历每个详细设计，递归获取其所有子级信息
                for (MilepostDesignPlanDetail milepostDesignPlanDetailDto : milepostDesignPlanDetailDtos) {
                    // 获取所有包含自己和子级详设
                    List<MilepostDesignPlanDetailDto> designPlanDetailDtoList = milepostDesignPlanDetailService.getDesignPlanDetail(milepostDesignPlanDetailDto.getId());
                    if (ListUtils.isNotEmpty(designPlanDetailDtoList)) {

                        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtoList) {
                            // 递归获取当前节点及其所有子节点
                            List<MilepostDesignPlanDetailDto> allDetailDtosIncludeSelf = getAllDetailDtosIncludeSelf(designPlanDetailDto);
                            //排除已确认的详细设计
                            allDetailDtosIncludeSelf.removeIf(m -> Objects.equals(m.getGenerateRequirement(), Boolean.TRUE));
                            // 提取所有相关的项目ID（去重）
                            List<Long> childProjectIds = allDetailDtosIncludeSelf.stream()
                                    .map(MilepostDesignPlanDetailDto::getProjectId)
                                    .distinct()
                                    .collect(Collectors.toList());
                            // 提取所有相关的物料编码
                            Stream<String> childPamCodes = allDetailDtosIncludeSelf.stream()
                                    .map(MilepostDesignPlanDetailDto::getPamCode);
                            // 提取所有相关的交货时间
                            Stream<String> childDeliveryTimeStrList = allDetailDtosIncludeSelf.stream()
                                    .map(d -> DateUtil.format(d.getDeliveryTime(), DateUtil.TIMESTAMP_PATTERN));

                            // 根据提取的条件查询采购需求信息并添加到结果集
                            purchaseMaterialRequirements.addAll(
                                    purchaseMaterialRequirementExtMapper.listAddressesByDeliveryCondition(
                                            childProjectIds,
                                            childPamCodes.collect(Collectors.toList()),
                                            childDeliveryTimeStrList.collect(Collectors.toList())
                                    )
                            );
                        }
                    }
                }
            }
        } else {
            // 普通模式：直接根据传入的条件查询采购需求，不考虑子级关系
            purchaseMaterialRequirements.addAll(
                    purchaseMaterialRequirementExtMapper.listAddressesByDeliveryCondition(
                            projectIds,
                            pamCodes,
                            deliveryTimeStrList
                    )
            );
        }

        if (ListUtils.isEmpty(purchaseMaterialRequirements)) return "";

        // 构建提示信息
        StringBuilder tipBuilder = new StringBuilder();

        // 检查每个采购需求的收货信息是否有变化
        for (PurchaseMaterialRequirement requirement : purchaseMaterialRequirements) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(requirement.getDeliveryAddress())
                    && org.apache.commons.lang3.StringUtils.isNotBlank(requirement.getConsignee())
                    && org.apache.commons.lang3.StringUtils.isNotBlank(requirement.getContactPhone())) {
                // 比较新旧地址信息是否一致
                boolean addressMismatch = !Objects.equals(deliveryAddress, requirement.getDeliveryAddress());
                boolean consigneeMismatch = !Objects.equals(consignee, requirement.getConsignee());
                boolean phoneMismatch = !Objects.equals(contactPhone, requirement.getContactPhone());

                // 如果有任何信息不一致，添加到提示信息中
                if (addressMismatch || consigneeMismatch || phoneMismatch) {
                    tipBuilder.append(String.format("物料：%s，计划交货日期为：%s的需求，当前收货地址：%s，收货人：%s，联系方式：%s;",
                            requirement.getPamCode(),
                            DateUtil.format(requirement.getDeliveryTime(), "yyyy-MM-dd"),
                            requirement.getDeliveryAddress(),
                            requirement.getConsignee(),
                            requirement.getContactPhone()));
                    //加入换行符
                    tipBuilder.append(System.lineSeparator());
                }
            }
        }

        // 如果有不一致的信息，添加更新提示
        if (tipBuilder.length() > 0) {
            tipBuilder.append(String.format("是否更新为：收货地址：%s，收货人：%s，联系方式：%s", deliveryAddress, consignee, contactPhone));
        }

        return tipBuilder.toString();
    }


    private List<MilepostDesignPlanDetailDto> getAllDetailDtosIncludeSelf(MilepostDesignPlanDetailDto detailDto) {
        List<MilepostDesignPlanDetailDto> result = new ArrayList<>();
        if (detailDto == null) {
            return result;
        }

        // 添加自己
        result.add(detailDto);

        // 递归获取所有子节点
        List<MilepostDesignPlanDetailDto> sonDtos = detailDto.getSonDtos();
        if (ListUtil.isPresent(sonDtos)) {
            for (MilepostDesignPlanDetailDto sonDto : sonDtos) {
                result.addAll(getAllDetailDtosIncludeSelf(sonDto));
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchEditRequirementDeliveryAddress(BatchEditRequirementDeliveryAddressDto dto) {
        String deliveryAddress = dto.getDeliveryAddress();
        String consignee = dto.getConsignee();
        String contactPhone = dto.getContactPhone();
        List<Long> requirementIds = dto.getRequirementIds();
        Boolean handleResult = false;
        PurchaseMaterialRequirementExample example = new PurchaseMaterialRequirementExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(requirementIds);
        List<PurchaseMaterialRequirement> purchaseMaterialRequirements = purchaseMaterialRequirementMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(purchaseMaterialRequirements)) {
            try {
                for (PurchaseMaterialRequirement purchaseMaterialRequirement : purchaseMaterialRequirements) {
                    requirementDeliveryAddressHistoryService.saveDeliveryAddressHistory(deliveryAddress
                            , consignee
                            , contactPhone
                            , purchaseMaterialRequirement.getId()
                            , -1L
                            , 0
                            , PurchaseMaterialRequirementDeliveryAddressHistoryEditTypeEnum.MANUAL_CHANGE.getCode());
                }
                handleResult = Boolean.TRUE;
            } catch (Exception e) {
                logger.error("批量修改采购需求收货地址异常：", e);

            }
        }
        return handleResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String materialRequirementFreeze(PurchaseMaterialRequirementDto dto) {
        List<String> stringIds = dto.getIds();
        List<Long> ids = stringIds.stream().map(Long::parseLong).collect(Collectors.toList());
        List<String> errorMessages = new ArrayList<>();

        for (Long id : ids) {
            if (errorMessages.size() >= 10) {
                break;
            }
            PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(id);
            if (requirement == null) continue;
            if (!requirement.getStatus().equals(0)) {
                errorMessages.add(String.format("[%s]的[%s]需求状态不是待下达或者冻结状态不是未冻结，不能冻结", requirement.getRequirementCode(), requirement.getPamCode()));
                continue;
            }

            if (requirement.getFreezeFlag().equals(FreezeFlag.FREEZE.getCode())) {
                errorMessages.add(String.format("[%s]的[%s]需求状态不是待下达或者冻结状态不是未冻结，不能冻结", requirement.getRequirementCode(), requirement.getPamCode()));
                continue;
            }

            Date now = new Date();
            requirement.setFreezeFlag(FreezeFlag.FREEZE.getCode());
            requirement.setFreezeAt(now);
            requirement.setFreezeReason(dto.getFreezeReason());
            requirement.setFreezeByName(CacheDataUtils.findUserById(SystemContext.getUserId()).getName());
            purchaseMaterialRequirementMapper.updateByPrimaryKey(requirement);
            PurchaseMaterialRequirementFreezeLog log = new PurchaseMaterialRequirementFreezeLog();
            log.setPurchaseMaterialRequirementId(id);
            log.setOperationType(FreezeOperationTypeEnum.FREEZE.getCode());
            log.setFreezeReason(dto.getFreezeReason());
            log.setCreateAt(now);
            log.setDeletedFlag(false);
            purchaseMaterialRequirementFreezeLogMapper.insert(log);
        }

        if (!errorMessages.isEmpty()) {
            throw new BizException(Code.ERROR, String.join(";", errorMessages));
        }
        return "操作成功，共处理" + ids.size() + "条数据";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String materialRequirementThaw(PurchaseMaterialRequirementDto dto) {
        List<String> stringIds = dto.getIds();
        List<Long> ids = stringIds.stream().map(Long::parseLong).collect(Collectors.toList());
        List<String> errorMessages = new ArrayList<>();

        for (Long id : ids) {
            if (errorMessages.size() >= 10) {
                break;
            }
            PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(id);
            if (requirement == null) continue;
            // 校验：是否已解冻
            if (requirement.getFreezeFlag().equals(FreezeFlag.NOT_FREEZE.getCode())) {
                errorMessages.add(String.format("[%s]的[%s]未冻结，无需解冻",
                        requirement.getRequirementCode(), requirement.getPamCode()));
                continue;
            }

            Date now = new Date();
            requirement.setFreezeFlag(FreezeFlag.NOT_FREEZE.getCode());
            requirement.setFreezeAt(null);
            requirement.setFreezeReason("");
            requirement.setFreezeByName("");
            purchaseMaterialRequirementMapper.updateByPrimaryKey(requirement);
            PurchaseMaterialRequirementFreezeLog log = new PurchaseMaterialRequirementFreezeLog();
            log.setPurchaseMaterialRequirementId(id);
            log.setOperationType(FreezeOperationTypeEnum.THAW.getCode());
            log.setCreateAt(now);
            log.setDeletedFlag(false);
            purchaseMaterialRequirementFreezeLogMapper.insert(log);

        }
        // 统一处理错误
        if (!errorMessages.isEmpty()) {
            throw new BizException(Code.ERROR, String.join(";", errorMessages));
        }
        return "操作成功，共处理" + ids.size() + "条数据";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String wbsTypeUpdate(PurchaseMaterialRequirementDto dto) {
        List<String> stringIds = dto.getIds();
        List<Long> ids = stringIds.stream().map(Long::parseLong).collect(Collectors.toList());
        List<String> errorMessages = new ArrayList<>();

        for (Long id : ids) {
            if (errorMessages.size() > 10) {
                break;
            }
            PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(id);
            if (requirement == null) continue;

            //校验1：状态是否是待下单
            if (!requirement.getStatus().equals(0)) {
                errorMessages.add(String.format("[%s]的[%s]需求状态不是待下达", requirement.getRequirementCode(), requirement.getPamCode()));
                continue;
            }
            //校验2：所选数据已下达量=0
//            BigDecimal releasedQuantity = purchaseMaterialRequirementExtMapper.selectWbsReleasedQuantity(id);
            BigDecimal releasedQuantity = Optional.ofNullable(purchaseMaterialRequirementExtMapper.selectWbsReleasedQuantity(id))
                    .orElse(BigDecimal.ZERO);
            if (releasedQuantity.compareTo(BigDecimal.ZERO) != 0) {
                errorMessages.add(String.format("[%s]的[%s]已下达量不等于0", requirement.getRequirementCode(), requirement.getPamCode()));
                continue;
            }

            PurchaseMaterialRequirementPurchaseTypeChangeLog log = new PurchaseMaterialRequirementPurchaseTypeChangeLog();
            log.setPurchaseMaterialRequirementId(id);
            log.setOperationType(PurchaseTypeOperationTypeEnum.WBS.getCode());
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                String requirementJson = objectMapper.writeValueAsString(requirement);
                log.setPurchaseMaterialRequirementChangeBeforeInfo(requirementJson);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }

            requirement.setPurchaseType(dto.getPurchaseType());
            String requirementTypeExport = dto.getRequirementTypeExport();
            List<DictDto> requirementTypeDicts = basedataExtService.getLtcDict(DictType.DESIGN_REQUIREMENT_TYPE.code(), null, null);
            Map<String, String> requirementTypeMap = requirementTypeDicts.stream().collect(Collectors.toMap(DictDto::getName, DictDto::getCode));
            String requirementTypeString = requirementTypeMap.get(requirementTypeExport);
            requirement.setRequirementType(Integer.valueOf(requirementTypeString));
            purchaseMaterialRequirementMapper.updateByPrimaryKey(requirement);

            try {
                String requirementJson = objectMapper.writeValueAsString(requirement);
                log.setPurchaseMaterialRequirementChangeAfterInfo(requirementJson);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
            log.setDeletedFlag(false);
            purchaseMaterialRequirementPurchaseTypeChangeLogMapper.insert(log);

        }
        // 统一处理错误
        if (!errorMessages.isEmpty()) {
            throw new BizException(Code.ERROR, String.join(";", errorMessages));
        }

        return "操作成功，共处理" + ids.size() + "条数据";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String requirementTypeUpdate(PurchaseMaterialRequirementDto dto) {
        List<String> stringIds = dto.getIds();
        List<Long> ids = stringIds.stream().map(Long::parseLong).collect(Collectors.toList());
        List<String> errorMessages = new ArrayList<>();

        for (Long id : ids) {
            if (errorMessages.size() > 10) {
                break;
            }
            PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(id);
            if (requirement == null) continue;

            //校验1：状态是否是待下单
            if (!requirement.getStatus().equals(0)) {
                errorMessages.add(String.format("[%s]需求状态不为待下达", requirement.getPamCode()));
                continue;
            }

            //校验2：所选数据已采购量=0
            BigDecimal releasedQuantity = Optional.ofNullable(purchaseMaterialRequirementExtMapper.selectReleasedQuantity(id))
                    .orElse(BigDecimal.ZERO);
            if (releasedQuantity.compareTo(BigDecimal.ZERO) != 0) {
                errorMessages.add(String.format("[%s]已采购量不等于0", requirement.getPamCode()));
                continue;
            }
            //校验3：如果外包（整包）需求->物料采购需求（WBS）,ERP编码不能为空
            if (3 == dto.getPurchaseType()) {
                if (org.apache.commons.lang3.StringUtils.isBlank(requirement.getErpCode())) {
                    errorMessages.add(String.format("[%s]存在ERP编码为空，不允许修改为[%s]需求类型", requirement.getPamCode(), dto.getRequirementTypeExport()));
                    continue;
                }
            }

            PurchaseMaterialRequirementPurchaseTypeChangeLog log = new PurchaseMaterialRequirementPurchaseTypeChangeLog();
            log.setPurchaseMaterialRequirementId(id);
            log.setOperationType(PurchaseTypeOperationTypeEnum.OUTSOURCE.getCode());
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                String requirementJson = objectMapper.writeValueAsString(requirement);
                log.setPurchaseMaterialRequirementChangeBeforeInfo(requirementJson);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }

            requirement.setPurchaseType(dto.getPurchaseType());
            String requirementTypeExport = dto.getRequirementTypeExport();
            List<DictDto> requirementTypeDicts = basedataExtService.getLtcDict(DictType.DESIGN_REQUIREMENT_TYPE.code(), null, null);
            Map<String, String> requirementTypeMap = requirementTypeDicts.stream().collect(Collectors.toMap(DictDto::getName, DictDto::getCode));
            String requirementTypeString = requirementTypeMap.get(requirementTypeExport);
            requirement.setRequirementType(Integer.valueOf(requirementTypeString));
            purchaseMaterialRequirementMapper.updateByPrimaryKey(requirement);

            try {
                String requirementJson = objectMapper.writeValueAsString(requirement);
                log.setPurchaseMaterialRequirementChangeAfterInfo(requirementJson);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
            log.setDeletedFlag(false);
            purchaseMaterialRequirementPurchaseTypeChangeLogMapper.insert(log);

        }

        // 统一处理错误
        if (!errorMessages.isEmpty()) {
            throw new BizException(Code.ERROR, String.join(";", errorMessages));
        }

        return "操作成功，共处理" + ids.size() + "条数据";
    }

    /**
     * 校验采购订单是否已同步到FAP系统
     * <p>
     * 已同步到FAP系统的订单不允许进行变更操作，此方法用于在订单变更前进行校验。
     * 如果发现已同步的订单，会记录详细的订单信息并抛出业务异常。
     * </p>
     *
     * @param purchaseOrderList 待校验的采购订单列表
     * @throws BizException 当发现已同步FAP的订单时抛出异常
     */
    private void validateOrdersNotSyncedToFap(List<PurchaseOrder> purchaseOrderList) {
        // 筛选出已同步到FAP系统的订单
        List<PurchaseOrder> syncedToFapOrders = purchaseOrderList.stream()
                .filter(s -> Objects.equals(s.getSyncToFap(), Boolean.TRUE))
                .collect(Collectors.toList());

        if (!syncedToFapOrders.isEmpty()) {
            // 记录已同步到FAP系统的订单信息
            List<String> syncedOrderInfo = syncedToFapOrders.stream()
                    .map(order -> String.format("订单ID:%s,订单号:%s", order.getId(), order.getNum()))
                    .collect(Collectors.toList());

            logger.warn("采购订单变更校验-发现已同步FAP系统的订单不允许变更, 订单详情:[{}]",
                    String.join("; ", syncedOrderInfo));

            throw new BizException(Code.ERROR, "此订单为关联订单，不支持做采购订单添加行的变更");
        }
    }
}
