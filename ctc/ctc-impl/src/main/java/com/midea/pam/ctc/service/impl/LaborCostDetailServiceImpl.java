package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.DiffCompanyLaborCostSetDTO;
import com.midea.pam.common.basedata.dto.OrgLaborCostTypeSetDTO;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.VendorMip;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.dto.LaborCostDetailDto;
import com.midea.pam.common.ctc.dto.LaborCostHardWorkingSyncDto;
import com.midea.pam.common.ctc.dto.WorkingHourResultDto;
import com.midea.pam.common.ctc.entity.CnbEmpBasicData;
import com.midea.pam.common.ctc.entity.CnbEmpBasicDataExample;
import com.midea.pam.common.ctc.entity.LaborCostDetail;
import com.midea.pam.common.ctc.entity.LaborCostDetailExample;
import com.midea.pam.common.ctc.entity.ProjectMember;
import com.midea.pam.common.ctc.entity.ProjectMemberExample;
import com.midea.pam.common.ctc.entity.ProjectRole;
import com.midea.pam.common.ctc.entity.WorkingHour;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.UserType;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.CnbEmpBasicDataExtMapper;
import com.midea.pam.ctc.mapper.CnbEmpBasicDataMapper;
import com.midea.pam.ctc.mapper.LaborCostDetailMapper;
import com.midea.pam.ctc.mapper.ProjectMemberMapper;
import com.midea.pam.ctc.mapper.ProjectRoleMapper;
import com.midea.pam.ctc.mapper.WorkingHourExtMapper;
import com.midea.pam.ctc.mapper.WorkingHourMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.LaborCostDetailService;
import com.midea.pam.ctc.service.WorkingHourService;
import com.midea.pam.ctc.service.helper.LaborCostRealMonthHelper;
import com.midea.pam.ctc.service.helper.OrgLaborCostTypeSetHelper;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 人工成本明细业务实现类
 *
 * <AUTHOR>
 * @since 2019-06-10 10:27
 */
public class LaborCostDetailServiceImpl implements LaborCostDetailService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private WorkingHourMapper workingHourMapper;
    @Resource
    private WorkingHourExtMapper workingHourExtMapper;
    @Resource
    private WorkingHourService workingHourService;
    @Resource
    private ProjectMemberMapper projectMemberMapper;
    @Resource
    private CnbEmpBasicDataExtMapper cnbEmpBasicDataExtMapper;
    @Resource
    private ProjectRoleMapper projectRoleMapper;
    @Resource
    private LaborCostDetailMapper laborCostDetailMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OrgLaborCostTypeSetHelper orgLaborCostTypeSetHelper;
    @Resource
    private LaborCostRealMonthHelper laborCostRealMonthHelper;
    @Resource
    private CnbEmpBasicDataMapper cnbEmpBasicDataMapper;


    @Override
    public List<LaborCostDetail> collection(CostCollectionDto collectionDto, Date collectionDate, Map<Long, Map<String, BigDecimal>> unitMap) {
        List<LaborCostDetail> laborCostDetails = new ArrayList<>();
        Long projectId = collectionDto.getProjectId();
        // 根据项目ID获取工时
        // 实际人力费率为空的重新归集
        List<WorkingHourResultDto> workingHours = getToConfirmWorkingHours(collectionDate, projectId);
        // 查询供应商MIP
        String vendorCodeStr = "";
        if (!CollectionUtils.isEmpty(workingHours)) {
            for (WorkingHourResultDto workingHour : workingHours) {
                if (StringUtils.isNotEmpty(workingHour.getVendorCode())) {
                    vendorCodeStr += workingHour.getVendorCode() + ",";
                }
            }
        }
        List<VendorMip> vendorMipList = new ArrayList<>();
        if (StringUtils.isNotEmpty(vendorCodeStr)) {
            vendorCodeStr = vendorCodeStr.substring(0, vendorCodeStr.length() - 1);
            final String vendorMipUrl = ModelsEnum.BASEDATA.getBaseUrl() + "vendor/listVendorMip?vendorCodeStr=" + vendorCodeStr;
            final String vendorMipRes = restTemplate.getForEntity(vendorMipUrl, String.class).getBody();
            DataResponse<List<VendorMip>> vendorMipResResponse = JSON.parseObject(vendorMipRes, new TypeReference<DataResponse<List<VendorMip>>>() {
            });
            vendorMipList = vendorMipResResponse.getData();
        }

        //外包人力费率
        //获取项目角色
        Map<Long, ProjectMember> projectMemberMap = getProjectMemberMap(projectId);
        if (!CollectionUtils.isEmpty(workingHours)) {
            // 先收集人力费率
            for (WorkingHourResultDto workingHour : workingHours) {
                if (workingHour.getLaborCostType() == null) {
                    continue;
                }
                if (workingHour.getBizUnitId() == null && workingHour.getLaborCostSourceUnitId() == null) {
                    continue;
                }
                // 工时费率获取时应该优先取填报人所在部门的费率
                final Long bizUnitId = workingHour.getLaborCostSourceUnitId() != null ?
                        workingHour.getLaborCostSourceUnitId() : workingHour.getBizUnitId();
                final Map<String, BigDecimal> unitPriceMap = unitMap.get(bizUnitId);
                // 判断实际人力费率是否存在
                BigDecimal costMoney = getCostMoney(workingHour, unitPriceMap);
                //外包
                if (UserType.HRO.code().equals(workingHour.getUserType()) && !workingHour.getIsImport()) {
                    logger.info("外包人员:" + workingHour.getId());
                    if (unitPriceMap == null) {
                        return null;
                    }
                    if (StringUtils.isNotEmpty(workingHour.getRoleName())) {
                        costMoney = unitPriceMap.getOrDefault(workingHour.getRoleName(), BigDecimal.ZERO);
                    }

                }
                // 实际人力费率为空的不归集
                if (costMoney == null || costMoney.compareTo(BigDecimal.ZERO) == 0) {
                    logger.info("工时ID：{}，项目ID：{}，出勤月份：{}，类型：{}, 职级：{}, 使用单位：{}，未找到对应人力费率，不归集",
                            workingHour.getId(), projectId, DateUtils.format(workingHour.getApplyDate(), DateUtils.FORMAT_YEAR_MONTH),
                            workingHour.getLaborCostType(), workingHour.getLevel(), bizUnitId);
                    continue;
                }

                // 人工费用
                LaborCostDetail laborCostDetail = initLaborCostDetail(workingHour, projectMemberMap, costMoney);
                BigDecimal cost = laborCostDetail.getCostMoney().multiply(laborCostDetail.getActualWorkingHours().divide(new BigDecimal(8)))
                        .setScale(2, BigDecimal.ROUND_HALF_UP);
                laborCostDetail.setCostTotal(cost);
                //外部员工 归集后labor_cost_detail的 project_id为空，user_name为空，外部工时的user_name名称可以取vendor_mip下的vendor_name -- youwei
                if (UserType.HRO.code().equals(laborCostDetail.getUserType())) {
                    laborCostDetail.setProjectId(workingHour.getProjectId());
                    if (StringUtils.isNotEmpty(workingHour.getVendorCode())) {
                        if (!CollectionUtils.isEmpty(vendorMipList)) {
                            // 先收集人力费率
                            for (VendorMip vendorMip : vendorMipList) {
                                if (workingHour.getVendorCode().equals(vendorMip.getVendorCode())) {
                                    laborCostDetail.setUserName(vendorMip.getVendorName());
                                }
                            }
                        }
                    }
                }

                laborCostDetails.add(laborCostDetail);
            }
        }
        return laborCostDetails;
    }

    @Override
    public void batchSave(Long collectionId, List<LaborCostDetail> laborCostDetails) {
        if (!CollectionUtils.isEmpty(laborCostDetails)) {
            for (LaborCostDetail laborCostDetail : laborCostDetails) {
                laborCostDetail.setCostCollectionId(collectionId);
                laborCostDetail.setDeletedFlag(false);
                laborCostDetail.setAccountingFlag(0);
                //外部人员 无须入账
                if (UserType.HRO.code().equals(laborCostDetail.getUserType())) {
                    laborCostDetail.setStatus(3);
                    laborCostDetail.setAccountingFlag(2);
                }
                laborCostDetailMapper.insert(laborCostDetail);
            }
        }
    }

    @Override
    public List<LaborCostDetailDto> queryDetailByCollectionIdAndType(Long collectionId, List<String> typeList) {
        LaborCostDetailExample example = new LaborCostDetailExample();
        LaborCostDetailExample.Criteria criteria = example.createCriteria();
        criteria.andCostCollectionIdEqualTo(collectionId).andDeletedFlagEqualTo(Boolean.FALSE);
        if (!CollectionUtils.isEmpty(typeList)) {
            criteria.andUserTypeIn(typeList);
        }
        List<LaborCostDetail> laborCostDetails = laborCostDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(laborCostDetails)) {
            return Collections.emptyList();
        }
        return BeanConverter.copy(laborCostDetails, LaborCostDetailDto.class);
    }

    @Override
    public List<LaborCostDetail> queryByCollection(Long collectionId) {
        LaborCostDetailExample example = new LaborCostDetailExample();
        example.createCriteria().andCostCollectionIdEqualTo(collectionId).andDeletedFlagEqualTo(false);
        List<LaborCostDetail> laborCostDetails = laborCostDetailMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(laborCostDetails)) {
            return laborCostDetails;
        }
        return null;
    }

    /**
     * 获取项目确认工时
     *
     * @param collectionDate 确认时间
     * @param projectId      项目ID
     * @return 工时信息
     */
    private List<WorkingHourResultDto> getToConfirmWorkingHours(Date collectionDate, Long projectId) {
        Map<String, Date> startAndEnd = DateUtils.getStartAndEnd(collectionDate);
        List<WorkingHourResultDto> workingHourDtoList =
                workingHourExtMapper.getToConfirmWorkingHours(startAndEnd.get("start"), startAndEnd.get("end"), projectId);
        return filterWorkingHours(workingHourDtoList);
    }

    /**
     * hr部门ou科目配置
     * 跨单位ou科目配置
     *
     * @param workingHourResultList
     * @return
     */
    private List<WorkingHourResultDto> filterWorkingHours(List<WorkingHourResultDto> workingHourResultList) {
        if (CollectionUtils.isEmpty(workingHourResultList)) {
            return Collections.emptyList();
        }

        //批量查询发薪OU
        List<String> statisticDateList = workingHourResultList.stream().map(WorkingHourResultDto::getStatisticDate).distinct().collect(Collectors.toList());
        statisticDateList.add("-1"); //防空
        CnbEmpBasicDataExample cnbEmpBasicDataExample = new CnbEmpBasicDataExample();
        cnbEmpBasicDataExample.createCriteria().andStatisticDateIn(statisticDateList).andOuIdIsNotNull().andDeletedFlagEqualTo(Boolean.FALSE);
        cnbEmpBasicDataExample.setOrderByClause("create_at desc");
        List<CnbEmpBasicData> cnbEmpBasicDataList = cnbEmpBasicDataMapper.selectByExample(cnbEmpBasicDataExample);
        Map<String, Long> fullPayOuIdMap = cnbEmpBasicDataList.stream().collect(Collectors.toMap(s -> buildCnbEmpBasicDataKey(s.getUserId(), s.getStatisticDate()), s -> s.getOuId(), (a, b) -> a));

        List<WorkingHour> updateWorkingHourList = new ArrayList<>();
        Map<String, String> userHardWorkingMap = new HashMap<>(50);
        Map<String, String> laborHardWorkingMap = new HashMap<>(10);
        Map<String, String> companyHardWorkingMap = new HashMap<>(10);
        List<WorkingHourResultDto> resultList = workingHourResultList.stream().filter(e -> {
            // 项目ou
            Long ouId = e.getOuId();
            // 出勤人发薪ou（如果最新的推过来了，更新working_hour表）
            Long fullPayOuId = Optional.ofNullable(e.getFullPayOuId()).orElse(ouId);
            Long lastestFullPayOuId = fullPayOuIdMap.get(buildCnbEmpBasicDataKey(e.getUserId(), e.getStatisticDate()));
            if (lastestFullPayOuId != null && !lastestFullPayOuId.equals(fullPayOuId)) {
                fullPayOuId = lastestFullPayOuId;

                WorkingHour updateItem = new WorkingHour();
                updateItem.setId(e.getId());
                updateItem.setFullPayOuId(fullPayOuId);
                updateItem.setUpdateAt(new Date());
                updateWorkingHourList.add(updateItem);
            }

            Long bizUnitId = e.getBizUnitId(); // 项目使用单位
//            Long laborCostSourceUnitId = e.getLaborCostSourceUnitId(); // 考勤使用单位
            Long laborCostTypeSetId = e.getLaborCostTypeSetId(); // 人力费率
            // 跨单位工时查找科目配置
            if (fullPayOuId != null && !fullPayOuId.equals(ouId)) {
                String hardWorking = companyHardWorkingMap.get(bizUnitId + "" + ouId + fullPayOuId);
                if (hardWorking != null && !"null".equals(hardWorking)) {
                    e.setHardWorking(hardWorking);
                    return true;
                }
                DiffCompanyLaborCostSetDTO diffCompanyLaborCostSet =
                        basedataExtService.getDiffCompanyLaborCostSet(bizUnitId, fullPayOuId, ouId);
                if (diffCompanyLaborCostSet != null && StringUtils.isNotEmpty(diffCompanyLaborCostSet.getHardWorking())) {
                    companyHardWorkingMap.put(bizUnitId + "" + ouId + fullPayOuId, diffCompanyLaborCostSet.getHardWorking());
                    e.setHardWorking(diffCompanyLaborCostSet.getHardWorking());
                    return true;
                }
                companyHardWorkingMap.put(bizUnitId + "" + ouId + fullPayOuId, "null");
            }
            // 非跨单位工时,查hr人力费率ou科目配置
            String hardWorking = userHardWorkingMap.get(e.getUserId() + "" + ouId);
            if (hardWorking != null) {
                if ("null".equals(hardWorking)) {
                    return false;
                } else {
                    e.setHardWorking(hardWorking);
                    return true;
                }
            }
            UserInfo userInfo = CacheDataUtils.findUserById(e.getUserId());
            if (userInfo != null) {
                // 如果工时表存的时点setid空则实时取用户表
                laborCostTypeSetId = laborCostTypeSetId == null ? userInfo.getLaborCostTypeSetId() : laborCostTypeSetId;
                if (laborCostTypeSetId != null) {
                    hardWorking = laborHardWorkingMap.get(laborCostTypeSetId + "" + ouId);
                    if (hardWorking != null) {
                        if ("null".equals(hardWorking)) {
                            return false;
                        } else {
                            e.setHardWorking(hardWorking);
                            return true;
                        }
                    }
                    OrgLaborCostTypeSetDTO laborCostTypeSetDTO = basedataExtService.findOrgLaborCostTypeSetById(laborCostTypeSetId, ouId);
                    if (laborCostTypeSetDTO != null && StringUtils.isNotEmpty(laborCostTypeSetDTO.getHardWorking())) {
                        userHardWorkingMap.put(e.getUserId() + "" + ouId, laborCostTypeSetDTO.getHardWorking());
                        laborHardWorkingMap.put(laborCostTypeSetId + "" + ouId, laborCostTypeSetDTO.getHardWorking());
                        e.setHardWorking(laborCostTypeSetDTO.getHardWorking());
                        return true;
                    }
                }
                laborHardWorkingMap.put(laborCostTypeSetId + "" + ouId, "null");
            }
            userHardWorkingMap.put(e.getUserId() + "" + ouId, "null");
            logger.warn("用户【{}】的科目未查找到，不归集此用户的工时", e.getUserId());
            return false;
        }).collect(Collectors.toList());

        // 批量更新working_hour表 发薪ou
        if(!CollectionUtils.isEmpty(updateWorkingHourList)) {
            ListUtils.splistList(updateWorkingHourList, 200).forEach(list -> workingHourExtMapper.batchUpdate(list));
        }

        return resultList;
    }

    /**
     * 初始化人工成本明细
     *
     * @param workingHour      考情工时
     * @param projectMemberMap 项目成员
     * @return 人工成本明细
     */
    private LaborCostDetail initLaborCostDetail(WorkingHourResultDto workingHour, Map<Long, ProjectMember> projectMemberMap, BigDecimal costMoney) {
        UserInfo userInfo = CacheDataUtils.findUserById(workingHour.getUserId());
        LaborCostDetail laborCostDetail = new LaborCostDetail();
        laborCostDetail.setUserId(workingHour.getUserId());
        if (userInfo != null) {
            laborCostDetail.setUserName(userInfo.getName());
        }

        //外部人员听说不需要是项目人员
        if (userInfo != null) {
            if (UserType.INTERNAL.code().equals(workingHour.getUserType()) || UserType.RECRUIT.code().equals(workingHour.getUserType())) {
                laborCostDetail.setProjectId(workingHour.getProjectId());
                if (projectMemberMap.containsKey(userInfo.getId())) {
                    Long roleId = projectMemberMap.get(userInfo.getId()).getProjectMemberRole();
                    if (roleId != null) {
                        ProjectRole projectRole = projectRoleMapper.selectByPrimaryKey(roleId);
                        laborCostDetail.setProjectRole(projectRole.getName());
                    }
                }
            }
        }

        laborCostDetail.setUserType(workingHour.getUserType());
        laborCostDetail.setApplyDate(workingHour.getApplyDate());
        laborCostDetail.setSystemApplyDate(DateUtils.getShortDate(workingHour.getApproveTime()));
        laborCostDetail.setFillInDate(DateUtils.getShortDate(workingHour.getCreateAt()));
        laborCostDetail.setActualWorkingHours(workingHour.getActualWorkingHours());
        laborCostDetail.setPlanCostMoney(workingHour.getCostMoney());
        // 实际人力费率
        laborCostDetail.setCostMoney(costMoney);
        laborCostDetail.setHardWorking(workingHour.getHardWorking());
        laborCostDetail.setWbsBudgetCode(workingHour.getWbsBudgetCode());
        laborCostDetail.setLaborWbsCostId(workingHour.getLaborWbsCostId());
        laborCostDetail.setWorkingHourId(workingHour.getId());
        laborCostDetail.setDeletedFlag(false);
        return laborCostDetail;
    }

    /**
     * 人工费率取值逻辑：
     * 用working_hour表的 apply_date/labor_cost_type/level
     * 匹配 labor_cost_rank_real_month_detail的 month(在头表)/labor_cost_type_code/level_name
     * 如果匹配上就取 labor_cost_rank_real_month_detail.unit_price
     *
     * @param workingHour
     * @param unitPriceMap
     * @return
     */
    private BigDecimal getCostMoney(WorkingHour workingHour, Map<String, BigDecimal> unitPriceMap) {
        if (workingHour.getIsImport()) {
            return workingHour.getCostMoney();
        }

        if (MapUtils.isEmpty(unitPriceMap)) {
            return null;
        }
        String applyMonth = null != workingHour.getApplyDate() ?
                DateUtils.format(workingHour.getApplyDate(), DateUtils.FORMAT_YEAR_MONTH) : null;
        String key = applyMonth + "-" + workingHour.getLaborCostType().toUpperCase() + "-" + workingHour.getLevel();
        logger.debug("实际人力费率过滤: {}", key);
        BigDecimal costMoney = unitPriceMap.get(key.toUpperCase());
        return costMoney;
    }

    /**
     * 获取项目角色，key：用户ID
     *
     * @param projectId 项目ID
     * @return 项目角色
     */
    private Map<Long, ProjectMember> getProjectMemberMap(Long projectId) {
        Map<Long, ProjectMember> projectMemberMap = new HashMap<>();
        ProjectMemberExample example = new ProjectMemberExample();
        example.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(false);
        List<ProjectMember> projectMembers = projectMemberMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(projectMembers)) {
            projectMembers.forEach(projectMember -> {
                projectMemberMap.put(projectMember.getUserId(), projectMember);
            });
        }
        return projectMemberMap;
    }

    /**
     * 根据用户ID，填报日期判断是否存在人工费用
     *
     * @param projectId      项目ID
     * @param userId         用户ID
     * @param applyDate      出勤日期
     * @param collectionDate 归集日期
     * @return 人工费用
     */
    private LaborCostDetail queryExist(Long projectId, Long userId, Date applyDate, Date collectionDate) {
        LaborCostDetailExample example = new LaborCostDetailExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andSystemApplyDateEqualTo(collectionDate)
                .andProjectIdEqualTo(projectId)
                .andApplyDateEqualTo(applyDate);
        List<LaborCostDetail> laborCostDetails = laborCostDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(laborCostDetails)) {
            return null;
        } else {
            return laborCostDetails.get(0);
        }
    }

    @Override
    public List<LaborCostDetail> findNeedRestartCollection() {
        LaborCostDetailExample example = new LaborCostDetailExample();
        final LaborCostDetailExample.Criteria criteria = example.createCriteria();
        criteria.andCostCollectionIdIsNotNull();
        criteria.andCostMoneyEqualTo(BigDecimal.ZERO);
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andUserTypeEqualTo("1");
        final List<LaborCostDetail> laborCostDetails = laborCostDetailMapper.selectByExample(example);
        return laborCostDetails;
    }

    @Override
    public void updateLaborCostHardWorking(LaborCostDetailDto queryDto) {
        if (queryDto.getOuId() == null
                && queryDto.getProjectId() == null
                && queryDto.getUserId() == null) {
            return;
        }
        // 查询需要更新的数据
        List<WorkingHourResultDto> toUpdateLaborList = workingHourExtMapper.selectToUpdateLaborCost(queryDto);
        if (ListUtils.isEmpty(toUpdateLaborList)) {
            return;
        }
        // 过滤并重新查询科目
        List<WorkingHourResultDto> workingHourResultDtoList = filterWorkingHours(toUpdateLaborList);
        // 根据人工成本明细id更新未入账状态下的科目
        for (WorkingHourResultDto workingHourResultDto : workingHourResultDtoList) {
            LaborCostDetail laborCostDetail = new LaborCostDetail();
            laborCostDetail.setId(workingHourResultDto.getLaborCostDetailId());
            laborCostDetail.setHardWorking(workingHourResultDto.getHardWorking());
            workingHourExtMapper.updateLaborCostHardWorking(laborCostDetail);
        }
    }

    @Override
    public void syncLaborCostHardWorking(String lastUpdateDate) {
        logger.info("开始同步人工成本明细hard_working字段，最后更新时间：{}", lastUpdateDate);

        // 1. 查询需要同步的数据
        List<LaborCostHardWorkingSyncDto> needSyncDataList = cnbEmpBasicDataExtMapper.selectNeedSyncHardWorkingData(lastUpdateDate);

        if (CollectionUtils.isEmpty(needSyncDataList)) {
            logger.info("没有需要同步的数据");
            return;
        }

        logger.info("查询到需要同步的数据条数：{}", needSyncDataList.size());

        // 2. 为每条数据查询对应的hard_working科目配置
        List<LaborCostHardWorkingSyncDto> toUpdateList = new ArrayList<>();
        Map<String, String> hardWorkingCache = new HashMap<>();
        Map<Long, Long> bizUnitIdCache = new HashMap<>();

        for (LaborCostHardWorkingSyncDto syncData : needSyncDataList) {
            Long fullPayOuId = syncData.getFullPayOuId();
            Long ouId = syncData.getOuId();
            Long bizUnitId = bizUnitIdCache.get(ouId);
            if (bizUnitId == null) {
                bizUnitId = CacheDataUtils.getTopUnitIdByOuId(ouId);
                bizUnitIdCache.put(ouId, bizUnitId);
            }

            // 使用缓存避免重复查询
            String cacheKey = bizUnitId + "_" + fullPayOuId + "_" + ouId;
            String hardWorking = hardWorkingCache.get(cacheKey);

            if (hardWorking == null) {
                // 调用跨单位工时查找科目配置逻辑
                DiffCompanyLaborCostSetDTO diffCompanyLaborCostSet = basedataExtService.getDiffCompanyLaborCostSet(bizUnitId, fullPayOuId, ouId);

                if (diffCompanyLaborCostSet != null && StringUtils.isNotEmpty(diffCompanyLaborCostSet.getHardWorking())) {
                    hardWorking = diffCompanyLaborCostSet.getHardWorking();
                    hardWorkingCache.put(cacheKey, hardWorking);
                } else {
                    hardWorkingCache.put(cacheKey, "null");
                    logger.warn("用户【{}】统计日期【{}】未找到跨单位工时科目配置，bizUnitId={}, fullPayOuId={}, ouId={}",
                            syncData.getUserId(), syncData.getStatisticDate(), bizUnitId, fullPayOuId, ouId);
                    continue;
                }
            } else if ("null".equals(hardWorking)) {
                continue;
            }

            if (!Objects.equals(syncData.getHardWorking(), hardWorking)) {
                syncData.setHardWorking(hardWorking);
                toUpdateList.add(syncData);
            }
        }

        if (CollectionUtils.isEmpty(toUpdateList)) {
            logger.info("实际需要更新的条数为0，无需更新");
            return;
        }

        // 3. 批量更新labor_cost_detail表的hard_working字段
        logger.info("开始批量更新labor_cost_detail表，更新条数：{}，更新的数据：{}", toUpdateList.size(), JsonUtils.toString(toUpdateList));

        // 分批处理，避免SQL过长
        int batchSize = 100;
        Date date = new Date();
        for (int i = 0; i < toUpdateList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, toUpdateList.size());
            List<LaborCostHardWorkingSyncDto> batchList = toUpdateList.subList(i, endIndex);

            try {
                cnbEmpBasicDataExtMapper.batchUpdateLaborCostDetailHardWorking(batchList);
                logger.info("批量更新成功，批次：{}-{}", i, endIndex - 1);
            } catch (Exception e) {
                logger.error("批量更新失败，批次：{}-{}，错误：{}", i, endIndex - 1, e.getMessage(), e);
                throw e;
            }

            // 4. 批量更新working_hour表的full_pay_ou_id字段
            List<WorkingHour> updateWorkingHourList = new ArrayList<>();
            List<Long> workingHourIdList = batchList.stream().map(LaborCostHardWorkingSyncDto::getWorkingHourId).collect(Collectors.toList());
            Map<Long, Long> fullPayOuIdMap = new HashMap<>();
            workingHourService.selectByIds(workingHourIdList).forEach(workingHour -> fullPayOuIdMap.put(workingHour.getId(), workingHour.getFullPayOuId()));
            for (LaborCostHardWorkingSyncDto syncDto : batchList) {
                if (!Objects.equals(syncDto.getFullPayOuId(), fullPayOuIdMap.get(syncDto.getWorkingHourId()))) {
                    WorkingHour updateItem = new WorkingHour();
                    updateItem.setId(syncDto.getWorkingHourId());
                    updateItem.setFullPayOuId(syncDto.getFullPayOuId());
                    updateItem.setUpdateAt(date);
                    updateWorkingHourList.add(updateItem);
                }
            }
            if (!CollectionUtils.isEmpty(updateWorkingHourList)) {
                workingHourExtMapper.batchUpdate(updateWorkingHourList);
            }

            logger.info("开始批量更新working_hour表，更新条数：{}，更新的数据：{}", updateWorkingHourList.size(), JsonUtils.toString(updateWorkingHourList));
        }

        logger.info("人工成本明细hard_working字段同步完成");
    }

    private String buildCnbEmpBasicDataKey(Long userId, String statisticDate) {
        return String.format("buildCnbEmpBasicDataKey_%s_%s", userId, statisticDate);
    }
}
