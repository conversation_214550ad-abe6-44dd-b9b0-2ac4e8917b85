package com.midea.pam.ctc.project.service.impl;

import com.midea.pam.common.basedata.dto.FeeItemDto;
import com.midea.pam.common.basedata.entity.LaborCost;
import com.midea.pam.common.basedata.entity.LaborExternalCost;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.FlatChangeHistoryDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetHumanChangeHistoryDto;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetAsset;
import com.midea.pam.common.ctc.entity.ProjectBudgetAssetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetAssetExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetFee;
import com.midea.pam.common.ctc.entity.ProjectBudgetFeeChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetFeeExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetHuman;
import com.midea.pam.common.ctc.entity.ProjectBudgetHumanChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetHumanExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterial;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialAddRelation;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetTargetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetTargetChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravel;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravelChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravelExample;
import com.midea.pam.common.ctc.entity.ProjectBusinessRsChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectMemberChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectMemberChangeHistoryExample;
import com.midea.pam.common.ctc.vo.ProjectBudgetChangeVO;
import com.midea.pam.common.ctc.vo.ProjectBudgetFeeChangeHistoryVO;
import com.midea.pam.common.enums.ChangeType;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.PurchaseContractStatus;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetAssetMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetMaterialExtMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetTargetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBusinessRsChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectMemberChangeHistoryMapper;
import com.midea.pam.ctc.project.service.ProjectBudgetAssetChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetFeeChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetHumanChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetMaterialAddRelationService;
import com.midea.pam.ctc.project.service.ProjectBudgetMaterialChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetTravelChangeHistoryService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.ProjectBudgetFeeService;
import com.midea.pam.ctc.service.ProjectBudgetHumanService;
import com.midea.pam.ctc.service.ProjectBudgetMaterialService;
import com.midea.pam.ctc.service.ProjectBudgetTravelService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-6-28
 * @description
 */
public class ProjectBudgetChangeHelper {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectBudgetMaterialChangeHistoryService materialChangeHistoryService;
    @Resource
    private ProjectBudgetTargetChangeHistoryMapper projectBudgetTargetChangeHistoryMapper;
    @Resource
    private ProjectBudgetHumanChangeHistoryService humanChangeHistoryService;
    @Resource
    private ProjectBudgetTravelChangeHistoryService travelChangeHistoryService;
    @Resource
    private ProjectBudgetFeeChangeHistoryService feeChangeHistoryService;
    @Resource
    private ProjectBudgetMaterialService projectBudgetMaterialService;
    @Resource
    private ProjectBudgetHumanService projectBudgetHumanService;
    @Resource
    private ProjectBudgetTravelService projectBudgetTravelService;
    @Resource
    private ProjectBudgetFeeService projectBudgetFeeService;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private ProjectBudgetMaterialAddRelationService projectBudgetMaterialAddRelationService;
    @Resource
    private ProjectMemberChangeHistoryMapper memberChangeHistoryMapper;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private ProjectBusinessRsChangeHistoryMapper projectBusinessRsChangeHistoryMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private ProjectBudgetAssetMapper projectBudgetAssetMapper;
    @Resource
    private ProjectBudgetAssetChangeHistoryService projectBudgetAssetChangeHistoryService;
    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;
    @Resource
    private ProjectBudgetMaterialExtMapper budgetMaterialExtMapper;


    public void handleChangeMaterialToOfficialTable(Long headerId, Long projectId) {
        // 此次变更记录
        final List<ProjectBudgetMaterialChangeHistory> histories = materialChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);

        // 此次变更前记录
        ProjectBudgetMaterialExample example = new ProjectBudgetMaterialExample();
        final ProjectBudgetMaterialExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        final List<ProjectBudgetMaterial> projectBudgetMaterials = projectBudgetMaterialService.selectByExample(example);

        // 父级新旧ID映射关系
        Map<Long, Long> idMap = new HashMap<>();

        if (ListUtils.isEmpty(projectBudgetMaterials)) {
            if (ListUtils.isNotEmpty(histories)) {

                // 处理父级
                histories.forEach(history -> {
                    if (history.getParentId() == null) {
                        final ProjectBudgetMaterial projectBudgetMaterial = BeanConverter.copyProperties(history, ProjectBudgetMaterial.class);
                        projectBudgetMaterial.setId(null);
                        projectBudgetMaterialService.insert(projectBudgetMaterial);

                        idMap.put(history.getId(), projectBudgetMaterial.getId());
                        //处理子级
                        buildSubLayer(history, idMap, histories);
                    }
                });
            }
        } else {
            // 变更记录为空，说明全部已删除
            if (ListUtils.isEmpty(histories)) {
                projectBudgetMaterials.forEach(projectBudgetMaterial -> {
                    projectBudgetMaterial.setDeletedFlag(Boolean.TRUE);
                    projectBudgetMaterialService.updateByPrimaryKeySelective(projectBudgetMaterial);
                    //删除详设
                    MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
                    example2.createCriteria().andProjectBudgetMaterialIdEqualTo(projectBudgetMaterial.getId()).andDeletedFlagEqualTo(false);
                    List<MilepostDesignPlanDetail> list = milepostDesignPlanDetailMapper.selectByExample(example2);
                    if (ListUtils.isNotEmpty(list)) {
                        for (MilepostDesignPlanDetail dto : list) {
                            MilepostDesignPlanDetailDto entity = new MilepostDesignPlanDetailDto();
                            BeanConverter.copy(entity, dto);
                            entity.setDeletedFlag(Boolean.TRUE);
                            milepostDesignPlanDetailService.save(entity, projectBudgetMaterial.getUpdateBy());
                        }
                    }
                });
                return;
            }

            // 变更前所有物料
            final List<Long> needDeleteList = projectBudgetMaterials.stream().map(ProjectBudgetMaterial::getId).collect(Collectors.toList());

            // 先处理父级
            histories.forEach(history -> {
                if (history.getParentId() == null) {
                    final Long originId = history.getOriginId();

                    final ProjectBudgetMaterial projectBudgetMaterial =
                            BeanConverter.copyProperties(history, ProjectBudgetMaterial.class);

                    // 不为空，则为变更记录
                    if (originId != null) {
                        needDeleteList.remove(originId);

                        projectBudgetMaterial.setId(originId);
                        projectBudgetMaterialService.updateByPrimaryKey(projectBudgetMaterial);
                    } else {
                        // 新增
                        projectBudgetMaterial.setId(new Date().getTime());
                        projectBudgetMaterial.setUpdateAt(new Date());
                        projectBudgetMaterial.setUpdateBy(null);
                        projectBudgetMaterial.setCode(materialExtService.generateMaterialPAMCode());
                        projectBudgetMaterialService.insertSelective(projectBudgetMaterial);

                        // 新增历史表及正式表ID关联关系
                        ProjectBudgetMaterialAddRelation projectBudgetMaterialAddRelation = new ProjectBudgetMaterialAddRelation();
                        projectBudgetMaterialAddRelation.setHistoryId(history.getId());
                        projectBudgetMaterialAddRelation.setOfficialId(projectBudgetMaterial.getId());
                        projectBudgetMaterialAddRelation.setDeletedFlag(Boolean.FALSE);
                        projectBudgetMaterialAddRelation.setProjectId(history.getProjectId());
                        projectBudgetMaterialAddRelationService.insert(projectBudgetMaterialAddRelation);
                    }
                    idMap.put(history.getId(), projectBudgetMaterial.getId());
                    // 处理子级
                    buildSubLayer(history, histories, idMap, needDeleteList);

                }
            });

            // 删除
            if (ListUtils.isNotEmpty(needDeleteList)) {
                needDeleteList.forEach(id -> {
                    //ProjectBudgetMaterial projectBudgetMaterial = new ProjectBudgetMaterial();
                    //projectBudgetMaterial.setId(id);
                    //projectBudgetMaterial.setDeletedFlag(Boolean.TRUE);
                    //projectBudgetMaterialService.updateByPrimaryKeySelective(projectBudgetMaterial);

                    projectBudgetMaterialService.deleteByPrimaryKey(id);
                    //删除详设
                    MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
                    example2.createCriteria().andProjectBudgetMaterialIdEqualTo(id).andDeletedFlagEqualTo(false);
                    List<MilepostDesignPlanDetail> list = milepostDesignPlanDetailMapper.selectByExample(example2);
                    if (ListUtils.isNotEmpty(list)) {
                        for (MilepostDesignPlanDetail dto : list) {
                            MilepostDesignPlanDetailDto entity = new MilepostDesignPlanDetailDto();
                            BeanConverter.copy(entity, dto);
                            entity.setDeletedFlag(Boolean.TRUE);
                            milepostDesignPlanDetailService.save(entity, null);
                        }
                    }
                });
            }


        }

    }

    private void buildSubLayer(ProjectBudgetMaterialChangeHistory history, Map<Long, Long> idMap, List<ProjectBudgetMaterialChangeHistory> histories) {
        List<ProjectBudgetMaterialChangeHistory> historyList
                = histories.stream().filter(his -> history.getId().equals(his.getParentId())).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(historyList)) {
            historyList.forEach(hist -> {
                final ProjectBudgetMaterial projectBudgetMaterial = BeanConverter.copyProperties(hist, ProjectBudgetMaterial.class);
                projectBudgetMaterial.setId(null);
                // 处理上下级关系
                projectBudgetMaterial.setParentId(idMap.get(hist.getParentId()));
                projectBudgetMaterialService.insert(projectBudgetMaterial);

                idMap.put(hist.getId(), projectBudgetMaterial.getId());
                buildSubLayer(hist, idMap, histories);
            });
        }

    }

    private void buildSubLayer(ProjectBudgetMaterialChangeHistory history, List<ProjectBudgetMaterialChangeHistory> histories, Map<Long, Long> idMap, List<Long> needDeleteList) {
        List<ProjectBudgetMaterialChangeHistory> historyList
                = histories.stream().filter(his -> history.getId().equals(his.getParentId())).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(historyList)) {
            historyList.forEach(hist -> {
                final Long originId = hist.getOriginId();

                final ProjectBudgetMaterial projectBudgetMaterial =
                        BeanConverter.copyProperties(hist, ProjectBudgetMaterial.class);

                // 不为空，则为变更记录
                if (originId != null) {
                    needDeleteList.remove(originId);

                    projectBudgetMaterial.setId(originId);
                    // 处理上下级关系
                    projectBudgetMaterial.setParentId(idMap.get(hist.getParentId()));
                    projectBudgetMaterialService.updateByPrimaryKey(projectBudgetMaterial);
                } else {
                    // 新增
                    projectBudgetMaterial.setId(new Date().getTime());
                    projectBudgetMaterial.setUpdateAt(new Date());
                    projectBudgetMaterial.setUpdateBy(null);
                    // 处理上下级关系
                    projectBudgetMaterial.setParentId(idMap.get(hist.getParentId()));
                    projectBudgetMaterial.setCode(materialExtService.generateMaterialPAMCode());

                    projectBudgetMaterialService.insertSelective(projectBudgetMaterial);

                    // 新增历史表及正式表ID关联关系
                    ProjectBudgetMaterialAddRelation projectBudgetMaterialAddRelation = new ProjectBudgetMaterialAddRelation();
                    projectBudgetMaterialAddRelation.setHistoryId(hist.getId());
                    projectBudgetMaterialAddRelation.setOfficialId(projectBudgetMaterial.getId());
                    projectBudgetMaterialAddRelation.setDeletedFlag(Boolean.FALSE);
                    projectBudgetMaterialAddRelation.setProjectId(hist.getProjectId());
                    projectBudgetMaterialAddRelationService.insert(projectBudgetMaterialAddRelation);
                }
                idMap.put(hist.getId(), projectBudgetMaterial.getId());
                buildSubLayer(hist, histories, idMap, needDeleteList);
            });
        }
    }


    public void handleChangeHumanToOfficialTable(Long headerId, Long projectId) {
        final List<ProjectBudgetHumanChangeHistory> histories =
                humanChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);

        ProjectBudgetHumanExample example = new ProjectBudgetHumanExample();
        final ProjectBudgetHumanExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        final List<ProjectBudgetHuman> projectBudgetHumans = projectBudgetHumanService.selectByExample(example);

        if (ListUtils.isEmpty(projectBudgetHumans)) {
            if (ListUtils.isNotEmpty(histories)) {
                histories.forEach(history -> {
                    final ProjectBudgetHuman projectBudgetHuman = BeanConverter.copyProperties(history, ProjectBudgetHuman.class);
                    projectBudgetHuman.setId(null);
                    projectBudgetHumanService.insert(projectBudgetHuman);
                });
            }
        } else {

            // 变更记录为空，说明全部已删除
            if (ListUtils.isEmpty(histories)) {
                projectBudgetHumans.forEach(projectBudgetHuman -> {
                    projectBudgetHuman.setDeletedFlag(Boolean.TRUE);
                    projectBudgetHumanService.updateByPrimaryKeySelective(projectBudgetHuman);
                });
                return;
            }

            // 变更前所有人力
            final List<Long> needDeleteList = projectBudgetHumans.stream().map(ProjectBudgetHuman::getId).collect(Collectors.toList());

            histories.forEach(history -> {
                final Long originId = history.getOriginId();

                final ProjectBudgetHuman projectBudgetHuman =
                        BeanConverter.copyProperties(history, ProjectBudgetHuman.class);

                // 不为空，则为变更记录
                if (originId != null) {
                    needDeleteList.remove(originId);
                    projectBudgetHuman.setId(originId);
                    projectBudgetHumanService.updateByPrimaryKey(projectBudgetHuman);
                } else {
                    // 新增
                    projectBudgetHuman.setId(null);
                    projectBudgetHuman.setUpdateAt(null);
                    projectBudgetHuman.setUpdateBy(null);
                    projectBudgetHumanService.insertSelective(projectBudgetHuman);
                }
            });

            // 删除
            if (ListUtils.isNotEmpty(needDeleteList)) {
                needDeleteList.forEach(id -> {
                    ProjectBudgetHuman projectBudgetHuman = new ProjectBudgetHuman();
                    projectBudgetHuman.setId(id);
                    projectBudgetHuman.setDeletedFlag(Boolean.TRUE);
                    projectBudgetHumanService.updateByPrimaryKeySelective(projectBudgetHuman);
                });
            }
        }
    }

    public void handleChangeTravelToOfficialTable(Long headerId, Long projectId) {
        final List<ProjectBudgetTravelChangeHistory> histories =
                travelChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);

        ProjectBudgetTravelExample example = new ProjectBudgetTravelExample();
        final ProjectBudgetTravelExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        final List<ProjectBudgetTravel> projectBudgetTravels = projectBudgetTravelService.selectByExample(example);

        if (ListUtils.isEmpty(projectBudgetTravels)) {
            if (ListUtils.isNotEmpty(histories)) {
                histories.forEach(history -> {
                    final ProjectBudgetTravel projectBudgetTravel = BeanConverter.copyProperties(history, ProjectBudgetTravel.class);
                    projectBudgetTravel.setId(null);
                    projectBudgetTravelService.insert(projectBudgetTravel);
                });
            }
        } else {

            // 变更记录为空，说明全部已删除
            if (ListUtils.isEmpty(histories)) {
                projectBudgetTravels.forEach(projectBudgetTravel -> {
                    projectBudgetTravel.setDeletedFlag(Boolean.TRUE);
                    projectBudgetTravelService.updateByPrimaryKeySelective(projectBudgetTravel);
                });
                return;
            }

            // 变更前所有差旅
            final List<Long> needDeleteList = projectBudgetTravels.stream().map(ProjectBudgetTravel::getId).collect(Collectors.toList());

            histories.forEach(history -> {
                final Long originId = history.getOriginId();

                final ProjectBudgetTravel projectBudgetTravel =
                        BeanConverter.copyProperties(history, ProjectBudgetTravel.class);

                // 不为空，则为变更记录
                if (originId != null) {
                    needDeleteList.remove(originId);
                    projectBudgetTravel.setId(originId);
                    projectBudgetTravelService.updateByPrimaryKey(projectBudgetTravel);
                } else {
                    // 新增
                    projectBudgetTravel.setId(null);
                    projectBudgetTravel.setUpdateAt(null);
                    projectBudgetTravel.setUpdateBy(null);
                    projectBudgetTravelService.insertSelective(projectBudgetTravel);
                }
            });

            // 删除
            if (ListUtils.isNotEmpty(needDeleteList)) {
                needDeleteList.forEach(id -> {
                    ProjectBudgetTravel projectBudgetTravel = new ProjectBudgetTravel();
                    projectBudgetTravel.setId(id);
                    projectBudgetTravel.setDeletedFlag(Boolean.TRUE);
                    projectBudgetTravelService.updateByPrimaryKeySelective(projectBudgetTravel);
                });
            }
        }
    }

    public void handleChangeFeeToOfficialTable(Long headerId, Long projectId) {
        final List<ProjectBudgetFeeChangeHistory> histories =
                feeChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);

        ProjectBudgetFeeExample example = new ProjectBudgetFeeExample();
        final ProjectBudgetFeeExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        final List<ProjectBudgetFee> projectBudgetFees = projectBudgetFeeService.selectByExample(example);

        if (ListUtils.isEmpty(projectBudgetFees)) {
            if (ListUtils.isNotEmpty(histories)) {
                histories.forEach(history -> {
                    final ProjectBudgetFee projectBudgetFee = BeanConverter.copyProperties(history, ProjectBudgetFee.class);
                    projectBudgetFee.setId(null);
                    projectBudgetFeeService.insert(projectBudgetFee);
                });
            }
        } else {

            // 变更记录为空，说明全部已删除
            if (ListUtils.isEmpty(histories)) {
                projectBudgetFees.forEach(projectBudgetFee -> {
                    projectBudgetFee.setDeletedFlag(Boolean.TRUE);
                    projectBudgetFeeService.updateByPrimaryKeySelective(projectBudgetFee);
                });
                return;
            }

            // 变更前所有项目费用
            final List<Long> needDeleteList = projectBudgetFees.stream().map(ProjectBudgetFee::getId).collect(Collectors.toList());

            histories.forEach(history -> {
                final Long originId = history.getOriginId();

                final ProjectBudgetFee projectBudgetFee =
                        BeanConverter.copyProperties(history, ProjectBudgetFee.class);

                // 不为空，则为变更记录
                if (originId != null) {
                    needDeleteList.remove(originId);
                    projectBudgetFee.setId(originId);
                    projectBudgetFee.setCreateAt(null);
                    projectBudgetFee.setCreateBy(null);
                    projectBudgetFeeService.updateByPrimaryKeySelective(projectBudgetFee);
                } else {
                    // 新增
                    projectBudgetFee.setId(null);
                    projectBudgetFee.setUpdateAt(null);
                    projectBudgetFee.setUpdateBy(null);
                    projectBudgetFeeService.insertSelective(projectBudgetFee);
                }
            });

            // 删除
            if (ListUtils.isNotEmpty(needDeleteList)) {
                needDeleteList.forEach(id -> {
                    ProjectBudgetFee projectBudgetFee = new ProjectBudgetFee();
                    projectBudgetFee.setId(id);
                    projectBudgetFee.setDeletedFlag(Boolean.TRUE);
                    projectBudgetFeeService.updateByPrimaryKeySelective(projectBudgetFee);
                });
            }
        }
    }

    public void handleChangeAssetToOfficialTable(Long headerId, Long projectId) {
        final List<ProjectBudgetAssetChangeHistory> histories =
                projectBudgetAssetChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);

        ProjectBudgetAssetExample example = new ProjectBudgetAssetExample();
        final ProjectBudgetAssetExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        final List<ProjectBudgetAsset> projectBudgetAssets = projectBudgetAssetMapper.selectByExample(example);

        if (ListUtils.isEmpty(projectBudgetAssets)) {
            if (ListUtils.isNotEmpty(histories)) {
                histories.forEach(history -> {
                    final ProjectBudgetAsset projectBudgetAsset = BeanConverter.copyProperties(history, ProjectBudgetAsset.class);
                    projectBudgetAsset.setId(null);
                    projectBudgetAssetMapper.insert(projectBudgetAsset);
                });
            }
        } else {

            // 变更记录为空，说明全部已删除
            if (ListUtils.isEmpty(histories)) {
                projectBudgetAssets.forEach(projectBudgetAsset -> {
                    projectBudgetAsset.setDeletedFlag(Boolean.TRUE);
                    projectBudgetAssetMapper.updateByPrimaryKeySelective(projectBudgetAsset);
                });
                return;
            }

            // 变更前所有资产预算
            final List<Long> needDeleteList = projectBudgetAssets.stream().map(ProjectBudgetAsset::getId).collect(Collectors.toList());

            histories.forEach(history -> {
                final Long originId = history.getOriginId();

                final ProjectBudgetAsset projectBudgetAsset =
                        BeanConverter.copyProperties(history, ProjectBudgetAsset.class);

                // 不为空，则为变更记录
                if (originId != null) {
                    needDeleteList.remove(originId);
                    projectBudgetAsset.setId(originId);
                    projectBudgetAssetMapper.updateByPrimaryKey(projectBudgetAsset);
                } else {
                    // 新增
                    projectBudgetAsset.setId(null);
                    projectBudgetAsset.setUpdateAt(null);
                    projectBudgetAsset.setUpdateBy(null);
                    projectBudgetAssetMapper.insertSelective(projectBudgetAsset);
                }
            });

            // 删除
            if (ListUtils.isNotEmpty(needDeleteList)) {
                needDeleteList.forEach(id -> {
                    ProjectBudgetAsset projectBudgetAsset = new ProjectBudgetAsset();
                    projectBudgetAsset.setId(id);
                    projectBudgetAsset.setDeletedFlag(Boolean.TRUE);
                    projectBudgetAssetMapper.updateByPrimaryKeySelective(projectBudgetAsset);
                });
            }
        }
    }

    /**
     * 构建物料变更信息
     *
     * @param headerId
     * @param projectBudgetChangeVO
     */
    public void buildMaterialChangeHistory(Long headerId, ProjectBudgetChangeVO projectBudgetChangeVO) {
        final List<ProjectBudgetMaterialChangeHistory> beforeMaterialHistories =
                materialChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.HISTORY, null);
        Map<Long, ProjectBudgetMaterialChangeHistory> beforeMaterialOriginMap = new HashMap<>();
        if (ListUtils.isNotEmpty(beforeMaterialHistories)) {
            beforeMaterialHistories.forEach(history -> {
                if (history.getOriginId() != null) {
                    beforeMaterialOriginMap.put(history.getOriginId(), history);
                }
            });
        }

        final List<ProjectBudgetMaterialChangeHistory> afterMaterialHistories =
                materialChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);
        if (ListUtils.isNotEmpty(afterMaterialHistories)) {
            // 物料变更记录
            List<ProjectBudgetChangeVO.ChangeHistory> materialHistories = new ArrayList<>();

            afterMaterialHistories.forEach(history -> {
                FlatChangeHistoryDTO flatDto = new FlatChangeHistoryDTO();
                BeanUtils.copyProperties(history, flatDto);
                final Long originId = history.getOriginId();
                final Boolean deletedFlag = history.getDeletedFlag();
                // 设置 HasDesign 属性
                Long designCount = milepostDesignPlanDetailExtMapper.getDesignBybudgetMaterialId(originId);
                flatDto.setHasDesign(designCount != null && designCount > 0);

                // 设置 deleteConfirm 属性
                Integer cancelStatus = PurchaseContractStatus.CANCEL.getCode();
                Integer refuseStatus = PurchaseContractStatus.REFUSE.getCode();
                // 检查当前物料是否关联有效的采购合同
                boolean hasPurchaseContract = checkPurchaseContractExists(originId, cancelStatus, refuseStatus);
                // 检查当前物料是否是叶子节点（没有子节点）
                boolean isLeafNode = checkIsLeafNode(originId, afterMaterialHistories);
                //如果没有采购合同 或 是叶子节点，则允许删除（true）
                flatDto.setDeleteConfirm(!hasPurchaseContract || isLeafNode);
                ProjectBudgetChangeVO.ChangeHistory changeHistory = buildChangeHistory(originId, deletedFlag, beforeMaterialOriginMap);
                changeHistory.setChange(flatDto);
                materialHistories.add(changeHistory);
            });

            Map<Long, List<Long>> parentMap = new HashMap<>();
            Map<Long, ProjectBudgetChangeVO.ChangeHistory> changeMap = new HashMap<>();
            List<ProjectBudgetChangeVO.ChangeHistory> parentMaterialHistories = new ArrayList<>();

            // 构建树
            materialHistories.forEach(history -> {
                final FlatChangeHistoryDTO change = (FlatChangeHistoryDTO) history.getChange();
                final Long id = change.getId();
                final Long parentId = change.getParentId();
                if (parentId == null) {
                    parentMaterialHistories.add(history);
                }
            });

            buildMaterialTree(parentMaterialHistories, materialHistories, true);

            projectBudgetChangeVO.setMaterialHistories(parentMaterialHistories);
        }
    }

    private boolean checkPurchaseContractExists(Long materialId, Integer cancelStatus, Integer refuseStatus) {
        Integer count = budgetMaterialExtMapper.countPurchaseContractsByMaterialId(materialId, cancelStatus, refuseStatus);
        return count != null && count > 0;
    }

    private boolean checkIsLeafNode(Long materialId, List<ProjectBudgetMaterialChangeHistory> allHistories) {
        if (allHistories == null || materialId == null) {
            return true;
        }
        return allHistories.stream()
                .filter(history -> history != null && history.getParentId() != null)
                .noneMatch(history -> materialId.equals(history.getParentId()));
    }


    private void buildMaterialTree(List<ProjectBudgetChangeVO.ChangeHistory> parentMaterialHistories, List<ProjectBudgetChangeVO.ChangeHistory> materialHistories, boolean isRootLevel) {
        parentMaterialHistories.forEach(history -> {
            final List<ProjectBudgetChangeVO.ChangeHistory> subHistories = new ArrayList<>();
            final FlatChangeHistoryDTO change = (FlatChangeHistoryDTO) history.getChange();
            //保持原来逻辑，最外层的HasDesign赋默认值false
            if (isRootLevel) {
                change.setHasDesign(false);
            }
            final Long id = change.getId();
            final Integer type = history.getType();
            if (ChangeType.ADD.code() == type) {
                final ProjectBudgetMaterialAddRelation parentMaterialAddRelation = projectBudgetMaterialAddRelationService.findByHistoryId(id);
                if (parentMaterialAddRelation != null) {
                    change.setOriginId(parentMaterialAddRelation.getOfficialId());

                    final ProjectBudgetMaterial projectBudgetMaterial =
                            projectBudgetMaterialService.selectByPrimaryKey(
                                    change.getOriginId()
                            );
                    change.setCode(projectBudgetMaterial != null ? projectBudgetMaterial.getCode() : null);
                }
            }
            for (ProjectBudgetChangeVO.ChangeHistory materialHistory : materialHistories) {
                final FlatChangeHistoryDTO hisChange = (FlatChangeHistoryDTO) materialHistory.getChange();
                Long changeParentId = hisChange.getParentId();
                if (changeParentId == null) {
                    continue;
                }
                if (changeParentId.equals(id)) {
                    subHistories.add(materialHistory);
                }
            }
            if (ListUtils.isNotEmpty(subHistories)) {
                history.setSubHistories(subHistories);
                buildMaterialTree(subHistories, materialHistories, false);
            }
        });
    }

    public void buildHumanChangeHistory(Long headerId, ProjectBudgetChangeVO projectBudgetChangeVO) {
        final List<ProjectBudgetHumanChangeHistory> beforeHumanHistories = humanChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.HISTORY, null);
        Map<Long, ProjectBudgetHumanChangeHistory> beforeHumanOriginMap = new HashMap<>();
        if (ListUtils.isNotEmpty(beforeHumanHistories)) {
            beforeHumanHistories.forEach(history -> {
                UserInfo userInfo = CacheDataUtils.findUserById(history.getUserId());
                if (userInfo != null) {
                    history.setName(userInfo.getName());
                }
                if (history.getOriginId() != null) {
                    beforeHumanOriginMap.put(history.getOriginId(), history);
                }
            });
        }

        final List<ProjectBudgetHumanChangeHistory> afterHumanHistories = humanChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);
        if (ListUtils.isNotEmpty(afterHumanHistories)) {
            // 人力变更记录
            List<ProjectBudgetChangeVO.ChangeHistory> humanHistories = new ArrayList<>();
            for (ProjectBudgetHumanChangeHistory history : afterHumanHistories) {
                final ProjectBudgetHumanChangeHistoryDto dto = new ProjectBudgetHumanChangeHistoryDto();
                BeanUtils.copyProperties(history, dto);
                final Long originId = history.getOriginId();
                final Boolean deletedFlag = history.getDeletedFlag();
                UserInfo userInfo = CacheDataUtils.findUserById(history.getUserId());
                if (userInfo != null) {
                    dto.setName(userInfo.getName());
                }
                if (null != history.getFunctionId()) {
                    dto.setProjectRole(history.getFunctionId());
                    if (Objects.equals(1, history.getType()) || Objects.equals(3, history.getType())) {//内部&自招外包
                        LaborCost laborCost = CacheDataUtils.findLaborCostById(history.getFunctionId());
                        dto.setProjectRoleName(Objects.nonNull(laborCost) ? laborCost.getName() : null);
                    } else if (Objects.equals(2, history.getType()) && StringUtils.isNotBlank(history.getVendorName())) {
                        LaborExternalCost laborExternalCost = basedataExtService.getLaborExternalCost(history.getFunctionId(), history.getVendorName());
                        dto.setProjectRoleName(Objects.nonNull(laborExternalCost) ? laborExternalCost.getName() : null);
                    }
                }
                ProjectBudgetChangeVO.ChangeHistory changeHistory = buildChangeHistory(originId, deletedFlag, beforeHumanOriginMap);
                changeHistory.setChange(dto);
                humanHistories.add(changeHistory);
            }

            projectBudgetChangeVO.setHumanHistories(humanHistories);
        }
    }

    public void buildMemberChangeHistory(Long headerId, ProjectBudgetChangeVO projectBudgetChangeVO) {
        ProjectMemberChangeHistoryExample example = new ProjectMemberChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headerId)
                .andHistoryTypeEqualTo(HistoryType.HISTORY.getCode());
        final List<ProjectMemberChangeHistory> beforeMemberHistories = memberChangeHistoryMapper.selectByExample(example);
        Map<Long, ProjectMemberChangeHistory> beforeMemberOriginMap = new HashMap<>();
        if (ListUtils.isNotEmpty(beforeMemberHistories)) {
            beforeMemberHistories.forEach(history -> {
                if (history.getOriginId() != null) {
                    beforeMemberOriginMap.put(history.getOriginId(), history);
                }
            });
        }

        ProjectMemberChangeHistoryExample example1 = new ProjectMemberChangeHistoryExample();
        example1.createCriteria().andHeaderIdEqualTo(headerId)
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        final List<ProjectMemberChangeHistory> afterMemberHistories = memberChangeHistoryMapper.selectByExample(example1);
        if (ListUtils.isNotEmpty(afterMemberHistories)) {
            // 人力变更记录
            List<ProjectBudgetChangeVO.ChangeHistory> memberHistories = new ArrayList<>();

            afterMemberHistories.forEach(history -> {
                final Long originId = history.getOriginId();
                final Boolean deletedFlag = history.getDeletedFlag();
                ProjectBudgetChangeVO.ChangeHistory changeHistory = buildChangeHistory(originId, deletedFlag, beforeMemberOriginMap);
                changeHistory.setChange(history);
                memberHistories.add(changeHistory);
            });

            projectBudgetChangeVO.setMemberHistories(memberHistories);
        }
    }

    public void buildTravelChangeHistory(Long headerId, ProjectBudgetChangeVO projectBudgetChangeVO) {
        final List<ProjectBudgetTravelChangeHistory> beforeTravelHistories = travelChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.HISTORY, null);
        Map<Long, ProjectBudgetTravelChangeHistory> beforeTravelOriginMap = new HashMap<>();
        if (ListUtils.isNotEmpty(beforeTravelHistories)) {
            beforeTravelHistories.forEach(history -> {
                if (history.getOriginId() != null) {
                    beforeTravelOriginMap.put(history.getOriginId(), history);
                }
            });
        }

        final List<ProjectBudgetTravelChangeHistory> afterTravelHistories = travelChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);
        if (ListUtils.isNotEmpty(afterTravelHistories)) {
            // 差旅变更记录
            List<ProjectBudgetChangeVO.ChangeHistory> travelHistories = new ArrayList<>();

            afterTravelHistories.forEach(history -> {
                final Long originId = history.getOriginId();
                final Boolean deletedFlag = history.getDeletedFlag();
                ProjectBudgetChangeVO.ChangeHistory changeHistory = buildChangeHistory(originId, deletedFlag, beforeTravelOriginMap);
                changeHistory.setChange(history);
                travelHistories.add(changeHistory);
            });

            projectBudgetChangeVO.setTravelHistories(travelHistories);
        }
    }

    public void buildFeeChangeHistory(Long headerId, ProjectBudgetChangeVO projectBudgetChangeVO) {
        final List<ProjectBudgetFeeChangeHistory> beforeFeeHistories = feeChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.HISTORY, null);
        Map<Long, ProjectBudgetFeeChangeHistoryVO> beforeFeeOriginMap = new HashMap<>();
        if (ListUtils.isNotEmpty(beforeFeeHistories)) {
            beforeFeeHistories.forEach(history -> {
                if (history.getOriginId() != null) {
                    final ProjectBudgetFeeChangeHistoryVO projectBudgetFeeChangeHistoryVO =
                            BeanConverter.copyProperties(history, ProjectBudgetFeeChangeHistoryVO.class);
                    if (projectBudgetFeeChangeHistoryVO.getFeeItemId() != null) {
                        final FeeItemDto feeItem = CacheDataUtils.findFeeItemById(projectBudgetFeeChangeHistoryVO.getFeeItemId());
                        if (feeItem != null) {
                            projectBudgetFeeChangeHistoryVO.setFeeItemName(feeItem.getFeeTypeName());
                        }
                    }
                    beforeFeeOriginMap.put(history.getOriginId(), projectBudgetFeeChangeHistoryVO);
                }
            });
        }

        final List<ProjectBudgetFeeChangeHistory> afterFeeHistories = feeChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);
        if (ListUtils.isNotEmpty(afterFeeHistories)) {
            // 项目费用变更记录
            List<ProjectBudgetChangeVO.ChangeHistory> feeHistories = new ArrayList<>();

            afterFeeHistories.forEach(history -> {
                final Long originId = history.getOriginId();
                final Boolean deletedFlag = history.getDeletedFlag();
                ProjectBudgetChangeVO.ChangeHistory changeHistory = buildChangeHistory(originId, deletedFlag, beforeFeeOriginMap);

                final ProjectBudgetFeeChangeHistoryVO projectBudgetFeeChangeHistoryVO =
                        BeanConverter.copyProperties(history, ProjectBudgetFeeChangeHistoryVO.class);
                if (projectBudgetFeeChangeHistoryVO.getFeeItemId() != null) {
                    final FeeItemDto feeItem = CacheDataUtils.findFeeItemById(projectBudgetFeeChangeHistoryVO.getFeeItemId());
                    if (feeItem != null) {
                        projectBudgetFeeChangeHistoryVO.setFeeItemName(feeItem.getFeeTypeName());
                    }
                }
                changeHistory.setChange(projectBudgetFeeChangeHistoryVO);
                feeHistories.add(changeHistory);
            });

            projectBudgetChangeVO.setFeeHistories(feeHistories);
        }
    }

    public void buildAssetChangeHistory(Long headerId, ProjectBudgetChangeVO projectBudgetChangeVO) {
        final List<ProjectBudgetAssetChangeHistory> beforeAssetHistories = projectBudgetAssetChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.HISTORY, null);
        Map<Long, ProjectBudgetAssetChangeHistory> beforeAssetOriginMap = new HashMap<>();
        if (ListUtils.isNotEmpty(beforeAssetHistories)) {
            beforeAssetHistories.forEach(history -> {
                if (history.getOriginId() != null) {
                    beforeAssetOriginMap.put(history.getOriginId(), history);
                }
            });
        }

        final List<ProjectBudgetAssetChangeHistory> afterAssetHistories = projectBudgetAssetChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);
        if (ListUtils.isNotEmpty(afterAssetHistories)) {
            // 资产预算变更记录
            List<ProjectBudgetChangeVO.ChangeHistory> assetHistories = new ArrayList<>();

            afterAssetHistories.forEach(history -> {
                final Long originId = history.getOriginId();
                final Boolean deletedFlag = history.getDeletedFlag();
                ProjectBudgetChangeVO.ChangeHistory changeHistory = buildChangeHistory(originId, deletedFlag, beforeAssetOriginMap);
                changeHistory.setChange(history);
                assetHistories.add(changeHistory);
            });

            projectBudgetChangeVO.setAssetHistories(assetHistories);
        }
    }

    /**
     * 构建对象
     *
     * @param originId
     * @param deletedFlag
     * @param beforeOriginMap
     * @return
     */
    private ProjectBudgetChangeVO.ChangeHistory buildChangeHistory(Long originId, Boolean deletedFlag, Map beforeOriginMap) {
        ProjectBudgetChangeVO.ChangeHistory changeHistory = new ProjectBudgetChangeVO.ChangeHistory();

        if (originId != null) {
            // 删除
            if (deletedFlag) {
                changeHistory.setType(ChangeType.DEL.code());
                // 更新
            } else {
                changeHistory.setHistory(beforeOriginMap.get(originId));
                changeHistory.setType(ChangeType.UPDATE.code());
            }
        } else {
            // 新增
            changeHistory.setType(ChangeType.ADD.code());
        }

        return changeHistory;
    }

    public void buildTargetChangeHistory(Long headerId, ProjectBudgetChangeVO projectBudgetChangeVO) {
        ProjectBudgetTargetChangeHistoryExample example = new ProjectBudgetTargetChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headerId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectBudgetTargetChangeHistory> budgetTargetChangeHistories = projectBudgetTargetChangeHistoryMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(budgetTargetChangeHistories)) {
            // 目标成本变更记录
            ProjectBudgetChangeVO.ChangeHistory changeHistory = new ProjectBudgetChangeVO.ChangeHistory();

            budgetTargetChangeHistories.forEach(targetChangeHistory -> {
                if (Objects.equals(HistoryType.CHANGE.getCode(), targetChangeHistory.getHistoryType())) {
                    changeHistory.setChange(targetChangeHistory);
                } else if (Objects.equals(HistoryType.HISTORY.getCode(), targetChangeHistory.getHistoryType())) {
                    changeHistory.setHistory(targetChangeHistory);
                }
                changeHistory.setType(ChangeType.UPDATE.code());
            });

            projectBudgetChangeVO.setBudgetTargetHistory(changeHistory);
        }
    }

    public void buildBusinessHistory(Long headerId, ProjectBudgetChangeVO projectBudgetChangeVO) {
        ProjectBusinessRsChangeHistoryExample businessExample = new ProjectBusinessRsChangeHistoryExample();
        businessExample.createCriteria().andHeaderIdEqualTo(headerId)
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        projectBudgetChangeVO.setProjectBusinessRsDtoList(projectBusinessRsChangeHistoryMapper.selectByExample(businessExample));
    }
}
