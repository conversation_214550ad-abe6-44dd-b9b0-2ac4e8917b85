package com.midea.pam.ctc.contract.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.FeeItemDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.dto.TaxInfoDto;
import com.midea.pam.common.basedata.dto.UnitDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.entity.ContractIndustry;
import com.midea.pam.common.basedata.entity.ContractSigningCenter;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.FeeItem;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrgUnit;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UnitOuRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.crm.dto.ContractVo;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.crm.dto.ProjectSummaryBudget;
import com.midea.pam.common.crm.dto.QuotationDetailDto;
import com.midea.pam.common.crm.dto.QuotationDto;
import com.midea.pam.common.crm.entity.Customer;
import com.midea.pam.common.ctc.dto.CheckResultDetailDTO;
import com.midea.pam.common.ctc.dto.CheckResultHeaderDTO;
import com.midea.pam.common.ctc.dto.CheckSceneInfoDTO;
import com.midea.pam.common.ctc.dto.ContractChangewayDTO;
import com.midea.pam.common.ctc.dto.ContractDTO;
import com.midea.pam.common.ctc.dto.ContractHisDTO;
import com.midea.pam.common.ctc.dto.ContractInventoryDTO;
import com.midea.pam.common.ctc.dto.ContractInventoryHisDTO;
import com.midea.pam.common.ctc.dto.ContractProductDTO;
import com.midea.pam.common.ctc.dto.EamPurchaseInfoDto;
import com.midea.pam.common.ctc.dto.GlegalContractDto;
import com.midea.pam.common.ctc.dto.InvoicePlanDTO;
import com.midea.pam.common.ctc.dto.InvoicePlanDetailDto;
import com.midea.pam.common.ctc.dto.InvoicePlanDetailHisDTO;
import com.midea.pam.common.ctc.dto.InvoicePlanHisDTO;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.ctc.dto.MilepostTemplateStageDto;
import com.midea.pam.common.ctc.dto.MilepostTemplateStageGroupDto;
import com.midea.pam.common.ctc.dto.ProjectBusinessRsDto;
import com.midea.pam.common.ctc.dto.ProjectContractRsDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.ProjectProfitDto;
import com.midea.pam.common.ctc.dto.ProjectProfitHisDTO;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.ctc.dto.ReceiptInvoiceRelationHisDTO;
import com.midea.pam.common.ctc.dto.ReceiptPlanDTO;
import com.midea.pam.common.ctc.dto.ReceiptPlanDetailDTO;
import com.midea.pam.common.ctc.dto.ReceiptPlanDetailHisDTO;
import com.midea.pam.common.ctc.dto.ReceiptPlanHisDTO;
import com.midea.pam.common.ctc.entity.CheckResultHeader;
import com.midea.pam.common.ctc.entity.CheckResultHeaderExample;
import com.midea.pam.common.ctc.entity.CheckSceneInfo;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.ContractChangeway;
import com.midea.pam.common.ctc.entity.ContractExample;
import com.midea.pam.common.ctc.entity.ContractHis;
import com.midea.pam.common.ctc.entity.ContractHisExample;
import com.midea.pam.common.ctc.entity.ContractInventoryExample;
import com.midea.pam.common.ctc.entity.ContractInventoryHis;
import com.midea.pam.common.ctc.entity.ContractOrigin;
import com.midea.pam.common.ctc.entity.ContractOriginExample;
import com.midea.pam.common.ctc.entity.ContractProduct;
import com.midea.pam.common.ctc.entity.ContractProductCost;
import com.midea.pam.common.ctc.entity.ContractProductCostHis;
import com.midea.pam.common.ctc.entity.ContractProductExample;
import com.midea.pam.common.ctc.entity.ContractProductHis;
import com.midea.pam.common.ctc.entity.ContractProductHisExample;
import com.midea.pam.common.ctc.entity.EamPurchaseInfo;
import com.midea.pam.common.ctc.entity.EamPurchaseInfoExample;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetails;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetailsExample;
import com.midea.pam.common.ctc.entity.InvoiceApplyHeader;
import com.midea.pam.common.ctc.entity.InvoiceApplyHeaderExample;
import com.midea.pam.common.ctc.entity.InvoicePlan;
import com.midea.pam.common.ctc.entity.InvoicePlanDetail;
import com.midea.pam.common.ctc.entity.InvoicePlanDetailExample;
import com.midea.pam.common.ctc.entity.InvoicePlanDetailHis;
import com.midea.pam.common.ctc.entity.InvoicePlanDetailHisExample;
import com.midea.pam.common.ctc.entity.InvoicePlanExample;
import com.midea.pam.common.ctc.entity.InvoicePlanHis;
import com.midea.pam.common.ctc.entity.InvoicePlanHisExample;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.InvoiceReceivableExample;
import com.midea.pam.common.ctc.entity.MilepostTemplateStage;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBusinessRs;
import com.midea.pam.common.ctc.entity.ProjectBusinessRsExample;
import com.midea.pam.common.ctc.entity.ProjectContractRs;
import com.midea.pam.common.ctc.entity.ProjectContractRsChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectContractRsChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectContractRsExample;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlanExample;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectMilepostExample;
import com.midea.pam.common.ctc.entity.ProjectMilepostGroup;
import com.midea.pam.common.ctc.entity.ProjectMilepostGroupExample;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectProfitExample;
import com.midea.pam.common.ctc.entity.ProjectProfitHis;
import com.midea.pam.common.ctc.entity.ProjectProfitHisExample;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ProjectTypeExample;
import com.midea.pam.common.ctc.entity.ReceiptClaimContractRel;
import com.midea.pam.common.ctc.entity.ReceiptClaimContractRelExample;
import com.midea.pam.common.ctc.entity.ReceiptInvoiceRelationHis;
import com.midea.pam.common.ctc.entity.ReceiptInvoiceRelationHisExample;
import com.midea.pam.common.ctc.entity.ReceiptPlan;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetail;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailExample;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailHis;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailHisExample;
import com.midea.pam.common.ctc.entity.ReceiptPlanExample;
import com.midea.pam.common.ctc.entity.ReceiptPlanHis;
import com.midea.pam.common.ctc.entity.ReceiptPlanHisExample;
import com.midea.pam.common.ctc.entity.RefundApply;
import com.midea.pam.common.ctc.entity.RefundApplyDetail;
import com.midea.pam.common.ctc.entity.RefundApplyDetailExample;
import com.midea.pam.common.ctc.entity.RevenueCostOrder;
import com.midea.pam.common.ctc.entity.RevenueCostOrderExample;
import com.midea.pam.common.ctc.excelVo.TemplateInvoicePlanExcelVO;
import com.midea.pam.common.ctc.excelVo.TemplateReceiptPlanExcelVO;
import com.midea.pam.common.ctc.excelVo.TemplateSalesContractExcelVO;
import com.midea.pam.common.ctc.excelVo.TemplateSalesSubContractExcelVO;
import com.midea.pam.common.ctc.vo.ContractFinanceVO;
import com.midea.pam.common.ctc.vo.ContractVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ContractChangewayStatus;
import com.midea.pam.common.enums.ContractStatus;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.GlegalContractEnums;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.IncomePoint;
import com.midea.pam.common.enums.InvoicePlanInvoiceType;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.RefundStatus;
import com.midea.pam.common.enums.RevenueCostOrderTypeEnums;
import com.midea.pam.common.enums.WorkflowOperationType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FormInstanceEvent;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.contract.service.ContractHisService;
import com.midea.pam.ctc.contract.service.ContractProductCostService;
import com.midea.pam.ctc.contract.service.ContractService;
import com.midea.pam.ctc.contract.service.InvoicePlanService;
import com.midea.pam.ctc.contract.service.LegalContractService;
import com.midea.pam.ctc.contract.service.ReceiptInvoiceRelationService;
import com.midea.pam.ctc.contract.service.ReceiptPlanService;
import com.midea.pam.ctc.contract.service.helper.ContractHelper;
import com.midea.pam.ctc.contract.service.helper.ContractModifyCheck;
import com.midea.pam.ctc.eam.service.service.EamPurchaseInfoService;
import com.midea.pam.ctc.mapper.CheckResultHeaderExtMapper;
import com.midea.pam.ctc.mapper.CheckResultHeaderMapper;
import com.midea.pam.ctc.mapper.ContractChangewayMapper;
import com.midea.pam.ctc.mapper.ContractExtMapper;
import com.midea.pam.ctc.mapper.ContractHisExtMapper;
import com.midea.pam.ctc.mapper.ContractHisMapper;
import com.midea.pam.ctc.mapper.ContractInventoryExtMapper;
import com.midea.pam.ctc.mapper.ContractInventoryHisMapper;
import com.midea.pam.ctc.mapper.ContractInventoryMapper;
import com.midea.pam.ctc.mapper.ContractMapper;
import com.midea.pam.ctc.mapper.ContractOriginMapper;
import com.midea.pam.ctc.mapper.ContractProductCostHisMapper;
import com.midea.pam.ctc.mapper.ContractProductCostMapper;
import com.midea.pam.ctc.mapper.ContractProductExtMapper;
import com.midea.pam.ctc.mapper.ContractProductHisMapper;
import com.midea.pam.ctc.mapper.ContractProductMapper;
import com.midea.pam.ctc.mapper.GlegalFileInfoExtMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailsMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyHeaderMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailHisMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailMapper;
import com.midea.pam.ctc.mapper.InvoicePlanExtMapper;
import com.midea.pam.ctc.mapper.InvoicePlanHisMapper;
import com.midea.pam.ctc.mapper.InvoicePlanMapper;
import com.midea.pam.ctc.mapper.InvoiceReceivableMapper;
import com.midea.pam.ctc.mapper.MilepostTemplateStageGroupMapper;
import com.midea.pam.ctc.mapper.OrganizationCustomDictExtMapper;
import com.midea.pam.ctc.mapper.ProjectBusinessRsMapper;
import com.midea.pam.ctc.mapper.ProjectContractRsChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectContractRsMapper;
import com.midea.pam.ctc.mapper.ProjectHistoryHeaderMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostGroupMapper;
import com.midea.pam.ctc.mapper.ProjectProfitHisMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.RdmResourcePlanExtMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimContractRelMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimExtMapper;
import com.midea.pam.ctc.mapper.ReceiptInvoiceRelationHisExtMapper;
import com.midea.pam.ctc.mapper.ReceiptInvoiceRelationHisMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailHisMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanExtMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanHisMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanMapper;
import com.midea.pam.ctc.mapper.RefundApplyDetailMapper;
import com.midea.pam.ctc.mapper.RefundApplyMapper;
import com.midea.pam.ctc.mapper.RevenueCostOrderMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CheckResultService;
import com.midea.pam.ctc.service.CheckSceneInfoService;
import com.midea.pam.ctc.service.CrmExtService;
import com.midea.pam.ctc.service.MilepostTemplateStageService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectContractRsService;
import com.midea.pam.ctc.service.ProjectIncomeCostPlanService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.service.RevenueCostOrderService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.support.id.IdGenerator;
import com.midea.pam.support.id.IdGenerators;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * <AUTHOR>
 * @date 2019-4-24
 * @description 合同操作
 */
public class ContractServiceImpl implements ContractService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final Long NOT_EXIST_ID = -1L;

    @Resource
    private IdGenerators idGenerators;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private ContractProductMapper contractProductMapper;
    @Resource
    private ProjectIncomeCostPlanMapper projectIncomeCostPlanMapper;
    @Resource
    private ContractProductCostMapper contractProductCostMapper;
    @Resource
    private ContractProductExtMapper contractProductExtMapper;
    @Resource
    private ContractExtMapper contractExtMapper;
    @Resource
    private InvoicePlanService invoicePlanService;
    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;
    @Resource
    private InvoicePlanMapper invoicePlanMapper;
    @Resource
    private InvoicePlanExtMapper invoicePlanExtMapper;
    @Resource
    private ReceiptPlanService receiptPlanService;
    @Resource
    private ProjectTypeService projectTypeService;
    @Resource
    private ProjectContractRsService projectContractRsService;
    @Resource
    private ProjectService projectService;
    @Resource
    private LegalContractService legalContractService;
    @Resource
    private ContractHelper contractHelper;
    @Resource
    private ContractModifyCheck contractModifyCheck;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ContractProductCostService contractProductCostService;
    @Resource
    private ReceiptInvoiceRelationService receiptInvoiceRelationService;
    @Resource
    private ContractHisMapper contractHisMapper;
    @Resource
    private ContractProductHisMapper contractProductHisMapper;
    @Resource
    private ContractProductCostHisMapper contractProductCostHisMapper;
    @Resource
    private ReceiptPlanHisMapper receiptPlanHisMapper;
    @Resource
    private ReceiptPlanDetailHisMapper receiptPlanDetailHisMapper;
    @Resource
    private InvoicePlanHisMapper invoicePlanHisMapper;
    @Resource
    private InvoicePlanDetailHisMapper invoicePlanDetailHisMapper;
    @Resource
    private ContractChangewayMapper contractChangewayMapper;
    @Resource
    private ReceiptInvoiceRelationHisMapper receiptInvoiceRelationHisMapper;
    @Resource
    private ReceiptInvoiceRelationHisExtMapper receiptInvoiceRelationHisExtMapper;
    @Resource
    private ProjectContractRsMapper projectContractRsMapper;
    @Resource
    private InvoiceApplyDetailsMapper invoiceApplyDetailsMapper;
    @Resource
    private InvoiceApplyHeaderMapper invoiceApplyHeaderMapper;
    @Resource
    private ReceiptClaimContractRelMapper receiptClaimContractRelMapper;
    @Resource
    private ProjectProfitHisMapper projectProfitHisMapper;
    @Resource
    private ProjectProfitService projectProfitService;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Autowired
    private BasedataExtService basedataExtService;
    @Resource
    private ProjectIncomeCostPlanService projectIncomeCostPlanService;
    @Resource
    private RevenueCostOrderService revenueCostOrderService;
    @Resource
    private ContractHisService contractHisService;
    @Resource
    private MilepostTemplateStageService milepostTemplateStageService;
    @Resource
    private RevenueCostOrderMapper revenueCostOrderMapper;
    @Resource
    private ReceiptPlanMapper receiptPlanMapper;
    @Resource
    private ReceiptPlanExtMapper receiptPlanExtMapper;
    @Resource
    private ReceiptPlanDetailMapper receiptPlanDetailMapper;
    @Resource
    private ProjectTypeMapper projectTypeMapper;
    @Resource
    private ContractHisExtMapper contractHisExtMapper;
    @Resource
    private EamPurchaseInfoService eamPurchaseInfoService;
    @Resource
    private RdmResourcePlanExtMapper rdmResourcePlanExtMapper;
    @Resource
    private InvoiceReceivableMapper invoiceReceivableMapper;
    @Resource
    private ReceiptClaimExtMapper receiptClaimExtMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private RefundApplyMapper refundApplyMapper;
    @Resource
    private RefundApplyDetailMapper refundApplyDetailMapper;
    @Resource
    private CrmExtService crmExtService;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private GlegalFileInfoExtMapper glegalFileInfoExtMapper;
    @Resource
    private ProjectHistoryHeaderMapper projectHistoryHeaderMapper;
    @Resource
    private ProjectContractRsChangeHistoryMapper projectContractRsChangeHistoryMapper;
    @Resource
    private ProjectMilepostGroupMapper projectMilepostGroupMapper;
    @Resource
    private MilepostTemplateStageGroupMapper milepostTemplateStageGroupMapper;
    @Resource
    private ProjectBusinessRsMapper projectBusinessRsMapper;
    @Resource
    private ContractInventoryMapper contractInventoryMapper;
    @Resource
    private ContractInventoryHisMapper contractInventoryHisMapper;
    @Resource
    private ContractInventoryExtMapper contractInventoryExtMapper;
    @Resource
    private ContractOriginMapper contractOriginMapper;

    @Resource
    private CheckResultHeaderMapper checkResultHeaderMapper;

    @Resource
    private CheckSceneInfoService checkSceneInfoService;

    @Resource
    private CheckResultHeaderExtMapper checkResultHeaderExtMapper;

    @Resource
    private CheckResultService checkResultService;

    @Resource
    private OrganizationCustomDictExtMapper organizationCustomDictExtMapper;


    // 变更类型 1->基础信息变更 2->服务内容变更 3->开票回款计划变更
    private final static Integer CHANGE_TYPE_ONE = 1;
    private final static Integer CHANGE_TYPE_TWO = 2;
    private final static Integer CHANGE_TYPE_THREE = 3;

    // 服务内容变更时子合同变更类型 1->子合同新增 2->子合同删除
    private final static Integer OPERATION_TYPE_ONE = 1;
    private final static Integer OPERATION_TYPE_TWO = 2;
    private final static Integer OPERATION_TYPE_THREE = 3;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractVO saveBaseInfo(ContractDTO contractDTO) {
        checkBaseInfoParam(contractDTO);
        return baseInfoTemporary(contractDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractVO splittingContract(ContractDTO contractDTO) {
        splittingContractCheckParam(contractDTO);

        final Contract localContract = this.findById(contractDTO.getId());
        if (localContract.getLegalContractNum() != null) {
            final List<ContractProductDTO> cpList = contractDTO.getContractProducts();
            // 报价单总额
            BigDecimal totalAmount = BigDecimal.ZERO;
            // 报价单成本总额
            BigDecimal totalCost = BigDecimal.ZERO;
            for (ContractProductDTO contractProductDTO : cpList) {
                final BigDecimal amount = contractProductDTO.getAmount();
                final BigDecimal cost = contractProductDTO.getCost();
                totalAmount = totalAmount.add(amount);
//                totalCost = totalCost.add(cost);
            }

            // 如果是只关联了商机，且没关联法务的情况，则允许在拆分总额不一致的情况下提交，提交时更新合同总额及总成本
            if (totalAmount.compareTo(localContract.getAmount()) != 0) {
                localContract.setAmount(totalAmount);
//                localContract.setCost(totalCost);

                this.update(localContract);

                logger.info("合同：{}，拆分总额不一致,更新合同总额，amount：{} to {}",
                        localContract.getName(), localContract.getAmount(), totalAmount, localContract.getCost(), totalCost);
            }
        }

        splittingContractTemporary(contractDTO);
        return this.findDetailById(contractDTO.getId());
    }

    /**
     * 拆分合同参数校验
     *
     * @param contractDTO 合同信息
     */
    private void splittingContractCheckParam(ContractDTO contractDTO) {
        final List<ContractDTO> childrenContracts = contractDTO.getChildrenContracts();
        final Long ouId = contractDTO.getOuId();
        final String ouName = contractDTO.getOuName();
        if (CollectionUtils.isEmpty(childrenContracts)) {
            throw new MipException("子合同不能为空");
        }
        // 1.当关联的采购申请的供应商跟主合同的OU不一致时，需要提醒项目经理重新选择；2.主合同金额（含税）必须等于关联采购申请总额。
        eamPurchaseInfoService.eamPurchaseDataCheck(contractDTO, null);

        childrenContracts.forEach(contract -> {
            // 必填校验
            Assert.notNull(contract.getName(), "项目名称不能为空");
            Assert.notNull(contract.getProfitDepartmentId(), "利润部门不能为空");
            Assert.notNull(contract.getStartTime(), "生效日期不能为空");
            Assert.notNull(contract.getManager(), "项目经理不能为空");

            final List<ContractProductDTO> contractProducts = contract.getContractProducts();
            // 合同产品必填校验
            if (CollectionUtils.isEmpty(contractProducts)) {
                Assert.notNull(contract.getName(), "产品信息不能为空");
            }

            for (ContractProductDTO contractProduct : contractProducts) {
                Assert.notNull(contractProduct.getProductTypeName(), "服务类型不能为空");
                Assert.notNull(contractProduct.getAmount(), "金额不能为空");
//                Assert.notNull(contractProduct.getCost(), "预估成本不能为空");
            }
        });

        // 合理性校验

//        final List<ContractProductDTO> cpList = contractDTO.getContractProducts();
//        // 报价单总额
//        BigDecimal totalAmount = BigDecimal.ZERO;
//        for (ContractProductDTO contractProductDTO : cpList) {
//            final BigDecimal amount = contractProductDTO.getAmount();
//            totalAmount = totalAmount.add(amount);
//        }
//
//        BigDecimal totalAllocatedAmount = BigDecimal.ZERO;
//        // 正常情况下，已拆分金额不能超过合同总额
//        for (ContractProductDTO contractProductDTO : cpList) {
//            final BigDecimal allocatedAmount = contractProductDTO.getAllocatedAmount();
//            totalAllocatedAmount = totalAllocatedAmount.add(allocatedAmount);
//            if (totalAllocatedAmount.compareTo(totalAmount) > 0) {
//                throw new MipException("已拆分金额不能超过合同总额");
//            }
//        }

        // 校验成本
        BigDecimal costTotalAmount = BigDecimal.ZERO;

        for (ContractDTO childrenContract : childrenContracts) {
            final BigDecimal cost = childrenContract.getCost() == null ? BigDecimal.ZERO : childrenContract.getCost();
            costTotalAmount = costTotalAmount.add(cost);
        }

        final BigDecimal mainCost = contractDTO.getCost();
        if (mainCost != null && mainCost.compareTo(costTotalAmount) < 0) {
            throw new MipException("成本拆分不得超过合同预估成本");
        }

        final Contract localContract = findById(contractDTO.getId());

        // 生效日期不得超过合同有效期
        // 失效日期不得超过合同有效期
        childrenContracts.forEach(contract -> {
            final Date startTime = contract.getStartTime();
            final Date endTime = contract.getEndTime();
            if (startTime != null && localContract.getStartTime() != null
                    && startTime.compareTo(localContract.getStartTime()) < 0) {
                throw new MipException("生效日期不得超过合同有效期");
            }

            if (endTime != null && localContract.getEndTime() != null
                    && endTime.compareTo(localContract.getEndTime()) > 0) {
                throw new MipException("失效日期不得超过合同有效期");
            }

        });


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractVO makeInvoicePlan(ContractDTO contractDTO) {
        // 开票计划数据校验
        final List<ContractDTO> childrenContracts = contractDTO.getChildrenContracts();
        if (CollectionUtils.isNotEmpty(childrenContracts)) {
            for (ContractDTO childrenContract : childrenContracts) {
                invoicePlanParamCheck(childrenContract.getInvoicePlans());
            }
        }
        return this.makeInvoicePlanTemporary(contractDTO);
    }

    /**
     * 开票计划校验
     *
     * @param invoicePlans
     */
    private void invoicePlanParamCheck(List<InvoicePlanDTO> invoicePlans) {
        if (CollectionUtils.isNotEmpty(invoicePlans)) {
            invoicePlans.forEach(invoicePlanDTO -> {
                Assert.notNull(invoicePlanDTO.getType(), "发票类型不能为空");
                Assert.notNull(invoicePlanDTO.getContractId(), "关联合同不能为空");

                final List<InvoicePlanDetailDto> invoicePlanDetails = invoicePlanDTO.getInvoicePlanDetails();
                if (CollectionUtils.isEmpty(invoicePlanDetails)) {
                    throw new MipException("开票计划详情不能为空");
                }

                // 子合同计划金额
                final BigDecimal amount = invoicePlanDTO.getAmount();

                // 开票计划总额
                BigDecimal totalInvoicePlanAmount = BigDecimal.ZERO;
                for (InvoicePlanDetail invoicePlanDetail : invoicePlanDetails) {
                    Assert.notNull(invoicePlanDetail.getDate(), "计划开票日期不能为空");
                    Assert.notNull(invoicePlanDetail.getAmount(), "含税金额不能为空");

                    // 金额只能为正数
                    final BigDecimal invoicePlanAmount = invoicePlanDetail.getAmount();
                    if (BigDecimal.ZERO.compareTo(invoicePlanAmount) > 0) {
                        throw new MipException("金额只能为正数");
                    }
                    totalInvoicePlanAmount = totalInvoicePlanAmount.add(invoicePlanAmount);
                }

                if (totalInvoicePlanAmount.compareTo(amount) != 0) {
                    throw new MipException("开票计划总额需与合同金额一致");
                }
            });
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractVO makeReceiptPlan(ContractDTO contractDTO) {
        // 回款计划
        final List<ReceiptPlanDTO> receiptPlans = contractDTO.getReceiptPlans();
        receiptPlanParamCheck(receiptPlans);

        return this.makeReceiptPlanTemporary(contractDTO);
    }

    /**
     * 回款计划校验
     *
     * @param receiptPlans
     */
    private void receiptPlanParamCheck(List<ReceiptPlanDTO> receiptPlans) {
        if (CollectionUtils.isNotEmpty(receiptPlans)) {
            receiptPlans.forEach(receiptPlanDTO -> {
                Assert.notNull(receiptPlanDTO.getContractId(), "关联合同不能为空");
                Assert.notNull(receiptPlanDTO.getProductTypeId(), "服务类型不能为空");

                final List<ReceiptPlanDetailDTO> receiptPlanDetails = receiptPlanDTO.getReceiptPlanDetails();
                if (CollectionUtils.isEmpty(receiptPlanDetails)) {
                    throw new MipException("回款计划详情不能为空");
                }
                // 子合同计划回款金额
                final BigDecimal amount = receiptPlanDTO.getAmount();

                // 开票计划总额
                BigDecimal totalReceiptPlanAmount = BigDecimal.ZERO;
                for (ReceiptPlanDetail receiptPlanDetail : receiptPlanDetails) {
                    Assert.notNull(receiptPlanDetail.getDate(), "计划回款日期不能为空");
                    Assert.notNull(receiptPlanDetail.getAmount(), "回款金额不能为空");

                    // 金额只能为正数
                    final BigDecimal receiptPlanAmount = receiptPlanDetail.getAmount();
                    if (BigDecimal.ZERO.compareTo(receiptPlanAmount) > 0) {
                        throw new MipException("金额只能为正数");
                    }

                    totalReceiptPlanAmount = totalReceiptPlanAmount.add(receiptPlanAmount);
                }

                if (totalReceiptPlanAmount.compareTo(amount) != 0) {
                    throw new MipException("回款计划总额需与合同金额一致");
                }
            });
        }
    }

    //直接从数据库中抽取前缀
    private String getSeqPerfixFromDB(Long ouId) {
        Map<String, Object> map = new HashMap<>();
        map.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/cacheDataOuUnitExt/getSeqPerfixByOuId", map);
        String seqPerfix = restTemplate.getForObject(url, String.class);
        if (seqPerfix == null || "".equals(seqPerfix)) {
            logger.error("error--fatal-->PengBo:合同前缀丢失");
        }
        return seqPerfix;
    }

    //消除前缀丢失原因；补全redis缓存
    private void referenceCacheForUnitOu(Long ouId) {
        Map<String, Object> map = new HashMap<>();
        map.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/cacheDataOuUnitExt/cache", map);
        String res = restTemplate.getForObject(url, String.class);
    }

    private Long getCompanyIdFromCache(ContractDTO contractDTO) {
        Long companyId = null;
        UnitOuRel unitOuRel = CacheDataUtils.findUnitOuRelByOuId(contractDTO.getOuId());
        if (unitOuRel != null && unitOuRel.getUnitId() != null) {
            Long unitId = unitOuRel.getUnitId();
            //PengBo;根据unit_id(虚拟部门id)获取此虚拟部门的顶级公司id(获取顶级公司id方式如下)
            companyId = CacheDataUtils.getTopUnitIdByUnitId(unitId);//公司id
        }
        return companyId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractVO baseInfoTemporary(ContractDTO contractDTO) {
        /**
         UnitOuRel unitOuRel = CacheDataUtils.findUnitOuRelByOuId(contractDTO.getOuId());
         if (unitOuRel != null && unitOuRel.getUnitId() != null) {
         Long unitId = unitOuRel.getUnitId();
         //PengBo;根据unit_id(虚拟部门id)获取此虚拟部门的顶级公司id(获取顶级公司id方式如下)
         companyId = CacheDataUtils.getTopUnitIdByUnitId(unitId);//公司id
         }*/
        if (contractDTO.getOuId() == null) {
            logger.error("error--Main：--前缀”即将“丢失了！！！(PengBo)--原因：合同的ouId不存在,会影响后面的查找");
        }
        Long companyId = getCompanyIdFromCache(contractDTO);
        if (companyId == null) {
            logger.error("error：--前缀“正在”丢失了！！！(PengBo)--原因：companyId==null，后面根本不会获取到前缀");
            //刷新一下缓存；
            referenceCacheForUnitOu(contractDTO.getOuId());
            //刷新缓存后，重新获取。
            companyId = getCompanyIdFromCache(contractDTO);
        }
        String seqPerfix = basedataExtService.getUnitSeqPerfix(companyId);
        if (seqPerfix == null || "".equals(seqPerfix)) {
            seqPerfix = getSeqPerfixFromDB(contractDTO.getOuId());
        } else {
            logger.info("ok!!!-->PengBo:合同前缀存在！");
            logger.info("1:contractDTO.getId()=" + contractDTO.getId());
            logger.info("2:companyId=" + companyId);
            logger.info("3:seqPerfix=" + seqPerfix);
        }

        String code = "SX";
        code = seqPerfix + code + CacheDataUtils.generateSequence(4, seqPerfix + code, DateUtil.DATE_YYMM_PATTERN);
        // 设置为草稿状态
        contractDTO.setStatus(ContractStatus.DRAFT.getCode());
        final Contract contract = contractDTO.convertToContract();
        if (!Objects.equals(contractDTO.getIsSynchronizeLegalSystemFlag(), null)) {
            contractDTO.getIsSynchronizeLegalSystemFlag().equals(Boolean.FALSE);
            contract.setIsDoubleChapterContract(Boolean.TRUE);
        }
        if (!Objects.equals(contractDTO.getCustomerType(), null)) {
            contractDTO.getCustomerType().equals(Boolean.TRUE);
            contract.setBelongArea(2);
        }
        contract.setDeletedFlag(Boolean.FALSE);
        if (contract.getId() == null) {
            contract.setCode(code);
            contract.setStartTime(contract.getStartTime() != null ? DateUtils.getShortDate(contract.getStartTime()) : null);
            contract.setEndTime(contract.getEndTime() != null ? DateUtils.getShortDate(contract.getEndTime()) : null);
            contractMapper.insertSelective(contract);
        } else {
            final List<ContractVO> childrenContracts = this.getListByParentId(contract.getId());
            if (CollectionUtils.isNotEmpty(childrenContracts)) {
                final Contract localContract = contractMapper.selectByPrimaryKey(contract.getId());
                final Long localOuId = localContract.getOuId();
                final Long localUnitId = localContract.getUnitId();
                final Long customerId = localContract.getCustomerId();
                final Long salesManager = localContract.getSalesManager();
                // 子合同继承父合同OU，客户，销售经理
                // 如果更改了OU、UNIT，则同步更新子合同
                if (contract.getUnitId() != null || contract.getOuId() != null
                        || contract.getCustomerId() != null || contract.getSalesManager() != null) {
                    if (!Objects.deepEquals(localOuId, contract.getOuId())
                            || Objects.deepEquals(localUnitId, contract.getUnitId())
                            || Objects.deepEquals(customerId, contract.getCustomerId())
                            || Objects.deepEquals(salesManager, contract.getSalesManager())) {
                        childrenContracts.forEach(childrenContract -> {
                            childrenContract.setOuId(contract.getOuId());
                            childrenContract.setUnitId(contract.getUnitId());
                            childrenContract.setCustomerId(contract.getCustomerId());
                            childrenContract.setCustomerName(contract.getCustomerName());
                            childrenContract.setCustomerCode(contract.getCustomerCode());
                            childrenContract.setSalesManager(contract.getSalesManager());
                            childrenContract.setCurrency(contract.getCurrency());
                            childrenContract.setConversionDate(contract.getConversionDate());
                            childrenContract.setConversionRate(contract.getConversionRate());
                            childrenContract.setConversionType(contract.getConversionType());
                            childrenContract.setEampurchaseId(contract.getEampurchaseId());
                            contractMapper.updateByPrimaryKey(childrenContract);
                        });
                    }
                }
            }

            final Contract localContract = findById(contract.getId());
            // 对比商机是否已经变更,若变更，则删除后续数据
            if (contract.getBusinessId() != null) {
                if (localContract.getBusinessId() == null || !Objects.equals(localContract.getBusinessId(), contract.getBusinessId())) {
                    contractProductExtMapper.deleteByContractId(contract.getId());
                    logger.info("合同：{}-{}，商机由{}->{}，移除主合同拆分成本信息",
                            contract.getId(), contract.getName(), localContract.getBusinessId(), contract.getBusinessId());
                }
            } else {
                if (localContract.getBusinessId() != null) {
                    contractProductExtMapper.deleteByContractId(contract.getId());
                    logger.info("合同：{}-{}，商机由{}->{}，移除主合同拆分成本信息",
                            contract.getId(), contract.getName(), localContract.getBusinessId(), contract.getBusinessId());
                }
            }
            if (Boolean.FALSE.equals(contractDTO.getIsSynchronizeLegalSystemFlag())) {
                localContract.setNotsyncType(contract.getNotsyncType());
                localContract.setNotsyncReason(contract.getNotsyncReason());
            } else {
                localContract.setNotsyncType(null);
                localContract.setNotsyncReason(null);
            }
            localContract.setWhetherCustomerConfirm(contract.getWhetherCustomerConfirm());
            // 需要将有值更新为无值
            localContract.setName(contract.getName());
            localContract.setFrameFlag(contract.getFrameFlag());
            localContract.setLegalContractNum(contract.getLegalContractNum());
            localContract.setBusinessId(contract.getBusinessId());
            localContract.setCustomerId(contract.getCustomerId());
            localContract.setCustomerName(contract.getCustomerName());
            localContract.setCustomerCode(contract.getCustomerCode());
            localContract.setStartTime(contract.getStartTime() != null ? DateUtils.getShortDate(contract.getStartTime()) : null);
            localContract.setEndTime(contract.getEndTime() != null ? DateUtils.getShortDate(contract.getEndTime()) : null);
            localContract.setOriginalContractAnnex(contract.getOriginalContractAnnex());
            localContract.setOuId(contract.getOuId());
            localContract.setAmount(contract.getAmount());
            localContract.setCost(contract.getCost());
            localContract.setUnitId(contract.getUnitId());
            localContract.setSalesManager(contract.getSalesManager());
            localContract.setAnnex(contract.getAnnex());
            localContract.setRemark(contract.getRemark());
            localContract.setManager(contract.getManager());
            localContract.setProfitDepartmentId(contract.getProfitDepartmentId());
            localContract.setBusinessTypeId(contract.getBusinessTypeId());
            localContract.setParentId(contract.getParentId());
            localContract.setFrameContractId(contract.getFrameContractId());
            localContract.setCurrency(contract.getCurrency());
            localContract.setConversionDate(contract.getConversionDate());
            localContract.setConversionRate(contract.getConversionRate());
            localContract.setConversionType(contract.getConversionType());
            localContract.setPaymentMethod(contract.getPaymentMethod());
            localContract.setInvoiceType(contract.getInvoiceType());
            localContract.setClasses(contract.getClasses());
            localContract.setSealCategory(contract.getSealCategory());
            localContract.setSealAdminAccountIds(contract.getSealAdminAccountIds());
            localContract.setEampurchaseId(contract.getEampurchaseId());
            localContract.setIfWatermarking(contract.getIfWatermarking());
            localContract.setNoWatermarkingReason(contract.getNoWatermarkingReason());
            localContract.setIsSynchronizeLegalSystemFlag(contract.getIsSynchronizeLegalSystemFlag());
            //重新编辑保存/下一步 更新盖章信息等
            localContract.setIsElectronicContract(contract.getIsElectronicContract());
            localContract.setBelongArea(contract.getBelongArea());
            localContract.setOtherPhone(contract.getOtherPhone());
            localContract.setOtherName(contract.getOtherName());
            localContract.setOtherEmail(contract.getOtherEmail());
            localContract.setPublicOrPrivate(contract.getPublicOrPrivate());

            // 如果是框架合同，去除金额、成本等
            if (Objects.equals(contract.getFrameFlag(), Boolean.TRUE)) {
                localContract.setAmount(null);
                localContract.setCost(null);
                localContract.setExcludingTaxAmount(null);
                localContract.setFrameContractId(null);
            }
            // 行业
            localContract.setIndustry(contract.getIndustry());
            localContract.setSigningCenter(contract.getSigningCenter());
            update(localContract);
        }

        return this.findDetailById(contract.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractVO splittingContractTemporary(ContractDTO contractDTO) {
        final List<ContractProductDTO> contractProducts = contractDTO.getContractProducts();
        // 主合同ID
        final Long id = contractDTO.getId();
        final Contract parentContract = contractMapper.selectByPrimaryKey(id);

        if (CollectionUtils.isNotEmpty(contractProducts)) {

            // 先删除数据，再保存现有数据
            if (id != null) {
                contractProductExtMapper.deleteByContractId(id);
                contractInventoryExtMapper.logicalDeleteByParentContractId(id);
            }

            // 保存合同拆分信息，产品数据不会很多
            for (ContractProductDTO contractProductDTO : contractProducts) {
                final ContractProduct contractProduct = contractProductDTO.convertToContractProduct();
                contractProduct.setId(null);
                contractProduct.setContractId(id);
                contractProduct.setDeletedFlag(Boolean.FALSE);
                contractProductMapper.insert(contractProduct);
            }
        }

        // 主合同必须拆分至少一个子合同
        final List<ContractDTO> childrenContracts = contractDTO.getChildrenContracts();

        if (CollectionUtils.isNotEmpty(childrenContracts)) {
            final List<Contract> localChildrenContracts = lishSubContract(id);
            // 待删除子合同ID列表
            Set<Long> childrenContractIdSet = new HashSet<>();
            Map<Long, Contract> childMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(localChildrenContracts)) {
                localChildrenContracts.forEach(contract -> childrenContractIdSet.add(contract.getId()));
                localChildrenContracts.forEach(contract -> childMap.put(contract.getId(), contract));
            }

            //childrenContractIdDelList待删除的子合同和采购申请关联子合同id，childrenContractIdAddList待新增的子合同和采购申请关联子合同id
            List<Long> childrenContractIdDelList = new ArrayList<>();
            childrenContractIdDelList = localChildrenContracts.stream().map(c -> c.getId()).collect(Collectors.toList());
            Map<Long, String> childrenContractIdAddMap = new HashMap<>();

            for (ContractDTO childrenContract : childrenContracts) {
                childrenContract.setParentId(id);

                //页面有传值过来则按页面传值更新，没有就继承父合同OU、虚拟部门、客户、币种、销售经理
                if (childrenContract.getOuId() == null || childrenContract.getOuId() == 0) {
                    childrenContract.setOuId(parentContract.getOuId());
                }
                if (childrenContract.getUnitId() == null || childrenContract.getUnitId() == 0) {
                    childrenContract.setUnitId(parentContract.getUnitId());
                }
                if (childrenContract.getCustomerId() == null || childrenContract.getCustomerId() == 0) {
                    childrenContract.setCustomerId(parentContract.getCustomerId());
                }
                //需求AAIG042425,合同 客户id与客户code不一致问题
                CustomerDto customer = contractHelper.getCustomer(childrenContract.getCustomerId());
                if (customer != null) {
                    childrenContract.setCustomerCode(customer.getCrmCode());
                    childrenContract.setCustomerName(customer.getName());
                } else {
                    if (StringUtils.isEmpty(childrenContract.getCustomerCode())) {
                        childrenContract.setCustomerCode(parentContract.getCustomerCode());
                    }
                    if (StringUtils.isEmpty(childrenContract.getCustomerName())) {
                        childrenContract.setCustomerName(parentContract.getCustomerName());
                    }
                }
                if (StringUtils.isEmpty(childrenContract.getCurrency())) {
                    childrenContract.setCurrency(parentContract.getCurrency());
                }
                if (childrenContract.getSalesManager() == null || childrenContract.getSalesManager() == 0) {
                    childrenContract.setSalesManager(parentContract.getSalesManager());
                }
                final Contract contract = childrenContract.convertToContract();
                contract.setStatus(ContractStatus.DRAFT.getCode());
                contract.setFrameFlag(Boolean.FALSE);
                if (contract.getId() == null) {
                    contract.setDeletedFlag(Boolean.FALSE);
                    Long companyId = null;
                    UnitOuRel unitOuRel = CacheDataUtils.findUnitOuRelByOuId(parentContract.getOuId());
                    if (unitOuRel != null && unitOuRel.getUnitId() != null) {
                        Long unitId = unitOuRel.getUnitId();
                        companyId = CacheDataUtils.getTopUnitIdByUnitId(unitId);//公司id
                    }
                    String seqPerfix = basedataExtService.getUnitSeqPerfix(companyId);
                    String code = "SZ";
                    code = seqPerfix + code + CacheDataUtils.generateSequence(4, seqPerfix + code, DateUtil.DATE_YYMM_PATTERN);
                    contract.setCode(code);
                    contractMapper.insert(contract);
                    childrenContractIdAddMap.put(contract.getId(), contract.getEampurchaseId());
                } else {
                    // 需要更新失效时间为空的场景
                    final Contract needUpdateContract = childMap.get(contract.getId());
                    if (needUpdateContract != null) {
                        needUpdateContract.setName(contract.getName());
                        needUpdateContract.setProfitDepartmentId(contract.getProfitDepartmentId());
                        needUpdateContract.setBusinessTypeId(contract.getBusinessTypeId());
                        needUpdateContract.setCost(contract.getCost());
                        needUpdateContract.setStartTime(contract.getStartTime());
                        needUpdateContract.setEndTime(contract.getEndTime());
                        needUpdateContract.setManager(contract.getManager());
                        needUpdateContract.setAmount(contract.getAmount());
                        needUpdateContract.setExcludingTaxAmount(contract.getExcludingTaxAmount());
                        needUpdateContract.setCustomerId(contract.getCustomerId());
                        needUpdateContract.setCustomerCode(contract.getCustomerCode());
                        needUpdateContract.setCustomerName(contract.getCustomerName());
                        needUpdateContract.setEampurchaseId(contract.getEampurchaseId());
                        needUpdateContract.setBusinessId(contract.getBusinessId());
                        needUpdateContract.setProcessGrossProfitRatio(contract.getProcessGrossProfitRatio());
                        needUpdateContract.setBusinessSegment(contract.getBusinessSegment());
                        needUpdateContract.setWhetherCustomerConfirm(contract.getWhetherCustomerConfirm());
                        needUpdateContract.setNotsyncType(contract.getNotsyncType());
                        needUpdateContract.setNotsyncReason(contract.getNotsyncReason());
                        contractMapper.updateByPrimaryKey(needUpdateContract);
                        childrenContractIdAddMap.put(needUpdateContract.getId(), needUpdateContract.getEampurchaseId());
                    }

                    // 从待删除列表移除
                    childrenContractIdSet.remove(contract.getId());
                }

                // 子合同产品关系列表
                final List<ContractProductDTO> childrenContractProducts = childrenContract.getContractProducts();

                ContractProductExample contractProductExample = new ContractProductExample();
                ContractProductExample.Criteria criteria = contractProductExample.createCriteria();
                criteria.andDeletedFlagEqualTo(Boolean.FALSE);
                criteria.andContractIdEqualTo(contract.getId());

                List<ContractProduct> localContractProducts = contractProductMapper.selectByExample(contractProductExample);

                Map<Long, ContractProduct> contractProductMap = new HashMap<>();
                localContractProducts.forEach(contractProduct -> {
                    contractProductMap.put(contractProduct.getId(), contractProduct);
                });

                // 移除合同产品关系
//                contractProductExtMapper.deleteByContractId(contract.getId());

                List<Map> existsList = new ArrayList<>();
                Map<String, String> existsMap = new HashMap<>();

                if (CollectionUtils.isNotEmpty(childrenContractProducts)) {

                    // 保存合同拆分信息，产品数据不会很多
                    for (ContractProductDTO contractProductDTO : childrenContractProducts) {
                        // 收集已存在的合同、服务类型、税率

                        final ContractProduct contractProduct = contractProductDTO.convertToContractProduct();
                        contractProduct.setContractId(contract.getId());
                        if (contractProduct.getId() == null) {
                            contractProduct.setDeletedFlag(Boolean.FALSE);
                            contractProductMapper.insert(contractProduct);
                        } else {
                            contractProductMapper.updateByPrimaryKeySelective(contractProduct);
                            contractProductMap.remove(contractProduct.getId());
                        }
                    }
                }

                if (contractProductMap.size() > 0) {
                    Set<Map.Entry<Long, ContractProduct>> entries = contractProductMap.entrySet();
                    entries.forEach(entry -> {
                        Long contractProductId = entry.getKey();
                        ContractProduct updateContractProduct = new ContractProduct();
                        updateContractProduct.setId(contractProductId);
                        updateContractProduct.setDeletedFlag(Boolean.TRUE);

                        contractProductMapper.updateByPrimaryKey(updateContractProduct);

                        InvoicePlanExample invoicePlanExample = new InvoicePlanExample();
                        InvoicePlanExample.Criteria invoicePlanExampleCriteria = invoicePlanExample.createCriteria();
                        invoicePlanExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE);
                        invoicePlanExampleCriteria.andContractProductIdEqualTo(contractProductId);
                        List<InvoicePlan> invoicePlans = invoicePlanMapper.selectByExample(invoicePlanExample);

                        // 删除
                        invoicePlans.forEach(invoicePlanDTO -> {
                            invoicePlanService.deleteById(invoicePlanDTO.getId());

                            InvoicePlanDetailExample invoicePlanDetailExample = new InvoicePlanDetailExample();
                            InvoicePlanDetailExample.Criteria criteria1 = invoicePlanDetailExample.createCriteria();
                            criteria1.andDeletedFlagEqualTo(Boolean.FALSE);
                            criteria1.andPlanIdEqualTo(invoicePlanDTO.getId());
                            List<InvoicePlanDetail> invoicePlanDetails = invoicePlanDetailMapper.selectByExample(invoicePlanDetailExample);

                            // 删除开票回款关系
                            if (ListUtils.isNotEmpty(invoicePlanDetails)) {
                                invoicePlanDetails.forEach(invoicePlanDetail -> {
                                    invoicePlanDetail.setDeletedFlag(Boolean.TRUE);
                                    invoicePlanDetailMapper.updateByPrimaryKey(invoicePlanDetail);
                                    receiptInvoiceRelationService.deleteByInvoicePlanDetailId(invoicePlanDetail.getId());
                                });
                            }
                        });
                    });

                }

                // 移除合同产品成本信息
                final List<ContractProductCost> localContractProductCosts = contractProductCostService.listByContractId(contract.getId());
                if (ListUtils.isNotEmpty(localContractProductCosts)) {
                    localContractProductCosts.forEach(contractProductCost -> {
                        contractProductCost.setDeletedFlag(Boolean.TRUE);
                        contractProductCostService.update(contractProductCost);
                    });
                }

                // 新增成本信息
                final List<ContractProductCost> contractProductCosts = childrenContract.getContractProductCosts();
                if (ListUtils.isNotEmpty(contractProductCosts)) {
                    contractProductCosts.forEach(contractProductCost -> {
                        contractProductCost.setId(null);
                        contractProductCost.setContractId(contract.getId());
                        contractProductCost.setUpdateAt(null);
                        contractProductCost.setUpdateBy(null);
                        contractProductCost.setDeletedFlag(Boolean.FALSE);

                        contractProductCostService.save(contractProductCost);
                    });
                }

                // 新增产品清单
                List<ContractInventoryDTO> contractInventoryList = childrenContract.getContractInventoryList();
                if (ListUtils.isNotEmpty(contractInventoryList)) {
                    contractInventoryList.forEach(contractInventory -> {
                        contractInventory.setId(null);
                        contractInventory.setContractId(contract.getId());
                        contractInventory.setParentContractId(id);
                        contractInventory.setDeletedFlag(Optional.ofNullable(contractInventory.getDeletedFlag()).orElse(DeletedFlag.VALID.code()));
                        contractInventoryMapper.insert(contractInventory);
                    });
                }
            }

            // 维护contract_eampurchase_relation
            eamPurchaseInfoService.saveContractEamPurchaseRel(childrenContractIdDelList, childrenContractIdAddMap, Boolean.FALSE);

            // 处理删除的子合同信息
            if (childrenContractIdSet.size() > 0) {
                childrenContractIdSet.forEach(childrenContractId -> {
                    // 移除子合同
                    contractMapper.deleteByPrimaryKey(childrenContractId);
                    contractProductMapper.deleteByContractId(childrenContractId);
                    // 移除开票回款计划
                    invoicePlanService.deleteByContractId(childrenContractId);
                    receiptPlanService.deleteByContractId(childrenContractId);
                });
            }
        }

        //为防止子合同中的不含税金额出现错误，现在不含税金额做一次后台的更新
        ContractExample contractExample = new ContractExample();
        contractExample.createCriteria().andParentIdEqualTo(contractDTO.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        final List<Contract> finalChlidrenContractList = contractMapper.selectByExample(contractExample);
        if (CollectionUtils.isNotEmpty(finalChlidrenContractList)) {
            for (Contract finalChildrenContract : finalChlidrenContractList) {
                ContractProductExample contractProductExample = new ContractProductExample();
                contractProductExample.createCriteria().andContractIdEqualTo(finalChildrenContract.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                final List<ContractProduct> finalContractProductList = contractProductMapper.selectByExample(contractProductExample);
                BigDecimal totalExcludingTaxAmount = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(finalContractProductList)) {
                    for (ContractProduct contractProduct : finalContractProductList) {
                        BigDecimal excludingTaxAmount = contractProduct.getExcludingTaxAmount() == null ? BigDecimal.ZERO : contractProduct.getExcludingTaxAmount();
                        totalExcludingTaxAmount = totalExcludingTaxAmount.add(excludingTaxAmount);
                    }
                }

                finalChildrenContract.setExcludingTaxAmount(totalExcludingTaxAmount);
                contractMapper.updateByPrimaryKey(finalChildrenContract);

            }
        }
        return this.findDetailById(contractDTO.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractVO makeInvoicePlanTemporary(ContractDTO contractDTO) {
        final Long id = contractDTO.getId();

        // 子合同列表
        ContractExample contractExample = new ContractExample();
        final ContractExample.Criteria contractExampleCriteria = contractExample.createCriteria();
        contractExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE);
        contractExampleCriteria.andParentIdEqualTo(id);
        final List<Contract> localChildrenContracts = contractMapper.selectByExample(contractExample);

        Set<Long> cIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(localChildrenContracts)) {
            cIdSet = localChildrenContracts.stream().map(Contract::getId).collect(Collectors.toSet());
        }

        final List<ContractDTO> childrenContracts = contractDTO.getChildrenContracts();
        if (CollectionUtils.isNotEmpty(childrenContracts)) {

            // 开票计划先删除，再新增
            for (ContractDTO childrenContract : childrenContracts) {

                // 获取开票计划详情，处理编号问题
                final List<InvoicePlanDetail> invoicePlanDetails = invoicePlanService.listByContractId(childrenContract.getId());

                Map<Long, String> needDeleteCodeMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(invoicePlanDetails)) {
                    invoicePlanDetails.forEach(invoicePlanDetail -> {
                        needDeleteCodeMap.put(invoicePlanDetail.getId(), invoicePlanDetail.getCode());
                    });
                }

                Map<Long, String> needUpdateMap = new HashMap<>();

                Map<String, Long> thisCodeMap = new HashMap<>();

                // 删除合同所有的开票计划信息
                invoicePlanService.deleteByContractId(childrenContract.getId());

                final List<InvoicePlanDTO> invoicePlans = childrenContract.getInvoicePlans();

                if (CollectionUtils.isNotEmpty(invoicePlans)) {
                    // 保存开票计划
                    invoicePlans.forEach(invoicePlanDTO -> {
                        // 处理编号
                        final List<InvoicePlanDetailDto> details = invoicePlanDTO.getInvoicePlanDetails();
                        if (CollectionUtils.isNotEmpty(details)) {
                            details.forEach(detail -> {
                                // 待更新的数据，保留之前生成的code
                                if (detail.getId() != null) {
                                    final String code = needDeleteCodeMap.get(detail.getId());
                                    detail.setCode(code);
                                    // 重新做新增操作，保存code

                                    needDeleteCodeMap.remove(detail.getId());
                                    needUpdateMap.put(detail.getId(), detail.getCode());

                                    detail.setId(null);
                                }
                            });
                        }

                        invoicePlanDTO.setId(null);
                        invoicePlanDTO.setContractId(childrenContract.getId());
                        invoicePlanDTO.setCustomerId(childrenContract.getCustomerId());
                        invoicePlanDTO.setCustomerName(childrenContract.getCustomerName());
                        invoicePlanService.save(invoicePlanDTO);

                        // 收集更新后，开票计划详情新id
                        final List<InvoicePlanDetailDto> thisInvoicePlanDetails = invoicePlanDTO.getInvoicePlanDetails();
                        if (ListUtils.isNotEmpty(thisInvoicePlanDetails)) {
                            thisInvoicePlanDetails.forEach(invoicePlanDetail -> {
                                thisCodeMap.put(invoicePlanDetail.getCode(), invoicePlanDetail.getId());
                            });
                        }

                    });
                }

                cIdSet.remove(childrenContract.getId());

                // 删除已不存在的开票回款关系
                if (needDeleteCodeMap.size() > 0) {
                    for (Long detailId : needDeleteCodeMap.keySet()) {
                        receiptInvoiceRelationService.deleteByInvoicePlanDetailId(detailId);
                    }
                }

                // 更新开票回款关系
                final Set<Map.Entry<Long, String>> entries = needUpdateMap.entrySet();
                if (entries != null) {
                    for (Map.Entry<Long, String> entry : entries) {
                        final String code = entry.getValue();
                        final Long oldUpdateId = entry.getKey();
                        final Long newUpdateId = thisCodeMap.get(code);

                        if (newUpdateId != null) {
                            receiptInvoiceRelationService.updateInvoicePlanDetailId(newUpdateId, oldUpdateId);
                        }
                    }
                }
            }
        }

        if (cIdSet.size() > 0) {
            cIdSet.forEach(contractId -> {
                invoicePlanService.deleteByContractId(contractId);

                // 删除已不存在的开票计划详情
                final List<InvoicePlanDetail> invoicePlanDetails = invoicePlanService.listByContractId(contractId);
                if (ListUtils.isNotEmpty(invoicePlanDetails)) {
                    invoicePlanDetails.forEach(invoicePlanDetail -> {
                        receiptInvoiceRelationService.deleteByInvoicePlanDetailId(invoicePlanDetail.getId());
                    });
                }
            });
        }

        return this.findDetailById(contractDTO.getId());
    }


    private List<Contract> lishSubContract(Long contractId) {
        ContractExample contractExample = new ContractExample();
        final ContractExample.Criteria contractExampleCriteria = contractExample.createCriteria();
        contractExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE);
        contractExampleCriteria.andParentIdEqualTo(contractId);
        return contractMapper.selectByExample(contractExample);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractVO makeReceiptPlanTemporary(ContractDTO contractDTO) {
        final Long id = contractDTO.getId();

        final List<Contract> localChildrenContracts = lishSubContract(id);

        Set<Long> cIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(localChildrenContracts)) {
            cIdSet = localChildrenContracts.stream().map(Contract::getId).collect(Collectors.toSet());
        }

        final List<ContractDTO> childrenContracts = contractDTO.getChildrenContracts();
        if (CollectionUtils.isNotEmpty(childrenContracts)) {
            // 回款计划先删除，再新增
            for (ContractDTO childrenContract : childrenContracts) {

                final List<ReceiptPlanDetail> receiptPlanDetails = receiptPlanService.listByContractId(childrenContract.getId());
                Map<Long, String> codeMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(receiptPlanDetails)) {
                    receiptPlanDetails.forEach(receiptPlanDetail -> codeMap.put(receiptPlanDetail.getId(), receiptPlanDetail.getCode()));
                }

                receiptPlanService.deleteByContractId(childrenContract.getId());

                final List<ReceiptPlanDTO> receiptPlans = childrenContract.getReceiptPlans();

                if (CollectionUtils.isNotEmpty(receiptPlans)) {
                    // 保存回款计划
                    receiptPlans.forEach(receiptPlanDTO -> {
                        // 处理编号
                        final List<ReceiptPlanDetailDTO> details = receiptPlanDTO.getReceiptPlanDetails();
                        if (CollectionUtils.isNotEmpty(details)) {
                            details.forEach(detail -> {
                                // 待更新的数据，保留之前生成的code
                                if (detail.getId() != null) {
                                    detail.setCode(codeMap.get(detail.getId()));
                                    // 重新做新增操作，保存code
                                    detail.setId(null);
                                }
                            });
                        }

                        receiptPlanDTO.setContractId(childrenContract.getId());
                        receiptPlanDTO.setId(null);
                        receiptPlanService.save(receiptPlanDTO);
                    });
                }

                cIdSet.remove(childrenContract.getId());
            }

            if (cIdSet.size() > 0) {
                cIdSet.forEach(contractId -> receiptPlanService.deleteByContractId(contractId));
            }
        }
        //新增销售合同、子合同数据插入逻辑 chenchong by 20220718
        List<ContractVO> childContractVOList = this.getListByParentId(id);

        List<BigDecimal> bigDecimals = childContractVOList.stream().filter(obj -> null != obj.getProcessGrossProfitRatio()).map(ContractVO::getProcessGrossProfitRatio).collect(Collectors.toList());
        if (!ObjectUtil.hasEmpty(bigDecimals)) {
            BigDecimal processGrossProfitRatio = childContractVOList.stream().map(entity -> Optional.ofNullable(entity.getAmount()).orElse(BigDecimal.ZERO).multiply(entity.getProcessGrossProfitRatio())).reduce(BigDecimal.ZERO, BigDecimal::add);
            Contract contract = this.findById(id);
            contract.setProcessGrossProfitRatio(processGrossProfitRatio.divide((Objects.nonNull(contract.getAmount()) ? contract.getAmount() : BigDecimal.ZERO), 4, BigDecimal.ROUND_HALF_UP));
            contractMapper.updateByPrimaryKeySelective(contract);
        }
        return this.findDetailById(contractDTO.getId());
    }

    @Override
    public List<Contract> selectByExample(ContractExample example) {
        return contractMapper.selectByExample(example);
    }

    @Override
    public int update(Contract contract) {
        return contractMapper.updateByPrimaryKey(contract);
    }

    @Override
    public Contract findById(Long id) {
        return contractMapper.selectByPrimaryKey(id);
    }

    @Override
    public PageInfo<ContractVO> pageQueryContracts(ContractDTO contractDTO, String orderParam, Integer orderType, int pageNumber, int pageSize) {

        // 构建查询条件
        Map<String, Object> param = new HashMap<>();
        buildListContractParam(contractDTO, false, param, true);
        // 默认按照合同编号倒序
        buildOrderCondition(orderParam, orderType, param);

        PageHelper.startPage(pageNumber, pageSize);
        // 数据转换
        PageHelper.startPage(pageNumber, pageSize);
        final List<Contract> contracts = contractExtMapper.listContract(param);
        final PageInfo<ContractVO> pageInfo = BeanConverter.convertPage(contracts, ContractVO.class);

        final List<ContractVO> list = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(contractVO -> contractHelper.formatContractVO(contractVO));
        }

        return pageInfo;
    }

    @Override
    public List<ContractVO> listContracts(ContractDTO contractDTO, String orderParam, Integer orderType) {
        // 构建查询条件
        Map<String, Object> param = new HashMap<>();
        final boolean isContinue = buildListContractParam(contractDTO, true, param, true);

        // 判断部分查询条件查询返回为空，则不继续查询
        if (!isContinue) {
            logger.debug("部分查询条件为空，直接返回");
            return Lists.newArrayList();
        }

        // 使用单位下所有二级虚拟部门
        final List<UnitDto> unitDtoList = getUnitByParentId(SystemContext.getUnitId());
        Map<Long, String> orgMap = new HashMap<>();
        if (ListUtils.isNotEmpty(unitDtoList)) {
            unitDtoList.forEach(unitDto -> {
                orgMap.put(unitDto.getId(), unitDto.getUnitName());
            });
        }
        // 默认按照合同编号倒序
        buildOrderCondition(orderParam, orderType, param);

        // 数据转换
        final List<Contract> contracts = contractExtMapper.listContract(param);
        if (ListUtils.isNotEmpty(contracts)) {
            final List<ContractVO> contractVOList = BeanConverter.copy(contracts, ContractVO.class);

            List<Long> customerIdList = new ArrayList<Long>();
            Set<Long> industryIdList = new HashSet<>();
            Set<Long> signingCenterIdList = new HashSet<>();
            contractVOList.forEach(contractVO -> {
                customerIdList.add(contractVO.getCustomerId());
                industryIdList.add(contractVO.getIndustry());
                signingCenterIdList.add(contractVO.getSigningCenter());
            });
            Map<Long, CustomerDto> customerDtoMap = null;
            if (customerIdList != null && customerIdList.size() > 0) {
                customerDtoMap = contractHelper.getCustomerList(customerIdList);
            }

            Map<Long, String> contractIndustryMap = contractHelper.getIndustry(industryIdList);
            Map<Long, String> signingCenterMap = contractHelper.getSigningCenter(signingCenterIdList);

            for (ContractVO contractVO : contractVOList) {

                if (contractVO.getCustomerId() != null && customerDtoMap != null && customerDtoMap.get(contractVO.getCustomerId()) != null) {
                    // 客户
                    CustomerDto customer = customerDtoMap.get(contractVO.getCustomerId());
                    if (customer != null) {
                        contractVO.setCustomerType(customer.getInner());
                        contractVO.setCustomerName(customer.getName());
                    }
                }
                // 销售部门
                if (orgMap.containsKey(contractVO.getUnitId())) {
                    contractVO.setUnitName(orgMap.get(contractVO.getUnitId()));
                }

                // 销售经理
                contractHelper.formatSalesManager(contractVO);

                // 项目经理
                contractHelper.formatManager(contractVO);

                // 回款率
                final BigDecimal receiptRate = contractHelper.calculateParentContractReceiptRate(contractVO.getId(), contractVO.getAmount());
                contractVO.setReceiptRate(receiptRate);

                // 行业
                if (contractVO.getIndustry() != null) {
                    contractVO.setIndustryStr(contractIndustryMap != null ? contractIndustryMap.get(contractVO.getIndustry()) : null);
                }
                // 签约中心
                if (contractVO.getSigningCenter() != null) {
                    contractVO.setSigningCenterStr(signingCenterMap != null ? signingCenterMap.get(contractVO.getSigningCenter()) : null);
                }
            }
            return contractVOList;
        }
        return Lists.newArrayList();
    }

    private Map<Long, String> getSigningCenter(Set<Long> signingCenterIdList) {
        if (CollectionUtils.isEmpty(signingCenterIdList)) return null;

        return null;
    }

    private Map<Long, String> getIndustry(Set<Long> industryIdList) {
        if (CollectionUtils.isEmpty(industryIdList)) return null;

        return null;
    }

    /**
     * 获取当前登录用户授权的二级部门
     *
     * @return List<OrgUnit>
     */
    private List<OrgUnit> getOrgUnit() {
        if (SystemContext.getUserId() == null || SystemContext.getUnitId() == null) {
            return new ArrayList<>();
        }
        Map<String, Object> unitMap = new HashMap<>();
        unitMap.put("userId", SystemContext.getUserId());
        unitMap.put("unitId", SystemContext.getUnitId());
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectUserSecondUnit", unitMap);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<OrgUnit>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OrgUnit>>>() {
        });
        return response.getData();
    }

    private List<UnitDto> getUnitByParentId(Long parentId) {
        Map<String, Object> unitMap = new HashMap<>();
        unitMap.put("parentId", parentId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/unit/list", unitMap);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<UnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<UnitDto>>>() {
        });
        return response.getData();
    }

    private void buildOrderCondition(String orderParam, Integer orderType, Map<String, Object> param) {
        // 默认按照合同编号倒序
        if (StringUtils.isEmpty(orderParam)) {
            param.put("orderParam", "create_at desc");
        } else {
            if (Objects.equals(orderParam, "filingDate")) {
                orderParam = "filing_date";
            }
            if (1 == orderType) {
                param.put("orderParam", orderParam + " asc");
            } else {
                param.put("orderParam", orderParam + " desc");
            }
        }
    }

    @Override
    public PageInfo<ContractVO> pageQueryChildrenContracts(ContractDTO contractDTO, String orderParam, Integer orderType, int pageNumber, int pageSize) {

        Map<String, Object> param = new HashMap<>();
        final Long userId = SystemContext.getUserId();
        param.put("userId", userId);
        param.put("parentContractName", contractDTO.getParentContractName());

        buildListContractParam(contractDTO, false, param, false);


        // 使用单位下所有二级虚拟部门
        final List<UnitDto> unitDtoList = getUnitByParentId(SystemContext.getUnitId());
        Map<Long, String> orgMap = new HashMap<>();
        if (ListUtils.isNotEmpty(unitDtoList)) {
            unitDtoList.forEach(unitDto -> {
                orgMap.put(unitDto.getId(), unitDto.getUnitName());
            });
        }

        // 排序
        buildOrderCondition(orderParam, orderType, param);

        PageHelper.startPage(pageNumber, pageSize);
        final List<ContractVO> contractVOList = contractExtMapper.listChildrenContracts(param);
        final PageInfo<ContractVO> pageInfo = BeanConverter.convertPage(contractVOList, ContractVO.class);

        final List<ContractVO> list = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(contractVO -> {
                // 客户
                final CustomerDto customer = contractHelper.getCustomer(contractVO.getCustomerId());
                if (customer != null) {
                    contractVO.setCustomerType(customer.getInner());
                    contractVO.setCustomerName(customer.getName());
                }

                // 销售部门
                if (orgMap.containsKey(contractVO.getUnitId())) {
                    contractVO.setUnitName(orgMap.get(contractVO.getUnitId()));
                }
                //利润部门
                if (orgMap.containsKey(contractVO.getProfitDepartmentId())) {
                    contractVO.setProfitDepartmentName(orgMap.get(contractVO.getProfitDepartmentId()));
                }

                // 销售经理
                contractHelper.formatSalesManager(contractVO);

                // 项目经理
                contractHelper.formatManager(contractVO);

                // 业务实体
                contractHelper.formatOu(contractVO);

                // 回款率
                final BigDecimal receiptRate = contractHelper.calculateSubContractReceiptRate(contractVO.getId(), contractVO.getAmount());
                contractVO.setReceiptRate(receiptRate);


                //父合同ID
                Long parentId = contractVO.getParentId();
                if (parentId != null) {
                    contractVO.setContractIsView(isContractIsView(parentId));
                }
            });
        }

        return pageInfo;
    }

    @Override
    public List<ContractVO> listChildrenContracts(ContractDTO contractDTO, String orderParam, Integer orderType) {
        Map<String, Object> param = new HashMap<>();
        final Long userId = SystemContext.getUserId();
        param.put("userId", userId);
        param.put("parentContractName", contractDTO.getParentContractName());
        buildListContractParam(contractDTO, false, param, false);

        // 使用单位下所有二级虚拟部门
        final List<UnitDto> unitDtoList = getUnitByParentId(SystemContext.getUnitId());
        Map<Long, String> orgMap = new HashMap<>();
        if (ListUtils.isNotEmpty(unitDtoList)) {
            unitDtoList.forEach(unitDto -> {
                orgMap.put(unitDto.getId(), unitDto.getUnitName());

            });
        }

        // 排序
        buildOrderCondition(orderParam, orderType, param);

        final List<ContractVO> list = contractExtMapper.listChildrenContracts(param);
        if (CollectionUtils.isNotEmpty(list)) {

            List<Long> customerIdList = new ArrayList<Long>();
            list.forEach(contractVO -> {
                customerIdList.add(contractVO.getCustomerId());
            });
            Map<Long, CustomerDto> customerDtoMap = null;
            if (customerIdList != null && customerIdList.size() > 0) {
                customerDtoMap = contractHelper.getCustomerList(customerIdList);
            }

            for (ContractVO contractVO : list) {

                if (contractVO.getCustomerId() != null && customerDtoMap != null && customerDtoMap.get(contractVO.getCustomerId()) != null) {
                    // 客户
                    CustomerDto customer = customerDtoMap.get(contractVO.getCustomerId());
                    if (customer != null) {
                        contractVO.setCustomerType(customer.getInner());
                        contractVO.setCustomerName(customer.getName());
                    }
                }

                // 销售经理
                contractHelper.formatSalesManager(contractVO);

                // 销售部门
                if (orgMap.containsKey(contractVO.getUnitId())) {
                    contractVO.setUnitName(orgMap.get(contractVO.getUnitId()));
                }

                // 项目经理
                contractHelper.formatManager(contractVO);

                // 回款率
                final BigDecimal receiptRate = contractHelper.calculateSubContractReceiptRate(contractVO.getId(), contractVO.getAmount());
                contractVO.setReceiptRate(receiptRate);
            }
        }

        return list;
    }

    @Override
    public List<ContractVO> getListByParentId(Long parentId) {
        Assert.notNull(parentId, "父合同不存在");

        Contract parentContract = contractMapper.selectByPrimaryKey(parentId);
        if (parentContract == null) {
            throw new MipException("父合同不存在");
        }

        ContractExample contractExample = new ContractExample();
        ContractExample.Criteria criteria = contractExample.createCriteria();
        criteria.andParentIdEqualTo(parentId);
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        List<Contract> contractList = contractMapper.selectByExample(contractExample);

        return BeanConverter.copy(contractList, ContractVO.class);
    }

    @Override
    public ContractVO findDetailById(Long id) {
        ContractVO contractVO = new ContractVO();

        // 主合同
        final Contract contract = this.findById(id);

        BeanConverter.copy(contract, contractVO);
        final UserInfo invalid = CacheDataUtils.findUserById(contract.getInvalidId());
        contractVO.setInvalidName(null != invalid ? invalid.getName() : null);

        if (contract.getCustomerId() != null) {
            String url = String.format("%scustomer/view?customerId=%s", ModelsEnum.CRM.getBaseUrl(), contract.getCustomerId());
            String crmRes = restTemplate.getForObject(url, String.class);
            final DataResponse<CustomerDto> customerDtoDataResponse = JSON.parseObject(crmRes, new TypeReference<DataResponse<CustomerDto>>() {
            });
            final CustomerDto customerDto = customerDtoDataResponse.getData();
            List<CustomerDto> customerDtoList = new ArrayList<>();
            customerDtoList.add(customerDto);
            contractVO.setCustomerDtoList(customerDtoList);
        }

        // 获取行业信息
        String industryStr = getIndustryStr(contract.getIndustry());
        contractVO.setIndustryStr(industryStr);
        // 获取签约中心信息
        String signingCenterStr = getSigningCenterStr(contract.getSigningCenter());
        contractVO.setSigningCenterStr(signingCenterStr);

        // 拆分详情
        ContractProductExample contractProductExample = new ContractProductExample();
        final ContractProductExample.Criteria criteria = contractProductExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andContractIdEqualTo(id);
        final List<ContractProduct> contractProducts = contractProductMapper.selectByExample(contractProductExample);
        if (CollectionUtils.isNotEmpty(contractProducts)) {
            List<ContractProductDTO> ContractProductDTOList = BeanConverter.copy(contractProducts, ContractProductDTO.class);
            contractVO.setContractProducts(ContractProductDTOList);
        }

        contractHelper.formatContractVO(contractVO);

        // 子合同
        final List<ContractVO> childContractVOList = this.getListByParentId(id);
        if (CollectionUtils.isNotEmpty(childContractVOList)) {
            // 获取计量单位
            Map<String, String> unitMap = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null)
                    .stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName, (a, b) -> a));
            // 查询产品清单
            ContractInventoryExample contractInventoryExample = new ContractInventoryExample();
            contractInventoryExample.createCriteria().andParentContractIdEqualTo(id).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<ContractInventoryDTO> contractInventoryList = BeanConverter.copy(contractInventoryMapper.selectByExample(contractInventoryExample), ContractInventoryDTO.class);
            contractInventoryList.forEach(s -> s.setUnitName(unitMap.get(s.getUnit())));
            Map<Long, List<ContractInventoryDTO>> contractInventoryMap = contractInventoryList.stream().collect(Collectors.groupingBy(ContractInventoryDTO::getContractId));

            childContractVOList.forEach(childContract -> {
                ProjectType projectType = projectTypeMapper.selectByPrimaryKey(childContract.getBusinessTypeId());
                if (projectType != null) {
                    childContract.setBusinessTypeName(projectType.getName());
                }
                contractHelper.buildDetail(childContract);
                contractHelper.formatContractVO(childContract);
                //产品清单
                childContract.setContractInventoryList(contractInventoryMap.get(childContract.getId()));
                //查看该合同是否有跟项目关联
                ProjectContractRsExample projectContractRsExample = new ProjectContractRsExample();
                final ProjectContractRsExample.Criteria pcrCriteria = projectContractRsExample.createCriteria();
                pcrCriteria.andContractIdEqualTo(childContract.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                projectContractRsExample.setOrderByClause("create_at desc");
                final List<ProjectContractRs> projectContractRsList = projectContractRsService.selectByExample(projectContractRsExample);
                if (CollectionUtils.isNotEmpty(projectContractRsList)) {
                    contractVO.setIsRelated(true);
                    final ProjectContractRs projectContractRs = projectContractRsList.get(0);
                    final Long projectId = projectContractRs.getProjectId();
                    final Project project = projectService.selectByPrimaryKey(projectId);

                    if (project != null) {
                        //查看该合同是否有主辅里程碑
                        boolean isMilepost = isMilepost(projectId);
                        if (isMilepost) {
                            contractVO.setIsMilepost(true);
                            childContract.setIsMilepost(true);
                            ProjectProfitExample projectProfitExample = new ProjectProfitExample();
                            ProjectProfitExample.Criteria criteria1 = projectProfitExample.createCriteria();
                            criteria1.andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
                            final List<ProjectProfit> projectProfits = projectProfitService.selectByExample(projectProfitExample);
                            ProjectProfitDto projectProfitDto = new ProjectProfitDto();
                            if (CollectionUtils.isNotEmpty(projectProfits)) {
                                BeanConverter.copy(projectProfits.get(0), projectProfitDto);
                                childContract.setProjectProfitDTO(projectProfitDto);
                            }
                        }
                        childContract.setProject(project);
                        //收入成本计划最大的实际结束时间
                        childContract.setMaxActualEndTime(getMaxActualEndTime(project));
                    }

                }
            });

            contractVO.setChildrenContracts(childContractVOList);
        }

        // 子合同
        if (contract.getParentId() != null) {
            // 主合同
            final Contract parentContract = contractMapper.selectByPrimaryKey(contract.getParentId());
            if (parentContract != null) {
                contractVO.setParentContractName(parentContract.getName());
                contractVO.setParentContractCode(parentContract.getCode());
            }

            // 项目信息
            ProjectContractRsExample projectContractRsExample = new ProjectContractRsExample();
            final ProjectContractRsExample.Criteria pcrCriteria = projectContractRsExample.createCriteria();
            pcrCriteria.andContractIdEqualTo(contract.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            projectContractRsExample.setOrderByClause("create_at desc");
            final List<ProjectContractRs> projectContractRsList = projectContractRsService.selectByExample(projectContractRsExample);
            if (CollectionUtils.isNotEmpty(projectContractRsList)) {
                final ProjectContractRs projectContractRs = projectContractRsList.get(0);
                final Long projectId = projectContractRs.getProjectId();
                final Project project = projectService.selectByPrimaryKey(projectId);
                contractVO.setProject(project);
            }

            // 合同产品成本拆分信息
            final List<ContractProductCost> contractProductCosts = contractProductCostService.listByContractId(id);
            contractVO.setContractProductCosts(contractProductCosts);
        }

        // 关联框架合同
        final Long frameContractId = contract.getFrameContractId();
        if (frameContractId != null) {
            final Contract frameContract = contractMapper.selectByPrimaryKey(frameContractId);
            if (frameContract != null) {
                contractVO.setFrameContractName(frameContract.getName());
            }
        }
        if (contractVO.getStatus().equals(ContractStatus.EFFECTIVE.getCode()) ||
                contractVO.getStatus().equals(ContractStatus.TO_BE_EFFECTIVE.getCode())) {
            contractVO.setChangewayId(null);
        }

        List<ContractVO> finalChildrenContracts = contractVO.getChildrenContracts();
        if (CollectionUtils.isNotEmpty(finalChildrenContracts)) {
            for (ContractVO finalChildrenContract : finalChildrenContracts) {
                String eampurchaseId = finalChildrenContract.getEampurchaseId();
                final String businessTypeName = finalChildrenContract.getBusinessTypeName();
                final Boolean isInvoice = this.isInvoice(finalChildrenContract.getId()); //开票申请是否100红冲
                finalChildrenContract.setIsInvoice(isInvoice);
                if (StringUtils.isNotEmpty(eampurchaseId)) {
                    List<Long> eamPurchaseIds = Arrays.stream(eampurchaseId.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    EamPurchaseInfoExample eamPurchaseInfoExample = new EamPurchaseInfoExample();
                    eamPurchaseInfoExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(eamPurchaseIds);
                    List<EamPurchaseInfo> eamPurchaseInfoList = eamPurchaseInfoService.selectByExample(eamPurchaseInfoExample);

                    //added by dengfei added at 20210112 人力外包和人力外包rdm 需要给一个rdm结算单开票的标志
                    if (StringUtils.isNotEmpty(businessTypeName)) {
                        if (businessTypeName.contains("人力外包")) {
                            if (CollectionUtils.isNotEmpty(eamPurchaseInfoList)) {
//                                final List<String> eamContractCodeList = eamPurchaseInfoList.stream().map(EamPurchaseInfo::getContractCode).collect(Collectors.toList());
                                for (EamPurchaseInfo eamPurchaseInfo : eamPurchaseInfoList) {
                                    InvoiceApplyHeaderExample invoiceApplyHeaderExample = new InvoiceApplyHeaderExample();
                                    invoiceApplyHeaderExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andStatusNotEqualTo(-1)
                                            .andEamPurchaseCodeEqualTo(eamPurchaseInfo.getContractCode());
                                    final List<InvoiceApplyHeader> invoiceApplyHeaderList = invoiceApplyHeaderMapper.selectByExample(invoiceApplyHeaderExample);
                                    if (CollectionUtils.isNotEmpty(invoiceApplyHeaderList)) {
                                        eamPurchaseInfo.setAttribute5("已开票");
                                    }

                                }

                            }
                        }
                    }
                    finalChildrenContract.setEamPurchaseInfoList(eamPurchaseInfoList);
                }
            }
            contractVO.setChildrenContracts(finalChildrenContracts);
        }

        String eampurchaseId = contractVO.getEampurchaseId();
        if (StringUtils.isNotEmpty(eampurchaseId)) {
            List<Long> eamPurchaseIds = Arrays.stream(eampurchaseId.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            EamPurchaseInfoExample eamPurchaseInfoExample = new EamPurchaseInfoExample();
            eamPurchaseInfoExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(eamPurchaseIds);
            List<EamPurchaseInfo> eamPurchaseInfoList = eamPurchaseInfoService.selectByExample(eamPurchaseInfoExample);
            contractVO.setEamPurchaseInfoList(eamPurchaseInfoList);
        }

        List<ContractVO> finalChildrenContractVOList = contractVO.getChildrenContracts();
        if (CollectionUtils.isNotEmpty(finalChildrenContractVOList)) {
            for (ContractVO childrenVO : finalChildrenContractVOList) {
                //补全开票回款中的里程碑信息
                final List<InvoicePlanDTO> invoicePlanDTOList = childrenVO.getInvoicePlans();
                if (CollectionUtils.isNotEmpty(invoicePlanDTOList)) {
                    for (InvoicePlanDTO invoicePlanDTO : invoicePlanDTOList) {
                        final List<InvoicePlanDetailDto> invoicePlanDetailDTOList = invoicePlanDTO.getInvoicePlanDetails();
                        if (CollectionUtils.isNotEmpty(invoicePlanDetailDTOList)) {
                            for (InvoicePlanDetailDto invoicePlanDetailDto : invoicePlanDetailDTOList) {
                                final Long milestoneId = invoicePlanDetailDto.getMilestoneId();
                                if (null != milestoneId) {
                                    ProjectMilepost projectMilepost = projectMilepostService.selectByPrimaryKey(milestoneId);
                                    // 查项目里程碑
                                    if (null != projectMilepost) {
                                        invoicePlanDetailDto.setProjectId(projectMilepost.getProjectId());
                                        invoicePlanDetailDto.setMilestoneName(projectMilepost.getName());
                                        if (null != projectMilepost.getGroupId()) {
                                            ProjectMilepostGroup group = projectMilepostGroupMapper.selectByPrimaryKey(projectMilepost.getGroupId());
                                            // 如果有并行交付线，拼接名称
                                            if (null != group) {
                                                invoicePlanDetailDto.setMilestoneName(group.getName() + "-" + projectMilepost.getName());
                                            }
                                        }
                                    }
                                    // 查里程碑模板
                                    else {
                                        MilepostTemplateStage milepostTemplateStage = milepostTemplateStageService.selectByPrimaryKey(milestoneId);
                                        if (milepostTemplateStage != null) {
                                            invoicePlanDetailDto.setMilestoneName(milepostTemplateStage.getMilepostStage());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                final List<ReceiptPlanDTO> receiptPlanDTOList = childrenVO.getReceiptPlans();
                if (CollectionUtils.isNotEmpty(receiptPlanDTOList)) {
                    for (ReceiptPlanDTO receiptPlanDTO : receiptPlanDTOList) {
                        final List<ReceiptPlanDetailDTO> receiptPlanDetailDTOList = receiptPlanDTO.getReceiptPlanDetails();
                        if (CollectionUtils.isNotEmpty(receiptPlanDetailDTOList)) {
                            for (ReceiptPlanDetailDTO receiptPlanDetailDTO : receiptPlanDetailDTOList) {
                                final Long milestoneId = receiptPlanDetailDTO.getMilestoneId();
                                ProjectMilepost projectMilepost = projectMilepostService.selectByPrimaryKey(milestoneId);
                                if (null != projectMilepost) {
                                    receiptPlanDetailDTO.setProjectId(projectMilepost.getProjectId());
                                    receiptPlanDetailDTO.setMilestoneName(projectMilepost.getName());
                                    // 里程碑名称拼接并行交付线名称
                                    if (null != projectMilepost.getGroupId()) {
                                        ProjectMilepostGroup group = projectMilepostGroupMapper.selectByPrimaryKey(projectMilepost.getGroupId());
                                        if (null != group) {
                                            receiptPlanDetailDTO.setMilestoneName(group.getName() + "-" + projectMilepost.getName());
                                        }
                                    }
                                } else {
                                    MilepostTemplateStage milepostTemplateStage = milepostTemplateStageService.selectByPrimaryKey(milestoneId);
                                    if (milepostTemplateStage != null) {
                                        receiptPlanDetailDTO.setMilestoneName(milepostTemplateStage.getMilepostStage());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 模板库返回的附件attachmentId(删除的数据也算上，都相同)----重构fileAttachmentId   (废弃)
        return contractVO;
    }

    private String getSigningCenterStr(Long signingCenter) {
        if (signingCenter == null) return null;
        String url = String.format("%scontractSigningCenter/view/" + signingCenter, ModelsEnum.BASEDATA.getBaseUrl());
        String baseDataRes = restTemplate.getForObject(url, String.class);
        ContractSigningCenter contractSigningCenter = JSON.parseObject(baseDataRes, new TypeReference<ContractSigningCenter>() {
        });
        if (contractSigningCenter == null) return null;
        return contractSigningCenter.getLevel1() + "/" +
                contractSigningCenter.getLevel2();
    }

    private String getIndustryStr(Long industry) {
        if (industry == null) return null;
        String url = String.format("%scontractIndustry/view/" + industry, ModelsEnum.BASEDATA.getBaseUrl());
        String baseDataRes = restTemplate.getForObject(url, String.class);
        ContractIndustry contractIndustry = JSON.parseObject(baseDataRes, new TypeReference<ContractIndustry>() {
        });
        if (contractIndustry == null) return null;
        return contractIndustry.getLevel1() + "/" +
                contractIndustry.getLevel2() + "/" +
                contractIndustry.getLevel3();
    }

    @Override
    public ContractVO findDetailById(Long id, String type) {
        ContractVO vo = findDetailById(id);
        if (StringUtils.isNotEmpty(type) && "change".equals(type)) {
            ContractHisExample example = new ContractHisExample();
            example.setOrderByClause("create_at desc");
            example.createCriteria().andContractIdEqualTo(id)
                    .andChangewayIdEqualTo(vo.getChangewayId())
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            List<ContractHis> contractHisList = contractHisMapper.selectByExample(example);
            ContractHis po = contractHisList.stream().findFirst().orElse(null);
            vo.setFileAttachmentId(Objects.isNull(po) ? null : po.getFileAttachmentId());
        }
        if (Objects.isNull(vo.getDrafter())) {
            vo.setDrafter(vo.getCreateBy());
        }
        UserInfo userInfo = CacheDataUtils.findUserById(vo.getDrafter());
        if (Objects.nonNull(userInfo)) {
            vo.setDrafterName(userInfo.getName());
        }
        return vo;
    }

    @Override
    public ContractVO findChildrenDetailById(Long id) {
        ContractVO contractVO = new ContractVO();
        // 子合同
        final Contract contract = this.findById(id);
        BeanConverter.copy(contract, contractVO);
        if (contractVO != null) {
            contractHelper.buildDetail(contractVO);
            contractHelper.formatContractVO(contractVO);
            //查看该合同是否有跟项目关联
            ProjectContractRsExample projectContractRsExample = new ProjectContractRsExample();
            final ProjectContractRsExample.Criteria pcrCriteria = projectContractRsExample.createCriteria();
            pcrCriteria.andContractIdEqualTo(contractVO.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            final List<ProjectContractRs> projectContractRsList = projectContractRsService.selectByExample(projectContractRsExample);
            if (CollectionUtils.isNotEmpty(projectContractRsList)) {
                contractVO.setIsRelated(true);
                final ProjectContractRs projectContractRs = projectContractRsList.get(0);
                final Long projectId = projectContractRs.getProjectId();
                final Project project = projectService.selectByPrimaryKey(projectId);
                contractVO.setProject(project);
            }

            String eampurchaseId = contractVO.getEampurchaseId();
            if (StringUtils.isNotEmpty(eampurchaseId)) {
                List<Long> eamPurchaseIds = Arrays.stream(eampurchaseId.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                EamPurchaseInfoExample eamPurchaseInfoExample = new EamPurchaseInfoExample();
                eamPurchaseInfoExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(eamPurchaseIds);
                List<EamPurchaseInfo> eamPurchaseInfoList = eamPurchaseInfoService.selectByExample(eamPurchaseInfoExample);
                contractVO.setEamPurchaseInfoList(eamPurchaseInfoList);
            }

            // 子合同详情中新增关联税票栏目信息也就是应收发票信息
            InvoiceReceivableExample invoiceReceivableExample = new InvoiceReceivableExample();
            invoiceReceivableExample.setOrderByClause("gl_date desc");
            invoiceReceivableExample.createCriteria().andContractIdEqualTo(id).andDeletedFlagEqualTo(0);
            List<InvoiceReceivable> invoiceReceivableList = invoiceReceivableMapper.selectByExampleWithBLOBs(invoiceReceivableExample);
            if (CollectionUtils.isNotEmpty(invoiceReceivableList)) {
                List<InvoiceReceivableDto> invoiceReceivableDtoList = BeanConverter.convert(invoiceReceivableList, InvoiceReceivableDto.class);
                for (InvoiceReceivableDto invoiceReceivableDto : invoiceReceivableDtoList) {
                    if (StringUtils.isNotEmpty(invoiceReceivableDto.getApplyCode())) {
                        InvoiceApplyHeaderExample invoiceApplyHeaderExample = new InvoiceApplyHeaderExample();
                        invoiceApplyHeaderExample.createCriteria().andApplyCodeEqualTo(invoiceReceivableDto.getApplyCode()).andDeletedFlagEqualTo(Boolean.FALSE);
                        List<InvoiceApplyHeader> invoiceApplyHeaderList = invoiceApplyHeaderMapper.selectByExample(invoiceApplyHeaderExample);
                        if (CollectionUtils.isNotEmpty(invoiceApplyHeaderList)) {
                            //开票申请号是唯一的
                            invoiceReceivableDto.setInvoiceApplyId(invoiceApplyHeaderList.get(0).getId());
                        }
                    }
                    BigDecimal exclusiveOfTax = invoiceReceivableDto.getExclusiveOfTax() == null ? BigDecimal.ZERO : invoiceReceivableDto.getExclusiveOfTax();
                    BigDecimal externalInvoiceAmount = invoiceReceivableDto.getExternalInvoiceAmount() == null ? BigDecimal.ZERO : invoiceReceivableDto.getExternalInvoiceAmount();
                    BigDecimal taxIncludedPrice = invoiceReceivableDto.getTaxIncludedPrice() == null ? BigDecimal.ZERO : invoiceReceivableDto.getTaxIncludedPrice();
                    BigDecimal ticketTail = exclusiveOfTax.subtract(externalInvoiceAmount);
                    BigDecimal taxAmount = taxIncludedPrice.subtract(ticketTail);
                    invoiceReceivableDto.setTicketTail(ticketTail);
                    invoiceReceivableDto.setTaxAmount(taxAmount);
                }
                contractVO.setInvoiceReceivableDtoList(invoiceReceivableDtoList);
            }

            //子合同中新增关联回款栏目
            ReceiptClaimContractRelExample receiptClaimContractRelExample = new ReceiptClaimContractRelExample();
            receiptClaimContractRelExample.createCriteria().andContractIdEqualTo(id).andDeletedFlagEqualTo(0);
            List<ReceiptClaimContractRel> receiptClaimContractRelList = receiptClaimContractRelMapper.selectByExample(receiptClaimContractRelExample);
            if (CollectionUtils.isNotEmpty(receiptClaimContractRelList)) {
                List<Long> detailIdList = receiptClaimContractRelList.stream().map(ReceiptClaimContractRel::getReceiptClaimDetailId).distinct().collect(Collectors.toList());
                ReceiptClaimDto receiptClaimDtoQuery = new ReceiptClaimDto();
                receiptClaimDtoQuery.setIdList(detailIdList);
                receiptClaimDtoQuery.setContractId(id);
                List<ReceiptClaimDto> receiptClaimDtoList = receiptClaimExtMapper.queryDetailList1(receiptClaimDtoQuery);
                if (CollectionUtils.isNotEmpty(receiptClaimDtoList)) {
                    List<ReceiptClaimDto> receiptClaimDtoList1 = new ArrayList<>();
                    for (ReceiptClaimDto receiptClaimDto : receiptClaimDtoList) {
                        receiptClaimDto.setDisplayType("回款");
                    }
                    final List<Long> receiptClaimDetailIds = receiptClaimDtoList.stream().map(r -> r.getId()).distinct().collect(Collectors.toList());
                    RefundApplyDetailExample refundApplyDetailExample = new RefundApplyDetailExample();
                    refundApplyDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andReceiptClaimDetailIdIn(receiptClaimDetailIds)
                            .andContractIdEqualTo(id);
                    final List<RefundApplyDetail> refundApplyDetailList = refundApplyDetailMapper.selectByExample(refundApplyDetailExample);
                    BigDecimal totalRefundAmount = BigDecimal.ZERO;
                    if (CollectionUtils.isNotEmpty(refundApplyDetailList)) {
                        for (RefundApplyDetail refundApplyDetail : refundApplyDetailList) {
                            String invoicePlanCode = "";
                            BigDecimal invoicePlanAmount = null;
//                                totalRefundAmount = totalRefundAmount.add(refundApplyDetail.getRefundAmount() == null?BigDecimal.ZERO : refundApplyDetail.getRefundAmount());
                            for (ReceiptClaimDto receiptClaimDto : receiptClaimDtoList) {
                                if (receiptClaimDto.getId() != null && receiptClaimDto.getId().equals(refundApplyDetail.getReceiptClaimDetailId())) {
                                    invoicePlanCode = receiptClaimDto.getInvoicePlanCode();
                                    invoicePlanAmount = receiptClaimDto.getInvoicePlanAmount();
                                }
                            }
                            final RefundApply refundApply = refundApplyMapper.selectByPrimaryKey(refundApplyDetail.getRefundApplyId());
                            if (refundApply.getRefundApplyStatus() != null && refundApply.getRefundApplyStatus().equals(RefundStatus.PAYMENTTED.getCode())) {
                                ReceiptClaimDto receiptClaimDto1 = new ReceiptClaimDto();
                                receiptClaimDto1.setDisplayType("退款");
                                receiptClaimDto1.setCashReceiptCode(refundApply.getRefundCashCode());
                                receiptClaimDto1.setCustomerName(refundApply.getCustomerName());
                                receiptClaimDto1.setReceiptCode(refundApply.getRefundApplyCode());
                                receiptClaimDto1.setAccountingDate(refundApply.getRefundEntryDate());
                                receiptClaimDto1.setInvoicePlanCode(invoicePlanCode);
                                receiptClaimDto1.setInvoicePlanAmount(invoicePlanAmount);
                                receiptClaimDto1.setAllocatedAmount(refundApplyDetail.getRefundAmount() == null ? BigDecimal.ZERO : refundApplyDetail.getRefundAmount());
                                receiptClaimDtoList1.add(receiptClaimDto1);
                            }
                        }
                    }
                    receiptClaimDtoList.addAll(receiptClaimDtoList1);

                }
                contractVO.setReceiptClaimDtoList(receiptClaimDtoList);
            }

            // 获取计量单位
            Map<String, String> unitMap = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null)
                    .stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName, (a, b) -> a));
            // 产品清单
            ContractInventoryExample contractInventoryExample = new ContractInventoryExample();
            contractInventoryExample.createCriteria().andContractIdEqualTo(id).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<ContractInventoryDTO> contractInventoryList = BeanConverter.copy(contractInventoryMapper.selectByExample(contractInventoryExample), ContractInventoryDTO.class);
            contractInventoryList.forEach(s -> s.setUnitName(unitMap.get(s.getUnit())));
            contractVO.setContractInventoryList(contractInventoryList);
        }

        //补全开票回款中的里程碑信息
        final List<InvoicePlanDTO> invoicePlanDTOList = contractVO.getInvoicePlans();
        if (CollectionUtils.isNotEmpty(invoicePlanDTOList)) {
            for (InvoicePlanDTO invoicePlanDTO : invoicePlanDTOList) {
                final List<InvoicePlanDetailDto> invoicePlanDetailDTOList = invoicePlanDTO.getInvoicePlanDetails();
                if (CollectionUtils.isNotEmpty(invoicePlanDetailDTOList)) {
                    for (InvoicePlanDetailDto invoicePlanDetailDto : invoicePlanDetailDTOList) {
                        final Long milestoneId = invoicePlanDetailDto.getMilestoneId();
                        // 如果绑定了里程碑，要提供projectId
                        if (null != milestoneId) {
                            ProjectMilepost projectMilepost = projectMilepostService.selectByPrimaryKey(milestoneId);
                            // 查项目里程碑
                            if (null != projectMilepost) {
                                invoicePlanDetailDto.setProjectId(projectMilepost.getProjectId());
                                invoicePlanDetailDto.setMilestoneName(projectMilepost.getName());
                                if (null != projectMilepost.getGroupId()) {
                                    ProjectMilepostGroup group = projectMilepostGroupMapper.selectByPrimaryKey(projectMilepost.getGroupId());
                                    // 如果有并行交付线，拼接名称
                                    if (null != group) {
                                        invoicePlanDetailDto.setMilestoneName(group.getName() + "-" + projectMilepost.getName());
                                    }
                                }
                            }
                            // 查里程碑模板
                            else {
                                MilepostTemplateStage milepostTemplateStage = milepostTemplateStageService.selectByPrimaryKey(milestoneId);
                                if (milepostTemplateStage != null) {
                                    invoicePlanDetailDto.setMilestoneName(milepostTemplateStage.getMilepostStage());
                                }
                            }
                        }
                    }
                }
            }
        }
        final List<ReceiptPlanDTO> receiptPlanDTOList = contractVO.getReceiptPlans();
        if (CollectionUtils.isNotEmpty(receiptPlanDTOList)) {
            for (ReceiptPlanDTO receiptPlanDTO : receiptPlanDTOList) {
                final List<ReceiptPlanDetailDTO> receiptPlanDetailDTOList = receiptPlanDTO.getReceiptPlanDetails();
                if (CollectionUtils.isNotEmpty(receiptPlanDetailDTOList)) {
                    for (ReceiptPlanDetailDTO receiptPlanDetailDTO : receiptPlanDetailDTOList) {
                        Long milestoneId = receiptPlanDetailDTO.getMilestoneId();
                        ProjectMilepost projectMilepost = projectMilepostService.selectByPrimaryKey(milestoneId);
                        // 查项目里程碑
                        if (null != projectMilepost) {
                            receiptPlanDetailDTO.setProjectId(projectMilepost.getProjectId());
                            receiptPlanDetailDTO.setMilestoneName(projectMilepost.getName());
                            if (null != projectMilepost.getGroupId()) {
                                ProjectMilepostGroup group = projectMilepostGroupMapper.selectByPrimaryKey(projectMilepost.getGroupId());
                                // 如果有并行交付线，拼接名称
                                if (null != group) {
                                    receiptPlanDetailDTO.setMilestoneName(group.getName() + "-" + projectMilepost.getName());
                                }
                            }
                        }
                        // 查里程碑模板
                        else {
                            MilepostTemplateStage milepostTemplateStage = milepostTemplateStageService.selectByPrimaryKey(milestoneId);
                            if (milepostTemplateStage != null) {
                                receiptPlanDetailDTO.setMilestoneName(milepostTemplateStage.getMilepostStage());
                            }
                        }
                    }
                }
            }
        }

        return contractVO;
    }

    private Date getMaxActualEndTime(Project project) {
        //收入成本计划最大的实际结束时间
        Date endDate = null;
        //查询子合同关联项目是否存在已结转的会计节点（用于日期变更的校验）
        ProjectType projectType = this.findProjectType(project.getType());
        Long ouId = project.getOuId();
        // 月度确认型的项目
        if (IncomePoint.MONTH.getCode().equals(projectType.getIncomePoint())) {
            Boolean typeFlag = true;//是否是交付收入计划项目类型
            //根据字典类型以及字典码找到组
            String dicName = this.findDicName();
            //根据字ouId+参数对象（公司或组织）+ 配置的组织参数名称找到对应的组织参数数据
            OrganizationCustomDict organizationCustomDict =
                    basedataExtService.getOrganizationCustomDictByName(ouId, OrgCustomDictOrgFrom.OU.code(), dicName);
            if (Objects.nonNull(organizationCustomDict)) {
                String value = organizationCustomDict.getValue();
                if (StringUtils.isNotEmpty(value)) {
                    List<String> values = Arrays.asList(StringUtils.split(value, ","));
                    //项目类型为交付收入计划项目类型
                    if (values.stream().anyMatch(projectType.getName()::equals)) {
                        typeFlag = false;
                    }
                }
            }
            if (typeFlag) {
                //查询收入成本计划
                ProjectIncomeCostPlanExample projectIncomeCostPlanExample = new ProjectIncomeCostPlanExample();
                ProjectIncomeCostPlanExample.Criteria costPlancriteria = projectIncomeCostPlanExample.createCriteria();
                costPlancriteria.andProjectIdEqualTo(project.getId());
                costPlancriteria.andDeletedFlagEqualTo(Boolean.FALSE);
                costPlancriteria.andCarryStatusEqualTo("1");
                costPlancriteria.andCarryoverBillIdIsNotNull();
                projectIncomeCostPlanExample.setOrderByClause(" actual_end_time desc");
                List<ProjectIncomeCostPlan> projectIncomeCostPlans = projectIncomeCostPlanMapper.selectByExample(projectIncomeCostPlanExample);
                if (CollectionUtils.isNotEmpty(projectIncomeCostPlans)) {
                    endDate = projectIncomeCostPlans.get(0).getActualEndTime();
                }
            }
        }
        return endDate;
    }

    /**
     * 根据字典类型以及字典码找到组
     */
    private String findDicName() {
        Dict dict = basedataExtService.findDictByTypeAndCode("organization_custom_dict", "16");
        org.springframework.util.Assert.notNull(dict, "请先在字典中配置字典类型为organization_custom_dict字典的字典码，字典名称：交付收入计划项目类型，字典code:16");
        org.springframework.util.Assert.hasText(dict.getName(), "请先在字典中配置字典类型为organization_custom_dict字典的字典码，字典名称：交付收入计划项目类型，字典code:16");
        return dict.getName();
    }

    /**
     * 获取项目类型
     */
    private ProjectType findProjectType(Long projectTypeId) {
        ProjectType projectType = projectTypeService.selectByPrimaryKey(projectTypeId);
        org.springframework.util.Assert.notNull(projectType, "未找到ID=" + projectTypeId + "的项目类型数据!");
        org.springframework.util.Assert.hasText(projectType.getName(), "ID=" + projectTypeId + "的项目类型名称不能为空");
        return projectType;
    }

    @Override
    public PageInfo<Contract> pageFrameContracts(ContractDTO contractDTO, int pageNumber, int pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap.put("fuzzyLike", contractDTO.getFuzzyLike());
        List<Integer> statusList = new ArrayList<>();
        statusList.add(ContractStatus.EFFECTIVE.getCode());
        statusList.add(ContractStatus.TO_BE_EFFECTIVE.getCode());
        conditionMap.put("statusList", statusList);

        List<Long> unitList = basedataExtService.findUnitIds(SystemContext.getUnitId());
        unitList.add(NOT_EXIST_ID);//防止空值
        conditionMap.put("unitList", unitList);

        final List<Contract> contracts = contractExtMapper.listFrameContracts(conditionMap);
        final PageInfo<Contract> pageInfo = BeanConverter.convertPage(contracts, Contract.class);
        return pageInfo;
    }

    @Override
    public List<Contract> findNeedUpdateStatus() {
        ContractExample contractExample = new ContractExample();
        final ContractExample.Criteria contractExampleCriteria = contractExample.createCriteria();
        List<Integer> statusList = new ArrayList<>();
        statusList.add(ContractStatus.TO_BE_EFFECTIVE.getCode());
        statusList.add(ContractStatus.EFFECTIVE.getCode());
        contractExampleCriteria.andStatusIn(statusList);
        contractExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE);

        final List<Contract> contracts = contractMapper.selectByExample(contractExample);
        return contracts;
    }

    @Override
    public List<ContractProductDTO> findWinPlanWithCostByBusinessId(Long businessId) {
        Assert.notNull(businessId, "商机ID不能为空");

        List<ContractProductDTO> contractProductDTOList = new ArrayList<>();

        Map<String, Object> param = new HashMap<>();
        param.put("businessId", businessId);
        param.put("status", 3);
        param.put("unitId", SystemContext.getUnitId());
        final String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "quotation/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<List<QuotationDto>> response =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<QuotationDto>>>() {
                });

        if (response != null && response.getData() != null && response.getData().size() > 0) {
            final QuotationDto data = response.getData().get(0);
            final List<QuotationDetailDto> quotationDetailDtoList = data.getDetailDtos();
            if (CollectionUtils.isNotEmpty(quotationDetailDtoList)) {
                final Long currentUnitId = SystemContext.getUnitId();
                /*final List<FeeItemDto> feeItemDtoList = CacheDataUtils.findAllFeeItem();
                Map<String, FeeItemDto> feeItemDtoMap = new HashMap<>();
                feeItemDtoList.forEach(feeItemDto -> {
                    final Long unitId = feeItemDto.getUnitId();
                    if (Objects.equals(currentUnitId, unitId)) {
                        feeItemDtoMap.put(feeItemDto.getName(), feeItemDto);
                    }
                });*/
                quotationDetailDtoList.forEach(quotationDetailDto -> {
                    ContractProductDTO contractProductDTO = new ContractProductDTO();
                    // 成本类型名称
                    String costType = quotationDetailDto.getFeeName();
                    // 转成成本类型
                    // TODO 按照中文名称匹配，坑
                    if ("产品(硬件/软件)".equals(costType)) {
                        costType = "产品(硬件/软件)";
                    } else if ("人力".equals(costType)) {
                        costType = "人力";
                    } else if ("差旅".equals(costType)) {
                        costType = "差旅";
                    }
                    /*final FeeItemDto feeItemDto = feeItemDtoMap.get(costType);*/
                    //edit by ex_xuwj4 begin
                    FeeItemDto feeItemDto = null;
                    if (StringUtils.isNotEmpty(costType) && null != currentUnitId) {
                        final Map<String, Object> param_1 = new HashMap<>();
                        param_1.put("name", costType);
                        param_1.put("unitId", currentUnitId);
                        final String url_1 = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/feeItem/selectListByNameAndUnitId", param_1);
                        String res = restTemplate.getForEntity(url_1, String.class).getBody();
                        DataResponse<FeeItemDto> feeItemDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<FeeItemDto>>() {
                        });
                        feeItemDto = feeItemDataResponse.getData();
                    }
                    //edit by ex_xuwj4 end
                    if (feeItemDto != null) {
                        contractProductDTO.setProductTypeId(feeItemDto.getId());
                        contractProductDTO.setProductTypeName(costType);
                        contractProductDTO.setAmount(quotationDetailDto.getAfterTaxDiscount());
                        contractProductDTO.setCost(quotationDetailDto.getCost());

                        contractProductDTOList.add(contractProductDTO);
                    }
                });
            }
        }

        return contractProductDTOList;
    }

    @Override
    public PageInfo<ContractVO> pageChildrenContractsWithUnused(String code, String name, String resourceCode, Long projectTypeId, Long projectId, Long headerId, String type, int pageNumber, int pageSize) {
        //存储项目已关联的合同
        List<Long> contractIdList = new ArrayList<>();
        contractIdList.add(-1L);
        if (Objects.nonNull(projectId)) {
            ProjectContractRsExample example = new ProjectContractRsExample();
            example.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<ProjectContractRs> contractRsList = projectContractRsMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(contractRsList)) {
                List<Long> rsList = contractRsList.stream().map(ProjectContractRs::getContractId).collect(Collectors.toList());
                contractIdList.addAll(rsList);
            }
        }
        if (Objects.nonNull(headerId)) {
            ProjectContractRsChangeHistoryExample changeExample = new ProjectContractRsChangeHistoryExample();
            changeExample.createCriteria().andHeaderIdEqualTo(headerId)
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<ProjectContractRsChangeHistory> changeHistoryList = projectContractRsChangeHistoryMapper.selectByExample(changeExample);
            if (ListUtils.isNotEmpty(changeHistoryList)) {
                //变更新建new、重新编辑edit
                List<Long> rsChangeList = changeHistoryList.stream().filter(e -> Objects.equals(HistoryType.CHANGE.getCode(), e.getHistoryType())).map(ProjectContractRsChangeHistory::getContractId).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(rsChangeList)) {
                    contractIdList.addAll(rsChangeList);
                }
            }
        }
        Map<String, Object> queryParam = buildParam(code, name, projectTypeId, projectId, contractIdList);
        return getContractVOPageInfo(resourceCode, projectId, pageNumber, pageSize, contractIdList, queryParam);
    }

    private PageInfo<ContractVO> getContractVOPageInfo(String resourceCode, Long projectId, int pageNumber, int pageSize, List<Long> contractIdList, Map<String, Object> queryParam) {
        PageHelper.startPage(pageNumber, pageSize);
        final List<ContractVO> contracts;
        if (StringUtils.isNotEmpty(resourceCode)) {
            List<Long> ous = buildOus(projectId);
            contracts = contractExtMapper.getContractByResourceCode(resourceCode, ous);
        } else {
            contracts = contractExtMapper.listChildrenContractsWithUnused(queryParam);
        }
        //增加置顶标识与占用标识
        if (ListUtils.isNotEmpty(contractIdList)) {
            contracts.forEach(vo -> {
                if (contractIdList.contains(vo.getId())) {
                    vo.setTopFlag(1);
                }
                List<ProjectContractRsDto> rsDtos = projectContractRsService.getProjectContractRsByContractId(vo.getId());
                if (CollectionUtils.isNotEmpty(rsDtos)) {
                    List<ProjectContractRsDto> rsDtoList = rsDtos.stream().filter(e -> !Objects.equals(projectId, e.getProjectId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(rsDtoList)) {
                        vo.setOccupyFlag(Boolean.TRUE);
                        List<String> projectCodeList = rsDtoList.stream().map(ProjectContractRsDto::getProjectCode).collect(Collectors.toList());
                        vo.setOccupyProjectCode(String.join(",", projectCodeList));
                    }
                }
            });
        }
        contracts.sort(Comparator.comparingInt(ContractVO::getTopFlag).reversed());
        PageInfo<ContractVO> pageInfo = BeanConverter.convertPage(contracts, ContractVO.class);
        List<ContractVO> list = pageInfo.getList();
        packageContractVO(list);
        return pageInfo;
    }

    public void packageContractVO(List<ContractVO> list) {
        if (ListUtils.isNotEmpty(list)) {
            list.forEach(contractVO -> {
                // 客户
                contractHelper.formatCustomer(contractVO);
                final UserInfo manager = CacheDataUtils.findUserById(contractVO.getManager());
                if (manager != null) {
                    contractVO.setManagerName(manager.getName());
                }
                // 利润部门
                Unit unit = CacheDataUtils.findUnitById(contractVO.getProfitDepartmentId());
                if (unit != null) {
                    contractVO.setProfitDepartmentName(unit.getUnitName());
                }
                // 销售经理
                if (contractVO.getSalesManager() != null) {
                    final UserInfo salesManager = CacheDataUtils.findUserById(contractVO.getSalesManager());
                    if (salesManager != null) {
                        contractVO.setSalesManagerName(salesManager.getName());
                    }
                }
                // 业务实体
                contractVO.setOuName(CacheDataUtils.findOuNameById(contractVO.getOuId()));
            });
        }
    }

    /**
     * 选择合同权限控制
     *
     * @param projectId
     * @return
     */
    public List<Long> buildOus(Long projectId) {
        final List<Long> ous = new ArrayList<>();
        ous.add(NOT_EXIST_ID);
        if (projectId == null) {
            // 立项场景
            List<OperatingUnitDto> operatingUnits = basedataExtService.queryCurrentUnitOu();
            if (CollectionUtils.isNotEmpty(operatingUnits)) {
                for (OperatingUnit ou : operatingUnits) {
                    ous.add(ou.getId());
                }
            }
        } else {
            Project project = projectMapper.selectByPrimaryKey(projectId);
            if (project != null) {
                // 立项重新编辑场景
                if (Objects.equals(project.getStatus(), ProjectStatus.SAVE.getCode())
                        || Objects.equals(project.getStatus(), ProjectStatus.REFUSE.getCode())
                        || Objects.equals(project.getStatus(), ProjectStatus.RETURN.getCode())) {
                    List<OperatingUnitDto> operatingUnits = basedataExtService.queryCurrentUnitOu();
                    if (CollectionUtils.isNotEmpty(operatingUnits)) {
                        for (OperatingUnit ou : operatingUnits) {
                            ous.add(ou.getId());
                        }
                    }
                    // 预立项转正&关联合同变更场景，应该限制选择与项目业务实体不一致的合同  BUG2023041260225
                } else {
                    ous.add(project.getOuId());
                }
            }
        }
        return ous;
    }

    private Map<String, Object> buildParam(String code, String name, Long projectTypeId, Long projectId, List<Long> contractIdList) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("projectTypeId", projectTypeId);
        param.put("projectId", projectId);
        param.put("ouId", buildOus(projectId));
        param.put("contractIdList", contractIdList);
        return param;
    }

    @Override
    public ContractVO getContractDetailWithBudget(Long id) {
        final Contract contract = contractMapper.selectByPrimaryKey(id);
        if (contract == null) {
            return null;
        }
        ContractVO contractVO = new ContractVO();
        BeanConverter.copy(contract, contractVO);
        // 用于项目分级 added at 20200218
        if (contract.getParentId() != null && !contract.getParentId().equals("")) {
            final Contract parentContract = contractMapper.selectByPrimaryKey(contract.getParentId());
            if (parentContract != null) {
                contractVO.setParentAmount(parentContract.getAmount());
                //子合同没有关联赢单商机的话，取主合同的赢单商机
                if (contractVO.getBusinessId() == null) {
                    contractVO.setBusinessId(parentContract.getBusinessId());
                }
                contractVO.setConversionType(parentContract.getConversionType());
                contractVO.setConversionDate(parentContract.getConversionDate());
                contractVO.setConversionRate(parentContract.getConversionRate());
            }
        }
        contractHelper.formatContractVO(contractVO);

        ContractVO.Budget budget = new ContractVO.Budget();
        // 合同物料总预算
        BigDecimal materialCost = BigDecimal.ZERO;
        // 人力总预算
        BigDecimal manpowerCost = BigDecimal.ZERO;
        // 合同差旅费
        BigDecimal travelCost = BigDecimal.ZERO;
        // 合同非差旅费
        BigDecimal otherCost = BigDecimal.ZERO;

        // 构建预算信息
        ContractProductExample contractProductExample = new ContractProductExample();
        final ContractProductExample.Criteria criteria = contractProductExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andContractIdEqualTo(id);

        final List<ContractProduct> contractProducts = contractProductMapper.selectByExample(contractProductExample);
        if (ListUtils.isNotEmpty(contractProducts)) {
            // 所有费用类型
/*            final List<FeeItemDto> feeItemDtoList = CacheDataUtils.findAllFeeItem();
            Map<Long, FeeItemDto> feeItemMap = new HashMap<>();
            feeItemDtoList.forEach(feeItemDto -> feeItemMap.put(feeItemDto.getId(), feeItemDto));*/
            for (ContractProduct contractProduct : contractProducts) {
                final Long productTypeId = contractProduct.getProductTypeId();
                /*final FeeItemDto feeItemDto = feeItemMap.get(productTypeId);*/
                //edit by ex_xuwj4 begin
                final Map<String, Object> param_2 = new HashMap<>();
                FeeItemDto feeItemDto = null;
                if (null != productTypeId) {
                    param_2.put("id", productTypeId);
                    final String url_2 = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/feeItem/findById", param_2);
                    String res = restTemplate.getForEntity(url_2, String.class).getBody();
                    DataResponse<FeeItem> feeItemDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<FeeItem>>() {
                    });
                    FeeItem feeItem = feeItemDataResponse.getData();
                    feeItemDto = BeanConverter.copy(feeItem, FeeItemDto.class);
                }
                //edit by ex_xuwj4 end
                if (feeItemDto != null) {
                    final String name = feeItemDto.getName();
                    if ("产品(硬件/软件)".equals(name)) {
                        materialCost = materialCost.add(contractProduct.getCost());
                    } else if ("人力".equals(name)) {
                        manpowerCost = manpowerCost.add(contractProduct.getCost());
                    } else if ("差旅".equals(name)) {
                        travelCost = travelCost.add(contractProduct.getCost());
                    } else {
                        otherCost = otherCost.add(contractProduct.getCost());
                    }
                }
            }
        }

        budget.setCost(contract.getCost());
        budget.setManpowerCost(manpowerCost);
        budget.setMaterialCost(materialCost);
        budget.setTravelCost(travelCost);
        budget.setOtherCost(otherCost);
        //通过网关跨域查找用户信息
        final Map<String, Object> params = new HashMap<>();
        List<Long> ouList = SystemContext.getOus();
        String ous = "";
        for (Long ou : ouList) {
            ous = ous + String.valueOf(ou) + ",";
        }
        params.put("ous", ous);
        final String urls = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/list", params);
        ResponseEntity<String> responseEntitys = restTemplate.getForEntity(urls, String.class);
        final PageInfo<OrganizationRelDto> dataResponses =
                JSON.parseObject(responseEntitys.getBody(), new TypeReference<PageInfo<OrganizationRelDto>>() {
                });

        List<String> companyList = new ArrayList<>();
        if (dataResponses != null && dataResponses.getList() != null) {
            List<OrganizationRelDto> organizationRelDtos = dataResponses.getList();
            for (OrganizationRelDto organizationRelDto : organizationRelDtos) {
                String s = "E0" + organizationRelDto.getCompanyCode();
                companyList.add(s);
            }
        }
        contractVO.setResourcePlans(rdmResourcePlanExtMapper.getByContractId(id, companyList));
        contractVO.setBudget(budget);

        return contractVO;
    }

    @Override
    public PageInfo<ContractVO> pageContractsByCustomer(Long customerId, int pageNumber, int pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        Map<String, Object> param = new HashMap<>();

        // 使用单位下所有二级虚拟部门
        final List<UnitDto> unitDtoList = getUnitByParentId(SystemContext.getUnitId());
        List<Long> allUnits = new ArrayList<>();
        if (ListUtils.isNotEmpty(unitDtoList)) {
            unitDtoList.forEach(unitDto -> {
                allUnits.add(unitDto.getId());
            });
        } else {
            allUnits.add(NOT_EXIST_ID);
        }
        param.put("allUnits", allUnits);
        param.put("customerId", customerId);

        final List<Contract> contracts = contractExtMapper.listContractsByCustomer(param);
        final PageInfo<ContractVO> pageInfo = BeanConverter.convertPage(contracts, ContractVO.class);
        final List<ContractVO> list = pageInfo.getList();
        if (ListUtils.isNotEmpty(list)) {
            list.forEach(contractVO -> {
                contractHelper.formatCustomer(contractVO);

                contractHelper.formatManager(contractVO);

                contractHelper.formatSalesManager(contractVO);

                contractHelper.formatUnit(contractVO);

                final UserInfo userInfo = CacheDataUtils.findUserById(contractVO.getManager());
                if (userInfo != null) {
                    contractVO.setManagerName(userInfo.getName());
                }

                // 回款率
                BigDecimal receiptRate = contractHelper.calculateContractReceiptRate(contractVO);
                contractVO.setReceiptRate(receiptRate);
            });
        }

        //TODO 变更数

        return pageInfo;
    }

    private void checkBaseInfoParam(ContractDTO contractDTO) {
        // 必填校验
        Assert.notNull(contractDTO.getName(), "合同名称不能为空");
        Assert.notNull(contractDTO.getCustomerId(), "客户不能为空");
        Assert.notNull(contractDTO.getStartTime(), "合同有效期不能为空");
        Assert.notNull(contractDTO.getEndTime(), "合同有效期不能为空");
        Assert.notNull(contractDTO.getOriginalContractAnnex(), "合同原件不能为空");
        Assert.notNull(contractDTO.getOuId(), "所属业务实体不能为空");
        Assert.notNull(contractDTO.getUnitId(), "销售部门不能为空");
        Assert.notNull(contractDTO.getSalesManager(), "销售经理不能为空");
        Assert.notNull(contractDTO.getManager(), "项目经理不能为空");
        Assert.notNull(contractDTO.getClasses(), "合同等级不能为空");
        Assert.notNull(contractDTO.getInvoiceType(), "发票类型不能为空");
        if (Objects.isNull(contractDTO.getIsSynchronizeLegalSystemFlag())) {
            throw new MipException("是否同步法务系统不能为空");
        }

        if (Boolean.FALSE.equals(contractDTO.getIsSynchronizeLegalSystemFlag())) {
            //法务合同与普通合同共用一个字段originalContractAnnex,无需判断合同是否上传
            Assert.notBlank(contractDTO.getNotsyncType(), "不同步法务类型不能为空");
            Assert.notBlank(contractDTO.getNotsyncReason(), "不同步法务原因不能为空");
            //当不同步原因选择的类型=已在法务系统双签，则“法务系统合同编号”为必填
            if (Objects.equals("已在法务系统双签", contractDTO.getNotsyncType()) || Objects.equals("已在法务系统签订框架合同", contractDTO.getNotsyncType())) {
                Assert.notBlank(contractDTO.getLegalContractNum(), "法务系统合同编号不能为空");
            }
        }
        //是否提交确认材料	选择“是”，则“合同附件”为必填，选择“否”，则“合同附件”为非必填
        if (Objects.equals(Boolean.TRUE, contractDTO.getWhetherCustomerConfirm())) {
            Assert.notBlank(contractDTO.getAnnex(), "是否提交确认材料为【是】合同附件不能为空");
        }


        // 校验社会统一信用代码
        CustomerDto customer = contractHelper.getCustomer(contractDTO.getCustomerId());
        if (Boolean.TRUE.equals(contractDTO.getIsElectronicContract()) && StringUtils.isEmpty(customer.getRegistrationNumber())) {
            throw new MipException("主体缺少社会统一信用代码，请走纸质合同");
        }

        //合同录入，重新编辑，同步到美的开票平台改成了同步到开票易，开票易不存在发票类型：2-电子专票 3-电子普票
        List<String> invoiceWay = new ArrayList<>(organizationCustomDictService.queryByName("开票方式", contractDTO.getOuId(), OrgCustomDictOrgFrom.OU));
        if ((invoiceWay.isEmpty() || "0".equals(invoiceWay.get(0))) //开票易
                && (Objects.equals(contractDTO.getInvoiceType(), InvoicePlanInvoiceType.VAT_SPECIAL_ELECTRON.getCode()) || Objects.equals(contractDTO.getInvoiceType(), InvoicePlanInvoiceType.VAT_GENERAL_ELECTRON.getCode()))) {
            throw new MipException("发票类型错误，请重新选择");
        }
    }

    /**
     * 组装查询条件
     *
     * @param contract 查询对象
     * @param isReturn
     * @param param
     * @param isMaster 是否主合同
     * @return 是否继续查询，true 继续；false 直接返回
     */
    private boolean buildListContractParam(ContractDTO contract, boolean isReturn, Map<String, Object> param, Boolean isMaster) {

        boolean flag = true;

        // 合同编号
        if (StringUtils.isNotEmpty(contract.getCode())) {
            param.put("code", contract.getCode());
        }

        // 合同名称
        if (StringUtils.isNotEmpty(contract.getName())) {
            param.put("name", contract.getName());
        }

        // 合同类型
        if (contract.getFrameFlag() != null) {
            param.put("frameFlag", contract.getFrameFlag());
        }

        // 客户
        if (StringUtils.isNotEmpty(contract.getCustomerName())) {
            param.put("customerName", contract.getCustomerName());
        }

        // 客户属性
        if (contract.getCustomerType() != null) {
            final List<CustomerDto> customerDtoList = contractHelper.getCustomerByInner(contract.getCustomerType());
            if (customerDtoList.size() == 0) {
                if (isReturn) {
                    return false;
                }
                param.put("customerIds", Lists.newArrayList(NOT_EXIST_ID));
            } else {
                final List<Long> idList = customerDtoList.stream().map(Customer::getId).collect(Collectors.toList());
                param.put("customerIds", idList);
            }
        }

        // 利润部门
        if (contract.getProfitDepartmentId() != null) {
            param.put("profitDepartmentId", contract.getProfitDepartmentId());
        }
        // 销售部门
        if (isMaster) {
            if (contract.getUnitId() != null) {
                param.put("unitId", contract.getUnitId());
            }
        } else {
            if (contract.getUnitId() != null) {
                param.put("profitDepartmentId", contract.getUnitId());
            }
        }

        // 销售经理
        if (contract.getSalesManagerName() != null) {
            final List<UserInfoDto> users = contractHelper.getUsers(contract.getSalesManagerName());
            final List<Long> userIdList = users.stream().map(UserInfoDto::getId).collect(Collectors.toList());
            if (userIdList.size() > 0) {
                param.put("salesManagers", userIdList);
            } else {
                param.put("salesManagers", Lists.newArrayList(NOT_EXIST_ID));
                if (isReturn) {
                    return false;
                }
                flag = false;
            }
        }

        // 项目经理
        if (contract.getManagerName() != null) {
            final List<UserInfoDto> users = contractHelper.getUsers(contract.getManagerName());
            final List<Long> userIdList = users.stream().map(UserInfoDto::getId).collect(Collectors.toList());
            if (userIdList.size() > 0) {
                param.put("managers", userIdList);
            } else {
                param.put("managers", Lists.newArrayList(NOT_EXIST_ID));
                if (isReturn) {
                    return false;
                }
                flag = false;
            }
        }

        // 状态
        if (contract.getStatus() != null) {
            param.put("status", contract.getStatus());
        }

        // 归档日期
        if (contract.getFilingStartDate() != null) {
            param.put("filingStartDate", contract.getFilingStartDate());
        }

        if (contract.getFilingEndDate() != null) {
            param.put("filingEndDate", contract.getFilingEndDate());
        }

        // 使用单位
        List<Long> allUnits = basedataExtService.findUnitIds(SystemContext.getUnitId());
        if (isMaster) {
            param.put("allUnits", allUnits);
        } else {
            param.put("allProfitDepartmentIds", allUnits);
        }

        if (contract.getMe() != null && contract.getMe()) {
            param.put("isMe", true);
            param.put("userId", SystemContext.getUserId());
        }

        if (contract.getList() != null && contract.getList()) {
            param.put("isList", true);
            List<Long> authorUnits = SystemContext.getSecondUnits();
            if (authorUnits == null || authorUnits.size() == 0) {
                authorUnits = new ArrayList<Long>();
                authorUnits.add(NOT_EXIST_ID);
            }
            //授权的
            if (isMaster) {//主合同授权
                param.put("authorUnits", authorUnits);
            } else {//子合同
                param.put("authorSubUnits", authorUnits);
            }
        }
        return flag;
    }

    @Override
    public List<ContractFinanceVO> listContractByFrameContractId(Long id) {
        ContractExample contractExample = new ContractExample();
        final ContractExample.Criteria criteria = contractExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andFrameContractIdEqualTo(id);
        final List<Contract> contracts = this.selectByExample(contractExample);
        if (ListUtils.isEmpty(contracts)) {
            return Lists.newArrayList();
        }

        BigDecimal hundred = new BigDecimal("100");
        final List<ContractFinanceVO> contractFinanceVOList = BeanConverter.copy(contracts, ContractFinanceVO.class);
        contractFinanceVOList.forEach(contractFinanceVO -> {
            // 合同总额
            final BigDecimal amount = contractFinanceVO.getAmount();

            // 主合同已开票金额
            final BigDecimal invoiceAmount = getParentContractInvoiceAmount(contractFinanceVO.getId());
            // 剩余开票金额
            final BigDecimal unBilledAmount = amount.subtract(invoiceAmount);
            // 开票率
            final BigDecimal invoiceRate = invoiceAmount.divide(amount, 4, BigDecimal.ROUND_HALF_UP).multiply(hundred);

            // 主合同已回款金额
            final BigDecimal receiptAmount = receiptPlanService.getFrameContractReceiptAmount(contractFinanceVO.getId());
            // 剩余回款金额
            final BigDecimal remainingReceiptAmount = amount.subtract(receiptAmount);
            // 回款率
            final BigDecimal receiptRate = receiptAmount.divide(amount, 4, BigDecimal.ROUND_HALF_UP).multiply(hundred);

            contractFinanceVO.setActualInvoiceAmount(invoiceAmount);
            contractFinanceVO.setUnBilledAmount(unBilledAmount);
            contractFinanceVO.setInvoiceRate(invoiceRate);
            contractFinanceVO.setActualReceiptAmount(receiptAmount);
            contractFinanceVO.setRemainingReceiptAmount(remainingReceiptAmount);
            contractFinanceVO.setReceiptRate(receiptRate);
        });

        return contractFinanceVOList;
    }

    private BigDecimal getParentContractInvoiceAmount(Long parentId) {
        final List<ContractVO> childrenContracts = this.getListByParentId(parentId);
        if (ListUtils.isEmpty(childrenContracts)) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalInvoiceAmount = BigDecimal.ZERO;
        for (ContractVO childrenContract : childrenContracts) {
            final BigDecimal contractInvoiceAmount = invoicePlanService.getContractInvoiceAmount(childrenContract.getId());
            totalInvoiceAmount = totalInvoiceAmount.add(contractInvoiceAmount == null ? BigDecimal.ZERO : contractInvoiceAmount);
        }

        return totalInvoiceAmount;
    }

    private BigDecimal getParentContractReceiptAmount(Long parentId) {
        final List<ContractVO> childrenContracts = this.getListByParentId(parentId);
        if (ListUtils.isEmpty(childrenContracts)) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalReceiptAmount = BigDecimal.ZERO;
        for (ContractVO childrenContract : childrenContracts) {
            final BigDecimal contractReceiptAmount = receiptPlanService.getContractReceiptAmount(childrenContract.getId());
            totalReceiptAmount = totalReceiptAmount.add(contractReceiptAmount == null ? BigDecimal.ZERO : contractReceiptAmount);
        }

        return totalReceiptAmount;
    }

    @Override
    public void updateDetail(ContractDTO contractDTO) {
        final Contract contract = contractMapper.selectByPrimaryKey(contractDTO.getId());
        if (contract == null) {
            throw new MipException("合同不存在");
        }

        contract.setRemark(contractDTO.getRemark());
        contract.setSalesManager(contractDTO.getSalesManager());
        contract.setManager(contractDTO.getManager());

        // 是否已上传双章合同
        if (contractDTO.getIsDoubleChapterContract() != null) {
            contract.setIsDoubleChapterContract(contractDTO.getIsDoubleChapterContract());
        }
        // 合同原件
        if (contractDTO.getOriginalContractAnnex() != null) {
            contract.setOriginalContractAnnex(contractDTO.getOriginalContractAnnex());
        }

        ContractExample example = new ContractExample();
        ContractExample.Criteria criteria = example.createCriteria();
        criteria.andParentIdEqualTo(contract.getId());

        List<Contract> list = contractMapper.selectByExample(example);
        if (list != null && list.size() != 0) {
            for (Contract dbzContract : list) {
                dbzContract.setSalesManager(contractDTO.getSalesManager());
                dbzContract.setUpdateAt(new Date());
                dbzContract.setUpdateBy(SystemContext.getUserId());
                contractMapper.updateByPrimaryKey(dbzContract);
            }
        }

        update(contract);
    }

    @Override
    public List<ContractVO> listByCustomerId(ContractDTO contractDTO) {
        ContractExample example = new ContractExample();
        ContractExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());

        if (contractDTO.getCustomerId() != null) {
            criteria.andCustomerIdEqualTo(contractDTO.getCustomerId());
        }
        if (ListUtil.isPresent(contractDTO.getStatuses())) {
            criteria.andStatusIn(contractDTO.getStatuses());
        }
        List<Contract> list = this.selectByExample(example);
        if (ListUtil.isPresent(list)) {
            List<ContractVO> vos = BeanConverter.copy(list, ContractVO.class);
            return vos;
        }

        return new ArrayList<>();
    }

    @Override
    public void abandon(Long id, String invalidReason) {
        Assert.notNull(id, "合同ID不能为空");
        //更新合同状态
        this.updateContractStatus(id, invalidReason, ContractStatus.CANCEL.getCode());
    }

    public String getFormUrl(Integer status) {
        String url = "";
        switch (status) {
            case 3:
                url = "contractApp";
                break;
            case 9:
                url = "contractApp";
                break;
            default:
                break;
        }
        return url;
    }


    /**
     * 判断当前登录用户是否创建人
     *
     * @param contractId
     * @return boolean true是 false否
     */
    @Override
    public Boolean isCreater(Long contractId) {
        Contract contract = contractMapper.selectByPrimaryKey(contractId);
        return isCreater(contract);
    }

    /**
     * 判断当前登录用户是否创建人
     *
     * @param contract
     * @return boolean true是 false否
     */
    private Boolean isCreater(Contract contract) {
        if (contract == null) {
            return false;
        }
        Long id = contract.getCreateBy();
        if (id == null) {
            return false;
        }
        long currentUserId = SystemContext.getUserId();//当前登录用户ID
        if (id.longValue() == currentUserId) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Boolean isManager(Long contractId) {
        Contract contract = contractMapper.selectByPrimaryKey(contractId);
        return isManager(contract);
    }

    public Boolean isManager(Contract contract) {
        if (contract == null) {
            return false;
        }
        Long id = contract.getManager();
        if (id == null) {
            return false;
        }
        long currentUserId = SystemContext.getUserId();//当前登录用户ID
        if (id.longValue() == currentUserId) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断当前登录用户是否销售经理
     *
     * @param contractId
     * @return boolean true是 false否
     */
    @Override
    public Boolean isSaleManager(Long contractId) {
        Contract ontract = contractMapper.selectByPrimaryKey(contractId);
        return isSaleManager(ontract);
    }


    /**
     * 判断当前登录用户是否销售经理
     *
     * @param ontract
     * @return boolean true是 false否
     */
    private Boolean isSaleManager(Contract ontract) {
        if (ontract == null) {
            return false;
        }
        Long id = ontract.getSalesManager();
        if (id == null) {
            return false;
        }
        long currentUserId = SystemContext.getUserId();//当前登录用户ID
        if (id.longValue() == currentUserId) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断当前登录用户是否有数据权限
     *
     * @param contractId
     * @return boolean true是 false否
     */
    @Override
    public Boolean isDataAuthor(Long contractId) {
        Contract contract = contractMapper.selectByPrimaryKey(contractId);
        return isDataAuthor(contract);
    }

    @Override
    public Boolean isFinance(Long contractId) {
        Contract contract = contractMapper.selectByPrimaryKey(contractId);
        return isFinance(contract);
    }

    @Override
    public Boolean isContractIsView(Long contractId) {
        Contract contract = this.contractMapper.selectByPrimaryKey(contractId);
        return isContractIsView(contract);
    }

    @Override
    public void saveChildrenContractChangeInfo(Long contractId, Long changewayId) {
        ContractVO contract = this.findDetailById(contractId);
        if (Objects.isNull(contract) || CollectionUtils.isEmpty(contract.getChildrenContracts())) {
            return;
        }

        for (ContractVO childrenContract : contract.getChildrenContracts()) {
            ContractHis childrenContractHis = BeanConverter.copy(childrenContract, ContractHis.class);
            childrenContractHis.setContractId(childrenContractHis.getId());
            childrenContractHis.setId(null);
            childrenContractHis.setChangewayId(changewayId);
            contractHisMapper.insert(childrenContractHis);
            logger.info("子合同记录表已被插入,childrenContractHis: " + childrenContractHis.toString());

            //插入子合同产品计划记录
            List<ContractProductHis> childrenContractProductHiss = BeanConverter.copy(childrenContract.getContractProducts(), ContractProductHis.class);
            if (CollectionUtils.isNotEmpty(childrenContractProductHiss)) {
                for (ContractProductHis childrenContractProductHis : childrenContractProductHiss) {
                    childrenContractProductHis.setId(null);
                    childrenContractProductHis.setChangewayId(changewayId);
                    contractProductHisMapper.insert(childrenContractProductHis);
                    logger.info("子合同产品计划记录表已被插入,childrencontractProductHis:" + childrenContractProductHis.toString());
                }
            }
            //插入子合同产品成本计划记录
            List<ContractProductCostHis> childrenContractProductCostHiss = BeanConverter.copy(childrenContract.getContractProductCosts(), ContractProductCostHis.class);
            if (CollectionUtils.isNotEmpty(childrenContractProductCostHiss)) {
                for (ContractProductCostHis childrenContractProductCostHis : childrenContractProductCostHiss) {
                    childrenContractProductCostHis.setId(null);
                    childrenContractProductCostHis.setChangewayId(changewayId);
                    contractProductCostHisMapper.insert(childrenContractProductCostHis);
                    logger.info("子合同产品成本计划记录表已被插入,childrenContractProductCostHis: " + childrenContractProductCostHis.toString());
                }
            }
            //插入子合同开票计划以及详情
            if (CollectionUtils.isNotEmpty(childrenContract.getInvoicePlans())) {
                for (InvoicePlanDTO childrenInvoicePlanDTO : childrenContract.getInvoicePlans()) {
                    InvoicePlanHis childrenInvoicePlanHis = BeanConverter.copy(childrenInvoicePlanDTO, InvoicePlanHisDTO.class);
                    childrenInvoicePlanHis.setId(null);
                    childrenInvoicePlanHis.setChangewayId(changewayId);
                    childrenInvoicePlanHis.setCustomerId(contract.getCustomerId());
                    childrenInvoicePlanHis.setCustomerName(contract.getCustomerName());
                    invoicePlanHisMapper.insert(childrenInvoicePlanHis);
                    logger.info("子合同开票计划记录表已被插入,id= " + childrenInvoicePlanHis.getId());
                    List<InvoicePlanDetailHisDTO> invoicePlanDetailHiss = BeanConverter.copy(childrenInvoicePlanDTO.getInvoicePlanDetails(), InvoicePlanDetailHisDTO.class);
                    if (CollectionUtils.isNotEmpty(invoicePlanDetailHiss)) {
                        for (InvoicePlanDetailHis invoicePlanDetailHis : invoicePlanDetailHiss) {
                            invoicePlanDetailHis.setId(null);
                            invoicePlanDetailHis.setChangewayId(changewayId);
                            invoicePlanDetailHisMapper.insert(invoicePlanDetailHis);
                            logger.info("子合同开票计划详情记录表已被插入,childrenInvoicePlanDetailHis: " + invoicePlanDetailHis.getId());
                        }
                    }
                }
            }
            //插入子合同回款计划、详情以及回款开票关系记录数据
            if (CollectionUtils.isNotEmpty(childrenContract.getReceiptPlans())) {
                for (ReceiptPlanDTO childrenReceiptPlanDTO : childrenContract.getReceiptPlans()) {
                    ReceiptPlanHis childrenReceiptPlanHis = BeanConverter.copy(childrenReceiptPlanDTO, ReceiptPlanHisDTO.class);
                    childrenReceiptPlanHis.setId(null);
                    childrenReceiptPlanHis.setChangewayId(changewayId);
                    receiptPlanHisMapper.insert(childrenReceiptPlanHis);
                    logger.info("子合同回款计划记录表已被插入,childrenReceiptPlanHis: " + childrenReceiptPlanHis.toString());
                    //插入详情
                    if (CollectionUtils.isNotEmpty(childrenReceiptPlanDTO.getReceiptPlanDetails())) {
                        for (ReceiptPlanDetailDTO childrenReceiptPlanDetailDTO : childrenReceiptPlanDTO.getReceiptPlanDetails()) {
                            ReceiptPlanDetailHis childrenReceiptPlanDetailHis = BeanConverter.copy(childrenReceiptPlanDetailDTO, ReceiptPlanDetailHisDTO.class);
                            childrenReceiptPlanDetailHis.setId(null);
                            childrenReceiptPlanDetailHis.setChangewayId(changewayId);
                            receiptPlanDetailHisMapper.insert(childrenReceiptPlanDetailHis);
                            logger.info("子合同回款详情计划记录表已被插入,childrenReceiptPlanDetailHis: " + childrenReceiptPlanDetailHis.toString());

                            List<ReceiptInvoiceRelationHisDTO> childrenReceiptInvoiceRelationHisDTOs = BeanConverter.copy(childrenReceiptPlanDetailDTO.getReceiptInvoiceRelations(), ReceiptInvoiceRelationHisDTO.class);
                            if (CollectionUtils.isNotEmpty(childrenReceiptInvoiceRelationHisDTOs)) {
                                for (ReceiptInvoiceRelationHisDTO childrenReceiptInvoiceRelationHisDTO : childrenReceiptInvoiceRelationHisDTOs) {
                                    ReceiptInvoiceRelationHis childrenReceiptInvoiceRelationHis = childrenReceiptInvoiceRelationHisDTO.conventToReceiptInvoiceRelationHis();
                                    childrenReceiptInvoiceRelationHis.setId(null);
                                    childrenReceiptInvoiceRelationHis.setChangewayId(changewayId);
                                    childrenReceiptInvoiceRelationHis.setDeletedFlag(false);
                                    receiptInvoiceRelationHisMapper.insert(childrenReceiptInvoiceRelationHis);
                                    logger.info("子合同回款开票关系记录表已被插入,receiptInvoiceRelationHis: " + childrenReceiptInvoiceRelationHis.toString());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * addedby ex_pengzhong2 20191121 15:00
     *
     * @param contractHisDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long changeBaseType(ContractHisDTO contractHisDTO) {

        // 检查合同变更重复流程
        checkExistsContractChange(contractHisDTO);

        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(contractHisDTO.getChangewayId());

        //数据校验
        contractModifyCheck.baseInfoParamCheck(contractHisDTO, CHANGE_TYPE_ONE);

        if (contractHisDTO.getChangewayId() != null && contractChangeway.getChangeType().equals(CHANGE_TYPE_ONE)) {
            if (Objects.equals(ContractChangewayStatus.PENDING.getCode(), contractChangeway.getApprovalStatus()) || Objects.equals(ContractChangewayStatus.APPROVED.getCode(), contractChangeway.getApprovalStatus())) {
                throw new BizException(Code.ERROR, "流程提交后不允许再次保存草稿！");
            }

            contractChangeway.setAttachId(contractHisDTO.getAttachId());
            contractChangeway.setChangeReasonType(contractHisDTO.getChangeReasonType());
            contractChangeway.setChangeReasonTypeId(contractHisDTO.getChangeReasonTypeId());
            contractChangeway.setSealAdminAccountIds(contractHisDTO.getSealAdministratorChangeIds());
            contractChangeway.setIsElectronicContract(contractHisDTO.getIsElectronicContract());
            contractChangeway.setSealCategory(contractHisDTO.getSealCategory());
            contractChangeway.setChangeProtocolFile(contractHisDTO.getChangeProtocolFile());
            contractChangeway.setOtherName(contractHisDTO.getOtherName());
            contractChangeway.setOtherPhone(contractHisDTO.getOtherPhone());
            contractChangeway.setOtherEmail(contractHisDTO.getOtherEmail());
            contractChangewayMapper.updateByPrimaryKey(contractChangeway);
            //只作更新操作
            ContractHis contractHis = contractHisDTO.convertToContractHis();
            ContractHisExample contractHisExample = new ContractHisExample();
            ContractHisExample.Criteria criteria = contractHisExample.createCriteria();
            criteria.andContractIdEqualTo(contractHis.getContractId());
            criteria.andChangewayIdEqualTo(contractHis.getChangewayId());
            List<ContractHis> contractHisList = contractHisMapper.selectByExample(contractHisExample);
            if (CollectionUtils.isNotEmpty(contractHisList)) {
                contractHis.setId(contractHisList.get(0).getId());
            } else {
                throw new MipException("没找到对应主合同id！");
            }
            //没错，我不信任前端，老是坑我，FileAttachmentId设值后恒不变
            ContractHis contractHisOrigin = contractHisMapper.selectByPrimaryKey(contractHis.getId());
            if (contractHisOrigin.getFileAttachmentId() != null) {
                contractHis.setFileAttachmentId(contractHisOrigin.getFileAttachmentId());
            }
            contractHisMapper.updateByPrimaryKey(contractHis);
            final List<ContractHisDTO> childrenContractHisDTOList = contractHisDTO.getChildrenContractHiss();
            childrenContractHisDTOList.forEach(contractHisDTO1 -> {
                ContractHis childrenContractHis = contractHisDTO1.convertToContractHis();
//                if (contractHisDTO1.getStartTime()==null||contractHisDTO1.getEndTime()==null){
//                    throw new MipException("请填写子合同中的生效和失效日期！");
//                }
                ContractHisExample childrenContractHisExample = new ContractHisExample();
                ContractHisExample.Criteria childrenCriteria = childrenContractHisExample.createCriteria();
                childrenCriteria.andContractIdEqualTo(contractHisDTO1.getContractId());
                childrenCriteria.andChangewayIdEqualTo(contractHisDTO.getChangewayId());
                List<ContractHis> childrenContractHisList = contractHisMapper.selectByExample(childrenContractHisExample);
                if (CollectionUtils.isNotEmpty(childrenContractHisList)) {
                    childrenContractHis.setId(childrenContractHisList.get(0).getId());
                    childrenContractHis.setContractId(contractHisDTO1.getContractId());
                    childrenContractHis.setChangewayId(contractHisDTO.getChangewayId());
                } else {
                    throw new MipException("没找到对应子合同id！");
                }
                contractHisMapper.updateByPrimaryKey(childrenContractHis);
            });
            return contractHisDTO.getChangewayId();
        } else {
            if (ObjectUtils.isEmpty(contractHisDTO) || CollectionUtils.isEmpty(contractHisDTO.getChildrenContractHiss())) {
                throw new MipException("主,子合同都不能为空！");
            }
            // 取被变更的主合同
            Contract contract = contractMapper.selectByPrimaryKey(contractHisDTO.getContractId());
            /**
             * ------------------------------------ 首次用户基本信息变更开始..... -------------------------------------------
             */
            logger.info("销售合同基础信息变更开始...");
            //插入合同变更记录头表
            Long changewayId = insertToContractChangewayType(contractHisDTO, 1);
            logger.info("变更记录表已被插入,changewayId= " + changewayId);
            //插入合同记录表（主）,插入的是页面数据
            ContractHis contractHis = contractHisDTO.convertToContractHis();
            contractHis.setId(null);
            contractHis.setChangewayId(changewayId);
            //主合同有效期
            Date startTime = contractHis.getStartTime();
            Date endTime = contractHis.getEndTime();
            //主合同的毛利率
            List<BigDecimal> bigDecimals = contractHisDTO.getChildrenContractHiss().stream().filter(obj -> null != obj.getProcessGrossProfitRatio()).map(ContractHisDTO::getProcessGrossProfitRatio).collect(Collectors.toList());
            if (!ObjectUtil.hasEmpty(bigDecimals)) {
                BigDecimal processGrossProfitRatio = contractHisDTO.getChildrenContractHiss().stream().
                        map(entity -> Optional.ofNullable(entity.getAmount()).orElse(BigDecimal.ZERO).multiply(Optional.ofNullable(entity.getProcessGrossProfitRatio()).orElse(BigDecimal.ZERO))).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
                contractHis.setProcessGrossProfitRatio(processGrossProfitRatio.divide((Objects.nonNull(contractHis.getAmount()) ? contractHis.getAmount() : BigDecimal.ZERO), 4, RoundingMode.HALF_UP));
            }
            contractHisMapper.insert(contractHis);
            logger.info("主合同记录表已被插入,id= " + contractHis.getId());
            //插入合同产品(主)，插入的是页面数据
            if (CollectionUtils.isNotEmpty(contractHisDTO.getContractProductHiss()) && contractHisDTO.getContractProductHiss().size() > 0) {
                //合同产品记录(主)
                ContractProductHis contractProductHis = contractHisDTO.getContractProductHiss().get(0);
                contractProductHis.setId(null);
                contractProductHis.setChangewayId(changewayId);
                contractProductHisMapper.insert(contractProductHis);
                logger.info("合同产品(主)记录表已被插入,contractProductHis: " + contractProductHis.toString());
            }
            //插入子合同记录，包括其产品，成本，开票「详情」，回款「详情，开票回款关联」
            final List<ContractHisDTO> childrenContractHisDTOs = contractHisDTO.getChildrenContractHiss();

            childrenContractHisDTOs.forEach(childrenContractHisDTO -> {
                Contract childrenContract = contractMapper.selectByPrimaryKey(childrenContractHisDTO.getContractId());
                Boolean isCustomerChanged = childrenContract.getCustomerId().equals(childrenContractHisDTO.getCustomerId());
                Boolean isOuIdChanged = childrenContract.getOuId().equals(childrenContractHisDTO.getOuId());
                // 对于关联了eam采购申请的子合同不允许变更
                if (childrenContractHisDTO.getBusinessTypeId() != null) {
                    final ProjectType oriProjectType = projectTypeMapper.selectByPrimaryKey(childrenContract.getBusinessTypeId());
                    final ProjectType projectTypeHis = projectTypeMapper.selectByPrimaryKey(childrenContractHisDTO.getBusinessTypeId());
                    if (StringUtils.isNotEmpty(oriProjectType.getName()) && oriProjectType.getName().contains("人力外包（RDM）")) {
                        if (StringUtils.isNotEmpty(projectTypeHis.getName()) && !projectTypeHis.getName().contains("人力外包（RDM）")) {
                            throw new MipException("该子合同已经关联采购申请，不允许对该子合同的业务模式进行变更，如需更改请走服务内容变更！");
                        }
                    }
                }

                Contract contract1 = new Contract();
                contract1.setId(childrenContract.getId());
                contract1.setChangewayId(changewayId);
                contractMapper.updateByPrimaryKeySelective(contract1);
                //取该合同关联的项目
                ProjectContractRsExample projectContractRsExample = new ProjectContractRsExample();
                ProjectContractRsExample.Criteria criteria = projectContractRsExample.createCriteria();
                criteria.andContractIdEqualTo(childrenContractHisDTO.getContractId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                // 合同关联项目时，合同失效日期必须大于项目已结转的会计日期节点!
                List<ProjectContractRs> projectContractRsList = projectContractRsMapper.selectByExample(projectContractRsExample);
                if (CollectionUtils.isNotEmpty(projectContractRsList)) {
                    ProjectProfitDto projectProfitDto = projectProfitService.findProfitDetail(projectContractRsList.get(0).getProjectId());
                    List<ProjectMilepostDto> projectMilepostList = projectProfitDto.getMainMileposts();
                    ProjectIncomeCostPlanExample projectIncomeCostPlanExample = new ProjectIncomeCostPlanExample();
                    ProjectIncomeCostPlanExample.Criteria projectIncomeCostPlanExampleCriteria = projectIncomeCostPlanExample.createCriteria();
                    projectIncomeCostPlanExampleCriteria.andProjectIdEqualTo(projectContractRsList.get(0).getProjectId()).andDeletedFlagEqualTo(Boolean.FALSE);
                    List<ProjectIncomeCostPlan> projectIncomeCostPlans = projectIncomeCostPlanMapper.selectByExample(projectIncomeCostPlanExample);

                    if (CollectionUtils.isNotEmpty(projectIncomeCostPlans)) {
                        for (ProjectIncomeCostPlan projectIncomeCostPlan : projectIncomeCostPlans) {
                            if (projectIncomeCostPlan.getCarryoverBillId() != null && Objects.equals(projectIncomeCostPlan.getCarryStatus(), "1")) {
                                if (!isCustomerChanged) {
                                    throw new MipException("合同被项目关联，项目已经结转时，不允许变更客户！");
                                }
                            }
                        }
                    }
                    RevenueCostOrderExample revenueCostOrderExample = new RevenueCostOrderExample();
                    RevenueCostOrderExample.Criteria revenueCostOrderCriteria = revenueCostOrderExample.createCriteria();
                    revenueCostOrderCriteria.andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(projectContractRsList.get(0).getProjectId())
                            .andStatusNotEqualTo(RevenueCostOrderTypeEnums.CANCEL.getCode());
                    List<RevenueCostOrder> revenueCostOrders = revenueCostOrderMapper.selectByExample(revenueCostOrderExample);
                    if (CollectionUtils.isNotEmpty(revenueCostOrders)) {
                        BigDecimal totalRevenue =
                                revenueCostOrders.stream()
                                        .map(e -> Optional.ofNullable(e.getRevenue()).orElse(BigDecimal.ZERO))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add); //累计工单收入
                        if (!isCustomerChanged && totalRevenue.compareTo(BigDecimal.ZERO) != 0) {
                            throw new MipException("项目存在收入成本工单不允许变更客户!");
                        }
                    }

                    //项目已结转不允许客户变更
                    for (ProjectMilepostDto projectMilepost : projectMilepostList) {
                        // incomeFlag为true且carryoverBillId不为空,则为项目已结转
                        if (!ObjectUtils.isEmpty(projectMilepost.getIncomeFlag()) && projectMilepost.getIncomeFlag() && null != projectMilepost.getCarryoverBillId()) {
                            if (!isCustomerChanged) {
                                throw new MipException("合同被项目关联，项目已经结转时，不允许变更客户！");
                            }
                        }
                    }
                }

                final ContractHis childrenContractHis = childrenContractHisDTO.convertToContractHis();
                childrenContractHis.setId(null);
                childrenContractHis.setChangewayId(changewayId);
                //子合同有效期
                Date childStartTime = childrenContractHis.getStartTime();
                Date childEndTime = childrenContractHis.getEndTime();
/*                if (childStartTime == null|| childEndTime==null){
                    throw new MipException("请填写子合同的生效和失效日期！");
                }*/
                //子合同生效日期必须在合同有效期之间
//                if (startTime.compareTo(childStartTime)>0 || endTime.compareTo(childEndTime)<0){
//                    throw new MipException("子合同生效日期必须在合同有效期之间!");
//                }
                /**
                 * 主合同客户变动-->子合同客户变动-->开票计划客户变动-->且子合同关联项目时，项目上的客户也需要变动【该步骤，需要审批通过才去执行】,
                 * 前提是子合同没有开过票或者回过款，才允许变动
                 */

                // 判断该子合同是否已开过票
                Boolean isInvoice = isInvoice(childrenContractHis.getContractId());
                // 判断该子合同是否已经回过款
                Boolean isReceipt = isReceipt(childrenContractHis.getContractId());

                //子合同已经开过票，回过款都不允许变更业务实体OU
                if (!isOuIdChanged) {
                    if (isInvoice || isReceipt) {
                        throw new MipException("子合同已经开过票或者回过款都不允许变更业务实体OU！");
                    }
                }

                //子合同已经开过票，回过款都不允许变更客户
                if (!isCustomerChanged && (isInvoice || isReceipt)) {
                    throw new MipException("该合同已经开过票或者回过款，请联系财务完成作废发票、重新认领回款等一系列操作后，再发起客户变更!");
                }
                childrenContractHis.setOuId(contractHisDTO.getOuId());
                childrenContractHis.setUnitId(contractHisDTO.getUnitId());
                contractHisMapper.insert(childrenContractHis);
                logger.info("子合同记录表已被插入,childrenContractHis: " + childrenContractHis.toString());
                //插入子合同产品计划记录
                List<ContractProductHis> childrenContractProductHiss = childrenContractHisDTO.getContractProductHiss();
                if (CollectionUtils.isNotEmpty(childrenContractProductHiss) && childrenContractProductHiss.size() > 0) {
                    for (ContractProductHis childrenContractProductHis : childrenContractProductHiss) {
                        childrenContractProductHis.setId(null);
                        childrenContractProductHis.setChangewayId(changewayId);
                        contractProductHisMapper.insert(childrenContractProductHis);
                        logger.info("子合同产品计划记录表已被插入,childrencontractProductHis:" + childrenContractProductHis.toString());
                    }
                }
                //插入子合同产品成本计划记录
                List<ContractProductCostHis> childrenContractProductCostHiss = childrenContractHisDTO.getContractProductCostHiss();
                if (CollectionUtils.isNotEmpty(childrenContractProductCostHiss) && childrenContractProductCostHiss.size() > 0) {
                    for (ContractProductCostHis childrenContractProductCostHis : childrenContractProductCostHiss) {
                        childrenContractProductCostHis.setId(null);
                        childrenContractProductCostHis.setChangewayId(changewayId);
                        contractProductCostHisMapper.insert(childrenContractProductCostHis);
                        logger.info("子合同产品成本计划记录表已被插入,childrenContractProductCostHis: " + childrenContractProductCostHis.toString());
                    }
                }
                //插入子合同开票计划以及详情
                List<InvoicePlanHisDTO> childrenInvoicePlanHisDTOS = childrenContractHisDTO.getInvoicePlanHisDTOs();
                if (CollectionUtils.isNotEmpty(childrenInvoicePlanHisDTOS) && childrenInvoicePlanHisDTOS.size() > 0) {
                    for (InvoicePlanHisDTO childrenInvoicePlanHisDTO : childrenInvoicePlanHisDTOS) {
                        InvoicePlanHis childrenInvoicePlanHis = childrenInvoicePlanHisDTO.conventToInvoicePlanHis();
                        childrenInvoicePlanHis.setId(null);
                        childrenInvoicePlanHis.setChangewayId(changewayId);
                        childrenInvoicePlanHis.setCustomerId(contract.getCustomerId());
                        childrenInvoicePlanHis.setCustomerName(contract.getCustomerName());
                        invoicePlanHisMapper.insert(childrenInvoicePlanHis);
                        logger.info("子合同开票计划记录表已被插入,id= " + childrenInvoicePlanHis.getId());
                        List<InvoicePlanDetailHisDTO> invoicePlanDetailHiss = childrenInvoicePlanHisDTO.getInvoicePlanDetailsHiss();
                        for (InvoicePlanDetailHis invoicePlanDetailHis : invoicePlanDetailHiss) {
                            invoicePlanDetailHis.setId(null);
                            invoicePlanDetailHis.setChangewayId(changewayId);
                            invoicePlanDetailHisMapper.insert(invoicePlanDetailHis);
                            logger.info("子合同开票计划详情记录表已被插入,childrenInvoicePlanDetailHis: " + invoicePlanDetailHis.getId());
                        }
                    }
                }
                //插入子合同回款计划、详情以及回款开票关系记录数据
                List<ReceiptPlanHisDTO> childrenReceiptPlanHisDTOs = childrenContractHisDTO.getReceiptPlanHissDTOs();
                if (CollectionUtils.isNotEmpty(childrenReceiptPlanHisDTOs) && childrenReceiptPlanHisDTOs.size() > 0) {
                    for (ReceiptPlanHisDTO childrenReceiptPlanHisDTO : childrenReceiptPlanHisDTOs) {
                        ReceiptPlanHis childrenReceiptPlanHis = childrenReceiptPlanHisDTO.conventToReceiptPlanHis();
                        childrenReceiptPlanHis.setId(null);
                        childrenReceiptPlanHis.setChangewayId(changewayId);
                        receiptPlanHisMapper.insert(childrenReceiptPlanHis);
                        logger.info("子合同回款计划记录表已被插入,childrenReceiptPlanHis: " + childrenReceiptPlanHis.toString());
                        //插入详情
                        List<ReceiptPlanDetailHisDTO> childrenReceiptPlanDetailHisDTOs = childrenReceiptPlanHisDTO.getReceiptPlanDetailHisDTOS();
                        for (ReceiptPlanDetailHisDTO childrenReceiptPlanDetailHisDTO : childrenReceiptPlanDetailHisDTOs) {
                            ReceiptPlanDetailHis childrenReceiptPlanDetailHis = childrenReceiptPlanDetailHisDTO.conventToReceiptPlanDetailHis();
                            childrenReceiptPlanDetailHis.setId(null);
                            childrenReceiptPlanDetailHis.setChangewayId(changewayId);
                            receiptPlanDetailHisMapper.insert(childrenReceiptPlanDetailHis);
                            logger.info("子合同回款详情计划记录表已被插入,childrenReceiptPlanDetailHis: " + childrenReceiptPlanDetailHis.toString());

                            List<ReceiptInvoiceRelationHisDTO> childrenReceiptInvoiceRelationHisDTOs = childrenReceiptPlanDetailHisDTO.getReceiptInvoiceRelationHisDTOs();
                            if (CollectionUtils.isNotEmpty(childrenReceiptInvoiceRelationHisDTOs) && childrenReceiptInvoiceRelationHisDTOs.size() > 0) {
                                for (ReceiptInvoiceRelationHisDTO childrenReceiptInvoiceRelationHisDTO : childrenReceiptInvoiceRelationHisDTOs) {
                                    ReceiptInvoiceRelationHis childrenReceiptInvoiceRelationHis = childrenReceiptInvoiceRelationHisDTO.conventToReceiptInvoiceRelationHis();
                                    childrenReceiptInvoiceRelationHis.setId(null);
                                    childrenReceiptInvoiceRelationHis.setChangewayId(changewayId);
                                    childrenReceiptInvoiceRelationHis.setDeletedFlag(false);
                                    receiptInvoiceRelationHisMapper.insert(childrenReceiptInvoiceRelationHis);
                                    logger.info("子合同回款开票关系记录表已被插入,receiptInvoiceRelationHis: " + childrenReceiptInvoiceRelationHis.toString());
                                }
                            }
                        }
                    }
                }
            });
            //更新主合同中的changewayId,标识该合同已经被变更过
            Contract contractUpdate = new Contract();
            contractUpdate.setId(contractHisDTO.getContractId());
            contractUpdate.setChangewayId(changewayId);
            contractMapper.updateByPrimaryKeySelective(contractUpdate);
            logger.info("销售合同基础信息变更结束...");
            /**
             * ------------------------------------ 用户基本信息变更结束..... -------------------------------------------
             */

            return changewayId;
        }

    }

    /**
     * addedby ex_pengzhong2 20191123 15:00
     *
     * @param contractHisDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long changeServiceContent(ContractHisDTO contractHisDTO) {
        Long changewayId = changeType(contractHisDTO, CHANGE_TYPE_TWO);
        return changewayId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long changeInvRecPlan(ContractHisDTO contractHisDTO) {
        Long changewayId = changeType(contractHisDTO, CHANGE_TYPE_THREE);
        return changewayId;
    }

    /**
     * 检查合同变更重复流程
     *
     * @param contractHisDTO
     */
    private void checkExistsContractChange(ContractHisDTO contractHisDTO) {
        Contract contract = contractMapper.selectByPrimaryKey(contractHisDTO.getContractId());
        if (Objects.equals(ContractStatus.CHANGING.getCode(), contract.getStatus())) {
            throw new ApplicationBizException("合同变更中，请先完成上一次变更");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractHisDTO splittingConProTemporary(ContractHisDTO contractHisDTO) {
        List<ContractHisDTO> childrencontractHisDTOS = contractHisDTO.getChildrenContractHiss();
        if (CollectionUtils.isNotEmpty(childrencontractHisDTOS)) {
            childrencontractHisDTOS.forEach(childrencontractHisDTO -> {
                Integer operationType = childrencontractHisDTO.getOperationType();
                List<ContractProductHis> contractProductHiss = childrencontractHisDTO.getContractProductHiss();
                ContractChangeway contractChangeway = new ContractChangeway();
                if (contractHisDTO.getChangewayId() != null) {
                    contractChangeway = contractChangewayMapper.selectByPrimaryKey(contractHisDTO.getChangewayId());
                }
                Long autoContractId = null;
                //如果是新增的子合同，那么就临时生成一个子合同id
                if (null != childrencontractHisDTO.getContractId()) {
                    //删除用户取消操作导致的垃圾数据
                    contractProductHisMapper.deleteByConIdAndChanId(childrencontractHisDTO.getContractId());
                    autoContractId = childrencontractHisDTO.getContractId();
                } else {
                    IdGenerator idGenerator = idGenerators.get(Long.class);
                    autoContractId = Long.valueOf(idGenerator.next(Contract.class).toString());
                }
                //首次的时候插入
                if (contractChangeway == null || contractHisDTO.getChangewayId() == null) {
                    if (CollectionUtils.isNotEmpty(contractProductHiss)) {
                        for (ContractProductHis contractProductHis1 : contractProductHiss) {
                            //校验该合同产品下的开票计划是否能被删除
                            if (contractProductHis1.getDeletedFlag() && contractProductHis1.getContractProductId() != null) {
                                this.checkContractProductDel(contractProductHis1);
                            }

                            //生成随机数用于前端做标识
                            if (contractProductHis1.getContractProductId() == null) {
                                IdGenerator idGenerator = idGenerators.get(Long.class);
                                contractProductHis1.setContractProductId(Long.valueOf(idGenerator.next(ContractProduct.class).toString()));
                                if (childrencontractHisDTO.getContractId() == null) {
                                    contractProductHis1.setContractId(autoContractId);
                                }
                            }
                            contractProductHisMapper.insert(contractProductHis1);
                        }
                    }
                    ContractProductHisExample contractProductHisExample = new ContractProductHisExample();
                    ContractProductHisExample.Criteria criteria = contractProductHisExample.createCriteria();
                    criteria.andChangewayIdIsNull().andContractIdEqualTo(childrencontractHisDTO.getContractId() == null ? autoContractId : childrencontractHisDTO.getContractId()).andDeletedFlagEqualTo(Boolean.FALSE);
                    List<ContractProductHis> childrenConProHiss = contractProductHisMapper.selectByExample(contractProductHisExample);
                    childrencontractHisDTO.setContractId(autoContractId);
                    childrencontractHisDTO.setContractProductHiss(childrenConProHiss);

                } else {
                    //再次进行变更提交,更新原有服务类型，添加新增数据
//                    ContractProductHisExample contractProductHisExample = new ContractProductHisExample();
//                    ContractProductHisExample.Criteria criteria = contractProductHisExample.createCriteria();
//                    criteria.andChangewayIdEqualTo(contractHisDTO.getChangewayId()).andContractIdEqualTo(childrencontractHisDTO.getContractId());
//                    List<ContractProductHis> childrenConProHiss =  contractProductHisMapper.selectByExample(contractProductHisExample);
                    if (CollectionUtils.isNotEmpty(contractProductHiss)) {
                        for (ContractProductHis contractProductHis1 : contractProductHiss) {
                            //校验该合同产品下的开票计划是否能被删除
                            if (contractProductHis1.getDeletedFlag() && contractProductHis1.getContractProductId() != null) {
                                this.checkContractProductDel(contractProductHis1);
                            }
                            //这里有点特殊前端做了对contractProductId做了处理
                            ContractProductHisExample contractProductHisExample = new ContractProductHisExample();
                            ContractProductHisExample.Criteria contractProductHisExampleCriteria = contractProductHisExample.createCriteria();
                            contractProductHisExampleCriteria.andChangewayIdEqualTo(contractProductHis1.getChangewayId())
                                    .andContractProductIdEqualTo(contractProductHis1.getContractProductId() == null ? 0L : contractProductHis1.getContractProductId()).andContractIdEqualTo(contractProductHis1.getContractId() == null ? 0L : contractProductHis1.getContractId());
                            List<ContractProductHis> contractProductHiss1 = contractProductHisMapper.selectByExample(contractProductHisExample);
                            if (CollectionUtils.isNotEmpty(contractProductHiss1)) {
                                if (CollectionUtils.isNotEmpty(contractProductHiss1)) {
                                    ContractProductHis contractProductHis2 = new ContractProductHis();
                                    contractProductHis2.setId(contractProductHiss1.get(0).getId());
                                    contractProductHis2.setDeletedFlag(contractProductHis1.getDeletedFlag());
                                    contractProductHis2.setAmount(contractProductHis1.getAmount());
                                    contractProductHis2.setExcludingTaxAmount(contractProductHis1.getExcludingTaxAmount());
                                    contractProductHis2.setTaxId(contractProductHis1.getTaxId());
                                    contractProductHis2.setTaxRate(contractProductHis1.getTaxRate());
                                    contractProductHis2.setTaxValue(contractProductHis1.getTaxValue());
                                    contractProductHisMapper.updateByPrimaryKeySelective(contractProductHis2);
                                }
                            } else {
                                //临时存一个id，给前端当作一个标识
                                IdGenerator idGenerator = idGenerators.get(Long.class);
                                contractProductHis1.setContractProductId(Long.valueOf(idGenerator.next(ContractProduct.class).toString()));
                                if (childrencontractHisDTO.getContractId() == null) {
                                    contractProductHis1.setContractId(autoContractId);
                                }
                                contractProductHisMapper.insert(contractProductHis1);
                            }
                        }
                    }
                    if (operationType == null || operationType == 1) {
                        ContractProductHisExample contractProductHisExample = new ContractProductHisExample();
                        ContractProductHisExample.Criteria criteria = contractProductHisExample.createCriteria();
                        criteria.andChangewayIdEqualTo(contractHisDTO.getChangewayId()).andContractIdEqualTo(childrencontractHisDTO.getContractId() == null ? autoContractId : childrencontractHisDTO.getContractId());
                        List<ContractProductHis> childrenConProHiss = contractProductHisMapper.selectByExample(contractProductHisExample);
                        childrencontractHisDTO.setContractId(autoContractId);
                        childrencontractHisDTO.setContractProductHiss(childrenConProHiss);
                    }
                }
            });
        } else {
            throw new MipException("子合同不允许为空！");
        }

        return contractHisDTO;
    }

    private void checkContractProductDel(ContractProductHis contractProductHis) {
        if (contractProductHis.getContractId() == null
                || contractProductHis.getContractProductId() == null) {
            return;
        }
        InvoicePlanExample invoicePlanExample = new InvoicePlanExample();
        String contractProductCond = "" + contractProductHis.getProductId() + contractProductHis.getProductTypeName() + contractProductHis.getTaxId();
        invoicePlanExample.createCriteria().andContractIdEqualTo(contractProductHis.getContractId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<InvoicePlan> invoicePlanList = invoicePlanMapper.selectByExample(invoicePlanExample);
        if (CollectionUtils.isEmpty(invoicePlanList)) {
            return;
        }
        for (InvoicePlan invoicePlan : invoicePlanList) {
            String invoicePlanCond = "" + (invoicePlan.getProductId() == null ? ("-1") : invoicePlan.getProductId()) + invoicePlan.getProductTypeName() + invoicePlan.getTaxId();
            Long planId = invoicePlan.getId();
            if (!contractProductCond.trim().equals(invoicePlanCond.trim())
                    || planId == null) {
                continue;
            }
            InvoicePlanDetailExample invoicePlanDetailExample = new InvoicePlanDetailExample();
            invoicePlanDetailExample.createCriteria().andContractIdEqualTo(contractProductHis.getContractId())
                    .andPlanIdEqualTo(planId).andDeletedFlagEqualTo(Boolean.FALSE);
            List<InvoicePlanDetail> invoicePlanDetailList = invoicePlanDetailMapper.selectByExample(invoicePlanDetailExample);
            if (CollectionUtils.isEmpty(invoicePlanDetailList)) {
                continue;
            }
            for (InvoicePlanDetail invoicePlanDetail : invoicePlanDetailList) {
                if (invoicePlanDetail.getId() == null) {
                    continue;
                }
                InvoiceApplyDetailsExample invoiceApplyDetailsExample = new InvoiceApplyDetailsExample();
                invoiceApplyDetailsExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andStatusNotEqualTo(-1)
                        .andPlanDetailIdEqualTo(invoicePlanDetail.getId());
                List<InvoiceApplyDetails> invoiceApplyDetailsList = invoiceApplyDetailsMapper.selectByExample(invoiceApplyDetailsExample);
                if (CollectionUtils.isEmpty(invoiceApplyDetailsList)) {
                    continue;
                }
                BigDecimal totalTaxIncludedPrice = invoiceApplyDetailsList.stream().map(e -> Optional.ofNullable(e.getTaxIncludedPrice()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (totalTaxIncludedPrice.compareTo(BigDecimal.ZERO) != 0) {
                    throw new MipException("该合同产品关联的开票计划，已经做过开票申请，不允许删除！");
                }
            }
        }
    }

    private Boolean isContractIsView(Contract contract) {
        if (contract == null) {
            return false;
        }
        //只要是创建人、有数据权限、项目成员、所有者
        if (isCreater(contract) || isDataAuthor(contract) || isManager(contract) || isSaleManager(contract)) {
            return true;
        }
        return false;
    }

    /**
     * 判断当前登录用户是否有数据权限
     *
     * @param contract
     * @return boolean true是 false否
     */
    public Boolean isDataAuthor(Contract contract) {
        if (contract == null) {
            return false;
        }
        Long id = contract.getUnitId();
        if (id == null) {
            return false;
        }
        List<Long> list = SystemContext.getSecondUnits();//当前登录用户拥有二级部门权限
        if (list == null || list.size() == 0) {
            return false;
        }
        if (list.contains(id)) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 判断当前登录用户是否财务人员
     *
     * @param contract
     * @return boolean true是 false否
     */
    public Boolean isFinance(Contract contract) {
        if (contract == null) {
            return false;
        }
        ProjectContractRsExample projectContractRsExample = new ProjectContractRsExample();
        ProjectContractRsExample.Criteria criteria = projectContractRsExample.createCriteria();
        criteria.andContractIdEqualTo(contract.getId());
        List<ProjectContractRs> projectContractRsList = projectContractRsMapper.selectByExample(projectContractRsExample);
        if (CollectionUtils.isEmpty(projectContractRsList)) {
            return false;
        }

        return projectBusinessService.isFinance(projectContractRsList.get(0).getProjectId());
    }


    /**
     * 更新合同状态
     */
    private void updateContractStatus(Long contractId, String invalidReason, int contractStatus) {
        final Contract contract = this.findById(contractId);
        Integer status = contract.getStatus();
        Assert.notNull(contract, "该合同不存在，作废失败");
        FormInstanceEvent formInstanceEvent = new FormInstanceEvent();
        formInstanceEvent.setFormInstanceId(contractId);
        formInstanceEvent.setFormUrl("contractApp");
        formInstanceEvent.setStatus(0);
        formInstanceEvent.setEventType(WorkflowOperationType.DRAFT_ABANDON.getCode());
        formInstanceEvent.setCompanyId(SystemContext.getUnitId());
        formInstanceEvent.setCreateAt(new Date());
        formInstanceEvent.setCreateBy(SystemContext.getUserId());
        contract.setInvalidId(SystemContext.getUserId());
        contract.setInvalidTime(new Date());
        contract.setStatus(contractStatus);
        contract.setLegalContractNum(null);
        Assert.notNull(invalidReason, "作废原因不能为空");
        contract.setInvalidReason(invalidReason);
        if (StringUtil.isNotNull(formInstanceEvent.getFormUrl())) {
            String jsonString = JSONObject.toJSONString(formInstanceEvent);
            stringRedisTemplate.opsForList().leftPushAll(Constants.REDIS_WF_EVENT_KEY, jsonString);
        }
        this.update(contract);
        //更新子合同状态
        this.updateChildContracts(contractId, contractStatus);
    }

    /**
     * 更新子合同状态
     */
    private void updateChildContracts(Long parentContractId, int contractStatus) {
        final List<ContractVO> childContracts = this.getListByParentId(parentContractId);
        if (CollectionUtils.isNotEmpty(childContracts)) {
            childContracts.forEach(contractVO -> {
                Contract child = new Contract();
                BeanUtils.copyProperties(contractVO, child);
                child.setStatus(contractStatus);
                this.update(child);
                //作废合同关联采购信息
                contractExtMapper.updateContractEamPurchaseRel(child.getId());
            });
        }
    }


    /**
     * 插入变更记录
     */
    @Override
    public Long insertToContractChangewayType(ContractHisDTO contractHisDTO, Integer changeType) {
        //插入合同变更记录头表
        ContractChangeway contractChangeway = new ContractChangeway();
        contractChangeway.setContractId(contractHisDTO.getContractId());
        contractChangeway.setChangeType(changeType);
        contractChangeway.setApprovalStatus(ContractChangewayStatus.DRAFT.getCode());
        contractChangeway.setChangeReason(contractHisDTO.getChangeReason());
        contractChangeway.setCreateBy(SystemContext.getUserId());
        contractChangeway.setCreateAt(new Date());
        contractChangeway.setAttachId(contractHisDTO.getAttachId());
        contractChangeway.setChangeReasonType(contractHisDTO.getChangeReasonType());
        contractChangeway.setChangeReasonTypeId(contractHisDTO.getChangeReasonTypeId());

        contractChangeway.setIsElectronicContract(contractHisDTO.getIsElectronicContract());
        contractChangeway.setOtherName(contractHisDTO.getOtherName());
        contractChangeway.setOtherPhone(contractHisDTO.getOtherPhone());
        contractChangeway.setOtherEmail(contractHisDTO.getOtherEmail());
        contractChangeway.setPublicOrPrivate(contractHisDTO.getPublicOrPrivate());
        contractChangeway.setSealCategory(contractHisDTO.getSealCategory());
        contractChangeway.setSealAdminAccountIds(contractHisDTO.getSealAdministratorChangeIds());
        contractChangeway.setChangeProtocolFile(contractHisDTO.getChangeProtocolFile());

        contractChangewayMapper.insert(contractChangeway);
        return contractChangeway.getId();

    }

    /**
     * 判断是否开过票
     *
     * @param contractId
     * @return
     */
    private Boolean isInvoice(Long contractId) {
        List<InvoiceApplyDetails> invoiceApplyDetails = this.getInvoiceApplyDetailsById(contractId);
        if (CollectionUtils.isNotEmpty(invoiceApplyDetails)) {
            BigDecimal totalTaxIncludedPrice = BigDecimal.ZERO;
            for (InvoiceApplyDetails invoiceApplyDetail : invoiceApplyDetails) {
                BigDecimal taxIncludedPrice = invoiceApplyDetail.getTaxIncludedPrice() == null ? BigDecimal.ZERO : invoiceApplyDetail.getTaxIncludedPrice();
                InvoiceApplyHeader invoiceApplyHeader = invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyDetail.getApplyHeaderId());
                if (invoiceApplyHeader.getStatus() != -1 && invoiceApplyHeader.getDeletedFlag() != Boolean.TRUE && invoiceApplyDetail.getStatus() != -1) {
                    totalTaxIncludedPrice = totalTaxIncludedPrice.add(taxIncludedPrice);
                }
            }
            if (!BigDecimalUtils.equals(totalTaxIncludedPrice, BigDecimal.ZERO)) {
                return true;
            } else {
                return false;
            }

        }
        return false;
    }

    /**
     * 判断是否回过款
     *
     * @param contractId
     * @return
     */
    private Boolean isReceipt(Long contractId) {
        List<ReceiptClaimContractRel> receiptClaimContractRelList = new ArrayList<>();
        ReceiptClaimContractRelExample receiptClaimContractRelExample = new ReceiptClaimContractRelExample();
        ReceiptClaimContractRelExample.Criteria receiptClaimContractRelCriteria = receiptClaimContractRelExample.createCriteria();
        receiptClaimContractRelCriteria.andContractIdEqualTo(contractId).andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
        receiptClaimContractRelList = receiptClaimContractRelMapper.selectByExample(receiptClaimContractRelExample);
        if (CollectionUtils.isNotEmpty(receiptClaimContractRelList) && receiptClaimContractRelList.size() > 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否有主辅里程碑
     *
     * @param projectId
     * @return
     */
    @Override
    public Boolean isMilepost(Long projectId) {
        final ProjectMilepostExample helpMilepostExample = new ProjectMilepostExample();
        helpMilepostExample.createCriteria().andProjectIdEqualTo(projectId).
                andDeletedFlagEqualTo(false).andHelpFlagEqualTo(true);
        helpMilepostExample.setOrderByClause("order_num asc");
        final List<ProjectMilepost> helpMileposts = projectMilepostService.selectByExample(helpMilepostExample);
        Boolean isExistHelpMilepost = CollectionUtils.isNotEmpty(helpMileposts) && helpMileposts.size() > 0;

        final ProjectMilepostExample mainMilepostExample = new ProjectMilepostExample();
        mainMilepostExample.createCriteria().andProjectIdEqualTo(projectId).
                andDeletedFlagEqualTo(false).andHelpFlagEqualTo(false);
        mainMilepostExample.setOrderByClause("order_num asc");
        final List<ProjectMilepost> mainMileposts = projectMilepostService.selectByExample(mainMilepostExample);
        Boolean isExistMainMilepost = CollectionUtils.isNotEmpty(mainMileposts) && mainMileposts.size() > 0;
        if (isExistHelpMilepost && isExistMainMilepost) {
            return true;
        }
        return false;
    }

    @Override
    public Long terminationSubmit(ContractChangewayDTO contractChangewayDTO) {
        //参数校验
        Guard.notNull(contractChangewayDTO.getCheckResultHeaderId(), "终止场景不能为空");
        Guard.notNull(contractChangewayDTO.getTerminationDate(), "终止日期不能为空");
        Guard.notNull(contractChangewayDTO.getChangeReason(), "终止原因不能为空");
        Guard.notNull(contractChangewayDTO.getChangeProtocolFile(), "终止协议不能为空");
        Long contractId = contractChangewayDTO.getContractId();
        Guard.notNull(contractId, "合同ID不能为空");
        //业务逻辑检验
        Contract contract = this.findById(contractId);
        Guard.notNull(contract, "合同不存在");
        //如果是法务合同
        if (Boolean.TRUE.equals(contract.getIsSynchronizeLegalSystemFlag())) {
            Guard.notNull(contractChangewayDTO.getFileAttachmentId(), "attachmentId不能为空");
            Guard.notNull(contractChangewayDTO.getIsElectronicContract(), "是否电子合同不能为空");
            if (Boolean.FALSE.equals(contractChangewayDTO.getIsElectronicContract())) {
                Guard.notNull(contractChangewayDTO.getSealCategory(), "合同印章类别不能为空");
                Guard.notNull(contractChangewayDTO.getSealAdminAccountIds(), "印章管理员不能为空");
            }
        }
        //对已生效的合同的能否发起终止、变更等校验。 对某一个合同用户可能多tab打开且提交了流程，其他tab卡在详情页，按钮仍可用
        if (!Objects.equals(ContractStatus.EFFECTIVE.getCode(), contract.getStatus())) {
            throw new MipException("当前合同状态不是生效状态，无法发起终止流程");
        }
        Long headerId = contractChangewayDTO.getCheckResultHeaderId();
        CheckResultHeader checkResultHeader = checkResultHeaderMapper.selectByPrimaryKey(headerId);
        Guard.notNull(checkResultHeader, "合同未终止检查或检查已失效");
        Guard.notNull(checkResultHeader.getCheckSceneId(), "终止场景不能为空");
        CheckSceneInfoDTO checkSceneInfoDTO = new CheckSceneInfoDTO();
        checkSceneInfoDTO.setId(checkResultHeader.getCheckSceneId());
        checkSceneInfoDTO.setEffectiveFlag(true);//只取生效的数据
        List<CheckSceneInfo> checkSceneInfoList = checkSceneInfoService.list(checkSceneInfoDTO);
        if (ListUtils.isEmpty(checkSceneInfoList)) {
            throw new MipException("终止场景已失效，请重新选择");
        }
        Date createAt = checkResultHeader.getCreateAt();
        if (!DateUtils.isDifferenceWithinMinutes(createAt, 10)) {
            throw new MipException("当前检查结果已失效，请重新检查");
        }
        ContractChangeway contractChangeway = BeanConverter.copy(contractChangewayDTO, ContractChangeway.class);
        if (Objects.isNull(contractChangewayDTO.getId())) {
            //插入合同变更记录头表
            contractChangeway.setChangeType(0);
            contractChangeway.setApprovalStatus(ContractChangewayStatus.DRAFT.getCode());
            contractChangeway.setCreateBy(SystemContext.getUserId());
            contractChangeway.setCreateAt(new Date());
            contractChangewayMapper.insert(contractChangeway);
        } else {
            //将check_result_header表中关联的changewayId失效
            CheckResultHeader updatePo = new CheckResultHeader();
            updatePo.setSourceId(contractChangeway.getId());
            updatePo.setUpdateBy(SystemContext.getUserId());
            updatePo.setUpdateAt(new Date());
            updatePo.setDeletedFlag(Boolean.TRUE);
            checkResultHeaderExtMapper.unbindChangewayId(updatePo);
            //撤回或驳回重新编辑保存
            contractChangeway.setApprovalStatus(ContractChangewayStatus.DRAFT.getCode());
            contractChangeway.setUpdateBy(SystemContext.getUserId());
            contractChangeway.setUpdateAt(new Date());
            contractChangewayMapper.updateByPrimaryKeySelective(contractChangeway);
        }
        //更新check_result_header，绑定changewayId
        checkResultHeader.setSourceId(contractChangeway.getId());
        checkResultHeaderMapper.updateByPrimaryKey(checkResultHeader);
        return contractChangeway.getId();
    }

    @Override
    public ContractChangewayDTO terminationView(Long id) {
        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(id);
        ContractChangewayDTO contractChangewayDTO = new ContractChangewayDTO();
        BeanConverter.copy(contractChangeway, contractChangewayDTO);
        //组装返回对象
        CheckResultHeaderExample example = new CheckResultHeaderExample();
        example.createCriteria().andSourceIdEqualTo(id).andBusinessIdEqualTo(contractChangeway.getContractId()).andResultEqualTo(3);
        List<CheckResultHeader> checkResultHeaders = checkResultHeaderMapper.selectByExample(example);
        if (ListUtils.isEmpty(checkResultHeaders)) {
            throw new MipException("未查询到检查内容，请稍后再试");
        }
        CheckResultHeader checkResultHeader = checkResultHeaders.get(0);
        CheckResultHeaderDTO checkResultHeaderDTO = checkResultService.resultDetailByHeaderId(checkResultHeader);
        if (Objects.nonNull(checkResultHeaderDTO)) {
            contractChangewayDTO.setCheckResultHeaderId(checkResultHeaderDTO.getId());
            contractChangewayDTO.setCheckSceneId(checkResultHeaderDTO.getCheckSceneId());
            contractChangewayDTO.setSceneName(checkResultHeaderDTO.getSceneName());
            contractChangewayDTO.setCheckResultHeaderDTO(checkResultHeaderDTO);
        }
        return contractChangewayDTO;
    }

    @Override
    public ResponseMap getContractTerminationApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        ContractChangewayDTO dto = terminationView(id);
        if (ObjectUtils.isEmpty(dto)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("未查询到合同终止详情内容");
            return responseMap;
        }
        Contract contract = this.findById(dto.getContractId());
        Guard.notNull(contract, "合同不存在");
        ContractVO contractVO = new ContractVO();
        BeanConverter.copy(contract, contractVO);
        contractHelper.formatContractVO(contractVO);

        headMap.put("name", contractVO.getName()); //合同名称
        headMap.put("legalAffairsId", contractVO.getLegalContractNum()); //法务合同编号
        headMap.put("unitName", contractVO.getUnitName()); //销售部门
        headMap.put("customerName", contractVO.getCustomerName());//客户名称
        headMap.put("salesManagerName", contractVO.getSalesManagerName()); //销售经理
        headMap.put("managerName", contractVO.getManagerName()); //项目经理
        headMap.put("currency", contractVO.getCurrency()); //币种
        headMap.put("amount", Objects.isNull(contractVO.getAmount()) ? "" : contractVO.getAmount().stripTrailingZeros().toPlainString());//合同金额含税
        headMap.put("excludingTaxAmount", Objects.isNull(contractVO.getExcludingTaxAmount()) ? "" : contractVO.getExcludingTaxAmount().stripTrailingZeros().toPlainString());  //合同金额不含税
        headMap.put("ouName", contractVO.getOuName()); //业务实体

        headMap.put("sceneName", dto.getSceneName());//终止场景
        headMap.put("terminationDate", DateUtils.format(dto.getTerminationDate(), DateUtils.FORMAT_SHORT));//终止日期
        headMap.put("changeReason", dto.getChangeReason());//终止原因

        responseMap.setHeadMap(headMap);

        //检查明细
        List<Map<String, String>> list1 = new ArrayList<>();
        List<CheckResultDetailDTO> detailDTOList = dto.getCheckResultHeaderDTO().getDetailDTOList();
        if (CollectionUtils.isNotEmpty(detailDTOList)) {
            for (CheckResultDetailDTO detailDTO : detailDTOList) {
                Map<String, String> detailMap = new HashMap<>();
                detailMap.put("checkName", detailDTO.getCheckName()); //检查点名称
                String checkResultName = "";
                switch (detailDTO.getCheckResult()) {
                    case 1:
                        checkResultName = "通过";
                        break;
                    case 2:
                        checkResultName = "不通过";
                        break;
                    case 3:
                        checkResultName = "警告";
                        break;
                    case 4:
                        checkResultName = "提示";
                        break;
                    default:
                }
                detailMap.put("checkResult", checkResultName); //检查结果
                list1.add(detailMap);
            }
            responseMap.setList1(list1);
        }
        //附件
        if (StringUtils.hasText(dto.getChangeProtocolFile())) {
            headMap.put("annex", dto.getChangeProtocolFile());
        }
        responseMap.setMsg("成功");
        responseMap.setStatus("success");
        return responseMap;
    }

    @Override
    public Boolean terminationCheck(Long id) {
        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(id);
        Guard.notNull(contractChangeway, "终止变更信息不存在");
        Contract contract = contractMapper.selectByPrimaryKey(contractChangeway.getContractId());
        //对已生效的合同的能否发起终止、变更等校验。 对某一个合同用户可能多tab打开且提交了流程，其他tab卡在详情页，按钮仍可用
        if (!Objects.equals(ContractStatus.EFFECTIVE.getCode(), contract.getStatus())) {
            throw new BizException(Code.ERROR, "当前合同状态不是生效状态，无法发起终止流程");
        }
        CheckResultHeaderExample example = new CheckResultHeaderExample();
        example.createCriteria()
                .andSourceIdEqualTo(contractChangeway.getId()).andBusinessIdEqualTo(contract.getId())
                .andResultEqualTo(3).andDeletedFlagEqualTo(Boolean.FALSE);
        List<CheckResultHeader> checkResultHeaders = checkResultHeaderMapper.selectByExample(example);
        if (ListUtils.isEmpty(checkResultHeaders)) {
            throw new BizException(Code.ERROR, "终止检查已失效");
        }
        CheckResultHeader checkResultHeader = checkResultHeaders.get(0);
        Date createAt = checkResultHeader.getCreateAt();
        if (!DateUtils.isDifferenceWithinMinutes(createAt, 10)) {
            throw new BizException(Code.ERROR, "当前检查结果已失效，请重新检查");
        }
        return true;
    }

    /**
     * 取开票申请行数据
     *
     * @param contractId
     * @return
     */
    private List<InvoiceApplyDetails> getInvoiceApplyDetailsById(Long contractId) {
        InvoiceApplyDetailsExample invoiceApplyDetailsExample = new InvoiceApplyDetailsExample();
        InvoiceApplyDetailsExample.Criteria criteria1 = invoiceApplyDetailsExample.createCriteria();
        criteria1.andContractIdEqualTo(contractId);
        List<InvoiceApplyDetails> invoiceApplyDetails = invoiceApplyDetailsMapper.selectByExample(invoiceApplyDetailsExample);
        return invoiceApplyDetails;
    }

    /**
     * 新增或更新收入成本计划变更记录
     *
     * @param changeContractId 变更合同ID
     * @param projectId        项目ID
     * @param mainIncome       主收入
     * @param helpIncome       辅收入
     */
    private void saveProjectProfitHis(Long changeContractId, Long projectId, BigDecimal mainIncome, BigDecimal helpIncome) {
        ProjectProfitHisExample projectProfitHisExample = new ProjectProfitHisExample();
        ProjectProfitHisExample.Criteria profitHisExampleCriteria = projectProfitHisExample.createCriteria();
        profitHisExampleCriteria.andChangewayIdEqualTo(changeContractId);
        profitHisExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectProfitHis> projectProfitHisList = projectProfitHisMapper.selectByExample(projectProfitHisExample);
        if (CollectionUtils.isNotEmpty(projectProfitHisList)) {
            ProjectProfitHis projectProfitHis = projectProfitHisList.get(0);
            projectProfitHis.setMainIncome(mainIncome);
            projectProfitHis.setHelpBudget(helpIncome);
            projectProfitHisMapper.updateByPrimaryKey(projectProfitHis);
        } else {
            ProjectProfitExample projectProfitExample = new ProjectProfitExample();
            ProjectProfitExample.Criteria criteria = projectProfitExample.createCriteria();
            criteria.andProjectIdEqualTo(projectId);
            criteria.andDeletedFlagEqualTo(Boolean.FALSE);
            List<ProjectProfit> projectProfits = projectProfitService.selectByExample(projectProfitExample);
            if (CollectionUtils.isNotEmpty(projectProfits)) {
                ProjectProfit projectProfit = projectProfits.get(0);
                ProjectProfitHis projectProfitHis = BeanConverter.copy(projectProfit, ProjectProfitHis.class);
                projectProfitHis.setId(null);
                projectProfitHis.setProjectProfitId(projectProfit.getId());
                projectProfitHis.setMainIncome(mainIncome);
                projectProfitHis.setHelpIncome(helpIncome);
                projectProfitHis.setChangewayId(changeContractId);
                projectProfitHisMapper.insert(projectProfitHis);
            }
        }
    }

    /**
     * 开票计划变更 规则校验
     *
     * @param invoicePlanHisDtoList
     */
    private void changesOfInv(List<InvoicePlanHisDTO> invoicePlanHisDtoList) {
        if (CollectionUtils.isNotEmpty(invoicePlanHisDtoList)) {
            invoicePlanHisDtoList.forEach(invoicePlanHisDto -> {
                final List<InvoicePlanDetailHisDTO> invoicePlanDetailHisList = invoicePlanHisDto.getInvoicePlanDetailsHiss();
                // 子合同计划金额
                final BigDecimal amount = invoicePlanHisDto.getAmount();
                // 开票计划总额
                BigDecimal totalInvoicePlanAmount = BigDecimal.ZERO;
                for (InvoicePlanDetailHis planDetailHis : invoicePlanDetailHisList) {
                    Assert.notNull(planDetailHis.getDate(), "计划开票日期不能为空");
                    Assert.notNull(planDetailHis.getAmount(), "含税金额不能为空");
                    //todo 已开票的开票计划行不允许删除

                    //todo 开票计划行被删除时，回款计划关联开票计划需要清除掉，让用户重新绑定

                    // 金额只能为正数
                    final BigDecimal invoicePlanAmount = planDetailHis.getAmount();
                    final BigDecimal actualAmount = planDetailHis.getActualAmount() != null ? planDetailHis.getActualAmount() : BigDecimal.ZERO;
                    if (BigDecimal.ZERO.compareTo(invoicePlanAmount) > 0) {
                        throw new MipException("金额只能为正数");
                    }
                    //计划开票金额不得小于实际开票金额
                    if (invoicePlanAmount.compareTo(actualAmount) < 0) {
                        throw new MipException("计划开票金额不得小于实际开票金额");
                    }
                    if (Objects.equals(planDetailHis.getDeletedFlag(), Boolean.FALSE)) {
                        totalInvoicePlanAmount = totalInvoicePlanAmount.add(invoicePlanAmount);
                    }
                }
                if (totalInvoicePlanAmount.compareTo(amount) != 0) {
                    throw new MipException("开票计划总额需与合同金额一致");
                }

            });
        }
    }

    /**
     * 回款计划变更 规则校验
     *
     * @param receiptPlanHisDtoList
     */
    private void changesOfRec(List<ReceiptPlanHisDTO> receiptPlanHisDtoList) {
        if (CollectionUtils.isNotEmpty(receiptPlanHisDtoList)) {
            receiptPlanHisDtoList.forEach(ReceiptPlanHisDto -> {
                final List<ReceiptPlanDetailHisDTO> receiptPlanDetailHisDTOList = ReceiptPlanHisDto.getReceiptPlanDetailHisDTOS();
                if (CollectionUtils.isEmpty(receiptPlanDetailHisDTOList)) {
                    throw new MipException("回款计划详情不能为空");
                }
                // 子合同计划回款金额
                final BigDecimal amount = ReceiptPlanHisDto.getAmount();
                // todo 已回款的回款计划不允许删除；

                // 开票计划总额
                BigDecimal totalReceiptPlanAmount = BigDecimal.ZERO;
                for (ReceiptPlanDetailHisDTO receiptPlanDetailHisDto : receiptPlanDetailHisDTOList) {
                    // 删除的数据不做校验
                    if (receiptPlanDetailHisDto.getDeletedFlag() != null && receiptPlanDetailHisDto.getDeletedFlag()) {
                        continue;
                    }

                    Assert.notNull(receiptPlanDetailHisDto.getDate(), "计划回款日期不能为空");
                    Assert.notNull(receiptPlanDetailHisDto.getAmount(), "回款金额不能为空");

                    // 金额只能为正数
                    final BigDecimal receiptPlanAmount = receiptPlanDetailHisDto.getAmount();
                    final BigDecimal actualAmount = receiptPlanDetailHisDto.getActualAmount() == null ? BigDecimal.ZERO : receiptPlanDetailHisDto.getActualAmount();
                    if (BigDecimal.ZERO.compareTo(receiptPlanAmount) > 0) {
                        throw new MipException("金额只能为正数");
                    }
                    // 计划回款金额不得小于实际回款金额
                    if (receiptPlanAmount.compareTo(actualAmount) < 0) {
                        throw new MipException("计划回款金额不得小于实际回款金额");
                    }

                    totalReceiptPlanAmount = totalReceiptPlanAmount.add(receiptPlanAmount);
                }

                if (totalReceiptPlanAmount.compareTo(amount) != 0) {
                    throw new MipException("回款计划总额需与合同金额一致");
                }
            });
        }
    }

    private Long changeType(ContractHisDTO contractHisDTO, Integer type) {

        // 检查合同变更重复流程
        checkExistsContractChange(contractHisDTO);

        contractModifyCheck.baseInfoParamCheck(contractHisDTO, type);
        //原子合同信息
        ContractVO contractVO = this.findDetailById(contractHisDTO.getContractId());

        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(contractHisDTO.getChangewayId());
        if (contractHisDTO.getChangewayId() != null && contractChangeway.getChangeType().equals(type)) {

            if (Objects.equals(ContractChangewayStatus.PENDING.getCode(), contractChangeway.getApprovalStatus()) || Objects.equals(ContractChangewayStatus.APPROVED.getCode(), contractChangeway.getApprovalStatus())) {
                throw new BizException(Code.ERROR, "流程提交后不允许再次保存草稿！");
            }

            Long againChangewayId = contractHisDTO.getChangewayId();
            contractChangeway.setAttachId(contractHisDTO.getAttachId());
            contractChangeway.setChangeReasonTypeId(contractHisDTO.getChangeReasonTypeId());
            contractChangeway.setChangeReasonType(contractHisDTO.getChangeReasonType());
            contractChangeway.setSealAdminAccountIds(contractHisDTO.getSealAdministratorChangeIds());
            contractChangeway.setIsElectronicContract(contractHisDTO.getIsElectronicContract());
            contractChangeway.setSealCategory(contractHisDTO.getSealCategory());
            contractChangeway.setChangeProtocolFile(contractHisDTO.getChangeProtocolFile());
            contractChangeway.setOtherName(contractHisDTO.getOtherName());
            contractChangeway.setOtherPhone(contractHisDTO.getOtherPhone());
            contractChangeway.setOtherEmail(contractHisDTO.getOtherEmail());
            contractChangewayMapper.updateByPrimaryKey(contractChangeway);
            /**
             * ------------------------------------ 再次进行合同服务内容变更..... -------------------------------------------
             */
            logger.info("再次对合同服务内容进行变更...");
            //如果更改了子合同的金额，主合同也需要进行相应的调整（也就是需要更新主子合同的金额），也需要更新合同产品(主)
            ContractHis contractHis = contractHisDTO.convertToContractHis();
            contractHisExtMapper.updateByIdAndChangewayId(contractHis);
            //更新 子合同记录，包括其产品，成本，开票「详情」，回款「详情，开票回款关联」
            List<ContractHisDTO> childrenContractHisDTOAll = contractHisDTO.getChildrenContractHiss();
            /***
             *  ----------------------------   合同服务内容变更--新增子合同 added at 20200401 --------------------------------
             */
            //先删除重复提交新增的子合同数据
            contractHisExtMapper.updateContractAddDelete(contractHisDTO.getChangewayId(), contractHisDTO.getContractId());
            //子合同新增
            List<ContractHisDTO> childrencontractHisDTOsTypeAdd = childrenContractHisDTOAll.stream().filter(c -> null != c.getOperationType() && c.getOperationType().equals(OPERATION_TYPE_ONE)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(childrencontractHisDTOsTypeAdd)) {
                childrencontractHisDTOsTypeAdd.forEach(childrencontractHis -> {
                    childrencontractHis.setParentId(contractHis.getContractId());
                    childrencontractHis.setChangewayId(contractHis.getChangewayId());
                });
                contractHisService.chidrenContractAddSer(childrencontractHisDTOsTypeAdd);
            }
            List<ContractHisDTO> childrencontractHisDTOsTypeAddDel = childrenContractHisDTOAll.stream().filter(c -> null != c.getOperationType()
                    && c.getOperationType().equals(OPERATION_TYPE_THREE) && Objects.equals(c.getDeletedFlag(), Boolean.TRUE)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(childrencontractHisDTOsTypeAddDel)) {
                Long changewayId = contractHisDTO.getChangewayId();
                for (ContractHisDTO hisDTO : childrencontractHisDTOsTypeAddDel) {
                    //说明该数据是重新编辑的新增子合同的数据
                    Long contractHisDTOId = hisDTO.getId() == null ? hisDTO.getContractId() : hisDTO.getId();
                    if (contractHisDTOId != null) {
                        ContractHis contractHisQuery = new ContractHis();
                        contractHisQuery.setContractId(contractHisDTOId);
                        contractHisQuery.setChangewayId(changewayId);
                        contractHisMapper.deleteByPrimaryKey(contractHisDTOId);
                        contractHisExtMapper.deleteContractProductHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteInvoicePlanHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteInvoicePlanDetailHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteReceiptPlanHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteReceiptPlanDetailHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteReceiptInvoiceRelationHisByChangewayId(contractHisQuery);
                        contractHisExtMapper.deleteContractInventoryHisByChangewayId(contractHisQuery);
                    }
                }
            }

            //子合同删除
            List<ContractHisDTO> childrencontractHisDTOsTypeDel = childrenContractHisDTOAll.stream().filter(c -> null != c.getOperationType() && c.getOperationType().equals(OPERATION_TYPE_TWO)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(childrencontractHisDTOsTypeDel)) {
                Long changewayId = contractHisDTO.getChangewayId();
                for (ContractHisDTO hisDTO : childrencontractHisDTOsTypeDel) {
                    hisDTO.setChangewayId(changewayId);
                }
                contractHisService.chidrenContractDelSer(childrencontractHisDTOsTypeDel);
            }
            //将删除的子合同取消
            List<ContractHisDTO> childrencontractHisDTOsTypeCancelDel = childrenContractHisDTOAll.stream().filter(c -> null != c.getOperationType()
                    && c.getOperationType().equals(OPERATION_TYPE_THREE)
                    && Objects.equals(c.getDeletedFlag(), Boolean.FALSE)).collect(Collectors.toList());
            List<ContractHisDTO> childrenContractHisDTOs = childrenContractHisDTOAll.stream().filter(c -> c.getOperationType() == null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(childrencontractHisDTOsTypeCancelDel)) {
                Long changewayId = contractHisDTO.getChangewayId();
                for (ContractHisDTO contractHisDTOTypeCancelDel : childrencontractHisDTOsTypeCancelDel) {
                    contractHisDTOTypeCancelDel.setOperationType(null);
                    contractHisDTOTypeCancelDel.setChangewayId(changewayId);
                    contractHisDTOTypeCancelDel.setDeletedFlag(Boolean.FALSE);
                    ContractHisExample contractHisExample = new ContractHisExample();
                    contractHisExample.createCriteria().andContractIdEqualTo(contractHisDTOTypeCancelDel.getContractId()).andChangewayIdEqualTo(changewayId);
                    List<ContractHis> contractHisList = contractHisMapper.selectByExample(contractHisExample);
                    if (CollectionUtils.isNotEmpty(contractHisList)) {
                        contractHisDTOTypeCancelDel.setId(contractHisList.get(0).getId());
                        contractHisMapper.updateByPrimaryKey(contractHisDTOTypeCancelDel);
                    }
                }
            }

            childrenContractHisDTOs.addAll(childrencontractHisDTOsTypeCancelDel);

            for (ContractHisDTO childrenContractHisDTO : childrenContractHisDTOs) {
                ContractHis contractHisQuery = new ContractHis();
                contractHisQuery.setContractId(childrenContractHisDTO.getContractId());
                contractHisQuery.setChangewayId(contractHisDTO.getChangewayId());
                contractHisExtMapper.deleteContractProductHisByChangewayId(contractHisQuery);
                contractHisExtMapper.deleteInvoicePlanHisByChangewayId(contractHisQuery);
                contractHisExtMapper.deleteInvoicePlanDetailHisByChangewayId(contractHisQuery);
                contractHisExtMapper.deleteReceiptPlanDetailHisByChangewayId(contractHisQuery);
                contractHisExtMapper.deleteReceiptInvoiceRelationHisByChangewayId(contractHisQuery);
                contractHisExtMapper.deleteContractInventoryHisByChangewayId(contractHisQuery);
            }

            if (CollectionUtils.isNotEmpty(childrenContractHisDTOs)) {
                for (ContractHisDTO childrenContractHisDTO : childrenContractHisDTOs) {
                    BigDecimal amount = BigDecimal.ZERO;
                    BigDecimal excludingTaxAmount = BigDecimal.ZERO;
                    List<ContractProductHis> childrenContractProductHiss = childrenContractHisDTO.getContractProductHiss();
                    if (CollectionUtils.isNotEmpty(childrenContractProductHiss) && childrenContractProductHiss.size() > 0) {
                        for (ContractProductHis childrenContractProductHis : childrenContractProductHiss) {
                            amount = amount.add(childrenContractProductHis.getAmount());
                            excludingTaxAmount = excludingTaxAmount.add(childrenContractProductHis.getExcludingTaxAmount());
                            childrenContractProductHis.setId(null);
                            childrenContractProductHis.setChangewayId(contractHisDTO.getChangewayId());
                            contractProductHisMapper.insert(childrenContractProductHis);
                        }
                    }
                    childrenContractHisDTO.setOperationType(null);
                    contractHisExtMapper.updateByIdAndChangewayId(childrenContractHisDTO);

                    List<InvoicePlanHisDTO> childrenInvoicePlanHisDTOS = childrenContractHisDTO.getInvoicePlanHisDTOs();
                    // 开票计划新旧ID对应关系
                    Map<Long, Long> invoiceDetailIdMap = new HashMap<>();
                    Map<String, Long> invoiceDetailCodeMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(childrenInvoicePlanHisDTOS)) {
                        for (InvoicePlanHisDTO childrenInvoicePlanHisDTO : childrenInvoicePlanHisDTOS) {
                            InvoicePlanHis childrenInvoicePlanHis = childrenInvoicePlanHisDTO.conventToInvoicePlanHis();
                            childrenInvoicePlanHis.setId(null);
                            if (childrenInvoicePlanHis.getInvoicePlanId() == null) {
                                IdGenerator idGenerator = idGenerators.get(Long.class);
                                childrenInvoicePlanHis.setInvoicePlanId(Long.valueOf(idGenerator.next(InvoicePlan.class).toString()));
                            }
                            childrenInvoicePlanHis.setChangewayId(contractHisDTO.getChangewayId());
                            invoicePlanHisMapper.insert(childrenInvoicePlanHis);
                            List<InvoicePlanDetailHisDTO> invoicePlanDetailHiss = childrenInvoicePlanHisDTO.getInvoicePlanDetailsHiss();
                            int num = 1;
                            for (InvoicePlanDetailHis planDetailHiss : invoicePlanDetailHiss) {
                                // 删除的不作处理
                                if (planDetailHiss.getDeletedFlag() != null && planDetailHiss.getDeletedFlag()) {
                                    continue;
                                }
                                if (Objects.nonNull(planDetailHiss.getInvoicePlanDetailId())) {
                                    InvoicePlanDetail invoicePlanDetail = invoicePlanDetailMapper.selectByPrimaryKey(planDetailHiss.getInvoicePlanDetailId());
                                    if (Objects.nonNull(invoicePlanDetail)) {
                                        BigDecimal actualReceiptAmount = Optional.ofNullable(invoicePlanDetail.getActualReceiptAmount()).orElse(BigDecimal.ZERO);
                                        if (planDetailHiss.getAmount().subtract(actualReceiptAmount).compareTo(BigDecimal.ZERO) < 0) {
                                            throw new BizException(Code.ERROR, String.format("开票计划编号：%s开票金额不能小于“已回款金额”-“已退款金额”=%s", planDetailHiss.getCode(), actualReceiptAmount.stripTrailingZeros().toPlainString()));
                                        }
                                    }
                                }

                                planDetailHiss.setPlanId(childrenInvoicePlanHis.getInvoicePlanId());
                                Long oldId = planDetailHiss.getInvoicePlanDetailId();
                                planDetailHiss.setChangewayId(contractHisDTO.getChangewayId());

                                planDetailHiss.setNum(num++);
                                if (StringUtils.isEmpty(planDetailHiss.getCode())) {
                                    // 设置流水号
                                    String seqPerfix = basedataExtService.getUnitSeqPerfix(SystemContext.getUnitId());
                                    String code = seqPerfix + "KP" + CacheDataUtils.generateSequence(3, seqPerfix + "KP", DateUtil.DATE_YYMMDD_PATTERN);
                                    planDetailHiss.setCode(code);
                                }
                                invoicePlanDetailHisMapper.insert(planDetailHiss);
                                Long newId = planDetailHiss.getId();
                                invoiceDetailIdMap.put(oldId, newId);
                                invoiceDetailCodeMap.put(planDetailHiss.getCode(), newId);
                            }
                        }
                    }

                    List<ReceiptPlanHisDTO> childrenReceiptPlanHisDTOs = childrenContractHisDTO.getReceiptPlanHissDTOs();
                    if (CollectionUtils.isNotEmpty(childrenReceiptPlanHisDTOs)) {
                        for (ReceiptPlanHisDTO childrenReceiptPlanHisDTO : childrenReceiptPlanHisDTOs) {
                            ReceiptPlanHis childrenReceiptPlanHis = childrenReceiptPlanHisDTO.conventToReceiptPlanHis();
//                            childrenReceiptPlanHis.setAmount(amount);
                            ReceiptPlanHisExample receiptPlanHisExample = new ReceiptPlanHisExample();
                            if (childrenReceiptPlanHisDTO.getId() == null) {
                                receiptPlanHisExample.createCriteria().andChangewayIdEqualTo(contractHisDTO.getChangewayId())
                                        .andReceiptPlanIdEqualTo(childrenReceiptPlanHisDTO.getReceiptPlanId()).andContractIdEqualTo(childrenReceiptPlanHisDTO.getContractId());
                            } else {
                                receiptPlanHisExample.createCriteria().andIdEqualTo(childrenReceiptPlanHisDTO.getId());
                            }
                            List<ReceiptPlanHis> receiptPlanHisList = receiptPlanHisMapper.selectByExample(receiptPlanHisExample);
                            if (CollectionUtils.isNotEmpty(receiptPlanHisList)) {
                                ReceiptPlanHis receiptPlanHis = receiptPlanHisList.get(0);
                                childrenReceiptPlanHis.setId(receiptPlanHis.getId());
                                receiptPlanHisMapper.updateByPrimaryKeySelective(childrenReceiptPlanHis);
                            }
                            //更新详情
                            List<ReceiptPlanDetailHisDTO> childrenReceiptPlanDetailHisDTOs = childrenReceiptPlanHisDTO.getReceiptPlanDetailHisDTOS();
                            if (CollectionUtils.isNotEmpty(childrenReceiptPlanDetailHisDTOs)) {
                                int num = 1;
                                for (ReceiptPlanDetailHisDTO childrenReceiptPlanDetailHisDTO : childrenReceiptPlanDetailHisDTOs) {
                                    // 删除的不作处理
                                    if (childrenReceiptPlanDetailHisDTO.getDeletedFlag() != null && childrenReceiptPlanDetailHisDTO.getDeletedFlag()) {
                                        continue;
                                    }

                                    ReceiptPlanDetailHis childrenReceiptPlanDetailHis = childrenReceiptPlanDetailHisDTO.conventToReceiptPlanDetailHis();
                                    childrenReceiptPlanDetailHis.setId(null);
                                    childrenReceiptPlanDetailHis.setPlanId(childrenReceiptPlanHisDTO.getReceiptPlanId());
                                    childrenReceiptPlanDetailHis.setNum(num++);
                                    if (StringUtils.isEmpty(childrenReceiptPlanDetailHis.getCode())) {
                                        // 设置流水号
                                        String seqPerfix = basedataExtService.getUnitSeqPerfix(SystemContext.getUnitId());
                                        String code = seqPerfix + "HK" + CacheDataUtils.generateSequence(3, seqPerfix + "HK", DateUtil.DATE_YYMMDD_PATTERN);
                                        childrenReceiptPlanDetailHis.setCode(code);
                                    }
                                    childrenReceiptPlanDetailHis.setChangewayId(contractHisDTO.getChangewayId());
                                    receiptPlanDetailHisMapper.insert(childrenReceiptPlanDetailHis);
                                    //更新回款开票关系记录表
                                    List<ReceiptInvoiceRelationHisDTO> childrenReceiptInvoiceRelationHisDTOs = childrenReceiptPlanDetailHisDTO.getReceiptInvoiceRelationHisDTOs();
                                    if (CollectionUtils.isNotEmpty(childrenReceiptInvoiceRelationHisDTOs) && childrenReceiptInvoiceRelationHisDTOs.size() > 0) {
                                        for (ReceiptInvoiceRelationHisDTO childrenReceiptInvoiceRelationHisDTO : childrenReceiptInvoiceRelationHisDTOs) {
                                            // 删除的不作处理
                                            if (childrenReceiptInvoiceRelationHisDTO.getDeletedFlag() != null
                                                    && childrenReceiptInvoiceRelationHisDTO.getDeletedFlag()) {
                                                continue;
                                            }

                                            ReceiptInvoiceRelationHis childrenReceiptInvoiceRelationHis = childrenReceiptInvoiceRelationHisDTO.conventToReceiptInvoiceRelationHis();

                                            Long receiptPlanDetailHisId = childrenReceiptPlanDetailHis.getId();
                                            childrenReceiptInvoiceRelationHis.setReceiptPlanDetailId(receiptPlanDetailHisId);
                                            childrenReceiptInvoiceRelationHis.setChangewayId(contractHisDTO.getChangewayId());

                                            String invoicePlanDetailCode = childrenReceiptInvoiceRelationHisDTO.getInvoicePlanDetailCode();
                                            Long newId = invoiceDetailCodeMap.get(invoicePlanDetailCode);

                                            // 已经删除的开票计划
                                            if (newId == null) {
                                                continue;
                                            }
                                            childrenReceiptInvoiceRelationHis.setInvoicePlanDetailId(newId);
                                            childrenReceiptInvoiceRelationHis.setDeletedFlag(Boolean.FALSE);
                                            receiptInvoiceRelationHisMapper.insert(childrenReceiptInvoiceRelationHis);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    //更新收入成本计划
                    if (!ObjectUtils.isEmpty(childrenContractHisDTO.getProjectProfitHisDTO())) {
                        ProjectProfitHisDTO projectProfitHisDTO = childrenContractHisDTO.getProjectProfitHisDTO();
                        ProjectProfitHis projectProfitHis = new ProjectProfitHis();
                        projectProfitHis.setId(projectProfitHisDTO.getId());
                        projectProfitHis.setMainIncome(projectProfitHisDTO.getMainIncome());
                        projectProfitHis.setHelpIncome(projectProfitHisDTO.getHelpIncome());
                        projectProfitHisMapper.updateByPrimaryKey(projectProfitHis);
                    }

                    //产品清单(变更项)
                    List<ContractInventoryHisDTO> contractInventoryHisList = childrenContractHisDTO.getContractInventoryHisList();
                    if (CollectionUtils.isNotEmpty(contractInventoryHisList)) {
                        for (ContractInventoryHisDTO contractInventoryHis : contractInventoryHisList) {
//                            contractInventoryHis.setOriginId(contractInventoryHis.getId()); 重新编辑这里的id是ContractInventoryHis的id
                            contractInventoryHis.setId(null);
                            contractInventoryHis.setContractId(childrenContractHisDTO.getContractId());
                            contractInventoryHis.setParentContractId(contractHisDTO.getContractId());
                            contractInventoryHis.setChangewayId(contractHisDTO.getChangewayId());
                            contractInventoryHis.setDeletedFlag(Optional.ofNullable(contractInventoryHis.getDeletedFlag()).orElse(DeletedFlag.VALID.code()));
                            contractInventoryHis.setHistoryType(HistoryType.CHANGE.getCode());
                            contractInventoryHisMapper.insert(contractInventoryHis);
                            logger.info("contractInventoryHisMapper contractHisId:" + contractInventoryHis.getContractId() + "---" + contractInventoryHis.getId());
                        }
                    }
                }
            }

            //补全被删掉的主合同服务类型数据
            List<ContractProductHis> contractProductHiss = contractHisDTO.getContractProductHiss();
            if (CollectionUtils.isNotEmpty(contractProductHiss)) {
                for (ContractProductHis contractProductHis : contractProductHiss) {
                    contractProductHis.setId(null);
                    contractProductHisMapper.insert(contractProductHis);
                }
            }

            // 该变更完成之后最终需要维护的子合同与采购申请的信息
            ContractHisExample contractHisExample = new ContractHisExample();
            contractHisExample.createCriteria().andChangewayIdEqualTo(againChangewayId).andDeletedFlagEqualTo(Boolean.FALSE).andParentIdIsNotNull();
            final List<ContractHis> childrenContractHisList = contractHisMapper.selectByExample(contractHisExample);

            if (CollectionUtils.isNotEmpty(childrenContractHisList)) {
                Map<Long, String> childrenContractHisMap = new HashMap<>();
                List<Long> childrenContractHisIdList = childrenContractHisList.stream().map(c -> c.getId()).collect(Collectors.toList());
                for (ContractHis his : childrenContractHisList) {
                    childrenContractHisMap.put(his.getId(), his.getEampurchaseId());
                }
                eamPurchaseInfoService.saveContractEamPurchaseRel(childrenContractHisIdList, childrenContractHisMap, Boolean.TRUE);
            }
            return againChangewayId;
        } else {
            /**
             * ------------------------------------ 首次合同服务内容变更开始..... -------------------------------------------
             */
            logger.info("合同服务内容变更开始...");
            //插入合同变更记录头表
            Long changewayId = insertToContractChangewayType(contractHisDTO, type);
            logger.info("变更记录表已被插入,changewayId= " + changewayId);
            //插入合同记录表（主）,插入的是页面数据
            ContractHis contractHis = contractHisDTO.convertToContractHis();
            contractHis.setId(null);
            contractHis.setChangewayId(changewayId);
            if (null != contractHisDTO.getIsUpdate() && contractHisDTO.getIsUpdate()) {
                contractHis.setFileAttachmentId(contractHisDTO.getFileAttachmentId());
            }
            contractHisMapper.insert(contractHis);


            logger.info("主合同记录表已被插入,id= " + contractHis.getId());
            //插入合同产品(主)，插入的是页面数据
            if (CollectionUtils.isNotEmpty(contractHisDTO.getContractProductHiss()) && contractHisDTO.getContractProductHiss().size() > 0) {
                //合同产品记录(主)
                ContractProductHis contractProductHis = contractHisDTO.getContractProductHiss().get(0);
                contractProductHis.setId(null);
                contractProductHis.setChangewayId(changewayId);
                contractProductHisMapper.insert(contractProductHis);
                logger.info("合同产品(主)记录表已被插入,contractProductHis: " + contractProductHis.toString());
            }
            //插入子合同记录，包括其产品，成本，开票「详情」，回款「详情，开票回款关联」
            final List<ContractHisDTO> childrenContractHisDTOAll = contractHisDTO.getChildrenContractHiss();
            /***
             *  ----------------------------   合同服务内容变更--新增子合同 added at 20200401 --------------------------------
             */
            //子合同新增
            List<ContractHisDTO> childrencontractHisDTOsTypeAdd = childrenContractHisDTOAll.stream().filter(c -> null != c.getOperationType() && c.getOperationType().equals(OPERATION_TYPE_ONE)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(childrencontractHisDTOsTypeAdd)) {
                for (ContractHisDTO hisDTO : childrencontractHisDTOsTypeAdd) {
                    hisDTO.setChangewayId(changewayId);
                }
                contractHisService.chidrenContractAddSer(childrencontractHisDTOsTypeAdd);
            }

            //子合同删除
            List<ContractHisDTO> childrencontractHisDTOsTypeDel = childrenContractHisDTOAll.stream().filter(c -> null != c.getOperationType() && c.getOperationType().equals(OPERATION_TYPE_TWO)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(childrencontractHisDTOsTypeDel)) {
                for (ContractHisDTO hisDTO : childrencontractHisDTOsTypeDel) {
                    hisDTO.setChangewayId(changewayId);

                    Contract contract2 = new Contract();
                    contract2.setId(hisDTO.getContractId());
                    contract2.setChangewayId(changewayId);
                    contractMapper.updateByPrimaryKeySelective(contract2);
                }
                contractHisService.chidrenContractDelSer(childrencontractHisDTOsTypeDel);
            }
            List<ContractHisDTO> childrenContractHisDTOs = childrenContractHisDTOAll.stream().filter(c -> c.getOperationType() == null).collect(Collectors.toList());
            // 原有服务内容变更逻辑不变
            childrenContractHisDTOs.forEach(childrenContractHisDTO -> {

                //更新子合同changewayId
                Contract contract1 = new Contract();
                contract1.setId(childrenContractHisDTO.getContractId());
                contract1.setChangewayId(changewayId);
                contractMapper.updateByPrimaryKeySelective(contract1);
                //子合同服务类型&产品总金额
                BigDecimal num = BigDecimal.ZERO;
                BigDecimal amount = num.add(childrenContractHisDTO.getAmount());
                final ContractHis childrenContractHis = childrenContractHisDTO.convertToContractHis();
                childrenContractHis.setId(null);
                childrenContractHis.setChangewayId(changewayId);
                contractHisMapper.insert(childrenContractHis);
                logger.info("子合同记录表已被插入,childrenContractHis: " + childrenContractHis.toString());
                //插入子合同产品计划记录
                List<ContractProductHis> childrenContractProductHiss = childrenContractHisDTO.getContractProductHiss();
                Set<String> serviceType = new HashSet<>();
                List<String> childrenProductTypeNames = new ArrayList<>();
                List<String> childrenContractAmountTaxs = new ArrayList<>();
                List<String> childrenContractInvPlanAmounts = new ArrayList<>();
                List<String> childrenContractRecPlanAmounts = new ArrayList<>();
                Boolean isInvoice = isInvoice(childrenContractHisDTO.getContractId());

                if (CollectionUtils.isNotEmpty(childrenContractProductHiss) && childrenContractProductHiss.size() > 0) {
                    //删除再次做相同变更时留下的数据
                    contractProductHisMapper.deleteByConIdAndChanId(childrenContractHisDTO.getContractId());
                    for (ContractProductHis childrenContractProductHis : childrenContractProductHiss) {
                        childrenContractProductHis.setId(null);
                        childrenContractProductHis.setChangewayId(changewayId);
                        contractProductHisMapper.insert(childrenContractProductHis);
                        serviceType.add(childrenContractProductHis.getProductTypeName());
                        childrenProductTypeNames.add(String.valueOf(childrenContractProductHis.getContractProductId()));
                        childrenContractAmountTaxs.add(childrenContractProductHis.getAmount().toString());
                        logger.info("子合同产品计划记录表已被插入,childrencontractProductHis:" + childrenContractProductHis.toString());
                    }
                    childrenContractAmountTaxs.sort(Comparator.comparing(String::hashCode));

                }

                //插入子合同产品成本计划记录
                List<ContractProductCostHis> childrenContractProductCostHiss = childrenContractHisDTO.getContractProductCostHiss();
                if (CollectionUtils.isNotEmpty(childrenContractProductCostHiss) && childrenContractProductCostHiss.size() > 0) {
                    for (ContractProductCostHis childrenContractProductCostHis : childrenContractProductCostHiss) {
                        childrenContractProductCostHis.setId(null);
                        childrenContractProductCostHis.setChangewayId(changewayId);
                        contractProductCostHisMapper.insert(childrenContractProductCostHis);
                        logger.info("子合同产品成本计划记录表已被插入,childrenContractProductCostHis: " + childrenContractProductCostHis.toString());
                    }
                } else {
//                    throw new MipException("子合同收入成本计划不能为空！");
                    logger.info("该数据已经不需要再保留，不在做子合同产品成本计划");
                }
                //插入子合同开票计划以及详情
                List<InvoicePlanHisDTO> childrenInvoicePlanHisDTOS = childrenContractHisDTO.getInvoicePlanHisDTOs();
                //============================>>>>>>>>>服务类型&产品条目增加对应的开票计划需要同步调整
                if (childrenContractProductHiss.size() != childrenInvoicePlanHisDTOS.size() && serviceType.size() > 2) {
                    throw new MipException("服务类型&产品条目和开票计划数目不一致！");
                }
                if (isInvoice) {
                    //============================>>>>>>>>>开票计划业务规则校验
                    changesOfInv(childrenInvoicePlanHisDTOS);
                }
                String seqPerfix = basedataExtService.getUnitSeqPerfix(SystemContext.getUnitId());
                Map<Long, Long> idMap = new HashMap<>();
                Map<String, Long> invoiceDetailCodeMap = new HashMap<>();

                if (CollectionUtils.isNotEmpty(childrenInvoicePlanHisDTOS) && childrenInvoicePlanHisDTOS.size() > 0) {
                    //合同实际开票金额   actualTotalAmount
                    BigDecimal actualTotalAmount = BigDecimal.ZERO;
                    for (InvoicePlanHisDTO childrenInvoicePlanHisDTO : childrenInvoicePlanHisDTOS) {
                        InvoicePlanHis childrenInvoicePlanHis = childrenInvoicePlanHisDTO.conventToInvoicePlanHis();
                        childrenInvoicePlanHis.setId(null);
                        //新增头
                        if (childrenInvoicePlanHis.getInvoicePlanId() == null) {
                            IdGenerator idGenerator = idGenerators.get(Long.class);
                            childrenInvoicePlanHis.setInvoicePlanId(Long.valueOf(idGenerator.next(InvoicePlan.class).toString()));
                        }
                        childrenInvoicePlanHis.setChangewayId(changewayId);
                        invoicePlanHisMapper.insert(childrenInvoicePlanHis);
                        logger.info("子合同开票计划记录表已被插入,id= " + childrenInvoicePlanHis.getId());
                        if (isInvoice) {
                            BigDecimal actualAmount = childrenInvoicePlanHisDTO.getActualAmount();
                            actualTotalAmount = actualTotalAmount.add(actualAmount != null ? actualAmount : BigDecimal.ZERO);
                        }

                        childrenContractInvPlanAmounts.add(childrenInvoicePlanHisDTO.getAmount().toString());

                        List<InvoicePlanDetailHisDTO> invoicePlanDetailHiss = childrenInvoicePlanHisDTO.getInvoicePlanDetailsHiss();
                        int i = 1;
                        for (InvoicePlanDetailHis invoicePlanDetailHis : invoicePlanDetailHiss) {
                            // 删除的不作处理
                            if (invoicePlanDetailHis.getDeletedFlag() != null && invoicePlanDetailHis.getDeletedFlag()) {
                                continue;
                            }

                            if (Objects.nonNull(invoicePlanDetailHis.getInvoicePlanDetailId())) {
                                InvoicePlanDetail invoicePlanDetail = invoicePlanDetailMapper.selectByPrimaryKey(invoicePlanDetailHis.getInvoicePlanDetailId());
                                if (Objects.nonNull(invoicePlanDetail)) {
                                    BigDecimal actualReceiptAmount = Optional.ofNullable(invoicePlanDetail.getActualReceiptAmount()).orElse(BigDecimal.ZERO);
                                    if (invoicePlanDetailHis.getAmount().subtract(actualReceiptAmount).compareTo(BigDecimal.ZERO) < 0) {
                                        throw new BizException(Code.ERROR, String.format("开票计划编号：%s开票金额不能小于“已回款金额”-“已退款金额”=%s", invoicePlanDetailHis.getCode(), actualReceiptAmount.stripTrailingZeros().toPlainString()));
                                    }
                                }
                            }

                            invoicePlanDetailHis.setId(null);
                            //新增行
                            invoicePlanDetailHis.setPlanId(childrenInvoicePlanHis.getInvoicePlanId());
                            invoicePlanDetailHis.setChangewayId(changewayId);
                            if (StringUtils.isEmpty(invoicePlanDetailHis.getCode())) {
                                // 设置流水号
                                //String code = "KP" + CacheDataUtils.generateSequence(3, "KP");
                                String code = seqPerfix + "KP" + CacheDataUtils.generateSequence(3, seqPerfix + "KP", DateUtil.DATE_YYMMDD_PATTERN);
                                invoicePlanDetailHis.setCode(code);
                            }
                            // 设置笔数
                            invoicePlanDetailHis.setNum(i++);
                            invoicePlanDetailHisMapper.insert(invoicePlanDetailHis);
                            idMap.put(invoicePlanDetailHis.getInvoicePlanDetailId(), invoicePlanDetailHis.getId());
                            invoiceDetailCodeMap.put(invoicePlanDetailHis.getCode(), invoicePlanDetailHis.getId());
                            logger.info("子合同开票计划详情记录表已被插入,childrenInvoicePlanDetailHis: " + invoicePlanDetailHis.getId());
                        }
                    }

                    //============================>>>>>>>>>服务类型&产品修改金额不得小于开票金额
                    if (isInvoice) {
                        if (amount.compareTo(actualTotalAmount) < 0) {
                            throw new MipException("服务产品类型总金额不得小于实际开票金额！");
                        }
                    }

                } else {
                    throw new MipException("子合同开票计划不能为空！");
                }

                //插入子合同回款计划、详情以及回款开票关系记录数据
                List<ReceiptPlanHisDTO> childrenReceiptPlanHisDTOs = childrenContractHisDTO.getReceiptPlanHissDTOs();
                //============================>>>>>>>>>回款计划业务规则校验
                changesOfRec(childrenReceiptPlanHisDTOs);
                if (CollectionUtils.isNotEmpty(childrenReceiptPlanHisDTOs) && childrenReceiptPlanHisDTOs.size() > 0) {

                    // 回款计划变更时，新增的开票计划无编号，无法与自动生成的回款计划进行关联，故通过newTag字段在后端进行关联
                    Map<String, String> codeMap = new HashMap<>();
                    for (InvoicePlanHisDTO dto : childrenInvoicePlanHisDTOS) {
                        for (InvoicePlanDetailHisDTO detail : dto.getInvoicePlanDetailsHiss()) {
                            if (StringUtils.isNotEmpty(detail.getNewTag())) {
                                codeMap.put(detail.getNewTag(), detail.getCode());
                            }
                        }
                    }

                    for (ReceiptPlanHisDTO childrenReceiptPlanHisDTO : childrenReceiptPlanHisDTOs) {
                        ReceiptPlanHis childrenReceiptPlanHis = childrenReceiptPlanHisDTO.conventToReceiptPlanHis();
                        childrenReceiptPlanHis.setId(null);
                        childrenReceiptPlanHis.setChangewayId(changewayId);
                        receiptPlanHisMapper.insert(childrenReceiptPlanHis);
                        logger.info("子合同回款计划记录表已被插入,childrenReceiptPlanHis: " + childrenReceiptPlanHis.toString());
                        childrenContractRecPlanAmounts.add(childrenReceiptPlanHisDTO.getAmount().toString());
                        //插入详情
                        List<ReceiptPlanDetailHisDTO> childrenReceiptPlanDetailHisDTOs = childrenReceiptPlanHisDTO.getReceiptPlanDetailHisDTOS();
                        int i = 1;
                        for (ReceiptPlanDetailHisDTO childrenReceiptPlanDetailHisDTO : childrenReceiptPlanDetailHisDTOs) {
                            // 删除的不作处理
                            if (childrenReceiptPlanDetailHisDTO.getDeletedFlag() != null && childrenReceiptPlanDetailHisDTO.getDeletedFlag()) {
                                continue;
                            }

                            ReceiptPlanDetailHis childrenReceiptPlanDetailHis = childrenReceiptPlanDetailHisDTO.conventToReceiptPlanDetailHis();
                            childrenReceiptPlanDetailHis.setId(null);
                            if (childrenReceiptPlanDetailHis.getPlanId() == null) {
                                childrenReceiptPlanDetailHis.setPlanId(childrenReceiptPlanHis.getReceiptPlanId());
                            }
                            childrenReceiptPlanDetailHis.setChangewayId(changewayId);
                            if (StringUtils.isEmpty(childrenReceiptPlanDetailHis.getCode())) {
                                // 设置流水号
                                //String code = "HK" + CacheDataUtils.generateSequence(3, "HK");
                                String code = seqPerfix + "HK" + CacheDataUtils.generateSequence(3, seqPerfix + "HK", DateUtil.DATE_YYMMDD_PATTERN);

                                childrenReceiptPlanDetailHis.setCode(code);
                            }
                            // 设置笔数
                            childrenReceiptPlanDetailHis.setNum(i++);
                            receiptPlanDetailHisMapper.insert(childrenReceiptPlanDetailHis);
                            logger.info("子合同回款详情计划记录表已被插入,childrenReceiptPlanDetailHis: " + childrenReceiptPlanDetailHis.toString());

                            List<ReceiptInvoiceRelationHisDTO> childrenReceiptInvoiceRelationHisDTOs = childrenReceiptPlanDetailHisDTO.getReceiptInvoiceRelationHisDTOs();
                            if (CollectionUtils.isNotEmpty(childrenReceiptInvoiceRelationHisDTOs)) {
                                for (ReceiptInvoiceRelationHisDTO childrenReceiptInvoiceRelationHisDTO : childrenReceiptInvoiceRelationHisDTOs) {
                                    ReceiptInvoiceRelationHis childrenReceiptInvoiceRelationHis = childrenReceiptInvoiceRelationHisDTO.conventToReceiptInvoiceRelationHis();
                                    childrenReceiptInvoiceRelationHis.setId(null);
                                    childrenReceiptInvoiceRelationHis.setChangewayId(changewayId);
                                    childrenReceiptInvoiceRelationHis.setReceiptPlanDetailId(childrenReceiptPlanDetailHis.getId());
                                    Long invoiceDetailId = invoiceDetailCodeMap.get(childrenReceiptInvoiceRelationHisDTO.getInvoicePlanDetailCode());
                                    // 已经删除的开票计划
                                    if (invoiceDetailId == null) {
                                        continue;
                                    }
                                    childrenReceiptInvoiceRelationHis.setInvoicePlanDetailId(invoiceDetailId);
                                    childrenReceiptInvoiceRelationHis.setDeletedFlag(false);
                                    receiptInvoiceRelationHisMapper.insert(childrenReceiptInvoiceRelationHis);
                                    logger.info("子合同回款开票关系记录表已被插入,receiptInvoiceRelationHis: " + childrenReceiptInvoiceRelationHis.toString());
                                }
                            } else if (StringUtils.isNotEmpty(childrenReceiptPlanDetailHisDTO.getNewTag())) {
                                String invoicePlanDetailCode = codeMap.get(childrenReceiptPlanDetailHisDTO.getNewTag());
                                if (invoicePlanDetailCode != null) {
                                    Long invoiceDetailId = invoiceDetailCodeMap.get(invoicePlanDetailCode);
                                    ReceiptInvoiceRelationHis relationHis = new ReceiptInvoiceRelationHis();
                                    relationHis.setChangewayId(changewayId);
                                    relationHis.setContractId(childrenReceiptPlanDetailHisDTO.getContractId());
                                    relationHis.setDeletedFlag(Boolean.FALSE);
                                    relationHis.setCreateAt(new Date());
                                    relationHis.setCreateBy(SystemContext.getUserId());
                                    relationHis.setInvoicePlanDetailId(invoiceDetailId);
                                    relationHis.setReceiptPlanDetailId(childrenReceiptPlanDetailHis.getId());
                                    receiptInvoiceRelationHisMapper.insert(relationHis);
                                }
                            }
                        }
                    }

                } else {
                    throw new MipException("子合同回款计划不能为空！");
                }

                //插入收入成本计划记录表
                if (!ObjectUtils.isEmpty(childrenContractHisDTO.getProjectProfitHisDTO())) {
                    ProjectProfitHisDTO projectProfitHisDTO = childrenContractHisDTO.getProjectProfitHisDTO();
                    ProjectProfitHis projectProfitHis = projectProfitHisDTO.convertToProjectProfitHis();
                    projectProfitHis.setId(null);
                    projectProfitHis.setChangewayId(changewayId);
                    projectProfitHisMapper.insert(projectProfitHis);
                    logger.info("子合同收入成本计划记录表已被插入,projectProfitHis: " + projectProfitHis.toString());
                }

                //产品清单(变更项)
                List<ContractInventoryHisDTO> contractInventoryHisList = childrenContractHisDTO.getContractInventoryHisList();
                if (CollectionUtils.isNotEmpty(contractInventoryHisList)) {
                    for (ContractInventoryHisDTO contractInventoryHis : contractInventoryHisList) {
                        contractInventoryHis.setOriginId(contractInventoryHis.getId());
                        contractInventoryHis.setId(null);
                        contractInventoryHis.setContractId(childrenContractHisDTO.getContractId());
                        contractInventoryHis.setParentContractId(contractHisDTO.getContractId());
                        contractInventoryHis.setChangewayId(changewayId);
                        contractInventoryHis.setDeletedFlag(Optional.ofNullable(contractInventoryHis.getDeletedFlag()).orElse(DeletedFlag.VALID.code()));
                        contractInventoryHis.setHistoryType(HistoryType.CHANGE.getCode());
                        contractInventoryHisMapper.insert(contractInventoryHis);
                        logger.info("contractInventoryHisMapper contractHisId:" + contractInventoryHis.getContractId() + "---" + contractInventoryHis.getId());
                    }
                }

            });
            //更新主合同中的changewayId,标识该合同已经被更改过
            Contract contractUpdate = new Contract();
            contractUpdate.setId(contractHisDTO.getContractId());
            contractUpdate.setChangewayId(changewayId);
            contractMapper.updateByPrimaryKeySelective(contractUpdate);
            logger.info("销售合同基础信息变更结束...");

            //将变更后的子合同与采购申请关系进行维护
            ContractHisExample contractHisExample = new ContractHisExample();
            contractHisExample.createCriteria().andChangewayIdEqualTo(changewayId).andDeletedFlagEqualTo(Boolean.FALSE).andParentIdIsNotNull();
            final List<ContractHis> childrenContractHisList = contractHisMapper.selectByExample(contractHisExample);

            if (CollectionUtils.isNotEmpty(childrenContractHisList)) {
                Map<Long, String> childrenContractHisMap = new HashMap<>();
                for (ContractHis his : childrenContractHisList) {
                    childrenContractHisMap.put(his.getId(), his.getEampurchaseId());
                }
                eamPurchaseInfoService.saveContractEamPurchaseRel(null, childrenContractHisMap, Boolean.TRUE);
            }

            /** 插入子合同产品清单历史项 **/
            List<ContractVO> childrenContractList = contractVO.getChildrenContracts();
            for (ContractVO childrenContractVO : childrenContractList) {
                if (childrenContractVO != null) {
                    List<ContractInventoryHis> contractInventoryHisList = BeanConverter.copy(childrenContractVO.getContractInventoryList(), ContractInventoryHis.class);
                    if (CollectionUtils.isNotEmpty(contractInventoryHisList)) {
                        for (ContractInventoryHis childrenContractInventoryHistory : contractInventoryHisList) {
                            childrenContractInventoryHistory.setOriginId(childrenContractInventoryHistory.getId());
                            childrenContractInventoryHistory.setId(null);
                            childrenContractInventoryHistory.setChangewayId(changewayId);
                            childrenContractInventoryHistory.setHistoryType(HistoryType.HISTORY.getCode());
                            childrenContractInventoryHistory.setDeletedFlag(DeletedFlag.VALID.code());
                            contractInventoryHisMapper.insert(childrenContractInventoryHistory);
                            logger.info("子合同产品清单表历史项已被插入,childrenContractInventoryHistory:" + childrenContractInventoryHistory.toString());
                        }
                    }
                }
            }

            /**
             * ------------------------------------ 合同服务内容变更结束..... -------------------------------------------
             */
            return changewayId;
        }
    }

    @Override
    public ContractHisDTO saveInvoicePlanDetails(ContractHisDTO contractHisDTO) {
        Long changewayId = contractHisDTO.getChangewayId();
        List<ContractHisDTO> childrenContractHiss = contractHisDTO.getChildrenContractHiss();
        if (CollectionUtils.isNotEmpty(childrenContractHiss)) {
            String seqPerfix = basedataExtService.getUnitSeqPerfix(SystemContext.getUnitId());

            for (ContractHisDTO contractHiss : childrenContractHiss) {
                List<InvoicePlanHisDTO> invoicePlanHisDTOs = contractHiss.getInvoicePlanHisDTOs();
                if (CollectionUtils.isEmpty(invoicePlanHisDTOs)) {
                    continue;
                }

                for (InvoicePlanHisDTO invoicePlanHisDTO : invoicePlanHisDTOs) {
                    List<InvoicePlanDetailHisDTO> invoicePlanDetailsHiss = invoicePlanHisDTO.getInvoicePlanDetailsHiss();
                    if (CollectionUtils.isEmpty(invoicePlanDetailsHiss)) {
                        continue;
                    }

                    invoicePlanDetailsHiss.forEach(invoicePlanDetailHis -> {
                        Long id = invoicePlanDetailHis.getId();
                        // 新增
                        if (id == null) {
                            invoicePlanDetailHis.setChangewayId(changewayId);
                            if (StringUtils.isEmpty(invoicePlanDetailHis.getCode())) {
                                // 设置流水号
                                //String code = "HK" + CacheDataUtils.generateSequence(3, "HK");
                                String code = seqPerfix + "KP" + CacheDataUtils.generateSequence(3, seqPerfix + "KP", DateUtil.DATE_YYMMDD_PATTERN);
                                invoicePlanDetailHis.setCode(code);

                                invoicePlanDetailHisMapper.insert(invoicePlanDetailHis);
                            }
                        }
                    });
                }
            }
        }
        return contractHisDTO;
    }

    /**
     * 移动审批销售合同新增
     *
     * @param id
     * @return
     */
    @Override
    public ResponseMap getContractApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> childrenMapList = new ArrayList<>();
        List<AduitAtta> fileList = new ArrayList<>();
        ContractVO contractVO = new ContractVO();
        // 主合同
        final Contract contract = this.findById(id);
        if (ObjectUtils.isEmpty(contract)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("头信息不能为空");
        }
        BeanConverter.copy(contract, contractVO);
        contractHelper.formatContractVO(contractVO);
        headMap.put("name", contractVO.getName()); //合同名称
        headMap.put("legalAffairsId", contractVO.getLegalContractNum()); //法务合同编号
        headMap.put("unitName", contractVO.getUnitName()); //销售部门
        headMap.put("customerName", contractVO.getCustomerName());
        headMap.put("salesManagerName", contractVO.getSalesManagerName()); //销售经理
        headMap.put("managerName", contractVO.getManagerName()); //项目经理
        headMap.put("currency", contractVO.getCurrency()); //币种
        String amountStr = String.valueOf(contractVO.getAmount());
        headMap.put("amount", amountStr.length() > 0 ? amountStr.substring(0, amountStr.length() - 1) : ""); //合同金额含税
        headMap.put("ouName", contractVO.getOuName()); //业务实体
        headMap.put("isSynchronizeLegalSystemFlag", Boolean.TRUE.equals(contractVO.getIsSynchronizeLegalSystemFlag()) ? "是" : "否"); //同步法务系统
        headMap.put("notsyncType", contractVO.getNotsyncType()); //不同步类型
        headMap.put("notsyncReason", contractVO.getNotsyncReason()); //不同步原因
        // 获取行业信息
        String industryStr = getIndustryStr(contract.getIndustry());
        headMap.put("industryStr", industryStr);
        // 获取签约中心信息
        String signingCenterStr = getSigningCenterStr(contract.getSigningCenter());
        headMap.put("signingCenterStr", signingCenterStr);
        //子合同
        ContractExample contractExample = new ContractExample();
        ContractExample.Criteria criteria = contractExample.createCriteria();
        criteria.andParentIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE);
        final List<Contract> childrenContractList = contractMapper.selectByExample(contractExample);
        BigDecimal totalExcludingTaxAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(childrenContractList)) {
            for (Contract childrenContract : childrenContractList) {
                Map<String, String> childrenMap = new HashMap<>();
                ContractVO childrenContractVO = new ContractVO();
                BeanConverter.copy(childrenContract, childrenContractVO);
                contractHelper.formatContractVO(childrenContractVO);
                contractHelper.formatProfitDepartment(childrenContractVO);
                childrenMap.put("name", childrenContract.getName());
                String childrenAmount = childrenContract.getAmount() == null ? "" : String.valueOf(childrenContract.getAmount());
                String childrenExcludingTaxAmount = childrenContractVO.getExcludingTaxAmount() == null ? "" : String.valueOf(childrenContractVO.getExcludingTaxAmount());
                String childrenCost = childrenContract.getCost() == null ? "" : String.valueOf(childrenContract.getCost());
                childrenMap.put("amount", childrenAmount.length() > 0 ? childrenAmount.substring(0, childrenAmount.length() - 1) : "");
                childrenMap.put("excludingTaxAmount", childrenExcludingTaxAmount.length() > 0 ? childrenExcludingTaxAmount.substring(0, childrenExcludingTaxAmount.length() - 1) : "");
                childrenMap.put("cost", childrenCost.substring(0, childrenCost.length() - 1)); //预估成本不含税
                childrenMap.put("managerName", childrenContractVO.getManagerName());
                childrenMap.put("businessName", childrenContractVO.getProjectTypeName());//业务模式
                childrenMap.put("customerName", childrenContractVO.getCustomerName());//开票客户
                childrenMap.put("profitDepartmentName", childrenContractVO.getProfitDepartmentName());//虚拟部门
                childrenMap.put("startTime", null != childrenContractVO.getStartTime() ? DateUtils.format(childrenContractVO.getStartTime(), "yyyy-MM-dd") : "");
                childrenMap.put("endTime", null != childrenContractVO.getEndTime() ? DateUtils.format(childrenContractVO.getEndTime(), "yyyy-MM-dd") : "");
                childrenMapList.add(childrenMap);
                totalExcludingTaxAmount = totalExcludingTaxAmount.add(childrenContractVO.getExcludingTaxAmount() == null ? BigDecimal.ZERO : childrenContractVO.getExcludingTaxAmount());
            }
        }
        String excludingTaxAmountStr = totalExcludingTaxAmount == null ? "" : String.valueOf(totalExcludingTaxAmount);
        headMap.put("excludingTaxAmount", excludingTaxAmountStr.length() > 0 ? excludingTaxAmountStr.substring(0, excludingTaxAmountStr.length() - 1) : "");  //合同金额不含税
        responseMap.setHeadMap(headMap);
        String originalContractAnnex = contractVO.getOriginalContractAnnex();
        List<Long> idList = new ArrayList<>();
        for (String oAnnex : originalContractAnnex.split(",")) {
            try {
                idList.add(Long.parseLong(oAnnex));
            } catch (NumberFormatException e) {
                logger.error(e.getMessage(), e);
            }
        }
        if (CollectionUtils.isNotEmpty(idList)) {
            for (Long aLong : idList) {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(String.valueOf(aLong));
                fileList.add(aduitAtta);
            }
        }
        responseMap.setList1(childrenMapList);
        responseMap.setFileList(fileList);
        responseMap.setStatus("success");
        responseMap.setMsg("success");
        return responseMap;
    }

    /**
     * 移动审批基本信息变更
     *
     * @param id
     * @return
     */
    @Override
    public ResponseMap getContractBaseInfoChanageApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> headListMap = new ArrayList<>();
        List<AduitAtta> fileList = new ArrayList<>();
        List<Map<String, String>> detailMapList = new ArrayList<>();
        ContractVO contractVO = new ContractVO();
        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(id);
        // 主合同
        final Contract contract = this.findById(contractChangeway.getContractId());
        if (ObjectUtils.isEmpty(contract)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("头信息不能为空");
        }
        BeanConverter.copy(contract, contractVO);
        contractHelper.formatContractVO(contractVO);
        String startTime = null != contractVO.getStartTime() ? DateUtils.format(contractVO.getStartTime(), "yyyy-MM-dd") : "";
        String endTime = null != contractVO.getEndTime() ? DateUtils.format(contractVO.getEndTime(), "yyyy-MM-dd") : "";
        headMap.put("name", contractVO.getName() == null ? "" : contractVO.getName()); //合同名称
//        headMap.put("changeType","基本信息变更");
        headMap.put("changeReasonType", contractChangeway.getChangeReasonType() == null ? "" : contractChangeway.getChangeReasonType()); //变更原因类型
        headMap.put("changeReason", contractChangeway.getChangeReason() == null ? "" : contractChangeway.getChangeReason());
        headMap.put("unitName", contractVO.getUnitName() == null ? "" : contractVO.getUnitName()); //销售部门
        headMap.put("customerName", contractVO.getCustomerName() == null ? "" : contractVO.getCustomerName());
        headMap.put("salesManagerName", contractVO.getSalesManagerName() == null ? "" : contractVO.getSalesManagerName()); //销售经理
        headMap.put("managerName", contractVO.getManagerName() == null ? "" : contractVO.getManagerName()); //项目经理
        headMap.put("time", startTime + "-" + endTime); //有效期
        headMap.put("currency", contractVO.getCurrency() == null ? "" : contractVO.getCurrency()); //币种
        String amount = contractVO.getAmount() == null ? "0.000" : String.valueOf(contractVO.getAmount());
        headMap.put("amount", amount.substring(0, amount.length() - 1));//合同金额含税
        headMap.put("ouName", contractVO.getOuName() == null ? " " : contractVO.getOuName()); //业务实体

        //变更内容 变更前 变更后的数据
        ContractHisExample contractHisExample = new ContractHisExample();
        ContractHisExample.Criteria criteria = contractHisExample.createCriteria();
        criteria.andContractIdEqualTo(contract.getId()).andChangewayIdEqualTo(contractVO.getChangewayId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ContractHis> contractHisList = contractHisMapper.selectByExample(contractHisExample);
        ContractVO contractHisVO = new ContractVO();
        BigDecimal totalExcludingTaxAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(contractHisList)) {
            Map<String, String> fieldNameMap = new HashMap<>();
            fieldNameMap.put("name", "合同名称");
            fieldNameMap.put("startTime", "生效日期");
            fieldNameMap.put("endTime", "失效日期");
            fieldNameMap.put("unitId", "销售部门");
            fieldNameMap.put("ouId", "业务实体");
            fieldNameMap.put("customerName", "客户");
            fieldNameMap.put("industry", "行业");
            fieldNameMap.put("signingCenter", "签约中心");
            fieldNameMap.put("businessId", "关联商机");
            fieldNameMap.put("legalAffairsId", "关联法务合同");
            fieldNameMap.put("salesManager", "销售经理");
            fieldNameMap.put("manager", "项目经理");
            fieldNameMap.put("remark", "备注");
            BeanConverter.copy(contractHisList.get(0), contractHisVO);
            contractHisVO.setId(contractHisList.get(0).getContractId());
            String[] ignoreFiles = new String[]{"id", "changeReason", "changewayId", "customerId", "customerCode", "childrenContracts", "invoicePlans", "receiptPlans", "contractProductCosts"
                    , "project", "budget", "receiptRate", "projectProfitDTO", "maxActualEndTime", "createBy", "createAt", "updateBy", "updateAt", "fillingDate", "status", "annex"};
            Map<String, List<Object>> compareResult = PublicUtil.compareFields(contractVO, contractHisVO, ignoreFiles);
            Set<String> keySet = compareResult.keySet();
            for (String key : keySet) {
                List<Object> list = compareResult.get(key);
                Map<String, String> detailMap = new HashMap<>();
                String fieldName = fieldNameMap.get(key);
                if (fieldName != null && !fieldName.equals("")) {
                    detailMap.put("field", fieldName);
                    switch (key) {
                        case "unitId":
                            final Unit unit = CacheDataUtils.findUnitById((Long) list.get(0));
                            final Unit unit1 = CacheDataUtils.findUnitById((Long) list.get(1));
                            if (unit != null && unit1 != null) {
                                detailMap.put("changeBefore", unit.getUnitName() == null ? "" : unit.getUnitName());
                                detailMap.put("changeAfter", unit1.getUnitName() == null ? "" : unit1.getUnitName());
                            }
                            break;
                        case "ouId":
                            final OperatingUnit operatingUnit = CacheDataUtils.findOuById((Long) list.get(0));
                            final OperatingUnit operatingUnit1 = CacheDataUtils.findOuById((Long) list.get(1));
                            if (operatingUnit != null && operatingUnit1 != null) {
                                detailMap.put("changeBefore", operatingUnit.getOperatingUnitName() == null ? "" : operatingUnit.getOperatingUnitName());
                                detailMap.put("changeAfter", operatingUnit1.getOperatingUnitName() == null ? "" : operatingUnit1.getOperatingUnitName());
                            }
                            break;
                        case "businessId":
                            Map<String, Object> param = new HashMap<>();
                            Map<String, Object> param1 = new HashMap<>();
                            param.put("id", list.get(0));
                            param1.put("id1", list.get(1));
                            final String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "business/view", param);
                            final String url1 = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "business/view", param1);
                            String res = restTemplate.getForObject(url, String.class);
                            String res1 = restTemplate.getForObject(url1, String.class);
                            DataResponse<BusinessDto> response = JSON.parseObject(res, new TypeReference<DataResponse<BusinessDto>>() {
                            });
                            DataResponse<BusinessDto> response1 = JSON.parseObject(res1, new TypeReference<DataResponse<BusinessDto>>() {
                            });
                            if (response.getData() != null && response.getData().getName() != null) {
                                detailMap.put("changeBefore", response.getData().getName());
                            } else {
                                detailMap.put("changeBefore", "");
                            }
                            if (response1.getData() != null && response1.getData().getName() != null) {
                                detailMap.put("changeAfter", response1.getData().getName());
                            } else {
                                detailMap.put("changeAfter", "");
                            }
                            break;
                        case "salesManager":
                            final UserInfo userInfo = CacheDataUtils.findUserById((Long) list.get(0));
                            final UserInfo userInfo1 = CacheDataUtils.findUserById((Long) list.get(1));
                            if (userInfo != null && userInfo1 != null) {
                                detailMap.put("changeBefore", userInfo.getName() == null ? "" : userInfo.getName());
                                detailMap.put("changeAfter", userInfo1.getName() == null ? "" : userInfo1.getName());
                            }
                            break;
                        case "manager":
                            final UserInfo mUserInfo = CacheDataUtils.findUserById((Long) list.get(0));
                            final UserInfo mUserInfo1 = CacheDataUtils.findUserById((Long) list.get(1));
                            if (mUserInfo != null && mUserInfo1 != null) {
                                detailMap.put("changeBefore", mUserInfo.getName() == null ? "" : mUserInfo.getName());
                                detailMap.put("changeAfter", mUserInfo1.getName() == null ? "" : mUserInfo1.getName());
                            }
                            break;
                        case "startTime":
                            detailMap.put("changeBefore", list.get(0) == null ? "" : DateUtils.format((Date) list.get(0), "yyyy-MM-dd"));
                            detailMap.put("changeAfter", list.get(1) == null ? "" : DateUtils.format((Date) list.get(1), "yyyy-MM-dd"));
                            break;
                        case "endTime":
                            detailMap.put("changeBefore", list.get(0) == null ? "" : DateUtils.format((Date) list.get(0), "yyyy-MM-dd"));
                            detailMap.put("changeAfter", list.get(1) == null ? "" : DateUtils.format((Date) list.get(1), "yyyy-MM-dd"));
                            break;
                        case "industry":
                            // 获取行业信息
                            detailMap.put("changeBefore", getIndustryStr(list.get(0) != null ? (Long) (list.get(0)) : null));
                            detailMap.put("changeAfter", getIndustryStr(list.get(1) != null ? (Long) (list.get(1)) : null));
                            break;
                        case "signingCenter":
                            // 获取签约中心信息
                            detailMap.put("changeBefore", getSigningCenterStr(list.get(0) != null ? (Long) (list.get(0)) : null));
                            detailMap.put("changeAfter", getSigningCenterStr(list.get(1) != null ? (Long) (list.get(1)) : null));
                            break;
                        default:
                            detailMap.put("changeBefore", list.get(0) == null ? "" : String.valueOf(list.get(0)));
                            detailMap.put("changeAfter", list.get(1) == null ? "" : String.valueOf(list.get(1)));
                    }
                    detailMapList.add(detailMap);
                }
            }
            ContractHisExample childrenContractHisExample = new ContractHisExample();
            ContractHisExample.Criteria childrenCriteria = childrenContractHisExample.createCriteria();
            childrenCriteria.andParentIdEqualTo(contract.getId()).andChangewayIdEqualTo(contractVO.getChangewayId()).andDeletedFlagEqualTo(Boolean.FALSE);
            List<ContractHis> childrenContractHisList = contractHisMapper.selectByExample(childrenContractHisExample);

            if (CollectionUtils.isNotEmpty(childrenContractHisList)) {
                int i = 0;
                for (ContractHis childrenContractHis : childrenContractHisList) {
                    Map<String, String> childrenFieldNameMap = new TreeMap<>();
                    childrenFieldNameMap.put("name", "名称");
                    childrenFieldNameMap.put("customerName", "开票客户");
                    childrenFieldNameMap.put("startTime", "子合同生效日期");
                    childrenFieldNameMap.put("endTime", "子合同失效日期");
                    childrenFieldNameMap.put("manager", "项目经理");
                    childrenFieldNameMap.put("businessTypeId", "业务模式");
                    childrenFieldNameMap.put("profitDepartmentId", "虚拟部门");
//                    childrenFieldNameMap.put("")
                    ContractVO childrenContractHisVO = new ContractVO();
                    ContractVO childrenContractVO = new ContractVO();
                    BeanConverter.copy(childrenContractHis, childrenContractHisVO);
                    childrenContractHisVO.setId(null);

                    Contract childrenContract = contractMapper.selectByPrimaryKey(childrenContractHis.getContractId());
                    BeanConverter.copy(childrenContract, childrenContractVO);

                    Map<String, List<Object>> childrenCompareResult = PublicUtil.compareFields(childrenContractVO, childrenContractHisVO, ignoreFiles);
                    Set<String> childrenKeySet = childrenCompareResult.keySet();
                    i = i + 1;
                    for (String childrenKey : childrenKeySet) {
                        List<Object> childrenList = childrenCompareResult.get(childrenKey);
                        Map<String, String> childrenDetailMap = new HashMap<>();
                        String childrenFieldName = childrenFieldNameMap.get(childrenKey);
                        if (childrenFieldName != null && !childrenFieldName.equals("")) {
                            childrenDetailMap.put("field", "子合同" + i + childrenFieldName);
                            switch (childrenKey) {
                                case "startTime":
                                    childrenDetailMap.put("changeBefore", childrenList.get(0) == null ? "" : DateUtils.format((Date) childrenList.get(0), "yyyy-MM-dd"));
                                    childrenDetailMap.put("changeAfter", childrenList.get(1) == null ? "" : DateUtils.format((Date) childrenList.get(1), "yyyy-MM-dd"));
                                    break;
                                case "endTime":
                                    childrenDetailMap.put("changeBefore", childrenList.get(0) == null ? "" : DateUtils.format((Date) childrenList.get(0), "yyyy-MM-dd"));
                                    childrenDetailMap.put("changeAfter", childrenList.get(1) == null ? "" : DateUtils.format((Date) childrenList.get(1), "yyyy-MM-dd"));
                                    break;
                                case "manager":
                                    final UserInfo mUserInfo = CacheDataUtils.findUserById((Long) childrenList.get(0));
                                    final UserInfo mUserInfo1 = CacheDataUtils.findUserById((Long) childrenList.get(1));
                                    if (mUserInfo != null && mUserInfo1 != null) {
                                        childrenDetailMap.put("changeBefore", mUserInfo.getName() == null ? "" : mUserInfo.getName());
                                        childrenDetailMap.put("changeAfter", mUserInfo1.getName() == null ? "" : mUserInfo1.getName());
                                    }
                                    break;
                                case "businessTypeId":
                                    final ProjectType projectType = projectTypeMapper.selectByPrimaryKey((Long) childrenList.get(0));
                                    final ProjectType projectType1 = projectTypeMapper.selectByPrimaryKey((Long) childrenList.get(1));
                                    if (projectType != null && projectType1 != null) {
                                        childrenDetailMap.put("changeBefore", projectType.getName());
                                        childrenDetailMap.put("changeAfter", projectType1.getName());
                                    }
                                    break;
                                case "profitDepartmentId":
                                    final Unit unit = CacheDataUtils.findUnitById((Long) childrenList.get(0));
                                    final Unit unit1 = CacheDataUtils.findUnitById((Long) childrenList.get(1));
                                    if (unit != null && unit1 != null) {
                                        childrenDetailMap.put("changeBefore", unit.getUnitName());
                                        childrenDetailMap.put("changeAfter", unit1.getUnitName());
                                    }
                                    break;
                                default:
                                    childrenDetailMap.put("changeBefore", childrenList.get(0) == null ? "" : String.valueOf(childrenList.get(0)));
                                    childrenDetailMap.put("changeAfter", childrenList.get(1) == null ? "" : String.valueOf(childrenList.get(1)));
                            }
                            detailMapList.add(childrenDetailMap);
                        }
                    }

                    totalExcludingTaxAmount = totalExcludingTaxAmount.add(childrenContractHis.getExcludingTaxAmount() == null ? BigDecimal.ZERO : childrenContractHis.getExcludingTaxAmount());

                }
            }


        }
        String ss = totalExcludingTaxAmount == null ? "0.000" : String.valueOf(totalExcludingTaxAmount);
        headMap.put("excludingTaxAmount", ss.substring(0, ss.length() - 1));
        String annex = contractHisList.get(0).getAnnex();
        List<Long> idList = new ArrayList<>();
        List<Long> idList1 = new ArrayList<>();
        if (StringUtils.isNotEmpty(annex) && annex.length() > 0) {
            for (String oAnnex : annex.split(",")) {
                try {
                    idList.add(Long.parseLong(oAnnex));
                } catch (NumberFormatException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(idList)) {
            for (Long aLong : idList) {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(String.valueOf(aLong));
                fileList.add(aduitAtta);
            }
        }
        String attachId = contractHisList.get(0).getAttachId();
        if (StringUtils.isNotEmpty(attachId) && attachId.length() > 0) {
            for (String oAttach : attachId.split(",")) {
                try {
                    idList1.add(Long.parseLong(oAttach));
                } catch (NumberFormatException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(idList1)) {
            for (Long aLong1 : idList1) {
                AduitAtta aduitAtta1 = new AduitAtta();
                aduitAtta1.setFdId(String.valueOf(aLong1));
                fileList.add(aduitAtta1);
            }
        }
        headListMap.add(headMap);
        responseMap.setStatus("success");
        responseMap.setMsg("success");
        responseMap.setList1(headListMap);
        responseMap.setList2(detailMapList);
        responseMap.setFileList(fileList);
        return responseMap;
    }

    /**
     * 移动审批销售合同服务内容变更
     *
     * @param id
     * @return
     */
    @Override
    public ResponseMap getContractProductChanageApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<AduitAtta> fileList = new ArrayList<>();
        List<Map<String, String>> childrenMapList = new ArrayList<>(); //存放变更原因
        List<Map<String, String>> childrenMapList1 = new ArrayList<>(); //存放服务类型
        ContractVO contractVO = new ContractVO();
        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(id);
        // 主合同
        final Contract contract = this.findById(contractChangeway.getContractId());
        if (ObjectUtils.isEmpty(contract)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("头信息不能为空");
        }
        BigDecimal changeBeforeAmount = BigDecimal.ZERO;
        changeBeforeAmount = changeBeforeAmount.add(contract.getAmount() == null ? BigDecimal.ZERO : contract.getAmount());
        BeanConverter.copy(contract, contractVO);
        contractHelper.formatContractVO(contractVO);


        //变更后的合同数据
        ContractHisExample contractHisExample = new ContractHisExample();
        ContractHisExample.Criteria criteria = contractHisExample.createCriteria();
        criteria.andContractIdEqualTo(contract.getId()).andChangewayIdEqualTo(contractVO.getChangewayId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ContractHis> contractHisList = contractHisMapper.selectByExample(contractHisExample);
        if (CollectionUtils.isNotEmpty(contractHisList)) {
            final ContractHis contractHis = contractHisList.get(0);
            Map<String, String> childrenMap = new HashMap<>();
            String startTime = null != contractVO.getStartTime() ? DateUtils.format(contractVO.getStartTime(), "yyyy-MM-dd") : "";
            String endTime = null != contractVO.getEndTime() ? DateUtils.format(contractVO.getEndTime(), "yyyy-MM-dd") : "";
            childrenMap.put("name", contractVO.getName()); //合同名称
//            childrenMap.put("changeType", "合同服务内容变更");
            childrenMap.put("changeReasonType", contractChangeway.getChangeReasonType());
            childrenMap.put("changeReason", contractChangeway.getChangeReason());
            childrenMap.put("unitName", contractVO.getUnitName()); //销售部门
            childrenMap.put("customerName", contractVO.getCustomerName());
            childrenMap.put("salesManagerName", contractVO.getSalesManagerName()); //销售经理
            childrenMap.put("managerName", contractVO.getManagerName()); //项目经理
            childrenMap.put("time", startTime + "-" + endTime);
            childrenMap.put("currency", contractVO.getCurrency()); //币种
            childrenMap.put("amount", String.valueOf(contractHis.getAmount()).length() > 1 ? String.valueOf(contractHis.getAmount()).substring(0, String.valueOf(contractHis.getAmount()).length() - 1) : String.valueOf(contractHis.getAmount()));//合同金额含税
            childrenMap.put("ouName", contractVO.getOuName()); //业务实体


            BigDecimal changeAfterAmount = BigDecimal.ZERO;
            BigDecimal dValueAmount = BigDecimal.ZERO;
            changeAfterAmount = changeAfterAmount.add(contractHis.getAmount());
            dValueAmount = changeAfterAmount.subtract(changeBeforeAmount);
            String dv = String.valueOf(dValueAmount).length() > 1 ? String.valueOf(dValueAmount).substring(0, String.valueOf(dValueAmount).length() - 1) : String.valueOf(dValueAmount);
            if (BigDecimalUtils.isGreater(dValueAmount, BigDecimal.ZERO)) {
                dv = "+" + dv;
            }
            headMap.put("changeBeforeAmount", String.valueOf(changeBeforeAmount).length() > 1 ? String.valueOf(changeBeforeAmount).substring(0, String.valueOf(changeBeforeAmount).length() - 1) : String.valueOf(changeBeforeAmount)); //变更前合同金额含税
            headMap.put("changeAfterAmount", String.valueOf(changeAfterAmount).length() > 1 ? String.valueOf(changeAfterAmount).substring(0, String.valueOf(changeAfterAmount).length() - 1) : String.valueOf(changeAfterAmount)); //变更后合同金额含税
            headMap.put("dValueAmount", dv); //调整差异额
            headMap.put("currency", contractHis.getCurrency()); //币种
            headMap.put("conversionType", contractHis.getConversionType()); //汇率类型
            headMap.put("conversionRate", Optional.ofNullable(contractHis.getConversionRate()).orElse(BigDecimal.ONE).stripTrailingZeros().toPlainString()); //汇率


            //该合同变更后所有的服务类型信息
            ContractHisExample contractHisExample1 = new ContractHisExample();
            ContractHisExample.Criteria contractHisExample1Criteria = contractHisExample1.createCriteria();
            contractHisExample1Criteria.andDeletedFlagEqualTo(Boolean.FALSE).andChangewayIdEqualTo(contractVO.getChangewayId())
                    .andParentIdEqualTo(contract.getId());
            List<ContractHis> childrenContractHisList = contractHisMapper.selectByExample(contractHisExample1);
            BigDecimal totalExcludingTaxAmount = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(childrenContractHisList)) {
                for (ContractHis childrenContractHis : childrenContractHisList) {
                    ContractProductHisExample contractProductHisExample = new ContractProductHisExample();
                    ContractProductHisExample.Criteria contractProductHisExampleCriteria = contractProductHisExample.createCriteria();
                    contractProductHisExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE).andContractIdEqualTo(childrenContractHis.getContractId() == null ? childrenContractHis.getId() : childrenContractHis.getContractId())
                            .andChangewayIdEqualTo(contractVO.getChangewayId());
                    List<ContractProductHis> contractProductHisList = contractProductHisMapper.selectByExample(contractProductHisExample);
                    if (CollectionUtils.isNotEmpty(contractProductHisList)) {
                        for (ContractProductHis contractProductHis : contractProductHisList) {
//                            Long contractId = contractProductHis.getContractId();
//                            ContractHisExample contractHisExample2 = new ContractHisExample();
//                            contractHisExample2.createCriteria().andChangewayIdEqualTo(id).andContractIdEqualTo(contractId);
//                            List<ContractHis> contractHisList2 = contractHisMapper.selectByExample(contractHisExample2);
                            String operationType = "";
                            Integer type = childrenContractHis.getOperationType() == null ? 0 : childrenContractHis.getOperationType();
                            switch (type) {
                                case 1:
                                    operationType = "新增";
                                    break;
                                case 2:
                                    operationType = "删除";
                                    break;
                                default:
                                    operationType = "变更";
                            }
                            Map<String, String> childrenMap1 = new HashMap<>();
                            String productTypeAndName = null != contractProductHis.getProductTypeName() ? contractProductHis.getProductTypeName() : "" + "/"
                                    + null != contractProductHis.getProductName() ? contractProductHis.getProductName() : "";
                            String amount = String.valueOf(contractProductHis.getAmount() == null ? "" : contractProductHis.getAmount());
                            String excludingTaxAmount = String.valueOf(contractProductHis.getExcludingTaxAmount() == null ? "" : contractProductHis.getExcludingTaxAmount());
                            childrenMap1.put("operationType", operationType);
                            childrenMap1.put("productTypeAndName", productTypeAndName); //服务类型产品
                            childrenMap1.put("taxRate", contractProductHis.getTaxRate()); //税率
                            childrenMap1.put("amount", amount.length() > 1 ? amount.substring(0, amount.length() - 1) : amount); //金额含税
                            childrenMap1.put("contractName", childrenContractHis.getName()); //关联子合同
                            childrenMap1.put("productTypeName", contractProductHis.getProductTypeName()); //服务类型
                            childrenMap1.put("productName", contractProductHis.getProductName()); // 产品名称
                            childrenMap1.put("excludingTaxAmount", excludingTaxAmount.length() > 1 ? excludingTaxAmount.substring(0, excludingTaxAmount.length() - 1) : excludingTaxAmount); // 金额不含税
                            childrenMapList1.add(childrenMap1);
                        }
                    }
                    totalExcludingTaxAmount = totalExcludingTaxAmount.add(childrenContractHis.getExcludingTaxAmount() == null ? BigDecimal.ZERO : childrenContractHis.getExcludingTaxAmount());
                }
            }
            String valueOfxcludingTaxAmount = String.valueOf(totalExcludingTaxAmount == null ? "" : totalExcludingTaxAmount);
            childrenMap.put("excludingTaxAmount", valueOfxcludingTaxAmount.length() > 1 ? valueOfxcludingTaxAmount.substring(0, valueOfxcludingTaxAmount.length() - 1) : valueOfxcludingTaxAmount); //合同金额不含税
            childrenMapList.add(childrenMap);
        }

        String originalContractAnnex = contractVO.getOriginalContractAnnex();
        List<Long> idList = new ArrayList<>();
        if (StringUtils.isNotEmpty(originalContractAnnex) && originalContractAnnex.length() > 0) {
            for (String oAnnex : originalContractAnnex.split(",")) {
                try {
                    idList.add(Long.parseLong(oAnnex));
                } catch (NumberFormatException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(idList)) {
            for (Long aLong : idList) {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(String.valueOf(aLong));
                fileList.add(aduitAtta);
            }
        }
        responseMap.setStatus("success");
        responseMap.setMsg("success");
        responseMap.setHeadMap(headMap);
        responseMap.setFileList(fileList);
        responseMap.setList1(childrenMapList);
        responseMap.setList2(childrenMapList1);
        return responseMap;
    }

    /**
     * 移动审批销售合同开票回款变更
     *
     * @param id
     * @return
     */
    @Override
    public ResponseMap getContractReceiptPlanChanageApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<AduitAtta> fileList = new ArrayList<>();
        List<Map<String, String>> childrenMapList = new ArrayList<>(); //存放变更原因
        List<Map<String, String>> childrenMapList1 = new ArrayList<>(); //存放开票计划
        List<Map<String, String>> childrenMapList2 = new ArrayList<>(); //存放回款计划
        ContractVO contractVO = new ContractVO();
        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(id);
        // 主合同
        final Contract contract = this.findById(contractChangeway.getContractId());
        if (ObjectUtils.isEmpty(contract)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("头信息不能为空");
        }
        BeanConverter.copy(contract, contractVO);
        contractHelper.formatContractVO(contractVO);

        //变更后的合同数据
        ContractHisExample contractHisExample = new ContractHisExample();
        ContractHisExample.Criteria criteria = contractHisExample.createCriteria();
        criteria.andContractIdEqualTo(contract.getId()).andChangewayIdEqualTo(contractVO.getChangewayId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ContractHis> contractHisList = contractHisMapper.selectByExample(contractHisExample);
        if (CollectionUtils.isNotEmpty(contractHisList)) {
            final ContractHis contractHis = contractHisList.get(0);
            Map<String, String> childrenMap = new HashMap<>();
            String startTime = null != contractVO.getStartTime() ? DateUtils.format(contractVO.getStartTime(), "yyyy-MM-dd") : "";
            String endTime = null != contractVO.getEndTime() ? DateUtils.format(contractVO.getEndTime(), "yyyy-MM-dd") : "";
            childrenMap.put("name", contractVO.getName()); //合同名称
//            childrenMap.put("changeType", "开票回款计划变更");
            childrenMap.put("changeReasonType", contractChangeway.getChangeReasonType());
            childrenMap.put("changeReason", contractChangeway.getChangeReason());
            childrenMap.put("unitName", contractVO.getUnitName()); //销售部门
            childrenMap.put("salesManagerName", contractVO.getSalesManagerName()); //销售经理
            childrenMap.put("customerName", contractVO.getCustomerName());
            childrenMap.put("managerName", contractVO.getManagerName()); //项目经理
            childrenMap.put("time", startTime + "-" + endTime);
            childrenMap.put("currency", contractVO.getCurrency()); //币种
            childrenMap.put("amount", String.valueOf(contractHis.getAmount()).length() > 1 ?
                    String.valueOf(contractHis.getAmount()).substring(0, String.valueOf(contractHis.getAmount()).length() - 1) : String.valueOf(contractHis.getAmount()));//合同金额含税
            childrenMap.put("ouName", contractVO.getOuName()); //业务实体

            //该合同所有的开票回款计划数据信息
            ContractHisExample contractHisExample1 = new ContractHisExample();
            ContractHisExample.Criteria contractHisExample1Criteria = contractHisExample1.createCriteria();
            contractHisExample1Criteria.andDeletedFlagEqualTo(Boolean.FALSE).andChangewayIdEqualTo(contractVO.getChangewayId())
                    .andParentIdEqualTo(contract.getId());
            List<ContractHis> childrenContractHisList = contractHisMapper.selectByExample(contractHisExample1);
            BigDecimal totalExcludingTaxAmount = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(childrenContractHisList)) {
                for (ContractHis childrenContractHis : childrenContractHisList) {
                    // 开票计划记录
                    InvoicePlanHisExample invoicePlanHisExample = new InvoicePlanHisExample();
                    invoicePlanHisExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andChangewayIdEqualTo(contractChangeway.getId())
                            .andContractIdEqualTo(childrenContractHis.getContractId());
                    List<InvoicePlanHis> invoicePlanHisList = invoicePlanHisMapper.selectByExample(invoicePlanHisExample);

                    // 开票计划详情记录
                    InvoicePlanDetailHisExample invoicePlanDetailHisExample = new InvoicePlanDetailHisExample();
                    InvoicePlanDetailHisExample.Criteria invoicePlanDetailHisExampleCriteria = invoicePlanDetailHisExample.createCriteria();
                    invoicePlanDetailHisExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE).andChangewayIdEqualTo(contractChangeway.getId())
                            .andContractIdEqualTo(childrenContractHis.getContractId());
                    List<InvoicePlanDetailHis> invoicePlanDetailHisList = invoicePlanDetailHisMapper.selectByExample(invoicePlanDetailHisExample);
                    if (CollectionUtils.isNotEmpty(invoicePlanHisList) && CollectionUtils.isNotEmpty(invoicePlanDetailHisList)) {
                        int i = 0;
                        for (InvoicePlanDetailHis invoicePlanDetailHis : invoicePlanDetailHisList) {
                            i++;
                            Map<String, String> invoicePlanDetailHisMap = new HashMap<>();
                            invoicePlanDetailHisMap.put("num", String.valueOf(i));
                            invoicePlanDetailHisMap.put("invoicePlanDate", null != invoicePlanDetailHis.getDate() ? DateUtils.format(invoicePlanDetailHis.getDate(), "yyyy-MM-dd") : "");
                            invoicePlanDetailHisMap.put("invoicePlanAmount", String.valueOf(invoicePlanDetailHis.getAmount()).length() > 4 ?
                                    String.valueOf(invoicePlanDetailHis.getAmount()).substring(0, String.valueOf(invoicePlanDetailHis.getAmount()).length() - 4) : String.valueOf(invoicePlanDetailHis.getAmount()));
                            invoicePlanDetailHisMap.put("contractName", Optional.ofNullable(childrenContractHis).map(c -> c.getName()).orElse(" "));
                            invoicePlanDetailHisMap.put("type", InvoicePlanInvoiceType.getValue(invoicePlanHisList.get(0).getType()));
                            invoicePlanDetailHisMap.put("taxRate", invoicePlanHisList.get(0).getTaxRate() == null ? " " : invoicePlanHisList.get(0).getTaxRate());
                            String excludingTaxAmount = invoicePlanDetailHis.getExcludingTaxAmount() == null ? "0.00" : invoicePlanDetailHis.getExcludingTaxAmount().toString();
                            invoicePlanDetailHisMap.put("excludingTaxAmount", excludingTaxAmount.length() > 4 ? excludingTaxAmount.substring(0, excludingTaxAmount.length() - 4) : excludingTaxAmount);
                            invoicePlanDetailHisMap.put("requirement", invoicePlanDetailHis.getRequirement() == null ? " " : invoicePlanDetailHis.getRequirement());
                            childrenMapList1.add(invoicePlanDetailHisMap);
                        }
                    }
                    ReceiptPlanDetailHisExample receiptPlanDetailHisExample = new ReceiptPlanDetailHisExample();
                    ReceiptPlanDetailHisExample.Criteria receiptPlanDetailHisExampleCriteria = receiptPlanDetailHisExample.createCriteria();
                    receiptPlanDetailHisExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE).andChangewayIdEqualTo(contractChangeway.getId())
                            .andContractIdEqualTo(childrenContractHis.getContractId());
                    List<ReceiptPlanDetailHis> receiptPlanDetailHisList = receiptPlanDetailHisMapper.selectByExample(receiptPlanDetailHisExample);
                    if (CollectionUtils.isNotEmpty(receiptPlanDetailHisList)) {
                        int n = 0;
                        for (ReceiptPlanDetailHis receiptPlanDetailHis : receiptPlanDetailHisList) {
                            n++;
                            Map<String, String> receiptPlanDetailHisMap = new HashMap<>();
                            String receiptAmount = String.valueOf(receiptPlanDetailHis.getAmount() == null ? "0.00" : receiptPlanDetailHis.getAmount());
                            receiptPlanDetailHisMap.put("num", String.valueOf(n));
                            receiptPlanDetailHisMap.put("receiptPlanDate", null != receiptPlanDetailHis.getDate() ? DateUtils.format(receiptPlanDetailHis.getDate(), "yyyy-MM-dd") : "");
                            receiptPlanDetailHisMap.put("receiptPlanAmount", receiptAmount.length() > 4 ? receiptAmount.substring(0, receiptAmount.length() - 4) : receiptAmount);
                            receiptPlanDetailHisMap.put("contractName", Optional.ofNullable(childrenContractHis).map(c -> c.getName()).orElse(" "));
                            receiptPlanDetailHisMap.put("requirement", Optional.ofNullable(receiptPlanDetailHis).map(r -> r.getRequirement()).orElse(" "));
                            // 查询开票回款关系记录表
                            ReceiptInvoiceRelationHisExample receiptInvoiceRelationHisExample = new ReceiptInvoiceRelationHisExample();
                            receiptInvoiceRelationHisExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andContractIdEqualTo(childrenContractHis.getContractId())
                                    .andChangewayIdEqualTo(contractChangeway.getId()).andReceiptPlanDetailIdEqualTo(receiptPlanDetailHis.getId());
                            List<ReceiptInvoiceRelationHis> receiptInvoiceRelationHisList = receiptInvoiceRelationHisMapper.selectByExample(receiptInvoiceRelationHisExample);
                            Set<Long> invoicePlanDetailsHiss = new HashSet<>();
                            if (CollectionUtils.isNotEmpty(receiptInvoiceRelationHisList)) {
                                for (ReceiptInvoiceRelationHis receiptInvoiceRelationHis : receiptInvoiceRelationHisList) {
                                    invoicePlanDetailsHiss.add(receiptInvoiceRelationHis.getInvoicePlanDetailId());
                                }
                            }
                            if (CollectionUtils.isNotEmpty(invoicePlanDetailsHiss)) {
                                //找到关联的开票信息
                                InvoicePlanDetailHisExample invoicePlanDetailHisExample1 = new InvoicePlanDetailHisExample();
                                invoicePlanDetailHisExample1.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andChangewayIdEqualTo(contractChangeway.getId())
                                        .andContractIdEqualTo(childrenContractHis.getContractId()).andIdIn(new ArrayList<>(invoicePlanDetailsHiss));
                                List<InvoicePlanDetailHis> invoicePlanDetailHisList1 = invoicePlanDetailHisMapper.selectByExample(invoicePlanDetailHisExample1);
                                if (CollectionUtils.isNotEmpty(invoicePlanDetailHisList1)) {
                                    StringBuffer invoicePlanDetailCodes = new StringBuffer();
                                    invoicePlanDetailHisList1.forEach(InvoicePlanDetailHis -> {
                                        invoicePlanDetailCodes.append(InvoicePlanDetailHis.getCode()).append(",");
                                    });
                                    receiptPlanDetailHisMap.put("invoicePlanDetailCodes", invoicePlanDetailCodes.toString().substring(0, invoicePlanDetailCodes.toString().lastIndexOf(",")));

                                }
                            } else {
                                receiptPlanDetailHisMap.put("invoicePlanDetailCodes", " ");
                            }

                            childrenMapList2.add(receiptPlanDetailHisMap);
                        }
                    }

                    totalExcludingTaxAmount = totalExcludingTaxAmount.add(childrenContractHis.getExcludingTaxAmount() == null ? BigDecimal.ZERO : childrenContractHis.getExcludingTaxAmount());

                }
            }
            String valueOfExcludingTaxAmount = String.valueOf(totalExcludingTaxAmount == null ? "" : totalExcludingTaxAmount);
            childrenMap.put("excludingTaxAmount", valueOfExcludingTaxAmount.length() > 1 ? valueOfExcludingTaxAmount.substring(0, valueOfExcludingTaxAmount.length() - 1) : valueOfExcludingTaxAmount); //合同金额不含税
            childrenMapList.add(childrenMap);
        }

        String originalContractAnnex = contractVO.getOriginalContractAnnex();
        List<Long> idList = new ArrayList<>();
        if (StringUtils.isNotEmpty(originalContractAnnex) && originalContractAnnex.length() > 0) {
            for (String oAnnex : originalContractAnnex.split(",")) {
                try {
                    idList.add(Long.parseLong(oAnnex));
                } catch (NumberFormatException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(idList)) {
            for (Long aLong : idList) {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(String.valueOf(aLong));
                fileList.add(aduitAtta);
            }
        }
        responseMap.setStatus("success");
        responseMap.setMsg("success");
        responseMap.setFileList(fileList);
        responseMap.setList1(childrenMapList);
        responseMap.setList2(childrenMapList1);
        responseMap.setList3(childrenMapList2);
        return responseMap;
    }

    /**
     * ex_pengzhong2 取子合同上对应的已开票和已回款金额汇总
     *
     * @param id
     * @return
     */
    @Override
    public Map<String, String> getActualInvAndRecAmount(Long id) {
        if (id != null) {
            Map<String, String> actualInvAndRecAmounts = new HashMap<>();
            InvoicePlanDetailExample invoicePlanDetailExample = new InvoicePlanDetailExample();
            InvoicePlanDetailExample.Criteria invoicePlanDetailExampleCriteria = invoicePlanDetailExample.createCriteria();
            invoicePlanDetailExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE).andContractIdEqualTo(id);
            List<InvoicePlanDetail> invoicePlanDetailList = invoicePlanDetailMapper.selectByExample(invoicePlanDetailExample);

            ReceiptPlanDetailExample receiptPlanDetailExample = new ReceiptPlanDetailExample();
            ReceiptPlanDetailExample.Criteria receiptPlanDetailExampleCriteria = receiptPlanDetailExample.createCriteria();
            receiptPlanDetailExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE).andContractIdEqualTo(id);
            List<ReceiptPlanDetail> receiptPlanDetailList = receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample);

            if (CollectionUtils.isNotEmpty(receiptPlanDetailList)) {
                BigDecimal receiptActualAmount = BigDecimal.ZERO;
                for (ReceiptPlanDetail receiptPlanDetail : receiptPlanDetailList) {
                    receiptActualAmount = receiptActualAmount.add(Optional.ofNullable(receiptPlanDetail).
                            map(rec -> rec.getActualAmount()).orElse(BigDecimal.ZERO));
                }
                actualInvAndRecAmounts.put("receiptActualAmount", receiptActualAmount.toString());
            }

            if (CollectionUtils.isNotEmpty(invoicePlanDetailList)) {
                BigDecimal planActualAmount = BigDecimal.ZERO;
                for (InvoicePlanDetail invoicePlanDetail : invoicePlanDetailList) {
                    planActualAmount = planActualAmount.add(Optional.ofNullable(invoicePlanDetail).
                            map(inv -> inv.getTaxIncludedPrice()).orElse(BigDecimal.ZERO));
                }
                actualInvAndRecAmounts.put("planActualAmount", planActualAmount.toString());
            }
            return actualInvAndRecAmounts;
        }
        return null;
    }

    @Override
    public Boolean isCanBeDel(Long contractId) {
        return contractHisService.isCanbeDel(contractId);
    }

    @Override
    public List<EamPurchaseInfoDto> orNotChange(Long contractId) {
        List<EamPurchaseInfoDto> changedDto = new ArrayList<>();
        List<Long> purchaseInfoIdList = new ArrayList<>();
        ContractExample contractExample = new ContractExample();
        contractExample.createCriteria().andParentIdEqualTo(contractId);
        List<Contract> childContract = contractMapper.selectByExample(contractExample);
        if (ListUtils.isNotEmpty(childContract)) {
            for (Contract contract : childContract) {
                List<EamPurchaseInfoDto> purchaseInfoDtoList = contractExtMapper.findPurchaseBycontractId(contract.getId());
                if (ListUtils.isNotEmpty(purchaseInfoDtoList)) {
                    for (EamPurchaseInfoDto eamPurchaseInfoDto : purchaseInfoDtoList) {
                        // added by dengfei added at 20201204 reason:非人力外包的关联采购合同金额变更时，attribute2还是原来的contractCode，并且不会产生新的数据行。
                        if (StringUtils.isNotEmpty(eamPurchaseInfoDto.getAttribute2()) && eamPurchaseInfoDto.getAttribute2().equals(eamPurchaseInfoDto.getContractCode())) {
                            EamPurchaseInfo newEamPurchaseInfo = new EamPurchaseInfo();
                            BeanConverter.copy(eamPurchaseInfoDto, newEamPurchaseInfo);
                            eamPurchaseInfoDto.setNewEamPurchaseInfo(newEamPurchaseInfo);
                            ContractExample contractExample1 = new ContractExample();
                            contractExample1.createCriteria().andEampurchaseIdEqualTo(eamPurchaseInfoDto.getId().toString())
                                    .andDeletedFlagEqualTo(Boolean.FALSE).andParentIdIsNotNull();
                            final List<Contract> eamContractList = contractMapper.selectByExample(contractExample1);
                            BigDecimal allEamMoney = BigDecimal.ZERO;
                            for (Contract eamContract : eamContractList) {
                                allEamMoney = allEamMoney.add(eamContract.getAmount());
                            }
                            if (BigDecimalUtils.isEquals(allEamMoney, new BigDecimal(eamPurchaseInfoDto.getAttribute1()).multiply(BigDecimal.valueOf(10000L)))) {
                                return null;
                            } else {
                                eamPurchaseInfoDto.setAttribute1(allEamMoney.divide(BigDecimal.valueOf(10000L)).toPlainString());
                                changedDto.add(eamPurchaseInfoDto);
                            }
                        } else {

                            EamPurchaseInfo newEamPurchaseInfo = this.statisticalAmount(eamPurchaseInfoDto);
                            if (new BigDecimal(newEamPurchaseInfo.getAttribute1()).compareTo(new BigDecimal(eamPurchaseInfoDto.getAttribute1())) != 0 && !purchaseInfoIdList.contains(eamPurchaseInfoDto.getId())) {
                                purchaseInfoIdList.add(eamPurchaseInfoDto.getId());//去重
                                eamPurchaseInfoDto.setNewEamPurchaseInfo(newEamPurchaseInfo);
                                changedDto.add(eamPurchaseInfoDto);
                            }

                        }

                    }
                }
            }
        }

        return changedDto;
    }

    public EamPurchaseInfo statisticalAmount(EamPurchaseInfo eamPurchaseInfo) {
        EamPurchaseInfo newEamPurchaseInfo = eamPurchaseInfo;
        EamPurchaseInfoExample example = new EamPurchaseInfoExample();
        EamPurchaseInfoExample.Criteria criteria = example.createCriteria();
        criteria.andAttribute2EqualTo(eamPurchaseInfo.getContractCode());
        List<EamPurchaseInfo> eamPurchaseInfoList = eamPurchaseInfoService.selectByExample(example);
        if (ListUtils.isNotEmpty(eamPurchaseInfoList)) {
            newEamPurchaseInfo = statisticalAmount(eamPurchaseInfoList.get(0));
        }
        return newEamPurchaseInfo;
    }

    @Override
    public Boolean checkBaseInfoChange(Long id) {
        ProjectContractRsExample example = new ProjectContractRsExample();
        final ProjectContractRsExample.Criteria criteria = example.createCriteria();
        criteria.andContractIdEqualTo(id);
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectContractRs> projectContractRsList = projectContractRsMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(projectContractRsList)) {
            Long projectId = projectContractRsList.get(0).getProjectId();
            Boolean completed = projectService.checkIncomeCompleted(projectId);
            return !completed;
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean checkProject(Long projectId, Long contractId) {
        Project project = projectService.selectByPrimaryKey(projectId);
        // added by dengfei added at 20210104 bug:项目收入已结转完，不允许变更合同金额
        Boolean completed = projectService.checkIncomeCompletedSec(projectId);
        if (project != null) {
            Contract contract = contractMapper.selectByPrimaryKey(contractId);
            if (contract == null) {
                throw new BizException(ErrorCode.ERROR, "该项目关联的合同不存在");
            }
            if (project.getStatus() == ProjectStatus.TERMINATIONING.getCode() || project.getStatus() == ProjectStatus.TERMINATION.getCode()) {
                String msg = project.getStatus() == ProjectStatus.TERMINATIONING.getCode() ? "终止审批中" : "已终止";
                throw new BizException(ErrorCode.ERROR, "子合同" + contract.getCode() + "对应的项目" + msg + "，不允许变更合同金额");
            }

            if (project.getStatus() == ProjectStatus.CLOSE.getCode()) {
                throw new BizException(ErrorCode.ERROR, "子合同" + contract.getCode() + "对应的项目已结项，不允许变更合同金额");
            }
            if (completed) {
                throw new BizException(ErrorCode.ERROR, "子合同" + contract.getCode() + "对应的项目收入已结转完，不允许变更合同金额");
            }
        }
        return true;
    }

    @Override
    public List<MilepostTemplateStageDto> getMilepostInfoRel(Long milepostTemplateId, Long projectId) {
        List<MilepostTemplateStageDto> parentTemplateList = new ArrayList<>();
        if (projectId != null) {
            /* 查询父里程碑 */
            ProjectMilepostExample parentExample = new ProjectMilepostExample();
            parentExample.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE).andParentIdIsNull();
            List<ProjectMilepost> parentList = projectMilepostService.selectByExample(parentExample);
            if (CollectionUtils.isNotEmpty(parentList)) {
                for (ProjectMilepost parent : parentList) {
                    MilepostTemplateStageDto parentTemplate = new MilepostTemplateStageDto();
                    parentTemplate.setId(parent.getId());
                    parentTemplate.setMilepostStage(parent.getName());
                    parentTemplate.setIncomeFlag(parent.getIncomeFlag());
                    parentTemplate.setStatus(parent.getStatus());
                    parentTemplate.setOrderNum(parent.getOrderNum());
                    /* 查询并行交付线 */
                    if (Boolean.TRUE.equals(parent.getParallelDeliveryLineFlag())) {
                        ProjectMilepostGroupExample groupExample = new ProjectMilepostGroupExample();
                        groupExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(projectId).andProjectMilepostIdEqualTo(parent.getId());
                        List<ProjectMilepostGroup> groupList = projectMilepostGroupMapper.selectByExample(groupExample);
                        if (CollectionUtils.isNotEmpty(groupList)) {
                            List<MilepostTemplateStageGroupDto> groupTemplateList = new ArrayList<>();
                            for (ProjectMilepostGroup group : groupList) {
                                MilepostTemplateStageGroupDto groupTemplate = new MilepostTemplateStageGroupDto();
                                groupTemplate.setId(group.getId());
                                groupTemplate.setMilepostTemplateStageId(parent.getId());
                                groupTemplate.setName(group.getName());
                                groupTemplate.setOrderNo(group.getOrderNo());
                                /* 查询子里程碑 */
                                ProjectMilepostExample childExample = new ProjectMilepostExample();
                                childExample.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE).andParentIdEqualTo(parent.getId()).andGroupIdEqualTo(group.getId());
                                List<ProjectMilepost> childList = projectMilepostService.selectByExample(childExample);
                                if (CollectionUtils.isNotEmpty(childList)) {
                                    List<MilepostTemplateStageDto> childTemplateList = new ArrayList<>();
                                    for (ProjectMilepost child : childList) {
                                        MilepostTemplateStageDto childTemplate = new MilepostTemplateStageDto();
                                        childTemplate.setId(child.getId());
                                        childTemplate.setMilepostStage(child.getName());
                                        childTemplate.setIncomeFlag(child.getIncomeFlag());
                                        childTemplate.setStatus(child.getStatus());
                                        childTemplate.setOrderNum(child.getOrderNum());
                                        childTemplate.setGroupId(child.getGroupId());
                                        childTemplate.setGroupName(group.getName());
                                        childTemplate.setParentId(child.getParentId());
                                        childTemplateList.add(childTemplate);
                                    }
                                    // 设置子里程碑
                                    groupTemplate.setStageList(childTemplateList);
                                }
                                groupTemplateList.add(groupTemplate);
                            }
                            // 设置并行交付线
                            parentTemplate.setGroupList(groupTemplateList);
                        }
                    }
                    parentTemplateList.add(parentTemplate);
                }
            }
        } else {
            parentTemplateList = milepostTemplateStageService.findMilepostInfoRel(milepostTemplateId, projectId);
        }
        return parentTemplateList;
    }

    @Override
    public Integer updateContract(Long id) {
        return contractExtMapper.updateGleContract(id);
    }

    @Override
    public Integer updatePContract(Long id) {
        return contractExtMapper.updateGlePContract(id);
    }

    @Override
    public Integer callWriteGleInfo2Contract(GlegalContractDto dto) {
        return contractExtMapper.updateGleInfo2Contract(dto);
    }

    @Override
    public Integer callWriteGleInfo2PContract(GlegalContractDto dto) {
        return contractExtMapper.updateGleInfo2PContract(dto);
    }

    @Override
    public Integer callWriteGleInfo2ModifyContract(GlegalContractDto dto) {
        return contractExtMapper.updateGleInfo2ModifyContract(dto);
    }

    @Override
    public Integer callWriteGleInfo2ModifyPContract(GlegalContractDto dto) {
        return contractExtMapper.updateGleInfo2ModifyPContract(dto);
    }

    @Override
    public List<Contract> listByBusinessId(Long businessId) {
        ContractExample contractExample = new ContractExample();
        ContractExample.Criteria criteria = contractExample.createCriteria();
        criteria.andBusinessIdEqualTo(businessId).andStatusEqualTo(7).andStatusEqualTo(9);
        return contractMapper.selectByExample(contractExample);
    }


    /**
     * 校验器
     */
    private final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    @Transactional
    @Override
    public Response salesContract(Long unitId, List<TemplateSalesContractExcelVO> excelVOList) {

        unitId = unitId != null ? unitId : SystemContext.getUnitId();
        boolean result = validSalesContract(unitId, excelVOList);

        if (result) {
            for (TemplateSalesContractExcelVO vo : excelVOList) {
                Contract contract = BeanConverter.copy(vo, Contract.class);
                contract.setApprovalDate(contract.getStartTime());
                contract.setProfitDepartmentId(contract.getUnitId());
                contract.setClasses(vo.getClasses());
                if (contract.getId() == null) {
                    contract.setStatus(5);
                    contract.setDeletedFlag(false);
                    contract.setImportFlag(true);
                    contract.setSealCategory(5);
                    contract.setPublicOrPrivate(0);
                    contract.setImportFlag(Boolean.TRUE);
                    contract.setIsSynchronizeLegalSystemFlag(Boolean.FALSE);
                    contract.setIsDoubleChapterContract(Boolean.FALSE);
                    contract.setIfWatermarking(Boolean.FALSE);
                    contract.setIsElectronicContract(Boolean.FALSE);
                    contract.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    contract.setCreateAt(new Date());
                    contractMapper.insert(contract);
                } else {
                    contract.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    contract.setUpdateAt(new Date());
                    contractMapper.updateByPrimaryKeySelective(contract);
                }
            }
            return Response.response();
        }
        return Response.dataResponse(excelVOList.stream().filter(e -> StringUtils.isNotEmpty(e.getValidResult()))
                .collect(Collectors.toList()));
    }

    private boolean validSalesContract(Long unitId, List<TemplateSalesContractExcelVO> list) {

        list.forEach(c -> c.setValidResult(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (list.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getValidResult()))) {
            return false;
        }

        Set<String> usernames = new HashSet<>(list.size());
        list.forEach(e -> {
            usernames.add(e.getSalesManagerMip());
            usernames.add(e.getManagerMip());
        });
        Map<String, Long> userMap = getUserByUsernames(usernames).stream()
                .collect(Collectors.toMap(UserInfoDto::getUsername, UserInfoDto::getId));

        // 查询当前使用单位下的所有项目类型
        Map<String, ProjectType> projectTypeMap = getProjectTypeByUnitIdMap(unitId);
        // 查询当前使用单位下的业务实体
        Map<String, Long> ouMap = basedataExtService.queryCurrentUnitOu(unitId).stream()
                .collect(Collectors.toMap(OperatingUnitDto::getOperatingUnitName, OperatingUnitDto::getOperatingUnitId, (key1, key2) -> key2));
        // 查询当前使用单位下的销售部门
        Map<String, Long> unitMap = basedataExtService.findUnitDtos(null, unitId.toString()).stream()
                .collect(Collectors.toMap(UnitDto::getUnitName, UnitDto::getId, (key1, key2) -> key2));

        Map<String, String> paymentTypeMap = basedataExtService.getLtcDict("payment_type", null, null).stream()
                .collect(Collectors.toMap(DictDto::getName, DictDto::getCode, (key1, key2) -> key2));

        List<String> contractCodes = list.stream().map(TemplateSalesContractExcelVO::getCode).distinct().collect(Collectors.toList());
        if (contractCodes.size() != list.size()) {
            TemplateSalesContractExcelVO excelVO = new TemplateSalesContractExcelVO();
            excelVO.setValidResult("合同编号存在重复");
            list.clear();
            list.add(excelVO);
            return false;
        }
        Map<String, Long> contractMap = listContractByCode(contractCodes, true).stream().collect(Collectors.toMap(Contract::getCode, Contract::getId));

        List<String> customerCode = list.stream().map(TemplateSalesContractExcelVO::getCustomerCode).distinct().collect(Collectors.toList());
        Map<String, CustomerDto> customerMap = crmExtService.getCustomerByCode(customerCode)
                .stream().collect(Collectors.toMap(CustomerDto::getCrmCode, e -> e));

        for (TemplateSalesContractExcelVO vo : list) {
            List<String> validResultList = new ArrayList<>();

            vo.setId(contractMap.get(vo.getCode()));
            vo.setFrameFlag(vo.getFrameFlag());
            vo.setInvoiceType(vo.getInvoiceType());

            if (vo.getAmount().scale() > 2) {
                validResultList.add("合同总额（含税）小数位不得超过2位");
            }
            // 校验项目类型、赋值项目类型ID
            ProjectType projectType = projectTypeMap.get(vo.getBusinessTypeName());
            if (projectType != null) {
                vo.setBusinessTypeId(projectType.getId());
            } else {
                validResultList.add("当前使用单位无法匹配该项目类型");
            }
            // 校验销售部门、赋值销售部门ID
            vo.setUnitId(unitMap.get(vo.getUnitName()));
            if (vo.getUnitId() == null) {
                validResultList.add("当前使用单位无此销售部门");
            }
            // 校验客户名称、客户CRM编码、赋值客户ID
            Customer customer = customerMap.get(vo.getCustomerCode());
            if (customer != null) {
                if (vo.getCustomerName().equalsIgnoreCase(customer.getName())) {
                    vo.setCustomerId(customer.getId());
                    if (Boolean.TRUE.equals(customer.getInner())) {
                        // 如果内部客户，则所属区域为美的内部
                        vo.setBelongArea(2);
                    } else {
                        // 否则为中国国内
                        vo.setBelongArea(0);
                    }
                    if (Boolean.TRUE.equals(customer.getIsOversea())) {
                        // 海外客户
                        vo.setBelongArea(1);
                    }
                } else {
                    validResultList.add("客户名称与客户CRM编码不匹配");
                }
            } else {
                validResultList.add("根据客户CRM编码未匹配到客户信息");
            }
            // 业务实体校验及ID赋值
            vo.setOuId(ouMap.get(vo.getOuName()));
            if (vo.getOuId() == null) {
                validResultList.add("当前使用单位无此业务实体");
            }
            // 销售经理校验及ID赋值
            vo.setSalesManager(userMap.get(vo.getSalesManagerMip()));
            if (vo.getSalesManager() == null) {
                validResultList.add("销售经理MIP账号不存在");
            }
            // 项目经理校验及ID赋值
            vo.setManager(userMap.get(vo.getManagerMip()));
            if (vo.getManager() == null) {
                validResultList.add("项目经理MIP账号不存在");
            }
            // 支付方式校验及转换
            if (StringUtils.isNotEmpty(vo.getPaymentMethodName())) {
                vo.setPaymentMethod(paymentTypeMap.get(vo.getPaymentMethodName()));
                if (vo.getPaymentMethod() == null) {
                    validResultList.add("支付方式不存在");
                }
            }
            vo.setValidResult(String.join(",", validResultList));
        }
        return list.stream().allMatch(e -> StringUtils.isEmpty(e.getValidResult()));
    }

    private List<UserInfoDto> getUserByUsernames(Set<String> usernames) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "employeeInfo/listUserByUserNames";
        String res = restTemplate.postForObject(url, usernames, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<UserInfoDto>>() {
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response salesSubContract(Long unitId, List<TemplateSalesSubContractExcelVO> excelVoList) {

        unitId = unitId != null ? unitId : SystemContext.getUnitId();
        List<TemplateSalesSubContractExcelVO> filteredList = excelVoList.stream()
                .filter(vo -> vo.getCode() != null)
                .filter(vo -> vo.getName() != null)
                .filter(vo -> vo.getBusinessTypeName() != null)
                .collect(Collectors.toList());
        boolean result = validSalesSubContract(unitId, filteredList);

        if (result) {
            for (TemplateSalesSubContractExcelVO vo : filteredList) {
                Contract contract = BeanConverter.copy(vo, Contract.class);
                if (contract.getId() == null) {
                    contract.setDeletedFlag(Boolean.FALSE);
                    contract.setImportFlag(Boolean.TRUE);
                    contract.setCreateAt(new Date());
                    contract.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    contractMapper.insert(contract);
                    vo.setId(contract.getId());
                } else {
                    contract.setUpdateAt(new Date());
                    contract.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    contractMapper.updateByPrimaryKeySelective(contract);
                }
            }

            List<Long> subContractIds = filteredList.stream().map(TemplateSalesSubContractExcelVO::getId).collect(Collectors.toList());
            ContractProductExample cpExample = new ContractProductExample();
            cpExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andContractIdIn(subContractIds);
            Map<Long, List<ContractProduct>> contractProductMap = contractProductMapper.selectByExample(cpExample).stream()
                    .collect(Collectors.groupingBy(ContractProduct::getContractId));

            InvoicePlanExample ipExample = new InvoicePlanExample();
            ipExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andContractIdIn(subContractIds);
            Map<Long, List<InvoicePlan>> invoicePlanMap = invoicePlanMapper.selectByExample(ipExample).stream()
                    .collect(Collectors.groupingBy(InvoicePlan::getContractId));

            ReceiptPlanExample rpExample = new ReceiptPlanExample();
            rpExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andContractIdIn(subContractIds);
            Map<Long, Long> receiptPlanMap = receiptPlanMapper.selectByExample(rpExample).stream()
                    .collect(Collectors.toMap(ReceiptPlan::getContractId, ReceiptPlan::getId));

            filteredList.forEach(vo -> {
                ContractProduct contractProduct = vo.generateContractProduct();
                List<ContractProduct> contractProducts = contractProductMap.get(vo.getId());
                if (ListUtils.isNotEmpty(contractProducts)) {
                    Optional<ContractProduct> first = contractProducts.stream().filter(e ->
                            vo.getProductTypeName().equals(e.getProductTypeName()) && vo.getTaxValue().equals(e.getTaxValue())
                    ).findFirst();
                    first.ifPresent(product -> contractProduct.setId(product.getId()));
                }
                if (contractProduct.getId() == null) {
                    // 设置子合同ID
                    contractProduct.setContractId(vo.getId());
                    contractProduct.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    contractProduct.setCreateAt(new Date());
                    contractProductMapper.insert(contractProduct);
                } else {
                    contractProduct.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    contractProduct.setUpdateAt(new Date());
                    contractProductMapper.updateByPrimaryKeySelective(contractProduct);
                }

                // 封装开票计划行：与合同产品一对一
                InvoicePlan invoicePlan = vo.generateInvoicePlan();
                List<InvoicePlan> invoicePlans = invoicePlanMap.get(vo.getId());
                if (ListUtils.isNotEmpty(invoicePlans)) {
                    Optional<InvoicePlan> first = invoicePlans.stream().filter(e -> contractProduct.getId().equals(e.getContractProductId())).findFirst();
                    first.ifPresent(plan -> invoicePlan.setId(plan.getId()));
                }
                invoicePlan.setContractId(vo.getId());
                invoicePlan.setContractProductId(contractProduct.getId());
                if (invoicePlan.getId() == null) {
                    invoicePlan.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    invoicePlan.setCreateAt(new Date());
                    invoicePlanMapper.insert(invoicePlan);
                } else {
                    invoicePlan.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    invoicePlan.setUpdateAt(new Date());
                    invoicePlanMapper.updateByPrimaryKeySelective(invoicePlan);
                }

            });
            // 根据合同产品列表更新子合同总额（不含税）
            contractExtMapper.updateSubSumAmount(subContractIds);
            // 根据子合同ID列表查询子合同更新合同总额（不含税）
            contractExtMapper.updateMainSumAmount(subContractIds);
            return Response.response();
        }
        return Response.dataResponse(filteredList.stream().filter(e -> StringUtils.isNotEmpty(e.getValidResult())).collect(Collectors.toList()));
    }

    private boolean validSalesSubContract(Long unitId, List<TemplateSalesSubContractExcelVO> list) {

        list.forEach(c -> c.setValidResult(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (list.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getValidResult()))) {
            return false;
        }

        Set<String> usernames = list.stream().map(TemplateSalesSubContractExcelVO::getManagerMip).collect(Collectors.toSet());
        Map<String, Long> userMap = getUserByUsernames(usernames).stream()
                .collect(Collectors.toMap(UserInfoDto::getUsername, UserInfoDto::getId));

        // 查询业务分类（虚拟单位）
        Map<String, Long> unitMap = basedataExtService.findUnitDtos(null, unitId.toString())
                .stream().collect(Collectors.toMap(UnitDto::getUnitName, UnitDto::getId, (key1, key2) -> key2));
        // 查询业务模式（项目类型）
        Map<String, ProjectType> projectTypeMap = getProjectTypeByUnitIdMap(unitId);
        // 查询服务类型（组织参数→服务类型）
        Set<String> productTypeSet = organizationCustomDictService.queryByName("服务类型", unitId, OrgCustomDictOrgFrom.COMPANY);

        Map<String, OperatingUnit> operatingUnitMap = getAllOperatingUnit().stream()
                .collect(Collectors.toMap(OperatingUnit::getOperatingUnitName, e -> e, (e1, e2) -> e1));

        List<String> mainContractCodes = list.stream().map(TemplateSalesSubContractExcelVO::getMainCode).distinct().collect(Collectors.toList());
        Map<String, Contract> mainContractMap = listContractByCode(mainContractCodes, true).stream().collect(Collectors.toMap(Contract::getCode, e -> e));

        List<String> subContractCodes = list.stream().map(TemplateSalesSubContractExcelVO::getCode).distinct().collect(Collectors.toList());
        if (subContractCodes.size() != list.size()) {
            TemplateSalesSubContractExcelVO excelVO = new TemplateSalesSubContractExcelVO();
            excelVO.setValidResult("子合同编号存在重复");
            list.clear();
            list.add(excelVO);
            return false;
        }
        Map<String, Long> subContractMap = listContractByCode(subContractCodes, false)
                .stream().collect(Collectors.toMap(Contract::getCode, Contract::getId));

        List<String> customerCode = list.stream().map(TemplateSalesSubContractExcelVO::getCustomerCode).distinct().collect(Collectors.toList());
        Map<String, CustomerDto> customerMap = crmExtService.getCustomerByCode(customerCode)
                .stream().collect(Collectors.toMap(CustomerDto::getCrmCode, e -> e));

        List<String> taxRates = list.stream().map(TemplateSalesSubContractExcelVO::getTaxRate).distinct().collect(Collectors.toList());
        Map<String, TaxInfoDto> taxInfoMap = getTaxInfoByTaxRates(taxRates, 2).stream()
                .collect(Collectors.toMap(TaxInfoDto::getTaxRate, e -> e));

        for (TemplateSalesSubContractExcelVO vo : list) {

            List<String> validResultList = new ArrayList<>();
            // 校验主合同编号、赋值parentId
            Contract mainContract = mainContractMap.get(vo.getMainCode());
            if (mainContract != null) {
                vo.setParentId(mainContract.getId());
                vo.setFrameFlag(mainContract.getFrameFlag());
                vo.setOuId(mainContract.getOuId());
                vo.setStatus(mainContract.getStatus());
                vo.setUnitId(mainContract.getUnitId());
                vo.setCurrency(mainContract.getCurrency());
            } else {
                validResultList.add("PAM主合同编号不存在或存在多个");
            }

            // 如果合同编码存在，则赋值合同id给数据，修改数据
            vo.setId(subContractMap.get(vo.getCode()));


            // 校验子合同业务分类（销售部门）、赋值ID
            vo.setProfitDepartmentId(unitMap.get(vo.getProfitDepartmentName()));
            if (vo.getProfitDepartmentId() == null) {
                validResultList.add("当前使用单位无此业务分类");
            }

            // 校验业务模式（项目类型）、赋值ID
            ProjectType projectType = projectTypeMap.get(vo.getBusinessTypeName());
            if (projectType != null) {
                vo.setBusinessTypeId(projectType.getId());
            } else {
                validResultList.add("当前使用单位无法匹配该项目类型");
            }

            // 项目经理校验及ID赋值
            vo.setManager(userMap.get(vo.getManagerMip()));
            if (vo.getManager() == null) {
                validResultList.add("项目经理MIP账号不存在");
            }

            // 校验开票客户
            // 校验客户名称、客户CRM编码、赋值客户ID
            Customer customer = customerMap.get(vo.getCustomerCode());
            if (customer != null) {
                if (vo.getCustomerName().equalsIgnoreCase(customer.getName())) {
                    vo.setCustomerId(customer.getId());
                } else {
                    validResultList.add("开票客户名称与客户CRM编码不匹配");
                }
            } else {
                validResultList.add("根据客户CRM编码未匹配到客户信息");
            }

            // 校验服务类型
            if (!productTypeSet.contains(vo.getProductTypeName())) {
                validResultList.add("服务类型与组织参数中配置不一致");
            }

            OperatingUnit operatingUnit = operatingUnitMap.get(vo.getOuName());
            if (operatingUnit != null) {
                vo.setOuId(operatingUnit.getId());
            } else {
                validResultList.add("根据业务实体名称查找不到业务实体");
            }

            TaxInfoDto taxInfoDto = taxInfoMap.get(vo.getTaxRate());
            if (taxInfoDto != null) {
                vo.setTaxId(taxInfoDto.getId());
                vo.setTaxValue(new BigDecimal(taxInfoDto.getTaxValue()));
            } else {
                validResultList.add("税率不存在");
            }

            //校验校验填写内容必须存在“虚拟部门对应业务分部”配置里
            String businessSegment = vo.getBusinessSegment();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(businessSegment)) {
                Set<String> organizationCustomDict = organizationCustomDictService.queryByName("虚拟部门对应业务分部", vo.getUnitId(), OrgCustomDictOrgFrom.VIRTUAL_ORG);
                if (organizationCustomDict != null && !organizationCustomDict.isEmpty()) {

                    Set<String> allowedSegments = organizationCustomDict.stream()
                            .flatMap(s -> Arrays.stream(s.split(",")))
                            .map(String::trim)
                            .collect(Collectors.toSet());

                    if (!allowedSegments.contains(businessSegment.trim())) {
                        validResultList.add("业务分部必须存在“虚拟部门对应业务分部”配置里");
                    }
                }
            }

            vo.setValidResult(String.join("，", validResultList));
        }
        return list.stream().allMatch(e -> StringUtils.isEmpty(e.getValidResult()));
    }

    private List<OperatingUnit> getAllOperatingUnit() {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "operatingUnit/findByAll";
        String res = restTemplate.getForObject(url, String.class);
        Asserts.notNull(res, Code.ERROR);
        DataResponse<List<OperatingUnit>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnit>>>() {
        });
        Assert.isTrue(dataResponse.getCode() == 0);
        Assert.notNull(dataResponse.getData(), "系统内业务实体为空");
        return dataResponse.getData();
    }

    private List<TaxInfoDto> getTaxInfoByTaxRates(List<String> taxRates, Integer taxType) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "finance/taxInfo/getTaxInfoByTaxRate?taxType=" + taxType;
        String res = restTemplate.postForObject(url, taxRates, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<TaxInfoDto>>() {
        });
    }

    @Transactional
    @Override
    public List<TemplateInvoicePlanExcelVO> invoicePlan(List<TemplateInvoicePlanExcelVO> excelVOList) {

        boolean result = validInvoicePlan(excelVOList);

        if (result) {
            Map<Long, List<TemplateInvoicePlanExcelVO>> excelVOListGroup = excelVOList.stream()
                    .collect(Collectors.groupingBy(TemplateInvoicePlanExcelVO::getContractProductId));
            List<Long> planIds = new ArrayList<>(excelVOListGroup.size());
            for (List<TemplateInvoicePlanExcelVO> voList : excelVOListGroup.values()) {
                InvoicePlan invoicePlan = voList.get(0).generateInvoicePlan();
                if (invoicePlan.getId() == null) {
                    invoicePlan.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    invoicePlan.setCreateAt(new Date());
                    invoicePlanMapper.insert(invoicePlan);
                } else {
                    invoicePlan.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    invoicePlan.setUpdateAt(new Date());
                    invoicePlanMapper.updateByPrimaryKeySelective(invoicePlan);
                }
                planIds.add(invoicePlan.getId());
                for (TemplateInvoicePlanExcelVO vo : voList) {
                    InvoicePlanDetail invoicePlanDetail = vo.generateInvoicePlanDetail();
                    if (invoicePlanDetail.getId() != null) {
                        invoicePlanDetail.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                        invoicePlanDetail.setUpdateAt(new Date());
                        invoicePlanDetailMapper.updateByPrimaryKeySelective(invoicePlanDetail);
                    } else {
                        invoicePlanDetail.setPlanId(invoicePlan.getId());
                        invoicePlanDetail.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                        invoicePlanDetail.setCreateAt(new Date());
                        invoicePlanDetailMapper.insert(invoicePlanDetail);
                    }
                }
            }
            invoicePlanExtMapper.updateAmountByIds(planIds);
            return Collections.emptyList();
        }
        return excelVOList.stream().filter(e -> StringUtils.isNotEmpty(e.getValidResult())).collect(Collectors.toList());
    }

    private boolean validInvoicePlan(List<TemplateInvoicePlanExcelVO> excelVOList) {

        excelVOList.forEach(c -> c.setValidResult(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (excelVOList.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getValidResult()))) {
            return false;
        }

        List<String> detailCodes = excelVOList.stream().map(TemplateInvoicePlanExcelVO::getDetailCode).distinct().collect(Collectors.toList());
        if (detailCodes.size() != excelVOList.size()) {
            TemplateInvoicePlanExcelVO vo = new TemplateInvoicePlanExcelVO();
            vo.setValidResult("开票计划编号不能重复");
            excelVOList.clear();
            excelVOList.add(vo);
            return false;
        }

        List<String> subContractCodes = excelVOList.stream().map(TemplateInvoicePlanExcelVO::getContractCode).distinct().collect(Collectors.toList());
        Map<String, Contract> subContractMap = listContractByCode(subContractCodes, false).stream()
                .collect(Collectors.toMap(Contract::getCode, e -> e));

        InvoicePlanDetailExample planDetailExample = new InvoicePlanDetailExample();
        planDetailExample.createCriteria().andDeletedFlagEqualTo(false).andCodeIn(detailCodes);
        Map<String, InvoicePlanDetail> invoicePlanDetailMap = invoicePlanDetailMapper.selectByExample(planDetailExample)
                .stream().collect(Collectors.toMap(InvoicePlanDetail::getCode, e -> e));

        Map<Long, List<ContractProduct>> contractProductMap = Collections.emptyMap();
        Map<Long, List<InvoicePlan>> invoicePlanListMap = Collections.emptyMap();
        if (subContractMap.size() > 0) {
            List<Long> subContractIds = subContractMap.values().stream().map(Contract::getId).collect(Collectors.toList());
            ContractProductExample productExample = new ContractProductExample();
            productExample.createCriteria().andDeletedFlagEqualTo(false).andContractIdIn(subContractIds);
            contractProductMap = contractProductMapper.selectByExample(productExample).stream()
                    .collect(Collectors.groupingBy(ContractProduct::getContractId));

            invoicePlanListMap = invoicePlanExtMapper.selectBySubContractCodes(subContractIds).stream()
                    .collect(Collectors.groupingBy(InvoicePlan::getContractId));
        }

        for (TemplateInvoicePlanExcelVO vo : excelVOList) {

            List<String> validResultList = new ArrayList<>();

            Contract subContract = subContractMap.get(vo.getContractCode());
            if (subContract != null) {
                if (!vo.getName().equals(subContract.getName())) {
                    validResultList.add("子合同名称与编码不对应");
                } else {
                    vo.setContractId(subContract.getId());

                    InvoicePlanDetail planDetail = invoicePlanDetailMap.get(vo.getDetailCode());
                    if (planDetail != null) {
                        vo.setPlanId(planDetail.getPlanId());
                        vo.setPlanDetailId(planDetail.getId());
                    }

                    List<ContractProduct> contractProducts = contractProductMap.get(subContract.getId());
                    Optional<ContractProduct> first = contractProducts.stream()
                            .filter(e -> vo.getProductTypeName().equals(e.getProductTypeName()) &&
                                    vo.getRate().multiply(new BigDecimal("100")).compareTo(e.getTaxValue()) == 0).findFirst();
                    if (first.isPresent()) {
                        ContractProduct contractProduct = first.get();
                        vo.setContractProductId(contractProduct.getId());
                        vo.setTaxId(contractProduct.getTaxId());
                        // 根据合同id、合同产品id查询开票计划行
                        List<InvoicePlan> invoicePlanList = invoicePlanListMap.get(subContract.getId());
                        if (ListUtils.isNotEmpty(invoicePlanList)) {
                            Optional<InvoicePlan> firstInvoicePlan = invoicePlanList.stream()
                                    .filter(e -> contractProduct.getId().equals(e.getContractProductId())).findFirst();
                            firstInvoicePlan.ifPresent(e -> vo.setPlanId(e.getId()));
                        }
                    } else {
                        validResultList.add("开票计划关联子合同产品失败");
                    }
                }
            } else {
                validResultList.add("子合同编码不存在或存在多个");
            }

            vo.setValidResult(String.join(",", validResultList));
        }
        return excelVOList.stream().allMatch(e -> StringUtils.isEmpty(e.getValidResult()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response receiptPlan(List<TemplateReceiptPlanExcelVO> list) {

        boolean result = validReceiptPlan(list);

        if (result) {
            List<Long> receiptPlanIds = new ArrayList<>();

            Map<String, List<TemplateReceiptPlanExcelVO>> voGroup = list.stream()
                    .collect(Collectors.groupingBy(TemplateReceiptPlanExcelVO::getSubContractCode));

            for (List<TemplateReceiptPlanExcelVO> voList : voGroup.values()) {
                ReceiptPlan receiptPlan = new ReceiptPlan();
                if (voList.get(0).getPlanId() == null) {
                    receiptPlan.setContractId(voList.get(0).getContractId());
                    receiptPlan.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    receiptPlan.setCreateAt(new Date());
                    receiptPlan.setDeletedFlag(false);
                    receiptPlanMapper.insert(receiptPlan);
                }
                for (TemplateReceiptPlanExcelVO vo : voList) {
                    ReceiptPlanDetail detail = vo.getReceiptPlanDetail();
                    detail.setPlanId(receiptPlan.getId());
                    detail.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    detail.setCreateAt(new Date());
                    if (detail.getId() == null) {
                        receiptPlanDetailMapper.insert(detail);
                    } else {
                        receiptPlanDetailMapper.updateByPrimaryKeySelective(detail);
                    }
                }
                receiptPlanIds.add(receiptPlan.getId());
            }
            // 更新笔数
            ReceiptPlanDetailExample planDetailExample = new ReceiptPlanDetailExample();
            planDetailExample.createCriteria().andDeletedFlagEqualTo(false).andPlanIdIn(receiptPlanIds);
            List<ReceiptPlanDetail> receiptPlanDetailList = receiptPlanDetailMapper.selectByExample(planDetailExample);
            Map<Long, List<ReceiptPlanDetail>> receiptPlanDetailGroup = receiptPlanDetailList.stream()
                    .collect(Collectors.groupingBy(ReceiptPlanDetail::getPlanId));
            receiptPlanDetailGroup.values().forEach(ds -> {
                int i = 1;
                for (ReceiptPlanDetail detail : ds) {
                    ReceiptPlanDetail rpd = new ReceiptPlanDetail();
                    rpd.setId(detail.getId());
                    rpd.setNum(i++);
                    receiptPlanDetailMapper.updateByPrimaryKeySelective(rpd);
                }
            });
            receiptPlanExtMapper.updateAmountByIds(receiptPlanIds);
            return Response.response();
        }
        return Response.dataResponse(list.stream().filter(e -> StringUtils.isNotEmpty(e.getValidResult())).collect(Collectors.toList()));
    }

    private boolean validReceiptPlan(List<TemplateReceiptPlanExcelVO> list) {

        list.forEach(c -> c.setValidResult(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (list.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getValidResult()))) {
            return false;
        }

        List<String> subContractCodes = list.stream().map(TemplateReceiptPlanExcelVO::getSubContractCode)
                .distinct().collect(Collectors.toList());
        Map<String, Contract> subContractMap = listContractByCode(subContractCodes, false).stream()
                .collect(Collectors.toMap(Contract::getCode, e -> e));

        Map<Long, Long> receiptPlanMap = Collections.emptyMap();
        if (!subContractMap.isEmpty()) {
            List<Long> contractIds = subContractMap.values().stream().map(Contract::getId).collect(Collectors.toList());
            ReceiptPlanExample receiptPlanExample = new ReceiptPlanExample();
            receiptPlanExample.createCriteria()
                    .andDeletedFlagEqualTo(false)
                    .andContractIdIn(contractIds);
            receiptPlanMap = receiptPlanMapper.selectByExample(receiptPlanExample).stream()
                    .collect(Collectors.toMap(ReceiptPlan::getContractId, ReceiptPlan::getId));
        }

        List<String> detailCodes = list.stream().map(TemplateReceiptPlanExcelVO::getDetailCode).distinct().collect(Collectors.toList());
        if (detailCodes.size() != list.size()) {
            TemplateReceiptPlanExcelVO excelVO = new TemplateReceiptPlanExcelVO();
            excelVO.setValidResult("回款计划编号存在重复");
            list.clear();
            list.add(excelVO);
            return false;
        }
        ReceiptPlanDetailExample planDetailExample = new ReceiptPlanDetailExample();
        planDetailExample.createCriteria().andDeletedFlagEqualTo(false).andCodeIn(detailCodes);
        Map<String, Long> receiptPlanDetailMap = receiptPlanDetailMapper.selectByExample(planDetailExample).stream()
                .collect(Collectors.toMap(ReceiptPlanDetail::getCode, ReceiptPlanDetail::getId));

        for (TemplateReceiptPlanExcelVO vo : list) {
            List<String> validResultList = new ArrayList<>();

            Contract subContract = subContractMap.get(vo.getSubContractCode());
            if (subContract != null) {
                if (!vo.getSubContractName().equals(subContract.getName())) {
                    validResultList.add("子合同名称不匹配");
                } else {
                    vo.setContractId(subContract.getId());
                    vo.setPlanId(receiptPlanMap.get(subContract.getId()));
                }
            } else {
                validResultList.add("子合同不存在");
            }
            vo.setDetailId(receiptPlanDetailMap.get(vo.getDetailCode()));
            vo.setValidResult(String.join(",", validResultList));
        }
        return list.stream().allMatch(e -> StringUtils.isEmpty(e.getValidResult()));
    }

    /**
     * 根据使用单位查询项目类型
     *
     * @param unitId 使用单位
     * @return
     */
    private Map<String, ProjectType> getProjectTypeByUnitIdMap(Long unitId) {
        ProjectTypeExample example = new ProjectTypeExample();
        ProjectTypeExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(false).andUnitIdEqualTo(unitId);
        List<ProjectType> projectTypeList = projectTypeMapper.selectByExampleWithBLOBs(example);
        return projectTypeList.stream().collect(Collectors.toMap(ProjectType::getName, Function.identity(), (key1, key2) -> key2));
    }

    /**
     * 根据合同编号查询合同信息
     *
     * @param codes 合同编号集合
     * @return
     */
    private List<Contract> listContractByCode(List<String> codes, boolean isParent) {
        ContractExample example = new ContractExample();
        ContractExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(false).andCodeIn(codes);
        if (isParent) {
            criteria.andParentIdIsNull();
        } else {
            criteria.andParentIdIsNotNull();
        }
        return contractMapper.selectByExample(example);
    }

    @Override
    public List<ContractVO> findBusinessList(List<String> ids) {
        ArrayList<ContractVO> list = new ArrayList<>();
        //Map<String,String>  map = new HashMap<>();
        for (String item : ids) {
            ContractVO contractDetailWithBudget = getContractDetailWithBudget(Long.valueOf(item));
            if (!ObjectUtils.isEmpty(contractDetailWithBudget)) {
                list.add(contractDetailWithBudget);
            }
        }

        List<ContractVO> arrays = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            arrays = list.stream().filter(a ->
                    ObjectUtil.isNotNull(a.getBusinessId())).collect(Collectors.collectingAndThen(Collectors.toCollection(()
                    -> new TreeSet<>(Comparator.comparing(ContractVO::getBusinessId))), ArrayList::new));
        }
        //对关联商机号去重处理 chenchong by 20220610
//        map = list.stream().filter(o -> StringUtils.isNotEmpty(o.getBusinessCode())).collect(Collectors.toMap(ContractVO::getBusinessCode,ContractVO::getCode,(v1, v2)->v2));
//        for (ContractVO vo : list){
//            String businessCode = vo.getBusinessCode();
//            if (!(map.containsKey(businessCode) && map.get(businessCode).equals(vo.getCode()))){
//                vo.setBusinessCode(null);
//                vo.setBusinessName(null);
//            }
//        }
        return arrays;
    }

    @Override
    public List<ProjectBusinessRsDto> getBusinessByContractIds(List<Long> contractIds, Long currentProjectId) {
        if (CollectionUtils.isEmpty(contractIds)) return new ArrayList();

        ContractExample example = new ContractExample();
        example.createCriteria().andIdIn(contractIds);
        List<Contract> contractList = contractMapper.selectByExample(example);

        //获取关联商机：子合同有的话取子合同的赢单商机，否则取主合同的赢单商机
        List<Long> businessIdList = new ArrayList<>();
        List<Long> parentContractIdList = new ArrayList<>();
        for (Contract contract : contractList) {
            if (contract.getBusinessId() != null) {
                businessIdList.add(contract.getBusinessId());
            } else {
                if (contract.getParentId() != null) {
                    parentContractIdList.add(contract.getParentId());
                }
            }
        }

        // 查询主合同商机
        if (ListUtil.isPresent(parentContractIdList)) {
            example = new ContractExample();
            example.createCriteria().andIdIn(parentContractIdList);
            List<Contract> parentContractList = contractMapper.selectByExample(example);
            for (Contract parentContract : parentContractList) {
                if (parentContract.getBusinessId() != null) {
                    businessIdList.add(parentContract.getBusinessId());
                }
            }
        }

        //去重
        businessIdList = businessIdList.stream().distinct().collect(Collectors.toList());
        List<ProjectBusinessRsDto> businessList = new ArrayList<>();

        //如果是非立项场景，报价占有回写，以及占用成本计算不包含当前项目的数据
        Map<Long, ProjectBusinessRs> costInfoMap = new HashMap<>();
        if (currentProjectId != null) {
            ProjectBusinessRsExample rsExample = new ProjectBusinessRsExample();
            rsExample.createCriteria().andProjectIdEqualTo(currentProjectId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<ProjectBusinessRs> businessRsList = projectBusinessRsMapper.selectByExample(rsExample);
            costInfoMap = businessRsList.stream().collect(Collectors.toMap(ProjectBusinessRs::getBusinessId, Function.identity()));
        }

        //商机报价单信息
        for (Long businessId : businessIdList) {
            ProjectBusinessRsDto dto = crmExtService.findQuotationInfoByBusinessId(businessId);
            if (Objects.nonNull(dto)) {
                //若商机有关联报价单，计算关联项目报价占用
                if (dto.getQuotationId() != null) {
                    this.calculateCostUsed(dto, currentProjectId, costInfoMap);
                }
                businessList.add(dto);
            }
        }

        return businessList;
    }

    /**
     * 计算关联项目报价占用
     *
     * @param dto
     * @return
     */
    @Override
    public ProjectBusinessRsDto calculateCostUsed(ProjectBusinessRsDto dto, Long currentProjectId, Map<Long, ProjectBusinessRs> costInfoMap) {
        //关联项目预算占用
        List<ProjectSummaryBudget> projectSummaryBudgetList = contractExtMapper.findProjectCostUsed(dto.getBusinessId());
        //变更场景，占用成本计算不包含当前项目的数据
        if (currentProjectId != null) {
            Iterator<ProjectSummaryBudget> iterator = projectSummaryBudgetList.iterator();
            while (iterator.hasNext()) {
                ProjectSummaryBudget budget = iterator.next();
                if (Objects.equals(budget.getId(), currentProjectId)) {
                    iterator.remove();
                }
            }
        }
        dto.setProjectSummaryBudgetList(projectSummaryBudgetList);

        //已占用成本
        BigDecimal costTotalUsed = BigDecimal.ZERO;
        for (ProjectSummaryBudget budget : projectSummaryBudgetList) {
            costTotalUsed = costTotalUsed.add(Optional.ofNullable(budget.getCostUsed()).orElse(BigDecimal.ZERO));
        }
        dto.setCostTotalUsed(costTotalUsed);

        //报价剩余可用成本(不含税)
        if (dto.getContractTotalBudget() != null && dto.getCostTotalUsed() != null) {
            dto.setRemainTotalUsed(dto.getContractTotalBudget().subtract(dto.getCostTotalUsed()));
        }

        //变更场景，本次占用成本回写
        if (costInfoMap != null && costInfoMap.get(dto.getBusinessId()) != null) {
            ProjectBusinessRs costInfo = costInfoMap.get(dto.getBusinessId());
            dto.setCostUsed(costInfo.getCostUsed());
            dto.setCostUsedPercent(costInfo.getCostUsedPercent());
        }
        return dto;
    }

    /**
     * 校验子合同是否已关联非作废状态的项目，并且已占用商机成本
     *
     * @param contractId
     * @return
     */
    public List<ContractVo> isChildCanbeChange(Long contractId) {
        List<ContractVo> resultList = new ArrayList<>();
        //查询所有子合同
        ContractExample example = new ContractExample();
        example.createCriteria().andParentIdEqualTo(contractId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<Contract> childContractList = contractMapper.selectByExample(example);

        for (Contract childContract : childContractList) {
            /** 和前端约定的返回格式：childContractId：true(可以变更)，false(不能变更) */
            ContractVo result = new ContractVo(childContract.getId(), Boolean.TRUE);
            if (childContract.getBusinessId() != null) {
                ProjectBusinessRsDto dto = new ProjectBusinessRsDto();
                dto.setBusinessId(childContract.getBusinessId());
                //计算关联项目报价占用，如果占用大于0，商机不允许变更
                this.calculateCostUsed(dto, null, null);
                if (dto.getCostTotalUsed() != null && dto.getCostTotalUsed().compareTo(BigDecimal.ZERO) > 0) {
                    result.setChangeFlag(Boolean.FALSE);
                }
            }
            resultList.add(result);
        }
        return resultList;
    }

    @Override
    public void copyToDataIoContract(String unitCodes) {
        List<String> unitCodeList;
        if (StringUtils.isNotEmpty(unitCodes)) {
            unitCodeList = Arrays.stream(unitCodes.split(",")).collect(Collectors.toList());
        } else {
            // 广东美控智慧建筑有限公司
            unitCodeList = Collections.singletonList("MKZH");
        }
        contractExtMapper.updateDataIoContract();
        List<Long> ids = contractExtMapper.selectDataIoContractIds();
        ListUtils.splistList(ids, 500).forEach(list ->
                contractExtMapper.updateDataIoContractExcludingTaxAmount(list)
        );
        contractExtMapper.copyToDataIoContractByUnitCode(unitCodeList);
    }

    @Override
    public ContractOrigin packageContractOrigin(Contract contract, Contract parentContract) {
        ContractOrigin contractOrigin = new ContractOrigin();
        BeanUtils.copyProperties(contract, contractOrigin);
        contractOrigin.setContractId(contract.getId());
        contractOrigin.setId(null);
        // 业务实体
        if (contractOrigin.getOuId() != null) {
            final OperatingUnit operatingUnit = CacheDataUtils.findOuById(contractOrigin.getOuId());
            if (operatingUnit != null) {
                contractOrigin.setOuName(operatingUnit.getOperatingUnitName());
            }
        }
        //销售部门
        if (contractOrigin.getUnitId() != null) {
            final Unit unit = CacheDataUtils.findUnitById(contractOrigin.getUnitId());
            if (unit != null) {
                contractOrigin.setUnitName(unit.getUnitName());
            }
        }
        //销售经理
        if (contractOrigin.getSalesManager() != null) {
            final UserInfo userInfo = CacheDataUtils.findUserById(contractOrigin.getSalesManager());
            if (userInfo != null) {
                contractOrigin.setSalesManagerName(userInfo.getName());
            }
        }
        //项目经理
        if (contractOrigin.getManager() != null) {
            final UserInfo userInfo = CacheDataUtils.findUserById(contractOrigin.getManager());
            if (userInfo != null) {
                contractOrigin.setManagerName(userInfo.getName());
            }
        }
        //利润部门
        if (contractOrigin.getProfitDepartmentId() != null) {
            final Unit unit = CacheDataUtils.findUnitById(contractOrigin.getProfitDepartmentId());
            if (unit != null) {
                contractOrigin.setProfitDepartmentName(unit.getUnitName());
            }
        }
        //项目类型
        if (contractOrigin.getBusinessTypeId() != null) {
            ProjectType projectType = projectTypeService.selectByPrimaryKey(contractOrigin.getBusinessTypeId());
            if (projectType != null) {
                contractOrigin.setBusinessTypeName(projectType.getName());
            }
        }
        //父合同信息
        if (contractOrigin.getParentId() != null && parentContract != null) {
            contractOrigin.setParentCode(parentContract.getCode());
            contractOrigin.setParentName(parentContract.getName());
        }
        //行业
        if (contractOrigin.getIndustry() != null) {
            String industryStr = getIndustryStr(contractOrigin.getIndustry());
            contractOrigin.setIndustryLevel(industryStr);
        }
        //签约中心
        if (contractOrigin.getSigningCenterLevel() != null) {
            String signingCenterStr = getSigningCenterStr(contractOrigin.getSigningCenter());
            contractOrigin.setSigningCenterLevel(signingCenterStr);
        }
        //创建人
        if (contractOrigin.getCreateBy() != null) {
            final UserInfo userInfo = CacheDataUtils.findUserById(contractOrigin.getCreateBy());
            if (userInfo != null) {
                contractOrigin.setCreateByName(userInfo.getName());
            }
        }
        return contractOrigin;
    }

    private ContractOrigin packageContractOriginBatch(Contract contract,
                                                      Contract parentContract,
                                                      Map<Long, String> projectTypeMap,
                                                      Map<Long, String> ouMap,
                                                      Map<Long, String> unitMap,
                                                      Map<Long, String> userMap) {
        ContractOrigin contractOrigin = new ContractOrigin();
        BeanUtils.copyProperties(contract, contractOrigin);
        // 业务实体
        if (contractOrigin.getOuId() != null) {
            contractOrigin.setOuName(ouMap.get(contractOrigin.getOuId()));
        }
        //销售部门
        if (contractOrigin.getUnitId() != null) {
            contractOrigin.setUnitName(unitMap.get(contractOrigin.getUnitId()));
        }
        //销售经理
        if (contractOrigin.getSalesManager() != null) {
            contractOrigin.setSalesManagerName(userMap.get(contractOrigin.getSalesManager()));
        }
        //项目经理
        if (contractOrigin.getManager() != null) {
            contractOrigin.setManagerName(userMap.get(contractOrigin.getManager()));
        }
        //利润部门
        if (contractOrigin.getProfitDepartmentId() != null) {
            contractOrigin.setProfitDepartmentName(unitMap.get(contractOrigin.getProfitDepartmentId()));
        }
        //项目类型
        if (contractOrigin.getBusinessTypeId() != null) {
            contractOrigin.setBusinessTypeName(projectTypeMap.get(contractOrigin.getBusinessTypeId()));
        }
        //父合同信息
        if (contractOrigin.getParentId() != null && parentContract != null) {
            contractOrigin.setParentCode(parentContract.getCode());
            contractOrigin.setParentName(parentContract.getName());
        }
        //行业
        if (contractOrigin.getIndustry() != null) {
            String industryStr = getIndustryStr(contractOrigin.getIndustry());
            contractOrigin.setIndustryLevel(industryStr);
        }
        //签约中心
        if (contractOrigin.getSigningCenterLevel() != null) {
            String signingCenterStr = getSigningCenterStr(contractOrigin.getSigningCenter());
            contractOrigin.setSigningCenterLevel(signingCenterStr);
        }
        //创建人
        if (contractOrigin.getCreateBy() != null) {
            contractOrigin.setCreateByName(userMap.get(contractOrigin.getCreateBy()));
        }
        contractOrigin.setContractId(contract.getId());
        contractOrigin.setId(null);
        contractOrigin.setCreateBy(20230616L);
        contractOrigin.setCreateAt(new Date());
        contractOrigin.setUpdateBy(null);
        contractOrigin.setUpdateAt(null);
        contractOrigin.setDeletedFlag(DeletedFlag.VALID.code());
        return contractOrigin;
    }

    /**
     * contractId不为空，指定保存某个合同的初始版本，已存在的话覆盖更新
     *
     * @param contractId
     * @return
     */
    @Override
    public Integer contractOriginInit(Long contractId) {
        //查询已备份的合同
        List<Long> contractIdListExist = contractExtMapper.checkContractOriginExist();

        List<Integer> statusList = Arrays.asList(
                ContractStatus.EFFECTIVE.getCode(),
                ContractStatus.FREEZE.getCode(),
                ContractStatus.COMPLETE.getCode(),
                ContractStatus.CHANGING.getCode()
        );
        ContractExample example = new ContractExample();
        example.createCriteria().andStatusIn(statusList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<Contract> contractList = contractMapper.selectByExample(example);

        if (contractId != null) {
            //只备份指定的合同
            contractList.removeIf(s -> !Objects.equals(s.getId(), contractId));
        } else if (CollectionUtils.isNotEmpty(contractIdListExist)) {
            //备份过的合同不再备份
            contractList.removeIf(s -> contractIdListExist.contains(s.getId()));
        }
        if (CollectionUtils.isEmpty(contractList)) return 0;
        Map<Long, Contract> contractMap = contractList.stream().collect(Collectors.toMap(Contract::getId, Function.identity()));

        //批量查询ou
        Map<Long, String> ouMap = new HashMap<>();
        List<Long> ouIdList = contractList.stream().map(Contract::getOuId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        for (Long ouId : ouIdList) {
            OperatingUnit operatingUnit = CacheDataUtils.findOuById(ouId);
            if (operatingUnit != null) {
                ouMap.put(ouId, operatingUnit.getOperatingUnitName());
            }
        }

        //批量查询单位
        Map<Long, String> unitMap = new HashMap<>();
        List<Long> unitIdList = contractList.stream().flatMap(c -> Stream.of(c.getUnitId(), c.getProfitDepartmentId())).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        for (Long unitId : unitIdList) {
            final Unit unit = CacheDataUtils.findUnitById(unitId);
            if (unit != null) {
                unitMap.put(unitId, unit.getUnitName());
            }
        }

        //批量查询用户
        Map<Long, String> userMap = new HashMap<>();
        List<Long> userIdList = contractList.stream().flatMap(c -> Stream.of(c.getSalesManager(), c.getManager(), c.getCreateBy())).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        for (Long userId : userIdList) {
            UserInfo userInfo = CacheDataUtils.findUserById(userId);
            if (userInfo != null) {
                userMap.put(userId, userInfo.getName());
            }
        }

        //批量查询项目类型
        List<Long> businessTypeIdList = contractList.stream().map(Contract::getBusinessTypeId).distinct().collect(Collectors.toList());
        ProjectTypeExample projectTypeExample = new ProjectTypeExample();
        projectTypeExample.createCriteria().andIdIn(businessTypeIdList);
        Map<Long, String> projectTypeMap = projectTypeMapper.selectByExample(projectTypeExample).stream().collect(Collectors.toMap(ProjectType::getId, ProjectType::getName));

        //批量查询审批通过的合同历史
        Map<Long, List<ContractHis>> contractHisMap = contractHisExtMapper.queryChangePass().stream().collect(Collectors.groupingBy(ContractHis::getContractId));

        List<ContractOrigin> contractOriginList = new ArrayList<>();
        for (Contract contract : contractList) {
            //校验合同是否做过变更
            List<ContractHis> contractHisList = contractHisMap.get(contract.getId());
            if (contractHisList == null) {
                contractOriginList.add(packageContractOriginBatch(contract, contractMap.get(contract.getParentId()), projectTypeMap, ouMap, unitMap, userMap));
            } else {
                ContractHis contractHis = contractHisList.stream().min(Comparator.comparing(ContractHis::getCreateAt)).orElse(new ContractHis());
                Contract contractChanged = new Contract();
                BeanUtils.copyProperties(contractHis, contractChanged);
                contractChanged.setId(contract.getId());
                contractOriginList.add(packageContractOriginBatch(contractChanged, contractMap.get(contractChanged.getParentId()), projectTypeMap, ouMap, unitMap, userMap));
            }
        }

        //备份指定合同
        if (contractId != null) {
            ContractOrigin contractOrigin = contractOriginList.get(0);
            ContractOriginExample contractOriginExample = new ContractOriginExample();
            contractOriginExample.createCriteria().andContractIdEqualTo(contractId);
            List<ContractOrigin> contractOriginExist = contractOriginMapper.selectByExample(contractOriginExample);
            if (CollectionUtils.isNotEmpty(contractOriginExist)) {
                contractOrigin.setId(contractOriginExist.get(0).getId());
                contractOriginMapper.updateByPrimaryKey(contractOrigin);
            } else {
                contractOriginMapper.insert(contractOrigin);
            }
            return 1;
        }

        //批量插入
        List<List<ContractOrigin>> lists = ListUtils.splistList(contractOriginList, 1000);
        for (List<ContractOrigin> list : lists) {
            contractExtMapper.batchInsertContractOrigin(list);
        }
        return contractList.size();
    }

    @Override
    public Boolean statusCheck(Long id, String operationType) {
        Contract contract = contractMapper.selectByPrimaryKey(id);
        Guard.notNull(contract, "合同不存在");
        if (Objects.equals(operationType, "change")) {
            //如果合同关联了项目，项目结项了也不允许再对合同操作
            List<ContractVO> subContracts = getListByParentId(contract.getId());
            for (ContractVO subContract : subContracts) {
                // 项目信息
                ProjectContractRsExample projectContractRsExample = new ProjectContractRsExample();
                final ProjectContractRsExample.Criteria pcrCriteria = projectContractRsExample.createCriteria();
                pcrCriteria.andContractIdEqualTo(subContract.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                projectContractRsExample.setOrderByClause("create_at desc");
                final List<ProjectContractRs> projectContractRsList = projectContractRsService.selectByExample(projectContractRsExample);
                if (CollectionUtils.isNotEmpty(projectContractRsList)) {
                    final ProjectContractRs projectContractRs = projectContractRsList.get(0);
                    final Long projectId = projectContractRs.getProjectId();
                    final Project project = projectService.selectByPrimaryKey(projectId);
                    if (Objects.equals(ProjectStatus.CLOSE.getCode(), project.getStatus())) {
                        throw new BizException(Code.ERROR, "合同所关联的项目已结项，不允许发起合同变更或终止。");
                    }
                }
            }
            //对已生效的合同的能否发起终止、变更等校验。 对某一个合同用户可能多tab打开且提交了流程，其他tab卡在详情页，按钮仍可用
            if (!Objects.equals(ContractStatus.EFFECTIVE.getCode(), contract.getStatus())) {
                throw new BizException(Code.ERROR, "合同状态不为生效，不允许发起合同变更或终止。");
            }
        } else {
            //合同新建场景 -  上一步、下一步的校验。（前端提示）
            if (Objects.equals(ContractStatus.PENDING.getCode(), contract.getStatus()) || Objects.equals(ContractStatus.CHANGING.getCode(), contract.getStatus()) || Objects.equals(ContractStatus.EFFECTIVE.getCode(), contract.getStatus())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public Boolean changeStatusCheck(Long id, String operationType, Long changeHeaderId) {
        if (Objects.nonNull(changeHeaderId)) {
            ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(changeHeaderId);
            if (Objects.equals(ContractChangewayStatus.PENDING.getCode(), contractChangeway.getApprovalStatus()) || Objects.equals(ContractChangewayStatus.APPROVED.getCode(), contractChangeway.getApprovalStatus())) {
                return false;
            }
        }
        Contract contract = contractMapper.selectByPrimaryKey(id);
        if (Objects.equals(ContractStatus.PENDING.getCode(), contract.getStatus()) || Objects.equals(ContractStatus.CHANGING.getCode(), contract.getStatus())) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean updateContractStatus(Long id, Integer status) {
        Contract contract = contractMapper.selectByPrimaryKey(id);
        if (!Objects.equals(ContractStatus.EFFECTIVE.getCode(), contract.getStatus()) && !Objects.equals(ContractStatus.EFFECTIVE.getCode(), status)) {
            contract.setStatus(status);
            contractMapper.updateByPrimaryKeySelective(contract);
            return true;
        }
        return false;
    }

    @Override
    public Integer updateContractAndChangeway(Long id, String type) {
        ContractChangeway contractChangeway = contractChangewayMapper.selectByPrimaryKey(id);
        contractChangeway.setApprovalStatus(ContractChangewayStatus.PENDING.getCode());
        contractChangewayMapper.updateByPrimaryKey(contractChangeway);
        Integer contractStatus = null;
        if (Objects.equals(type, GlegalContractEnums.CONTRACT_MODIFY.getCode())) {
            contractStatus = ContractStatus.CHANGING.getCode();
        } else if (Objects.equals(type, GlegalContractEnums.CONTRACT_TERMINATION.getCode())) {
            contractStatus = ContractStatus.TERMINATIONING.getCode();
        } else {
            return 0;
        }
        return contractExtMapper.updateContractByChange(contractStatus, null, contractChangeway.getContractId());
    }

}