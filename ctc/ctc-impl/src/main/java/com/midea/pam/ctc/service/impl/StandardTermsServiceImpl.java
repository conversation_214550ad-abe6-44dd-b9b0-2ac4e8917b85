package com.midea.pam.ctc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.StandardTermsContentDto;
import com.midea.pam.common.ctc.dto.StandardTermsDto;
import com.midea.pam.common.ctc.entity.CtcAttachment;
import com.midea.pam.common.ctc.entity.CtcAttachmentExample;
import com.midea.pam.common.ctc.entity.StandardTerms;
import com.midea.pam.common.ctc.entity.StandardTermsContent;
import com.midea.pam.common.ctc.entity.StandardTermsContentExample;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.StandardTermsEnum;
import com.midea.pam.ctc.common.enums.StandardTermsScopeApplicationEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.CtcAttachmentMapper;
import com.midea.pam.ctc.mapper.StandardTermsContentMapper;
import com.midea.pam.ctc.mapper.StandardTermsMapper;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.StandardTermsService;
import com.midea.pam.system.SystemContext;
import com.mideaframework.commons.core.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class StandardTermsServiceImpl implements StandardTermsService {

    @Resource
    private StandardTermsMapper standardTermsMapper;
    @Resource
    private StandardTermsContentMapper standardTermsContentMapper;
    @Resource
    private CtcAttachmentMapper ctcAttachmentMapper;

    @Resource
    CtcAttachmentService ctcAttachmentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveInfo(StandardTermsDto dto, Integer status) {
        Long id = dto.getId();
        //校验标准条款内容不能有相同的key
        List<StandardTermsContentDto> standardTermsContentList = dto.getStandardTermsContentList();
        if (CollectionUtils.isNotEmpty(standardTermsContentList)) {
            List<StandardTermsContentDto> filteredList = standardTermsContentList.stream()
                    .filter(t -> !Boolean.FALSE.equals(t.getDeletedFlag()) || null == t.getDeletedFlag())
                    .collect(Collectors.toList());

            Map<String, Long> keyCountMap = filteredList.stream()
                    .collect(Collectors.groupingBy(
                            StandardTermsContentDto::getTextBoxKey,
                            Collectors.counting()
                    ));

            List<String> duplicateKeys = keyCountMap.entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            if (!duplicateKeys.isEmpty()) {
                throw new BizException(Code.ERROR, "textBoxKey重复: " + duplicateKeys);
            }
        }

        //如果ID存在则编辑操作，否则是新增
        if (null != id) {
            StandardTerms standardTerms = standardTermsMapper.selectByPrimaryKey(id);
            if (null != standardTerms) {
                standardTerms.setTermsName(dto.getTermsName());
                standardTerms.setTermsDisplayContent(dto.getTermsDisplayContent());
                standardTerms.setAttachIdListStr(dto.getAttachIdListStr());
                standardTerms.setScopeApplication(dto.getScopeApplication());
                standardTerms.setRemark(dto.getRemark());
                standardTerms.setUpdateAt(new Date());
                standardTerms.setUpdateBy(SystemContext.getUserId());
                UserInfo userInfo = CacheDataUtils.findUserById(SystemContext.getUserId());
                standardTerms.setUpdateByName(userInfo.getName());
                if (CollectionUtils.isNotEmpty(dto.getCtcAttachmentList())){
                    List<CtcAttachmentDto> ctcAttachmentList = dto.getCtcAttachmentList();
                    String attachIds = ctcAttachmentList.stream()
                            .map(CtcAttachmentDto::getAttachId)
                            .map(Object::toString)
                            .collect(Collectors.joining(","));
                    standardTerms.setAttachIdListStr(attachIds);
                }
                standardTermsMapper.updateByPrimaryKeyWithBLOBs(standardTerms);
                if (CollectionUtils.isNotEmpty(standardTermsContentList)) {
                    for (StandardTermsContentDto standardTermsContentDto : standardTermsContentList) {
                        if (Boolean.TRUE.equals(standardTermsContentDto.getDeletedFlag()) && null != standardTermsContentDto.getId()) {
                            //删除操作
                            Long contentDtoId = standardTermsContentDto.getId();
                            StandardTermsContent standardTermsContent = standardTermsContentMapper.selectByPrimaryKey(contentDtoId);
                            if (null != standardTermsContent) {
                                standardTermsContent.setDeletedFlag(true);
                                standardTermsContentMapper.updateByPrimaryKey(standardTermsContent);
                            }
                        } else if (null != standardTermsContentDto.getId()) {
                            //修改操作
                            Long contentDtoId = standardTermsContentDto.getId();
                            StandardTermsContent standardTermsContent = standardTermsContentMapper.selectByPrimaryKey(contentDtoId);
                            if (null != standardTermsContent) {
                                standardTermsContent.setTextBoxKey(standardTermsContentDto.getTextBoxKey());
                                standardTermsContent.setTextBoxTitle(standardTermsContentDto.getTextBoxTitle());
                                standardTermsContent.setDefaultValue(standardTermsContentDto.getDefaultValue());
                                standardTermsContent.setRemark(standardTermsContentDto.getRemark());
                                standardTermsContent.setUpdateAt(new Date());
                                standardTermsContent.setUpdateBy(SystemContext.getUserId());
                                standardTermsContentMapper.updateByPrimaryKey(standardTermsContent);
                            }
                        } else if (null == standardTermsContentDto.getId()) {
                            StandardTermsContent standardTermsContent = new StandardTermsContent();
                            standardTermsContent.setAssociationTermsId(dto.getId());
                            standardTermsContent.setTextBoxKey(standardTermsContentDto.getTextBoxKey());
                            standardTermsContent.setTextBoxTitle(standardTermsContentDto.getTextBoxTitle());
                            standardTermsContent.setDefaultValue(standardTermsContentDto.getDefaultValue());
                            standardTermsContent.setRemark(standardTermsContentDto.getRemark());
                            standardTermsContent.setDeletedFlag(false);
                            standardTermsContentMapper.insert(standardTermsContent);
                        }
                    }
                }

                //维护附件信息
                updateCtcAttachmentInfo(dto, standardTerms);
            }
            return id;
        }else {
            StandardTerms standardTerms = new StandardTerms();
            //CGTK+2位年份+2位月份+2位日期+3位流水号
            String termsCode = CodePrefix.STANDARD_TERMS.code() + CacheDataUtils.generateSequence(Constants.CODE_ORDER_NUM_LENGTH,
                    CodePrefix.STANDARD_TERMS.code(), DateUtil.DATE_YYMMDD_PATTERN);
            standardTerms.setTermsCode(termsCode);
            standardTerms.setTermsName(dto.getTermsName());
            standardTerms.setTermsStatus(status);
            standardTerms.setTermsDisplayContent(dto.getTermsDisplayContent());
            standardTerms.setScopeApplication(dto.getScopeApplication());
            //保存附件信息
            if (CollectionUtils.isNotEmpty(dto.getCtcAttachmentList())){
                List<CtcAttachmentDto> ctcAttachmentList = dto.getCtcAttachmentList();
                String attachIds = ctcAttachmentList.stream()
                        .map(CtcAttachmentDto::getAttachId)
                        .map(Object::toString)
                        .collect(Collectors.joining(","));
                standardTerms.setAttachIdListStr(attachIds);
            }
            standardTerms.setRemark(dto.getRemark());
            standardTerms.setDeletedFlag(false);
            Long userId = SystemContext.getUserId();
            UserInfo userInfo = CacheDataUtils.findUserById(userId);
            standardTerms.setCreateByName(userInfo.getName());
            standardTerms.setUpdateAt(new Date());
            standardTerms.setUpdateBy(userId);
            standardTerms.setUpdateByName(userInfo.getName());
            standardTerms.setUnitId(SystemContext.getUnitId());
            standardTermsMapper.insert(standardTerms);

            if (CollectionUtils.isNotEmpty(standardTermsContentList)) {
                for (StandardTermsContentDto standardTermsContentDto : standardTermsContentList) {
                    StandardTermsContent standardTermsContent = new StandardTermsContent();
                    standardTermsContent.setAssociationTermsId(standardTerms.getId());
                    standardTermsContent.setTextBoxKey(standardTermsContentDto.getTextBoxKey());
                    standardTermsContent.setTextBoxTitle(standardTermsContentDto.getTextBoxTitle());
                    standardTermsContent.setDefaultValue(standardTermsContentDto.getDefaultValue());
                    standardTermsContent.setRemark(standardTermsContentDto.getRemark());
                    standardTermsContent.setDeletedFlag(false);
                    standardTermsContentMapper.insert(standardTermsContent);
                }
            }
            //处理文件信息
            updateCtcAttachmentInfo(dto, standardTerms);
            return standardTerms.getId();
        }
    }

    private void updateCtcAttachmentInfo(StandardTermsDto dto, StandardTerms standardTerms) {
        // 先全部删除附件,再上传
        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
        ctcAttachmentDto.setModule(CtcAttachmentModule.STANDARD_TERMS.code());
        ctcAttachmentDto.setModuleId(standardTerms.getId());
        ctcAttachmentService.deleteByModule(ctcAttachmentDto);

        List<CtcAttachmentDto> ctcAttachmentList = dto.getCtcAttachmentList();
        if (CollectionUtils.isNotEmpty(ctcAttachmentList)){
            for (CtcAttachmentDto attachmentDto : ctcAttachmentList) {
                attachmentDto.setModuleId(standardTerms.getId());
                attachmentDto.setModule(CtcAttachmentModule.STANDARD_TERMS.code());
                attachmentDto.setDeletedFlag(Optional.ofNullable(attachmentDto.getDeletedFlag()).orElse(DeletedFlag.VALID.code()));
                attachmentDto.setId(null);
                ctcAttachmentMapper.insertSelective(attachmentDto);
            }
        }

    }

    @Override
    public StandardTermsDto findInfoRuleById(Long id) {
        StandardTerms standardTerms = standardTermsMapper.selectByPrimaryKey(id);
        StandardTermsDto standardTermsDto = new StandardTermsDto();
        if (null != standardTerms) {
            BeanConverter.copy(standardTerms, standardTermsDto);
            StandardTermsContentExample contentExample = new StandardTermsContentExample();
            StandardTermsContentExample.Criteria contentCriteria = contentExample.createCriteria();
            contentCriteria.andAssociationTermsIdEqualTo(id).andDeletedFlagEqualTo(false);
            List<StandardTermsContent> standardTermsContents = standardTermsContentMapper.selectByExample(contentExample);
            if (CollectionUtils.isNotEmpty(standardTermsContents)) {
                List<StandardTermsContentDto> contentDtoList = standardTermsContents.stream()
                        .map(standardTermsContent -> {
                            StandardTermsContentDto dto = new StandardTermsContentDto();
                            BeanConverter.copy(standardTermsContent, dto);
                            return dto;
                        })
                        .sorted(Comparator.comparing(StandardTermsContentDto::getCreateAt)) // 按 createAt 升序
                        .collect(Collectors.toList());

                standardTermsDto.setStandardTermsContentList(contentDtoList);
            }

            //获取附件信息
            CtcAttachmentExample ctcAttachmentExample = new CtcAttachmentExample();
            CtcAttachmentExample.Criteria ctcCriteria = ctcAttachmentExample.createCriteria();
            ctcCriteria.andModuleIdEqualTo(id).andDeletedFlagEqualTo(false);
            List<CtcAttachment> ctcAttachments = ctcAttachmentMapper.selectByExample(ctcAttachmentExample);
            if (CollectionUtils.isNotEmpty(ctcAttachments)) {
                List<CtcAttachmentDto> ctcAttachmentDtoList = new ArrayList<>();
                for (CtcAttachment ctcAttachment : ctcAttachments) {
                    CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
                    BeanConverter.copy(ctcAttachment, ctcAttachmentDto);
                    UserInfo user = CacheDataUtils.findUserById(ctcAttachment.getCreateBy());
                    if (user != null) {
                        ctcAttachmentDto.setCreateUserName(user.getName());
                    }
                    ctcAttachmentDtoList.add(ctcAttachmentDto);
                }
                standardTermsDto.setCtcAttachmentList(ctcAttachmentDtoList);
            }
            return standardTermsDto;
        }
        return standardTermsDto;
    }

    @Override
    public ResponseMap getMobileStandardTermsDetail(Long id) {

        ResponseMap responseMap = new ResponseMap();

        StandardTerms standardTerms = standardTermsMapper.selectByPrimaryKey(id);
        if (null == standardTerms) {
            responseMap.setStatus("fail");
            responseMap.setMsg("未查询到标准条款内容内容");
            return responseMap;
        }
        //条款信息
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("termsName",standardTerms.getTermsName());
        String scopeApplication = standardTerms.getScopeApplication();
        if (StringUtils.isNotBlank(scopeApplication)){
            String nameByCode = StandardTermsScopeApplicationEnum.getNameByCode(scopeApplication);
            headMap.put("scopeApplication",nameByCode);
        }
        headMap.put("remark",standardTerms.getRemark());
        headMap.put("termsDisplayContent",standardTerms.getTermsDisplayContent());
        responseMap.setHeadMap(headMap);

        //附件
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(CtcAttachmentModule.STANDARD_TERMS.code());
        attachmentQuery.setModuleId(id);
        List<CtcAttachmentDto> attachmentList = ctcAttachmentService.selectList(attachmentQuery);
        if (CollectionUtil.isNotEmpty(attachmentList)) {
            List<AduitAtta> fileList = new ArrayList<>();
            attachmentList.forEach(attachment -> {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(String.valueOf(attachment.getAttachId()));
                String fileName = attachment.getFileName() == null ? attachment.getAttachName() : attachment.getFileName();
                aduitAtta.setFileName(fileName);
                aduitAtta.setFileSize(String.valueOf(attachment.getFileSize()));
                fileList.add(aduitAtta);
            });
            responseMap.setFileList(fileList);
        }
        responseMap.setStatus("success");
        return responseMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long discard(StandardTermsDto dto) {
        Long id = dto.getId();
        //弃用操作
        if ( null != id) {
            StandardTerms standardTerms = standardTermsMapper.selectByPrimaryKey(id);
            if (null != standardTerms) {
                standardTerms.setTermsStatus(StandardTermsEnum.DEPRECATED.getCode());
                standardTerms.setUpdateAt(new Date());
                standardTerms.setUpdateBy(SystemContext.getUserId());
                UserInfo userInfo = CacheDataUtils.findUserById(SystemContext.getUserId());
                standardTerms.setUpdateByName(userInfo.getName());
                standardTermsMapper.updateByPrimaryKey(standardTerms);
                return id;
            }
        }
        return id;
    }
}
