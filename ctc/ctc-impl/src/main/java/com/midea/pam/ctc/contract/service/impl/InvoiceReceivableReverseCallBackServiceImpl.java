package com.midea.pam.ctc.contract.service.impl;

import com.midea.pam.common.ctc.dto.InvoiceApplyDetailSplitDTO;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.ctc.dto.WorkflowCallbackCommonDto;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetailSplit;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetailSplitHistory;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetailSplitHistoryExample;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetails;
import com.midea.pam.common.ctc.entity.InvoiceApplyHeader;
import com.midea.pam.common.ctc.entity.InvoiceApplyHeaderExample;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.RdmSettlementSheetDetail;
import com.midea.pam.common.ctc.entity.RdmSettlementSheetDetailExample;
import com.midea.pam.common.ctc.entity.WorkingHour;
import com.midea.pam.common.ctc.entity.WorkingHourExample;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.InvoiceApplyStatusEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.InvoiceReceivableReverseCallBackService;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailSplitHistoryMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailSplitMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailsExtMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailsMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyHeaderMapper;
import com.midea.pam.ctc.mapper.InvoiceReceivableMapper;
import com.midea.pam.ctc.mapper.RdmSettlementSheetDetailMapper;
import com.midea.pam.ctc.mapper.WorkingHourMapper;
import com.midea.pam.ctc.service.CrmExtService;
import com.midea.pam.ctc.service.InvoiceApplyService;
import com.midea.pam.ctc.service.InvoiceReceivableService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-03-13
 * @description 应收发票红冲流程回调
 */
public class InvoiceReceivableReverseCallBackServiceImpl implements InvoiceReceivableReverseCallBackService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceReceivableReverseCallBackServiceImpl.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private WorkflowCallbackService workflowCallbackService;
    @Resource
    private InvoiceReceivableMapper invoiceReceivableMapper;
    @Resource
    private InvoiceReceivableService invoiceReceivableService;
    @Resource
    private InvoiceApplyService invoiceApplyService;
    @Resource
    private InvoiceApplyHeaderMapper invoiceApplyHeaderMapper;
    @Resource
    private InvoiceApplyDetailSplitMapper invoiceApplyDetailSplitMapper;
    @Resource
    private InvoiceApplyDetailSplitHistoryMapper invoiceApplyDetailSplitHistoryMapper;
    @Resource
    private InvoiceApplyDetailsMapper invoiceApplyDetailsMapper;
    @Resource
    private InvoiceApplyDetailsExtMapper invoiceApplyDetailsExtMapper;
    @Resource
    private RdmSettlementSheetDetailMapper rdmSettlementSheetDetailMapper;
    @Resource
    private WorkingHourMapper workingHourMapper;
    @Resource
    OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private CrmExtService crmExtService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftSubmit(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        String handlerId = workflowCallbackCommonDto.getHandlerId();
        Long companyId = workflowCallbackCommonDto.getCompanyId();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        logger.info("应收发票红冲提交审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("InvoiceReceivableReverseCallBack_draftSubmit_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }
                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);
                    // 更新记录状态
                    changeInvoiceReceivableStatus(formInstanceId, "审批中");
                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("应收发票红冲提交审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("应收发票红冲提交审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("应收发票红冲提交审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Override
    public void approved(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        String handlerId = workflowCallbackCommonDto.getHandlerId();
        Long companyId = workflowCallbackCommonDto.getCompanyId();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        logger.info("应收发票红冲审批通过回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("InvoiceReceivableReverseCallBack_draftSubmit_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }
                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    InvoiceReceivableDto invoiceReceivable = invoiceReceivableService.reverseInvoiceView(formInstanceId);
                    Guard.notNull(invoiceReceivable, "应收发票红冲审批回调接口失败，formInstanceId对应的发票记录不存在，不处理");

                    //生成开票申请
                    InvoiceApplyHeader invoiceApplyHeader = new InvoiceApplyHeader();
                    invoiceReceivableService.generateInvoiceApply(invoiceReceivable, invoiceApplyHeader);
                    // 更新记录状态等
                    invoiceReceivable.setApplyCode(invoiceApplyHeader.getApplyCode());//开票申请编号
                    invoiceReceivable.setApplyHeaderId(invoiceApplyHeader.getId());
                    invoiceReceivable.setStatus("审批通过");
                    invoiceReceivableMapper.updateByPrimaryKeySelective(invoiceReceivable);

                    //  插入开票申请行拆分明细正式表
                    for (InvoiceApplyDetailSplitDTO applyDetailSplitDTO : invoiceReceivable.getApplyDetailSplitList()) {
                        invoiceApplyDetailSplitMapper.insert(applyDetailSplitDTO);
                        //更新开票计划实际开票金额
                        InvoiceApplyDetails invoiceApplyDetails = invoiceApplyDetailsMapper.selectByPrimaryKey(applyDetailSplitDTO.getApplyDetailId());
                        if (invoiceApplyDetails != null) {
                            invoiceApplyDetailsExtMapper.updateInvoicePlanPrice(invoiceApplyDetails.getCode());
                            //把对应工时上的开票申请ID去掉
                            updateWoringHouder(invoiceReceivable.getOldApplyCode());
                        }
                    }
                    Long oldHeaderId = invoiceApplyHeader.getOldHeaderId();
                    Assert.notNull(oldHeaderId, "原开票申请id为空");
                    //如果是关联交易 开票状态同步EAM
                    InvoiceApplyHeader header = invoiceApplyHeaderMapper.selectByPrimaryKey(oldHeaderId);
                    if (Objects.nonNull(header) && StringUtils.isNotBlank(header.getEamPurchaseCode())) {
                        header.setEamStatus(InvoiceApplyStatusEnum.HEADER_WRITEOFF.getCode());
                        invoiceApplyService.updateEamInvoiceStatus(header);
                    }

                    //检查是否存在配置，再检查客户
                    Long ouId = invoiceReceivable.getOuId();
                    List<String> autoPushMif = new ArrayList<>(organizationCustomDictService.queryByName("是否自动推送开票平台", ouId, OrgCustomDictOrgFrom.OU));
                    if (autoPushMif.isEmpty()) {
                        throw new BizException(Code.ERROR, "组织参数【是否自动推送开票平台】未配置");
                    }
                    // 判断是否自动推送,只有不自动推送且是国外客户才【不主动推送】
                    boolean shouldPush = !("0".equals(autoPushMif.get(0)) && invoiceReceivableService.queryCustomerIsAbroad(invoiceReceivable.getCustomerCode()));
                    if (shouldPush) {
                        //红冲发票写入美的开票平台
                        invoiceReceivableService.pushRedInvoiceToMif(invoiceReceivable.getId());
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("应收发票红冲审批通过回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("应收发票红冲审批通过回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("应收发票红冲审批通过回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refused(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        String handlerId = workflowCallbackCommonDto.getHandlerId();
        Long companyId = workflowCallbackCommonDto.getCompanyId();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        logger.info("应收发票红冲驳回审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("InvoiceReceivableReverseCallBack_refuse_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }
                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);
                    // 更新记录状态
                    changeInvoiceReceivableStatus(formInstanceId, "驳回");
                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("应收发票红冲驳回审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("应收发票红冲驳回审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("应收发票红冲驳回审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftReturn(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        String handlerId = workflowCallbackCommonDto.getHandlerId();
        Long companyId = workflowCallbackCommonDto.getCompanyId();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        logger.info("应收发票红冲撤回审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("InvoiceReceivableReverseCallBack_draftReturn_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }
                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);
                    // 更新记录状态
                    changeInvoiceReceivableStatus(formInstanceId, "草稿");
                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("应收发票红冲撤回审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("应收发票红冲撤回审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("应收发票红冲撤回审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void abandon(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        String handlerId = workflowCallbackCommonDto.getHandlerId();
        Long companyId = workflowCallbackCommonDto.getCompanyId();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        logger.info("应收发票红冲废弃审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("InvoiceReceivableReverseCallBack_abandon_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }
                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);
                    // 更新记录状态并释放金额
                    changeStatusAndAmount(formInstanceId, "作废");
                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("应收发票红冲废弃审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("应收发票红冲废弃审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("应收发票红冲废弃审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        String handlerId = workflowCallbackCommonDto.getHandlerId();
        Long companyId = workflowCallbackCommonDto.getCompanyId();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        logger.info("应收发票红冲删除审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("InvoiceReceivableReverseCallBack_delete_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }
                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);
                    // 更新记录状态并释放金额
                    changeStatusAndAmount(formInstanceId, "作废");
                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("应收发票红冲删除审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("应收发票红冲删除审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("应收发票红冲删除审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    private void changeInvoiceReceivableStatus(Long id, String status) {
        InvoiceReceivable invoiceReceivable = new InvoiceReceivable();
        invoiceReceivable.setId(id);
        invoiceReceivable.setStatus(status);
        invoiceReceivableMapper.updateByPrimaryKeySelective(invoiceReceivable);
    }

    private void changeStatusAndAmount(Long id, String status) {
        changeInvoiceReceivableStatus(id,status);
        //释放金额
        InvoiceApplyDetailSplitHistoryExample historyExample = new InvoiceApplyDetailSplitHistoryExample();
        historyExample.createCriteria()
                .andInvoiceReceivableIdEqualTo(id)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<InvoiceApplyDetailSplitHistory> invoiceApplyDetailSplitHistories = invoiceApplyDetailSplitHistoryMapper.selectByExample(historyExample);
        if (ListUtils.isNotEmpty(invoiceApplyDetailSplitHistories)){
            for (InvoiceApplyDetailSplitHistory invoiceApplyDetailSplitHistory : invoiceApplyDetailSplitHistories) {
                //恢复原开票申请行拆分明细表的金额
                InvoiceApplyDetailSplit oldApplyDetailSplit = invoiceApplyDetailSplitMapper.selectByPrimaryKey(invoiceApplyDetailSplitHistory.getOldSplitId());
                oldApplyDetailSplit.setRemainTaxIncludedPrice(oldApplyDetailSplit.getRemainTaxIncludedPrice().subtract(invoiceApplyDetailSplitHistory.getTaxIncludedPrice()));
                oldApplyDetailSplit.setRemainExclusiveOfTax(oldApplyDetailSplit.getRemainExclusiveOfTax().subtract(invoiceApplyDetailSplitHistory.getExclusiveOfTax()));
                oldApplyDetailSplit.setRemainQuantity(oldApplyDetailSplit.getRemainQuantity().subtract(invoiceApplyDetailSplitHistory.getQuantity()));
                invoiceApplyDetailSplitMapper.updateByPrimaryKey(oldApplyDetailSplit);

                //删除拆分历史明细
                invoiceApplyDetailSplitHistory.setDeletedFlag(Boolean.TRUE);
                invoiceApplyDetailSplitHistoryMapper.updateByPrimaryKey(invoiceApplyDetailSplitHistory);
            }
        }
    }

    private void updateWoringHouder(String applyCode) {
        if (applyCode != null) {
            InvoiceApplyHeaderExample invoiceApplyHeaderExample = new InvoiceApplyHeaderExample();
            invoiceApplyHeaderExample.createCriteria().andDeletedFlagEqualTo(false).andApplyCodeEqualTo(applyCode);
            List<InvoiceApplyHeader> invoiceApplyHeaders = invoiceApplyHeaderMapper.selectByExample(invoiceApplyHeaderExample);
            if (ListUtils.isNotEmpty(invoiceApplyHeaders)) {
                RdmSettlementSheetDetailExample rdmSettlementSheetDetailExample = new RdmSettlementSheetDetailExample();
                //判断是否来源RDM的单子
                if (invoiceApplyHeaders.get(0).getSettlementSheetId() != null) {
                    rdmSettlementSheetDetailExample.createCriteria().andSettlementIdEqualTo(invoiceApplyHeaders.get(0).getSettlementSheetId());
                    List<RdmSettlementSheetDetail> rdmSettlementSheetDetails = rdmSettlementSheetDetailMapper.selectByExample(rdmSettlementSheetDetailExample);
                    rdmSettlementSheetDetails.forEach(r -> {
                        WorkingHourExample workingHourExample = new WorkingHourExample();
                        workingHourExample.createCriteria().andUserMipEqualTo(r.getUsername()).andInvoiceApplyHeaderIdEqualTo(invoiceApplyHeaders.get(0).getId()).andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code());
                        List<WorkingHour> workingHours = workingHourMapper.selectByExample(workingHourExample);
                        workingHours.forEach(w -> {
                            w.setInvoiceApplyHeaderId(null);
                            workingHourMapper.updateByPrimaryKeySelective(w);
                        });
                    });
                }
            }
        }
    }


}
