package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDto;
import com.midea.pam.common.ctc.dto.PaymentRecordDto;
import com.midea.pam.common.ctc.dto.PaymentWriteOffRecordDto;
import com.midea.pam.common.ctc.dto.PrepayErpDto;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentRecord;
import com.midea.pam.common.ctc.entity.PaymentRecordExample;
import com.midea.pam.common.ctc.entity.PaymentWriteOffCancelRecord;
import com.midea.pam.common.ctc.entity.PaymentWriteOffRecord;
import com.midea.pam.common.ctc.entity.PaymentWriteOffRecordExample;
import com.midea.pam.common.ctc.entity.PositiveNegativeInvoiceRecord;
import com.midea.pam.common.ctc.entity.PositiveNegativeInvoiceRecordExample;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.entity.ResendExecuteExample;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.GlPeriodType;
import com.midea.pam.common.enums.PaymentApplyGcebErpStatus;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.PaymentRecordStatus;
import com.midea.pam.ctc.common.enums.PaymentWriteOffRecordCancelStatus;
import com.midea.pam.ctc.common.enums.PaymentWriteOffRecordStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.ext.service.impl.PositiveAndNegativeInvoiceEsbServiceImpl;
import com.midea.pam.ctc.mapper.PaymentApplyMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.PaymentRecordExtMapper;
import com.midea.pam.ctc.mapper.PaymentRecordMapper;
import com.midea.pam.ctc.mapper.PaymentWriteOffCancelRecordMapper;
import com.midea.pam.ctc.mapper.PaymentWriteOffRecordMapper;
import com.midea.pam.ctc.mapper.PositiveNegativeInvoiceRecordMapper;
import com.midea.pam.ctc.service.*;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

public class PaymentWriteOffRecordServiceImpl implements PaymentWriteOffRecordService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 5;//5分钟

    @Resource
    private PaymentInvoiceService paymentInvoiceService;
    @Resource
    private PaymentApplyService paymentApplyService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private PaymentApplyMapper paymentApplyMapper;
    @Resource
    private PaymentWriteOffRecordMapper writeOffRecordMapper;
    @Resource
    private PaymentRecordMapper paymentRecordMapper;
    @Resource
    private PaymentRecordService paymentRecordService;
    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;
    @Resource
    private EsbService esbService;
    @Resource
    private ResendExecuteService resendExecuteService;
    @Resource
    private PaymentWriteOffCancelRecordMapper writeOffCancelRecordMapper;

    @Resource
    private PositiveNegativeInvoiceRecordMapper positiveNegativeInvoiceRecordMapper;

    @Resource
    private PositiveAndNegativeInvoiceEsbServiceImpl invoiceEsbService;

    @Resource
    private PaymentRecordExtMapper paymentRecordExtMapper;

    @Resource
    private SdpService sdpService;


    @Override
    public List<PaymentWriteOffRecordDto> selectList(PaymentWriteOffRecordDto query) {
        List<PaymentWriteOffRecord> recordList = writeOffRecordMapper.selectByExample(buildExample(query));
        List<PaymentWriteOffRecordDto> writeOffRecordDtoList = BeanConverter.copy(recordList, PaymentWriteOffRecordDto.class);
        for (PaymentWriteOffRecordDto dto : writeOffRecordDtoList) {
            PaymentInvoice paymentInvoice = paymentInvoiceService.getById(dto.getPaymentInvoiceId());
            if (paymentInvoice != null) {
                dto.setTotalInvoiceIncludedPrice(paymentInvoice.getTotalInvoiceIncludedPrice());
            }
        }
        return writeOffRecordDtoList;
    }

    public PaymentWriteOffRecordExample buildExample(PaymentWriteOffRecordDto query) {
        PaymentWriteOffRecordExample example = new PaymentWriteOffRecordExample();
        example.setOrderByClause("create_at desc");
        PaymentWriteOffRecordExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        if (query.getPaymentRecordId() != null) {
            criteria.andPaymentRecordIdEqualTo(query.getPaymentRecordId());
        }
        return example;
    }

    @Override
    public int submit(PaymentWriteOffRecord[] writeOffRecords, Long userId) {
        if (null == writeOffRecords || writeOffRecords.length == 0) {
            throw new BizException(Code.ERROR, "核销对象列表不能为空");
        }
        //付款申请单
        Long paymentApplyId = writeOffRecords[0].getPaymentApplyId();
        if (null == paymentApplyId) {
            PaymentRecord record = paymentRecordService.getById(writeOffRecords[0].getPaymentRecordId());
            paymentApplyId = record.getPaymentApplyId();
        }
        PaymentApply paymentApply = paymentApplyService.findById(paymentApplyId);
        if (null == paymentApply) {
            throw new BizException(Code.ERROR, "付款申请单不能为空");
        }

        //核销逻辑
        for (PaymentWriteOffRecord writeOffRecord : writeOffRecords) {
            writeOffRecord(writeOffRecord, userId);
        }

        //更新预付款申请单核销状态
        paymentApply.setWriteOffStatus(2);//部分核销
        //判断预付款是否完全核销
        if (isPrepayAllForApply(paymentApply)) {
            paymentApply.setWriteOffStatus(3);
        }
        paymentApplyService.update(paymentApply);

        return 0;
    }

    /**
     * 判断预付款申请是否已经完全核销
     *
     * @param paymentApply
     * @return
     */
    public Boolean isPrepayAllForApply(PaymentApply paymentApply) {
        if (null == paymentApply) {
            return false;
        }
        if (!PaymentApplyGcebErpStatus.SUCCESS.getCode().equals(paymentApply.getGcebStatus())) {
            return false;
        }
        //根据预付款查询付款记录
        PaymentRecordExample recordExample = new PaymentRecordExample();
        recordExample.createCriteria().andDeletedFlagEqualTo(0).andPaymentApplyIdEqualTo(paymentApply.getId());
        List<PaymentRecord> recordList = paymentRecordMapper.selectByExample(recordExample);
        BigDecimal totalPrepayAmount = BigDecimal.valueOf(0.00);
        for (PaymentRecord record : recordList) {
            if (PaymentRecordStatus.DONE.equals(record.getPaymentStatus())) {
                totalPrepayAmount.add(record.getWriteOffAmount());
            }
        }
        return totalPrepayAmount.compareTo(paymentApply.getReallyPayIncludedPrice()) == 0;
    }

    public void writeOffRecord(PaymentWriteOffRecord writeOffRecord, Long userId) {
        //查询核销记录表
        if (null == writeOffRecord) {
            throw new BizException(Code.ERROR, "核销记录不能为空");
        }
        if (Objects.isNull(writeOffRecord.getAmount()) || writeOffRecord.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizException(Code.ERROR, "核销金额有误");
        }
        //ap发票
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(writeOffRecord.getPaymentInvoiceId());
        if (null == paymentInvoice) {
            throw new BizException(Code.ERROR, "发票不能为空");
        }
        BigDecimal surplusAmount = paymentInvoice.getSurplusAmount();//发票剩余可用金额
        if (1 != paymentInvoice.getErpStatus()) {
            throw new BizException(Code.ERROR, "AP发票未同步");
        }
        if (paymentInvoice.getAvailable() != null && 1 != paymentInvoice.getAvailable()) {
            throw new BizException(Code.ERROR, "AP发票已被占用");
        }
        if (surplusAmount.compareTo(writeOffRecord.getAmount()) < 0) {
            throw new BizException(Code.ERROR, "发票可用金额不足");
        }
        //付款记录
        PaymentRecord record = paymentRecordService.getById(writeOffRecord.getPaymentRecordId());
        if (null == record) {
            throw new BizException(Code.ERROR, "付款记录不能为空");
        }
        if (record.getAmount().compareTo(record.getWriteOffAmount().add(writeOffRecord.getAmount())) < 0) {
            throw new BizException(Code.ERROR, "本次核销金额不能大于预付款剩余核销金额");
        }
        //todo
        Long ouId = paymentInvoice.getOuId();
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
        if (ListUtils.isEmpty(organizationRelList)) {
            throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
        }
        OrganizationRelDto organizationRelDto = organizationRelList.get(0);
        Long ledgerId = organizationRelDto.getLedgerId();
        List<GlPeriodDto> glPeriodDtoList = basedataExtService.getGlPeriod(ledgerId, GlPeriodType.PAYMENT_PERIOD.getCode().toString(), DateUtils.format(new Date(), "yyyy-MM"));
        List<GlPeriodDto> glPeriodOpenList = basedataExtService.getGlPeriod2(ledgerId, GlPeriodType.PAYMENT_PERIOD.getCode().toString(), "O");
        if (ListUtils.isEmpty(glPeriodOpenList)) {
            throw new MipException("erp分类账为" + ledgerId + "，期间类型为" + GlPeriodType.PAYMENT_PERIOD.getName() + "【会计期间未打开】");
        }
        Date happenDate = null;
        GlPeriodDto glPeriodOpen = null;
        if (ListUtils.isNotEmpty(glPeriodOpenList)) {
            glPeriodOpenList.sort(Comparator.comparing(GlPeriodDto::getEndDate).reversed());
            glPeriodOpen = glPeriodOpenList.get(0);
        }
        if (ListUtils.isNotEmpty(glPeriodDtoList)) {
            GlPeriodDto dto = glPeriodDtoList.get(0);
            String status = dto.getClosingStatus();
            Date startDate = dto.getStartDate();
            Date endDate = dto.getEndDate();
            if ((new Date()).after(startDate) && (new Date()).before(DateUtils.addDay(endDate, 1)) && "O".equals(status)) {
                happenDate = DateUtils.parse(DateUtils.format(new Date(), "yyyy-MM-dd 00:00:00"));
            } else {
                happenDate = Optional.ofNullable(glPeriodOpen).map(GlPeriodDto::getEndDate).orElse(null);
            }
        }
        //更新ap发票可用余额
        paymentInvoice.setSurplusAmount(paymentInvoice.getSurplusAmount().subtract(writeOffRecord.getAmount()));
        paymentInvoice.setTotalPayIncludedPrice(paymentInvoice.getTotalPayIncludedPrice() == null ?
                BigDecimal.ZERO : paymentInvoice.getTotalPayIncludedPrice().add(writeOffRecord.getAmount()));
        paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);

        PaymentApply paymentApply = paymentApplyMapper.selectByPrimaryKey(record.getPaymentApplyId());
        Guard.notNull(paymentApply, String.format("付款申请单id：%s对应的付款申请不存在", record.getPaymentApplyId()));
        //插入核销记录，待同步
        writeOffRecord.setPaymentApplyId(paymentApply.getId());
        writeOffRecord.setPaymentApplyCode(paymentApply.getPaymentApplyCode());
        writeOffRecord.setApInvoiceCode(paymentInvoice.getApInvoiceCode());
        writeOffRecord.setDeletedFlag(DeletedFlag.VALID.code());
        writeOffRecord.setStatus(PaymentWriteOffRecordStatus.SYNCHRONIZING.code());
        //用户未选时取系统给予的核销日期
        if (ObjectUtils.isEmpty(writeOffRecord.getHappenDate())) {
            writeOffRecord.setHappenDate(happenDate);//设定核销日期
        }
        writeOffRecordMapper.insert(writeOffRecord);

        //增加核销金额（含在途）
        record.setWriteOffAmount(record.getWriteOffAmount().add(writeOffRecord.getAmount()));
        //核销状态(0未核销/3部分核销/4已核销)
        if (record.getWriteOffAmount().compareTo(BigDecimal.ZERO) == 0) {
            record.setPaymentStatus(PaymentRecordStatus.NOT.code());//未核销
        } else if (record.getWriteOffAmount().compareTo(BigDecimal.ZERO) > 0 && record.getWriteOffAmount().compareTo(record.getAmount()) < 0) {
            record.setPaymentStatus(PaymentRecordStatus.PART.code());//部分核销
        } else if (record.getWriteOffAmount().compareTo(record.getAmount()) == 0) {
            record.setPaymentStatus(PaymentRecordStatus.DONE.code());//已核销
        }
        paymentRecordMapper.updateByPrimaryKeySelective(record);

        //预付款核销记录写入
        HandleDispatcher.route(BusinessTypeEnums.PREPAY.getCode(), String.valueOf(writeOffRecord.getId()), null, null, false);
    }

    /**
     * 预付款撤销核销记录
     *
     * @param writeOffRecordId 核销对象id
     * @param happenDate       撤销入账日期
     * @return
     */
    @Override
    public int cancel(Long writeOffRecordId, Date happenDate) {
        PaymentWriteOffRecord writeOffRecord = writeOffRecordMapper.selectByPrimaryKey(writeOffRecordId);
        //查询核销记录表
        if (null == writeOffRecord) {
            throw new BizException(Code.ERROR, "核销记录不存在");
        }
        //'同步状态(-1同步失败/0未同步/1同步中/2已同步)，撤销状态(3撤销中/4已撤销/5撤销失败)'
        // 核销状态为撤销失败或者空，可以撤销
        if (!(StringUtils.isEmpty(writeOffRecord.getCancelStatus())
                || Objects.equals(writeOffRecord.getCancelStatus(), PaymentWriteOffRecordCancelStatus.FAIL.code()))) {
            throw new BizException(Code.ERROR, "核销记录当前状态不允许撤销");
        }
        //付款记录
        PaymentRecord record = paymentRecordService.getById(writeOffRecord.getPaymentRecordId());
        if (null == record) {
            throw new BizException(Code.ERROR, "付款记录不能为空");
        }
        //ap发票
        PaymentInvoiceDto invoiceDto = paymentInvoiceService.getById(writeOffRecord.getPaymentInvoiceId());
        //以下为撤销核销逻辑
        //同步失败（即没有核销成功），并且撤销日期为空，则可以进行撤销操作，无需调用接口，更新对应业务即可

        //撤销失败和已核销，需要调用接口(撤销核销金额为负)
        //核销记录更新状态
        Date cancelDate = happenDate != null ? happenDate : new Date();
        writeOffRecord.setCancelDate(cancelDate);
        writeOffRecord.setCancelStatus(PaymentWriteOffRecordCancelStatus.CANCELING.code());
        writeOffRecordMapper.updateByPrimaryKeySelective(writeOffRecord);

        //保存撤销核销记录
        PaymentWriteOffCancelRecord writeOffCancelRecord = new PaymentWriteOffCancelRecord();
        writeOffCancelRecord.setPaymentWriteOffRecordId(writeOffRecord.getId());
        writeOffCancelRecord.setStatus(writeOffRecord.getStatus());
        writeOffCancelRecord.setCancelStatus(PaymentWriteOffRecordCancelStatus.CANCELING.code());
        writeOffCancelRecord.setCancelDate(cancelDate);
        writeOffCancelRecord.setDeletedFlag(DeletedFlag.VALID.code());
        writeOffCancelRecordMapper.insert(writeOffCancelRecord);

        //撤销核销记录写入
        HandleDispatcher.route(BusinessTypeEnums.CANCEL_PREPAY.getCode(), String.valueOf(writeOffCancelRecord.getId()), null,
                null, false);

        return 0;
    }

    /**
     * 预付款删除核销记录
     *
     * @param writeOffRecordId 核销对象id
     * @param happenDate       撤销入账日期
     * @return
     */
    @Override
    public int delete(Long writeOffRecordId, Date happenDate) {
        String lockName = String.format("paymentWriteOffRecord_delete_%s", writeOffRecordId);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                PaymentWriteOffRecord writeOffRecord = writeOffRecordMapper.selectByPrimaryKey(writeOffRecordId);
                //查询核销记录表
                if (null == writeOffRecord) {
                    throw new BizException(Code.ERROR, "核销记录不存在");
                }
                if (Objects.equals(writeOffRecord.getDeletedFlag(), DeletedFlag.INVALID.code())) {
                    throw new BizException(Code.ERROR, "核销记录已被删除，请勿重复操作");
                }
                //'同步状态(-1同步失败/0未同步/1同步中/2已同步)，撤销状态(3撤销中/4已撤销/5撤销失败)'
                // 未同步，同步失败才可以删除核销记录
                if (!(Objects.equals(writeOffRecord.getStatus(), PaymentWriteOffRecordStatus.NOT_SYNCHRONIZED.code())
                        || Objects.equals(writeOffRecord.getStatus(), PaymentWriteOffRecordStatus.SYNCHRONIZATION_FAILED.code()))) {
                    throw new BizException(Code.ERROR, "核销记录当前状态不允许操作删除核销");
                }
                //付款记录
                PaymentRecord record = paymentRecordService.getById(writeOffRecord.getPaymentRecordId());
                if (null == record) {
                    throw new BizException(Code.ERROR, "付款记录不能为空");
                }
                //ap发票
                PaymentInvoiceDto invoiceDto = paymentInvoiceService.getById(writeOffRecord.getPaymentInvoiceId());

                //恢复发票占用金额
                invoiceDto.setSurplusAmount(invoiceDto.getSurplusAmount().add(writeOffRecord.getAmount()));
                invoiceDto.setTotalPayIncludedPrice(invoiceDto.getTotalPayIncludedPrice().subtract(writeOffRecord.getAmount()));
                paymentInvoiceService.update(invoiceDto);

                //恢复付款记录核销金额和状态
                record.setWriteOffAmount(record.getWriteOffAmount().subtract(writeOffRecord.getAmount()));
                if (record.getWriteOffAmount().compareTo(BigDecimal.ZERO) == 0) {
                    record.setPaymentStatus(PaymentRecordStatus.NOT.code());//未核销
                } else if (record.getWriteOffAmount().compareTo(BigDecimal.ZERO) > 0 && record.getWriteOffAmount().compareTo(record.getAmount()) < 0) {
                    record.setPaymentStatus(PaymentRecordStatus.PART.code());//部分核销
                } else if (record.getWriteOffAmount().compareTo(record.getAmount()) == 0) {
                    record.setPaymentStatus(PaymentRecordStatus.DONE.code());//已核销
                }
                paymentRecordService.save(record, SystemContext.getUserId());

                //删除核销记录
                writeOffRecord.setDeletedFlag(DeletedFlag.INVALID.code());
                writeOffRecordMapper.updateByPrimaryKeySelective(writeOffRecord);
            }
        } catch (Exception e) {
            logger.error("预付款删除核销记录出现异常", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
        return 0;
    }

    @Override
    public int resend(Long writeOffRecordId, Date happenDate) {
        PaymentWriteOffRecord writeOffRecord = writeOffRecordMapper.selectByPrimaryKey(writeOffRecordId);
        //查询核销记录表
        if (null == writeOffRecord) {
            throw new BizException(Code.ERROR, "核销记录不存在");
        }
        //'核销状态(-1核销失败)'才允许做重推
        if (!(PaymentWriteOffRecordStatus.SYNCHRONIZATION_FAILED.code().equals(writeOffRecord.getStatus())
                && writeOffRecord.getCancelDate() == null)) {
            throw new BizException(Code.ERROR, "核销记录当前状态不允许重新同步");
        }
        //付款记录
//        PaymentRecord record = paymentRecordService.getById(writeOffRecord.getPaymentRecordId());
//        if (null == record) {
//            throw new BizException(Code.ERROR, "付款记录不能为空");
//        }
        //核销记录更新状态
        writeOffRecord.setStatus(PaymentWriteOffRecordStatus.SYNCHRONIZING.code());
        if (happenDate != null) {
            //核销记录更新状态
            writeOffRecord.setHappenDate(happenDate);
        }
        writeOffRecordMapper.updateByPrimaryKeySelective(writeOffRecord);

        //修改待推送记录
        ResendExecuteExample executeExample = new ResendExecuteExample();
        executeExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andApplyNoEqualTo(String.valueOf(writeOffRecordId));
        List<ResendExecute> resendExecuteList = resendExecuteService.selectByExample(executeExample);
        if (resendExecuteList == null || resendExecuteList.size() <= 0) {
            throw new BizException(Code.ERROR, "核销对象接口参数不存在");
        }
        ResendExecute resend = resendExecuteList.get(0);
        resend.setStatus(CommonStatus.TODO.getCode());
        resendExecuteService.updateByPrimaryKeySelective(resend);

        //恢复付款记录核销金额和状态
//        record.setPaymentStatus(PaymentRecordStatus.DOING.code());//核销中
//        paymentRecordService.save(record, SystemContext.getUserId());

        return 0;
    }

    @Override
    public int positiveAndNegativeSubmit(PaymentWriteOffRecord[] writeOffRecords, Long userId) {
        if (null == writeOffRecords || writeOffRecords.length == 0) {
            throw new BizException(Code.ERROR, "核销对象列表不能为空");
        }

        Long paymentInvoiceId = writeOffRecords[0].getPaymentRecordId();
        BigDecimal submitAmount = Arrays.stream(writeOffRecords).map(PaymentWriteOffRecord::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal writeOffAmount = BigDecimal.ZERO;
        List<PaymentRecordDto> list = paymentRecordExtMapper.selectPositiveAndNegativeInvoice(paymentInvoiceId);
        if(ListUtils.isEmpty(list)){
            throw new MipException("负数发票不存在");
        }
        PaymentRecordDto paymentRecordDto = list.get(0);
        BigDecimal amount = Optional.ofNullable(paymentRecordDto.getAmount()).orElse(BigDecimal.ZERO);
        PositiveNegativeInvoiceRecordExample example = new PositiveNegativeInvoiceRecordExample();
        example.createCriteria().andNegPaymentInvoiceIdEqualTo(paymentInvoiceId).andDeletedFlagEqualTo(false);
        List<PositiveNegativeInvoiceRecord> positiveNegativeInvoiceRecords = positiveNegativeInvoiceRecordMapper.selectByExample(example);
        if(ListUtils.isNotEmpty(positiveNegativeInvoiceRecords)){
            writeOffAmount = positiveNegativeInvoiceRecords.stream().map(PositiveNegativeInvoiceRecord::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        }
        if(writeOffAmount.add(submitAmount).compareTo(amount.abs()) > 0){
            throw new BizException(Code.ERROR, "已核销发票总额不能超过应核销发票总额");
        }

        //核销逻辑
        for (PaymentWriteOffRecord writeOffRecord : writeOffRecords) {
            positiveAndNegativeRecord(writeOffRecord);
        }

        return 0;
    }

    @Override
    public int positiveAndNegativeResend(Long writeOffRecordId, Date happenDate) {
        PositiveNegativeInvoiceRecord record = positiveNegativeInvoiceRecordMapper.selectByPrimaryKey(writeOffRecordId);
        //查询核销记录表
        if (null == record) {
            throw new BizException(Code.ERROR, "正负发票核销记录不存在");
        }
        //'核销状态(-1核销失败)'才允许做重推
        if (!PaymentWriteOffRecordStatus.SYNCHRONIZATION_FAILED.code().equals(record.getSyncStatus())) {
            throw new BizException(Code.ERROR, "核销记录当前状态不允许重新同步");
        }
        //核销记录更新状态
        record.setSyncStatus(PaymentWriteOffRecordStatus.SYNCHRONIZING.code());
        if (happenDate != null) {
            //核销记录更新状态
            record.setHappenDate(happenDate);
        }
        positiveNegativeInvoiceRecordMapper.updateByPrimaryKeySelective(record);

        //修改待推送记录
        ResendExecuteExample executeExample = new ResendExecuteExample();
        executeExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andApplyNoEqualTo(String.valueOf(writeOffRecordId));
        List<ResendExecute> resendExecuteList = resendExecuteService.selectByExample(executeExample);
        if (CollectionUtils.isEmpty(resendExecuteList)) {
            throw new BizException(Code.ERROR, "核销对象接口参数不存在");
        }
        ResendExecute resend = resendExecuteList.get(0);
        resend.setStatus(CommonStatus.TODO.getCode());
        resendExecuteService.updateByPrimaryKeySelective(resend);
        return 0;
    }

    @Override
    public int positiveAndNegativeDelete(Long writeOffRecordId, Date happenDate) {
        String lockName = String.format("PositiveNegativeInvoiceRecord_delete_%s", writeOffRecordId);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                PositiveNegativeInvoiceRecord record = positiveNegativeInvoiceRecordMapper.selectByPrimaryKey(writeOffRecordId);
                //查询核销记录表
                if (null == record) {
                    throw new BizException(Code.ERROR, "正负发票核销记录不存在");
                }
                if (Objects.equals(record.getDeletedFlag(), DeletedFlag.INVALID.code())) {
                    throw new BizException(Code.ERROR, "核销记录已被删除，请勿重复操作");
                }
                //'同步状态(-1同步失败/0未同步/1同步中/2已同步)，撤销状态(3撤销中/4已撤销/5撤销失败)'
                // 未同步，同步失败才可以删除核销记录
                if (!(Objects.equals(record.getSyncStatus(), PaymentWriteOffRecordStatus.NOT_SYNCHRONIZED.code())
                        || Objects.equals(record.getSyncStatus(), PaymentWriteOffRecordStatus.SYNCHRONIZATION_FAILED.code()))) {
                    throw new BizException(Code.ERROR, "核销记录当前状态不允许操作删除核销");
                }

                //更新负数发票可用
                PaymentInvoice neg = paymentInvoiceMapper.selectByPrimaryKey(record.getNegPaymentInvoiceId());
                logger.info("待更新的负数发票为:{}",JSON.toJSONString(neg));
                neg.setSurplusAmount(neg.getSurplusAmount().subtract(record.getAmount()));
                neg.setTotalPayIncludedPrice(neg.getTotalPayIncludedPrice() == null ?
                        BigDecimal.ZERO : neg.getTotalPayIncludedPrice().add(record.getAmount()));
                neg.setUpdateAt(new Date());
                neg.setUpdateBy(SystemContext.getUserId());
                paymentInvoiceMapper.updateByPrimaryKeySelective(neg);
                //更新正数发票的可用余额
                PaymentInvoice pos = paymentInvoiceMapper.selectByPrimaryKey(record.getPosPaymentInvoiceId());
                logger.info("待更新的负数发票为:{}",JSON.toJSONString(pos));
                pos.setSurplusAmount(pos.getSurplusAmount().add(record.getAmount()));
                pos.setTotalPayIncludedPrice(pos.getTotalPayIncludedPrice() == null ?
                        BigDecimal.ZERO : pos.getTotalPayIncludedPrice().subtract(record.getAmount()));
                pos.setUpdateAt(new Date());
                pos.setUpdateBy(SystemContext.getUserId());
                paymentInvoiceMapper.updateByPrimaryKeySelective(pos);

                //删除核销记录
                PositiveNegativeInvoiceRecord invoiceRecord = new PositiveNegativeInvoiceRecord();
                invoiceRecord.setId(writeOffRecordId);
                invoiceRecord.setDeletedFlag(DeletedFlag.INVALID.code());
                positiveNegativeInvoiceRecordMapper.updateByPrimaryKeySelective(invoiceRecord);

            }
        } catch (Exception e) {
            logger.error("正负发票删除核销记录出现异常", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
        return 0;
    }

    /**
     * 测试推送ESB
     * @param applyNo
     */
    @Override
    public void testPositiveAndNegativeEsb(String applyNo) {
        ResendExecute resendExecute = new ResendExecute();
        resendExecute.setApplyNo(applyNo);
        logger.info("推送的applyNo:{}",applyNo);
        EsbResponse execute = invoiceEsbService.execute(resendExecute);
        logger.info("响应结果为:{}",JSON.toJSONString(execute));

    }

    private void positiveAndNegativeRecord(PaymentWriteOffRecord writeOffRecord){
        if (null == writeOffRecord) {
            throw new BizException(Code.ERROR, "核销记录不能为空");
        }
        if (Objects.isNull(writeOffRecord.getAmount()) || writeOffRecord.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizException(Code.ERROR, "核销金额有误");
        }


        //ap发票-正数
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(writeOffRecord.getPaymentInvoiceId());
        if (null == paymentInvoice) {
            throw new BizException(Code.ERROR, "发票不能为空");
        }
        BigDecimal surplusAmount = paymentInvoice.getSurplusAmount();//发票剩余可用金额
        if (1 != paymentInvoice.getErpStatus()) {
            throw new BizException(Code.ERROR, "AP发票未同步");
        }
        if (paymentInvoice.getAvailable() != null && 1 != paymentInvoice.getAvailable()) {
            throw new BizException(Code.ERROR, "AP发票已被占用");
        }
        if (surplusAmount.compareTo(writeOffRecord.getAmount()) < 0) {
            throw new BizException(Code.ERROR, "发票可用金额不足");
        }




        //ouId校验
        Long ouId = paymentInvoice.getOuId();
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
        if (ListUtils.isEmpty(organizationRelList)) {
            throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
        }
        OrganizationRelDto organizationRelDto = organizationRelList.get(0);
        Long ledgerId = organizationRelDto.getLedgerId();
        List<GlPeriodDto> glPeriodDtoList = basedataExtService.getGlPeriod(ledgerId, GlPeriodType.PAYMENT_PERIOD.getCode().toString(), DateUtils.format(new Date(), "yyyy-MM"));
        List<GlPeriodDto> glPeriodOpenList = basedataExtService.getGlPeriod2(ledgerId, GlPeriodType.PAYMENT_PERIOD.getCode().toString(), "O");
        if (ListUtils.isEmpty(glPeriodOpenList)) {
            throw new MipException("erp分类账为" + ledgerId + "，期间类型为" + GlPeriodType.PAYMENT_PERIOD.getName() + "【会计期间未打开】");
        }
        Date happenDate = null;
        GlPeriodDto glPeriodOpen = null;
        if (ListUtils.isNotEmpty(glPeriodOpenList)) {
            glPeriodOpenList.sort(Comparator.comparing(GlPeriodDto::getEndDate).reversed());
            glPeriodOpen = glPeriodOpenList.get(0);
        }
        if (ListUtils.isNotEmpty(glPeriodDtoList)) {
            GlPeriodDto dto = glPeriodDtoList.get(0);
            String status = dto.getClosingStatus();
            Date startDate = dto.getStartDate();
            Date endDate = dto.getEndDate();
            if ((new Date()).after(startDate) && (new Date()).before(DateUtils.addDay(endDate, 1)) && "O".equals(status)) {
                happenDate = DateUtils.parse(DateUtils.format(new Date(), "yyyy-MM-dd 00:00:00"));
            } else {
                happenDate = Optional.ofNullable(glPeriodOpen).map(GlPeriodDto::getEndDate).orElse(null);
            }
        }
        //更新正数发票可用余额
        paymentInvoice.setSurplusAmount(paymentInvoice.getSurplusAmount().subtract(writeOffRecord.getAmount()));
        paymentInvoice.setTotalPayIncludedPrice(paymentInvoice.getTotalPayIncludedPrice() == null ?
                BigDecimal.ZERO : paymentInvoice.getTotalPayIncludedPrice().add(writeOffRecord.getAmount()));
        paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
        //更新负数发票的可用余额
        PaymentInvoice negPaymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(writeOffRecord.getPaymentRecordId());
        negPaymentInvoice.setSurplusAmount(negPaymentInvoice.getSurplusAmount().add(writeOffRecord.getAmount()));
        negPaymentInvoice.setTotalPayIncludedPrice(negPaymentInvoice.getTotalPayIncludedPrice() == null ?
                BigDecimal.ZERO : negPaymentInvoice.getTotalPayIncludedPrice().subtract(writeOffRecord.getAmount()));
        paymentInvoiceMapper.updateByPrimaryKeySelective(negPaymentInvoice);
        //插入正负核销记录表
        //查询负数发票

        PositiveNegativeInvoiceRecord record = new PositiveNegativeInvoiceRecord();
        record.setNegPaymentInvoiceId(negPaymentInvoice.getId());//负数发票
        record.setNegApInvoiceCode(negPaymentInvoice.getApInvoiceCode());//负数发票编号
        //record.setPaymentRecordId();
        record.setPaymentApplyId(paymentInvoice.getPaymentApplyId());
        record.setPaymentApplyCode(paymentInvoice.getPaymentApplyCode());
        record.setPosPaymentInvoiceId(paymentInvoice.getId());//正数发票
        record.setPosApInvoiceCode(paymentInvoice.getApInvoiceCode());//正数发票
        record.setAmount(writeOffRecord.getAmount());
        record.setSyncStatus(PaymentWriteOffRecordStatus.SYNCHRONIZING.code());
        //record.setSyncMessage("");
        if (ObjectUtils.isEmpty(writeOffRecord.getHappenDate())) {
            record.setHappenDate(happenDate);//设定核销日期
        }else {
            record.setHappenDate(writeOffRecord.getHappenDate());//设定核销日期
        }
        record.setDeletedFlag(DeletedFlag.VALID.code());
        record.setCreateBy(SystemContext.getUserId());
        record.setCreateAt(new Date());
        //record.setUpdateBy(0L);
        //record.setUpdateAt(new Date());
        positiveNegativeInvoiceRecordMapper.insert(record);
        logger.info("获取的待添加的数据为:{}", JSON.toJSONString(record));

        //正负发票核销记录写入
        HandleDispatcher.route(BusinessTypeEnums.POSITIVE_AND_NEGATIVE_RECORD.getCode(), String.valueOf(record.getId()), null, null, false);

    }

    public void autoSend(Long writeOffRecordId, Date applyDate, BigDecimal amount, Long ouId, Long erpInvoiceId, String erpInvoiceCode, String currency) {
        final ResendExecute resend = new ResendExecute();
        resend.setBusinessType(BusinessTypeEnums.PREPAY.getCode());
        resend.setApplyNo(String.valueOf(writeOffRecordId));
        resend.setDeletedFlag(Boolean.FALSE);
        resend.setBatch(false);
        resend.setStatus(CommonStatus.PROCESSING.getCode());
        resendExecuteService.insertSelective(resend);

        List<PrepayErpDto> prepayErpDtos = new ArrayList<>();
        PrepayErpDto erpDto = new PrepayErpDto();
        erpDto.setId(writeOffRecordId);
        erpDto.setOrgId(new BigDecimal(ouId));
        erpDto.setApplyDate(DateUtil.format(applyDate != null ? applyDate : new Date(), "yyyy-MM-dd"));
        erpDto.setPrepayInvoiceId(new BigDecimal(erpInvoiceId));
        erpDto.setAmount(amount);
        erpDto.setInvoiceId(new BigDecimal(erpInvoiceCode));
        erpDto.setCurrencyCode(currency);
        prepayErpDtos.add(erpDto);
        try {
//            final EsbResponse<Object> response = esbService.callAPPSCUXAPPREPAYService(prepayErpDtos,
//                    BusinessTypeEnums.PREPAY.getCode());
            final EsbResponse<Object> response = sdpService.callSdpPrepay(prepayErpDtos, BusinessTypeEnums.PREPAY.getCode());
            resend.setResponCode(response.getResponsecode());
            resend.setStatus(Objects.equals(response.getResponsecode(), ResponseCodeEnums.SUCESS.getCode()) ?
                    CommonStatus.DONE.getCode() : CommonStatus.ERROR.getCode());
            resend.setResponMsg(response.getResponsemessage());
            resend.setEsbSerialNo(null != response.getData() ? String.valueOf(response.getData()) : null);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            resend.setStatus(CommonStatus.ERROR.getCode());
            resend.setResponCode(ResponseCodeEnums.FAULT.getCode());
            resend.setResponMsg(e.toString());
            logger.error("业务编号为:{}的单据发送失败，单据编号为:{}", resend.getBusinessType(), resend.getApplyNo(), e);
        } finally {
            resendExecuteService.updateByPrimaryKeySelective(resend);
            callback(resend);
        }
    }

    /**
     * 回调.
     *
     * @param resendExecute
     */
    public void callback(ResendExecute resendExecute) {
        logger.info("预付款核销回调开始：" + resendExecute);
        final String applyNo = resendExecute.getApplyNo();
        Assert.notNull(applyNo, "applyNo不能为空");
        //查询核销记录表
        PaymentWriteOffRecord writeOffRecord = writeOffRecordMapper.selectByPrimaryKey(Long.parseLong(applyNo));
        if (null == writeOffRecord) {
            throw new BizException(Code.ERROR, "核销记录不能为空");
        }
        //付款记录
        PaymentRecord record = paymentRecordService.getById(writeOffRecord.getPaymentRecordId());
        if (null == record) {
            throw new BizException(Code.ERROR, "付款记录不能为空");
        }
        if (Objects.equals(resendExecute.getResponCode(), ResponseCodeEnums.SUCESS.getCode())) {
            //更新记录状态
            writeOffRecord.setStatus(PaymentWriteOffRecordStatus.SYNCHRONIZING.code());
            writeOffRecord.setSyncMessage(resendExecute.getResponMsg());
            writeOffRecordMapper.updateByPrimaryKeySelective(writeOffRecord);

            record.setPaymentMsg(resendExecute.getResponMsg());
            paymentRecordMapper.updateByPrimaryKeySelective(record);
        } else {
            paymentRecordService.dealFailPrepay(writeOffRecord, record, resendExecute.getResponMsg());
        }
    }

}
