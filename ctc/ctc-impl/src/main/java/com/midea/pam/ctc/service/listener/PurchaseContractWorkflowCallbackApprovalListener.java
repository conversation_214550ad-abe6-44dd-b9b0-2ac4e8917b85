package com.midea.pam.ctc.service.listener;

import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.PurchaseMaterialReleaseDetailDto;
import com.midea.pam.common.ctc.entity.PaymentPlan;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractBudget;
import com.midea.pam.common.ctc.entity.PurchaseContractBudgetExample;
import com.midea.pam.common.ctc.entity.PurchaseContractChangeHeader;
import com.midea.pam.common.ctc.entity.PurchaseContractChangeHeaderExample;
import com.midea.pam.common.enums.EmailStatus;
import com.midea.pam.common.enums.NoticeBusinessType;
import com.midea.pam.common.enums.PaymentPlanStatus;
import com.midea.pam.common.enums.PurchaseContractChangeType;
import com.midea.pam.common.enums.PurchaseContractStatus;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.PurchaseContractChangeHeaderStatus;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.PaymentPlanService;
import com.midea.pam.ctc.contract.service.PurchaseContractBudgetService;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.contract.service.helper.PurchaseContractHelper;
import com.midea.pam.ctc.mapper.EmailExtMapper;
import com.midea.pam.ctc.mapper.PaymentPlanExtMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetMapper;
import com.midea.pam.ctc.mapper.PurchaseContractChangeHeaderMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementExtMapper;
import com.midea.pam.ctc.mapper.UnitExtMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.SdpPurchaseContractService;
import com.midea.pam.ctc.service.event.PurchaseContractWorkflowCallbackApprovalEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class PurchaseContractWorkflowCallbackApprovalListener implements ApplicationListener<PurchaseContractWorkflowCallbackApprovalEvent> {

    private final static Logger logger = LoggerFactory.getLogger(PurchaseContractWorkflowCallbackApprovalListener.class);

    private final static Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private PurchaseContractService purchaseContractService;
    @Resource
    private PaymentPlanService paymentPlanService;
    @Resource
    private PurchaseContractChangeHeaderMapper purchaseContractChangeHeaderMapper;
    @Resource
    private PurchaseContractBudgetService purchaseContractBudgetService;
    @Resource
    private PurchaseContractBudgetMapper purchaseContractBudgetMapper;
    @Resource
    private PurchaseContractBudgetExtMapper purchaseContractBudgetExtMapper;
    @Resource
    private PurchaseMaterialRequirementExtMapper purchaseMaterialRequirementExtMapper;
    @Resource
    private PaymentPlanExtMapper paymentPlanExtMapper;
    @Resource
    private WorkflowCallbackService workflowCallbackService;
    @Resource
    private SdpPurchaseContractService sdpPurchaseContractService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private UnitExtMapper unitExtMapper;
    @Resource
    private EmailExtMapper emailExtMapper;

    @Value("${route.purchaseContractDetailUrl}")
    private String purchaseContractDetailUrl;

    @Value("${business.ruiShiGeUnitId}")
    private String ruiShiGeUnitId;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @Async
    public void onApplicationEvent(PurchaseContractWorkflowCallbackApprovalEvent event) {
        logger.info("采购合同审批通过回调的异步处理参数为:{}", JsonUtils.toString(event));

        pass(event.getFormInstanceId(), event.getFdInstanceId(), event.getFormUrl(), event.getEventName(), event.getHandlerId(),
                event.getCompanyId(), event.getCreateUserId(), event.getNodeId(), event.getTimestampStr());
    }

    @Transactional(rollbackFor = Exception.class)
    public void pass(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                     Long createUserId, String nodeId, String timestamp) {
        logger.info("采购合同审批通过回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("PurchaseContractWorkflowCallbackApproval_pass_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    PurchaseContract purchaseContract = purchaseContractService.findById(formInstanceId);
                    Guard.notNull(purchaseContract, "采购合同审批通过回调 formInstanceId对应合同不存在，不处理");

                    int status = PurchaseContractStatus.EFFECTIVE.getCode();
                    purchaseContract.setStatus(status);

                    //BUG2023101741027-严有为-PAM-运维-中【销售/采购合同】法务回调按照flieDate/PAM内审批流按照timestamp回写合同的归档日期
                    long filingLong = Long.parseLong(timestamp);
                    Date filingDate = new Date(filingLong);
                    purchaseContract.setFilingDate(filingDate);
                    purchaseContractService.update(purchaseContract);

                    purchasingFollowerEmailSet(purchaseContract);

                    String seqPerfix = basedataExtService.getUnitSeqPerfix(companyId);
                    final List<PaymentPlan> paymentPlans = paymentPlanService.findByContractId(purchaseContract.getId());
                    if (ListUtils.isNotEmpty(paymentPlans)) {
                        paymentPlans.forEach(paymentPlan -> {
                            final String paymentPlanCode = PurchaseContractHelper.generatePaymentPlanCode(seqPerfix);
                            paymentPlan.setCode(paymentPlanCode);
                            paymentPlan.setStatus(PaymentPlanStatus.UNPAYED.getCode());
                        });
                        paymentPlanExtMapper.batchUpdate(paymentPlans);
                    }

                    //防止重复回调
                    PurchaseContractChangeHeaderExample checkExample = new PurchaseContractChangeHeaderExample();
                    checkExample.createCriteria().andPurchaseContractIdEqualTo(purchaseContract.getId())
                            .andChangeTypeEqualTo(PurchaseContractChangeType.BUILD.getCode())
                            .andStatusEqualTo(PurchaseContractChangeHeaderStatus.PASS.code())
                            .andDeletedFlagEqualTo(Boolean.FALSE);
                    long count = purchaseContractChangeHeaderMapper.countByExample(checkExample);
                    if (count <= 0) {
                        PurchaseContractChangeHeaderExample example = new PurchaseContractChangeHeaderExample();
                        example.createCriteria().andPurchaseContractIdEqualTo(purchaseContract.getId())
                                .andChangeTypeEqualTo(PurchaseContractChangeType.BUILD.getCode())
                                .andDeletedFlagEqualTo(Boolean.FALSE);
                        List<PurchaseContractChangeHeader> headers = purchaseContractChangeHeaderMapper.selectByExample(example);
                        //如果审批人只有一个，而且设置了相同的审批人自动跳过，回调会缺失draftSubmit事件，导致版本历史没有生成
                        if (ListUtils.isEmpty(headers)) {
                            headers = new ArrayList<>();
                            headers.add(generateVersionHistory(purchaseContract));
                        }

                        if (ListUtils.isNotEmpty(headers)) {
                            //更新变更头状态，生成版本号
                            PurchaseContractChangeHeader header = headers.get(0);
                            header.setStatus(PurchaseContractChangeHeaderStatus.PASS.code());
                            header.setVersionCode(purchaseContractService.generateVersionCode(header, purchaseContract.getCode())); //版本号
                            purchaseContractChangeHeaderMapper.updateByPrimaryKeySelective(header);

                            //备份合同基本信息/付款计划/预算/合同内容
                            purchaseContractService.backup(purchaseContract, header.getId(), true, true, true, true);

                            //回调事件存在重叠，保存审批节点审批人信息
                            if (nodeId.contains(",")) {
                                purchaseContractService.saveNodeInfo(header.getId(), handlerId, nodeId);
                            }
                        }

                        //查询合同新增时关联的采购物料，如果已采购量=总需求量(即未采购量=0)，采购需求状态改为：已关闭；否则改为：待采购
                        purchaseContractBudgetService.updateRequirementStatus(purchaseContract.getId());

                        //采购合同预算更新 需求发布时间/合同创建时间/合同审批通过时间
                        PurchaseContractBudgetExample budgetExample = new PurchaseContractBudgetExample();
                        budgetExample.createCriteria().andPurchaseContractIdEqualTo(purchaseContract.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                        List<PurchaseContractBudget> purchaseContractBudgetList = purchaseContractBudgetMapper.selectByExample(budgetExample);
                        if (ListUtils.isNotEmpty(purchaseContractBudgetList)) {
                            List<Long> requirementIdList = purchaseContractBudgetList.stream().map(PurchaseContractBudget::getPurchaseRequirementId)
                                    .distinct().collect(Collectors.toList());
                            Map<Long, Date> publishTimeMap = purchaseMaterialRequirementExtMapper.selectLatestPublishTime(requirementIdList)
                                    .stream().collect(Collectors.toMap(PurchaseMaterialReleaseDetailDto::getPurchaseRequirementId,
                                            PurchaseMaterialReleaseDetailDto::getPublishTime));
                            Date contractApprovalTime = new Date();
                            for (PurchaseContractBudget purchaseContractBudget : purchaseContractBudgetList) {
                                purchaseContractBudget.setPublishTime(publishTimeMap.get(purchaseContractBudget.getPurchaseRequirementId()));
                                purchaseContractBudget.setPurchaseContractCreateAt(purchaseContract.getCreateAt());
                                purchaseContractBudget.setContractApprovalTime(contractApprovalTime);
                            }
                            purchaseContractBudgetExtMapper.batchUpdate(purchaseContractBudgetList);
                        }
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            true);
                    //SDP 预付款信息推送
                    sdpPurchaseContractService.pushPurchaseContractInfo("采购合同(新增)审批通过",purchaseContract.getId(),false,null);
                } catch (ApplicationBizException e) {
                    logger.info("采购合同审批通过回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("采购合同审批通过回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("采购合同审批通过回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    private void purchasingFollowerEmailSet(PurchaseContract purchaseContract) {
        Long projectId = purchaseContract.getProjectId();
        Project project = projectMapper.selectByPrimaryKey(projectId);
        if (project != null) {
            Long ruiShiGeUnitIdLong = Long.valueOf(ruiShiGeUnitId);
            Long unitId = project.getUnitId();

            Unit unit = unitExtMapper.selectByPrimaryKey(unitId);
            Long parentId = unit.getParentId();
            if (Objects.equals(ruiShiGeUnitIdLong, unitId) || Objects.equals(ruiShiGeUnitIdLong, parentId)) {
                if (Boolean.FALSE.equals(purchaseContract.getIsSynchronizeLegalSystemFlag())) {
                    Long purchasingFollower = purchaseContract.getPurchasingFollower();
                    UserInfo userById = CacheDataUtils.findUserById(purchasingFollower);
                    //采购跟进人MIP
                    String mip = userById.getUsername();

                    Email email = new Email();
                    String header = "PAM采购合同" + purchaseContract.getCode() + "双签合同上传提醒，请在7天内及时完成合同双签原件上传，避免影响付款申请发起";
                    email.setSubject(header);
                    email.setLanguage("zh-CN");
                    email.setReceiver(mip);

                    String content = buildContent(purchaseContract, header);
                    email.setContent(content);
                    email.setBusinessType(NoticeBusinessType.PURCHASING_FOLLOWER_DOCUMENT_UPLOAD.getType());
                    email.setDeletedFlag(Boolean.FALSE);
                    email.setStatus(EmailStatus.TO_DO.getCode());
                    email.setFromAddress("pam");
                    emailExtMapper.insert(email);
                }
            }
        }
    }


    /**
     * 生成版本历史，变更类型0：合同新增
     *
     * @param purchaseContract
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PurchaseContractChangeHeader generateVersionHistory(PurchaseContract purchaseContract) {
        PurchaseContractChangeHeader header = new PurchaseContractChangeHeader();
        header.setChangeType(PurchaseContractChangeType.BUILD.getCode());
        header.setDeletedFlag(Boolean.FALSE);
        header.setPurchaseContractId(purchaseContract.getId());
        header.setStatus(PurchaseContractChangeHeaderStatus.DRAFT.code());
        header.setIsElectronicContract(purchaseContract.getIsElectronicContract());
        header.setOtherName(purchaseContract.getOtherName());
        header.setOtherPhone(purchaseContract.getOtherPhone());
        header.setPublicOrPrivate(purchaseContract.getPublicOrPrivate());
        header.setSealCategory(purchaseContract.getSealCategory());
        header.setSealAdminAccountIds(purchaseContract.getSealAdminAccountIds());
        header.setCreateBy(purchaseContract.getCreateBy());
        purchaseContractChangeHeaderMapper.insertSelective(header);
        return header;
    }

    private String buildContent(PurchaseContract purchaseContract, String header) {
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");

        String urlStr = "<a href='" + buildMilepostNoticeUrl(purchaseContract.getId()) + "' target='_blank'>" +
                header + "</a>";
        header = "<div style='font-size: 12px;'>" + urlStr + "</div><br/>";

        sb.append(header);
        sb.append("</html>");
        return sb.toString();
    }

    private String buildMilepostNoticeUrl(Long batchId) {
        return purchaseContractDetailUrl + batchId;
    }

}
