package com.midea.pam.ctc.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.HashBasedTable;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.BrandMaintenance;
import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.basedata.entity.MaterialCost;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.dto.ErpCodeRuleDto;
import com.midea.pam.common.ctc.dto.MaterialCustomDictHeaderDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailSubmitHistoryDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanOldDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.entity.DesignPlanDetailMatchResult;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailMiddle;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummary;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummaryExample;
import com.midea.pam.common.ctc.excelVo.DesignPlanDetailChangeImportExcelVO;
import com.midea.pam.common.ctc.excelVo.ImportDesignPlanDetailKUKA2022ExcelVo;
import com.midea.pam.common.ctc.excelVo.ImportMilepostDesignPlanDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailImportExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailKsImportExcelVo;
import com.midea.pam.common.ctc.excelVo.UpdateMilepostDesignPlanDetailExcelVo;
import com.midea.pam.common.ctc.query.DetailByProjectIdAndMilepostIdNewWbsQuery;
import com.midea.pam.common.ctc.vo.MerielSubclassVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErpOrganizationId;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.MaterialCodeRuleEnum;
import com.midea.pam.common.enums.MaterialCostType;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.common.util.idaas.TwoTuple;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DesignPlanDetailGenerateRequire;
import com.midea.pam.ctc.common.enums.ErpCodeSource;
import com.midea.pam.ctc.common.enums.MaterialCategoryEnum;
import com.midea.pam.ctc.common.enums.ProjectMilepostAnnexType;
import com.midea.pam.ctc.common.redis.RedisLuaScriptServer;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMiddleExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMiddleMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryMapper;
import com.midea.pam.ctc.service.AsyncRequestResultService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.ErpCodeRuleService;
import com.midea.pam.ctc.service.MaterialCostExtService;
import com.midea.pam.ctc.service.MaterialCustomDictService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MaterialGetService;
import com.midea.pam.ctc.service.MaterialReturnService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailSubmitHistoryService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.PurchaseOrderService;
import com.midea.pam.ctc.service.PurchaseProgressService;
import com.midea.pam.ctc.service.event.DesignPlanDetailChangeMatchResultEvent;
import com.midea.pam.ctc.service.event.DesignPlanDetailMatchResultEvent;
import com.midea.pam.ctc.service.event.KsDesignPlanDetailMatchResultEvent;
import com.midea.pam.ctc.service.event.KsMatchingMaterialCodeEvent;
import com.midea.pam.ctc.service.event.MatchingMaterialCodeEvent;
import com.midea.pam.ctc.service.helper.MilepostDesignPlanHelper;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.DateUtils.FORMAT_SHORT;
import static com.midea.pam.common.util.StringUtils.buildGetUrl;

public class MilepostDesignPlanDetailServiceImpl implements MilepostDesignPlanDetailService {

    private static final Logger logger = LoggerFactory.getLogger(MilepostDesignPlanDetailServiceImpl.class);

    private final static String MATERIAL_CLASS = "物料";
    private final static String PURCHASE_PART = "外购物料";
    private final static String KANBAN_MATERIAL = "看板物料";
    private final static String MACHINED_PART_TYPE = "jiagongjian_type";
    private final static String MACHINED_PART = "机械加工件";

    private final static String ERROR_JGJFL_KEY = "ERROR_JGJFL_KEY";
    private final static String ERROR_CZ_KEY = "ERROR_CZ_KEY";
    private final static String ERROR_DWZL_KEY = "ERROR_DWZL_KEY";
    private final static String ERROR_CZCL_KEY = "ERROR_CZCL_KEY";
    private final static String ERROR_JGJFL = "加工件分类";
    private final static String ERROR_CZ = "材质";
    private final static String ERROR_DWZL = "单位重量(Kg)";
    private final static String ERROR_CZCL = "材质处理";

    private final static String ERROR_TH_KEY = "ERROR_TH_KEY";
    private final static String ERROR_TZBBH_KEY = "ERROR_TZBBH_KEY";
    private final static String ERROR_BMCL_KEY = "ERROR_BMCL_KEY";
    private final static String ERROR_TH = "图号";
    private final static String ERROR_TZBBH = "图纸版本号";
    private final static String ERROR_BMCL = "表面处理";

    private final static String MATERIAL_TYPE = "material_type";

    private final static Integer EACH_THREAD_DATA_NUM = 500;

    @Resource
    private MilepostDesignPlanHelper milepostDesignPlanHelper;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private ProjectService projectService;
    @Resource
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private MaterialCustomDictService materialCustomDictService;
    @Resource
    private MaterialReturnService materialReturnService;
    @Resource
    private MaterialGetService materialGetService;
    @Resource
    private PurchaseProgressService purchaseProgressService;
    @Resource
    private MaterialCostExtService materialCostExtService;
    @Resource
    private ErpCodeRuleService erpCodeRuleService;
    @Resource
    private AsyncRequestResultService asyncRequestResultService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private MilepostDesignPlanDetailMiddleMapper middleMapper;
    @Resource
    private MilepostDesignPlanDetailMiddleExtMapper middleExtMapper;
    @Resource
    private MilepostDesignPlanDetailSubmitHistoryService milepostDesignPlanDetailSubmitHistoryService;
    @Resource
    private ProjectWbsBudgetSummaryMapper projectWbsBudgetSummaryMapper;
    @Qualifier("importDesignPlanDetailKUKA2022Executor")
    @Resource
    private ThreadPoolTaskExecutor importDesignPlanDetailKUKA2022Executor;
    @Resource
    private RedisLuaScriptServer redisLuaScriptServer;


    @Override
    public MilepostDesignPlanDetailDto add(MilepostDesignPlanDetailDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        dto.setInit(Boolean.FALSE);
        MilepostDesignPlanDetail entity = BeanConverter.copy(dto, MilepostDesignPlanDetail.class);
        String itemInfo = entity.getMaterielDescr();
        if (StringUtils.isNotEmpty(itemInfo)) {
            entity.setMaterielDescr(itemInfo.replaceAll("\r|\n", " ").replaceAll(",", " ").replaceAll("，", " "));
        }
        milepostDesignPlanDetailMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        //保存mrp
        milepostDesignPlanService.setMRP(dto.getId());
        return dto;
    }

    @Override
    public MilepostDesignPlanDetailDto update(MilepostDesignPlanDetailDto dto) {
        Asserts.notEmpty(dto.getId(), ErrorCode.ID_NOT_NULL);
        MilepostDesignPlanDetail entity = BeanConverter.copy(dto, MilepostDesignPlanDetail.class);
        logger.info("*************** MilepostDesignPlanDetailServiceImpl.update入参:{} ***************", JSONObject.toJSONString(entity));
        milepostDesignPlanDetailMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        //先将旧的详细设计删除再新增一条(为支持详细设计的多层结构)
        //milepostDesignPlanDetailExtMapper.updateById(dto.getId());
        //再重新保存
        //保存mrp
        milepostDesignPlanService.setMRP(dto.getId());
        //this.add(dto);
        return dto;
    }

    @Override
    public MilepostDesignPlanDetailDto save(MilepostDesignPlanDetailDto dto, Long userBy) {
        if (null != dto.getOrStorage() && dto.getOrStorage()) {
            dto.setId(null);
            dto.setCreateBy(userBy);
            dto.setStatus(CheckStatus.DRAFT.code());
            return this.add(dto);
        } else {
            if (dto.getId() == null) {
                dto.setCreateBy(userBy);
                return this.add(dto);
            } else {
                dto.setUpdateBy(userBy);
                return this.update(dto);
            }
        }
    }

    @Override
    public MilepostDesignPlanDetailDto getById(Long id) {
        if (id == null) {
            return null;
        }
        MilepostDesignPlanDetail entity = milepostDesignPlanDetailMapper.selectByPrimaryKey(id);
        MilepostDesignPlanDetailDto dto = BeanConverter.copy(entity, MilepostDesignPlanDetailDto.class);
        return dto;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> selectList(MilepostDesignPlanDetailDto query) {
        return milepostDesignPlanDetailExtMapper.selectList(query);
    }

    @Override
    public PageInfo<MilepostDesignPlanDetailDto> selectPage(MilepostDesignPlanDetailDto Query) {
        return null;
    }

    public MilepostDesignPlanDetailExample buildCondition(MilepostDesignPlanDetailDto query) {
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        MilepostDesignPlanDetailExample.Criteria criteria = example.createCriteria();
        if (query.getWbsLayer() != null) {
            criteria.andWbsLayerLike(query.getWbsLayer());
        }
        if (query.getMaterielDescr() != null) {
            criteria.andMaterielDescrLike(query.getMaterielDescr());
        }
        if (query.getPamCode() != null) {
            criteria.andPamCodeLike(query.getPamCode());
        }
        if (query.getErpCode() != null) {
            criteria.andErpCodeLike(query.getErpCode());
        }
        if (query.getWbsSummaryCode() != null) {
            criteria.andWbsSummaryCodeLike(query.getWbsSummaryCode());
        }

        if (query.getDeletedFlag() != null) {
            criteria.andDeletedFlagEqualTo(query.getDeletedFlag());
        } else {
            criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        }
        if (query.getNoIncludeId() != null) {
            criteria.andIdNotEqualTo(query.getNoIncludeId());
        }
        if (query.getProjectId() != null) {
            criteria.andProjectIdEqualTo(query.getProjectId());
        }
        if (query.getSubmitRecordId() != null) {
            criteria.andSubmitRecordIdEqualTo(query.getSubmitRecordId());
        }
        if (query.getMilepostId() != null) {
            criteria.andMilepostIdEqualTo(query.getMilepostId());
        }
        if (query.getWhetherModel() != null) {
            criteria.andWhetherModelEqualTo(query.getWhetherModel());
        }
        if (query.getModuleStatus() != null) {
            criteria.andModuleStatusEqualTo(query.getModuleStatus());
        }
        if (query.getParentId() != null) {
            criteria.andParentIdEqualTo(query.getParentId());
        }
        if (query.getGenerateRequirement() != null) {
            criteria.andGenerateRequirementEqualTo(query.getGenerateRequirement());
        }
        if (query.getProjectBudgetMaterialId() != null) {
            criteria.andProjectBudgetMaterialIdEqualTo(query.getProjectBudgetMaterialId());
        }
        if (query.getPamCode() != null) {
            criteria.andPamCodeEqualTo(query.getPamCode());
        }
        if (query.getDeliveryTime() != null) {
            criteria.andDeliveryTimeEqualTo(query.getDeliveryTime());
        }
        if (query.getStatus() != null) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (ListUtil.isPresent(query.getParentIds())) {
            criteria.andParentIdIn(query.getParentIds());
        }
        if (StringUtils.hasText(query.getErpCode())) {
            criteria.andErpCodeEqualTo(query.getErpCode());
        }
        if (query.getDesignPlanDetailIds() != null) {
            criteria.andIdIn(query.getDesignPlanDetailIds());
        }
        if (query.getStatusList() != null) {
            criteria.andStatusIn(query.getStatusList());
        }
        if (query.getId() != null) {
            criteria.andIdEqualTo(query.getId());
        }
        return example;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MilepostDesignPlanDetailDto saveWithMaterialCostAndMaterial(MilepostDesignPlanDetailDto dto, Long userBy, Project project,
                                                                       Boolean isCreateMaterialAndCost, Long storageId) {
        if (Objects.isNull(dto.getId())) {
            if (null != dto.getMaterialCategory()) {
                Asserts.notEmpty(dto.getPamCode(), ErrorCode.CTC_PAM_CODE_NOT_NULL);
            }
        }
        //创建物料估价(新导入的交付物及删除物料成本的物料)
        if ((Objects.isNull(dto.getDesignCost()) && isCreateMaterialAndCost && MATERIAL_CLASS.equals(dto.getMaterialClassification())) ||
                (Objects.isNull(dto.getDesignCost()) && isCreateMaterialAndCost && (PURCHASE_PART.equals(dto.getMaterialCategory()) || KANBAN_MATERIAL.equals(dto.getMaterialCategory())))) { //修改为根据物料类型做判断
            MaterialCostDto materialCostDto = materialCostExtService.matchingDesignCost(dto.getPamCode(), storageId);
            if (Objects.isNull(materialCostDto)) {
                materialCostDto = this.saveMaterialCostDto(dto, project, userBy, storageId);
                Guard.notNull(materialCostDto, "创建未估价物料信息异常");
            }
            dto.setDesignCostId(materialCostDto.getId());
        }

        //创建物料信息(新导入的交付物)
        if ((Objects.isNull(dto.getId()) && isCreateMaterialAndCost && MATERIAL_CLASS.equals(dto.getMaterialClassification()))
                || (Objects.isNull(dto.getId()) && isCreateMaterialAndCost && StringUtils.isNotEmpty(dto.getPamCode())) && StringUtils.isNotEmpty(dto.getMaterialCategory())) {
            dto.setCreateBy(userBy);
            this.saveMaterial(dto, storageId, project.getOuId());
        }

        dto.setWbsConfirmFlag(true);

        if (StringUtils.hasText(dto.getDeliveryTimeStr())) {
            try {
                dto.setDeliveryTime(DateUtils.parse(dto.getDeliveryTimeStr(), FORMAT_SHORT));
            } catch (Exception e) {
                dto.setDeliveryTime(DateUtils.parse(dto.getDeliveryTimeStr()));
            }
        }

        // 保证高并发情况下审批回调已经通过了，提交的接口修改了status
        if (null != dto.getId()) {
            String redisKey = "milepostDesignPlanDetailSubmitApprovalStatus_" + dto.getId();
            String luaScript = String.format("return redis.call('get', '%s'); \n", redisKey);
            String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
            if (StringUtils.isNotEmpty(redisResult) && CheckStatus.PASS.code().equals(Integer.valueOf(redisResult))
                    && CheckStatus.DRAFT.code().equals(dto.getStatus())) {
                logger.info("milepostDesignPlanDetailSubmitApprovalStatus_{} 已经审批通过，不允许修改状态为草稿", dto.getId());
                dto.setStatus(CheckStatus.PASS.code());
            }
        }

        return save(dto, userBy);
    }

    @Override
    public void updateMaterialCostRequirement(String pamCode, Long milepostId, Long organizationId) {
        MaterialCostDto materialCostDto = materialCostExtService.matchingDesignCost(pamCode, organizationId);
        if (null == materialCostDto) {
            return;
        }
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andStatusIsNotNull()// 详细设计status为空的，不做处理
                .andMilepostIdEqualTo(milepostId).andPamCodeEqualTo(pamCode);
        List<MilepostDesignPlanDetail> designPlanDetailList = milepostDesignPlanDetailMapper.selectByExample(example);
        BigDecimal totalNum = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(designPlanDetailList)) {
            for (MilepostDesignPlanDetail designPlanDetail : designPlanDetailList) {
                BigDecimal requirementNum =
                        designPlanDetail.getNumber().multiply(getParentSigmaById(designPlanDetail.getParentId(), null));
                if (null != requirementNum) {
                    totalNum = totalNum.add(requirementNum);
                }
            }
            materialCostDto.setRequirementNum(totalNum);
            materialCostExtService.invokeMaterialCostApiSave(materialCostDto);
        }
    }

    /**
     * 创建物料
     */
    @Override
    public void saveMaterial(MilepostDesignPlanDetailDto dto, Long storageId, Long ouId) {
        String chartVersion = dto.getChartVersion();
        //TODO:3082,代表机器人的物料,将来可考虑获取上下文的ouId
        MaterialDto materialDto = materialExtService.getNotDeletedMaterialByPamCodeOrErpCode(dto.getPamCode(), dto.getErpCode(),
                storageId != null ? storageId : ErpOrganizationId.ROBOT.code());
        // 详设导入提交时通过 pamCode 和 organizationId 去 material表里面查数据，如果不存在或者erpCode导入跨组织物料引用找不到pamCode
        if (Objects.isNull(materialDto) || dto.isImportFindMaterialPamCodeIsNotExist()) {
            materialDto = Optional.ofNullable(materialDto).orElse(new MaterialDto());
            materialDto.setDelistFlag(Boolean.FALSE);
            //必填
            materialDto.setOrganizationId(storageId != null ? storageId : ErpOrganizationId.ROBOT.code());
            materialDto.setBuyerNumber(materialExtService.getBuyerNumberByOu(ouId));
            materialDto.setItemInfo(dto.getMaterielDescr());//物料描述
            materialDto.setUnit(dto.getUnitCode());//基本计量单位
            if (null != dto.getMaterielType()) {
                ErpCodeRuleDto erpCodeRuleDto = erpCodeRuleService.getByCodingSubclass(dto.getMaterielType());
                if (erpCodeRuleDto != null) {
                    materialDto.setItemType(erpCodeRuleDto.getErpPurchaseFlag());//用户物料类型
                }
                materialDto.setMaterialType(dto.getMaterielType());//物料小类
            }
            if (StringUtils.isNotEmpty(dto.getMaterialCategory())) {
                if ("外购物料".equals(dto.getMaterialCategory())) {
                    materialDto.setItemType("P");
                    materialDto.setMaterialAttribute("外购件");
                } else if ("虚拟件".equals(dto.getMaterialCategory())) {
                    materialDto.setItemType("PH");
                } else if ("装配件".equals(dto.getMaterialCategory())) {
                    materialDto.setItemType("SA");
                } else if ("成品".equals(dto.getMaterialCategory())) {
                    materialDto.setItemType("FG");
                } else if ("看板物料".equals(dto.getMaterialCategory())) {
                    materialDto.setItemType("SA-P");
                    materialDto.setMaterialAttribute("看板件");
                }
            }
            materialDto.setInventoryType(StringUtils.isNotEmpty(dto.getInventoryType()) ? dto.getInventoryType() : "0.0.0");//库存分类
            materialDto.setPurType("0.0.0");//采购分类
            materialDto.setItemCode(dto.getErpCode());//物料编码(ERP编码)
            materialDto.setItemStatus(StringUtils.isNotEmpty(dto.getItemStatus()) ? dto.getItemStatus() : "Active");
            materialDto.setDeleteFlag(Boolean.FALSE);
            materialDto.setPamCode(StringUtils.isEmpty(dto.getPamCode()) ? null : dto.getPamCode());//PAM编码
            materialDto.setMaterialClassification(StringUtils.isEmpty(dto.getMaterialClassification()) ? null :
                    dto.getMaterialClassification());//物料大类
            materialDto.setName(dto.getName());//名称
            materialDto.setModel(dto.getModel());//型号/规格/图号
            materialDto.setBrand(dto.getBrand());//品牌
            materialDto.setMachiningPartType(dto.getMachiningPartType());//加工件分类
            materialDto.setMaterial(dto.getMaterial());//材料
            materialDto.setMaterialProcessing(dto.getMaterialProcessing());//材质处理
            materialDto.setUnitWeight(dto.getUnitWeight());//单位重量
            materialDto.setCreateBy(dto.getCreateBy());

            //昆山迭代五，新增字段
            materialDto.setMaterialCategory(StringUtils.isEmpty(dto.getMaterialCategory()) ? null :
                    dto.getMaterialCategory()); //物料类型
            materialDto.setCodingMiddleclass(StringUtils.isEmpty(dto.getCodingMiddleClass()) ? null :
                    dto.getCodingMiddleClass()); //物料中类
            materialDto.setFigureNumber(StringUtils.isEmpty(dto.getFigureNumber()) ? null : dto.getFigureNumber()); //图号
            materialDto.setChartVersion(StringUtils.isEmpty(dto.getChartVersion()) ? "0" : dto.getChartVersion());
            materialDto.setMaximumDrawVersionNum(StringUtils.isNotEmpty(chartVersion) && NumberUtil.isNumber(chartVersion) ?
                    BigDecimalUtils.stringToLong(chartVersion) : null);
            //图纸版本
            materialDto.setBrandMaterialCode(StringUtils.isEmpty(dto.getBrandMaterialCode()) ? null :
                    dto.getBrandMaterialCode());  //品牌商物料编码
            materialDto.setOrSparePartsMask(StringUtils.isEmpty(dto.getOrSparePartsMask()) ? null :
                    dto.getOrSparePartsMask()); //备件标识
            materialDto.setBuyerRound(StringUtils.isEmpty(dto.getPerchasingLeadtime()) ? null :
                    dto.getPerchasingLeadtime().toString()); //采购提前期
            materialDto.setMinimumOrderQuantity(StringUtils.isEmpty(dto.getMinPerchaseQuantity()) ? null :
                    dto.getMinPerchaseQuantity()); //最小订货量（最小采购量）
            materialDto.setFixedLotMultiplier(StringUtils.isEmpty(dto.getMinPackageQuantity()) ? null :
                    dto.getMinPackageQuantity().toString()); //固定批次增加（最小包装量）

            String userName = null;
            if (null != dto.getMaterielType()) {
                userName = queryKsMaterialCustomDict(dto.getMaterialClassification(), dto.getMaterielType(), storageId); //配套人员
            }
            if (userName != null) { //根据物料小类查询配套人员
                materialDto.setSourcer(userName);
            } else { // 根据库存组织物料大中小类查询配套人员
                // 查询核价员（配套人员）并保存到物料基础表、物料估价表
                MaterialCustomDictHeaderDto materialCustomDictHeaderDto = new MaterialCustomDictHeaderDto();
                materialCustomDictHeaderDto.setOrganizationId(storageId);
                materialCustomDictHeaderDto.setCodingClass(dto.getMaterialClassification());
                materialCustomDictHeaderDto.setCodingMiddleclass(dto.getCodingMiddleClass());
                materialCustomDictHeaderDto.setCodingSubclass(dto.getMaterielType());
                MaterialCustomDictHeaderDto materialCustomDictHeaderDtoNew = materialCustomDictService.queryValuer(materialCustomDictHeaderDto);
                materialDto.setSourcer(materialCustomDictHeaderDtoNew.getValuerMip());
            }
            materialExtService.invokeMaterialApiSave(materialDto);
        } else if (StringUtils.isNotEmpty(chartVersion) && NumberUtil.isNumber(chartVersion)
                && (Objects.isNull(materialDto.getMaximumDrawVersionNum())
                || new BigDecimal(chartVersion).compareTo(BigDecimal.valueOf(materialDto.getMaximumDrawVersionNum())) > 0)) {
            if (StringUtils.isNotEmpty(dto.getInventoryType())) {
                materialDto.setInventoryType(dto.getInventoryType());//库存分类
            }
            materialDto.setMaximumDrawVersionNum(BigDecimalUtils.stringToLong(chartVersion));
            materialExtService.invokeMaterialApiSave(materialDto);
        } else {
            // 没有删除，但是退市的物料
            if (StringUtils.isNotEmpty(dto.getInventoryType())) {
                materialDto.setInventoryType(dto.getInventoryType());//库存分类
                materialExtService.invokeMaterialApiSave(materialDto);
            }
        }

        List<MaterialDto> fillMaterialDtoList = dto.getFillMaterialDtoList();
        if (ListUtils.isNotEmpty(fillMaterialDtoList)) {
            for (MaterialDto fillMaterialDto : fillMaterialDtoList) {
                // 如果按ERP编码在其它库存组织（如137）找到一条数据，并且这条数据PAM编码不存在：新增物料基础表136本身数据，137增加P码、部分继承的字段
                materialExtService.invokeMaterialApiSave(fillMaterialDto);
            }
        }
    }

    /**
     * 创建物料估价
     */
    @Override
    public MaterialCostDto saveMaterialCostDto(MilepostDesignPlanDetailDto dto, Project project, Long userBy, Long storageId) {
        MaterialCostDto materialCostDto = new MaterialCostDto();
        materialCostDto.setOrganizationId(storageId != null ? storageId : ErpOrganizationId.ROBOT.code());
        materialCostDto.setPamCode(dto.getPamCode());
        materialCostDto.setItemCode(dto.getErpCode());
        materialCostDto.setDescr(dto.getMaterielDescr());
        materialCostDto.setUnit(dto.getUnitCode());
        materialCostDto.setBusinessClassification(dto.getBusinessClassification());
        materialCostDto.setMaterialClassification(dto.getMaterialClassification());
        materialCostDto.setMaterielType(dto.getMaterielType());
        materialCostDto.setMaterialCostType(MaterialCostType.APPRAISAL.code());
        materialCostDto.setProjectCode(project.getCode());
        materialCostDto.setProjectId(project.getId());
        //统计需求量
        BigDecimal totalNum = dto.getNumber().multiply(getParentSigmaById(dto.getParentId(), null));
        materialCostDto.setMilepostId(dto.getMilepostId());
        materialCostDto.setRequirementNum(totalNum);
        materialCostDto.setCreateBy(userBy);
        materialCostDto.setCreateAt(new Date());
        materialCostDto.setMachiningPartType(dto.getMachiningPartType());
        materialCostDto.setUnitWeight(dto.getUnitWeight());
        materialCostDto.setMaterial(dto.getMaterial());
        materialCostDto.setMaterialProcessing(dto.getMaterialProcessing());

        //昆山迭代五，新增字段
        materialCostDto.setMaterialCategory(StringUtils.isEmpty(dto.getMaterialCategory()) ? null :
                dto.getMaterialCategory()); //物料类型
        materialCostDto.setCodingMiddleclass(StringUtils.isEmpty(dto.getCodingMiddleClass()) ? null :
                dto.getCodingMiddleClass()); //物料中类
        materialCostDto.setFigureNumber(StringUtils.isEmpty(dto.getFigureNumber()) ? null : dto.getFigureNumber()); //图号
        materialCostDto.setChartVersion(StringUtils.isEmpty(dto.getChartVersion()) ? null : dto.getChartVersion());
        //图纸版本
        materialCostDto.setBrandMaterialCode(StringUtils.isEmpty(dto.getBrandMaterialCode()) ? null :
                dto.getBrandMaterialCode());  //品牌商物料编码
        materialCostDto.setOrSparePartsMask(StringUtils.isEmpty(dto.getOrSparePartsMask()) ? null :
                dto.getOrSparePartsMask()); //备件标识
        materialCostDto.setAdvancePurchaseData(StringUtils.isEmpty(dto.getPerchasingLeadtime()) ? null :
                dto.getPerchasingLeadtime().toString()); //采购提前期
        materialCostDto.setMinOrderQuantity(StringUtils.isEmpty(dto.getMinPerchaseQuantity()) ? null :
                dto.getMinPerchaseQuantity().toString()); //最小订货量（最小采购量）
        materialCostDto.setFixedLotMultiplier(StringUtils.isEmpty(dto.getMinPackageQuantity()) ? null :
                dto.getMinPackageQuantity().toString()); //固定批次增加（最小包装量）
        materialCostDto.setName(StringUtils.isEmpty(dto.getName()) ? null : dto.getName()); //名字
        materialCostDto.setBrand(StringUtils.isEmpty(dto.getBrand()) ? null : dto.getBrand()); //品牌
        materialCostDto.setModel(StringUtils.isEmpty(dto.getModel()) ? null : dto.getModel()); //型号


        Long aLong = queryMaterialCustomDict(dto.getMaterialClassification(), dto.getMaterielType(), storageId); //分配核价人
        if (aLong != null) {
            materialCostDto.setNuclearPriceUserBy(aLong);
        } else { //昆山迭代五新增功能，若根据小类查不出，则根据物料中类带出。
            // 查询核价员（配套人员）并保存到物料基础表、物料估价表
            MaterialCustomDictHeaderDto materialCustomDictHeaderDto = new MaterialCustomDictHeaderDto();
            materialCustomDictHeaderDto.setOrganizationId(storageId);
            materialCustomDictHeaderDto.setCodingClass(dto.getMaterialClassification());
            materialCustomDictHeaderDto.setCodingMiddleclass(dto.getCodingMiddleClass());
            materialCustomDictHeaderDto.setCodingSubclass(dto.getMaterielType());
            MaterialCustomDictHeaderDto materialCustomDictHeaderDtoNew =
                    materialCustomDictService.queryValuer(materialCustomDictHeaderDto);
            materialCostDto.setNuclearPriceUserBy(materialCustomDictHeaderDtoNew.getValuerId());
        }
        OrganizationRel organizationRel =
                materialCostExtService.invokeOrganizationRelApi(materialCostDto.getOrganizationId());
        if (organizationRel != null) {
            materialCostDto.setCurrency(organizationRel.getCurrency());
        }
        return materialCostExtService.invokeMaterialCostApiSave(materialCostDto);
    }

    @Override
    public Long queryMaterialCustomDict(String materialClassification, String materielType, Long storageId) {
        Long nuclearPriceUserBy = -1L;
        List<MaterialCustomDictHeaderDto> headerDtoList = new ArrayList<>();
        MaterialCustomDictHeaderDto customDictHeaderDto = new MaterialCustomDictHeaderDto();
        customDictHeaderDto.setCodingClass(materialClassification);
        customDictHeaderDto.setCodingSubclass(materielType);
        customDictHeaderDto.setOrganizationId(storageId);
        customDictHeaderDto.setCategoryName("核价员");//核价人
        headerDtoList.add(customDictHeaderDto);
        Set<String> mipnames = materialCustomDictService.queryMaterialCustomDict(headerDtoList);
        if (mipnames.iterator().hasNext()) {
            UserInfo userInfo = CacheDataUtils.findUserByMip(mipnames.iterator().next());
            if (userInfo != null) {
                nuclearPriceUserBy = userInfo.getId();
            }
        }
        return nuclearPriceUserBy;
    }

    public String queryKsMaterialCustomDict(String materialClassification, String materielType, Long storageId) {
        String username = null;
        List<MaterialCustomDictHeaderDto> headerDtoList = new ArrayList<>();
        MaterialCustomDictHeaderDto customDictHeaderDto = new MaterialCustomDictHeaderDto();
        customDictHeaderDto.setCodingClass(materialClassification);
        customDictHeaderDto.setCodingSubclass(materielType);
        customDictHeaderDto.setOrganizationId(storageId);
        customDictHeaderDto.setCategoryName("核价员");//核价人
        headerDtoList.add(customDictHeaderDto);
        Set<String> mipnames = materialCustomDictService.queryMaterialCustomDict(headerDtoList);
        if (mipnames.iterator().hasNext()) {
            UserInfo userInfo = CacheDataUtils.findUserByMip(mipnames.iterator().next());
            if (userInfo != null) {
                username = userInfo.getUsername();
            }
        }
        return username;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean recursiveSave(Project project, MilepostDesignPlanDetailDto dto, Long parentId,
                                 String releaseLotNumber, Long userBy, int level, Long storageId,
                                 ProjectWbsReceiptsDto recordDto, List<Long> historyIds, List<MilepostDesignPlanDetail> updatePlanDetailList,
                                 List<TwoTuple<TwoTuple<Long, Boolean>, MilepostDesignPlanDetailDto>> twoTupleList) {
        if (dto.getId() == null) {
            //新建设计信息需要初始化是否产生物料需求字段
            dto.setGenerateRequirement(DesignPlanDetailGenerateRequire.NOT_PRODUCED.code());
        }
        dto.setProjectId(project.getId());

        //新导入的交付物且有erp编码,需要同步erp编码与pam编码最新的对应关系
        if (dto.getId() == null && StringUtils.isNotEmpty(dto.getErpCode())) {
            dto.setPamCode(materialExtService.syncPamCode2MaterialAndCost(dto.getPamCode(), dto.getErpCode()));
        }
        //需要过滤一级/二级BOM模组，不生成物料估价
        Boolean isCreateMaterialAndCost = level > 2 ? Boolean.TRUE : Boolean.FALSE;

        //这里的parentId 不是详设的parentId，而是milepost_design_plan_detail_submit_history的parentId，对应milepost_design_plan_detail_submit_history的id
        dto.setParentId(parentId);
        MilepostDesignPlanDetailSubmitHistoryDto parentDto = saveSubmitHistory(dto, userBy, isCreateMaterialAndCost, recordDto,
                releaseLotNumber, updatePlanDetailList, twoTupleList);
        historyIds.add(parentDto.getId());
        try {
            List<MilepostDesignPlanDetailDto> sonDtos = dto.getSonDtos();
            if (ListUtil.isPresent(sonDtos)) {
                for (MilepostDesignPlanDetailDto sonDto : sonDtos) {
                    recursiveSave(project, sonDto, parentDto.getId(), releaseLotNumber, userBy, ++level, storageId, recordDto, historyIds,
                            updatePlanDetailList, twoTupleList);
                }
            }
        } catch (Exception e) {
            logger.error("柔性详设发布的recursiveSave方法错误", e);
            throw e;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean recursiveSave(Project project, MilepostDesignPlanDetailDto dto, Long parentId, Long submitRecordId,
                                 Long milepostId, Long userBy, int level, Long storageId, Boolean storageStatus, Set<Long> uploadPathIdSet) {
        if (uploadPathIdSet.contains(dto.getId()) || Boolean.TRUE.equals(dto.get_isNew())) {
            //这里的parentId 才是详设的parentId
            dto.setParentId(parentId);
            dto.setOrStorage(storageStatus);
            if (null != submitRecordId && null == dto.getId()) {
                dto.setOrStorage(Boolean.TRUE);
                dto.setSubmitRecordId(submitRecordId);
            } else {
                dto.setSubmitRecordId(null);
            }
            if (dto.getId() == null) {
                //新建设计信息需要初始化是否产生物料需求字段
                dto.setGenerateRequirement(DesignPlanDetailGenerateRequire.NOT_PRODUCED.code());
            }
            dto.setProjectId(project.getId());
            dto.setMilepostId(milepostId);

            //新导入的交付物且有erp编码,需要同步erp编码与pam编码最新的对应关系
            if (dto.getId() == null && StringUtils.isNotEmpty(dto.getErpCode())) {
                dto.setPamCode(materialExtService.syncPamCode2MaterialAndCost(dto.getPamCode(), dto.getErpCode()));
            }
            //需要过滤一级/二级BOM模组，不生成物料估价
            Boolean isCreateMaterialAndCost = level > 2 ? Boolean.TRUE : Boolean.FALSE;
            MilepostDesignPlanDetailDto parentDto = saveWithMaterialCostAndMaterial(dto, userBy, project, isCreateMaterialAndCost, storageId);

            List<MilepostDesignPlanDetailDto> sonDtos = dto.getSonDtos();
            if (ListUtil.isPresent(sonDtos)) {
                for (MilepostDesignPlanDetailDto sonDto : sonDtos) {
                    sonDto.setOrStorage(storageStatus);
                    if (null == sonDto.getId()) {
                        sonDto.setOrStorage(Boolean.TRUE);
                    }
                    recursiveSave(project, sonDto, parentDto.getId(), submitRecordId, milepostId, userBy, ++level, storageId, storageStatus,
                            uploadPathIdSet);
                }
            }
        } else {
            List<MilepostDesignPlanDetailDto> sonDtos = dto.getSonDtos();
            if (ListUtil.isPresent(sonDtos)) {
                for (MilepostDesignPlanDetailDto sonDto : sonDtos) {
                    sonDto.setOrStorage(storageStatus);
                    if (null == sonDto.getId()) {
                        sonDto.setOrStorage(Boolean.TRUE);
                    }
                    recursiveSave(project, sonDto, sonDto.getParentId(), submitRecordId, milepostId, userBy, ++level, storageId, storageStatus,
                            uploadPathIdSet);
                }
            }
        }

        return true;
    }

    @Override
    public MilepostDesignPlanDetailDto getDesignPlanDetailForBusiness(MilepostDesignPlanDetailDto dto, Long id, Boolean haveSon) {
        MilepostDesignPlanDetailDto result = new MilepostDesignPlanDetailDto();

        //如果有模糊查询
        if (dto != null && dto.getDetailByProjectIdAndMilepostIdNewWbsQuery() != null) {
            result = this.getDesignPlanDetailWithSonsById(null, id, haveSon,
                    dto.getDetailByProjectIdAndMilepostIdNewWbsQuery());
        } else {
            //没模糊查询
            result = this.getDesignPlanDetailWithSonsById(null, id, haveSon, null);
        }

   /*     //获取该父级以及下面所有的下级数据
        List<MilepostDesignPlanDetailDto> list = this.getDesignPlanDetail(id);
        MilepostDesignPlanDetailDto result =list.get(0);*/

        //业务逻辑:交付物层次,去掉status为空的数据(发布但没有走审批流程的废弃数据)
        if (null != result) {
            //handleMilepostDesignPlanDetail(result);
            //统计需求量和已生成采购需求量
            countRequirementNum(result, BigDecimal.ONE);
/*            MilepostDesignPlanDetailDto milepostDesignPlanDetailDto = result.getSonDtos().get(0);
            countRequirementNum2(milepostDesignPlanDetailDto, BigDecimal.ONE);*/
        }
        return result;
    }

    @Override
    public MilepostDesignPlanDetailDto getDesignPlanDetailForBusinessNew(MilepostDesignPlanDetailDto dto, Long id,
                                                                         Boolean haveSon,
                                                                         DetailByProjectIdAndMilepostIdNewWbsQuery query) {
        MilepostDesignPlanDetailDto result = this.getDesignPlanDetailWithSonsById(dto, id, haveSon, query);
        //移除草稿状态
        List<MilepostDesignPlanDetailDto> sons = new ArrayList<>();
        if (ListUtils.isNotEmpty(result.getSonDtos())) {
            for (MilepostDesignPlanDetailDto sonDesignPlanDetail : result.getSonDtos()) {
                if (null != sonDesignPlanDetail.getProjectBudgetMaterialId() && (null == sonDesignPlanDetail.getWhetherModel() || Boolean.FALSE == sonDesignPlanDetail.getWhetherModel())) {
                    getDesignPlanDetailForBusinessNew(sonDesignPlanDetail, sonDesignPlanDetail.getId(), haveSon, null);
                }
                sons.add(sonDesignPlanDetail);
            }
        }

        result.setSonDtos(sons);
        if (null != result) {
            //统计需求量和已生成采购需求量
            countRequirementNum(result, BigDecimal.ONE);
        }
        return result;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> getDesignPlanDetail(Long id) {
        //MilepostDesignPlanDetailDto dto = new MilepostDesignPlanDetailDto();
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = milepostDesignPlanDetailExtMapper.selectById(id);
        List<MilepostDesignPlanDetailDto> list = buildTree(designPlanDetailDtos, id);
        //dto.setSonDtos(list);
        return list;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> selectMilepostDesignPlanDtoInfo(MilepostDesignPlanDetail detail) {
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        MilepostDesignPlanDetailExample.Criteria c = example.createCriteria();
        if (null != detail.getProjectId()) {
            c.andProjectIdEqualTo(detail.getProjectId());
        }
        if (null != detail.getMilepostId()) {
            c.andMilepostIdEqualTo(detail.getMilepostId());
        }
        if (null != detail.getProjectBudgetMaterialId()) {
            c.andProjectBudgetMaterialIdEqualTo(detail.getProjectBudgetMaterialId());
        }
        if (null != detail.getParentId()) {
            c.andParentIdEqualTo(detail.getParentId());
        }
        List<MilepostDesignPlanDetail> list = milepostDesignPlanDetailMapper.selectByExample(example);
        List<MilepostDesignPlanDetailDto> datas = new ArrayList<>();
        if (ListUtils.isNotEmpty(list)) {
            Project project = projectService.selectByPrimaryKey(list.get(0).getProjectId());
            for (MilepostDesignPlanDetail d : list) {
                MilepostDesignPlanDetailDto planDetail = BeanConverter.copy(d, MilepostDesignPlanDetailDto.class);
                MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
                example2.createCriteria().andParentIdEqualTo(d.getId());
                List<MilepostDesignPlanDetail> list2 = milepostDesignPlanDetailMapper.selectByExample(example2);
                if (ListUtils.isNotEmpty(list2)) {
                    List<MilepostDesignPlanDetailDto> sonDtos = BeanConverter.copy(list2,
                            MilepostDesignPlanDetailDto.class);
                    batchSetNumSum(sonDtos, project);
                    planDetail.setSonDtos(sonDtos);
                }
                datas.add(planDetail);
            }
            batchSetNumSum(datas, project);
        }
        return datas;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> buildTree(List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos, Long parentId) {
        // 根节点
        List<MilepostDesignPlanDetailDto> rootDetailDtos = new ArrayList<MilepostDesignPlanDetailDto>();

        // 组装所有的根节点
        for (MilepostDesignPlanDetailDto detailDto : milepostDesignPlanDetailDtos) {
            if (detailDto.getParentId() == -1) {
                rootDetailDtos.add(detailDto);
            }
            logger.info("详细设计id" + detailDto.getId());
            if (Objects.equals(detailDto.getId(), parentId)) {
                rootDetailDtos.add(detailDto);
            }
        }
        //为根节点设置子节点，getClild递归调用
        for (MilepostDesignPlanDetailDto rootDetailDto : rootDetailDtos) {
            // 获取根节点下面的所有子节点，使用getClild方法
            List<MilepostDesignPlanDetailDto> childList = getSons(rootDetailDto.getId(), milepostDesignPlanDetailDtos);
            //给根节点设置子节点
            rootDetailDto.setSonDtos(childList);
        }

        return rootDetailDtos;

    }

    private List<MilepostDesignPlanDetailDto> getSons(Long id, List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos) {
        // 子节点
        List<MilepostDesignPlanDetailDto> childList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto detailDtoAll : milepostDesignPlanDetailDtos) {
            // 遍历所有节点，将所有节点的父id与传过来的根节点的id进行比较，相等则说明：为该根节点的子节点
            if (Objects.equals(detailDtoAll.getParentId(), id)) {
                childList.add(detailDtoAll);
            }
        }

        // 进行递归
        for (MilepostDesignPlanDetailDto dto : childList) {
            dto.setSonDtos(getSons(dto.getId(), milepostDesignPlanDetailDtos));
        }

        //如果节点下没有子节点，返回一个空List（递归退出)
        if (childList.size() == 0) {
            return new ArrayList<>();
        }
        return childList;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> getDesignPlanDetailForStaging(Long submitRecordId) {
        MilepostDesignPlanDetailDto query = new MilepostDesignPlanDetailDto();
        query.setDeletedFlag(Boolean.FALSE);
        query.setSubmitRecordId(submitRecordId);
        // 找出所有数据
        final List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtoAll = this.selectList(query);
        // 根节点
        List<MilepostDesignPlanDetailDto> rootDetailDtos = new ArrayList<MilepostDesignPlanDetailDto>();

        // 组装所有的根节点
        for (MilepostDesignPlanDetailDto detailDto : milepostDesignPlanDetailDtoAll) {
            if (detailDto.getParentId() == -1) {
                rootDetailDtos.add(detailDto);
            }
        }
        //为根节点设置子节点，getClild递归调用
        for (MilepostDesignPlanDetailDto rootDetailDto : rootDetailDtos) {
            // 获取根节点下面的所有子节点，使用getClild方法
            List<MilepostDesignPlanDetailDto> childList = getChild(rootDetailDto.getId(),
                    milepostDesignPlanDetailDtoAll);
            //给根节点设置子节点
            rootDetailDto.setSonDtos(childList);
        }

        return rootDetailDtos;
    }

    /**
     * 获取子节点
     *
     * @param id
     * @param milepostDesignPlanDetailDtoAll
     * @return 每个根节点下，所有子节点列表
     */
    public List<MilepostDesignPlanDetailDto> getChild(Long id,
                                                      List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtoAll) {
        // 子节点
        List<MilepostDesignPlanDetailDto> childList = new ArrayList<MilepostDesignPlanDetailDto>();
        for (MilepostDesignPlanDetailDto detailDtoAll : milepostDesignPlanDetailDtoAll) {
            // 遍历所有节点，将所有节点的父id与传过来的根节点的id进行比较，相等则说明：为该根节点的子节点
            if (detailDtoAll.getParentId().equals(id)) {
                childList.add(detailDtoAll);
            }
        }

        // 进行递归
        for (MilepostDesignPlanDetailDto dto : childList) {
            dto.setSonDtos(getChild(dto.getId(), milepostDesignPlanDetailDtoAll));
        }

        //如果节点下没有子节点，返回一个空List（递归退出)
        if (childList.size() == 0) {
            return new ArrayList<MilepostDesignPlanDetailDto>();
        }
        return childList;
    }


    @Override
    public void countRequirementNum(MilepostDesignPlanDetailDto dto, BigDecimal preNumber) {
        BigDecimal parentNumber = dto.getNumber() != null ? dto.getNumber().multiply(preNumber) : new BigDecimal("0");
        if (ListUtil.isPresent(dto.getSonDtos())) {
            Iterator<MilepostDesignPlanDetailDto> iterator = dto.getSonDtos().iterator();
            while (iterator.hasNext()) {
                MilepostDesignPlanDetailDto son = iterator.next();
                //根据物料类型走昆山逻辑否则走机器人原有逻辑20210511
                if (ListUtil.isPresent(son.getSonDtos()) || ("装配件".equals(son.getMaterialCategory())) || ("虚拟件".equals(son.getMaterialCategory()))) {
                    countRequirementNum(son, parentNumber);
                    son.setRequirementNum(null);//模块不需要统计
                } else {
                    //需求数量
                    son.setTotalNum(son.getNumber().multiply(parentNumber));
                }
            }
        }
    }

    @Override
    public MilepostDesignPlanDetailDto getDesignPlanDetailWithSonsById(MilepostDesignPlanDetailDto dto, Long id,
                                                                       Boolean haveSon,
                                                                       DetailByProjectIdAndMilepostIdNewWbsQuery query) {
        if (null == dto && null == id) {
            return null;
        }
        if (null == dto) {
            dto = getById(id);
            if (query != null && !"-1".equals(dto.getParentId())) {
                MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
                MilepostDesignPlanDetailExample.Criteria criteria = example.createCriteria();
                criteria.andIdEqualTo(id);
                if (query.getMaterielDescr() != null) {
                    criteria.andMaterielDescrLike(query.getMaterielDescr());
                }
                if (query.getPamCode() != null) {
                    criteria.andPamCodeLike(query.getPamCode());
                }
                if (query.getErpCode() != null) {
                    criteria.andErpCodeLike(query.getErpCode());
                }
                if (query.getWbsLayer() != null) {
                    criteria.andWbsLayerLike(query.getWbsLayer());
                }
                if (query.getWbsSummaryCode() != null) {
                    criteria.andWbsSummaryCodeLike(query.getWbsSummaryCode());
                }
                if (query.getModuleStatus() != null) {
                    criteria.andModuleStatusEqualTo(query.getModuleStatus());
                }
                List<MilepostDesignPlanDetail> milepostDesignPlanDetailList =
                        milepostDesignPlanDetailMapper.selectByExample(example);
                if (milepostDesignPlanDetailList != null && milepostDesignPlanDetailList.size() == 1) {
                    MilepostDesignPlanDetail entity = milepostDesignPlanDetailList.get(0);
                    dto = BeanConverter.copy(entity, MilepostDesignPlanDetailDto.class);
                }
            }
        }
        if (null == haveSon) {
            haveSon = true;
        }
        if (haveSon) {
            MilepostDesignPlanDetailDto designPlanDetailDto = new MilepostDesignPlanDetailDto();
            designPlanDetailDto.setParentId(id);
            designPlanDetailDto.setDeletedFlag(false);
            if (query != null) {
                if (query.getMaterielDescr() != null) {
                    designPlanDetailDto.setMaterielDescr(query.getMaterielDescr());
                }
                if (query.getPamCode() != null) {
                    designPlanDetailDto.setPamCode(query.getPamCode());
                }
                if (query.getErpCode() != null) {
                    designPlanDetailDto.setErpCode(query.getErpCode());
                }
                if (query.getWbsLayer() != null) {
                    designPlanDetailDto.setWbsLayer(query.getWbsLayer());
                }
                if (query.getWbsSummaryCode() != null) {
                    designPlanDetailDto.setWbsSummaryCode(query.getWbsSummaryCode());
                }
                if (query.getModuleStatus() != null) {
                    designPlanDetailDto.setModuleStatus(query.getModuleStatus());
                }
            }
            List<MilepostDesignPlanDetailDto> sonDtos = milepostDesignPlanDetailExtMapper.selectList(designPlanDetailDto);

            if (ListUtil.isPresent(sonDtos)) {
                List<Long> sonIds = ListUtil.map(sonDtos, "id");
                MilepostDesignPlanDetailDto haveSonIdQuery = new MilepostDesignPlanDetailDto();
                haveSonIdQuery.setParentIds(sonIds);
                haveSonIdQuery.setDeletedFlag(false);
                List<MilepostDesignPlanDetailDto> haveSonDtos = this.selectList(haveSonIdQuery);
                List<Long> haveSonIds = ListUtil.map(haveSonDtos, "parentId");
                for (MilepostDesignPlanDetailDto sonDto : sonDtos) {
                    //二级模组已确认状态时，把该状态赋值给三级物料
                    if (null != dto.getModuleStatus()) {
                        if (Objects.equals(dto.getModuleStatus(), new Integer(1))) {
                            sonDto.setModuleStatus(dto.getModuleStatus());
                        }
                    }
                    if (ListUtils.isNotEmpty(haveSonIds)) {
                        if (ListUtil.isPresent(haveSonIds) && haveSonIds.contains(sonDto.getId())) {
                            //封装当前sonDto的子层数据
                            getDesignPlanDetailWithSonsById(sonDto, sonDto.getId(), true, null);
                        }
                    }
                }
                dto.setSonDtos(sonDtos);
            }
        } else {
            MilepostDesignPlanDetailDto querys = new MilepostDesignPlanDetailDto();
            querys.setParentId(id);
            querys.setDeletedFlag(false);
            List<MilepostDesignPlanDetailDto> sonDtos = this.selectList(querys);

            if (ListUtil.isPresent(sonDtos)) {
                dto.setSonDtos(sonDtos);
            }
        }
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBatch(List<MilepostDesignPlanDetailDto> dtos, Long userBy) {
        for (MilepostDesignPlanDetailDto dto : dtos) {
            this.save(dto, userBy);
        }
        return true;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> getTheFirstLevelDetailByMilepostId(Long milepostId) {
        MilepostDesignPlanDetailDto milepostDesignPlanDetailQuery = new MilepostDesignPlanDetailDto();
        milepostDesignPlanDetailQuery.setMilepostId(milepostId);
        milepostDesignPlanDetailQuery.setParentId(-1L);
        milepostDesignPlanDetailQuery.setDeletedFlag(DeletedFlag.VALID.code());
        return this.selectList(milepostDesignPlanDetailQuery);
    }

    @Override
    public List<MilepostDesignPlanDetailDto> getTheFirstLevelDetailByProjectId(DetailByProjectIdAndMilepostIdNewWbsQuery query) {
        MilepostDesignPlanDetailDto milepostDesignPlanDetailQuery = new MilepostDesignPlanDetailDto();
        milepostDesignPlanDetailQuery.setProjectId(query.getProjectId());
        milepostDesignPlanDetailQuery.setParentId(-1L);
        milepostDesignPlanDetailQuery.setDeletedFlag(DeletedFlag.VALID.code());
        List<MilepostDesignPlanDetailDto> list = this.selectList(milepostDesignPlanDetailQuery);
        return list;
    }


    @Override
    public List<MilepostDesignPlanDetailDto> ksMatchingMaterialCodeEstimate(List<MilepostDesignPlanDetailKsImportExcelVo> ksDetailExcelVos,
                                                                            Long storageId, Long unitId, String remindType) {
        //Long organizationId = projectService.ksGetOrganizationIdByProjectId(projectId); //根据项目id查询库存组织
        /*//交易日期格式
        Integer line = new Integer(3);
        for(MilepostDesignPlanDetailKsImportExcelVo vo:ksDetailExcelVos){
            if(StringUtils.isNotEmpty(vo.getDeliveryTimeStr())){
                if(DateUtils.isValidDate(vo.getDeliveryTimeStr(),DateUtils.FORMAT_SHORT)){
                    vo.setDeliveryTime(DateUtils.parse(vo.getDeliveryTimeStr(),DateUtils.FORMAT_SHORT));
                }else{
                    throw new IllegalArgumentException("第" + line + "行日期格式正确");
                }
            }
            line++;
        }*/
        Map<String, Object> paramUnit = new HashMap<>(1);
        paramUnit.put("unitId", unitId);
        String getMaterialCodeRuleUrl = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/material/v1" +
                "/getMaterialCodeRuleConfig", paramUnit);
        String getMaterialCodeRuleRes = restTemplate.getForObject(getMaterialCodeRuleUrl, String.class);
        DataResponse<String> materialCodeRuleResponse = JSON.parseObject(getMaterialCodeRuleRes,
                new TypeReference<DataResponse<String>>() {
                });
        String materialCodeRule = materialCodeRuleResponse.getData(); //当前单位的物料编码规则
        MaterialCodeRuleEnum materialCodeRuleEnum = MaterialCodeRuleEnum.getEnumByCode(materialCodeRule);
        if (materialCodeRuleEnum == null) {
            throw new BizException(100, materialCodeRule);
        }

        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = BeanConverter.copy(ksDetailExcelVos,
                MilepostDesignPlanDetailDto.class);
        Integer index = new Integer(3);
        //获取"机械加工件"对应"加工件分类"的属性
        List<DictDto> dicts = basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null);
        Assert.notEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");

        List<MerielSubclassVO> merielSubclassList =
                erpCodeRuleService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUNSHAN.getCode());//昆山

        // 获取类别列表数据
        //List<MerielSubclassVO> merielSubclassListKunshan = erpCodeRuleService.findErpCodeRuleByRuleName
        // (MaterialCodeRuleEnum.KUNSHAN.getCode());//昆山

        //List<MerielSubclassVO> merielSubclassListKuka = erpCodeRuleService.findErpCodeRuleByRuleName
        // (MaterialCodeRuleEnum.KUKA.getCode());//机器人

        if (MaterialCodeRuleEnum.KUKAMIA.getCode().equals(materialCodeRule)) {
            merielSubclassList = erpCodeRuleService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUKAMIA.getCode());//昆山
        }

        // 物料类别Table数据（row小类，column中类，value实体类）
        HashBasedTable<String, String, MerielSubclassVO> merielClassTable = HashBasedTable.create();
        for (MerielSubclassVO merielSubclassVO : merielSubclassList) {
            merielClassTable.put(merielSubclassVO.getValue(), merielSubclassVO.getMiddileValue(), merielSubclassVO);
        }
        // 获取计量单位
        Map<String, String> unitMap =
                basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));
        // 获取备件标识组织参数配置
        Set<String> orSparePartsMaskSet = organizationCustomDictService.queryByName(Constants.SPAREPARTS_MASK, unitId
                , OrgCustomDictOrgFrom.COMPANY);
        // 获取物料类型及编码字典
        Byte a = 1;
        Map<String, DictDto> materialTypeMap =
                basedataExtService.getLtcDict(MATERIAL_TYPE, null, null).stream().filter(o -> !Objects.equals(a,
                        o.getOrderNum())).collect(Collectors.toMap(DictDto::getName, Function.identity(), (key1,
                                                                                                           key2) -> key2));
        if (null == orSparePartsMaskSet || orSparePartsMaskSet.isEmpty()) {
            throw new BizException(100, "备件标识需要在组织参数中维护");
        }
        if (ListUtils.isNotEmpty(designPlanDetailDtos)) {
            for (MilepostDesignPlanDetailDto dto : designPlanDetailDtos) {

                MilepostDesignPlanOldDto milepostDesignPlanOldDto = BeanConverter.copy(dto,
                        MilepostDesignPlanOldDto.class);
                if (!org.apache.commons.lang3.StringUtils.isBlank(milepostDesignPlanOldDto.getUnit()) && unitMap.containsKey(dto.getUnit())) {
                    milepostDesignPlanOldDto.setUnitCode(unitMap.get(dto.getUnit()));
                }
                dto.setMilepostDesignPlanOldDto(milepostDesignPlanOldDto);

                if (null != dto.getPamCode()) { //如果导入文件已经有PAM物料编码，则根据PAM编码和库存组织id去物料表查询并带出其他属性,不需校验其他属性。
                    if (dto.getNumber() == null) {
                        boolean flag = "excel".equalsIgnoreCase(remindType);
                        if (flag) {
                            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) + "，数量不能为空");
                        } else {
                            throw new IllegalArgumentException("第" + index + "行数量不能为空");
                        }
                    }
                    String code = dto.getPamCode();
                    final Map<String, Object> param = new HashMap<>();
                    param.put("pamCode", code);
                    param.put("organizationId", storageId);
                    String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material" +
                            "/selectByPamCodeAndOrganizationId", param);
                    String res = restTemplate.getForEntity(url, String.class).getBody();
                    DataResponse<List<Material>> response = JSON.parseObject(res,
                            new TypeReference<DataResponse<List<Material>>>() {
                            });
                    List<Material> data = response.getData();
                    if (data.isEmpty()) {
                        if ("excel".equalsIgnoreCase(remindType)) {
                            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) + "，导入的PAM编码有误");
                        } else {
                            throw new IllegalArgumentException("第" + index + "行导入的PAM编码有误");
                        }
                    } else {
                        Material material = response.getData().get(0);
                        dto = setDto(material, dto);
                    }
                    index++;
                } else {
                    this.checkDto(dto, index, dicts, merielClassTable, orSparePartsMaskSet, unitMap, materialTypeMap,
                            remindType); //校验导入数据
                    index++;
                    //判断物料的唯一性
                    if (null != dto.getBrandMaterialCode()) { // 有品牌商物料编码时，根据 品牌商物料编码+品牌带出
                        Material material = materialExtService.judgeUniqueCheckType4(dto.getBrand(),
                                dto.getBrandMaterialCode(), storageId);
                        if (material != null) {
                            logger.info("第{}行物料基础表已存在品牌：{}，品牌商物料编码：{}，库存组织：{}物料，直接引用", index, dto.getBrand(),
                                    dto.getBrandMaterialCode(), storageId);
                            dto.setPamCode(material.getPamCode());
                            dto.setErpCode(material.getItemCode());
                            dto = setDto(material, dto);
                        } else {
                            dto.setPamCode(materialExtService.generateMaterialPAMCode());
                        }
                    } else {
                        if ("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory())) {
                            MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(),
                                    dto.getCodingMiddleClass());
                            if (merielSubclassVO != null && Objects.equals(2, merielSubclassVO.getCheckType())) {
                                Material material = materialExtService.judgeUniqueCheckType2(dto.getBrand(),
                                        dto.getModel(), dto.getBrandMaterialCode(), storageId);
                                if (material != null) {
                                    logger.info("第{}行物料基础表已存在品牌：{}，型号：{}，品牌商物料编码：{}，库存组织：{}物料，直接引用", index,
                                            dto.getBrand(), dto.getModel(), dto.getBrandMaterialCode(), storageId);
                                    dto.setPamCode(material.getPamCode());
                                    dto.setErpCode(material.getItemCode());
                                    dto = setDto(material, dto);

                                }
                            }
                            if (merielSubclassVO != null && Objects.equals(3, merielSubclassVO.getCheckType())) {
                                Material material = materialExtService.judgeUniqueCheckType3(dto.getFigureNumber(),
                                        dto.getChartVersion(), storageId);
                                if (material != null) {
                                    logger.info("第{}行物料基础表已存在图号：{}，图纸版本号：{}，库存组织：{}物料，直接引用", index,
                                            dto.getFigureNumber(), dto.getChartVersion(), storageId);
                                    dto.setPamCode(material.getPamCode());
                                    dto.setErpCode(material.getItemCode());
                                    dto = setDto(material, dto);
                                } else {
                                    dto.setPamCode(materialExtService.generateMaterialPAMCode());
                                }
                            }

                            // 未自动带出PAM_CODE的外购物料看板物料则不处理，有前端页面生成PAM_CODE
                        /*if (StringUtils.isEmpty(dto.getPamCode())) {
                            dto.setPamCode(materialExtService.generateMaterialPAMCode());
                        }*/
                        } else {
                            // 装配件、虚拟件唯一性校验（名称+图号+图纸版本号+库存组织），不唯一带出pamCode、erpCode，否则生成pamCode
                            if (StringUtils.isNotEmpty(dto.getFigureNumber()) && StringUtils.isNotEmpty(dto.getChartVersion())) {
                                Material material = materialExtService.judgeAssemblyPartUnique(dto.getName(),
                                        dto.getFigureNumber(), dto.getChartVersion(), storageId);
                                if (material != null) {
                                    logger.info("第{}行物料基础表已存在名称：{}，图号：{}，图纸版本号：{}，库存组织：{}物料，直接引用", index,
                                            dto.getName(), dto.getFigureNumber(), dto.getChartVersion(), storageId);
                                    dto.setPamCode(material.getPamCode());
                                    dto = setDto(material, dto);
                                } else {
                                    dto.setPamCode(materialExtService.generateMaterialPAMCode());
                                }
                            } else {
                                dto.setPamCode(materialExtService.generateMaterialPAMCode());
                            }
                        }
                    }
                }
                if (null == dto.getMaterielDescr()) {
                    ksGenerateMaterielDescr(dto);//物料描述
                }
                MaterialCostDto materialCostDto = materialCostExtService.matchingDesignCost(dto.getPamCode(),
                        storageId);
                if (Objects.nonNull(materialCostDto)) {
                    if (null != materialCostDto.getMaterialCategory() && Objects.equals(materialCostDto.getMaterialCategory(),
                            dto.getMaterialCategory())) { //因为原来的估价表没有物料类型的字段，因此不带出非昆山物料的字段属性
                        dto.setDesignCostId(materialCostDto.getId());
                        dto.setDesignCost(materialCostDto.getItemCost());
                        dto.setUnitCode(materialCostDto.getUnit());
                        dto.setUnit(materialCostDto.getUnitName());
                        dto.setMaterielType(materialCostDto.getMaterielType());
                        dto.setMachiningPartType(materialCostDto.getMachiningPartType());
                        dto.setMaterial(materialCostDto.getMaterial());
                        dto.setUnitWeight(materialCostDto.getUnitWeight());
                        dto.setMaterialProcessing(materialCostDto.getMaterialProcessing());
                        //昆山迭代5需求增加相关字段
                        //dto.setMaterialCategory(materialCostDto.getMaterialCategory());//物料类型
                        dto.setCodingMiddleClass(materialCostDto.getCodingMiddleclass());//物料中类
                        dto.setFigureNumber(materialCostDto.getFigureNumber());//图号
                        dto.setChartVersion(materialCostDto.getChartVersion());//图纸版本号
                        dto.setBrandMaterialCode(materialCostDto.getBrandMaterialCode());//品牌商物料编码
                        dto.setOrSparePartsMask(materialCostDto.getOrSparePartsMask());//备件标识
                        dto.setBrand(materialCostDto.getBrand()); //品牌
                        dto.setName(materialCostDto.getName()); //名称
                        dto.setModel(materialCostDto.getModel()); //规格型号
                        dto.setMaterielDescr(materialCostDto.getDescr()); // 物料描述
                    }
                }
            }
        }


        return designPlanDetailDtos;
    }

    private MilepostDesignPlanDetailDto setDto(Material material, MilepostDesignPlanDetailDto dto) {
        dto.setMaterielDescr(material.getItemInfo()); // 物料描述
        dto.setUnitCode(material.getUnit());//基本计量单位
        if (null != material.getUnit()) {
            DictDto unit = CacheDataUtils.findDictByTypeAndCode(DictType.MEASUREMENT_UNIT.code(), material.getUnit());
            if (null != unit) {
                dto.setUnit(unit.getName());
            }
        }
        dto.setMaterielType(material.getMaterialType()); //物料小类
        dto.setErpCode(material.getItemCode()); //erp编码
        dto.setMaterialClassification(material.getMaterialClassification()); //物料大类
        dto.setName(material.getName()); //名称
        dto.setModel(material.getModel()); //规格型号
        dto.setBrand(material.getBrand()); //品牌
        dto.setMachiningPartType(material.getMachiningPartType());//加工件分类
        dto.setMaterial(material.getMaterial());//材料
        dto.setMaterialProcessing(material.getMaterialProcessing());//材质处理
        dto.setUnitWeight(material.getUnitWeight());//单位重量

        dto.setMaterialCategory(material.getMaterialCategory()); //物料类型
        dto.setCodingMiddleClass(material.getCodingMiddleclass()); //物料中类
        dto.setFigureNumber(material.getFigureNumber()); //图号
        dto.setChartVersion(material.getChartVersion()); //图纸版本
        dto.setBrandMaterialCode(material.getBrandMaterialCode());  //品牌商物料编码
        dto.setOrSparePartsMask(material.getOrSparePartsMask()); //备件标识
        dto.setPerchasingLeadtime(StringUtils.isEmpty(material.getBuyerRound()) ? null :
                Long.parseLong(material.getBuyerRound())); //采购提前期
        dto.setMinPerchaseQuantity(material.getMinimumOrderQuantity()); //最小订货量（最小采购量）
        dto.setMinPackageQuantity(StringUtils.isEmpty(material.getFixedLotMultiplier()) ? null :
                Long.parseLong(material.getFixedLotMultiplier())); //固定批次增加（最小包装量）

        if (StringUtils.isNotEmpty(dto.getDeliveryTimeStr())) {
            if (DateUtils.isValidDate(dto.getDeliveryTimeStr(), FORMAT_SHORT)) {
                dto.setDeliveryTime(DateUtils.parse(dto.getDeliveryTimeStr(), FORMAT_SHORT));
            } else {
                dto.setValidResult("物料到货日期格式不正确");
            }
        }

        return dto;

    }


    @Override
    public List<MilepostDesignPlanDetailDto> matchingMaterialCodeEstimate(List<MilepostDesignPlanDetailImportExcelVo> detailExcelVos,
                                                                          String remindType) throws UnsupportedEncodingException {
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = BeanConverter.copy(detailExcelVos,
                MilepostDesignPlanDetailDto.class);
        // 获取计量单位
        Map<String, String> unitMap =
                basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));
        // 获取类别列表数据
        List<MerielSubclassVO> merielSubclassList =
                erpCodeRuleService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUKAMIA.getCode());
        // 物料类别Table数据（row小类，column中类，value实体类）
        HashBasedTable<String, String, MerielSubclassVO> merielClassTable = HashBasedTable.create();
        for (MerielSubclassVO merielSubclassVO : merielSubclassList) {
            merielClassTable.put(merielSubclassVO.getValue(), merielSubclassVO.getMiddileValue(), merielSubclassVO);
        }

        if (ListUtils.isNotEmpty(designPlanDetailDtos)) {
            Long organizationId =
                    projectService.getOrganizationIdByProjectId(designPlanDetailDtos.get(0).getProjectId());
            for (MilepostDesignPlanDetailDto dto : designPlanDetailDtos) {
                generateMaterielDescr(dto);//物料描述
                checkImport(dto, remindType, merielClassTable); //检查导入的excel
                //
                matchingMaterialUnitCode(dto, remindType, unitMap); //匹配物料单位编码
                matchingMaterialCodeAsync(dto, organizationId, remindType, unitMap);
                /*boolean matchFlag = matchingMaterialCode(dto); //匹配物料编码
                logger.info("物料信息：{}-{}, 匹配物料编码结果：{}，pamCode：{}", dto.getId(), dto.getName(), matchFlag, dto
                .getPamCode());
                MaterialCostDto materialCostDto = materialCostExtService.matchingDesignCost(dto.getPamCode(),
                organizationId);
                if (Objects.nonNull(materialCostDto)) {
                    dto.setDesignCostId(materialCostDto.getId());
                    dto.setDesignCost(materialCostDto.getItemCost());
                    dto.setUnitCode(materialCostDto.getUnit());
                    dto.setUnit(materialCostDto.getUnitName());
                    dto.setMaterielType(materialCostDto.getMaterielType());
                    dto.setMachiningPartType(materialCostDto.getMachiningPartType());
                    dto.setMaterial(materialCostDto.getMaterial());
                    dto.setUnitWeight(materialCostDto.getUnitWeight());
                    dto.setMaterialProcessing(materialCostDto.getMaterialProcessing());
                } else {
                    matchingMaterialUnitCode(dto, remindType, unitMap); //匹配物料单位编码
                }*/

            }
        }

        return designPlanDetailDtos;
    }

    private void generateMaterielDescr(MilepostDesignPlanDetailDto dto) {
        StringBuffer materielDescr = new StringBuffer();
        if (StringUtils.isNotEmpty(dto.getBrand())) {
            materielDescr.append(dto.getBrand());
            materielDescr.append(" ");
        }
        if (StringUtils.isNotEmpty(dto.getName())) {
            materielDescr.append(dto.getName());
            materielDescr.append(" ");
        }
        if (StringUtils.isNotEmpty(dto.getModel())) {
            materielDescr.append(dto.getModel());
        }
        dto.setMaterielDescr(materielDescr.toString());
    }

    private void ksGenerateMaterielDescr(MilepostDesignPlanDetailDto dto) {
        if ("装配件".equals(dto.getMaterialCategory())) { //先写死"装配件",后续修改
            dto.setMaterielDescr(dto.getName());
        } else {
            StringBuffer materielDescr = new StringBuffer();
            if (StringUtils.isNotEmpty(dto.getName())) {
                materielDescr.append(dto.getName());
                materielDescr.append("#");
            }
            if (StringUtils.isNotEmpty(dto.getFigureNumber())) {
                materielDescr.append(dto.getFigureNumber());
                materielDescr.append("#");
            }
            if (StringUtils.isNotEmpty(dto.getChartVersion())) {
                materielDescr.append(dto.getChartVersion());
                materielDescr.append("#");
            }
            if (StringUtils.isNotEmpty(dto.getModel())) {
                materielDescr.append(dto.getModel());
                materielDescr.append("#");
            }
            if (StringUtils.isNotEmpty(dto.getBrand())) {
                materielDescr.append(dto.getBrand());
                materielDescr.append("#");
            }
            if (StringUtils.isNotEmpty(dto.getBrandMaterialCode())) {
                materielDescr.append(dto.getBrandMaterialCode());
            }
            dto.setMaterielDescr(materielDescr.toString());
        }
    }

    @Override
    public AsyncRequestResult matchingMaterialCodeEstimateAsync(List<MilepostDesignPlanDetailImportExcelVo> detailExcelVos) throws UnsupportedEncodingException {
        this.checkDetailExcelVos(detailExcelVos);
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult = asyncRequestResultService.add(asyncRequestResult);
        applicationEventPublisher.publishEvent(new DesignPlanDetailMatchResultEvent(this, asyncRequestResult.getId(),
                detailExcelVos));
        return asyncRequestResult;
    }

    @Override
    public AsyncRequestResult ksMatchingMaterialCodeEstimateAsync(List<MilepostDesignPlanDetailKsImportExcelVo> ksDetailExcelVos, Long storageId,
                                                                  Long unitId) throws UnsupportedEncodingException {
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult = asyncRequestResultService.add(asyncRequestResult);
        applicationEventPublisher.publishEvent(new KsDesignPlanDetailMatchResultEvent(this,
                asyncRequestResult.getId(), ksDetailExcelVos, storageId, unitId));
        return asyncRequestResult;
    }

    private void checkDto(MilepostDesignPlanDetailDto dto, Integer index, List<DictDto> dicts, HashBasedTable<String,
            String, MerielSubclassVO> merielClassTable,
                          Set<String> orSparePartsMaskSet, Map<String, String> unitMap,
                          Map<String, DictDto> materialTypeMap, String remindType) {
        logger.info("materialCategory：{}", dto.getMaterialCategory());
        boolean flag = "excel".equalsIgnoreCase(remindType);
        if (null != dto.getMaterialCategory()) {
            DictDto materialTypeDict = materialTypeMap.get(dto.getMaterialCategory());
            if (null == materialTypeDict) {
                if (flag) {
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) +
                            "物料类型为字典项material_type中序号不为“1”对应的值");
                } else {
                    throw new IllegalArgumentException("第" + index + "行物料类型为字典项material_type中序号不为“1”对应的值");
                }
            }
            if (materialTypeDict != null && ("装配件".equals(materialTypeDict.getName()) || "虚拟件".equals(materialTypeDict.getName()))) {
                checkAssemblyPart(dto, index, merielClassTable, dicts, orSparePartsMaskSet, unitMap, remindType);
                //校验物料类型为“装配件”的数据
            } else {
                checkElse(dto, index, merielClassTable, dicts, orSparePartsMaskSet, unitMap, remindType);
                //校验物料类型为“非装配件”的数据
            }
        } else {
            if (flag) {
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) +
                        "，物料类型不能为空");
            } else {
                throw new IllegalArgumentException("第" + index + "行物料类型不能为空");
            }
        }
    }

    private void checkAssemblyPart(MilepostDesignPlanDetailDto dto, Integer index, HashBasedTable<String, String,
            MerielSubclassVO> merielClassTable,
                                   List<DictDto> dicts, Set<String> orSparePartsMaskSet, Map<String, String> unitMap,
                                   String remindType) {
        boolean flag = "excel".equalsIgnoreCase(remindType);
        if (flag) {
            StringBuffer stringBuffer = new StringBuffer(StringUtils.isEmpty(dto.getValidResult()) ? "" :
                    dto.getValidResult());
            //校验必填项
            if (StringUtil.isNull(dto.getCodingMiddleClass())) {
                stringBuffer.append("，物料中类不能为空");
            } else {
                Map<String, MerielSubclassVO> map = merielClassTable.column(dto.getCodingMiddleClass());
                if (map.isEmpty()) {
                    stringBuffer.append("，物料[" + dto.getName() + " " + dto.getModel() + "]的物料中类不正确");
                } else {
                    for (Map.Entry<String, MerielSubclassVO> entry : map.entrySet()) {
                        dto.setMaterialClassification(entry.getValue().getBigValue());
                    }
                }
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(dto.getMaterielType())) {
                if (merielClassTable.rowKeySet().contains(dto.getMaterielType())) {
                    MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(),
                            dto.getCodingMiddleClass());
                    if (merielSubclassVO == null) {
                        stringBuffer.append("，物料小类对应中类不存在");
                    } else {
                        dto.setMaterialClassification(merielSubclassVO.getBigValue());
                    }
                }
            }
            if (StringUtil.isNull(dto.getBrand())) {
                stringBuffer.append("，品牌不能为空");
            }
            if (StringUtil.isNull(dto.getName())) {
                stringBuffer.append("，名称不能为空");
            }
            if (Objects.isNull(dto.getNumber())) {
                stringBuffer.append("，数量不能为空");
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(dto.getUnit()) || !unitMap.containsKey(dto.getUnit())) {
                stringBuffer.append("，物料[" + dto.getName() + " " + dto.getModel() + "]的单位不能为空且必须为字典项measurement_unit" +
                        "的值");
            } else {
                dto.setUnitCode(unitMap.get(dto.getUnit()));
            }
            if (StringUtil.isNull(dto.getFigureNumber())) {
                stringBuffer.append("，图号不能为空");
            }
            if (StringUtil.isNull(dto.getChartVersion())) {
                stringBuffer.append("，图纸版本号不能为空");
            }
            //校验导入日
            if (StringUtils.isNotEmpty(dto.getDeliveryTimeStr())) {
                if (DateUtils.isValidDate(dto.getDeliveryTimeStr(), FORMAT_SHORT)) {
                    dto.setDeliveryTime(DateUtils.parse(dto.getDeliveryTimeStr(), FORMAT_SHORT));
                } else {
                    stringBuffer.append("，物料到货日期格式不正确");
                }
            }
            dto.setValidResult(stringBuffer.toString());
        } else {
            //校验必填项
            if (StringUtil.isNull(dto.getCodingMiddleClass())) {
                throw new IllegalArgumentException("第" + index + "行物料中类不能为空");
            } else {
                if (!merielClassTable.columnKeySet().contains(dto.getCodingMiddleClass())) {
                    throw new IllegalArgumentException("第" + index + "行物料[" + dto.getName() + " " + dto.getModel() +
                            "]的物料中类不正确");
                }
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(dto.getMaterielType())) {
                if (merielClassTable.rowKeySet().contains(dto.getMaterielType())) {
                    if (merielClassTable.get(dto.getMaterielType(), dto.getCodingMiddleClass()) == null) {
                        throw new IllegalArgumentException("第" + index + "行物料小类对应中类不存在");
                    }
                }
            }
            if (StringUtil.isNull(dto.getBrand())) {
                throw new IllegalArgumentException("第" + index + "行品牌不能为空");
            }
            if (StringUtil.isNull(dto.getName())) {
                throw new IllegalArgumentException("第" + index + "行名称不能为空");
            }
            if (Objects.isNull(dto.getNumber())) {
                throw new IllegalArgumentException("第" + index + "行数量不能为空");
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(dto.getUnit()) || !unitMap.containsKey(dto.getUnit())) {
                throw new IllegalArgumentException("第" + index + "行物料[" + dto.getName() + " " + dto.getModel() +
                        "]的单位不能为空且必须为字典项measurement_unit的值");
            } else {
                dto.setUnitCode(unitMap.get(dto.getUnit()));
            }
            if (StringUtil.isNull(dto.getFigureNumber())) {
                throw new IllegalArgumentException("第" + index + "行图号不能为空");
            }
            if (StringUtil.isNull(dto.getChartVersion())) {
                throw new IllegalArgumentException("第" + index + "行图纸版本号不能为空");
            }
        }
    }

    private void checkElse(MilepostDesignPlanDetailDto dto, Integer index, HashBasedTable<String, String,
            MerielSubclassVO> merielClassTable,
                           List<DictDto> dicts, Set<String> orSparePartsMaskSet, Map<String, String> unitMap,
                           String remindType) {
        boolean flag = "excel".equalsIgnoreCase(remindType);
        StringBuffer stringBuffer = new StringBuffer(StringUtils.isEmpty(dto.getValidResult()) ? "" :
                dto.getValidResult());
        //校验必填项
        if (StringUtil.isNull(dto.getCodingMiddleClass())) {
            if (flag) {
                stringBuffer.append("，物料中类不能为空");
            } else {
                throw new IllegalArgumentException("第" + index + "行物料中类不能为空");
            }
        } else {
            if (!merielClassTable.columnKeySet().contains(dto.getCodingMiddleClass())) {
                if (flag) {
                    stringBuffer.append("，物料[" + dto.getName() + " " + dto.getModel() + "]的物料中类不正确");
                } else {
                    throw new IllegalArgumentException("第" + index + "行物料[" + dto.getName() + " " + dto.getModel() +
                            "]的物料中类不正确");
                }
            }
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(dto.getMaterielType())) {
            if (flag) {
                stringBuffer.append("，物料小类不能为空");
            } else {
                throw new IllegalArgumentException("第" + index + "行物料小类不能为空");
            }
        } else {
            if (merielClassTable.rowKeySet().contains(dto.getMaterielType())) {
                MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(),
                        dto.getCodingMiddleClass());
                if (merielSubclassVO == null) {
                    if (flag) {
                        stringBuffer.append("，物料小类对应中类不存在");
                    } else {
                        throw new IllegalArgumentException("第" + index + "行物料小类对应中类不存在");
                    }
                } else {
                    dto.setMaterialClassification(merielSubclassVO.getBigValue());
                }
            } else {
                if (flag) {
                    stringBuffer.append("，物料小类不存在");
                } else {
                    throw new IllegalArgumentException("第" + index + "行物料小类不存在");
                }
            }
        }
        if (StringUtil.isNull(dto.getBrand())) {
            if (flag) {
                stringBuffer.append("，品牌不能为空");
            } else {
                throw new IllegalArgumentException("第" + index + "行品牌不能为空");
            }
        }
        if (StringUtil.isNull(dto.getName())) {
            if (flag) {
                stringBuffer.append("，名称不能为空");
            } else {
                throw new IllegalArgumentException("第" + index + "行名称不能为空");
            }
        }
        if (StringUtil.isNull(dto.getModel())) {
            if (flag) {
                stringBuffer.append("，规格/型号不能为空");
            } else {
                throw new IllegalArgumentException("第" + index + "行规格/型号不能为空");
            }
        }
        if (Objects.isNull(dto.getNumber())) {
            if (flag) {
                stringBuffer.append("，数量不能为空");
            } else {
                throw new IllegalArgumentException("第" + index + "行数量不能为空");
            }
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(dto.getUnit()) || !unitMap.containsKey(dto.getUnit())) {
            if (flag) {
                stringBuffer.append("，单位不能为空且必须为字典项measurement_unit的值");
            } else {
                throw new IllegalArgumentException("第" + index + "行的单位不能为空且必须为字典项measurement_unit的值");
            }
        } else {
            dto.setUnitCode(unitMap.get(dto.getUnit()));
        }
        if (StringUtil.isNull(dto.getOrSparePartsMask())) {
            if (flag) {
                stringBuffer.append("，备件标识不能为空");
            } else {
                throw new IllegalArgumentException("第" + index + "行备件标识不能为空");
            }
        } else {
            if (!orSparePartsMaskSet.contains(dto.getOrSparePartsMask())) {
                if (flag) {
                    stringBuffer.append("，备件标识不正确");
                } else {
                    throw new IllegalArgumentException("第" + index + "行的备件标识不正确");
                }
            }
        }
        //当物料中类为”机械加工件、外协加工“时，图号，图纸版本号，加工分类，材质，单件重量(Kg)，表面处理为必填项，若未填写，在数据校验环节需要弹出提示“导入文件有误,请重新下载模板填写数据”
        MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(), dto.getCodingMiddleClass());
        if (merielSubclassVO != null && Objects.equals(3, merielSubclassVO.getCheckType())) {
            Map<String, String> errorMap = new HashMap<>();
            if (!org.springframework.util.StringUtils.hasText(dto.getMachiningPartType())) {
                errorMap.put(ERROR_JGJFL_KEY, ERROR_JGJFL);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getMaterial())) {
                errorMap.put(ERROR_CZ_KEY, ERROR_CZ);
            }
            if (Objects.isNull(dto.getUnitWeight())) {
                errorMap.put(ERROR_DWZL_KEY, ERROR_DWZL);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getFigureNumber())) {
                errorMap.put(ERROR_TH_KEY, ERROR_TH);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getChartVersion())) {
                errorMap.put(ERROR_TZBBH_KEY, ERROR_TZBBH);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getMaterialProcessing())) {
                errorMap.put(ERROR_BMCL_KEY, ERROR_BMCL);
            }
            if (!dicts.stream().anyMatch(dict -> dict.getName().equals(dto.getMachiningPartType()))) {
                if (flag) {
                    stringBuffer.append("，物料[" + dto.getName() + " " + dto.getModel() + "]的加工件分类不正确");
                } else {
                    throw new IllegalArgumentException("第" + index + "行物料[" + dto.getName() + " " + dto.getModel() +
                            "]的加工件分类不正确");
                }
            }
            if (!CollectionUtils.isEmpty(errorMap)) {
                StringBuffer buffer = new StringBuffer();
                errorMap.entrySet().forEach(entry -> buffer.append(entry.getValue()).append("、"));
                if (flag) {
                    stringBuffer.append("，物料中类为机械加工件、外协加工时，" + buffer.toString().substring(0, buffer.length() - 1) +
                            "不能为空");
                } else {
                    throw new IllegalArgumentException("第" + index + "行物料中类为机械加工件、外协加工时，" + buffer.toString().substring(0, buffer.length() - 1) +
                            "不能为空");
                }
            }
        }
        //校验导入日
        if (StringUtils.isNotEmpty(dto.getDeliveryTimeStr())) {
            if (flag) {
                if (DateUtils.isValidDate(dto.getDeliveryTimeStr(), FORMAT_SHORT)) {
                    dto.setDeliveryTime(DateUtils.parse(dto.getDeliveryTimeStr(), FORMAT_SHORT));
                } else {
                    stringBuffer.append("，物料到货日期格式不正确");
                }
            }
        }
        dto.setValidResult(stringBuffer.toString());
    }

    private void checkDetailExcelVos(List<MilepostDesignPlanDetailImportExcelVo> detailExcelVos) throws UnsupportedEncodingException {
        //当物料小类为”机械加工件“时，加工件分类、材质、单位重量、材质处理为必填项，若未填写，在数据校验环节需要弹出提示“导入文件有误,请重新下载模板填写数据”
        Asserts.notEmpty(detailExcelVos, ErrorCode.CTC_DESIGN_PLAN_CHANGE_IMPORT_NOT_NULL);
        List<MilepostDesignPlanDetailImportExcelVo> machinedPartDetails =
                detailExcelVos.stream().filter(detail -> MACHINED_PART.equals(detail.getMaterielType())).collect(Collectors.toList());
        Integer index = new Integer(3);
        StringBuilder indexNullExcel = new StringBuilder();
        StringBuilder indexNotNullExcel = new StringBuilder();
        if (!CollectionUtils.isEmpty(machinedPartDetails)) {
            //获取机械加工件对应加工件分类的属性
            List<DictDto> dicts = basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null);
            Assert.notEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");
            Map<String, String> errorMap = new HashMap<>();
            for (MilepostDesignPlanDetailImportExcelVo importDetail : machinedPartDetails) {
                if (!org.springframework.util.StringUtils.hasText(importDetail.getMachiningPartType())) {
                    errorMap.put(ERROR_JGJFL_KEY, ERROR_JGJFL);
                }
                if (!org.springframework.util.StringUtils.hasText(importDetail.getMaterial())) {
                    errorMap.put(ERROR_CZ_KEY, ERROR_CZ);
                }
                if (Objects.isNull(importDetail.getUnitWeight())) {
                    errorMap.put(ERROR_DWZL_KEY, ERROR_DWZL);
                }
                if (!org.springframework.util.StringUtils.hasText(importDetail.getMaterialProcessing())) {
                    errorMap.put(ERROR_CZCL_KEY, ERROR_CZCL);
                }
                if (!dicts.stream().anyMatch(dict -> dict.getName().equals(importDetail.getMachiningPartType()))) {
                    throw new IllegalArgumentException("物料[" + importDetail.getName() + " " + importDetail.getModel() + "]的加工件分类不正确，导入失败，请重新导入");
                }
            }
            if (!CollectionUtils.isEmpty(errorMap)) {
                StringBuffer buffer = new StringBuffer();
                errorMap.entrySet().forEach(entry -> buffer.append(entry.getValue()).append("、"));
                throw new IllegalArgumentException("物料小类为机械加工件时，" + buffer.toString().substring(0,
                        buffer.length() - 1) + "不能为空");
            }
        }
        // TODO
        //遍历整个excel文档，判断品牌、名称和型号的长度是否大于230个字符，
        Iterator<MilepostDesignPlanDetailImportExcelVo> iterator = detailExcelVos.iterator();
        while (iterator.hasNext()) {
            MilepostDesignPlanDetailImportExcelVo excelPartDetail = iterator.next();
            int sum = 0;
            if (StringUtils.isEmpty(excelPartDetail.getModel())) {
                indexNullExcel.append(index).append("、");
            } else {
                sum =
                        checkLength(excelPartDetail.getBrand()) + checkLength(excelPartDetail.getName()) + checkLength(excelPartDetail.getModel());
            }
            if (sum > 230) {
                indexNotNullExcel.append(index).append("、");
            }
            index++;
        }
        if (StringUtils.isNotEmpty(String.valueOf(indexNullExcel))) {
            throw new IllegalArgumentException("第" + indexNullExcel.toString().substring(0,
                    indexNullExcel.length() - 1) + "行" + "型号不能为空");
        } else if (StringUtils.isNotEmpty(String.valueOf(indexNotNullExcel))) {
            throw new IllegalArgumentException("第" + indexNotNullExcel.toString().substring(0,
                    indexNotNullExcel.length() - 1) + "行" + "品牌+名称+型号的长度不得超过230个字符");
        }
    }

    /**
     * 检查长度
     */
    private Integer checkLength(String excelPartDetail) throws UnsupportedEncodingException {
        if (StringUtils.isEmpty(excelPartDetail)) {
            return 0;
        }
        return excelPartDetail.getBytes("utf-8").length;
    }

    /**
     * @param dto        待检查的数据
     * @param remindType 校验结果提醒方式，null或者throw报错提醒，excel将校验结果放在dto的validResult到后导出excel
     */
    private void checkImport(MilepostDesignPlanDetailDto dto, String remindType, HashBasedTable<String, String,
            MerielSubclassVO> merielClassTable) {
        if ("excel".equalsIgnoreCase(remindType)) {
            StringBuffer stringBuffer = new StringBuffer(StringUtils.isEmpty(dto.getValidResult()) ? "" :
                    dto.getValidResult());
            if (StringUtils.isEmpty(dto.getMaterielType())) {
                stringBuffer.append("，").append(ErrorCode.CTC_CODING_SUBCLASS_NOT_NULL.getMsg());
            } else {
                //校验物料小类是否有对应的erp编码规则
                Map<String, MerielSubclassVO> map = merielClassTable.row(dto.getMaterielType());
                if (map.isEmpty()) {
                    stringBuffer.append("，").append(ErrorCode.CTC_MATERIAL_CLASS_TYPE_ILLEGAL.getMsg());
                } else {
                    //设置物料大类值
                    for (Map.Entry<String, MerielSubclassVO> entry : map.entrySet()) {
                        if (!Objects.equals("物料", entry.getValue().getBigValue())) {
                            stringBuffer.append("，").append(ErrorCode.CTC_CODING_SUBCLASS_NOT_TRUE.getMsg());
                        } else {
                            dto.setMaterialClassification(entry.getValue().getBigValue());

                        }
                    }

                }
            }
            if (StringUtils.isEmpty(dto.getMaterielDescr())) {
                stringBuffer.append("，").append(ErrorCode.CTC_MATERIEL_DESCR_NOT_NULL.getMsg());
            }
            if (dto.getNumber() == null) {
                stringBuffer.append("，").append(ErrorCode.CTC_NUMBER_NOT_NULL.getMsg());
            }
            if (StringUtils.isEmpty(dto.getUnit())) {
                stringBuffer.append("，").append(ErrorCode.CTC_MATERIAL_UNIT_NOT_NULL.getMsg());
            }
            if (dto.getDeliveryTime() == null) {
                stringBuffer.append("，").append(ErrorCode.CTC_MILEPOST_DESIGN_PLAN_DELIVERY_TIME_NULL.getMsg());
            }
            dto.setValidResult(stringBuffer.toString());
        } else {
            Asserts.notEmpty(dto.getMaterielType(), ErrorCode.CTC_CODING_SUBCLASS_NOT_NULL);
            Asserts.notEmpty(dto.getMaterielDescr().trim(), ErrorCode.CTC_MATERIEL_DESCR_NOT_NULL);
            Asserts.notEmpty(dto.getNumber(), ErrorCode.CTC_NUMBER_NOT_NULL);
            Asserts.notEmpty(dto.getUnit(), ErrorCode.CTC_MATERIAL_UNIT_NOT_NULL);
            Asserts.notEmpty(dto.getDeliveryTime(), ErrorCode.CTC_MILEPOST_DESIGN_PLAN_DELIVERY_TIME_NULL);

            //校验物料小类是否有对应的erp编码规则
            Map<String, MerielSubclassVO> map = merielClassTable.row(dto.getMaterielType());
            Asserts.isTrue(!map.isEmpty(), ErrorCode.CTC_MATERIAL_CLASS_TYPE_ILLEGAL);
            //设置物料大类值
            for (Map.Entry<String, MerielSubclassVO> entry : map.entrySet()) {
                dto.setMaterialClassification(entry.getValue().getBigValue());
            }
        }
    }

    /**
     * 匹配物料单位编码
     */
    public void matchingMaterialUnitCode(MilepostDesignPlanDetailDto dto, String remindType,
                                         Map<String, String> unitMap) {
        if ("excel".equalsIgnoreCase(remindType)) {
            StringBuffer stringBuffer = new StringBuffer(StringUtils.isEmpty(dto.getValidResult()) ? "" :
                    dto.getValidResult());
            if (StringUtils.isEmpty(dto.getUnit())) {
                stringBuffer.append("，").append(ErrorCode.CTC_MATERIAL_UNIT_NOT_NULL.getMsg());
            } else {
                if (unitMap.containsKey(dto.getUnit())) {
                    dto.setUnitCode(unitMap.get(dto.getUnit()));
                } else {
                    stringBuffer.append("，").append(ErrorCode.BASEDATA_MATERIAL_UNIT_NOT_FIND.getMsg());
                }
            }
            dto.setValidResult(stringBuffer.toString());
        } else {
            Asserts.notEmpty(dto.getUnit(), ErrorCode.CTC_MATERIAL_UNIT_NOT_NULL);
            Asserts.isTrue(unitMap.containsKey(dto.getUnit()), ErrorCode.BASEDATA_MATERIAL_UNIT_NOT_FIND);
            dto.setUnitCode(unitMap.get(dto.getUnit()));
        }
    }

    @Override
    public AsyncRequestResult matchingMaterialCodeAsync(MilepostDesignPlanDetailDto dto, Long organizationId,
                                                        String remindType, Map<String, String> unitMap) throws UnsupportedEncodingException {
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult = asyncRequestResultService.add(asyncRequestResult);
        applicationEventPublisher.publishEvent(new MatchingMaterialCodeEvent(this, dto, organizationId, remindType,
                unitMap));
        return asyncRequestResult;
    }

    /**
     * 匹配物料编码
     */
    public Boolean matchingMaterialCode(MilepostDesignPlanDetailDto dto) {

        boolean matchFlag = false;

        //判断当物料小类为”机械加工件“时，需要用型号+名称+品牌+加工件分类+材质+单位重量+材质处理 7个条件来判断是否为同一个物料
        if (MACHINED_PART.equals(dto.getMaterielType())) {
            List<MaterialDto> materials = materialExtService.findMaterialDto(dto);
            if (!CollectionUtils.isEmpty(materials)) {
                return buildMilepostDesignPlanDetailDto(dto, materials, ErpCodeSource.INVENTORY_ORGANIZATION.code(),
                        Boolean.TRUE);
            }
            dto.setPamCode(materialExtService.generateMaterialPAMCode());
            dto.setNumberOfMatchingResults(0);
            return matchFlag;
        } else {
            //匹配MA2(型号+名称+品牌+物料小类:精确匹配)
            if (StringUtils.isNotEmpty(dto.getModel())) {
                List<MaterialDto> materialDtosFromMA2ByModel =
                        getMaterial(dto.getName(), dto.getModel(), dto.getBrand(), "MA2", null, dto.getMaterielType());
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMA2ByModel,
                        ErpCodeSource.INVENTORY_ORGANIZATION.code(), Boolean.TRUE);
                if (matchFlag) {
                    return matchFlag;
                }
            }

            //匹配MM0(型号+名称+品牌+物料小类:精确匹配)
            if (StringUtils.isNotEmpty(dto.getModel())) {
                List<MaterialDto> materialDtosFromMM0ByModel =
                        getMaterial(dto.getName(), dto.getModel(), dto.getBrand(), null,
                                Constants.MMO_ORGANIZATION_ID, dto.getMaterielType());
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMM0ByModel,
                        ErpCodeSource.MAIN_ORGANIZATION.code(), Boolean.TRUE);
                if (matchFlag) {
                    return matchFlag;
                }
            }

            // 模糊匹配规格型号
            if (StringUtils.isNotEmpty(dto.getModel())) {
                List<MaterialDto> materialDtosFromMA2ByModel = getMaterial("MA2", null, null, dto.getModel());
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMA2ByModel,
                        ErpCodeSource.INVENTORY_ORGANIZATION.code(), Boolean.FALSE);
                if (matchFlag) {
                    return matchFlag;
                }

                List<MaterialDto> materialDtosFromMM0ByModel = getMaterial(null, null, null, dto.getModel());
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMM0ByModel,
                        ErpCodeSource.MAIN_ORGANIZATION.code(), Boolean.FALSE);
                if (matchFlag) {
                    return matchFlag;
                }
            }

            // 模糊匹配物料名称
            if (StringUtils.isNotEmpty(dto.getModel())) {
                List<MaterialDto> materialDtosFromMA2ByModel = getMaterial("MA2", null, dto.getName(), null);
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMA2ByModel,
                        ErpCodeSource.INVENTORY_ORGANIZATION.code(), Boolean.FALSE);
                if (matchFlag) {
                    return matchFlag;
                }

                List<MaterialDto> materialDtosFromMM0ByModel = getMaterial(null, null, dto.getName(), null);
                matchFlag = buildMilepostDesignPlanDetailDto(dto, materialDtosFromMM0ByModel,
                        ErpCodeSource.MAIN_ORGANIZATION.code(), Boolean.FALSE);
                if (matchFlag) {
                    return matchFlag;
                }
            }
        }
        return matchFlag;
    }

    private List<MaterialDto> getMaterial(String materialName, String materialModel, String materialBrand,
                                          String organizationCode, Long organizationId, String materielType) {
        List<MaterialDto> materialDtoList = materialExtService.invokeMaterialApiSelectList(materialName,
                materialModel, materialBrand, organizationCode, organizationId, materielType, null, null,
                Boolean.FALSE);
        return materialDtoList;
    }

    private List<MaterialDto> getMaterial(String organizationCode, Long organizationId, String fuzzyName,
                                          String fuzzyModel) {
        List<MaterialDto> materialDtoList = materialExtService.invokeMaterialApiSelectList
                (null, null, null, organizationCode, organizationId, null, fuzzyName, fuzzyModel, Boolean.FALSE);
        return materialDtoList;
    }

    /**
     * 是否匹配成功
     *
     * @param dto
     * @param materialDtos
     * @param erpCodeSource
     * @return
     */
    private boolean buildMilepostDesignPlanDetailDto(MilepostDesignPlanDetailDto dto, List<MaterialDto> materialDtos,
                                                     Integer erpCodeSource, boolean needUpdateFlag) {
        if (ListUtil.isPresent(materialDtos) && materialDtos.size() == 1) {
            dto.setNumberOfMatchingResults(1);
            dto.setMatchingCondition(1);
            dto.setErpCodeSource(erpCodeSource);

            if (!needUpdateFlag) {
                return Boolean.TRUE;
            }

            if (materialDtos.get(0).getPamCode() != null) {
                //导入的信息以系统存在的为准
                dto.setPamCode(materialDtos.get(0).getPamCode());
                dto.setMaterielDescr(materialDtos.get(0).getItemInfo());
                dto.setBrand(materialDtos.get(0).getBrand());
                dto.setModel(materialDtos.get(0).getModel());
                dto.setName(materialDtos.get(0).getName());
                dto.setMaterialClassification(materialDtos.get(0).getMaterialClassification());//物料大类
                dto.setMaterielType(materialDtos.get(0).getMaterialType());//物料小类
                dto.setUnitCode(materialDtos.get(0).getUnit());
                dto.setMachiningPartType(materialDtos.get(0).getMachiningPartType());
                dto.setMaterial(materialDtos.get(0).getMaterial());
                dto.setUnitWeight(materialDtos.get(0).getUnitWeight());
                dto.setMaterialProcessing(materialDtos.get(0).getMaterialProcessing());
            } else {
                dto.setPamCode(materialExtService.generateMaterialPAMCode());
            }
            dto.setErpCode(materialDtos.get(0).getItemCode());

            return Boolean.TRUE;
        } else if (ListUtil.isPresent(materialDtos) && materialDtos.size() > 1) {
            dto.setNumberOfMatchingResults(materialDtos.size());
            dto.setMatchingCondition(1);

            return Boolean.FALSE;
        }

        return Boolean.FALSE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleRecursive(Long detailPlanParentId, Long submitRecordId, Map<Long, String> materialIdWithNewErpCode) {
        MilepostDesignPlanDetailDto planDetailQuery = new MilepostDesignPlanDetailDto();
        planDetailQuery.setParentId(detailPlanParentId);
        if (null != submitRecordId) {
            planDetailQuery.setSubmitRecordId(submitRecordId);
        }
        planDetailQuery.setDeletedFlag(Boolean.FALSE);
        List<MilepostDesignPlanDetailDto> designPlanDetailSons = this.selectList(planDetailQuery);
        if (ListUtils.isEmpty(designPlanDetailSons)) {
            return;
        }
        logger.info("handleRecursive的detailPlanParentId：{}，designPlanDetailSons：{}", detailPlanParentId, JsonUtils.toString(designPlanDetailSons));

        List<String> pamCodeList = designPlanDetailSons.stream().map(MilepostDesignPlanDetailDto::getPamCode).distinct().collect(Collectors.toList());
        List<Material> materialList = materialExtService.invokeMaterialApiGetByPamCodeList(pamCodeList, null);
        Map<String, List<Material>> materialMap = CollectionUtils.isEmpty(materialList) ? new HashMap<>()
                : materialList.stream().collect(Collectors.groupingBy(Material::getPamCode));

        Long organizationId = projectService.getOrganizationIdByProjectId(designPlanDetailSons.get(0).getProjectId());
        List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto designPlanDetailSon : designPlanDetailSons) {
            logger.info("符合去生成E码的详设id：{}，erpCode：{}", designPlanDetailSon.getId(), designPlanDetailSon.getErpCode());
            //递归
            handleRecursive(designPlanDetailSon.getId(), submitRecordId, materialIdWithNewErpCode);

            //采购件才生成erp编码 （采购件已更改为 外购物料）
            if (StringUtils.isEmpty(designPlanDetailSon.getMaterialCategory()) || "看板物料".equals(designPlanDetailSon.getMaterialCategory())
                    || "外购物料".equals(designPlanDetailSon.getMaterialCategory())) {
                String erpCode;
                Long materialId = null;
                Long orgId = null;
                Long itemId = null;
                List<Material> materials = materialMap.get(designPlanDetailSon.getPamCode());
                if (!CollectionUtils.isEmpty(materials)) {
                    Material originalMaterial = null;
                    Material otherMaterial = null;
                    for (Material material : materials) {
                        if (Objects.equals(material.getOrganizationId(), organizationId)) {
                            originalMaterial = material;
                        } else {
                            otherMaterial = material;
                        }
                    }
                    if (null != originalMaterial) {
                        erpCode = StringUtils.isNotEmpty(originalMaterial.getItemCode()) ? originalMaterial.getItemCode()
                                : (null != otherMaterial && StringUtils.isNotEmpty(otherMaterial.getItemCode())) ? otherMaterial.getItemCode()
                                : designPlanDetailSon.getErpCode();
                        materialId = StringUtils.isNotEmpty(originalMaterial.getItemCode()) ? originalMaterial.getId()
                                : (null != otherMaterial && StringUtils.isNotEmpty(otherMaterial.getItemCode())) ? otherMaterial.getId() : null;
                        itemId = StringUtils.isNotEmpty(originalMaterial.getItemCode()) ? originalMaterial.getItemId()
                                : (null != otherMaterial && StringUtils.isNotEmpty(otherMaterial.getItemCode())) ? otherMaterial.getItemId() :
                                null;
                        orgId = StringUtils.isNotEmpty(originalMaterial.getItemCode()) ? organizationId
                                : (null != otherMaterial && StringUtils.isNotEmpty(otherMaterial.getItemCode())) ?
                                otherMaterial.getOrganizationId() : null;
                    } else {
                        erpCode = StringUtils.isNotEmpty(otherMaterial.getItemCode()) ? otherMaterial.getItemCode() :
                                designPlanDetailSon.getErpCode();
                        materialId = StringUtils.isNotEmpty(otherMaterial.getItemCode()) ? otherMaterial.getId() : null;
                        itemId = StringUtils.isNotEmpty(otherMaterial.getItemCode()) ? otherMaterial.getItemId() : null;
                        orgId = StringUtils.isNotEmpty(otherMaterial.getItemCode()) ? otherMaterial.getOrganizationId() : null;
                    }
                } else {
                    erpCode = designPlanDetailSon.getErpCode();
                }
                logger.info("准备去生成E码的详设id：{}，erpCode：{}，materialId：{}，itemId：{}，orgId：{}",
                        designPlanDetailSon.getId(), erpCode, materialId, itemId, orgId);

                //erpCode为空说明没有找到任何组织的物料，需要生成E码和推送ERP；itemId为空说明至少要推送ERP(但是有可能不需要生成E码)
                if (StringUtils.isEmpty(erpCode) || null == itemId) {
                    designPlanDetailSon.setErpCode(
                            materialExtService.handleNewMaterial(
                                    erpCode,
                                    designPlanDetailSon.getPamCode(),
                                    designPlanDetailSon.getMaterielType(),
                                    Optional.ofNullable(orgId).orElse(organizationId),
                                    materialIdWithNewErpCode,
                                    designPlanDetailSon.getWhetherModel())
                    );
                } else {
                    designPlanDetailSon.setErpCode(erpCode);
                    materialIdWithNewErpCode.put(materialId, erpCode);
                }
            }

            updateDesignPlanDetailList.add(designPlanDetailSon);
        }

        logger.info("handleRecursive批量更新详设的updateDesignPlanDetailList：{}", JsonUtils.toString(updateDesignPlanDetailList));
        if (ListUtils.isNotEmpty(updateDesignPlanDetailList)) {
            milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
        }
    }

    @Override
    public Long getIdByProjectBudgetMaterialId(Long projectBudgetMaterialId) {
        if (projectBudgetMaterialId == null) {
            return null;
        }
        MilepostDesignPlanDetailDto query = new MilepostDesignPlanDetailDto();
        query.setProjectBudgetMaterialId(projectBudgetMaterialId);
        List<MilepostDesignPlanDetailDto> dtos = this.selectList(query);
        if (ListUtil.isPresent(dtos)) {
            return dtos.get(0).getId();
        }
        return null;
    }

    @Override
    public BigDecimal getParentSigmaById(Long id, BigDecimal sigma) {
        if (sigma == null) {
            sigma = new BigDecimal(1);
        }
        if (null != id && id < 0) {
            sigma = new BigDecimal(1);
        }
        MilepostDesignPlanDetailDto currentDto = this.getById(id);
        if (currentDto == null) {
            return new BigDecimal(1);
        }

        if (Objects.equals(currentDto.getParentId(), -1L)) {//父节点
            BigDecimal number = Optional.ofNullable(currentDto.getNumber()).orElse(BigDecimal.ZERO);

            String redisKey = "milepostDesignPlanDetailFinalNumber_" + currentDto.getId();
            StringBuffer sb = new StringBuffer();
            sb.append(String.format("if redis.call('exists', '%s') == 0 then \n", redisKey));
            sb.append(String.format("redis.call('setnx', '%s', %s); \n", redisKey, number));
            sb.append(String.format("redis.call('expire', '%s', %d); \n", redisKey, 3600L));
            sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
            sb.append("else \n");
            sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
            sb.append("end; \n");
            String redisNumber = redisLuaScriptServer.executeLuaScript(sb.toString(), new ArrayList<>(), "");
            logger.info("getParentSigmaById的详设id：{}，redisNumber：{}，redisLuaScriptServer执行的脚本：{}", currentDto.getId(), redisNumber, sb.toString());

            return sigma.multiply(new BigDecimal(redisNumber));
        } else {
            BigDecimal number = Optional.ofNullable(currentDto.getNumber()).orElse(BigDecimal.ZERO);

            String redisKey = "milepostDesignPlanDetailFinalNumber_" + currentDto.getId();
            StringBuffer sb = new StringBuffer();
            sb.append(String.format("if redis.call('exists', '%s') == 0 then \n", redisKey));
            sb.append(String.format("redis.call('setnx', '%s', %s); \n", redisKey, number));
            sb.append(String.format("redis.call('expire', '%s', %d); \n", redisKey, 3600L));
            sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
            sb.append("else \n");
            sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
            sb.append("end; \n");
            String redisNumber = redisLuaScriptServer.executeLuaScript(sb.toString(), new ArrayList<>(), "");
            logger.info("getParentSigmaById的详设id：{}，redisNumber：{}，redisLuaScriptServer执行的脚本：{}", currentDto.getId(), redisNumber, sb.toString());

            return getParentSigmaById(currentDto.getParentId(), sigma).multiply(new BigDecimal(redisNumber));
        }
    }

    private void packageDto(MilepostDesignPlanDetailDto dto) {
        if (dto.getDesignCostId() != null && dto.getDesignCost() == null) {//存在待估价物料信息
            MaterialCostDto materialCostDto = materialCostExtService.invokeMaterialCostApiView(dto.getDesignCostId());
            if (materialCostDto != null) {
                dto.setDesignCost(materialCostDto.getItemCost());
            }
        }
    }

    @Override
    public List<MilepostDesignPlanDetail> listConfirmDetailsByPamCode(String pamCode) {
        // 审批中、审批通过
        List<Integer> statusList = new ArrayList<>();
        statusList.addAll(CheckStatus.getApprovedStatus());
        statusList.addAll(CheckStatus.getCheckingStatus());

        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        final MilepostDesignPlanDetailExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andStatusIn(statusList);
        criteria.andPamCodeEqualTo(pamCode);
        final List<MilepostDesignPlanDetail> milepostDesignPlanDetails =
                milepostDesignPlanDetailMapper.selectByExample(example);
        return milepostDesignPlanDetails;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> findNotGenerateRequireDesignPlanDetails(Long detailPlanParentId) {
        List<MilepostDesignPlanDetail> list = milepostDesignPlanDetailExtMapper.selectPassedByParentId(detailPlanParentId);
        List<MilepostDesignPlanDetailDto> dtos = BeanConverter.copy(list, MilepostDesignPlanDetailDto.class);
        for (MilepostDesignPlanDetailDto dto : dtos) {
            packageDto(dto);
        }
        return dtos;
    }


    @Override
    public Boolean updateDesignCost(MaterialCost materialCost) {
        if (ObjectUtils.isEmpty(materialCost)) {
            return false;
        }
        //根据库存组织查询业务实体ouId
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOrganizationId(materialCost.getOrganizationId());
        List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
        if (ListUtils.isEmpty(organizationRelList)) {
            throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
        }
        OrganizationRelDto organizationRelDto = organizationRelList.get(0);
        int count = milepostDesignPlanDetailExtMapper.updateDesignCost(materialCost.getItemCode(),
                materialCost.getId(), organizationRelDto.getOperatingUnitId(), materialCost.getItemCost());
        return count > 0;
    }

    @Override
    public List<MilepostDesignPlanDetail> getNoGenerateRequirement(Long projectId) {
        return milepostDesignPlanDetailExtMapper.getNoGenerateRequirement(projectId);
    }

    @Override
    public void deleteMaterialCost(Long detailPlanParentId, Long submitRecordId, Long organizationId) {
        MilepostDesignPlanDetailDto planDetailQuery = new MilepostDesignPlanDetailDto();
        planDetailQuery.setParentId(detailPlanParentId);
        planDetailQuery.setSubmitRecordId(submitRecordId);
        planDetailQuery.setDeletedFlag(Boolean.TRUE);
        List<MilepostDesignPlanDetailDto> designPlanDetailSons = this.selectList(planDetailQuery);
        if (ListUtils.isNotEmpty(designPlanDetailSons)) {
            for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : designPlanDetailSons) {
                milepostDesignPlanHelper.handleMaterialCost(milepostDesignPlanDetailDto.getPamCode(), organizationId);
            }
        }
    }


    @Override
    public AsyncRequestResult matchingMaterialChangingAsync(List<DesignPlanDetailChangeImportExcelVO> detailExcelVos) {
        //校验导入的数据
        this.checkDetailExcelVosTwo(detailExcelVos);
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult = asyncRequestResultService.add(asyncRequestResult);

        applicationEventPublisher.publishEvent(new DesignPlanDetailChangeMatchResultEvent(this,
                asyncRequestResult.getId(), detailExcelVos));
        return asyncRequestResult;
    }

    @Override
    public List<DesignPlanDetailMatchResult> matchingMaterialChanging(List<DesignPlanDetailChangeImportExcelVO> detailExcelVos) {

        List<DesignPlanDetailMatchResult> designPlanDetailMatchResults = new ArrayList<>();

        if (ListUtils.isNotEmpty(detailExcelVos)) {
            List<String> pamCodeList = new ArrayList<>();
            for (DesignPlanDetailChangeImportExcelVO dto : detailExcelVos) {
//                checkImport(dto); // todo 检查导入的excel数据 这里需要修改
                pamCodeList.add(dto.getPamCode());
            }
            final List<Material> materialList = materialExtService.invokeMaterialApiGetByPamCodeList(pamCodeList, null);
            for (Material material : materialList) {
                DesignPlanDetailMatchResult designPlanDetailMatchResult = new DesignPlanDetailMatchResult();
                designPlanDetailMatchResult.setPamCode(material.getPamCode());
                designPlanDetailMatchResult.setErpCode(material.getErpCode());
                designPlanDetailMatchResult.setUnit(material.getUnit());
                designPlanDetailMatchResult.setMaterielType(material.getMaterialType());
                final List<DesignPlanDetailChangeImportExcelVO> importExcelVOS =
                        detailExcelVos.stream().filter(d -> d.getPamCode().equals(material.getPamCode())).collect(Collectors.toList());
                final DesignPlanDetailChangeImportExcelVO excelVO = importExcelVOS.get(0);

                designPlanDetailMatchResult.setUnitWeight(material.getUnitWeight());
                designPlanDetailMatchResult.setMachiningPartType(material.getMachiningPartType());
                designPlanDetailMatchResult.setMaterial(material.getMaterial());
                designPlanDetailMatchResult.setBrand(material.getBrand());

                generateMaterielDescrSec(designPlanDetailMatchResult, material); // 生成物料描述

                designPlanDetailMatchResults.add(designPlanDetailMatchResult);
            }
        }

        return designPlanDetailMatchResults;
    }

    private void checkDetailExcelVosTwo(List<DesignPlanDetailChangeImportExcelVO> detailExcelVos) {
        Asserts.notEmpty(detailExcelVos, ErrorCode.CTC_DESIGN_PLAN_CHANGE_IMPORT_NOT_NULL);

        //获取机械加工件对应加工件分类的属性
        List<DictDto> dicts = basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null);
        Assert.notEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");
        Map<String, String> errorMap = new HashMap<>();
        int index = 0;
        for (DesignPlanDetailChangeImportExcelVO detailExcelVo : detailExcelVos) {
            index = index + 1;
            // 1.PAM物料编码必须输入
            if (!StringUtils.isNotEmpty(detailExcelVo.getPamCode())) {
                throw new IllegalArgumentException("导入的Excel中的第" + index + "行的PAM物料编码不能为空");
            }
            // todo 2.物料小类为“机械加工件”时,加加工分类，单件重量(Kg)，表面处理才可以修改

            // 3.「加工件分类」字段只能选择字典项中的值(围栏,底座,机加件,焊接钣金件)
            if (!dicts.stream().anyMatch(dict -> dict.getName().equals(detailExcelVo.getMachiningPartTypeNew()))) {
                throw new IllegalArgumentException("pam物料编码[" + detailExcelVo.getPamCode() + "]的加工件分类不正确，导入失败，请重新导入");
            }

        }
    }

    private void generateMaterielDescrSec(DesignPlanDetailMatchResult dto, Material material) {
        StringBuffer materielDescr = new StringBuffer();
        if (StringUtils.isNotEmpty(material.getName())) {
            materielDescr.append(material.getName());
            materielDescr.append("#");
        }
        /*if (StringUtils.isNotEmpty(material.getTuhao())){
            materielDescr.append(material.getTuhao());
            materielDescr.append("#");
        }
        if (StringUtils.isNotEmpty(material.getTuzhibanbenhao())){
            materielDescr.append(material.getTuzhibanbenhao());
            materielDescr.append("#");
        }*/
        if (StringUtils.isNotEmpty(dto.getModel())) {
            materielDescr.append(dto.getModel());
            materielDescr.append("#");
        }
        if (StringUtils.isNotEmpty(dto.getBrand())) {
            materielDescr.append(dto.getBrand());
            materielDescr.append("#");
        }
//        if (StringUtils.isNotEmpty(material.getpingpaishang())){
//            materielDescr.append(material.getpingpaishang());
//        }
        dto.setMaterielDescr(materielDescr.toString());
    }

    @Override
    public List<MilepostDesignPlanDetail> importDesignPlan(List<ImportMilepostDesignPlanDetailExcelVo> ksDetailExcelVos) {
        String projectCode = "";
        Project project = new Project();
        Long projectId = null;
        Long milepostId = null;
        Long userId = SystemContext.getUserId();
        List<MilepostDesignPlanDetail> details = new ArrayList<>();
        List<DictDto> dicts = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        String now = DateUtils.getNow();
        for (ImportMilepostDesignPlanDetailExcelVo vo : ksDetailExcelVos) {
            //初始化项目信息
            if (!projectCode.equals(vo.getProjectCode())) {
                projectCode = vo.getProjectCode();
                ProjectExample example = new ProjectExample();
                example.createCriteria().andCodeEqualTo(projectCode);
                List<Project> list = projectService.selectByExample(example);
                if (ListUtils.isNotEmpty(list)) {
                    project = list.get(0);
                    projectId = project.getId();
                    //删除掉原有详设
                    MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
                    example2.createCriteria().andProjectIdEqualTo(projectId);
                    List<MilepostDesignPlanDetail> list2 = milepostDesignPlanDetailMapper.selectByExample(example2);
                    if (ListUtils.isNotEmpty(list2)) {
                        logger.info("*************** MilepostDesignPlanDetailServiceImpl.importDesignPlan入参1:{} ***************",
                                JSONObject.toJSONString(list2));
                        for (MilepostDesignPlanDetail l : list2) {
                            l.setDeletedFlag(Boolean.TRUE);
                            milepostDesignPlanDetailMapper.updateByPrimaryKey(l);
                        }
                    }

                } else {

                }
                //项目存在
                if (null != projectId) {
                    //根据预算生成详设
                    milepostDesignPlanService.projectBudgetMaterialToMilepostDesignPlanDetail(projectId, userId);
                    //获取预算信息
                    MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
                    example2.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
                    details = milepostDesignPlanDetailMapper.selectByExample(example2);

                    ProjectMilepostDto projectMilepostQuery = new ProjectMilepostDto();
                    projectMilepostQuery.setProjectId(projectId);
                    projectMilepostQuery.setAnnexType(ProjectMilepostAnnexType.BOM.code());
                    List<ProjectMilepostDto> milepostDtos = projectMilepostService.selectList(projectMilepostQuery);

                    if (ListUtils.isNotEmpty(milepostDtos)) {
                        milepostId = milepostDtos.get(0).getId();
                    }

                }

            }
            MilepostDesignPlanDetail detail = new MilepostDesignPlanDetail();
            //是否模组
            if ("是".equals(vo.getIsModel())) {
                detail.setWhetherModel(Boolean.TRUE);
            } else {
                detail.setWhetherModel(Boolean.FALSE);
            }
            //是否初始化
            if ("是".equals(vo.getInit())) {
                detail.setInit(Boolean.TRUE);
            } else {
                detail.setInit(Boolean.FALSE);
            }
            //模组层 已根据预算生成的详设
            for (MilepostDesignPlanDetail d : details) {
                if (d.getPamCode().equals(vo.getPamCode())) {
                    vo.setPlanId(d.getId());
                    detail = d;
                    if (d.getParentId() != -1L) {
                        detail.setWhetherModel(Boolean.TRUE);
                    }
                }
            }
            /*if(null != vo.getModel() && vo.getModel()){

            }else{*/
            detail.setParentId(-1L);
            //序号 设置parent
            String serialNumber = vo.getSerialNumber();
            if (!pattern.matcher(serialNumber).matches()) {
                String parentNumber = serialNumber.substring(0, serialNumber.lastIndexOf("."));
                for (ImportMilepostDesignPlanDetailExcelVo parent : ksDetailExcelVos) {
                    if (parent.getSerialNumber().equals(parentNumber) && parent.getProjectCode().equals(vo.getProjectCode())) {
                        detail.setParentId(parent.getPlanId());

                    }
                }
            }
            detail.setDeletedFlag(Boolean.FALSE);
            detail.setPamCode(vo.getPamCode());//pamCode
            detail.setNumber(vo.getNumber());//数量
            detail.setDeliveryTime(vo.getDeliveryTime());//交货日期
            detail.setSource(vo.getSource());//来源
            detail.setProjectId(projectId);
            detail.setMilepostId(milepostId);
            //集成外包
            if ("是".equals(vo.getExt())) {
                detail.setExt(Boolean.TRUE);
            } else {
                detail.setExt(Boolean.FALSE);
            }
            //模组状态
            if ("已确认".equals(vo.getModuleStatus())) {
                detail.setModuleStatus(new Integer(1));
            } else if ("未确认".equals(vo.getModuleStatus())) {
                detail.setModuleStatus(new Integer(0));
            }

            //根据pamcode获取物料信息
            MaterialDto materialDto = materialExtService.invokeMaterialApiGetByPamCode(vo.getPamCode(), null);
            //物料存在
            if (null != materialDto) {
                //物料描述
                detail.setMaterielDescr(materialDto.getItemInfo());
                // ERP物料编码
                if (StringUtils.isNotEmpty(materialDto.getItemCode())) {
                    detail.setErpCode(materialDto.getItemCode());
                }
                // 单位
                if (StringUtils.isNotEmpty(materialDto.getUnit())) {
                    for (DictDto d : dicts) {
                        if (materialDto.getUnit().equals(d.getCode())) {
                            detail.setUnit(d.getName());
                            detail.setUnitCode(materialDto.getUnit());
                        }
                    }

                }
                // 物料类型
                if (StringUtils.isNotEmpty(materialDto.getMaterialCategory())) {
                    detail.setMaterialCategory(materialDto.getMaterialCategory());
                    if (MaterialCategoryEnum.ASSEMBLY_PARTS.msg().equals(materialDto.getMaterialCategory())) {
                        detail.setWhetherModel(Boolean.TRUE);
                    }
                }
                // 设计成本
                //detail.setDesignCost();
                // 预算单价
                //detail.setBudgetUnitPrice();
                // 设计成本是否来源估价
                //detail.setItemCostIsNull();
                // 名称
                detail.setName(materialDto.getName());
                // 型号/规格/图号
                detail.setModel(materialDto.getModel());
                // 品牌
                detail.setBrand(materialDto.getBrand());
                // 物料大类
                detail.setMaterialClassification(materialDto.getMaterialClassification());
                // 物料中类
                detail.setCodingMiddleClass(materialDto.getCodingMiddleclass());
                // 物料小类
                detail.setMaterielType(materialDto.getMaterialType());
                // 加工件分类
                detail.setMachiningPartType(materialDto.getMachiningPartType());
                // 材质
                detail.setMaterial(materialDto.getMaterial());
                // 图号
                detail.setFigureNumber(materialDto.getFigureNumber());
                // 图纸版本号
                detail.setChartVersion(materialDto.getChartVersion());
                // 备件标识
                detail.setOrSparePartsMask(materialDto.getOrSparePartsMask());
                // 品牌商物料编码
                detail.setBrandMaterialCode(materialDto.getBrandMaterialCode());
                // 单位重量(Kg)
                detail.setUnitWeight(materialDto.getUnitWeight());
                // 材质处理
                detail.setMaterialProcessing(materialDto.getMaterialProcessing());

            }
            detail.setRemark("初始化" + now);
            if (null != vo.getPlanId()) {
                detail.setId(vo.getPlanId());
                logger.info("*************** MilepostDesignPlanDetailServiceImpl.importDesignPlan入参2:{} ***************",
                        JSONObject.toJSONString(detail));
                milepostDesignPlanDetailMapper.updateByPrimaryKey(detail);
            } else {
                milepostDesignPlanDetailMapper.insert(detail);
                if (null != detail.getId()) {
                    vo.setPlanId(detail.getId());
                }
            }

            //}

        }
        return details;
    }

    @Override
    public List<MilepostDesignPlanDetail> importDesignPlan2(List<ImportMilepostDesignPlanDetailExcelVo> ksDetailExcelVos) {
        String projectCode = "";
        Project project = new Project();
        Long projectId = null;
        Long milepostId = null;
        Long userId = SystemContext.getUserId();
        List<MilepostDesignPlanDetail> details = new ArrayList<>();
        List<DictDto> dicts = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        String now = DateUtils.getNow();
        ImportMilepostDesignPlanDetailExcelVo voFirst = ksDetailExcelVos.get(0);
        MilepostDesignPlanDetail parents = new MilepostDesignPlanDetail();
        for (ImportMilepostDesignPlanDetailExcelVo vo : ksDetailExcelVos) {
            //初始化项目信息
            if (!projectCode.equals(vo.getProjectCode())) {
                projectCode = vo.getProjectCode();
                ProjectExample example = new ProjectExample();
                example.createCriteria().andCodeEqualTo(projectCode);
                List<Project> list = projectService.selectByExample(example);
                if (ListUtils.isNotEmpty(list)) {
                    project = list.get(0);
                    projectId = project.getId();
                }
                //项目存在
                if (null != projectId) {
                    //获取详设信息
                    MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
                    example2.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE).andPamCodeEqualTo(voFirst.getPamCode());
                    details = milepostDesignPlanDetailMapper.selectByExample(example2);
                    if (null != details) {
                        milepostId = details.get(0).getMilepostId();
                        parents = details.get(0);
                    }
                }

            }
            MilepostDesignPlanDetail detail = new MilepostDesignPlanDetail();
            MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
            example2.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE).andPamCodeEqualTo(vo.getPamCode());
            details = milepostDesignPlanDetailMapper.selectByExample(example2);
            if (null != details) {
                detail = details.get(0);
                vo.setPlanId(detail.getId());
            }
            //是否模组
            if ("是".equals(vo.getIsModel())) {
                detail.setWhetherModel(Boolean.TRUE);
            } else {
                detail.setWhetherModel(Boolean.FALSE);
            }
            //是否初始化
            if ("是".equals(vo.getInit())) {
                detail.setInit(Boolean.TRUE);
            } else {
                detail.setInit(Boolean.FALSE);
            }
            /*//模组层 已根据预算生成的详设
            for(MilepostDesignPlanDetail d:details){
                if(d.getPamCode().equals(vo.getPamCode())){
                    vo.setPlanId(d.getId());
                    detail = d;
                    if(d.getParentId() != -1L){
                        detail.setWhetherModel(Boolean.TRUE);
                    }
                }
            }*/
            /*if(null != vo.getModel() && vo.getModel()){

            }else{*/
            if (null == detail.getParentId()) {
                detail.setParentId(parents.getId());
            }

            //序号 设置parent
            String serialNumber = vo.getSerialNumber();
            if (!pattern.matcher(serialNumber).matches()) {
                String parentNumber = serialNumber.substring(0, serialNumber.lastIndexOf("."));
                for (ImportMilepostDesignPlanDetailExcelVo parent : ksDetailExcelVos) {
                    if (parent.getSerialNumber().equals(parentNumber) && parent.getProjectCode().equals(vo.getProjectCode())) {
                        detail.setParentId(parent.getPlanId());
                    }
                }
            }
            detail.setDeletedFlag(Boolean.FALSE);
            detail.setPamCode(vo.getPamCode());//pamCode
            detail.setNumber(vo.getNumber());//数量
            detail.setDeliveryTime(vo.getDeliveryTime());//交货日期
            detail.setSource(vo.getSource());//来源
            detail.setProjectId(projectId);
            detail.setMilepostId(milepostId);
            //集成外包
            if ("是".equals(vo.getExt())) {
                detail.setExt(Boolean.TRUE);
            } else {
                detail.setExt(Boolean.FALSE);
            }
            //模组状态
            if ("已确认".equals(vo.getModuleStatus())) {
                detail.setModuleStatus(new Integer(1));
            } else if ("未确认".equals(vo.getModuleStatus())) {
                detail.setModuleStatus(new Integer(0));
            }

            //根据pamcode获取物料信息
            MaterialDto materialDto = materialExtService.invokeMaterialApiGetByPamCode(vo.getPamCode(), null);
            //物料存在
            if (null != materialDto) {
                //物料描述
                detail.setMaterielDescr(materialDto.getItemInfo());
                // ERP物料编码
                if (StringUtils.isNotEmpty(materialDto.getItemCode())) {
                    detail.setErpCode(materialDto.getItemCode());
                }
                // 单位
                if (StringUtils.isNotEmpty(materialDto.getUnit())) {
                    for (DictDto d : dicts) {
                        if (materialDto.getUnit().equals(d.getCode())) {
                            detail.setUnit(d.getName());
                            detail.setUnitCode(materialDto.getUnit());
                        }
                    }

                }
                // 物料类型
                if (StringUtils.isNotEmpty(materialDto.getMaterialCategory())) {
                    detail.setMaterialCategory(materialDto.getMaterialCategory());
                    if (MaterialCategoryEnum.ASSEMBLY_PARTS.msg().equals(materialDto.getMaterialCategory())) {
                        detail.setWhetherModel(Boolean.TRUE);
                    }
                }
                // 设计成本
                //detail.setDesignCost();
                // 预算单价
                //detail.setBudgetUnitPrice();
                // 设计成本是否来源估价
                //detail.setItemCostIsNull();
                // 名称
                detail.setName(materialDto.getName());
                // 型号/规格/图号
                detail.setModel(materialDto.getModel());
                // 品牌
                detail.setBrand(materialDto.getBrand());
                // 物料大类
                detail.setMaterialClassification(materialDto.getMaterialClassification());
                // 物料中类
                detail.setCodingMiddleClass(materialDto.getCodingMiddleclass());
                // 物料小类
                detail.setMaterielType(materialDto.getMaterialType());
                // 加工件分类
                detail.setMachiningPartType(materialDto.getMachiningPartType());
                // 材质
                detail.setMaterial(materialDto.getMaterial());
                // 图号
                detail.setFigureNumber(materialDto.getFigureNumber());
                // 图纸版本号
                detail.setChartVersion(materialDto.getChartVersion());
                // 备件标识
                detail.setOrSparePartsMask(materialDto.getOrSparePartsMask());
                // 品牌商物料编码
                detail.setBrandMaterialCode(materialDto.getBrandMaterialCode());
                // 单位重量(Kg)
                detail.setUnitWeight(materialDto.getUnitWeight());
                // 材质处理
                detail.setMaterialProcessing(materialDto.getMaterialProcessing());

            }
            detail.setRemark("初始化" + now);
            if (null != vo.getPlanId()) {
                detail.setId(vo.getPlanId());
                logger.info("*************** MilepostDesignPlanDetailServiceImpl.importDesignPlan2入参:{} ***************",
                        JSONObject.toJSONString(detail));
                milepostDesignPlanDetailMapper.updateByPrimaryKey(detail);
            } else {
                milepostDesignPlanDetailMapper.insert(detail);
                if (null != detail.getId()) {
                    vo.setPlanId(detail.getId());
                }
            }

            //}

        }
        return details;
    }


    @Override
    public List<MilepostDesignPlanDetail> updateDesignPlan(List<UpdateMilepostDesignPlanDetailExcelVo> ksDetailExcelVos) {
        String projectCode = "";
        Project project = new Project();
        Long projectId = null;
        Long milepostId = null;
        Long userId = SystemContext.getUserId();
        List<MilepostDesignPlanDetail> details = new ArrayList<>();
        for (UpdateMilepostDesignPlanDetailExcelVo vo : ksDetailExcelVos) {

            if (!projectCode.equals(vo.getProjectCode())) {
                projectCode = vo.getProjectCode();
                ProjectExample example = new ProjectExample();
                example.createCriteria().andCodeEqualTo(projectCode).andDeletedFlagEqualTo(Boolean.FALSE);
                List<Project> list = projectService.selectByExample(example);
                if (ListUtils.isNotEmpty(list)) {
                    project = list.get(0);
                    projectId = project.getId();
                }
            }
            if (StringUtils.isEmpty(vo.getProjectCode()) && StringUtils.isNotEmpty(vo.getPamCode())) {
                MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
                example2.createCriteria().andPamCodeEqualTo(vo.getPamCode()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<MilepostDesignPlanDetail> list2 = milepostDesignPlanDetailMapper.selectByExample(example2);
                logger.info("*************** MilepostDesignPlanDetailServiceImpl.updateDesignPlan入参:{} ***************",
                        JSONObject.toJSONString(list2));
                for (MilepostDesignPlanDetail plan : list2) {
                    if (StringUtils.isNotEmpty(vo.getMaterialCategory())) {
                        plan.setMaterialCategory(vo.getMaterialCategory());
                    }
                    milepostDesignPlanDetailMapper.updateByPrimaryKey(plan);
                }
            } else if (null != projectId) {
                MilepostDesignPlanDetailExample example2 = new MilepostDesignPlanDetailExample();
                example2.createCriteria().andProjectIdEqualTo(projectId).andPamCodeEqualTo(vo.getPamCode()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<MilepostDesignPlanDetail> list2 = milepostDesignPlanDetailMapper.selectByExample(example2);
                if (ListUtils.isNotEmpty(list2)) {
                    MilepostDesignPlanDetail plan = list2.get(0);
                    if (StringUtils.isNotEmpty(vo.getMaterialCategory())) {
                        plan.setMaterialCategory(vo.getMaterialCategory());
                    }
                    if (null != vo.getDeleteFlag() && 1 == vo.getDeleteFlag()) {
                        plan.setDeletedFlag(Boolean.TRUE);
                    } else {
                        plan.setDeletedFlag(Boolean.FALSE);
                    }
                    //是否初始化
                    if ("是".equals(vo.getInit())) {
                        plan.setInit(Boolean.TRUE);
                    } else {
                        plan.setInit(Boolean.FALSE);
                    }
                    if (null != vo.getNumber()) {
                        plan.setNumber(vo.getNumber());//数量
                    }
                    if (null != vo.getDeliveryTime()) {
                        plan.setDeliveryTime(vo.getDeliveryTime());//交货日期
                    }
                    if (null != vo.getSource()) {
                        plan.setSource(vo.getSource());//来源
                    }
                    logger.info("*************** MilepostDesignPlanDetailServiceImpl.updateDesignPlan入参2:{} ***************",
                            JSONObject.toJSONString(plan));
                    milepostDesignPlanDetailMapper.updateByPrimaryKey(plan);
                }
            }

        }
        return null;
    }

    @Override
    public boolean ksMatchingMaterialCodeEstimateNew(List<MilepostDesignPlanDetailDto> designPlanDetailDtos,
                                                     Long storageId, Long unitId) {
        boolean result = true;

        //获取"机械加工件"对应"加工件分类"的属性
        List<DictDto> dicts = basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null);
        Assert.notEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");
        // 获取类别列表数据
        List<MerielSubclassVO> merielSubclassList =
                erpCodeRuleService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUNSHAN.getCode());
        // 物料类别Table数据（row小类，column中类，value实体类）
        HashBasedTable<String, String, MerielSubclassVO> merielClassTable = HashBasedTable.create();
        for (MerielSubclassVO merielSubclassVO : merielSubclassList) {
            merielClassTable.put(merielSubclassVO.getValue(), merielSubclassVO.getMiddileValue(), merielSubclassVO);
        }
        // 获取计量单位
        Map<String, String> unitMap =
                basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));
        //获取品牌列表数据
        List<BrandMaintenance> brandMaintenanceList = basedataExtService.getBrandMaintenance();
        Assert.notEmpty(brandMaintenanceList, "请先维护品牌信息");
        Map<String, Integer> brandMaintenanceMap =
                brandMaintenanceList.stream().collect(Collectors.toMap(BrandMaintenance::getBrandName,
                        BrandMaintenance::getBrandStatus, (key1, key2) -> key2));
        // 获取备件标识组织参数配置
        Set<String> orSparePartsMaskSet = organizationCustomDictService.queryByName(Constants.SPAREPARTS_MASK, unitId
                , OrgCustomDictOrgFrom.COMPANY);
        // 获取物料类型及编码字典
        Byte a = 1;
        Map<String, DictDto> materialTypeMap =
                basedataExtService.getLtcDict(MATERIAL_TYPE, null, null).stream().filter(o -> !Objects.equals(a,
                        o.getOrderNum())).collect(Collectors.toMap(DictDto::getName, Function.identity(), (key1,
                                                                                                           key2) -> key2));
        if (null == orSparePartsMaskSet || orSparePartsMaskSet.isEmpty()) {
            throw new BizException(100, "备件标识需要在组织参数中维护");
        }

        List<String> repetition1 = new ArrayList<>();
        List<String> repetition2 = new ArrayList<>();
        List<String> repetition3 = new ArrayList<>();
        List<String> repetition4 = new ArrayList<>();

        if (ListUtils.isNotEmpty(designPlanDetailDtos)) {
            for (MilepostDesignPlanDetailDto dto : designPlanDetailDtos) {
                if (null != dto.getPamCode()) { //如果导入文件已经有PAM物料编码，则根据PAM编码和库存组织id去物料表查询并带出其他属性,不需校验其他属性。
                    if (dto.getNumber() == null) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) +
                                "，数量不能为空");
                    }
                    String code = dto.getPamCode();
                    final Map<String, Object> param = new HashMap<>();
                    param.put("pamCode", code);
                    param.put("organizationId", storageId);
                    String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material" +
                            "/selectByPamCodeAndOrganizationId", param);
                    String res = restTemplate.getForEntity(url, String.class).getBody();
                    DataResponse<List<Material>> response = JSON.parseObject(res,
                            new TypeReference<DataResponse<List<Material>>>() {
                            });
                    List<Material> data = response.getData();
                    if (data.isEmpty()) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) +
                                "，导入的PAM编码有误");
                    } else {
                        Material material = response.getData().get(0);
                        this.setDto(material, dto);
                    }
                } else {
                    if (result) {
                        //校验导入数据
                        result = this.checkDesignDetailDto(dto, dicts, merielClassTable, orSparePartsMaskSet, unitMap
                                , materialTypeMap,
                                brandMaintenanceMap, result, storageId, repetition1, repetition2, repetition3,
                                repetition4);
                    }
                }
            }
        }
        return result;
    }


    private boolean checkDesignDetailDto(MilepostDesignPlanDetailDto dto, List<DictDto> dicts, HashBasedTable<String,
            String, MerielSubclassVO> merielClassTable, Set<String> orSparePartsMaskSet, Map<String, String> unitMap,
                                         Map<String, DictDto> materialTypeMap,
                                         Map<String, Integer> brandMaintenanceMap, boolean result, Long storageId,
                                         List<String> repetition1, List<String> repetition2, List<String> repetition3
            , List<String> repetition4) {
        logger.info("materialCategory：{}", dto.getMaterialCategory());
        if (null != dto.getMaterialCategory()) {
            DictDto materialTypeDict = materialTypeMap.get(dto.getMaterialCategory());
            if (null == materialTypeDict) {
                result = false;
                dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) +
                        "物料类型为字典项material_type中序号不为“1”对应的值");
            }
            if (materialTypeDict != null && ("装配件".equals(materialTypeDict.getName()) || "虚拟件".equals(materialTypeDict.getName()))) {
                checkFather(dto, merielClassTable, dicts, orSparePartsMaskSet, unitMap, brandMaintenanceMap, result); //校验物料类型为“装配件”的数据
            } else {
                checkSon(dto, merielClassTable, dicts, orSparePartsMaskSet, unitMap, brandMaintenanceMap, result,
                        repetition1, repetition2, repetition3, repetition4); //校验物料类型为“非装配件”的数据
            }
        } else {
            result = false;
            dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) + "，物料类型不能为空");
        }

        if (result) {
            //判断物料的唯一性
            checkKsMaterial(dto, storageId);
            //this.ksMatchingMaterialCodeAsync(dto,storageId);
        }
        return result;
    }

    @Override
    public AsyncRequestResult ksMatchingMaterialCodeAsync(MilepostDesignPlanDetailDto dto, Long storageId) {
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult = asyncRequestResultService.add(asyncRequestResult);
        applicationEventPublisher.publishEvent(new KsMatchingMaterialCodeEvent(this, dto, storageId));
        return asyncRequestResult;
    }


    private Boolean checkRepetition(MilepostDesignPlanDetailDto dto, boolean result, List<String> repetition1,
                                    List<String> repetition2, List<String> repetition3) {
        if ("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory())) {
            if ("机械加工件".equals(dto.getCodingMiddleClass())) { //物料中类+品牌+图号 检索相同编码
                if (ListUtils.isNotEmpty(repetition1) && repetition1.contains(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getFigureNumber())) {
                    result = false;
                    dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) +
                            "，根据物料唯一性原则，模板中存在重复数据！");
                }
                repetition1.add(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getFigureNumber());
            } else {
                if (null != dto.getBrandMaterialCode()) { //品牌商物料编码存在  物料中类+品牌+品牌商物料编码 检索相同编码
                    if (ListUtils.isNotEmpty(repetition2) && repetition2.contains(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getBrandMaterialCode())) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) +
                                "，根据物料唯一性原则，模板中存在重复数据！");
                    }
                    repetition2.add(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getBrandMaterialCode());
                } else { //品牌商物料编码不存在  物料中类+品牌+规格/型号 检索相同编码
                    if (ListUtils.isNotEmpty(repetition3) && repetition3.contains(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getModel())) {
                        result = false;
                        dto.setValidResult((StringUtils.isEmpty(dto.getValidResult()) ? "" : dto.getValidResult()) +
                                "，根据物料唯一性原则，模板中存在重复数据！");
                    }
                    repetition3.add(dto.getCodingMiddleClass() + ":" + dto.getBrand() + ":" + dto.getModel());
                }
            }
        }
        return result;
    }

    private Boolean checkSon(MilepostDesignPlanDetailDto dto,
                             HashBasedTable<String, String, MerielSubclassVO> merielClassTable, List<DictDto> dicts,
                             Set<String> orSparePartsMaskSet, Map<String, String> unitMap,
                             Map<String, Integer> brandMaintenanceMap,
                             boolean result,
                             List<String> repetition1, List<String> repetition2, List<String> repetition3,
                             List<String> repetition4) {
        StringBuffer stringBuffer = new StringBuffer(StringUtils.isEmpty(dto.getValidResult()) ? "" :
                dto.getValidResult());
        //校验必填项
        if (StringUtil.isNull(dto.getCodingMiddleClass())) {
            result = false;
            stringBuffer.append("，物料中类不能为空");
        } else {
            if (!merielClassTable.columnKeySet().contains(dto.getCodingMiddleClass())) {
                result = false;
                stringBuffer.append("，物料[" + dto.getName() + " " + dto.getModel() + "]的物料中类不正确");
            }
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(dto.getMaterielType())) {
            result = false;
            stringBuffer.append("，物料小类不能为空");
        } else {
            if (merielClassTable.rowKeySet().contains(dto.getMaterielType())) {
                MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(),
                        dto.getCodingMiddleClass());
                if (merielSubclassVO == null) {
                    result = false;
                    stringBuffer.append("，物料小类对应中类不存在");
                } else {
                    dto.setMaterialClassification(merielSubclassVO.getBigValue());
                }
            } else {
                result = false;
                stringBuffer.append("，物料小类不存在");
            }
        }
        if (StringUtil.isNull(dto.getBrand())) {
            result = false;
            stringBuffer.append("，品牌不能为空");
        } else {
            if (!brandMaintenanceMap.containsKey(dto.getBrand())) {
                result = false;
                stringBuffer.append(dto.getBrand() + "，在品牌表中不存在");
            } else {
                Integer i = brandMaintenanceMap.get(dto.getBrand());
                if (i.equals(1)) {
                    result = false;
                    stringBuffer.append(dto.getBrand() + "已禁用");
                }
            }
        }
        if (StringUtil.isNull(dto.getName())) {
            result = false;
            stringBuffer.append("，名称不能为空");
        }
        if (StringUtil.isNull(dto.getModel())) {
            result = false;
            stringBuffer.append("，规格/型号不能为空");
        }
        if (Objects.isNull(dto.getNumber())) {
            result = false;
            stringBuffer.append("，数量不能为空");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(dto.getUnit()) || !unitMap.containsKey(dto.getUnit())) {
            result = false;
            stringBuffer.append("，单位不能为空且必须为字典项measurement_unit的值");
        } else {
            dto.setUnitCode(unitMap.get(dto.getUnit()));
        }
        if (StringUtil.isNull(dto.getOrSparePartsMask())) {
            result = false;
            stringBuffer.append("，备件标识不能为空");
        } else {
            if (!orSparePartsMaskSet.contains(dto.getOrSparePartsMask())) {
                result = false;
                stringBuffer.append("，备件标识不正确");
            }
        }
        //当物料中类为”机械加工件、外协加工“时，图号，图纸版本号，加工分类，材质，单件重量(Kg)，表面处理为必填项，若未填写，在数据校验环节需要弹出提示“导入文件有误,请重新下载模板填写数据”
        MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(), dto.getCodingMiddleClass());
        if (merielSubclassVO != null && Objects.equals(3, merielSubclassVO.getCheckType())) {
            Map<String, String> errorMap = new HashMap<>();
            if (!org.springframework.util.StringUtils.hasText(dto.getMachiningPartType())) {
                errorMap.put(ERROR_JGJFL_KEY, ERROR_JGJFL);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getMaterial())) {
                errorMap.put(ERROR_CZ_KEY, ERROR_CZ);
            }
            if (Objects.isNull(dto.getUnitWeight())) {
                errorMap.put(ERROR_DWZL_KEY, ERROR_DWZL);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getFigureNumber())) {
                errorMap.put(ERROR_TH_KEY, ERROR_TH);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getChartVersion())) {
                errorMap.put(ERROR_TZBBH_KEY, ERROR_TZBBH);
            }
            if (!org.springframework.util.StringUtils.hasText(dto.getMaterialProcessing())) {
                errorMap.put(ERROR_BMCL_KEY, ERROR_BMCL);
            }
            if (!dicts.stream().anyMatch(dict -> dict.getName().equals(dto.getMachiningPartType()))) {
                result = false;
                stringBuffer.append("，物料[" + dto.getName() + " " + dto.getModel() + "]的加工件分类不正确");
            }
            if (!CollectionUtils.isEmpty(errorMap)) {
                result = false;
                StringBuffer buffer = new StringBuffer();
                errorMap.entrySet().forEach(entry -> buffer.append(entry.getValue()).append("、"));
                stringBuffer.append("，物料中类为机械加工件、外协加工时，" + buffer.toString().substring(0, buffer.length() - 1) +
                        "不能为空");
            }
        }
        //校验导入日
        if (StringUtils.isNotEmpty(dto.getDeliveryTimeStr())) {
            if (DateUtils.isValidDate(dto.getDeliveryTimeStr(), FORMAT_SHORT)) {
                dto.setDeliveryTime(DateUtils.parse(dto.getDeliveryTimeStr(), FORMAT_SHORT));
            } else {
                result = false;
                stringBuffer.append("，物料到货日期格式不正确");
            }
        }
        dto.setValidResult(stringBuffer.toString());

        // 判重
        this.checkRepetition(dto, result, repetition1, repetition2, repetition3);

        return result;
    }

    private Boolean checkFather(MilepostDesignPlanDetailDto dto,
                                HashBasedTable<String, String, MerielSubclassVO> merielClassTable, List<DictDto> dicts,
                                Set<String> orSparePartsMaskSet, Map<String, String> unitMap,
                                Map<String, Integer> brandMaintenanceMap,
                                boolean result) {
        StringBuffer stringBuffer = new StringBuffer(StringUtils.isEmpty(dto.getValidResult()) ? "" :
                dto.getValidResult());
        //校验必填项
        if (StringUtil.isNull(dto.getCodingMiddleClass())) {
            result = false;
            stringBuffer.append("，物料中类不能为空");
        } else {
            Map<String, MerielSubclassVO> map = merielClassTable.column(dto.getCodingMiddleClass());
            if (map.isEmpty()) {
                result = false;
                stringBuffer.append("，物料[" + dto.getName() + " " + dto.getModel() + "]的物料中类不正确");
            } else {
                for (Map.Entry<String, MerielSubclassVO> entry : map.entrySet()) {
                    dto.setMaterialClassification(entry.getValue().getBigValue());
                }
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(dto.getMaterielType())) {
            if (merielClassTable.rowKeySet().contains(dto.getMaterielType())) {
                MerielSubclassVO merielSubclassVO = merielClassTable.get(dto.getMaterielType(),
                        dto.getCodingMiddleClass());
                if (merielSubclassVO == null) {
                    result = false;
                    stringBuffer.append("，物料小类对应中类不存在");
                } else {
                    dto.setMaterialClassification(merielSubclassVO.getBigValue());
                }
            }
        }
        if (StringUtil.isNull(dto.getBrand())) {
            result = false;
            stringBuffer.append("，品牌不能为空");
        } else {
            if (!brandMaintenanceMap.containsKey(dto.getBrand())) {
                result = false;
                stringBuffer.append(dto.getBrand() + "，在品牌表中不存在");
            } else {
                Integer i = brandMaintenanceMap.get(dto.getBrand());
                if (i.equals(1)) {
                    result = false;
                    stringBuffer.append(dto.getBrand() + "已禁用");
                }
            }
        }
        if (StringUtil.isNull(dto.getName())) {
            result = false;
            stringBuffer.append("，名称不能为空");
        }
        if (Objects.isNull(dto.getNumber())) {
            result = false;
            stringBuffer.append("，数量不能为空");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(dto.getUnit()) || !unitMap.containsKey(dto.getUnit())) {
            result = false;
            stringBuffer.append("，物料[" + dto.getName() + " " + dto.getModel() + "]的单位不能为空且必须为字典项measurement_unit的值");
        } else {
            dto.setUnitCode(unitMap.get(dto.getUnit()));
        }
        if (StringUtil.isNull(dto.getFigureNumber())) {
            result = false;
            stringBuffer.append("，图号不能为空");
        }
        if (StringUtil.isNull(dto.getChartVersion())) {
            result = false;
            stringBuffer.append("，图纸版本号不能为空");
        }
        //校验导入日
        if (StringUtils.isNotEmpty(dto.getDeliveryTimeStr())) {
            if (DateUtils.isValidDate(dto.getDeliveryTimeStr(), FORMAT_SHORT)) {
                dto.setDeliveryTime(DateUtils.parse(dto.getDeliveryTimeStr(), FORMAT_SHORT));
            } else {
                result = false;
                stringBuffer.append("，物料到货日期格式不正确");
            }
        }
        dto.setValidResult(stringBuffer.toString());

        return result;
    }

    @Override
    public void checkKsMaterial(MilepostDesignPlanDetailDto dto, Long storageId) {
        if ("外购物料".equals(dto.getMaterialCategory()) || "看板物料".equals(dto.getMaterialCategory())) {
            if ("机械加工件".equals(dto.getCodingMiddleClass())) { //物料中类+品牌+图号 检索相同编码
                List<Material> materials = materialExtService.checkMaterial1(dto.getCodingMiddleClass(),
                        dto.getBrand(), dto.getFigureNumber(), null);
                if (ListUtils.isNotEmpty(materials)) {
                    logger.info("物料基础表已存在物料中类：{}，品牌：{}，图号：{}物料，直接引用", dto.getCodingMiddleClass(), dto.getBrand(),
                            dto.getFigureNumber());
                    dto = setDtoNew(materials.get(0), dto);
                } else {
                    //去临时表查
                    MilepostDesignPlanDetailMiddle milepostDesignPlanDetailMiddle =
                            checkMaterial1(dto.getCodingMiddleClass(), dto.getBrand(), dto.getFigureNumber());
                    dto.setPamCode(null == milepostDesignPlanDetailMiddle ?
                            materialExtService.generateMaterialPAMCode() : milepostDesignPlanDetailMiddle.getPamCode());
                }
            } else {
                if (null != dto.getBrandMaterialCode()) { //品牌商物料编码存在  物料中类+品牌+品牌商物料编码 检索相同编码
                    List<Material> materials = materialExtService.checkMaterial3(dto.getCodingMiddleClass(),
                            dto.getBrand(), dto.getBrandMaterialCode(), null);
                    if (ListUtils.isNotEmpty(materials)) { //物料中类+品牌+规格/型号 检索相同编码
                        MilepostDesignPlanDetailMiddle middle = checkMaterial3(dto.getCodingMiddleClass(),
                                dto.getBrand(), dto.getBrandMaterialCode());
                        if (null != middle) {
                            dto.setPamCode(middle.getPamCode());
                        } else {
                            List<Material> materials1 = materialExtService.checkMaterial2(dto.getCodingMiddleClass(),
                                    dto.getBrand(), dto.getModel(), null);
                            if (ListUtils.isNotEmpty(materials1)) {
                                logger.info("物料基础表已存在物料中类：{}，品牌：{}，规格/型号：{}物料，直接引用", dto.getCodingMiddleClass(),
                                        dto.getBrand(), dto.getModel());
                                dto = setDtoNew(materials1.get(0), dto);
                            } else {
                                MilepostDesignPlanDetailMiddle milepostDesignPlanDetailMiddle =
                                        checkMaterial2(dto.getCodingMiddleClass(), dto.getBrand(), dto.getModel());
                                dto.setPamCode(null == milepostDesignPlanDetailMiddle ?
                                        materialExtService.generateMaterialPAMCode() :
                                        milepostDesignPlanDetailMiddle.getPamCode());
                            }
                        }
                    } else {
                        logger.info("物料基础表已存在物料中类：{}，品牌：{}，规格/型号：{}物料，直接引用", dto.getCodingMiddleClass(),
                                dto.getBrand(), dto.getModel());
                        dto = setDtoNew(materials.get(0), dto);
                    }
                } else { //品牌商物料编码不存在  物料中类+品牌+规格/型号 检索相同编码
                    List<Material> materials = materialExtService.checkMaterial2(dto.getCodingMiddleClass(),
                            dto.getBrand(), dto.getModel(), null);
                    if (ListUtils.isNotEmpty(materials)) {
                        logger.info("物料基础表已存在物料中类：{}，品牌：{}，规格/型号：{}物料，直接引用", dto.getCodingMiddleClass(),
                                dto.getBrand(), dto.getModel());
                        dto = setDtoNew(materials.get(0), dto);
                    } else {
                        MilepostDesignPlanDetailMiddle milepostDesignPlanDetailMiddle =
                                checkMaterial2(dto.getCodingMiddleClass(), dto.getBrand(), dto.getModel());
                        dto.setPamCode(null == milepostDesignPlanDetailMiddle ?
                                materialExtService.generateMaterialPAMCode() :
                                milepostDesignPlanDetailMiddle.getPamCode());
                    }
                }
            }
        } else {
            // 装配件、虚拟件,自动生成pamCode
            dto.setPamCode(materialExtService.generateMaterialPAMCode());
        }
        if (null == dto.getMaterielDescr()) {
            ksGenerateMaterielDescr(dto);//物料描述
        }
        MaterialCostDto materialCostDto = materialCostExtService.matchingDesignCost(dto.getPamCode(), storageId);
        if (Objects.nonNull(materialCostDto)) {
            if (null != materialCostDto.getMaterialCategory() && Objects.equals(materialCostDto.getMaterialCategory()
                    , dto.getMaterialCategory())) { //因为原来的估价表没有物料类型的字段，因此不带出非昆山物料的字段属性
                dto.setDesignCostId(materialCostDto.getId());
                dto.setDesignCost(materialCostDto.getItemCost());
                dto.setUnitCode(materialCostDto.getUnit());
                dto.setUnit(materialCostDto.getUnitName());
                dto.setMaterielType(materialCostDto.getMaterielType());
                dto.setMachiningPartType(materialCostDto.getMachiningPartType());
                dto.setMaterial(materialCostDto.getMaterial());
                dto.setUnitWeight(materialCostDto.getUnitWeight());
                dto.setMaterialProcessing(materialCostDto.getMaterialProcessing());

                dto.setCodingMiddleClass(materialCostDto.getCodingMiddleclass());//物料中类
                dto.setFigureNumber(materialCostDto.getFigureNumber());//图号
                dto.setChartVersion(materialCostDto.getChartVersion());//图纸版本号
                dto.setBrandMaterialCode(materialCostDto.getBrandMaterialCode());//品牌商物料编码
                dto.setOrSparePartsMask(materialCostDto.getOrSparePartsMask());//备件标识
                dto.setBrand(materialCostDto.getBrand()); //品牌
                dto.setName(materialCostDto.getName()); //名称
                dto.setModel(materialCostDto.getModel()); //规格型号
                dto.setMaterielDescr(materialCostDto.getDescr()); // 物料描述
            }
        }
    }

    private MilepostDesignPlanDetailMiddle checkMaterial3(String codingMiddleClass, String brand,
                                                          String brandMaterialCode) {
        return middleExtMapper.checkMaterial3(codingMiddleClass, brand, brandMaterialCode);
    }

    private MilepostDesignPlanDetailMiddle checkMaterial2(String codingMiddleClass, String brand, String model) {
        return middleExtMapper.checkMaterial2(codingMiddleClass, brand, model);
    }

    private MilepostDesignPlanDetailMiddle checkMaterial1(String codingMiddleClass, String brand, String figureNumber) {
        return middleExtMapper.checkMaterial1(codingMiddleClass, brand, figureNumber);
    }

    @Override
    public void saveMilepostDesignDetailMiddle(MilepostDesignPlanDetailMiddle milepostDesignPlanDetailMiddle,
                                               long batch) {
        if (null == milepostDesignPlanDetailMiddle.getId()) {
            milepostDesignPlanDetailMiddle.setCreateAt(new Date());
            milepostDesignPlanDetailMiddle.setCreateBy(SystemContext.getUserId());
            milepostDesignPlanDetailMiddle.setMarkId(batch);
            milepostDesignPlanDetailMiddle.setDeletedFlag(false);
            middleMapper.insertSelective(milepostDesignPlanDetailMiddle);
        } else {
            milepostDesignPlanDetailMiddle.setUpdateAt(new Date());
            milepostDesignPlanDetailMiddle.setUpdateBy(SystemContext.getUserId());
            middleMapper.updateByPrimaryKeySelective(milepostDesignPlanDetailMiddle);
        }
    }

    private MilepostDesignPlanDetailDto setDtoNew(Material materialDto, MilepostDesignPlanDetailDto dto) {
        dto.setPamCode(materialDto.getPamCode());
        dto.setErpCode(materialDto.getItemCode());
        dto.setMaterielDescr(materialDto.getItemInfo()); // 物料描述
        dto.setUnitCode(materialDto.getUnit());//基本计量单位
        if (null != materialDto.getUnit()) {
            DictDto unit = CacheDataUtils.findDictByTypeAndCode(DictType.MEASUREMENT_UNIT.code(),
                    materialDto.getUnit());
            if (null != unit) {
                dto.setUnit(unit.getName());
            }
        }
        dto.setMaterielType(materialDto.getMaterialType()); //物料小类
        dto.setMaterialClassification(materialDto.getMaterialClassification()); //物料大类
        dto.setName(materialDto.getName()); //名称
        dto.setModel(materialDto.getModel()); //规格型号
        dto.setBrand(materialDto.getBrand()); //品牌
        dto.setMachiningPartType(materialDto.getMachiningPartType());//加工件分类
        dto.setMaterial(materialDto.getMaterial());//材料
        dto.setMaterialProcessing(materialDto.getMaterialProcessing());//材质处理
        dto.setUnitWeight(materialDto.getUnitWeight());//单位重量

        dto.setCodingMiddleClass(materialDto.getCodingMiddleclass()); //物料中类
        dto.setFigureNumber(materialDto.getFigureNumber()); //图号
        dto.setChartVersion(materialDto.getChartVersion()); //图纸版本
        dto.setBrandMaterialCode(materialDto.getBrandMaterialCode());  //品牌商物料编码
        dto.setOrSparePartsMask(materialDto.getOrSparePartsMask()); //备件标识
        dto.setPerchasingLeadtime(StringUtils.isEmpty(materialDto.getBuyerRound()) ? null :
                Long.parseLong(materialDto.getBuyerRound())); //采购提前期
        dto.setMinPerchaseQuantity(materialDto.getMinimumOrderQuantity()); //最小订货量（最小采购量）
        dto.setMinPackageQuantity(StringUtils.isEmpty(materialDto.getFixedLotMultiplier()) ? null :
                Long.parseLong(materialDto.getFixedLotMultiplier())); //固定批次增加（最小包装量）

        if (StringUtils.isNotEmpty(dto.getDeliveryTimeStr())) {
            if (DateUtils.isValidDate(dto.getDeliveryTimeStr(), FORMAT_SHORT)) {
                dto.setDeliveryTime(DateUtils.parse(dto.getDeliveryTimeStr(), FORMAT_SHORT));
            } else {
                dto.setValidResult("物料到货日期格式不正确");
            }
        }

        return dto;

    }

    private MilepostDesignPlanDetailSubmitHistoryDto saveSubmitHistory(MilepostDesignPlanDetailDto dto, Long userBy, Boolean isCreateMaterialAndCost,
                                                                       ProjectWbsReceiptsDto recordDto, String releaseLotNumber,
                                                                       List<MilepostDesignPlanDetail> updatePlanDetailList,
                                                                       List<TwoTuple<TwoTuple<Long, Boolean>, MilepostDesignPlanDetailDto>> twoTupleList) {
        MilepostDesignPlanDetailSubmitHistoryDto submitHistoryDto = BeanConverter.copyProperties(dto, MilepostDesignPlanDetailSubmitHistoryDto.class);
        submitHistoryDto.setId(null);
        submitHistoryDto.setProjectWbsReceiptsId(recordDto.getId());
        submitHistoryDto.setUpdateBy(null);
        submitHistoryDto.setUpdateAt(null);

        if (null != dto.getDesignPlanDetailId()) {
            submitHistoryDto.setModuleStatus(null);
            if (!Boolean.FALSE.equals(dto.getWbsLastLayer())) {
                MilepostDesignPlanDetail milepostDesignPlanDetail = new MilepostDesignPlanDetail();
                milepostDesignPlanDetail.setId(dto.getDesignPlanDetailId());
                milepostDesignPlanDetail.setStatus(CheckStatus.WBS_RECEIPTS_DRAFT.code());
                updatePlanDetailList.add(milepostDesignPlanDetail);
            }
        } else {
            submitHistoryDto.setDesignReleaseLotNumber(releaseLotNumber);
            submitHistoryDto.setWbsConfirmFlag(true);
            submitHistoryDto.setHistoryType(true);
        }

        MilepostDesignPlanDetailSubmitHistoryDto historyDto = milepostDesignPlanDetailSubmitHistoryService.save(submitHistoryDto, userBy);
        twoTupleList.add(new TwoTuple<>(new TwoTuple<>(historyDto.getId(), isCreateMaterialAndCost), dto));
        return historyDto;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> selectDetailByIds(List<Long> ids) {
        return milepostDesignPlanDetailExtMapper.selectDetailByIds(ids);
    }

    @Override
    public List<MilepostDesignPlanDetail> selectByExample(MilepostDesignPlanDetailExample example) {
        return milepostDesignPlanDetailMapper.selectByExample(example);
    }

    @Override
    public List<MilepostDesignPlanDetail> selectByParentId(Long parentId) {
        return milepostDesignPlanDetailExtMapper.selectByParentId(parentId);
    }

    @Override
    public void deleteByDetailIds(List<Long> detailIds) {
        milepostDesignPlanDetailExtMapper.deleteByDetailIds(detailIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importDesignPlanDetailKUKA2022(List<ImportDesignPlanDetailKUKA2022ExcelVo> detailExcelVOList) {
        List<DictDto> dictDtoList = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Map<String, DictDto> unitMap = dictDtoList.stream().collect(Collectors.toMap(DictDto::getName, Function.identity()));
        Map<String, OrganizationRel> organizationMap = queryCurrentUnitOrganization();

        List<String> projectCodeList = new ArrayList<>();
        List<String> wbsSummaryCodeList = new ArrayList<>();
        List<String> pamCodeList = new ArrayList<>();
        List<String> erpCodeList = new ArrayList<>();
        List<Long> organizationIdList = new ArrayList<>();
        Map<String, Long> orgMap = new HashMap<>();
        Map<String, String> parentPamCodeMap = new HashMap<>();
        for (ImportDesignPlanDetailKUKA2022ExcelVo excelVo : detailExcelVOList) {
            String projectCode = excelVo.getProjectCode();
            projectCodeList.add(projectCode);
            String wbsSummaryCode = excelVo.getWbsSummaryCode();
            wbsSummaryCodeList.add(wbsSummaryCode);
            String pamCode = excelVo.getPamCode();
            pamCodeList.add(pamCode);
            String erpCode = excelVo.getErpCode();
            if (StringUtils.isNotEmpty(erpCode)) {
                erpCodeList.add(erpCode);
            }

            String organizationCode = excelVo.getOrganizationCode();
            OrganizationRel organizationRel = organizationMap.get(organizationCode);
            Long organizationId = organizationRel.getOrganizationId();
            if (Objects.nonNull(organizationId)) {
                organizationIdList.add(organizationId);
                orgMap.put(organizationCode, organizationId);
            }
            String parentPamCode = excelVo.getParentPamCode();
            if (StringUtils.isNotEmpty(parentPamCode)) {
                parentPamCodeMap.put(getPamCodeAndWbsSummaryCodeAndDeliveryTimeKey(pamCode, wbsSummaryCode, excelVo.getDeliveryTime()),
                        parentPamCode);
            }
        }
        ProjectExample example = new ProjectExample();
        example.createCriteria().andCodeIn(projectCodeList);
        List<Project> projectList = projectService.selectByExample(example);
        Guard.notNullOrEmpty(projectList, "所有的项目号-新都不存在不存在");
        Map<String, Long> projectMap = new HashMap<>();
        List<Long> projectIdList = new ArrayList<>();
        for (Project project : projectList) {
            projectMap.put(project.getCode(), project.getId());
            projectIdList.add(project.getId());
        }

        ProjectWbsBudgetSummaryExample projectWbsBudgetSummaryExample = new ProjectWbsBudgetSummaryExample();
        projectWbsBudgetSummaryExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdIn(projectIdList)
                .andSummaryTypeEqualTo(WbsBudgetFieldConstant.WBS).andSummaryCodeIn(wbsSummaryCodeList);
        List<ProjectWbsBudgetSummary> projectWbsBudgetSummaryList = projectWbsBudgetSummaryMapper.selectByExample(projectWbsBudgetSummaryExample);
        Map<String, List<ProjectWbsBudgetSummary>> projectWbsBudgetSummaryMap = CollectionUtils.isEmpty(projectWbsBudgetSummaryList) ? new HashMap<>()
                : projectWbsBudgetSummaryList.stream().collect(Collectors.groupingBy(e -> getProjectWbsBudgetSummaryKey(e.getParentId(),
                e.getSummaryCode())));

        List<Material> pamCodeMaterialList = materialExtService.invokeMaterialApiGetByPamCodeListAndOrgIdList(pamCodeList, organizationIdList);
        Map<String, Material> pamCodeMaterialMap = CollectionUtils.isEmpty(pamCodeMaterialList) ? new HashMap<>()
                : pamCodeMaterialList.stream().collect(Collectors.toMap(e -> getPamCodeMaterialKey(e.getPamCode(), e.getOrganizationId()),
                Function.identity()));

        Map<String, MaterialDto> erpCodeMaterialMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(erpCodeList)) {
            List<MaterialDto> materialDtoList = materialExtService.invokeMaterialApiGetByErpCodeAndOrgIdList(erpCodeList, organizationIdList);
            if (!CollectionUtils.isEmpty(materialDtoList)) {
                erpCodeMaterialMap.putAll(materialDtoList.stream().collect(Collectors.toMap(e ->
                        getErpCodeMaterialKey(e.getErpCode(), e.getOrganizationId()), Function.identity())));
            }
        }

        List<Boolean> booleanList = new ArrayList<>();
        List<String> msgList = new ArrayList<>();
        int threadNum = detailExcelVOList.size() / EACH_THREAD_DATA_NUM + (detailExcelVOList.size() % EACH_THREAD_DATA_NUM == 0 ? 0 : 1);
        List<MilepostDesignPlanDetail> designPlanDetailList = new ArrayList<>();
        final CountDownLatch countDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * EACH_THREAD_DATA_NUM;
            int endIndex = i == threadNum ? detailExcelVOList.size() : i * EACH_THREAD_DATA_NUM;
            List<ImportDesignPlanDetailKUKA2022ExcelVo> eachTreadExcelVos = detailExcelVOList.subList(startIndex, endIndex);
            importDesignPlanDetailKUKA2022Executor.execute(() -> {
                try {
                    // 多线程异步执行主干导入方法
                    List<MilepostDesignPlanDetail> addDesignPlanDetailList = executeEachTreadExcelVos(eachTreadExcelVos, unitMap, orgMap, projectMap,
                            projectWbsBudgetSummaryMap, pamCodeMaterialMap, erpCodeMaterialMap);
                    designPlanDetailList.addAll(addDesignPlanDetailList);
                } catch (ApplicationBizException e) {
                    msgList.add(e.getMessage());
                    booleanList.add(false);
                } catch (Exception e) {
                    logger.error("importDesignPlanDetailKUKA2022的executeEachTreadExcelVos操作记录失败", e);
                    booleanList.add(false);
                } finally {
                    countDownLatch.countDown();
                    if (Objects.equals(countDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after importDesignPlanDetailKUKA2022 executeEachTreadExcelVos", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("importDesignPlanDetailKUKA2022的executeEachTreadExcelVos的await失败", ex);
            Thread.currentThread().interrupt();
        }
        if (booleanList.contains(false)) {
            if (CollectionUtils.isEmpty(msgList)) {
                throw new ApplicationBizException("柔性项目详细设计初始化导入校验赋值失败");
            } else {
                throw new ApplicationBizException(msgList.toString());
            }
        }

        batchInsertAndUpdateDesignPlanDetailList(designPlanDetailList, parentPamCodeMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsertAndUpdateDesignPlanDetailList(List<MilepostDesignPlanDetail> designPlanDetailList, Map<String, String> parentPamCodeMap) {

        List<MilepostDesignPlanDetail> finalDesignPlanDetailList = new ArrayList<>();
        // 先批量添加所有数据
        Map<String, Long> parentIdMap = new HashMap<>();
        List<Boolean> booleanList = new ArrayList<>();
        List<String> msgList = new ArrayList<>();
        int threadNum = designPlanDetailList.size() / EACH_THREAD_DATA_NUM + (designPlanDetailList.size() % EACH_THREAD_DATA_NUM == 0 ? 0 : 1);
        final CountDownLatch addCountDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * EACH_THREAD_DATA_NUM;
            int endIndex = i == threadNum ? designPlanDetailList.size() : i * EACH_THREAD_DATA_NUM;
            List<MilepostDesignPlanDetail> addDetailList = designPlanDetailList.subList(startIndex, endIndex);
            importDesignPlanDetailKUKA2022Executor.execute(() -> {
                try {
                    // 多线程异步执行批量添加方法
                    batchInsertDesignPlanDetailList(addDetailList, parentIdMap);
                    finalDesignPlanDetailList.addAll(addDetailList);
                } catch (ApplicationBizException e) {
                    msgList.add(e.getMessage());
                    booleanList.add(false);
                } catch (Exception e) {
                    logger.error("importDesignPlanDetailKUKA2022的batchInsertDesignPlanDetailList操作记录失败", e);
                    booleanList.add(false);
                } finally {
                    addCountDownLatch.countDown();
                    if (Objects.equals(addCountDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after importDesignPlanDetailKUKA2022 batchInsertDesignPlanDetailList", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            });
        }
        try {
            addCountDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("importDesignPlanDetailKUKA2022的batchInsertDesignPlanDetailList的await失败", ex);
            Thread.currentThread().interrupt();
        }
        if (booleanList.contains(false)) {
            if (CollectionUtils.isEmpty(msgList)) {
                throw new ApplicationBizException("柔性项目详细设计初始化导入批量添加失败");
            } else {
                throw new ApplicationBizException(msgList.toString());
            }
        }

        // 然后批量修改有parentId的数据
        final CountDownLatch updateCountDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * EACH_THREAD_DATA_NUM;
            int endIndex = i == threadNum ? finalDesignPlanDetailList.size() : i * EACH_THREAD_DATA_NUM;
            List<MilepostDesignPlanDetail> updateDetailList = finalDesignPlanDetailList.subList(startIndex, endIndex);
            importDesignPlanDetailKUKA2022Executor.execute(() -> {
                try {
                    // 多线程异步执行批量修改方法
                    batchUpdateDesignPlanDetailList(updateDetailList, parentPamCodeMap, parentIdMap);
                } catch (ApplicationBizException e) {
                    msgList.add(e.getMessage());
                    booleanList.add(false);
                } catch (Exception e) {
                    logger.error("importDesignPlanDetailKUKA2022的batchUpdateDesignPlanDetailList操作记录失败", e);
                    booleanList.add(false);
                } finally {
                    updateCountDownLatch.countDown();
                    if (Objects.equals(updateCountDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after importDesignPlanDetailKUKA2022 batchUpdateDesignPlanDetailList", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            });
        }
        try {
            updateCountDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("importDesignPlanDetailKUKA2022的batchUpdateDesignPlanDetailList的await失败", ex);
            Thread.currentThread().interrupt();
        }
        if (booleanList.contains(false)) {
            if (CollectionUtils.isEmpty(msgList)) {
                throw new ApplicationBizException("柔性项目详细设计初始化导入批量修改失败");
            } else {
                throw new ApplicationBizException(msgList.toString());
            }
        }

    }

    private List<MilepostDesignPlanDetail> executeEachTreadExcelVos(List<ImportDesignPlanDetailKUKA2022ExcelVo> eachTreadExcelVos, Map<String,
            DictDto> unitMap,
                                                                    Map<String, Long> orgMap, Map<String, Long> projectMap,
                                                                    Map<String, List<ProjectWbsBudgetSummary>> projectWbsBudgetSummaryMap,
                                                                    Map<String, Material> pamCodeMaterialMap,
                                                                    Map<String, MaterialDto> erpCodeMaterialMap) {
        List<MilepostDesignPlanDetail> addDesignPlanDetailList = new ArrayList<>();
        for (ImportDesignPlanDetailKUKA2022ExcelVo excelVo : eachTreadExcelVos) {
            MilepostDesignPlanDetail detail = new MilepostDesignPlanDetail();
            Integer serialNumber = excelVo.getSerialNumber();
            // 组织代码
            String organizationCode = excelVo.getOrganizationCode();
            Long organizationId = orgMap.get(organizationCode);
            Guard.notNull(organizationId, String.format("序号：%s的组织代码：%s在当前单位下不存在", serialNumber, organizationCode));
            // 项目号-新
            String projectCode = excelVo.getProjectCode();
            Long projectId = projectMap.get(projectCode);
            Guard.notNull(projectId, String.format("序号：%s的项目号-新：%s不存在", serialNumber, projectCode));
            detail.setProjectId(projectId);
            // PAM编码-新
            String pamCode = excelVo.getPamCode();
            Material pamCodeMaterial = pamCodeMaterialMap.get(getPamCodeMaterialKey(pamCode, organizationId));
            Guard.notNull(pamCodeMaterial, String.format("序号：%s的PAM编码-新：%s不存在", serialNumber, pamCode));

            /*MilepostDesignPlanDetailExample planDetailExample1 = new MilepostDesignPlanDetailExample();
            planDetailExample1.createCriteria().andPamCodeEqualTo(pamCode).andWbsSummaryCodeEqualTo(wbsSummaryCode)
                    .andDeliveryTimeEqualTo(excelVo.getDeliveryTime()).andDeletedFlagEqualTo(Boolean.FALSE);
            List<MilepostDesignPlanDetail> detailList1 =
                    milepostDesignPlanDetailMapper.selectByExample(planDetailExample1);
            if (!CollectionUtils.isEmpty(detailList1)) {
                throw new ApplicationBizException(String.format("序号：%s的PAM编码-新：%sWBS号：%s期望交货日期：%s在详细设计表中已存在",
                        serialNumber, pamCode, wbsSummaryCode, DateUtils.format(excelVo.getDeliveryTime())));
            }*/
            detail.setPamCode(pamCode);

            // WBS号
            String wbsSummaryCode = excelVo.getWbsSummaryCode();
            Guard.isTrue(wbsSummaryCode.startsWith(projectCode), String.format("序号：%s的WBS号：%s对应的项目号-新：%s不正确",
                    serialNumber, wbsSummaryCode, projectCode));
            detail.setWbsSummaryCode(wbsSummaryCode);
            // ERP编码-新
            String erpCode = excelVo.getErpCode();
            String materialCategory = excelVo.getMaterialCategory();
            if (Objects.equals(materialCategory, MaterialCategoryEnum.PURCHASED_PARTS.msg())) {
                Guard.notNullOrEmpty(erpCode, String.format("序号：%s的外购物料时ERP编码-新不能为空", serialNumber));
                MaterialDto erpCodeMaterial = erpCodeMaterialMap.get(getErpCodeMaterialKey(erpCode, organizationId));
                Guard.notNull(erpCodeMaterial, String.format("序号：%s的ERP编码-新：%s不存在", serialNumber, erpCode));
                detail.setErpCode(erpCode);
            }
            // 计量单位名称-新
            String unit = excelVo.getUnit();
            DictDto dictDto = unitMap.get(unit);
            Guard.notNull(dictDto, String.format("序号：%s的计量单位名称-新：%s不存在", serialNumber, unit));
            detail.setUnit(unit);
            detail.setUnitCode(dictDto.getCode());
            // 父级编码-新
            String parentPamCode = excelVo.getParentPamCode();
            if (StringUtils.isNotEmpty(parentPamCode)) {
                Material parentPamCodeMaterial = pamCodeMaterialMap.get(getPamCodeMaterialKey(parentPamCode, organizationId));
                Guard.notNull(parentPamCodeMaterial, String.format("序号：%s的父级编码-新：%s不存在", serialNumber, parentPamCode));
            }

            // 已生成采购需求数量
            BigDecimal requirementNum = excelVo.getRequirementNum();
            if (requirementNum.compareTo(BigDecimal.ZERO) > 0) {
                Guard.equals(materialCategory, MaterialCategoryEnum.PURCHASED_PARTS.msg(),
                        String.format("序号：%s的已生成采购需求数量有值时，物料类型只能为外购物料", serialNumber));
            }
            detail.setNumber(excelVo.getNumber());
            // 是否预算层数据
            if (excelVo.getBudgetLayerIs()) {
                // 如果是预算层需要校验WBS号在预算表中是否存在
                /*List<ProjectWbsBudgetSummary> summaryList = projectWbsBudgetSummaryMap.get(getProjectWbsBudgetSummaryKey(projectId,
                wbsSummaryCode));
                Guard.notNullOrEmpty(summaryList, String.format("序号：%s是预算层数据，WBS号：%s不存在", serialNumber, wbsSummaryCode));*/
            }
            List<ProjectWbsBudgetSummary> summaryList = projectWbsBudgetSummaryMap.get(getProjectWbsBudgetSummaryKey(projectId, projectCode));
            if (!CollectionUtils.isEmpty(summaryList)) {
                if (Boolean.TRUE.equals(summaryList.get(0).getProjectDetailSelectFlag())) {
                    detail.setWbsLastLayer(true);
                    detail.setWbsConfirmFlag(true);
                } else {
                    detail.setWbsLastLayer(false);
                    detail.setWbsConfirmFlag(false);
                }
            }

            detail.setGenerateRequirement(excelVo.getProjectWbsRequirementPublishIs());
            detail.setDesignReleaseLotNumber(excelVo.getDesignReleaseLotNumber());
            // 物料类型
            detail.setMaterialCategory(materialCategory);
            if (MaterialCategoryEnum.ASSEMBLY_PARTS.msg().equals(materialCategory)) {
                detail.setWhetherModel(Boolean.TRUE);
            }
            detail.setExtIs(excelVo.getExtIs());
            //物料描述
            detail.setMaterielDescr(pamCodeMaterial.getItemInfo());
            // 名称
            detail.setName(pamCodeMaterial.getName());
            // 型号/规格/图号
            detail.setModel(pamCodeMaterial.getModel());
            // 品牌
            detail.setBrand(pamCodeMaterial.getBrand());
            // 物料大类
            detail.setMaterialClassification(pamCodeMaterial.getMaterialClassification());
            // 物料中类
            detail.setCodingMiddleClass(pamCodeMaterial.getCodingMiddleclass());
            // 物料小类
            detail.setMaterielType(pamCodeMaterial.getMaterialType());
            // 加工件分类
            detail.setMachiningPartType(pamCodeMaterial.getMachiningPartType());
            // 材质
            detail.setMaterial(pamCodeMaterial.getMaterial());
            // 图号
            detail.setFigureNumber(pamCodeMaterial.getFigureNumber());
            // 图纸版本号
            detail.setChartVersion(pamCodeMaterial.getChartVersion());
            // 备件标识
            detail.setOrSparePartsMask(pamCodeMaterial.getOrSparePartsMask());
            // 品牌商物料编码
            detail.setBrandMaterialCode(pamCodeMaterial.getBrandMaterialCode());
            // 单位重量(Kg)
            detail.setUnitWeight(pamCodeMaterial.getUnitWeight());
            // 材质处理
            detail.setMaterialProcessing(pamCodeMaterial.getMaterialProcessing());

            detail.setNumber(excelVo.getNumber());
            detail.setDeliveryTime(excelVo.getDeliveryTime());
            detail.setDeletedFlag(false);
            detail.setWbsLayer(excelVo.getWbsLayer());
            detail.setDispatchIs(excelVo.getDispatchIs());
            detail.setInit(true);
            detail.setRemark("初始化" + DateUtils.getNow());

            addDesignPlanDetailList.add(detail);
        }
        return addDesignPlanDetailList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchInsertDesignPlanDetailList(List<MilepostDesignPlanDetail> addDesignPlanDetailList, Map<String, Long> parentIdMap) {
        if (CollectionUtils.isEmpty(addDesignPlanDetailList)) {
            return;
        }
        milepostDesignPlanDetailExtMapper.batchInsert(addDesignPlanDetailList);
        for (MilepostDesignPlanDetail detail : addDesignPlanDetailList) {
            String key = getPamCodeAndWbsSummaryCodeAndDeliveryTimeKey(detail.getPamCode(), detail.getWbsSummaryCode(), detail.getDeliveryTime());
            parentIdMap.put(key, detail.getId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateDesignPlanDetailList(List<MilepostDesignPlanDetail> updateDesignPlanDetailList, Map<String, String> parentPamCodeMap,
                                                Map<String, Long> parentIdMap) {
        if (CollectionUtils.isEmpty(updateDesignPlanDetailList)) {
            return;
        }
        List<MilepostDesignPlanDetail> detailList = new ArrayList<>();
        for (MilepostDesignPlanDetail detail : updateDesignPlanDetailList) {
            String key = getPamCodeAndWbsSummaryCodeAndDeliveryTimeKey(detail.getPamCode(), detail.getWbsSummaryCode(), detail.getDeliveryTime());
            String parentPamCode = parentPamCodeMap.get(key);
            if (StringUtils.isNotEmpty(parentPamCode)) {
                parentIdMap.forEach((k, v) -> {
                    if (Objects.equals(k.split("_")[1], parentPamCode) && detail.getWbsSummaryCode().startsWith(k.split("_")[2])) {
                        detail.setParentId(v);
                        detailList.add(detail);
                    }
                });
            }
        }
        if (!CollectionUtils.isEmpty(detailList)) {
            milepostDesignPlanDetailExtMapper.batchUpdate(detailList);
        }
    }


    private String getPamCodeAndWbsSummaryCodeAndDeliveryTimeKey(String pamCode, String wbsSummaryCode, Date deliveryTime) {
        return String.format("getPamCodeAndWbsSummaryCodeAndDeliveryTimeKey_%s_%s_%s", pamCode, wbsSummaryCode, DateUtils.format(deliveryTime));
    }

    private String getProjectWbsBudgetSummaryKey(Long projectId, String wbsSummaryCode) {
        return String.format("getProjectWbsBudgetSummaryKey_%s_%s", projectId, wbsSummaryCode);
    }

    private String getPamCodeMaterialKey(String pamCode, Long organizationId) {
        return String.format("getPamCodeMaterialKey_%s_%s", pamCode, organizationId);
    }

    private String getErpCodeMaterialKey(String pamCode, Long organizationId) {
        return String.format("getErpCodeMaterialKey_%s_%s", pamCode, organizationId);
    }

    private Map<String, OrganizationRel> queryCurrentUnitOrganization() {
        Map<String, Object> param = new HashMap<>();
        param.put("unitId", SystemContext.getUnitId());
        String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operatingUnit/queryCurrentUnitOu", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OperatingUnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
        Map<String, OrganizationRel> organizationMap = new HashMap<>();
        List<OperatingUnitDto> operatingUnitDtoList = response.getData();
        if (CollectionUtils.isEmpty(operatingUnitDtoList)) {
            return organizationMap;
        }
        for (OperatingUnitDto operatingUnitDto : operatingUnitDtoList) {
            List<OrganizationRel> rels = operatingUnitDto.getRels();
            for (OrganizationRel organizationRel : rels) {
                if (organizationRel.getOrganizationId() != null) {
                    organizationMap.put(organizationRel.getOrganizationCode(), organizationRel);
                }
            }
        }
        return organizationMap;
    }

    /**
     * 批量设置当前设计信息的各种数量之和
     *
     * @param designPlanDetailList
     */
    @Override
    public void batchSetNumSum(List<MilepostDesignPlanDetailDto> designPlanDetailList, Project project) {
        // 根据项目id查询所有物料需求的已下达量
        Map<String, BigDecimal> orderNumMap = purchaseOrderService.queryOrderNumMapByProjectId(project.getId());
        Map<String, BigDecimal> warehousingNumMap = purchaseProgressService.queryWarehousingNumMapByProjectId(project.getCode());
        Map<String, BigDecimal> getNumMap = materialGetService.queryGetNumMapByProjectId(project.getId());
        Map<String, BigDecimal> returnNumMap = materialReturnService.queryReturnNumMapByProjectId(project.getId());
        for (MilepostDesignPlanDetailDto sonDesignPlanDetail : designPlanDetailList) {
            if (StringUtils.isEmpty(sonDesignPlanDetail.getErpCode())) {
                continue;
            }
            // 已下达量
            if (null != sonDesignPlanDetail.getDeliveryTime()) {
                BigDecimal releasedQuantity = orderNumMap.get(sonDesignPlanDetail.getErpCode() + "-"
                        + sonDesignPlanDetail.getWbsSummaryCode() + "-"
                        + DateUtils.format(sonDesignPlanDetail.getDeliveryTime(), FORMAT_SHORT));
                sonDesignPlanDetail.setOrderNumSum(releasedQuantity != null ? releasedQuantity : BigDecimal.ZERO);
            } else {
                sonDesignPlanDetail.setOrderNumSum(BigDecimal.ZERO);
            }

            //领用数量
            BigDecimal getNum = getNumMap.get(sonDesignPlanDetail.getErpCode());
            getNum = getNum != null ? getNum : BigDecimal.ZERO;
            BigDecimal returnNum = returnNumMap.get(sonDesignPlanDetail.getErpCode());
            returnNum = returnNum != null ? returnNum : BigDecimal.ZERO;
            sonDesignPlanDetail.setRecipientsNumSum(getNum.subtract(returnNum));

            //接受/入库数量
            BigDecimal warehousingNum = warehousingNumMap.get(sonDesignPlanDetail.getErpCode());
            sonDesignPlanDetail.setWarehousingNumSum(warehousingNum != null ? warehousingNum : BigDecimal.ZERO);
        }
    }

    @Override
    public Long  getProjectDesignDetailExistGenerateRequirement(Long projectId) {
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(projectId)
                .andGenerateRequirementEqualTo(Boolean.TRUE);
       return milepostDesignPlanDetailMapper.countByExample(example);

    }
}
