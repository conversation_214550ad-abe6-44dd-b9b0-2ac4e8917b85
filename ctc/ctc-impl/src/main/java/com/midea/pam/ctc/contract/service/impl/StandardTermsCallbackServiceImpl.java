package com.midea.pam.ctc.contract.service.impl;

import com.midea.pam.common.ctc.entity.StandardTerms;
import com.midea.pam.ctc.common.enums.StandardTermsEnum;
import com.midea.pam.ctc.contract.service.StandardTermsCallbackService;
import com.midea.pam.ctc.mapper.StandardTermsMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

public class StandardTermsCallbackServiceImpl implements StandardTermsCallbackService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private StandardTermsMapper standardTermsMapper;

    /**
     * 提交审批
     *
     * @param formInstanceId
     */
    @Override
    public void draftSubmit(Long formInstanceId) {
        logger.info("标准条款新增审批提交回调 formInstanceId:{}", formInstanceId);
        if (null == formInstanceId) {
            logger.error("标准条款新增审批提交回调 formInstanceId为空，不处理");
            return;
        }

        StandardTerms standardTerms = standardTermsMapper.selectByPrimaryKey(formInstanceId);
        if (null == standardTerms) {
            logger.error("标准条款新增审批提交回调 formInstanceId:{}对应的标准条款不存在，不处理", formInstanceId);
            return;
        }

        //修改状态为审批中
        standardTerms.setTermsStatus(StandardTermsEnum.PENDING.getCode());
        standardTermsMapper.updateByPrimaryKey(standardTerms);
    }

    /**
     * 驳回
     */
    @Override
    public void refused(Long formInstanceId) {
        logger.info("标准条款新增审批驳回回调 formInstanceId:{}", formInstanceId);
        if (null == formInstanceId) {
            logger.error("标准条款新增审批提交回调 formInstanceId为空，不处理");
            return;
        }

        StandardTerms standardTerms = standardTermsMapper.selectByPrimaryKey(formInstanceId);
        if (null == standardTerms) {
            logger.error("标准条款新增审批提交回调 formInstanceId:{}对应的标准条款不存在，不处理", formInstanceId);
            return;
        }

        //修改状态为审批中
        if (standardTerms.getTermsStatus().equals(StandardTermsEnum.PENDING.getCode())) {
            standardTerms.setTermsStatus(StandardTermsEnum.REFUSE.getCode());
        }
        standardTermsMapper.updateByPrimaryKey(standardTerms);
    }

    /**
     * 撤回
     */
    @Override
    public void draftReturn(Long formInstanceId) {
        logger.info("标准条款新增审批撤回回调 formInstanceId:{}", formInstanceId);
        if (null == formInstanceId) {
            logger.error("标准条款新增审批提交回调 formInstanceId为空，不处理");
            return;
        }

        StandardTerms standardTerms = standardTermsMapper.selectByPrimaryKey(formInstanceId);
        if (null == standardTerms) {
            logger.error("标准条款新增审批提交回调 formInstanceId:{}对应的标准条款不存在，不处理", formInstanceId);
            return;
        }

        //修改状态为审批中
        if (standardTerms.getTermsStatus().equals(StandardTermsEnum.PENDING.getCode())) {
            standardTerms.setTermsStatus(StandardTermsEnum.WAIT_ENABLE.getCode());
        }
        standardTermsMapper.updateByPrimaryKey(standardTerms);
    }

    /**
     * 通过
     */
    @Override
    public void approved(Long formInstanceId) {
        logger.info("标准条款新增审批通过回调 formInstanceId:{}", formInstanceId);
        if (null == formInstanceId) {
            logger.error("标准条款新增审批提交回调 formInstanceId为空，不处理");
            return;
        }

        StandardTerms standardTerms = standardTermsMapper.selectByPrimaryKey(formInstanceId);
        if (null == standardTerms) {
            logger.error("标准条款新增审批提交回调 formInstanceId:{}对应的标准条款不存在，不处理", formInstanceId);
            return;
        }

        //修改状态为审批中
        if (standardTerms.getTermsStatus().equals(StandardTermsEnum.PENDING.getCode())) {
            standardTerms.setTermsStatus(StandardTermsEnum.APPROVED.getCode());
        }
        standardTermsMapper.updateByPrimaryKey(standardTerms);
    }

    /**
     * 作废
     */
    @Override
    public void abandon(Long formInstanceId) {
        logger.info("标准条款新增审批作废回调 formInstanceId:{}", formInstanceId);
        if (null == formInstanceId) {
            logger.error("标准条款新增审批提交回调 formInstanceId为空，不处理");
            return;
        }

        StandardTerms standardTerms = standardTermsMapper.selectByPrimaryKey(formInstanceId);
        if (null == standardTerms) {
            logger.error("标准条款新增审批提交回调 formInstanceId:{}对应的标准条款不存在，不处理", formInstanceId);
            return;
        }

        //修改状态为审批中
        if (standardTerms.getTermsStatus().equals(StandardTermsEnum.PENDING.getCode())) {
            standardTerms.setTermsStatus(StandardTermsEnum.WAIT_ENABLE.getCode());
        }
        standardTermsMapper.updateByPrimaryKey(standardTerms);
    }

    /**
     * 删除
     */
    @Override
    public void delete(Long formInstanceId) {
        logger.info("标准条款新增审批删除回调 formInstanceId:{}", formInstanceId);
        if (null == formInstanceId) {
            logger.error("标准条款新增审批提交回调 formInstanceId为空，不处理");
            return;
        }

        StandardTerms standardTerms = standardTermsMapper.selectByPrimaryKey(formInstanceId);
        if (null == standardTerms) {
            logger.error("标准条款新增审批提交回调 formInstanceId:{}对应的标准条款不存在，不处理", formInstanceId);
            return;
        }

        //修改状态为审批中
        if (standardTerms.getTermsStatus().equals(StandardTermsEnum.PENDING.getCode())) {
            standardTerms.setTermsStatus(StandardTermsEnum.WAIT_ENABLE.getCode());
        }
        standardTermsMapper.updateByPrimaryKey(standardTerms);
    }


}
