package com.midea.pam.ctc.wbs.service.impl;

import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.enums.ProjectChangeStatus;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.RequirementStatusEnum;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.ProjectHistoryHeaderMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.event.ProjectWbsBudgetChangeWorkflowCallbackApprovalEvent;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetChangeWorkflowCallbackService;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-04-29
 * @description 项目wbs预算变更回调
 */
public class ProjectWbsBudgetChangeWorkflowCallbackServiceImpl implements ProjectWbsBudgetChangeWorkflowCallbackService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private ProjectHistoryHeaderMapper projectHistoryHeaderMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;
    @Resource
    private WorkflowCallbackService workflowCallbackService;


    @Override
    public void pass(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                     Long createUserId, Long handlerUserId) {
        applicationEventPublisher.publishEvent(new ProjectWbsBudgetChangeWorkflowCallbackApprovalEvent(this, formInstanceId, fdInstanceId, formUrl,
                eventName, handlerId, companyId, createUserId, handlerUserId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftSubmit(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                            Long createUserId) {
        logger.info("项目WBS预算变更审批提交审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectWbsBudgetChangeWorkflowCallback_draftSubmit_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(projectHistoryHeader, "项目WBS预算变更审批提交审批回调 formInstanceId对应单据不存在，不处理");

                    projectHistoryHeader.setStatus(ProjectChangeStatus.APPROVALING.getCode());
                    projectHistoryHeaderMapper.updateByPrimaryKeySelective(projectHistoryHeader);

                    // 更新项目状态为变更中
                    Project project = new Project();
                    project.setId(projectHistoryHeader.getProjectId());
                    project.setStatus(ProjectStatus.CHANGING.getCode());
                    projectService.updateByPrimaryKeySelective(project);

                    logger.info("项目WBS预算变更审批提交审批回调，状态更新为待审批");

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目WBS预算变更审批提交审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目WBS预算变更审批提交审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目WBS预算变更审批提交审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refuse(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                       Long createUserId) {
        logger.info("项目WBS预算变更审批驳回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectWbsBudgetChangeWorkflowCallback_refuse_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(projectHistoryHeader, "项目WBS预算变更审批驳回回调 formInstanceId对应单据不存在，不处理");

                    projectHistoryHeader.setStatus(ProjectChangeStatus.REFUSE.getCode());
                    projectHistoryHeaderMapper.updateByPrimaryKeySelective(projectHistoryHeader);

                    // 更新项目状态为进行中
                    Project project = new Project();
                    project.setId(projectHistoryHeader.getProjectId());
                    project.setStatus(ProjectStatus.APPROVALED.getCode());
                    projectService.updateByPrimaryKeySelective(project);

                    logger.info("项目WBS预算变更驳回回调，状态更新为驳回，headerId={}", handlerId);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目WBS预算变更审批驳回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目WBS预算变更审批驳回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目WBS预算变更审批驳回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Override
    public void abandon(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                        Long createUserId) {
        logger.info("项目WBS预算变更审批作废回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectWbsBudgetChangeWorkflowCallback_abandonCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(projectHistoryHeader, "项目WBS预算变更审批作废回调 formInstanceId对应单据不存在，不处理");

                    projectHistoryHeader.setStatus(ProjectChangeStatus.ABANDON.getCode());
                    projectHistoryHeaderMapper.updateByPrimaryKeySelective(projectHistoryHeader);

                    //更新WBS变更单状态为：作废
                    projectWbsReceiptsService.updateRequirementStatus(projectHistoryHeader.getProjectWbsReceiptsId(), RequirementStatusEnum.CANCEL.getCode());

                    logger.info("项目WBS预算变更作废回调，状态更新为作废");

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目WBS预算变更审批作废回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目WBS预算变更审批作废回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目WBS预算变更审批作废回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftReturn(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                            Long createUserId) {
        logger.info("项目WBS预算变更审批撤回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectWbsBudgetChangeWorkflowCallback_draftReturn_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(projectHistoryHeader, "项目WBS预算变更审批撤回回调 formInstanceId对应单据不存在，不处理");

                    projectHistoryHeader.setStatus(ProjectChangeStatus.RETURN.getCode());
                    projectHistoryHeaderMapper.updateByPrimaryKey(projectHistoryHeader);

                    // 更新项目状态为进行中
                    Project project = new Project();
                    project.setId(projectHistoryHeader.getProjectId());
                    project.setStatus(ProjectStatus.APPROVALED.getCode());
                    projectService.updateByPrimaryKeySelective(project);

                    logger.info("项目WBS预算变更撤回回调，状态更新为草稿");

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目WBS预算变更审批撤回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目WBS预算变更审批撤回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目WBS预算变更审批撤回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Override
    public void delete(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                       Long createUserId) {
        logger.info("项目WBS预算变更审批删除回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectWbsBudgetChangeWorkflowCallback_deleteCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(projectHistoryHeader, "项目WBS预算变更审批删除回调 formInstanceId对应单据不存在，不处理");

                    projectHistoryHeader.setDeletedFlag(true);
                    projectHistoryHeaderMapper.updateByPrimaryKeySelective(projectHistoryHeader);

                    //更新WBS变更单状态为：作废
                    projectWbsReceiptsService.updateRequirementStatus(projectHistoryHeader.getProjectWbsReceiptsId(), RequirementStatusEnum.CANCEL.getCode());

                    logger.info("项目WBS预算变更删除回调");

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("项目WBS预算变更审批删除回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目WBS预算变更审批删除回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目WBS预算变更审批删除回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }
}
