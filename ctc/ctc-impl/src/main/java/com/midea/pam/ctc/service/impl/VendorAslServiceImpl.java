package com.midea.pam.ctc.service.impl;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.entity.VendorAslCtc;
import com.midea.pam.common.ctc.entity.VendorAslCtcExample;
import com.midea.pam.common.ctc.query.VendorAslQuery;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementExtMapper;
import com.midea.pam.ctc.mapper.VendorAslCtcExtMapper;
import com.midea.pam.ctc.mapper.VendorAslCtcMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.OrganizationRelExtService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.ctc.service.VendorAslService;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class VendorAslServiceImpl implements VendorAslService {

    private final static Logger logger = LoggerFactory.getLogger(VendorAslService.class);
    private final static Long MAXWAITTIME = (long) 1000 * 60 * 5;//5分钟

    @Resource
    private VendorAslCtcMapper vendorAslCtcMapper;
    @Resource
    private VendorAslCtcExtMapper vendorAslCtcExtMapper;
    @Resource
    EsbService esbService;
    @Resource
    OrganizationRelExtService organizationRelExtService;
    @Resource
    BasedataExtService basedataExtService;
    @Resource
    private PurchaseMaterialRequirementExtMapper purchaseMaterialRequirementExtMapper;
    @Resource
    private SdpCarrierServicel sdpCarrierServicel;


    @Override
    public PageInfo<VendorAslCtc> list(VendorAslQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<VendorAslCtc> vendorAslCtcList = this.selectList(query);
        return BeanConverter.convertPage(vendorAslCtcList, VendorAslCtc.class);
    }

    public List<VendorAslCtc> selectList(VendorAslQuery query) {
        // 获取当前使用单位有效的库存组织
        List<OrganizationRelDto> organizationRels = basedataExtService.selectCurrentOrganization();
        if (ListUtils.isEmpty(organizationRels)) {
            return Lists.emptyList();
        }
        VendorAslCtcExample example = new VendorAslCtcExample();
        VendorAslCtcExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andOrganizationCodeIn(organizationRels.stream().map(OrganizationRel::getOrganizationCode)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
        if (!StringUtils.isEmpty(query.getOrganizationName())) {
            criteria.andOrganizationNameLike("%" + query.getOrganizationName() + "%");
        }
        if (!StringUtils.isEmpty(query.getErpDisableFlag())) {
            List<String> erpDisableFlags = Arrays.asList(query.getErpDisableFlag().split(","));
            criteria.andErpDisableFlagIn(erpDisableFlags);
        }
        if (!StringUtils.isEmpty(query.getAslStatus())) {
            List<String> aslStatus = Arrays.asList(query.getAslStatus().split(","));
            criteria.andAslStatusIn(aslStatus);
        }
        if (!StringUtils.isEmpty(query.getMaterialCode())) {
            criteria.andMaterialCodeLike("%" + query.getMaterialCode() + "%");
        }
        if (!StringUtils.isEmpty(query.getVendorCode())) {
            criteria.andVendorCodeLike("%" + query.getVendorCode() + "%");
        }
        if (!StringUtils.isEmpty(query.getVendorName())) {
            criteria.andVendorNameLike("%" + query.getVendorName() + "%");
        }
        if (!StringUtils.isEmpty(query.getPrimaryVendorItem())) {
            criteria.andPrimaryVendorItemLike("%" + query.getPrimaryVendorItem() + "%");
        }
        if (!StringUtils.isEmpty(query.getItemInfo())) {
            criteria.andItemInfoLike("%" + query.getItemInfo() + "%");
        }
        return this.vendorAslCtcMapper.selectByExample(example);
    }

    /**
     * 唯一条件查重：
     * operatingCode + vendorCode + vendorSiteCode+ materialCode
     *
     * @param vendorAsl
     * @return
     */
    public VendorAslCtc findByUniqueKey(VendorAslCtc vendorAsl) {
        VendorAslCtcExample example = new VendorAslCtcExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andOrganizationCodeEqualTo(vendorAsl.getOrganizationCode())
                .andVendorCodeEqualTo(vendorAsl.getVendorCode())
                .andVendorSiteCodeEqualTo(vendorAsl.getVendorSiteCode())
                .andMaterialCodeEqualTo(vendorAsl.getMaterialCode())
                .andGlobalFlagEqualTo(vendorAsl.getGlobalFlag());
        List<VendorAslCtc> entityList = this.vendorAslCtcMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(entityList)) {
            return null;
        }
        return entityList.get(0);
    }

    /**
     * 根据指定时间或前一天0点拉取数据
     *
     * @param lastUpdateDate
     * @param materialCode
     */
    @Override
    public void getVendorAslFromErp(String lastUpdateDate, String lastUpdateDateEnd, String materialCode) {
        List<String> itemList = new ArrayList<>();
        Map<String, String> paramMap = new HashMap();
        Date yesterday = DateUtils.subtractDay(new Date(), 1);
        if (ObjectUtils.isEmpty(lastUpdateDate)) {
            lastUpdateDate = DateUtils.format(yesterday, "yyyy-MM-dd 00:00:00");
        }
        if (ObjectUtils.isEmpty(lastUpdateDateEnd)) {
            lastUpdateDateEnd = DateUtils.format(new Date(), "yyyy-MM-dd 23:59:59");
        }
        //取有效的库存组织ID作为P01参数
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setPamEnabled(0);
        List<OrganizationRelDto> orgRelList = organizationRelExtService.invokeApiList(query).getList();
        Set<Long> organizationIdSet = new HashSet<Long>();
        //去重
        for (OrganizationRelDto orgRel : orgRelList) {
            organizationIdSet.add(orgRel.getOrganizationId());
        }
        for (Long organizationId : organizationIdSet) {
            paramMap.clear();
//            paramMap.put(EsbConstant.ERP_IP_P01, lastUpdateDate);//最后更新時間
            paramMap.put(EsbConstant.ERP_SDP_P01, lastUpdateDate);//最后更新時間
            if (!com.alibaba.dubbo.common.utils.StringUtils.isEmpty(lastUpdateDateEnd)) {
//                paramMap.put(EsbConstant.ERP_IP_P02, lastUpdateDateEnd);//最后更新时间结束
                paramMap.put(EsbConstant.ERP_SDP_P02, lastUpdateDateEnd);//最后更新时间结束
            }
//            paramMap.put(EsbConstant.ERP_IP_P03, String.valueOf(organizationId));//库存组织id
            paramMap.put(EsbConstant.ERP_SDP_P03, String.valueOf(organizationId));//库存组织id
            if (!StringUtils.isEmpty(materialCode)) {
//                paramMap.put(EsbConstant.ERP_IP_P04, materialCode);//物料code
                paramMap.put(EsbConstant.ERP_SDP_P04, materialCode);//物料code
            }
            this.getVendorAslFromErp(paramMap, itemList);
        }
    }

    /**
     * erp批准供应商查询 ERP-PAM-009
     */
    @Override
    public void getVendorAslFromErp(Map<String, String> paramMap, List<String> itemList) {
        //调erp接口
//        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_009, paramMap);
        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_009, paramMap);
        for (SdpTradeResultResponseEleDto item : returnItemList) {
            VendorAslCtc vendorAsl = new VendorAslCtc();
            vendorAsl.setType(item.getC1());
            vendorAsl.setCommodity(item.getC2());
            vendorAsl.setOrganizationCode(item.getC3());
            vendorAsl.setOrganizationName(item.getC4());
            vendorAsl.setVendorBusinessType(item.getC5());
            vendorAsl.setAslStatus(item.getC6());

            if (StringUtils.isEmpty(item.getC7())) {
                vendorAsl.setErpDisableFlag("N");
            } else {
                vendorAsl.setErpDisableFlag(item.getC7());
            }

            vendorAsl.setVendorCode(item.getC8());
            vendorAsl.setVendorName(item.getC9());
            vendorAsl.setVendorSiteCode(item.getC10());
            vendorAsl.setMaterialCode(item.getC11());
            vendorAsl.setPrimaryVendorItem(item.getC12());

            if (StringUtils.isEmpty(item.getC13())) {
                vendorAsl.setGlobalFlag("N");
            } else {
                vendorAsl.setGlobalFlag(item.getC13());
            }

            vendorAsl.setRemark(item.getC14());
            vendorAsl.setBrand(item.getC15());
            vendorAsl.setItemInfo(item.getC17());

            String c18 = item.getC18();
            if (StringUtils.isNotEmpty(c18)) {
                Date d = DateUtils.parse(c18, DateUtils.FORMAT_LONG);
                vendorAsl.setVendorCreateTime(d);
            }

            String c16 = item.getC16();
            if (StringUtils.isNotEmpty(c16)) {
                Date d = DateUtils.parse(c16, DateUtils.FORMAT_LONG);
                vendorAsl.setVendorUpdateTime(d);
            }

            //create Or update
            VendorAslCtc existEntity = this.findByUniqueKey(vendorAsl);
            if (ObjectUtils.isEmpty(existEntity)) {
                vendorAsl.setDeletedFlag(false);
                vendorAslCtcMapper.insert(vendorAsl);
                if (!itemList.contains(vendorAsl.getMaterialCode())) {
                    itemList.add(vendorAsl.getMaterialCode());
                }
            } else {
                vendorAsl.setId(existEntity.getId());
                vendorAslCtcMapper.updateByPrimaryKeySelective(vendorAsl);
            }
            //ex_xiedl2 --> 数据同步到basedata模块下的批准供应商表
            basedataExtService.syncVendorAsl(vendorAsl);
        }
    }

    @Override
    public List<VendorAslCtc> selectListByMaterialCode(String materialCode, String storageCode) {
        Asserts.notEmpty(materialCode, ErrorCode.BASEDATA_MATERIAL_CODE_NOT_NULL);
        List<String> aslStatus = new ArrayList<>();
        aslStatus.add("Approved");
        aslStatus.add("ONETIME");

        VendorAslCtcExample example = new VendorAslCtcExample();
        VendorAslCtcExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andCondition("(erp_disable_flag <> 'Y')");
        criteria.andAslStatusIn(aslStatus);
        criteria.andMaterialCodeEqualTo(materialCode);
        if (StringUtils.isNotEmpty(storageCode)) {
            criteria.andOrganizationCodeEqualTo(storageCode);
        }
        List<VendorAslCtc> list = vendorAslCtcMapper.selectByExample(example);
        return list;
    }

    @Override
    public VendorAslCtc getById(Long id) {
        if (id == null) {
            return null;
        }
        return vendorAslCtcMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<String> findAllVendorAslCtcs() {
        List<String> aslStatus = new ArrayList<>();
        aslStatus.add("Approved");
        aslStatus.add("ONETIME");

        VendorAslCtcExample example = new VendorAslCtcExample();
        VendorAslCtcExample.Criteria criteria = example.createCriteria();
        criteria.andCondition("(erp_disable_flag <> 'Y')");
        criteria.andAslStatusIn(aslStatus);
        List<String> list = vendorAslCtcExtMapper.findAllVendorAslCtcs();
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : list;
    }

    @Override
    public List<String> getAslStatus() {
        List<String> result = new ArrayList<>();
        VendorAslCtcExample vendorAslCtcExample = new VendorAslCtcExample();
        VendorAslCtcExample.Criteria criteria = vendorAslCtcExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorAslCtc> vendorAslCtcs = vendorAslCtcMapper.selectByExample(vendorAslCtcExample);
        if (ListUtils.isNotEmpty(vendorAslCtcs)) {
            result = vendorAslCtcs.stream().map(VendorAslCtc::getAslStatus).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public Map<String, Object> export(VendorAslQuery query) {
        Map<String, Object> resultMap = new HashMap<>();
        List<VendorAslCtc> vendorAslCtcList = this.selectList(query);
        resultMap.put("vendorAslCtcList", vendorAslCtcList);
        return resultMap;
    }

}
