package com.midea.pam.ctc.contract.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.dto.TaxInfoDto;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDto;
import com.midea.pam.common.ctc.dto.PaymentPenaltyProfitDto;
import com.midea.pam.common.ctc.dto.PaymentPlanDTO;
import com.midea.pam.common.ctc.dto.PaymentRecordDto;
import com.midea.pam.common.ctc.dto.PaymentWriteOffRecordDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentApplyDetailRel;
import com.midea.pam.common.ctc.entity.PaymentApplyDetailRelExample;
import com.midea.pam.common.ctc.entity.PaymentApplyExample;
import com.midea.pam.common.ctc.entity.PaymentApplyExternalAttachment;
import com.midea.pam.common.ctc.entity.PaymentApplyInvoiceRel;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetail;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetailExample;
import com.midea.pam.common.ctc.entity.PaymentPenaltyProfit;
import com.midea.pam.common.ctc.entity.PaymentPenaltyProfitExample;
import com.midea.pam.common.ctc.entity.PaymentPlan;
import com.midea.pam.common.ctc.entity.PaymentRecord;
import com.midea.pam.common.ctc.entity.PaymentRecordExample;
import com.midea.pam.common.ctc.entity.PaymentWriteOffRecord;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectContractRs;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractPunishment;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.query.PaymentApplyQuery;
import com.midea.pam.common.ctc.query.PaymentRecordQuery;
import com.midea.pam.common.ctc.vo.PurchaseContractProgressVo;
import com.midea.pam.common.enums.AttachmentStatus;
import com.midea.pam.common.enums.AuditStatus;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.EmailStatus;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.GSCPaymentAndInvoiceStatusPushEnum;
import com.midea.pam.common.enums.GcebInterfaceSynStatus;
import com.midea.pam.common.enums.GlPeriodType;
import com.midea.pam.common.enums.InvoiceDetailStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.NoticeBusinessType;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PaymentApplyBizStatus;
import com.midea.pam.common.enums.PaymentApplyEsbStatus;
import com.midea.pam.common.enums.PaymentApplyGcebErpStatus;
import com.midea.pam.common.enums.PaymentApplySourceNameEnum;
import com.midea.pam.common.enums.PaymentInvoiceAccountEntryTypeEnum;
import com.midea.pam.common.enums.PaymentInvoiceErpStatusEnum;
import com.midea.pam.common.enums.PaymentInvoiceSourceEnum;
import com.midea.pam.common.enums.PaymentInvoiceStatusEnum;
import com.midea.pam.common.enums.PaymentPlanStatus;
import com.midea.pam.common.enums.PurchaseContractPunishmentAccountStatusEnum;
import com.midea.pam.common.enums.PurchaseContractPunishmentApproveEnums;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.gateway.entity.TempMsgPush;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PamStringUtil;
import com.midea.pam.common.util.UrlUtils;
import com.midea.pam.ctc.api.server.PassGcebToPamService;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.enums.PaymentApplyWriteOffStatus;
import com.midea.pam.ctc.common.enums.PaymentRecordStatus;
import com.midea.pam.ctc.common.enums.PaymentWriteOffRecordStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.contract.service.PaymentPlanService;
import com.midea.pam.ctc.contract.service.PurchaseContractPunishmentService;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.contract.service.helper.ContractHelper;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.ContractMapper;
import com.midea.pam.ctc.mapper.MaterialOutsourcingContractConfigMapper;
import com.midea.pam.ctc.mapper.PaymentApplyDetailRelMapper;
import com.midea.pam.ctc.mapper.PaymentApplyExtMapper;
import com.midea.pam.ctc.mapper.PaymentApplyExternalAttachmentExtMapper;
import com.midea.pam.ctc.mapper.PaymentApplyInvoiceRelMapper;
import com.midea.pam.ctc.mapper.PaymentApplyMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailExtMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceExtMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.PaymentPenaltyProfitMapper;
import com.midea.pam.ctc.mapper.PaymentPlanExtMapper;
import com.midea.pam.ctc.mapper.PaymentPlanMapper;
import com.midea.pam.ctc.mapper.PaymentRecordMapper;
import com.midea.pam.ctc.mapper.PaymentWriteOffRecordMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPunishmentExtMapper;
import com.midea.pam.ctc.service.AdapterService;
import com.midea.pam.ctc.service.AgencySynService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.GSCPaymentAndInvoiceStatusPushService;
import com.midea.pam.ctc.service.MeiXinPushMsgService;
import com.midea.pam.ctc.service.NoticeService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.PaymentApplyDetailRelService;
import com.midea.pam.ctc.service.PaymentApplyInvoiceRelService;
import com.midea.pam.ctc.service.PaymentInvoiceDetailService;
import com.midea.pam.ctc.service.PaymentInvoiceService;
import com.midea.pam.ctc.service.PaymentPenaltyProfitService;
import com.midea.pam.ctc.service.PaymentRecordService;
import com.midea.pam.ctc.service.PaymentWriteOffRecordService;
import com.midea.pam.ctc.service.ProjectContractRsService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ResendExecuteService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * <AUTHOR>
 * @date 2019-7-30
 * @description 付款申请
 */
public class PaymentApplyServiceImpl implements PaymentApplyService {

    @Value("${route.paymentApplyEmailUrl}")
    private String paymentApplyEmailUrl;

    public PaymentApplyServiceImpl(String symbol) {
        this.symbol = symbol;
    }

    //付款申请单号
    private final String APPLY_CODE = "FK";

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 5;//5分钟

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private String symbol;

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    @Resource
    private PaymentApplyMapper paymentApplyMapper;
    @Resource
    private NoticeService noticeService;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private PaymentApplyExtMapper paymentApplyExtMapper;
    @Resource
    private AgencySynService agencySynService;
    @Resource
    private AdapterService adapterService;
    @Resource
    private PaymentInvoiceService paymentInvoiceService;
    @Resource
    private PaymentPlanService paymentPlanService;
    @Resource
    private PurchaseContractService purchaseContractService;
    @Resource
    private ResendExecuteService resendExecuteService;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private PaymentInvoiceDetailService paymentInvoiceDetailService;
    @Resource
    private PaymentApplyInvoiceRelService paymentApplyInvoiceRelService;
    @Resource
    private PaymentPenaltyProfitService paymentPenaltyProfitService;
    @Resource
    private PaymentApplyDetailRelService paymentApplyDetailRelService;
    @Resource
    private PaymentApplyDetailRelMapper paymentApplyDetailRelMapper;
    @Resource
    private PaymentApplyInvoiceRelMapper paymentApplyInvoiceRelMapper;
    @Resource
    private MeiXinPushMsgService meiXinPushMsgService;
    @Resource
    private PassGcebToPamService passGcebToPamService;
    @Resource
    PaymentInvoiceDetailExtMapper paymentInvoiceDetailExtMapper;
    @Resource
    PaymentPenaltyProfitMapper paymentPenaltyProfitMapper;
    @Resource
    PaymentWriteOffRecordMapper paymentWriteOffRecordMapper;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private EsbService esbService;
    @Resource
    private PaymentRecordMapper paymentRecordMapper;
    @Resource
    private PaymentRecordService paymentRecordService;
    @Resource
    private ProjectContractRsService projectContractRsService;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private ContractHelper contractHelper;
    @Resource
    private ProjectService projectService;
    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;
    @Resource
    private PaymentWriteOffRecordService writeOffRecordService;
    @Resource
    private PaymentApplyService paymentApplyService;
    @Resource
    private PaymentInvoiceExtMapper paymentInvoiceExtMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private PaymentPlanMapper paymentPlanMapper;
    @Resource
    private PaymentPlanExtMapper paymentPlanExtMapper;
    @Resource
    private PaymentInvoiceDetailMapper paymentInvoiceDetailMapper;
    @Resource
    private PurchaseContractPunishmentService punishmentService;
    @Resource
    private MaterialOutsourcingContractConfigMapper materialOutsourcingContractConfigMapper;
    @Resource
    private PurchaseContractPunishmentExtMapper punishmentExtMapper;
    @Resource
    private GSCPaymentAndInvoiceStatusPushService gscPaymentAndInvoiceStatusPushService;
    @Resource
    private PaymentApplyExternalAttachmentExtMapper paymentApplyExternalAttachmentExtMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private SdpCarrierServicel sdpCarrierServicel;


    public void auditPass(PaymentApply paymentApply) {
        if (paymentApply != null) {
            //是否预付款(1:是 0否)
            Integer isCharge = paymentApply.getIsCharge();
            String esbStatus = "0";//待同步
            if (isCharge == 1) {
                String businessTypeCode = BusinessTypeEnums.PAYMENT_APPLY.getCode();
                List<DictDto> dictList = basedataExtService.findDictByType();
                logger.info("获取的第三方票据写入数据为:{}", JSON.toJSONString(dictList));
                Map<Long, DictDto> dictMap = dictList.stream().collect(Collectors.toMap(DictDto::getId, Function.identity()));
                //第三方票据写入
                if (dictMap.containsKey(paymentApply.getPaymentMethodId())) {
                    businessTypeCode = BusinessTypeEnums.PAYMENT_INVOICE_THIRD.getCode();
                }
//                Dict dict = basedataExtService.findDictById(paymentApply.getPaymentMethodId());
//                com.midea.mcomponent.core.util.Assert.notNull(dict, String.format("字典【%s】不能为空", paymentApply.getPaymentMethodName()));
//                // 第三方票据-迪链
//                if ("DL".equals(dict.getCode())) {
//                    businessTypeCode = BusinessTypeEnums.PAYMENT_INVOICE_DL.getCode();
//                }
                //审批通过推送资金
                HandleDispatcher.route(businessTypeCode, String.valueOf(paymentApply.getId()), null, null, false);
            } else {
                if (1 == paymentApply.getInvoiceMethod()) {
                    //1新增发票，应付发票写入之后才付款计划写入
                    //打包发票行生成头
                    PaymentInvoice invoice = new PaymentInvoice();
                    BigDecimal totalInvoiceIncludedPrice = BigDecimal.valueOf(0.00);// 发票金额含税
                    BigDecimal amount = BigDecimal.valueOf(0.00);//累计罚扣（含税）
                    BigDecimal taxAmount = BigDecimal.valueOf(0.00);//税额
                    Date dueDate = null;
                    //发票行
                    List<PaymentInvoiceDetailDto> invoiceDetails = paymentInvoiceDetailExtMapper.selectListByApplyId(paymentApply.getId());
                    for (PaymentInvoiceDetailDto detailDto : invoiceDetails) {
                        totalInvoiceIncludedPrice = totalInvoiceIncludedPrice.add(detailDto.getTaxIncludedPrice());
                        if (null != detailDto.getTaxAmount()) {
                            taxAmount = taxAmount.add(detailDto.getTaxAmount());
                        }
                        if (null != detailDto.getDueDate()) {
                            dueDate = Objects.isNull(dueDate) ? detailDto.getDueDate() : (dueDate.compareTo(detailDto.getDueDate()) > 0 ?
                                    detailDto.getDueDate() : dueDate);
                        }
                    }
                    //罚扣/返利
                    PaymentPenaltyProfitExample profitExample = new PaymentPenaltyProfitExample();
                    profitExample.createCriteria().andPaymentApplyIdEqualTo(paymentApply.getId())
                            .andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
                    List<PaymentPenaltyProfit> penaltyProfits = paymentPenaltyProfitMapper.selectByExample(profitExample);
                    for (PaymentPenaltyProfit profit : penaltyProfits) {
                        amount = amount.add(profit.getAmount());
                        if (null != profit.getTaxAmount()) {
                            taxAmount = taxAmount.subtract(profit.getTaxAmount());
                        }
                        totalInvoiceIncludedPrice = totalInvoiceIncludedPrice.subtract(profit.getAmount());
                        //罚扣关联发票
                        if (null != profit.getPaymentInvoiceDetailId()) {
                            PaymentInvoiceDetailDto detailDto = paymentInvoiceDetailService.getDetailById(profit.getPaymentInvoiceDetailId(), null);
                            invoiceDetails.add(detailDto);//罚扣发票一起打包成ap发票
                        }
                    }
                    invoice.setPaymentApplyId(paymentApply.getId());
                    invoice.setInvoiceDate(paymentApply.getDateEntry());
                    invoice.setPaymentApplyCode(paymentApply.getPaymentApplyCode());
                    invoice.setPaymentPlanCode(paymentApply.getPaymentPlanCode());
                    invoice.setPaymentPlanId(paymentApply.getPaymentPlanId());
                    invoice.setPurchaseContractId(paymentApply.getPurchaseContractId());
                    invoice.setPurchaseContractCode(paymentApply.getPurchaseContractCode());
                    invoice.setOuId(paymentApply.getOuId());
                    invoice.setApInvoiceCode("PAM-" + paymentApply.getPaymentApplyCode());//ap发票编号
                    invoice.setTotalInvoiceIncludedPrice(totalInvoiceIncludedPrice);
                    invoice.setAmount(amount);
                    invoice.setTotalAmount(totalInvoiceIncludedPrice.add(amount));
                    //剩余可用金额=打包发票总金额-本次付款申请金额
                    invoice.setSurplusAmount(totalInvoiceIncludedPrice.subtract(paymentApply.getTaxPayIncludedPrice()));
                    invoice.setTaxAmount(taxAmount);
                    invoice.setErpStatus(PaymentInvoiceErpStatusEnum.CHECKING.code());//ERP同步状态(0-验证中1-已验证2-已取消) 默认验证中
                    invoice.setCollectionStatus(0); // 默认未归集
                    invoice.setAvailable(0);//是否可用(1-未占用 0-已占用)
                    invoice.setSource(PaymentInvoiceSourceEnum.PAYMENT.getCode());//來源付款
                    invoice.setAuditDate(new Date());
                    invoice.setStatus(PaymentInvoiceStatusEnum.PASS.getCode());
                    invoice.setDueDate(dueDate);//发票到期日
                    invoice.setGlDate(paymentApply.getGlDate());
                    invoice.setAccountEntryType(PaymentInvoiceAccountEntryTypeEnum.REGULAR_INVOICE.getCode());
                    PaymentInvoiceDto invoiceDto = BeanConverter.copy(invoice, PaymentInvoiceDto.class);
                    //写入税码
                    if (ListUtils.isNotEmpty(invoiceDetails)) {
                        PaymentInvoiceDetailDto paymentInvoiceDetailDto = invoiceDetails.get(0);
                        if (Objects.nonNull(paymentInvoiceDetailDto.getTaxRate())) {
                            BigDecimal taxRate = paymentInvoiceDetailDto.getTaxRate().stripTrailingZeros().stripTrailingZeros();
                            String taxCode = taxCodeQuery(taxRate.toPlainString());
                            invoiceDto.setTaxCode(taxCode);
                        }
                    }
                    Long invoiceId = paymentInvoiceService.save(invoiceDto);
                    //保存发票行(包括罚扣发票)的发票信息头ID
                    for (PaymentInvoiceDetailDto detailDto : invoiceDetails) {
                        detailDto.setPaymentInvoiceId(invoiceId);
                        detailDto.setInvoiceStatus(InvoiceDetailStatus.QUOTE.getCode());//变为已关联
                        punishmentService.updatePunishmentAccountStatus(detailDto, PurchaseContractPunishmentAccountStatusEnum.ACCOUNTED.getCode());
                    }
                    paymentInvoiceDetailService.batchSave(invoiceDetails, SystemContext.getUserId());
                    //保存发票头和付款申请的关联
                    PaymentApplyInvoiceRel paymentApplyInvoiceRel = new PaymentApplyInvoiceRel();
                    paymentApplyInvoiceRel = new PaymentApplyInvoiceRel();
                    paymentApplyInvoiceRel.setPaymentApplyId(paymentApply.getId());
                    paymentApplyInvoiceRel.setPaymentInvoiceId(invoiceId);
                    paymentApplyInvoiceRel.setAmount(paymentApply.getTaxPayIncludedPrice());
                    paymentApplyInvoiceRel.setDeletedFlag(0);
                    paymentApplyInvoiceRelService.save(paymentApplyInvoiceRel, SystemContext.getUserId());
                    //应付发票插入erp推送队列
                    HandleDispatcher.route(BusinessTypeEnums.PAYMENT_INVOICE.getCode(), String.valueOf(invoiceId), null, null, true, null,
                            invoiceDto.getOuId());
                } else {
                    //2选用已入账发票,付款计划直接插入erp推送队列
                    HandleDispatcher.route(BusinessTypeEnums.PAYMENT_PLAN.getCode(), String.valueOf(paymentApply.getId()), null, null, true);
                    esbStatus = "2";//更新付款申请同步状态
                }
            }
            paymentApply.setEsbStatus(esbStatus);
            paymentApply.setAuditDate(new Date());
            update(paymentApply);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelApply(PaymentApply paymentApply) {
        if (1 == paymentApply.getInvoiceMethod()) {
            //1新增发票,发票行改为未引用
            //发票行
            List<PaymentInvoiceDetailDto> invoiceDetails = paymentInvoiceDetailExtMapper.selectListByApplyId(paymentApply.getId());
            for (PaymentInvoiceDetailDto detailDto : invoiceDetails) {
                detailDto.setInvoiceStatus(InvoiceDetailStatus.UNQUOTED.getCode());
                punishmentService.updatePunishmentAccountStatus(detailDto, PurchaseContractPunishmentAccountStatusEnum.NOT_ACCOUNTED.getCode());
            }
            //罚扣/返利发票行改为未引用
            PaymentPenaltyProfitExample profitExample = new PaymentPenaltyProfitExample();
            profitExample.createCriteria().andPaymentApplyIdEqualTo(paymentApply.getId()).andDeletedFlagEqualTo(0);
            List<PaymentPenaltyProfit> penaltyProfits = paymentPenaltyProfitMapper.selectByExample(profitExample);
            for (PaymentPenaltyProfit profit : penaltyProfits) {
                //罚扣关联发票
                if (null != profit.getPaymentInvoiceDetailId()) {
                    PaymentInvoiceDetailDto detailDto = paymentInvoiceDetailService.getDetailById(profit.getPaymentInvoiceDetailId(), null);
                    detailDto.setInvoiceStatus(InvoiceDetailStatus.UNQUOTED.getCode());
                    invoiceDetails.add(detailDto);//罚扣发票一起打包成ap发票
                    punishmentService.updatePunishmentAccountStatus(detailDto, PurchaseContractPunishmentAccountStatusEnum.NOT_ACCOUNTED.getCode());
                }
            }
            paymentInvoiceDetailService.batchSave(invoiceDetails, SystemContext.getUserId());
        } else {
            //2已入账发票,增加剩余金额
            PaymentApplyInvoiceRel query = new PaymentApplyInvoiceRel();
            query.setPaymentApplyId(paymentApply.getId());
            List<PaymentApplyInvoiceRel> paymentApplyInvoiceRels = paymentApplyInvoiceRelService.selectList(query);
            for (PaymentApplyInvoiceRel invoiceRel : paymentApplyInvoiceRels) {
                PaymentInvoiceDto invoiceDto = paymentInvoiceService.getById(invoiceRel.getPaymentInvoiceId());
                if (null != invoiceDto) {
                    invoiceDto.setSurplusAmount(invoiceDto.getSurplusAmount().add(invoiceRel.getAmount()));
                    paymentInvoiceService.update(invoiceDto);
                }
            }

        }
    }

    public void releaseApply(PaymentApply paymentApply) {
        if (2 == paymentApply.getInvoiceMethod()) {
            //2已入账发票,增加剩余金额
            PaymentApplyInvoiceRel query = new PaymentApplyInvoiceRel();
            query.setPaymentApplyId(paymentApply.getId());
            List<PaymentApplyInvoiceRel> paymentApplyInvoiceRels = paymentApplyInvoiceRelService.selectList(query);
            for (PaymentApplyInvoiceRel invoiceRel : paymentApplyInvoiceRels) {
                PaymentInvoiceDto invoiceDto = paymentInvoiceService.getById(invoiceRel.getPaymentInvoiceId());
                if (null != invoiceDto) {
                    invoiceDto.setSurplusAmount(invoiceDto.getSurplusAmount().add(invoiceRel.getAmount()));
                    paymentInvoiceService.update(invoiceDto);
                }
                //删除关联关系
                paymentApplyInvoiceRelMapper.deleteByPrimaryKey(invoiceRel.getId());
            }
        }
    }

    public Map<String, String> pushPaymentApply(Long paymentApplyId) {
        Map<String, String> paymentMap = new HashMap<String, String>();
        PaymentApplyDto paymentApplyDto = this.findPaymentApplyById(paymentApplyId);
        if (ObjectUtil.isNull(paymentApplyDto)) {
            throw new BizException(Code.ERROR, "付款申请单不能为空");
        }
        EsbResponse<String> stringEsbResponse = passGcebToPamService.advancePaymentApply(paymentApplyDto);
        if (ResponseCodeEnums.SUCESS.getCode().equals(stringEsbResponse.getResponsecode())) {
            paymentMap.put("responseCode", "success");
            paymentMap.put("responseMessage", stringEsbResponse.getResponsemessage());
            paymentMap.put("status", "IMPORTING");
        } else {
            paymentMap.put("responseCode", "fail");
            paymentMap.put("responseMessage", stringEsbResponse.getResponsemessage());
            paymentMap.put("status", "IMPORT_FAIL");
        }
        return paymentMap;
    }

    @Override
    public PaymentApply handlePaymentApply(Long paymentApplyId) {
        PaymentApplyDto paymentApply = this.findPaymentApplyById(paymentApplyId);
        paymentApply.setErpMsg(null);
        //选用已入账发票或者预付款，直接重发
        String applyNo = Long.toString(paymentApplyId);//单据ID
        //付款申请写入
        String businessType = Objects.equals(paymentApply.getIsCharge(), 1) ? BusinessTypeEnums.PAYMENT_APPLY.getCode() :
                BusinessTypeEnums.PAYMENT_PLAN.getCode();
        if (paymentApply != null) {
            String esbStatus = "2";//同步成功
            //是否预付款(1:是 0否)并且发票付款
            if (0 == paymentApply.getIsCharge() && 1 == paymentApply.getInvoiceMethod()) {
                List<PaymentInvoiceDto> paymentInvoiceDtoList = paymentInvoiceService.getApplyInvoice(paymentApplyId);
                if (ListUtils.isNotEmpty(paymentInvoiceDtoList)) {
                    PaymentInvoiceDto invoiceDto = paymentInvoiceDtoList.get(0);
                    //发票已验证
                    if (Objects.equals(invoiceDto.getErpStatus(), 1)) {
                        //先查询是否已经插入resend表
                        ResendExecute resendExecute = resendExecuteService.getByApplyNoAndBusinessType(Long.toString(paymentApplyId),
                                BusinessTypeEnums.PAYMENT_PLAN.getCode());
                        if (resendExecute == null) {
                            //AP发票状态都可用,付款计划插入erp推送队列
                            HandleDispatcher.route(BusinessTypeEnums.PAYMENT_PLAN.getCode(), String.valueOf(paymentApplyId), null, null, true);
                        }
                    } else if (Objects.equals(invoiceDto.getErpStatus(), 0)) {
                        //验证中，检查发票是否已同步
                        Map<String, String> paramMap = new HashMap();
//                        paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(invoiceDto.getOuId()));
                        paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(invoiceDto.getOuId()));
                        //查询时间取发票写入的时间
//                        paramMap.put(EsbConstant.ERP_IP_P02, DateUtil.format(invoiceDto.getCreateAt()));
                        paramMap.put(EsbConstant.ERP_SDP_P02, DateUtil.format(invoiceDto.getCreateAt()));
//                        paramMap.put(EsbConstant.ERP_IP_P03, invoiceDto.getApInvoiceCode());
                        paramMap.put(EsbConstant.ERP_SDP_P03, invoiceDto.getApInvoiceCode());
                        paymentInvoiceService.getInvoiceInfoFromErp(paramMap, null);
                        invoiceDto = paymentInvoiceService.getById(invoiceDto.getId());//重新查询发票是否已同步
                        if (Objects.equals(invoiceDto.getErpStatus(), 1)) {
                            //先查询是否已经插入resend表
                            ResendExecute resendExecute = resendExecuteService.getByApplyNoAndBusinessType(Long.toString(paymentApplyId),
                                    BusinessTypeEnums.PAYMENT_PLAN.getCode());
                            if (resendExecute == null) {
                                //AP发票状态都可用,付款计划插入erp推送队列
                                HandleDispatcher.route(BusinessTypeEnums.PAYMENT_PLAN.getCode(), String.valueOf(paymentApplyId), null, null, true);
                            }
                        } else if (Objects.equals(invoiceDto.getErpStatus(), 0)) {
                            //查不到发票数据,还未入账
                            throw new BizException(ErrorCode.ERROR, "发票还未入账，不能同步付款申请。");
                        } else {
                            paymentApply.setErpMsg(invoiceDto.getErpMsg());
                            esbStatus = "1";//发票同步异常
                        }
                    } else {
                        //已取消/同步失败
                        invoiceDto.setErpStatus(0);//ERP同步状态(0-验证中1-已验证2-已取消) 默认验证中
                        invoiceDto.setErpMsg("");
                        applyNo = Long.toString(invoiceDto.getId());
                        businessType = BusinessTypeEnums.PAYMENT_INVOICE.getCode();
                        paymentInvoiceService.update(invoiceDto);
                        esbStatus = "0";//待同步
                    }
                }
            }
            paymentApply.setEsbStatus(esbStatus);
            paymentApply.setErpMsg(null);
            paymentApply.setGcebMsg(null);
            update(paymentApply);
            agencySynService.syndataToErp(businessType, Long.valueOf(applyNo));
            adapterService.resend(applyNo, null, businessType);
        }
        return paymentApply;
    }

    @Override
    public PageInfo<PaymentApplyDto> selectPage(PaymentApplyQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());

        //biz_status（1草稿,2审核中,3驳回,4生效（支付中）,5作废，6支付成功，7、取消支付、8、部分支付、9、异常完结）
        String bizStatus = query.getBizStatus();
        //`gceb_Status`'gceb同步状态(1-支付完成,2-取消支付,3-部分支付,4-付款中,5-异常完结)',
        //`erp_Status` 'erp同步状态(1-支付完成,2-取消支付,3-部分支付,4-付款中,5-异常完结)',
        if (!StringUtils.isEmpty(bizStatus)) {
            if (bizStatus.equals(PaymentApplyBizStatus.DRAFT.getCode()) ||
                    bizStatus.equals(PaymentApplyBizStatus.PENDING.getCode()) ||
                    bizStatus.equals(PaymentApplyBizStatus.REFUSE.getCode()) ||
                    bizStatus.equals(PaymentApplyBizStatus.EFFECTIVE.getCode()) ||
                    bizStatus.equals(PaymentApplyBizStatus.CANCEL.getCode())) {
                query.setAuditStatus(Integer.parseInt(bizStatus));
            } else {
                String status = null;
                if (bizStatus.equals(PaymentApplyBizStatus.SUCCESS.getCode())) {
                    status = PaymentApplyGcebErpStatus.SUCCESS.getCode();
                } else if (bizStatus.equals(PaymentApplyBizStatus.CANCEL_PAY.getCode())) {
                    status = PaymentApplyGcebErpStatus.CANCEL.getCode();
                } else if (bizStatus.equals(PaymentApplyBizStatus.PARTIAL_PAYMENT.getCode())) {
                    status = PaymentApplyGcebErpStatus.PARTIAL_PAYMENT.getCode();
                } else if (bizStatus.equals(PaymentApplyBizStatus.EXCEPTION.getCode())) {
                    status = PaymentApplyGcebErpStatus.EXCEPTION.getCode();
                }
                query.setGcebAndErpStatus(status);
            }
        }
        List<PaymentApplyDto> list = paymentApplyExtMapper.selectPage(query);
        for (PaymentApplyDto paymentApplyDto : list) {
            statusTransformation(paymentApplyDto);
        }
        PageInfo<PaymentApplyDto> page = BeanConverter.convertPage(list, PaymentApplyDto.class);
        return page;
    }

    /**
     * 转换付款申请状态
     *
     * @param paymentApplyDto
     */
    @Override
    public void statusTransformation(PaymentApplyDto paymentApplyDto) {
        String auditStatus = paymentApplyDto.getAuditStatus() + "";
        if (auditStatus.equals(PaymentApplyBizStatus.DRAFT.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.PENDING.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.REFUSE.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.EFFECTIVE.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.CANCEL.getCode())) {
            paymentApplyDto.setBizStatus(paymentApplyDto.getAuditStatus() + "");
        }

        if (Objects.equals(paymentApplyDto.getAuditStatus(), AuditStatus.EXCEPTION.getCode())) {
            paymentApplyDto.setBizStatus(AuditStatus.EXCEPTION.getCode() + "");
        }

        if (auditStatus.equals(PaymentApplyBizStatus.EFFECTIVE.getCode())) {
            String is = paymentApplyDto.getIsCharge() + "";//是否预付款(1:是 0否)
            String status = "";
            if ("1".equals(is)) {
                status = paymentApplyDto.getGcebStatus();
            } else {
                status = paymentApplyDto.getErpStatus();
            }
            if (status.equals(PaymentApplyGcebErpStatus.SUCCESS.getCode())) {
                paymentApplyDto.setBizStatus(PaymentApplyBizStatus.SUCCESS.getCode());
            } else if (status.equals(PaymentApplyGcebErpStatus.CANCEL.getCode())) {
                paymentApplyDto.setBizStatus(PaymentApplyBizStatus.CANCEL_PAY.getCode());
            } else if (status.equals(PaymentApplyGcebErpStatus.PARTIAL_PAYMENT.getCode())) {
                paymentApplyDto.setBizStatus(PaymentApplyBizStatus.PARTIAL_PAYMENT.getCode());
            } else if (status.equals(PaymentApplyGcebErpStatus.EXCEPTION.getCode())) {
                paymentApplyDto.setBizStatus(PaymentApplyBizStatus.EXCEPTION.getCode());
            }

        }
    }

    @Override
    public List<PaymentApplyDto> selectListAll(PaymentApplyQuery query) {

        //biz_status（1草稿,2审核中,3驳回,4生效（支付中）,5作废，6支付成功，7、取消支付、8、部分支付、9、异常完结）
        String bizStatus = query.getBizStatus();
        //`gceb_Status`'gceb同步状态(1-支付完成,2-取消支付,3-部分支付,4-付款中,5-异常完结)',
        //`erp_Status` 'erp同步状态(1-支付完成,2-取消支付,3-部分支付,4-付款中,5-异常完结)',
        if (!StringUtils.isEmpty(bizStatus)) {
            if (bizStatus.equals(PaymentApplyBizStatus.DRAFT.getCode()) ||
                    bizStatus.equals(PaymentApplyBizStatus.PENDING.getCode()) ||
                    bizStatus.equals(PaymentApplyBizStatus.REFUSE.getCode()) ||
                    bizStatus.equals(PaymentApplyBizStatus.EFFECTIVE.getCode()) ||
                    bizStatus.equals(PaymentApplyBizStatus.CANCEL.getCode())) {
                query.setAuditStatus(Integer.parseInt(bizStatus));
            } else {
                String status = null;
                if (bizStatus.equals(PaymentApplyBizStatus.SUCCESS.getCode())) {
                    status = PaymentApplyGcebErpStatus.SUCCESS.getCode();
                } else if (bizStatus.equals(PaymentApplyBizStatus.CANCEL_PAY.getCode())) {
                    status = PaymentApplyGcebErpStatus.CANCEL.getCode();
                } else if (bizStatus.equals(PaymentApplyBizStatus.PARTIAL_PAYMENT.getCode())) {
                    status = PaymentApplyGcebErpStatus.PARTIAL_PAYMENT.getCode();
                } else if (bizStatus.equals(PaymentApplyBizStatus.EXCEPTION.getCode())) {
                    status = PaymentApplyGcebErpStatus.EXCEPTION.getCode();
                }
                query.setGcebAndErpStatus(status);
            }
        }
        List<PaymentApplyDto> list = paymentApplyExtMapper.selectPage(query);
        for (PaymentApplyDto paymentApplyDto : list) {
            statusTransformation(paymentApplyDto);
        }
        List<PaymentApplyDto> dtos = BeanConverter.copy(list, PaymentApplyDto.class);
        return dtos;
    }

    @Override
    public PaymentApply findPaymentApplyByCode(String code) {
        PaymentApplyExample example = new PaymentApplyExample();
        example.createCriteria().andPaymentApplyCodeEqualTo(code);
        List<PaymentApply> list = paymentApplyMapper.selectByExample(example);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 查询付款申请
     *
     * @param query 查询条件
     * @return 付款申请列表
     */
    public List<PaymentApplyDto> list(PaymentApplyQuery query) {
        List<PaymentApply> paymentApplys = this.getEntity(query);
        List<PaymentApplyDto> dtos = BeanConverter.convert(paymentApplys, PaymentApplyDto.class);
        if (!CollectionUtils.isEmpty(dtos)) {
            for (PaymentApplyDto dto : dtos) {
                dto.setPaymentInvoices(paymentInvoiceService.getApplyInvoice(dto.getId()));
            }
        }
        return dtos;
    }

    public List<PaymentApply> getEntity(PaymentApplyQuery query) {
        PaymentApplyExample example = new PaymentApplyExample();
        PaymentApplyExample.Criteria criteria = example.createCriteria();
        example.setOrderByClause("update_at desc");
        criteria.andDeletedFlagEqualTo(0);
        if (!StringUtils.isEmpty(query.getIds())) {
            criteria.andIdIn(query.getIds());
        }
        if (!StringUtils.isEmpty(query.getGcebStatus())) {
            criteria.andGcebStatusEqualTo(query.getGcebStatus());
        }
        if (!StringUtils.isEmpty(query.getAuditStatus())) {
            criteria.andAuditStatusEqualTo(query.getAuditStatus());
        }
        if (!StringUtils.isEmpty(query.getErpStatus())) {
            criteria.andErpStatusEqualTo(query.getErpStatus());
        }
        if (!StringUtils.isEmpty(query.getEsbStatus())) {
            criteria.andEsbStatusEqualTo(query.getEsbStatus());
        }
        if (null != query.getPaymentPlanId()) {
            criteria.andPaymentPlanIdEqualTo(query.getPaymentPlanId());
        }
        if (!StringUtils.isEmpty(query.getPaymentApplyCode())) {
            criteria.andPaymentApplyCodeEqualTo(query.getPaymentApplyCode());
        }
        List<PaymentApply> paymentApplies = paymentApplyMapper.selectByExample(example);
        if (null == paymentApplies) {
            return new ArrayList<>();
        }
        return paymentApplies;
    }

    @Override
    public PaymentApplyDto findPaymentApplyById(Long id) {
        Asserts.notEmpty(id, ErrorCode.ID_NOT_NULL);
        PaymentApply paymentApply = paymentApplyMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(paymentApply, ErrorCode.CTC_PAYMENT_APPLY_CODE_EXIST);
        PaymentApplyDto result = BeanConverter.copy(paymentApply, PaymentApplyDto.class);

        List<CtcAttachmentDto> attachmentRelations = ctcAttachmentService.selectListByModuleAndModuleId(CtcAttachmentModule.PAYMENT_APPLY.code(), id);
        result.setAttachmentRelations(attachmentRelations);
        Long paymentPlanId = result.getPaymentPlanId();
        if (paymentPlanId != null && paymentPlanId >= 0) {
            PaymentPlanDTO paymentPlan = paymentPlanService.findPaymentPlanById(paymentPlanId);
            if (paymentPlan != null) {
                result.setNum(paymentPlan.getNum());//付款计划笔数
                result.setLegalAffairsCode(paymentPlan.getLegalAffairsCode());//法务合同编号
                Long milestoneId = paymentPlan.getMilestoneId();//关联里程碑ID
                if (milestoneId != null && milestoneId > 0) {
                    ProjectMilepostDto projectMilepostDto = projectMilepostService.getById(milestoneId);
                    result.setProjectMilepostDto(projectMilepostDto);
                }
            }

            //累计付款金额（含税）-来源于付款申请关联的计划
            BigDecimal actualAmount = paymentApplyExtMapper.actualAmount(paymentPlanId);
            result.setActualAmount(actualAmount);

            //累计在途金额（含税）-来源于付款申请关联的计划
            BigDecimal totalOnTheWayAmount = paymentApplyExtMapper.totalOnTheWayAmount(paymentPlanId);
            result.setTotalOnTheWayAmount(totalOnTheWayAmount);

            //累计罚扣款（含税）-来源于付款申请关联的计划")
            BigDecimal totalpenaltyAmount = paymentApplyExtMapper.totalpenaltyAmount(paymentPlanId);
            result.setTotalpenaltyAmount(totalpenaltyAmount);
            if (paymentPlan != null && Objects.nonNull(paymentPlan.getAllocationPunishmentAmountWithTax())) {
                result.setTotalpenaltyAmount(result.getTotalpenaltyAmount().add(paymentPlan.getAllocationPunishmentAmountWithTax()));
            }

        }

        Integer isCharge = result.getIsCharge() == null ? 0 : result.getIsCharge();
        if (null != isCharge && isCharge.equals(0)) {//是否预付款(1:是 0否)
            //查询发票行信息
            PaymentInvoiceDetailDto detailQuery = new PaymentInvoiceDetailDto();
            detailQuery.setPaymentApplyId(id);
            detailQuery.setDeletedFlag(DeletedFlag.VALID.code());
            result.setPaymentInvoiceDetails(paymentInvoiceDetailService.selectListWithDetail(detailQuery));
            //查询发票头信息
            PaymentInvoiceDto invoiceQuery = new PaymentInvoiceDto();
            invoiceQuery.setPaymentApplyId(id);
            invoiceQuery.setPurchaseContractId(result.getPurchaseContractId());
            invoiceQuery.setDeletedFlag(DeletedFlag.VALID.code());
            result.setPaymentInvoices(paymentInvoiceService.list(invoiceQuery));
            //查询罚扣信息
            PaymentPenaltyProfitDto penaltyProfitQuery = new PaymentPenaltyProfitDto();
            penaltyProfitQuery.setPaymentApplyId(id);
            penaltyProfitQuery.setDeletedFlag(0);
            result.setPaymentPenaltyProfits(paymentPenaltyProfitService.selectListWithDetail(penaltyProfitQuery));
        }
        //联行号
        Long ouId = result.getOuId();
        String vendorBankNum = result.getVendorBankNum();
        String vendorCode = result.getVendorCode();
        String accountName = result.getAccountName();
        VendorSiteBankDto vendorSiteBankInfo = basedataExtService.getVendorSiteBankInfo(ouId, vendorBankNum, vendorCode, accountName);
        if (vendorSiteBankInfo != null) {
            result.setVendorSiteBank(vendorSiteBankInfo);
            result.setCombineBankNummber(vendorSiteBankInfo.getCombineBankNummber());//联行号
        }

        // 付款记录
        PaymentRecordExample example = new PaymentRecordExample();
        example.createCriteria().andPaymentApplyIdEqualTo(id).andDeletedFlagEqualTo(0);
        List<PaymentRecord> paymentRecord = paymentRecordMapper.selectByExample(example);

        List<PaymentWriteOffRecordDto> writeOffRecordDtosForWeb = new ArrayList<>();
        if (paymentRecord != null) {
            List<PaymentRecordDto> paymentRecordList = BeanConverter.convert(paymentRecord, PaymentRecordDto.class);
            paymentRecordList.forEach(record -> {
                //查询核销记录
                PaymentWriteOffRecordDto writeOffRecordDto = new PaymentWriteOffRecordDto();
                writeOffRecordDto.setPaymentRecordId(record.getId());
                List<PaymentWriteOffRecordDto> writeOffRecordDtos = writeOffRecordService.selectList(writeOffRecordDto);
                for (PaymentWriteOffRecordDto dto : writeOffRecordDtos) {
                    PaymentInvoice invoice = paymentInvoiceMapper.selectByPrimaryKey(dto.getPaymentInvoiceId());
                    if (invoice != null) {
                        dto.setSurplusAmount(invoice.getSurplusAmount());//查询发票可用金额
                        dto.setInvoicePaymentApplyCode(invoice.getPaymentApplyCode());//查询发票的付款申请编号
                    }
                }
                record.setWriteOffRecordList(writeOffRecordDtos);
                writeOffRecordDtosForWeb.addAll(writeOffRecordDtos);
            });
            result.setPaymentRecordDtos(paymentRecordList);
            result.setWriteOffRecordList(writeOffRecordDtosForWeb);
        }
        statusTransformation(result);

        //采购合同进度执行金额（不含税）
        PurchaseContractProgressVo progressVo = purchaseContractService.calculateExecuteAmountTotal(paymentApply.getPurchaseContractId());
        result.setPurchasingContractProgressAmount(progressVo.getExecuteAmountTotalHeader());

        //已入账税票金额（不含税）
        BigDecimal amountOfTaxReceipts = paymentInvoiceExtMapper.calculateAmountOfTaxReceipts(paymentApply.getPurchaseContractId());
        result.setAmountOfTaxReceipts(amountOfTaxReceipts);

        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(result.getPurchaseContractId());
        result.setContractTypeName(purchaseContract.getTypeName());

        //查询当前业务实体下对应的本位币
        String localCurrency = null;
        List<OrganizationRel> organizationRels = basedataExtService.queryByOuId(ouId);
        if (ListUtils.isNotEmpty(organizationRels)) {
            localCurrency = com.midea.pam.common.util.StringUtils.isNotEmpty(organizationRels.get(0).getCurrency()) ?
                    organizationRels.get(0).getCurrency() : "";
        }

        if (!Objects.equals(result.getCurrency(), localCurrency)) {
            result.setForeignFlag(Boolean.TRUE);
        }

        //付款方法信息
        Dict dict = basedataExtService.findDictById(result.getPaymentMethodId());
        if (dict != null) {
            result.setPaymentMethodCode(dict.getCode());
            result.setPaymentMethodType(dict.getType());
        }
        result.setConversionRate(Optional.ofNullable(result.getConversionRate()).orElse(BigDecimal.ONE));
        result.setTaxPayIncludedPrice(Optional.ofNullable(result.getTaxPayIncludedPrice()).orElse(BigDecimal.ZERO));

        //查询外部附件列表
        if (Objects.equals(result.getSourceSystemName(), PaymentApplySourceNameEnum.GSC.getName())) {
            List<PaymentApplyExternalAttachment> paymentApplyExternalAttachments = paymentApplyExternalAttachmentExtMapper.selectByPaymentApplyId(result.getId());
            if (ListUtils.isNotEmpty(paymentApplyExternalAttachments)) {
                result.setExternalAttachmentList(paymentApplyExternalAttachments);
            }
        }

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PaymentApplyDto saveOrUpdate(PaymentApplyDto paymentApplyDto) {
        checkParams(paymentApplyDto);
        //付款申请，非预付款且选用常规发票，才需要校验gl日期
        if (paymentApplyDto.getIsCharge() == 0 && paymentApplyDto.getInvoiceMethod() == 1) {
            paymentInvoiceService.checkGlDate(paymentApplyDto.getOuId(), paymentApplyDto.getGlDate());
        }
        if (!BigDecimalUtils.isGreater(paymentApplyDto.getTaxPayIncludedPrice(), BigDecimal.ZERO)) {
            throw new MipException("本次申请付款金额(含税)不能为0或者为负数");
        }

        Long id = paymentApplyDto.getId();
        PaymentApply dbPaymentApply = null;

        Long purchaseContractId = paymentApplyDto.getPurchaseContractId();
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);
        if (!StringUtils.isEmpty(paymentApplyDto.getRemark())
                && paymentApplyDto.getRemark().length() >= 80) {
            throw new MipException("付款备注不能超过80字符");
        }
        if (id != null && id > 0) {
            dbPaymentApply = paymentApplyMapper.selectByPrimaryKey(id);
        }
        if (dbPaymentApply == null) {

            boolean is = isExistingApplication(paymentApplyDto.getPaymentPlanId(), paymentApplyDto.getTaxPayIncludedPrice());
            if (!is) {
                throw new BizException(ErrorCode.CTC_PAYMENT_PLAN_CODE_EXCESS);
            }
            String seqPerfix = basedataExtService.getUnitSeqPerfix(SystemContext.getUnitId());
            // 付款申请单号
            //因sit环境redis服务问题导致单据号重复，先
            final String code = seqPerfix + APPLY_CODE + CacheDataUtils.generateSequence(4, seqPerfix + APPLY_CODE, "YYMM");
            // final String code = seqPerfix+APPLY_CODE+ CacheDataUtils.getRandom(5);

            dbPaymentApply = new PaymentApply();
            if (purchaseContract != null) {
                dbPaymentApply.setCurrency(purchaseContract.getCurrency());
                dbPaymentApply.setConversionDate(purchaseContract.getConversionDate());
                dbPaymentApply.setConversionRate(purchaseContract.getConversionRate());
                dbPaymentApply.setConversionType(purchaseContract.getConversionType());
            }
            dbPaymentApply.setCreateAt(new Date());//创建时间
            dbPaymentApply.setUpdateAt(new Date());//修改时间
            dbPaymentApply.setVersion(0L);
            dbPaymentApply.setPaymentApplyCode(code);
            dbPaymentApply.setAuditStatus(AuditStatus.DRAFT.getCode());//付款申请审批状态
            if (Objects.isNull(paymentApplyDto.getSourceSystemName())) {
                dbPaymentApply.setSourceSystemName(PaymentApplySourceNameEnum.PAM.getName());
            }
            dbPaymentApply.setDeletedFlag(0);
            paymentApplyDtoToPaymentApply(paymentApplyDto, dbPaymentApply);
            //前端传值为contractId,去采购合同id只能在这取
            PurchaseContract purContract = purchaseContractMapper.selectByPrimaryKey(dbPaymentApply.getPurchaseContractId());
            if (purContract != null) {
                //查组织参数:付款申请合同原件控制
                Set<String> typeSet = organizationCustomDictService.queryByName("付款申请合同原件控制", SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
                //付款申请合同原件控制，0：不启用，1：启用
                if (typeSet.size() == 1 && Objects.equals(typeSet.iterator().next(), "1")) {
                    if (!Objects.equals(Boolean.TRUE, purContract.getGleSignFlag())) {
                        throw new BizException(Code.ERROR, "没有上传合同双签原件无法发起付款申请");
                    }
                }
            }
            paymentApplyMapper.insert(dbPaymentApply);
        } else {
            paymentApplyDtoToPaymentApply(paymentApplyDto, dbPaymentApply);
            paymentApplyMapper.updateByPrimaryKey(dbPaymentApply);
            // 推送GSC付款申请支付状态
            if (Objects.equals(dbPaymentApply.getSourceSystemName(), PaymentApplySourceNameEnum.GSC.getName())) {
                gscPaymentAndInvoiceStatusPushService.pushPaymentAndInvoiceStatus(GSCPaymentAndInvoiceStatusPushEnum.PAYMENT_APPLY_GCEB_STATUS_PUSH, dbPaymentApply.getId());
            }
        }

        Long paymentApplyId = dbPaymentApply.getId();//付款申请ID
        paymentApplyDto.setId(paymentApplyId);
        Integer isCharge = paymentApplyDto.getIsCharge();
        if (isCharge != null && isCharge == 0) {//是否预付款(1:是 0否)
            Integer invoiceMethod = paymentApplyDto.getInvoiceMethod();
            if (invoiceMethod == 1) {//1新增发票2选用已入账发票
                //保存付款申请和发票行关联表
                saveApplyInvoiceDetailRel(paymentApplyDto);
                //保存罚扣信息(暂定新增票才可以保存罚扣信息)
                List<PaymentPenaltyProfitDto> paymentPenaltyProfits = paymentApplyDto.getPaymentPenaltyProfits();
                List<PaymentInvoiceDetailDto> detailDtos = new ArrayList<>();
                for (PaymentPenaltyProfitDto penaltyProfitDto : paymentPenaltyProfits) {
                    penaltyProfitDto.setPaymentApplyId(paymentApplyId);
                    //更新罚扣关联发票状态
                    if (null != penaltyProfitDto.getPaymentInvoiceDetailId()) {
                        PaymentInvoiceDetailDto detailDto = paymentInvoiceDetailService.getDetailById(penaltyProfitDto.getPaymentInvoiceDetailId(), null);
                        if (null != penaltyProfitDto.getDeletedFlag() && penaltyProfitDto.getDeletedFlag() == 1) {
                            detailDto.setInvoiceStatus(InvoiceDetailStatus.UNQUOTED.getCode());
                            punishmentService.updatePunishmentAccountStatus(detailDto, PurchaseContractPunishmentAccountStatusEnum.NOT_ACCOUNTED.getCode());
                        } else {
                            detailDto.setInvoiceStatus(InvoiceDetailStatus.QUOTE.getCode());
                            punishmentService.updatePunishmentAccountStatus(detailDto, PurchaseContractPunishmentAccountStatusEnum.ACCOUNTED.getCode());
                        }
                        detailDtos.add(detailDto);
                    }
                }
                paymentInvoiceDetailService.batchSave(detailDtos, SystemContext.getUserId());
                paymentPenaltyProfitService.batchSave(paymentPenaltyProfits, SystemContext.getUserId());
            } else {
                //保存付款申请和AP发票关联表(包括ap发票本次占用金额)
                saveApplyInvoiceRel(paymentApplyDto);
            }
        }
        //保存附件
        List<CtcAttachmentDto> attachmentRelations = paymentApplyDto.getAttachmentRelations();
        if (null != attachmentRelations && attachmentRelations.size() > 0) {
            ctcAttachmentService.saveBatch(attachmentRelations, CtcAttachmentModule.PAYMENT_APPLY.code(),
                    paymentApplyId, AttachmentStatus.PASSED.code(), SystemContext.getUserId());
        }

        //更新付款计划付款申请来源
        if (!Objects.equals(paymentApplyDto.getSourceSystemName(), PaymentApplySourceNameEnum.GSC.getName())) {
            paymentPlanExtMapper.updatePaymentApplySource(paymentApplyDto.getPaymentPlanId(), PaymentApplySourceNameEnum.PAM.getName());
        }

        paymentApplyDto.setId(paymentApplyId);
        return findPaymentApplyById(paymentApplyId);
    }

    /**
     * 参数校验
     *
     * @param paymentApplyDto 付款申请
     */
    private void checkParams(PaymentApplyDto paymentApplyDto) {
        if (paymentApplyDto.getIsCharge() != null && paymentApplyDto.getIsCharge() == 0) {
            if (paymentApplyDto.getGlDate() == null) {
                throw new BizException(Code.ERROR, "总帐日期不能为空");
            }
        }
        if (ListUtils.isNotEmpty(paymentApplyDto.getPaymentPenaltyProfits())) {
            paymentApplyDto.getPaymentPenaltyProfits().forEach(e -> {
                if (e.getAmount() == null) {
                    throw new BizException(Code.ERROR, "罚扣金额不能为空");
                }
            });
        }

        //非预付款判断是否关联合同是否存在待处理的罚扣
        if (paymentApplyDto.getIsCharge() == 0) {
            Long purchaseContractId = paymentApplyDto.getPurchaseContractId();
            if (Objects.isNull(purchaseContractId)) {
                List<PaymentInvoiceDetailDto> paymentInvoiceDetails = paymentApplyDto.getPaymentInvoiceDetails();
                if (ListUtils.isNotEmpty(paymentInvoiceDetails)) {
                    purchaseContractId = paymentInvoiceDetails.get(0).getPurchaseContractId();
                }
            }

            if (Objects.isNull(purchaseContractId)) {
                List<PaymentInvoiceDto> paymentInvoices = paymentApplyDto.getPaymentInvoices();
                if (ListUtils.isNotEmpty(paymentInvoices)) {
                    purchaseContractId = paymentInvoices.get(0).getPurchaseContractId();
                }
            }

            if (Objects.nonNull(purchaseContractId)) {
                List<Integer> statusList = Arrays.asList(PurchaseContractPunishmentApproveEnums.DRAFT.getCode()
                        , PurchaseContractPunishmentApproveEnums.PENDING_CONFIRM.getCode()
                        , PurchaseContractPunishmentApproveEnums.APPROVING.getCode()
                        , PurchaseContractPunishmentApproveEnums.PENDING_PROCESS.getCode());
                //查询待处理罚扣的数量
                List<PurchaseContractPunishment> punishmentList = punishmentExtMapper.getPunishmentListByStatus(purchaseContractId, statusList);
                if (ListUtils.isNotEmpty(punishmentList)) {
                    punishmentList.sort(Comparator.comparing(PurchaseContractPunishment::getStatus));
                    StringBuilder tipSB = new StringBuilder();
                    for (PurchaseContractPunishment purchaseContractPunishment : punishmentList) {
                        Integer status = purchaseContractPunishment.getStatus();
                        String statusName = PurchaseContractPunishmentApproveEnums.getMsgByCode(status);
                        tipSB.append(statusName).append("的罚扣:").append(purchaseContractPunishment.getCode()).append(",");
                    }
                    //对tipSB最后的逗号进行处理
                    String tip = tipSB.toString();
                    if (tip.endsWith(",")) {
                        tip = tip.substring(0, tip.length() - 1);
                    }

                    throw new BizException(Code.ERROR, "合同存在状态为:" + tip + "，请先处理完（作废或生效）再发起发票付款");
                }
            }
        }
        if (Objects.nonNull(paymentApplyDto.getPurchaseContractCode())) {
            //如果该合同下存在一个或多个付款计划超付（超付就是这个付款计划的剩余付款金额（含税）为负数）检查
            final Map<String, Object> params = new HashMap<>();
            params.put("purchaseContractCode", paymentApplyDto.getPurchaseContractCode());
            params.put("unitId", SystemContext.getUnitId());
            String url = UrlUtils.buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/paymentPlan/v1/listByUnitIdAndContractCode", params);

            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            DataResponse<List<PaymentPlanDTO>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<PaymentPlanDTO>>>() {
            });
            if (Objects.nonNull(response) && Objects.nonNull(response.getData())) {
                List<PaymentPlanDTO> planList = response.getData();
                if (ListUtils.isNotEmpty(planList)) {
                    for (PaymentPlanDTO paymentPlanDTO : planList) {
                        BigDecimal amount = Optional.ofNullable(paymentPlanDTO.getAmount()).orElse(BigDecimal.ZERO);
                        BigDecimal actualAmount = Optional.ofNullable(paymentPlanDTO.getActualAmount()).orElse(BigDecimal.ZERO);
                        BigDecimal totalOnTheWayAmount = Optional.ofNullable(paymentPlanDTO.getTotalOnTheWayAmount()).orElse(BigDecimal.ZERO);
                        BigDecimal totalPenaltyAmount = Optional.ofNullable(paymentPlanDTO.getTotalpenaltyAmount()).orElse(BigDecimal.ZERO);
                        if ((amount.subtract(actualAmount).subtract(totalOnTheWayAmount).subtract(totalPenaltyAmount)).compareTo(BigDecimal.ZERO) < 0) {
                            throw new BizException(Code.ERROR, "付款计划：" + paymentPlanDTO.getCode() + "超付了，请调整付款计划金额");
                        }
                    }
                }
            }
        }

        //付款申请来源校验
        if (Objects.nonNull(paymentApplyDto.getPaymentPlanId())) {
            PaymentPlan paymentPlan = paymentPlanMapper.selectByPrimaryKey(paymentApplyDto.getPaymentPlanId());
            if (StringUtil.isNotNull(paymentPlan.getPaymentApplySource())
                    && Objects.equals(PaymentApplySourceNameEnum.GSC.getName(), paymentPlan.getPaymentApplySource())
                    && !Objects.equals(PaymentApplySourceNameEnum.GSC.getName(), paymentApplyDto.getSourceSystemName())) {
                throw new BizException(Code.ERROR, "该付款计划已在GSC发起付款申请，不可在PAM发起");
            }
        }
    }

    /**
     * 判断该付款计划是否超额
     *
     * @param paymentPlanId       付款计划ID
     * @param taxPayIncludedPrice 本次付款金额
     * @return
     */
    private boolean isExistingApplication(Long paymentPlanId, BigDecimal taxPayIncludedPrice) {
        if (taxPayIncludedPrice == null) {
            taxPayIncludedPrice = new BigDecimal(0);
        }
        if (paymentPlanId == null || paymentPlanId == 0) {
            throw new BizException(ErrorCode.ID_NOT_NULL);
        }
        PaymentPlan paymentPlan = null;
        if (paymentPlanId != null && paymentPlanId > 0) {
            paymentPlan = paymentPlanService.findById(paymentPlanId);
        }
        if (paymentPlan == null) {
            throw new BizException(ErrorCode.CTC_PAYMENT_PLAN_CODE_EXIST);
        }

        BigDecimal amount = paymentPlan.getAmount();//计划付款金额
        if (amount == null) {
            amount = new BigDecimal(0);
        }

        //累计已经申请金额（含税）
       /* BigDecimal totalApplyAmount = paymentApplyExtMapper.totalApplyAmount(paymentPlanId);
        if(totalApplyAmount==null){
            totalApplyAmount = new BigDecimal(0);
        }*/

        //累计在途金额（含税）")
        BigDecimal totalOnTheWayAmount = paymentApplyExtMapper.totalOnTheWayAmount(paymentPlanId);
        if (totalOnTheWayAmount == null) {
            totalOnTheWayAmount = new BigDecimal(0);
        }


        //累计付款金额（含税）")
        BigDecimal actualAmount = paymentApplyExtMapper.actualAmount(paymentPlanId);
        if (actualAmount == null) {
            actualAmount = new BigDecimal(0);
        }

        //累计罚扣款（含税）")
       /* BigDecimal totalpenaltyAmount = paymentApplyExtMapper.totalpenaltyAmount(paymentPlanId);
        if(totalpenaltyAmount==null){
            totalpenaltyAmount = new BigDecimal(0);
        }*/
        //signum返回 的是 -1, 0, 1，分别表示 负数、零、正数
        BigDecimal ss = amount.subtract(totalOnTheWayAmount).subtract(actualAmount).subtract(taxPayIncludedPrice);
        if (ss.signum() == -1) {
            return false;
        }
        return true;
    }

    /**
     * 判断该付款申请AP票余额是否足够
     *
     * @param list                用于本次付款的ap发票
     * @param taxPayIncludedPrice 本次付款金额
     * @return
     */
    private boolean isEnoughForAp(List<PaymentInvoiceDto> list, BigDecimal taxPayIncludedPrice) {
        if (taxPayIncludedPrice == null) {
            taxPayIncludedPrice = BigDecimal.valueOf(0.00);
        }
        BigDecimal apAmount = BigDecimal.valueOf(0.00);
        for (PaymentInvoiceDto paymentInvoiceDto : list) {
            if (1 == paymentInvoiceDto.getErpStatus()) {//ap发票已验证
                apAmount = apAmount.add(paymentInvoiceDto.getSurplusAmount());
            }
        }
        return taxPayIncludedPrice.compareTo(apAmount) < 1;
    }

    private void paymentApplyDtoToPaymentApply(PaymentApplyDto paymentApplyDto, PaymentApply paymentApply) {

        BeanUtils.copyProperties(paymentApplyDto, paymentApply, getNullPropertyNames(paymentApplyDto));
        Long paymentPlanId = paymentApplyDto.getPaymentPlanId();


        if (paymentPlanId != null && paymentPlanId > 0) {
            PaymentPlanDTO paymentPlanDTO = paymentPlanService.findPaymentPlanById(paymentPlanId);
            if (paymentPlanDTO != null) {
                paymentApply.setPaymentPlanCode(paymentPlanDTO.getCode());//采购合同付款计划编号
                paymentApply.setPaymentPlanNum(paymentPlanDTO.getNum());//采购合同付款计划笔数
                paymentApply.setPaymentPlanDate(paymentPlanDTO.getDate());//计划付款日期
                paymentApply.setRequirement(paymentPlanDTO.getRequirement());//付款条件
                paymentApply.setTaxPlanIncludedPrice(paymentPlanDTO.getAmount());//计划付款金额(含税)
                paymentApply.setVendorSiteCode(paymentPlanDTO.getVendorSiteCode());//供应商地点名称
                String vendorSiteIdStr = paymentPlanDTO.getErpVendorSiteId();//ERP地点信息ID
                if (!StringUtils.isEmpty(vendorSiteIdStr)) {
                    paymentApply.setErpVendorSiteId(Long.parseLong(vendorSiteIdStr));//供应商地点ID
                }
                //paymentApply.setCurrency("CNY");// 写死  币种
                //dbPaymentApply.setMoneyBack();//项目回款
                Long vendorId = paymentPlanDTO.getVendorId();
                if (vendorId != null && vendorId > 0) {
                    paymentApply.setVendorId(vendorId);//供应商ID
                    VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(vendorId);
                    if (vendorSiteBankDto != null) {
                        paymentApply.setVendorCode(vendorSiteBankDto.getVendorCode());//供应商编码
                        paymentApply.setVendorErpId(vendorSiteBankDto.getErpVendorId());//供应商ID(ERP)
                    }
                }
                paymentApply.setVendorName(paymentPlanDTO.getVendorName());//供应商名称
                paymentApply.setProjectId(paymentPlanDTO.getProjectId());//项目ID
                paymentApply.setProjectCode(paymentPlanDTO.getProjectCode());//项目编码
                paymentApply.setProjectName(paymentPlanDTO.getProjectName());//项目名称
                paymentApply.setPurchaseContractId(paymentPlanDTO.getContractId());//合同ID
                paymentApply.setPurchaseContractCode(paymentPlanDTO.getPurchaseContractCode());//合同编码
                paymentApply.setPurchaseContractName(paymentPlanDTO.getPurchaseContractName());//合同名称

                Long contractId = paymentPlanDTO.getContractId();//采购合同ID
                PurchaseContract purchaseContract = null;
                if (contractId != null && contractId > 0) {
                    purchaseContract = purchaseContractService.findById(contractId);
                }
                if (purchaseContract != null) {
                    Long ouId = purchaseContract.getOuId();//业务实体ID
                    paymentApply.setOuId(ouId);
                    if (ouId != null && ouId > 0) {
                        OperatingUnit operatingUnit = CacheDataUtils.findOuById(ouId);
                        if (operatingUnit != null) {
                            paymentApply.setOuName(operatingUnit.getOperatingUnitName());//业务实体名称
                        }
                    }
                }
            }
            paymentApply.setGcebStatus(GcebInterfaceSynStatus.WAIT_FOR.getCode());//资金系统同步状态
            paymentApply.setErpStatus(GcebInterfaceSynStatus.WAIT_FOR.getCode());
            paymentApply.setRemark(paymentApplyDto.getRemark());//备注
            paymentApply.setSubmitBy(SystemContext.getUserId());//提交人ID
            paymentApply.setSubmitName(SystemContext.getUserName());//提交人姓名
            paymentApply.setSubmitDate(new Date());//提交日期
            paymentApply.setPaymentMethodId(paymentApplyDto.getPaymentMethodId());//付款方式id
            paymentApply.setPaymentMethodName(paymentApplyDto.getPaymentMethodName());//付款方式名称
            paymentApply.setPaymentMethod(paymentApplyDto.getPaymentMethod());//账号付款方式
            paymentApply.setVendorBankNum(paymentApplyDto.getVendorBankNum());//供应商银行账号
            paymentApply.setAccountNum(paymentApplyDto.getAccountNum());//供应商账户名
            paymentApply.setAccountName(paymentApplyDto.getAccountName());//供应商账户开户行
            paymentApply.setTaxPayIncludedPrice(paymentApplyDto.getTaxPayIncludedPrice());//本次申请付款金额
            BeanUtils.copyProperties(paymentApply, paymentApplyDto, getNullPropertyNames(paymentApply));
        } else {
            paymentApplyDto.setCreateAt(null);//创建时间
            paymentApplyDto.setCreateBy(null);//创建人
            paymentApplyDto.setIsCharge(null);//是否预付款
            paymentApplyDto.setVersion(null);//版本号
            BeanUtils.copyProperties(paymentApplyDto, paymentApply, getNullPropertyNames(paymentApplyDto));
        }

        if (Objects.nonNull(paymentApplyDto.getOuId()) && Objects.nonNull(paymentApplyDto.getVendorId())) {
            Pair<String, String> budgetProjectNumberPair = basedataExtService.getBudgetProjectNumberByVendorId(null, paymentApplyDto.getVendorId(), paymentApplyDto.getOuId());
            if (Objects.nonNull(budgetProjectNumberPair)) {
                paymentApply.setItemNumber(budgetProjectNumberPair.getValue());
                paymentApply.setItemNumberName(budgetProjectNumberPair.getKey());
            } else {
                logger.info("budgetProjectNumberPair返回为空");
            }
        }
    }

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    @Override
    public int save(PaymentApply paymentApply) {
        return paymentApplyMapper.insert(paymentApply);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int abandon(Long applyId) {
        String lockName = String.format("purchaseApply_abandon_%s", applyId);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                final PaymentApply paymentApply = findById(applyId);
                if (paymentApply == null) {
                    throw new BizException(ErrorCode.ERROR, "付款申请不存在");
                }
                if (Objects.equals(paymentApply.getAuditStatus(), AuditStatus.CANCEL.getCode())) {
                    throw new BizException(ErrorCode.ERROR, "付款申请已作废，请勿重复操作");
                }
                //只有草稿、驳回、esb同步异常,状态可以作废
                if (!(AuditStatus.REFUSE.getCode() == paymentApply.getAuditStatus()
                        || AuditStatus.DRAFT.getCode() == paymentApply.getAuditStatus()
                        || PaymentApplyEsbStatus.ABNORMAL.getCode().equals(paymentApply.getEsbStatus()))) {
                    throw new BizException(ErrorCode.ERROR, "付款申请当前状态不可作废");
                }

                paymentApply.setAuditStatus(AuditStatus.CANCEL.getCode());
                update(paymentApply);
                //释放发票行/发票头
                cancelApply(paymentApply);
            }
        } catch (Exception e) {
            logger.error("付款申请作废出现异常", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
        return 1;
    }

    @Override
    public int update(PaymentApply paymentApply) {
        return paymentApplyMapper.updateByPrimaryKey(paymentApply);
    }

    @Override
    public String updatePaymentGcebStatus(String paymentApplyCode, String gcebStatus, String remark, BigDecimal cancelAmount) {
        if (!StringUtils.isEmpty(paymentApplyCode)) {
            PaymentApply paymentApply = findPaymentApplyByCode(paymentApplyCode);
            if (paymentApply != null) {
                paymentApply.setGcebStatus(gcebStatus);
                paymentApply.setRemark(remark);
                paymentApply.setUpdateAt(new Date());
                BigDecimal totalCancelAmount = BigDecimal.ZERO;
                if (PaymentApplyGcebErpStatus.CANCEL.getCode().equals(gcebStatus)) {
                    totalCancelAmount = paymentApply.getTotalCancelAmount() == null ? BigDecimal.ZERO :
                            paymentApply.getTotalCancelAmount();
                    if (cancelAmount != null) {
                        totalCancelAmount = totalCancelAmount.add(cancelAmount);
                    }
                    paymentApply.setTotalCancelAmount(totalCancelAmount);
                }
                updateGcebStatus(totalCancelAmount, paymentApply);

                paymentApplyMapper.updateByPrimaryKeySelective(paymentApply);

                // 取消支付发送邮件提醒
                emailPaymentApply(paymentApply.getId());

                // 推送GSC付款申请支付状态
                if (Objects.equals(paymentApply.getSourceSystemName(), PaymentApplySourceNameEnum.GSC.getName())) {
                    gscPaymentAndInvoiceStatusPushService.pushPaymentAndInvoiceStatus(GSCPaymentAndInvoiceStatusPushEnum.PAYMENT_APPLY_GCEB_STATUS_PUSH, paymentApply.getId());
                }

                return "success";
            }
        }
        return "fail";
    }

    /**
     * 根据当前的付款金额和取消金额更新Gceb状态。
     *
     * @param totalCancelAmount 取消的金额
     * @param paymentApply      支付申请对象
     */
    private void updateGcebStatus(BigDecimal totalCancelAmount, PaymentApply paymentApply) {
        // 本次付款申请金额（含税）
        BigDecimal taxPayIncludedPrice = paymentApply.getTaxPayIncludedPrice();


        if (taxPayIncludedPrice == null) {
            logger.warn("付款申请金额为null，无法更新Gceb状态。PaymentApplyId: {}", paymentApply.getId());
            return;
        }

        if (totalCancelAmount == null) {
            totalCancelAmount = BigDecimal.ZERO;
        }


        // 实际付款金额（含税）
        BigDecimal reallyPayIncludedPrice = paymentApply.getReallyPayIncludedPrice();
        //BigDecimal reallyPayIncludedPrice = taxPayIncludedPrice.subtract(totalCancelAmount);

        logger.info("开始更新Gceb状态。PaymentApplyId: {}, totalCancelAmount: {}, TaxPayIncludedPrice: {}, ReallyPayIncludedPrice: {}",
                paymentApply.getId(), totalCancelAmount, taxPayIncludedPrice, reallyPayIncludedPrice);

        if (taxPayIncludedPrice.compareTo(totalCancelAmount) == 0) {
            // 情况 i：取消金额等于付款申请金额，状态更新为：取消支付
            paymentApply.setGcebStatus(PaymentApplyGcebErpStatus.CANCEL.getCode());
            logger.info("取消金额({})等于付款申请金额({})，GcebStatus设置为CANCEL。PaymentApplyId: {}",
                    totalCancelAmount, taxPayIncludedPrice, paymentApply.getId());
        } else if ((Objects.nonNull(reallyPayIncludedPrice) && (totalCancelAmount.add(reallyPayIncludedPrice)).compareTo(taxPayIncludedPrice) == 0)) {
            // 情况 ii：取消金额不等于付款申请金额，实际付款金额 + 取消金额 = 付款申请金额，状态更新为：支付成功
            paymentApply.setGcebStatus(PaymentApplyGcebErpStatus.SUCCESS.getCode());
            logger.info("取消金额({})不等于付款申请金额({})，且实际付款金额({}) + 取消金额 = 付款申请金额，GcebStatus设置为SUCCESS。PaymentApplyId: {}",
                    totalCancelAmount, taxPayIncludedPrice, reallyPayIncludedPrice, paymentApply.getId());
        } else if (totalCancelAmount.compareTo(taxPayIncludedPrice) != 0 && Objects.isNull(reallyPayIncludedPrice)) {
            // 情况 iii：取消金额不等于付款申请金额，实际付款金额为0，状态更新为：生效（支付中）
            paymentApply.setGcebStatus(PaymentApplyGcebErpStatus.IN_PAYMENT.getCode());
            logger.info("取消金额({})不等于付款申请金额({})，实际付款金额为0，GcebStatus设置为IN_PAYMENT。PaymentApplyId: {}",
                    totalCancelAmount, taxPayIncludedPrice, paymentApply.getId());
        } else {
            // 情况 iv：取消金额不等于付款申请金额，实际付款金额 ≠ 0，状态更新为：部分支付
            paymentApply.setGcebStatus(PaymentApplyGcebErpStatus.PARTIAL_PAYMENT.getCode());
            logger.info("取消金额({})不等于付款申请金额({})，实际付款金额({}) > 0，GcebStatus设置为PARTIAL_PAYMENT。PaymentApplyId: {}",
                    totalCancelAmount, taxPayIncludedPrice, reallyPayIncludedPrice, paymentApply.getId());
        }
    }

    @Override
    public int deleteById(Long id) {
        PaymentApply paymentApply = new PaymentApply();
        paymentApply.setId(id);
        paymentApply.setDeletedFlag(1);
        return update(paymentApply);
    }

    @Override
    public PaymentApply findById(Long id) {
        return paymentApplyMapper.selectByPrimaryKey(id);
    }

    /**
     * PAM-ERP-028 付款申请写入
     *
     * @param applyDto
     */
    @Override
    public void pushApplyToErp(PaymentApplyDto applyDto, String lastUpdateDate) {
        PaymentApply updatePaymentApply = new PaymentApply();
        updatePaymentApply.setId(applyDto.getId());
        //接口发送状态0-待同步 1-同步异常2-同步成功
        String esbStatus = applyDto.getEsbStatus();
        //待同步/同步异常--发送付款申请
        if (!"0".equals(esbStatus)) {
            return;
        }
        List<PaymentInvoiceDto> invoiceDtos = paymentInvoiceService.getApplyInvoice(applyDto.getId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(invoiceDtos)) {
            for (PaymentInvoiceDto invoiceDto : invoiceDtos) {
                Integer erpStatus = invoiceDto.getErpStatus();
                //ERP同步状态(0验证中/1同步成功/2-已取消/3同步失败/4待同步 )
                if (erpStatus == 0 || erpStatus == 4) {
                    return;
                } else if (erpStatus == 2 || erpStatus == 3) {
                    //ap发票状态已取消,付款申请同步异常
                    updatePaymentApply.setEsbStatus("1");
                    updatePaymentApply.setErpMsg(invoiceDto.getErpMsg());
                    paymentApplyMapper.updateByPrimaryKeySelective(updatePaymentApply);
                    return;
                }
            }
            //AP发票状态都可用,付款计划插入erp推送队列
            HandleDispatcher.route(BusinessTypeEnums.PAYMENT_PLAN.getCode(), String.valueOf(applyDto.getId()), null, null, true);
            //更新付款申请同步状态
            updatePaymentApply.setEsbStatus("2");
            paymentApplyMapper.updateByPrimaryKeySelective(updatePaymentApply);
        }
    }

    /**
     * PAM-ERP-028 付款申请写入 回调
     *
     * @param id
     * @param isSuccess
     * @param msg
     * @param actualCost
     * @return
     */
    @Override
    public Boolean planCallBack(Long id, Boolean isSuccess, String msg, String actualCost) {
        if (StringUtils.isEmpty(id)) {
            return Boolean.FALSE;
        }
        PaymentApply apply = paymentApplyMapper.selectByPrimaryKey(id);
        // 如果不是未支付，直接跳过
        if (!(apply.getErpStatus() == null
                || Objects.equals(apply.getErpStatus(), GcebInterfaceSynStatus.WAIT_FOR.getCode()))) {
            return Boolean.TRUE;
        }
        if (isSuccess) {
            apply.setEsbStatus("2");//同步成功
            apply.setErpStatus("4");//付款中
        } else {
            apply.setEsbStatus("1");
            apply.setErpMsg(msg);
        }
        update(apply);
        return Boolean.TRUE;
    }

    /**
     * erp查询预付款结果(用于预付款)
     *
     * @param paramMap
     */
    @Override
    public void getApplyInfoFromErp(PaymentApplyDto applyDto, Map<String, String> paramMap) {
        //调erp接口
        logger.info("ERP_PIFACECODE_027 start............");
//        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_027, paramMap);
        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_027, paramMap);
        logger.info("erp查询预付款结果(用于预付款)信息为:{}", JSON.toJSONString(returnItemList));
        BigDecimal totalPayAmount = BigDecimal.valueOf(0.00);//付款申请实际付款金额(含税)
        Boolean flag = false;//支付标识
        PaymentRecord updatePaymentRecord = null;
        for (SdpTradeResultResponseEleDto item : returnItemList) {
            String paymentApplyCode = item.getC13();//付款申请编号
            if (!StringUtils.isEmpty(paymentApplyCode)
                    && paymentApplyCode.equals(applyDto.getPaymentApplyCode())) {
                //外币付款，会多一条CNY的付款记录，导致支付状态不对 BUG2023030931281
                if (!Objects.equals(applyDto.getCurrency(), item.getC11())) {
                    continue;
                }
                Long erpInvoiceId = PamStringUtil.toLongValue(item.getC17());//ERP erp发票ID
                String erpInvoiceCode = item.getC20();//ERP erp发票编号
                String payNum = item.getC9();//付款编号
                String pamentDate = item.getC5();//付款日期
                String happenDate = item.getC5();//发生日期
                BigDecimal amount = PamStringUtil.toBigDecimal(item.getC12());//付款金额(含税)
                if (null != amount && amount.compareTo(BigDecimal.ZERO) > 0) {
                    totalPayAmount = totalPayAmount.add(amount);
                    //根据付款申请ID以及付款编号查询实际付款记录
                    PaymentRecordExample example = new PaymentRecordExample();
                    example.createCriteria().andPaymentApplyIdEqualTo(applyDto.getId()).andPayNumEqualTo(payNum);
                    List<PaymentRecord> list = paymentRecordMapper.selectByExample(example);
                    logger.info("付款计划ID:{},付款申请ID:{},付款申请编号:{},付款申请累计实际付款金额:{},查询的付款记录为:{},",
                            applyDto.getPaymentPlanId(), applyDto.getId(), payNum, totalPayAmount,
                            JSON.toJSONString(list));
                    if (list != null && list.size() > 0) {
                        updatePaymentRecord = new PaymentRecord();
                        updatePaymentRecord.setId(list.get(0).getId());
                        updatePaymentRecord.setPurchaseContractCode(applyDto.getPurchaseContractCode());
                        updatePaymentRecord.setPurchaseContractId(applyDto.getPurchaseContractId());
                        updatePaymentRecord.setErpInvoiceId(erpInvoiceId);
                        updatePaymentRecord.setErpInvoiceCode(erpInvoiceCode);
                        updatePaymentRecord.setPayType(applyDto.getIsCharge() == 1 ? "预付款" : "发票付款");
                        updatePaymentRecord.setPenaltyAmount(BigDecimal.valueOf(0.00));//TODO 罚扣
                        if (!StringUtils.isEmpty(happenDate)) {
                            updatePaymentRecord.setHappenDate(DateUtil.parseDate(happenDate));
                        }
                        updatePaymentRecord.setSubmitBy(applyDto.getSubmitBy());
                        updatePaymentRecord.setSubmitPerson(applyDto.getSubmitName());
                        updatePaymentRecord.setAmount(amount);
                        if (!StringUtils.isEmpty(pamentDate)) {
                            updatePaymentRecord.setPaymentDate(DateUtil.parseDate(pamentDate));
                        }
                        paymentRecordMapper.updateByPrimaryKeySelective(updatePaymentRecord);
                    } else {
                        PaymentRecord paymentRecord = new PaymentRecord();
                        paymentRecord.setPaymentApplyId(applyDto.getId());
                        paymentRecord.setPurchaseContractCode(applyDto.getPurchaseContractCode());
                        paymentRecord.setPurchaseContractId(applyDto.getPurchaseContractId());
                        paymentRecord.setErpInvoiceId(erpInvoiceId);
                        paymentRecord.setErpInvoiceCode(erpInvoiceCode);
                        paymentRecord.setPayType(applyDto.getIsCharge() == 1 ? "预付款" : "发票付款");
                        paymentRecord.setPenaltyAmount(BigDecimal.valueOf(0.00));//TODO 罚扣
                        if (!StringUtils.isEmpty(happenDate)) {
                            paymentRecord.setHappenDate(DateUtil.parseDate(happenDate));
                        }
                        paymentRecord.setSubmitBy(applyDto.getSubmitBy());
                        paymentRecord.setSubmitPerson(applyDto.getSubmitName());
                        paymentRecord.setPayNum(payNum);
                        paymentRecord.setAmount(amount);
                        paymentRecord.setWriteOffAmount(BigDecimal.ZERO);
                        if (!StringUtils.isEmpty(pamentDate)) {
                            paymentRecord.setPaymentDate(DateUtil.parseDate(pamentDate));
                        }
                        paymentRecord.setPaymentStatus(PaymentRecordStatus.NOT.code());
                        paymentRecord.setDeletedFlag(0);
                        paymentRecordMapper.insertSelective(paymentRecord);
                        flag = true;
                    }
                    logger.info("新增或更新后: 付款计划ID:{},付款申请ID:{},付款申请编号:{},付款申请累计实际付款金额:{},查询的付款记录为:{},",
                            applyDto.getPaymentPlanId(), applyDto.getId(), payNum, totalPayAmount,
                            JSON.toJSONString(list));
                }
            }
        }
        // 更新实际付款金额
        paymentPlanExtMapper.updateActualAmountById(applyDto.getPaymentPlanId());
        if (ListUtils.isEmpty(returnItemList)) {
            return;
        }
        PaymentApply updatePaymentApply = new PaymentApply();
        updatePaymentApply.setId(applyDto.getId());
        //判断是否部分支付
        if (totalPayAmount.compareTo(BigDecimal.valueOf(0.00)) > 0) {
            updatePaymentApply.setGcebStatus(PaymentApplyGcebErpStatus.PARTIAL_PAYMENT.getCode());//资金状态部分支付
        } else {
            updatePaymentApply.setGcebStatus(PaymentApplyGcebErpStatus.IN_PAYMENT.getCode());//资金状态支付中
        }
        //判断有没有支付完成(释放金额+已支付=申请支付)
        if (applyDto.getTaxPayIncludedPrice().compareTo(totalPayAmount) == 0) {
            updatePaymentApply.setGcebStatus(PaymentApplyGcebErpStatus.SUCCESS.getCode());//资金状态支付完成
        }
        //已支付金额 + 取消金额 = 申请支付金额 则状态为支付完成
        if (Objects.nonNull(applyDto.getTotalCancelAmount()) && totalPayAmount.add(applyDto.getTotalCancelAmount()).compareTo(applyDto.getTaxPayIncludedPrice()) == 0) {
            updatePaymentApply.setGcebStatus(PaymentApplyGcebErpStatus.SUCCESS.getCode());//资金状态支付完成
        }

        updatePaymentApply.setReallyPayIncludedPrice(totalPayAmount);
        updatePaymentApply.setEsbStatus(PaymentApplyEsbStatus.SUCCESS.getCode());
        logger.info("查询预付款付款申请支付结果：付款申请id【{}】，gceb同步状态【{}】，付款申请对象信息【{}】", applyDto.getId(), updatePaymentApply.getGcebStatus(),
                JSON.toJSONString(applyDto));
        paymentApplyMapper.updateByPrimaryKeySelective(updatePaymentApply);

        // 推送GSC付款申请支付状态
        if (Objects.equals(updatePaymentApply.getSourceSystemName(), PaymentApplySourceNameEnum.GSC.getName())) {
            gscPaymentAndInvoiceStatusPushService.pushPaymentAndInvoiceStatus(GSCPaymentAndInvoiceStatusPushEnum.PAYMENT_APPLY_GCEB_STATUS_PUSH, updatePaymentApply.getId());
        }

        //更新付款计划支付状态
        if (applyDto.getPaymentPlanId() != null) {
            updatePaymentPlanStatus(applyDto.getPaymentPlanId());
        }

        if (flag) {
            // 付款申请 部分支付/支付完成 邮件提醒
            emailPaymentApply(applyDto.getId());
        }
        logger.info("ERP_PIFACECODE_027 END............");
    }

    /**
     * erp查询发票付款状态
     *
     * @param paramMap
     */
    @Override
    public void getApplyStatusFromErp(Map<String, String> paramMap, PaymentApplyDto applyDto) {
        //调erp接口
        logger.info("ERP_PIFACECODE_042 start............");
//        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_042, paramMap);
        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_042, paramMap);
        logger.info("erp查询发票付款状态信息为:{}", JSON.toJSONString(returnItemList));
        BigDecimal totalReleaseAmount = BigDecimal.valueOf(0.00);//付款申请累计取消付款金额(含税)
        BigDecimal totalPayAmount = BigDecimal.valueOf(0.00);//付款申请实际付款金额(含税)
        Boolean flag = false;//支付标识
        PaymentApply updatePaymentApply = new PaymentApply();
        updatePaymentApply.setId(applyDto.getId());
        PaymentInvoice updatePaymentInvoice = null;
        PaymentRecord updatePaymentRecord = null;
        //应付发票剩余金额需要更新的集合
        Map<Long, BigDecimal> invoiceSurplusAmountUpdateMap = new HashMap<>();
        for (SdpTradeResultResponseEleDto item : returnItemList) {
            String payPlanNum = item.getC1();//付款编号
            if (!StringUtils.isEmpty(payPlanNum) && payPlanNum.equals(applyDto.getPaymentApplyCode())) {
                String statusLockupCode = item.getC16();//付款状态
                String invoiceId = item.getC8();//发票ID
                String invoiceNum = item.getC9();//发票编号
                BigDecimal lockedAmt = PamStringUtil.toBigDecimal(item.getC11());//发票冻结金额
                BigDecimal applyAmt = PamStringUtil.toBigDecimal(item.getC13());//申请金额
                String currency = item.getC18();//币种
                //和付款申请币种不一样的付款记录忽略掉
                if (!Objects.equals(currency, applyDto.getCurrency())) {
                    //根据付款申请单id和ap发票编号查询付款记录，如果存在，将其逻辑删除掉，修正数据
                    PaymentRecord paymentRecord = paymentRecordService.getByApplyIdAndInvoiceCode(applyDto.getId(), invoiceNum);
                    if (paymentRecord != null) {
                        paymentRecord.setDeletedFlag(DeletedFlagEnum.INVALID.code());
                        paymentRecordMapper.updateByPrimaryKeySelective(paymentRecord);
                    }
                    continue;
                }

                List<PaymentInvoiceDto> paymentInvoices = applyDto.getPaymentInvoices();
                //当前返回行信息关联ap发票
                PaymentInvoiceDto invoice = null;
                if (StringUtil.isNotNull(invoiceId) && StringUtil.isNotNull(invoiceNum)) {
                    for (PaymentInvoiceDto invoiceDto : paymentInvoices) {
                        if (invoiceId.equals(invoiceDto.getErpInvoiceCode()) && invoiceNum.equals(invoiceDto.getApInvoiceCode())) {
                            invoice = invoiceDto;
                        }
                    }
                }
                if (invoice != null) {
                    //如果锁定金额为0或者空则释放发票状态,未占用
                    if (lockedAmt == null || (lockedAmt != null && lockedAmt.compareTo(BigDecimal.ZERO) == 0)) {
                        updatePaymentInvoice = new PaymentInvoice();
                        updatePaymentInvoice.setId(invoice.getId());
                        updatePaymentInvoice.setAvailable(1);//是否可用(1-未占用 0-已占用)
                        paymentInvoiceMapper.updateByPrimaryKeySelective(updatePaymentInvoice);
                    }
                }
                //CANCELED已取消 REJECTED已拒绝(付款中的意思并非取消付款) INPAYMENT已提交付款
                if ("REJECTED".equals(statusLockupCode)) {
                    updatePaymentApply.setErpStatus("6");//ERP状态已拒绝

                } else if ("CANCELED".equals(statusLockupCode)) {
                    updatePaymentApply.setErpStatus("2");//ERP状态取消付款
                    totalReleaseAmount = totalReleaseAmount.add(applyAmt);//取消金额
                    //释放ap发票金额
                    if (invoice != null) {
                        invoiceSurplusAmountUpdateMap.put(invoice.getId(), applyAmt);
                    }
                } else if ("INPAYMENT".equals(statusLockupCode)) {
                    BigDecimal payedAmount = PamStringUtil.toBigDecimal(item.getC14());//实际支付金额
                    BigDecimal releasedAmount = PamStringUtil.toBigDecimal(item.getC15());//释放金额
                    BigDecimal invoiceBal = PamStringUtil.toBigDecimal(item.getC10());//发票余额
                    if (null != invoiceBal && invoice != null) {
                        //更新发票实际已付款金额
                        updatePaymentInvoice = new PaymentInvoice();
                        updatePaymentInvoice.setId(invoice.getId());
                        updatePaymentInvoice.setTotalPayIncludedPrice(invoice.getTotalInvoiceIncludedPrice().subtract(invoiceBal));
                        paymentInvoiceMapper.updateByPrimaryKeySelective(updatePaymentInvoice);
                    }
                    if (null != releasedAmount && releasedAmount.compareTo(BigDecimal.valueOf(0.00)) > 0) {
                        totalReleaseAmount = totalReleaseAmount.add(releasedAmount);
                        //释放金额不为空，则资金取消
                        updatePaymentApply.setErpStatus("3");//ERP状态部分支付
                        //释放ap发票金额
                        if (invoice != null) {
                            //加入AP发票剩余金额更新集合
                            invoiceSurplusAmountUpdateMap.put(invoice.getId(), releasedAmount);
                        }
                    }
                    if (null != payedAmount && payedAmount.compareTo(BigDecimal.valueOf(0.00)) > 0) {
                        totalPayAmount = totalPayAmount.add(payedAmount);
                        //根据付款申请单id和ap发票编号查询付款记录
                        PaymentRecord paymentRecord = paymentRecordService.getByApplyIdAndInvoiceCode(applyDto.getId(), invoiceNum);
                        logger.info("付款计划ID:{},付款申请ID:{},ap发票编号:{},付款申请累计实际付款金额:{},查询的付款记录为:{},",
                                applyDto.getPaymentPlanId(), applyDto.getId(), invoiceNum, totalPayAmount,
                                JSON.toJSONString(paymentRecord));
                        if (paymentRecord == null) {
                            paymentRecord = new PaymentRecord();
                            paymentRecord.setPaymentApplyId(applyDto.getId());
                            paymentRecord.setErpInvoiceCode(invoiceId);
                            paymentRecord.setApInvoiceCode(invoiceNum);
                            paymentRecord.setPayType(applyDto.getIsCharge() == 1 ? "预付款" : "发票付款");
                            paymentRecord.setPenaltyAmount(BigDecimal.valueOf(0.00));//TODO 罚扣
                            paymentRecord.setHappenDate(new Date());
                            paymentRecord.setSubmitBy(applyDto.getSubmitBy());
                            paymentRecord.setSubmitPerson(applyDto.getSubmitName());
                            paymentRecord.setPayNum(applyDto.getPaymentApplyCode());
                            paymentRecord.setAmount(payedAmount);
                            paymentRecord.setPaymentDate(new Date());//TODO 实际付款日期
                            paymentRecord.setDeletedFlag(0);
                            paymentRecordService.add(paymentRecord);
                            flag = true;
                        } else {
                            updatePaymentRecord = new PaymentRecord();
                            updatePaymentRecord.setId(paymentRecord.getId());
                            updatePaymentRecord.setErpInvoiceCode(invoiceId);
                            updatePaymentRecord.setPayType(applyDto.getIsCharge() == 1 ? "预付款" : "发票付款");
                            updatePaymentRecord.setPenaltyAmount(BigDecimal.valueOf(0.00));//TODO 罚扣
                            updatePaymentRecord.setHappenDate(new Date());
                            updatePaymentRecord.setSubmitBy(applyDto.getSubmitBy());
                            updatePaymentRecord.setSubmitPerson(applyDto.getSubmitName());
                            updatePaymentRecord.setPayNum(applyDto.getPaymentApplyCode());
                            updatePaymentRecord.setAmount(payedAmount);
                            updatePaymentRecord.setPaymentDate(new Date());//TODO 实际付款日期
                            paymentRecordMapper.updateByPrimaryKeySelective(updatePaymentRecord);
                        }
                        logger.info("新增或更新后: 付款计划ID:{},付款申请ID:{},ap发票编号:{},付款申请累计实际付款金额:{},查询的付款记录为:{},",
                                applyDto.getPaymentPlanId(), applyDto.getId(), invoiceNum, totalPayAmount,
                                JSON.toJSONString(paymentRecord));
                    }
                }
            }
        }
        // 更新实际付款金额
        paymentPlanExtMapper.updateActualAmountById(applyDto.getPaymentPlanId());
        if (ListUtils.isEmpty(returnItemList)) {
            return;
        }
        //判断是否部分支付
        if (totalPayAmount.compareTo(BigDecimal.valueOf(0.00)) > 0) {
            updatePaymentApply.setErpStatus("3");//ERP状态部分支付
        } else {
            updatePaymentApply.setErpStatus("4");//ERP状态付款中
        }

        //判断有没有支付完成(释放金额+已支付=申请支付)
        if (applyDto.getTaxPayIncludedPrice().compareTo(totalPayAmount.add(totalReleaseAmount)) == 0) {
            updatePaymentApply.setErpStatus("1");//ERP状态支付完成

        }
        //判断有没有取消支付(释放金额=申请支付)
        if (applyDto.getTaxPayIncludedPrice().compareTo(totalReleaseAmount) == 0) {
            updatePaymentApply.setErpStatus("2");//ERP状态取消支付
        }

        //已支付金额 + 取消金额 = 申请支付金额 则状态为支付完成
        if (Objects.nonNull(applyDto.getTotalCancelAmount()) && totalPayAmount.add(applyDto.getTotalCancelAmount()).compareTo(applyDto.getTaxPayIncludedPrice()) == 0) {
            updatePaymentApply.setErpStatus("1");//资金状态支付完成
        }

        updatePaymentApply.setReallyPayIncludedPrice(totalPayAmount);
        updatePaymentApply.setTotalCancelAmount(totalReleaseAmount);
        updatePaymentApply.setEsbStatus(PaymentApplyEsbStatus.SUCCESS.getCode());
        logger.info("查询非预付款付款申请支付结果：付款申请id【{}】，erp同步状态【{}】，付款申请对象信息【{}】", applyDto.getId(), updatePaymentApply.getErpStatus(),
                JSON.toJSONString(applyDto));
        //更新ap发票剩余金额
        updateInvoiceSurplusAmounts(updatePaymentApply, invoiceSurplusAmountUpdateMap);

        paymentApplyMapper.updateByPrimaryKeySelective(updatePaymentApply);

        //更新付款计划支付状态
        if (applyDto.getPaymentPlanId() != null) {
            updatePaymentPlanStatus(applyDto.getPaymentPlanId());
        }

        if (flag) {
            // 付款申请 部分支付/支付完成 邮件提醒
            emailPaymentApply(applyDto.getId());
        }
        logger.info("ERP_PIFACECODE_042 END............");
    }

    /**
     * 更新AP发票剩余金额
     *
     * @param invoiceSurplusAmountUpdateMap
     */
    public void updateInvoiceSurplusAmounts(PaymentApply updatePaymentApply, Map<Long, BigDecimal> invoiceSurplusAmountUpdateMap) {
        logger.info("更新AP发票剩余金额,剩余金额集合:{},ERP状态：{}", JSON.toJSONString(invoiceSurplusAmountUpdateMap), updatePaymentApply.getErpStatus());
        String erpStatus = updatePaymentApply.getErpStatus();
        //支付完成或取消支付才更新剩余金额
        if (Objects.equals(erpStatus, "1") || Objects.equals(erpStatus, "2")) {
            if (Objects.nonNull(invoiceSurplusAmountUpdateMap) && !invoiceSurplusAmountUpdateMap.isEmpty()) {
                invoiceSurplusAmountUpdateMap.forEach((invoiceId, releasedAmount) -> {
                    try {
                        logger.info("更新AP发票剩余金额,AP发票ID:{},释放金额:{}", invoiceId, releasedAmount);
                        paymentInvoiceExtMapper.updateSurplusAmountById(invoiceId, releasedAmount);
                    } catch (Exception e) {
                        logger.error("更新AP发票剩余金额失败,AP发票ID:{},释放金额:{}", invoiceId, releasedAmount, e);
                    }
                });
            }
        }
    }

    /**
     * 更新付款计划支付状态
     *
     * @param paymentPlanId
     */
    @Override
    public void updatePaymentPlanStatus(Long paymentPlanId) {
        PaymentPlan updatePaymentPlan = new PaymentPlan();
        updatePaymentPlan.setId(paymentPlanId);
        List<PaymentPlanDTO> paymentPlanDTOList = paymentPlanExtMapper.calculateSurplusAmount(paymentPlanId);
        if (ListUtil.isPresent(paymentPlanDTOList)) {
            PaymentPlanDTO paymentPlanInfo = paymentPlanDTOList.get(0);
            //计划付款金额（含税）
            BigDecimal amount = paymentPlanInfo.getAmount();
            //剩余待付款金额（含税）
            BigDecimal surplusAmount = Optional.ofNullable(paymentPlanInfo.getSurplusAmount()).orElse(BigDecimal.ZERO);
            surplusAmount = surplusAmount.subtract(paymentPlanInfo.getAllocationPunishmentAmountWithTax());
            if (amount != null) {
                if (surplusAmount.compareTo(BigDecimal.ZERO) == 0) {
                    updatePaymentPlan.setStatus(PaymentPlanStatus.PAYED.getCode());
                } else if (surplusAmount.compareTo(amount) == 0) {
                    updatePaymentPlan.setStatus(PaymentPlanStatus.UNPAYED.getCode());
                } else if (surplusAmount.compareTo(amount) < 0 && surplusAmount.compareTo(BigDecimal.ZERO) > 0) {
                    updatePaymentPlan.setStatus(PaymentPlanStatus.PAYING.getCode());
                } else {
                    //处理超付的情况, 剩余待付款金额（含税）减去分配罚扣金额(含税) 如果小于0，则超付
                    logger.info("付款计划存在超付，paymentPlanId：{},剩余待付款金额（含税）:{},分配罚扣金额(含税):{}", paymentPlanId, surplusAmount, paymentPlanInfo.getAllocationPunishmentAmountWithTax());
                    updatePaymentPlan.setStatus(PaymentPlanStatus.OVERPAY.getCode());
                }
                paymentPlanMapper.updateByPrimaryKeySelective(updatePaymentPlan);


            }
        }
    }

    @Override
    public void updatePaymentPlanStatusAndActualAmount(Long paymentPlanId) {
        PaymentPlan updatePaymentPlan = new PaymentPlan();
        updatePaymentPlan.setId(paymentPlanId);
        List<PaymentPlanDTO> paymentPlanDTOList = paymentPlanExtMapper.calculateSurplusAmount(paymentPlanId);
        if (ListUtil.isPresent(paymentPlanDTOList)) {
            PaymentPlanDTO paymentPlanInfo = paymentPlanDTOList.get(0);
            //计划付款金额（含税）
            BigDecimal amount = paymentPlanInfo.getAmount();
            //剩余待付款金额（含税）
            BigDecimal surplusAmount = Optional.ofNullable(paymentPlanInfo.getSurplusAmount()).orElse(BigDecimal.ZERO);
            surplusAmount = surplusAmount.subtract(paymentPlanInfo.getAllocationPunishmentAmountWithTax());
            if (amount != null) {
                //更新 实际付款金额
                updatePaymentPlan.setActualAmount(paymentPlanInfo.getActualAmount());
                if (surplusAmount.compareTo(BigDecimal.ZERO) == 0) {
                    updatePaymentPlan.setStatus(PaymentPlanStatus.PAYED.getCode());
                } else if (surplusAmount.compareTo(amount) == 0) {
                    updatePaymentPlan.setStatus(PaymentPlanStatus.UNPAYED.getCode());
                } else if (surplusAmount.compareTo(amount) < 0 && surplusAmount.compareTo(BigDecimal.ZERO) > 0) {
                    updatePaymentPlan.setStatus(PaymentPlanStatus.PAYING.getCode());
                } else {
                    //处理超付的情况, 剩余待付款金额（含税）减去分配罚扣金额(含税) 如果小于0，则超付
                    logger.info("付款计划存在超付，paymentPlanId：{},剩余待付款金额（含税）:{},分配罚扣金额(含税):{}", paymentPlanId, surplusAmount, paymentPlanInfo.getAllocationPunishmentAmountWithTax());
                    updatePaymentPlan.setStatus(PaymentPlanStatus.OVERPAY.getCode());
                }
                paymentPlanMapper.updateByPrimaryKeySelective(updatePaymentPlan);
            }
        }
    }

    /**
     * 获取应付付款明细
     *
     * @param applyDto
     */
    @Override
    public void getApplyInfo(PaymentApplyDto applyDto, String lastUpdateDate) {
        //同步成功的付款申请才可以查询付款明细
        if (!"2".equals(applyDto.getEsbStatus())) {
            return;
        }
        String erpStatus = applyDto.getErpStatus();
        // erp同步状态(0-支付中,1-支付完成,2-取消支付,3-部分支付,4-付款中,5-异常完结,6-已拒绝)
        if ("0".equals(erpStatus) || "3".equals(erpStatus)
                || "4".equals(erpStatus) || "6".equals(erpStatus)) {
            //发票付款查询申请单付款明细
            Map<String, String> paramMap = new HashMap();
//            paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(applyDto.getOuId()));
            paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(applyDto.getOuId()));
            //查询时间取付款写入的时间
//            paramMap.put(EsbConstant.ERP_IP_P02, DateUtil.format(applyDto.getSubmitDate()));
            paramMap.put(EsbConstant.ERP_SDP_P02, DateUtil.format(applyDto.getSubmitDate()));
            //付款申请编号
//            paramMap.put(EsbConstant.ERP_IP_P03, applyDto.getPaymentApplyCode());
            paramMap.put(EsbConstant.ERP_SDP_P03, applyDto.getPaymentApplyCode());
            getApplyStatusFromErp(paramMap, applyDto);
        }
    }

    /**
     * 定时处理付款申请单(包括发票查询/发票写入/付款写入/付款查询)
     *
     * @param lastUpdateDate
     */
    @Override
    public void dealPaymentApplyAtTime(String lastUpdateDate, String paymentApplyCode) {
        //1.查询所有审批通过的付款申请
        PaymentApplyQuery query = new PaymentApplyQuery();
        query.setAuditStatus(AuditStatus.EFFECTIVE.getCode());
        query.setPaymentApplyCode(paymentApplyCode);
        List<PaymentApplyDto> paymentApplyDtos = list(query);
        for (PaymentApplyDto applyDto : paymentApplyDtos) {
            Integer isCharge = applyDto.getIsCharge();
            //2.判断是否预付款(1:是 0否)
            if (isCharge != null && isCharge == 0) {
                String esbStatus = applyDto.getEsbStatus();
                //3.判断接口发送状态 0-待同步 1-同步异常 2-同步成功
                if ("2".equals(esbStatus)) {
                    //同步成功的付款申请才可以查询付款明细
                    getApplyInfo(applyDto, lastUpdateDate);
                } else if ("0".equals(esbStatus)) {
                    //待同步写入付款申请到ERP
                    pushApplyToErp(applyDto, lastUpdateDate);
                }
            } else {
                String gcebStatus = applyDto.getGcebStatus();
                //gceb同步状态(0-同步中,1-支付完成,2-取消支付,3-部分支付,4-付款中,5-异常完结)
                if ("3".equals(gcebStatus) || "4".equals(gcebStatus) || "0".equals(gcebStatus)) {
                    Map<String, String> paramMap = new HashMap();
//                    paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(applyDto.getOuId()));
                    paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(applyDto.getOuId()));
                    //查询时间取付款写入的时间
//                    paramMap.put(EsbConstant.ERP_IP_P02, DateUtil.format(applyDto.getSubmitDate()));
                    paramMap.put(EsbConstant.ERP_SDP_P02, DateUtil.format(applyDto.getSubmitDate()));
                    //付款申请编号(来源单据号)
//                    paramMap.put(EsbConstant.ERP_IP_P03, applyDto.getPaymentApplyCode());
                    paramMap.put(EsbConstant.ERP_SDP_P03, applyDto.getPaymentApplyCode());
                    getApplyInfoFromErp(applyDto, paramMap);
                }
            }
        }
    }

    @Override
    public String paymentWriteOff(Long paymentRecordId) {
        PaymentRecordQuery paymentRecordQuery = new PaymentRecordQuery();
        paymentRecordQuery.setId(paymentRecordId);
        List<String> paymentStatusList = new ArrayList<>();
        //'核销状态(-2撤销失败/-1核销失败/0未核销/1核销中/2撤销中/3部分核销/4已核销)'
        paymentStatusList.add(PaymentRecordStatus.NOT.code());
        paymentStatusList.add(PaymentRecordStatus.PART.code());
        paymentRecordQuery.setPaymentStatusList(paymentStatusList);
        List<PaymentRecord> listPaymentRecord = paymentRecordService.selectList(paymentRecordQuery);
        Guard.notNullOrEmpty(listPaymentRecord, "未核销和部分核销的实际付款记录不存在");

        for (PaymentRecord paymentRecord : listPaymentRecord) {
            PaymentApply paymentApply = paymentApplyMapper.selectByPrimaryKey(paymentRecord.getPaymentApplyId());
            logger.info("付款申请单id：{}对应的付款申请为：[{}]", paymentRecord.getPaymentApplyId(), JsonUtils.toString(paymentApply));
            if (paymentApply == null || !"2".equals(paymentApply.getEsbStatus()) || !"1".equals(paymentApply.getGcebStatus())) {
                //完成支付的预付款才可以核销
                logger.info("付款申请单id：{}对应的付款申请不是完成支付的预付款", paymentRecord.getPaymentApplyId());
                continue;
            }

            BigDecimal amount = paymentRecord.getAmount();//付款金额(含税)
            BigDecimal writeOffAmount = paymentRecord.getWriteOffAmount();//累计核销金额（含在途）
            if (writeOffAmount == null) {
                writeOffAmount = new BigDecimal(0);//初始化累计核销金额，怕计算空值错误
            }
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                logger.info("付款申请单id：{}对应的的付款金额(含税)为空或者0", paymentRecord.getPaymentApplyId());
                continue;
            }

            BigDecimal ableWriteOffAmount = amount.subtract(writeOffAmount);//可核销金额

            if (ableWriteOffAmount.compareTo(BigDecimal.ZERO) <= 0) {
                logger.info("付款申请单id：{}对应的的可核销金额小于或者等于0", paymentRecord.getPaymentApplyId());
                continue;
            }

            Long purchaseContractId = paymentRecord.getPurchaseContractId();//采购合同ID
            PaymentInvoiceDto query = new PaymentInvoiceDto();
            query.setPurchaseContractId(purchaseContractId);
            query.setErpStatus(1);
            query.setAvailable(1);//是否可用(1-可用 0-不可用)
            query.setSurplusAmountMoreThan("1");

            List<PaymentInvoiceDto> list = paymentInvoiceService.list(query);
            if (CollectionUtils.isEmpty(list)) {
                logger.info("采购合同id：{}对应的发票信息不存在", purchaseContractId);
                continue;
            }
            for (PaymentInvoiceDto paymentInvoiceDto : list) {
                BigDecimal surplusAmount = paymentInvoiceDto.getSurplusAmount();//剩余可用金额

                BigDecimal xiaohao;//消耗金额
                //比较大小可以用 surplusAmount.compareTo(bigDecimal) 返回值 -1 小于 0 等于 1 大于
                if (surplusAmount.compareTo(ableWriteOffAmount) >= 0) {
                    xiaohao = ableWriteOffAmount;
                } else {
                    xiaohao = surplusAmount;
                }
                //核销金额（含在途）
                writeOffAmount = writeOffAmount.add(xiaohao);
                paymentRecord.setWriteOffAmount(writeOffAmount);

                Date date = setWriteOffDay(paymentApply.getOuId());
                if (Objects.isNull(date)) {
                    continue;
                }
                //更新ap发票可用余额
                surplusAmount = surplusAmount.subtract(xiaohao);
                paymentInvoiceDto.setSurplusAmount(surplusAmount);
                paymentInvoiceDto.setTotalPayIncludedPrice(Optional.ofNullable(paymentInvoiceDto.getTotalPayIncludedPrice()).orElse(BigDecimal.ZERO).add(xiaohao));
                paymentInvoiceDto.setUpdateAt(new Date());
                paymentInvoiceService.update(paymentInvoiceDto);

                PaymentWriteOffRecord writeOffRecord = new PaymentWriteOffRecord();
                writeOffRecord.setPaymentRecordId(paymentRecord.getId());
                writeOffRecord.setPaymentInvoiceId(paymentInvoiceDto.getId());
                writeOffRecord.setPaymentApplyId(paymentApply.getId());
                writeOffRecord.setPaymentApplyCode(paymentApply.getPaymentApplyCode());

                writeOffRecord.setAmount(xiaohao);
                writeOffRecord.setApInvoiceCode(paymentInvoiceDto.getApInvoiceCode());
                writeOffRecord.setHappenDate(new Date());
                writeOffRecord.setDeletedFlag(DeletedFlag.VALID.code());
                writeOffRecord.setStatus(PaymentWriteOffRecordStatus.SYNCHRONIZING.code());//同步中
                writeOffRecord.setHappenDate(date);
                paymentWriteOffRecordMapper.insert(writeOffRecord);
                //预付款核销记录写入
                HandleDispatcher.route(BusinessTypeEnums.PREPAY.getCode(), String.valueOf(writeOffRecord.getId()), null, null, false);

                ableWriteOffAmount = ableWriteOffAmount.subtract(xiaohao);
                if (ableWriteOffAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }

            }
            //核销状态(0未核销/3部分核销/4已核销)
//            paymentRecord.setPaymentStatus(PaymentRecordStatus.DOING.code());
            if (paymentRecord.getWriteOffAmount().compareTo(BigDecimal.ZERO) == 0) {
                paymentRecord.setPaymentStatus(PaymentRecordStatus.NOT.code());//未核销
            } else if (paymentRecord.getWriteOffAmount().compareTo(BigDecimal.ZERO) > 0 && paymentRecord.getWriteOffAmount().compareTo(paymentRecord.getAmount()) < 0) {
                paymentRecord.setPaymentStatus(PaymentRecordStatus.PART.code());//部分核销
            } else if (paymentRecord.getWriteOffAmount().compareTo(paymentRecord.getAmount()) == 0) {
                paymentRecord.setPaymentStatus(PaymentRecordStatus.DONE.code());//已核销
            }
            paymentRecord.setUpdateAt(new Date());
            paymentRecordMapper.updateByPrimaryKeySelective(paymentRecord);

            //更新预付款申请单核销状态
            paymentApply.setWriteOffStatus(PaymentApplyWriteOffStatus.PART.code());//部分核销
            //判断预付款是否完全核销
            if (isPrepayAllForApply(paymentApply)) {
                paymentApply.setWriteOffStatus(PaymentApplyWriteOffStatus.DONE.code());//已核销
            }
            paymentApplyService.update(paymentApply);
        }

        return "success";
    }

    /**
     * 判断预付款申请是否已经完全核销
     *
     * @param paymentApply
     * @return
     */
    public Boolean isPrepayAllForApply(PaymentApply paymentApply) {
        if (null == paymentApply) {
            return false;
        }
        if (!PaymentApplyGcebErpStatus.SUCCESS.getCode().equals(paymentApply.getGcebStatus())) {
            return false;
        }
        //根据预付款查询付款记录
        PaymentRecordExample recordExample = new PaymentRecordExample();
        recordExample.createCriteria().andDeletedFlagEqualTo(0).andPaymentApplyIdEqualTo(paymentApply.getId());
        List<PaymentRecord> recordList = paymentRecordMapper.selectByExample(recordExample);
        BigDecimal totalPrepayAmount = BigDecimal.valueOf(0.00);
        for (PaymentRecord record : recordList) {
            if (PaymentRecordStatus.DONE.equals(record.getPaymentStatus())) {
                totalPrepayAmount.add(record.getWriteOffAmount());
            }
        }
        return totalPrepayAmount.compareTo(paymentApply.getReallyPayIncludedPrice()) == 0;
    }

    private Date setWriteOffDay(Long ouId) {
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
        if (ListUtils.isEmpty(organizationRelList)) {
            throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
        }
        OrganizationRelDto organizationRelDto = organizationRelList.get(0);
        Long ledgerId = organizationRelDto.getLedgerId();
        List<GlPeriodDto> glPeriodDtoList = basedataExtService.getGlPeriod(ledgerId, GlPeriodType.PAYMENT_PERIOD.getCode().toString(),
                DateUtils.format(new Date(), "yyyy-MM"));
        List<GlPeriodDto> glPeriodOpenList = basedataExtService.getGlPeriod2(ledgerId, GlPeriodType.PAYMENT_PERIOD.getCode().toString(), "O");

        Date happenDate = null;
        GlPeriodDto glPeriodOpen = null;
        if (ListUtils.isNotEmpty(glPeriodOpenList)) {
            glPeriodOpenList.sort(Comparator.comparing(GlPeriodDto::getEndDate).reversed());
            glPeriodOpen = glPeriodOpenList.get(0);
        }
        if (ListUtils.isNotEmpty(glPeriodDtoList)) {
            GlPeriodDto dto = glPeriodDtoList.get(0);
            String status = dto.getClosingStatus();
            Date startDate = dto.getStartDate();
            Date endDate = dto.getEndDate();
            if ((new Date()).after(startDate) && (new Date()).before(DateUtils.addDay(endDate, 1)) && "O".equals(status)) {
                happenDate = DateUtils.parse(DateUtils.format(new Date(), "yyyy-MM-dd 00:00:00"));
            } else {
                happenDate = Optional.ofNullable(glPeriodOpen).map(GlPeriodDto::getEndDate).orElse(null);
            }
        }
        return happenDate;
    }

    public void saveApplyInvoiceDetailRel(PaymentApplyDto applyDto) {
        //发票申请行信息
        List<PaymentInvoiceDetailDto> paymentInvoiceDetailDtos = applyDto.getPaymentInvoiceDetails();
        if (ListUtils.isNotEmpty(paymentInvoiceDetailDtos)) {
            //判断明细行不能存在不一致的税率
            if (paymentInvoiceDetailDtos.stream().filter(p -> Objects.equals(p.getDeletedFlag(), Boolean.FALSE))
                    .map(PaymentInvoiceDetailDto::getTaxRate).distinct().count() > 1) {
                throw new BizException(Code.ERROR, "常规发票的税率不一致，请发票单独入账后再选用已入账发票付款");
            }

            PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(applyDto.getPurchaseContractId());
            Asserts.notNull(purchaseContract, ErrorCode.CTC_PURCHASE_CONTRACT_NOT_EXISTS);
            //本次入账税票的含税金额汇总
            BigDecimal taxIncludedPriceSum = paymentInvoiceDetailDtos.stream().filter(s -> Objects.equals(s.getDeletedFlag(), Boolean.FALSE)).map(PaymentInvoiceDetailDto::getTaxIncludedPrice).reduce(BigDecimal.ZERO, BigDecimalUtils::add);

            //按采购合同类型配置是否启用应付入账金额超合同金额校验
            paymentInvoiceService.checkAmountInvoiceOverContract(purchaseContract.getId(), purchaseContract.getAmount(), taxIncludedPriceSum);

            //判断录入发票金额是否充足
            BigDecimal totalInvoiceAmount = BigDecimal.valueOf(0.00);
            for (PaymentInvoiceDetailDto detailDto : paymentInvoiceDetailDtos) {
                totalInvoiceAmount = totalInvoiceAmount.add(detailDto.getTaxIncludedPrice());
            }
            if (totalInvoiceAmount.compareTo(applyDto.getTaxPayIncludedPrice()) < 0) {
                throw new BizException(ErrorCode.ERROR, "申请付款金额大于发票总额!");
            }

            List<Long> paymentInvoiceDetailIds = paymentInvoiceDetailDtos.stream().map(PaymentInvoiceDetailDto::getId)
                    .collect(Collectors.toList());
            PaymentInvoiceDetailExample detailExample = new PaymentInvoiceDetailExample();
            detailExample.createCriteria().andIdIn(paymentInvoiceDetailIds);
            Map<Long, String> invoiceStatusMap = paymentInvoiceDetailMapper.selectByExample(detailExample).stream()
                    .collect(Collectors.toMap(PaymentInvoiceDetail::getId, PaymentInvoiceDetail::getInvoiceStatus));

            PaymentApplyDetailRelExample example = new PaymentApplyDetailRelExample();
            example.createCriteria()
                    .andPaymentApplyIdEqualTo(applyDto.getId())
                    .andInvoiceDetailIdIn(paymentInvoiceDetailIds)
                    .andDeletedFlagEqualTo(0);
            Map<Long, PaymentApplyDetailRel> paymentApplyDetailRelMap = paymentApplyDetailRelMapper.selectByExample(example)
                    .stream().collect(Collectors.toMap(PaymentApplyDetailRel::getInvoiceDetailId, Function.identity()));

            for (PaymentInvoiceDetailDto detailDto : paymentInvoiceDetailDtos) {
                //判断该发票行是否已被其他付款申请引用
                PaymentApplyDetailRel paymentApplyDetailRel = paymentApplyDetailRelMap.get(detailDto.getId());
                if (InvoiceDetailStatus.QUOTE.getCode().equals(invoiceStatusMap.get(detailDto.getId())) && paymentApplyDetailRel == null) {
                    throw new BizException(ErrorCode.ERROR, "发票" + detailDto.getInvoiceDetailCode() + "状态不可以用!");
                }
                if (null == paymentApplyDetailRel) {
                    paymentApplyDetailRel = new PaymentApplyDetailRel();
                    paymentApplyDetailRel.setPaymentApplyId(applyDto.getId());
                    paymentApplyDetailRel.setInvoiceDetailId(detailDto.getId());
                    paymentApplyDetailRel.setDeletedFlag(0);
                    paymentApplyDetailRelService.save(paymentApplyDetailRel, SystemContext.getUserId());
                } else {
                    if (detailDto.getDeletedFlag()) {
                        //删除关联
                        paymentApplyDetailRelMapper.deleteByPrimaryKey(paymentApplyDetailRel.getId());
                    }
                }
                //保存发票引用状态
                detailDto.setInvoiceStatus(detailDto.getDeletedFlag() ? InvoiceDetailStatus.UNQUOTED.getCode() : InvoiceDetailStatus.QUOTE.getCode());
                punishmentService.updatePunishmentAccountStatus(detailDto,
                        detailDto.getDeletedFlag() ? PurchaseContractPunishmentAccountStatusEnum.NOT_ACCOUNTED.getCode() : PurchaseContractPunishmentAccountStatusEnum.ACCOUNTED.getCode());
                detailDto.setDeletedFlag(false);
            }
            paymentInvoiceDetailService.batchSave(paymentInvoiceDetailDtos, SystemContext.getUserId());
        }
    }

    public void saveApplyInvoiceRel(PaymentApplyDto applyDto) {
        //发票申请头信息(释放前)
        List<PaymentInvoiceDto> oldList = applyDto.getPaymentInvoices();
        //删除旧数据(释放ap发票占用金额)
        releaseApply(applyDto);
        //发票申请头信息(释放后)
        List<PaymentInvoiceDto> newList = new ArrayList<>();
        for (PaymentInvoiceDto invoiceDto : oldList) {
            //删除关联,前端传true,待优化 --xiedl
            if (!invoiceDto.getDeletedFlag()) {
                invoiceDto = paymentInvoiceService.getById(invoiceDto.getId());
                if (1 == invoiceDto.getErpStatus()) {//ap发票已验证
                    newList.add(invoiceDto);
                }
            }
        }
        //判断AP发票余额是否充足
        Boolean isEnough = isEnoughForAp(newList, applyDto.getTaxPayIncludedPrice());
        if (!isEnough) {
            throw new BizException(ErrorCode.CTC_PAYMENT_INVOICE_CODE_EXCESS);
        }
        BigDecimal totalPay = applyDto.getTaxPayIncludedPrice();//发票剩余待支付金额(初始化为本次支付金额)
        //分配每张AP票本次付款申请占用金额
        for (PaymentInvoiceDto paymentInvoiceDto : newList) {
            if (totalPay.compareTo(BigDecimal.valueOf(0.00)) < 1) {
                //剩余支付金额为0,无需过多发票
                break;
            }
            PaymentApplyInvoiceRel paymentApplyInvoiceRel = new PaymentApplyInvoiceRel();
            paymentApplyInvoiceRel.setDeletedFlag(0);
            paymentApplyInvoiceRel.setPaymentApplyId(applyDto.getId());
            paymentApplyInvoiceRel.setPaymentInvoiceId(paymentInvoiceDto.getId());
            //该AP票用于本次付款申请的金额
            if (totalPay.compareTo(paymentInvoiceDto.getSurplusAmount()) >= 0) {
                //剩余待支付金额大于等于发票余额
                paymentApplyInvoiceRel.setAmount(paymentInvoiceDto.getSurplusAmount());
            } else {
                //发票余额大于剩余待支付金额
                paymentApplyInvoiceRel.setAmount(totalPay);
            }
            totalPay = totalPay.subtract(paymentApplyInvoiceRel.getAmount());
            paymentApplyInvoiceRelService.save(paymentApplyInvoiceRel, SystemContext.getUserId());
            //减少ap发票的可用余额
            paymentInvoiceDto.setSurplusAmount(paymentInvoiceDto.getSurplusAmount().subtract(paymentApplyInvoiceRel.getAmount()));
            if (paymentInvoiceDto.getPaymentApplyId() == null) {
                paymentInvoiceDto.setPaymentApplyId(applyDto.getId());
                paymentInvoiceDto.setPaymentApplyCode(applyDto.getPaymentApplyCode());
            }
            paymentInvoiceService.save(paymentInvoiceDto, SystemContext.getUserId());
        }
    }

    public String deme(String username) {
        TempMsgPush msg = new TempMsgPush();
        msg.setTitle("工时填报");
        msg.setSummary("您有未填工时，请尽快填报");
        msg.setShareRange(0);
        msg.setOpenType(3);
        msg.setCreateTime(DateUtils.format(new Date()));
        JSONObject object = new JSONObject();

        //统计本周所有已填报工时
        Map<String, Date> applyDateBetween = DateUtil.getWeekDate();

        String dateStr = DateUtil.format(applyDateBetween.get("date1"), DateUtil.DATE_PATTERN) + ",";
        dateStr += DateUtil.format(applyDateBetween.get("date2"), DateUtil.DATE_PATTERN) + ",";
        dateStr += DateUtil.format(applyDateBetween.get("date3"), DateUtil.DATE_PATTERN) + ",";
        dateStr += DateUtil.format(applyDateBetween.get("date4"), DateUtil.DATE_PATTERN) + ",";
        dateStr += DateUtil.format(applyDateBetween.get("date5"), DateUtil.DATE_PATTERN);
        object.put("dateStr", dateStr);
        msg.setExtras(object.toString());
        msg.setMc_widget_identifier(Constants.MC_WIDGET_IDENTIFIER_MOBILE);
        msg.setBatchToUsers(username);
        msg.setJumpUrl(Constants.SUBMIT_JUMP_URL);
        return meiXinPushMsgService.sendTempMsg(msg);
    }

    @Override
    public ResponseMap getPaymentApplyApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();  //付款信息
        List<Map<String, String>> detailMapList = new ArrayList<>(); //罚扣信息
        Map<Integer, String> milePostStatusMap = new HashMap<>();
        List<AduitAtta> fileList = new ArrayList<>();  //附件信息
        milePostStatusMap.put(0, "进行中");
        milePostStatusMap.put(1, "评审中");
        milePostStatusMap.put(2, "评审通过");
        milePostStatusMap.put(3, "驳回");
        milePostStatusMap.put(4, "废弃");
        PaymentApplyDto result = null;
        if (id == null || id == 0) {
            throw new BizException(ErrorCode.ID_NOT_NULL);
        }
        PaymentApplyQuery query = new PaymentApplyQuery();
        query.setId(id);
        List<PaymentApplyDto> list = paymentApplyExtMapper.selectPage(query);
        if (list == null || list.size() == 0) {
            throw new BizException(ErrorCode.CTC_PAYMENT_APPLY_CODE_EXIST);
        }
        result = list.get(0);
        if (result != null) {
            List<CtcAttachmentDto> attachmentRelations =
                    ctcAttachmentService.selectListByModuleAndModuleId(CtcAttachmentModule.PAYMENT_APPLY.code(), result.getId());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(attachmentRelations)) {
                for (CtcAttachmentDto attachmentRelation : attachmentRelations) {
                    AduitAtta aduitAtta = new AduitAtta();
                    aduitAtta.setFdId(attachmentRelation.getAttachId().toString());
                    aduitAtta.setFileName(attachmentRelation.getAttachName() == null ? " " : attachmentRelation.getAttachName());
                    aduitAtta.setFileSize(attachmentRelation.getFileSize() == null ? " " : attachmentRelation.getFileSize().toString());
                    fileList.add(aduitAtta);
                }
            }
            //付款计划id
            Long paymentPlanId = result.getPaymentPlanId();
            if (paymentPlanId != null) {
                //对应的付款计划
                PaymentPlanDTO paymentPlanDTO = paymentPlanService.findPaymentPlanById(paymentPlanId);
                if (paymentPlanDTO != null) {
                    Long milestoneId = paymentPlanDTO.getMilestoneId();//关联里程碑ID
                    if (milestoneId != null) {
                        ProjectMilepostDto projectMilepostDto = projectMilepostService.getById(milestoneId);
                        result.setProjectMilepostDto(projectMilepostDto);  //设置对应的里程碑
                    }
                }
                //累计罚扣款（含税）-来源于付款申请关联的计划")
                BigDecimal totalpenaltyAmount = paymentApplyExtMapper.totalpenaltyAmount(paymentPlanId);
                result.setTotalpenaltyAmount(totalpenaltyAmount);
            }

            Integer isCharge = result.getIsCharge();
            if (isCharge == 0) {//是否预付款(1:是 0否)
                //查询罚扣信息
                PaymentPenaltyProfitDto penaltyProfitQuery = new PaymentPenaltyProfitDto();
                penaltyProfitQuery.setPaymentApplyId(id);
                penaltyProfitQuery.setDeletedFlag(0);
                result.setPaymentPenaltyProfits(paymentPenaltyProfitService.selectListWithDetail(penaltyProfitQuery));
            }

            headMap.put("vendorName", result.getVendorName()); //供应商名称
            headMap.put("isCharge", isCharge == 0 ? "否" : "是");
            headMap.put("purchaseContractName", result.getPurchaseContractName()); //采购合同名称
            headMap.put("projectCode", result.getProjectCode() == null ? "" : result.getProjectCode()); // 项目编号
            // 计划付款金额（含税）
            headMap.put("taxPlanIncludedPrice", result.getTaxPlanIncludedPrice() == null ? String.valueOf(BigDecimal.ZERO) :
                    String.valueOf(result.getTaxPlanIncludedPrice()));
            // 罚扣金额（含税）
            headMap.put("totalpenaltyAmount", result.getTotalpenaltyAmount() == null ? String.valueOf(BigDecimal.ZERO) :
                    String.valueOf(result.getTotalpenaltyAmount()));
            // 申请付款金额（含税）
            headMap.put("taxPayIncludedPrice", result.getTaxPayIncludedPrice() == null ? String.valueOf(BigDecimal.ZERO) :
                    String.valueOf(result.getTaxPayIncludedPrice()));
            headMap.put("currency", result.getCurrency());
            headMap.put("paymentMethodName", result.getPaymentMethodName()); //付款方式
            if (result.getProjectMilepostDto() != null) {
                // 关联里程碑
                headMap.put("projectMilePostName", result.getProjectMilepostDto().getName() == null ? "" : result.getProjectMilepostDto().getName());
                // 里程碑状态:0进行中,1评审中,2评审通过,3驳回,4废弃
                headMap.put("projectMilePostStatus", milePostStatusMap.get(result.getProjectMilepostDto().getStatus()));
            } else {
                headMap.put("projectMilePostName", " ");
                headMap.put("projectMilePostStatus", " ");
            }
            // 项目回款比例
            if (result.getProjectId() != null) {
                List<ProjectContractRs> projectContractRsList = new ArrayList<>();
                projectContractRsList = projectContractRsService.selectByProjectId(result.getProjectId());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(projectContractRsList)) {
                    Contract contract = contractMapper.selectByPrimaryKey(projectContractRsList.get(0).getContractId());
                    // 销售子合同实际回款比例
                    BigDecimal receiptRate = contractHelper.calculateContractReceiptRate(contract);
                    String receiptRateStr = receiptRate == null ? "0.00" : String.valueOf(receiptRate);
                    headMap.put("receiptRate", receiptRateStr + "%");
                } else {
                    headMap.put("receiptRate", " ");
                }
            }
            // 付款条件
            headMap.put("requirement", result.getRequirement() == null ? "" : result.getRequirement());
            headMap.put("remark", result.getRemark() == null ? "" : result.getRemark());
            headMap.put("ouName", result.getOuName());
            if (result.getPaymentPenaltyProfits() != null) {
                List<PaymentPenaltyProfitDto> paymentPenaltyProfitDtos = result.getPaymentPenaltyProfits();
                Map<String, String> detailMap = new HashMap<>();
                for (int i = 0; i < paymentPenaltyProfitDtos.size(); i++) {
                    PaymentPenaltyProfitDto paymentPenaltyProfitDto = result.getPaymentPenaltyProfits().get(i);
                    //罚扣类型
                    detailMap.put("payPenaltyType", paymentPenaltyProfitDto.getPayPenaltyName() == null ? "" :
                            paymentPenaltyProfitDto.getPayPenaltyName());
                    //罚扣金额（含税）
                    detailMap.put("amount", paymentPenaltyProfitDto.getAmount() == null ? "0" : String.valueOf(paymentPenaltyProfitDto.getAmount()));
                    // 税率
                    String taxRate = paymentPenaltyProfitDto.getTaxRate() == null ? "0.00" : String.valueOf(paymentPenaltyProfitDto.getTaxRate());
                    detailMap.put("taxRate", taxRate.substring(0, taxRate.indexOf(".")) + "%");
                    // 税额
                    detailMap.put("taxAmount", paymentPenaltyProfitDto.getTaxAmount() == null ? "0" :
                            String.valueOf(paymentPenaltyProfitDto.getTaxAmount()));
                    // 关联发票号
                    detailMap.put("invoiceDetailCode", paymentPenaltyProfitDto.getInvoiceDetailCode() == null ? "" :
                            paymentPenaltyProfitDto.getInvoiceDetailCode());
                    detailMap.put("reason", paymentPenaltyProfitDto.getReason() == null ? "" : paymentPenaltyProfitDto.getReason());
                    BigDecimal excludingTaxAmount = paymentPenaltyProfitDto.getAmount().subtract(paymentPenaltyProfitDto.getTaxAmount() == null ?
                            BigDecimal.ZERO : paymentPenaltyProfitDto.getTaxAmount());
                    // 罚扣金额（不含税）
                    detailMap.put("excludingTaxAmount", excludingTaxAmount == null ? "0" : String.valueOf(excludingTaxAmount));
                    detailMapList.add(detailMap);
                }
            }

        } else {
            responseMap.setStatus("fail");
            responseMap.setMsg("result不能为空");
        }
        responseMap.setHeadMap(headMap);
        responseMap.setList1(detailMapList);
        responseMap.setFileList(fileList);
        responseMap.setStatus("success");
        responseMap.setMsg("成功");
        return responseMap;
    }

    @Override
    public Map<String, String> emailPaymentApply(Long id) {
        PaymentApplyDto paymentApplyDto = BeanConverter.copy(paymentApplyMapper.selectByPrimaryKey(id), PaymentApplyDto.class);
        if (paymentApplyDto == null) {
            return null;
        }
        statusTransformation(paymentApplyDto);

        Project project = projectService.selectByPrimaryKey(paymentApplyDto.getProjectId());
        // 取消支付/支付成功/部分支付 发送邮件
        if (!(Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.CANCEL_PAY.getCode())
                || Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.SUCCESS.getCode())
                || Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.PARTIAL_PAYMENT.getCode()))) {
            return null;
        }
        List<Email> emails = new ArrayList<>();

        Email email = new Email();
        String subject = "PAM付款申请/";
        if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.CANCEL_PAY.getCode())) {
            subject += "取消支付/";
        } else if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.SUCCESS.getCode())) {
            subject += "支付成功/";
        } else if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.PARTIAL_PAYMENT.getCode())) {
            subject += "部分支付/";
        }
        subject += paymentApplyDto.getPaymentApplyCode() + "/"
                + paymentApplyDto.getVendorName() + "/" + paymentApplyDto.getTaxPayIncludedPrice();
        email.setSubject(subject);
        email.setLanguage("zh-CN");

        String content = buildContent(paymentApplyDto, project);
        email.setContent(content);
        email.setBusinessType(NoticeBusinessType.PAYMENT_APPLY_REMIND.getType());
        email.setDeletedFlag(Boolean.FALSE);
        email.setStatus(EmailStatus.TO_DO.getCode());
        email.setFromAddress("pam");

        // 付款申请人
        UserInfo userInfo = CacheDataUtils.findUserById(Long.valueOf(paymentApplyDto.getSubmitBy()));
        String paymentReceiver = "";
        if (userInfo != null) {
            paymentReceiver = userInfo.getUsername();
            email.setReceiver(userInfo.getUsername());
            emails.add(email);
        }

        // 项目经理
        if (project != null && project.getManagerId() != null) {
            Email emailManager = BeanConverter.copy(email, Email.class);
            userInfo = CacheDataUtils.findUserById(Long.valueOf(project.getManagerId()));
            if (userInfo != null && !paymentReceiver.equals(userInfo.getUsername())) {
                emailManager.setReceiver(userInfo.getUsername());
                emails.add(emailManager);
            }
        }

        if (ListUtils.isNotEmpty(emails)) {
            noticeService.sendMail(emails);
        }
        return null;
    }

    @Override
    public void prePaymentDlPay(Long paymentId, boolean status, String msg) {
        if (!status) {
            return;
        }
        if (paymentId == null) {
            logger.error("付款申请id不能为空");
            return;
        }
        ResendExecute resendExecute = new ResendExecute();
        resendExecute.setApplyNo(String.valueOf(paymentId));
        resendExecute.setBusinessType(BusinessTypeEnums.APPAYMENTS_PAYMENT_APPLY.getCode());
        resendExecute.setBatch(Boolean.FALSE);
        resendExecute.setDeletedFlag(Boolean.FALSE);
        resendExecute.setCreateAt(new Date());
        resendExecute.setCreateBy(-1L);
        resendExecute.setStatus(0);
        resendExecuteService.saveResendExecute(resendExecute);
    }

    @Override
    public void prePaymentDlPayCallBack(Long paymentId, boolean status, String msg) {
        if (paymentId == null) {
            logger.error("付款申请id不能为空");
            return;
        }
        PaymentApply paymentApply = new PaymentApply();
        paymentApply.setId(paymentId);
        paymentApply.setEsbStatus(status ? PaymentApplyEsbStatus.SUCCESS.getCode() : PaymentApplyEsbStatus.ABNORMAL.getCode());
        paymentApply.setErpMsg(msg);
        paymentApplyMapper.updateByPrimaryKeySelective(paymentApply);
    }

    private String buildContent(PaymentApplyDto paymentApplyDto, Project project) {
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        String header = buildHeader(paymentApplyDto, project);

        String table = buildTable(paymentApplyDto, project);
        sb.append(header);

        sb.append(table);
        sb.append(buildUrl(paymentApplyDto.getId()));
        sb.append("</html>");
        return sb.toString();
    }

    private String buildHeader(PaymentApplyDto paymentApplyDto, Project project) {
        String urlStr = "【PAM温馨提醒】付款申请 "
                + paymentApplyDto.getPaymentApplyCode() + " 已";
        if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.CANCEL_PAY.getCode())) {
            urlStr += "取消支付";
        } else if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.SUCCESS.getCode())) {
            urlStr += "支付成功";
        } else if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.PARTIAL_PAYMENT.getCode())) {
            urlStr += "部分支付";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        urlStr = "<div style='font-size: 12px;'>" + urlStr + "</div><br/>";
        sb.append(urlStr);
        sb.append("</html>");
        return sb.toString();
    }

    private String buildTable(PaymentApplyDto paymentApplyDto, Project project) {
        StringBuilder sb = new StringBuilder();
        sb.append("<table style='font-size: 12px;border-collapse:collapse;table-layout: fixed;word-wrap:break-word;width: 900px;'>");
        sb.append("<tr style='height:30px;'>");
        sb.append("<td style='padding-left: 10px;border: #999 1px solid;align:center;width:17%;'>项目名称</td>");
        sb.append("<td style='padding-left: 10px;border: #999 1px solid;align:center;width:50%;'>" + project.getName() + "</td>");
        sb.append("<td style='padding-left: 10px;border: #999 1px solid;align:center;width:17%;'>项目编号</td>");
        sb.append("<td style='padding-left: 10px;border: #999 1px solid;align:center;width:16%;'>" + project.getCode() + "</td>");
        sb.append("</tr>");

        sb.append("<tr style='height:30px;'>");
        sb.append(buildTd("采购合同名称"));
        sb.append(buildTd(paymentApplyDto.getPurchaseContractName()));
        sb.append(buildTd("采购合同编号"));
        sb.append(buildTd(paymentApplyDto.getPurchaseContractCode()));
        sb.append("</tr>");

        sb.append("<tr style='height:30px;'>");
        sb.append(buildTd("供应商名称"));
        sb.append("<td style='padding-left: 10px;border: #999 1px solid; align:center;' colspan='3'>" + paymentApplyDto.getVendorName() + "</td>");
        sb.append("</tr>");

        sb.append("<tr style='height:30px;'>");
        sb.append(buildTd("申请付款金额（含税）"));
        if (paymentApplyDto.getTaxPayIncludedPrice() != null) {
            sb.append(buildTd("" + paymentApplyDto.getTaxPayIncludedPrice()));
        } else {
            sb.append(buildTd("0.00"));
        }
        sb.append(buildTd("申请日期"));
        if (paymentApplyDto.getAuditDate() == null) {
            sb.append(buildTd(DateUtil.format(paymentApplyDto.getCreateAt(), DateUtil.DATE_PATTERN)));
        } else {
            sb.append(buildTd(DateUtil.format(paymentApplyDto.getAuditDate(), DateUtil.DATE_PATTERN)));
        }
        sb.append("</tr>");

        sb.append("<tr style='height:30px;'>");
        sb.append(buildTd("实际付款金额（含税）"));
        if (paymentApplyDto.getReallyPayIncludedPrice() != null) {
            sb.append(buildTd("" + paymentApplyDto.getReallyPayIncludedPrice()));
        } else {
            sb.append(buildTd("0.00"));
        }
        sb.append(buildTd("付款日期"));
        if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.CANCEL_PAY.getCode())) {
            sb.append(buildTd(""));
        } else if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.SUCCESS.getCode())
                || Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.PARTIAL_PAYMENT.getCode())) {
            if (paymentApplyDto.getUpdateAt() == null) {
                sb.append(buildTd(DateUtil.format(new Date(), DateUtil.DATE_PATTERN)));
            } else {
                sb.append(buildTd(DateUtil.format(paymentApplyDto.getUpdateAt(), DateUtil.DATE_PATTERN)));
            }
        }
        sb.append("</tr>");


        sb.append("<tr style='height:30px;'>");
        sb.append(buildTd("付款状态"));
        if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.CANCEL_PAY.getCode())) {
            sb.append("<td style='padding-left: 10px;border: #999 1px solid; align:center;' colspan='3'>取消支付</td>");
        } else if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.SUCCESS.getCode())) {
            sb.append("<td style='padding-left: 10px;border: #999 1px solid; align:center;' colspan='3'>支付完成</td>");
        } else if (Objects.equals(paymentApplyDto.getBizStatus(), PaymentApplyBizStatus.PARTIAL_PAYMENT.getCode())) {
            sb.append("<td style='padding-left: 10px;border: #999 1px solid; align:center;' colspan='3'>部分支付</td>");
        }
        sb.append("</tr>");

        sb.append("</table>");
        return sb.toString();
    }

    private String buildTd(String value) {
        return "<td style='padding-left: 10px;border: #999 1px solid; align:center;'>" + (value == null ? "" : value) + "</td>";
    }

    private String buildUrl(Long paymentApplyId) {
        String url = paymentApplyEmailUrl + paymentApplyId;
        String urlStr = "<br><a href='" + url + "' target='_blank'>" + url + "</a>";
        return urlStr;
    }

    /**
     * 根据税率查询税码
     *
     * @param taxRate
     * @return
     */
    private String taxCodeQuery(String taxRate) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", 1);
        param.put("pageSize", Integer.MAX_VALUE);
        param.put("taxType", 1);
        param.put("taxValue", taxRate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "finance/taxInfo/getTaxList", param);
        String res = restTemplate.getForObject(url, String.class);
        PageInfo<TaxInfoDto> data = JSON.parseObject(res, new TypeReference<PageInfo<TaxInfoDto>>() {
        });
        if (Objects.nonNull(data) && ListUtils.isNotEmpty(data.getList())) {
            return data.getList().get(0).getTaxCode();
        }
        return "";
    }

    @Override
    public boolean updateAuditStatusForException(Long id) {
        PaymentApply paymentApply = paymentApplyMapper.selectByPrimaryKey(id);
        paymentApply.setAuditStatus(AuditStatus.EXCEPTION.getCode());
        return paymentApplyMapper.updateByPrimaryKeySelective(paymentApply) > 0;
    }
}
