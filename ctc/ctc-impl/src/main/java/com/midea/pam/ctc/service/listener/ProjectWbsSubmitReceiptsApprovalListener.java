package com.midea.pam.ctc.service.listener;

import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.entity.MaterialAdjustHeader;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.enums.MaterialAdjustEnum;
import com.midea.pam.common.enums.RequirementStatusEnum;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.MilepostDesignPlanDetailModelStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.service.MaterialAdjustService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.event.ProjectWbsSubmitReceiptsApprovalEvent;
import com.midea.pam.ctc.service.helper.SystemContextHelper;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description 柔性详设发布审批通过事件监听
 * Created by liuqing85
 * Date 2023/4/6 10:05
 */
public class ProjectWbsSubmitReceiptsApprovalListener implements ApplicationListener<ProjectWbsSubmitReceiptsApprovalEvent> {

    private static final Logger logger = LoggerFactory.getLogger(ProjectWbsSubmitReceiptsApprovalListener.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private ProjectWbsReceiptsMapper projectWbsReceiptsMapper;

    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;

    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectExtMapper projectExtMapper;

    @Resource
    private MaterialAdjustService materialAdjustService;

    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;

    @Resource
    private WorkflowCallbackService workflowCallbackService;

    @Resource
    private SystemContextHelper systemContextHelper;

    @Transactional(rollbackFor = Exception.class)
    @Async
    @Override
    public void onApplicationEvent(ProjectWbsSubmitReceiptsApprovalEvent event) {
        logger.info("详设发布单据审批通过回调的异步处理参数为:{}", JsonUtils.toString(event));
        systemContextHelper.dealUserInfo(event.getCreateUserId(), event.getCompanyId());
        passCallback(event.getFormInstanceId(), event.getFdInstanceId(), event.getFormUrl(), event.getEventName(), event.getHandlerId(),
                event.getCompanyId(), event.getCreateUserId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void passCallback(String formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                             Long createUserId) {
        logger.info("详设发布单据审批通过回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, createUserId:{}",
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, companyId);
        String lockName = String.format("ProjectWbsSubmitReceipts_passCallback_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(Long.valueOf(formInstanceId), fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(Long.valueOf(formInstanceId), fdInstanceId, formUrl,
                            eventName);
                    formInstance = workflowCallbackService.getFormInstance(Long.valueOf(formInstanceId), formUrl);

                    ProjectWbsReceipts receipt = projectWbsReceiptsMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
                    Guard.notNull(receipt, String.format("详设发布单据审批通过回调 formInstanceId:%s对应需求发布单不存在，不处理", formInstanceId));

                    // 更新详设发布单据状态：生效
                    ProjectWbsReceipts updateItem = new ProjectWbsReceipts();
                    updateItem.setId(receipt.getId());
                    updateItem.setRequirementStatus(RequirementStatusEnum.PROCESS.getCode());
                    updateItem.setUpdateBy(createUserId);
                    updateItem.setUpdateAt(new Date());

                    projectWbsReceiptsMapper.updateByPrimaryKeySelective(updateItem);
                    // 根据详设发布单据，获取详细设计
                    List<MilepostDesignPlanDetailSubmitHistory> planDetailHistoryList =
                            projectWbsReceiptsService.getDesignPlanDetailForSubmit(receipt);
                    Guard.notNullOrEmpty(planDetailHistoryList, String.format("详设发布单据审批通过回调 formInstanceId：%s对应详细设计关联不存在，不处理", formInstanceId));

                    logger.info("详设发布单据审批通过回调的formInstanceId：{}，planDetailHistoryList：{}", formInstanceId, JsonUtils.toString(planDetailHistoryList));

                    List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
                    List<MilepostDesignPlanDetailSubmitHistory> planDetailSubmitHistoryUpdateList = new ArrayList<>();
                    List<MilepostDesignPlanDetailSubmitHistory> planDetailSubmitHistoryAddList = new ArrayList<>();

                    Map<Long, MilepostDesignPlanDetailSubmitHistory> planDetailHistoryMap = planDetailHistoryList.stream()
                            .collect(Collectors.toMap(MilepostDesignPlanDetailSubmitHistory::getId, Function.identity()));
                    // 当前提交历史id 和 对应的父级DTO
                    Map<Long, MilepostDesignPlanDetailSubmitHistory> currentHistoryMap = new HashMap<>();
                    Map<Long, MilepostDesignPlanDetailSubmitHistory> parentHistoryMap = new HashMap<>();
                    for (MilepostDesignPlanDetailSubmitHistory submitHistory : planDetailHistoryList) {
                        Long parentId = submitHistory.getParentId();
                        if (null != parentId && parentId != -1) {
                            parentHistoryMap.put(submitHistory.getId(), planDetailHistoryMap.get(parentId));
                        }
                        currentHistoryMap.put(submitHistory.getId(), submitHistory);
                        if (Objects.nonNull(submitHistory.getDesignPlanDetailId())) {
                            planDetailSubmitHistoryUpdateList.add(submitHistory);
                        } else {
                            planDetailSubmitHistoryAddList.add(submitHistory);
                        }
                    }

                    // 先修改
                    for (MilepostDesignPlanDetailSubmitHistory submitHistory : planDetailSubmitHistoryUpdateList) {
                        MilepostDesignPlanDetailDto milepostDesignPlanDetail = BeanConverter.copyProperties(submitHistory,
                                MilepostDesignPlanDetailDto.class);
                        Long designPlanDetailId = submitHistory.getDesignPlanDetailId();
                        milepostDesignPlanDetail.setId(designPlanDetailId);
                        // TODO 踩坑，milepostDesignPlanDetailSubmitHistory的parent_id不是详设的parent_id，一定要置为null
                        milepostDesignPlanDetail.setParentId(null);
                        if (!Boolean.FALSE.equals(milepostDesignPlanDetail.getWbsLastLayer())) {
                            milepostDesignPlanDetail.setStatus(CheckStatus.WBS_RECEIPTS_PASSED.code());
                        }
                        updateDesignPlanDetailList.add(milepostDesignPlanDetail);
                    }
                    logger.info("详设发布单据审批通过回调的formInstanceId：{}，updateDesignPlanDetailList：{}", formInstanceId,
                            JsonUtils.toString(updateDesignPlanDetailList));
                    // 批量更新当前的详细设计
                    if (CollectionUtils.isNotEmpty(updateDesignPlanDetailList)) {
                        milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
                    }

                    // 再添加(详设发布单据审批通过时，没有改变的详设 moduleStatus 不变，只有新增时 moduleStatus 才为 0--未确认)
                    List<Long> addSubmitHistoryIdList = new ArrayList<>();
                    for (MilepostDesignPlanDetailSubmitHistory currentSubmitHistory : planDetailSubmitHistoryAddList) {
                        MilepostDesignPlanDetailSubmitHistory parentSubmitHistory = parentHistoryMap.get(currentSubmitHistory.getId());
                        fillParentId(parentSubmitHistory, currentSubmitHistory, parentHistoryMap, currentHistoryMap, addSubmitHistoryIdList);
                    }

                    Long organizationId = projectService.getOrganizationIdByProjectId(receipt.getProjectId());

                    // 详细设计发布审批通过后，往物料新增添和变更行表插入记录

                    MaterialAdjustHeader materialAdjustHeader = new MaterialAdjustHeader();
                    materialAdjustHeader.setApplyBy(receipt.getCreateBy());
                    UserInfo userInfo = CacheDataUtils.findUserById(receipt.getCreateBy());
                    if (userInfo != null) {
                        materialAdjustHeader.setApplyByName(userInfo.getName());
                    }
                    materialAdjustHeader.setApplyTime(receipt.getCreateAt());
                    materialAdjustHeader.setCreateBy(receipt.getCreateBy());
                    materialAdjustHeader.setCreateAt(new Date());
                    materialAdjustHeader.setUpdateBy(receipt.getCreateBy());
                    materialAdjustHeader.setUpdateAt(new Date());
                    // 生成单据号
                    Long unitId = projectExtMapper.getUnitIdByProjectId(receipt.getProjectId());
                    materialAdjustHeader.setAdjustCode(materialAdjustService.generateMaterialAdjustCode(unitId));
                    materialAdjustHeader.setDeletedFlag(Boolean.FALSE);
                    materialAdjustHeader.setStatus(MaterialAdjustEnum.APPROVED.code());
                    // 封装库存组织id
                    materialAdjustHeader.setOrganizationId(organizationId);
                    // 封装业务实体id
                    Long ouId = projectService.selectByPrimaryKey(receipt.getProjectId()).getOuId();
                    materialAdjustHeader.setOuId(ouId);
                    materialAdjustHeader.setAdjustType(MaterialAdjustEnum.MATERIAL_ADJUST_ADDED.code());
                    materialAdjustHeader.setResource(1);
                    materialAdjustHeader.setSyncStatus(MaterialAdjustEnum.SYNC_STATUS_SUCCESS.code());
                    materialAdjustHeader.setSyncMes("");
                    materialAdjustHeader.setProjectId(receipt.getProjectId());
                    materialAdjustService.insertHeader(materialAdjustHeader);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(Long.valueOf(formInstanceId), fdInstanceId, formUrl, eventName,
                            formInstance, true);

                    try {
                        projectWbsReceiptsService.ProjectWbsDesignPlanAutoConfirm(receipt.getProjectId(),receipt.getId());
                    } catch (Exception e) {
                       logger.error("详设发布单据审批-执行自动确认失败", e);
                    }
                } catch (ApplicationBizException e) {
                    logger.info("详设发布单据审批通过回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("详设发布单据审批通过回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("详设发布单据审批通过回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    private void fillParentId(MilepostDesignPlanDetailSubmitHistory parentSubmitHistory, MilepostDesignPlanDetailSubmitHistory currentSubmitHistory,
                              Map<Long, MilepostDesignPlanDetailSubmitHistory> parentHistoryMap,
                              Map<Long, MilepostDesignPlanDetailSubmitHistory> currentHistoryMap,
                              List<Long> addSubmitHistoryIdList) {
        Long parentDesignPlanDetailId = parentSubmitHistory.getDesignPlanDetailId();
        // 一直递归按照详设层级的顺序的开始添加
        if (null == parentDesignPlanDetailId) {
            MilepostDesignPlanDetailSubmitHistory grandSubmitHistory = parentHistoryMap.get(parentSubmitHistory.getId());
            fillParentId(grandSubmitHistory, parentSubmitHistory, parentHistoryMap, currentHistoryMap, addSubmitHistoryIdList);
        } else {
            Long submitHistoryId = currentSubmitHistory.getId();
            // 防止重复添加详设
            if (addSubmitHistoryIdList.contains(submitHistoryId)) {
                return;
            }
            // 添加有上级详设id的当前submitHistory
            MilepostDesignPlanDetailDto currentMilepostDesignPlanDetail = BeanConverter.copyProperties(currentSubmitHistory,
                    MilepostDesignPlanDetailDto.class);
            currentMilepostDesignPlanDetail.setId(null);
            currentMilepostDesignPlanDetail.setParentId(parentDesignPlanDetailId);
            // 详设发布单据审批通过时，只有新增时 moduleStatus 才为 0--未确认
            currentMilepostDesignPlanDetail.setModuleStatus(MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code());
            if (!Boolean.FALSE.equals(currentMilepostDesignPlanDetail.getWbsLastLayer())) {
                currentMilepostDesignPlanDetail.setStatus(CheckStatus.WBS_RECEIPTS_PASSED.code());
            }
            milepostDesignPlanDetailService.save(currentMilepostDesignPlanDetail, null);
            // 重新设置当前 添加生成的详设id
            MilepostDesignPlanDetailSubmitHistory submitHistory = currentHistoryMap.get(submitHistoryId);
            submitHistory.setDesignPlanDetailId(currentMilepostDesignPlanDetail.getId());
            currentHistoryMap.put(submitHistoryId, submitHistory);
            addSubmitHistoryIdList.add(submitHistoryId);
        }
    }
}
