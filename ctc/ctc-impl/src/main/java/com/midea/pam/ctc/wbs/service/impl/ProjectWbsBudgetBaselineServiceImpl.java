package com.midea.pam.ctc.wbs.service.impl;


import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.dto.FeeItemExpenseTypeDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBaselineChangeDTO;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetBaselineChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetBaselineDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetObjectDto;
import com.midea.pam.common.ctc.dto.WbsDynamicFieldsDto;
import com.midea.pam.common.ctc.dto.WbsTemplateRuleDto;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.OrganizationCustomDictExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBaselineBatch;
import com.midea.pam.common.ctc.entity.ProjectBaselineBatchExample;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetBaseline;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetBaselineChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetBaselineChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetBaselineExample;
import com.midea.pam.common.ctc.entity.WbsCustomizeRule;
import com.midea.pam.common.ctc.entity.WbsCustomizeRuleExample;
import com.midea.pam.common.ctc.entity.WbsTemplateInfo;
import com.midea.pam.common.ctc.entity.WbsTemplateRuleDetail;
import com.midea.pam.common.ctc.entity.WbsTemplateRuleDetailExample;
import com.midea.pam.common.ctc.excelVo.ProjectWbsBudgetImportResponseExcelVO;
import com.midea.pam.common.ctc.vo.PreviewWbsBudgetChangeVo;
import com.midea.pam.common.enums.ChangeType;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CtcProjectChangeType;
import com.midea.pam.common.enums.DescribeDisplayEnum;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.ProjectChangeStatus;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.ctc.mapper.OrganizationCustomDictMapper;
import com.midea.pam.ctc.mapper.ProjectBaselineBatchMapper;
import com.midea.pam.ctc.mapper.ProjectHistoryHeaderMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetBaselineChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetBaselineExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetBaselineMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetMapper;
import com.midea.pam.ctc.mapper.UnitExtMapper;
import com.midea.pam.ctc.mapper.WbsCustomizeRuleMapper;
import com.midea.pam.ctc.mapper.WbsTemplateInfoMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleDetailMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleExtMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleMapper;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.wbs.service.PreviewWbsBudgetInfoChangeService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetBaselineService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetCheckHelpper;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetService;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleService;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.UUID;
import java.util.stream.Collectors;


public class ProjectWbsBudgetBaselineServiceImpl implements ProjectWbsBudgetBaselineService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectWbsBudgetMapper projectWbsBudgetMapper;
    @Resource
    private ProjectWbsBudgetBaselineMapper projectWbsBudgetBaselineMapper;
    @Lazy //循环依赖，懒加载
    @Resource
    private ProjectWbsBudgetService projectWbsBudgetService;
    @Resource
    private WbsTemplateRuleService wbsTemplateRuleService;
    @Resource
    private ProjectWbsBudgetCheckHelpper projectWbsBudgetCheckHelpper;
    @Resource
    private ProjectTypeService projectTypeService;
    @Resource
    private ProjectWbsBudgetBaselineExtMapper projectWbsBudgetBaselineExtMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectHistoryHeaderMapper projectHistoryHeaderMapper;
    @Resource
    private ProjectWbsBudgetBaselineChangeHistoryMapper projectWbsBudgetBaselineChangeHistoryMapper;
    @Resource
    private PreviewWbsBudgetInfoChangeService previewWbsBudgetInfoChangeService;
    @Resource
    private ProjectBaselineBatchMapper projectBaselineBatchMapper;
    @Resource
    private UnitExtMapper unitExtMapper;
    @Resource
    private OrganizationCustomDictMapper organizationCustomDictMapper;
    @Resource
    private WbsTemplateInfoMapper wbsTemplateInfoMapper;
    @Resource
    private WbsCustomizeRuleMapper wbsCustomizeRuleMapper;
    @Resource
    private WbsTemplateRuleDetailMapper wbsTemplateRuleDetailMapper;

    @Resource
    private WbsTemplateRuleExtMapper wbsTemplateRuleExtMapper;

    /**
     * 获取前端格式的wbs预算基线
     *
     * @param projectId 项目id
     * @return
     */
    @Override
    public List<Map<String, Object>> findWbsBudgetBaselineMapsByWebfront(Long projectId) {
        /* wbs预算基线 */
        List<Map<String, Object>> wbsBudgetBaselineMapList = new ArrayList<>();
        ProjectWbsBudgetBaselineExample projectWbsBudgetBaselineExample = new ProjectWbsBudgetBaselineExample();
        projectWbsBudgetBaselineExample.createCriteria().andProjectIdEqualTo(projectId);
        List<ProjectWbsBudgetBaseline> wbsBudgetBaselineList = projectWbsBudgetBaselineMapper.selectByExample(projectWbsBudgetBaselineExample);
        for (ProjectWbsBudgetBaseline wbsBudgetBaseline : wbsBudgetBaselineList) {
            Map<String, Object> wbsBudgetBaselineMap = ProjectWbsBudgetBaselineDto.entity2Map(wbsBudgetBaseline);
            wbsBudgetBaselineMap.put(WbsBudgetFieldConstant.ROW_ID, wbsBudgetBaselineMap.get(WbsBudgetFieldConstant.ID));
            wbsBudgetBaselineMapList.add(wbsBudgetBaselineMap);
        }
        return wbsBudgetBaselineMapList;
    }

    /**
     * 导入wbs基线
     *
     * @param importResponseExcelVO 导入参数
     * @return
     */
    @Override
    public ProjectWbsBudgetImportResponseExcelVO importWbsBaselineFromExcel(ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO) {
        ProjectWbsBudgetImportResponseExcelVO result = new ProjectWbsBudgetImportResponseExcelVO();

        Long wbsTemplateInfoId = importResponseExcelVO.getWbsTemplateInfoId();
        //校验是否已经开启描述自定义
        String describeDisplay = "";
        WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(wbsTemplateInfoId);
        if (null != wbsTemplateInfo){
            describeDisplay = wbsTemplateInfo.getDescribeDisplay();
        }


        /* wbs预算基线（前端页面数据） */
        List<Map<String, Object>> pageBaselineMapList = importResponseExcelVO.getPageBaselineMapList();
        /* wbs预算（前端页面数据） */
        List<Map<String, Object>> pageBudgetMapList = importResponseExcelVO.getPageBudgetMapList();
        /* Excel导入记录 */
        List<Map> excelMapList = importResponseExcelVO.getExcelMapList();

        // wbs动态列
        List<WbsDynamicFieldsDto> wbsDynamicFieldList = wbsTemplateRuleService.getWbsDynamicFields(importResponseExcelVO.getWbsTemplateInfoId());
        // 检查excel，设置默认值
        List<String> errorMsgList = projectWbsBudgetService.checkImportExcelAndInit(importResponseExcelVO.getBatchCode(), excelMapList, importResponseExcelVO.getWbsTemplateInfoId(), false);
        if (!CollectionUtils.isEmpty(errorMsgList)) {
            result.setErrorMsg(errorMsgList);
            return result;
        }

        // wbs预算Map，只存有效记录（增量或覆盖导入）
        Map<String, Map> uniqueBudgetMap = new HashMap();

        /**
         * Map存放计算后的基线
         * k = (wbs+activity)
         * hk = [diffBaselineCost变更基线差异]    // 后面的没有做统计 [baselineCost变更前基线] [afterChangeBaselineCost变更后基线]
         * hv = bigdecimal
         */
        Map<String, Map<String, BigDecimal>> sumBaselineCostMap = new HashMap<>();

        // wbs预算集合，包含已删除的wbs预算
        List<Map> budgetResultList = mergeWbsBudget(importResponseExcelVO,
                excelMapList,
                pageBudgetMapList,
                uniqueBudgetMap,
                wbsDynamicFieldList,
                wbsTemplateInfoId,
                describeDisplay);

        // 校验同一个WBS下面，同一个经济事项不允许重复
        errorMsgList = projectWbsBudgetCheckHelpper.checkWbsRepeatFeeType(budgetResultList, importResponseExcelVO.getWbsTemplateInfoId(), SystemContext.getUnitId());
        if (!CollectionUtils.isEmpty(errorMsgList)) {
            result.setErrorMsg(errorMsgList);
            return result;
        }

        // wbs预算集合，包含已删除的wbs预算
        List<Map> baselineResultList = mergeWbsBaseline(importResponseExcelVO,
                excelMapList,
                pageBaselineMapList,
                uniqueBudgetMap,
                wbsDynamicFieldList,
                sumBaselineCostMap,
                wbsTemplateInfoId,
                describeDisplay);

        /* 重新统计预算 - 基线差异 */
        if (CollectionUtils.isNotEmpty(budgetResultList)) {
            for (Map budgetMap : budgetResultList) {
                // 跳过已删除记录
                if (Boolean.TRUE.equals(MapUtils.getBoolean(budgetMap, WbsBudgetFieldConstant.DELETED_FLAG))) {
                    continue;
                }
                // 预算金额
                BigDecimal price = StringUtils.isBlank(MapUtils.getString(budgetMap, WbsBudgetFieldConstant.PRICE)) ? BigDecimal.ZERO : new BigDecimal(MapUtils.getString(budgetMap, WbsBudgetFieldConstant.PRICE));

                // 基线差异
                BigDecimal diffBaselineCost = BigDecimal.ZERO;
                String uniqueBudgetKey = ProjectWbsBudgetUtils.getWbsBudgetUniqueKey(budgetMap, wbsDynamicFieldList, null);
                if (sumBaselineCostMap.containsKey(uniqueBudgetKey)) {
                    Map<String, BigDecimal> baselineCostMap = sumBaselineCostMap.get(uniqueBudgetKey);
                    if (StringUtils.isNotBlank(MapUtils.getString(baselineCostMap, WbsBudgetFieldConstant.DIFF_BASELINE_COST))) {
                        diffBaselineCost = new BigDecimal(MapUtils.getString(baselineCostMap, WbsBudgetFieldConstant.DIFF_BASELINE_COST));
                    }
                }
                // 变更后预算 = 预算金额 + 基线差异
                BigDecimal afterChangePrice = price.add(diffBaselineCost).setScale(2, RoundingMode.HALF_UP);
                budgetMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, afterChangePrice);
            }
        }

        result.setWbsBudgetList(budgetResultList);
        result.setWbsBudgetBaselineList(baselineResultList);
        return result;
    }

    /**
     * wbs预算（增量或覆盖导入，只初始化数据，不统计）
     *
     * @param importResponseExcelVO 导入参数
     * @param excelMapList          Excel导入记录
     * @param pageBudgetMapList     前端预算记录
     * @param uniqueBudgetMap       wbs预算Map，只存有效记录（增量或覆盖导入）[前端会传afterChangePrice]
     * @param wbsDynamicFieldList   wbs动态列
     * @return
     */
    private List<Map> mergeWbsBudget(ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO, List<Map> excelMapList, List<Map<String, Object>> pageBudgetMapList, Map<String, Map> uniqueBudgetMap, List<WbsDynamicFieldsDto> wbsDynamicFieldList,Long wbsTemplateInfoId,String describeDisplay) {

        // 已删除的wbs预算
        List<Map> removeBudgetMapList = new ArrayList<>();

        // 获取费用类型经济事项
        List<FeeItemExpenseTypeDto> feeItemExpenseTypeDtoList = projectTypeService.selectFeeItemExpenseType(importResponseExcelVO.getProjectType());

        /* 增量导入 */
        if (WbsBudgetFieldConstant.ADD.equals(importResponseExcelVO.getImportType()) && !CollectionUtils.isEmpty(pageBudgetMapList)) {
            // 前端预算合并到uniqueBudgetMap
            ProjectWbsBudgetUtils.wbsBudget2UniqueMap(pageBudgetMapList, uniqueBudgetMap, removeBudgetMapList, wbsDynamicFieldList);
        }
        /* 覆盖导入 */
        else {
            // 获取数据库中的预算，合并到uniqueBudgetMap
            List<Map<String, Object>> wbsBudgetBaselineMapList = projectWbsBudgetService.findWbsBudgetMapsByWebfront(importResponseExcelVO.getProjectId());
            if (!CollectionUtils.isEmpty(wbsBudgetBaselineMapList)) {
                // 覆盖导入初始化：变更后预算金额 = 预算金额（后面汇总基线差异再减回来）
                wbsBudgetBaselineMapList.stream().forEach(a -> a.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, a.get(WbsBudgetFieldConstant.PRICE)));
                ProjectWbsBudgetUtils.wbsBudget2UniqueMap(wbsBudgetBaselineMapList, uniqueBudgetMap, removeBudgetMapList, wbsDynamicFieldList);

            }
        }

        for (Map excel : excelMapList) {
            // 唯一key = wbs动态列 + activity活动事项
            String uniqueBudgetKey = ProjectWbsBudgetUtils.getWbsBudgetUniqueKey(excel, wbsDynamicFieldList, null);
            Map budgetMap = new HashMap();
            if (uniqueBudgetMap.containsKey(uniqueBudgetKey)) {
                budgetMap = uniqueBudgetMap.get(uniqueBudgetKey);
            } else {
                budgetMap.putAll(excel);
                // 新增wbs预算，预算金额 = 0
                budgetMap.put(WbsBudgetFieldConstant.PRICE, BigDecimal.ZERO);
                // 新增wbs基线，预算基线 = 0
                budgetMap.put(WbsBudgetFieldConstant.BASELINE_COST, BigDecimal.ZERO);
                // 新增wbs变更后预算，变更后基线 = 0
                budgetMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, BigDecimal.ZERO);

                // 设置经济事项
                projectWbsBudgetService.setFeeExpenseType(feeItemExpenseTypeDtoList, budgetMap, wbsDynamicFieldList);
            }
            String descriptionString = "";
            Object description = budgetMap.get(WbsBudgetFieldConstant.DESCRIPTION);
            if (description != null){
                descriptionString = description.toString();
            }
            //如果开启了自定义描述，则通过自定义描述返回，否则返回原来末级的描述
            descriptionRuleSet(wbsTemplateInfoId, describeDisplay, budgetMap,descriptionString);
            // 默认rowId
            budgetMap.put(WbsBudgetFieldConstant.ROW_ID, budgetMap.containsKey(WbsBudgetFieldConstant.ID) ?
                    budgetMap.get(WbsBudgetFieldConstant.ID) : UUID.randomUUID().toString());
            uniqueBudgetMap.put(uniqueBudgetKey, budgetMap);
        }

        // wbs预算（增量或覆盖导入） 包含已删除的wbs预算
        List<Map> budgetResultList = new ArrayList<>();
        budgetResultList.addAll(uniqueBudgetMap.values());
        if (!CollectionUtils.isEmpty(removeBudgetMapList)) {
            budgetResultList.addAll(removeBudgetMapList);
        }
        return budgetResultList;
    }

    private void descriptionRuleSet(Long wbsTemplateInfoId, String describeDisplay, Map budgetMap,String descriptionString) {
        if (DescribeDisplayEnum.CUSTOMIZE.getCode().equals(describeDisplay)) {
            TreeMap<Integer, String> fieldValueMap = new TreeMap<>();
            for (Object keyObj : budgetMap.keySet()) {
                String key = keyObj.toString();
                if (key.startsWith("field_")) {
                    int fieldNum = Integer.parseInt(key.substring(6));
                    String fieldValue = budgetMap.get(key).toString();
                    fieldValueMap.put(fieldNum, fieldValue);
                }
            }
            String dynamicCombination = String.join("-", fieldValueMap.values());
            if (!dynamicCombination.isEmpty()) {
                WbsCustomizeRuleExample example = new WbsCustomizeRuleExample();
                example.createCriteria()
                        .andDynamicCombinationEqualTo(dynamicCombination)
                        .andWbsTemplateInfoIdEqualTo(wbsTemplateInfoId)
                        .andDeletedFlagEqualTo(false);

                List<WbsCustomizeRule> rules = wbsCustomizeRuleMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(rules)) {
                    budgetMap.put(WbsBudgetFieldConstant.DESCRIPTION, rules.get(0).getDescription());
                } else {
                    // 如果没有查询到自定义描述，可以设置为原来末级带出来的描述
                    budgetMap.put(WbsBudgetFieldConstant.DESCRIPTION, descriptionString);
                }
            }
        }
    }
    /**
     * wbs基线（增量或覆盖导入）
     * 统计变更前基线，变更后基线
     *
     * @param importResponseExcelVO 导入参数
     * @param excelMapList          Excel导入记录
     * @param pageBaselineMapList   前端基线记录
     * @param uniqueBudgetMap       wbs预算Map，只存有效记录（增量或覆盖导入）
     * @param wbsDynamicFieldList   wbs动态列
     * @param sumBaselineCostMap    存放计算后的基线
     * @return
     */
    private List<Map> mergeWbsBaseline(ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO,
                                       List<Map> excelMapList,
                                       List<Map<String, Object>> pageBaselineMapList,
                                       Map<String, Map> uniqueBudgetMap,
                                       List<WbsDynamicFieldsDto> wbsDynamicFieldList,
                                       Map<String, Map<String, BigDecimal>> sumBaselineCostMap,
                                       Long wbsTemplateInfoId,String describeDisplay) {

        // wbs基线（增量或覆盖导入） 包含已删除的wbs基线
        List<Map> baselineResultList = new ArrayList<>();
        // 已删除的wbs基线
        List<Map> removeBaselineMapList = new ArrayList<>();
        // wbs基线Map，只存有效记录（增量或覆盖导入）
        Map<String, Map> uniqueBaselineMap = new HashMap();

        /* 增量导入 */
        if (WbsBudgetFieldConstant.ADD.equals(importResponseExcelVO.getImportType()) && !CollectionUtils.isEmpty(pageBaselineMapList)) {
            // 前端基线合并到uniqueMap
            ProjectWbsBudgetUtils.wbsBaseline2UniqueMap(pageBaselineMapList, uniqueBudgetMap, uniqueBaselineMap, removeBaselineMapList, wbsDynamicFieldList);
        }
        /* 覆盖导入 */
        else {
            // 获取数据库中的预算基线，合并到uniqueBudgetMap
            List<Map<String, Object>> wbsBudgetBaselineMapList = findWbsBudgetBaselineMapsByWebfront(importResponseExcelVO.getProjectId());
            if (!CollectionUtils.isEmpty(wbsBudgetBaselineMapList)) {
                ProjectWbsBudgetUtils.wbsBaseline2UniqueMap(wbsBudgetBaselineMapList, uniqueBudgetMap, uniqueBaselineMap, removeBaselineMapList, wbsDynamicFieldList);
                for (String key : uniqueBaselineMap.keySet()) {
                    Map<String, Object> uniqueBaseline = uniqueBaselineMap.get(key);
                    // 默认 预算基线-变更后 = 0
                    uniqueBaseline.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, BigDecimal.ZERO);
                    uniqueBaselineMap.put(key, uniqueBaseline);
                }
            }
        }
        for (Map excel : excelMapList) {
            // 唯一key = wbs动态列 + activity活动事项 + 批次号
            String uniqueBaselineKey = ProjectWbsBudgetUtils.getWbsBaselineUniqueKey(excel, wbsDynamicFieldList, importResponseExcelVO.getBatchCode(), null);

            Map baselineMap = new HashMap();
            if (uniqueBaselineMap.containsKey(uniqueBaselineKey)) {
                baselineMap = uniqueBaselineMap.get(uniqueBaselineKey);
                // 前端预算基线-变更后
                BigDecimal afterChangeBaselineCost = BigDecimal.valueOf(MapUtils.getDouble(baselineMap, WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST));
                // excel预算基线
                BigDecimal excelBaselineCost = BigDecimal.valueOf(MapUtils.getDouble(excel, WbsBudgetFieldConstant.BASELINE_COST));
                // 修改记录：预算基线-变更后 = excel的预算基线汇总
                baselineMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, afterChangeBaselineCost.add(excelBaselineCost));
            } else {
                baselineMap.putAll(excel);
                // 新增记录：预算基线 = 0
                baselineMap.put(WbsBudgetFieldConstant.BASELINE_COST, BigDecimal.ZERO);
                // 新增记录：预算基线-变更后 = excel的预算基线
                baselineMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, BigDecimal.valueOf(MapUtils.getDouble(excel, WbsBudgetFieldConstant.BASELINE_COST)));
            }
            String descriptionString = "";
            Object description = baselineMap.get(WbsBudgetFieldConstant.DESCRIPTION);
            if (description != null){
                descriptionString = description.toString();
            }
            //如果开启了自定义描述，则通过自定义描述返回，否则返回原来末级的描述
            descriptionRuleSet(wbsTemplateInfoId, describeDisplay, baselineMap,descriptionString);
            // 预算默认2位小数
            baselineMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, BigDecimal.valueOf(MapUtils.getDouble(baselineMap, WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST)).setScale(2, RoundingMode.HALF_UP).toPlainString());
            // 默认rowId
            baselineMap.put(WbsBudgetFieldConstant.ROW_ID, baselineMap.containsKey(WbsBudgetFieldConstant.ID) ?
                    baselineMap.get(WbsBudgetFieldConstant.ID) : UUID.randomUUID().toString());

            /* 基线行关联预算行的rowId */
            String uniqueBudgetKey = ProjectWbsBudgetUtils.getWbsBudgetUniqueKey(baselineMap, wbsDynamicFieldList, null);
            Map wbsBudget = uniqueBudgetMap.get(uniqueBudgetKey);
            if (StringUtils.isNotBlank(MapUtils.getString(wbsBudget, WbsBudgetFieldConstant.ROW_ID))) {
                // 关联预算的rowId
                baselineMap.put(WbsBudgetFieldConstant.PROJECT_WBS_BUDGET_ID, MapUtils.getString(wbsBudget, WbsBudgetFieldConstant.ROW_ID));
                // 基线的描述取预算的描述
                baselineMap.put(WbsBudgetFieldConstant.DESCRIPTION, wbsBudget.get(WbsBudgetFieldConstant.DESCRIPTION));
            }
            uniqueBaselineMap.put(uniqueBaselineKey, baselineMap);
        }
        /* 统计计算后的基线 */
        for (Map uniqueBaseline : uniqueBaselineMap.values()) {
            String uniqueBudgetKey = ProjectWbsBudgetUtils.getWbsBudgetUniqueKey(uniqueBaseline, wbsDynamicFieldList, null);
            Map<String, BigDecimal> sumMap = new HashMap<>();
            if (sumBaselineCostMap.containsKey(uniqueBudgetKey)) {
                sumMap = sumBaselineCostMap.get(uniqueBudgetKey);
            }
            // 变更前基线
            BigDecimal baselineCost = StringUtils.isBlank(MapUtils.getString(uniqueBaseline, WbsBudgetFieldConstant.BASELINE_COST)) ? BigDecimal.ZERO : new BigDecimal(MapUtils.getString(uniqueBaseline, WbsBudgetFieldConstant.BASELINE_COST));
            // 变更后基线
            BigDecimal afterChangeBaseCost = StringUtils.isBlank(MapUtils.getString(uniqueBaseline, WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST)) ? BigDecimal.ZERO : new BigDecimal(MapUtils.getString(uniqueBaseline, WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST));
            // 变更基线差异[变更后-变更前]（汇总）
            BigDecimal sumDiffBaselineCost = StringUtils.isBlank(MapUtils.getString(sumMap, WbsBudgetFieldConstant.DIFF_BASELINE_COST)) ? BigDecimal.ZERO : new BigDecimal(MapUtils.getString(sumMap, WbsBudgetFieldConstant.DIFF_BASELINE_COST));
            // 变更基线差异[变更后-变更前]累加
            sumMap.put(WbsBudgetFieldConstant.DIFF_BASELINE_COST, sumDiffBaselineCost.add(afterChangeBaseCost.subtract(baselineCost)));
            sumBaselineCostMap.put(uniqueBudgetKey, sumMap);
        }

        baselineResultList.addAll(uniqueBaselineMap.values());
        if (!CollectionUtils.isEmpty(removeBaselineMapList)) {
            baselineResultList.addAll(removeBaselineMapList);
        }
        return baselineResultList;
    }

    /**
     * 预立项转正
     * 获取前端格式的wbs预算关联信息
     *
     * @param projectId
     * @return
     */
    @Override
    public ProjectWbsBudgetObjectDto findPreWbsBudgetInfoByWebfront(Long projectId) {
        ProjectWbsBudgetObjectDto result = projectWbsBudgetService.findWbsBudgetInfoByWebfront(projectId);
        if (null != result && !CollectionUtils.isEmpty(result.getWbsBudgetList())) {
            for (Map<String, Object> wbsBudget : result.getWbsBudgetList()) {
                wbsBudget.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, wbsBudget.get(WbsBudgetFieldConstant.PRICE));
            }
        }
        if (null != result && !CollectionUtils.isEmpty(result.getWbsBudgetBaselineList())) {
            for (Map<String, Object> wbsBaseline : result.getWbsBudgetBaselineList()) {
                wbsBaseline.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, wbsBaseline.get(WbsBudgetFieldConstant.BASELINE_COST));
            }
        }
        return result;
    }

    /**
     * 批量导入
     *
     * @param dataList
     * @param batchSize
     * @return
     */
    @Override
    public int batchInsert(List<ProjectWbsBudgetBaseline> dataList, int batchSize) {
        if (CollectionUtils.isEmpty(dataList)) {
            return 0;
        }
        if (dataList.size() <= batchSize) {
            return projectWbsBudgetBaselineExtMapper.batchInsert(dataList);
        }
        int batchNum = dataList.size() / batchSize;
        int n = dataList.size() % batchSize;
        if (n > 0) {
            batchNum = batchNum + 1;
        }
        int resultNum = 0;
        for (int i = 0; i < batchNum; i++) {
            List<ProjectWbsBudgetBaseline> subList = subList(dataList, (i + 1), batchSize);
            resultNum = resultNum + projectWbsBudgetBaselineExtMapper.batchInsert(subList);
        }
        return resultNum;
    }

    /**
     * 导入wbs基线 - 预算变更场景
     *
     * @param importResponseExcelVO 导入参数
     * @return
     */
    @Override
    public ProjectWbsBudgetImportResponseExcelVO changeImportCheck(ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO) {
        Long wbsTemplateInfoId = importResponseExcelVO.getWbsTemplateInfoId();
        //校验是否已经开启描述自定义
        String describeDisplay ="";
        WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(wbsTemplateInfoId);
        if (null != wbsTemplateInfo){
            describeDisplay = wbsTemplateInfo.getDescribeDisplay();
        }

        ProjectWbsBudgetImportResponseExcelVO result = new ProjectWbsBudgetImportResponseExcelVO();
        /* wbs预算基线（前端页面数据） */
        List<Map<String, Object>> pageBaselineMapList = importResponseExcelVO.getPageBaselineMapList();
        if (CollectionUtils.isEmpty(pageBaselineMapList)) {
            ProjectWbsBudgetObjectDto preWbsBudgetInfoByWebfront = findPreWbsBudgetInfoByWebfront(importResponseExcelVO.getProjectId());
            pageBaselineMapList = preWbsBudgetInfoByWebfront.getWbsBudgetBaselineList();
        }
        /* Excel导入记录 */
        List<Map> excelMapList = importResponseExcelVO.getExcelMapList();
        // wbs动态列
        List<WbsDynamicFieldsDto> wbsDynamicFieldList = wbsTemplateRuleService.getWbsDynamicFields(importResponseExcelVO.getWbsTemplateInfoId());
        // 检查excel，设置默认值
        List<String> errorMsgList = projectWbsBudgetService.checkImportExcelAndInit(importResponseExcelVO.getBatchCode(), excelMapList, importResponseExcelVO.getWbsTemplateInfoId(), true);
        if (!CollectionUtils.isEmpty(errorMsgList)) {
            result.setErrorMsg(errorMsgList);
            return result;
        }
        // wbs预算Map，只存有效记录（增量或覆盖导入）
        Map<String, Map> uniqueBudgetMap = new HashMap();

        /**
         * Map存放计算后的基线
         * k = (wbs+activity)
         * hk = [diffBaselineCost变更基线差异]    // 后面的没有做统计 [baselineCost变更前基线] [afterChangeBaselineCost变更后基线]
         * hv = bigdecimal
         */
        // wbs预算集合，包含已删除的wbs预算
        calculateWbsBaseline(importResponseExcelVO,
                excelMapList,
                pageBaselineMapList,
                uniqueBudgetMap,
                wbsDynamicFieldList,
                errorMsgList,
                wbsTemplateInfoId,
                describeDisplay);
        if (!CollectionUtils.isEmpty(errorMsgList)) {
            result.setErrorMsg(errorMsgList);
            return result;
        }
        return result;
    }

    /**
     * 导入wbs基线 - 预算变更场景
     *
     * @param importResponseExcelVO 导入参数
     * @return
     */
    @Override
    public ProjectWbsBudgetImportResponseExcelVO changeImport(ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO) {
        Long wbsTemplateInfoId = importResponseExcelVO.getWbsTemplateInfoId();
        //校验是否已经开启描述自定义
        String describeDisplay ="";
        WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(wbsTemplateInfoId);
        if (null != wbsTemplateInfo){
             describeDisplay = wbsTemplateInfo.getDescribeDisplay();
        }
        ProjectWbsBudgetImportResponseExcelVO result = new ProjectWbsBudgetImportResponseExcelVO();
        /* wbs预算基线（前端页面数据） */
        List<Map<String, Object>> pageBaselineMapList = importResponseExcelVO.getPageBaselineMapList();
        if (CollectionUtils.isEmpty(pageBaselineMapList)) {
            ProjectWbsBudgetObjectDto preWbsBudgetInfoByWebfront = findPreWbsBudgetInfoByWebfront(importResponseExcelVO.getProjectId());
            pageBaselineMapList = preWbsBudgetInfoByWebfront.getWbsBudgetBaselineList();
        }
        /* Excel导入记录 */
        List<Map> excelMapList = importResponseExcelVO.getExcelMapList();
        // wbs动态列
        List<WbsDynamicFieldsDto> wbsDynamicFieldList = wbsTemplateRuleService.getWbsDynamicFields(importResponseExcelVO.getWbsTemplateInfoId());
        // wbs预算Map，只存有效记录（增量或覆盖导入）
        Map<String, Map> uniqueBudgetMap = new HashMap();
        // 获取数据库中的预算，合并到uniqueBudgetMap
        List<Map<String, Object>> wbsBudgetBaselineMapList = projectWbsBudgetService.findWbsBudgetMapsByWebfront(importResponseExcelVO.getProjectId());
        if (!CollectionUtils.isEmpty(wbsBudgetBaselineMapList)) {
            ProjectWbsBudgetUtils.wbsBudget2UniqueMap(wbsBudgetBaselineMapList, uniqueBudgetMap, new ArrayList<>(), wbsDynamicFieldList);
        }
        /**
         * Map存放计算后的基线
         * k = (wbs+activity)
         * hk = [diffBaselineCost变更基线差异]    // 后面的没有做统计 [baselineCost变更前基线] [afterChangeBaselineCost变更后基线]
         * hv = bigdecimal
         */
        // wbs预算集合，包含已删除的wbs预算
        List<Map> baselineResultList = calculateWbsBaseline(importResponseExcelVO,
                excelMapList,
                pageBaselineMapList,
                uniqueBudgetMap,
                wbsDynamicFieldList,
                new ArrayList<>(),
                wbsTemplateInfoId,
                describeDisplay);
        result.setWbsBudgetBaselineList(baselineResultList);
        return result;
    }

    /**
     * wbs基线（增量或覆盖导入）
     * 统计变更前基线，变更后基线
     *
     * @param importResponseExcelVO 导入参数
     * @param excelMapList          Excel导入记录
     * @param pageBaselineMapList   前端基线记录
     * @param uniqueBudgetMap       wbs预算Map，只存有效记录（增量或覆盖导入）
     * @param wbsDynamicFieldList   wbs动态列
     * @return
     */
    private List<Map> calculateWbsBaseline(ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO,
                                           List<Map> excelMapList,
                                           List<Map<String, Object>> pageBaselineMapList,
                                           Map<String, Map> uniqueBudgetMap,
                                           List<WbsDynamicFieldsDto> wbsDynamicFieldList, List<String> errorMsgList,
                                           Long wbsTemplateInfoId,
                                           String describeDisplay) {

        // wbs基线（增量或覆盖导入） 包含已删除的wbs基线
        List<Map> baselineResultList = new ArrayList<>();
        // 已删除的wbs基线
        List<Map> removeBaselineMapList = new ArrayList<>();
        // wbs基线Map，只存有效记录（增量或覆盖导入）
        Map<String, Map> uniqueBaselineMap = new HashMap();

        /* 增量导入 */
        if (WbsBudgetFieldConstant.ADD.equals(importResponseExcelVO.getImportType()) && !CollectionUtils.isEmpty(pageBaselineMapList)) {
            // 前端基线合并到uniqueMap
            ProjectWbsBudgetUtils.wbsBaseline2UniqueMap(pageBaselineMapList, uniqueBudgetMap, uniqueBaselineMap, removeBaselineMapList, wbsDynamicFieldList);
        }
        /* 覆盖导入 */
        else {
            // 获取数据库中的预算基线，合并到uniqueBudgetMap
            List<Map<String, Object>> wbsBudgetBaselineMapList = findWbsBudgetBaselineMapsByWebfront(importResponseExcelVO.getProjectId());
            if (!CollectionUtils.isEmpty(wbsBudgetBaselineMapList)) {
                ProjectWbsBudgetUtils.wbsBaseline2UniqueMap(wbsBudgetBaselineMapList, uniqueBudgetMap, uniqueBaselineMap, removeBaselineMapList, wbsDynamicFieldList);
                for (String key : uniqueBaselineMap.keySet()) {
                    Map<String, Object> uniqueBaseline = uniqueBaselineMap.get(key);
                    // 默认 预算基线-变更后 = 0
                    uniqueBaseline.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, BigDecimal.ZERO);
                    uniqueBaselineMap.put(key, uniqueBaseline);
                }
            }
        }
        for (Map excel : excelMapList) {
            // 唯一key = wbs动态列 + activity活动事项 + 批次号
            String uniqueBaselineKey = ProjectWbsBudgetUtils.getWbsBaselineUniqueKey(excel, wbsDynamicFieldList, importResponseExcelVO.getBatchCode(), null);

            Map baselineMap = new HashMap();
            BigDecimal newAfterChangeBaselineCost = BigDecimal.ZERO;
            if (uniqueBaselineMap.containsKey(uniqueBaselineKey)) {
                baselineMap = uniqueBaselineMap.get(uniqueBaselineKey);
                // 前端预算基线-变更后
                BigDecimal afterChangeBaselineCost = BigDecimal.valueOf(MapUtils.getDouble(baselineMap, WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST));
                // excel预算基线
                BigDecimal excelBaselineCost = BigDecimal.valueOf(MapUtils.getDouble(excel, WbsBudgetFieldConstant.BASELINE_COST));
                // 修改记录：预算基线-变更后 = excel的预算基线汇总
                newAfterChangeBaselineCost = afterChangeBaselineCost.add(excelBaselineCost);
            } else {
                baselineMap.putAll(excel);
                // 新增记录：预算基线 = 0
                baselineMap.put(WbsBudgetFieldConstant.BASELINE_COST, BigDecimal.ZERO);
                // 新增记录：预算基线-变更后 = excel的预算基线
                newAfterChangeBaselineCost = BigDecimal.valueOf(MapUtils.getDouble(excel, WbsBudgetFieldConstant.BASELINE_COST));
            }
            // 负数检查
            if (BigDecimalUtils.isLess(newAfterChangeBaselineCost, BigDecimal.ZERO)) {
                // 尝试从 uniqueBaselineMap 中查找相同 wbs 动态列 + activity 活动事项，但不同批次号的记录
                String baseKeyWithoutBatch = ProjectWbsBudgetUtils.getWbsBaselineUniqueKey(excel, wbsDynamicFieldList, importResponseExcelVO.getProjectCode(), null);
                // 循环查找，直到 newAfterChangeBaselineCost 为 0
                while (BigDecimalUtils.isLess(newAfterChangeBaselineCost, BigDecimal.ZERO)) {
                    boolean foundValidRecord = false;
                    for (Map.Entry<String, Map> entry : uniqueBaselineMap.entrySet()) {
                        if (entry.getKey().startsWith(baseKeyWithoutBatch) && !entry.getKey().equals(uniqueBaselineKey)) {
                            Map otherBaselineMap = entry.getValue();
                            BigDecimal otherAfterChangeBaselineCost = BigDecimal.valueOf(MapUtils.getDouble(otherBaselineMap, WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST));
                            BigDecimal add = BigDecimalUtils.add(otherAfterChangeBaselineCost, newAfterChangeBaselineCost);

                            // 如果 otherAfterChangeBaselineCost 大于零，才设置 foundValidRecord 为 true
                            if (BigDecimalUtils.isGreater(otherAfterChangeBaselineCost, BigDecimal.ZERO)) {
                                foundValidRecord = true;
                                // 如果 add 大于零，则退出循环
                                if (BigDecimalUtils.isGreater(add, BigDecimal.ZERO)) {
                                    newAfterChangeBaselineCost = BigDecimal.ZERO;
                                    otherBaselineMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, add);
                                    break;
                                } else {
                                    newAfterChangeBaselineCost = add;
                                    otherBaselineMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, BigDecimal.ZERO);
                                }
                            }
                        }
                    }
                    // 如果没有找到有效记录，退出循环
                    if (!foundValidRecord) {
                        break;
                    }
                }
                // 重新检查是否为负数
                if (BigDecimalUtils.isLess(newAfterChangeBaselineCost, BigDecimal.ZERO)) {
                    errorMsgList.add(String.format("行%s列%s，活动事项%s，基线变更后的金额不能为负数",
                            excel.get(WbsBudgetFieldConstant.EXCEL_ROW_NUM),
                            excel.get(WbsBudgetFieldConstant.ACTIVITY_NAME),
                            excel.get(WbsBudgetFieldConstant.ACTIVITY_CODE)));
                    continue;
                }
            }
            baselineMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, newAfterChangeBaselineCost);
            // 预算默认2位小数
            baselineMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST, BigDecimal.valueOf(MapUtils.getDouble(baselineMap, WbsBudgetFieldConstant.AFTER_CHANGE_BASELINE_COST)).setScale(2, RoundingMode.HALF_UP).toPlainString());
            // 默认rowId
            baselineMap.put(WbsBudgetFieldConstant.ROW_ID, baselineMap.containsKey(WbsBudgetFieldConstant.ID) ?
                    baselineMap.get(WbsBudgetFieldConstant.ID) : UUID.randomUUID().toString());

            String descriptionString = "";
            TreeMap<Integer, String> fieldValueMap = new TreeMap<>();
            for (Object keyObj : baselineMap.keySet()) {
                String key = keyObj.toString();
                if (key.startsWith("field_")) {
                    int fieldNum = Integer.parseInt(key.substring(6));
                    String fieldValue = baselineMap.get(key).toString();
                    fieldValueMap.put(fieldNum, fieldValue);
                }
            }
            if (!fieldValueMap.isEmpty()) {
                // 获取动态列规则列表
                WbsTemplateRuleDto param = new WbsTemplateRuleDto();
                param.setDeletedFlag(false);
                param.setWbsTemplateInfoId(wbsTemplateInfoId);
                List<WbsTemplateRuleDto> wbsRuleList = wbsTemplateRuleExtMapper.selectList(param);
                if (CollectionUtils.isNotEmpty(wbsRuleList)) {
                    Collections.sort(wbsRuleList);
                    List<WbsDynamicFieldsDto> resultList = new ArrayList<>();
                    for (WbsTemplateRuleDto rule : wbsRuleList) {
                        if ("1".equals(rule.getOrderNo()) && "项目".equals(rule.getRuleName())) {
                            continue;
                        }
                        WbsDynamicFieldsDto dto = new WbsDynamicFieldsDto();
                        dto.setKey("field_" + rule.getOrderNo());
                        dto.setWbsTemplateRuleId(rule.getId());
                        dto.setLable(rule.getRuleName());
                        resultList.add(dto);
                    }

                    for (Integer currentFieldNum : fieldValueMap.descendingKeySet()) {
                        String currentFieldValue = fieldValueMap.get(currentFieldNum);

                        for (WbsDynamicFieldsDto dto : resultList) {
                            int dtoFieldNum = Integer.parseInt(dto.getKey().substring(6));
                            if (dtoFieldNum == currentFieldNum) {
                                WbsTemplateRuleDetailExample example = new WbsTemplateRuleDetailExample();
                                example.createCriteria()
                                        .andWbsTemplateRuleIdEqualTo(dto.getWbsTemplateRuleId())
                                        .andCodeEqualTo(currentFieldValue)
                                        .andDeletedFlagEqualTo(false);

                                List<WbsTemplateRuleDetail> details = wbsTemplateRuleDetailMapper.selectByExample(example);
                                if (CollectionUtils.isNotEmpty(details)) {
                                    descriptionString = details.get(0).getDescription();
                                    if (StringUtils.isNotBlank(descriptionString)) {
                                        break;
                                    }
                                }
                            }
                        }

                        if (StringUtils.isNotBlank(descriptionString)) {
                            break;
                        }
                    }
                }
            }


            if (DescribeDisplayEnum.CUSTOMIZE.getCode().equals(describeDisplay)) {
                String dynamicCombination = String.join("-", fieldValueMap.values());
                if (!dynamicCombination.isEmpty()) {
                    WbsCustomizeRuleExample example = new WbsCustomizeRuleExample();
                    example.createCriteria()
                            .andDynamicCombinationEqualTo(dynamicCombination)
                            .andWbsTemplateInfoIdEqualTo(wbsTemplateInfoId)
                            .andDeletedFlagEqualTo(false);

                    List<WbsCustomizeRule> rules = wbsCustomizeRuleMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(rules)) {
                        baselineMap.put(WbsBudgetFieldConstant.DESCRIPTION, rules.get(0).getDescription());
                    } else {
                        // 如果没有查询到自定义描述，可以设置为空或保持末级带出来的描述
                        baselineMap.put(WbsBudgetFieldConstant.DESCRIPTION, descriptionString);
                    }
                }
            }else {
                baselineMap.put(WbsBudgetFieldConstant.DESCRIPTION, descriptionString);
            }

            /* 基线行关联预算行的rowId */
            String uniqueBudgetKey = ProjectWbsBudgetUtils.getWbsBudgetUniqueKey(baselineMap, wbsDynamicFieldList, null);
            Map wbsBudget = uniqueBudgetMap.get(uniqueBudgetKey);
            if (StringUtils.isNotBlank(MapUtils.getString(wbsBudget, WbsBudgetFieldConstant.ROW_ID))) {
                // 关联预算的rowId
                baselineMap.put(WbsBudgetFieldConstant.PROJECT_WBS_BUDGET_ID, MapUtils.getString(wbsBudget, WbsBudgetFieldConstant.ROW_ID));
                // 基线的描述取预算的描述
                if (!DescribeDisplayEnum.CUSTOMIZE.getCode().equals(describeDisplay)){
                    baselineMap.put(WbsBudgetFieldConstant.DESCRIPTION, wbsBudget.get(WbsBudgetFieldConstant.DESCRIPTION));
                }
            }
            uniqueBaselineMap.put(uniqueBaselineKey, baselineMap);
        }
        baselineResultList.addAll(uniqueBaselineMap.values());
        if (!CollectionUtils.isEmpty(removeBaselineMapList)) {
            baselineResultList.addAll(removeBaselineMapList);
        }
        return baselineResultList;
    }

    /**
     * 预算基线变更保存
     *
     * @param projectInfo 项目数据
     */
    @Transactional
    @Override
    public Long saveChange(ProjectDto projectInfo) {
        Asserts.notNull(projectInfo, ErrorCode.CTC_PROJECT_NOT_NULL);
        Project project = projectMapper.selectByPrimaryKey(projectInfo.getId());
        Assert.notNull(project, "项目不存在" + projectInfo.getId());
        Assert.isTrue(Objects.equals(project.getWbsEnabled(), Boolean.TRUE), "项目类型未启用wbs");
        // 权限校验
        authCheck(project);

        Long type = project.getType();
        Long wbsTemplateInfoId = project.getWbsTemplateInfoId();
        // 修改变更头记录
        ProjectHistoryHeader header = buildProjectHistoryHeader(projectInfo.getId(), projectInfo.getReason(),
                projectInfo.getReasonType(), projectInfo.getHeaderId());
        //因只做了基线的交互，预算行需调整
        ProjectWbsBudgetObjectDto preWbsBudgetInfoByWebfront = findPreWbsBudgetInfoByWebfront(projectInfo.getId());
        List<Map<String, Object>> wbsBudgetList = preWbsBudgetInfoByWebfront.getWbsBudgetList();

        // 获取费用类型经济事项
        List<FeeItemExpenseTypeDto> feeItemExpenseTypeDtoList = projectTypeService.selectFeeItemExpenseType(type);
        // wbs动态列
        List<WbsDynamicFieldsDto> wbsDynamicFieldList = wbsTemplateRuleService.getWbsDynamicFields(wbsTemplateInfoId);

//        List<Map<String, Object>> beforeBaselineList = preWbsBudgetInfoByWebfront.getWbsBudgetBaselineList();
        List<Map<String, Object>> afterBaselineList = projectInfo.getProjectWbsBudgetObjectDTO().getWbsBudgetBaselineList();
        // 找出变更后多出的条目
        List<Map<String, Object>> addItems = findAddedItems(wbsBudgetList, afterBaselineList);

        for (Map<String, Object> addMap : addItems) {
            // 新增wbs预算，预算金额 = 0
            addMap.put(WbsBudgetFieldConstant.PRICE, BigDecimal.ZERO);
            // 新增wbs变更后预算
            addMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, BigDecimal.ZERO);

            addMap.put(WbsBudgetFieldConstant.ROW_ID, addMap.get(WbsBudgetFieldConstant.PROJECT_WBS_BUDGET_ID));
            // 设置经济事项
            projectWbsBudgetService.setFeeExpenseType(feeItemExpenseTypeDtoList, addMap, wbsDynamicFieldList);
            addMap.remove(WbsBudgetFieldConstant.PROJECT_WBS_BUDGET_ID);
            //构建新增的预算行
            wbsBudgetList.add(addMap);
        }
        projectInfo.getProjectWbsBudgetObjectDTO().setWbsBudgetList(wbsBudgetList);
        projectInfo.setWbsTemplateInfoId(wbsTemplateInfoId);
        // wbs预算变更（要在合同变更后面执行，因为要存项目金额）
        previewWbsBudgetInfoChangeService.savePreWbsBudgetChange(projectInfo, header);

        return header.getId();
    }

    @Override
    public ProjectWbsBaselineChangeDTO changeView(Long headerId, Boolean reEdit) {

        Guard.notNull(headerId, "参数headerId不能为空");
        ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(headerId);
        Guard.notNull(projectHistoryHeader, "变更记录不存在");
        Project project = projectMapper.selectByPrimaryKey(projectHistoryHeader.getProjectId());
        Guard.notNull(project, "项目不存在");

        /* 基线批次 */
        ProjectBaselineBatchExample projectBaselineBatchExample = new ProjectBaselineBatchExample();
        projectBaselineBatchExample.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectBaselineBatch> wbsBaselineBatchList = projectBaselineBatchMapper.selectByExample(projectBaselineBatchExample);


        ProjectWbsBudgetBaselineChangeHistoryExample wbsBudgetBaselineChangeHistoryExample = new ProjectWbsBudgetBaselineChangeHistoryExample();
        wbsBudgetBaselineChangeHistoryExample.createCriteria().andHeaderIdEqualTo(headerId).andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        wbsBudgetBaselineChangeHistoryExample.setOrderByClause(" id desc ");
        List<ProjectWbsBudgetBaselineChangeHistory> wbsBudgetBaselineChanges =
                projectWbsBudgetBaselineChangeHistoryMapper.selectByExample(wbsBudgetBaselineChangeHistoryExample);
        Guard.notNull(wbsBudgetBaselineChanges, "变更明细不存在");
        List<ProjectWbsBudgetBaselineChangeHistoryDto> changeHistoryList = BeanConverter.copy(wbsBudgetBaselineChanges, ProjectWbsBudgetBaselineChangeHistoryDto.class);

        List<ProjectWbsBudgetBaselineChangeHistoryDto> historyList = changeHistoryList.stream().filter(a -> a.getHistoryType().equals(HistoryType.HISTORY.getCode())).collect(Collectors.toList());
        List<ProjectWbsBudgetBaselineChangeHistoryDto> changeList = changeHistoryList.stream().filter(a -> a.getHistoryType().equals(HistoryType.CHANGE.getCode())).collect(Collectors.toList());
        //变更汇总
        BigDecimal sumBaselineCost = historyList.stream().map(ProjectWbsBudgetBaselineChangeHistoryDto::getBaselineCost).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        BigDecimal sumAfterChangeBaselineCost = changeList.stream().map(ProjectWbsBudgetBaselineChangeHistoryDto::getBaselineCost).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        BigDecimal sumChangePrice = sumAfterChangeBaselineCost.subtract(sumBaselineCost);

        ProjectWbsBaselineChangeDTO detail = new ProjectWbsBaselineChangeDTO();
        PreviewWbsBudgetChangeVo changeHistory;
        List<PreviewWbsBudgetChangeVo> previewWbsBudgetChangeVoList = new ArrayList<>();
        for (ProjectWbsBudgetBaselineChangeHistoryDto change : changeList) {
            changeHistory = new PreviewWbsBudgetChangeVo();
            // 变更后基线，为了方便前端查询组装的字段
            change.setAfterChangeBaselineCost(change.getBaselineCost().setScale(2, RoundingMode.HALF_UP));
            List<ProjectWbsBudgetBaselineChangeHistoryDto> filterHistoryList = new ArrayList<>();
            if (null != change.getOriginId()) {
                filterHistoryList = historyList.stream().filter(a -> a.getOriginId().equals(change.getOriginId())).collect(Collectors.toList());
            }

            if (null == change.getOriginId() || CollectionUtils.isEmpty(filterHistoryList)) {
                // 预算金额取0（方便前端展示）
                change.setBaselineCost(BigDecimal.ZERO);
                changeHistory.setType(ChangeType.ADD.code());
            } else {
                ProjectWbsBudgetBaselineChangeHistoryDto history = filterHistoryList.get(0);
                changeHistory.setHistory(ProjectWbsBudgetBaselineChangeHistoryDto.dto2Map(history));
                if (Boolean.TRUE.equals(change.getDeletedFlag())) {
                    // 【删除】
                    changeHistory.setType(ChangeType.DEL.code());
                } else {
                    if (BigDecimalUtils.equals(change.getBaselineCost(), filterHistoryList.get(0).getBaselineCost())) {
                        // 金额相同判定未发生变更,重新编辑需要返回未变更的项
                        if (Boolean.TRUE.equals(reEdit)) {
                            changeHistory.setType(ChangeType.NO.code());
                        } else {
                            continue;
                        }
                    } else {
                        // 【修改】
                        changeHistory.setType(ChangeType.UPDATE.code());
                    }
                }
            }
            changeHistory.setChange(ProjectWbsBudgetBaselineChangeHistoryDto.dto2Map(change));
            previewWbsBudgetChangeVoList.add(changeHistory);
        }

        detail.setHeaderId(headerId);
        detail.setProjectId(project.getId());
        detail.setProjectCode(project.getCode());
        detail.setProjectName(project.getName());
        detail.setWbsTemplateInfoId(project.getWbsTemplateInfoId());
        detail.setReasonType(projectHistoryHeader.getReasonType());
        detail.setReason(projectHistoryHeader.getReason());
        detail.setPreviewWbsBudgetChangeVoList(previewWbsBudgetChangeVoList);
        detail.setWbsBaselineBatchList(wbsBaselineBatchList);
        detail.setProjectType(project.getType());
        detail.setSumBaselineCost(sumBaselineCost.stripTrailingZeros().toPlainString());
        detail.setSumAfterChangeBaselineCost(sumAfterChangeBaselineCost.stripTrailingZeros().toPlainString());
        detail.setSumChangePrice(sumChangePrice.stripTrailingZeros().toPlainString());
        return detail;
    }

    @Override
    public ResponseMap getBudgetBaselineChangeApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();

        Guard.notNull(id, "参数Id不能为空");
        ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(id);
        Guard.notNull(projectHistoryHeader, "变更记录不存在");
        Project project = projectMapper.selectByPrimaryKey(projectHistoryHeader.getProjectId());
        Guard.notNull(project, "项目不存在");

        //变更原因
        headMap.put("name", project.getName()); //项目名称
        headMap.put("code", project.getCode()); // 项目编号
        headMap.put("type", "基线变更-预算"); // 变更类型
        headMap.put("currency", project.getCurrency()); // 币种
        headMap.put("reasonType", projectHistoryHeader.getReasonType());//变更原因
        headMap.put("reason", projectHistoryHeader.getReason());//变更说明

        ProjectWbsBudgetBaselineChangeHistoryExample wbsBudgetBaselineChangeHistoryExample = new ProjectWbsBudgetBaselineChangeHistoryExample();
        wbsBudgetBaselineChangeHistoryExample.createCriteria().andHeaderIdEqualTo(projectHistoryHeader.getId()).andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        wbsBudgetBaselineChangeHistoryExample.setOrderByClause(" id desc ");
        List<ProjectWbsBudgetBaselineChangeHistory> wbsBudgetBaselineChanges = projectWbsBudgetBaselineChangeHistoryMapper.selectByExample(wbsBudgetBaselineChangeHistoryExample);
        Guard.notNull(wbsBudgetBaselineChanges, "变更明细不存在");
        List<ProjectWbsBudgetBaselineChangeHistoryDto> changeHistoryList = BeanConverter.copy(wbsBudgetBaselineChanges, ProjectWbsBudgetBaselineChangeHistoryDto.class);

        List<ProjectWbsBudgetBaselineChangeHistoryDto> historyList = changeHistoryList.stream().filter(a -> a.getHistoryType().equals(HistoryType.HISTORY.getCode())).collect(Collectors.toList());
        List<ProjectWbsBudgetBaselineChangeHistoryDto> changeList = changeHistoryList.stream().filter(a -> a.getHistoryType().equals(HistoryType.CHANGE.getCode())).collect(Collectors.toList());

        //变更汇总
        BigDecimal sumBaselineCost = historyList.stream().map(ProjectWbsBudgetBaselineChangeHistoryDto::getBaselineCost).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        BigDecimal sumAfterChangeBaselineCost = changeList.stream().map(ProjectWbsBudgetBaselineChangeHistoryDto::getBaselineCost).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        BigDecimal sumChangePrice = sumAfterChangeBaselineCost.subtract(sumBaselineCost);

        //变更内容
        List<Map<String, String>> list1 = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(changeList)) {
            for (ProjectWbsBudgetBaselineChangeHistoryDto change : changeList) {
                Map<String, String> detailMap = new HashMap<>();
                detailMap.put("wbsSummaryCode", project.getCode() + "-" + change.getWbsFullCode()); //wbs
                detailMap.put("description", change.getDescription()); //描述
                detailMap.put("activityCode", change.getActivityCode()); //活动事项
                detailMap.put("wbsBaselineBatch", change.getProjectBaselineBatchCode()); //预算基线批次
                BigDecimal baselineCost;
                BigDecimal afterChangeBaselineCost;
                BigDecimal changePrice;
                List<ProjectWbsBudgetBaselineChangeHistoryDto> filterHistoryList = new ArrayList<>();
                if (null != change.getOriginId()) {
                    filterHistoryList = historyList.stream().filter(a -> a.getOriginId().equals(change.getOriginId())).collect(Collectors.toList());
                }
                if (null == change.getOriginId() || CollectionUtils.isEmpty(filterHistoryList)) {
                    baselineCost = BigDecimal.ZERO;
                    afterChangeBaselineCost = change.getBaselineCost();
                } else {
                    ProjectWbsBudgetBaselineChangeHistoryDto history = filterHistoryList.get(0);
                    if (Boolean.TRUE.equals(change.getDeletedFlag())) {
                        // 【删除】
                        baselineCost = history.getBaselineCost();
                        afterChangeBaselineCost = BigDecimal.ZERO;
                    } else {
                        if (BigDecimalUtils.equals(change.getBaselineCost(), filterHistoryList.get(0).getBaselineCost())) {
                            // 金额相同
                            continue;
                        } else {
                            // 【修改】
                            baselineCost = history.getBaselineCost();
                            afterChangeBaselineCost = change.getBaselineCost();
                        }
                    }
                }
                changePrice = afterChangeBaselineCost.subtract(baselineCost);
                detailMap.put("baselineCost", baselineCost.stripTrailingZeros().toPlainString()); //变更前-预算基线
                detailMap.put("afterChangeBaselineCost", afterChangeBaselineCost.stripTrailingZeros().toPlainString()); //变更后-预算基线
                detailMap.put("changePrice", changePrice.stripTrailingZeros().toPlainString()); //变更额
                list1.add(detailMap);
            }
            responseMap.setList1(list1);
        }
        headMap.put("sumBaselineCost", sumBaselineCost.stripTrailingZeros().toPlainString());//变更前-预算基线
        headMap.put("sumAfterChangeBaselineCost", sumAfterChangeBaselineCost.stripTrailingZeros().toPlainString());//变更后-预算基线
        headMap.put("sumChangePrice", sumChangePrice.stripTrailingZeros().toPlainString());//变更额
        responseMap.setHeadMap(headMap);

        responseMap.setMsg("成功");
        responseMap.setStatus("success");
        return responseMap;
    }

    @Override
    public Boolean budgetBaselineChangeCheck(Long id) {
        Guard.notNull(id, "参数Id不能为空");
        ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(id);
        Guard.notNull(projectHistoryHeader, "变更记录不存在");
        Project project = projectMapper.selectByPrimaryKey(projectHistoryHeader.getProjectId());
        Assert.isTrue(Objects.equals(project.getWbsEnabled(), Boolean.TRUE), "项目类型未启用wbs");
        // 权限校验
        authCheck(project);
        return true;
    }

    private List<Map<String, Object>> findAddedItems(List<Map<String, Object>> beforeList, List<Map<String, Object>> afterList) {
        // 使用 HashSet 来存储变更前的 projectWbsBudgetId
        HashMap<String, Map<String, Object>> beforeMap = new HashMap<>();
        // 将变更前列表的 projectWbsBudgetId 存储到 map 中
        for (Map<String, Object> item : beforeList) {
            String projectWbsBudgetId = String.valueOf(item.get("id"));
            beforeMap.put(projectWbsBudgetId, item);
        }

        // 存储变更后列表中多出的项目
        List<Map<String, Object>> addedItems = new ArrayList<>();

        // 遍历变更后列表，找出不在变更前列表中的项目
        for (Map<String, Object> item : afterList) {
            String projectWbsBudgetId = String.valueOf(item.get("projectWbsBudgetId"));
            Boolean deletedFlag = (Boolean) item.get("deletedFlag");
            if (deletedFlag == null) {
                deletedFlag = false;
            }
            if (!beforeMap.containsKey(projectWbsBudgetId) && !deletedFlag) {
                addedItems.add(item);
            }
        }
        return addedItems;
    }

    /**
     * 创建变更信息头表
     * 如果是变更变更就删除行结构
     *
     * @param projectId 项目id
     * @param reason    变更原因
     * @param headerId  头id有就从数据库拿并且清除行数据
     * @return 变更头
     */
    private ProjectHistoryHeader buildProjectHistoryHeader(Long projectId, String reason,
                                                           String reasonType, Long headerId) {

        if (headerId != null) {
            ProjectHistoryHeader header = projectHistoryHeaderMapper.selectByPrimaryKey(headerId);
            if (!Arrays.asList(ProjectChangeStatus.REFUSE.getCode(), ProjectChangeStatus.RETURN.getCode(), ProjectChangeStatus.DRAFT.getCode()).
                    contains(header.getStatus())) {
                throw new BizException(Code.ERROR, "该变更记录已失效或审批");
            }
//            projectWbsBudgetBaselineChangeHistoryExtMapper.deleteByHeaderId(headerId);
            //审批回调会修改状态
//            header.setStatus(ProjectStatus.APPROVALING.getCode());
            header.setReason(reason);
            header.setReasonType(reasonType);
            projectHistoryHeaderMapper.updateByPrimaryKeySelective(header);
            return header;
        }
        ProjectHistoryHeader header = new ProjectHistoryHeader();
        header.setChangeType(CtcProjectChangeType.WBS_BUDGET_BASE_LINE_CHANGE.getCode());
        header.setDeletedFlag(false);
        header.setProjectId(projectId);
        header.setStatus(ProjectChangeStatus.DRAFT.getCode());
        header.setReason(reason);
        header.setReasonType(reasonType);
        projectHistoryHeaderMapper.insertSelective(header);
        return header;
    }

    private List<ProjectWbsBudgetBaseline> subList(List<ProjectWbsBudgetBaseline> dataList, int batchNum, int batchSize) {
        int size = dataList.size();
        int startIndex = (batchNum - 1) * batchSize;
        int endIndex = batchNum * batchSize;
        if (endIndex > size) {
            endIndex = size;
        }
        return dataList.subList(startIndex, endIndex);
    }

    /**
     * 权限校验
     *
     * @param project
     */
    private void authCheck(Project project) {
        final Long managerId = project.getManagerId();
        final Long financial = project.getFinancial();
        final Long userId = SystemContext.getUserId();
        Unit unit = unitExtMapper.selectByPrimaryKey(project.getUnitId());
        OrganizationCustomDictExample ocdExample = new OrganizationCustomDictExample();
        ocdExample.createCriteria().andDeletedFlagEqualTo(false).andOrgIdEqualTo(unit.getParentId()).andNameEqualTo("项目财务是否允许预算变更");
        List<OrganizationCustomDict> organizationCustomDictList = organizationCustomDictMapper.selectByExample(ocdExample);
        if (CollectionUtils.isEmpty(organizationCustomDictList) || !Objects.equals("1", organizationCustomDictList.get(0).getValue())) {
            Assert.isTrue(Objects.equals(managerId, userId), "只有项目经理可以发起项目预算变更");
        } else {
            Assert.isTrue(Objects.equals(managerId, userId) || Objects.equals(financial, userId), "只有项目经理或者项目财务可以发起项目预算变更");
        }
        final Integer status = project.getStatus();
        Assert.isTrue(Objects.equals(status, ProjectStatus.APPROVALED.getCode()), "项目进行中才能发起项目变更");
    }
}
