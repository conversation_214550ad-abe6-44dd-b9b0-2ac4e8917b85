package com.midea.pam.ctc.requirement.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsRequirementChangeRecordDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsRequirementChangeRecordRelationDto;
import com.midea.pam.common.ctc.dto.ProjectWbsSummaryDto;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummary;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummaryExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsRequirementChangeRecord;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsRequirementChangeRecordRelation;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsRequirementChangeRecordRelationExample;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProjectWbsBudgetSummarySummaryTypeEnums;
import com.midea.pam.common.enums.ProjectWbsReceiptsRequirementChangeRecordStatusEnum;
import com.midea.pam.common.enums.RequirementStatusEnum;
import com.midea.pam.common.statistics.vo.ProjectWbsCostByReceiptsVO;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.ProjectWbsReceiptsBudgetDemandTypeEnums;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsRequirementChangeRecordMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsRequirementChangeRecordRelationMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsBudgetChangeHistoryService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsRequirementChangeRecordRelationService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsRequirementChangeRecordService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ProjectWbsReceiptsRequirementChangeRecordServiceImpl implements ProjectWbsReceiptsRequirementChangeRecordService {

    private final static Logger logger = LoggerFactory.getLogger(ProjectWbsReceiptsRequirementChangeRecordServiceImpl.class);

    @Resource
    private ProjectWbsReceiptsRequirementChangeRecordMapper projectWbsReceiptsRequirementChangeRecordMapper;

    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;

    @Resource
    private ProjectWbsReceiptsBudgetChangeHistoryService projectWbsReceiptsBudgetChangeHistoryService;

    @Resource
    private ProjectWbsReceiptsBudgetMapper projectWbsReceiptsBudgetMapper;

    @Resource
    private ProjectWbsReceiptsRequirementChangeRecordRelationService projectWbsReceiptsRequirementChangeRecordRelationService;

    @Resource
    private ProjectWbsReceiptsRequirementChangeRecordRelationMapper projectWbsReceiptsRequirementChangeRecordRelationMapper;

    @Resource
    private ProjectWbsBudgetSummaryMapper projectWbsBudgetSummaryMapper;

    @Resource
    private CtcAttachmentService ctcAttachmentService;

    @Resource
    private RestTemplate restTemplate;

    @Override
    public ProjectWbsReceiptsRequirementChangeRecordDto add(ProjectWbsReceiptsRequirementChangeRecordDto dto) {
        dto.setDeletedFlag(Boolean.FALSE);
        ProjectWbsReceiptsRequirementChangeRecord entity = BeanConverter.copy(dto, ProjectWbsReceiptsRequirementChangeRecord.class);
        projectWbsReceiptsRequirementChangeRecordMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public ProjectWbsReceiptsRequirementChangeRecordDto update(ProjectWbsReceiptsRequirementChangeRecordDto dto) {
        ProjectWbsReceiptsRequirementChangeRecord entity = BeanConverter.copy(dto, ProjectWbsReceiptsRequirementChangeRecord.class);
        projectWbsReceiptsRequirementChangeRecordMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public ProjectWbsReceiptsRequirementChangeRecordDto save(ProjectWbsReceiptsRequirementChangeRecordDto dto, Long userBy) {
        if (dto.getId() == null) {
            if (null != userBy && null == dto.getCreateBy()) {
                dto.setCreateBy(userBy);
            }
            dto.setCreateAt(new Date());
            return this.add(dto);
        } else {
            if (null != userBy) {
                dto.setUpdateBy(userBy);
            }
            dto.setUpdateAt(new Date());
            return this.update(dto);
        }
    }

    @Override
    public ProjectWbsReceiptsRequirementChangeRecordDto getById(Long id) {
        return BeanConverter.copy(projectWbsReceiptsRequirementChangeRecordMapper.selectByPrimaryKey(id),
                ProjectWbsReceiptsRequirementChangeRecordDto.class);
    }

    @Override
    public List<ProjectWbsReceiptsRequirementChangeRecordDto> selectList(ProjectWbsReceiptsRequirementChangeRecordDto query) {
        return null;
    }

    @Override
    public PageInfo<ProjectWbsReceiptsRequirementChangeRecordDto> selectPage(ProjectWbsReceiptsRequirementChangeRecordDto query) {
        return null;
    }

    @Override
    public ProjectWbsReceiptsRequirementChangeRecordDto findRequirementDetailByProjectWbsReceiptsId(Long projectWbsReceiptsId) {
        Guard.notNull(projectWbsReceiptsId, "需求发布单据id不能为空");
        ProjectWbsReceiptsDto receipts = projectWbsReceiptsService.getById(projectWbsReceiptsId);
        Guard.notNull(receipts, "需求发布单据不存在");

        //当前的单据预算
        ProjectWbsReceiptsBudgetExample budgetExample = new ProjectWbsReceiptsBudgetExample();
        budgetExample.createCriteria().andProjectWbsReceiptsIdEqualTo(receipts.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsReceiptsBudget> budgetList = projectWbsReceiptsBudgetMapper.selectByExample(budgetExample);
        Guard.notNullOrEmpty(budgetList, "需求发布单据预算不存在");
        Map<Long, List<ProjectWbsReceiptsBudget>> budgetMap = budgetList.stream()
                .collect(Collectors.groupingBy(e -> Objects.isNull(e.getParentid()) ? -1L : e.getParentid()));
        List<ProjectWbsReceiptsBudgetDto> parentBudgetDtoList = new ArrayList<>();
        for (ProjectWbsReceiptsBudget budget : budgetList) {
            if (null == budget.getParentid() || -1 == budget.getParentid()) {
                ProjectWbsReceiptsBudgetDto dto = BeanConverter.copy(budget, ProjectWbsReceiptsBudgetDto.class);
                parentBudgetDtoList.add(dto);
            }
        }
        for (ProjectWbsReceiptsBudgetDto parentBudgetDto : parentBudgetDtoList) {
            List<ProjectWbsReceiptsBudget> childBudgetList = budgetMap.get(parentBudgetDto.getId());
            if (CollectionUtils.isNotEmpty(childBudgetList)) {
                List<ProjectWbsReceiptsBudgetDto> childDtoList = BeanConverter.copy(childBudgetList, ProjectWbsReceiptsBudgetDto.class);
                parentBudgetDto.addChilds(childDtoList);
            }
        }

        ProjectWbsReceiptsRequirementChangeRecordDto changeRecordDto = new ProjectWbsReceiptsRequirementChangeRecordDto();
        changeRecordDto.setRequirementCode(receipts.getRequirementCode());
        changeRecordDto.setProjectId(receipts.getProjectId());
        changeRecordDto.setWebType(receipts.getWebType()); //页面类型
        List<ProjectWbsReceiptsRequirementChangeRecordRelationDto> parentDtoList = new ArrayList<>();
        for (ProjectWbsReceiptsBudgetDto parentBudgetDto : parentBudgetDtoList) {
            // 每个 wbsSummaryCode 只有一个 parentid = -1 的记录
            ProjectWbsReceiptsRequirementChangeRecordRelationDto parentRecordRelationDto = new ProjectWbsReceiptsRequirementChangeRecordRelationDto();
            parentRecordRelationDto.setProjectWbsReceiptsId(receipts.getId());
            parentRecordRelationDto.setWbsSummaryCode(parentBudgetDto.getWbsSummaryCode());
            parentRecordRelationDto.setBudgetOccupiedAmountBefore(parentBudgetDto.getBudgetOccupiedAmount());
            parentRecordRelationDto.setBudgetOccupiedAmountAfter(parentBudgetDto.getBudgetOccupiedAmount());
            parentRecordRelationDto.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode());
            commonRecordRelationSet(receipts.getId(), parentBudgetDto.getWbsSummaryCode(), receipts.getProjectId(), parentRecordRelationDto);

            List<ProjectWbsReceiptsBudgetDto> childDtoList = parentBudgetDto.getChilds();
            if (CollectionUtils.isNotEmpty(childDtoList)) {
                List<ProjectWbsReceiptsRequirementChangeRecordRelationDto> childs = new ArrayList<>();
                for (ProjectWbsReceiptsBudgetDto childDto : childDtoList) {
                    ProjectWbsReceiptsRequirementChangeRecordRelationDto child = new ProjectWbsReceiptsRequirementChangeRecordRelationDto();
                    child.setProjectWbsReceiptsId(receipts.getId());
                    child.setWbsSummaryCode(childDto.getWbsSummaryCode());
                    child.setBudgetOccupiedAmountBefore(childDto.getBudgetOccupiedAmount());
                    child.setBudgetOccupiedAmountAfter(childDto.getBudgetOccupiedAmount());
                    child.setDemandType(childDto.getDemandType());
                    commonRecordRelationSet(receipts.getId(), childDto.getWbsSummaryCode(), receipts.getProjectId(), child);
                    childs.add(child);
                }
                parentRecordRelationDto.setChilds(childs);
            }
            parentDtoList.add(parentRecordRelationDto);
        }
        changeRecordDto.setRelationDtoList(parentDtoList);

        return changeRecordDto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProjectWbsReceiptsRequirementChangeRecordDto submitRequirementChangeRecord(ProjectWbsReceiptsRequirementChangeRecordDto dto) {
        Guard.notNull(dto, "需求变更记录不能为空");
        Long projectWbsReceiptsId = dto.getProjectWbsReceiptsId();
        Guard.notNull(projectWbsReceiptsId, "需求发布单据id不能为空");
        ProjectWbsReceiptsDto projectWbsReceiptsDto = projectWbsReceiptsService.getById(projectWbsReceiptsId);
        Guard.notNull(projectWbsReceiptsDto, "需求发布单据不存在");

        ProjectWbsReceiptsBudgetChangeHistoryExample changeHistoryExample = new ProjectWbsReceiptsBudgetChangeHistoryExample();
        changeHistoryExample.createCriteria().andProjectWbsPublishReceiptsIdEqualTo(projectWbsReceiptsId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsReceiptsBudgetChangeHistory> changeHistoryList =
                projectWbsReceiptsBudgetChangeHistoryService.selectByExample(changeHistoryExample);
        if (CollectionUtils.isNotEmpty(changeHistoryList)) {
            List<Long> projectWbsChangeReceiptsIdList =
                    changeHistoryList.stream().map(ProjectWbsReceiptsBudgetChangeHistory::getProjectWbsChangeReceiptsId).collect(Collectors.toList());
            ProjectWbsReceiptsExample projectWbsReceiptsExample = new ProjectWbsReceiptsExample();
            projectWbsReceiptsExample.createCriteria().andIdIn(projectWbsChangeReceiptsIdList).andDeletedFlagEqualTo(Boolean.FALSE);
            List<ProjectWbsReceipts> projectWbsReceipts = projectWbsReceiptsService.selectByExample(projectWbsReceiptsExample);
            for (ProjectWbsReceipts receipts : projectWbsReceipts) {
                if (Objects.equals(RequirementStatusEnum.SUBMITTED.getCode(), receipts.getRequirementStatus())) {
                    throw new ApplicationBizException(String.format("存在状态为：提交中的详细设计变更单据：%s，正在变更当前需求预算，请处理",
                            receipts.getRequirementCode()));
                }
                if (Objects.equals(RequirementStatusEnum.PENDING.getCode(), receipts.getRequirementStatus())) {
                    throw new ApplicationBizException(String.format("存在状态为：审批中的详细设计变更单据：%s，正在变更当前需求预算，请处理",
                            receipts.getRequirementCode()));
                }
            }
        }

        List<ProjectWbsReceiptsRequirementChangeRecordRelationDto> relationDtoList = dto.getRelationDtoList();
        //详细设计变更停留待处理，但是需求预算已经发生变化的场景，需要校验，变更前的需求预算如果已经发生变化，提示：变更前的需求预算已经发生变化，请刷新页面
        ProjectWbsReceiptsBudgetExample projectWbsReceiptsBudgetExample = new ProjectWbsReceiptsBudgetExample();
        projectWbsReceiptsBudgetExample.createCriteria().andProjectWbsReceiptsIdEqualTo(projectWbsReceiptsId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsReceiptsBudget> projectWbsReceiptsBudgetList = projectWbsReceiptsBudgetMapper.selectByExample(projectWbsReceiptsBudgetExample);
        Map<String, BigDecimal> projectWbsReceiptsBudgetMap = projectWbsReceiptsBudgetList.stream()
                .collect(Collectors.toMap(e -> e.getWbsSummaryCode() + "_" + e.getDemandType(), ProjectWbsReceiptsBudget::getBudgetOccupiedAmount));
        logger.info("需求发布单据id：{}，需求发布单据预算信息：{}", projectWbsReceiptsId, JSONObject.toJSONString(projectWbsReceiptsBudgetMap));
        List<ProjectWbsReceiptsRequirementChangeRecordRelationDto> sumBudgetRecordRelationDtoList = new ArrayList<>();
        for (ProjectWbsReceiptsRequirementChangeRecordRelationDto recordRelationDto : relationDtoList) {
            if (CollectionUtils.isNotEmpty(recordRelationDto.getChilds())) {
                sumBudgetRecordRelationDtoList.addAll(recordRelationDto.getChilds());
            }
        }
        logger.info("需求发布单据id：{}，需求变更记录关联信息sumBudgetRecordRelationDtoList：{}",
                projectWbsReceiptsId, JSONObject.toJSONString(sumBudgetRecordRelationDtoList));
        for (ProjectWbsReceiptsRequirementChangeRecordRelationDto sumRelationDto : sumBudgetRecordRelationDtoList) {
            if (projectWbsReceiptsBudgetMap.containsKey(sumRelationDto.getWbsSummaryCode() + "_" + sumRelationDto.getDemandType())) {
                BigDecimal budgetOccupiedAmount =
                        projectWbsReceiptsBudgetMap.get(sumRelationDto.getWbsSummaryCode() + "_" + sumRelationDto.getDemandType());
                if (!Objects.equals(budgetOccupiedAmount.stripTrailingZeros().toPlainString(),
                        sumRelationDto.getBudgetOccupiedAmountBefore().stripTrailingZeros().toPlainString())) {
                    this.syncProjectWbsReceiptsRequirementChangeRecordRelation(relationDtoList.get(0).getProjectWbsReceiptsRequirementChangeRecordId(),
                            projectWbsReceiptsId);

                    dto.setProjectWbsReceiptsRequirementIsChange(true);
                    return dto;
                }
            }
        }

        UserInfo userInfo = CacheDataUtils.findUserById(SystemContext.getUserId());
        dto.setRecordStatus(ProjectWbsReceiptsRequirementChangeRecordStatusEnum.DRAFT.getCode());
        dto.setProducerId(userInfo.getId());
        dto.setProducerName(userInfo.getName());
        ProjectWbsReceiptsRequirementChangeRecordDto recordDto = this.save(dto, SystemContext.getUserId());

        projectWbsReceiptsRequirementChangeRecordRelationService.saveRequirementChangeRecordRelation(dto.getId(), projectWbsReceiptsId,
                relationDtoList);

        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS_REQUIREMENT_CHANGE_RECORD.code());
        ctcAttachmentDto.setModuleId(dto.getId());
        ctcAttachmentService.deleteByModule(ctcAttachmentDto);
        // 保存附件信息
        if (CollectionUtils.isNotEmpty(dto.getAttachmentList())) {
            for (CtcAttachmentDto attachmentDto : dto.getAttachmentList()) {
                if (attachmentDto.getId() == null) {
                    attachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS_REQUIREMENT_CHANGE_RECORD.code());
                    attachmentDto.setModuleId(dto.getId());
                }
                ctcAttachmentService.save(attachmentDto, SystemContext.getUserId());
            }
        }

        return recordDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncProjectWbsReceiptsRequirementChangeRecordRelation(Long projectWbsReceiptsRequirementChangeRecordId,
                                                                      Long projectWbsReceiptsId) {
        if (null == projectWbsReceiptsRequirementChangeRecordId || null == projectWbsReceiptsId) {
            return;
        }

        ProjectWbsReceiptsRequirementChangeRecordRelationExample example = new ProjectWbsReceiptsRequirementChangeRecordRelationExample();
        example.createCriteria()
                .andProjectWbsReceiptsRequirementChangeRecordIdEqualTo(projectWbsReceiptsRequirementChangeRecordId)
                .andProjectWbsReceiptsIdEqualTo(projectWbsReceiptsId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsReceiptsRequirementChangeRecordRelation> relationList =
                projectWbsReceiptsRequirementChangeRecordRelationMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(relationList)) {
            return;
        }

        Map<String, ProjectWbsReceiptsRequirementChangeRecordRelation> recordRelationMap = new HashMap<>();
        ProjectWbsReceiptsRequirementChangeRecordRelation parentRecordRelation = null;
        for (ProjectWbsReceiptsRequirementChangeRecordRelation recordRelation : relationList) {
            if (Objects.equals(recordRelation.getDemandType(), ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode())) {
                parentRecordRelation = recordRelation;
            } else {
                String key =
                        recordRelation.getProjectWbsReceiptsId() + "_" + recordRelation.getWbsSummaryCode() + "_" + recordRelation.getDemandType();
                recordRelationMap.put(key, recordRelation);
            }
        }

        BigDecimal parentBudgetOccupiedAmountBeforeDifference = BigDecimal.ZERO;
        ProjectWbsReceiptsBudgetExample projectWbsReceiptsBudgetExample = new ProjectWbsReceiptsBudgetExample();
        projectWbsReceiptsBudgetExample.createCriteria().andProjectWbsReceiptsIdEqualTo(projectWbsReceiptsId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsReceiptsBudget> projectWbsReceiptsBudgets = projectWbsReceiptsBudgetMapper.selectByExample(projectWbsReceiptsBudgetExample);
        for (ProjectWbsReceiptsBudget budget : projectWbsReceiptsBudgets) {
            String budgetKey = budget.getProjectWbsReceiptsId() + "_" + budget.getWbsSummaryCode() + "_" + budget.getDemandType();
            if (recordRelationMap.containsKey(budgetKey)) {
                ProjectWbsReceiptsRequirementChangeRecordRelation recordRelation = recordRelationMap.get(budgetKey);
                BigDecimal budgetOccupiedAmount = Optional.ofNullable(budget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO);
                BigDecimal budgetOccupiedAmountBefore = Optional.ofNullable(recordRelation.getBudgetOccupiedAmountBefore()).orElse(BigDecimal.ZERO);
                parentBudgetOccupiedAmountBeforeDifference = parentBudgetOccupiedAmountBeforeDifference
                        .add(budgetOccupiedAmount.subtract(budgetOccupiedAmountBefore));

                ProjectWbsReceiptsRequirementChangeRecordRelation updateRecordRelation = new ProjectWbsReceiptsRequirementChangeRecordRelation();
                updateRecordRelation.setId(recordRelation.getId());
                updateRecordRelation.setBudgetOccupiedAmountBefore(budgetOccupiedAmount);
                projectWbsReceiptsRequirementChangeRecordRelationMapper.updateByPrimaryKeySelective(updateRecordRelation);
            }
        }

        if (null != parentRecordRelation) {
            BigDecimal budgetOccupiedAmountBefore = Optional.ofNullable(parentRecordRelation.getBudgetOccupiedAmountBefore()).orElse(BigDecimal.ZERO);
            budgetOccupiedAmountBefore = budgetOccupiedAmountBefore.add(parentBudgetOccupiedAmountBeforeDifference);

            ProjectWbsReceiptsRequirementChangeRecordRelation updateParentRecordRelation = new ProjectWbsReceiptsRequirementChangeRecordRelation();
            updateParentRecordRelation.setId(parentRecordRelation.getId());
            updateParentRecordRelation.setBudgetOccupiedAmountBefore(budgetOccupiedAmountBefore);
            projectWbsReceiptsRequirementChangeRecordRelationMapper.updateByPrimaryKeySelective(updateParentRecordRelation);
        }
    }

    @Override
    public ProjectWbsReceiptsRequirementChangeRecordDto findRequirementChangeRecord(Long id) {
        Guard.notNull(id, "需求变更记录id不能为空");
        ProjectWbsReceiptsRequirementChangeRecordDto recordDto = this.getById(id);
        Guard.notNull(recordDto, "需求变更记录不存在");
        Long projectWbsReceiptsId = recordDto.getProjectWbsReceiptsId();
        Guard.notNull(projectWbsReceiptsId, "需求发布单据id不能为空");
        ProjectWbsReceiptsDto receipts = projectWbsReceiptsService.getById(projectWbsReceiptsId);
        Guard.notNull(receipts, "需求发布单据不存在");

        recordDto.setRequirementCode(receipts.getRequirementCode());
        recordDto.setProjectId(receipts.getProjectId());
        recordDto.setWebType(receipts.getWebType());
        List<ProjectWbsReceiptsRequirementChangeRecordRelationDto> dtoList =
                projectWbsReceiptsRequirementChangeRecordRelationService.findRecordRelationDtoList(id);
        logger.info("需求变更记录id：{}，需求发布单据id：{}，需求变更记录关联信息dtoList：{}", id, projectWbsReceiptsId, JSONObject.toJSONString(dtoList));

        if (CollectionUtils.isNotEmpty(dtoList)) {
            Map<String, ProjectWbsReceiptsRequirementChangeRecordRelationDto> RelationDtoMap;
            // 每个 wbsSummaryCode 只有一个 parentId = -1 的记录
            RelationDtoMap = dtoList.stream()
                    .collect(Collectors.toMap(ProjectWbsReceiptsRequirementChangeRecordRelationDto::getWbsSummaryCode, Function.identity()));

            List<ProjectWbsReceiptsRequirementChangeRecordRelationDto> relationDtoList = new ArrayList<>();
            for (String key : RelationDtoMap.keySet()) {
                ProjectWbsReceiptsRequirementChangeRecordRelationDto parentRecordRelationDto = RelationDtoMap.get(key);
                commonRecordRelationSet(receipts.getId(), key, receipts.getProjectId(), parentRecordRelationDto);

                List<ProjectWbsReceiptsRequirementChangeRecordRelationDto> finalChilds = new ArrayList<>();
                ProjectWbsReceiptsRequirementChangeRecordRelationDto demandClasses = null;
                ProjectWbsReceiptsRequirementChangeRecordRelationDto materialOutSource = null;
                for (ProjectWbsReceiptsRequirementChangeRecordRelationDto child : parentRecordRelationDto.getChilds()) {
                    if (Objects.equals(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode(), child.getDemandType())) {
                        demandClasses = child;
                    }
                    if (Objects.equals(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode(), child.getDemandType())) {
                        materialOutSource = child;
                    }
                }

                if (Objects.nonNull(demandClasses)) {
                    commonRecordRelationSet(receipts.getId(), key, receipts.getProjectId(), demandClasses);
                    finalChilds.add(demandClasses);
                }
                if (Objects.nonNull(materialOutSource)) {
                    commonRecordRelationSet(receipts.getId(), key, receipts.getProjectId(), materialOutSource);
                    finalChilds.add(materialOutSource);
                }
                parentRecordRelationDto.setChilds(finalChilds);

                relationDtoList.add(parentRecordRelationDto);
            }
            recordDto.setRelationDtoList(relationDtoList);
        }

        //查询附件信息
        List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(id,
                CtcAttachmentModule.PROJECT_WBS_RECEIPTS_REQUIREMENT_CHANGE_RECORD.code(), null);
        recordDto.setAttachmentList(attachmentDtos);
        return recordDto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeRecordStatus(Long id, Integer recordStatus, Long updateBy) {
        Guard.notNull(id, "需求变更记录id不能为空");
        Guard.notNull(recordStatus, "需求变更记录状态不能为空");
        Guard.notNull(updateBy, "操作人id不能为空");

        ProjectWbsReceiptsRequirementChangeRecordDto recordDto = new ProjectWbsReceiptsRequirementChangeRecordDto();
        recordDto.setId(id);
        recordDto.setRecordStatus(recordStatus);
        recordDto.setUpdateBy(updateBy);
        recordDto.setUpdateAt(new Date());
        if (Objects.equals(recordStatus, ProjectWbsReceiptsRequirementChangeRecordStatusEnum.DELETE.getCode())) {
            recordDto.setDeletedFlag(Boolean.TRUE);
        }
        this.update(recordDto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRequirementChangeRecord(ProjectWbsReceiptsRequirementChangeRecordDto dto) {
        Guard.notNull(dto, "需求变更记录不能为空");
        Long id = dto.getId();
        Guard.notNull(id, "需求变更记录id不能为空");

        List<ProjectWbsReceiptsRequirementChangeRecordRelationDto> relationDtoList = dto.getRelationDtoList();
        if (CollectionUtils.isNotEmpty(relationDtoList)) {
            for (ProjectWbsReceiptsRequirementChangeRecordRelationDto recordRelationDto : relationDtoList) {
                Guard.notNull(recordRelationDto.getId(), "需求变更记录关联id不能为空");
                projectWbsReceiptsRequirementChangeRecordRelationMapper.updateByPrimaryKeySelective(recordRelationDto);
            }
        }

        this.update(dto);
    }

    private void commonRecordRelationSet(Long projectWbsReceiptsId,
                                         String wbsSummaryCode,
                                         Long projectId,
                                         ProjectWbsReceiptsRequirementChangeRecordRelationDto recordRelationDto) {
        logger.info("需求发布单据id：{}，wbsSummaryCode：{}，项目id：{}，需求变更记录关联信息recordRelationDto：{}",
                projectWbsReceiptsId, wbsSummaryCode, projectId, JSONObject.toJSONString(recordRelationDto));

        List<ProjectWbsCostByReceiptsVO> filterSummaryBudgets = getChangeSummaryMap(projectId, wbsSummaryCode,
                Lists.newArrayList(projectWbsReceiptsId));
        logger.info("需求发布单据id：{}，wbsSummaryCode：{}，项目id：{}，需求变更记录关联信息filterSummaryBudgets：{}",
                projectWbsReceiptsId, wbsSummaryCode, projectId, JSONObject.toJSONString(filterSummaryBudgets));

        BigDecimal summaryBudgetsDemandCost = BigDecimal.ZERO;
        BigDecimal summaryBudgetsOnTheWayCost = BigDecimal.ZERO;
        BigDecimal summaryBudgetsIncurredCost = BigDecimal.ZERO;
        if (Objects.equals(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode(), recordRelationDto.getDemandType())) {
            // 汇总
            for (ProjectWbsCostByReceiptsVO receiptsVO : filterSummaryBudgets) {
                summaryBudgetsDemandCost =
                        summaryBudgetsDemandCost.add(Optional.ofNullable(receiptsVO.getDemandCost()).orElse(BigDecimal.ZERO));
                summaryBudgetsOnTheWayCost =
                        summaryBudgetsOnTheWayCost.add(Optional.ofNullable(receiptsVO.getOnTheWayCost()).orElse(BigDecimal.ZERO));
                summaryBudgetsIncurredCost =
                        summaryBudgetsIncurredCost.add(Optional.ofNullable(receiptsVO.getIncurredCost()).orElse(BigDecimal.ZERO));
            }
        } else {
            for (ProjectWbsCostByReceiptsVO receiptsVO : filterSummaryBudgets) {
                Integer summaryBudgetsDemandType = receiptsVO.getDemandType().intValue();
                if (Objects.equals(summaryBudgetsDemandType, recordRelationDto.getDemandType())) {
                    summaryBudgetsDemandCost =
                            summaryBudgetsDemandCost.add(Optional.ofNullable(receiptsVO.getDemandCost()).orElse(BigDecimal.ZERO));
                    summaryBudgetsOnTheWayCost =
                            summaryBudgetsOnTheWayCost.add(Optional.ofNullable(receiptsVO.getOnTheWayCost()).orElse(BigDecimal.ZERO));
                    summaryBudgetsIncurredCost =
                            summaryBudgetsIncurredCost.add(Optional.ofNullable(receiptsVO.getIncurredCost()).orElse(BigDecimal.ZERO));
                }
            }
        }

        ProjectWbsBudgetSummaryExample budgetSummaryExample = new ProjectWbsBudgetSummaryExample();
        budgetSummaryExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andProjectIdEqualTo(projectId)
                .andSummaryCodeEqualTo(wbsSummaryCode)
                .andSummaryTypeEqualTo(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType())
                .andProjectDetailSelectFlagEqualTo(Boolean.TRUE);
        List<ProjectWbsBudgetSummary> projectWbsBudgetSummaries = projectWbsBudgetSummaryMapper.selectByExample(budgetSummaryExample);
        ProjectWbsBudgetSummary projectWbsBudgetSummary = CollectionUtils.isNotEmpty(projectWbsBudgetSummaries) ? projectWbsBudgetSummaries.get(0)
                : new ProjectWbsBudgetSummary();

        recordRelationDto.setPrice(Optional.ofNullable(projectWbsBudgetSummary.getPrice()).orElse(BigDecimal.ZERO));
        BigDecimal demandCost = Optional.ofNullable(projectWbsBudgetSummary.getDemandCost()).orElse(BigDecimal.ZERO)
                .subtract(summaryBudgetsDemandCost);
        BigDecimal onTheWayCost = Optional.ofNullable(projectWbsBudgetSummary.getOnTheWayCost()).orElse(BigDecimal.ZERO)
                .subtract(summaryBudgetsOnTheWayCost);
        BigDecimal incurredCost = Optional.ofNullable(projectWbsBudgetSummary.getIncurredCost()).orElse(BigDecimal.ZERO)
                .subtract(summaryBudgetsIncurredCost);
        BigDecimal remainingCost = Optional.ofNullable(projectWbsBudgetSummary.getPrice()).orElse(BigDecimal.ZERO)
                .subtract(demandCost).subtract(onTheWayCost).subtract(incurredCost);
        recordRelationDto.setDemandCost(demandCost);
        recordRelationDto.setOnTheWayCost(onTheWayCost);
        recordRelationDto.setIncurredCost(incurredCost);
        recordRelationDto.setRemainingCost(remainingCost);
    }

    private List<ProjectWbsCostByReceiptsVO> getChangeSummaryMap(Long projectId, String wbsSummaryCode, List<Long> projectWbsPublishReceiptsIdList) {
        ProjectWbsSummaryDto projectWbsSummaryDto = new ProjectWbsSummaryDto();
        projectWbsSummaryDto.setProjectId(projectId);
        projectWbsSummaryDto.setWbsSummaryCode(wbsSummaryCode);
        projectWbsSummaryDto.setProjectWbsPublishReceiptsIdList(projectWbsPublishReceiptsIdList);
        String url = String.format("%sstatistics/project/wbsCost/requirementCodeBudget", ModelsEnum.STATISTICS.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(120 * 1000);
        httpRequestFactory.setConnectTimeout(120 * 1000);
        httpRequestFactory.setReadTimeout(120 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        DataResponse<List<ProjectWbsCostByReceiptsVO>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<ProjectWbsCostByReceiptsVO>>>() {
                });
        return response.getData();
    }

}
