package com.midea.pam.ctc.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.ctc.dto.OrganizationCustomDictDto;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.PaymentPlanDTO;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentApplyExample;
import com.midea.pam.common.ctc.entity.PaymentPlan;
import com.midea.pam.common.ctc.entity.PaymentPlanExample;
import com.midea.pam.common.ctc.query.PaymentApplyQuery;
import com.midea.pam.common.ctc.vo.PurchaseContractProgressVo;
import com.midea.pam.common.enums.AuditStatus;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PaymentPlanStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.contract.service.PaymentPlanService;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.contract.service.helper.PurchaseContractHelper;
import com.midea.pam.ctc.mapper.PaymentApplyExtMapper;
import com.midea.pam.ctc.mapper.PaymentApplyMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceExtMapper;
import com.midea.pam.ctc.mapper.PaymentPlanExtMapper;
import com.midea.pam.ctc.mapper.PaymentPlanMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-6-4
 * @description 付款计划
 */
public class PaymentPlanServiceImpl implements PaymentPlanService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private PaymentPlanExtMapper paymentPlanExtMapper;
    @Resource
    private PurchaseContractService purchaseContractService;
    @Resource
    private PaymentPlanMapper paymentPlanMapper;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private PaymentApplyService paymentApplyService;
    @Resource
    private PaymentApplyExtMapper paymentApplyExtMapper;
    @Resource
    private PaymentApplyMapper paymentApplyMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private PaymentInvoiceExtMapper paymentInvoiceExtMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;

    @Resource
    private RestTemplate restTemplate;


    @Override
    public PageInfo<PaymentPlanDTO> selectPage(PaymentPlanDTO query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<PaymentPlanDTO> list = getList(query);
        PageInfo<PaymentPlanDTO> page = BeanConverter.convertPage(list, PaymentPlanDTO.class);
        return page;
    }

    @Override
    public List<PaymentPlanDTO> selectList(PaymentPlanDTO query) {
        List<PaymentPlanDTO> list = getList(query);

        List<PaymentPlanDTO> dtos = BeanConverter.copy(list, PaymentPlanDTO.class);
        return dtos;
    }

    private List<PaymentPlanDTO> getList(PaymentPlanDTO query) {
        List<PaymentPlanDTO> list = paymentPlanExtMapper.selectPage(query);
        for (PaymentPlanDTO dto : list) {
            Long id = dto.getId();
            Long vendorId = dto.getVendorId();
            if (vendorId != null && vendorId > 0) {
                dto.setVendorId(vendorId);//供应商ID
                VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(vendorId);
                if (vendorSiteBankDto != null) {
                    dto.setVendorCode(vendorSiteBankDto.getVendorCode());//供应商编码
                }
            }

            PaymentApplyExample example = new PaymentApplyExample();
            example.createCriteria().andPaymentPlanIdEqualTo(id).andAuditStatusEqualTo(AuditStatus.PENDING.getCode());

            List<PaymentApply> listPaymentApply = paymentApplyMapper.selectByExample(example);
            if (listPaymentApply != null && listPaymentApply.size() != 0) {
                dto.setAlread(true);
            } else {
                dto.setAlread(false);
            }

            //累计已经申请金额（含税）
            BigDecimal totalApplyAmount = paymentApplyExtMapper.actualApplyAmount(id);
            dto.setTotalApplyAmount(totalApplyAmount);

            //累计在途金额（含税）")
            BigDecimal totalOnTheWayAmount = paymentApplyExtMapper.totalOnTheWayAmount(id);
            dto.setTotalOnTheWayAmount(totalOnTheWayAmount);

            //累计罚扣款（含税）")
            BigDecimal totalpenaltyAmount = paymentApplyExtMapper.totalpenaltyAmount(id);
            BigDecimal allocationPunishmentAmountWithTax = dto.getAllocationPunishmentAmountWithTax();
            if(Objects.nonNull(allocationPunishmentAmountWithTax)){
                dto.setTotalpenaltyAmount(totalpenaltyAmount.add(allocationPunishmentAmountWithTax));
            }else{
                dto.setTotalpenaltyAmount(totalpenaltyAmount);
            }



        }
        return list;
    }

    @Override
    public PaymentPlanDTO findPaymentPlanById(Long id) {
        PaymentPlanDTO result = null;
        if (id == null || id == 0) {
            throw new BizException(ErrorCode.ID_NOT_NULL);
        }
        PaymentPlanDTO query = new PaymentPlanDTO();
        query.setId(id);
        List<PaymentPlanDTO> list = paymentPlanExtMapper.selectPage(query);
        if (list == null || list.size() == 0) {
            throw new BizException(ErrorCode.CTC_PAYMENT_PLAN_CODE_EXIST);
        }
        result = list.get(0);
        Long milestoneId = result.getMilestoneId();//关联里程碑ID
        if (milestoneId != null && milestoneId > 0) {
            ProjectMilepostDto projectMilepostDto = projectMilepostService.getById(milestoneId);
            result.setMilestoneName(projectMilepostDto.getName());//关联里程碑名称
            result.setMilestoneEndTime(projectMilepostDto.getEndTime());//关联里程碑计划完成时间
            result.setMilestoneStatus(projectMilepostDto.getStatus());//里程碑状态
            result.setResponsible(projectMilepostDto.getResponsible());//里程碑负责人
            result.setResponsibleName(projectMilepostDto.getResponsibleName());//里程碑负责姓名
        }


        Long vendorId = result.getVendorId();
        if (vendorId != null && vendorId > 0) {
            result.setVendorId(vendorId);//供应商ID
            VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(vendorId);
            if (vendorSiteBankDto != null) {
                result.setVendorCode(vendorSiteBankDto.getVendorCode());//供应商编码
            }
        }


        Long ouId = result.getOuId();//业务实体ID
        if (ouId != null && ouId > 0) {
            OperatingUnit operatingUnit = CacheDataUtils.findOuById(ouId);
            if (operatingUnit != null) {
                result.setOuName(operatingUnit.getOperatingUnitName());//业务实体名称
            }
        }

        if (Objects.nonNull(result.getVendorId()) && Objects.nonNull(ouId)) {
            Pair<String, String> projectNumberPair = basedataExtService.getBudgetProjectNumberByVendorId(null,result.getVendorId(), ouId);
            if (Objects.nonNull(projectNumberPair)) {
                result.setItemNumber(projectNumberPair.getValue());//预算项目号
                result.setItemNumberName(projectNumberPair.getKey());//预算号名称
            }
        }


        PaymentApplyQuery paymentApplyQuery = new PaymentApplyQuery();
        paymentApplyQuery.setPaymentPlanId(result.getId());
        paymentApplyQuery.setPageNum(1);
        paymentApplyQuery.setPageSize(Integer.MAX_VALUE);
        PageInfo<PaymentApplyDto> pageinfoList = paymentApplyService.selectPage(paymentApplyQuery);
        if (pageinfoList != null) {
            result.setPaymentApplyDtoList(pageinfoList.getList());
        }

        //累计已经申请金额（含税）
        BigDecimal totalApplyAmount = paymentApplyExtMapper.actualApplyAmount(id);
        result.setTotalApplyAmount(totalApplyAmount);

        //累计在途金额（含税）")
        BigDecimal totalOnTheWayAmount = paymentApplyExtMapper.totalOnTheWayAmount(id);
        result.setTotalOnTheWayAmount(totalOnTheWayAmount);

        //累计罚扣款（含税）")
        BigDecimal totalpenaltyAmount = paymentApplyExtMapper.totalpenaltyAmount(id);
        BigDecimal allocationPunishmentAmountWithTax = result.getAllocationPunishmentAmountWithTax();
        if(Objects.nonNull(allocationPunishmentAmountWithTax)){
            result.setTotalpenaltyAmount(totalpenaltyAmount.add(allocationPunishmentAmountWithTax));
        }else{
            result.setTotalpenaltyAmount(totalpenaltyAmount);
        }




        //累计付款金额（含税）-来源于付款申请关联的计划
        BigDecimal actualAmount = paymentApplyExtMapper.actualAmount(id);
        result.setActualAmount(actualAmount);

        PaymentApplyExample example = new PaymentApplyExample();
        example.createCriteria().andPaymentPlanIdEqualTo(id).andAuditStatusEqualTo(AuditStatus.PENDING.getCode()).andDeletedFlagEqualTo(0);

        List<PaymentApply> listPaymentApply = paymentApplyMapper.selectByExample(example);
        // 有审核中付款申请，不允许提交
        if (listPaymentApply != null && listPaymentApply.size() != 0) {
            result.setAlread(true);
        } else {
            result.setAlread(false);
        }

        //采购合同进度执行金额（不含税）
        PurchaseContractProgressVo progressVo = purchaseContractService.calculateExecuteAmountTotal(result.getContractId());
        result.setPurchasingContractProgressAmount(progressVo.getExecuteAmountTotalHeader());

        //已入账税票金额（不含税）
        BigDecimal amountOfTaxReceipts = paymentInvoiceExtMapper.calculateAmountOfTaxReceipts(result.getContractId());
        result.setAmountOfTaxReceipts(amountOfTaxReceipts);

        //查询当前业务实体下对应的本位币
        String localCurrency = null;
        List<OrganizationRel> organizationRels = basedataExtService.queryByOuId(ouId);
        if (ListUtils.isNotEmpty(organizationRels)) {
            localCurrency = StringUtils.isNotEmpty(organizationRels.get(0).getCurrency()) ? organizationRels.get(0).getCurrency() : "";
        }

        if (!Objects.equals(result.getCurrency(), localCurrency)) {
            result.setForeignFlag(Boolean.TRUE);
            VendorSiteBankDto vendorSitBank = getVendorSitBank(vendorId);
            if (Objects.nonNull(vendorSitBank)) {
                result.setTerritoryCode(vendorSitBank.getTerritoryCode());//供应商地址-国家代码
                result.setTerritoryName(vendorSitBank.getTerritoryName());//供应商地址-国家
                result.setFullAddress(vendorSitBank.getFullAddress());//供应商地址-详细地址
                result.setSwiftCode(vendorSitBank.getSwiftCode());//银行swiftcode
            }
        }
        return result;
    }

    private VendorSiteBankDto getVendorSitBank(Long vendorId) {
        //	根据付款计划的"业务实体id+供应商id+供应商地点id"（去重+排除已删除），获取数据表"vendor_site_bank"的"国家代码"
        Map<String, Object> param = new HashMap<>();
        param.put("id", vendorId);
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/findVendorSiteBankDtoById", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<VendorSiteBankDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<VendorSiteBankDto>>() {
                });
        return response.getData();
    }


    @Override
    public List<PaymentPlan> selectByExample(PaymentPlanExample example) {
        return paymentPlanMapper.selectByExample(example);
    }

    @Override
    public int save(PaymentPlan paymentPlan) {
        return paymentPlanMapper.insert(paymentPlan);
    }

    @Override
    public int update(PaymentPlan paymentPlan) {
        return paymentPlanMapper.updateByPrimaryKey(paymentPlan);
    }

    @Override
    public int deleteById(Long id) {
        PaymentPlan paymentPlan = new PaymentPlan();
        paymentPlan.setId(id);
        paymentPlan.setDeletedFlag(Boolean.TRUE);
        return update(paymentPlan);
    }

    @Override
    public PaymentPlan findById(Long id) {
        return paymentPlanMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<PaymentPlan> findByContractId(Long contractId) {
        PaymentPlanExample example = new PaymentPlanExample();
        final PaymentPlanExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andContractIdEqualTo(contractId);
        final List<PaymentPlan> paymentPlans = selectByExample(example);

        return paymentPlans == null ? Lists.newArrayList() : paymentPlans;
    }

    @Override
    public BigDecimal getActualPaymentAmountByContractId(Long contractId) {
        return paymentPlanExtMapper.getActualPaymentAmountByContractId(contractId);
    }

    @Override
    public Map<Long, BigDecimal> getActualPaymentAmountByContractIds(List<Long> contractIds) {
        if (ListUtils.isEmpty(contractIds)) {
            return Collections.emptyMap();
        }
        return paymentPlanExtMapper.getActualPaymentAmountByContractIds(contractIds)
                .stream().collect(Collectors.toMap(PaymentPlanDTO::getContractId, PaymentPlanDTO::getTotalActualAmount));
    }

    @Override
    public List<PaymentPlanDTO> selectByBillId(Long billId) {
        return paymentPlanExtMapper.selectByBillId(billId);
    }

    @Override
    public List<PaymentPlanDTO> getPaymentPlan(Long contractId) {
        List<PaymentPlanDTO> paymentPlanDtoList = paymentPlanExtMapper.selectByContractId(contractId);
        if (!paymentPlanDtoList.isEmpty()) {
            List<Long> planIds = paymentPlanDtoList.stream().map(PaymentPlanDTO::getId).collect(Collectors.toList());
            Map<Long, PaymentApplyDto> paymentApplyDtoMap = paymentApplyExtMapper.summaryApplyAmount(planIds)
                    .stream().collect(Collectors.toMap(PaymentApplyDto::getPaymentPlanId, e -> e));
            paymentPlanDtoList.forEach(e -> {
                PaymentApplyDto paymentApplyDto = paymentApplyDtoMap.get(e.getId());
                // 已付款金额
                e.setActualAmount(paymentApplyDto == null ? BigDecimal.ZERO : paymentApplyDto.getActualAmount());
                // 在途金额
                e.setTransitAmount(paymentApplyDto == null ? BigDecimal.ZERO : paymentApplyDto.getTotalOnTheWayAmount());
            });
        }
        return paymentPlanDtoList;
    }

    @Override
    public void modifyBillPaymentPlan(List<PaymentPlanDTO> paymentPlanDtoList) {
        List<PaymentPlan> paymentPlans = BeanConverter.copy(paymentPlanDtoList, PaymentPlan.class);
        List<PaymentPlan> newPaymentPlans = paymentPlans.stream().filter(e -> e.getId() == null).collect(Collectors.toList());
        List<PaymentPlan> updatePaymentPlans = paymentPlans.stream().filter(e -> e.getId() != null).collect(Collectors.toList());
        List<Long> ids = null;
        int num = 1;
        if (!updatePaymentPlans.isEmpty()) {
            ids = updatePaymentPlans.stream().map(PaymentPlan::getId).collect(Collectors.toList());
            for (PaymentPlan plan : updatePaymentPlans) {
                if (!Objects.equals(plan.getDeletedFlag(), Boolean.TRUE)) {
                    plan.setNum(num++);
                }
                paymentPlanMapper.updateByPrimaryKeySelective(plan);
            }
        }

        Long billId = paymentPlanDtoList.get(0).getBillId();
        if (billId == null) {
            throw new BizException(Code.ERROR, "付款计划对账单id不能为空");
        }
        paymentPlanExtMapper.deleteExcludeIds(billId, ids);

        String seqPrefix = basedataExtService.getUnitSeqPerfix(SystemContext.getUnitId());
        if (!newPaymentPlans.isEmpty()) {
            for (PaymentPlan plan : newPaymentPlans) {
                plan.setCode(PurchaseContractHelper.generatePaymentPlanCode(seqPrefix));
                plan.setStatus(PaymentPlanStatus.UNPAYED.getCode());
                plan.setDeletedFlag(Boolean.FALSE);
                plan.setNum(num++);
            }
            paymentPlanExtMapper.batchInsert(newPaymentPlans);
        }
    }

    @Override
    public void fixState() {
        List<PaymentPlanDTO> paymentPlanList = paymentPlanExtMapper.calculateSurplusAmount(null);
        List<PaymentPlan> updateItemList = new ArrayList<>();
        PaymentPlan updateItem = null;

        for (PaymentPlanDTO paymentPlan : paymentPlanList) {
            BigDecimal amount = paymentPlan.getAmount();
            BigDecimal surplusAmount = paymentPlan.getSurplusAmount();
            surplusAmount = surplusAmount.subtract(paymentPlan.getAllocationPunishmentAmountWithTax());
            if (amount != null) {
                if (surplusAmount.compareTo(BigDecimal.ZERO) == 0) {
                    paymentPlan.setStatus(PaymentPlanStatus.PAYED.getCode());
                } else if (surplusAmount.compareTo(amount) == 0) {
                    paymentPlan.setStatus(PaymentPlanStatus.UNPAYED.getCode());
                } else if(surplusAmount.compareTo(amount) < 0 && surplusAmount.compareTo(BigDecimal.ZERO) > 0){
                    paymentPlan.setStatus(PaymentPlanStatus.PAYING.getCode());
                } else {
                    //处理超付的情况, 剩余待付款金额（含税）减去分配罚扣金额(含税) 如果小于0，则超付
                    logger.info("付款计划存在超付，paymentPlanId：{},剩余待付款金额（含税）:{},分配罚扣金额(含税):{}",paymentPlan,surplusAmount,paymentPlan.getAllocationPunishmentAmountWithTax());
                    paymentPlan.setStatus(PaymentPlanStatus.OVERPAY.getCode());
                }

                if (paymentPlan.getStatus() != null) {
                    updateItem = new PaymentPlan();
                    updateItem.setId(paymentPlan.getId());
                    updateItem.setStatus(paymentPlan.getStatus());
                    updateItemList.add(updateItem);
                }
            }
        }

        List<List<PaymentPlan>> lists = ListUtils.splistList(updateItemList, 1000);
        for (List<PaymentPlan> list : lists) {
            paymentPlanExtMapper.batchUpdate(BeanConverter.copy(list, PaymentPlan.class));
        }
    }

    @Override
    public Boolean getCurrentOrgPrepaymentFlagForCustomDict() {
        String dictName = "预付款标识";
        Long currUnitId = SystemContext.getUnitId();
        if (currUnitId != null) {
            OrganizationCustomDict dictDto = organizationCustomDictService.queryByOuIdAndNameAndOrgForm(currUnitId, dictName, "company");
            if (dictDto != null && dictDto.getValue() != null) {
                return dictDto.getValue().equals("是");
            }
        }
        return false;
    }
}
