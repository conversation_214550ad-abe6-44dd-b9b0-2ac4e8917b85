package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.TaxInfoDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UnitOuRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.crm.dto.CustomerBankAccountDto;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.crm.entity.Customer;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.InvoiceApplyDetailsDto;
import com.midea.pam.common.ctc.dto.InvoiceApplyDto;
import com.midea.pam.common.ctc.dto.InvoiceApplyHeaderDto;
import com.midea.pam.common.ctc.dto.InvoiceApplyPlanDetailDTO;
import com.midea.pam.common.ctc.dto.InvoiceApplyQuery;
import com.midea.pam.common.ctc.dto.InvoiceApplyReceivableAgesDTO;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.ctc.dto.ProductTaxSettingDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.eam.PmsResult;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.ContractEampurchaseRelation;
import com.midea.pam.common.ctc.entity.ContractEampurchaseRelationExample;
import com.midea.pam.common.ctc.entity.CtcAttachment;
import com.midea.pam.common.ctc.entity.CtcAttachmentExample;
import com.midea.pam.common.ctc.entity.EamPurchaseInfo;
import com.midea.pam.common.ctc.entity.EamPurchaseInfoExample;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetailSplit;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetails;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetailsExample;
import com.midea.pam.common.ctc.entity.InvoiceApplyHeader;
import com.midea.pam.common.ctc.entity.InvoiceApplyHeaderExample;
import com.midea.pam.common.ctc.entity.InvoiceApplyReceivableAges;
import com.midea.pam.common.ctc.entity.InvoiceApplyReceivableAgesExample;
import com.midea.pam.common.ctc.entity.InvoicePlan;
import com.midea.pam.common.ctc.entity.InvoicePlanDetail;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.InvoiceReceivableExample;
import com.midea.pam.common.ctc.entity.MifPushLog;
import com.midea.pam.common.ctc.entity.MilepostTemplateStage;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.RdmSettlementSheet;
import com.midea.pam.common.ctc.entity.RdmSettlementSheetDetail;
import com.midea.pam.common.ctc.entity.RdmSettlementSheetDetailExample;
import com.midea.pam.common.ctc.entity.WorkingHour;
import com.midea.pam.common.ctc.entity.WorkingHourExample;
import com.midea.pam.common.ctc.query.ProductTaxSettingQueryDto;
import com.midea.pam.common.ctc.vo.InvoiceApplyDetailExcelVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.enums.InvoiceApplyStatusEnum;
import com.midea.pam.common.enums.InvoicePlanInvoiceType;
import com.midea.pam.common.enums.MilepostStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.enums.WorkflowOperationType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FormInstanceEvent;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.CommonErpStatus;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.HttpClientUtil;
import com.midea.pam.ctc.contract.service.ContractService;
import com.midea.pam.ctc.contract.service.InvoicePlanService;
import com.midea.pam.ctc.contract.service.helper.ContractHelper;
import com.midea.pam.ctc.eam.service.service.EamInvokeHelperService;
import com.midea.pam.ctc.feign.basedata.feign.BasedataLtcDictFeignClient;
import com.midea.pam.ctc.mapper.ContractEampurchaseRelationMapper;
import com.midea.pam.ctc.mapper.ContractMapper;
import com.midea.pam.ctc.mapper.CtcAttachmentMapper;
import com.midea.pam.ctc.mapper.EamPurchaseInfoMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailSplitMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailsExtMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailsMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyHeaderMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyReceivableAgesExtMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyReceivableAgesMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailMapper;
import com.midea.pam.ctc.mapper.InvoicePlanMapper;
import com.midea.pam.ctc.mapper.MifPushLogMapper;
import com.midea.pam.ctc.mapper.MilepostTemplateStageMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostMapper;
import com.midea.pam.ctc.mapper.RdmSettlementSheetDetailMapper;
import com.midea.pam.ctc.mapper.RdmSettlementSheetMapper;
import com.midea.pam.ctc.mapper.WorkingHourExtMapper;
import com.midea.pam.ctc.mapper.WorkingHourMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CrmExtService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.InvoiceApplyService;
import com.midea.pam.ctc.service.InvoiceReceivableService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProductTaxSettingService;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.ctc.service.config.CommonPropertiesConfig;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 开票申请service
 * @date 2019-4-25
 */
public class InvoiceApplyServiceImpl implements InvoiceApplyService {

    private static final Logger logger = LoggerFactory.getLogger(WorkingHourServiceImpl.class);

    @Resource
    private InvoiceApplyHeaderMapper headerMapper;
    @Resource
    private ContractService contractService;
    @Resource
    private InvoiceApplyDetailsMapper detailsMapper;
    @Resource
    private InvoiceApplyReceivableAgesMapper invoiceApplyReceivableAgesMapper;
    @Resource
    private InvoiceApplyReceivableAgesExtMapper invoiceApplyReceivableAgesExtMapper;

    @Resource
    private InvoiceReceivableService invoiceReceivableService;
    @Resource
    private InvoiceApplyDetailSplitMapper invoiceApplyDetailSplitMapper;
    @Resource
    private InvoicePlanMapper planMapper;
    @Resource
    private InvoiceApplyHeaderMapper invoiceApplyHeaderMapper;
    @Resource
    private InvoiceApplyDetailsMapper invoiceApplyDetailsMapper;
    @Resource
    private InvoicePlanDetailMapper planDetailMapper;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private InvoiceApplyDetailsExtMapper invoiceApplyDetailsExtMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    CtcAttachmentMapper ctcAttachmentMapper;
    @Resource
    private InvoicePlanService invoicePlanService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RdmSettlementSheetMapper rdmSettlementSheetMapper;
    @Resource
    private RdmSettlementSheetDetailMapper rdmSettlementSheetDetailMapper;
    @Resource
    private WorkingHourMapper workingHourMapper;
    @Resource
    private EamPurchaseInfoMapper eamPurchaseInfoMapper;
    @Resource
    private EsbService esbService;
    @Resource
    private ProjectMilepostMapper projectMilepostMapper;
    @Resource
    private MilepostTemplateStageMapper milepostTemplateStageMapper;
    @Resource
    private ContractEampurchaseRelationMapper relationMapper;
    @Resource
    private Validator validator;
    @Resource
    private ProductTaxSettingService productTaxSettingService;
    @Resource
    private BasedataLtcDictFeignClient ltcDictFeignClient;
    @Resource
    private ContractHelper contractHelper;
    @Resource
    private CommonPropertiesConfig commonPropertiesConfig;
    @Resource
    private MifPushLogMapper mifPushLogMapper;
    @Resource
    private EamInvokeHelperService eamInvokeHelperService;
    @Resource
    private WorkingHourExtMapper workingHourExtMapper;
    @Resource
    private CrmExtService crmExtService;
    @Resource
    private SdpService sdpService;


    @Override
    public InvoiceApplyDto queryById(Long id) {
        InvoiceApplyDto dto = new InvoiceApplyDto();
        if (id != null) {
            //invoice_apply_header 开票信息的头部信息
            InvoiceApplyHeader header = headerMapper.selectByPrimaryKey(id);
            if (header != null) {
                BeanUtils.copyProperties(header, dto);
                //添加业务实体名称
                dto.setOuName(CacheDataUtils.findOuById(header.getOuId()).getOperatingUnitName());
                if (header.getCreateBy() != null) {
                    UserInfo user = CacheDataUtils.findUserById(header.getCreateBy());
                    if (user != null) dto.setCreateUserName(user.getName());
                }
                if (header.getUpdateBy() != null) {
                    UserInfo user = CacheDataUtils.findUserById(header.getUpdateBy());
                    if (user != null) dto.setUpdateUserName(user.getName());
                }
                //作废人
                if (header.getInvalidBy() != null) {
                    UserInfo user = CacheDataUtils.findUserById(header.getInvalidBy());
                    if (user != null) {
                        dto.setInvalidByName(user.getName());
                    }
                }
                //关联交易信息
                if (StringUtils.isNotBlank(header.getEamPurchaseCode())) {
                    EamPurchaseInfoExample example = new EamPurchaseInfoExample();
                    example.createCriteria().andDeletedFlagEqualTo(false).andContractCodeEqualTo(header.getEamPurchaseCode());
                    List<EamPurchaseInfo> eamPurchaseInfos = eamPurchaseInfoMapper.selectByExample(example);
                    if (ListUtils.isNotEmpty(eamPurchaseInfos)) {
                        dto.setEamContractName(eamPurchaseInfos.get(0).getContractName());// 采购合同名称
                        dto.setEamContractCode(eamPurchaseInfos.get(0).getContractCode());// 采购合同编号
                        dto.setEamContractId(eamPurchaseInfos.get(0).getId());// 采购合同id
                    }

                }
                //结算单信息
                if (header.getSettlementSheetId() != null) {
                    RdmSettlementSheet rdmSettlementSheet = rdmSettlementSheetMapper.selectByPrimaryKey(header.getSettlementSheetId());
                    if (rdmSettlementSheet != null) {
                        dto.setSettlementName(rdmSettlementSheet.getWf150title());//结算单名称
                        dto.setSettlementPrice(rdmSettlementSheet.getTotal());//结算单金额
                    }
                }
                //红冲开票申请明细 / 原开票申请信息
                if (header.getTotalTaxIncludedPrice().floatValue() > 0) {

                    InvoiceApplyHeaderExample headerExample = new InvoiceApplyHeaderExample();
                    headerExample.createCriteria().andOldHeaderIdEqualTo(header.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                    List<InvoiceApplyHeader> invalidApplyHeaders = headerMapper.selectByExample(headerExample);
                    if (ListUtils.isNotEmpty(invalidApplyHeaders)) {
                        List<InvoiceApplyHeaderDto> reverseDtoList = BeanConverter.copy(invalidApplyHeaders, InvoiceApplyHeaderDto.class);
                        for (InvoiceApplyHeaderDto reverseDto : reverseDtoList) {
                            if (reverseDto.getCreateBy() != null) {
                                reverseDto.setReverseUserName(CacheDataUtils.findUserById(reverseDto.getCreateBy()).getName());//冲销人
                            }
                        }
                        dto.setReverseDtoList(reverseDtoList);
                    }
                } else {
                    InvoiceApplyHeader oldHeader = headerMapper.selectByPrimaryKey(header.getOldHeaderId());
                    InvoiceApplyHeaderDto oldHeaderDto = BeanConverter.copy(oldHeader, InvoiceApplyHeaderDto.class);
                    if (oldHeaderDto.getCreateBy() != null) {
                        oldHeaderDto.setReverseUserName(Optional.ofNullable(CacheDataUtils.findUserById(oldHeaderDto.getCreateBy())).orElse(new UserInfo()).getName());//冲销人
                    }
                    dto.setOldHeaderDto(oldHeaderDto);
                }
            }
            InvoiceApplyDetailsExample detailsExample = new InvoiceApplyDetailsExample();
            detailsExample.createCriteria().andApplyHeaderIdEqualTo(id).andDeletedFlagEqualTo(false);
            //开票申请明细表 invoice_apply_details
            List<InvoiceApplyDetails> invoiceApplyDetails = detailsMapper.selectByExampleWithBLOBs(detailsExample);
            if (!ObjectUtils.isEmpty(invoiceApplyDetails)) {
                List<InvoiceApplyDetailsDto> list = BeanConverter.copy(invoiceApplyDetails, InvoiceApplyDetailsDto.class);
                List<InvoiceApplyDetailsDto> listDto = new ArrayList<>();
                /**
                 *查询开票详情表里面的上传附件
                 */
                for (InvoiceApplyDetailsDto invoiceApplyDetailsDto : list) {
                    CtcAttachmentExample example = new CtcAttachmentExample();
                    example.createCriteria().andModuleIdEqualTo(invoiceApplyDetailsDto.getId()).andModuleEqualTo(CtcAttachmentModule.APPLICATION_FOR_INVOICING.code());
                    List<CtcAttachment> list1 = ctcAttachmentMapper.selectByExample(example);
                    if (!ObjectUtils.isEmpty(list1)) {
                        List<CtcAttachmentDto> list2 = BeanConverter.copy(list1, CtcAttachmentDto.class);
                        invoiceApplyDetailsDto.setCtcAttachmentDto(list2.get(0));
                    }

                    //发票类型
                    if (invoiceApplyDetailsDto.getApplyHeaderId() != null) {
                        InvoiceApplyHeader invoiceApplyHeader =
                                invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyDetailsDto.getApplyHeaderId());
                        if (invoiceApplyHeader != null) {
                            invoiceApplyDetailsDto.setType(invoiceApplyHeader.getPlanInvoiceType());
                        }
                    }
                    //销售经理
                    if (invoiceApplyDetailsDto.getContractId() != null) {
                        Contract contract = contractMapper.selectByPrimaryKey(invoiceApplyDetailsDto.getContractId());
                        if (contract != null) {
                            UserInfo userInfo = CacheDataUtils.findUserById(contract.getSalesManager());
                            if (userInfo != null) {
                                invoiceApplyDetailsDto.setSalesManagerName(userInfo.getName());
                            }
                            dto.setContract(contract);
                            dto.setContractAmount(contract.getAmount());
                            dto.setContractExcludingTaxAmount(contract.getExcludingTaxAmount());
                        }
                    }

                    /**
                     * 计算能实际开票的最大金额(计划开票金额-实际已近含税开票金额)
                     */
                    InvoicePlanDetail invoicePlanDetail = null;
                    if (!ObjectUtils.isEmpty(invoiceApplyDetailsDto.getPlanDetailId())) {
                        invoicePlanDetail = planDetailMapper.selectByPrimaryKey(invoiceApplyDetailsDto.getPlanDetailId());
                    }

                    if (invoicePlanDetail != null) {
                        BigDecimal tax = invoicePlanDetail.getTaxIncludedPrice() == null ? BigDecimal.ZERO : invoicePlanDetail.getTaxIncludedPrice();
                        invoiceApplyDetailsDto.setTaxIncludedPriceLimit(invoicePlanDetail.getAmount().add(BigDecimal.ZERO).subtract(tax).setScale(2,
                                BigDecimal.ROUND_HALF_UP));
                        invoiceApplyDetailsDto.setRequirement(invoicePlanDetail.getRequirement());//开票条件

                        //关联里程碑信息
                        if (invoicePlanDetail.getMilestoneId() != null) {
                            ProjectMilepost projectMilepost = projectMilepostMapper.selectByPrimaryKey(invoicePlanDetail.getMilestoneId());
                            if (projectMilepost != null) {
                                invoiceApplyDetailsDto.setMilestoneId(projectMilepost.getId());
                                invoiceApplyDetailsDto.setMilestoneName(projectMilepost.getName());
                                invoiceApplyDetailsDto.setMilestoneStatus(projectMilepost.getStatus());
                            } else {
                                MilepostTemplateStage milepostTemplateStage =
                                        milepostTemplateStageMapper.selectByPrimaryKey(invoicePlanDetail.getMilestoneId());
                                if (milepostTemplateStage != null) {
                                    invoiceApplyDetailsDto.setMilestoneName(milepostTemplateStage.getMilepostStage());
                                }
                            }
                        }
                    }

                    /**
                     * 添加项目
                     */
                    ProjectDto projectDto = invoicePlanService.getProjectName(invoiceApplyDetailsDto.getContractId());
                    if (!ObjectUtils.isEmpty(projectDto)) {
                        //项目id
                        invoiceApplyDetailsDto.setProjectId(projectDto.getId());
                        //项目名称
                        invoiceApplyDetailsDto.setProjectName(projectDto.getName());
                        //项目编号
                        invoiceApplyDetailsDto.setProjectCode(projectDto.getCode());
                        //项目经理
                        invoiceApplyDetailsDto.setManagerName(projectDto.getManagerName());
                    }


                    listDto.add(invoiceApplyDetailsDto);
                }
                dto.setDetails(listDto);
                // 按照开票计划明细分类,方便拆分应收发票
                setPlanDetailInfo(dto);
            } else {
                dto.setDetails(null);
            }
            InvoiceReceivableExample receivableExample = new InvoiceReceivableExample();
            receivableExample.createCriteria().andApplyHeaderIdEqualTo(id);
            //应收发票表 invoice_receivable
            List<InvoiceReceivable> invoiceReceivable = invoiceReceivableService.selectByExample(receivableExample);
            List<InvoiceReceivableDto> invoiceReceivableDtoList = new ArrayList<>();
            for (InvoiceReceivable invoiceReceivable1 : invoiceReceivable) {
                InvoiceReceivableDto invoiceReceivableDto = new InvoiceReceivableDto();
                BeanUtils.copyProperties(invoiceReceivable1, invoiceReceivableDto);
                if (null != invoiceReceivableDto.getExternalInvoiceAmount()) {
                    BigDecimal diff =
                            invoiceReceivableDto.getExclusiveOfTax().setScale(2, BigDecimal.ROUND_HALF_UP).subtract(invoiceReceivableDto.getExternalInvoiceAmount().setScale(2, BigDecimal.ROUND_HALF_UP)).abs();
                    invoiceReceivableDto.setTicketTail(diff);
                }
                invoiceReceivableDtoList.add(invoiceReceivableDto);
            }
            dto.setReceivables(invoiceReceivableDtoList);
            //银行账号
            CustomerDto customerDto = CacheDataUtils.findCustomerById(dto.getCustomerId());
            if (customerDto != null && ListUtils.isNotEmpty(customerDto.getCustomerBankAccountDtoList())) {
                dto.setCustomerBankAccount(customerDto.getCustomerBankAccountDtoList().get(0).getBankAccountNum());
            }

            OperatingUnit ou = CacheDataUtils.findOuById(dto.getOuId());
            if (ou != null) {
                dto.setOuName(ou.getOperatingUnitName());
                UnitOuRel unitOuRel = CacheDataUtils.findUnitOuRelByOuId(ou.getId());
                Long companyId = CacheDataUtils.getTopUnitIdByUnitId(unitOuRel.getUnitId());//公司id

                // 查询库存组织参数：开票申请启用账期
                Set<String> valueSet = organizationCustomDictService.queryByName("开票申请启用账期", companyId, OrgCustomDictOrgFrom.COMPANY);
                /** 如果没有配置【开票申请启用账期】="是"，走现有非柔性单位的逻辑-->返回receivableAgesDTOList，而且 开票申请的“账期（天）”字段的值不接受前端传值，默认为【空】 **/
                if (CollectionUtils.isEmpty(valueSet) || !valueSet.contains("是")) {
                    //账龄明细
                    List<InvoiceApplyReceivableAgesDTO> receivableAgesDTOList = getInvoiceApplyReceivableAgesDTOS(id);
                    dto.setReceivableAgesDTOList(receivableAgesDTOList);
                    dto.setPaymentTerm(null);
                }
            }
        }
        return dto;
    }

    private List<InvoiceApplyReceivableAgesDTO> getInvoiceApplyReceivableAgesDTOS(Long applyHeaderId) {
        List<InvoiceApplyReceivableAgesDTO> receivableAgesDTOList = invoiceApplyReceivableAgesExtMapper.selectByApplyHeaderId(applyHeaderId);
        if (ListUtils.isNotEmpty(receivableAgesDTOList)) {
            for (InvoiceApplyReceivableAgesDTO receivableAgesDTO : receivableAgesDTOList) {
                Long userId = receivableAgesDTO.getUpdateBy() != null ? receivableAgesDTO.getUpdateBy() : receivableAgesDTO.getCreateBy();
                UserInfo userInfo = CacheDataUtils.findUserById(userId);
                if (userInfo != null) {
                    receivableAgesDTO.setUpdateByName(userInfo.getName());
                }
            }
        }
        return receivableAgesDTOList;
    }

    private void setPlanDetailInfo(InvoiceApplyDto invoiceApplyDto) {
        if (ListUtils.isEmpty(invoiceApplyDto.getDetails())) {
            return;
        }
        List<Long> planDetailIdList = invoiceApplyDto.getDetails().stream().map(InvoiceApplyDetailsDto::getPlanDetailId)
                .filter(Objects::nonNull).distinct().sorted().collect(Collectors.toList());
        List<InvoiceApplyPlanDetailDTO> applyPlanDetailDTOList = new ArrayList<>();
        if (ListUtils.isEmpty(planDetailIdList)) {
            return;
        }
        for (Long planDetailId : planDetailIdList) {
            List<InvoiceApplyDetailsDto> applyDetailList = invoiceApplyDto.getDetails().stream()
                    .filter(applyDetailsDto -> planDetailId.equals(applyDetailsDto.getPlanDetailId()))
                    .sorted(Comparator.comparing(InvoiceApplyDetailsDto::getId))
                    .collect(Collectors.toList());
            InvoiceApplyPlanDetailDTO applyPlanDetailDTO = new InvoiceApplyPlanDetailDTO();
            applyPlanDetailDTO.setApplyDetailList(applyDetailList);
            // 可开票最大金额(计划开票金额-含税实际开票金额)
            applyPlanDetailDTO.setTaxIncludedPriceLimit(applyDetailList.get(0).getTaxIncludedPriceLimit());
            applyPlanDetailDTO.setPlanDetailId(applyDetailList.get(0).getPlanDetailId());
            applyPlanDetailDTO.setPlanId(applyDetailList.get(0).getPlanId());
            applyPlanDetailDTO.setTaxRace(applyDetailList.get(0).getTaxRace());
            applyPlanDetailDTO.setTotalTaxIncludedPrice(BigDecimal.ZERO);
            applyPlanDetailDTO.setTotalExclusiveOfTax(BigDecimal.ZERO);
            for (InvoiceApplyDetailsDto invoiceApplyDetailsDto : applyDetailList) {
                BigDecimal taxIncludedPrice = invoiceApplyDetailsDto.getTaxIncludedPrice() != null ? invoiceApplyDetailsDto.getTaxIncludedPrice() :
                        BigDecimal.ZERO;
                BigDecimal exclusiveOfTax = invoiceApplyDetailsDto.getExclusiveOfTax() != null ? invoiceApplyDetailsDto.getExclusiveOfTax() :
                        BigDecimal.ZERO;
                applyPlanDetailDTO.setTotalTaxIncludedPrice(applyPlanDetailDTO.getTotalTaxIncludedPrice().add(taxIncludedPrice));
                applyPlanDetailDTO.setTotalExclusiveOfTax(applyPlanDetailDTO.getTotalExclusiveOfTax().add(exclusiveOfTax));
                applyPlanDetailDTO.setRemark(invoiceApplyDetailsDto.getRemark()); //把票面备注放到和开票明细同一级
            }
            applyPlanDetailDTOList.add(applyPlanDetailDTO);
        }
        invoiceApplyDto.setPlanDetailList(applyPlanDetailDTOList);
    }

    /**
     * 移动审批开票申请信息查询
     *
     * @param id
     * @return
     */
    @Override
    public ResponseMap getInvoiceApply(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> detailMapList1 = new ArrayList<>();
        if (id != null) {
            InvoiceApplyDto dto = new InvoiceApplyDto();
            BigDecimal dValue = BigDecimal.ZERO;
            InvoiceApplyHeader header = headerMapper.selectByPrimaryKey(id);
            if (header != null) {
                BeanUtils.copyProperties(header, dto);
                dto.setOuName(CacheDataUtils.findOuById(header.getOuId()).getOperatingUnitName());
                if (header.getCreateBy() != null) {
                    UserInfo user = CacheDataUtils.findUserById(header.getCreateBy());
                    if (user != null) dto.setCreateUserName(user.getName());
                }
                dValue = dto.getTotalTaxIncludedPrice().subtract(dto.getTotalExclusiveOfTax());
                //往开票头信息放数据
                headMap.put("invoiceCustomer", dto.getInvoiceCustomer());
                headMap.put("createUserName", dto.getCreateUserName());
                headMap.put("currencyCode", dto.getCurrencyCode());
                headMap.put("totalTaxIncludedPrice", dto.getTotalTaxIncludedPrice() == null ? "" : String.valueOf(dto.getTotalTaxIncludedPrice()));
                headMap.put("totalExclusiveOfTax", dto.getTotalExclusiveOfTax() == null ? "" : String.valueOf(dto.getTotalExclusiveOfTax()));
                headMap.put("dValue", dValue == null ? "" : String.valueOf(dValue));
                headMap.put("applyType", dto.getApplyType().equals("plan") ? "合同开票" : "手工开票");
                headMap.put("invoiceDate", dto.getInvoiceDate() == null ? "" : DateUtils.format(dto.getInvoiceDate(), "yyyy-MM-dd"));
                headMap.put("remark", dto.getRemark() == null ? "" : dto.getRemark());
                headMap.put("ouName", dto.getOuName() == null ? "" : dto.getOuName());

                InvoiceApplyDetailsExample detailsExample = new InvoiceApplyDetailsExample();
                detailsExample.createCriteria().andApplyHeaderIdEqualTo(id).andDeletedFlagEqualTo(false);
                //开票申请明细表 invoice_apply_details
                List<InvoiceApplyDetails> invoiceApplyDetails = detailsMapper.selectByExample(detailsExample);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(invoiceApplyDetails)) {
                    for (InvoiceApplyDetails invoiceApplyDetail : invoiceApplyDetails) {
                        ProjectDto projectDto = invoicePlanService.getProjectName(invoiceApplyDetail.getContractId());
                        BigDecimal dValue1 = BigDecimal.ZERO;
                        dValue1 = invoiceApplyDetail.getTaxIncludedPrice().subtract(invoiceApplyDetail.getExclusiveOfTax());
                        Map<String, String> detailMap = new HashMap<>();
                        detailMap.put("product", invoiceApplyDetail.getProduct());
                        detailMap.put("quantity", invoiceApplyDetail.getQuantity() == null ? "" : String.valueOf(invoiceApplyDetail.getQuantity()));
                        detailMap.put("price", invoiceApplyDetail.getPrice() == null ? "" : String.valueOf(invoiceApplyDetail.getPrice()));
                        detailMap.put("productTaxName", invoiceApplyDetail.getProductTaxName() == null ? "" :
                                String.valueOf(invoiceApplyDetail.getProductTaxName()));
                        detailMap.put("model", invoiceApplyDetail.getModel() == null ? "" : invoiceApplyDetail.getModel());
                        detailMap.put("unit", invoiceApplyDetail.getUnit() == null ? "" : invoiceApplyDetail.getUnit());
                        String taxRace = invoiceApplyDetail.getTaxRace() == null ? "0.00" : String.valueOf(invoiceApplyDetail.getTaxRace());
                        detailMap.put("taxRace", taxRace.substring(0, taxRace.indexOf(".")) + "%");
                        detailMap.put("taxIncludedPrice", invoiceApplyDetail.getTaxIncludedPrice() == null ? "" :
                                String.valueOf(invoiceApplyDetail.getTaxIncludedPrice()));
                        detailMap.put("exclusiveOfTax", invoiceApplyDetail.getExclusiveOfTax() == null ? "" :
                                invoiceApplyDetail.getExclusiveOfTax().toString());
                        detailMap.put("dValue", dValue1.toString());
                        detailMap.put("contractName", invoiceApplyDetail.getContractName());
                        detailMap.put("customerName", dto.getCustomerName() == null ? "" : dto.getCustomerName());
                        detailMap.put("projectName", Optional.ofNullable(projectDto).map(p -> p.getName()).orElse(" "));
                        detailMap.put("projectCode", Optional.ofNullable(projectDto).map(p -> p.getCode()).orElse(" "));
                        detailMap.put("remark", invoiceApplyDetail.getRemark() == null ? "" : invoiceApplyDetail.getRemark());

                        InvoicePlanDetail invoicePlanDetail = planDetailMapper.selectByPrimaryKey(invoiceApplyDetail.getPlanDetailId());

                        //发票类型
                        if (invoiceApplyDetail.getApplyHeaderId() != null) {
                            InvoiceApplyHeader invoiceApplyHeader =
                                    invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyDetail.getApplyHeaderId());
                            if (invoiceApplyHeader != null) {
                                detailMap.put("type", InvoicePlanInvoiceType.getValue(invoiceApplyHeader.getPlanInvoiceType()));
                            }
                        }
                        if (invoicePlanDetail != null) {
                            //开票条件
                            detailMap.put("requirement", invoicePlanDetail.getRequirement());

                            //关联里程碑信息
                            if (invoicePlanDetail.getMilestoneId() != null) {
                                ProjectMilepost projectMilepost = projectMilepostMapper.selectByPrimaryKey(invoicePlanDetail.getMilestoneId());
                                if (projectMilepost != null) {
                                    detailMap.put("milestoneName", String.valueOf(projectMilepost.getName()));
                                    detailMap.put("milestoneStatus", MilepostStatus.getValue(projectMilepost.getStatus()));
                                } else {
                                    MilepostTemplateStage milepostTemplateStage =
                                            milepostTemplateStageMapper.selectByPrimaryKey(invoicePlanDetail.getMilestoneId());
                                    if (milepostTemplateStage != null) {
                                        detailMap.put("milestoneName", String.valueOf(milepostTemplateStage.getMilepostStage()));
                                    }
                                }
                            }
                            //销售经理
                            if (invoicePlanDetail.getContractId() != null) {
                                Contract contract = contractMapper.selectByPrimaryKey(invoicePlanDetail.getContractId());
                                if (contract != null) {
                                    UserInfo userInfo = CacheDataUtils.findUserById(contract.getSalesManager());
                                    if (userInfo != null) {
                                        detailMap.put("salesManagerName", String.valueOf(userInfo.getName()));
                                    }
                                }
                            }

                        }

                        //项目经理
                        if (projectDto != null && projectDto.getManagerId() != null) {
                            UserInfo userInfo = CacheDataUtils.findUserById(projectDto.getManagerId());
                            if (userInfo != null) {
                                detailMap.put("managerName", String.valueOf(userInfo.getName()));
                            }
                        }
                        detailMapList1.add(detailMap);

                    }
                }
            } else {
                responseMap.setMsg("头信息为空！");
                responseMap.setStatus("fail");
            }

            responseMap.setMsg("成功");
            responseMap.setStatus("success");
            responseMap.setHeadMap(headMap);
            responseMap.setList1(detailMapList1);
        }
        return responseMap;
    }

    @Override
    public PageInfo<InvoiceApplyHeaderDto> query(InvoiceApplyQuery query) {
        InvoiceApplyHeaderExample example = new InvoiceApplyHeaderExample();
        InvoiceApplyHeaderExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(query.getApplyCode())) criteria.andApplyCodeEqualTo(query.getApplyCode());
        if (StringUtils.isNotBlank(query.getApplyType())) criteria.andApplyTypeEqualTo(query.getApplyType());
        if (StringUtils.isNotBlank(query.getInvoiceDateStart()))
            criteria.andInvoiceDateGreaterThanOrEqualTo(DateUtil.parseDate(query.getInvoiceDateStart(), "yyyy-MM-dd"));
        if (StringUtils.isNotBlank(query.getInvoiceDateEnd()))
            criteria.andInvoiceDateLessThan(DateUtil.parseDate(query.getInvoiceDateEnd(), "yyyy-MM-dd"));
        if (StringUtils.isNotBlank(query.getCustomerName()))
            criteria.andCustomerNameLike("%" + query.getCustomerName() + "%");
        if (StringUtils.isNotBlank(query.getOuId())) criteria.andOuIdEqualTo(Long.parseLong(query.getOuId()));
        if (query.getOrNotTax() != null) criteria.andOrNotTaxEqualTo(query.getOrNotTax());
        if (StringUtils.isNotBlank(query.getGlDateStart()))
            criteria.andGlDateGreaterThanOrEqualTo(DateUtil.parseDate(query.getGlDateStart(), "yyyy-MM-dd"));
        if (StringUtils.isNotBlank(query.getGlDateEnd()))
            criteria.andGlDateLessThan(DateUtil.parseDate(query.getGlDateEnd(), "yyyy-MM-dd"));
        if (null != query.getStatus()) criteria.andStatusEqualTo(query.getStatus());
        if (StringUtils.isNotBlank(query.getTaxIncludedPrice()))
            criteria.andTotalTaxIncludedPriceEqualTo(BigDecimal.valueOf(Float.parseFloat(query.getTaxIncludedPrice())));
        if (StringUtils.isNotBlank(query.getCreateAtStart()))
            criteria.andCreateAtGreaterThanOrEqualTo(DateUtil.parseDate(query.getCreateAtStart(), "yyyy-MM-dd"));
        if (StringUtils.isNotBlank(query.getCreateAtEnd()))
            criteria.andCreateAtLessThan(DateUtil.parseDate(query.getCreateAtEnd(), "yyyy-MM-dd"));
        //按创建者姓名模糊查询
        if (StringUtils.isNotBlank(query.getCreateUserName())) {
            Map<String, Object> param = new HashMap<>();
            param.put("userName", query.getCreateUserName());
            //得到对应的用户信息 ltc_user_info
            String url = WorkingHourServiceImpl.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/employeeInfo/query", param);
            String res = restTemplate.getForEntity(url, String.class).getBody();
            List<Long> userIds = new ArrayList<>();
            JSONArray array = JSONArray.parseArray(res);
            for (int i = 0; i < array.size(); i++) {
                JSONObject json = array.getJSONObject(i);

                if (json.containsKey("id")) {
                    userIds.add(json.getLong("id"));
                }
            }
            if (userIds.size() > 0) {
                criteria.andCreateByIn(userIds);
            } else {
                //按名称模糊查找没有对应用户时
                criteria.andCreateByEqualTo(0l);
            }
        }
        if (query.getTaxLower() != null) {
            StringBuilder condiction = new StringBuilder();
            condiction.append(" IFNULL(total_tax_included_price, 0)-IFNULL(total_penalty, 0)-IFNULL(total_exclusive_of_tax, 0) >= ").append(query.getTaxLower());
            criteria.andCondition(condiction.toString());
        }
        if (query.getTaxUpper() != null) {
            StringBuilder condiction = new StringBuilder();
            condiction.append(" IFNULL(total_tax_included_price, 0)-IFNULL(total_penalty, 0)-IFNULL(total_exclusive_of_tax, 0) <= ").append(query.getTaxUpper());
            criteria.andCondition(condiction.toString());
        }

        //invoice_apply_header 开票信息的头部信息
        example.setOrderByClause("create_at desc");//设置开票的时间的顺序问题
        //获取用户名的数据权限
        List<Long> idList = SystemContext.getOus();
        if (ObjectUtils.isEmpty(idList)) {
            idList.add(-1L);
        }
        criteria.andOuIdIn(idList);
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        final List<InvoiceApplyHeader> list1 = headerMapper.selectByExample(example);
        PageInfo<InvoiceApplyHeaderDto> pageInfo = BeanConverter.convertPage(list1, InvoiceApplyHeaderDto.class);
        if (ListUtils.isEmpty(pageInfo.getList())) return pageInfo;
        OperatingUnit ou;
        UserInfo user;
        for (InvoiceApplyHeaderDto dto : pageInfo.getList()) {
            ou = null;
            ou = CacheDataUtils.findOuById(dto.getOuId());
            if (ou != null) dto.setOuName(ou.getOperatingUnitName());
            user = null;
            user = CacheDataUtils.findUserById(dto.getCreateBy());
            if (user != null) dto.setCreateUserName(user.getName());

        }
        return pageInfo;
    }


    @Override
    public PageInfo<InvoiceApplyHeaderDto> myquery(InvoiceApplyQuery query) {
        InvoiceApplyHeaderExample example = new InvoiceApplyHeaderExample();
        InvoiceApplyHeaderExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(query.getApplyCode())) criteria.andApplyCodeEqualTo(query.getApplyCode());
        if (StringUtils.isNotBlank(query.getApplyType())) criteria.andApplyTypeEqualTo(query.getApplyType());
        if (StringUtils.isNotBlank(query.getInvoiceDateStart()))
            criteria.andInvoiceDateGreaterThanOrEqualTo(DateUtil.parseDate(query.getInvoiceDateStart(), "yyyy-MM-dd"));
        if (StringUtils.isNotBlank(query.getInvoiceDateEnd()))
            criteria.andInvoiceDateLessThan(DateUtil.parseDate(query.getInvoiceDateEnd(), "yyyy-MM-dd"));
        if (StringUtils.isNotBlank(query.getCustomerName()))
            criteria.andCustomerNameLike("%" + query.getCustomerName() + "%");
        if (StringUtils.isNotBlank(query.getOuId())) criteria.andOuIdEqualTo(Long.parseLong(query.getOuId()));
        if (StringUtils.isNotBlank(query.getGlDateStart()))
            criteria.andGlDateGreaterThanOrEqualTo(DateUtil.parseDate(query.getGlDateStart(), "yyyy-MM-dd"));
        if (StringUtils.isNotBlank(query.getGlDateEnd()))
            criteria.andGlDateLessThan(DateUtil.parseDate(query.getGlDateEnd(), "yyyy-MM-dd"));
        if (null != query.getStatus()) criteria.andStatusEqualTo(query.getStatus());
        if (StringUtils.isNotBlank(query.getTaxIncludedPrice()))
            criteria.andTotalTaxIncludedPriceEqualTo(BigDecimal.valueOf(Float.parseFloat(query.getTaxIncludedPrice())));
        if (StringUtils.isNotBlank(query.getCreateAtStart()))
            criteria.andCreateAtGreaterThanOrEqualTo(DateUtil.parseDate(query.getCreateAtStart(), "yyyy-MM-dd"));
        if (StringUtils.isNotBlank(query.getCreateAtEnd()))
            criteria.andCreateAtLessThan(DateUtil.parseDate(query.getCreateAtEnd(), "yyyy-MM-dd"));
        //按创建者姓名模糊查询
        if (StringUtils.isNotBlank(query.getCreateUserName())) {
            Map<String, Object> param = new HashMap<>();
            param.put("userName", query.getCreateUserName());
            //得到对应的用户信息 ltc_user_info
            String url = WorkingHourServiceImpl.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/employeeInfo/query", param);
            String res = restTemplate.getForEntity(url, String.class).getBody();
            List<Long> userIds = new ArrayList<>();
            JSONArray array = JSONArray.parseArray(res);
            for (int i = 0; i < array.size(); i++) {
                JSONObject json = array.getJSONObject(i);

                if (json.containsKey("id")) {
                    userIds.add(json.getLong("id"));
                }
            }
            if (userIds.size() > 0) {
                criteria.andCreateByIn(userIds);
            } else {
                //按名称模糊查找没有对应用户时
                criteria.andCreateByEqualTo(0l);
            }
        }
        if (query.getTaxLower() != null) {
            StringBuilder condiction = new StringBuilder();
            condiction.append(" IFNULL(total_tax_included_price, 0)-IFNULL(total_penalty, 0)-IFNULL(total_exclusive_of_tax, 0) >= ").append(query.getTaxLower());
            criteria.andCondition(condiction.toString());
        }
        if (query.getTaxUpper() != null) {
            StringBuilder condiction = new StringBuilder();
            condiction.append(" IFNULL(total_tax_included_price, 0)-IFNULL(total_penalty, 0)-IFNULL(total_exclusive_of_tax, 0) <= ").append(query.getTaxUpper());
            criteria.andCondition(condiction.toString());
        }

        //invoice_apply_header 开票信息的头部信息
        example.setOrderByClause("create_at desc");//设置开票的时间的顺序问题


        //当前
        StringBuffer sqlStr = new StringBuffer();
        sqlStr.append("(" +
                //满足销售子合同其中销售经理或项目经理
                "id IN(" +
                "SELECT DISTINCT apply_header_id" +
                " FROM " +
                "invoice_apply_details d INNER JOIN contract c ON d.contract_id = c.id" +
                " WHERE " +
                "(c.sales_manager = " + SystemContext.getUserId() + " OR c.manager = " + SystemContext.getUserId() + " ) AND c.deleted_flag = 0" +
                ") " +
                //满足开票申请单创建人
                "OR id IN (" +
                "SELECT DISTINCT id " +
                "FROM " +
                "invoice_apply_header " +
                "WHERE " +
                "create_by =" + SystemContext.getUserId() +
                ")" +
                ")" +
                //当前单位下
                "and ou_id in(" + basedataExtService.queryCurrentStrUnitOu() + ")");
        criteria.andCondition(sqlStr.toString());


        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        final List<InvoiceApplyHeader> list1 = headerMapper.selectByExample(example);
        PageInfo<InvoiceApplyHeaderDto> pageInfo = BeanConverter.convertPage(list1, InvoiceApplyHeaderDto.class);
        if (ListUtils.isEmpty(pageInfo.getList())) return pageInfo;
        OperatingUnit ou;
        UserInfo user;
        for (InvoiceApplyHeaderDto dto : pageInfo.getList()) {
            ou = null;
            ou = CacheDataUtils.findOuById(dto.getOuId());
            if (ou != null) dto.setOuName(ou.getOperatingUnitName());
            user = null;
            user = CacheDataUtils.findUserById(dto.getCreateBy());
            if (user != null) dto.setCreateUserName(user.getName());
        }
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(InvoiceApplyDto dto) {
        // 查询库存组织参数：开票申请启用账期
        Set<String> set = organizationCustomDictService.queryByName("开票申请启用账期", SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
        if (!CollectionUtils.isEmpty(set) && set.contains("是") && StringUtils.isEmpty(dto.getPaymentTerm())) {
            throw new MipException("账期（天）不能为空，请检查数据");
        }
        boolean agesFlag = false;//默认非必填
        Set<String> ageSet = organizationCustomDictService.queryByName("开票申请填写应收账龄明细", SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
        if (!CollectionUtils.isEmpty(ageSet) && ageSet.contains("1")) {
            agesFlag = true;
            if (CollectionUtils.isEmpty(dto.getReceivableAgesDTOList())) {
                throw new MipException("开票申请账龄信息未维护，请补充");
            }
        }
        //检查是否存在配置，再检查客户
        Long ouId = dto.getOuId();
        List<String> autoPushMif = new ArrayList<>(organizationCustomDictService.queryByName("是否自动推送开票平台", ouId, OrgCustomDictOrgFrom.OU));
        if (!autoPushMif.isEmpty() && ("0".equals(autoPushMif.get(0)))) {
            String customerCode = dto.getCustomerCode();
            Assert.notNull(customerCode, "客户编号不能为空");
            //如果客户是E开头的客户时，校验客户是否维护企业性质
            if (customerCode.startsWith("E")) {
                Map<String, CustomerDto> customerMap = crmExtService.getCustomerByCode(Collections.singletonList(customerCode))
                        .stream().collect(Collectors.toMap(CustomerDto::getCrmCode, e -> e));
                Customer customer = customerMap.get(customerCode);
                Assert.notNull(customer, "客户不存在");
                Assert.notNull(customer.getEnterpriseNatureName(), "请先到客户详情页维护客户的企业性质");
            }
        }
        InvoiceApplyHeader header = null;
        if (dto.getId() != null) {
            header = headerMapper.selectByPrimaryKey(dto.getId());
        }
        if (header == null) {
            header = new InvoiceApplyHeader();
            String seqPerfix = basedataExtService.getUnitSeqPerfix(SystemContext.getUnitId());
            //R+FP+YY+MM+4位流水码
            String code = seqPerfix + "FP" + CacheDataUtils.generateSequence(4, seqPerfix + "FP", DateUtil.DATE_YYMM_PATTERN);
            header.setApplyCode(code);
            header.setApplyDate(new Date());
        }
        BigDecimal totalTaxIncludedPrice = BigDecimal.ZERO;
        BigDecimal totalExclusiveOfTax = BigDecimal.ZERO;
        BigDecimal totalPenalty = BigDecimal.ZERO;
        BigDecimal exclusiveOfTax;
        BigDecimal receivableAmount = BigDecimal.ZERO;
        BigDecimal race;
        Long planId = null;
        if (StringUtils.isNotBlank(dto.getName())) header.setName(dto.getName());//名称
        if (StringUtils.isNotBlank(dto.getApplyType()))
            header.setApplyType(dto.getApplyType());//申请类型：plan合同开票计划，hand手工开票
        if (dto.getCustomerId() != null) header.setCustomerId(dto.getCustomerId());//客户ID
        if (StringUtils.isNotBlank(dto.getCustomerName())) header.setCustomerName(dto.getCustomerName());//客户名称
        if (StringUtils.isNotBlank(dto.getCustomerCode())) header.setCustomerCode(dto.getCustomerCode());//客户编码
        if (StringUtils.isNotBlank(dto.getInvoiceCustomer()))
            header.setInvoiceCustomer(dto.getInvoiceCustomer());//开票客户
        if (dto.getOuId() != null) header.setOuId(dto.getOuId());//业务实体
        if (StringUtils.isNotBlank(dto.getCurrencyCode())) header.setCurrencyCode(dto.getCurrencyCode());//币种
        if (StringUtils.isNotBlank(dto.getExchangeRateType()))
            header.setExchangeRateType(dto.getExchangeRateType());//汇率类型
        if (dto.getExchangeRateDate() != null) header.setExchangeRateDate(dto.getExchangeRateDate());//汇率日期
        if (dto.getLimitPrice() != null) header.setLimitPrice(dto.getLimitPrice());//限额
        if (StringUtils.isNotBlank(dto.getInvoiceType())) header.setInvoiceType(dto.getInvoiceType());//应收发票类型
        if (dto.getInvoiceDate() != null) header.setInvoiceDate(dto.getInvoiceDate());//开票日期
        if (dto.getPlanInvoiceType() != null) header.setPlanInvoiceType(dto.getPlanInvoiceType());//发票类型
        if (dto.getGlDate() != null) header.setGlDate(dto.getGlDate());//总账日期(记账日期)
        if (StringUtils.isNotBlank(dto.getRemark())) header.setRemark(dto.getRemark());//备注
        if (null != (dto.getStatus())) header.setStatus(dto.getStatus());//状态
        if (StringUtils.isNotBlank(dto.getSourceCode())) header.setSourceCode(dto.getSourceCode());//来源编码
        if (dto.getSendToKpy() != null) header.setSendToKpy(dto.getSendToKpy());//是否写入开票易
        if (dto.getSettlementSheetId() != null) header.setSettlementSheetId(dto.getSettlementSheetId());//结算单id
        if (StringUtils.isNotBlank(dto.getEamContractCode()))
            header.setEamPurchaseCode(dto.getEamContractCode());//EAM采购合同编号
        if (StringUtils.isNotBlank(dto.getPayStage())) header.setPayStage(dto.getPayStage());//付款期数
        if (dto.getModelkey() != null) header.setModelkey(dto.getModelkey());//模块标识
        if (StringUtils.isNotBlank(dto.getAnnex())) header.setAnnex(dto.getAnnex());//附件id
        if (dto.getConversionRate() != null) header.setConversionRate(dto.getConversionRate());//汇率
        if (dto.getPaymentTerm() != null) header.setPaymentTerm(dto.getPaymentTerm()); //账期
        List<InvoiceApplyPlanDetailDTO> planDetailDTOList = dto.getPlanDetailList();
        if (ListUtils.isEmpty(planDetailDTOList)
                || ListUtils.isEmpty(planDetailDTOList.get(0).getApplyDetailList())) {
            throw new MipException("开票明细数据不能为空");
        }
        InvoiceApplyDetailsDto invoiceApplyDetailsDto = planDetailDTOList.get(0).getApplyDetailList().get(0);
        // 合同表 修改 eampurchaseId
        Long contractId = invoiceApplyDetailsDto.getContractId();
        Guard.notNull(contractId, "开票明细数据的合同Id不能为空");
        Contract contract = contractMapper.selectByPrimaryKey(contractId);
        Guard.notNull(contract, String.format("开票明细数据的合同Id：%s对应的合同为空", contractId));
//        contract.setEampurchaseId(Optional.ofNullable(dto.getEamContractId()).map(String::valueOf).orElse(null));
        contractMapper.updateByPrimaryKey(contract);

        header.setContractId(contractId);//合同Id
        planId = invoiceApplyDetailsDto.getPlanId();
        Guard.notNull(header.getPlanInvoiceType(), "发票类型不能为空");
        //  EAM关联交易优化
        if (StringUtils.isNotEmpty(header.getEamPurchaseCode())) {
            header.setEamStatus(InvoiceApplyStatusEnum.HEADER_PASS.code());
        }
        // 允许不同合同合并开票
        header.setMergeDifferentInvoic(Boolean.TRUE.equals(dto.getMergeDifferentInvoic()));
        if (header.getId() == null) {
            header.setStatus(InvoiceApplyStatusEnum.HEADER_DRAFT.code());
            headerMapper.insertSelective(header);
        } else {
            headerMapper.updateByPrimaryKeySelective(header);
        }
        // 先删除现有明细数据
        deleteApplyDetailByHeaderId(header.getId());
        // 根据计划明细保存对应的开票明细金额
        for (InvoiceApplyPlanDetailDTO planDetailDTO : planDetailDTOList) {
            List<InvoiceApplyDetailsDto> applyDetailsDtoList = planDetailDTO.getApplyDetailList();
            if (ListUtils.isNotEmpty(applyDetailsDtoList)) {
                planDetailDTO.setTotalTaxIncludedPrice(BigDecimal.ZERO);
                for (InvoiceApplyDetailsDto applyDetailsDto : applyDetailsDtoList) {
                    if (Objects.equals(applyDetailsDto.getDeletedFlag(), Boolean.TRUE)) {
                        continue;
                    }
                    if (applyDetailsDto.getTaxIncludedPrice() == null
                            || applyDetailsDto.getExclusiveOfTax() == null
                            || applyDetailsDto.getQuantity() == null
                            || applyDetailsDto.getTaxRace() == null) {
                        throw new MipException("开票明细数据金额不能为空");
                    }
                    applyDetailsDto.setApplyHeaderId(header.getId());
                    applyDetailsDto.setStatus((!ObjectUtils.isEmpty(header.getStatus())) ? header.getStatus() :
                            InvoiceApplyStatusEnum.HEADER_DRAFT.code());
                    applyDetailsDto.setTaxIncludedPrice(applyDetailsDto.getTaxIncludedPrice().setScale(2, BigDecimal.ROUND_HALF_UP));//含税金额
                    totalTaxIncludedPrice = totalTaxIncludedPrice.add(applyDetailsDto.getTaxIncludedPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                    planDetailDTO.setTotalTaxIncludedPrice(planDetailDTO.getTotalTaxIncludedPrice().add(applyDetailsDto.getTaxIncludedPrice()));
                    if (applyDetailsDto.getPenalty() != null) {
                        applyDetailsDto.setPenalty(applyDetailsDto.getPenalty().setScale(2, BigDecimal.ROUND_HALF_UP));//罚扣金额
                        totalPenalty = totalPenalty.add(applyDetailsDto.getPenalty());
                    }
                    applyDetailsDto.setTaxRace(applyDetailsDto.getTaxRace().setScale(2, BigDecimal.ROUND_HALF_UP));//税率
                    //计算不含税金额
                    exclusiveOfTax = applyDetailsDto.getTaxIncludedPrice().setScale(2, BigDecimal.ROUND_HALF_UP);
                    if (applyDetailsDto.getPenalty() != null) {
                        //含税金额-扣减金额
                        exclusiveOfTax = exclusiveOfTax.subtract(applyDetailsDto.getPenalty().setScale(2, BigDecimal.ROUND_HALF_UP).abs());
                    }
                    //不含税金额 = 含税金额 / (1+税率）
                    //(100+税率)/100
                    race =
                            BigDecimal.valueOf(100).setScale(2, BigDecimal.ROUND_HALF_UP).add(applyDetailsDto.getTaxRace()).divide(BigDecimal.valueOf(100).setScale(2, BigDecimal.ROUND_HALF_UP), 2);
                    //不含税金额
                    exclusiveOfTax = exclusiveOfTax.divide(race, 2, BigDecimal.ROUND_HALF_UP);
                    ;//除
                    applyDetailsDto.setExclusiveOfTax(exclusiveOfTax);
                    totalExclusiveOfTax = totalExclusiveOfTax.add(exclusiveOfTax);
                    applyDetailsDto.setDeletedFlag(Boolean.FALSE);
                    applyDetailsDto.setRemark(planDetailDTO.getRemark()); //把开票明细的票面备注存到每个行
                    detailsMapper.insertSelective(applyDetailsDto);
                    /**
                     * 保存附件文件信息ctc_attachment
                     */
                    CtcAttachmentDto ctcAttachmentDto = applyDetailsDto.getCtcAttachmentDto();
                    if (!ObjectUtils.isEmpty(ctcAttachmentDto) && ctcAttachmentDto.getId() == null) {
                        ctcAttachmentDto.setModuleId(applyDetailsDto.getId());
                        ctcAttachmentDto.setModule(CtcAttachmentModule.APPLICATION_FOR_INVOICING.code());
                        ctcAttachmentMapper.insert(ctcAttachmentDto);
                    }
                }
                /**
                 * 开票计划详情里面的实际开票金额不能大于计划开票金额
                 */
                InvoicePlanDetail invoicePlanDetail = planDetailMapper.selectByPrimaryKey(planDetailDTO.getPlanDetailId());
                BigDecimal tax = invoicePlanDetail.getTaxIncludedPrice() == null ? BigDecimal.ZERO : invoicePlanDetail.getTaxIncludedPrice();
                if (tax.add(planDetailDTO.getTotalTaxIncludedPrice()).compareTo(invoicePlanDetail.getAmount().add(BigDecimal.ZERO)) == 1) {
                    throw new MipException("开票总金额不能大于计划开票金额");
                }
            }
        }
        header.setTotalTaxIncludedPrice(totalTaxIncludedPrice);
        List<InvoiceApplyReceivableAgesDTO> receivableAgesDTOList =
                Optional.ofNullable(dto.getReceivableAgesDTOList()).orElse(new ArrayList<>()).stream().filter(e -> Objects.isNull(e.getDeletedFlag()) || Boolean.FALSE.equals(e.getDeletedFlag())).collect(Collectors.toList());
        if (agesFlag && ListUtils.isNotEmpty(receivableAgesDTOList)) {
            //检查数据的合法性
            checkAgesDTOList(receivableAgesDTOList, header);
            //先删除所有
            if (Objects.nonNull(header.getId())) {
                invoiceApplyReceivableAgesExtMapper.deleteAllByApplyHeaderId(header.getId());
            }
            for (InvoiceApplyReceivableAgesDTO receivableAgesDTO : receivableAgesDTOList) {
                InvoiceApplyReceivableAges receivableAges = BeanConverter.copy(receivableAgesDTO, InvoiceApplyReceivableAges.class);
                receivableAges.setApplyHeaderId(header.getId());
                receivableAges.setDeletedFlag(Boolean.FALSE);
                invoiceApplyReceivableAgesMapper.insert(receivableAges);
            }
        }

        boolean lessThanFlag = false;

        // 组织参数控制金额
        String dictName = "开票申请是否允许小于结算单金额";
        Set<String> valueSet = organizationCustomDictService.queryByName(dictName, dto.getOuId(),
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
        if (!CollectionUtils.isEmpty(valueSet)) {
            String next = valueSet.iterator().next();
            if (Objects.equals(next, "是")) {
                lessThanFlag = true;
            }
        }

        if (lessThanFlag) {
            if (header.getSettlementSheetId() != null && totalTaxIncludedPrice.compareTo(dto.getSettlementPrice()) > 0) {
                throw new BizException(Code.ERROR,
                        "开票申请总额（含税）必须小于等于结算单金额（含税），开票申请金额：" + totalTaxIncludedPrice + "，结转单金额：" + dto.getSettlementPrice());
            }
        } else {
            if (header.getSettlementSheetId() != null && totalTaxIncludedPrice.compareTo(dto.getSettlementPrice()) != 0) {
                throw new BizException(Code.ERROR,
                        "开票申请总额（含税）必须等于结算单金额（含税），开票申请金额：" + totalTaxIncludedPrice + "，结转单金额：" + dto.getSettlementPrice());
            }
        }


        header.setTotalPenalty(totalPenalty);
        header.setTotalExclusiveOfTax(totalExclusiveOfTax);
        headerMapper.updateByPrimaryKeySelective(header);
        return header.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer updateEamPurchase(InvoiceApplyDto dto) {
        Guard.notNull(dto, "开票申请头信息为空");
        Long invoiceApplyHeaderId = dto.getId();
        Guard.notNull(invoiceApplyHeaderId, "开票申请头ID为空");
        InvoiceApplyHeader applyHeader = headerMapper.selectByPrimaryKey(invoiceApplyHeaderId);
        Guard.notNull(applyHeader, String.format("开票申请头ID：%s不存在", invoiceApplyHeaderId));

        // 合同表 修改 eampurchaseId
        Long contractId = applyHeader.getContractId();
        Guard.notNull(contractId, "开票申请的开票明细数据的合同Id不能为空");
        Contract contract = contractMapper.selectByPrimaryKey(contractId);
        Guard.notNull(contract, String.format("开票申请的开票明细数据的合同Id：%s对应的合同为空", contractId));
        contract.setEampurchaseId(Optional.ofNullable(dto.getEamContractId()).map(String::valueOf).orElse(null));
        contractMapper.updateByPrimaryKey(contract);

        return headerMapper.updateByPrimaryKeySelective(dto);
    }

    private void deleteApplyDetailByHeaderId(Long applyHeaderId) {
        InvoiceApplyDetailsExample example = new InvoiceApplyDetailsExample();
        example.createCriteria().andApplyHeaderIdEqualTo(applyHeaderId);
        List<InvoiceApplyDetails> invoiceApplyDetailsList = invoiceApplyDetailsMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(invoiceApplyDetailsList)) {
            for (InvoiceApplyDetails applyDetail : invoiceApplyDetailsList) {
                invoiceApplyDetailsMapper.deleteByPrimaryKey(applyDetail.getId());
            }
        }
    }

    @Override
    @Transactional
    public InvoiceApplyDto updateReceivableAges(InvoiceApplyDto dto) {
        Long applyHeaderId = dto.getId();
        Guard.notNull(applyHeaderId, "开票申请Id不能为空");

        InvoiceApplyHeader header = headerMapper.selectByPrimaryKey(applyHeaderId);
        Guard.notNull(header, "开票申请不存在");

        List<InvoiceApplyReceivableAgesDTO> receivableAgesDTOList = dto.getReceivableAgesDTOList().stream().filter(e -> Objects.isNull(e.getDeletedFlag()) || Boolean.FALSE.equals(e.getDeletedFlag())).collect(Collectors.toList());
        Guard.notNullOrEmpty(receivableAgesDTOList, "应收发票账龄明细不能为空");
        //检查数据的合法性
        checkAgesDTOList(receivableAgesDTOList, header);
        //先删除所有
        invoiceApplyReceivableAgesExtMapper.deleteAllByApplyHeaderId(applyHeaderId);
        for (InvoiceApplyReceivableAgesDTO receivableAgesDTO : receivableAgesDTOList) {
            InvoiceApplyReceivableAges receivableAges = BeanConverter.copy(receivableAgesDTO, InvoiceApplyReceivableAges.class);
            receivableAges.setApplyHeaderId(applyHeaderId);
            receivableAges.setDeletedFlag(Boolean.FALSE);
            invoiceApplyReceivableAgesMapper.insert(receivableAges);
        }
        //更新账龄明细的已冲销金额
        this.updateWriteOffAmountByApplyId(applyHeaderId);
        //查询账龄明细
        List<InvoiceApplyReceivableAgesDTO> dtoList = getInvoiceApplyReceivableAgesDTOS(applyHeaderId);
        dto.setReceivableAgesDTOList(dtoList);
        return dto;
    }

    private void checkAgesDTOList(List<InvoiceApplyReceivableAgesDTO> receivableAgesDTOList, InvoiceApplyHeader header) {
        Long invoiceApplyId = header.getId();
        List<InvoiceApplyDetails> invoiceApplyDetailsList = getInvoiceApplyDetails(invoiceApplyId);
        Map<String, BigDecimal> invoiceApplyDetailMap = invoiceApplyDetailsList.stream()
                .collect(Collectors.groupingBy(
                        InvoiceApplyDetails::getContractCode,
                        Collectors.mapping(InvoiceApplyDetails::getTaxIncludedPrice, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )));
        Map<String, BigDecimal> receivableAgesMap = receivableAgesDTOList.stream()
                .collect(Collectors.groupingBy(
                        InvoiceApplyReceivableAgesDTO::getContractCode,
                        Collectors.mapping(InvoiceApplyReceivableAgesDTO::getAmount, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )));
        // 判断两个 Map 中相同 contractId 对应的 金额 合计是否一致
        boolean noConsistent = false;
        List<String> errContractCodeList = new ArrayList<>();
        for (String contractCode : receivableAgesMap.keySet()) {
            BigDecimal receivableAgesTotal = receivableAgesMap.get(contractCode);
            //不能用getOrDefault,如果invoiceApplyDetailTotal为null则不相等，【receivableAgesTotal】一定存在值
            BigDecimal invoiceApplyDetailTotal = invoiceApplyDetailMap.get(contractCode);
            if (!BigDecimalUtils.equals(invoiceApplyDetailTotal, receivableAgesTotal)) {
                noConsistent = true;
                errContractCodeList.add(contractCode);
            }
        }
        if (noConsistent) {
            throw new MipException(
                    String.format("账龄行：%s合同金额与开票金额不一致，请调整", String.join(", ", errContractCodeList)));
        }
        // 计算 amount 的合计 -解决少提或漏合同的问题
        BigDecimal totalAmount = receivableAgesDTOList.stream()
                .map(InvoiceApplyReceivableAgesDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        if (totalAmount.compareTo(header.getTotalTaxIncludedPrice()) != 0) {
            throw new MipException("账龄行应收金额合计不等于开票申请总额，请检查数据");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long changeStatus(Long id, Integer status, String type) {     //String-Integer
        InvoiceApplyHeader header = headerMapper.selectByPrimaryKey(id);
        if (header != null) {
            header.setStatus(status);
            if (status.equals(InvoiceApplyStatusEnum.HEADER_PASS.code())) {
                header.setApprovedAt(new Date());
            }
            headerMapper.updateByPrimaryKeySelective(header);
            InvoiceApplyDetailsExample detailsExample = new InvoiceApplyDetailsExample();
            detailsExample.createCriteria().andApplyHeaderIdEqualTo(header.getId());
            List<InvoiceApplyDetails> details = detailsMapper.selectByExample(detailsExample);
            if (ListUtils.isNotEmpty(details)) {
                for (InvoiceApplyDetails detail : details) {
                    detail.setStatus(status);
                    detailsMapper.updateByPrimaryKeySelective(detail);

                    invoiceApplyDetailsExtMapper.updateInvoicePlanPrice(detail.getCode());//更新开票计划金额
                }
            }
            if (header.getEamPurchaseCode() != null) {
                header.setEamStatus(status);
                //开票状态同步EAM
                updateEamInvoiceStatus(header);
            }
            return header.getId();
        } else {
            return 0L;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEamInvoiceStatus(InvoiceApplyHeader header) {

        InvoiceApplyHeaderDto eamDto = new InvoiceApplyHeaderDto();
        eamDto.setModelKey(header.getModelkey());
        eamDto.setGeamContractCode(header.getEamPurchaseCode());
        eamDto.setPayStage(header.getPayStage());
        eamDto.setEamStatus(header.getEamStatus());
        eamDto.setCurrencyCode(header.getCurrencyCode());
        eamDto.setTotalTaxIncludedPrice(header.getTotalTaxIncludedPrice());
        eamDto.setApplyCode(header.getApplyCode());
        header.setEamMsg(InvoiceApplyStatusEnum.getValueByCode(header.getEamStatus()) + "状态同步成功");

        PmsResult pmsResult = eamInvokeHelperService.syncInvoiceStatus(eamDto);//开票状态同步至EAM
        if (Objects.equals(Boolean.FALSE, pmsResult.getSuccess())) {
            header.setEamMsg(InvoiceApplyStatusEnum.getValueByCode(header.getEamStatus())
                    + "同步失败,原因:" + pmsResult.getMessage());
            header.setEamStatus(InvoiceApplyStatusEnum.HEADER_ERROR.getCode());
        }
        invoiceApplyHeaderMapper.updateByPrimaryKeySelective(header);//更新eam同步状态
    }

    /**
     * 计算应收发票到账日期
     *
     * @return Date ： 到账日期
     */
    private Date calculateDueDate(InvoiceApplyDto header) {
        String paymentTerm = header.getPaymentTerm();
        if (StringUtils.isEmpty(paymentTerm)) {
            return null;
        }
        int leftIndex = paymentTerm.indexOf("（");
        int days = Integer.parseInt(paymentTerm.substring(0, leftIndex));
        String ruleStr = paymentTerm.substring(leftIndex + 1, paymentTerm.length() - 1);
        return "最新开票申请审批通过日期".equals(ruleStr) ? DateUtils.addDay(header.getApprovedAt(), days) : null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long splitReceivable(Long id) {

        //改变状态 提交审批改成InvoiceApplyStatusEnum.HEADER_CHECKING.code()
        changeStatus(id, InvoiceApplyStatusEnum.HEADER_PASS.code(), null);
        //包含开票头部、开票详情信息、应收发票信息
        InvoiceApplyDto header = queryById(id);
        // 开票限额 、税率 、 开票申请含税金额
        BigDecimal limitPrice, race, applyTaxIncludePrice;
        InvoiceReceivable receivable = null;
        List<InvoiceReceivable> receivables = new ArrayList<>();
        List<InvoiceApplyDetailSplit> applyDetailSplits = new ArrayList<>();
        Map<Long, InvoiceApplyDetails> invoiceApplyDetailsMap = new HashMap<>();
        for (InvoiceApplyPlanDetailDTO applyPlanDetailDTO : header.getPlanDetailList()) {
            //(100+税率)/100
            race = BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP)
                    .add(applyPlanDetailDTO.getTaxRace().setScale(2, RoundingMode.HALF_UP))
                    .divide(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP), RoundingMode.CEILING);
            if (header.getLimitPrice() == null || header.getLimitPrice().compareTo(new BigDecimal("999")) < 0) {
                limitPrice = new BigDecimal("999"); // 默认最低限额999
            } else {
                limitPrice = header.getLimitPrice();
            }
            if (limitPrice == null || BigDecimal.ZERO.compareTo(limitPrice) == 0
                    || BigDecimal.valueOf(-1L).compareTo(limitPrice) == 0
                    || Objects.equals(header.getPlanInvoiceType(), InvoicePlanInvoiceType.VAT_SPECIAL_ELECTRON.getCode())
                    || Objects.equals(header.getPlanInvoiceType(), InvoicePlanInvoiceType.VAT_GENERAL_ELECTRON.getCode())) {
                // 当限额等于-1的时候就是相当于不用拆分
                // 如果发票类型是 电子专票 或者 电子普票  开票申请不显示开票拆分限额，不做拆分动作
                limitPrice = applyPlanDetailDTO.getTotalTaxIncludedPrice(); // 多个开票申请行对应一个应收发票
            } else {
                //组织参数设置的金额为不含税，转为含税限额
                limitPrice = header.getLimitPrice().multiply(race).setScale(2, RoundingMode.HALF_UP);
            }
            /*
                允许不同合同合并开票=false（原逻辑）
                举例1:一个开票计划120W，开票限额100W，同一个开票计划拆分了3条明细30W+40W+50W，则开票申请审批通过后，生成70W、50W 两张应收发票（30W+40W<100W,30W+40W+50W>100W）
                举例2：一个开票计划待申请金额70W，开票限额100W，开票明细拆分成3条10W+20W+20W，则开票申请审批通过后，生成1张50W的应收发票；
                举例3：一个开票计划待申请金额160W，开票限额100W，本次开票将开票申请明细120W+30W，则开票申请审批通过后，生成2张应收发票，1张100W，1张50W（内含2条明细，20W+30W）

                允许不同合同合并开票=true（新逻辑）
            */

            // 允许不同合同合并开票=true（新逻辑）
            if (!Boolean.TRUE.equals(header.getMergeDifferentInvoic())) {
                receivable = new InvoiceReceivable();
            } else if (null == receivable) {
                receivable = new InvoiceReceivable();
            }
            receivables.add(receivable);
            for (InvoiceApplyDetails detail : applyPlanDetailDTO.getApplyDetailList()) {
                invoiceApplyDetailsMap.put(detail.getId(), detail);
                //把开票申请明细拆分成应收发票--------------------------
                applyTaxIncludePrice = detail.getTaxIncludedPrice(); // 开票申请含税金额

                // 允许不同合同合并开票=true（新逻辑）
                // 如果上一个应收发票还没满金额,但是加上本次开票申请行的金额满了的话,就生成一个新的应收发票
                if (!Boolean.TRUE.equals(header.getMergeDifferentInvoic())
                        && receivable.getId() != null
                        && receivable.getTaxIncludedPrice() != null
                        && receivable.getTaxIncludedPrice().add(applyTaxIncludePrice).compareTo(limitPrice) > 0) {
                    receivable = new InvoiceReceivable();
                    receivables.add(receivable);
                }
                do {
                    receivable.setApplyHeaderId(header.getId());
                    receivable.setApplyCode(header.getApplyCode());
                    receivable.setApplyDetailId(detail.getId());
                    receivable.setContractId(detail.getContractId());
                    receivable.setContractCode(detail.getContractCode());
                    receivable.setCustomerId(header.getCustomerId());
                    receivable.setCustomerName(header.getCustomerName());
                    receivable.setCustomerCode(header.getCustomerCode());
                    receivable.setInvoiceCustomer(header.getInvoiceCustomer());
                    receivable.setOuId(header.getOuId());
                    if (StringUtils.isEmpty(receivable.getInvoiceCode())) {
                        receivable.setInvoiceCode(this.generateInvoiceCode(receivable.getOuId()));//生成流水号
                    }
                    receivable.setInvoiceDate(header.getInvoiceDate());
                    receivable.setInvoiceType(header.getInvoiceType());
                    receivable.setCurrencyCode(header.getCurrencyCode());
                    receivable.setLimitPrice(header.getLimitPrice());
                    receivable.setGlDate(header.getApprovedAt());
                    receivable.setSendToKpy(header.getSendToKpy());
                    receivable.setPlanInvoiceType(header.getPlanInvoiceType());
                    receivable.setErpStatus(CommonErpStatus.NOT_PUSH.code().toString());
                    receivable.setCreateBy(header.getCreateBy());
                    receivable.setPrice(detail.getPrice());//单价
                    receivable.setConversionRate(header.getConversionRate());//汇率
                    logger.info("【发票拆分运算前信息】发票id：{}，applyDetailId：{}，contractCode：{}，limitPrice：{}，applyTaxIncludePrice：{}，taxIncludedPrice：{}，",
                            receivable.getId(), receivable.getApplyDetailId(), receivable.getContractCode(), limitPrice, applyTaxIncludePrice, receivable.getTaxIncludedPrice());
                    // 当前应收发票的含税金额
                    BigDecimal receivableTaxIncludedPrice = receivable.getTaxIncludedPrice() != null
                            ? receivable.getTaxIncludedPrice() : BigDecimal.ZERO;
                    // 当前应收发票的不含税金额
                    BigDecimal receivableExclusiveOfTax = receivable.getExclusiveOfTax() != null ? receivable.getExclusiveOfTax() : BigDecimal.ZERO;
                    // 应收发票占用开票申请行含税拆分金额
                    BigDecimal splitTaxIncludedPrice;
                    if (receivableTaxIncludedPrice.add(applyTaxIncludePrice).compareTo(limitPrice) > 0) {//含税金额 大于 限额
                        receivable.setTaxIncludedPrice(limitPrice);//取限额作为含税金额
                        splitTaxIncludedPrice = limitPrice.subtract(receivableTaxIncludedPrice).setScale(2, RoundingMode.HALF_UP);
                        // 开票申请行剩余金额
                        applyTaxIncludePrice = applyTaxIncludePrice.subtract(splitTaxIncludedPrice).setScale(2, RoundingMode.HALF_UP);
                    } else {
                        receivable.setTaxIncludedPrice(receivableTaxIncludedPrice.add(applyTaxIncludePrice));// 追加含税金额
                        splitTaxIncludedPrice = applyTaxIncludePrice;
                        applyTaxIncludePrice = BigDecimal.ZERO;
                    }
                    receivable.setQuantity(receivable.getTaxIncludedPrice().divide(detail.getPrice(), 15, RoundingMode.HALF_UP)); //数量
                    // 开票申请行拆分明细表不含税金额汇总,避免差额
                    receivableExclusiveOfTax = receivableExclusiveOfTax.add(splitTaxIncludedPrice.divide(race, 2, RoundingMode.HALF_UP));
                    logger.info("【发票拆分运算后信息】发票id：{}，applyDetailId：{}，contractCode：{}，limitPrice：{}，applyTaxIncludePrice：{}，taxIncludedPrice：{}，splitTaxIncludedPrice：{}，receivableExclusiveOfTax：{}",
                            receivable.getId(), receivable.getApplyDetailId(), receivable.getContractCode(), limitPrice, applyTaxIncludePrice, receivable.getTaxIncludedPrice(), splitTaxIncludedPrice, receivableExclusiveOfTax);

                    receivable.setExclusiveOfTax(receivableExclusiveOfTax);
                    receivable.setTaxRace(detail.getTaxRace());
                    if (StringUtils.isNotBlank(detail.getTaxCode())) {
                        receivable.setTaxCode(detail.getTaxCode());
                    } else {
                        /**
                         * 从税码表中提取税率编码
                         */
                        String taxVale = String.valueOf(detail.getTaxRace().intValue());
                        String taxType = "2";
                        String url = ModelsEnum.BASEDATA.getBaseUrl() + "finance/taxInfo/getTaxInfoList?taxValue=" + taxVale + "&taxType=" + taxType;
                        String res = restTemplate.getForEntity(url, String.class).getBody();
                        PageInfo<TaxInfoDto> data = JSON.parseObject(res, new TypeReference<PageInfo<TaxInfoDto>>() {
                        });
                        if (Objects.nonNull(data) && ListUtils.isNotEmpty(data.getList())) {
                            receivable.setTaxCode(data.getList().get(0).getTaxCode());
                        }
                    }
                    receivable.setUnit(detail.getUnit());//单位
                    receivable.setProduct(detail.getProduct());
                    receivable.setProductTaxCode(detail.getProductTaxCode());
                    receivable.setProductTaxName(detail.getProductTaxName());
                    receivable.setRemark(detail.getRemark());
                    receivable.setExchangeRateDate(header.getExchangeRateDate());
                    receivable.setExchangeRateType(header.getExchangeRateType());
                    // InvoiceApplyStatusEnum.(detail.getStatus())
                    receivable.setStatus(InvoiceApplyStatusEnum.getValueByCode(detail.getStatus()));
                    receivable.setDeletedFlag(0);
                    receivable.setPaymentTerm(header.getPaymentTerm());
                    receivable.setDueDate(calculateDueDate(header));
                    receivable.setSendMifStatus(0);
                    if (Objects.equals(receivable.getSendToKpy(), 1)) {
                        receivable.setSendMifStatus(3);
                    }
                    receivable.setSpecBusinessTag(detail.getSpecBusinessTag());
                    receivable.setSpecBusinessInfo(detail.getSpecBusinessInfo());
                    // 应收发票金额没有超限额就接着加
                    if (receivable.getId() == null) {
                        invoiceReceivableService.insertSelective(receivable);
                    } else {
                        invoiceReceivableService.updateByPrimaryKeySelective(receivable);
                    }
                    // 新增开票申请行拆分明细表, 用于erp接口同步支持一对多
                    InvoiceApplyDetailSplit applyDetailSplit = new InvoiceApplyDetailSplit();
                    applyDetailSplit.setApplyDetailId(detail.getId());
                    applyDetailSplit.setInvoiceReceivableId(receivable.getId());
                    applyDetailSplit.setTaxIncludedPrice(splitTaxIncludedPrice);
                    applyDetailSplit.setRemainTaxIncludedPrice(splitTaxIncludedPrice);
                    applyDetailSplit.setTaxRace(detail.getTaxRace());
                    applyDetailSplit.setExclusiveOfTax(splitTaxIncludedPrice.divide(race, 2, RoundingMode.HALF_UP));
                    applyDetailSplit.setRemainExclusiveOfTax(splitTaxIncludedPrice.divide(race, 2, RoundingMode.HALF_UP));
                    applyDetailSplit.setTaxCode(receivable.getTaxCode());
                    applyDetailSplit.setQuantity(splitTaxIncludedPrice.divide(detail.getPrice(), 15, RoundingMode.HALF_UP));
                    applyDetailSplit.setRemainQuantity(splitTaxIncludedPrice.divide(detail.getPrice(), 15, RoundingMode.HALF_UP));
                    applyDetailSplit.setPrice(detail.getPrice());
                    applyDetailSplit.setUnit(detail.getUnit());
                    applyDetailSplit.setRemark(detail.getRemark());
                    applyDetailSplit.setProduct(detail.getProduct());
                    applyDetailSplit.setModel(detail.getModel());
                    applyDetailSplit.setProductTaxCode(detail.getProductTaxCode());
                    applyDetailSplit.setProductTaxName(detail.getProductTaxName());
                    applyDetailSplit.setContractId(detail.getContractId());
                    applyDetailSplit.setContractCode(detail.getContractCode());
                    applyDetailSplit.setApplyHeaderId(detail.getApplyHeaderId());
                    applyDetailSplit.setPlanId(detail.getPlanId());
                    applyDetailSplit.setPlanDetailId(detail.getPlanDetailId());
                    applyDetailSplit.setDeletedFlag(Boolean.FALSE);
                    applyDetailSplit.setSpecBusinessTag(detail.getSpecBusinessTag());
                    applyDetailSplit.setSpecBusinessInfo(detail.getSpecBusinessInfo());
                    invoiceApplyDetailSplitMapper.insert(applyDetailSplit);
                    applyDetailSplits.add(applyDetailSplit);
                    // 如果开票计划和有剩余的金额, 新建应收发票
                    if (applyTaxIncludePrice.compareTo(BigDecimal.ZERO) > 0) {
                        receivable = new InvoiceReceivable();
                        receivables.add(receivable);
                    }
                } while (applyTaxIncludePrice.compareTo(BigDecimal.ZERO) > 0);
            }
        }

        //AAIG012398-修正生产经常遇到拆分小数出现很多位的情况
        if (!CollectionUtils.isEmpty(applyDetailSplits)) {
            Map<Long, List<InvoiceApplyDetailSplit>> map = applyDetailSplits.stream().collect(Collectors.groupingBy(InvoiceApplyDetailSplit::getApplyDetailId));
            for (Long applyDetailId : map.keySet()) {
                List<InvoiceApplyDetailSplit> list = map.get(applyDetailId);
                if (list != null && list.size() == 1) { //在生成发票的时候，开票明细的行没有进行拆分只归属到一个应收发票的明细下面
                    InvoiceApplyDetailSplit invoiceApplyDetailSplit = list.get(0);
                    InvoiceApplyDetails invoiceApplyDetail = invoiceApplyDetailsMap.get(applyDetailId);
                    if (Objects.nonNull(invoiceApplyDetailSplit) && Objects.nonNull(invoiceApplyDetail)
                            && !BigDecimalUtils.isEquals(invoiceApplyDetailSplit.getQuantity(), invoiceApplyDetail.getQuantity())) {
                        //修正因为总价/单价除不尽出现小数点的数量
                        invoiceApplyDetailSplit.setQuantity(invoiceApplyDetail.getQuantity());
                        invoiceApplyDetailSplit.setRemainQuantity(invoiceApplyDetail.getQuantity());
                        invoiceApplyDetailSplit.setUpdateAt(new Date());
                        invoiceApplyDetailSplitMapper.updateByPrimaryKeySelective(invoiceApplyDetailSplit);
                    }
                }
            }
        }

        //根据开票申请id查询到结算单明细
        List<WorkingHour> updateWorkingHours = new ArrayList<>();
        if (header.getSettlementSheetId() != null) {
            List<RdmSettlementSheetDetail> rdmSettlementSheetDetails = getRdmSettlementSheetDetails(header.getSettlementSheetId());
            if (ListUtils.isNotEmpty(rdmSettlementSheetDetails)) {
                rdmSettlementSheetDetails.forEach(r -> {
                    //根据mipName+来源RDM确认修改工时的数据
                    WorkingHourExample workingHourExample = new WorkingHourExample();
                    workingHourExample.createCriteria().andDeleteFlagEqualTo(0).andRdmFlagEqualTo(1).andUserMipEqualTo(r.getMipname())
                            .andApplyDateBetween(r.getStartTime(), r.getEndTime());
                    List<WorkingHour> workingHours = workingHourMapper.selectByExample(workingHourExample);
                    workingHours.forEach(w -> {
                        w.setInvoiceApplyHeaderId(header.getId());
                        updateWorkingHours.add(w);
                    });
                });
            }
        }
        if (ListUtils.isNotEmpty(updateWorkingHours)) {
            workingHourExtMapper.batchUpdate(updateWorkingHours);
        }
        //如果是关联交易,获取交易号并保存到开票申请
        if (header.getEamPurchaseCode() != null) {
            //同步开票状态
            header.setEamStatus(InvoiceApplyStatusEnum.HEADER_PASS.code());
            updateEamInvoiceStatus(header);

            // 开票手工关联采购合同 added by dengfei added at20210125
            EamPurchaseInfoExample eamPurchaseInfoExample = new EamPurchaseInfoExample();
            eamPurchaseInfoExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andContractCodeEqualTo(header.getEamPurchaseCode());
            final List<EamPurchaseInfo> eamPurchaseInfoList = eamPurchaseInfoMapper.selectByExample(eamPurchaseInfoExample);
            if (ListUtils.isNotEmpty(eamPurchaseInfoList)) {
                final EamPurchaseInfo eamPurchaseInfo = eamPurchaseInfoList.get(0);
                // 合同开票关联eam采购合同
                ContractEampurchaseRelationExample relationExample = new ContractEampurchaseRelationExample();
                relationExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andEampurchaseIdEqualTo(eamPurchaseInfo.getId()).andContractIdIsNotNull();
                final List<ContractEampurchaseRelation> contractEampurchaseRelations = relationMapper.selectByExample(relationExample);
                if (!ListUtils.isNotEmpty(contractEampurchaseRelations)) {
                    ContractEampurchaseRelation eampurchaseRelation = new ContractEampurchaseRelation();
                    eampurchaseRelation.setContractId(header.getContractId());
                    eampurchaseRelation.setEampurchaseId(eamPurchaseInfo.getId());
                    eampurchaseRelation.setProjectCode(eamPurchaseInfo.getProjectCode());
                    eampurchaseRelation.setProviderCode(eamPurchaseInfo.getProviderCode());
                    eampurchaseRelation.setApplyCode(eamPurchaseInfo.getApplyCode());
                    eampurchaseRelation.setDeletedFlag(Boolean.FALSE);
                    relationMapper.insert(eampurchaseRelation);
                    Contract contract1 = new Contract();
                    contract1.setId(header.getContractId());
                    contract1.setEampurchaseId(eamPurchaseInfo.getId().toString());
                    contractMapper.updateByPrimaryKeySelective(contract1);
                }
            }


            //获取交易号
            InvoiceApplyHeader invoiceApplyHeader = new InvoiceApplyHeader();
            invoiceApplyHeader.setId(header.getId());
//            EsbResponse seqResponse = esbService.callCUXICPGETSEQPKGPortType();
            EsbResponse seqResponse = sdpService.callCuxicpGetSeqPkgPortType();
            if (ResponseCodeEnums.SUCESS.getCode().equals(seqResponse.getResponsecode())) {
                String code = (String) seqResponse.getData();
                invoiceApplyHeader.setTransactionNumber(code);
            } else {
                invoiceApplyHeader.setEamMsg(seqResponse.getResponsemessage());
            }
            invoiceApplyHeaderMapper.updateByPrimaryKeySelective(invoiceApplyHeader);
        }

        //检查是否存在配置，再检查客户
        Long ouId = header.getOuId();
        List<String> autoPushMif = new ArrayList<>(organizationCustomDictService.queryByName("是否自动推送开票平台", ouId, OrgCustomDictOrgFrom.OU));
        if (autoPushMif.isEmpty()) {
            throw new BizException(Code.ERROR, "组织参数【是否自动推送开票平台】未配置");
        }
        // 判断是否自动推送,只有不自动推送且是国外客户才【不主动推送】
        boolean shouldPush = !("0".equals(autoPushMif.get(0)) && invoiceReceivableService.queryCustomerIsAbroad(header.getCustomerCode()));
        if (shouldPush) {
            syncBlueInvoiceToMif(receivables, applyDetailSplits);
        }
        return 1L;
    }

    @Override
    public void syncBlueInvoiceToMif(List<InvoiceReceivable> receivables, List<InvoiceApplyDetailSplit> applyDetailSplits) {
        logger.info("开始写入美的开票平台");
        try {
            if (ListUtils.isEmpty(receivables) || receivables.get(0).getSendToKpy() != 1) {
                return;
            }
            Long ouId = receivables.get(0).getOuId();
            List<String> invoiceWay = new ArrayList<>(organizationCustomDictService.queryByName("开票方式", ouId, OrgCustomDictOrgFrom.OU));
            if (invoiceWay.isEmpty()) {
                throw new BizException(Code.ERROR, "组织参数【开票方式】未配置");
            }
            if (!"2".equals(invoiceWay.get(0))) {
                return;
            }
            List<Long> ouIds = receivables.stream().map(InvoiceReceivable::getOuId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, String> ouMap = basedataExtService.getOuInfoByOuIds(ouIds).stream().collect(
                    Collectors.toMap(
                            OperatingUnitDto::getOperatingUnitId,
                            OperatingUnitDto::getOperatingUnitName));
            String ouName = ouMap.get(ouId);
            if (StringUtils.isEmpty(ouName)) {
                throw new BizException(Code.ERROR, "ouName查询失败");
            }

            List<String> values = new ArrayList<>(organizationCustomDictService.queryByName("主体税号", ouId, OrgCustomDictOrgFrom.OU));
            if (ListUtils.isEmpty(values)) {
                throw new BizException(Code.ERROR, "组织参数【主体税号】未配置");
            }
            String taxpayerCode = values.get(0);

            List<String> immediateInvoiceValues = new ArrayList<>(organizationCustomDictService.queryByName("是否直接开票", ouId, OrgCustomDictOrgFrom.OU));
            if (ListUtils.isEmpty(immediateInvoiceValues) || StringUtils.isEmpty(immediateInvoiceValues.get(0))) {
                throw new BizException(Code.ERROR, "组织参数【是否直接开票】未配置");
            }
            try {
                Integer.valueOf(immediateInvoiceValues.get(0));
            } catch (NumberFormatException e) {
                throw new BizException(Code.ERROR, "组织参数【是否直接开票】配置有误");
            }
            Integer immediateInvoice = Integer.valueOf(immediateInvoiceValues.get(0));

            List<String> editableValues = new ArrayList<>(organizationCustomDictService.queryByName("是否允许修改", ouId, OrgCustomDictOrgFrom.OU));
            if (ListUtils.isEmpty(editableValues) || StringUtils.isEmpty(editableValues.get(0))) {
                throw new BizException(Code.ERROR, "组织参数【是否允许修改】未配置");
            }
            try {
                Integer.valueOf(editableValues.get(0));
            } catch (NumberFormatException e) {
                throw new BizException(Code.ERROR, "组织参数【是否允许修改】配置有误");
            }
            Integer editable = Integer.valueOf(editableValues.get(0));

            if (commonPropertiesConfig.getMifAppId() == null) {
                throw new BizException(Code.ERROR, "配置项mifAppId不存在");
            }
            if (commonPropertiesConfig.getMifAppKey() == null) {
                throw new BizException(Code.ERROR, "配置项mifAppKey不存在");
            }
            if (commonPropertiesConfig.getMifBlueInvoicePushPath() == null) {
                throw new BizException(Code.ERROR, "配置项mifBlueInvoicePushPath不存在");
            }
            Map<Long, CustomerDto> customerMap = contractHelper.getCustomerInfoBatch(
                    Collections.singletonList(receivables.get(0).getCustomerId()),
                    receivables.get(0).getContractId());

            Map<Long, List<InvoiceApplyDetailSplit>> detailSplitMap = applyDetailSplits.stream()
                    .collect(Collectors.groupingBy(InvoiceApplyDetailSplit::getInvoiceReceivableId));

            receivables.forEach(invoice -> {
                String requestJson = null;
                String res = null;
                try {
                    List<InvoiceApplyDetailSplit> detailSplits = detailSplitMap.get(invoice.getId());
                    requestJson = formBlueRequestJson(invoice, detailSplits, ouName, taxpayerCode, immediateInvoice, editable, customerMap);
                    invoice.setSendMifStatus(2);
                    logger.info("蓝票【{}】写入美的开票平台开始", invoice.getId());
                    res = HttpClientUtil.doPost(commonPropertiesConfig.getMifBlueInvoicePushPath(), requestJson);
                    logger.info("蓝票【{}】写入美的开票平台结束", invoice.getId());
                    Map<String, String> response = null;
                    if (res != null) {
                        response = JSON.parseObject(res, new TypeReference<Map<String, String>>() {
                        });
                        if (response != null && Arrays.asList("00000", "71003").contains(response.get("code"))) {
                            logger.info("发票【{}】写入美的开票平台成功", invoice.getInvoiceCode());
                            invoice.setSendMifStatus(1);
                            invoice.setSendToKpy(2);
                            invoice.setMifMsg("");
                            invoice.setStatus("已同步美的开票平台");
                        }
                    }
                    if (res == null || response == null) {
                        invoice.setMifMsg("响应内容：" + res);
                    } else if (invoice.getSendMifStatus() == 2) {
                        String errMsg = response.get("msg");
                        invoice.setMifMsg(StringUtils.isEmpty(errMsg) ? "响应内容：" + res : "响应消息：" + errMsg);
                    }
                } catch (Exception e) {
                    logger.error("发票【{}】写入美的开票平台失败", invoice.getInvoiceCode(), e);
                    invoice.setMifMsg("异常信息：" + e.getMessage());
                    invoice.setSendMifStatus(2);
                } finally {
                    saveMifPushLog(invoice.getId(), commonPropertiesConfig.getMifBlueInvoicePushPath(), requestJson, res);
                }
            });
        } catch (Exception e) {
            logger.error("发票【{}】写入美的开票平台失败",
                    receivables.stream().map(InvoiceReceivable::getInvoiceCode)
                            .collect(Collectors.joining(",")), e);
            receivables.forEach(invoice -> {
                invoice.setSendMifStatus(2);
                invoice.setMifMsg("异常信息：" + e.getMessage());
            });
        }
        receivables.forEach(invoice -> invoiceReceivableService.updateByPrimaryKeySelective(invoice));
    }

    @Override
    public Boolean initReceivableAges() {
        List<Long> applyHeaderIdList = invoiceApplyReceivableAgesExtMapper.findApplyHeaderIdList();
        if (CollectionUtils.isEmpty(applyHeaderIdList)) {
            return Boolean.FALSE;
        }
        List<InvoiceApplyReceivableAges> receivableAgesList = invoiceApplyReceivableAgesExtMapper.initReceivableAges(applyHeaderIdList);
        if (CollectionUtils.isEmpty(receivableAgesList)) {
            return Boolean.FALSE;
        }
        //先删除所有
        invoiceApplyReceivableAgesExtMapper.batchDeleteByApplyHeaderId(applyHeaderIdList);
        invoiceApplyReceivableAgesExtMapper.batchInsert(receivableAgesList);
        for (Long applyHeaderId : applyHeaderIdList) {
            //更新账龄明细的已冲销金额
            this.updateWriteOffAmountByApplyId(applyHeaderId);
        }
        return Boolean.TRUE;
    }

    private void saveMifPushLog(Long invoiceId, String url, String requestJson, String responseJson) {
        try {
            MifPushLog mifPushLog = new MifPushLog();
            mifPushLog.setInvoiceId(invoiceId);
            mifPushLog.setUrl(url);
            mifPushLog.setRequestJson(requestJson);
            mifPushLog.setResponseJson(responseJson);
            mifPushLogMapper.insert(mifPushLog);
        } catch (Exception e) {
            logger.error("蓝票【{}】写入美的开票平台日志保存失败，请求地址：{}，请求入参json：{}，请求响应json：{}", invoiceId, url, requestJson, responseJson, e);
        }
    }

    private String formBlueRequestJson(InvoiceReceivable invoice,
                                       List<InvoiceApplyDetailSplit> detailSplits,
                                       String ouName,
                                       String taxpayerCode,
                                       Integer immediateInvoice,
                                       Integer editable,
                                       Map<Long, CustomerDto> customerMap) {
        Map<String, Object> requestBody = new HashMap<>(3);
        // 系统id
        requestBody.put("appId", commonPropertiesConfig.getMifAppId());
        // 系统安全码
        requestBody.put("appKey", commonPropertiesConfig.getMifAppKey());
        Map<String, Object> data = new HashMap<>(20);
        requestBody.put("data", data);
        // 上游传入唯一流水号
        data.put("bizId", invoice.getId());
        data.put("ouCode", invoice.getOuId());
        data.put("ouName", ouName);
        // ERP客户编码
        data.put("erpCustCode", invoice.getCustomerCode());
        // 发票类型, 发票类型（1-电子普票 2-电子专票 3- 纸质普票 4-纸质专票）
        data.put("invoiceType", invoiceTypeMapping(invoice.getPlanInvoiceType()));
        // 主体税号-开票方
        data.put("taxpayerCode", taxpayerCode);

        CustomerDto customerDto = customerMap.get(invoice.getCustomerId());
        if (customerDto != null) {
            String registrationNumber = customerDto.getRegistrationNumber();
            // 因为境外客户没有统一信用代码。故主体是境外客户，默认一个统一信用代码传给智汇票
            if (Objects.equals(customerDto.getEnterpriseNatureName(), "境外企业")) {
                registrationNumber = "000000000000000001";
            }
            // 客户税号, 如果票种是专票，或者发票抬头为企业，此项为必填
            data.put("payTaxpayerCode", registrationNumber);
            CustomerBankAccountDto customerBankAccountDto = customerDto.getCustomerBankAccountDto();
            if (customerBankAccountDto != null) {
                // 客户名称, 如果票种是专票，此项为必填
                data.put("payTaxpayerName", customerBankAccountDto.getInvoiceName());
                // 客户地址, 如果票种是专票，此项为必填
                data.put("payUnitAddress", customerBankAccountDto.getBankAddress());
                // 客户电话号码, 如果票种是专票，此项为必填
                data.put("payFixedPhoneNumber", customerBankAccountDto.getCellphone());
                // 客户开户行, 如果票种是专票，此项为必填
                data.put("payBankName", customerBankAccountDto.getBankAccountName());
                // 客户银行账户, 如果票种是专票，此项为必填
                data.put("payBankAccount", customerBankAccountDto.getBankAccountNum());
            }
        }
        // 发票抬头（1个人/2企业） 开专票时，必须为企业
        data.put("invoiceHead", 2);
        // 开票规则
        data.put("invoiceRule", 1);
        // 是否直接开票
        data.put("immediateInvoice", immediateInvoice);
        // 允许修改
        data.put("editable", editable);
        // 主业务单号
        data.put("businessNo", invoice.getInvoiceCode());
        // 票面备注
        data.put("remarks", invoice.getRemark());

        List<Map<String, Object>> detailList = new ArrayList<>();
        data.put("detailList", detailList);

        // 发票汇率
        final BigDecimal conversionRate = Optional.ofNullable(invoice.getConversionRate()).orElse(BigDecimal.ONE);
        BigDecimal invoiceAmt = BigDecimal.ZERO;
        for (InvoiceApplyDetailSplit d : detailSplits) {
            Map<String, Object> detail = new HashMap<>(10);
            detailList.add(detail);
            // 开票项目/商品名称
            detail.put("goodsName", d.getProduct());
            // 单位
            detail.put("goodsUnit", d.getUnit());
            // 税码
            detail.put("taxCode", d.getProductTaxCode());
            // 税率
            detail.put("taxRate", handleTaxRate(d.getTaxRace()));
            // 开票项唯一id (接入系统唯一健，不可重复，后续发起红冲会用到)
            detail.put("bizDetailId", d.getId());
            // 业务单号（比如 业务系统的借据号，可以重复）
            detail.put("businessNo", invoice.getInvoiceCode());
            // 规格编码
            detail.put("standards", d.getModel());
            // 数量，折扣开票项 时为负数，否则为正数
            detail.put("goodCount", d.getQuantity());
            // 含税单价，必须为正数
            detail.put("taxUnitPrice", BigDecimalUtils.multiply(d.getPrice(), conversionRate).setScale(8, BigDecimal.ROUND_HALF_UP));
            // 含税金额 含税金额必须等于 含税单价*数量
            // 折扣开票项 时为负数，否则为正数
            final BigDecimal amtContainTax = BigDecimalUtils.multiply(d.getTaxIncludedPrice(), conversionRate).setScale(2, RoundingMode.HALF_UP);
            detail.put("amtContainTax", amtContainTax);
            invoiceAmt = invoiceAmt.add(amtContainTax);
            // 针对明细的备注
//            detail.put("detailRemark", d.getRemark());
            // 特定业务要素类型JSON字符串
            if (StringUtils.isNotBlank(d.getSpecBusinessInfo())) {
                data.put("specialElementJsonstr", d.getSpecBusinessInfo());
            }
        }
        // 发票金额
        data.put("invoiceAmt", invoiceAmt);
        return JSON.toJSONString(requestBody);
    }

    /**
     * PAM-美的开票平台 开票类型映射
     *
     * @param invoiceType
     * @return
     */
    private Integer invoiceTypeMapping(Integer invoiceType) {
        Integer s = null;
        switch (invoiceType) {
            case 0:
                s = 4;
                break;  //纸质专票
            case 1:
                s = 3;
                break;  //纸质普票
            case 2:
                s = 2;
                break;  //电子专票
            case 3:
                s = 1;
                break;  //电子普票
        }
        return s;
    }

    /**
     * 处理税率
     *
     * @param taxRace 整数税率
     * @return {@link BigDecimal} 小数税率
     */
    private BigDecimal handleTaxRate(BigDecimal taxRace) {
        BigDecimal taxRate;
        if (taxRace == null || taxRace.compareTo(BigDecimal.ZERO) == 0) {
            taxRate = new BigDecimal("0.00");
        } else {
            taxRate = taxRace.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        }
        return taxRate;
    }

    @Override
    @Transactional
    public JSONArray applyByPlanDetails(String planDetailIds) {
        JSONArray result = new JSONArray();
        Map<String, InvoiceApplyDto> applyDtoMap = new HashMap<>();
        for (String planDetailId : planDetailIds.split(",")) {
            try {
                //开票计划明细
                InvoicePlanDetail detail = planDetailMapper.selectByPrimaryKey(Long.parseLong(planDetailId));
                if (detail != null) {
                    //开票计划头表
                    InvoicePlan plan = planMapper.selectByPrimaryKey(detail.getPlanId());
                    //合同信息
                    Contract contract = contractMapper.selectByPrimaryKey(detail.getContractId());
                    if (plan != null && contract != null) {
                        if (contract.getCustomerId() == null) throw new MipException("开票计划的客户ID为空");
                        if (contract.getOuId() == null) throw new MipException("开票计划的OUID为空");
                        if (StringUtils.isBlank(contract.getCurrency())) throw new MipException("开票计划的币种为空");
                        //根据客户，ou，币种拆分
                        if (!applyDtoMap.containsKey(contract.getCustomerId() + "_" + contract.getOuId() + "_" + contract.getCurrency())) {
                            InvoiceApplyDto apply = new InvoiceApplyDto();
                            //apply.setName();
                            apply.setApplyType("plan");
                            apply.setCustomerId(contract.getCustomerId());
                            apply.setOuId(contract.getOuId());
                            apply.setCurrencyCode(contract.getCurrency());
                            //apply.setExchangeRateType();
                            //apply.setExchangeRateDate();
                            apply.setTotalTaxIncludedPrice(detail.getAmount());
                            apply.setTotalExclusiveOfTax(BigDecimal.ZERO);//后面计算出来
                            //apply.setInvoiceType();
                            apply.setInvoiceDate(detail.getDate());
                            apply.setGlDate(detail.getDate());
                            apply.setTotalPenalty(BigDecimal.ZERO);
							/*OperatingUnit ou = CacheDataUtils.findOuById(contract.getOuId());
							apply.setLimitPrice(ou.getLimitPrice());*/
                            BigDecimal limitPrice = this.getLimitPrice(contract, plan.getType());
                            Assert.notNull(limitPrice, "开票限额为空");
                            apply.setLimitPrice(limitPrice);
                            apply.setCustomerName(contract.getCustomerName());
                            apply.setCustomerCode(contract.getCustomerCode());
                            apply.setInvoiceCustomer(contract.getCustomerName());
                            apply.setPlanInvoiceType(plan.getType().intValue());
                            apply.setStatus(InvoiceApplyStatusEnum.HEADER_DRAFT.code()); //原来的起草改成草稿
                            apply.setDeletedFlag(false);
                            apply.setDetails(new ArrayList<>());
                            applyDtoMap.put(contract.getCustomerId() + "_" + contract.getOuId() + "_" + contract.getCurrency(), apply);
                        }
                        InvoiceApplyDto apply = applyDtoMap.get(contract.getCustomerId() + "_" + contract.getOuId() + "_" + contract.getCurrency());
                        InvoiceApplyDetailsDto applyDetails = new InvoiceApplyDetailsDto();
                        applyDetails.setContractId(contract.getId());
                        applyDetails.setContractCode(contract.getCode());
                        applyDetails.setPlanId(detail.getId());
                        applyDetails.setTaxIncludedPrice(detail.getAmount());
                        applyDetails.setTaxRace(plan.getRate());
                        String url = ModelsEnum.CRM.getBaseUrl() + "/product/getProductWithSonProductsById?id=" + plan.getProductId();
                        String res = restTemplate.getForEntity(url, String.class).getBody();
                        if (StringUtils.isNotBlank(res)) {
                            JSONObject json = JSON.parseObject(res);
                            if (json.containsKey("data")) {
                                JSONObject product = json.getJSONObject("data");
                                applyDetails.setProduct(product.getString("name"));
                                applyDetails.setUnit(product.getString("unit"));
                            }
                        }
                        //applyDetails.setTaxCode();
                        applyDetails.setQuantity(BigDecimal.valueOf(1));
                        applyDetails.setPenalty(BigDecimal.ZERO);
                        applyDetails.setDeletedFlag(false);
                        apply.getDetails().add(applyDetails);
                    }//end if not null
                    //修改开票计划状态
                    detail.setStatus(10);
                    this.planDetailMapper.updateByPrimaryKeySelective(detail);
                }//end if not null
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }//end for
        //保存数据
        applyDtoMap.forEach((k, v) -> {
            result.add(this.save(v));
        });
        return result;
    }


    /**
     * 从参数配置表里面取开票限额
     *
     * @return
     */
    private BigDecimal getLimitPrice(Contract contract, Integer invoiceType) {
        String dictName = "开票限额";
        if (invoiceType != null) {
            // 0-纸质专票 1-纸质普票 2-电子专票 3-电子普票
            dictName = Objects.equals(0, invoiceType.intValue()) ? "开票限额（专票）" : "开票限额（普票）";
        }
        Set<String> valueSet = organizationCustomDictService.queryByName(dictName, contract.getOuId(),
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
        if (CollectionUtils.isEmpty(valueSet)) {
            dictName = "开票限额";
            valueSet = organizationCustomDictService.queryByName(dictName, contract.getOuId(),
                    OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
            return CollectionUtils.isEmpty(valueSet) ? null : new BigDecimal(valueSet.iterator().next());
        } else {
            return new BigDecimal(valueSet.iterator().next());
        }
    }

    @Override
    public Boolean checkInvoiceNum(String applyCode) {
        if (StringUtils.isNotBlank(applyCode)) {
            InvoiceReceivableExample example = new InvoiceReceivableExample();
            example.createCriteria().andExternalInvoiceNumEqualTo(applyCode);
            List<InvoiceReceivable> list = this.invoiceReceivableService.selectByExample(example);

            return list.size() > 0;
        } else {
            return false;
        }
    }

    @Override
    public Boolean isCreater(Long invoiceApplyId) {
        InvoiceApplyHeader invoiceApplyHeader = this.invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyId);
        if (invoiceApplyHeader == null) {
            return false;
        }
        Long id = invoiceApplyHeader.getCreateBy();
        if (id == null) {
            return false;
        }
        long currentUserId = SystemContext.getUserId();//当前登录用户ID
        if (id == currentUserId) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Boolean isManager(Long invoiceApplyId) {
        InvoiceApplyHeader invoiceApplyHeader = this.invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyId);
        if (invoiceApplyHeader == null) {
            return false;
        }
        List<InvoiceApplyDetails> list = this.getInvoiceApplyDetails(invoiceApplyId);
        if (list == null || list.size() == 0) {
            return false;
        }
        for (InvoiceApplyDetails invoiceApplyDetails : list) {
            if (invoiceApplyDetails.getContractId() != null) {
                if (contractService.isManager(invoiceApplyDetails.getContractId())) {
                    return true;
                }
            }
        }
        return false;
    }


    private List<InvoiceApplyDetails> getInvoiceApplyDetails(Long invoiceApplyId) {
        InvoiceApplyDetailsExample example = new InvoiceApplyDetailsExample();
        InvoiceApplyDetailsExample.Criteria criteria = example.createCriteria();
        criteria.andApplyHeaderIdEqualTo(invoiceApplyId);
        criteria.andDeletedFlagEqualTo(false);
        List<InvoiceApplyDetails> list = invoiceApplyDetailsMapper.selectByExample(example);
        return list;
    }

    @Override
    public Boolean isSaleManager(Long invoiceApplyId) {
        InvoiceApplyHeader invoiceApplyHeader = this.invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyId);
        if (invoiceApplyHeader == null) {
            return false;
        }
        List<InvoiceApplyDetails> list = this.getInvoiceApplyDetails(invoiceApplyId);
        if (list == null || list.size() == 0) {
            return false;
        }
        for (InvoiceApplyDetails invoiceApplyDetails : list) {
            if (invoiceApplyDetails.getContractId() != null) {
                if (contractService.isSaleManager(invoiceApplyDetails.getContractId())) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public Boolean isDataAuthor(Long invoiceApplyId) {
        InvoiceApplyHeader invoiceApplyHeader = this.invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyId);
        if (invoiceApplyHeader == null) {
            return false;
        }
        List<InvoiceApplyDetails> list = this.getInvoiceApplyDetails(invoiceApplyId);
        if (list == null || list.size() == 0) {
            return false;
        }
        for (InvoiceApplyDetails invoiceApplyDetails : list) {
            if (invoiceApplyDetails.getContractId() != null) {
                if (contractService.isDataAuthor(invoiceApplyDetails.getContractId())) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public Boolean isFinance(Long invoiceApplyId) {
        InvoiceApplyHeader invoiceApplyHeader = this.invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyId);
        if (invoiceApplyHeader == null) {
            return false;
        }
        List<InvoiceApplyDetails> list = this.getInvoiceApplyDetails(invoiceApplyId);
        if (list == null || list.size() == 0) {
            return false;
        }
        for (InvoiceApplyDetails invoiceApplyDetails : list) {
            if (invoiceApplyDetails.getContractId() != null) {
                if (contractService.isFinance(invoiceApplyDetails.getContractId())) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    @Transactional
    public Boolean invalid(InvoiceApplyHeaderDto invoiceApplyHeaderDto) {

        //流程作废
        this.disable(invoiceApplyHeaderDto);

        //更新表头状态
        InvoiceApplyHeader invoiceApplyHeader = new InvoiceApplyHeader();
        invoiceApplyHeader.setId(invoiceApplyHeaderDto.getId());
        invoiceApplyHeader.setStatus(InvoiceApplyStatusEnum.HEADER_DELETE.getCode());
        invoiceApplyHeader.setInvalidAt(new Date());
        invoiceApplyHeader.setInvalidBy(SystemContext.getUserId());
        invoiceApplyHeader.setInvalidReason(invoiceApplyHeaderDto.getInvalidReason());
        invoiceApplyHeaderMapper.updateByPrimaryKeySelective(invoiceApplyHeader);

        //更新明细
        invoiceApplyDetailsExtMapper.updateStatus(invoiceApplyHeaderDto.getId());

        //更新开票计划的金额
        InvoiceApplyDetailsExample example = new InvoiceApplyDetailsExample();
        example.createCriteria().andApplyHeaderIdEqualTo(invoiceApplyHeaderDto.getId());
        List<InvoiceApplyDetails> applyDetailsList = invoiceApplyDetailsMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(applyDetailsList)) {
            for (InvoiceApplyDetails invoiceApplyDetails : applyDetailsList) {
                invoiceApplyDetailsExtMapper.updateInvoicePlanPrice(invoiceApplyDetails.getCode());
            }
        }
        return true;
    }

    /**
     * 应收发票编号规则： 代码公司段(美云:M，机器人:R)+IV+YY+MM+DD+3位流水码
     *
     * @return
     */
    private String generateInvoiceCode(Long ouId) {
        logger.info("应收发票编号规则参数ouId:" + ouId);
        //取ou的关联的第一个unit(二级单位)
        UnitOuRel unitOuRel = CacheDataUtils.findUnitOuRelByOuId(ouId);
        logger.info("unitOuRel:" + unitOuRel);
        Assert.notNull(unitOuRel, "缓存取unitOuRel失败");
        //根据二级单位取一级单位
        Long bizUnitId = CacheDataUtils.getTopUnitIdByUnitId(unitOuRel.getUnitId());
        Unit bizUnit = CacheDataUtils.findUnitById(bizUnitId);

        StringBuffer invoiceCode = new StringBuffer();
        String seqPerfix = bizUnit.getSeqPerfix();
        invoiceCode.append(seqPerfix);
        invoiceCode.append(CodePrefix.INVOICECODE.code());
        invoiceCode.append(CacheDataUtils.generateSequence(Constants.INVOICE_CODE_LENGTH, seqPerfix + CodePrefix.INVOICECODE.code(),
                DateUtil.DATE_YYMMDD_PATTERN));
        return invoiceCode.toString();
    }


    @Override
    public Boolean disable(InvoiceApplyHeaderDto invoiceApplyHeaderDto) {
        InvoiceApplyHeader invoiceApplyHeader = invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyHeaderDto.getId());
        Integer status = invoiceApplyHeader.getStatus();
        FormInstanceEvent formInstanceEvent = new FormInstanceEvent();
        formInstanceEvent.setFormInstanceId(invoiceApplyHeader.getId());
        formInstanceEvent.setFormUrl(getFormUrl(status));
        formInstanceEvent.setStatus(0);
        formInstanceEvent.setEventType(WorkflowOperationType.DRAFT_ABANDON.getCode());
        formInstanceEvent.setCompanyId(SystemContext.getUnitId());
        formInstanceEvent.setCreateAt(new Date());
        stringRedisTemplate.opsForList().leftPushAll(Constants.REDIS_WF_EVENT_KEY, JSONObject.toJSONString(formInstanceEvent));
        return true;
    }

    @Override
    public Integer syncToEam(String ids) {
        List<Long> idList = com.midea.pam.common.util.StringUtils.splitToLong(ids, ",");
        InvoiceApplyHeaderExample example = new InvoiceApplyHeaderExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andIdIn(idList);
        List<InvoiceApplyHeader> invoiceApplyHeaders = invoiceApplyHeaderMapper.selectByExample(example);
        for (InvoiceApplyHeader header : invoiceApplyHeaders) {
            InvoiceApplyHeaderExample headerExample = new InvoiceApplyHeaderExample();
            headerExample.createCriteria().andDeletedFlagEqualTo(false).andOldHeaderIdEqualTo(header.getId());
            List<InvoiceApplyHeader> applyHeaders = invoiceApplyHeaderMapper.selectByExample(headerExample);
            //判断是否被冲销过
            if (ListUtils.isNotEmpty(applyHeaders)) {
                header.setEamStatus(InvoiceApplyStatusEnum.HEADER_WRITEOFF.getCode());
            } else {
                header.setEamStatus(header.getStatus());
            }

            updateEamInvoiceStatus(header);
        }
        return idList.size();
    }

    @Override
    public void batchUpdateWriteOffAmountByApplyId(String applyHeaderId) {
        if (!"null".equalsIgnoreCase(applyHeaderId)
                && StringUtils.isNotBlank(applyHeaderId)) {
            updateWriteOffAmountByApplyId(Long.valueOf(applyHeaderId));
        } else {
            InvoiceApplyHeaderExample example = new InvoiceApplyHeaderExample();
            example.createCriteria().andDeletedFlagEqualTo(false).andOldHeaderIdIsNull()
                    .andStatusEqualTo(InvoiceApplyStatusEnum.HEADER_PASS.getCode());
            List<InvoiceApplyHeader> invoiceApplyHeaders = invoiceApplyHeaderMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(invoiceApplyHeaders)) {
                for (InvoiceApplyHeader invoiceApplyHeader : invoiceApplyHeaders) {
                    updateWriteOffAmountByApplyId(invoiceApplyHeader.getId());
                }
            }
        }
    }

    @Override
    @Transactional
    public void updateWriteOffAmountByApplyId(Long applyHeaderId) {
        // 按合同维度查询开票申请已冲销金额-此时无需到行维度（红冲头行仅有一条）
        List<InvoiceApplyDetails> detailsList = invoiceApplyDetailsExtMapper.countWriteOffAmount(applyHeaderId);
        if (ListUtils.isEmpty(detailsList)) {
            return;
        }
        for (InvoiceApplyDetails invoiceApplyDetails : detailsList) {
            BigDecimal writeOffAmount = invoiceApplyDetails.getTaxIncludedPrice();//开票申请已冲销金额
            //账龄明细
            InvoiceApplyReceivableAgesExample receivableAgesExample = new InvoiceApplyReceivableAgesExample();
            receivableAgesExample.createCriteria().andApplyHeaderIdEqualTo(applyHeaderId)
                    .andContractIdEqualTo(invoiceApplyDetails.getContractId()).andDeletedFlagEqualTo(Boolean.FALSE);
            receivableAgesExample.setOrderByClause(" expire_date asc ");
            List<InvoiceApplyReceivableAges> receivableAgesList = invoiceApplyReceivableAgesMapper.selectByExample(receivableAgesExample);
            if (ListUtils.isNotEmpty(receivableAgesList)) {
                for (InvoiceApplyReceivableAges receivableAge : receivableAgesList) {
                    if (BigDecimalUtils.isEquals(writeOffAmount, BigDecimal.ZERO)) {
                        break;
                    }
                    BigDecimal amount = receivableAge.getAmount();
                    if (writeOffAmount.compareTo(amount) >= 0) {
                        receivableAge.setWriteOffAmount(amount);
                        writeOffAmount = writeOffAmount.subtract(amount);
                    } else {
                        receivableAge.setWriteOffAmount(writeOffAmount);
                        writeOffAmount = BigDecimal.ZERO;
                    }
                    invoiceApplyReceivableAgesMapper.updateByPrimaryKey(receivableAge);
                }
            }
        }
    }

    public String getFormUrl(Integer status) {
        String url = "";
        switch (status) {
            case -2:
                url = "invoiceApply";
                break;
            case 0:
                url = "invoiceApply";
                break;
            case 2:
                url = "invoiceApply";
                break;
            default:
                break;
        }
        return url;
    }

    private List<RdmSettlementSheetDetail> getRdmSettlementSheetDetails(Long settlementSheetId) {
        RdmSettlementSheetDetailExample rdmSettlementSheetDetailExample = new RdmSettlementSheetDetailExample();
        rdmSettlementSheetDetailExample.createCriteria().andDeletedFlagEqualTo(false).andSettlementIdEqualTo(settlementSheetId);
        List<RdmSettlementSheetDetail> rdmSettlementSheetDetails = rdmSettlementSheetDetailMapper.selectByExample(rdmSettlementSheetDetailExample);
        if (ListUtils.isNotEmpty(rdmSettlementSheetDetails)) {
            return rdmSettlementSheetDetails;
        }
        return null;
    }

    @Override
    public List<InvoiceApplyDetailExcelVO> checkInvoiceApplyDetail(InvoiceApplyPlanDetailDTO detailDTO) {

        checkRequiredField(detailDTO);

        List<InvoiceApplyDetailExcelVO> excelVOList = detailDTO.getExcelVOList();

        excelVOList.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        ProductTaxSettingQueryDto queryDto = new ProductTaxSettingQueryDto();
        queryDto.setOuId(String.valueOf(detailDTO.getOuId()));
        queryDto.setStatus(Boolean.TRUE);
        queryDto.setPageNum(1);
        queryDto.setPageSize(999);
        PageInfo<ProductTaxSettingDto> productTaxSettingList = productTaxSettingService.getProductTaxSettingList(queryDto);
        Map<String, ProductTaxSettingDto> productTaxSettingMap = productTaxSettingList.getList()
                .stream().collect(Collectors.toMap(ProductTaxSettingDto::getTaxName, e -> e));

        List<String> unitNames = ltcDictFeignClient.listByType("measurement_unit")
                .stream().map(DictDto::getName).collect(Collectors.toList());

        final OrgCustomDictOrgFrom orgCustomDictOrgFrom = OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom("ou");
        if (orgCustomDictOrgFrom == null) {
            throw new MipException("类型参数有误");
        }
        excelVOList.forEach(vo -> {
            List<String> errMsgList = new ArrayList<>();

            // 检查错误信息是否存在，若存在则添加到 errMsgList
            if (StringUtils.isNotEmpty(vo.getErrMsg())) {
                errMsgList.add(vo.getErrMsg());
            }

            if (Objects.nonNull(vo.getProductTaxName())) {
                ProductTaxSettingDto productTaxSettingDto = productTaxSettingMap.get(vo.getProductTaxName());
                if (productTaxSettingDto != null) {
                    vo.setProductTaxCode(productTaxSettingDto.getTaxCode());
                } else {
                    errMsgList.add("【产品税收名称】不存在");
                }
            }

            if (StringUtils.isNotBlank(vo.getModel()) && checkInputLength(vo.getModel(), 64)) {
                errMsgList.add("【规格型号】最多64个字符,中文算三个");
            }

            if (Objects.nonNull(vo.getUnit()) && !unitNames.contains(vo.getUnit())) {
                errMsgList.add("【单位】不存在");
            }

            if (Objects.nonNull(vo.getTaxIncludedPrice()) && vo.getTaxIncludedPrice().scale() > 2) {
                errMsgList.add("【含税金额】小数位最多两位");
            }

            if (!errMsgList.isEmpty()) {
                vo.setErrMsg(String.join(",", errMsgList));
            }
        });

        BigDecimal taxIncludedPriceSum = excelVOList.stream().map(InvoiceApplyDetailExcelVO::getTaxIncludedPrice)
                .reduce(BigDecimal.ZERO, BigDecimalUtils::add);

        if (taxIncludedPriceSum.compareTo(detailDTO.getTotalTaxIncludedPrice()) > 0) {
            InvoiceApplyDetailExcelVO errExcelVO = new InvoiceApplyDetailExcelVO();
            errExcelVO.setErrMsg("总【含税金额】大于【申请开票金额（含税）】");
            excelVOList.add(errExcelVO);
        }

        if (excelVOList.stream().allMatch(e -> StringUtils.isEmpty(e.getErrMsg()))) {
            excelVOList.forEach(vo -> {
                vo.setTaxRace(detailDTO.getTaxRace());
                vo.setPrice(vo.getTaxIncludedPrice().divide(BigDecimal.valueOf(vo.getQuantity()), 2, RoundingMode.HALF_UP));
                //不含税价格=含税价格÷（1+税率）
                BigDecimal taxRate = detailDTO.getTaxRace().movePointLeft(2);
                vo.setExclusiveOfTax(vo.getTaxIncludedPrice().divide(BigDecimal.ONE.add(taxRate), 2, RoundingMode.HALF_UP));
                vo.setTaxAmount(vo.getTaxIncludedPrice().subtract(vo.getExclusiveOfTax()));
                vo.setInvoicePlanCode(detailDTO.getInvoicePlanCode());
                vo.setInvoiceType(detailDTO.getInvoiceType());
                vo.setRequirement(detailDTO.getRequirement());
                vo.setContractName(detailDTO.getContractName());
                vo.setContractCode(detailDTO.getContractCode());
                vo.setSalesManagerName(detailDTO.getSalesManagerName());
                vo.setMilestoneName(detailDTO.getMilestoneName());
                vo.setMilestoneStatus(detailDTO.getMilestoneStatus());
                vo.setProjectName(detailDTO.getProjectName());
                vo.setProjectCode(detailDTO.getProjectCode());
                vo.setManagerName(detailDTO.getManagerName());
            });
        }

        return excelVOList;
    }

    private void checkRequiredField(InvoiceApplyPlanDetailDTO detailDTO) {
        if (detailDTO.getOuId() == null) {
            throw new BizException(Code.ERROR, "业务实体id不为能空!");
        }
        if (detailDTO.getTaxRace() == null) {
            throw new BizException(Code.ERROR, "税率不为能空!");
        }
        if (detailDTO.getTotalTaxIncludedPrice() == null) {
            throw new BizException(Code.ERROR, "开票计划金额不为能空!");
        }
    }

    private boolean checkInputLength(String input, int maxLength) {
        int currentLength = calculateLength(input);
        return currentLength > maxLength;
    }

    private int calculateLength(String input) {
        int length = 0;
        for (char ch : input.toCharArray()) {
            if (Character.UnicodeScript.of(ch) == Character.UnicodeScript.HAN) {
                length += 3;
            } else {
                length += 1;
            }
        }
        return length;
    }
}
