package com.midea.pam.ctc.service.listener;

import com.alibaba.fastjson.JSON;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.ctc.dto.ProjectProfitDto;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectMilepostExample;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetChangeHistory;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.ProjectChangeStatus;
import com.midea.pam.common.enums.ProjectResourceStatus;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.RequirementStatusEnum;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.ProjectBudgetChangeSummaryHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectHistoryHeaderMapper;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeSummaryHistoryService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.WbsPushToErpService;
import com.midea.pam.ctc.service.event.ProjectWbsBudgetChangeWorkflowCallbackApprovalEvent;
import com.midea.pam.ctc.service.event.ProjectWbsBudgetFeeEvent;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetChangeHistoryService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetChangeService;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class ProjectWbsBudgetChangeWorkflowCallbackApprovalListener implements ApplicationListener<ProjectWbsBudgetChangeWorkflowCallbackApprovalEvent> {

    private final static Logger logger = LoggerFactory.getLogger(ProjectWbsBudgetChangeWorkflowCallbackApprovalListener.class);

    private final static Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private ProjectBudgetChangeSummaryHistoryService summaryHistoryService;
    @Resource
    private ProjectProfitService projectProfitService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private ProjectHistoryHeaderMapper projectHistoryHeaderMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private ProjectWbsBudgetChangeService projectWbsBudgetChangeService;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private WbsPushToErpService wbsPushToErpService;
    @Resource
    private ProjectWbsBudgetChangeHistoryService projectWbsBudgetChangeHistoryService;
    @Resource
    private ProjectBudgetChangeSummaryHistoryMapper projectBudgetChangeSummaryHistoryMapper;
    @Resource
    private WorkflowCallbackService workflowCallbackService;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    @Async
    public void onApplicationEvent(ProjectWbsBudgetChangeWorkflowCallbackApprovalEvent event) {
        logger.info("项目WBS预算变更审批通过回调的异步处理参数为:{}", JsonUtils.toString(event));

        pass(event.getFormInstanceId(), event.getFdInstanceId(), event.getFormUrl(), event.getEventName(), event.getHandlerId(),
                event.getCompanyId(), event.getCreateUserId(), event.getHandlerUserId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void pass(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                     Long createUserId, Long handlerUserId) {
        logger.info("项目WBS预算变更审批通过回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}, handlerUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, handlerUserId);
        String lockName = String.format("ProjectWbsBudgetChangeWorkflowCallbackApproval_pass_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    asynfromEvent(formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, handlerUserId);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            true);
                } catch (ApplicationBizException e) {
                    logger.info("项目WBS预算变更审批通过回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目WBS预算变更审批通过回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目WBS预算变更审批通过回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    public void asynfromEvent(Long headerId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId, Long createUserId, Long handlerUserId) {
        if (null == SystemContext.get()) {
            UserInfoDto userInfoDto = new UserInfoDto();
            userInfoDto.setUserId(handlerUserId);
            userInfoDto.setUsername(handlerId);
            userInfoDto.setUnitId(companyId);
            SystemContext.set(userInfoDto);
            logger.info("项目WBS预算变更用户信息：{}", JSON.toJSONString(userInfoDto));
        }

        final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(headerId);
        Guard.notNull(projectHistoryHeader, "项目WBS预算变更审批通过回调 formInstanceId对应单据不存在，不处理");

        projectHistoryHeader.setStatus(ProjectChangeStatus.APPROVALED.getCode());
        projectHistoryHeaderMapper.updateByPrimaryKeySelective(projectHistoryHeader);

        // 计算变更前后的预算金额
        List<ProjectWbsBudgetChangeHistory> beforeBudgetHistoryList = projectWbsBudgetChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.HISTORY, null);
        List<ProjectWbsBudgetChangeHistory> afterBudgetHistoryList = projectWbsBudgetChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);
        BigDecimal beforeAmount = beforeBudgetHistoryList.stream().map(ProjectWbsBudgetChangeHistory::getPrice).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        BigDecimal afterAmount = afterBudgetHistoryList.stream().map(ProjectWbsBudgetChangeHistory::getPrice).reduce(BigDecimal.ZERO, BigDecimalUtils::add);

        // project_budget_change_summary_history表，前端计算偶发出错，改为后端计算，供报表使用
        ProjectBudgetChangeSummaryHistory summaryHistory = summaryHistoryService.getByHeaderId(headerId);
        summaryHistory.setBeforeAmount(beforeAmount);
        summaryHistory.setAfterAmount(afterAmount);
        summaryHistory.setChangeAmount(afterAmount.subtract(beforeAmount));
        projectBudgetChangeSummaryHistoryMapper.updateByPrimaryKeySelective(summaryHistory);

        // 更新项目状态为进行中及项目WBS预算
        Project project = new Project();
        project.setId(projectHistoryHeader.getProjectId());
        project.setStatus(ProjectStatus.APPROVALED.getCode());
        project.setBudgetCost(afterAmount);
        project.setResourceStatus(ProjectResourceStatus.NOCHANGE.getCode());
        projectService.updateByPrimaryKeySelective(project);
        project = projectService.selectByPrimaryKey(project.getId());
        logger.info("项目WBS预算变更更新项目信息：{}", JSON.toJSONString(project));

        // 更新收入成本计划
        updateProfitDetail(projectHistoryHeader.getProjectId(), afterAmount);

        // 同步变更信息至正式表
        projectWbsBudgetChangeService.changeSuccess(headerId);

        // 重新生成详细设计
        milepostDesignPlanService.milepostDesignPlanJoinWbs(projectHistoryHeader.getProjectId());

        //项目号同步ems插入resend表（同步WBS号）
        wbsPushToErpService.wbsPushToErp("project", project.getId());

        //更新WBS变更单状态为：生效
        projectWbsReceiptsService.updateRequirementStatus(projectHistoryHeader.getProjectWbsReceiptsId(), RequirementStatusEnum.PROCESS.getCode());

        logger.info("项目预算变更同步预算信息至EMS：{}, {}", BusinessTypeEnums.PROJECT_BUDGET_MODIFY_EMS.getCode(), String.valueOf(headerId));
        // 同步预算信息至EMS
        applicationEventPublisher.publishEvent(new ProjectWbsBudgetFeeEvent(this, projectHistoryHeader.getProjectId(), projectHistoryHeader.getCreateBy(), headerId));
        logger.info("项目WBS预算变更审批通过审批回调，状态更新为审批通过");

        //【项目类型】立项后详细设计模组默认状态，设置为：已确认
        projectBusinessService.updateModuleStatusByProjectType(project);

        //更新wbs预算快照事件
        projectService.publishProjectWbsBudgetVersion(headerId);
    }

    /**
     * 更新收入成本计划
     *
     * @param projectId
     * @param afterAmount
     */
    private void updateProfitDetail(Long projectId, BigDecimal afterAmount) {
        // 更新收入成本计划
        final ProjectProfitDto profitDetail = projectProfitService.findProfitDetail(projectId);
        if (profitDetail != null && profitDetail.getId() != null) {
            final BigDecimal mainBudget = profitDetail.getMainBudget();

            final Long profitId = profitDetail.getId();
            ProjectProfit projectProfit = new ProjectProfit();
            projectProfit.setId(profitId);
            projectProfit.setMainBudget(afterAmount);
            projectProfitService.updateByPrimaryKeySelective(projectProfit);
            logger.info("项目WBS预算变更更新收入成本计划信息：{}", JSON.toJSONString(projectProfit));

            if (mainBudget.compareTo(afterAmount) != 0) {
                logger.info("项目WBS预算变更更新收入成本计划明细");
                final ProjectMilepostExample milepostExample = new ProjectMilepostExample();
                milepostExample.createCriteria()
                        .andProjectIdEqualTo(projectId)
                        .andDeletedFlagEqualTo(false)
                        .andHelpFlagEqualTo(Boolean.FALSE);
                final List<ProjectMilepost> mileposts = projectMilepostService.selectByExample(milepostExample);
                if (ListUtils.isNotEmpty(mileposts)) {
                    BigDecimal hundred = new BigDecimal("100");
                    mileposts.forEach(milepost -> {
                        final BigDecimal costRatio = milepost.getCostRatio() == null ? BigDecimal.ZERO :
                                milepost.getCostRatio();
                        final BigDecimal cost = milepost.getCost() == null ? BigDecimal.ZERO : milepost.getCost();

                        final BigDecimal newCost = afterAmount.multiply(costRatio).divide(hundred);
                        milepost.setCost(newCost);
                        projectMilepostService.updateByPrimaryKeySelective(milepost);
                        logger.info("项目WBS预算变更更新收入成本计划明细，{}-{}，{} 变更为 {}",
                                milepost.getId(),
                                milepost.getName(),
                                cost,
                                newCost);
                    });
                }
            }
        }
    }
}
