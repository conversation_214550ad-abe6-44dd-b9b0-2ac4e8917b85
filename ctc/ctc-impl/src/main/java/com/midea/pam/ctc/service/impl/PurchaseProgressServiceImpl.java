package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.mcomponent.core.util.date.DateUtil;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.entity.PurchaseProgress;
import com.midea.pam.common.ctc.entity.PurchaseProgressExample;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.mapper.PurchaseProgressMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.PurchaseProgressService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class PurchaseProgressServiceImpl implements PurchaseProgressService {
    @Resource
    private PurchaseProgressMapper purchaseProgressMapper;
    @Resource
    private RestTemplate restTemplate;

    @Resource
    private EsbService esbService;

    @Resource
    private SdpCarrierServicel sdpCarrierServicel;

    @Override
    public int insert(PurchaseProgress record) {
        return purchaseProgressMapper.insert(record);
    }

    @Override
    public int insertSelective(PurchaseProgress record) {
        return purchaseProgressMapper.insertSelective(record);
    }

    @Override
    public List<PurchaseProgress> selectByExample(PurchaseProgressExample example) {
        return purchaseProgressMapper.selectByExample(example);
    }

    @Override
    public PurchaseProgress selectByPrimaryKey(Long id) {
        return purchaseProgressMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(PurchaseProgress record) {
        return purchaseProgressMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PurchaseProgress record) {
        return purchaseProgressMapper.updateByPrimaryKey(record);
    }


    @Override
    public void asynFromErp(final String date) {
        String startDateTime = date;
        String endDateTime = null;
        if (StringUtils.isEmpty(startDateTime)) {
            endDateTime = DateUtils.format(new Date(), DateUtils.FORMAT_LONG);//当前时间
            startDateTime = DateUtil.addHour(endDateTime, -1);//当前时间-1小时
        } else {
            //指定的开始时间+1小时
            endDateTime = DateUtil.addHour(startDateTime, 24);
        }
        final List<OrganizationRel> rels = getValidOrganization();
        if (ListUtils.isEmpty(rels)) return;
        for (OrganizationRel rel : rels) {
            final Map<String, String> params = new HashMap();
//            params.put(EsbConstant.ERP_IP_P01, String.valueOf(rel.getOrganizationId()));
//            params.put(EsbConstant.ERP_IP_P02, startDateTime);
//            params.put(EsbConstant.ERP_IP_P03, endDateTime);
            params.put(EsbConstant.ERP_SDP_P01, String.valueOf(rel.getOrganizationId()));
            params.put(EsbConstant.ERP_SDP_P02, startDateTime);
            params.put(EsbConstant.ERP_SDP_P03, endDateTime);
//            List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_035, params);
            List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_035, params);
            // C11 是数量;  transactionType:状态
            if (ListUtils.isEmpty(returnItemList)) {
                continue;
            }
            for (SdpTradeResultResponseEleDto erpMassQueryReturnVo : returnItemList) {
                final PurchaseProgressExample condition = new PurchaseProgressExample();
                condition.createCriteria().andTransactionIdEqualTo(erpMassQueryReturnVo.getC3());
                final List<PurchaseProgress> progresses = selectByExample(condition);
                if (ListUtils.isNotEmpty(progresses)) {
                    final PurchaseProgress progress = progresses.get(0);
                    progress.setOuId(Long.parseLong(erpMassQueryReturnVo.getC1()));
                    progress.setReceiptNum(erpMassQueryReturnVo.getC2());
                    progress.setTransactionType(erpMassQueryReturnVo.getC4());
                    progress.setTransactionDate(StringUtils.isNotEmpty(erpMassQueryReturnVo.getC5()) ? DateUtils.parse(erpMassQueryReturnVo.getC5(), DateUtils.FORMAT_SHORT) : null);
                    progress.setPoNumber(erpMassQueryReturnVo.getC6());
                    progress.setPoLineNo(erpMassQueryReturnVo.getC7());
                    progress.setOrganizationId(Long.parseLong(erpMassQueryReturnVo.getC8()));
                    progress.setItemCode(erpMassQueryReturnVo.getC9());
                    progress.setUnitOfMeasure(erpMassQueryReturnVo.getC10());
                    progress.setTransactionQuantity(StringUtils.isNotEmpty(erpMassQueryReturnVo.getC11()) ? new BigDecimal(erpMassQueryReturnVo.getC11()) : null);
                    progress.setSubinventory(erpMassQueryReturnVo.getC12());
                    progress.setLocator(erpMassQueryReturnVo.getC13());
                    progress.setRemark(erpMassQueryReturnVo.getC14());
//                    progress.setProjectCode(erpMassQueryReturnVo.getC15());
                    progress.setUnitPrice(StringUtils.isNotEmpty(erpMassQueryReturnVo.getC15())?new BigDecimal(erpMassQueryReturnVo.getC15()).setScale(6,BigDecimal.ROUND_HALF_UP):null);
                    updateByPrimaryKeySelective(progress);
                } else {
                    final PurchaseProgress progress = new PurchaseProgress();
                    progress.setTransactionId(erpMassQueryReturnVo.getC3());
                    progress.setOuId(Long.parseLong(erpMassQueryReturnVo.getC1()));
                    progress.setReceiptNum(erpMassQueryReturnVo.getC2());
                    progress.setTransactionType(erpMassQueryReturnVo.getC4());
                    progress.setTransactionDate(StringUtils.isNotEmpty(erpMassQueryReturnVo.getC5()) ? DateUtils.parse(erpMassQueryReturnVo.getC5(), DateUtils.FORMAT_SHORT) : null);
                    progress.setPoNumber(erpMassQueryReturnVo.getC6());
                    progress.setPoLineNo(erpMassQueryReturnVo.getC7());
                    progress.setOrganizationId(Long.parseLong(erpMassQueryReturnVo.getC8()));
                    progress.setItemCode(erpMassQueryReturnVo.getC9());
                    progress.setUnitOfMeasure(erpMassQueryReturnVo.getC10());
                    progress.setTransactionQuantity(StringUtils.isNotEmpty(erpMassQueryReturnVo.getC11()) ? new BigDecimal(erpMassQueryReturnVo.getC11()) : null);
                    progress.setSubinventory(erpMassQueryReturnVo.getC12());
                    progress.setLocator(erpMassQueryReturnVo.getC13());
                    progress.setRemark(erpMassQueryReturnVo.getC14());
//                    progress.setProjectCode(erpMassQueryReturnVo.getC15());
                    progress.setUnitPrice(StringUtils.isNotEmpty(erpMassQueryReturnVo.getC15())?new BigDecimal(erpMassQueryReturnVo.getC15()).setScale(6,BigDecimal.ROUND_HALF_UP):null);
                    progress.setOrganizationCode(rel.getOrganizationCode());
                    progress.setOrganizationName(rel.getOrganizationName());
                    progress.setOuName(rel.getOperatingUnitName());
                    insertSelective(progress);
                }
            }
        }

    }

    /**
     * 获取系统有效的库存组织.
     *
     * @return 有效的库存组织
     */
    private List<OrganizationRel> getValidOrganization() {
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/organizationRel/getValidOrganization", new HashMap<>());
        final String res = restTemplate.getForEntity(url , String.class).getBody();
        final DataResponse<List<OrganizationRel>> data = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationRel>>>(){});
        return data != null ? data.getData() : null;
    }

    /**
     * 获取物料的首次定价日期
     * @param projectNum
     * @param materialCode
     * @return
     */
    @Override
    public Date getFirstPriceDate(String projectNum, String materialCode) {
        Date firstPriceDate = null;
        PurchaseProgressExample example = new PurchaseProgressExample();
        example.createCriteria().andItemCodeEqualTo(materialCode);
        //todo 项目号条件查询
        List<PurchaseProgress> progressList = purchaseProgressMapper.selectByExample(example);
        if(!CollectionUtils.isEmpty(progressList)) {
            firstPriceDate = progressList.get(0).getTransactionDate();
        }
        return firstPriceDate;
    }

    @Override
    public BigDecimal getTransactionQuantitySum(String itemCode, String projectCode, List<String> transactionTypes) {
        Asserts.notEmpty(transactionTypes, ErrorCode.CTC_PURCHASE_PROGRESS_TRAN_TYPE_NOT_NULL);

        BigDecimal transactionQuantitySum = new BigDecimal(0);
        if (itemCode == null || projectCode == null) {
            return transactionQuantitySum;
        }

        PurchaseProgressExample example = new PurchaseProgressExample();
        PurchaseProgressExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        criteria.andItemCodeEqualTo(itemCode);
        criteria.andProjectCodeEqualTo(projectCode);
        criteria.andTransactionTypeIn(transactionTypes);
        List<PurchaseProgress> list = purchaseProgressMapper.selectByExample(example);

        for (PurchaseProgress entity : list) {
            transactionQuantitySum = transactionQuantitySum.add(entity.getTransactionQuantity());
        }

        return transactionQuantitySum;
    }

    @Override
    public Map<String, BigDecimal> queryWarehousingNumMapByProjectId(String projectCode) {
        Map<String, BigDecimal> warehousingNumMap = new HashMap<>();
        //接受/入库数量
        List<String> transactionTypes = new ArrayList<>();
        transactionTypes.add("RECEIVE");
        transactionTypes.add("RETURN TO RECEIVING");
        transactionTypes.add("DELIVER");
        transactionTypes.add("RETURN TO VENDOR");

        PurchaseProgressExample example = new PurchaseProgressExample();
        PurchaseProgressExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        criteria.andProjectCodeEqualTo(projectCode);
        criteria.andTransactionTypeIn(transactionTypes);
        List<PurchaseProgress> list = purchaseProgressMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(list)) {
            warehousingNumMap = list.stream().collect(Collectors.toMap(PurchaseProgress::getItemCode,
                    PurchaseProgress::getTransactionQuantity, (e1, e2) -> e1.add(e2)));
        }
        return warehousingNumMap;
    }
}
