package com.midea.pam.ctc.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.GlDailyRateDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.GlDailyRateQuery;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.dto.VendorPenaltyChangeDto;
import com.midea.pam.common.ctc.dto.VendorPenaltyConfigDto;
import com.midea.pam.common.ctc.dto.VendorPenaltyCostDetailDto;
import com.midea.pam.common.ctc.dto.VendorPenaltyDetailDto;
import com.midea.pam.common.ctc.dto.VendorPenaltyDetailHistoryDto;
import com.midea.pam.common.ctc.dto.VendorPenaltyDto;
import com.midea.pam.common.ctc.dto.VendorPenaltyHistoryDto;
import com.midea.pam.common.ctc.dto.VendorPenaltyInvoiceAmountChangeDto;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.VendorPenalty;
import com.midea.pam.common.ctc.entity.VendorPenaltyChange;
import com.midea.pam.common.ctc.entity.VendorPenaltyChangeExample;
import com.midea.pam.common.ctc.entity.VendorPenaltyConfig;
import com.midea.pam.common.ctc.entity.VendorPenaltyConfigExample;
import com.midea.pam.common.ctc.entity.VendorPenaltyCostDetail;
import com.midea.pam.common.ctc.entity.VendorPenaltyCostDetailExample;
import com.midea.pam.common.ctc.entity.VendorPenaltyDetail;
import com.midea.pam.common.ctc.entity.VendorPenaltyDetailExample;
import com.midea.pam.common.ctc.entity.VendorPenaltyDetailHistory;
import com.midea.pam.common.ctc.entity.VendorPenaltyDetailHistoryExample;
import com.midea.pam.common.ctc.entity.VendorPenaltyExample;
import com.midea.pam.common.ctc.entity.VendorPenaltyHistory;
import com.midea.pam.common.ctc.entity.VendorPenaltyHistoryExample;
import com.midea.pam.common.ctc.entity.VendorPenaltyInvoiceAmountChange;
import com.midea.pam.common.ctc.entity.VendorPenaltyInvoiceAmountChangeExample;
import com.midea.pam.common.ctc.excelVo.VendorPenaltyDetailExcelVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.VendorPenaltyChangeStatus;
import com.midea.pam.common.enums.VendorPenaltyStatus;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.common.util.Utils;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetExtMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyChangeMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyConfigMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyCostDetailExtMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyCostDetailMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyDetailExtMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyDetailHistoryExtMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyDetailHistoryMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyDetailMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyExtMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyHistoryExtMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyHistoryMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyInvoiceAmountChangeExtMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyInvoiceAmountChangeMapper;
import com.midea.pam.ctc.mapper.VendorPenaltyMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CostCollectionService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.ctc.service.VendorPenaltyRecordService;
import com.midea.pam.ctc.service.VendorPenaltyService;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class VendorPenaltyServiceImpl implements VendorPenaltyService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private VendorPenaltyMapper vendorPenaltyMapper;
    @Resource
    private VendorPenaltyExtMapper vendorPenaltyExtMapper;
    @Resource
    private VendorPenaltyDetailMapper vendorPenaltyDetailMapper;
    @Resource
    private VendorPenaltyDetailExtMapper vendorPenaltyDetailExtMapper;
    @Resource
    private Validator validator;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectWbsBudgetExtMapper projectWbsBudgetExtMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private VendorPenaltyConfigMapper vendorPenaltyConfigMapper;
    @Resource
    private VendorPenaltyChangeMapper vendorPenaltyChangeMapper;
    @Resource
    private VendorPenaltyDetailHistoryMapper vendorPenaltyDetailHistoryMapper;
    @Resource
    private VendorPenaltyHistoryMapper vendorPenaltyHistoryMapper;
    @Resource
    private VendorPenaltyHistoryExtMapper vendorPenaltyHistoryExtMapper;
    @Resource
    private VendorPenaltyDetailHistoryExtMapper vendorPenaltyDetailHistoryExtMapper;
    @Resource
    EsbService esbService;
    @Resource
    private VendorPenaltyCostDetailExtMapper vendorPenaltyCostDetailExtMapper;
    @Resource
    private VendorPenaltyCostDetailMapper vendorPenaltyCostDetailMapper;
    @Resource
    private CostCollectionService costCollectionService;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private VendorPenaltyInvoiceAmountChangeMapper vendorPenaltyInvoiceAmountChangeMapper;
    @Resource
    private VendorPenaltyInvoiceAmountChangeExtMapper vendorPenaltyInvoiceAmountChangeExtMapper;
    @Resource
    private VendorPenaltyRecordService vendorPenaltyRecordService;
    @Resource
    private SdpCarrierServicel sdpCarrierServicel;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncVendorPenalty(Date startDate, Date endDate, Long unitId) {
        List<OperatingUnitDto> operatingUnitDtoList;
        if (unitId != null) {
            operatingUnitDtoList = basedataExtService.getOperatingUnitByUnitId(unitId);
        } else {
            operatingUnitDtoList = basedataExtService.getAllOperatingUnit();
        }
        if (endDate == null) {
            endDate = DateUtils.addDay(new Date(), 1);
        }
        if (startDate == null) {
            startDate = DateUtils.subtractDay(endDate, 7);
        }
        if (startDate.after(endDate)) {
            throw new BizException(Code.ERROR, "开始时间应小于结束时间");
        }
        List<Map<String, String>> dateZones = DateUtils.cutDateZones(startDate, endDate, 365);
        if (dateZones == null) {
            throw new BizException(Code.ERROR, String.format("时间截取失败，startDate：%s，endDate：%s",
                    DateUtils.format(startDate), DateUtils.format(endDate)));
        }
        Map<String, BigDecimal> conversionRateMap = new HashMap<>(16);
        operatingUnitDtoList.forEach(ou -> {
            OperatingUnitDto operatingUnitDto = basedataExtService.queryUnitByOuId(ou.getOperatingUnitId());
            List<SdpTradeResultResponseEleDto> returnItemListTotal = new ArrayList<>(100);
            dateZones.forEach(dateZone -> {
                Map<String, String> paramMap = new HashMap<>(3);
//                paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(ou.getOperatingUnitId()));
//                paramMap.put(EsbConstant.ERP_IP_P03, dateZone.get("startDate") + " 00:00:00");
//                paramMap.put(EsbConstant.ERP_IP_P04, dateZone.get("endDate") + " 00:00:00");
                paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(ou.getOperatingUnitId()));
                paramMap.put(EsbConstant.ERP_SDP_P03, dateZone.get("startDate") + " 00:00:00");
                paramMap.put(EsbConstant.ERP_SDP_P04, dateZone.get("endDate") + " 00:00:00");
//                List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_070, paramMap);
                List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_070, paramMap);
                if (ListUtils.isNotEmpty(returnItemList)) {
                    returnItemListTotal.addAll(returnItemList);
                }
            });
            completeSync(returnItemListTotal, ou, conversionRateMap, operatingUnitDto);
        });
    }

    private void completeSync(List<SdpTradeResultResponseEleDto> returnItemListTotal,
                              OperatingUnitDto ou,
                              Map<String, BigDecimal> conversionRateMap,
                              OperatingUnitDto operatingUnitDto) {
        if (returnItemListTotal.isEmpty()) {
            return;
        }
        List<VendorPenalty> newVendorPenaltyList = new ArrayList<>();
        List<VendorPenalty> existsVendorPenaltyList = new ArrayList<>();
        List<VendorPenalty> vendorPenaltyList = convertToVendorPenalty(returnItemListTotal, ou);

        List<Long> erpPenaltyIds = vendorPenaltyList.stream().map(VendorPenalty::getErpPenaltyId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (!erpPenaltyIds.isEmpty()) {
            Map<Long, VendorPenaltyDto> existsVendorPenaltyMap = vendorPenaltyExtMapper.selectByErpPenaltyIds(erpPenaltyIds)
                    .stream().collect(Collectors.toMap(VendorPenaltyDto::getErpPenaltyId, Function.identity()));
            vendorPenaltyList.forEach(e -> {
                VendorPenaltyDto vendorPenaltyDto = existsVendorPenaltyMap.get(e.getErpPenaltyId());
                if (vendorPenaltyDto != null) {
                    e.setId(vendorPenaltyDto.getId());
                    existsVendorPenaltyList.add(e);
                } else {
                    newVendorPenaltyList.add(e);
                }
            });
            Date date = new Date();
            Date beginningOfDay = DateUtil.getBeginningOfDay(date);
            Date endingOfDay = DateUtil.getEndingOfDay(date);
            if (!newVendorPenaltyList.isEmpty()) {
                newVendorPenaltyList.forEach(e -> {
                    // 设置汇率
                    if (e.getCurrency() != null && ou.getCurrency() != null) {
                        BigDecimal conversionRate;
                        if (e.getCurrency().equals(ou.getCurrency())) {
                            conversionRate = BigDecimal.ONE;
                        } else {
                            conversionRate = conversionRateMap.computeIfAbsent(e.getCurrency() + "-" + ou.getCurrency(),
                                    key -> {
                                        GlDailyRateQuery query = new GlDailyRateQuery();
                                        query.setFromCurrency(e.getCurrency());
                                        query.setToCurrency(ou.getCurrency());
                                        query.setExchangeDateStart(beginningOfDay);
                                        query.setExchangeDateEnd(endingOfDay);
                                        query.setExchangeType("Corporate");
                                        GlDailyRateDto glDailyRateDto = basedataExtService.queryGlDailyRate(query);
                                        return glDailyRateDto != null ? glDailyRateDto.getExchangeRate() : BigDecimal.ZERO;
                                    });
                            if (conversionRate.compareTo(BigDecimal.ZERO) == 0) {
                                conversionRate = null;
                            }
                        }
                        e.setConversionDate(beginningOfDay);
                        e.setConversionRate(conversionRate);
                    }
                    e.setStatus(VendorPenaltyStatus.PENDING.getCode());
                    e.setCollectionFlag(Boolean.FALSE);
                    if (operatingUnitDto != null) {
                        e.setUnitId(operatingUnitDto.getParentUnitId());
                    }
                    e.setDeletedFlag(Boolean.FALSE);
                    if (e.getErpParentPenaltyId() == null) {
                        e.setCollectionAmount(BigDecimal.ZERO);
                    }
                });
                List<List<VendorPenalty>> lists = ListUtils.splistList(newVendorPenaltyList, 300);
                lists.forEach(list -> {
                    vendorPenaltyExtMapper.batchInsert(list);
                });
            }
            existsVendorPenaltyList.forEach(e -> vendorPenaltyMapper.updateByPrimaryKeySelective(e));
            // 此次同步涉及到的父罚扣的id
            List<Long> erpPenaltyIdList = vendorPenaltyList.stream().map(e ->
                    Utils.getFirstNotNull(e.getErpParentPenaltyId(), e.getErpPenaltyId())
            ).distinct().collect(Collectors.toList());
            // 更新开票金额及拆分金额
            updateInvoiceAmount(erpPenaltyIdList);
            completeReverseHandle(erpPenaltyIdList);
        }
    }

    private List<VendorPenalty> convertToVendorPenalty(List<SdpTradeResultResponseEleDto> returnItemListTotal,
                                                       OperatingUnitDto ou) {
        return returnItemListTotal.stream().map(e -> {
            VendorPenalty vendorPenalty = new VendorPenalty();
            vendorPenalty.setOuId(ou.getOperatingUnitId());
            vendorPenalty.setOuName(ou.getOperatingUnitName());
            vendorPenalty.setCode(e.getC2());
            vendorPenalty.setVendorCode(e.getC3());
            vendorPenalty.setVendorName(e.getC4());
            vendorPenalty.setVendorSiteCode(e.getC5());
            vendorPenalty.setPenaltyType(e.getC7());
            vendorPenalty.setCurrency(e.getC8());
            if (StringUtils.isNotEmpty(e.getC9())) {
                vendorPenalty.setAmount(new BigDecimal(e.getC9()));
            }
            vendorPenalty.setPenaltyTitle(e.getC10());
            if (StringUtils.isNotEmpty(e.getC11())) {
                vendorPenalty.setPenaltyDate(DateUtils.parse(e.getC11()));
            }
            if (StringUtils.isNotEmpty(e.getC12())) {
                vendorPenalty.setErpUpdateAt(DateUtils.parse(e.getC12()));
            }
            if (StringUtils.isNotEmpty(e.getC14())) {
                vendorPenalty.setErpPenaltyId(Long.parseLong(e.getC14()));
            }
            vendorPenalty.setProjectWbsCode(e.getC16());
            vendorPenalty.setSubject(e.getC20());
            vendorPenalty.setSubjectThirdDesc(e.getC21());
            if (StringUtils.isNotEmpty(e.getC13())) {
                vendorPenalty.setErpStatus(Integer.parseInt(e.getC13()));
            }
            if (StringUtils.isNotEmpty(e.getC19())) {
                vendorPenalty.setErpParentPenaltyId(Long.parseLong(e.getC19()));
            }
            vendorPenalty.setErpCreator(e.getC22());
            return vendorPenalty;
        }).collect(Collectors.toList());
    }

    private void completeReverseHandle(List<Long> erpPenaltyIdList) {
        List<VendorPenaltyDetail> detailList = vendorPenaltyDetailExtMapper.selectNeedReversePenaltyDetail(erpPenaltyIdList);
        reverseHandle(detailList);
    }

    /**
     * 更新罚扣头的开票金额、罚扣行的开票金额及已发生成本
     *
     * @param erpPenaltyIdList erp罚扣id
     */
    private void updateInvoiceAmount(List<Long> erpPenaltyIdList) {

        VendorPenaltyExample vendorPenaltyExample = new VendorPenaltyExample();
        vendorPenaltyExample.createCriteria()
                .andErpPenaltyIdIn(erpPenaltyIdList)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        // 更新前的罚扣
        List<VendorPenalty> beforeUpdatePenaltyList = vendorPenaltyMapper.selectByExample(vendorPenaltyExample);
        Map<Long, VendorPenalty> beforeUpdatePenaltyMap = beforeUpdatePenaltyList.stream()
                .collect(Collectors.toMap(VendorPenalty::getId, Function.identity()));

        // 更新罚扣头的开票金额
        vendorPenaltyExtMapper.updateInvoiceAmount(erpPenaltyIdList);

        // 更新后的罚扣
        List<VendorPenalty> vendorPenalties = vendorPenaltyMapper.selectByExample(vendorPenaltyExample);
        Map<Long, VendorPenalty> vendorPenaltyMap = vendorPenalties.stream()
                .collect(Collectors.toMap(VendorPenalty::getId, Function.identity()));

        // 生效且开票金额有变动的才生成罚扣开票金额变更记录
        List<VendorPenaltyInvoiceAmountChange> changeList = new ArrayList<>();
        beforeUpdatePenaltyList.stream().filter(e -> e.getStatus() == 2).forEach(e -> {
            VendorPenalty penalty = vendorPenaltyMap.get(e.getId());
            if (penalty.getInvoiceAmount().compareTo(e.getInvoiceAmount()) != 0) {
                VendorPenaltyInvoiceAmountChange change = new VendorPenaltyInvoiceAmountChange();
                change.setPenaltyId(penalty.getId());
                change.setOriginalInvoiceAmount(e.getInvoiceAmount());
                change.setUpdateInvoiceAmount(penalty.getInvoiceAmount());
                changeList.add(change);
            }
        });
        if (!changeList.isEmpty()) {
            vendorPenaltyInvoiceAmountChangeExtMapper.batchInsert(changeList);
        }

        // 计算罚扣明细行金额分配
        List<Long> penaltyIds = vendorPenalties.stream().map(VendorPenalty::getId).collect(Collectors.toList());
        VendorPenaltyDetailExample vendorPenaltyDetailExample = new VendorPenaltyDetailExample();
        vendorPenaltyDetailExample.createCriteria()
                .andHeadIdIn(penaltyIds)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        Map<Long, List<VendorPenaltyDetail>> detailGroup = vendorPenaltyDetailMapper.selectByExample(vendorPenaltyDetailExample)
                .stream().collect(Collectors.groupingBy(VendorPenaltyDetail::getHeadId));

        detailGroup.forEach((headId, detailList) -> {
            VendorPenalty beforeUpdatePenalty = beforeUpdatePenaltyMap.get(headId);
            VendorPenalty vendorPenalty = vendorPenaltyMap.get(headId);
            if (beforeUpdatePenalty.getInvoiceAmount().compareTo(vendorPenalty.getInvoiceAmount()) != 0) {
                if (vendorPenalty.getAmount().compareTo(BigDecimal.ZERO) != 0) {
                    computePenaltyDetailAmountAllocation(vendorPenalty, detailList, true);
                }
            }
        });
    }

    /**
     * 计算罚扣明细金额分配
     *
     * @param vendorPenalty 罚扣
     * @param detailList    罚扣详情
     */
    private <VP extends VendorPenalty, VPD extends VendorPenaltyDetail> void computePenaltyDetailAmountAllocation(
            VP vendorPenalty, List<VPD> detailList, boolean update) {
        BigDecimal remainInvoiceAmount = vendorPenalty.getInvoiceAmount();
        BigDecimal remainOccurCost = null;
        if (vendorPenalty.getConversionRate() != null) {
            remainOccurCost = vendorPenalty.getInvoiceAmount().multiply(vendorPenalty.getConversionRate()).negate();
        }
        int index = 0;
        for (VendorPenaltyDetail detail : detailList) {
            if (index == detailList.size() - 1) {
                break;
            }
            BigDecimal ratio = vendorPenalty.getInvoiceAmount().divide(vendorPenalty.getAmount(), 2, RoundingMode.HALF_UP);
            // 罚扣明细行分配到的开票金额
            BigDecimal detailInvoiceAmount = ratio.multiply(detail.getAmount());
            detail.setInvoiceAmount(detailInvoiceAmount);
            remainInvoiceAmount = remainInvoiceAmount.subtract(detailInvoiceAmount);

            if (vendorPenalty.getConversionRate() != null && Objects.nonNull(detail.getProjectCost())) {
                // 罚扣明细行分配到的已发生成本
                BigDecimal detailOccurCost = ratio.multiply(detail.getProjectCost());
                detail.setOccurCost(detailOccurCost);
                remainOccurCost = remainOccurCost.subtract(detailOccurCost);
            }
            index++;
        }
        detailList.get(index).setInvoiceAmount(remainInvoiceAmount);
        detailList.get(index).setOccurCost(remainOccurCost);
        if (update) {
            detailList.forEach(e -> vendorPenaltyDetailMapper.updateByPrimaryKeySelective(e));
        }
    }

    @Override
    public PageInfo<VendorPenaltyDto> page(VendorPenaltyDto dto) {
        buildParams(dto);
        PageHelper.startPage(Optional.ofNullable(dto.getPageNum()).orElse(1),
                Optional.ofNullable(dto.getPageSize()).orElse(10));
        List<VendorPenaltyDto> vendorPenaltyDtoList = vendorPenaltyExtMapper.page(dto);
        return BeanConverter.convertPage(vendorPenaltyDtoList, VendorPenaltyDto.class);
    }

    private void buildParams(VendorPenaltyDto dto) {
        dto.setUnitId(SystemContext.getUnitId());
        if (StringUtils.isNotEmpty(dto.getStatusStr())) {
            List<Integer> statuses = Arrays.stream(dto.getStatusStr().split(","))
                    .map(Integer::parseInt).collect(Collectors.toList());
            dto.setStatusList(statuses);
        }
        if (StringUtils.isNotEmpty(dto.getErpStatusStr())) {
            List<Integer> erpStatusList = Arrays.stream(dto.getErpStatusStr().split(","))
                    .map(Integer::parseInt).collect(Collectors.toList());
            dto.setErpStatusList(erpStatusList);
        }

        Set<String> subjects = organizationCustomDictService.queryByName("项目供应商罚扣科目", SystemContext.getUnitId(),
                OrgCustomDictOrgFrom.COMPANY);
        if (subjects != null && subjects.size() > 0) {
            dto.setSubjectList(new ArrayList<>(subjects));
        }
    }

    @Override
    public List<VendorPenaltyDetailDto> listDetailByPenaltyIds(List<Long> penaltyIds) {
        if (ListUtils.isEmpty(penaltyIds)) {
            return Collections.emptyList();
        }
        VendorPenaltyDetailExample detailExample = new VendorPenaltyDetailExample();
        detailExample.createCriteria()
                .andHeadIdIn(penaltyIds)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorPenaltyDetail> detailList = vendorPenaltyDetailMapper.selectByExample(detailExample);
        return BeanConverter.copy(detailList, VendorPenaltyDetailDto.class);
    }

    @Override
    public List<VendorPenaltyDto> listSonPenalty(List<Long> erpPenaltyIds) {
        if (ListUtils.isEmpty(erpPenaltyIds)) {
            return Collections.emptyList();
        }
        VendorPenaltyExample example = new VendorPenaltyExample();
        example.createCriteria()
                .andErpParentPenaltyIdIn(erpPenaltyIds)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        example.setOrderByClause("erp_parent_penalty_id");
        List<VendorPenalty> vendorPenaltyList = vendorPenaltyMapper.selectByExample(example);
        return BeanConverter.copy(vendorPenaltyList, VendorPenaltyDto.class);
    }

    @Override
    public List<String> listPenaltyType() {
        return vendorPenaltyExtMapper.selectAllPenaltyType(SystemContext.getUnitId());
    }

    @Override
    public List<String> listPenaltyCurrency() {
        return vendorPenaltyExtMapper.selectAllPenaltyCurrency(SystemContext.getUnitId());
    }

    @Override
    public VendorPenaltyDto detail(Long id) {
        VendorPenalty vendorPenalty = vendorPenaltyMapper.selectByPrimaryKey(id);
        if (vendorPenalty == null) {
            throw new BizException(Code.ERROR, "供应商罚扣不存在");
        }
        VendorPenaltyDto vendorPenaltyDto = BeanConverter.copy(vendorPenalty, VendorPenaltyDto.class);
        VendorPenaltyDetailExample detailExample = new VendorPenaltyDetailExample();
        detailExample.createCriteria().andHeadIdEqualTo(id)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorPenaltyDetail> vendorPenaltyDetails = vendorPenaltyDetailMapper.selectByExample(detailExample);
        vendorPenaltyDto.setVendorPenaltyDetails(BeanConverter.copy(vendorPenaltyDetails, VendorPenaltyDetailDto.class));

        VendorPenaltyConfigExample configExample = new VendorPenaltyConfigExample();
        configExample.createCriteria()
                .andPenaltyTypeEqualTo(vendorPenaltyDto.getPenaltyType())
                .andUnitIdEqualTo(vendorPenaltyDto.getUnitId())
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorPenaltyConfig> vendorPenaltyConfigs = vendorPenaltyConfigMapper.selectByExample(configExample);
        if (!vendorPenaltyConfigs.isEmpty()) {
            vendorPenaltyDto.setVendorPenaltyConfig(BeanConverter.copy(vendorPenaltyConfigs.get(0), VendorPenaltyConfigDto.class));
        }
        ProjectDto project = lookupProject(vendorPenaltyDto.getProjectWbsCode(), vendorPenaltyDto.getOuId());
        vendorPenaltyDto.setProject(project);
        getChangeList(vendorPenaltyDto);
        getInvoiceAmountChangeList(vendorPenaltyDto);
        return vendorPenaltyDto;
    }

    @Override
    public VendorPenaltyDto detailByApp(Long id) {
        Long vendorPenaltyId = vendorPenaltyRecordService.getVendorPenaltyId(id);
        return detail(vendorPenaltyId);
    }

    /**
     * 查询罚扣已开票金额变更记录表
     *
     * @param vendorPenaltyDto 罚扣记录
     */
    private void getInvoiceAmountChangeList(VendorPenaltyDto vendorPenaltyDto) {
        VendorPenaltyInvoiceAmountChangeExample changeExample = new VendorPenaltyInvoiceAmountChangeExample();
        changeExample.createCriteria().andPenaltyIdEqualTo(vendorPenaltyDto.getId());
        List<VendorPenaltyInvoiceAmountChange> vendorPenaltyInvoiceAmountChanges = vendorPenaltyInvoiceAmountChangeMapper.selectByExample(changeExample);
        vendorPenaltyDto.setInvoiceAmountChangeDtoList(BeanConverter.copy(vendorPenaltyInvoiceAmountChanges, VendorPenaltyInvoiceAmountChangeDto.class));
    }

    private void getChangeList(VendorPenaltyDto vendorPenaltyDto) {
        VendorPenaltyChangeExample changeExample = new VendorPenaltyChangeExample();
        changeExample.createCriteria()
                .andRecordIdEqualTo(vendorPenaltyDto.getId())
                .andStatusNotEqualTo(VendorPenaltyChangeStatus.DRAFT.getCode())
                .andDeletedFlagEqualTo(Boolean.FALSE);
        changeExample.setOrderByClause("id desc");

        List<VendorPenaltyChange> vendorPenaltyChanges = vendorPenaltyChangeMapper.selectByExample(changeExample);
        List<VendorPenaltyChangeDto> changeList = BeanConverter.copy(vendorPenaltyChanges, VendorPenaltyChangeDto.class);
        Map<Long, UserInfo> userNameMap = new HashMap<>(3);
        changeList.forEach(e -> {
            UserInfo userInfo = userNameMap.computeIfAbsent(e.getCreateBy(), CacheDataUtils::findUserById);
            if (userInfo != null) {
                e.setCreator(userInfo.getName());
            }
        });
        vendorPenaltyDto.setChangeList(changeList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long submit(VendorPenaltyDto vendorPenaltyDto) {
        VendorPenalty vendorPenalty = vendorPenaltyMapper.selectByPrimaryKey(vendorPenaltyDto.getId());
        if (vendorPenalty == null) {
            throw new BizException(Code.ERROR, "供应商罚扣不存在");
        }
        if (vendorPenalty.getStatus() != VendorPenaltyStatus.PENDING.getCode()) {
            throw new BizException(Code.ERROR, String.format("不能提交状态为【%s】的供应商罚扣单据",
                    VendorPenaltyStatus.getValue(vendorPenalty.getStatus())));
        }
        vendorPenalty.setAttachmentIds(Optional.ofNullable(vendorPenaltyDto.getAttachmentIds()).orElse(""));
        vendorPenalty.setConversionDate(vendorPenaltyDto.getConversionDate());
        vendorPenalty.setConversionRate(vendorPenaltyDto.getConversionRate());
        vendorPenaltyMapper.updateByPrimaryKeySelective(vendorPenalty);
        List<VendorPenaltyDetailDto> vendorPenaltyDetailDtoList = vendorPenaltyDto.getVendorPenaltyDetails();

        boolean existNullProjectCostFlag = vendorPenaltyDetailDtoList.stream().map(VendorPenaltyDetail::getProjectCost).anyMatch(Objects::isNull);

        if (existNullProjectCostFlag) {
            throw new BizException(Code.ERROR, "项目成本不能为空");
        }

        List<VendorPenaltyDetail> vendorPenaltyDetails = BeanConverter.copy(vendorPenaltyDetailDtoList, VendorPenaltyDetail.class);

        checkWbsBudget(vendorPenaltyDetails, vendorPenalty.getOuId());

        vendorPenaltyDetails.stream().filter(e -> e.getId() != null)
                .forEach(e -> vendorPenaltyDetailMapper.updateByPrimaryKeySelective(e));

        List<VendorPenaltyDetail> newDetails = vendorPenaltyDetails.stream()
                .filter(e -> e.getId() == null).collect(Collectors.toList());
        if (!newDetails.isEmpty()) {
            newDetails.forEach(e -> {
                e.setHeadId(vendorPenalty.getId());
                e.setDeletedFlag(Boolean.FALSE);
                e.setCollectionFlag(Boolean.FALSE);
                e.setSummaryFlag(Boolean.FALSE);
                e.setCollectionAmount(BigDecimal.ZERO);
                e.setSummaryOccurCost(BigDecimal.ZERO);
            });
            vendorPenaltyDetailExtMapper.batchInsert(newDetails);
        }

        // 计算罚扣详情分配的开票金额及已发生成本
        VendorPenaltyDetailExample detailExample = new VendorPenaltyDetailExample();
        detailExample.createCriteria()
                .andHeadIdEqualTo(vendorPenaltyDto.getId())
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorPenaltyDetail> detailList = vendorPenaltyDetailMapper.selectByExample(detailExample);
        if (vendorPenalty.getAmount().compareTo(BigDecimal.ZERO) != 0) {
            computePenaltyDetailAmountAllocation(vendorPenalty, detailList, true);
        }
        Long id = vendorPenalty.getId();
        //添加供应商罚扣提交审批记录表
        if (Objects.nonNull(id) && StringUtils.isNotEmpty(vendorPenaltyDto.getFormUrl())) {
            vendorPenaltyRecordService.saveAndUpdateByVendorPenaltyId(id, vendorPenaltyDto.getFormUrl());
        }
        return id;
    }

    /**
     * 校验 WBS+活动事项的预算 是否存在
     *
     * @param ouId                 业务实体id
     * @param vendorPenaltyDetails 供应商罚扣明细行
     */
    private void checkWbsBudget(List<VendorPenaltyDetail> vendorPenaltyDetails, Long ouId) {

        List<String> wbsCodes = vendorPenaltyDetails.stream()
                .filter(e -> !Boolean.TRUE.equals(e.getDeletedFlag()))
                .map(VendorPenaltyDetail::getWbsCode).collect(Collectors.toList());
        if (wbsCodes.size() == 0) {
            throw new BizException(Code.ERROR, "供应商罚扣明细行不能为空");
        }

        Map<String, List<ProjectWbsBudgetDto>> projectWbsBudgetMap = projectWbsBudgetExtMapper.selectByWbsCode(wbsCodes, ouId)
                .stream().collect(Collectors.groupingBy(e -> e.getProjectCode() + "-" + e.getWbsFullCode()));

        List<String> projectCodes = vendorPenaltyDetails.stream().filter(e -> !Boolean.TRUE.equals(e.getDeletedFlag()))
                .map(VendorPenaltyDetail::getProjectCode).collect(Collectors.toList());

        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria()
                .andOuIdEqualTo(ouId)
                .andCodeIn(projectCodes)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        Map<String, Project> projectMap = projectMapper.selectByExample(projectExample).stream()
                .collect(Collectors.toMap(Project::getCode, Function.identity()));

        List<String> errMsgList = new ArrayList<>();
        vendorPenaltyDetails.stream()
                .filter(e -> !Boolean.TRUE.equals(e.getDeletedFlag())
                        && StringUtils.isNotEmpty(e.getWbsCode())
                        && StringUtils.isNotEmpty(e.getProjectActivityCode())).forEach(e -> {
                    List<ProjectWbsBudgetDto> wbsBudgetDtoList = projectWbsBudgetMap.get(e.getWbsCode());
                    Optional<ProjectWbsBudgetDto> first = Optional.empty();
                    if (ListUtils.isNotEmpty(wbsBudgetDtoList)) {
                        first = wbsBudgetDtoList.stream().filter(w ->
                                w.getActivityCode().equals(e.getProjectActivityCode())).findFirst();
                    }
                    if (!first.isPresent()) {
                        Project project = projectMap.get(e.getProjectCode());
                        if (project == null) {
                            errMsgList.add(String.format("项目：%s不存在", e.getProjectCode()));
                        } else {
                            errMsgList.add(String.format("项目：%s，WBS:%s，活动事项：%s的预算不存在，请找对应的项目经理：%s新增预算",
                                    e.getProjectCode(), e.getWbsCode(), e.getProjectActivityCode(), project.getManagerName()));
                        }
                    }
                });
        if (!errMsgList.isEmpty()) {
            throw new BizException(Code.ERROR, String.join("\n", errMsgList));
        }
    }

    @Override
    public VendorPenaltyDto importVendorPenaltyDetail(List<VendorPenaltyDetailExcelVo> excelVoList, Long ouId) {
        VendorPenaltyDto vendorPenaltyDto = new VendorPenaltyDto();
        vendorPenaltyDto.setExcelVoList(excelVoList);
        List<String> errMsgList = new ArrayList<>();
        vendorPenaltyDto.setErrMsgList(errMsgList);
        excelVoList.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining("，"))));
        int i = 2;
        for (VendorPenaltyDetailExcelVo vo : excelVoList) {
            if (StringUtils.isNotEmpty(vo.getErrMsg())) {
                errMsgList.add(String.format("行：%s，%s", i, vo.getErrMsg()));
            }
            vo.setErrMsg(null);
            i++;
        }
        if (errMsgList.isEmpty()) {
            List<String> projectCodes = excelVoList.stream().map(VendorPenaltyDetailExcelVo::getProjectCode).distinct().collect(Collectors.toList());
            if (projectCodes.size() != excelVoList.size()) {
                errMsgList.add("项目号不能相同");
            }

            ProjectExample projectExample = new ProjectExample();
            projectExample.createCriteria()
                    .andOuIdEqualTo(ouId)
                    .andCodeIn(projectCodes)
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            Map<String, Project> projectMap = projectMapper.selectByExample(projectExample)
                    .stream().collect(Collectors.toMap(Project::getCode, Function.identity()));
            i = 2;
            for (VendorPenaltyDetailExcelVo vo : excelVoList) {
                Project project = projectMap.get(vo.getProjectCode());
                if (project != null) {
                    vo.setWbsEnabled(project.getWbsEnabled());
                } else {
                    errMsgList.add(String.format("行：%s，填写的项目号不存在", i));
                }
                try {
                    new BigDecimal(vo.getAmount());
                } catch (Exception e) {
                    errMsgList.add(String.format("行：%s，填写的罚扣金额有误", i));
                }
                i++;
            }
        }
        return vendorPenaltyDto;
    }

    private ProjectDto lookupProject(String code, Long ouId) {
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria()
                .andOuIdEqualTo(ouId)
                .andCodeEqualTo(code)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<Project> projects = projectMapper.selectByExample(projectExample);
        ProjectDto projectDto = null;
        if (!projects.isEmpty()) {
            projectDto = new ProjectDto();
            projectDto.setCode(projects.get(0).getCode());
            projectDto.setWbsEnabled(projects.get(0).getWbsEnabled());
        } else {
            List<ProjectWbsBudgetDto> projectWbsBudgets = projectWbsBudgetExtMapper.selectByWbsCode(Collections.singletonList(code), ouId);
            if (!projectWbsBudgets.isEmpty()) {
                projectDto = new ProjectDto();
                projectDto.setCode(projectWbsBudgets.get(0).getProjectCode());
                projectDto.setWbsEnabled(projectWbsBudgets.get(0).getWbsEnabled());
            }
        }
        return projectDto;
    }

    @Override
    public void draftSubmitCallback(String formInstanceId) {
        logger.info("供应商罚扣提交审批回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("供应商罚扣提交审批回调：formInstanceId为空，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调,formInstanceId为空");
        }
        Long vendorPenaltyId = vendorPenaltyRecordService.getVendorPenaltyId(Long.valueOf(formInstanceId));
        if (Objects.isNull(vendorPenaltyId)) {
            logger.error("供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据id不存在，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据id不存在");
        }
        VendorPenalty vendorPenalty = vendorPenaltyMapper.selectByPrimaryKey(vendorPenaltyId);
        if (vendorPenalty == null) {
            logger.error("供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据不存在，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据不存在");
        }
        vendorPenalty.setStatus(VendorPenaltyStatus.APPROVAL.getCode());
        vendorPenaltyMapper.updateByPrimaryKeySelective(vendorPenalty);
    }

    @Override
    public void refuseCallback(String formInstanceId) {
        logger.info("供应商罚扣审批驳回回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("供应商罚扣审批驳回回调：formInstanceId为空，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调,formInstanceId为空");
        }
        Long vendorPenaltyId = vendorPenaltyRecordService.getVendorPenaltyId(Long.valueOf(formInstanceId));
        if (Objects.isNull(vendorPenaltyId)) {
            logger.error("供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据id不存在，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据id不存在");
        }
        VendorPenalty vendorPenalty = vendorPenaltyMapper.selectByPrimaryKey(vendorPenaltyId);
        if (vendorPenalty == null) {
            logger.error("供应商罚扣审批驳回回调：formInstanceId对应的供应商罚扣单据不存在，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据不存在");
        }
        vendorPenalty.setStatus(VendorPenaltyStatus.PENDING.getCode());
        vendorPenaltyMapper.updateByPrimaryKeySelective(vendorPenalty);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void passCallback(String formInstanceId, Long companyId) {
        logger.info("供应商罚扣审批通过回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("供应商罚扣审批通过回调：formInstanceId为空，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调,formInstanceId为空");
        }
        Long vendorPenaltyId = vendorPenaltyRecordService.getVendorPenaltyId(Long.valueOf(formInstanceId));
        if (Objects.isNull(vendorPenaltyId)) {
            logger.error("供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据id不存在，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据id不存在");
        }
        VendorPenalty vendorPenalty = vendorPenaltyMapper.selectByPrimaryKey(vendorPenaltyId);
        if (vendorPenalty == null) {
            logger.error("供应商罚扣审批通过回调：formInstanceId对应的供应商罚扣单据不存在，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据不存在");
        }
        vendorPenalty.setStatus(VendorPenaltyStatus.EFFECTIVE.getCode());
        vendorPenaltyMapper.updateByPrimaryKeySelective(vendorPenalty);
    }

    @Override
    public void draftReturnCallback(String formInstanceId) {
        logger.info("供应商罚扣审批撤回回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("供应商罚扣审批撤回回调：formInstanceId为空，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调,formInstanceId为空");
        }
        Long vendorPenaltyId = vendorPenaltyRecordService.getVendorPenaltyId(Long.valueOf(formInstanceId));
        if (Objects.isNull(vendorPenaltyId)) {
            logger.error("供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据id不存在，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据id不存在");
        }
        VendorPenalty vendorPenalty = vendorPenaltyMapper.selectByPrimaryKey(vendorPenaltyId);
        if (vendorPenalty == null) {
            logger.error("供应商罚扣审批撤回回调：formInstanceId对应的供应商罚扣单据不存在，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据不存在");
        }
        vendorPenalty.setStatus(VendorPenaltyStatus.PENDING.getCode());
        vendorPenaltyMapper.updateByPrimaryKeySelective(vendorPenalty);
    }

    @Override
    public void abandonCallback(String formInstanceId) {
        logger.info("供应商罚扣审批作废回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("供应商罚扣审批作废回调：formInstanceId为空，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调,formInstanceId为空");
        }
        Long vendorPenaltyId = vendorPenaltyRecordService.getVendorPenaltyId(Long.valueOf(formInstanceId));
        if (Objects.isNull(vendorPenaltyId)) {
            logger.error("供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据id不存在，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据id不存在");
        }
        VendorPenalty vendorPenalty = vendorPenaltyMapper.selectByPrimaryKey(vendorPenaltyId);
        if (vendorPenalty == null) {
            logger.error("供应商罚扣审批作废回调：formInstanceId对应的供应商罚扣单据不存在，不处理");
            throw new BizException(Code.ERROR, "供应商罚扣提交审批回调：formInstanceId对应的供应商罚扣单据不存在");
        }
        vendorPenalty.setStatus(VendorPenaltyStatus.PENDING.getCode());
        vendorPenaltyMapper.updateByPrimaryKeySelective(vendorPenalty);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveChange(VendorPenaltyChangeDto vendorPenaltyChangeDto) {
        VendorPenalty vendorPenalty = vendorPenaltyMapper.selectByPrimaryKey(vendorPenaltyChangeDto.getRecordId());
        if (vendorPenalty == null) {
            throw new BizException(Code.ERROR, "供应商罚扣单据不存在");
        }
        if (vendorPenalty.getStatus() != VendorPenaltyStatus.EFFECTIVE.getCode()) {
            throw new BizException(Code.ERROR, String.format("不能对状态为【%S】的供应商罚扣单据进行变更",
                    VendorPenaltyStatus.getValue(vendorPenalty.getStatus())));
        }
        if (ListUtils.isEmpty(vendorPenaltyChangeDto.getVendorPenaltyDetailHistoryList())) {
            throw new BizException(Code.ERROR, "供应商罚扣变更明细不能为空");
        }

        checkWbsBudget(BeanConverter.copy(vendorPenaltyChangeDto.getVendorPenaltyDetailHistoryList(), VendorPenaltyDetail.class),
                vendorPenalty.getOuId());

        VendorPenaltyChange vendorPenaltyChange = saveVendorPenaltyChange(vendorPenaltyChangeDto);
        saveVendorPenaltyHistory(vendorPenaltyChangeDto, vendorPenalty, vendorPenaltyChange.getId());
        saveVendorPenaltyDetailHistory(vendorPenaltyChangeDto, vendorPenalty, vendorPenaltyChange.getId());
        return vendorPenaltyChange.getId();
    }

    private VendorPenaltyChange saveVendorPenaltyChange(VendorPenaltyChangeDto vendorPenaltyChangeDto) {
        VendorPenaltyChange change = BeanConverter.copy(vendorPenaltyChangeDto, VendorPenaltyChange.class);
        if (change.getId() != null) {
            VendorPenaltyChange oriChange = vendorPenaltyChangeMapper.selectByPrimaryKey(change.getId());
            if (oriChange == null) {
                throw new BizException(Code.ERROR, "供应商罚扣变更记录不存在");
            }
            if (oriChange.getStatus() != VendorPenaltyChangeStatus.DRAFT.getCode() &&
                    oriChange.getStatus() != VendorPenaltyChangeStatus.REFUSE.getCode() &&
                    oriChange.getStatus() != VendorPenaltyChangeStatus.DRAFT_RETURN.getCode()) {
                throw new BizException(Code.ERROR, String.format("不能对状态为【%S】的供应商罚扣变更进行提交",
                        VendorPenaltyChangeStatus.getValue(oriChange.getStatus())));
            }
            vendorPenaltyDetailHistoryExtMapper.deleteByChangeId(change.getId());
            vendorPenaltyHistoryExtMapper.deleteByChangeId(change.getId());
            change.setStatus(oriChange.getStatus());
            vendorPenaltyChangeMapper.updateByPrimaryKeySelective(change);
        } else {
            change.setStatus(VendorPenaltyChangeStatus.DRAFT.getCode());
            change.setDeletedFlag(Boolean.FALSE);
            vendorPenaltyChangeMapper.insertSelective(change);
        }
        return change;
    }

    /**
     * 保存罚扣明细变更历史
     *
     * @param changeDto 变更记录信息
     * @param changeId  变更记录id
     */
    private void saveVendorPenaltyDetailHistory(VendorPenaltyChangeDto changeDto, VendorPenalty vendorPenalty, Long changeId) {
        VendorPenaltyDetailExample example = new VendorPenaltyDetailExample();
        example.createCriteria()
                .andHeadIdEqualTo(changeDto.getRecordId())
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorPenaltyDetail> detailList = vendorPenaltyDetailMapper.selectByExample(example);
        List<VendorPenaltyDetailHistory> historyList = BeanConverter.copy(detailList, VendorPenaltyDetailHistory.class);
        historyList.forEach(e -> {
            e.setOriginId(e.getId());
            e.setHistoryType(1);
            e.setChangeId(changeId);
            e.setId(null);
        });

        List<VendorPenaltyDetailHistoryDto> vendorPenaltyDetailHistoryList = changeDto.getVendorPenaltyDetailHistoryList();
        List<Long> changeDetailIds = vendorPenaltyDetailHistoryList.stream().map(VendorPenaltyDetailHistoryDto::getOriginId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<VendorPenaltyDetailDto> newDetailList = new ArrayList<>(vendorPenaltyDetailHistoryList.size());
        detailList.forEach(e -> {
            if (!changeDetailIds.contains(e.getId())) {
                newDetailList.add(BeanConverter.copy(e, VendorPenaltyDetailDto.class));
            }
        });
        List<VendorPenaltyDetailDto> detailDtoList = BeanConverter.copy(vendorPenaltyDetailHistoryList, VendorPenaltyDetailDto.class);
        detailDtoList.forEach(e -> {
            if (!Boolean.TRUE.equals(e.getDeletedFlag())) {
                newDetailList.add(e);
            }
        });
        computePenaltyDetailAmountAllocation(vendorPenalty, newDetailList, false);
        BeanConverter.copy(detailDtoList, VendorPenaltyDetailHistory.class).forEach(e -> {
            e.setHistoryType(2);
            e.setChangeId(changeId);
            e.setHeadId(changeDto.getRecordId());
            e.setId(null);
            if (e.getDeletedFlag() == null) {
                e.setDeletedFlag(Boolean.FALSE);
            }
            historyList.add(e);
        });
        vendorPenaltyDetailHistoryExtMapper.batchInsert(historyList);
    }

    /**
     * 保存罚扣基本信息变更历史
     *
     * @param changeDto     变更记录信息
     * @param vendorPenalty 罚扣单据
     * @param changeId      变更记录id
     */
    private void saveVendorPenaltyHistory(VendorPenaltyChangeDto changeDto, VendorPenalty vendorPenalty, Long changeId) {
        VendorPenaltyHistory vendorPenaltyHistory = BeanConverter.copy(vendorPenalty, VendorPenaltyHistory.class);
        vendorPenaltyHistory.setOriginId(vendorPenalty.getId());
        vendorPenaltyHistory.setHistoryType(1);
        vendorPenaltyHistory.setChangeId(changeId);
        vendorPenaltyHistory.setId(null);

        List<VendorPenaltyHistory> vendorPenaltyHistoryList = new ArrayList<>(2);
        vendorPenaltyHistoryList.add(vendorPenaltyHistory);

        vendorPenaltyHistory = BeanConverter.copy(changeDto.getVendorPenaltyHistory(), VendorPenaltyHistory.class);
        vendorPenaltyHistory.setOriginId(vendorPenalty.getId());
        vendorPenaltyHistory.setHistoryType(2);
        vendorPenaltyHistory.setChangeId(changeId);
        vendorPenaltyHistory.setDeletedFlag(Boolean.FALSE);
        vendorPenaltyHistory.setId(null);
        vendorPenaltyHistoryList.add(vendorPenaltyHistory);

        vendorPenaltyHistoryExtMapper.batchInsert(vendorPenaltyHistoryList);
    }

    @Override
    public VendorPenaltyChangeDto changeDetail(Long changeId) {
        VendorPenaltyChange vendorPenaltyChange = vendorPenaltyChangeMapper.selectByPrimaryKey(changeId);
        if (vendorPenaltyChange == null) {
            throw new BizException(Code.ERROR, "供应商罚扣变更记录不存在");
        }
        VendorPenaltyChangeDto vendorPenaltyChangeDto = BeanConverter.copy(vendorPenaltyChange, VendorPenaltyChangeDto.class);

        // 罚扣基本信息
        VendorPenaltyHistoryExample vendorPenaltyHistoryExample = new VendorPenaltyHistoryExample();
        vendorPenaltyHistoryExample.createCriteria()
                .andChangeIdEqualTo(changeId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorPenaltyHistory> vendorPenaltyHistoryList = vendorPenaltyHistoryMapper.selectByExample(vendorPenaltyHistoryExample);
        vendorPenaltyChangeDto.setVendorPenaltyHistoryList(BeanConverter.copy(vendorPenaltyHistoryList, VendorPenaltyHistoryDto.class));

        // 罚扣明细信息
        VendorPenaltyDetailHistoryExample detailHistoryExample = new VendorPenaltyDetailHistoryExample();
        detailHistoryExample.createCriteria().andChangeIdEqualTo(changeId);
        List<VendorPenaltyDetailHistory> detailHistories = vendorPenaltyDetailHistoryMapper.selectByExample(detailHistoryExample);
        List<VendorPenaltyDetailHistoryDto> detailHistoryDtoList = BeanConverter.copy(detailHistories, VendorPenaltyDetailHistoryDto.class);
        List<List<VendorPenaltyDetailHistoryDto>> detailHistoryDtoLists = new ArrayList<>(detailHistoryDtoList.stream().filter(e -> e.getOriginId() != null).collect(Collectors.groupingBy(VendorPenaltyDetailHistoryDto::getOriginId)).values());
        detailHistoryDtoList.stream().filter(e -> e.getOriginId() == null).forEach(e -> {
            detailHistoryDtoLists.add(Collections.singletonList(e));
        });
        vendorPenaltyChangeDto.setDetailHistoryDtoLists(detailHistoryDtoLists);
        return vendorPenaltyChangeDto;
    }

    @Override
    public void abandonChange(Long changeId) {
        VendorPenaltyChange change = vendorPenaltyChangeMapper.selectByPrimaryKey(changeId);
        change.setStatus(VendorPenaltyChangeStatus.ABANDON.getCode());
        vendorPenaltyChangeMapper.updateByPrimaryKeySelective(change);
        VendorPenalty vendorPenalty = vendorPenaltyMapper.selectByPrimaryKey(change.getRecordId());
        vendorPenalty.setStatus(VendorPenaltyStatus.EFFECTIVE.getCode());
        vendorPenaltyMapper.updateByPrimaryKeySelective(vendorPenalty);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeDraftSubmitCallback(String formInstanceId) {
        logger.info("供应商罚扣变更提交回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("供应商罚扣变更提交回调：formInstanceId为空，不处理");
            return;
        }
        VendorPenaltyChange change = vendorPenaltyChangeMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (change == null) {
            logger.error("供应商罚扣变更提交回调：formInstanceId对应的供应商罚扣变更不存在，不处理");
            return;
        }
        change.setStatus(VendorPenaltyChangeStatus.APPROVAL.getCode());
        vendorPenaltyChangeMapper.updateByPrimaryKeySelective(change);
        changeVendorPenaltyStatus(change.getRecordId(), VendorPenaltyStatus.CHANGING);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeRefuseCallback(String formInstanceId) {
        logger.info("供应商罚扣变更审批驳回回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("供应商罚扣变更审批驳回回调：formInstanceId为空，不处理");
            return;
        }
        VendorPenaltyChange change = vendorPenaltyChangeMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (change == null) {
            logger.error("供应商罚扣变更审批驳回回调：formInstanceId对应的供应商罚扣变更不存在，不处理");
            return;
        }
        change.setStatus(VendorPenaltyChangeStatus.REFUSE.getCode());
        vendorPenaltyChangeMapper.updateByPrimaryKeySelective(change);
        changeVendorPenaltyStatus(change.getRecordId(), VendorPenaltyStatus.EFFECTIVE);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changePassCallback(String formInstanceId) {
        logger.info("供应商罚扣变更审批通过回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("供应商罚扣变更审批通过回调：formInstanceId为空，不处理");
            return;
        }
        VendorPenaltyChange change = vendorPenaltyChangeMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (change == null) {
            logger.error("供应商罚扣变更审批通过回调：formInstanceId对应的供应商罚扣变更不存在，不处理");
            return;
        }
        change.setStatus(VendorPenaltyChangeStatus.PASS.getCode());
        vendorPenaltyChangeMapper.updateByPrimaryKeySelective(change);

        completeVendorPenaltyDetailChange(change);

        completeVendorPenaltyChange(change.getId());
    }

    /**
     * 完成罚扣基本信息变更
     *
     * @param changeId 变更记录id
     */
    private void completeVendorPenaltyChange(Long changeId) {
        VendorPenaltyHistoryExample historyExample = new VendorPenaltyHistoryExample();
        historyExample.createCriteria()
                .andChangeIdEqualTo(changeId)
                .andHistoryTypeEqualTo(2);
        List<VendorPenaltyHistory> history = vendorPenaltyHistoryMapper.selectByExample(historyExample);
        if (!history.isEmpty()) {
            VendorPenalty vendorPenalty = BeanConverter.copy(history.get(0), VendorPenalty.class);
            vendorPenalty.setId(history.get(0).getOriginId());
            vendorPenalty.setStatus(VendorPenaltyStatus.EFFECTIVE.getCode());
            vendorPenalty.setCollectionFlag(Boolean.FALSE);
            vendorPenalty.setCollectionAmount(BigDecimal.ZERO);
            vendorPenalty.setCreateAt(null);
            vendorPenalty.setCreateBy(null);
            vendorPenaltyMapper.updateByPrimaryKeySelective(vendorPenalty);
        }
    }

    /**
     * 完成罚扣详情变更
     *
     * @param change 变更记录
     */
    private void completeVendorPenaltyDetailChange(VendorPenaltyChange change) {
        VendorPenaltyDetailHistoryExample detailHistoryExample = new VendorPenaltyDetailHistoryExample();
        detailHistoryExample.createCriteria()
                .andChangeIdEqualTo(change.getId())
                .andHistoryTypeEqualTo(2);
        List<VendorPenaltyDetailHistory> detailHistoryList = vendorPenaltyDetailHistoryMapper.selectByExample(detailHistoryExample);

        // 新增的罚扣详情
        List<VendorPenaltyDetailHistory> newDetails = detailHistoryList.stream()
                .filter(e -> e.getOriginId() == null).collect(Collectors.toList());
        if (!newDetails.isEmpty()) {
            List<VendorPenaltyDetail> detailList = BeanConverter.copy(newDetails, VendorPenaltyDetail.class);
            detailList.forEach(e -> {
                e.setCollectionFlag(Boolean.FALSE);
                e.setSummaryFlag(Boolean.FALSE);
                e.setCollectionAmount(BigDecimal.ZERO);
                e.setSummaryOccurCost(BigDecimal.ZERO);
            });
            vendorPenaltyDetailExtMapper.batchInsert(detailList);
        }

        // 变更的罚扣详情
        List<VendorPenaltyDetailHistory> updateDetails = detailHistoryList.stream()
                .filter(e -> e.getOriginId() != null).collect(Collectors.toList());
        if (!updateDetails.isEmpty()) {
            updateDetails.forEach(e -> {
                VendorPenaltyDetail detail = BeanConverter.copy(e, VendorPenaltyDetail.class);
                detail.setId(e.getOriginId());
                detail.setCreateAt(null);
                detail.setCreateBy(null);
                vendorPenaltyDetailMapper.updateByPrimaryKeySelective(detail);
            });
        }

        //完成变更后,进行金额拆分分配重计算及逆向处理
        VendorPenaltyDetailExample detailExample = new VendorPenaltyDetailExample();
        detailExample.createCriteria().andHeadIdEqualTo(change.getRecordId());
        List<VendorPenaltyDetail> detailList = vendorPenaltyDetailMapper.selectByExample(detailExample);
        VendorPenalty vendorPenalty = vendorPenaltyMapper.selectByPrimaryKey(change.getRecordId());
        computePenaltyDetailAmountAllocation(vendorPenalty,
                detailList.stream().filter(e -> !e.getDeletedFlag()).collect(Collectors.toList()), true);
        reverseHandle(detailList);
    }

    /**
     * 逆向处理: 生成返回归集, 清除成本归集和项目成本汇总的金额和标识
     */
    private void reverseHandle(List<VendorPenaltyDetail> detailList) {
        // 进行反向归集
        List<VendorPenaltyDetail> collectDetails = detailList.stream()
                .filter(e -> e.getCollectionFlag() && (e.getCollectionAmount().compareTo(e.getInvoiceAmount()) != 0 || e.getDeletedFlag()))
                .collect(Collectors.toList());
        Set<Long> detailIds = new HashSet<>(detailList.size());
        if (!collectDetails.isEmpty()) {
            List<Long> penaltyIds = collectDetails.stream().map(VendorPenaltyDetail::getHeadId).distinct().collect(Collectors.toList());
            VendorPenaltyExample vendorPenaltyExample = new VendorPenaltyExample();
            vendorPenaltyExample.createCriteria().andIdIn(penaltyIds);
            List<VendorPenalty> vendorPenaltyList = vendorPenaltyMapper.selectByExample(vendorPenaltyExample);
            Map<Long, VendorPenalty> vendorPenaltyMap = vendorPenaltyList.stream().collect(Collectors.toMap(VendorPenalty::getId, Function.identity()));
            List<VendorPenaltyDetailDto> detailDtoList = new ArrayList<>(collectDetails.size());
            collectDetails.forEach(e -> {
                VendorPenalty vendorPenalty = vendorPenaltyMap.get(e.getHeadId());
                VendorPenaltyDetailDto detailDto = BeanConverter.copy(e, VendorPenaltyDetailDto.class);
                detailDto.setPenaltyCode(vendorPenalty.getCode());
                detailDto.setVendorCode(vendorPenalty.getVendorCode());
                detailDto.setVendorName(vendorPenalty.getVendorName());
                detailDto.setCurrency(vendorPenalty.getCurrency());
                detailDto.setConversionRate(vendorPenalty.getConversionRate());
                detailDto.setPenaltyCreateTime(DateUtils.getShortDate(new Date()));
                detailDto.setInvoiceAmount(detailDto.getCollectionAmount().negate());
                detailDto.setOccurCost(detailDto.getSummaryOccurCost().negate());
                detailDtoList.add(detailDto);
                detailIds.add(e.getId());
            });
            Map<String, List<VendorPenaltyDetailDto>> detailDtoMap = detailDtoList.stream()
                    .collect(Collectors.groupingBy(VendorPenaltyDetailDto::getProjectCode));
            detailDtoMap.forEach((k, v) -> costCollectionService.collectVendorPenaltyCost(v));
            vendorPenaltyExtMapper.clearCollectionFlag(penaltyIds);
        }

        // 需重新进行项目成本汇总的罚扣明细行id
        List<Long> penaltyDetailIds = detailList.stream()
                .filter(e -> e.getSummaryFlag() && (e.getSummaryOccurCost().compareTo(e.getOccurCost()) != 0 || e.getDeletedFlag()))
                .map(VendorPenaltyDetail::getId).collect(Collectors.toList());

        detailIds.addAll(penaltyDetailIds);
        if (!detailIds.isEmpty()) {
            // 清除归集和汇总标识, 并重置归集金额和汇总金额为零
            vendorPenaltyDetailExtMapper.clearCollectionAndSummaryFlag(detailIds);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeDraftReturnCallback(String formInstanceId) {
        logger.info("供应商罚扣变更审批撤回回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("供应商罚扣变更审批撤回回调：formInstanceId为空，不处理");
            return;
        }
        VendorPenaltyChange change = vendorPenaltyChangeMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (change == null) {
            logger.error("供应商罚扣变更审批撤回回调：formInstanceId对应的供应商罚扣变更不存在，不处理");
            return;
        }
        change.setStatus(VendorPenaltyChangeStatus.DRAFT_RETURN.getCode());
        vendorPenaltyChangeMapper.updateByPrimaryKeySelective(change);
        changeVendorPenaltyStatus(change.getRecordId(), VendorPenaltyStatus.EFFECTIVE);
    }

    @Override
    public void changeAbandonCallback(String formInstanceId) {
        logger.info("供应商罚扣变更审批作废回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("供应商罚扣变更审批作废回调：formInstanceId为空，不处理");
            return;
        }
        VendorPenaltyChange change = vendorPenaltyChangeMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (change == null) {
            logger.error("供应商罚扣变更审批作废回调：formInstanceId对应的供应商罚扣变更不存在，不处理");
            return;
        }
        change.setStatus(VendorPenaltyChangeStatus.ABANDON.getCode());
        vendorPenaltyChangeMapper.updateByPrimaryKeySelective(change);
    }

    private void changeVendorPenaltyStatus(Long vendorPenaltyId, VendorPenaltyStatus status) {
        VendorPenalty vendorPenalty = new VendorPenalty();
        vendorPenalty.setId(vendorPenaltyId);
        vendorPenalty.setStatus(status.getCode());
        vendorPenaltyMapper.updateByPrimaryKeySelective(vendorPenalty);
    }

    @Override
    public List<VendorPenaltyDetailDto> getCollectionPenaltyDetail(CostCollectionDto collectionDto) {
        List<VendorPenaltyDetailDto> detailList = vendorPenaltyCostDetailExtMapper.selectCollectionPenaltyDetail(collectionDto.getOuId(), collectionDto.getProjectCode());
        if (detailList.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> detailIds = detailList.stream().map(VendorPenaltyDetailDto::getId).collect(Collectors.toList());
        vendorPenaltyDetailExtMapper.updateCollectionFlag(detailIds);

        List<Long> penaltyIds = detailList.stream().map(VendorPenaltyDetailDto::getPenaltyId).distinct().collect(Collectors.toList());
        vendorPenaltyExtMapper.updateCollectionFlag(penaltyIds);

        return detailList;
    }

    @Override
    public List<VendorPenaltyCostDetailDto> queryByCollection(Long collectionId) {
        VendorPenaltyCostDetailExample example = new VendorPenaltyCostDetailExample();
        example.createCriteria()
                .andCollectionIdEqualTo(collectionId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<VendorPenaltyCostDetail> costDetails = vendorPenaltyCostDetailMapper.selectByExample(example);
        return BeanConverter.copy(costDetails, VendorPenaltyCostDetailDto.class);
    }
}
