package com.midea.pam.ctc.ext.service.impl;

import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.ctc.dto.CostAccountingErpDto;
import com.midea.pam.common.ctc.dto.HroBillEntryAccountInfoDto;
import com.midea.pam.common.ctc.dto.HroWorkingHourBillDto;
import com.midea.pam.common.ctc.dto.PurchaseContractDTO;
import com.midea.pam.common.ctc.entity.HroWorkingHourBill;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.HroWorkingHourBillSyncStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.DateUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.mapper.HroWorkingHourBillMapper;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.HroWorkingHourBillService;
import com.midea.pam.ctc.service.OrganizationRelExtService;
import com.midea.pam.ctc.service.SdpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * PAM-ERP-029-6采购合同对账单入账写入
 *
 * <AUTHOR>
 */
public class PurchaseContractBillEsbServiceImpl extends AbstractCommonBusinessService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private HroWorkingHourBillMapper hroWorkingHourBillMapper;
    @Resource
    private HroWorkingHourBillService hroWorkingHourBillService;
    @Resource
    private EsbService esbService;
    @Resource
    private OrganizationRelExtService organizationRelExtService;
    @Resource
    private SdpService sdpService;


    @Override
    public EsbResponse<?> execute(ResendExecute resendExecute) {
        logger.info("采购合同对账单入账写入开始：" + resendExecute);
        String applyNo = resendExecute.getApplyNo();
        Assert.notNull(applyNo, "applyNo不能为空");
        HroWorkingHourBillDto billDto = null;
        try {
            billDto = hroWorkingHourBillService.getEntryAccountInfo(Long.valueOf(applyNo));
            Assert.notNull(billDto, String.format("id为【%s】的点工工时对账单不存在", applyNo));
            List<CostAccountingErpDto> dtoList = formCostAccountingErpDto(billDto);
//            return esbService.callCUXGLJOURNALSIMPORTAPIPKGPortType(dtoList);
            return sdpService.callGERPGlJournalsImport(dtoList);
        } catch (Exception e) {
            HroWorkingHourBill bill = new HroWorkingHourBill();
            bill.setId(Long.valueOf(applyNo));
            bill.setErpSyncStatus(HroWorkingHourBillSyncStatus.SYNC_FAIL.getCode());
            bill.setErpSyncMsg("同步失败：" + e.getMessage());
            hroWorkingHourBillMapper.updateByPrimaryKeySelective(bill);
            logger.error("采购合同对账单入账写入失败", e);
            EsbResponse<Object> esbResponse = new EsbResponse<>();
            throw e;
        }
    }

    private List<CostAccountingErpDto> formCostAccountingErpDto(HroWorkingHourBillDto billDto) {
        List<CostAccountingErpDto> dtoList = new ArrayList<>();
        PurchaseContractDTO contractDTO = billDto.getPurchaseContractDTO();
        int index = 1;
        List<HroBillEntryAccountInfoDto> entryAccountInfoDtoList = billDto.getEntryAccountInfoDtoList();
        if (ListUtils.isEmpty(entryAccountInfoDtoList)) {
            throw new BizException(Code.ERROR, "入账信息不存在");
        }
        for (HroBillEntryAccountInfoDto infoDto : entryAccountInfoDtoList) {
            CostAccountingErpDto dto = new CostAccountingErpDto();
            dtoList.add(dto);
            dto.setId(billDto.getId());
            dto.setLedgerId(new BigDecimal(this.getLedgerId(contractDTO.getOuId())));
            dto.setStatus("U");
            dto.setAccountingDate(DateUtils.formatDate(DateUtil.getEndingOfMonth(billDto.getGlDate())));
            dto.setActualFlag("A");
            dto.setUserJesourceName("PAM");
            dto.setUserJeCategoryName(".记账凭证");
            dto.setCurrencyCode(contractDTO.getCurrency());
            dto.setBathName(billDto.getCode());
            dto.setJournalName("PAM_日记账导入_合同对账单入账");

            if (infoDto.getSubject() == null) {
                throw new BizException(Code.ERROR, "科目为空");
            }
            String[] segments = infoDto.getSubject().split("\\.");
            dto.setSegment1(segments.length >= 1 ? segments[0] : null);
            dto.setSegment2(segments.length >= 2 ? segments[1] : null);
            dto.setSegment3(segments.length >= 3 ? segments[2] : null);
            dto.setSegment4(segments.length >= 4 ? segments[3] : null);
            dto.setSegment5(segments.length >= 5 ? segments[4] : null);
            dto.setSegment6(segments.length >= 6 ? segments[5] : null);
            dto.setSegment7(segments.length >= 7 ? segments[6] : null);

            //外币汇率信息必填
            if (!"CNY".equals(contractDTO.getCurrency())) {
                dto.setUserCurrencyConversionType(contractDTO.getConversionType());
                dto.setCurrencyConversionDate(DateUtils.formatDate(contractDTO.getConversionDate()));
                dto.setCurrencyConversionRate(contractDTO.getConversionRate());
            }

            BigDecimal conversionRate = Optional.ofNullable(contractDTO.getConversionRate()).orElse(BigDecimal.ONE);
            if ("借方".equals(infoDto.getSide())) {
                dto.setEnterEddr(billDto.getExcludeTaxAmount());
                dto.setAccountEddr(billDto.getExcludeTaxAmount().multiply(conversionRate));
            } else {
                dto.setEnterEdcr(billDto.getExcludeTaxAmount());
                dto.setAccountEdcr(billDto.getExcludeTaxAmount().multiply(conversionRate));
            }

            dto.setReference5("PAM_日记账导入_合同对账单入账_" + billDto.getCode());
            dto.setReference10("合同对账单入账_" + billDto.getCode());

            dto.setLineNumber(BigDecimal.valueOf(index++));
            dto.setSourcecode("PAM");
            dto.setSourceNum(billDto.getCode());

            dto.setEsbDataSource(BusinessTypeEnums.PURCHASE_CONTRACT_BILL.getCode());
            dto.setOperationType("CREATE");
        }
        return dtoList;
    }

    /**
     * 根据ouId查找ledgerId
     *
     * @param ouId 业务实体id
     */
    private Long getLedgerId(Long ouId) {
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setPamEnabled(0);
        query.setOperatingUnitId(ouId);
        PageInfo<OrganizationRelDto> orgDtoPage = organizationRelExtService.invokeApiList(query);
        return orgDtoPage.getList().get(0).getLedgerId();
    }

}
