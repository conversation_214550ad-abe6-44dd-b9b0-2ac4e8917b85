package com.midea.pam.ctc.service.config;

import com.midea.pam.ctc.job.ApplyContractUnitJob;
import com.midea.pam.ctc.job.BasicCacheInitJob;
import com.midea.pam.ctc.job.BusinessApplyRelSyncJob;
import com.midea.pam.ctc.job.ContractStatusSyncJob;
import com.midea.pam.ctc.job.CostCollectionDeleteJob;
import com.midea.pam.ctc.job.CostCollectionJob;
import com.midea.pam.ctc.job.DataIoContractJob;
import com.midea.pam.ctc.job.DeleteMrpSumJob;
import com.midea.pam.ctc.job.DifferenceShareDataDetailSyncJob;
import com.midea.pam.ctc.job.DifferenceShareErpDataSyncJob;
import com.midea.pam.ctc.job.GcebSyncDataJob;
import com.midea.pam.ctc.job.GeamSyncDataJob;
import com.midea.pam.ctc.job.IHRAttendDetailJob;
import com.midea.pam.ctc.job.InvoicePlanRemindJob;
import com.midea.pam.ctc.job.InvoiceSynchToEamJob;
import com.midea.pam.ctc.job.KsProjectIncomeCostPlanCreateJob;
import com.midea.pam.ctc.job.LaborCostHardWorkingSyncJob;
import com.midea.pam.ctc.job.MaterialActualCostSyncJob;
import com.midea.pam.ctc.job.MaterialCostSyncJob;
import com.midea.pam.ctc.job.MaterialGetAndReturnFormJob;
import com.midea.pam.ctc.job.MaterialGetAndReturnSyncJob;
import com.midea.pam.ctc.job.MaterialGetJob;
import com.midea.pam.ctc.job.MilepostDesignPlanDetailCostUpdateJob;
import com.midea.pam.ctc.job.MilepostDesignPlanNotPublishRequirementDeleteJob;
import com.midea.pam.ctc.job.MilepostDesignPlanNotPublishRequirementJob;
import com.midea.pam.ctc.job.MilepostDesignPlanNotPublishRequirementSendEmailJob;
import com.midea.pam.ctc.job.MilepostDesignPlanSubmitRecordSyncJob;
import com.midea.pam.ctc.job.MilepostNoticeJob;
import com.midea.pam.ctc.job.PaymentInoviceCancelErpTimeoutJob;
import com.midea.pam.ctc.job.PaymentInvoiceVoucherNumberJob;
import com.midea.pam.ctc.job.PaymentSyncDataJob;
import com.midea.pam.ctc.job.PaymentWriteOffCancelErpTimeoutJob;
import com.midea.pam.ctc.job.PaymentWriteOffDataJob;
import com.midea.pam.ctc.job.ProjectBudgetCostJob;
import com.midea.pam.ctc.job.ProjectIncomeCostPlanCreateJob;
import com.midea.pam.ctc.job.ProjectIncomeCostPlanDataRepairJob;
import com.midea.pam.ctc.job.ProjectSynchroJob;
import com.midea.pam.ctc.job.PullMaterialGetJob;
import com.midea.pam.ctc.job.PurchaseBpaPriceJob;
import com.midea.pam.ctc.job.PurchaseContractStatusSyncJob;
import com.midea.pam.ctc.job.PurchaseMaterialRequirementSyncJob;
import com.midea.pam.ctc.job.PurchaseOrderJob;
import com.midea.pam.ctc.job.PurchaseProgressJob;
import com.midea.pam.ctc.job.RDMSyncDataJob;
import com.midea.pam.ctc.job.RDMToWorkingHourJob;
import com.midea.pam.ctc.job.ReceiptClaimDrawbackToGcebJob;
import com.midea.pam.ctc.job.ReceiptClaimSyncJob;
import com.midea.pam.ctc.job.RefundApplyJob;
import com.midea.pam.ctc.job.ResendExecuteJob;
import com.midea.pam.ctc.job.SealAdministratorJob;
import com.midea.pam.ctc.job.TransMsgRetryShardingHandlerJob;
import com.midea.pam.ctc.job.VendorAslSyncJob;
import com.midea.pam.ctc.job.VendorPenaltySyncJob;
import com.midea.pam.ctc.job.WorkingHourApproveRemindJob;
import com.midea.pam.ctc.job.WorkingHourRemindJob;
import com.midea.pam.ctc.job.WorkingHourSubmitRemindJob;
import com.midea.pam.ctc.job.WriteOffInvoiceJob;
import com.midea.pam.ctc.job.WriteOffJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

//@ConditionalOnProperty(prefix = "xxl.job", name = "enabled", havingValue = "true")
@Configuration
//@EnableConfigurationProperties({XxlJobProperties.class})
public class XxlJobAutoConfiguration {
    private static final Logger LOGGER = LoggerFactory.getLogger(XxlJobAutoConfiguration.class);
    /*@Resource
    private XxlJobProperties properties;

    @Bean(initMethod = "start", destroyMethod = "destroy")
    public XxlJobExecutor xxlJobExecutor() {
        LOGGER.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobExecutor xxlJobExecutor = new XxlJobExecutor();
        xxlJobExecutor.setAdminAddresses(properties.getAdminAddress());
        xxlJobExecutor.setAppName(properties.getAppName());
        xxlJobExecutor.setIp(properties.getIp());
        xxlJobExecutor.setPort(properties.getExecutorPort());
        xxlJobExecutor.setAccessToken(properties.getAccessToken());
        xxlJobExecutor.setLogPath(properties.getLogPath());
        xxlJobExecutor.setLogRetentionDays(properties.getLogRetentionDays());
        return xxlJobExecutor;
    }*/

    @Bean
    public ProjectSynchroJob projectSynchroJob() {
        return new ProjectSynchroJob();
    }

    @Bean
    public InvoicePlanRemindJob invoicePlanRemindJob() {
        return new InvoicePlanRemindJob();
    }

    @Bean
    public RDMSyncDataJob rdmSyncDataJob() {
        return new RDMSyncDataJob();
    }

    @Bean
    public GeamSyncDataJob geamSyncDataJob() {
        return new GeamSyncDataJob();
    }

    @Bean
    public RDMToWorkingHourJob rdmToWorkingHourJob() {
        return new RDMToWorkingHourJob();
    }

    @Bean
    public IHRAttendDetailJob ihrAttendDetailJob() {
        return new IHRAttendDetailJob();
    }

    @Bean
    public PaymentSyncDataJob paymentSyncDataJob() {
        return new PaymentSyncDataJob();
    }

    @Bean
    public PaymentWriteOffDataJob paymentWriteOffDataJob() {
        return new PaymentWriteOffDataJob();
    }

    @Bean
    public ProjectBudgetCostJob projectBudgetCostJob() {
        return new ProjectBudgetCostJob();
    }

    @Bean
    public ContractStatusSyncJob contractStatusSyncJob() {
        return new ContractStatusSyncJob();
    }

    @Bean
    public ReceiptClaimSyncJob receiptClaimSyncJob() {
        return new ReceiptClaimSyncJob();
    }

    @Bean
    public ReceiptClaimDrawbackToGcebJob receiptClaimDrawbackToGcebJob() {
        return new ReceiptClaimDrawbackToGcebJob();
    }

    @Bean
    public ResendExecuteJob resendExecuteJob() {
        return new ResendExecuteJob();
    }

    @Bean
    public PurchaseProgressJob purchaseProgressJob() {
        return new PurchaseProgressJob();
    }

    @Bean
    public PurchaseOrderJob purchaseOrderJob() {
        return new PurchaseOrderJob();
    }

    @Bean
    public PurchaseContractStatusSyncJob purchaseContractStatusSyncJob() {
        return new PurchaseContractStatusSyncJob();
    }

    @Bean
    public GcebSyncDataJob gcebSyncDataJob() {
        return new GcebSyncDataJob();
    }

    @Bean
    CostCollectionJob costCollectionJob() {
        return new CostCollectionJob();
    }

    @Bean
    VendorPenaltySyncJob vendorPenaltySyncJob() {
        return new VendorPenaltySyncJob();
    }


    @Bean
    MaterialActualCostSyncJob materialActualCostSyncJob() {
        return new MaterialActualCostSyncJob();
    }

    @Bean
    VendorAslSyncJob vendorAslSyncJob() {
        return new VendorAslSyncJob();
    }

    @Bean
    PurchaseMaterialRequirementSyncJob purchaseMaterialRequirementSyncJob() {
        return new PurchaseMaterialRequirementSyncJob();
    }

    @Bean
    WorkingHourSubmitRemindJob workingHourSubmitRemindJob() {
        return new WorkingHourSubmitRemindJob();
    }

    @Bean
    WorkingHourApproveRemindJob workingHourApproveRemindJob() {
        return new WorkingHourApproveRemindJob();
    }

    @Bean
    MaterialCostSyncJob materialCostSyncJob() {
        return new MaterialCostSyncJob();
    }

    @Bean
    MaterialGetJob materialGetJob() {
        return new MaterialGetJob();
    }

    @Bean
    ProjectIncomeCostPlanDataRepairJob projectIncomeCostPlanDataRepairJob() {
        return new ProjectIncomeCostPlanDataRepairJob();
    }

    @Bean
    MilepostDesignPlanDetailCostUpdateJob milepostDesignPlanDetailCostUpdateJob() {
        return new MilepostDesignPlanDetailCostUpdateJob();
    }

    @Bean
    public DifferenceShareDataDetailSyncJob differenceShareDataDetailSyncJob() {
        return new DifferenceShareDataDetailSyncJob();
    }

    @Bean
    public DifferenceShareErpDataSyncJob differenceShareErpDataSyncJob() {
        return new DifferenceShareErpDataSyncJob();
    }

    @Bean
    public MilepostNoticeJob milepostNoticeJob() {
        return new MilepostNoticeJob();
    }

    @Bean
    public WorkingHourRemindJob workingHourRemindJob() {
        return new WorkingHourRemindJob();
    }

    @Bean
    public ProjectIncomeCostPlanCreateJob projectIncomeCostPlanCreateJob() {
        return new ProjectIncomeCostPlanCreateJob();
    }

    @Bean
    public WriteOffJob writeOffJob() {
        return new WriteOffJob();
    }

    @Bean
    public PurchaseBpaPriceJob purchaseBpaPriceJob() {
        return new PurchaseBpaPriceJob();
    }

    @Bean
    public RefundApplyJob refundApplyJob() {
        return new RefundApplyJob();
    }

    @Bean
    public ApplyContractUnitJob applyContractUnitJob() {
        return new ApplyContractUnitJob();
    }

    @Bean
    public SealAdministratorJob sealAdministratorJob() {
        return new SealAdministratorJob();
    }

    @Bean
    public WriteOffInvoiceJob writeOffInvoiceJob() {
        return new WriteOffInvoiceJob();
    }

    @Bean
    public InvoiceSynchToEamJob invoiceSynchToEamJob() {
        return new InvoiceSynchToEamJob();
    }

    @Bean
    public BusinessApplyRelSyncJob businessApplyRelSyncJob() {
        return new BusinessApplyRelSyncJob();
    }

    @Bean
    public PullMaterialGetJob pullMaterialGetJob() {
        return new PullMaterialGetJob();
    }

    @Bean
    public KsProjectIncomeCostPlanCreateJob ksProjectIncomeCostPlanCreateJob() {
        return new KsProjectIncomeCostPlanCreateJob();
    }

    @Bean
    public MaterialGetAndReturnSyncJob materialGetAndReturnSyncJob() {
        return new MaterialGetAndReturnSyncJob();
    }

    @Bean
    public MilepostDesignPlanSubmitRecordSyncJob milepostDesignPlanSubmitRecordSyncJob() {
        return new MilepostDesignPlanSubmitRecordSyncJob();
    }

    @Bean
    public MaterialGetAndReturnFormJob materialGetAndReturnFormJob() {
        return new MaterialGetAndReturnFormJob();
    }

    @Bean
    public PaymentInoviceCancelErpTimeoutJob paymentInoviceCancelErpTimeoutJob() {
        return new PaymentInoviceCancelErpTimeoutJob();
    }

    @Bean
    public PaymentWriteOffCancelErpTimeoutJob paymentWriteOffCancelErpTimeoutJob() {
        return new PaymentWriteOffCancelErpTimeoutJob();
    }

    @Bean
    public DeleteMrpSumJob deleteMrpSumJob() {
        return new DeleteMrpSumJob();
    }

    @Bean
    public BasicCacheInitJob basicCacheJob() {
        return new BasicCacheInitJob();
    }

    @Bean
    public DataIoContractJob dataIoContractJob() {
        return new DataIoContractJob();
    }

    @Bean
    public MilepostDesignPlanNotPublishRequirementJob milepostDesignPlanNotPublishRequirementJob() {
        return new MilepostDesignPlanNotPublishRequirementJob();
    }

    @Bean
    public MilepostDesignPlanNotPublishRequirementSendEmailJob milepostDesignPlanNotPublishRequirementSendEmailJob() {
        return new MilepostDesignPlanNotPublishRequirementSendEmailJob();
    }

    @Bean
    public MilepostDesignPlanNotPublishRequirementDeleteJob milepostDesignPlanNotPublishRequirementDeleteJob() {
        return new MilepostDesignPlanNotPublishRequirementDeleteJob();
    }

    @Bean
    public TransMsgRetryShardingHandlerJob transMsgRetryShardingHandlerJob() {
        return new TransMsgRetryShardingHandlerJob();
    }
    @Bean
    public CostCollectionDeleteJob costCollectionDeleteJob() {
        return new CostCollectionDeleteJob();
    }

    @Bean
    public PaymentInvoiceVoucherNumberJob paymentInvoiceVoucherNumberJob(){
        return new PaymentInvoiceVoucherNumberJob();
    }

    @Bean
    public LaborCostHardWorkingSyncJob laborCostHardWorkingSyncJob() {
        return new LaborCostHardWorkingSyncJob();
    }
}

