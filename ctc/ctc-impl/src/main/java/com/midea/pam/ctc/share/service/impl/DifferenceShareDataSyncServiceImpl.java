package com.midea.pam.ctc.share.service.impl;

import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.GlPeriod;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.ctc.entity.DifferenceShareDataSync;
import com.midea.pam.common.ctc.entity.DifferenceShareDataSyncExample;
import com.midea.pam.common.enums.DifferenceShareDataSyncStatus;
import com.midea.pam.common.enums.GlPeriodType;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.mapper.DifferenceShareDataSyncMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.share.service.DifferenceShareDataSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

public class DifferenceShareDataSyncServiceImpl implements DifferenceShareDataSyncService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private DifferenceShareDataSyncMapper differenceShareDataSyncMapper;

    @Resource
    private BasedataExtService basedataExtService;

    @Override
    public long countByExample(DifferenceShareDataSyncExample example) {
        return differenceShareDataSyncMapper.countByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return differenceShareDataSyncMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(DifferenceShareDataSync record) {
        return differenceShareDataSyncMapper.insert(record);
    }

    @Override
    public int insertSelective(DifferenceShareDataSync record) {
        return differenceShareDataSyncMapper.insertSelective(record);
    }

    @Override
    public List<DifferenceShareDataSync> selectByExample(DifferenceShareDataSyncExample example) {
        return differenceShareDataSyncMapper.selectByExample(example);
    }

    @Override
    public DifferenceShareDataSync selectByPrimaryKey(Long id) {
        return differenceShareDataSyncMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(DifferenceShareDataSync record) {
        return differenceShareDataSyncMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(DifferenceShareDataSync record) {
        return differenceShareDataSyncMapper.updateByPrimaryKey(record);
    }

    @Override
    public DifferenceShareDataSync getByOuIdAndPeriodType(Long ouId, String glPeriod, Long periodType) {
        DifferenceShareDataSyncExample example = new DifferenceShareDataSyncExample();
        DifferenceShareDataSyncExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andOuIdEqualTo(ouId);
        criteria.andGlPeriodEqualTo(glPeriod);
        criteria.andPeriodTypeEqualTo(periodType);

        List<DifferenceShareDataSync> differenceShareDataSyncsList = this.selectByExample(example);
        if (ListUtils.isNotEmpty(differenceShareDataSyncsList)) {
            return differenceShareDataSyncsList.get(0);
        }
        return null;
    }

    @Override
    public String getNeedSyncGlPeriod(Long ouId) {
        DifferenceShareDataSyncExample example = new DifferenceShareDataSyncExample();
        DifferenceShareDataSyncExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andOuIdEqualTo(ouId);

        example.setOrderByClause(" gl_period desc");

        List<DifferenceShareDataSync> differenceShareDataSyncsList = this.selectByExample(example);
        if (ListUtils.isNotEmpty(differenceShareDataSyncsList)) {
            OrganizationRelQuery query = new OrganizationRelQuery();
            query.setOperatingUnitId(ouId);
            List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
            OrganizationRelDto organizationRelDto = null;
            if (ListUtils.isNotEmpty(organizationRelList)) {
                organizationRelDto = organizationRelList.get(0);
            }

            String glPeriod = differenceShareDataSyncsList.get(0).getGlPeriod();
            // 期间是否全部同步成功
            Boolean organizationCompleteFlag = false;

            // 库存期间
            DifferenceShareDataSync differenceShareDataSync = this.getByOuIdAndPeriodType(ouId, glPeriod, GlPeriodType.ORGANIZATION_PERIOD.getCode());
            if (differenceShareDataSync != null) {
                Integer syncStatus = differenceShareDataSync.getSyncStatus();
                if (DifferenceShareDataSyncStatus.SUCCESS.getCode() == syncStatus) {
                    organizationCompleteFlag = true;
                }
            } else {
                if (organizationRelDto != null) {
                    Long organizationId = organizationRelDto.getOrganizationId();
                    List<GlPeriodDto> glPeriodList = basedataExtService.getGlPeriodByOrganizationId(organizationId, GlPeriodType.ORGANIZATION_PERIOD.getCode().toString(), glPeriod);
                    if (ListUtils.isNotEmpty(glPeriodList)) {
                        // 库存期间关闭，新增同步记录
                        if (close(glPeriodList.get(0))) {
                            logger.info("库存期间关闭，新增同步记录, {}, {}", ouId, glPeriod);
                            save(ouId, glPeriod, GlPeriodType.ORGANIZATION_PERIOD.getCode());
                        }
                    }
                }
            }

            Boolean paymentCompleteFlag = false;

            // 应付期间
            differenceShareDataSync = this.getByOuIdAndPeriodType(ouId, glPeriod, GlPeriodType.PAYMENT_PERIOD.getCode());
            if (differenceShareDataSync != null) {
                Integer syncStatus = differenceShareDataSync.getSyncStatus();
                if (DifferenceShareDataSyncStatus.SUCCESS.getCode() == syncStatus) {
                    paymentCompleteFlag = true;
                } else {
                    paymentCompleteFlag = false;
                }
            } else {
                if (organizationRelDto != null) {
                    Long ledgerId = organizationRelDto.getLedgerId();
                    List<GlPeriodDto> glPeriodList = basedataExtService.getGlPeriod(ledgerId, GlPeriodType.PAYMENT_PERIOD.getCode().toString(), glPeriod);
                    if (ListUtils.isNotEmpty(glPeriodList)) {
                        // 应付期间关闭，新增同步记录
                        if (close(glPeriodList.get(0))) {
                            logger.info("应付期间关闭，新增同步记录, {},{}", ouId, glPeriod);
                            save(ouId, glPeriod, GlPeriodType.PAYMENT_PERIOD.getCode());
                        }
                    }
                }
            }

            // 全部同步成功，则获取下一个会计期间的是否关闭
            if (organizationCompleteFlag && paymentCompleteFlag) {
                // 下一期间
                glPeriod = getNextPeriod(glPeriod);

                logger.info("全部同步成功，则获取下一个会计期间:{} 是否关闭", glPeriod);

                if (organizationRelDto == null) {
                    return glPeriod;
                }

                Long ledgerId = organizationRelDto.getLedgerId();
                Long organizationId = organizationRelDto.getOrganizationId();

                // 同步下一个会计期间
                createNextPeriod(ouId, glPeriod, ledgerId, organizationId);
            }
            return glPeriod;
        }

        return null;
    }

    /**
     * 判断会计期间是否关闭
     */
    private boolean close(GlPeriod glPeriod) {
        String closingStatus = glPeriod.getClosingStatus();
        String showStatus = glPeriod.getShowStatus();
        String preShowStatus = glPeriod.getPreShowStatus();
        String preClosingStatus = glPeriod.getPreClosingStatus();

        boolean closeFlag = "C".equals(closingStatus) || "Closed".equals(showStatus) ||
                "C".equals(preClosingStatus) || "Closed".equals(preShowStatus);
        return closeFlag;
    }

    private String getNextPeriod(String glPeriod) {
        String nextGlPeriod;

        final String[] yearAndMonth = glPeriod.split("-");
        int year = Integer.parseInt(yearAndMonth[0]);
        int month = Integer.parseInt(yearAndMonth[1]);

        if (month == 12) {
            year = year + 1;
            month = 1;
        } else {
            month = month + 1;
        }

        if (month < 10) {
            nextGlPeriod = year + "-0" + month;
        } else {
            nextGlPeriod = year + "-" + month;
        }
        return nextGlPeriod;
    }

    private void createNextPeriod(Long ouId, String glPeriod, Long ledgerId, Long organizationId) {
        // 库存期间按照库存组织查询
        List<GlPeriodDto> glPeriodList = basedataExtService.getGlPeriodByOrganizationId(organizationId, GlPeriodType.ORGANIZATION_PERIOD.getCode().toString(), glPeriod);
        if (ListUtils.isNotEmpty(glPeriodList)) {
            // 库存期间关闭，新增同步记录
            if (close(glPeriodList.get(0))) {
                save(ouId, glPeriod, GlPeriodType.ORGANIZATION_PERIOD.getCode());
            }
        }

        glPeriodList = basedataExtService.getGlPeriod(ledgerId, GlPeriodType.PAYMENT_PERIOD.getCode().toString(), glPeriod);
        if (ListUtils.isNotEmpty(glPeriodList)) {
            // 库存期间关闭，新增同步记录
            if (close(glPeriodList.get(0))) {
                save(ouId, glPeriod, GlPeriodType.PAYMENT_PERIOD.getCode());
            }
        }
    }

    private DifferenceShareDataSync save(Long ouId, String glPeriod, Long periodType) {
        DifferenceShareDataSync record = new DifferenceShareDataSync();
        record.setGlPeriod(glPeriod);
        record.setOuId(ouId);
        record.setPeriodType(periodType);
        record.setDeletedFlag(Boolean.FALSE);
        record.setSyncStatus(DifferenceShareDataSyncStatus.TODO.getCode());
        this.insertSelective(record);
        return record;
    }
}
