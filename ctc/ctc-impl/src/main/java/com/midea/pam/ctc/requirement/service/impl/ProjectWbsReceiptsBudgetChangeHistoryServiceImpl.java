package com.midea.pam.ctc.requirement.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsBudgetChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetExample;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.ProjectWbsReceiptsBudgetDemandTypeEnums;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsBudgetChangeHistoryService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ProjectWbsReceiptsBudgetChangeHistoryServiceImpl implements ProjectWbsReceiptsBudgetChangeHistoryService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectWbsReceiptsBudgetChangeHistoryServiceImpl.class);

    @Resource
    private ProjectWbsReceiptsBudgetChangeHistoryMapper projectWbsReceiptsBudgetChangeHistoryMapper;

    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;

    @Resource
    private ProjectWbsReceiptsBudgetMapper projectWbsReceiptsBudgetMapper;
    @Override
    public void createReceiptsBudgetChangeHistory(Long projectWbsChangeReceiptsId, Long projectWbsPublishReceiptsId, Long userBy,
                                                  List<String> alreadyCreatePublishReceiptsIdAndWbsSummaryCode, String wbsSummaryCode,
                                                  Set<Boolean> extIsSet) {
        logger.info("createReceiptsBudgetChangeHistory方法的projectWbsChangeReceiptsId是:{},projectWbsPublishReceiptsId是:{},userBy:{}," +
                        "alreadyCreatePublishReceiptsIdAndWbsSummaryCode是:{},wbsSummaryCode:{},extIsSet:{}",
                projectWbsChangeReceiptsId, projectWbsPublishReceiptsId, userBy,
                JSON.toJSONString(alreadyCreatePublishReceiptsIdAndWbsSummaryCode), wbsSummaryCode, JSON.toJSONString(extIsSet));
        if (CollectionUtils.isEmpty(extIsSet)) {
            return;
        }
        for (Boolean extIs : extIsSet) {
            if (alreadyCreatePublishReceiptsIdAndWbsSummaryCode.contains(getPublishReceiptsIdAndWbsSummaryCodeAndExtIs(projectWbsPublishReceiptsId,
                    wbsSummaryCode, extIs))) {
                return;
            }
        }

        ProjectWbsReceiptsBudgetExample budgetExample = new ProjectWbsReceiptsBudgetExample();
        // todo 踩坑，一定要加 deletedFlag =0 ，否则重复添加数据
        budgetExample.createCriteria().andProjectWbsReceiptsIdEqualTo(projectWbsPublishReceiptsId)
                .andWbsSummaryCodeEqualTo(wbsSummaryCode).andDeletedFlagEqualTo(false);
        List<ProjectWbsReceiptsBudget> budgetList = projectWbsReceiptsBudgetMapper.selectByExample(budgetExample);
        Map<Integer, ProjectWbsReceiptsBudget> budgetMap = budgetList.stream().collect(Collectors.toMap(ProjectWbsReceiptsBudget::getDemandType,
                Function.identity()));
        if (extIsSet.contains(Boolean.FALSE) && budgetMap.containsKey(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode())
                && extIsSet.contains(Boolean.TRUE) && budgetMap.containsKey(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode())) {
            // 外包和非外包都有
            ProjectWbsReceiptsBudget demandBudget = budgetMap.get(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode());
            ProjectWbsReceiptsBudget outsourceBudget = budgetMap.get(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode());
            ProjectWbsReceiptsBudgetChangeHistory changeHistory = new ProjectWbsReceiptsBudgetChangeHistory();
            changeHistory.setProjectWbsChangeReceiptsId(projectWbsChangeReceiptsId);
            changeHistory.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
            changeHistory.setWbsSummaryCode(demandBudget.getWbsSummaryCode());
            changeHistory.setActivityCode(demandBudget.getActivityCode());
            changeHistory.setBudgetOccupiedAmountBefore(Optional.ofNullable(demandBudget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO)
                    .add(Optional.ofNullable(outsourceBudget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO)));
            changeHistory.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode());
            changeHistory.setParentid(-1L);
            changeHistory.setDeletedFlag(false);
            changeHistory.setVersion(1L);
            projectWbsReceiptsBudgetChangeHistoryMapper.insertSelective(changeHistory);

            ProjectWbsReceiptsBudgetChangeHistory sonChangeHistory1 = new ProjectWbsReceiptsBudgetChangeHistory();
            sonChangeHistory1.setProjectWbsChangeReceiptsId(projectWbsChangeReceiptsId);
            sonChangeHistory1.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
            sonChangeHistory1.setWbsSummaryCode(demandBudget.getWbsSummaryCode());
            sonChangeHistory1.setActivityCode(demandBudget.getActivityCode());
            sonChangeHistory1.setBudgetOccupiedAmountBefore(Optional.ofNullable(demandBudget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO));
            sonChangeHistory1.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode());
            sonChangeHistory1.setParentid(changeHistory.getId());
            sonChangeHistory1.setDeletedFlag(false);
            sonChangeHistory1.setVersion(1L);
            projectWbsReceiptsBudgetChangeHistoryMapper.insertSelective(sonChangeHistory1);

            ProjectWbsReceiptsBudgetChangeHistory sonChangeHistory2 = new ProjectWbsReceiptsBudgetChangeHistory();
            sonChangeHistory2.setProjectWbsChangeReceiptsId(projectWbsChangeReceiptsId);
            sonChangeHistory2.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
            sonChangeHistory2.setWbsSummaryCode(outsourceBudget.getWbsSummaryCode());
            sonChangeHistory2.setActivityCode(outsourceBudget.getActivityCode());
            sonChangeHistory2.setBudgetOccupiedAmountBefore(Optional.ofNullable(outsourceBudget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO));
            sonChangeHistory2.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode());
            sonChangeHistory2.setParentid(changeHistory.getId());
            sonChangeHistory2.setDeletedFlag(false);
            sonChangeHistory2.setVersion(1L);
            projectWbsReceiptsBudgetChangeHistoryMapper.insertSelective(sonChangeHistory2);

            alreadyCreatePublishReceiptsIdAndWbsSummaryCode.add(getPublishReceiptsIdAndWbsSummaryCodeAndExtIs(projectWbsPublishReceiptsId,
                    wbsSummaryCode, true));
            alreadyCreatePublishReceiptsIdAndWbsSummaryCode.add(getPublishReceiptsIdAndWbsSummaryCodeAndExtIs(projectWbsPublishReceiptsId,
                    wbsSummaryCode, false));
        } else if (extIsSet.contains(Boolean.FALSE) && budgetMap.containsKey(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode())) {
            // 只有非外包都有
            ProjectWbsReceiptsBudget demandBudget = budgetMap.get(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode());
            ProjectWbsReceiptsBudgetChangeHistory changeHistory = new ProjectWbsReceiptsBudgetChangeHistory();
            changeHistory.setProjectWbsChangeReceiptsId(projectWbsChangeReceiptsId);
            changeHistory.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
            changeHistory.setWbsSummaryCode(demandBudget.getWbsSummaryCode());
            changeHistory.setActivityCode(demandBudget.getActivityCode());
            changeHistory.setBudgetOccupiedAmountBefore(Optional.ofNullable(demandBudget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO));
            changeHistory.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode());
            changeHistory.setParentid(-1L);
            changeHistory.setDeletedFlag(false);
            changeHistory.setVersion(1L);
            projectWbsReceiptsBudgetChangeHistoryMapper.insertSelective(changeHistory);

            ProjectWbsReceiptsBudgetChangeHistory sonChangeHistory1 = new ProjectWbsReceiptsBudgetChangeHistory();
            sonChangeHistory1.setProjectWbsChangeReceiptsId(projectWbsChangeReceiptsId);
            sonChangeHistory1.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
            sonChangeHistory1.setWbsSummaryCode(demandBudget.getWbsSummaryCode());
            sonChangeHistory1.setActivityCode(demandBudget.getActivityCode());
            sonChangeHistory1.setBudgetOccupiedAmountBefore(Optional.ofNullable(demandBudget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO));
            sonChangeHistory1.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode());
            sonChangeHistory1.setParentid(changeHistory.getId());
            sonChangeHistory1.setDeletedFlag(false);
            sonChangeHistory1.setVersion(1L);
            projectWbsReceiptsBudgetChangeHistoryMapper.insertSelective(sonChangeHistory1);

            alreadyCreatePublishReceiptsIdAndWbsSummaryCode.add(getPublishReceiptsIdAndWbsSummaryCodeAndExtIs(projectWbsPublishReceiptsId,
                    wbsSummaryCode, false));
        } else if (extIsSet.contains(Boolean.TRUE) && budgetMap.containsKey(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode())) {
            //只有外包
            ProjectWbsReceiptsBudget outsourceBudget = budgetMap.get(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode());
            ProjectWbsReceiptsBudgetChangeHistory changeHistory = new ProjectWbsReceiptsBudgetChangeHistory();
            changeHistory.setProjectWbsChangeReceiptsId(projectWbsChangeReceiptsId);
            changeHistory.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
            changeHistory.setWbsSummaryCode(outsourceBudget.getWbsSummaryCode());
            changeHistory.setActivityCode(outsourceBudget.getActivityCode());
            changeHistory.setBudgetOccupiedAmountBefore(Optional.ofNullable(outsourceBudget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO));
            changeHistory.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode());
            changeHistory.setParentid(-1L);
            changeHistory.setDeletedFlag(false);
            changeHistory.setVersion(1L);
            projectWbsReceiptsBudgetChangeHistoryMapper.insertSelective(changeHistory);

            ProjectWbsReceiptsBudgetChangeHistory sonChangeHistory2 = new ProjectWbsReceiptsBudgetChangeHistory();
            sonChangeHistory2.setProjectWbsChangeReceiptsId(projectWbsChangeReceiptsId);
            sonChangeHistory2.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
            sonChangeHistory2.setWbsSummaryCode(outsourceBudget.getWbsSummaryCode());
            sonChangeHistory2.setActivityCode(outsourceBudget.getActivityCode());
            sonChangeHistory2.setBudgetOccupiedAmountBefore(Optional.ofNullable(outsourceBudget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO));
            sonChangeHistory2.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode());
            sonChangeHistory2.setParentid(changeHistory.getId());
            sonChangeHistory2.setDeletedFlag(false);
            sonChangeHistory2.setVersion(1L);
            projectWbsReceiptsBudgetChangeHistoryMapper.insertSelective(sonChangeHistory2);

            alreadyCreatePublishReceiptsIdAndWbsSummaryCode.add(getPublishReceiptsIdAndWbsSummaryCodeAndExtIs(projectWbsPublishReceiptsId,
                    wbsSummaryCode, true));
        }
    }

    // 将projectWbsPublishReceiptsId和wbsSummaryCode以及extIs组合成一个字符串，用于判断是否已经创建过
    private String getPublishReceiptsIdAndWbsSummaryCodeAndExtIs(Long projectWbsPublishReceiptsId, String wbsSummaryCode, Boolean extIs) {
        return projectWbsPublishReceiptsId + "_" + wbsSummaryCode + "_"
                + (Boolean.TRUE.equals(extIs) ? "1" : (Boolean.FALSE.equals(extIs) ? 0 : "null"));
    }

    @Override
    public List<ProjectWbsReceiptsBudgetChangeHistoryDto> findReceiptsBudgetHistories(ProjectWbsReceiptsDto receipts, Long projectId) {
        ProjectWbsReceiptsBudgetChangeHistoryExample example = new ProjectWbsReceiptsBudgetChangeHistoryExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andProjectWbsChangeReceiptsIdEqualTo(receipts.getId());
        List<ProjectWbsReceiptsBudgetChangeHistory> budgetChangeHistoryList = projectWbsReceiptsBudgetChangeHistoryMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(budgetChangeHistoryList)) {
            return new ArrayList<>();
        }

        logger.info("findReceiptsBudgetHistories的budgetChangeHistoryList：{}", JSON.toJSONString(budgetChangeHistoryList));
        Map<Long, List<ProjectWbsReceiptsBudgetChangeHistory>> budgetChangeHistoryMap = budgetChangeHistoryList.stream()
                .collect(Collectors.groupingBy(e -> Objects.isNull(e.getParentid()) ? -1 : e.getParentid()));
        List<ProjectWbsReceiptsBudgetChangeHistoryDto> parentDtoList = new ArrayList<>();
        // 找出所有parent
        for (ProjectWbsReceiptsBudgetChangeHistory budget : budgetChangeHistoryList) {
            if (Objects.isNull(budget.getParentid()) || budget.getParentid() == -1) {
                ProjectWbsReceiptsBudgetChangeHistoryDto dto = BeanConverter.copy(budget, ProjectWbsReceiptsBudgetChangeHistoryDto.class);
                dto.setPublishRequirementCode(projectWbsReceiptsService.getRequirementCodeByReceiptsId(dto.getProjectWbsPublishReceiptsId()));
                parentDtoList.add(dto);
            }
        }
        // 设置child
        for (ProjectWbsReceiptsBudgetChangeHistoryDto parent : parentDtoList) {
            List<ProjectWbsReceiptsBudgetChangeHistory> childs = budgetChangeHistoryMap.get(parent.getId());
            if (CollectionUtils.isNotEmpty(childs)) {
                List<ProjectWbsReceiptsBudgetChangeHistoryDto> childDtoList = BeanConverter.copy(childs,
                        ProjectWbsReceiptsBudgetChangeHistoryDto.class);
                for (ProjectWbsReceiptsBudgetChangeHistoryDto childDto : childDtoList) {
                    childDto.setPublishRequirementCode(projectWbsReceiptsService.getRequirementCodeByReceiptsId(childDto.getProjectWbsPublishReceiptsId()));
                }
                parent.addChilds(childDtoList);
            }
        }

        return parentDtoList;
    }

    @Override
    public List<ProjectWbsReceiptsBudgetChangeHistory> selectByExample(ProjectWbsReceiptsBudgetChangeHistoryExample example) {
        return projectWbsReceiptsBudgetChangeHistoryMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveReceiptsBudgetChangeHistory(Long receiptsId, List<ProjectWbsReceiptsBudgetChangeHistoryDto> budgetChangeHistories) {
        logger.info("saveReceiptsBudgetChangeHistory的receiptsId：{}，projectWbsReceiptsBudgetChangeHistories：{}",
                receiptsId, JSONObject.toJSONString(budgetChangeHistories));
        if (CollectionUtils.isEmpty(budgetChangeHistories)) {
            return;
        }

        for (ProjectWbsReceiptsBudgetChangeHistoryDto changeHistoryDto : budgetChangeHistories) {
            List<ProjectWbsReceiptsBudgetChangeHistoryDto> sumBudgetChangeHistoryDto = new ArrayList<>();
            for (ProjectWbsReceiptsBudgetChangeHistoryDto child : changeHistoryDto.getChilds()) {
                if (ListUtils.isNotEmpty(child.getChilds())) {
                    sumBudgetChangeHistoryDto.addAll(child.getChilds());
                }
            }
            Map<Long, List<ProjectWbsReceiptsBudgetChangeHistoryDto>> sumBudgetChangeHistoryMap =
                    sumBudgetChangeHistoryDto.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsBudgetChangeHistoryDto::getProjectWbsPublishReceiptsId));
            if (MapUtils.isEmpty(sumBudgetChangeHistoryMap)) {
                continue;
            }

            logger.info("saveReceiptsBudgetChangeHistory的changeHistoryDto：{}，sumBudgetChangeHistoryMap：{}",
                    JSONObject.toJSONString(changeHistoryDto), JSONObject.toJSONString(sumBudgetChangeHistoryMap));
            for (Long key : sumBudgetChangeHistoryMap.keySet()) {
                List<ProjectWbsReceiptsBudgetChangeHistoryDto> childBudgetChangeHistoryDtos = sumBudgetChangeHistoryMap.get(key);
                //先更新子节点
                Set<String> wbsSummaryCodeSet = new HashSet<>();
                for (ProjectWbsReceiptsBudgetChangeHistoryDto childBudget : childBudgetChangeHistoryDtos) {
                    wbsSummaryCodeSet.add(childBudget.getWbsSummaryCode());
                    ProjectWbsReceiptsBudgetChangeHistory forSaveEntity = new ProjectWbsReceiptsBudgetChangeHistory();
                    forSaveEntity.setId(childBudget.getId());
                    forSaveEntity.setBudgetOccupiedAmountAfter(childBudget.getBudgetOccupiedAmountAfter());
                    forSaveEntity.setUpdateBy(SystemContext.getUserId());
                    forSaveEntity.setUpdateAt(new Date());
                    projectWbsReceiptsBudgetChangeHistoryMapper.updateByPrimaryKeySelective(forSaveEntity);
                }

                //再更新父节点
                for (String wbsSummaryCode : wbsSummaryCodeSet) {
                    ProjectWbsReceiptsBudgetExample budgetExample = new ProjectWbsReceiptsBudgetExample();
                    budgetExample.createCriteria().andProjectWbsReceiptsIdEqualTo(key)
                            .andWbsSummaryCodeEqualTo(wbsSummaryCode).andDeletedFlagEqualTo(Boolean.FALSE);
                    List<ProjectWbsReceiptsBudget> budgetList = projectWbsReceiptsBudgetMapper.selectByExample(budgetExample);
                    Map<Integer, BigDecimal> budgetMap = budgetList.stream()
                            .collect(Collectors.toMap(ProjectWbsReceiptsBudget::getDemandType, ProjectWbsReceiptsBudget::getBudgetOccupiedAmount));

                    ProjectWbsReceiptsBudgetChangeHistoryExample changeHistoryExample = new ProjectWbsReceiptsBudgetChangeHistoryExample();
                    changeHistoryExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                            .andProjectWbsChangeReceiptsIdEqualTo(receiptsId)
                            .andProjectWbsPublishReceiptsIdEqualTo(key)
                            .andWbsSummaryCodeEqualTo(wbsSummaryCode);
                    List<ProjectWbsReceiptsBudgetChangeHistory> changeHistoryList =
                            projectWbsReceiptsBudgetChangeHistoryMapper.selectByExample(changeHistoryExample);
                    BigDecimal allBudgetOccupiedAmountBefore =
                            Optional.ofNullable(budgetMap.get(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode())).orElse(BigDecimal.ZERO);
                    BigDecimal allBudgetOccupiedAmountAfter = BigDecimal.ZERO;
                    Long parentBudgetChangeHistoryId = null;
                    for (ProjectWbsReceiptsBudgetDemandTypeEnums enums : ProjectWbsReceiptsBudgetDemandTypeEnums.values()) {
                        Integer code = enums.getCode();
                        if (changeHistoryList.stream().anyMatch(changeHistory -> Objects.equals(changeHistory.getDemandType(), code))) {
                            for (ProjectWbsReceiptsBudgetChangeHistory changeHistory : changeHistoryList) {
                                Integer demandType = changeHistory.getDemandType();
                                if (!Objects.equals(code, demandType)) {
                                    continue;
                                }

                                if (Objects.equals(demandType, ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode())) {
                                    parentBudgetChangeHistoryId = changeHistory.getId();
                                } else {
                                    allBudgetOccupiedAmountAfter = allBudgetOccupiedAmountAfter
                                            .add(Optional.ofNullable(changeHistory.getBudgetOccupiedAmountAfter()).orElse(BigDecimal.ZERO));
                                }
                            }
                        } else if (!Objects.equals(code, ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode())) {
                            //变更前的预算
                            BigDecimal originalAmount = Optional.ofNullable(budgetMap.get(code)).orElse(BigDecimal.ZERO);
                            allBudgetOccupiedAmountAfter = allBudgetOccupiedAmountAfter.add(originalAmount);
                        }
                    }

                    if (null == parentBudgetChangeHistoryId) {
                        ProjectWbsReceiptsBudgetChangeHistory forSaveEntity = new ProjectWbsReceiptsBudgetChangeHistory();
                        forSaveEntity.setId(null);
                        forSaveEntity.setProjectWbsChangeReceiptsId(receiptsId);
                        forSaveEntity.setProjectWbsPublishReceiptsId(key);
                        forSaveEntity.setWbsSummaryCode(wbsSummaryCode);
                        forSaveEntity.setBudgetOccupiedAmountBefore(allBudgetOccupiedAmountBefore);
                        forSaveEntity.setBudgetOccupiedAmountAfter(allBudgetOccupiedAmountAfter);
                        forSaveEntity.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode());
                        forSaveEntity.setParentid(-1L);
                        forSaveEntity.setCreateBy(SystemContext.getUserId());
                        forSaveEntity.setCreateAt(new Date());
                        forSaveEntity.setDeletedFlag(false);
                        forSaveEntity.setVersion(1L);
                        projectWbsReceiptsBudgetChangeHistoryMapper.insertSelective(forSaveEntity);
                    } else {
                        ProjectWbsReceiptsBudgetChangeHistory forSaveEntity = new ProjectWbsReceiptsBudgetChangeHistory();
                        forSaveEntity.setId(parentBudgetChangeHistoryId);
                        forSaveEntity.setBudgetOccupiedAmountAfter(allBudgetOccupiedAmountAfter);
                        forSaveEntity.setUpdateBy(SystemContext.getUserId());
                        forSaveEntity.setUpdateAt(new Date());
                        projectWbsReceiptsBudgetChangeHistoryMapper.updateByPrimaryKeySelective(forSaveEntity);
                    }
                }
            }
        }
    }

    @Override
    public List<ProjectWbsReceiptsDto> selectChangeReceiptsByPublishReceiptsId(Long publishReceiptsId) {
        ProjectWbsReceiptsBudgetChangeHistoryExample example = new ProjectWbsReceiptsBudgetChangeHistoryExample();
        example.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andProjectWbsPublishReceiptsIdEqualTo(publishReceiptsId);
        List<ProjectWbsReceiptsBudgetChangeHistory> projectWbsReceiptsBudgetChangeHistories =
                projectWbsReceiptsBudgetChangeHistoryMapper.selectByExample(example);
        List<Long> changeReceiptsIds =
                projectWbsReceiptsBudgetChangeHistories.stream().map(ProjectWbsReceiptsBudgetChangeHistory::getProjectWbsChangeReceiptsId).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(changeReceiptsIds)) {
            return projectWbsReceiptsService.selectByIds(changeReceiptsIds);
        } else {
            return new ArrayList<>();
        }
    }
}
