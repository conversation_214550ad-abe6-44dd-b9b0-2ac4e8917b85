package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.dto.TaxInfoDto;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.VendorSiteBankForDisplay;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.crm.entity.Customer;
import com.midea.pam.common.crm.entity.CustomerExample;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDetailDto;
import com.midea.pam.common.ctc.dto.PaymentApplyDetailsDto;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.PaymentInvoceCheckCancelDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailErpDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceErpDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceFreezeRecordDto;
import com.midea.pam.common.ctc.dto.PaymentWriteOffRecordDetailsDto;
import com.midea.pam.common.ctc.dto.PunishmentInvoiceVO;
import com.midea.pam.common.ctc.dto.PurchaseContractDTO;
import com.midea.pam.common.ctc.dto.ReceiptClaimInvoiceRelDto;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.entity.BusiSceneNonSale;
import com.midea.pam.common.ctc.entity.BusiSceneNonSaleExample;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.CostCollection;
import com.midea.pam.common.ctc.entity.GscPaymentInvoice;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetailSplit;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetails;
import com.midea.pam.common.ctc.entity.InvoiceApplyHeader;
import com.midea.pam.common.ctc.entity.InvoicePlanDetail;
import com.midea.pam.common.ctc.entity.InvoicePlanDetailExample;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.InvoiceReceivableExample;
import com.midea.pam.common.ctc.entity.MaterialOutsourceCostDetail;
import com.midea.pam.common.ctc.entity.MaterialOutsourcingContractConfig;
import com.midea.pam.common.ctc.entity.MaterialOutsourcingContractConfigExample;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentApplyDetailRel;
import com.midea.pam.common.ctc.entity.PaymentApplyExample;
import com.midea.pam.common.ctc.entity.PaymentApplyInvoiceRel;
import com.midea.pam.common.ctc.entity.PaymentApplyInvoiceRelExample;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetail;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetailExample;
import com.midea.pam.common.ctc.entity.PaymentInvoiceExample;
import com.midea.pam.common.ctc.entity.PaymentInvoiceFreezeRecord;
import com.midea.pam.common.ctc.entity.PaymentInvoiceFreezeRecordExample;
import com.midea.pam.common.ctc.entity.PaymentPlan;
import com.midea.pam.common.ctc.entity.PaymentRecord;
import com.midea.pam.common.ctc.entity.PaymentRecordExample;
import com.midea.pam.common.ctc.entity.PaymentWriteOffRecord;
import com.midea.pam.common.ctc.entity.PaymentWriteOffRecordExample;
import com.midea.pam.common.ctc.entity.PositiveNegativeInvoiceRecord;
import com.midea.pam.common.ctc.entity.PositiveNegativeInvoiceRecordExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractPunishment;
import com.midea.pam.common.ctc.entity.PurchaseContractPunishmentConfig;
import com.midea.pam.common.ctc.entity.PurchaseContractPunishmentExample;
import com.midea.pam.common.ctc.entity.ReceiptClaim;
import com.midea.pam.common.ctc.entity.ReceiptClaimContractRel;
import com.midea.pam.common.ctc.entity.ReceiptClaimDetail;
import com.midea.pam.common.ctc.entity.ReceiptClaimDetailExample;
import com.midea.pam.common.ctc.entity.ReceiptClaimInvoiceRel;
import com.midea.pam.common.ctc.entity.ReceiptClaimInvoiceRelExample;
import com.midea.pam.common.ctc.entity.ReceiptPlan;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetail;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailExample;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.excelVo.InvoiceApplyExcelVo;
import com.midea.pam.common.ctc.excelVo.InvoiceReceivableExcelVo;
import com.midea.pam.common.ctc.excelVo.PaymentApplyExcelVo;
import com.midea.pam.common.ctc.excelVo.PaymentInvoiceExcelVo;
import com.midea.pam.common.ctc.excelVo.ReceiptClaimExcelVo;
import com.midea.pam.common.ctc.excelVo.ReceiptClaimInoviceRelExcelVo;
import com.midea.pam.common.ctc.query.PaymentRecordQuery;
import com.midea.pam.common.ctc.vo.PurchaseContractProgressVo;
import com.midea.pam.common.enums.AuditStatus;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.GSCPaymentAndInvoiceStatusPushEnum;
import com.midea.pam.common.enums.GlPeriodType;
import com.midea.pam.common.enums.InvoiceDetailStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PaymentApplyBizStatus;
import com.midea.pam.common.enums.PaymentApplyGcebErpStatus;
import com.midea.pam.common.enums.PaymentApplySourceNameEnum;
import com.midea.pam.common.enums.PaymentInvoiceAccountEntryTypeEnum;
import com.midea.pam.common.enums.PaymentInvoiceErpStatusEnum;
import com.midea.pam.common.enums.PaymentInvoiceSourceEnum;
import com.midea.pam.common.enums.PaymentInvoiceStatusEnum;
import com.midea.pam.common.enums.PurchaseContractPunishmentAccountStatusEnum;
import com.midea.pam.common.enums.PurchaseContractPunishmentInvoiceStatusEnum;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.Builder;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.carrybill.service.MaterialOutsourcingContractConfigService;
import com.midea.pam.ctc.common.enums.CostCollectionEnum;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.FreezeType;
import com.midea.pam.ctc.common.enums.MaterialOutsourceCostDetailType;
import com.midea.pam.ctc.common.enums.PaymentApplyIsChargeEnum;
import com.midea.pam.ctc.common.enums.PaymentApplyWriteOffStatus;
import com.midea.pam.ctc.common.enums.PaymentInvoiceErpCancelStatusEnum;
import com.midea.pam.ctc.common.enums.PaymentInvoiceFreezeStatus;
import com.midea.pam.ctc.common.enums.PaymentRecordStatus;
import com.midea.pam.ctc.common.enums.PaymentWriteOffRecordCancelStatus;
import com.midea.pam.ctc.common.enums.PaymentWriteOffRecordStatus;
import com.midea.pam.ctc.common.enums.ReceiptClaimEnum;
import com.midea.pam.ctc.common.enums.WriteOffEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.contract.service.PurchaseContractPunishmentService;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.BusiSceneNonSaleMapper;
import com.midea.pam.ctc.mapper.ContractExtMapper;
import com.midea.pam.ctc.mapper.CostCollectionMapper;
import com.midea.pam.ctc.mapper.CustomerExtMapper;
import com.midea.pam.ctc.mapper.GscPaymentInvoiceDetailExtMapper;
import com.midea.pam.ctc.mapper.GscPaymentInvoiceExtMapper;
import com.midea.pam.ctc.mapper.GscPaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailSplitMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailsExtMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailsMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyHeaderMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailExtMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailMapper;
import com.midea.pam.ctc.mapper.InvoiceReceivableMapper;
import com.midea.pam.ctc.mapper.MaterialOutsourceCostDetailMapper;
import com.midea.pam.ctc.mapper.MaterialOutsourcingContractConfigMapper;
import com.midea.pam.ctc.mapper.PaymentApplyDetailRelMapper;
import com.midea.pam.ctc.mapper.PaymentApplyExtMapper;
import com.midea.pam.ctc.mapper.PaymentApplyInvoiceRelMapper;
import com.midea.pam.ctc.mapper.PaymentApplyMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailExtMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceExtMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceFreezeRecordMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.PaymentPlanExtMapper;
import com.midea.pam.ctc.mapper.PaymentRecordExtMapper;
import com.midea.pam.ctc.mapper.PaymentRecordMapper;
import com.midea.pam.ctc.mapper.PaymentWriteOffRecordMapper;
import com.midea.pam.ctc.mapper.PositiveNegativeInvoiceRecordMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.PurchaseContractExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPunishmentExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPunishmentMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimContractRelExtMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimContractRelMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimDetailMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimInvoiceRelMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanMapper;
import com.midea.pam.ctc.mapper.purchaseContractPunishmentInvoiceRelExtMapper;
import com.midea.pam.ctc.remote.RemoteBaseDataService;
import com.midea.pam.ctc.remote.RemoteCrmService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.BusiSceneNonSaleService;
import com.midea.pam.ctc.service.CostCollectionService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.GSCPaymentAndInvoiceStatusPushService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.OrganizationRelExtService;
import com.midea.pam.ctc.service.PaymentInvoiceService;
import com.midea.pam.ctc.service.PaymentRecordService;
import com.midea.pam.ctc.service.ResendExecuteService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

public class PaymentInvoiceServiceImpl implements PaymentInvoiceService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private final String APPLY_CODE = "FK";

    private final static Long MAX_WAIT_TIME = (long) 1000 * 60 * 5;//5分钟

    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;
    @Resource
    private ProjectTypeMapper projectTypeMapper;
    @Resource
    private CostCollectionMapper costCollectionMapper;
    @Resource
    private CostCollectionService costCollectionService;
    @Resource
    private MaterialOutsourceCostDetailMapper materialOutsourceCostDetailMapper;
    @Resource
    private BusiSceneNonSaleMapper busiSceneNonSaleMapper;
    @Resource
    private OrganizationRelExtService organizationRelExtService;
    @Resource
    private BusiSceneNonSaleService busiSceneNonSaleService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private PurchaseContractService purchaseContractService;
    @Resource
    private PaymentApplyInvoiceRelMapper paymentApplyInvoiceRelMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private PaymentInvoiceService paymentInvoiceService;
    @Resource
    private PaymentApplyService paymentApplyService;
    @Resource
    private PaymentInvoiceExtMapper paymentInvoiceExtMapper;
    @Resource
    private PaymentApplyExtMapper paymentApplyExtMapper;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private PaymentInvoiceDetailMapper paymentInvoiceDetailMapper;
    @Resource
    private EsbService esbService;
    @Resource
    private ResendExecuteService resendExecuteService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private ReceiptClaimDetailMapper receiptClaimDetailMapper;
    @Resource
    private PaymentPlanExtMapper paymentPlanExtMapper;
    @Resource
    private PurchaseContractExtMapper purchaseContractExtMapper;
    @Autowired
    private RemoteBaseDataService remoteBaseDataService;
    @Autowired
    private RemoteCrmService remoteCrmService;
    @Resource
    private ProjectExtMapper projectExtMapper;
    @Resource
    private InvoiceApplyHeaderMapper invoiceApplyHeaderMapper;
    @Resource
    private InvoiceApplyDetailsMapper invoiceApplyDetailsMapper;
    @Resource
    private InvoiceReceivableMapper invoiceReceivableMapper;
    @Resource
    private ReceiptClaimMapper receiptClaimMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ContractExtMapper contractExtMapper;
    @Resource
    private InvoiceApplyDetailsExtMapper invoiceApplyDetailsExtMapper;
    @Resource
    private ReceiptClaimContractRelMapper receiptClaimContractRelMapper;
    @Resource
    private PaymentApplyMapper paymentApplyMapper;
    @Resource
    private PaymentApplyDetailRelMapper paymentApplyDetailRelMapper;
    @Resource
    private InvoicePlanDetailExtMapper invoicePlanDetailExtMapper;
    @Resource
    private PaymentRecordExtMapper paymentRecordExtMapper;
    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;
    @Resource
    private ReceiptPlanDetailMapper receiptPlanDetailMapper;
    @Resource
    private ReceiptPlanMapper receiptPlanMapper;
    @Resource
    private PaymentWriteOffRecordMapper paymentWriteOffRecordMapper;
    @Resource
    private PaymentRecordMapper paymentRecordMapper;
    @Resource
    private PaymentRecordService paymentRecordService;
    @Resource
    private Validator validator;
    @Resource
    private CustomerExtMapper customerExtMapper;
    @Resource
    private ReceiptClaimInvoiceRelMapper receiptClaimInvoiceRelMapper;
    @Resource
    ReceiptClaimContractRelExtMapper receiptClaimContractRelExtMapper;
    @Resource
    private InvoiceApplyDetailSplitMapper invoiceApplyDetailSplitMapper;
    @Resource
    private PaymentInvoiceDetailExtMapper paymentInvoiceDetailExtMapper;
    @Resource
    private PaymentInvoiceFreezeRecordMapper paymentInvoiceFreezeRecordMapper;
    @Resource
    private PositiveNegativeInvoiceRecordMapper positiveNegativeInvoiceRecordMapper;
    @Resource
    private PurchaseContractPunishmentService punishmentService;
    @Resource
    private MaterialOutsourcingContractConfigMapper materialOutsourcingContractConfigMapper;
    @Resource
    private PurchaseContractPunishmentExtMapper punishmentExtMapper;
    @Resource
    private PurchaseContractPunishmentMapper punishmentMapper;
    @Resource
    private purchaseContractPunishmentInvoiceRelExtMapper punishmentInvoiceRelExtMapper;
    @Resource
    private GSCPaymentAndInvoiceStatusPushService gscPaymentAndInvoiceStatusPushService;
    @Resource
    private GscPaymentInvoiceExtMapper gscPaymentInvoiceExtMapper;
    @Resource
    private GscPaymentInvoiceDetailExtMapper gscPaymentInvoiceDetailExtMapper;
    @Resource
    private GscPaymentInvoiceMapper gscPaymentInvoiceMapper;

    @Resource
    private MaterialOutsourcingContractConfigService materialOutsourcingContractConfigService;
    @Resource
    private SdpService sdpService;

    @Resource
    private SdpCarrierServicel sdpCarrierServicel;


    @Override
    public PaymentInvoiceDto add(PaymentInvoiceDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        PaymentInvoice entity = BeanConverter.copy(dto, PaymentInvoice.class);
        if (entity.getFreezeStatus() == null) {
            entity.setFreezeStatus(PaymentInvoiceFreezeStatus.UNFROZE.getCode());
        }
        //设置预算项目号
        if(Objects.isNull(entity.getItemNumber())){
            Pair<String, String> budgetProjectNumberPair = basedataExtService.getBudgetProjectNumberByVendorId(entity.getPurchaseContractId(), null, entity.getOuId());
            if(Objects.nonNull(budgetProjectNumberPair)){
                entity.setItemNumber(budgetProjectNumberPair.getValue());
            }
        }
        paymentInvoiceMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public PaymentInvoiceDto update(PaymentInvoiceDto dto) {
        PaymentInvoice entity = BeanConverter.copy(dto, PaymentInvoice.class);
        paymentInvoiceMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public PaymentInvoiceDto save(PaymentInvoiceDto dto, Long userBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
            return this.add(dto);
        } else {
            dto.setUpdateBy(userBy);
            return this.update(dto);
        }
    }

    @Override
    public PaymentInvoiceDto getById(Long id) {
        if (id == null) {
            return null;
        }
        PaymentInvoice entity = paymentInvoiceMapper.selectByPrimaryKey(id);
        PaymentInvoiceDto dto = BeanConverter.copy(entity, PaymentInvoiceDto.class);
        return dto;
    }

    @Override
    public List<PaymentInvoiceDto> selectList(PaymentInvoiceDto query) {
        List<PaymentInvoice> list = paymentInvoiceMapper.selectByExample(buildCondition(query));
        List<PaymentInvoiceDto> dtos = BeanConverter.copy(list, PaymentInvoiceDto.class);
        return dtos;
    }

    @Override
    public PageInfo<PaymentInvoiceDto> selectPage(PaymentInvoiceDto query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<PaymentInvoice> list = paymentInvoiceMapper.selectByExample(buildCondition(query));
        PageInfo<PaymentInvoiceDto> dtos = BeanConverter.convertPage(list, PaymentInvoiceDto.class);
        return dtos;
    }

    private PaymentInvoiceExample buildCondition(PaymentInvoiceDto query) {
        PaymentInvoiceExample example = new PaymentInvoiceExample();
        PaymentInvoiceExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());

        return example;
    }

    /**
     * 根据付款申请单查询AP发票
     *
     * @param applyId
     * @return
     */
    public List<PaymentInvoiceDto> getApplyInvoice(Long applyId) {
        if (null == applyId) {
            throw new BizException(Code.ERROR, "付款申请单ID不能为空");
        }
        List<PaymentInvoiceDto> invoiceDtos = paymentInvoiceExtMapper.selectByApplyId(applyId);
        return invoiceDtos;
    }

    /**
     * 发票信息行列表查询
     *
     * @param query
     * @return
     */
    @Override
    public List<PaymentInvoiceDetail> detailList(PaymentInvoiceDetail query) {
        PaymentInvoiceDetailExample example = buildDetailExample(query);
        List<PaymentInvoiceDetail> detailList = paymentInvoiceDetailMapper.selectByExample(example);
        return detailList;
    }


    /**
     * 发票信息头列表查询
     *
     * @param query
     * @return
     */
    @Override
    public List<PaymentInvoiceDto> list(PaymentInvoiceDto query) {
        PaymentInvoiceExample example = buildExample(query);
        List<PaymentInvoice> invoices = paymentInvoiceMapper.selectByExample(example);
        List<PaymentInvoiceDto> invoiceDtos = BeanConverter.convert(invoices, PaymentInvoiceDto.class);
        if (ListUtils.isNotEmpty(invoiceDtos)) {
            for (PaymentInvoiceDto invoiceDto : invoiceDtos) {
                Long purchaseContractId = invoiceDto.getPurchaseContractId();
                PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);
                //合同汇率信息
                if (purchaseContract != null) {
                    invoiceDto.setCurrency(purchaseContract.getCurrency());
                    invoiceDto.setConversionDate(purchaseContract.getConversionDate());
                    invoiceDto.setConversionType(purchaseContract.getConversionType());
                    invoiceDto.setConversionRate(purchaseContract.getConversionRate());
                }
                //冻结信息
                if (Objects.equals(invoiceDto.getFreezeStatus(), PaymentInvoiceFreezeStatus.FROZE.getCode())) {
                    PaymentInvoiceFreezeRecordExample recordExample = new PaymentInvoiceFreezeRecordExample();
                    recordExample.createCriteria().andPaymentInvoiceIdEqualTo(invoiceDto.getId())
                            .andFreezeTypeEqualTo(FreezeType.FREEZE.getCode())
                            .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                    recordExample.setOrderByClause("freeze_time desc");
                    List<PaymentInvoiceFreezeRecord> recordList = paymentInvoiceFreezeRecordMapper.selectByExample(recordExample);
                    if (CollectionUtils.isNotEmpty(recordList)) {
                        invoiceDto.setFreezeName(recordList.get(0).getFreezeName());
                        invoiceDto.setFreezeReason(recordList.get(0).getFreezeReason());
                    } else {
                        invoiceDto.setFreezeName("");
                        invoiceDto.setFreezeReason("");
                        UserInfo userInfo = CacheDataUtils.findUserById(invoiceDto.getCreateBy());
                        if (userInfo != null) {
                            invoiceDto.setFreezeName(userInfo.getName());
                        }
                    }
                }
            }
        }
        return invoiceDtos;
    }

    private PaymentInvoiceDetailExample buildDetailExample(PaymentInvoiceDetail query) {
        PaymentInvoiceDetailExample example = new PaymentInvoiceDetailExample();
        PaymentInvoiceDetailExample.Criteria criteria = example.createCriteria();

        if (!ObjectUtils.isEmpty(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (!ObjectUtils.isEmpty(query.getPaymentInvoiceId())) {
            criteria.andPaymentInvoiceIdEqualTo(query.getPaymentInvoiceId());
        }
        return example;
    }

    private PaymentInvoiceExample buildExample(PaymentInvoiceDto query) {
        PaymentInvoiceExample example = new PaymentInvoiceExample();
        PaymentInvoiceExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(false);
        if (!ObjectUtils.isEmpty(query.getPaymentPlanCode())) {
            criteria.andPaymentPlanCodeEqualTo(query.getPaymentPlanCode());
        }
        if (!ObjectUtils.isEmpty(query.getPaymentPlanId())) {
            criteria.andPaymentPlanIdEqualTo(query.getPaymentPlanId());
        }
        if (!ObjectUtils.isEmpty(query.getPurchaseContractId())) {
            criteria.andPurchaseContractIdEqualTo(query.getPurchaseContractId());
        }
        if (!ObjectUtils.isEmpty(query.getApInvoiceCode())) {
            criteria.andApInvoiceCodeLike(StringUtils.buildSqlLikeCondition(query.getApInvoiceCode()));
        }
        if (!ObjectUtils.isEmpty(query.getErpStatus())) {
            criteria.andErpStatusEqualTo(query.getErpStatus());
        }
        if (!ObjectUtils.isEmpty(query.getStatus())) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (!ObjectUtils.isEmpty(query.getAvailable())) {
            criteria.andAvailableEqualTo(query.getAvailable());
        }
        if (query.getPaymentApplyId() != null) {
            //付款申请发票头关联
            PaymentApplyInvoiceRelExample invoiceExample = new PaymentApplyInvoiceRelExample();
            invoiceExample.createCriteria()
                    .andPaymentApplyIdEqualTo(query.getPaymentApplyId())
                    .andDeletedFlagEqualTo(0);
            List<PaymentApplyInvoiceRel> invoiceRels = paymentApplyInvoiceRelMapper.selectByExample(invoiceExample);
            List<Long> invoiceIds = new ArrayList<>();
            invoiceIds.add(-1L);
            for (PaymentApplyInvoiceRel invoiceRel : invoiceRels) {
                invoiceIds.add(invoiceRel.getPaymentInvoiceId());
            }
            criteria.andIdIn(invoiceIds);
        }
        if (!StringUtils.isEmpty(query.getSurplusAmountMoreThan())) {
            criteria.andSurplusAmountGreaterThan(new BigDecimal(0));
        }

        if (StringUtils.isNotEmpty(query.getContractIdsStr())) {
            if (StringUtils.isNotEmpty(query.getContractIdsStr())) {
                List<Long> contractIds = new ArrayList<>();
                String[] arrStr = query.getContractIdsStr().split(",");
                for (String s : arrStr) {
                    if (StringUtils.isNotEmpty(s)) {
                        contractIds.add(Long.valueOf(s));
                    }
                }
                criteria.andPurchaseContractIdIn(contractIds);
            }
        }
        if (StringUtils.isNotEmpty(query.getStatusExclude())) {
            List<Integer> statusList = new ArrayList<>();
            String[] arrStr = query.getStatusExclude().split(",");
            for (String s : arrStr) {
                if (StringUtils.isNotEmpty(s)) {
                    statusList.add(Integer.valueOf(s));
                }
            }
            criteria.andStatusNotIn(statusList);
        }
        return example;
    }

    /**
     * PAM-ERP-023 应付发票写入
     *
     * @param invoiceDetailId 增值票id
     * @param glDate          入账日期
     */
    @Override
    public void pushToErp(Long invoiceDetailId, Date glDate) {
        PaymentInvoiceDetail invoiceDetail = paymentInvoiceDetailMapper.selectByPrimaryKey(invoiceDetailId);
        if (null == invoiceDetail) {
            throw new BizException(Code.ERROR, "增值票不存在");
        }
        if (InvoiceDetailStatus.QUOTE.getCode().equals(invoiceDetail.getInvoiceStatus())) {
            throw new BizException(Code.ERROR, "增值票不是未引用状态");
        }
        Long vendorId = invoiceDetail.getVendorId();
        VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(vendorId);
        if (vendorSiteBankDto == null) {
            throw new BizException(Code.ERROR, "供应商银行信息不存在");
        }
        PurchaseContract purchaseContract =
                purchaseContractMapper.selectByPrimaryKey(invoiceDetail.getPurchaseContractId());
        if (purchaseContract == null) {
            throw new BizException(Code.ERROR, "合同信息不存在");
        }

        //创建AP票
        PaymentInvoice invoice = new PaymentInvoice();
        invoice.setPaymentApplyId(-1L);
        invoice.setInvoiceDate(invoiceDetail.getInvoiceDate());
        invoice.setPaymentPlanId(-1L);
        invoice.setPurchaseContractId(invoiceDetail.getPurchaseContractId());
        invoice.setPurchaseContractCode(invoiceDetail.getPurchaseContractCode());
        invoice.setOuId(purchaseContract.getOuId());
        invoice.setApInvoiceCode("PAM-" + invoiceDetail.getInvoiceDetailCode());//ap发票编号
        invoice.setTotalInvoiceIncludedPrice(invoiceDetail.getTaxIncludedPrice());
        invoice.setTotalPayIncludedPrice(BigDecimal.ZERO);
        invoice.setAmount(BigDecimal.ZERO);
        invoice.setTotalAmount(invoiceDetail.getTaxIncludedPrice());
        //剩余可用金额
        invoice.setSurplusAmount(invoiceDetail.getTaxIncludedPrice());
        invoice.setTaxAmount(invoiceDetail.getTaxAmount());
        invoice.setErpStatus(0);//ERP同步状态(0-验证中1-已验证2-已取消) 默认验证中
        invoice.setCollectionStatus(0); // 默认未归集
        invoice.setAvailable(1);//是否可用(1-未占用 0-已占用)
        invoice.setCreateAt(new Date());
        invoice.setCreateBy(SystemContext.getUserId());
        PaymentInvoiceDto invoiceDto = BeanConverter.copy(invoice, PaymentInvoiceDto.class);
        invoiceDto.setSource(PaymentInvoiceSourceEnum.PAYMENT.getCode());
        //设置预算项目号
        if(Objects.isNull(invoiceDto.getItemNumber())){
            Pair<String, String> budgetProjectNumberPair = basedataExtService.getBudgetProjectNumberByVendorId(invoiceDto.getPurchaseContractId(), null, invoiceDto.getOuId());
            if(Objects.nonNull(budgetProjectNumberPair)){
                invoiceDto.setItemNumber(budgetProjectNumberPair.getValue());
            }
        }
        Long invoiceId = paymentInvoiceService.save(invoiceDto);
        //修改发票行状态
        invoiceDetail.setInvoiceStatus(InvoiceDetailStatus.QUOTE.getCode());
        punishmentService.updatePunishmentAccountStatus(invoiceDetail, PurchaseContractPunishmentAccountStatusEnum.ACCOUNTED.getCode());
        invoiceDetail.setPaymentInvoiceId(invoiceId);
        paymentInvoiceDetailMapper.updateByPrimaryKeySelective(invoiceDetail);

        final ResendExecute resend = new ResendExecute();
        resend.setBusinessType(BusinessTypeEnums.PAYMENT_INVOICE.getCode());
        resend.setApplyNo(String.valueOf(invoiceId));
        resend.setDeletedFlag(Boolean.FALSE);
        resend.setBatch(false);
        resend.setStatus(CommonStatus.PROCESSING.getCode());
        resendExecuteService.insertSelective(resend);

        //手动写入接口
        List<PaymentInvoiceErpDto> dtoList = new ArrayList<>();
        PaymentInvoiceErpDto erpDto = new PaymentInvoiceErpDto();
        List<PaymentInvoiceDetailErpDto> detailErpDtoList = new ArrayList<>();
        //头信息
        erpDto.setId(invoiceDetail.getId());
        Long ouId = purchaseContract.getOuId();
        erpDto.setOrgId(ouId != null ? new BigDecimal(ouId) : null);
        erpDto.setBatchName(invoiceDetail.getInvoiceDetailCode());
        erpDto.setInvoiceNum(invoice.getApInvoiceCode());
        erpDto.setInvoiceTypeLookupCode("MIXED");
        erpDto.setInvoiceDate(DateUtils.formatDate(invoice.getInvoiceDate()));
        erpDto.setGlDate(DateUtils.formatDate(glDate != null ? glDate : new Date()));
        erpDto.setVendorId(new BigDecimal(vendorSiteBankDto.getErpVendorId()));//供应商ID
        erpDto.setVendorSiteCode(new BigDecimal(vendorSiteBankDto.getErpVendorSiteId()));//供应商地点ID
        erpDto.setPaymentMethodCode(vendorSiteBankDto.getPaymentMethodCode());
        erpDto.setPaymentMethod(vendorSiteBankDto.getPaymentMethodName());
        erpDto.setTermsName("立即付款");
        erpDto.setAttribute3(Optional.of(invoiceDto).map(PaymentInvoice::getItemNumber).orElse(""));
        erpDto.setInvoiceAmount(invoice.getTotalInvoiceIncludedPrice());// 发票额
        erpDto.setTaxAmount(invoice.getTaxAmount());//发票税额
        erpDto.setDescription("PAM发票手工导入，" + invoiceDetail.getInvoiceDetailCode());// 发票描述
        erpDto.setCurrencyCode(Optional.ofNullable(invoiceDetail.getCurrency()).orElse("CNY"));//币种

        //不含税发票金额行信息
        PaymentInvoiceDetailErpDto detailErpDto = new PaymentInvoiceDetailErpDto();
        detailErpDto.setId(invoiceDetail.getId() + "");
        detailErpDto.setInvoiceNum(invoice.getApInvoiceCode());
        detailErpDto.setOrgId(new BigDecimal(purchaseContract.getOuId()));
        detailErpDto.setLineTypeLookUpCode("ITEM");
        detailErpDto.setAmount(invoiceDetail.getTaxIncludedPrice().subtract(invoiceDetail.getTaxAmount()));//行金额，不含税
        //查询组织参数配置--区分含税不含税
        detailErpDto.setSegment1(getBuyerNumberByOu(purchaseContract.getOuId()));
        detailErpDto.setSegment2("0");
        detailErpDto.setSegment3("5401010101");
        detailErpDto.setSegment4("0");
        detailErpDto.setSegment5("0");
        detailErpDto.setSegment6("0");
        detailErpDto.setSegment7("0");
        String remark = "";
        remark = "发票编号" + invoiceDetail.getInvoiceDetailCode();
        detailErpDto.setDescription(remark);
        detailErpDtoList.add(detailErpDto);

        erpDto.setDetailErpDtos(detailErpDtoList);

        // 设置汇率
        PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(invoiceDetail.getPurchaseContractId());
        //查询当前业务实体下对应的本位币
        List<OrganizationRel> organizationRels = basedataExtService.queryByOuId(contract.getOuId());
        if (ListUtils.isNotEmpty(organizationRels)) {
            if (null == organizationRels.get(0).getCurrency()) {
                throw new BizException(Code.ERROR, String.format("请配置当前业务实体的本位币，ouId=%s", contract.getOuId()));
            }
        } else {
            throw new BizException(Code.ERROR, String.format("请配置当前业务实体的本位币，ouId=%s", contract.getOuId()));
        }
        //非本位币才设值
        if (!Objects.equals(erpDto.getCurrencyCode(), organizationRels.get(0).getCurrency())) {
            if (StringUtils.isEmpty(contract.getConversionType())) {
                throw new BizException(Code.ERROR, String.format("发票明细ID:%s外币发票的汇率类型不能为空，", invoiceDetailId));
            }
            erpDto.setExchangeRateType("Corporate".equals(contract.getConversionType()) ? "公司" : contract.getConversionType());  //汇率类型
            erpDto.setExchangeRateDate(DateUtils.formatDate(contract.getConversionDate())); //汇率日期
            if ("用户".equals(erpDto.getExchangeRateType())) {
                erpDto.setExchangeRate(contract.getConversionRate()); //汇率，公司留空
            }
        }
        dtoList.add(erpDto);
        try {
            final EsbResponse<Object> response = sdpService.callErpPaymentInvoice(dtoList);
            resend.setResponCode(response.getResponsecode());
            resend.setStatus(Objects.equals(response.getResponsecode(),
                    ResponseCodeEnums.SUCESS.getCode()) ? CommonStatus.DONE.getCode() : CommonStatus.ERROR.getCode());
            resend.setResponMsg(response.getResponsemessage());
            resend.setEsbSerialNo(null != response.getData() ? String.valueOf(response.getData()) : null);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            resend.setStatus(CommonStatus.ERROR.getCode());
            resend.setResponCode(ResponseCodeEnums.FAULT.getCode());
            resend.setResponMsg(e.toString());
            logger.error("业务编号为:{}的单据发送失败，单据编号为:{}", resend.getBusinessType(), resend.getApplyNo(), e);
        } finally {
            resendExecuteService.updateByPrimaryKeySelective(resend);
        }

    }

    @Override
    public String getBuyerNumberByOu(Long ouId) {
        if (ouId == null) {
            return "129701";
        }
        String dictName = "OU_CODE";
        Set<String> valueSet = organizationCustomDictService.queryByName(dictName,
                ouId,
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
        if (CollectionUtils.isEmpty(valueSet)) {
            return "129701";
        } else {
            return valueSet.iterator().next();
        }
    }

    @Override
    public void callBack(Long id, Boolean isSuccess, String msg, String actualCost) {
        PaymentInvoice paymentInvoice = paymentInvoiceService.getById(id);
        // 如果已经验证成功，直接跳过
        if (Objects.equals(paymentInvoice.getErpStatus(), PaymentInvoiceErpStatusEnum.SUCCESS.code())) {
            return;
        }
        if (Objects.equals(paymentInvoice.getSource(), PaymentInvoiceSourceEnum.REFUND.getCode())) {
            //PAM-ERP-023-1 退款应付发票写入
            dealPaymentInvoiceRefund(paymentInvoice, isSuccess, msg);
        } else {
            //PAM-ERP-023 应付发票写入
            dealPaymentInvoice(paymentInvoice, isSuccess, msg);
        }
    }

    public void dealPaymentInvoice(PaymentInvoice paymentInvoice, Boolean isSuccess, String msg) {
        if (isSuccess) {
            //查询ERP发票号
            Map<String, String> paramMap = new HashMap();
//            paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(paymentInvoice.getOuId()));
//            paramMap.put(EsbConstant.ERP_IP_P02, DateUtils.format(paymentInvoice.getCreateAt()));
//            paramMap.put(EsbConstant.ERP_IP_P03, paymentInvoice.getApInvoiceCode());
            paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(paymentInvoice.getOuId()));
            paramMap.put(EsbConstant.ERP_SDP_P02, DateUtils.format(paymentInvoice.getCreateAt()));
            paramMap.put(EsbConstant.ERP_SDP_P03, paymentInvoice.getApInvoiceCode());
            getInvoiceInfoFromErp(paramMap, paymentInvoice.getId());
        } else {
            paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.FAILURE.code());
            paymentInvoice.setErpMsg(msg);
            //更新对应付款申请的同步状态
            if (paymentInvoice.getSource().equals(PaymentInvoiceSourceEnum.PAYMENT.getCode())) {
                if (paymentInvoice.getPaymentApplyId() != null) {
                    PaymentApply applyDto = paymentApplyService.findById(paymentInvoice.getPaymentApplyId());
                    applyDto.setEsbStatus("1");//接口发送状态(0-待同步,1-同步异常,2-同步成功)
                    applyDto.setErpMsg(msg);
                    paymentApplyService.update(applyDto);
                }
            }
            if (paymentInvoice.getSource().equals(PaymentInvoiceSourceEnum.REFUND.getCode())) {
                ReceiptClaimDetailExample example = new ReceiptClaimDetailExample();
                example.createCriteria().andPaymentInvoiceIdEqualTo(paymentInvoice.getId());
                ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByExample(example).get(0);
                PaymentApply paymentApply = paymentApplyService.findById(receiptClaimDetail.getPaymentApplyId());
                paymentApply.setAuditStatus(5);

                if (paymentApply.getIsCharge() == 1) {
                    paymentApply.setGcebStatus(String.valueOf(5));
                } else {
                    paymentApply.setErpStatus(String.valueOf(5));
                }
                paymentApplyService.update(paymentApply);

                // 推送GSC付款申请支付状态
                if(Objects.equals(paymentApply.getSourceSystemName(), PaymentApplySourceNameEnum.GSC.getName())){
                    gscPaymentAndInvoiceStatusPushService.pushPaymentAndInvoiceStatus(GSCPaymentAndInvoiceStatusPushEnum.PAYMENT_APPLY_GCEB_STATUS_PUSH, paymentApply.getId());
                }
            }
            //更新行状态
            paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
        }
    }

    public void dealPaymentInvoiceRefund(PaymentInvoice paymentInvoice, Boolean isSuccess, String msg) {
        if (!isSuccess) {
            paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.FAILURE.code());
            paymentInvoice.setErpMsg(msg);
            //更新行状态
            paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
        } else {
            //更新发票状态
            paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.CHECKING.code());
            paymentInvoice.setErpMsg(msg);
            paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
            //生成负数的付款记录
            generatePaymentRecord(paymentInvoice);
        }
    }

    /**
     * 判断预付款申请是否已经完全核销
     *
     * @param paymentApply
     * @return
     */
    public Boolean isPrepayAllForApply(PaymentApply paymentApply) {
        if (null == paymentApply) {
            return false;
        }
        if (!PaymentApplyGcebErpStatus.SUCCESS.getCode().equals(paymentApply.getGcebStatus())) {
            return false;
        }
        //根据预付款查询付款记录
        PaymentRecordExample recordExample = new PaymentRecordExample();
        recordExample.createCriteria().andDeletedFlagEqualTo(0).andPaymentApplyIdEqualTo(paymentApply.getId());
        List<PaymentRecord> recordList = paymentRecordMapper.selectByExample(recordExample);
        BigDecimal totalPrepayAmount = BigDecimal.valueOf(0.00);
        for (PaymentRecord record : recordList) {
            if (PaymentRecordStatus.DONE.equals(record.getPaymentStatus())) {
                totalPrepayAmount.add(record.getWriteOffAmount());
            }
        }
        return totalPrepayAmount.compareTo(paymentApply.getReallyPayIncludedPrice()) == 0;
    }

    private Date setWriteOffDay(Long ouId) {
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
        if (ListUtils.isEmpty(organizationRelList)) {
            throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
        }
        OrganizationRelDto organizationRelDto = organizationRelList.get(0);
        Long ledgerId = organizationRelDto.getLedgerId();
        List<GlPeriodDto> glPeriod = basedataExtService.getGlPeriod(ledgerId,
                GlPeriodType.PAYMENT_PERIOD.getCode().toString(),
                null);
        List<GlPeriodDto> glPeriodOpen = basedataExtService.getGlPeriod2(ledgerId,
                GlPeriodType.PAYMENT_PERIOD.getCode().toString(),
                "O");
        glPeriodOpen.sort(Comparator.comparing(GlPeriodDto::getEndDate).reversed());
        Date happenDate = null;
        if (ListUtils.isNotEmpty(glPeriod)) {
            for (GlPeriodDto glPeriodDto : glPeriod) {
                String status = glPeriodDto.getClosingStatus();
                Date startDate = glPeriodDto.getStartDate();
                Date endDate = glPeriodDto.getEndDate();
                if ((new Date()).after(startDate) && (new Date()).before(DateUtils.addDay(endDate, 1)) && "O".equals(
                        status)) {
                    happenDate = (new Date());
                } else {
                    happenDate = (glPeriodOpen.get(0).getEndDate());
                }
            }
        }
        return happenDate;
    }

    public PaymentRecord generatePaymentRecord(PaymentInvoice paymentInvoice) {
        PaymentRecord paymentRecord = new PaymentRecord();
        paymentRecord = new PaymentRecord();
        paymentRecord.setCreateBy(SystemContext.getUserId());
        paymentRecord.setCreateAt(new Date());
        paymentRecord.setPaymentApplyId(paymentInvoice.getPaymentApplyId());
        paymentRecord.setPurchaseContractId(paymentInvoice.getPurchaseContractId());
        paymentRecord.setPurchaseContractCode(paymentInvoice.getPurchaseContractCode());
//        paymentRecord.setErpInvoiceId(null);
//        paymentRecord.setErpInvoiceCode(null);
        paymentRecord.setPayType("预付款");
        paymentRecord.setPenaltyAmount(BigDecimal.valueOf(0.00));//罚扣
        paymentRecord.setHappenDate(new Date());
        paymentRecord.setPaymentDate(new Date());
        paymentRecord.setSubmitBy(SystemContext.getUserId());
        paymentRecord.setPayNum(paymentInvoice.getPaymentApplyCode());
        paymentRecord.setAmount(paymentInvoice.getTotalInvoiceIncludedPrice().negate());//付款金额(含税),取负数
        paymentRecord.setWriteOffAmount(BigDecimal.valueOf(0.00));
        paymentRecord.setPaymentStatus(PaymentRecordStatus.NOT.code());
        paymentRecord.setDeletedFlag(0);
        paymentRecordMapper.insert(paymentRecord);
        logger.info("付款计划ID:{},付款申请ID:{},发票编号:{},付款申请累计实际付款金额:{},添加的付款记录为:{},",
                paymentInvoice.getPaymentPlanId(), paymentInvoice.getPaymentApplyId(), paymentInvoice.getApInvoiceCode(),
                paymentInvoice.getTotalInvoiceIncludedPrice().negate(),
                JSON.toJSONString(paymentRecord));

        return paymentRecord;
    }

    @Override
    @Transactional
    public void cancelCallBack(Long id, boolean isSuccess, String msg, String sourceId3) {
        PaymentInvoiceExample invoiceExampleexample = new PaymentInvoiceExample();
        invoiceExampleexample.createCriteria().andErpInvoiceCodeEqualTo(String.valueOf(id));
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByExample(invoiceExampleexample).get(0);
        if (isSuccess) {
            ReceiptClaimDetailExample example = new ReceiptClaimDetailExample();
            example.createCriteria().andPaymentInvoiceIdEqualTo(paymentInvoice.getId());
            ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByExample(example).get(0);

            // added by dengfei added at 20210119 已撤回的非销售回款，页面显示已认款
            receiptClaimDetail.setClaimStatus(ReceiptClaimEnum.UNCLAIM.getCode());

            PaymentApply paymentApply = paymentApplyService.findById(receiptClaimDetail.getPaymentApplyId());

            receiptClaimDetail.setErpStatus(ReceiptClaimEnum.ERP_NOT_PUSH.getCode());
            receiptClaimDetail.setErpMessage("");
            receiptClaimDetailMapper.updateByPrimaryKeySelective(receiptClaimDetail);

            //取消AP发票
            paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.CANCEL.code());

            //作废付款申请
            paymentApply.setAuditStatus(5);
            if (paymentApply.getIsCharge() == 1) {
                paymentApply.setGcebStatus(String.valueOf(5));
            } else {
                paymentApply.setErpStatus(String.valueOf(5));
            }
            paymentApplyService.update(paymentApply);

            // 推送GSC付款申请支付状态
            if(Objects.equals(paymentApply.getSourceSystemName(), PaymentApplySourceNameEnum.GSC.getName())){
                gscPaymentAndInvoiceStatusPushService.pushPaymentAndInvoiceStatus(GSCPaymentAndInvoiceStatusPushEnum.PAYMENT_APPLY_GCEB_STATUS_PUSH, paymentApply.getId());
            }

        } else {
            paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.SUCCESS.code());//恢复已验证状态
            paymentInvoice.setErpMsg(msg);
        }

        paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
    }

    @Override
    public PaymentInvoiceDto getDetailById(Long id) {
        PaymentInvoiceDto dto = this.getById(id);
        packageDto(dto);

        if (dto != null && dto.getPurchaseContractId() != null) {
            dto.setPurchaseContract(purchaseContractService.findById(dto.getPurchaseContractId()));
            dto.setVendorSiteBank(getVendorInfo(dto.getPurchaseContract()));
            //查询项目code
            if (dto.getPurchaseContract() != null && dto.getPurchaseContract().getProjectId() != null) {
                Project project = projectMapper.selectByPrimaryKey(dto.getPurchaseContract().getProjectId());
                if (project != null) {
                    dto.setProjectCode(project.getCode());
                    dto.setProjectName(project.getName());
                }
            }
            //采购合同进度执行金额（不含税）
            PurchaseContractProgressVo progressVo = purchaseContractService.calculateExecuteAmountTotal(dto.getPurchaseContractId());
            dto.setPurchasingContractProgressAmount(progressVo.getExecuteAmountTotalHeader());

            //已入账税票金额（不含税）
            BigDecimal amountOfTaxReceipts = paymentInvoiceExtMapper.calculateAmountOfTaxReceipts(dto.getPurchaseContractId());
            dto.setAmountOfTaxReceipts(amountOfTaxReceipts);
            //添加付款申请明细
            List<PaymentApplyDetailsDto> paymentApplyDetailsDtos = new ArrayList<>();
            PaymentApplyInvoiceRelExample example = new PaymentApplyInvoiceRelExample();
            example.createCriteria().andPaymentInvoiceIdEqualTo(dto.getId())
                    .andDeletedFlagEqualTo(0);
            List<PaymentApplyInvoiceRel> paymentApplyInvoiceRels = paymentApplyInvoiceRelMapper.selectByExample(example);
            logger.info("获取付款申请发票关联信息为:{}", JSON.toJSONString(paymentApplyDetailsDtos));
            if (CollectionUtils.isNotEmpty(paymentApplyInvoiceRels)) {
                List<Long> paymentApplyIdList = new ArrayList<>();
                for (PaymentApplyInvoiceRel invoiceRel : paymentApplyInvoiceRels) {
                    paymentApplyIdList.add(invoiceRel.getPaymentApplyId());
                }
                logger.info("获取的付款申请编号为:{}", JSON.toJSONString(paymentApplyIdList));
                if (CollectionUtils.isNotEmpty(paymentApplyIdList)) {
                    PaymentApplyExample paymentApplyExample = new PaymentApplyExample();
                    paymentApplyExample.createCriteria().andIdIn(paymentApplyIdList)
                            .andDeletedFlagEqualTo(0);
                    List<PaymentApply> paymentApplies = paymentApplyMapper.selectByExample(paymentApplyExample);
                    if (CollectionUtils.isNotEmpty(paymentApplies)) {
                        //查付款记录表
                        PaymentRecordExample paymentRecordExample = new PaymentRecordExample();
                        paymentRecordExample.createCriteria().andPaymentApplyIdIn(paymentApplyIdList)
                                .andDeletedFlagEqualTo(0);
                        List<PaymentRecord> paymentRecords = paymentRecordMapper.selectByExample(paymentRecordExample);
                        //要找到跟这张发票关联的付款记录,付款记录存在一对多的情况
                        Map<String, List<PaymentRecord>> listMap = paymentRecords.stream().collect(Collectors.groupingBy(x -> x.getPaymentApplyId() + "-" + x.getApInvoiceCode()));
                        logger.info("付款申请对应的付款记录为:{}", JSON.toJSONString(listMap));
                        for (PaymentApply paymentApply : paymentApplies) {
                            //实际金额和实际日期,需要取付款记录表中的
                            String key = paymentApply.getId() + "-" + dto.getApInvoiceCode();
                            logger.info("待匹配的key为:{}", key);
                            if (listMap.containsKey(key)) {
                                for (PaymentRecord paymentRecord : listMap.get(key)) {
                                    PaymentApplyDetailsDto detailsDto = new PaymentApplyDetailsDto();
                                    detailsDto.setReallyPayIncludedPrice(paymentRecord.getAmount());
                                    detailsDto.setActualPaymentDate(paymentRecord.getPaymentDate());
                                    paymentApplyDetailsDtos.add(createPaymentApplyDetailsDto(paymentApply, detailsDto));
                                }
                            } else {
                                PaymentApplyDetailsDto detailsDto = new PaymentApplyDetailsDto();
                                paymentApplyDetailsDtos.add(createPaymentApplyDetailsDto(paymentApply, detailsDto));
                            }
                        }
                    }
                }
            }
            logger.info("获取发票关联的付款申请明细为:{}", JSON.toJSONString(paymentApplyDetailsDtos));
            dto.setPaymentApplyDetailsDtos(paymentApplyDetailsDtos);

            //核销明细
            PaymentWriteOffRecordExample paymentWriteOffRecordExample = new PaymentWriteOffRecordExample();
            paymentWriteOffRecordExample.createCriteria().andPaymentInvoiceIdEqualTo(dto.getId())
                    .andDeletedFlagEqualTo(false);
            List<PaymentWriteOffRecord> records = paymentWriteOffRecordMapper.selectByExample(paymentWriteOffRecordExample);
            List<PaymentWriteOffRecordDetailsDto> paymentWriteOffRecordDetailsDtos = new ArrayList<>();
            logger.info("获取预付款核销记录:{}", JSON.toJSONString(records));
            if (CollectionUtils.isNotEmpty(records)) {
                List<Long> paymentRecordIds = records.stream()
                        .map(PaymentWriteOffRecord::getPaymentRecordId)
                        .collect(Collectors.toList());
                Map<Long, PaymentRecord> map = new HashMap<>();
                if (CollectionUtils.isNotEmpty(paymentRecordIds)) {
                    PaymentRecordExample paymentRecordExample = new PaymentRecordExample();
                    paymentRecordExample.createCriteria().andIdIn(paymentRecordIds)
                            .andDeletedFlagEqualTo(0);
                    List<PaymentRecord> paymentRecords = paymentRecordMapper.selectByExample(paymentRecordExample);
                    for (PaymentRecord paymentRecord : paymentRecords) {
                        map.put(paymentRecord.getId(), paymentRecord);
                    }
                }


                for (PaymentWriteOffRecord record : records) {

                    Long paymentRecordId = record.getPaymentRecordId();
                    PaymentWriteOffRecordDetailsDto detailsDto = new PaymentWriteOffRecordDetailsDto();
                    detailsDto.setWriteOffAmount(record.getAmount());
                    detailsDto.setWriteOffDate(record.getHappenDate());
                    detailsDto.setWriteOffType("预付款核销");
                    if (map.containsKey(paymentRecordId)) {
                        PaymentRecord paymentRecord = map.get(paymentRecordId);
                        detailsDto.setErpInvoiceId(paymentRecord.getErpInvoiceId());
                        detailsDto.setErpInvoiceCode(paymentRecord.getErpInvoiceCode());
                    }
                    detailsDto.setPaymentApplyCode(record.getPaymentApplyCode());
                    detailsDto.setStatus(record.getStatus());
                    paymentWriteOffRecordDetailsDtos.add(detailsDto);
                }

            }
            //正负发票核销明细
            //这里需要注意如果是正数发票,则显示与之相关的负数发票信息,如果是负数发票,则显示与之相关的正数发票信息
            BigDecimal totalInvoiceIncludedPrice = dto.getTotalInvoiceIncludedPrice(); //发票金额含税
            if (totalInvoiceIncludedPrice.compareTo(new BigDecimal(0)) > 0) {
                logger.info("展示的发票为正数发票");
                //查询与之相关的负数发票
                PositiveNegativeInvoiceRecordExample recordExample = new PositiveNegativeInvoiceRecordExample();
                recordExample.createCriteria().andPosPaymentInvoiceIdEqualTo(id)
                        .andDeletedFlagEqualTo(Boolean.FALSE);
                List<PositiveNegativeInvoiceRecord> invoiceRecords = positiveNegativeInvoiceRecordMapper.selectByExample(recordExample);
                if (CollectionUtils.isNotEmpty(invoiceRecords)) {
                    //List<Long> ids = invoiceRecords.stream().map(PositiveNegativeInvoiceRecord::getNegPaymentInvoiceId).collect(Collectors.toList());
                    //查询与之相关的负数发票
                    for (PositiveNegativeInvoiceRecord invoiceRecord : invoiceRecords) {
                        PaymentWriteOffRecordDetailsDto detailsDto = new PaymentWriteOffRecordDetailsDto();
                        detailsDto.setWriteOffAmount(invoiceRecord.getAmount());
                        detailsDto.setWriteOffDate(invoiceRecord.getHappenDate());
                        detailsDto.setWriteOffType("正负发票核销");
                        detailsDto.setErpInvoiceId(invoiceRecord.getNegPaymentInvoiceId());
                        detailsDto.setErpInvoiceCode(invoiceRecord.getNegApInvoiceCode());
                        detailsDto.setPaymentApplyCode(invoiceRecord.getPaymentApplyCode());
                        detailsDto.setStatus(invoiceRecord.getSyncStatus());
                        paymentWriteOffRecordDetailsDtos.add(detailsDto);
                    }
                }
            } else if (totalInvoiceIncludedPrice.compareTo(new BigDecimal(0)) < 0) {
                logger.info("展示的发票为负数发票");
                //查询与之相关的正数发票
                PositiveNegativeInvoiceRecordExample recordExample = new PositiveNegativeInvoiceRecordExample();
                recordExample.createCriteria().andNegPaymentInvoiceIdEqualTo(id)
                        .andDeletedFlagEqualTo(Boolean.FALSE);
                List<PositiveNegativeInvoiceRecord> invoiceRecords = positiveNegativeInvoiceRecordMapper.selectByExample(recordExample);
                if (CollectionUtils.isNotEmpty(invoiceRecords)) {
                    //List<Long> ids = invoiceRecords.stream().map(PositiveNegativeInvoiceRecord::getPosPaymentInvoiceId).collect(Collectors.toList());
                    //查询与之相关的正数发票
                    for (PositiveNegativeInvoiceRecord invoiceRecord : invoiceRecords) {
                        PaymentWriteOffRecordDetailsDto detailsDto = new PaymentWriteOffRecordDetailsDto();

                        detailsDto.setWriteOffAmount(invoiceRecord.getAmount().multiply(new BigDecimal(-1)));
                        detailsDto.setWriteOffDate(invoiceRecord.getHappenDate());
                        detailsDto.setWriteOffType("正负发票核销");
                        detailsDto.setErpInvoiceId(invoiceRecord.getPosPaymentInvoiceId());
                        detailsDto.setErpInvoiceCode(invoiceRecord.getPosApInvoiceCode());
                        detailsDto.setPaymentApplyCode(invoiceRecord.getPaymentApplyCode());
                        detailsDto.setStatus(invoiceRecord.getSyncStatus());
                        paymentWriteOffRecordDetailsDtos.add(detailsDto);
                    }
                }

            } else {
                logger.info("发票金额为0,不处理");
            }

            logger.info("获取发票关联的核销明细为:{}", JSON.toJSONString(paymentWriteOffRecordDetailsDtos));
            dto.setPaymentWriteOffRecordDetailsDtos(paymentWriteOffRecordDetailsDtos);

            punishmentInvoiceSelect(dto);

            setGscPaymentInvoiceId(dto);
        }
        return dto;
    }

    /**
     * 设置GSC付款发票ID
     * @param dto 付款发票DTO
     */
    private void setGscPaymentInvoiceId(PaymentInvoiceDto dto) {
        Optional.ofNullable(dto.getGscInvoiceNumber())
                .filter(StringUtils::isNotEmpty)
                .map(gscPaymentInvoiceExtMapper::getGscPaymentInvoiceByInvoiceNumber)
                .filter(Objects::nonNull)
                .ifPresent(gscPaymentInvoice -> dto.setGscPaymentInvoiceId(gscPaymentInvoice.getId()));
    }

    /**
     * 获取罚扣信息
     * @param dto
     */
    private void punishmentInvoiceSelect(PaymentInvoiceDto dto) {
        Integer accountEntryType = dto.getAccountEntryType();
        //罚扣信息查询
        if(Objects.equals(accountEntryType,PaymentInvoiceAccountEntryTypeEnum.NO_INVOICE_PENALTY.getCode())){
            List<PunishmentInvoiceVO> punishmentInvoiceVOS = punishmentExtMapper.selectNoPunishmentInvoiceAccountEntryList(dto.getId(), null);
            if(ListUtils.isNotEmpty(punishmentInvoiceVOS)){
                punishmentInvoiceVOS.forEach(vo->{
                    if(Objects.nonNull(vo.getCreateBy())){
                        UserInfo userInfo = CacheDataUtils.findUserById(vo.getCreateBy());
                        vo.setCreator(userInfo.getName());
                    }
                });
                dto.setPunishmentInvoiceViewList(punishmentInvoiceVOS);
                BigDecimal totalAmount = punishmentInvoiceVOS.stream().map(PunishmentInvoiceVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                dto.setTaxExcludedPrice(totalAmount);
            }else{
                //前端不含税字段取值逻辑：totalInvoiceIncludedPrice - taxAmount
                dto.setTaxAmount(dto.getTotalInvoiceIncludedPrice());
            }
        }
    }

    private PaymentApplyDetailsDto createPaymentApplyDetailsDto(PaymentApply paymentApply, PaymentApplyDetailsDto detailsDto) {
        detailsDto.setPaymentApplyCode(paymentApply.getPaymentApplyCode());
        //这里需要判断是否为预付款,取值不同,跟付款申请列表保持一致
        detailsDto.setPaymentApplyStatus(getPaymentApplyStatus(paymentApply));
        detailsDto.setApplyDate(paymentApply.getSubmitDate());
        detailsDto.setPaymentMethodId(paymentApply.getPaymentMethodId());
        detailsDto.setPaymentMethodName(paymentApply.getPaymentMethodName());
        detailsDto.setEsbStatus(paymentApply.getEsbStatus());
        return detailsDto;
    }

    private Integer getPaymentApplyStatus(PaymentApply paymentApply) {
        String paymentApplyStatus = null;

        String auditStatus = paymentApply.getAuditStatus() + "";
        if (auditStatus.equals(PaymentApplyBizStatus.DRAFT.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.PENDING.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.REFUSE.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.EFFECTIVE.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.CANCEL.getCode())) {
            paymentApplyStatus = auditStatus;
        }

        if (auditStatus.equals(PaymentApplyBizStatus.EFFECTIVE.getCode())) {
            //是否预付款(1:是 0否)
            String isCharge = paymentApply.getIsCharge() + "";
            String status = "";
            if ("1".equals(isCharge)) {
                status = paymentApply.getGcebStatus();
            } else {
                status = paymentApply.getErpStatus();
            }
            if (status.equals(PaymentApplyGcebErpStatus.SUCCESS.getCode())) {
                paymentApplyStatus = PaymentApplyBizStatus.SUCCESS.getCode();
            } else if (status.equals(PaymentApplyGcebErpStatus.CANCEL.getCode())) {
                paymentApplyStatus = PaymentApplyBizStatus.CANCEL_PAY.getCode();
            } else if (status.equals(PaymentApplyGcebErpStatus.PARTIAL_PAYMENT.getCode())) {
                paymentApplyStatus = PaymentApplyBizStatus.PARTIAL_PAYMENT.getCode();
            } else if (status.equals(PaymentApplyGcebErpStatus.EXCEPTION.getCode())) {
                paymentApplyStatus = PaymentApplyBizStatus.EXCEPTION.getCode();
            } else if (status.equals(PaymentApplyGcebErpStatus.REJECTED.getCode())) {
                paymentApplyStatus = PaymentApplyBizStatus.REJECTED.getCode();
            }
        }
        logger.info("处理后的付款申请状态为:{}", paymentApplyStatus);
        return StringUtils.isNotEmpty(paymentApplyStatus) ? Integer.parseInt(paymentApplyStatus) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentInvoiceDto saveTheInvoice(PaymentInvoiceDto dto) {
        checkParams(dto);
        checkGlDate(dto.getOuId(), dto.getGlDate());
        Integer accountEntryType = dto.getAccountEntryType();
        //常规发票
        if(Objects.equals(PaymentInvoiceAccountEntryTypeEnum.REGULAR_INVOICE.getCode(), accountEntryType)){
            List<Long> detailIds = ListUtils.map(dto.getPaymentInvoiceDetailDtoList(), "id");
            checkPaymentInvoiceDetail(detailIds, dto.getId(), dto.getPurchaseContractId());

            MaterialOutsourcingContractConfig materialConfig = materialOutsourcingContractConfigService.selectByPurchaseContractCode(dto.getPurchaseContractCode());
            if (null == materialConfig){
                throw new BizException(Code.ERROR, "未找到采购合同类型配置 ，请联系IT进行配置");
            }

            //如果是应付发票提交前单据检查则终止后续操作
            if(Objects.equals(dto.isDraftSubmitCheck(),Boolean.TRUE)){
                return null;
            }

            PaymentInvoice paymentInvoice = new PaymentInvoice();
            if (dto.getId() != null) {
                paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(dto.getId());
                //释放关联的税票
                PaymentInvoiceDetailExample detailExample = new PaymentInvoiceDetailExample();
                detailExample.createCriteria().andPaymentInvoiceIdEqualTo(dto.getId())
                        .andInvoiceStatusEqualTo(InvoiceDetailStatus.QUOTE.getCode())
                        .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<PaymentInvoiceDetail> paymentInvoiceDetailList = paymentInvoiceDetailMapper.selectByExample(detailExample);
                paymentInvoiceDetailList.forEach(detail -> {
                    detail.setPaymentInvoiceId(null);
                    detail.setInvoiceStatus(InvoiceDetailStatus.UNQUOTED.getCode());
                    punishmentService.updatePunishmentAccountStatus(detail, PurchaseContractPunishmentAccountStatusEnum.NOT_ACCOUNTED.getCode());
                    paymentInvoiceDetailMapper.updateByPrimaryKey(detail);
                });
            }


            //生成AP发票
            StringBuilder code = new StringBuilder();
            BigDecimal invoiceAmount = BigDecimal.ZERO;
            BigDecimal finedAmount = BigDecimal.ZERO;
            BigDecimal taxAmount = BigDecimal.ZERO;
            Date dueDate = null;

            //拼接ap发票号
            PaymentInvoiceDetailExample example = new PaymentInvoiceDetailExample();
            example.createCriteria().andDeletedFlagEqualTo(false).andIdIn(detailIds);
            example.setOrderByClause("invoice_detail_code * 1 asc");
            List<PaymentInvoiceDetail> paymentInvoiceDetails = paymentInvoiceDetailMapper.selectByExample(example);
            code = code.append("PAM" + paymentInvoiceDetails.get(0).getInvoiceDetailCode());
            if (paymentInvoiceDetails.size() > 1) {
                code.append("-" + paymentInvoiceDetails.get(paymentInvoiceDetails.size() - 1).getInvoiceDetailCode());
            }
            for (PaymentInvoiceDetail detail : paymentInvoiceDetails) {
                //是否引用了税票
                if (dto.getIsRel() == 1) {
                    invoiceAmount = invoiceAmount.add(detail.getTaxIncludedPrice());
                    if (null != detail.getDueDate()) {
                        dueDate = Objects.isNull(dueDate) ? detail.getDueDate() : (dueDate.compareTo(detail.getDueDate()) > 0 ? detail.getDueDate() : dueDate);
                    }
                } else {
                    finedAmount = finedAmount.add(detail.getTaxIncludedPrice());
                }
                taxAmount = taxAmount.add(detail.getTaxAmount());
            }

            if(ListUtils.isNotEmpty(paymentInvoiceDetails)){
                BigDecimal taxRate = paymentInvoiceDetails.get(0).getTaxRate().stripTrailingZeros().stripTrailingZeros();
                String taxCode = taxCodeQuery(taxRate.toPlainString());
                paymentInvoice.setTaxCode(taxCode);
            }

            // 附件，多个用,隔开
            paymentInvoice.setAnnex(dto.getAnnex());
            paymentInvoice.setTotalInvoiceIncludedPrice(invoiceAmount);//发票金额
            paymentInvoice.setAmount(finedAmount);//罚扣金额
            //原币金额（发票总金额（含税）（发票金额+罚扣金额））
            paymentInvoice.setTotalAmount(paymentInvoice.getTotalInvoiceIncludedPrice().add(paymentInvoice.getAmount()));
            paymentInvoice.setSurplusAmount(paymentInvoice.getTotalInvoiceIncludedPrice());//剩余可用金额
            paymentInvoice.setTaxAmount(taxAmount);//税额
            paymentInvoice.setTotalPayIncludedPrice(BigDecimal.ZERO);



            if (dto.getInvoiceDate() != null) {
                paymentInvoice.setInvoiceDate(dto.getInvoiceDate());//发票日期
            }
            if (StringUtils.isNotEmpty(dto.getPurchaseContractCode())) {
                paymentInvoice.setPurchaseContractCode(dto.getPurchaseContractCode());//采购合同编号
            }
            if (dto.getPurchaseContractId() != null) {
                paymentInvoice.setPurchaseContractId(dto.getPurchaseContractId());//采购合同id
            }
            if (dto.getOuId() != null) {
                paymentInvoice.setOuId(dto.getOuId());//业务实体id
            }
            paymentInvoice.setAvailable(1);
            paymentInvoice.setSource(PaymentInvoiceSourceEnum.PAYMENT.getCode());//来源
            paymentInvoice.setStatus(0);//审批状态
            paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.WAITING.code());//同步状态默认待同步
            paymentInvoice.setDueDate(dueDate);//发票到期日
            paymentInvoice.setGlDate(dto.getGlDate());
            paymentInvoice.setFreezeStatus(dto.getFreezeStatus());
            paymentInvoice.setAccountEntryType(dto.getAccountEntryType());
            //发票入账说明
            paymentInvoice.setInvoiceEntryExplain(dto.getInvoiceEntryExplain());
            if (Objects.isNull(paymentInvoice.getCreateAt())) {
                paymentInvoice.setCreateAt(new Date());
                paymentInvoice.setCreateBy(SystemContext.getUserId());
            }
            if (paymentInvoice.getId() != null) {
                paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
            } else {
                //生成ap发票号
                paymentInvoice.setApInvoiceCode(uniqueCode(code.toString(), dto.getPurchaseContractId(), dto.getOuId()));
                paymentInvoice.setDeletedFlag(false);
                paymentInvoice.setSource(PaymentInvoiceSourceEnum.PAYMENT.getCode());
                //设置预算项目号
                Pair<String, String> budgetProjectNumberPair = basedataExtService.getBudgetProjectNumberByVendorId(paymentInvoice.getPurchaseContractId(), null, paymentInvoice.getOuId());
                if(Objects.nonNull(budgetProjectNumberPair)){
                    paymentInvoice.setItemNumber(budgetProjectNumberPair.getValue());
                }
                paymentInvoiceMapper.insertSelective(paymentInvoice);
            }

            //更新税票状态
            Map<String, Object> param = new HashMap<>();
            param.put("id", paymentInvoice.getId());
            param.put("detailIds", detailIds);
            param.put("userId", SystemContext.getUserId());
            paymentInvoiceExtMapper.batchUpdateDetailStatus(param);
            for (PaymentInvoiceDetail paymentInvoiceDetail : paymentInvoiceDetails) {
                punishmentService.updatePunishmentAccountStatus(paymentInvoiceDetail, PurchaseContractPunishmentAccountStatusEnum.ACCOUNTED.getCode());
            }
            return BeanConverter.copy(paymentInvoice, PaymentInvoiceDto.class);
        }else{
            //不开票罚扣处理
          return punishmentHandle(dto);
        }
    }

    /**
     * 根据税率查询税码
     * @param taxRate
     * @return
     */
    private String taxCodeQuery(String taxRate){
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", 1);
        param.put("pageSize", Integer.MAX_VALUE);
        param.put("taxType",1);
        param.put("taxValue",taxRate);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "finance/taxInfo/getTaxList", param);
        String res = restTemplate.getForObject(url, String.class);
        PageInfo<TaxInfoDto> data = JSON.parseObject(res, new TypeReference<PageInfo<TaxInfoDto>>() {
        });
        if (Objects.nonNull(data) && ListUtils.isNotEmpty(data.getList())) {
            return data.getList().get(0).getTaxCode();
        }
        return "";
    }

    /**
     * 不开票罚扣处理
     * @param dto
     */
    private PaymentInvoiceDto punishmentHandle(PaymentInvoiceDto dto) {
        //罚扣处理
        List<PaymentInvoiceDto.PunishmentInvoiceSubmit> punishmentInvoiceSubmitList = dto.getPunishmentInvoiceSubmitList();
        Guard.notNullOrEmpty(punishmentInvoiceSubmitList, "罚扣信息不能为空");
        PaymentInvoice paymentInvoice = new PaymentInvoice();
        //排除删除的罚扣
        List<Long> punishmentIdList = punishmentInvoiceSubmitList.stream()
                .filter(p->!p.isDeletedFlag()).map(PaymentInvoiceDto.PunishmentInvoiceSubmit::getPunishmentId).collect(Collectors.toList());
        List<Long> deletedPunishmentIdList = punishmentInvoiceSubmitList.stream()
                .filter(PaymentInvoiceDto.PunishmentInvoiceSubmit::isDeletedFlag).map(PaymentInvoiceDto.PunishmentInvoiceSubmit::getPunishmentId).collect(Collectors.toList());
        //检查罚扣关联罚扣配置入账科目是否配置
        List<PurchaseContractPunishmentConfig> purchaseContractPunishmentConfigs = punishmentExtMapper.selectPunishmentConfigList(punishmentIdList);
        if (ListUtils.isNotEmpty(purchaseContractPunishmentConfigs)) {
            for (PurchaseContractPunishmentConfig config : purchaseContractPunishmentConfigs) {
                if (StringUtils.isEmpty(config.getAccountingSubject()))
                    throw new BizException(Code.ERROR, "采购合同发票入账未找到有效的入账科目，请联系IT进行配置");
                if (!validateString(config.getAccountingSubject())) {
                    throw new BizException(Code.ERROR, "采购合同发票入账未找到有效的入账科目，请联系IT进行配置");
                }
            }
        }

        //如果是应付发票提交前单据检查则终止后续操作
        if(Objects.equals(dto.isDraftSubmitCheck(),Boolean.TRUE)){
            return null;
        }

        //生成AP发票
        StringBuilder code = new StringBuilder();
        BigDecimal invoiceAmount = BigDecimal.ZERO;
        BigDecimal finedAmount = BigDecimal.ZERO;
        BigDecimal taxAmount = BigDecimal.ZERO;
        Date dueDate = null;

        PurchaseContractPunishmentExample punishmentExample = new PurchaseContractPunishmentExample();
        punishmentExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(punishmentIdList);
        punishmentExample.setOrderByClause("code asc");
        List<PurchaseContractPunishment> purchaseContractPunishments = punishmentMapper.selectByExample(punishmentExample);
        if (ListUtils.isNotEmpty(purchaseContractPunishments)) {
            code = code.append("PAM-").append(purchaseContractPunishments.get(0).getCode());
            if (purchaseContractPunishments.size() > 1) {
                String laterCode = purchaseContractPunishments.get(purchaseContractPunishments.size() - 1).getCode();
                if(StringUtils.isNotEmpty(laterCode)){
                    laterCode = laterCode.replaceAll("[A-Za-z]", "");
                    code.append("-").append(laterCode);
                }
            }
            //取罚扣不含税金额
            invoiceAmount = purchaseContractPunishments.stream().map(PurchaseContractPunishment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 附件，多个用,隔开
        paymentInvoice.setAnnex(dto.getAnnex());
        paymentInvoice.setTotalInvoiceIncludedPrice(invoiceAmount.multiply(new BigDecimal(-1)));//发票金额
        paymentInvoice.setSurplusAmount(invoiceAmount.multiply(new BigDecimal(-1))); //剩余金额
        //paymentInvoice.setAmount(finedAmount);//罚扣金额
        paymentInvoice.setTotalPayIncludedPrice(BigDecimal.ZERO);
        paymentInvoice.setTaxAmount(taxAmount);
        paymentInvoice.setDueDate(dueDate);

        if (dto.getInvoiceDate() != null) {
            paymentInvoice.setInvoiceDate(dto.getInvoiceDate());//发票日期
        }
        if (StringUtils.isNotEmpty(dto.getPurchaseContractCode())) {
            paymentInvoice.setPurchaseContractCode(dto.getPurchaseContractCode());//采购合同编号
        }
        if (dto.getPurchaseContractId() != null) {
            paymentInvoice.setPurchaseContractId(dto.getPurchaseContractId());//采购合同id
        }
        if (dto.getOuId() != null) {
            paymentInvoice.setOuId(dto.getOuId());//业务实体id
        }

        paymentInvoice.setAvailable(1);
        paymentInvoice.setSource(PaymentInvoiceSourceEnum.PAYMENT.getCode());//来源
        paymentInvoice.setStatus(0);//审批状态
        paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.WAITING.code());//同步状态默认待同步
        paymentInvoice.setDueDate(dueDate);//发票到期日
        paymentInvoice.setGlDate(dto.getGlDate());
        paymentInvoice.setFreezeStatus(dto.getFreezeStatus());
        paymentInvoice.setAccountEntryType(dto.getAccountEntryType());

        //发票入账说明
        paymentInvoice.setInvoiceEntryExplain(dto.getInvoiceEntryExplain());
        if (Objects.isNull(paymentInvoice.getCreateAt())) {
            paymentInvoice.setCreateAt(new Date());
            paymentInvoice.setCreateBy(SystemContext.getUserId());
        }
        if (dto.getId() != null) {
            paymentInvoice.setId(dto.getId());
            if(Objects.isNull(paymentInvoice.getItemNumber())){
                Pair<String, String> budgetProjectNumberPair = basedataExtService.getBudgetProjectNumberByVendorId(paymentInvoice.getPurchaseContractId(), null, paymentInvoice.getOuId());
                if(Objects.nonNull(budgetProjectNumberPair)){
                    paymentInvoice.setItemNumber(budgetProjectNumberPair.getValue());
                }
            }
            paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
        } else {
            //生成ap发票号
            paymentInvoice.setApInvoiceCode(uniqueCode(code.toString(), dto.getPurchaseContractId(), dto.getOuId()));
            paymentInvoice.setDeletedFlag(false);
            paymentInvoice.setSource(PaymentInvoiceSourceEnum.PAYMENT.getCode());
            Pair<String, String> budgetProjectNumberPair = basedataExtService.getBudgetProjectNumberByVendorId(paymentInvoice.getPurchaseContractId(), null, paymentInvoice.getOuId());
            if(Objects.nonNull(budgetProjectNumberPair)){
                paymentInvoice.setItemNumber(budgetProjectNumberPair.getValue());
            }
            paymentInvoiceMapper.insertSelective(paymentInvoice);
        }

        Integer result = punishmentExtMapper.updatePunishmentPaymentInvoiceId(punishmentIdList,paymentInvoice.getId());
        if (result <= 0) {
            logger.error("应付发票保存-更新罚扣失败，请检查数据");
            throw new BizException(Code.ERROR, "应付发票保存-更新罚扣失败，请检查数据");
        }

        //将原来关联的罚扣设置为未入账，未开票
        if(ListUtils.isNotEmpty(deletedPunishmentIdList)){
            for (Long punishmentId : deletedPunishmentIdList) {
                PurchaseContractPunishment purchaseContractPunishment = punishmentMapper.selectByPrimaryKey(punishmentId);
                purchaseContractPunishment.setPaymentInvoiceId(null);
                purchaseContractPunishment.setAccountStatus(PurchaseContractPunishmentAccountStatusEnum.NOT_ACCOUNTED.getCode());
                purchaseContractPunishment.setInvoiceStatus(PurchaseContractPunishmentInvoiceStatusEnum.NOT_INVOICED.getCode());
                punishmentMapper.updateByPrimaryKey(purchaseContractPunishment);
            }
        }

        return BeanConverter.copy(paymentInvoice, PaymentInvoiceDto.class);
    }

    /**
     * 验证字符串是否符合规则：
     * 1. 必须有3个以上的小数点
     * 2. 第二个和第三个小数点之间必须有内容
     * @param input 需要验证的字符串
     * @return 是否符合规则
     */
    public static boolean validateString(String input) {
        if (input == null || input.isEmpty()) {
            return false;
        }

        // 检查是否有三个以上小数点
        String dotPattern = "\\.";
        String[] parts = input.split(dotPattern);
        if (parts.length < 4) {  // 如果分割后的数组长度小于4，说明小数点不足3个
            return false;
        }

        // 检查第二个和第三个小数点之间是否有内容
        try {
            String content = extractContent(input);
            return !content.isEmpty();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 提取第二个和第三个小数点之间的内容
     * @param input 输入字符串
     * @return 提取的内容
     * @throws IllegalArgumentException 如果输入字符串格式不正确
     */
    public static String extractContent(String input) {
        if (input == null || input.isEmpty()) {
            throw new IllegalArgumentException("Input string cannot be null or empty");
        }

        // 使用正则表达式匹配第二个和第三个小数点之间的内容
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("[^.]*\\.[^.]*\\.([^.]*)\\.");
        java.util.regex.Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            return matcher.group(1);
        } else {
            throw new IllegalArgumentException("Invalid string format");
        }
    }


    /**
     * 发票号生成规则调整（"OU+供应商+发票号"不允许重复），重复的情况，在发票后面加-1，同税票号往上递增，如PAM22010141-2
     *
     * @param code
     * @param contractId
     * @param ouId
     * @return
     */
    private String uniqueCode(String code, Long contractId, Long ouId) {
        if (contractId != null) {
            PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(contractId);
            if (contract != null) {
                int num = paymentInvoiceExtMapper.statisticalCount(code, ouId, contract.getVendorId());
                if (num > 0) {
                    return getCodeNotDuplicate(code, num);
                }
            }
        } else {
            logger.warn("{}应付发票对应的采购合同为空", code);
        }
        return code;
    }

    private String getCodeNotDuplicate(String code, int num) {
        String apInvoiceCode = code + "-" + num;
        PaymentInvoiceExample example = new PaymentInvoiceExample();
        example.createCriteria().andApInvoiceCodeEqualTo(apInvoiceCode);
        long count = paymentInvoiceMapper.countByExample(example);
        if (count > 0) {
            return getCodeNotDuplicate(code, ++num);
        }
        return apInvoiceCode;
    }

    /**
     * 校验总账日期是否在会计期间内
     *
     * @param ouId
     * @param glDate
     */
    public void checkGlDate(Long ouId, Date glDate) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "glPeriod/checkGlDate";
        url += "?ouId=" + ouId;
        url += "&glDate=" + DateUtils.format(glDate);
        DataResponse response = restTemplate.getForObject(url, DataResponse.class);
        if (response == null || response.getCode() != 0) {
            throw new BizException(Code.ERROR, "总账日期校验失败");
        }
        Boolean isExist = (Boolean) response.getData();
        if (!Boolean.TRUE.equals(isExist)) {
            throw new BizException(Code.ERROR, String.format("\"%s\"不在打开的总账期间范围内",
                    DateUtils.formatDate(glDate)));
        }
    }

    @Override
    public List<PaymentInvoiceFreezeRecordDto> getFreezePage(PaymentInvoiceFreezeRecordDto query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        PaymentInvoiceFreezeRecordExample example = new PaymentInvoiceFreezeRecordExample();
        example.createCriteria().andPaymentInvoiceIdEqualTo(query.getPaymentInvoiceId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        example.setOrderByClause("freeze_time desc");
        List<PaymentInvoiceFreezeRecord> list = paymentInvoiceFreezeRecordMapper.selectByExample(example);
        return BeanConverter.copy(list, PaymentInvoiceFreezeRecordDto.class);
    }

    @Transactional
    @Override
    public Boolean freeze(PaymentInvoiceFreezeRecord paymentInvoiceFreezeRecord) {
        Asserts.notEmpty(paymentInvoiceFreezeRecord.getPaymentInvoiceId(), ErrorCode.CTC_PAYMENT_INVOICE_ID_NOT_NULL);
        Asserts.notEmpty(paymentInvoiceFreezeRecord.getFreezeType(), ErrorCode.CTC_PAYMENT_INVOICE_FREEZE_TYPE_NOT_NULL);
        Asserts.notEmpty(paymentInvoiceFreezeRecord.getFreezeReason(), ErrorCode.CTC_PAYMENT_INVOICE_FREEZE_REASON_NOT_NULL);
        // 并发时候的key值
        String lockKey = Constants.DistributedLockKey.DISTRIBUTED_LOCK_REQUIREMENT_CLOSE;
        try {
            if (DistributedCASLock.lock(lockKey, String.valueOf(paymentInvoiceFreezeRecord.getPaymentInvoiceId()), MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                return dealWithFreezePaymentInvoice(paymentInvoiceFreezeRecord, SystemContext.getUserId());
            }
        } catch (Exception e) {
            logger.error("应付发票冻结/取消冻结异常异常", e);
            throw new BizException(Code.ERROR, e.getMessage());
        } finally {
            DistributedCASLock.unLock(lockKey, String.valueOf(paymentInvoiceFreezeRecord.getPaymentInvoiceId()));
        }
        return false;
    }

    @Override
    public String queryPaymentInvoiceInfo(Long ouId, String createDate, String apInvoiceCode) {
        Map<String, String> paramMap = new HashMap();
//        paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(ouId));
        paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(ouId));
        //查询时间取发票写入的时间
//        paramMap.put(EsbConstant.ERP_IP_P02, createDate);
        paramMap.put(EsbConstant.ERP_SDP_P02, createDate);
//        paramMap.put(EsbConstant.ERP_IP_P03, apInvoiceCode);
        paramMap.put(EsbConstant.ERP_SDP_P03, apInvoiceCode);
//        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_026, paramMap);
        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_026, paramMap);
        logger.info("ERP响应参数为:{}", JSON.toJSONString(returnItemList));
        String invoiceVouNum = null;
        if (CollectionUtils.isNotEmpty(returnItemList)) {
            SdpTradeResultResponseEleDto vo = returnItemList.get(0);
            invoiceVouNum = vo.getC14();
        }
        logger.info("发票:{},对应的应付发票凭证号为:{}", apInvoiceCode, invoiceVouNum);
        return invoiceVouNum;
    }

    @Override
    public List<PaymentInvoiceDto> listWriteOffInvoice(Long purchaseContractId, Long vendorId, String currency, String apInvoiceCode) {
        List<PaymentInvoiceDto> invoices = paymentInvoiceExtMapper.selectWriteOffInvoice(purchaseContractId, vendorId, currency, apInvoiceCode);
        if (ListUtils.isNotEmpty(invoices)) {
            for (PaymentInvoiceDto invoiceDto : invoices) {
                //冻结信息
                if (Objects.equals(invoiceDto.getFreezeStatus(), PaymentInvoiceFreezeStatus.FROZE.getCode())) {
                    PaymentInvoiceFreezeRecordExample recordExample = new PaymentInvoiceFreezeRecordExample();
                    recordExample.createCriteria().andPaymentInvoiceIdEqualTo(invoiceDto.getId())
                            .andFreezeTypeEqualTo(FreezeType.FREEZE.getCode())
                            .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                    recordExample.setOrderByClause("freeze_time desc");
                    List<PaymentInvoiceFreezeRecord> recordList = paymentInvoiceFreezeRecordMapper.selectByExample(recordExample);
                    if (CollectionUtils.isNotEmpty(recordList)) {
                        invoiceDto.setFreezeName(recordList.get(0).getFreezeName());
                        invoiceDto.setFreezeReason(recordList.get(0).getFreezeReason());
                    } else {
                        invoiceDto.setFreezeName("");
                        invoiceDto.setFreezeReason("");
                        UserInfo userInfo = CacheDataUtils.findUserById(invoiceDto.getCreateBy());
                        if (userInfo != null) {
                            invoiceDto.setFreezeName(userInfo.getName());
                        }
                    }
                }
            }
        }
        return invoices;
    }

    private Boolean dealWithFreezePaymentInvoice(PaymentInvoiceFreezeRecord record, Long userBy) {
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(record.getPaymentInvoiceId());
        if (Objects.equals(record.getFreezeType(), paymentInvoice.getFreezeStatus())) {
            throw new BizException(Code.ERROR, "请勿重复操作");
        }
        // 插入冻结记录
        record.setFreezeId(userBy);
        UserInfo userInfo = CacheDataUtils.findUserById(userBy);
        if (userInfo != null) {
            record.setFreezeName(userInfo.getName());
        }
        record.setFreezeTime(new Date());
        record.setDeletedFlag(DeletedFlag.VALID.code());
        paymentInvoiceFreezeRecordMapper.insert(record);
        // 更新应付发票冻结状态
        paymentInvoice.setFreezeStatus(record.getFreezeType());
        paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
        return true;
    }

    /**
     * 参数校验
     *
     * @param dto 应付发票
     */
    private void checkParams(PaymentInvoiceDto dto) {
        if (dto.getGlDate() == null) {
            throw new BizException(Code.ERROR, "总账日期不能为空");
        }
    }

    private void checkPaymentInvoiceDetail(List<Long> detailIds, Long invoiceId, Long purchaseContractId) {
        PaymentInvoiceDetailExample example = new PaymentInvoiceDetailExample();
        example.createCriteria().andIdIn(detailIds);
        List<PaymentInvoiceDetail> paymentInvoiceDetails = paymentInvoiceDetailMapper.selectByExample(example);
        String errorMsgs = paymentInvoiceDetails.stream().filter(e ->
                        InvoiceDetailStatus.QUOTE.getCode().equals(e.getInvoiceStatus()) &&
                                (invoiceId == null || e.getPaymentInvoiceId() == null || invoiceId.compareTo(e.getPaymentInvoiceId()) != 0))
                .map(s -> {
                    String errorMsg = "";
                    PaymentInvoice invoice = paymentInvoiceMapper.selectByPrimaryKey(s.getPaymentInvoiceId());
                    if (invoice != null) {
                        errorMsg = String.format("发票：%s已被应付发票%s（状态=%s）引用，无需重复入账", s.getInvoiceDetailCode(), invoice.getApInvoiceCode(), PaymentInvoiceStatusEnum.getNameByCode(invoice.getStatus()));
                    }
                    return errorMsg;
                })
                .collect(Collectors.joining("；"));
        if (StringUtils.isNotEmpty(errorMsgs)) {
            throw new BizException(Code.ERROR, errorMsgs);
        }

        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);
        Asserts.notNull(purchaseContract, ErrorCode.CTC_PURCHASE_CONTRACT_NOT_EXISTS);
        //本次入账税票的含税金额汇总
        BigDecimal taxIncludedPriceSum = paymentInvoiceDetails.stream().map(PaymentInvoiceDetail::getTaxIncludedPrice).reduce(BigDecimal.ZERO, BigDecimalUtils::add);

        //按采购合同类型配置是否启用应付入账金额超合同金额校验
        checkAmountInvoiceOverContract(purchaseContractId, purchaseContract.getAmount(), taxIncludedPriceSum);

        // 明细行不能存在不一致的税率
        if (ListUtils.isNotEmpty(paymentInvoiceDetails) && paymentInvoiceDetails.stream().filter(p->Objects.equals(p.getDeletedFlag(),Boolean.FALSE))
                .map(PaymentInvoiceDetail::getTaxRate).distinct().count() > 1) {
            throw new BizException(Code.ERROR,"税率不一致，请分别入账");
        }
    }

    /**
     * 按采购合同类型配置是否启用应付入账金额超合同金额校验
     *
     * @param purchaseContractId
     * @param purchaseContractAmount
     * @param taxIncludedPriceSum    本次入账税票的含税金额汇总
     */
    @Override
    public void checkAmountInvoiceOverContract(Long purchaseContractId, BigDecimal purchaseContractAmount, BigDecimal taxIncludedPriceSum) {
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);
        Asserts.notNull(purchaseContract, ErrorCode.CTC_PURCHASE_CONTRACT_NOT_EXISTS);
        //是否引用新的发票
        Boolean ifQuoteInvoice = taxIncludedPriceSum != null ? Boolean.TRUE : Boolean.FALSE;
        //查询采购合同类型配置
        MaterialOutsourcingContractConfigExample contractConfigExample = new MaterialOutsourcingContractConfigExample();
        contractConfigExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andUnitIdEqualTo(SystemContext.getUnitId())
                .andOutsourcingContractTypeNameEqualTo(purchaseContract.getTypeName());
        List<MaterialOutsourcingContractConfig> materialOutsourcingContractConfigList = materialOutsourcingContractConfigMapper.selectByExample(contractConfigExample);
        if (!org.springframework.util.CollectionUtils.isEmpty(materialOutsourcingContractConfigList)) {
            MaterialOutsourcingContractConfig contractConfig = materialOutsourcingContractConfigList.get(0);
            if (Objects.equals(contractConfig.getAmountInvoiceOverContractFlag(), 1)) {
                //尾差
                BigDecimal diff = Optional.ofNullable(contractConfig.getAmountInvoiceOverContractDiff()).orElse(BigDecimal.ZERO);

                //合同含税金额
                purchaseContractAmount = Optional.ofNullable(purchaseContractAmount).orElse(BigDecimal.ZERO);

                //本次入账税票的含税金额汇总
                taxIncludedPriceSum = Optional.ofNullable(taxIncludedPriceSum).orElse(BigDecimal.ZERO);

                //已入账及正在入账的应付发票金额汇总
                PaymentInvoiceExample paymentInvoiceExample = new PaymentInvoiceExample();
                paymentInvoiceExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                        .andPurchaseContractIdEqualTo(purchaseContractId)
                        .andStatusIn(Arrays.asList(PaymentInvoiceStatusEnum.PASSING.getCode(), PaymentInvoiceStatusEnum.PASS.getCode()))
                        .andSourceNotEqualTo(PaymentInvoiceSourceEnum.REFUND.getCode());
                BigDecimal paymentInvoiceSum = paymentInvoiceMapper.selectByExample(paymentInvoiceExample).stream().map(PaymentInvoice::getTotalInvoiceIncludedPrice).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
                //查询当前采购合同生效罚扣金额（含税）总和
                BigDecimal totalPunishmentAmount = punishmentExtMapper.getTotalPunishmentAmountByPurchaseContractId(purchaseContract.getId());
                totalPunishmentAmount = Optional.ofNullable(totalPunishmentAmount).orElse(BigDecimal.ZERO);
                //合同金额（含税）+ 应付入账金额超合同金额允许尾差（如果有配置，加上，没有配置视为0）- 该合同下罚扣的金额（含税）
                if (taxIncludedPriceSum.add(paymentInvoiceSum).compareTo(purchaseContractAmount.add(diff).subtract(totalPunishmentAmount)) > 0) {
                    if (ifQuoteInvoice) {
                        throw new BizException(ErrorCode.ERROR, String.format("本次入账税票的含税金额汇总：%s + 已入账及正在入账的应付发票金额汇总：%s 已超过合同金额（含税）", taxIncludedPriceSum, paymentInvoiceSum));
                    } else {
                        throw new BizException(ErrorCode.ERROR, String.format("已入账及正在入账的应付发票金额汇总：%s 已超过合同金额（含税）", paymentInvoiceSum));
                    }
                }
            }
        }
    }

    @Override
    public PaymentInvoiceDto updateGlDate(PaymentInvoiceDto dto) {
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(dto.getId());
        if (null == paymentInvoice) {
            throw new BizException(Code.ERROR, "应付发票不存在！");
        }
        if (!Objects.equals(paymentInvoice.getErpStatus(), PaymentInvoiceErpStatusEnum.FAILURE.code())) {
            throw new BizException(Code.ERROR, "当前状态不可修改总账日期！");
        }
        paymentInvoice.setGlDate(dto.getGlDate());
        paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
        return BeanConverter.copy(paymentInvoice, PaymentInvoiceDto.class);
    }

    @Override
    public PaymentInvoiceDto updateDueDate(PaymentInvoiceDto dto) {
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(dto.getId());
        if (null == paymentInvoice) {
            throw new BizException(Code.ERROR, "应付发票不存在！");
        }
        //非财务不可修改
        paymentInvoice.setDueDate(dto.getDueDate());
        paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
        return BeanConverter.copy(paymentInvoice, PaymentInvoiceDto.class);
    }

    @Override
    public Long changeStatus(Long id, Integer status) {
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(id);
        if (paymentInvoice != null) {
            paymentInvoice.setStatus(status);
            if (status.equals(PaymentInvoiceStatusEnum.PASS.getCode())) {
                paymentInvoice.setAuditDate(new Date());//审批通过日期
                paymentInvoice.setInvoiceDate(paymentInvoice.getAuditDate());//发票日期 = 审批通过日期
                paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.CHECKING.code());
                HandleDispatcher.route(BusinessTypeEnums.PAYMENT_INVOICE.getCode(),
                        String.valueOf(paymentInvoice.getId()),
                        null,
                        null,
                        true,
                        null,
                        paymentInvoice.getOuId());
            } else if (status.equals(PaymentInvoiceStatusEnum.INVALID.getCode())) {
                //如果是作废就更改税票引用状态
                PaymentInvoiceDetailExample example = new PaymentInvoiceDetailExample();
                example.createCriteria().andDeletedFlagEqualTo(false).andPaymentInvoiceIdEqualTo(id);
                List<PaymentInvoiceDetail> paymentInvoiceDetails = paymentInvoiceDetailMapper.selectByExample(example);
                if (ListUtils.isNotEmpty(paymentInvoiceDetails)) {
                    paymentInvoiceDetails.stream().forEach(detail -> {
                        detail.setPaymentInvoiceId(null);
                        detail.setInvoiceStatus(InvoiceDetailStatus.UNQUOTED.getCode());
                        punishmentService.updatePunishmentAccountStatus(detail, PurchaseContractPunishmentAccountStatusEnum.NOT_ACCOUNTED.getCode());
                        paymentInvoiceDetailMapper.updateByPrimaryKey(detail);
                    });
                }
            }
            paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
            return paymentInvoice.getId();
        }
        return 0L;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean synchToErp(String ids) {
        List<Long> idList = StringUtils.splitToLong(ids, ",");
        List<Integer> statusList = Arrays.asList(PaymentInvoiceErpStatusEnum.FAILURE.code(),
                PaymentInvoiceErpStatusEnum.WAITING.code());
        PaymentInvoiceExample example = new PaymentInvoiceExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andIdIn(idList).andErpStatusIn(statusList);
        List<PaymentInvoice> paymentInvoiceList = paymentInvoiceMapper.selectByExample(example);
        for (PaymentInvoice paymentInvoice : paymentInvoiceList) {
            paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.CHECKING.code());
            paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
            if (PaymentInvoiceSourceEnum.REFUND.is(paymentInvoice.getSource())) {
                // 退款重付则走 退款应付发票写入 同步erp定时任务逻辑
                HandleDispatcher.route(BusinessTypeEnums.REFUND_PAYMENT_INVOICE.getCode(),
                        String.valueOf(paymentInvoice.getId()),
                        null,
                        null,
                        true);
            } else {
                HandleDispatcher.route(BusinessTypeEnums.PAYMENT_INVOICE.getCode(),
                        String.valueOf(paymentInvoice.getId()),
                        null,
                        null,
                        true,
                        null,
                        paymentInvoice.getOuId());
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response paymentInvoiceExcelVo(List<PaymentInvoiceExcelVo> list) {
        if (ListUtils.isEmpty(list)) {
            return Response.response();
        }
        boolean isPass = paymentInvoiceExcelDataValid(list);
        if (isPass) {
            for (PaymentInvoiceExcelVo vo : list) {
                PaymentInvoice paymentInvoice = vo.buildHeader();
                if (paymentInvoice.getId() == null) {
                    paymentInvoice.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    paymentInvoice.setCreateAt(new Date());
                    paymentInvoiceMapper.insertSelective(paymentInvoice);
                } else {
                    paymentInvoice.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    paymentInvoice.setUpdateAt(new Date());
                    paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
                }

                PaymentInvoiceDetail paymentInvoiceDetail = vo.buildDetail(paymentInvoice);
                if (paymentInvoiceDetail.getId() == null) {
                    paymentInvoiceDetail.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    paymentInvoiceDetail.setCreateAt(new Date());
                    paymentInvoiceDetailMapper.insertSelective(paymentInvoiceDetail);
                } else {
                    paymentInvoiceDetail.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    paymentInvoiceDetail.setUpdateAt(new Date());
                    paymentInvoiceDetailMapper.updateByPrimaryKeySelective(paymentInvoiceDetail);
                }
            }
            return Response.response();
        }
        return Response.dataResponse(list.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList()));
    }

    /**
     * 发票登录数据导入校验
     */
    private boolean paymentInvoiceExcelDataValid(List<PaymentInvoiceExcelVo> list) {

        list.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (list.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getErrMsg()))) {
            return false;
        }

        List<String> ppCodes = list.stream().map(PaymentInvoiceExcelVo::getPaymentPlanCode).collect(Collectors.toList());
        Map<String, Long> ppMap;
        if (ppCodes.isEmpty()) {
            ppMap = new HashMap<>(0);
        } else {
            ppMap = paymentPlanExtMapper.selectByCodes(ppCodes).stream()
                    .collect(Collectors.toMap(PaymentPlan::getCode, PaymentPlan::getId));
        }

        List<String> pcCodes = list.stream().map(PaymentInvoiceExcelVo::getPurchaseContractCode).collect(Collectors.toList());
        Map<String, PurchaseContract> pcMap = purchaseContractExtMapper.selectByCodes(pcCodes).stream()
                .collect(Collectors.toMap(PurchaseContract::getCode, e -> e, (e1, e2) -> e1));

        List<String> ouNames = list.stream().map(PaymentInvoiceExcelVo::getOuIdName).collect(Collectors.toList());
        Map<String, OperatingUnitDto> ouMap = getOperatingUnitByNames(ouNames).stream()
                .collect(Collectors.toMap(OperatingUnitDto::getOperatingUnitName, e -> e));

        List<String> vendorCodes = list.stream().map(PaymentInvoiceExcelVo::getVendorCode).collect(Collectors.toList());
        Map<String, List<VendorSiteBankDto>> vendorSiteBankGroup = getVendorSiteBankByCodes(vendorCodes).stream()
                .collect(Collectors.groupingBy(VendorSiteBankDto::getVendorCode));

        List<String> projectCodes = list.stream().map(PaymentInvoiceExcelVo::getProjectCode).collect(Collectors.toList());
        Map<String, Project> projectMap = projectExtMapper.selectByCodes(projectCodes).stream()
                .collect(Collectors.toMap(Project::getCode, e -> e));

        List<String> apInvoiceCodes = list.stream().map(PaymentInvoiceExcelVo::getApInvoiceCode).collect(Collectors.toList());
        PaymentInvoiceExample invoiceExample = new PaymentInvoiceExample();
        invoiceExample.createCriteria().andApInvoiceCodeIn(apInvoiceCodes).andDeletedFlagEqualTo(Boolean.FALSE);
        Map<String, Long> paymentInvoiceMap = paymentInvoiceMapper.selectByExample(invoiceExample).stream()
                .collect(Collectors.toMap(PaymentInvoice::getApInvoiceCode, PaymentInvoice::getId));

        PaymentInvoiceDetailExample invoiceDetailExample = new PaymentInvoiceDetailExample();
        invoiceDetailExample.createCriteria().andInvoiceDetailCodeIn(apInvoiceCodes).andDeletedFlagEqualTo(Boolean.FALSE);
        Map<String, Long> paymentInvoiceDetailMap = paymentInvoiceDetailMapper.selectByExample(invoiceDetailExample).stream()
                .collect(Collectors.toMap(PaymentInvoiceDetail::getInvoiceDetailCode, PaymentInvoiceDetail::getId));

        for (PaymentInvoiceExcelVo vo : list) {
            List<String> errMsgList = new ArrayList<>();

            vo.setInvoiceId(paymentInvoiceMap.get(vo.getApInvoiceCode()));
            vo.setInvoiceDetailId(paymentInvoiceDetailMap.get(vo.getApInvoiceCode()));

            if (StringUtils.isNotEmpty(vo.getPaymentPlanCode())) {
                Long paymentPlanId = ppMap.get(vo.getPaymentPlanCode());
                if (paymentPlanId != null) {
                    vo.setPaymentPlanId(paymentPlanId);
                } else {
                    errMsgList.add("付款计划不存在");
                }
            }

            PurchaseContract purchaseContract = pcMap.get(vo.getPurchaseContractCode());
            if (purchaseContract != null) {
                vo.setPurchaseContractId(purchaseContract.getId());
                vo.setPurchaseContractName(purchaseContract.getName());
            } else {
                errMsgList.add("采购合同不存在");
            }

            OperatingUnitDto ou = ouMap.get(vo.getOuIdName());
            if (ou != null) {
                vo.setOuId(ou.getId());
                List<VendorSiteBankDto> vendorSiteBankDtos = vendorSiteBankGroup.get(vo.getVendorCode());
                if (ListUtils.isNotEmpty(vendorSiteBankDtos)) {
                    Optional<VendorSiteBankDto> first = vendorSiteBankDtos.stream().filter(e ->
                            ou.getId().equals(e.getOperatingUnitId()) &&
                                    vo.getVendorName().equals(e.getVendorName()) &&
                                    vo.getVendorSiteCode().equals(e.getVendorSiteCode())).findFirst();
                    if (first.isPresent()) {
                        vo.setVendorId(first.get().getId());
                    } else {
                        errMsgList.add(vo.getVendorCode() + "供应商数据不匹配");
                    }
                } else {
                    errMsgList.add(vo.getVendorCode() + "供应商不存在");
                }
            } else {
                errMsgList.add("业务实体不存在");
            }

            Project project = projectMap.get(vo.getProjectCode());
            if (project != null) {
                vo.setProjectId(project.getId());
                vo.setProjectName(project.getName());
            } else {
                errMsgList.add("项目不存在");
            }

            vo.setErrMsg(String.join(",", errMsgList));
        }
        return list.stream().allMatch(e -> StringUtils.isEmpty(e.getErrMsg()));
    }

    /**
     * 根据业务实体名称查询业务实体
     */
    private List<OperatingUnitDto> getOperatingUnitByNames(List<String> names) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "operatingUnit/getOperatingUnitByNames";
        String res = restTemplate.postForObject(url, names, String.class);
        return JSON.parseObject(res, new TypeReference<List<OperatingUnitDto>>() {
        });
    }

    /**
     * 根据供应商编号查询供应商
     */
    private List<VendorSiteBankDto> getVendorSiteBankByCodes(List<String> codes) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "vendor/getVendorSiteBankByCodes";
        String res = restTemplate.postForObject(url, codes, String.class);
        return JSON.parseObject(res, new TypeReference<List<VendorSiteBankDto>>() {
        });
    }

    /**
     * 数据初始化-开票申请导入
     *
     * @param list
     */
    @Override
    @Transactional
    public void importInvoiceApply(List<InvoiceApplyExcelVo> list) {

        List<String> errorList = new ArrayList<>();

        Map<String, Customer> customerMap = new HashMap<>();
        Map<String, OperatingUnit> operatingUnitMap = new HashMap<>();
        Map<String, Contract> contractMap = new HashMap<>();

        /* 检查 */
        for (InvoiceApplyExcelVo vo : list) {
            if (!customerMap.containsKey(vo.getCustomerCode())) {
                Customer customer = getCustomerByCRMCode(vo.getCustomerCode());
                if (customer == null) {
                    errorList.add(String.format("客户'%s'不存在!", vo.getCustomerCode()));
                }
                customerMap.put(vo.getCustomerCode(), customer);
            }

            if (!operatingUnitMap.containsKey(vo.getOuId())) {
                String url = ModelsEnum.BASEDATA.getBaseUrl() + "operatingUnit/findOperatingUnitByName?name=" + vo.getOuId();
                ResponseEntity<String> forEntity = restTemplate.getForEntity(url, String.class);
                OperatingUnit operatingUnit = JSON.parseObject(forEntity.getBody(), new TypeReference<DataResponse<OperatingUnit>>() {
                }).getData();
                if (operatingUnit == null) {
                    errorList.add(String.format("OU不存在'%s'不存在!", vo.getOuId()));
                }
                operatingUnitMap.put(vo.getOuId(), operatingUnit);
            }

            if (!contractMap.containsKey(vo.getPamCodeNew())) {
                Contract contract = contractExtMapper.findByCode(vo.getPamCodeNew());
                if (contract == null) {
                    errorList.add(String.format("合同'%s'不存在!", vo.getPamCodeNew()));
                }
                contractMap.put(vo.getPamCodeNew(), contract);
            }
        }

        if (!CollectionUtils.isEmpty(errorList)) {
            throw new ApplicationBizException(org.apache.commons.lang3.StringUtils.join(errorList));
        }

        /* 保存 */
        for (InvoiceApplyExcelVo vo : list) {

            Customer customer = customerMap.get(vo.getCustomerCode());
            OperatingUnit operatingUnit = operatingUnitMap.get(vo.getOuId());
            Contract contract = contractMap.get(vo.getPamCodeNew());

            InvoicePlanDetail invoicePlanDetail = invoicePlanDetailExtMapper.findByPlanCode(vo.getPlanCode());

            InvoiceApplyHeader invoiceApplyHeader = vo.buildHeader(customer, operatingUnit, contract);

            invoiceApplyHeaderMapper.insertSelective(invoiceApplyHeader);

            InvoiceApplyDetails invoiceApplyDetails = vo.buildDetail(invoiceApplyHeader, contract, invoicePlanDetail);
            invoiceApplyDetailsMapper.insertSelective(invoiceApplyDetails);
        }


    }

    /**
     * 数据初始化-应收发票导入
     *
     * @param list
     */
    @Override
    @Transactional
    public void importInvoiceReceivable(List<InvoiceReceivableExcelVo> list) {


        List<String> errorList = new ArrayList<>();

        // 开票申请头
        Map<String, InvoiceApplyHeader> invoiceApplyHeaderMap = new HashMap<>();
        // 开票申请明细
        Map<String, InvoiceApplyDetails> invoiceApplyDetailMap = new HashMap<>();
        Map<String, Customer> customerMap = new HashMap<>();
        Map<String, OperatingUnit> operatingUnitMap = new HashMap<>();
        Map<String, Contract> contractMap = new HashMap<>();

        for (InvoiceReceivableExcelVo item : list) {
            if (!StringUtils.hasLength(item.getSerialNumber())) continue;
            InvoiceApplyDetails invoiceApplyDetail = null;
            if (!invoiceApplyDetailMap.containsKey(item.getApplyCode())) {
                invoiceApplyDetail = invoiceApplyDetailsExtMapper.findDetailByHeaderCode(item.getApplyCode());
                if (null == invoiceApplyDetail) {
                    errorList.add(String.format("开票申请号%s不存在!", item.getApplyCode()));
                }
                invoiceApplyDetailMap.put(item.getApplyCode(), invoiceApplyDetail);
            } else {
                invoiceApplyDetail = invoiceApplyDetailMap.get(item.getApplyCode());
            }

            if (null != invoiceApplyDetail) {
                if (!invoiceApplyHeaderMap.containsKey(item.getApplyCode())) {
                    InvoiceApplyHeader invoiceApplyHeader = invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyDetail.getApplyHeaderId());
                    if (null == invoiceApplyHeader) {
                        errorList.add(String.format("开票申请头%s不存在!", item.getApplyCode()));
                    }
                    invoiceApplyHeaderMap.put(item.getApplyCode(), invoiceApplyHeader);
                }
            }

            if (!operatingUnitMap.containsKey(item.getOuId())) {
                String url = ModelsEnum.BASEDATA.getBaseUrl() + "operatingUnit/findOperatingUnitByName?name=" + item.getOuId();
                ResponseEntity<String> forEntity = restTemplate.getForEntity(url, String.class);
                OperatingUnit operatingUnit = JSON.parseObject(forEntity.getBody(), new TypeReference<DataResponse<OperatingUnit>>() {
                }).getData();
                if (operatingUnit == null) {
                    errorList.add(String.format("OU不存在'%s'不存在!", item.getOuId()));
                }
                operatingUnitMap.put(item.getOuId(), operatingUnit);
            }

            if (!contractMap.containsKey(item.getContractId())) {
                Contract contract = contractExtMapper.findByCode(item.getContractId());
                if (contract == null) {
                    errorList.add(String.format("合同'%s'不存在!", item.getContractId()));
                }
                contractMap.put(item.getContractId(), contract);
            }

            if (!customerMap.containsKey(item.getCustomerCode())) {
                Customer customer = getCustomerByCRMCode(item.getCustomerCode());
                if (customer == null) {
                    errorList.add(String.format("客户'%s'不存在!", item.getCustomerCode()));
                }
                customerMap.put(item.getCustomerCode(), customer);
            }
        }

        if (!CollectionUtils.isEmpty(errorList)) {
            throw new ApplicationBizException(org.apache.commons.lang3.StringUtils.join(errorList));
        }

        for (InvoiceReceivableExcelVo item : list) {
            if (!StringUtils.hasLength(item.getSerialNumber())) continue;

            InvoiceApplyDetails invoiceApplyDetail = invoiceApplyDetailMap.get(item.getApplyCode());
            InvoiceApplyHeader invoiceApplyHeader = invoiceApplyHeaderMap.get(item.getApplyCode());
            Contract contract = contractMap.get(item.getContractId());
            Customer customer = customerMap.get(item.getCustomerCode());
            OperatingUnit operatingUnit = operatingUnitMap.get(item.getOuId());

            if (StringUtils.isNotEmpty(item.getDueDateStatusName())) {
                if (Objects.equals("未推送", item.getDueDateStatusName())) {
                    item.setDueDateStatus(0);
                } else if (Objects.equals("推送中", item.getDueDateStatusName())) {
                    item.setDueDateStatus(1);
                } else if (Objects.equals("推送失败", item.getDueDateStatusName())) {
                    item.setDueDateStatus(2);
                } else if (Objects.equals("已推送", item.getDueDateStatusName())) {
                    item.setDueDateStatus(3);
                } else {
                    item.setDueDateStatus(0);
                }
            } else {
                item.setDueDateStatus(0);
            }

            // 应收发票
            InvoiceReceivable invoiceReceivable = item.buildEntity(invoiceApplyDetail, contract, customer, operatingUnit);
            invoiceReceivable.setSource(0);// 默认0

            // 更新开票申请限额
            InvoiceApplyHeader applyHeader = new InvoiceApplyHeader();
            applyHeader.setId(invoiceApplyHeader.getId());
            applyHeader.setLimitPrice(invoiceReceivable.getTaxIncludedPrice());
            invoiceApplyHeaderMapper.updateByPrimaryKeySelective(applyHeader);
            // 更新开票申请明细单价
            InvoiceApplyDetails applyDetails = new InvoiceApplyDetails();
            applyDetails.setId(invoiceApplyDetail.getId());
            applyDetails.setUnit(invoiceReceivable.getUnit());
            applyDetails.setPrice(invoiceReceivable.getTaxIncludedPrice());
            invoiceApplyDetailsMapper.updateByPrimaryKeySelective(applyDetails);

            // 保存应收发票
            invoiceReceivableMapper.insertSelective(invoiceReceivable);

            // 新增开票申请行拆分明细表, 用于erp接口同步支持一对多
            InvoiceApplyDetailSplit applyDetailSplit = new InvoiceApplyDetailSplit();
            applyDetailSplit.setApplyDetailId(invoiceApplyDetail.getId());
            applyDetailSplit.setInvoiceReceivableId(invoiceReceivable.getId());
            applyDetailSplit.setTaxIncludedPrice(invoiceReceivable.getTaxIncludedPrice());
            applyDetailSplit.setRemainTaxIncludedPrice(invoiceReceivable.getTaxIncludedPrice());
            applyDetailSplit.setTaxRace(invoiceReceivable.getTaxRace());
            applyDetailSplit.setExclusiveOfTax(invoiceReceivable.getExclusiveOfTax());
            applyDetailSplit.setRemainExclusiveOfTax(invoiceReceivable.getExclusiveOfTax());
            applyDetailSplit.setTaxCode(invoiceReceivable.getTaxCode());
            applyDetailSplit.setQuantity(invoiceReceivable.getQuantity());
            applyDetailSplit.setRemainQuantity(invoiceReceivable.getQuantity());
            applyDetailSplit.setPrice(invoiceReceivable.getTaxIncludedPrice());
            applyDetailSplit.setUnit(invoiceReceivable.getUnit());
            applyDetailSplit.setRemark(invoiceApplyDetail.getRemark());
            applyDetailSplit.setProduct(invoiceReceivable.getProduct());
            applyDetailSplit.setModel(invoiceApplyDetail.getModel());
            applyDetailSplit.setProductTaxCode(invoiceApplyDetail.getProductTaxCode());
            applyDetailSplit.setProductTaxName(invoiceApplyDetail.getProductTaxName());
            applyDetailSplit.setContractId(invoiceApplyDetail.getContractId());
            applyDetailSplit.setContractCode(invoiceApplyDetail.getContractCode());
            applyDetailSplit.setApplyHeaderId(invoiceApplyDetail.getApplyHeaderId());
            applyDetailSplit.setPlanId(invoiceApplyDetail.getPlanId());
            applyDetailSplit.setPlanDetailId(invoiceApplyDetail.getPlanDetailId());
            applyDetailSplit.setDeletedFlag(Boolean.FALSE);
            invoiceApplyDetailSplitMapper.insert(applyDetailSplit);
        }
    }

    /**
     * ApplyDetailSplit数据修复脚本
     *
     * @param InvoiceReceivableId
     */
    @Override
    public void initApplyDetailSplit(Long InvoiceReceivableId) {

        // 应收发票
        InvoiceReceivable invoiceReceivable = invoiceReceivableMapper.selectByPrimaryKey(InvoiceReceivableId);
        InvoiceApplyDetails invoiceApplyDetail = invoiceApplyDetailsMapper.selectByPrimaryKey(invoiceReceivable.getApplyDetailId());
        InvoiceApplyHeader invoiceApplyHeader = invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyDetail.getApplyHeaderId());

        // 更新应收发票金额
        invoiceReceivable.setUnit("件");
        invoiceReceivable.setPrice(invoiceReceivable.getTaxIncludedPrice());
        invoiceReceivable.setLimitPrice(invoiceReceivable.getTaxIncludedPrice());
        invoiceReceivableMapper.updateByPrimaryKeySelective(invoiceReceivable);

        // 更新开票申请限额
        InvoiceApplyHeader applyHeader = new InvoiceApplyHeader();
        applyHeader.setId(invoiceApplyHeader.getId());
        applyHeader.setLimitPrice(invoiceReceivable.getTaxIncludedPrice());
        invoiceApplyHeaderMapper.updateByPrimaryKeySelective(applyHeader);
        // 更新开票申请明细单价
        InvoiceApplyDetails applyDetails = new InvoiceApplyDetails();
        applyDetails.setId(invoiceApplyDetail.getId());
        applyDetails.setUnit(invoiceReceivable.getUnit());
        applyDetails.setPrice(invoiceReceivable.getTaxIncludedPrice());
        invoiceApplyDetailsMapper.updateByPrimaryKeySelective(applyDetails);

        // 新增开票申请行拆分明细表, 用于erp接口同步支持一对多
        InvoiceApplyDetailSplit applyDetailSplit = new InvoiceApplyDetailSplit();
        applyDetailSplit.setApplyDetailId(invoiceApplyDetail.getId());
        applyDetailSplit.setInvoiceReceivableId(invoiceReceivable.getId());
        applyDetailSplit.setTaxIncludedPrice(invoiceReceivable.getTaxIncludedPrice());
        applyDetailSplit.setRemainTaxIncludedPrice(invoiceReceivable.getTaxIncludedPrice());
        applyDetailSplit.setTaxRace(invoiceReceivable.getTaxRace());
        applyDetailSplit.setExclusiveOfTax(invoiceReceivable.getExclusiveOfTax());
        applyDetailSplit.setRemainExclusiveOfTax(invoiceReceivable.getExclusiveOfTax());
        applyDetailSplit.setTaxCode(invoiceReceivable.getTaxCode());
        applyDetailSplit.setQuantity(invoiceReceivable.getQuantity());
        applyDetailSplit.setRemainQuantity(invoiceReceivable.getQuantity());
        applyDetailSplit.setPrice(invoiceReceivable.getTaxIncludedPrice());
        applyDetailSplit.setUnit(invoiceReceivable.getUnit());
        applyDetailSplit.setRemark(invoiceApplyDetail.getRemark());
        applyDetailSplit.setProduct(invoiceReceivable.getProduct());
        applyDetailSplit.setModel(invoiceApplyDetail.getModel());
        applyDetailSplit.setProductTaxCode(invoiceApplyDetail.getProductTaxCode());
        applyDetailSplit.setProductTaxName(invoiceApplyDetail.getProductTaxName());
        applyDetailSplit.setContractId(invoiceApplyDetail.getContractId());
        applyDetailSplit.setContractCode(invoiceApplyDetail.getContractCode());
        applyDetailSplit.setApplyHeaderId(invoiceApplyDetail.getApplyHeaderId());
        applyDetailSplit.setPlanId(invoiceApplyDetail.getPlanId());
        applyDetailSplit.setPlanDetailId(invoiceApplyDetail.getPlanDetailId());
        applyDetailSplit.setDeletedFlag(Boolean.FALSE);
        invoiceApplyDetailSplitMapper.insert(applyDetailSplit);
    }

    /**
     * 数据初始化-收款导入（回款分配、合同收款）
     *
     * @param list
     */
    @Override
    @Transactional
    public void importReceiptClaim(List<ReceiptClaimExcelVo> list) {
        List<String> errorList = new ArrayList<>();

        Map<String, InvoicePlanDetail> invoicePlanDetailMap = new HashMap<>();
        Map<String, Customer> customerMap = new HashMap<>();
        Map<String, OperatingUnit> operatingUnitMap = new HashMap<>();
        Map<String, Contract> contractMap = new HashMap<>();

        Map<String, ReceiptPlanDetail> receiptPlanDetailMap = new HashMap<>();
        Map<String, ReceiptPlan> receiptPlanMap = new HashMap<>();

        for (ReceiptClaimExcelVo vo : list) {
            if (!operatingUnitMap.containsKey(vo.getOuId())) {
                String url = ModelsEnum.BASEDATA.getBaseUrl() + "operatingUnit/findOperatingUnitByName?name=" + vo.getOuId();
                ResponseEntity<String> forEntity = restTemplate.getForEntity(url, String.class);
                OperatingUnit operatingUnit = JSON.parseObject(forEntity.getBody(), new TypeReference<DataResponse<OperatingUnit>>() {
                }).getData();
                if (operatingUnit == null) {
                    errorList.add(String.format("OU='%s'不存在!", vo.getOuId()));
                }
                operatingUnitMap.put(vo.getOuId(), operatingUnit);
            }

            Contract contract = null;
            if (!contractMap.containsKey(vo.getPamContractCode())) {
                contract = contractExtMapper.findByCode(vo.getPamContractCode());
                if (contract == null) {
                    errorList.add(String.format("合同'%s'不存在!", vo.getPamContractCode()));
                }
                contractMap.put(vo.getPamContractCode(), contract);
            } else {
                contract = contractMap.get(vo.getPamContractCode());
            }

            if (!customerMap.containsKey(vo.getCrmCustomerCode())) {
                Customer customer = getCustomerByCRMCode(vo.getCrmCustomerCode());
                if (customer == null) {
                    errorList.add(String.format("客户'%s'不存在!", vo.getCrmCustomerCode()));
                }
                customerMap.put(vo.getCrmCustomerCode(), customer);
            }

            if (StringUtils.isEmpty(vo.getInvoicePlanDetailCode())) {
                errorList.add(String.format("PAM合同编号:%s对应的合同不包含开票计划行号:%s!", vo.getPamContractCode(), vo.getInvoicePlanDetailCode()));
            }

            if (StringUtils.isEmpty(vo.getInvoicePlanDetailCode())) {
                errorList.add("开票计划行号不能为空");
            } else if (null != contract) {
                if (!invoicePlanDetailMap.containsKey(contract.getId() + "-" + vo.getInvoicePlanDetailCode())) {
                    InvoicePlanDetailExample invoicePlanDetailExample = new InvoicePlanDetailExample();
                    invoicePlanDetailExample.createCriteria().andContractIdEqualTo(contract.getId()).andCodeEqualTo(vo.getInvoicePlanDetailCode()).andDeletedFlagEqualTo(false);
                    List<InvoicePlanDetail> invoicePlanDetails = invoicePlanDetailMapper.selectByExample(invoicePlanDetailExample);

                    if (CollectionUtils.isEmpty(invoicePlanDetails)) {
                        errorList.add(String.format("PAM合同编号:%s对应的合同不包含开票计划行号:%s!", vo.getPamContractCode(), vo.getInvoicePlanDetailCode()));
                        invoicePlanDetailMap.put(contract.getId() + "-" + vo.getInvoicePlanDetailCode(), null);
                    } else {
                        invoicePlanDetailMap.put(contract.getId() + "-" + vo.getInvoicePlanDetailCode(), invoicePlanDetails.get(0));
                    }
                }

                // 更新回款计划行的实际回款金额
                ReceiptPlanDetail receiptPlanDetail = null;
                if (receiptPlanDetailMap.containsKey(contract.getId() + "-" + vo.getReceiptPlanDetailCode())) {
                    receiptPlanDetail = receiptPlanDetailMap.get(contract.getId() + "-" + vo.getReceiptPlanDetailCode());
                } else {
                    ReceiptPlanDetailExample receiptPlanDetailExample = new ReceiptPlanDetailExample();
                    ReceiptPlanDetailExample.Criteria receiptCriteria = receiptPlanDetailExample.createCriteria();
                    receiptCriteria.andContractIdEqualTo(contract.getId()).andCodeEqualTo(vo.getReceiptPlanDetailCode()).andDeletedFlagEqualTo(false);
                    List<ReceiptPlanDetail> ReceiptPlanDetails = receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample);
                    if (CollectionUtils.isNotEmpty(ReceiptPlanDetails)) {
                        receiptPlanDetail = ReceiptPlanDetails.get(0);
                    }
                    if (null == receiptPlanDetail) {
                        errorList.add(String.format("PAM合同编号:%s对应的合同不包含回款计划行编号:%s", vo.getPamContractCode(), vo.getReceiptPlanDetailCode()));
                    }
                    receiptPlanDetailMap.put(contract.getId() + "-" + vo.getReceiptPlanDetailCode(), receiptPlanDetail);
                }
                if (null != receiptPlanDetail) {
                    BigDecimal actualAmount = receiptPlanDetail.getActualAmount() != null ? receiptPlanDetail.getActualAmount() : BigDecimal.ZERO;
                    receiptPlanDetail.setActualAmount(actualAmount.add(vo.getPayAmount()));
                    if (receiptPlanDetail.getActualAmount().compareTo(receiptPlanDetail.getAmount()) > 0) {
                        errorList.add(String.format("PAM合同编号:%s对应的回款计划行编号:%s实际回款金额超过计划回款金额", vo.getPamContractCode(), vo.getReceiptPlanDetailCode()));
                    }

                    // 更新回款计划头的实际回款金额
                    ReceiptPlan receiptPlan = null;
                    if (receiptPlanMap.containsKey(receiptPlanDetail.getPlanId().toString())) {
                        receiptPlan = receiptPlanMap.get(receiptPlanDetail.getPlanId().toString());
                    } else {
                        receiptPlan = receiptPlanMapper.selectByPrimaryKey(receiptPlanDetail.getPlanId());
                        if (null == receiptPlan) {
                            errorList.add(String.format("PAM合同编号:%s对应的合同不包含回款计划行编号:%s", vo.getPamContractCode(), vo.getReceiptPlanDetailCode()));
                        }
                        receiptPlanMap.put(receiptPlanDetail.getPlanId().toString(), receiptPlan);
                    }
                    if (null != receiptPlan) {
                        actualAmount = receiptPlan.getActualAmount() != null ? receiptPlan.getActualAmount() : BigDecimal.ZERO;
                        receiptPlan.setActualAmount(actualAmount.add(vo.getPayAmount()));
                        if (receiptPlan.getActualAmount().compareTo(receiptPlan.getAmount()) > 0) {
                            errorList.add(String.format("PAM合同编号:%s对应的回款计划行编号:%s实际回款金额超过计划回款金额", vo.getPamContractCode(), vo.getReceiptPlanDetailCode()));
                        }
                    }
                }
            }
        }

        if (!CollectionUtils.isEmpty(errorList)) {
            throw new ApplicationBizException(org.apache.commons.lang3.StringUtils.join(errorList));
        }

        for (ReceiptClaimExcelVo vo : list) {

            Customer customer = customerMap.get(vo.getCrmCustomerCode());
            OperatingUnit operatingUnit = operatingUnitMap.get(vo.getOuId());
            Contract contract = contractMap.get(vo.getPamContractCode());
            InvoicePlanDetail invoicePlanDetail = invoicePlanDetailMap.get(contract.getId() + "-" + vo.getInvoicePlanDetailCode());

            if (receiptPlanDetailMap.containsKey(contract.getId() + "-" + vo.getReceiptPlanDetailCode())) {
                ReceiptPlanDetail receiptPlanDetail = receiptPlanDetailMap.get(contract.getId() + "-" + vo.getReceiptPlanDetailCode());
                receiptPlanDetailMapper.updateByPrimaryKeySelective(receiptPlanDetail);
                if (receiptPlanMap.containsKey(receiptPlanDetail.getPlanId().toString())) {
                    ReceiptPlan receiptPlan = receiptPlanMap.get(receiptPlanDetail.getPlanId().toString());
                    receiptPlanMapper.updateByPrimaryKeySelective(receiptPlan);
                    receiptPlanMap.remove(receiptPlanDetail.getPlanId().toString());
                }
                receiptPlanDetailMap.remove(contract.getId() + "-" + vo.getReceiptPlanDetailCode());
            }

            ReceiptClaim receiptClaim = vo.buildHeader(operatingUnit);
            receiptClaimMapper.insertSelective(receiptClaim);

            ReceiptClaimDetail receiptClaimDetail = vo.buildDetail(receiptClaim, customer);
            receiptClaimDetailMapper.insertSelective(receiptClaimDetail);

            ReceiptClaimContractRel receiptClaimContractRel = new ReceiptClaimContractRel();
            receiptClaimContractRel.setContractId(contract.getId());
            receiptClaimContractRel.setReceiptClaimDetailId(receiptClaimDetail.getId());
            receiptClaimContractRel.setInvoicePlanId(invoicePlanDetail.getPlanId());
            receiptClaimContractRel.setInvoicePlanDetailId(invoicePlanDetail.getId());
            receiptClaimContractRel.setCreateBy(SystemContext.getUserId());
            receiptClaimContractRel.setCreateAt(new Date());
            receiptClaimContractRel.setDeletedFlag(0);
            receiptClaimContractRel.setAllocatedAmount(receiptClaimDetail.getClaimAmount());
            receiptClaimContractRel.setLocalAllocatedAmount(receiptClaimDetail.getClaimAmount());
            receiptClaimContractRelMapper.insertSelective(receiptClaimContractRel);
        }
    }

    /**
     * 数据初始化-收款发票核销
     *
     * @param list
     */
    @Override
    @Transactional
    public void importReceiptClaimInvoiceRel(List<ReceiptClaimInoviceRelExcelVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ApplicationBizException("记录不能为空");
        }

        List<String> errorList = new ArrayList<>();
        Map<String, InvoiceReceivable> invoiceReceivableMap = new HashMap<>();
        Map<String, ReceiptClaimDetail> receiptClaimDetailMap = new HashMap<>();

        for (ReceiptClaimInoviceRelExcelVo excelVo : list) {
            // 检查必填字段
            List<String> checkNotNull = excelVo.checkNotNull();
            if (CollectionUtils.isNotEmpty(checkNotNull)) {
                errorList.addAll(checkNotNull);
                continue;
            }

            InvoiceReceivable invoiceReceivable = null;
            if (!invoiceReceivableMap.containsKey(excelVo.getInvoiceCode())) {
                InvoiceReceivableExample invoiceReceivableExample = new InvoiceReceivableExample();
                invoiceReceivableExample.createCriteria().andDeletedFlagEqualTo(0).andInvoiceCodeEqualTo(excelVo.getInvoiceCode());
                List<InvoiceReceivable> invoiceReceivableList = invoiceReceivableMapper.selectByExample(invoiceReceivableExample);
                if (CollectionUtils.isNotEmpty(invoiceReceivableList)) {
                    invoiceReceivable = invoiceReceivableList.get(0);
                }
                if (null == invoiceReceivable) {
                    errorList.add(String.format("应收发票号%s不存在", excelVo.getInvoiceCode()));
                }
                invoiceReceivableMap.put(excelVo.getInvoiceCode(), invoiceReceivable);
            } else {
                invoiceReceivable = invoiceReceivableMap.get(excelVo.getInvoiceCode());
            }

            ReceiptClaimDetail receiptClaimDetail = null;
            if (!receiptClaimDetailMap.containsKey(excelVo.getCashReceiptCode())) {
                ReceiptClaimDetailExample receiptClaimDetailExample = new ReceiptClaimDetailExample();
                receiptClaimDetailExample.createCriteria().andDeletedFlagEqualTo(0).andReceiptCodeEqualTo(excelVo.getCashReceiptCode());
                List<ReceiptClaimDetail> receiptClaimDetailList = receiptClaimDetailMapper.selectByExample(receiptClaimDetailExample);

                if (CollectionUtils.isNotEmpty(receiptClaimDetailList)) {
                    receiptClaimDetail = receiptClaimDetailList.get(0);
                }
                if (null == receiptClaimDetail) {
                    errorList.add(String.format("收款编号%s不存在", excelVo.getCashReceiptCode()));
                }
                receiptClaimDetailMap.put(excelVo.getCashReceiptCode(), receiptClaimDetail);
            } else {
                receiptClaimDetail = receiptClaimDetailMap.get(excelVo.getCashReceiptCode());
            }

            if (null == invoiceReceivable || null == receiptClaimDetail) {
                continue;
            }
            ReceiptClaimInvoiceRelExample receiptClaimInvoiceRelExample = new ReceiptClaimInvoiceRelExample();
            receiptClaimInvoiceRelExample.createCriteria().andDeletedFlagEqualTo(0).andInvoiceIdEqualTo(invoiceReceivable.getId()).andReceiptClaimDetailIdEqualTo(receiptClaimDetail.getId());
            if (receiptClaimInvoiceRelMapper.countByExample(receiptClaimInvoiceRelExample) > 0) {
                errorList.add(String.format("收款编号%s，应收发票号%s核销关系已存在", excelVo.getCashReceiptCode(), excelVo.getInvoiceCode()));
            }
        }

        if (!CollectionUtils.isEmpty(errorList)) {
            throw new ApplicationBizException(org.apache.commons.lang3.StringUtils.join(errorList));
        }

        for (ReceiptClaimInoviceRelExcelVo excelVo : list) {
            InvoiceReceivable invoiceReceivable = invoiceReceivableMap.get(excelVo.getInvoiceCode());
            ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMap.get(excelVo.getCashReceiptCode());

            ReceiptClaimInvoiceRel receiptRel = new ReceiptClaimInvoiceRel();

            receiptRel.setReceiptClaimDetailId(receiptClaimDetail.getId());
            receiptRel.setInvoiceId(invoiceReceivable.getId());

            receiptRel.setWriteOffAmount(excelVo.getWriteOffAmount());
            receiptRel.setWriteOffDate(excelVo.getWriteOffDate());
            // 核销人
            receiptRel.setWriteOffBy(Constants.ADMIM);
            // gl日期
            receiptRel.setGlDate(excelVo.getWriteOffDate());
            // 初始化数据的应该是已核销的状态
            receiptRel.setInvoiceWfStatus(WriteOffEnum.WRITTEN_OFF.getCode());
            receiptRel.setErpStatus(WriteOffEnum.ERP_PUSHED.getCode());//推送成功
            receiptRel.setErpMessage(null);
            receiptRel.setDeletedFlag(0);
            receiptRel.setCancelAt(null);
            receiptRel.setCancelBy(null);
            receiptClaimInvoiceRelMapper.insert(receiptRel);
        }

        //更新收款核销状态
        ReceiptClaimInvoiceRelDto param = new ReceiptClaimInvoiceRelDto();
        List<Long> receiptClaimDetailIdList = null;
        if (!receiptClaimDetailMap.isEmpty()) {
            receiptClaimDetailIdList = receiptClaimDetailMap.values().stream().map(a -> a.getId()).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(receiptClaimDetailIdList)) {
            param.setReceiptClaimDetailIdList(receiptClaimDetailIdList);
        }
        receiptClaimContractRelExtMapper.updateDetailStatus(param);
    }

    @Override
    @Transactional
    public Response importPaymentApply(List<PaymentApplyExcelVo> list) {

        boolean isValidPass = validPaymentApplyData(list);

        if (isValidPass) {
            List<PaymentRecord> paymentRecordList = new ArrayList<>();
            for (PaymentApplyExcelVo vo : list) {
                PaymentApply paymentApply = vo.buildPaymentApply();
                if (paymentApply.getId() == null) {
                    paymentApply.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    paymentApplyMapper.insertSelective(paymentApply);

                    if (paymentApply.getIsCharge() != 1) {
                        //插入payment_apply_invoice_rel
                        PaymentApplyInvoiceRel paymentApplyInvoiceRel = new PaymentApplyInvoiceRel();
                        paymentApplyInvoiceRel.setPaymentApplyId(paymentApply.getId());
                        paymentApplyInvoiceRel.setPaymentInvoiceId(vo.getPaymentInvoice().getId());
                        paymentApplyInvoiceRel.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                        paymentApplyInvoiceRel.setDeletedFlag(0);
                        paymentApplyInvoiceRelMapper.insertSelective(paymentApplyInvoiceRel);

                        //插入payment_apply_detail_rel
                        List<PaymentInvoiceDetail> paymentInvoiceDetails = vo.getPaymentInvoiceDetails();
                        paymentInvoiceDetails.forEach(e -> {
                            PaymentApplyDetailRel paymentApplyDetailRel = new PaymentApplyDetailRel();
                            paymentApplyDetailRel.setPaymentApplyId(paymentApply.getId());
                            paymentApplyDetailRel.setInvoiceDetailId(e.getId());
                            paymentApplyDetailRel.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                            paymentApplyDetailRel.setDeletedFlag(0);
                            paymentApplyDetailRelMapper.insertSelective(paymentApplyDetailRel);
                        });
                    }
                } else {
                    paymentApply.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    paymentApplyMapper.updateByPrimaryKeySelective(paymentApply);
                }

                if (paymentApply.getIsCharge() != 1) {
                    PaymentInvoice paymentInvoice = vo.getPaymentInvoice();
                    paymentInvoice.setPaymentApplyId(paymentApply.getId());
                    paymentInvoice.setPaymentApplyCode(paymentApply.getPaymentApplyCode());
                    paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
                }

                // paymentRecord 记录
                PaymentRecord paymentRecord = new PaymentRecord();
                paymentRecord.setId(vo.getPaymentRecordId());
                paymentRecord.setPaymentApplyId(paymentApply.getId());
                paymentRecord.setPurchaseContractCode(paymentApply.getPurchaseContractCode());
                paymentRecord.setPurchaseContractId(paymentApply.getPurchaseContractId());
                paymentRecord.setPayType(paymentApply.getIsCharge() == 1 ? "预付款" : "发票付款");
                paymentRecord.setPaymentStatus(paymentApply.getIsCharge() == 1 ? "0" : ""); // 未核销
                paymentRecord.setPenaltyAmount(BigDecimal.valueOf(0.00));//TODO 罚扣
                paymentRecord.setHappenDate(new Date());
                paymentRecord.setSubmitBy(paymentApply.getSubmitBy());
                paymentRecord.setSubmitPerson(paymentApply.getSubmitName());
                paymentRecord.setPayNum(paymentApply.getPaymentApplyCode());
                paymentRecord.setAmount(vo.getPaymentAmount());
                paymentRecord.setWriteOffAmount(BigDecimal.ZERO);
                paymentRecord.setPaymentDate(new Date());//TODO 实际付款日期
                paymentRecord.setErpInvoiceId(vo.getErpInvoiceId());
                paymentRecord.setErpInvoiceCode(vo.getErpInvoiceCode());
                paymentRecord.setDeletedFlag(0);
                paymentRecord.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                paymentRecord.setApInvoiceCode(vo.getInvoiceNum());
                paymentRecordList.add(paymentRecord);
            }
            // 增加 paymentRecord 记录
            if (!paymentRecordList.isEmpty()) {
                List<PaymentRecord> newPaymentRecords = paymentRecordList.stream().filter(e -> e.getId() == null)
                        .peek(e -> {
                            e.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                        }).collect(Collectors.toList());
                if (!newPaymentRecords.isEmpty()) {
                    paymentRecordExtMapper.batchInsert(newPaymentRecords);
                }
                List<PaymentRecord> oldPaymentRecords = paymentRecordList.stream().filter(e -> e.getId() != null)
                        .peek(e -> {
                            e.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                        }).collect(Collectors.toList());
                oldPaymentRecords.forEach(e -> paymentRecordMapper.updateByPrimaryKeySelective(e));
                List<String> planCodes = list.stream().map(PaymentApplyExcelVo::getPaymentPlanCode).collect(Collectors.toList());
                paymentPlanExtMapper.updateActualAmount(planCodes,
                        Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")),
                        new Date());
            }
            return Response.response();
        }
        return Response.dataResponse(list.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList()));
    }

    private boolean validPaymentApplyData(List<PaymentApplyExcelVo> list) {

        list.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (list.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getErrMsg()))) {
            return false;
        }

        List<String> planCodes = list.stream().map(PaymentApplyExcelVo::getPaymentPlanCode).collect(Collectors.toList());
        Map<String, PaymentPlan> paymentPlanMap = paymentPlanExtMapper.selectByCodes(planCodes).stream()
                .collect(Collectors.toMap(PaymentPlan::getCode, e -> e));

        List<String> vendorCodes = list.stream().map(PaymentApplyExcelVo::getVendorCode).collect(Collectors.toList());
        Map<String, List<VendorSiteBankDto>> vendorSiteBankGroup = getVendorSiteBankByCodes(vendorCodes).stream()
                .collect(Collectors.groupingBy(VendorSiteBankDto::getVendorCode));

        List<String> pcCodes = list.stream().map(PaymentApplyExcelVo::getContractCode).collect(Collectors.toList());
        Map<String, PurchaseContract> pcMap = purchaseContractExtMapper.selectByCodes(pcCodes).stream()
                .collect(Collectors.toMap(PurchaseContract::getCode, e -> e, (e1, e2) -> e1));

        List<Long> projectIds = pcMap.values().stream().map(PurchaseContract::getProjectId).collect(Collectors.toList());
        ProjectExample example = new ProjectExample();
        example.createCriteria().andIdIn(projectIds);
        Map<Long, Project> projectMap = projectMapper.selectByExample(example).stream().collect(Collectors.toMap(Project::getId, e -> e));

        List<String> ouNames = list.stream().map(PaymentApplyExcelVo::getOuName).collect(Collectors.toList());
        Map<String, OperatingUnitDto> ouMap = getOperatingUnitByNames(ouNames).stream()
                .collect(Collectors.toMap(OperatingUnitDto::getOperatingUnitName, e -> e));

        List<String> invoiceNums = list.stream().filter(e -> e.getIsChange() != 1)
                .map(PaymentApplyExcelVo::getInvoiceNum).distinct().collect(Collectors.toList());
        Map<String, PaymentInvoice> invoiceMap;
        Map<Long, List<PaymentInvoiceDetail>> invoiceDetailsMap;
        if (!invoiceNums.isEmpty()) {
            invoiceMap = paymentInvoiceExtMapper.selectByInvoiceCode(invoiceNums)
                    .stream().collect(Collectors.toMap(PaymentInvoice::getApInvoiceCode, e -> e, (e1, e2) -> e1));
            List<Long> paymentInvoiceIds = invoiceMap.values().stream().map(PaymentInvoice::getId).collect(Collectors.toList());
            if (!paymentInvoiceIds.isEmpty()) {
                invoiceDetailsMap = paymentInvoiceDetailExtMapper.selectByPaymentInvoiceIds(paymentInvoiceIds)
                        .stream().collect(Collectors.groupingBy(PaymentInvoiceDetail::getPaymentInvoiceId));
            } else {
                invoiceDetailsMap = new HashMap<>(0);
            }
        } else {
            invoiceMap = new HashMap<>(0);
            invoiceDetailsMap = new HashMap<>(0);
        }

        Map<String, DictDto> repaymentTypeMap = getDictByType("prepayment_type").stream()
                .collect(Collectors.toMap(DictDto::getName, e -> e));
        Map<String, DictDto> paymentTypeMap = getDictByType("payment_type").stream()
                .collect(Collectors.toMap(DictDto::getName, e -> e));

        List<String> paymentApplyCodes = list.stream().map(PaymentApplyExcelVo::getPaymentApplyCode).collect(Collectors.toList());
        PaymentApplyExample paymentApplyExample = new PaymentApplyExample();
        paymentApplyExample.createCriteria().andPaymentApplyCodeIn(paymentApplyCodes).andDeletedFlagEqualTo(0);
        Map<String, Long> paymentApplyMap = paymentApplyMapper.selectByExample(paymentApplyExample).stream()
                .collect(Collectors.toMap(PaymentApply::getPaymentApplyCode, PaymentApply::getId));

        List<Long> paymentApplyIds = new ArrayList<>(paymentApplyMap.values());
        Map<Long, Long> paymentRecordMap;
        if (!paymentApplyIds.isEmpty()) {
            PaymentRecordExample paymentRecordExample = new PaymentRecordExample();
            paymentRecordExample.createCriteria().andPaymentApplyIdIn(paymentApplyIds).andDeletedFlagEqualTo(0);
            paymentRecordMap = paymentRecordMapper.selectByExample(paymentRecordExample).stream()
                    .collect(Collectors.toMap(PaymentRecord::getPaymentApplyId, PaymentRecord::getId));
        } else {
            paymentRecordMap = new HashMap<>(0);
        }

        list.forEach(vo -> {
            List<String> errMsgList = new ArrayList<>();

            vo.setPaymentApplyId(paymentApplyMap.get(vo.getPaymentApplyCode()));
            if (vo.getPaymentApplyId() != null) {
                vo.setPaymentRecordId(paymentRecordMap.get(vo.getPaymentApplyId()));
            }

            PaymentPlan plan = paymentPlanMap.get(vo.getPaymentPlanCode());
            if (plan != null) {
                vo.setPaymentPlan(plan);
            } else {
                errMsgList.add("付款计划不存在");
            }

            OperatingUnitDto ou = ouMap.get(vo.getOuName());
            if (ou != null) {
                vo.setOperatingUnit(ou);
                List<VendorSiteBankDto> vendorSiteBankDtos = vendorSiteBankGroup.get(vo.getVendorCode());
                if (ListUtils.isNotEmpty(vendorSiteBankDtos)) {
                    Optional<VendorSiteBankDto> first = vendorSiteBankDtos.stream().filter(e ->
                            ou.getId().equals(e.getOperatingUnitId()) &&
                                    vo.getVendorName().equals(e.getVendorName()) &&
                                    vo.getVendorSiteCode().equals(e.getVendorSiteCode())).findFirst();
                    if (first.isPresent()) {
                        vo.setVendorSiteBankDto(first.get());
                    } else {
                        errMsgList.add(vo.getVendorCode() + "供应商数据不匹配");
                    }
                } else {
                    errMsgList.add(vo.getVendorCode() + "供应商不存在");
                }
            } else {
                errMsgList.add("业务实体不存在");
            }


            PurchaseContract contract = pcMap.get(vo.getContractCode());
            if (contract != null) {
                vo.setContract(contract);
                Project project = projectMap.get(contract.getProjectId());
                if (project != null) {
                    vo.setProject(project);
                } else {
                    errMsgList.add("合同关联的项目不存在");
                }
            } else {
                errMsgList.add("合同不存在");
            }

            if (vo.getIsChange() != 1) {
                PaymentInvoice paymentInvoice = invoiceMap.get(vo.getInvoiceNum());
                if (paymentInvoice != null) {
                    vo.setPaymentInvoice(paymentInvoice);
                    List<PaymentInvoiceDetail> paymentInvoiceDetails = invoiceDetailsMap.get(paymentInvoice.getId());
                    if (ListUtils.isNotEmpty(paymentInvoiceDetails)) {
                        vo.setPaymentInvoiceDetails(paymentInvoiceDetails);
                    } else {
                        errMsgList.add("发票行不存在");
                    }
                } else {
                    errMsgList.add("发票头不存在");
                }
                DictDto dictDto = paymentTypeMap.get(vo.getPaymentMethodCode());
                if (dictDto != null) {
                    vo.setPaymentMethod(dictDto);
                } else {
                    errMsgList.add("付款方式不存在");
                }
            } else {
                DictDto dictDto = repaymentTypeMap.get(vo.getPaymentMethodCode());
                if (dictDto != null) {
                    vo.setPaymentMethod(dictDto);
                } else {
                    errMsgList.add("预付款方式不存在");
                }
            }
            vo.setErrMsg(String.join(",", errMsgList));
        });
        return list.stream().allMatch(e -> StringUtils.isEmpty(e.getErrMsg()));
    }

    private List<DictDto> getDictByType(String type) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "ltcDict/listByType?type=" + type;
        String res = restTemplate.getForObject(url, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<DictDto>>() {
        });
    }

    private <T> void check(T t, String msg) {
        if (t == null) throw new BizException(Code.ERROR, msg);
    }

    private void packageDto(PaymentInvoiceDto dto) {
        //查询ou名称
        OperatingUnit operatingUnit = CacheDataUtils.findOuById(dto.getOuId());
        if (operatingUnit != null) {
            dto.setOuName(operatingUnit.getOperatingUnitName());
        }
        //创建人
        UserInfo userInfo = CacheDataUtils.findUserById(dto.getCreateBy());
        if (userInfo != null) {
            dto.setCreateByName(userInfo.getName());
        }
        UserInfo cancelUserInfo = CacheDataUtils.findUserById(dto.getErpCancelBy());
        //取消人
        if (cancelUserInfo != null) {
            dto.setErpCancelName(cancelUserInfo.getName());
        }
        PaymentInvoiceDetailExample invoiceDetailExample = new PaymentInvoiceDetailExample();
        invoiceDetailExample.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andPurchaseContractIdEqualTo(dto.getPurchaseContractId())
                .andPaymentInvoiceIdEqualTo(dto.getId());
        //采购发票列表
        List<PaymentInvoiceDetailDto> paymentInvoiceDetailDtoList =
                BeanConverter.copy(paymentInvoiceDetailMapper.selectByExample(
                        invoiceDetailExample), PaymentInvoiceDetailDto.class);
        if (ListUtils.isNotEmpty(paymentInvoiceDetailDtoList)) {
            String accountGroupDebit = "";
            //非销售业务场景-GL成本结转
            List<BusiSceneNonSaleDetailDto> detailDtoList = this.getBusiSceneDetailList(dto.getOuId());
            if (ListUtils.isNotEmpty(detailDtoList)) {
                accountGroupDebit = detailDtoList.get(0).getAccountGroupDebit();
            }
            //基础信息里加发票类型,多张票取第一个
            dto.setInvoiceType(paymentInvoiceDetailDtoList.get(0).getInvoiceType());
            for (PaymentInvoiceDetailDto paymentInvoiceDetailDto : paymentInvoiceDetailDtoList) {
                 paymentInvoiceDetailDto.setAccountGroupDebit(paymentInvoiceDetailDto.getAccountingSubjectProject());
                if (StringUtils.isNotEmpty(paymentInvoiceDetailDto.getInvoiceType())) {
                    paymentInvoiceDetailDto.setInvoiceTypeName("0".equals(paymentInvoiceDetailDto.getInvoiceType()) ? "增值税专票" : "增值税普票");
                }
            }
        }
        dto.setPaymentInvoiceDetailList(paymentInvoiceDetailDtoList);

        Optional.ofNullable(dto.getGscInvoiceNumber()).ifPresent(n->dto.setSourceSystemName(PaymentApplySourceNameEnum.GSC.getName()));
    }

    /**
     * 获取非销售业务场景配置
     *
     * @return
     */
    private List<BusiSceneNonSaleDetailDto> getBusiSceneDetailList(Long ouId) {
        BusiSceneNonSaleExample example = new BusiSceneNonSaleExample();
        example.createCriteria().andBusiSceneNameEqualTo("采购合同发票入账").andOuIdEqualTo(ouId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<BusiSceneNonSale> busiSceneNonSales = busiSceneNonSaleMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(busiSceneNonSales)) {
            Map<String, Object> param = new HashMap();
            param.put(Constants.Page.PAGE_NUM.toString(), 1);
            param.put(Constants.Page.PAGE_SIZE.toString(), 100);
            param.put("busiSceneNonSaleId", String.valueOf(busiSceneNonSales.get(0).getId()));
            PageInfo<BusiSceneNonSaleDetailDto> pageInfo = busiSceneNonSaleService.detailPage(param);
            return pageInfo.getList();
        }
        return new ArrayList<BusiSceneNonSaleDetailDto>();
    }

    @Override
    public Long save(PaymentInvoiceDto paymentInvoicedto) {
        paymentInvoicedto.setDeletedFlag(DeletedFlag.VALID.code());
        PaymentInvoice entity = BeanConverter.copy(paymentInvoicedto, PaymentInvoice.class);
        if (entity.getFreezeStatus() == null) {
            entity.setFreezeStatus(PaymentInvoiceFreezeStatus.UNFROZE.getCode());
        }
        //设置预算项目号
        if(Objects.isNull(entity.getItemNumber())){
            Pair<String, String> budgetProjectNumberPair = basedataExtService.getBudgetProjectNumberByVendorId(entity.getPurchaseContractId(), null, entity.getOuId());
            if(Objects.nonNull(budgetProjectNumberPair)){
                entity.setItemNumber(budgetProjectNumberPair.getValue());
            }
        }
        paymentInvoiceMapper.insert(entity);
        return entity.getId();
    }

    private void paymentWriteOff(PaymentInvoiceDto paymentInvoice, PaymentInvoice updatePaymentInvoice) {
        //获取原预付款申请单
        PaymentApply paymentApply = paymentApplyMapper.selectByPrimaryKey(paymentInvoice.getPaymentApplyId());
        Long originalPaymentId = paymentApply.getOriginalPaymentId();
        if (originalPaymentId == null) return;
        PaymentApply originalPaymentApply = paymentApplyMapper.selectByPrimaryKey(originalPaymentId);
        logger.info("付款申请单id：{}对应的付款申请为：[{}]", originalPaymentId, JsonUtils.toString(originalPaymentApply));
        if (Objects.isNull(originalPaymentApply)) return;

        //非预付款直接返回
        if (!Objects.equals(originalPaymentApply.getIsCharge(), PaymentApplyIsChargeEnum.ADVANCE.getCode())) return;

        //查询原预付款申请单的付款记录
        PaymentRecordQuery paymentRecordQuery = new PaymentRecordQuery();
        List<String> paymentStatusList = new ArrayList<>();
        paymentStatusList.add(PaymentRecordStatus.NOT.code());//未核销
        paymentStatusList.add(PaymentRecordStatus.PART.code());//部分核销
        paymentRecordQuery.setPaymentStatusList(paymentStatusList);
        paymentRecordQuery.setPaymentApplyId(originalPaymentApply.getId());
        List<PaymentRecord> paymentRecordList = paymentRecordService.selectList(paymentRecordQuery);
        if (CollectionUtils.isEmpty(paymentRecordList)) return;

        //对原预付款申请单的付款记录进行核销
        PaymentRecord updatePaymentRecord = null;
        for (PaymentRecord paymentRecord : paymentRecordList) {

            //查询付款记录可核销金额
            BigDecimal amount = paymentRecord.getAmount();//付款金额(含税)
            BigDecimal writeOffAmount = paymentRecord.getWriteOffAmount();//累计核销金额（含在途）
            if (writeOffAmount == null) {
                writeOffAmount = new BigDecimal(0);//初始化累计核销金额，怕计算空值错误
            }
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            BigDecimal ableWriteOffAmount = amount.subtract(writeOffAmount);//可核销金额
            if (ableWriteOffAmount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            //更新ap退款发票可用余额
            BigDecimal surplusAmount = paymentInvoice.getSurplusAmount();//剩余可用金额
            BigDecimal xiaohao = null;//消耗金额
            if (surplusAmount.compareTo(ableWriteOffAmount) >= 0) {
                xiaohao = ableWriteOffAmount;
            } else {
                xiaohao = surplusAmount;
            }
            surplusAmount = surplusAmount.subtract(xiaohao);
            updatePaymentInvoice.setSurplusAmount(surplusAmount);
            updatePaymentInvoice.setTotalPayIncludedPrice(paymentInvoice.getTotalPayIncludedPrice().add(xiaohao));

            //更新付款记录状态
            writeOffAmount = writeOffAmount.add(xiaohao);//核销金额（含在途）
            updatePaymentRecord = new PaymentRecord();
            updatePaymentRecord.setId(paymentRecord.getId());
            updatePaymentRecord.setWriteOffAmount(writeOffAmount);
            //核销状态(0未核销/3部分核销/4已核销)
            if (writeOffAmount.compareTo(BigDecimal.ZERO) == 0) {
                updatePaymentRecord.setPaymentStatus(PaymentRecordStatus.NOT.code());//未核销
            } else if (writeOffAmount.compareTo(BigDecimal.ZERO) > 0 && writeOffAmount.compareTo(paymentRecord.getAmount()) < 0) {
                updatePaymentRecord.setPaymentStatus(PaymentRecordStatus.PART.code());//部分核销
            } else if (writeOffAmount.compareTo(paymentRecord.getAmount()) == 0) {
                updatePaymentRecord.setPaymentStatus(PaymentRecordStatus.DONE.code());//已核销
            }
            updatePaymentRecord.setUpdateAt(new Date());
            paymentRecordMapper.updateByPrimaryKeySelective(updatePaymentRecord);

            //生成付款记录
            PaymentWriteOffRecord writeOffRecord = new PaymentWriteOffRecord();
            writeOffRecord.setPaymentRecordId(paymentRecord.getId());
            writeOffRecord.setPaymentInvoiceId(paymentInvoice.getId());
            writeOffRecord.setPaymentApplyId(originalPaymentApply.getId());
            writeOffRecord.setPaymentApplyCode(originalPaymentApply.getPaymentApplyCode());
            Date date = setWriteOffDay(paymentApply.getOuId());
            writeOffRecord.setAmount(paymentInvoice.getTotalInvoiceIncludedPrice());
            writeOffRecord.setApInvoiceCode(paymentInvoice.getApInvoiceCode());
            writeOffRecord.setHappenDate(new Date());
            writeOffRecord.setDeletedFlag(DeletedFlag.VALID.code());
            writeOffRecord.setStatus(PaymentWriteOffRecordStatus.SYNCHRONIZING.code());//同步中
            writeOffRecord.setHappenDate(date);
            paymentWriteOffRecordMapper.insert(writeOffRecord);

            //预付款核销记录写入
            HandleDispatcher.route(BusinessTypeEnums.PREPAY.getCode(), String.valueOf(writeOffRecord.getId()), null, null, false);
        }

        //更新发票状态
        updatePaymentInvoice.setUpdateAt(new Date());
        paymentInvoiceMapper.updateByPrimaryKeySelective(updatePaymentInvoice);

        //更新原预付款申请单核销状态
        PaymentApply updateOriginalPaymentApply = new PaymentApply();
        updateOriginalPaymentApply.setId(originalPaymentApply.getId());
        updateOriginalPaymentApply.setWriteOffStatus(PaymentApplyWriteOffStatus.PART.code());//部分核销
        //判断预付款是否完全核销
        if (isPrepayAllForApply(originalPaymentApply)) {
            updateOriginalPaymentApply.setWriteOffStatus(PaymentApplyWriteOffStatus.DONE.code());//已核销
        }
        paymentApplyMapper.updateByPrimaryKeySelective(updateOriginalPaymentApply);
    }

    @Override
    public void getInvoiceInfoFromErp(Map<String, String> paramMap, Long paymentInvoiceId) {
        //调erp接口
//        List<ERPMassQueryReturnVo> returnItemList = esbService.callEsbMassQuery(EsbConstant.ERP_PIFACECODE_026, paramMap);
        List<SdpTradeResultResponseEleDto> returnItemList = sdpCarrierServicel.callSdpMassQuery(EsbConstant.ERP_PIFACECODE_026, paramMap);
        PaymentInvoice updatePaymentInvoice = null;
        for (SdpTradeResultResponseEleDto item : returnItemList) {
            String invoiceNum = item.getC9();//发票号
            if (StringUtils.isNotEmpty(invoiceNum)) {
                String approvalStatus = item.getC7();//发票状态
                String description = item.getC22();//摘要
                String invoiceId = item.getC3();//发票ID

                PaymentInvoiceExample example = new PaymentInvoiceExample();
                PaymentInvoiceExample.Criteria criteria = example.createCriteria();
                criteria.andApInvoiceCodeEqualTo(invoiceNum)
                        .andStatusEqualTo(PaymentInvoiceStatusEnum.PASS.getCode())
                        .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                if (paymentInvoiceId != null) {
                    criteria.andIdEqualTo(paymentInvoiceId);
                }
                List<PaymentInvoiceDto> invoiceDtos = BeanConverter.copy(paymentInvoiceMapper.selectByExample(example), PaymentInvoiceDto.class);
                if (null == invoiceDtos || invoiceDtos.size() < 1) {
                    continue;
                }
                PaymentInvoiceDto invoice = invoiceDtos.get(0);
                logger.debug("查询发票状态：发票id【{}】，发票状态【{}】，发票对象信息【{}】", invoice.getId(), approvalStatus, JSON.toJSONString(invoice));

                updatePaymentInvoice = new PaymentInvoice();
                updatePaymentInvoice.setId(invoice.getId());
                if ("APPROVED".equals(approvalStatus)) {
                    //同步成功
                    updatePaymentInvoice.setErpStatus(1);
                    updatePaymentInvoice.setErpMsg(description);
                    updatePaymentInvoice.setErpInvoiceCode(invoiceId);
                    paymentInvoiceMapper.updateByPrimaryKeySelective(updatePaymentInvoice);

                    //如果是预付款退款重付生成的发票，原预付款付款记录要和退款重付生成的应付发票做核销，不能再选用
                    if (Objects.equals(invoice.getSource(), PaymentInvoiceSourceEnum.REFUND.getCode())) {
                        paymentWriteOff(invoice, updatePaymentInvoice);
                    }
                } else if ("CANCELLED".equals(approvalStatus)) {
                    //同步失败
                    updatePaymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.FAILURE.code());
                    updatePaymentInvoice.setErpMsg(description);
                    paymentInvoiceMapper.updateByPrimaryKeySelective(updatePaymentInvoice);
                }
            }
        }
    }

    @Override
    public void getInvoiceInfoAtTime(String lastUpdateDate,String apInvoiceCode) {
        //查询待验证的AP发票
        PaymentInvoiceDto query = new PaymentInvoiceDto();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(apInvoiceCode)){
            query.setApInvoiceCode(apInvoiceCode);
        }else{
            query.setErpStatus(0);
        }
        List<PaymentInvoiceDto> invoiceDtos = paymentInvoiceService.list(query);
        for (PaymentInvoiceDto invoiceDto : invoiceDtos) {
            Map<String, String> paramMap = new HashMap();
//            paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(invoiceDto.getOuId()));
            paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(invoiceDto.getOuId()));
            //查询时间取发票写入的时间
//            paramMap.put(EsbConstant.ERP_IP_P02, DateUtil.format(invoiceDto.getCreateAt()));
            paramMap.put(EsbConstant.ERP_SDP_P02, DateUtil.format(invoiceDto.getCreateAt()));
//            paramMap.put(EsbConstant.ERP_IP_P03, invoiceDto.getApInvoiceCode());
            paramMap.put(EsbConstant.ERP_SDP_P03, invoiceDto.getApInvoiceCode());
            getInvoiceInfoFromErp(paramMap, null);
        }
    }

    @Override
    public VendorSiteBankForDisplay getVendorInfo(PurchaseContract purchaseContract) {
        //补全供应商信息
        Long ouId = purchaseContract.getOuId();
        String erpVendorSiteId = purchaseContract.getErpVendorSiteId();
        String vendorCode = purchaseContract.getVendorCode();
        if (StringUtils.isNotEmpty(erpVendorSiteId) && StringUtils.isNotEmpty(vendorCode)) {
            VendorSiteBankForDisplay vendorSiteBankInfo = basedataExtService.getVendorSiteBankStatus(ouId,
                    vendorCode,
                    erpVendorSiteId);
            if (vendorSiteBankInfo != null) {
                return vendorSiteBankInfo;
            }
        }
        return null;
    }


    /**
     * 作废
     *
     * @param paymentInvoiceId 应付发票ID
     * @return
     */
    @Override
    @Transactional
    public Boolean invalid(Long paymentInvoiceId) {

        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(paymentInvoiceId);
        if (null == paymentInvoice) {
            throw new ApplicationBizException("查询不到对应的应付发票");
        }

        // 作废检查
        checkInvalid(paymentInvoice);

        // 作废应付发票
        paymentInvoice.setStatus(PaymentInvoiceStatusEnum.INVALID.getCode());
        paymentInvoiceMapper.updateByPrimaryKey(paymentInvoice);

        // 删除关联的税票
        PaymentInvoiceDetailExample detailExample = new PaymentInvoiceDetailExample();
        detailExample.createCriteria().andPaymentInvoiceIdEqualTo(paymentInvoiceId);
        List<PaymentInvoiceDetail> paymentInvoiceDetailList = paymentInvoiceDetailMapper.selectByExample(detailExample);
        if (!CollectionUtils.isEmpty(paymentInvoiceDetailList)) {
            paymentInvoiceDetailList.stream().forEach(detail -> {
                // 删除关联的税票
                detail.setPaymentInvoiceId(null);
                detail.setInvoiceStatus(InvoiceDetailStatus.UNQUOTED.getCode());
                punishmentService.updatePunishmentAccountStatus(detail, PurchaseContractPunishmentAccountStatusEnum.NOT_ACCOUNTED.getCode());
                paymentInvoiceDetailMapper.updateByPrimaryKey(detail);
            });
        }
        //删除罚扣记录的关联
        punishmentService.removePaymentInvoicePunishmentAssociation(paymentInvoice);

        //GSC来源的应付发票取消信息推送
        cancelInvoiceGscHandle(paymentInvoiceId);

        // 如果已经归集,生成负数的归集成本
        if (Objects.equals(paymentInvoice.getCollectionStatus(), 1)) {
            saveMaterialCostOut(paymentInvoice);
        }
        return true;
    }

    private void saveMaterialCostOut(PaymentInvoice paymentInvoice) {
        // 查询采购合同
        PurchaseContract purchaseContract =
                purchaseContractMapper.selectByPrimaryKey(paymentInvoice.getPurchaseContractId());
        if (purchaseContract == null) {
            return;
        }
        // 查询项目
        Project project = projectMapper.selectByPrimaryKey(purchaseContract.getProjectId());
        if (project == null) {
            return;
        }
        // 查询项目类型
        ProjectType projectType = projectTypeMapper.selectByPrimaryKey(project.getType());
        if (projectType == null) {
            return;
        }
        // 保存负数成本归集记录
        CostCollection collection = new CostCollection();
        collection.setProjectId(project.getId());
        collection.setProjectCode(project.getCode());
        collection.setProjectName(project.getName());
        collection.setProjectType(projectType.getName());
        collection.setType(CostCollectionEnum.NORMAL_TYPE.getCode());//正常
        collection.setOuId(project.getOuId());
        collection.setOuName(CacheDataUtils.findOuById(collection.getOuId()).getOperatingUnitName());
        collection.setCollectionDate(new Date());
        collection.setCarryStatus(0);

        collection.setProjectId(project.getId());
        collection.setProjectCode(project.getCode());
        collection.setProjectName(project.getName());
        ProjectType type = projectTypeMapper.selectByPrimaryKey(project.getType());
        if (null != type) {
            collection.setProjectType(type.getName());
            collection.setCostMethodMain(type.getCostMethod());
        }
        collection.setStatus(0);
        collection.setType(CostCollectionEnum.NORMAL_TYPE.getCode());
        collection.setOuId(project.getOuId());
        collection.setOuName(CacheDataUtils.findOuById(collection.getOuId()).getOperatingUnitName());
        collection.setCollectionDate(new Date());
        collection.setCarryStatus(0);
        collection.setMaterialActualCost(BigDecimal.ZERO);
        collection.setInnerLaborCost(BigDecimal.ZERO);
        collection.setOuterLaborCost(BigDecimal.ZERO);
        collection.setFeeCost(BigDecimal.ZERO);
        collection.setMaterialDifferenceCost(BigDecimal.ZERO);
        collection.setMaterialDifferenceCost(BigDecimal.ZERO);
        collection.setCurrency("CNY");
        collection.setDeletedFlag(Boolean.FALSE);
        // 发票含税金额
        BigDecimal totalInvoiceIncludedPrice =
                Optional.ofNullable(paymentInvoice.getTotalInvoiceIncludedPrice()).orElse(BigDecimal.ZERO).negate();
        // 发票税额
        BigDecimal taxAmount = Optional.ofNullable(paymentInvoice.getTaxAmount()).orElse(BigDecimal.ZERO).negate();
        // 汇率
        BigDecimal conversionRate = Optional.ofNullable(purchaseContract.getConversionRate()).orElse(BigDecimal.ONE);
        // 本位币金额
        BigDecimal localCurrencyAmount = totalInvoiceIncludedPrice.subtract(taxAmount)
                .multiply(conversionRate)
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        collection.setMaterialOutsourceCost(localCurrencyAmount);
        collection.setCostDate(paymentInvoice.getErpCancelDate());
        costCollectionMapper.insert(collection);

        // 保存物料外包成本明细
        //插明细表
        MaterialOutsourceCostDetail build = Builder.of(MaterialOutsourceCostDetail::new)
                .with(MaterialOutsourceCostDetail::setCostCollectionId, collection.getId())
                .with(MaterialOutsourceCostDetail::setApInvoiceCode, paymentInvoice.getApInvoiceCode())
                .with(MaterialOutsourceCostDetail::setPurchaseContractCode, paymentInvoice.getPurchaseContractCode())
                .with(MaterialOutsourceCostDetail::setPurchaseContractName, purchaseContract.getName())
                .with(MaterialOutsourceCostDetail::setInvoiceAmount, totalInvoiceIncludedPrice)
                .with(MaterialOutsourceCostDetail::setTaxAmount, taxAmount)
                .with(MaterialOutsourceCostDetail::setCreateAt, new Date())
                .with(MaterialOutsourceCostDetail::setDeletedFlag, 0)
                .with(MaterialOutsourceCostDetail::setVendorId, purchaseContract.getVendorId())
                .with(MaterialOutsourceCostDetail::setVendorCode, purchaseContract.getVendorCode())
                .with(MaterialOutsourceCostDetail::setVendorSiteCode, purchaseContract.getVendorSiteCode())
                .with(MaterialOutsourceCostDetail::setVendorName, purchaseContract.getVendorName())
                .with(MaterialOutsourceCostDetail::setInvoiceCurrency, purchaseContract.getCurrency())
                .with(MaterialOutsourceCostDetail::setLocalCurrency, "CNY") // todo 发票外包方案暂未实现，默认CNY
                .with(MaterialOutsourceCostDetail::setLocalCurrencyAmount, localCurrencyAmount)
                .with(MaterialOutsourceCostDetail::setInvoiceCurrency, purchaseContract.getCurrency())
                .with(MaterialOutsourceCostDetail::setConversionRate, conversionRate)
                .with(MaterialOutsourceCostDetail::setPaymentInvoiceId, paymentInvoice.getId())
                .with(MaterialOutsourceCostDetail::setInvoiceDate,
                        paymentInvoice.getAuditDate() != null ? paymentInvoice.getAuditDate() :
                                paymentInvoice.getInvoiceDate())
                .with(MaterialOutsourceCostDetail::setType, MaterialOutsourceCostDetailType.PAYMENT_INVOICE.getCode())
                .build();
        materialOutsourceCostDetailMapper.insertSelective(build);
    }

    /**
     * 作废检查
     *
     * @param paymentInvoice
     */
    private void checkInvalid(PaymentInvoice paymentInvoice) {

        // 应付发票ERP同步状态 = “草稿“、“驳回“、“撤回“  允许作废
        // 应付发票状态 = “审批中“
        if (PaymentInvoiceStatusEnum.PASSING.getCode().equals(paymentInvoice.getStatus())) {
            throw new ApplicationBizException("应付发票正在审批中，请先进行审批，不能作废");
        }
        // 应付发票状态 = “已作废”
        if (PaymentInvoiceStatusEnum.INVALID.getCode().equals(paymentInvoice.getStatus())) {
            throw new ApplicationBizException("应付发票已作废，不能重复操作");
        }
        // 应付发票状态 = “审批通过”
        if (PaymentInvoiceStatusEnum.PASS.getCode().equals(paymentInvoice.getStatus())) {
            // 应付发票ERP取消状态 = “已取消” 可以作废
            // 应付发票ERP取消状态 = “取消中”
            if (PaymentInvoiceErpCancelStatusEnum.DOING.getCode().equals(paymentInvoice.getErpCancelStatus())) {
                throw new ApplicationBizException("应付发票正在取消中，不能作废");
            }
            // 应付发票ERP取消状态 = “取消失败”
            if (PaymentInvoiceErpCancelStatusEnum.FAIL.getCode().equals(paymentInvoice.getErpCancelStatus())) {
                throw new ApplicationBizException("应付发票未取消，不能作废");
            }
            // 应付发票ERP取消状态 = “未取消” 或者历史记录 = null
            if (null == paymentInvoice.getErpCancelStatus() || PaymentInvoiceErpCancelStatusEnum.NOT.getCode().equals(paymentInvoice.getErpCancelStatus())) {
                // 应付发票ERP同步状态 = “待同步“ 可以作废
                // 应付发票ERP同步状态 = “同步中“
                if (PaymentInvoiceErpStatusEnum.CHECKING.code().equals(paymentInvoice.getErpStatus())) {
                    throw new ApplicationBizException("应付发票正在同步ERP，不能作废");
                }
                // 应付发票ERP同步状态 = “同步成功“
                if (PaymentInvoiceErpStatusEnum.SUCCESS.code().equals(paymentInvoice.getErpStatus())) {
                    throw new ApplicationBizException("应付发票已成功同步ERP，不能作废");
                }
                // 应付发票ERP同步状态 = “同步失败“
                if (PaymentInvoiceErpStatusEnum.FAILURE.code().equals(paymentInvoice.getErpStatus())) {
                    // 付款申请【字段有裁剪】
                    List<PaymentApplyDto> paymentApplyDtoList = paymentApplyExtMapper.listByInvoiceId(paymentInvoice.getId());
                    // 未关联付款申请，允许作废
                    // 已关联付款申请
                    if (CollectionUtils.isEmpty(paymentApplyDtoList)) {
                        // 预付款申请数量
                        Long advanceCount = paymentApplyDtoList.stream()
                                .filter(a -> a.getIsCharge().intValue() == PaymentApplyIsChargeEnum.ADVANCE.getCode())
                                .count();
                        // 关联付款申请包含“预付款“
                        if (advanceCount.intValue() > 0) {
                            throw new ApplicationBizException("应付发票已被有效的付款申请关联，不能作废");
                        }
                        // 剩下的全是“非预付款”的付款申请
                        for (PaymentApplyDto paymentApply : paymentApplyDtoList) {
                            paymentApplyService.statusTransformation(paymentApply);
                            // 付款申请状态 != “作废“ 或 “取消支付“
                            if (!PaymentApplyBizStatus.CANCEL_PAY.getCode().equals(paymentApply.getBizStatus()) &&
                                    !PaymentApplyBizStatus.CANCEL.getCode().equals(paymentApply.getBizStatus())) {
                                throw new ApplicationBizException("应付发票已被有效的付款申请关联，不能作废");
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 付款发票ERP取消检查
     *
     * @param paymentInvoiceIds
     * @return
     */
    @Override
    public List<PaymentInvoceCheckCancelDto> checkCancelErpArray(String paymentInvoiceIds) {
        List<Long> ids = com.midea.pam.common.util.StringUtils.splitToLong(paymentInvoiceIds, ",");
        List<PaymentInvoceCheckCancelDto> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids)) {
            return result;
        }
        PaymentInvoceCheckCancelDto checkCancelDto;
        for (Long paymentInvoiceId : ids) {
            checkCancelDto = checkCancelErp(paymentInvoiceId);
            result.add(checkCancelDto);
        }
        return result;
    }

    /**
     * 付款发票ERP取消
     *
     * @param paymentInvoiceId 应付发票ID
     * @param erpCancelCause   ERP取消原因
     */
    @Override
    public Boolean cancelErp(Long paymentInvoiceId, String erpCancelCause, Date erpCancelDate) {
        // 付款发票ERP取消检查
        PaymentInvoceCheckCancelDto checkResult = checkCancelErp(paymentInvoiceId);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(checkResult.getMessage())) {
            throw new ApplicationBizException(checkResult.getMessage());
        }
        PaymentInvoiceDto paymentInvoiceDto = getById(paymentInvoiceId);
        if (null == paymentInvoiceDto) {
            throw new BizException(ErrorCode.CTC_PAYMENT_APPLY_CODE_EXIST);
        }
        // 检查是否推送ERP（取消发票）
        if (checkSendCancelErp(paymentInvoiceDto)) {
            /**
             * 需要同步ERP
             */
            // 判断取消日期是否在打开期间
            checkPeriod(paymentInvoiceDto.getOuId(), erpCancelDate, paymentInvoiceId);
            PaymentInvoice paymentInvoice = new PaymentInvoice();
            //更新应付发票的同步状态
            paymentInvoice.setId(paymentInvoiceId);
            // ERP取消状态 = 取消中
            paymentInvoice.setErpCancelStatus(PaymentInvoiceErpCancelStatusEnum.DOING.getCode());
            // ERP取消时间
            paymentInvoice.setErpCancelDate(erpCancelDate);
            // ERP取消原因
            paymentInvoice.setErpCancelCause(erpCancelCause);
            // ERP取消人
            paymentInvoice.setErpCancelBy(SystemContext.getUserId());
            // ERP返回消息
            paymentInvoice.setErpMsg("");
            paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
            HandleDispatcher.route(BusinessTypeEnums.CANCEL_INVALID_PAYMENT_INVOICE.getCode(),
                    String.valueOf(paymentInvoiceId),
                    null,
                    null,
                    true);
        } else {
            /**
             * 不需要同步ERP
             */
            PaymentInvoice paymentInvoice = new PaymentInvoice();
            //更新应付发票的同步状态
            paymentInvoice.setId(paymentInvoiceId);
            // ERP取消状态 = 已取消
            paymentInvoice.setErpCancelStatus(PaymentInvoiceErpCancelStatusEnum.DONE.getCode());
            // ERP取消时间
            paymentInvoice.setErpCancelDate(erpCancelDate);
            // ERP取消原因
            paymentInvoice.setErpCancelCause(erpCancelCause);
            paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);

            // 作废应付发票
            invalid(paymentInvoice.getId());
        }
        return true;
    }

    private void cancelInvoiceGscHandle(Long paymentInvoiceId) {
        if(Objects.isNull(paymentInvoiceId)) return;
        //GSC税票申请取消回调
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(paymentInvoiceId);
        String gscInvoiceNumber = paymentInvoice.getGscInvoiceNumber();
        //判断当前应付发票是否关联GSC税票申请
        if(StringUtils.isNotEmpty(gscInvoiceNumber)){
            logger.info("GSC税票申请-应付发票取消,paymentInvoiceId:{}", paymentInvoiceId);
            GscPaymentInvoice gscPaymentInvoice = gscPaymentInvoiceExtMapper.getGscPaymentInvoiceByInvoiceNumber(gscInvoiceNumber);
            //删除GSC税票申请
            gscPaymentInvoiceDetailExtMapper.logicDeleteInvoiceDetailByInvoiceId(gscPaymentInvoice.getId());
            //设置GSC开票申请状态：作废
            gscPaymentInvoice.setStatus(AuditStatus.CANCEL.getCode());
            gscPaymentInvoiceMapper.updateByPrimaryKeySelective(gscPaymentInvoice);
            //推送GSC税票申请取消状态
            gscPaymentAndInvoiceStatusPushService.pushPaymentAndInvoiceStatus(GSCPaymentAndInvoiceStatusPushEnum.INVOICE_DETAIL_AP_CANCEL_STATUS_PUSH, gscPaymentInvoice.getId());
        }
    }

    /**
     * 付款发票ERP取消回调 APM-ERP-071
     *
     * @param id
     * @param isSuccess
     * @param msg
     * @param sourceId3
     */
    @Override
    public void cancelErpSyncCallback(Long id, boolean isSuccess, String msg, String sourceId3) {
        logger.info("APM-ERP-071 付款发票ERP取消回调 id={},isSuccess={},msg={},sourceid3=", id, isSuccess, msg, sourceId3);
        PaymentInvoiceExample invoiceExampleexample = new PaymentInvoiceExample();
        invoiceExampleexample.createCriteria().andErpInvoiceCodeEqualTo(String.valueOf(id));

        List<PaymentInvoice> paymentInvoiceList = paymentInvoiceMapper.selectByExample(invoiceExampleexample);
        if (!CollectionUtils.isEmpty(paymentInvoiceList)) {
            PaymentInvoice paymentInvoice = paymentInvoiceList.get(0);
            if (isSuccess) {
                paymentInvoice.setErpCancelStatus(PaymentInvoiceErpCancelStatusEnum.DONE.getCode());
                paymentInvoice.setErpCancelMsg(msg);
                paymentInvoiceMapper.updateByPrimaryKey(paymentInvoice);

                // 作废应付发票
                invalid(paymentInvoice.getId());
            } else {
                paymentInvoice.setErpCancelStatus(PaymentInvoiceErpCancelStatusEnum.FAIL.getCode());
                paymentInvoice.setErpCancelMsg(msg);
                paymentInvoiceMapper.updateByPrimaryKey(paymentInvoice);
            }
        } else {
            logger.error("APM-ERP-071 付款发票ERP取消回调 ERP发票号{}不存在", id);
        }

    }

    /**
     * 付款发票ERP取消检查
     *
     * @param paymentInvoiceId
     * @return
     */
    private PaymentInvoceCheckCancelDto checkCancelErp(Long paymentInvoiceId) {
        PaymentInvoceCheckCancelDto result = new PaymentInvoceCheckCancelDto();
        result.setId(paymentInvoiceId);
        PaymentInvoiceDto paymentInvoiceDto = getById(paymentInvoiceId);
        if (null == paymentInvoiceDto) {
            return result.setResult(paymentInvoiceId, false, "应付发票不存在，id=" + paymentInvoiceId);
        }
        // 应付发票状态 = “草稿“
        if (PaymentInvoiceStatusEnum.DRAFT.getCode().equals(paymentInvoiceDto.getStatus())) {
            return result.setResult(paymentInvoiceId, false, "应付发票未生效，如需取消，请打开应付发票详情，进行作废处理");
        }
        // 应付发票状态 = “审批中“
        if (PaymentInvoiceStatusEnum.PASSING.getCode().equals(paymentInvoiceDto.getStatus())) {
            return result.setResult(paymentInvoiceId, false, "应付发票正在审批中，请先进行审批，不能取消");
        }
        // 应付发票状态 = “驳回“
        if (PaymentInvoiceStatusEnum.REJECT.getCode().equals(paymentInvoiceDto.getStatus())) {
            return result.setResult(paymentInvoiceId, false, "应付发票被审批驳回，请重新编辑后提交或删除，不能取消");
        }
        // 应付发票状态 = “撤回“
        if (PaymentInvoiceStatusEnum.BACK.getCode().equals(paymentInvoiceDto.getStatus())) {
            return result.setResult(paymentInvoiceId, false, "应付发票已撤回审批，请重新编辑后提交或删除，不能取消");
        }
        // 应付发票状态 = “作废“
        if (PaymentInvoiceStatusEnum.INVALID.getCode().equals(paymentInvoiceDto.getStatus())) {
            return result.setResult(paymentInvoiceId, false, "应付发票已作废，无需取消");
        }
        // 应付发票状态 = “审批通过“
        if (PaymentInvoiceStatusEnum.PASS.getCode().equals(paymentInvoiceDto.getStatus())) {

            // 应付发票ERP取消状态 = “已取消”
            if (PaymentInvoiceErpCancelStatusEnum.DONE.getCode().equals(paymentInvoiceDto.getErpCancelStatus())) {
                return result.setResult(paymentInvoiceId, false, "应付发票已取消，不能重复取消");
            }
            // 应付发票ERP取消状态 = “取消中”
            if (PaymentInvoiceErpCancelStatusEnum.DOING.getCode().equals(paymentInvoiceDto.getErpCancelStatus())) {
                return result.setResult(paymentInvoiceId, false, "应付发票正在取消中，不能重复取消");
            }
            // 应付发票ERP取消状态 = “取消失败”、“未取消” 或者历史记录 = null
            if (null == paymentInvoiceDto.getErpCancelStatus()
                    || PaymentInvoiceErpCancelStatusEnum.FAIL.getCode().equals(paymentInvoiceDto.getErpCancelStatus())
                    || PaymentInvoiceErpCancelStatusEnum.NOT.getCode().equals(paymentInvoiceDto.getErpCancelStatus())) {
                // 应付发票ERP同步状态 = “待同步“
                if (PaymentInvoiceErpStatusEnum.WAITING.code().equals(paymentInvoiceDto.getErpStatus())
                        || PaymentInvoiceErpStatusEnum.CANCEL.code().equals(paymentInvoiceDto.getErpStatus())) {
                    return result.setResult(paymentInvoiceId, false, "应付发票未同步ERP，不能取消");
                }
                // 应付发票ERP同步状态 = “同步中“
                if (PaymentInvoiceErpStatusEnum.CHECKING.code().equals(paymentInvoiceDto.getErpStatus())) {
                    return result.setResult(paymentInvoiceId, false, "应付发票正在同步ERP，不能重复取消");
                }
                // 应付发票ERP同步状态 = “同步失败“
                if (PaymentInvoiceErpStatusEnum.FAILURE.code().equals(paymentInvoiceDto.getErpStatus())) {
                    // 付款申请【字段有裁剪】
                    List<PaymentApplyDto> paymentApplyDtoList = paymentApplyExtMapper.listByInvoiceId(paymentInvoiceDto.getId());
                    // 没有关联付款申请，或关联的都是无效的付款申请 允许取消
                    if (!CollectionUtils.isEmpty(paymentApplyDtoList)) {
                        // 付款申请无效  状态 = 作废或取消支付
                        for (PaymentApplyDto paymentApplyDto : paymentApplyDtoList) {
                            paymentApplyService.statusTransformation(paymentApplyDto);
                            // 付款申请状态 != “作废“ 或 “取消支付“
                            if (!PaymentApplyBizStatus.CANCEL_PAY.getCode().equals(paymentApplyDto.getBizStatus()) && !PaymentApplyBizStatus.CANCEL.getCode().equals(paymentApplyDto.getBizStatus())) {
                                return result.setResult(paymentInvoiceId, false, "应付发票已被有效的付款申请关联，不能取消");
                            }
                        }
                    }
                }
                // 应付发票ERP同步状态 = “同步成功“
                if (paymentInvoiceDto.getErpStatus().equals(PaymentInvoiceErpStatusEnum.SUCCESS.code())) {
                    // 付款申请【字段有裁剪】
                    List<PaymentApplyDto> paymentApplyDtoList =
                            paymentApplyExtMapper.listByInvoiceId(paymentInvoiceDto.getId());
                    // 没有关联付款申请，或关联的都是无效的付款申请 允许取消
                    if (!CollectionUtils.isEmpty(paymentApplyDtoList)) {
                        // 付款申请无效  状态 = 作废或取消支付
                        for (PaymentApplyDto paymentApplyDto : paymentApplyDtoList) {
                            paymentApplyService.statusTransformation(paymentApplyDto);
                            // 付款申请状态 != “作废“ 或 “取消支付“
                            if (!PaymentApplyBizStatus.CANCEL_PAY.getCode().equals(paymentApplyDto.getBizStatus()) &&
                                    !PaymentApplyBizStatus.CANCEL.getCode().equals(paymentApplyDto.getBizStatus())) {
                                return result.setResult(paymentInvoiceId, false, "应付发票已被有效的付款申请关联，不能取消");
                            }
                        }
                    }
                    // 不存在有效已核销记录 允许取消
                    PaymentWriteOffRecordExample writeOffRecordExample = new PaymentWriteOffRecordExample();
                    writeOffRecordExample.createCriteria().
                            andPaymentInvoiceIdEqualTo(paymentInvoiceDto.getId()).
                            andDeletedFlagEqualTo(false);
                    List<PaymentWriteOffRecord> paymentWriteOffRecordList = paymentWriteOffRecordMapper.selectByExample(writeOffRecordExample);
                    if (!CollectionUtils.isEmpty(paymentWriteOffRecordList)) {
                        for (PaymentWriteOffRecord paymentWriteOffRecord : paymentWriteOffRecordList) {
                            /**
                             * 无效应付发票核销记录
                             *
                             * paymentWriteOffRecord.cancel_status = 1 撤销成功
                             *
                             */
                            if (!PaymentWriteOffRecordCancelStatus.SUCCESS.code().equals(paymentWriteOffRecord.getCancelStatus())) {
                                return result.setResult(paymentInvoiceId, false, "应付发票已被预付款核销，不能取消");
                            }
                        }
                    }
                }
            }
        }
        return result.setResult(paymentInvoiceId, true, "");
    }

    /**
     * 检查会计期间是否打开
     */
    private void checkPeriod(Long ouId, Date erpCancelDate, Long paymentInvoiceId) {
        String glPeriodName = DateUtils.getYearMonth(erpCancelDate);
        //erp分类账id
        Long ledgerId = null;
        OrganizationRelQuery organizationRelQuery = new OrganizationRelQuery();
        organizationRelQuery.setPamEnabled(0);
        organizationRelQuery.setOperatingUnitId(ouId);
        PageInfo<OrganizationRelDto> ledgerPage = organizationRelExtService.invokeApiList(organizationRelQuery);
        List<OrganizationRelDto> ledgerList = ledgerPage.getList();
        Asserts.notEmpty(ledgerList, ErrorCode.CTC_COMPANY_CODE_NULL);
        ledgerId = Long.valueOf(ledgerList.get(0).getLedgerId());
        Asserts.notEmpty(ledgerId, ErrorCode.CTC_COMPANY_CODE_NULL);

        //查询期间类型为应付的会计期间
        GlPeriodDto generalLedgerAccountingPeriod =
                CacheDataUtils.findCachePeriod(GlPeriodType.PAYMENT_PERIOD.getCode(), ledgerId, glPeriodName);
        if (null == generalLedgerAccountingPeriod) {
            logger.info("当前会计期间未打开，请联系财务人员。applicationId：{}，ledgerId：{}，periodName：{}，redisKey：{}。入参--->erpCancelDate：{}，paymentInvoiceId：{}，ouId：{}",
                    GlPeriodType.PAYMENT_PERIOD.getCode(), ledgerId, glPeriodName, CacheDataUtils.buildKey(Constants.Prefix.PERIOD, GlPeriodType.PAYMENT_PERIOD.getCode(), ledgerId, glPeriodName), erpCancelDate, paymentInvoiceId, ouId);
        }
        Asserts.notEmpty(generalLedgerAccountingPeriod, ErrorCode.CTC_GENERAL_LEDGER_PERIOD_NOT_OPEN);

        //会计期间是否打开
        if (!(generalLedgerAccountingPeriod.getShowStatus().equals("Open")
                || generalLedgerAccountingPeriod.getShowStatus().equals("Opened"))) {
            throw new ApplicationBizException("【" + glPeriodName + "】会计期间未打开，请联系财务人员");
        }
    }

    /**
     * 检查是否推送ERP（取消发票）
     *
     * @param paymentInvoiceDto
     * @return true:推送，false:不推送
     */
    private boolean checkSendCancelErp(PaymentInvoiceDto paymentInvoiceDto) {
        // 应付发票状态 = “审批通过“   应付发票ERP同步状态 = “同步失败“
        if (PaymentInvoiceStatusEnum.PASS.getCode().equals(paymentInvoiceDto.getStatus())
                && PaymentInvoiceErpStatusEnum.FAILURE.code().equals(paymentInvoiceDto.getErpStatus())) {
            // 付款申请【字段有裁剪】
            List<PaymentApplyDto> paymentApplyDtoList = paymentApplyExtMapper.listByInvoiceId(paymentInvoiceDto.getId());
            // 没有关联付款申请，或关联的都是无效的付款申请 允许取消
            if (!CollectionUtils.isEmpty(paymentApplyDtoList)) {
                // 付款申请无效  状态 = 作废或取消支付
                for (PaymentApplyDto paymentApplyDto : paymentApplyDtoList) {
                    paymentApplyService.statusTransformation(paymentApplyDto);
                    // 付款申请状态 != “作废“ 或 “取消支付“
                    if (!PaymentApplyBizStatus.CANCEL_PAY.getCode().equals(paymentApplyDto.getBizStatus()) && !PaymentApplyBizStatus.CANCEL.getCode().equals(paymentApplyDto.getBizStatus())) {
                        throw new ApplicationBizException("应付发票已被有效的付款申请关联，不能取消");
                    }
                }
            }
            return false;
        }
        return true;
    }

    /**
     * 根据crm编码获取客户
     *
     * @param crmCode
     * @return
     */
    private Customer getCustomerByCRMCode(String crmCode) {
        CustomerExample customerExample = new CustomerExample();
        customerExample.createCriteria().andDeleteFlagEqualTo(false).andCrmCodeEqualTo(crmCode);
        List<Customer> customerList = customerExtMapper.selectByExample(customerExample);
        if (CollectionUtils.isNotEmpty(customerList)) {
            return customerList.get(0);
        }
        return null;
    }

    @Override
    public PaymentInvoiceDto paymentInvoiceDetailApp(Long id) {
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(id);
        PaymentInvoiceDto dto = BeanConverter.copy(paymentInvoice, PaymentInvoiceDto.class);

        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(paymentInvoice.getPurchaseContractId());
        dto.setPurchaseContractDto(BeanConverter.copy(purchaseContract, PurchaseContractDTO.class));

        Project project = projectMapper.selectByPrimaryKey(purchaseContract.getProjectId());
        if (project != null) {
            dto.setProjectName(project.getName());
            dto.setProjectCode(project.getCode());
        }

        //查询ou名称
        OperatingUnit operatingUnit = CacheDataUtils.findOuById(dto.getOuId());
        if (operatingUnit != null) {
            dto.setOuName(operatingUnit.getOperatingUnitName());
        }

        UserInfo userInfo = CacheDataUtils.findUserById(dto.getCreateBy());
        if (userInfo != null) {
            dto.setCreateByName(userInfo.getName());
        }

        PaymentInvoiceDetailExample detailExample = new PaymentInvoiceDetailExample();
        detailExample.createCriteria()
                .andPaymentInvoiceIdEqualTo(id)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<PaymentInvoiceDetail> paymentInvoiceDetails = paymentInvoiceDetailMapper.selectByExample(detailExample);
        dto.setPaymentInvoiceDetailList(BeanConverter.convert(paymentInvoiceDetails, PaymentInvoiceDetailDto.class));
        String accountGroupDebit = "";
        //非销售业务场景-GL成本结转
        List<BusiSceneNonSaleDetailDto> detailDtoList = this.getBusiSceneDetailList(dto.getOuId());
        if (ListUtils.isNotEmpty(detailDtoList)) {
            accountGroupDebit = detailDtoList.get(0).getAccountGroupDebit();
        }
        for (PaymentInvoiceDetailDto detailDto : dto.getPaymentInvoiceDetailList()) {
            detailDto.setAccountGroupDebit(accountGroupDebit);
        }
        return dto;
    }

    @Override
    public Boolean paymentInvoiceDraftSubmitCheck(Long id) {
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(id);
        logger.info("进入应付发票提交前单据检查，id={}", id);
        PaymentInvoiceDetail paymentInvoiceDetail = new PaymentInvoiceDetail();
        paymentInvoiceDetail.setPaymentInvoiceId(id);
        List<PaymentInvoiceDetail> paymentInvoiceDetails = detailList(paymentInvoiceDetail);
        PaymentInvoiceDto dto = BeanConverter.copy(paymentInvoice, PaymentInvoiceDto.class);
        if(ListUtils.isNotEmpty(paymentInvoiceDetails)){
            dto.setPaymentInvoiceDetailDtoList(BeanConverter.convert(paymentInvoiceDetails, PaymentInvoiceDetailDto.class));
        }
        //设置提交前单据检查标识
        dto.setDraftSubmitCheck(Boolean.TRUE);
        saveTheInvoice(dto);
        return true;
    }
}
