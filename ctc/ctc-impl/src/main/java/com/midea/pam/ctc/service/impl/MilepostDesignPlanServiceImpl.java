package com.midea.pam.ctc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.DesignPlanMaterialDetailDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanChangeRecordDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanConfirmRecordDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanConfirmRecordRelationDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanFieldDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanPurchaseRecordDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanSubmitRecordDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetMaterialDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.ProjectProfitDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetSummaryDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.UpdateDeliveryTimeDto;
import com.midea.pam.common.ctc.dto.UpdateTickDeliveryTimeDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.entity.DesignPlanMaterialDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecordExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChange;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChangeExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistoryExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecordExample;
import com.midea.pam.common.ctc.entity.MrpSum;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterial;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialExample;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectMilepostChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummary;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummaryExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementExample;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetail;
import com.midea.pam.common.ctc.entity.TicketTasksDetail;
import com.midea.pam.common.ctc.entity.TicketTasksDetailExample;
import com.midea.pam.common.ctc.query.DetailByProjectIdAndMilepostIdNewWbsQuery;
import com.midea.pam.common.ctc.vo.MilepostDesignPlanDetailApprovedVO;
import com.midea.pam.common.ctc.vo.ProjectBudgetChangeVO;
import com.midea.pam.common.ctc.vo.ProjectBudgetMaterialChangeHistoryVO;
import com.midea.pam.common.ctc.vo.ProjectCheckExcelVo;
import com.midea.pam.common.ctc.vo.ProjectCheckReturnVo;
import com.midea.pam.common.enums.AttachmentStatus;
import com.midea.pam.common.enums.BudgetConfigEnums;
import com.midea.pam.common.enums.ChangeType;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.MilepostStatus;
import com.midea.pam.common.enums.RequirementStatusEnum;
import com.midea.pam.common.enums.RequirementTypeEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.Builder;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.CollectionsUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.ObjUtils;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DesignPlanDetailGenerateRequire;
import com.midea.pam.ctc.common.enums.MaterialCategoryEnum;
import com.midea.pam.ctc.common.enums.MilepostDesignPlanDetailModelStatus;
import com.midea.pam.ctc.common.enums.ProjectMilepostAnnexType;
import com.midea.pam.ctc.common.enums.ReleaseDetailStatus;
import com.midea.pam.ctc.common.redis.RedisLuaScriptServer;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.contract.service.ReceiptPlanService;
import com.midea.pam.ctc.mapper.AsyncRequestResultMapper;
import com.midea.pam.ctc.mapper.DesignPlanMaterialDetailExtMapper;
import com.midea.pam.ctc.mapper.DesignPlanMaterialDetailMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanConfirmRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanSubmitRecordMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostExtMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialReleaseDetailExtMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementMapper;
import com.midea.pam.ctc.mapper.TicketTasksDetailMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.service.AsyncRequestResultService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.DesignPlanMaterialDetailService;
import com.midea.pam.ctc.service.MailExtService;
import com.midea.pam.ctc.service.MaterialCostExtService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MilepostDesignPlanChangeRecordService;
import com.midea.pam.ctc.service.MilepostDesignPlanConfirmRecordRelationService;
import com.midea.pam.ctc.service.MilepostDesignPlanConfirmRecordService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailSubmitHistoryService;
import com.midea.pam.ctc.service.MilepostDesignPlanPurchaseRecordService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.MilepostDesignPlanSubmitRecordService;
import com.midea.pam.ctc.service.ProjectBudgetMaterialService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.PurchaseMaterialReleaseDetailService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.TicketTasksService;
import com.midea.pam.ctc.service.event.DesignPlanDetailSubmitEvent;
import com.midea.pam.ctc.service.event.DesignPlanDetailSubmitForProjectEvent;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;

public class MilepostDesignPlanServiceImpl implements MilepostDesignPlanService {

    private final static Logger logger = LoggerFactory.getLogger(MilepostDesignPlanServiceImpl.class);

    private final static String MATERIAL_CLASS = "物料";

    private final static Long MAX_WAIT_TIME_SYNC = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;
    @Resource
    private MilepostDesignPlanSubmitRecordService milepostDesignPlanSubmitRecordService;
    @Resource
    private MilepostDesignPlanConfirmRecordService milepostDesignPlanConfirmRecordService;
    @Resource
    private MilepostDesignPlanSubmitRecordMapper milepostDesignPlanSubmitRecordMapper;
    @Resource
    private MilepostDesignPlanChangeRecordService milepostDesignPlanChangeRecordService;
    @Resource
    private MilepostDesignPlanConfirmRecordMapper milepostDesignPlanConfirmRecordMapper;
    @Resource
    private MilepostDesignPlanConfirmRecordRelationService milepostDesignPlanConfirmRecordRelationService;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private ReceiptPlanService receiptPlanService;
    @Resource
    private ProjectBudgetMaterialService projectBudgetMaterialService;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private MailExtService mailExtService;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private ProjectProfitService projectProfitService;
    @Resource
    private PurchaseMaterialRequirementService purchaseMaterialRequirementService;
    @Resource
    private PurchaseMaterialReleaseDetailService purchaseMaterialReleaseDetailService;
    @Resource
    private MaterialCostExtService materialCostExtService;
    @Resource
    private AsyncRequestResultService asyncRequestResultService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private AsyncRequestResultMapper asyncRequestResultMapper;
    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private MilepostDesignPlanPurchaseRecordService milepostDesignPlanPurchaseRecordService;
    @Resource
    private ProjectService projectService;
    @Resource
    private DesignPlanMaterialDetailService designPlanMaterialDetailService;
    @Resource
    private DesignPlanMaterialDetailMapper designPlanMaterialDetailMapper;
    @Resource
    private DesignPlanMaterialDetailExtMapper detailExtMapper;
    @Resource
    private TicketTasksService ticketTasksService;
    @Resource
    private TicketTasksDetailMapper ticketTasksDetailMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private ProjectTypeMapper projectTypeMapper;
    @Resource
    private ProjectWbsReceiptsMapper projectWbsReceiptsMapper;
    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;
    @Resource
    private MilepostDesignPlanDetailSubmitHistoryService milepostDesignPlanDetailSubmitHistoryService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectWbsBudgetSummaryMapper projectWbsBudgetSummaryMapper;
    @Resource
    private ProjectMilepostMapper projectMilepostMapper;
    @Resource
    private ProjectWbsReceiptsExtMapper projectWbsReceiptsExtMapper;
    @Resource
    private ProjectMilepostExtMapper projectMilepostExtMapper;
    @Resource
    private RedisLuaScriptServer redisLuaScriptServer;
    @Resource
    private PurchaseMaterialRequirementMapper purchaseMaterialRequirementMapper;
    @Resource
    private PurchaseMaterialReleaseDetailExtMapper purchaseMaterialReleaseDetailExtMapper;
    @Resource
    private MilepostDesignPlanDetailChangeService milepostDesignPlanDetailChangeService;


    @Override
    public MilepostDesignPlanDto checkDetailByProjectIdAndMilepostId(Long projectId, Long milepostId) {
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(milepostId, ErrorCode.CTC_MILEPOST_ID_NULL);
        MilepostDesignPlanDto planDto = new MilepostDesignPlanDto();
        //planDto.setMilepostDto(projectMilepostService.getById(milepostId));

        //检测详细设计下有审批中的详细设计发布和变更流程
        //提交记录
        MilepostDesignPlanSubmitRecordDto designPlanSubmitRecordQuery = new MilepostDesignPlanSubmitRecordDto();
        designPlanSubmitRecordQuery.setProjectId(projectId);
        designPlanSubmitRecordQuery.setMilepostId(milepostId);
        designPlanSubmitRecordQuery.setProjectSubmit(false);
        designPlanSubmitRecordQuery.setStatusIsNull(false);
        designPlanSubmitRecordQuery.setStatus(-1);
        planDto.setSubmitRecordDtos(milepostDesignPlanSubmitRecordService.selectList(designPlanSubmitRecordQuery));

        //审批中的变更记录
        MilepostDesignPlanChangeRecordDto designPlanChangeRecordQuery = new MilepostDesignPlanChangeRecordDto();
        designPlanChangeRecordQuery.setProjectId(projectId);
        designPlanChangeRecordQuery.setMilepostId(milepostId);
        designPlanChangeRecordQuery.setStatusIsNull(false);
        designPlanChangeRecordQuery.setStatus(-1);
        planDto.setChangeRecordDtos(milepostDesignPlanChangeRecordService.selectList(designPlanChangeRecordQuery));

        return planDto;
    }

    public List<MilepostDesignPlanDetailDto> setDesignTotalVal(List<MilepostDesignPlanDetailDto> designPlanDetailDtos) {
        return designPlanDetailDtos.parallelStream().peek(dto -> ObjUtils.ofNullable(dto).ifThenAccept(d -> ListUtils.isNotEmpty(d.getSonDtos()),
                d -> {
                    setDesignTotalVal(d.getSonDtos());
                    d.setDesignCost(d.getSonDtos().stream().map(MilepostDesignPlanDetailDto::getDesignTotal).reduce(BigDecimal.ZERO,
                            BigDecimalUtils::add));
                    d.setDesignTotal(BigDecimalUtils.multiply(d.getDesignCost(), d.getNumber()));
                }).ifThenAccept(d -> null != d.getDesignCost(),
                d -> d.setDesignTotal(BigDecimalUtils.multiply(d.getDesignCost(), d.getNumber())))).collect(Collectors.toList());
    }

    @Override
    public MilepostDesignPlanDto getDetailByProjectIdAndMilepostId(Long projectId, Long milepostId) {
        Guard.notNull(projectId, "项目id不能为空");
        Guard.notNull(milepostId, "里程碑id不能为空");

        List<MilepostDesignPlanDetailDto> firstLevelDetailList = milepostDesignPlanDetailService.getTheFirstLevelDetailByMilepostId(milepostId);
        if (CollectionUtils.isEmpty(firstLevelDetailList)) {
            ProjectBudgetMaterialExample example = new ProjectBudgetMaterialExample();
            ProjectBudgetMaterialExample.Criteria criteria = example.createCriteria();
            criteria.andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(projectId);
            List<ProjectBudgetMaterial> projectBudgetMaterials = projectBudgetMaterialService.selectByExample(example);
            if (CollectionUtils.isNotEmpty(projectBudgetMaterials)) {
                ProjectBudgetMaterial projectBudgetMaterial = projectBudgetMaterials.get(0);
                //获取预算最顶层
                MilepostDesignPlanDetailDto dto = new MilepostDesignPlanDetailDto();
                dto.setProjectBudgetMaterialId(projectBudgetMaterial.getId());//项目物料预算id
                dto.setProjectId(projectId);//项目id
                dto.setMilepostId(milepostId);//详细设计方案类型里程碑id
                dto.setMaterielDescr(projectBudgetMaterial.getName());//物料描述
                //因为空导致过不去 修复与20210329
                if (null != projectBudgetMaterial.getUnit()) {
                    dto.setUnit(getMaterialUnitByCode(projectBudgetMaterial.getUnit()));//单位
                }

                dto.setUnitCode(projectBudgetMaterial.getUnit());//单位编码(物料预算unit存放的是单位编码)
                if (null != projectBudgetMaterial.getNumber()) {
                    dto.setNumber(new BigDecimal(projectBudgetMaterial.getNumber()));//数量
                }
                dto.setBudgetUnitPrice(projectBudgetMaterial.getPrice());//单价
                dto.setPamCode(projectBudgetMaterial.getCode());//PAM编码
                dto.setSource(projectBudgetMaterial.getSource());//来源
                dto.setExt(projectBudgetMaterial.getExt());//是否外包
                dto.setParentId(-1L);
                dto.setDeletedFlag(DeletedFlag.VALID.code());
                dto.setCreateBy(-1L);
                //项目详细设计的预算小计=项目预算总价 added at 20200520
                dto.setBudgetSubtotal(projectBudgetMaterial.getPriceTotal());
                if (null != projectBudgetMaterial.getPlannedDeliveryStartDate()) {
                    Date deliveryTime = DateUtils.resetDate(projectBudgetMaterial.getPlannedDeliveryStartDate());
                    dto.setDeliveryTime(deliveryTime);
                    //取项目物料预算的计划交付时间到详设
                }
                //获取预算子节点
                ProjectBudgetMaterialExample example2 = new ProjectBudgetMaterialExample();
                ProjectBudgetMaterialExample.Criteria criteria2 = example2.createCriteria();
                criteria2.andParentIdEqualTo(projectBudgetMaterial.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<ProjectBudgetMaterial> pms = projectBudgetMaterialService.selectByExample(example2);
                if (CollectionUtils.isNotEmpty(pms)) {
                    for (ProjectBudgetMaterial p : pms) {
                        MilepostDesignPlanDetailExample query = new MilepostDesignPlanDetailExample();
                        query.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectBudgetMaterialIdEqualTo(p.getId());
                        List<MilepostDesignPlanDetail> details = milepostDesignPlanDetailMapper.selectByExample(query);
                        if (CollectionUtils.isNotEmpty(details)) {
                            MilepostDesignPlanDetail d = details.get(0);
                            if (null != d.getParentId()) {
                                dto.setId(d.getParentId());
                                break;
                            }
                        }
                    }
                }
                //重新生成顶级父节点
                milepostDesignPlanDetailService.save(dto, -1L);
            }
        }

        MilepostDesignPlanDetailDto milepostDesignPlanDetailQuery = new MilepostDesignPlanDetailDto();
        milepostDesignPlanDetailQuery.setProjectId(projectId);
        milepostDesignPlanDetailQuery.setMilepostId(milepostId);
        milepostDesignPlanDetailQuery.setDeletedFlag(Boolean.FALSE);
        List<MilepostDesignPlanDetailDto> designPlanDetailDtoList = milepostDesignPlanDetailService.selectList(milepostDesignPlanDetailQuery);
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos = getDesignPlanDetailWithSon(designPlanDetailDtoList);

        //提交记录
        MilepostDesignPlanSubmitRecordDto designPlanSubmitRecordQuery = new MilepostDesignPlanSubmitRecordDto();
        designPlanSubmitRecordQuery.setProjectId(projectId);
        designPlanSubmitRecordQuery.setMilepostId(milepostId);
        designPlanSubmitRecordQuery.setProjectSubmit(false);
        designPlanSubmitRecordQuery.setStatusIsNull(false);
        List<MilepostDesignPlanSubmitRecordDto> submitRecordDtos = milepostDesignPlanSubmitRecordService.selectList(designPlanSubmitRecordQuery);
        List<Long> submitCheckingWhetherModelDesignPlanDetailIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(submitRecordDtos)) {
            submitCheckingWhetherModelDesignPlanDetailIdList.addAll(submitRecordDtos.stream().filter(e -> Objects.equals(e.getStatus(),
                            CheckStatus.CHECKING.code()))
                    .map(MilepostDesignPlanSubmitRecordDto::getUploadPathId).distinct().collect(Collectors.toList()));
        }

        //确认记录
        MilepostDesignPlanConfirmRecordDto designPlanConfirmRecordQuery = new MilepostDesignPlanConfirmRecordDto();
        designPlanConfirmRecordQuery.setProjectId(projectId);
        designPlanConfirmRecordQuery.setMilepostId(milepostId);
        designPlanConfirmRecordQuery.setProjectSubmit(false);
        designPlanConfirmRecordQuery.setStatusIsNull(false);

        //提前采购记录
        MilepostDesignPlanPurchaseRecordDTO designPlanPurchaseRecordQuery = new MilepostDesignPlanPurchaseRecordDTO();
        designPlanPurchaseRecordQuery.setProjectId(projectId);
        designPlanPurchaseRecordQuery.setMilepostId(milepostId);
        designPlanPurchaseRecordQuery.setStatusIsNull(false);
        designPlanPurchaseRecordQuery.setOrderBy(true);

        //变更记录
        MilepostDesignPlanChangeRecordDto designPlanChangeRecordQuery = new MilepostDesignPlanChangeRecordDto();
        designPlanChangeRecordQuery.setProjectId(projectId);
        designPlanChangeRecordQuery.setMilepostId(milepostId);
        designPlanChangeRecordQuery.setStatusIsNull(false);
        List<MilepostDesignPlanChangeRecordDto> changeRecordDtos = milepostDesignPlanChangeRecordService.selectList(designPlanChangeRecordQuery);
        List<Long> mdpChangeCheckingWhetherModelDesignPlanDetailIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(changeRecordDtos)) {
            mdpChangeCheckingWhetherModelDesignPlanDetailIdList.addAll(changeRecordDtos.stream().filter(e -> Objects.equals(e.getStatus(),
                            CheckStatus.MDP_CHANGE_CHECKING.code()))
                    .map(MilepostDesignPlanChangeRecordDto::getUploadPathId).distinct().collect(Collectors.toList()));
        }

        //所有审批通过的提交记录对应的附件记录
        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
        attachmentDtos.addAll(milepostDesignPlanSubmitRecordService.allApprovedSubmitRecordAttachment(projectId));

        //所有审批通过的变更记录对应的附件记录
        attachmentDtos.addAll(milepostDesignPlanChangeRecordService.allAttachFromAllPassedChangeRecord(projectId));

        //过滤掉未审批通过的附件
        List<CtcAttachmentDto> attachments = attachmentDtos.stream().filter(attachment -> Objects.equals(attachment.getStatus(),
                        AttachmentStatus.PASSED.code()))
                .collect(Collectors.toList());
        attachments.sort(Comparator.comparing(CtcAttachmentDto::getCreateAt));
        Collections.reverse(attachments);

        Builder<MilepostDesignPlanDto> with = Builder.of(MilepostDesignPlanDto::new)
                .with(MilepostDesignPlanDto::setMilepostDto, projectMilepostService.getById(milepostId))
                .with(MilepostDesignPlanDto::setDesignPlanDetailDtos, setDesignTotalVal(milepostDesignPlanDetailDtos))
                .with(MilepostDesignPlanDto::setSubmitRecordDtos, submitRecordDtos)
                .with(MilepostDesignPlanDto::setConfirmRecordDtos, milepostDesignPlanConfirmRecordService.selectList(designPlanConfirmRecordQuery))
                .with(MilepostDesignPlanDto::setPurchaseRecordDTOS, milepostDesignPlanPurchaseRecordService.selectList(designPlanPurchaseRecordQuery)
                        .stream().filter(a -> !Objects.equals(a.getStatus(), CheckStatus.PURCHASE_DELETE.code())).collect(Collectors.toList()))
                .with(MilepostDesignPlanDto::setChangeRecordDtos, changeRecordDtos)
                .with(MilepostDesignPlanDto::setAttachmentDtos, attachments)
                .with(MilepostDesignPlanDto::setMdpChangeCheckingWhetherModelDesignPlanDetailIdList,
                        mdpChangeCheckingWhetherModelDesignPlanDetailIdList)
                .with(MilepostDesignPlanDto::setSubmitCheckingWhetherModelDesignPlanDetailIdList, submitCheckingWhetherModelDesignPlanDetailIdList);
        Project project = projectBusinessService.selectByPrimaryKey(projectId);
        if (project != null) {
            with = with.with(MilepostDesignPlanDto::setProjectName, project.getName())
                    .with(MilepostDesignPlanDto::setProjectCode, project.getCode())
                    .with(MilepostDesignPlanDto::setProjectStatus, project.getStatus());
            //查询库存组织
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null) {
                with = with.with(MilepostDesignPlanDto::setStorageId, projectProfit.getStorageId());
            }
        }
        return with.buildForParallel();
    }

    private List<MilepostDesignPlanDetailDto> getDesignPlanDetailWithSon(List<MilepostDesignPlanDetailDto> designPlanDetailDtoList) {
        List<MilepostDesignPlanDetailDto> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(designPlanDetailDtoList)) {
            return resultList;
        }

        Map<Long, List<MilepostDesignPlanDetailDto>> parentDesignPlanDetailDtoMap = new HashMap<>();
        MilepostDesignPlanDetailDto parentDesignPlanDetailDto = null;
        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtoList) {
            Long parentId = designPlanDetailDto.getParentId();
            if (Objects.isNull(parentId)) {
                continue;
            }

            if (-1 == parentId) {
                parentDesignPlanDetailDto = designPlanDetailDto;
            }

            if (parentDesignPlanDetailDtoMap.containsKey(parentId)) {
                parentDesignPlanDetailDtoMap.get(parentId).add(designPlanDetailDto);
            } else {
                parentDesignPlanDetailDtoMap.put(parentId, Lists.newArrayList(designPlanDetailDto));
            }
        }
        if (parentDesignPlanDetailDto == null) {
            return resultList;
        }

        setDesignPlanDetailWithSons(parentDesignPlanDetailDto, parentDesignPlanDetailDtoMap);

        //统计需求量和已生成采购需求量
        milepostDesignPlanDetailService.countRequirementNum(parentDesignPlanDetailDto, BigDecimal.ONE);

        resultList.add(parentDesignPlanDetailDto);

        return resultList;
    }

    private void setDesignPlanDetailWithSons(MilepostDesignPlanDetailDto parentDesignPlanDetailDto,
                                             Map<Long, List<MilepostDesignPlanDetailDto>> parentDesignPlanDetailDtoMap) {
        List<MilepostDesignPlanDetailDto> sonDtoList = parentDesignPlanDetailDtoMap.get(parentDesignPlanDetailDto.getId());
        if (CollectionUtils.isEmpty(sonDtoList)) {
            return;
        }

        for (MilepostDesignPlanDetailDto sonDto : sonDtoList) {
            //二级模组已确认状态时，把该状态赋值给三级物料
            if (Objects.equals(parentDesignPlanDetailDto.getModuleStatus(), MilepostDesignPlanDetailModelStatus.CONFIRMED.code())) {
                sonDto.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());
            }

            setDesignPlanDetailWithSons(sonDto, parentDesignPlanDetailDtoMap);
        }

        parentDesignPlanDetailDto.setSonDtos(sonDtoList);
    }

    //判断是否最底层: 是layerMax=false; 否layerMax=true
    private List<MilepostDesignPlanDetailDto> layerMax(List<MilepostDesignPlanDetailDto> list) {
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).getSonDtos() != null && list.get(i).getSonDtos().size() > 0) {
                    layerMax(list.get(i).getSonDtos());
                    list.get(i).setLayerMax(true);
                }
            }
        }
        return list;
    }

    @Override
    public MilepostDesignPlanDto getDetailByProjectIdAndProjectId(DetailByProjectIdAndMilepostIdNewWbsQuery param) {
        logger.info("getDetailByProjectIdAndProjectId的param：{}", JSON.toJSONString(param));
        Guard.notNull(param, "参数为空");
        Guard.notNull(param.getProjectId(), "项目id不能为空");

        boolean onlyProjectId = true;
        MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
        MilepostDesignPlanDetailExample.Criteria criteria = milepostDesignPlanDetailExample.createCriteria();
        if (param.getWbsLayer() != null) {
            criteria.andWbsLayerLike("%" + param.getWbsLayer() + "%");
            onlyProjectId = false;
        }
        if (param.getMaterielDescr() != null) {
            criteria.andMaterielDescrLike("%" + param.getMaterielDescr() + "%");
            onlyProjectId = false;
        }
        if (param.getPamCode() != null) {
            criteria.andPamCodeLike("%" + param.getPamCode() + "%");
            onlyProjectId = false;
        }
        if (param.getErpCode() != null) {
            criteria.andErpCodeLike("%" + param.getErpCode() + "%");
            onlyProjectId = false;
        }
        if (param.getWbsSummaryCode() != null) {
            criteria.andWbsSummaryCodeLike("%" + param.getWbsSummaryCode() + "%");
            onlyProjectId = false;
        }
        if (param.getModuleStatus() != null) {
            criteria.andModuleStatusEqualTo(param.getModuleStatus());
            onlyProjectId = false;
        }
        if (param.getDispatchIs() != null) {
            criteria.andDispatchIsEqualTo(param.getDispatchIs());
            onlyProjectId = false;
        }
        if (param.getExtIs() != null) {
            criteria.andExtIsEqualTo(param.getExtIs());
            onlyProjectId = false;
        }
        if (param.getBrand() != null) {
            criteria.andBrandLike("%" + param.getBrand() + "%");
            onlyProjectId = false;
        }
        if (StringUtils.isNotBlank(param.getRequirementTypeStr())) {
            List<Integer> requirementTypeList =
                    Arrays.stream(param.getRequirementTypeStr().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(requirementTypeList)) {
                criteria.andRequirementTypeIn(requirementTypeList);
                onlyProjectId = false;
            }
        }
        if (StringUtils.isNotBlank(param.getPurchaseMaterialRequirementReceiptsCode())) {
            onlyProjectId = false;
        }
        //根据设计人员搜索
        if (StringUtils.isNotEmpty(param.getPlanDesigner())) {
            criteria.andPlanDesignerLike("%" + param.getPlanDesigner() + "%");
            onlyProjectId = false;
        }
        criteria.andProjectIdEqualTo(param.getProjectId()).andDeletedFlagEqualTo(Boolean.FALSE);
        long start = System.currentTimeMillis();
        //List<MilepostDesignPlanDetailDto> conditionQueryDetailDtoList = milepostDesignPlanDetailExtMapper.selectDetailAndReceiptsByExample
        // (milepostDesignPlanDetailExample);
        List<MilepostDesignPlanDetailDto> conditionQueryDetailDtoList = selectDetailAndReceiptsByExample(param, milepostDesignPlanDetailExample);
        long end = System.currentTimeMillis();
        logger.info("查询接口耗时为:{}毫秒", end - start);

        Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(conditionQueryDetailDtoList)) {
            List<ProjectWbsReceiptsDto> receiptsDtoList = projectWbsReceiptsExtMapper.selectByDtos(conditionQueryDetailDtoList);
            receiptsDtoMap.putAll(CollectionUtils.isEmpty(receiptsDtoList) ? new HashMap<>()
                    : receiptsDtoList.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId)));
        }
        for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : conditionQueryDetailDtoList) {
            milepostDesignPlanDetailDto.setHasConfirmed(CollectionUtils.isNotEmpty(receiptsDtoMap.get(milepostDesignPlanDetailDto.getId())));
        }

        List<MilepostDesignPlanDetailDto> parentAndSonRelTreeDetailDtoList = new ArrayList<>();

        //详设变更时才设置允许增删改标识
        if (ListUtils.isNotEmpty(conditionQueryDetailDtoList)) {
            if (Objects.equals(1, param.getEditQuery())) {
                // 创建过滤后的列表，只包含符合条件的节点(whetherModel=1且wbsLastLayer=1或whetherModel=0)
//                List<MilepostDesignPlanDetailDto> filteredList = conditionQueryDetailDtoList.stream()
//                        .filter(dto -> (Boolean.TRUE.equals(dto.getWhetherModel()) && Boolean.TRUE.equals(dto.getWbsLastLayer()))
//                                )
//                        .collect(Collectors.toList());

                // 只调用一次checkEditAble方法，它会递归处理并设置每个节点的editable属性
                checkEditAble(conditionQueryDetailDtoList);

                // 将过滤后列表中节点的editable属性同步回原列表中对应的节点
                Map<Long, Boolean> editableMap = conditionQueryDetailDtoList.stream()
                        .collect(Collectors.toMap(MilepostDesignPlanDetailDto::getId, MilepostDesignPlanDetailDto::getEditable));

                conditionQueryDetailDtoList.forEach(dto -> {
                    if (editableMap.containsKey(dto.getId())) {
                        dto.setEditable(editableMap.get(dto.getId()));
                    }
                });
                if (StringUtils.isNotEmpty(param.getRequirementCode())) {
                    setEditableByPamCode(conditionQueryDetailDtoList, param.getRequirementCode());
                }
            }
        }

        if (ListUtils.isNotEmpty(conditionQueryDetailDtoList)) {

            if (onlyProjectId) {
                parentAndSonRelTreeDetailDtoList = buildTree(conditionQueryDetailDtoList);
                setDesignTotalVal(parentAndSonRelTreeDetailDtoList);
            } else {
                parentAndSonRelTreeDetailDtoList = this.findParentForPlanDetail(conditionQueryDetailDtoList);
            }

            layerMax(parentAndSonRelTreeDetailDtoList);

            if (CollectionUtils.isNotEmpty(parentAndSonRelTreeDetailDtoList)) {
                MilepostDesignPlanDetailDto dto = parentAndSonRelTreeDetailDtoList.get(0);
                milepostDesignPlanDetailService.countRequirementNum(dto, BigDecimal.ONE);
            }
        }

        //详细设计单据记录
        ProjectWbsReceiptsExample receiptsExample = new ProjectWbsReceiptsExample();
        receiptsExample.createCriteria().andProjectIdEqualTo(param.getProjectId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        receiptsExample.setOrderByClause(" create_at desc ");
        List<ProjectWbsReceipts> receiptsList = projectWbsReceiptsMapper.selectByExampleWithBLOBs(receiptsExample);

        //附件列表
        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
        if (ListUtil.isPresent(receiptsList)) {
            List<Long> receiptsIdList = receiptsList.stream().map(ProjectWbsReceipts::getId).collect(Collectors.toList());
            attachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(receiptsIdList, CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
        }

        Builder<MilepostDesignPlanDto> builder = Builder.of(MilepostDesignPlanDto::new)
                .with(MilepostDesignPlanDto::setDesignPlanDetailDtos, parentAndSonRelTreeDetailDtoList)
                .with(MilepostDesignPlanDto::setAttachmentDtos, attachmentDtos)
                .with(MilepostDesignPlanDto::setReceiptsDtos, BeanConverter.copy(receiptsList, ProjectWbsReceiptsDto.class));
        Project project = projectBusinessService.selectByPrimaryKey(param.getProjectId());
        if (project != null) {
            builder = builder.with(MilepostDesignPlanDto::setProjectName, project.getName()).with(MilepostDesignPlanDto::setProjectCode,
                            project.getCode())
                    .with(MilepostDesignPlanDto::setProjectStatus, project.getStatus());
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null) {
                builder = builder.with(MilepostDesignPlanDto::setStorageId, projectProfit.getStorageId());
            }
        }
        MilepostDesignPlanDto milepostDesignPlanDto = builder.buildForParallel();
        // 将totalNum复制到proDemandTotalNum字段
        if (milepostDesignPlanDto != null && ListUtils.isNotEmpty(milepostDesignPlanDto.getDesignPlanDetailDtos())) {
            copyTotalNumToProDemandTotalNum(milepostDesignPlanDto.getDesignPlanDetailDtos());
        }
        return milepostDesignPlanDto;
    }

    /**
     * 递归复制totalNum到proDemandTotalNum字段
     *
     * @param detailDtos 详细设计DTO列表
     */
    private void copyTotalNumToProDemandTotalNum(List<MilepostDesignPlanDetailDto> detailDtos) {
        if (ListUtils.isEmpty(detailDtos)) {
            return;
        }

        for (MilepostDesignPlanDetailDto dto : detailDtos) {
            // 非空判断后复制totalNum到proDemandTotalNum
            if (dto.getTotalNum() != null) {
                dto.setProDemandTotalNum(dto.getTotalNum());
            }

            // 递归处理子节点
            if (ListUtils.isNotEmpty(dto.getSonDtos())) {
                copyTotalNumToProDemandTotalNum(dto.getSonDtos());
            }
        }
    }

    private List<MilepostDesignPlanDetailDto> selectDetailAndReceiptsByExample(DetailByProjectIdAndMilepostIdNewWbsQuery param,
                                                                               MilepostDesignPlanDetailExample example) {
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                milepostDesignPlanDetailExtMapper.selectDetailAndReceiptsByCondition(example);

        Map<String, String> requirementMap = new HashMap<>();
        if (StringUtils.isNotEmpty(param.getPurchaseMaterialRequirementReceiptsCode())) {
            PurchaseMaterialRequirementExample purchaseMaterialRequirementExample = new PurchaseMaterialRequirementExample();
            PurchaseMaterialRequirementExample.Criteria criteria = purchaseMaterialRequirementExample.createCriteria();
            criteria.andProjectIdEqualTo(param.getProjectId());
            criteria.andRequirementCodeLike("%" + param.getPurchaseMaterialRequirementReceiptsCode() + "%");
            criteria.andDeletedFlagEqualTo(false);
            List<PurchaseMaterialRequirement> requirementList = purchaseMaterialRequirementMapper.selectByExample(purchaseMaterialRequirementExample);
            if (CollectionUtils.isEmpty(requirementList)) {
                return new ArrayList<>();
            }

            for (PurchaseMaterialRequirement requirement : requirementList) {
                requirementMap.put(getRequirementKey(requirement.getDeliveryTime(), requirement.getPamCode(), requirement.getWbsSummaryCode()),
                        requirement.getRequirementCode());
            }
        }

        List<MilepostDesignPlanDetailDto> finalDesignPlanDetailDtos = new ArrayList<>();
        List<Long> designPlanDetailIds = new ArrayList<>();
        List<Long> planDetailIds = new ArrayList<>();
        List<Date> deliveryTimeList = new ArrayList<>();
        List<String> pamCodeList = new ArrayList<>();
        List<String> wbsSummaryCodeList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto detailDto : milepostDesignPlanDetailDtos) {
            if (MapUtils.isNotEmpty(requirementMap) && !requirementMap.containsKey(getRequirementKey(detailDto.getDeliveryTime(),
                    detailDto.getPamCode(), detailDto.getWbsSummaryCode()))) {
                continue;
            }

            designPlanDetailIds.add(detailDto.getId());

            if (detailDto.getWbsLastLayer() == null || Boolean.TRUE.equals(detailDto.getWbsLastLayer())) {
                planDetailIds.add(detailDto.getId());
            }

            Date deliveryTime = detailDto.getDeliveryTime();
            if (deliveryTime != null && !deliveryTimeList.contains(deliveryTime)) {
                deliveryTimeList.add(deliveryTime);
            }
            String pamCode = detailDto.getPamCode();
            if (StringUtils.isNotEmpty(pamCode) && !pamCodeList.contains(pamCode)) {
                pamCodeList.add(pamCode);
            }
            String wbsSummaryCode = detailDto.getWbsSummaryCode();
            if (StringUtils.isNotEmpty(wbsSummaryCode) && !wbsSummaryCodeList.contains(wbsSummaryCode)) {
                wbsSummaryCodeList.add(wbsSummaryCode);
            }

            finalDesignPlanDetailDtos.add(detailDto);
        }
        logger.info("获取里程碑详细设计方案设计信息的id集合为:{}", designPlanDetailIds.size());
        logger.info("过滤后的id集合:{}", planDetailIds.size());

        if (StringUtils.isEmpty(param.getPurchaseMaterialRequirementReceiptsCode())) {
            PurchaseMaterialRequirementExample purchaseMaterialRequirementExample = new PurchaseMaterialRequirementExample();
            PurchaseMaterialRequirementExample.Criteria criteria = purchaseMaterialRequirementExample.createCriteria();
            criteria.andProjectIdEqualTo(param.getProjectId());
            if (CollectionUtils.isNotEmpty(deliveryTimeList)) {
                criteria.andDeliveryTimeIn(deliveryTimeList);
            }
            if (CollectionUtils.isNotEmpty(pamCodeList)) {
                criteria.andPamCodeIn(pamCodeList);
            }
            if (CollectionUtils.isNotEmpty(wbsSummaryCodeList)) {
                criteria.andWbsSummaryCodeIn(wbsSummaryCodeList);
            }
            criteria.andDeletedFlagEqualTo(false);
            List<PurchaseMaterialRequirement> requirementList = purchaseMaterialRequirementMapper.selectByExample(purchaseMaterialRequirementExample);
            for (PurchaseMaterialRequirement requirement : requirementList) {
                requirementMap.put(getRequirementKey(requirement.getDeliveryTime(), requirement.getPamCode(), requirement.getWbsSummaryCode()),
                        requirement.getRequirementCode());
            }
        }

        //查询
        List<MilepostDesignPlanFieldDto> purchaseStatusList =
                milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanPurchaseInfo(designPlanDetailIds);
        Map<Long, Integer> purchaseStatusMap = new HashMap<>();//采购状态
        Map<Long, BigDecimal> approvalNumMap = new HashMap<>();//提前采购审批中的数量
        Map<Long, BigDecimal> purchaseNumMap = new HashMap<>();//提前采购数量
        for (MilepostDesignPlanFieldDto dto : purchaseStatusList) {
            purchaseStatusMap.put(dto.getDesignPlanDetailId(), dto.getPurchaseStatus());
            if (Objects.equals(dto.getPurchaseStatus(), -1)) {
                approvalNumMap.put(dto.getDesignPlanDetailId(), dto.getPurchaseNum());
            }
            if (Objects.equals(dto.getPurchaseStatus(), 0) || Objects.equals(dto.getPurchaseStatus(), -1) || Objects.equals(dto.getPurchaseStatus()
                    , 1)
                    || Objects.equals(dto.getPurchaseStatus(), 3) || Objects.equals(dto.getPurchaseStatus(), 40)) {
                purchaseNumMap.put(dto.getDesignPlanDetailId(), dto.getPurchaseNum());

            }
        }

        Map<Long, MilepostDesignPlanFieldDto> mergeMap = new HashMap<>();

        if (ListUtils.isNotEmpty(planDetailIds)) {
            Long projectId = param.getProjectId();
            //三个临时表的查询
            List<MilepostDesignPlanFieldDto> t1List = milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanT1(projectId);
            Map<Long, MilepostDesignPlanFieldDto> t1Map = t1List.stream().collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId,
                    Function.identity()));
            List<MilepostDesignPlanFieldDto> t2List = milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanT2(projectId);
            Map<Long, MilepostDesignPlanFieldDto> t2Map = t2List.stream().collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId,
                    Function.identity()));
            List<MilepostDesignPlanFieldDto> t3List = milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanT3(projectId);
            Map<Long, MilepostDesignPlanFieldDto> t3Map = t3List.stream().collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId,
                    Function.identity()));
            //合并数据,如果表1有数据以表1为准,如果表1没有取表2,如果表2也没有,取表3
            mergeMap = planDetailIds.stream().map(x -> {
                MilepostDesignPlanFieldDto dto = new MilepostDesignPlanFieldDto();
                dto.setDesignPlanDetailId(x);
                dto.setRequirementCode(t1Map.containsKey(x) ? t1Map.get(x).getRequirementCode() : (t2Map.containsKey(x) ?
                        t2Map.get(x).getRequirementCode() : (t3Map.containsKey(x) ? t3Map.get(x).getRequirementCode() : null)));
                dto.setRequirementStatus(t1Map.containsKey(x) ? t1Map.get(x).getRequirementStatus() : (t2Map.containsKey(x) ?
                        t2Map.get(x).getRequirementStatus() : (t3Map.containsKey(x) ? t3Map.get(x).getRequirementStatus() : null)));
                return dto;
            }).collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId, Function.identity()));
        }

        for (MilepostDesignPlanDetailDto x : finalDesignPlanDetailDtos) {
            //提前采购审批状态 t purchaseStatus
            x.setPurchaseStatus(purchaseStatusMap.get(x.getId()));
            //提前采购数量 t2 purchaseNum
            x.setPurchaseNum(Optional.ofNullable(purchaseNumMap.get(x.getId())).orElse(BigDecimal.ZERO.setScale(6)));
            //提前采购审批中的数量 t3 approvalNum
            x.setApprovalNum(Optional.ofNullable(approvalNumMap.get(x.getId())).orElse(BigDecimal.ZERO.setScale(6)));
            //需求发布单据编号 pwr2,pwr,pwr3 requirementCode
            if (x.getWbsLastLayer() == null || Boolean.TRUE.equals(x.getWbsLastLayer())) {
                x.setRequirementCode(mergeMap.containsKey(x.getId()) ? mergeMap.get(x.getId()).getRequirementCode() : null);
                //单据状态 0-草稿、1-待处理、2-审核中、3-驳回、4-生效、5-作废 pwr2,pwr,pwr3 requirementStatus
                x.setRequirementStatus(mergeMap.containsKey(x.getId()) ? mergeMap.get(x.getId()).getRequirementStatus() : null);
            }
            x.setPurchaseMaterialRequirementReceiptsCode(requirementMap.get(getRequirementKey(x.getDeliveryTime(), x.getPamCode(),
                    x.getWbsSummaryCode())));
        }

        return finalDesignPlanDetailDtos;
    }

    @Override
    public MilepostDesignPlanDto getWbsLayerDetailByProjectId(Long projectId, Integer editQuery, String wbsRequirementCode) {
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
        MilepostDesignPlanDetailExample.Criteria criteria = milepostDesignPlanDetailExample.createCriteria();
        criteria.andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);

        // 只查wbs层级的详设行（懒加载，只最多查到工具层，不带物料和装配件）
        List<MilepostDesignPlanDetailDto> conditionQueryDetailDtoList =
                milepostDesignPlanDetailExtMapper.selectWbsLayerDetailByProjectId(milepostDesignPlanDetailExample);

        //合并并设置需求编码和状态信息
        mergeAndSetRequirementInfo(conditionQueryDetailDtoList);

        Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = new HashMap<>();
        List<Long> detailIds = CollectionUtils.isEmpty(conditionQueryDetailDtoList) ? new ArrayList<>()
                : conditionQueryDetailDtoList.stream().map(MilepostDesignPlanDetailDto::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(detailIds)) {
            List<ProjectWbsReceiptsDto> receiptsDtoList = projectWbsReceiptsService.selectByDetailIds(detailIds);
            receiptsDtoMap.putAll(CollectionUtils.isEmpty(receiptsDtoList) ? new HashMap<>()
                    : receiptsDtoList.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId)));
        }
        for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : conditionQueryDetailDtoList) {
            // 找到该详设是否被确认
            milepostDesignPlanDetailDto.setHasConfirmed(CollectionUtils.isNotEmpty(receiptsDtoMap.get(milepostDesignPlanDetailDto.getId())));

//            //详设变更时才设置允许增删改标识
//            if(Objects.equals(1,editQuery)){
//                // 只有当是模组且是最后一级时才设置编辑标识
//                if(Boolean.TRUE.equals(milepostDesignPlanDetailDto.getWhetherModel()) &&
//                        Boolean.TRUE.equals(milepostDesignPlanDetailDto.getWbsLastLayer())) {
//                    milepostDesignPlanDetailDto.setEditable(checkEditAble(Lists.newArrayList(milepostDesignPlanDetailDto)));
//                }
//            }
        }

        //详设变更时才设置允许增删改标识
        processDesignChangeEditPermission(projectId, conditionQueryDetailDtoList, editQuery, wbsRequirementCode);

        List<MilepostDesignPlanDetailDto> parentAndSonRelTreeDetailDtoList = new ArrayList<>();

        if (ListUtils.isNotEmpty(conditionQueryDetailDtoList)) {
            // 装换成树结构
            parentAndSonRelTreeDetailDtoList = this.findParentForPlanDetail(conditionQueryDetailDtoList);

            layerMax(parentAndSonRelTreeDetailDtoList);

            if (parentAndSonRelTreeDetailDtoList != null && parentAndSonRelTreeDetailDtoList.size() > 0) {
                MilepostDesignPlanDetailDto dto = parentAndSonRelTreeDetailDtoList.get(0);
                milepostDesignPlanDetailService.countRequirementNum(dto, BigDecimal.ONE);
            }
        }

        //详细设计单据记录
        ProjectWbsReceiptsExample receiptsExample = new ProjectWbsReceiptsExample();
        receiptsExample.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        receiptsExample.setOrderByClause(" create_at desc ");
        List<ProjectWbsReceipts> receiptsList = projectWbsReceiptsMapper.selectByExampleWithBLOBs(receiptsExample);

        //附件列表
        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
        if (ListUtil.isPresent(receiptsList)) {
            List<Long> receiptsIdList = receiptsList.stream().map(ProjectWbsReceipts::getId).collect(Collectors.toList());
            attachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(receiptsIdList, CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
        }

        Builder<MilepostDesignPlanDto> builder = Builder.of(MilepostDesignPlanDto::new)
                .with(MilepostDesignPlanDto::setDesignPlanDetailDtos, parentAndSonRelTreeDetailDtoList)
                .with(MilepostDesignPlanDto::setAttachmentDtos, attachmentDtos)
                .with(MilepostDesignPlanDto::setReceiptsDtos, BeanConverter.copy(receiptsList, ProjectWbsReceiptsDto.class));
        Project project = projectBusinessService.selectByPrimaryKey(projectId);
        if (project != null) {
            builder = builder.with(MilepostDesignPlanDto::setProjectName, project.getName()).with(MilepostDesignPlanDto::setProjectCode,
                            project.getCode())
                    .with(MilepostDesignPlanDto::setProjectStatus, project.getStatus());
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null) {
                builder = builder.with(MilepostDesignPlanDto::setStorageId, projectProfit.getStorageId());
            }
        }


        MilepostDesignPlanDto milepostDesignPlanDto = builder.buildForParallel();


        return milepostDesignPlanDto;
    }


    /**
     * 处理详设变更的编辑权限
     * 设置符合条件节点(模组且最后一级)的编辑状态
     *
     * @param projectId                   项目ID
     * @param conditionQueryDetailDtoList 详设计划详情列表
     * @param editQuery                   编辑查询标识(1表示详设变更)
     */
    private void processDesignChangeEditPermission(Long projectId, List<MilepostDesignPlanDetailDto> conditionQueryDetailDtoList, Integer editQuery, String wbsRequirementCode) {
        if (!Objects.equals(1, editQuery)) {
            logger.debug("非详设变更模式，跳过编辑权限设置");
            return;
        }

        logger.info("开始处理详设变更的编辑权限设置，项目ID：{}", projectId);

        // 创建过滤后的列表，只包含符合条件的节点(whetherModel=1且wbsLastLayer=1)
        List<MilepostDesignPlanDetailDto> filteredList = conditionQueryDetailDtoList.stream()
                .filter(dto -> (Boolean.TRUE.equals(dto.getWhetherModel()) && Boolean.TRUE.equals(dto.getWbsLastLayer())))
                .collect(Collectors.toList());

        logger.info("过滤后需要设置编辑权限的节点数量：{}", filteredList.size());

        if (filteredList.isEmpty()) {
            logger.info("没有符合条件的节点需要设置编辑权限，处理完成");
            return;
        }

        // 预先加载所有可能需要的子节点数据，避免递归中的重复查询
        long startTime = System.currentTimeMillis();

        // 1. 收集所有节点ID
        Set<Long> allNodeIds = new HashSet<>();
        for (MilepostDesignPlanDetailDto dto : filteredList) {
            allNodeIds.add(dto.getId());
        }

        logger.debug("初始节点ID集合大小：{}", allNodeIds.size());

        // 2. 一次性查询所有节点的子节点关系（递归查询所有层级）
        Map<Long, List<MilepostDesignPlanDetail>> parentChildrenMap = new HashMap<>();
        Set<Long> processedIds = new HashSet<>();
        int queryRounds = 0;

        while (!allNodeIds.isEmpty()) {
            queryRounds++;
            int currentBatchSize = allNodeIds.size();
            logger.debug("第{}轮子节点查询，当前批次节点数：{}", queryRounds, currentBatchSize);

            // 获取尚未处理的节点ID
            List<Long> batchIds = new ArrayList<>(allNodeIds);
            processedIds.addAll(allNodeIds);
            allNodeIds.clear();

            // 批量查询这些节点的直接子节点
            MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
            example.createCriteria()
                    .andParentIdIn(batchIds)
                    .andDeletedFlagEqualTo(Boolean.FALSE);

            List<MilepostDesignPlanDetail> children = milepostDesignPlanDetailMapper.selectByExample(example);
            logger.debug("查询到{}个子节点", children.size());

            // 按父节点ID分组
            Map<Long, List<MilepostDesignPlanDetail>> batchMap = children.stream()
                    .collect(Collectors.groupingBy(MilepostDesignPlanDetail::getParentId));

            // 合并到总映射
            parentChildrenMap.putAll(batchMap);

            // 收集下一批需要处理的节点ID
            for (MilepostDesignPlanDetail child : children) {
                if (!processedIds.contains(child.getId())) {
                    allNodeIds.add(child.getId());
                }
            }
        }

        logger.info("预加载子节点关系耗时：{}ms，共{}轮查询，加载{}个父节点的子节点关系",
                System.currentTimeMillis() - startTime, queryRounds, parentChildrenMap.size());

        // 3. 检查方法，传入预加载的数据
        startTime = System.currentTimeMillis();
        checkEditAbleWithPreloadedData(filteredList, parentChildrenMap);
        logger.info("优化版checkEditAble方法执行耗时：{}ms", System.currentTimeMillis() - startTime);

        // 将过滤后列表中节点的editable属性同步回原列表中对应的节点
        Map<Long, Boolean> editableMap = filteredList.stream()
                .collect(Collectors.toMap(MilepostDesignPlanDetailDto::getId, MilepostDesignPlanDetailDto::getEditable));

        logger.info("编辑权限映射表大小：{}，可编辑节点数：{}",
                editableMap.size(),
                editableMap.values().stream().filter(Boolean::booleanValue).count());

        conditionQueryDetailDtoList.forEach(dto -> {
            if (editableMap.containsKey(dto.getId())) {
                dto.setEditable(editableMap.get(dto.getId()));
            } else {
                // 如果在映射表中找不到，则默认设置为false
                dto.setEditable(false);
            }
        });

        if (StringUtils.isNotBlank(wbsRequirementCode)) {
            setEditableByPamCode(conditionQueryDetailDtoList, wbsRequirementCode);
        }

        logger.info("详设变更编辑权限设置完成");
    }

    /**
     * 根据变更单据中的pamCode设置编辑权限（仅处理当前层级节点）
     *
     * @param detailDtoList      详设计划详情列表
     * @param wbsRequirementCode 变更单据编号
     */
    private void setEditableByPamCode(List<MilepostDesignPlanDetailDto> detailDtoList, String wbsRequirementCode) {
        logger.info("开始根据变更单据编号设置pamCode编辑权限，单据编号：{}", wbsRequirementCode);

        try {
            // 根据单据编号查询ID
            ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsService.getByRequirementCode(wbsRequirementCode);
            if (projectWbsReceipts == null) {
                logger.warn("未找到对应的变更单据，单据编号：{}", wbsRequirementCode);
                return;
            }

            Long wbsReceiptsId = projectWbsReceipts.getId();
            logger.debug("查询到变更单据ID：{}", wbsReceiptsId);

            MilepostDesignPlanDetailChangeExample changeExample = new MilepostDesignPlanDetailChangeExample();
            changeExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                    .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode())
                    .andProjectWbsReceiptsIdEqualTo(wbsReceiptsId);

            List<MilepostDesignPlanDetailChange> changes = milepostDesignPlanDetailChangeService.selectByExample(changeExample);
            logger.info("查询到变更记录数量：{}", changes != null ? changes.size() : 0);

            if (ListUtils.isNotEmpty(changes)) {
                int matchCount = 0;
                for (MilepostDesignPlanDetailDto dto : detailDtoList) {
                    for (MilepostDesignPlanDetailChange change : changes) {
                        if (StringUtils.isNotEmpty(change.getPamCode()) &&
                                StringUtils.isNotEmpty(dto.getPamCode()) &&
                                Objects.equals(change.getPamCode(), dto.getPamCode())) {
                            dto.setEditable(true);
                            matchCount++;
                        }
                    }
                }
                logger.info("根据pamCode匹配成功并设置可编辑的节点数量：{}", matchCount);
            } else {
                logger.info("未找到相关变更记录，不进行pamCode编辑权限设置");
            }
        } catch (Exception e) {
            logger.error("根据pamCode设置编辑权限异常", e);
        }
    }

    /**
     * 根据变更单据中的pamCode递归设置编辑权限（处理所有层级节点）
     *
     * @param detailDtoList      详设计划详情列表
     * @param wbsRequirementCode 变更单据编号
     */
    private void setEditableByPamCodeRecursively(List<MilepostDesignPlanDetailDto> detailDtoList, String wbsRequirementCode) {
        logger.info("开始递归设置pamCode编辑权限，单据编号：{}", wbsRequirementCode);

        try {
            // 增加空值检查，避免传入null值到getByRequirementCode方法
            if (StringUtils.isEmpty(wbsRequirementCode)) {
                logger.warn("变更单据编号为空，跳过pamCode编辑权限设置");
                return;
            }

            // 根据单据编号查询ID
            ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsService.getByRequirementCode(wbsRequirementCode);
            if (projectWbsReceipts == null) {
                logger.warn("未找到对应的变更单据，单据编号：{}", wbsRequirementCode);
                return;
            }

            Long wbsReceiptsId = projectWbsReceipts.getId();

            // 查询变更记录
            MilepostDesignPlanDetailChangeExample changeExample = new MilepostDesignPlanDetailChangeExample();
            changeExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                    .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode())
                    .andProjectWbsReceiptsIdEqualTo(wbsReceiptsId);

            List<MilepostDesignPlanDetailChange> changes = milepostDesignPlanDetailChangeService.selectByExample(changeExample);
            logger.info("查询到变更记录数量：{}", changes != null ? changes.size() : 0);

            if (ListUtils.isNotEmpty(changes)) {
                // 创建pamCode到变更记录的映射，提高查找效率
                Map<String, MilepostDesignPlanDetailChange> pamCodeChangeMap = changes.stream()
                        .filter(change -> StringUtils.isNotEmpty(change.getPamCode()))
                        .collect(Collectors.toMap(
                                MilepostDesignPlanDetailChange::getPamCode,
                                change -> change,
                                (existing, replacement) -> existing  // 如有重复，保留第一个
                        ));

                // 递归处理节点及其子节点
                int matchCount = processNodesRecursively(detailDtoList, pamCodeChangeMap);
                logger.info("递归处理后设置可编辑的节点总数：{}", matchCount);
            } else {
                logger.info("未找到相关变更记录，不进行pamCode编辑权限设置");
            }
        } catch (Exception e) {
            logger.error("递归设置pamCode编辑权限异常", e);
        }
    }

    /**
     * 递归处理节点及其子节点的编辑权限
     *
     * @param detailDtoList    节点列表
     * @param pamCodeChangeMap pamCode到变更记录的映射
     * @return 设置为可编辑的节点数量
     */
    private int processNodesRecursively(List<MilepostDesignPlanDetailDto> detailDtoList,
                                        Map<String, MilepostDesignPlanDetailChange> pamCodeChangeMap) {
        int matchCount = 0;

        if (CollectionUtils.isEmpty(detailDtoList)) {
            return matchCount;
        }

        for (MilepostDesignPlanDetailDto dto : detailDtoList) {
            // 处理当前节点
            if (StringUtils.isNotEmpty(dto.getPamCode()) && pamCodeChangeMap.containsKey(dto.getPamCode())) {
                dto.setEditable(true);
                matchCount++;
            }

            // 递归处理子节点
            if (ListUtils.isNotEmpty(dto.getSonDtos())) {
                matchCount += processNodesRecursively(dto.getSonDtos(), pamCodeChangeMap);
            }
        }

        return matchCount;
    }

    /**
     * 使用预加载数据的优化版检查节点是否可编辑方法
     *
     * @param planDetailDtoList 需要检查的节点列表
     * @param parentChildrenMap 预加载的父子节点关系映射
     * @return 是否可编辑
     */
    private Boolean checkEditAbleWithPreloadedData(List<MilepostDesignPlanDetailDto> planDetailDtoList,
                                                   Map<Long, List<MilepostDesignPlanDetail>> parentChildrenMap) {
        if (CollectionUtils.isEmpty(planDetailDtoList)) {
            return true;
        }

        // 检查是否有状态为草稿、待审批或审核中的节点
        boolean hasSpecialStatus = planDetailDtoList.stream().anyMatch(dto ->
                CheckStatus.WBS_RECEIPTS_DRAFT.code().equals(dto.getStatus())
                        || CheckStatus.WBS_RECEIPTS_PENDING.code().equals(dto.getStatus())
                        || CheckStatus.WBS_RECEIPTS_CHECKING.code().equals(dto.getStatus()));

        if (hasSpecialStatus) {
            // 如果有特殊状态，则设置对应节点的editable
            planDetailDtoList.forEach(detailDto -> {
                boolean isSpecialStatus = CheckStatus.WBS_RECEIPTS_DRAFT.code().equals(detailDto.getStatus())
                        || CheckStatus.WBS_RECEIPTS_PENDING.code().equals(detailDto.getStatus())
                        || CheckStatus.WBS_RECEIPTS_CHECKING.code().equals(detailDto.getStatus());
                detailDto.setEditable(!isSpecialStatus);
            });
            return false;
        }

        // 处理所有节点
        boolean allEditable = true;
        for (MilepostDesignPlanDetailDto parentDto : planDetailDtoList) {
            boolean parentEditable;

            // 从预加载数据中获取当前节点的所有子节点
            List<MilepostDesignPlanDetail> childDetails = parentChildrenMap.getOrDefault(parentDto.getId(), Collections.emptyList());

            if (CollectionUtils.isEmpty(childDetails)) {
                // 没有子节点，可编辑
                parentEditable = true;
            } else {
                // 转换子节点为DTO
                List<MilepostDesignPlanDetailDto> childDtos = BeanConverter.convert(
                        childDetails, MilepostDesignPlanDetailDto.class);

                // 递归检查子节点，使用预加载数据
                parentEditable = checkEditAbleWithPreloadedData(childDtos, parentChildrenMap);
            }

            // 设置当前节点的可编辑状态
            parentDto.setEditable(parentEditable);

            // 更新整体可编辑状态
            if (!parentEditable) {
                allEditable = false;
            }
        }

        return allEditable;
    }

    /**
     * 合并并设置需求编码和状态信息
     * <p>
     * 该方法从三个临时表(T1、T2、T3)中查询需求编码和状态信息，并按优先级合并：
     * 1. 优先使用T1表数据
     * 2. 如T1无数据，则使用T2表数据
     * 3. 如T2也无数据，则使用T3表数据
     * 只为最后一层WBS节点设置需求编码和状态
     * </p>
     *
     * @param conditionQueryDetailDtoList 需要设置需求编码的详设计划详情列表
     */
    private void mergeAndSetRequirementInfo(List<MilepostDesignPlanDetailDto> conditionQueryDetailDtoList) {
        List<Long> planDetailIds = new ArrayList<>();
        Map<Long, MilepostDesignPlanFieldDto> mergeMap = new HashMap<>();
        for (MilepostDesignPlanDetailDto detailDto : conditionQueryDetailDtoList) {
            if (detailDto.getWbsLastLayer() == null || Boolean.TRUE.equals(detailDto.getWbsLastLayer())) {
                planDetailIds.add(detailDto.getId());
            }
        }
        if (ListUtils.isNotEmpty(planDetailIds)) {
            Long projectId = conditionQueryDetailDtoList.stream().map(MilepostDesignPlanDetail::getProjectId).distinct().findFirst().get();
            //三个临时表的查询
            List<MilepostDesignPlanFieldDto> t1List = milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanT1(projectId);
            Map<Long, MilepostDesignPlanFieldDto> t1Map = t1List.stream().collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId,
                    Function.identity()));
            List<MilepostDesignPlanFieldDto> t2List = milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanT2(projectId);
            Map<Long, MilepostDesignPlanFieldDto> t2Map = t2List.stream().collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId,
                    Function.identity()));
            List<MilepostDesignPlanFieldDto> t3List = milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanT3(projectId);
            Map<Long, MilepostDesignPlanFieldDto> t3Map = t3List.stream().collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId,
                    Function.identity()));
            //合并数据,如果表1有数据以表1为准,如果表1没有取表2,如果表2也没有,取表3
            mergeMap = planDetailIds.stream().map(x -> {
                MilepostDesignPlanFieldDto dto = new MilepostDesignPlanFieldDto();
                dto.setDesignPlanDetailId(x);
                dto.setRequirementCode(t1Map.containsKey(x) ? t1Map.get(x).getRequirementCode() : (t2Map.containsKey(x) ?
                        t2Map.get(x).getRequirementCode() : (t3Map.containsKey(x) ? t3Map.get(x).getRequirementCode() : null)));
                dto.setRequirementStatus(t1Map.containsKey(x) ? t1Map.get(x).getRequirementStatus() : (t2Map.containsKey(x) ?
                        t2Map.get(x).getRequirementStatus() : (t3Map.containsKey(x) ? t3Map.get(x).getRequirementStatus() : null)));
                return dto;
            }).collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId, Function.identity()));
        }

        for (MilepostDesignPlanDetailDto x : conditionQueryDetailDtoList) {
            //需求发布单据编号 pwr2,pwr,pwr3 requirementCode
            if (x.getWbsLastLayer() == null || Boolean.TRUE.equals(x.getWbsLastLayer())) {
                x.setRequirementCode(mergeMap.containsKey(x.getId()) ? mergeMap.get(x.getId()).getRequirementCode() : null);
                //单据状态 0-草稿、1-待处理、2-审核中、3-驳回、4-生效、5-作废 pwr2,pwr,pwr3 requirementStatus
                x.setRequirementStatus(mergeMap.containsKey(x.getId()) ? mergeMap.get(x.getId()).getRequirementStatus() : null);
            }
        }
    }


    @Override
    public MilepostDesignPlanDto getDetailByProjectIdWithWBSEnabled(DetailByProjectIdAndMilepostIdNewWbsQuery query) {
        Asserts.notEmpty(query.getProjectId(), ErrorCode.CTC_PROJECT_ID_NULL);

        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                CollectionsUtil.ofNullable(milepostDesignPlanDetailService.getTheFirstLevelDetailByProjectId(query))
                        .changeToOptional().map(s -> s.parallelStream()
                                .map(MilepostDesignPlanDetail::getId)
                                .map(i -> milepostDesignPlanDetailService.getDesignPlanDetailForBusinessNew(null, i, null, query))
                                .collect(Collectors.toList())).orElse(new ArrayList<>());

        //提交记录
        MilepostDesignPlanSubmitRecordDto designPlanSubmitRecordQuery = new MilepostDesignPlanSubmitRecordDto();
        designPlanSubmitRecordQuery.setProjectId(query.getProjectId());
        designPlanSubmitRecordQuery.setProjectSubmit(false);
        designPlanSubmitRecordQuery.setStatusIsNull(false);


        //确认记录
        MilepostDesignPlanConfirmRecordDto designPlanConfirmRecordQuery = new MilepostDesignPlanConfirmRecordDto();
        designPlanConfirmRecordQuery.setProjectId(query.getProjectId());
        designPlanConfirmRecordQuery.setProjectSubmit(false);
        designPlanConfirmRecordQuery.setStatusIsNull(false);


        //提前采购记录
        MilepostDesignPlanPurchaseRecordDTO designPlanPurchaseRecordQuery = new MilepostDesignPlanPurchaseRecordDTO();
        designPlanPurchaseRecordQuery.setProjectId(query.getProjectId());
        designPlanPurchaseRecordQuery.setStatusIsNull(false);
        designPlanPurchaseRecordQuery.setOrderBy(true);

        //变更记录
        MilepostDesignPlanChangeRecordDto designPlanChangeRecordQuery = new MilepostDesignPlanChangeRecordDto();
        designPlanChangeRecordQuery.setProjectId(query.getProjectId());
        designPlanChangeRecordQuery.setStatusIsNull(false);

        //所有审批通过的提交记录对应的附件记录
        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
        attachmentDtos.addAll(milepostDesignPlanSubmitRecordService.allApprovedSubmitRecordAttachment(query.getProjectId()));

        //所有审批通过的变更记录对应的附件记录
        attachmentDtos.addAll(milepostDesignPlanChangeRecordService.allAttachFromAllPassedChangeRecord(query.getProjectId()));

        //过滤掉未审批通过的附件
        List<CtcAttachmentDto> attachments =
                attachmentDtos.stream().filter(attachment -> Objects.equals(attachment.getStatus(), AttachmentStatus.PASSED.code())).collect(Collectors.toList());
        attachments.sort(Comparator.comparing(CtcAttachmentDto::getCreateAt));
        Collections.reverse(attachments);
        Builder<MilepostDesignPlanDto> with =
                Builder.of(MilepostDesignPlanDto::new).with(MilepostDesignPlanDto::setDesignPlanDetailDtos,
                                setDesignTotalVal(milepostDesignPlanDetailDtos))
                        .with(MilepostDesignPlanDto::setSubmitRecordDtos,
                                milepostDesignPlanSubmitRecordService.selectList(designPlanSubmitRecordQuery))
                        .with(MilepostDesignPlanDto::setConfirmRecordDtos,
                                milepostDesignPlanConfirmRecordService.selectList(designPlanConfirmRecordQuery))
                        .with(MilepostDesignPlanDto::setPurchaseRecordDTOS,
                                milepostDesignPlanPurchaseRecordService.selectList(designPlanPurchaseRecordQuery)
                                        .stream().filter(a -> !Objects.equals(a.getStatus(),
                                                CheckStatus.PURCHASE_DELETE.code())).collect(Collectors.toList()))
                        .with(MilepostDesignPlanDto::setChangeRecordDtos,
                                milepostDesignPlanChangeRecordService.selectList(designPlanChangeRecordQuery))
                        .with(MilepostDesignPlanDto::setAttachmentDtos, attachments);
        Project project = projectBusinessService.selectByPrimaryKey(query.getProjectId());
        if (project != null) {
            with = with.with(MilepostDesignPlanDto::setProjectName, project.getName())
                    .with(MilepostDesignPlanDto::setProjectCode, project.getCode())
                    .with(MilepostDesignPlanDto::setProjectStatus, project.getStatus());

            //查询库存组织
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null) {
                with = with.with(MilepostDesignPlanDto::setStorageId, projectProfit.getStorageId());
            }
        }
        return with.buildForParallel();
    }

    @Override
    public MilepostDesignPlanDto getDetailByProjectIdAndMilepostIdNew(Long projectId, Long milepostId) {
        Guard.notNull(projectId, "项目id不能为空");
        Guard.notNull(milepostId, "里程碑id不能为空");

        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                CollectionsUtil.ofNullable(milepostDesignPlanDetailService.getTheFirstLevelDetailByMilepostId(milepostId))
                        .changeToOptional().map(s -> s.parallelStream().map(MilepostDesignPlanDetail::getId)
                                .map(i -> milepostDesignPlanDetailService.getDesignPlanDetailForBusinessNew(null, i, null, null))
                                .collect(Collectors.toList())).orElse(new ArrayList<>());
        logger.info("getDetailByProjectIdAndMilepostIdNew的milepostDesignPlanDetailDtos：{}", JSON.toJSONString(milepostDesignPlanDetailDtos));

        //提交记录
        MilepostDesignPlanSubmitRecordDto designPlanSubmitRecordQuery = new MilepostDesignPlanSubmitRecordDto();
        designPlanSubmitRecordQuery.setProjectId(projectId);
        designPlanSubmitRecordQuery.setMilepostId(milepostId);
        designPlanSubmitRecordQuery.setProjectSubmit(false);
        designPlanSubmitRecordQuery.setStatusIsNull(false);
        List<MilepostDesignPlanSubmitRecordDto> submitRecordDtos = milepostDesignPlanSubmitRecordService.selectList(designPlanSubmitRecordQuery);
        List<Long> submitCheckingWhetherModelDesignPlanDetailIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(submitRecordDtos)) {
            submitCheckingWhetherModelDesignPlanDetailIdList.addAll(submitRecordDtos.stream().filter(e -> Objects.equals(e.getStatus(),
                            CheckStatus.CHECKING.code()))
                    .map(MilepostDesignPlanSubmitRecordDto::getUploadPathId).distinct().collect(Collectors.toList()));
        }

        //确认记录
        MilepostDesignPlanConfirmRecordDto designPlanConfirmRecordQuery = new MilepostDesignPlanConfirmRecordDto();
        designPlanConfirmRecordQuery.setProjectId(projectId);
        designPlanConfirmRecordQuery.setMilepostId(milepostId);
        designPlanConfirmRecordQuery.setProjectSubmit(false);
        designPlanConfirmRecordQuery.setStatusIsNull(false);

        //提前采购记录
        MilepostDesignPlanPurchaseRecordDTO designPlanPurchaseRecordQuery = new MilepostDesignPlanPurchaseRecordDTO();
        designPlanPurchaseRecordQuery.setProjectId(projectId);
        designPlanPurchaseRecordQuery.setMilepostId(milepostId);
        designPlanPurchaseRecordQuery.setStatusIsNull(false);
        designPlanPurchaseRecordQuery.setOrderBy(true);

        //变更记录
        MilepostDesignPlanChangeRecordDto designPlanChangeRecordQuery = new MilepostDesignPlanChangeRecordDto();
        designPlanChangeRecordQuery.setProjectId(projectId);
        designPlanChangeRecordQuery.setMilepostId(milepostId);
        designPlanChangeRecordQuery.setStatusIsNull(false);
        List<MilepostDesignPlanChangeRecordDto> changeRecordDtos = milepostDesignPlanChangeRecordService.selectList(designPlanChangeRecordQuery);
        List<Long> mdpChangeCheckingWhetherModelDesignPlanDetailIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(changeRecordDtos)) {
            mdpChangeCheckingWhetherModelDesignPlanDetailIdList.addAll(changeRecordDtos.stream().filter(e -> Objects.equals(e.getStatus(),
                            CheckStatus.MDP_CHANGE_CHECKING.code()))
                    .map(MilepostDesignPlanChangeRecordDto::getUploadPathId).distinct().collect(Collectors.toList()));
        }

        //所有审批通过的提交记录对应的附件记录
        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
        attachmentDtos.addAll(milepostDesignPlanSubmitRecordService.allApprovedSubmitRecordAttachment(projectId));

        //所有审批通过的变更记录对应的附件记录
        attachmentDtos.addAll(milepostDesignPlanChangeRecordService.allAttachFromAllPassedChangeRecord(projectId));

        //过滤掉未审批通过的附件
        List<CtcAttachmentDto> attachments = attachmentDtos.stream().filter(attachment -> Objects.equals(attachment.getStatus(),
                        AttachmentStatus.PASSED.code()))
                .collect(Collectors.toList());
        attachments.sort(Comparator.comparing(CtcAttachmentDto::getCreateAt));
        Collections.reverse(attachments);

        Builder<MilepostDesignPlanDto> with = Builder.of(MilepostDesignPlanDto::new)
                .with(MilepostDesignPlanDto::setMilepostDto, projectMilepostService.getById(milepostId))
                .with(MilepostDesignPlanDto::setDesignPlanDetailDtos, setDesignTotalVal(milepostDesignPlanDetailDtos))
                .with(MilepostDesignPlanDto::setSubmitRecordDtos, submitRecordDtos)
                .with(MilepostDesignPlanDto::setConfirmRecordDtos, milepostDesignPlanConfirmRecordService.selectList(designPlanConfirmRecordQuery))
                .with(MilepostDesignPlanDto::setPurchaseRecordDTOS, milepostDesignPlanPurchaseRecordService.selectList(designPlanPurchaseRecordQuery)
                        .stream().filter(a -> !Objects.equals(a.getStatus(), CheckStatus.PURCHASE_DELETE.code())).collect(Collectors.toList()))
                .with(MilepostDesignPlanDto::setChangeRecordDtos, changeRecordDtos)
                .with(MilepostDesignPlanDto::setAttachmentDtos, attachments)
                .with(MilepostDesignPlanDto::setMdpChangeCheckingWhetherModelDesignPlanDetailIdList,
                        mdpChangeCheckingWhetherModelDesignPlanDetailIdList)
                .with(MilepostDesignPlanDto::setSubmitCheckingWhetherModelDesignPlanDetailIdList, submitCheckingWhetherModelDesignPlanDetailIdList);
        Project project = projectBusinessService.selectByPrimaryKey(projectId);
        if (project != null) {
            with = with.with(MilepostDesignPlanDto::setProjectName, project.getName())
                    .with(MilepostDesignPlanDto::setProjectCode, project.getCode())
                    .with(MilepostDesignPlanDto::setProjectStatus, project.getStatus());
            //查询库存组织
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null) {
                with = with.with(MilepostDesignPlanDto::setStorageId, projectProfit.getStorageId());
            }
        }
        return with.buildForParallel();
    }

    @Override
    public MilepostDesignPlanDto getDetailByProjectIdAndMilepostIdNewWbs(DetailByProjectIdAndMilepostIdNewWbsQuery query) {
        Guard.notNull(query, "查询参数为空");
        Guard.notNull(query.getProjectId(), "项目id不能为空");

        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                CollectionsUtil.ofNullable(milepostDesignPlanDetailService.getTheFirstLevelDetailByProjectId(query))
                        .changeToOptional().map(s -> s.parallelStream()
                                .map(MilepostDesignPlanDetail::getId)
                                .map(i -> milepostDesignPlanDetailService.getDesignPlanDetailForBusinessNew(null, i,
                                        null, query))
                                .collect(Collectors.toList())).orElse(new ArrayList<>());

        //提交记录
        MilepostDesignPlanSubmitRecordDto designPlanSubmitRecordQuery = new MilepostDesignPlanSubmitRecordDto();
        designPlanSubmitRecordQuery.setProjectId(query.getProjectId());
        designPlanSubmitRecordQuery.setProjectSubmit(false);
        designPlanSubmitRecordQuery.setStatusIsNull(false);

        //确认记录
        MilepostDesignPlanConfirmRecordDto designPlanConfirmRecordQuery = new MilepostDesignPlanConfirmRecordDto();
        designPlanConfirmRecordQuery.setProjectId(query.getProjectId());
        designPlanConfirmRecordQuery.setProjectSubmit(false);
        designPlanConfirmRecordQuery.setStatusIsNull(false);

        //提前采购记录
        MilepostDesignPlanPurchaseRecordDTO designPlanPurchaseRecordQuery = new MilepostDesignPlanPurchaseRecordDTO();
        designPlanPurchaseRecordQuery.setProjectId(query.getProjectId());
        designPlanPurchaseRecordQuery.setStatusIsNull(false);
        designPlanPurchaseRecordQuery.setOrderBy(true);

        //变更记录
        MilepostDesignPlanChangeRecordDto designPlanChangeRecordQuery = new MilepostDesignPlanChangeRecordDto();
        designPlanChangeRecordQuery.setProjectId(query.getProjectId());
        designPlanChangeRecordQuery.setStatusIsNull(false);
        //所有审批通过的提交记录对应的附件记录
        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
        attachmentDtos.addAll(milepostDesignPlanSubmitRecordService.allApprovedSubmitRecordAttachment(query.getProjectId()));

        //所有审批通过的变更记录对应的附件记录
        attachmentDtos.addAll(milepostDesignPlanChangeRecordService.allAttachFromAllPassedChangeRecord(query.getProjectId()));

        //过滤掉未审批通过的附件
        List<CtcAttachmentDto> attachments = attachmentDtos.stream().filter(attachment -> Objects.equals(attachment.getStatus(),
                        AttachmentStatus.PASSED.code()))
                .collect(Collectors.toList());
        attachments.sort(Comparator.comparing(CtcAttachmentDto::getCreateAt));
        Collections.reverse(attachments);

        Builder<MilepostDesignPlanDto> with = Builder.of(MilepostDesignPlanDto::new)
                .with(MilepostDesignPlanDto::setDesignPlanDetailDtos, setDesignTotalVal(milepostDesignPlanDetailDtos))
                .with(MilepostDesignPlanDto::setSubmitRecordDtos, milepostDesignPlanSubmitRecordService.selectList(designPlanSubmitRecordQuery))
                .with(MilepostDesignPlanDto::setConfirmRecordDtos, milepostDesignPlanConfirmRecordService.selectList(designPlanConfirmRecordQuery))
                .with(MilepostDesignPlanDto::setPurchaseRecordDTOS, milepostDesignPlanPurchaseRecordService.selectList(designPlanPurchaseRecordQuery)
                        .stream().filter(a -> !Objects.equals(a.getStatus(), CheckStatus.PURCHASE_DELETE.code())).collect(Collectors.toList()))
                .with(MilepostDesignPlanDto::setChangeRecordDtos, milepostDesignPlanChangeRecordService.selectList(designPlanChangeRecordQuery))
                .with(MilepostDesignPlanDto::setAttachmentDtos, attachments);
        Project project = projectBusinessService.selectByPrimaryKey(query.getProjectId());
        if (project != null) {
            with = with.with(MilepostDesignPlanDto::setProjectName, project.getName())
                    .with(MilepostDesignPlanDto::setProjectCode, project.getCode())
                    .with(MilepostDesignPlanDto::setProjectStatus, project.getStatus());
            //查询库存组织
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null) {
                with = with.with(MilepostDesignPlanDto::setStorageId, projectProfit.getStorageId());
            }
        }
        return with.buildForParallel();
    }

    @Override
    public Map<String, Object> getMaterialStatusDetailByProjectIdAndMilepostId(Long projectId, Long milepostId) {
        Guard.notNull(projectId, "项目id不能为空");

        Project project = projectService.selectByPrimaryKey(projectId);
        if (!Boolean.TRUE.equals(project.getWbsEnabled())) {
            Guard.notNull(milepostId, "里程碑id不能为空");
        }
        ProjectProfitDto projectProfitDto = projectProfitService.findProfitDetail(projectId);

        Map<String, Object> resultMap = new HashMap<>();
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        MilepostDesignPlanDetailExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId).andMaterialCategoryIn(Lists.newArrayList("外购物料", "看板物料")).andDeletedFlagEqualTo(false);
        if (!Boolean.TRUE.equals(project.getWbsEnabled())) {
            criteria.andMilepostIdEqualTo(milepostId);
        }
        List<MilepostDesignPlanDetail> erpPlanDetailList = milepostDesignPlanDetailMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(erpPlanDetailList)) {
            // 物料未同步ERP数 erp_code为空的也要算进去
            List<String> erpCodeList =
                    erpPlanDetailList.stream().map(MilepostDesignPlanDetail::getErpCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            resultMap.put("erpPlanDetailSize", erpPlanDetailList.size());
            if (CollectionUtils.isNotEmpty(erpCodeList)) {
                Map<String, Integer> countMap = new HashMap<>();
                for (String erpCode : erpCodeList) {
                    if (countMap.containsKey(erpCode)) {
                        countMap.put(erpCode, countMap.get(erpCode) + 1);
                    } else {
                        countMap.put(erpCode, 1);
                    }
                }
                List<String> finalCodeList = materialExtService.getNotSuccessErpCodeList(erpCodeList,
                        Optional.ofNullable(projectProfitDto).map(ProjectProfitDto::getStorageId).orElse(null));
                // erp_code不为空的部分去物料表查有多少个未同步的  +  erp_code为空的部分
                resultMap.put("notSuccessErpCodeSize", (CollectionUtils.isEmpty(finalCodeList) ? 0 :
                        finalCodeList.stream().map(erpCode -> countMap.get(erpCode)).reduce(Integer::sum).orElse(0)) + erpPlanDetailList.size() - erpCodeList.size());
            } else {
                resultMap.put("notSuccessErpCodeSize", erpPlanDetailList.size());
            }

            // 未生成采购需求数 统计的逻辑应该把条件：并且ERP物料编码不为空 去掉（只要是外购物料，不管目前是否有ERP编码，最后都应该生成采购需求）
            List<MilepostDesignPlanDetail> generatePurchaseList =
                    erpPlanDetailList.stream().filter(e -> Objects.equals(e.getMaterialCategory(), "外购物料")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(generatePurchaseList)) {
                resultMap.put("generatePurchaseTotalSize", generatePurchaseList.size());
                //统计的逻辑应该把条件：并且ERP物料编码不为空 去掉，改为该项目物料类型为：外购物料中，未产生需求的数量
                resultMap.put("notGeneratePurchaseSize",
                        generatePurchaseList.stream().filter(e -> Objects.isNull(e.getGenerateRequirement()) || !e.getGenerateRequirement()).count());
            } else {
                resultMap.put("generatePurchaseTotalSize", 0);
                resultMap.put("notGeneratePurchaseSize", 0);
            }
        } else {
            resultMap.put("erpPlanDetailSize", 0);
            resultMap.put("notSuccessErpCodeSize", 0);
            resultMap.put("generatePurchaseTotalSize", 0);
            resultMap.put("notGeneratePurchaseSize", 0);
        }

        return resultMap;
    }

    @Override
    public Map<String, Object> getMaterialStatusDetailByProjectId(Long projectId) {
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);

        ProjectProfitDto projectProfitDto = projectProfitService.findProfitDetail(projectId);

        Map<String, Object> resultMap = new HashMap<>();
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andProjectIdEqualTo(projectId).andMaterialCategoryIn(Lists.newArrayList("外购物料",
                "看板物料")).andDeletedFlagEqualTo(false);
        List<MilepostDesignPlanDetail> erpPlanDetailList = milepostDesignPlanDetailMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(erpPlanDetailList)) {
            // 物料未同步ERP数 erp_code为空的也要算进去
            List<String> erpCodeList =
                    erpPlanDetailList.stream().map(MilepostDesignPlanDetail::getErpCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            resultMap.put("erpPlanDetailSize", erpPlanDetailList.size());
            if (CollectionUtils.isNotEmpty(erpCodeList)) {
                Map<String, Integer> countMap = new HashMap<>();
                for (String erpCode : erpCodeList) {
                    if (countMap.containsKey(erpCode)) {
                        countMap.put(erpCode, countMap.get(erpCode) + 1);
                    } else {
                        countMap.put(erpCode, 1);
                    }
                }
                List<String> finalCodeList = materialExtService.getNotSuccessErpCodeList(erpCodeList,
                        Optional.ofNullable(projectProfitDto).map(ProjectProfitDto::getStorageId).orElse(null));
                // erp_code不为空的部分去物料表查有多少个未同步的  +  erp_code为空的部分
                resultMap.put("notSuccessErpCodeSize", (CollectionUtils.isEmpty(finalCodeList) ? 0 :
                        finalCodeList.stream().map(erpCode -> countMap.get(erpCode)).reduce(Integer::sum).orElse(0)) + erpPlanDetailList.size() - erpCodeList.size());
            } else {
                resultMap.put("notSuccessErpCodeSize", erpPlanDetailList.size());
            }

            // 未生成采购需求数 统计的逻辑应该把条件：并且ERP物料编码不为空 去掉（只要是外购物料，不管目前是否有ERP编码，最后都应该生成采购需求）
            List<MilepostDesignPlanDetail> generatePurchaseList =
                    erpPlanDetailList.stream().filter(e -> Objects.equals(e.getMaterialCategory(), "外购物料")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(generatePurchaseList)) {
                resultMap.put("generatePurchaseTotalSize", generatePurchaseList.size());
                resultMap.put("notGeneratePurchaseSize",
                        generatePurchaseList.stream().filter(e -> Objects.isNull(e.getGenerateRequirement()) || !e.getGenerateRequirement()).count());
            } else {
                resultMap.put("generatePurchaseTotalSize", 0);
                resultMap.put("notGeneratePurchaseSize", 0);
            }
        } else {
            resultMap.put("erpPlanDetailSize", 0);
            resultMap.put("notSuccessErpCodeSize", 0);
            resultMap.put("generatePurchaseTotalSize", 0);
            resultMap.put("notGeneratePurchaseSize", 0);
        }

        return resultMap;
    }

    @Override
    public List<String> getDynamicPamCodeOrErpCodeList(Long projectId, Long milepostId, boolean isSynchronizedErp,
                                                       boolean isGeneratePurchase) {
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);

        ProjectProfitDto projectProfitDto = projectProfitService.findProfitDetail(projectId);

        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        MilepostDesignPlanDetailExample.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(milepostId)) {
            criteria.andMilepostIdEqualTo(milepostId);
        }
        criteria.andProjectIdEqualTo(projectId).andMaterialCategoryIn(Lists.newArrayList("外购物料", "看板物料")).andDeletedFlagEqualTo(false);
        List<MilepostDesignPlanDetail> erpPlanDetailList = milepostDesignPlanDetailMapper.selectByExample(example);
        // 物料未同步ERP数 和 未生成采购需求数都为0
        if (CollectionUtils.isEmpty(erpPlanDetailList)) {
            return new ArrayList<>();
        }

        // 物料未同步ERP 对应的pam_code 集合
        if (isSynchronizedErp) {
            Map<Long, String> pamCodeMap = new HashMap<>();
            Map<Long, String> erpCodeMap = new HashMap<>();
            for (MilepostDesignPlanDetail detail : erpPlanDetailList) {
                pamCodeMap.put(detail.getId(), detail.getPamCode());
                erpCodeMap.put(detail.getId(), detail.getErpCode());
            }
            List<String> erpCodeList =
                    erpCodeMap.values().stream().filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(erpCodeList)) {
                Set<String> notSuccessPamCodeSet = new HashSet<>();
                List<String> notSuccessErpCodeList = materialExtService.getNotSuccessErpCodeList(erpCodeList,
                        Optional.ofNullable(projectProfitDto).map(ProjectProfitDto::getStorageId).orElse(null));
                pamCodeMap.forEach((k, v) -> {
                    String erpCode = erpCodeMap.get(k);
                    // erpCode为空的部分 + erpCode不为空时去物料表查有多少个未同步的
                    if (StringUtils.isEmpty(erpCode) || notSuccessErpCodeList.contains(erpCode)) {
                    }
                });
                return new ArrayList<>(notSuccessPamCodeSet);
            } else {
                return pamCodeMap.values().stream().distinct().collect(Collectors.toList());
            }
        }

        // 未生成采购需求 对应的erp_code 集合
        if (isGeneratePurchase) {
            Set<String> notGeneratePurchaseErpCodeSet = new HashSet<>();
            List<MilepostDesignPlanDetail> generatePurchaseList =
                    erpPlanDetailList.stream().filter(e -> Objects.equals(e.getMaterialCategory(), "外购物料")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(generatePurchaseList)) {
                /**这里取值修改为要包含ERP_CODE为空的情况,因而返回参数需改为返回的行ID*/
                notGeneratePurchaseErpCodeSet.addAll(generatePurchaseList.stream().filter(e -> (Objects.isNull(e.getGenerateRequirement()) || !e.getGenerateRequirement()))
                        .map(x -> Long.toString(x.getId())).collect(Collectors.toSet()));
            }
            return new ArrayList<>(notGeneratePurchaseErpCodeSet);
        }
        return new ArrayList<>();

    }

    /**
     * 仅支持3层结构
     *
     * @param submitId
     * @return
     */
    @Override
    public MilepostDesignPlanDto getDetailBySubmitId(Long submitId) {
        Asserts.notEmpty(submitId, ErrorCode.CTC_MILEPOST_DESIGN_PLAN_SUBMIT_ID_NULL);
        MilepostDesignPlanDto planDto = new MilepostDesignPlanDto();


        //提交记录对应的附件记录
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code());
        attachmentQuery.setModuleId(submitId);
        planDto.setAttachmentDtos(ctcAttachmentService.selectList(attachmentQuery));

        //提交记录
        MilepostDesignPlanSubmitRecordDto designPlanSubmitRecordQuery = new MilepostDesignPlanSubmitRecordDto();
        designPlanSubmitRecordQuery.setId(submitId);
        planDto.setSubmitRecordDtos(milepostDesignPlanSubmitRecordService.selectList(designPlanSubmitRecordQuery));

        if (ListUtils.isEmpty(planDto.getSubmitRecordDtos())) {
            return planDto;
        }

        MilepostDesignPlanSubmitRecordDto submitRecordDto = planDto.getSubmitRecordDtos().get(0);

        Project project = projectService.selectByPrimaryKey(submitRecordDto.getProjectId());


        Long uploadPathId = null;

        List<Long> uploadPathIds = new ArrayList<>();

        //获取提交记录上传模组id
        if (ObjectUtil.isNotEmpty(submitRecordDto.getUploadPathId())) {
            uploadPathId = submitRecordDto.getUploadPathId();
        } else if (ObjectUtil.isNotEmpty(submitRecordDto.getBatchUploadPathIds())) {
            uploadPathIds =
                    Arrays.stream(submitRecordDto.getBatchUploadPathIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        }

/*
        //设计BOM
        if (ListUtil.isPresent(planDto.getSubmitRecordDtos())) {

            planDto.setProjectId(submitRecordDto.getProjectId());
            planDto.setMilepostId(submitRecordDto.getMilepostId());

            //设计信息
            List<Long> theFirstLevelDetailIds = ListUtil.map(milepostDesignPlanDetailService
            .getTheFirstLevelDetailByMilepostId(submitRecordDto.getMilepostId()), "id");
            List<MilepostDesignPlanDetailDto> firstDesignPlanDetailDtos = new ArrayList<>();
            for (Long theFirstLevelDetailId : theFirstLevelDetailIds) {
                //第一层物料
                MilepostDesignPlanDetailDto firstDesignPlanDetailDto = milepostDesignPlanDetailService
                .getDesignPlanDetailForBusiness(null, theFirstLevelDetailId, null);

                //遍历第二层物料id， 只显示有上传的模组
                List<MilepostDesignPlanDetailDto> sonDtos = new ArrayList<>();
                if (ListUtil.isPresent(firstDesignPlanDetailDto.getSonDtos())) {
                    boolean isUpload = false;
                    for (MilepostDesignPlanDetailDto secondaryDetailDto : firstDesignPlanDetailDto.getSonDtos()) {
                        if (secondaryDetailDto.getId().equals(uploadPathId) //只显保留上传的模组数据
                                || Objects.equals(submitRecordDto.getProjectSubmit(), Boolean.TRUE)) {//立项直接生成的详设
                            MilepostDesignPlanDetailDto designPlanDetailQuery = new MilepostDesignPlanDetailDto();
                            designPlanDetailQuery.setSubmitRecordId(submitId);
                            designPlanDetailQuery.setParentId(secondaryDetailDto.getId());
                            secondaryDetailDto.setSonDtos(milepostDesignPlanDetailService.selectList
                            (designPlanDetailQuery));
                            sonDtos.add(secondaryDetailDto);
                            isUpload = true;
                        }
                    }
                    if (isUpload) {//只显保留上传的模组数据
                        firstDesignPlanDetailDto.setSonDtos(sonDtos);
                        firstDesignPlanDetailDtos.add(firstDesignPlanDetailDto);
                    }
                }
            }
            planDto.setDesignPlanDetailDtos(firstDesignPlanDetailDtos);
        }*/

        //设计BOM 未启用wbs
        if (!Boolean.TRUE.equals(project.getWbsEnabled())) {
            if (ListUtil.isPresent(planDto.getSubmitRecordDtos())) {
                planDto.setProjectId(submitRecordDto.getProjectId());
                planDto.setMilepostId(submitRecordDto.getMilepostId());
                MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
                MilepostDesignPlanDetailExample.Criteria criteria = milepostDesignPlanDetailExample.createCriteria();
                criteria.andSubmitRecordIdEqualTo(submitId);
                List<MilepostDesignPlanDetail> milepostDesignPlanDetails =
                        milepostDesignPlanDetailMapper.selectByExample(milepostDesignPlanDetailExample);
                //根据提交记录查询提交的详细设计
                List<Long> ids = new ArrayList<>();
                for (MilepostDesignPlanDetail milepostDesignPlanDetail : milepostDesignPlanDetails) {
                    ids.add(milepostDesignPlanDetail.getId());
                }
                if (ListUtils.isNotEmpty(ids)) {
                    List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                            milepostDesignPlanDetailExtMapper.selectByIds(ids); //查询详设提交数据的所有上级数据
                    if (ListUtils.isNotEmpty(milepostDesignPlanDetailDtos)) {
                        List<MilepostDesignPlanDetailDto> list = buildTree(milepostDesignPlanDetailDtos); //构建树形结构
                        planDto.setDesignPlanDetailDtos(list);
                    }
                }
            }
        }

        //交付信息
        List<MilepostDesignPlanDetailDto> deliveryInformationDtos = new ArrayList<>();
        if (null != uploadPathId) {
            MilepostDesignPlanDetailDto deliveryInformationDto = milepostDesignPlanDetailService.getById(uploadPathId);
            if (null != deliveryInformationDto) {
                MilepostDesignPlanDetailDto sonDtoQuery = new MilepostDesignPlanDetailDto();
                sonDtoQuery.setSubmitRecordId(submitId);
                List<MilepostDesignPlanDetailDto> son = milepostDesignPlanDetailService.selectList(sonDtoQuery);
                if (ListUtils.isNotEmpty(son)) {
                    deliveryInformationDto.setSonDtos(son);
                }
                deliveryInformationDtos.add(deliveryInformationDto);
            }
        }//传了uploadPathIds为批量导入 现为启用wbs项目
        else if (ListUtils.isNotEmpty(uploadPathIds)) {
            MilepostDesignPlanDetailExample detailExample = new MilepostDesignPlanDetailExample();
            detailExample.createCriteria().andIdIn(uploadPathIds);
            List<MilepostDesignPlanDetail> designPlanDetails =
                    milepostDesignPlanDetailMapper.selectByExample(detailExample);
            if (ListUtils.isNotEmpty(designPlanDetails)) {
                MilepostDesignPlanDetailDto sonDtoQuery = new MilepostDesignPlanDetailDto();
                sonDtoQuery.setSubmitRecordId(submitId);
                List<MilepostDesignPlanDetailDto> son = milepostDesignPlanDetailService.selectList(sonDtoQuery);
                if (ListUtils.isNotEmpty(son)) {
                    List<MilepostDesignPlanDetailDto> topDetail = BeanConverter.copy(designPlanDetails,
                            MilepostDesignPlanDetailDto.class);
                    //根据uploadPathIds查询的topDetail为上传所选的模组 给一个新增标识
                    topDetail.forEach(detail -> detail.setIsNew(true));
                    List<MilepostDesignPlanDetailDto> allMilepostDesignPlanDetail = new ArrayList<>();
                    allMilepostDesignPlanDetail.addAll(topDetail);
                    allMilepostDesignPlanDetail.addAll(son);
                    List<MilepostDesignPlanDetailDto> designPlanDetailDtos =
                            findParentForPlanDetail(buildMilepostDesignPlanDetailTree(allMilepostDesignPlanDetail,
                                    topDetail));
                    planDto.setDesignPlanDetailDtos(designPlanDetailDtos);
                }
            }
        }

        //把查询的项目信息都加进去
        planDto.setProjectId(project.getId());
        planDto.setProjectCode(project.getCode());
        planDto.setProjectName(project.getName());
        planDto.setProjectStatus(project.getStatus());

        return planDto;
    }

    private List<MilepostDesignPlanDetailDto> buildTree(List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos) {

        // 根节点
        List<MilepostDesignPlanDetailDto> rootDetailDtos = new ArrayList<MilepostDesignPlanDetailDto>();

        // 组装所有的根节点
        for (MilepostDesignPlanDetailDto detailDto : milepostDesignPlanDetailDtos) {
            if (null != detailDto.getParentId() && -1 == detailDto.getParentId()) {
                rootDetailDtos.add(detailDto);
            }
        }
        //为根节点设置子节点，getClild递归调用
        for (MilepostDesignPlanDetailDto rootDetailDto : rootDetailDtos) {
            // 获取根节点下面的所有子节点，使用getClild方法
            List<MilepostDesignPlanDetailDto> childList = getChild(rootDetailDto.getId(), milepostDesignPlanDetailDtos);
            //给根节点设置子节点
            rootDetailDto.setSonDtos(childList);
        }

        return rootDetailDtos;

    }

    private List<MilepostDesignPlanDetailDto> getChild(Long id,
                                                       List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos) {
        // 子节点
        List<MilepostDesignPlanDetailDto> childList = new ArrayList<MilepostDesignPlanDetailDto>();
        for (MilepostDesignPlanDetailDto detailDtoAll : milepostDesignPlanDetailDtos) {
            // 遍历所有节点，将所有节点的父id与传过来的根节点的id进行比较，相等则说明：为该根节点的子节点
            if (detailDtoAll.getParentId().equals(id)) {
                childList.add(detailDtoAll);
            }
        }

        // 进行递归
        for (MilepostDesignPlanDetailDto dto : childList) {
            dto.setSonDtos(getChild(dto.getId(), milepostDesignPlanDetailDtos));
        }

        //如果节点下没有子节点，返回一个空List（递归退出)
        if (childList.size() == 0) {
            return new ArrayList<MilepostDesignPlanDetailDto>();
        }
        return childList;
    }


    @Override
    public MilepostDesignPlanDto getDetailBySubmitIdForStaging(Long submitId) {
        Asserts.notEmpty(submitId, ErrorCode.CTC_MILEPOST_DESIGN_PLAN_SUBMIT_ID_NULL);
        MilepostDesignPlanDto planDto = new MilepostDesignPlanDto();

        //提交记录对应的附件记录
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code());
        attachmentQuery.setModuleId(submitId);
        planDto.setAttachmentDtos(ctcAttachmentService.selectList(attachmentQuery));

        //提交记录
        MilepostDesignPlanSubmitRecordDto designPlanSubmitRecordQuery = new MilepostDesignPlanSubmitRecordDto();
        designPlanSubmitRecordQuery.setId(submitId);
        planDto.setSubmitRecordDtos(milepostDesignPlanSubmitRecordService.selectList(designPlanSubmitRecordQuery));

        MilepostDesignPlanSubmitRecordDto submitRecordDto = planDto.getSubmitRecordDtos().get(0);

        //获取提交记录上传模组id
        Long uploadPathId = submitRecordDto.getUploadPathId();

        //设计BOM
        if (ListUtil.isPresent(planDto.getSubmitRecordDtos())) {

            planDto.setProjectId(submitRecordDto.getProjectId());
            planDto.setMilepostId(submitRecordDto.getMilepostId());

            //设计信息
            List<MilepostDesignPlanDetailDto> designPlanDetailDtos =
                    milepostDesignPlanDetailService.getDesignPlanDetailForStaging(submitRecordDto.getId());
            planDto.setDesignPlanDetailDtos(designPlanDetailDtos);
        }

        //交付信息
        List<MilepostDesignPlanDetailDto> deliveryInformationDtos = new ArrayList<>();
        if (uploadPathId != null) {
            MilepostDesignPlanDetailDto deliveryInformationDto = milepostDesignPlanDetailService.getById(uploadPathId);
            MilepostDesignPlanDetailDto sonDtoQuery = new MilepostDesignPlanDetailDto();
            sonDtoQuery.setSubmitRecordId(submitId);
            deliveryInformationDto.setSonDtos(milepostDesignPlanDetailService.selectList(sonDtoQuery));
            deliveryInformationDtos.add(deliveryInformationDto);
        }


        return planDto;
    }

    @Override
    public MilepostDesignPlanDetailApprovedVO getDetailBySubmitId1(Long submitId) {
        MilepostDesignPlanDto milepostDesignPlanDto = this.getDetailBySubmitId(submitId);
        if (!ObjectUtils.isEmpty(milepostDesignPlanDto)) {
            if (milepostDesignPlanDto.getProjectId() != null && milepostDesignPlanDto.getMilepostId() != null) {
                MilepostDesignPlanDetailApprovedVO approvedDesignPlanDetail = new MilepostDesignPlanDetailApprovedVO();
                ProjectDto projectDto = projectBusinessService.findById(milepostDesignPlanDto.getProjectId());
                approvedDesignPlanDetail.setProjectCode(projectDto.getCode());
                approvedDesignPlanDetail.setDesignPlanDetailDtos(milepostDesignPlanDto.getDesignPlanDetailDtos());
                return approvedDesignPlanDetail;
            }
        }
        return null;
    }

    @Override
    public MilepostDesignPlanDetailApprovedVO getDetailByProjectId(Long projectId) {
        MilepostDesignPlanSubmitRecordExample submitRecordExample = new MilepostDesignPlanSubmitRecordExample();
        submitRecordExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectSubmitEqualTo(Boolean.TRUE).andProjectIdEqualTo(projectId);
        List<MilepostDesignPlanSubmitRecord> submitRecordList =
                milepostDesignPlanSubmitRecordMapper.selectByExampleWithBLOBs(submitRecordExample);
        if (ListUtils.isNotEmpty(submitRecordList)) {
            MilepostDesignPlanDto milepostDesignPlanDto = this.getDetailBySubmitId(submitRecordList.get(0).getId());
            if (!ObjectUtils.isEmpty(milepostDesignPlanDto)) {
                if (milepostDesignPlanDto.getProjectId() != null && milepostDesignPlanDto.getMilepostId() != null) {
                    MilepostDesignPlanDetailApprovedVO approvedDesignPlanDetail =
                            new MilepostDesignPlanDetailApprovedVO();
                    ProjectDto projectDto = projectBusinessService.findById(milepostDesignPlanDto.getProjectId());
                    approvedDesignPlanDetail.setProjectCode(projectDto.getCode());
                    approvedDesignPlanDetail.setDesignPlanDetailDtos(milepostDesignPlanDto.getDesignPlanDetailDtos());
                    return approvedDesignPlanDetail;
                }
            }
        }
        return null;
    }

    @Override
    public MilepostDesignPlanDetailApprovedVO getMaterialCostForPurchase(Long submitId) {
        MilepostDesignPlanDto milepostDesignPlanDto =
                milepostDesignPlanPurchaseRecordService.getDetailByConfirmId(submitId);
        if (!ObjectUtils.isEmpty(milepostDesignPlanDto)) {
            if (milepostDesignPlanDto.getProjectId() != null && milepostDesignPlanDto.getMilepostId() != null) {
                MilepostDesignPlanDetailApprovedVO approvedDesignPlanDetail = new MilepostDesignPlanDetailApprovedVO();
                ProjectDto projectDto = projectBusinessService.findById(milepostDesignPlanDto.getProjectId());
                approvedDesignPlanDetail.setProjectCode(projectDto.getCode());
                approvedDesignPlanDetail.setDesignPlanDetailDtos(milepostDesignPlanPurchaseRecordService.exportMaterialCostForPurchase(submitId));
                return approvedDesignPlanDetail;
            }
        }
        return null;
    }

    @Override
    public MilepostDesignPlanDto getDetailByConfirmId(Long confirmId) {
        Asserts.notEmpty(confirmId, ErrorCode.CTC_MILEPOST_DESIGN_PLAN_CONFIRM_ID_NULL);
        MilepostDesignPlanDto planDto = new MilepostDesignPlanDto();

        //获取确认模组ids
        MilepostDesignPlanConfirmRecordRelationDto relationQuery = new MilepostDesignPlanConfirmRecordRelationDto();
        relationQuery.setConfirmRecordId(confirmId);
        List<MilepostDesignPlanConfirmRecordRelationDto> confirmRecordRelationDtos =
                milepostDesignPlanConfirmRecordRelationService.selectListWithDesignPlan(relationQuery);
        List<Long> confirmDesignPlanDetailIds = ListUtil.map(confirmRecordRelationDtos, "designPlanDetailId");

        //确认记录
        MilepostDesignPlanConfirmRecordDto designPlanConfirmRecordQuery = new MilepostDesignPlanConfirmRecordDto();
        designPlanConfirmRecordQuery.setId(confirmId);
        planDto.setConfirmRecordDtos(milepostDesignPlanConfirmRecordService.selectList(designPlanConfirmRecordQuery));

        //设计BOM
        if (ListUtil.isPresent(planDto.getConfirmRecordDtos())) {
            MilepostDesignPlanConfirmRecordDto confirmRecordDto = planDto.getConfirmRecordDtos().get(0);
            planDto.setProjectId(confirmRecordDto.getProjectId());
            planDto.setMilepostId(confirmRecordDto.getMilepostId());

            //设计信息
            List<Long> theFirstLevelDetailIds =
                    ListUtil.map(milepostDesignPlanDetailService.getTheFirstLevelDetailByMilepostId(confirmRecordDto.getMilepostId()), "id");
            List<MilepostDesignPlanDetailDto> firstDesignPlanDetailDtos = new ArrayList<>();
            for (Long theFirstLevelDetailId : theFirstLevelDetailIds) {
                //第一层物料
                MilepostDesignPlanDetailDto firstDesignPlanDetailDto =
                        milepostDesignPlanDetailService.getDesignPlanDetailForBusiness(null, theFirstLevelDetailId,
                                null);

                //遍历第二层物料id
                if (ListUtil.isPresent(firstDesignPlanDetailDto.getSonDtos())) {
                    List<MilepostDesignPlanDetailDto> secondDesignPlanDetailDtos = new ArrayList<>();
                    for (MilepostDesignPlanDetailDto secondaryDetailDto : firstDesignPlanDetailDto.getSonDtos()) {
                        if (confirmDesignPlanDetailIds.contains(secondaryDetailDto.getId())) {//若确认模组id,则不保留
                            secondDesignPlanDetailDtos.add(secondaryDetailDto);
                        }
                    }
                    firstDesignPlanDetailDto.setSonDtos(secondDesignPlanDetailDtos);
                }
                firstDesignPlanDetailDtos.add(firstDesignPlanDetailDto);
            }
            planDto.setDesignPlanDetailDtos(firstDesignPlanDetailDtos);
        }

        //交付信息
        List<MilepostDesignPlanDetailDto> deliveryInformationDtos = new ArrayList<>();
        for (Long confirmDesignPlanDetailId : confirmDesignPlanDetailIds) {
            MilepostDesignPlanDetailDto deliveryInformationDto =
                    milepostDesignPlanDetailService.getById(confirmDesignPlanDetailId);
            deliveryInformationDtos.add(deliveryInformationDto); //设计小计
        }

        List<MilepostDesignPlanDetailDto> topParentMilepostDesignPlanDetailDtos =
                filterTopParent(deliveryInformationDtos);

        topParentMilepostDesignPlanDetailDtos.removeAll(Collections.singleton(null));

        planDto.setDeliveryInformationDtos(buildMilepostDesignPlanDetailTree(deliveryInformationDtos,
                topParentMilepostDesignPlanDetailDtos));

        //附件记录
        //TODO:2019-08-23:应产品紧急需求:确认记录详情页面需要带出附件(bug编码:BUG2019082332723)
        //TODO:附件包含:
        //TODO:1、确认记录对应的所有模组在所有提交记录和变更记录对应的附件
        //TODO:2、所有提交记录和变更记录的上传路径id(模组id)upload_path_id->空的附件
        List<CtcAttachmentDto> attachments = new ArrayList<>();
        Long milepostId = null;
        if (ListUtil.isPresent(planDto.getConfirmRecordDtos()) && planDto.getConfirmRecordDtos().get(0).getMilepostId() != null) {
            milepostId = planDto.getConfirmRecordDtos().get(0).getMilepostId();
        }

        List<Long> submitRecordIds = new ArrayList<>();
//        submitRecordIds.addAll(milepostDesignPlanSubmitRecordService.idsByUploadPathIds(confirmDesignPlanDetailIds));
        submitRecordIds.addAll(milepostDesignPlanSubmitRecordService.idsForUploadPathIdIsNullByMilepostId(milepostId));

        List<Long> changeRecordIds = new ArrayList<>();
        changeRecordIds.addAll(milepostDesignPlanChangeRecordService.idsByUploadPathIds(confirmDesignPlanDetailIds));
        changeRecordIds.addAll(milepostDesignPlanChangeRecordService.idsForUploadPathIdIsNullByMilepostId(milepostId));

        attachments.addAll(milepostDesignPlanSubmitRecordService.allAttachFromSubmitRecordIds(submitRecordIds));
        attachments.addAll(milepostDesignPlanChangeRecordService.allAttachFromChangeRecordIds(changeRecordIds));
        //过滤掉未审批通过的附件
        List<CtcAttachmentDto> filterAttachments =
                attachments.stream().filter(attachment -> Objects.equals(attachment.getStatus(),
                        AttachmentStatus.PASSED.code())).collect(Collectors.toList());
        planDto.setAttachmentDtos(filterAttachments);

        return planDto;
    }

    public List<MilepostDesignPlanDetailDto> filterTopParent(List<MilepostDesignPlanDetailDto> dtos) {
        List<MilepostDesignPlanDetailDto> topParentDto = new ArrayList<>();
        for (MilepostDesignPlanDetailDto dto : dtos) {
            topParentDto.add(findTopParent(dtos, dto));
        }
        return topParentDto.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private MilepostDesignPlanDetailDto findTopParent(List<MilepostDesignPlanDetailDto> dtos, MilepostDesignPlanDetailDto checkDto) {
        if (dtos.stream().anyMatch(dto -> dto.getId().equals(checkDto.getParentId()))) {
            return null;
        }
        return checkDto;
    }

    public List<MilepostDesignPlanDetailDto> buildMilepostDesignPlanDetailTree(List<MilepostDesignPlanDetailDto> deliveryInformationDtos,
                                                                               List<MilepostDesignPlanDetailDto> topParentDtos) {
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos = new ArrayList<>();
        for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : deliveryInformationDtos) {
            if (topParentDtos.stream().anyMatch(dto -> dto.getId().equals(milepostDesignPlanDetailDto.getId()))) {
                milepostDesignPlanDetailDtos.add(findChild(milepostDesignPlanDetailDto, deliveryInformationDtos));
            }
        }
        return milepostDesignPlanDetailDtos;
    }

    private MilepostDesignPlanDetailDto findChild(MilepostDesignPlanDetailDto milepostDesignPlanDetailDto,
                                                  List<MilepostDesignPlanDetailDto> deliveryInformationDtos) {
        for (MilepostDesignPlanDetailDto dto : deliveryInformationDtos) {
            if (Objects.equals(dto.getParentId(), milepostDesignPlanDetailDto.getId())) {
                if (CollectionUtils.isEmpty(milepostDesignPlanDetailDto.getSonDtos())) {
                    milepostDesignPlanDetailDto.setSonDtos(new ArrayList<>());
                }
                milepostDesignPlanDetailDto.getSonDtos().add(findChild(dto, deliveryInformationDtos));
            }
        }
        return milepostDesignPlanDetailDto;
    }

    @Override
    public ResponseMap getMilepostDesignPlanConfirmApp(Long id) {
        MilepostDesignPlanDto milepostDesignPlanDto = this.getDetailByConfirmId(id);
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> list = new ArrayList<>();
        List<AduitAtta> aduitAttaList = new ArrayList<>();
        if (milepostDesignPlanDto == null) {
            responseMap.setStatus("fail");
            responseMap.setMsg("数据缺失");
        } else {
            ProjectDto projectDto = projectBusinessService.findById(milepostDesignPlanDto.getProjectId());
            if (!ObjectUtils.isEmpty(projectDto)) {
                headMap.put("name", projectDto.getName() == null ? " " : projectDto.getName());
                headMap.put("code", projectDto.getCode() == null ? " " : projectDto.getCode());
                headMap.put("typeName", projectDto.getTypeName() == null ? " " : projectDto.getTypeName());
                headMap.put("managerName", projectDto.getManagerName() == null ? " " : projectDto.getManagerName());
                headMap.put("ouName", projectDto.getOuName());//业务实体
                if (projectDto.getProfit() != null) {
                    headMap.put("currency", projectDto.getProfit().getCurrency() == null ? " " :
                            projectDto.getProfit().getCurrency());//币种
                }
                List<ProjectBudgetMaterialDto> materials = projectDto.getMaterials();
                if (CollectionUtils.isNotEmpty(materials)) {
                    BigDecimal priceTotal = BigDecimal.ZERO;//所有模组的总预算
                    for (ProjectBudgetMaterialDto material : materials) {
                        priceTotal = priceTotal.add(material.getPriceTotal() == null ? BigDecimal.ZERO :
                                material.getPriceTotal());
                    }
                    headMap.put("materialTotalBudget", priceTotal.stripTrailingZeros().toPlainString());//物料总预算
                }
            }
            BigDecimal totalCost = BigDecimal.ZERO;
            Set<Long> ids = new HashSet<>();
            //本次确认的模组设计
            List<MilepostDesignPlanDetailDto> deliveryInformationDtos =
                    milepostDesignPlanDto.getDeliveryInformationDtos();
            if (CollectionUtils.isNotEmpty(deliveryInformationDtos)) {
                for (MilepostDesignPlanDetailDto deliveryInformationDto : deliveryInformationDtos) {
                    BigDecimal cost = BigDecimal.ZERO; //本次单行模组的设计成本合计
                    List<MilepostDesignPlanDetailDto> sonDtoss = deliveryInformationDto.getSonDtos();
                    if (CollectionUtils.isNotEmpty(sonDtoss)) {
                        for (MilepostDesignPlanDetailDto dtoss : sonDtoss) {
                            BigDecimal totalNum = dtoss.getTotalNum() == null ? BigDecimal.ZERO : dtoss.getTotalNum();
                            BigDecimal designCost = dtoss.getDesignCost() == null ? BigDecimal.ZERO :
                                    dtoss.getDesignCost();
                            BigDecimal money = totalNum.multiply(designCost);
                            cost = cost.add(money);
                        }
                    } else {
                        cost = (cost.add(deliveryInformationDto.getDesignCost() == null ? BigDecimal.ZERO :
                                deliveryInformationDto.getDesignCost())).multiply(deliveryInformationDto.getNumber());
                    }
                    totalCost = totalCost.add(cost);

                    ids.add(deliveryInformationDto.getId());
                    Map<String, String> detailMap = new HashMap<>();
                    detailMap.put("materielDescr", deliveryInformationDto.getMaterielDescr() == null ? " " :
                            deliveryInformationDto.getMaterielDescr());
                    detailMap.put("code", deliveryInformationDto.getErpCode() == null ? " " :
                            deliveryInformationDto.getErpCode());
                    detailMap.put("unit", deliveryInformationDto.getUnit() == null ? " " :
                            deliveryInformationDto.getUnit());
                    detailMap.put("number", deliveryInformationDto.getNumber() == null ? " " :
                            deliveryInformationDto.getNumber().stripTrailingZeros().toPlainString());
                    String budgetUnitPrice = deliveryInformationDto.getBudgetUnitPrice() == null ? "0" :
                            deliveryInformationDto.getBudgetUnitPrice().stripTrailingZeros().toPlainString();
                    detailMap.put("budgetUnitPrice", budgetUnitPrice);
                    if (ObjectUtils.isEmpty(deliveryInformationDto.getSonDtos())) {
                        String designCost = deliveryInformationDto.getDesignCost() == null ? "0" :
                                deliveryInformationDto.getDesignCost().stripTrailingZeros().toPlainString();
                        detailMap.put("designCost", designCost);
                    } else {
                        List<MilepostDesignPlanDetailDto> sonsDtos = deliveryInformationDto.getSonDtos();
                        BigDecimal moneydesign = BigDecimal.ZERO;
                        for (MilepostDesignPlanDetailDto sonsDto : sonsDtos) {
                            BigDecimal totalNum = sonsDto.getTotalNum() == null ? BigDecimal.ZERO :
                                    sonsDto.getTotalNum();
                            BigDecimal designCost = sonsDto.getDesignCost() == null ? BigDecimal.ZERO :
                                    sonsDto.getDesignCost();
                            moneydesign = moneydesign.add(totalNum.multiply(designCost));
                        }
                        detailMap.put("designCost", moneydesign.stripTrailingZeros().toPlainString());
                    }
                    list.add(detailMap);
                }
            }
            headMap.put("cost", totalCost.stripTrailingZeros().toPlainString()); //本次做进度确认模组的设计成本合计(本次设计成本)
            //已经确认的模组设计详情
            MilepostDesignPlanDetailDto query = new MilepostDesignPlanDetailDto();
            query.setProjectId(milepostDesignPlanDto.getProjectId());
            query.setMilepostId(milepostDesignPlanDto.getMilepostId());
            query.setStatus(15);
            query.setDeletedFlag(Boolean.FALSE);
            List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtosed =
                    milepostDesignPlanDetailService.selectList(query);
            /**
             * 取 已确认设计成本、设计成本合计。
             */
            BigDecimal allTotalCost = BigDecimal.ZERO;//设计成本合计=本次设计成本+已确认设计成本
            if (CollectionUtils.isNotEmpty(milepostDesignPlanDetailDtosed)) {
                BigDecimal cost1 = BigDecimal.ZERO;
                for (MilepostDesignPlanDetailDto dto : milepostDesignPlanDetailDtosed) {
                    BigDecimal num1 = dto.getNumber();
                    BigDecimal money1 = dto.getDesignCost();
                    //查看该模组是否有子模组
                    MilepostDesignPlanDetailExample milepostDesignPlanDetailExample =
                            new MilepostDesignPlanDetailExample();
                    milepostDesignPlanDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andParentIdEqualTo(dto.getId()).andMilepostIdEqualTo(dto.getMilepostId());
                    List<MilepostDesignPlanDetail> milepostDesignPlanDetails =
                            milepostDesignPlanDetailMapper.selectByExample(milepostDesignPlanDetailExample);
                    if (CollectionUtils.isNotEmpty(milepostDesignPlanDetails)) {
                        for (MilepostDesignPlanDetail sonDetail : milepostDesignPlanDetails) {
                            BigDecimal cost = BigDecimal.ZERO;
                            BigDecimal number1 = sonDetail.getNumber() == null ? BigDecimal.ZERO :
                                    sonDetail.getNumber();
                            BigDecimal designCost1 = sonDetail.getDesignCost() == null ? BigDecimal.ZERO :
                                    sonDetail.getDesignCost();
                            cost = num1.multiply(number1).multiply(designCost1);
                            cost1 = cost1.add(cost);
                        }
                    } else {
                        //没有子模组表示已确认的只是子模组
                        cost1 = cost1.add(num1.multiply(money1));
                    }

                }
                headMap.put("confirmCost", cost1.stripTrailingZeros().toPlainString());//已确认设计成本
                allTotalCost = totalCost.add(cost1);
                headMap.put("totalCost", allTotalCost.stripTrailingZeros().toPlainString()); //设计成本合计=本次设计成本+已确认设计成本
            } else { //没有已确认的数据
                headMap.put("confirmCost", "0");
                headMap.put("totalCost", totalCost.stripTrailingZeros().toPlainString());
            }
            /*if (CollectionUtils.isNotEmpty(designPlanDetailDtos)){
                BigDecimal allTotalPrice = BigDecimal.ZERO; //所有已确认模组设计成本合计
                for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                    List<MilepostDesignPlanDetailDto> sonDtos = designPlanDetailDto.getSonDtos();
                    if (CollectionUtils.isNotEmpty(sonDtos)){
                        BigDecimal totalPrice = BigDecimal.ZERO; //该模组下的已确认的子模组设计成本之和
                        for (MilepostDesignPlanDetailDto sonDto : sonDtos) {
                            if (ids.contains(sonDto.getId())){
                                continue;
                            }else {
                                BigDecimal price = BigDecimal.ZERO;//已确认的单行模组的设计成本合计
                                price = (price.add(sonDto.getDesignCost()==null?BigDecimal.ZERO:sonDto.getDesignCost()))
                                        .multiply(sonDto.getNumber());
                                totalPrice = totalPrice.add(price);
                            }
                        }
                        allTotalPrice = allTotalPrice.add(totalPrice);
                    }
                }
                String allTotalPriceStr = allTotalPrice.toString();
                headMap.put("confirmCost",allTotalPriceStr==null?"0.00":allTotalPriceStr);//已确认设计成本
                BigDecimal allTotalCost = BigDecimal.ZERO;
                allTotalCost = allTotalCost.add(totalCost).add(allTotalPrice);
                headMap.put("totalCost",allTotalCost==null?"0.00":allTotalCost.stripTrailingZeros().toPlainString());
                //设计成本合计=本次设计成本+已确认设计成本
            }*/

            //附件信息
            List<CtcAttachmentDto> attachmentDtos = milepostDesignPlanDto.getAttachmentDtos();
            if (CollectionUtils.isNotEmpty(attachmentDtos)) {
                for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                    if (attachmentDto.getFileName() == null) {
                        AduitAtta aduitAtta = new AduitAtta();
                        aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" :
                                attachmentDto.getAttachId().toString());
                        aduitAtta.setFileName(attachmentDto.getAttachName() == null ? "" :
                                attachmentDto.getAttachName());
//                    aduitAtta.setFileName(attachmentDto.getAttachName()==null?attachmentDto.getFileName()
//                    :attachmentDto.getAttachName());
                        aduitAtta.setFileSize(attachmentDto.getFileSize() == null ? "" :
                                attachmentDto.getFileSize().toString());
                        aduitAttaList.add(aduitAtta);
                    }
                }
            }

            // 查询合同回款信息
            Long contractId = projectDto.getContractId();
            if (contractId != null) {
                BigDecimal totalAmount = BigDecimal.ZERO; //计划回款金额
                BigDecimal receivableActualAmount = BigDecimal.ZERO; //已回款
                BigDecimal receivableRemainingAmount = BigDecimal.ZERO; // 剩余回款
                BigDecimal receiptRate = BigDecimal.ZERO; // 回款率
                // 回款计划
                List<ReceiptPlanDetail> receiptPlanDetails = receiptPlanService.listByContractId(contractId);
                if (ListUtils.isNotEmpty(receiptPlanDetails)) {
                    for (ReceiptPlanDetail receiptPlanDetail : receiptPlanDetails) {
                        BigDecimal receiptPlanDetailAmount = receiptPlanDetail.getAmount() != null ?
                                receiptPlanDetail.getAmount() : BigDecimal.ZERO; // 计划回款金额
                        BigDecimal receiptPlanDetailActualAmount = receiptPlanDetail.getActualAmount() != null ?
                                receiptPlanDetail.getActualAmount() : BigDecimal.ZERO; // 实际回款金额
                        totalAmount = totalAmount.add(receiptPlanDetailAmount);
                        receivableActualAmount = receivableActualAmount.add(receiptPlanDetailActualAmount);
                    }

                    receivableRemainingAmount = totalAmount.subtract(receivableActualAmount);
                    if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        receiptRate =
                                receivableActualAmount.divide(totalAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
                    }
                    headMap.put("receivableActualAmount", "" + receivableActualAmount.setScale(2));
                    headMap.put("receivableRemainingAmount", "" + receivableRemainingAmount.setScale(2));
                    headMap.put("receiptRate", receiptRate.setScale(2) + "%");
                }
            }

            responseMap.setStatus("success");
            responseMap.setMsg("success");
            responseMap.setHeadMap(headMap);
            responseMap.setList1(list);
            responseMap.setFileList(aduitAttaList);
        }

        return responseMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MilepostDesignPlanSubmitRecordDto saveSubmitRecordWithDetail(MilepostDesignPlanDto dto, Long userBy) {
        Guard.notNull(dto.getProjectId(), "项目id不能为空");

        projectBusinessService.checkProjectIsApprovaled(dto.getProjectId());

        MilepostDesignPlanSubmitRecordDto submitRecordDto = dto.getSubmitRecordDto();
        List<CtcAttachmentDto> attachmentDtos = dto.getAttachmentDtos();
        MilepostDesignPlanSubmitRecordDto submitResultDto = null;
        if (submitRecordDto != null) {
            submitRecordDto.setStatus(CheckStatus.DRAFT.code());
            submitRecordDto.setProjectId(dto.getProjectId());
            submitRecordDto.setMilepostId(dto.getMilepostId());
            Asserts.notEmpty(dto.getMilepostId(), ErrorCode.CTC_MILEPOST_ID_NULL);
            submitRecordDto.setPublisherBy(userBy);
            submitRecordDto.setProjectSubmit(Boolean.FALSE);//是否立项时上传
            if (submitRecordDto.getId() != null) {
                milepostDesignPlanDetailExtMapper.deleteMilepostDesignPlanDetailsBySubmitId(submitRecordDto.getId());
            }
            submitResultDto = milepostDesignPlanSubmitRecordService.saveSubmitRecordWithRecursiveDetail(submitRecordDto, userBy);

            //修改详设中间表数据
            if (null != dto.getMarkId()) {
                this.updateMilepostDesignPlanMiddle(dto);
            }
        }
        if (ListUtil.isPresent(attachmentDtos)) {
            for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                if (attachmentDto.getId() == null) {
                    attachmentDto.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code());
                    if (submitResultDto != null) {
                        attachmentDto.setModuleId(submitResultDto.getId());
                    }
                    attachmentDto.setStatus(AttachmentStatus.CHECKING.code());
                }
                ctcAttachmentService.save(attachmentDto, userBy);
            }
        }
        return submitResultDto;
    }

    private void updateMilepostDesignPlanMiddle(MilepostDesignPlanDto dto) {
        basedataExtService.updateMilepostDesignPlanMiddle(dto.getMarkId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MilepostDesignPlanSubmitRecordDto saveSubmitRecordWithDetailForProject(ProjectDto projectDto, Long userBy) {
        MilepostDesignPlanDto dto = projectDto.getMilepostDesignPlanSubmitRecord();
        Asserts.notEmpty(dto.getProjectId(), ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(dto.getMilepostId(), ErrorCode.CTC_MILEPOST_ID_NULL);

        MilepostDesignPlanSubmitRecordDto submitRecordDto = dto.getSubmitRecordDto();
//        List<CtcAttachmentDto> attachmentDtos = dto.getAttachmentDtos();
        MilepostDesignPlanSubmitRecordDto submitResultDto = null;
        if (submitRecordDto != null) {
            submitRecordDto.setProjectId(dto.getProjectId());
            submitRecordDto.setMilepostId(dto.getMilepostId());
            submitRecordDto.setPublisherBy(userBy);
            submitRecordDto.setPurchase(Boolean.TRUE);//默认采购
            submitRecordDto.setProjectSubmit(Boolean.TRUE);//是否立项时上传

            String uploadPathIds = submitRecordDto.getUploadPathIds();
            Set<Long> uploadPathIdSet = new HashSet<>();
            if (StringUtils.isNotEmpty(uploadPathIds)) {
                try {
                    uploadPathIdSet = Arrays.stream(uploadPathIds.split(",")).filter(StringUtils::isNotEmpty).map(Long::valueOf).collect(Collectors.toSet());
                } catch (Exception e) {
                    throw new BizException(Code.ERROR, "详设导入参数异常");
                }
            }
            submitRecordDto.setUploadPathIdSet(uploadPathIdSet);
            submitResultDto = milepostDesignPlanSubmitRecordService.saveSubmitRecordWithRecursiveDetail(submitRecordDto, userBy);

            //修改详设中间表数据
            if (null != dto.getMarkId()) {
                this.updateMilepostDesignPlanMiddle(dto);
            }
        }
//        if (ListUtil.isPresent(attachmentDtos)) {
//            for (CtcAttachmentDto attachmentDto : attachmentDtos) {
//                if (attachmentDto.getId() == null) {
//                    attachmentDto.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code());
//                    if (submitResultDto != null) {
//                        attachmentDto.setModuleId(submitResultDto.getId());
//                    }
//                    attachmentDto.setStatus(AttachmentStatus.CHECKING.code());
//                }
//                ctcAttachmentService.save(attachmentDto, userBy);
//            }
//        }
        //先删除旧的确认记录
        MilepostDesignPlanConfirmRecordExample confirmRecordExample = new MilepostDesignPlanConfirmRecordExample();
        confirmRecordExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(dto.getProjectId());
        List<MilepostDesignPlanConfirmRecord> confirmRecordList = milepostDesignPlanConfirmRecordMapper.selectByExample(confirmRecordExample);
        if (ListUtils.isNotEmpty(confirmRecordList)) {
            for (MilepostDesignPlanConfirmRecord confirmRecord : confirmRecordList) {
                confirmRecord.setDeletedFlag(Boolean.TRUE);
                milepostDesignPlanConfirmRecordMapper.updateByPrimaryKey(confirmRecord);
            }
        }
        //如果上传方案时勾选了详细设计是否已完成
        if (projectDto.getConfirmRecordFlag() != null && projectDto.getConfirmRecordFlag()) {
            //服务业务采购需求下达--模组确认
            MilepostDesignPlanConfirmRecordDto confirmRecordDto =
                    milepostDesignPlanConfirmRecordService.saveMilepostDesignPlanConfirmRecord(projectDto.getMilepostDesignPlanSubmitRecord());
            if (confirmRecordDto != null) {
                confirmRecordDto.setConfirmRecordFlag(Boolean.TRUE);
                milepostDesignPlanConfirmRecordService.saveConfirmRecordWithRecursiveDetail(confirmRecordDto, SystemContext.getUserId());
            }
        }
        return submitResultDto;
    }

    @Override
    public void saveSubmitRecordWithDetail(Long asyncRequestResultId, MilepostDesignPlanDto dto, Long userBy) {
        logger.info("saveSubmitRecordWithDetail的asyncRequestResultId：{}，dto：{}，userBy：{}", asyncRequestResultId, JsonUtils.toString(dto), userBy);
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult.setId(asyncRequestResultId);

        Long id;
        try {
            Project project = projectService.selectByPrimaryKey(dto.getProjectId());
            if (Boolean.TRUE.equals(project.getWbsEnabled())) {
                ProjectWbsReceiptsDto projectWbsReceiptsDto = this.saveSubmitHistoryWithDetail(dto, userBy);
                id = projectWbsReceiptsDto.getId();
            } else {
                MilepostDesignPlanSubmitRecordDto submitRecordDto = this.saveSubmitRecordWithDetail(dto, userBy);
                id = submitRecordDto.getId();
            }
            asyncRequestResult.setResponseCode(ErrorCode.SUCCESS.getCode());
            asyncRequestResult.setResponseMsg(ErrorCode.SUCCESS.getMsg());
            asyncRequestResult.setResponseData(String.valueOf(id));
            asyncRequestResultMapper.updateByPrimaryKeySelective(asyncRequestResult);
        } catch (Exception e) {
            logger.error("saveSubmitRecordWithDetail异常: ", e);
            asyncRequestResult.setResponseCode(ErrorCode.ERROR.getCode());
            asyncRequestResult.setResponseMsg(e.getMessage());
            asyncRequestResultMapper.updateByPrimaryKeySelective(asyncRequestResult);
        }
    }

    @Override
    public void saveSubmitRecordWithDetailForProject(Long asyncRequestResultId, ProjectDto dto, Long userBy) {
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult.setId(asyncRequestResultId);
        try {
            MilepostDesignPlanSubmitRecordDto recordDto = this.saveSubmitRecordWithDetailForProject(dto, userBy);

            asyncRequestResult.setResponseCode(ErrorCode.SUCCESS.getCode());
            asyncRequestResult.setResponseMsg(ErrorCode.SUCCESS.getMsg());
            asyncRequestResult.setResponseData(String.valueOf(recordDto.getId()));
            asyncRequestResultMapper.updateByPrimaryKeySelective(asyncRequestResult);
        } catch (Exception e) {
            logger.error("系统未知异常: ", e);
            asyncRequestResult.setResponseCode(ErrorCode.ERROR.getCode());
            asyncRequestResult.setResponseMsg(e.getMessage());
            asyncRequestResultMapper.updateByPrimaryKeySelective(asyncRequestResult);
        }
    }

    @Override
    public List<String> savePlanWithDetailAsyncBefore(MilepostDesignPlanDto dto) {
        logger.info("savePlanWithDetailAsyncBefore的dto：{}", JsonUtils.toString(dto));

        MilepostDesignPlanSubmitRecordDto submitRecordDto = dto.getSubmitRecordDto();
        if (null == submitRecordDto) {
            return new ArrayList<>();
        }

        Long uploadPathId = submitRecordDto.getUploadPathId();
        MilepostDesignPlanDetailDto detailDto = milepostDesignPlanDetailService.getById(uploadPathId);
        if (Objects.isNull(detailDto) || Boolean.TRUE.equals(detailDto.getDeletedFlag())) {
            return new ArrayList<>();
        }

        Set<String> pamCodeSet = new HashSet<>();
        BigDecimal number = detailDto.getNumber();
        if (Objects.isNull(number) || BigDecimal.ZERO.compareTo(number) == 0) {
            pamCodeSet.add(detailDto.getPamCode());
        }
        recursivePamCode(detailDto.getParentId(), pamCodeSet);

        return new ArrayList<>(pamCodeSet);
    }

    private void recursivePamCode(Long parentId, Set<String> pamCodeSet) {
        MilepostDesignPlanDetailDto grandDetailDto = milepostDesignPlanDetailService.getById(parentId);
        if (Objects.isNull(grandDetailDto) || Boolean.TRUE.equals(grandDetailDto.getDeletedFlag())) {
            return;
        }

        BigDecimal number = grandDetailDto.getNumber();
        if (Objects.isNull(number) || BigDecimal.ZERO.compareTo(number) == 0) {
            pamCodeSet.add(grandDetailDto.getPamCode());
        }
        recursivePamCode(grandDetailDto.getParentId(), pamCodeSet);
    }

    @Override
    public AsyncRequestResult saveSubmitRecordWithDetailAsync(MilepostDesignPlanDto dto, Long userBy) {
        logger.info("saveSubmitRecordWithDetailAsync的dto：{}，userBy：{}", JsonUtils.toString(dto), userBy);

        //将前端传入的平铺的新增详设组装层级结构
        assembleFlatDesignPlanToHierarchy(dto);
        logger.info("assembleFlatDesignPlanToHierarchy 处理后的数据:{}",JsonUtils.toString(dto));
        this.checkMilepostDesignPlanDetail(dto);

        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult = asyncRequestResultService.add(asyncRequestResult);

        if (null != dto.getReceiptsDto() && null == dto.getReceiptsDto().getUnitId()) {
            dto.getReceiptsDto().setUnitId(SystemContext.getUnitId());
        }
        logger.info("saveSubmitRecordWithDetailAsync校验后的dto：{}，userBy：{}", JsonUtils.toString(dto), userBy);
        applicationEventPublisher.publishEvent(new DesignPlanDetailSubmitEvent(this, asyncRequestResult.getId(), dto, userBy));
        return asyncRequestResult;
    }

    /**
     * 组装平铺的详设数据为层级结构
     *
     * @param dto 包含平铺详设数据的DTO
     */
    private void assembleFlatDesignPlanToHierarchy(MilepostDesignPlanDto dto) {
        logger.info("开始组装平铺的详设数据为层级结构，dto：{}", JsonUtils.toString(dto.getReceiptsDto()));

        if (Objects.nonNull(dto.getReceiptsDto()) && Objects.nonNull(dto.getReceiptsDto().getFlatMilepostDesignPlanDtos())) {
            List<MilepostDesignPlanDetailDto> flatMilepostDesignPlanDtos = dto.getReceiptsDto().getFlatMilepostDesignPlanDtos();
            logger.info("平铺的详设数据数量：{}", flatMilepostDesignPlanDtos.size());

            // 处理基于uid的多层级新增关系，构建独立树并从平铺列表中移除
            List<MilepostDesignPlanDetailDto> uidBasedTrees = processUidBasedHierarchyAndRemoveFromFlatForDesignPlan(flatMilepostDesignPlanDtos);

            //按照parentId进行分组
            Map<Long, List<MilepostDesignPlanDetailDto>> flatMilepostDesignPlanDtoMap = flatMilepostDesignPlanDtos.stream().filter(s->Objects.nonNull(s.getParentId()))
                    .collect(Collectors.groupingBy(MilepostDesignPlanDetailDto::getParentId));

            //获取所有parentId
            Set<Long> parentIds = flatMilepostDesignPlanDtoMap.keySet();
            logger.info("父节点ID集合：{}", parentIds);

            MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
            milepostDesignPlanDetailExample.createCriteria().andIdIn(new ArrayList<>(parentIds)).andDeletedFlagEqualTo(Boolean.FALSE);
            List<MilepostDesignPlanDetail> parentDetailList = milepostDesignPlanDetailMapper.selectByExample(milepostDesignPlanDetailExample);
            logger.info("查询到的父节点数量：{}", parentDetailList.size());

            ArrayList<MilepostDesignPlanDetailDto> queryDetailDtoList = parentDetailList.stream().map(p -> {
                MilepostDesignPlanDetailDto detailDto = new MilepostDesignPlanDetailDto();
                detailDto.setId(p.getId());
                detailDto.setParentId(p.getParentId());
                return detailDto;
            }).collect(Collectors.toCollection(ArrayList::new));

            List<MilepostDesignPlanDetailDto> packagePlanDetailList = packagePlanDetail(queryDetailDtoList);

            if (ListUtils.isNotEmpty(packagePlanDetailList)) {
                MilepostDesignPlanDetailDto packagePlanDetail = packagePlanDetailList.get(0);
                logger.info("组装后的根节点ID：{}", packagePlanDetail.getId());

                // 为已存在的节点设置designPlanDetailId，确保它们不会被当作新节点处理
                setDesignPlanDetailIdForExistingNodes(packagePlanDetail);

                flatMilepostDesignPlanDtoMap.forEach((parentId, flatMilepostDesignPlanDetailDtos) -> {
                    //找到需要插入的父节点
                    MilepostDesignPlanDetailDto appendSonDtoDetailDto = findEntityById(packagePlanDetail, parentId.toString());
                    if (appendSonDtoDetailDto != null) {
                        appendSonDtoDetailDto.setSonDtos(flatMilepostDesignPlanDetailDtos);
                        logger.info("为父节点ID：{}设置了{}个子节点", parentId, flatMilepostDesignPlanDetailDtos.size());
                    } else {
                        logger.warn("未找到ID为{}的父节点", parentId);
                    }
                });

                //将parentIds转换字符串
                String pathIds = parentIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                dto.getReceiptsDto().setBatchUploadPathIds(pathIds);
                logger.info("设置批量上传路径IDs：{}", pathIds);

                //组装wbsLayer路径
                String wbsPath = buildWbsLayerPath(packagePlanDetail);
                dto.getReceiptsDto().setBatchUploadPaths(wbsPath);
                logger.info("设置批量上传WBS路径：{}", wbsPath);

                // 将基于uid构建的独立树添加到根节点的子节点中（避免重复添加）
                addUidBasedTreesToPackagePlanDetailWithDuplicateCheck(packagePlanDetail, uidBasedTrees);

                //设置designPlanDetailDtos属性，按照原来逻辑处理详设提交
                dto.getReceiptsDto().setDesignPlanDetailDtos(packagePlanDetailList);
                logger.info("完成平铺详设数据的层级结构组装,dto:{}", JsonUtils.toString(dto.getReceiptsDto()));
            } else {
                // 如果没有数据库父节点，但有基于uid的独立树，直接使用这些树
                handleUidBasedTreesWhenNoPackagePlanDetail(dto, uidBasedTrees);
            }
        } else {
            logger.info("无需组装平铺详设数据，跳过处理");
        }
        // 处理submitId层级关系
        if(Objects.nonNull(dto.getReceiptsDto()) && Objects.nonNull(dto.getReceiptsDto().getFlatMilepostDesignPlanDtos())
           && Objects.nonNull(dto.getReceiptsDto().getDesignPlanDetailDtos())) {
            processSubmitIdHierarchy(dto.getReceiptsDto().getFlatMilepostDesignPlanDtos(),
                                   dto.getReceiptsDto().getDesignPlanDetailDtos());
            //清空dto.getReceiptsDto().getFlatMilepostDesignPlanDtos()
            dto.getReceiptsDto().setFlatMilepostDesignPlanDtos(null);
        }


    }

    /**
     * 处理submitId层级关系
     * 对处理后的packagePlanDetailList集合与flatMilepostDesignPlanDtos中的元素比较，
     * 如果flatMilepostDesignPlanDtos中的对象submitId，submitParentId不为空则根据submitParentId查找它的父级，
     * 并添加到父级的sonDtos下
     *
     * @param flatMilepostDesignPlanDtos 平铺的详设数据列表
     * @param packagePlanDetailList 处理后的层级结构列表
     */
    private void processSubmitIdHierarchy(List<MilepostDesignPlanDetailDto> flatMilepostDesignPlanDtos,
                                        List<MilepostDesignPlanDetailDto> packagePlanDetailList) {
        logger.info("开始处理submitId层级关系，平铺数据数量：{}，层级结构数量：{}",
                   flatMilepostDesignPlanDtos.size(), packagePlanDetailList.size());

        if (ListUtils.isEmpty(flatMilepostDesignPlanDtos) || ListUtils.isEmpty(packagePlanDetailList)) {
            logger.info("数据为空，跳过submitId层级处理");
            return;
        }

        // 筛选出有submitId和submitParentId的元素
        List<MilepostDesignPlanDetailDto> submitIdElements = flatMilepostDesignPlanDtos.stream()
            .filter(dto -> Objects.nonNull(dto.getSubmitId()) && Objects.nonNull(dto.getSubmitParentId()))
            .collect(Collectors.toList());

        logger.info("找到{}个有submitId和submitParentId的元素", submitIdElements.size());

        if (ListUtils.isEmpty(submitIdElements)) {
            logger.info("没有需要处理的submitId层级关系");
            return;
        }

        // 处理每个有submitId层级关系的元素
        for (MilepostDesignPlanDetailDto submitElement : submitIdElements) {
            Long submitParentId = submitElement.getSubmitParentId();
            logger.info("处理元素submitId：{}，submitParentId：{}",
                       submitElement.getSubmitId(), submitParentId);

            // 在packagePlanDetailList中查找父级节点
            MilepostDesignPlanDetailDto parentNode = findNodeBySubmitId(packagePlanDetailList, submitParentId);

            if (Objects.nonNull(parentNode)) {
                // 将当前元素添加到父级的sonDtos中
                addToParentSonDtos(parentNode, submitElement);
                logger.info("成功将submitId：{}的元素添加到submitParentId：{}的父级节点下",
                           submitElement.getSubmitId(), submitParentId);
            } else {
                logger.warn("未找到submitParentId：{}对应的父级节点", submitParentId);
            }
        }

        logger.info("完成submitId层级关系处理");
    }

    /**
     * 根据submitId在层级结构中查找节点
     *
     * @param packagePlanDetailList 层级结构列表
     * @param submitId 要查找的submitId
     * @return 找到的节点，未找到返回null
     */
    private MilepostDesignPlanDetailDto findNodeBySubmitId(List<MilepostDesignPlanDetailDto> packagePlanDetailList,
                                                          Long submitId) {
        if (ListUtils.isEmpty(packagePlanDetailList) || Objects.isNull(submitId)) {
            return null;
        }

        for (MilepostDesignPlanDetailDto node : packagePlanDetailList) {
            // 检查当前节点
            if (Objects.equals(submitId, node.getSubmitId())) {
                return node;
            }

            // 递归检查子节点
            MilepostDesignPlanDetailDto found = findNodeBySubmitIdRecursive(node, submitId);
            if (Objects.nonNull(found)) {
                return found;
            }
        }

        return null;
    }

    /**
     * 递归查找submitId对应的节点
     *
     * @param node 当前节点
     * @param submitId 要查找的submitId
     * @return 找到的节点，未找到返回null
     */
    private MilepostDesignPlanDetailDto findNodeBySubmitIdRecursive(MilepostDesignPlanDetailDto node, Long submitId) {
        if (Objects.isNull(node) || Objects.isNull(submitId)) {
            return null;
        }

        // 检查当前节点
        if (Objects.equals(submitId, node.getSubmitId())) {
            return node;
        }

        // 递归检查子节点
        if (Objects.nonNull(node.getSonDtos())) {
            for (MilepostDesignPlanDetailDto child : node.getSonDtos()) {
                MilepostDesignPlanDetailDto found = findNodeBySubmitIdRecursive(child, submitId);
                if (Objects.nonNull(found)) {
                    return found;
                }
            }
        }

        return null;
    }

    /**
     * 将子元素添加到父级节点的sonDtos中
     *
     * @param parentNode 父级节点
     * @param childElement 要添加的子元素
     */
    private void addToParentSonDtos(MilepostDesignPlanDetailDto parentNode, MilepostDesignPlanDetailDto childElement) {
        if (Objects.isNull(parentNode) || Objects.isNull(childElement)) {
            return;
        }

        // 初始化sonDtos列表（如果为空）
        if (Objects.isNull(parentNode.getSonDtos())) {
            parentNode.setSonDtos(new ArrayList<>());
        }

        // 检查是否已存在（避免重复添加）
        boolean alreadyExists = parentNode.getSonDtos().stream()
            .anyMatch(son -> Objects.equals(son.getSubmitId(), childElement.getSubmitId()));

        if (!alreadyExists) {
            parentNode.getSonDtos().add(childElement);
            logger.info("成功添加子元素到父级节点，父级submitId：{}，子元素submitId：{}",
                       parentNode.getSubmitId(), childElement.getSubmitId());
        } else {
            logger.info("子元素已存在，跳过添加，父级submitId：{}，子元素submitId：{}",
                       parentNode.getSubmitId(), childElement.getSubmitId());
        }
    }

    /**
     * 为已存在的节点设置designPlanDetailId，确保它们不会被当作新节点处理
     *
     * @param node 当前节点
     */
    private void setDesignPlanDetailIdForExistingNodes(MilepostDesignPlanDetailDto node) {
        if (node == null) {
            return;
        }

        // 如果节点已存在（不是新增的），则设置designPlanDetailId为其id
        if (node.getId() != null && (node.get_isNew() == null || !Boolean.TRUE.equals(node.get_isNew()))) {
            node.setDesignPlanDetailId(node.getId());
            logger.info("为已存在节点ID：{}设置designPlanDetailId", node.getId());
        }

        // 递归处理子节点
        if (node.getSonDtos() != null) {
            for (MilepostDesignPlanDetailDto child : node.getSonDtos()) {
                // 只处理非新增的子节点
                if (child.get_isNew() == null || !Boolean.TRUE.equals(child.get_isNew())) {
                    setDesignPlanDetailIdForExistingNodes(child);
                }
            }
        }
    }

    /**
     * 递归查找指定ID的详设
     *
     * @param root     根实体
     * @param targetId 目标ID
     * @return 找到的实体，未找到则返回null
     */
    public MilepostDesignPlanDetailDto findEntityById(MilepostDesignPlanDetailDto root, String targetId) {
        // 空值检查
        if (root == null || targetId == null) {
            return null;
        }

        // 检查当前节点
        if (targetId.equals(root.getId() != null ? root.getId().toString() : null)) {
            return root;
        }

        // 检查子节点
        if (root.getSonDtos() != null) {
            for (MilepostDesignPlanDetailDto child : root.getSonDtos()) {
                MilepostDesignPlanDetailDto found = findEntityById(child, targetId);
                if (found != null) {
                    return found;
                }
            }
        }

        // 未找到
        return null;
    }

    /**
     * 提取wbsLayer路径（单一方法实现）
     *
     * @param root 根节点实体
     * @return 格式化的wbsLayer路径
     */
    public static String buildWbsLayerPath(MilepostDesignPlanDetailDto root) {
        if (root == null) {
            return "";
        }

        StringBuilder path = new StringBuilder();
        MilepostDesignPlanDetailDto currentNode = root;

        // 添加根节点的wbsLayer
        if (currentNode.getWbsLayer() != null && !currentNode.getWbsLayer().isEmpty()) {
            path.append(currentNode.getWbsLayer());
        }

        // 迭代向下遍历，始终选择第一个子节点
        while (currentNode.getSonDtos() != null && !currentNode.getSonDtos().isEmpty()) {
            currentNode = currentNode.getSonDtos().get(0);

            if (currentNode.getWbsLayer() != null && !currentNode.getWbsLayer().isEmpty()) {
                path.append("/").append(currentNode.getWbsLayer());
            }
        }

        return path.toString();
    }

    @Override
    public AsyncRequestResult saveSubmitRecordWithDetailForProjectAsync(ProjectDto dto, Long userBy) {
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult = asyncRequestResultService.add(asyncRequestResult);
        applicationEventPublisher.publishEvent(new DesignPlanDetailSubmitForProjectEvent(this,
                asyncRequestResult.getId(), dto, userBy));
        return asyncRequestResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean projectBudgetMaterialToMilepostDesignPlanDetail(Long projectId, Long userBy) {
        logger.info("---开始将物料预算写入详细设计方案，项目id为:{}", projectId);
        Project project = projectService.selectByPrimaryKey(projectId);
        Guard.notNull(project, String.format("项目id为%s的项目不存在", projectId));

        ProjectMilepostDto projectMilepostQuery = new ProjectMilepostDto();
        projectMilepostQuery.setProjectId(projectId);
        projectMilepostQuery.setAnnexType(ProjectMilepostAnnexType.BOM.code());
        List<ProjectMilepostDto> milepostDtos = projectMilepostService.selectList(projectMilepostQuery);
        //如果没有设计BOM里程碑，预算不需要转详设] 20210420youwei
        if (ListUtils.isEmpty(milepostDtos)) {
            return false;
        }

        try {
            //1.获取项目中详细设计方案类型里程碑
            Long milepostId = null;//this.findMilepostId(projectId);
            if (ListUtils.isNotEmpty(milepostDtos)) {
                milepostId = milepostDtos.get(0).getId();
            }
            //2.获取项目物料预算
            List<ProjectBudgetMaterial> projectBudgetMaterials = this.findProjectBudgetMaterials(projectId);
            logger.info("项目预算变更的projectBudgetMaterials：{}", JsonUtils.toString(projectBudgetMaterials));
            if (CollectionUtils.isNotEmpty(projectBudgetMaterials)) {
                //3.获取所有详细设计物料Map
                Map</*projectBudgetMaterialId*/Long, MilepostDesignPlanDetailDto> planDetailDtoMap = this.findMilepostDesignPlanDetails(projectId);
                logger.info("项目预算变更的planDetailDtoMap：{}", JsonUtils.toString(planDetailDtoMap));

                //记录物料id(第一层产品id)与详细方案设计信息id
                HashMap<Long, Long> idMap = new HashMap<>();
                for (ProjectBudgetMaterial projectBudgetMaterial : projectBudgetMaterials) {
                    //已经生成了PAM码，则不需要再生成
                    projectBudgetMaterial.setCode(
                            StringUtils.isNotBlank(projectBudgetMaterial.getCode()) ? projectBudgetMaterial.getCode()
                                    : materialExtService.generateMaterialPAMCode());
                    projectBudgetMaterial.setCreateBy(userBy);

                    //判断是否已经生成详细设计
                    MilepostDesignPlanDetailDto planDetailDto = planDetailDtoMap.get(projectBudgetMaterial.getId());
                    if (Objects.nonNull(planDetailDto) && !Boolean.TRUE.equals(project.getWbsEnabled())) {
                        //如果预算删除，详细设计还未被进度确认，则删除对应的进度确认
                        if (Boolean.TRUE.equals(projectBudgetMaterial.getDeletedFlag())
                                && !Objects.equals(CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code(), planDetailDto.getStatus())) {
                            planDetailDto.setDeletedFlag(Boolean.TRUE);
                            milepostDesignPlanDetailService.save(planDetailDto, userBy);
                            //关联子节点
                            MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
                            example.createCriteria().andParentIdEqualTo(planDetailDto.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                            List<MilepostDesignPlanDetail> list = milepostDesignPlanDetailMapper.selectByExample(example);
                            if (ListUtils.isNotEmpty(list)) {
                                logger.info("*************** MilepostDesignPlanServiceImpl.projectBudgetMaterialToMilepostDesignPlanDetail:{} " +
                                        "***************", JSONObject.toJSONString(list));
                                for (MilepostDesignPlanDetail p : list) {
                                    p.setDeletedFlag(Boolean.TRUE);
                                    p.setUpdateBy(userBy);
                                    milepostDesignPlanDetailMapper.updateByPrimaryKey(p);
                                }
                            }
                        } else {
                            //如果存在,则更新
                            this.updateDesignPlanDetail(idMap, projectBudgetMaterial, planDetailDto, null, userBy);
                        }
                    } else {
                        if (!Boolean.TRUE.equals(project.getWbsEnabled())) {
                            //不存在,则插入
                            this.budgetMaterialTranslateDesignPlanDetail(idMap, projectBudgetMaterial.getId(),
                                    projectBudgetMaterial, null, projectId, milepostId, null, userBy);
                        }
                    }
                    // 项目物料预算 = 详细设计关联，从map剔除
                    planDetailDtoMap.remove(projectBudgetMaterial.getId());
                    projectBudgetMaterialService.updateByPrimaryKeySelective(projectBudgetMaterial);
                }
                logger.info("项目预算变更的最终planDetailDtoMap：{}", JsonUtils.toString(planDetailDtoMap));

                // 项目物料预算 != 详细设计，覆盖导入时要删除
                if (MapUtils.isNotEmpty(planDetailDtoMap)) {
                    // 预立项转正，如果是覆盖导入，删除项目预算不存在的历史详细设计物料
                    for (Long projectBudgetMaterialId : planDetailDtoMap.keySet()) {
                        milepostDesignPlanDetailExtMapper.logicallyDeleteByProjectBudgetMataerialId(projectBudgetMaterialId, userBy);
                    }
                }
            }

            ProjectBudgetMaterialExample projectBudgetMaterialExample = new ProjectBudgetMaterialExample();
            projectBudgetMaterialExample.createCriteria().andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE).andParentIdIsNotNull();
            final List<ProjectBudgetMaterial> projectBudgetMaterialList = projectBudgetMaterialService.selectByExample(projectBudgetMaterialExample);
            final List<ProjectBudgetMaterial> collect =
                    projectBudgetMaterialList.stream().filter(p -> p.getExt() == null || p.getExt() == Boolean.FALSE).collect(Collectors.toList());
            //模组状态都是“集成外包”的模组，需要将对应里程碑状态变更为：8
            if (ListUtils.isEmpty(collect)) {
                logger.info("projectBudgetMaterialToMilepostDesignPlanDetail，更新前的里程碑信息:{}", JsonUtils.toString(milepostDtos.get(0)));
                ProjectMilepost projectMilepost = new ProjectMilepost();
                projectMilepost.setId(milepostId);
                projectMilepost.setStatus(MilepostStatus.CAN_PASS.getCode());
                projectMilepostExtMapper.updateStatusExt(projectMilepost);
            }

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            logger.error("项目id:" + projectId + "物料预算转换详细方案异常", e);
            throw new BizException(Code.ERROR, "物料预算转换详细方案异常");
        }

        return true;
    }

    /**
     * 获取所有项目物料预算
     */
    private List<ProjectBudgetMaterial> findProjectBudgetMaterials(Long projectId) {
        ProjectBudgetMaterialExample projectBudgetMaterialExample = new ProjectBudgetMaterialExample();
        projectBudgetMaterialExample.setOrderByClause("parent_id asc");//排序:第一层居前
        ProjectBudgetMaterialExample.Criteria criteria = projectBudgetMaterialExample.createCriteria();
        criteria.andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectBudgetMaterial> projectBudgetMaterials = projectBudgetMaterialService.selectByExample(projectBudgetMaterialExample);
        if (CollectionUtils.isEmpty(projectBudgetMaterials)) {
            return new ArrayList<>();
        }
        return projectBudgetMaterials;
    }

    /**
     * 获取所有详细设计物料转换为Map
     */
    public Map<Long, MilepostDesignPlanDetailDto> findMilepostDesignPlanDetails(Long projectId) {
        MilepostDesignPlanDetailDto query = new MilepostDesignPlanDetailDto();
        query.setProjectId(projectId);
        List<MilepostDesignPlanDetailDto> planDetailDtos = milepostDesignPlanDetailService.selectList(query);

        Map<Long, MilepostDesignPlanDetailDto> planDetailDtoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(planDetailDtos)) {
            for (MilepostDesignPlanDetailDto planDetail : planDetailDtos) {
                if (null == planDetail.getProjectBudgetMaterialId()) {
                    continue;
                }
                planDetailDtoMap.put(planDetail.getProjectBudgetMaterialId(), planDetail);
            }
        }
        return planDetailDtoMap;
    }

    /**
     * 更新详细设计物料
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateDesignPlanDetail(HashMap<Long, Long> idMap, ProjectBudgetMaterial projectBudgetMaterial,
                                       MilepostDesignPlanDetailDto planDetailDto, Integer milepostStatus, Long userBy) {
        planDetailDto.setUnit(getMaterialUnitByCode(projectBudgetMaterial.getUnit()));//单位
        planDetailDto.setUnitCode(projectBudgetMaterial.getUnit());//单位编码(物料预算unit存放的是单位编码)
        if (projectBudgetMaterial.getNumber() != null) {
            planDetailDto.setNumber(new BigDecimal(projectBudgetMaterial.getNumber()));//数量
        }
        planDetailDto.setBudgetUnitPrice(projectBudgetMaterial.getPrice());//单价
        planDetailDto.setPamCode(projectBudgetMaterial.getCode());//PAM编码
        planDetailDto.setSource(projectBudgetMaterial.getSource());//来源
        planDetailDto.setExt(projectBudgetMaterial.getExt());//是否外包
        planDetailDto.setMaterielDescr(projectBudgetMaterial.getName());//物料描述
        //设计里程碑审批通过后，允许新增模组，新增模组直接默认为“已模组确认”状态，不需要项目经理进行模组确认
        //预立项转正时，
        //1.新增:
        //1.1.里程碑未确认：详设预算层模组状态设置为未确认
        //1.2.里程碑已确认：详设预算层模组状态设置为已确认
        //2.删除：已确认的模组删除，模组状态可以不变，但已生成的采购需求删除  --保持现状？需与技术确认
        //3.更新（已存在的详设预算层）:不修改模组状态

        //20210325youwei确认
        if (null != projectBudgetMaterial.getPlannedDeliveryStartDate()) {
            Date deliveryTime = DateUtils.resetDate(projectBudgetMaterial.getPlannedDeliveryStartDate()); //重置日期时分秒为0
            planDetailDto.setDeliveryTime(deliveryTime); //取项目物料预算的计划交付时间到详设
        }
        planDetailDto.setDeletedFlag(projectBudgetMaterial.getDeletedFlag());
        //项目详细设计的预算小计=项目预算总价 added at 20200520
        planDetailDto.setBudgetSubtotal(projectBudgetMaterial.getPriceTotal());
        //预算变更审批通过之后比较前后的物料预算，如果有层级变化的话，对应的whetherModel取值要改变
        List<ProjectBudgetMaterial> budgetMaterialList = projectBudgetMaterialService.selectByParentId(planDetailDto.getId());
        if (ListUtils.isNotEmpty(budgetMaterialList)) {
            planDetailDto.setWhetherModel(false);
        }
        milepostDesignPlanDetailService.save(planDetailDto, userBy);
        idMap.put(projectBudgetMaterial.getId(), planDetailDto.getId());
        //如果有计划交货日期，子节点的外购物料得同步
        if (null != planDetailDto.getDeliveryTime()) {
            MilepostDesignPlanDetailExample example3 = new MilepostDesignPlanDetailExample();
            example3.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andParentIdEqualTo(planDetailDto.getId())
                    .andMaterialCategoryEqualTo(MaterialCategoryEnum.PURCHASED_PARTS.msg());
            List<MilepostDesignPlanDetail> detailson = milepostDesignPlanDetailMapper.selectByExample(example3);
            if (ListUtils.isNotEmpty(detailson)) {
                logger.info("*************** MilepostDesignPlanServiceImpl.updateDesignPlanDetail入参:{} ***************",
                        JSONObject.toJSONString(detailson));
                for (MilepostDesignPlanDetail son : detailson) {
                    son.setDeliveryTime(planDetailDto.getDeliveryTime());
                    milepostDesignPlanDetailMapper.updateByPrimaryKeySelective(son);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PurchaseMaterialRequirementDto> confirmLogic(Long designPlanDetailId, List<Long> projectIdList, List<Long> requirementIdList) {
        logger.info("进度确认逻辑的designPlanDetailId：{}，projectIdList：{}", designPlanDetailId, JsonUtils.toString(projectIdList));
        MilepostDesignPlanDetail plan = milepostDesignPlanDetailMapper.selectByPrimaryKey(designPlanDetailId);
        logger.info("进度确认逻辑的plan：{}", JsonUtils.toString(plan));
        List<PurchaseMaterialRequirementDto> newlyCreatedRequirementDtos = null;
        if (null != plan) {
            //发布状态或者草稿等状态过滤掉
            Boolean isNotDratOrDelete = true;
            if (null != plan.getStatus()) {
                if (CheckStatus.DRAFT.code() != plan.getStatus() && CheckStatus.CHECKING.code() != plan.getStatus()) {
                    isNotDratOrDelete = true;
                } else {
                    isNotDratOrDelete = false;
                }
            }
            //详设已删除
            if (plan.getDeletedFlag()) {
                isNotDratOrDelete = false;
            }
            if (isNotDratOrDelete) {
                //更新里程碑详细设计方案设计信息（milepost_design_plan_detail）状态-已确认,里程碑详细方案:供应链确认通过
                milepostDesignPlanConfirmRecordService.updateMilepostDesignPlanDetailModuleStatus(designPlanDetailId,
                        MilepostDesignPlanDetailModelStatus.CONFIRMED.code(),
                        CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code());
                //子节点
                MilepostDesignPlanDetailDto planDetailQuery = new MilepostDesignPlanDetailDto();
                planDetailQuery.setParentId(designPlanDetailId);
                planDetailQuery.setDeletedFlag(Boolean.FALSE);
                List<MilepostDesignPlanDetailDto> designPlanDetailSons =
                        milepostDesignPlanDetailExtMapper.selectList(planDetailQuery);
                for (MilepostDesignPlanDetailDto p : designPlanDetailSons) {
                    if (!Objects.equals(CheckStatus.DRAFT.code(), p.getStatus()) && !Objects.equals(CheckStatus.CHECKING.code(), p.getStatus())) {
                        milepostDesignPlanConfirmRecordService.updateMilepostDesignPlanDetailModuleStatus(p.getId(),
                                MilepostDesignPlanDetailModelStatus.CONFIRMED.code(), CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code());
                    }
                }
                if (null == plan.getDeliveryTime() || MaterialCategoryEnum.PURCHASED_PARTS.msg().equals(plan.getMaterialCategory())) {
                    List<MilepostDesignPlanDetailDto> designPlanDetailDtoList =
                            milepostDesignPlanDetailExtMapper.selectDeliveryTime(designPlanDetailId);
                    logger.info("进度确认逻辑的designPlanDetailDtoList：{}", JsonUtils.toString(designPlanDetailDtoList));
                    if (ListUtils.isNotEmpty(designPlanDetailDtoList)) {
                        Date deliveryTime = Optional.ofNullable(plan.getDeliveryTime()).orElse(designPlanDetailDtoList.get(0).getDeliveryTime());
                        //更新详细设计交货时间
                        //遍历更新交货时间直至底层
                        milepostDesignPlanConfirmRecordService.updateDeliveryTimeById(deliveryTime, designPlanDetailId,
                                CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code());
                    } else {
                        //模组没有机会交货时间，数据异常
                        Project project = projectService.selectByPrimaryKey(plan.getProjectId());
                        final ProjectType projectType = projectTypeMapper.selectByPrimaryKey(project.getType());
                        if (null == projectType) {
                            throw new MipException("找不到该项目项目类型");
                        }
                        String budgetConfig = projectType.getBudgetConfig();
                        JSONArray materiel = JSON.parseObject(budgetConfig).getJSONArray(BudgetConfigEnums.MATERIEL.getCode());

                        for (int i = 0; i < materiel.size(); i++) {
                            String code = materiel.getJSONObject(i).get("code").toString();
                            if (!StringUtils.isEmpty(code)) {
                                if ("fieldSetByMaterial".equals(code)) {
                                    String value = materiel.getJSONObject(i).get("value").toString();
                                    if (!StringUtils.isEmpty(value)) {
                                        if ("[1]".equals(value)) {
                                            throw new RuntimeException("模组层没有计划交货日期" + designPlanDetailId);
                                        }
                                    }

                                }
                            }
                        }

                    }
                } else {
                    Date deliveryTime = plan.getDeliveryTime();
                    MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
                    example.createCriteria().andParentIdEqualTo(designPlanDetailId).andDeletedFlagEqualTo(false);
                    List<MilepostDesignPlanDetail> list = milepostDesignPlanDetailMapper.selectByExample(example);
                    if (ListUtils.isNotEmpty(list)) {
                        List<MilepostDesignPlanDetailDto> dtos = BeanConverter.copy(list, MilepostDesignPlanDetailDto.class);
                        for (MilepostDesignPlanDetailDto dto : dtos) {
                            if (null == dto.getDeliveryTime()) {
                                milepostDesignPlanConfirmRecordService.updateDeliveryTimeById(deliveryTime, dto.getId(),
                                        CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code());
                            } else {
                                milepostDesignPlanConfirmRecordService.updateDeliveryTimeById(dto.getDeliveryTime(), dto.getId(),
                                        CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code());
                            }
                        }
                    }
                }
                logger.info("进度确认逻辑的产生采购需求的详设id：{}", designPlanDetailId);
                newlyCreatedRequirementDtos = purchaseMaterialRequirementService.recursiveGenerateRequirement(designPlanDetailId, requirementIdList);
                if (newlyCreatedRequirementDtos != null && !newlyCreatedRequirementDtos.isEmpty()) {
                    logger.info("本次确认操作新创建了 {} 个采购需求。", newlyCreatedRequirementDtos.size());
                }
            }

            // 同一个流程的projectId相同，只用执行一次
            if (!projectIdList.contains(plan.getProjectId())) {
                // 详细设计进度确认，同步里程碑状态 = 8 (详细方案类型里程碑允许评审通过)
                if (null != plan.getMilepostId()) {
                    // 获取所有详细设计
                    MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
                    milepostDesignPlanDetailExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(plan.getProjectId());
                    List<MilepostDesignPlanDetail> milepostDesignPlanDetailList =
                            milepostDesignPlanDetailMapper.selectByExample(milepostDesignPlanDetailExample);
                    if (CollectionUtils.isNotEmpty(milepostDesignPlanDetailList)) {
                        // 排除项目层
                        milepostDesignPlanDetailList = milepostDesignPlanDetailList.stream()
                                .filter(a -> !Objects.isNull(a.getParentId()) && a.getParentId().intValue() > 0).collect(Collectors.toList());
                        // 判断是否全部都进度确认通过，是就更新里程碑状态
                        Boolean confirmedMatch = milepostDesignPlanDetailList.stream().allMatch(a -> Objects.equals(a.getModuleStatus(), 1));
                        if (Boolean.TRUE.equals(confirmedMatch)) {
                            ProjectMilepost milepost = projectMilepostMapper.selectByPrimaryKey(plan.getMilepostId());
                            logger.info("详细设计进度确认，更新前的里程碑信息：{}", JsonUtils.toString(milepost));
                            if (Objects.nonNull(milepost) && !Objects.equals(milepost.getStatus(), MilepostStatus.CAN_PASS.getCode())
                                    && !Objects.equals(milepost.getStatus(), MilepostStatus.PASSED.getCode())) {
                                String lockName = String.format("MilepostDesignPlanServiceImpl.confirmLogic_%s", plan.getProjectId());
                                String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
                                try {
                                    if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME_SYNC, MAX_WAIT_TIME_SYNC * 2)) {
                                        ProjectMilepost projectMilepost = new ProjectMilepost();
                                        projectMilepost.setId(plan.getMilepostId());
                                        projectMilepost.setStatus(MilepostStatus.CAN_PASS.getCode());
                                        int count = projectMilepostMapper.updateByPrimaryKeySelective(projectMilepost);
                                        logger.info("详细设计进度确认，同步里程碑状态=8{}，里程碑id={}", count > 0 ? "成功" : "失败", plan.getMilepostId());

                                        projectIdList.add(plan.getProjectId());
                                    }
                                } catch (Exception e) {
                                    logger.error("confirmLogic处理projectId失败", e);
                                    throw e;
                                } finally {
                                    DistributedCASLock.unLock(lockName, currentDateStr);
                                }
                            }
                        }
                    }
                }
            }
        }
        logger.info("进度确认逻辑结束的designPlanDetailId：{}，designPlanDetail：{}", designPlanDetailId,
                JsonUtils.toString(milepostDesignPlanDetailMapper.selectByPrimaryKey(designPlanDetailId)));
        return newlyCreatedRequirementDtos;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public MilepostDesignPlanDetailDto budgetMaterialTranslateDesignPlanDetail(HashMap<Long, Long> idMap,
                                                                               Long projectBudgetMaterialId,
                                                                               ProjectBudgetMaterial projectBudgetMaterial, Long detailParentId,
                                                                               Long projectId,
                                                                               Long milepostId,
                                                                               Integer milepostStatus, Long userBy) {
        MilepostDesignPlanDetailDto resultDto = new MilepostDesignPlanDetailDto();
        Project project = projectService.selectByPrimaryKey(projectId);
        if (Boolean.TRUE.equals(project.getWbsEnabled())) {
            logger.warn("启用WBS的项目不走这段逻辑,请检查逻辑");
            return resultDto;
        }
        logger.info("预算变更：插入详设==" + projectBudgetMaterialId);
        MilepostDesignPlanDetailDto dto = new MilepostDesignPlanDetailDto();
        if (null == milepostId && null != projectId) {
            //milepostId = this.findMilepostId(projectId);
            ProjectMilepostDto projectMilepostQuery = new ProjectMilepostDto();
            projectMilepostQuery.setProjectId(projectId);
            projectMilepostQuery.setAnnexType(ProjectMilepostAnnexType.BOM.code());
            List<ProjectMilepostDto> milepostDtos = projectMilepostService.selectList(projectMilepostQuery);
            if (ListUtils.isNotEmpty(milepostDtos)) {
                milepostId = milepostDtos.get(0).getId();
            }
        }
        dto.setProjectBudgetMaterialId(projectBudgetMaterialId);//项目物料预算id
        dto.setProjectId(projectId);//项目id
        dto.setMilepostId(milepostId);//详细设计方案类型里程碑id
        dto.setMaterielDescr(projectBudgetMaterial.getName());//物料描述
        //因为空导致过不去 修复与20210329
        if (null != projectBudgetMaterial.getUnit()) {
            dto.setUnit(getMaterialUnitByCode(projectBudgetMaterial.getUnit()));//单位
        }

        dto.setUnitCode(projectBudgetMaterial.getUnit());//单位编码(物料预算unit存放的是单位编码)
        if (null != projectBudgetMaterial.getNumber()) {
            dto.setNumber(new BigDecimal(projectBudgetMaterial.getNumber()));//数量
        }
        dto.setBudgetUnitPrice(projectBudgetMaterial.getPrice());//单价
        dto.setPamCode(projectBudgetMaterial.getCode());//PAM编码
        dto.setSource(projectBudgetMaterial.getSource());//来源
        dto.setExt(projectBudgetMaterial.getExt());//是否外包
        dto.setParentId(detailParentId);
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        dto.setCreateBy(userBy);
        //项目详细设计的预算小计=项目预算总价 added at 20200520
        dto.setBudgetSubtotal(projectBudgetMaterial.getPriceTotal());
        /*if (null != projectBudgetMaterial.getPlannedDeliveryDate()) {
            dto.setDeliveryTime(projectBudgetMaterial.getPlannedDeliveryDate()); //取项目物料预算的计划交付时间到详设
        }*/
        //20210325youwei确认
        if (null != projectBudgetMaterial.getPlannedDeliveryStartDate()) {
            dto.setDeliveryTime(projectBudgetMaterial.getPlannedDeliveryStartDate()); //取项目物料预算的计划交付时间到详设
        }
        if (projectBudgetMaterial.getParentId() != null) {
            if (dto.getParentId() == null && idMap != null) {
                //获取物料父节点转换详细方案后的id
                dto.setParentId(idMap.get(projectBudgetMaterial.getParentId()));
            }

          /*  //暂时默认物料预算产品只有两层,第二层默认为模组
            dto.setWhetherModel(true);*/
            //昆山迭代4需求，预算最低层，才能’导入下层物料’和’变更’。 begin
            List<ProjectBudgetMaterial> budgetMaterialList = projectBudgetMaterialService.selectByParentId(projectBudgetMaterial.getId());
            if (budgetMaterialList.isEmpty()) {
                dto.setWhetherModel(true);
                if (projectBudgetMaterial.getExt() != null && projectBudgetMaterial.getExt()) {
                    //dto.setModuleStatus(1);//没有明确要设置为确认状态先注释
                }

            }
            //昆山迭代4需求，预算最低层，才能’导入下层物料’和’变更’。 end

            //设计里程碑审批通过后，允许新增模组，新增模组直接默认为“已模组确认”状态，不需要项目经理进行模组确认
            if (MilepostStatus.PASSED.getCode().equals(milepostStatus) || MilepostStatus.CAN_PASS.getCode().equals(milepostStatus)) {
                dto.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());
                dto.setStatus(CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code());
            } else {
                dto.setModuleStatus(MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code());
            }

        } else {
            dto.setParentId((long) -1);
            dto.setWhetherModel(false);
        }

        resultDto = milepostDesignPlanDetailService.save(dto, userBy);
        if (idMap != null) {
            idMap.put(projectBudgetMaterial.getId(), resultDto.getId());
        }
        List<MilepostDesignPlanDetailDto> plan = new ArrayList<>();
        dto.setProjectId(projectId);
        plan.add(resultDto);
        ticketTasksService.createTicketTasksByDesignPlanDetail(plan, null, projectBudgetMaterial.getId(), "物料预算变更");
        return resultDto;
    }

    @Override
    public void notifyThePersonInChargeOfTheMilestoneToConfirm(Long milepostId) {
        ProjectMilepostDto projectMilepostDto = projectMilepostService.getById(milepostId);
        if (checkMilepostDesignPlanAllConfirm(milepostId)) {
            logger.info("notifyThePersonInChargeOfTheMilestoneToConfirm，更新前的里程碑信息:{}", JsonUtils.toString(projectMilepostDto));
            //标志里程碑状态为允许标注完成
            ProjectMilepost projectMilepost = new ProjectMilepost();
            projectMilepost.setId(milepostId);
            projectMilepost.setStatus(MilepostStatus.CAN_PASS.getCode());
            projectMilepostExtMapper.updateStatusExt(projectMilepost);

            //邮件通知
            if (projectMilepostDto.getResponsible() != null) {
                mailExtService.sendMail(projectMilepostDto.getResponsible(), "里程碑详细方案进度确认", "里程碑详细方案进度确认");
            }
        }
    }

    @Override
    public Boolean checkMilepostDesignPlanAllConfirm(Long milepostId) {
        MilepostDesignPlanDetailDto milepostDesignPlanDetailQuery = new MilepostDesignPlanDetailDto();
        milepostDesignPlanDetailQuery.setWhetherModel(true);
        milepostDesignPlanDetailQuery.setMilepostId(milepostId);
        milepostDesignPlanDetailQuery.setModuleStatus(MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code());
        milepostDesignPlanDetailQuery.setDeletedFlag(DeletedFlag.VALID.code());
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos =
                milepostDesignPlanDetailService.selectList(milepostDesignPlanDetailQuery);
        if (ListUtil.isPresent(designPlanDetailDtos)) {
            return false;//代表部分模组未确认
        }
        return true;//代表所有模组均已确认
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleProjectBudgetMaterialChange(Long projectId, Long userBy,
                                                     List<ProjectBudgetChangeVO.ChangeHistory> projectBudgetMaterialChangeHistoryList) {
        logger.info("预算变更：同步详设开始" + projectId);

        //获取项目中详细设计方案类型里程碑
        ProjectMilepostDto projectMilepostQuery = new ProjectMilepostDto();
        projectMilepostQuery.setProjectId(projectId);
        projectMilepostQuery.setAnnexType(ProjectMilepostAnnexType.BOM.code());
        List<ProjectMilepostDto> milepostDtos = projectMilepostService.selectList(projectMilepostQuery);
        if (ListUtils.isEmpty(milepostDtos)) {
            return false;
        }
        Long milepostId = milepostDtos.get(0).getId();

        //需要调整采购需求的物料(父系数量或集成外包发生变更)的模组id以及变更类型
        Map<Long, Integer> designDetailIdMap = new HashMap<>();
        Map<Long, BigDecimal> designDetailNumberMap = new HashMap<>();

        //记录成品id与详细方案设计信息id
        HashMap<Long, Long> budgetIdToDetailIdMap = new HashMap<>();
        Iterator<ProjectBudgetChangeVO.ChangeHistory> iterator = projectBudgetMaterialChangeHistoryList.iterator();
        while (iterator.hasNext()) {
            ProjectBudgetChangeVO.ChangeHistory changeHistory = iterator.next();
            ProjectBudgetMaterialChangeHistoryVO history = null;
            if (changeHistory.getHistory() != null) {
                history = (ProjectBudgetMaterialChangeHistoryVO) changeHistory.getHistory();
            }
            ProjectBudgetMaterialChangeHistoryVO change = null;
            if (changeHistory.getChange() != null) {
                change = (ProjectBudgetMaterialChangeHistoryVO) changeHistory.getChange();
            }
            Integer changeType = changeHistory.getType();
            logger.info("预算变更：变更类型" + changeType);
            if (change != null) {
                logger.info("预算变更：变更记录" + change.getId());
                if (changeType != null && changeType.equals(ChangeType.ADD.code())) {
                    ProjectBudgetMaterial projectBudgetMaterial = BeanConverter.copy(change,
                            ProjectBudgetMaterial.class);
                    if (change.getParentId() == null) {
                        //转换产品
                        this.budgetMaterialTranslateDesignPlanDetail(budgetIdToDetailIdMap, change.getOriginId(),
                                projectBudgetMaterial, null, projectId, milepostId, milepostDtos.get(0).getStatus(), userBy);
                    } else {
                        //转换模组
                        this.budgetMaterialTranslateDesignPlanDetail(budgetIdToDetailIdMap, change.getOriginId(),
                                projectBudgetMaterial,
                                milepostDesignPlanDetailService.getIdByProjectBudgetMaterialId(change.getOriginParentId()), projectId,
                                milepostId, milepostDtos.get(0).getStatus(), userBy);
                    }
                } else if (changeType != null && changeType.equals(ChangeType.UPDATE.code())) {
                    Long designId = milepostDesignPlanDetailService.getIdByProjectBudgetMaterialId(change.getOriginId());
                    MilepostDesignPlanDetailDto designPlanDetailDto = new MilepostDesignPlanDetailDto();
                    designPlanDetailDto.setId(designId);
                    //物料预算变更字段:集成外包 单位 数量 单价（不含税）
                    designPlanDetailDto.setExt(change.getExt());
                    if (change.getExt() != null && change.getExt()) {
                        designPlanDetailDto.setModuleStatus(1);
                    }
                    designPlanDetailDto.setUnit(getMaterialUnitByCode(change.getUnit()));
                    designPlanDetailDto.setUnitCode(change.getUnit());
                    if (change.getNumber() != null) {
                        designPlanDetailDto.setNumber(new BigDecimal(change.getNumber()));
                    }
                    designPlanDetailDto.setBudgetUnitPrice(change.getPrice());
                    designPlanDetailDto.setBudgetSubtotal(change.getPriceTotal());
                    //缺陷管理流程[PAM_生产_【项目变更】项目预算变更时，变更物料计划开工日期，详设没跟着变]
                    if (null != change.getPlannedDeliveryStartDate()) {
                        designPlanDetailDto.setDeliveryTime(change.getPlannedDeliveryStartDate());
                        //取项目物料预算的计划交付时间到详设
                    }

                    milepostDesignPlanDetailService.save(designPlanDetailDto, userBy);
                    Boolean historyExt = null;
                    if (null != history) {
                        historyExt = history.getExt();
                    }
                    Boolean changeExt = change.getExt();

                    if (historyExt != null && changeExt != null) {
                        if (historyExt.equals(changeExt) && changeExt.equals(false) && null != history.getNumber()
                                && !history.getNumber().equals(change.getNumber())) {
                            //集成外包没有变化,且非集成外包,并且数量没有调整
                            designDetailIdMap.put(designId, ChangeType.UPDATE.code());
                            designDetailNumberMap.put(designId, null == change.getNumber() ? null : new BigDecimal(change.getNumber()));
                        } else if (!historyExt.equals(changeExt) && changeExt.equals(true)) {
                            //集成外包变化,且变更后集成外包:是
                            //designDetailIdMap.put(designId, ChangeType.DEL.code());
                        } else if (!historyExt.equals(changeExt) && changeExt.equals(false)) {
                            //集成外包变化,且变更后集成外包:否
                            designDetailIdMap.put(designId, ChangeType.ADD.code());
                            designDetailNumberMap.put(designId, null == change.getNumber() ? null : new BigDecimal(change.getNumber()));
                        }
                    }
                    List<MilepostDesignPlanDetailDto> plan = new ArrayList<>();
                    designPlanDetailDto.setProjectId(projectId);
                    plan.add(designPlanDetailDto);
                    ticketTasksService.createTicketTasksByDesignPlanDetail(plan, null, change.getHeaderId(), "物料预算变更");
                } else if (changeType != null && changeType.equals(ChangeType.DEL.code())) {
                    Long designId = milepostDesignPlanDetailService.getIdByProjectBudgetMaterialId(change.getOriginId());
                    MilepostDesignPlanDetailDto designPlanDetailDto = new MilepostDesignPlanDetailDto();
                    designPlanDetailDto.setId(designId);
                    designPlanDetailDto.setDeletedFlag(DeletedFlag.INVALID.code());
                    milepostDesignPlanDetailService.save(designPlanDetailDto, userBy);

                    designDetailIdMap.put(designId, ChangeType.DEL.code());
                    designDetailNumberMap.put(designId, null == change.getNumber() ? null : new BigDecimal(change.getNumber()));
                    //预算删除删除详设后再删除工单
                    List<MilepostDesignPlanDetailDto> plan = new ArrayList<>();
                    designPlanDetailDto.setProjectId(projectId);
                    plan.add(designPlanDetailDto);
                    ticketTasksService.createTicketTasksByDesignPlanDetail(plan, null, change.getHeaderId(), "物料预算变更");
                }
            }
        }

        handlePurchaseRequirement(designDetailIdMap, designDetailNumberMap, userBy);
        return true;
    }

    @Override
    public List<MaterialCostDto> exportMaterialCostForSubmitRecord(Long submitRecordId) {
        MilepostDesignPlanDetailDto designPlanDetailQuery = new MilepostDesignPlanDetailDto();
        designPlanDetailQuery.setSubmitRecordId(submitRecordId);
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos =
                milepostDesignPlanDetailService.selectList(designPlanDetailQuery);

        List<Long> materialCostIds = null;
        if (ListUtil.isPresent(designPlanDetailDtos)) {
            materialCostIds = ListUtil.map(designPlanDetailDtos, "designCostId");
        }
        return materialCostExtService.invokeMaterialCostApiSelectListByIds(materialCostIds);
    }

    @Override
    public List<MaterialCostDto> exportMaterialCostForConfirmRecord(Long confirmRecordId) {
        MilepostDesignPlanConfirmRecordRelationDto confirmRecordRelationQuery =
                new MilepostDesignPlanConfirmRecordRelationDto();
        confirmRecordRelationQuery.setConfirmRecordId(confirmRecordId);
        List<MilepostDesignPlanConfirmRecordRelationDto> confirmRecordRelationDtos =
                milepostDesignPlanConfirmRecordRelationService.selectList(confirmRecordRelationQuery);
        //模组id集合
        List<Long> modelIds = null;
        if (ListUtil.isPresent(confirmRecordRelationDtos)) {
            modelIds = ListUtil.map(confirmRecordRelationDtos, "designPlanDetailId");
        } else {
            return new ArrayList<>();
        }

        MilepostDesignPlanDetailDto designPlanDetailQuery = new MilepostDesignPlanDetailDto();
        designPlanDetailQuery.setParentIds(modelIds);
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos =
                milepostDesignPlanDetailService.selectList(designPlanDetailQuery);

        List<Long> materialCostIds = null;
        if (ListUtil.isPresent(designPlanDetailDtos)) {
            materialCostIds = ListUtil.map(designPlanDetailDtos, "designCostId");
        }
        return materialCostExtService.invokeMaterialCostApiSelectListByIds(materialCostIds);
    }

    /**
     * 处理采购需求和发布明细
     *
     * @param designDetailIdMap
     */
    private void handlePurchaseRequirement(Map<Long, Integer> designDetailIdMap, Map<Long, BigDecimal> designDetailNumberMap, Long userBy) {
        logger.info("handlePurchaseRequirement的designDetailIdMap：{}，userBy：{}", JsonUtils.toString(designDetailIdMap), userBy);
        Iterator designDetailIdIterator = designDetailIdMap.entrySet().iterator();

        //把产品换成模组id
        Map<Long, Integer> modelIdMap = new HashMap();
        while (designDetailIdIterator.hasNext()) {
            Map.Entry entry = (Map.Entry) designDetailIdIterator.next();
            Long key = (Long) entry.getKey();
            Integer changeType = (Integer) entry.getValue();
            MilepostDesignPlanDetailDto designPlanDetailDto = milepostDesignPlanDetailService.getById(key);
            if (designPlanDetailDto != null && designPlanDetailDto.getParentId().equals((long) -1)) {
                MilepostDesignPlanDetailDto sonQuery = new MilepostDesignPlanDetailDto();
                sonQuery.setParentId(key);
                sonQuery.setDeletedFlag(DeletedFlag.VALID.code());
                List<MilepostDesignPlanDetailDto> sons = milepostDesignPlanDetailService.selectList(sonQuery);
                for (MilepostDesignPlanDetailDto son : sons) {
                    modelIdMap.put(son.getId(), changeType);
                }
                designDetailIdIterator.remove();
            }
        }

        Iterator modelIdMapIterator = modelIdMap.entrySet().iterator();
        while (modelIdMapIterator.hasNext()) {
            Map.Entry modelIdEntry = (Map.Entry) modelIdMapIterator.next();
            Long modelId = (Long) modelIdEntry.getKey();
            Integer changeType = (Integer) modelIdEntry.getValue();
            designDetailIdMap.put(modelId, changeType);
        }

        List<Long> requirementIdList = new ArrayList<>();
        //遍历所有发生变更的模组
        designDetailIdIterator = designDetailIdMap.entrySet().iterator();
        while (designDetailIdIterator.hasNext()) {
            Map.Entry designDetailIdEntry = (Map.Entry) designDetailIdIterator.next();
            Long designDetailId = (Long) designDetailIdEntry.getKey();
            Integer changeType = (Integer) designDetailIdEntry.getValue();
            if (changeType != null && changeType.equals(ChangeType.ADD.code())) {
                this.recursiveHandlePurChaseForAdd(designDetailId, userBy, requirementIdList, designDetailNumberMap);
            } else if (changeType != null && changeType.equals(ChangeType.UPDATE.code())) {
                this.recursiveHandlePurChaseForUpdate(designDetailId, userBy, requirementIdList, designDetailNumberMap);
            } else if (changeType != null && changeType.equals(ChangeType.DEL.code())) {
                this.recursiveHandlePurChaseForDel(designDetailId, userBy, requirementIdList, designDetailNumberMap);
            }
        }

        if (ListUtils.isNotEmpty(requirementIdList)) {
            purchaseMaterialRequirementService.batchUpdateStatus(requirementIdList);
        }

    }

    private void recursiveHandlePurChaseForUpdate(Long designPlanDetailId, Long userBy, List<Long> requirementIdList,
                                                  Map<Long, BigDecimal> designDetailNumberMap) {
        String redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + designPlanDetailId;
        String luaScript = String.format("return redis.call('get', '%s'); \n", redisKeyByDelete);
        String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
        if (StringUtils.isNotEmpty(redisResult)) {
            return;
        }
        if (designPlanDetailId == null) {
            return;
        }
        logger.info("recursiveHandlePurChaseForUpdate的designPlanDetailId：{}，userBy：{}，designDetailNumberMap：{}",
                designPlanDetailId, userBy, JsonUtils.toString(designDetailNumberMap));
        MilepostDesignPlanDetailDto designPlanDetailDto =
                milepostDesignPlanDetailService.getDesignPlanDetailForBusiness(null, designPlanDetailId, null);
        if (designPlanDetailDto == null) {
            return;
        }
        if (Boolean.TRUE.equals(designPlanDetailDto.getDeletedFlag())) {
            return;
        }
        BigDecimal designDetailChangeNum = designDetailNumberMap.get(designPlanDetailId);
        if (designDetailChangeNum == null) {
            return;
        }

        logger.info("recursiveHandlePurChaseForUpdate的新designPlanDetailDto：{}", JsonUtils.toString(designPlanDetailDto));
        //初始化不产生采购需求
//        if(null != designPlanDetailDto.getInit() && designPlanDetailDto.getInit()){
//            return;
//        }

        MilepostDesignPlanDetailDto parentDetailDto = null;
        if (designPlanDetailDto.getParentId() != null) {
            parentDetailDto = milepostDesignPlanDetailService.getById(designPlanDetailDto.getParentId());
        }
        logger.info("recursiveHandlePurChaseForUpdate的parentDetailDto：{}", JsonUtils.toString(parentDetailDto));

        String redisKey = "milepostDesignPlanDetailFinalNumber_" + designPlanDetailId;
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(String.format("if redis.call('exists', '%s') == 0 then \n", redisKey));
        stringBuffer.append(String.format("redis.call('setnx', '%s', %s); \n", redisKey, designDetailChangeNum));
        stringBuffer.append(String.format("redis.call('expire', '%s', %d); \n", redisKey, 3600L));
        stringBuffer.append(String.format("return redis.call('get', '%s'); \n", redisKey));
        stringBuffer.append("else \n");

        stringBuffer.append(String.format("local number = %s; \n", designDetailChangeNum));
        stringBuffer.append(String.format("local val = redis.call('get', '%s'); \n", redisKey));
        stringBuffer.append("if tonumber(number) ~= tonumber(val) then \n");
        stringBuffer.append("local res = tonumber(number) - tonumber(val); \n");
        stringBuffer.append(String.format("redis.call('incrbyfloat', '%s', res); \n", redisKey));
        stringBuffer.append(String.format("return redis.call('get', '%s'); \n", redisKey));
        stringBuffer.append("else \n");
        stringBuffer.append(String.format("return redis.call('get', '%s'); \n", redisKey));
        stringBuffer.append("end; \n");

        stringBuffer.append("end; \n");
        String redisNumber = redisLuaScriptServer.executeLuaScript(stringBuffer.toString(), new ArrayList<>(), "");

        //当前设计信息的模组(父节点)为非集成外包且设计信息已产生采购需求状态下,设计信息则需要更新采购需求和新增发布明细
        if (parentDetailDto != null && Boolean.FALSE.equals(parentDetailDto.getExt()) && Boolean.TRUE.equals(parentDetailDto.getWhetherModel())
                && DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code().equals(designPlanDetailDto.getGenerateRequirement())
                && Objects.equals("外购物料", designPlanDetailDto.getMaterialCategory()) && designPlanDetailDto.getNumber() != null) {

            PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(
                    designPlanDetailDto.getProjectId(),
                    designPlanDetailDto.getDeliveryTime(),
                    designPlanDetailDto.getPamCode());

            if (requirementDto != null) {
                //获取当前设计信息的发布明细累积:变化前的总发布量
                BigDecimal historyNum = purchaseMaterialReleaseDetailService.getPublishNumSumByDesignPlanDetailId(designPlanDetailDto.getId());
                //计算当前设计信息的最新发布总量:设计信息的数量*所有父级的积数
                BigDecimal parentSigmaById = milepostDesignPlanDetailService.getParentSigmaById(designPlanDetailDto.getParentId(), null);
                logger.info("recursiveHandlePurChaseForUpdate的详设id：{}，redisNumber：{}，parentSigmaById：{}，historyNum：{}，redisLuaScriptServer执行的脚本：{}",
                        designPlanDetailDto.getId(), redisNumber, parentSigmaById, historyNum, stringBuffer.toString());

                BigDecimal changeNum = new BigDecimal(redisNumber).multiply(parentSigmaById);
                //当前设计信息的当次发布差异量
                BigDecimal differenceNumber = changeNum.subtract(historyNum);

                String requirementRedisNumber = purchaseMaterialRequirementService.getRequirementRedisNumber(designPlanDetailDto.getProjectId(),
                        designPlanDetailDto.getDeliveryTime(), designPlanDetailDto.getPamCode(), requirementDto.getNeedTotal(), differenceNumber);

                requirementDto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                requirementDto.setApprovedSupplierNumber(0);
                requirementDto = purchaseMaterialRequirementService.save(requirementDto, userBy);
                if (BigDecimal.ZERO.compareTo(differenceNumber) != 0) {
                    //purchaseMaterialRequirementService.updateStatus(requirementDto.getId());//根据总需求量更新采购需求的状态
                    requirementIdList.add(requirementDto.getId());

                    purchaseMaterialRequirementService.addPurchaseMaterialReleaseDetail(
                            requirementDto.getMaterielId(),
                            requirementDto.getId(),
                            designPlanDetailDto,
                            ReleaseDetailStatus.CHANGE.code(),
                            userBy,
                            differenceNumber);

                    //高并发情况下更新采购需求的总需求量
                    /*BigDecimal totalNum =
                            purchaseMaterialReleaseDetailService.getPublishNumSumByPurchaseMaterialRequirementId(requirementDto.getId());
                    requirementDto.setNeedTotal(totalNum);
                    purchaseMaterialRequirementService.save(requirementDto, userBy);*/
                }
//                purchaseMaterialRequirementExtMapper.updateApprovedSupplierNumber();
            }
        }

        //处理所有下级的采购需求情况
        logger.info("recursiveHandlePurChaseForUpdate开始处理所有下级的采购需求情况");
        List<MilepostDesignPlanDetail> designPlanDetailList = milepostDesignPlanDetailService.selectByParentId(designPlanDetailDto.getId());
        logger.info("recursiveHandlePurChaseForUpdate的下级designPlanDetailList：{}，parentChangeNumber：{}", JsonUtils.toString(designPlanDetailList),
                designPlanDetailDto.getNumber());
        updateSonsPurchaseMaterialRequirement(designPlanDetailList, designDetailChangeNum, userBy, requirementIdList);
        logger.info("recursiveHandlePurChaseForUpdate结束处理所有下级的采购需求情况");
    }

    private void updateSonsPurchaseMaterialRequirement(List<MilepostDesignPlanDetail> designPlanDetailList, BigDecimal parentChangeNumber,
                                                       Long userBy, List<Long> requirementIdList) {
        if (CollectionUtils.isEmpty(designPlanDetailList)) {
            return;
        }
        for (MilepostDesignPlanDetail planDetail : designPlanDetailList) {
            String redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + planDetail.getId();
            String luaScript = String.format("return redis.call('get', '%s'); \n", redisKeyByDelete);
            String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
            if (StringUtils.isNotEmpty(redisResult)) {
                continue;
            }
            if (Boolean.TRUE.equals(planDetail.getDeletedFlag())) {
                continue;
            }

            String redisKey = "milepostDesignPlanDetailFinalNumber_" + planDetail.getId();
            StringBuffer sb = new StringBuffer();
            sb.append(String.format("if redis.call('exists', '%s') == 0 then \n", redisKey));
            sb.append(String.format("redis.call('setnx', '%s', %s); \n", redisKey, planDetail.getNumber()));
            sb.append(String.format("redis.call('expire', '%s', %d); \n", redisKey, 3600L));
            sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
            sb.append("else \n");

            sb.append(String.format("local number = %s; \n", planDetail.getNumber()));
            sb.append(String.format("local val = redis.call('get', '%s'); \n", redisKey));
            sb.append("if tonumber(number) ~= tonumber(val) then \n");
            sb.append("local res = tonumber(number) - tonumber(val); \n");
            sb.append(String.format("redis.call('incrbyfloat', '%s', res); \n", redisKey));
            sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
            sb.append("else \n");
            sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
            sb.append("end; \n");

            sb.append("end; \n");
            String redisNumber = redisLuaScriptServer.executeLuaScript(sb.toString(), new ArrayList<>(), "");

            if (DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code().equals(planDetail.getGenerateRequirement())
                    && Objects.equals("外购物料", planDetail.getMaterialCategory()) && planDetail.getNumber() != null && parentChangeNumber != null) {
                PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(
                        planDetail.getProjectId(),
                        planDetail.getDeliveryTime(),
                        planDetail.getPamCode());

                if (requirementDto != null) {
                    //获取当前设计信息的发布明细累积:变化前的总发布量
                    BigDecimal historyNum = purchaseMaterialReleaseDetailService.getPublishNumSumByDesignPlanDetailId(planDetail.getId());
                    logger.info("recursiveHandlePurChaseForUpdate子级详设：{}变更修改数量时，historyNum：{}", planDetail.getId(), historyNum);
                    //计算当前设计信息的最新发布总量:设计信息的数量*所有父级的积数
                    logger.info("recursiveHandlePurChaseForUpdate子级详设变更修改数量时，parentChangeNumber：{}", parentChangeNumber);

                    BigDecimal parentSigmaById = milepostDesignPlanDetailService.getParentSigmaById(planDetail.getParentId(), null);
                    logger.info("recursiveHandlePurChaseForUpdate的详设id：{}，redisNumber：{}，parentSigmaById：{}，historyNum：{}，" +
                            "redisLuaScriptServer执行的脚本：{}", planDetail.getId(), redisNumber, parentSigmaById, historyNum, sb.toString());

                    BigDecimal changeNum = new BigDecimal(redisNumber).multiply(parentSigmaById);
                    logger.info("recursiveHandlePurChaseForUpdate子级详设变更修改数量时，changeNum：{}", changeNum);
                    //当前设计信息的当次发布差异量
                    BigDecimal differenceNumber = changeNum.subtract(historyNum);

                    String requirementRedisNumber = purchaseMaterialRequirementService.getRequirementRedisNumber(planDetail.getProjectId(),
                            planDetail.getDeliveryTime(), planDetail.getPamCode(), requirementDto.getNeedTotal(), differenceNumber);

                    requirementDto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                    requirementDto.setApprovedSupplierNumber(0);
                    purchaseMaterialRequirementService.save(requirementDto, userBy);
                    if (BigDecimal.ZERO.compareTo(differenceNumber) != 0) {
                        //purchaseMaterialRequirementService.updateStatus(requirementDto.getId());//根据总需求量更新采购需求的状态
                        requirementIdList.add(requirementDto.getId());

                        purchaseMaterialRequirementService.addPurchaseMaterialReleaseDetail(
                                requirementDto.getMaterielId(),
                                requirementDto.getId(),
                                BeanConverter.copy(planDetail, MilepostDesignPlanDetailDto.class),
                                ReleaseDetailStatus.CHANGE.code(),
                                userBy,
                                differenceNumber);

                        //高并发情况下更新采购需求的总需求量
                        /*BigDecimal totalNum =
                                purchaseMaterialReleaseDetailService.getPublishNumSumByPurchaseMaterialRequirementId(requirementDto.getId());
                        requirementDto.setNeedTotal(totalNum);
                        purchaseMaterialRequirementService.save(requirementDto, userBy);*/
                    }
                }
            }

            List<MilepostDesignPlanDetail> detailList = milepostDesignPlanDetailService.selectByParentId(planDetail.getId());
            logger.info("recursiveHandlePurChaseForUpdate的下级detailList：{}，parentChangeNumber：{}",
                    JsonUtils.toString(designPlanDetailList), parentChangeNumber);
            updateSonsPurchaseMaterialRequirement(detailList, parentChangeNumber, userBy, requirementIdList);
        }
    }

    private void recursiveHandlePurChaseForAdd(Long designPlanDetailId, Long userBy, List<Long> requirementIdList,
                                               Map<Long, BigDecimal> designDetailNumberMap) {
        if (designPlanDetailId == null) {
            return;
        }
        logger.info("recursiveHandlePurChaseForAdd的designPlanDetailId：{}，userBy：{}，designDetailNumberMap：{}",
                designPlanDetailId, userBy, JsonUtils.toString(designDetailNumberMap));

        String redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + designPlanDetailId;
        String luaScript = String.format("return redis.call('get', '%s'); \n", redisKeyByDelete);
        String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
        if (StringUtils.isNotEmpty(redisResult)) {
            return;
        }

        MilepostDesignPlanDetailDto designPlanDetailDto =
                milepostDesignPlanDetailService.getDesignPlanDetailForBusiness(null, designPlanDetailId, null);
        if (designPlanDetailDto == null) {
            return;
        }
        if (Boolean.TRUE.equals(designPlanDetailDto.getDeletedFlag())) {
            return;
        }

        logger.info("recursiveHandlePurChaseForAdd的新designPlanDetailDto：{}", JsonUtils.toString(designPlanDetailDto));

        MilepostDesignPlanDetailDto parentDetailDto = null;
        if (designPlanDetailDto.getParentId() != null) {
            parentDetailDto = milepostDesignPlanDetailService.getById(designPlanDetailDto.getParentId());
            if (parentDetailDto == null) {
                return;
            }
            if (Boolean.TRUE.equals(parentDetailDto.getDeletedFlag())) {
                return;
            }

            redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + designPlanDetailDto.getParentId();
            luaScript = String.format("return redis.call('get', '%s'); \n", redisKeyByDelete);
            redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
            if (StringUtils.isNotEmpty(redisResult)) {
                return;
            }
        }
        logger.info("recursiveHandlePurChaseForAdd的parentDetailDto：{}", JsonUtils.toString(parentDetailDto));
        //当前设计信息的模组(父节点)为非集成外包且设计信息已产生采购需求状态下,设计信息则需要更新或新增采购需求和新增发布明细
        if (parentDetailDto != null && Boolean.FALSE.equals(parentDetailDto.getExt()) && Boolean.TRUE.equals(parentDetailDto.getWhetherModel())
                && Objects.equals("外购物料", designPlanDetailDto.getMaterialCategory()) && designPlanDetailDto.getNumber() != null) {

            PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(
                    designPlanDetailDto.getProjectId(),
                    designPlanDetailDto.getDeliveryTime(),
                    designPlanDetailDto.getPamCode());

            BigDecimal differenceNumber = designPlanDetailDto.getNumber()
                    .multiply(milepostDesignPlanDetailService.getParentSigmaById(designPlanDetailDto.getParentId(), null));

            if (requirementDto == null) {
                Long organizationId = projectBusinessService.getOrganizationIdByProjectId(designPlanDetailDto.getProjectId());
                MaterialDto materialDto = materialExtService.invokeMaterialApiGetByPamCode(designPlanDetailDto.getPamCode(), organizationId);
                //TODO:3082,代表机器人的物料,将来可考虑获取上下文的ouId
                requirementDto = new PurchaseMaterialRequirementDto();
                //生成物料采购需求
                requirementDto.setProjectId(designPlanDetailDto.getProjectId());
                requirementDto.setErpCode(designPlanDetailDto.getErpCode());
                requirementDto.setPamCode(designPlanDetailDto.getPamCode());
                requirementDto.setDeliveryTime(designPlanDetailDto.getDeliveryTime());
                if (materialDto != null) {
                    requirementDto.setMaterielId(materialDto.getId());
                    requirementDto.setMaterielDescr(materialDto.getItemInfo());
                }
                requirementDto.setUnitCode(designPlanDetailDto.getUnitCode());
                requirementDto.setUnit(designPlanDetailDto.getUnit());
            }

            String requirementRedisNumber = purchaseMaterialRequirementService.getRequirementRedisNumber(designPlanDetailDto.getProjectId(),
                    designPlanDetailDto.getDeliveryTime(), designPlanDetailDto.getPamCode(), requirementDto.getNeedTotal(), differenceNumber);

            requirementDto.setNeedTotal(new BigDecimal(requirementRedisNumber));
            requirementDto.setApprovedSupplierNumber(0);
            requirementDto = purchaseMaterialRequirementService.save(requirementDto, userBy);
            if (BigDecimal.ZERO.compareTo(differenceNumber) != 0) {
                //purchaseMaterialRequirementService.updateStatus(requirementDto.getId());//根据总需求量更新采购需求的状态
                requirementIdList.add(requirementDto.getId());

                purchaseMaterialRequirementService.addPurchaseMaterialReleaseDetail(requirementDto.getMaterielId(),
                        requirementDto.getId(),
                        designPlanDetailDto,
                        ReleaseDetailStatus.CHANGE.code(),
                        userBy,
                        differenceNumber);

                //高并发情况下更新采购需求的总需求量
                /*BigDecimal totalNum =
                        purchaseMaterialReleaseDetailService.getPublishNumSumByPurchaseMaterialRequirementId(requirementDto.getId());
                requirementDto.setNeedTotal(totalNum);
                purchaseMaterialRequirementService.save(requirementDto, userBy);*/
            }
//            purchaseMaterialRequirementExtMapper.updateApprovedSupplierNumber();

            designPlanDetailDto.setGenerateRequirement(DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code());
            milepostDesignPlanDetailService.save(designPlanDetailDto, userBy);
        }
    }

    /**
     * 递归删除详设信息的采购需求和发布明细
     *
     * @param designPlanDetailId
     * @param userBy
     */
    private void recursiveHandlePurChaseForDel(Long designPlanDetailId, Long userBy, List<Long> requirementIdList,
                                               Map<Long, BigDecimal> designDetailNumberMap) {
        if (designPlanDetailId == null) {
            return;
        }
        logger.info("recursiveHandlePurChaseForUpdate的designPlanDetailId：{}，userBy：{}，designDetailNumberMap：{}",
                designPlanDetailId, userBy, JsonUtils.toString(designDetailNumberMap));

        String redisKey = "milepostDesignPlanDetailByDelete_" + designPlanDetailId;
        String luaScript = String.format("return redis.call('get', '%s'); \n", redisKey);
        String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
        if (StringUtils.isNotEmpty(redisResult)) {
            return;
        }

        MilepostDesignPlanDetailDto designPlanDetailDto =
                milepostDesignPlanDetailService.getDesignPlanDetailForBusiness(null, designPlanDetailId, null);
        if (designPlanDetailDto == null) {
            return;
        }
        if (Boolean.TRUE.equals(designPlanDetailDto.getDeletedFlag())) {
            return;
        }

        logger.info("recursiveHandlePurChaseForUpdate的新designPlanDetailDto：{}", JsonUtils.toString(designPlanDetailDto));

        //只有设计信息有产生物料需求的值,且已产生采购需求和发布明细
        if (DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code().equals(designPlanDetailDto.getGenerateRequirement())
                && Objects.equals("外购物料", designPlanDetailDto.getMaterialCategory()) && designPlanDetailDto.getNumber() != null) {
            PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(
                    designPlanDetailDto.getProjectId(),
                    designPlanDetailDto.getDeliveryTime(),
                    designPlanDetailDto.getPamCode());

            if (requirementDto != null) {
                //获取当前设计信息的发布明细累积:变化前的总发布量
                BigDecimal historyNum = purchaseMaterialReleaseDetailService.getPublishNumSumByDesignPlanDetailId(designPlanDetailDto.getId());
                BigDecimal differenceNumber = BigDecimal.ZERO.subtract(historyNum);

                String requirementRedisNumber = purchaseMaterialRequirementService.getRequirementRedisNumber(designPlanDetailDto.getProjectId(),
                        designPlanDetailDto.getDeliveryTime(), designPlanDetailDto.getPamCode(), requirementDto.getNeedTotal(), differenceNumber);

                requirementDto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                requirementDto.setApprovedSupplierNumber(0);
                purchaseMaterialRequirementService.save(requirementDto, userBy);
                if (BigDecimal.ZERO.compareTo(differenceNumber) != 0) {
                    //purchaseMaterialRequirementService.updateStatus(requirementDto.getId());//根据总需求量更新采购需求的状态
                    requirementIdList.add(requirementDto.getId());

                    purchaseMaterialRequirementService.addPurchaseMaterialReleaseDetail(
                            requirementDto.getMaterielId(),
                            requirementDto.getId(),
                            designPlanDetailDto,
                            ReleaseDetailStatus.CHANGE.code(),
                            userBy,
                            differenceNumber);

                    //高并发情况下更新采购需求的总需求量
                    /*BigDecimal totalNum =
                            purchaseMaterialReleaseDetailService.getPublishNumSumByPurchaseMaterialRequirementId(requirementDto.getId());
                    requirementDto.setNeedTotal(totalNum);
                    purchaseMaterialRequirementService.save(requirementDto, userBy);*/
                }
//                    purchaseMaterialRequirementExtMapper.updateApprovedSupplierNumber();
            }
        }

        designPlanDetailDto.setDeletedFlag(Boolean.TRUE);
        milepostDesignPlanDetailService.save(designPlanDetailDto, userBy);

        String redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + designPlanDetailDto.getId();
        StringBuffer deleteSB = new StringBuffer();
        deleteSB.append(String.format("redis.call('setnx', '%s', %d); \n", redisKeyByDelete, designPlanDetailDto.getId()));
        deleteSB.append(String.format("redis.call('expire', '%s', %d); \n", redisKeyByDelete, 3600L));
        redisLuaScriptServer.executeLuaScript(deleteSB.toString(), new ArrayList<>(), "");

        //处理所有下级的采购需求情况
        logger.info("recursiveHandlePurChaseForDel开始处理所有下级的采购需求情况");
        List<MilepostDesignPlanDetail> designPlanDetailList = milepostDesignPlanDetailService.selectByParentId(designPlanDetailDto.getId());
        logger.info("recursiveHandlePurChaseForDel的下级designPlanDetailList：{}", JsonUtils.toString(designPlanDetailList));
        deleteSonsPurchaseMaterialRequirement(designPlanDetailList, userBy, requirementIdList);
        logger.info("recursiveHandlePurChaseForDel结束处理所有下级的采购需求情况");
    }

    private void deleteSonsPurchaseMaterialRequirement(List<MilepostDesignPlanDetail> designPlanDetailList, Long userBy,
                                                       List<Long> requirementIdList) {
        if (CollectionUtils.isEmpty(designPlanDetailList)) {
            return;
        }
        for (MilepostDesignPlanDetail planDetail : designPlanDetailList) {
            if (Boolean.TRUE.equals(planDetail.getDeletedFlag())) {
                continue;
            }
            String redisKey = "milepostDesignPlanDetailByDelete_" + planDetail.getId();
            String luaScript = String.format("return redis.call('get', '%s'); \n", redisKey);
            String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
            if (StringUtils.isNotEmpty(redisResult)) {
                continue;
            }

            if (DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code().equals(planDetail.getGenerateRequirement())
                    && Objects.equals("外购物料", planDetail.getMaterialCategory())) {
                PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(
                        planDetail.getProjectId(),
                        planDetail.getDeliveryTime(),
                        planDetail.getPamCode());

                if (null != requirementDto) {
                    //获取当前设计信息的发布明细累积:变化前的总发布量
                    BigDecimal historyNum = purchaseMaterialReleaseDetailService.getPublishNumSumByDesignPlanDetailId(planDetail.getId());
                    logger.info("recursiveHandlePurChaseForDel子级删除的详设：{}变更修改数量时，historyNum：{}", planDetail.getId(), historyNum);
                    //当前设计信息的当次发布差异量
                    BigDecimal differenceNumber = BigDecimal.ZERO.subtract(historyNum);

                    String requirementRedisNumber = purchaseMaterialRequirementService.getRequirementRedisNumber(planDetail.getProjectId(),
                            planDetail.getDeliveryTime(), planDetail.getPamCode(), requirementDto.getNeedTotal(), differenceNumber);

                    requirementDto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                    requirementDto.setApprovedSupplierNumber(0);
                    purchaseMaterialRequirementService.save(requirementDto, userBy);
                    if (BigDecimal.ZERO.compareTo(differenceNumber) != 0) {
                        //purchaseMaterialRequirementService.updateStatus(requirementDto.getId());//根据总需求量更新采购需求的状态
                        requirementIdList.add(requirementDto.getId());

                        purchaseMaterialRequirementService.addPurchaseMaterialReleaseDetail(
                                requirementDto.getMaterielId(),
                                requirementDto.getId(),
                                BeanConverter.copy(planDetail, MilepostDesignPlanDetailDto.class),
                                ReleaseDetailStatus.CHANGE.code(),
                                userBy,
                                differenceNumber);

                        //高并发情况下更新采购需求的总需求量
                        /*BigDecimal totalNum =
                                purchaseMaterialReleaseDetailService.getPublishNumSumByPurchaseMaterialRequirementId(requirementDto.getId());
                        requirementDto.setNeedTotal(totalNum);
                        purchaseMaterialRequirementService.save(requirementDto, userBy);*/
                    }
                }
            }

            planDetail.setDeletedFlag(Boolean.TRUE);
            milepostDesignPlanDetailService.save(BeanConverter.copy(planDetail, MilepostDesignPlanDetailDto.class), userBy);

            String redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + planDetail.getId();
            StringBuffer deleteSB = new StringBuffer();
            deleteSB.append(String.format("redis.call('setnx', '%s', %d); \n", redisKeyByDelete, planDetail.getId()));
            deleteSB.append(String.format("redis.call('expire', '%s', %d); \n", redisKeyByDelete, 3600L));
            redisLuaScriptServer.executeLuaScript(deleteSB.toString(), new ArrayList<>(), "");

            List<MilepostDesignPlanDetail> detailList = milepostDesignPlanDetailService.selectByParentId(planDetail.getId());
            logger.info("recursiveHandlePurChaseForDel的下级detailList：{}", JsonUtils.toString(designPlanDetailList));
            deleteSonsPurchaseMaterialRequirement(detailList, userBy, requirementIdList);
        }
    }

    /**
     * 查询单位编码
     *
     * @param code
     */
    private String getMaterialUnitByCode(String code) {
        Asserts.notEmpty(code, ErrorCode.BASEDATA_DICT_CODE_NOT_NULL);
        DictDto unit = CacheDataUtils.findDictByTypeAndCode(DictType.MEASUREMENT_UNIT.code(), code);
        Asserts.notEmpty(unit, ErrorCode.BASEDATA_MATERIAL_UNIT_NOT_FIND);
        return unit.getName();
    }

    @Override
    public MilepostDesignPlanDetailApprovedVO findApprovedDesignPlanDetail(Long projectId, Long milepostId) {
        Guard.notNull(projectId, "项目ID不能为空");

        MilepostDesignPlanDetailApprovedVO result = new MilepostDesignPlanDetailApprovedVO();
        ProjectDto projectDto = projectBusinessService.findById(projectId);
        result.setProjectCode(projectDto.getCode());
        //获取所有详细设计物料
        List<MilepostDesignPlanDetailDto> planDetailDtos = milepostDesignPlanDetailExtMapper.findApprovedMilepostDesignPlanDetailByMap(projectId,
                milepostId);
        if (CollectionUtils.isEmpty(planDetailDtos)) {
            return result;
        }

        MultiValueMap<Long, MilepostDesignPlanDetailDto> detailMap = new LinkedMultiValueMap<>();
        List<MilepostDesignPlanDetailDto> details = new ArrayList<>();
        List<Date> deliveryTimeList = new ArrayList<>();
        List<String> pamCodeList = new ArrayList<>();
        List<String> wbsSummaryCodeList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto detail : planDetailDtos) {
            if (detail.getParentId() == -1L) {
                details.add(detail);
            } else {
                if (MATERIAL_CLASS.equals(detail.getMaterialClassification())) {
                    //统计需求量
                    BigDecimal totalNum = detail.getNumber().multiply(milepostDesignPlanDetailService.getParentSigmaById(detail.getParentId(), null));
                    detail.setTotalNum(totalNum);
                } else if ("外购物料".equals(detail.getMaterialCategory()) || "看板物料".equals(detail.getMaterialCategory())) {
                    //查询直接上级，计算采购需求数
                    List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                            milepostDesignPlanDetailExtMapper.selectTotalNumberById(detail.getId());
                    BigDecimal totalNumber = new BigDecimal(1);
                    for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : milepostDesignPlanDetailDtos) {
                        totalNumber = totalNumber.multiply(milepostDesignPlanDetailDto.getNumber()).setScale(0, BigDecimal.ROUND_HALF_UP);
                    }
                    detail.setTotalNum(totalNumber);
                } else {
                    List<MilepostDesignPlanDetail> detailList = milepostDesignPlanDetailService.selectByParentId(detail.getId());
                    if (CollectionUtils.isNotEmpty(detailList) || ("装配件".equals(detail.getMaterialCategory())) || ("虚拟件".equals(detail.getMaterialCategory()))) {
                        //totalNum不作处理
                    } else {
                        BigDecimal totalNum = detail.getNumber().multiply(milepostDesignPlanDetailService.getParentSigmaById(detail.getParentId(),
                                null));
                        detail.setTotalNum(totalNum);
                    }
                }
                detailMap.add(detail.getParentId(), detail);
            }

            Date deliveryTime = detail.getDeliveryTime();
            if (deliveryTime != null && !deliveryTimeList.contains(deliveryTime)) {
                deliveryTimeList.add(deliveryTime);
            }
            String pamCode = detail.getPamCode();
            if (StringUtils.isNotEmpty(pamCode) && !pamCodeList.contains(pamCode)) {
                pamCodeList.add(pamCode);
            }
            String wbsSummaryCode = detail.getWbsSummaryCode();
            if (StringUtils.isNotEmpty(wbsSummaryCode) && !wbsSummaryCodeList.contains(wbsSummaryCode)) {
                wbsSummaryCodeList.add(wbsSummaryCode);
            }
        }

        PurchaseMaterialRequirementExample example = new PurchaseMaterialRequirementExample();
        PurchaseMaterialRequirementExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        if (CollectionUtils.isNotEmpty(deliveryTimeList)) {
            criteria.andDeliveryTimeIn(deliveryTimeList);
        }
        if (CollectionUtils.isNotEmpty(pamCodeList)) {
            criteria.andPamCodeIn(pamCodeList);
        }
        if (CollectionUtils.isNotEmpty(wbsSummaryCodeList)) {
            criteria.andWbsSummaryCodeIn(wbsSummaryCodeList);
        }
        criteria.andDeletedFlagEqualTo(false);
        List<PurchaseMaterialRequirement> requirementList = purchaseMaterialRequirementMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(requirementList)) {
            Map<String, String> requirementMap = new HashMap<>();
            for (PurchaseMaterialRequirement requirement : requirementList) {
                requirementMap.put(getRequirementKey(requirement.getDeliveryTime(), requirement.getPamCode(), requirement.getWbsSummaryCode()),
                        requirement.getRequirementCode());
            }

            for (MilepostDesignPlanDetailDto detailDto : details) {
                String requirementKey = getRequirementKey(detailDto.getDeliveryTime(), detailDto.getPamCode(), detailDto.getWbsSummaryCode());
                detailDto.setPurchaseMaterialRequirementReceiptsCode(requirementMap.get(requirementKey));
            }

            for (Long id : detailMap.keySet()) {
                List<MilepostDesignPlanDetailDto> designPlanDetailDtoList = detailMap.get(id);
                for (MilepostDesignPlanDetailDto detailDto : designPlanDetailDtoList) {
                    String requirementKey = getRequirementKey(detailDto.getDeliveryTime(), detailDto.getPamCode(), detailDto.getWbsSummaryCode());
                    detailDto.setPurchaseMaterialRequirementReceiptsCode(requirementMap.get(requirementKey));
                }
                detailMap.put(id, designPlanDetailDtoList);
            }
        }

        //树结构组合
        List<MilepostDesignPlanDetailDto> resultDetails = new ArrayList<>();
        this.buildDesignPlanDetailTree(resultDetails, details, detailMap, null);
        result.setDesignPlanDetailDtos(resultDetails);
        return result;
    }


    @Override
    public MilepostDesignPlanDetailApprovedVO findPurchaseApprovalingDesignPlanDetail(Long submitId) {
        Assert.notNull(submitId, "提交ID不能为空");
        MilepostDesignPlanDetailApprovedVO result = new MilepostDesignPlanDetailApprovedVO();
        //获取发布的所有详设物料
        MilepostDesignPlanDto planDetailDtos = milepostDesignPlanPurchaseRecordService.getDetailByConfirmId(submitId);
        if (planDetailDtos != null && ListUtils.isNotEmpty(planDetailDtos.getDesignPlanDetailDtos())) {
            MultiValueMap<Long, MilepostDesignPlanDetailDto> detailMap = new LinkedMultiValueMap<>();
            List<MilepostDesignPlanDetailDto> details = new ArrayList<>();
            //分层缓存数据
            getDesignPlanDetailMap(planDetailDtos.getDesignPlanDetailDtos(), detailMap, details);
            //树结构组合
            List<MilepostDesignPlanDetailDto> resultDetails = new ArrayList<>();
            this.buildDesignPlanDetailTree(resultDetails, details, detailMap, null);
            result.setDesignPlanDetailDtos(resultDetails);
            ProjectDto projectDto = projectBusinessService.findById(planDetailDtos.getProjectId());
            if (projectDto != null) {
                result.setProjectCode(projectDto.getCode());
            }
        }
        return result;
    }

    @Override
    public MilepostDesignPlanDetailApprovedVO findApprovalingDesignPlanDetail(Long submitId) {
        Assert.notNull(submitId, "提交ID不能为空");
        MilepostDesignPlanDetailApprovedVO result = new MilepostDesignPlanDetailApprovedVO();
        //获取发布的所有详设物料
        MilepostDesignPlanDto planDetailDtos = getDetailBySubmitId(submitId);
        MilepostDesignPlanDetailSubmitHistoryExample historyExample =
                new MilepostDesignPlanDetailSubmitHistoryExample();
        historyExample.createCriteria().andProjectWbsReceiptsIdEqualTo(submitId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<MilepostDesignPlanDetailSubmitHistory> histories =
                milepostDesignPlanDetailSubmitHistoryService.selectByExample(historyExample);
        if (planDetailDtos != null && ListUtils.isNotEmpty(planDetailDtos.getDesignPlanDetailDtos())) {
            ProjectDto projectDto = projectBusinessService.findById(planDetailDtos.getProjectId());
            result.setProjectCode(projectDto.getCode());
            MultiValueMap<Long, MilepostDesignPlanDetailDto> detailMap = new LinkedMultiValueMap<>();
            List<MilepostDesignPlanDetailDto> details = new ArrayList<>();
            //分层缓存数据
            getDesignPlanDetailMap(planDetailDtos.getDesignPlanDetailDtos(), detailMap, details);
            //树结构组合
            List<MilepostDesignPlanDetailDto> resultDetails = new ArrayList<>();
            this.buildDesignPlanDetailTree(resultDetails, details, detailMap, null);
            result.setDesignPlanDetailDtos(resultDetails);
        } else if (ListUtils.isNotEmpty(histories)) {
            ProjectDto projectDto = new ProjectDto();
            if (ObjectUtil.isNotNull(histories.get(0).getProjectId())) {
                projectDto = projectBusinessService.findById(histories.get(0).getProjectId());
            }
            result.setProjectCode(projectDto.getCode());
            MultiValueMap<Long, MilepostDesignPlanDetailDto> detailMap = new LinkedMultiValueMap<>();
            List<MilepostDesignPlanDetailDto> details = new ArrayList<>();
            List<MilepostDesignPlanDetailDto> historyToDetailDto = BeanConverter.copy(histories,
                    MilepostDesignPlanDetailDto.class);

            List<MilepostDesignPlanDetailDto> topLevelDesignPlanDetailDtos =
                    filterTopParent(historyToDetailDto);
            topLevelDesignPlanDetailDtos.removeAll(Collections.singleton(null));
            if (planDetailDtos != null) {
                planDetailDtos.setDesignPlanDetailDtos(buildMilepostDesignPlanDetailTree(historyToDetailDto,
                        topLevelDesignPlanDetailDtos));
                //分层缓存数据
                getDesignPlanDetailMap(planDetailDtos.getDesignPlanDetailDtos(), detailMap, details);
            }
            //树结构组合
            List<MilepostDesignPlanDetailDto> resultDetails = new ArrayList<>();
            this.buildDesignPlanDetailTree(resultDetails, details, detailMap, null);
            result.setDesignPlanDetailDtos(resultDetails);
        }
        return result;
    }

    public void getDesignPlanDetailMap(List<MilepostDesignPlanDetailDto> planDetailDtos, MultiValueMap<Long,
            MilepostDesignPlanDetailDto> detailMap, List<MilepostDesignPlanDetailDto> details) {
        for (MilepostDesignPlanDetailDto detail : planDetailDtos) {
            if (Objects.equals(detail.getParentId(), new Long(-1))) {
                details.add(detail);
                getDesignPlanDetailMap(detail.getSonDtos(), detailMap, details);
            } else {
                if (MATERIAL_CLASS.equals(detail.getMaterialClassification())) {
                    //统计需求量
                    BigDecimal totalNum =
                            detail.getNumber().multiply(milepostDesignPlanDetailService.getParentSigmaById(detail.getParentId(), null));
                    detail.setTotalNum(totalNum);
                }
                detailMap.add(detail.getParentId(), detail);
                if (ListUtils.isNotEmpty(detail.getSonDtos())) {
                    getDesignPlanDetailMap(detail.getSonDtos(), detailMap, details);
                }
            }
        }
    }

    @Override
    public ResponseMap getMilepostDesignPlanPurchaseApp(Long id) {
        MilepostDesignPlanDto milepostDesignPlanDto = this.getDetailBySubmitId(id);
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<AduitAtta> fileList = new ArrayList<>();
        if (ObjectUtils.isEmpty(milepostDesignPlanDto)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("数据为空");
        } else {
            //移动审批头数据
            ProjectDto projectDto = projectBusinessService.findById(milepostDesignPlanDto.getProjectId());
            headMap.put("isAdvance", "是");
            if (!ObjectUtils.isEmpty(projectDto)) {
                headMap.put("name", projectDto.getName() == null ? " " : projectDto.getName());
                headMap.put("code", projectDto.getCode() == null ? " " : projectDto.getCode());
                headMap.put("managerName", projectDto.getManagerName() == null ? " " : projectDto.getManagerName());
            }
            List<MilepostDesignPlanSubmitRecordDto> submitRecordDtos = milepostDesignPlanDto.getSubmitRecordDtos();
            if (CollectionUtils.isNotEmpty(submitRecordDtos)) {
                headMap.put("uploadPath", submitRecordDtos.get(0).getUploadPath() == null ? " " :
                        submitRecordDtos.get(0).getUploadPath());
            }
            List<CtcAttachmentDto> attachmentDtos = milepostDesignPlanDto.getAttachmentDtos();
            if (CollectionUtils.isNotEmpty(attachmentDtos)) {
                for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                    AduitAtta aduitAtta = new AduitAtta();
                    aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" :
                            String.valueOf(attachmentDto.getAttachId()));
                    aduitAtta.setFileName(attachmentDto.getFileName() == null ? attachmentDto.getAttachName() :
                            attachmentDto.getFileName());
                    aduitAtta.setFileSize(attachmentDto.getFileSize().toString());
                    fileList.add(aduitAtta);
                }
            }
            //发布明细附件
            CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
            attachmentQuery.setModule(CtcAttachmentModule.PROJECT_SUBMIT_DELIVERY_DETAIL.code());
            attachmentQuery.setModuleId(id);
            attachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
            if (CollectionUtils.isNotEmpty(attachmentDtos)) {
                CtcAttachmentDto attachmentDto = attachmentDtos.get(0);//驳回/撤回会重新生成，取最新的一个即可
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" :
                        String.valueOf(attachmentDto.getAttachId()));
                aduitAtta.setFileName(attachmentDto.getFileName() == null ? attachmentDto.getAttachName() :
                        attachmentDto.getFileName());
                aduitAtta.setFileSize(attachmentDto.getFileSize().toString());
                fileList.add(aduitAtta);
            }
            responseMap.setStatus("success");
            responseMap.setMsg("success");
            responseMap.setHeadMap(headMap);
            responseMap.setFileList(fileList);
        }
        return responseMap;
    }

    @Override
    public ResponseMap getPurchaseInAdvanceApp(Long id) {
        MilepostDesignPlanDto milepostDesignPlanDto = milepostDesignPlanPurchaseRecordService.getDetailByConfirmId(id);
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<AduitAtta> fileList = new ArrayList<>();
        if (ObjectUtils.isEmpty(milepostDesignPlanDto)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("数据为空");
        } else {
            //移动审批头数据
            //name,code,type,previewFlag,managerName,startDate,endDate
            ProjectDto projectDto = projectBusinessService.findById(milepostDesignPlanDto.getProjectId());
            if (!ObjectUtils.isEmpty(projectDto)) {
                headMap.put("name", projectDto.getName() == null ? " " : projectDto.getName());
                headMap.put("code", projectDto.getCode() == null ? " " : projectDto.getCode());
                headMap.put("type", projectDto.getTypeName() == null ? " " : projectDto.getTypeName());
                headMap.put("previewFlag", projectDto.getPreviewFlag() ? "是" : "否");
                headMap.put("managerName", projectDto.getManagerName() == null ? " " : projectDto.getManagerName());
                headMap.put("startDate", DateUtil.format(projectDto.getStartDate(), DateUtil.DATE_PATTERN));
                headMap.put("endDate", DateUtil.format(projectDto.getEndDate(), DateUtil.DATE_PATTERN));
            }
            //附件
            if (StringUtils.isNotEmpty(milepostDesignPlanDto.getAttachId())) {
                List<Long> attachIds =
                        Arrays.stream(milepostDesignPlanDto.getAttachId().split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                for (Long o : attachIds) {
                    AduitAtta aduitAtta = new AduitAtta();
                    aduitAtta.setFdId(o + "");
                    fileList.add(aduitAtta);
                }
            }
            //提前采购明细
            CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
            attachmentQuery.setModule(CtcAttachmentModule.PROJECT_PURCHASE_INADVANCE_ITEM.code());
            attachmentQuery.setModuleId(id);
            List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
            if (CollectionUtils.isNotEmpty(attachmentDtos)) {
                for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                    AduitAtta aduitAtta = new AduitAtta();
                    aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" :
                            String.valueOf(attachmentDto.getAttachId()));
                    aduitAtta.setFileName(attachmentDto.getFileName() == null ? attachmentDto.getAttachName() :
                            attachmentDto.getFileName());
                    aduitAtta.setFileSize(attachmentDto.getFileSize().toString());
                    fileList.add(aduitAtta);
                }
            }
            responseMap.setStatus("success");
            responseMap.setMsg("success");
            responseMap.setHeadMap(headMap);
            responseMap.setFileList(fileList);
        }
        return responseMap;
    }

    @Override
    public ResponseMap getMilepostDesignPlanNoPurchaseApp(Long id) {
        MilepostDesignPlanDto milepostDesignPlanDto = this.getDetailBySubmitId(id);
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<AduitAtta> fileList = new ArrayList<>();
        if (ObjectUtils.isEmpty(milepostDesignPlanDto)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("数据为空");
        } else {
            ProjectDto projectDto = projectBusinessService.findById(milepostDesignPlanDto.getProjectId());
            headMap.put("isAdvance", "否");
            if (!ObjectUtils.isEmpty(projectDto)) {
                headMap.put("name", projectDto.getName() == null ? " " : projectDto.getName());
                headMap.put("code", projectDto.getCode() == null ? " " : projectDto.getCode());
                headMap.put("managerName", projectDto.getManagerName() == null ? " " : projectDto.getManagerName());
            }
            List<MilepostDesignPlanSubmitRecordDto> submitRecordDtos = milepostDesignPlanDto.getSubmitRecordDtos();
            if (CollectionUtils.isNotEmpty(submitRecordDtos)) {
                headMap.put("uploadPath", submitRecordDtos.get(0).getUploadPath() == null ? " " :
                        submitRecordDtos.get(0).getUploadPath());
            }
            List<CtcAttachmentDto> attachmentDtos = milepostDesignPlanDto.getAttachmentDtos();
            if (CollectionUtils.isNotEmpty(attachmentDtos)) {
                for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                    AduitAtta aduitAtta = new AduitAtta();
                    aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" :
                            String.valueOf(attachmentDto.getAttachId()));
                    aduitAtta.setFileName(attachmentDto.getFileName() == null ? attachmentDto.getAttachName() :
                            attachmentDto.getFileName());
                    aduitAtta.setFileSize(attachmentDto.getFileSize().toString());
                    fileList.add(aduitAtta);
                }
            }
            //发布明细附件
            CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
            attachmentQuery.setModule(CtcAttachmentModule.PROJECT_SUBMIT_DELIVERY_DETAIL.code());
            attachmentQuery.setModuleId(id);
            attachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
            if (CollectionUtils.isNotEmpty(attachmentDtos)) {
                CtcAttachmentDto attachmentDto = attachmentDtos.get(0);//驳回/撤回会重新生成，取最新的一个即可
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" :
                        String.valueOf(attachmentDto.getAttachId()));
                aduitAtta.setFileName(attachmentDto.getFileName() == null ? attachmentDto.getAttachName() :
                        attachmentDto.getFileName());
                aduitAtta.setFileSize(attachmentDto.getFileSize().toString());
                fileList.add(aduitAtta);
            }
            responseMap.setStatus("success");
            responseMap.setMsg("success");
            responseMap.setHeadMap(headMap);
            responseMap.setFileList(fileList);
        }
        return responseMap;
    }

    /**
     * 组合树
     */
    private void buildDesignPlanDetailTree(List<MilepostDesignPlanDetailDto> resultDetails,
                                           List<MilepostDesignPlanDetailDto> details,
                                           MultiValueMap<Long, MilepostDesignPlanDetailDto> detailMap,
                                           String parentIndex) {
        if (!CollectionUtils.isEmpty(details)) {
            int sonIndex = 1;
            for (MilepostDesignPlanDetailDto detail : details) {
                String serialNumber = this.buildSerialNumber(parentIndex, sonIndex);
                detail.setSerialNumber(serialNumber);
                final UserInfo createBy = CacheDataUtils.findUserById(detail.getCreateBy());
                final UserInfo updateBy = CacheDataUtils.findUserById(detail.getUpdateBy());
                if (createBy != null) {
                    detail.setCreateName(createBy.getName());
                }
                if (updateBy != null) {
                    detail.setUpdateName(updateBy.getName());
                }
                resultDetails.add(detail);
                List<MilepostDesignPlanDetailDto> sonDetails = detailMap.get(detail.getId());
                if (!CollectionUtils.isEmpty(sonDetails)) {
                    this.buildDesignPlanDetailTree(resultDetails, sonDetails, detailMap, serialNumber);
                }
                ++sonIndex;
            }
        }
    }

    /**
     * 构建序号
     */
    private String buildSerialNumber(String parentIndex, int sonIndex) {
        if (StringUtils.isNotBlank(parentIndex)) {
            return parentIndex + "." + sonIndex;
        }
        return sonIndex + "";
    }


    /**
     * 检查提交的模组是否已经在进度确认中
     */
    private void checkMilepostDesignPlanDetail(MilepostDesignPlanDto dto) {
        Long projectId = dto.getProjectId();
        MilepostDesignPlanSubmitRecordDto submitRecordDto = dto.getSubmitRecordDto();
        ProjectWbsReceiptsDto receiptsDto = dto.getReceiptsDto();
        if (submitRecordDto != null) {
            List<MilepostDesignPlanDetailDto> sonDtos = submitRecordDto.getDesignPlanDetailDtos();
            if (ListUtils.isNotEmpty(sonDtos)) {
                List<Long> parentIdsOfNewNodes = findParentIdsOfNewNodes(sonDtos);
                if (ListUtils.isNotEmpty(parentIdsOfNewNodes)) {
                    parentIdsOfNewNodes.forEach(parnetId -> {
                        List<MilepostDesignPlanDetail> parentRecordsInApprovalList = milepostDesignPlanDetailExtMapper.selectParentRecordsInApprovalById(parnetId);
                        if (ListUtils.isNotEmpty(parentRecordsInApprovalList) && parentRecordsInApprovalList.get(0).getModuleStatus() != null) {
                            for (MilepostDesignPlanDetail milepostDesignPlanDetail : parentRecordsInApprovalList) {
                                Integer moduleStatus = milepostDesignPlanDetail.getModuleStatus();
                                if (Objects.equals(moduleStatus, MilepostDesignPlanDetailModelStatus.CONFIRMING.code())) {
                                    String errorCode = "模组[" + milepostDesignPlanDetail.getMaterielDescr() + "]正在进度确认中，不能再进行变更";
                                    throw new ApplicationBizException(errorCode);
                                }
                            }
                        }
                    });
                }
            }
        } else if (null != receiptsDto) {
            if (StringUtils.isNotEmpty(receiptsDto.getBatchUploadPathIds())) {
                List<Long> designIds =
                        Arrays.stream(receiptsDto.getBatchUploadPathIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(designIds)) {
                    MilepostDesignPlanDetailSubmitHistoryExample historyExample = new MilepostDesignPlanDetailSubmitHistoryExample();
                    historyExample.createCriteria().andDesignPlanDetailIdIn(designIds).andDeletedFlagEqualTo(Boolean.FALSE);
                    List<MilepostDesignPlanDetailSubmitHistory> milepostDesignPlanDetailSubmitHistories =
                            milepostDesignPlanDetailSubmitHistoryService.selectByExample(historyExample);
                    if (ListUtils.isNotEmpty(milepostDesignPlanDetailSubmitHistories)) {
                        List<Long> receiptIds =
                                milepostDesignPlanDetailSubmitHistories.stream().map(MilepostDesignPlanDetailSubmitHistory::getProjectWbsReceiptsId)
                                        .distinct().collect(Collectors.toList());
                        ProjectWbsReceiptsExample receiptsExample = new ProjectWbsReceiptsExample();
                        ProjectWbsReceiptsExample.Criteria submitCriteria = receiptsExample.createCriteria();
                        submitCriteria.andIdIn(receiptIds).andRequirementTypeEqualTo(RequirementTypeEnum.DESIGN_PLAN_SUBMIT.getCode())
                                .andRequirementStatusIn(Arrays.asList(RequirementStatusEnum.PENDING.getCode(), RequirementStatusEnum.DRAFT.getCode(),
                                        RequirementStatusEnum.REFUSE.getCode(), RequirementStatusEnum.TODO.getCode()));
                        if (receiptsDto.getId() != null) {
                            submitCriteria.andIdNotEqualTo(receiptsDto.getId());
                        }
                        List<ProjectWbsReceipts> projectWbsReceipts = projectWbsReceiptsMapper.selectByExampleWithBLOBs(receiptsExample);
                        if (ListUtils.isNotEmpty(projectWbsReceipts)) {
                            throw new ApplicationBizException("还有未生效的详细设计单据，不能进行详细设计发布");
                        }
                    }
                }
            } else {
                throw new ApplicationBizException("当前没有详设进行发布");
            }

            // 柔性详设发布、详设变更，提交审批时：新增数据 + 外购物料或看板物料  的"是否外包"或"是否急件"为空，不允许提交
            Project project = projectService.selectByPrimaryKey(projectId);
            if (Boolean.TRUE.equals(project.getWbsEnabled())) {
                checkExtIsAndDispatchIs(receiptsDto.getDesignPlanDetailDtos());
            }
        }
    }

    private void checkExtIsAndDispatchIs(List<MilepostDesignPlanDetailDto> designPlanDetailDtos) {
        if (CollectionUtils.isEmpty(designPlanDetailDtos)) {
            return;
        }
        for (MilepostDesignPlanDetailDto dto : designPlanDetailDtos) {
            if (Boolean.TRUE.equals(dto.get_isNew()) && (Objects.equals("外购物料", dto.getMaterialCategory()) || Objects.equals("看板物料",
                    dto.getMaterialCategory()))) {
                String pamCode = dto.getPamCode();
                Guard.notNull(dto.getExtIs(), String.format("PAM编码：%s的是否外包不能为空", pamCode));
                Guard.notNull(dto.getDispatchIs(), String.format("PAM编码：%s的是否急件不能为空", pamCode));
            }
            checkExtIsAndDispatchIs(dto.getSonDtos());
        }
    }


    @Override
    public List<MilepostDesignPlanDetailDto> getMilepostPlanDesignByTicketTasksCode(Long id) {
        //根据工单任务号查询该工单任务号对应的详细设计下所有符合条件的子详细设计信息
        ArrayList<String> materialCategoryList = new ArrayList<>();
        materialCategoryList.add("外购物料");
        materialCategoryList.add("看板物料");
        return milepostDesignPlanDetailExtMapper.getMilepostPlanDesignByTicketTasksCode(id, materialCategoryList);
    }

    @Override
    public ResponseMap setMilepostPlanDesignMRP(Long id) {
        logger.info("详细设计id：" + id);
        ResponseMap responseMap = new ResponseMap();
        MilepostDesignPlanDetail detail = milepostDesignPlanDetailMapper.selectByPrimaryKey(id);
        if (null != detail) {
            if ("外购物料".equals(detail.getMaterialCategory()) && !detail.getDeletedFlag()) {
                setMRP(id);
            } else {
                mrpChild(id);
            }
            responseMap.setStatus("0");
            responseMap.setMsg("success");
        } else {
            responseMap.setStatus("1");
            responseMap.setMsg("详细设计不存在");
        }
        logger.info("mrp明细：id:" + id + "===" + responseMap.getMsg());
        return responseMap;
    }

    @Override
    public void setMRP(Long id) {
        logger.info("MRP详细设计id：" + id);

        // 获取详设信息，检查是否为需求发布前的变更
        MilepostDesignPlanDetailDto designDetail = milepostDesignPlanDetailService.getById(id);

        // 判断是否为需求发布前的变更
        boolean isBeforeRequirementPublish = false;
        if (designDetail != null) {
            // 如果generateRequirement为null或者等于NOT_PRODUCED，则表示需求未发布
            isBeforeRequirementPublish = designDetail.getGenerateRequirement() == null ||
                    Objects.equals(designDetail.getGenerateRequirement(),
                            DesignPlanDetailGenerateRequire.NOT_PRODUCED.code());

            logger.info("详设id: {}, 是否为需求发布前变更: {}, generateRequirement: {}",
                    id, isBeforeRequirementPublish, designDetail.getGenerateRequirement());
        }

        // 如果是需求发布前的变更，则不写入milepost_design_plan_material_detail表
        if (isBeforeRequirementPublish) {
            logger.info("详设id: {}为需求发布前的变更，跳过写入MRP表", id);
            // 仍然需要处理子节点
            mrpChild(id);
            return;
        }

        //获取mrp是否已有记录
        DesignPlanMaterialDetail detail = designPlanMaterialDetailService.selectByDesignPlanDetailId(id);
        //获取当前详细信息
        DesignPlanMaterialDetailDTO dto = designPlanMaterialDetailService.getByDesignPlanDetailId(id);

        if (null != dto) {
            // 根据项目id查询此项目类型配置：需求传递MRP，如果配置为false则无需计算MRP需求
            Boolean requirementDeliverMrp = projectService.queryRequirementDeliverMrp(dto.getProjectId());
            if (requirementDeliverMrp == null || requirementDeliverMrp == false) {
                logger.info("此详细设计对应项目类型设置无需需求传递MRP");
                //删除需求
                /*if (null != detail) {
                    detail.setDeletedFlag(Boolean.TRUE);
                    designPlanMaterialDetailMapper.updateByPrimaryKeySelective(detail);
                }*/
                return;
            }

            //需求数量
            BigDecimal number = designPlanMaterialDetailService.selectDetailNumber(id);
            if (null != number) {
                dto.setNumber(number);
            }

            //获取采购数量
            BigDecimal getNumber =
                    designPlanMaterialDetailService.selectTicketTasksGetNumber(dto.getDesignPlanDetailId());
            if (null == getNumber) {
                getNumber = BigDecimal.ZERO;
            }
            //获取退料数量
            BigDecimal returnNumber =
                    designPlanMaterialDetailService.selectTicketTasksReturnNumber(dto.getDesignPlanDetailId());
            if (null == returnNumber) {
                returnNumber = BigDecimal.ZERO;
            }
            dto.setGetNumber(getNumber);
            dto.setReturnNumber(returnNumber);
            if (null != detail) {
                dto.setId(detail.getId());
                dto.setUpdateAt(new Date());
                dto.setDeletedFlag(Boolean.FALSE);
                dto.setAdvPurchFlag(Boolean.FALSE);
                designPlanMaterialDetailService.updateDesignPlanMaterialDetail(dto);
            } else {
                dto.setUpdateAt(new Date());
                dto.setDeletedFlag(Boolean.FALSE);
                dto.setAdvPurchFlag(Boolean.FALSE);
                designPlanMaterialDetailService.saveDesignPlanMaterialDetail(dto);
            }

        } else {//不是采购物料时删除
            if (null != detail) {
                //需求数量
                BigDecimal number = designPlanMaterialDetailService.selectDetailNumber(id);
                logger.info("需求数量：" + number);
                if (null != number) {
                    detail.setNumber(number);
                }

                //获取采购数量
                BigDecimal getNumber = designPlanMaterialDetailService.selectTicketTasksGetNumber(id);
                if (null == getNumber) {
                    getNumber = BigDecimal.ZERO;
                }
                //获取退料数量
                BigDecimal returnNumber = designPlanMaterialDetailService.selectTicketTasksReturnNumber(id);
                if (null == returnNumber) {
                    returnNumber = BigDecimal.ZERO;
                }
                detail.setGetNumber(getNumber);
                detail.setReturnNumber(returnNumber);
                detail.setDeletedFlag(Boolean.TRUE);
                detail.setAdvPurchFlag(Boolean.FALSE);
                designPlanMaterialDetailMapper.updateByPrimaryKeySelective(detail);
            }
        }
        mrpChild(id);
    }

    public void mrpChild(Long id) {
        logger.info("详细设计mrp获取子节点id：" + id);
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andStatusIsNotNull()// 详细设计status为空的，不做处理
                .andParentIdEqualTo(id);
        //查询子节点
        List<MilepostDesignPlanDetail> designPlanDetailList = milepostDesignPlanDetailMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(designPlanDetailList)) {
            for (MilepostDesignPlanDetail p : designPlanDetailList) {
                if ("外购物料".equals(p.getMaterialCategory())) {
                    setMRP(p.getId());
                } else {
                    mrpChild(p.getId());
                    //采购件变为看板物料，已确认详细设计，需要将’需求明细表’中的数据清理掉。
                    //获取mrp是否已有记录
                    DesignPlanMaterialDetail detail = designPlanMaterialDetailService.selectByDesignPlanDetailId(id);
                    if (null != detail) {
                        detail.setDeletedFlag(Boolean.TRUE);
                        designPlanMaterialDetailMapper.updateByPrimaryKeySelective(detail);
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseMap changeDeliveryTime(UpdateDeliveryTimeDto dto) {

        ResponseMap responseMap = new ResponseMap();
        List<Long> ids = dto.getId();
        Date time = DateUtil.parseDate(dto.getDeliveryTime());
        for (Long id : ids) {
            MilepostDesignPlanDetail mdp = milepostDesignPlanDetailMapper.selectByPrimaryKey(id);
            if (null != mdp) {
                mdp.setDeliveryTime(time);
                mdp.setUpdateAt(new Date());
                mdp.setUpdateBy(SystemContext.getUserId());
                logger.info("*************** MilepostDesignPlanServiceImpl.changeDeliveryTime入参1:{} ***************", JSONObject.toJSONString(mdp));
                milepostDesignPlanDetailMapper.updateByPrimaryKey(mdp);
                //同时修改子节点采购件
                MilepostDesignPlanDetailExample example3 = new MilepostDesignPlanDetailExample();
                example3.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andParentIdEqualTo(mdp.getId()).andMaterialCategoryEqualTo(MaterialCategoryEnum.PURCHASED_PARTS.msg());
                List<MilepostDesignPlanDetail> detailson = milepostDesignPlanDetailMapper.selectByExample(example3);
                if (ListUtils.isNotEmpty(detailson)) {
                    logger.info("*************** MilepostDesignPlanServiceImpl.changeDeliveryTime入参2:{} ***************",
                            JSONObject.toJSONString(detailson));
                    for (MilepostDesignPlanDetail son : detailson) {
                        son.setDeliveryTime(time);
                        milepostDesignPlanDetailMapper.updateByPrimaryKeySelective(son);
                    }
                }
            }
            //获取mrp记录
            DesignPlanMaterialDetail mrp = designPlanMaterialDetailMapper.selectByPrimaryKey(id);
            if (null != mrp) {
                mrp.setDeliveryTime(time);
                mrp.setUpdateAt(new Date());
                mrp.setUpdateBy(SystemContext.getUserId());
                designPlanMaterialDetailMapper.updateByPrimaryKey(mrp);
            }

        }

        responseMap.setStatus("0");
        responseMap.setMsg("success");
        return responseMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseMap updateTickDeliveryTimeDto(List<UpdateTickDeliveryTimeDto> dto) {
        ResponseMap responseMap = new ResponseMap();
        for (UpdateTickDeliveryTimeDto d : dto) {
            Date time = DateUtil.parseDate(d.getDeliveryTime());
            // TODO 工单的计划开始日期的值同步给工单下物料的“计划交货日期”
            TicketTasksDetailExample example = new TicketTasksDetailExample();
            example.createCriteria().andIdEqualTo(d.getId());
            List<TicketTasksDetail> list = ticketTasksDetailMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(list)) {
                for (TicketTasksDetail t : list) {
                    t.setPlanDeliveryTime(time);
                    ticketTasksDetailMapper.updateByPrimaryKeySelective(t);
                    //修改详设
                    ticketTasksService.updateDesignPlanDetailDeliveryTime(t.getMilepostDesignDetailId(), time);
                    //修改mrp记录
                    if (null != t.getMilepostDesignDetailId()) {
                        DesignPlanMaterialDetail mrp =
                                designPlanMaterialDetailMapper.selectByPrimaryKey(t.getMilepostDesignDetailId());
                        if (null != mrp) {
                            mrp.setDeliveryTime(time);
                            mrp.setUpdateAt(new Date());
                            mrp.setUpdateBy(SystemContext.getUserId());
                            designPlanMaterialDetailMapper.updateByPrimaryKey(mrp);
                        }
                    }

                }
            }

        }
        responseMap.setStatus("0");
        responseMap.setMsg("success");
        return responseMap;
    }

    @Override
    public List<MrpSum> getMRP() {
        List<DesignPlanMaterialDetailDTO> list = designPlanMaterialDetailService.selectAllJoinProject(null);
        List<MrpSum> mrp = new ArrayList<>();
        for (DesignPlanMaterialDetailDTO d : list) {
            //需求数量
            BigDecimal need = d.getNumber();
            //已领数量
            BigDecimal getNumber = d.getGetNumber().subtract(d.getReturnNumber());
            //需求数量等于领料数量排除
            if (need.compareTo(getNumber) != 0 && null != d.getErpCode()) {
                //是否前面存在过--标识
                boolean had = false;
                for (MrpSum m : mrp) {
                    //前面已存在--做叠加
                    if (m.getOrganizationId() == d.getOrganizationId() && d.getProjectCode().equals(m.getProject()) && d.getPamCode().equals(m.getPamCode()) && d.getErpCode().equals(m.getErpCode())) {
                        m.setNumber(m.getNumber().add(need));
                        m.setGetNumber(m.getGetNumber().add(getNumber));
                        //已存在
                        had = true;
                        if (null != m.getDeliveryTime() && null != d.getDeliveryTime()) {
                            Date deliveryTime = DateUtil.parseDate(m.getDeliveryTime());
                            //当前的交货时间比较后面的交付时间
                            int compareTo = deliveryTime.compareTo(d.getDeliveryTime());
                            //如果有多条记录，按照物料在该模组下，物料需求时间最早的需求时间(上一条的交货时间大于这条的交货时间)
                            if (compareTo > 0) {
                                String deliveryTimeAt = DateUtil.format(d.getDeliveryTime(),
                                        DateUtil.DATE_INTEGER_PATTERN);
                                m.setCreateTime(deliveryTimeAt);
                                //如果一个模组下有多个同样的物料，取最早物料交货时间，对应的需求创建时间
                                String createAt = DateUtil.format(d.getCreateAt(), DateUtil.DATE_INTEGER_PATTERN);
                                m.setCreateTime(createAt);
                                //UserInfo u = CacheDataUtils.findUserById(d.getCreateBy());
                                //if(null != u){
                                m.setCreateBy(d.getCreateBy());
                                // }
                            }
                            Date lastUpdateAt = DateUtil.parseDate(m.getUpdateTime());
                            //当前的交货时间比较后面的交付时间
                            int lastUpdate = lastUpdateAt.compareTo(d.getDeliveryTime());
                            //最后的交货时间
                            if (lastUpdate < 0) {
                                String lastUpdateTime = DateUtil.format(d.getDeliveryTime(),
                                        DateUtil.DATE_INTEGER_PATTERN);
                                m.setUpdateTime(lastUpdateTime);
                            }

                        } else {
                            String deliveryTime = DateUtil.format(d.getDeliveryTime(), DateUtil.DATE_INTEGER_PATTERN);
                            m.setDeliveryTime(deliveryTime);
                        }
                    }
                }
                //不存在则新建
                if (!had) {
                    MrpSum mp = new MrpSum();
                    mp.setProject(d.getProjectCode());
                    mp.setPamCode(d.getPamCode());
                    mp.setErpCode(d.getErpCode());
                    if (null != d.getCreateAt()) {
                        String createAt = DateUtil.format(d.getCreateAt(), DateUtil.DATE_INTEGER_PATTERN);
                        mp.setCreateTime(createAt);
                    }
                    if (null != d.getDeliveryTime()) {
                        String deliveryTime = DateUtil.format(d.getDeliveryTime(), DateUtil.DATE_INTEGER_PATTERN);
                        mp.setDeliveryTime(deliveryTime);
                        mp.setUpdateTime(deliveryTime);
                    }
                    mp.setNumber(d.getNumber());
                    mp.setGetNumber(getNumber);
                    //if(null != u){
                    mp.setCreateBy(d.getCreateBy());
                    //}
                    mp.setOrganizationId(d.getOrganizationId());
                    if (null != d.getOrganizationId()) {
                        mrp.add(mp);
                    }
                }
            }

        }
        return mrp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMrpByOrgAndPamCode(List<Long> projectIds, String pamCode) {
        detailExtMapper.updateMaterialTypeById(projectIds, pamCode);
        return Boolean.TRUE;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> findParentForPlanDetail(List<MilepostDesignPlanDetailDto> bottomPlanDetailDtos) {
        //取所有id
        List<Long> allIds =
                bottomPlanDetailDtos.stream().map(MilepostDesignPlanDetail::getId).collect(Collectors.toList());
        //因为不知道所选层级 所以把所有id的上级查询出来并包成一个list
        List<MilepostDesignPlanDetailDto> allDesignPlanDetailDtos = new ArrayList<>();
        for (MilepostDesignPlanDetailDto dto : bottomPlanDetailDtos) {
            if (ListUtils.isNotEmpty(dto.getSonDtos())) {
                allDesignPlanDetailDtos.addAll(dto.getSonDtos());
            }
            //如果allDesignPlanDetailDtos有数据 就把allIds中没有的数据加入allIds中
            if (ListUtils.isNotEmpty(allDesignPlanDetailDtos)) {
                allIds.addAll(allDesignPlanDetailDtos.stream().map(MilepostDesignPlanDetail::getId).filter(addDTOId -> !allIds.contains(addDTOId)).collect(Collectors.toList()));
            }

            MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();

            //如果有wbs则数据已填充 (后续可完善逻辑)
            if (StringUtils.isNotEmpty(dto.getWbsSummaryCode())) {
                allDesignPlanDetailDtos.add(dto);
            } else {
                allDesignPlanDetailDtos.addAll(milepostDesignPlanDetailExtMapper.selectList(dto));
            }
            //如果父id为空或者为-1则为最上级 或者已添加过的 就不查询最上级
            if ((null != dto.getParentId() && dto.getParentId() != -1) && !(allIds.contains(dto.getParentId()))) {
                getAllParentPlanDetailDTO(dto.getParentId(), allDesignPlanDetailDtos);
            }
        }

        //去重 （分别往上查可能会查到相同的详设）
        List<MilepostDesignPlanDetailDto> distinctDesignPlanDetailDtos = allDesignPlanDetailDtos.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MilepostDesignPlanDetail::getId))), ArrayList::new));

        // 找到最上层的父级详设
        List<MilepostDesignPlanDetailDto> topParentMilepostDesignPlanDetailDtos = filterTopParent(distinctDesignPlanDetailDtos);
        // 然后递归往下组成树结构
        topParentMilepostDesignPlanDetailDtos.removeAll(Collections.singleton(null));

        return setDesignTotalVal(buildMilepostDesignPlanDetailTree(distinctDesignPlanDetailDtos, topParentMilepostDesignPlanDetailDtos));
    }

    @Override
    public List<MilepostDesignPlanDetailDto> packagePlanDetail(List<MilepostDesignPlanDetailDto> designPlanDetailDtos) {
        logger.info("packagePlanDetail开始，designPlanDetailDtos：{}", JsonUtils.toString(designPlanDetailDtos));
        //取所有id
        List<Long> allIds = designPlanDetailDtos.stream().map(MilepostDesignPlanDetail::getId).collect(Collectors.toList());
        //因为不知道所选层级 所以把所有id的上级查询出来并包成一个list
        List<MilepostDesignPlanDetailDto> allDesignPlanDetailDtos = new ArrayList<>();
        for (MilepostDesignPlanDetailDto dto : designPlanDetailDtos) {
            dto = milepostDesignPlanDetailService.getById(dto.getId());
            //先查出传进来的数据的下级信息
            MilepostDesignPlanDetailExample detailExample = new MilepostDesignPlanDetailExample();
            detailExample.createCriteria().andParentIdEqualTo(dto.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            List<MilepostDesignPlanDetail> milepostDesignPlanDetails = milepostDesignPlanDetailMapper.selectByExample(detailExample);
            if (CollectionUtils.isNotEmpty(milepostDesignPlanDetails)) {
                List<MilepostDesignPlanDetailDto> sonPlanDetailDto = BeanConverter.convert(milepostDesignPlanDetails,
                        MilepostDesignPlanDetailDto.class);
                dto.setEditable(checkEditAble(sonPlanDetailDto));
                sonPlanDetailDto = sonPlanDetailDto.stream().filter(sonDto -> !allIds.contains(sonDto.getId())).collect(Collectors.toList());
                allDesignPlanDetailDtos.addAll(sonPlanDetailDto);
            } else {
                dto.setEditable(true);
            }
            //如果allDesignPlanDetailDtos有数据 就把allIds中没有的数据加入allIds中
            if (ListUtils.isNotEmpty(allDesignPlanDetailDtos)) {
                allIds.addAll(allDesignPlanDetailDtos.stream().map(MilepostDesignPlanDetail::getId).filter(addDTOId -> !allIds.contains(addDTOId))
                        .collect(Collectors.toList()));
            }
            allDesignPlanDetailDtos.add(dto);
            //如果父id为空或者为-1则为最上级 或者已添加过的 就不查询最上级
            if ((null == dto.getParentId() || dto.getParentId() != -1) && !(allIds.contains(dto.getParentId()))) {
                getAllParentPlanDetailDTO(dto.getParentId(), allDesignPlanDetailDtos);
            }
        }

        //去重
        List<MilepostDesignPlanDetailDto> distinctDesignPlanDetailDtos = allDesignPlanDetailDtos.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                        new TreeSet<>(Comparator.comparing(MilepostDesignPlanDetail::getId))), ArrayList::new));
        logger.info("开始distinctDesignPlanDetailDtos：{}", JsonUtils.toString(distinctDesignPlanDetailDtos));

        //判断是需求发布前还是需求发布后
        Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = new HashMap<>();
        List<Long> detailIds = CollectionUtils.isEmpty(distinctDesignPlanDetailDtos) ? new ArrayList<>()
                : distinctDesignPlanDetailDtos.stream().map(MilepostDesignPlanDetailDto::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(detailIds)) {
            List<ProjectWbsReceiptsDto> receiptsDtoList = projectWbsReceiptsService.selectByDetailIds(detailIds);
            receiptsDtoMap.putAll(CollectionUtils.isEmpty(receiptsDtoList) ? new HashMap<>()
                    : receiptsDtoList.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId)));
        }
        logger.info("receiptsDtoMap的detailIds：{}", JsonUtils.toString(detailIds));
        logger.info("receiptsDtoMap的keySet：{}", JsonUtils.toString(receiptsDtoMap.keySet()));
        for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : distinctDesignPlanDetailDtos) {
            // 找到该详设是否被确认
            milepostDesignPlanDetailDto.setHasConfirmed(CollectionUtils.isNotEmpty(receiptsDtoMap.get(milepostDesignPlanDetailDto.getId())));
            // requirementCode赋值
            ProjectWbsReceiptsDto param = new ProjectWbsReceiptsDto();
            param.setProjectId(milepostDesignPlanDetailDto.getProjectId());
            param.setPamCode(milepostDesignPlanDetailDto.getPamCode());
            ProjectWbsReceiptsDto projectWbsReceiptsDto = projectWbsReceiptsExtMapper.selectDto(param);
            if (!ObjectUtils.isEmpty(projectWbsReceiptsDto)) {
                milepostDesignPlanDetailDto.setRequirementCode(projectWbsReceiptsDto.getRequirementCode());
            }
        }

        List<MilepostDesignPlanDetailDto> topParentMilepostDesignPlanDetailDtos = filterTopParent(distinctDesignPlanDetailDtos);
        topParentMilepostDesignPlanDetailDtos.removeAll(Collections.singleton(null));
        logger.info("topParentMilepostDesignPlanDetailDtos：{}", JsonUtils.toString(topParentMilepostDesignPlanDetailDtos));

        List<MilepostDesignPlanDetailDto> resultDtoList =
                buildMilepostDesignPlanDetailTree(distinctDesignPlanDetailDtos, topParentMilepostDesignPlanDetailDtos);
        logger.info("packagePlanDetail结束，resultDtoList：{}", JsonUtils.toString(resultDtoList));
        return resultDtoList;
    }

    private Boolean checkEditAble(List<MilepostDesignPlanDetailDto> planDetailDto) {
        Boolean editAble = true;
        if (planDetailDto.stream().anyMatch(dto -> (CheckStatus.WBS_RECEIPTS_DRAFT.code().equals(dto.getStatus())
                || CheckStatus.WBS_RECEIPTS_PENDING.code().equals(dto.getStatus())
                || CheckStatus.WBS_RECEIPTS_CHECKING.code().equals(dto.getStatus())))) {
            planDetailDto.forEach(detailDto -> {
                if (CheckStatus.WBS_RECEIPTS_DRAFT.code().equals(detailDto.getStatus())
                        || CheckStatus.WBS_RECEIPTS_PENDING.code().equals(detailDto.getStatus())
                        || CheckStatus.WBS_RECEIPTS_CHECKING.code().equals(detailDto.getStatus())) {
                    detailDto.setEditable(false);
                } else {
                    detailDto.setEditable(true);
                }
            });
            editAble = false;
        } else {
            for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : planDetailDto) {
                MilepostDesignPlanDetailExample detailExample = new MilepostDesignPlanDetailExample();
                detailExample.createCriteria().andParentIdEqualTo(milepostDesignPlanDetailDto.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<MilepostDesignPlanDetail> milepostDesignPlanDetails =
                        milepostDesignPlanDetailMapper.selectByExample(detailExample);
                if (CollectionUtils.isNotEmpty(milepostDesignPlanDetails)) {
                    List<MilepostDesignPlanDetailDto> sonPlanDetailDto =
                            BeanConverter.convert(milepostDesignPlanDetails,
                                    MilepostDesignPlanDetailDto.class);
                    editAble = checkEditAble(sonPlanDetailDto);
                    milepostDesignPlanDetailDto.setEditable(editAble);
                } else {
                    milepostDesignPlanDetailDto.setEditable(true);
                }
            }
        }
        return editAble;
    }

    private List<MilepostDesignPlanDetailDto> getAllParentPlanDetailDTO(Long parentId, List<MilepostDesignPlanDetailDto> addDesignPlanDetailDtos) {
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andIdEqualTo(parentId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<MilepostDesignPlanDetail> milepostDesignPlanDetails = milepostDesignPlanDetailService.selectByExample(example);
        if (ListUtils.isNotEmpty(milepostDesignPlanDetails)) {
            MilepostDesignPlanDetailDto designPlanDetailDto = BeanConverter.copy(milepostDesignPlanDetails.get(0), MilepostDesignPlanDetailDto.class);
            addDesignPlanDetailDtos.add(designPlanDetailDto);
            if (designPlanDetailDto.getParentId() != null && designPlanDetailDto.getParentId() != -1) {
                getAllParentPlanDetailDTO(designPlanDetailDto.getParentId(), addDesignPlanDetailDtos);
            }
        }
        return addDesignPlanDetailDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectWbsReceiptsDto saveSubmitHistoryWithDetail(MilepostDesignPlanDto dto, Long userBy) {
        logger.info("saveSubmitHistoryWithDetail方法的dto：{}，userBy：{}", JSON.toJSONString(dto), userBy);
        Long projectId = dto.getProjectId();
        Guard.notNull(projectId, "柔性详设发布提交的项目id不能为空");
        ProjectWbsReceiptsDto receiptsDto = dto.getReceiptsDto();
        Guard.notNull(receiptsDto, "柔性详设发布提交的单据信息不能为空");

        projectBusinessService.checkProjectIsApprovaled(projectId);
        receiptsDto.setProjectId(projectId);
        UserInfo userInfo = CacheDataUtils.findUserById(userBy);
        if (userInfo != null) {
            receiptsDto.setProducerName(userInfo.getName());
        }
        receiptsDto.setProjectSubmit(Boolean.FALSE);//是否立项时上传
        if (receiptsDto.getId() != null) {
            projectWbsReceiptsService.deleteForSubmit(receiptsDto.getId());
        }
        // 设置前端传的表单模板id
        receiptsDto.setFormTemplateId(dto.getFormTemplateId());
        ProjectWbsReceiptsDto resultDto = milepostDesignPlanSubmitRecordService.saveSubmitHistoryWithRecursiveDetail(receiptsDto, userBy);

        //修改详设中间表数据
        if (null != dto.getMarkId()) {
            this.updateMilepostDesignPlanMiddle(dto);
        }

        return resultDto;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> getWbsLastLayer(Long projectId) {
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andProjectIdEqualTo(projectId)
                .andWbsLastLayerEqualTo(Boolean.TRUE)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<MilepostDesignPlanDetail> designPlanDetails = milepostDesignPlanDetailMapper.selectByExample(example);
        return BeanConverter.copy(designPlanDetails, MilepostDesignPlanDetailDto.class);
    }

    @Override
    public Boolean checkMaterialExist(MilepostDesignPlanDto dto) {
        Asserts.notEmpty(dto.getProjectId(), ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(dto.getErpCodeList(), ErrorCode.CTC_PROJECT_WBS_ERP_CODE_LIST_NOT_NULL);
        Boolean flag = true;
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andProjectIdEqualTo(dto.getProjectId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<MilepostDesignPlanDetail> designPlanDetails = milepostDesignPlanDetailMapper.selectByExample(example);
        if (ListUtil.isPresent(designPlanDetails)) {
            List<String> erpCodeExistList =
                    designPlanDetails.stream().map(MilepostDesignPlanDetail::getErpCode).collect(Collectors.toList());
            for (String erpCode : dto.getErpCodeList()) {
                if (!erpCodeExistList.contains(erpCode)) flag = false;
            }
        }
        return flag;
    }

    /**
     * 保存详细设计-只有wbs数据
     *
     * @param projectId
     * @return
     */
    @Override
    public void milepostDesignPlanJoinWbs(Long projectId) {
        Project project = projectMapper.selectByPrimaryKey(projectId);
        ProjectType projectType = projectTypeMapper.selectByPrimaryKey(project.getType());
        if (!Boolean.TRUE.equals(projectType.getWbsEnabled())) {
            return;
        }
        ProjectWbsBudgetSummaryExample projectWbsBudgetSummaryExample = new ProjectWbsBudgetSummaryExample();
        projectWbsBudgetSummaryExample.createCriteria().andProjectIdEqualTo(projectId).andSummaryTypeEqualTo(WbsBudgetFieldConstant.WBS);
        List<ProjectWbsBudgetSummary> sum = projectWbsBudgetSummaryMapper.selectByExample(projectWbsBudgetSummaryExample);

        List<ProjectWbsBudgetSummaryDto> summaries = BeanConverter.copy(sum, ProjectWbsBudgetSummaryDto.class);

        List<ProjectWbsBudgetSummaryDto> projectWbsBudgetSummaryDtos = buildWbsBudgetSummaryTree(summaries);
        logger.info("milepostDesignPlanJoinWbs方法的projectId：{}，projectWbsBudgetSummaryDtos：{}", projectId,
                JsonUtils.toString(projectWbsBudgetSummaryDtos));

        for (ProjectWbsBudgetSummaryDto forSaveSummary : projectWbsBudgetSummaryDtos) {
            MilepostDesignPlanDetailExample detailExample = new MilepostDesignPlanDetailExample();
            detailExample.createCriteria().andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(forSaveSummary.getSummaryCode()).andWbsLayerIsNotNull();
            List<MilepostDesignPlanDetail> milepostDesignPlanDetails = milepostDesignPlanDetailMapper.selectByExample(detailExample);
            if (ListUtils.isNotEmpty(milepostDesignPlanDetails)) {
                if (ListUtils.isNotEmpty(forSaveSummary.getSonDtos())) {
                    saveSonDesignPlanDetail(milepostDesignPlanDetails.get(0).getId(), forSaveSummary.getSonDtos(), project);
                }
            } else {
                MilepostDesignPlanDetailDto milepostDesignPlanDetail = new MilepostDesignPlanDetailDto();

                // ProjectSource:"项目来源:1-项目立项;2-预立项转正;3-商机立项;"
                milepostDesignPlanDetail.setSource("自动生成");
                milepostDesignPlanDetail.setLevel(project.getProjectLevel());
                milepostDesignPlanDetail.setWbsSummaryCode(forSaveSummary.getSummaryCode());
                milepostDesignPlanDetail.setNumber(new BigDecimal(1));
                milepostDesignPlanDetail.setWbsLayer(forSaveSummary.getWbsLayer());
                milepostDesignPlanDetail.setWbsConfirmFlag(forSaveSummary.getProjectDetailSelectFlag());
                milepostDesignPlanDetail.setParentId(-1L);
                milepostDesignPlanDetail.setDeletedFlag(false);
                milepostDesignPlanDetail.setWhetherModel(true);
                milepostDesignPlanDetail.setInit(false);
                milepostDesignPlanDetail.setModuleStatus(0);
                milepostDesignPlanDetail.setProjectId(project.getId());
                milepostDesignPlanDetail.setPamCode(materialExtService.generateMaterialPAMCode());
                milepostDesignPlanDetail.setWbsDescription(forSaveSummary.getDescription());
                milepostDesignPlanDetail.setMaterielDescr(forSaveSummary.getDescription());
                milepostDesignPlanDetail.setExt(Boolean.FALSE);
                if (Boolean.TRUE.equals(forSaveSummary.getProjectDetailSelectFlag())) {
                    milepostDesignPlanDetail.setWbsLastLayer(true);
                    milepostDesignPlanDetail.setWbsConfirmFlag(true);
                } else {
                    milepostDesignPlanDetail.setWbsLastLayer(false);
                    milepostDesignPlanDetail.setWbsConfirmFlag(false);
                }

                milepostDesignPlanDetailService.save(milepostDesignPlanDetail, null);
                if (ListUtils.isNotEmpty(forSaveSummary.getSonDtos())) {
                    saveSonDesignPlanDetail(milepostDesignPlanDetail.getId(), forSaveSummary.getSonDtos(), project);
                }
            }
        }
    }

    private void saveSonDesignPlanDetail(Long parentId, List<ProjectWbsBudgetSummaryDto> summaryDtos, Project project) {
        for (ProjectWbsBudgetSummaryDto forSaveSummary : summaryDtos) {
            MilepostDesignPlanDetailExample detailExample = new MilepostDesignPlanDetailExample();
            detailExample.createCriteria().andProjectIdEqualTo(project.getId()).andWbsSummaryCodeEqualTo(forSaveSummary.getSummaryCode()).andWbsLayerIsNotNull();
            List<MilepostDesignPlanDetail> milepostDesignPlanDetails = milepostDesignPlanDetailMapper.selectByExample(detailExample);
            if (ListUtils.isNotEmpty(milepostDesignPlanDetails)) {
                if (ListUtils.isNotEmpty(forSaveSummary.getSonDtos())) {
                    saveSonDesignPlanDetail(milepostDesignPlanDetails.get(0).getId(), forSaveSummary.getSonDtos(), project);
                }
            } else {
                MilepostDesignPlanDetailDto milepostDesignPlanDetail = new MilepostDesignPlanDetailDto();

                // ProjectSource:"项目来源:1-项目立项;2-预立项转正;3-商机立项;"
                milepostDesignPlanDetail.setSource("自动生成");
                milepostDesignPlanDetail.setLevel(project.getProjectLevel());
                milepostDesignPlanDetail.setWbsSummaryCode(forSaveSummary.getSummaryCode());
                milepostDesignPlanDetail.setNumber(new BigDecimal(1));
                milepostDesignPlanDetail.setWbsLayer(forSaveSummary.getWbsLayer());
                milepostDesignPlanDetail.setWbsConfirmFlag(forSaveSummary.getProjectDetailSelectFlag());
                milepostDesignPlanDetail.setParentId(parentId);
                milepostDesignPlanDetail.setDeletedFlag(false);
                milepostDesignPlanDetail.setWhetherModel(true);
                milepostDesignPlanDetail.setInit(false);
                milepostDesignPlanDetail.setModuleStatus(0);
                milepostDesignPlanDetail.setProjectId(project.getId());
                milepostDesignPlanDetail.setPamCode(materialExtService.generateMaterialPAMCode());
                milepostDesignPlanDetail.setWbsDescription(forSaveSummary.getDescription());
                milepostDesignPlanDetail.setMaterielDescr(forSaveSummary.getDescription());
                if (Boolean.TRUE.equals(forSaveSummary.getProjectDetailSelectFlag())) {
                    milepostDesignPlanDetail.setWbsLastLayer(true);
                    milepostDesignPlanDetail.setWbsConfirmFlag(true);
                } else {
                    milepostDesignPlanDetail.setWbsLastLayer(false);
                    milepostDesignPlanDetail.setWbsConfirmFlag(false);
                }

                milepostDesignPlanDetailService.save(milepostDesignPlanDetail, null);
                if (ListUtils.isNotEmpty(forSaveSummary.getSonDtos())) {
                    saveSonDesignPlanDetail(milepostDesignPlanDetail.getId(), forSaveSummary.getSonDtos(), project);
                }
            }
        }
    }

    private List<ProjectWbsBudgetSummaryDto> buildWbsBudgetSummaryTree(List<ProjectWbsBudgetSummaryDto> summaries) {
        List<ProjectWbsBudgetSummaryDto> rootSummaries = new ArrayList<>();
        //筛选出根节点
        summaries.forEach(summary -> {
            if (summary.getParentId() == null || summary.getParentId() == 0 || summary.getParentId() == -1) {
                rootSummaries.add(summary);
            }
        });

        //为根节点设置子节点，getClild递归调用
        for (ProjectWbsBudgetSummaryDto rootSummary : rootSummaries) {
            // 获取根节点下面的所有子节点，使用getClild方法
            List<ProjectWbsBudgetSummaryDto> childList = getChildBase(rootSummary.getId(), summaries);
            //给根节点设置子节点
            rootSummary.setSonDtos(childList);
        }

        return rootSummaries;
    }

    private List<ProjectWbsBudgetSummaryDto> getChildBase(Long id, List<ProjectWbsBudgetSummaryDto> summaries) {
        // 子节点
        List<ProjectWbsBudgetSummaryDto> childList = new ArrayList<ProjectWbsBudgetSummaryDto>();
        for (ProjectWbsBudgetSummaryDto detailDtoAll : summaries) {
            // 遍历所有节点，将所有节点的父id与传过来的根节点的id进行比较，相等则说明：为该根节点的子节点
            if (detailDtoAll.getParentId().equals(id)) {
                childList.add(detailDtoAll);
            }
        }

        // 进行递归
        for (ProjectWbsBudgetSummaryDto dto : childList) {
            dto.setSonDtos(getChildBase(dto.getId(), summaries));
        }

        //如果节点下没有子节点，返回一个空List（递归退出)
        if (childList.size() == 0) {
            return new ArrayList<ProjectWbsBudgetSummaryDto>();
        }
        return childList;
    }

    @Override
    public void updateDetailForChangeByDetailIds(List<Long> detailIds, Long updatedBy, Integer status,
                                                 Integer moduleStatus) {
        milepostDesignPlanDetailExtMapper.updateDetailForChangeByDetailIds(detailIds, updatedBy, status, moduleStatus);
    }

    @Override
    public List<MilepostDesignPlanDetailDto> getDetailsByParentId(Long detailId, Integer editQuery, String wbsRequirementCode) {
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = milepostDesignPlanDetailExtMapper.getDetailsByParentId(detailId);
        if (CollectionUtils.isEmpty(designPlanDetailDtos)) {
            return new ArrayList<>();
        }

        queryRequirementNumWithDesignPlanDetails(designPlanDetailDtos);

        Long projectId = null;
        List<Date> deliveryTimeList = new ArrayList<>();
        List<String> pamCodeList = new ArrayList<>();
        List<String> wbsSummaryCodeList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto detailDto : designPlanDetailDtos) {
            projectId = detailDto.getProjectId();

            Date deliveryTime = detailDto.getDeliveryTime();
            if (deliveryTime != null && !deliveryTimeList.contains(deliveryTime)) {
                deliveryTimeList.add(deliveryTime);
            }

            String pamCode = detailDto.getPamCode();
            if (StringUtils.isNotEmpty(pamCode) && !pamCodeList.contains(pamCode)) {
                pamCodeList.add(pamCode);
            }

            String wbsSummaryCode = detailDto.getWbsSummaryCode();
            if (StringUtils.isNotEmpty(wbsSummaryCode) && !wbsSummaryCodeList.contains(wbsSummaryCode)) {
                wbsSummaryCodeList.add(wbsSummaryCode);
            }
        }
        PurchaseMaterialRequirementExample example = new PurchaseMaterialRequirementExample();
        PurchaseMaterialRequirementExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        if (CollectionUtils.isNotEmpty(deliveryTimeList)) {
            criteria.andDeliveryTimeIn(deliveryTimeList);
        }
        if (CollectionUtils.isNotEmpty(pamCodeList)) {
            criteria.andPamCodeIn(pamCodeList);
        }
        if (CollectionUtils.isNotEmpty(wbsSummaryCodeList)) {
            criteria.andWbsSummaryCodeIn(wbsSummaryCodeList);
        }
        criteria.andDeletedFlagEqualTo(false);
        List<PurchaseMaterialRequirement> requirementList = purchaseMaterialRequirementMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(requirementList)) {
            Map<String, String> requirementMap = new HashMap<>();
            for (PurchaseMaterialRequirement requirement : requirementList) {
                requirementMap.put(getRequirementKey(requirement.getDeliveryTime(), requirement.getPamCode(), requirement.getWbsSummaryCode()),
                        requirement.getRequirementCode());
            }

            for (MilepostDesignPlanDetailDto detailDto : designPlanDetailDtos) {
                String requirementKey = getRequirementKey(detailDto.getDeliveryTime(), detailDto.getPamCode(), detailDto.getWbsSummaryCode());
                detailDto.setPurchaseMaterialRequirementReceiptsCode(requirementMap.get(requirementKey));
            }
        }
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos = milepostDesignPlanDetailService.buildTree(designPlanDetailDtos, detailId);
        milepostDesignPlanDetailDtos.forEach(dto -> {
            //统计需求量
            milepostDesignPlanDetailService.countRequirementNum(dto, BigDecimal.ONE);
        });

        //填充[允许增删改标识，是否有已确认]字段
        //判断是需求发布前还是需求发布后
        Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = new HashMap<>();
        List<Long> detailIds = CollectionUtils.isEmpty(designPlanDetailDtos) ? new ArrayList<>()
                : designPlanDetailDtos.stream().map(MilepostDesignPlanDetailDto::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(detailIds)) {
            List<ProjectWbsReceiptsDto> receiptsDtoList = projectWbsReceiptsService.selectByDetailIds(detailIds);
            receiptsDtoMap.putAll(CollectionUtils.isEmpty(receiptsDtoList) ? new HashMap<>()
                    : receiptsDtoList.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId)));
        }
        logger.info("receiptsDtoMap的detailIds：{}", JsonUtils.toString(detailIds));
        logger.info("receiptsDtoMap的keySet：{}", JsonUtils.toString(receiptsDtoMap.keySet()));

        for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : designPlanDetailDtos) {
            // 找到该详设是否被确认
            milepostDesignPlanDetailDto.setHasConfirmed(CollectionUtils.isNotEmpty(receiptsDtoMap.get(milepostDesignPlanDetailDto.getId())));
            // requirementCode赋值
            ProjectWbsReceiptsDto param = new ProjectWbsReceiptsDto();
            param.setProjectId(milepostDesignPlanDetailDto.getProjectId());
            param.setPamCode(milepostDesignPlanDetailDto.getPamCode());
            ProjectWbsReceiptsDto projectWbsReceiptsDto = projectWbsReceiptsExtMapper.selectDto(param);
            if (!ObjectUtils.isEmpty(projectWbsReceiptsDto)) {
                milepostDesignPlanDetailDto.setRequirementCode(projectWbsReceiptsDto.getRequirementCode());
            }
        }

        for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : milepostDesignPlanDetailDtos) {
            milepostDesignPlanDetailDto.setEditable(checkEditAble(milepostDesignPlanDetailDto.getSonDtos()));
        }
        setEditableByPamCodeRecursively(milepostDesignPlanDetailDtos, wbsRequirementCode);

        return milepostDesignPlanDetailDtos;
    }

    /**
     * 根据设计计划明细查询需求数量
     * 该方法通过设计计划明细ID列表查询对应的发布数量，并将结果填充到设计计划明细DTO列表中
     *
     * @param designPlanDetailDtos 设计计划明细DTO列表，用于接收查询到的需求数量
     */
    private void queryRequirementNumWithDesignPlanDetails(List<MilepostDesignPlanDetailDto> designPlanDetailDtos) {
        List<Long> designPlanDetailIds = designPlanDetailDtos.stream().map(MilepostDesignPlanDetailDto::getId).collect(Collectors.toList());

        List<MilepostDesignPlanDetailDto> queryRequirementNumPlanDetailDtoList = purchaseMaterialReleaseDetailExtMapper.getPublishNumByDesignPlanDetailIdList(designPlanDetailIds);


        for (MilepostDesignPlanDetailDto detailDto : designPlanDetailDtos) {
            for (MilepostDesignPlanDetailDto requirementNumPlanDetailDto : queryRequirementNumPlanDetailDtoList) {
                if (detailDto.getId().equals(requirementNumPlanDetailDto.getDesignPlanDetailId())) {
                    detailDto.setRequirementNum(requirementNumPlanDetailDto.getRequirementNum());
                    break;
                } else {
                    detailDto.setRequirementNum(BigDecimal.ZERO);
                }
            }
        }
    }


    private String getRequirementKey(Date deliveryTime, String pamCode, String wbsSummaryCode) {
        return DateUtil.format(deliveryTime, DateUtil.DATE_INTEGER_PATTERN) + "_" + pamCode + "_" + wbsSummaryCode;
    }

    @Override
    public List<String> filerMilepostDesignPlanByProjectIdAndErpCodes(MilepostDesignPlanDetailDto dto) {
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        MilepostDesignPlanDetailExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(dto.getProjectId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        if (CollectionUtils.isNotEmpty(dto.getErpCodeList())) {
            criteria.andErpCodeIn(dto.getErpCodeList());
        }
        List<MilepostDesignPlanDetail> milepostDesignPlanDetailList = milepostDesignPlanDetailMapper.selectByExample(example);

        return milepostDesignPlanDetailList.stream().map(MilepostDesignPlanDetail::getErpCode).collect(Collectors.toList());
    }

    @Override
    public void partialUpdateByChangeHistory(ProjectMilepostChangeHistory changeHistory) {
        // ProjectMilepost originMilepost = projectMilepostMapper.selectByPrimaryKey(changeHistory.getOriginId());
        ProjectMilepost updateMilepost = new ProjectMilepost();
        updateMilepost.setId(changeHistory.getOriginId());
        updateMilepost.setName(changeHistory.getName());
        updateMilepost.setOrderNum(changeHistory.getOrderNum());
        updateMilepost.setRatio(changeHistory.getRatio());
        updateMilepost.setBaseEndTime(changeHistory.getBaseEndTime());
        updateMilepost.setBaseStartTime(changeHistory.getBaseStartTime());
        updateMilepost.setStartTime(changeHistory.getStartTime());
        updateMilepost.setEndTime(changeHistory.getEndTime());
        updateMilepost.setNote(changeHistory.getNote());
        updateMilepost.setNotice(changeHistory.getNotice());
        updateMilepost.setResponsible(changeHistory.getResponsible());
        updateMilepost.setDeletedFlag(changeHistory.getDeletedFlag());
        updateMilepost.setDeliverTime(changeHistory.getDeliverTime());
        updateMilepost.setPreDeletedFlag(changeHistory.getPreDeletedFlag());
        updateMilepost.setIncomeFlag(changeHistory.getIncomeFlag());
        updateMilepost.setIncomeRatio(changeHistory.getIncomeRatio());
        updateMilepost.setIncome(changeHistory.getIncome());
        updateMilepost.setCostRatio(changeHistory.getCostRatio());
        updateMilepost.setCost(changeHistory.getCost());
        projectMilepostMapper.updateByPrimaryKeySelective(updateMilepost);
    }

    @Override
    public ProjectCheckExcelVo checkProjectPurchaseMaterialRequirementItem(Project project) {
        String[] headers = {"序号", "物料描述", "PAM编码", "ERP物料编码", "WBS", "单位", "采购需求总数", "单套数量", "需求日期", "名称", "型号/规格/图号",
                "品牌", "需求类型", "物料大类", "物料中类", "物料小类", "图号", "图纸版本号", "加工件分类", "材质", "单位重量(Kg)", "材质处理",
                "活动事项编码", "项目预算类别属性", "品牌商物料编码", "备件标识", "设计人员", "设计发布批次号", "备注", "来源", "确认状态", "已生成采购需求数量"};
        Date date = new Date();//检查时间
        ProjectCheckExcelVo projectCheckExcelVo = new ProjectCheckExcelVo();
        projectCheckExcelVo.setDate(date);
        projectCheckExcelVo.setProjectCode(project.getCode());
        projectCheckExcelVo.setProjectName(project.getName());
        projectCheckExcelVo.setHeaders(headers);
        projectCheckExcelVo.setPass(true);
        List<ProjectCheckReturnVo> returnVoList = new ArrayList<>();
        int num = 1;//导出明细计数

        //检查项目关联采购合同是否完全开票
        MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
        MilepostDesignPlanDetailExample.Criteria criteria = milepostDesignPlanDetailExample.createCriteria();
        criteria.andProjectIdEqualTo(project.getId()).andMaterialCategoryEqualTo(MaterialCategoryEnum.PURCHASED_PARTS.msg())
                .andModuleStatusIn(Lists.newArrayList(MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code(),
                        MilepostDesignPlanDetailModelStatus.CONFIRMING.code())).andDeletedFlagEqualTo(Boolean.FALSE);
        List<MilepostDesignPlanDetail> detailList = milepostDesignPlanDetailMapper.selectByExample(milepostDesignPlanDetailExample);
        if (CollectionUtils.isNotEmpty(detailList)) {
            projectCheckExcelVo.setPass(Boolean.FALSE);
            for (MilepostDesignPlanDetail detail : detailList) {
                Integer requirementType = detail.getRequirementType();
                DictDto dictDto = Objects.isNull(requirementType) ? null
                        : CacheDataUtils.findDictByTypeAndCode(DictType.DESIGN_REQUIREMENT_TYPE.code(), detail.getRequirementType().toString());

                ProjectCheckReturnVo returnVo = new ProjectCheckReturnVo();
                returnVo.setC0("" + num++);
                returnVo.setC1(detail.getMaterielDescr());
                returnVo.setC2(detail.getPamCode());
                returnVo.setC3(detail.getErpCode());
                returnVo.setC4(detail.getWbsSummaryCode());
                returnVo.setC5(detail.getUnit());
                returnVo.setC6("--");
                returnVo.setC7(Optional.ofNullable(detail.getNumber()).map(e -> e.stripTrailingZeros().toPlainString()).orElse("--"));
                returnVo.setC8(Optional.ofNullable(detail.getDeliveryTime()).map(e -> DateUtil.format(e, "yyyy-MM-dd")).orElse("--"));
                returnVo.setC9(detail.getName());
                returnVo.setC10(detail.getModel());

                returnVo.setC11(detail.getBrand());
                returnVo.setC12(Optional.ofNullable(dictDto).map(DictDto::getName).orElse("--"));
                returnVo.setC13(detail.getMaterialClassification());
                returnVo.setC14(detail.getCodingMiddleClass());
                returnVo.setC15(detail.getMaterielType());
                returnVo.setC16(detail.getFigureNumber());
                returnVo.setC17(detail.getChartVersion());
                returnVo.setC18(detail.getMachiningPartType());
                returnVo.setC19(detail.getMaterial());
                returnVo.setC20(Optional.ofNullable(detail.getUnitWeight()).map(e -> e.stripTrailingZeros().toPlainString()).orElse("--"));
                returnVo.setC21(detail.getMaterialProcessing());

                returnVo.setC22(detail.getActivityCode());
                returnVo.setC23(detail.getProjectBudgetType());
                returnVo.setC24(detail.getBrandMaterialCode());
                returnVo.setC25(detail.getOrSparePartsMask());
                returnVo.setC26(detail.getPlanDesigner());
                returnVo.setC27(detail.getDesignReleaseLotNumber());
                returnVo.setC28(detail.getRemark());
                returnVo.setC29(detail.getSource());
                returnVo.setC30(MilepostDesignPlanDetailModelStatus.getMsgByCode(detail.getModuleStatus()));
                returnVo.setC31("--");

                returnVoList.add(returnVo);
            }
        }

        projectCheckExcelVo.setReturnList(returnVoList);
        return projectCheckExcelVo;
    }

    @Override
    public List<MilepostDesignPlanDetailDto> getMilepostDesignPlanDetails(Long projectId, Long milepostId, boolean isSynchronizedErp, boolean isGeneratePurchase) {

        List<String> list = this.getDynamicPamCodeOrErpCodeList(projectId, milepostId, isSynchronizedErp, isGeneratePurchase);
        List<MilepostDesignPlanDetailDto> conditionQueryDetailDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
            MilepostDesignPlanDetailExample.Criteria criteria = milepostDesignPlanDetailExample.createCriteria();
            criteria.andProjectIdEqualTo(projectId).andDeletedFlagEqualTo(Boolean.FALSE);
            if (isSynchronizedErp && !isGeneratePurchase) {
                //查找的是物料未同步ERP的单据-取的是PAM码
                criteria.andPamCodeIn(list);
            }

            if (!isSynchronizedErp && isGeneratePurchase) {
                //查询的是未生成采购需求的单据--取的是单据ID
                List<Long> ids = list.stream().map(x -> Long.valueOf(x)
                ).collect(Collectors.toList());
                criteria.andIdIn(ids);
            }
            long start = System.currentTimeMillis();
            /**共用的方案被本迭代其他分支修改,因而重新写一份*/
            conditionQueryDetailDtoList = getMilepostDesignPlanDetail(milepostDesignPlanDetailExample, projectId);
            long end = System.currentTimeMillis();
            logger.info("查询耗时为:{}", end - start);
        }

        return conditionQueryDetailDtoList;
    }

    private List<MilepostDesignPlanDetailDto> getMilepostDesignPlanDetail(MilepostDesignPlanDetailExample example, Long projectId) {
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                milepostDesignPlanDetailExtMapper.selectDetailAndReceiptsByCondition(example);

        List<Long> designPlanDetailIds = new ArrayList<>();
        List<Long> planDetailIds = new ArrayList<>();
        for (MilepostDesignPlanDetailDto detailDto : milepostDesignPlanDetailDtos) {
            designPlanDetailIds.add(detailDto.getId());

            if (detailDto.getWbsLastLayer() == null || Boolean.TRUE.equals(detailDto.getWbsLastLayer())) {
                planDetailIds.add(detailDto.getId());
            }
        }
        logger.info("获取里程碑详细设计方案设计信息的id集合为:{}", designPlanDetailIds.size());
        logger.info("过滤后的id集合:{}", planDetailIds.size());
        //查询
        List<MilepostDesignPlanFieldDto> purchaseStatusList =
                milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanPurchaseInfo(designPlanDetailIds);
        Map<Long, Integer> purchaseStatusMap = new HashMap<>();//采购状态
        Map<Long, BigDecimal> approvalNumMap = new HashMap<>();//提前采购审批中的数量
        Map<Long, BigDecimal> purchaseNumMap = new HashMap<>();//提前采购数量
        for (MilepostDesignPlanFieldDto dto : purchaseStatusList) {
            purchaseStatusMap.put(dto.getDesignPlanDetailId(), dto.getPurchaseStatus());
            if (Objects.equals(dto.getPurchaseStatus(), -1)) {
                approvalNumMap.put(dto.getDesignPlanDetailId(), dto.getPurchaseNum());
            }
            if (Objects.equals(dto.getPurchaseStatus(), 0) || Objects.equals(dto.getPurchaseStatus(), -1) || Objects.equals(dto.getPurchaseStatus()
                    , 1)
                    || Objects.equals(dto.getPurchaseStatus(), 3) || Objects.equals(dto.getPurchaseStatus(), 40)) {
                purchaseNumMap.put(dto.getDesignPlanDetailId(), dto.getPurchaseNum());

            }
        }
        Map<Long, MilepostDesignPlanFieldDto> mergeMap;
        if (ListUtils.isNotEmpty(planDetailIds)) {
            //三个临时表的查询
            List<MilepostDesignPlanFieldDto> t1List = milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanT1(projectId);
            Map<Long, MilepostDesignPlanFieldDto> t1Map = t1List.stream().collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId,
                    Function.identity()));
            List<MilepostDesignPlanFieldDto> t2List = milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanT2(projectId);
            Map<Long, MilepostDesignPlanFieldDto> t2Map = t2List.stream().collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId,
                    Function.identity()));
            List<MilepostDesignPlanFieldDto> t3List = milepostDesignPlanDetailExtMapper.selectMilepostDesignPlanT3(projectId);
            Map<Long, MilepostDesignPlanFieldDto> t3Map = t3List.stream().collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId,
                    Function.identity()));
            //合并数据,如果表1有数据以表1为准,如果表1没有取表2,如果表2也没有,取表3
            mergeMap = planDetailIds.stream().map(x -> {
                MilepostDesignPlanFieldDto dto = new MilepostDesignPlanFieldDto();
                dto.setDesignPlanDetailId(x);
                dto.setRequirementCode(t1Map.containsKey(x) ? t1Map.get(x).getRequirementCode() : (t2Map.containsKey(x) ?
                        t2Map.get(x).getRequirementCode() : (t3Map.containsKey(x) ? t3Map.get(x).getRequirementCode() : null)));
                dto.setRequirementStatus(t1Map.containsKey(x) ? t1Map.get(x).getRequirementStatus() : (t2Map.containsKey(x) ?
                        t2Map.get(x).getRequirementStatus() : (t3Map.containsKey(x) ? t3Map.get(x).getRequirementStatus() : null)));
                return dto;
            }).collect(Collectors.toMap(MilepostDesignPlanFieldDto::getDesignPlanDetailId, Function.identity()));
        } else {
            mergeMap = new HashMap<>();
        }

        List<MilepostDesignPlanDetailDto> dtos = milepostDesignPlanDetailDtos.stream().map(x -> {
            //提前采购审批状态 t purchaseStatus
            x.setPurchaseStatus(purchaseStatusMap.get(x.getId()));
            //提前采购数量 t2 purchaseNum
            x.setPurchaseNum(Optional.ofNullable(purchaseNumMap.get(x.getId())).orElse(BigDecimal.ZERO.setScale(6)));
            //提前采购审批中的数量 t3 approvalNum
            x.setApprovalNum(Optional.ofNullable(approvalNumMap.get(x.getId())).orElse(BigDecimal.ZERO.setScale(6)));
            //需求发布单据编号 pwr2,pwr,pwr3 requirementCode
            if (x.getWbsLastLayer() == null || Boolean.TRUE.equals(x.getWbsLastLayer())) {
                x.setRequirementCode(mergeMap.containsKey(x.getId()) ? mergeMap.get(x.getId()).getRequirementCode() : null);
                //单据状态 0-草稿、1-待处理、2-审核中、3-驳回、4-生效、5-作废 pwr2,pwr,pwr3 requirementStatus
                x.setRequirementStatus(mergeMap.containsKey(x.getId()) ? mergeMap.get(x.getId()).getRequirementStatus() : null);
            }
            return x;
        }).collect(Collectors.toList());


        return dtos;
    }

    /**
     * 查找包含_isNew=true的节点的父节点信息
     *
     * @return 父节点ID列表
     */
    public List<Long> findParentIdsOfNewNodes(List<MilepostDesignPlanDetailDto> designPlanDetailDtos) {
        // 使用Guava创建结果列表
        List<Long> parentIds = Lists.newArrayList();

        // 获取设计方案详细信息列表
        if (CollUtil.isEmpty(designPlanDetailDtos)) {
            return parentIds;
        }

        // 遍历每个详细设计方案
        designPlanDetailDtos.forEach(detail -> findNewNodesParentId(detail, parentIds));

        return parentIds;
    }

    /**
     * 递归查找新增节点的父节点ID
     *
     * @param node      当前节点
     * @param parentIds 父节点ID结果集
     */
    private void findNewNodesParentId(MilepostDesignPlanDetailDto node, List<Long> parentIds) {
        if (node == null) {
            return;
        }

        // 检查子节点列表
        List<MilepostDesignPlanDetailDto> sonDtos = node.getSonDtos();
        if (CollUtil.isNotEmpty(sonDtos)) {
            // 遍历子节点
            for (MilepostDesignPlanDetailDto son : sonDtos) {
                // 检查是否为新增节点
                if (Boolean.TRUE.equals(son.get_isNew())) {
                    // 添加父节点ID到结果集
                    parentIds.add(node.getId());

                    logger.info("找到新增节点: parentId={}, parentMaterielDescr={}, newNodeMaterielDescr={}, orderNo={}",
                            node.getId(),
                            node.getMaterielDescr(),
                            son.getMaterielDescr(),
                            son.getOrderNumSum());
                }

                // 继续递归查找
                findNewNodesParentId(son, parentIds);
            }
        }
    }

    /**
     * 处理基于uid的多层级新增关系，构建独立树并从平铺列表中移除子节点
     * （专用于详设发布场景）
     *
     * 处理场景：页面新增了父级后，在这个父级下又新增了子级（可能多层）
     * 新增子级的parentId等于父级的uid，需要按照这种关系组装成树状层级
     * 组装完成后将这些详设的子节点从flatDetailDtoList中删除，保留顶层节点
     *
     * @param flatDetailDtoList 平铺的详设数据列表（会被修改，移除处理过的子节点）
     * @return 构建好的基于uid关系的独立树列表
     */
    private List<MilepostDesignPlanDetailDto> processUidBasedHierarchyAndRemoveFromFlatForDesignPlan(
            List<MilepostDesignPlanDetailDto> flatDetailDtoList) {

        if (ListUtils.isEmpty(flatDetailDtoList)) {
            logger.info("无详设数据，跳过uid层级处理");
            return new ArrayList<>();
        }

        logger.info("开始处理基于uid的多层级新增关系，共{}个节点", flatDetailDtoList.size());

        // 1. 构建uid到节点的映射关系（只处理_isNew=true的节点）
        Map<String, MilepostDesignPlanDetailDto> uidToNodeMap = new HashMap<>();
        for (MilepostDesignPlanDetailDto dto : flatDetailDtoList) {
            // 只处理_isNew=true的详设
            if (StringUtils.isNotEmpty(dto.getUid()) && Boolean.TRUE.equals(dto.get_isNew())) {
                uidToNodeMap.put(dto.getUid(), dto);
                logger.debug("构建uid映射: uid={}, nodeId={}, pamCode={}, _isNew={}",
                        dto.getUid(), dto.getId(), dto.getPamCode(), dto.get_isNew());
            }
        }

        logger.info("构建完成，uid映射数量：{}", uidToNodeMap.size());

        // 2. 识别基于uid的父子关系（只处理_isNew=true的节点）
        List<MilepostDesignPlanDetailDto> uidBasedNodes = new ArrayList<>();
        // 只收集子节点的uid，用于后续删除
        Set<String> childUids = new HashSet<>();
        // 收集根节点的uid，用于后续保留
        Set<String> rootUids = new HashSet<>();

        for (MilepostDesignPlanDetailDto dto : flatDetailDtoList) {
            // 只处理_isNew=true的详设
            if (dto.getParentId() != null && Boolean.TRUE.equals(dto.get_isNew())) {
                String parentUid = String.valueOf(dto.getParentId());

                // 检查parentId是否对应某个节点的uid
                if (uidToNodeMap.containsKey(parentUid)) {
                    uidBasedNodes.add(dto);
                    childUids.add(dto.getUid());
                    logger.debug("发现uid父子关系: 子节点uid={}, parentUid={}, _isNew={}",
                            dto.getUid(), parentUid, dto.get_isNew());
                }
            }
        }

        // 3. 同时收集所有作为父级的uid节点
        Set<String> parentUids = new HashSet<>();
        for (MilepostDesignPlanDetailDto dto : uidBasedNodes) {
            String parentUid = String.valueOf(dto.getParentId());
            parentUids.add(parentUid);
        }

        // 将作为父级的节点也加入处理列表
        for (String parentUid : parentUids) {
            MilepostDesignPlanDetailDto parentNode = uidToNodeMap.get(parentUid);
            if (parentNode != null && !childUids.contains(parentUid)) {
                uidBasedNodes.add(parentNode);
                rootUids.add(parentUid);
                logger.debug("添加父级节点: uid={}", parentUid);
            }
        }

        if (uidBasedNodes.isEmpty()) {
            logger.info("未发现基于uid的父子关系，跳过处理");
            return new ArrayList<>();
        }

        logger.info("发现{}个基于uid关系的节点", uidBasedNodes.size());

        // 4. 构建树状结构
        List<MilepostDesignPlanDetailDto> trees = buildUidBasedTreesForDesignPlan(uidBasedNodes, uidToNodeMap);

        clearParentIdInTreesForDesignPlan(trees);

        // 6. 从原始列表中只移除子节点，保留顶层节点
        flatDetailDtoList.removeIf(dto -> childUids.contains(dto.getUid()) && !rootUids.contains(dto.getUid()));

        logger.info("完成uid层级处理，构建了{}棵树，从原列表移除了{}个子节点，保留了{}个顶层节点",
                trees.size(), childUids.size(), rootUids.size());

        return trees;
    }

    /**
     * 基于uid关系构建树状结构（专用于详设发布场景）
     *
     * @param uidBasedNodes 参与uid关系的所有节点
     * @param uidToNodeMap uid到节点的映射
     * @return 构建好的树列表
     */
    private List<MilepostDesignPlanDetailDto> buildUidBasedTreesForDesignPlan(
            List<MilepostDesignPlanDetailDto> uidBasedNodes,
            Map<String, MilepostDesignPlanDetailDto> uidToNodeMap) {

        // 找出根节点（parentId不在uid映射中的节点，或parentId为-1的节点）
        List<MilepostDesignPlanDetailDto> rootNodes = new ArrayList<>();

        for (MilepostDesignPlanDetailDto node : uidBasedNodes) {
            if (node.getParentId() == null) {
                rootNodes.add(node);
                logger.debug("发现根节点: uid={}, pamCode={}", node.getUid(), node.getPamCode());
            } else {
                String parentUid = String.valueOf(node.getParentId());
                if (!uidToNodeMap.containsKey(parentUid)) {
                    // parentId不在uid映射中，说明这是一个根节点
                    rootNodes.add(node);
                    logger.debug("发现根节点（parentId不在uid映射中）: uid={}, parentId={}, pamCode={}",
                            node.getUid(), node.getParentId(), node.getPamCode());
                }
            }
        }

        // 为每个根节点构建子树
        for (MilepostDesignPlanDetailDto rootNode : rootNodes) {
            buildUidBasedChildTreeForDesignPlan(rootNode, uidBasedNodes, uidToNodeMap);
        }

        return rootNodes;
    }

    /**
     * 递归构建基于uid关系的子树（专用于详设发布场景）
     *
     * @param parentNode 父节点
     * @param allNodes 所有参与uid关系的节点
     * @param uidToNodeMap uid到节点的映射
     */
    private void buildUidBasedChildTreeForDesignPlan(MilepostDesignPlanDetailDto parentNode,
                                        List<MilepostDesignPlanDetailDto> allNodes,
                                        Map<String, MilepostDesignPlanDetailDto> uidToNodeMap) {

        List<MilepostDesignPlanDetailDto> children = new ArrayList<>();

        // 查找所有以当前节点uid为parentId的子节点
        for (MilepostDesignPlanDetailDto node : allNodes) {
            if (node.getParentId() != null ) {
                String parentUid = String.valueOf(node.getParentId());
                if (parentUid.equals(parentNode.getUid()) && !node.equals(parentNode)) {
                    children.add(node);
                    logger.debug("找到子节点: 父uid={}, 子uid={}, 子pamCode={}",
                            parentNode.getUid(), node.getUid(), node.getPamCode());
                }
            }
        }

        if (!children.isEmpty()) {
            parentNode.setSonDtos(children);

            // 递归构建每个子节点的子树
            for (MilepostDesignPlanDetailDto child : children) {
                buildUidBasedChildTreeForDesignPlan(child, allNodes, uidToNodeMap);
            }
        }
    }

    /**
     * 清理树中parentId为UID节点详设的parentId（专用于详设发布场景）
     *
     * @param trees 树列表
     */
    private void clearParentIdInTreesForDesignPlan(List<MilepostDesignPlanDetailDto> trees) {
        for (MilepostDesignPlanDetailDto tree : trees) {
            clearParentIdInNodeForDesignPlan(tree);
        }
        logger.info("已清理{}棵树中所有节点的parentId", trees.size());
    }

    /**
     * 递归清理节点及其子节点的parentId（专用于详设发布场景）
     *
     * @param node 当前节点
     */
    private void clearParentIdInNodeForDesignPlan(MilepostDesignPlanDetailDto node) {
        if (node == null) {
            return;
        }

        // 获取节点ID
        Long parentId = node.getParentId();

        // 检查节点是否存在于数据库中
        if (parentId != null) {
            try {
                MilepostDesignPlanDetail detail = milepostDesignPlanDetailMapper.selectByPrimaryKey(parentId);
                if (detail == null) {
                    // 节点不存在于数据库中，可以安全地设置parentId为空
                    node.setParentId(null);
                    logger.debug("节点不存在于数据库中，清除parentId: nodeId={}", parentId);
                } else {
                    // 节点存在于数据库中，保留原有parentId
                    logger.debug("节点存在于数据库中，保留parentId: nodeId={}, parentId={}", parentId, node.getParentId());
                }
            } catch (Exception e) {
                logger.error("查询节点失败，默认清除parentId: nodeId={}, error={}", parentId, e.getMessage());
                node.setParentId(null);
            }
        } else {
            // 没有ID的节点（新增节点），直接清除parentId
            node.setParentId(null);
            logger.debug("新增节点，清除parentId: uid={}", node.getUid());
        }

        // 递归清理子节点
        if (ListUtils.isNotEmpty(node.getSonDtos())) {
            for (MilepostDesignPlanDetailDto child : node.getSonDtos()) {
                clearParentIdInNodeForDesignPlan(child);
            }
        }
    }

    /**
     * 将基于uid构建的独立树添加到根节点的子节点中
     *
     * @param packagePlanDetail 根节点
     * @param uidBasedTrees 基于uid构建的独立树列表
     */
    private void addUidBasedTreesToPackagePlanDetail(MilepostDesignPlanDetailDto packagePlanDetail,
                                                     List<MilepostDesignPlanDetailDto> uidBasedTrees) {
        if (ListUtils.isNotEmpty(uidBasedTrees)) {
            if (packagePlanDetail.getSonDtos() == null) {
                packagePlanDetail.setSonDtos(new ArrayList<>());
            }
            packagePlanDetail.getSonDtos().addAll(uidBasedTrees);
            logger.info("添加了{}棵基于uid构建的独立树到根节点", uidBasedTrees.size());
        }
    }

    /**
     * 当没有数据库父节点时，处理基于uid的独立树
     *
     * @param dto 包含平铺详设数据的DTO
     * @param uidBasedTrees 基于uid构建的独立树列表
     */
    private void handleUidBasedTreesWhenNoPackagePlanDetail(MilepostDesignPlanDto dto,
                                                            List<MilepostDesignPlanDetailDto> uidBasedTrees) {
        if (ListUtils.isNotEmpty(uidBasedTrees)) {
            dto.getReceiptsDto().setDesignPlanDetailDtos(uidBasedTrees);
            logger.info("直接使用{}棵基于uid构建的独立树", uidBasedTrees.size());
        } else {
            logger.warn("未能组装出有效的详设层级结构");
        }
    }

    /**
     * 将基于uid构建的独立树添加到根节点的子节点中，并检查重复
     * 避免同一个节点既在层级结构中又在根节点的直接子节点中出现
     *
     * @param packagePlanDetail 根节点
     * @param uidBasedTrees 基于uid构建的独立树列表
     */
    private void addUidBasedTreesToPackagePlanDetailWithDuplicateCheck(MilepostDesignPlanDetailDto packagePlanDetail,
                                                                       List<MilepostDesignPlanDetailDto> uidBasedTrees) {
        if (ListUtils.isEmpty(uidBasedTrees)) {
            return;
        }

        // 收集已存在于层级结构中的所有节点的uid和pamCode
        Set<String> existingUids = new HashSet<>();
        Set<String> existingPamCodes = new HashSet<>();
        collectExistingNodeIdentifiers(packagePlanDetail, existingUids, existingPamCodes);

        // 过滤掉已经存在的节点
        List<MilepostDesignPlanDetailDto> filteredTrees = new ArrayList<>();
        for (MilepostDesignPlanDetailDto tree : uidBasedTrees) {
            if (!isNodeAlreadyExists(tree, existingUids, existingPamCodes)) {
                filteredTrees.add(tree);
                logger.info("添加基于uid的独立树节点: uid={}, pamCode={}, name={}",
                    tree.getUid(), tree.getPamCode(), tree.getName());
            } else {
                logger.info("跳过重复的节点: uid={}, pamCode={}, name={}",
                    tree.getUid(), tree.getPamCode(), tree.getName());
            }
        }

        if (ListUtils.isNotEmpty(filteredTrees)) {
            if (packagePlanDetail.getSonDtos() == null) {
                packagePlanDetail.setSonDtos(new ArrayList<>());
            }
            packagePlanDetail.getSonDtos().addAll(filteredTrees);
            logger.info("添加了{}棵基于uid构建的独立树到根节点（过滤后），原始数量：{}",
                filteredTrees.size(), uidBasedTrees.size());
        } else {
            logger.info("所有基于uid的独立树都已存在，跳过添加");
        }
    }

    /**
     * 递归收集已存在于层级结构中的所有节点的标识符
     *
     * @param node 当前节点
     * @param existingUids 收集uid的集合
     * @param existingPamCodes 收集pamCode的集合
     */
    private void collectExistingNodeIdentifiers(MilepostDesignPlanDetailDto node,
                                               Set<String> existingUids,
                                               Set<String> existingPamCodes) {
        if (node == null) {
            return;
        }

        // 收集当前节点的标识符
        if (StringUtils.isNotEmpty(node.getUid())) {
            existingUids.add(node.getUid());
        }
        if (StringUtils.isNotEmpty(node.getPamCode())) {
            existingPamCodes.add(node.getPamCode());
        }

        // 递归处理子节点
        if (ListUtils.isNotEmpty(node.getSonDtos())) {
            for (MilepostDesignPlanDetailDto child : node.getSonDtos()) {
                collectExistingNodeIdentifiers(child, existingUids, existingPamCodes);
            }
        }
    }

    /**
     * 检查节点是否已经存在于层级结构中
     *
     * @param node 要检查的节点
     * @param existingUids 已存在的uid集合
     * @param existingPamCodes 已存在的pamCode集合
     * @return true如果节点已存在，false否则
     */
    private boolean isNodeAlreadyExists(MilepostDesignPlanDetailDto node,
                                       Set<String> existingUids,
                                       Set<String> existingPamCodes) {
        if (node == null) {
            return false;
        }

        // 检查uid是否已存在
        if (StringUtils.isNotEmpty(node.getUid()) && existingUids.contains(node.getUid())) {
            return true;
        }

        // 检查pamCode是否已存在
        if (StringUtils.isNotEmpty(node.getPamCode()) && existingPamCodes.contains(node.getPamCode())) {
            return true;
        }

        return false;
    }
}

