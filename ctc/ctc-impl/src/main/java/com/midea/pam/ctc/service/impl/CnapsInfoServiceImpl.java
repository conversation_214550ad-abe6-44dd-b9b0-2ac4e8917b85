package com.midea.pam.ctc.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.CnapsInfoDto;
import com.midea.pam.common.ctc.entity.CnapsInfo;
import com.midea.pam.common.ctc.entity.CnapsInfoExample;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.CnapsInfoExtMapper;
import com.midea.pam.ctc.mapper.CnapsInfoMapper;
import com.midea.pam.ctc.service.CnapsInfoService;
import com.midea.pam.ctc.service.EsbService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: pam
 * @description: 联行号 实现层
 * @author: fangyl
 * @create: 2019-4-13
 **/
public class CnapsInfoServiceImpl implements CnapsInfoService {

    private static Logger logger = LoggerFactory.getLogger(CnapsInfoServiceImpl.class);

    @Resource
    private CnapsInfoMapper cnapsInfoMapper;
    @Resource
    private CnapsInfoExtMapper cnapsInfoExtMapper;
    @Resource
    private EsbService esbService;

    @Override
    public PageInfo<CnapsInfoDto> page(Map<String, Object> param) {
        //分页参数
        Integer pageNum = Integer.parseInt(param.get(Constants.Page.PAGE_NUM).toString());
        Integer pageSize = Integer.parseInt(param.get(Constants.Page.PAGE_SIZE).toString());
        PageHelper.startPage(pageNum, pageSize);
        //按条件查询数据
        CnapsInfoExample example = new CnapsInfoExample();
        CnapsInfoExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(new Integer(0));
        if (param.get("id") != null) {
            criteria.andIdEqualTo(Long.parseLong(param.get("id").toString()));
        }
        if (param.get("bankCode") != null) {
            criteria.andBankCodeLike("%" + param.get("bankCode").toString() + "%");
        }
        if (param.get("bankName") != null) {
            criteria.andBankNameLike("%" + param.get("bankName").toString() + "%");
        }
        if (param.get("cnapsCode") != null) {
            criteria.andCnapsCodeLike("%" + param.get("cnapsCode").toString() + "%");
        }
        if (param.get("orgCode") != null) {
            criteria.andOrgCodeLike("%" + param.get("orgCode").toString() + "%");
        }
        if (param.get("countryCode") != null) {
            criteria.andCountryCodeLike("%" + param.get("countryCode").toString() + "%");
        }
        if (param.get("countryName") != null) {
            criteria.andCountryNameLike("%" + param.get("countryName").toString() + "%");
        }
        if (param.get("verifyMark") != null) {
                final List<Integer> verifyMark= Arrays.stream(param.get("verifyMark").toString().split(",")).
                        map(s->Integer.parseInt(s.trim())).
                        collect(Collectors.toList());
                criteria.andVerifyMarkIn(verifyMark);
            }
            if (param.get("status") != null) {
                final List<Integer> status= Arrays.stream(param.get("status").toString().split(",")).
                        map(s->Integer.parseInt(s.trim())).
                        collect(Collectors.toList());
                criteria.andStatusIn(status);
        }
        List<CnapsInfo> entityList = cnapsInfoMapper.selectByExample(example);
        PageInfo<CnapsInfoDto> pageInfo = BeanConverter.convertPage(entityList, CnapsInfoDto.class);
        //数据处理（Entity转换Dto）
        for (CnapsInfoDto entity : pageInfo.getList()) {
            //创建人
            UserInfo userInfoCr = CacheDataUtils.findUserById(entity.getCreateBy());
            entity.setCreateByName(userInfoCr == null ? null : userInfoCr.getName());
            //更新人
            UserInfo userInfoUp = CacheDataUtils.findUserById(entity.getUpdateBy());
            entity.setUpdateByName(userInfoUp == null ? null : userInfoUp.getName());
        }
        return pageInfo;
    }

    /**
     * 唯一条件查重：BankName + CnapsCode
     * @param query
     * @return
     */
    private CnapsInfo findByUniqueKey(CnapsInfo query) {
        CnapsInfoExample example = new CnapsInfoExample();
        CnapsInfoExample.Criteria criteria = example.createCriteria();
        criteria.andBankNameEqualTo(query.getBankName()).andCnapsCodeEqualTo(query.getCnapsCode());
        List<CnapsInfo> entityList = cnapsInfoMapper.selectByExample(example);
        if(ListUtils.isNotEmpty(entityList)) {
            return entityList.get(0);
        }
        return null;
    }

    /**
     * 唯一条件查重：swift_code
     * @param query
     * @return
     */
    private CnapsInfo findBySwiftCode(CnapsInfo query) {
        CnapsInfoExample example = new CnapsInfoExample();
        CnapsInfoExample.Criteria criteria = example.createCriteria();
        criteria.andSwiftCodeEqualTo(query.getSwiftCode()).andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
        List<CnapsInfo> entityList = cnapsInfoMapper.selectByExample(example);
        if(ListUtils.isNotEmpty(entityList)) {
            return entityList.get(0);
        }
        return null;
    }

    /**
     * PAM-GFP-002 资金联行号同步
     * @param offset
     * @param lastUpdateDate
     * @return
     */
    @Override
    public String syncFromGceb(Integer offset, String lastUpdateDate) {
        Integer batchSize = 200;
        logger.info("-------callFDPCnapsInfoService--------:"+offset);
        List<CnapsInfo> cnapsInfoList = esbService.callFDPCnapsInfoService(offset, lastUpdateDate);
        for(CnapsInfo cnapsInfo : cnapsInfoList) {
            CnapsInfo existEntity = this.findByUniqueKey(cnapsInfo);
            if(ObjectUtils.isEmpty(existEntity)) {
                cnapsInfo.setDeletedFlag(0);
                cnapsInfoMapper.insert(cnapsInfo);
            } else {
                cnapsInfo.setId(existEntity.getId());
                cnapsInfoMapper.updateByPrimaryKeySelective(cnapsInfo);
            }
        }
        if(cnapsInfoList.size() > 0) {
            //递归拉取
            cnapsInfoList.clear();
            offset = offset + batchSize;
            this.syncFromGceb(offset, lastUpdateDate);
        }
        return "0";
    }

    /**
     * PAM-FDP Swift国际银行
     * @param offset
     * @param lastUpdateDate
     * @return
     */
    @Override
    public String syncFromSwift(Integer offset, String lastUpdateDate) {
        Integer batchSize = 2000;
        logger.info("-------callFDPCnapsInfoService--------:"+offset);
        List<CnapsInfo> cnapsInfoList = esbService.callSwiftCnapsInfoService(offset, lastUpdateDate);
        for(CnapsInfo cnapsInfo : cnapsInfoList) {
            CnapsInfo existEntity = this.findBySwiftCode(cnapsInfo);
            if(ObjectUtils.isEmpty(existEntity)) {
                cnapsInfo.setDeletedFlag(0);
                cnapsInfoMapper.insert(cnapsInfo);
            } else {
                cnapsInfo.setId(existEntity.getId());
                cnapsInfoMapper.updateByPrimaryKeySelective(cnapsInfo);
            }
        }
        if(cnapsInfoList.size() > 0) {
            //递归拉取
            cnapsInfoList.clear();
            offset = offset + batchSize;
            this.syncFromSwift(offset, lastUpdateDate);
        }
        return "0";
    }

    @Override
    public PageInfo<CnapsInfo> pageByCodeOrAddress(String fuzzyLike, int pageSize, int pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        final List<CnapsInfo> cnapsInfoList = cnapsInfoExtMapper.listByCodeOrAddress(fuzzyLike);
        PageInfo<CnapsInfo> pageInfo = BeanConverter.convertPage(cnapsInfoList, CnapsInfo.class);
        return pageInfo;
    }


}
