package com.midea.pam.ctc.service.listener;

import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanConfirmRecordDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanConfirmRecordRelationDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanConfirmRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanSubmitRecord;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.MilepostStatus;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.MilepostDesignPlanDetailModelStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.MilepostDesignPlanConfirmRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostMapper;
import com.midea.pam.ctc.service.AsyncRequestResultService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MilepostDesignPlanConfirmRecordRelationService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.TicketTasksService;
import com.midea.pam.ctc.service.event.DesignPlanDetailConfirmApprovalOldEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class DesignPlanDetailConfirmApprovalOldListener implements ApplicationListener<DesignPlanDetailConfirmApprovalOldEvent> {
    private final static Logger logger = LoggerFactory.getLogger(DesignPlanDetailConfirmApprovalOldListener.class);

    private final static Long MAXWAITTIME = (long) 1000 * 60 * 20;//20分钟

    private final static Long MAX_WAIT_TIME_SYNC = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private AsyncRequestResultService asyncRequestResultService;
    @Resource
    private TicketTasksService ticketTasksService;
    @Resource
    private MilepostDesignPlanConfirmRecordMapper milepostDesignPlanConfirmRecordMapper;
    @Resource
    private MilepostDesignPlanConfirmRecordRelationService milepostDesignPlanConfirmRecordRelationService;
    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private ProjectMilepostMapper projectMilepostMapper;
    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;


    @Resource
    private PurchaseMaterialRequirementService purchaseMaterialRequirementService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @Async
    public void onApplicationEvent(final DesignPlanDetailConfirmApprovalOldEvent event) {
        logger.info("---开始详设交付-确认审批事件，异步处理结果id为:{}", event.getDto().getId());
        // 并发时候的key值
        String lockKey = Constants.DistributedLockKey.DISTRIBUTED_LOCK_DESIGN_PLAN_CONFIRM_CALLBACK;
        //记录日志
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult.setBusinessType(Constants.AsyncRequestResultBusinessType.DESIGN_PLAN_CONFIRM);
        asyncRequestResult.setBusinessId(event.getDto().getId());
        try {
            if (DistributedCASLock.lock(lockKey, String.valueOf(event.getDto().getId()), MAXWAITTIME, MAXWAITTIME * 2)) {
                updateBatchStatusForConfirm(event.getDto(), event.getUserBy());
                asyncRequestResult.setResponseCode(ErrorCode.SUCCESS.getCode());
                asyncRequestResult.setResponseMsg(ErrorCode.SUCCESS.getMsg());
                asyncRequestResult.setResponseData("详设交付-确认审批事件异步处理成功");
            }
        } catch (Exception e) {
            logger.error("---详设交付-确认审批事件，异步处理异常", e);
            asyncRequestResult.setResponseCode(ErrorCode.ERROR.getCode());
            asyncRequestResult.setResponseMsg(ErrorCode.ERROR.getMsg());
            asyncRequestResult.setResponseData(e.getMessage());
        } finally {
            DistributedCASLock.unLock(lockKey, String.valueOf(event.getDto().getId()));
            if (asyncRequestResult.getResponseCode() == null) {
                asyncRequestResult.setResponseCode(ErrorCode.ERROR.getCode());
                asyncRequestResult.setResponseMsg(ErrorCode.ERROR.getMsg());
                asyncRequestResult.setResponseData("详设交付-确认审批事件异步处理获取锁失败");
            }
            asyncRequestResultService.add(asyncRequestResult);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBatchStatusForConfirm(MilepostDesignPlanConfirmRecordDto dto, Long userBy) {
        logger.info("DesignPlanDetailConfirmApprovalOldListener详细设计方案进度确认审批回调方法的dto：{}", JsonUtils.toString(dto));
        MilepostDesignPlanConfirmRecordDto recordDto = this.getById(dto.getId());
        MilepostDesignPlanConfirmRecordRelationDto relationQuery = new MilepostDesignPlanConfirmRecordRelationDto();
        relationQuery.setConfirmRecordId(dto.getId());
        List<MilepostDesignPlanConfirmRecordRelationDto> relationDtos = milepostDesignPlanConfirmRecordRelationService.selectList(relationQuery);
        BeanConverter.copy(dto, recordDto);
        recordDto.setStatus(dto.getStatus());
        if (null == recordDto.getConfirmAt()) {
            recordDto.setConfirmAt(new Date());
        }
        this.save(recordDto, userBy);

        if (CheckStatus.CHECKING.code().equals(dto.getStatus())) {
            for (MilepostDesignPlanConfirmRecordRelationDto relationDto : relationDtos) {
                //更新里程碑详细设计方案设计信息（milepost_design_plan_detail）状态-确认中
                updateMilepostDesignPlanDetailModuleStatus(relationDto.getDesignPlanDetailId(),
                        MilepostDesignPlanDetailModelStatus.CONFIRMING.code(), null);
            }
            //更新项目里程碑表（project_milepost） 详细方案类型里程碑模组确认中
            updateMilepostStatus(recordDto.getMilepostId(), MilepostStatus.CONFIRMING.getCode());
        } else if (CheckStatus.PASS.code().equals(dto.getStatus())) {
            String lockName = String.format("DesignPlanDetailChangeApprovalListener.handleMdpChangePassed_%s", recordDto.getProjectId());
            String value = String.valueOf(recordDto.getProjectId());
            try {
                if (DistributedCASLock.lock(lockName, value, MAX_WAIT_TIME_SYNC, MAX_WAIT_TIME_SYNC * 2)) {
                    //不知为何status被纂改，项目类型配置为“允许立项时上传详细设计方案”为是的项目，此时的状态应为“已确认”，此处覆盖一次
                    //   PS..坚持尽量不改原来逻辑的原则，只针对特定场景处理
                    if (recordDto != null && Objects.equals(recordDto.getProjectSubmit(), Boolean.TRUE)) {
                        milepostDesignPlanDetailExtMapper.updateStatusByProjectId(recordDto.getProjectId(), MilepostDesignPlanDetailModelStatus.CONFIRMED.code());
                    }
                    List<Long> projectIdList = new ArrayList<>();
                    Map<Long, String> materialIdWithNewErpCode = new HashMap<Long, String>();
                    for (MilepostDesignPlanConfirmRecordRelationDto relationDto : relationDtos) {
                        Long designPlanDetailId = relationDto.getDesignPlanDetailId();
                        milepostDesignPlanDetailService.handleRecursive(designPlanDetailId, null, materialIdWithNewErpCode);
                        logger.info("进度确认审批通过的handleRecursive的designPlanDetailId：{}，materialIdWithNewErpCode：{}", designPlanDetailId,
                                JsonUtils.toString(materialIdWithNewErpCode));

                        List<Long> requirementIdList = new ArrayList<>();
                        milepostDesignPlanService.confirmLogic(designPlanDetailId, projectIdList, requirementIdList);
                        if (ListUtils.isNotEmpty(requirementIdList)) {
                            purchaseMaterialRequirementService.batchUpdateStatus(requirementIdList);
                        }

                        //详细设计生成工单任务
                        List<MilepostDesignPlanDetailDto> list = milepostDesignPlanDetailService.getDesignPlanDetail(designPlanDetailId);
                        ticketTasksService.createTicketTasksByDesignPlanDetail(list, null, dto.getId(), "进度确认变更");
                    }

                    //批量推送需要更新erp编码的物料
                    materialExtService.saveBatchByIdWithNewErpCode(materialIdWithNewErpCode, userBy);
                    updateMilepostStatus(recordDto.getMilepostId(), MilepostStatus.CONFIRM_PASS.getCode());
                    milepostDesignPlanService.notifyThePersonInChargeOfTheMilestoneToConfirm(recordDto.getMilepostId());
                    //先生成物料编码在计算mrp
                    for (MilepostDesignPlanConfirmRecordRelationDto relationDto : relationDtos) {
                        milepostDesignPlanService.setMRP(relationDto.getDesignPlanDetailId());
                        logger.info("updateBatchStatusForConfirm审批通过最后的designPlanDetailId：{}，designPlanDetail：{}",
                                relationDto.getDesignPlanDetailId(),
                                JsonUtils.toString(milepostDesignPlanDetailMapper.selectByPrimaryKey(relationDto.getDesignPlanDetailId())));
                    }
                }
            } catch (Exception e) {
                logger.error("recursiveGenerateRequirement生成采购需求加锁失败", e);
                throw e;
            } finally {
                DistributedCASLock.unLock(lockName, value);
            }
        } else if (CheckStatus.REJECT.code().equals(dto.getStatus())) {
            for (MilepostDesignPlanConfirmRecordRelationDto relationDto : relationDtos) {
                updateMilepostDesignPlanDetailModuleStatus(relationDto.getDesignPlanDetailId(),
                        MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code(), null);
            }
            updateMilepostStatus(recordDto.getMilepostId(), MilepostStatus.CONFIRM_REJECT.getCode());
            // 撤回操作
        } else if (CheckStatus.RETURN.code().equals(dto.getStatus())) {
            for (MilepostDesignPlanConfirmRecordRelationDto relationDto : relationDtos) {
                updateMilepostDesignPlanDetailModuleStatus(relationDto.getDesignPlanDetailId(),
                        MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code(), null);
            }
            updateMilepostStatus(recordDto.getMilepostId(), MilepostStatus.DRAFT_RETURN.getCode());
        } else if (CheckStatus.MODEL_CONFIRMED_CANCEL.code().equals(dto.getStatus())) {   //流程作废
            for (MilepostDesignPlanConfirmRecordRelationDto relationDto : relationDtos) {
                updateMilepostDesignPlanDetailModuleStatus(relationDto.getDesignPlanDetailId(),
                        MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code(), null);
            }
            updateMilepostStatus(recordDto.getMilepostId(), MilepostStatus.PROCESSING.getCode());
        } else if (dto.getDeletedFlag()) { //流程删除
            for (MilepostDesignPlanConfirmRecordRelationDto relationDto : relationDtos) {
                updateMilepostDesignPlanDetailModuleStatus(relationDto.getDesignPlanDetailId(),
                        MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code(), null);
            }
            updateMilepostStatus(recordDto.getMilepostId(), MilepostStatus.PROCESSING.getCode());
        }
    }

    private MilepostDesignPlanConfirmRecordDto getById(Long id) {
        if (id == null) {
            return null;
        }
        MilepostDesignPlanConfirmRecord entity = milepostDesignPlanConfirmRecordMapper.selectByPrimaryKey(id);
        MilepostDesignPlanConfirmRecordDto dto = BeanConverter.copy(entity, MilepostDesignPlanConfirmRecordDto.class);
        packageDto(dto);
        return dto;
    }

    private void packageDto(MilepostDesignPlanConfirmRecordDto dto) {
        if (dto.getConfirmBy() != null) {
            UserInfo confirmer = CacheDataUtils.findUserById(dto.getConfirmBy());
            if (confirmer != null) {
                dto.setConfirmName(confirmer.getName());
            }
        }
    }

    private MilepostDesignPlanConfirmRecordDto save(MilepostDesignPlanConfirmRecordDto dto, Long userBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
            return this.add(dto);
        } else {
            dto.setUpdateBy(userBy);
            return this.update(dto);
        }
    }

    private MilepostDesignPlanConfirmRecordDto add(MilepostDesignPlanConfirmRecordDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        MilepostDesignPlanConfirmRecord entity = BeanConverter.copy(dto, MilepostDesignPlanConfirmRecord.class);
        milepostDesignPlanConfirmRecordMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    private MilepostDesignPlanConfirmRecordDto update(MilepostDesignPlanConfirmRecordDto dto) {
        if (!dto.getStatus().equals(CheckStatus.MODEL_CONFIRMED_DELETE.code())) {
            dto.setDeletedFlag(DeletedFlag.VALID.code());
        }
        MilepostDesignPlanConfirmRecord entity = BeanConverter.copy(dto, MilepostDesignPlanConfirmRecord.class);
        milepostDesignPlanConfirmRecordMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    /**
     * 更新里程碑详细方案状态
     *
     * @param milepostDesignPlanDetailId
     * @param moduleStatus
     */
    private void updateMilepostDesignPlanDetailModuleStatus(Long milepostDesignPlanDetailId, Integer moduleStatus, Integer status) {
        MilepostDesignPlanDetailDto designPlanDetailDto = new MilepostDesignPlanDetailDto();
        designPlanDetailDto.setId(milepostDesignPlanDetailId);
        designPlanDetailDto.setModuleStatus(moduleStatus);
        designPlanDetailDto.setStatus(status);
        designPlanDetailDto.setUpdateAt(new Date());
        //计进度确认，流程审批通过后 产生需求创建时间
        if (CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code().equals(status)) {
            designPlanDetailDto.setRequirementCreatDate(new Date());
        }
        milepostDesignPlanDetailService.save(designPlanDetailDto, null);
    }

    /**
     * 更新里程碑状态
     *
     * @param milepostId
     * @param status
     */
    private void updateMilepostStatus(Long milepostId, Integer status) {
        ProjectMilepost projectMilepost = projectMilepostMapper.selectByPrimaryKey(milepostId);
        logger.info("DesignPlanDetailConfirmApprovalOldListener详细设计方案进度确认审批回调milepostId：{}，更新前的里程碑信息：{}", milepostId, JsonUtils.toString(projectMilepost));
        //里程碑state=2(评审通过)是终态，状态不会再作更新
        if (projectMilepost != null && !Objects.equals(projectMilepost.getStatus(), MilepostStatus.PASSED.getCode())) {
            ProjectMilepost updateItem = new ProjectMilepost();
            updateItem.setId(milepostId);
            updateItem.setStatus(status);
            projectMilepostMapper.updateByPrimaryKeySelective(updateItem);
        }
    }

}
