package com.midea.pam.ctc.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.BrandMaintenance;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.cache.DesignPlanDetailImportCache;
import com.midea.pam.common.ctc.dto.MaterialAdjustDetailDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.entity.MaterialCustomDict;
import com.midea.pam.common.ctc.entity.MaterialCustomDictExample;
import com.midea.pam.common.ctc.entity.MaterialCustomDictHeader;
import com.midea.pam.common.ctc.entity.MaterialCustomDictHeaderExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChange;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChangeExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistoryExample;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectActivity;
import com.midea.pam.common.ctc.entity.ProjectActivityExample;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample;
import com.midea.pam.common.ctc.vo.MerielSubclassVO;
import com.midea.pam.common.enums.BudgetConfigEnums;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.KukaMaterialType;
import com.midea.pam.common.enums.MaterialAdjustEnum;
import com.midea.pam.common.enums.MaterialCodeRuleEnum;
import com.midea.pam.common.enums.MilepostDesignPlanDetailKukaWbsBatchImportExcelOperationTypeEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.RequirementStatusEnum;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.cache.DesignPlanDetailImportCacheUtils;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.MaterialCategoryEnum;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.MaterialCustomDictExtMapper;
import com.midea.pam.ctc.mapper.MaterialCustomDictHeaderMapper;
import com.midea.pam.ctc.mapper.MaterialCustomDictMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailChangeMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailSubmitHistoryExtMapper;
import com.midea.pam.ctc.mapper.OrganizationCustomDictExtMapper;
import com.midea.pam.ctc.mapper.ProjectActivityMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsExtMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.ErpCodeRuleService;
import com.midea.pam.ctc.service.MaterialAdjustExtService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailSubmitHistoryService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Description
 * Created by liuqing
 * Date 2022/7/22 16:38
 */
public class MaterialAdjustExtServiceImpl implements MaterialAdjustExtService {

    private static final Logger logger = LoggerFactory.getLogger(MaterialAdjustExtServiceImpl.class);

    private static final Integer EACH_THREAD_DATA_NUM = 150;

    private final static String MACHINED_PART_TYPE = "jiagongjian_type";

    private final static String MATERIAL_TYPE = "material_type";

    // 多层级导入相关常量
    private static final Pattern PARENT_NUM_PATTERN = Pattern.compile("^\\d+(\\.\\d+)*$");
    private static long tempIdCounter = 1000000L;

    @Resource
    private BasedataExtService basedataExtService;

    @Resource
    private MaterialExtService materialExtService;

    @Resource
    private ErpCodeRuleService erpCodeRuleService;

    @Resource
    private OrganizationCustomDictService organizationCustomDictService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;

    @Resource
    private MaterialCustomDictHeaderMapper materialCustomDictHeaderMapper;

    @Resource
    private MaterialCustomDictMapper materialCustomDictMapper;

    @Resource
    private MilepostDesignPlanDetailSubmitHistoryExtMapper milepostDesignPlanDetailSubmitHistoryExtMapper;

    @Resource
    private ProjectActivityMapper projectActivityMapper;

    @Resource
    private MilepostDesignPlanDetailSubmitHistoryService milepostDesignPlanDetailSubmitHistoryService;

    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;

    @Qualifier("importDesignPlanDetailMaterialExecutor")
    @Resource
    private ThreadPoolTaskExecutor importDesignPlanDetailMaterialExecutor;

    @Resource
    private OrganizationCustomDictExtMapper organizationCustomDictExtMapper;

    @Resource
    private MaterialCustomDictExtMapper materialCustomDictExtMapper;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private ProjectTypeMapper projectTypeMapper;

    @Resource
    private MilepostDesignPlanDetailChangeMapper milepostDesignPlanDetailChangeMapper;

    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;

    @Resource
    private ProjectWbsReceiptsExtMapper projectWbsReceiptsExtMapper;


    @Override
    public DataResponse<List<MilepostDesignPlanDetailDto>> validImportDesignPlanDetailMaterial(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos,
                                                                                               Long storageId, Long unitId,
                                                                                               List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo,
                                                                                               Long markId, String wbsSummaryCode, Boolean wbsEnabled,
                                                                                               Long projectId, Boolean isChange, Long detailId,
                                                                                               Boolean skipMultiLevelCheck) {
        long importStartTime = System.currentTimeMillis();

        // 多层级检查逻辑：只有当skipMultiLevelCheck为false时才执行多层级检查
        if (Boolean.FALSE.equals(skipMultiLevelCheck)) {
            logger.info("开始执行多层级检查，数据量：{}", kukaDetailExcelVos.size());

            // 预处理：为导入下级场景设置wbsPath
            preprocessDataForSubLevelImport(kukaDetailExcelVos, wbsSummaryCode);

            // 检查是否为多层级导入场景
            boolean isMultiLevel = isMultiLevelImport(kukaDetailExcelVos);

            if (isMultiLevel) {
                logger.info("检测到多层级导入场景，开始处理多层级逻辑");

                // 执行多层级校验和关系构建
                boolean multiLevelProcessResult = processMultiLevelImport(kukaDetailExcelVos, projectId);

                if (!multiLevelProcessResult) {
                    // 多层级处理失败，返回错误
                    logger.warn("多层级处理失败，返回校验错误");
                    DataResponse<List<MilepostDesignPlanDetailDto>> response = Response.dataResponse();
                    response.setMsg(MaterialCodeRuleEnum.KUKA2022.getCode());
                    response.setData(kukaDetailExcelVos);
                    return response;
                }

                // 多层级导入：为第一级详设设置parentId为前端传入的detailId
                setParentIdForMultiLevelFirstLevel(kukaDetailExcelVos, detailId);

                // 混合场景：为单层导入的详设也设置parentId为前端传入的detailId
                setParentIdForSingleLevelInMixedImport(kukaDetailExcelVos, detailId);

                logger.info("多层级处理完成，继续执行原有导入逻辑");
            } else {
                // 非多层级导入：为所有详设设置parentId为前端传入的detailId
                setParentIdForNonMultiLevel(kukaDetailExcelVos, detailId);
            }
        } else {
            logger.info("跳过多层级检查（来自批量导入调用），数据量：{}", kukaDetailExcelVos.size());
        }

        Guard.notNull(projectId, "项目ID为空");
        Project project = projectMapper.selectByPrimaryKey(projectId);
        Guard.notNull(project, String.format("项目ID：%s对应的项目不存在", projectId));
        Guard.notNull(project.getType(), String.format("项目ID：%s对应的项目类型为空", projectId));
        ProjectType projectType = projectTypeMapper.selectByPrimaryKey(project.getType());
        Guard.notNull(projectType, String.format("项目ID：%s对应的项目类型不存在", projectId));
        String budgetConfig = projectType.getBudgetConfig();
        JSONArray materiel = JSON.parseObject(budgetConfig).getJSONArray(BudgetConfigEnums.MATERIEL.getCode());
        // 项目类型设置的预算设置的字段设置的计划交付日期 的开关是否开启
        boolean isOpenDeliveryTime = false;
        for (int i = 0; i < materiel.size(); i++) {
            JSONObject jsonObject = materiel.getJSONObject(i);
            if (null == jsonObject) {
                continue;
            }
            String code = Optional.ofNullable(jsonObject.get("code")).map(Object::toString).orElse(null);
            String value = Optional.ofNullable(jsonObject.get("value")).map(Object::toString).orElse(null);
            if (Objects.equals("fieldSetByMaterial", code) && Objects.equals("[1]", value)) {
                isOpenDeliveryTime = true;
                break;
            }
        }

        // 查询当前使用单位的物料编码规则
        String currentMaterialCodeRule = materialExtService.getMaterialCodeRule(SystemContext.getUnitId());

        //获得所有使用当前物料编码规则的使用单位
        List<Unit> units = materialExtService.getAllUnitByUsingMaterialCodeRule(currentMaterialCodeRule);

        //根据使用单位获得对应所有库存组织
        String url = String.format("%sorganizationRel/queryByUnitId", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, units.stream().map(Unit::getId)
                .collect(Collectors.toList()), String.class);
        List<OrganizationRel> organizationRels = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<OrganizationRel>>>() {
        }).getData();
        List<Long> organizationIds = new ArrayList<>();
        Map<Long, String> organizationMap = new HashMap<>();
        for (OrganizationRel organizationRel : organizationRels) {
            Long organizationId = organizationRel.getOrganizationId();
            if (!organizationIds.contains(organizationId)) {
                organizationIds.add(organizationId);
                organizationMap.put(organizationId, organizationRel.getOrganizationName());
            }
        }

        // 物料编码规则  大+中+小类
        List<MerielSubclassVO> materialClasses = erpCodeRuleService.findErpCodeRuleByRuleName(currentMaterialCodeRule);
        Guard.notNullOrEmpty(materialClasses, "物料编码规则" + MaterialCodeRuleEnum.KUKA2022.getName() + "尚未维护数据,请先维护");
        Map<String, MerielSubclassVO> materialClassMap = new HashMap<>();
        for (MerielSubclassVO merielSubclassVO : materialClasses) {
            String erpCodeRuleKey = getErpCodeRuleKey(merielSubclassVO.getBigValue(), merielSubclassVO.getMiddileValue(),
                    merielSubclassVO.getValue());
            materialClassMap.put(erpCodeRuleKey, merielSubclassVO);
        }

        // 非标件大类
        List<String> nonStandardMaterialType = basedataExtService.getLtcDict(DictType.MATERIAL_TYPE_NON_STANDARD.code(), null, null).stream()
                .map(Dict::getName).distinct().collect(Collectors.toList());

        // 获取计量单位
        List<DictDto> unitDicts = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Map<String, String> unitNameCodeMap = new HashMap<>();
        Map<String, String> unitCodeNameMap = new HashMap<>();
        for (DictDto dictDto : unitDicts) {
            String code = dictDto.getCode();
            String name = dictDto.getName();
            unitNameCodeMap.put(name, code);
            unitCodeNameMap.put(code, name);
        }

        // 获取加工分类字典值
        List<String> machinedPartTypeList = basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null).stream().map(Dict::getName)
                .distinct().collect(Collectors.toList());
        // 获取备件标识组织参数配置
        Set<String> orSparePartsMaskSet = organizationCustomDictService.queryByName(Constants.SPAREPARTS_MASK, SystemContext.getUnitId(),
                OrgCustomDictOrgFrom.COMPANY);
        Guard.notNullOrEmpty(orSparePartsMaskSet, "备件标识需要在组织参数中维护");

        // 获取 需求类型关联是否外包 组织参数配置
        Set<String> requirementTypeExtIsSet = new HashSet<>();
        Map<String,Boolean> requirementTypeExtIsMap = new HashMap<>();
        if (wbsEnabled) {
            requirementTypeExtIsSet.addAll(organizationCustomDictService.queryByName(Constants.REQUIREMENTTYPE_EXTIS, SystemContext.getUnitId(),
                    OrgCustomDictOrgFrom.COMPANY));
            Guard.notNullOrEmpty(requirementTypeExtIsSet, "需求类型关联是否外包需要在组织参数中维护");
            // 对 requirementTypeExtIsSet 转换MAP，格式：标准设备_是，,MRO_否 ，将_前面的内容设置key,后面的内容如果是是则设置value为true，否则为false
            for (String requirementTypeExtIs : requirementTypeExtIsSet) {
                String[] split = requirementTypeExtIs.split("_");
                if (split.length == 2) {
                    requirementTypeExtIsMap.put(split[0], "是".equals(split[1]));
                }
            }
        }

        // 获取 需求类型 字典值
        List<DictDto> requirementTypeDicts = basedataExtService.getLtcDict(DictType.DESIGN_REQUIREMENT_TYPE.code(), null, null);
        Map<String, String> requirementTypeMap = requirementTypeDicts.stream().collect(Collectors.toMap(DictDto::getName, DictDto::getCode));
        // 获取物料类型及编码字典
        Byte b = 1;
        Map<String, String> materialTypeMap = basedataExtService.getLtcDict(MATERIAL_TYPE, null, null).stream()
                .filter(o -> !Objects.equals(b, o.getOrderNum())).collect(Collectors.toMap(DictDto::getName, DictDto::getCode, (key1, key2) -> key2));
        //获取品牌列表数据
        List<BrandMaintenance> brandMaintenanceList = basedataExtService.getBrandMaintenance();
        Guard.notNullOrEmpty(brandMaintenanceList, "请先维护品牌信息");

        // 物料编码规则字符忽略
        List<String> characterIgnoreList = basedataExtService.getLtcDict(DictType.MATERIAL_CODE_RULE_CHARACTER_IGNORE.code(), null, null).stream()
                .map(Dict::getName).distinct().collect(Collectors.toList());

        List<String> nonStandardMaterials = new ArrayList<>();
        List<String> standardMaterials = new ArrayList<>();
        List<String> checkRepeatPamWBSDeliveryTimeList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, List<MilepostDesignPlanDetailDto>> forCheckMaps = new HashMap<>();
        if (CollectionUtils.isNotEmpty(designPlanDetailDtoForWebInfo)) {
            if (Boolean.TRUE.equals(isChange)) {
                forCheckMaps.putAll(designPlanDetailDtoForWebInfo.stream().collect(Collectors.groupingBy(a -> {
                    StringBuilder group = new StringBuilder(a.getPamCode() + ":" + a.getWbsSummaryCode() + ":");
                    if (StringUtils.isNotEmpty(a.getDeliveryTimeStr())) {
                        try {
                            group.append(sdf.format(sdf.parse(a.getDeliveryTimeStr())));
                        } catch (Exception e) {
                            throw new ApplicationBizException("无法解析日期格式[" + a.getDeliveryTimeStr() + "]");
                        }
                    }
                    return group.toString();
                })));
                checkRepeatPamWBSDeliveryTimeList.addAll(new ArrayList<>(forCheckMaps.keySet()));
            } else {
                checkRepeatPamWBSDeliveryTimeList.addAll(designPlanDetailDtoForWebInfo.stream().map(webInfo -> {
                    try {
                        return webInfo.getPamCode() + ":" + webInfo.getWbsSummaryCode() +
                                ":" + ((ObjectUtils.isEmpty(webInfo.getDeliveryTimeStr())) ? ""
                                : sdf.format(sdf.parse(webInfo.getDeliveryTimeStr())));
                    } catch (Exception e) {
                        throw new ApplicationBizException("无法解析日期格式[" + webInfo.getDeliveryTimeStr() + "]");
                    }
                }).collect(Collectors.toList()));
            }

            designPlanDetailDtoForWebInfo.forEach(a -> {
                if (StringUtils.isNotEmpty(a.getMaterialClassification()) && nonStandardMaterialType.contains(a.getMaterialClassification())) {
                    if (StringUtils.isNotEmpty(a.getFigureNumber()) && Objects.equals(wbsSummaryCode, a.getWbsSummaryCode())) {
                        nonStandardMaterials.add(a.getFigureNumber());
                    }
                } else if (StringUtils.isNotEmpty(a.getMaterialClassification()) && !nonStandardMaterialType.contains(a.getMaterialClassification())) {
                    if (StringUtils.isNotEmpty(a.getBrand())) {
                        List<BrandMaintenance> maintenanceList = brandMaintenanceList.stream().filter(brand ->
                                        (Objects.equals(brand.getBrandNameCn(), a.getBrand()) || Objects.equals(brand.getBrandNameEn(), a.getBrand())
                                                || Objects.equals(brand.getBrandName(), a.getBrand()))
                                                && (Boolean.FALSE.equals(brand.getDeletedFlag()) || Objects.equals(0, brand.getBrandStatus())))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(maintenanceList) && StringUtils.isNotEmpty(a.getModel())
                                && Objects.equals(wbsSummaryCode, a.getWbsSummaryCode())) {
                            standardMaterials.add(maintenanceList.get(0).getBrandName() + ":"
                                    + removeCharacterIgnoreList(a.getModel(), characterIgnoreList));
                        }
                    }
                }
            });
        }

        //详设相关重复字段
        MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
        milepostDesignPlanDetailExample.createCriteria().andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andDeletedFlagEqualTo(false);
        List<MilepostDesignPlanDetail> designPlanDetails = milepostDesignPlanDetailService.selectByExample(milepostDesignPlanDetailExample);
        if (ListUtils.isNotEmpty(designPlanDetails)) {
            for (MilepostDesignPlanDetail designPlanDetail : designPlanDetails) {
                if (!Boolean.FALSE.equals(designPlanDetail.getWbsLastLayer())) {
                    String repeatKey = designPlanDetail.getPamCode() + ":" + designPlanDetail.getWbsSummaryCode() +
                            ":" + ((Objects.isNull(designPlanDetail.getDeliveryTime())) ? "" : sdf.format(designPlanDetail.getDeliveryTime()));
                    checkRepeatPamWBSDeliveryTimeList.add(repeatKey);

                    if (forCheckMaps.containsKey(repeatKey)) {
                        forCheckMaps.get(repeatKey).add(BeanConverter.copy(designPlanDetail, MilepostDesignPlanDetailDto.class));
                    } else {
                        List<MilepostDesignPlanDetailDto> list = new ArrayList<>();
                        list.add(BeanConverter.copy(designPlanDetail, MilepostDesignPlanDetailDto.class));
                        forCheckMaps.put(repeatKey, list);
                    }
                }
            }
        }

        //详设发布且不处于作废状态的也需要查询
        List<MilepostDesignPlanDetailSubmitHistory> submitHistories =
                milepostDesignPlanDetailSubmitHistoryExtMapper.selectRepeatPamWBSDeliveryTimeList(projectId, wbsSummaryCode);
        if (ListUtils.isNotEmpty(submitHistories)) {
            for (MilepostDesignPlanDetailSubmitHistory submitHistory : submitHistories) {
                String repeatKey = submitHistory.getPamCode() + ":" + submitHistory.getWbsSummaryCode() +
                        ":" + ((Objects.isNull(submitHistory.getDeliveryTime())) ? "" : sdf.format(submitHistory.getDeliveryTime()));
                checkRepeatPamWBSDeliveryTimeList.add(repeatKey);

                if (forCheckMaps.containsKey(repeatKey)) {
                    forCheckMaps.get(repeatKey).add(BeanConverter.copy(submitHistory, MilepostDesignPlanDetailDto.class));
                } else {
                    List<MilepostDesignPlanDetailDto> list = new ArrayList<>();
                    list.add(BeanConverter.copy(submitHistory, MilepostDesignPlanDetailDto.class));
                    forCheckMaps.put(repeatKey, list);
                }
            }
        }

        //详设变更且不处于作废状态的也需要查询
        MilepostDesignPlanDetailChangeExample detailChangeExample = new MilepostDesignPlanDetailChangeExample();
        detailChangeExample.createCriteria().andProjectIdEqualTo(projectId).andWbsSummaryCodeEqualTo(wbsSummaryCode).andDeletedFlagEqualTo(false);
        List<MilepostDesignPlanDetailChange> designPlanDetailChangeList =
                milepostDesignPlanDetailChangeMapper.selectByExample(detailChangeExample);
        if (ListUtils.isNotEmpty(designPlanDetailChangeList)) {
            List<Long> changeReceiptsIds = designPlanDetailChangeList.stream().map(MilepostDesignPlanDetailChange::getProjectWbsReceiptsId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (ListUtils.isNotEmpty(changeReceiptsIds)) {
                ProjectWbsReceiptsExample projectWbsReceiptsExample = new ProjectWbsReceiptsExample();
                projectWbsReceiptsExample.createCriteria().andIdIn(changeReceiptsIds)
                        .andRequirementStatusNotIn(Lists.newArrayList(RequirementStatusEnum.CANCEL.getCode(),
                                RequirementStatusEnum.PROCESS.getCode()));
                List<ProjectWbsReceipts> receipts = projectWbsReceiptsService.selectByExample(projectWbsReceiptsExample);
                if (ListUtils.isNotEmpty(receipts)) {
                    List<Long> receiptsIds = receipts.stream().map(ProjectWbsReceipts::getId).collect(Collectors.toList());
                    for (MilepostDesignPlanDetailChange designPlanDetailChange : designPlanDetailChangeList) {
                        Long projectWbsReceiptsId = designPlanDetailChange.getProjectWbsReceiptsId();
                        if (!receiptsIds.contains(projectWbsReceiptsId) || Boolean.FALSE.equals(designPlanDetailChange.getWbsLastLayer())) {
                            continue;
                        }

                        String repeatKey = designPlanDetailChange.getPamCode() + ":" + designPlanDetailChange.getWbsSummaryCode() + ":" +
                                ((Objects.isNull(designPlanDetailChange.getDeliveryTime())) ? "" :
                                        sdf.format(designPlanDetailChange.getDeliveryTime()));
                        checkRepeatPamWBSDeliveryTimeList.add(repeatKey);

                        if (forCheckMaps.containsKey(repeatKey)) {
                            forCheckMaps.get(repeatKey).add(BeanConverter.copy(designPlanDetailChange, MilepostDesignPlanDetailDto.class));
                        } else {
                            List<MilepostDesignPlanDetailDto> list = new ArrayList<>();
                            list.add(BeanConverter.copy(designPlanDetailChange, MilepostDesignPlanDetailDto.class));
                            forCheckMaps.put(repeatKey, list);
                        }
                    }
                }
            }
        }

        long executeStartTime = System.currentTimeMillis();
        logger.info("validImportDesignPlanDetailMaterial的execute before执行时间：{}", executeStartTime - importStartTime);

        DataResponse<List<MilepostDesignPlanDetailDto>> response = Response.dataResponse();
        boolean result = true;
        List<String> indexCheckList = new ArrayList<>();
        List<String> pamCodeList = new ArrayList<>();
        List<String> erpCodeList = new ArrayList<>();
        List<String> figureNumberList = new ArrayList<>();
        List<String> brandList = new ArrayList<>();
        List<String> modelList = new ArrayList<>();
        List<String> nonStandardMaterialClassificationList = new ArrayList<>();
        List<String> nonStandardFigureNumberList = new ArrayList<>();
        List<String> standardMaterialClassificationList = new ArrayList<>();
        List<String> standardBrandList = new ArrayList<>();
        List<String> standardModelList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto detailDto : kukaDetailExcelVos) {
            List<String> validResultList = StringUtils.isEmpty(detailDto.getValidResult()) ? new ArrayList<>()
                    : Arrays.asList(detailDto.getValidResult().split("，"));
            String pamCode = detailDto.getPamCode();
            if (StringUtils.isNotEmpty(pamCode) && !pamCodeList.contains(pamCode)) {
                pamCodeList.add(pamCode);
            }
            String erpCode = detailDto.getErpCode();
            if (StringUtils.isNotEmpty(erpCode) && !erpCodeList.contains(erpCode)) {
                erpCodeList.add(erpCode);
            }
            if (StringUtils.isNotEmpty(pamCode) && StringUtils.isNotEmpty(erpCode)) {
                result = false;
                validResultList.add("Pam编码与Erp码不能同时填写");
            }
            String figureNumber = detailDto.getFigureNumber();
            if (StringUtils.isNotEmpty(figureNumber) && !figureNumberList.contains(figureNumber)) {
                figureNumberList.add(figureNumber);
            }

            // 图号
            Integer figureNumberOracleLength = getOracleLengthFromMysqlVarchar(detailDto.getFigureNumber());
            // 型号/规格
            Integer modelOracleLength = getOracleLengthFromMysqlVarchar(detailDto.getModel());
            if (figureNumberOracleLength + modelOracleLength > 70) {
                result = false;
                validResultList.add("图号和型号字段的字符之和不能超过70");
            }
//            // 表面处理
//            String materialProcessing = detailDto.getMaterialProcessing();
//            int materialProcessingLength = StringUtils.isNotEmpty(materialProcessing) ? materialProcessing.length() : 0;
//            if (materialProcessingLength > 32) {
//                result = false;
//                validResultList.add("表面处理字段的字符不能超过32");
//            }
            // 备注
            String description = detailDto.getDescription();
            int descriptionLength = StringUtils.isNotEmpty(description) ? description.length() : 0;
            if (descriptionLength > 1000) {
                result = false;
                validResultList.add("备注字段的字符不能超过1000");
            }

            String numberStr = detailDto.getNumberStr();
            if (StringUtils.isNotEmpty(numberStr)) {
                if (BigDecimalUtils.isBigDecimal(numberStr)) {
                    detailDto.setNumber(BigDecimalUtils.stringToBigDecimal(numberStr));
                } else {
                    result = false;
                    validResultList.add("数量有误");
                }
            } else {
                result = false;
                validResultList.add("数量不能为空");
            }

//            String unitWeightStr = detailDto.getUnitWeightStr();
//            if (StringUtils.isNotEmpty(unitWeightStr)) {
//                if (BigDecimalUtils.isBigDecimal(unitWeightStr)) {
//                    detailDto.setUnitWeight(BigDecimalUtils.stringToBigDecimal(unitWeightStr));
//                } else {
//                    result = false;
//                    validResultList.add("单件重量(Kg)有误");
//                }
//            }

            if (StringUtils.isEmpty(detailDto.getSerialNumber())) {
                result = false;
                validResultList.add("序号不能为空");
            } else {
                if (!NumberUtil.isNumber(detailDto.getSerialNumber())) {
                    result = false;
                    validResultList.add("序号只能填数字");
                } else if (indexCheckList.contains(detailDto.getSerialNumber())) {
                    result = false;
                    validResultList.add("序号不能重复");
                } else {
                    indexCheckList.add(detailDto.getSerialNumber());
                }
            }
            if (StringUtils.isBlank(detailDto.getMaterialCategory())) {
                result = false;
                validResultList.add("物料类型不能为空");
            } else if (!materialTypeMap.containsKey(detailDto.getMaterialCategory())) {
                result = false;
                validResultList.add("物料类型只允许为:看板物料，外购物料，装配件，虚拟件");
            }
            if (Boolean.TRUE.equals(wbsEnabled) && ("外购物料".equals(detailDto.getMaterialCategory()) || "看板物料".equals(detailDto.getMaterialCategory()))) {
                // 是否急件/需求类型：库卡柔性单位，当物料类型为“外购物料”、“看板物料”必填，否则验证时报错，不能导入成功；其他单位下非必填
                if (StringUtils.isEmpty(detailDto.getDispatchIsStr())) {
                    result = false;
                    validResultList.add("外购物料/看板物料是否急件必填");
                }

                if (StringUtils.isEmpty(detailDto.getRequirementTypeStr())) {
                    result = false;
                    validResultList.add("外购物料/看板物料需求类型不能为空");
                }else{
                    //设置是否外包
                    detailDto.setExtIs(requirementTypeExtIsMap.get(detailDto.getRequirementTypeStr()));
                }
            }

            String deliveryTimeStr = detailDto.getDeliveryTimeStr();
            // 计划交货日期=不打勾   外购物料、看板物料 需要填写 物料到货日期；计划交货日期=打钩    外购物料、看板物料  不需要填写 物料到货日期
            if (Boolean.FALSE.equals(isOpenDeliveryTime) && ("外购物料".equals(detailDto.getMaterialCategory()) || "看板物料".equals(detailDto.getMaterialCategory()))) {
                if (StringUtils.isEmpty(deliveryTimeStr)) {
                    result = false;
                    validResultList.add("外购物料/看板物料,物料到货日期不能为空");
                }
            }
            if (StringUtils.isNotEmpty(deliveryTimeStr)) {
                if (DateUtils.isValidDate(deliveryTimeStr, DateUtils.FORMAT_SHORT)) {
                    detailDto.setDeliveryTime(DateUtils.parse(deliveryTimeStr, DateUtils.FORMAT_SHORT));
                } else {
                    result = false;
                    validResultList.add("物料到货日期格式不正确");
                }
            }

            // 是否急件/需求类型：库卡柔性单位，当物料类型为“外购物料”、“看板物料”必填，否则验证时报错，不能导入成功；其他单位下非必填
            String dispatchIsStr = detailDto.getDispatchIsStr();
            if (StringUtils.isNotEmpty(dispatchIsStr)) {
                if ("是".equals(dispatchIsStr)) {
                    detailDto.setDispatchIs(true);
                } else if ("否".equals(dispatchIsStr)) {
                    detailDto.setDispatchIs(false);
                } else {
                    result = false;
                    validResultList.add("外购物料/看板物料是否急件格式不正确");
                }
            }


            //需求类型：3：安装分包，4：调试、测量分包，5：设计分包，6：标准设备，7：定制设备整包，8：设备备件，9：项目整包，10：机加整包，11：机加散件，12：MRO，13：标准件
            if (StringUtils.isNotEmpty(detailDto.getRequirementTypeStr())) {


                String requirementType = requirementTypeMap.get(detailDto.getRequirementTypeStr());
                if (StringUtils.isNotEmpty(requirementType)) {
                    try {
                        detailDto.setRequirementType(Integer.parseInt(requirementType));
                    } catch (NumberFormatException e) {
                        result = false;
                        validResultList.add("[" + detailDto.getRequirementTypeStr() + "]数据字典需求类型配置格式不正确");
                    }
                } else {
                    result = false;
                    validResultList.add("[" + detailDto.getRequirementTypeStr() + "]未定义的需求类型");
                }
            }

//            if (StringUtils.isNotEmpty(detailDto.getPlanDesigner())) {
//                UserInfo userByMip = CacheDataUtils.findUserByMip(detailDto.getPlanDesigner());
//                if (Objects.isNull(userByMip)) {
//                    result = false;
//                    validResultList.add("该设计人员不存在");
//                }
//            } else {
//                detailDto.setPlanDesigner(SystemContext.getUserMip());
//            }
            //如果品牌不为空则转大写
            if (StringUtils.isNotEmpty(detailDto.getBrand())) {
                if (detailDto.getBrand().length() > 150) {
                    result = false;
                    validResultList.add("品牌字符长度不能超过150");
                }
                detailDto.setBrand(detailDto.getBrand().toUpperCase(Locale.ROOT));
                if (Objects.equals(detailDto.getBrand(), "无") || Objects.equals(detailDto.getBrand(), "NONE")) {
                    result = false;
                    validResultList.add("品牌不能为无或NONE");
                }
            }

            //材质内容长度校验
//            if (StringUtils.isNotEmpty(detailDto.getMaterial())) {
//                if (detailDto.getMaterial().length() > 32) {
//                    result = false;
//                    validResultList.add("材质字段的字符不能超过32");
//                }
//            }

            String finalBrandName = "";
            String brandName = detailDto.getBrand();
            if (StringUtils.isNotEmpty(brandName)) {
                List<BrandMaintenance> maintenanceList = brandMaintenanceList.stream().filter(brand ->
                        (Objects.equals(brand.getBrandNameCn(), brandName) || Objects.equals(brand.getBrandNameEn(), brandName)
                                || Objects.equals(brand.getBrandName(), brandName))
                                && (Boolean.FALSE.equals(brand.getDeletedFlag()) || Objects.equals(0, brand.getBrandStatus()))
                ).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(maintenanceList)) {
                    result = false;
                    validResultList.add("品牌名称：" + brandName + "在品牌表中不存在");
                } else {
                    finalBrandName = maintenanceList.get(0).getBrandName();
                    // 设置最终的品牌名称
                    detailDto.setBrand(finalBrandName);
                    if (!brandList.contains(finalBrandName)) {
                        brandList.add(finalBrandName);
                    }
                }
            }
            detailDto.setValidResult(Joiner.on("，").join(validResultList));

            String model = detailDto.getModel();
            if (StringUtils.isNotEmpty(model) && !modelList.contains(model)) {
                modelList.add(model);
            }

            // 1.根据模板大类判断标准/非标（有个数据字典，在里面的属于非标）

            // 2.查material
            //2.1.非标大类（在数据字典material_type_non-standard中）：
            //2.1.1.图号为空，不查询（已存在PAM编码、已存在ERP编码留空）
            //2.1.2.material_classification（在数据字典material_type_non-standard中）+图号+organization_id（本组织）+delist_flag=0，delete_flag=0
            // 2.2.标准件大类（不在数据字典material_type_non-standard中）：
            //2.2.1.品牌 或 型号/规格，其中一个1字段为空，不查询（已存在PAM编码、已存在ERP编码留空）
            //2.2.2.material_classification（不在数据字典material_type_non-standard中）+品牌+型号/规格+organization_id（本组织）+delist_flag=0，delete_flag=0

            //3.返回符合条件的PAM编码和ERP编码，如果存在多个，通过"；"拼接
            String materialClassification = detailDto.getMaterialClassification();
            if (StringUtils.isEmpty(materialClassification)) {
                continue;
            }
            if (nonStandardMaterialType.contains(materialClassification)) {
                if (StringUtils.isEmpty(figureNumber)) {
                    continue;
                }
                if (!nonStandardMaterialClassificationList.contains(materialClassification)) {
                    nonStandardMaterialClassificationList.add(materialClassification);
                }
                if (!nonStandardFigureNumberList.contains(figureNumber)) {
                    nonStandardFigureNumberList.add(figureNumber);
                }
            } else {
                if (StringUtils.isEmpty(finalBrandName) || StringUtils.isEmpty(model)) {
                    continue;
                }
                if (!standardMaterialClassificationList.contains(materialClassification)) {
                    standardMaterialClassificationList.add(materialClassification);
                }
                if (!standardBrandList.contains(finalBrandName)) {
                    standardBrandList.add(finalBrandName);
                }
                if (!standardModelList.contains(model)) {
                    standardModelList.add(model);
                }
            }
        }
        if (!result) {
            response.setMsg(MaterialCodeRuleEnum.KUKA2022.getCode());
            response.setData(kukaDetailExcelVos);
            return response;
        }

        // 根据模板大类判断标准/非标（有个数据字典，在里面的属于非标）
        Map<String, List<MaterialDto>> nonStandardMaterialMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(nonStandardMaterialClassificationList) && CollectionUtils.isNotEmpty(nonStandardFigureNumberList)) {
            List<MaterialDto> nonStandardMaterialList = materialExtService.getNonStandardMaterial(storageId, nonStandardMaterialClassificationList,
                    nonStandardFigureNumberList);
            nonStandardMaterialMap.putAll(CollectionUtils.isEmpty(nonStandardMaterialList) ? new HashMap<>()
                    : nonStandardMaterialList.stream().collect(Collectors.groupingBy(e -> getNonStandardMaterialKey(e.getMaterialClassification(),
                    e.getFigureNumber()))));
        }
        Map<String, List<MaterialDto>> standardMaterialMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(standardMaterialClassificationList) && CollectionUtils.isNotEmpty(standardBrandList)
                && CollectionUtils.isNotEmpty(standardModelList)) {
            // KUKA2022 规则下标准件的 model 忽略一些特殊符号
            List<MaterialDto> standardMaterialList = materialExtService.getStandardMaterial(storageId, standardMaterialClassificationList,
                    standardBrandList, new ArrayList<>());
            standardMaterialMap.putAll(CollectionUtils.isEmpty(standardMaterialList) ? new HashMap<>()
                    : standardMaterialList.stream().collect(Collectors.groupingBy(e -> getStandardMaterialKey(e.getMaterialClassification(),
                    e.getBrand(), e.getModel(), characterIgnoreList))));
        }
        logger.info("validImportDesignPlanDetailMaterial的dispatchIsMap：{}", JsonUtils.toString(nonStandardMaterialMap));
        logger.info("validImportDesignPlanDetailMaterial的dispatchIsMap：{}", JsonUtils.toString(standardMaterialMap));

        Map<String, List<MaterialDto>> pamAdjustMaterialsMap = new HashMap<>();
        Map<String, List<MaterialDto>> erpAdjustMaterialsMap = new HashMap<>();
        Map<String, List<MaterialDto>> adjustCodeForKukaCodeRuleMap = new HashMap<>();
        //通过pamCode和erpCode查询所有物料变更表中的数据
        List<MaterialDto> allPamCodeAndErpCodeAdjustMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pamCodeList) || CollectionUtils.isNotEmpty(erpCodeList)) {
            allPamCodeAndErpCodeAdjustMaterials.addAll(materialExtService.getAllPamCodeAndErpCodeAdjustMaterial(organizationIds, pamCodeList,
                    erpCodeList));
        }
        for (MaterialDto materialDto : allPamCodeAndErpCodeAdjustMaterials) {
            String pamCode = materialDto.getPamCode();
            if (StringUtils.isNotEmpty(pamCode)) {
                if (pamAdjustMaterialsMap.containsKey(pamCode)) {
                    pamAdjustMaterialsMap.get(pamCode).add(materialDto);
                } else {
                    pamAdjustMaterialsMap.put(pamCode, Lists.newArrayList(materialDto));
                }
            }
            String erpCode = materialDto.getErpCode();
            if (StringUtils.isNotEmpty(erpCode)) {
                if (erpAdjustMaterialsMap.containsKey(erpCode)) {
                    erpAdjustMaterialsMap.get(erpCode).add(materialDto);
                } else {
                    erpAdjustMaterialsMap.put(erpCode, Lists.newArrayList(materialDto));
                }
            }
        }
        //通过figureNumber和brand和model查询所有物料变更表中的数据
        List<MaterialDto> allFigureNumberAndBrandAndModelAdjustMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(figureNumberList) || CollectionUtils.isNotEmpty(brandList) || CollectionUtils.isNotEmpty(modelList)) {
            // KUKA2022 规则下标准件的 model 忽略一些特殊符号
            allFigureNumberAndBrandAndModelAdjustMaterials.addAll(materialExtService.getAllFigureNumberAndBrandAndModelAdjustMaterial(organizationIds,
                    figureNumberList, brandList, new ArrayList<>()));
        }
        for (MaterialDto materialDto : allFigureNumberAndBrandAndModelAdjustMaterials) {
            String adjustCodeForKukaCodeRuleKey = getAdjustCodeForKukaCodeRuleKey(materialDto.getFigureNumber(), materialDto.getBrand(),
                    materialDto.getModel(), nonStandardMaterialType, materialDto.getMaterialClassification(), characterIgnoreList);
            if (adjustCodeForKukaCodeRuleMap.containsKey(adjustCodeForKukaCodeRuleKey)) {
                adjustCodeForKukaCodeRuleMap.get(adjustCodeForKukaCodeRuleKey).add(materialDto);
            } else {
                adjustCodeForKukaCodeRuleMap.put(adjustCodeForKukaCodeRuleKey, Lists.newArrayList(materialDto));
            }
        }

        Map<String, List<MaterialDto>> pamMaterialsMap = new HashMap<>();
        Map<String, List<MaterialDto>> erpMaterialsMap = new HashMap<>();
        Map<String, List<MaterialDto>> pamDelistAndDeleteMaterialMap = new HashMap<>();
        Map<String, List<MaterialDto>> erpDelistAndDeleteMaterialMap = new HashMap<>();
        Map<String, List<MaterialDto>> materialForCheckRepeatMap = new HashMap<>();
        Map<String, List<MaterialDto>> materialForCheckAdjustRepeatMap = new HashMap<>();
        //通过pamCode和erpCode查找所有的物料（包括删除和退市的）
        List<MaterialDto> allPamCodeAndErpCodeMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pamCodeList) || CollectionUtils.isNotEmpty(erpCodeList)) {
            allPamCodeAndErpCodeMaterials.addAll(materialExtService.getAllPamCodeAndErpCodeMaterial(organizationIds, pamCodeList, erpCodeList));
        }
        for (MaterialDto materialDto : allPamCodeAndErpCodeMaterials) {
            String pamCode = materialDto.getPamCode();
            String erpCode = materialDto.getItemCode();
            //查询所有物料信息表中的数据
            if (Boolean.FALSE.equals(materialDto.getDeleteFlag()) && Boolean.FALSE.equals(materialDto.getDelistFlag())) {
                if (StringUtils.isNotEmpty(pamCode)) {
                    if (pamMaterialsMap.containsKey(pamCode)) {
                        pamMaterialsMap.get(pamCode).add(materialDto);
                    } else {
                        pamMaterialsMap.put(pamCode, Lists.newArrayList(materialDto));
                    }
                }
                if (StringUtils.isNotEmpty(erpCode)) {
                    if (erpMaterialsMap.containsKey(erpCode)) {
                        erpMaterialsMap.get(erpCode).add(materialDto);
                    } else {
                        erpMaterialsMap.put(erpCode, Lists.newArrayList(materialDto));
                    }
                }
            }
            //从基础信息表中查询所有退市或者已删除的物料
            if (Boolean.TRUE.equals(materialDto.getDeleteFlag()) || Boolean.TRUE.equals(materialDto.getDelistFlag())) {
                if (StringUtils.isNotEmpty(pamCode)) {
                    if (pamDelistAndDeleteMaterialMap.containsKey(pamCode)) {
                        pamDelistAndDeleteMaterialMap.get(pamCode).add(materialDto);
                    } else {
                        pamDelistAndDeleteMaterialMap.put(pamCode, Lists.newArrayList(materialDto));
                    }
                }
                if (StringUtils.isNotEmpty(erpCode)) {
                    if (erpDelistAndDeleteMaterialMap.containsKey(erpCode)) {
                        erpDelistAndDeleteMaterialMap.get(erpCode).add(materialDto);
                    } else {
                        erpDelistAndDeleteMaterialMap.put(erpCode, Lists.newArrayList(materialDto));
                    }
                }
            }
        }

        //通过figureNumber和brand和model查找所有的物料（包括删除和退市的）
        List<MaterialDto> allFigureNumberAndBrandAndModelMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(figureNumberList) || CollectionUtils.isNotEmpty(brandList) || CollectionUtils.isNotEmpty(modelList)) {
            // KUKA2022 规则下标准件的 model 忽略一些特殊符号
            allFigureNumberAndBrandAndModelMaterials.addAll(materialExtService.getAllFigureNumberAndBrandAndModelMaterial(organizationIds,
                    figureNumberList, brandList, new ArrayList<>()));
        }
        for (MaterialDto materialDto : allFigureNumberAndBrandAndModelMaterials) {
            //查询所有物料信息表中的数据
            if (Boolean.FALSE.equals(materialDto.getDeleteFlag()) && Boolean.FALSE.equals(materialDto.getDelistFlag())) {
                String materialForCheckRepeatKey = getMaterialForCheckRepeatKey(materialDto.getFigureNumber(), materialDto.getBrand(),
                        materialDto.getModel(), nonStandardMaterialType, materialDto.getMaterialClassification(), characterIgnoreList);
                if (materialForCheckRepeatMap.containsKey(materialForCheckRepeatKey)) {
                    materialForCheckRepeatMap.get(materialForCheckRepeatKey).add(materialDto);
                } else {
                    materialForCheckRepeatMap.put(materialForCheckRepeatKey, Lists.newArrayList(materialDto));
                }
            }
            //从基础信息表中查询所有退市或者已删除的物料
            if (Boolean.TRUE.equals(materialDto.getDeleteFlag()) || Boolean.TRUE.equals(materialDto.getDelistFlag())) {
                String materialForCheckAdjustRepeatKey = getMaterialForCheckAdjustRepeatKey(materialDto.getFigureNumber(), materialDto.getBrand(),
                        materialDto.getModel(), nonStandardMaterialType, materialDto.getMaterialClassification(), characterIgnoreList);
                if (materialForCheckAdjustRepeatMap.containsKey(materialForCheckAdjustRepeatKey)) {
                    materialForCheckAdjustRepeatMap.get(materialForCheckAdjustRepeatKey).add(materialDto);
                } else {
                    materialForCheckAdjustRepeatMap.put(materialForCheckAdjustRepeatKey, Lists.newArrayList(materialDto));
                }
            }
        }

        //序号与引用物料map
        Map<Long, MaterialDto> forFillImportMap = new HashMap<>();
        List<Boolean> booleanList = new ArrayList<>();
        // 校验逻辑
        int threadNum = kukaDetailExcelVos.size() / EACH_THREAD_DATA_NUM + (kukaDetailExcelVos.size() % EACH_THREAD_DATA_NUM == 0 ? 0 : 1);
        List<MilepostDesignPlanDetailDto> resultList = new ArrayList<>();
        final CountDownLatch countDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * EACH_THREAD_DATA_NUM;
            int endIndex = i == threadNum ? kukaDetailExcelVos.size() : i * EACH_THREAD_DATA_NUM;
            List<MilepostDesignPlanDetailDto> eachTreadExcelVos = kukaDetailExcelVos.subList(startIndex, endIndex);
            importDesignPlanDetailMaterialExecutor.execute(() -> {
                try {
                    // 多线程异步执行主干导入方法
                    boolean executeEachTreadResult = executeEachTreadExcelVo(eachTreadExcelVos, storageId, forFillImportMap, materialClasses,
                            unitNameCodeMap, orSparePartsMaskSet, nonStandardMaterialType, machinedPartTypeList, nonStandardMaterials,
                            standardMaterials, pamAdjustMaterialsMap, erpAdjustMaterialsMap, adjustCodeForKukaCodeRuleMap, pamMaterialsMap,
                            erpMaterialsMap, pamDelistAndDeleteMaterialMap, erpDelistAndDeleteMaterialMap, materialForCheckRepeatMap,
                            materialForCheckAdjustRepeatMap, isChange, nonStandardMaterialMap, standardMaterialMap, materialClassMap,
                            organizationMap, characterIgnoreList, organizationIds);
                    booleanList.add(executeEachTreadResult);
                } catch (Exception e) {
                    logger.error("executeEachTreadExcelVo操作记录失败", e);
                    booleanList.add(false);
                } finally {
                    countDownLatch.countDown();
                    if (Objects.equals(countDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after executeEachTreadExcelVo", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                    resultList.addAll(eachTreadExcelVos);
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("importDesignPlanDetailWithKuka2022的executeEachTreadExcelVo的await失败", ex);
            Thread.currentThread().interrupt();
        }
        if (booleanList.contains(false)) {
            response.setMsg(MaterialCodeRuleEnum.KUKA2022.getCode());
            response.setData(resultList);
            return response;
        }

        logger.info("executeEachTreadDealDataDtoList的待处理的resultList：{}", JsonUtils.toString(resultList));
        // 是否启用物料类别映射库存分类
        String currentDateStr = DateUtil.format(new Date(), "yyyy-MM-dd");
        OrganizationCustomDict organizationCustomDict = organizationCustomDictExtMapper.queryByNameAndOrgId("是否启用物料类别映射库存分类",
                storageId, currentDateStr);
        boolean enableInventoryType = Objects.nonNull(organizationCustomDict);
        // 必填校验 以及入库查重校验成功后 再循环所有数据赋值 并进行唯一性校验
        List<MilepostDesignPlanDetailDto> finalResultList = new ArrayList<>();
        final CountDownLatch dealDataCountDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * EACH_THREAD_DATA_NUM;
            int endIndex = i == threadNum ? resultList.size() : i * EACH_THREAD_DATA_NUM;
            List<MilepostDesignPlanDetailDto> eachTreadDealDataDtoList = resultList.subList(startIndex, endIndex);
            importDesignPlanDetailMaterialExecutor.execute(() -> {
                try {
                    // 多线程异步执行主干导入方法
                    boolean executeEachTreadDealDataResult = executeEachTreadDealDataDtoList(eachTreadDealDataDtoList, wbsEnabled, storageId,
                            forFillImportMap, nonStandardMaterialType, unitId, checkRepeatPamWBSDeliveryTimeList, wbsSummaryCode, sdf,
                            unitNameCodeMap, unitCodeNameMap, projectId, forCheckMaps, isChange, enableInventoryType);
                    booleanList.add(executeEachTreadDealDataResult);
                } catch (Exception e) {
                    logger.error("executeEachTreadDealDataDtoList操作记录失败", e);
                    booleanList.add(false);
                } finally {
                    dealDataCountDownLatch.countDown();
                    if (Objects.equals(dealDataCountDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after executeEachTreadDealDataDtoList", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                    finalResultList.addAll(eachTreadDealDataDtoList);
                }
            });
        }
        try {
            dealDataCountDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("importDesignPlanDetailWithKuka2022的executeEachTreadDealDataDtoList的await失败", ex);
            Thread.currentThread().interrupt();
        }

        long executeEndTime = System.currentTimeMillis();
        logger.info("validImportDesignPlanDetailMaterial的execute执行时间：{}", executeEndTime - executeStartTime);
        logger.info("executeEachTreadDealDataDtoList的已处理的finalResultList：{}", JsonUtils.toString(finalResultList));

        if (booleanList.contains(false)) {
            response.setMsg(MaterialCodeRuleEnum.KUKA2022.getCode());
        }

        // 为第一层记录设置UID
        setUidForFirstLevelRecords(finalResultList);

        // 根据上下级关系设置父级物料描述
        setParentMaterielDescrForAllData(finalResultList);

        // 为所有返回的记录设置editable字段
        for (MilepostDesignPlanDetailDto dto : finalResultList) {
            if (dto.getEditable() == null) {
                dto.setEditable(Boolean.TRUE);
            }
        }

        response.setData(finalResultList);
        return response;
    }


    private boolean executeEachTreadExcelVo(List<MilepostDesignPlanDetailDto> eachTreadExcelVos, Long storageId,
                                            Map<Long, MaterialDto> forFillImportMap,
                                            List<MerielSubclassVO> materialClasses, Map<String, String> unitNameCodeMap,
                                            Set<String> orSparePartsMaskSet,
                                            List<String> nonStandardMaterialType, List<String> machinedPartTypeList,
                                            List<String> nonStandardMaterials,
                                            List<String> standardMaterials, Map<String, List<MaterialDto>> pamAdjustMaterialsMap,
                                            Map<String, List<MaterialDto>> erpAdjustMaterialsMap,
                                            Map<String, List<MaterialDto>> adjustCodeForKukaCodeRuleMap,
                                            Map<String, List<MaterialDto>> pamMaterialsMap, Map<String, List<MaterialDto>> erpMaterialsMap,
                                            Map<String, List<MaterialDto>> pamDelistAndDeleteMaterialMap,
                                            Map<String, List<MaterialDto>> erpDelistAndDeleteMaterialMap,
                                            Map<String, List<MaterialDto>> materialForCheckRepeatMap,
                                            Map<String, List<MaterialDto>> materialForCheckAdjustRepeatMap, Boolean isChange,
                                            Map<String, List<MaterialDto>> nonStandardMaterialMap, Map<String, List<MaterialDto>> standardMaterialMap,
                                            Map<String, MerielSubclassVO> materialClassMap, Map<Long, String> organizationMap,
                                            List<String> characterIgnoreList, List<Long> organizationIds) {
        List<Boolean> uniqueList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto detailDto : eachTreadExcelVos) {
            // 已存在的PAM物料编码 和 已存在的ERP物料编码
            String materialClassification = detailDto.getMaterialClassification();
            String figureNumber = detailDto.getFigureNumber();
            String finalBrand = detailDto.getBrand();
            String finalModel = detailDto.getModel();
            StringBuilder pamCodeSB = new StringBuilder();
            StringBuilder erpCodeSB = new StringBuilder();
            if (nonStandardMaterialType.contains(materialClassification)) {
                if (StringUtils.isNotEmpty(materialClassification) && StringUtils.isNotEmpty(figureNumber)) {
                    List<MaterialDto> dtoList = nonStandardMaterialMap.getOrDefault(getNonStandardMaterialKey(materialClassification, figureNumber),
                            new ArrayList<>());
                    for (MaterialDto materialDto : dtoList) {
                        String pamCode = materialDto.getPamCode();
                        if (pamCodeSB.length() == 0 && StringUtils.isNotEmpty(pamCode)) {
                            pamCodeSB.append(pamCode);
                        } else if (pamCodeSB.length() > 0 && StringUtils.isNotEmpty(pamCode)) {
                            pamCodeSB.append(";").append(pamCode);
                        }
                        String erpCode = materialDto.getItemCode();
                        if (erpCodeSB.length() == 0 && StringUtils.isNotEmpty(erpCode)) {
                            erpCodeSB.append(erpCode);
                        } else if (erpCodeSB.length() > 0 && StringUtils.isNotEmpty(erpCode)) {
                            erpCodeSB.append(";").append(erpCode);
                        }
                    }
                }
            } else {
                if (StringUtils.isNotEmpty(materialClassification) && StringUtils.isNotEmpty(finalBrand) && StringUtils.isNotEmpty(finalModel)) {
                    List<MaterialDto> dtoList = standardMaterialMap.getOrDefault(getStandardMaterialKey(materialClassification, finalBrand,
                            finalModel, characterIgnoreList), new ArrayList<>());
                    for (MaterialDto materialDto : dtoList) {
                        String pamCode = materialDto.getPamCode();
                        if (pamCodeSB.length() == 0 && StringUtils.isNotEmpty(pamCode)) {
                            pamCodeSB.append(pamCode);
                        } else if (pamCodeSB.length() > 0 && StringUtils.isNotEmpty(pamCode)) {
                            pamCodeSB.append(";").append(pamCode);
                        }
                        String erpCode = materialDto.getItemCode();
                        if (erpCodeSB.length() == 0 && StringUtils.isNotEmpty(erpCode)) {
                            erpCodeSB.append(erpCode);
                        } else if (erpCodeSB.length() > 0 && StringUtils.isNotEmpty(erpCode)) {
                            erpCodeSB.append(";").append(erpCode);
                        }
                    }
                }
            }
            detailDto.setExistedPamCode(pamCodeSB.toString());
            detailDto.setExistedErpCode(erpCodeSB.toString());

            // todo 唯一性校验
            String pamCode = detailDto.getPamCode();
            String erpCode = detailDto.getErpCode();
            boolean res;
            if (StringUtils.isNotEmpty(pamCode)) {
                // 有P码
                res = pamCodeCheck(storageId, detailDto, forFillImportMap, pamAdjustMaterialsMap, pamMaterialsMap,
                        pamDelistAndDeleteMaterialMap, organizationMap);
            } else if (StringUtils.isNotEmpty(erpCode)) {
                // 有E码
                res = erpCodeCheck(storageId, detailDto, forFillImportMap, erpAdjustMaterialsMap, erpMaterialsMap,
                        erpDelistAndDeleteMaterialMap, organizationMap);
            } else {
                // P码、E码都没有
                res = erpCodeIsEmptyAndPamCodeIsEmptyCheck(storageId, detailDto, materialClasses, unitNameCodeMap, orSparePartsMaskSet,
                        nonStandardMaterialType, machinedPartTypeList, forFillImportMap, materialForCheckRepeatMap, materialForCheckAdjustRepeatMap,
                        adjustCodeForKukaCodeRuleMap, standardMaterials, isChange, nonStandardMaterials, materialClassMap, organizationMap,
                        characterIgnoreList, organizationIds);
            }
            uniqueList.add(res);
        }

        return !uniqueList.contains(false);
    }

    private Boolean executeEachTreadDealDataDtoList(List<MilepostDesignPlanDetailDto> eachTreadDealDataDtoList, Boolean wbsEnabled, Long storageId,
                                                    Map<Long, MaterialDto> forFillImportMap, List<String> nonStandardMaterialType, Long unitId,
                                                    List<String> checkRepeatPamWBSDeliveryTimeList, String wbsSummaryCode, SimpleDateFormat sdf,
                                                    Map<String, String> unitNameCodeMap, Map<String, String> unitCodeNameMap, Long projectId,
                                                    Map<String, List<MilepostDesignPlanDetailDto>> forCheckMaps, Boolean isChange,
                                                    boolean enableInventoryType) {
        Boolean result = true;
        List<String> materialClassificationList = new ArrayList<>();
        List<String> codingMiddleClassList = new ArrayList<>();
        List<String> materielTypeList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto forFillDetailDto : eachTreadDealDataDtoList) {
            MaterialDto materialDto = forFillImportMap.get(Long.parseLong(forFillDetailDto.getSerialNumber()));
            if (Objects.isNull(materialDto)) {
                // 没有引用物料
                forFillDetailDto.setUnitCode(unitNameCodeMap.get(forFillDetailDto.getUnit()));
                // 导入的物料大类、物料中类、物料小类都不能为空
                materialClassificationList.add(forFillDetailDto.getMaterialClassification());
                codingMiddleClassList.add(forFillDetailDto.getCodingMiddleClass());
                materielTypeList.add(forFillDetailDto.getMaterielType());
            } else {
                // 引用了物料
                String unitCode = "";
                if (StringUtils.isNotEmpty(materialDto.getAdjustCode())) {
                    // 引用了adjust
                    unitCode = materialDto.getUnitCode();
                    if (!unitCodeNameMap.containsKey(unitCode)) {
                        String errorMsg = String.format("引用的物料adjust表的pam编码：%s的物料的单位：%s 格式错误", materialDto.getPamCode(), unitCode);
                        forFillDetailDto.setValidResult((StringUtils.isEmpty(forFillDetailDto.getValidResult()) ? ""
                                : (forFillDetailDto.getValidResult() + "，")) + errorMsg);
                        result = false;
                    }
                } else {
                    // 引用了material
                    unitCode = materialDto.getUnit();
                    if (!unitCodeNameMap.containsKey(unitCode)) {
                        String errorMsg = String.format("引用的物料material表的pam编码：%s的物料的单位：%s 格式错误", materialDto.getPamCode(), unitCode);
                        forFillDetailDto.setValidResult((StringUtils.isEmpty(forFillDetailDto.getValidResult()) ? ""
                                : (forFillDetailDto.getValidResult() + "，")) + errorMsg);
                        result = false;
                    }
                }
                forFillDetailDto.setUnitCode(unitCode);
                forFillDetailDto.setUnit(unitCodeNameMap.get(unitCode));
                materialClassificationList.add(materialDto.getMaterialClassification());
                codingMiddleClassList.add(materialDto.getCodingMiddleclass());
                materielTypeList.add(materialDto.getMaterialType());
            }
        }
        if (!result) {
            return false;
        }

        MaterialCustomDictHeaderExample customDictHeaderExample = new MaterialCustomDictHeaderExample();
        customDictHeaderExample.createCriteria().andDeletedFlagEqualTo(false).andOrganizationIdEqualTo(storageId)
                .andCodingClassIn(materialClassificationList).andCodingMiddleclassIn(codingMiddleClassList).andCodingSubclassIn(materielTypeList);
        List<MaterialCustomDictHeader> materialCustomDictHeaders = materialCustomDictHeaderMapper.selectByExample(customDictHeaderExample);
        List<Long> headerIdList = new ArrayList<>();
        Map<String, List<Long>> materialCustomDictHeaderMap = new HashMap<>();
        for (MaterialCustomDictHeader customDictHeader : materialCustomDictHeaders) {
            Long headerId = customDictHeader.getId();
            String customDictHeaderKey = getCustomDictHeaderKey(customDictHeader.getCodingClass(), customDictHeader.getCodingMiddleclass(),
                    customDictHeader.getCodingSubclass());
            if (materialCustomDictHeaderMap.containsKey(customDictHeaderKey)) {
                List<Long> valueList = materialCustomDictHeaderMap.get(customDictHeaderKey);
                valueList.add(headerId);
                materialCustomDictHeaderMap.put(customDictHeaderKey, valueList);
            } else {
                materialCustomDictHeaderMap.put(customDictHeaderKey, Lists.newArrayList(headerId));
            }
            headerIdList.add(headerId);
        }

        Map<String, String> activityCodeMap = new HashMap<>();
        Map<String, Boolean> dispatchIsMap = new HashMap<>();
        // 处理库存分类的inventoryType
        Map<String, String> inventoryTypeMap = new HashMap<>();
        List<String> codeList = new ArrayList<>();
        if (MapUtils.isNotEmpty(materialCustomDictHeaderMap)) {
            MaterialCustomDictExample customDictExample = new MaterialCustomDictExample();
            customDictExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andNameEqualTo("活动事项编码").andHeaderIdIn(headerIdList);
            List<MaterialCustomDict> materialCustomDicts = materialCustomDictMapper.selectByExample(customDictExample);
            if (CollectionUtils.isNotEmpty(materialCustomDicts)) {
                materialCustomDictHeaderMap.forEach((k, v) -> {
                    // material_custom_dict中多个headerId 对应一个value
                    String activityCode =
                            materialCustomDicts.stream().filter(e -> v.contains(e.getHeaderId())).map(MaterialCustomDict::getValue).findFirst().orElse(null);
                    activityCodeMap.put(k, activityCode);
                    if (StringUtils.isNotEmpty(activityCode)) {
                        codeList.add(Arrays.asList(activityCode.split(",")).get(0));
                    }
                });
            }

            // 急件 导入时，如果大中小类的"允许标识急件"=1，则这条数据允许填写"是否急件"字段="是";如果大中小类的"允许标识急件"！=1，则这条数据不允许填写"是否急件"字段="是"
            MaterialCustomDictExample dictExample = new MaterialCustomDictExample();
            dictExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andNameEqualTo("允许标识急件").andHeaderIdIn(headerIdList);
            List<MaterialCustomDict> dictList = materialCustomDictMapper.selectByExample(dictExample);
            if (CollectionUtils.isNotEmpty(dictList)) {
                materialCustomDictHeaderMap.forEach((k, v) -> {
                    // material_custom_dict中多个headerId 对应一个value
                    String value =
                            dictList.stream().filter(e -> v.contains(e.getHeaderId())).map(MaterialCustomDict::getValue).findFirst().orElse(null);
                    dispatchIsMap.put(k, Objects.equals("1", value));
                });
            }

            if (enableInventoryType) {
                // “物料类别配置”，按"库存组织+物料大中小类"配置大中小类的"库存分类"（存在且未删除、启用日期<=系统当前日期<=失效日期、属性值不为空）
                String currentDateStr = DateUtil.format(new Date(), "yyyy-MM-dd");
                List<MaterialCustomDict> customDictList = materialCustomDictExtMapper.queryByNameAndHeaderIdList("库存分类", headerIdList,
                        currentDateStr);
                if (CollectionUtils.isNotEmpty(customDictList)) {
                    materialCustomDictHeaderMap.forEach((k, v) -> {
                        // material_custom_dict中多个headerId 对应一个value
                        String inventoryType =
                                customDictList.stream().filter(e -> v.contains(e.getHeaderId())).map(MaterialCustomDict::getValue).findFirst().orElse(null);
                        inventoryTypeMap.put(k, inventoryType);
                    });
                }
            }
        }
        logger.info("急件的dispatchIsMap：{}", JsonUtils.toString(dispatchIsMap));
        logger.info("柔性详设导入的库存分类的inventoryTypeMap：{}", JsonUtils.toString(inventoryTypeMap));

        Map<String, String> codeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(codeList)) {
            ProjectActivityExample example = new ProjectActivityExample();
            example.createCriteria().andDeletedFlagEqualTo(false).andUnitIdEqualTo(unitId).andCodeIn(codeList);
            List<ProjectActivity> projectActivityList = projectActivityMapper.selectByExample(example);
            // project_activity中一个code对应一个type
            if (CollectionUtils.isNotEmpty(projectActivityList)) {
                codeMap.putAll(projectActivityList.stream().collect(Collectors.toMap(ProjectActivity::getCode, ProjectActivity::getType)));
            }
        }


        for (MilepostDesignPlanDetailDto forFillDetailDto : eachTreadDealDataDtoList) {
            List<String> validResultList = StringUtils.isEmpty(forFillDetailDto.getValidResult()) ? new ArrayList<>()
                    : Arrays.asList(forFillDetailDto.getValidResult().split("，"));
            MaterialDto materialDto = forFillImportMap.get(Long.parseLong(forFillDetailDto.getSerialNumber()));
            if (null != materialDto) {
                // 设置库存分类的inventoryType(本组织直接用原物料的inventoryType-也就是不新建物料的情况)
                if (Objects.equals(storageId, materialDto.getOrganizationId())) {
                    forFillDetailDto.setInventoryType(materialDto.getInventoryType());
                } else if (enableInventoryType) {
                    String inventoryType = inventoryTypeMap.get(getCustomDictHeaderKey(materialDto.getMaterialClassification(),
                            materialDto.getCodingMiddleclass(),
                            materialDto.getMaterialType()));
                    if (StringUtils.isEmpty(inventoryType)) {
                        validResultList.add("该物料大中小类的库存分类未维护，不允许导入");
                        forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                        result = false;
                        continue;
                    }
                    forFillDetailDto.setInventoryType(inventoryType);
                }

                // 急件
                if (!Boolean.TRUE.equals(dispatchIsMap.get(getCustomDictHeaderKey(materialDto.getMaterialClassification(),
                        materialDto.getCodingMiddleclass(),
                        materialDto.getMaterialType()))) && Boolean.TRUE.equals(forFillDetailDto.getDispatchIs())) {
                    validResultList.add("不允许填写“是否急件”字段=“是”");
                    forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                    result = false;
                    continue;
                }

                forFillDetailDto.setPamCode(StringUtils.isNotEmpty(materialDto.getPamCode()) ? materialDto.getPamCode() : null);
                forFillDetailDto.setErpCode(materialDto.getItemCode());
                forFillDetailDto.setMaterielDescr(materialDto.getItemInfo());
                Integer materielDescrLength = getOracleLengthFromMysqlVarchar(forFillDetailDto.getMaterielDescr());
                if (materielDescrLength > 240) {
                    validResultList.add("物料描述的字段长度超过了240个字符");
                    result = false;
                }
                forFillDetailDto.setMaterialClassification(materialDto.getMaterialClassification());
                forFillDetailDto.setCodingMiddleClass(materialDto.getCodingMiddleclass());
                forFillDetailDto.setMaterielType(materialDto.getMaterialType());
                result = materialCheck(forFillDetailDto, validResultList, activityCodeMap, codeMap, result);
                if (!result) {
                    forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                    continue;
                }

                forFillDetailDto.setName(materialDto.getName());
                forFillDetailDto.setModel(materialDto.getModel());
                forFillDetailDto.setBrand(materialDto.getBrand());
                forFillDetailDto.setMachiningPartType(materialDto.getMachiningPartType());
                forFillDetailDto.setMaterial(materialDto.getMaterial());
                forFillDetailDto.setUnitWeight(materialDto.getUnitWeight());
                forFillDetailDto.setMaterialProcessing(materialDto.getMaterialProcessing());//材质处理
                forFillDetailDto.setFigureNumber(materialDto.getFigureNumber());
                DesignPlanDetailImportCache cache = BeanConverter.copy(forFillDetailDto, DesignPlanDetailImportCache.class);
                cache.setProjectId(projectId);
                cache.setWbsSummaryCode(wbsSummaryCode);
                if (StringUtils.isEmpty(forFillDetailDto.getPamCode())) {
                    if (DesignPlanDetailImportCacheUtils.hasKey(cache)) {
                        List<DesignPlanDetailImportCache> designPlanDetailImportCaches = DesignPlanDetailImportCacheUtils.listCache(cache);
                        forFillDetailDto.setPamCode(designPlanDetailImportCaches.get(0).getPamCode());
                    } else {
                        // 生成PAM编码
                        forFillDetailDto.setPamCode(materialExtService.generateMaterialPAMCode());
                        DesignPlanDetailImportCacheUtils.putCacheWithTimeExpire(cache, 8, TimeUnit.HOURS);
                    }
                }
            } else {
                // 设置库存分类的inventoryType
                if (enableInventoryType) {
                    String inventoryType = inventoryTypeMap.get(getCustomDictHeaderKey(forFillDetailDto.getMaterialClassification(),
                            forFillDetailDto.getCodingMiddleClass(),
                            forFillDetailDto.getMaterielType()));
                    if (StringUtils.isEmpty(inventoryType)) {
                        validResultList.add("该物料大中小类的库存分类未维护，不允许导入");
                        forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                        result = false;
                        continue;
                    }
                    forFillDetailDto.setInventoryType(inventoryType);
                }

                // 急件
                if (!Boolean.TRUE.equals(dispatchIsMap.get(getCustomDictHeaderKey(forFillDetailDto.getMaterialClassification(),
                        forFillDetailDto.getCodingMiddleClass(),
                        forFillDetailDto.getMaterielType()))) && Boolean.TRUE.equals(forFillDetailDto.getDispatchIs())) {
                    validResultList.add("不允许填写“是否急件”字段=“是”");
                    forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                    result = false;
                    continue;
                }

                result = materialCheck(forFillDetailDto, validResultList, activityCodeMap, codeMap, result);
                if (!result) {
                    forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                    continue;
                }

                MaterialAdjustDetailDTO adjustDetailDTO = new MaterialAdjustDetailDTO();
                adjustDetailDTO.setFigureNumber(forFillDetailDto.getFigureNumber());
                adjustDetailDTO.setBrand(forFillDetailDto.getBrand());
                adjustDetailDTO.setModel(forFillDetailDto.getModel());
                adjustDetailDTO.setName(forFillDetailDto.getName());
                adjustDetailDTO.setMaterialClass(forFillDetailDto.getMaterialClassification());
                // 生成物料描述
                forFillDetailDto.setMaterielDescr(generateMaterialDesForKuka2022(adjustDetailDTO, nonStandardMaterialType));
                Integer materielDescrLength = getOracleLengthFromMysqlVarchar(forFillDetailDto.getMaterielDescr());
                if (materielDescrLength > 240) {
                    validResultList.add("物料描述的字段长度超过了240个字符");
                    forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                    result = false;
                }
                DesignPlanDetailImportCache cache = BeanConverter.copy(forFillDetailDto, DesignPlanDetailImportCache.class);
                cache.setProjectId(projectId);
                cache.setWbsSummaryCode(wbsSummaryCode);
                if (DesignPlanDetailImportCacheUtils.hasKey(cache)) {
                    List<DesignPlanDetailImportCache> designPlanDetailImportCaches = DesignPlanDetailImportCacheUtils.listCache(cache);
                    forFillDetailDto.setPamCode(designPlanDetailImportCaches.get(0).getPamCode());
                } else {
                    // 生成PAM编码
                    forFillDetailDto.setPamCode(materialExtService.generateMaterialPAMCode());
                    cache.setPamCode(forFillDetailDto.getPamCode());
                    DesignPlanDetailImportCacheUtils.putCacheWithTimeExpire(cache, 8, TimeUnit.HOURS);
                }
            }
            if (Boolean.TRUE.equals(wbsEnabled)) {
                if (Boolean.TRUE.equals(isChange)) {
                    String repeatKey = forFillDetailDto.getPamCode() + ":" + wbsSummaryCode + ":"
                            + (Objects.isNull(forFillDetailDto.getDeliveryTime()) ? "" : sdf.format(forFillDetailDto.getDeliveryTime()));
                    if (forCheckMaps.containsKey(repeatKey)) {
                        validResultList.add("根据详设行唯一性原则，该条数据重复！");
                        forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                        result = false;
                    } else {
                        forCheckMaps.put(repeatKey, Lists.newArrayList(forFillDetailDto));
                    }
                } else {
                    String repeatKey = forFillDetailDto.getPamCode() + ":" + wbsSummaryCode + ":"
                            + (Objects.isNull(forFillDetailDto.getDeliveryTime()) ? "" : sdf.format(forFillDetailDto.getDeliveryTime()));
                    if (checkRepeatPamWBSDeliveryTimeList.contains(repeatKey)) {
                        validResultList.add("根据详设行唯一性原则，该条数据重复！");
                        forFillDetailDto.setValidResult(Joiner.on("，").join(validResultList));
                        result = false;
                    } else {
                        checkRepeatPamWBSDeliveryTimeList.add(repeatKey);
                    }
                }
            }
        }
        return result;
    }

    private String getCustomDictHeaderKey(String materialClassification, String codingMiddleClass, String materielType) {
        StringBuilder key = new StringBuilder("getCustomDictHeaderKey");
        if (StringUtils.isNotEmpty(materialClassification)) {
            key.append("_").append(materialClassification);
        }
        if (StringUtils.isNotEmpty(codingMiddleClass)) {
            key.append("_").append(codingMiddleClass);
        }
        if (StringUtils.isNotEmpty(materielType)) {
            key.append("_").append(materielType);
        }
        return key.toString();
    }

    private Boolean checkDesignPlanDetailQuotable(MilepostDesignPlanDetailDto detailDto, String figureNumber, String brand, String model,
                                                  Long organizationId, List<String> validResultList, Map<Long, MaterialDto> forFillImportMap,
                                                  Map<String, List<MaterialDto>> materialForCheckRepeatMap,
                                                  Map<String, List<MaterialDto>> materialForCheckAdjustRepeatMap,
                                                  Map<String, List<MaterialDto>> adjustCodeForKukaCodeRuleMap, List<String> nonStandardMaterialType,
                                                  Map<String, MerielSubclassVO> materialClassMap, Map<Long, String> organizationMap,
                                                  List<String> characterIgnoreList) {
        //查出所有相关的物料
        List<MaterialDto> adjustCodes = adjustCodeForKukaCodeRuleMap.get(getAdjustCodeForKukaCodeRuleKey(figureNumber, brand, model,
                nonStandardMaterialType, detailDto.getMaterialClassification(), characterIgnoreList));
        if (CollectionUtils.isNotEmpty(adjustCodes)) {
            if (nonStandardMaterialType.contains(detailDto.getMaterialClassification())) {
                adjustCodes = adjustCodes.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            } else {
                adjustCodes = adjustCodes.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            }
        }
        List<MaterialDto> currentAdjustMaterials = new ArrayList<>();
        List<MaterialDto> otherAdjustMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(adjustCodes)) {
            for (MaterialDto adjustMaterial : adjustCodes) {
                if (Objects.equals(adjustMaterial.getOrganizationId(), organizationId)) {
                    currentAdjustMaterials.add(adjustMaterial);
                } else {
                    otherAdjustMaterials.add(adjustMaterial);
                }
            }
        }
        //非标件就只根据图号 ,标件根据品牌 和型号/规格 查 基础信息表 未退市和未删除的物料
        List<MaterialDto> materialForCheckRepeat = materialForCheckRepeatMap.get(getMaterialForCheckRepeatKey(figureNumber, brand, model,
                nonStandardMaterialType, detailDto.getMaterialClassification(), characterIgnoreList));
        if (CollectionUtils.isNotEmpty(materialForCheckRepeat)) {
            if (nonStandardMaterialType.contains(detailDto.getMaterialClassification())) {
                materialForCheckRepeat = materialForCheckRepeat.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            } else {
                materialForCheckRepeat = materialForCheckRepeat.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                                && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                e.getMaterialType())))
                        .collect(Collectors.toList());
            }
        }
        //筛选当前库存组织的物料基础表中的数据
        List<MaterialDto> currentMaterials = new ArrayList<>();
        List<MaterialDto> otherMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(materialForCheckRepeat)) {
            for (MaterialDto materialDto : materialForCheckRepeat) {
                if (Objects.equals(materialDto.getOrganizationId(), organizationId)) {
                    currentMaterials.add(materialDto);
                } else {
                    otherMaterials.add(materialDto);
                }
            }
        }
        //非标件就只查图号 标件查品牌 和型号/规格的
        String materialForCheckAdjustRepeatKey = getMaterialForCheckAdjustRepeatKey(figureNumber, brand, model, nonStandardMaterialType,
                detailDto.getMaterialClassification(), characterIgnoreList);
        List<MaterialDto> delistAndDeleteMaterialDtos = materialForCheckAdjustRepeatMap.get(materialForCheckAdjustRepeatKey);
        if (CollectionUtils.isNotEmpty(delistAndDeleteMaterialDtos)) {
            if (nonStandardMaterialType.contains(detailDto.getMaterialClassification())) {
                delistAndDeleteMaterialDtos =
                        delistAndDeleteMaterialDtos.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                                        && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                        e.getMaterialType())))
                                .collect(Collectors.toList());
            } else {
                delistAndDeleteMaterialDtos =
                        delistAndDeleteMaterialDtos.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                                        && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                        e.getMaterialType())))
                                .collect(Collectors.toList());
            }
        }
        List<MaterialDto> currentDelistAndDeleteMaterialDtos = new ArrayList<>();
        List<MaterialDto> otherDelistAndDeleteMaterialDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(delistAndDeleteMaterialDtos)) {
            for (MaterialDto materialDto : delistAndDeleteMaterialDtos) {
                if (Objects.equals(materialDto.getOrganizationId(), organizationId)) {
                    currentDelistAndDeleteMaterialDtos.add(materialDto);
                } else {
                    otherDelistAndDeleteMaterialDtos.add(materialDto);
                }
            }
        }

        if (ListUtils.isEmpty(adjustCodes)) {
            if (ListUtils.isNotEmpty(currentMaterials)) {
                //本组织adj无，其他adj无, 本组织mat有
                if (currentMaterials.size() > 1) {
                    List<String> pamCodeList = currentMaterials.stream().map(Material::getPamCode).collect(Collectors.toList());
                    validResultList.add("根据唯一性条件查找到本组织存在多个物料:[" + pamCodeList.toString() + "]，请联系系统管理员");
                    return false;
                }
                MaterialDto materialDto = currentMaterials.get(0);
                if (Objects.equals(materialDto.getMaterialCategory(), detailDto.getMaterialCategory())) {
                    materialDto.setAllExtend(true);
                    forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), materialDto);
                } else {
                    //物料类型不一致时
                    validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName() + "]类型不一致, "
                            + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                    return false;
                }
            } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                //本组织adj无，其他adj无, 本组织mat退市或删除
                if (CollectionUtils.isNotEmpty(otherMaterials)) {
                    List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                            .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                            .map(MaterialDto::getPamCode).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                        //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                        validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                        detailDto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherMaterials.get(0));
                } else {
                    return true;
                }
            } else {
                //本组织adj无，其他adj无, 本组织mat无
                if (CollectionUtils.isNotEmpty(otherMaterials)) {
                    forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherMaterials.get(0));
                } else {
                    return true;
                }
            }
        } else {
            if (CollectionUtils.isNotEmpty(currentAdjustMaterials)) {
                //本组织adj有
                List<MaterialDto> currentDtoList = currentAdjustMaterials.stream().filter(e ->
                        !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(currentDtoList)) {
                    //本组织adj有(有单据状态！="审批通过")
                    for (MaterialDto dto : currentDtoList) {
                        validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                    }
                    return false;
                }

                if (ListUtils.isNotEmpty(currentMaterials)) {
                    //本组织adj有,本组织mat有
                    if (currentMaterials.size() > 1) {
                        List<String> pamCodeList = currentMaterials.stream().map(Material::getPamCode).collect(Collectors.toList());
                        validResultList.add("根据唯一性条件查找到本组织存在多个物料:[" + pamCodeList.toString() + "]，请联系系统管理员");
                        return false;
                    }
                    MaterialDto materialDto = currentMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), detailDto.getMaterialCategory())) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName() + "]类型不一致, "
                                + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        return false;
                    }
                } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    //本组织adj有, 本组织mat退市或删除
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        //本组织adj有, 本组织mat退市或删除, 其他mat有
                        List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            detailDto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        //本组织adj有, 本组织mat退市或删除, 其他mat退市或删除
                        return true;
                    } else {
                        //本组织adj有, 本组织mat退市或删除, 其他mat无
                        if (CollectionUtils.isNotEmpty(otherAdjustMaterials)) {
                            List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                    .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                    .map(MaterialDto::getPamCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                                //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                                validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                                detailDto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                    !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(dtoList)) {
                                //本组织adj有 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                                for (MaterialDto dto : dtoList) {
                                    validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                            "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                                }
                                return false;
                            }
                            forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherAdjustMaterials.get(0));
                        } else {
                            return true;
                        }
                    }
                } else {
                    //本组织adj有，本组织mat无
                    MaterialDto materialDto = currentAdjustMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), detailDto.getMaterialCategory())) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName() + "]类型不一致, "
                                + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        return false;
                    }
                    forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), materialDto);
                }
            } else {
                //本组织adj无，其他adj有
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    //本组织adj无，其他adj有，本组织mat有
                    if (currentMaterials.size() > 1) {
                        List<String> pamCodeList = currentMaterials.stream().map(Material::getPamCode).collect(Collectors.toList());
                        validResultList.add("根据唯一性条件查找到本组织存在多个物料:[" + pamCodeList.toString() + "]，请联系系统管理员");
                        return false;
                    }
                    MaterialDto materialDto = currentMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), detailDto.getMaterialCategory())) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName() + "]类型不一致, "
                                + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        return false;
                    }
                } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    //本组织adj无，其他adj有, 本组织mat删除或退市
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            detailDto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        return true;
                    } else {
                        List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            detailDto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto dto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                            }
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherAdjustMaterials.get(0));
                    }
                } else {
                    //本组织adj无，其他adj有, 本组织mat无
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        return true;
                    } else {
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat无 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto dto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                            }
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(detailDto.getSerialNumber()), otherAdjustMaterials.get(0));
                    }
                }
            }
        }
        return true;
    }

    private String getMaterialForCheckRepeatKey(String figureNumber, String brand, String model, List<String> nonStandardMaterialType,
                                                String materialClassification, List<String> characterIgnoreList) {
        StringBuilder key = new StringBuilder("getMaterialForCheckRepeatKey");
        if (nonStandardMaterialType.contains(materialClassification)) {
            if (StringUtils.isNotEmpty(figureNumber)) {
                key.append("_").append(figureNumber.toUpperCase());
            }
        } else {
            if (StringUtils.isNotEmpty(brand)) {
                key.append("_").append(brand.toUpperCase());
            }
            if (StringUtils.isNotEmpty(model)) {
                key.append("_").append(removeCharacterIgnoreList(model, characterIgnoreList));
            }
        }
        return key.toString();
    }


    private String getMaterialForCheckAdjustRepeatKey(String figureNumber, String brand, String model, List<String> nonStandardMaterialType,
                                                      String materialClassification, List<String> characterIgnoreList) {
        StringBuilder key = new StringBuilder("getMaterialForCheckAdjustRepeatKey");
        if (nonStandardMaterialType.contains(materialClassification)) {
            if (StringUtils.isNotEmpty(figureNumber)) {
                key.append("_").append(figureNumber.toUpperCase());
            }
        } else {
            if (StringUtils.isNotEmpty(brand)) {
                key.append("_").append(brand.toUpperCase());
            }
            if (StringUtils.isNotEmpty(model)) {
                key.append("_").append(removeCharacterIgnoreList(model, characterIgnoreList));
            }
        }
        return key.toString();
    }

    private String getAdjustCodeForKukaCodeRuleKey(String figureNumber, String brand, String model, List<String> nonStandardMaterialType,
                                                   String materialClassification, List<String> characterIgnoreList) {
        StringBuilder key = new StringBuilder("getAdjustCodeForKukaCodeRuleKey");
        if (nonStandardMaterialType.contains(materialClassification)) {
            if (StringUtils.isNotEmpty(figureNumber)) {
                key.append("_").append(figureNumber.toUpperCase());
            }
        } else {
            if (StringUtils.isNotEmpty(brand)) {
                key.append("_").append(brand.toUpperCase());
            }
            if (StringUtils.isNotEmpty(model)) {
                key.append("_").append(removeCharacterIgnoreList(model, characterIgnoreList));
            }
        }
        return key.toString();
    }

    private Boolean materialCheck(MilepostDesignPlanDetailDto dto, List<String> validResultList, Map<String, String> activityCodeMap,
                                  Map<String, String> codeMap, Boolean result) {
        String activityCode = activityCodeMap.get(getCustomDictHeaderKey(dto.getMaterialClassification(), dto.getCodingMiddleClass(),
                dto.getMaterielType()));
        //materialCtcExtMapper.getActivityForDesignPlanDetail(dto.getMaterialClassification(), dto.getCodingMiddleClass(), dto.getMaterielType(),
        // storageId);
        if (StringUtils.isEmpty(activityCode)) {
            validResultList.add("活动事项编码不存在，不允许导入！");
            return false;
        } else {
            List<String> activityCodes = Arrays.asList(activityCode.split(","));
            dto.setActivityCode(activityCodes.get(0));
            String projectBudgetType = codeMap.get(activityCodes.get(0));
            //materialCtcExtMapper.getProjectBudgetTypeForDesignPlanDetail(activityCodes.get(0), unitId);
            if (StringUtils.isEmpty(projectBudgetType)) {
                validResultList.add("项目预算类别属性不存在，不允许导入！");
                return false;
            } else {
                dto.setProjectBudgetType(projectBudgetType);
            }
        }

        if ((MaterialCategoryEnum.ASSEMBLY_PARTS.msg().equals(dto.getMaterialCategory())
                || MaterialCategoryEnum.VIRTUAL_PARTS.msg().equals(dto.getMaterialCategory()))) {
            dto.setWhetherModel(true);
        }

        return result;
    }

    /**
     * 根据物料新增与变更明细DTO生成物料描述
     *
     * @param detailDTO
     * @return
     */
    private String generateMaterialDesForKuka2022(MaterialAdjustDetailDTO detailDTO, List<String> nonStandardMaterialType) {
        StringBuffer sb = new StringBuffer();
        sb.append(detailDTO.getName());
        //非标件
        if (nonStandardMaterialType.contains(detailDTO.getMaterialClass())) {
            sb.append("#").append(detailDTO.getFigureNumber());
        } else {
            sb.append("#").append(detailDTO.getBrand());
            sb.append("#").append(detailDTO.getModel());
            sb.append("#").append(detailDTO.getOrSparePartsMask());
        }
        return sb.toString().length() <= 230 ? sb.toString() : sb.substring(0, 230);
    }

    private Integer getOracleLengthFromMysqlVarchar(String varchar) {
        if (StringUtils.isEmpty(varchar)) {
            return 0;
        }
        Integer length = varchar.length();

        StringBuilder sb = new StringBuilder();
        String pattern = "[\\u4e00-\\u9fa5]+";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(varchar);
        while (m.find()) {
            sb.append(m.group());
        }
        if (sb.length() == 0) {
            return length;
        }
        // oracle数据库的varchar2 每个汉字将占用3个byte
        return length - sb.length() + sb.length() * 3;
    }

    private String getNonStandardMaterialKey(String materialClassification, String figureNumber) {
        StringBuilder key = new StringBuilder("getNonStandardMaterialKey");
        if (StringUtils.isNotEmpty(materialClassification)) {
            key.append("_").append(materialClassification);
        }
        if (StringUtils.isNotEmpty(figureNumber)) {
            key.append("_").append(figureNumber.toUpperCase());
        }
        return key.toString();
    }

    private String getStandardMaterialKey(String materialClassification, String brand, String model, List<String> characterIgnoreList) {
        StringBuilder key = new StringBuilder("getStandardMaterialKey");
        if (StringUtils.isNotEmpty(materialClassification)) {
            key.append("_").append(materialClassification);
        }
        if (StringUtils.isNotEmpty(brand)) {
            key.append("_").append(brand.toUpperCase());
        }
        if (StringUtils.isNotEmpty(model)) {
            key.append("_").append(removeCharacterIgnoreList(model, characterIgnoreList));
        }
        return key.toString();
    }

    // 详设导入E码校验
    private boolean erpCodeCheck(Long storageId, MilepostDesignPlanDetailDto dto, Map<Long, MaterialDto> forFillImportMap,
                                 Map<String, List<MaterialDto>> erpAdjustMaterialsMap, Map<String, List<MaterialDto>> erpMaterialsMap,
                                 Map<String, List<MaterialDto>> erpDelistAndDeleteMaterialMap, Map<Long, String> organizationMap) {
        String erpCode = dto.getErpCode();
        String materialCategory = dto.getMaterialCategory();
        String serialNumber = dto.getSerialNumber();
        List<String> validResultList = StringUtils.isEmpty(dto.getValidResult()) ? new ArrayList<>()
                : Arrays.asList(dto.getValidResult().split("，"));

        //查询物料变更表中的数据
        List<MaterialDto> adjustMaterials = erpAdjustMaterialsMap.get(erpCode);
        //查询物料信息表中的数据
        List<MaterialDto> materials = erpMaterialsMap.get(erpCode);
        //筛选当前库存组织的物料基础表中的数据
        List<MaterialDto> currentMaterials = new ArrayList<>();
        List<MaterialDto> otherMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(materials)) {
            for (MaterialDto materialDto : materials) {
                if (Objects.equals(materialDto.getOrganizationId(), storageId)) {
                    currentMaterials.add(materialDto);
                } else {
                    otherMaterials.add(materialDto);
                }
            }
        }
        //从基础信息表中查询退市或者已删除的物料
        List<MaterialDto> delistAndDeleteMaterialDtos = erpDelistAndDeleteMaterialMap.get(erpCode);
        List<MaterialDto> currentDelistAndDeleteMaterialDtos = new ArrayList<>();
        List<MaterialDto> otherDelistAndDeleteMaterialDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(delistAndDeleteMaterialDtos)) {
            for (MaterialDto materialDto : delistAndDeleteMaterialDtos) {
                if (Objects.equals(materialDto.getOrganizationId(), storageId)) {
                    currentDelistAndDeleteMaterialDtos.add(materialDto);
                } else {
                    otherDelistAndDeleteMaterialDtos.add(materialDto);
                }
            }
        }
        if (ListUtils.isEmpty(adjustMaterials) && ListUtils.isEmpty(materials)) {
            validResultList.add("erp编码:[" + erpCode + "]在物料变更表和物料信息表中都不存在");
            dto.setValidResult(Joiner.on("，").join(validResultList));
            return false;
        }

        if (ListUtils.isNotEmpty(adjustMaterials)) {
            //adjust表中有数据的情况校验
            //筛选当前库存组织的物料变更表中的数据
            List<MaterialDto> currentAdjustMaterials = new ArrayList<>();
            List<MaterialDto> otherAdjustMaterials = new ArrayList<>();
            for (MaterialDto adjustMaterial : adjustMaterials) {
                if (Objects.equals(adjustMaterial.getOrganizationId(), storageId)) {
                    currentAdjustMaterials.add(adjustMaterial);
                } else {
                    otherAdjustMaterials.add(adjustMaterial);
                }
            }
            //如果当前组织物料变更表中有数据时
            if (ListUtils.isNotEmpty(currentAdjustMaterials)) {
                List<MaterialDto> currentDtoList = currentAdjustMaterials.stream().filter(e ->
                        !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(currentDtoList)) {
                    //本组织adj有(有单据状态！="审批通过")
                    for (MaterialDto mDto : currentDtoList) {
                        validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                    }
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
                //如果大于一个就报错
                if (currentAdjustMaterials.size() > 1) {
                    List<String> adjustCodeList = currentAdjustMaterials.stream().map(MaterialDto::getAdjustCode).collect(Collectors.toList());
                    validResultList.add("erp编码:[" + erpCode + "]对应的物料有多个单据:[+" + adjustCodeList.toString() + "]在同时导入,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    if (currentMaterials.size() > 1) {
                        validResultList.add("erp编码:[" + erpCode + "]对应的物料在本组织中存在多个,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    MaterialDto materialDto = currentMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                                + "]类型不一致, 本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                } else if (ListUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    if (CollectionUtils.isNotEmpty(otherAdjustMaterials)) {
                        if (CollectionUtils.isNotEmpty(otherMaterials)) {
                            List<String> delistFlagItemCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                    .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                    .map(MaterialDto::getItemCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagItemCodeList)) {
                                //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的ERP物料编码XX"
                                validResultList.add("本组织已存在退市的ERP物料编码:[" + delistFlagItemCodeList.toString() + "]");
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                        } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                            validResultList.add("erp编码:[" + erpCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        } else {
                            List<String> delistFlagItemCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                    .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                    .map(MaterialDto::getItemCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagItemCodeList)) {
                                //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的ERP物料编码XX"
                                validResultList.add("本组织已存在退市的ERP物料编码:[" + delistFlagItemCodeList.toString() + "]");
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                    !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(dtoList)) {
                                //本组织adj有 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                                for (MaterialDto mDto : dtoList) {
                                    validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                            "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                                }
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            forFillImportMap.put(Long.parseLong(serialNumber), otherAdjustMaterials.get(0));
                        }
                    } else {
                        if (CollectionUtils.isNotEmpty(otherMaterials)) {
                            forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                        } else {
                            //如果通过erp编码查到本组织已经退市或删除的 报错
                            validResultList.add("erp编码:[" + erpCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                    }
                } else {
                    // material 全部都没有
                    MaterialDto materialDto = currentAdjustMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                                + "]类型不一致, 本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                }
            } else {
                //本组织adj无，其他adj有, 本组织mat有值
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    if (currentMaterials.size() > 1) {
                        validResultList.add("erp编码:[" + erpCode + "]对应的物料在本组织中存在多个,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    MaterialDto materialDto = currentMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                                + "]类型不一致, 本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                } else if (ListUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    //本组织adj无，其他adj有, 本组织mat退市或删除
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        List<String> delistFlagItemCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                .map(MaterialDto::getItemCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagItemCodeList)) {
                            //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的ERP物料编码XX"
                            validResultList.add("本组织已存在退市的ERP物料编码:[" + delistFlagItemCodeList.toString() + "]");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        //如果通过erp编码查到本组织已经退市或删除的 报错
                        validResultList.add("erp编码:[" + erpCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    } else {
                        List<String> delistFlagItemCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                .map(MaterialDto::getItemCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagItemCodeList)) {
                            //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的ERP物料编码XX"
                            validResultList.add("本组织已存在退市的ERP物料编码:[" + delistFlagItemCodeList.toString() + "]");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto mDto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                            }
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(serialNumber), otherAdjustMaterials.get(0));
                    }
                } else {
                    //本组织adj无，其他adj有, 本组织mat无
                    if (ListUtils.isNotEmpty(otherMaterials)) {
                        forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        //如果通过erp编码查到本组织已经退市或删除的 报错
                        validResultList.add("erp编码:[" + erpCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    } else {
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat无 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto mDto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                            }
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(serialNumber), otherAdjustMaterials.get(0));
                    }
                }
            }
        } else {
            // adjust没有数据时(本组织adj无，其他adj无)
            // 本组织adj无，其他adj无，本组织mat有
            if (ListUtils.isNotEmpty(currentMaterials)) {
                if (currentMaterials.size() > 1) {
                    validResultList.add("erp编码:[" + erpCode + "]对应的物料在本组织中存在多个,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
                MaterialDto materialDto = currentMaterials.get(0);
                if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                    materialDto.setAllExtend(true);
                    forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                } else {
                    //物料类型不一致时
                    validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                            + "]类型不一致, " + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                //本组织adj无，其他adj无, 本组织mat退市或删除
                if (CollectionUtils.isNotEmpty(otherMaterials)) {
                    List<String> delistFlagItemCodeList = currentDelistAndDeleteMaterialDtos.stream()
                            .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                            .map(MaterialDto::getItemCode).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(delistFlagItemCodeList)) {
                        //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的ERP物料编码XX"
                        validResultList.add("本组织已存在退市的ERP物料编码:[" + delistFlagItemCodeList.toString() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                    //如果通过erp编码查到本组织已经退市或删除的 报错
                    validResultList.add("erp编码:[" + erpCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            } else {
                //本组织adj无，其他adj无, 本组织mat无
                if (CollectionUtils.isNotEmpty(otherMaterials)) {
                    forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                    //如果通过erp编码查到本组织已经退市或删除的 报错
                    validResultList.add("erp编码:[" + erpCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            }
        }
        dto.setValidResult(Joiner.on("，").join(validResultList));

        return true;
    }

    // 详设导入P码校验
    private boolean pamCodeCheck(Long storageId, MilepostDesignPlanDetailDto dto, Map<Long, MaterialDto> forFillImportMap,
                                 Map<String, List<MaterialDto>> pamAdjustMaterialsMap, Map<String, List<MaterialDto>> pamMaterialsMap,
                                 Map<String, List<MaterialDto>> pamDelistAndDeleteMaterialMap, Map<Long, String> organizationMap) {
        String pamCode = dto.getPamCode();
        String materialCategory = dto.getMaterialCategory();
        String serialNumber = dto.getSerialNumber();
        List<String> validResultList = StringUtils.isEmpty(dto.getValidResult()) ? new ArrayList<>()
                : Arrays.asList(dto.getValidResult().split("，"));

        //查询物料变更表中的数据
        List<MaterialDto> adjustMaterials = pamAdjustMaterialsMap.get(pamCode);
        //查询物料信息表中的数据
        List<MaterialDto> materials = pamMaterialsMap.get(pamCode);
        //筛选当前库存组织的物料基础表中的数据
        List<MaterialDto> currentMaterials = new ArrayList<>();
        List<MaterialDto> otherMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(materials)) {
            for (MaterialDto materialDto : materials) {
                if (Objects.equals(materialDto.getOrganizationId(), storageId)) {
                    currentMaterials.add(materialDto);
                } else {
                    otherMaterials.add(materialDto);
                }
            }
        }
        //从基础信息表中查询退市或者已删除的物料
        List<MaterialDto> delistAndDeleteMaterialDtos = pamDelistAndDeleteMaterialMap.get(pamCode);
        List<MaterialDto> currentDelistAndDeleteMaterialDtos = new ArrayList<>();
        List<MaterialDto> otherDelistAndDeleteMaterialDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(delistAndDeleteMaterialDtos)) {
            for (MaterialDto materialDto : delistAndDeleteMaterialDtos) {
                if (Objects.equals(materialDto.getOrganizationId(), storageId)) {
                    currentDelistAndDeleteMaterialDtos.add(materialDto);
                } else {
                    otherDelistAndDeleteMaterialDtos.add(materialDto);
                }
            }
        }
        if (ListUtils.isEmpty(adjustMaterials) && ListUtils.isEmpty(materials)) {
            validResultList.add("pam编码:[" + pamCode + "]在物料变更表和物料信息表中都不存在");
            dto.setValidResult(Joiner.on("，").join(validResultList));
            return false;
        }

        if (ListUtils.isNotEmpty(adjustMaterials)) {
            //adjust表中有数据的情况校验
            //筛选当前库存组织的物料变更表中的数据
            List<MaterialDto> currentAdjustMaterials = new ArrayList<>();
            List<MaterialDto> otherAdjustMaterials = new ArrayList<>();
            for (MaterialDto adjustMaterial : adjustMaterials) {
                if (Objects.equals(adjustMaterial.getOrganizationId(), storageId)) {
                    currentAdjustMaterials.add(adjustMaterial);
                } else {
                    otherAdjustMaterials.add(adjustMaterial);
                }
            }
            //如果当前组织物料变更表中有数据时
            if (ListUtils.isNotEmpty(currentAdjustMaterials)) {
                List<MaterialDto> currentDtoList = currentAdjustMaterials.stream().filter(e ->
                        !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(currentDtoList)) {
                    //本组织adj有(有单据状态！="审批通过")
                    for (MaterialDto mDto : currentDtoList) {
                        validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                    }
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
                //如果大于一个就报错
                if (currentAdjustMaterials.size() > 1) {
                    List<String> adjustCodeList = currentAdjustMaterials.stream().map(MaterialDto::getAdjustCode).collect(Collectors.toList());
                    validResultList.add("pam编码:[" + pamCode + "]对应的物料有多个单据:[+" + adjustCodeList.toString() + "]在同时导入,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    if (currentMaterials.size() > 1) {
                        validResultList.add("pam编码:[" + pamCode + "]对应的物料在本组织中存在多个,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    MaterialDto materialDto = currentMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                                + "]类型不一致, 本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                } else if (ListUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    if (CollectionUtils.isNotEmpty(otherAdjustMaterials)) {
                        if (CollectionUtils.isNotEmpty(otherMaterials)) {
                            List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                    .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                    .map(MaterialDto::getPamCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                                //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                                validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                        } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                            validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        } else {
                            List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                    .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                    .map(MaterialDto::getPamCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                                //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                                validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                    !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(dtoList)) {
                                //本组织adj有 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                                for (MaterialDto mDto : dtoList) {
                                    validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                            "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                                }
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            forFillImportMap.put(Long.parseLong(serialNumber), otherAdjustMaterials.get(0));
                        }
                    } else {
                        if (CollectionUtils.isNotEmpty(otherMaterials)) {
                            List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                    .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                    .map(MaterialDto::getPamCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                                //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                                validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                                dto.setValidResult(Joiner.on("，").join(validResultList));
                                return false;
                            }
                            forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                        } else {
                            //如果通过pam编码查到本组织已经退市或删除的 报错
                            validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                    }
                } else {
                    // material 全部都没有
                    MaterialDto materialDto = currentAdjustMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                                + "]类型不一致, 本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                }
            } else {
                //本组织adj无，其他adj有, 本组织mat有值
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    if (currentMaterials.size() > 1) {
                        validResultList.add("pam编码:[" + pamCode + "]对应的物料在本组织中存在多个,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    MaterialDto materialDto = currentMaterials.get(0);
                    if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                        materialDto.setAllExtend(true);
                        forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                    } else {
                        //物料类型不一致时
                        validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                                + "]类型不一致, 本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                } else if (ListUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    //本组织adj无，其他adj有, 本组织mat退市或删除
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        //如果通过pam编码查到本组织已经退市或删除的 报错
                        validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    } else {
                        List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                                .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                                .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto mDto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                            }
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(serialNumber), otherAdjustMaterials.get(0));
                    }
                } else {
                    //本组织adj无，其他adj有, 本组织mat无
                    if (ListUtils.isNotEmpty(otherMaterials)) {
                        forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        //如果通过pam编码查到本组织已经退市或删除的 报错
                        validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    } else {
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat无 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto mDto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(mDto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + mDto.getAdjustCode() + "]的Pam编码[" + mDto.getPamCode() + "],请联系标准化处理");
                            }
                            dto.setValidResult(Joiner.on("，").join(validResultList));
                            return false;
                        }
                        forFillImportMap.put(Long.parseLong(serialNumber), otherAdjustMaterials.get(0));
                    }
                }
            }
        } else {
            // adjust没有数据时(本组织adj无，其他adj无)
            // 本组织adj无，其他adj无，本组织mat有
            if (ListUtils.isNotEmpty(currentMaterials)) {
                if (currentMaterials.size() > 1) {
                    validResultList.add("pam编码:[" + pamCode + "]对应的物料在本组织中存在多个,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
                MaterialDto materialDto = currentMaterials.get(0);
                if (Objects.equals(materialDto.getMaterialCategory(), materialCategory)) {
                    materialDto.setAllExtend(true);
                    forFillImportMap.put(Long.parseLong(serialNumber), materialDto);
                } else {
                    //物料类型不一致时
                    validResultList.add("导入的物料类型与本组织物料:[" + materialDto.getName()
                            + "]类型不一致, " + "本组织物料类型为:[" + materialDto.getMaterialCategory() + "]");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                //本组织adj无，其他adj无, 本组织mat退市或删除
                if (CollectionUtils.isNotEmpty(otherMaterials)) {
                    List<String> delistFlagPamCodeList = currentDelistAndDeleteMaterialDtos.stream()
                            .filter(e -> Boolean.TRUE.equals(e.getDelistFlag()) && Boolean.FALSE.equals(e.getDeleteFlag()))
                            .map(MaterialDto::getPamCode).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                        //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                        validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                        dto.setValidResult(Joiner.on("，").join(validResultList));
                        return false;
                    }
                    forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                    //如果通过pam编码查到本组织已经退市或删除的 报错
                    validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            } else {
                //本组织adj无，其他adj无, 本组织mat无
                if (CollectionUtils.isNotEmpty(otherMaterials)) {
                    forFillImportMap.put(Long.parseLong(serialNumber), otherMaterials.get(0));
                } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                    //如果通过pam编码查到本组织已经退市或删除的 报错
                    validResultList.add("pam编码:[" + pamCode + "]查到本组织已经退市或删除的,请联系系统管理员");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            }
        }
        dto.setValidResult(Joiner.on("，").join(validResultList));

        return true;
    }

    /**
     * 标准件预匹配逻辑 - 通过品牌+型号匹配现有物料并自动填充大中小类
     * 基于现有业务逻辑，遍历materialForCheckRepeatMap查找匹配的标准件
     *
     * @param dto 导入数据DTO
     * @param forFillImportMap 物料填充映射
     * @param materialForCheckRepeatMap 物料重复检查映射
     * @param nonStandardMaterialType 非标准物料类型列表
     * @param validResultList 校验结果列表
     * @param characterIgnoreList 字符忽略列表
     * @return 是否匹配成功
     */
    private boolean tryMatchAndFillStandardMaterial(MilepostDesignPlanDetailDto dto,
                                                   Map<Long, MaterialDto> forFillImportMap,
                                                   Map<String, List<MaterialDto>> materialForCheckRepeatMap,
                                                   List<String> nonStandardMaterialType,
                                                   List<String> validResultList,
                                                   List<String> characterIgnoreList) {

        // 检查是否有品牌和型号但缺少大中小类
        if (StringUtils.isNotBlank(dto.getBrand()) &&
            StringUtils.isNotBlank(dto.getModel()) &&
            (StringUtils.isBlank(dto.getMaterialClassification()) ||
             StringUtils.isBlank(dto.getCodingMiddleClass()) ||
             StringUtils.isBlank(dto.getMaterielType()))) {

            logger.info("尝试标准件预匹配，序号：{}，品牌：{}，型号：{}",
                dto.getSerialNumber(), dto.getBrand(), dto.getModel());

            // 遍历materialForCheckRepeatMap查找匹配的标准件
            // 由于预匹配阶段不知道物料大类，需要遍历所有可能的标准件键
            String brand = dto.getBrand().toUpperCase();
            String normalizedModel = removeCharacterIgnoreList(dto.getModel(), characterIgnoreList);
            List<MaterialDto> candidateMaterials = new ArrayList<>();

            for (Map.Entry<String, List<MaterialDto>> entry : materialForCheckRepeatMap.entrySet()) {
                String key = entry.getKey();
                List<MaterialDto> materials = entry.getValue();

                // 检查键是否符合标准件格式：getMaterialForCheckRepeatKey_品牌_型号
                if (key.startsWith("getMaterialForCheckRepeatKey_") &&
                    key.contains("_" + brand + "_") &&
                    key.endsWith("_" + normalizedModel)) {

                    // 过滤出有效的标准件物料
                    List<MaterialDto> validMaterials = materials.stream()
                        .filter(m -> Boolean.FALSE.equals(m.getDeleteFlag()) &&
                                   Boolean.FALSE.equals(m.getDelistFlag()) &&
                                   !nonStandardMaterialType.contains(m.getMaterialClassification()) &&
                                   brand.equals(m.getBrand().toUpperCase()) &&
                                   normalizedModel.equals(removeCharacterIgnoreList(m.getModel(), characterIgnoreList)))
                        .collect(Collectors.toList());

                    candidateMaterials.addAll(validMaterials);
                }
            }

            if (candidateMaterials.size() == 1) {
                MaterialDto matchedMaterial = candidateMaterials.get(0);

                // 自动填充大中小类信息
                dto.setMaterialClassification(matchedMaterial.getMaterialClassification());
                dto.setCodingMiddleClass(matchedMaterial.getCodingMiddleclass());
                dto.setMaterielType(matchedMaterial.getMaterialType());

                // 将匹配到的物料放入forFillImportMap
                matchedMaterial.setAllExtend(true);
                forFillImportMap.put(Long.parseLong(dto.getSerialNumber()), matchedMaterial);

                logger.info("标准件预匹配成功，序号：{}，自动填充大中小类：{}，{}，{}，PAM码：{}",
                    dto.getSerialNumber(),
                    matchedMaterial.getMaterialClassification(),
                    matchedMaterial.getCodingMiddleclass(),
                    matchedMaterial.getMaterialType(),
                    matchedMaterial.getPamCode());

                return true;
            } else if (candidateMaterials.size() > 1) {
                validResultList.add("品牌:[" + dto.getBrand() + "] + 型号:[" + dto.getModel() +
                    "]匹配到多个标准件物料，请手动输入大中小类或联系系统管理员");
                logger.warn("标准件预匹配失败，序号：{}，匹配到多个标准件物料：{}",
                    dto.getSerialNumber(), candidateMaterials.size());
                return false;
            }

            logger.info("标准件预匹配失败，序号：{}，品牌：{}，型号：{}，未找到匹配的标准件物料",
                dto.getSerialNumber(), dto.getBrand(), dto.getModel());
        }

        return false;
    }

    /**
     * 外购物料匹配逻辑 - 通过品牌+型号+图号匹配现有物料并自动填充所有信息
     * 物料大中小类和名称改为非必填，根据填写的品牌、型号、图号加上当前项目的库存组织限制，在物料基础表匹配对应的物料
     *
     * @param dto 导入数据DTO
     * @param organizationIds 库存组织ID列表
     * @param forFillImportMap 物料填充映射
     * @param validResultList 校验结果列表
     * @param characterIgnoreList 字符忽略列表
     * @param nonStandardMaterialType 非标件大类列表
     * @return 是否匹配成功
     */
    private boolean tryMatchPurchasedMaterial(MilepostDesignPlanDetailDto dto,
                                             List<Long> organizationIds,
                                             Map<Long, MaterialDto> forFillImportMap,
                                             List<String> validResultList,
                                             List<String> characterIgnoreList,
                                             List<String> nonStandardMaterialType) {

        String brand = dto.getBrand();
        String model = dto.getModel();
        String figureNumber = dto.getFigureNumber();

        // 检查必要字段：至少需要填写品牌、型号、图号中的一个
        if (StringUtils.isBlank(brand) && StringUtils.isBlank(model) && StringUtils.isBlank(figureNumber)) {
            validResultList.add("外购物料至少需要填写品牌、型号、图号中的一个");
            return false;
        }

        logger.info("尝试外购物料匹配，序号：{}，品牌：{}，型号：{}，图号：{}，库存组织：{}",
            dto.getSerialNumber(), brand, model, figureNumber, organizationIds);

        // 数据预处理
        List<String> brandList = new ArrayList<>();
        if (StringUtils.isNotBlank(brand)) {
            brandList.add(brand.toUpperCase());
        }

        List<String> modelList = new ArrayList<>();
        if (StringUtils.isNotBlank(model)) {
            modelList.add(removeCharacterIgnoreList(model, characterIgnoreList));
        }

        List<String> figureNumberList = new ArrayList<>();
        if (StringUtils.isNotBlank(figureNumber)) {
            figureNumberList.add(figureNumber);
        }

        try {

            // 获取当前登录用户所拥有的业务实体(OU)列表
            // SystemContext.getOus()返回当前使用单位下登录用户所拥有的ou列表
            List<Long> ous = SystemContext.getOus();

            // 初始化库存组织ID集合，用于存储所有OU对应的库存组织
            List<Long> currOrganizationIds = new ArrayList<>();

            // 遍历每个业务实体，获取其对应的库存组织关系
            for (Long ou : ous) {
                // 通过基础数据服务查询指定OU下的组织关系列表
                // OrganizationRel包含业务实体与库存组织的映射关系
                List<OrganizationRel> organizationRels = basedataExtService.queryByOuId(ou);

                // 提取所有组织关系中的库存组织ID，并添加到当前库存组织ID集合中
                currOrganizationIds.addAll(organizationRels.stream()
                    .map(OrganizationRel::getOrganizationId)
                    .collect(Collectors.toList()));
            }


            // 调用查询接口
            List<MaterialDto> matchedMaterials = materialExtService.getAllFigureNumberAndBrandAndModelMaterial(
                    currOrganizationIds, figureNumberList, brandList, modelList);

            // 过滤有效物料（未删除、未退市）并实现精确匹配逻辑
            logger.info("外购物料匹配，序号：{}，SQL查询返回物料数量：{}", dto.getSerialNumber(), matchedMaterials.size());

            List<MaterialDto> validMaterials = matchedMaterials.stream()
                .filter(m -> Boolean.FALSE.equals(m.getDeleteFlag()) && Boolean.FALSE.equals(m.getDelistFlag()))
                .filter(m -> isExactMatch(m, brand, model, figureNumber, characterIgnoreList, nonStandardMaterialType))
                .collect(Collectors.toList());

            logger.info("外购物料匹配，序号：{}，精确匹配后物料数量：{}", dto.getSerialNumber(), validMaterials.size());

            // 结果处理
            if (validMaterials.isEmpty()) {
                validResultList.add("根据所填内容，当前库存组织下匹配不到或匹配到多条数据");
                logger.info("外购物料匹配失败，序号：{}，未找到匹配的物料", dto.getSerialNumber());
                return false;
            } else if (validMaterials.size() > 1) {
                validResultList.add("根据所填内容，当前库存组织下匹配不到或匹配到多条数据");
                logger.warn("外购物料匹配失败，序号：{}，匹配到多条物料：{}", dto.getSerialNumber(), validMaterials.size());
                return false;
            } else {
                // 匹配成功，自动填充
                MaterialDto matchedMaterial = validMaterials.get(0);
                matchedMaterial.setAllExtend(true);
                forFillImportMap.put(Long.parseLong(dto.getSerialNumber()), matchedMaterial);

                logger.info("外购物料匹配成功，序号：{}，PAM码：{}，物料名称：{}",
                    dto.getSerialNumber(), matchedMaterial.getPamCode(), matchedMaterial.getItemInfo());
                return true;
            }
        } catch (Exception e) {
            logger.error("外购物料匹配异常，序号：{}", dto.getSerialNumber(), e);
            validResultList.add("外购物料匹配过程中发生异常，请联系系统管理员");
            return false;
        }
    }

    /**
     * 精确匹配物料信息
     * 如果用户填写了多个字段（品牌、型号、图号），这些字段之间应该是AND关系
     *
     * @param material 物料信息
     * @param inputBrand 输入的品牌
     * @param inputModel 输入的型号
     * @param inputFigureNumber 输入的图号
     * @param characterIgnoreList 字符忽略列表
     * @param nonStandardMaterialType 非标件大类列表
     * @return 是否精确匹配
     */
    private boolean isExactMatch(MaterialDto material, String inputBrand, String inputModel,
                                String inputFigureNumber, List<String> characterIgnoreList,
                                List<String> nonStandardMaterialType) {

        logger.debug("精确匹配检查，物料PAM码：{}，输入品牌：{}，输入型号：{}，输入图号：{}，物料品牌：{}，物料型号：{}，物料图号：{}",
            material.getPamCode(), inputBrand, inputModel, inputFigureNumber,
            material.getBrand(), material.getModel(), material.getFigureNumber());

        // 排除非标件大类的过滤条件
        if (CollectionUtils.isNotEmpty(nonStandardMaterialType) &&
            StringUtils.isNotBlank(material.getMaterialClassification()) &&
            nonStandardMaterialType.contains(material.getMaterialClassification())) {
            logger.debug("排除非标件大类物料，物料分类：{}", material.getMaterialClassification());
            return false;
        }

        // 品牌匹配：如果用户填写了品牌，则必须匹配
        if (StringUtils.isNotBlank(inputBrand)) {
            String materialBrand = material.getBrand();
            if (StringUtils.isBlank(materialBrand) ||
                !inputBrand.toUpperCase().equals(materialBrand.toUpperCase())) {
                logger.debug("品牌匹配失败，输入：{}，物料：{}", inputBrand, materialBrand);
                return false;
            }
        }

        // 型号匹配：如果用户填写了型号，则必须匹配（考虑字符忽略规则）
        if (StringUtils.isNotBlank(inputModel)) {
            String materialModel = material.getModel();
            if (StringUtils.isBlank(materialModel)) {
                logger.debug("型号匹配失败，物料型号为空");
                return false;
            }
            String normalizedInputModel = removeCharacterIgnoreList(inputModel, characterIgnoreList);
            String normalizedMaterialModel = removeCharacterIgnoreList(materialModel, characterIgnoreList);
            if (!normalizedInputModel.equals(normalizedMaterialModel)) {
                logger.debug("型号匹配失败，标准化后输入：{}，标准化后物料：{}", normalizedInputModel, normalizedMaterialModel);
                return false;
            }
        }

        // 图号匹配：如果用户填写了图号，则必须匹配
        if (StringUtils.isNotBlank(inputFigureNumber)) {
            String materialFigureNumber = material.getFigureNumber();
            if (StringUtils.isBlank(materialFigureNumber) ||
                !inputFigureNumber.equals(materialFigureNumber)) {
                logger.debug("图号匹配失败，输入：{}，物料：{}", inputFigureNumber, materialFigureNumber);
                return false;
            }
        }

        logger.debug("精确匹配成功，物料PAM码：{}", material.getPamCode());
        return true;
    }

    /**
     * 详设导入没P、没E校验
     * 支持标准件通过品牌+型号自动匹配并填充大中小类
     *
     * @param storageId 存储ID
     * @param dto 导入数据DTO
     * @param materialClasses 物料大中小类列表
     * @param unitNameCodeMap 单位名称编码映射
     * @param orSparePartsMaskSet 备件标识集合
     * @param nonStandardMaterialType 非标准物料类型列表
     * @param machinedPartTypeList 加工件类型列表
     * @param forFillImportMap 物料填充映射
     * @param materialForCheckRepeatMap 物料重复检查映射
     * @param materialForCheckAdjustRepeatMap 物料调整重复检查映射
     * @param adjustCodeForKukaCodeRuleMap 调整编码映射
     * @param standardMaterials 标准物料列表
     * @param isChange 是否变更
     * @param nonStandardMaterials 非标准物料列表
     * @param materialClassMap 物料分类映射
     * @param organizationMap 组织映射
     * @param characterIgnoreList 字符忽略列表
     * @return 校验结果
     */
    private boolean erpCodeIsEmptyAndPamCodeIsEmptyCheck(Long storageId, MilepostDesignPlanDetailDto dto,
                                                         List<MerielSubclassVO> materialClasses, Map<String, String> unitNameCodeMap,
                                                         Set<String> orSparePartsMaskSet, List<String> nonStandardMaterialType,
                                                         List<String> machinedPartTypeList, Map<Long, MaterialDto> forFillImportMap,
                                                         Map<String, List<MaterialDto>> materialForCheckRepeatMap,
                                                         Map<String, List<MaterialDto>> materialForCheckAdjustRepeatMap,
                                                         Map<String, List<MaterialDto>> adjustCodeForKukaCodeRuleMap, List<String> standardMaterials,
                                                         Boolean isChange, List<String> nonStandardMaterials,
                                                         Map<String, MerielSubclassVO> materialClassMap, Map<Long, String> organizationMap,
                                                         List<String> characterIgnoreList, List<Long> organizationIds) {
        boolean result = true;
        List<String> validResultList = StringUtils.isEmpty(dto.getValidResult()) ? new ArrayList<>()
                : Arrays.asList(dto.getValidResult().split("，"));
        String serialNumber = dto.getSerialNumber();

        // ========== 根据物料类型区分处理逻辑 ==========
        String materialCategory = dto.getMaterialCategory();
        boolean materialMatched = false; // 标记是否通过匹配获得物料信息
        String materialClassification = dto.getMaterialClassification(); // 提前定义，供所有分支使用

        if ("外购物料".equals(materialCategory)) {
            // ========== 外购物料处理逻辑：根据物料分类区分处理方式 ==========
            logger.info("检测到外购物料，序号：{}，物料分类：{}", dto.getSerialNumber(), materialClassification);

            // 检查是否为非标件
            if (StringUtils.isNotBlank(materialClassification) && nonStandardMaterialType.contains(materialClassification)) {
                // 外购物料 + 非标件：使用原来的逻辑进行处理（需要物料大中小类和名称必填）
                logger.info("外购物料且为非标件，序号：{}，使用原有校验逻辑", dto.getSerialNumber());
                // 跳过新的匹配逻辑，继续执行后续的原有校验逻辑
                materialMatched = false;

                // 执行原有的大中小类校验逻辑
                final String finalMaterialClassification = materialClassification;
                if (StringUtils.isEmpty(materialClassification)) {
                    validResultList.add("物料大类不能为空");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                } else if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(finalMaterialClassification))) {
                    result = false;
                    validResultList.add("物料大类:[" + materialClassification + "]尚未在物料编码规则:[" + MaterialCodeRuleEnum.KUKA2022.getName() + "]中维护,请先维护");
                }

                if (StringUtils.isEmpty(dto.getCodingMiddleClass())) {
                    result = false;
                    validResultList.add("物料中类不能为空");
                } else if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(finalMaterialClassification)
                        && materialClass.getMiddileValue().equals(dto.getCodingMiddleClass()))) {
                    result = false;
                    validResultList.add("此物料中类不在填写的大类中");
                }

                if (StringUtils.isEmpty(dto.getMaterielType())) {
                    result = false;
                    validResultList.add("物料小类不能为空");
                } else {
                    if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(finalMaterialClassification)
                            && materialClass.getMiddileValue().equals(dto.getCodingMiddleClass()) && materialClass.getValue().equals(dto.getMaterielType()))) {
                        result = false;
                        validResultList.add("此物料小类不在填写的大类或中类中");
                    }
                }
            } else {
                // 外购物料 + 标准件：使用新的匹配逻辑（物料大中小类和名称非必填）
                logger.info("外购物料且为标准件，序号：{}，开始外购物料匹配", dto.getSerialNumber());

                materialMatched = tryMatchPurchasedMaterial(dto, organizationIds, forFillImportMap, validResultList, characterIgnoreList, nonStandardMaterialType);

                if (materialMatched) {
                    logger.info("外购物料匹配成功，序号：{}，物料信息已自动填充", dto.getSerialNumber());
                } else {
                    // 外购物料匹配失败，返回错误
                    logger.warn("外购物料匹配失败，序号：{}，无法确定物料信息", dto.getSerialNumber());
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            }
        } else if ("装配件".equals(materialCategory) || "虚拟件".equals(materialCategory) || "看板物料".equals(materialCategory)) {
            // ========== 装配件/虚拟件/看板物料：保持原有逻辑 ==========
            logger.info("检测到{}，序号：{}，执行原有校验逻辑", materialCategory, dto.getSerialNumber());

//            // 尝试标准件预匹配
//            boolean standardMaterialMatched = tryMatchAndFillStandardMaterial(dto, forFillImportMap,
//                materialForCheckRepeatMap, nonStandardMaterialType, validResultList, characterIgnoreList);
//
//            if (standardMaterialMatched) {
//                logger.info("标准件预匹配成功，序号：{}，跳过大中小类必填校验", dto.getSerialNumber());
//                materialMatched = true;
//            } else {
//                // ========== 原有的大中小类校验逻辑 ==========
//                // 为lambda表达式创建final变量
//                final String finalMaterialClassification = materialClassification;
//                if (StringUtils.isEmpty(materialClassification)) {
//                    validResultList.add("物料大类不能为空");
//                    dto.setValidResult(Joiner.on("，").join(validResultList));
//                    return false;
//                } else if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(finalMaterialClassification))) {
//                    result = false;
//                    validResultList.add("物料大类:[" + materialClassification + "]尚未在物料编码规则:[" + MaterialCodeRuleEnum.KUKA2022.getName() + "]中维护,请先维护");
//                }
//
//                if (StringUtils.isEmpty(dto.getCodingMiddleClass())) {
//                    result = false;
//                    validResultList.add("物料中类不能为空");
//                } else if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(finalMaterialClassification)
//                        && materialClass.getMiddileValue().equals(dto.getCodingMiddleClass()))) {
//                    result = false;
//                    validResultList.add("此物料中类不在填写的大类中");
//                }
//
//                if (StringUtils.isEmpty(dto.getMaterielType())) {
//                    result = false;
//                    validResultList.add("物料小类不能为空");
//                } else {
//                    if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(finalMaterialClassification)
//                            && materialClass.getMiddileValue().equals(dto.getCodingMiddleClass()) && materialClass.getValue().equals(dto.getMaterielType()))) {
//                        result = false;
//                        validResultList.add("此物料小类不在填写的大类或中类中");
//                    }
//                }
//            }
            // ========== 原有的大中小类校验逻辑 ==========
            // 为lambda表达式创建final变量
            final String finalMaterialClassification = materialClassification;
            if (StringUtils.isEmpty(materialClassification)) {
                validResultList.add("物料大类不能为空");
                dto.setValidResult(Joiner.on("，").join(validResultList));
                return false;
            } else if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(finalMaterialClassification))) {
                result = false;
                validResultList.add("物料大类:[" + materialClassification + "]尚未在物料编码规则:[" + MaterialCodeRuleEnum.KUKA2022.getName() + "]中维护,请先维护");
            }

            if (StringUtils.isEmpty(dto.getCodingMiddleClass())) {
                result = false;
                validResultList.add("物料中类不能为空");
            } else if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(finalMaterialClassification)
                    && materialClass.getMiddileValue().equals(dto.getCodingMiddleClass()))) {
                result = false;
                validResultList.add("此物料中类不在填写的大类中");
            }

            if (StringUtils.isEmpty(dto.getMaterielType())) {
                result = false;
                validResultList.add("物料小类不能为空");
            } else {
                if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(finalMaterialClassification)
                        && materialClass.getMiddileValue().equals(dto.getCodingMiddleClass()) && materialClass.getValue().equals(dto.getMaterielType()))) {
                    result = false;
                    validResultList.add("此物料小类不在填写的大类或中类中");
                }
            }
        } else {
            // 未知物料类型
            validResultList.add("不支持的物料类型：" + materialCategory);
            dto.setValidResult(Joiner.on("，").join(validResultList));
            return false;
        }

        // 单位校验（所有物料类型都需要）
        if (StringUtils.isBlank(dto.getUnit())) {
            result = false;
            validResultList.add("单位不能为空");
        } else if (!unitNameCodeMap.containsKey(dto.getUnit())) {
            result = false;
            validResultList.add("单位必须为字典项measurement_unit的值");
        }

        // 名称校验：根据物料类型和分类进行不同的校验
        if (!"外购物料".equals(materialCategory)) {
            // 装配件/虚拟件/看板物料：名称必填
            if (StringUtils.isBlank(dto.getName())) {
                result = false;
                validResultList.add("名称不能为空");
            } else if (dto.getName().length() > 150) {
                result = false;
                validResultList.add("名称字符长度不能超过150");
            }
        } else {
            // 外购物料：根据物料分类决定是否需要名称校验
            if (StringUtils.isNotBlank(materialClassification) && nonStandardMaterialType.contains(materialClassification)) {
                // 外购物料 + 非标件：名称必填（使用原有逻辑）
                if (StringUtils.isBlank(dto.getName())) {
                    result = false;
                    validResultList.add("名称不能为空");
                } else if (dto.getName().length() > 150) {
                    result = false;
                    validResultList.add("名称字符长度不能超过150");
                }
            }
            // 外购物料 + 标准件：名称非必填（通过匹配逻辑自动填充）
        }
//        if (StringUtils.isBlank(dto.getOrSparePartsMask())) {
//            result = false;
//            validResultList.add("备件标识不能为空");
//        } else if (!orSparePartsMaskSet.contains(dto.getOrSparePartsMask())) {
//            result = false;
//            validResultList.add("备件标识必须为组织参数中配置的备件标识中的值");
//        }

        // 获取物料大类（可能是用户输入的，也可能是自动填充的）
        // 注意：materialClassification变量已在前面定义，这里可能需要重新获取最新值（如果被匹配逻辑修改过）
        if (materialMatched) {
            // 如果通过匹配获得了物料信息，重新获取可能被更新的物料大类
            materialClassification = dto.getMaterialClassification();
        }

        // 对于外购物料，根据匹配结果和物料分类处理
        if ("外购物料".equals(materialCategory)) {
            if (materialMatched) {
                // 外购物料匹配成功，物料信息已自动填充，跳过后续校验
                logger.info("外购物料匹配成功，序号：{}，跳过后续物料大类相关校验", dto.getSerialNumber());
                dto.setValidResult(Joiner.on("，").join(validResultList));
                return result;
            } else {
                // 检查是否为非标件
                if (StringUtils.isNotBlank(materialClassification) && nonStandardMaterialType.contains(materialClassification)) {
                    // 外购物料 + 非标件：继续执行原有校验逻辑，不返回
                    logger.info("外购物料且为非标件，序号：{}，继续执行原有校验逻辑", dto.getSerialNumber());
                } else {
                    // 外购物料 + 标准件：匹配失败的情况，已在上面处理并返回
                    logger.warn("外购物料匹配失败，序号：{}，无法确定物料信息", dto.getSerialNumber());
                    validResultList.add("外购物料匹配失败，无法确定物料信息");
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }
            }
        }

        // 对于其他情况，确保物料大类不为空（经过预匹配或用户输入后应该有值）
        if (StringUtils.isEmpty(materialClassification)) {
            validResultList.add("无法确定物料大类，请检查输入信息");
            dto.setValidResult(Joiner.on("，").join(validResultList));
            return false;
        }

        //非标件校验
        if (nonStandardMaterialType.contains(materialClassification)) {
//            //「加工件分类」字段只能选择字典项中的值且不能为空
//            if (StringUtils.isBlank(dto.getMachiningPartType())) {
//                result = false;
//                validResultList.add("非标件,加工分类不能为空");
//            } else if (!machinedPartTypeList.contains(dto.getMachiningPartType())) {
//                result = false;
//                validResultList.add("非标件,加工分类只能选择字典项中的值");
//            }
            // 图号必填
            if (StringUtils.isBlank(dto.getFigureNumber())) {
                result = false;
                validResultList.add("非标件,图号不能为空");
            } else {
                if (!Boolean.TRUE.equals(isChange)) {
                    if (CollectionUtils.isNotEmpty(nonStandardMaterials) && nonStandardMaterials.contains(dto.getFigureNumber())) {
                        result = false;
                        validResultList.add("同一个WBS下,所导入数据中,存在多条图号:[" + dto.getFigureNumber() + "]的数据");
                    } else {
                        nonStandardMaterials.add(dto.getFigureNumber());
                    }
                }
            }
//            if (StringUtils.isBlank(dto.getChartVersion()) || !StringUtils.isNumeric(dto.getChartVersion())) {
//                result = false;
//                validResultList.add("非标件,图纸版本号不能为空,或只能为数字");
//            } else if (dto.getChartVersion().length() > 2) {
//                result = false;
//                validResultList.add("图纸版本号字符长度不能超过2");
//            }
//            if (StringUtils.isBlank(dto.getMaterial())) {
//                result = false;
//                validResultList.add("非标件,材质不能为空");
//            }
//            if (StringUtils.isBlank(dto.getMaterialProcessing())) {
//                result = false;
//                validResultList.add("非标件,表面处理不能为空");
//            }
//            if (null == dto.getUnitWeight()) {
//                result = false;
//                validResultList.add("非标件,单位重量不能为空");
//            }
        } else {
            //标准件校验
            if (!materialMatched) {
                // 如果不是通过预匹配得到的标准件，执行原有标准件校验
                String model = dto.getModel();
                if (StringUtils.isBlank(model)) {
                    result = false;
                    validResultList.add("型号/规格不能为空");
                } else {
                    if (model.length() > 150) {
                        result = false;
                        validResultList.add("型号/规格字符长度不能超过150");
                    }
                }

                String brandName = dto.getBrand();
                if (StringUtils.isBlank(brandName)) {
                    result = false;
                    validResultList.add("品牌不能为空");
                } else {
                    if (ListUtils.isNotEmpty(standardMaterials)
                            && standardMaterials.contains(brandName + ":" + removeCharacterIgnoreList(model, characterIgnoreList))) {
                        result = false;
                        validResultList.add("所导入数据中,存在多条品牌:[" + brandName + "] + 型号/规格:[" + model + "的数据");
                    } else {
                        standardMaterials.add(brandName + ":" + removeCharacterIgnoreList(model, characterIgnoreList));
                    }
                }

                if (StringUtils.isNotEmpty(dto.getChartVersion()) && !StringUtils.isNumeric(dto.getChartVersion())) {
                    result = false;
                    validResultList.add("图纸版本号只能为数字");
                }
            }
        }

        if (result) {
            //非标件校验
            if (nonStandardMaterialType.contains(materialClassification)) {
                result = checkDesignPlanDetailQuotable(dto, dto.getFigureNumber(), null, null, storageId, validResultList,
                        forFillImportMap, materialForCheckRepeatMap, materialForCheckAdjustRepeatMap, adjustCodeForKukaCodeRuleMap,
                        nonStandardMaterialType, materialClassMap, organizationMap, characterIgnoreList);
                dto.setImportMaterialType(KukaMaterialType.NON_STANDARD_MATERIAL.getCode());
            } else {
                String brandName = dto.getBrand();
                result = checkDesignPlanDetailQuotable(dto, null, brandName, dto.getModel(), storageId, validResultList,
                        forFillImportMap, materialForCheckRepeatMap, materialForCheckAdjustRepeatMap, adjustCodeForKukaCodeRuleMap,
                        nonStandardMaterialType, materialClassMap, organizationMap, characterIgnoreList);
                if (!result) {
                    dto.setValidResult(Joiner.on("，").join(validResultList));
                    return false;
                }

                // 如果不是预匹配的标准件，检查是否需要物料新增
                if (!materialMatched && Objects.isNull(forFillImportMap.get(Long.parseLong(serialNumber)))) {
                    result = false;
                    validResultList.add("标准件需要物料新增,不允许通过详设导入新增");
                }

                dto.setImportMaterialType(KukaMaterialType.STANDARD_MATERIAL.getCode());
            }
        }
        dto.setValidResult(Joiner.on("，").join(validResultList));

        return result;
    }

    private String getErpCodeRuleKey(String materialClassification, String codingMiddleclass, String materialType) {
        return String.format("getFlexibleErpCodeRuleKey_%s_%s_%s", materialClassification, codingMiddleclass, materialType);
    }

    //去除字符串中含有 characterIgnoreList集合字符忽略
    private String removeCharacterIgnoreList(String str, List<String> characterIgnoreList) {
        if (StringUtils.isEmpty(str) || CollectionUtils.isEmpty(characterIgnoreList)) {
            return StringUtils.isEmpty(str) ? str : str.toUpperCase(Locale.ROOT);
        }
        for (String character : characterIgnoreList) {
            if (Objects.equals(character, "空格")) {
                //给str去空格
                str = str.replaceAll(" ", "");
            } else {
                str = str.replace(character, "");
            }
        }
        return str.toUpperCase(Locale.ROOT);
    }

    @Override
    public DataResponse<List<MilepostDesignPlanDetailDto>> validBatchImportDesignPlanDetailMaterial(
            List<MilepostDesignPlanDetailDto> kukaDetailExcelVos, Long storageId, Long unitId,
            List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo, Long markId,
            Boolean wbsEnabled, Long projectId, Boolean isChange, String operateType) {

        logger.info("WBS-详设-批量导入-模版数据:{}", JsonUtils.toString(kukaDetailExcelVos));
        DataResponse<List<MilepostDesignPlanDetailDto>> response = Response.dataResponse();
        List<MilepostDesignPlanDetailDto> responseDataList = new ArrayList<>();

        // 检查是否为多层级导入场景，并根据操作类型决定校验范围
        List<MilepostDesignPlanDetailDto> dataForMultiLevelValidation = getDataForMultiLevelValidation(kukaDetailExcelVos, operateType);
        boolean isMultiLevel = isMultiLevelImport(dataForMultiLevelValidation);

        if (isMultiLevel) {
            logger.info("检测到多层级导入场景，开始处理多层级逻辑，操作类型：{}，校验数据量：{}", operateType, dataForMultiLevelValidation.size());

            // 执行多层级校验和关系构建（只针对需要校验的数据）
            boolean multiLevelProcessResult = processMultiLevelImport(dataForMultiLevelValidation, projectId);

            if (!multiLevelProcessResult) {
                // 多层级处理失败，直接返回校验错误
                logger.warn("多层级处理失败，返回校验错误");
                response.setMsg(MaterialCodeRuleEnum.KUKA2022.getCode());
                response.setData(kukaDetailExcelVos);
                return response;
            }

            logger.info("多层级处理完成，继续执行原有导入逻辑");
        }

        // 根据操作类型处理不同的导入逻辑
        if (MilepostDesignPlanDetailKukaWbsBatchImportExcelOperationTypeEnum.ADD.getType().equalsIgnoreCase(operateType)) {
            // 处理新建详设批量导入
            processAddBatchImport(kukaDetailExcelVos, storageId, unitId, markId,
                    wbsEnabled, projectId, isChange, responseDataList, designPlanDetailDtoForWebInfo);
        } else if (MilepostDesignPlanDetailKukaWbsBatchImportExcelOperationTypeEnum.EDIT.getType().equalsIgnoreCase(operateType)) {
            // 处理更新详设批量导入
            processUpdateBatchImport(kukaDetailExcelVos, storageId, unitId, designPlanDetailDtoForWebInfo,
                    markId, wbsEnabled, projectId, isChange, responseDataList);
        }
        boolean existValidResult = responseDataList.stream().anyMatch(e -> StringUtils.isNotBlank(e.getValidResult()));
        if (existValidResult) {
            response.setMsg(MaterialCodeRuleEnum.KUKA2022.getCode());
        }

        // 根据上下级关系设置父级物料描述
        setParentMaterielDescrForAllData(responseDataList);
        response.setData(responseDataList);
        return response;
    }

    // 处理新建详设批量导入
    private void processAddBatchImport(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos,
                                       Long storageId, Long unitId, Long markId, Boolean wbsEnabled,
                                       Long projectId, Boolean isChange, List<MilepostDesignPlanDetailDto> responseDataList,
                                       List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo) {

        for (MilepostDesignPlanDetailDto kukaDetailExcelVo : kukaDetailExcelVos) {
            String wbsPath = kukaDetailExcelVo.getWbsPath();
            String parentNum = kukaDetailExcelVo.getParentNum();

            // 检查是否为多层级导入且wbsPath为空的情况
            if (StringUtils.isNotBlank(parentNum) && StringUtils.isBlank(wbsPath)) {
                // 多层级导入数据，父子关系已在processMultiLevelImport中建立
                // 直接进行物料校验和导入
                processMultiLevelItemImport(kukaDetailExcelVo, storageId, unitId,
                                          markId, wbsEnabled, projectId, isChange,
                                          responseDataList, designPlanDetailDtoForWebInfo, kukaDetailExcelVos);
                continue;
            }

            // 检查wbsPath是否为空（非多层级情况下必须有wbsPath）
            if (StringUtils.isBlank(wbsPath)) {
                addErrorResponse(kukaDetailExcelVo, responseDataList);
                continue;
            }

            boolean isSubMaterialFlag = wbsPath.contains("/");

            if (isSubMaterialFlag) {
                // 处理包含PamCode的WBS路径
                processWbsPathWithSubMaterials(kukaDetailExcelVo, wbsPath, projectId, storageId, unitId,
                        markId, wbsEnabled, isChange, responseDataList, designPlanDetailDtoForWebInfo);
            } else {
                // 处理简单WBS路径
                processSimpleWbsPath(kukaDetailExcelVo, wbsPath, projectId, storageId, unitId,
                        markId, wbsEnabled, isChange, responseDataList, designPlanDetailDtoForWebInfo);
            }
        }
    }

    /**
     * 处理多层级导入的物料校验和导入
     *
     * @param kukaDetailExcelVo 导入数据
     * @param storageId 存储ID
     * @param unitId 单位ID
     * @param markId 标记ID
     * @param wbsEnabled WBS是否启用
     * @param projectId 项目ID
     * @param isChange 是否变更
     * @param responseDataList 响应数据列表
     * @param designPlanDetailDtoForWebInfo Web信息
     * @param allImportData 所有导入数据
     */
    private void processMultiLevelItemImport(MilepostDesignPlanDetailDto kukaDetailExcelVo,
                                           Long storageId, Long unitId, Long markId, Boolean wbsEnabled,
                                           Long projectId, Boolean isChange,
                                           List<MilepostDesignPlanDetailDto> responseDataList,
                                           List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo,
                                           List<MilepostDesignPlanDetailDto> allImportData) {

        logger.info("处理多层级导入物料，parentNum：{}", kukaDetailExcelVo.getParentNum());

        // 多层级导入数据的父子关系已经在processMultiLevelImport阶段建立
        // 这里直接进行物料校验和导入
        Long parentId = kukaDetailExcelVo.getParentId();

        if (parentId == null) {
            logger.warn("多层级导入数据缺少parentId，parentNum：{}", kukaDetailExcelVo.getParentNum());
            kukaDetailExcelVo.setValidResult("多层级导入数据缺少父级关系");
            responseDataList.add(kukaDetailExcelVo);
            return;
        }

        logger.info("处理多层级导入，parentNum：{}，parentId：{}", kukaDetailExcelVo.getParentNum(), parentId);

        // 查找父级的WBS摘要码
        String wbsSummaryCode = findParentWbsSummaryCode(kukaDetailExcelVo, responseDataList, allImportData);

        if (StringUtils.isBlank(wbsSummaryCode)) {
            logger.warn("未找到父级WBS摘要码，parentNum：{}", kukaDetailExcelVo.getParentNum());
            kukaDetailExcelVo.setValidResult("未找到父级WBS摘要码，请确保父级数据已正确处理");
            responseDataList.add(kukaDetailExcelVo);
            return;
        }

        // 设置parentWbsSummaryCode
        kukaDetailExcelVo.setParentWbsSummaryCode(wbsSummaryCode);

        // 验证并导入
        DataResponse<List<MilepostDesignPlanDetailDto>> listDataResponse = validImportDesignPlanDetailMaterial(
                Lists.newArrayList(kukaDetailExcelVo),
                storageId,
                unitId,
                designPlanDetailDtoForWebInfo,
                markId,
                wbsSummaryCode,
                wbsEnabled,
                projectId,
                isChange,
                parentId,
                Boolean.TRUE  // 跳过多层级检查，因为已在批量导入方法中处理
        );

        if (listDataResponse != null && CollectionUtils.isNotEmpty(listDataResponse.getData())) {
            // 为返回的数据设置parentWbsSummaryCode
            for (MilepostDesignPlanDetailDto resultDto : listDataResponse.getData()) {
                resultDto.setParentWbsSummaryCode(wbsSummaryCode);
            }
            responseDataList.addAll(listDataResponse.getData());
        }

        logger.info("多层级导入物料处理完成，parentNum：{}", kukaDetailExcelVo.getParentNum());
    }

    /**
     * 从导入数据中查找父级信息
     * 支持通过parentNum或parentId(UID)查找
     *
     * @param currentDto 当前导入数据
     * @param responseDataList 已处理的响应数据列表
     * @return 父级数据，如果未找到返回null
     */
    private MilepostDesignPlanDetailDto findParentFromImportData(MilepostDesignPlanDetailDto currentDto,
                                                               List<MilepostDesignPlanDetailDto> responseDataList) {
        String currentParentNum = currentDto.getParentNum();
        Long parentId = currentDto.getParentId();

        // 如果是第一层级，没有父级
        if (StringUtils.isBlank(currentParentNum) || !currentParentNum.contains(".")) {
            return null;
        }

        // 方法1：通过parentNum查找父级
        String parentLevelNum = getParentLevelNum(currentParentNum);
        if (StringUtils.isNotBlank(parentLevelNum)) {
            MilepostDesignPlanDetailDto parentByNum = responseDataList.stream()
                    .filter(dto -> parentLevelNum.equals(dto.getParentNum()))
                    .findFirst()
                    .orElse(null);

            if (parentByNum != null) {
                return parentByNum;
            }
        }

        // 方法2：如果通过parentNum找不到，且有parentId，尝试通过UID查找
        if (parentId != null) {
            String parentIdStr = String.valueOf(parentId);
            return responseDataList.stream()
                    .filter(dto -> parentIdStr.equals(dto.getUid()))
                    .findFirst()
                    .orElse(null);
        }

        return null;
    }

    /**
     * 查找多层级导入中的父级信息
     * 优先从内存查找，再从数据库查找
     *
     * @param currentDto 当前数据
     * @param responseDataList 已处理的数据列表
     * @param allImportData 所有导入数据（用于查找父级）
     * @return 父级的wbsSummaryCode，如果未找到返回null
     */
    private String findParentWbsSummaryCode(MilepostDesignPlanDetailDto currentDto,
                                          List<MilepostDesignPlanDetailDto> responseDataList,
                                          List<MilepostDesignPlanDetailDto> allImportData) {
        Long parentId = currentDto.getParentId();
        String parentNum = currentDto.getParentNum();

        logger.info("查找父级WBS摘要码，parentNum：{}，parentId：{}", parentNum, parentId);

        // 1. 先从已处理的响应数据中查找
        MilepostDesignPlanDetailDto parentFromResponse = findParentFromImportData(currentDto, responseDataList);
        if (parentFromResponse != null && StringUtils.isNotBlank(parentFromResponse.getWbsSummaryCode())) {
            logger.info("从响应数据中找到父级，wbsSummaryCode：{}", parentFromResponse.getWbsSummaryCode());
            return parentFromResponse.getWbsSummaryCode();
        }

        // 2. 从所有导入数据中查找（按parentNum查找）
        if (StringUtils.isNotBlank(parentNum) && parentNum.contains(".")) {
            String parentLevelNum = getParentLevelNum(parentNum);
            MilepostDesignPlanDetailDto parentFromImport = allImportData.stream()
                    .filter(dto -> parentLevelNum.equals(dto.getParentNum()))
                    .findFirst()
                    .orElse(null);

            if (parentFromImport != null && StringUtils.isNotBlank(parentFromImport.getWbsSummaryCode())) {
                logger.info("从导入数据中找到父级，wbsSummaryCode：{}", parentFromImport.getWbsSummaryCode());
                return parentFromImport.getWbsSummaryCode();
            }
        }

        // 3. 最后尝试从数据库查找（仅当parentId是真实ID时）
        if (parentId != null) {
            try {
                MilepostDesignPlanDetail parentDetail = milepostDesignPlanDetailMapper.selectByPrimaryKey(parentId);
                if (parentDetail != null && StringUtils.isNotBlank(parentDetail.getWbsSummaryCode())) {
                    logger.info("从数据库中找到父级，wbsSummaryCode：{}", parentDetail.getWbsSummaryCode());
                    return parentDetail.getWbsSummaryCode();
                }
            } catch (Exception e) {
                logger.warn("从数据库查找父级失败，parentId：{}，错误：{}", parentId, e.getMessage());
            }
        }

        logger.warn("未找到父级WBS摘要码，parentNum：{}，parentId：{}", parentNum, parentId);
        return null;
    }

   /* // 处理更新详设批量导入
    private void processUpdateBatchImport(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos,
                                          Long storageId, Long unitId, List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo,
                                          Long markId, Boolean wbsEnabled, Long projectId, Boolean isChange,
                                          List<MilepostDesignPlanDetailDto> responseDataList) {

        for (MilepostDesignPlanDetailDto kukaDetailExcelVo : kukaDetailExcelVos) {
            //操作类型 新增,修改,删除
            String batchImportOperationType = kukaDetailExcelVo.getBatchImportOperationType();
//            一、wbsLastLayer 最后一层，
//            1、可以导入，
//            2、不可以删除
//            3、editable ===true  && 子级没有moduleStatus !== 2的数据可以 修改
//            二、materialCategory === '装配件' || materialCategory === '虚拟件'
//            1、editable ===true  导入 ，删除
//            2、editable ===false 导入
//            3、editable ===true  && 子级没有moduleStatus !== 2的数据可以 修改
//            三、materialCategory ===‘外购物料’ || materialCategory ===‘看板物料’
//            1、editable ===true  && moduleStatus !== 2  可以 删除
//            2、editable ===true  可以修改


        }
    }*/

    private void processUpdateBatchImport(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos,
                                          Long storageId, Long unitId, List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo,
                                          Long markId, Boolean wbsEnabled, Long projectId, Boolean isChange,
                                          List<MilepostDesignPlanDetailDto> responseDataList) {


        for (MilepostDesignPlanDetailDto kukaDetailExcelVo : kukaDetailExcelVos) {
            // 获取操作类型：新增、修改、删除
            String batchImportOperationType = kukaDetailExcelVo.getBatchImportOperationType();
            String wbsPath = kukaDetailExcelVo.getWbsPath();
            String parentNum = kukaDetailExcelVo.getParentNum();

            // 检查是否为多层级导入且wbsPath为空的情况
            if (StringUtils.isNotBlank(parentNum) && StringUtils.isBlank(wbsPath)) {
                // 只有修改和删除操作不支持多层级导入，新增操作需要特殊处理
                if (!"新增".equalsIgnoreCase(batchImportOperationType)) {
                    kukaDetailExcelVo.setValidResult("多层级导入数据暂不支持更新操作");
                    responseDataList.add(kukaDetailExcelVo);
                    continue;
                }

                // 新增操作的多层级记录，父子关系已在processMultiLevelImport中建立
                // 直接进行物料校验和导入
                processMultiLevelItemImport(kukaDetailExcelVo, storageId, unitId,
                                          markId, wbsEnabled, projectId, isChange,
                                          responseDataList, designPlanDetailDtoForWebInfo, kukaDetailExcelVos);
                continue;
            }

            // 检查WBS路径是否为空（非多层级情况下必须有wbsPath）
            if (StringUtils.isBlank(wbsPath)) {
                addErrorResponse(kukaDetailExcelVo, responseDataList);
                continue;
            }

            boolean isSubMaterialFlag = wbsPath.contains("/");

            // 根据操作类型和WBS路径格式处理
            if ("新增".equalsIgnoreCase(batchImportOperationType)) {
                // 判断是否可以导入
                boolean canImport = false;

                if (isSubMaterialFlag) {
                    // 处理包含PAM码的WBS路径
                    String[] wbsPathArray = wbsPath.split("/");
                    String wbsSummaryCode = wbsPathArray[0]; // 获取WBS编码部分
                    wbsSummaryCode = wbsSummaryCode.trim(); // 去除两端空格
                    // 获取父级PAM码（如果有的话）
                    String parentPamCode = null;
                    if (wbsPathArray.length > 1) {
                        parentPamCode = wbsPathArray[1].trim(); // 第二部分可能是父级PAM码
                    }

                    canImport = canImportDetail(kukaDetailExcelVo, wbsSummaryCode, projectId, parentPamCode);
                } else {
                    // 处理简单WBS路径
                    canImport = canImportDetail(kukaDetailExcelVo, wbsPath, projectId, null);
                }

                if (!canImport) {
                    // 不符合导入条件，返回错误
                    kukaDetailExcelVo.setValidResult("该记录不符合导入条件");
                    responseDataList.add(kukaDetailExcelVo);
                    continue;
                }

                // 符合导入条件，处理导入
                if (isSubMaterialFlag) {
                    // 处理包含子材料的WBS路径
                    String[] wbsPathArray = wbsPath.split("/");
                    String wbsSummaryCode = wbsPathArray[0];
                    String parentPamCode = wbsPathArray.length > 1 ? wbsPathArray[1].trim() : null;

                    // 获取父级详设记录
                    List<MilepostDesignPlanDetail> details = getDesignPlanDetailListByWbsSummaryAndPamCode(
                            wbsSummaryCode, parentPamCode, projectId);

                    if (CollectionUtils.isNotEmpty(details)) {
                        // 使用新方法处理变更时的新建
                        processChangeAddValidationAndImport(kukaDetailExcelVo, details.get(0), projectId,
                                storageId, unitId, markId, wbsSummaryCode, wbsEnabled, isChange, responseDataList);
                    } else {
                        addErrorResponse(kukaDetailExcelVo, responseDataList);
                    }
                } else {
                    // 处理简单WBS路径
                    List<MilepostDesignPlanDetail> details = getDesignPlanDetailListByWbsSummaryAndProjectId(wbsPath, projectId);

                    if (CollectionUtils.isNotEmpty(details)) {
                        // 使用新方法处理变更时的新建
                        processChangeAddValidationAndImport(kukaDetailExcelVo, details.get(0), projectId,
                                storageId, unitId, markId, details.get(0).getWbsSummaryCode(),
                                wbsEnabled, isChange, responseDataList);
                    } else {
                        addErrorResponse(kukaDetailExcelVo, responseDataList);
                    }
                }
            } else if ("修改".equalsIgnoreCase(batchImportOperationType)) {
                // 修改操作
                processUpdateOperation(kukaDetailExcelVo, wbsPath, projectId, storageId, unitId,
                        markId, wbsEnabled, isChange, isSubMaterialFlag, responseDataList);
            } else if ("删除".equalsIgnoreCase(batchImportOperationType)) {
                // 删除操作
                processDeleteOperation(kukaDetailExcelVo, wbsPath, projectId, isSubMaterialFlag, responseDataList, unitId, storageId);
            } else {
                // 未知操作类型
                kukaDetailExcelVo.setValidResult("未知的操作类型");
                responseDataList.add(kukaDetailExcelVo);
            }
        }
    }

    // 处理修改操作
    private void processUpdateOperation(MilepostDesignPlanDetailDto kukaDetailExcelVo, String wbsPath, Long projectId,
                                        Long storageId, Long unitId, Long markId, Boolean wbsEnabled, Boolean isChange,
                                        boolean isSubMaterialFlag, List<MilepostDesignPlanDetailDto> responseDataList) {

        if (StringUtils.isBlank(kukaDetailExcelVo.getNumberStr())) {
            kukaDetailExcelVo.setValidResult("操作类型为修改时，数量必填");
            responseDataList.add(kukaDetailExcelVo);
            return;
        }

        // 获取目标详设记录
        MilepostDesignPlanDetail targetDetail = null;

        // 根据wbsPath找到父级记录，然后根据导入信息找到对应的子级记录
        targetDetail = findChildDetailByParentWbsPath(wbsPath, kukaDetailExcelVo, projectId, unitId, storageId);

        if (targetDetail == null) {
            addErrorResponse(kukaDetailExcelVo, responseDataList);
            return;
        }

        // 验证是否可以修改
        if (!canModifyDetail(targetDetail)) {
            kukaDetailExcelVo.setValidResult("该记录当前状态不允许修改");
            responseDataList.add(kukaDetailExcelVo);
            return;
        }

        // 检查number和chartVersion字段是否有变化
        boolean hasChanged = false;

        // 比较数量字段
        String importNumber = kukaDetailExcelVo.getNumberStr();
        BigDecimal dbNumber = targetDetail.getNumber();
        if (importNumber != null && dbNumber != null && importNumber.compareTo(String.valueOf(dbNumber)) != 0) {
            hasChanged = true;
        }

        // 只有当字段有变化时才返回结果给前端
        if (hasChanged) {
            // 将数据库记录转换为DTO并返回
            MilepostDesignPlanDetailDto dbRecordDto = BeanConverter.copy(targetDetail, MilepostDesignPlanDetailDto.class);

            // 保持原始导入数据的batchImportOperationType和parentWbsSummaryCode不变
            dbRecordDto.setBatchImportOperationType(kukaDetailExcelVo.getBatchImportOperationType());
            dbRecordDto.setParentWbsSummaryCode(kukaDetailExcelVo.getParentWbsSummaryCode());
            dbRecordDto.setNumber(new BigDecimal(importNumber));
            // 如果数量有修改，将旧的数量值设置到changeOldNumber字段
            if (importNumber != null && dbNumber != null && importNumber.compareTo(String.valueOf(dbNumber)) != 0) {
                dbRecordDto.setChangeOldNumber(dbNumber.intValue());
            }
            dbRecordDto.setEditable(Boolean.TRUE);
            responseDataList.add(dbRecordDto);
        } else {
            // 字段没有变化，不需要修改，不添加到响应列表中
            logger.info("导入的数据与数据库记录一致，无需修改。WBS路径: {}, PAM编码: {}", wbsPath, kukaDetailExcelVo.getPamCode());
        }
    }

    // 处理删除操作
    private void processDeleteOperation(MilepostDesignPlanDetailDto kukaDetailExcelVo, String wbsPath, Long projectId,
                                        boolean isSubMaterialFlag, List<MilepostDesignPlanDetailDto> responseDataList, Long unitId, Long storageId) {

        // 获取目标详设记录
        MilepostDesignPlanDetail targetDetail = null;

        // 根据wbsPath找到父级记录，然后根据导入信息找到对应的子级记录
        targetDetail = findChildDetailByParentWbsPath(wbsPath, kukaDetailExcelVo, projectId, unitId, storageId);

        if (targetDetail == null) {
            addErrorResponse(kukaDetailExcelVo, responseDataList);
            return;
        }

        // 验证是否可以删除
        if (!canDeleteDetail(targetDetail)) {
            kukaDetailExcelVo.setValidResult("该记录当前状态不允许删除");
            responseDataList.add(kukaDetailExcelVo);
            return;
        }

        // 执行删除操作
        try {
            // 将数据库记录转换为DTO并返回
            MilepostDesignPlanDetailDto dbRecordDto = BeanConverter.copy(targetDetail, MilepostDesignPlanDetailDto.class);

            // 保持原始导入数据的batchImportOperationType和parentWbsSummaryCode不变
            dbRecordDto.setBatchImportOperationType(kukaDetailExcelVo.getBatchImportOperationType());
            dbRecordDto.setParentWbsSummaryCode(kukaDetailExcelVo.getParentWbsSummaryCode());
            dbRecordDto.setEditable(Boolean.TRUE);
            responseDataList.add(dbRecordDto);
        } catch (Exception e) {
            logger.error("删除详设记录失败", e);
            kukaDetailExcelVo.setValidResult("删除失败：" + e.getMessage());
            responseDataList.add(kukaDetailExcelVo);
        }
    }

    /**
     * 判断详设记录是否可以修改
     *
     * @param detail 详设记录
     * @return 是否可以修改
     */
    private boolean canModifyDetail(MilepostDesignPlanDetail detail) {
        if (detail == null) {
            return false;
        }

        String materialCategory = detail.getMaterialCategory();
        Boolean wbsLastLayer = detail.getWbsLastLayer();

        // 首先判断记录是否可编辑
        boolean isEditable = isDetailEditable(detail);

        // 一、WBS最后一层
        if (Boolean.TRUE.equals(wbsLastLayer)) {
            // 可以修改的条件：editable === true 且子级没有moduleStatus !== 2的数据
            return isEditable && !hasUnfinishedChildren(detail.getId());
        }

        // 二、装配件或虚拟件
        if ("装配件".equals(materialCategory) || "虚拟件".equals(materialCategory)) {
            // 可以修改的条件：editable === true 且子级没有moduleStatus !== 2的数据
            return isEditable && !hasUnfinishedChildren(detail.getId());
        }

        // 三、外购物料或看板物料
        if ("外购物料".equals(materialCategory) || "看板物料".equals(materialCategory)) {
            // 可以修改的条件：editable === true
            return isEditable;
        }

        return false;
    }

    // 判断状态是否允许编辑
    private boolean isStatusAllowEdit(Integer status) {
        // 根据业务规则判断状态是否允许编辑
        // 例如：草稿状态、已退回状态等允许编辑，审批中、已审批等状态不允许编辑
        if (status == null) {
            return true; // 默认允许编辑
        }

        // 这里需要根据实际业务规则定义哪些状态允许编辑
        // 假设状态码：0-草稿，1-审批中，2-已审批，3-已退回
        return status == 0 || status == 3;
    }

    /**
     * 判断详设记录是否可以删除
     *
     * @param detail 详设记录
     * @return 是否可以删除
     */
    private boolean canDeleteDetail(MilepostDesignPlanDetail detail) {
        if (detail == null) {
            return false;
        }

        String materialCategory = detail.getMaterialCategory();
        Boolean wbsLastLayer = detail.getWbsLastLayer();
        Integer moduleStatus = detail.getModuleStatus();

        // 首先判断记录是否可编辑
        boolean isEditable = isDetailEditable(detail);

        // 一、WBS最后一层不可删除
        if (Boolean.TRUE.equals(wbsLastLayer)) {
            return false;
        }

        // 二、装配件或虚拟件
        if ("装配件".equals(materialCategory) || "虚拟件".equals(materialCategory)) {
            // 可以删除的条件：editable === true
            return isEditable;
        }

        // 三、外购物料或看板物料
        if ("外购物料".equals(materialCategory) || "看板物料".equals(materialCategory)) {
            // 可以删除的条件：editable === true 且 moduleStatus !== 2
            return isEditable && (moduleStatus == null || moduleStatus != 2);
        }

        return false;
    }

    /**
     * 检查是否有未完成的子级（包括所有层级的子记录）
     * 未完成的子级定义为：moduleStatus = 2 的子级记录
     *
     * @param parentId 父级ID
     * @return 是否有未完成的子级
     */
    private boolean hasUnfinishedChildren(Long parentId) {
        if (parentId == null) {
            return false;
        }

        // 查询直接子级中moduleStatus = 2的记录
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andParentIdEqualTo(parentId)
                .andModuleStatusEqualTo(2);

        long count = milepostDesignPlanDetailMapper.countByExample(example);
        if (count > 0) {
            return true; // 如果直接子级中有moduleStatus = 2的记录，返回true
        }

        // 查询所有直接子级
        example.clear();
        example.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andParentIdEqualTo(parentId);

        List<MilepostDesignPlanDetail> children = milepostDesignPlanDetailMapper.selectByExample(example);

        // 递归检查每个子级的子级
        for (MilepostDesignPlanDetail child : children) {
            if (hasUnfinishedChildren(child.getId())) {
                return true; // 如果任何子级的子级中有moduleStatus = 2的记录，返回true
            }
        }

        return false; // 所有层级的子记录都没有moduleStatus = 2的记录
    }


    // 处理包含子材料的WBS路径的方法
    private void processWbsPathWithSubMaterials(MilepostDesignPlanDetailDto kukaDetailExcelVo,
                                                String wbsPath, Long projectId, Long storageId, Long unitId, Long markId,
                                                Boolean wbsEnabled, Boolean isChange, List<MilepostDesignPlanDetailDto> responseDataList,
                                                List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo) {

        String[] wbsPathArray = wbsPath.split("/");
        String wbsSummaryCode = wbsPathArray[0];

        for (int i = 1; i < wbsPathArray.length; i++) {
            String pamCode = wbsPathArray[i];
            if (StringUtils.isBlank(pamCode)) {
                continue;
            }
            //对pamCode内容中的空格或换行符删除
            pamCode = pamCode.replaceAll("\\s*", "");
            // 获取对应wbsSummaryCode和pamCode详设
            List<MilepostDesignPlanDetail> details = getDesignPlanDetailListByWbsSummaryAndPamCode(
                    wbsSummaryCode, pamCode, projectId);

            if (CollectionUtils.isEmpty(details)) {
                // 父级路径不存在，返回错误
                addErrorResponse(kukaDetailExcelVo, responseDataList);
                continue;
            }

            // 处理模块验证和导入
            processModuleValidationAndImport(kukaDetailExcelVo, details.get(0), projectId,
                    storageId, unitId, markId, wbsSummaryCode, wbsEnabled, isChange, responseDataList, designPlanDetailDtoForWebInfo);
        }
    }

    // 处理简单WBS路径的方法
    private void processSimpleWbsPath(MilepostDesignPlanDetailDto kukaDetailExcelVo,
                                      String wbsPath, Long projectId, Long storageId, Long unitId, Long markId,
                                      Boolean wbsEnabled, Boolean isChange, List<MilepostDesignPlanDetailDto> responseDataList,
                                      List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo) {

        List<MilepostDesignPlanDetail> milepostDesignPlanDetailList =
                getDesignPlanDetailListByWbsSummaryAndProjectId(wbsPath, projectId);

        if (ListUtils.isEmpty(milepostDesignPlanDetailList)) {
            addErrorResponse(kukaDetailExcelVo, responseDataList);
            return;
        }

        // 处理模块验证和导入
        processModuleValidationAndImport(kukaDetailExcelVo, milepostDesignPlanDetailList.get(0),
                projectId, storageId, unitId, markId,
                milepostDesignPlanDetailList.get(0).getWbsSummaryCode(),
                wbsEnabled, isChange, responseDataList, designPlanDetailDtoForWebInfo);
    }

    // 模块状态验证和导入处理的共同逻辑
    private void processModuleValidationAndImport(MilepostDesignPlanDetailDto kukaDetailExcelVo,
                                                  MilepostDesignPlanDetail parentDetail, Long projectId, Long storageId, Long unitId,
                                                  Long markId, String wbsSummaryCode, Boolean wbsEnabled, Boolean isChange,
                                                  List<MilepostDesignPlanDetailDto> responseDataList, List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo) {

        // 查询WBS收据信息
        ProjectWbsReceiptsDto projectWbsReceiptsDto = new ProjectWbsReceiptsDto();
        projectWbsReceiptsDto.setProjectId(projectId);
        projectWbsReceiptsDto.setPamCode(parentDetail.getPamCode());
        ProjectWbsReceiptsDto queryResultWbsReceiptsDto = projectWbsReceiptsExtMapper.selectDto(projectWbsReceiptsDto);

        Integer moduleStatus = parentDetail.getModuleStatus();
        String materialCategory = parentDetail.getMaterialCategory();
        Boolean wbsLastLayer = parentDetail.getWbsLastLayer();

        boolean canImport = (moduleStatus != null && moduleStatus == 0) &&
                ((Objects.isNull(queryResultWbsReceiptsDto) || StringUtils.isBlank(queryResultWbsReceiptsDto.getRequirementCode())) &&
                        (Objects.equals("装配件", materialCategory) || Objects.equals("虚拟件", materialCategory)) ||
                        Objects.equals(wbsLastLayer, Boolean.TRUE));

        if (!canImport) {
            // 模组不符合导入条件，返回错误
            addErrorResponse(kukaDetailExcelVo, responseDataList);
            return;
        }

        // 验证并导入
        DataResponse<List<MilepostDesignPlanDetailDto>> listDataResponse = validImportDesignPlanDetailMaterial(
                Lists.newArrayList(kukaDetailExcelVo),
                storageId,
                unitId,
                designPlanDetailDtoForWebInfo,
                markId,
                wbsSummaryCode,
                wbsEnabled,
                projectId,
                isChange,
                parentDetail.getParentId(),
                Boolean.TRUE  // 跳过多层级检查，因为已在批量导入方法中处理
        );

        // 设置父级关系并添加到响应列表
        List<MilepostDesignPlanDetailDto> data = listDataResponse.getData();
        for (MilepostDesignPlanDetailDto planDetailDto : data) {
            planDetailDto.setParentId(parentDetail.getId());
            planDetailDto.setParentWbsSummaryCode(parentDetail.getWbsSummaryCode());
        }
        responseDataList.addAll(data);
    }

    // 添加错误响应的辅助方法
    private void addErrorResponse(MilepostDesignPlanDetailDto dto,
                                  List<MilepostDesignPlanDetailDto> responseDataList) {
        if (StringUtils.isBlank(dto.getValidResult())) {
            dto.setValidResult("父级路径不存在或不允许导入");
        }
        responseDataList.add(dto);
    }

    private List<MilepostDesignPlanDetail> getDesignPlanDetailListByWbsSummaryAndPamCode(String wbsSummaryCode, String pamCode, Long projectId) {
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andWbsSummaryCodeEqualTo(wbsSummaryCode)
                .andPamCodeEqualTo(pamCode)
                .andProjectIdEqualTo(projectId);
        return milepostDesignPlanDetailMapper.selectByExample(example);
    }

    private List<MilepostDesignPlanDetail> getDesignPlanDetailListByWbsSummaryAndProjectId(String wbsSummaryCode, Long projectId) {
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andWbsSummaryCodeEqualTo(wbsSummaryCode)
                .andProjectIdEqualTo(projectId);
        return milepostDesignPlanDetailMapper.selectByExample(example);
    }

    /**
     * 根据父级WBS路径和导入信息查找对应的子级详设记录
     *
     * @param parentWbsPath 父级WBS路径，格式为"WBS编码/PAM码"
     * @param importDto     导入的详设信息
     * @param projectId     项目ID
     * @return 找到的子级详设记录，如果未找到则返回null
     */
    private MilepostDesignPlanDetail findChildDetailByParentWbsPath(String parentWbsPath,
                                                                    MilepostDesignPlanDetailDto importDto,
                                                                    Long projectId, Long unitId, Long storeId) {
        if (StringUtils.isBlank(parentWbsPath) || importDto == null || projectId == null) {
            return null;
        }

        // 解析父级WBS路径
        String[] pathParts = parentWbsPath.split("/");
        if (pathParts.length < 1) {
            return null;
        }

        String parentWbsCode = pathParts[0]; // 父级WBS编码
        String parentPamCode = pathParts.length > 1 ? pathParts[1] : null; // 父级PAM码（如果存在）
        parentWbsCode = parentWbsCode.trim();
        // 1. 首先查找父级记录
        MilepostDesignPlanDetail parentDetail = null;

        MilepostDesignPlanDetailExample parentExample = new MilepostDesignPlanDetailExample();
        MilepostDesignPlanDetailExample.Criteria parentCriteria = parentExample.createCriteria()
                .andProjectIdEqualTo(projectId)
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andWbsSummaryCodeEqualTo(parentWbsCode)
                .andWhetherModelEqualTo(Boolean.TRUE);


        // 如果有父级PAM码，进一步筛选
        if (StringUtils.isNotBlank(parentPamCode)) {
            parentCriteria.andPamCodeEqualTo(parentPamCode);
        }else{
            //如果父级路径没有P码，则查找最后一层的WBS编码
            parentCriteria.andWbsLastLayerEqualTo(Boolean.TRUE);
        }

        List<MilepostDesignPlanDetail> parentDetails = milepostDesignPlanDetailMapper.selectByExample(parentExample);

        if (CollectionUtils.isEmpty(parentDetails)) {
                return null; // 找不到父级记录
        }

//        if (CollectionUtils.isEmpty(parentDetails)) {
//            // 如果找不到精确匹配的父级记录，尝试只用WBS编码查找
//            if (StringUtils.isNotBlank(parentPamCode)) {
//                parentExample.clear();
//                parentExample.createCriteria()
//                        .andProjectIdEqualTo(projectId)
//                        .andDeletedFlagEqualTo(Boolean.FALSE)
//                        .andWbsSummaryCodeEqualTo(parentWbsCode);
//
//
//                parentDetails = milepostDesignPlanDetailMapper.selectByExample(parentExample);
//            }
//

//        }

        // 如果找到多个父级记录，选择最匹配的一个
        if (parentDetails.size() > 1 && StringUtils.isNotBlank(parentPamCode)) {
            for (MilepostDesignPlanDetail detail : parentDetails) {
                if (parentPamCode.equals(detail.getPamCode())) {
                    parentDetail = detail;
                    break;
                }
            }
            // 如果没找到精确匹配的，取第一个
            if (parentDetail == null) {
                parentDetail = parentDetails.get(0);
            }
        } else {
            parentDetail = parentDetails.get(0);
        }

        // 2. 根据父级ID查找子级记录
        MilepostDesignPlanDetailExample childExample = new MilepostDesignPlanDetailExample();
        MilepostDesignPlanDetailExample.Criteria childCriteria = childExample.createCriteria()
                .andProjectIdEqualTo(projectId)
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andParentIdEqualTo(parentDetail.getId());

        // 添加更多搜索条件：PAM物料编码、ERP物料编码
        if (StringUtils.isNotBlank(importDto.getPamCode())) {
            childCriteria.andPamCodeEqualTo(importDto.getPamCode());
        }

        if (StringUtils.isNotBlank(importDto.getErpCode())) {
            childCriteria.andErpCodeEqualTo(importDto.getErpCode());
        }

        // 添加物料类型搜索条件
        if (StringUtils.isNotBlank(importDto.getMaterialCategory())) {
            childCriteria.andMaterialCategoryEqualTo(importDto.getMaterialCategory());
        }

        // 添加物料中类搜索条件
        if (StringUtils.isNotBlank(importDto.getCodingMiddleClass())) {
            childCriteria.andCodingMiddleClassEqualTo(importDto.getCodingMiddleClass());
        }

        // 添加物料小类搜索条件
        if (StringUtils.isNotBlank(importDto.getMaterielType())) {
            childCriteria.andMaterielTypeEqualTo(importDto.getMaterielType());
        }

        // 添加品牌搜索条件
        if (StringUtils.isNotBlank(importDto.getBrand())) {
            childCriteria.andBrandEqualTo(importDto.getBrand());
        }

        // 添加名称搜索条件
        if (StringUtils.isNotBlank(importDto.getName())) {
            childCriteria.andNameEqualTo(importDto.getName());
        }

        // 添加索引号搜索条件
        if (StringUtils.isNotBlank(importDto.getIndexNum())) {
            childCriteria.andIndexNumEqualTo(importDto.getIndexNum());
        }

        // 添加型号/规格搜索条件
        if (StringUtils.isNotBlank(importDto.getModel())) {
            childCriteria.andModelEqualTo(importDto.getModel());
        }

        // 添加图号搜索条件
        if (StringUtils.isNotBlank(importDto.getFigureNumber())) {
            childCriteria.andFigureNumberEqualTo(importDto.getFigureNumber());
        }

        // 添加品牌商物料编码搜索条件
        if (StringUtils.isNotBlank(importDto.getBrandMaterialCode())) {
            childCriteria.andBrandMaterialCodeEqualTo(importDto.getBrandMaterialCode());
        }

        // 3. 根据导入信息中的关键字段进一步筛选子级记录
        List<MilepostDesignPlanDetail> childDetails = milepostDesignPlanDetailMapper.selectByExample(childExample);

        if (CollectionUtils.isEmpty(childDetails)) {
            importDto.setValidResult("根据所填字段，该路径下匹配不到或者匹配到多个物料，请检查数据");
            return null; // 没有找到子级记录
        }

        if (childDetails.size() > 1) {
            // 设置导入数据的验证结果，这样前端可以显示错误信息
            importDto.setValidResult("根据所填字段，该路径下匹配不到或者匹配到多个物料，请检查数据");

            // 返回null表示未找到匹配记录，调用方需要处理这种情况
            return null;
        } else {
            return childDetails.get(0);
        }
    }

    /**
     * 判断详设记录是否可编辑
     * 参照 MilepostDesignPlanServiceImpl#checkEditAble 方法
     *
     * @param detail 详设记录
     * @return 是否可编辑
     */
    private boolean isDetailEditable(MilepostDesignPlanDetail detail) {
        if (detail == null) {
            return false;
        }

        // 检查记录状态
        Integer status = detail.getStatus();

        // 如果状态为草稿、待审批或审批中，则不可编辑
        if (status != null && (
                CheckStatus.WBS_RECEIPTS_DRAFT.code().equals(status) ||
                        CheckStatus.WBS_RECEIPTS_PENDING.code().equals(status) ||
                        CheckStatus.WBS_RECEIPTS_CHECKING.code().equals(status))) {
            return false;
        }

        // 检查子级记录状态
        MilepostDesignPlanDetailExample childExample = new MilepostDesignPlanDetailExample();
        childExample.createCriteria()
                .andParentIdEqualTo(detail.getId())
                .andDeletedFlagEqualTo(Boolean.FALSE);

        List<MilepostDesignPlanDetail> childDetails = milepostDesignPlanDetailMapper.selectByExample(childExample);

        if (CollectionUtils.isNotEmpty(childDetails)) {
            // 检查子级是否有不可编辑的记录
            for (MilepostDesignPlanDetail childDetail : childDetails) {
                Integer childStatus = childDetail.getStatus();

                if (childStatus != null && (
                        CheckStatus.WBS_RECEIPTS_DRAFT.code().equals(childStatus) ||
                                CheckStatus.WBS_RECEIPTS_PENDING.code().equals(childStatus) ||
                                CheckStatus.WBS_RECEIPTS_CHECKING.code().equals(childStatus))) {
                    return false;
                }

                // 递归检查子级的子级
                if (!isDetailEditable(childDetail)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 判断详设记录是否可以导入
     *
     * @param detailDto      详设记录DTO
     * @param wbsSummaryCode WBS摘要码
     * @param projectId      项目ID
     * @param parentPamCode  父级PAM码（可选，用于在多个相同WBS摘要码的情况下精确定位）
     * @return 是否可以导入
     */
    private boolean canImportDetail(MilepostDesignPlanDetailDto detailDto, String wbsSummaryCode, Long projectId, String parentPamCode) {
        // 获取父级WBS记录
        List<MilepostDesignPlanDetail> parentDetails = getDesignPlanDetailListByWbsSummaryAndProjectId(wbsSummaryCode, projectId);

        if (ListUtils.isEmpty(parentDetails)) {
            return false;
        }

        // 如果找到多个父级记录，尝试根据PAM码精确匹配
        MilepostDesignPlanDetail parentDetail = null;
        if (parentDetails.size() > 1 && StringUtils.isNotBlank(parentPamCode)) {
            for (MilepostDesignPlanDetail detail : parentDetails) {
                if (parentPamCode.equals(detail.getPamCode())) {
                    parentDetail = detail;
                    break;
                }
            }
        }

        // 如果没有找到精确匹配的记录，使用第一个记录
        if (parentDetail == null) {
            parentDetail = parentDetails.get(0);
        }

        // 获取父级的模组状态
        Integer moduleStatus = parentDetail.getModuleStatus();

        // 获取父级的可编辑状态
        boolean isEditable = isDetailEditable(parentDetail);

        // 获取父级的WBS是否为最后一层标志
        Boolean wbsLastLayer = parentDetail.getWbsLastLayer();

        // 获取物料类别
        String materialCategory = detailDto.getMaterialCategory();

        // 查询WBS收据信息
        ProjectWbsReceiptsDto projectWbsReceiptsDto = new ProjectWbsReceiptsDto();
        projectWbsReceiptsDto.setProjectId(projectId);
        projectWbsReceiptsDto.setWbsSummaryCode(wbsSummaryCode);
        ProjectWbsReceiptsDto queryResultWbsReceiptsDto = projectWbsReceiptsExtMapper.selectDto(projectWbsReceiptsDto);

        // 一、WBS最后一层
        if (Boolean.TRUE.equals(wbsLastLayer)) {
            // 1、可以导入
            return true;
        }
        // 二、装配件或虚拟件
        else if ("装配件".equals(materialCategory) || "虚拟件".equals(materialCategory)) {
            // 1、editable ===true 可导入
            // 2、editable ===false 可导入
            return true;
        }
        // 三、外购物料或看板物料
        else if ("外购物料".equals(materialCategory) || "看板物料".equals(materialCategory)) {
            // 对于新增操作，外购物料或看板物料都可以导入
            return true;
        }

        return false;
    }

    /**
     * 处理变更时的新建操作
     *
     * @param kukaDetailExcelVo 导入的详设数据
     * @param parentDetail      父级详设记录
     * @param projectId         项目ID
     * @param storageId         库存组织ID
     * @param unitId            单位ID
     * @param markId            标记ID
     * @param wbsSummaryCode    WBS摘要码
     * @param wbsEnabled        WBS是否启用
     * @param isChange          是否变更
     * @param responseDataList  响应数据列表
     */
    private void processChangeAddValidationAndImport(MilepostDesignPlanDetailDto kukaDetailExcelVo,
                                                     MilepostDesignPlanDetail parentDetail, Long projectId, Long storageId, Long unitId,
                                                     Long markId, String wbsSummaryCode, Boolean wbsEnabled, Boolean isChange,
                                                     List<MilepostDesignPlanDetailDto> responseDataList) {
        // 查询WBS收据信息
        ProjectWbsReceiptsDto projectWbsReceiptsDto = new ProjectWbsReceiptsDto();
        projectWbsReceiptsDto.setProjectId(projectId);
        projectWbsReceiptsDto.setPamCode(parentDetail.getPamCode());
        ProjectWbsReceiptsDto queryResultWbsReceiptsDto = projectWbsReceiptsExtMapper.selectDto(projectWbsReceiptsDto);

        Integer moduleStatus = parentDetail.getModuleStatus();
        String materialCategory = kukaDetailExcelVo.getMaterialCategory(); // 使用导入数据的物料类别
        Boolean wbsLastLayer = parentDetail.getWbsLastLayer();
        boolean isEditable = isDetailEditable(parentDetail);

        // 检查是否符合导入规则
        boolean canImportByRule = false;

        // 一、WBS最后一层，可以导入
        if (Boolean.TRUE.equals(wbsLastLayer)) {
            canImportByRule = true;
        }
        // 二、装配件或虚拟件，无论editable是否为true都可以导入
        else if ("装配件".equals(materialCategory) || "虚拟件".equals(materialCategory)) {
            canImportByRule = true;
        }
        // 三、外购物料或看板物料，需要检查editable状态
        else if ("外购物料".equals(materialCategory) || "看板物料".equals(materialCategory)) {
            // 可以导入的条件：editable === true
            canImportByRule = isEditable;
        }

        if (!canImportByRule) {
            // 不符合导入规则，返回错误
            kukaDetailExcelVo.setValidResult("该记录不符合导入规则");
            responseDataList.add(kukaDetailExcelVo);
            return;
        }

        // 验证并导入
        DataResponse<List<MilepostDesignPlanDetailDto>> listDataResponse = validImportDesignPlanDetailMaterial(
                Lists.newArrayList(kukaDetailExcelVo),
                storageId,
                unitId,
                Collections.emptyList(), // 变更时的新建不需要检查重复
                markId,
                wbsSummaryCode,
                wbsEnabled,
                projectId,
                isChange,
                parentDetail.getId(),
                Boolean.TRUE  // 跳过多层级检查，因为已在批量导入方法中处理
        );

        // 设置父级关系并添加到响应列表
        List<MilepostDesignPlanDetailDto> data = listDataResponse.getData();
        for (MilepostDesignPlanDetailDto planDetailDto : data) {
            planDetailDto.setParentId(parentDetail.getId());
            planDetailDto.setParentWbsSummaryCode(parentDetail.getWbsSummaryCode());
            planDetailDto.setEditable(Boolean.TRUE);
        }

        responseDataList.addAll(data);
    }

    /**
     * 检查并处理重复的PAM编码和ERP编码
     *
     * @param dtoList          需要检查的DTO列表
     * @param responseDataList 响应数据列表
     * @param originalList     原始数据列表，用于移除已处理的记录
     * @return 是否存在重复编码
     */
    private boolean checkAndHandleDuplicateCodes(List<MilepostDesignPlanDetailDto> dtoList,
                                                 List<MilepostDesignPlanDetailDto> responseDataList,
                                                 List<MilepostDesignPlanDetailDto> originalList) {
        boolean hasDuplicates = false;

        // 检查PAM编码重复
        Map<String, List<MilepostDesignPlanDetailDto>> pamCodeMap = new HashMap<>();
        // 检查ERP编码重复
        Map<String, List<MilepostDesignPlanDetailDto>> erpCodeMap = new HashMap<>();

        for (MilepostDesignPlanDetailDto dto : dtoList) {
            // 收集PAM编码
            if (StringUtils.isNotEmpty(dto.getPamCode())) {
                pamCodeMap.computeIfAbsent(dto.getPamCode(), k -> new ArrayList<>()).add(dto);
            }
            // 收集ERP编码
            if (StringUtils.isNotEmpty(dto.getErpCode())) {
                erpCodeMap.computeIfAbsent(dto.getErpCode(), k -> new ArrayList<>()).add(dto);
            }
        }

        // 处理重复的PAM编码
        hasDuplicates |= handleDuplicateCodes(pamCodeMap, responseDataList, originalList, "PAM编码");

        // 处理重复的ERP编码
        hasDuplicates |= handleDuplicateCodes(erpCodeMap, responseDataList, originalList, "ERP编码");

        return hasDuplicates;
    }

    /**
     * 处理重复的编码
     *
     * @param codeMap          编码映射
     * @param responseDataList 响应数据列表
     * @param originalList     原始数据列表
     * @param codeType         编码类型描述
     */
    private boolean handleDuplicateCodes(Map<String, List<MilepostDesignPlanDetailDto>> codeMap,
                                         List<MilepostDesignPlanDetailDto> responseDataList,
                                         List<MilepostDesignPlanDetailDto> originalList,
                                         String codeType) {
        boolean hasDuplicates = false;

        for (Map.Entry<String, List<MilepostDesignPlanDetailDto>> entry : codeMap.entrySet()) {
            if (entry.getValue().size() > 1) {
                hasDuplicates = true;
                String code = entry.getKey();
                List<MilepostDesignPlanDetailDto> duplicateList = entry.getValue();

                // 第一条数据不添加重复提示，只添加到响应列表中
                responseDataList.add(duplicateList.get(0));

                // 从第二条开始添加重复提示
                for (int i = 1; i < duplicateList.size(); i++) {
                    MilepostDesignPlanDetailDto dto = duplicateList.get(i);
                    String errorMsg = String.format("导入数据中存在重复的%s:[%s]", codeType, code);
                    dto.setValidResult(StringUtils.isEmpty(dto.getValidResult()) ?
                            errorMsg : dto.getValidResult() + "，" + errorMsg);
                    responseDataList.add(dto);
                }

                // 将这些已处理的记录从原始列表中移除，避免重复处理
                originalList.removeAll(entry.getValue());
            }
        }

        return hasDuplicates;
    }

    /**
     * 处理重复的编码
     *
     * @param codeMap          编码映射
     * @param responseDataList 响应数据列表
     * @param codeType         编码类型描述
     * @return 是否存在重复编码
     */
    private boolean processDuplicateCodes(Map<String, List<MilepostDesignPlanDetailDto>> codeMap,
                                          List<MilepostDesignPlanDetailDto> responseDataList,
                                          String codeType) {
        boolean hasDuplicates = false;

        for (Map.Entry<String, List<MilepostDesignPlanDetailDto>> entry : codeMap.entrySet()) {
            if (entry.getValue().size() > 1) {
                hasDuplicates = true;
                String code = entry.getKey();
                List<MilepostDesignPlanDetailDto> duplicateList = entry.getValue();

                // 第一条数据不添加重复提示，只添加到响应列表中
                responseDataList.add(duplicateList.get(0));

                // 从第二条开始添加重复提示
                for (int i = 1; i < duplicateList.size(); i++) {
                    MilepostDesignPlanDetailDto dto = duplicateList.get(i);
                    String errorMsg = String.format("导入数据中存在重复的%s:[%s]", codeType, code);
                    dto.setValidResult(StringUtils.isEmpty(dto.getValidResult()) ?
                            errorMsg : dto.getValidResult() + "，" + errorMsg);
                    responseDataList.add(dto);
                }
            }
        }

        return hasDuplicates;
    }

    /**
     * 检查并处理导入数据中的重复编码和图号
     *
     * @param detailDtos         需要检查的详设计划明细列表
     * @param responseDataList   响应数据列表，用于添加错误信息
     * @param kukaDetailExcelVos 原始导入数据
     * @return 是否存在重复编码或图号
     */
    private boolean checkAndHandleDuplicateCodesAndFigureNumbers(List<MilepostDesignPlanDetailDto> detailDtos,
                                                                 List<MilepostDesignPlanDetailDto> responseDataList,
                                                                 List<MilepostDesignPlanDetailDto> kukaDetailExcelVos) {
//        // 校验是否存在重复的pamCode和erpCode
//        Map<String, List<MilepostDesignPlanDetailDto>> pamCodeMap = new HashMap<>();
//        Map<String, List<MilepostDesignPlanDetailDto>> erpCodeMap = new HashMap<>();
////        // 校验是否存在重复的图号
////        Map<String, List<MilepostDesignPlanDetailDto>> figureNumberMap = new HashMap<>();
//
//        for (MilepostDesignPlanDetailDto dto : detailDtos) {
//            // 收集PAM编码
//            if (StringUtils.isNotEmpty(dto.getPamCode())) {
//                pamCodeMap.computeIfAbsent(dto.getPamCode(), k -> new ArrayList<>()).add(dto);
//            }
//            // 收集ERP编码
//            if (StringUtils.isNotEmpty(dto.getErpCode())) {
//                erpCodeMap.computeIfAbsent(dto.getErpCode(), k -> new ArrayList<>()).add(dto);
//            }
////            // 收集图号(非空时)
////            if (StringUtils.isNotEmpty(dto.getFigureNumber())) {
////                figureNumberMap.computeIfAbsent(dto.getFigureNumber(), k -> new ArrayList<>()).add(dto);
////            }
//        }
//
//        // 处理重复的pamCode和erpCode
//        boolean hasDuplicates = false;
//
//        // 处理重复的PAM编码
//        hasDuplicates |= processDuplicateCodes(pamCodeMap, responseDataList, "PAM编码");
//
//        // 处理重复的ERP编码
//        hasDuplicates |= processDuplicateCodes(erpCodeMap, responseDataList, "ERP编码");
//
////        // 处理重复的图号
////        hasDuplicates |= processDuplicateCodes(figureNumberMap, responseDataList, "图号");

        return false;
    }

    // ==================== 多层级导入相关方法 ====================

    /**
     * 根据操作类型获取需要进行多层级校验的数据
     *
     * @param kukaDetailExcelVos 所有导入数据
     * @param operateType 操作类型（ADD/EDIT）
     * @return 需要进行多层级校验的数据列表
     */
    private List<MilepostDesignPlanDetailDto> getDataForMultiLevelValidation(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos, String operateType) {
        if (MilepostDesignPlanDetailKukaWbsBatchImportExcelOperationTypeEnum.ADD.getType().equalsIgnoreCase(operateType)) {
            // 新增场景：所有数据都需要校验
            logger.info("新增场景，所有数据都进行多层级校验，数据量：{}", kukaDetailExcelVos.size());
            return kukaDetailExcelVos;
        } else if (MilepostDesignPlanDetailKukaWbsBatchImportExcelOperationTypeEnum.EDIT.getType().equalsIgnoreCase(operateType)) {
            // 变更场景：只对batchImportOperationType为"新增"的记录进行多层级校验
            List<MilepostDesignPlanDetailDto> addOnlyData = kukaDetailExcelVos.stream()
                    .filter(dto -> "新增".equalsIgnoreCase(dto.getBatchImportOperationType()))
                    .collect(Collectors.toList());
            logger.info("变更场景，只对新增记录进行多层级校验，总数据量：{}，新增数据量：{}", kukaDetailExcelVos.size(), addOnlyData.size());
            return addOnlyData;
        } else {
            // 未知操作类型，返回空列表
            logger.warn("未知操作类型：{}，跳过多层级校验", operateType);
            return new ArrayList<>();
        }
    }

    /**
     * 检查是否为多层级导入场景
     *
     * @param kukaDetailExcelVos 导入数据列表
     * @return 是否为多层级导入
     */
    private boolean isMultiLevelImport(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos) {
        return kukaDetailExcelVos.stream()
                .anyMatch(dto -> StringUtils.isNotBlank(dto.getParentNum()));
    }

    /**
     * 处理多层级导入
     *
     * @param kukaDetailExcelVos 导入数据列表
     * @param projectId 项目ID
     * @return 处理结果，true表示成功
     */
    private boolean processMultiLevelImport(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos, Long projectId) {
        logger.info("开始处理多层级导入，数据量：{}", kukaDetailExcelVos.size());

        try {
            // 1. 执行多层级校验
            if (!validateMultiLevelHierarchy(kukaDetailExcelVos, projectId)) {
                logger.warn("多层级校验失败");
                return false;
            }

            // 2. 分配临时ID
            assignTempIds(kukaDetailExcelVos);

            // 3. 构建层级关系
            buildHierarchicalRelationships(kukaDetailExcelVos, projectId);

            // 4. 建立父子关系
            establishParentChildRelationships(kukaDetailExcelVos);

            // 5. 验证ID关系
            if (!validateIdRelationships(kukaDetailExcelVos)) {
                logger.warn("多层级ID关系验证失败");
                return false;
            }

            logger.info("多层级导入处理完成");
            return true;

        } catch (Exception e) {
            logger.error("多层级导入处理异常", e);
            return false;
        }
    }

    /**
     * 多层级导入数据校验
     *
     * @param kukaDetailExcelVos 导入数据列表
     * @param projectId 项目ID
     * @return 校验结果，true表示通过
     */
    private boolean validateMultiLevelHierarchy(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos, Long projectId) {
        logger.info("开始多层级导入数据校验，数据量：{}", kukaDetailExcelVos.size());

        boolean allValid = true;

        for (MilepostDesignPlanDetailDto dto : kukaDetailExcelVos) {
            if (StringUtils.isNotBlank(dto.getParentNum())) {
                // 1. 格式校验
                if (!validateParentNumFormat(dto)) {
                    allValid = false;
                    continue;
                }

                // 2. wbsPath与parentNum的校验（第一级详设wbsPath必须传值，非第一级详设wbsPath必须为空）
                if (!validateFirstLevelWbsPath(dto)) {
                    allValid = false;
                    continue;
                }

                // 3. 互斥校验（wbsPath有值时parentNum必须是第一层）
                if (!validateMutualExclusion(dto)) {
                    allValid = false;
                    continue;
                }

                // 4. 第一级详设wbsPath在数据库中的存在性校验
                if (!validateFirstLevelWbsPathExists(dto, projectId)) {
                    allValid = false;
                    continue;
                }

                // 5. 父层级类型校验
                if (!validateParentLevelType(kukaDetailExcelVos, dto)) {
                    allValid = false;
                    continue;
                }

                // 6. 父层级存在性校验
                if (!validateParentLevelExists(kukaDetailExcelVos, dto)) {
                    allValid = false;
                    continue;
                }
            }
        }

        logger.info("多层级导入数据校验完成，结果：{}", allValid ? "通过" : "失败");
        return allValid;
    }

    /**
     * 格式校验：必须符合正整数.正整数.正整数格式
     *
     * @param dto 待校验的数据
     * @return 校验结果
     */
    private boolean validateParentNumFormat(MilepostDesignPlanDetailDto dto) {
        String parentNum = dto.getParentNum();

        if (!PARENT_NUM_PATTERN.matcher(parentNum).matches()) {
            dto.setValidResult(appendValidResult(dto.getValidResult(), "层级说明格式不符"));
            logger.warn("层级说明格式不符：{}", parentNum);
            return false;
        }

        return true;
    }

    /**
     * wbsPath与parentNum的校验：
     * 1. 第一级详设（parentNum不包含"."）：wbsPath必须有值
     * 2. 非第一级详设（parentNum包含"."）：wbsPath必须为空（互斥）
     *
     * @param dto 待校验的数据
     * @return 校验结果
     */
    private boolean validateFirstLevelWbsPath(MilepostDesignPlanDetailDto dto) {
        String parentNum = dto.getParentNum();
        String wbsPath = dto.getWbsPath();

        if (isFirstLevel(parentNum)) {
            // 第一级详设：wbsPath必须有值
            if (StringUtils.isBlank(wbsPath)) {
                dto.setValidResult(appendValidResult(dto.getValidResult(), "第一级详设的WBS路径不能为空"));
                logger.warn("第一级详设wbsPath校验失败，parentNum：{}，wbsPath为空", parentNum);
                return false;
            }
        } else {
            // 非第一级详设：wbsPath必须为空（与parentNum互斥）
            if (StringUtils.isNotBlank(wbsPath)) {
                dto.setValidResult(appendValidResult(dto.getValidResult(), "父级路径有值时，层级说明必须为第一层"));
                logger.warn("非第一级详设wbsPath校验失败，parentNum：{}，wbsPath：{}", parentNum, wbsPath);
                return false;
            }
        }

        return true;
    }

    /**
     * 第一级详设wbsPath在数据库中的存在性校验
     * 验证第一级详设的wbsPath能否在数据库中找到对应的父级记录
     *
     * @param dto 待校验的数据
     * @param projectId 项目ID（从主方法传入）
     * @return 校验结果
     */
    private boolean validateFirstLevelWbsPathExists(MilepostDesignPlanDetailDto dto, Long projectId) {
        String parentNum = dto.getParentNum();
        String wbsPath = dto.getWbsPath();

        // 只对第一级详设进行数据库存在性校验
        if (isFirstLevel(parentNum) && StringUtils.isNotBlank(wbsPath)) {
            if (projectId == null) {
                dto.setValidResult(appendValidResult(dto.getValidResult(), "项目ID为空，无法进行wbsPath校验"));
                logger.warn("第一级详设wbsPath存在性校验失败：项目ID为空，parentNum：{}，wbsPath：{}", parentNum, wbsPath);
                return false;
            }

            // 查找数据库中是否存在对应的父级记录
            MilepostDesignPlanDetail parentDetail = findParentByWbsPath(wbsPath, projectId);
            if (parentDetail == null) {
                dto.setValidResult(appendValidResult(dto.getValidResult(), "第一级详设的WBS路径不存在对应的父级记录"));
                logger.warn("第一级详设wbsPath存在性校验失败，parentNum：{}，wbsPath：{}，projectId：{}", parentNum, wbsPath, projectId);
                return false;
            }

            logger.debug("第一级详设wbsPath存在性校验通过，parentNum：{}，wbsPath：{}，找到父级ID：{}", parentNum, wbsPath, parentDetail.getId());
        }

        return true;
    }

    /**
     * 互斥校验：如果父级路径（wbsPath）有值，则parentNum必须是第一层（不能包含"."）
     * 这个校验确保wbsPath和多层级parentNum不会同时存在，避免逻辑冲突
     *
     * @param dto 待校验的数据
     * @return 校验结果
     */
    private boolean validateMutualExclusion(MilepostDesignPlanDetailDto dto) {
        String wbsPath = dto.getWbsPath();
        String parentNum = dto.getParentNum();

        if (StringUtils.isNotBlank(wbsPath) && parentNum.contains(".")) {
            dto.setValidResult(appendValidResult(dto.getValidResult(), "父级路径有值时，层级说明必须为第一层"));
            logger.warn("父级路径与层级说明互斥校验失败，wbsPath：{}，parentNum：{}", wbsPath, parentNum);
            return false;
        }

        return true;
    }

    /**
     * 父层级类型校验：子层级的父层级必须是装配件或虚拟件类型
     *
     * @param allData 所有导入数据
     * @param dto 当前校验的数据
     * @return 校验结果
     */
    private boolean validateParentLevelType(List<MilepostDesignPlanDetailDto> allData, MilepostDesignPlanDetailDto dto) {
        String parentNum = dto.getParentNum();

        // 如果是第一层，无需校验父层级类型
        if (!parentNum.contains(".")) {
            return true;
        }

        // 获取父层级编号
        String parentLevelNum = getParentLevelNum(parentNum);

        // 在同批数据中查找父层级
        MilepostDesignPlanDetailDto parentDto = findParentInBatch(allData, parentLevelNum);

        if (parentDto != null) {
            String materialCategory = parentDto.getMaterialCategory();
            if (!"装配件".equals(materialCategory) && !"虚拟件".equals(materialCategory)) {
                String errorMsg = String.format("父层级【%s】需为装配件/虚拟件", parentLevelNum);
                dto.setValidResult(appendValidResult(dto.getValidResult(), errorMsg));
                logger.warn("父层级类型校验失败：{}", errorMsg);
                return false;
            }
        }

        return true;
    }

    /**
     * 父层级存在性校验：子层级的父层级必须在同批导入数据中存在
     *
     * @param allData 所有导入数据
     * @param dto 当前校验的数据
     * @return 校验结果
     */
    private boolean validateParentLevelExists(List<MilepostDesignPlanDetailDto> allData, MilepostDesignPlanDetailDto dto) {
        String parentNum = dto.getParentNum();

        // 如果是第一层，无需校验父层级存在性
        if (!parentNum.contains(".")) {
            return true;
        }

        // 获取父层级编号
        String parentLevelNum = getParentLevelNum(parentNum);

        // 在同批数据中查找父层级
        MilepostDesignPlanDetailDto parentDto = findParentInBatch(allData, parentLevelNum);

        if (parentDto == null) {
            String errorMsg = String.format("父层级【%s】不存在", parentLevelNum);
            dto.setValidResult(appendValidResult(dto.getValidResult(), errorMsg));
            logger.warn("父层级存在性校验失败：{}", errorMsg);
            return false;
        }

        return true;
    }

    /**
     * 为多层级导入数据分配UID
     *
     * @param kukaDetailExcelVos 导入数据列表
     */
    private void assignTempIds(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos) {
        logger.info("开始为多层级导入数据分配UID");

        // 为每个有parentNum但没有UID的记录分配UID
        for (MilepostDesignPlanDetailDto dto : kukaDetailExcelVos) {
            if (StringUtils.isNotBlank(dto.getParentNum()) && StringUtils.isBlank(dto.getUid())) {
                dto.setUid(generateRandomUid());
                logger.debug("为层级{}分配UID：{}", dto.getParentNum(), dto.getUid());
            }
        }

        logger.info("多层级导入数据UID分配完成");
    }

    /**
     * 构建多层级导入的层级关系
     *
     * @param kukaDetailExcelVos 导入数据列表
     * @param projectId 项目ID
     */
    private void buildHierarchicalRelationships(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos, Long projectId) {
        logger.info("开始构建多层级导入的层级关系，数据量：{}", kukaDetailExcelVos.size());

        // 1. 按层级深度排序（第一级优先）
        List<MilepostDesignPlanDetailDto> sortedData = sortByHierarchyLevel(kukaDetailExcelVos);

        // 2. 分两轮处理：第一轮处理第一级，第二轮处理后续层级
        // 第一轮：处理第一级，生成UID并查找数据库中的父级
        for (MilepostDesignPlanDetailDto dto : sortedData) {
            if (StringUtils.isNotBlank(dto.getParentNum()) && isFirstLevel(dto.getParentNum())) {
                generateFirstLevelUid(dto, projectId);
            }
        }

        // 第二轮：处理后续层级，使用同批数据中的UID建立关系
        for (MilepostDesignPlanDetailDto dto : sortedData) {
            if (StringUtils.isNotBlank(dto.getParentNum()) && !isFirstLevel(dto.getParentNum())) {
                setChildParentId(kukaDetailExcelVos, dto);
            }
        }

        logger.info("多层级导入的层级关系构建完成");
    }

    /**
     * 建立多层级导入数据的父子关系
     * 使用UID建立关系，而不是ID
     *
     * @param kukaDetailExcelVos 导入数据列表
     */
    private void establishParentChildRelationships(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos) {
        logger.info("开始建立多层级导入数据的父子关系");

        // 创建parentNum到DTO的映射
        Map<String, MilepostDesignPlanDetailDto> parentNumMap = new HashMap<>();
        for (MilepostDesignPlanDetailDto dto : kukaDetailExcelVos) {
            if (StringUtils.isNotBlank(dto.getParentNum())) {
                parentNumMap.put(dto.getParentNum(), dto);
            }
        }

        // 为每个子级设置parentId（使用父级的UID）
        for (MilepostDesignPlanDetailDto dto : kukaDetailExcelVos) {
            if (StringUtils.isNotBlank(dto.getParentNum()) && dto.getParentNum().contains(".")) {
                String parentLevelNum = getParentLevelNum(dto.getParentNum());
                MilepostDesignPlanDetailDto parentDto = parentNumMap.get(parentLevelNum);

                if (parentDto != null) {
                    // 使用父级的UID作为parentId
                    try {
                        Long parentUidAsId = Long.parseLong(parentDto.getUid());
                        dto.setParentId(parentUidAsId);
                        logger.debug("设置层级{}的父级UID：{} -> {}", dto.getParentNum(), parentLevelNum, parentDto.getUid());
                    } catch (NumberFormatException e) {
                        logger.warn("父级UID不是数字格式，无法设置parentId：{}", parentDto.getUid());
                    }
                } else {
                    logger.warn("未找到层级{}的父级：{}", dto.getParentNum(), parentLevelNum);
                }
            }
        }

        logger.info("多层级导入数据父子关系建立完成");
    }

    /**
     * 验证多层级数据的ID关系是否正确
     *
     * @param kukaDetailExcelVos 导入数据列表
     * @return 验证结果
     */
    private boolean validateIdRelationships(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos) {
        logger.info("开始验证多层级数据的ID关系");

        Map<String, MilepostDesignPlanDetailDto> parentNumMap = new HashMap<>();
        for (MilepostDesignPlanDetailDto dto : kukaDetailExcelVos) {
            if (StringUtils.isNotBlank(dto.getParentNum())) {
                parentNumMap.put(dto.getParentNum(), dto);
            }
        }

        boolean isValid = true;
        for (MilepostDesignPlanDetailDto dto : kukaDetailExcelVos) {
            if (StringUtils.isNotBlank(dto.getParentNum()) && dto.getParentNum().contains(".")) {
                String parentLevelNum = getParentLevelNum(dto.getParentNum());
                MilepostDesignPlanDetailDto parentDto = parentNumMap.get(parentLevelNum);

                if (parentDto == null) {
                    logger.error("层级{}的父级{}不存在", dto.getParentNum(), parentLevelNum);
                    isValid = false;
                } else {
                    // 验证父子关系：考虑新增记录ID可能为null的情况
                    if (!validateParentChildIdRelationship(parentDto, dto)) {
                        logger.error("层级{}的父级ID关系不正确，父级ID：{}，子级parentId：{}",
                                   dto.getParentNum(), parentDto.getId(), dto.getParentId());
                        isValid = false;
                    }
                }
            }
        }

        logger.info("多层级数据ID关系验证完成，结果：{}", isValid ? "通过" : "失败");
        return isValid;
    }

    /**
     * 验证父子级ID关系是否正确
     * 处理新增记录ID为null的情况
     *
     * @param parentDto 父级数据
     * @param childDto 子级数据
     * @return 验证结果
     */
    private boolean validateParentChildIdRelationship(MilepostDesignPlanDetailDto parentDto, MilepostDesignPlanDetailDto childDto) {
        Long parentId = parentDto.getId();
        Long childParentId = childDto.getParentId();
        String parentUid = parentDto.getUid();

        // 情况1：父级有数据库ID，子级的parentId应该等于父级的ID
        if (parentId != null) {
            return Objects.equals(parentId, childParentId);
        }

        // 情况2：父级没有数据库ID（新增记录），但有UID，子级的parentId应该等于父级的UID（转换为Long）
        if (StringUtils.isNotBlank(parentUid)) {
            try {
                Long parentUidAsLong = Long.parseLong(parentUid);
                return Objects.equals(parentUidAsLong, childParentId);
            } catch (NumberFormatException e) {
                logger.warn("父级UID不是数字格式，无法验证关系：{}", parentUid);
                return false;
            }
        }

        // 情况3：父级既没有ID也没有UID，关系无法验证
        logger.warn("父级既没有ID也没有UID，无法验证父子关系");
        return false;
    }

    /**
     * 按层级深度排序（第一级优先）
     *
     * @param data 原始数据
     * @return 排序后的数据
     */
    private List<MilepostDesignPlanDetailDto> sortByHierarchyLevel(List<MilepostDesignPlanDetailDto> data) {
        return data.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getParentNum()))
                .sorted(Comparator.comparingInt(this::getHierarchyDepth))
                .collect(Collectors.toList());
    }

    /**
     * 获取层级深度
     * 例如：1 -> 1, 1.1 -> 2, 1.1.1 -> 3
     *
     * @param dto 数据项
     * @return 层级深度
     */
    private int getHierarchyDepth(MilepostDesignPlanDetailDto dto) {
        String parentNum = dto.getParentNum();
        if (StringUtils.isBlank(parentNum)) {
            return 0;
        }
        return parentNum.split("\\.").length;
    }

    /**
     * 判断是否为第一层级
     *
     * @param parentNum 层级编号
     * @return 是否为第一层级
     */
    private boolean isFirstLevel(String parentNum) {
        return !parentNum.contains(".");
    }

    /**
     * 为第一级生成UID并设置wbsSummaryCode
     *
     * @param dto 第一级数据
     * @param projectId 项目ID
     */
    private void generateFirstLevelUid(MilepostDesignPlanDetailDto dto, Long projectId) {
        logger.info("为第一级生成UID，parentNum：{}", dto.getParentNum());

        // 1. 生成10位随机数作为uid
        String uid = generateRandomUid();
        dto.setUid(uid);

        // 2. 通过wbsPath查找父级，仅用于设置wbsSummaryCode
        if (StringUtils.isNotBlank(dto.getWbsPath())) {
            MilepostDesignPlanDetail parentDetail = findParentByWbsPath(dto.getWbsPath(), projectId);
            if (parentDetail != null) {
                // 只设置wbsSummaryCode，parentId由setParentIdForMultiLevelFirstLevel方法统一处理
                dto.setWbsSummaryCode(parentDetail.getWbsSummaryCode());
                logger.info("第一级找到父级，设置wbsSummaryCode：{}", parentDetail.getWbsSummaryCode());
            } else {
                logger.warn("第一级未找到父级，wbsPath：{}", dto.getWbsPath());
            }
        }
    }

    /**
     * 设置子级的parentId
     * 多层级导入中，parentId使用上级的UID，只有一级使用数据库中的真实ID
     *
     * @param allData 所有数据
     * @param child 子级数据
     */
    private void setChildParentId(List<MilepostDesignPlanDetailDto> allData, MilepostDesignPlanDetailDto child) {
        String parentNum = child.getParentNum();
        logger.info("设置子级parentId，childParentNum：{}", parentNum);

        // 获取父层级编号
        String parentLevelNum = getParentLevelNum(parentNum);

        if (StringUtils.isNotBlank(parentLevelNum)) {
            // 在同批数据中查找父级
            MilepostDesignPlanDetailDto parentDto = findParentInBatch(allData, parentLevelNum);

            if (parentDto != null) {
                // 判断父级是否为第一级
                if (isFirstLevel(parentDto.getParentNum())) {
                    // 第一级：如果有数据库ID则使用数据库ID，否则使用UID
                    if (parentDto.getId() != null) {
                        child.setParentId(parentDto.getId());
                        logger.info("子级找到第一级父级（数据库记录），parentLevelNum：{}，parentId：{}",
                                   parentLevelNum, parentDto.getId());
                    } else {
                        // 第一级还没有保存到数据库，使用UID作为临时标识
                        try {
                            Long parentUidAsId = Long.parseLong(parentDto.getUid());
                            child.setParentId(parentUidAsId);
                            logger.info("子级找到第一级父级（新增记录），parentLevelNum：{}，parentUID：{}",
                                       parentLevelNum, parentDto.getUid());
                        } catch (NumberFormatException e) {
                            logger.warn("父级UID不是数字格式，无法设置parentId：{}", parentDto.getUid());
                        }
                    }
                } else {
                    // 非第一级：直接使用父级的UID作为parentId
                    try {
                        Long parentUidAsId = Long.parseLong(parentDto.getUid());
                        child.setParentId(parentUidAsId);
                        logger.info("子级找到多层级父级，parentLevelNum：{}，parentUID：{}",
                                   parentLevelNum, parentDto.getUid());
                    } catch (NumberFormatException e) {
                        logger.warn("父级UID不是数字格式，无法设置parentId：{}", parentDto.getUid());
                    }
                }

                // 设置子级的wbsSummaryCode，继承父级的
                if (StringUtils.isNotBlank(parentDto.getWbsSummaryCode())) {
                    child.setWbsSummaryCode(parentDto.getWbsSummaryCode());
                    logger.info("子级继承父级wbsSummaryCode：{}", parentDto.getWbsSummaryCode());
                }
            } else {
                logger.warn("子级未找到父级，parentLevelNum：{}", parentLevelNum);
            }
        }
    }



    /**
     * 生成10位随机数UID
     *
     * @return 10位随机数字符串
     */
    private String generateRandomUid() {
        StringBuilder uid = new StringBuilder();
        SecureRandom secureRandom = new SecureRandom();
        for (int i = 0; i < 10; i++) {
            uid.append(secureRandom.nextInt(10));
        }
        return uid.toString();
    }



    /**
     * 通过WBS路径查找父级
     *
     * @param wbsPath WBS路径
     * @param projectId 项目ID
     * @return 父级详设记录
     */
    private MilepostDesignPlanDetail findParentByWbsPath(String wbsPath, Long projectId) {
        // 处理复合路径（如 "WBS001/PAM001"）
        if (wbsPath.contains("/")) {
            String[] pathArray = wbsPath.split("/");
            String wbsSummaryCode = pathArray[0].trim();
            String pamCode = pathArray.length > 1 ? pathArray[1].trim() : null;

            if (StringUtils.isNotBlank(pamCode)) {
                return findByWbsSummaryAndPamCode(wbsSummaryCode, pamCode, projectId);
            } else {
                return findByWbsSummaryAndProjectId(wbsSummaryCode, projectId);
            }
        } else {
            // 简单路径
            return findByWbsSummaryAndProjectId(wbsPath.trim(), projectId);
        }
    }

    /**
     * 根据WBS摘要码和PAM编码查找详设记录
     *
     * @param wbsSummaryCode WBS摘要码
     * @param pamCode PAM编码
     * @param projectId 项目ID
     * @return 详设记录
     */
    private MilepostDesignPlanDetail findByWbsSummaryAndPamCode(String wbsSummaryCode, String pamCode, Long projectId) {
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andWbsSummaryCodeEqualTo(wbsSummaryCode)
                .andPamCodeEqualTo(pamCode)
                .andProjectIdEqualTo(projectId);

        List<MilepostDesignPlanDetail> details = milepostDesignPlanDetailMapper.selectByExample(example);
        return CollectionUtils.isEmpty(details) ? null : details.get(0);
    }

    /**
     * 根据WBS摘要码和项目ID查找详设记录
     *
     * @param wbsSummaryCode WBS摘要码
     * @param projectId 项目ID
     * @return 详设记录
     */
    private MilepostDesignPlanDetail findByWbsSummaryAndProjectId(String wbsSummaryCode, Long projectId) {
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andWbsSummaryCodeEqualTo(wbsSummaryCode)
                .andProjectIdEqualTo(projectId);

        List<MilepostDesignPlanDetail> details = milepostDesignPlanDetailMapper.selectByExample(example);
        return CollectionUtils.isEmpty(details) ? null : details.get(0);
    }

    /**
     * 获取父层级编号
     * 例如：1.1.1 -> 1.1，1.2 -> 1
     *
     * @param parentNum 当前层级编号
     * @return 父层级编号
     */
    private String getParentLevelNum(String parentNum) {
        int lastDotIndex = parentNum.lastIndexOf(".");
        if (lastDotIndex > 0) {
            return parentNum.substring(0, lastDotIndex);
        }
        return null;
    }

    /**
     * 在同批数据中查找指定层级编号的数据
     *
     * @param allData 所有导入数据
     * @param targetParentNum 目标层级编号
     * @return 找到的数据，如果没找到返回null
     */
    private MilepostDesignPlanDetailDto findParentInBatch(List<MilepostDesignPlanDetailDto> allData, String targetParentNum) {
        if (StringUtils.isBlank(targetParentNum)) {
            return null;
        }

        return allData.stream()
                .filter(dto -> targetParentNum.equals(dto.getParentNum()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 为导入下级场景预处理数据，设置第一级详设的wbsPath字段
     *
     * @param kukaDetailExcelVos 导入的数据列表
     * @param wbsSummaryCode 从前端传入的WBS摘要码
     */
    private void preprocessDataForSubLevelImport(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos, String wbsSummaryCode) {
        // 如果wbsSummaryCode有值，说明是导入下级场景
        if (StringUtils.isNotBlank(wbsSummaryCode)) {
            logger.info("导入下级场景预处理，wbsSummaryCode：{}", wbsSummaryCode);

            for (MilepostDesignPlanDetailDto dto : kukaDetailExcelVos) {
                // 对于第一级详设且wbsPath为空的情况，设置wbsPath
                if (StringUtils.isNotBlank(dto.getParentNum()) &&
                    isFirstLevel(dto.getParentNum()) &&
                    StringUtils.isBlank(dto.getWbsPath())) {
                    dto.setWbsPath(wbsSummaryCode);
                    logger.debug("为第一级详设设置wbsPath：{}，parentNum：{}", wbsSummaryCode, dto.getParentNum());
                }
            }
        }
    }

    /**
     * 追加校验结果信息
     *
     * @param existingResult 已有的校验结果
     * @param newMessage 新的错误信息
     * @return 合并后的校验结果
     */
    private String appendValidResult(String existingResult, String newMessage) {
        if (StringUtils.isBlank(existingResult)) {
            return newMessage;
        }
        return existingResult + "，" + newMessage;
    }

    /**
     * 为多层级导入的第一级详设设置parentId
     * 多层级导入中，第一级详设的parentId应该设置为前端传入的detailId
     *
     * @param kukaDetailExcelVos 导入数据列表
     * @param detailId 前端传入的父级详设ID
     */
    private void setParentIdForMultiLevelFirstLevel(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos, Long detailId) {
        if (detailId == null) {
            logger.warn("detailId为空，无法为多层级第一级设置parentId");
            return;
        }

        logger.info("开始为多层级导入的第一级详设设置parentId：{}", detailId);

        for (MilepostDesignPlanDetailDto dto : kukaDetailExcelVos) {
            // 只处理第一级的多层级数据
            if (StringUtils.isNotBlank(dto.getParentNum()) && isFirstLevel(dto.getParentNum())) {
                dto.setParentId(detailId);
                logger.debug("为第一级详设设置parentId：{}，parentNum：{}", detailId, dto.getParentNum());
            }
        }

        logger.info("多层级第一级parentId设置完成");
    }

    /**
     * 为非多层级导入的详设设置parentId
     * 非多层级导入中，所有详设的parentId都应该设置为前端传入的detailId
     *
     * @param kukaDetailExcelVos 导入数据列表
     * @param detailId 前端传入的父级详设ID
     */
    private void setParentIdForNonMultiLevel(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos, Long detailId) {
        if (detailId == null) {
            logger.warn("detailId为空，无法为非多层级导入设置parentId");
            return;
        }

        logger.info("开始为非多层级导入的详设设置parentId：{}", detailId);

        for (MilepostDesignPlanDetailDto dto : kukaDetailExcelVos) {
            // 非多层级导入，为所有记录设置parentId
            dto.setParentId(detailId);
            logger.debug("为详设设置parentId：{}", detailId);
        }

        logger.info("非多层级导入parentId设置完成，处理记录数：{}", kukaDetailExcelVos.size());
    }

    /**
     * 为混合导入场景中的单层详设设置parentId
     * 在多层级导入场景中，可能还存在单层导入的详设（没有parentNum字段），
     * 这些详设也需要设置parentId为前端传入的detailId
     *
     * @param kukaDetailExcelVos 导入数据列表
     * @param detailId 前端传入的父级详设ID
     */
    private void setParentIdForSingleLevelInMixedImport(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos, Long detailId) {
        if (detailId == null) {
            logger.warn("detailId为空，无法为混合场景中的单层导入设置parentId");
            return;
        }

        logger.info("开始为混合场景中的单层导入详设设置parentId：{}", detailId);

        int singleLevelCount = 0;
        for (MilepostDesignPlanDetailDto dto : kukaDetailExcelVos) {
            // 只处理单层导入的数据（没有parentNum字段的数据）
            if (StringUtils.isBlank(dto.getParentNum())) {
                dto.setParentId(detailId);
                singleLevelCount++;
                logger.debug("为单层详设设置parentId：{}", detailId);
            }
        }

        logger.info("混合场景中单层导入parentId设置完成，处理记录数：{}", singleLevelCount);
    }

    /**
     * 为第一层记录设置UID
     * 包括单层导入记录和多层级导入的第一级记录
     *
     * @param finalResultList 最终返回的数据列表
     */
    private void setUidForFirstLevelRecords(List<MilepostDesignPlanDetailDto> finalResultList) {
        if (CollectionUtils.isEmpty(finalResultList)) {
            logger.info("数据列表为空，跳过第一层UID设置");
            return;
        }

        logger.info("开始为第一层记录设置UID，数据量：{}", finalResultList.size());

        int processedCount = 0;
        for (MilepostDesignPlanDetailDto dto : finalResultList) {
            // 判断是否为第一层记录
            if (isFirstLevelRecord(dto)) {
                // 如果没有UID，则生成一个
                if (StringUtils.isBlank(dto.getUid())) {
                    String uid = generateRandomUid();
                    dto.setUid(uid);
                    processedCount++;
                    logger.debug("为第一层记录设置UID：{}，parentNum：{}", uid, dto.getParentNum());
                } else {
                    logger.debug("第一层记录已有UID：{}，parentNum：{}", dto.getUid(), dto.getParentNum());
                }
            }
        }

        logger.info("第一层记录UID设置完成，新设置：{} 条记录", processedCount);
    }

    /**
     * 判断是否为第一层记录
     * 第一层记录包括：
     * 1. 单层导入记录（没有parentNum）
     * 2. 多层级导入的第一级记录（parentNum为第一级格式）
     *
     * @param dto 数据记录
     * @return 是否为第一层记录
     */
    private boolean isFirstLevelRecord(MilepostDesignPlanDetailDto dto) {
        String parentNum = dto.getParentNum();

        if (StringUtils.isBlank(parentNum)) {
            // 没有parentNum，认为是单层导入记录
            return true;
        } else {
            // 有parentNum，判断是否为第一级
            return isFirstLevel(parentNum);
        }
    }

    /**
     * 根据上下级关系为所有数据设置正确的父级物料描述
     * 独立方法，不依赖现有变量，处理所有可能的parentId类型
     *
     * @param finalResultList 最终返回的数据列表
     */
    private void setParentMaterielDescrForAllData(List<MilepostDesignPlanDetailDto> finalResultList) {
        if (CollectionUtils.isEmpty(finalResultList)) {
            logger.info("数据列表为空，跳过父级物料描述设置");
            return;
        }

        logger.info("开始为所有数据设置正确的父级物料描述，数据量：{}", finalResultList.size());

        // 1. 收集所有需要查询的parentId，分类处理
        Set<Long> databaseParentIds = new HashSet<>(); // 数据库ID类型
        Set<String> uidParentIds = new HashSet<>();     // UID类型（10位数字字符串）

        // 2. 建立UID到记录的映射（用于查找UID类型的父级）
        Map<String, MilepostDesignPlanDetailDto> uidToRecordMap = new HashMap<>();

        for (MilepostDesignPlanDetailDto dto : finalResultList) {
            // 建立UID映射
            if (StringUtils.isNotBlank(dto.getUid())) {
                uidToRecordMap.put(dto.getUid(), dto);
            }

            // 收集parentId（第一轮先收集所有parentId）
            Long parentId = dto.getParentId();
            if (parentId != null) {
                String parentIdStr = String.valueOf(parentId);
                // 先尝试作为数字解析，稍后根据UID映射重新分类
                try {
                    Long.parseLong(parentIdStr);
                    // 暂时都加入数据库ID集合，稍后重新分类
                    databaseParentIds.add(parentId);
                } catch (NumberFormatException e) {
                    // 非数字格式，直接认为是数据库ID
                    databaseParentIds.add(parentId);
                }
            }
        }

        // 3. 重新分类parentId：根据UID映射区分数据库ID和UID
        Set<Long> finalDatabaseParentIds = new HashSet<>();
        for (Long parentId : databaseParentIds) {
            String parentIdStr = String.valueOf(parentId);
            if (isUidFormat(parentIdStr, uidToRecordMap)) {
                // 在UID映射中存在，认为是UID
                uidParentIds.add(parentIdStr);
            } else {
                // 不在UID映射中，认为是数据库ID
                finalDatabaseParentIds.add(parentId);
            }
        }

        // 4. 建立parentId到物料描述的缓存
        Map<Long, String> parentMaterielDescrCache = new HashMap<>();

        // 5. 批量查询数据库ID类型的父级记录
        if (!finalDatabaseParentIds.isEmpty()) {
            logger.info("批量查询数据库父级记录，数量：{}", finalDatabaseParentIds.size());

            MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
            example.createCriteria()
                    .andIdIn(new ArrayList<>(finalDatabaseParentIds))
                    .andDeletedFlagEqualTo(Boolean.FALSE);

            List<MilepostDesignPlanDetail> parentRecords = milepostDesignPlanDetailMapper.selectByExample(example);

            for (MilepostDesignPlanDetail parentRecord : parentRecords) {
                parentMaterielDescrCache.put(parentRecord.getId(), parentRecord.getMaterielDescr());
            }

            logger.info("数据库父级记录查询完成，找到：{} 条记录", parentRecords.size());
        }

        // 6. 为每条记录设置正确的父级物料描述
        int processedCount = 0;
        for (MilepostDesignPlanDetailDto dto : finalResultList) {
            Long parentId = dto.getParentId();
            if (parentId == null) {
                continue;
            }

            String parentMaterielDescr = null;
            String parentIdStr = String.valueOf(parentId);

            if (isUidFormat(parentIdStr, uidToRecordMap)) {
                // UID类型：在同批数据中查找
                MilepostDesignPlanDetailDto parentRecord = uidToRecordMap.get(parentIdStr);
                if (parentRecord != null) {
                    parentMaterielDescr = parentRecord.getMaterielDescr();
                    logger.debug("找到UID父级记录，parentUID：{}，物料描述：{}", parentIdStr, parentMaterielDescr);
                } else {
                    logger.warn("未找到UID父级记录，parentUID：{}", parentIdStr);
                }
            } else {
                // 数据库ID类型：从缓存中获取
                parentMaterielDescr = parentMaterielDescrCache.get(parentId);
                if (parentMaterielDescr != null) {
                    logger.debug("找到数据库父级记录，parentId：{}，物料描述：{}", parentId, parentMaterielDescr);
                } else {
                    logger.warn("未找到数据库父级记录，parentId：{}", parentId);
                }
            }

            // 设置父级物料描述
            if (StringUtils.isNotBlank(parentMaterielDescr)) {
                dto.setParentMaterielDescr(parentMaterielDescr);
                processedCount++;
            }
        }

        logger.info("父级物料描述设置完成，成功处理：{} 条记录", processedCount);
    }

    /**
     * 判断字符串是否为UID格式（纯数字且在UID映射中存在）
     *
     * @param str 待判断的字符串
     * @param uidToRecordMap UID到记录的映射
     * @return 是否为UID格式
     */
    private boolean isUidFormat(String str, Map<String, MilepostDesignPlanDetailDto> uidToRecordMap) {
        if (StringUtils.isBlank(str)) {
            return false;
        }

        try {
            Long.parseLong(str);
            // 检查是否在UID映射中存在
            return uidToRecordMap.containsKey(str);
        } catch (NumberFormatException e) {
            return false;
        }
    }
}

