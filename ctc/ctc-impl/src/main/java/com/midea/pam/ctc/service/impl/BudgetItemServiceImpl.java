package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.BudgetItemBatchSaveDto;
import com.midea.pam.common.ctc.dto.BudgetItemDto;
import com.midea.pam.common.ctc.entity.BudgetItem;
import com.midea.pam.common.ctc.entity.BudgetItemExample;
import com.midea.pam.common.ctc.entity.BudgetItemSys;
import com.midea.pam.common.ctc.entity.BudgetItemSysExample;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.BudgetItemStatus;
import com.midea.pam.ctc.common.enums.BudgetItemType;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.BudgetItemExtMapper;
import com.midea.pam.ctc.mapper.BudgetItemMapper;
import com.midea.pam.ctc.mapper.BudgetItemSysMapper;
import com.midea.pam.ctc.service.BudgetItemService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * @program: pam
 * @description: 预算项目号 实现层
 * @author: fangyl
 * @create: 2019-4-13
 **/
public class BudgetItemServiceImpl implements BudgetItemService {
    private static Logger logger = LoggerFactory.getLogger(BudgetItemServiceImpl.class);

    @Resource
    private BudgetItemMapper budgetItemMapper;
    @Resource
    private BudgetItemExtMapper budgetItemExtMapper;
    @Resource
    private BudgetItemSysMapper budgetItemSysMapper;
    @Resource
    private EsbService esbService;
    @Resource
    private RestTemplate restTemplate;

    @Override
    public PageInfo<BudgetItemDto> page(Map<String, Object> param) {
        //分页参数
        Integer pageNum = Integer.parseInt(param.get(Constants.Page.PAGE_NUM).toString());
        Integer pageSize = Integer.parseInt(param.get(Constants.Page.PAGE_SIZE).toString());
        PageHelper.startPage(pageNum, pageSize);
        //按条件查询数据
//        BudgetItemExample example = new BudgetItemExample();
        BudgetItemSysExample example = new BudgetItemSysExample();
        BudgetItemSysExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(new Integer(0));
        if (param.get("id") != null) {
            criteria.andIdEqualTo(Long.parseLong(param.get("id").toString()));
        }
        if (param.get("budgetSystemCode") != null) {
            criteria.andBudgetSystemCodeLike("%" + param.get("budgetSystemCode").toString() + "%");
        }
        if (param.get("budgetItemCode") != null) {
            criteria.andBudgetItemCodeLike("%" + param.get("budgetItemCode").toString() + "%");
        }
        if (param.get("budgetItemName") != null) {
            criteria.andBudgetItemNameLike("%" + param.get("budgetItemName").toString() + "%");
        }
        if (param.get("budgetItemType") != null) {
            final List<String> budgetItemType = Arrays.stream(param.get("budgetItemType").toString().split(",")).
                    map(s -> s.trim()).
                    collect(Collectors.toList());
            criteria.andBudgetItemTypeIn(budgetItemType);
        }
        if (param.get("category") != null) {
            final List<String> category = Arrays.stream(param.get("category").toString().split(",")).
                    map(s -> s.trim()).
                    collect(Collectors.toList());
            criteria.andCategoryIn(category);
        }
        if (param.get("isLeaf") != null) {
            final List<String> isLeaf = Arrays.stream(param.get("isLeaf").toString().split(",")).
                    map(s -> s.trim()).
                    collect(Collectors.toList());
            criteria.andIsLeafIn(isLeaf);
        }
        if (param.get("status") != null) {
            final List<Integer> status = Arrays.stream(param.get("status").toString().split(",")).
                    map(s -> Integer.parseInt(s.trim())).
                    collect(Collectors.toList());
            criteria.andStatusIn(status);
        }
        if (param.get("businesstype") != null) {
            if (Integer.parseInt(param.get("businesstype").toString()) != 3) {
                criteria.andBusinessTypeEqualTo(Integer.parseInt(param.get("businesstype").toString()));
            } else {
                criteria.andBusinessTypeIsNull();
            }
        }
        List<BudgetItemSys> entityList = budgetItemSysMapper.selectByExample(example);
        PageInfo<BudgetItemDto> pageInfo = BeanConverter.convertPage(entityList, BudgetItemDto.class);
        //数据处理（Entity转换Dto）
        List<BudgetItemDto> dtoList = pageInfo.getList();
        for (BudgetItemDto entity : dtoList) {
            //创建人
            if (entity.getCreateBy() != null) {
                UserInfo userInfoCr = CacheDataUtils.findUserById(entity.getCreateBy());
                entity.setCreateByName(userInfoCr == null ? null : userInfoCr.getName());
            }
            //更新人
            if (entity.getUpdateBy() != null) {
                UserInfo userInfoUp = CacheDataUtils.findUserById(entity.getUpdateBy());
                entity.setUpdateByName(userInfoUp == null ? null : userInfoUp.getName());
            }
            //业务实体
            if (entity.getOuId() != null) {
                OperatingUnit ou = CacheDataUtils.findOuById(entity.getId());
                if (ou != null) {
                    entity.setOuName(ou.getOperatingUnitName());
                }
            }
        }
        return pageInfo;
    }

    @Override
    public String batchSave(BudgetItemBatchSaveDto budgetItemBatchSaveDto) {
        List<BudgetItemDto> budgetItemDtoList = budgetItemBatchSaveDto.getBudgetItemDtoList();
        for (BudgetItemDto budgetItemDto : budgetItemDtoList) {
            this.save(budgetItemDto);
        }
        //同步收款的拆款类型
        budgetItemExtMapper.updateBusinessType();
        return null;
    }

    @Override
    public String save(BudgetItemDto budgetItemDto) {
        BudgetItemExample budgetItemExample = new BudgetItemExample();
        budgetItemExample.createCriteria().andBudgetSystemCodeEqualTo(budgetItemDto.getBudgetSystemCode())
                .andBudgetItemCodeEqualTo(budgetItemDto.getBudgetItemCode()).andOuIdEqualTo(budgetItemDto.getOuId()).andDeletedFlagEqualTo(0);
        final List<BudgetItem> budgetItems = budgetItemMapper.selectByExample(budgetItemExample);
        if (ListUtils.isNotEmpty(budgetItems)) {
            //更新资金预算项目类型配置
            budgetItemDto.setId(budgetItems.get(0).getId());
            budgetItemDto.setCreateBy(SystemContext.getUserId());
            budgetItemDto.setUpdateBy(SystemContext.getUserId());
            budgetItemDto.setCreateAt(new Date());
            budgetItemDto.setUpdateAt(new Date());
            budgetItemMapper.updateByPrimaryKeySelective(budgetItemDto);
        } else {
            //新增资金预算项目类型配置
            budgetItemDto.setId(null);
            budgetItemDto.setDeletedFlag(new Integer(0));
            budgetItemDto.setCreateBy(SystemContext.getUserId());
            budgetItemMapper.insertSelective(budgetItemDto);
        }
        return null;
    }

    /**
     * 唯一条件查重：budgetSystemCode + budgetItemCode
     *
     * @param budgetItem
     * @return
     */
    private BudgetItem findByUniqueKey(BudgetItem budgetItem) {
        BudgetItemExample example = new BudgetItemExample();
        example.createCriteria().
                andBudgetSystemCodeEqualTo(budgetItem.getBudgetSystemCode()).
                andBudgetItemCodeEqualTo(budgetItem.getBudgetItemCode());
        List<BudgetItem> entityList = budgetItemMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(entityList)) {
            return entityList.get(0);
        }
        return null;
    }

    /**
     * 唯一条件查重：budgetSystemCode + budgetItemCode + ouid is not null
     *
     * @param budgetItem
     * @return
     */
    private BudgetItem findByUniqueKey1(BudgetItem budgetItem) {
        final List<OperatingUnit> operatingUnits = findOperatingUnit();
        int num = 0;
        if (ListUtils.isNotEmpty(operatingUnits)) {
            num = operatingUnits.size();
        }
        BudgetItemExample example = new BudgetItemExample();
        example.createCriteria().
                andBudgetSystemCodeEqualTo(budgetItem.getBudgetSystemCode()).
                andBudgetItemCodeEqualTo(budgetItem.getBudgetItemCode());
        List<BudgetItem> entityList = budgetItemMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(entityList) && entityList.size() < num) {
            return entityList.get(0);
        }
        return null;
    }

    private List<OperatingUnit> findOperatingUnit() {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", 1);
        param.put("pageSize", 10);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "operatingUnit/list", param);
        String res = restTemplate.getForObject(url, String.class);
        PageInfo<OperatingUnit> data = JSON.parseObject(res, new TypeReference<PageInfo<OperatingUnit>>() {
        });
        final List<OperatingUnit> list = data.getList();
        return list;
    }


    /**
     * 项目预算号资金数据同步
     *
     * @return
     */
    @Override
    public void syncFromGceb(List<BudgetItem> list, Integer offset) {
        Integer batchSize = 50;
        logger.info("-------callFDPBudgetItemInfoService--------:" + offset);
        try {
            List<BudgetItem> budgetItemList = esbService.callFDPBudgetItemInfoService(offset, batchSize);
            if (ListUtils.isNotEmpty(budgetItemList)) {
                list.addAll(budgetItemList);
                // 清空当前的 budgetItemList
                budgetItemList.clear();
                offset = offset + batchSize;
                this.syncFromGceb(list, offset);
            }
        } catch (Exception e) {
            logger.error("Error BudgetItemServiceImpl.syncFromGceb: ", e);
        }
    }

    @Override
    public void updateBudgetItem(List<BudgetItem> budgetItemList) {
        if (ListUtils.isEmpty(budgetItemList)) {
            return;
        }
        final List<OperatingUnit> operatingUnits = findOperatingUnit();
        List<Long> ouIds = null;
        if (ListUtils.isNotEmpty(operatingUnits)) {
            ouIds = operatingUnits.stream().map(OperatingUnit::getOperatingUnitId).collect(Collectors.toList());
        }
        for (BudgetItem budgetItem : budgetItemList) {
            // 先删除数据
            budgetItemExtMapper.deleteBudgetItemSys(budgetItem);
            //在插入数据
            BudgetItemSys budgetItemSys = new BudgetItemSys();
            BeanConverter.copy(budgetItem, budgetItemSys);
            budgetItemSysMapper.insert(budgetItemSys);

            BudgetItem existEntity = findByUniqueKey(budgetItem);
            if (!ObjectUtils.isEmpty(existEntity)) {
                budgetItemExtMapper.updateByBudgetItem(budgetItem);
            }
//            BudgetItem existEntity1 = findByUniqueKey1(budgetItem);
//            if (!ObjectUtils.isEmpty(existEntity1)){
//                budgetItem.setDeletedFlag(0);
//                budgetItem.setId(null);
//                budgetItemMapper.insert(budgetItem);
//            }
            BudgetItemExample example = new BudgetItemExample();
            BudgetItemExample.Criteria criteria = example.createCriteria().
                    andBudgetSystemCodeEqualTo(budgetItem.getBudgetSystemCode()).
                    andBudgetItemCodeEqualTo(budgetItem.getBudgetItemCode());
            if (!CollectionUtils.isEmpty(ouIds)) {
                criteria.andOuIdIn(ouIds);
            }
            List<BudgetItem> entityList = budgetItemMapper.selectByExample(example);
            // 还没设置ouid的数据
            if (ListUtils.isNotEmpty(entityList)) {
                List<Long> tempOuIds = new ArrayList<>();
                tempOuIds.addAll(ouIds);
                final List<Long> settedOuIds = entityList.stream().map(BudgetItem::getOuId).collect(Collectors.toList());
                // 余下还未进行设置的ouId
                tempOuIds.removeAll(settedOuIds);
                tempOuIds.forEach(ouid -> {
                    budgetItem.setDeletedFlag(0);
                    budgetItem.setId(null);
                    budgetItem.setOuId(ouid);
                    budgetItemMapper.insert(budgetItem);
                });
//                final List<BudgetItem> unSettingBudgetItems = entityList.stream().filter(e -> e.getOuId() == null).collect(Collectors.toList());
//                if (ListUtils.isNotEmpty(unSettingBudgetItems)){
//                    for (int i = 0; i < unSettingBudgetItems.size(); i++) {
//                        unSettingBudgetItems.get(i).setOuId(tempOuIds.get(i));
//                        budgetItemMapper.updateByPrimaryKey(unSettingBudgetItems.get(i));
//                    }
//
//                }
            }
        }
    }

    /**
     * 根据业务实体id查询预算项目号
     *
     * @return
     */
    @Override
    public List<BudgetItemDto> selectByOuId(Long ouId) {
        BudgetItemExample example = new BudgetItemExample();
        example.createCriteria().andOuIdEqualTo(ouId)
                .andBudgetItemTypeEqualTo(BudgetItemType.INCOME.getCode())
                .andStatusEqualTo(BudgetItemStatus.VALID.getCode())
                .andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
        List<BudgetItem> budgetItems = budgetItemMapper.selectByExample(example);
        return BeanConverter.copy(budgetItems, BudgetItemDto.class);
    }

}
