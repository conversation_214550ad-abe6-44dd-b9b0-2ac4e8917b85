package com.midea.pam.ctc.service.impl;

import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.SdpReceivePaymentInvoiceRequestISPResultDTO;
import com.midea.pam.common.ctc.entity.*;
import com.midea.pam.common.ctc.vo.PurchaseContractProgressVo;
import com.midea.pam.common.enums.*;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.sdp.dto.SdpReceiveGscPaymentInvoiceIspDetail;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.mapper.*;
import com.midea.pam.ctc.sdp.service.SdpLogService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.SdpReceivePaymentInvoiceIspRequestService;
import com.midea.pam.system.SystemContext;
import deps.org.apache.commons.lang.builder.EqualsBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidationException;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class SdpReceivePaymentInvoiceIspRequestServiceImpl implements SdpReceivePaymentInvoiceIspRequestService {

    private static final Logger logger = LoggerFactory.getLogger(SdpReceivePaymentInvoiceIspRequestServiceImpl.class);


    private static final BigDecimal TOTAL_INVOICE_AMOUNT_THRESHOLD = new BigDecimal(500000);

    @Resource
    private PurchaseContractService purchaseContractService;

    @Resource
    private SdpLogService sdpLogService;

    @Resource
    private PurchaseContractDetailMapper purchaseContractDetailMapper;

    @Resource
    private PurchaseContractPunishmentExtMapper punishmentExtMapper;

    @Resource
    private PaymentInvoiceDetailExtMapper paymentInvoiceDetailExtMapper;

    @Resource
    private GscPaymentInvoiceMapper gscPaymentInvoiceMapper;

    @Resource
    private GscPaymentInvoiceExtMapper gscPaymentInvoiceExtMapper;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private GscPaymentInvoiceIspDetailMapper gscPaymentInvoiceIspDetailMapper;

    @Resource
    private GscPaymentInvoiceIspDetailExtMapper ispDetailExtMapper;

    @Resource
    private BasedataExtService basedataExtService;

    @Resource
    private OrganizationCustomDictService organizationCustomDictService;

    /**
     * 记录业务校验失败日志
     *
     * @param errorMessage      原始错误消息
     * @param businessId        业务ID
     * @param failedParam       校验失败的参数名称
     * @param requestParamValue 请求参数值
     * @param systemParamValue  系统参数值
     */
    private void logBizValidationError(String errorMessage, String businessId,
                                       String failedParam, String requestParamValue, String systemParamValue) {
        logger.error("{}, businessId:{}, 校验失败参数:{}, 请求参数值:{}, 系统参数值:{}",
                errorMessage, businessId, failedParam, requestParamValue, systemParamValue);
    }

    /**
     * 处理ISP开票申请   需求文档链接：https://confluence.midea.com/pages/viewpage.action?pageId=271749784
     *
     * @param requestISP ISP开票申请信息
     * @param businessId 业务ID
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SdpReceivePaymentInvoiceRequestISPResultDTO handle(SdpReceiveGscPaymentInvoiceIspDetail requestISP, String businessId) {
        SdpReceivePaymentInvoiceRequestISPResultDTO resultDTO = new SdpReceivePaymentInvoiceRequestISPResultDTO();
        SdpLog sdpLog = buildSdpLog(businessId, requestISP);

        try {
            logger.info("SDP开票申请接收接口处理开始,businessId:{},参数:{}", businessId, JsonUtils.toString(requestISP));

            //  验证合同信息
            validateContractInfo(requestISP);

            //校验 申请头金额是否与行的累计金额一致
            validateInvoiceAmountConsistency(requestISP,businessId);

            //  获取单位ID
            Long ouId = requestISP.getOuId();
            Long topUnitId = getAndValidateTopUnitId(ouId, businessId);

            //  构建当前用户信息
            buildUserInfo(ouId);

            //  获取基本参数
            String purchaseCode = requestISP.getPurchaseCode();
            String invoiceNumber = requestISP.getInvoiceNumber();
            BigDecimal invoiceTotalAmountExcludeTax = requestISP.getInvoiceTotalAmountExcludeTax().setScale(2, RoundingMode.HALF_UP);
            BigDecimal taxRate = BigDecimal.ONE.add(requestISP.getTaxRate().movePointLeft(2));
            BigDecimal invoiceTotalAmount = requestISP.getInvoiceTotalAmount().setScale(2, RoundingMode.HALF_UP);
            String taxRateStr = requestISP.getTaxRate().setScale(0, RoundingMode.DOWN).toString();

            //  获取采购合同
            List<PurchaseContract> purchaseContractList = getPurchaseContractByCode(purchaseCode, ouId);
            validatePurchaseContract(purchaseContractList, purchaseCode, ouId, businessId);
            PurchaseContract purchaseContract = purchaseContractList.get(0);

            //  校验重复提交和作废情况
            boolean reRequest = false;
            boolean abandonFlow = false;
            Integer operationType = requestISP.getOperationType();
            GscPaymentInvoice gscPaymentInvoice = null;

            if (Objects.nonNull(operationType)) {
                gscPaymentInvoice = gscPaymentInvoiceExtMapper.getGscPaymentInvoiceByInvoiceNumber(invoiceNumber);
                if (Objects.nonNull(gscPaymentInvoice)) {
                    // 处理重新提交和作废情况
                    if (handleResubmitAndAbandon(operationType, gscPaymentInvoice, invoiceNumber, businessId)) {
                        reRequest = Objects.equals(operationType, GSCSubmitOperationTypeEnum.RESUBMIT.getCode());
                        abandonFlow = Objects.equals(operationType, GSCSubmitOperationTypeEnum.ABANDON.getCode());
                    }
                } else {
                    logBizValidationError("SDP开票申请接收接口处理-未查询到对应的发票信息", businessId,
                            "发票号", invoiceNumber, "无匹配记录");
                    throw new BizException(Code.ERROR, "PAM不存在该ISP发票申请编号");
                }
            }

            //  处理作废流程
            if (abandonFlow) {
                resultDTO.setUnitId(topUnitId);
                resultDTO.setIsSuccess(Boolean.TRUE);
                resultDTO.setAbandonFlow(abandonFlow);
                resultDTO.setIspInvoiceApplyId(gscPaymentInvoice.getId());
                sdpLog.setStatus(SdpLogStatusEnum.RECEIVE_HANDLE_SUCCESS.getCode());
                return resultDTO;
            }

            gscPaymentInvoice = gscPaymentInvoiceExtMapper.getGscPaymentInvoiceByInvoiceNumber(invoiceNumber);

            if(Objects.nonNull(gscPaymentInvoice)){
                logBizValidationError("SDP开票申请接收接口处理-ISP发票号已存在", businessId,
                        "发票号", invoiceNumber, "已存在记录");
                throw new BizException(Code.ERROR, "ISP发票号已存在，请检查修改后再提交");
            }

            //  校验供应商信息
            validateVendorInfo(purchaseContract, requestISP, businessId, purchaseCode, ouId);

            //  校验币种
            validateCurrency(purchaseContract, requestISP, businessId, purchaseCode, ouId);

            //  校验不含税金额、税额是否计算正确
            validateTaxCalculation(invoiceTotalAmountExcludeTax, invoiceTotalAmount, taxRate, businessId, purchaseCode, ouId);

            //  验证剩余可开票金额相关信息
            List<PurchaseContractDetail> purchaseContractDetailList = getPurchaseContractDetailList(purchaseContract.getId(), taxRateStr);
            validateContractDetail(purchaseContractDetailList, businessId, purchaseCode, ouId);
            PurchaseContractDetail purchaseContractDetail = purchaseContractDetailList.get(0);

            //  计算可开票金额
            BigDecimal remainingInvoicableAmount = calculateRemainingInvoicableAmount(
                    purchaseContract, purchaseContractDetail, taxRateStr, businessId, purchaseCode, ouId);

            //  验证申请金额是否超过剩余可开票金额
            validateInvoiceAmount(requestISP.getInvoiceTotalAmount(), remainingInvoicableAmount, businessId, purchaseCode, ouId);

            //  验证申请金额（不含税）是否超过PAM当前采购合同金额（不含税）
            validateInvoiceAmountExcludeTax(
                    purchaseContract, invoiceTotalAmountExcludeTax, businessId, purchaseCode, ouId);

            //  校验采购合同状态
            validateContractStatus(purchaseContract, businessId, purchaseCode, ouId);

            //  获取项目信息
            Project project = projectMapper.selectByPrimaryKey(purchaseContract.getProjectId());

            //  保存GSC开票申请
            gscPaymentInvoice = saveGscPaymentInvoice(requestISP, purchaseContract, project, gscPaymentInvoice, reRequest);

            //  保存ISP行信息
            saveIspDetailLines(requestISP, gscPaymentInvoice, reRequest, businessId, purchaseCode, ouId);

            //  设置返回结果
            resultDTO.setUnitId(topUnitId);
            resultDTO.setIsSuccess(Boolean.TRUE);
            resultDTO.setIspInvoiceApplyId(gscPaymentInvoice.getId());
            resultDTO.setInvoiceNumber(gscPaymentInvoice.getInvoiceNumber());

            //  设置项目经理MIP
            if (Objects.nonNull(purchaseContract.getManager())) {
                UserInfo managerUserInfo = CacheDataUtils.findUserById(purchaseContract.getManager());
                if (managerUserInfo != null) {
                    resultDTO.setManagerMip(managerUserInfo.getUsername());
                }
            }

            //设置采购跟进人
            if(Objects.nonNull(purchaseContract.getPurchasingFollower())){
                UserInfo purchasingFollowerUserInfo = CacheDataUtils.findUserById(purchaseContract.getPurchasingFollower());
                if (purchasingFollowerUserInfo != null) {
                    resultDTO.setPurchasingFollowerMip(purchasingFollowerUserInfo.getUsername());
                }
            }

            //设置项目财务
            if(Objects.nonNull(project.getFinancial())){
                UserInfo projectFinanceUserInfo = CacheDataUtils.findUserById(project.getFinancial());
                if (projectFinanceUserInfo != null) {
                    resultDTO.setProjectFinance(projectFinanceUserInfo.getUsername());
                }
            }

            // 查询开票总额，设置标识
            if(invoiceTotalAmount.compareTo(TOTAL_INVOICE_AMOUNT_THRESHOLD) > 0){
                resultDTO.setTotalInvoiceAmountFlag(2);
            }else{
                resultDTO.setTotalInvoiceAmountFlag(1);
            }

            sdpLog.setApplyId(gscPaymentInvoice.getId());
            sdpLog.setStatus(SdpLogStatusEnum.RECEIVE_HANDLE_SUCCESS.getCode());
        } catch (BizException | ValidationException e ) {
            resultDTO.setIsSuccess(Boolean.FALSE);
            resultDTO.setErrorMsg(e.getMessage());
            sdpLog.setStatus(SdpLogStatusEnum.RECEIVE_HANDLE_FAIL.getCode());
        } finally {
            sdpLogService.save(sdpLog);
        }
        return resultDTO;
    }

    /**
     * 获取并验证顶级单位ID
     *
     * @param ouId       业务实体ID
     * @param businessId 业务ID
     * @return 顶级单位ID
     */
    private Long getAndValidateTopUnitId(Long ouId, String businessId) {
        Long topUnitId = CacheDataUtils.getTopUnitIdByOuId(ouId);
        if (Objects.isNull(topUnitId)) {
            logBizValidationError("SDP开票申请接收接口处理失败-根据ouId获取单位ID失败", businessId,
                    "ouId", String.valueOf(ouId), "无匹配记录");
            throw new BizException(Code.ERROR, "ouId对应的单位不存在");
        }
        return topUnitId;
    }

    /**
     * 验证采购合同
     *
     * @param purchaseContractList 采购合同列表
     * @param purchaseCode         采购合同编号
     * @param ouId                 业务实体ID
     * @param businessId           业务ID
     */
    private void validatePurchaseContract(List<PurchaseContract> purchaseContractList, String purchaseCode, Long ouId, String businessId) {
        if (ListUtils.isEmpty(purchaseContractList)) {
            logBizValidationError("SDP开票申请接收接口处理-未查询到对应的采购合同信息", businessId,
                    "采购合同", "purchaseCode=" + purchaseCode + ", ouId=" + ouId, "无匹配记录");
            throw new BizException(Code.ERROR, "存在合同编号、业务实体ID、付款计划编号、供应商信息与PAM不一致、币种不一致等问题，请检查修改后再提交");
        }

        PurchaseContract purchaseContract = purchaseContractList.get(0);
        if (Objects.isNull(purchaseContract.getManager())) {
            logBizValidationError("SDP开票申请接收接口处理-未查询到对应的项目经理信息", businessId,
                    "项目经理", "purchaseCode=" + purchaseCode + ", ouId=" + ouId, "无匹配记录");
            throw new BizException(Code.ERROR, "查询采购合同信息异常");
        }
    }

    /**
     * 处理重新提交和作废情况
     *
     * @param auditStatus       审核状态
     * @param gscPaymentInvoice GSC支付发票
     * @param invoiceNumber     发票号
     * @param businessId        业务ID
     * @return 是否继续处理
     */
    private boolean handleResubmitAndAbandon(Integer auditStatus, GscPaymentInvoice gscPaymentInvoice,
                                             String invoiceNumber, String businessId) {
        //重新提交开票申请校验
        if (Objects.equals(auditStatus, AuditStatus.PENDING.getCode())) {
            if (Objects.equals(gscPaymentInvoice.getStatus(), AuditStatus.REFUSE.getCode())) {
                logger.info("SDP开票申请接收接口处理-已存在对应的发票信息,重新提交,invoiceNumber:{},gscPaymentInvoice:{}",
                        invoiceNumber, gscPaymentInvoice);
                return true;
            } else {
                logBizValidationError("SDP开票申请接收接口处理-已存在对应的发票信息,但状态不是拒绝，不可再发起", businessId,
                        "发票状态", "auditStatus=" + auditStatus, "status=" + gscPaymentInvoice.getStatus());
                throw new BizException(Code.ERROR, "PAM没有符合条件的ISP发票申请");
            }
        }

        //作废校验
        if (Objects.equals(auditStatus, AuditStatus.CANCEL.getCode())) {
            if (Objects.equals(gscPaymentInvoice.getStatus(), AuditStatus.REFUSE.getCode())) {
                return true;
            } else {
                logBizValidationError("SDP开票申请接收接口处理-已存在对应的发票信息,但状态不是拒绝，不可作废", businessId,
                        "发票状态", "auditStatus=" + auditStatus, "status=" + gscPaymentInvoice.getStatus());
                throw new BizException(Code.ERROR, "PAM没有符合条件的ISP发票申请");
            }
        }

        return false;
    }

    /**
     * 校验供应商信息
     *
     * @param purchaseContract 采购合同
     * @param requestISP       ISP请求
     * @param businessId       业务ID
     * @param purchaseCode     采购合同编号
     * @param ouId             业务实体ID
     */
    private void validateVendorInfo(PurchaseContract purchaseContract, SdpReceiveGscPaymentInvoiceIspDetail requestISP,
                                    String businessId, String purchaseCode, Long ouId) {
        Long erpVendorId = -1L;
        if(Objects.nonNull(purchaseContract.getVendorId())){
            VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(purchaseContract.getVendorId());
            if(Objects.nonNull(vendorSiteBankDto)){
                erpVendorId = vendorSiteBankDto.getErpVendorId();
            }
        }

        boolean vendorCheckResult = new EqualsBuilder()
                .append(erpVendorId, requestISP.getErpVendorId())
                .append(purchaseContract.getVendorCode(), requestISP.getVendorCode())
                .append(purchaseContract.getVendorName(), requestISP.getErpVendor())
                .append(purchaseContract.getErpVendorSiteId(), requestISP.getErpVendorSiteId().toString())
                .isEquals();

        if (!vendorCheckResult) {
            String requestVendorInfo = String.format("vendorId=%s, vendorCode=%s, vendorName=%s, erpVendorSiteId=%s",
                    requestISP.getErpVendorId(), requestISP.getVendorCode(), requestISP.getErpVendor(), requestISP.getErpVendorSiteId());

            String systemVendorInfo = String.format("erpVendorId=%s, vendorCode=%s, vendorName=%s, erpVendorSiteId=%s",
                    erpVendorId, purchaseContract.getVendorCode(),
                    purchaseContract.getVendorName(), purchaseContract.getErpVendorSiteId());

            logBizValidationError("SDP开票申请接收接口处理-供应商信息不一致", businessId,
                    "供应商信息", requestVendorInfo, systemVendorInfo);
            throw new BizException(Code.ERROR, "存在合同编号、业务实体ID、付款计划编号、供应商信息与PAM不一致、币种不一致等问题，请检查修改后再提交");
        }
    }

    /**
     * 校验币种
     *
     * @param purchaseContract 采购合同
     * @param requestISP       ISP请求
     * @param businessId       业务ID
     * @param purchaseCode     采购合同编号
     * @param ouId             业务实体ID
     */
    private void validateCurrency(PurchaseContract purchaseContract, SdpReceiveGscPaymentInvoiceIspDetail requestISP,
                                  String businessId, String purchaseCode, Long ouId) {
        if (!Objects.equals(purchaseContract.getCurrency(), requestISP.getCurrency())) {
            logBizValidationError("SDP开票申请接收接口处理-币种不一致", businessId,
                    "币种", requestISP.getCurrency(), purchaseContract.getCurrency());
            throw new BizException(Code.ERROR, "存在合同编号、业务实体ID、付款计划编号、供应商信息与PAM不一致、币种不一致等问题，请检查修改后再提交");
        }
    }

    /**
     * 校验不含税金额、税额是否计算正确
     *
     * @param invoiceTotalAmountExcludeTax 不含税总金额
     * @param invoiceTotalAmount           含税总金额
     * @param taxRate                      税率
     * @param businessId                   业务ID
     * @param purchaseCode                 采购合同编号
     * @param ouId                         业务实体ID
     */
    private void validateTaxCalculation(BigDecimal invoiceTotalAmountExcludeTax, BigDecimal invoiceTotalAmount,
                                        BigDecimal taxRate, String businessId, String purchaseCode, Long ouId) {
        BigDecimal calculatedAmountExcludeTax = invoiceTotalAmount.divide(taxRate, 2, RoundingMode.DOWN);
        if (invoiceTotalAmountExcludeTax.compareTo(calculatedAmountExcludeTax) != 0) {
            logBizValidationError("SDP开票申请接收接口处理-不含税总金额与税额计算错误", businessId,
                    "不含税金额计算", "invoiceTotalAmountExcludeTax=" + invoiceTotalAmountExcludeTax,
                    "calculatedAmountExcludeTax=" + calculatedAmountExcludeTax);
            throw new BizException(Code.ERROR, "不含税、税额计算错误（两位小数）");
        }
    }

    /**
     * 验证合同详情
     *
     * @param purchaseContractDetailList 采购合同详情列表
     * @param businessId                 业务ID
     * @param purchaseCode               采购合同编号
     * @param ouId                       业务实体ID
     */
    private void validateContractDetail(List<PurchaseContractDetail> purchaseContractDetailList,
                                        String businessId, String purchaseCode, Long ouId) {
        if (ListUtils.isEmpty(purchaseContractDetailList)) {
            logBizValidationError("SDP开票申请接收接口处理-未查询到对应的采购合同内容信息", businessId,
                    "采购合同内容", "purchaseCode=" + purchaseCode + ", ouId=" + ouId, "无匹配记录");
            throw new BizException(Code.ERROR, "当前税率发票金额大于剩余可开票金额");
        }
    }

    /**
     * 计算剩余可开票金额
     *
     * @param purchaseContract       采购合同
     * @param purchaseContractDetail 采购合同详情
     * @param taxRateStr             税率字符串
     * @param businessId             业务ID
     * @param purchaseCode           采购合同编号
     * @param ouId                   业务实体ID
     * @return 剩余可开票金额
     */
    private BigDecimal calculateRemainingInvoicableAmount(PurchaseContract purchaseContract,
                                                          PurchaseContractDetail purchaseContractDetail,
                                                          String taxRateStr, String businessId,
                                                          String purchaseCode, Long ouId) {
        // 查询采购合同罚扣
        List<PurchaseContractPunishment> punishmentList = punishmentExtMapper
                .getPunishmentListByPurchaseContractIdAndTaxRate(purchaseContract.getId(), taxRateStr);

        // 含税罚扣金额
        BigDecimal punishmentTotalIncludeAmount = punishmentList.stream()
                .map(p -> p.getAmount().add(p.getAmount().multiply(new BigDecimal(p.getTaxRate()))))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 采购合同指定税率明细含税金额
        BigDecimal purchaseContractTaxRateTotalPrice = purchaseContractDetail.getTotalPrice();

        // 统计合同下同一税率开票金额
        List<PaymentInvoiceDetail> invoiceDetailList = paymentInvoiceDetailExtMapper
                .getDetailListByContractIdAndTaxRate(purchaseContract.getId(), taxRateStr);

        // 含税税票金额
        BigDecimal invoiceDetailIncludePriceTotal = invoiceDetailList.stream()
                .map(PaymentInvoiceDetail::getTaxIncludedPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 剩余可开票金额（含税）= 合同金额 - 已开票金额 - 罚扣金额 - ISP发票金额
        BigDecimal remainingInvoicableAmount = purchaseContractTaxRateTotalPrice
                .subtract(invoiceDetailIncludePriceTotal)
                .subtract(punishmentTotalIncludeAmount);

        // 减去ISP发票金额
        BigDecimal ispInvoiceTotalAmount = getIspInvoiceTotalAmountByContractId(purchaseContract.getId());
        remainingInvoicableAmount = remainingInvoicableAmount.subtract(ispInvoiceTotalAmount);

        logger.info("SDP开票申请接收接口处理-计算剩余可开票金额: 合同金额={}, 已开票金额={}, 罚扣金额={}, ISP发票金额={}, 剩余可开票金额={}",
                purchaseContractTaxRateTotalPrice, invoiceDetailIncludePriceTotal,
                punishmentTotalIncludeAmount, ispInvoiceTotalAmount, remainingInvoicableAmount);

        return remainingInvoicableAmount;
    }

    /**
     * 验证发票金额是否超过剩余可开票金额
     *
     * @param invoiceTotalAmount        发票总金额
     * @param remainingInvoicableAmount 剩余可开票金额
     * @param businessId                业务ID
     * @param purchaseCode              采购合同编号
     * @param ouId                      业务实体ID
     */
    private void validateInvoiceAmount(BigDecimal invoiceTotalAmount, BigDecimal remainingInvoicableAmount,
                                       String businessId, String purchaseCode, Long ouId) {
        if (invoiceTotalAmount.compareTo(remainingInvoicableAmount) > 0) {
            logBizValidationError("SDP开票申请接收接口处理-发票总金额（含税）大于剩余可开票金额（含税）", businessId,
                    "发票总金额", "invoiceTotalAmount=" + invoiceTotalAmount,
                    "remainingInvoicableAmount=" + remainingInvoicableAmount);
            throw new BizException(Code.ERROR, "当前税率发票金额大于剩余可开票金额");
        }
    }

    /**
     * 校验开票申请合同进度控制
     * 根据组织参数"开票申请合同进度控制"决定是否启用校验
     * 当组织参数值=1时启用校验，其他值或没有配置时不启用
     *
     * @param ouId       业务实体ID
     * @param businessId 业务ID
     * @return true-启用校验，false-不启用校验
     */
    private boolean validateContractProgressControl(Long ouId, String businessId) {
        try {
            logger.info("SDP开票申请接收接口处理-开始校验开票申请合同进度控制, businessId:{}, ouId:{}", businessId, ouId);

            // 获取顶级单位ID用于查询组织参数
            Long topUnitId = CacheDataUtils.getTopUnitIdByOuId(ouId);
            if (Objects.isNull(topUnitId)) {
                logger.info("SDP开票申请接收接口处理-未获取到顶级单位ID，跳过开票申请合同进度控制校验, businessId:{}, ouId:{}", businessId, ouId);
                return false;
            }

            // 查询组织参数：开票申请合同进度控制
            Set<String> paramValueSet = organizationCustomDictService.queryByName("开票申请合同进度控制", topUnitId, OrgCustomDictOrgFrom.COMPANY);

            if (paramValueSet == null || paramValueSet.isEmpty()) {
                logger.info("SDP开票申请接收接口处理-未配置开票申请合同进度控制组织参数，跳过校验, businessId:{}, topUnitId:{}", businessId, topUnitId);
                return false;
            }

            String paramValue = paramValueSet.iterator().next();
            boolean isEnabled = "1".equals(paramValue);

            logger.info("SDP开票申请接收接口处理-开票申请合同进度控制组织参数值:{}, 校验状态:{}, businessId:{}, topUnitId:{}",
                    paramValue, isEnabled ? "启用" : "不启用", businessId, topUnitId);

            return isEnabled;

        } catch (Exception e) {
            logger.error("SDP开票申请接收接口处理-校验开票申请合同进度控制异常, businessId:{}, ouId:{}, 异常信息:{}",
                    businessId, ouId, e.getMessage(), e);
            // 异常情况下不阻断流程，返回false跳过校验
            return false;
        }
    }

    /**
     * 验证不含税发票金额是否超过PAM当前采购合同金额（不含税）
     *
     * @param purchaseContract             采购合同
     * @param invoiceTotalAmountExcludeTax 不含税发票总金额
     * @param businessId                   业务ID
     * @param purchaseCode                 采购合同编号
     * @param ouId                         业务实体ID
     */
    private void validateInvoiceAmountExcludeTax(PurchaseContract purchaseContract,
                                                 BigDecimal invoiceTotalAmountExcludeTax,
                                                 String businessId, String purchaseCode, Long ouId) {
        // 校验开票申请合同进度控制
        if (!validateContractProgressControl(ouId, businessId)) {
            logger.info("SDP开票申请接收接口处理-开票申请合同进度控制未启用，跳过不含税发票金额校验, businessId:{}, purchaseCode:{}, ouId:{}",
                    businessId, purchaseCode, ouId);
            return;
        }

        logger.info("SDP开票申请接收接口处理-开票申请合同进度控制已启用，执行不含税发票金额校验, businessId:{}, purchaseCode:{}, ouId:{}",
                businessId, purchaseCode, ouId);

        // 获取合同当前金额(已扣减有效罚扣金额)
        PurchaseContractProgressVo purchaseContractProgressVo = purchaseContractService
                .calculateExecuteAmountTotal(purchaseContract.getId());
        //获取累计进度执行金额（不含税）
        BigDecimal purchaseContractCurrAmount = purchaseContractProgressVo.getExecuteAmountTotalHeader();

        // 查询已开票金额（不含税）
        List<PaymentInvoiceDetail> invoiceDetailList = paymentInvoiceDetailExtMapper
                .getDetailListByContractIdAndTaxRate(purchaseContract.getId(), null);
        BigDecimal invoiceDetailExcludePriceTotal = invoiceDetailList.stream()
                .map(PaymentInvoiceDetail::getTaxExcludedPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 扣减已开票金额
        purchaseContractCurrAmount = purchaseContractCurrAmount.subtract(invoiceDetailExcludePriceTotal);

        // 扣减ISP已开票金额（不含税）
        BigDecimal ispInvoiceTotalAmountWithoutTax = getIspInvoiceTotalAmountWithoutTaxByContractId(purchaseContract.getId());
        purchaseContractCurrAmount = purchaseContractCurrAmount.subtract(ispInvoiceTotalAmountWithoutTax);

        logger.info("SDP开票申请接收接口处理-计算剩余可开票金额（不含税）: 累计进度执行金额（不含税）={}, 已开票金额={}, ISP发票金额={}, 剩余可开票金额={}",
                purchaseContractProgressVo.getExcludingTaxAmountHeader(), invoiceDetailExcludePriceTotal,
                ispInvoiceTotalAmountWithoutTax, purchaseContractCurrAmount);

        if (invoiceTotalAmountExcludeTax.compareTo(purchaseContractCurrAmount) > 0) {
            logBizValidationError("SDP开票申请接收接口处理-发票总金额（不含税）大于PAM当前采购合同金额", businessId,
                    "不含税发票金额", "invoiceTotalAmountExcludeTax=" + invoiceTotalAmountExcludeTax,
                    "purchaseContractCurrAmount=" + purchaseContractCurrAmount);
            throw new BizException(Code.ERROR, "超出可开票金额，请联系项目经理收货确认");
        }
    }

    /**
     * 验证合同状态
     *
     * @param purchaseContract 采购合同
     * @param businessId       业务ID
     * @param purchaseCode     采购合同编号
     * @param ouId             业务实体ID
     */
    private void validateContractStatus(PurchaseContract purchaseContract, String businessId,
                                        String purchaseCode, Long ouId) {
        if (Objects.equals(purchaseContract.getStatus(), PurchaseContractStatus.CHANGEING.getCode())) {
            logBizValidationError("SDP开票申请接收接口处理-该采购合同正在变更中", businessId,
                    "合同状态", "purchaseCode=" + purchaseCode,
                    "status=" + purchaseContract.getStatus());
            throw new BizException(Code.ERROR, "该采购合同正在变更中，无法发起申请");
        }
    }

    /**
     * 保存GSC开票申请
     *
     * @param requestISP        ISP请求
     * @param purchaseContract  采购合同
     * @param project           项目
     * @param gscPaymentInvoice GSC支付发票
     * @param reRequest         是否重新请求
     * @return 保存后的GSC支付发票
     */
    private GscPaymentInvoice saveGscPaymentInvoice(SdpReceiveGscPaymentInvoiceIspDetail requestISP,
                                                    PurchaseContract purchaseContract, Project project,
                                                    GscPaymentInvoice gscPaymentInvoice, boolean reRequest) {
        if (!reRequest) {
            gscPaymentInvoice = new GscPaymentInvoice();
        }

        gscPaymentInvoice.setOuId(requestISP.getOuId());
        gscPaymentInvoice.setPurchaseContractId(purchaseContract.getId());
        gscPaymentInvoice.setPurchaseCode(purchaseContract.getCode());
        gscPaymentInvoice.setProjectId(purchaseContract.getProjectId());
        gscPaymentInvoice.setProjectCode(project.getCode());
        gscPaymentInvoice.setErpVendorId(requestISP.getErpVendorId());
        gscPaymentInvoice.setVendorCode(requestISP.getVendorCode());
        gscPaymentInvoice.setErpVendorName(requestISP.getErpVendor());
        gscPaymentInvoice.setErpVendorSiteId(requestISP.getErpVendorSiteId().toString());
        gscPaymentInvoice.setCurrency(requestISP.getCurrency());
        gscPaymentInvoice.setInvoiceNumber(requestISP.getInvoiceNumber());
        gscPaymentInvoice.setInvoiceAttribute(requestISP.getInvoiceAttribute());
        gscPaymentInvoice.setTotalInvoiceAmount(requestISP.getInvoiceTotalAmount());
        gscPaymentInvoice.setTaxRate(requestISP.getTaxRate());
        gscPaymentInvoice.setTaxAmount(requestISP.getInvoiceTotalAmount());
        gscPaymentInvoice.setAmountWithoutTax(requestISP.getInvoiceTotalAmountExcludeTax());
        gscPaymentInvoice.setTaxInvoiceStatus(GscPaymentInvoiceTaxInvoiceStatusEnum.NOT_INVOICED.getCode());
        gscPaymentInvoice.setRemark(requestISP.getRemark());
        gscPaymentInvoice.setDeletedFlag(Boolean.FALSE);

        if (Objects.nonNull(gscPaymentInvoice.getId())) {
            gscPaymentInvoiceMapper.updateByPrimaryKeySelective(gscPaymentInvoice);
            logger.info("SDP开票申请接收接口处理-更新GSC开票申请信息: {}", JsonUtils.toString(gscPaymentInvoice));
        } else {
            gscPaymentInvoiceMapper.insert(gscPaymentInvoice);
            logger.info("SDP开票申请接收接口处理-插入GSC开票申请信息: {}", JsonUtils.toString(gscPaymentInvoice));
        }

        return gscPaymentInvoice;
    }

    /**
     * 保存ISP行信息
     *
     * @param requestISP        ISP请求
     * @param gscPaymentInvoice GSC支付发票
     * @param reRequest         是否重新请求
     * @param businessId        业务ID
     * @param purchaseCode      采购合同编号
     * @param ouId              业务实体ID
     */
    private void saveIspDetailLines(SdpReceiveGscPaymentInvoiceIspDetail requestISP,
                                    GscPaymentInvoice gscPaymentInvoice, boolean reRequest,
                                    String businessId, String purchaseCode, Long ouId) {
        if (reRequest) {
            // 重新提交则删除之前的ISP行信息
            ispDetailExtMapper.batchLoginDeleteByInvoiceId(gscPaymentInvoice.getId());
            logger.info("SDP开票申请接收接口处理-重新提交，删除之前的ISP行信息: gscPaymentInvoiceId={}", gscPaymentInvoice.getId());
        }

        List<SdpReceiveGscPaymentInvoiceIspDetail.InvoiceLine> lineList = requestISP.getLineList();
        if (lineList == null || lineList.isEmpty()) {
            logBizValidationError("SDP开票申请接收接口处理-ISP行信息为空", businessId,
                    "ISP行信息", "purchaseCode=" + purchaseCode, "lineList is empty");
            throw new BizException(Code.ERROR, "ISP行信息不能为空");
        }

        for (SdpReceiveGscPaymentInvoiceIspDetail.InvoiceLine line : lineList) {
            if (line == null) continue;

            GscPaymentInvoiceIspDetail ispDetail = new GscPaymentInvoiceIspDetail();
            ispDetail.setGscPaymentInvoiceId(gscPaymentInvoice.getId());
            ispDetail.setLineId(line.getLineId());
            ispDetail.setInvoiceAmount(line.getInvoiceAmount());
            ispDetail.setTaxAmount(line.getTaxAmount());
            ispDetail.setAmountWithoutTax(line.getInvoiceAmountExcludeTax());
            ispDetail.setDeletedFlag(Boolean.FALSE);

            logger.info("SDP开票申请接收接口处理-正在插入ISP行数据: {}", JsonUtils.toString(ispDetail));

            // 插入并检查结果
            int insertResult = gscPaymentInvoiceIspDetailMapper.insert(ispDetail);
            if (insertResult <= 0) {
                logger.error("SDP开票申请接收接口处理-ISP行数据插入失败: {}", JsonUtils.toString(ispDetail));
                throw new BizException(Code.ERROR, "ISP行数据插入失败");
            }
        }
    }

    /**
     * 根据采购合同编号获取采购合同
     *
     * @param purchaseCode 采购合同编号
     * @param ouId         业务实体ID
     * @return 采购合同列表
     */
    private List<PurchaseContract> getPurchaseContractByCode(String purchaseCode, Long ouId) {
        PurchaseContractExample purchaseContractExample = new PurchaseContractExample();
        purchaseContractExample.createCriteria()
                .andCodeEqualTo(purchaseCode)
                .andOuIdEqualTo(ouId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        return purchaseContractService.selectByExample(purchaseContractExample);
    }

    /**
     * 获取采购合同详情
     *
     * @param purchaseContractId 采购合同ID
     * @param taxRate            税率
     * @return 采购合同详情列表
     */
    private List<PurchaseContractDetail> getPurchaseContractDetailList(Long purchaseContractId, String taxRate) {
        PurchaseContractDetailExample purchaseContractDetailExample = new PurchaseContractDetailExample();
        taxRate = Optional.ofNullable(taxRate)
                .map(rate -> "%" + rate + "%")
                .orElse("");
        purchaseContractDetailExample
                .createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andContractIdEqualTo(purchaseContractId)
                .andTaxRateLike(taxRate);
        return purchaseContractDetailMapper.selectByExample(purchaseContractDetailExample);
    }

    /**
     * 构建SDP日志
     *
     * @param businessId 业务ID
     * @param requestISP ISP请求
     * @return SDP日志
     */
    private static SdpLog buildSdpLog(String businessId, SdpReceiveGscPaymentInvoiceIspDetail requestISP) {
        SdpLog sdpLog = new SdpLog();
        sdpLog.setSdpBusinessId(businessId);
        sdpLog.setOperationType(SdpLogOperaTypeEnum.RECEIVE.getCode());
        sdpLog.setSdpBusinessType(TopicCodeEnum.PAYMENT_INVOICE_REQUEST.getTopicCode());
        sdpLog.setOuId(requestISP.getOuId());
        sdpLog.setDataCount(1);
        sdpLog.setDeletedFlag(Boolean.FALSE);
        return sdpLog;
    }

    /**
     * 根据采购合同ID获取已开票金额（含税）
     *
     * @param purchaseContractId 采购合同ID
     * @return 已开票金额
     */
    private BigDecimal getIspInvoiceTotalAmountByContractId(Long purchaseContractId) {
        GscPaymentInvoiceExample example = new GscPaymentInvoiceExample();
        example.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andPurchaseContractIdEqualTo(purchaseContractId)
                .andStatusIn(Arrays.asList(AuditStatus.PENDING.getCode(), AuditStatus.EFFECTIVE.getCode()));
        List<GscPaymentInvoice> gscPaymentInvoiceList = gscPaymentInvoiceMapper.selectByExample(example);
        if (ListUtils.isEmpty(gscPaymentInvoiceList)) return BigDecimal.ZERO;
        return gscPaymentInvoiceList.stream()
                .map(GscPaymentInvoice::getTotalInvoiceAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 根据采购合同ID获取已开票金额（不含税）
     *
     * @param purchaseContractId 采购合同ID
     * @return 已开票金额
     */
    private BigDecimal getIspInvoiceTotalAmountWithoutTaxByContractId(Long purchaseContractId) {
        GscPaymentInvoiceExample example = new GscPaymentInvoiceExample();
        example.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andPurchaseContractIdEqualTo(purchaseContractId)
                .andStatusIn(Arrays.asList(AuditStatus.PENDING.getCode(), AuditStatus.EFFECTIVE.getCode()));
        List<GscPaymentInvoice> gscPaymentInvoiceList = gscPaymentInvoiceMapper.selectByExample(example);
        if (ListUtils.isEmpty(gscPaymentInvoiceList)) return BigDecimal.ZERO;
        return gscPaymentInvoiceList.stream()
                .map(GscPaymentInvoice::getAmountWithoutTax)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 验证GSC ISP 开票申请信息
     * 此方法会对所有必填字段进行校验，如果存在为空的必填字段，将抛出详细的验证异常
     *
     * @param sdpReceiveGscPaymentInvoiceIspDetail SDP接收发票申请信息实体
     * @throws ValidationException 当存在必填字段为空时抛出此异常
     */
    private void validateContractInfo(SdpReceiveGscPaymentInvoiceIspDetail sdpReceiveGscPaymentInvoiceIspDetail) {
        // 创建验证器工厂和验证器实例
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();

        // 存储所有验证错误信息
        List<String> errorMessages = new ArrayList<>();

        // 校验实体信息
        Set<ConstraintViolation<SdpReceiveGscPaymentInvoiceIspDetail>> violations = validator.validate(sdpReceiveGscPaymentInvoiceIspDetail);
        for (ConstraintViolation<SdpReceiveGscPaymentInvoiceIspDetail> violation : violations) {
            errorMessages.add(violation.getMessage());
        }

        // 如果存在验证错误，抛出异常并包含所有错误信息
        if (!errorMessages.isEmpty()) {
            logger.error("ISP开票申请信息验证失败：{}", String.join(",", errorMessages));
            throw new ValidationException("ISP开票申请信息验证失败：" + String.join(",", errorMessages));
        }


        logger.info("ISP开票申请信息验证通过: {}", JsonUtils.toString(sdpReceiveGscPaymentInvoiceIspDetail));
    }

    /**
     * 构建当前用户信息
     *
     * @param ouId 业务实体ID
     */
    private static void buildUserInfo(Long ouId) {
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setUsername("GSC");
        userInfoDto.setUnitId(CacheDataUtils.getTopUnitIdByOuId(ouId));
        SystemContext.set(userInfoDto);
    }

    /**
     * 验证发票总金额与行项目金额是否一致
     * 验证含税总金额和不含税总金额与行项目金额之和是否一致
     *
     * @param requestISP ISP开票申请信息
     * @param businessId 业务ID
     */
    private void validateInvoiceAmountConsistency(SdpReceiveGscPaymentInvoiceIspDetail requestISP, String businessId) {
        // 获取发票总金额信息
        BigDecimal invoiceTotalAmount = requestISP.getInvoiceTotalAmount();
        BigDecimal invoiceTotalAmountExcludeTax = requestISP.getInvoiceTotalAmountExcludeTax();
        // 行项目列表
        List<SdpReceiveGscPaymentInvoiceIspDetail.InvoiceLine> lineList = requestISP.getLineList();
        if (lineList == null || lineList.isEmpty()) {
            logger.error("SDP开票申请接收接口处理-ISP行项目列表为空, businessId:{}", businessId);
            throw new BizException(Code.ERROR, "ISP开票申请行列表不能为空");
        }

        // 计算行含税金额之和
        BigDecimal calculatedTotalAmount = lineList.stream()
                .map(SdpReceiveGscPaymentInvoiceIspDetail.InvoiceLine::getInvoiceAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);

        // 计算行不含税金额之和
        BigDecimal calculatedTotalAmountExcludeTax = lineList.stream()
                .map(SdpReceiveGscPaymentInvoiceIspDetail.InvoiceLine::getInvoiceAmountExcludeTax)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);

        // 验证含税总金额是否一致
        if (invoiceTotalAmount.compareTo(calculatedTotalAmount) != 0) {
            logBizValidationError("SDP开票申请接收接口处理-发票总金额（含税）与行金额累计不一致", businessId,
                    "发票总金额（含税）", invoiceTotalAmount.toString(), calculatedTotalAmount.toString());
            throw new BizException(Code.ERROR, "发票总金额（含税）与行金额累计不一致");
        }

        // 验证不含税总金额是否一致
        if (invoiceTotalAmountExcludeTax.compareTo(calculatedTotalAmountExcludeTax) != 0) {
            logBizValidationError("SDP开票申请接收接口处理-发票总金额（不含税）与行金额累计不一致", businessId,
                    "发票总金额（不含税）", invoiceTotalAmountExcludeTax.toString(), calculatedTotalAmountExcludeTax.toString());
            throw new BizException(Code.ERROR, "发票总金额（不含税）与行金额累计不一致");
        }
        //发票行税额校验
        for (SdpReceiveGscPaymentInvoiceIspDetail.InvoiceLine invoiceLine : lineList) {
            BigDecimal invoiceAmount = invoiceLine.getInvoiceAmount().setScale(2, RoundingMode.HALF_UP);
            BigDecimal taxAmount = invoiceLine.getTaxAmount().setScale(2, RoundingMode.HALF_UP);
            // 从不含税金额计算税额
            BigDecimal invoiceAmountExcludeTax = invoiceLine.getInvoiceAmountExcludeTax();
            BigDecimal taxRate = requestISP.getTaxRate().movePointLeft(2);
            BigDecimal calcTaxAmount = invoiceAmountExcludeTax.multiply(taxRate).setScale(2, RoundingMode.DOWN);  // 使用向下舍入
            if(calcTaxAmount.compareTo(taxAmount) != 0){
                logBizValidationError("SDP开票申请接收接口处理-发票行税额不正确", businessId,
                        "发票行税额不正确", taxAmount.toString(), calcTaxAmount.toString());
                throw new BizException(Code.ERROR, "发票行税额不正确");
            }
        }


        logger.info("SDP开票申请接收接口处理-发票总金额与行项目金额验证一致性通过, businessId:{}", businessId);
    }
}