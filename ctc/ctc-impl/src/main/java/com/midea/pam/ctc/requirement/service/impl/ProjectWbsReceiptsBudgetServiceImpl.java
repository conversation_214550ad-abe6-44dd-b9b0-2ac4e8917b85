package com.midea.pam.ctc.requirement.service.impl;

import com.alibaba.fastjson.JSON;
import com.midea.pam.common.ctc.cache.ProjectWbsBudgetSummaryCache;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDesignPlanRelDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsDesignPlanRel;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsDesignPlanRelExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample;
import com.midea.pam.common.enums.ProjectWbsBudgetSummarySummaryTypeEnums;
import com.midea.pam.common.enums.RequirementStatusEnum;
import com.midea.pam.common.enums.RequirementTypeEnum;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.cache.ProjectWbsBudgetSummaryCacheUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.MaterialCategoryEnum;
import com.midea.pam.ctc.common.enums.ProjectWbsReceiptsBudgetDemandTypeEnums;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsDesignPlanRelMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsBudgetService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class ProjectWbsReceiptsBudgetServiceImpl implements ProjectWbsReceiptsBudgetService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectWbsReceiptsBudgetServiceImpl.class);

    @Resource
    private ProjectWbsReceiptsBudgetMapper projectWbsReceiptsBudgetMapper;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectWbsReceiptsDesignPlanRelMapper projectWbsReceiptsDesignPlanRelMapper;

    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;

    @Resource
    private ProjectWbsReceiptsBudgetExtMapper projectWbsReceiptsBudgetExtMapper;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 创建需求预算
     *
     * @param dto
     */
    @Override
    public List<ProjectWbsReceiptsBudgetDto> createRequirementBudget(ProjectWbsReceiptsDto dto) {
        logger.info("createRequirementBudget的ProjectWbsReceiptsDto：{}", JsonUtils.toString(dto));
        List<ProjectWbsReceiptsBudgetDto> result = new ArrayList<>();
        ProjectWbsReceiptsBudgetDto childBudgetDto;
        ProjectWbsReceiptsBudgetDto parentBudgetDto;
        ProjectWbsReceiptsBudget childBudget;
        ProjectWbsReceiptsBudget parentBudget;
        if (CollectionUtils.isEmpty(dto.getDesignRelList())) {
            return result;
        }
        /**
         * Map记录父预算
         * key = wbsSummaryCode  value = 预算
         */
        Map<String, ProjectWbsReceiptsBudgetDto> parentBudgetMap = new HashMap<>();
        /**
         * Map记录子预算
         * key = wbsSummaryCode + extIs  value = 预算
         */
        Map<String, ProjectWbsReceiptsBudgetDto> childBudgetMap = new HashMap<>();

        if (null != dto.getId()) {
            projectWbsReceiptsBudgetExtMapper.deleteByProjectWbsReceiptsId(dto.getId());
        }

        for (ProjectWbsReceiptsDesignPlanRelDto relDto : dto.getDesignRelList()) {

            MilepostDesignPlanDetail planDetail = milepostDesignPlanDetailMapper.selectByPrimaryKey(relDto.getDesignPlanDetailId());
            if (null == planDetail) {
                // 查询详细设计为空，不处理
                continue;
            }

            // 如果是wbs层级，只会处理最底层，否则跳过
            if (StringUtils.isNotBlank(planDetail.getWbsSummaryCode()) && Boolean.FALSE.equals(planDetail.getWbsConfirmFlag())) {
                continue;
            }

            // 只对外购物料产生采购需求
            if (!(StringUtils.isNotBlank(planDetail.getMaterialCategory()) && MaterialCategoryEnum.PURCHASED_PARTS.msg().equals(planDetail.getMaterialCategory()))) {
                continue;
            }

            /**
             * 历史关联确认记录
             */
            ProjectWbsReceiptsDesignPlanRelExample relExample = new ProjectWbsReceiptsDesignPlanRelExample();
            relExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(dto.getProjectId())
                    .andDesignPlanDetailIdEqualTo(relDto.getDesignPlanDetailId());
            long hasReceipt = projectWbsReceiptsDesignPlanRelMapper.countByExample(relExample);

            // 已被进度或部分确认，跳过
            if (hasReceipt > 0) {
                continue;
            }

            // 保存parent预算
            if (parentBudgetMap.containsKey(planDetail.getWbsSummaryCode())) {
                parentBudgetDto = parentBudgetMap.get(planDetail.getWbsSummaryCode());
            } else {
                parentBudget = new ProjectWbsReceiptsBudget();
                parentBudget.setProjectWbsReceiptsId(dto.getId());
                parentBudget.setBudgetOccupiedAmount(BigDecimal.ZERO);
                parentBudget.setWbsSummaryCode(planDetail.getWbsSummaryCode());
                parentBudget.setDeletedFlag(false);
                parentBudget.setVersion(1L);
                parentBudget.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode());
                projectWbsReceiptsBudgetMapper.insert(parentBudget);
                parentBudgetDto = BeanConverter.copy(parentBudget, ProjectWbsReceiptsBudgetDto.class);
                parentBudgetMap.put(planDetail.getWbsSummaryCode(), parentBudgetDto);
            }

            // extIs可能有空值，默认处理空为false
            boolean extIs = Boolean.TRUE.equals(planDetail.getExtIs());
            if (childBudgetMap.containsKey(planDetail.getWbsSummaryCode() + extIs)) {
                // 重复的子预算不处理
                continue;
            }
            childBudget = new ProjectWbsReceiptsBudget();
            childBudget.setProjectWbsReceiptsId(dto.getId());
            childBudget.setDeletedFlag(false);
            childBudget.setVersion(1L);
            childBudget.setParentid(parentBudgetDto.getId());
            childBudget.setBudgetOccupiedAmount(BigDecimal.ZERO);
            childBudget.setWbsSummaryCode(planDetail.getWbsSummaryCode());
            childBudget.setDemandType(extIs ? ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode() :
                    ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode());
            projectWbsReceiptsBudgetMapper.insert(childBudget);
            childBudgetDto = BeanConverter.copy(childBudget, ProjectWbsReceiptsBudgetDto.class);
            childBudgetMap.put(planDetail.getWbsSummaryCode() + extIs, childBudgetDto);
            parentBudgetDto.addChild(childBudgetDto);
            parentBudgetMap.put(planDetail.getWbsSummaryCode(), parentBudgetDto);
        }
        return new ArrayList(parentBudgetMap.values());
    }

    /**
     * 查询需求预算
     *
     * @param receipts
     * @param projectId
     * @return
     */
    @Override
    public List<ProjectWbsReceiptsBudgetDto> findRequirementBudget(ProjectWbsReceipts receipts, Long projectId) {
        Project project = projectMapper.selectByPrimaryKey(projectId);

        //先通过单据找到所有在此项目下的已审批通过的需求发布单据
        ProjectWbsReceiptsExample receiptsExample = new ProjectWbsReceiptsExample();
        receiptsExample.createCriteria().andIdNotEqualTo(receipts.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andProjectIdEqualTo(project.getId())
                .andRequirementTypeEqualTo(RequirementTypeEnum.REQUIREMENT_PUBLISH.getCode())
                .andRequirementStatusIn(Arrays.asList(RequirementStatusEnum.PROCESS.getCode(), RequirementStatusEnum.PENDING.getCode()));
        List<ProjectWbsReceipts> otherProjectWbsReceipts = projectWbsReceiptsService.selectByExampleWithBLOBs(receiptsExample);
        List<Long> otherReceiptsId = CollectionUtils.isEmpty(otherProjectWbsReceipts) ? new ArrayList<>()
                : otherProjectWbsReceipts.stream().map(ProjectWbsReceipts::getId).collect(Collectors.toList());

        //当前的单据预算
        ProjectWbsReceiptsBudgetExample example = new ProjectWbsReceiptsBudgetExample();
        example.createCriteria().andProjectWbsReceiptsIdEqualTo(receipts.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<ProjectWbsReceiptsBudget> budgetList = projectWbsReceiptsBudgetMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(budgetList)) {
            return new ArrayList<>();
        }

        List<String> wbsSummaryCodes = budgetList.stream().map(ProjectWbsReceiptsBudget::getWbsSummaryCode).collect(Collectors.toList());
        Map<String, List<ProjectWbsReceiptsBudget>> otherBudgetMap = new HashMap<>();
        //其他单据包含此单据 WBS的预算
        if (ListUtils.isNotEmpty(otherReceiptsId)) {
            ProjectWbsReceiptsBudgetExample otherExample = new ProjectWbsReceiptsBudgetExample();
            otherExample.createCriteria().andProjectWbsReceiptsIdIn(otherReceiptsId).andWbsSummaryCodeIn(wbsSummaryCodes).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<ProjectWbsReceiptsBudget> otherBudgets = projectWbsReceiptsBudgetMapper.selectByExample(otherExample);
            otherBudgetMap.putAll(CollectionUtils.isEmpty(otherBudgets) ? new HashMap<>()
                    : otherBudgets.stream().collect(Collectors.groupingBy(budget -> budget.getWbsSummaryCode() + ":" + budget.getDemandType())));
        }

        logger.info("findRequirementBudget的budgetList：{}", JSON.toJSONString(budgetList));
        List<ProjectWbsReceiptsBudgetDto> parentDtoList = new ArrayList<>();
        // 找出所有parent
        for (ProjectWbsReceiptsBudget budget : budgetList) {
            if (null == budget.getParentid() || -1 == budget.getParentid()) {
                ProjectWbsReceiptsBudgetDto dto = BeanConverter.copy(budget, ProjectWbsReceiptsBudgetDto.class);
                List<ProjectWbsReceiptsBudget> projectWbsReceiptsBudgets =
                        otherBudgetMap.get(budget.getWbsSummaryCode() + ":" + budget.getDemandType());
                BigDecimal otherBudgetOccupiedAmount = BigDecimal.ZERO;
                if (ListUtils.isNotEmpty(projectWbsReceiptsBudgets)) {
                    otherBudgetOccupiedAmount =
                            projectWbsReceiptsBudgets.stream().map(e -> Optional.ofNullable(e.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                ProjectWbsBudgetSummaryCache cache = ProjectWbsBudgetSummaryCacheUtils.getCache(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType(),
                        project.getId(), dto.getWbsSummaryCode());
                if (Objects.nonNull(cache)) {
                    dto.setPrice(cache.getPrice());
                    dto.setBaselineCost(cache.getBaselineCost());
                    dto.setDemandCost(otherBudgetOccupiedAmount.add(Optional.ofNullable(budget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO)));
                    dto.setOnTheWayCost(cache.getOnTheWayCost());
                    dto.setIncurredCost(cache.getIncurredCost());
                    dto.setRemainingCost(cache.getPrice().subtract(otherBudgetOccupiedAmount));
                    dto.setChangeAccumulateCost(cache.getChangeAccumulateCost());
                }
                parentDtoList.add(dto);
            }
        }
        // 设置child
        for (ProjectWbsReceiptsBudgetDto parent : parentDtoList) {
            List<ProjectWbsReceiptsBudget> childs =
                    budgetList.stream().filter(a -> Objects.equals(parent.getId(), a.getParentid())).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(childs)) {
                List<ProjectWbsReceiptsBudgetDto> childDtoList = BeanConverter.copy(childs, ProjectWbsReceiptsBudgetDto.class);
                for (ProjectWbsReceiptsBudgetDto childDto : childDtoList) {
                    List<ProjectWbsReceiptsBudget> projectWbsReceiptsBudgets =
                            otherBudgetMap.get(childDto.getWbsSummaryCode() + ":" + childDto.getDemandType());
                    BigDecimal otherBudgetOccupiedAmount = BigDecimal.ZERO;
                    if (ListUtils.isNotEmpty(projectWbsReceiptsBudgets)) {
                        otherBudgetOccupiedAmount =
                                projectWbsReceiptsBudgets.stream().map(e -> Optional.ofNullable(e.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    ProjectWbsBudgetSummaryCache cache =
                            ProjectWbsBudgetSummaryCacheUtils.getCache(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType(), project.getId(),
                                    childDto.getWbsSummaryCode());
                    if (Objects.nonNull(cache)) {
                        childDto.setPrice(cache.getPrice());
                        childDto.setBaselineCost(cache.getBaselineCost());
                        childDto.setDemandCost(otherBudgetOccupiedAmount.add(Optional.ofNullable(childDto.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO)));
                        childDto.setOnTheWayCost(cache.getOnTheWayCost());
                        childDto.setIncurredCost(cache.getIncurredCost());
                        childDto.setRemainingCost(cache.getPrice().subtract(otherBudgetOccupiedAmount));
                        childDto.setChangeAccumulateCost(cache.getChangeAccumulateCost());
                    }
                }
                parent.addChilds(childDtoList);
            }
        }
        return parentDtoList;
    }

    /**
     * 保存需求预算
     *
     * @param budgetList
     */
    @Override
    public void saveRequirementBudget(List<ProjectWbsReceiptsBudgetDto> budgetList) {
        if (CollectionUtils.isEmpty(budgetList)) {
            return;
        }
        ProjectWbsReceiptsBudget saveEntity;
        for (ProjectWbsReceiptsBudgetDto parent : budgetList) {
            saveEntity = new ProjectWbsReceiptsBudget();
            saveEntity.setId(parent.getId());
            saveEntity.setBudgetOccupiedAmount(parent.getBudgetOccupiedAmount());
            projectWbsReceiptsBudgetMapper.updateByPrimaryKeySelective(saveEntity);
            if (CollectionUtils.isEmpty(parent.getChilds())) {
                continue;
            }
            for (ProjectWbsReceiptsBudgetDto child : parent.getChilds()) {
                saveEntity = new ProjectWbsReceiptsBudget();
                saveEntity.setId(child.getId());
                saveEntity.setBudgetOccupiedAmount(child.getBudgetOccupiedAmount());
                projectWbsReceiptsBudgetMapper.updateByPrimaryKeySelective(saveEntity);
            }
        }
    }

    /**
     * 获取wbs下的预算占用金额（外包）
     *
     * @param recepitsId
     * @param wbsCode
     * @return
     */
    @Override
    public BigDecimal getWbsBudgetOccupiedAmountByOs(Long recepitsId, String wbsCode) {
        ProjectWbsReceiptsBudgetExample example = new ProjectWbsReceiptsBudgetExample();
        example.createCriteria().andDeletedFlagEqualTo(false)
                .andProjectWbsReceiptsIdEqualTo(recepitsId)
                .andDemandTypeEqualTo(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode())
                .andWbsSummaryCodeEqualTo(wbsCode);
        BigDecimal sumPrice = BigDecimal.ZERO;
        List<ProjectWbsReceiptsBudget> budgetList = projectWbsReceiptsBudgetMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(budgetList)) {
            for (ProjectWbsReceiptsBudget budget : budgetList) {
                sumPrice = sumPrice.add(Optional.ofNullable(budget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO));
            }
        }
        return sumPrice;
    }

    @Override
    public List<ProjectWbsReceiptsBudgetDto> selectByExample(ProjectWbsReceiptsBudgetExample example) {
        return BeanConverter.copy(projectWbsReceiptsBudgetMapper.selectByExample(example), ProjectWbsReceiptsBudgetDto.class);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjectWbsReceiptsBudget budget) {
        return projectWbsReceiptsBudgetMapper.updateByPrimaryKeySelective(budget);
    }
}
