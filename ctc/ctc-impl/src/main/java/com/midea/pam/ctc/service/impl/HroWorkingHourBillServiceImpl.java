package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.mybatisplus.toolkit.IdWorker;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.CoaSubjectDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.CoaSubject;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CostAccountingErpDto;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.dto.HroBillEntryAccountInfoDto;
import com.midea.pam.common.ctc.dto.HroWorkingHourBillCostDetailDto;
import com.midea.pam.common.ctc.dto.HroWorkingHourBillDto;
import com.midea.pam.common.ctc.dto.HroWorkingHourBillItemDto;
import com.midea.pam.common.ctc.dto.HroWorkingHourItemDto;
import com.midea.pam.common.ctc.dto.PurchaseContractDTO;
import com.midea.pam.common.ctc.entity.BusiSceneNonSale;
import com.midea.pam.common.ctc.entity.BusiSceneNonSaleDetail;
import com.midea.pam.common.ctc.entity.BusiSceneNonSaleDetailExample;
import com.midea.pam.common.ctc.entity.BusiSceneNonSaleExample;
import com.midea.pam.common.ctc.entity.HroBillWorkingHourRef;
import com.midea.pam.common.ctc.entity.HroWorkingHourBill;
import com.midea.pam.common.ctc.entity.HroWorkingHourBillCostDetail;
import com.midea.pam.common.ctc.entity.HroWorkingHourBillCostDetailExample;
import com.midea.pam.common.ctc.entity.HroWorkingHourBillExample;
import com.midea.pam.common.ctc.entity.HroWorkingHourBillItem;
import com.midea.pam.common.ctc.entity.HroWorkingHourBillItemExample;
import com.midea.pam.common.ctc.entity.MaterialOutsourcingContractConfig;
import com.midea.pam.common.ctc.entity.MaterialOutsourcingContractConfigExample;
import com.midea.pam.common.ctc.entity.PaymentPlan;
import com.midea.pam.common.ctc.entity.PaymentPlanExample;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractBudget;
import com.midea.pam.common.ctc.entity.PurchaseContractBudgetExample;
import com.midea.pam.common.ctc.entity.PurchaseContractExample;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.enums.CtcBusiSceneNsModule;
import com.midea.pam.common.enums.CtcBusiSceneNsType;
import com.midea.pam.common.enums.HroWorkingHourBillStatus;
import com.midea.pam.common.enums.HroWorkingHourBillSyncStatus;
import com.midea.pam.common.enums.HroWorkingHourStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.PaymentPlanStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.contract.service.helper.PurchaseContractHelper;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.BusiSceneNonSaleDetailMapper;
import com.midea.pam.ctc.mapper.BusiSceneNonSaleMapper;
import com.midea.pam.ctc.mapper.HroBillWorkingHourRefExtMapper;
import com.midea.pam.ctc.mapper.HroBillWorkingHourRefMapper;
import com.midea.pam.ctc.mapper.HroWorkingHourBillCostDetailMapper;
import com.midea.pam.ctc.mapper.HroWorkingHourBillExtMapper;
import com.midea.pam.ctc.mapper.HroWorkingHourBillItemExtMapper;
import com.midea.pam.ctc.mapper.HroWorkingHourBillItemMapper;
import com.midea.pam.ctc.mapper.HroWorkingHourBillMapper;
import com.midea.pam.ctc.mapper.HroWorkingHourExtMapper;
import com.midea.pam.ctc.mapper.MaterialOutsourcingContractConfigMapper;
import com.midea.pam.ctc.mapper.PaymentPlanExtMapper;
import com.midea.pam.ctc.mapper.PaymentPlanMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.HroWorkingHourBillService;
import com.midea.pam.ctc.service.OrganizationRelExtService;
import com.midea.pam.ctc.service.ResendExecuteService;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class HroWorkingHourBillServiceImpl implements HroWorkingHourBillService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private HroWorkingHourExtMapper hroWorkingHourExtMapper;
    @Resource
    private PurchaseContractBudgetMapper purchaseContractBudgetMapper;
    @Resource
    private HroWorkingHourBillMapper hroWorkingHourBillMapper;
    @Resource
    private HroWorkingHourBillExtMapper hroWorkingHourBillExtMapper;
    @Resource
    private HroWorkingHourBillItemExtMapper hroWorkingHourBillItemExtMapper;
    @Resource
    private HroWorkingHourBillItemMapper hroWorkingHourBillItemMapper;
    @Resource
    private HroBillWorkingHourRefMapper hroBillWorkingHourRefMapper;
    @Resource
    private HroBillWorkingHourRefExtMapper hroBillWorkingHourRefExtMapper;
    @Resource
    private PaymentPlanMapper paymentPlanMapper;
    @Resource
    private PaymentPlanExtMapper paymentPlanExtMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private MaterialOutsourcingContractConfigMapper materialOutsourcingContractConfigMapper;
    @Resource
    private HroWorkingHourBillCostDetailMapper hroWorkingHourBillCostDetailMapper;
    @Resource
    private BusiSceneNonSaleMapper busiSceneNonSaleMapper;
    @Resource
    private BusiSceneNonSaleDetailMapper busiSceneNonSaleDetailMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OrganizationRelExtService organizationRelExtService;
    @Resource
    private EsbService esbService;
    @Resource
    private ResendExecuteService resendExecuteService;
    @Resource
    private SdpService sdpService;


    @Override
    public List<HroWorkingHourBillItemDto> getNotBillWorkingHour(Long contractId, Date startDate, Date endDate, Long billId) {
        // 查询未对账工时
        List<HroWorkingHourItemDto> hroWorkingHourItemDtos = hroWorkingHourExtMapper
                .selectNotBillWorkingHour(contractId, startDate, endDate, billId);

        // 查询点工角色的采购单价
        PurchaseContractBudgetExample budgetExample = new PurchaseContractBudgetExample();
        budgetExample.createCriteria()
                .andPurchaseContractIdEqualTo(contractId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        Map<Long, BigDecimal> rolePriceMap = purchaseContractBudgetMapper.selectByExample(budgetExample)
                .stream().collect(Collectors.toMap(
                        PurchaseContractBudget::getMaterialId,
                        PurchaseContractBudget::getPrice, (e1, e2) -> e1));

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        return hroWorkingHourItemDtos.stream().collect(
                        // 按mip账号和姓名分组工时
                        Collectors.groupingBy(e -> e.getUsername() + e.getName()))
                .values().stream().map(userGroup -> {
                    HroWorkingHourBillItemDto userGroupItem = new HroWorkingHourBillItemDto();
                    userGroupItem.setUsername(userGroup.get(0).getUsername());
                    userGroupItem.setName(userGroup.get(0).getName());
                    // 用户已填报的工时数
                    BigDecimal fillWorkingHour = userGroup.stream()
                            .map(HroWorkingHourItemDto::getWorkingHour).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 用户已审批通过的工时数
                    BigDecimal passWorkingHour = userGroup.stream().filter(e -> e.getImportStatus() == HroWorkingHourStatus.PASS.getCode())
                            .map(HroWorkingHourItemDto::getWorkingHour).reduce(BigDecimal.ZERO, BigDecimal::add);
                    userGroupItem.setFillWorkingHour(fillWorkingHour);
                    userGroupItem.setPassWorkingHour(passWorkingHour);
                    // 用户下按角色分组的工时
                    userGroupItem.setGroupWorkingHours(
                            userGroup.stream().collect(
                                            // 用户下按角色分组的工时
                                            Collectors.groupingBy(HroWorkingHourItemDto::getRoleName))
                                    .values().stream().map(roleGroup -> {
                                        HroWorkingHourBillItemDto roleGroupItem = new HroWorkingHourBillItemDto();
                                        roleGroupItem.setRoleName(roleGroup.get(0).getRoleName());
                                        roleGroupItem.setRolePrice(rolePriceMap.get(roleGroup.get(0).getRoleId()));
                                        // 用户角色下按月汇总的工时数
                                        roleGroupItem.setMonthWorkingHour(
                                                roleGroup.stream().filter(e -> e.getImportStatus() == HroWorkingHourStatus.PASS.getCode())
                                                        .collect(Collectors.toMap(e ->
                                                                        dateFormat.format(e.getWorkDate()),
                                                                HroWorkingHourItemDto::getWorkingHour, BigDecimal::add)));
                                        return roleGroupItem;
                                    }).collect(Collectors.toList()));
                    // 用户分组关联的工时id
                    userGroupItem.setHroWorkingHourItemIds(userGroup.stream()
                            .map(HroWorkingHourItemDto::getId).collect(Collectors.toList()));
                    return userGroupItem;
                }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveHroWorkingHourBill(HroWorkingHourBillDto hroWorkingHourBillDto) {

        List<HroWorkingHourBillItemDto> billItemsDtoList = hroWorkingHourBillDto.getBillItems();
        // 对账单总工时
        BigDecimal totalWorkingHour = BigDecimal.ZERO;
        // 对账单关联的工时id
        List<Long> workingHourItemIds = new ArrayList<>();
        if (billItemsDtoList != null) {
            for (HroWorkingHourBillItemDto billItem : billItemsDtoList) {
                for (HroWorkingHourBillItemDto groupWorkingHour : billItem.getGroupWorkingHours()) {
                    totalWorkingHour = totalWorkingHour.add(
                            groupWorkingHour.getMonthWorkingHour().values()
                                    .stream().reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
            hroWorkingHourBillDto.setTotalWorkingHour(totalWorkingHour);
            billItemsDtoList.forEach(e -> workingHourItemIds.addAll(e.getHroWorkingHourItemIds()));
        }

        HroWorkingHourBill hroWorkingHourBill = BeanConverter.copy(hroWorkingHourBillDto, HroWorkingHourBill.class);
        if (Objects.equals(hroWorkingHourBillDto.getIsSubmit(), Boolean.TRUE)) {
            // 提交
            hroWorkingHourBill.setStatus(HroWorkingHourBillStatus.WAITING_HANDLE.getCode());
            if (ListUtils.isEmpty(billItemsDtoList)) {
                throw new BizException(Code.ERROR, "对账单明细不能为空");
            }
        } else {
            // 暂存
            hroWorkingHourBill.setStatus(HroWorkingHourBillStatus.DRAFT.getCode());
        }
        checkWorkingHourItem(hroWorkingHourBillDto.getId(), workingHourItemIds);
        if (hroWorkingHourBillDto.getId() == null) {
            String code = CodePrefix.HRO_WORKING_HOUR_BILL_CODE.code() + CacheDataUtils.generateSequence(3,
                    CodePrefix.HRO_WORKING_HOUR_BILL_CODE.code(), "yyMMdd");
            hroWorkingHourBill.setCode(code);
            hroWorkingHourBill.setDeletedFlag(Boolean.FALSE);
            hroWorkingHourBill.setCreator(SystemContext.getUserName());
            hroWorkingHourBill.setUnitId(SystemContext.getUnitId());
            hroWorkingHourBill.setCollectionFlag(Boolean.FALSE);
            hroWorkingHourBill.setErpSyncStatus(HroWorkingHourBillSyncStatus.NO_SYNC.getCode());
            hroWorkingHourBillMapper.insert(hroWorkingHourBill);
        } else {
            HroWorkingHourBill hroWorkingHourBill1 = hroWorkingHourBillMapper.selectByPrimaryKey(hroWorkingHourBillDto.getId());
            if (!hroWorkingHourBill1.getCreateBy().equals(SystemContext.getUserId())) {
                throw new BizException(Code.ERROR, "只有制单人允许提交");
            }
            if (hroWorkingHourBill1.getStatus() != HroWorkingHourBillStatus.DRAFT.getCode()) {
                throw new BizException(Code.ERROR, String.format("不能暂存或提交状态为【%s】的对账单",
                        HroWorkingHourBillStatus.getValue(hroWorkingHourBill1.getStatus())));
            }
            hroWorkingHourBillMapper.updateByPrimaryKeySelective(hroWorkingHourBill);
            // 逻辑删除之前保存的工时关联记录
            hroBillWorkingHourRefExtMapper.deleteByBillId(hroWorkingHourBillDto.getId());
            // 逻辑删除之前保存的对账单明细行记录
            hroWorkingHourBillItemExtMapper.deleteWorkingHourBillItem(hroWorkingHourBillDto.getId());
        }

        if (ListUtils.isNotEmpty(workingHourItemIds)) {
            List<HroBillWorkingHourRef> refs = workingHourItemIds.stream().map(e -> {
                HroBillWorkingHourRef ref = new HroBillWorkingHourRef();
                ref.setBillId(hroWorkingHourBill.getId());
                ref.setWorkingHourItemId(e);
                ref.setDeletedFlag(Boolean.FALSE);
                return ref;
            }).collect(Collectors.toList());
            hroBillWorkingHourRefExtMapper.batchInsert(refs);
        }

        if (ListUtils.isNotEmpty(billItemsDtoList)) {
            List<HroWorkingHourBillItem> billItems = new ArrayList<>();
            billItemsDtoList.stream().filter(g -> g.getGroupWorkingHours() != null)
                    .forEach(g -> g.getGroupWorkingHours().forEach(e -> {
                                HroWorkingHourBillItem billItem = BeanConverter.copy(e, HroWorkingHourBillItem.class);
                                billItem.setUsername(g.getUsername());
                                billItem.setName(g.getName());
                                billItem.setBillId(hroWorkingHourBill.getId());
                                billItem.setDeletedFlag(Boolean.FALSE);
                                billItems.add(billItem);
                            })
                    );
            hroWorkingHourBillItemExtMapper.batchInsert(billItems);
        }
        return hroWorkingHourBill.getId();
    }

    /**
     * 检测对账单打包的工时是否有已对账的
     *
     * @param billId             对账单id,存在则排除其占用的
     * @param workingHourItemIds 工时id
     */
    private void checkWorkingHourItem(Long billId, List<Long> workingHourItemIds) {
        if (ListUtils.isNotEmpty(workingHourItemIds)) {
            List<HroWorkingHourBillDto> billDtos = hroWorkingHourBillExtMapper.selectBillInfo(billId, workingHourItemIds);
            if (!billDtos.isEmpty()) {
                throw new BizException(Code.ERROR, "存在已对账工时，请刷新重新打包工时");
            }
        }
    }

    @Override
    public PageInfo<HroWorkingHourBillDto> pageHroWorkingHourBill(HroWorkingHourBillDto billDto) {
        PageHelper.startPage(billDto.getPageNum() == null ? 1 : billDto.getPageNum(),
                billDto.getPageSize() == null ? 10 : billDto.getPageSize());
        billDto.setUnitId(SystemContext.getUnitId());
        if (StringUtils.isNotEmpty(billDto.getStatusStr())) {
            billDto.setStatuses(Arrays.stream(billDto.getStatusStr().split(","))
                    .map(Integer::parseInt).collect(Collectors.toList()));
        }
        if (StringUtils.isNotEmpty(billDto.getErpSyncStatusStr())) {
            billDto.setErpSyncStatuses(Arrays.stream(billDto.getErpSyncStatusStr().split(","))
                    .map(Integer::parseInt).collect(Collectors.toList()));
        }
        List<HroWorkingHourBillDto> billDtos = hroWorkingHourBillExtMapper.pageHroWorkingHourBill(billDto);
        return BeanConverter.convertPage(billDtos, HroWorkingHourBillDto.class);
    }

    @Override
    public HroWorkingHourBillDto detail(Long id) {
        HroWorkingHourBillDto billDto = hroWorkingHourBillExtMapper.selectById(id);
        if (billDto == null) {
            throw new BizException(Code.ERROR, "单据不存在");
        }
        // 对账单打包的工时
        List<HroWorkingHourItemDto> hroWorkingHourItemDtos = hroWorkingHourExtMapper.selectWorkingHourByBillId(id);

        HroWorkingHourBillItemExample billItemExample = new HroWorkingHourBillItemExample();
        billItemExample.createCriteria()
                .andBillIdEqualTo(id)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        Map<String, HroWorkingHourBillItem> billItemsMap = hroWorkingHourBillItemMapper.selectByExample(billItemExample)
                .stream().collect(Collectors.toMap(e -> e.getUsername() + e.getName() + e.getRoleName(), e -> e));

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");

        List<HroWorkingHourBillItemDto> billItemDtos = hroWorkingHourItemDtos.stream().collect(
                        // 按mip账号和姓名分组工时
                        Collectors.groupingBy(e -> e.getUsername() + e.getName()))
                .values().stream().map(userGroup -> {
                    HroWorkingHourBillItemDto userGroupItem = new HroWorkingHourBillItemDto();
                    userGroupItem.setUsername(userGroup.get(0).getUsername());
                    userGroupItem.setName(userGroup.get(0).getName());
                    // 用户已填报的工时数
                    BigDecimal fillWorkingHour = userGroup.stream().map(HroWorkingHourItemDto::getWorkingHour)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    userGroupItem.setFillWorkingHour(fillWorkingHour);
                    userGroupItem.setPassWorkingHour(fillWorkingHour);
                    // 用户下按角色分组的工时
                    userGroupItem.setGroupWorkingHours(userGroup.stream().collect(
                                    // 用户下按角色分组的工时
                                    Collectors.groupingBy(HroWorkingHourItemDto::getRoleName))
                            .values().stream().map(roleGroup -> {
                                HroWorkingHourBillItem billItem = billItemsMap.get(
                                        roleGroup.get(0).getUsername() +
                                                roleGroup.get(0).getName() +
                                                roleGroup.get(0).getRoleName());
                                HroWorkingHourBillItemDto roleGroupItem = BeanConverter.copy(billItem, HroWorkingHourBillItemDto.class);
                                // 用户角色下按月汇总的工时数
                                roleGroupItem.setMonthWorkingHour(
                                        roleGroup.stream().collect(Collectors.toMap(e ->
                                                        dateFormat.format(e.getWorkDate()),
                                                HroWorkingHourItemDto::getWorkingHour, BigDecimal::add)));
                                return roleGroupItem;
                            }).collect(Collectors.toList()));
                    // 用户分组关联的工时id
                    userGroupItem.setHroWorkingHourItemIds(userGroup.stream()
                            .map(HroWorkingHourItemDto::getId).collect(Collectors.toList()));
                    return userGroupItem;
                }).collect(Collectors.toList());
        billDto.setBillItems(billItemDtos);
        return billDto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long submitApprove(HroWorkingHourBillDto hroWorkingHourBillDto) {
        HroWorkingHourBill hroWorkingHourBill = hroWorkingHourBillMapper.selectByPrimaryKey(hroWorkingHourBillDto.getId());
        if (!hroWorkingHourBill.getHandlerId().equals(SystemContext.getUserId())) {
            throw new BizException(Code.ERROR, "只有处理人允许提交审批");
        }
        if (hroWorkingHourBill.getStatus() != HroWorkingHourBillStatus.WAITING_HANDLE.getCode()) {
            throw new BizException(Code.ERROR, String.format("不能提交审批状态为【%s】的对账单",
                    HroWorkingHourBillStatus.getValue(hroWorkingHourBill.getStatus())));
        }
        hroWorkingHourBill = BeanConverter.copy(hroWorkingHourBillDto, HroWorkingHourBill.class);
        hroWorkingHourBillMapper.updateByPrimaryKeySelective(hroWorkingHourBill);

        if (ListUtils.isEmpty(hroWorkingHourBillDto.getPaymentPlans())) {
            throw new BizException(Code.ERROR, "付款计划不能为空");
        }

        List<PaymentPlan> paymentPlans = BeanConverter.copy(hroWorkingHourBillDto.getPaymentPlans(), PaymentPlan.class);
        // 新增的付款计划
        List<PaymentPlan> newPaymentPlans = paymentPlans.stream().filter(e -> e.getId() == null).collect(Collectors.toList());
        // 变更的付款计划
        List<PaymentPlan> updatePaymentPlans = paymentPlans.stream().filter(e -> e.getId() != null).collect(Collectors.toList());
        int num = 1;
        List<Long> ids = null;
        if (!updatePaymentPlans.isEmpty()) {
            ids = updatePaymentPlans.stream().map(PaymentPlan::getId).collect(Collectors.toList());
            for (PaymentPlan plan : updatePaymentPlans) {
                if (!Objects.equals(plan.getDeletedFlag(), Boolean.TRUE)) {
                    plan.setNum(num++);
                }
                paymentPlanMapper.updateByPrimaryKeySelective(plan);
            }
        }
        paymentPlanExtMapper.deleteExcludeIds(hroWorkingHourBill.getId(), ids);
        if (!newPaymentPlans.isEmpty()) {
            for (PaymentPlan plan : newPaymentPlans) {
                plan.setBillId(hroWorkingHourBillDto.getId());
                plan.setStatus(PaymentPlanStatus.UNPAYED.getCode());
                plan.setDeletedFlag(Boolean.FALSE);
                plan.setNum(num++);
            }
            paymentPlanExtMapper.batchInsert(newPaymentPlans);
        }
        return hroWorkingHourBillDto.getId();
    }

    @Override
    public void fallback(Long id) {
        HroWorkingHourBill hroWorkingHourBill = hroWorkingHourBillMapper.selectByPrimaryKey(id);
        if (hroWorkingHourBill == null) {
            throw new BizException(Code.ERROR, "对账单不存在");
        }
        if (!hroWorkingHourBill.getHandlerId().equals(SystemContext.getUserId())) {
            throw new BizException(Code.ERROR, "只有处理人允许退回");
        }
        if (hroWorkingHourBill.getStatus() != HroWorkingHourBillStatus.WAITING_HANDLE.getCode()) {
            throw new BizException(Code.ERROR, String.format("不能退回状态为【%s】的对账单",
                    HroWorkingHourBillStatus.getValue(hroWorkingHourBill.getStatus())));
        }
        hroWorkingHourBill.setStatus(HroWorkingHourBillStatus.DRAFT.getCode());
        hroWorkingHourBillMapper.updateByPrimaryKeySelective(hroWorkingHourBill);
    }

    @Override
    public void abandon(Long id) {
        HroWorkingHourBill hroWorkingHourBill = hroWorkingHourBillMapper.selectByPrimaryKey(id);
        if (hroWorkingHourBill == null) {
            throw new BizException(Code.ERROR, "对账单不存在");
        }
        if (!hroWorkingHourBill.getCreateBy().equals(SystemContext.getUserId())) {
            throw new BizException(Code.ERROR, "只有制单人允许作废");
        }
        if (hroWorkingHourBill.getStatus() != HroWorkingHourBillStatus.DRAFT.getCode()) {
            throw new BizException(Code.ERROR, String.format("不能作废状态为【%s】的对账单",
                    HroWorkingHourBillStatus.getValue(hroWorkingHourBill.getStatus())));
        }
        hroWorkingHourBill.setStatus(HroWorkingHourBillStatus.ABANDON.getCode());
        hroWorkingHourBillMapper.updateByPrimaryKeySelective(hroWorkingHourBill);
    }

    @Override
    public void draftSubmitCallback(String formInstanceId) {
        logger.info("点工对账单提交审批回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("点工对账单提交审批回调：formInstanceId为空，不处理");
            return;
        }
        HroWorkingHourBill hroWorkingHourBill = hroWorkingHourBillMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (hroWorkingHourBill == null) {
            logger.error("点工对账单提交审批回调：formInstanceId对应的点工对账单不存在，不处理");
            return;
        }
        hroWorkingHourBill.setStatus(HroWorkingHourBillStatus.APPROVAL.getCode());
        hroWorkingHourBillMapper.updateByPrimaryKeySelective(hroWorkingHourBill);
    }

    @Override
    public void refuseCallback(String formInstanceId) {
        logger.info("点工对账单审批驳回回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("点工对账单审批驳回回调：formInstanceId为空，不处理");
            return;
        }
        HroWorkingHourBill hroWorkingHourBill = hroWorkingHourBillMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (hroWorkingHourBill == null) {
            logger.error("点工对账单审批驳回回调：formInstanceId对应的点工对账单不存在，不处理");
            return;
        }
        hroWorkingHourBill.setStatus(HroWorkingHourBillStatus.WAITING_HANDLE.getCode());
        hroWorkingHourBillMapper.updateByPrimaryKeySelective(hroWorkingHourBill);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void passCallback(String formInstanceId, Long companyId) {
        logger.info("点工对账单审批通过回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("点工对账单审批通过回调：formInstanceId为空，不处理");
            return;
        }
        HroWorkingHourBill hroWorkingHourBill = hroWorkingHourBillMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (hroWorkingHourBill == null) {
            logger.error("点工对账单审批通过回调：formInstanceId对应的点工对账单不存在，不处理");
            return;
        }
        hroWorkingHourBill.setStatus(HroWorkingHourBillStatus.PASS.getCode());
        hroWorkingHourBill.setApprovalTime(new Date());
        hroWorkingHourBill.setGlDate(DateUtils.getShortDate(hroWorkingHourBill.getApprovalTime()));
        hroWorkingHourBill.setErpSyncStatus(HroWorkingHourBillSyncStatus.SYNCING.getCode());
        hroWorkingHourBillMapper.updateByPrimaryKeySelective(hroWorkingHourBill);
        PaymentPlanExample paymentPlanExample = new PaymentPlanExample();
        paymentPlanExample.createCriteria().andBillIdEqualTo(hroWorkingHourBill.getId())
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<PaymentPlan> paymentPlans = paymentPlanMapper.selectByExample(paymentPlanExample);
        String seqPrefix = basedataExtService.getUnitSeqPerfix(companyId);
        paymentPlans.forEach(e -> {
            e.setContractId(hroWorkingHourBill.getContractId());
            e.setCode(PurchaseContractHelper.generatePaymentPlanCode(seqPrefix));
            paymentPlanMapper.updateByPrimaryKeySelective(e);
        });
        try {
            HandleDispatcher.route(BusinessTypeEnums.PURCHASE_CONTRACT_BILL.getCode(),
                    hroWorkingHourBill.getId().toString(),
                    null, null, null);
        } catch (Exception e) {
            logger.error("对账单【{}】同步写入resend_execute表失败", hroWorkingHourBill.getCode(), e);
        }
    }

    @Override
    public void draftReturnCallback(String formInstanceId) {
        logger.info("点工对账单审批撤回回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("点工对账单审批撤回回调：formInstanceId为空，不处理");
            return;
        }
        HroWorkingHourBill hroWorkingHourBill = hroWorkingHourBillMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (hroWorkingHourBill == null) {
            logger.error("点工对账单审批撤回回调：formInstanceId对应的点工对账单不存在，不处理");
            return;
        }
        hroWorkingHourBill.setStatus(HroWorkingHourBillStatus.WAITING_HANDLE.getCode());
        hroWorkingHourBillMapper.updateByPrimaryKeySelective(hroWorkingHourBill);
    }

    @Override
    public void abandonCallback(String formInstanceId) {
        logger.info("点工对账单审批作废回调：formInstanceId:{}", formInstanceId);
        if (StringUtils.isEmpty(formInstanceId)) {
            logger.error("点工对账单审批作废回调：formInstanceId为空，不处理");
            return;
        }
        HroWorkingHourBill hroWorkingHourBill = hroWorkingHourBillMapper.selectByPrimaryKey(Long.valueOf(formInstanceId));
        if (hroWorkingHourBill == null) {
            logger.error("点工对账单审批作废回调：formInstanceId对应的点工对账单不存在，不处理");
            return;
        }
        hroWorkingHourBill.setStatus(HroWorkingHourBillStatus.ABANDON.getCode());
        hroWorkingHourBillMapper.updateByPrimaryKeySelective(hroWorkingHourBill);
    }

    @Override
    public List<HroWorkingHourItemDto> getBillWorkingHour(List<Long> billIds) {
        if (ListUtils.isEmpty(billIds)) {
            return Collections.emptyList();
        }
        List<HroWorkingHourItemDto> hroWorkingHourItemDtos = hroWorkingHourExtMapper.selectBillWorkingHour(billIds);
        if (hroWorkingHourItemDtos.isEmpty()) {
            return hroWorkingHourItemDtos;
        }
        hroWorkingHourItemDtos.forEach(e -> {
            e.setImportStatusDesc(HroWorkingHourStatus.getValue(e.getImportStatus()));
            e.setIsBill("是");
            e.setBillStatusDesc(HroWorkingHourBillStatus.getValue(e.getBillStatus()));
        });
        return hroWorkingHourItemDtos;
    }

    @Override
    public List<HroWorkingHourBillDto> paymentInvoiceQueryBill(Long contractId, String billCode, List<Long> paymentInvoiceDetailIds) {
        return hroWorkingHourBillExtMapper.paymentInvoiceQueryBill(contractId, billCode, paymentInvoiceDetailIds);
    }

    @Override
    public List<HroWorkingHourBillCostDetail> generateCollectionDetail(CostCollectionDto collectionDto) {

        MaterialOutsourcingContractConfigExample contractConfigExample = new MaterialOutsourcingContractConfigExample();
        contractConfigExample.createCriteria()
                .andOutsourcingContractTypeNameEqualTo("点工采购")
                .andCostCarryForwardMethodEqualTo("04")
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<Long> unitIds = materialOutsourcingContractConfigMapper.selectByExample(contractConfigExample)
                .stream().map(MaterialOutsourcingContractConfig::getUnitId).distinct().collect(Collectors.toList());
        if (unitIds.isEmpty()) {
            return Collections.emptyList();
        }

        HroWorkingHourBillExample billExample = new HroWorkingHourBillExample();
        billExample.createCriteria()
                .andProjectIdEqualTo(collectionDto.getProjectId())
                .andStatusEqualTo(HroWorkingHourBillStatus.PASS.getCode())
                .andUnitIdIn(unitIds)
                .andCollectionFlagEqualTo(Boolean.FALSE)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<HroWorkingHourBill> hroWorkingHourBills = hroWorkingHourBillMapper.selectByExample(billExample);
        if (hroWorkingHourBills.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> billIds = hroWorkingHourBills.stream().map(HroWorkingHourBill::getId).collect(Collectors.toList());
        hroWorkingHourBillExtMapper.updateCollectionFlagById(billIds);

        List<Long> contractIds = hroWorkingHourBills.stream().map(HroWorkingHourBill::getContractId).collect(Collectors.toList());

        PurchaseContractExample contractExample = new PurchaseContractExample();
        contractExample.createCriteria().andIdIn(contractIds);
        Map<Long, PurchaseContract> purchaseContractMap = purchaseContractMapper.selectByExample(contractExample).stream()
                .collect(Collectors.toMap(PurchaseContract::getId, Function.identity()));
        return hroWorkingHourBills.stream().map(e -> {
            PurchaseContract contract = purchaseContractMap.get(e.getContractId());
            HroWorkingHourBillCostDetail costDetail = new HroWorkingHourBillCostDetail();
            costDetail.setBillId(e.getId());
            costDetail.setStartDate(e.getStartDate());
            costDetail.setEndDate(e.getEndDate());
            costDetail.setVendorCode(contract.getVendorCode());
            costDetail.setVendorName(contract.getVendorName());
            costDetail.setVendorSiteCode(contract.getVendorSiteCode());
            costDetail.setContractCode(contract.getCode());
            costDetail.setContractName(contract.getName());
            costDetail.setCurrency(contract.getCurrency());
            costDetail.setBillCost(e.getAmount().divide(e.getTaxRate().add(BigDecimal.valueOf(100)).movePointLeft(2), 2, RoundingMode.HALF_UP));
            costDetail.setDeletedFlag(Boolean.FALSE);
            return costDetail;
        }).collect(Collectors.toList());
    }

    @Override
    public List<HroWorkingHourBillCostDetailDto> queryByCollection(Long collectionId) {
        HroWorkingHourBillCostDetailExample example = new HroWorkingHourBillCostDetailExample();
        example.createCriteria()
                .andCostCollectionIdEqualTo(collectionId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<HroWorkingHourBillCostDetail> billCostDetails = hroWorkingHourBillCostDetailMapper.selectByExample(example);
        return BeanConverter.copy(billCostDetails, HroWorkingHourBillCostDetailDto.class);
    }

    @Override
    public HroWorkingHourBillDto getEntryAccountInfo(Long id) {
        HroWorkingHourBill bill = hroWorkingHourBillMapper.selectByPrimaryKey(id);
        PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(bill.getContractId());
        HroWorkingHourBillDto billDto = BeanConverter.copy(bill, HroWorkingHourBillDto.class);
        // 不含税金额
        BigDecimal excludeTaxAmount = bill.getAmount().divide(
                BigDecimal.ONE.add(Optional.ofNullable(bill.getTaxRate()).orElse(BigDecimal.ZERO).movePointLeft(2)),
                2, RoundingMode.HALF_UP);
        billDto.setExcludeTaxAmount(excludeTaxAmount);
        billDto.setPurchaseContractDTO(BeanConverter.copy(contract, PurchaseContractDTO.class));
        billDto.setAccountingPeriod(DateUtils.format(billDto.getGlDate(), DateUtils.FORMAT_YEAR_MONTH));
        Date beginningOfDay = DateUtil.getBeginningOfDay(new Date());
        BusiSceneNonSaleExample busiSceneNonSaleExample = new BusiSceneNonSaleExample();
        busiSceneNonSaleExample.createCriteria()
                .andBusiSceneNameEqualTo("采购合同对账单入账")
                .andOuIdEqualTo(contract.getOuId())
                .andStartDateLessThanOrEqualTo(beginningOfDay)
                .andEndDateGreaterThanOrEqualTo(beginningOfDay)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        busiSceneNonSaleExample.or()
                .andBusiSceneNameEqualTo("采购合同对账单入账")
                .andOuIdEqualTo(contract.getOuId())
                .andStartDateLessThanOrEqualTo(beginningOfDay)
                .andEndDateIsNull()
                .andDeletedFlagEqualTo(Boolean.FALSE);

        List<BusiSceneNonSale> busiSceneNonSales = busiSceneNonSaleMapper.selectByExample(busiSceneNonSaleExample);

        if (!busiSceneNonSales.isEmpty()) {
            BusiSceneNonSale busiSceneNonSale = busiSceneNonSales.get(0);
            BusiSceneNonSaleDetailExample detailExample = new BusiSceneNonSaleDetailExample();
            detailExample.createCriteria()
                    .andBusiSceneNonSaleIdEqualTo(busiSceneNonSale.getId())
                    .andDeletedFlagEqualTo(Boolean.FALSE);

            Optional<BusiSceneNonSaleDetail> detailOptional = busiSceneNonSaleDetailMapper.selectByExample(detailExample)
                    .stream().filter(e -> CtcBusiSceneNsModule.GL.getCode().equals(e.getModule()) &&
                            CtcBusiSceneNsType.RJZ.getCode().equals(e.getType()))
                    .findFirst();
            if (detailOptional.isPresent()) {
                BusiSceneNonSaleDetail detail = detailOptional.get();
                HroBillEntryAccountInfoDto debitDto = new HroBillEntryAccountInfoDto();
                HroBillEntryAccountInfoDto creditDto = new HroBillEntryAccountInfoDto();
                billDto.setEntryAccountInfoDtoList(Arrays.asList(debitDto, creditDto));
                debitDto.setType("外部");
                creditDto.setType("外部");
                debitDto.setSide("借方");
                creditDto.setSide("贷方");
                debitDto.setSubject(detail.getAccountGroupDebit());
                creditDto.setSubject(detail.getAccountGroupCredit());
                List<String> flexValues = new ArrayList<>(2);
                if (debitDto.getSubject() != null) {
                    String[] split = debitDto.getSubject().split("\\.");
                    if (split.length >= 3) {
                        debitDto.setFlexValue(split[2]);
                        flexValues.add(debitDto.getFlexValue());
                    }
                }
                if (creditDto.getSubject() != null) {
                    String[] split = creditDto.getSubject().split("\\.");
                    if (split.length >= 3) {
                        creditDto.setFlexValue(split[2]);
                        flexValues.add(creditDto.getFlexValue());
                    }
                }
                if (!flexValues.isEmpty()) {
                    List<CoaSubjectDto> coaSubjects = getCoaSubjectByFlexValues(flexValues);
                    Map<String, CoaSubjectDto> coaSubjectMap = coaSubjects.stream().collect(Collectors.toMap(
                            CoaSubject::getFlexValue, Function.identity(), (e1, e2) -> e1));
                    CoaSubjectDto coaSubjectDto = coaSubjectMap.get(debitDto.getFlexValue());
                    debitDto.setSubjectDesc(coaSubjectDto != null ? coaSubjectDto.getDescription() : null);
                    coaSubjectDto = coaSubjectMap.get(creditDto.getFlexValue());
                    creditDto.setSubjectDesc(coaSubjectDto != null ? coaSubjectDto.getDescription() : null);
                }
                debitDto.setAmount(excludeTaxAmount);
                creditDto.setAmount(excludeTaxAmount);
            }
        }
        return billDto;
    }

    @Override
    public void purchaseContractBillCallBack(Long id, boolean isSuccess, String msg) {
        HroWorkingHourBill bill = hroWorkingHourBillMapper.selectByPrimaryKey(id);
        if (bill == null) {
            logger.error("id为{}的对账单不存在", id);
            return;
        }
        if (isSuccess) {
            bill.setErpSyncStatus(HroWorkingHourBillSyncStatus.SYNC_SUCCESS.getCode());
        } else {
            bill.setErpSyncStatus(HroWorkingHourBillSyncStatus.SYNC_FAIL.getCode());
        }
        bill.setErpSyncMsg(msg);
        hroWorkingHourBillMapper.updateByPrimaryKeySelective(bill);
    }

    @Override
    public void syncEntryAccountInfo(Long id) {
        HroWorkingHourBill bill = hroWorkingHourBillMapper.selectByPrimaryKey(id);
        if (bill == null) {
            throw new BizException(Code.ERROR, "对账单不存在");
        }
        HandleDispatcher.route(BusinessTypeEnums.PURCHASE_CONTRACT_BILL.getCode(),
                id.toString(), null, null, null);
        bill.setErpSyncStatus(HroWorkingHourBillSyncStatus.SYNCING.getCode());
        hroWorkingHourBillMapper.updateByPrimaryKeySelective(bill);
    }

    private List<CoaSubjectDto> getCoaSubjectByFlexValues(List<String> flexValues) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "coaSubject/getCoaSubjectByFlexValues";
        String res = restTemplate.postForObject(url, flexValues, String.class);
        DataResponse<List<CoaSubjectDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<CoaSubjectDto>>>() {
        });
        if (response != null && response.getCode() == 0) {
            return response.getData();
        }
        throw new BizException(Code.ERROR, "COA科目值集查询失败：" + res);
    }

    @Override
    public ResponseMap getMobileApprovalBillData(Long id) {
        if (id == null) {
            return null;
        }
        HroWorkingHourBillDto billDto = hroWorkingHourBillExtMapper.selectById(id);
        if (billDto == null) {
            throw new BizException(Code.ERROR, "对账单不存在");
        }
        Map<String, String> headMap = new HashMap<>(14);
        headMap.put("code", billDto.getCode());
        if (billDto.getStartDate() != null && billDto.getEndDate() != null) {
            headMap.put("billRange", DateUtils.formatDate(billDto.getStartDate()) + " — " + DateUtils.formatDate(billDto.getEndDate()));
        }
        headMap.put("handler", billDto.getHandler());
        headMap.put("creator", billDto.getCreator());
        headMap.put("contractCode", billDto.getContractCode());
        headMap.put("contractName", billDto.getContractName());
        headMap.put("projectCode", billDto.getProjectCode());
        headMap.put("projectName", billDto.getProjectName());
        headMap.put("remark", billDto.getRemark());
        headMap.put("billExplain", billDto.getBillExplain());
        headMap.put("currency", billDto.getCurrency());
        if (billDto.getTaxRate() != null) {
            headMap.put("taxRate", billDto.getTaxRate().stripTrailingZeros().toPlainString());
        }
        if (billDto.getAmount() != null) {
            headMap.put("amount", billDto.getAmount().stripTrailingZeros().toPlainString());
        }
        if (billDto.getAmount() != null) {
            headMap.put("amountNotTax", billDto.getAmount().divide(BigDecimal.ONE.add(billDto.getTaxRate()), 2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        }
        String attachmentIds = null;
        if (StringUtils.isNotEmpty(billDto.getBillAttachmentIds())) {
            attachmentIds = billDto.getBillAttachmentIds();
        }
        if (StringUtils.isNotEmpty(billDto.getAttachmentIds())) {
            if (attachmentIds != null) {
                attachmentIds += "," + billDto.getAttachmentIds();
            } else {
                attachmentIds = billDto.getAttachmentIds();
            }
        }
        headMap.put("attachmentIds", attachmentIds);

        PaymentPlanExample paymentPlanExample = new PaymentPlanExample();
        paymentPlanExample.createCriteria()
                .andBillIdEqualTo(id)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<PaymentPlan> paymentPlans = paymentPlanMapper.selectByExample(paymentPlanExample);
        List<Map<String, String>> paymentPlanMapList = new ArrayList<>(paymentPlans.size());
        int i = 1;
        for (PaymentPlan plan : paymentPlans) {
            Map<String, String> paymentPlan = new HashMap<>();
            paymentPlan.put("index", String.valueOf(i++));
            if (plan.getAmount() != null) {
                paymentPlan.put("amount", plan.getAmount().stripTrailingZeros().toPlainString());
            }
            paymentPlan.put("requirement", plan.getRequirement());
            paymentPlan.put("paymentMethodName", plan.getPaymentMethodName());
            if (plan.getDate() != null) {
                paymentPlan.put("date", DateUtils.formatDate(plan.getDate()));
            }
            if (plan.getProportion() != null) {
                paymentPlan.put("proportion", plan.getProportion().stripTrailingZeros().toPlainString() + "%");
            }
            paymentPlanMapList.add(paymentPlan);
        }
        ResponseMap responseMap = new ResponseMap();
        responseMap.setMsg("成功");
        responseMap.setStatus("success");
        responseMap.setHeadMap(headMap);
        responseMap.setList1(paymentPlanMapList);
        return responseMap;
    }

    @Override
    public EsbResponse manualSyncEntryAccountInfo(Long id, BigDecimal negTaxAmount, Date accountingDate) {
        HroWorkingHourBillDto billDto = getEntryAccountInfo(id);
        if (billDto == null) {
            throw new BizException(Code.ERROR, String.format("id为【%d】的点工工时对账单不存在", id));
        }
        List<CostAccountingErpDto> dtoList = formCostAccountingErpDto(billDto, negTaxAmount, accountingDate);
//        EsbResponse esbResponse = esbService.callCUXGLJOURNALSIMPORTAPIPKGPortType(dtoList);
        EsbResponse esbResponse = sdpService.callGERPGlJournalsImport(dtoList);
        if ("000000".equals(esbResponse.getResponsecode())) {
            ResendExecute resendExecute = new ResendExecute();
            resendExecute.setApplyNo(id.toString());
            resendExecute.setBusinessType(BusinessTypeEnums.PURCHASE_CONTRACT_BILL.getCode());
            resendExecute.setEsbSerialNo(esbResponse.getData().toString());
            resendExecute.setStatus(1);
            resendExecute.setResponCode(esbResponse.getResponsecode());
            resendExecute.setResponMsg(esbResponse.getResponsemessage());
            resendExecute.setBatch(Boolean.FALSE);
            resendExecute.setDeletedFlag(Boolean.FALSE);
            resendExecuteService.insert(resendExecute);
        }
        return esbResponse;
    }

    @Override
    public int updateGlDate(HroWorkingHourBillDto hroWorkingHourBillDto) {
        HroWorkingHourBill hroWorkingHourBill = hroWorkingHourBillMapper.selectByPrimaryKey(hroWorkingHourBillDto.getId());
        if (null == hroWorkingHourBill) {
            throw new BizException(Code.ERROR, "点工工时对账单不存在！");
        }
        if (!Objects.equals(hroWorkingHourBill.getErpSyncStatus(), HroWorkingHourBillSyncStatus.SYNC_FAIL.getCode())) {
            throw new BizException(Code.ERROR, "当前状态不可修改总账日期！");
        }
        hroWorkingHourBill.setGlDate(DateUtils.getShortDate(hroWorkingHourBillDto.getGlDate()));
        return hroWorkingHourBillMapper.updateByPrimaryKeySelective(hroWorkingHourBill);
    }

    private List<CostAccountingErpDto> formCostAccountingErpDto(HroWorkingHourBillDto billDto, BigDecimal negTaxAmount, Date accountingDate) {
        List<CostAccountingErpDto> dtoList = new ArrayList<>();
        PurchaseContractDTO contractDTO = billDto.getPurchaseContractDTO();
        int index = 1;
        List<HroBillEntryAccountInfoDto> entryAccountInfoDtoList = billDto.getEntryAccountInfoDtoList();
        if (ListUtils.isEmpty(entryAccountInfoDtoList)) {
            throw new BizException(Code.ERROR, "入账信息不存在");
        }
        for (HroBillEntryAccountInfoDto infoDto : entryAccountInfoDtoList) {
            CostAccountingErpDto dto = new CostAccountingErpDto();
            dtoList.add(dto);
            dto.setId(IdWorker.getId());
            dto.setLedgerId(new BigDecimal(this.getLedgerId(contractDTO.getOuId())));
            dto.setStatus("U");
            dto.setAccountingDate(DateUtils.formatDate(accountingDate));
            dto.setActualFlag("A");
            dto.setUserJesourceName("PAM");
            dto.setUserJeCategoryName(".记账凭证");
            dto.setCurrencyCode(contractDTO.getCurrency());
            dto.setBathName(billDto.getCode());
            dto.setJournalName("PAM_日记账导入_合同对账单入账");

            if (infoDto.getSubject() == null) {
                throw new BizException(Code.ERROR, "科目为空");
            }
            String[] segments = infoDto.getSubject().split("\\.");
            dto.setSegment1(segments.length >= 1 ? segments[0] : null);
            dto.setSegment2(segments.length >= 2 ? segments[1] : null);
            dto.setSegment3(segments.length >= 3 ? segments[2] : null);
            dto.setSegment4(segments.length >= 4 ? segments[3] : null);
            dto.setSegment5(segments.length >= 5 ? segments[4] : null);
            dto.setSegment6(segments.length >= 6 ? segments[5] : null);
            dto.setSegment7(segments.length >= 7 ? segments[6] : null);

            //外币汇率信息必填
            if (!"CNY".equals(contractDTO.getCurrency())) {
                dto.setUserCurrencyConversionType(contractDTO.getConversionType());
                dto.setCurrencyConversionDate(DateUtils.formatDate(contractDTO.getConversionDate()));
                dto.setCurrencyConversionRate(contractDTO.getConversionRate());
            }

            BigDecimal conversionRate = Optional.ofNullable(contractDTO.getConversionRate()).orElse(BigDecimal.ONE);
            if ("借方".equals(infoDto.getSide())) {
                dto.setEnterEddr(negTaxAmount);
                dto.setAccountEddr(negTaxAmount.multiply(conversionRate));
            } else {
                dto.setEnterEdcr(negTaxAmount);
                dto.setAccountEdcr(negTaxAmount.multiply(conversionRate));
            }

            dto.setReference5("PAM_日记账导入_合同对账单入账_" + billDto.getCode());
            dto.setReference10("合同对账单入账_" + billDto.getCode());

            dto.setLineNumber(BigDecimal.valueOf(index++));
            dto.setSourcecode("PAM");
            dto.setSourceNum(billDto.getCode());

            dto.setEsbDataSource(BusinessTypeEnums.PURCHASE_CONTRACT_BILL.getCode());
            dto.setOperationType("CREATE");
        }
        return dtoList;
    }

    /**
     * 根据ouId查找ledgerId
     *
     * @param ouId 业务实体id
     */
    private Long getLedgerId(Long ouId) {
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setPamEnabled(0);
        query.setOperatingUnitId(ouId);
        PageInfo<OrganizationRelDto> orgDtoPage = organizationRelExtService.invokeApiList(query);
        return orgDtoPage.getList().get(0).getLedgerId();
    }
}
