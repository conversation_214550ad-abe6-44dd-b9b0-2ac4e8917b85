package com.midea.pam.ctc.project.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.midea.esb.RequestHeader;
import com.midea.gems.gemsadjustapplyservice.v1.CreateAdjustApply;
import com.midea.gems.gemsadjustapplyservice.v1.CreateAdjustApplyResponse;
import com.midea.gems.gemsadjustapplyservice.v1.GemsAdjustApplyService;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangePushEms;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.esb.util.EsbUtil;
import com.midea.pam.ctc.esb.util.EsbCtcUtil;
import com.midea.pam.ctc.ext.service.impl.AbstractCommonBusinessService;
import com.midea.pam.ctc.project.service.ProjectBudgetChangePushEmsService;
import com.midea.pam.ctc.service.GEMSCarrierServicel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-7-12
 * @description
 */
public class ProjectBudgetChangeSyncServiceImpl extends AbstractCommonBusinessService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectBudgetChangePushEmsService projectBudgetChangePushEmsService;
    @Resource
    private GemsAdjustApplyService gemsAdjustApplyService;
    @Resource
    private GEMSCarrierServicel gemsCarrierService;


    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        logger.info("------------------- 预算变更ESB推送开始 -------------------");
        logger.info("待推送信息：{}", JSONObject.toJSONString(resendExecute));
        String serialNo = null;

        final String applyNo = resendExecute.getApplyNo();
        Assert.notNull(applyNo, "applyNo不能为空");

        // 变更ID
        final Long id = Long.valueOf(applyNo);

        final ProjectBudgetChangePushEms projectBudgetChangePushEms = projectBudgetChangePushEmsService.selectByPrimaryKey(id);
        Assert.notNull(projectBudgetChangePushEms, "预算变更推送信息project_budget_change_push_ems不存在");

//        serialNo = syncChangeBudgetToEms(projectBudgetChangePushEms);
        EsbResponse response = gemsCarrierService.createAdjustApply(projectBudgetChangePushEms);

        logger.info("------------------- 预算变更ESB推送结束 -------------------");
        return response;
    }

    private String syncChangeBudgetToEms(ProjectBudgetChangePushEms projectBudgetChangePushEms) {
        String serialNo = null;

        //final Long headerId = projectBudgetChangePushEms.getHeaderId();
        final Date applyDate = projectBudgetChangePushEms.getApplyDate();
        final String applyReason = projectBudgetChangePushEms.getApplyReason();
        final String applyUser = projectBudgetChangePushEms.getApplyUser();
        final BigDecimal changeAmount = projectBudgetChangePushEms.getChangeAmount();
        final Long budgetNodeId = projectBudgetChangePushEms.getBudgetNodeId();

        if (budgetNodeId != null) {
            serialNo = syncChangeBudgetToEms(projectBudgetChangePushEms.getId(), applyReason, applyUser, changeAmount, budgetNodeId, applyDate);
        } else {
            logger.info("预算id不存在，不同步");
        }

        return serialNo;
    }

    private String syncChangeBudgetToEms(Long projectBudgetChangePushEmsId, String reason, String username, BigDecimal changeAmount, Long budgetNodeId, Date applyDate) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();

        final CreateAdjustApply.EmsIoBmAdjustApplyH emsIoBmAdjustApplyH =
                buildEmsIoBmAdjustApplyH(projectBudgetChangePushEmsId, reason, username, changeAmount, budgetNodeId, applyDate);

        logger.info("推送预算信息：{}", JSONObject.toJSONString(emsIoBmAdjustApplyH));
        final CreateAdjustApplyResponse.CreateResult adjustApply = gemsAdjustApplyService.createAdjustApply(emsIoBmAdjustApplyH, headerRequest);
        logger.info("推送预算结果：{}", JSONObject.toJSONString(adjustApply));
        return headerRequest.getSerialNo();
    }

    private CreateAdjustApply.EmsIoBmAdjustApplyH buildEmsIoBmAdjustApplyH(Long projectBudgetChangePushEmsId, String reason, String username, BigDecimal changeAmount, Long budgetNodeId, Date applyDate) {
        String adjustApplyCode = "PAM_BAD" + String.valueOf(projectBudgetChangePushEmsId);

        CreateAdjustApply.EmsIoBmAdjustApplyH emsIoBmAdjustApplyH = new CreateAdjustApply.EmsIoBmAdjustApplyH();
        emsIoBmAdjustApplyH.setAdjustApplyCode(adjustApplyCode);
        emsIoBmAdjustApplyH.setApplyByCode(username);
        emsIoBmAdjustApplyH.setApplyDate(EsbUtil.formatXMLGreDate(applyDate));
        emsIoBmAdjustApplyH.setReasonDesc(reason);
        emsIoBmAdjustApplyH.setSourceSystem("PAM");
        emsIoBmAdjustApplyH.setSourceOrderType("BM");
        emsIoBmAdjustApplyH.setSourceOrderId(String.valueOf(projectBudgetChangePushEmsId));
        emsIoBmAdjustApplyH.setSourceOrderCode(String.valueOf(projectBudgetChangePushEmsId));
//        emsIoBmAdjustApplyH.setSourceOrderUrl("TEST");
        emsIoBmAdjustApplyH.setCreatedByCode(username);
        emsIoBmAdjustApplyH.setCreationDate(EsbUtil.formatXMLGreDate(applyDate));
//		emsIoBmAdjustApplyH.setLastUpdatedCode();
//		emsIoBmAdjustApplyH.setLastUpdateDate();
        emsIoBmAdjustApplyH.setBmAdjustType("BESIDE_BUDGET");
        emsIoBmAdjustApplyH.setIsDraft("N");
        CreateAdjustApply.EmsIoBmAdjustApplyH.EmsIoBmAdjustApplyL emsIoBmAdjustApplyL = new CreateAdjustApply.EmsIoBmAdjustApplyH.EmsIoBmAdjustApplyL();
        emsIoBmAdjustApplyL.setAdjustAmount(changeAmount);
        emsIoBmAdjustApplyL.setBudgetNodeId(budgetNodeId);
        emsIoBmAdjustApplyL.setRemark(reason);
//        emsIoBmAdjustApplyL.setSourceOrderLineId("pam344104");
//        emsIoBmAdjustApplyL.setSourceOrderId("pam344103");
//        emsIoBmAdjustApplyL.setSourceOrderCode("pam344103");
//        emsIoBmAdjustApplyL.setSourceSystem("PAM");
        emsIoBmAdjustApplyH.getEmsIoBmAdjustApplyL().add(emsIoBmAdjustApplyL);

        return emsIoBmAdjustApplyH;
    }

}
