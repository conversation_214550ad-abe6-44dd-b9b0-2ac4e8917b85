package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.CoaSubject;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDetailDto;
import com.midea.pam.common.ctc.dto.CostAccountingErpDto;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.dto.GlDailyAccountingErpDto;
import com.midea.pam.common.ctc.dto.LaborCostDetailDto;
import com.midea.pam.common.ctc.dto.WorkingHourAccountingDetailDto;
import com.midea.pam.common.ctc.dto.WorkingHourAccountingDto;
import com.midea.pam.common.ctc.entity.AgencySynInfo;
import com.midea.pam.common.ctc.entity.AgencySynInfoExample;
import com.midea.pam.common.ctc.entity.AsyncCostCollectionResult;
import com.midea.pam.common.ctc.entity.BusiSceneNonSale;
import com.midea.pam.common.ctc.entity.BusiSceneNonSaleExample;
import com.midea.pam.common.ctc.entity.CarryoverBill;
import com.midea.pam.common.ctc.entity.CarryoverBillCostCollectionRel;
import com.midea.pam.common.ctc.entity.CarryoverBillCostCollectionRelExample;
import com.midea.pam.common.ctc.entity.CarryoverBillExample;
import com.midea.pam.common.ctc.entity.CostCollection;
import com.midea.pam.common.ctc.entity.CostCollectionExample;
import com.midea.pam.common.ctc.entity.LaborCostDetail;
import com.midea.pam.common.ctc.entity.LaborCostDetailExample;
import com.midea.pam.common.ctc.entity.LaborCostDetailWorkingHourAccountingRes;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.OrganizationCustomDictExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.entity.WorkingHourAccounting;
import com.midea.pam.common.ctc.entity.WorkingHourAccountingDetail;
import com.midea.pam.common.ctc.entity.WorkingHourAccountingDetailExample;
import com.midea.pam.common.ctc.entity.WorkingHourAccountingDetailSubject;
import com.midea.pam.common.ctc.entity.WorkingHourAccountingDetailSubjectExample;
import com.midea.pam.common.ctc.entity.WorkingHourAccountingExample;
import com.midea.pam.common.ctc.entity.WorkingHourWriteoffLaborcostRel;
import com.midea.pam.common.ctc.entity.WorkingHourWriteoffLaborcostRelExample;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.CarryoverBillReverseStatus;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.SubBusinessTypeEnums;
import com.midea.pam.common.enums.UserType;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CommonErpStatus;
import com.midea.pam.ctc.common.enums.WorkingHourAccountingEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.AgencySynInfoMapper;
import com.midea.pam.ctc.mapper.AsyncCostCollectionResultMapper;
import com.midea.pam.ctc.mapper.BusiSceneNonSaleMapper;
import com.midea.pam.ctc.mapper.CarryoverBillCostCollectionRelMapper;
import com.midea.pam.ctc.mapper.CarryoverBillMapper;
import com.midea.pam.ctc.mapper.CostCollectionExtMapper;
import com.midea.pam.ctc.mapper.CostCollectionMapper;
import com.midea.pam.ctc.mapper.LaborCostDetailExtMapper;
import com.midea.pam.ctc.mapper.LaborCostDetailMapper;
import com.midea.pam.ctc.mapper.LaborCostDetailWorkingHourAccountingResMapper;
import com.midea.pam.ctc.mapper.OrganizationCustomDictMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.WorkingHourAccountingDetailMapper;
import com.midea.pam.ctc.mapper.WorkingHourAccountingDetailSubjectMapper;
import com.midea.pam.ctc.mapper.WorkingHourAccountingExtMapper;
import com.midea.pam.ctc.mapper.WorkingHourAccountingMapper;
import com.midea.pam.ctc.mapper.WorkingHourMapper;
import com.midea.pam.ctc.mapper.WorkingHourWriteoffLaborcostRelMapper;
import com.midea.pam.ctc.service.AsyncCostCollectionResultService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.BusiSceneNonSaleService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.OrganizationRelExtService;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.ctc.service.WorkingHourAccountingService;
import com.midea.pam.ctc.service.event.CostCollectionResultEvent;
import com.midea.pam.system.SystemContext;
import io.netty.util.internal.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class WorkingHourAccountingServiceImpl implements WorkingHourAccountingService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private final static Long MAXWAITTIME = (long) 1000 * 60 * 8;//8分钟

    @Resource
    CostCollectionExtMapper costCollectionExtMapper;
    @Resource
    private CostCollectionMapper costCollectionMapper;
    @Resource
    private WorkingHourAccountingMapper workingHourAccountingMapper;
    @Resource
    private LaborCostDetailMapper laborCostDetailMapper;
    @Resource
    private WorkingHourMapper workingHourMapper;
    @Resource
    WorkingHourAccountingMapper accountingMapper;
    @Resource
    LaborCostDetailWorkingHourAccountingResMapper laborWorkingResMapper;
    @Resource
    WorkingHourAccountingDetailMapper accountingDetailMapper;
    @Resource
    WorkingHourAccountingExtMapper accountingExtMapper;
    @Resource
    private BusiSceneNonSaleMapper busiSceneNonSaleMapper;
    @Resource
    BusiSceneNonSaleService busiSceneNonSaleService;
    @Resource
    private EsbService esbService;
    @Resource
    OrganizationRelExtService organizationRelExtService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private WorkingHourAccountingDetailSubjectMapper workingHourAccountingDetailSubjectMapper;
    @Resource
    private CarryoverBillCostCollectionRelMapper costCollectionRelMapper;
    @Resource
    private CarryoverBillMapper carryoverBillMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private WorkingHourWriteoffLaborcostRelMapper writeoffLaborcostRelMapper;
    @Resource
    private AgencySynInfoMapper agencySynInfoMapper;
    @Resource
    private AsyncCostCollectionResultService asyncCostCollectionResultService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private AsyncCostCollectionResultMapper asyncCostCollectionResultMapper;
    @Resource
    private OrganizationCustomDictMapper organizationCustomDictMapper;
    @Resource
    private LaborCostDetailExtMapper laborCostDetailExtMapper;
    @Resource
    private SdpService sdpService;


    /**
     * 成本工时入账单头表保存方法
     *
     * @param accountingDto
     */
    private void save(WorkingHourAccountingDto accountingDto) {
        WorkingHourAccounting accounting = new WorkingHourAccounting();
        BeanUtils.copyProperties(accountingDto, accounting);
        if (ObjectUtils.isEmpty(accounting.getId())) {
            accounting.setErpStatus(CommonErpStatus.NOT_PUSH.code());
            accounting.setDeletedFlag(Boolean.FALSE);
            accounting.setStatus(0);
            // 冲销标识设为: 未冲销
            accounting.setWriteOffStatus(1);
            accountingMapper.insert(accounting);

        } else {
            accountingMapper.updateByPrimaryKeySelective(accounting);
        }
        accountingDto.setId(accounting.getId());
    }

    /**
     * 统计
     *
     * @param query
     * @param excludedList
     * @return
     */
    @Override
    public WorkingHourAccountingDto batchSummary(CostCollectionDto query, List<CostCollectionDto> excludedList) {
        List<CostCollectionDto> costCollectionDtoList = this.queryCollectionSummary(query);
        Assert.notEmpty(costCollectionDtoList, "无符合查询条件数据");
        //从查询的结果列表清除excludedList(排除列)
        if (ListUtils.isNotEmpty(excludedList)) {
            List<Long> excludedProjectIds = ListUtil.map(excludedList, "projectId");
            for (Long projectId : excludedProjectIds) {
                Predicate<CostCollectionDto> predicate = collectionDto -> collectionDto.getProjectId().equals(projectId);
                costCollectionDtoList.removeIf(predicate);
            }
        }

        BigDecimal totalLaborCost = BigDecimal.ZERO;
        Long firstDtoOuId = null;
        String ouName = null;
        //统计所工时成本和项目数
        for (CostCollectionDto dto : costCollectionDtoList) {
            totalLaborCost = BigDecimalUtils.add(totalLaborCost, dto.getInnerLaborCost() == null ? BigDecimal.ZERO : dto.getInnerLaborCost());
            totalLaborCost = BigDecimalUtils.add(totalLaborCost, dto.getOuterLaborCost() == null ? BigDecimal.ZERO : dto.getOuterLaborCost());
            if (ObjectUtils.isEmpty(firstDtoOuId)) firstDtoOuId = dto.getOuId();
            if (ObjectUtils.isEmpty(ouName)) ouName = dto.getOuName();
            //检查是否存在不同ou
            if (!ObjectUtils.isEmpty(firstDtoOuId)) {
                if (!firstDtoOuId.equals(dto.getOuId())) {
                    throw new BizException(Code.ERROR, "选择范围中存在多个业务实体的数据，请重新选择!");
                }
            }
        }
        //返回结果对象
        WorkingHourAccountingDto resultDto = new WorkingHourAccountingDto();
        resultDto.setCostCollectionDtoList(costCollectionDtoList);
        resultDto.setTotalLaborCost(totalLaborCost);
        resultDto.setProjectCount(costCollectionDtoList.size());
        resultDto.setOuId(firstDtoOuId);
        resultDto.setOuName(ouName);
        return resultDto;
    }

    /**
     * 查找未入账工单(ouId+glPeriod)
     *
     * @param ouId
     * @param glPeriod
     * @return
     */
    private WorkingHourAccountingDto getNonAccountingRecord(Long ouId, String glPeriod) {
        WorkingHourAccountingDto existAccountingDto = null;
        //查询工单是否存在
        WorkingHourAccountingDto query = new WorkingHourAccountingDto();
        query.setGlPeriod(glPeriod);
        query.setOuId(ouId);
        WorkingHourAccountingExample example = this.buildExample(query);
        example.getOredCriteria().get(0).andErpStatusEqualTo(CommonErpStatus.NOT_PUSH.code());//未同步
        List<WorkingHourAccounting> accountingList = accountingMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(accountingList)) {
            existAccountingDto = new WorkingHourAccountingDto();
            BeanUtils.copyProperties(accountingList.get(0), existAccountingDto);
        }
        return existAccountingDto;
    }

    /**
     * 生成借贷方科目
     *
     * @param collectionDto
     * @return
     */
    public List<WorkingHourAccountingDetailSubject> getWorkingHourAccountingDetailSubject(CostCollectionDto collectionDto) {
        List<WorkingHourAccountingDetailSubject> list = new ArrayList<>();
        for (int i = 2; i >= 1; i--) {
            WorkingHourAccountingDetailSubject workingHourAccountingDetailSubject = new WorkingHourAccountingDetailSubject();
            BeanUtils.copyProperties(collectionDto, workingHourAccountingDetailSubject);
            workingHourAccountingDetailSubject.setId(null);
            workingHourAccountingDetailSubject.setDeletedFlag(Boolean.FALSE);
            if (i == 2) {
                workingHourAccountingDetailSubject.setDebitCredit(WorkingHourAccountingEnum.DEBIT.getCode());
                workingHourAccountingDetailSubject.setWorkingHour(BigDecimalUtils.add(collectionDto.getInnerWorkingHour(), collectionDto.getOuterWorkingHour()));
                workingHourAccountingDetailSubject.setLaborCost(BigDecimalUtils.add(collectionDto.getInnerLaborCost(), collectionDto.getOuterLaborCost()));
                workingHourAccountingDetailSubject.setLaborCostType("GS_A4");
            } else {
                workingHourAccountingDetailSubject.setDebitCredit(WorkingHourAccountingEnum.CREDIT.getCode());
            }
            list.add(workingHourAccountingDetailSubject);
        }
        return list;
    }

    /**
     * 把头部信息拆分4行内---借方贷方 外-----借方贷方 4条语句
     *
     * @param collectionDto
     * @param type
     * @return
     */
    public List<WorkingHourAccountingDetailSubject> getWorkingHourAccountingDetail(CostCollectionDto collectionDto, String type) {
        List<WorkingHourAccountingDetailSubject> list = new ArrayList<>();
        for (int i = 2; i >= 1; i--) {
            WorkingHourAccountingDetailSubject workingHourAccountingDetailSubject = new WorkingHourAccountingDetailSubject();
            BeanUtils.copyProperties(collectionDto, workingHourAccountingDetailSubject);
            workingHourAccountingDetailSubject.setId(null);
            workingHourAccountingDetailSubject.setDeletedFlag(Boolean.FALSE);
            if ("GS_A4".equals(type)) {
                workingHourAccountingDetailSubject.setWorkingHour(collectionDto.getInnerWorkingHour() == null ? BigDecimal.ZERO : collectionDto.getInnerWorkingHour());
                workingHourAccountingDetailSubject.setLaborCost(collectionDto.getInnerLaborCost() == null ? BigDecimal.ZERO : collectionDto.getInnerLaborCost());
                workingHourAccountingDetailSubject.setLaborCostType("GS_A4"); //GS_A4 是内部
            } else {
                workingHourAccountingDetailSubject.setWorkingHour(collectionDto.getOuterWorkingHour() == null ? BigDecimal.ZERO : collectionDto.getOuterWorkingHour());
                workingHourAccountingDetailSubject.setLaborCost(collectionDto.getOuterLaborCost() == null ? BigDecimal.ZERO : collectionDto.getOuterLaborCost());
                workingHourAccountingDetailSubject.setLaborCostType("GS_A5"); //GS_A5 是外部
            }
            if (i == 2) {
                workingHourAccountingDetailSubject.setDebitCredit(WorkingHourAccountingEnum.DEBIT.getCode().toString());
            } else {
                workingHourAccountingDetailSubject.setDebitCredit(WorkingHourAccountingEnum.CREDIT.getCode().toString());
            }

            list.add(workingHourAccountingDetailSubject);
        }
        return list;
    }

    /**
     * 批量入账
     *
     * @param costCollectionDtoList
     * @param glDate
     */
    @Override
    public AsyncCostCollectionResult batchSave(List<CostCollectionDto> costCollectionDtoList, Date glDate, Long unitId) {
        Assert.notEmpty(costCollectionDtoList, "无选中统计数据");

        // 并发时候的key值
        String lockKey = Constants.DistributedLockKey.DISTRIBUTED_LOCK_ACCOUNTING + unitId;
        // 创建入账批次号
        String accountingBatchNum = CacheDataUtils.generateSequence(Constants.CODE_PAM_CODE_LENGTH, CodePrefix.ACCOUNTING_BATCH_NUM.code());
        AsyncCostCollectionResult asyncCostCollectionResult = new AsyncCostCollectionResult();
        asyncCostCollectionResult.setAccountingBatchNum(accountingBatchNum);
        asyncCostCollectionResultService.add(asyncCostCollectionResult);
        try {
            if (DistributedCASLock.lock(lockKey, accountingBatchNum, MAXWAITTIME, MAXWAITTIME * 2)) {
                logger.info("---开始批量入账，入账批次号为:{}", accountingBatchNum);
                //先把归集表的数据改为"入账中=1"状态
                int count = this.updateToDoLaborCostDetail(accountingBatchNum, costCollectionDtoList);
                if (count > 0) {
                    applicationEventPublisher.publishEvent(new CostCollectionResultEvent(this, asyncCostCollectionResult.getId(), asyncCostCollectionResult.getAccountingBatchNum(), costCollectionDtoList, glDate, unitId));
                } else {
                    asyncCostCollectionResult.setResponseCode(ErrorCode.SUCCESS.getCode());
                    asyncCostCollectionResult.setResponseMsg(ErrorCode.SUCCESS.getMsg());
                    asyncCostCollectionResult.setResponseData(null);
                    asyncCostCollectionResult.setApplyMonth(DateUtils.format(glDate, "yyyy-MM"));
                    asyncCostCollectionResultMapper.updateByPrimaryKeySelective(asyncCostCollectionResult);
                }
            }
        } catch (Exception e) {
            logger.info("---批量入账{}，异步处理异常:{}", accountingBatchNum, e.getMessage());
            asyncCostCollectionResult.setResponseCode(ErrorCode.ERROR.getCode());
            asyncCostCollectionResult.setResponseMsg(ErrorCode.ERROR.getMsg());
            asyncCostCollectionResult.setResponseData(e.getMessage());
            asyncCostCollectionResultMapper.updateByPrimaryKeySelective(asyncCostCollectionResult);
        } finally {
            DistributedCASLock.unLock(lockKey, accountingBatchNum);
            logger.info("---批量入账结束，入账批次号为:{}", accountingBatchNum);
        }
        return asyncCostCollectionResult;
    }

    /**
     * 单条头记录保存
     *
     * @param costCollectionDtoList
     * @param glDate
     */
    private void save(List<CostCollectionDto> costCollectionDtoList, String accountingBatchNum,
                      Date glDate, String approveMonth, String applyMonth, Long unitId) {
        //根据选择行的数据进行统计
        List<WorkingHourAccountingDetail> detailList = new ArrayList<>();
        BigDecimal totalWorkingHour = BigDecimal.ZERO;
        BigDecimal totalLaborCost = BigDecimal.ZERO;
        BigDecimal innerWorkingHour = BigDecimal.ZERO;
        BigDecimal innerLaborCost = BigDecimal.ZERO;
        BigDecimal outerWorkingHour = BigDecimal.ZERO;
        BigDecimal outerLaborCost = BigDecimal.ZERO;
        for (CostCollectionDto collectionDto : costCollectionDtoList) {
            WorkingHourAccountingDetail detail = new WorkingHourAccountingDetail();
            BeanUtils.copyProperties(collectionDto, detail);
            detail.setId(null);
            detail.setDeletedFlag(Boolean.FALSE);
            //工时、费用统计
            totalWorkingHour = BigDecimalUtils.add(totalWorkingHour, detail.getInnerWorkingHour());
            totalWorkingHour = BigDecimalUtils.add(totalWorkingHour, detail.getOuterWorkingHour());
            totalLaborCost = BigDecimalUtils.add(totalLaborCost, detail.getInnerLaborCost());
            totalLaborCost = BigDecimalUtils.add(totalLaborCost, detail.getOuterLaborCost());
            //内外来统计工时与人工成本费用
            innerWorkingHour = BigDecimalUtils.add(innerWorkingHour, detail.getInnerWorkingHour());
            outerWorkingHour = BigDecimalUtils.add(outerWorkingHour, detail.getOuterWorkingHour());
            innerLaborCost = BigDecimalUtils.add(innerLaborCost, detail.getInnerLaborCost());
            outerLaborCost = BigDecimalUtils.add(outerLaborCost, detail.getOuterLaborCost());

            detail.setApplyMonth(applyMonth);
            detailList.add(detail);
        }
        /**
         * 构建WorkingHourAccountingDetailSubject里面的数据
         */
        CostCollectionDto costCollectionDto = new CostCollectionDto();
        costCollectionDto.setInnerLaborCost(innerLaborCost);
        costCollectionDto.setInnerWorkingHour(innerWorkingHour);
        costCollectionDto.setOuterWorkingHour(outerWorkingHour);
        costCollectionDto.setOuterLaborCost(outerLaborCost);
        List<WorkingHourAccountingDetailSubject> detailSubjectsList = this.getWorkingHourAccountingDetailSubject(costCollectionDto);

        //保存数据
        if (ListUtils.isNotEmpty(detailList)) {
            String glPeriod = DateUtils.getYearMonth(glDate);
            WorkingHourAccountingDto dto = new WorkingHourAccountingDto();
            String seqPerfix = basedataExtService.getUnitSeqPerfix(unitId);
            dto.setCode(seqPerfix + "GS" + CacheDataUtils.generateSequence(3, seqPerfix + "GS", DateUtil.DATE_YYMMDD_PATTERN));
            dto.setDailyBatchNum(StringUtils.replace(dto.getCode(), "GS", "PAM"));
            dto.setOuId(costCollectionDtoList.get(0).getOuId());
            dto.setOuName(costCollectionDtoList.get(0).getOuName());
            dto.setCurrency("CNY");
            dto.setGlDate(glDate);
            dto.setGlPeriod(glPeriod);
            dto.setErpStatus(CommonErpStatus.NOT_PUSH.code());//未同步
            dto.setApplyMonth(applyMonth);
            dto.setApproveMonth(approveMonth);
            this.formatSceneDetail(dto);

            //累计工时
            totalWorkingHour = totalWorkingHour.add(dto.getTotalWorkingHour() == null ? BigDecimal.ZERO : dto.getTotalWorkingHour());
            dto.setTotalWorkingHour(totalWorkingHour);
            //累计成本
            totalLaborCost = totalLaborCost.add(dto.getTotalLaborCost() == null ? BigDecimal.ZERO : dto.getTotalLaborCost());
            dto.setTotalLaborCost(totalLaborCost);
            //保存头数
            this.save(dto);
            //保存行数据
            for (WorkingHourAccountingDetail detail : detailList) {
                detail.setWorkingHourAccountingId(dto.getId());
                this.saveDetail(detail);
            }

            //人工成本明细写入账单id，防止重复入账
            for (CostCollectionDto collectionDto : costCollectionDtoList) {
                LaborCostDetailDto laborCostDetailDto = new LaborCostDetailDto();
                laborCostDetailDto.setAccountingBatchNum(accountingBatchNum);
                laborCostDetailDto.setProjectId(collectionDto.getProjectId());
                laborCostDetailDto.setWorkingHourAccountingId(dto.getId());
                laborCostDetailDto.setApplyMonth(applyMonth);
                laborCostDetailDto.setApproveMonth(approveMonth);
                costCollectionExtMapper.updateLaborCostDetailByProject(laborCostDetailDto);
            }

            /**
             * 往表WorkingHourAccountingDetailSubject里面填写数据
             */
            //查询组织参数的 OU_CODE
            OrganizationCustomDictExample organizationCustomDictExample = new OrganizationCustomDictExample();
            organizationCustomDictExample.createCriteria().andNameEqualTo("OU_CODE").andOrgIdEqualTo(costCollectionDtoList.get(0).getOuId()).andDeletedFlagEqualTo(false);
            List<OrganizationCustomDict> organizationCustomDicts = organizationCustomDictMapper.selectByExample(organizationCustomDictExample);
            String value = null;
            if (ListUtils.isNotEmpty(organizationCustomDicts)) {
                value = organizationCustomDicts.get(0).getValue();
            }
            //借方科目(固定，需要替换第一段)
            String[] debitSubjectArray = Constants.SUBJECT.split("\\.");
            if (StringUtils.isNotEmpty(value)) {
                debitSubjectArray[0] = value;
            }
            String debitSubject = ArrayUtils.toString(debitSubjectArray);
            debitSubject = debitSubject.substring(1, debitSubject.length() - 1);//去除{}
            debitSubject = debitSubject.replace(",", ".");//逗号替换回点号

            List<LaborCostDetail> details = laborCostDetailExtMapper.selectByWorkingHourAccountingId(dto.getId()); //查询人工成本明细

            logger.info("往working_hour_accounting_detail_subject表插入数据");
            for (WorkingHourAccountingDetailSubject detailSubject : detailSubjectsList) {
                if (detailSubject.getDebitCredit().equals(WorkingHourAccountingEnum.DEBIT.getCode())) {
                    detailSubject.setSubject(debitSubject); // 借方
                    if (null != detailSubject.getSubject()) {
                        String[] flexValueSetIds = detailSubject.getSubject().split("\\.");
                        CoaSubject coaSubject = basedataExtService.getCoaSubject(flexValueSetIds[2]);
                        if (!ObjectUtils.isEmpty(coaSubject)) {
                            detailSubject.setSubjectDescribe(coaSubject.getDescription());
                        }
                    }
                    detailSubject.setWorkingHourAccountingId(dto.getId());
                    workingHourAccountingDetailSubjectMapper.insert(detailSubject);
                } else {
                    for (LaborCostDetail laborCostDetail : details) {
                        if (UserType.INTERNAL.code().equals(laborCostDetail.getUserType()) || UserType.RECRUIT.code().equals(laborCostDetail.getUserType())) {
                            detailSubject.setId(null);
                            String[] creditSubjectArray = null;
                            if (StringUtils.isNotEmpty(laborCostDetail.getHardWorking())) {
                                creditSubjectArray = laborCostDetail.getHardWorking().split("\\.");
                            } else {
                                throw new BizException(ErrorCode.CTC_SUBJECT_NOT_NULL);
                            }

                            String creditSubject0 = ArrayUtils.toString(creditSubjectArray[0]); //第一段
                            String creditSubject1 = ArrayUtils.toString(creditSubjectArray[1]); //第二段
                            if (!Objects.equals("0", creditSubject1) && creditSubject1.length() == 11 && creditSubject0.length() == 6) {
                                creditSubject1 = creditSubject1.substring(creditSubject0.length());//截取数据
                                StringBuilder str = new StringBuilder();
                                String s = str.append(value).append(creditSubject1).toString();
                                creditSubjectArray[1] = s; //替换第二段
                            }

                            if (StringUtils.isNotEmpty(value)) { //替换第一段
                                creditSubjectArray[0] = value;
                            }
                            String fianlCreditSubject = ArrayUtils.toString(creditSubjectArray);
                            fianlCreditSubject = fianlCreditSubject.substring(1, fianlCreditSubject.length() - 1);//去除{}
                            fianlCreditSubject = fianlCreditSubject.replace(",", ".");//逗号替换回点号

                            detailSubject.setSubject(fianlCreditSubject); //贷方
                            detailSubject.setLaborCost(laborCostDetail.getCostTotal()); //重新汇总给值
                            detailSubject.setWorkingHour(laborCostDetail.getActualWorkingHours()); //重新汇总给值

                            if (null != detailSubject.getSubject()) {
                                String[] flexValueSetIds = detailSubject.getSubject().split("\\.");
                                CoaSubject coaSubject = basedataExtService.getCoaSubject(flexValueSetIds[2]);
                                if (!ObjectUtils.isEmpty(coaSubject)) {
                                    detailSubject.setSubjectDescribe(coaSubject.getDescription());
                                }
                            }
                            detailSubject.setWorkingHourAccountingId(dto.getId());
                            detailSubject.setLaborCostType(UserType.INTERNAL.code().equals(laborCostDetail.getUserType()) ? "GS_A4" : "GS_A5");
                            workingHourAccountingDetailSubjectMapper.insert(detailSubject);
                        }
                    }
                }
            }

            //人工成本明细写入关联表
            LaborCostDetailExample laborCostDetailExample = new LaborCostDetailExample();
            laborCostDetailExample.createCriteria().andWorkingHourAccountingIdEqualTo(dto.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            List<LaborCostDetail> laborCostDetails = laborCostDetailMapper.selectByExample(laborCostDetailExample);
            for (LaborCostDetail laborCostDetail : laborCostDetails) {
                LaborCostDetailWorkingHourAccountingRes laborWorkingRes = new LaborCostDetailWorkingHourAccountingRes();
                laborWorkingRes.setLaborCostDetailId(laborCostDetail.getId());
                laborWorkingRes.setWorkingHourAccountingId(dto.getId());
                laborWorkingRes.setWorkingHourId(laborCostDetail.getWorkingHourId());
                laborWorkingRes.setAccountingFlag(laborCostDetail.getAccountingFlag());
                laborWorkingRes.setWriteOffReason(dto.getWriteOffReason());
                laborWorkingRes.setWriteOffTime(dto.getWriteOffTime());
                laborWorkingRes.setDeletedFlag(Boolean.FALSE);
                laborWorkingResMapper.insert(laborWorkingRes);
            }

            // 推送erp
            ResendExecute resendExecute = new ResendExecute();
            resendExecute.setApplyNo(dto.getId().toString());
            resendExecute.setSubApplyNo(dto.getId().toString());
            resendExecute.setBusinessType(BusinessTypeEnums.WORKING_HOUR_ACCOUNTING.getCode());
            resendExecute.setSubBusinessType(SubBusinessTypeEnums.WORKING_HOUR_ACCOUNTING.getCode());
            resendExecute.setBatch(false);
            HandleDispatcher.route(resendExecute);
            WorkingHourAccounting workingHourAccounting = new WorkingHourAccounting();
            workingHourAccounting.setId(dto.getId());
            workingHourAccounting.setErpStatus(CommonErpStatus.PUSHING.code());
            accountingMapper.updateByPrimaryKeySelective(workingHourAccounting);
        }
    }

    /**
     * 按照出勤月份分组
     *
     * @param costCollectionDtoList
     */
    private Map<String, List<CostCollectionDto>> collectByApplyMonth(List<CostCollectionDto> costCollectionDtoList) {
        Map<String, List<CostCollectionDto>> applyMonthMap = new HashMap<>();
        costCollectionDtoList.forEach(costCollectionDto -> {
            String applyMonthKey = costCollectionDto.getApproveMonth() + "_" + costCollectionDto.getApplyMonth() + "_" + costCollectionDto.getOuId();
            if (applyMonthMap.containsKey(applyMonthKey)) {
                applyMonthMap.get(applyMonthKey).add(costCollectionDto);
            } else {
                List<CostCollectionDto> costCollectionDTOs = new ArrayList<>();
                costCollectionDTOs.add(costCollectionDto);
                applyMonthMap.put(applyMonthKey, costCollectionDTOs);
            }
        });
        logger.debug("按照出勤月份分组: 拆分前数据：{}，拆分后数据：{}", JSONArray.toJSONString(costCollectionDtoList),
                JSONObject.toJSONString(applyMonthMap));
        return applyMonthMap;
    }

    private void formatSceneDetail(WorkingHourAccountingDto dto) {
        BusiSceneNonSaleExample example = new BusiSceneNonSaleExample();
        example.createCriteria().andBusiSceneNameEqualTo("工时成本入账").andOuIdEqualTo(dto.getOuId());//todo 按编码确定?
        List<BusiSceneNonSale> entityList = busiSceneNonSaleMapper.selectByExample(example);
        dto.setDailyBatchName("PAM_日记账导入_工时成本入账");
        if (ListUtils.isNotEmpty(entityList)) {
            Map<String, Object> param = new HashMap<>();
            param.put(Constants.Page.PAGE_NUM, 1);
            param.put(Constants.Page.PAGE_SIZE, 2);
            param.put("busiSceneNonSaleId", String.valueOf(entityList.get(0).getId()));
            PageInfo<BusiSceneNonSaleDetailDto> sceneNonSaleDetailDto = busiSceneNonSaleService.detailPage(param);
            if (sceneNonSaleDetailDto != null && ListUtils.isNotEmpty(sceneNonSaleDetailDto.getList())) {
                List<BusiSceneNonSaleDetailDto> list = new ArrayList<>(sceneNonSaleDetailDto.getList());
                BusiSceneNonSaleDetailDto sceneDetail = sceneNonSaleDetailDto.getList().get(0);
                dto.setDebitSubject(sceneDetail.getAccountGroupDebit());//借方科目
                dto.setCreditSubject(sceneDetail.getAccountGroupCredit());//贷方科目
                dto.setBusiSceneNonSaleDetailDtos(list);
            }
        }
    }

    @Override
    public PageInfo<CostCollectionDto> collectionSummaryPage(CostCollectionDto query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<CostCollectionDto> dtoList = this.queryCollectionSummary(query);
        PageInfo<CostCollectionDto> pageInfo = BeanConverter.convertPage(dtoList, CostCollectionDto.class);
        return pageInfo;
    }

    @Override
    public List<CostCollectionDto> queryCollectionSummary(CostCollectionDto query) {
        // 不含汇总的工时成本数据
        List<CostCollectionDto> dtoList = this.queryCollectionSummaryExcludeSumWorkingHour(query);
        this.packageSumWorkingHour(dtoList);
        return dtoList;
    }

    /**
     * 查询不含汇总工时、成本信息的归集数据列表
     *
     * @param query 查询条件
     * @return 列表
     */
    private List<CostCollectionDto> queryCollectionSummaryExcludeSumWorkingHour(CostCollectionDto query) {
        // 不含汇总的工时成本数据
        List<CostCollectionDto> dtoList = costCollectionExtMapper.queryCollectionSummaryExcludeSumWorkingHour(query);
        return dtoList;
    }

    /**
     * 包装内外部成本及工时汇总信息
     *
     * @param dtoList 待包装信息列表
     */
    private void packageSumWorkingHour(List<CostCollectionDto> dtoList) {
        if (CollectionUtils.isNotEmpty(dtoList)) {
            final List<Long> projectIds = dtoList.stream().map(CostCollection::getProjectId).collect(Collectors.toList());

            List<LaborCostDetailDto> laborCostDetailDtos = new ArrayList<>();

            // 按照500每次分批查询
            if (projectIds.size() > 500) {
                int batchNum = projectIds.size() / 500;
                int remainder = projectIds.size() % 500;

                if (remainder > 0) {
                    batchNum = batchNum + 1;
                }

                for (int i = 0; i < batchNum; i++) {
                    int fromIndex = 500 * i;
                    int toIndex = 500 * (i + 1);
                    if (toIndex > projectIds.size()) {
                        toIndex = projectIds.size();
                    }

                    final List<Long> batchProjectIds = new ArrayList<>();
                    for (int j = fromIndex; j < toIndex; j++) {
                        batchProjectIds.add(projectIds.get(j));
                    }

                    // 根据项目id汇总人力成本数据
                    final List<LaborCostDetailDto> batchLaborCostDetailDtos =
                            costCollectionExtMapper.sumProjectCostGroupByMonthAndUserType(batchProjectIds);

                    if (CollectionUtils.isNotEmpty(batchLaborCostDetailDtos)) {
                        laborCostDetailDtos.addAll(batchLaborCostDetailDtos);
                    }
                }
            } else {
                laborCostDetailDtos =
                        costCollectionExtMapper.sumProjectCostGroupByMonthAndUserType(projectIds);
            }

            if (CollectionUtils.isNotEmpty(laborCostDetailDtos)) {
                Map<String, LaborCostDetailDto> laborCostDetailDtoMap = new HashMap<>();

                laborCostDetailDtos.forEach(laborCostDetailDto -> {
                    final Long projectId = laborCostDetailDto.getProjectId();
                    final String applyMonth = laborCostDetailDto.getApplyMonth();
                    final String approveMonth = laborCostDetailDto.getApproveMonth();
                    final String userType = laborCostDetailDto.getUserType();
                    final BigDecimal costTotal = laborCostDetailDto.getCostTotal();
                    final BigDecimal actualWorkingHours = laborCostDetailDto.getActualWorkingHours();

                    String key = projectId + "-" + approveMonth + "-" + applyMonth + "-" + userType;

                    laborCostDetailDtoMap.put(key, laborCostDetailDto);
                });

                // 重新组装成本等数据

                dtoList.forEach(costCollectionDto -> {
                    final Long projectId = costCollectionDto.getProjectId();
                    final String applyMonth = costCollectionDto.getApplyMonth();
                    final String approveMonth = costCollectionDto.getApproveMonth();

                    String key = projectId + "-" + approveMonth + "-" + applyMonth;

                    String innerKey = key + "-" + "1";
                    String outerKey = key + "-" + "2";

                    // 内部
                    if (laborCostDetailDtoMap.containsKey(innerKey)) {
                        final LaborCostDetailDto laborCostDetailDto = laborCostDetailDtoMap.get(innerKey);
                        costCollectionDto.setInnerLaborCost(laborCostDetailDto.getCostTotal());
                        costCollectionDto.setInnerWorkingHour(laborCostDetailDto.getActualWorkingHours());
                    }

                    // 外部
                    if (laborCostDetailDtoMap.containsKey(outerKey)) {
                        final LaborCostDetailDto laborCostDetailDto = laborCostDetailDtoMap.get(outerKey);
                        costCollectionDto.setOuterLaborCost(laborCostDetailDto.getCostTotal());
                        costCollectionDto.setOuterWorkingHour(laborCostDetailDto.getActualWorkingHours());
                    }
                });
            }
        }
    }

    @Override
    public PageInfo<LaborCostDetailDto> laborCostSummaryPage(LaborCostDetailDto query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<LaborCostDetailDto> dtoList = this.queryLaborSummaryDetail(query);
        PageInfo<LaborCostDetailDto> pageInfo = BeanConverter.convertPage(dtoList, LaborCostDetailDto.class);
        for (LaborCostDetailDto dto : pageInfo.getList()) {
            dto.setMipName(CacheDataUtils.findUserById(dto.getUserId()).getUsername());
        }
        return pageInfo;
    }

    public List<LaborCostDetailDto> queryLaborSummaryDetail(LaborCostDetailDto query) {
        List<LaborCostDetailDto> dtoList = costCollectionExtMapper.queryLaborSummaryDetail(query);
        return dtoList;
    }

    private WorkingHourAccountingExample buildExample(WorkingHourAccountingDto query) {
        WorkingHourAccountingExample example = new WorkingHourAccountingExample();
        WorkingHourAccountingExample.Criteria criteria = example.createCriteria();
        if (!ObjectUtils.isEmpty(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (!ObjectUtils.isEmpty(query.getOuId())) {
            criteria.andOuIdEqualTo(query.getOuId());
        }
        if (!ObjectUtils.isEmpty(query.getCode())) {
            criteria.andCodeLike("%" + query.getCode() + "%");
        }
        if (!ObjectUtils.isEmpty(query.getGlDate())) {
            criteria.andGlDateEqualTo(query.getGlDate());
        }
        if (!ObjectUtils.isEmpty(query.getGlPeriod())) {
            criteria.andGlPeriodEqualTo(query.getGlPeriod());
        }
        if (!ObjectUtils.isEmpty(query.getErpStatus())) {
            criteria.andErpStatusEqualTo(query.getErpStatus());
        }
        return example;
    }

    @Override
    public PageInfo<WorkingHourAccountingDto> page(WorkingHourAccountingDto query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<WorkingHourAccounting> accountingList = this.list(query);
        PageInfo<WorkingHourAccountingDto> dtoList = BeanConverter.convertPage(accountingList, WorkingHourAccountingDto.class);
        for (WorkingHourAccountingDto dto : dtoList.getList()) {
            UserInfo userInfo = CacheDataUtils.findUserById(dto.getCreateBy());
            if (userInfo != null) {
                dto.setCreateByName(userInfo.getName());
            }
        }
        return dtoList;
    }

    public List<WorkingHourAccounting> list(WorkingHourAccountingDto query) {
        List<WorkingHourAccounting> accountingList = new ArrayList<>();
        WorkingHourAccountingExample example = this.buildExample(query);
        //项目编码、项目名称需在明细表里进行查询
        if (!ObjectUtils.isEmpty(query.getProjectCode()) || !ObjectUtils.isEmpty(query.getProjectName())) {
            List<WorkingHourAccountingDetail> detailList = this.detailList(query);
            if (ListUtils.isNotEmpty(detailList)) {
                List<Long> headerIds = ListUtil.map(detailList, "workingHourAccountingId");
                example.getOredCriteria().get(0).andIdIn(headerIds);
            } else {
                return accountingList;
            }
        }
        accountingList = accountingMapper.selectByExample(example);
        return accountingList;
    }


    /**
     * 成本工时入账单明细表保存方法
     *
     * @param detail
     */
    public void saveDetail(WorkingHourAccountingDetail detail) {
        if (ObjectUtils.isEmpty(detail.getId())) {
            detail.setDeletedFlag(Boolean.FALSE);
            accountingDetailMapper.insert(detail);
        } else {
            accountingDetailMapper.updateByPrimaryKeySelective(detail);
        }
    }

    private WorkingHourAccountingDetailExample buildDetailExample(WorkingHourAccountingDto query) {
        WorkingHourAccountingDetailExample example = new WorkingHourAccountingDetailExample();
        WorkingHourAccountingDetailExample.Criteria criteria = example.createCriteria();
        if (!ObjectUtils.isEmpty(query.getId())) {
            criteria.andWorkingHourAccountingIdEqualTo(query.getId());//头表id
        }
        if (!ObjectUtils.isEmpty(query.getProjectCode())) {
            criteria.andProjectCodeLike("%" + query.getProjectCode() + "%");
        }
        if (!ObjectUtils.isEmpty(query.getProjectName())) {
            criteria.andProjectNameLike("%" + query.getProjectName() + "%");
        }
        return example;
    }

    @Override
    public PageInfo<WorkingHourAccountingDetail> detailPage(WorkingHourAccountingDto query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<WorkingHourAccountingDetail> detailList = this.detailList(query);
        PageInfo<WorkingHourAccountingDetail> pageInfo = BeanConverter.convertPage(detailList, WorkingHourAccountingDetail.class);
        return pageInfo;
    }

    public List<WorkingHourAccountingDetail> detailList(WorkingHourAccountingDto query) {
        WorkingHourAccountingDetailExample detailExample = this.buildDetailExample(query);
        List<WorkingHourAccountingDetail> accountingDetailList = accountingDetailMapper.selectByExample(detailExample);
        return accountingDetailList;
    }

    @Override
    public void pushToErp(Long id) {
        WorkingHourAccounting workingHourAccounting = accountingMapper.selectByPrimaryKey(id);
        if (workingHourAccounting.getErpStatus().equals(CommonErpStatus.PUSHED.code()) ||
                workingHourAccounting.getErpStatus().equals(CommonErpStatus.PUSHING.code())) {
            throw new BizException(Code.ERROR, "记账中或已记账的工单不能进行此操作!");
        }
        if (workingHourAccounting.getStatus() == 1) {
            throw new BizException(Code.ERROR, "作废的入账单不能进行此操作!");
        }
        ResendExecute resendExecute = new ResendExecute();
        resendExecute.setApplyNo(workingHourAccounting.getId().toString());
        resendExecute.setSubApplyNo(workingHourAccounting.getId().toString());
        resendExecute.setBusinessType(BusinessTypeEnums.WORKING_HOUR_ACCOUNTING.getCode());
        resendExecute.setSubBusinessType(SubBusinessTypeEnums.WORKING_HOUR_ACCOUNTING.getCode());
        resendExecute.setBatch(false);
        HandleDispatcher.route(resendExecute);
        workingHourAccounting.setErpStatus(CommonErpStatus.PUSHING.code());
        accountingMapper.updateByPrimaryKeySelective(workingHourAccounting);

        //记录此次同步的信息到代办管理数据同步信息记录项中
        AgencySynInfoExample agencySynInfoExample = new AgencySynInfoExample();
        agencySynInfoExample.createCriteria().andBusinessTypeEqualTo(BusinessTypeEnums.WORKING_HOUR_ACCOUNTING.getCode())
                .andApplyNoEqualTo(workingHourAccounting.getId().toString()).andDeletedFlagEqualTo(Boolean.FALSE);
        final List<AgencySynInfo> agencySyninfoList = agencySynInfoMapper.selectByExample(agencySynInfoExample);
        if (ListUtils.isNotEmpty(agencySyninfoList)) {
            for (AgencySynInfo agencySyninfo : agencySyninfoList) {
                agencySyninfo.setSynStartTime(new Date());
                agencySynInfoMapper.updateByPrimaryKey(agencySyninfo);
            }
        } else {
            AgencySynInfo agencySyninfo = new AgencySynInfo();
            agencySyninfo.setBusinessType(BusinessTypeEnums.WORKING_HOUR_ACCOUNTING.getCode());
            agencySyninfo.setApplyNo(workingHourAccounting.getId().toString());
            agencySyninfo.setSynStartTime(new Date());
            agencySyninfo.setDeletedFlag(Boolean.FALSE);
            agencySynInfoMapper.insert(agencySyninfo);
        }
    }

    /**
     * ERP回调处理
     *
     * @param id
     * @param isSuccess
     * @param msg
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pamResultReturnHandle(Long id, Boolean isSuccess, String msg) {
        WorkingHourAccounting workingHourAccounting = workingHourAccountingMapper.selectByPrimaryKey(id);
        if (Objects.equals(workingHourAccounting.getErpStatus(), CommonErpStatus.PUSHED.code())) {
            return;
        }
        WorkingHourAccountingDto dto = new WorkingHourAccountingDto();
        dto.setId(id);
        if (isSuccess) {
            dto.setErpStatus(CommonErpStatus.PUSHED.code());
            dto.setErpMessage(StringUtil.EMPTY_STRING);
        } else {
            dto.setErpStatus(CommonErpStatus.PUSH_FAILED.code());
            dto.setErpMessage(msg + " " + (new Date()).toString());
        }

        //更新状态
        this.save(dto);
        //更新人工工时归集是否入账状态
        LaborCostDetailDto laborCostDetailDto = new LaborCostDetailDto();
        laborCostDetailDto.setWorkingHourAccountingId(id);
        laborCostDetailDto.setAccountingFlag(1);
        costCollectionExtMapper.updateLaborCostDetailToAccounted(laborCostDetailDto);
    }

    @Override
    public void pushToErpTest(Long id) {
        List<GlDailyAccountingErpDto> dtos = new ArrayList<>();
        Integer lineNum = 1;
        PageInfo<WorkingHourAccountingDetailSubject> list = this.getByWorkingHourAccountingID(id, 1, 10);
        List<WorkingHourAccountingDetailSubject> listSubject = list.getList();
        WorkingHourAccountingDto query = new WorkingHourAccountingDto();
        query.setId(id);
        query.setPageNum(1);
        query.setPageSize(1);
        List<WorkingHourAccountingDto> dtoList = this.page(query).getList();

        for (WorkingHourAccountingDetailSubject workingHourAccountingDetailSubject : listSubject) {
            GlDailyAccountingErpDto debitDto = this.formatCostAccountingErpDto2(dtoList.get(0), workingHourAccountingDetailSubject, lineNum);
            debitDto.setEsbDataSource(BusinessTypeEnums.WORKING_HOUR_ACCOUNTING.getCode());
            dtos.add(debitDto);
        }

        if (ListUtils.isNotEmpty(dtos)) {
//            esbService.callCUXGLJOURNALSIMPORTAPIPKGPortType(BeanConverter.copy(dtos, CostAccountingErpDto.class));
            sdpService.callGERPGlJournalsImport(BeanConverter.copy(dtos, CostAccountingErpDto.class));
        }
    }

    private GlDailyAccountingErpDto formatCostAccountingErpDto2(WorkingHourAccountingDto accountingDto, WorkingHourAccountingDetailSubject accountingDetailSubject, Integer lineNum) {
        GlDailyAccountingErpDto dto = new GlDailyAccountingErpDto();
        dto.setLineNumber(new BigDecimal(lineNum));
        dto.setId(accountingDto.getId());
        dto.setLedgerId(new BigDecimal(this.getLedgerId(accountingDto.getOuId()))); //根据ouId查找ledgerId
        dto.setStatus("U");
        dto.setAccountingDate(DateUtils.formatDate(accountingDto.getGlDate()));
        dto.setActualFlag("A");
        dto.setUserJeCategoryName(".记账凭证");
        dto.setCurrencyCode(accountingDto.getCurrency());
        dto.setBathName(accountingDto.getDailyBatchName() + "_" + accountingDto.getDailyBatchNum());
        dto.setJournalName(accountingDto.getDailyBatchName() + "_" + accountingDto.getDailyBatchNum());
        //借方
        String[] subject = null;
        if (WorkingHourAccountingEnum.DEBIT.getCode().equals(accountingDetailSubject.getDebitCredit())) {
            dto.setEnterEddr(accountingDetailSubject.getLaborCost());
            dto.setAccountEddr(accountingDetailSubject.getLaborCost());
            subject = StringUtils.split(accountingDetailSubject.getSubject(), ".");//借方科目
        } else {
            dto.setEnterEdcr(accountingDetailSubject.getLaborCost());
            dto.setAccountEdcr(accountingDetailSubject.getLaborCost());
            subject = StringUtils.split(accountingDetailSubject.getSubject(), ".");//贷方科目
        }
        dto.setSegment1(subject[0]);
        dto.setSegment2(subject[1]);
        dto.setSegment3(subject[2]);
        dto.setSegment4(subject[3]);
        dto.setSegment5(subject[4]);
        dto.setSegment6(subject[5]);
        dto.setSegment7(subject[6]);
        dto.setReference5(accountingDto.getDailyBatchName() + "_" + accountingDto.getDailyBatchNum());
        dto.setReference10(accountingDto.getDailyBatchName() + "_" + accountingDto.getDailyBatchNum());
        dto.setOperationType("CREATE");
        return dto;
    }


    //todo 每次2行，Debit and Credit
    private GlDailyAccountingErpDto formatCostAccountingErpDto(WorkingHourAccountingDto accountingDto, Integer lineNum, boolean isDebit) {
        GlDailyAccountingErpDto dto = new GlDailyAccountingErpDto();
        dto.setLineNumber(new BigDecimal(lineNum));
        dto.setId(accountingDto.getId());
        dto.setLedgerId(new BigDecimal(this.getLedgerId(accountingDto.getOuId()))); //根据ouId查找ledgerId
        dto.setStatus("U");
        dto.setAccountingDate(DateUtils.formatDate(accountingDto.getGlDate()));
        dto.setActualFlag("A");
        dto.setUserJeCategoryName(".记账凭证");
        dto.setCurrencyCode(accountingDto.getCurrency());
        dto.setBathName(accountingDto.getDailyBatchName() + "_" + accountingDto.getDailyBatchNum());
        dto.setJournalName(accountingDto.getDailyBatchName() + "_" + accountingDto.getDailyBatchNum());
        //借方0r贷方
        String[] subject = null;
        if (isDebit) {
            dto.setEnterEddr(accountingDto.getTotalLaborCost());
            dto.setAccountEddr(accountingDto.getTotalLaborCost());
            subject = StringUtils.split(accountingDto.getDebitSubject(), "."); //借方科目
        } else {
            dto.setEnterEdcr(accountingDto.getTotalLaborCost());
            dto.setAccountEdcr(accountingDto.getTotalLaborCost());
            subject = StringUtils.split(accountingDto.getCreditSubject(), ".");//贷方科目
        }
        dto.setSegment1(subject[0]);
        dto.setSegment2(subject[1]);
        dto.setSegment3(subject[2]);
        dto.setSegment4(subject[3]);
        dto.setSegment5(subject[4]);
        dto.setSegment6(subject[5]);
        dto.setSegment7(subject[6]);
        dto.setReference5(accountingDto.getDailyBatchName() + "_" + accountingDto.getDailyBatchNum());
        dto.setReference10(accountingDto.getDailyBatchName() + "_" + accountingDto.getDailyBatchNum());
        dto.setOperationType("CREATE");
        return dto;
    }

    /**
     * 根据ouId查找ledgerId
     *
     * @param ouId
     * @return
     */
    private Long getLedgerId(Long ouId) {
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setPamEnabled(0);
        query.setOperatingUnitId(ouId);
        PageInfo<OrganizationRelDto> orgDtoPage = organizationRelExtService.invokeApiList(query);
        return orgDtoPage.getList().get(0).getLedgerId();
    }

    /**
     * 根据头ID查询工时成本入账根据科目明细
     */

    public PageInfo<WorkingHourAccountingDetailSubject> getByWorkingHourAccountingID(Long workingHourAccountingId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        WorkingHourAccountingDetailSubjectExample example = new WorkingHourAccountingDetailSubjectExample();
        example.createCriteria().andWorkingHourAccountingIdEqualTo(workingHourAccountingId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<WorkingHourAccountingDetailSubject> listSubject = workingHourAccountingDetailSubjectMapper.selectByExample(example);
        PageInfo<WorkingHourAccountingDetailSubject> pageInfo = BeanConverter.convertPage(listSubject, WorkingHourAccountingDetailSubject.class);
        return pageInfo;
    }

    /**
     * 工时成本入账单冲销页面需要的数据信息
     *
     * @param params
     * @return
     */
    @Override
    public WorkingHourAccountingDto writeOffInfo(Map<String, Object> params) {
        List<LaborCostDetailDto> laborCostDetailDtoList = accountingExtMapper.findWorkingAccountWriteOff(params);
        WorkingHourAccountingDto workingHourAccountingDto = new WorkingHourAccountingDto();
        List<WorkingHourAccountingDetailDto> workingHourAccountingDetailDtoList = new ArrayList<>();
        List<LaborCostDetailDto> finalLaborCostDetailDtoList = new ArrayList<>();

        String userStr = (String) params.get("userStr");
        if (CollectionUtils.isNotEmpty(laborCostDetailDtoList)) {
            for (LaborCostDetailDto laborCostDetailDto : laborCostDetailDtoList) {
                final UserInfo userInfo = CacheDataUtils.findUserById(laborCostDetailDto.getUserId());
                laborCostDetailDto.setMipName(userInfo.getUsername());
                if (StringUtils.isNotEmpty(userStr)) {
                    String userName = laborCostDetailDto.getUserName();
                    String mipName = laborCostDetailDto.getMipName();
                    if (userName.contains(userStr) || mipName.contains(userStr)) {
                        finalLaborCostDetailDtoList.add(laborCostDetailDto);
                    }
                } else {
                    finalLaborCostDetailDtoList.add(laborCostDetailDto);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(finalLaborCostDetailDtoList)) {
            //冲销合计 工时-内部
            BigDecimal innerWorkingHourSummary = BigDecimal.ZERO;
            //冲销合计 工时-外部
            BigDecimal outerWorkingHourSummary = BigDecimal.ZERO;
            //冲销合计 工时合计
            BigDecimal workingHourTotalSummary = BigDecimal.ZERO;
            //冲销合计 工时成本合计
            BigDecimal totalLaborCostSummary = BigDecimal.ZERO;

            for (LaborCostDetailDto laborCostDetailDto : finalLaborCostDetailDtoList) {
                if (UserType.INTERNAL.code().equals(laborCostDetailDto.getUserType())) {
                    innerWorkingHourSummary = innerWorkingHourSummary.add(laborCostDetailDto.getActualWorkingHours() == null ? BigDecimal.ZERO : laborCostDetailDto.getActualWorkingHours());
                }
                if (UserType.HRO.code().equals(laborCostDetailDto.getUserType()) || UserType.RECRUIT.code().equals(laborCostDetailDto.getUserType())) {
                    outerWorkingHourSummary = outerWorkingHourSummary.add(laborCostDetailDto.getActualWorkingHours() == null ? BigDecimal.ZERO : laborCostDetailDto.getActualWorkingHours());
                }
                workingHourTotalSummary = workingHourTotalSummary.add(laborCostDetailDto.getActualWorkingHours() == null ? BigDecimal.ZERO : laborCostDetailDto.getActualWorkingHours());
                totalLaborCostSummary = totalLaborCostSummary.add(laborCostDetailDto.getCostTotal() == null ? BigDecimal.ZERO : laborCostDetailDto.getCostTotal());

                //已结转数据
                Map<String, Object> params1 = new HashMap<>();
                params1.put("projectId", laborCostDetailDto.getProjectId());
                params1.put("userId", laborCostDetailDto.getUserId());
                params1.put("carryStatus", laborCostDetailDto.getCarryStatus());
                params1.put("costMoney", laborCostDetailDto.getCostMoneyStr());
                List<LaborCostDetailDto> sonLaborCostDetailDtoList = accountingExtMapper.findLaborCostDetailCarryStatus(params1);

                // 为sonLaborCostDetailDtoList设置结转单号
                if (CollectionUtils.isNotEmpty(sonLaborCostDetailDtoList)) {
                    for (LaborCostDetailDto sonLaborCostDetailDto : sonLaborCostDetailDtoList) {
                        Long costCollectionId = sonLaborCostDetailDto.getCostCollectionId();
                        if (costCollectionId != null) {
                            CarryoverBillCostCollectionRelExample costCollectionRelExample = new CarryoverBillCostCollectionRelExample();
                            costCollectionRelExample.createCriteria().andCostCollectionIdEqualTo(costCollectionId).andCarryoverBillIdIsNotNull().andDeletedFlagEqualTo(Boolean.FALSE);
                            List<CarryoverBillCostCollectionRel> carryoverBillCostCollectionRelList = costCollectionRelMapper.selectByExample(costCollectionRelExample);
                            CostCollection costCollection = costCollectionMapper.selectByPrimaryKey(costCollectionId);
                            if (CollectionUtils.isNotEmpty(carryoverBillCostCollectionRelList) && costCollection != null) {
                                List<Long> carryoverBillIds = carryoverBillCostCollectionRelList.stream().map(CarryoverBillCostCollectionRel::getCarryoverBillId).collect(Collectors.toList());
                                List<Integer> reverseStatus = new ArrayList<>();
                                reverseStatus.add(CarryoverBillReverseStatus.CAN.getCode());
                                reverseStatus.add(CarryoverBillReverseStatus.CAN_NOT.getCode());
                                CarryoverBillExample carryoverBillExample = new CarryoverBillExample();
                                carryoverBillExample.createCriteria().andIdIn(carryoverBillIds).andStatusEqualTo(1).andDeletedFlagEqualTo(Boolean.FALSE).andReverseStatusIn(reverseStatus);
                                List<CarryoverBill> carryoverBillList = carryoverBillMapper.selectByExample(carryoverBillExample);
                                CarryoverBill carryoverBill = null;
                                if (CollectionUtils.isNotEmpty(carryoverBillList)) {
                                    if (carryoverBillList.size() > 1) {
                                        throw new MipException("该归集数据被多笔结转单关联，请联系管理员！");
                                    } else {
                                        carryoverBill = carryoverBillList.get(0);
                                    }
                                }

                                if (!ObjectUtils.isEmpty(carryoverBill) && costCollection.getCarryStatus() == 1) {
                                    sonLaborCostDetailDto.setCarryBillNum(carryoverBill.getBillNum());
                                    sonLaborCostDetailDto.setCarryoverBillId(carryoverBill.getId());
                                }
                            }
                        }
                    }
                }

                laborCostDetailDto.setLaborCostDetailDtoList(sonLaborCostDetailDtoList);

            }
            //过滤已结转数据
            List<LaborCostDetailDto> laborCostDetailCarryOverList = finalLaborCostDetailDtoList.stream().filter(laborCostDetailDto -> 1 == laborCostDetailDto.getCarryStatus()).collect(Collectors.toList());
            //过滤未结转数据
            List<LaborCostDetailDto> laborCostDetailDtoNoneList = finalLaborCostDetailDtoList.stream().filter(laborCostDetailDto -> 0 == laborCostDetailDto.getCarryStatus()).collect(Collectors.toList());

            workingHourAccountingDto.setLaborCostDetailCarryOverList(laborCostDetailCarryOverList);
            workingHourAccountingDto.setLaborCostDetailDtoNoneList(laborCostDetailDtoNoneList);
            workingHourAccountingDto.setInnerWorkingHourSummary(innerWorkingHourSummary);
            workingHourAccountingDto.setOuterWorkingHourSummary(outerWorkingHourSummary);
            workingHourAccountingDto.setWorkingHourTotalSummary(workingHourTotalSummary);
            workingHourAccountingDto.setTotalLaborCostSummary(totalLaborCostSummary);
            workingHourAccountingDto.setCurrency("CNY");
        }

        return workingHourAccountingDto;
    }

    @Transactional
    @Override
    public Boolean workingAccountWriteOff(WorkingHourAccountingDto dto) {

        if (true) {
            throw new BizException(Code.ERROR, "此功能已暂停使用");
        }

        //未结转的数据
        List<LaborCostDetailDto> laborCostDetailDtoNoneList = dto.getLaborCostDetailDtoNoneList();
        if (CollectionUtils.isNotEmpty(laborCostDetailDtoNoneList)) {
            List<LaborCostDetailDto> sonLaborCostDetailDtoList = new ArrayList<>();
            for (LaborCostDetailDto laborCostDetailDto : laborCostDetailDtoNoneList) {
                sonLaborCostDetailDtoList.addAll(laborCostDetailDto.getLaborCostDetailDtoList());
            }
            Long writeOffId = null;
            if (CollectionUtils.isNotEmpty(sonLaborCostDetailDtoList)) {
                for (LaborCostDetailDto costDetailDto : sonLaborCostDetailDtoList) {
                    String applyMonthStr = "";
                    if (costDetailDto.getApplyDate() != null) {
                        applyMonthStr = DateUtil.format(costDetailDto.getApplyDate(), "yyyy-MM");
                    }
                    costDetailDto.setApplyDateStr(applyMonthStr);
                }

                //根据填报出勤日期来决定生成几笔冲销单
                Map<String, List<LaborCostDetailDto>> laborCostDetailDtoMapByApplyDate = sonLaborCostDetailDtoList.stream()
                        .collect(Collectors.groupingBy(LaborCostDetailDto::getApplyDateStr));
                for (Map.Entry<String, List<LaborCostDetailDto>> mapEntry : laborCostDetailDtoMapByApplyDate.entrySet()) {
                    List<LaborCostDetailDto> laborCostDetailDtoList = mapEntry.getValue();
                    //统计要冲销的总工时和总人工成本
                    BigDecimal totalWorkingHour = BigDecimal.ZERO;//已经填报工时
                    BigDecimal totalLaborCost = BigDecimal.ZERO; //已发生人工成本

                    if (CollectionUtils.isNotEmpty(laborCostDetailDtoList)) {
                        for (LaborCostDetailDto detailDto : laborCostDetailDtoList) {
                            totalWorkingHour = totalWorkingHour.add(detailDto.getActualWorkingHours());
                            totalLaborCost = totalLaborCost.add(detailDto.getCostTotal());
                        }

                    }

                    //工时入账冲销单明细
                    Long projectId = null;
                    String projectCode = "";
                    String projectName = "";
                    String projectType = "";
                    Long ouId = null;
                    if (CollectionUtils.isNotEmpty(laborCostDetailDtoList)) {
                        projectId = laborCostDetailDtoList.get(0).getProjectId();

                    }

                    if (projectId != null) {
                        Project project = projectMapper.selectByPrimaryKey(projectId);
                        if (project != null) {
                            ouId = project.getOuId();
                            projectCode = project.getCode();
                            projectName = project.getName();
                            projectType = project.getType().toString();
                        }
                    }
                    //生成工时入账冲销单
                    WorkingHourAccounting workingHourAccounting = new WorkingHourAccounting();
                    String seqPerfix = basedataExtService.getUnitSeqPerfix(SystemContext.getUnitId());
                    workingHourAccounting.setCode(seqPerfix + "GS" + CacheDataUtils.generateSequence(3, seqPerfix + "GS", DateUtil.DATE_YYMMDD_PATTERN));
                    workingHourAccounting.setDailyBatchNum(StringUtils.replace(dto.getCode(), "GS", "PAM"));
                    workingHourAccounting.setTotalLaborCost(totalLaborCost);
                    workingHourAccounting.setId(null);
                    workingHourAccounting.setOuId(ouId);
                    if (ouId != null) {
                        final OperatingUnit operatingUnit = CacheDataUtils.findOuById(ouId);
                        if (operatingUnit != null) {
                            workingHourAccounting.setOuName(operatingUnit.getOperatingUnitName());
                        }
                    }
                    workingHourAccounting.setTotalWorkingHour(totalWorkingHour.multiply(BigDecimal.valueOf(-1l)));
                    workingHourAccounting.setTotalLaborCost(totalLaborCost.multiply(BigDecimal.valueOf(-1l)));
                    workingHourAccounting.setWriteOffFile(dto.getWriteOffFile());
                    workingHourAccounting.setWriteOffReason(dto.getWriteOffReason());
                    workingHourAccounting.setWriteOffUser(dto.getWriteOffUser());
                    workingHourAccounting.setWriteOffTime(dto.getWriteOffTime());
                    workingHourAccounting.setGlPeriod(DateUtil.format(dto.getWriteOffTime(), "yyyy-MM"));
                    workingHourAccounting.setGlDate(dto.getWriteOffTime());
                    workingHourAccounting.setErpStatus(2);
                    workingHourAccounting.setStatus(0);
                    workingHourAccounting.setDeletedFlag(Boolean.FALSE);
                    workingHourAccounting.setCreateAt(new Date());
                    workingHourAccounting.setUpdateAt(null);
                    workingHourAccounting.setUpdateBy(null);
                    workingHourAccounting.setCurrency("CNY");
                    workingHourAccounting.setApplyMonth(mapEntry.getKey());
                    workingHourAccounting.setApproveMonth(mapEntry.getKey());
                    accountingMapper.insert(workingHourAccounting);
                    writeOffId = workingHourAccounting.getId();
                    WorkingHourAccountingDto accountingDto = BeanConverter.copy(workingHourAccounting, WorkingHourAccountingDto.class);

                    BigDecimal finalInnerWorkingHour = BigDecimal.ZERO;
                    BigDecimal finalInnerLaborCost = BigDecimal.ZERO;
                    BigDecimal finalOuterWorkingHour = BigDecimal.ZERO;
                    BigDecimal finalOuterLaborCost = BigDecimal.ZERO;

                    if (CollectionUtils.isNotEmpty(laborCostDetailDtoList)) {
                        //然后再根据内部，外部进行分组
                        Map<String, List<LaborCostDetailDto>> stringListMap = laborCostDetailDtoList.stream().collect(Collectors.groupingBy(LaborCostDetailDto::getUserType));
                        //内部人工明细
                        List<LaborCostDetailDto> laborCostDetailDtoList1 = stringListMap.get("1");
                        if (CollectionUtils.isNotEmpty(laborCostDetailDtoList1)) {
                            BigDecimal innerLaborCost = BigDecimal.ZERO;
                            BigDecimal actualWorkingHours = BigDecimal.ZERO;
                            for (LaborCostDetailDto detailDto : laborCostDetailDtoList1) {
                                innerLaborCost = innerLaborCost.add(detailDto.getCostTotal());
                                actualWorkingHours = actualWorkingHours.add(detailDto.getActualWorkingHours());

                                //插入工时成本入账冲销与工时成本明细关联关系表
                                if (writeOffId != null) {
                                    WorkingHourWriteoffLaborcostRel rel = new WorkingHourWriteoffLaborcostRel();
                                    rel.setWorkingHourAccountingId(null);
                                    rel.setWorkingHourWriteoffId(writeOffId);
                                    rel.setLaborCostDetailId(detailDto.getId());
                                    rel.setDeletedFlag(Boolean.FALSE);
                                    writeoffLaborcostRelMapper.insert(rel);
                                }

                            }
                            finalInnerLaborCost = innerLaborCost;
                            finalInnerWorkingHour = actualWorkingHours;

                            WorkingHourAccountingDetail workingHourAccountingDetail = new WorkingHourAccountingDetail();
                            workingHourAccountingDetail.setId(null);
                            workingHourAccountingDetail.setProjectId(projectId);
                            workingHourAccountingDetail.setProjectCode(projectCode);
                            workingHourAccountingDetail.setProjectName(projectName);
                            workingHourAccountingDetail.setProjectType(projectType);
                            workingHourAccountingDetail.setInnerLaborCost(innerLaborCost.multiply(BigDecimal.valueOf(-1l)));
                            workingHourAccountingDetail.setInnerWorkingHour(actualWorkingHours.multiply(BigDecimal.valueOf(-1l)));
                            workingHourAccountingDetail.setWorkingHourAccountingId(writeOffId);
                            workingHourAccountingDetail.setApplyMonth(workingHourAccounting.getApplyMonth());
                            workingHourAccountingDetail.setCreateAt(new Date());
                            workingHourAccountingDetail.setUpdateAt(null);
                            workingHourAccountingDetail.setUpdateBy(null);
                            accountingDetailMapper.insert(workingHourAccountingDetail);
                        }
                        //外部人工明细
                        List<LaborCostDetailDto> laborCostDetailDtoList2 = stringListMap.get("2");


                        if (CollectionUtils.isNotEmpty(laborCostDetailDtoList2)) {
                            BigDecimal outerLaborCost = BigDecimal.ZERO;
                            BigDecimal actualWorkingHours = BigDecimal.ZERO;
                            for (LaborCostDetailDto detailDto : laborCostDetailDtoList2) {
                                outerLaborCost = outerLaborCost.add(detailDto.getCostTotal());
                                actualWorkingHours = actualWorkingHours.add(detailDto.getActualWorkingHours());
                            }
                            WorkingHourAccountingDetail workingHourAccountingDetail = new WorkingHourAccountingDetail();
                            workingHourAccountingDetail.setId(null);
                            workingHourAccountingDetail.setProjectId(projectId);
                            workingHourAccountingDetail.setProjectCode(projectCode);
                            workingHourAccountingDetail.setProjectName(projectName);
                            workingHourAccountingDetail.setProjectType(projectType);
                            workingHourAccountingDetail.setInnerLaborCost(outerLaborCost.multiply(BigDecimal.valueOf(-1l)));
                            workingHourAccountingDetail.setInnerWorkingHour(actualWorkingHours.multiply(BigDecimal.valueOf(-1l)));
                            workingHourAccountingDetail.setWorkingHourAccountingId(writeOffId);
                            workingHourAccountingDetail.setApplyMonth(workingHourAccounting.getApplyMonth());
                            workingHourAccountingDetail.setCreateAt(new Date());
                            workingHourAccountingDetail.setUpdateAt(null);
                            workingHourAccountingDetail.setUpdateBy(null);
                            accountingDetailMapper.insert(workingHourAccountingDetail);
                            finalOuterLaborCost = outerLaborCost;
                            finalOuterWorkingHour = actualWorkingHours;

                        }

                    }


                    this.formatSceneDetail(accountingDto);
                    WorkingHourAccounting workingHourAccounting1 = BeanConverter.copy(accountingDto, WorkingHourAccounting.class);
                    accountingMapper.updateByPrimaryKey(workingHourAccounting1);

                    List<WorkingHourAccountingDetailSubject> detailSubjectsList = new ArrayList<>();
                    CostCollectionDto costCollectionDto = new CostCollectionDto();
                    costCollectionDto.setInnerLaborCost(finalInnerLaborCost == null ? BigDecimal.ZERO : finalInnerLaborCost.multiply(BigDecimal.valueOf(-1l)));
                    costCollectionDto.setInnerWorkingHour(finalInnerWorkingHour == null ? BigDecimal.ZERO : finalInnerWorkingHour.multiply(BigDecimal.valueOf(-1l)));
                    costCollectionDto.setOuterWorkingHour(finalOuterWorkingHour == null ? BigDecimal.ZERO : finalOuterWorkingHour.multiply(BigDecimal.valueOf(-1l)));
                    costCollectionDto.setOuterLaborCost(finalOuterLaborCost == null ? BigDecimal.ZERO : finalOuterLaborCost.multiply(BigDecimal.valueOf(-1)));
                    List<WorkingHourAccountingDetailSubject> innerDetailSubject = this.getWorkingHourAccountingDetail(costCollectionDto, "GS_A4");
                    detailSubjectsList.addAll(innerDetailSubject);
                    List<WorkingHourAccountingDetailSubject> outerDetailSubject = this.getWorkingHourAccountingDetail(costCollectionDto, "GS_A5");
                    detailSubjectsList.addAll(outerDetailSubject);

                    //插入工时入账冲销科目明细 WorkingHourAccountingDetailSubject
                    if (CollectionUtils.isNotEmpty(accountingDto.getBusiSceneNonSaleDetailDtos())) {
                        for (BusiSceneNonSaleDetailDto busiSceneNonSaleDetailDto : accountingDto.getBusiSceneNonSaleDetailDtos()) {
                            String type = busiSceneNonSaleDetailDto.getFlag();
                            for (WorkingHourAccountingDetailSubject detailSubject : detailSubjectsList) {
                                if (type.equals(detailSubject.getLaborCostType())) {
                                    if (detailSubject.getDebitCredit().equals(WorkingHourAccountingEnum.DEBIT.getCode().toString())) {
                                        detailSubject.setSubject(busiSceneNonSaleDetailDto.getAccountGroupDebit());
                                    } else {
                                        detailSubject.setSubject(busiSceneNonSaleDetailDto.getAccountGroupCredit());
                                    }
                                    String[] flexValueSetIds = detailSubject.getSubject().split("\\.");
                                    CoaSubject coaSubject = basedataExtService.getCoaSubject(flexValueSetIds[2]);
                                    detailSubject.setWorkingHourAccountingId(accountingDto.getId());
                                    if (!ObjectUtils.isEmpty(coaSubject)) {
                                        detailSubject.setSubjectDescribe(coaSubject.getDescription());
                                    }
                                    workingHourAccountingDetailSubjectMapper.insert(detailSubject);
                                }
                            }
                        }
                    }
                }

            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean abandon(Long id) {
        Date date = new Date();
        Long userId = SystemContext.getUserId();
        WorkingHourAccounting workingHourAccounting = accountingMapper.selectByPrimaryKey(id);
        if (!ObjectUtils.isEmpty(workingHourAccounting)) {
            if (workingHourAccounting.getErpStatus() == 2 || workingHourAccounting.getErpStatus() == 4) {
                workingHourAccounting.setStatus(1);
                accountingMapper.updateByPrimaryKeySelective(workingHourAccounting);
                //判断是否为冲销的数据
                if (workingHourAccounting.getWriteOffUser() != null || workingHourAccounting.getWriteOffTime() != null) {
                    //冲销数据
                    return Boolean.TRUE;
                } else {
                    //非冲销数据
                    LaborCostDetailExample laborCostDetailExample = new LaborCostDetailExample();
                    laborCostDetailExample.createCriteria().andWorkingHourAccountingIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE);
                    List<LaborCostDetail> laborCostDetails = laborCostDetailMapper.selectByExample(laborCostDetailExample);
                    if (CollectionUtils.isNotEmpty(laborCostDetails)) {
                        for (LaborCostDetail laborCostDetail : laborCostDetails) {
                            laborCostDetail.setWorkingHourAccountingId(null);
                            laborCostDetail.setStatus(0);
                            laborCostDetail.setUpdateAt(new Date());
                            laborCostDetail.setUpdateBy(userId);
                            laborCostDetailMapper.updateByPrimaryKey(laborCostDetail);
                            logger.info("作废操作成功 workingHourAccounting：{},laborCostDetail{}", id, laborCostDetail.getId());
                        }
                    }

                }
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public Map<String, Object> exportWriteOffInfo(Long id) {
        Map<String, Object> responseMap = new HashMap<>();
        WorkingHourWriteoffLaborcostRelExample relExample = new WorkingHourWriteoffLaborcostRelExample();
        relExample.createCriteria().andWorkingHourWriteoffIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE);
        List<WorkingHourWriteoffLaborcostRel> workingHourWriteoffLaborcostRelList = writeoffLaborcostRelMapper.selectByExample(relExample);
        Map<String, Object> param = new HashMap<>();
        List<LaborCostDetailDto> laborCostDetailDtoList;
        if (CollectionUtils.isNotEmpty(workingHourWriteoffLaborcostRelList)) {
            //冲销单
            List<Long> laborCostDetailIdList = workingHourWriteoffLaborcostRelList.stream().map(WorkingHourWriteoffLaborcostRel::getLaborCostDetailId).collect(Collectors.toList());
            param.put("laborCostDetailIdList", laborCostDetailIdList);
            laborCostDetailDtoList = accountingExtMapper.findLaborCostDetailInfo(param);
        } else {
            //非冲销单
            param.put("workingHourAccountingId", id);
            laborCostDetailDtoList = accountingExtMapper.findLaborCostDetailInfo(param);
        }
        if (CollectionUtils.isEmpty(laborCostDetailDtoList)) {
            return responseMap;
        }

        List<Long> costCollectionIdList = new ArrayList<>();
        Map<String, LaborCostDetailDto> laborCostDetailDtoMap = new HashMap<>();
        for (LaborCostDetailDto laborCostDetailDto : laborCostDetailDtoList) {
            final UserInfo userInfo = CacheDataUtils.findUserById(laborCostDetailDto.getUserId());
            laborCostDetailDto.setMipName(userInfo.getUsername());
            costCollectionIdList.add(laborCostDetailDto.getCostCollectionId());

            Long projectId = laborCostDetailDto.getProjectId();
            String userType = laborCostDetailDto.getUserType();
            // 工时成本入账单详情的 applyDate 对应的 applyMonth 都相同
            String key = projectId + "-" + userType;
            laborCostDetailDtoMap.put(key, laborCostDetailDto);
        }

        logger.info("工时成本入账单详情导出laborCostDetailDtoList：{}", JSONObject.toJSONString(laborCostDetailDtoList));
        logger.info("工时成本入账单详情导出laborCostDetailDtoMap：{}", JSONObject.toJSONString(laborCostDetailDtoMap));
        logger.info("工时成本入账单详情导出costCollectionIdList：{}", costCollectionIdList.toString());
        CostCollectionExample example = new CostCollectionExample();
        example.createCriteria().andIdIn(costCollectionIdList).andDeletedFlagEqualTo(Boolean.FALSE);
        List<CostCollection> costCollectionList = costCollectionMapper.selectByExample(example);
        List<CostCollectionDto> costCollectionDtoList = new ArrayList<>();
        for (CostCollection costCollection : costCollectionList) {
            CostCollectionDto costCollectionDto = BeanConverter.copy(costCollection, CostCollectionDto.class);
            Long projectId = costCollectionDto.getProjectId();
            String innerKey = projectId + "-" + "1"; //内部
            String outerKey = projectId + "-" + "3"; //自招外包
            // 内部
            if (laborCostDetailDtoMap.containsKey(innerKey)) {
                final LaborCostDetailDto laborCostDetailDto = laborCostDetailDtoMap.get(innerKey);
                costCollectionDto.setInnerLaborCost(laborCostDetailDto.getCostTotal());
                costCollectionDto.setInnerWorkingHour(laborCostDetailDto.getActualWorkingHours());
            }
            // 外部
            if (laborCostDetailDtoMap.containsKey(outerKey)) {
                final LaborCostDetailDto laborCostDetailDto = laborCostDetailDtoMap.get(outerKey);
                costCollectionDto.setOuterLaborCost(laborCostDetailDto.getCostTotal());
                costCollectionDto.setOuterWorkingHour(laborCostDetailDto.getActualWorkingHours());
            }

            // 工时成本（跨单位）
            List<LaborCostDetailDto> dtoList = laborCostDetailDtoList.stream().filter(e -> Objects.equals(e.getCostCollectionId(), costCollectionDto.getId())
                    && e.getIsCrossUnitLaborCost()).collect(Collectors.toList());
            BigDecimal crossUnitLaborCost = CollectionUtils.isEmpty(dtoList) ? BigDecimal.ZERO
                    : dtoList.stream().map(e -> Optional.ofNullable(e.getCostTotal()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            costCollectionDto.setCrossUnitLaborCost(crossUnitLaborCost.setScale(2, BigDecimal.ROUND_HALF_UP));
            costCollectionDtoList.add(costCollectionDto);
        }

        responseMap.put("costCollectionDtoList", costCollectionDtoList);
        responseMap.put("laborCostDetailDtoList", laborCostDetailDtoList);
        return responseMap;
    }

    @Transactional
    @Override
    public void saveCostCollectionDetail(Long asyncRequestResultId, String accountingBatchNum,
                                         List<CostCollectionDto> costCollectionDtoList, Date glDate, Long unitId) {
        AsyncCostCollectionResult asyncCostCollectionResult = new AsyncCostCollectionResult();
        asyncCostCollectionResult.setId(asyncRequestResultId);
        try {
            logger.info("开始-工时成本统计");
            // 根据月份+项目重新查询汇总，防止前端传参数据有时差
            costCollectionDtoList = accountingExtMapper.getCostCollectionInfo(accountingBatchNum);

            // 按照出勤月份汇总生成入账单，多个出勤月份生成多笔入账单
            final Map<String, List<CostCollectionDto>> applyMonthMap = collectByApplyMonth(costCollectionDtoList);

            final Set<Map.Entry<String, List<CostCollectionDto>>> entries = applyMonthMap.entrySet();
            for (Map.Entry<String, List<CostCollectionDto>> entry : entries) {
                final String monthStr = entry.getKey();
                String[] monthArr = monthStr.split("_");
                final List<CostCollectionDto> costCollectionDTOs = entry.getValue();
                save(costCollectionDTOs, accountingBatchNum, glDate, monthArr[0], monthArr[1], unitId);
                logger.info("工时成本统计生成入账单成功,{},{},{},{},{}", accountingBatchNum, glDate, monthArr[0], monthArr[1], unitId);
            }
            asyncCostCollectionResult.setResponseCode(ErrorCode.SUCCESS.getCode());
            asyncCostCollectionResult.setResponseMsg(ErrorCode.SUCCESS.getMsg());
            asyncCostCollectionResult.setResponseData(null);
            asyncCostCollectionResult.setApplyMonth(DateUtils.format(glDate, "yyyy-MM"));
            asyncCostCollectionResultMapper.updateByPrimaryKeySelective(asyncCostCollectionResult);
            logger.info("修改工时成本统计状态成功");
        } catch (Exception e) {
            logger.error("工时成本统计生成入账单异常", e);
            asyncCostCollectionResult.setResponseCode(ErrorCode.ERROR.getCode());
            asyncCostCollectionResult.setResponseMsg(ErrorCode.ERROR.getMsg());
            asyncCostCollectionResult.setResponseData(null);
            asyncCostCollectionResult.setApplyMonth(DateUtils.format(glDate, "yyyy-MM"));
            asyncCostCollectionResultMapper.updateByPrimaryKeySelective(asyncCostCollectionResult);
            //回滚状态为“未入账”
            updateCancelLaborCostDetail(accountingBatchNum, costCollectionDtoList);
            logger.error("回滚工时成本统计状态");
        }
    }

    private void updateDoneLaborCostDetail(String accountingBatchNum, List<CostCollectionDto> costCollectionDtoList) {
        costCollectionDtoList.forEach(c -> {
            String applyMonth = c.getApplyMonth();
            String approveMonth = c.getApproveMonth();
            Long projectId = c.getProjectId();
            costCollectionExtMapper.updateDoneLaborCostDetail(accountingBatchNum, applyMonth, approveMonth, projectId);
        });
    }

    private void updateCancelLaborCostDetail(String accountingBatchNum, List<CostCollectionDto> costCollectionDtoList) {
        costCollectionDtoList.forEach(c -> {
            String applyMonth = c.getApplyMonth();
            String approveMonth = c.getApproveMonth();
            Long projectId = c.getProjectId();
            costCollectionExtMapper.updateCancelLaborCostDetail(accountingBatchNum, applyMonth, approveMonth, projectId);
        });
    }

    private int updateToDoLaborCostDetail(String accountingBatchNum, List<CostCollectionDto> costCollectionDtoList) {
        int count = 0;
        for (CostCollectionDto costCollectionDto : costCollectionDtoList) {
            String applyMonth = costCollectionDto.getApplyMonth();
            String approveMonth = costCollectionDto.getApproveMonth();
            Long projectId = costCollectionDto.getProjectId();
            count += costCollectionExtMapper.updateToDoLaborCostDetail(accountingBatchNum, applyMonth, approveMonth, projectId);
        }
        return count;
    }

    @Override
    @Transactional
    public Boolean redDashed(WorkingHourAccountingDto dto) {
        Long userId = SystemContext.getUserId();
        Date date = new Date();

        WorkingHourAccounting workingHourAccounting = accountingMapper.selectByPrimaryKey(dto.getId());
        // 冲销标识设为: 已冲销
        workingHourAccounting.setWriteOffStatus(2);
        accountingMapper.updateByPrimaryKeySelective(workingHourAccounting);

        //生成负数的工时入账单
        workingHourAccounting.setId(null);
        workingHourAccounting.setCode(workingHourAccounting.getCode() + "-1");
        workingHourAccounting.setTotalWorkingHour(workingHourAccounting.getTotalWorkingHour().multiply(BigDecimal.valueOf(-1)));
        workingHourAccounting.setTotalLaborCost(workingHourAccounting.getTotalLaborCost().multiply(BigDecimal.valueOf(-1)));
        workingHourAccounting.setGlPeriod(dto.getGlPeriod());
        workingHourAccounting.setErpStatus(CommonErpStatus.PUSHING.code());
        workingHourAccounting.setErpMessage(null);
        workingHourAccounting.setRemark(null);
        workingHourAccounting.setCreateAt(date);
        workingHourAccounting.setCreateBy(userId);
        workingHourAccounting.setUpdateAt(null);
        workingHourAccounting.setUpdateBy(null);
        workingHourAccounting.setWriteOffReason(dto.getWriteOffReason());
        workingHourAccounting.setWriteOffUser(userId);
        workingHourAccounting.setWriteOffTime(new Date());
        // 冲销标识设为: 不可冲销
        workingHourAccounting.setWriteOffStatus(3);

        // 工时入帐单红冲后，会计期间=当前月份，就等于当前日期，当会计期间不等于当前月份，入账日期取会计期间的最后一天
        String glPeriod = dto.getGlPeriod();
        String currentMonth = DateUtils.format(date, DateUtils.FORMAT_YEAR_MONTH);
        if (Objects.equals(glPeriod, currentMonth)) {
            workingHourAccounting.setGlDate(date);
        } else {
            Date beginningDate = DateUtil.getBeginningOfMonth(glPeriod);
            Date endingDate = DateUtil.getEndOfMonth(beginningDate);
            workingHourAccounting.setGlDate(endingDate);
        }

        accountingMapper.insertSelective(workingHourAccounting);

        //生成负数工时入帐单明细
        WorkingHourAccountingDetailExample example = new WorkingHourAccountingDetailExample();
        example.createCriteria().andWorkingHourAccountingIdEqualTo(dto.getId()).andDeletedFlagEqualTo(false);
        List<WorkingHourAccountingDetail> workingHourAccountingDetails = accountingDetailMapper.selectByExample(example);
        Assert.notEmpty(workingHourAccountingDetails, "工时入账单明细不能为空");

        for (WorkingHourAccountingDetail workingHourAccountingDetail : workingHourAccountingDetails) {
            workingHourAccountingDetail.setId(null);
            workingHourAccountingDetail.setWorkingHourAccountingId(workingHourAccounting.getId());
            workingHourAccountingDetail.setInnerLaborCost(workingHourAccountingDetail.getInnerLaborCost() == null ?
                    BigDecimal.ZERO : workingHourAccountingDetail.getInnerLaborCost().multiply(BigDecimal.valueOf(-1)));
            workingHourAccountingDetail.setInnerWorkingHour(workingHourAccountingDetail.getInnerWorkingHour() == null ?
                    BigDecimal.ZERO : workingHourAccountingDetail.getInnerWorkingHour().multiply(BigDecimal.valueOf(-1)));
            workingHourAccountingDetail.setOuterLaborCost(workingHourAccountingDetail.getOuterLaborCost() == null ?
                    BigDecimal.ZERO : workingHourAccountingDetail.getOuterLaborCost().multiply(BigDecimal.valueOf(-1)));
            workingHourAccountingDetail.setOuterWorkingHour(workingHourAccountingDetail.getOuterWorkingHour() == null ?
                    BigDecimal.ZERO : workingHourAccountingDetail.getOuterWorkingHour().multiply(BigDecimal.valueOf(-1)));
            workingHourAccountingDetail.setCreateAt(date);
            workingHourAccountingDetail.setCreateBy(userId);
            workingHourAccountingDetail.setUpdateAt(null);
            workingHourAccountingDetail.setUpdateBy(null);
            accountingDetailMapper.insertSelective(workingHourAccountingDetail);
        }

        //生成负数科目明细
        WorkingHourAccountingDetailSubjectExample query = new WorkingHourAccountingDetailSubjectExample();
        query.createCriteria().andWorkingHourAccountingIdEqualTo(dto.getId()).andDeletedFlagEqualTo(false);
        List<WorkingHourAccountingDetailSubject> workingHourAccountingDetailSubjects = workingHourAccountingDetailSubjectMapper.selectByExample(query);
        Assert.notEmpty(workingHourAccountingDetailSubjects, "科目明细为空");

        for (WorkingHourAccountingDetailSubject workingHourAccountingDetailSubject : workingHourAccountingDetailSubjects) {
            workingHourAccountingDetailSubject.setId(null);
            workingHourAccountingDetailSubject.setWorkingHourAccountingId(workingHourAccounting.getId());
            workingHourAccountingDetailSubject.setLaborCost(workingHourAccountingDetailSubject.getLaborCost().multiply(BigDecimal.valueOf(-1)));
            workingHourAccountingDetailSubject.setWorkingHour(workingHourAccountingDetailSubject.getWorkingHour().multiply(BigDecimal.valueOf(-1)));
            workingHourAccountingDetailSubject.setCreateAt(date);
            workingHourAccountingDetailSubject.setCreateBy(userId);
            workingHourAccountingDetailSubject.setUpdateAt(null);
            workingHourAccountingDetailSubject.setUpdateBy(null);
            workingHourAccountingDetailSubjectMapper.insertSelective(workingHourAccountingDetailSubject);
        }

        LaborCostDetailExample queryBean = new LaborCostDetailExample();
        queryBean.createCriteria().andWorkingHourAccountingIdEqualTo(dto.getId()).andDeletedFlagEqualTo(false);
        List<LaborCostDetail> laborCostDetails = laborCostDetailMapper.selectByExample(queryBean);
        Assert.notEmpty(laborCostDetails, "人工成本不存在");

        //新增冲销关系表数据
        for (LaborCostDetail laborCostDetail : laborCostDetails) {
            WorkingHourWriteoffLaborcostRel writeoffLaborcostRel = new WorkingHourWriteoffLaborcostRel();
            writeoffLaborcostRel.setWorkingHourAccountingId(dto.getId());
            writeoffLaborcostRel.setLaborCostDetailId(laborCostDetail.getId());
            writeoffLaborcostRel.setWorkingHourWriteoffId(workingHourAccounting.getId());
            writeoffLaborcostRel.setCreateAt(date);
            writeoffLaborcostRel.setCreateBy(userId);
            writeoffLaborcostRel.setDeletedFlag(false);
            writeoffLaborcostRelMapper.insertSelective(writeoffLaborcostRel);

        }
        //重新参与工时入账
        costCollectionExtMapper.updateLaborCostDetailStatus(dto.getId());

        // 推送erp
        ResendExecute resendExecute = new ResendExecute();
        resendExecute.setApplyNo(workingHourAccounting.getId().toString());
        resendExecute.setSubApplyNo(workingHourAccounting.getId().toString());
        resendExecute.setBusinessType(BusinessTypeEnums.WORKING_HOUR_ACCOUNTING.getCode());
        resendExecute.setSubBusinessType(SubBusinessTypeEnums.WORKING_HOUR_ACCOUNTING.getCode());
        resendExecute.setBatch(false);
        HandleDispatcher.route(resendExecute);
        return true;
    }

}
