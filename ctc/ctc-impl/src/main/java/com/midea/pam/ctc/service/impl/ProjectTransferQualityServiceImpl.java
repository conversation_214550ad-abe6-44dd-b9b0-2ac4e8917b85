package com.midea.pam.ctc.service.impl;

import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetFeeDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectTypeDto;
import com.midea.pam.common.ctc.entity.CodeRule;
import com.midea.pam.common.ctc.entity.CodeRuleExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.vo.ProjectVO;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CostMethod;
import com.midea.pam.common.enums.MilepostStatus;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.TransferProjectState;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.ProjectContractRsExtMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostExtMapper;
import com.midea.pam.ctc.service.CodeRuleService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectTransferQualityService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.service.event.ProjectTransferQualityWorkflowCallbackApprovalEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Description
 * Created by liuqing
 * Date 2021/12/13 17:49
 */
public class ProjectTransferQualityServiceImpl extends ProjectBusinessServiceImpl implements ProjectTransferQualityService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String QUALITY_FEE = "质保费";

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private CodeRuleService codeRuleService;
    @Resource
    private ProjectTypeService projectTypeService;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private ProjectContractRsExtMapper projectContractRsExtMapper;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private WorkflowCallbackService workflowCallbackService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private ProjectMilepostExtMapper projectMilepostExtMapper;


    @Override
    public ProjectDto getBaseInfoByProjectId(Long projectId) {
        CodeRuleExample example = new CodeRuleExample();
        example.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId()).andRuleTypeEqualTo(3).andDeletedFlagEqualTo(Boolean.FALSE);
        List<CodeRule> codeRuleList = codeRuleService.selectByExample(example);
        Guard.notNullOrEmpty(codeRuleList, "为质保期内专用的编码规则不存在");
        CodeRule codeRule = codeRuleList.get(0);
//        ProjectType projectType = projectTypeService.selectByPrimaryKey(codeRule.getProjectTypeId());
        ProjectDto budgetInfo = projectBusinessService.getBudgetInfoById(projectId, null);
        Guard.notNull(budgetInfo, "项目预算信息不存在");

        ProjectDto info = getBaseInfoById(projectId, "");
        ProjectType projectType = projectTypeService.selectByPrimaryKey(info.getProjectType().getTransferProjectTypeId());
        Guard.notNull(projectType, "为质保期内专用的编码规则对应的项目类型不存在");

        ProjectTypeDto projectTypeDto = BeanConverter.copy(projectType, info.getProjectType());
        info.setProjectType(projectTypeDto);
        info.setType(projectType.getId());
        info.setTypeName(projectType.getName());
        String code = info.getCode();
        String suffix = codeRule.getSuffix();
        if (!code.endsWith(suffix)) {
            info.setCode(String.format("%s%s", code, suffix));
        }
        String name = info.getName();
        if (!name.endsWith("（质保期内）")) {
            info.setName(String.format("%s%s", name, "（质保期内）"));
        }
        info.setProjectContractRsDtoList(Collections.emptyList());
        info.setProjectBusinessRsDtoList(Collections.emptyList());
        info.setAssociatedAmount(info.getAmount());
        info.setAmount(BigDecimal.ZERO);
        info.setExcludingTaxAmount(BigDecimal.ZERO);
        info.setContractCode(null);
        info.setContractName(null);
        info.setContractId(null);
        // 转质保项目也要显示客户 2022-03-11
//        info.setCustomerId(null);
//        info.setCustomerName(null);
        info.setStartTime(info.getQualityAssuranceStartTime());
        info.setEndTime(info.getQualityAssuranceEndTime());
        info.setCostMethodMain(projectType.getCostMethod());
        info.setCostMethodMainName(CostMethod.getValue(projectType.getCostMethod()));

        // 质保费
        List<ProjectBudgetFeeDto> fees = new ArrayList<>();
        budgetInfo.getFees().forEach(projectBudgetFeeDto -> {
            if (Objects.equals(projectBudgetFeeDto.getFeeItemName(), QUALITY_FEE)) {
                fees.add(projectBudgetFeeDto);
            }
        });
        info.setFees(fees);
        return info;
    }

    @Override
    public ProjectVO save(ProjectDto projectInfo) {
        Guard.notNull(projectInfo, "保存的项目不能为空");
        Long originalProject = projectInfo.getOriginalProject();
        Guard.notNull(originalProject, "原项目ID不能为空");
        Project project = projectService.selectByPrimaryKey(originalProject);
        Guard.notNull(project, "原项目不能为空");
        if (!Objects.equals(project.getStatus(), ProjectStatus.APPROVALED.getCode())
                && !Objects.equals(project.getStatus(), ProjectStatus.CLOSE.getCode())) {
            throw new BizException(Code.ERROR, String.format("当前项目状态是%s，未到达可转质保条件", ProjectStatus.getValue(project.getStatus())));
        } else {
            if (Objects.equals(project.getStatus(), ProjectStatus.APPROVALED.getCode())) {
                ProjectMilepost projectMilepost = projectMilepostExtMapper.queryFinalAcceptanceNote(originalProject);
                if (projectMilepost == null) {
                    throw new BizException(Code.ERROR, "此项目找不到可转质保的里程碑节点，请联系IT协助处理");
                }
                if (!Objects.equals(projectMilepost.getStatus(), MilepostStatus.PASSED.getCode())) {
                    throw new BizException(Code.ERROR, String.format("项目里程碑【%s】未通过，未到达可转质保条件", projectMilepost.getName()));
                }
            }
            if (projectInfo.getId() == null &&
                    (Objects.equals(project.getTransferProjectState(), TransferProjectState.HAD.getCode())
                            || Objects.equals(project.getTransferProjectState(), TransferProjectState.MARKED_AND_NOT_NEED.getCode()))) {
                throw new BizException(Code.ERROR, String.format("当前项目的转质保状态是%s，不可继续转质保", TransferProjectState.getName(project.getTransferProjectState())));
            }
        }
        project.setTransferProjectState(TransferProjectState.HAD.getCode());
        projectInfo.setIsTransferSubmit(Boolean.TRUE);
        projectInfo.setTransferProjectState(null);
        //转质保流程发起时，会带出旧项目预算，新建项目应该清除id新增保存
        if (projectInfo.getId() == null) {
            projectInfo.getFees().forEach(s -> s.setId(null));
        }
        ProjectVO projectVO = super.save(projectInfo);
        projectService.updateByPrimaryKeySelective(project);
        return projectVO;
    }

    @Override
    public void updateProjectDesign(ProjectDto projectDto, Integer status) {
//        super.updateProjectDesign(projectDto, status);
    }

    @Override
    public void pass(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                     Long createUserId, Long handlerUserId) {
        applicationEventPublisher.publishEvent(new ProjectTransferQualityWorkflowCallbackApprovalEvent(this, formInstanceId, fdInstanceId, formUrl,
                eventName, handlerId, companyId, createUserId, handlerUserId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftSubmit(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId, Long createUserId, String fileId, String fileName, String fileSize) {
        logger.info("转质保立项审批提交审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectTransferQualityWorkflowCallback_draftSubmit_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final Project project = projectService.selectByPrimaryKey(formInstanceId);
                    project.setStatus(ProjectStatus.APPROVALING.getCode());
                    // 保存生成的Excel信息
                    if (StringUtils.isNotEmpty(fileId) && StringUtils.isNotEmpty(fileName)) {
                        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
                        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_DELIVERY_DETAIL.code());
                        ctcAttachmentDto.setDeletedFlag(false);
                        ctcAttachmentDto.setModuleId(formInstanceId);
                        ctcAttachmentDto.setAttachId(Long.valueOf(fileId));
                        ctcAttachmentDto.setFileName(fileName);
                        if (fileSize != null) {
                            ctcAttachmentDto.setFileSize(Long.valueOf(fileSize).intValue());
                        }
                        ctcAttachmentDto.setUpdateAt(new Date());
                        ctcAttachmentDto.setCreateAt(new Date());
                        ctcAttachmentDto.setStatus(1);
                        ctcAttachmentDto.setRemark(formInstanceId + "-2");
                        ctcAttachmentService.add(ctcAttachmentDto);
                    }
                    //项目类型配置为“允许立项时上传详细设计方案”为是的项目
//                    this.updateProjectDesign(BeanConverter.copy(project, ProjectDto.class), CheckStatus.CHECKING.code());
                    projectService.updateByPrimaryKeySelective(project);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("转质保立项审批提交审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("转质保立项审批提交审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("转质保立项审批提交审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refuse(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId, Long createUserId) {
        logger.info("转质保立项审批驳回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectTransferQualityWorkflowCallback_refuse_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final Project project = projectService.selectByPrimaryKey(formInstanceId);
                    project.setStatus(ProjectStatus.REFUSE.getCode());
                    //项目类型配置为“允许立项时上传详细设计方案”为是的项目
//                    this.updateProjectDesign(BeanConverter.copy(project, ProjectDto.class), CheckStatus.REJECT.code());
                    projectService.updateByPrimaryKeySelective(project);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("转质保立项审批驳回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("转质保立项审批驳回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("转质保立项审批驳回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftReturn(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId, Long createUserId) {
        logger.info("转质保立项审批撤回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectTransferQualityWorkflowCallback_draftReturn_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final Project project = projectService.selectByPrimaryKey(formInstanceId);
                    project.setStatus(ProjectStatus.SAVE.getCode());
                    //项目类型配置为“允许立项时上传详细设计方案”为是的项目
//                    this.updateProjectDesign(BeanConverter.copy(project, ProjectDto.class), CheckStatus.RETURN.code());
                    projectService.updateByPrimaryKeySelective(project);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("转质保立项审批撤回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("转质保立项审批撤回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("转质保立项审批撤回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void abandon(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId, Long createUserId) {
        logger.info("转质保立项审批作废回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectTransferQualityWorkflowCallback_abandonCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final Project project = projectService.selectByPrimaryKey(formInstanceId);
                    if (Objects.equals(project.getStatus(), ProjectStatus.INVALID.getCode())) {
                        return;
                    }
                    project.setStatus(ProjectStatus.INVALID.getCode());
                    //释放资源计划
                    projectService.releaseResourcePlan(project);
                    projectService.updateByPrimaryKeySelective(project);
                    //转质保项目作废处理
                    projectService.dealWithTransferProjectAbandon(project.getId());

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("转质保立项审批作废回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("转质保立项审批作废回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("转质保立项审批作废回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId, Long createUserId) {
        logger.info("转质保立项审批删除回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectTransferQualityWorkflowCallback_deleteCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    final Project project = projectService.selectByPrimaryKey(formInstanceId);
                    project.setStatus(ProjectStatus.INVALID.getCode());
                    //释放资源计划
                    projectService.releaseResourcePlan(project);
                    projectService.updateByPrimaryKeySelective(project);
                    //转质保项目作废处理
                    projectService.dealWithTransferProjectAbandon(project.getId());

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("转质保立项审批删除回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("转质保立项审批删除回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("转质保立项审批删除回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }
}
