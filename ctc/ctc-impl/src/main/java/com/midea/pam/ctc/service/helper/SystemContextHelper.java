package com.midea.pam.ctc.service.helper;

import com.midea.mcomponent.security.common.util.UserUtils;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2025-07-06
 * @description 线程上下文处理
 */
public class SystemContextHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public void dealUserInfo(Long createUserId, Long currentUnitId) {
        if (null == createUserId) {
            UserUtils.setUserTL(null, null, null, null);
            SystemContext.remove();
            return;
        }
        UserInfo userInfo = CacheDataUtils.findUserById(createUserId);
        if (userInfo == null) {
            logger.info("用户不存在，createUserId：{}，currentUnitId：{}", createUserId, currentUnitId);
            UserUtils.setUserTL(null, null, null, null);
            SystemContext.remove();
            return;
        }
        UserInfoDto user = new UserInfoDto();
        user.setId(userInfo.getId());
        user.setUsername(userInfo.getUsername());
        user.setName(userInfo.getName());
        if (currentUnitId != null) {
            user.setUnitId(currentUnitId);
            Unit currentUnit = CacheDataUtils.findUnitById(currentUnitId);
            user.setUnitName(currentUnit.getUnitName());
        }
        SystemContext.set(user);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(userInfo.getUsername())) {
            UserUtils.setUserTL(null, userInfo.getUsername(), null, null);
        }
    }

}
