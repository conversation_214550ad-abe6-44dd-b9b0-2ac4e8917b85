package com.midea.pam.ctc.contract.service.helper;

import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.ctc.dto.ContractHisDTO;
import com.midea.pam.common.ctc.dto.InvoicePlanDetailHisDTO;
import com.midea.pam.common.ctc.dto.InvoicePlanHisDTO;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetails;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetailsExample;
import com.midea.pam.common.ctc.entity.InvoiceApplyHeader;
import com.midea.pam.common.ctc.entity.InvoicePlanDetailHis;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.ctc.contract.service.ContractService;
import com.midea.pam.ctc.mapper.ContractMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailsMapper;
import com.midea.pam.ctc.mapper.InvoiceApplyHeaderMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailHisMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailMapper;
import com.midea.pam.ctc.mapper.InvoicePlanMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimContractRelMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailHisMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;



/**
 * @program: common-module
 * @description: 合同信息变更校验类
 * @author:zhongpeng
 * @create:2019-12-06 16:38
 **/
public class ContractModifyCheck {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ContractService contractService;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private InvoiceApplyDetailsMapper invoiceApplyDetailsMapper;
    @Resource
    private InvoiceApplyHeaderMapper invoiceApplyHeaderMapper;
    @Resource
    private ReceiptClaimContractRelMapper receiptClaimContractRelMapper;
    @Resource
    private InvoicePlanDetailHisMapper invoicePlanDetailHisMapper;
    @Resource
    private InvoicePlanMapper invoicePlanMapper;
    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;
    @Resource
    private ReceiptPlanMapper receiptPlanMapper;
    @Resource
    private ReceiptPlanDetailMapper receiptPlanDetailMapper;
    @Resource
    private ReceiptPlanDetailHisMapper receiptPlanDetailHisMapper;

    /**
     *  对合同变更提交的数据进行业务规则校验
     * @param contractHisDTO
     * @param changeType
     */
    public void baseInfoParamCheck(ContractHisDTO contractHisDTO,Integer changeType){
        //基本数据校验
        Assert.notNull(contractHisDTO.getName(), "合同名称不能为空");
        Assert.notNull(contractHisDTO.getCustomerId(), "客户不能为空");
        Assert.notNull(contractHisDTO.getStartTime(), "合同有效期不能为空");
        Assert.notNull(contractHisDTO.getEndTime(), "合同有效期不能为空");
//        Assert.notNull(contractHisDTO.getOriginalContractAnnex(), "合同原件不能为空");
        Assert.notNull(contractHisDTO.getOuId(), "所属业务实体不能为空");
        Assert.notNull(contractHisDTO.getUnitId(), "销售部门不能为空");
        Assert.notNull(contractHisDTO.getSalesManager(), "销售经理不能为空");
        List<ContractHisDTO> childrenContractHisDTOS = contractHisDTO.getChildrenContractHiss();
        for (ContractHisDTO childrenContractHisDTO : childrenContractHisDTOS) {
            if (childrenContractHisDTO.getOperationType()==null){
                Contract OldContract = contractMapper.selectByPrimaryKey(childrenContractHisDTO.getContractId());
                if (childrenContractHisDTO.getProject()!=null){
                    if ((childrenContractHisDTO.getProject().getStatus()==ProjectStatus.TERMINATIONING.getCode()
                    || childrenContractHisDTO.getProject().getStatus()==ProjectStatus.TERMINATION.getCode())
                    && OldContract.getAmount().compareTo(childrenContractHisDTO.getAmount())!=0){
                        String msg = childrenContractHisDTO.getProject().getStatus() == ProjectStatus.TERMINATIONING.getCode()?"终止审批中":"已终止";
                        throw new BizException(ErrorCode.ERROR,"子合同"+childrenContractHisDTO.getName()+"对应的项目"+msg+"，不允许变更合同金额");
                    }
                    if (childrenContractHisDTO.getProject().getStatus() == ProjectStatus.CLOSE.getCode() && OldContract.getAmount().compareTo(childrenContractHisDTO.getAmount())!=0){
                        throw new BizException(ErrorCode.ERROR,"子合同"+childrenContractHisDTO.getName()+"对应的项目已结项，不允许变更合同金额");
                    }
                }
                Long contractId = childrenContractHisDTO.getContractId();
                Contract childrenContract = contractMapper.selectByPrimaryKey(contractId);
                // 开票计划
//                InvoicePlanExample invoicePlanExample = new InvoicePlanExample();
//                InvoicePlanExample.Criteria invoicePlanCriteria = invoicePlanExample.createCriteria();
//                invoicePlanCriteria.andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE);
//                List<InvoicePlan> invoicePlanList = invoicePlanMapper.selectByExample(invoicePlanExample);
                //回款计划
//                ReceiptPlanExample receiptPlanExample = new ReceiptPlanExample();
//                ReceiptPlanExample.Criteria receiptPlanCriteria = receiptPlanExample.createCriteria();
//                receiptPlanCriteria.andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE);
//                List<ReceiptPlan> receiptPlanList = receiptPlanMapper.selectByExample(receiptPlanExample);

                //取该子合同是否有对应的开票申请
                Boolean isInvoice = false;
                InvoiceApplyDetailsExample invoiceApplyDetailsExample = new InvoiceApplyDetailsExample();
                InvoiceApplyDetailsExample.Criteria invoiceApplyDetailsCriteria= invoiceApplyDetailsExample.createCriteria();
                invoiceApplyDetailsCriteria.andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE);
                List<InvoiceApplyDetails> invoiceApplyDetails = invoiceApplyDetailsMapper.selectByExample(invoiceApplyDetailsExample);
                if (CollectionUtils.isNotEmpty(invoiceApplyDetails)){
                    BigDecimal totalTaxIncludedPrice = BigDecimal.ZERO;
                    for (InvoiceApplyDetails invoiceApplyDetail : invoiceApplyDetails) {
                        BigDecimal taxIncludedPrice = invoiceApplyDetail.getTaxIncludedPrice()==null?BigDecimal.ZERO:invoiceApplyDetail.getTaxIncludedPrice();
                        InvoiceApplyHeader invoiceApplyHeader = invoiceApplyHeaderMapper.selectByPrimaryKey(invoiceApplyDetail.getApplyHeaderId());
                        if (invoiceApplyHeader.getStatus() != -1 && invoiceApplyHeader.getDeletedFlag() != Boolean.TRUE && invoiceApplyDetail.getStatus() != -1){
                            totalTaxIncludedPrice = totalTaxIncludedPrice.add(taxIncludedPrice);
                        }
                    }
                    if (!BigDecimalUtils.equals(totalTaxIncludedPrice,BigDecimal.ZERO)){
                        isInvoice = Boolean.TRUE;
                    }else {
                        isInvoice = Boolean.FALSE;
                    }

                }
                //取该子合同是否有对应的回款
//                Boolean isReceipt = false;
//                List<ReceiptClaimContractRel> receiptClaimContractRelList = new ArrayList<>();
//                ReceiptClaimContractRelExample receiptClaimContractRelExample = new ReceiptClaimContractRelExample();
//                ReceiptClaimContractRelExample.Criteria receiptClaimContractRelCriteria = receiptClaimContractRelExample.createCriteria();
//                receiptClaimContractRelCriteria.andContractIdEqualTo(contractId).andDeletedFlagEqualTo(0);
//                receiptClaimContractRelList = receiptClaimContractRelMapper.selectByExample(receiptClaimContractRelExample);
//                if (CollectionUtils.isNotEmpty(receiptClaimContractRelList)&&receiptClaimContractRelList.size()>0){
//                    isReceipt =  true;
//                }

                //取页面提交的客户
                Long customerId = childrenContractHisDTO.getCustomerId();
                //取页面上开票计划行
                List<InvoicePlanHisDTO> invoicePlanHisDTOS = childrenContractHisDTO.getInvoicePlanHisDTOs();
                if (CollectionUtils.isNotEmpty(invoicePlanHisDTOS)){
                    for (InvoicePlanHisDTO invoicePlanHisDTO : invoicePlanHisDTOS) {
                        List<InvoicePlanDetailHisDTO> invoicePlanDetailHiss= invoicePlanHisDTO.getInvoicePlanDetailsHiss();
                        if (CollectionUtils.isNotEmpty(invoicePlanDetailHiss)){
                            String InvoicePlanDetailId = "";
                            for (InvoicePlanDetailHis planDetailHiss : invoicePlanDetailHiss) {
                                if (Objects.equals(changeType, 2) || Objects.equals(changeType, 3)) {
                                    Assert.notNull(planDetailHiss.getDate(), "计划开票日期不能为空");
                                }
                                InvoicePlanDetailId = planDetailHiss.getInvoicePlanDetailId()+InvoicePlanDetailId;
                                logger.info("InvoicePlanDetailId:"+InvoicePlanDetailId);
                            }
                        }
                    }
                }

                //合同已经开过票不允许变更客户
                if (isInvoice==true&&!customerId.equals(childrenContract.getCustomerId())){
                    throw new MipException("该合同已经开过票，不允许变更客户！");
                }
            }

        }
    }




    public static boolean compareString(String a,String b){
        byte[] b1 = a.getBytes();
        byte[] b2 = b.getBytes();
        int[] bCount = new int[256];
        for(int i=0;i<256;i++){
            bCount[i] = 0;
        }
        for(int i=0;i<b1.length;i++)
            bCount[b1[i]-'0']++;
        for(int i=0;i<b2.length;i++)
            bCount[b2[i]-'0']--;
        for(int i=0;i<256;i++){
            if(bCount[i]!=0)
                return false;
        }
        return true;
    }


}
