package com.midea.pam.ctc.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.BrandMaintenance;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.cache.MaterialImportCache;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.MaterialAdjustDetailDTO;
import com.midea.pam.common.ctc.dto.MaterialAdjustHeaderDTO;
import com.midea.pam.common.ctc.dto.MaterialAttributeImportDto;
import com.midea.pam.common.ctc.dto.MaterialCustomDictHeaderDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanChangeRecordDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailChangeDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.entity.MaterialAdjustDetail;
import com.midea.pam.common.ctc.entity.MaterialAdjustDetailExample;
import com.midea.pam.common.ctc.entity.MaterialAdjustHeader;
import com.midea.pam.common.ctc.entity.MaterialAdjustHeaderExample;
import com.midea.pam.common.ctc.entity.MaterialCustomDict;
import com.midea.pam.common.ctc.entity.MaterialCustomDictHeader;
import com.midea.pam.common.ctc.entity.MaterialCustomDictHeaderExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanChangeRecord;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.excelVo.DesignPlanDetailChangeImportExcelVO;
import com.midea.pam.common.ctc.excelVo.MaterialAdjustDetailImportExcelVo;
import com.midea.pam.common.ctc.vo.DesignPlanDetailChangeExportExcelVO;
import com.midea.pam.common.ctc.vo.MaterialChangeTypeVo;
import com.midea.pam.common.ctc.vo.MerielSubclassVO;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CommonStatus;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.MaterialAdjustEnum;
import com.midea.pam.common.enums.MaterialCodeRuleEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.NoticeBusinessType;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.UserInfoUtile;
import com.midea.pam.common.util.cache.MaterialImportUtils;
import com.midea.pam.common.util.idaas.TwoTuple;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.EmailExtMapper;
import com.midea.pam.ctc.mapper.FormInstanceExtMapper;
import com.midea.pam.ctc.mapper.MaterialAdjustDetailMapper;
import com.midea.pam.ctc.mapper.MaterialAdjustExtMapper;
import com.midea.pam.ctc.mapper.MaterialAdjustHeaderMapper;
import com.midea.pam.ctc.mapper.MaterialCustomDictExtMapper;
import com.midea.pam.ctc.mapper.MaterialCustomDictHeaderMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanChangeRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.OrganizationCustomDictExtMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.ErpCodeRuleService;
import com.midea.pam.ctc.service.MaterialAdjustService;
import com.midea.pam.ctc.service.MaterialCustomDictService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.TicketTasksService;
import com.midea.pam.ctc.service.event.MaterialAdjustApprovalEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * <AUTHOR>
 * @date 2021/2/25
 * @desc 详细设计-物料新增和变更服务
 */
public class MaterialAdjustServiceImpl implements MaterialAdjustService {

    private static final Logger logger = LoggerFactory.getLogger(MaterialAdjustServiceImpl.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    private final static String MACHINED_PART_TYPE = "jiagongjian_type";

    private final static String MATERIAL_PREFIX = "WL";

    private final static String MATERIAL_TYPE = "material_type";

    @Value("${route.materialAdjustDetailUrl}")
    private String materialAdjustDetailUrl;
    @Qualifier("importMaterialAdjustDetailFlexibleExecutor")
    @Resource
    private ThreadPoolTaskExecutor importMaterialAdjustDetailFlexibleExecutor;
    @Resource
    private MaterialAdjustHeaderMapper materialAdjustHeaderMapper;
    @Resource
    private MaterialAdjustDetailMapper materialAdjustDetailMapper;
    @Resource
    private MaterialAdjustExtMapper materialAdjustExtMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private ErpCodeRuleService erpCodeRuleService;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private MaterialCustomDictService materialCustomDictService;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;
    @Resource
    private ProjectExtMapper projectExtMapper;
    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;
    @Resource
    private TicketTasksService ticketTasksService;
    @Resource
    private MilepostDesignPlanChangeRecordMapper milepostDesignPlanChangeRecordMapper;
    @Resource
    private MilepostDesignPlanDetailChangeService milepostDesignPlanDetailChangeService;
    @Resource
    private EmailExtMapper emailExtMapper;
    @Resource
    private OrganizationCustomDictExtMapper organizationCustomDictExtMapper;
    @Resource
    private MaterialCustomDictHeaderMapper materialCustomDictHeaderMapper;
    @Resource
    private MaterialCustomDictExtMapper materialCustomDictExtMapper;
    @Resource
    private CtcAttachmentService ctcAttachmentService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private WorkflowCallbackService workflowCallbackService;

    @Resource
    private FormInstanceExtMapper formInstanceExtMapper;

    @Override
    public PageInfo<MaterialAdjustHeaderDTO> page(Integer pageNum, Integer pageSize, MaterialAdjustHeaderDTO query) {
        // 获取用户当前使用单位ouIdList作为过滤条件（根据使用单位过滤数据）
        List<Long> ouIdList =
                basedataExtService.queryCurrentUnitOu().stream().map(OperatingUnit::getOperatingUnitId).collect(Collectors.toList());
        if (!ouIdList.isEmpty()) {
            query.setOuIdList(ouIdList);
        }else{
            // 当用户没有使用单位时，直接返回空集合
            return new PageInfo<>();
        }
        PageHelper.startPage(pageNum, pageSize);
        if(Objects.isNull(query.getMe())){
            query.setMe(false);
        }else{
            query.setCurrentUserId(SystemContext.getUserId());
        }
        List<MaterialAdjustHeader> materialAdjustHeaderList = materialAdjustExtMapper.selectByDto(query);
        PageInfo<MaterialAdjustHeaderDTO> pageInfo = BeanConverter.convertPage(materialAdjustHeaderList, MaterialAdjustHeaderDTO.class);
        for (MaterialAdjustHeaderDTO materialAdjustHeaderDTO : pageInfo.getList()) {
            packageMaterialAdjustHeaderDTO(materialAdjustHeaderDTO);
        }
        return pageInfo;
    }

    @Override
    public MaterialAdjustHeaderDTO getDetailById(Long id) {
        //头信息
        MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(id);
        Asserts.notNull(materialAdjustHeader, ErrorCode.CTC_MATERIAL_ADJUST_HEADER_NOT_NULL);
        MaterialAdjustHeaderDTO materialAdjustHeaderDTO = BeanConverter.copy(materialAdjustHeader, MaterialAdjustHeaderDTO.class);
        materialAdjustHeaderDTO.setAdjustTypeName(MaterialAdjustEnum.getMsg(materialAdjustHeaderDTO.getAdjustType()));
        //物料明细
        List<MaterialAdjustDetailDTO> detailDTOList = getMaterialAdjustDetailDTOS(materialAdjustHeaderDTO.getId());
        materialAdjustHeaderDTO.setDetailDTOList(detailDTOList);
        //附件
        List<CtcAttachmentDto> ctcAttachmentDtos =
                ctcAttachmentService.selectListByModuleAndModuleId(CtcAttachmentModule.MATERIAL_ADJUST_ADD_APP.code(), id);
        if (!ctcAttachmentDtos.isEmpty()) {
            materialAdjustHeaderDTO.setCtcAttachmentDto(ctcAttachmentDtos.get(0));
        }
        packageMaterialAdjustHeaderDTO(materialAdjustHeaderDTO);
        return materialAdjustHeaderDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MaterialAdjustHeaderDTO saveWithDetail(MaterialAdjustHeaderDTO dto) {
        MaterialAdjustHeaderDTO headerDTO = new MaterialAdjustHeaderDTO();
        String lockName = "MaterialAdjust_saveWithDetail_Name";
        String lockValue = "MaterialAdjust_saveWithDetail_Value";
        Boolean changeHandleFlag = false;
        String oldHandleUserName = "";
        try {
            if (DistributedCASLock.lock(lockName, lockValue, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                if (Objects.nonNull(dto.getId())) {
                    MaterialAdjustHeader adjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(dto.getId());
                    Guard.notNull(adjustHeader, String.format("物料调整单[%s]不存在", dto.getId()));
                    if (Objects.equals(adjustHeader.getStatus(), MaterialAdjustEnum.APPROVED.code())) {
                        return getDetailById(dto.getId());
                    }
                    //判断是否修改处理人
                    Long handleByOld = adjustHeader.getHandleBy();

                    Long handleByNew = dto.getHandleBy();

                    if(!Objects.equals(handleByNew,handleByOld)){
                        Integer status = adjustHeader.getStatus();
                        if(!Objects.equals(status,MaterialAdjustEnum.DRAFT.code()) && !Objects.equals(status,MaterialAdjustEnum.REJECT.code())){
                            throw new BizException(Code.ERROR,"当前单据状态不能修改处理人");
                        }

                        changeHandleFlag = Boolean.TRUE;
                        oldHandleUserName = UserInfoUtile.getUserName(handleByOld);
                    }
                }

                // 校验保存入库数据
                checkMaterialAdjustHeaderDTO(dto);
                MaterialAdjustHeader materialAdjustHeader = BeanConverter.copy(dto, MaterialAdjustHeader.class);
                // 保存或修改头表数据
                saveMaterialAdjustHeader(materialAdjustHeader);
                // 保存行表明细数据
                if (CollectionUtils.isNotEmpty(dto.getDetailDTOList())) {
                    List<MaterialAdjustDetail> detailList = BeanConverter.copy(dto.getDetailDTOList(), MaterialAdjustDetail.class);
                    saveMaterialAdjustDetail(materialAdjustHeader.getId(), detailList);
                }

                headerDTO = getDetailById(materialAdjustHeader.getId());
                // 提交标准化
                if (Boolean.FALSE.equals(dto.getSubmitApprove())) {
                    sendEmailForMaterialAdjust(headerDTO, headerDTO.getHandleBy(), NoticeBusinessType.MATERIAL_ADJUST_SUBMIT_STANDARD);
                }
                //设置是否修改处理人标识，返回时gateway服务处理上一个处理人的流程实例
                headerDTO.setChangeHandleFlag(changeHandleFlag);
                headerDTO.setOldHandleUserName(oldHandleUserName);
            }
        } catch (Exception e) {
            logger.error("saveWithDetail加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, lockValue);
        }

        return headerDTO;
    }

    @Override
    public Response validImportDetail(Long organizationId,
                                      List<MaterialAdjustDetailImportExcelVo> detailImportExcelVoList) {
        // 校验当前使用单位的物料编码规则
        String materialCodeRule = materialExtService.getMaterialCodeRule(SystemContext.getUnitId());
        if (!MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
            throw new BizException(100, "物料新增导入数据失败，当前使用单位配置的物料编码规则未找到对应校验逻辑");
        }

        List<MaterialAdjustDetailDTO> materialAdjustDetailDTOList = BeanConverter.copy(detailImportExcelVoList,
                MaterialAdjustDetailDTO.class);
        // 导入数据校验，并返回修改校验结果
        boolean result = getValidDetailResults(organizationId, materialAdjustDetailDTOList);

        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(materialAdjustDetailDTOList);
    }

    /**
     * 昆山物料导入数据校验，并返回修改校验结果
     *
     * @param materialAdjustDetailDTOList
     * @param organizationId
     * @return
     */
    private boolean getValidDetailResults(Long organizationId,
                                          List<MaterialAdjustDetailDTO> materialAdjustDetailDTOList) {
        boolean result = true;

        // 获取类别列表数据（row小类，column中类，value实体类）
        List<MerielSubclassVO> merielSubclassList =
                erpCodeRuleService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUNSHAN.getCode());
        // 用图号+版本号+库存组织判断唯一性的物料中类
        List<String> checkType3MaterialMiddleClassList = merielSubclassList.stream().filter(o -> Objects.equals(3,
                o.getCheckType())).map(MerielSubclassVO::getMiddileValue).distinct().collect(Collectors.toList());
        HashBasedTable<String, String, MerielSubclassVO> merielClassTable = HashBasedTable.create();
        for (MerielSubclassVO merielSubclassVO : merielSubclassList) {
            merielClassTable.put(merielSubclassVO.getValue(), merielSubclassVO.getMiddileValue(), merielSubclassVO);
        }
        // 获取计量单位
        Map<String, String> unitMap =
                basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));
        // 获取加工分类字典值
        List<String> machinedPartTypeList =
                basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null).stream().map(Dict::getName).distinct().collect(Collectors.toList());
        // 获取备件标识组织参数配置
        Set<String> orSparePartsMaskSet = organizationCustomDictService.queryByName(Constants.SPAREPARTS_MASK,
                SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
        if (orSparePartsMaskSet == null || orSparePartsMaskSet.isEmpty()) {
            throw new BizException(100, "备件标识需要在组织参数中维护");
        }
        // 获取物料类型及编码字典
        Map<String, String> materialTypeMap =
                basedataExtService.getLtcDict(MATERIAL_TYPE, null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));

        logger.info("1.导入物料数据基础信息校验开始");
        for (MaterialAdjustDetailDTO detailDTO : materialAdjustDetailDTOList) {
            List<String> validResultList = new ArrayList<>();
            if (!"外购物料".equals(detailDTO.getMaterialType()) || !"看板物料".equals(detailDTO.getMaterialType())) {
                result = false;
                validResultList.add("物料类型只能选择外购物料或者看板物料");
            }
            if (StringUtils.isBlank(detailDTO.getMaterialMiddleClass()) || !merielClassTable.columnKeySet().contains(detailDTO.getMaterialMiddleClass())) {
                result = false;
                validResultList.add("物料中类不能为空且只能为字典项的值");
            } else {
                if (checkType3MaterialMiddleClassList.contains(detailDTO.getMaterialMiddleClass())) {
                    if (StringUtils.isBlank(detailDTO.getFigureNumber())) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 图号不能为空");
                    }
                    if (StringUtils.isBlank(detailDTO.getChartVersion())) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 图纸版本号不能为空");
                    }
                    if (StringUtils.isBlank(detailDTO.getMachiningPartType())) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 加工分类不能为空");
                    }
                    if (StringUtils.isBlank(detailDTO.getMaterial())) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 材质不能为空");
                    }
                    if (detailDTO.getUnitWeight() == null) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 单件重量(Kg)不能为空");
                    }
                    if (StringUtils.isBlank(detailDTO.getSurfaceHandle())) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 表面处理不能为空");
                    }
                }
            }
            if (StringUtils.isBlank(detailDTO.getMaterialSmallClass())) {
                result = false;
                validResultList.add("物料小类不能为空");
            } else {
                if (merielClassTable.rowKeySet().contains(detailDTO.getMaterialSmallClass())) {
                    if (merielClassTable.get(detailDTO.getMaterialSmallClass(), detailDTO.getMaterialMiddleClass()) == null) {
                        result = false;
                        validResultList.add("此物料小类不在填写的中类中");
                    } else {
                        // 根据小类中类获取大类并设置
                        detailDTO.setMaterialClass(merielClassTable.get(detailDTO.getMaterialSmallClass(),
                                detailDTO.getMaterialMiddleClass()).getBigValue());
                    }
                } else {
                    result = false;
                    validResultList.add("物料小类不存在");
                }
            }
            if (StringUtils.isBlank(detailDTO.getBrand())) {
                result = false;
                validResultList.add("品牌不能为空");
            } else {
                if (detailDTO.getBrand().length() > 150) {
                    result = false;
                    validResultList.add("品牌字符长度不能超过150");
                }
            }
            if (StringUtils.isBlank(detailDTO.getName())) {
                result = false;
                validResultList.add("名称不能为空");
            } else {
                if (detailDTO.getName().length() > 150) {
                    result = false;
                    validResultList.add("名称字符长度不能超过150");
                }
            }
            if (StringUtils.isBlank(detailDTO.getModel())) {
                result = false;
                validResultList.add("型号/规格不能为空");
            } else {
                if (detailDTO.getModel().length() > 150) {
                    result = false;
                    validResultList.add("型号/规格字符长度不能超过150");
                }
            }
            if (StringUtils.isBlank(detailDTO.getUnit()) || !unitMap.containsKey(detailDTO.getUnit())) {
                result = false;
                validResultList.add("单位不能为空且必须为字典项measurement_unit的值");
            }
            if (StringUtils.isNotBlank(detailDTO.getFigureNumber()) && detailDTO.getFigureNumber().length() > 150) {
                result = false;
                validResultList.add("图号字符长度不能超过150");
            }
            if (StringUtils.isNotBlank(detailDTO.getChartVersion()) && detailDTO.getChartVersion().length() > 2) {
                result = false;
                validResultList.add("图纸版本号字符长度不能超过2");
            }
            if (StringUtils.isNotBlank(detailDTO.getMachiningPartType()) && !machinedPartTypeList.contains(detailDTO.getMachiningPartType())) {
                result = false;
                validResultList.add("加工分类不为空时只能选择字典项中的值");
            }
            if (StringUtils.isBlank(detailDTO.getOrSparePartsMask()) || !orSparePartsMaskSet.contains(detailDTO.getOrSparePartsMask())) {
                result = false;
                validResultList.add("备件标识不能为空且必须为组织参数中配置的备件标识中的值");
            }
            detailDTO.setValidResult(Joiner.on("，").join(validResultList));
        }

        logger.info("2.导入物料数据基础信息校验结束");

        // 加入限流200
        RateLimiter rateLimiter = RateLimiter.create(200);
        // 判断唯一性
        for (MaterialAdjustDetailDTO detailDTO : materialAdjustDetailDTOList) {
            MerielSubclassVO merielSubclassVO = merielClassTable.get(detailDTO.getMaterialSmallClass(),
                    detailDTO.getMaterialMiddleClass());
            if (merielSubclassVO == null) {
                continue;
            }
            if (null != detailDTO.getBrandMaterialCode()) { //品牌商物料编码+品牌+库存组织判断唯一性
                Material material = materialExtService.judgeUniqueCheckType4(detailDTO.getBrand(),
                        detailDTO.getBrandMaterialCode(), organizationId);
                if (material != null) {
                    result = false;
                    detailDTO.setValidResult(detailDTO.getValidResult() + "物料基础表已存在此品牌+品牌商物料编码+库存组织物料");
                }
            } else {
                // 用品牌+型号+品牌商物料编码+库存组织判断唯一性
                if (Objects.equals(merielSubclassVO.getCheckType(), 2) && StringUtils.isNotBlank(detailDTO.getBrandMaterialCode())) {
                    // 导入数据中存在多条相同的品牌+型号+品牌商物料编码
                    long count = materialAdjustDetailDTOList.stream().filter(e -> Objects.equals(e.getBrand(),
                            detailDTO.getBrand())
                            && Objects.equals(e.getModel(), detailDTO.getModel()) && Objects.equals(e.getBrandMaterialCode(),
                            detailDTO.getBrandMaterialCode())).count();
                    if (count > 1) {
                        result = false;
                        detailDTO.setValidResult(detailDTO.getValidResult() + "，所有导入数据中存在多条相同的品牌：" + detailDTO.getBrand() + "，型号:"
                                + detailDTO.getModel() + "，品牌商物料编码:" + detailDTO.getBrandMaterialCode() + "的数据");
                        continue;
                    }
                    List<String> adjustCodeList = judgeUniqueCheckType2(detailDTO.getBrand(), detailDTO.getModel(),
                            detailDTO.getBrandMaterialCode(), organizationId);
                    if (adjustCodeList.isEmpty()) {
                        rateLimiter.acquire();
                        Material material = materialExtService.judgeUniqueCheckType2(detailDTO.getBrand(),
                                detailDTO.getModel(), detailDTO.getBrandMaterialCode(), organizationId);
                        if (material != null) {
                            result = false;
                            detailDTO.setValidResult(detailDTO.getValidResult() + "物料基础表已存在此品牌+型号+品牌商物料编码+库存组织物料");
                        }
                    } else {
                        result = false;
                        detailDTO.setValidResult(detailDTO.getValidResult() + "，单据号：" + Joiner.on("、").join(adjustCodeList) +
                                "已导入此品牌+型号+品牌商物料编码+库存组织物料");
                    }
                } else if (Objects.equals(merielSubclassVO.getCheckType(), 3)) {
                    // 用图号+版本号+库存组织判断唯一性
                    // 导入数据中存在多条相同的图号+版本号
                    long count = materialAdjustDetailDTOList.stream().filter(e -> Objects.equals(e.getFigureNumber(),
                            detailDTO.getFigureNumber())
                            && Objects.equals(e.getChartVersion(), detailDTO.getChartVersion())).count();
                    if (count > 1) {
                        result = false;
                        detailDTO.setValidResult(detailDTO.getValidResult() + "，所有导入数据中存在多条相同的图号：" + detailDTO.getFigureNumber() + "，版本号:"
                                + detailDTO.getChartVersion() + "的数据");
                        continue;
                    }
                    List<String> adjustCodeList = judgeUniqueCheckType3(detailDTO.getFigureNumber(),
                            detailDTO.getChartVersion(), organizationId);
                    if (adjustCodeList.isEmpty()) {
                        rateLimiter.acquire();
                        Material material = materialExtService.judgeUniqueCheckType3(detailDTO.getFigureNumber(),
                                detailDTO.getChartVersion(), organizationId);
                        if (material != null) {
                            result = false;
                            detailDTO.setValidResult(detailDTO.getValidResult() + "物料基础表已存在此图号+版本号+库存组织物料");
                        }
                    } else {
                        result = false;
                        detailDTO.setValidResult(detailDTO.getValidResult() + "，单据号：" + Joiner.on("、").join(adjustCodeList) + "已导入此图号+版本号+库存组织物料");
                    }
                }
            }
        }

        if (result) {
            for (MaterialAdjustDetailDTO detailDTO : materialAdjustDetailDTOList) {
                // 封装使用单位编码
                detailDTO.setUnitCode(unitMap.get(detailDTO.getUnit()));
                // 封装物料类型编码（对应material_type字典中的code值，对应物料基础表的item_type（物料用户类型））
                detailDTO.setMaterialTypeCode(materialTypeMap.get(detailDTO.getMaterialType()));
                // 生成物料描述
                detailDTO.setItemDes(generateMaterialDes(detailDTO));
                Integer itemDesLength = getOracleLengthFromMysqlVarchar(detailDTO.getItemDes());
                if (itemDesLength > 240) {
                    detailDTO.setValidResult("物料描述的字段长度超过了240个字符");
                    result = false;
                }
                // 生成PAM编码
                MerielSubclassVO merielSubclassVO = merielClassTable.get(detailDTO.getMaterialSmallClass(),
                        detailDTO.getMaterialMiddleClass());
                if (merielSubclassVO.getCheckType() != 2) {
                    detailDTO.setPamCode(materialExtService.generateMaterialPAMCode());
                }
            }
        }
        return result;
    }

    /**
     * 用品牌+型号+品牌商物料编码+库存组织判断唯一性
     *
     * @param brand             品牌
     * @param model             型号
     * @param brandMaterialCode 品牌商物料编码
     * @param organizationId    库存组织
     * @return 返回单据号列表
     */
    private List<String> judgeUniqueCheckType2(String brand, String model, String brandMaterialCode,
                                               Long organizationId) {
        return materialAdjustExtMapper.judgeUniqueCheckType2(brand, model, brandMaterialCode, organizationId);
    }

    /**
     * 用图号+版本号+库存组织判断唯一性
     *
     * @param figureNumber   图号
     * @param chartVersion   版本号
     * @param organizationId 库存组织
     * @return 返回单据号列表
     */
    private List<String> judgeUniqueCheckType3(String figureNumber, String chartVersion, Long organizationId) {
        return materialAdjustExtMapper.judgeUniqueCheckType3(figureNumber, chartVersion, organizationId);
    }

    /**
     * 保存或修改头表数据
     *
     * @param materialAdjustHeader 头表数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveMaterialAdjustHeader(MaterialAdjustHeader materialAdjustHeader) {
        if (materialAdjustHeader.getId() == null) {
            materialAdjustHeader.setApplyBy(SystemContext.getUserId());
            materialAdjustHeader.setApplyByName(SystemContext.getUserName());
            materialAdjustHeader.setApplyTime(new Date());
            materialAdjustHeader.setCreateBy(SystemContext.getUserId());
            materialAdjustHeader.setUpdateBy(SystemContext.getUserId());
            materialAdjustHeader.setAdjustCode(generateMaterialAdjustCode(SystemContext.getUnitId()));
            materialAdjustHeader.setDeletedFlag(Boolean.FALSE);
//            materialAdjustHeader.setStatus(MaterialAdjustEnum.DRAFT.code());
            materialAdjustHeaderMapper.insert(materialAdjustHeader);
        } else {
            materialAdjustHeader.setUpdateBy(SystemContext.getUserId());
            materialAdjustHeader.setUpdateAt(new Date());
            materialAdjustHeaderMapper.updateByPrimaryKey(materialAdjustHeader);
        }
    }

    /**
     * 保存行表数据
     *
     * @param detailList 行表数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveMaterialAdjustDetail(Long headerId, List<MaterialAdjustDetail> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }

        materialAdjustExtMapper.deleteMaterialAdjustDetailByHeaderId(headerId);
        List<MaterialAdjustDetail> materialAdjustDetailList = new ArrayList<>();
        for (MaterialAdjustDetail materialAdjustDetail : detailList) {
            if (StringUtils.isEmpty(materialAdjustDetail.getInventoryType())) {
                materialAdjustDetail.setInventoryType("0.0.0");
            }
            if (Objects.equals(materialAdjustDetail.getMaterialType(), "外购物料")) {
                materialAdjustDetail.setMaterialAttribute("外购件");
            } else if (Objects.equals(materialAdjustDetail.getMaterialType(), "看板物料")) {
                materialAdjustDetail.setMaterialAttribute("看板件");
            }
            materialAdjustDetail.setHeaderId(headerId);
            materialAdjustDetail.setDeletedFlag(Boolean.FALSE);
            materialAdjustDetailList.add(materialAdjustDetail);
        }
        materialAdjustExtMapper.batchInsert(materialAdjustDetailList);
    }

    /**
     * 根据头表ID查询明细数据
     *
     * @param headerId 头表ID
     * @return
     */
    private List<MaterialAdjustDetailDTO> getMaterialAdjustDetailDTOS(Long headerId) {
        MaterialAdjustDetailExample example = new MaterialAdjustDetailExample();
        example.setOrderByClause("create_at desc");
        example.createCriteria().andHeaderIdEqualTo(headerId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<MaterialAdjustDetail> detailList = materialAdjustDetailMapper.selectByExample(example);
        List<MaterialAdjustDetailDTO> detailDTOList = BeanConverter.copy(detailList, MaterialAdjustDetailDTO.class);
        detailDTOList.forEach(e -> e.setSyncStatusName(MaterialAdjustEnum.getMsg(e.getSyncStatus())));
        return detailDTOList;
    }

    /**
     * 根据查询条件封装查询Example对象
     *
     * @param query 查询条件
     * @return
     */
    private MaterialAdjustHeaderExample getMaterialAdjustHeaderExample(MaterialAdjustHeaderDTO query) {
        MaterialAdjustHeaderExample example = new MaterialAdjustHeaderExample();
        MaterialAdjustHeaderExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        if (StringUtils.isNotBlank(query.getAdjustCode())) {
            criteria.andAdjustCodeLike("%" + query.getAdjustCode().trim() + "%");
        }
        if (StringUtils.isNotBlank(query.getAdjustTypeStr())) {
            String[] split = query.getAdjustTypeStr().split(",");
            try {
                List<Integer> adjustTypeList = Stream.of(split).map(Integer::parseInt).collect(Collectors.toList());
                criteria.andAdjustTypeIn(adjustTypeList);
            } catch (Exception e) {
                throw new BizException(100, "变更类型参数错误");
            }
        }
        if (StringUtils.isNotBlank(query.getApplyByName())) {
            criteria.andApplyByNameLike("%" + query.getApplyByName() + "%");
        }
        if (StringUtils.isNotBlank(query.getHandleByName())) {
            criteria.andHandleByNameLike("%" + query.getHandleByName() + "%");
        }
        if (StringUtils.isNotBlank(query.getStatusStr())) {
            String[] split = query.getStatusStr().split(",");
            try {
                List<Integer> statusList = Stream.of(split).map(Integer::parseInt).collect(Collectors.toList());
                criteria.andStatusIn(statusList);
            } catch (Exception e) {
                throw new BizException(100, "审批状态参数错误");
            }
        }
        if (StringUtils.isNotBlank(query.getOrganizationIdStr())) {
            String[] split = query.getOrganizationIdStr().split(",");
            try {
                List<Long> organizationIdList = Stream.of(split).map(Long::parseLong).collect(Collectors.toList());
                criteria.andOrganizationIdIn(organizationIdList);
            } catch (Exception e) {
                throw new BizException(100, "库存组织参数错误");
            }
        }
        if (query.getCreateAtBegin() != null) {
            criteria.andCreateAtGreaterThanOrEqualTo(query.getCreateAtBegin());
        }
        if (query.getCreateAtEnd() != null) {
            criteria.andCreateAtLessThanOrEqualTo(query.getCreateAtEnd());
        }
        if (StringUtils.isNotBlank(query.getResourceStr())) {
            String[] split = query.getResourceStr().split(",");
            try {
                List<Integer> resourceList = Stream.of(split).map(Integer::parseInt).collect(Collectors.toList());
                criteria.andResourceIn(resourceList);
            } catch (Exception e) {
                throw new BizException(100, "来源参数错误");
            }
        }
        if (StringUtils.isNotBlank(query.getSyncStatusStr())) {
            String[] split = query.getSyncStatusStr().split(",");
            try {
                List<Integer> syncStatusList = Stream.of(split).map(Integer::parseInt).collect(Collectors.toList());
                criteria.andSyncStatusIn(syncStatusList);
            } catch (Exception e) {
                throw new BizException(100, "同步状态参数错误");
            }
        }
        // 获取用户当前使用单位ouIdList作为过滤条件（根据使用单位过滤数据）
        List<Long> ouIdList =
                basedataExtService.queryCurrentUnitOu().stream().map(OperatingUnit::getOperatingUnitId).collect(Collectors.toList());
        if (!ouIdList.isEmpty()) {
            criteria.andOuIdIn(ouIdList);
        }

        example.setOrderByClause("create_at desc");
        return example;
    }

    /**
     * 保存入库参数校验
     *
     * @param headerDTO 需校验数据
     */
    private void checkMaterialAdjustHeaderDTO(MaterialAdjustHeaderDTO headerDTO) {
        // 头表数据校验
        Asserts.notNull(headerDTO, ErrorCode.CTC_MATERIAL_ADJUST_HEADER_NOT_NULL);
        Asserts.isTrue(headerDTO.getOrganizationId() != null, ErrorCode.BASEDATA_ORGANIZATION_ID_NOT_NULL);
        Asserts.isTrue(headerDTO.getOuId() != null, ErrorCode.CTC_OU_ID_NOT_NULL);
        Asserts.isTrue(headerDTO.getAdjustType() != null && MaterialAdjustEnum.getEnumByCode(headerDTO.getAdjustType()) != null,
                ErrorCode.CTC_MATERIAL_ADJUST_HEADER_ADJUSTTYPE_NOT_NULL);
        Asserts.isTrue(headerDTO.getStatus() != null && MaterialAdjustEnum.getEnumByCode(headerDTO.getStatus()) != null, ErrorCode.PARAMS_INVALID);
        Asserts.notNull(headerDTO.getResource(), ErrorCode.PARAMS_INVALID);

        String materialCodeRule = materialExtService.getMaterialCodeRule(SystemContext.getUnitId());
        boolean  ignoreResourceCheck = false;
        if(!Objects.equals(MaterialCodeRuleEnum.KUKA2022.getCode(),materialCodeRule)){
            ignoreResourceCheck = true;
        }
        //数据来源为：手工，增加handleBy字段为空校验
        if (0 == headerDTO.getResource() && !ignoreResourceCheck) {
            Asserts.notNull(headerDTO.getHandleBy(), ErrorCode.HANDLE_BY_NOT_NULL);
        }

        // 行表数据校验，pamCode不能为空
        List<MaterialAdjustDetailDTO> detailDTOList = headerDTO.getDetailDTOList();
        Asserts.isTrue(!detailDTOList.isEmpty(), ErrorCode.CTC_MATERIAL_ADJUST_DETAIL_NOT_NULL);

        List<MaterialAdjustDetailDTO> emptyPamCodeList = new ArrayList<>();
        Set<String> pamCodeSet = new HashSet<>();
        List<String> materialClassificationList = new ArrayList<>();
        List<String> codingMiddleClassList = new ArrayList<>();
        List<String> materielTypeList = new ArrayList<>();
        for (MaterialAdjustDetailDTO adjustDetailDTO : detailDTOList) {
            if (StringUtils.isBlank(adjustDetailDTO.getPamCode())) {
                emptyPamCodeList.add(adjustDetailDTO);
            }
            pamCodeSet.add(adjustDetailDTO.getPamCode());
            materialClassificationList.add(adjustDetailDTO.getMaterialClass());
            codingMiddleClassList.add(adjustDetailDTO.getMaterialMiddleClass());
            materielTypeList.add(adjustDetailDTO.getMaterialSmallClass());
        }
        if (CollectionUtils.isNotEmpty(emptyPamCodeList)) {
            throw new ApplicationBizException("所有物料pamCode不能为空");
        }
        // 行表数据pamCode不能重复
        if (detailDTOList.size() > pamCodeSet.size()) {
            throw new ApplicationBizException("PAM物料编码不能重复");
        }

        // 是否启用物料类别映射库存分类
        String currentDateStr = DateUtil.format(new Date(), "yyyy-MM-dd");
        OrganizationCustomDict organizationCustomDict = organizationCustomDictExtMapper.queryByNameAndOrgId("是否启用物料类别映射库存分类",
                headerDTO.getOrganizationId(), currentDateStr);
        boolean enableInventoryType = Objects.nonNull(organizationCustomDict);
        List<String> validResultList = new ArrayList<>();
        if (enableInventoryType) {
            // 获取物料类别配置（库存分类）
            Map<String, String> inventoryTypeMap = getInventoryTypeMap(headerDTO.getOrganizationId(), materialClassificationList,
                    codingMiddleClassList, materielTypeList);
            logger.info("checkMaterialAdjustHeaderDTO的库存分类的inventoryTypeMap：{}", JsonUtils.toString(inventoryTypeMap));

            for (MaterialAdjustDetailDTO detailDTO : detailDTOList) {
                String inventoryType = inventoryTypeMap.get(getMaterialAdjustCustomDictHeaderKey(detailDTO.getMaterialClass(),
                        detailDTO.getMaterialMiddleClass(),
                        detailDTO.getMaterialSmallClass()));
                if (StringUtils.isEmpty(inventoryType)) {
                    validResultList.add(String.format("物料编码：%s的大中小类的库存分类未维护，不允许导入", detailDTO.getPamCode()));
                    continue;
                }
                detailDTO.setInventoryType(inventoryType);
            }
            headerDTO.setDetailDTOList(detailDTOList);
        }
        // 是提交审批
        if (Boolean.TRUE.equals(headerDTO.getSubmitApprove()) && CollectionUtils.isNotEmpty(validResultList)) {
            throw new ApplicationBizException(validResultList.toString());
        }
    }

    /**
     * 封装业务实体名称，库存组织名称
     *
     * @param materialAdjustHeaderDTO
     */
    private void packageMaterialAdjustHeaderDTO(MaterialAdjustHeaderDTO materialAdjustHeaderDTO) {
        // 业务实体名称
        if (materialAdjustHeaderDTO.getOuId() != null) {
            OperatingUnit operatingUnit = CacheDataUtils.findOuById(materialAdjustHeaderDTO.getOuId());
            materialAdjustHeaderDTO.setOuName(operatingUnit == null ? null : operatingUnit.getOperatingUnitName());
        }
        // 库存组织名称
        if (materialAdjustHeaderDTO.getOrganizationId() != null) {
            OrganizationRelDto organizationRelDto = CacheDataUtils.findOrganizationById(materialAdjustHeaderDTO.getOrganizationId());
            materialAdjustHeaderDTO.setOrganizationName(organizationRelDto == null ? null : organizationRelDto.getOrganizationName());
        }
    }

    @Override
    public MaterialAdjustHeaderDTO getMaterialAttributeChangeInfoById(Long id) {
        final MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(id);
        if (materialAdjustHeader != null) {
            final MaterialAdjustHeaderDTO materialAdjustHeaderDTO = BeanConverter.copy(materialAdjustHeader,
                    MaterialAdjustHeaderDTO.class);
            final UserInfo userInfo = CacheDataUtils.findUserById(materialAdjustHeaderDTO.getApplyBy());
            if (userInfo != null) {
                materialAdjustHeaderDTO.setApplyByName(userInfo.getName());
            }
            final OperatingUnit operatingUnit = CacheDataUtils.findOuById(materialAdjustHeaderDTO.getOuId());
            if (operatingUnit != null) {
                materialAdjustHeaderDTO.setOuName(operatingUnit.getOperatingUnitName());
            }
            final OrganizationRelDto relDto =
                    CacheDataUtils.findOrganizationById(materialAdjustHeaderDTO.getOrganizationId());
            if (relDto != null) {
                materialAdjustHeaderDTO.setOrganizationName(relDto.getOrganizationName());
            }
            MaterialAdjustDetailExample materialAdjustDetailExample = new MaterialAdjustDetailExample();
            materialAdjustDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andHeaderIdEqualTo(id);
            final List<MaterialAdjustDetail> materialAdjustDetails =
                    materialAdjustDetailMapper.selectByExample(materialAdjustDetailExample);
            if (ListUtils.isNotEmpty(materialAdjustDetails)) {
                final List<MaterialAdjustDetailDTO> materialAdjustDetailDTOS =
                        BeanConverter.copy(materialAdjustDetails, MaterialAdjustDetailDTO.class);
                materialAdjustHeaderDTO.setDetailDTOList(materialAdjustDetailDTOS);
            }
            return materialAdjustHeaderDTO;

        } else {
            return null;
        }
    }

    @Override
    public MaterialAdjustHeaderDTO materialAttributeSaveOrUpdate(MaterialAdjustHeaderDTO dto) {
        Long materialAdjustId = null;
        if (null != dto.getId()) {
            materialAdjustId = updateMaterialAttributeChange(dto);
        } else {
            materialAdjustId = addMaterialAttributeChange(dto);
        }
        if (materialAdjustId != null) {
            return getMaterialAttributeChangeInfoById(materialAdjustId);
        }
        return null;

    }

    @Transactional
    @Override
    public Long updateMaterialAttributeChange(MaterialAdjustHeaderDTO dto) {
        final Long headerId = dto.getId();
        if (headerId != null) {
            materialAdjustExtMapper.deleteMaterialAdjustDetailByHeaderId(headerId);
            materialAdjustHeaderMapper.updateByPrimaryKey(dto);
            final List<MaterialAdjustDetailDTO> materialAdjustDetailDTO = dto.getDetailDTOList();
            if (ListUtils.isNotEmpty(materialAdjustDetailDTO)) {
                for (MaterialAdjustDetailDTO adjustDetailDTO : materialAdjustDetailDTO) {
                    adjustDetailDTO.setHeaderId(headerId);
                    adjustDetailDTO.setDeletedFlag(Boolean.FALSE);
                    materialAdjustDetailMapper.insert(adjustDetailDTO);
                }
            }
        } else {
            throw new BizException(Code.ERROR.getCode(), "id不能为空！");
        }
        return dto.getId();
    }

    @Transactional
    @Override
    public Long addMaterialAttributeChange(MaterialAdjustHeaderDTO dto) {
        // 生成单据号
        dto.setAdjustCode(generateMaterialAdjustCode(SystemContext.getUnitId()));
        dto.setDeletedFlag(Boolean.FALSE);
        dto.setApplyBy(SystemContext.getUserId());
        dto.setStatus(MaterialAdjustEnum.DRAFT.code());
        dto.setApplyTime(new Date());
        dto.setResource(0);
        materialAdjustHeaderMapper.insert(dto);
        final List<MaterialAdjustDetailDTO> materialAdjustDetailDTOS = dto.getDetailDTOList();
        if (ListUtils.isNotEmpty(materialAdjustDetailDTOS)) { //数据不多暂时通过for循环遍历单条插入
            for (MaterialAdjustDetailDTO materialAdjustDetailDTO : materialAdjustDetailDTOS) {
                materialAdjustDetailDTO.setHeaderId(dto.getId());
                materialAdjustDetailDTO.setDeletedFlag(Boolean.FALSE);
                materialAdjustDetailMapper.insert(materialAdjustDetailDTO);
            }
        }
        logger.info("物料属性变更单据号{}操作成功！", dto.getAdjustCode());
        return dto.getId();
    }

    @Override
    public Map<String, Object> exportChangeErrorDetail(List<MaterialAttributeImportDto> materialAttributeImportDtos) {
        Map<String, Object> resultMap = new HashMap<>();
        if (ListUtils.isNotEmpty(materialAttributeImportDtos)) {
            final Long organizationId = materialAttributeImportDtos.get(0).getOrganizationId();

            final List<DesignPlanDetailChangeExportExcelVO> designPlanDetailChangeExportExcelVOS =
                    BeanConverter.copy(materialAttributeImportDtos, DesignPlanDetailChangeExportExcelVO.class);

            designPlanDetailChangeExportExcelVOS.forEach(d -> {
                StringBuffer sb = new StringBuffer();

                if (!StringUtils.isNotEmpty(d.getPamCode())) {
                    sb.append("pam编码不能为空");
                }
                final MaterialDto materialDto = materialExtService.invokeMaterialApiGetByPamCode(d.getPamCode(),
                        organizationId);
                if (materialDto == null) {
                    sb.append("该物料不在所选的组织 ");
                } else {
                    /*final String materialType = materialDto.getMaterialType();
                    if (StringUtils.isNotEmpty(materialType)){
                        if (!materialType.equals(MaterialAdjustEnum.MATERIAL_SMALL_CLASS_ONE.msg())){
                            if (StringUtils.isNotEmpty(d.getMachiningPartTypeNew()) || d.getUnitWeightNew() != null
                            || StringUtils.isNotEmpty(d.getSurfaceHandleNew())){
                                sb.append("非"+ MaterialAdjustEnum.MATERIAL_SMALL_CLASS_ONE.msg() +"不能修改加工分类，单件重量(Kg)
                                ，表面处理这几个属性");
                            }
                        }else {
                            //获取机械加工件对应加工件分类的属性
                            List<DictDto> dicts = basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null);
                            Assert.notEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");
                            if (!dicts.stream().anyMatch(dict -> dict.getName().equals(d.getMachiningPartTypeNew()))) {
                                sb.append("导入的加工件分类需要在数据字典中 ");
                            }
                        }
                    }*/
                    //获取机械加工件对应加工件分类的属性
                    List<DictDto> dicts = basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null);
                    Assert.notEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");
                    if (!dicts.stream().anyMatch(dict -> dict.getName().equals(d.getMachiningPartTypeNew()))) {
                        sb.append("导入的加工件分类需要在数据字典中 ");
                    }
                }

                if (StringUtils.isNotEmpty(d.getOrSparePartsMaskNew())) {
                    final OrgCustomDictOrgFrom orgCustomDictOrgFrom =
                            OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.COMPANY.code());
                    if (orgCustomDictOrgFrom == null) {
                        sb.append("类型参数有误,请先配置组织参数 ");
                    }
                    Long orgId = SystemContext.getUnitId();
                    List<OrganizationCustomDict> organizationCustomDictList =
                            organizationCustomDictService.queryByOrdId(orgId, new String(), orgCustomDictOrgFrom);
                    if (CollectionUtils.isNotEmpty(organizationCustomDictList)) {
                        String sparepartsMask = "";
                        Iterator<OrganizationCustomDict> orgIt = organizationCustomDictList.iterator();
                        while (orgIt.hasNext()) {
                            OrganizationCustomDict organizationCustomDict = orgIt.next();
                            if (Constants.SPAREPARTS_MASK.equals(organizationCustomDict.getName())) {
                                sparepartsMask = organizationCustomDict.getValue();
                                break;
                            }
                        }
                        if (StringUtils.isNotEmpty(sparepartsMask)) {
                            if (!sparepartsMask.contains(d.getOrSparePartsMaskNew().trim())) {
                                sb.append("备件标识需要在组织参数中维护 ");
                            }
                        } else {
                            sb.append("备件标识需要在组织参数中维护 ");
                        }
                    }

                    if (StringUtils.isNotEmpty(d.getUnitWeightNew())) {
                        if (!d.getUnitWeightNew().matches("^[0.0-9.0]+$")) {
                            sb.append("单件重量(Kg)的数值只能是正确的数字 ");
                        }
                    }

                }

                final String errorMes = sb.toString();
                if (StringUtils.isNotEmpty(errorMes)) {
                    d.setErrorReason(errorMes);
                }
            });
            if (ListUtils.isNotEmpty(designPlanDetailChangeExportExcelVOS)) {
                resultMap.put("designPlanDetailChangeExportExcelVOS", designPlanDetailChangeExportExcelVOS);
            } else {
                resultMap.put("designPlanDetailChangeExportExcelVOS", com.google.common.collect.Lists.newArrayList());
            }

        }
        return resultMap;
    }

    @Override
    public DataResponse<Object> importMaterialAttributeChange(List<DesignPlanDetailChangeImportExcelVO> designPlanDetailChangeImportExcelVOS) {
        DataResponse<Object> response = Response.dataResponse();

        if (ListUtils.isNotEmpty(designPlanDetailChangeImportExcelVOS)) {
            final List<MaterialAttributeImportDto> materialAttributeImportDtos =
                    BeanConverter.copy(designPlanDetailChangeImportExcelVOS, MaterialAttributeImportDto.class);
            materialAttributeImportDtos.forEach(m -> {

                m.setOrganizationId(m.getOrganizationId());
                /** 数据校验 将错误的数据展示给页面提示
                 * 导入有报错时所有数据不导入
                 *  1.pamcode不能为空，否则报错
                 *  2.该物料不在本组织时报错
                 *  3.物料小类为“机械加工件”时，加工分类，单件重量(Kg)，表面处理才可以修改，否则这几个属性不能修改。（这里逻辑去掉 modifyby：guorui at 20210306）
                 *  4.「加工分类」字段只能选择字典项中的值(围栏,底座,机加件,焊接钣金件)
                 *  5.备件标识只能选择字典项中的值
                 *  6.单件重量只能是数字
                 */
                final String msg = response.getMsg();
                if (!StringUtils.isNotEmpty(m.getPamCode())) {
                    response.setCode(1);
                    if (!StringUtils.isNotEmpty(msg) || msg.equals("SUCCESS")) {
                        response.setMsg("导入的数据中pam编码不能为空，请下载检查结果查看具体信息");
                    }
                    return;
                }
                final MaterialDto materialDto = materialExtService.invokeMaterialApiGetByPamCode(m.getPamCode(),
                        m.getOrganizationId());


                if (materialDto == null) {
                    response.setCode(1);
                    if (!StringUtils.isNotEmpty(msg) || msg.equals("SUCCESS")) {
                        response.setMsg("导入的数据物料不在本组织不能导入，请下载检查结果查看具体信息");
                    }
                    return;
                } else {
                    // 拼接物料描述(名称#图号#图纸版本号#型号#规格#品牌#品牌商物料编码) 这里改成从物料表里面取值
                    m.setMaterialName(materialDto.getName());
                    m.setChartNum(materialDto.getFigureNumber());
                    m.setModel(materialDto.getModel());
                    m.setBrand(materialDto.getBrand());
                    m.setBrandsMaterialCode(materialDto.getBrandMaterialCode());
                    final String materialDes = generateMaterialDes(m);
                    m.setItemDes(materialDes);
                    m.setChartVersion(materialDto.getChartVersion());
                    m.setMachiningPartType(materialDto.getMachiningPartType());
                    m.setSurfaceHandle(materialDto.getMaterialProcessing()); // 原来叫材质处理
                    m.setMaterial(materialDto.getMaterial());
                    m.setUnitWeight(materialDto.getUnitWeight() == null ? "" :
                            materialDto.getUnitWeight().toPlainString());
                    m.setErpCode(materialDto.getItemCode());

                    DictDto unit = CacheDataUtils.findDictByTypeAndCode(DictType.MEASUREMENT_UNIT.code(),
                            materialDto.getUnit());
                    if (unit != null) {
                        m.setUnit(unit.getName());
                    }
                    m.setUnitCode(materialDto.getUnit());

                    m.setCodingMiddleclass(materialDto.getCodingMiddleclass());
                    m.setMaterialType(materialDto.getMaterialType());
                    m.setMaterialCategory(materialDto.getMaterialCategory());
                    m.setCodingMiddleclass(materialDto.getCodingMiddleclass());
                    m.setOrSparePartsMask(materialDto.getOrSparePartsMask());
                }
                //获取机械加工件对应加工件分类的属性
                List<DictDto> dicts = basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null);
                Assert.notEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");
                if (!dicts.stream().anyMatch(dict -> dict.getName().equals(m.getMachiningPartTypeNew()))) {
                    response.setCode(1);
                    if (!StringUtils.isNotEmpty(msg) || msg.equals("SUCCESS")) {
                        response.setMsg("导入的数据物料加工件分类没有在字典项中维护，请下载检查结果查看具体信息");
                    }
                    return;
                }

                final String unitWeightNew = m.getUnitWeightNew();
                if (StringUtils.isNotEmpty(unitWeightNew)) {
                    if (!unitWeightNew.matches("^[0.0-9.0]+$")) {
                        response.setCode(1);
                        response.setMsg("单件重量(Kg)的数值只能是正确的数字！");
                        return;
                    }

                }

                if (StringUtils.isNotEmpty(m.getOrSparePartsMaskNew())) {
                    final OrgCustomDictOrgFrom orgCustomDictOrgFrom =
                            OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.COMPANY.code());
                    if (orgCustomDictOrgFrom == null) {
                        response.setMsg("类型参数有误,请先配置组织参数");
                    }
                    Long orgId = SystemContext.getUnitId();
                    List<OrganizationCustomDict> organizationCustomDictList =
                            organizationCustomDictService.queryByOrdId(orgId, new String(), orgCustomDictOrgFrom);
                    if (CollectionUtils.isNotEmpty(organizationCustomDictList)) {
                        String sparepartsMask = "";
                        Iterator<OrganizationCustomDict> orgIt = organizationCustomDictList.iterator();
                        while (orgIt.hasNext()) {
                            OrganizationCustomDict organizationCustomDict = orgIt.next();
                            if (Constants.SPAREPARTS_MASK.equals(organizationCustomDict.getName())) {
                                sparepartsMask = organizationCustomDict.getValue();
                                break;
                            }
                        }
                        if (StringUtils.isNotEmpty(sparepartsMask)) {
                            if (!sparepartsMask.contains(m.getOrSparePartsMaskNew().trim())) {
                                response.setCode(1);
                                response.setMsg("备件标识需要在组织参数中维护");
                            }
                            return;
                        } else {
                            response.setCode(1);
                            response.setMsg("备件标识需要在组织参数中维护");
                            return;
                        }
                    }

                }

                /*final String materialType = materialDto.getMaterialType();
                if (StringUtils.isNotEmpty(materialType)){
                    if (!materialType.equals(MaterialAdjustEnum.MATERIAL_SMALL_CLASS_ONE.msg())){
                        if (StringUtils.isNotEmpty(m.getMachiningPartType()) || m.getUnitWeight() != null ||
                        StringUtils.isNotEmpty(m.getSurfaceHandle())){
                            response.setCode(1);
                            if (!StringUtils.isNotEmpty(msg) || msg.equals("SUCCESS")){
                                response.setMsg("非"+ MaterialAdjustEnum.MATERIAL_SMALL_CLASS_ONE.msg()
                                +"不能修改加工分类，单件重量(Kg)，表面处理这几个属性，请下载检查结果查看具体信息");
                            }
                            return;
                        }
                    }else {
                        //获取机械加工件对应加工件分类的属性
                        List<DictDto> dicts = basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null);
                        Assert.notEmpty(dicts, "请先在数据字典里维护[加工件分类]信息");
                        if (!dicts.stream().anyMatch(dict -> dict.getName().equals(m.getMachiningPartTypeNew()))) {
                            response.setCode(1);
                            if (!StringUtils.isNotEmpty(msg) || msg.equals("SUCCESS")){
                                response.setMsg("导入的数据物料加工件分类没有在字典项中维护，请下载检查结果查看具体信息");
                            }
                            return;
                        }

                    }
                }*/

            });

            response.setData(materialAttributeImportDtos);
        }

        return response;
    }

    /**
     * 物料属性变更流程提交
     *
     * @param formInstanceId
     * @param code
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatusChecking(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                                     Long createUserId, Integer code) {
        logger.info("物料新增提交审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, createUserId:{}",
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, companyId);
        String lockName = String.format("MaterialAdjust_updateStatusChecking_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(formInstanceId);
                    materialAdjustHeader.setStatus(code);
                    materialAdjustHeaderMapper.updateByPrimaryKey(materialAdjustHeader);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("物料新增提交审批回调失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("物料新增提交审批回调失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("物料新增提交审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    /**
     * 物料属性变更流程驳回
     *
     * @param formInstanceId
     * @param code
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatusRejectForChangeTypeAndAttribute(Long formInstanceId, String fdInstanceId, String formUrl, String eventName,
                                                            String handlerId, Long companyId, Long createUserId, Integer code) {
        logger.info("物料属性变更驳回审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, createUserId:{}",
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, companyId);
        String lockName = String.format("MaterialAdjust_updateStatusRejectForChangeTypeAndAttribute_%s_%s_%s_%s", formInstanceId, fdInstanceId,
                formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(formInstanceId);
                    materialAdjustHeader.setStatus(code);
                    materialAdjustHeaderMapper.updateByPrimaryKey(materialAdjustHeader);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("物料属性变更驳回审批回调失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("物料属性变更驳回审批回调失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("物料属性变更驳回审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    /**
     * 物料属性变更流程审批通过
     *
     * @param formInstanceId
     * @param code
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateStatusPass(Long formInstanceId, Integer code, Long companyId) {
        final MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(formInstanceId);

        String materialCodeRule = materialExtService.getMaterialCodeRule(companyId);
        logger.info("物料新增审批通过的formInstanceId：{},单位id：{},对应的编码规则配置：{}", formInstanceId, companyId, materialCodeRule);

        if (materialAdjustHeader != null) {
            materialAdjustHeader.setStatus(code);
            // 更新行表数据的逻辑需要调整
            materialAdjustHeader.setSyncStatus(MaterialAdjustEnum.SYNC_STATUS_WAIT.code());
            materialAdjustHeaderMapper.updateByPrimaryKey(materialAdjustHeader);

            // 新增或更新物料属性的逻辑
            if (MaterialAdjustEnum.MATERIAL_ADJUST_ADDED.code().equals(materialAdjustHeader.getAdjustType())) {
                // 获取物料类型及编码字典
                Map<String, String> materialTypeMap = basedataExtService.getLtcDict(MATERIAL_TYPE, null, null).stream()
                        .collect(Collectors.toMap(DictDto::getName, DictDto::getCode, (key1, key2) -> key2));
                MaterialAdjustHeaderDTO materialAdjustHeaderDTO = getDetailById(formInstanceId);
                if (Objects.nonNull(materialAdjustHeaderDTO.getOuId())) {
                    String buyerNumber = materialExtService.getBuyerNumberByOu(materialAdjustHeaderDTO.getOuId());
                    if (StringUtils.isNotEmpty(buyerNumber)) {
                        materialAdjustHeaderDTO.setBuyerNumber(buyerNumber);
                    }
                }
                materialAdjustHeaderDTO.setMaterialCodeRule(materialCodeRule);

                List<String> pamCodeList =
                        materialAdjustHeaderDTO.getDetailDTOList().stream().map(MaterialAdjustDetailDTO::getPamCode).distinct().collect(Collectors.toList());
                List<Material> materialList = materialExtService.invokeMaterialApiGetByPamCodeList(pamCodeList, null);
                Map<String, List<Material>> materialMap = CollectionUtils.isEmpty(materialList) ? new HashMap<>()
                        : materialList.stream().collect(Collectors.groupingBy(Material::getPamCode));

                for (MaterialAdjustDetailDTO detailDTO : materialAdjustHeaderDTO.getDetailDTOList()) {
                    // 封装物料类型编码（对应material_type字典中的code值，对应物料基础表的item_type（物料用户类型））
                    detailDTO.setMaterialTypeCode(materialTypeMap.get(detailDTO.getMaterialType()));

                    boolean assemblyIs = Objects.equals("装配件", detailDTO.getMaterialType());
                    if (assemblyIs) {
                        detailDTO.setErpCode(null);
                    } else {
                        String erpCode;
                        List<Material> materials = materialMap.get(detailDTO.getPamCode());
                        if (CollectionUtils.isNotEmpty(materials)) {
                            Material originalMaterial = null;
                            Material otherMaterial = null;
                            for (Material material : materials) {
                                if (Objects.equals(material.getOrganizationId(), materialAdjustHeaderDTO.getOrganizationId())) {
                                    originalMaterial = material;
                                } else {
                                    otherMaterial = material;
                                }
                            }
                            if (null != originalMaterial) {
                                erpCode = StringUtils.isNotEmpty(originalMaterial.getItemCode()) ? originalMaterial.getItemCode()
                                        : (null != otherMaterial && StringUtils.isNotEmpty(otherMaterial.getItemCode())) ?
                                        otherMaterial.getItemCode() : detailDTO.getErpCode();
                            } else {
                                erpCode = (null != otherMaterial && StringUtils.isNotEmpty(otherMaterial.getItemCode())) ?
                                        otherMaterial.getItemCode() : detailDTO.getErpCode();
                            }
                        } else {
                            erpCode = detailDTO.getErpCode();
                        }

                        if (StringUtils.isEmpty(erpCode)) {
                            // 生成ERP编码
                            detailDTO.setErpCode(erpCodeRuleService.generateMaterialERPCode(detailDTO.getMaterialSmallClass(),
                                    detailDTO.getMaterialMiddleClass(), detailDTO.getMaterialClass(), materialCodeRule));
                        } else {
                            detailDTO.setErpCode(erpCode);
                        }
                    }

                    if (StringUtils.isEmpty(detailDTO.getInventoryType())) {
                        detailDTO.setInventoryType("0.0.0");
                    }
                    // 查询核价员（配套人员）并保存到物料基础表、物料估价表
                    MaterialCustomDictHeaderDto materialCustomDictHeaderDto = new MaterialCustomDictHeaderDto();
                    materialCustomDictHeaderDto.setOrganizationId(materialAdjustHeaderDTO.getOrganizationId());
                    materialCustomDictHeaderDto.setCodingClass(detailDTO.getMaterialClass());
                    materialCustomDictHeaderDto.setCodingMiddleclass(detailDTO.getMaterialMiddleClass());
                    materialCustomDictHeaderDto.setCodingSubclass(detailDTO.getMaterialSmallClass());
                    MaterialCustomDictHeaderDto materialCustomDictHeaderDtoNew = materialCustomDictService.queryValuer(materialCustomDictHeaderDto);
                    detailDTO.setValuerId(materialCustomDictHeaderDtoNew.getValuerId());
                    detailDTO.setValuerMip(materialCustomDictHeaderDtoNew.getValuerMip());
                }
                materialExtService.syncMaterialMaterialAdjustDetail(materialAdjustHeaderDTO);
                saveWithDetail(materialAdjustHeaderDTO);
            } else if (MaterialAdjustEnum.MATERIAL_ADJUST_ATTRIBUTE_CHANGE.code().equals(materialAdjustHeader.getAdjustType())) {
                logger.info("物料管理头id为：{}", materialAdjustHeader.getId());
                MaterialAdjustDetailExample materialAdjustDetailExample = new MaterialAdjustDetailExample();
                materialAdjustDetailExample.createCriteria().andHeaderIdEqualTo(materialAdjustHeader.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                final List<MaterialAdjustDetail> materialAdjustDetailList =
                        materialAdjustDetailMapper.selectByExample(materialAdjustDetailExample);
                if (ListUtils.isNotEmpty(materialAdjustDetailList)) {
                    // 首先会去变更pam_basedata中的物料属性，再去新增resend_execute中的数据，最后这些数据会通过定时任务同步给erp
                    final List<MaterialAdjustDetailDTO> materialAdjustDetailDTOS =
                            BeanConverter.copy(materialAdjustDetailList, MaterialAdjustDetailDTO.class);
                    materialAdjustDetailDTOS.get(0).setOrganizationId(materialAdjustHeader.getOrganizationId());
                    materialExtService.syncMaterialAttribute(materialAdjustDetailDTOS);
                }
            } else if (MaterialAdjustEnum.MATERIAL_ADJUST_TYPE_CHANGE.code().equals(materialAdjustHeader.getAdjustType())) { //物料类型变更
                MaterialAdjustDetailExample materialAdjustDetailExample = new MaterialAdjustDetailExample();
                materialAdjustDetailExample.createCriteria().andHeaderIdEqualTo(materialAdjustHeader.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                final List<MaterialAdjustDetail> materialAdjustDetailList =
                        materialAdjustDetailMapper.selectByExample(materialAdjustDetailExample);
                if (ListUtils.isNotEmpty(materialAdjustDetailList)) {
                    MaterialAdjustDetail materialAdjustDetail = materialAdjustDetailList.get(0);
                    logger.info("物料管理行id:{},新的物料类型：{}", materialAdjustDetail.getId(),
                            materialAdjustDetail.getMaterialTypeNew());
                    //根据库存组织查询使用单位
                    Long organizationId = materialAdjustHeader.getOrganizationId();
                    final Map<String, Object> param = new HashMap<>();
                    param.put("organizationId", organizationId);
                    String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(),
                            "unit/selectUnitIdByOrganizationId", param);
                    String res = restTemplate.getForEntity(url, String.class).getBody();
                    logger.info(res);
                    DataResponse<Long> response = JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
                    });
                    List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos = null;
                    List<Long> projectIds = null;
                    if (null != response.getData()) {
                        List<Project> projects = getProject(response.getData());//查询当前使用单位下的项目
                        logger.info("当前使用单位下的项目数为{}", projects.size());
                        if (ListUtils.isNotEmpty(projects)) {
                            projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());
                            milepostDesignPlanDetailDtos =
                                    milepostDesignPlanDetailExtMapper.selectByProjectIdAndPamCode(projectIds,
                                            materialAdjustDetail.getPamCode()); //查询受影响的详细设计详情
                            logger.info("受影响的详设数为{}", milepostDesignPlanDetailDtos.size());
                        }
                    }
                    logger.info("要修改的物料id:{}", materialAdjustDetail.getMaterialId());
                    //更新物料基础表的物料类型
                    if (null != materialAdjustDetail.getMaterialId() && null != materialAdjustDetail.getMaterialTypeNew()) {
                        materialExtService.syncMaterialType(materialAdjustDetail.getMaterialId(),
                                materialAdjustDetail.getMaterialTypeNew());
                    }
                    if (ListUtils.isNotEmpty(milepostDesignPlanDetailDtos)) {
                        //插入到‘详细设计方案设计信息变更表’
                        saveMilepostDesignPlanDetialChange(milepostDesignPlanDetailDtos, materialAdjustHeader);
                        for (MilepostDesignPlanDetailDto milepostDesignPlanDetail : milepostDesignPlanDetailDtos) {
                            logger.info("旧物料类型:{},新物料类型:{}", materialAdjustDetail.getMaterialType(),
                                    materialAdjustDetail.getMaterialTypeNew());
                            //修改详细设计物料类型
                            milepostDesignPlanDetailExtMapper.updateByMilepostDesignPlanId(milepostDesignPlanDetail.getId(),
                                    materialAdjustDetail.getMaterialTypeNew());
                            //重新生成工单及工单明细
                            logger.info("详细设计id：{}", milepostDesignPlanDetail.getId());
                            List<MilepostDesignPlanDetailDto> list =
                                    milepostDesignPlanDetailService.getDesignPlanDetail(milepostDesignPlanDetail.getId());
                            ticketTasksService.createTicketTasksByDesignPlanDetail(list, null, formInstanceId,
                                    "物料类型变更");
                            if ("外购物料".equals(materialAdjustDetail.getMaterialType()) && "看板物料".equals(materialAdjustDetail.getMaterialTypeNew())) { //取消采购需求
                                //根据项目id和pamCode取消采购需求
                                Boolean b = milepostDesignPlanService.updateMrpByOrgAndPamCode(projectIds,
                                        materialAdjustDetail.getPamCode());
                                logger.info("取消采购需求操作成功{}", b);
                                //同步erp
                                ResendExecute resendExecute = new ResendExecute();
                                resendExecute.setBusinessType(BusinessTypeEnums.MATERIAL_VALUATION.getCode());
                                resendExecute.setOuId(organizationId);
                                resendExecute.setApplyNo(materialAdjustDetail.getMaterialId().toString());
                                resendExecute.setDeletedFlag(Boolean.FALSE);
                                resendExecute.setCreateAt(new Date());
                                resendExecute.setBatch(true);
                                resendExecute.setStatus(CommonStatus.TODO.getCode());
                                HandleDispatcher.route(resendExecute);
                            } else if ("看板物料".equals(materialAdjustDetail.getMaterialType()) && "外购物料".equals(materialAdjustDetail.getMaterialTypeNew())) { //看板物料->外购物料： 增加采购需求
                                logger.info("详细设计id：{}", milepostDesignPlanDetail.getId());
                                milepostDesignPlanService.setMRP(milepostDesignPlanDetail.getId()); //增加采购需求
                                //同步erp
                                ResendExecute resendExecute = new ResendExecute();
                                resendExecute.setBusinessType(BusinessTypeEnums.MATERIAL_VALUATION.getCode());
                                resendExecute.setOuId(organizationId);
                                resendExecute.setApplyNo(materialAdjustDetail.getMaterialId().toString());
                                resendExecute.setDeletedFlag(Boolean.FALSE);
                                resendExecute.setCreateAt(new Date());
                                resendExecute.setBatch(true);
                                resendExecute.setStatus(CommonStatus.TODO.getCode());
                                HandleDispatcher.route(resendExecute);
                            }
                        }
                    }
                }

            }
        }

        return "1";
    }

    @Override
    public void updateStatusPassCommon(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId,
                                       Long companyId, Long createUserId, Integer code) {
        applicationEventPublisher.publishEvent(new MaterialAdjustApprovalEvent(this, formInstanceId, fdInstanceId, formUrl,
                eventName, handlerId, companyId, createUserId, code));
    }

    private void saveMilepostDesignPlanDetialChange(List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos,
                                                    MaterialAdjustHeader materialAdjustHeader) {
        //List<MilepostDesignPlanDetailChangeDto> changeDtoList = new ArrayList<>();
        for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : milepostDesignPlanDetailDtos) {
            MilepostDesignPlanChangeRecordDto dto = new MilepostDesignPlanChangeRecordDto();
            dto.setPublishAt(materialAdjustHeader.getCreateAt());
            dto.setPublisherBy(materialAdjustHeader.getCreateBy());
            dto.setChangeReason(materialAdjustHeader.getAdjustReasonDes());
            dto.setProjectId(milepostDesignPlanDetailDto.getProjectId());
            dto.setMilepostId(milepostDesignPlanDetailDto.getMilepostId());
            dto.setUploadPathId(milepostDesignPlanDetailDto.getParentId());
            dto.setStatus(1);
            dto.setDeletedFlag(true);

            MilepostDesignPlanChangeRecordDto resultDto = save(dto, materialAdjustHeader.getCreateBy());

            MilepostDesignPlanDetailChangeDto changeDto = new MilepostDesignPlanDetailChangeDto();
            setmilepostDesignPlanDetailChangeDto(changeDto, milepostDesignPlanDetailDto, resultDto,
                    materialAdjustHeader);

            milepostDesignPlanDetailChangeService.saveMaterialTypeChange(changeDto, materialAdjustHeader.getCreateBy());
        }
    }

    private void setmilepostDesignPlanDetailChangeDto(MilepostDesignPlanDetailChangeDto changeDto,
                                                      MilepostDesignPlanDetailDto milepostDesignPlanDetailDto,
                                                      MilepostDesignPlanChangeRecordDto resultDto,
                                                      MaterialAdjustHeader materialAdjustHeader) {
        changeDto.setHistoryType(HistoryType.CHANGE.getCode());
        changeDto.setDesignPlanDetailId(milepostDesignPlanDetailDto.getId());
        changeDto.setChangeRecordId(resultDto.getId());
        changeDto.setProjectBudgetMaterialId(milepostDesignPlanDetailDto.getProjectBudgetMaterialId());
        changeDto.setProjectId(milepostDesignPlanDetailDto.getProjectId());
        changeDto.setMilepostId(milepostDesignPlanDetailDto.getMilepostId());
        changeDto.setParentId(milepostDesignPlanDetailDto.getParentId());
        changeDto.setWhetherModel(milepostDesignPlanDetailDto.getWhetherModel());
        changeDto.setModuleStatus(milepostDesignPlanDetailDto.getModuleStatus());
        changeDto.setExt(milepostDesignPlanDetailDto.getExt());
        changeDto.setMaterielDescr(milepostDesignPlanDetailDto.getMaterielDescr());
        changeDto.setUnit(milepostDesignPlanDetailDto.getUnit());
        changeDto.setUnitCode(milepostDesignPlanDetailDto.getUnitCode());
        changeDto.setNumber(milepostDesignPlanDetailDto.getNumber());
        changeDto.setDeliveryTime(milepostDesignPlanDetailDto.getDeliveryTime());
        changeDto.setName(milepostDesignPlanDetailDto.getName());
        changeDto.setModel(milepostDesignPlanDetailDto.getModel());
        changeDto.setBrand(milepostDesignPlanDetailDto.getBrand());
        changeDto.setMaterialClassification(milepostDesignPlanDetailDto.getMaterialClassification());
        changeDto.setMaterielType(milepostDesignPlanDetailDto.getMaterielType());
        changeDto.setMachiningPartType(milepostDesignPlanDetailDto.getMachiningPartType());
        changeDto.setMaterial(milepostDesignPlanDetailDto.getMaterial());
        changeDto.setUnitWeight(milepostDesignPlanDetailDto.getUnitWeight());
        changeDto.setMaterialProcessing(milepostDesignPlanDetailDto.getMaterialProcessing());
        changeDto.setBudgetUnitPrice(milepostDesignPlanDetailDto.getBudgetUnitPrice());
        changeDto.setDesignCost(milepostDesignPlanDetailDto.getDesignCost());
        changeDto.setDesignCostId(milepostDesignPlanDetailDto.getDesignCostId());
        changeDto.setBudgetSubtotal(milepostDesignPlanDetailDto.getBudgetSubtotal());
        changeDto.setPamCode(milepostDesignPlanDetailDto.getPamCode());
        changeDto.setErpCode(milepostDesignPlanDetailDto.getErpCode());
        changeDto.setErpCodeSource(milepostDesignPlanDetailDto.getErpCodeSource());
        changeDto.setSource(milepostDesignPlanDetailDto.getSource());
        changeDto.setRemark(milepostDesignPlanDetailDto.getRemark());
        changeDto.setStatus(milepostDesignPlanDetailDto.getStatus());
        //changeDto.setGenerateRequirement(milepostDesignPlanDetailDto.getGenerateRequirement());
        changeDto.setDeletedFlag(true);
        changeDto.setCreateBy(materialAdjustHeader.getCreateBy());
        changeDto.setMaterialCategory(milepostDesignPlanDetailDto.getMaterialCategory());
        changeDto.setCodingMiddleClass(milepostDesignPlanDetailDto.getCodingMiddleClass());
        changeDto.setFigureNumber(milepostDesignPlanDetailDto.getFigureNumber());
        changeDto.setChartVersion(milepostDesignPlanDetailDto.getChartVersion());
        changeDto.setBrandMaterialCode(milepostDesignPlanDetailDto.getBrandMaterialCode());
        changeDto.setOrSparePartsMask(milepostDesignPlanDetailDto.getOrSparePartsMask());

        //增加查询模组的信息
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                milepostDesignPlanDetailExtMapper.selectModel(milepostDesignPlanDetailDto.getParentId());
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtoList =
                milepostDesignPlanDetailExtMapper.selectTotalNumberById(milepostDesignPlanDetailDto.getParentId());
        BigDecimal parentNumber = new BigDecimal(1);
        if (ListUtils.isNotEmpty(milepostDesignPlanDetailDtoList)) {
            for (MilepostDesignPlanDetailDto designPlanDetailDto : milepostDesignPlanDetailDtoList) {
                parentNumber = parentNumber.multiply(designPlanDetailDto.getNumber());
            }
        }
        if (ListUtils.isNotEmpty(milepostDesignPlanDetailDtos)) {
            MilepostDesignPlanDetailDto detailDto = milepostDesignPlanDetailDtos.get(0);
            changeDto.setModelId(detailDto.getId()); //模组id
            changeDto.setModelPamCode(detailDto.getPamCode()); //模组PAM编码
            changeDto.setModelNum(parentNumber.setScale(10, BigDecimal.ROUND_HALF_UP)); //模组数量(当前详设以及上层直接父级节点数量的乘积)
            // ‘此字只是用来接收计算结果，忽略字面意思’
            changeDto.setModelName(detailDto.getMaterielDescr()); //模组名称
        }

    }

    private MilepostDesignPlanChangeRecordDto save(MilepostDesignPlanChangeRecordDto dto, Long createBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(createBy);
            return add(dto);
        } else {
            dto.setUpdateBy(createBy);
            return update(dto);
        }
    }

    private MilepostDesignPlanChangeRecordDto update(MilepostDesignPlanChangeRecordDto dto) {
        MilepostDesignPlanChangeRecord entity = BeanConverter.copy(dto, MilepostDesignPlanChangeRecord.class);
        milepostDesignPlanChangeRecordMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    private MilepostDesignPlanChangeRecordDto add(MilepostDesignPlanChangeRecordDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        MilepostDesignPlanChangeRecord entity = BeanConverter.copy(dto, MilepostDesignPlanChangeRecord.class);
        milepostDesignPlanChangeRecordMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    private List<Project> getProject(Long companyId) {
        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andStatusNotBetween(9, 13);
        List<Long> units = basedataExtService.findUnitIds(companyId);
        if (units != null && units.size() > 0) {
            criteria.andUnitIdIn(units);
        }
        return projectMapper.selectByExample(projectExample);
    }


    /**
     * 物料属性变更流程撤回
     *
     * @param formInstanceId
     * @param code
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatusDraftReturnForChangeTypeAndAttribute(Long formInstanceId, String fdInstanceId, String formUrl, String eventName,
                                                                 String handlerId, Long companyId, Long createUserId, Integer code) {
        logger.info("物料属性变更审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, createUserId:{}",
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, companyId);
        String lockName = String.format("MaterialAdjust_updateStatusDraftReturnForChangeTypeAndAttribute_%s_%s_%s_%s", formInstanceId, fdInstanceId
                , formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(formInstanceId);
                    materialAdjustHeader.setStatus(code);
                    materialAdjustHeaderMapper.updateByPrimaryKey(materialAdjustHeader);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("物料属性变更审批回调失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("物料属性变更审批回调失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("物料属性变更审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    /**
     * 物料属性变更流程作废
     *
     * @param formInstanceId
     * @param code
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void abandon(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                        Long createUserId, Integer code) {
        logger.info("物料新增作废审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, createUserId:{}",
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, companyId);
        String lockName = String.format("MaterialAdjust_abandon_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(formInstanceId);
                    //物料新增-KUKA2022-审批流程-驳回后在我的流程作废 -- --单据状态不变、审批流程变为作废状态
                    if (!Objects.equals(materialAdjustHeader.getStatus(), MaterialAdjustEnum.TODO.code())) {
                        materialAdjustHeader.setStatus(code);
                        materialAdjustHeaderMapper.updateByPrimaryKey(materialAdjustHeader);
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("物料新增作废审批回调失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("物料新增作废审批回调失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("物料新增作废审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    /**
     * 物料属性变更流程删除
     *
     * @param formInstanceId
     * @param code
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                       Long createUserId, Integer code) {
        logger.info("物料新增删除审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, createUserId:{}",
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, companyId);
        String lockName = String.format("MaterialAdjust_delete_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(formInstanceId);
                    //物料新增-KUKA2022-审批流程-驳回后在我的流程作废 -- --单据状态不变、审批流程变为作废状态
                    if (!Objects.equals(materialAdjustHeader.getStatus(), MaterialAdjustEnum.TODO.code())) {
                        materialAdjustHeader.setStatus(code);
                        materialAdjustHeaderMapper.updateByPrimaryKey(materialAdjustHeader);
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("物料新增删除审批回调失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("物料新增删除审批回调失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("物料新增删除审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteDraft(Long id) {
        MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(id);
        if (materialAdjustHeader == null) {
            throw new BizException(100, "参数错误");
        }
        if (!(MaterialAdjustEnum.DRAFT.code().equals(materialAdjustHeader.getStatus()) || MaterialAdjustEnum.REJECT.code().equals(materialAdjustHeader.getStatus()))) {
            throw new BizException(100, "只能作废草稿或退回状态的单据");
        }
        MaterialAdjustHeader record = new MaterialAdjustHeader();
        record.setId(id);
        record.setStatus(MaterialAdjustEnum.CANCEL.code());
        return materialAdjustHeaderMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Integer draftReturn(Long id) {
        MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(id);
        if (materialAdjustHeader == null) {
            throw new BizException(100, "物料单据不存在");
        }
        if (!Objects.equals(materialAdjustHeader.getApplyBy(), SystemContext.getUserId())) {
            throw new MipException("只有申请人允许撤回");
        }
        if (!Objects.equals(materialAdjustHeader.getStatus(), MaterialAdjustEnum.TODO.code())) {
            throw new MipException("单据不为待处理状态，不能撤回");
        }
        materialAdjustHeader.setStatus(MaterialAdjustEnum.DRAFT.code());
        return materialAdjustHeaderMapper.updateByPrimaryKeySelective(materialAdjustHeader);
    }

    @Override
    public Integer reject(Long id) {
        MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(id);
        if (materialAdjustHeader == null) {
            throw new BizException(100, "物料单据不存在");
        }
        if (!Objects.equals(materialAdjustHeader.getHandleBy(), SystemContext.getUserId())) {
            throw new MipException("只有处理人允许退回");
        }
        if (!Objects.equals(materialAdjustHeader.getStatus(), MaterialAdjustEnum.TODO.code())) {
            throw new MipException("单据不为待处理状态，不能退回");
        }
        materialAdjustHeader.setStatus(MaterialAdjustEnum.REJECT.code());
        return materialAdjustHeaderMapper.updateByPrimaryKeySelective(materialAdjustHeader);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatusReject(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                                   Long createUserId, Integer code) {
        logger.info("物料新增驳回审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, createUserId:{}",
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, companyId);
        String lockName = String.format("MaterialAdjust_updateStatusReject_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(formInstanceId);
                    // 获取 物料编码规则 组织参数配置
                    Set<String> set = organizationCustomDictService.queryByName(Constants.MATERIAL_CODE_RULE, companyId,
                            OrgCustomDictOrgFrom.COMPANY);
                    if (CollectionUtils.isNotEmpty(set) && set.contains("KUKA2022")) {
                        code = MaterialAdjustEnum.TODO.code();
                    }
                    materialAdjustHeader.setStatus(code);
                    materialAdjustHeaderMapper.updateByPrimaryKey(materialAdjustHeader);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("物料新增驳回审批回调失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("物料新增驳回审批回调失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("物料新增驳回审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatusDraftReturn(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                                        Long createUserId, Integer code) {
        logger.info("物料新增撤回审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, createUserId:{}",
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, companyId);
        String lockName = String.format("MaterialAdjust_updateStatusDraftReturn_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(formInstanceId);
                    // 获取 物料编码规则 组织参数配置
                    Set<String> set = organizationCustomDictService.queryByName(Constants.MATERIAL_CODE_RULE, companyId,
                            OrgCustomDictOrgFrom.COMPANY);
                    if (CollectionUtils.isNotEmpty(set) && set.contains("KUKA2022")) {
                        code = MaterialAdjustEnum.TODO.code();
                    }
                    materialAdjustHeader.setStatus(code);
                    materialAdjustHeaderMapper.updateByPrimaryKey(materialAdjustHeader);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("物料新增撤回审批回调失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("物料新增撤回审批回调失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("物料新增撤回审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Override
    public Integer materialAddReject(MaterialAdjustHeaderDTO dto) {
        Guard.notNull(dto, "退回的参数为空");
        Long id = dto.getId();
        Guard.notNull(id, "退回的参数为空");
        Date rejectAt = dto.getRejectAt();
        Guard.notNull(rejectAt, "退回日期为空");
        String rejectReason = dto.getRejectReason();
        Guard.notNullOrEmpty(rejectReason, "退回原因为空");

        MaterialAdjustHeader header = materialAdjustHeaderMapper.selectByPrimaryKey(id);
        if (header == null) {
            throw new ApplicationBizException("物料单据不存在");
        }
        if (!Objects.equals(header.getHandleBy(), SystemContext.getUserId())) {
            throw new ApplicationBizException("只有处理人允许退回");
        }
        if (!Objects.equals(header.getStatus(), MaterialAdjustEnum.TODO.code())) {
            throw new ApplicationBizException("单据不为待处理状态，不能退回");
        }

        dto.setStatus(MaterialAdjustEnum.REJECT.code());
        sendEmailForMaterialAdjust(dto, dto.getApplyBy(), NoticeBusinessType.MATERIAL_ADJUST_TODO);

        MaterialAdjustHeader materialAdjustHeader = new MaterialAdjustHeader();
        materialAdjustHeader.setId(id);
        materialAdjustHeader.setRejectAt(rejectAt);
        materialAdjustHeader.setRejectReason(rejectReason);
        materialAdjustHeader.setStatus(MaterialAdjustEnum.REJECT.code());
        return materialAdjustHeaderMapper.updateByPrimaryKeySelective(materialAdjustHeader);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteById(Long id) {
        Guard.notNull(id, "删除的参数为空");

        MaterialAdjustDetailExample materialAdjustDetailExample = new MaterialAdjustDetailExample();
        materialAdjustDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andHeaderIdEqualTo(id);
        List<MaterialAdjustDetail> materialAdjustDetails = materialAdjustDetailMapper.selectByExample(materialAdjustDetailExample);

        MaterialAdjustHeader materialAdjustHeader = new MaterialAdjustHeader();
        materialAdjustHeader.setId(id);
        materialAdjustHeader.setDeletedFlag(Boolean.TRUE);
        materialAdjustHeaderMapper.updateByPrimaryKeySelective(materialAdjustHeader);
        for (MaterialAdjustDetail materialAdjustDetail : materialAdjustDetails) {
            MaterialAdjustDetail record = new MaterialAdjustDetail();
            record.setId(materialAdjustDetail.getId());
            record.setDeletedFlag(Boolean.TRUE);
            materialAdjustDetailMapper.updateByPrimaryKeySelective(record);
        }
    }

    private void sendEmailForMaterialAdjust(MaterialAdjustHeaderDTO dto, Long receiverId, NoticeBusinessType noticeBusinessType) {
        try {
            if (null != dto) {
                String subject = String.format("PAM物料新增提醒：请关注物料新增和变更单据“%s”的处理情况", dto.getAdjustCode());
                StringBuilder content = new StringBuilder("<html><div style='font-size: 12px;'>");
                content.append(String.format("<div>PAM物料新增提醒：请关注物料新增单据“%s”的处理情况</div>", dto.getAdjustCode()));
                content.append(String.format("<div><a href='%s' target='_blank'>点击跳转至PAM处理</a></div>", materialAdjustDetailUrl + dto.getId()));
                content.append(String.format("<div>单据号：%s</div>", dto.getAdjustCode()));
                content.append(String.format("<div>变更类型：%s</div>", Optional.ofNullable(MaterialAdjustEnum.getMsg(dto.getAdjustType())).orElse("")));
                content.append(String.format("<div>单据状态：%s</div>", Optional.ofNullable(MaterialAdjustEnum.getMsg(dto.getStatus())).orElse("")));
                content.append(String.format("<div>申请人：%s</div>", dto.getApplyByName()));
                content.append(String.format("<div>处理人：%s</div>", dto.getHandleByName()));
                content.append(String.format("<div>库存组织：%s</div>", dto.getOrganizationName()));
                content.append("</div></html>");

                sendEmail(receiverId, null, noticeBusinessType.getType(), subject, content.toString());
            }
        } catch (Exception e) {
            logger.error("{}邮件发送失败", noticeBusinessType.getName(), e);
        }
    }

    /**
     * 发送邮件
     *
     * @param receiveMipAccount ：接收人Mip账号
     * @param receiverId        ：接收人id，无receiveMipAccount，则通过receiverId查询receiverMipAccount
     * @param subject           : 邮件主题
     * @param content           ：邮件内容
     */
    private void sendEmail(Long receiverId, String receiveMipAccount, int businessType, String subject, String content) {
        Email email = new Email();
        email.setFromAddress("pam");
        email.setStatus(0);
        email.setLanguage("zh-CN");
        email.setBusinessType(businessType);
        if (StringUtils.isEmpty(receiveMipAccount)) {
            UserInfo user = CacheDataUtils.findUserById(receiverId);
            receiveMipAccount = Optional.ofNullable(user).map(UserInfo::getUsername).orElse(SystemContext.getUserMip());
        }
        email.setReceiver(receiveMipAccount);
        email.setSubject(subject);
        email.setContent(content);
        email.setDeletedFlag(Boolean.FALSE);
        emailExtMapper.insert(email);
    }

    @Override
    public void insertHeader(MaterialAdjustHeader materialAdjustHeader) {
        materialAdjustHeaderMapper.insert(materialAdjustHeader);
    }

    private String generateMaterialDes(MaterialAttributeImportDto m) {
        StringBuffer sb = new StringBuffer();
        sb.append(StringUtils.isNotEmpty(m.getMaterialName()) ? m.getMaterialName() : "");
        sb.append("#");
        sb.append(StringUtils.isNotEmpty(m.getChartNum()) ? m.getChartNum() : "");
        sb.append("#");
        sb.append(StringUtils.isNotEmpty(m.getChartVersionNew()) ? m.getChartVersionNew() : "");
        sb.append("#");
        sb.append(StringUtils.isNotEmpty(m.getModel()) ? m.getModel() : "");
        sb.append("#");
        sb.append(StringUtils.isNotEmpty(m.getBrand()) ? m.getBrand() : "");
        sb.append("#");
        sb.append(StringUtils.isNotEmpty(m.getBrandsMaterialCode()) ? m.getBrandsMaterialCode() : "");
        return sb.toString();
    }


    /**
     * 物料属性变更/类型变更单据号生成规则： 代码公司段(美云:M，机器人:R,昆山: K)+WL+YYYYMMDD+5位流水码
     *
     * @param unitId
     * @return
     */
    @Override
    public String generateMaterialAdjustCode(Long unitId) {
        StringBuffer materialAdjustCode = new StringBuffer();
        String seqPerfix = basedataExtService.getUnitSeqPerfix(unitId);
        materialAdjustCode.append(seqPerfix);
        materialAdjustCode.append(MATERIAL_PREFIX);
        materialAdjustCode.append(CacheDataUtils.generateSequence(5, seqPerfix + MATERIAL_PREFIX,
                DateUtil.DATE_INTEGER_PATTERN));
        return materialAdjustCode.toString();
    }

    @Override
    public void updateMaterialAdjustSyncInfo(List<MaterialAdjustDetail> materialAdjustDetailList) {
        if (!materialAdjustDetailList.isEmpty()) {
            for (MaterialAdjustDetail materialAdjustDetail : materialAdjustDetailList) {
                materialAdjustExtMapper.updateDetailSyncInfo(materialAdjustDetail);
            }

            List<String> pamCodeList =
                    materialAdjustDetailList.stream().map(MaterialAdjustDetail::getPamCode).collect(Collectors.toList());
            List<String> erpCodeList =
                    materialAdjustDetailList.stream().map(MaterialAdjustDetail::getErpCode).collect(Collectors.toList());
            List<MaterialAdjustHeader> materialAdjustHeadeList =
                    materialAdjustExtMapper.listHeaderNeedUpdateSyncInfo(pamCodeList, erpCodeList);
            if (!materialAdjustHeadeList.isEmpty()) {
                for (MaterialAdjustHeader materialAdjustHeader : materialAdjustHeadeList) {
                    Integer syncStatus = materialAdjustHeader.getSyncStatus() == 0 ?
                            MaterialAdjustEnum.SYNC_STATUS_EXCEPTION.code() :
                            MaterialAdjustEnum.SYNC_STATUS_SUCCESS.code();
                    String syncMes = materialAdjustHeader.getSyncStatus() == 0 ?
                            MaterialAdjustEnum.SYNC_STATUS_EXCEPTION.msg() :
                            MaterialAdjustEnum.SYNC_STATUS_SUCCESS.msg();
                    materialAdjustHeader.setSyncStatus(syncStatus);
                    materialAdjustHeader.setSyncMes(syncMes);
                    materialAdjustHeaderMapper.updateByPrimaryKeySelective(materialAdjustHeader);
                }
            }
        }
    }

    /**
     * 根据物料新增与变更明细DTO生成物料描述
     *
     * @param detailDTO
     * @return
     */
    private String generateMaterialDes(MaterialAdjustDetailDTO detailDTO) {
        StringBuffer sb = new StringBuffer();
        sb.append(detailDTO.getName());
        if (StringUtils.isNotBlank(detailDTO.getFigureNumber())) {
            sb.append("#").append(detailDTO.getFigureNumber());
        }
        if (StringUtils.isNotBlank(detailDTO.getChartVersion())) {
            sb.append("#").append(detailDTO.getChartVersion());
        }
        sb.append("#").append(detailDTO.getModel()).append("#").append(detailDTO.getBrand());
        if (StringUtils.isNotBlank(detailDTO.getBrandMaterialCode())) {
            sb.append("#").append(detailDTO.getBrandMaterialCode());
        }
        return sb.toString().length() <= 230 ? sb.toString() : sb.toString().substring(0, 230);
    }

    /**
     * 根据物料新增与变更明细DTO生成物料描述
     *
     * @param detailDTO
     * @return
     */
    private String generateMaterialDesForKuka2022(MaterialAdjustDetailDTO detailDTO, List<String> nonStandardMaterialType) {
        StringBuffer sb = new StringBuffer();
        sb.append(detailDTO.getName());
        //非标件
        if (nonStandardMaterialType.contains(detailDTO.getMaterialClass())) {
            sb.append("#").append(detailDTO.getFigureNumber());
        } else {
            sb.append("#").append(detailDTO.getBrand());
            sb.append("#").append(detailDTO.getModel());
            sb.append("#").append(detailDTO.getOrSparePartsMask());
        }
        return sb.toString().length() <= 230 ? sb.toString() : sb.toString().substring(0, 230);
    }


    @Override
    public MaterialAdjustHeaderDTO getMaterialChangeTypeInfoById(Long id) {
        final MaterialAdjustHeader materialAdjustHeader = materialAdjustHeaderMapper.selectByPrimaryKey(id);
        if (materialAdjustHeader != null) {
            final MaterialAdjustHeaderDTO materialAdjustHeaderDTO = BeanConverter.copy(materialAdjustHeader,
                    MaterialAdjustHeaderDTO.class);
/*            final UserInfo userInfo = CacheDataUtils.findUserById(materialAdjustHeaderDTO.getApplyBy());
            if (userInfo != null){
                materialAdjustHeaderDTO.setApplyByName(userInfo.getName());
            }*/
            final OperatingUnit operatingUnit = CacheDataUtils.findOuById(materialAdjustHeaderDTO.getOuId());
            if (operatingUnit != null) {
                materialAdjustHeaderDTO.setOuName(operatingUnit.getOperatingUnitName());
            }
            final OrganizationRelDto relDto =
                    CacheDataUtils.findOrganizationById(materialAdjustHeaderDTO.getOrganizationId());
            if (relDto != null) {
                materialAdjustHeaderDTO.setOrganizationName(relDto.getOrganizationName());
            }
            MaterialAdjustDetailExample materialAdjustDetailExample = new MaterialAdjustDetailExample();
            materialAdjustDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andHeaderIdEqualTo(id);
            final List<MaterialAdjustDetail> materialAdjustDetails =
                    materialAdjustDetailMapper.selectByExample(materialAdjustDetailExample);
            if (ListUtils.isNotEmpty(materialAdjustDetails)) {
                final List<MaterialAdjustDetailDTO> materialAdjustDetailDTOS =
                        BeanConverter.copy(materialAdjustDetails, MaterialAdjustDetailDTO.class);
                materialAdjustHeaderDTO.setDetailDTOList(materialAdjustDetailDTOS);
            }
            return materialAdjustHeaderDTO;

        } else {
            return null;
        }
    }

    @Override
    public MaterialAdjustHeaderDTO materialChangeTypeSaveOrUpdate(MaterialAdjustHeaderDTO dto) {

        checkStatus(dto); //校验当前库存组织下的物料的变更状态

        Long materialAdjustId = null;
        if (null != dto.getId()) {
            materialAdjustId = updateMaterialChangeType(dto);
        } else {
            materialAdjustId = addMaterialChangeType(dto);
        }
        if (materialAdjustId != null) {
            return getMaterialChangeTypeInfoById(materialAdjustId);
        }
        return null;
    }

    private void checkStatus(MaterialAdjustHeaderDTO dto) {
        Long organizationId = dto.getOrganizationId();
        String pamCode = dto.getDetailDTOList().get(0).getPamCode();
        MaterialAdjustHeader materialAdjustHeader =
                materialAdjustExtMapper.selectStatusByOrgAndPamCode(organizationId, pamCode);
        if (null != materialAdjustHeader) {
            throw new BizException(Code.ERROR.getCode(), "当前物料的变更正在审批过程中，请通过后再进行变更！");
        }
    }

    @Transactional
    @Override
    public Long updateMaterialChangeType(MaterialAdjustHeaderDTO dto) {
        final Long headerId = dto.getId();
        if (headerId != null) {
            materialAdjustExtMapper.deleteMaterialAdjustDetailByHeaderId(headerId);
            materialAdjustHeaderMapper.updateByPrimaryKey(dto);
            final List<MaterialAdjustDetailDTO> materialAdjustDetailDTO = dto.getDetailDTOList();
            if (ListUtils.isNotEmpty(materialAdjustDetailDTO)) {
                for (MaterialAdjustDetailDTO adjustDetailDTO : materialAdjustDetailDTO) {
                    adjustDetailDTO.setHeaderId(headerId);
                    adjustDetailDTO.setDeletedFlag(Boolean.FALSE);
                    materialAdjustDetailMapper.insert(adjustDetailDTO);
                }
            }
        } else {
            throw new BizException(Code.ERROR.getCode(), "id不能为空！");
        }
        return dto.getId();
    }

    @Override
    public Long addMaterialChangeType(MaterialAdjustHeaderDTO dto) {
        // 生成单据号
        dto.setAdjustCode(generateMaterialAdjustCode(SystemContext.getUnitId()));
        dto.setDeletedFlag(Boolean.FALSE);
        dto.setApplyBy(SystemContext.getUserId());
        dto.setApplyByName(SystemContext.getUserName());
        dto.setStatus(MaterialAdjustEnum.DRAFT.code());
        dto.setApplyTime(new Date());
        dto.setResource(0);
        materialAdjustHeaderMapper.insert(dto);
        final List<MaterialAdjustDetailDTO> materialAdjustDetailDTOS = dto.getDetailDTOList();
        if (ListUtils.isNotEmpty(materialAdjustDetailDTOS)) {
            for (MaterialAdjustDetailDTO materialAdjustDetailDTO : materialAdjustDetailDTOS) {
                materialAdjustDetailDTO.setHeaderId(dto.getId());
                materialAdjustDetailDTO.setDeletedFlag(Boolean.FALSE);
                materialAdjustDetailMapper.insert(materialAdjustDetailDTO);
            }
        }
        logger.info("物料类型变更单据号{}操作成功！", dto.getAdjustCode());
        return dto.getId();
    }

    @Override
    public List<MaterialChangeTypeVo> findChangeTypeDetail(Long materialId, Long operatingUnitId, Long organizationId) {
        if (null == materialId) {
            throw new BizException(Code.ERROR.getCode(), "id不能为空！");
        }
        MaterialDto dto = getMaterDto(materialId); //查询变更的物料信息
        List<Long> idList = projectExtMapper.getProjectDetail(operatingUnitId);//根据业务实体查询有效的项目

        List<MaterialChangeTypeVo> materialChangeTypeVos =
                materialAdjustExtMapper.selectMaterialDetail(dto.getPamCode(), idList, organizationId);

        if (ListUtils.isNotEmpty(materialChangeTypeVos)) {
            materialChangeTypeVos.forEach(m -> {
                //List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtoList =
                // milepostDesignPlanDetailExtMapper.selectModel(m.getDesignId());//查询模组信息
                if ("装配件".equals(m.getMaterialCategory()) || "虚拟件".equals(m.getMaterialCategory())) {
                    //设置单价、金额
                    BigDecimal itemCost = materialAdjustExtMapper.selectPriceBySon(m.getDesignId());
                    BigDecimal totalPrice = new BigDecimal(m.getNumber()).multiply(itemCost);
                    m.setPrice(itemCost);
                    m.setTotalPrice(totalPrice);
                } else if ("外购物料".equals(m.getMaterialCategory()) || "看板物料".equals(m.getMaterialCategory())) {
                    //查询直接上级，计算采购需求数
                    List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                            milepostDesignPlanDetailExtMapper.selectTotalNumberById(m.getDesignId());
                    BigDecimal totalNumber = new BigDecimal(1);
                    if (ListUtils.isNotEmpty(milepostDesignPlanDetailDtos)) {
                        for (MilepostDesignPlanDetailDto milepostDesignPlanDetailDto : milepostDesignPlanDetailDtos) {
                            totalNumber = totalNumber.multiply(milepostDesignPlanDetailDto.getNumber()).setScale(0,
                                    BigDecimal.ROUND_HALF_UP);
                        }
                    }
                    m.setTotalNumber(totalNumber);
                }
            });
        }
        return materialChangeTypeVos;
    }

    private MaterialDto getMaterDto(Long materialId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", materialId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/material/getById", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MaterialDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MaterialDto>>() {
        });
        return response.getData();
    }

    @Override
    public ResponseMap getMaterialTypeChangeApp(Long materialAdjustHeaderId) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        MaterialAdjustHeaderDTO materialAdjustHeaderDTO = this.getMaterialChangeTypeInfoById(materialAdjustHeaderId);
        MaterialAdjustDetailDTO materialAdjustDetailDTO = materialAdjustHeaderDTO.getDetailDTOList().get(0);
        if (ObjectUtils.isEmpty(materialAdjustHeaderDTO)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("头信息不能为空");
        }

        if (null != materialAdjustHeaderDTO.getAnnex() && !materialAdjustHeaderDTO.getAnnex().equals("")) {
            headMap.put("annex", materialAdjustHeaderDTO.getAnnex()); //用于附件
        }
        if (null != materialAdjustHeaderDTO.getAdjustCode())
            headMap.put("adjustCode", materialAdjustHeaderDTO.getAdjustCode()); //单据编号
        if (null != materialAdjustHeaderDTO.getAdjustType())
            headMap.put("adjustType", "物料类型变更"); // 单据类型
        if (null != materialAdjustHeaderDTO.getAdjustReasonDes()) {
            headMap.put("adjustReasonDes", materialAdjustHeaderDTO.getAdjustReasonDes()); // 变更原因
        }
        if (null != materialAdjustDetailDTO.getPamCode()) {
            headMap.put("pamCode", materialAdjustDetailDTO.getPamCode()); // pam物料编码
        }
        if (null != materialAdjustDetailDTO.getErpCode()) {
            headMap.put("erpCode", materialAdjustDetailDTO.getErpCode()); // erp物料编码
        }
        if (null != materialAdjustDetailDTO.getItemDes()) {
            headMap.put("itemDes", materialAdjustDetailDTO.getItemDes()); // 物料描述
        }
        if (null != materialAdjustDetailDTO.getMaterialType()) {
            headMap.put("materialType", materialAdjustDetailDTO.getMaterialType()); // 旧物料类型
        }
        if (null != materialAdjustDetailDTO.getMaterialTypeNew()) {
            headMap.put("materialType", materialAdjustDetailDTO.getMaterialTypeNew()); // 新物料类型
        }
        if (null != materialAdjustHeaderDTO.getRemark()) {
            headMap.put("remark", materialAdjustHeaderDTO.getRemark()); //备注
        }
        if (null != materialAdjustHeaderDTO.getApplyTime()) {
            headMap.put("applyTime", materialAdjustHeaderDTO.getApplyTime().toString()); //申请日期
        }
        if (null != materialAdjustHeaderDTO.getApplyBy()) {
            final UserInfo userInfo = CacheDataUtils.findUserById(materialAdjustHeaderDTO.getApplyBy());
            if (userInfo != null) {
                headMap.put("applyBy", userInfo.getName()); //申请人
            }
        }

        if (null != materialAdjustHeaderDTO.getOrganizationId()) {
            final OrganizationRelDto relDto =
                    CacheDataUtils.findOrganizationById(materialAdjustHeaderDTO.getOrganizationId());
            if (relDto != null) {
                materialAdjustHeaderDTO.setOrganizationName(relDto.getOrganizationName());
                headMap.put("organizationId", relDto.getOrganizationName()); //库存组织
            }
        }
        responseMap.setHeadMap(headMap);

        responseMap.setStatus("success");
        responseMap.setMsg("success");
        return responseMap;
    }

    @Override
    public Response validImportDetailNew(Long organizationId,
                                         List<MaterialAdjustDetailImportExcelVo> detailImportExcelVoList,
                                         Long adjustHeaderId) {
        // 校验当前使用单位的物料编码规则
        String materialCodeRule = materialExtService.getMaterialCodeRule(SystemContext.getUnitId());
        if (StringUtils.isBlank(materialCodeRule)) {
            throw new BizException(100, "物料新增导入数据失败，当前使用单位未配置物料编码规则");
        }

        List<MaterialAdjustDetailDTO> materialAdjustDetailDTOList;
        TwoTuple<Boolean, List<MaterialAdjustDetailDTO>> twoTuple;
        if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
            materialAdjustDetailDTOList = BeanConverter.copy(detailImportExcelVoList, MaterialAdjustDetailDTO.class);
            // 导入数据校验，并返回修改校验结果
            twoTuple = getValidDetailResultsNew(organizationId, materialAdjustDetailDTOList);
        } else if (MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
            materialAdjustDetailDTOList = BeanConverter.copy(detailImportExcelVoList, MaterialAdjustDetailDTO.class);
            // 导入数据校验，并返回修改校验结果
            twoTuple = getValidDetailResultsForKuka2022(organizationId, materialAdjustDetailDTOList, adjustHeaderId, materialCodeRule, false);
        } else {
            throw new BizException(100, "物料新增导入数据失败，当前使用单位配置的物料编码规则未找到对应校验逻辑");
        }

        DataResponse<Object> dataResponse = Response.dataResponse();
        Boolean result = twoTuple.getFirst();
        List<MaterialAdjustDetailDTO> finalDTOList = twoTuple.getSecond();
        if (result) {
            dataResponse.setMsg("SUCCESS");
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(finalDTOList);
    }

    private TwoTuple<Boolean, List<MaterialAdjustDetailDTO>> getValidDetailResultsForKuka2022(Long organizationId,
                                                                                              List<MaterialAdjustDetailDTO> materialAdjustDetailDTOList,
                                                                                              Long adjustHeaderId, String materialCodeRule,
                                                                                              boolean middleAndSmallClassCheck) {
        boolean result = true;

        //获得所有使用当前物料编码规则的使用单位
        List<Unit> units = materialExtService.getAllUnitByUsingMaterialCodeRule(materialCodeRule);

        //根据使用单位获得对应所有库存组织
        String url = String.format("%sorganizationRel/queryByUnitId", ModelsEnum.BASEDATA.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, units.stream().map(Unit::getId).collect(Collectors.toList()),
                String.class);
        List<OrganizationRel> organizationRels = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<OrganizationRel>>>() {
        }).getData();
        List<Long> organizationIds = new ArrayList<>();
        Map<Long, String> organizationMap = new HashMap<>();
        for (OrganizationRel organizationRel : organizationRels) {
            Long orgId = organizationRel.getOrganizationId();
            if (!organizationIds.contains(orgId)) {
                organizationIds.add(orgId);
                organizationMap.put(orgId, organizationRel.getOrganizationName());
            }
        }

        // 物料编码规则
        List<MerielSubclassVO> materialClasses = erpCodeRuleService.findErpCodeRuleByRuleName(materialCodeRule);
        Guard.notNullOrEmpty(materialClasses, "物料编码规则" + MaterialCodeRuleEnum.KUKA2022.getName() + "尚未维护数据,请先维护");
        Map<String, MerielSubclassVO> materialClassMap = new HashMap<>();
        for (MerielSubclassVO merielSubclassVO : materialClasses) {
            String erpCodeRuleKey = getErpCodeRuleKey(merielSubclassVO.getBigValue(), merielSubclassVO.getMiddileValue(),
                    merielSubclassVO.getValue());
            materialClassMap.put(erpCodeRuleKey, merielSubclassVO);
        }

        // 非标件大类
        List<String> nonStandardMaterialType = basedataExtService.getLtcDict(DictType.MATERIAL_TYPE_NON_STANDARD.code(),
                null, null).stream().map(Dict::getName).distinct().collect(Collectors.toList());

        // 获取计量单位
        List<DictDto> dictDtoList = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Map<String, String> unitNameCodeMap = new HashMap<>();
        Map<String, String> unitCodeNameMap = new HashMap<>();
        for (DictDto dictDto : dictDtoList) {
            String code = dictDto.getCode();
            String name = dictDto.getName();
            unitNameCodeMap.put(name, code);
            unitCodeNameMap.put(code, name);
        }

        // 获取加工分类字典值
        List<String> machinedPartTypeList =
                basedataExtService.getLtcDict(MACHINED_PART_TYPE, null, null).stream().map(Dict::getName).distinct().collect(Collectors.toList());
        // 获取备件标识组织参数配置
        Set<String> orSparePartsMaskSet = organizationCustomDictService.queryByName(Constants.SPAREPARTS_MASK, SystemContext.getUnitId(),
                OrgCustomDictOrgFrom.COMPANY);
        if (orSparePartsMaskSet == null || orSparePartsMaskSet.isEmpty()) {
            throw new BizException(100, "备件标识需要在组织参数中维护");
        }
        // 获取物料类型及编码字典
        Map<String, String> materialTypeMap =
                basedataExtService.getLtcDict(MATERIAL_TYPE, null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));

        // 获取物料类型及编码字典
        List<String> materialTypes = Lists.newArrayList("外购物料", "看板物料", "装配件");

        // 物料编码规则字符忽略
        List<String> characterIgnoreList = basedataExtService.getLtcDict(DictType.MATERIAL_CODE_RULE_CHARACTER_IGNORE.code(), null, null).stream()
                .map(Dict::getName).distinct().collect(Collectors.toList());

        //获取品牌列表数据
        List<BrandMaintenance> brandMaintenanceList = basedataExtService.getBrandMaintenance();
        Guard.notNullOrEmpty(brandMaintenanceList, "请先维护品牌信息");
        List<String> brandMaintenanceCnLists = new ArrayList<>();
        List<String> brandMaintenanceEnLists = new ArrayList<>();
        for (BrandMaintenance brand : brandMaintenanceList) {
            if ((!DeletedFlag.INVALID.code().equals(brand.getDeletedFlag()) || 0 == brand.getBrandStatus())
                    && !Objects.equals(brand.getBrandNameCn(), "无")) {
                brandMaintenanceCnLists.add(brand.getBrandNameCn());
            }
            if ((!DeletedFlag.INVALID.code().equals(brand.getDeletedFlag()) || 0 == brand.getBrandStatus())
                    && !Objects.equals(brand.getBrandNameEn(), "NONE")) {
                brandMaintenanceEnLists.add(brand.getBrandNameEn());
            }
        }

        List<String> nonStandardMaterials = new ArrayList<>();

        List<String> standardMaterials = new ArrayList<>();

        List<Long> indexCheckList = new ArrayList<>();

        for (MaterialAdjustDetailDTO materialAdjustDetailDTO : materialAdjustDetailDTOList) {
            List<String> validResultList = new ArrayList<>();

            // 图号
            Integer figureNumberOracleLength = getOracleLengthFromMysqlVarchar(materialAdjustDetailDTO.getFigureNumber());
            // 型号/规格
            Integer modelOracleLength = getOracleLengthFromMysqlVarchar(materialAdjustDetailDTO.getModel());
            if (figureNumberOracleLength + modelOracleLength > 70) {
                result = false;
                validResultList.add("图号和型号字段的字符之和不能超过70");
            }
            // 表面处理
            String surfaceHandle = materialAdjustDetailDTO.getSurfaceHandle();
            int surfaceHandleLength = StringUtils.isNotEmpty(surfaceHandle) ? surfaceHandle.length() : 0;
            if (surfaceHandleLength > 32) {
                result = false;
                validResultList.add("表面处理字段的字符不能超过32");
            }

            if (null == materialAdjustDetailDTO.getIndex()) {
                result = false;
                validResultList.add("序号不能为空");
            } else {
                if (ListUtils.isNotEmpty(indexCheckList) && indexCheckList.contains(materialAdjustDetailDTO.getIndex())) {
                    result = false;
                    validResultList.add("序号不能重复");
                } else {
                    indexCheckList.add(materialAdjustDetailDTO.getIndex());
                }
            }
            if (StringUtils.isBlank(materialAdjustDetailDTO.getMaterialType())) {
                result = false;
                validResultList.add("物料类型不能为空");
            } else if (!materialTypes.contains(materialAdjustDetailDTO.getMaterialType())) {
                result = false;
                validResultList.add("物料类型只允许为:外购物料或者看板物料或者装配件");
            }
            if (StringUtils.isBlank(materialAdjustDetailDTO.getMaterialClass())) {
                result = false;
                validResultList.add("物料大类不能为空");
                materialAdjustDetailDTO.setValidResult(Joiner.on("，").join(validResultList));
                continue;
            } else if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(materialAdjustDetailDTO.getMaterialClass()))) {
                result = false;
                validResultList.add("物料大类:[" + materialAdjustDetailDTO.getMaterialClass() + "]尚未在物料编码规则:["
                        + MaterialCodeRuleEnum.KUKA2022.getName() + "]中维护,请先维护");
            }
            if (StringUtils.isBlank(materialAdjustDetailDTO.getMaterialMiddleClass())) {
                if (middleAndSmallClassCheck) {
                    result = false;
                    validResultList.add("物料中类不能为空");
                }
            } else if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(materialAdjustDetailDTO.getMaterialClass())
                    && materialClass.getMiddileValue().equals(materialAdjustDetailDTO.getMaterialMiddleClass()))) {
                result = false;
                validResultList.add("此物料中类不在填写的大类中");
            }
            if (StringUtils.isBlank(materialAdjustDetailDTO.getMaterialSmallClass())) {
                if (middleAndSmallClassCheck) {
                    result = false;
                    validResultList.add("物料小类不能为空");
                }
            } else {
                if (materialClasses.stream().noneMatch(materialClass -> materialClass.getBigValue().equals(materialAdjustDetailDTO.getMaterialClass())
                        && materialClass.getMiddileValue().equals(materialAdjustDetailDTO.getMaterialMiddleClass())
                        && materialClass.getValue().equals(materialAdjustDetailDTO.getMaterialSmallClass()))) {
                    result = false;
                    validResultList.add("此物料小类不在填写的大类或中类中");
                }
            }
            if (StringUtils.isBlank(materialAdjustDetailDTO.getUnit())) {
                result = false;
                validResultList.add("单位不能为空");
            } else if (!unitNameCodeMap.containsKey(materialAdjustDetailDTO.getUnit())) {
                result = false;
                validResultList.add("单位必须为字典项measurement_unit的值");
            }
            if (StringUtils.isBlank(materialAdjustDetailDTO.getName())) {
                result = false;
                validResultList.add("名称不能为空");
            } else if (materialAdjustDetailDTO.getName().length() > 150) {
                result = false;
                validResultList.add("名称字符长度不能超过150");
            }
            if (StringUtils.isBlank(materialAdjustDetailDTO.getOrSparePartsMask())) {
                result = false;
                validResultList.add("备件标识不能为空");
            } else if (!orSparePartsMaskSet.contains(materialAdjustDetailDTO.getOrSparePartsMask())) {
                result = false;
                validResultList.add("备件标识必须为组织参数中配置的备件标识中的值");
            }
            //如果品牌不为空则转大写
            if (StringUtils.isNotEmpty(materialAdjustDetailDTO.getBrand())) {
                materialAdjustDetailDTO.setBrand(materialAdjustDetailDTO.getBrand().toUpperCase(Locale.ROOT));
            }
            //非标件校验
            if (nonStandardMaterialType.contains(materialAdjustDetailDTO.getMaterialClass())) {
                //「加工件分类」字段只能选择字典项中的值且不能为空
                if (StringUtils.isBlank(materialAdjustDetailDTO.getMachiningPartType())) {
                    result = false;
                    validResultList.add("非标件,加工分类不能为空");
                } else if (!machinedPartTypeList.contains(materialAdjustDetailDTO.getMachiningPartType())) {
                    result = false;
                    validResultList.add("非标件,加工分类只能选择字典项中的值");
                }
                // 图号必填
                if (StringUtils.isBlank(materialAdjustDetailDTO.getFigureNumber())) {
                    result = false;
                    validResultList.add("非标件,图号不能为空");
                }
                if (StringUtils.isBlank(materialAdjustDetailDTO.getChartVersion())) {
                    result = false;
                    validResultList.add("非标件,图纸版本号不能为空");
                } else if (materialAdjustDetailDTO.getChartVersion().length() > 2) {
                    result = false;
                    validResultList.add("图纸版本号字符长度不能超过2");
                }
                if (StringUtils.isBlank(materialAdjustDetailDTO.getMaterial())) {
                    result = false;
                    validResultList.add("非标件,材质不能为空");
                }
                if (StringUtils.isBlank(materialAdjustDetailDTO.getSurfaceHandle())) {
                    result = false;
                    validResultList.add("非标件,表面处理不能为空");
                }
                if (null == materialAdjustDetailDTO.getUnitWeight()) {
                    result = false;
                    validResultList.add("非标件,单位重量不能为空");
                }
                if (ListUtils.isNotEmpty(nonStandardMaterials) && nonStandardMaterials.contains(materialAdjustDetailDTO.getFigureNumber())) {
                    result = false;
                    validResultList.add("所导入数据中,存在多条图号:[" + materialAdjustDetailDTO.getFigureNumber() + "]的数据");
                } else {
                    nonStandardMaterials.add(materialAdjustDetailDTO.getFigureNumber());
                }
                if (StringUtils.isNotEmpty(materialAdjustDetailDTO.getBrand())) {
                    materialAdjustDetailDTO.setBrand(materialAdjustDetailDTO.getBrand().toUpperCase(Locale.ROOT));
                    if (materialAdjustDetailDTO.getBrand().equals("无") && materialAdjustDetailDTO.getBrand().equals("NONE")) {
                        result = false;
                        validResultList.add("品牌字符不能为无和NONE");
                    }
                    if (materialAdjustDetailDTO.getBrand().length() > 150) {
                        result = false;
                        validResultList.add("品牌字符长度不能超过150");
                    }
                    if (!brandMaintenanceCnLists.contains(materialAdjustDetailDTO.getBrand())
                            && !brandMaintenanceEnLists.contains(materialAdjustDetailDTO.getBrand())) {
                        result = false;
                        validResultList.add("在品牌表中不存在");
                    }
                }
            } else {
                //标准件校验
                if (StringUtils.isBlank(materialAdjustDetailDTO.getBrand())) {
                    result = false;
                    validResultList.add("品牌不能为空");
                } else {
                    materialAdjustDetailDTO.setBrand(materialAdjustDetailDTO.getBrand().toUpperCase(Locale.ROOT));
                    if (materialAdjustDetailDTO.getBrand().equals("无") && materialAdjustDetailDTO.getBrand().equals("NONE")) {
                        result = false;
                        validResultList.add("品牌字符不能同时为无和NONE");
                    }
                    if (materialAdjustDetailDTO.getBrand().length() > 150) {
                        result = false;
                        validResultList.add("品牌字符长度不能超过150");
                    }
                    if (!brandMaintenanceCnLists.contains(materialAdjustDetailDTO.getBrand())
                            && !brandMaintenanceEnLists.contains(materialAdjustDetailDTO.getBrand())) {
                        result = false;
                        validResultList.add("在品牌表中不存在");
                    } else {
                        String brandName = brandMaintenanceList.stream().filter(brand ->
                                        (Objects.equals(brand.getBrandNameCn(), materialAdjustDetailDTO.getBrand())
                                                || Objects.equals(brand.getBrandNameEn(), materialAdjustDetailDTO.getBrand()))
                                                && (Boolean.FALSE.equals(brand.getDeletedFlag()) || NumberUtil.equals(0, brand.getBrandStatus())))
                                .collect(Collectors.toList()).get(0).getBrandName();
                        if (ListUtils.isNotEmpty(standardMaterials) && standardMaterials.contains(brandName + ":"
                                + removeCharacterIgnoreList(materialAdjustDetailDTO.getModel(), characterIgnoreList))) {
                            result = false;
                            validResultList.add("所导入数据中,存在多条品牌:[" + brandName + "] + 型号/规格:[" + materialAdjustDetailDTO.getModel() + "]的数据");
                        } else {
                            standardMaterials.add(brandName + ":"
                                    + removeCharacterIgnoreList(materialAdjustDetailDTO.getModel(), characterIgnoreList));
                        }
                    }
                }
                if (StringUtils.isBlank(materialAdjustDetailDTO.getModel())) {
                    result = false;
                    validResultList.add("型号/规格不能为空");
                } else {
                    if (materialAdjustDetailDTO.getModel().length() > 150) {
                        result = false;
                        validResultList.add("型号/规格字符长度不能超过150");
                    }
                }
            }
            materialAdjustDetailDTO.setValidResult(Joiner.on("，").join(validResultList));
        }

        //必填校验与重复校验后如果无问题 从库里查重
        if (result) {
            Map<Long, MaterialDto> forFillImportMap = new HashMap<>();
            for (MaterialAdjustDetailDTO materialAdjustDetailDTO : materialAdjustDetailDTOList) {
                String brandName = null;
                if (StringUtils.isNotEmpty(materialAdjustDetailDTO.getBrand())) {
                    brandName = brandMaintenanceList.stream().filter(brand ->
                                    (Objects.equals(brand.getBrandNameCn(), materialAdjustDetailDTO.getBrand())
                                            || Objects.equals(brand.getBrandNameEn(), materialAdjustDetailDTO.getBrand()))
                                            && (Boolean.FALSE.equals(brand.getDeletedFlag()) || NumberUtil.equals(0, brand.getBrandStatus())))
                            .collect(Collectors.toList()).get(0).getBrandName();
                }
                // 先校验有没有对应的物料导入缓存
                String cacheKey = MaterialImportUtils.getMaterialImportKey(materialAdjustDetailDTO.getFigureNumber(), brandName,
                        materialAdjustDetailDTO.getModel(), nonStandardMaterialType, materialAdjustDetailDTO.getMaterialClass());
                if (MaterialImportUtils.hasCacheKey(cacheKey)) {
                    continue;
                }

                List<String> validResultList = new ArrayList<>();
                //非标件校验
                if (nonStandardMaterialType.contains(materialAdjustDetailDTO.getMaterialClass())) {
                    result = checkQuotable(materialAdjustDetailDTO, materialAdjustDetailDTO.getFigureNumber(), null, null,
                            nonStandardMaterialType, organizationId, validResultList, result, forFillImportMap, adjustHeaderId, organizationIds,
                            materialClassMap, organizationMap, characterIgnoreList);
                } else {
                    result = checkQuotable(materialAdjustDetailDTO, null, brandName, materialAdjustDetailDTO.getModel(),
                            nonStandardMaterialType, organizationId, validResultList, result, forFillImportMap, adjustHeaderId, organizationIds,
                            materialClassMap, organizationMap, characterIgnoreList);
                }
                materialAdjustDetailDTO.setValidResult(Joiner.on("，").join(validResultList));
            }

            if (result) {
                List<MaterialAdjustDetailDTO> finalDTOList = new ArrayList<>();
                for (MaterialAdjustDetailDTO materialAdjustDetailDTO : materialAdjustDetailDTOList) {
                    String brandName = null;
                    if (StringUtils.isNotEmpty(materialAdjustDetailDTO.getBrand())) {
                        brandName = brandMaintenanceList.stream().filter(brand ->
                                        (Objects.equals(brand.getBrandNameCn(), materialAdjustDetailDTO.getBrand())
                                                || Objects.equals(brand.getBrandNameEn(), materialAdjustDetailDTO.getBrand()))
                                                && (Boolean.FALSE.equals(brand.getDeletedFlag()) || NumberUtil.equals(0, brand.getBrandStatus())))
                                .collect(Collectors.toList()).get(0).getBrandName();
                    }
                    // 先校验有没有对应的物料导入缓存
                    String cacheKey = MaterialImportUtils.getMaterialImportKey(materialAdjustDetailDTO.getFigureNumber(), brandName,
                            materialAdjustDetailDTO.getModel(), nonStandardMaterialType, materialAdjustDetailDTO.getMaterialClass());
                    MaterialImportCache materialImportCache = MaterialImportUtils.getCache(cacheKey);

                    if (null != materialImportCache) {
                        logger.info("cacheKey：{}的materialImportCache：{}", cacheKey, JsonUtils.toString(materialImportCache));
                        boolean allExtend = Objects.equals(materialImportCache.getOrganizationId(), organizationId);
                        if (allExtend) {
                            //全部继承
                            finalDTOList.add(materialImportCache);
                        } else {
                            //部分继承
                            materialAdjustDetailDTO.setOrganizationId(Objects.nonNull(materialImportCache.getOrganizationId())
                                    ? materialImportCache.getOrganizationId() : materialAdjustDetailDTO.getOrganizationId());
                            materialAdjustDetailDTO.setPamCode(StringUtils.isNotEmpty(materialImportCache.getPamCode())
                                    ? materialImportCache.getPamCode() : materialAdjustDetailDTO.getPamCode());
                            materialAdjustDetailDTO.setErpCode(StringUtils.isNotEmpty(materialImportCache.getErpCode())
                                    ? materialImportCache.getErpCode() : materialAdjustDetailDTO.getErpCode());
                            materialAdjustDetailDTO.setItemDes(StringUtils.isNotEmpty(materialImportCache.getItemDes())
                                    ? materialImportCache.getItemDes() : materialAdjustDetailDTO.getItemDes());
                            materialAdjustDetailDTO.setMaterialClass(StringUtils.isNotEmpty(materialImportCache.getMaterialClass())
                                    ? materialImportCache.getMaterialClass() : materialAdjustDetailDTO.getMaterialClass());
                            materialAdjustDetailDTO.setMaterialClassId(Objects.nonNull(materialImportCache.getMaterialClassId())
                                    ? materialImportCache.getMaterialClassId() : materialAdjustDetailDTO.getMaterialClassId());
                            materialAdjustDetailDTO.setMaterialMiddleClass(StringUtils.isNotEmpty(materialImportCache.getMaterialMiddleClass())
                                    ? materialImportCache.getMaterialMiddleClass() : materialAdjustDetailDTO.getMaterialMiddleClass());
                            materialAdjustDetailDTO.setMaterialMiddleClassId(Objects.nonNull(materialImportCache.getMaterialMiddleClassId())
                                    ? materialImportCache.getMaterialMiddleClassId() : materialAdjustDetailDTO.getMaterialMiddleClassId());
                            materialAdjustDetailDTO.setMaterialSmallClass(StringUtils.isNotEmpty(materialImportCache.getMaterialSmallClass())
                                    ? materialImportCache.getMaterialSmallClass() : materialAdjustDetailDTO.getMaterialSmallClass());
                            materialAdjustDetailDTO.setMaterialSmallClassId(Objects.nonNull(materialImportCache.getMaterialSmallClassId())
                                    ? materialImportCache.getMaterialSmallClassId() : materialAdjustDetailDTO.getMaterialSmallClassId());
                            materialAdjustDetailDTO.setName(StringUtils.isNotEmpty(materialImportCache.getName())
                                    ? materialImportCache.getName() : materialAdjustDetailDTO.getName());
                            materialAdjustDetailDTO.setModel(StringUtils.isNotEmpty(materialImportCache.getModel())
                                    ? materialImportCache.getModel() : materialAdjustDetailDTO.getModel());
                            materialAdjustDetailDTO.setBrand(StringUtils.isNotEmpty(materialImportCache.getBrand())
                                    ? materialImportCache.getBrand() : materialAdjustDetailDTO.getBrand());
                            materialAdjustDetailDTO.setMachiningPartType(StringUtils.isNotEmpty(materialImportCache.getMachiningPartType())
                                    ? materialImportCache.getMachiningPartType() : materialAdjustDetailDTO.getMachiningPartType());
                            materialAdjustDetailDTO.setMaterial(StringUtils.isNotEmpty(materialImportCache.getMaterial())
                                    ? materialImportCache.getMaterial() : materialAdjustDetailDTO.getMaterial());
                            materialAdjustDetailDTO.setUnitWeight(Objects.nonNull(materialImportCache.getUnitWeight())
                                    ? materialImportCache.getUnitWeight() : materialAdjustDetailDTO.getUnitWeight());
                            materialAdjustDetailDTO.setSurfaceHandle(StringUtils.isNotEmpty(materialImportCache.getSurfaceHandle())
                                    ? materialImportCache.getSurfaceHandle() : materialAdjustDetailDTO.getSurfaceHandle());
                            materialAdjustDetailDTO.setFigureNumber(StringUtils.isNotEmpty(materialImportCache.getFigureNumber())
                                    ? materialImportCache.getFigureNumber() : materialAdjustDetailDTO.getFigureNumber());
                            materialAdjustDetailDTO.setUnitCode(StringUtils.isNotEmpty(materialImportCache.getUnitCode())
                                    ? materialImportCache.getUnitCode() : materialAdjustDetailDTO.getUnitCode());
                            materialAdjustDetailDTO.setUnit(StringUtils.isNotEmpty(materialImportCache.getUnit())
                                    ? materialImportCache.getUnit() : materialAdjustDetailDTO.getUnit());
                            materialAdjustDetailDTO.setMaterialTypeCode(StringUtils.isNotEmpty(materialImportCache.getMaterialTypeCode())
                                    ? materialImportCache.getMaterialTypeCode() : materialAdjustDetailDTO.getMaterialTypeCode());
                            finalDTOList.add(materialAdjustDetailDTO);
                        }
                    } else {
                        materialAdjustDetailDTO.setOrganizationId(organizationId);
                        MaterialDto materialDto = forFillImportMap.get(materialAdjustDetailDTO.getIndex());
                        if (null != materialDto) {
                            materialAdjustDetailDTO.setPamCode(materialDto.getPamCode());
                            materialAdjustDetailDTO.setErpCode(materialDto.getItemCode());
                            materialAdjustDetailDTO.setItemDes(materialDto.getItemInfo());
                            Integer itemDesLength = getOracleLengthFromMysqlVarchar(materialAdjustDetailDTO.getItemDes());
                            if (itemDesLength > 240) {
                                materialAdjustDetailDTO.setValidResult("物料描述的字段长度超过了240个字符");
                                result = false;
                            }
                            materialAdjustDetailDTO.setMaterialClass(materialDto.getMaterialClassification());
                            materialAdjustDetailDTO.setMaterialClassId(materialDto.getMaterialClassificationId());
                            materialAdjustDetailDTO.setMaterialMiddleClass(materialDto.getCodingMiddleclass());
                            materialAdjustDetailDTO.setMaterialMiddleClassId(materialDto.getCodingMiddleclassId());
                            materialAdjustDetailDTO.setMaterialSmallClass(materialDto.getMaterialType());
                            materialAdjustDetailDTO.setMaterialSmallClassId(materialDto.getMaterialTypeId());
                            materialAdjustDetailDTO.setName(materialDto.getName());
                            materialAdjustDetailDTO.setModel(materialDto.getModel());
                            materialAdjustDetailDTO.setBrand(materialDto.getBrand());
                            materialAdjustDetailDTO.setMachiningPartType(materialDto.getMachiningPartType());
                            materialAdjustDetailDTO.setMaterial(materialDto.getMaterial());
                            materialAdjustDetailDTO.setUnitWeight(materialDto.getUnitWeight());
                            materialAdjustDetailDTO.setSurfaceHandle(materialDto.getMaterialProcessing());//材质处理
                            materialAdjustDetailDTO.setFigureNumber(materialDto.getFigureNumber());
                            // 引用了物料
                            String unitCode = "";
                            if (StringUtils.isNotEmpty(materialDto.getAdjustCode())) {
                                // 引用了adjust
                                unitCode = materialDto.getUnitCode();
                                if (!unitCodeNameMap.containsKey(unitCode)) {
                                    String errorMsg = String.format("引用的物料adjust表的pam编码：%s的物料的单位：%s 格式错误", materialDto.getPamCode(), unitCode);
                                    materialAdjustDetailDTO.setValidResult(errorMsg);
                                    result = false;
                                }
                            } else {
                                // 引用了material
                                unitCode = materialDto.getUnit();
                                if (!unitCodeNameMap.containsKey(unitCode)) {
                                    String errorMsg = String.format("引用的物料material表的pam编码：%s的物料的单位：%s 格式错误", materialDto.getPamCode(), unitCode);
                                    materialAdjustDetailDTO.setValidResult(errorMsg);
                                    result = false;
                                }
                            }
                            materialAdjustDetailDTO.setUnitCode(unitCode);
                            materialAdjustDetailDTO.setUnit(unitCodeNameMap.get(unitCode));
                            //封装使用单位编码
                            materialAdjustDetailDTO.setMaterialTypeCode(materialTypeMap.get(materialAdjustDetailDTO.getMaterialType()));//
                            // 封装物料类型编码（对应material_type字典中的code值，对应物料基础表的item_type（物料用户类型））
                        } else {
                            // 封装使用单位编码
                            materialAdjustDetailDTO.setUnitCode(unitNameCodeMap.get(materialAdjustDetailDTO.getUnit()));
                            // 封装物料类型编码（对应material_type字典中的code值，对应物料基础表的item_type（物料用户类型））
                            materialAdjustDetailDTO.setMaterialTypeCode(materialTypeMap.get(materialAdjustDetailDTO.getMaterialType()));
                            // 生成PAM编码
                            materialAdjustDetailDTO.setPamCode(materialExtService.generateMaterialPAMCode());

                            if (StringUtils.isNotEmpty(materialAdjustDetailDTO.getBrand())) {
                                materialAdjustDetailDTO.setBrand(brandName);
                            }
                            // 生成物料描述
                            materialAdjustDetailDTO.setItemDes(generateMaterialDesForKuka2022(materialAdjustDetailDTO, nonStandardMaterialType));
                            Integer itemDesLength = getOracleLengthFromMysqlVarchar(materialAdjustDetailDTO.getItemDes());
                            if (itemDesLength > 240) {
                                materialAdjustDetailDTO.setValidResult("物料描述的字段长度超过了240个字符");
                                result = false;
                            }
                        }

                        if (result) {
                            // 导入校验成功后就存入缓存
                            materialImportCache = BeanConverter.copy(materialAdjustDetailDTO, MaterialImportCache.class);
                            MaterialImportUtils.putCacheWithTimeExpire(cacheKey, materialImportCache, 5, TimeUnit.MINUTES);
                        }
                        finalDTOList.add(materialAdjustDetailDTO);
                    }
                }
                materialAdjustDetailDTOList = finalDTOList;

            }

        }

        return new TwoTuple<>(result, materialAdjustDetailDTOList);
    }

    private Boolean checkQuotable(MaterialAdjustDetailDTO materialAdjustDetailDTO, String figureNumber, String brand, String model,
                                  List<String> nonStandardMaterialType, Long organizationId, List<String> validResultList,
                                  Boolean result, Map<Long, MaterialDto> forFillImportMap, Long adjustHeaderId, List<Long> organizationIds,
                                  Map<String, MerielSubclassVO> materialClassMap, Map<Long, String> organizationMap,
                                  List<String> characterIgnoreList) {
        //查出所有相关的物料
        // KUKA2022 规则下标准件的 model 忽略一些特殊符号
        List<MaterialDto> adjustCodes = getAdjustCodeForKukaCodeRule(figureNumber, brand, null, organizationIds, adjustHeaderId);
        if (nonStandardMaterialType.contains(materialAdjustDetailDTO.getMaterialClass())) {
            adjustCodes = adjustCodes.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                            && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                            e.getMaterialType())))
                    .collect(Collectors.toList());
        } else {
            adjustCodes = adjustCodes.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                            && Objects.equals(removeCharacterIgnoreList(model, characterIgnoreList),
                            removeCharacterIgnoreList(e.getModel(), characterIgnoreList))
                            && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                            e.getMaterialType())))
                    .collect(Collectors.toList());
        }
        List<MaterialDto> currentAdjustMaterials = new ArrayList<>();
        List<MaterialDto> otherAdjustMaterials = new ArrayList<>();
        for (MaterialDto adjustMaterial : adjustCodes) {
            if (Objects.equals(adjustMaterial.getOrganizationId(), organizationId)) {
                currentAdjustMaterials.add(adjustMaterial);
            } else {
                otherAdjustMaterials.add(adjustMaterial);
            }
        }

        //非标件就只根据图号 ,标件根据品牌 和型号/规格 查 基础信息表 未退市和未删除的物料
        // KUKA2022 规则下标准件的 model 忽略一些特殊符号
        List<MaterialDto> materialForCheckRepeat = materialExtService.getMaterialForCheckRepeat(figureNumber, brand, null, organizationIds);
        if (nonStandardMaterialType.contains(materialAdjustDetailDTO.getMaterialClass())) {
            materialForCheckRepeat = materialForCheckRepeat.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                            && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                            e.getMaterialType())))
                    .collect(Collectors.toList());
        } else {
            materialForCheckRepeat = materialForCheckRepeat.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                            && Objects.equals(removeCharacterIgnoreList(model, characterIgnoreList),
                            removeCharacterIgnoreList(e.getModel(), characterIgnoreList))
                            && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                            e.getMaterialType())))
                    .collect(Collectors.toList());
        }
        List<MaterialDto> currentMaterials = new ArrayList<>();
        List<MaterialDto> otherMaterials = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(materialForCheckRepeat)) {
            for (MaterialDto materialDto : materialForCheckRepeat) {
                if (Objects.equals(materialDto.getOrganizationId(), organizationId)) {
                    currentMaterials.add(materialDto);
                } else {
                    otherMaterials.add(materialDto);
                }
            }
        }

        ;

        // adj无
        if (ListUtils.isEmpty(adjustCodes)) {
            if (ListUtils.isNotEmpty(materialForCheckRepeat)) {
                // adj无，本组织mat有 报错或引用
                if (CollectionUtils.isNotEmpty(currentMaterials)) {
                    result = false;
                    List<String> pamCodeList = currentMaterials.stream().map(Material::getPamCode).collect(Collectors.toList());
                    validResultList.add("物料基础表中,已存在" + (StringUtils.isBlank(figureNumber) ? "" :
                            "图号:[" + figureNumber + "]") + (StringUtils.isBlank(brand) ? "" :
                            "品牌:[" + brand + "]") + (StringUtils.isBlank(model) ? "" :
                            "型号/规格:[" + model + "]") + "的数据" +
                            (ListUtils.isEmpty(pamCodeList)?"" : ",PAM编码:[" + Joiner.on(",").join(pamCodeList) + "]")
                            );
                } else {
                    // adj无，本组织mat无，其他组织mat有
                    forFillImportMap.put(materialAdjustDetailDTO.getIndex(), otherMaterials.get(0));
                }
            } else {
                // adj无，mat没有(没有 未退市未删除)
                return result;
            }
        } else {
            //非标件就只查图号 标件查品牌 和型号/规格的
            // KUKA2022 规则下标准件的 model 忽略一些特殊符号
            List<MaterialDto> delistAndDeleteMaterialDtos = materialExtService.getMaterialForCheckAdjustRepeat(figureNumber, brand, null,
                    organizationIds);
            if (nonStandardMaterialType.contains(materialAdjustDetailDTO.getMaterialClass())) {
                delistAndDeleteMaterialDtos =
                        delistAndDeleteMaterialDtos.stream().filter(e -> nonStandardMaterialType.contains(e.getMaterialClassification())
                                        && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                        e.getMaterialType())))
                                .collect(Collectors.toList());
            } else {
                delistAndDeleteMaterialDtos =
                        delistAndDeleteMaterialDtos.stream().filter(e -> !nonStandardMaterialType.contains(e.getMaterialClassification())
                                        && Objects.equals(removeCharacterIgnoreList(model, characterIgnoreList),
                                        removeCharacterIgnoreList(e.getModel(), characterIgnoreList))
                                        && materialClassMap.containsKey(getErpCodeRuleKey(e.getMaterialClassification(), e.getCodingMiddleclass(),
                                        e.getMaterialType())))
                                .collect(Collectors.toList());
            }
            List<MaterialDto> currentDelistAndDeleteMaterialDtos = new ArrayList<>();
            List<MaterialDto> otherDelistAndDeleteMaterialDtos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(delistAndDeleteMaterialDtos)) {
                for (MaterialDto materialDto : delistAndDeleteMaterialDtos) {
                    if (Objects.equals(materialDto.getOrganizationId(), organizationId)) {
                        currentDelistAndDeleteMaterialDtos.add(materialDto);
                    } else {
                        otherDelistAndDeleteMaterialDtos.add(materialDto);
                    }
                }
            }

            if (ListUtils.isNotEmpty(currentAdjustMaterials)) {
                // 本组织adj有
                List<MaterialDto> currentDtoList = currentAdjustMaterials.stream().filter(e ->
                        !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(currentDtoList)) {
                    //本组织adj有(有单据状态！="审批通过")
                    for (MaterialDto dto : currentDtoList) {
                        validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                    }
                    return false;
                }

                // 本组织adj有,本组织mat有
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    result = false;
                    List<String> pamCodeList = currentMaterials.stream().map(Material::getPamCode).collect(Collectors.toList());
                    validResultList.add("物料基础表中,已存在" + (StringUtils.isBlank(figureNumber) ? "" :
                            "图号:[" + figureNumber + "]") + (StringUtils.isBlank(brand) ? "" :
                            "品牌:[" + brand + "]") + (StringUtils.isBlank(model) ? "" :
                            "型号/规格:[" + model + "]") + "的数据" +
                            (ListUtils.isEmpty(pamCodeList)?"" : ",PAM编码:[" + Joiner.on(",").join(pamCodeList) + "]")
                            );
                } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    // 本组织adj有,本组织mat退市或删除
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        // 其他mat有
                        List<String> delistFlagPamCodeList =
                                currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag())
                                                && Boolean.FALSE.equals(e.getDeleteFlag()))
                                        .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            return false;
                        }
                        forFillImportMap.put(materialAdjustDetailDTO.getIndex(), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        // 其他mat退市或删除
                        return result;
                    } else {
                        // 其他组织mat没有
                        if (CollectionUtils.isNotEmpty(otherAdjustMaterials)) {
                            // 其他组织adj有 --引用其他adj
                            List<String> delistFlagPamCodeList =
                                    currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag())
                                                    && Boolean.FALSE.equals(e.getDeleteFlag()))
                                            .map(MaterialDto::getPamCode).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                                //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                                validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                                return false;
                            }
                            List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                    !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(dtoList)) {
                                //本组织adj有 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                                for (MaterialDto dto : dtoList) {
                                    validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                            "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                                }
                                return false;
                            }
                            forFillImportMap.put(materialAdjustDetailDTO.getIndex(), otherAdjustMaterials.get(0));
                        } else {
                            // 其他组织adj没有 --新码
                            return result;
                        }
                    }
                } else {
                    // 本组织adj有,本组织mat无
                    Set<String> adjustCodeSet = currentAdjustMaterials.stream().map(MaterialDto::getAdjustCode).collect(Collectors.toSet());
                    result = false;
                    validResultList.add("单据号：" + adjustCodeSet.toString() + "已导入此库存组织物料");
                }
            } else {
                // 本组织adj无，其他adj有
                // 本组织mat有
                if (ListUtils.isNotEmpty(currentMaterials)) {
                    result = false;
                    List<String> pamCodeList = currentMaterials.stream().map(Material::getPamCode).collect(Collectors.toList());
                    validResultList.add("物料基础表中,已存在" + (StringUtils.isBlank(figureNumber) ? "" :
                            "图号:[" + figureNumber + "]") + (StringUtils.isBlank(brand) ? "" :
                            "品牌:[" + brand + "]") + (StringUtils.isBlank(model) ? "" :
                            "型号/规格:[" + model + "]") +  "的数据" +
                            (ListUtils.isEmpty(pamCodeList)?"" : ",PAM编码:[" + Joiner.on(",").join(pamCodeList) + "]")
                            );
                } else if (CollectionUtils.isNotEmpty(currentDelistAndDeleteMaterialDtos)) {
                    // 本组织adj无，其他adj有,本组织mat退市或删除
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        // 其他mat有  --引用
                        List<String> delistFlagPamCodeList =
                                currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag())
                                                && Boolean.FALSE.equals(e.getDeleteFlag()))
                                        .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat未退市未删除：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            return false;
                        }
                        forFillImportMap.put(materialAdjustDetailDTO.getIndex(), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        // 其他mat退市或删除 --新码
                        return result;
                    } else {
                        // 其他mat没有 --引用其他adj
                        List<String> delistFlagPamCodeList =
                                currentDelistAndDeleteMaterialDtos.stream().filter(e -> Boolean.TRUE.equals(e.getDelistFlag())
                                                && Boolean.FALSE.equals(e.getDeleteFlag()))
                                        .map(MaterialDto::getPamCode).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(delistFlagPamCodeList)) {
                            //本组织mat退市 + 其他组织mat没有数据 +其他组织adj有：不做部分继承，报错"本组织已存在退市的PAM物料编码XX"
                            validResultList.add("本组织已存在退市的PAM物料编码:[" + delistFlagPamCodeList.toString() + "]");
                            return false;
                        }
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat删除 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto dto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                            }
                            return false;
                        }
                        forFillImportMap.put(materialAdjustDetailDTO.getIndex(), otherAdjustMaterials.get(0));
                    }
                } else {
                    // 本组织adj无，其他adj有,本组织mat无
                    if (CollectionUtils.isNotEmpty(otherMaterials)) {
                        // 其他mat有  --引用
                        forFillImportMap.put(materialAdjustDetailDTO.getIndex(), otherMaterials.get(0));
                    } else if (CollectionUtils.isNotEmpty(otherDelistAndDeleteMaterialDtos)) {
                        // 其他mat退市或删除 --新码
                        return result;
                    } else {
                        List<MaterialDto> dtoList = otherAdjustMaterials.stream().filter(e ->
                                !Objects.equals(MaterialAdjustEnum.APPROVED.code(), e.getMaterialAdjustHeaderStatus())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(dtoList)) {
                            //本组织adj无 + 本组织mat无 + 其他组织mat无 + 其他组织adj有(有单据状态！="审批通过")
                            for (MaterialDto dto : dtoList) {
                                validResultList.add("库存组织[" + organizationMap.get(dto.getOrganizationId()) + "]" +
                                        "存在未生效单据[" + dto.getAdjustCode() + "]的Pam编码[" + dto.getPamCode() + "],请联系标准化处理");
                            }
                            return false;
                        }
                        // 其他mat没有 --引用其他adj
                        forFillImportMap.put(materialAdjustDetailDTO.getIndex(), otherAdjustMaterials.get(0));
                    }
                }
            }
        }
        return result;
    }

    private String getErpCodeRuleKey(String materialClassification, String codingMiddleclass, String materialType) {
        return String.format("getMaterialErpCodeRuleKey_%s_%s_%s", materialClassification, codingMiddleclass, materialType);
    }

    //去除字符串中含有 characterIgnoreList集合字符忽略
    private String removeCharacterIgnoreList(String str, List<String> characterIgnoreList) {
        if (StringUtils.isEmpty(str) || CollectionUtils.isEmpty(characterIgnoreList)) {
            return StringUtils.isEmpty(str) ? str : str.toUpperCase(Locale.ROOT);
        }
        for (String character : characterIgnoreList) {
            if (Objects.equals(character, "空格")) {
                //给str去空格
                str = str.replaceAll(" ", "");
            } else {
                str = str.replace(character, "");
            }
        }
        return str.toUpperCase(Locale.ROOT);
    }

    private TwoTuple<Boolean, List<MaterialAdjustDetailDTO>> getValidDetailResultsNew(Long organizationId,
                                                                                      List<MaterialAdjustDetailDTO> materialAdjustDetailDTOList) {
        boolean result = true;

        // 获取类别列表数据（row小类，column中类，value实体类）
        List<MerielSubclassVO> merielSubclassList = erpCodeRuleService.findErpCodeRuleByRuleName(MaterialCodeRuleEnum.KUNSHAN.getCode());
        // 用图号+版本号+库存组织判断唯一性的物料中类
        List<String> checkType3MaterialMiddleClassList = merielSubclassList.stream().filter(o -> Objects.equals(3, o.getCheckType()))
                .map(MerielSubclassVO::getMiddileValue).distinct().collect(Collectors.toList());
        HashBasedTable<String, String, MerielSubclassVO> merielClassTable = HashBasedTable.create();
        for (MerielSubclassVO merielSubclassVO : merielSubclassList) {
            merielClassTable.put(merielSubclassVO.getValue(), merielSubclassVO.getMiddileValue(), merielSubclassVO);
        }
        // 获取计量单位
        Map<String, String> unitMap = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null,
                null).stream().collect(Collectors.toMap(DictDto::getName, DictDto::getCode, (key1, key2) -> key2));
        // 获取加工分类字典值
        List<String> machinedPartTypeList = basedataExtService.getLtcDict(MACHINED_PART_TYPE, null,
                null).stream().map(Dict::getName).distinct().collect(Collectors.toList());
        // 获取备件标识组织参数配置
        Set<String> orSparePartsMaskSet = organizationCustomDictService.queryByName(Constants.SPAREPARTS_MASK, SystemContext.getUnitId(),
                OrgCustomDictOrgFrom.COMPANY);
        if (orSparePartsMaskSet == null || orSparePartsMaskSet.isEmpty()) {
            throw new BizException(100, "备件标识需要在组织参数中维护");
        }
        // 获取物料类型及编码字典
        Map<String, String> materialTypeMap =
                basedataExtService.getLtcDict(MATERIAL_TYPE, null, null).stream().collect(Collectors.toMap(DictDto::getName,
                        DictDto::getCode, (key1, key2) -> key2));
        //获取品牌列表数据
        List<BrandMaintenance> brandMaintenanceList = basedataExtService.getBrandMaintenance();
        Guard.notNullOrEmpty(brandMaintenanceList, "请先维护品牌信息");

        List<String> brandMaintenanceCnLists = brandMaintenanceList.stream().filter(brand ->
                        !DeletedFlag.INVALID.code().equals(brand.getDeletedFlag()) || 0 == brand.getBrandStatus())
                .map(BrandMaintenance::getBrandNameCn).filter(brand -> !Objects.equals(brand, "无")).collect(Collectors.toList());

        List<String> brandMaintenanceEnLists = brandMaintenanceList.stream().filter(brand ->
                        !DeletedFlag.INVALID.code().equals(brand.getDeletedFlag()) || 0 == brand.getBrandStatus())
                .map(BrandMaintenance::getBrandNameEn).filter(brand -> !Objects.equals(brand, "NONE")).collect(Collectors.toList());

        logger.info("1.导入物料数据基础信息校验开始");
        List<String> materialTypeList = Lists.newArrayList("外购物料", "看板物料");
        for (MaterialAdjustDetailDTO detailDTO : materialAdjustDetailDTOList) {
            List<String> validResultList = new ArrayList<>();
            if (!materialTypeList.contains(detailDTO.getMaterialType())) {
                result = false;
                validResultList.add("物料类型只能选择外购物料或者看板物料");
            }
            if (StringUtils.isBlank(detailDTO.getMaterialMiddleClass()) || !merielClassTable.columnKeySet().contains(detailDTO.getMaterialMiddleClass())) {
                result = false;
                validResultList.add("物料中类不能为空且只能为字典项的值");
            } else {
                if (checkType3MaterialMiddleClassList.contains(detailDTO.getMaterialMiddleClass())) {
                    if (StringUtils.isBlank(detailDTO.getFigureNumber())) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 图号不能为空");
                    }
                    if (StringUtils.isBlank(detailDTO.getChartVersion())) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 图纸版本号不能为空");
                    }
                    if (StringUtils.isBlank(detailDTO.getMachiningPartType())) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 加工分类不能为空");
                    }
                    if (StringUtils.isBlank(detailDTO.getMaterial())) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 材质不能为空");
                    }
                    if (detailDTO.getUnitWeight() == null) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 单件重量(Kg)不能为空");
                    }
                    if (StringUtils.isBlank(detailDTO.getSurfaceHandle())) {
                        result = false;
                        validResultList.add("此物料类型用图号判断唯一性 表面处理不能为空");
                    }
                }
            }
            String materialClass = "";
            if (StringUtils.isBlank(detailDTO.getMaterialSmallClass())) {
                result = false;
                validResultList.add("物料小类不能为空");
            } else {
                if (merielClassTable.rowKeySet().contains(detailDTO.getMaterialSmallClass())) {
                    if (merielClassTable.get(detailDTO.getMaterialSmallClass(), detailDTO.getMaterialMiddleClass()) == null) {
                        result = false;
                        validResultList.add("此物料小类不在填写的中类中");
                    } else {
                        // 根据小类中类获取大类并设置
                        materialClass = merielClassTable.get(detailDTO.getMaterialSmallClass(), detailDTO.getMaterialMiddleClass()).getBigValue();
                        detailDTO.setMaterialClass(materialClass);
                    }
                } else {
                    result = false;
                    validResultList.add("物料小类不存在");
                }
            }
            if (StringUtils.isBlank(detailDTO.getBrand())) {
                result = false;
                validResultList.add("品牌不能为空");
            } else {
                if (detailDTO.getBrand().length() > 150) {
                    result = false;
                    validResultList.add("品牌字符长度不能超过150");
                }
                if (!brandMaintenanceCnLists.contains(detailDTO.getBrand()) && !brandMaintenanceEnLists.contains(detailDTO.getBrand())) {
                    result = false;
                    validResultList.add("在品牌表中不存在");
                }
            }
            if (StringUtils.isBlank(detailDTO.getName())) {
                result = false;
                validResultList.add("名称不能为空");
            } else {
                if (detailDTO.getName().length() > 150) {
                    result = false;
                    validResultList.add("名称字符长度不能超过150");
                }
            }
            if (StringUtils.isBlank(detailDTO.getModel())) {
                result = false;
                validResultList.add("型号/规格不能为空");
            } else {
                if (detailDTO.getModel().length() > 150) {
                    result = false;
                    validResultList.add("型号/规格字符长度不能超过150");
                }
            }
            if (StringUtils.isBlank(detailDTO.getUnit()) || !unitMap.containsKey(detailDTO.getUnit())) {
                result = false;
                validResultList.add("单位不能为空且必须为字典项measurement_unit的值");
            }
            if (StringUtils.isNotBlank(detailDTO.getFigureNumber()) && detailDTO.getFigureNumber().length() > 150) {
                result = false;
                validResultList.add("图号字符长度不能超过150");
            }
            if (StringUtils.isNotBlank(detailDTO.getChartVersion()) && detailDTO.getChartVersion().length() > 2) {
                result = false;
                validResultList.add("图纸版本号字符长度不能超过2");
            }
            if (StringUtils.isNotBlank(detailDTO.getMachiningPartType()) && !machinedPartTypeList.contains(detailDTO.getMachiningPartType())) {
                result = false;
                validResultList.add("加工分类不为空时只能选择字典项中的值");
            }
            if (StringUtils.isBlank(detailDTO.getOrSparePartsMask()) || !orSparePartsMaskSet.contains(detailDTO.getOrSparePartsMask())) {
                result = false;
                validResultList.add("备件标识不能为空且必须为组织参数中配置的备件标识中的值");
            }
            detailDTO.setValidResult(Joiner.on("，").join(validResultList));
        }
        logger.info("2.导入物料数据基础信息校验结束");
        if (!result) {
            return new TwoTuple<>(false, materialAdjustDetailDTOList);
        }

        // 加入限流200
        RateLimiter rateLimiter = RateLimiter.create(200);
        // 判断唯一性
        long count;
        if (result) {
            for (MaterialAdjustDetailDTO detailDTO : materialAdjustDetailDTOList) {
                if ("机械加工件".equals(detailDTO.getMaterialMiddleClass())) { //物料中类+品牌+图号 检索相同编码
                    // 导入数据中存在多条相同的物料中类+品牌+图号
                    count = materialAdjustDetailDTOList.stream().filter(e -> Objects.equals(e.getMaterialMiddleClass(),
                            detailDTO.getMaterialMiddleClass())
                            && Objects.equals(e.getBrand(), detailDTO.getBrand()) && Objects.equals(e.getFigureNumber(),
                            detailDTO.getFigureNumber())).count();
                    if (count > 1) {
                        result = false;
                        detailDTO.setValidResult(detailDTO.getValidResult() + "，所有导入数据中存在多条相同的物料中类：" + detailDTO.getMaterialMiddleClass() + "，品牌:"
                                + detailDTO.getBrand() + "，图号:" + detailDTO.getFigureNumber() + "的数据");
                        continue;
                    }
                    List<String> adjustCodeList = checkMaterial1(detailDTO.getMaterialMiddleClass(), detailDTO.getBrand(),
                            detailDTO.getFigureNumber(), organizationId);
                    if (adjustCodeList.isEmpty()) {
                        rateLimiter.acquire();
                        List<Material> materials = materialExtService.checkMaterial1(detailDTO.getMaterialMiddleClass(), detailDTO.getBrand(),
                                detailDTO.getFigureNumber(), organizationId);
                        if (ListUtils.isNotEmpty(materials)) {
                            result = false;
                            detailDTO.setValidResult(detailDTO.getValidResult() + "，物料基础表已存在此物料中类+品牌+图号物料");
                        }
                    } else {
                        rateLimiter.acquire();
                        List<Material> materials = materialExtService.checkMaterial4(detailDTO.getMaterialMiddleClass(), detailDTO.getBrand(),
                                detailDTO.getFigureNumber(), organizationId);
                        if (CollectionUtils.isEmpty(materials)) {
                            result = false;
                            detailDTO.setValidResult(detailDTO.getValidResult() + "，单据号：" + Joiner.on("、").join(adjustCodeList) +
                                    "已导入此品牌+型号+图号+库存组织物料");
                        }
                    }
                } else {
                    if (StringUtils.isNotEmpty(detailDTO.getBrandMaterialCode())) { //品牌商物料编码存在  物料中类+品牌+品牌商物料编码
                        // 检索相同编码
                        // 导入数据中存在多条相同的物料中类+品牌+品牌商物料编码
                        count = materialAdjustDetailDTOList.stream().filter(e -> Objects.equals(e.getMaterialMiddleClass(),
                                detailDTO.getMaterialMiddleClass())
                                && Objects.equals(e.getBrand(), detailDTO.getBrand()) && Objects.equals(e.getBrandMaterialCode(),
                                detailDTO.getBrandMaterialCode())).count();
                        if (count > 1) {
                            result = false;
                            detailDTO.setValidResult(detailDTO.getValidResult() +
                                    "，所有导入数据中存在多条相同的物料中类：" + detailDTO.getMaterialMiddleClass() + "，品牌:"
                                    + detailDTO.getBrand() + "，品牌商物料编码:" + detailDTO.getBrandMaterialCode() +
                                    "的数据");
                            continue;
                        }
                        List<String> adjustCodeList = checkMaterial3(detailDTO.getMaterialMiddleClass(), detailDTO.getBrand(),
                                detailDTO.getBrandMaterialCode(), organizationId);
                        if (adjustCodeList.isEmpty()) {
                            rateLimiter.acquire();
                            List<Material> materials = materialExtService.checkMaterial3(detailDTO.getMaterialMiddleClass(),
                                    detailDTO.getBrand(), detailDTO.getBrandMaterialCode(), organizationId);
                            if (ListUtils.isEmpty(materials)) { //物料中类+品牌+规格/型号  检索相同编码
                                // 导入数据中存在多条相同的物料中类+品牌+规格/型号
                                count = materialAdjustDetailDTOList.stream().filter(e -> Objects.equals(e.getMaterialMiddleClass(),
                                        detailDTO.getMaterialMiddleClass())
                                        && Objects.equals(e.getBrand(), detailDTO.getBrand()) && Objects.equals(e.getModel(), detailDTO.getModel())).count();
                                if (count > 1) {
                                    result = false;
                                    detailDTO.setValidResult(detailDTO.getValidResult() +
                                            "，所有导入数据中存在多条相同的物料中类：" + detailDTO.getMaterialMiddleClass() + "，品牌:"
                                            + detailDTO.getBrand() + "，规格/型号:" + detailDTO.getModel() + "的数据");
                                    continue;
                                }
                                List<String> adjustCodeList2 = checkMaterial2(detailDTO.getMaterialMiddleClass(), detailDTO.getBrand(),
                                        detailDTO.getModel(), organizationId);
                                if (adjustCodeList2.isEmpty()) {
                                    rateLimiter.acquire();
                                    List<Material> materials1 = materialExtService.checkMaterial2(detailDTO.getMaterialMiddleClass(),
                                            detailDTO.getBrand(), detailDTO.getModel(), organizationId);
                                    if (ListUtils.isNotEmpty(materials1)) {
                                        result = false;
                                        detailDTO.setValidResult(detailDTO.getValidResult() + "，物料基础表已存在此物料中类+品牌+规格/型号物料");
                                    }
                                } else {
                                    rateLimiter.acquire();
                                    List<Material> materials1 = materialExtService.checkMaterial5(detailDTO.getMaterialMiddleClass(),
                                            detailDTO.getBrand(), detailDTO.getModel(), organizationId);
                                    if (CollectionUtils.isEmpty(materials1)) {
                                        result = false;
                                        detailDTO.setValidResult(detailDTO.getValidResult() + "，单据号：" + Joiner.on("、").join(adjustCodeList2) +
                                                "已导入此物料中类+品牌+规格/型号物料");
                                    }
                                }
                            } else {
                                result = false;
                                detailDTO.setValidResult(detailDTO.getValidResult() +
                                        "，物料基础表已存在此物料中类+品牌+品牌商物料编码物料");
                            }
                        } else {
                            rateLimiter.acquire();
                            List<Material> materials = materialExtService.checkMaterial6(detailDTO.getMaterialMiddleClass(),
                                    detailDTO.getBrand(), detailDTO.getBrandMaterialCode(), organizationId);
                            if (CollectionUtils.isEmpty(materials)) {
                                result = false;
                                detailDTO.setValidResult(detailDTO.getValidResult() + "，单据号：" + Joiner.on("、").join(adjustCodeList) +
                                        "已导入此物料中类+品牌+品牌商物料编码物料");
                            }
                        }
                    } else { //品牌商物料编码不存在  物料中类+品牌+规格/型号 检索相同编码
                        // 导入数据中存在多条相同的物料中类+品牌+规格/型号
                        count = materialAdjustDetailDTOList.stream().filter(e -> Objects.equals(e.getMaterialMiddleClass(),
                                detailDTO.getMaterialMiddleClass())
                                && Objects.equals(e.getBrand(), detailDTO.getBrand()) && Objects.equals(e.getModel(), detailDTO.getModel())).count();
                        if (count > 1) {
                            result = false;
                            detailDTO.setValidResult(detailDTO.getValidResult() +
                                    "，所有导入数据中存在多条相同的物料中类：" + detailDTO.getMaterialMiddleClass() + "，品牌:"
                                    + detailDTO.getBrand() + "，规格/型号:" + detailDTO.getModel() + "的数据");
                            continue;
                        }
                        List<String> adjustCodeList = checkMaterial2(detailDTO.getMaterialMiddleClass(), detailDTO.getBrand(), detailDTO.getModel()
                                , organizationId);
                        if (adjustCodeList.isEmpty()) {
                            rateLimiter.acquire();
                            List<Material> materials = materialExtService.checkMaterial2(detailDTO.getMaterialMiddleClass(),
                                    detailDTO.getBrand(), detailDTO.getModel(), organizationId);
                            if (ListUtils.isNotEmpty(materials)) {
                                result = false;
                                detailDTO.setValidResult(detailDTO.getValidResult() + "，物料基础表已存在此物料中类+品牌+规格/型号物料");
                            }
                        } else {
                            rateLimiter.acquire();
                            List<Material> materials = materialExtService.checkMaterial5(detailDTO.getMaterialMiddleClass(),
                                    detailDTO.getBrand(), detailDTO.getModel(), organizationId);
                            if (CollectionUtils.isEmpty(materials)) {
                                result = false;
                                detailDTO.setValidResult(detailDTO.getValidResult() + "，单据号：" + Joiner.on("、").join(adjustCodeList) +
                                        "已导入此物料中类+品牌+规格/型号物料");
                            }
                        }
                    }
                }
            }
        }

        if (result) {
            for (MaterialAdjustDetailDTO detailDTO : materialAdjustDetailDTOList) {
                String brandName = brandMaintenanceList.stream().filter(brand ->
                                (Objects.equals(brand.getBrandNameCn(), detailDTO.getBrand()) || Objects.equals(brand.getBrandNameEn(),
                                        detailDTO.getBrand()))
                                        && (DeletedFlag.VALID.code().equals(brand.getDeletedFlag()) || NumberUtil.equals(0, brand.getBrandStatus())))
                        .collect(Collectors.toList()).get(0).getBrandName();
                detailDTO.setBrand(brandName);
                // 封装使用单位编码
                detailDTO.setUnitCode(unitMap.get(detailDTO.getUnit()));
                // 封装物料类型编码（对应material_type字典中的code值，对应物料基础表的item_type（物料用户类型））
                detailDTO.setMaterialTypeCode(materialTypeMap.get(detailDTO.getMaterialType()));
                // 生成物料描述
                detailDTO.setItemDes(generateMaterialDes(detailDTO));
                Integer itemDesLength = getOracleLengthFromMysqlVarchar(detailDTO.getItemDes());
                if (itemDesLength > 240) {
                    detailDTO.setValidResult("物料描述的字段长度超过了240个字符");
                    result = false;
                }
                // 生成PAM编码
                detailDTO.setPamCode(materialExtService.generateMaterialPAMCode());
            }
        }
        return new TwoTuple<>(result, materialAdjustDetailDTOList);
    }

    private Map<String, String> getInventoryTypeMap(Long organizationId, List<String> materialClassificationList,
                                                    List<String> codingMiddleClassList, List<String> materielTypeList) {
        Map<String, String> inventoryTypeMap = new HashMap<>();
        // 获取物料类别配置（库存分类）
        MaterialCustomDictHeaderExample customDictHeaderExample = new MaterialCustomDictHeaderExample();
        customDictHeaderExample.createCriteria().andDeletedFlagEqualTo(false).andOrganizationIdEqualTo(organizationId)
                .andCodingClassIn(materialClassificationList).andCodingMiddleclassIn(codingMiddleClassList).andCodingSubclassIn(materielTypeList);
        List<MaterialCustomDictHeader> materialCustomDictHeaders = materialCustomDictHeaderMapper.selectByExample(customDictHeaderExample);
        Map<String, List<Long>> materialCustomDictHeaderMap = new HashMap<>();
        List<Long> headerIdList = new ArrayList<>();
        for (MaterialCustomDictHeader customDictHeader : materialCustomDictHeaders) {
            Long headerId = customDictHeader.getId();
            String customDictHeaderKey = getMaterialAdjustCustomDictHeaderKey(customDictHeader.getCodingClass(),
                    customDictHeader.getCodingMiddleclass(),
                    customDictHeader.getCodingSubclass());
            if (materialCustomDictHeaderMap.containsKey(customDictHeaderKey)) {
                List<Long> valueList = materialCustomDictHeaderMap.get(customDictHeaderKey);
                valueList.add(headerId);
                materialCustomDictHeaderMap.put(customDictHeaderKey, valueList);
            } else {
                materialCustomDictHeaderMap.put(customDictHeaderKey, Lists.newArrayList(headerId));
            }
            headerIdList.add(headerId);
        }
        logger.info("getInventoryTypeMap的materialCustomDictHeaderMap：{}", JsonUtils.toString(materialCustomDictHeaderMap));
        if (MapUtils.isNotEmpty(materialCustomDictHeaderMap)) {
            // “物料类别配置”，按"库存组织+物料大中小类"配置大中小类的"库存分类"（存在且未删除、启用日期<=系统当前日期<=失效日期、属性值不为空）
            String currentDateStr = DateUtil.format(new Date(), "yyyy-MM-dd");
            List<MaterialCustomDict> dictList = materialCustomDictExtMapper.queryByNameAndHeaderIdList("库存分类", headerIdList, currentDateStr);
            if (CollectionUtils.isNotEmpty(dictList)) {
                materialCustomDictHeaderMap.forEach((k, v) -> {
                    // material_custom_dict中多个headerId 对应一个value
                    String inventoryType =
                            dictList.stream().filter(e -> v.contains(e.getHeaderId())).map(MaterialCustomDict::getValue).findFirst().orElse(null);
                    inventoryTypeMap.put(k, inventoryType);
                });
            }
        }
        return inventoryTypeMap;
    }

    private String getMaterialAdjustCustomDictHeaderKey(String materialClassification, String codingMiddleClass, String materielType) {
        return String.format("getMaterialAdjustCustomDictHeaderKey_%s_%s_%s", materialClassification, codingMiddleClass, materielType);
    }

    private List<String> checkMaterial2(String materialMiddleClass, String brand, String model, Long organizationId) {
        return materialAdjustExtMapper.checkMaterial2(materialMiddleClass, brand, model, organizationId);
    }

    private List<String> checkMaterial3(String materialMiddleClass, String brand, String brandMaterialCode,
                                        Long organizationId) {
        return materialAdjustExtMapper.checkMaterial3(materialMiddleClass, brand, brandMaterialCode, organizationId);
    }

    private List<String> checkMaterial1(String materialMiddleClass, String brand, String figureNumber, Long organizationId) {
        return materialAdjustExtMapper.checkMaterial1(materialMiddleClass, brand, figureNumber, organizationId);
    }

    private List<MaterialDto> getAdjustCodeForKukaCodeRule(String figureNumber,
                                                           String brand,
                                                           String model,
                                                           List<Long> organizationIds,
                                                           Long adjustHeaderId) {
        return materialAdjustExtMapper.getAdjustCodeForKukaCodeRule(figureNumber, brand, model, organizationIds, adjustHeaderId);
    }

    @Override
    public Response importMaterialAdjustDetailFlexible(Long organizationId, List<MaterialAdjustDetailImportExcelVo> detailImportExcelVoList) {
        // 校验当前使用单位的物料编码规则
        String materialCodeRule = materialExtService.getMaterialCodeRule(SystemContext.getUnitId());
        if (!MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule) && !MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
            throw new ApplicationBizException("物料初始化导入数据失败，当前使用单位配置的物料编码规则未找到对应校验逻辑");
        }

        List<MaterialAdjustDetailDTO> materialAdjustDetailDTOList = BeanConverter.copy(detailImportExcelVoList, MaterialAdjustDetailDTO.class);
        List<Boolean> booleanList = new ArrayList<>();
        // 校验逻辑
        int threadNum = detailImportExcelVoList.size() / 500 + (detailImportExcelVoList.size() % 500 == 0 ? 0 : 1);
        List<MaterialAdjustDetailDTO> resultList = new ArrayList<>();
        final CountDownLatch countDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * 500;
            int endIndex = i == threadNum ? detailImportExcelVoList.size() : i * 500;
            List<MaterialAdjustDetailDTO> eachTreadExcelVos = materialAdjustDetailDTOList.subList(startIndex, endIndex);
            importMaterialAdjustDetailFlexibleExecutor.execute(() -> {
                TwoTuple<Boolean, List<MaterialAdjustDetailDTO>> twoTuple = new TwoTuple<>();
                try {
                    // 多线程异步执行主干导入方法
                    if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
                        // 导入数据校验，并返回修改校验结果
                        twoTuple = getValidDetailResultsNew(organizationId, eachTreadExcelVos);
                    } else {
                        // 导入数据校验，并返回修改校验结果
                        twoTuple = getValidDetailResultsForKuka2022(organizationId, eachTreadExcelVos, null,
                                materialCodeRule, true);
                    }
                    booleanList.add(twoTuple.getFirst());
                } catch (ApplicationBizException e) {
                    booleanList.add(false);
                    throw e;
                } catch (Exception e) {
                    logger.error("物料初始化导入校验失败");
                    booleanList.add(false);
                } finally {
                    countDownLatch.countDown();
                    if (Objects.equals(countDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after executeEachTreadExcelVo", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                    resultList.addAll(twoTuple.getSecond());
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("importMaterialAdjustDetailFlexible的executeEachTreadExcelVo的await失败", ex);
            Thread.currentThread().interrupt();
        }

        if (booleanList.contains(false)) {
            DataResponse<List<MaterialAdjustDetailDTO>> response = Response.dataResponse();
            response.setData(resultList.stream().filter(e -> StringUtils.isNotEmpty(e.getValidResult())).collect(Collectors.toList()));
            response.setMsg("FAIL");
            response.setCode(1);
            return response;
        }

        OrganizationRelDto organizationRelDto = CacheDataUtils.findOrganizationById(organizationId);
        OperatingUnit operatingUnit = CacheDataUtils.findOuById(SystemContext.getOus().get(0));
        List<MaterialAdjustHeaderDTO> headerDtos = new ArrayList<>();
        if (resultList.size() > 1000) {
            int runSize = (resultList.size() / 1000) + 1;
            for (int i = 0; i < runSize; i++) {
                List<MaterialAdjustDetailDTO> newResultList;
                MaterialAdjustHeaderDTO dto = new MaterialAdjustHeaderDTO();
                dto.setAdjustType(100);
                dto.setAdjustTypeName("新增物料");
                dto.setApplyByName("初始化导入");
                dto.setOrganizationName(organizationRelDto == null ? null : organizationRelDto.getOrganizationName());
                dto.setOrganizationId(organizationId);
                dto.setOuId(SystemContext.getOus().get(0));
                dto.setOuName(operatingUnit == null ? null : operatingUnit.getOperatingUnitName());
                dto.setResource(0);
                dto.setStatus(0);
                dto.setResourcesName("初始化导入");
                dto.setHandleBy(Long.parseLong(DateUtil.format(new Date(), DateUtil.DATE_YYMMDD_PATTERN)));
                if ((i + 1) == runSize) {
                    int startIdx = (i * 1000);
                    int endIdx = resultList.size();
                    newResultList = resultList.subList(startIdx, endIdx);
                } else {
                    int startIdx = (i * 1000);
                    int endIdx = (i + 1) * 1000;
                    newResultList = resultList.subList(startIdx, endIdx);
                }
                if (ListUtils.isNotEmpty(newResultList)) {
                    dto.setDetailDTOList(newResultList);
                    MaterialAdjustHeaderDTO materialAdjustHeaderDTO = saveWithDetail(dto);
                    headerDtos.add(materialAdjustHeaderDTO);
                }
            }
        } else {
            MaterialAdjustHeaderDTO dto = new MaterialAdjustHeaderDTO();
            dto.setAdjustType(100);
            dto.setAdjustTypeName("新增物料");
            dto.setApplyByName("初始化导入");
            dto.setOrganizationName(organizationRelDto == null ? null : organizationRelDto.getOrganizationName());
            dto.setOrganizationId(organizationId);
            dto.setOuId(SystemContext.getOus().get(0));
            dto.setOuName(operatingUnit == null ? null : operatingUnit.getOperatingUnitName());
            dto.setResource(0);
            dto.setStatus(0);
            dto.setResourcesName("初始化导入");
            dto.setDetailDTOList(resultList);
            dto.setHandleBy(Long.parseLong(DateUtil.format(new Date(), DateUtil.DATE_YYMMDD_PATTERN)));
            MaterialAdjustHeaderDTO materialAdjustHeaderDTO = saveWithDetail(dto);
            headerDtos.add(materialAdjustHeaderDTO);
        }

        // 获取物料类型及编码字典
        Map<String, String> materialTypeMap = basedataExtService.getLtcDict(MATERIAL_TYPE, null, null).stream()
                .collect(Collectors.toMap(DictDto::getName, DictDto::getCode, (key1, key2) -> key2));
        for (MaterialAdjustHeaderDTO headerDto : headerDtos) {
            try {
                saveMaterialApprove(headerDto, materialCodeRule, materialTypeMap);
            } catch (Exception e) {
                logger.error("importMaterialAdjustDetailFlexible的saveMaterialApprove失败", e);
                return Response.err(String.format("importMaterialAdjustDetailFlexible的saveMaterialApprove失败，原因：%s", e.getMessage()));
            }
        }

        DataResponse<Boolean> response = Response.dataResponse();
        response.setMsg("SUCCESS");
        response.setCode(0);
        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveMaterialApprove(MaterialAdjustHeaderDTO materialAdjustHeaderDTO, String materialCodeRule, Map<String, String> materialTypeMap) throws Exception {
        if (materialAdjustHeaderDTO == null) {
            return;
        }

        materialAdjustHeaderDTO.setStatus(MaterialAdjustEnum.APPROVED.code());
        // 更新行表数据的逻辑需要调整
        materialAdjustHeaderDTO.setSyncStatus(MaterialAdjustEnum.SYNC_STATUS_WAIT.code());
        materialAdjustHeaderMapper.updateByPrimaryKeySelective(materialAdjustHeaderDTO);

        // 新增或更新物料属性的逻辑
        if (Objects.nonNull(materialAdjustHeaderDTO.getOuId())) {
            String buyerNumber = materialExtService.getBuyerNumberByOu(materialAdjustHeaderDTO.getOuId());
            if (StringUtils.isNotEmpty(buyerNumber)) {
                materialAdjustHeaderDTO.setBuyerNumber(buyerNumber);
            }
        }
        materialAdjustHeaderDTO.setMaterialCodeRule(materialCodeRule);

        List<String> pamCodeList =
                materialAdjustHeaderDTO.getDetailDTOList().stream().map(MaterialAdjustDetailDTO::getPamCode).distinct().collect(Collectors.toList());
        List<Material> materialList = materialExtService.invokeMaterialApiGetByPamCodeList(pamCodeList, null);
        Map<String, List<Material>> materialMap = CollectionUtils.isEmpty(materialList) ? new HashMap<>()
                : materialList.stream().collect(Collectors.groupingBy(Material::getPamCode));

        for (MaterialAdjustDetailDTO detailDTO : materialAdjustHeaderDTO.getDetailDTOList()) {
            // 封装物料类型编码（对应material_type字典中的code值，对应物料基础表的item_type（物料用户类型））
            detailDTO.setMaterialTypeCode(materialTypeMap.get(detailDTO.getMaterialType()));

            boolean assemblyIs = Objects.equals("装配件", detailDTO.getMaterialType());
            if (assemblyIs) {
                detailDTO.setErpCode(null);
            } else {
                String erpCode;
                List<Material> materials = materialMap.get(detailDTO.getPamCode());
                if (CollectionUtils.isNotEmpty(materials)) {
                    Material originalMaterial = null;
                    Material otherMaterial = null;
                    for (Material material : materials) {
                        if (Objects.equals(material.getOrganizationId(), materialAdjustHeaderDTO.getOrganizationId())) {
                            originalMaterial = material;
                        } else {
                            otherMaterial = material;
                        }
                    }
                    if (null != originalMaterial) {
                        erpCode = StringUtils.isNotEmpty(originalMaterial.getItemCode()) ? originalMaterial.getItemCode()
                                : (null != otherMaterial && StringUtils.isNotEmpty(otherMaterial.getItemCode())) ? otherMaterial.getItemCode() :
                                detailDTO.getErpCode();
                    } else {
                        erpCode = (null != otherMaterial && StringUtils.isNotEmpty(otherMaterial.getItemCode())) ? otherMaterial.getItemCode() :
                                detailDTO.getErpCode();
                    }
                } else {
                    erpCode = detailDTO.getErpCode();
                }

                if (StringUtils.isEmpty(erpCode)) {
                    // 生成ERP编码
                    detailDTO.setErpCode(erpCodeRuleService.generateMaterialERPCode(detailDTO.getMaterialSmallClass(),
                            detailDTO.getMaterialMiddleClass(), detailDTO.getMaterialClass(), materialCodeRule));
                } else {
                    detailDTO.setErpCode(erpCode);
                }
            }

            if (StringUtils.isEmpty(detailDTO.getInventoryType())) {
                detailDTO.setInventoryType("0.0.0");
            }
            // 查询核价员（配套人员）并保存到物料基础表、物料估价表
            MaterialCustomDictHeaderDto materialCustomDictHeaderDto = new MaterialCustomDictHeaderDto();
            materialCustomDictHeaderDto.setOrganizationId(materialAdjustHeaderDTO.getOrganizationId());
            materialCustomDictHeaderDto.setCodingClass(detailDTO.getMaterialClass());
            materialCustomDictHeaderDto.setCodingMiddleclass(detailDTO.getMaterialMiddleClass());
            materialCustomDictHeaderDto.setCodingSubclass(detailDTO.getMaterialSmallClass());
            MaterialCustomDictHeaderDto materialCustomDictHeaderDtoNew = materialCustomDictService.queryValuer(materialCustomDictHeaderDto);
            detailDTO.setValuerId(materialCustomDictHeaderDtoNew.getValuerId());
            detailDTO.setValuerMip(materialCustomDictHeaderDtoNew.getValuerMip());
        }
        materialExtService.syncMaterialMaterialAdjustDetail(materialAdjustHeaderDTO);
        saveWithDetail(materialAdjustHeaderDTO);
    }

    @Override
    public List<Long> queryCurrentUnitOrganizationId() {
        Map<String, Object> param = new HashMap<>();
        param.put("unitId", SystemContext.getUnitId());
        final String url = com.midea.pam.common.util.StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/operatingUnit/queryCurrentUnitOu",
                param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<OperatingUnitDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
        });
        List<Long> organizationIdList = new ArrayList<>();
        List<OperatingUnitDto> operatingUnitDtos = response.getData();
        if (operatingUnitDtos.isEmpty()) {
            return organizationIdList;
        }
        for (OperatingUnitDto operatingUnitDto : operatingUnitDtos) {
            if (operatingUnitDto.getRels() != null && !operatingUnitDto.getRels().isEmpty()) {
                for (OrganizationRel organizationRel : operatingUnitDto.getRels()) {
                    if (organizationRel != null && organizationRel.getOrganizationId() != null) {
                        organizationIdList.add(organizationRel.getOrganizationId());
                    }
                }
            }
        }
        return organizationIdList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response importMaterialAdjustDetailFlexibleBackUp(Long organizationId, List<MaterialAdjustDetailImportExcelVo> detailImportExcelVoList) {
        // 校验当前使用单位的物料编码规则
        String materialCodeRule = materialExtService.getMaterialCodeRule(SystemContext.getUnitId());
        if (!MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule) && !MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
            throw new ApplicationBizException("物料初始化导入数据失败，当前使用单位配置的物料编码规则未找到对应校验逻辑");
        }

        List<MaterialAdjustDetailDTO> materialAdjustDetailDTOList = BeanConverter.copy(detailImportExcelVoList, MaterialAdjustDetailDTO.class);
        List<Boolean> booleanList = new ArrayList<>();
        // 校验逻辑
        int threadNum = detailImportExcelVoList.size() / 500 + (detailImportExcelVoList.size() % 500 == 0 ? 0 : 1);
        List<MaterialAdjustDetailDTO> resultList = new ArrayList<>();
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * 500;
            int endIndex = i == threadNum ? detailImportExcelVoList.size() : i * 500;
            List<MaterialAdjustDetailDTO> eachTreadExcelVos = materialAdjustDetailDTOList.subList(startIndex, endIndex);
            TwoTuple<Boolean, List<MaterialAdjustDetailDTO>> twoTuple = new TwoTuple<>();
            try {
                if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
                    // 导入数据校验，并返回修改校验结果
                    twoTuple = getValidDetailResultsNew(organizationId, eachTreadExcelVos);
                } else {
                    // 导入数据校验，并返回修改校验结果
                    twoTuple = getValidDetailResultsForKuka2022(organizationId, eachTreadExcelVos, null,
                            materialCodeRule, true);
                }
                booleanList.add(twoTuple.getFirst());
            } catch (Exception e) {
                logger.error("物料初始化导入校验失败");
                booleanList.add(false);
            } finally {
                resultList.addAll(twoTuple.getSecond());
            }
        }
        if (booleanList.contains(false)) {
            DataResponse<List<MaterialAdjustDetailDTO>> response = Response.dataResponse();
            response.setData(resultList.stream().filter(e -> StringUtils.isNotEmpty(e.getValidResult())).collect(Collectors.toList()));
            response.setMsg("FAIL");
            response.setCode(1);
            return response;
        }

        OrganizationRelDto organizationRelDto = CacheDataUtils.findOrganizationById(organizationId);
        OperatingUnit operatingUnit = CacheDataUtils.findOuById(SystemContext.getOus().get(0));
        List<MaterialAdjustHeaderDTO> headerDtos = new ArrayList<>();
        if (resultList.size() > 1000) {
            int runSize = (resultList.size() / 1000) + 1;
            for (int i = 0; i < runSize; i++) {
                List<MaterialAdjustDetailDTO> newResultList;
                MaterialAdjustHeaderDTO dto = new MaterialAdjustHeaderDTO();
                dto.setAdjustType(100);
                dto.setAdjustTypeName("新增物料");
                dto.setApplyByName("初始化导入");
                dto.setOrganizationName(organizationRelDto == null ? null : organizationRelDto.getOrganizationName());
                dto.setOrganizationId(organizationId);
                dto.setOuId(SystemContext.getOus().get(0));
                dto.setOuName(operatingUnit == null ? null : operatingUnit.getOperatingUnitName());
                dto.setResource(0);
                dto.setStatus(0);
                dto.setResourcesName("初始化导入");
                if ((i + 1) == runSize) {
                    int startIdx = (i * 1000);
                    int endIdx = resultList.size();
                    newResultList = resultList.subList(startIdx, endIdx);
                } else {
                    int startIdx = (i * 1000);
                    int endIdx = (i + 1) * 1000;
                    newResultList = resultList.subList(startIdx, endIdx);
                }
                if (ListUtils.isNotEmpty(newResultList)) {
                    dto.setDetailDTOList(newResultList);
                    MaterialAdjustHeaderDTO materialAdjustHeaderDTO = saveWithDetail(dto);
                    headerDtos.add(materialAdjustHeaderDTO);
                }
            }
        } else {
            MaterialAdjustHeaderDTO dto = new MaterialAdjustHeaderDTO();
            dto.setAdjustType(100);
            dto.setAdjustTypeName("新增物料");
            dto.setApplyByName("初始化导入");
            dto.setOrganizationName(organizationRelDto == null ? null : organizationRelDto.getOrganizationName());
            dto.setOrganizationId(organizationId);
            dto.setOuId(SystemContext.getOus().get(0));
            dto.setOuName(operatingUnit == null ? null : operatingUnit.getOperatingUnitName());
            dto.setResource(0);
            dto.setStatus(0);
            dto.setResourcesName("初始化导入");
            dto.setDetailDTOList(resultList);
            MaterialAdjustHeaderDTO materialAdjustHeaderDTO = saveWithDetail(dto);
            headerDtos.add(materialAdjustHeaderDTO);
        }

        // 获取物料类型及编码字典
        Map<String, String> materialTypeMap = basedataExtService.getLtcDict(MATERIAL_TYPE, null, null).stream()
                .collect(Collectors.toMap(DictDto::getName, DictDto::getCode, (key1, key2) -> key2));
        for (MaterialAdjustHeaderDTO headerDto : headerDtos) {
            try {
                saveMaterialApprove(headerDto, materialCodeRule, materialTypeMap);
            } catch (Exception e) {
                logger.error("importMaterialAdjustDetailFlexibleBackUp的saveMaterialApprove失败", e);
                return Response.err(String.format("importMaterialAdjustDetailFlexibleBackUp的saveMaterialApprove失败，原因：%s", e.getMessage()));
            }
        }

        DataResponse<Boolean> response = Response.dataResponse();
        response.setMsg("SUCCESS");
        response.setCode(0);
        return response;
    }

    private Integer getOracleLengthFromMysqlVarchar(String varchar) {
        if (StringUtils.isEmpty(varchar)) {
            return 0;
        }
        Integer length = varchar.length();

        StringBuilder sb = new StringBuilder();
        String pattern = "[\\u4e00-\\u9fa5]+";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(varchar);
        while (m.find()) {
            sb.append(m.group());
        }
        if (sb.length() == 0) {
            return length;
        }
        // oracle数据库的varchar2 每个汉字将占用3个byte
        return length - sb.length() + sb.length() * 3;
    }
}
