package com.midea.pam.ctc.service.impl;

import com.midea.pam.common.ctc.dto.WorkflowCallbackCommonDto;
import com.midea.pam.common.enums.InvoiceApplyStatusEnum;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.service.InvoiceApplyCallBackService;
import com.midea.pam.ctc.service.InvoiceApplyService;
import com.midea.pam.ctc.service.event.InvoiceApplyCallBackApprovalEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

public class InvoiceApplyCallBackServiceImpl implements InvoiceApplyCallBackService {

    private final static Logger logger = LoggerFactory.getLogger(InvoiceApplyCallBackServiceImpl.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private InvoiceApplyService invoiceApplyService;

    @Resource
    private WorkflowCallbackService workflowCallbackService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftSubmit(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("开票申请审批提交回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("InvoiceApplyCallback_draftSubmit_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    invoiceApplyService.changeStatus(formInstanceId, InvoiceApplyStatusEnum.HEADER_CHECKING.code(),
                            InvoiceApplyStatusEnum.HEADER_CHECKING.code().toString()); //提交审批改成InvoiceApplyStatusEnum.HEADER_CHECKING.code()

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("开票申请审批提交回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("开票申请审批提交回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }
            }
        } catch (Exception e) {
            logger.error("开票申请审批提交回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Override
    public void pass(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        applicationEventPublisher.publishEvent(new InvoiceApplyCallBackApprovalEvent(this, workflowCallbackCommonDto));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refuse(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("开票申请审批驳回回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("InvoiceApplyCallback_refuse_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    invoiceApplyService.changeStatus(formInstanceId, InvoiceApplyStatusEnum.HEADER_DRAFT.code(),
                            InvoiceApplyStatusEnum.HEADER_REFUSE.code().toString()); //审批驳回 改成InvoiceApplyStatusEnum.HEADER_REFUSE.code()

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("开票申请审批驳回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("开票申请审批驳回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }
            }
        } catch (Exception e) {
            logger.error("开票申请审批驳回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void abandon(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("开票申请审批作废回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("InvoiceApplyCallback_abandon_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    invoiceApplyService.changeStatus(formInstanceId, InvoiceApplyStatusEnum.HEADER_DELETE.code(),
                            InvoiceApplyStatusEnum.HEADER_ABANDON.code().toString()); //审批作废 改成InvoiceApplyStatusEnum.HEADER_DELETE.code()

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("开票申请审批作废回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("开票申请审批作废回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }
            }
        } catch (Exception e) {
            logger.error("开票申请审批作废回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftReturn(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("开票申请审批撤回回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("InvoiceApplyCallback_draftReturn_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    invoiceApplyService.changeStatus(formInstanceId, InvoiceApplyStatusEnum.HEADER_DRAFT.code(),
                            InvoiceApplyStatusEnum.HEADER_RETURN.code().toString()); //审批撤回 改成InvoiceApplyStatusEnum.HEADER_DRAFT.code()

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("开票申请审批撤回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("开票申请审批撤回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }
            }
        } catch (Exception e) {
            logger.error("开票申请审批撤回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("开票申请审批删除回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("InvoiceApplyCallback_delete_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    invoiceApplyService.changeStatus(formInstanceId, InvoiceApplyStatusEnum.HEADER_DELETE.code(),
                            InvoiceApplyStatusEnum.HEADER_DELETE.code().toString()); //审批删除 改成InvoiceApplyStatusEnum.HEADER_DELETE.code()

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("开票申请审批删除回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("开票申请审批删除回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }
            }
        } catch (Exception e) {
            logger.error("开票申请审批删除回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }
}
