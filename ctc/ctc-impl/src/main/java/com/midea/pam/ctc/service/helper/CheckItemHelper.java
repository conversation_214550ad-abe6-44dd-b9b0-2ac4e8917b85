package com.midea.pam.ctc.service.helper;

import com.alibaba.fastjson.JSONObject;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.entity.CheckItem;
import com.midea.pam.common.ctc.entity.CheckItemExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.vo.ProjectCheckExcelVo;
import com.midea.pam.common.ctc.vo.ProjectCheckReturnVo;
import com.midea.pam.common.enums.CheckItemEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.contract.service.impl.OssService;
import com.midea.pam.ctc.mapper.CheckItemMapper;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.config.CommonPropertiesConfig;
import com.midea.pam.system.SystemContext;
import org.apache.http.entity.ContentType;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/5/21
 * @description 检查点检查辅助工具类
 */
public class CheckItemHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectService projectService;

    @Resource
    private CheckItemMapper checkItemMapper;

    @Resource
    private OssService ossService;

    @Resource
    private CtcAttachmentService ctcAttachmentService;

    @Resource
    private CommonPropertiesConfig commonPropertiesConfig;

    /**
     * 结转检查
     *
     * @param projectDtos 待检查项目
     * @param lastPointFlagProjectIds 待检查最后一个节点项目
     * @return 检出提示信息
     */
    public JSONObject carryoverCheck(List<ProjectDto> projectDtos, List<Long> lastPointFlagProjectIds) {
        JSONObject result = new JSONObject();

        JSONObject checkResult = new JSONObject();

        Map<String, List<ProjectCheckExcelVo>> noPassResultMap = new HashMap<>();

        CheckItemExample checkItemExample = new CheckItemExample();
        CheckItemExample.Criteria criteria = checkItemExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andCarryoverCheckFlagEqualTo(Boolean.TRUE);
        List<CheckItem> checkItems = checkItemMapper.selectByExample(checkItemExample);
        if (ListUtils.isNotEmpty(checkItems) && ListUtils.isNotEmpty(lastPointFlagProjectIds)) {
            for (Long projectId : lastPointFlagProjectIds) {
                // 项目最后一个节点结转检查点检查(检查项配置了结转检查)
                checkOne(projectId, Boolean.TRUE, checkItems, checkResult, noPassResultMap);
            }
        }

        // 其他检查(默认所有项目都检查)
        if (ListUtils.isNotEmpty(projectDtos)) {
            for (ProjectDto projectDto : projectDtos) {
                otherCheckOne(projectDto, checkResult, noPassResultMap);
            }
        }

        // 生成检查不通过的文件信息
        if (!noPassResultMap.isEmpty()) {
            try {
                Long attachmentId = generateExcel(noPassResultMap);
                result.put("attachmentId", attachmentId);
            } catch (IOException e) {
                logger.error("生成excel文件失败", e);
                throw new BizException(Code.ERROR, "生成不通过检查文件失败");
            }
        }

        result.put("checkResult", checkResult);
        return result;
    }

    private int checkOne(Long projectId, Boolean flag, List<CheckItem> checkItems, JSONObject result, Map<String, List<ProjectCheckExcelVo>> noPassResultMap) {
        Project project = projectService.selectByPrimaryKey(projectId);
        //如果项目是最后一个收入节点，并且状态为"终止"，则去掉A12的检查点
        if (project.getStatus()==(ProjectStatus.TERMINATION.getCode()) && flag) {
            for (int i = 0; i < checkItems.size(); i++) {
                if (checkItems.get(i).getCheckCode().equals("A12"))
                    checkItems.remove(i);
            }
        }
        if (project == null) {
            return 0;
        }

        int i = 0;
        JSONObject checkResult = new JSONObject();

        StringBuilder warnMsg = new StringBuilder();
        for (CheckItem checkItem : checkItems) {
            String checkCode = checkItem.getCheckCode();
            String checkName = checkItem.getCheckName();

            Map<String, Object> workflowInfo = new HashMap<>();
            ProjectCheckExcelVo projectCheckExcelVo = null;

            //项目是否存在审批中的流程
            if (CheckItemEnums.A01.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectItem(project, workflowInfo);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目存在审批中的流程;");
                //项目是否存在审批中、变更中的工时
            } else if (CheckItemEnums.A02.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectChangeWorkingHourItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目存在审批中、变更中的工时;");
                //销售合同实际开票金额与合同金额（含税）是否一致
            } else if (CheckItemEnums.A03.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkContractInvoiceItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("销售合同实际开票金额与合同金额（含税）不一致;");
                //销售合同实际回款金额与实际开票金额是否一致
            } else if (CheckItemEnums.A04.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkContractMoneyBackItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("销售合同实际回款金额与实际开票金额不一致;");
                //项目工时填报天数是否与合同天数一致
            } else if (CheckItemEnums.A05.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectWorkingHourItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目工时填报天数与合同天数不一致;");
                //项目辅里程碑节点是否已全部交付
            } else if (CheckItemEnums.A06.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectMilepostItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }

                i++;
                warnMsg.append("项目辅里程碑节点未全部交付;");
                //项目是否存在在途的费用申请/报销单
            } else if (CheckItemEnums.A07.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectFeeItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }

                i++;
                warnMsg.append("项目存在在途的费用申请/报销单;");
                //项目详细设计是否存在审批中的流程
            } else if (CheckItemEnums.A08.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectDesignItem(project, workflowInfo);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目详细设计存在审批中的流程;");
                //项目外包采购是否存在审批中的流程
            } else if (CheckItemEnums.A09.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectPurchaseItem(project, workflowInfo);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目外包采购存在审批中的流程;");
                //项目库存是否存在审批中的流程
            } else if (CheckItemEnums.A10.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectStockApprovalItem(project, workflowInfo);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目库存存在审批中的流程;");
                //项目是否已完成详细设计方案的进度确认
            } else if (CheckItemEnums.A11.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectPlanItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目未完成详细设计方案的进度确认;");
                //项目状态终止类型的跳过该检查点
                //项目详细设计方案的采购物料是否已全部生成采购需求
            } else if (CheckItemEnums.A12.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectMaterielItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目详细设计方案的采购物料未全部生成采购需求;");
                //项目采购需求是否已全部生成采购订单
            } else if (CheckItemEnums.A13.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectRequirementItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目采购需求未全部生成采购订单;");
                //项目采购订单物料是否已全部接收入库
            } else if (CheckItemEnums.A14.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectPurchaseOrderItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目采购订单物料未全部接收入库;");
                //项目子库的库存是否为0
            } else if (CheckItemEnums.A15.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectStockItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目子库的库存不为0;");
                // 项目库存是否存在未处理完的单据
            } else if (CheckItemEnums.A16.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectStorageItem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目库存存在未处理完的单据;");
                // 项目在采购发票登记处存在非“引入”、“取消”状态的发票；如果存在则返回存在的数据明细；（导出格式同税票登记列表）
            } else if (CheckItemEnums.A26.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectContractInvoiceStatus(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目下的采购合同存在未入账的发票或罚扣（严重）");
                //项目是否存在给“关闭”、“取消”的问题，如果有如果存在则返回存在的数据明细；（导出格式同项目问题列表）
            } else if (CheckItemEnums.A25.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectProblem(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目存在未关闭的问题（警告）");
            } else if (CheckItemEnums.A30.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkProjectCustomer(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目关联多个客户，但客户属性不一致，属于异常数据，不参与结转;");
            } else if (CheckItemEnums.A31.getCode().equals(checkCode)) {
                projectCheckExcelVo = projectService.checkInnerCustomer(project);
                if (projectCheckExcelVo.getReturnList() == null || projectCheckExcelVo.getReturnList().size() == 0) {
                    continue;
                }
                i++;
                warnMsg.append("项目存在多个内部客户，属于异常数据，不参与结转;");
            }


            if (projectCheckExcelVo != null) {
                // 收集检查不通过信息
                projectCheckExcelVo.setCheckName(checkName);
            }

            collectNoPassResult(noPassResultMap, checkCode, projectCheckExcelVo);

            if (warnMsg.length() > 0) {
                checkResult.put("detail", warnMsg.toString());
                result.put(projectId.toString(), checkResult);
            }
        }

        return i;
    }

    private int otherCheckOne(ProjectDto projectDto, JSONObject result, Map<String, List<ProjectCheckExcelVo>> noPassResultMap) {
        int checkItemCount = 0;
        String checkDetail = null;
        StringBuilder warnMsg = new StringBuilder();
        JSONObject checkResult = result.getJSONObject(projectDto.getId().toString());
        if (checkResult != null) {
            checkDetail = checkResult.getObject("detail", String.class);
            if (StringUtils.isNotEmpty(checkDetail) && checkDetail.contains(";")) {
                String[] split = checkDetail.split(";");
                checkItemCount = split.length;
                // 防止重复,先清除后面重新赋值
                result.remove(projectDto.getId().toString());
            }
        } else {
            checkResult = new JSONObject();
        }

        // 项目预算总额为0，不允许结转
        String checkCode = Constants.OtherCheck01.CODE;
        String checkName = Constants.OtherCheck01.NAME;

        ProjectCheckExcelVo projectCheckExcelVo = null;
        projectCheckExcelVo = projectService.checkProjectBudgetItem(projectDto);
        if (projectCheckExcelVo.getReturnList() != null && projectCheckExcelVo.getReturnList().size() != 0) {
            checkItemCount++;
            warnMsg.append("项目预算总额为0，不允许结转;");
            // 收集检查不通过信息
            projectCheckExcelVo.setCheckName(checkName);
            collectNoPassResult(noPassResultMap, checkCode, projectCheckExcelVo);
        }

        // 项目无客户，无法获取往来段
        checkCode = Constants.OtherCheck02.CODE;
        checkName = Constants.OtherCheck02.NAME;

        boolean hasCustomer = true;
        projectCheckExcelVo = null;
        projectCheckExcelVo = projectService.checkProjectCustomerItem(projectDto);
        if (projectCheckExcelVo.getReturnList() != null && projectCheckExcelVo.getReturnList().size() != 0) {
            checkItemCount++;
            warnMsg.append("项目无客户，无法获取往来段;");
            // 收集检查不通过信息
            projectCheckExcelVo.setCheckName(checkName);
            collectNoPassResult(noPassResultMap, checkCode, projectCheckExcelVo);
            hasCustomer = false;
        }

        // 外币项目无结转汇率，无法结转
        checkCode = Constants.OtherCheck03.CODE;
        checkName = Constants.OtherCheck03.NAME;

        projectCheckExcelVo = null;
        projectCheckExcelVo = projectService.checkProjectCarryoverBillConversionRateItem(projectDto);
        if (projectCheckExcelVo.getReturnList() != null && projectCheckExcelVo.getReturnList().size() != 0) {
            checkItemCount++;
            warnMsg.append("外币项目无结转汇率，无法结转;");
            // 收集检查不通过信息
            projectCheckExcelVo.setCheckName(checkName);
            collectNoPassResult(noPassResultMap, checkCode, projectCheckExcelVo);
        }

        if (hasCustomer){
            // 客户企业性质为空，不允许结转
            checkCode = Constants.OtherCheck04.CODE;
            checkName = Constants.OtherCheck04.NAME;

            projectCheckExcelVo = null;
            projectCheckExcelVo = projectService.checkCustomerEnterpriseNature(projectDto);
            if (projectCheckExcelVo.getReturnList() != null && projectCheckExcelVo.getReturnList().size() != 0) {
                checkItemCount++;
                warnMsg.append("客户企业性质为空，不允许结转;");
                // 收集检查不通过信息
                projectCheckExcelVo.setCheckName(checkName);
                collectNoPassResult(noPassResultMap, checkCode, projectCheckExcelVo);
            }
        }

        if (checkItemCount > 0) {
            checkResult.put("header", "项目" + projectDto.getCode() + "共有" + checkItemCount + "个未检查通过的检查点");
            checkResult.put("detail", checkDetail);
            checkResult.put("otherDetail", warnMsg.toString());
            result.put(projectDto.getId().toString(), checkResult);
        }
        return checkItemCount;
    }

    private void collectNoPassResult(Map<String, List<ProjectCheckExcelVo>> noPassResultMap, String code, ProjectCheckExcelVo projectCheckExcelVo) {
        if (noPassResultMap == null) {
            return;
        }
        List<ProjectCheckExcelVo> projectCheckExcelVos = null;
        if (noPassResultMap.containsKey(code)) {
            projectCheckExcelVos = noPassResultMap.get(code);
        } else {
            projectCheckExcelVos = new ArrayList<>();
            noPassResultMap.put(code, projectCheckExcelVos);
        }

        projectCheckExcelVos.add(projectCheckExcelVo);
    }

    /**
     * 生成检查文件
     *
     * @param noPassResultMap
     * @return 附件ID
     * @throws IOException
     */
    private Long generateExcel(Map<String, List<ProjectCheckExcelVo>> noPassResultMap) throws IOException {
        HSSFWorkbook workbook = buildExcel(noPassResultMap);

        Date currentDate = new Date();
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
        Long userId = SystemContext.getUserId();

        String filePath = commonPropertiesConfig.getCarryoverBillCheckFilePath() + "/成本结转检查" + df.format(currentDate) + ".xls";

        FileInputStream fileInputStream = null;
        OutputStream out = null;
        try {
            out = new FileOutputStream(filePath);
            workbook.write(out);
        } catch (Exception e) {
            logger.info("创建文件失败", e);
            throw new BizException(Code.ERROR, "生成文件失败");
        } finally {
            if (out != null) {
                out.close();
            }
        }

        File pdfFile = new File(filePath);
        fileInputStream = new FileInputStream(pdfFile);
        MultipartFile multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
        Map<String, Object> map = ossService.upload(multipartFile);
        CtcAttachmentDto dto = new CtcAttachmentDto();
        dto.setModule(CtcAttachmentModule.PROJECT_CHECK_ITEM.code());
        dto.setDeletedFlag(false);
        dto.setAttachId((Long) map.get("fileId"));
        dto.setFileName((String) map.get("fileName"));
        dto.setUpdateAt(new Date());
        dto.setUpdateBy(userId);
        dto.setCreateAt(new Date());
        dto.setCreateBy(userId);
        dto.setStatus(1);
        ctcAttachmentService.add(dto);

        return dto.getId();
    }

    private HSSFWorkbook buildExcel(Map<String, List<ProjectCheckExcelVo>> noPassResultMap) {
        HSSFWorkbook workbook = new HSSFWorkbook();

        Set<Map.Entry<String, List<ProjectCheckExcelVo>>> entries = noPassResultMap.entrySet();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        int sheetNum = 0;
        for (Map.Entry<String, List<ProjectCheckExcelVo>> entry : entries) {
            List<ProjectCheckExcelVo> noPassResultList = entry.getValue();

            // 生成一个表格
            HSSFSheet sheet = workbook.createSheet();
            workbook.setSheetName(sheetNum, "Sheet" + String.valueOf(sheetNum+1));
            // 设置表格默认列宽度为20个字节
            sheet.setDefaultColumnWidth((short) 20);
            // 生成一个样式
            HSSFCellStyle style = workbook.createCellStyle();
            // 设置这些样式

            // 指定当单元格内容显示不下时自动换行
            style.setWrapText(true);

            ProjectCheckExcelVo checkResult = noPassResultList.get(0);

            // 产生表格标题行
            for (int i = 0; i < 2; i++) {
                HSSFRow row = sheet.createRow(i);
                if (i == 0) {
                    HSSFCell cell0 = row.createCell((short) 0);
                    cell0.setCellStyle(style);
                    HSSFRichTextString text0 = new HSSFRichTextString("输出时间");
                    cell0.setCellValue(text0.toString());
                    HSSFCell cell1 = row.createCell((short) 1);
                    cell1.setCellStyle(style);
                    HSSFRichTextString text1 = new HSSFRichTextString(df.format(checkResult.getDate()));
                    cell1.setCellValue(text1.toString());
                } else if (i == 1) {
                    HSSFCell cell0 = row.createCell((short) 0);
                    cell0.setCellStyle(style);
                    HSSFRichTextString text0 = new HSSFRichTextString("检查点");
                    cell0.setCellValue(text0.toString());
                    HSSFCell cell1 = row.createCell((short) 1);
                    cell1.setCellStyle(style);
                    HSSFRichTextString text1 = new HSSFRichTextString(checkResult.getCheckName());
                    cell1.setCellValue(text1.toString());
                }
            }

            String[] headers = checkResult.getHeaders();
            HSSFRow row5 = sheet.createRow(3);
            for (int i = 0; i < headers.length; i++) {
                HSSFCell cell = row5.createCell((short) i);
                cell.setCellStyle(style);
                HSSFRichTextString text = new HSSFRichTextString(headers[i]);
                cell.setCellValue(text.toString());
            }

            HSSFCell cell = row5.createCell(headers.length);
            cell.setCellStyle(style);
            cell.setCellValue("项目编号");

            HSSFCell cell2 = row5.createCell(headers.length + 1);
            cell2.setCellStyle(style);
            cell2.setCellValue("项目名称");

            int rowIndex = 4;
            int rowNo = 1; // 序号
            for (ProjectCheckExcelVo projectCheckExcelVo : noPassResultList) {

                List<ProjectCheckReturnVo> datas = projectCheckExcelVo.getReturnList();
                String projectCode = projectCheckExcelVo.getProjectCode();
                String projectName = projectCheckExcelVo.getProjectName();

                // 遍历集合数据，产生数据行
                for (ProjectCheckReturnVo o : datas) {
                    HSSFRow row = sheet.createRow(rowIndex);
                    // 重新给序号值，避免序号错乱
                    HSSFCell numCell = row.createCell(0);
                    numCell.setCellValue(rowNo++);
                    for (int i = 1; i < headers.length; i++) {
                        try {
                            Method method = o.getClass().getMethod("getC" + i);//得到方法对象
                            Object value = method.invoke(o);//得到返回值
                            HSSFCell thisCell = row.createCell(i);
                            thisCell.setCellValue(String.valueOf(ObjectUtils.isEmpty(value) ? "" : value));
                        } catch (Exception e) {
                            logger.error("创建excel失败", e);
                            throw new BizException(Code.ERROR, "创建excel失败");
                        }

                    }
                    rowIndex++;

                    HSSFCell codeCell = row.createCell(headers.length);
                    codeCell.setCellStyle(style);
                    codeCell.setCellValue(projectCode);

                    HSSFCell nameCell = row.createCell(headers.length + 1);
                    nameCell.setCellStyle(style);
                    nameCell.setCellValue(projectName);
                }
            }

            sheetNum++;
        }

        return workbook;
    }

    public JSONObject asyncCarryoverCheck(List<Long> projectIds) {
        JSONObject result = new JSONObject();

        CheckItemExample checkItemExample = new CheckItemExample();
        CheckItemExample.Criteria criteria = checkItemExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andCarryoverCheckFlagEqualTo(Boolean.TRUE);
        List<CheckItem> checkItems = checkItemMapper.selectByExample(checkItemExample);
        if (ListUtils.isNotEmpty(checkItems) && ListUtils.isNotEmpty(projectIds)) {
            final CountDownLatch latch = new CountDownLatch(projectIds.size());
            projectIds.forEach(projectId -> {
                syncCheckOne(latch, projectId, checkItems, result);
            });
            try {
                latch.await(20000, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                logger.info("校验失败", e);
                Thread.currentThread().interrupt();
//                throw new BizException(Code.ERROR, "失败");
            }
        }

        return result;
    }

    @Async
    public void syncCheckOne(CountDownLatch latch, Long projectId, List<CheckItem> checkItems, JSONObject result) {
//        checkOne(projectId, checkItems, result, null, null);
//        latch.countDown();
    }
}
