package com.midea.pam.ctc.share.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.CoaSubject;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.UnitOuRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.*;
import com.midea.pam.common.ctc.entity.*;
import com.midea.pam.common.enums.*;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CostCollectionEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.AgencySynInfoMapper;
import com.midea.pam.ctc.mapper.CarryoverIncomeAccountingExtMapper;
import com.midea.pam.ctc.mapper.CostCollectionMapper;
import com.midea.pam.ctc.mapper.DifferenceShareAccountMapper;
import com.midea.pam.ctc.service.*;
import com.midea.pam.ctc.share.service.*;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-11-22
 * @description 差异分摊入账单
 */
public class DifferenceShareAccountServiceImpl implements DifferenceShareAccountService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private DifferenceShareAccountMapper differenceShareAccountMapper;

    @Resource
    private DifferenceShareAccountDetailService detailService;

    @Resource
    private DifferenceShareAccountSummaryService summaryService;

    @Resource
    private SubjectBalanceService subjectBalanceService;

    @Resource
    private CarryoverIncomeAccountingExtMapper carryoverIncomeAccountingExtMapper;

    @Resource
    private BusiSceneNonSaleService busiSceneNonSaleService;

    @Resource
    private OrganizationCustomDictService organizationCustomDictService;

    @Resource
    private BasedataExtService basedataExtService;

    @Resource
    private InvoicePriceDifferenceRecordService invoicePriceDifferenceRecordService;

    @Resource
    private MaterialUpdateDifferenceRecordService materialUpdateDifferenceRecordService;

    @Resource
    private PurchasePriceDifferenceRecordService purchasePriceDifferenceRecordService;

    @Resource
    private SubjectBalanceRecordService subjectBalanceRecordService;

    @Resource
    private DifferenceShareDataSyncService differenceShareDataSyncService;

    @Resource
    private DifferenceShareResultDetailService differenceShareResultDetailService;

    @Resource
    private MaterialActualCostDetailService materialActualCostDetailService;

    @Resource
    private CostCollectionService costCollectionService;

    @Resource
    private CostCollectionMapper costCollectionMapper;

    @Resource
    private ProjectService projectService;

    @Resource
    private AgencySynInfoMapper agencySynInfoMapper;

    public final String beginningFixedGlPeriod = "2019-10";

    @Override
    public long countByExample(DifferenceShareAccountExample example) {
        return differenceShareAccountMapper.countByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return differenceShareAccountMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(DifferenceShareAccount record) {
        return differenceShareAccountMapper.insert(record);
    }

    @Override
    public int insertSelective(DifferenceShareAccount record) {
        return differenceShareAccountMapper.insertSelective(record);
    }

    @Override
    public List<DifferenceShareAccount> selectByExample(DifferenceShareAccountExample example) {
        return differenceShareAccountMapper.selectByExample(example);
    }

    @Override
    public DifferenceShareAccount selectByPrimaryKey(Long id) {
        return differenceShareAccountMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(DifferenceShareAccount record) {
        return differenceShareAccountMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(DifferenceShareAccount record) {
        return differenceShareAccountMapper.updateByPrimaryKey(record);
    }

    @Override
    public PageInfo<DifferenceShareAccountDTO> page(DifferenceShareAccountDTO differenceShareAccountDTO, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        DifferenceShareAccountExample example = buildExample(differenceShareAccountDTO);
        example.setOrderByClause(" create_at desc");
        final List<DifferenceShareAccount> differenceShareAccounts = this.selectByExample(example);
        final PageInfo<DifferenceShareAccountDTO> pageInfo = BeanConverter.convertPage(differenceShareAccounts, DifferenceShareAccountDTO.class);
        final List<DifferenceShareAccountDTO> list = pageInfo.getList();
        Optional.ofNullable(list).ifPresent(accountDTOList -> {
            accountDTOList.forEach(accountDTO -> {
                if (accountDTO.getCreateBy() != null) {
                    final UserInfo userInfo = CacheDataUtils.findUserById(accountDTO.getCreateBy());
                    accountDTO.setCreateUsername(userInfo != null ? userInfo.getName() : null);
                }
            });
        });

        return pageInfo;
    }

    /**
     * 构建查询对象
     *
     * @param differenceShareAccountDTO 查询条件
     * @return 查询对象
     */
    private DifferenceShareAccountExample buildExample(DifferenceShareAccountDTO differenceShareAccountDTO) {
        DifferenceShareAccountExample example = new DifferenceShareAccountExample();
        final DifferenceShareAccountExample.Criteria criteria = example.createCriteria();
        // 模糊
        if (StringUtils.isNotEmpty(differenceShareAccountDTO.getAccountNum())) {
            criteria.andAccountNumLike(StringUtils.buildSqlLikeCondition(differenceShareAccountDTO.getAccountNum()));
        }

        if (StringUtils.isNotEmpty(differenceShareAccountDTO.getStatusStr())) {
            final List<Integer> statusList = Arrays.stream(differenceShareAccountDTO.getStatusStr().split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            criteria.andStatusIn(statusList);
        }

        if (StringUtils.isNotEmpty(differenceShareAccountDTO.getTypeStr())) {
            final List<Integer> typeList = Arrays.stream(differenceShareAccountDTO.getTypeStr().split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            criteria.andTypeIn(typeList);
        }

        if (StringUtils.isNotEmpty(differenceShareAccountDTO.getOuIdsStr())) {
            final List<Long> ouIdList = Arrays.stream(differenceShareAccountDTO.getOuIdsStr().split(",")).map(s -> Long.valueOf(s.trim())).collect(Collectors.toList());
            criteria.andOuIdIn(ouIdList);
        }

        if (StringUtils.isNotEmpty(differenceShareAccountDTO.getGlPeriod())) {
            criteria.andGlPeriodEqualTo(differenceShareAccountDTO.getGlPeriod());
        }

        if (StringUtils.isNotEmpty(differenceShareAccountDTO.getCurrency())) {
            criteria.andCurrencyEqualTo(differenceShareAccountDTO.getCurrency());
        }

        if (StringUtils.isNotEmpty(differenceShareAccountDTO.getWoPeriod())){
            criteria.andWoPeriodEqualTo(differenceShareAccountDTO.getWoPeriod());
        }

        if (StringUtils.isNotEmpty(differenceShareAccountDTO.getSyncStatusStr())) {
            final List<Integer> syncStatusList = Arrays.stream(differenceShareAccountDTO.getSyncStatusStr().
                    split(",")).
                    map(s -> Integer.parseInt(s.trim())).
                    collect(Collectors.toList());
            criteria.andSyncStatusIn(syncStatusList);
        }

        if (differenceShareAccountDTO.getOuId() != null) {
            criteria.andOuIdEqualTo(differenceShareAccountDTO.getOuId());
        }

        if (ListUtils.isNotEmpty(differenceShareAccountDTO.getDifferenceShareAccountIds())){
            criteria.andIdIn(differenceShareAccountDTO.getDifferenceShareAccountIds());
        }
        if(StringUtils.isNotEmpty(differenceShareAccountDTO.getOuName())){
            final List<String> ouNameList = Arrays.stream(differenceShareAccountDTO.getOuName()
                    .split(",")).
                    map(s -> s.trim()).
                    collect(Collectors.toList());
            criteria.andOuNameIn(ouNameList);
        }
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        return example;
    }


    @Override
    public DifferenceShareAccountDTO getDetailById(Long id) {
        final DifferenceShareAccount differenceShareAccount = this.selectByPrimaryKey(id);
        if (differenceShareAccount == null) {
            return null;
        }

        final DifferenceShareAccountDTO detail = BeanConverter.copy(differenceShareAccount, DifferenceShareAccountDTO.class);
        if (detail != null && detail.getCreateAt() != null) {
            final UserInfo userInfo = CacheDataUtils.findUserById(differenceShareAccount.getCreateBy());
            detail.setCreateUsername(userInfo != null ? userInfo.getName() : null);
        }
        packageDetails(detail);
        packageSummaries(detail);

        return detail;
    }

    private void packageDetails(DifferenceShareAccountDTO record) {
        if (record != null && record.getId() != null) {
            final List<DifferenceShareAccountDetail> details = detailService.listByAccountId(record.getId());
            record.setDifferenceShareAccountDetails(details);
        }
    }

    private void packageSummaries(DifferenceShareAccountDTO record) {
        if (record != null && record.getId() != null) {
            final List<DifferenceShareAccountSummary> summaries = summaryService.listGroupByDifferenceCategoryAndSubjectCode(record.getId());
            record.setDifferenceShareAccountSummaries(summaries);
        }
    }

    @Transactional
    @Override
    public DifferenceShareAccount beginningCalculate(String glPeriod, Long ouId) {
        // 判断是否已做过差异分摊
        beginningCheck(glPeriod, ouId);

        // 查询设置的期初分摊对应收入
        final Set<String> organizationCustomDictSet = organizationCustomDictService.queryByName("期初差异分摊对应收入", ouId, OrgCustomDictOrgFrom.OU);
        if (CollectionUtils.isEmpty(organizationCustomDictSet)) {
            throw new BizException(Code.ERROR, "请先配置期初差异分摊对应收入");
        }

        final Iterator<String> iterator = organizationCustomDictSet.iterator();
        BigDecimal fixedIncome = new BigDecimal(iterator.next()); // 固定收入

        // 查询设置的可分摊科目
        final List<DictDto> dictDtos = basedataExtService.getLtcDict("Range of different", null, null);
        List<String> subjectList = new ArrayList<>(); // 科目列表
        if (!CollectionUtils.isEmpty(dictDtos)) {
            dictDtos.forEach(dictDto -> {
                subjectList.add(dictDto.getCode());
            });
        }

        // 所有类型数据字典
        Map<String, String> subjectMap = new HashMap<>(); // 可分摊类型
        final List<DictDto> allDictDtos = basedataExtService.getLtcDict("CostElements_Type", null, null);
        if (!CollectionUtils.isEmpty(allDictDtos)) {
            allDictDtos.forEach(dictDto -> subjectMap.put(dictDto.getCode(), dictDto.getName()));
        }

        // 查询期初可做差异分摊余额
        List<SubjectBalance> subjectBalances = getCurrentNeedShareSubjectBalances(glPeriod, ouId, subjectList);

        if (!CollectionUtils.isEmpty(subjectBalances)) {

            // 查询会计期间收入
            final List<CarryoverIncomeAccounting> carryoverIncomeAccountings =
                    carryoverIncomeAccountingExtMapper.listIncomeAccountingByGlPeriod(ouId, glPeriod);
            if (CollectionUtils.isEmpty(carryoverIncomeAccountings)) {
                logger.info("会计期间[{}]，未查询到当前结转收入数据，期初分摊计算结束", glPeriod);
                throw new BizException(Code.ERROR, "会计期间[" + glPeriod + "]，未查询到当前结转收入数据，期初分摊计算结束");
            }

            // 当期总收入金额
            BigDecimal totalIncomeSubject = BigDecimal.ZERO;
            for (CarryoverIncomeAccounting carryoverIncomeAccounting : carryoverIncomeAccountings) {// 收入科目
                totalIncomeSubject = totalIncomeSubject.add(carryoverIncomeAccounting.getIncomeAmount());
            }

            // 收入总额小于0，不分摊
            if (!BigDecimalUtils.isGreater(totalIncomeSubject, BigDecimal.ZERO)) {
                logger.info("收入：{}, 收入总额小于0，不分摊", totalIncomeSubject);
                throw new BizException(Code.ERROR, "会计期间[" + glPeriod + "]，收入总额[" + totalIncomeSubject + "]小于0，不分摊");
            }

            // 当期收入/固定收入*固定差异（分摊汇总指定区间值对应的待分摊差异小计）
            // 当期收入/固定收入 => 分摊比例
            BigDecimal shareRate = totalIncomeSubject.divide(fixedIncome, 4, RoundingMode.HALF_UP);

            // 余额差异分摊
            shareBalances(subjectBalances, shareRate);

            // 新增保存本期余额表
            addSubjectBalances(subjectBalances);

            // 此次分摊总额
            BigDecimal totalAmount = BigDecimal.ZERO;

            // 计算此次分摊总额
            for (SubjectBalance subjectBalance : subjectBalances) {
                BigDecimal currentOccurrenceAmount = subjectBalance.getCurrentOccurrenceAmount() == null ?
                        BigDecimal.ZERO : subjectBalance.getCurrentOccurrenceAmount();
                totalAmount = totalAmount.add(currentOccurrenceAmount);
            }

            // 新增入账单头表
            DifferenceShareAccount differenceShareAccount = addDifferenceShareAccount(ouId, glPeriod, totalAmount, DifferenceShareAccountType.BEGINNING.getCode());

            // 新增入账单明细
            addDetails(subjectBalances, ouId, differenceShareAccount.getId(), subjectMap, carryoverIncomeAccountings, totalIncomeSubject);

            // 新增入账单分摊汇总明细
            addSummaries(subjectBalances, shareRate, differenceShareAccount.getId(), subjectMap, 2);

            return differenceShareAccount;
        }

        return null;
    }

    /**
     * 校验是否会计期间是否已做过期初分摊
     *
     * @param glPeriod 会计期间
     * @param ouId     ou
     */
    private void beginningCheck(String glPeriod, Long ouId) {
        DifferenceShareAccountExample example = new DifferenceShareAccountExample();
        DifferenceShareAccountExample.Criteria criteria = example.createCriteria();
        criteria.andGlPeriodEqualTo(glPeriod);
        criteria.andOuIdEqualTo(ouId);
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andTypeEqualTo(DifferenceShareAccountType.BEGINNING.getCode());
        criteria.andStatusEqualTo(DifferenceShareAccountStatus.VALID.getCode());
        List<DifferenceShareAccount> differenceShareAccounts = this.selectByExample(example);
        if (!CollectionUtils.isEmpty(differenceShareAccounts)) {
            throw new BizException(Code.ERROR, "期初类型的本期差异分摊已存在，不能重复创建，冲销后才能重新做单");
        }
    }

    /**
     * 查询上一个月
     *
     * @param glPeriod 当月
     * @return 上个月
     */
    private String getLastPeriod(String glPeriod) {
        String lastGlPeriod;

        final String[] yearAndMonth = glPeriod.split("-");
        int year = Integer.parseInt(yearAndMonth[0]);
        int month = Integer.parseInt(yearAndMonth[1]);

        if (month == 1) {
            year = year - 1;
            month = 12;
        } else {
            month = month - 1;
        }

        if (month < 10) {
            lastGlPeriod = year + "-0" + month;
        } else {
            lastGlPeriod = year + "-" + month;
        }
        return lastGlPeriod;
    }

    /**
     * 获取期间对应可分摊余额数据
     *
     * @param glPeriod          期间
     * @param filterSubjectList 可分摊科目
     * @return 余额列表
     */
    @Override
    public List<SubjectBalance> getCurrentNeedShareSubjectBalances(String glPeriod, Long ouId, List<String> filterSubjectList) {
        // 查询期初可做差异分摊余额数据
        List<SubjectBalance> subjectBalances = subjectBalanceService.listCanDoBeginningCalculate(glPeriod, ouId, filterSubjectList);
        if (CollectionUtils.isEmpty(subjectBalances)) {
            String lastGlPeriod = getLastPeriod(glPeriod);

            // 查询上一期的结存
            subjectBalances = subjectBalanceService.listCanDoBeginningCalculate(lastGlPeriod, ouId, filterSubjectList);

            if (!CollectionUtils.isEmpty(subjectBalances)) {
                // 清空本期发生数据
                subjectBalances.forEach(subjectBalance -> {
                    BigDecimal endingBalance = subjectBalance.getEndingBalance();
                    subjectBalance.setBeginT(endingBalance);
                    subjectBalance.setEndT(endingBalance);
                    subjectBalance.setPeriodNet(BigDecimal.ZERO);
                    subjectBalance.setCurrentOccurrenceAmount(BigDecimal.ZERO);
                    subjectBalance.setGlPeriod(glPeriod);
                });
            }
        }

        return subjectBalances;
    }

    public List<SubjectBalance> getCurrentNeedShareSubjectBalances(String glPeriod, Long ouId, List<String> filterSubjectList, int differenceShareType) {
        // 查询期初可做差异分摊余额数据
        List<SubjectBalance> subjectBalances = subjectBalanceService.listCanDoCalculate(glPeriod, ouId, filterSubjectList, differenceShareType);
        if (CollectionUtils.isEmpty(subjectBalances)) {
            String lastGlPeriod = getLastPeriod(glPeriod);

            // 查询上一期的结存
            subjectBalances = subjectBalanceService.listCanDoCalculate(lastGlPeriod, ouId, filterSubjectList, differenceShareType);

            if (!CollectionUtils.isEmpty(subjectBalances)) {
                // 清空本期发生数据
                subjectBalances.forEach(subjectBalance -> {
                    BigDecimal endingBalance = subjectBalance.getEndingBalance();
                    subjectBalance.setBeginT(endingBalance);
                    subjectBalance.setEndT(BigDecimal.ZERO);
                    subjectBalance.setPeriodNet(BigDecimal.ZERO);
                    subjectBalance.setCurrentOccurrenceAmount(BigDecimal.ZERO);
                    subjectBalance.setGlPeriod(glPeriod);
                });
            }
        }

        return subjectBalances;
    }

    /**
     * 余额分摊
     *
     * @param subjectBalances 待分摊数据
     * @param shareRate       分摊比例
     */
    private void shareBalances(List<SubjectBalance> subjectBalances, BigDecimal shareRate) {
        for (SubjectBalance subjectBalance : subjectBalances) {
            BigDecimal endT = subjectBalance.getEndT();

            // 余额=0时，此科目不参与分摊
            if (BigDecimalUtils.equals(endT, BigDecimal.ZERO)) {
                continue;
            }

            // 固定差异
            final BigDecimal fixedDifferenceAmount =
                    subjectBalanceService.getFixedDifferenceAmount(beginningFixedGlPeriod, subjectBalance.getAccountCode(), DifferenceShareAccountType.BEGINNING.getCode());

            // 分摊额度
            final BigDecimal shareAmount = fixedDifferenceAmount.multiply(shareRate).setScale(2, RoundingMode.HALF_UP);

            // 绝对值
            final BigDecimal abs = endT.abs();

            // 余额绝对值小于1，直接按余额分摊
            /* 所有条件都执行 相同的功能块; Sonar会报问题，当真正需要根据不同条件执行不同的功能时再放出这段代码
            if (BigDecimalUtils.isLess(abs, BigDecimal.ONE)) {
                // 可分摊额大于差异结存额，分摊所有余额
                if (BigDecimalUtils.isGreater(shareAmount.abs(), endT.abs())) {
                    // 分摊完成
                    subjectBalance.setCurrentOccurrenceAmount(endT);
                } else {
                    // 部分分摊
                    subjectBalance.setCurrentOccurrenceAmount(shareAmount);
                }
            } else {
                if (BigDecimalUtils.isGreater(shareAmount.abs(), endT.abs())) {
                    // 分摊完成
                    subjectBalance.setCurrentOccurrenceAmount(endT);
                } else {
                    // 部分分摊
                    subjectBalance.setCurrentOccurrenceAmount(shareAmount);
                }
            }
             */

            // 可分摊额大于差异结存额，分摊所有余额
            if (BigDecimalUtils.isGreater(shareAmount.abs(), endT.abs())) {
                // 分摊完成
                subjectBalance.setCurrentOccurrenceAmount(endT);
            } else {
                // 部分分摊
                subjectBalance.setCurrentOccurrenceAmount(shareAmount);
            }
        }
    }

    /**
     * 更新余额表数据
     *
     * @param subjectBalances 待插入数据
     * @return 插入记录数
     */
    private int addSubjectBalances(List<SubjectBalance> subjectBalances) {
        int count = 0;
        if (!CollectionUtils.isEmpty(subjectBalances)) {
            for (SubjectBalance subjectBalance : subjectBalances) {
                subjectBalance.setId(null);

                final BigDecimal endT = subjectBalance.getEndT();
                final BigDecimal currentOccurrenceAmount = subjectBalance.getCurrentOccurrenceAmount();
                subjectBalance.setBeginT(endT);

                final BigDecimal endingBalance = endT.subtract(currentOccurrenceAmount);
                subjectBalance.setEndingBalance(endingBalance);

                if (!BigDecimalUtils.isEquals(endingBalance, BigDecimal.ZERO)) {
                    subjectBalance.setNextFlag(1);
                }

                final int i = subjectBalanceService.insert(subjectBalance);
                count = count + i;
            }
        }

        return count;
    }

    /**
     * 新增入账单
     *
     * @param ouId        ou
     * @param glPeriod    会计期间
     * @param totalAmount 此次发生总金额
     * @return 入账单
     */
    private DifferenceShareAccount addDifferenceShareAccount(Long ouId, String glPeriod, BigDecimal totalAmount, int type) {
        Long unitId = null;
        OperatingUnit ou = CacheDataUtils.findOuById(ouId);
        UnitOuRel unitOuRel = CacheDataUtils.findUnitOuRelByOuId(ouId);
        if (unitOuRel != null && unitOuRel.getUnitId() != null) {
            unitId = CacheDataUtils.getTopUnitIdByUnitId(unitOuRel.getUnitId());
        }

        // 插入入账单
        DifferenceShareAccount differenceShareAccount = new DifferenceShareAccount();
        differenceShareAccount.setStatus(DifferenceShareAccountStatus.VALID.getCode());
        differenceShareAccount.setOuId(ouId);
        differenceShareAccount.setGlPeriod(glPeriod);
        differenceShareAccount.setCurrency("CNY");
        differenceShareAccount.setWoPeriod(null);
        differenceShareAccount.setAmount(totalAmount);
        differenceShareAccount.setSyncStatus(DifferenceShareAccountSyncStatus.TODO.getCode());
        differenceShareAccount.setOuName(ou.getOperatingUnitName());
        differenceShareAccount.setType(type);
        differenceShareAccount.setAccountNum(generateAccountNum(unitId));
        differenceShareAccount.setDeletedFlag(Boolean.FALSE);

        this.insertSelective(differenceShareAccount);

        return differenceShareAccount;
    }

    /**
     * 插入入账单明细及分摊汇总明细
     */
    private void addDetails(List<SubjectBalance> subjectBalances, Long ouId, Long accountId, Map<String, String> subjectMap,
                            List<CarryoverIncomeAccounting> carryoverIncomeAccountings, BigDecimal totalIncomeSubject) {

        // 按照短的科目分组
        Map<String, List<SubjectBalance>> groupSubjectBalanceMap = new HashMap<>();
        subjectBalances.forEach(subjectBalance -> {
            // 科目
            final String segment3 = subjectBalance.getSegment3();
            List<SubjectBalance> groupSubjectBalances = groupSubjectBalanceMap.get(segment3);
            if (groupSubjectBalances == null) {
                groupSubjectBalances = new ArrayList<>();
                groupSubjectBalanceMap.put(segment3, groupSubjectBalances);
            }

            groupSubjectBalances.add(subjectBalance);
        });

        // 非销售场景配置
        final List<BusiSceneNonSaleDetailDto> busiSceneNonSaleDetailDtos = busiSceneNonSaleService.getBusiSceneDetailListByName("期初差异分摊", ouId);

        Map<String, BusiSceneNonSaleDetailDto> busiSceneNonSaleDetailMap = new HashMap<>();
        busiSceneNonSaleDetailDtos.forEach(busiSceneNonSaleDetailDto -> {
            final String flag = busiSceneNonSaleDetailDto.getFlag();
            busiSceneNonSaleDetailMap.put(flag, busiSceneNonSaleDetailDto);
        });

        // 插入入账明细
        List<DifferenceShareAccountDetail> details = new ArrayList<>();

        // 总的分摊额度
        BigDecimal allCurrentOccurrenceAmount = BigDecimal.ZERO;
        // 总的期初余额
        BigDecimal allBeginningBalance = BigDecimal.ZERO;

        final Set<Map.Entry<String, List<SubjectBalance>>> entries = groupSubjectBalanceMap.entrySet();
        for (Map.Entry<String, List<SubjectBalance>> entry : entries) {
            final String key = entry.getKey();
            final List<SubjectBalance> groupSubjectBalances = entry.getValue();

            // 科目对应分摊总额及期初总额
            BigDecimal totalOccurrenceAmount = BigDecimal.ZERO;
            BigDecimal totalBeginningBalance = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(groupSubjectBalances)) {
                for (SubjectBalance groupSubjectBalance : groupSubjectBalances) {
                    totalOccurrenceAmount = totalOccurrenceAmount.add(groupSubjectBalance.getCurrentOccurrenceAmount());
                    totalBeginningBalance = totalBeginningBalance.add(groupSubjectBalance.getBeginT());

                    allCurrentOccurrenceAmount = allCurrentOccurrenceAmount.add(groupSubjectBalance.getCurrentOccurrenceAmount());
                    allBeginningBalance = allBeginningBalance.add(groupSubjectBalance.getBeginT());
                }
            }

            final BusiSceneNonSaleDetailDto busiSceneNonSaleDetailDto = busiSceneNonSaleDetailMap.get(key);

            // 借
            DifferenceShareAccountDetail debitDetail = new DifferenceShareAccountDetail();
            debitDetail.setAccountId(accountId);
            debitDetail.setTypeCode(key);
            debitDetail.setTypeName(subjectMap.get(key));
            debitDetail.setAccountCode(busiSceneNonSaleDetailDto.getAccountGroupDebit());
            debitDetail.setAmount(totalOccurrenceAmount);
            debitDetail.setLoanType(LoanType.DEBIT.getCode());
            debitDetail.setDeletedFlag(Boolean.FALSE);
            debitDetail.setAccountDesc(getCoaSubjectDescription(debitDetail.getAccountCode()));
            debitDetail.setRemark(busiSceneNonSaleDetailDto.getRemark());

            details.add(debitDetail);

            // 贷
            DifferenceShareAccountDetail creditDetail = new DifferenceShareAccountDetail();
            creditDetail.setAccountId(accountId);
            creditDetail.setTypeCode(key);
            creditDetail.setTypeName(subjectMap.get(key));
            creditDetail.setAccountCode(busiSceneNonSaleDetailDto.getAccountGroupCredit());
            creditDetail.setAmount(totalOccurrenceAmount);
            creditDetail.setLoanType(LoanType.CREDIT.getCode());
            creditDetail.setDeletedFlag(Boolean.FALSE);
            creditDetail.setAccountDesc(getCoaSubjectDescription(creditDetail.getAccountCode()));
            creditDetail.setRemark(busiSceneNonSaleDetailDto.getRemark());

            details.add(creditDetail);
        }

        // 添加汇总
        final BusiSceneNonSaleDetailDto busiSceneNonSaleDetailDto = busiSceneNonSaleDetailMap.get("Total");

        Map<String, DifferenceShareAccountDetail> detailMap = new HashMap<>();
        // 汇总借（需要拆成多条），且相同accountCode需要汇总成一条
        for (CarryoverIncomeAccounting carryoverIncomeAccounting : carryoverIncomeAccountings) {
            BigDecimal incomeAmount = carryoverIncomeAccounting.getIncomeAmount();
            String incomeSubject = carryoverIncomeAccounting.getIncomeSubject();

            // 替换第5/6段
            String accountGroupDebit = busiSceneNonSaleDetailDto.getAccountGroupDebit();
            String[] accountGroupDebitArr = accountGroupDebit.split("\\.");
            String[] incomeSubjectArr = incomeSubject.split("\\.");

            String newAccountCode = accountGroupDebitArr[0] + "." + accountGroupDebitArr[1] + "." + accountGroupDebitArr[2] + "."
                    + accountGroupDebitArr[3] + "." + incomeSubjectArr[4] + "." + incomeSubjectArr[5] + "." + accountGroupDebitArr[6];

            DifferenceShareAccountDetail differenceShareAccountDetail = detailMap.get(newAccountCode);

            if (differenceShareAccountDetail == null) {
                // 单个收入分摊额
                BigDecimal occurrenceAmount = incomeAmount.multiply(allCurrentOccurrenceAmount).divide(totalIncomeSubject, 2, RoundingMode.HALF_UP);

                DifferenceShareAccountDetail subDebitDetail = new DifferenceShareAccountDetail();
                subDebitDetail.setAccountId(accountId);
                subDebitDetail.setTypeCode(busiSceneNonSaleDetailDto.getFlag());
                subDebitDetail.setTypeName(subjectMap.get(busiSceneNonSaleDetailDto.getFlag()));
                subDebitDetail.setAmount(occurrenceAmount);
                subDebitDetail.setLoanType(LoanType.DEBIT.getCode());
                subDebitDetail.setDeletedFlag(Boolean.FALSE);
                subDebitDetail.setAccountCode(newAccountCode);
                subDebitDetail.setAccountDesc(getCoaSubjectDescription(subDebitDetail.getAccountCode()));
                subDebitDetail.setRemark(busiSceneNonSaleDetailDto.getRemark());
                detailMap.put(newAccountCode, subDebitDetail);
            } else {
                BigDecimal occurrenceAmount = incomeAmount.multiply(allCurrentOccurrenceAmount).divide(totalIncomeSubject, 2, RoundingMode.HALF_UP);
                BigDecimal currentAmount = differenceShareAccountDetail.getAmount();
                currentAmount = currentAmount.add(occurrenceAmount);
                differenceShareAccountDetail.setAmount(currentAmount);
            }
        }

        List<DifferenceShareAccountDetail> needAddDifferenceShareAccountDetails = new ArrayList<>();
        Set<Map.Entry<String, DifferenceShareAccountDetail>> entries1 = detailMap.entrySet();
        for (Map.Entry<String, DifferenceShareAccountDetail> entry : entries1) {
            DifferenceShareAccountDetail value = entry.getValue();
            BigDecimal amount1 = value.getAmount();
            if (!BigDecimalUtils.equals(amount1, BigDecimal.ZERO)) {
                needAddDifferenceShareAccountDetails.add(value);
            }
        }

        // 最后采用倒减计算，避免尾差
        if (needAddDifferenceShareAccountDetails.size() > 0) {
            BigDecimal lastAmount = BigDecimal.ZERO;

            for (int i = 0; i < needAddDifferenceShareAccountDetails.size() - 1; i++) {
                DifferenceShareAccountDetail detail = needAddDifferenceShareAccountDetails.get(i);
                lastAmount = lastAmount.add(detail.getAmount());
            }

            lastAmount = allCurrentOccurrenceAmount.subtract(lastAmount);
            needAddDifferenceShareAccountDetails.get(needAddDifferenceShareAccountDetails.size() - 1).setAmount(lastAmount);

            details.addAll(needAddDifferenceShareAccountDetails);
        }

        // 汇总贷
        DifferenceShareAccountDetail creditDetail = new DifferenceShareAccountDetail();
        creditDetail.setAccountId(accountId);
        creditDetail.setTypeCode(busiSceneNonSaleDetailDto.getFlag());
        creditDetail.setTypeName(subjectMap.get(busiSceneNonSaleDetailDto.getFlag()));
        creditDetail.setAccountCode(busiSceneNonSaleDetailDto.getAccountGroupCredit());
        creditDetail.setAmount(allCurrentOccurrenceAmount);
        creditDetail.setLoanType(LoanType.CREDIT.getCode());
        creditDetail.setDeletedFlag(Boolean.FALSE);
        creditDetail.setAccountDesc(getCoaSubjectDescription(creditDetail.getAccountCode()));
        creditDetail.setRemark(busiSceneNonSaleDetailDto.getRemark());

        details.add(creditDetail);

        // 入库
        if (details.size() > 0) {
            for (DifferenceShareAccountDetail detail : details) {
                String typeCode = detail.getTypeCode();

                // 期初类型差役分摊因为业务规则变更，生成会计科目时只保留“total”类型的科目入账
                if (!Objects.equals(typeCode, "Total")) {
                    detail.setDeletedFlag(Boolean.TRUE);
                }

                detailService.insertSelective(detail);
            }

        }
    }

    /**
     * 新增入账单分摊汇总明细
     *
     * @param subjectBalances
     * @param accountId
     * @param subjectMap
     */
    private void addSummaries(List<SubjectBalance> subjectBalances, BigDecimal shareRate, Long accountId, Map<String, String> subjectMap, int differenceCategory) {

        // 插入分摊汇总
        List<DifferenceShareAccountSummary> differenceShareAccountSummaries = new ArrayList<>();
        // 插入分摊汇总
        subjectBalances.forEach(subjectBalance -> {

            DifferenceShareAccountSummary differenceShareAccountSummary = new DifferenceShareAccountSummary();
            differenceShareAccountSummary.setAccountId(accountId);
            differenceShareAccountSummary.setSubjectCode(subjectBalance.getSegment3());
            differenceShareAccountSummary.setSubjectName(subjectMap.get(subjectBalance.getSegment3()));
            differenceShareAccountSummary.setBeginningBalance(subjectBalance.getBeginT());
            differenceShareAccountSummary.setCurrentTransferredAmount(subjectBalance.getCurrentOccurrenceAmount());
            final BigDecimal endingBalance = subjectBalance.getEndT().subtract(subjectBalance.getCurrentOccurrenceAmount());
            differenceShareAccountSummary.setEndingBalance(endingBalance);
            differenceShareAccountSummary.setAccountCode(subjectBalance.getAccountCode());
            differenceShareAccountSummary.setDifferenceCategory(differenceCategory);
            differenceShareAccountSummary.setCurrentOccurrenceAmount(BigDecimal.ZERO);
            differenceShareAccountSummary.setUnallocatedDifferenceAmount(subjectBalance.getEndT());
            differenceShareAccountSummary.setDeletedFlag(Boolean.FALSE);
            differenceShareAccountSummary.setShareRate(shareRate.multiply(new BigDecimal("100")));

            differenceShareAccountSummaries.add(differenceShareAccountSummary);

        });

        addDifferenceShareAccountSummaries(differenceShareAccountSummaries);
    }

    private void addSummaries(List<SubjectBalance> subjectBalances, Long accountId, Map<String, String> subjectMap, int differenceCategory, BigDecimal shareRate) {

        // 插入分摊汇总
        List<DifferenceShareAccountSummary> differenceShareAccountSummaries = new ArrayList<>();
        // 插入分摊汇总
        subjectBalances.forEach(subjectBalance -> {
            DifferenceShareAccountSummary differenceShareAccountSummary = new DifferenceShareAccountSummary();
            differenceShareAccountSummary.setAccountId(accountId);
            differenceShareAccountSummary.setSubjectCode(subjectBalance.getSegment3());
            differenceShareAccountSummary.setSubjectName(subjectMap.get(subjectBalance.getSegment3()));
            differenceShareAccountSummary.setBeginningBalance(subjectBalance.getBeginT());
            differenceShareAccountSummary.setCurrentOccurrenceAmount(subjectBalance.getPeriodNet());
            differenceShareAccountSummary.setEndingBalance(subjectBalance.getEndingBalance());
            differenceShareAccountSummary.setAccountCode(subjectBalance.getAccountCode());
            differenceShareAccountSummary.setDifferenceCategory(differenceCategory);
            differenceShareAccountSummary.setUnallocatedDifferenceAmount(subjectBalance.getEndT());
            differenceShareAccountSummary.setDeletedFlag(Boolean.FALSE);
            differenceShareAccountSummary.setShareRate(shareRate.multiply(new BigDecimal("100")));
            differenceShareAccountSummary.setCurrentTransferredAmount(subjectBalance.getCurrentOccurrenceAmount());

            if (!BigDecimalUtils.equals(differenceShareAccountSummary.getBeginningBalance(), BigDecimal.ZERO)
                    || !BigDecimalUtils.equals(differenceShareAccountSummary.getCurrentOccurrenceAmount(), BigDecimal.ZERO)
                    || !BigDecimalUtils.equals(differenceShareAccountSummary.getUnallocatedDifferenceAmount(), BigDecimal.ZERO)) {
                differenceShareAccountSummaries.add(differenceShareAccountSummary);
            }
        });

        addDifferenceShareAccountSummaries(differenceShareAccountSummaries);
    }

    /**
     * 保持分摊汇总
     *
     * @param differenceShareAccountSummaries 分摊汇总
     * @return
     */
    private int addDifferenceShareAccountSummaries(List<DifferenceShareAccountSummary> differenceShareAccountSummaries) {
        int count = 0;
        if (!CollectionUtils.isEmpty(differenceShareAccountSummaries)) {
            for (DifferenceShareAccountSummary differenceShareAccountSummary : differenceShareAccountSummaries) {
                int i = summaryService.insert(differenceShareAccountSummary);
                count = count + i;
            }
        }
        return count;
    }

    private String getCoaSubjectDescription(String subject) {
        if (StringUtils.isNotEmpty(subject)) {
            String[] flexValueSetIds = subject.split("\\.");
            CoaSubject coaSubject = basedataExtService.getCoaSubject(flexValueSetIds[2]);
            if (!ObjectUtils.isEmpty(coaSubject)) {
                return coaSubject.getDescription();
            }
        }
        return null;
    }

    /**
     * 结转单编号规则： 代码公司段(美云:M，机器人:R)+JZ+YY+MM+DD+3位流水码
     *
     * @return 入账单号
     */
    private String generateAccountNum(Long unitId) {
        String unitSeqPerfix = basedataExtService.getUnitSeqPerfix(unitId);
        StringBuffer accountNum = new StringBuffer();
        accountNum.append(unitSeqPerfix);
        accountNum.append(CodePrefix.DIFFERENCE_SHARE_ACCOUNT_CODE.code());
        accountNum.append(CacheDataUtils.generateSequence(Constants.DIFFERENCE_SHARE_ACCOUNT_LENGTH, unitSeqPerfix + CodePrefix.DIFFERENCE_SHARE_ACCOUNT_CODE.code(), DateUtil.DATE_YYMMDD_PATTERN));
        return accountNum.toString();
    }

    @Transactional
    @Override
    public void accountSync(Long id) {
        final DifferenceShareAccount differenceShareAccount = this.selectByPrimaryKey(id);
        if (differenceShareAccount == null) {
            throw new BizException(Code.ERROR, "入账单不存在");
        }
        HandleDispatcher.route(BusinessTypeEnums.DIFFERENCE_SHARE_ACCOUNT_SYNC_ERP.getCode(), String.valueOf(id), null, null, Boolean.FALSE);
        differenceShareAccount.setSyncStatus(DifferenceShareAccountSyncStatus.DOING.getCode());
        this.updateByPrimaryKeySelective(differenceShareAccount);

        AgencySynInfoExample agencySynInfoExample = new AgencySynInfoExample();
        agencySynInfoExample.createCriteria().andBusinessTypeEqualTo(BusinessTypeEnums.DIFFERENCE_SHARE_ACCOUNT_SYNC_ERP.getCode())
                .andApplyNoEqualTo(id.toString()).andDeletedFlagEqualTo(Boolean.FALSE);
        final List<AgencySynInfo> agencySyninfoList = agencySynInfoMapper.selectByExample(agencySynInfoExample);
        if (ListUtils.isNotEmpty(agencySyninfoList)){
            for (AgencySynInfo agencySyninfo : agencySyninfoList) {
                agencySyninfo.setSynStartTime(new Date());
                agencySynInfoMapper.updateByPrimaryKey(agencySyninfo);
            }
        }else {
            AgencySynInfo agencySyninfo = new AgencySynInfo();
            agencySyninfo.setBusinessType(BusinessTypeEnums.DIFFERENCE_SHARE_ACCOUNT_SYNC_ERP.getCode());
            agencySyninfo.setApplyNo(id.toString());
            agencySyninfo.setSynStartTime(new Date());
            agencySyninfo.setDeletedFlag(Boolean.FALSE);
            agencySynInfoMapper.insert(agencySyninfo);
        }
    }

    @Override
    public void differenceShareResultReturnHandle(Long id, Boolean success, String msg) {
        DifferenceShareAccount differenceShareAccount = this.selectByPrimaryKey(id);
        if (differenceShareAccount == null) {
            logger.info("差异分摊入账单同步esb回调{}不存在", id);
            return;
        }

        if (success) {
            differenceShareAccount.setSyncStatus(DifferenceShareAccountSyncStatus.SUCCESS.getCode());
            differenceShareAccount.setSyncMessage(null);

            DifferenceShareResultDetailExample differenceShareResultDetailExample = new DifferenceShareResultDetailExample();
            DifferenceShareResultDetailExample.Criteria criteria = differenceShareResultDetailExample.createCriteria();
            criteria.andDeletedFlagEqualTo(Boolean.FALSE);
            criteria.andDifferenceShareAccountIdEqualTo(id);
            criteria.andStatusEqualTo(DifferenceShareResultDetailStatus.NO.getCode());
            List<DifferenceShareResultDetail> differenceShareResultDetails = differenceShareResultDetailService.selectByExample(differenceShareResultDetailExample);
            if (ListUtils.isNotEmpty(differenceShareResultDetails)) {
                differenceShareResultDetails.forEach(differenceShareResultDetail -> {
                    differenceShareResultDetail.setStatus(DifferenceShareResultDetailStatus.TODO.getCode());
                    differenceShareResultDetailService.updateByPrimaryKeySelective(differenceShareResultDetail);
                });
            }
        } else {
            differenceShareAccount.setSyncStatus(DifferenceShareAccountSyncStatus.FAILURE.getCode());
            differenceShareAccount.setSyncMessage(msg);
        }

        this.updateByPrimaryKey(differenceShareAccount);
    }

    @Transactional
    @Override
    public DifferenceShareAccount currentCalculate(String glPeriod, Long ouId) {
        // 校验
        currentCalculateCheck(glPeriod, ouId);

        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
        if (ListUtils.isEmpty(organizationRelList)) {
            throw new BizException(Code.ERROR, ouId + "erp组织配置不存在");
        }

        OrganizationRelDto organizationRelDto = organizationRelList.get(0);

        Date startDate = DateUtil.getBeginningOfMonth(glPeriod);
        Date endDate = DateUtil.getEndOfMonth(glPeriod);
        String startDateStr = DateUtil.format(startDate, DateUtil.DATE_PATTERN);
        String endDateStr = DateUtil.format(endDate, DateUtil.DATE_PATTERN);

        // 每个项目对应的差异总额
        Map<String, BigDecimal> directProjectMap = new HashMap<>();
        // 科目 =》 （项目 =》 发生额）
        Map<String, Map<String, BigDecimal>> directAccountProjectAmountMap = new HashMap<>();

        // 成本科目汇总
        Map<String, BigDecimal> materialAccountMap = new HashMap<>();
        // 可进行差异分摊成本更新记录
        List<MaterialUpdateDifferenceRecord> materialUpdateDifferenceRecords =
                materialUpdateDifferenceRecordService.listByOuIdAndGlDate(organizationRelDto.getOrganizationId(), startDateStr, endDateStr);
        materialUpdateDifferenceRecordHandle(materialUpdateDifferenceRecords, materialAccountMap, directProjectMap, directAccountProjectAmountMap);

        // 发票价差
        List<InvoicePriceDifferenceRecord> invoicePriceDifferenceRecords = invoicePriceDifferenceRecordService.listByOuIdAndGlDate(ouId, startDateStr, endDateStr);
        // 发票价差汇总
        Map<String, BigDecimal> invoicePriceAccountMap = new HashMap<>();
        invoicePriceDifferenceRecordHandle(invoicePriceDifferenceRecords, invoicePriceAccountMap, directProjectMap, directAccountProjectAmountMap);

        // 采购价差
        List<PurchasePriceDifferenceRecord> purchasePriceDifferenceRecords = purchasePriceDifferenceRecordService.listByOuIdAndGlDate(ouId, startDateStr, endDateStr);
        // 采购价差汇总
        Map<String, BigDecimal> purchasePriceAccountMap = new HashMap<>();
        purchasePriceDifferenceRecordHandle(purchasePriceDifferenceRecords, purchasePriceAccountMap, directProjectMap, directAccountProjectAmountMap);

        // 科目余额
        List<SubjectBalanceRecord> subjectBalanceRecords = subjectBalanceRecordService.listByGlPeriodAndLedgerId(glPeriod, ouId, organizationRelDto.getLedgerId());
        // 科目余额汇总
        Map<String, BigDecimal> subjectBalanceRecordAccountMap = new HashMap<>();
        subjectBalanceRecordHandle(subjectBalanceRecords, subjectBalanceRecordAccountMap);

        // 项目物料领用信息
        Map<String, BigDecimal> projectMaterialMap = new HashMap<>();
        // 项目物料领用信息
        BigDecimal projectMaterialTotalAmount = getProjectMaterialTotalAmount(startDate, endDate, ouId, projectMaterialMap);

        // 直接差异分摊率
        BigDecimal directRadio = getDirectRadio(ouId);
        // 间接差异
        BigDecimal indirectRadio = getIndirectRadio(glPeriod, ouId, organizationRelDto, projectMaterialTotalAmount);

        // 查询设置的可参与分摊科目列表
        List<String> subjectList = getShareSubject();

        // 直接差异科目余额表
        List<SubjectBalance> directSubjectBalances = getCurrentNeedShareSubjectBalances(glPeriod, ouId, subjectList, DifferenceShareAccountType.CURRENT_DIRECT.getCode());

        // 间接差异科目余额表
        List<SubjectBalance> indirectSubjectBalances = getCurrentNeedShareSubjectBalances(glPeriod, ouId, subjectList, DifferenceShareAccountType.CURRENT_INDIRECT.getCode());

        // 科目直接差异分摊
        Map<String, BigDecimal> directAccountMap = new HashMap<>();

        // 当期发生总额
        BigDecimal totalAmount = BigDecimal.ZERO;
        // 间接差异总额
        BigDecimal indirectTotalAmount = BigDecimal.ZERO;

        // 直接差异分摊
        if (ListUtils.isNotEmpty(directSubjectBalances)) {
            for (SubjectBalance subjectBalance : directSubjectBalances) {
                BigDecimal beginT = subjectBalance.getBeginT();
                String accountCode = subjectBalance.getAccountCode();

                // 间接发生额
                BigDecimal materialAccountAmount =
                        materialAccountMap.get(accountCode) == null ? BigDecimal.ZERO : materialAccountMap.get(accountCode);
                BigDecimal invoicePriceAmount =
                        invoicePriceAccountMap.get(accountCode) == null ? BigDecimal.ZERO : invoicePriceAccountMap.get(accountCode);
                BigDecimal purchasePriceAmount =
                        purchasePriceAccountMap.get(accountCode) == null ? BigDecimal.ZERO : purchasePriceAccountMap.get(accountCode);

                // 直接差异分摊额
                // 本期直接差异分摊额
                BigDecimal periodNet = materialAccountAmount.add(invoicePriceAmount).add(purchasePriceAmount);
                directAccountMap.put(accountCode, periodNet);

                // 间接发生额
                BigDecimal endT = periodNet.add(beginT);
                BigDecimal currentOccurrenceAmount = periodNet.multiply(directRadio).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal endingBalance = endT.subtract(currentOccurrenceAmount);

                subjectBalance.setPeriodNet(periodNet);
                subjectBalance.setEndT(endT);
                subjectBalance.setCurrentOccurrenceAmount(currentOccurrenceAmount);
                subjectBalance.setEndingBalance(endingBalance);

                totalAmount = totalAmount.add(currentOccurrenceAmount);
                indirectTotalAmount = indirectTotalAmount.add(currentOccurrenceAmount);
            }

            // 更新
            directSubjectBalances.forEach(subjectBalance -> subjectBalanceService.updateByPrimaryKeySelective(subjectBalance));
        }

        Map<String, Map<String, BigDecimal>> indirectAccountProjectAmountMap = new HashMap<>();

        // 间接差异分摊
        if (ListUtils.isNotEmpty(indirectSubjectBalances)) {
            for (SubjectBalance subjectBalance : indirectSubjectBalances) {
                String accountCode = subjectBalance.getAccountCode();
                BigDecimal beginT = subjectBalance.getBeginT();

                // 总发生额
                BigDecimal periodNetAmount =
                        subjectBalanceRecordAccountMap.get(accountCode) == null ? BigDecimal.ZERO : subjectBalanceRecordAccountMap.get(accountCode);

                // 直接发生额
                BigDecimal directAmount =
                        directAccountMap.get(accountCode) == null ? BigDecimal.ZERO : directAccountMap.get(accountCode);

                // 间接发生额
                BigDecimal periodNet = periodNetAmount.subtract(directAmount);
                BigDecimal endT = periodNet.add(beginT);
                BigDecimal indirectAmount = periodNetAmount.subtract(directAmount);
                BigDecimal currentOccurrenceAmount = indirectAmount.multiply(indirectRadio).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal endingBalance = endT.subtract(currentOccurrenceAmount);

                subjectBalance.setPeriodNet(indirectAmount);
                subjectBalance.setEndT(endT);
                subjectBalance.setCurrentOccurrenceAmount(currentOccurrenceAmount);
                subjectBalance.setEndingBalance(endingBalance);

                totalAmount = totalAmount.add(currentOccurrenceAmount);
            }

            indirectSubjectBalances.forEach(subjectBalance -> subjectBalanceService.updateByPrimaryKeySelective(subjectBalance));

            List<SubjectBalance> needHandleList = new ArrayList<>();
            for (SubjectBalance indirectSubjectBalance : indirectSubjectBalances) {
                if (!BigDecimalUtils.equals(indirectSubjectBalance.getCurrentOccurrenceAmount(), BigDecimal.ZERO)) {
                    needHandleList.add(indirectSubjectBalance);
                }
            }

            if (needHandleList.size() > 0) {
                for (int i = 0; i < needHandleList.size(); i++) {
                    SubjectBalance subjectBalance = needHandleList.get(i);
                    BigDecimal currentOccurrenceAmount = subjectBalance.getCurrentOccurrenceAmount();
                    String accountCode = subjectBalance.getAccountCode();

                    BigDecimal accountTotalAmount = BigDecimal.ZERO;
                    Map<String, BigDecimal> projectAmountMap = new HashMap<>();
                    Set<Map.Entry<String, BigDecimal>> entries = projectMaterialMap.entrySet();

                    Iterator<Map.Entry<String, BigDecimal>> iterator = entries.iterator();
                    Map.Entry<String, BigDecimal> tail = null;
                    while (iterator.hasNext()) {
                        tail = iterator.next();
                    }

                    for (Map.Entry<String, BigDecimal> entry : entries) {
                        String key = entry.getKey();
                        if (Objects.equals(key, tail.getKey())) {
                            continue;
                        }

                        String projectCode = entry.getKey();
                        // 项目领用物料金额
                        BigDecimal projectMaterialAmount = entry.getValue();
                        // 每个项目在该科目分摊额度
                        BigDecimal indirectAmount =
                                projectMaterialAmount.divide(projectMaterialTotalAmount, 4, BigDecimal.ROUND_HALF_UP)
                                        .multiply(currentOccurrenceAmount).setScale(2, BigDecimal.ROUND_HALF_UP);

                        // 科目项目对应的发生额
                        projectAmountMap.put(projectCode, indirectAmount);

                        accountTotalAmount = accountTotalAmount.add(indirectAmount);
                        indirectAccountProjectAmountMap.put(accountCode, projectAmountMap);
                    }

                    // 到减法
                    String projectCode = tail.getKey();
                    // 每个项目在该科目分摊额度
                    BigDecimal indirectAmount = currentOccurrenceAmount.subtract(accountTotalAmount);

                    // 科目项目对应的发生额
                    projectAmountMap.put(projectCode, indirectAmount);
                    indirectAccountProjectAmountMap.put(accountCode, projectAmountMap);
                }
            }
        }

        // 新增入账单头表
        DifferenceShareAccount differenceShareAccount = addDifferenceShareAccount(ouId, glPeriod, totalAmount, DifferenceShareAccountType.CURRENT.getCode());

        // 可分摊类型名称
        Map<String, String> subjectMap = new HashMap<>();
        final List<DictDto> allDictDtos = basedataExtService.getLtcDict("CostElements_Type", null, null);
        if (!CollectionUtils.isEmpty(allDictDtos)) {
            allDictDtos.forEach(dictDto -> subjectMap.put(dictDto.getCode(), dictDto.getName()));
        }

        // 新增汇总明细
        addSummaries(directSubjectBalances, differenceShareAccount.getId(), subjectMap, 1, directRadio);
        addSummaries(indirectSubjectBalances, differenceShareAccount.getId(), subjectMap, 2, indirectRadio);

        // 新增明细
        addDetails(directSubjectBalances, ouId, differenceShareAccount.getId(), subjectMap, 1);
        addDetails(indirectSubjectBalances, ouId, differenceShareAccount.getId(), subjectMap, 2);

        // 项目差异计算: 项目直接差异 + 项目间接差异
        Set<Map.Entry<String, BigDecimal>> entries = projectMaterialMap.entrySet();
        for (Map.Entry<String, BigDecimal> entry : entries) {
            String projectCode = entry.getKey();
            // 项目领用物料金额
            BigDecimal projectMaterialAmount = entry.getValue();
            BigDecimal indirectAmount =
                    projectMaterialAmount.divide(projectMaterialTotalAmount, 4, BigDecimal.ROUND_HALF_UP)
                            .multiply(indirectTotalAmount).setScale(2, BigDecimal.ROUND_HALF_UP);

            if (StringUtils.isNotEmpty(projectCode)) {
                if (directProjectMap.containsKey(projectCode)) {
                    BigDecimal directAmount = directProjectMap.get(projectCode);
                    directAmount = directAmount.add(indirectAmount);
                    directProjectMap.put(projectCode, directAmount);
                } else {
                    directProjectMap.put(projectCode, indirectAmount);
                }
            }
        }

        // 直接差异插入到待归集明细
        List<DifferenceShareResultDetail> differenceShareResultDetailList
                = addResultDetail(directAccountProjectAmountMap, differenceShareAccount.getId(), directRadio, endDate, DifferenceShareAccountType.CURRENT_DIRECT.getCode());

        if (differenceShareResultDetailList.size() > 0) {
            differenceShareResultDetailList.forEach(differenceShareResultDetail -> {
                differenceShareResultDetailService.insert(differenceShareResultDetail);
            });
        }

        differenceShareResultDetailList = addResultDetail(indirectAccountProjectAmountMap, differenceShareAccount.getId(), indirectRadio, endDate, DifferenceShareAccountType.CURRENT_INDIRECT.getCode());

        if (differenceShareResultDetailList.size() > 0) {
            differenceShareResultDetailList.forEach(differenceShareResultDetail -> {
                differenceShareResultDetailService.insert(differenceShareResultDetail);
            });
        }

        return differenceShareAccount;
    }

    /**
     * 成本更新
     *
     * @param directProjectMap
     * @param materialAccountMap
     */
    private void materialUpdateDifferenceRecordHandle(List<MaterialUpdateDifferenceRecord> materialUpdateDifferenceRecords,
                                                      Map<String, BigDecimal> materialAccountMap, Map<String, BigDecimal> directProjectMap,
                                                      Map<String, Map<String, BigDecimal>> directAccountProjectAmountMap) {


        if (ListUtils.isNotEmpty(materialUpdateDifferenceRecords)) {
            for (MaterialUpdateDifferenceRecord materialUpdateDifferenceRecord : materialUpdateDifferenceRecords) {
                // 更新总金额
                BigDecimal trxValueBase = materialUpdateDifferenceRecord.getTrxValueBase();
                // 项目号
                String locator = materialUpdateDifferenceRecord.getLocator();
                // 会计科目段
                String accountSegment = materialUpdateDifferenceRecord.getAccountSegment();

                if (StringUtils.isNotEmpty(locator)) {
                    if (directProjectMap.containsKey(locator)) {
                        BigDecimal amount = directProjectMap.get(locator);
                        amount = amount.add(trxValueBase);
                        directProjectMap.put(locator, amount);
                    } else {
                        directProjectMap.put(locator, trxValueBase);
                    }

                    // 统计每个科目段对于的金额总和
                    if (materialAccountMap.containsKey(accountSegment)) {
                        BigDecimal materialAccountAmount = materialAccountMap.get(accountSegment);
                        materialAccountAmount = materialAccountAmount.add(trxValueBase);
                        materialAccountMap.put(accountSegment, materialAccountAmount);
                    } else {
                        materialAccountMap.put(accountSegment, trxValueBase);
                    }

                    // 科目项目对应的发生额
                    if (directAccountProjectAmountMap.containsKey(accountSegment)) {
                        Map<String, BigDecimal> projectAmountMap = directAccountProjectAmountMap.get(accountSegment);

                        if (projectAmountMap.containsKey(locator)) {
                            BigDecimal amount = projectAmountMap.get(locator);
                            amount = amount.add(trxValueBase);
                            projectAmountMap.put(locator, amount);
                        } else {
                            projectAmountMap.put(locator, trxValueBase);
                        }
                    } else {
                        Map<String, BigDecimal> projectAmountMap = new HashMap<>();
                        projectAmountMap.put(locator, trxValueBase);
                        directAccountProjectAmountMap.put(accountSegment, projectAmountMap);
                    }
                }
            }
        }
    }

    private void invoicePriceDifferenceRecordHandle(List<InvoicePriceDifferenceRecord> invoicePriceDifferenceRecords,
                                                    Map<String, BigDecimal> invoicePriceAccountMap, Map<String, BigDecimal> directProjectMap,
                                                    Map<String, Map<String, BigDecimal>> directAccountProjectAmountMap) {


        if (ListUtils.isNotEmpty(invoicePriceDifferenceRecords)) {
            for (InvoicePriceDifferenceRecord invoicePriceDifferenceRecord : invoicePriceDifferenceRecords) {
                String projectNum = invoicePriceDifferenceRecord.getProjectNum();
                BigDecimal differenceBaseAmount = invoicePriceDifferenceRecord.getDifferenceBaseAmount();
                String concatenatedSegments = invoicePriceDifferenceRecord.getConcatenatedSegments();

                if (StringUtils.isNotEmpty(projectNum)) {
                    if (directProjectMap.containsKey(projectNum)) {
                        BigDecimal amount = directProjectMap.get(projectNum);
                        amount = amount.add(differenceBaseAmount);
                        directProjectMap.put(projectNum, amount);
                    } else {
                        directProjectMap.put(projectNum, differenceBaseAmount);
                    }

                    // 统计每个科目段对于的金额总和
                    if (invoicePriceAccountMap.containsKey(concatenatedSegments)) {
                        BigDecimal accountAmount = invoicePriceAccountMap.get(concatenatedSegments);
                        accountAmount = accountAmount.add(differenceBaseAmount);
                        invoicePriceAccountMap.put(concatenatedSegments, accountAmount);
                    } else {
                        invoicePriceAccountMap.put(concatenatedSegments, differenceBaseAmount);
                    }

                    // 科目项目对应的发生额
                    if (directAccountProjectAmountMap.containsKey(concatenatedSegments)) {
                        Map<String, BigDecimal> projectAmountMap = directAccountProjectAmountMap.get(concatenatedSegments);

                        if (projectAmountMap.containsKey(projectNum)) {
                            BigDecimal amount = projectAmountMap.get(projectNum);
                            amount = amount.add(differenceBaseAmount);
                            projectAmountMap.put(projectNum, amount);
                        } else {
                            projectAmountMap.put(projectNum, differenceBaseAmount);
                        }
                    } else {
                        Map<String, BigDecimal> projectAmountMap = new HashMap<>();
                        projectAmountMap.put(projectNum, differenceBaseAmount);
                        directAccountProjectAmountMap.put(concatenatedSegments, projectAmountMap);
                    }
                }
            }
        }
    }

    private void purchasePriceDifferenceRecordHandle(List<PurchasePriceDifferenceRecord> purchasePriceDifferenceRecords, Map<String, BigDecimal> purchasePriceAccountMap, Map<String, BigDecimal> directProjectMap, Map<String, Map<String, BigDecimal>> directAccountProjectAmountMap) {
        if (ListUtils.isNotEmpty(purchasePriceDifferenceRecords)) {
            for (PurchasePriceDifferenceRecord purchasePriceDifferenceRecord : purchasePriceDifferenceRecords) {
                BigDecimal varianceAmountBase =
                        purchasePriceDifferenceRecord.getVarianceAmountBase() == null ? BigDecimal.ZERO : purchasePriceDifferenceRecord.getVarianceAmountBase();
                String projectNum = purchasePriceDifferenceRecord.getProjectNum();
                String accountCombine = purchasePriceDifferenceRecord.getAccountCombine();

                if (StringUtils.isNotEmpty(projectNum)) {
                    if (directProjectMap.containsKey(projectNum)) {
                        BigDecimal amount = directProjectMap.get(projectNum);
                        amount = amount.add(varianceAmountBase);
                        directProjectMap.put(projectNum, amount);
                    } else {
                        directProjectMap.put(projectNum, varianceAmountBase);
                    }

                    // 统计每个科目段对于的金额总和
                    if (purchasePriceAccountMap.containsKey(accountCombine)) {
                        BigDecimal accountAmount = purchasePriceAccountMap.get(accountCombine);
                        accountAmount = accountAmount.add(varianceAmountBase);
                        purchasePriceAccountMap.put(accountCombine, accountAmount);
                    } else {
                        purchasePriceAccountMap.put(accountCombine, varianceAmountBase);
                    }

                    // 科目项目对应的发生额
                    if (directAccountProjectAmountMap.containsKey(accountCombine)) {
                        Map<String, BigDecimal> projectAmountMap = directAccountProjectAmountMap.get(accountCombine);

                        if (projectAmountMap.containsKey(projectNum)) {
                            BigDecimal amount = projectAmountMap.get(projectNum);
                            amount = amount.add(varianceAmountBase);
                            projectAmountMap.put(projectNum, amount);
                        } else {
                            projectAmountMap.put(projectNum, varianceAmountBase);
                        }
                    } else {
                        Map<String, BigDecimal> projectAmountMap = new HashMap<>();
                        projectAmountMap.put(projectNum, varianceAmountBase);
                        directAccountProjectAmountMap.put(accountCombine, projectAmountMap);
                    }
                }
            }
        }
    }

    private void subjectBalanceRecordHandle(List<SubjectBalanceRecord> subjectBalanceRecords, Map<String, BigDecimal> subjectBalanceRecordAccountMap) {
        if (ListUtils.isNotEmpty(subjectBalanceRecords)) {
            subjectBalanceRecords.forEach(subjectBalanceRecord -> {
                String accountCode = subjectBalanceRecord.getAccountCode();
                // 发生额 = 借-贷
                BigDecimal periodNetDr =
                        subjectBalanceRecord.getPeriodNetDr() == null ? BigDecimal.ZERO : subjectBalanceRecord.getPeriodNetDr();
                BigDecimal periodNetCr =
                        subjectBalanceRecord.getPeriodNetCr() == null ? BigDecimal.ZERO : subjectBalanceRecord.getPeriodNetCr();

                // 总的科目发生额 = 借-贷
                BigDecimal periodNet = periodNetDr.subtract(periodNetCr);
                if (subjectBalanceRecordAccountMap.containsKey(accountCode)
                        && subjectBalanceRecordAccountMap.get(accountCode) != null) {
                    periodNet = periodNet.add(subjectBalanceRecordAccountMap.get(accountCode));
                }
                subjectBalanceRecordAccountMap.put(accountCode, periodNet);
            });
        }
    }

    private ProjectDto getProjectByCode(String code) {
        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andCodeEqualTo(code);

        List<Project> projects = projectService.selectByExample(projectExample);
        if (ListUtils.isNotEmpty(projects)) {
            Long id = projects.get(0).getId();
            return projectService.findDetail(id);
        }

        return null;
    }

    /**
     * 直接差异分摊率
     */
    private BigDecimal getDirectRadio(Long ouId) {
        // 查询设置的期初分摊对应收入
        final Set<String> organizationCustomDictSet = organizationCustomDictService.queryByName("直接差异分摊率", ouId, OrgCustomDictOrgFrom.OU);
        if (CollectionUtils.isEmpty(organizationCustomDictSet)) {
            throw new BizException(Code.ERROR, "请先配置直接差异分摊率");
        }

        final Optional<String> first = organizationCustomDictSet.stream().findFirst();
        if (!first.isPresent()) {
            throw new BizException(Code.ERROR, "请先配置直接差异分摊率");
        }

        String directRadioStr = first.get();
        // 直接差异分摊率
        BigDecimal directRadio = new BigDecimal(directRadioStr).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
        return directRadio;
    }

    /**
     * 项目领用物料总额
     */
    private BigDecimal getProjectMaterialTotalAmount(Date startDate, Date endDate, Long ouId, Map<String, BigDecimal> projectMaterialMap) {
        // 项目物料领用总额
        BigDecimal projectMaterialTotalAmount = BigDecimal.ZERO;
        // 查询领料退料
        List<MaterialActualCostDetailDto> materialActualCostDetailDtoList =
                materialActualCostDetailService.selectByBetweenCollectionDate(startDate, endDate, ouId);

        for (MaterialActualCostDetailDto materialActualCostDetailDto : materialActualCostDetailDtoList) {
            String projectCode = materialActualCostDetailDto.getProjectCode();
            BigDecimal actualAmount = materialActualCostDetailDto.getActualAmount();
            if (projectMaterialMap.containsKey(projectCode)) {
                BigDecimal amount = projectMaterialMap.get(projectCode);

                if (String.valueOf(2).equals(String.valueOf(materialActualCostDetailDto.getType()))) {
                    //退料金额为负
                    actualAmount = actualAmount.multiply(new BigDecimal(-1));

                }
                amount = amount.add(actualAmount);
                projectMaterialMap.put(projectCode, amount);
            } else {
                if (String.valueOf(2).equals(String.valueOf(materialActualCostDetailDto.getType()))) {
                    //退料金额为负
                    actualAmount = actualAmount.multiply(new BigDecimal(-1));
                }
                projectMaterialMap.put(projectCode, actualAmount);
            }

            projectMaterialTotalAmount = projectMaterialTotalAmount.add(actualAmount);
        }
        return projectMaterialTotalAmount;
    }

    /**
     * 查询可分摊科目
     *
     * @return
     */
    private List<String> getShareSubject() {
        final List<DictDto> dictDtos = basedataExtService.getLtcDict("Range of different", null, null);
        List<String> subjectList = new ArrayList<>(); // 科目列表
        if (!CollectionUtils.isEmpty(dictDtos)) {
            subjectList = dictDtos.stream().map(DictDto::getCode).collect(Collectors.toList());
        }
        return subjectList;
    }

    /**
     * 间接差异分摊率
     */
    private BigDecimal getIndirectRadio(String glPeriod, Long ouId, OrganizationRelDto organizationRelDto, BigDecimal projectMaterialTotalAmount) {
        // 查询设置的库存科目
        final List<DictDto> dictDtos2 = basedataExtService.getLtcDict("Range of Inventory", null, null);
        List<String> balanceSubjectList = new ArrayList<>(); // 科目列表
        if (!CollectionUtils.isEmpty(dictDtos2)) {
            dictDtos2.forEach(dictDto -> {
                balanceSubjectList.add(dictDto.getCode());
            });
        }

        // 查询库存科目余额
        BigDecimal balanceAmount = subjectBalanceRecordService.getAmountByGlPeriodAndLedgerIdAndSegment3(glPeriod, ouId, organizationRelDto.getLedgerId(), balanceSubjectList);

        if (balanceAmount == null) {
            balanceAmount = BigDecimal.ZERO;
        }

        // 间接分摊率：当月项目领用/（当月项目领用+库存科目余额）
        BigDecimal indirectRadio =
                projectMaterialTotalAmount.divide(balanceAmount.add(projectMaterialTotalAmount), 4, BigDecimal.ROUND_HALF_UP);
        return indirectRadio;
    }

    private void addDetails(List<SubjectBalance> subjectBalances, Long ouId, Long accountId, Map<String, String> subjectMap, int differenceCategory) {
        if (ListUtils.isNotEmpty(subjectBalances)) {
            // 按照短的科目分组
            Map<String, List<SubjectBalance>> groupSubjectBalanceMap = new HashMap<>();
            subjectBalances.forEach(subjectBalance -> {
                // 科目
                final String segment3 = subjectBalance.getSegment3();
                List<SubjectBalance> groupSubjectBalances = groupSubjectBalanceMap.get(segment3);
                if (groupSubjectBalances == null) {
                    groupSubjectBalances = new ArrayList<>();
                    groupSubjectBalanceMap.put(segment3, groupSubjectBalances);
                }

                groupSubjectBalances.add(subjectBalance);
            });

            // 非销售场景配置
            final List<BusiSceneNonSaleDetailDto> busiSceneNonSaleDetailDtos = busiSceneNonSaleService.getBusiSceneDetailListByName("当期差异分摊", ouId);

            Map<String, BusiSceneNonSaleDetailDto> busiSceneNonSaleDetailMap = new HashMap<>();
            busiSceneNonSaleDetailDtos.forEach(busiSceneNonSaleDetailDto -> {
                final String flag = busiSceneNonSaleDetailDto.getFlag();
                busiSceneNonSaleDetailMap.put(flag, busiSceneNonSaleDetailDto);
            });

            // 插入入账明细
            List<DifferenceShareAccountDetail> details = new ArrayList<>();

            // 总的分摊额度
            BigDecimal allCurrentOccurrenceAmount = BigDecimal.ZERO;
            // 总的期初余额
            BigDecimal allBeginningBalance = BigDecimal.ZERO;

            final Set<Map.Entry<String, List<SubjectBalance>>> entries = groupSubjectBalanceMap.entrySet();
            for (Map.Entry<String, List<SubjectBalance>> entry : entries) {
                final String key = entry.getKey();
                final List<SubjectBalance> groupSubjectBalances = entry.getValue();

                // 科目对应分摊总额及期初总额
                BigDecimal totalOccurrenceAmount = BigDecimal.ZERO;
                BigDecimal totalBeginningBalance = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(groupSubjectBalances)) {
                    for (SubjectBalance groupSubjectBalance : groupSubjectBalances) {
                        totalOccurrenceAmount = totalOccurrenceAmount.add(groupSubjectBalance.getCurrentOccurrenceAmount());
                        totalBeginningBalance = totalBeginningBalance.add(groupSubjectBalance.getBeginT());

                        allCurrentOccurrenceAmount = allCurrentOccurrenceAmount.add(groupSubjectBalance.getCurrentOccurrenceAmount());
                        allBeginningBalance = allBeginningBalance.add(groupSubjectBalance.getBeginT());
                    }
                }

                if (BigDecimalUtils.equals(totalOccurrenceAmount, BigDecimal.ZERO)) {
                    continue;
                }

                final BusiSceneNonSaleDetailDto busiSceneNonSaleDetailDto = busiSceneNonSaleDetailMap.get(key);

                // 借
                DifferenceShareAccountDetail debitDetail = new DifferenceShareAccountDetail();
                debitDetail.setAccountId(accountId);
                debitDetail.setTypeCode(key);
                debitDetail.setTypeName(subjectMap.get(key));
                debitDetail.setAccountCode(busiSceneNonSaleDetailDto != null ? busiSceneNonSaleDetailDto.getAccountGroupDebit() : null);
                debitDetail.setAmount(totalOccurrenceAmount);
                debitDetail.setLoanType(LoanType.DEBIT.getCode());
                debitDetail.setDeletedFlag(Boolean.FALSE);
                debitDetail.setAccountDesc(getCoaSubjectDescription(debitDetail.getAccountCode()));
                debitDetail.setRemark(busiSceneNonSaleDetailDto != null ? busiSceneNonSaleDetailDto.getRemark() + "_" + getDifferenceCategoryName(differenceCategory) : null);

                details.add(debitDetail);

                // 贷
                DifferenceShareAccountDetail creditDetail = new DifferenceShareAccountDetail();
                creditDetail.setAccountId(accountId);
                creditDetail.setTypeCode(key);
                creditDetail.setTypeName(subjectMap.get(key));
                creditDetail.setAccountCode(busiSceneNonSaleDetailDto != null ? busiSceneNonSaleDetailDto.getAccountGroupCredit() : null);
                creditDetail.setAmount(totalOccurrenceAmount);
                creditDetail.setLoanType(LoanType.CREDIT.getCode());
                creditDetail.setDeletedFlag(Boolean.FALSE);
                creditDetail.setAccountDesc(getCoaSubjectDescription(creditDetail.getAccountCode()));
                creditDetail.setRemark(busiSceneNonSaleDetailDto != null ? busiSceneNonSaleDetailDto.getRemark() + "_" + getDifferenceCategoryName(differenceCategory) : null);

                details.add(creditDetail);
            }

            if (details.size() > 0) {
                details.forEach(detail -> {
                    detailService.insertSelective(detail);
                });
            }
        }

    }

    private String getDifferenceCategoryName(int differenceCategory) {
        if (differenceCategory == 1) {
            return "直接差异分摊";
        } else if (differenceCategory == 2) {
            return "间接差异分摊";
        }
        return null;
    }

    private List<DifferenceShareResultDetail> addResultDetail(Map<String, Map<String, BigDecimal>> accountProjectAmountMap, Long differenceShareAccountId,
                                                              BigDecimal directRadio, Date endDate, int differenceType) {

        List<DifferenceShareResultDetail> differenceShareResultDetailList = new ArrayList<>();

        if (accountProjectAmountMap.size() > 0) {
            Set<Map.Entry<String, Map<String, BigDecimal>>> accountEntries = accountProjectAmountMap.entrySet();
            for (Map.Entry<String, Map<String, BigDecimal>> accountEntry : accountEntries) {
                String account = accountEntry.getKey();
                Map<String, BigDecimal> projectMap = accountEntry.getValue();
                Set<Map.Entry<String, BigDecimal>> projectEntries = projectMap.entrySet();
                for (Map.Entry<String, BigDecimal> projectEntry : projectEntries) {
                    String projectCode = projectEntry.getKey();
                    BigDecimal amount = projectEntry.getValue();

                    DifferenceShareResultDetail differenceShareResultDetail = new DifferenceShareResultDetail();

                    differenceShareResultDetail.setAmount(amount);
                    differenceShareResultDetail.setProjectCode(projectCode);
                    ProjectDto project = getProjectByCode(projectCode);
                    if (project != null) {
                        differenceShareResultDetail.setProjectId(project.getId());
                    }
                    differenceShareResultDetail.setAccountCode(account);
                    differenceShareResultDetail.setCostDate(endDate);
                    differenceShareResultDetail.setShareRadio(directRadio.multiply(new BigDecimal(100)));
                    differenceShareResultDetail.setDifferenceShareAccountId(differenceShareAccountId);
                    differenceShareResultDetail.setDifferenceType(differenceType);
                    differenceShareResultDetail.setDeletedFlag(Boolean.FALSE);
                    differenceShareResultDetail.setStatus(DifferenceShareResultDetailStatus.NO.getCode());
                    differenceShareResultDetailList.add(differenceShareResultDetail);
                }
            }
        }

        return differenceShareResultDetailList;
    }

    @Override
    public void currentCalculateCheck(String glPeriod, Long ouId) {
        // 判断库存期间、应付期间已关闭
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
        if (ListUtils.isEmpty(organizationRelList)) {
            throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
        }

        OrganizationRelDto organizationRelDto = organizationRelList.get(0);
        Long ledgerId = organizationRelDto.getLedgerId();
        Long organizationId = organizationRelDto.getOrganizationId();
        List<GlPeriodDto> glPeriodList = basedataExtService.getGlPeriod(ledgerId, null, glPeriod);
        if (ListUtils.isEmpty(glPeriodList)) {
            throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
        }

        // 库存期间
        Long organizationPeriod = GlPeriodType.ORGANIZATION_PERIOD.getCode();
        // 应付期间
        Long paymentPeriod = GlPeriodType.PAYMENT_PERIOD.getCode();
        StringBuilder errorMsg = new StringBuilder();
        glPeriodList.forEach(glPeriodDto -> {
            String periodType = glPeriodDto.getPeriodType();
            String closingStatus = glPeriodDto.getClosingStatus();
            String showStatus = glPeriodDto.getShowStatus();
            String preShowStatus = glPeriodDto.getPreShowStatus();
            String preClosingStatus = glPeriodDto.getPreClosingStatus();

            boolean closeFlag = "C".equals(closingStatus) || "Closed".equals(showStatus) ||
                    "C".equals(preClosingStatus) || "Closed".equals(preShowStatus);
            if (organizationPeriod != null
                    && organizationPeriod.equals(Long.valueOf(periodType))) {
                if (Objects.equals(glPeriodDto.getOrganizationId(), organizationId) && !closeFlag) {
                    errorMsg.append("库存期间未关闭;");
                }
            } else if (paymentPeriod != null && paymentPeriod.equals(Long.valueOf(periodType))) {
                if (!closeFlag) {
                    errorMsg.append("应付期间未关闭;");
                }
            }
        });

        if (errorMsg.length() > 0) {
            throw new BizException(Code.ERROR, errorMsg.substring(0, errorMsg.length() - 1).toString());
        }

        DifferenceShareDataSync differenceShareDataSync = differenceShareDataSyncService.getByOuIdAndPeriodType(ouId, glPeriod, GlPeriodType.ORGANIZATION_PERIOD.getCode());
        if (differenceShareDataSync != null) {
            Integer syncStatus = differenceShareDataSync.getSyncStatus();
            if (DifferenceShareDataSyncStatus.TODO.getCode() == syncStatus) {
                throw new BizException(Code.ERROR, "库存期间还未同步");
            } else if (DifferenceShareDataSyncStatus.DOING.getCode() == syncStatus) {
                throw new BizException(Code.ERROR, "库存期间同步中");
            } else if (DifferenceShareDataSyncStatus.FAILURE.getCode() == syncStatus) {
                throw new BizException(Code.ERROR, "库存期间同步失败");
            }
        } else {
            throw new BizException(Code.ERROR, "库存期间还未同步");
        }

        differenceShareDataSync = differenceShareDataSyncService.getByOuIdAndPeriodType(ouId, glPeriod, GlPeriodType.PAYMENT_PERIOD.getCode());
        if (differenceShareDataSync != null) {
            Integer syncStatus = differenceShareDataSync.getSyncStatus();
            if (DifferenceShareDataSyncStatus.TODO.getCode() == syncStatus) {
                throw new BizException(Code.ERROR, "应付期间还未同步");
            } else if (DifferenceShareDataSyncStatus.DOING.getCode() == syncStatus) {
                throw new BizException(Code.ERROR, "应付期间同步中");
            } else if (DifferenceShareDataSyncStatus.FAILURE.getCode() == syncStatus) {
                throw new BizException(Code.ERROR, "应付期间同步失败");
            }
        } else {
            throw new BizException(Code.ERROR, "应付期间还未同步");
        }
    }

    /**
     * 直接差异
     *
     * @param ouId
     */
    private void directCurrentCalculate(Long ouId) {

    }

    /**
     * 间接差异
     *
     * @param ouId
     */
    private void indirectCurrentCalculate(Long ouId) {

    }

    @Transactional
    @Override
    public DifferenceShareAccount calculate(String glPeriod, Long ouId, Integer type) {
        DifferenceShareAccount differenceShareAccount = null;
        if (type == DifferenceShareAccountType.BEGINNING.getCode()) {
            differenceShareAccount = this.beginningCalculate(glPeriod, ouId);
        } else if (type == DifferenceShareAccountType.CURRENT.getCode()) {
            differenceShareAccount = this.currentCalculate(glPeriod, ouId);
        }
        return differenceShareAccount;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean invalidDifferenceShareAccountById(Long id) {
        DifferenceShareAccount differenceShareAccount = this.selectByPrimaryKey(id);
        if (differenceShareAccount == null) {
            return false;
        }

        differenceShareAccount.setStatus(DifferenceShareAccountStatus.INVALID.getCode());
        this.updateByPrimaryKey(differenceShareAccount);

        // 失效
        DifferenceShareAccountDetailExample detailExample = new DifferenceShareAccountDetailExample();
        DifferenceShareAccountDetailExample.Criteria detailCriteria = detailExample.createCriteria();
        detailCriteria.andAccountIdEqualTo(id);

        List<DifferenceShareAccountDetail> differenceShareAccountDetails = detailService.selectByExample(detailExample);
        if (ListUtils.isNotEmpty(differenceShareAccountDetails)) {
            for (DifferenceShareAccountDetail differenceShareAccountDetail : differenceShareAccountDetails) {
                differenceShareAccountDetail.setDeletedFlag(Boolean.TRUE);
                detailService.updateByPrimaryKeySelective(differenceShareAccountDetail);
            }
        }

        DifferenceShareAccountSummaryExample summaryExample = new DifferenceShareAccountSummaryExample();
        DifferenceShareAccountSummaryExample.Criteria summaryCriteria = summaryExample.createCriteria();
        summaryCriteria.andAccountIdEqualTo(id);

        List<DifferenceShareAccountSummary> differenceShareAccountSummaries = summaryService.selectByExample(summaryExample);
        if (ListUtils.isNotEmpty(differenceShareAccountSummaries)) {
            for (DifferenceShareAccountSummary differenceShareAccountSummary : differenceShareAccountSummaries) {
                differenceShareAccountSummary.setDeletedFlag(Boolean.TRUE);
                summaryService.updateByPrimaryKeySelective(differenceShareAccountSummary);
            }
        }

        DifferenceShareResultDetailExample resultDetailExample = new DifferenceShareResultDetailExample();
        DifferenceShareResultDetailExample.Criteria resultDetailCriteria = resultDetailExample.createCriteria();
        resultDetailCriteria.andDifferenceShareAccountIdEqualTo(id);
        List<DifferenceShareResultDetail> differenceShareResultDetails = differenceShareResultDetailService.selectByExample(resultDetailExample);
        if (ListUtils.isNotEmpty(differenceShareResultDetails)) {
            for (DifferenceShareResultDetail differenceShareResultDetail : differenceShareResultDetails) {
                differenceShareResultDetail.setDeletedFlag(Boolean.TRUE);
                differenceShareResultDetailService.updateByPrimaryKeySelective(differenceShareResultDetail);
            }
        }

        return true;
    }
}
