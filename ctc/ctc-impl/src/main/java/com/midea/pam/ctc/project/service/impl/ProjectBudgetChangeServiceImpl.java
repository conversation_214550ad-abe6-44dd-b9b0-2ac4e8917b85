package com.midea.pam.ctc.project.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.FeeType;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.FlatChangeHistoryDTO;
import com.midea.pam.common.ctc.dto.ProjectBudgetChangeDTO;
import com.midea.pam.common.ctc.dto.ProjectBudgetFeeDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetHumanChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetMaterialChangeHistoryDTO;
import com.midea.pam.common.ctc.dto.ProjectBudgetMaterialDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectMemberChangeHistoryDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.WorkingHourDto;
import com.midea.pam.common.ctc.entity.CostCollection;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.OrganizationCustomDictExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBudgetAsset;
import com.midea.pam.common.ctc.entity.ProjectBudgetAssetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetAssetChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetAssetExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetFee;
import com.midea.pam.common.ctc.entity.ProjectBudgetFeeChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetFeeChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetFeeExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetHuman;
import com.midea.pam.common.ctc.entity.ProjectBudgetHumanChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetHumanChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetHumanExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterial;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetTarget;
import com.midea.pam.common.ctc.entity.ProjectBudgetTargetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetTargetChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetTargetExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravel;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravelChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravelChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravelExample;
import com.midea.pam.common.ctc.entity.ProjectBusinessRsChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectBusinessRsChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeaderExample;
import com.midea.pam.common.ctc.entity.ProjectMember;
import com.midea.pam.common.ctc.entity.ProjectMemberChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectMemberChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectMemberExample;
import com.midea.pam.common.ctc.entity.ProjectResourceRel;
import com.midea.pam.common.ctc.entity.ProjectResourceRelExample;
import com.midea.pam.common.ctc.entity.RdmResourcePlan;
import com.midea.pam.common.ctc.entity.RdmResourcePlanExample;
import com.midea.pam.common.ctc.vo.MaterialStatisticsVO;
import com.midea.pam.common.ctc.vo.ProjectBudgetChangeVO;
import com.midea.pam.common.ctc.vo.ProjectBudgetFeeChangeHistoryVO;
import com.midea.pam.common.ctc.vo.ProjectBudgetMaterialChangeHistoryVO;
import com.midea.pam.common.ctc.vo.ProjectCostVO;
import com.midea.pam.common.enums.AttachmentStatus;
import com.midea.pam.common.enums.ChangeType;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CtcProjectChangeType;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProjectChangeStatus;
import com.midea.pam.common.enums.ProjectChangeType;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.OrganizationCustomDictMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetAssetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetAssetChangeSummaryHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetAssetMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetMaterialMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetTargetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetTargetMapper;
import com.midea.pam.ctc.mapper.ProjectBusinessRsChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.ProjectHistoryHeaderMapper;
import com.midea.pam.ctc.mapper.ProjectMemberChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectMemberMapper;
import com.midea.pam.ctc.mapper.ProjectResourceRelMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementExtMapper;
import com.midea.pam.ctc.mapper.RdmResourcePlanMapper;
import com.midea.pam.ctc.mapper.UnitExtMapper;
import com.midea.pam.ctc.mapper.WorkingHourExtMapper;
import com.midea.pam.ctc.project.service.ProjectBudgetAssetChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeService;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeSummaryHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetFeeChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetFeeChangeSummaryHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetHumanChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetHumanChangeSummaryHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetMaterialChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetMaterialChangeSummaryHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetTravelChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetTravelChangeSummaryHistoryService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CostCollectionService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.FeeCostDetailService;
import com.midea.pam.ctc.service.MaterialGetService;
import com.midea.pam.ctc.service.MaterialReturnService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.ProjectBudgetFeeService;
import com.midea.pam.ctc.service.ProjectBudgetHumanService;
import com.midea.pam.ctc.service.ProjectBudgetMaterialService;
import com.midea.pam.ctc.service.ProjectBudgetTravelService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.PurchaseOrderService;
import com.midea.pam.ctc.service.PurchaseProgressService;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-6-25
 * @description 预算变更
 */
public class ProjectBudgetChangeServiceImpl implements ProjectBudgetChangeService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectBudgetChangeSummaryHistoryService summaryHistoryService;
    @Resource
    private ProjectBudgetMaterialChangeHistoryService materialChangeHistoryService;
    @Resource
    private ProjectBudgetMaterialMapper projectBudgetMaterialMapper;
    @Resource
    private ProjectBudgetMaterialChangeSummaryHistoryService materialChangeSummaryHistoryService;
    @Resource
    private ProjectBudgetTargetChangeHistoryMapper projectBudgetTargetChangeHistoryMapper;
    @Resource
    private ProjectBudgetTargetMapper projectBudgetTargetMapper;
    @Resource
    private ProjectExtMapper projectExtMapper;
    @Resource
    private ProjectBudgetHumanChangeHistoryService humanChangeHistoryService;
    @Resource
    private ProjectBudgetHumanChangeSummaryHistoryService humanChangeSummaryHistoryService;
    @Resource
    private ProjectBudgetTravelChangeHistoryService travelChangeHistoryService;
    @Resource
    private ProjectBudgetTravelChangeSummaryHistoryService travelChangeSummaryHistoryService;
    @Resource
    private ProjectBudgetFeeChangeHistoryService feeChangeHistoryService;
    @Resource
    private ProjectBudgetFeeChangeSummaryHistoryService feeChangeSummaryHistoryService;
    @Resource
    private ProjectBudgetChangeSummaryHistoryService changeSummaryHistoryService;
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectBudgetMaterialService projectBudgetMaterialService;
    @Resource
    private ProjectBudgetHumanService projectBudgetHumanService;
    @Resource
    private ProjectBudgetTravelService projectBudgetTravelService;
    @Resource
    private ProjectBudgetFeeService projectBudgetFeeService;
    @Resource
    private ProjectBudgetChangeSummaryHistoryService projectBudgetChangeSummaryHistoryService;
    @Resource
    private CostCollectionService costCollectionService;
    @Resource
    private FeeCostDetailService feeCostDetailService;
    @Resource
    private ProjectHistoryHeaderMapper projectHistoryHeaderMapper;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private PurchaseProgressService purchaseProgressService;
    @Resource
    private MaterialGetService materialGetService;
    @Resource
    private MaterialReturnService materialReturnService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ProjectBudgetChangeHelper projectBudgetChangeHelper;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private ProjectMemberMapper projectMemberMapper;
    @Resource
    private WorkingHourExtMapper workingHourExtMapper;
    @Resource
    private ProjectResourceRelMapper projectResourceRelMapper;
    @Resource
    private RdmResourcePlanMapper rdmResourcePlanMapper;
    @Resource
    private ProjectMemberChangeHistoryMapper projectMemberChangeHistoryMapper;
    @Resource
    private ProjectBusinessRsChangeHistoryMapper projectBusinessRsChangeHistoryMapper;
    @Resource
    private UnitExtMapper unitExtMapper;
    @Resource
    private OrganizationCustomDictMapper organizationCustomDictMapper;
    @Resource
    private PurchaseMaterialRequirementExtMapper purchaseMaterialRequirementExtMapper;
    @Resource
    private ProjectBudgetAssetMapper projectBudgetAssetMapper;
    @Resource
    private ProjectBudgetAssetChangeHistoryMapper projectBudgetAssetChangeHistoryMapper;
    @Resource
    private ProjectBudgetAssetChangeHistoryService projectBudgetAssetChangeHistoryService;
    @Resource
    private ProjectBudgetAssetChangeSummaryHistoryMapper projectBudgetAssetChangeSummaryHistoryMapper;
    @Resource
    private BasedataExtService basedataExtService;


    /**
     * 预算变更规则：
     * 第一次变更后，提交审批，新建header记录；
     * 第一次变更后，保存未提交审批，再编辑，再保存，则失效上次提交数据，新建此次提交记录（变更ID不变）
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long budgetChange(ProjectBudgetChangeDTO projectBudgetChangeDTO) {
        budgetChangeParamCheck(projectBudgetChangeDTO);

        final Long projectId = projectBudgetChangeDTO.getProjectId();
        final Project project = projectService.selectByPrimaryKey(projectId);
        Assert.notNull(project, "项目不存在");

        authCheck(project);
        //二次检验物料预算能否删除
        projectBusinessService.checkMaterialsCanDelete(projectId, BeanConverter.copy(projectBudgetChangeDTO.getProjectBudgetMaterialChangeHistories(), ProjectBudgetMaterialDto.class));
        //二次检验非差旅预算能否删除
        projectBusinessService.checkFeeCanDelete(projectId, BeanConverter.copy(projectBudgetChangeDTO.getProjectBudgetFeeChangeHistories(), ProjectBudgetFeeDto.class));

        final Long newHeaderId = saveOrUpdateChangeRecord(projectBudgetChangeDTO);

        final Integer type = projectBudgetChangeDTO.getType();

        final List<ProjectBudgetMaterialChangeHistoryDTO> projectBudgetMaterialChangeHistoryList = projectBudgetChangeDTO.getProjectBudgetMaterialChangeHistories();
        final List<ProjectBudgetHumanChangeHistoryDto> projectBudgetHumanChangeHistories = projectBudgetChangeDTO.getProjectBudgetHumanChangeHistories();
        final List<ProjectMemberChangeHistoryDto> projectMemberChangeHistories = projectBudgetChangeDTO.getProjectMemberChangeHistories();
        final List<ProjectBudgetTravelChangeHistory> projectBudgetTravelChangeHistories = projectBudgetChangeDTO.getProjectBudgetTravelChangeHistories();
        final List<ProjectBudgetFeeChangeHistory> projectBudgetFeeChangeHistories = projectBudgetChangeDTO.getProjectBudgetFeeChangeHistories();
        final List<ProjectBudgetAssetChangeHistory> projectBudgetAssetChangeHistories = projectBudgetChangeDTO.getProjectBudgetAssetChangeHistories();

        // 变更后提交需要特殊处理，将正式表ID赋值到变更记录ID
        if (Objects.equals(2, type)) {
            if (ListUtils.isNotEmpty(projectBudgetMaterialChangeHistoryList)) {
                projectBudgetMaterialChangeHistoryList.forEach(history -> {
                    history.setId(history.getOriginId());

                    //递归处理子层
                    if (ListUtils.isNotEmpty(history.getSubHistories())) {
                        updateSubHistoryParentId(history);
                    }
                });
            }

            if (ListUtils.isNotEmpty(projectBudgetHumanChangeHistories)) {
                projectBudgetHumanChangeHistories.forEach(history -> {
                    history.setId(history.getOriginId());
                });
            }
            if (ListUtils.isNotEmpty(projectMemberChangeHistories)) {
                projectMemberChangeHistories.forEach(history -> {
                    history.setId(history.getOriginId());
                });
            }
            if (ListUtils.isNotEmpty(projectBudgetTravelChangeHistories)) {
                projectBudgetTravelChangeHistories.forEach(history -> {
                    history.setId(history.getOriginId());
                });
            }
            if (ListUtils.isNotEmpty(projectBudgetFeeChangeHistories)) {
                projectBudgetFeeChangeHistories.forEach(history -> {
                    history.setId(history.getOriginId());
                });
            }
            if (ListUtils.isNotEmpty(projectBudgetAssetChangeHistories)) {
                projectBudgetAssetChangeHistories.forEach(history -> {
                    history.setId(history.getOriginId());
                });
            }
        }

        // 物料变更记录保存
        materialChangeSave(projectBudgetMaterialChangeHistoryList, projectId, newHeaderId);
        // 物料变更汇总记录保存
        final ProjectBudgetMaterialChangeSummaryHistory projectBudgetMaterialChangeSummaryHistory = projectBudgetChangeDTO.getProjectBudgetMaterialChangeSummaryHistory();
        projectBudgetMaterialChangeSummaryHistory.setProjectId(projectId);
        projectBudgetMaterialChangeSummaryHistory.setHeaderId(newHeaderId);
        projectBudgetMaterialChangeSummaryHistory.setDeletedFlag(Boolean.FALSE);
        materialSummarySave(projectBudgetMaterialChangeSummaryHistory);

        // 人力变更记录保存
        humanChangeSave(projectBudgetHumanChangeHistories, projectId, newHeaderId);
        // 资源计划关联关系记录保存/rdm工时检查
        //projectResourceRelSave(projectMemberChangeHistories, project, newHeaderId);
        // 人力变更汇总记录保存
        final ProjectBudgetHumanChangeSummaryHistory projectBudgetHumanChangeSummaryHistory = projectBudgetChangeDTO.getProjectBudgetHumanChangeSummaryHistory();
        humanSummaryChangeSave(projectBudgetHumanChangeSummaryHistory, projectId, newHeaderId);

        // 差旅预算记录保存
        travelChangeSave(projectBudgetTravelChangeHistories, projectId, newHeaderId);
        // 差旅预算汇总记录保存
        final ProjectBudgetTravelChangeSummaryHistory projectBudgetTravelChangeSummaryHistory = projectBudgetChangeDTO.getProjectBudgetTravelChangeSummaryHistory();
        travelSummaryChangeSave(projectBudgetTravelChangeSummaryHistory, projectId, newHeaderId);

        // 项目费用预算记录保存
        feeChangeSave(projectBudgetFeeChangeHistories, projectId, newHeaderId);
        // 项目费用预算汇总记录保存
        final ProjectBudgetFeeChangeSummaryHistory projectBudgetFeeChangeSummaryHistory = projectBudgetChangeDTO.getProjectBudgetFeeChangeSummaryHistory();
        feeSummaryChangeSave(projectBudgetFeeChangeSummaryHistory, projectId, newHeaderId);

        // 项目资产预算记录保存
        assetChangeSave(projectBudgetAssetChangeHistories, projectId, newHeaderId);
        // 项目资产预算汇总记录保存
        final ProjectBudgetAssetChangeSummaryHistory projectBudgetAssetChangeSummaryHistory = projectExtMapper.queryAssetChangeSummaryHistory(projectId, newHeaderId);
        assetSummaryChangeSave(projectBudgetAssetChangeSummaryHistory, projectId, newHeaderId);

        // 项目变更汇总记录保存
        final ProjectBudgetChangeSummaryHistory projectBudgetChangeSummaryHistory = projectBudgetChangeDTO.getProjectBudgetChangeSummaryHistory();
        //记录当时的项目金额,数字看板查变更记录需要用
        projectBudgetChangeSummaryHistory.setExcludingTaxAmount(project.getAmount());
        budgetSummaryChangeSave(projectBudgetChangeSummaryHistory, projectId, newHeaderId);

        // 目标成本变更记录保存
        changeTargetCostByBudget(projectBudgetChangeDTO);

        // 商机报价单变更记录保存
        ProjectDto projectDto = new ProjectDto();
        projectDto.setId(projectId);
        projectDto.setProjectBusinessRsList(projectBudgetChangeDTO.getProjectBusinessRsList());
        projectBusinessService.saveBusinessRsChangeHistory(projectDto, projectHistoryHeaderMapper.selectByPrimaryKey(newHeaderId));

        //保存附件信息
        this.saveCtcAttachmentDtos(projectBudgetChangeDTO.getAttachmentDtos(), CtcAttachmentModule.PROJECT_BUDGET_CHANGE.code(), newHeaderId);

        return newHeaderId;
    }

    private void updateSubHistoryParentId(ProjectBudgetMaterialChangeHistoryDTO history) {
        final List<ProjectBudgetMaterialChangeHistoryDTO> subHistories = history.getSubHistories();
        subHistories.forEach(subHistory -> {
            subHistory.setId(subHistory.getOriginId());
            subHistory.setParentId(history.getOriginId());

            if (ListUtils.isNotEmpty(subHistory.getSubHistories())) {
                updateSubHistoryParentId(subHistory);
            }
        });
    }

    /**
     * 转正预算变更信息保存
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long budgetPreviewChange(ProjectDto projectDto, Long headerId) {
        final Long projectId = projectDto.getId();
        final Project project = projectService.selectByPrimaryKey(projectId);
        Assert.notNull(project, "项目不存在");

        final List<ProjectBudgetMaterialChangeHistoryDTO> projectBudgetMaterialChangeHistoryList = BeanConverter.copy(projectDto.getMaterials(), ProjectBudgetMaterialChangeHistoryDTO.class);
        final List<ProjectBudgetHumanChangeHistoryDto> projectBudgetHumanChangeHistories = BeanConverter.copy(projectDto.getHumans(), ProjectBudgetHumanChangeHistoryDto.class);
        final List<ProjectMemberChangeHistoryDto> projectMemberChangeHistories = BeanConverter.copy(projectDto.getMembers(), ProjectMemberChangeHistoryDto.class);
        final List<ProjectBudgetTravelChangeHistory> projectBudgetTravelChangeHistories = BeanConverter.copy(projectDto.getTravels(), ProjectBudgetTravelChangeHistory.class);
        final List<ProjectBudgetFeeChangeHistory> projectBudgetFeeChangeHistories = BeanConverter.copy(projectDto.getFees(), ProjectBudgetFeeChangeHistory.class);
        final List<ProjectBudgetAssetChangeHistory> projectBudgetAssetChangeHistories = BeanConverter.copy(projectDto.getAssets(), ProjectBudgetAssetChangeHistory.class);

        // 转换子级元素,兼容预算变更结构
        changeChildrentoSubHistories(projectBudgetMaterialChangeHistoryList);

        // 物料变更记录保存
        materialChangeSave(projectBudgetMaterialChangeHistoryList, projectId, headerId);
        // 物料变更汇总记录保存
        final ProjectBudgetMaterialChangeSummaryHistory materialChangeSummaryHistory = projectExtMapper.queryMaterialChangeSummaryHistory(projectId, headerId);
        materialSummarySave(materialChangeSummaryHistory);

        // 人力变更记录保存
        humanChangeSave(projectBudgetHumanChangeHistories, projectId, headerId);
        // 资源计划关联关系记录保存/rdm工时检查
        projectResourceRelSave(projectMemberChangeHistories, project, headerId);
        // 人力变更汇总记录保存
        final ProjectBudgetHumanChangeSummaryHistory projectBudgetHumanChangeSummaryHistory = projectExtMapper.queryHumanChangeSummaryHistory(projectId, headerId);
        humanSummaryChangeSave(projectBudgetHumanChangeSummaryHistory, projectId, headerId);

        // 差旅预算记录保存
        travelChangeSave(projectBudgetTravelChangeHistories, projectId, headerId);
        // 差旅预算汇总记录保存
        final ProjectBudgetTravelChangeSummaryHistory projectBudgetTravelChangeSummaryHistory = projectExtMapper.queryTravelChangeSummaryHistory(projectId, headerId);
        travelSummaryChangeSave(projectBudgetTravelChangeSummaryHistory, projectId, headerId);

        // 项目费用预算记录保存
        feeChangeSave(projectBudgetFeeChangeHistories, projectId, headerId);
        // 项目费用预算汇总记录保存
        final ProjectBudgetFeeChangeSummaryHistory projectBudgetFeeChangeSummaryHistory = projectExtMapper.queryFeeChangeSummaryHistory(projectId, headerId);
        feeSummaryChangeSave(projectBudgetFeeChangeSummaryHistory, projectId, headerId);

        // 项目资产预算记录保存
        assetChangeSave(projectBudgetAssetChangeHistories, projectId, headerId);
        // 项目资产预算汇总记录保存
        final ProjectBudgetAssetChangeSummaryHistory projectBudgetAssetChangeSummaryHistory = projectExtMapper.queryAssetChangeSummaryHistory(projectId, headerId);
        assetSummaryChangeSave(projectBudgetAssetChangeSummaryHistory, projectId, headerId);

        // 项目变更汇总记录保存
        final ProjectBudgetChangeSummaryHistory projectBudgetChangeSummaryHistory = projectExtMapper.queryChangeSummaryHistory(projectId, headerId);
        budgetSummaryChangeSave(projectBudgetChangeSummaryHistory, projectId, headerId);

        // 转正后预算
        projectDto.setBudgetCost(projectBudgetChangeSummaryHistory.getAfterAmount());
        return headerId;
    }

    private void changeChildrentoSubHistories(List<ProjectBudgetMaterialChangeHistoryDTO> materialChangeHistoryDTOS) {
        if (ListUtils.isNotEmpty(materialChangeHistoryDTOS)) {
            // 变更后物料记录
            materialChangeHistoryDTOS.forEach(history -> {

                if (ListUtils.isNotEmpty(history.getChildren())) {
                    history.setSubHistories(BeanConverter.copy(history.getChildren(), ProjectBudgetMaterialChangeHistoryDTO.class));
                    changeChildrentoSubHistories(history.getSubHistories());
                }
            });
        }
    }

    private void setMaterialChangeDetailInfo(ProjectBudgetChangeDTO projectBudgetChangeDTO) {
        // 变更后物料记录
        List<ProjectBudgetMaterialChangeHistoryDTO> materialChangeHistoryList = projectBudgetChangeDTO.getProjectBudgetMaterialChangeHistories();
        // 外购物料预算汇总
        BigDecimal extPriceTotal = BigDecimal.ZERO;
        // 外购物料预算变更差额
        BigDecimal extPriceTotalChange = BigDecimal.ZERO;

        // 统计外购物料预算金额
        for (ProjectBudgetMaterialChangeHistoryDTO change : materialChangeHistoryList) {
            if (change.getId() == null) {
                return;
            }
            ProjectBudgetMaterial before = projectBudgetMaterialMapper.selectByPrimaryKey(change.getId());
            if (before == null) {
                return;
            }

            BigDecimal afterExtPriceTotal = change.getExtPriceTotal() != null ? change.getExtPriceTotal() : BigDecimal.ZERO;
            BigDecimal beforeExtPriceTotal = before.getExtPriceTotal() != null ? before.getExtPriceTotal() : BigDecimal.ZERO;
            extPriceTotal = extPriceTotal.add(afterExtPriceTotal);

            extPriceTotalChange = extPriceTotalChange.add(afterExtPriceTotal.subtract(beforeExtPriceTotal));
        }

        projectBudgetChangeDTO.setMaterialOuterAfterAmount(extPriceTotal);
        projectBudgetChangeDTO.setMaterialOuterChangeAmount(extPriceTotalChange);
        // 物料预算变更差额
        BigDecimal materialChangeSummary = projectBudgetChangeDTO.getProjectBudgetMaterialChangeSummaryHistory().getChangeAmount();
        materialChangeSummary = materialChangeSummary != null ? materialChangeSummary : BigDecimal.ZERO;
        // 自制预算变更差额 = 物料预算变更差额 - 外购物料预算变更差额
        projectBudgetChangeDTO.setMaterialInnerChangeAmount(materialChangeSummary.subtract(extPriceTotalChange));
        // 自制物料变更后预算 = 物料变更后总预算 - 外购物料变更后预算
        projectBudgetChangeDTO.setMaterialInnerAfterAmount(projectBudgetChangeDTO.getProjectBudgetMaterialChangeSummaryHistory().getAfterAmount().subtract(projectBudgetChangeDTO.getMaterialOuterAfterAmount()));
    }

    private void changeTargetCostByBudget(ProjectBudgetChangeDTO projectBudgetChangeDTO) {
        ProjectBudgetTargetExample example = new ProjectBudgetTargetExample();
        example.createCriteria().andProjectIdEqualTo(projectBudgetChangeDTO.getProjectId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectBudgetTarget> budgetTargetList = projectBudgetTargetMapper.selectByExample(example);
        if (ListUtils.isEmpty(budgetTargetList)) {
            return;
        }

        // 变更前变更明细
        ProjectBudgetTargetChangeHistory history = new ProjectBudgetTargetChangeHistory();
        // 变更后变更明细
        ProjectBudgetTargetChangeHistory change = new ProjectBudgetTargetChangeHistory();
        // 原目标成本
        ProjectBudgetTarget originTarget = budgetTargetList.get(0);

        // 保存变更前目标成本数据
        BeanUtils.copyProperties(originTarget, history);
        BeanUtils.copyProperties(originTarget, change);
        history.setId(null);
        history.setHistoryType(HistoryType.HISTORY.getCode());
        history.setOriginId(originTarget.getId());
        history.setHeaderId(projectBudgetChangeDTO.getHeaderId());
        history.setDeletedFlag(Boolean.FALSE);
        projectBudgetTargetChangeHistoryMapper.insertSelective(history);

        // 保存变更后的目标成本数据
        change.setId(null);
        change.setHistoryType(HistoryType.CHANGE.getCode());
        change.setOriginId(originTarget.getId());
        change.setHeaderId(projectBudgetChangeDTO.getHeaderId());
        change.setDeletedFlag(Boolean.FALSE);

        // 查询自制物料/外购物料变更后预算
        setMaterialChangeDetailInfo(projectBudgetChangeDTO);

        if (Objects.equals(projectBudgetChangeDTO.getReasonClassification(), ProjectChangeType.INNER.msg())) {
            // 内部调整，最新内部调整预算 = 原内部调整预算 + 调整差额
            // 内部调整预算
            BigDecimal innerAdjustTotalTargetBudget = change.getInnerAdjustTotalTargetBudget() != null ? change.getInnerAdjustTotalTargetBudget() :
                    BigDecimal.ZERO;
            change.setInnerAdjustTotalTargetBudget(innerAdjustTotalTargetBudget.add(projectBudgetChangeDTO.getProjectBudgetChangeSummaryHistory().getChangeAmount()));

            // 内部调整物料预算
            BigDecimal innerAdjustMaterialTargetBudget = change.getInnerAdjustMaterialTargetBudget() != null ?
                    change.getInnerAdjustMaterialTargetBudget() : BigDecimal.ZERO;
            change.setInnerAdjustTotalTargetBudget(innerAdjustMaterialTargetBudget.add(projectBudgetChangeDTO.getProjectBudgetMaterialChangeSummaryHistory().getChangeAmount()));

            // 内部调整物料-自制预算
            BigDecimal innerAdjustMaterialSelfTargetBudget = change.getInnerAdjustMaterialSelfTargetBudget() != null ?
                    change.getInnerAdjustMaterialSelfTargetBudget() : BigDecimal.ZERO;
            change.setInnerAdjustTotalTargetBudget(innerAdjustMaterialSelfTargetBudget.add(projectBudgetChangeDTO.getMaterialInnerChangeAmount()));

            //内部调整物料-外购预算
            BigDecimal innerAdjustMaterialOutTargetBudget = change.getInnerAdjustMaterialOutTargetBudget() != null ?
                    change.getInnerAdjustMaterialOutTargetBudget() : BigDecimal.ZERO;
            change.setInnerAdjustMaterialOutTargetBudget(innerAdjustMaterialOutTargetBudget.add(projectBudgetChangeDTO.getMaterialOuterChangeAmount()));

            // 内部调整人力预算
            BigDecimal innerAdjustHumanTargetBudget = change.getInnerAdjustHumanTargetBudget() != null ? change.getInnerAdjustHumanTargetBudget() :
                    BigDecimal.ZERO;
            change.setInnerAdjustHumanTargetBudget(innerAdjustHumanTargetBudget.add(projectBudgetChangeDTO.getProjectBudgetHumanChangeSummaryHistory().getChangeAmount()));

            // 内部调整差旅预算
            BigDecimal innerAdjustTravelTargetBudget = change.getInnerAdjustTravelTargetBudget() != null ?
                    change.getInnerAdjustTravelTargetBudget() : BigDecimal.ZERO;
            change.setInnerAdjustTravelTargetBudget(innerAdjustTravelTargetBudget.add(projectBudgetChangeDTO.getProjectBudgetTravelChangeSummaryHistory().getChangeAmount()));

            // 内部调整其它费用预算
            BigDecimal innerAdjustOtherTargetBudget = change.getInnerAdjustOtherTargetBudget() != null ? change.getInnerAdjustOtherTargetBudget() :
                    BigDecimal.ZERO;
            change.setInnerAdjustOtherTargetBudget(innerAdjustOtherTargetBudget.add(projectBudgetChangeDTO.getProjectBudgetFeeChangeSummaryHistory().getChangeAmount()));

        } else if (Objects.equals(projectBudgetChangeDTO.getReasonClassification(), ProjectChangeType.OUTTER.msg())) {
            // 客户原因增补合同，最新增补合同整预算 = 原增补合同调整预算 + 调整差额
            // 增补合同调整预算
            BigDecimal outerAdjustTotalTargetBudget = change.getOuterAdjustTotalTargetBudget() != null ? change.getOuterAdjustTotalTargetBudget() :
                    BigDecimal.ZERO;
            change.setOuterAdjustTotalTargetBudget(outerAdjustTotalTargetBudget.add(projectBudgetChangeDTO.getProjectBudgetChangeSummaryHistory().getChangeAmount()));

            // 增补合同调整物料预算
            BigDecimal outerAdjustMaterialTargetBudget = change.getOuterAdjustMaterialTargetBudget() != null ?
                    change.getOuterAdjustMaterialTargetBudget() : BigDecimal.ZERO;
            change.setOuterAdjustTotalTargetBudget(outerAdjustMaterialTargetBudget.add(projectBudgetChangeDTO.getProjectBudgetMaterialChangeSummaryHistory().getChangeAmount()));

            // 增补合同调整物料-自制预算
            BigDecimal outerAdjustMaterialSelfTargetBudget = change.getOuterAdjustMaterialSelfTargetBudget() != null ?
                    change.getOuterAdjustMaterialSelfTargetBudget() : BigDecimal.ZERO;
            change.setOuterAdjustTotalTargetBudget(outerAdjustMaterialSelfTargetBudget.add(projectBudgetChangeDTO.getMaterialInnerChangeAmount()));

            //增补合同调整物料-外购预算
            BigDecimal outerAdjustMaterialOutTargetBudget = change.getOuterAdjustMaterialOutTargetBudget() != null ?
                    change.getOuterAdjustMaterialOutTargetBudget() : BigDecimal.ZERO;
            change.setOuterAdjustMaterialOutTargetBudget(outerAdjustMaterialOutTargetBudget.add(projectBudgetChangeDTO.getMaterialOuterChangeAmount()));

            // 增补合同调整人力预算
            BigDecimal outerAdjustHumanTargetBudget = change.getOuterAdjustHumanTargetBudget() != null ? change.getOuterAdjustHumanTargetBudget() :
                    BigDecimal.ZERO;
            change.setOuterAdjustHumanTargetBudget(outerAdjustHumanTargetBudget.add(projectBudgetChangeDTO.getProjectBudgetHumanChangeSummaryHistory().getChangeAmount()));

            // 增补合同调整差旅预算
            BigDecimal outerAdjustTravelTargetBudget = change.getOuterAdjustTravelTargetBudget() != null ?
                    change.getOuterAdjustTravelTargetBudget() : BigDecimal.ZERO;
            change.setOuterAdjustTravelTargetBudget(outerAdjustTravelTargetBudget.add(projectBudgetChangeDTO.getProjectBudgetTravelChangeSummaryHistory().getChangeAmount()));

            // 增补合同调整其它费用预算
            BigDecimal outerAdjustOtherTargetBudget = change.getOuterAdjustOtherTargetBudget() != null ? change.getOuterAdjustOtherTargetBudget() :
                    BigDecimal.ZERO;
            change.setOuterAdjustOtherTargetBudget(outerAdjustOtherTargetBudget.add(projectBudgetChangeDTO.getProjectBudgetFeeChangeSummaryHistory().getChangeAmount()));
        }

        // 如果设置了同步变更目标成本明细
        if (Objects.equals(Boolean.TRUE, projectBudgetChangeDTO.getBudgetTargetFlag())) {
            // 项目立项/转正初始目标总成本
            BigDecimal initTotalTargetCost = change.getInitTotalTargetCost() != null ? change.getInitTotalTargetCost() : BigDecimal.ZERO;
            // 项目立项/转正初始物料目标成本
            BigDecimal initMaterialTargetCost = change.getInitMaterialTargetCost() != null ? change.getInitMaterialTargetCost() : BigDecimal.ZERO;
            // 项目立项/转正初始物料目标成本-外购
            BigDecimal initMaterialOutTargetCost = change.getInitMaterialOutTargetCost() != null ? change.getInitMaterialOutTargetCost() :
                    BigDecimal.ZERO;
            // 项目立项/转正初始物料目标成本-自制
            BigDecimal initMaterialSelfTargetCost = change.getInitMaterialSelfTargetCost() != null ? change.getInitMaterialSelfTargetCost() :
                    BigDecimal.ZERO;
            // 项目立项/转正初始其它费用目标成本
            BigDecimal initOtherTargetCost = change.getInitOtherTargetCost() != null ? change.getInitOtherTargetCost() : BigDecimal.ZERO;
            // 项目立项/转正初始差旅目标成本
            BigDecimal initTravelTargetCost = change.getInitTravelTargetCost() != null ? change.getInitTravelTargetCost() : BigDecimal.ZERO;
            // 项目立项/转正初始人力目标成本
            BigDecimal initHumanTargetCost = change.getInitHumanTargetCost() != null ? change.getInitHumanTargetCost() : BigDecimal.ZERO;

            // 变更后-物料/人力/差旅/其它费用目标成本调整为对应的变更后的预算
            change.setCurrentTotalTargetCost(projectBudgetChangeDTO.getProjectBudgetChangeSummaryHistory().getAfterAmount());
            change.setCurrentMaterialTargetCost(projectBudgetChangeDTO.getProjectBudgetMaterialChangeSummaryHistory().getAfterAmount());
            change.setCurrentMaterialOutTargetCost(projectBudgetChangeDTO.getMaterialOuterAfterAmount());
            change.setCurrentMaterialSelfTargetCost(projectBudgetChangeDTO.getMaterialInnerAfterAmount());
            change.setCurrentTravelTargetCost(projectBudgetChangeDTO.getProjectBudgetTravelChangeSummaryHistory().getAfterAmount());
            change.setCurrentOtherTargetCost(projectBudgetChangeDTO.getProjectBudgetFeeChangeSummaryHistory().getAfterAmount());
            change.setCurrentHumanTargetCost(projectBudgetChangeDTO.getProjectBudgetHumanChangeSummaryHistory().getAfterAmount());

            if (Objects.equals(projectBudgetChangeDTO.getReasonClassification(), ProjectChangeType.INNER.msg())) {
                //内部调整目标成本=变更后目标成本-初始目标成本-增补合同调整目标成本

                // 内部调整目标成本
                BigDecimal outerAdjustTotalTargetCost = change.getOuterAdjustTotalTargetCost() != null ? change.getOuterAdjustTotalTargetCost() :
                        BigDecimal.ZERO;
                change.setInnerAdjustTotalTargetCost(change.getCurrentTotalTargetCost().subtract(initTotalTargetCost).subtract(outerAdjustTotalTargetCost));

                // 内部调整物料目标成本
                BigDecimal outerAdjustMaterialTargetCost = change.getInnerAdjustMaterialTargetCost() != null ?
                        change.getInnerAdjustMaterialTargetCost() : BigDecimal.ZERO;
                change.setInnerAdjustTotalTargetCost(change.getCurrentMaterialTargetCost().subtract(initMaterialTargetCost).subtract(outerAdjustMaterialTargetCost));

                // 内部调整物料-自制目标成本
                BigDecimal outerAdjustMaterialSelfTargetCost = change.getOuterAdjustMaterialSelfTargetCost() != null ?
                        change.getOuterAdjustMaterialSelfTargetCost() : BigDecimal.ZERO;
                change.setInnerAdjustTotalTargetCost(change.getCurrentMaterialSelfTargetCost().subtract(initMaterialSelfTargetCost).subtract(outerAdjustMaterialSelfTargetCost));

                //内部调整物料-外购目标成本
                BigDecimal outerAdjustMaterialOutTargetCost = change.getOuterAdjustMaterialOutTargetCost() != null ?
                        change.getOuterAdjustMaterialOutTargetCost() : BigDecimal.ZERO;
                change.setInnerAdjustMaterialOutTargetCost(change.getCurrentMaterialOutTargetCost().subtract(initMaterialOutTargetCost).subtract(outerAdjustMaterialOutTargetCost));

                // 内部调整人力目标成本
                BigDecimal outerAdjustHumanTargetCost = change.getOuterAdjustHumanTargetCost() != null ? change.getOuterAdjustHumanTargetCost() :
                        BigDecimal.ZERO;
                change.setInnerAdjustHumanTargetCost(change.getCurrentHumanTargetCost().subtract(initHumanTargetCost).subtract(outerAdjustHumanTargetCost));

                // 内部调整差旅目标成本
                BigDecimal outerAdjustTravelTargetCost = change.getOuterAdjustTravelTargetCost() != null ? change.getOuterAdjustTravelTargetCost()
                        : BigDecimal.ZERO;
                change.setInnerAdjustTravelTargetCost(change.getCurrentTravelTargetCost().subtract(initTravelTargetCost).subtract(outerAdjustTravelTargetCost));

                // 内部调整其它费用目标成本
                BigDecimal outerAdjustOtherTargetCost = change.getOuterAdjustOtherTargetCost() != null ? change.getOuterAdjustOtherTargetCost() :
                        BigDecimal.ZERO;
                change.setInnerAdjustOtherTargetCost(change.getCurrentOtherTargetCost().subtract(initOtherTargetCost).subtract(outerAdjustOtherTargetCost));

            } else if (Objects.equals(projectBudgetChangeDTO.getReasonClassification(), ProjectChangeType.OUTTER.msg())) {
                //增补合同调整目标成本=变更后目标成本-初始目标成本-内部调整目标成本

                // 增补合同调整目标成本
                BigDecimal innerAdjustTotalTargetCost = change.getInnerAdjustTotalTargetCost() != null ? change.getInnerAdjustTotalTargetCost() :
                        BigDecimal.ZERO;
                change.setOuterAdjustTotalTargetCost(change.getCurrentTotalTargetCost().subtract(initTotalTargetCost).subtract(innerAdjustTotalTargetCost));

                // 增补合同调整物料目标成本
                BigDecimal innerAdjustMaterialTargetCost = change.getInnerAdjustMaterialTargetCost() != null ?
                        change.getInnerAdjustMaterialTargetCost() : BigDecimal.ZERO;
                change.setOuterAdjustTotalTargetCost(change.getCurrentMaterialTargetCost().subtract(initMaterialTargetCost).subtract(innerAdjustMaterialTargetCost));

                // 增补合同调整物料-自制目标成本
                BigDecimal innerAdjustMaterialSelfTargetCost = change.getInnerAdjustMaterialSelfTargetCost() != null ?
                        change.getInnerAdjustMaterialSelfTargetCost() : BigDecimal.ZERO;
                change.setOuterAdjustTotalTargetCost(change.getCurrentMaterialSelfTargetCost().subtract(initMaterialSelfTargetCost).subtract(innerAdjustMaterialSelfTargetCost));

                //增补合同调整物料-外购目标成本
                BigDecimal innerAdjustMaterialOutTargetCost = change.getInnerAdjustMaterialOutTargetCost() != null ?
                        change.getInnerAdjustMaterialOutTargetCost() : BigDecimal.ZERO;
                change.setOuterAdjustMaterialOutTargetCost(change.getCurrentMaterialOutTargetCost().subtract(initMaterialOutTargetCost).subtract(innerAdjustMaterialOutTargetCost));

                // 增补合同调整人力目标成本
                BigDecimal innerAdjustHumanTargetCost = change.getInnerAdjustHumanTargetCost() != null ? change.getInnerAdjustHumanTargetCost() :
                        BigDecimal.ZERO;
                change.setOuterAdjustHumanTargetCost(change.getCurrentHumanTargetCost().subtract(initHumanTargetCost).subtract(innerAdjustHumanTargetCost));

                // 增补合同调整差旅目标成本
                BigDecimal innerAdjustTravelTargetCost = change.getInnerAdjustTravelTargetCost() != null ? change.getInnerAdjustTravelTargetCost()
                        : BigDecimal.ZERO;
                change.setOuterAdjustTravelTargetCost(change.getCurrentTravelTargetCost().subtract(initTravelTargetCost).subtract(innerAdjustTravelTargetCost));

                // 增补合同调整其它费用目标成本
                BigDecimal innerAdjustOtherTargetCost = change.getInnerAdjustOtherTargetCost() != null ? change.getInnerAdjustOtherTargetCost() :
                        BigDecimal.ZERO;
                change.setOuterAdjustOtherTargetCost(change.getCurrentOtherTargetCost().subtract(initOtherTargetCost).subtract(innerAdjustOtherTargetCost));
            }
        }
        projectBudgetTargetChangeHistoryMapper.insertSelective(change);
    }

    @Override
    public JSONArray getBudgetChangeWorkingHoursSummary(ProjectBudgetChangeDTO projectBudgetChangeDTO) {
        List<WorkingHourDto> workingHourDtoList = getBudgetChangeWorkingHoursDetail(projectBudgetChangeDTO);
        Map<String, WorkingHourDto> userMap = new HashMap<>();
        //根据月份汇总
        for (WorkingHourDto workingHourDto : workingHourDtoList) {
            WorkingHourDto summary = userMap.get(workingHourDto.getUserId() + DateUtils.format(DateUtils.parse(workingHourDto.getApplyDate()),
                    DateUtils.FORMAT_YEAR_MONTH));
            if (summary == null) {
                summary = new WorkingHourDto();
                summary.setApplyDate(DateUtils.format(DateUtils.parse(workingHourDto.getApplyDate()), DateUtils.FORMAT_YEAR_MONTH));
                summary.setActualWorkingHours(workingHourDto.getActualWorkingHours());
                summary.setCarryWorkingHours(workingHourDto.getCarryWorkingHours());
                summary.setUserName(workingHourDto.getUserName());
                summary.setMipName(workingHourDto.getMipName());
                userMap.put(workingHourDto.getUserId() + DateUtils.format(DateUtils.parse(workingHourDto.getApplyDate()),
                        DateUtils.FORMAT_YEAR_MONTH), summary);
            } else {
                summary.setActualWorkingHours(Float.sum(summary.getActualWorkingHours(), workingHourDto.getActualWorkingHours()));
                summary.setCarryWorkingHours(summary.getCarryWorkingHours().add(workingHourDto.getCarryWorkingHours()));
            }
        }
        JSONArray array = new JSONArray();
        userMap.forEach((k, v) -> {
            String str = JSON.toJSONString(v);
            array.add(JSON.parseObject(str));
        });

        return array;
    }

    @Override
    public List<WorkingHourDto> getBudgetChangeWorkingHoursDetail(ProjectBudgetChangeDTO projectBudgetChangeDTO) {
        List<WorkingHourDto> workingHourDtoList = new ArrayList<>();
        // 变更后的人员投入区间
        List<ProjectMemberChangeHistoryDto> memberChangeHistoryDtoList = projectBudgetChangeDTO.getProjectMemberChangeHistories();
        if (ListUtils.isEmpty(memberChangeHistoryDtoList)) {
            return new ArrayList<>();
        }
        // 按人分组
        Map<Long, List<ProjectMemberChangeHistoryDto>> memberChangeHistoryMap =
                memberChangeHistoryDtoList.stream().collect(Collectors.groupingBy(memberChangeHistoryDto -> memberChangeHistoryDto.getUserId()));
        Set<Map.Entry<Long, List<ProjectMemberChangeHistoryDto>>> entries = memberChangeHistoryMap.entrySet();
        // 查询预算变更影响的审批通过工时明细
        for (Map.Entry<Long, List<ProjectMemberChangeHistoryDto>> entry : entries) {
            List<ProjectMemberChangeHistoryDto> changeHistoryDtoList = entry.getValue();
            ProjectMemberChangeHistoryDto changeHistoryDto = changeHistoryDtoList.get(0);
            if (changeHistoryDto.getIsFromResource() == null || changeHistoryDto.getIsFromResource() == Boolean.FALSE) {
                // 非rdm引入无需自动冲销
                continue;
            }
            // 查询所有审批通过的工时（RDM）
            List<WorkingHourDto> approvedWorkingHourDtoList = workingHourExtMapper.getApprovedWorkingHours(changeHistoryDto.getProjectId(),
                    changeHistoryDto.getUserId());
            if (ListUtils.isEmpty(approvedWorkingHourDtoList)) continue;
            for (WorkingHourDto workingHourDto : approvedWorkingHourDtoList) {
                // 循环判断工时是否在最新的投入区间
                Boolean haveToWriteOff = checkBudgetChangeWorkingHour(changeHistoryDtoList, workingHourDto);
                //只统计被归集的数据并且不在最新的投入区间的工时
                if (workingHourDto.getLaborCostDetailId() != null && haveToWriteOff) {
                    UserInfo userInfo = CacheDataUtils.findUserById(workingHourDto.getUserId());
                    if (userInfo != null) {
                        workingHourDto.setUserName(userInfo.getName());
                        workingHourDto.setMipName(userInfo.getUsername());
                    }
                    workingHourDtoList.add(workingHourDto);
                }
            }
        }
        return workingHourDtoList;
    }

    /**
     * 判断工时是否在最新的投入区间
     *
     * @param changeHistoryDtoList 变更后的最新投入区间
     * @param workingHourDto       rdm引入工时
     * @return true-需要被冲销，即不在最新的投入区间
     */
    private Boolean checkBudgetChangeWorkingHour(List<ProjectMemberChangeHistoryDto> changeHistoryDtoList, WorkingHourDto workingHourDto) {
        for (ProjectMemberChangeHistoryDto changeHistoryDto : changeHistoryDtoList) {
            Date applyDate = DateUtil.parseDate(workingHourDto.getApplyDate());
            changeHistoryDto.setStartTime(DateUtil.parseDate(changeHistoryDto.getStartTimeStr()));
            changeHistoryDto.setEndTime(DateUtil.parseDate(changeHistoryDto.getEndTimeStr()));
            if (Objects.equals(changeHistoryDto.getIsFromResource(), Boolean.TRUE) && !applyDate.before(changeHistoryDto.getStartTime()) && !applyDate.after(changeHistoryDto.getEndTime()) && !Objects.equals(changeHistoryDto.getDeletedFlag(), Boolean.TRUE)) {
                return false;
            }
        }
        return true;
    }

    private void projectResourceRelSave(List<ProjectMemberChangeHistoryDto> projectMemberChangeHistories, Project project, Long headerId) {
        if (ListUtils.isEmpty(projectMemberChangeHistories)) {
            return;
        }
        List<ProjectMember> deletedMemberList = new ArrayList<>();//变更后需要删除的项目成员（资源计划引入）

        //作废原先的项目资源关系表
        ProjectResourceRelExample resourceRelExample = new ProjectResourceRelExample();
        resourceRelExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
        List<ProjectResourceRel> projectResourceRelList = projectResourceRelMapper.selectByExample(resourceRelExample);
        if (ListUtils.isNotEmpty(projectResourceRelList)) {
            for (ProjectResourceRel projectResourceRel : projectResourceRelList) {
                projectResourceRel.setDeletedFlag(Boolean.TRUE);
                projectResourceRelMapper.updateByPrimaryKeySelective(projectResourceRel);
            }
        }

        //预算变更提交避免生成重复的数据,在提交后把之前的数据删掉再重新保存
        if (headerId != null) {
            ProjectMemberChangeHistoryExample example = new ProjectMemberChangeHistoryExample();
            example.createCriteria().andHeaderIdEqualTo(headerId);
            List<ProjectMemberChangeHistory> historyList = projectMemberChangeHistoryMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(historyList)) {
                historyList.forEach(history -> {
                    history.setDeletedFlag(Boolean.TRUE);
                    projectMemberChangeHistoryMapper.updateByPrimaryKeySelective(history);
                });
            }
        }

        ProjectMemberExample memberExample = new ProjectMemberExample();
        memberExample.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectMember> historyMember = projectMemberMapper.selectByExample(memberExample);

        // 变更前项目成员信息
        if (ListUtils.isNotEmpty(historyMember)) {
            historyMember.forEach(projectMember -> {
                ProjectMemberChangeHistory before = BeanConverter.copyProperties(projectMember, ProjectMemberChangeHistory.class);
                before.setMipname(projectMember.getUserName());
                before.setOriginId(projectMember.getId());
                before.setStartTime(projectMember.getStartDate());
                before.setEndTime(projectMember.getEndDate());
                before.setHeaderId(headerId);
                before.setHistoryType(HistoryType.HISTORY.getCode());
                before.setProjectId(project.getId());
                before.setDeletedFlag(Boolean.FALSE);
                before.setId(null);
                projectMemberChangeHistoryMapper.insert(before);
            });
        }

        // 变更后项目成员信息
        if (ListUtils.isNotEmpty(projectMemberChangeHistories)) {
            for (ProjectMemberChangeHistoryDto projectMemberChangeHistoryDto : projectMemberChangeHistories) {

                ProjectMemberChangeHistory history = BeanConverter.copyProperties(projectMemberChangeHistoryDto, ProjectMemberChangeHistory.class);
                history.setOriginId(projectMemberChangeHistoryDto.getId());
                history.setProjectId(project.getId());
                history.setHeaderId(headerId);
                if (projectMemberChangeHistoryDto.getUserId() != null) {
                    UserInfo user = CacheDataUtils.findUserById(projectMemberChangeHistoryDto.getUserId());
                    if (user != null) {
                        history.setMipname(user.getUsername());
                    }
                }
                history.setHistoryType(HistoryType.CHANGE.getCode());
                history.setDeletedFlag(projectMemberChangeHistoryDto.getDeletedFlag() == null ? Boolean.FALSE :
                        projectMemberChangeHistoryDto.getDeletedFlag());
                history.setId(null);
                if (StringUtils.isNotEmpty(projectMemberChangeHistoryDto.getStartTimeStr())) {
                    history.setStartTime(DateUtils.parse(projectMemberChangeHistoryDto.getStartTimeStr(), "yyyy-MM-dd"));
                }
                if (StringUtils.isNotEmpty(projectMemberChangeHistoryDto.getEndTimeStr())) {
                    history.setEndTime(DateUtils.parse(projectMemberChangeHistoryDto.getEndTimeStr(), "yyyy-MM-dd"));
                }
                history.setIsFromResource(projectMemberChangeHistoryDto.getIsFromResource() == null ? Boolean.FALSE :
                        projectMemberChangeHistoryDto.getIsFromResource());
                projectMemberChangeHistoryMapper.insert(history);

                if (project != null && StringUtils.isNotEmpty(project.getResourceCode()) && !Objects.equals(projectMemberChangeHistoryDto.getDeletedFlag(), Boolean.TRUE) && projectMemberChangeHistoryDto.getIsFromResource() != null && projectMemberChangeHistoryDto.getIsFromResource()) {
                    ProjectResourceRel projectResourceRel = new ProjectResourceRel();
                    projectResourceRel.setProjectId(project.getId());
                    projectResourceRel.setResourceCode(project.getResourceCode());
                    projectResourceRel.setDeletedFlag(projectMemberChangeHistoryDto.getDeletedFlag() == null ? Boolean.FALSE :
                            projectMemberChangeHistoryDto.getDeletedFlag());
                    projectResourceRel.setMipname(projectMemberChangeHistoryDto.getMipname());
                    if (StringUtils.isNotEmpty(projectMemberChangeHistoryDto.getStartTimeStr())) {
                        projectResourceRel.setStartTime(DateUtils.parse(projectMemberChangeHistoryDto.getStartTimeStr(), "yyyy-MM-dd"));
                    }
                    if (StringUtils.isNotEmpty(projectMemberChangeHistoryDto.getEndTimeStr())) {
                        projectResourceRel.setEndTime(DateUtils.parse(projectMemberChangeHistoryDto.getEndTimeStr(), "yyyy-MM-dd"));
                    }
                    //先判断引用的时间段在其他人力外包RDM项目有没有重叠
                    projectBusinessService.checkResourceDate(projectResourceRel);
                    projectResourceRelMapper.insertSelective(projectResourceRel);
                }


            }
        }

    }

    private RdmResourcePlan getResourcePlan(String resourceCode, String username, Long ouId) {
        if (ouId == null || username == null || resourceCode == null) {
            return null;
        }
        RdmResourcePlanExample resourcePlanExample = new RdmResourcePlanExample();
        resourcePlanExample.createCriteria().andResourceCodeEqualTo(resourceCode).andMipnameEqualTo(username).andSupplierCodeIn(getSupplierCode(ouId));
        List<RdmResourcePlan> resourcePlanList = rdmResourcePlanMapper.selectByExample(resourcePlanExample);
        if (ListUtils.isNotEmpty(resourcePlanList)) {
            RdmResourcePlan resourcePlan = resourcePlanList.get(0);
            //如果存在多行记录，合并资源计划的开始结束时间
            for (int i = 1; i < resourcePlanList.size(); i++) {
                if (resourcePlan.getStartTime() != null && resourcePlanList.get(i).getStartTime() != null && resourcePlan.getStartTime().after(resourcePlanList.get(i).getStartTime())) {
                    resourcePlan.setStartTime(resourcePlanList.get(i).getStartTime());
                }
                if (resourcePlan.getEndTime() != null && resourcePlanList.get(i).getEndTime() != null && resourcePlan.getEndTime().before(resourcePlanList.get(i).getEndTime())) {
                    resourcePlan.setEndTime(resourcePlanList.get(i).getEndTime());
                }
            }
            return resourcePlan;
        }
        return null;
    }

    /**
     * 根据当前使用单位ou过滤资源计划数据
     *
     * @return
     */
    private List<String> getSupplierCode(Long ouId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("ous", ouId);
        params.put("pamEnabled", 0);//启用标记
        final String urls = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "organizationRel/list", params);
        ResponseEntity<String> responseEntitys = restTemplate.getForEntity(urls, String.class);
        final PageInfo<OrganizationRelDto> dataResponses = JSON.parseObject(responseEntitys.getBody(),
                new TypeReference<PageInfo<OrganizationRelDto>>() {
                });
        List<String> companyList = new ArrayList<>();
        companyList.add("-1L");//防止空值
        if (dataResponses != null && dataResponses.getList() != null) {
            List<OrganizationRelDto> organizationRelDtos = dataResponses.getList();
            for (OrganizationRelDto organizationRelDto : organizationRelDtos) {
                String s = "E0" + organizationRelDto.getCompanyCode();
                companyList.add(s);
            }
        }
        return companyList;
    }

    private void checkDeletedMember(List<ProjectMember> deletedMemberList) {
        if (ListUtils.isEmpty(deletedMemberList)) {
            return;
        }
        StringBuffer collectionException = new StringBuffer();
        StringBuffer hourException = new StringBuffer();
        List<String> userList1 = new ArrayList<>();
        for (ProjectMember member : deletedMemberList) {
            //资源计划引入
            if (member.getIsFromResource() != null && member.getIsFromResource()) {
                UserInfo userInfo = CacheDataUtils.findUserById(member.getUserId());
                //存在已入账工时，不允许删除
                long countWorkingHourByRdm = workingHourExtMapper.countWorkingHourByRdm(member.getUserId(), member.getProjectId(),
                        member.getStartDate(), member.getEndDate());
                if (countWorkingHourByRdm > 0) {
                    userList1.add(userInfo.getName());
                }
            }
        }

        if (ListUtils.isNotEmpty(userList1)) {
            collectionException.append("以下成员（" + userList1.get(0));
            for (int i = 1; i < userList1.size(); i++) {
                collectionException.append("," + userList1.get(i));
            }
            collectionException.append("）存在已入账工时，请联系财务冲销工时后再删除人员！");
        }
        if (userList1.size() > 0) {
            String exception = (collectionException.length() > 0 ? collectionException.toString() : "") + (hourException.length() > 0 ?
                    (" " + hourException.toString()) : "");
            throw new MipException(exception);
        }

    }

    @Override
    public ResponseMap getProjectBudgetChangeApp(Long formInstanceId) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> detailMapList = new ArrayList<>();
        final Long headerId = formInstanceId;
        final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(headerId);
        Project project = projectService.selectByPrimaryKey(projectHistoryHeader.getProjectId());
        Asserts.notEmpty(projectHistoryHeader, ErrorCode.CTC_PROJECT_NOT_FIND);
        final ProjectBudgetChangeSummaryHistory summaryHistory = summaryHistoryService.getByHeaderId(headerId);
        Asserts.notEmpty(summaryHistory, ErrorCode.CTC_CHANGE_RECORD_NOT_FIND);

        ProjectBudgetChangeVO budgetChangeVO = getBudgetHistory(formInstanceId);
        //客户信息
        headMap.put("name", project.getName()); //项目名称
        headMap.put("code", project.getCode()); // 项目编号
        headMap.put("type", "预算变更"); // 变更类型
        headMap.put("currency", project.getCurrency()); // 币种
        headMap.put("reasonType", projectHistoryHeader.getReasonType());//变更原因
        headMap.put("reason", projectHistoryHeader.getReason());//变更说明
        headMap.put("changeAmountSum", Optional.ofNullable(summaryHistory.getChangeAmount()).map(String::valueOf).orElse("0")); //预算变更金额汇总
        responseMap.setHeadMap(headMap);
        //变更内容--物料预算
        ProjectBudgetMaterialChangeSummaryHistory materialChangeSummaryHistory = budgetChangeVO.getMaterialSummaryHistory();
        if (!ObjectUtils.isEmpty(materialChangeSummaryHistory) && materialChangeSummaryHistory.getChangeAmount().compareTo(BigDecimal.ZERO) != 0) {
            Map<String, String> detailMap1 = new HashMap<>();
            detailMap1.put("item", "物料预算"); //变更项
            detailMap1.put("beforeAmount", String.valueOf(materialChangeSummaryHistory.getBeforeAmount().setScale(2, BigDecimal.ROUND_HALF_UP)));
            //变更前值
            detailMap1.put("afterAmount", String.valueOf(materialChangeSummaryHistory.getAfterAmount().setScale(2, BigDecimal.ROUND_HALF_UP))); //变更后值
            detailMap1.put("changeAmount", String.valueOf(materialChangeSummaryHistory.getChangeAmount().setScale(2, BigDecimal.ROUND_HALF_UP)));
            //调整差异额
            detailMapList.add(detailMap1);
        }
        //变更内容--人力预算
        ProjectBudgetHumanChangeSummaryHistory humanChangeSummaryHistory = budgetChangeVO.getHumanSummaryHistory();
        if (!ObjectUtils.isEmpty(humanChangeSummaryHistory) && humanChangeSummaryHistory.getChangeAmount().compareTo(BigDecimal.ZERO) != 0) {
            Map<String, String> detailMap2 = new HashMap<>();
            detailMap2.put("item", "人力预算"); //变更项
            detailMap2.put("beforeAmount", String.valueOf(humanChangeSummaryHistory.getBeforeAmount().setScale(2, BigDecimal.ROUND_HALF_UP))); //变更前值
            detailMap2.put("afterAmount", String.valueOf(humanChangeSummaryHistory.getAfterAmount().setScale(2, BigDecimal.ROUND_HALF_UP))); //变更后值
            detailMap2.put("changeAmount", String.valueOf(humanChangeSummaryHistory.getChangeAmount().setScale(2, BigDecimal.ROUND_HALF_UP))); //调整差异额
            detailMapList.add(detailMap2);
        }
        //变更内容--差旅预算
        ProjectBudgetTravelChangeSummaryHistory travelChangeSummaryHistory = budgetChangeVO.getTravelSummaryHistory();
        if (!ObjectUtils.isEmpty(travelChangeSummaryHistory) && travelChangeSummaryHistory.getChangeAmount().compareTo(BigDecimal.ZERO) != 0) {
            Map<String, String> detailMap3 = new HashMap<>();
            detailMap3.put("item", "差旅预算"); //变更项
            detailMap3.put("beforeAmount", String.valueOf(travelChangeSummaryHistory.getBeforeAmount().setScale(2, BigDecimal.ROUND_HALF_UP))); //变更前值
            detailMap3.put("afterAmount", String.valueOf(travelChangeSummaryHistory.getAfterAmount().setScale(2, BigDecimal.ROUND_HALF_UP))); //变更后值
            detailMap3.put("changeAmount", String.valueOf(travelChangeSummaryHistory.getChangeAmount().setScale(2, BigDecimal.ROUND_HALF_UP)));
            //调整差异额
            detailMapList.add(detailMap3);
        }
        //变更内容--非差旅预算（搬运/技术支持。。。）非差旅预算需要显示具体费用类型的变更值
        List<ProjectBudgetChangeVO.ChangeHistory> changeHistoryList = budgetChangeVO.getFeeHistories();
        if (!CollectionUtils.isEmpty(changeHistoryList)) {
            for (ProjectBudgetChangeVO.ChangeHistory changeHistory : changeHistoryList) {
                Map<String, String> detailMap = new HashMap<>();
                Object change = changeHistory.getChange();//变更后
                Object history = changeHistory.getHistory();//变更前
                String item = "";
                BigDecimal beforeAmount = BigDecimal.ZERO;
                BigDecimal afterAmount = BigDecimal.ZERO;
                BigDecimal changeAmount = BigDecimal.ZERO;
                if (!ObjectUtils.isEmpty(history)) {
                    ProjectBudgetFeeChangeHistoryVO historyHistoryVO = (ProjectBudgetFeeChangeHistoryVO) history;
                    item = historyHistoryVO.getFeeItemName();
                    beforeAmount = historyHistoryVO.getAmount();
                }
                if (!ObjectUtils.isEmpty(change)) {
                    ProjectBudgetFeeChangeHistoryVO changeHistoryVO = (ProjectBudgetFeeChangeHistoryVO) change;
                    item = changeHistoryVO.getFeeItemName();
                    afterAmount = changeHistoryVO.getAmount();
                }
                changeAmount = afterAmount.subtract(beforeAmount);
                if (changeAmount.compareTo(BigDecimal.ZERO) != 0) {
                    detailMap.put("item", item); //变更项
                    detailMap.put("beforeAmount", beforeAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //变更前值
                    detailMap.put("afterAmount", afterAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //变更后值
                    detailMap.put("changeAmount", changeAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //调整差异额
                    detailMapList.add(detailMap);
                }
            }
        }
        //变更内容--资产预算
        ProjectBudgetAssetChangeSummaryHistory assetChangeSummaryHistory = budgetChangeVO.getAssetSummaryHistory();
        if (!ObjectUtils.isEmpty(assetChangeSummaryHistory) && assetChangeSummaryHistory.getChangeAmount().compareTo(BigDecimal.ZERO) != 0) {
            Map<String, String> detailMap4 = new HashMap<>();
            detailMap4.put("item", "资产预算"); //变更项
            detailMap4.put("beforeAmount", String.valueOf(assetChangeSummaryHistory.getBeforeAmount().setScale(2, BigDecimal.ROUND_HALF_UP))); //变更前值
            detailMap4.put("afterAmount", String.valueOf(assetChangeSummaryHistory.getAfterAmount().setScale(2, BigDecimal.ROUND_HALF_UP))); //变更后值
            detailMap4.put("changeAmount", String.valueOf(assetChangeSummaryHistory.getChangeAmount().setScale(2, BigDecimal.ROUND_HALF_UP))); //调整差异额
            //调整差异额
            detailMapList.add(detailMap4);
        }
        //获取附件
        List<CtcAttachmentDto> attachmentDtos = budgetChangeVO.getAttachmentDtos();
        if (!CollectionUtils.isEmpty(attachmentDtos)) {
            List<AduitAtta> fileList = new ArrayList<>();
            for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(String.valueOf(attachmentDto.getAttachId()));
                aduitAtta.setFileSize(String.valueOf(attachmentDto.getFileSize()));
                aduitAtta.setFileName(attachmentDto.getAttachName());
                fileList.add(aduitAtta);
            }
            responseMap.setFileList(fileList);
        }
        responseMap.setList1(detailMapList);
        responseMap.setStatus("success");
        responseMap.setMsg("success");
        return responseMap;
    }

    private Long saveOrUpdateChangeRecord(ProjectBudgetChangeDTO projectBudgetChangeDTO) {
        final Long projectId = projectBudgetChangeDTO.getProjectId();
        Long headerId = projectBudgetChangeDTO.getHeaderId();

        // 变更记录，删除原有的数据，重新保存一次
        if (headerId != null) {
            final ProjectHistoryHeader localProjectHistoryHeader = new ProjectHistoryHeader();
            localProjectHistoryHeader.setId(headerId);
            localProjectHistoryHeader.setChangeType(CtcProjectChangeType.BUDGET.getCode());
            localProjectHistoryHeader.setProjectId(projectId);
            localProjectHistoryHeader.setReason(projectBudgetChangeDTO.getReason());
            localProjectHistoryHeader.setReasonType(projectBudgetChangeDTO.getReasonType());
            localProjectHistoryHeader.setReasonClassification(projectBudgetChangeDTO.getReasonClassification());
            localProjectHistoryHeader.setBudgetTargetFlag(projectBudgetChangeDTO.getBudgetTargetFlag());
            localProjectHistoryHeader.setStatus(ProjectChangeStatus.DRAFT.getCode());
            projectHistoryHeaderMapper.updateByPrimaryKeySelective(localProjectHistoryHeader);

            // 删除预算变更信息
            removeChangeRecord(headerId);

            // 删除商机报价单变更记录
            ProjectBusinessRsChangeHistoryExample businessRsChangeHistoryExample = new ProjectBusinessRsChangeHistoryExample();
            businessRsChangeHistoryExample.createCriteria().andHeaderIdEqualTo(headerId);
            List<ProjectBusinessRsChangeHistory> businessRsChangeHistoryList =
                    projectBusinessRsChangeHistoryMapper.selectByExample(businessRsChangeHistoryExample);
            if (PublicUtil.isNotEmpty(businessRsChangeHistoryList)) {
                businessRsChangeHistoryList.forEach(item -> {
                    projectBusinessRsChangeHistoryMapper.deleteByPrimaryKey(item.getId());
                });
            }

        } else {
            // 第一次保存新增
            ProjectHistoryHeader newProjectHistoryHeader = new ProjectHistoryHeader();
            newProjectHistoryHeader.setChangeType(CtcProjectChangeType.BUDGET.getCode());
            newProjectHistoryHeader.setProjectId(projectId);
            newProjectHistoryHeader.setReason(projectBudgetChangeDTO.getReason());
            newProjectHistoryHeader.setReasonType(projectBudgetChangeDTO.getReasonType());
            newProjectHistoryHeader.setReasonClassification(projectBudgetChangeDTO.getReasonClassification());
            newProjectHistoryHeader.setBudgetTargetFlag(projectBudgetChangeDTO.getBudgetTargetFlag());
            newProjectHistoryHeader.setStatus(ProjectChangeStatus.DRAFT.getCode());
            newProjectHistoryHeader.setDeletedFlag(Boolean.FALSE);
            saveHistoryHeader(newProjectHistoryHeader);

            headerId = newProjectHistoryHeader.getId();
            projectBudgetChangeDTO.setHeaderId(headerId);
        }

        return headerId;
    }

    /**
     * 失效变更记录相关信息
     *
     * @param headerId 变更ID
     */
    @Override
    public void removeChangeRecord(Long headerId) {
        // 失效项目总预算变更汇总记录
        final ProjectBudgetChangeSummaryHistory changeSummaryHistory = projectBudgetChangeSummaryHistoryService.getByHeaderId(headerId);
        if (changeSummaryHistory != null) {
            projectBudgetChangeSummaryHistoryService.deleteByPrimaryKey(changeSummaryHistory.getId());
        }

        // 失效项目资产预算变更记录
        final ProjectBudgetAssetChangeSummaryHistory AssetChangeSummaryHistory = projectBudgetAssetChangeHistoryService.getAssetChangeSummaryByHeaderId(headerId);
        if (AssetChangeSummaryHistory != null) {
            projectBudgetAssetChangeSummaryHistoryMapper.deleteByPrimaryKey(AssetChangeSummaryHistory.getId());
        }

        projectBudgetAssetChangeHistoryService.deleteByHeaderId(headerId);

        // 失效项目费用预算变更记录
        final ProjectBudgetFeeChangeSummaryHistory feeChangeSummaryHistory = feeChangeSummaryHistoryService.getByHeaderId(headerId);
        if (feeChangeSummaryHistory != null) {
            feeChangeSummaryHistoryService.deleteByPrimaryKey(feeChangeSummaryHistory.getId());
        }

        feeChangeHistoryService.deleteByHeaderId(headerId);

        // 失效项目人力预算变更记录
        final ProjectBudgetHumanChangeSummaryHistory humanChangeSummaryHistory = humanChangeSummaryHistoryService.getByHeaderId(headerId);
        if (humanChangeSummaryHistory != null) {
            humanChangeSummaryHistoryService.deleteByPrimaryKey(humanChangeSummaryHistory.getId());
        }

        humanChangeHistoryService.deleteByHeaderId(headerId);

        // 删除项目差旅预算变更记录
        final ProjectBudgetTravelChangeSummaryHistory travelChangeSummaryHistory = travelChangeSummaryHistoryService.getByHeaderId(headerId);
        if (travelChangeSummaryHistory != null) {
            travelChangeSummaryHistoryService.deleteByPrimaryKey(travelChangeSummaryHistory.getId());
        }

        travelChangeHistoryService.deleteByHeaderId(headerId);

        // 删除项目物料预算变更记录表
        final ProjectBudgetMaterialChangeSummaryHistory materialChangeSummaryHistory = materialChangeSummaryHistoryService.getByHeaderId(headerId);
        if (materialChangeSummaryHistory != null) {
            materialChangeSummaryHistoryService.deleteByPrimaryKey(materialChangeSummaryHistory.getId());
        }

        // 删除项目目标成本变更记录
        ProjectBudgetTargetChangeHistoryExample budgetTargetChangeHistoryExample = new ProjectBudgetTargetChangeHistoryExample();
        budgetTargetChangeHistoryExample.createCriteria().andHeaderIdEqualTo(headerId);
        List<ProjectBudgetTargetChangeHistory> list = projectBudgetTargetChangeHistoryMapper.selectByExample(budgetTargetChangeHistoryExample);
        if (PublicUtil.isNotEmpty(list)) {
            list.forEach(budgetTargetChangeHistory -> {
                projectBudgetTargetChangeHistoryMapper.deleteByPrimaryKey(budgetTargetChangeHistory.getId());
            });
        }

        materialChangeHistoryService.deleteByHeaderId(headerId);

    }

    /**
     * 保存附件信息
     */
    private void saveCtcAttachmentDtos(List<CtcAttachmentDto> attachmentDtos, Integer module, Long moduleId) {
        if (ListUtil.isPresent(attachmentDtos)) {
            //先过滤删除状态的附件
           /* List<CtcAttachmentDto> attachmentList = attachmentDtos.stream()
                    .filter(attachment -> !(Objects.nonNull(attachment.getDeletedFlag()) && attachment.getDeletedFlag())).collect(Collectors.toList
                    ());*/
            for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                attachmentDto.setModule(module);
                attachmentDto.setModuleId(moduleId);
                attachmentDto.setStatus(AttachmentStatus.CHECKING.code());
//                attachmentDto.setId(null);
                this.ctcAttachmentService.save(attachmentDto, SystemContext.getUserId());
            }
        }
    }

    private void authCheck(Project project) {
        final Long managerId = project.getManagerId();
        final Long financial = project.getFinancial();
        final Long userId = SystemContext.getUserId();
        Unit unit = unitExtMapper.selectByPrimaryKey(project.getUnitId());
        OrganizationCustomDictExample ocdExample = new OrganizationCustomDictExample();
        ocdExample.createCriteria().andDeletedFlagEqualTo(false).andOrgIdEqualTo(unit.getParentId()).andNameEqualTo("项目财务是否允许预算变更");
        List<OrganizationCustomDict> organizationCustomDictList = organizationCustomDictMapper.selectByExample(ocdExample);
        if (CollectionUtils.isEmpty(organizationCustomDictList) || !Objects.equals("1", organizationCustomDictList.get(0).getValue())) {
            Assert.isTrue(Objects.equals(managerId, userId), "只有项目经理可以发起项目预算变更");
        } else {
            Assert.isTrue(Objects.equals(managerId, userId) || Objects.equals(financial, userId), "只有项目经理或者项目财务可以发起项目预算变更");
        }

        final Integer status = project.getStatus();
        Assert.isTrue(Objects.equals(status, ProjectStatus.APPROVALED.getCode()), "项目进行中才能发起项目变更");
    }

    /**
     * 参数校验
     *
     * @param projectBudgetChangeDTO 入参
     */
    private void budgetChangeParamCheck(ProjectBudgetChangeDTO projectBudgetChangeDTO) {
        final Long projectId = projectBudgetChangeDTO.getProjectId();
        final String reason = projectBudgetChangeDTO.getReason();
        Assert.notNull(projectId, "项目不能为空");
        Assert.notNull(reason, "变更原因不能为空");
        Assert.isTrue(reason.length() < 1024, "变更原因长度不能超过1024");

        // 判断金额的阀值
        BigDecimal max = new BigDecimal("999999999999");

        final ProjectBudgetChangeSummaryHistory projectBudgetChangeSummaryHistory = projectBudgetChangeDTO.getProjectBudgetChangeSummaryHistory();

        if (projectBudgetChangeSummaryHistory != null) {
            final BigDecimal afterAmount = projectBudgetChangeSummaryHistory.getAfterAmount();
            final BigDecimal beforeAmount = projectBudgetChangeSummaryHistory.getBeforeAmount();
            final BigDecimal changeAmount = projectBudgetChangeSummaryHistory.getChangeAmount();

            if (afterAmount != null) {
                Assert.isTrue(max.compareTo(afterAmount) > 0, "项目总预算超出金额上限999,999,999,999，请联系系统管理员");
            }

            if (beforeAmount != null) {
                Assert.isTrue(max.compareTo(beforeAmount) > 0, "项目总预算超出金额上限999,999,999,999，请联系系统管理员");
            }

            if (changeAmount != null) {
                Assert.isTrue(max.compareTo(changeAmount) > 0, "项目总预算超出金额上限999,999,999,999，请联系系统管理员");
            }
        }
    }

    private void saveHistoryHeader(ProjectHistoryHeader projectHistoryHeader) {
        projectHistoryHeaderMapper.insert(projectHistoryHeader);
    }

    private void materialChangeSave(List<ProjectBudgetMaterialChangeHistoryDTO> materialChangeHistoryList, Long projectId, Long headerId) {
        //检查materialChangeHistoryList中子节点名称是否重复
        checkBudgetMaterial(materialChangeHistoryList);

        ProjectBudgetMaterialExample example = new ProjectBudgetMaterialExample();
        final ProjectBudgetMaterialExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        final List<ProjectBudgetMaterial> projectBudgetMaterials = projectBudgetMaterialService.selectByExample(example);

        // 变更前物料记录
        Map<Long, ProjectBudgetMaterialChangeHistory> materialMap = new HashMap<>();
        // 待更新父级关系记录
        List<ProjectBudgetMaterialChangeHistory> needUpdateParentList = new ArrayList<>();
        if (ListUtils.isNotEmpty(projectBudgetMaterials)) {
            projectBudgetMaterials.forEach(material -> {
                // 保存变更前物料记录，父级关系转换
                final ProjectBudgetMaterialChangeHistory addMaterial = BeanConverter.copyProperties(material,
                        ProjectBudgetMaterialChangeHistory.class);
                addMaterial.setOriginId(addMaterial.getId());
                addMaterial.setHeaderId(headerId);
                addMaterial.setHistoryType(HistoryType.HISTORY.getCode());
                addMaterial.setDeletedFlag(Boolean.FALSE);
                addMaterial.setId(null);
                materialChangeHistoryService.insert(addMaterial);

                // 收集待更新父级记录
                if (material.getParentId() != null) {
                    needUpdateParentList.add(addMaterial);
                }

                materialMap.put(material.getId(), addMaterial);
            });
        }

        // 更新父级
        if (ListUtils.isNotEmpty(needUpdateParentList)) {
            needUpdateParentList.forEach(material -> {
                final Long parentId = material.getParentId();
                final ProjectBudgetMaterialChangeHistory projectBudgetMaterialChangeHistory = materialMap.get(parentId);
                if (projectBudgetMaterialChangeHistory != null) {
                    material.setParentId(projectBudgetMaterialChangeHistory.getId());
                }
                materialChangeHistoryService.updateByPrimaryKeySelective(material);
            });
        }

        // 变更后物料记录
        materialChangeHistoryList.forEach(history -> {
            final ProjectBudgetMaterialChangeHistory addMaterial = BeanConverter.copyProperties(history, ProjectBudgetMaterialChangeHistory.class);
            final Long id = history.getId();
            addMaterial.setOriginId(id);
            addMaterial.setHeaderId(headerId);
            addMaterial.setProjectId(projectId);
            addMaterial.setHistoryType(HistoryType.CHANGE.getCode());
            addMaterial.setDeletedFlag(history.getDeletedFlag() == null ? Boolean.FALSE : history.getDeletedFlag());
            addMaterial.setId(null);
            materialChangeHistoryService.insert(addMaterial);

            //递归处理子层
            if (ListUtils.isNotEmpty(history.getSubHistories())) {
                saveSubHistoryList(history, headerId, addMaterial.getId(), projectId);
            }
        });
    }

    private void checkBudgetMaterial(List<ProjectBudgetMaterialChangeHistoryDTO> materialChangeHistoryList) {
        if (CollectionUtils.isEmpty(materialChangeHistoryList)) {
            return;
        }

        for (ProjectBudgetMaterialChangeHistoryDTO history : materialChangeHistoryList) {
            if (Boolean.TRUE.equals(history.getDeletedFlag())) {
                continue;
            }

            if (CollectionUtils.isEmpty(history.getSubHistories())) {
                continue;
            }

            // 检查当前组中是否有重复的 name
            Set<String> names = new HashSet<>();
            List<ProjectBudgetMaterialChangeHistoryDTO> validMaterials = history.getSubHistories()
                    .stream()
                    .filter(material -> !Boolean.TRUE.equals(material.getDeletedFlag()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(validMaterials)) {
                continue;
            }

            for (ProjectBudgetMaterialChangeHistoryDTO material : validMaterials) {
                if (!names.add(material.getName())) {
                    throw new BizException(Code.ERROR, "物料预算存在相同名称的数据，请手动进行合并");
                }
                checkChildrenBudgetMaterial(material, new HashSet<>());
            }
        }
    }

    /**
     * 递归检查子节点
     *
     * @param dto   物料
     * @param names 名称集合
     */
    private void checkChildrenBudgetMaterial(ProjectBudgetMaterialChangeHistoryDTO dto, Set<String> names) {
        List<ProjectBudgetMaterialChangeHistoryDTO> childrens = dto.getSubHistories();
        if (ListUtils.isNotEmpty(childrens)) {
            List<ProjectBudgetMaterialChangeHistoryDTO> newChildrens = childrens
                    .stream()
                    .filter(material -> !Objects.equals(Boolean.TRUE, material.getDeletedFlag()))
                    .collect(Collectors.toList());
            for (ProjectBudgetMaterialChangeHistoryDTO children : newChildrens) {
                // 检查子节点名称是否重复
                if (!names.add(children.getName())) {
                    throw new BizException(Code.ERROR, "物料预算存在相同名称的数据，请手动进行合并");
                }
                checkChildrenBudgetMaterial(children, new HashSet<>());
            }
        }
    }

    private void saveSubHistoryList(ProjectBudgetMaterialChangeHistoryDTO history, Long headerId, Long parentId, Long projectId) {
        final List<ProjectBudgetMaterialChangeHistoryDTO> subHistoryList = history.getSubHistories();
        subHistoryList.forEach(subHistory -> {
            subHistory.setOriginId(subHistory.getId());
            subHistory.setHeaderId(headerId);
            subHistory.setParentId(parentId);
            subHistory.setProjectId(projectId);
            subHistory.setHistoryType(HistoryType.CHANGE.getCode());
            subHistory.setDeletedFlag(subHistory.getDeletedFlag() == null ? Boolean.FALSE : subHistory.getDeletedFlag());
            subHistory.setId(null);
            materialChangeHistoryService.insert(subHistory);

            if (ListUtils.isNotEmpty(subHistory.getSubHistories())) {
                saveSubHistoryList(subHistory, headerId, subHistory.getId(), projectId);
            }

        });
    }

    private void materialSummarySave(ProjectBudgetMaterialChangeSummaryHistory projectBudgetMaterialChangeSummaryHistory) {
        projectBudgetMaterialChangeSummaryHistory.setId(null);
        projectBudgetMaterialChangeSummaryHistory.setDeletedFlag(Boolean.FALSE);
        materialChangeSummaryHistoryService.insert(projectBudgetMaterialChangeSummaryHistory);
    }

    private void humanChangeSave(List<ProjectBudgetHumanChangeHistoryDto> projectBudgetHumanChangeHistories, Long projectId, Long headerId) {
        ProjectBudgetHumanExample example = new ProjectBudgetHumanExample();
        final ProjectBudgetHumanExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        final List<ProjectBudgetHuman> projectBudgetHumanList = projectBudgetHumanService.selectByExample(example);
        // 变更前人力预算信息
        if (ListUtils.isNotEmpty(projectBudgetHumanList)) {
            //查询经济事项
            Map<Long, String> feeTypeMap = new HashMap<>();
            List<Long> feeTypeIdList = projectBudgetHumanList.stream().map(ProjectBudgetHuman::getFeeTypeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(feeTypeIdList)) {
                List<FeeType> feeTypes = basedataExtService.getFeeTypeByIds(feeTypeIdList);
                feeTypeMap = feeTypes.stream().collect(Collectors.toMap(FeeType::getId, FeeType::getFeeTypeName));
            }

            for (ProjectBudgetHuman projectBudgetHuman : projectBudgetHumanList) {
                final ProjectBudgetHumanChangeHistory projectBudgetHumanChangeHistory = BeanConverter.copyProperties(projectBudgetHuman, ProjectBudgetHumanChangeHistory.class);
                projectBudgetHumanChangeHistory.setOriginId(projectBudgetHuman.getId());
                projectBudgetHumanChangeHistory.setHeaderId(headerId);
                projectBudgetHumanChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                projectBudgetHumanChangeHistory.setProjectId(projectId);
                projectBudgetHumanChangeHistory.setFeeTypeName(feeTypeMap.get(projectBudgetHuman.getFeeTypeId()));
                projectBudgetHumanChangeHistory.setDeletedFlag(Boolean.FALSE);
                projectBudgetHumanChangeHistory.setId(null);
                humanChangeHistoryService.insert(projectBudgetHumanChangeHistory);
            }
        }

        // 变更后人力预算
        projectBudgetHumanChangeHistories.forEach(history -> {
            history.setOriginId(history.getId());
            history.setProjectId(projectId);
            history.setHeaderId(headerId);
            history.setHistoryType(HistoryType.CHANGE.getCode());
            history.setOriginId(history.getId());
            history.setDeletedFlag(history.getDeletedFlag() == null ? Boolean.FALSE : history.getDeletedFlag());
            history.setId(null);
            history.setFunctionId(history.getProjectRole());
            history.setUserAttrId(history.getLevelId());
            history.setUserAttrName(history.getLevelName());
            humanChangeHistoryService.insert(history);
        });

    }

    private void humanSummaryChangeSave(ProjectBudgetHumanChangeSummaryHistory history, Long projectId, Long headerId) {
        history.setId(null);
        history.setProjectId(projectId);
        history.setHeaderId(headerId);
        history.setDeletedFlag(Boolean.FALSE);
        humanChangeSummaryHistoryService.insert(history);
    }

    /**
     * 保存差旅预算变更 (为了展示排序，删除的数据也需要保存)
     *
     * @param histories
     * @param projectId
     * @param headerId
     */
    private void travelChangeSave(List<ProjectBudgetTravelChangeHistory> histories, Long projectId, Long headerId) {
        ProjectBudgetTravelExample example = new ProjectBudgetTravelExample();
        final ProjectBudgetTravelExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        final List<ProjectBudgetTravel> projectBudgetTravels = projectBudgetTravelService.selectByExample(example);

        // 变更前差旅预算信息
        if (ListUtils.isNotEmpty(projectBudgetTravels)) {
            projectBudgetTravels.forEach(projectBudgetTravel -> {
                final ProjectBudgetTravelChangeHistory beforeTravel = BeanConverter.copyProperties(projectBudgetTravel,
                        ProjectBudgetTravelChangeHistory.class);
                beforeTravel.setProjectId(projectId);
                beforeTravel.setHeaderId(headerId);
                beforeTravel.setOriginId(projectBudgetTravel.getId());
                beforeTravel.setDeletedFlag(Boolean.FALSE);
                beforeTravel.setHistoryType(HistoryType.HISTORY.getCode());
                beforeTravel.setId(null);

                travelChangeHistoryService.insert(beforeTravel);
            });
        }

        // 变更后差旅预算信息
        if (ListUtils.isNotEmpty(histories)) {
            histories.forEach(history -> {
                history.setOriginId(history.getId());
                history.setProjectId(projectId);
                history.setHeaderId(headerId);
                history.setDeletedFlag(history.getDeletedFlag() == null ? Boolean.FALSE : history.getDeletedFlag());
                history.setHistoryType(HistoryType.CHANGE.getCode());
                history.setId(null);

                travelChangeHistoryService.insert(history);
            });
        }
    }

    /**
     * 保存差旅变更汇总
     *
     * @param history   变更记录
     * @param projectId 项目ID
     * @param headerId  头ID
     */
    private void travelSummaryChangeSave(ProjectBudgetTravelChangeSummaryHistory history, Long projectId, Long headerId) {
        history.setProjectId(projectId);
        history.setHeaderId(headerId);
        history.setDeletedFlag(Boolean.FALSE);
        history.setId(null);
        travelChangeSummaryHistoryService.insert(history);
    }

    /**
     * 保存项目费用预算变更
     *
     * @param histories
     * @param projectId
     * @param headerId
     */
    private void feeChangeSave(List<ProjectBudgetFeeChangeHistory> histories, Long projectId, Long headerId) {
        ProjectBudgetFeeExample example = new ProjectBudgetFeeExample();
        final ProjectBudgetFeeExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        final List<ProjectBudgetFee> projectBudgetFees = projectBudgetFeeService.selectByExample(example);

        // 变更前差旅预算信息
        if (ListUtils.isNotEmpty(projectBudgetFees)) {
            projectBudgetFees.forEach(projectBudgetFee -> {
                final ProjectBudgetFeeChangeHistory beforeFee = BeanConverter.copyProperties(projectBudgetFee, ProjectBudgetFeeChangeHistory.class);
                beforeFee.setProjectId(projectId);
                beforeFee.setHeaderId(headerId);
                beforeFee.setOriginId(projectBudgetFee.getId());
                beforeFee.setDeletedFlag(Boolean.FALSE);
                beforeFee.setHistoryType(HistoryType.HISTORY.getCode());
                beforeFee.setId(null);

                feeChangeHistoryService.insert(beforeFee);
            });
        }

        // 变更后差旅预算信息
        if (ListUtils.isNotEmpty(histories)) {
            histories.forEach(history -> {
                history.setOriginId(history.getId());
                history.setId(null);
                history.setProjectId(projectId);
                history.setHeaderId(headerId);
                history.setDeletedFlag(history.getDeletedFlag() == null ? Boolean.FALSE : history.getDeletedFlag());
                history.setPreviewFlag(Boolean.FALSE);
                history.setHistoryType(HistoryType.CHANGE.getCode());

                feeChangeHistoryService.insert(history);
            });
        }
    }

    /**
     * 保存项目资产预算变更
     *
     * @param histories
     * @param projectId
     * @param headerId
     */
    private void assetChangeSave(List<ProjectBudgetAssetChangeHistory> histories, Long projectId, Long headerId) {
        ProjectBudgetAssetExample example = new ProjectBudgetAssetExample();
        final ProjectBudgetAssetExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        final List<ProjectBudgetAsset> projectBudgetAssets = projectBudgetAssetMapper.selectByExample(example);

        // 变更前差旅预算信息
        if (ListUtils.isNotEmpty(projectBudgetAssets)) {
            projectBudgetAssets.forEach(projectBudgetAsset -> {
                final ProjectBudgetAssetChangeHistory beforeAsset = BeanConverter.copyProperties(projectBudgetAsset, ProjectBudgetAssetChangeHistory.class);
                beforeAsset.setProjectId(projectId);
                beforeAsset.setHeaderId(headerId);
                beforeAsset.setOriginId(projectBudgetAsset.getId());
                beforeAsset.setDeletedFlag(Boolean.FALSE);
                beforeAsset.setHistoryType(HistoryType.HISTORY.getCode());
                beforeAsset.setId(null);

                projectBudgetAssetChangeHistoryMapper.insert(beforeAsset);
            });
        }

        // 变更后差旅预算信息
        if (ListUtils.isNotEmpty(histories)) {
            histories.forEach(history -> {
                history.setOriginId(history.getId());
                history.setId(null);
                history.setProjectId(projectId);
                history.setHeaderId(headerId);
                history.setDeletedFlag(history.getDeletedFlag() == null ? Boolean.FALSE : history.getDeletedFlag());
                history.setPreviewFlag(Boolean.FALSE);
                history.setHistoryType(HistoryType.CHANGE.getCode());

                projectBudgetAssetChangeHistoryMapper.insert(history);
            });
        }
    }

    /**
     * 保存项目费用变更汇总
     *
     * @param history
     * @param projectId
     * @param headerId
     */
    private void feeSummaryChangeSave(ProjectBudgetFeeChangeSummaryHistory history, Long projectId, Long headerId) {
        history.setProjectId(projectId);
        history.setHeaderId(headerId);
        history.setId(null);
        history.setDeletedFlag(Boolean.FALSE);
        feeChangeSummaryHistoryService.insert(history);
    }

    private void budgetSummaryChangeSave(ProjectBudgetChangeSummaryHistory history, Long projectId, Long headerId) {
        history.setId(null);
        history.setHeaderId(headerId);
        history.setProjectId(projectId);
        history.setDeletedFlag(Boolean.FALSE);
        changeSummaryHistoryService.insert(history);
    }

    /**
     * 保存资产预算变更汇总
     *
     * @param history
     * @param projectId
     * @param headerId
     */
    private void assetSummaryChangeSave(ProjectBudgetAssetChangeSummaryHistory history, Long projectId, Long headerId) {
        history.setProjectId(projectId);
        history.setHeaderId(headerId);
        history.setId(null);
        history.setDeletedFlag(Boolean.FALSE);
        projectBudgetAssetChangeSummaryHistoryMapper.insert(history);
    }

    @Override
    public boolean check(Long projectId) {
        final Project project = projectService.selectByPrimaryKey(projectId);
        Assert.isNull(project, "项目不存在");
        authCheck(project);

        // 发起变更的流程不能在变更
        ProjectHistoryHeaderExample example = new ProjectHistoryHeaderExample();
        final ProjectHistoryHeaderExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andProjectIdEqualTo(projectId);
        criteria.andStatusEqualTo(ProjectChangeStatus.APPROVALING.getCode());
        final List<ProjectHistoryHeader> projectHistoryHeaders = projectHistoryHeaderMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(projectHistoryHeaders)) {
            throw new MipException("项目存在审批中的变更流程");
        }

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long changeSuccess(Long headerId) {
        logger.info("预算变更开始：" + headerId);
        final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(headerId);
        Assert.notNull(projectHistoryHeader, "变更记录不存在");
        projectHistoryHeader.setStatus(ProjectChangeStatus.APPROVALED.getCode());
        projectHistoryHeaderMapper.updateByPrimaryKeySelective(projectHistoryHeader);

        final Long projectId = projectHistoryHeader.getProjectId();
        final Long createBy = projectHistoryHeader.getCreateBy();
        // 将变更记录写入正式表
        // 物料
        projectBudgetChangeHelper.handleChangeMaterialToOfficialTable(headerId, projectHistoryHeader.getProjectId());
        logger.info("预算变更：物料");
        // 人力
        projectBudgetChangeHelper.handleChangeHumanToOfficialTable(headerId, projectHistoryHeader.getProjectId());
        //logger.info("预算变更：人力");
        // 项目成员
        //handleChangeMember(headerId, projectHistoryHeader.getProjectId());
        logger.info("预算变更：项目成员");
        // 差旅
        projectBudgetChangeHelper.handleChangeTravelToOfficialTable(headerId, projectHistoryHeader.getProjectId());
        logger.info("预算变更：差旅");
        // 项目费用
        projectBudgetChangeHelper.handleChangeFeeToOfficialTable(headerId, projectHistoryHeader.getProjectId());
        logger.info("预算变更：项目费用");
        // 项目资产
        projectBudgetChangeHelper.handleChangeAssetToOfficialTable(headerId, projectHistoryHeader.getProjectId());
        logger.info("预算变更：资产费用");

        List<ProjectBudgetChangeVO.ChangeHistory> projectBudgetMaterialChangeHistoryList = new ArrayList<>();

        ProjectBudgetChangeVO projectBudgetChangeVO = new ProjectBudgetChangeVO();
        projectBudgetChangeVO.setHeader(projectHistoryHeader);
        logger.info("预算变更：构建物料变更信息");
        // 构建物料变更信息
        projectBudgetChangeHelper.buildMaterialChangeHistory(headerId, projectBudgetChangeVO);
        final List<ProjectBudgetChangeVO.ChangeHistory> materialHistories = projectBudgetChangeVO.getMaterialHistories();

        buildProjectBudgetMaterialChange(materialHistories, projectBudgetMaterialChangeHistoryList, null);
        milepostDesignPlanService.handleProjectBudgetMaterialChange(projectId, createBy, projectBudgetMaterialChangeHistoryList);

        logger.info("预算变更结束");
        return headerId;
    }

    private void handleChangeMember(Long headerId, Long projectId) {
        Project project = projectService.selectByPrimaryKey(projectId);

        //查询项目成员变更记录
        ProjectMemberChangeHistoryExample example1 = new ProjectMemberChangeHistoryExample();
        example1.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        List<ProjectMemberChangeHistory> memberChangeHistoryList = projectMemberChangeHistoryMapper.selectByExample(example1);
        //先查询是否已经存在项目成员
        ProjectMemberExample example = new ProjectMemberExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(projectId);
        List<ProjectMember> projectMembers = projectMemberMapper.selectByExample(example);
        if (ListUtils.isEmpty(projectMembers)) {
            if (ListUtils.isNotEmpty(memberChangeHistoryList)) {
                memberChangeHistoryList.forEach(history -> {
                    final ProjectMember projectMember = BeanConverter.copyProperties(history, ProjectMember.class);
                    projectMember.setId(null);
                    projectMember.setUserName(history.getMipname());
                    projectMember.setStartDate(history.getStartTime());
                    projectMember.setEndDate(history.getEndTime());
                    projectMemberMapper.insert(projectMember);
                });
            }
        } else {
            // 变更记录为空，说明全部已删除
            if (ListUtils.isEmpty(memberChangeHistoryList)) {
                for (ProjectMember projectMember : projectMembers) {
                    projectMember.setDeletedFlag(Boolean.TRUE);
                    //如果是项目经理，修改引入标记,不做删除
                    if (Objects.equals(project.getManagerId(), projectMember.getUserId())) {
                        projectMember.setIsFromResource(Boolean.FALSE);
                    } else {
                        projectMember.setDeletedFlag(Boolean.TRUE);
                        //如果是资源计划引入，删除对应的工时数据（只删除审批中/草稿/变更中，审批通过的工时在后面冲销的时候删除）
                        if (projectMember.getIsFromResource() != null && projectMember.getIsFromResource()) {
                            workingHourExtMapper.batcheDeleteWorkingHourByRdm(projectMember.getUserId(), projectMember.getProjectId(),
                                    projectMember.getStartDate(), projectMember.getEndDate());
                        }
                    }
                    projectMemberMapper.updateByPrimaryKeySelective(projectMember);
                }
                return;
            }

            if (ListUtils.isNotEmpty(projectMembers) && ListUtils.isNotEmpty(memberChangeHistoryList)) {
                projectMembers.forEach(member -> {
                    member.setDeletedFlag(Boolean.TRUE);
                    //如果是资源计划引入，删除对应的工时数据（只删除审批中/草稿/变更中，审批通过的工时在后面冲销的时候删除）
                    if (member.getIsFromResource() != null && member.getIsFromResource()) {
                        workingHourExtMapper.batcheDeleteWorkingHourByRdm(member.getUserId(), member.getProjectId(), member.getStartDate(),
                                member.getEndDate());
                    }
                    projectMemberMapper.updateByPrimaryKeySelective(member);
                });
                memberChangeHistoryList.forEach(changeHistory -> {
                    ProjectMember projectMember = BeanConverter.copyProperties(changeHistory, ProjectMember.class);
                    projectMember.setId(null);
                    if (changeHistory.getUserId() != null) {
                        UserInfo user = CacheDataUtils.findUserById(changeHistory.getUserId());
                        if (user != null) {
                            projectMember.setUserName(user.getUsername());
                        }
                    }
                    projectMember.setUserName(changeHistory.getMipname());
                    projectMember.setStartDate(changeHistory.getStartTime());
                    projectMember.setEndDate(changeHistory.getEndTime());
                    projectMemberMapper.insertSelective(projectMember);
                });
            }

        }
    }

    private void buildProjectBudgetMaterialChange(List<ProjectBudgetChangeVO.ChangeHistory> materialHistories,
                                                  List<ProjectBudgetChangeVO.ChangeHistory> targetList, Long parentId) {
        if (ListUtils.isNotEmpty(materialHistories)) {
            materialHistories.forEach(history -> {
                final Integer type = history.getType();
                Long originId = null;

                Object changeObj = history.getChange();
                Object hisObj = history.getHistory();

                if (changeObj != null) {
                    FlatChangeHistoryDTO change = (FlatChangeHistoryDTO) changeObj;
                    final ProjectBudgetMaterialChangeHistoryVO projectBudgetMaterialChangeHistoryVO =
                            BeanConverter.copyProperties(change, ProjectBudgetMaterialChangeHistoryVO.class);
                    history.setChange(projectBudgetMaterialChangeHistoryVO);
                }

                if (hisObj != null) {
                    ProjectBudgetMaterialChangeHistory his = (ProjectBudgetMaterialChangeHistory) hisObj;
                    final ProjectBudgetMaterialChangeHistoryVO projectBudgetMaterialChangeHistoryVO =
                            BeanConverter.copyProperties(his, ProjectBudgetMaterialChangeHistoryVO.class);
                    history.setHistory(projectBudgetMaterialChangeHistoryVO);
                }

                // 判断是否为变更记录
                if (ChangeType.UPDATE.code() == type) {
                    final ProjectBudgetMaterialChangeHistoryVO change = (ProjectBudgetMaterialChangeHistoryVO) history.getChange();
                    final ProjectBudgetMaterialChangeHistoryVO his = (ProjectBudgetMaterialChangeHistoryVO) history.getHistory();

                    if (change != null && his != null && Objects.equals(change.getName(), his.getName()) && Objects.equals(change.getCode(),
                            his.getCode()) && Objects.equals(change.getExt(), his.getExt()) && Objects.equals(change.getNumber(), his.getNumber())
                            && Objects.equals(change.getPrice(), his.getPrice()) && Objects.equals(change.getPriceTotal(), his.getPriceTotal())
                            && Objects.equals(change.getUnit(), his.getUnit()) && Objects.equals(change.getSource(), his.getSource())
                            && Objects.equals(change.getErpCode(), his.getErpCode())
                            && Objects.equals(change.getPlannedDeliveryStartDate(), his.getPlannedDeliveryStartDate())
                            && Objects.equals(change.getPlannedDeliveryDate(), his.getPlannedDeliveryDate())) {
                        history.setType(ChangeType.NO.code());
                    }

                    if (parentId != null) {
                        his.setOriginParentId(parentId);
                    }

                    if (his != null) {
                        originId = his.getOriginId();
                    }
                } else if (ChangeType.ADD.code() == type) {
                    final ProjectBudgetMaterialChangeHistoryVO change = (ProjectBudgetMaterialChangeHistoryVO) history.getChange();
                    if (parentId != null) {
                        change.setOriginParentId(parentId);
                    }

                    if (change != null) {
                        originId = change.getOriginId();
                    }
                }

                targetList.add(history);

                final List<ProjectBudgetChangeVO.ChangeHistory> subHistories = history.getSubHistories();
                buildProjectBudgetMaterialChange(subHistories, targetList, originId);
            });
        }
    }

    @Override
    public ProjectBudgetChangeVO getBudgetHistory(Long headerId) {
        final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(headerId);
        Assert.notNull(projectHistoryHeader, "变更记录不存在");

        ProjectBudgetChangeVO projectBudgetChangeVO = new ProjectBudgetChangeVO();
        projectBudgetChangeVO.setHeader(projectHistoryHeader);

        final Long projectId = projectHistoryHeader.getProjectId();
        final Project project = projectService.selectByPrimaryKey(projectId);
        if (project != null) {
            projectBudgetChangeVO.setCode(project.getCode());
            projectBudgetChangeVO.setProjectName(project.getName());
            projectBudgetChangeVO.setProjectAmount(project.getAmount());
        }

        // 构建物料变更信息
        projectBudgetChangeHelper.buildMaterialChangeHistory(headerId, projectBudgetChangeVO);
        // 构建物料变更汇总信息
        final ProjectBudgetMaterialChangeSummaryHistory materialChangeSummaryHistory = materialChangeSummaryHistoryService.getByHeaderId(headerId);
        projectBudgetChangeVO.setMaterialSummaryHistory(materialChangeSummaryHistory);

        // 构建人力变更信息
        projectBudgetChangeHelper.buildHumanChangeHistory(headerId, projectBudgetChangeVO);

        // 构建项目成员变更信息
        projectBudgetChangeHelper.buildMemberChangeHistory(headerId, projectBudgetChangeVO);
        // 构建人力变更汇总信息
        final ProjectBudgetHumanChangeSummaryHistory humanChangeSummaryHistory = humanChangeSummaryHistoryService.getByHeaderId(headerId);
        projectBudgetChangeVO.setHumanSummaryHistory(humanChangeSummaryHistory);

        // 构建差旅变更信息
        projectBudgetChangeHelper.buildTravelChangeHistory(headerId, projectBudgetChangeVO);
        // 构建差旅变更汇总信息
        final ProjectBudgetTravelChangeSummaryHistory travelChangeSummaryHistory = travelChangeSummaryHistoryService.getByHeaderId(headerId);
        projectBudgetChangeVO.setTravelSummaryHistory(travelChangeSummaryHistory);

        // 构建项目费用变更信息
        projectBudgetChangeHelper.buildFeeChangeHistory(headerId, projectBudgetChangeVO);
        // 构建项目费用变更汇总信息
        final ProjectBudgetFeeChangeSummaryHistory feeChangeSummaryHistory = feeChangeSummaryHistoryService.getByHeaderId(headerId);
        projectBudgetChangeVO.setFeeSummaryHistory(feeChangeSummaryHistory);

        // 构建项目资产变更信息
        projectBudgetChangeHelper.buildAssetChangeHistory(headerId, projectBudgetChangeVO);
        // 构建项目资产变更汇总信息
        final ProjectBudgetAssetChangeSummaryHistory assetChangeSummaryHistory = projectBudgetAssetChangeHistoryService.getAssetChangeSummaryByHeaderId(headerId);
        projectBudgetChangeVO.setAssetSummaryHistory(assetChangeSummaryHistory);

        final ProjectBudgetChangeSummaryHistory summaryHistory = projectBudgetChangeSummaryHistoryService.getByHeaderId(headerId);
        projectBudgetChangeVO.setSummaryHistory(summaryHistory);

        // 构建目标成本变更信息
        projectBudgetChangeHelper.buildTargetChangeHistory(headerId, projectBudgetChangeVO);

        // 构建商机报价单变更信息
        projectBudgetChangeHelper.buildBusinessHistory(headerId, projectBudgetChangeVO);

        //获取附件
        projectBudgetChangeVO.setAttachmentDtos(this.findCtcAttachmentDto(headerId, CtcAttachmentModule.PROJECT_BUDGET_CHANGE.code()));

        return projectBudgetChangeVO;
    }

    /**
     * 构建附件信息
     */
    private List<CtcAttachmentDto> findCtcAttachmentDto(Long moduleId, Integer module) {
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(module);
        attachmentQuery.setModuleId(moduleId);
        List<CtcAttachmentDto> ctcAttachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
        if (CollectionUtils.isEmpty(ctcAttachmentDtos)) {
            return Collections.emptyList();
        }
        return ctcAttachmentDtos;
    }

    @Override
    public ProjectCostVO getProjectCost(Long projectId) {
        ProjectCostVO projectCostVO = new ProjectCostVO();

        final CostCollection sum = costCollectionService.sum(projectId);
        if (sum != null) {
            projectCostVO.setInnerLaborCost(sum.getInnerLaborCost());
            projectCostVO.setOuterLaborCost(sum.getOuterLaborCost());
            projectCostVO.setMaterialCost(sum.getMaterialActualCost());

            // 计算差旅成本及非差旅成本
            Map<String, Object> param = new HashMap<>();
            final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "feeType/getTravel", param);
            String res = restTemplate.getForObject(url, String.class);
            DataResponse<List<FeeType>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<FeeType>>>() {
            });
            if (response != null) {
                Map param2 = new HashMap();
                param2.put("projectId", projectId);
                // 差旅 +  非差旅
                BigDecimal all = feeCostDetailService.sum(param2);
                if (all == null) {
                    all = BigDecimal.ZERO;
                }
                final List<FeeType> feeTypeList = response.getData();

                BigDecimal travelSum = BigDecimal.ZERO;
                if (ListUtils.isNotEmpty(feeTypeList)) {
                    final List<String> codeList = feeTypeList.stream().map(FeeType::getFeeTypeCode).collect(Collectors.toList());
                    param2.put("codeList", codeList);
                    // 差旅
                    travelSum = feeCostDetailService.sum(param2);
                    if (travelSum == null) {
                        travelSum = BigDecimal.ZERO;
                    }
                }

                // 非差旅
                BigDecimal noTravelSum = all.subtract(travelSum);

                projectCostVO.setTravelCost(travelSum);
                projectCostVO.setFeeCost(noTravelSum);
            }

        }

        return projectCostVO;
    }

    public String buildRequirementKey(Long projectId, MilepostDesignPlanDetail planDetail) {
        return planDetail.getErpCode() + "@" + DateUtils.format(planDetail.getDeliveryTime()) + "@" + planDetail.getId();
    }

    @Override
    public MaterialStatisticsVO getMaterialStatistics(Long materialId) {
        // 判断是成品、模组
        final ProjectBudgetMaterial projectBudgetMaterial = projectBudgetMaterialService.selectByPrimaryKey(materialId);
        Assert.notNull(projectBudgetMaterial, "信息不存在");

        final Long projectId = projectBudgetMaterial.getProjectId();
        Project project = projectBusinessService.selectByPrimaryKey(projectId);

        MaterialStatisticsVO materialStatisticsVO = new MaterialStatisticsVO();

        // 已设计物料总数
        int designNum = 0;
        // 已设计物料金额
        BigDecimal designAmount = BigDecimal.ZERO;
        // 已下单物料总数
        int orderNum = 0;
        // 已下单物料金额
        BigDecimal orderAmount = BigDecimal.ZERO;
        // 已接收物料总数
        int receiptNum = 0;
        // 已接收物料金额
        BigDecimal receiptAmount = BigDecimal.ZERO;
        // 已入库物料总数
        int deliverNum = 0;
        // 已入库物料金额
        BigDecimal deliverAmount = BigDecimal.ZERO;
        // 已领物料总数
        int recipientNum = 0;
        // 已领物料金额
        BigDecimal recipientAmount = BigDecimal.ZERO;

        // 待处理模组
        List<Long> needHandleIds = new ArrayList<>();

        Map<Long, BigDecimal> numberMap = new HashMap<>();

        BigDecimal cpNumber = BigDecimal.ONE;

        final Long parentId = projectBudgetMaterial.getParentId();
        if (parentId == null) {
            ProjectBudgetMaterialExample example = new ProjectBudgetMaterialExample();
            final ProjectBudgetMaterialExample.Criteria criteria = example.createCriteria();
            criteria.andDeletedFlagEqualTo(Boolean.FALSE);
            criteria.andParentIdEqualTo(materialId);

            final List<ProjectBudgetMaterial> projectBudgetMaterials = projectBudgetMaterialService.selectByExample(example);
            if (ListUtils.isNotEmpty(projectBudgetMaterials)) {
                projectBudgetMaterials.forEach(material -> {
                    needHandleIds.add(material.getId());

                    numberMap.put(material.getId(), material.getNumber() == null ? BigDecimal.ONE : BigDecimal.valueOf(material.getNumber()));
                });
            }

            cpNumber = projectBudgetMaterial.getNumber() == null ? BigDecimal.ONE : BigDecimal.valueOf(projectBudgetMaterial.getNumber());
//            needHandleIds.add(materialId);
//            numberMap.put(materialId, projectBudgetMaterial.getNumber() == null ? BigDecimal.ONE : BigDecimal.valueOf(projectBudgetMaterial
            //            .getNumber()));
        } else {
            needHandleIds.add(materialId);
            numberMap.put(materialId, projectBudgetMaterial.getNumber() == null ? BigDecimal.ONE :
                    BigDecimal.valueOf(projectBudgetMaterial.getNumber()));
        }

        if (needHandleIds.size() > 0) {

            for (Long needHandleId : needHandleIds) {

                // 模组数量
                final BigDecimal mzNumber = numberMap.get(needHandleId).multiply(cpNumber);

                MilepostDesignPlanDetailExample planDetailExample = new MilepostDesignPlanDetailExample();
                final MilepostDesignPlanDetailExample.Criteria criteria = planDetailExample.createCriteria();
                criteria.andDeletedFlagEqualTo(Boolean.FALSE);
                criteria.andProjectBudgetMaterialIdEqualTo(needHandleId);

                final List<MilepostDesignPlanDetail> milepostDesignPlanDetails = milepostDesignPlanDetailMapper.selectByExample(planDetailExample);
                if (ListUtils.isNotEmpty(milepostDesignPlanDetails)) {
                    // 物料信息
                    final List<Long> idList = milepostDesignPlanDetails.stream().map(MilepostDesignPlanDetail::getId).collect(Collectors.toList());

                    MilepostDesignPlanDetailExample planDetailExample2 = new MilepostDesignPlanDetailExample();
                    final MilepostDesignPlanDetailExample.Criteria criteria2 = planDetailExample2.createCriteria();
                    criteria2.andDeletedFlagEqualTo(Boolean.FALSE);
                    criteria2.andParentIdIn(idList);

                    final List<MilepostDesignPlanDetail> subMilepostDesignPlanDetails =
                            milepostDesignPlanDetailMapper.selectByExample(planDetailExample2);

                    if (ListUtils.isNotEmpty(subMilepostDesignPlanDetails)) {

                        /** 批量查询已下达量 **/
                        List<Long> requirementIdList = new ArrayList<>();
                        Map<Long, BigDecimal> releasedQuantityMap = new HashMap<>();
                        Map<String, Long> requirementIdMap = new HashMap<>();
                        for (MilepostDesignPlanDetail detail : subMilepostDesignPlanDetails) {
                            final String erpCode = detail.getErpCode();
                            final Date deliveryTime = detail.getDeliveryTime();
                            Long requirementId = purchaseOrderService.getRequirementIdByFilter(erpCode, projectId, deliveryTime);
                            if (requirementId != null) {
                                requirementIdList.add(requirementId);
                                requirementIdMap.put(buildRequirementKey(projectId, detail), requirementId);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(requirementIdList)) {
                            releasedQuantityMap = purchaseMaterialRequirementExtMapper.selectReleasedQuantityByIds(requirementIdList).stream()
                                    .collect(Collectors.toMap(PurchaseMaterialRequirementDto::getRequirementId,
                                            PurchaseMaterialRequirementDto::getReleasedQuantity, (a, b) -> a));
                        }

                        for (MilepostDesignPlanDetail detail : subMilepostDesignPlanDetails) {

                            final String erpCode = detail.getErpCode();
                            final Date deliveryTime = detail.getDeliveryTime();

                            // 单价
                            BigDecimal designCost = detail.getDesignCost() == null ? BigDecimal.ZERO : detail.getDesignCost();
                            // 数量
                            BigDecimal number = detail.getNumber() == null ? BigDecimal.ZERO : detail.getNumber();
                            number = number.multiply(mzNumber);
                            designNum = number.intValue() + designNum;
                            designAmount = designAmount.add(designCost.multiply(number));

                            if (projectId != null && StringUtils.isNotEmpty(erpCode)) {
                                BigDecimal orderNumD = BigDecimal.ZERO;
                                Long requirementId = requirementIdMap.get(buildRequirementKey(projectId, detail));
                                if (requirementId != null) {
                                    orderNumD = Optional.ofNullable(releasedQuantityMap.get(requirementId)).orElse(BigDecimal.ZERO);
                                }
//                                BigDecimal orderNumD = purchaseOrderService.getOrderNumSum(erpCode, projectId, deliveryTime);
                                //采购订单下达数量
                                orderNum = orderNum + orderNumD.intValue();
                                orderAmount = orderAmount.add(designCost.multiply(orderNumD));

                                //接受数量
                                List<String> transactionTypes = new ArrayList<>();
                                transactionTypes.add("RECEIVE");
                                transactionTypes.add("RETURN TO RECEIVING");

                                BigDecimal receiveUnitNum = purchaseProgressService.getTransactionQuantitySum(erpCode, project.getCode(),
                                        transactionTypes);
                                receiptNum = receiptNum + receiveUnitNum.intValue();
                                receiptAmount = receiptAmount.add(designCost.multiply(receiveUnitNum));

                                //入库数量
                                transactionTypes.clear();
                                transactionTypes.add("DELIVER");
                                transactionTypes.add("RETURN TO VENDOR");
                                BigDecimal deliverUnitNum = purchaseProgressService.getTransactionQuantitySum(erpCode, project.getCode(),
                                        transactionTypes);

                                deliverNum = deliverNum + deliverUnitNum.intValue();
                                deliverAmount = deliverAmount.add(designCost.multiply(deliverUnitNum));

                                //领用数量
                                BigDecimal recipientsNumSum = new BigDecimal(0);
                                recipientsNumSum = recipientsNumSum.add(materialGetService.getPickingQuantity(projectId, erpCode));
                                recipientsNumSum = recipientsNumSum.subtract(materialReturnService.getReturnQuantity(projectId, erpCode));

                                recipientNum = recipientNum + recipientsNumSum.intValue();
                                recipientAmount = recipientAmount.add(designCost.multiply(recipientsNumSum));
                            }
                        }
                    }
                }
            }
        }
        materialStatisticsVO.setMaterialId(materialId);
        materialStatisticsVO.setDesignNum(designNum);
        materialStatisticsVO.setDesignAmount(designAmount);
        materialStatisticsVO.setOrderNum(orderNum);
        materialStatisticsVO.setOrderAmount(orderAmount);
        materialStatisticsVO.setReceiptNum(receiptNum);
        materialStatisticsVO.setReceiptAmount(receiptAmount);
        materialStatisticsVO.setDeliverNum(deliverNum);
        materialStatisticsVO.setDeliverAmount(deliverAmount);
        materialStatisticsVO.setRecipientNum(recipientNum);
        materialStatisticsVO.setReceiptAmount(recipientAmount);

        return materialStatisticsVO;
    }
}
