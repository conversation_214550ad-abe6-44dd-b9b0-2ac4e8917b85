package com.midea.pam.ctc.wbs.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.ProjectWbsBudgetSummaryCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetSummaryDto;
import com.midea.pam.common.ctc.dto.ProjectWbsSummaryDto;
import com.midea.pam.common.ctc.dto.WbsDynamicFieldsDto;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectActivity;
import com.midea.pam.common.ctc.entity.ProjectActivityExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummary;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummaryExample;
import com.midea.pam.common.ctc.entity.WbsCustomizeRule;
import com.midea.pam.common.ctc.entity.WbsCustomizeRuleExample;
import com.midea.pam.common.ctc.entity.WbsTemplateInfo;
import com.midea.pam.common.ctc.entity.WbsTemplateRuleDetail;
import com.midea.pam.common.ctc.entity.WbsTemplateRuleDetailExample;
import com.midea.pam.common.enums.DescribeDisplayEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProjectWbsBudgetSummarySummaryTypeEnums;
import com.midea.pam.common.statistics.vo.ProjectWbsCostSystemVO;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.GenerateIDUtils;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.common.util.cache.ProjectWbsBudgetSummaryCacheUtils;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.ProjectActivityMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryMapper;
import com.midea.pam.ctc.mapper.WbsCustomizeRuleMapper;
import com.midea.pam.ctc.mapper.WbsTemplateInfoMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleDetailMapper;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetSummaryService;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ProjectWbsBudgetSummaryServiceImpl implements ProjectWbsBudgetSummaryService {

    @Resource
    private ProjectWbsBudgetSummaryMapper projectWbsBudgetSummaryMapper;
    @Resource
    private ProjectWbsBudgetSummaryExtMapper projectWbsBudgetSummaryExtMapper;
    @Resource
    private ProjectWbsBudgetExtMapper projectWbsBudgetExtMapper;
    @Resource
    private WbsTemplateRuleService wbsTemplateRuleService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectActivityMapper projectActivityMapper;
    @Resource
    private ProjectWbsBudgetMapper projectWbsBudgetMapper;
    @Autowired
    private RestTemplate restTemplate;

    @Resource
    private WbsTemplateInfoMapper wbsTemplateInfoMapper;

    @Resource
    private WbsTemplateRuleDetailMapper wbsTemplateRuleDetailMapper;

    @Resource
    private WbsCustomizeRuleMapper wbsCustomizeRuleMapper;
    /**
     * 项目详情-wbs预算 查询列表
     *
     * @param param
     * @return
     */
    @Override
    public List<Map<String, Object>> findByWbsBudgetList(Map<String, Object> param) {
        List<ProjectWbsBudgetDto> details = selectDetailByParam(param, ProjectWbsBudgetSummarySummaryTypeEnums.WBS);
        if (CollectionUtils.isNotEmpty(details)) {
            return ProjectWbsBudgetDto.dto2MapBatch(details);
        }
        return new ArrayList<>();
    }

    /**
     * 查询明细
     *
     * @param param
     * @param summaryTypeEnums
     * @return
     */
    private List<ProjectWbsBudgetDto> selectDetailByParam(Map<String, Object> param, ProjectWbsBudgetSummarySummaryTypeEnums summaryTypeEnums) {
        Long projectId = MapUtils.getLong(param, WbsBudgetFieldConstant.PROJECT_ID);
        if (Objects.isNull(projectId)) {
            throw new ApplicationBizException(String.format("参数%s不能为空", WbsBudgetFieldConstant.PROJECT_ID));
        }
        Project project = projectMapper.selectByPrimaryKey(projectId);
        if (Objects.isNull(project)) {
            throw new ApplicationBizException("项目不存在");
        }
        if (!Boolean.TRUE.equals(project.getWbsEnabled()) || Objects.isNull(project.getWbsTemplateInfoId())) {
            throw new ApplicationBizException("项目类型对应wbs模板未启用或不存在");
        }
        param.put(WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID, project.getWbsTemplateInfoId());

        /* wbs与activity维度不同，查询金额一个是查明细，一个是查汇总，要区分处理 */
        if (summaryTypeEnums.getType().equals(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType())) {
            // 活动事项剩余可用预算，查明细
            String minActivityRemainingCost = MapUtils.getString(param, "minActivityRemainingCost");
            if (StringUtils.isNotBlank(minActivityRemainingCost)) {
                param.put("minRemainingCost", minActivityRemainingCost);
            }
            String maxActivityRemainingCost = MapUtils.getString(param, "maxActivityRemainingCost");
            if (StringUtils.isNotBlank(maxActivityRemainingCost)) {
                param.put("maxRemainingCost", maxActivityRemainingCost);
            }
            List<Long> summaryIdList;
            // wbs剩余可用预算，查汇总
            String minWbsRemainingCost = MapUtils.getString(param, "minWbsRemainingCost");
            String maxWbsRemainingCost = MapUtils.getString(param, "maxWbsRemainingCost");
            if (StringUtils.isNotBlank(minWbsRemainingCost) || StringUtils.isNotBlank(maxWbsRemainingCost)) {
                ProjectWbsBudgetSummaryExample example = new ProjectWbsBudgetSummaryExample();
                ProjectWbsBudgetSummaryExample.Criteria criteria = example.createCriteria();

                criteria.andProjectIdEqualTo(projectId);
                criteria.andSummaryTypeEqualTo(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType());
                if (StringUtils.isNotBlank(minWbsRemainingCost)) {
                    criteria.andRemainingCostGreaterThanOrEqualTo(new BigDecimal(minWbsRemainingCost));
                }
                if (StringUtils.isNotBlank(maxWbsRemainingCost)) {
                    criteria.andRemainingCostLessThanOrEqualTo(new BigDecimal(maxWbsRemainingCost));
                }
                criteria.andProjectDetailSelectFlagEqualTo(true);
                criteria.andDeletedFlagEqualTo(false);

                List<ProjectWbsBudgetSummary> summaryList = projectWbsBudgetSummaryMapper.selectByExample(example);
                if (!CollectionUtils.isEmpty(summaryList)) {
                    summaryIdList = summaryList.stream().map(ProjectWbsBudgetSummary::getId).collect(Collectors.toList());
                    param.put("parentWbsIdList", summaryIdList);
                } else {
                    return new ArrayList<>();
                }
            }
        } else if (summaryTypeEnums.getType().equals(ProjectWbsBudgetSummarySummaryTypeEnums.ACTIVITY.getType())) {
            // wbs剩余可用预算，查明细
            String minWbsRemainingCost = MapUtils.getString(param, "minWbsRemainingCost");
            if (StringUtils.isNotBlank(minWbsRemainingCost)) {
                param.put("minRemainingCost", minWbsRemainingCost);
            }
            String maxWbsRemainingCost = MapUtils.getString(param, "maxWbsRemainingCost");
            if (StringUtils.isNotBlank(maxWbsRemainingCost)) {
                param.put("maxRemainingCost", maxWbsRemainingCost);
            }
            List<Long> summaryIdList;
            // 活动事项剩余可用预算，查汇总
            String minActivityRemainingCost = MapUtils.getString(param, "minActivityRemainingCost");
            String maxActivityRemainingCost = MapUtils.getString(param, "maxActivityRemainingCost");
            if (StringUtils.isNotBlank(minActivityRemainingCost) || StringUtils.isNotBlank(maxActivityRemainingCost)) {
                ProjectWbsBudgetSummaryExample example = new ProjectWbsBudgetSummaryExample();
                ProjectWbsBudgetSummaryExample.Criteria criteria = example.createCriteria();

                criteria.andProjectIdEqualTo(projectId);
                criteria.andSummaryTypeEqualTo(ProjectWbsBudgetSummarySummaryTypeEnums.ACTIVITY.getType());
                if (StringUtils.isNotBlank(minActivityRemainingCost)) {
                    criteria.andRemainingCostGreaterThanOrEqualTo(new BigDecimal(minActivityRemainingCost));
                }
                if (StringUtils.isNotBlank(maxActivityRemainingCost)) {
                    criteria.andRemainingCostLessThanOrEqualTo(new BigDecimal(maxActivityRemainingCost));
                }
                criteria.andProjectDetailSelectFlagEqualTo(true);
                criteria.andDeletedFlagEqualTo(false);

                List<ProjectWbsBudgetSummary> summaryList = projectWbsBudgetSummaryMapper.selectByExample(example);
                if (!CollectionUtils.isEmpty(summaryList)) {
                    summaryIdList = summaryList.stream().map(ProjectWbsBudgetSummary::getId).collect(Collectors.toList());
                    param.put("parentActivityIdList", summaryIdList);
                } else {
                    return new ArrayList<>();
                }
            }
        }

        Long wbsTemplateInfoId = project.getWbsTemplateInfoId();
        param.put(WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID, wbsTemplateInfoId);

        // 动态列参数组装
        List<WbsTemplateRuleCache> dynamicFields = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);
        if (!CollectionUtils.isEmpty(dynamicFields)) {
            List<WbsDynamicFieldsDto> dynamicFieldList = new ArrayList<>();
            for (WbsTemplateRuleCache field : dynamicFields) {
                if (StringUtils.isNotBlank(MapUtils.getString(param, field.getKey()))) {
                    WbsDynamicFieldsDto wbsDynamicParam = new WbsDynamicFieldsDto();
                    wbsDynamicParam.setKey(field.getKey());
                    wbsDynamicParam.setValue(MapUtils.getString(param, field.getKey()));
                    dynamicFieldList.add(wbsDynamicParam);
                }
            }
            if (!CollectionUtils.isEmpty(dynamicFieldList)) {
                param.put("dynamicFieldList", dynamicFieldList);
            }
        }
        return projectWbsBudgetExtMapper.listByParam(param);
    }

    /**
     * 项目详情-wbs预算 查询wbs类型预算
     * （此方法是查project_wbs_budget_summary表汇总，此实现方案暂时搁置）
     *
     * @param param
     * @return
     */
    @Override
    public List<Map<String, Object>> findByWbsBudgetSummary(Map<String, Object> param) {
        List<ProjectWbsBudgetDto> details = selectDetailByParam(param, ProjectWbsBudgetSummarySummaryTypeEnums.WBS);
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>();
        }
        Long projectId = MapUtils.getLong(param, WbsBudgetFieldConstant.PROJECT_ID);
        Project project = projectMapper.selectByPrimaryKey(projectId);

        return buildSummaryTree(project.getWbsTemplateInfoId(), project.getCode(), details, ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType());
    }

    /**
     * 项目详情-wbs预算 查询活动事项类型预算
     * （此方法是查project_wbs_budget_summary表汇总，此实现方案暂时搁置）
     *
     * @param param
     * @return
     */
    @Override
    public List<Map<String, Object>> findByActivityBudgetSummary(Map<String, Object> param) {
        List<ProjectWbsBudgetDto> details = selectDetailByParam(param, ProjectWbsBudgetSummarySummaryTypeEnums.ACTIVITY);
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>();
        }
        Long projectId = MapUtils.getLong(param, WbsBudgetFieldConstant.PROJECT_ID);
        Project project = projectMapper.selectByPrimaryKey(projectId);

        return buildSummaryTree(project.getWbsTemplateInfoId(), project.getCode(), details, ProjectWbsBudgetSummarySummaryTypeEnums.ACTIVITY.getType());
    }

    /**
     * 生成预算汇总树
     *
     * @param wbsTemplateInfoId
     * @param projectCode
     * @param detailDtoList
     * @param summaryType
     * @return
     */
    private List<Map<String, Object>> buildSummaryTree(Long wbsTemplateInfoId,
                                                       String projectCode,
                                                       List<ProjectWbsBudgetDto> detailDtoList,
                                                       String summaryType) {
        if (CollectionUtils.isEmpty(detailDtoList)) {
            return new ArrayList<>();
        }
        // 子节点
        List<Map<String, Object>> detailNode = ProjectWbsBudgetDto.dto2MapBatch(detailDtoList);
        // 上一层summaryId
        List<Long> parentIdList = detailDtoList.stream().map(a -> summaryType.equals(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType()) ? a.getParentWbsId() : a.getParentActivityId()).collect(Collectors.toList());
        // 动态列
        List<WbsDynamicFieldsDto> dynamicFields = wbsTemplateRuleService.getWbsDynamicFields(wbsTemplateInfoId);
        // 生成树
        List<Map<String, Object>> result = buildParentNode(detailNode, projectCode, parentIdList, summaryType, new HashMap<>(), dynamicFields, true);
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        // top只有1个，项目层
        return result;
    }

    /**
     * 构建父级树（从detail开始）
     *
     * @param childNodes       子节点
     * @param projectCode      象奴编号
     * @param parentSummaryIds 上一层summaryId
     * @param summaryType      汇总类型（wbs、activity）
     * @param topNodeMap       最顶层集合
     * @param dynamicFields    动态列
     * @param detailFlag       明细层标记（首次传入是true，递归后是false）
     * @return
     */
    private List<Map<String, Object>> buildParentNode(List<Map<String, Object>> childNodes,
                                                      String projectCode,
                                                      List<Long> parentSummaryIds,
                                                      String summaryType,
                                                      Map<Long, Map<String, Object>> topNodeMap,
                                                      List<WbsDynamicFieldsDto> dynamicFields,
                                                      boolean detailFlag) {

        /* 1、获取上一层汇总记录 */
        ProjectWbsBudgetSummaryExample example = new ProjectWbsBudgetSummaryExample();
        example.createCriteria().andIdIn(parentSummaryIds).andDeletedFlagEqualTo(false).andSummaryTypeEqualTo(summaryType);
        List<ProjectWbsBudgetSummary> parentDtoList = projectWbsBudgetSummaryMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(parentDtoList)) {
            return childNodes;
        }

        // 上一层节点
        List<Map<String, Object>> parentNodes = new ArrayList<>();
        Map<String, Object> parentNode;

        for (ProjectWbsBudgetSummary parent : parentDtoList) {
            parentNode = ProjectWbsBudgetSummaryDto.entity2MapQueryJson(projectCode, parent, dynamicFields);
            // 判断是否top节点
            if (null == (parent.getParentId()) || Long.valueOf(-1L).equals(parent.getParentId())) {
                if (topNodeMap.containsKey(parent.getId())) {
                    parentNode = topNodeMap.get(parent.getId());
                } else {
                    topNodeMap.put(parent.getId(), parentNode);
                }
            }

            /* 获取子集 */
            List<Map<String, Object>> childs;
            // 明细层：parent.id = child.parent_wbs_id || child.parent_activity_id
            if (detailFlag) {
                childs = childNodes.stream().filter(a -> parent.getId().equals(summaryType.equals(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType()) ? MapUtils.getLong(a, WbsBudgetFieldConstant.PARENT_WBS_ID) : MapUtils.getLong(a, WbsBudgetFieldConstant.PARENT_ACTIVITY_ID))).collect(Collectors.toList());
            }
            // 汇总层：parent.id = child.parent_id
            else {
                childs = childNodes.stream().filter(a -> parent.getId().equals(MapUtils.getLong(a, WbsBudgetFieldConstant.PARENT_ID))).collect(Collectors.toList());
            }

            /* 顶层node要处理不同层级的子集累加 */
            if (parentNode.containsKey(WbsBudgetFieldConstant.CHILDS) && !CollectionUtils.isEmpty(childs)) {
                List<Map<String, Object>> nodeChilds = (List<Map<String, Object>>) parentNode.get(WbsBudgetFieldConstant.CHILDS);
                nodeChilds.addAll(childs);
                parentNode.put(WbsBudgetFieldConstant.CHILDS, nodeChilds);
            } else if (CollectionUtils.isNotEmpty(childs)) {
                if (detailFlag) {
                    parentNode.put(WbsBudgetFieldConstant.DETAILS, childs);
                } else {
                    parentNode.put(WbsBudgetFieldConstant.CHILDS, childs);
                }
                // 汇总层最底层，拼装WBS + ACTIVITY数据
                if (Boolean.TRUE.equals(parent.getProjectDetailSelectFlag()) && CollectionUtils.isNotEmpty(dynamicFields)) {
                    for (WbsDynamicFieldsDto field : dynamicFields) {
                        parentNode.put(field.getKey(), MapUtils.getString(childs.get(0), field.getKey()));
                    }
                    parentNode.put(WbsBudgetFieldConstant.ACTIVITY_CODE, MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_CODE));
                    parentNode.put(WbsBudgetFieldConstant.ACTIVITY_NAME, MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_NAME));
                    parentNode.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_TYPE));
                }
            }

            String descriptionString = "";
            Object description = parentNode.get(WbsBudgetFieldConstant.DESCRIPTION);
            if (description != null){
                descriptionString = description.toString();
            }

            Object summaryCode = parentNode.get(WbsBudgetFieldConstant.SUMMARY_CODE);
            if (summaryCode != null) {
                String summaryCodeString = summaryCode.toString();
                String lastPart = summaryCodeString.substring(summaryCodeString.lastIndexOf('-') + 1);
                Object wbsTemplateRuleId = parentNode.get(WbsBudgetFieldConstant.WBS_TEMPLATE_RULE_ID);
                if (wbsTemplateRuleId != null) {
                    Long wbsTemplateRuleIdLong = Long.parseLong(wbsTemplateRuleId.toString());
                    WbsTemplateRuleDetailExample wbsTemplateRuleDetailExample = new WbsTemplateRuleDetailExample();
                    WbsTemplateRuleDetailExample.Criteria criteria = wbsTemplateRuleDetailExample.createCriteria();
                    criteria.andWbsTemplateRuleIdEqualTo(wbsTemplateRuleIdLong).andCodeEqualTo(lastPart).andDeletedFlagEqualTo(false);
                    List<WbsTemplateRuleDetail> wbsTemplateRuleDetails = wbsTemplateRuleDetailMapper.selectByExample(wbsTemplateRuleDetailExample);
                    if (CollectionUtils.isNotEmpty(wbsTemplateRuleDetails)) {
                        WbsTemplateRuleDetail wbsTemplateRuleDetail = wbsTemplateRuleDetails.get(0);
                        parentNode.put(WbsBudgetFieldConstant.DESCRIPTION, wbsTemplateRuleDetail.getDescription());
                    }
                }
            }

            Map<Integer, String> fieldMap = new TreeMap<>();
            for (Map.Entry<String, Object> entry : parentNode.entrySet()) {
                if (entry.getKey().startsWith("field_")) {
                    int fieldNum = Integer.parseInt(entry.getKey().substring(6));
                    fieldMap.put(fieldNum, entry.getValue().toString());
                }
            }

            if (!fieldMap.isEmpty()) {
                parentNode.put(WbsBudgetFieldConstant.DESCRIPTION, descriptionString);
            }

            parentNodes.add(parentNode);
        }

        /* 判断当前是否top节点，如果是就直接返回 */
        boolean returnFlag = parentDtoList.stream().anyMatch(a -> Objects.isNull(a.getParentId()) || Objects.equals(-1L, a.getParentId()));
        if (returnFlag) {
            return parentNodes;
        }
        // 当前不是top节点，继续遍历上层节点
        List<Long> parentSummaryIdList = parentDtoList.stream().map(ProjectWbsBudgetSummary::getParentId).collect(Collectors.toList());
        return buildParentNode(parentNodes, projectCode, parentSummaryIdList, summaryType, topNodeMap, dynamicFields, false);
    }

    /**
     * 统计activityCode为System的需求预算
     *
     * @param map
     * @return
     */
    @Deprecated
    private Map<String, Object> setSystemActivityByWbs(Map<String, Object> map) {
        BigDecimal demandCost = getSystemDemandCost(map.get("projectId"), map.get("projectCode") + "-" + map.get("wbsFullCode"));
        if (Objects.nonNull(demandCost)) {
            Map<String, Object> stringObjectMap = new HashMap<>();
            stringObjectMap.put("id", GenerateIDUtils.generateSixteenBitID());
            stringObjectMap.put(WbsBudgetFieldConstant.PRICE, BigDecimal.ZERO);//预算金额
            stringObjectMap.put(WbsBudgetFieldConstant.DEMAND_COST, demandCost);//需求预算
            stringObjectMap.put(WbsBudgetFieldConstant.REMAINING_COST, BigDecimal.ZERO);//剩余可用预算
            stringObjectMap.put("parentWbsId", map.get("parentWbsId"));
            stringObjectMap.put("projectId", map.get("projectId"));
            stringObjectMap.put("projectCode", map.get("projectCode"));
            stringObjectMap.put("wbsFullCode", map.get("wbsFullCode"));
            stringObjectMap.put("wbsLastCode", map.get("wbsLastCode"));
            stringObjectMap.put("activityCode", "System");
            stringObjectMap.put("activityOrderNo", "1000");
            return stringObjectMap;
        } else {
            return null;
        }
    }

    /**
     * 统计activityCode为System的需求预算
     *
     * @param map
     * @return
     */
    @Deprecated
    private Map<String, Object> setSystemActivityByActivity(Map<String, Object> map) {
        BigDecimal demandCost = getSystemDemandCost(map.get("projectId"), map.get("projectCode") + "-" + map.get("wbsFullCode"));
        if (Objects.nonNull(demandCost)) {
            Map<String, Object> stringObjectMap = new HashMap<>();
            stringObjectMap.put("id", GenerateIDUtils.generateSixteenBitID());
            stringObjectMap.put(WbsBudgetFieldConstant.PRICE, BigDecimal.ZERO);//预算金额
            stringObjectMap.put(WbsBudgetFieldConstant.DEMAND_COST, demandCost);//需求预算
            stringObjectMap.put(WbsBudgetFieldConstant.REMAINING_COST, BigDecimal.ZERO);//剩余可用预算
            stringObjectMap.put("parentActivityId", map.get("parentActivityId"));
            stringObjectMap.put("projectId", map.get("projectId"));
            stringObjectMap.put("projectCode", map.get("projectCode"));
            stringObjectMap.put("wbsFullCode", map.get("wbsFullCode"));
            stringObjectMap.put("wbsLastCode", map.get("wbsLastCode"));
            stringObjectMap.put("activityCode", "System");
            stringObjectMap.put("activityOrderNo", "1000");
            stringObjectMap.put("dynamicValues", map.get("dynamicValues"));
            return stringObjectMap;
        } else {
            return null;
        }

    }

    /**
     * 调用查询System需求预算汇总接口
     *
     * @param projectId
     * @param wbsSummaryCode
     * @return
     */
    @Deprecated
    private BigDecimal getSystemDemandCost(Object projectId, Object wbsSummaryCode) {
        ProjectWbsSummaryDto projectWbsSummaryDto = new ProjectWbsSummaryDto();
        projectWbsSummaryDto.setProjectId(Long.parseLong(String.valueOf(projectId)));
        projectWbsSummaryDto.setWbsSummaryCode(String.valueOf(wbsSummaryCode));
        projectWbsSummaryDto.setUnitId(SystemContext.getUnitId());
        String url = String.format("%sstatistics/project/wbsCost/system", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        DataResponse<ProjectWbsCostSystemVO> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsCostSystemVO>>() {
        });
        if (Objects.nonNull(response.getData())) {
            return response.getData().getDemandCost();
        } else {
            return null;
        }
    }

    /**
     * 保存汇总数据（wbs、activity）
     *
     * @param projectId
     * @return price 汇总预算
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal saveWbsBudgetSummary(Long projectId) {
        Project project = projectMapper.selectByPrimaryKey(projectId);

        // unitId优先取project的parendt - unitId
        Long unitId = SystemContext.getUnitId();
        Unit unit = CacheDataUtils.findUnitById(project.getUnitId());
        unitId = unit != null ? unit.getParentId() : unitId;

        if (!Boolean.TRUE.equals(project.getWbsEnabled()) || Objects.isNull(project.getWbsTemplateInfoId())) {
            throw new ApplicationBizException("项目类型对应wbs模板未启用或不存在");
        }
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        List<ProjectWbsBudgetDto> projectWbsBudgetDtoList = projectWbsBudgetExtMapper.listByParam(param);
        if (CollectionUtils.isEmpty(projectWbsBudgetDtoList)) {
            throw new ApplicationBizException("项目wbs预算不存在");
        }
        // 删除历史记录
        projectWbsBudgetSummaryExtMapper.deleteByProjectId(projectId);

        List<Map<String, Object>> dataList = ProjectWbsBudgetDto.dto2MapBatch(projectWbsBudgetDtoList);

        // wbs汇总：项目层
        ProjectWbsBudgetSummary topSummary = new ProjectWbsBudgetSummary();
        sumCost(topSummary, dataList);
        topSummary.setProjectId(project.getId());
        topSummary.setParentId(-1L);
        topSummary.setSummaryCode(project.getCode());
        topSummary.setActivityName(null);
        topSummary.setSummaryType(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType());
        topSummary.setProjectDetailSelectFlag(false);
        topSummary.setDeletedFlag(false);
        topSummary.setVersion(1L);
        topSummary.setWbsLayer("项目");
        projectWbsBudgetSummaryMapper.insert(topSummary);
        // 保存wbs汇总
        saveWbsSummary(topSummary, dataList, project.getWbsTemplateInfoId());

        // 保存activity汇总
        saveActivitySummary(project, dataList, unitId);

        // 更新缓存
//        saveCache(projectId);
        return topSummary.getPrice();
    }

    /**
     * 保存wbs汇总
     *
     * @param topSummary
     * @param dataList
     * @param wbsTemplateInfoId
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWbsSummary(ProjectWbsBudgetSummary topSummary,
                               List<Map<String, Object>> dataList,
                               Long wbsTemplateInfoId) {

        // 动态列参数组装
        List<WbsDynamicFieldsDto> dynamicFields = wbsTemplateRuleService.getWbsDynamicFields(wbsTemplateInfoId);
        saveWbsSummary(topSummary, 0, dynamicFields, dataList);
    }

    /**
     * 保存wbs汇总
     *
     * @param parentSummary 汇总
     * @param index         动态列序号
     * @param dynamicFields 动态列
     * @param dataList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWbsSummary(ProjectWbsBudgetSummary parentSummary,
                               int index,
                               List<WbsDynamicFieldsDto> dynamicFields,
                               List<Map<String, Object>> dataList) {

        // 动态列
        WbsDynamicFieldsDto dynamicField = dynamicFields.get(index);
        // Map<动态列field , List<Data>
        Map<String, List<Map<String, Object>>> groupMap = dataList.stream().collect(Collectors.groupingBy(a -> a.get(dynamicField.getKey()) + ""));
        // Map<动态列field , SummaryCode>
        Map<String, String> summaryCodeMap = new HashMap<>();
        // 批量新增汇总
        List<ProjectWbsBudgetSummary> batchInsertWbsBudgetSummaryList = new ArrayList<>();

        ProjectWbsBudget wbsBudget;
        for (String k : groupMap.keySet()) {
            List<Map<String, Object>> v = groupMap.get(k);

            ProjectWbsBudgetSummary entity = new ProjectWbsBudgetSummary();
            // 汇总
            if (!CollectionUtils.isEmpty(v)) {
                sumCost(entity, v);
            }
            entity.setProjectId(parentSummary.getProjectId());
            entity.setParentId(parentSummary.getId());
            entity.setSummaryCode(parentSummary.getSummaryCode() + "-" + k);
            entity.setActivityName(null);
            entity.setSummaryType(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType());
            entity.setDeletedFlag(false);
            entity.setVersion(1L);
            entity.setWbsLayer(dynamicField.getLable());
            entity.setWbsTemplateRuleId(dynamicField.getWbsTemplateRuleId());

            summaryCodeMap.put(k, entity.getSummaryCode());

            // 最后一层动态列
            if (index == dynamicFields.size() - 1) {
                entity.setProjectDetailSelectFlag(true);
                if (!CollectionUtils.isEmpty(v)) {
                    // 设置描述（wbs描述去重，2000长度拦截）
                    Set<String> desSet = new HashSet<>();
                    StringBuilder desString = new StringBuilder();
                    for (Map map : v) {
                        String des = MapUtils.getString(map, WbsBudgetFieldConstant.DESCRIPTION);
                        if (StringUtils.isNotBlank(des) && !desSet.contains(des)) {
                            desSet.add(des);
                            if (desString.length() == 0) {
                                desString.append(des);
                            } else {
                                desString.append("," + des);
                            }
                        }
                    }
                    entity.setDescription(desString.length() > 2000 ? desString.substring(0, 1999) : desString.toString());
                }
                batchInsertWbsBudgetSummaryList.add(entity);
            } else {
                entity.setProjectDetailSelectFlag(false);
                batchInsertWbsBudgetSummaryList.add(entity);
            }
        }

        // 批量插入汇总
        if (CollectionUtils.isNotEmpty(batchInsertWbsBudgetSummaryList)) {
            projectWbsBudgetSummaryExtMapper.batchInsert(batchInsertWbsBudgetSummaryList);
        }

        // 批量更新预算
        List<ProjectWbsBudget> batchUpdateWbsBudgetList = new ArrayList<>();

        for (String k : groupMap.keySet()) {
            List<Map<String, Object>> v = groupMap.get(k);
            String summaryCode = summaryCodeMap.get(k);
            /* 最后一层动态列 批量更新预算 */
            if (index == dynamicFields.size() - 1) {
                ProjectWbsBudgetSummary summary = batchInsertWbsBudgetSummaryList.stream().filter(a -> a.getSummaryCode().equals(summaryCode)).findFirst().orElse(null);
                if (null != summary) {
                    // 重新赋值汇总的parentId
                    for (Map<String, Object> map : v) {
                        wbsBudget = new ProjectWbsBudgetDto();
                        wbsBudget.setId(MapUtils.getLong(map, "id"));
                        wbsBudget.setParentWbsId(summary.getId());
                        batchUpdateWbsBudgetList.add(wbsBudget);
                    }
                }
            }
            /* 非最后一层动态列 递归 */
            else {
                ProjectWbsBudgetSummary summary = batchInsertWbsBudgetSummaryList.stream().filter(a -> a.getSummaryCode().equals(summaryCode)).findFirst().orElse(null);
                if (null != summary) {
                    // 递归
                    saveWbsSummary(summary, index + 1, dynamicFields, v);
                }
            }
        }

        // 批量更新预算
        if (CollectionUtils.isNotEmpty(batchUpdateWbsBudgetList)) {
            // 批量更新parentWbsId
            projectWbsBudgetExtMapper.batchUpdate(batchUpdateWbsBudgetList);
        }
    }

    /**
     * 汇总金额
     *
     * @param entity
     * @param childs
     */
    private void sumCost(ProjectWbsBudgetSummary entity, List<Map<String, Object>> childs) {
        BigDecimal sumPrice = BigDecimal.ZERO;
        BigDecimal sumBaselineCost = BigDecimal.ZERO;
        BigDecimal sumDemandCost = BigDecimal.ZERO;
        BigDecimal sumOnTheWayCost = BigDecimal.ZERO;
        BigDecimal sumIncurredCost = BigDecimal.ZERO;
        BigDecimal sumRemainingCost = BigDecimal.ZERO;
        BigDecimal sumChangeAccumulateCost = BigDecimal.ZERO;
        for (int i = 0; i < childs.size(); i++) {
            Map child = childs.get(i);
            if (child.containsKey(WbsBudgetFieldConstant.PRICE)) {
                sumPrice = sumPrice.add(new BigDecimal(child.get(WbsBudgetFieldConstant.PRICE) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.BASELINE_COST)) {
                sumBaselineCost = sumBaselineCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.BASELINE_COST) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.DEMAND_COST)) {
                sumDemandCost = sumDemandCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.DEMAND_COST) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.ON_THE_WAY_COST)) {
                sumOnTheWayCost = sumOnTheWayCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.ON_THE_WAY_COST) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.INCURRED_COST)) {
                sumIncurredCost = sumIncurredCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.INCURRED_COST) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.REMAINING_COST)) {
                sumRemainingCost = sumRemainingCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.REMAINING_COST) + ""));
            }
            if (child.containsKey(WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST)) {
                sumChangeAccumulateCost = sumChangeAccumulateCost.add(new BigDecimal(child.get(WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST) + ""));
            }
        }
        entity.setPrice(sumPrice);
        entity.setBaselineCost(sumBaselineCost);
        entity.setDemandCost(sumDemandCost);
        entity.setOnTheWayCost(sumOnTheWayCost);
        entity.setIncurredCost(sumIncurredCost);
        entity.setRemainingCost(sumRemainingCost);
        entity.setChangeAccumulateCost(sumChangeAccumulateCost);
    }

    /**
     * 保存activity汇总
     *
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveActivitySummary(Project project, List<Map<String, Object>> dataList, Long unitId) {
        // 获取data中唯一的orderNo
        List<String> groupOrderNo = dataList.stream().map(a -> a.get(WbsBudgetFieldConstant.ACTIVITY_ORDER_NO) + "").distinct().collect(Collectors.toList());

        // 上级order集合 <orderNo,下级>
        Map<String, HashSet<String>> parentOrderMap = new HashMap<>();

        // 第一层orderNo
        HashSet<String> firstOrderNoSet = new HashSet<>();

        for (String orderNo : groupOrderNo) {
            // 组装父层orderNo
            setParentOrderNo(orderNo, parentOrderMap, firstOrderNoSet);
        }
        // Map orderNo 分组 <orderNo, data>
        Map<String, List<Map<String, Object>>> groupMap = dataList.stream().collect(Collectors.groupingBy(a -> MapUtils.getString(a, WbsBudgetFieldConstant.ACTIVITY_ORDER_NO)));

        // parentOrderMap获取活动事项列表 <orderNo, ProjectActivity>
        Map<String, ProjectActivity> parentActivity = getOrderMapActivity(unitId, parentOrderMap);

        // 批量更新预算
        List<ProjectWbsBudget> batchUpdateWbsBudget = new ArrayList<>();
        for (String firstOrderNo : firstOrderNoSet) {
            saveActivitySummary(firstOrderNo, project.getCode(), project.getId(), -1L, parentActivity, parentOrderMap, groupMap, batchUpdateWbsBudget);
        }
        // 批量更新预算
        if (CollectionUtils.isNotEmpty(batchUpdateWbsBudget)) {
            // 批量更新parentActivityId
            projectWbsBudgetExtMapper.batchUpdate(batchUpdateWbsBudget);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public ProjectWbsBudgetSummary saveActivitySummary(String parentOrderNo,
                                                       String projectCode,
                                                       Long projectId,
                                                       Long parentId,
                                                       Map<String, ProjectActivity> parentActivity,
                                                       Map<String, HashSet<String>> parentOrderMap,
                                                       Map<String, List<Map<String, Object>>> groupMap,
                                                       List<ProjectWbsBudget> batchUpdateWbsBudget) {

        ProjectWbsBudgetSummary entity = new ProjectWbsBudgetSummary();
        entity.setSummaryType(ProjectWbsBudgetSummarySummaryTypeEnums.ACTIVITY.getType());
        entity.setDeletedFlag(false);
        entity.setVersion(1L);
        entity.setProjectId(projectId);
        entity.setParentId(parentId);
        entity.setProjectDetailSelectFlag(false);

        // groupMap能匹配到就是最底层
        if (groupMap.containsKey(parentOrderNo)) {
            List<Map<String, Object>> childs = groupMap.get(parentOrderNo);
            if (CollectionUtils.isEmpty(childs)) {
                return entity;
            }
            // 汇总金额
            sumCost(entity, childs);
            entity.setActivityName(MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_NAME));
            entity.setActivityType(MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_TYPE));
            entity.setActivityCode(MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_CODE));
            entity.setActivityOrderNo(MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_ORDER_NO));
            entity.setProjectDetailSelectFlag(true);
            entity.setSummaryCode(projectCode + "." + MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_CODE));
            projectWbsBudgetSummaryMapper.insert(entity);
            ProjectWbsBudgetDto wbsBudget;
            // 重新赋值汇总的parentId
            for (Map<String, Object> map : childs) {
                wbsBudget = new ProjectWbsBudgetDto();
                wbsBudget.setId(MapUtils.getLong(map, "id"));
                wbsBudget.setParentActivityId(entity.getId());
                // 添加批量更新
                batchUpdateWbsBudget.add(wbsBudget);
            }
        } else {
            ProjectActivity currentActivity = parentActivity.get(parentOrderNo);
            entity.setActivityName(currentActivity.getName());
            entity.setActivityType(currentActivity.getType());
            entity.setActivityCode(currentActivity.getCode());
            entity.setActivityOrderNo(currentActivity.getOrderNo());
            entity.setSummaryCode(projectCode + "." + currentActivity.getCode());
            // 初始化一个0金额
            sumCost(entity, new ArrayList<>());
            projectWbsBudgetSummaryMapper.insert(entity);

            HashSet<String> childOrder = parentOrderMap.get(parentOrderNo);
            List<ProjectWbsBudgetSummary> childs = new ArrayList<>();
            for (String order : childOrder) {
                ProjectWbsBudgetSummary child = saveActivitySummary(order, entity.getSummaryCode(), projectId, entity.getId(), parentActivity, parentOrderMap, groupMap, batchUpdateWbsBudget);
                childs.add(child);
            }
            if (!CollectionUtils.isEmpty(childs)) {
                List<Map<String, Object>> childMapList = ProjectWbsBudgetSummaryDto.entity2MapBatch(childs);
                sumCost(entity, childMapList);
                projectWbsBudgetSummaryMapper.updateByPrimaryKey(entity);
            }
        }
        return entity;
    }

    /**
     * 拼接父层orderNo
     *
     * @param orderNo
     * @param parentOrderMap
     * @param firstOrderNoSet
     */
    private void setParentOrderNo(String orderNo,
                                  Map<String, HashSet<String>> parentOrderMap,
                                  Set<String> firstOrderNoSet) {
        if (orderNo.contains(".")) {
            // 获取父orderNo，将当前orderNo放到 parentOrderMap里
            if (parentOrderMap.containsKey(StringUtils.substringBeforeLast(orderNo, "."))) {
                parentOrderMap.get(StringUtils.substringBeforeLast(orderNo, ".")).add(orderNo);
            } else {
                HashSet<String> set = new HashSet();
                set.add(orderNo);
                parentOrderMap.put(StringUtils.substringBeforeLast(orderNo, "."), set);
            }
            setParentOrderNo(StringUtils.substringBeforeLast(orderNo, "."), parentOrderMap, firstOrderNoSet);
        } else {
            // 保存第一层orderNo
            firstOrderNoSet.add(orderNo);
        }
    }

    /**
     * parentOrderMap获取活动事项列表
     *
     * @param parentOrderMap
     * @return <orderNo, ProjectActivity>
     */
    private Map<String, ProjectActivity> getOrderMapActivity(Long unitId, Map<String, HashSet<String>> parentOrderMap) {
        if (parentOrderMap.isEmpty()) {
            return new HashMap<>();
        }
        List<String> orderNoList = new ArrayList<>(parentOrderMap.keySet());
        ProjectActivityExample example = new ProjectActivityExample();
        example.createCriteria().andUnitIdEqualTo(unitId).andOrderNoIn(orderNoList);
        List<ProjectActivity> list = projectActivityMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(ProjectActivity::getOrderNo, Function.identity(), (a1, a2) -> a1));
    }


    /**
     * 更新缓存
     *
     * @param projectId 项目id
     * @return
     */
    @Override
    public boolean saveCache(Long projectId) {
        ProjectWbsBudgetSummaryExample example = new ProjectWbsBudgetSummaryExample();
        example.createCriteria()
                .andDeletedFlagEqualTo(false)
                .andProjectIdEqualTo(projectId);
        List<ProjectWbsBudgetSummary> list = projectWbsBudgetSummaryMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        List<ProjectWbsBudgetSummaryCache> cacheList = BeanConverter.copy(list, ProjectWbsBudgetSummaryCache.class);

        ProjectWbsBudgetSummaryCacheUtils.removeAllCache(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType(), projectId);
        ProjectWbsBudgetSummaryCacheUtils.removeAllCache(ProjectWbsBudgetSummarySummaryTypeEnums.ACTIVITY.getType(), projectId);
        return ProjectWbsBudgetSummaryCacheUtils.putCacheBatch(cacheList);
    }

    @Override
    public List<ProjectWbsBudgetDto> selectDetailByProject(Long projectId) {
        Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        List<ProjectWbsBudgetDto> details = selectDetailByParam(param, ProjectWbsBudgetSummarySummaryTypeEnums.WBS);
        if (CollectionUtils.isNotEmpty(details)) {
            return details;
        }
        return new ArrayList<>();
    }


}
