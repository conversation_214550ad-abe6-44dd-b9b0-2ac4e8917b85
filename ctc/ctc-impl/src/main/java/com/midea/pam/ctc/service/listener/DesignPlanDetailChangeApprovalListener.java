package com.midea.pam.ctc.service.listener;

import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanChangeRecordDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailChangeDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanChangeRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.vo.DesignPlanDetailChangeHistoryVO;
import com.midea.pam.common.enums.AttachmentStatus;
import com.midea.pam.common.enums.ChangeType;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.PurchaseMaterialRequirementDeliveryAddressHistoryEditTypeEnum;
import com.midea.pam.common.enums.PurchaseMaterialRequirementDeliveryAddressHistoryTypeEnum;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DesignPlanDetailGenerateRequire;
import com.midea.pam.ctc.common.enums.MilepostDesignPlanDetailModelStatus;
import com.midea.pam.ctc.common.enums.ReleaseDetailStatus;
import com.midea.pam.ctc.common.redis.RedisLuaScriptServer;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.MilepostDesignPlanChangeRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.PurchaseMaterialReleaseDetailService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementDeliveryAddressHistoryService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.TicketTasksService;
import com.midea.pam.ctc.service.event.DesignPlanDetailChangeApprovalEvent;
import com.midea.pam.ctc.service.event.UpdateMaterialCostRequirementEvent;
import com.midea.pam.ctc.service.helper.MilepostDesignPlanHelper;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 详设交付-变更审批事件.监听.
 */
public class DesignPlanDetailChangeApprovalListener implements ApplicationListener<DesignPlanDetailChangeApprovalEvent> {

    private final static Logger logger = LoggerFactory.getLogger(DesignPlanDetailChangeApprovalListener.class);

    private final static Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    private final static Long MAX_WAIT_TIME_SYNC = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private MilepostDesignPlanChangeRecordMapper milepostDesignPlanChangeRecordMapper;

    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;

    @Resource
    private MilepostDesignPlanDetailChangeService milepostDesignPlanDetailChangeService;

    @Resource
    private CtcAttachmentService ctcAttachmentService;

    @Resource
    private PurchaseMaterialRequirementService purchaseMaterialRequirementService;

    @Resource
    private MaterialExtService materialExtService;

    @Resource
    private PurchaseMaterialReleaseDetailService purchaseMaterialReleaseDetailService;

    @Resource
    private ProjectService projectService;

    @Resource
    private MilepostDesignPlanHelper milepostDesignPlanHelper;

    @Resource
    private TicketTasksService ticketTasksService;

    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;

    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;

    @Resource
    private WorkflowCallbackService workflowCallbackService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private RedisLuaScriptServer redisLuaScriptServer;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private PurchaseMaterialRequirementDeliveryAddressHistoryService deliveryAddressHistoryService;

    @Transactional(rollbackFor = Exception.class, timeout = 600)
    @Override
    @Async
    public void onApplicationEvent(final DesignPlanDetailChangeApprovalEvent event) {
        logger.info("详细设计变更审批公共回调的异步处理参数为:{}", JsonUtils.toString(event));
        updateBatchStatus(event.getDto());
    }

    @Transactional(rollbackFor = Exception.class, timeout = 600)
    public void updateBatchStatus(MilepostDesignPlanDetailDto dto) {
        logger.info("详细设计变更审批公共回调的updateBatchStatus的dto：{}", JsonUtils.toString(dto));
        Long formInstanceId = dto.getFormInstanceId();
        String fdInstanceId = dto.getFdInstanceId();
        String formUrl = dto.getFormUrl();
        String eventName = dto.getEventName();
        Long createUserId = dto.getCreateUserId();
        Integer checkStatus = dto.getCheckStatus();
        String lockName = String.format("DesignPlanDetailSubmitApproval_updateBatchStatusForSubmitWithDetail_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    MilepostDesignPlanChangeRecordDto recordDto = this.getById(formInstanceId);
                    Guard.notNull(recordDto, String.format("变更记录id：%s，在milepost_design_plan_change_record变更记录表中不存在", formInstanceId));
                    Guard.notNull(recordDto.getUploadPathId(), String.format("变更记录id：%s，在milepost_design_plan_change_record变更记录表的上传路径id不能为空",
                            formInstanceId));
                    MilepostDesignPlanDetailDto historyModelDetailDto = milepostDesignPlanDetailService.getById(recordDto.getUploadPathId());
                    recordDto.setStatus(checkStatus);
                    this.save(recordDto, createUserId);
                    //如果已进度确认
                    if (!Objects.equals(CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code(), historyModelDetailDto.getStatus())) {
                        historyModelDetailDto.setStatus(Objects.equals(CheckStatus.MDP_CHANGE_PASSED.code(), checkStatus) ?
                                CheckStatus.PASS.code() : checkStatus);
                        milepostDesignPlanDetailService.save(historyModelDetailDto, null);
                    }

                    if (Objects.equals(checkStatus, CheckStatus.MDP_CHANGE_PASSED.code())) {
                        //详设变更审批通过处理
                        String lockNameSync = String.format("DesignPlanDetailChangeApprovalListener.handleMdpChangePassed_%s",
                                recordDto.getProjectId());
                        String value = String.valueOf(recordDto.getProjectId());
                        try {
                            if (DistributedCASLock.lock(lockNameSync, value, MAX_WAIT_TIME_SYNC, MAX_WAIT_TIME_SYNC * 2)) {
                                this.handleMdpChangePassed(recordDto, formInstanceId, createUserId);
                            }
                        } catch (Exception e) {
                            logger.error("handleMdpChangePassed处理采购需求加锁失败", e);
                            throw e;
                        } finally {
                            DistributedCASLock.unLock(lockNameSync, value);
                        }
                    }

                    if (Objects.equals(dto.getStatus(), CheckStatus.PASS.code())) {
                        workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                                formInstance, true);
                    } else {
                        workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                                formInstance, false);
                    }
                } catch (ApplicationBizException e) {
                    logger.info("详细设计变更审批公共回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("详细设计变更审批公共回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("详细设计变更审批公共回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    private MilepostDesignPlanChangeRecordDto add(MilepostDesignPlanChangeRecordDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        MilepostDesignPlanChangeRecord entity = BeanConverter.copy(dto, MilepostDesignPlanChangeRecord.class);
        milepostDesignPlanChangeRecordMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    private MilepostDesignPlanChangeRecordDto update(MilepostDesignPlanChangeRecordDto dto) {
        MilepostDesignPlanChangeRecord entity = BeanConverter.copy(dto, MilepostDesignPlanChangeRecord.class);
        milepostDesignPlanChangeRecordMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    private MilepostDesignPlanChangeRecordDto save(MilepostDesignPlanChangeRecordDto dto, Long userBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
            return this.add(dto);
        } else {
            dto.setUpdateBy(userBy);
            return this.update(dto);
        }
    }

    private MilepostDesignPlanChangeRecordDto getById(Long id) {
        MilepostDesignPlanChangeRecord entity = milepostDesignPlanChangeRecordMapper.selectByPrimaryKey(id);
        MilepostDesignPlanChangeRecordDto dto = BeanConverter.copy(entity, MilepostDesignPlanChangeRecordDto.class);
        return dto;
    }

    /**
     * 详设变更审批通过处理
     */
    private void handleMdpChangePassed(MilepostDesignPlanChangeRecordDto recordDto, Long changeRecordId, Long userBy) {
        logger.info(String.format("详设变更审批通过的recordDto：%s,用户id：%d,formInstanceId：%d", JsonUtils.toString(recordDto), userBy, changeRecordId));
        //根据项目获取库存组织
        Long organizationId = projectService.getOrganizationIdByProjectId(recordDto.getProjectId());
        //变更模组(只允许更改集成外包字段)
        DesignPlanDetailChangeHistoryVO modelChangeHistoryVO =
                milepostDesignPlanDetailChangeService.getContrastWithSonsByModelId(recordDto.getUploadPathId(), changeRecordId);
        logger.info(String.format("详设变更审批通过的变更模组的数据modelChangeHistoryVO：%s", JsonUtils.toString(modelChangeHistoryVO)));
        Boolean modelHistoryExt = modelChangeHistoryVO.getHistory().getExt();
        Boolean modelChangeExt = modelChangeHistoryVO.getChange().getExt();
        if (modelHistoryExt != null && modelChangeExt != null && !modelHistoryExt.equals(modelChangeExt)) {
            MilepostDesignPlanDetailDto modelDto = new MilepostDesignPlanDetailDto();
            modelDto.setId(recordDto.getUploadPathId());
            modelDto.setExt(modelChangeExt);
            milepostDesignPlanDetailService.save(modelDto, null);
        }

        //变更模组设计信息
        Map<Long, String> materialIdWithNewErpCode = new HashMap<>();
        List<DesignPlanDetailChangeHistoryVO> sonChangeHistoryVOs = modelChangeHistoryVO.getSonVos();

        // 批量查询物料信息
        List<String> pamCodeList = new ArrayList<>();
        List<String> updateMaterialCostRequirementPamCodeList = new ArrayList<>();
        if (ListUtils.isNotEmpty(sonChangeHistoryVOs)) {
            for (DesignPlanDetailChangeHistoryVO history : sonChangeHistoryVOs) {
                if (history.getType().equals(ChangeType.UPDATE.code())) {//设计信息变更类型:更新
                    if (modelHistoryExt != null && modelChangeExt != null) {
                        if (!modelHistoryExt.equals(modelChangeExt) && modelChangeExt.equals(false)) {//集成外包变化,
                            // 且变更后集成外包:否
                            MilepostDesignPlanDetailChangeDto change = history.getChange();
                            pamCodeList.add(change.getPamCode());
                        }
                    }
                } else if (history.getType().equals(ChangeType.ADD.code())
                        && history.getChange() != null
                        && !Objects.equals(history.getChange().getDeletedFlag(), Boolean.TRUE)) {
                    //设计信息变更类型:新增
                    MilepostDesignPlanDetailChangeDto change = history.getChange();
                    pamCodeList.add(change.getPamCode());
                }

                if (null != history.getChange()) {
                    String pamCode = history.getChange().getPamCode();
                    if (StringUtils.isNotEmpty(pamCode)) {
                        updateMaterialCostRequirementPamCodeList.add(pamCode);
                    }
                }
            }
        }
        logger.info(String.format("详设变更审批通过的数据pamCodeList：%s", JsonUtils.toString(pamCodeList)));

        Map<String, Material> materialMap = new HashMap<>();
        if (ListUtils.isNotEmpty(pamCodeList)) {
            List<Material> materials = materialExtService.invokeMaterialApiGetByPamCodeList(pamCodeList, organizationId);
            materials.forEach(material -> {
                materialMap.put(material.getPamCode(), material);
            });
        }
        logger.info(String.format("详设变更审批通过的数据materialMap：%s", JsonUtils.toString(materialMap)));

        logger.info(String.format("详设变更审批通过的数据sonChangeHistoryVOs：%s", JsonUtils.toString(sonChangeHistoryVOs)));
        List<Long> requirementIdList = new ArrayList<>();
        List<Long> projectIdList = new ArrayList<>();
        for (DesignPlanDetailChangeHistoryVO sonChangeHistoryVO : sonChangeHistoryVOs) {
            Integer type = sonChangeHistoryVO.getType();
            MilepostDesignPlanDetailChangeDto change = sonChangeHistoryVO.getChange();
            if (Objects.equals(type, ChangeType.UPDATE.code())) {//设计信息变更类型:更新
                if (modelHistoryExt != null && modelChangeExt != null) {
                    if (modelHistoryExt.equals(modelChangeExt)) {//集成外包没有变化
                        this.handleChangeForUpdate(sonChangeHistoryVO, recordDto.getCreateBy(), changeRecordId, projectIdList, requirementIdList);
                    } else if (modelChangeExt.equals(true)) {//集成外包变化,
                        // 且变更后集成外包:是
                        this.handleChangeForDel(sonChangeHistoryVO, recordDto.getCreateBy(), changeRecordId, requirementIdList);
                    } else if (modelChangeExt.equals(false)) {//集成外包变化,
                        // 且变更后集成外包:否
                        Material material = materialMap.get(change.getPamCode());
                        this.handleChangeForAdd(sonChangeHistoryVO, recordDto.getCreateBy(), materialIdWithNewErpCode, material,
                                projectIdList, requirementIdList);
                    }
                } else {
                    //变更
                    this.handleChangeForUpdate(sonChangeHistoryVO, recordDto.getCreateBy(), changeRecordId, projectIdList, requirementIdList);
                }
            } else if (Objects.equals(type, ChangeType.ADD.code())) {//设计信息变更类型:新增
                // 判断是否已删除（新增的物料可以在驳回/撤回后删除）
                Material material = materialMap.get(change.getPamCode());
                if (Objects.equals(Boolean.TRUE, change.getDeletedFlag()) && material == null) {
                    // 删除物料估价以及对应物料 (material不为空证明本次详设变更同一个pamcode删了又加)
                    milepostDesignPlanHelper.handleMaterialCost(change.getPamCode(), organizationId);
                } else {
                    this.handleChangeForAdd(sonChangeHistoryVO, recordDto.getCreateBy(), materialIdWithNewErpCode, material, projectIdList,
                            requirementIdList);
                }
            } else if (Objects.equals(type, ChangeType.DEL.code())) {//设计信息变更类型:删除
                this.handleChangeForDel(sonChangeHistoryVO, recordDto.getCreateBy(), changeRecordId, requirementIdList);
            }
        }

        ArrayList<Long> requirementIdListNew = new ArrayList<>(requirementIdList);

        //对删除的详细不更改对应的采购需求的收货地址
        removeDeletedDetailRequirementIds(modelChangeHistoryVO, requirementIdListNew);

        // 批量保存采购需求历史收货信息
        this.batchSaveRequirementDeliveryInfo(requirementIdListNew, recordDto);

        if (CollectionUtils.isNotEmpty(requirementIdList)) {
            purchaseMaterialRequirementService.batchUpdateStatus(requirementIdList);
        }

        // 变更附件信息状态，把审批中状态改为审批通过
        this.updateCtcAttachmentDtoStatus(changeRecordId);

        //批量推送需要更新erp编码的物料
        materialExtService.saveBatchByIdWithNewErpCode(materialIdWithNewErpCode, userBy);

        //批量更新物料估价的采购需求量
        applicationEventPublisher.publishEvent(new UpdateMaterialCostRequirementEvent(this, updateMaterialCostRequirementPamCodeList,
                recordDto.getMilepostId(), organizationId));
    }

    private void removeDeletedDetailRequirementIds(DesignPlanDetailChangeHistoryVO modelChangeHistoryVO, List<Long> requirementIdList) {
        for (DesignPlanDetailChangeHistoryVO sonVo : modelChangeHistoryVO.getSonVos()) {
            MilepostDesignPlanDetailChangeDto change = sonVo.getChange();
            if(Objects.nonNull(change)){
                if(Objects.equals(HistoryType.CHANGE.getCode(),change.getHistoryType())){
                    if(Objects.nonNull(change.getDeletedFlag()) && change.getDeletedFlag()){
                        Long projectId = change.getProjectId();
                        String pamCode = change.getPamCode();
                        Date deliveryTime = change.getDeliveryTime();
                        if(projectId != null && StringUtils.isNotEmpty(pamCode) && deliveryTime != null){
                            PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(projectId, deliveryTime, pamCode);
                            if(Objects.nonNull(requirementDto) && Objects.nonNull(requirementDto.getId())){
                                requirementIdList.remove(requirementDto.getId());
                            }
                        }
                    }
                }
            }
        }
        logger.info("详设变更审批通过-需要更新采购需求收货地址的IDS：{}", JsonUtils.toString(requirementIdList));
    }

    private void handleChangeForUpdate(DesignPlanDetailChangeHistoryVO vo, Long userBy, Long changeRecordId, List<Long> projectIdList,
                                       List<Long> requirementIdList) {
        logger.info(String.format("详设变更审批通过的修改vo：%s,用户id：%d,formInstanceId：%d,projectIdList：%s",
                JsonUtils.toString(vo), userBy, changeRecordId, JsonUtils.toString(projectIdList)));

        String redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + vo.getHistory().getDesignPlanDetailId();
        String luaScript = String.format("return redis.call('get', '%s'); \n", redisKeyByDelete);
        String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
        if (StringUtils.isNotEmpty(redisResult)) {
            return;
        }

        MilepostDesignPlanDetailDto theLastDetailDto = milepostDesignPlanDetailService.getById(vo.getHistory().getDesignPlanDetailId());
        if (null == theLastDetailDto) {
            return;
        }
        //删除的详设也不做操作
        if (Boolean.TRUE.equals(theLastDetailDto.getDeletedFlag())) {
            return;
        }
        //查询父节点
        MilepostDesignPlanDetailDto theParentDto = milepostDesignPlanDetailService.getById(vo.getChange().getParentId());

        theLastDetailDto.setNumber(vo.getChange().getNumber());
        theLastDetailDto.setDeletedFlag(vo.getChange().getDeletedFlag());
        if (CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code().equals(theParentDto.getStatus())) {
            theLastDetailDto.setStatus(CheckStatus.MDP_SUPPLY_CHAIN_EVALUATED_APPROVED.code());
        } else {
            theLastDetailDto.setStatus(CheckStatus.MDP_CHANGE_PASSED.code());
        }

        theLastDetailDto.setChangeRecordId(changeRecordId);
        if (Objects.equals(theParentDto.getModuleStatus(), MilepostDesignPlanDetailModelStatus.CONFIRMED.code())) {
            //产生采购需求
            theLastDetailDto.setGenerateRequirement(DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code());
        }
        milepostDesignPlanDetailService.save(theLastDetailDto, userBy);

        if (Objects.equals(theParentDto.getModuleStatus(), MilepostDesignPlanDetailModelStatus.CONFIRMED.code())) {
            if (StringUtils.isEmpty(theLastDetailDto.getErpCode())) {
                Map<Long, String> materialIdWithNewErpCode = new HashMap<>();
                Long organizationId = projectService.getOrganizationIdByProjectId(theLastDetailDto.getProjectId());
                //milepostDesignPlanDetailService.handleRecursive(theLastDetailDto.getId(), null,
                // materialIdWithNewErpCode);//生成erpcode
                //递归
                milepostDesignPlanDetailService.handleRecursive(theLastDetailDto.getId(), null, materialIdWithNewErpCode);
                //采购件才生成erp编码 （采购件已更改为 外购物料） 机器人为空
                if (StringUtils.isEmpty(theLastDetailDto.getMaterialCategory()) || "看板物料".equals(theLastDetailDto.getMaterialCategory())
                        || "外购物料".equals(theLastDetailDto.getMaterialCategory())) {
                    theLastDetailDto.setErpCode(
                            materialExtService.handleNewMaterial(
                                    theLastDetailDto.getErpCode(),
                                    theLastDetailDto.getPamCode(),
                                    theLastDetailDto.getMaterielType(),
                                    organizationId,
                                    materialIdWithNewErpCode,
                                    theLastDetailDto.getWhetherModel())
                    );
                    milepostDesignPlanDetailService.save(theLastDetailDto, null);
                }
                materialExtService.saveBatchByIdWithNewErpCode(materialIdWithNewErpCode, userBy);//推送到erp
            }

            milepostDesignPlanService.confirmLogic(theLastDetailDto.getId(), projectIdList, requirementIdList);//进度确认逻辑
        }

        List<MilepostDesignPlanDetailDto> list = milepostDesignPlanDetailService.getDesignPlanDetail(theLastDetailDto.getParentId());
        ticketTasksService.createTicketTasksByDesignPlanDetail(list, null, vo.getChange().getChangeRecordId(), "详细设计变更");
        //updateMilepostStatus(resultDetailDto.getMilepostId(), MilepostStatus.CONFIRM_PASS.getCode());//修改里程碑状态
        //milepostDesignPlanService.notifyThePersonInChargeOfTheMilestoneToConfirm(resultDetailDto.getMilepostId());
        milepostDesignPlanService.setMRP(theLastDetailDto.getId());//设置mrp明细
            /*//工单任务变更
            List<MilepostDesignPlanDetailDto> plan = new ArrayList<>();
            plan.add(theLastDetailDto);
            ticketTasksService.createTicketTasksByDesignPlanDetail(plan, null);*/

        theLastDetailDto = milepostDesignPlanDetailService.getById(theLastDetailDto.getId());

        String redisKey = "milepostDesignPlanDetailFinalNumber_" + vo.getHistory().getDesignPlanDetailId();
        StringBuffer sb = new StringBuffer();
        sb.append(String.format("if redis.call('exists', '%s') == 0 then \n", redisKey));
        sb.append(String.format("redis.call('setnx', '%s', %s); \n", redisKey, vo.getChange().getNumber()));
        sb.append(String.format("redis.call('expire', '%s', %d); \n", redisKey, 3600L));
        sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
        sb.append("else \n");

        sb.append(String.format("local number = %s; \n", vo.getChange().getNumber()));
        sb.append(String.format("local val = redis.call('get', '%s'); \n", redisKey));
        sb.append("if tonumber(number) ~= tonumber(val) then \n");
        sb.append("local res = tonumber(number) - tonumber(val); \n");
        sb.append(String.format("redis.call('incrbyfloat', '%s', res); \n", redisKey));
        sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
        sb.append("else \n");
        sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
        sb.append("end; \n");

        sb.append("end; \n");
        String redisNumber = redisLuaScriptServer.executeLuaScript(sb.toString(), new ArrayList<>(), "");

        if (DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code().equals(theLastDetailDto.getGenerateRequirement())) {
            PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(
                    theLastDetailDto.getProjectId(),
                    theLastDetailDto.getDeliveryTime(),
                    theLastDetailDto.getPamCode());

            if (vo.getHistory().getNumber() != null && vo.getChange().getNumber() != null && requirementDto != null
                    && StringUtils.isNotEmpty(theLastDetailDto.getErpCode())
                    && Objects.equals("外购物料", theLastDetailDto.getMaterialCategory())) {
                //获取当前设计信息的发布明细累积:变化前的总发布量
                BigDecimal historyNum =
                        purchaseMaterialReleaseDetailService.getPublishNumSumByDesignPlanDetailId(vo.getHistory().getDesignPlanDetailId());
                logger.info("详设变更修改数量时，historyNum：{}", historyNum);
                //计算当前设计信息的最新发布总量:设计信息的数量*所有父级的积数
                logger.info("详设变更修改数量时，originalChangeNum：{}", vo.getChange().getNumber());
                BigDecimal parentSigmaById = milepostDesignPlanDetailService.getParentSigmaById(vo.getHistory().getParentId(), null);
                logger.info("handleChangeForUpdate的详设id：{}，redisNumber：{}，parentSigmaById：{}，redisLuaScriptServer执行的脚本：{}",
                        vo.getHistory().getDesignPlanDetailId(), redisNumber, parentSigmaById, sb.toString());

                BigDecimal changeNum = new BigDecimal(redisNumber).multiply(parentSigmaById);
                //当前设计信息的当次发布差异量
                BigDecimal differenceNumber = changeNum.subtract(historyNum);

                String requirementRedisNumber = purchaseMaterialRequirementService.getRequirementRedisNumber(theLastDetailDto.getProjectId(),
                        theLastDetailDto.getDeliveryTime(), theLastDetailDto.getPamCode(), requirementDto.getNeedTotal(), differenceNumber);

                requirementDto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                requirementDto.setApprovedSupplierNumber(0);
                purchaseMaterialRequirementService.save(requirementDto, userBy);
                if (BigDecimal.ZERO.compareTo(differenceNumber) != 0) {
                    //purchaseMaterialRequirementService.updateStatus(requirementDto.getId());//根据总需求量更新采购需求的状态
                    requirementIdList.add(requirementDto.getId());

                    purchaseMaterialRequirementService.addPurchaseMaterialReleaseDetail(
                            requirementDto.getMaterielId(),
                            requirementDto.getId(),
                            theLastDetailDto,
                            ReleaseDetailStatus.CHANGE.code(),
                            userBy,
                            differenceNumber);

                    //高并发情况下更新采购需求的总需求量
                    /*BigDecimal totalNum =
                            purchaseMaterialReleaseDetailService.getPublishNumSumByPurchaseMaterialRequirementId(requirementDto.getId());
                    requirementDto.setNeedTotal(totalNum);
                    purchaseMaterialRequirementService.save(requirementDto, userBy);*/
                }
//                    purchaseMaterialRequirementExtMapper.updateApprovedSupplierNumber();
            }
        }

        //处理所有下级的采购需求情况
        logger.info("handleChangeForUpdate开始处理所有下级的采购需求情况");
        List<MilepostDesignPlanDetail> designPlanDetailList = milepostDesignPlanDetailService.selectByParentId(theLastDetailDto.getId());
        logger.info("handleChangeForUpdate的下级designPlanDetailList：{}，parentChangeNumber：{}", JsonUtils.toString(designPlanDetailList),
                vo.getChange().getNumber());
        updateSonsPurchaseMaterialRequirement(designPlanDetailList, vo.getChange().getNumber(), userBy, requirementIdList);

    }

    private void updateSonsPurchaseMaterialRequirement(List<MilepostDesignPlanDetail> designPlanDetailList, BigDecimal parentChangeNumber,
                                                       Long userBy, List<Long> requirementIdList) {
        if (CollectionUtils.isEmpty(designPlanDetailList)) {
            return;
        }
        for (MilepostDesignPlanDetail planDetail : designPlanDetailList) {
            String redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + planDetail.getId();
            String luaScript = String.format("return redis.call('get', '%s'); \n", redisKeyByDelete);
            String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
            if (StringUtils.isNotEmpty(redisResult)) {
                continue;
            }
            if (Boolean.TRUE.equals(planDetail.getDeletedFlag())) {
                continue;
            }

            String redisKey = "milepostDesignPlanDetailFinalNumber_" + planDetail.getId();
            StringBuffer sb = new StringBuffer();
            sb.append(String.format("if redis.call('exists', '%s') == 0 then \n", redisKey));
            sb.append(String.format("redis.call('setnx', '%s', %s); \n", redisKey, planDetail.getNumber()));
            sb.append(String.format("redis.call('expire', '%s', %d); \n", redisKey, 3600L));
            sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
            sb.append("else \n");

            sb.append(String.format("local number = %s; \n", planDetail.getNumber()));
            sb.append(String.format("local val = redis.call('get', '%s'); \n", redisKey));
            sb.append("if tonumber(number) ~= tonumber(val) then \n");
            sb.append("local res = tonumber(number) - tonumber(val); \n");
            sb.append(String.format("redis.call('incrbyfloat', '%s', res); \n", redisKey));
            sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
            sb.append("else \n");
            sb.append(String.format("return redis.call('get', '%s'); \n", redisKey));
            sb.append("end; \n");

            sb.append("end; \n");
            String redisNumber = redisLuaScriptServer.executeLuaScript(sb.toString(), new ArrayList<>(), "");

            if (DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code().equals(planDetail.getGenerateRequirement())) {
                PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(
                        planDetail.getProjectId(),
                        planDetail.getDeliveryTime(),
                        planDetail.getPamCode());

                if (planDetail.getNumber() != null && parentChangeNumber != null && requirementDto != null
                        && StringUtils.isNotEmpty(planDetail.getErpCode()) && Objects.equals("外购物料", planDetail.getMaterialCategory())) {
                    //获取当前设计信息的发布明细累积:变化前的总发布量
                    BigDecimal historyNum = purchaseMaterialReleaseDetailService.getPublishNumSumByDesignPlanDetailId(planDetail.getId());
                    logger.info("子级详设：{}变更修改数量时，historyNum：{}", planDetail.getId(), historyNum);
                    //计算当前设计信息的最新发布总量:设计信息的数量*所有父级的积数
                    logger.info("子级详设变更修改数量时，parentChangeNumber：{}", parentChangeNumber);

                    BigDecimal parentSigmaById = milepostDesignPlanDetailService.getParentSigmaById(planDetail.getParentId(), null);
                    logger.info("handleChangeForUpdate的详设id：{}，redisNumber：{}，parentSigmaById：{}，redisLuaScriptServer执行的脚本：{}",
                            planDetail.getId(), redisNumber, parentSigmaById, sb.toString());

                    BigDecimal changeNum = new BigDecimal(redisNumber).multiply(parentSigmaById);
                    //当前设计信息的当次发布差异量
                    BigDecimal differenceNumber = changeNum.subtract(historyNum);

                    String requirementRedisNumber = purchaseMaterialRequirementService.getRequirementRedisNumber(planDetail.getProjectId(),
                            planDetail.getDeliveryTime(), planDetail.getPamCode(), requirementDto.getNeedTotal(), differenceNumber);

                    requirementDto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                    requirementDto.setApprovedSupplierNumber(0);
                    purchaseMaterialRequirementService.save(requirementDto, userBy);
                    if (BigDecimal.ZERO.compareTo(differenceNumber) != 0) {
                        //purchaseMaterialRequirementService.updateStatus(requirementDto.getId());//根据总需求量更新采购需求的状态
                        requirementIdList.add(requirementDto.getId());

                        purchaseMaterialRequirementService.addPurchaseMaterialReleaseDetail(
                                requirementDto.getMaterielId(),
                                requirementDto.getId(),
                                BeanConverter.copy(planDetail, MilepostDesignPlanDetailDto.class),
                                ReleaseDetailStatus.CHANGE.code(),
                                userBy,
                                differenceNumber);

                        //高并发情况下更新采购需求的总需求量
                        /*BigDecimal totalNum =
                                purchaseMaterialReleaseDetailService.getPublishNumSumByPurchaseMaterialRequirementId(requirementDto.getId());
                        requirementDto.setNeedTotal(totalNum);
                        purchaseMaterialRequirementService.save(requirementDto, userBy);*/
                    }
                }
            }

            List<MilepostDesignPlanDetail> detailList = milepostDesignPlanDetailService.selectByParentId(planDetail.getId());
            logger.info("handleChangeForUpdate的下级detailList：{}，parentChangeNumber：{}", JsonUtils.toString(designPlanDetailList), parentChangeNumber);
            updateSonsPurchaseMaterialRequirement(detailList, parentChangeNumber, userBy, requirementIdList);
        }
    }

    private void handleChangeForDel(DesignPlanDetailChangeHistoryVO vo, Long userBy, Long changeRecordId, List<Long> requirementIdList) {
        logger.info(String.format("详设变更审批通过的删除vo：%s,用户id：%d,formInstanceId：%d", JsonUtils.toString(vo), userBy, changeRecordId));

        String redisKey = "milepostDesignPlanDetailByDelete_" + vo.getHistory().getDesignPlanDetailId();
        String luaScript = String.format("return redis.call('get', '%s'); \n", redisKey);
        String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
        if (StringUtils.isNotEmpty(redisResult)) {
            return;
        }
        MilepostDesignPlanDetailDto theLastDetailDto = milepostDesignPlanDetailService.getById(vo.getHistory().getDesignPlanDetailId());
        if (null == theLastDetailDto) {
            return;
        }
        if (Boolean.TRUE.equals(theLastDetailDto.getDeletedFlag())) {
            return;
        }

        Long organizationId = projectService.getOrganizationIdByProjectId(theLastDetailDto.getProjectId());
        if (Objects.equals(theLastDetailDto.getGenerateRequirement(), DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code())) {
            PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(
                    theLastDetailDto.getProjectId(),
                    theLastDetailDto.getDeliveryTime(),
                    theLastDetailDto.getPamCode());

            if (vo.getHistory().getNumber() != null && requirementDto != null && StringUtils.isNotEmpty(theLastDetailDto.getErpCode())
                    && Objects.equals("外购物料", theLastDetailDto.getMaterialCategory())) {
                //获取当前设计信息的发布明细累积:变化前的总发布量
                BigDecimal historyNum =
                        purchaseMaterialReleaseDetailService.getPublishNumSumByDesignPlanDetailId(vo.getHistory().getDesignPlanDetailId());
                logger.info("当前删除的详设：{}变更修改数量时，historyNum：{}", vo.getHistory().getDesignPlanDetailId(), historyNum);
                //当前设计信息的当次发布差异量
                BigDecimal differenceNumber = BigDecimal.ZERO.subtract(historyNum);

                String requirementRedisNumber = purchaseMaterialRequirementService.getRequirementRedisNumber(theLastDetailDto.getProjectId(),
                        theLastDetailDto.getDeliveryTime(), theLastDetailDto.getPamCode(), requirementDto.getNeedTotal(), differenceNumber);
                logger.info("handleChangeForDel的projectId：{}，pamCode：{}，deliveryTime：{}，" +
                                "对应的needTotal：{}，differenceNumber：{}，requirementRedisNumber：{}",
                        theLastDetailDto.getProjectId(), theLastDetailDto.getPamCode(),
                        DateUtil.format(theLastDetailDto.getDeliveryTime(), "yyyyMMddHHmmss"),
                        requirementDto.getNeedTotal(), differenceNumber, requirementRedisNumber);

                requirementDto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                requirementDto.setApprovedSupplierNumber(0);
                purchaseMaterialRequirementService.save(requirementDto, userBy);
                if (BigDecimal.ZERO.compareTo(differenceNumber) != 0) {
                    //purchaseMaterialRequirementService.updateStatus(requirementDto.getId());//根据总需求量更新采购需求的状态
                    requirementIdList.add(requirementDto.getId());

                    purchaseMaterialRequirementService.addPurchaseMaterialReleaseDetail(
                            requirementDto.getMaterielId(),
                            requirementDto.getId(),
                            theLastDetailDto,
                            ReleaseDetailStatus.CHANGE.code(),
                            userBy,
                            differenceNumber);

                    //高并发情况下更新采购需求的总需求量
                    /*BigDecimal totalNum =
                            purchaseMaterialReleaseDetailService.getPublishNumSumByPurchaseMaterialRequirementId(requirementDto.getId());
                    requirementDto.setNeedTotal(totalNum);
                    purchaseMaterialRequirementService.save(requirementDto, userBy);*/
                }
//                    purchaseMaterialRequirementExtMapper.updateApprovedSupplierNumber();
            }
        }

        theLastDetailDto.setNumber(vo.getChange().getNumber());
        theLastDetailDto.setDeletedFlag(vo.getChange().getDeletedFlag());
        theLastDetailDto.setStatus(CheckStatus.MDP_CHANGE_PASSED.code());
        theLastDetailDto.setChangeRecordId(changeRecordId);
        milepostDesignPlanDetailService.save(theLastDetailDto, userBy);

        String redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + theLastDetailDto.getId();
        StringBuffer deleteSB = new StringBuffer();
        deleteSB.append(String.format("redis.call('setnx', '%s', %d); \n", redisKeyByDelete, theLastDetailDto.getId()));
        deleteSB.append(String.format("redis.call('expire', '%s', %d); \n", redisKeyByDelete, 3600L));
        redisLuaScriptServer.executeLuaScript(deleteSB.toString(), new ArrayList<>(), "");

        //处理所有下级的采购需求情况
        logger.info("handleChangeForDel开始处理所有下级的采购需求情况");
        List<MilepostDesignPlanDetail> designPlanDetailList = milepostDesignPlanDetailService.selectByParentId(theLastDetailDto.getId());
        logger.info("handleChangeForDel的下级designPlanDetailList：{}", JsonUtils.toString(designPlanDetailList));
        deleteSonsPurchaseMaterialRequirement(designPlanDetailList, userBy, changeRecordId, requirementIdList);
        logger.info("handleChangeForDel结束处理所有下级的采购需求情况");

        List<MilepostDesignPlanDetailDto> plan = new ArrayList<>();
        plan.add(theLastDetailDto);
        ticketTasksService.createTicketTasksByDesignPlanDetail(plan, null, vo.getChange().getChangeRecordId(), "详细设计变更");
        //删除子物料
        //子节点
        MilepostDesignPlanDetailDto planDetailQuery = new MilepostDesignPlanDetailDto();
        planDetailQuery.setParentId(theLastDetailDto.getId());
        planDetailQuery.setDeletedFlag(Boolean.FALSE);
        List<MilepostDesignPlanDetailDto> designPlanDetailSons =
                milepostDesignPlanDetailExtMapper.selectList(planDetailQuery);
        if (ListUtils.isNotEmpty(designPlanDetailSons)) {
            for (MilepostDesignPlanDetailDto d : designPlanDetailSons) {
                deleteSonPlanDetail(vo, userBy, changeRecordId, d.getId());
                d.setDeletedFlag(vo.getChange().getDeletedFlag());
                d.setStatus(CheckStatus.MDP_CHANGE_PASSED.code());
                d.setChangeRecordId(changeRecordId);
                milepostDesignPlanDetailService.save(d, userBy);
            }
            ticketTasksService.createTicketTasksByDesignPlanDetail(designPlanDetailSons, null, vo.getChange().getChangeRecordId(), "详细设计变更");
        }

        if (Boolean.TRUE.equals(vo.getChange().getDeletedFlag())) {
            // 删除物料估价以及对应物料
            milepostDesignPlanHelper.handleMaterialCost(theLastDetailDto.getPamCode(), organizationId);
        }
    }

    private void deleteSonsPurchaseMaterialRequirement(List<MilepostDesignPlanDetail> designPlanDetailList, Long userBy, Long changeRecordId,
                                                       List<Long> requirementIdList) {
        if (CollectionUtils.isEmpty(designPlanDetailList)) {
            return;
        }
        for (MilepostDesignPlanDetail planDetail : designPlanDetailList) {
            if (Boolean.TRUE.equals(planDetail.getDeletedFlag())) {
                continue;
            }
            String redisKey = "milepostDesignPlanDetailByDelete_" + planDetail.getId();
            String luaScript = String.format("return redis.call('get', '%s'); \n", redisKey);
            String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
            if (StringUtils.isNotEmpty(redisResult)) {
                continue;
            }

            if (DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code().equals(planDetail.getGenerateRequirement())) {
                PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(
                        planDetail.getProjectId(),
                        planDetail.getDeliveryTime(),
                        planDetail.getPamCode());

                if (null != requirementDto
                        && StringUtils.isNotEmpty(planDetail.getErpCode()) && Objects.equals("外购物料", planDetail.getMaterialCategory())) {
                    //获取当前设计信息的发布明细累积:变化前的总发布量
                    BigDecimal historyNum = purchaseMaterialReleaseDetailService.getPublishNumSumByDesignPlanDetailId(planDetail.getId());
                    logger.info("子级删除的详设：{}变更修改数量时，historyNum：{}", planDetail.getId(), historyNum);
                    //当前设计信息的当次发布差异量
                    BigDecimal differenceNumber = BigDecimal.ZERO.subtract(historyNum);

                    String requirementRedisNumber = purchaseMaterialRequirementService.getRequirementRedisNumber(planDetail.getProjectId(),
                            planDetail.getDeliveryTime(), planDetail.getPamCode(), requirementDto.getNeedTotal(), differenceNumber);
                    logger.info("deleteSonsPurchaseMaterialRequirement的projectId：{}，pamCode：{}，deliveryTime：{}，" +
                                    "对应的needTotal：{}，differenceNumber：{}，requirementRedisNumber：{}",
                            planDetail.getProjectId(), planDetail.getPamCode(), DateUtil.format(planDetail.getDeliveryTime(), "yyyyMMddHHmmss"),
                            requirementDto.getNeedTotal(), differenceNumber, requirementRedisNumber);

                    requirementDto.setNeedTotal(new BigDecimal(requirementRedisNumber));
                    requirementDto.setApprovedSupplierNumber(0);
                    purchaseMaterialRequirementService.save(requirementDto, userBy);
                    if (BigDecimal.ZERO.compareTo(differenceNumber) != 0) {
                        //purchaseMaterialRequirementService.updateStatus(requirementDto.getId());//根据总需求量更新采购需求的状态
                        requirementIdList.add(requirementDto.getId());

                        purchaseMaterialRequirementService.addPurchaseMaterialReleaseDetail(
                                requirementDto.getMaterielId(),
                                requirementDto.getId(),
                                BeanConverter.copy(planDetail, MilepostDesignPlanDetailDto.class),
                                ReleaseDetailStatus.CHANGE.code(),
                                userBy,
                                differenceNumber);

                        //高并发情况下更新采购需求的总需求量
                        /*BigDecimal totalNum =
                                purchaseMaterialReleaseDetailService.getPublishNumSumByPurchaseMaterialRequirementId(requirementDto.getId());
                        requirementDto.setNeedTotal(totalNum);
                        purchaseMaterialRequirementService.save(requirementDto, userBy);*/
                    }
                }
            }

            planDetail.setDeletedFlag(Boolean.FALSE);
            planDetail.setStatus(CheckStatus.MDP_CHANGE_PASSED.code());
            planDetail.setChangeRecordId(changeRecordId);
            milepostDesignPlanDetailService.save(BeanConverter.copy(planDetail, MilepostDesignPlanDetailDto.class), userBy);

            String redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + planDetail.getId();
            StringBuffer deleteSB = new StringBuffer();
            deleteSB.append(String.format("redis.call('setnx', '%s', %d); \n", redisKeyByDelete, planDetail.getId()));
            deleteSB.append(String.format("redis.call('expire', '%s', %d); \n", redisKeyByDelete, 3600L));
            redisLuaScriptServer.executeLuaScript(deleteSB.toString(), new ArrayList<>(), "");

            List<MilepostDesignPlanDetail> detailList = milepostDesignPlanDetailService.selectByParentId(planDetail.getId());
            logger.info("handleChangeForDel的下级detailList：{}", JsonUtils.toString(designPlanDetailList));
            deleteSonsPurchaseMaterialRequirement(detailList, userBy, changeRecordId, requirementIdList);
        }
    }

    private void deleteSonPlanDetail(DesignPlanDetailChangeHistoryVO vo, Long userBy, Long changeRecordId, Long parentId) {
        MilepostDesignPlanDetailDto planDetailQuery = new MilepostDesignPlanDetailDto();
        planDetailQuery.setParentId(parentId);
        planDetailQuery.setDeletedFlag(Boolean.FALSE);
        List<MilepostDesignPlanDetailDto> designPlanDetailSons =
                milepostDesignPlanDetailExtMapper.selectList(planDetailQuery);
        if (ListUtils.isNotEmpty(designPlanDetailSons)) {
            for (MilepostDesignPlanDetailDto d : designPlanDetailSons) {
                deleteSonPlanDetail(vo, userBy, changeRecordId, d.getId());
                d.setDeletedFlag(vo.getChange().getDeletedFlag());
                d.setStatus(CheckStatus.MDP_CHANGE_PASSED.code());
                d.setChangeRecordId(changeRecordId);
                milepostDesignPlanDetailService.save(d, userBy);
            }
            ticketTasksService.createTicketTasksByDesignPlanDetail(designPlanDetailSons, null, vo.getChange().getChangeRecordId(), "详细设计变更");
        }
    }

    private void handleChangeForAdd(DesignPlanDetailChangeHistoryVO vo, Long userBy, Map<Long, String> materialIdWithNewErpCode, Material material,
                                    List<Long> projectIdList, List<Long> requirementIdList) {
        logger.info(String.format("详设变更审批通过的新增vo：%s,用户id：%d,materialIdWithNewErpCode：%s,material：%s,projectIdList：%s",
                JsonUtils.toString(vo), userBy, JsonUtils.toString(materialIdWithNewErpCode), JsonUtils.toString(material),
                JsonUtils.toString(projectIdList)));

        String redisKeyByDelete = "milepostDesignPlanDetailByDelete_" + vo.getChange().getParentId();
        String luaScript = String.format("return redis.call('get', '%s'); \n", redisKeyByDelete);
        String redisResult = redisLuaScriptServer.executeLuaScript(luaScript, new ArrayList<>(), "");
        if (StringUtils.isNotEmpty(redisResult)) {
            return;
        }

        //查询父节点
        MilepostDesignPlanDetailDto theParentDto = milepostDesignPlanDetailService.getById(vo.getChange().getParentId());
        boolean canAdd = null != theParentDto && (null == vo.getChange().getDeletedFlag() || !vo.getChange().getDeletedFlag());
        if (!canAdd) {
            return;
        }
        if (Boolean.TRUE.equals(theParentDto.getDeletedFlag())) {
            return;
        }

        //过滤掉已删除状态的
        MilepostDesignPlanDetailDto addDetailDto = BeanConverter.copy(vo.getChange(), MilepostDesignPlanDetailDto.class);
        addDetailDto.setId(null);
        addDetailDto.setGenerateRequirement(DesignPlanDetailGenerateRequire.NOT_PRODUCED.code());
        addDetailDto.setStatus(CheckStatus.MDP_CHANGE_PASSED.code());
        MilepostDesignPlanDetailDto resultDetailDto = milepostDesignPlanDetailService.save(addDetailDto, userBy);

        //若模组已确认,则更新物料采购需求
        boolean canRequirement = Objects.equals(theParentDto.getModuleStatus(), MilepostDesignPlanDetailModelStatus.CONFIRMED.code());
        if (!canRequirement) {
            return;
        }

        //父节点已确认
        if (Objects.nonNull(resultDetailDto.getId())) {
            Long organizationId = projectService.getOrganizationIdByProjectId(resultDetailDto.getProjectId());
            if (resultDetailDto.getStatus() != null) {
                //递归
                milepostDesignPlanDetailService.handleRecursive(resultDetailDto.getId(), null, materialIdWithNewErpCode);

                //采购件才生成erp编码 （采购件已更改为 外购物料）
                if ("看板物料".equals(resultDetailDto.getMaterialCategory()) || "外购物料".equals(resultDetailDto.getMaterialCategory())) {
                    resultDetailDto.setErpCode(
                            materialExtService.handleNewMaterial(
                                    resultDetailDto.getErpCode(),
                                    resultDetailDto.getPamCode(),
                                    resultDetailDto.getMaterielType(),
                                    organizationId,
                                    materialIdWithNewErpCode,
                                    resultDetailDto.getWhetherModel())
                    );
                }

                milepostDesignPlanDetailService.save(resultDetailDto, userBy);

            }
            milepostDesignPlanService.confirmLogic(resultDetailDto.getId(), projectIdList, requirementIdList);//进度确认逻辑
            materialExtService.saveBatchByIdWithNewErpCode(materialIdWithNewErpCode, userBy);//推送到erp

        }
        List<MilepostDesignPlanDetailDto> planDetail = milepostDesignPlanDetailService.getDesignPlanDetail(resultDetailDto.getId());
        ticketTasksService.createTicketTasksByDesignPlanDetail(planDetail, null, vo.getChange().getChangeRecordId(), "详细设计变更");

        milepostDesignPlanService.setMRP(resultDetailDto.getId());//设置mrp明细

        // milepostDesignPlanService.confirmLogic更新了计划交货时间
        resultDetailDto = milepostDesignPlanDetailService.getById(resultDetailDto.getId());

        PurchaseMaterialRequirementDto requirementDto = purchaseMaterialRequirementService.getByProjectIdAndPamCodeAndDeliveryTime(
                resultDetailDto.getProjectId(),
                resultDetailDto.getDeliveryTime(),
                resultDetailDto.getPamCode());

        if (null == requirementDto) {
            requirementDto = new PurchaseMaterialRequirementDto();
            //生成物料采购需求
            requirementDto.setProjectId(resultDetailDto.getProjectId());
            requirementDto.setErpCode(resultDetailDto.getErpCode());
            requirementDto.setPamCode(resultDetailDto.getPamCode());
            requirementDto.setDeliveryTime(resultDetailDto.getDeliveryTime());
            if (null != material) {
                requirementDto.setMaterielId(material.getId());
                requirementDto.setMaterielDescr(material.getItemInfo());
            }
            requirementDto.setUnitCode(resultDetailDto.getUnitCode());
            requirementDto.setUnit(resultDetailDto.getUnit());
        }

        // 详细设计进度确认后的变更，只有外购物料才会产生采购需求 + ERP编码不为空
        if (null != resultDetailDto.getNumber() && Objects.equals("外购物料", resultDetailDto.getMaterialCategory())
                && StringUtils.isNotEmpty(resultDetailDto.getErpCode())) {
            //计算当前设计信息的最新发布总量:设计信息的数量*所有父级的积数
            BigDecimal differenceNumber;
            String requirementRedisKey = "requirementFinalNeedTotal_" + resultDetailDto.getProjectId() + "_"
                    + DateUtil.format(resultDetailDto.getDeliveryTime(), "yyyyMMdd") + "_" + resultDetailDto.getPamCode();
            String existRedisResult = stringRedisTemplate.opsForValue().get(requirementRedisKey);
            if (StringUtils.isNotEmpty(existRedisResult)) {
                BigDecimal parentSigmaById = milepostDesignPlanDetailService.getParentSigmaById(vo.getChange().getParentId(), null);
                logger.info("existRedisResult：{}，handleChangeForAdd的详设id：{}，parentSigmaById：{}", existRedisResult, resultDetailDto.getId(), parentSigmaById);

                differenceNumber = resultDetailDto.getNumber().multiply(parentSigmaById);

                String requirementRedisNumber = purchaseMaterialRequirementService.getRequirementRedisNumber(resultDetailDto.getProjectId(),
                        resultDetailDto.getDeliveryTime(), resultDetailDto.getPamCode(), requirementDto.getNeedTotal(), differenceNumber);

                requirementDto.setNeedTotal(new BigDecimal(requirementRedisNumber));
            } else {
                BigDecimal parentSigmaById = milepostDesignPlanDetailService.getParentSigmaById(vo.getChange().getParentId(), null);
                logger.info("requirementRedisKey：{}，handleChangeForAdd的详设id：{}，parentSigmaById：{}，Number：{}",
                        requirementRedisKey, resultDetailDto.getId(), parentSigmaById, resultDetailDto.getNumber());

                differenceNumber = resultDetailDto.getNumber().multiply(parentSigmaById);
                BigDecimal theLastNeedTotal = Optional.ofNullable(requirementDto.getNeedTotal()).orElse(BigDecimal.ZERO);
                theLastNeedTotal = theLastNeedTotal.add(differenceNumber);
                requirementDto.setNeedTotal(theLastNeedTotal);
            }

            requirementDto.setApprovedSupplierNumber(0);
            purchaseMaterialRequirementService.save(requirementDto, userBy);
            if (BigDecimal.ZERO.compareTo(differenceNumber) != 0 && null != requirementDto.getId()) {
                //purchaseMaterialRequirementService.updateStatus(requirementDto.getId());//根据总需求量更新采购需求的状态
                requirementIdList.add(requirementDto.getId());
            }
            // purchaseMaterialRequirementExtMapper.updateApprovedSupplierNumber();

            if (BigDecimal.ZERO.compareTo(differenceNumber) != 0) {
                purchaseMaterialRequirementService.addPurchaseMaterialReleaseDetail(
                        requirementDto.getMaterielId(),
                        requirementDto.getId(),
                        resultDetailDto,
                        ReleaseDetailStatus.CHANGE.code(),
                        userBy,
                        differenceNumber);

                //高并发情况下更新采购需求的总需求量
                /*BigDecimal totalNum = purchaseMaterialReleaseDetailService.getPublishNumSumByPurchaseMaterialRequirementId(requirementDto.getId());
                requirementDto.setNeedTotal(totalNum);
                purchaseMaterialRequirementService.save(requirementDto, userBy);*/
            }

            //产生采购需求
            resultDetailDto.setGenerateRequirement(DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code());
            milepostDesignPlanDetailService.save(resultDetailDto, userBy);
        }

    }

    /**
     * 变更附件信息状态，把审批中状态改为审批通过
     * 注意：
     */
    private void updateCtcAttachmentDtoStatus(Long changeRecordId) {
        CtcAttachmentDto query = new CtcAttachmentDto();
        query.setModuleId(changeRecordId);
        List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(changeRecordId,
                CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code(), null);
        if (!CollectionUtils.isEmpty(attachmentDtos)) {
            for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                attachmentDto.setStatus(AttachmentStatus.PASSED.code());
                ctcAttachmentService.update(attachmentDto);
            }
            //需要把删除状态的附件，同步删除非该更新记录ID的附件
            this.deleteAttachmentDtos(attachmentDtos);
        }
    }

    private void deleteAttachmentDtos(List<CtcAttachmentDto> attachmentDtos) {
        List<Long> attachIds = attachmentDtos.stream()
                .filter(attachment -> Objects.equals(attachment.getDeletedFlag(), Boolean.TRUE)).map(CtcAttachmentDto::getAttachId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(attachIds)) {
            List<CtcAttachmentDto> deleteAttachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(null, null,
                    attachIds);
            if (!CollectionUtils.isEmpty(deleteAttachmentDtos)) {
                for (CtcAttachmentDto attachmentDto : deleteAttachmentDtos) {
                    attachmentDto.setStatus(AttachmentStatus.PASSED.code());
                    attachmentDto.setDeletedFlag(Boolean.TRUE);
                    ctcAttachmentService.update(attachmentDto);
                }
            }
        }
    }

    /**
     * 批量更新采购需求的联系信息
     * @param requirementIdList 需求ID列表
     * @param recordDto 里程碑详细设计方案变更记录dto
     */
    public void batchSaveRequirementDeliveryInfo(List<Long> requirementIdList, MilepostDesignPlanChangeRecordDto recordDto) {
        if (CollectionUtils.isEmpty(requirementIdList)) {
            return;
        }
        for (Long requirementId : requirementIdList) {
            deliveryAddressHistoryService.saveDeliveryAddressHistory(recordDto.getDeliveryAddress()
                    , recordDto.getConsignee()
                    , recordDto.getContactPhone()
                    , requirementId
                    , recordDto.getId()
                    , PurchaseMaterialRequirementDeliveryAddressHistoryTypeEnum.DETAIL_CHANGE.getCode()
                    , PurchaseMaterialRequirementDeliveryAddressHistoryEditTypeEnum.REQUIREMENT_UPDATE.getCode());
        }
    }
}
