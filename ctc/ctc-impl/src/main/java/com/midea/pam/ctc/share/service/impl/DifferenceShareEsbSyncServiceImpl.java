package com.midea.pam.ctc.share.service.impl;

import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.DifferenceShareEsb;
import com.midea.pam.common.ctc.dto.DifferenceShareEsbSyncDTO;
import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.pam.common.ctc.entity.DifferenceShareDataSyncDetail;
import com.midea.pam.common.ctc.entity.InvoicePriceDifferenceRecord;
import com.midea.pam.common.ctc.entity.MaterialUpdateDifferenceRecord;
import com.midea.pam.common.ctc.entity.PurchasePriceDifferenceRecord;
import com.midea.pam.common.ctc.entity.SubjectBalanceRecord;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.DifferenceShareDataSyncDetailSyncStatus;
import com.midea.pam.common.enums.DifferenceShareDataSyncDetailType;
import com.midea.pam.common.enums.DifferenceShareDataSyncStatus;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.DifferenceShareDataSyncDetailMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.ctc.share.service.DifferenceShareEsbSyncService;
import com.midea.pam.ctc.share.service.InvoicePriceDifferenceRecordService;
import com.midea.pam.ctc.share.service.MaterialUpdateDifferenceRecordService;
import com.midea.pam.ctc.share.service.PurchasePriceDifferenceRecordService;
import com.midea.pam.ctc.share.service.SubjectBalanceRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/12/24
 * @description 差异分摊esb同步接口
 */
public class DifferenceShareEsbSyncServiceImpl implements DifferenceShareEsbSyncService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private EsbService esbService;

    @Resource
    private DifferenceShareDataSyncDetailMapper differenceShareDataSyncDetailMapper;

    @Resource
    private InvoicePriceDifferenceRecordService invoicePriceDifferenceRecordService;

    @Resource
    private MaterialUpdateDifferenceRecordService materialUpdateDifferenceRecordService;

    @Resource
    private PurchasePriceDifferenceRecordService purchasePriceDifferenceRecordService;

    @Resource
    private SubjectBalanceRecordService subjectBalanceRecordService;

    @Resource
    private BasedataExtService basedataExtService;

    @Resource
    private OrganizationCustomDictService organizationCustomDictService;

    @Resource
    private SdpCarrierServicel sdpCarrierServicel;

    @Resource
    private SdpService sdpService;

    @Override
    public EsbResponse generateInvoicePriceDifferenceRecord(String glPeriod, Long ouId, Long dataSyncId, Long batchNum) {
        DifferenceShareEsbSyncDTO differenceShareEsbSyncDTO =
                buildDifferenceShareEsbSyncDTO(glPeriod, ouId, dataSyncId, batchNum, DifferenceShareDataSyncDetailType.INVOICE_PRICE_DIFFERENCE_RECORD.getCode());

        differenceShareEsbSyncDTO.setInterfaceCode(DifferenceShareEsb.InvoicePriceDifferenceRecord.ESB_DATA_SOURCE);
        differenceShareEsbSyncDTO.setInterfaceName(DifferenceShareEsb.InvoicePriceDifferenceRecord.CONCURRENT_PROGRAM_NAME);

        //EsbResponse esbResponse = esbService.callCUXPOCOMMONREQAPIPKGPortType(differenceShareEsbSyncDTO);
        EsbResponse esbResponse = sdpService.callGERPPoCommonReq(differenceShareEsbSyncDTO);
        return esbResponse;
    }

    private OrganizationRelDto getOrganizationRelByOuId(Long ouId) {
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        List<OrganizationRelDto> organizationRelList = basedataExtService.getOrganizationRel(query);
        if (ListUtils.isEmpty(organizationRelList)) {
            throw new BizException(Code.ERROR, ouId + "erp组织配置不存在");
        }

        OrganizationRelDto organizationRelDto = organizationRelList.get(0);
        return organizationRelDto;
    }

    private DifferenceShareDataSyncDetail createDifferenceShareDataSyncDetail(String glPeriod, Long ouId, Long dataSyncId, Long batchNum, int type) {
        DifferenceShareDataSyncDetail differenceShareDataSyncDetail = new DifferenceShareDataSyncDetail();
        differenceShareDataSyncDetail.setGlPeriod(glPeriod);
        differenceShareDataSyncDetail.setType(type);
        differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncDetailSyncStatus.NO.getCode());
        differenceShareDataSyncDetail.setDataSyncId(dataSyncId);
        differenceShareDataSyncDetail.setBatchNum(batchNum);
        differenceShareDataSyncDetail.setDeletedFlag(Boolean.FALSE);
        differenceShareDataSyncDetail.setOuId(ouId);
        differenceShareDataSyncDetailMapper.insert(differenceShareDataSyncDetail);

        return differenceShareDataSyncDetail;
    }

    private String getGlPeriodFirstDay(String glPeriod) {
        Date glStartDate = DateUtil.getBeginningOfMonth(glPeriod);
        String startDateStr = DateUtil.format(glStartDate, DateUtil.DATE_PATTERN);
        return startDateStr;
    }

    private String getGlPeriodLastDay(String glPeriod) {
        Date glEndDate = DateUtil.getEndOfMonth(glPeriod);
        String endDateStr = DateUtil.format(glEndDate, DateUtil.DATE_PATTERN);
        return endDateStr;
    }

    private DifferenceShareEsbSyncDTO buildDifferenceShareEsbSyncDTO(String glPeriod, Long ouId, Long dataSyncId, Long batchNum, int type) {
        String startDateStr = getGlPeriodFirstDay(glPeriod);
        String endDateStr = getGlPeriodLastDay(glPeriod);

        OrganizationRelDto organizationRelDto = getOrganizationRelByOuId(ouId);

        DifferenceShareDataSyncDetail differenceShareDataSyncDetail = createDifferenceShareDataSyncDetail(glPeriod, ouId, dataSyncId, batchNum, type);

        DifferenceShareEsbSyncDTO differenceShareEsbSyncDTO = new DifferenceShareEsbSyncDTO();
        differenceShareEsbSyncDTO.setId(differenceShareDataSyncDetail.getId());
        differenceShareEsbSyncDTO.setOrganizationId(organizationRelDto.getOrganizationId());
        differenceShareEsbSyncDTO.setGlStartDate(startDateStr);
        differenceShareEsbSyncDTO.setGlEndDate(endDateStr);
        differenceShareEsbSyncDTO.setOuId(ouId);
        differenceShareEsbSyncDTO.setLedgerId(organizationRelDto.getLedgerId());
        differenceShareEsbSyncDTO.setCompanyCode(organizationRelDto.getCompanyCode());
        differenceShareEsbSyncDTO.setGlPeriod(glPeriod);

        return differenceShareEsbSyncDTO;
    }

    @Override
    public void generateMaterialUpdateDifferenceRecord(String glPeriod, Long ouId, Long dataSyncId, Long batchNum) {
        // 直接调用PAM-ERP-047-01 成本更新差异明细查询，无需事先GSC触发ERP请求
        DifferenceShareDataSyncDetail differenceShareDataSyncDetail = new DifferenceShareDataSyncDetail();
        differenceShareDataSyncDetail.setGlPeriod(glPeriod);
        differenceShareDataSyncDetail.setType(DifferenceShareDataSyncDetailType.MATERIAL_UPDATE_DIFFERENCE_RECORD.getCode());
        differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncDetailSyncStatus.TODO.getCode());
        differenceShareDataSyncDetail.setDataSyncId(dataSyncId);
        differenceShareDataSyncDetail.setBatchNum(batchNum);
        differenceShareDataSyncDetail.setDeletedFlag(Boolean.FALSE);
        differenceShareDataSyncDetail.setOuId(ouId);
        differenceShareDataSyncDetail.setErpInterfaceCode(BusinessTypeEnums.MATERIAL_UPDATE_DIFFERENCE_RECORD_01.getCode());
        differenceShareDataSyncDetailMapper.insert(differenceShareDataSyncDetail);
    }

    @Override
    public EsbResponse generatePurchasePriceDifferenceRecord(String glPeriod, Long ouId, Long dataSyncId, Long batchNum) {
        DifferenceShareEsbSyncDTO differenceShareEsbSyncDTO =
                buildDifferenceShareEsbSyncDTO(glPeriod, ouId, dataSyncId, batchNum, DifferenceShareDataSyncDetailType.PURCHASE_PRICE_DIFFERENCE_RECORD.getCode());

        differenceShareEsbSyncDTO.setInterfaceCode(DifferenceShareEsb.PurchasePriceDifferenceRecord.ESB_DATA_SOURCE);
        differenceShareEsbSyncDTO.setInterfaceName(DifferenceShareEsb.PurchasePriceDifferenceRecord.CONCURRENT_PROGRAM_NAME);

        //EsbResponse esbResponse = esbService.callCUXPOCOMMONREQAPIPKGPortType(differenceShareEsbSyncDTO);
        EsbResponse esbResponse = sdpService.callGERPPoCommonReq(differenceShareEsbSyncDTO);
        return esbResponse;
    }

    @Override
    public EsbResponse generateSubjectBalanceRecord(String glPeriod, Long ouId, Long dataSyncId, Long batchNum) {
        DifferenceShareEsbSyncDTO differenceShareEsbSyncDTO =
                buildDifferenceShareEsbSyncDTO(glPeriod, ouId, dataSyncId, batchNum, DifferenceShareDataSyncDetailType.SUBJECT_BALANCE_RECORD.getCode());

        differenceShareEsbSyncDTO.setInterfaceCode(DifferenceShareEsb.SubjectBalanceRecord.ESB_DATA_SOURCE);
        differenceShareEsbSyncDTO.setInterfaceName(DifferenceShareEsb.SubjectBalanceRecord.CONCURRENT_PROGRAM_NAME);

        //EsbResponse esbResponse = esbService.callCUXPOCOMMONREQAPIPKGPortType(differenceShareEsbSyncDTO);
        EsbResponse esbResponse = sdpService.callGERPPoCommonReq(differenceShareEsbSyncDTO);
        return esbResponse;
    }

    @Override
    public boolean invoicePriceDifferenceRecordSyncFromErp(Long id) {
        DifferenceShareDataSyncDetail differenceShareDataSyncDetail = differenceShareDataSyncDetailMapper.selectByPrimaryKey(id);
        Long currRequestId = differenceShareDataSyncDetail.getCurrRequestId();
        Integer sumPage = differenceShareDataSyncDetail.getSumPage();
        String glPeriod = differenceShareDataSyncDetail.getGlPeriod();
        Long ouId = differenceShareDataSyncDetail.getOuId();

        String startDateStr = getGlPeriodFirstDay(glPeriod);
        String endDateStr = getGlPeriodLastDay(glPeriod);

        // 先删除历史数据
        invoicePriceDifferenceRecordService.deleteByOuIdAndGlDate(ouId, startDateStr, endDateStr);

        differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncDetailSyncStatus.DOING.getCode());
        differenceShareDataSyncDetailMapper.updateByPrimaryKey(differenceShareDataSyncDetail);


        for (Integer i = 1; i <= sumPage; i++) {
            List<SdpTradeResultResponseEleDto> erpMassQueryReturnVos =
                    this.getErpMassQueryReturnVos(differenceShareDataSyncDetail.getErpInterfaceCode(), currRequestId, i);

            List<InvoicePriceDifferenceRecord> invoicePriceDifferenceRecords = new ArrayList<>();
            // 解析
            for (SdpTradeResultResponseEleDto item : erpMassQueryReturnVos) {
                InvoicePriceDifferenceRecord invoicePriceDifferenceRecord = formatInvoicePriceDifferenceRecord(item);
                invoicePriceDifferenceRecords.add(invoicePriceDifferenceRecord);
            }

            if (invoicePriceDifferenceRecords.size() > 0) {
                invoicePriceDifferenceRecords.forEach(invoicePriceDifferenceRecord -> invoicePriceDifferenceRecordService.insert(invoicePriceDifferenceRecord));
            }
        }

        differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncDetailSyncStatus.SUCCESS.getCode());
        differenceShareDataSyncDetailMapper.updateByPrimaryKey(differenceShareDataSyncDetail);

        // 判断是否本次全部成功了，则同步更新同表的同步状态数据

        return true;
    }

    private InvoicePriceDifferenceRecord formatInvoicePriceDifferenceRecord(SdpTradeResultResponseEleDto item) {
        String c1 = item.getC1();// OU ID
        String c2 = item.getC2();// 会计科目段
        String c3 = item.getC3();// 会计科目组合
        String c4 = item.getC4();// 总账日期
        String c5 = item.getC5();// 供应商编码
        String c6 = item.getC6();// 供应商地点ID
        String c7 = item.getC7();// 币种
        String c8 = item.getC8();// 物料编码
        String c9 = item.getC9();// 匹配数量
        String c10 = item.getC10();// 单位
        String c11 = item.getC11();// PO单号
        String c12 = item.getC12();// 项目号
        String c13 = item.getC13();// PO价（本位币）
        String c14 = item.getC14();// 发票单价（本位币）
        String c15 = item.getC15();// 单位发票差异（本位币）
        String c16 = item.getC16();// 汇率
        String c17 = item.getC17();// 发票差异（本位币）

        InvoicePriceDifferenceRecord invoicePriceDifferenceRecord = new InvoicePriceDifferenceRecord();
        invoicePriceDifferenceRecord.setOrgId(c1 != null ? Long.valueOf(c1) : null);
        invoicePriceDifferenceRecord.setAccountCode(c2);
        invoicePriceDifferenceRecord.setConcatenatedSegments(c3);
        invoicePriceDifferenceRecord.setGlDate(c4);
        invoicePriceDifferenceRecord.setVendorNumber(c5);
        invoicePriceDifferenceRecord.setVendorSiteId(conventToLong(c6));
        invoicePriceDifferenceRecord.setCurrencyCode(c7);
        invoicePriceDifferenceRecord.setItemCode(c8);
        invoicePriceDifferenceRecord.setQuantityInvoiced(conventToBigDecimal(c9));
        invoicePriceDifferenceRecord.setItemUom(c10);
        invoicePriceDifferenceRecord.setPoNumber(c11);
        invoicePriceDifferenceRecord.setProjectNum(c12);
        invoicePriceDifferenceRecord.setPoPrice(conventToBigDecimal(c13));
        invoicePriceDifferenceRecord.setInvoicePrice(conventToBigDecimal(c14));
        invoicePriceDifferenceRecord.setDifferenceUnit(conventToBigDecimal(c15));
        invoicePriceDifferenceRecord.setExchangeRate(conventToBigDecimal(c16));
        invoicePriceDifferenceRecord.setDifferenceBaseAmount(conventToBigDecimal(c17));

        invoicePriceDifferenceRecord.setDeletedFlag(Boolean.FALSE);

        return invoicePriceDifferenceRecord;
    }

    private List<SdpTradeResultResponseEleDto> getErpMassQueryReturnVos(String type, Long currRequestId, int pageNum) {
        Map<String, String> paramMap = new HashMap<>();
        // 批次号
//        paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(currRequestId));
        paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(currRequestId));
        // 页码
//        paramMap.put(EsbConstant.ERP_IP_P02, String.valueOf(pageNum));
        paramMap.put(EsbConstant.ERP_SDP_P02, String.valueOf(pageNum));

//        List<ERPMassQueryReturnVo> erpMassQueryReturnVos = esbService.callEsbMassQuery(type, paramMap);
        List<SdpTradeResultResponseEleDto> erpMassQueryReturnVos = sdpCarrierServicel.callSdpMassQuery(type, paramMap);

        return erpMassQueryReturnVos;
    }

    private BigDecimal conventToBigDecimal(String str) {
        if (StringUtils.isNotEmpty(str)) {
            return new BigDecimal(str);
        }
        return null;
    }

    private Long conventToLong(String str) {
        if (StringUtils.isNotEmpty(str)) {
            return Long.valueOf(str);
        }
        return null;
    }

    private Integer conventToInteger(String str) {
        if (StringUtils.isNotEmpty(str)) {
            return Integer.valueOf(str);
        }
        return null;
    }

    @Transactional
    @Override
    public boolean materialUpdateDifferenceRecordSyncFromErp(Long id) {
        DifferenceShareDataSyncDetail differenceShareDataSyncDetail = differenceShareDataSyncDetailMapper.selectByPrimaryKey(id);
        String glPeriod = differenceShareDataSyncDetail.getGlPeriod();
        Long ouId = differenceShareDataSyncDetail.getOuId();

        String startDateStr = getGlPeriodFirstDay(glPeriod);
        String endDateStr = getGlPeriodLastDay(glPeriod);

        OrganizationRelDto organizationRel = getOrganizationRelByOuId(ouId);

        // 先删除历史数据
        materialUpdateDifferenceRecordService.deleteByOrgIdAndGlPeriod(organizationRel.getOrganizationId(), startDateStr, endDateStr);

        // 先查询一次，获取总页数
        List<SdpTradeResultResponseEleDto> erpMassQueryReturnVos =
                this.getErpMassQueryReturnVosByOrganizationId(differenceShareDataSyncDetail.getErpInterfaceCode(), organizationRel.getOrganizationId(), glPeriod, 1);
        if (ListUtils.isEmpty(erpMassQueryReturnVos)) {
            differenceShareDataSyncDetail.setSumPage(0);
            differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncDetailSyncStatus.SUCCESS.getCode());
            differenceShareDataSyncDetailMapper.updateByPrimaryKey(differenceShareDataSyncDetail);
            return false;
        }
        Integer sumPage = Integer.valueOf(erpMassQueryReturnVos.get(0).getC19());
        differenceShareDataSyncDetail.setSumPage(sumPage);
        for (Integer i = 1; i <= sumPage; i++) {
            erpMassQueryReturnVos =
                    this.getErpMassQueryReturnVosByOrganizationId(differenceShareDataSyncDetail.getErpInterfaceCode(), organizationRel.getOrganizationId(), glPeriod, i);

            List<MaterialUpdateDifferenceRecord> materialUpdateDifferenceRecords = new ArrayList<>();
            // 解析
            for (SdpTradeResultResponseEleDto item : erpMassQueryReturnVos) {
                MaterialUpdateDifferenceRecord materialUpdateDifferenceRecord = formatMaterialUpdateDifferenceRecord(item);
                materialUpdateDifferenceRecords.add(materialUpdateDifferenceRecord);
            }

            if (materialUpdateDifferenceRecords.size() > 0) {
                materialUpdateDifferenceRecords.forEach(
                        materialUpdateDifferenceRecord -> materialUpdateDifferenceRecordService.insert(materialUpdateDifferenceRecord));
            }
        }

        differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncDetailSyncStatus.SUCCESS.getCode());
        differenceShareDataSyncDetailMapper.updateByPrimaryKey(differenceShareDataSyncDetail);

        // 判断是否本次全部成功了，则同步更新同表的同步状态数据

        return true;
    }

    private MaterialUpdateDifferenceRecord formatMaterialUpdateDifferenceRecord(SdpTradeResultResponseEleDto item) {
        String c1 = item.getC1();// 库存组织 ID
        String c2 = item.getC2();// 会计科目段
        String c3 = item.getC3();// 会计科目组合
        String c4 = item.getC4();// 总账日期
        String c5 = item.getC5();// 物料编码
        String c6 = item.getC6();// 物料类型
        String c7 = item.getC7();// 物料状态
        String c8 = item.getC8();// 单位
        String c9 = item.getC9();// 更新前单位成本
        String c10 = item.getC10();// 更新前冻本成本取值日期
        String c11 = item.getC11();// 更新后单位成本
        String c12 = item.getC12();// 更新后冻本成本取值日期
        String c13 = item.getC13();// 单位更新金额
        String c14 = item.getC14();// 数量
        String c15 = item.getC15();// 子库存
        String c16 = item.getC16();// 项目号/货位
        String c17 = item.getC17();// 更新总金额
        String c18 = item.getC18();// 更新前金额

        MaterialUpdateDifferenceRecord materialUpdateDifferenceRecord = new MaterialUpdateDifferenceRecord();
        materialUpdateDifferenceRecord.setOrganizationId(conventToLong(c1));
        materialUpdateDifferenceRecord.setAccountSegment(c2);
        materialUpdateDifferenceRecord.setAccountSegment(c3);
        materialUpdateDifferenceRecord.setGlDate(c4);
        materialUpdateDifferenceRecord.setItemCode(c5);
        materialUpdateDifferenceRecord.setItemType(c6);
        materialUpdateDifferenceRecord.setItemStatus(c7);
        materialUpdateDifferenceRecord.setItemUom(c8);
        materialUpdateDifferenceRecord.setPriorCost(conventToBigDecimal(c9));
        materialUpdateDifferenceRecord.setPriorCostDate(c10 != null ? DateUtil.parseDate(c10) : null);
        materialUpdateDifferenceRecord.setNewCost(conventToBigDecimal(c11));
        materialUpdateDifferenceRecord.setNewCostDate(c12 != null ? DateUtil.parseDate(c12) : null);
        materialUpdateDifferenceRecord.setDiffCost(conventToBigDecimal(c13));
        materialUpdateDifferenceRecord.setQty(conventToBigDecimal(c14));
        materialUpdateDifferenceRecord.setSubInv(c15);
        materialUpdateDifferenceRecord.setLocator(c16);
        if (StringUtils.isNotEmpty(c16)) {
            String[] locatorStr = c16.split("\\.");
            if (locatorStr != null && locatorStr.length > 0) {
                materialUpdateDifferenceRecord.setLocator(locatorStr[0]);
            }
        }
        materialUpdateDifferenceRecord.setTrxValueBase(conventToBigDecimal(c17));
        materialUpdateDifferenceRecord.setBeforeAmount(conventToBigDecimal(c18));

        materialUpdateDifferenceRecord.setDeletedFlag(Boolean.FALSE);

        return materialUpdateDifferenceRecord;
    }

    @Override
    public boolean purchasePriceDifferenceRecordSyncFromErp(Long id) {
        DifferenceShareDataSyncDetail differenceShareDataSyncDetail = differenceShareDataSyncDetailMapper.selectByPrimaryKey(id);
        Long currRequestId = differenceShareDataSyncDetail.getCurrRequestId();
        Integer sumPage = differenceShareDataSyncDetail.getSumPage();
        String glPeriod = differenceShareDataSyncDetail.getGlPeriod();
        Long ouId = differenceShareDataSyncDetail.getOuId();

        String startDateStr = getGlPeriodFirstDay(glPeriod);
        String endDateStr = getGlPeriodLastDay(glPeriod);

        purchasePriceDifferenceRecordService.deleteByOrgIdAndGlPeriod(ouId, startDateStr, endDateStr);

        differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncDetailSyncStatus.DOING.getCode());
        differenceShareDataSyncDetailMapper.updateByPrimaryKey(differenceShareDataSyncDetail);

        for (Integer i = 1; i <= sumPage; i++) {
            List<SdpTradeResultResponseEleDto> erpMassQueryReturnVos =
                    this.getErpMassQueryReturnVos(differenceShareDataSyncDetail.getErpInterfaceCode(), currRequestId, i);

            List<PurchasePriceDifferenceRecord> purchasePriceDifferenceRecords = new ArrayList<>();
            // 解析
            for (SdpTradeResultResponseEleDto item : erpMassQueryReturnVos) {
                PurchasePriceDifferenceRecord purchasePriceDifferenceRecord = formatPurchasePriceDifferenceRecord(item);
                purchasePriceDifferenceRecords.add(purchasePriceDifferenceRecord);
            }

            if (purchasePriceDifferenceRecords.size() > 0) {
                purchasePriceDifferenceRecords.forEach(
                        purchasePriceDifferenceRecord -> purchasePriceDifferenceRecordService.insert(purchasePriceDifferenceRecord));
            }
        }

        differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncDetailSyncStatus.SUCCESS.getCode());
        differenceShareDataSyncDetailMapper.updateByPrimaryKey(differenceShareDataSyncDetail);
        return true;
    }

    private PurchasePriceDifferenceRecord formatPurchasePriceDifferenceRecord(SdpTradeResultResponseEleDto item) {
        String c1 = item.getC1();// OU ID
        String c2 = item.getC2();// 会计科目段
        String c3 = item.getC3();// 会计科目组合
        String c4 = item.getC4();// 事务处理日期
        String c5 = item.getC5();// 供应商编码
        String c6 = item.getC6();// 事务处理来源
        String c7 = item.getC7();// 项目号
        String c8 = item.getC8();// 物料编码
        String c9 = item.getC9();// 子库
        String c10 = item.getC10();// 交易数量
        String c11 = item.getC11();// 输入币种
        String c12 = item.getC12();// 采购单价-原币
        String c13 = item.getC13();// 本位币种
        String c14 = item.getC14();// 采购单价-本位币
        String c15 = item.getC15();// 冻结成本
        String c16 = item.getC16();// 单位价差
        String c17 = item.getC17();// 采购价差

        PurchasePriceDifferenceRecord purchasePriceDifferenceRecord = new PurchasePriceDifferenceRecord();
        purchasePriceDifferenceRecord.setOrgId(conventToLong(c1));
        purchasePriceDifferenceRecord.setAccount(c2);
        purchasePriceDifferenceRecord.setAccountCombine(c3);
        purchasePriceDifferenceRecord.setGlDate(c4);
        purchasePriceDifferenceRecord.setVendorCode(c5);
        purchasePriceDifferenceRecord.setTrxSourceName(c6);
        purchasePriceDifferenceRecord.setProjectNum(c7);
        purchasePriceDifferenceRecord.setItemCode(c8);
        purchasePriceDifferenceRecord.setSubInv(c9);
        purchasePriceDifferenceRecord.setTrxQty(new BigDecimal(c10));
        purchasePriceDifferenceRecord.setEnteredCurrencyCode(c11);
        purchasePriceDifferenceRecord.setUnitPrice(conventToBigDecimal(c12));
        purchasePriceDifferenceRecord.setBaseCurrencyCode(c13);
        purchasePriceDifferenceRecord.setPoCost(conventToBigDecimal(c14));
        purchasePriceDifferenceRecord.setForzenCost(conventToBigDecimal(c15));
        purchasePriceDifferenceRecord.setUnitVariance(conventToBigDecimal(c16));
        purchasePriceDifferenceRecord.setVarianceAmountBase(conventToBigDecimal(c17));

        purchasePriceDifferenceRecord.setDeletedFlag(Boolean.FALSE);

        return purchasePriceDifferenceRecord;
    }

    /**
     * 批量处理
     */
    @Override
    public boolean subjectBalanceRecordSyncFromErp(Long id) {
        DifferenceShareDataSyncDetail differenceShareDataSyncDetail = differenceShareDataSyncDetailMapper.selectByPrimaryKey(id);
        Long currRequestId = differenceShareDataSyncDetail.getCurrRequestId();
        Integer sumPage = differenceShareDataSyncDetail.getSumPage();
        String glPeriod = differenceShareDataSyncDetail.getGlPeriod();
        Long ouId = differenceShareDataSyncDetail.getOuId();

        subjectBalanceRecordService.deleteByGlPeriod(glPeriod, ouId);

        differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncDetailSyncStatus.DOING.getCode());
        differenceShareDataSyncDetailMapper.updateByPrimaryKey(differenceShareDataSyncDetail);
        // 根据account_code 中第一段从OU组织参数中查找OU code  根据OU code找出OU id
        Map<Long, Set<String>> accountCodeMap = organizationCustomDictService.selectByName("OU_CODE",
                null, OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom("ou"));
        for (Integer i = 1; i <= sumPage; i++) {
            List<SdpTradeResultResponseEleDto> erpMassQueryReturnVos =
                    this.getErpMassQueryReturnVos(differenceShareDataSyncDetail.getErpInterfaceCode(), currRequestId, i);

            List<SubjectBalanceRecord> subjectBalanceRecords = new ArrayList<>();
            // 解析
            for (SdpTradeResultResponseEleDto item : erpMassQueryReturnVos) {
                SubjectBalanceRecord subjectBalanceRecord = formatSubjectBalanceRecord(item, accountCodeMap);
                subjectBalanceRecords.add(subjectBalanceRecord);
            }

            if (subjectBalanceRecords.size() > 0) {
                subjectBalanceRecords.forEach(
                        subjectBalanceRecord -> subjectBalanceRecordService.insert(subjectBalanceRecord));
            }
        }

        differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncDetailSyncStatus.SUCCESS.getCode());
        differenceShareDataSyncDetailMapper.updateByPrimaryKey(differenceShareDataSyncDetail);

        // 同步更新发生额度（subjectBalance）
        return true;
    }

    @Override
    public boolean syncFromErp(Long id) {
        DifferenceShareDataSyncDetail differenceShareDataSyncDetail = differenceShareDataSyncDetailMapper.selectByPrimaryKey(id);
        int type = differenceShareDataSyncDetail.getType();

        if (DifferenceShareDataSyncDetailType.INVOICE_PRICE_DIFFERENCE_RECORD.getCode() == type) {
            this.invoicePriceDifferenceRecordSyncFromErp(id);
        } else if (DifferenceShareDataSyncDetailType.MATERIAL_UPDATE_DIFFERENCE_RECORD.getCode() == type) {
            this.materialUpdateDifferenceRecordSyncFromErp(id);
        } else if (DifferenceShareDataSyncDetailType.PURCHASE_PRICE_DIFFERENCE_RECORD.getCode() == type) {
            this.purchasePriceDifferenceRecordSyncFromErp(id);
        } else if (DifferenceShareDataSyncDetailType.SUBJECT_BALANCE_RECORD.getCode() == type) {
            this.subjectBalanceRecordSyncFromErp(id);
        }

        return true;
    }

    private SubjectBalanceRecord formatSubjectBalanceRecord(SdpTradeResultResponseEleDto item, Map<Long, Set<String>> accountCodeMap) {
        String c1 = item.getC1();// 会计科目
        String c2 = item.getC2();// 科目组合
        String c3 = item.getC3();// 期间名称
        String c4 = item.getC4();// 期初余额
        String c5 = item.getC5();// 借方发生额
        String c6 = item.getC6();// 贷方发生额
        String c7 = item.getC7();// 期末余额
        String c8 = item.getC8();// 帐套 ID

        SubjectBalanceRecord subjectBalanceRecord = new SubjectBalanceRecord();
        subjectBalanceRecord.setSegment3(c1);
        subjectBalanceRecord.setAccountCode(c2);
        subjectBalanceRecord.setPeriod(c3);
        subjectBalanceRecord.setBeginT(conventToBigDecimal(c4));
        subjectBalanceRecord.setPeriodNetDr(conventToBigDecimal(c5));
        subjectBalanceRecord.setPeriodNetCr(conventToBigDecimal(c6));
        subjectBalanceRecord.setEndT(conventToBigDecimal(c7));
        subjectBalanceRecord.setLedgerId(conventToLong(c8));

        // 根据account_code 中第一段从OU组织参数中查找OU code  根据OU code找出OU id
        if (StringUtils.isNotEmpty(c2) && accountCodeMap != null && accountCodeMap.size() > 0) {
            String[] accountCodeStr = c2.split("\\.");
            if (accountCodeStr != null && accountCodeStr.length > 0) {
                String accountCode = accountCodeStr[0];
                Set<Long> ouIdList = accountCodeMap.keySet();
                for (Long ouId : ouIdList) {
                    if (Objects.equals(accountCodeMap.get(ouId).iterator().next(), accountCode)) {
                        subjectBalanceRecord.setOrganizationId(ouId);
                        break;
                    }
                }
            }
        }

        subjectBalanceRecord.setDeletedFlag(Boolean.FALSE);

        return subjectBalanceRecord;
    }


    @Override
    public boolean esbResultReturnHandle(Long id, Boolean success, String erpInterfaceCode, Long currRequestId, int sumPage, String message, int type) {
        DifferenceShareDataSyncDetail differenceShareDataSyncDetail = differenceShareDataSyncDetailMapper.selectByPrimaryKey(id);
        if (differenceShareDataSyncDetail == null) {
            logger.info("差异分摊入账单同步esb回调{}不存在", id);
            return false;
        }

        if (success) {
            differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncStatus.TODO.getCode());
            differenceShareDataSyncDetail.setSumPage(sumPage);
            differenceShareDataSyncDetail.setCurrRequestId(currRequestId);
            differenceShareDataSyncDetail.setType(type);
            differenceShareDataSyncDetail.setErpInterfaceCode(erpInterfaceCode);
            differenceShareDataSyncDetailMapper.updateByPrimaryKey(differenceShareDataSyncDetail);
        } else {
            differenceShareDataSyncDetail.setSyncStatus(DifferenceShareDataSyncStatus.FAILURE.getCode());
            differenceShareDataSyncDetail.setSyncMessage(message);
            differenceShareDataSyncDetail.setType(type);
            differenceShareDataSyncDetailMapper.updateByPrimaryKey(differenceShareDataSyncDetail);
        }

        return true;
    }

    @Override
    public List<SdpTradeResultResponseEleDto> getErpMassQueryReturnVosByOrganizationId(String erpInterfaceCode, Long organizationId, String glPeriod, Integer pageNum) {
        Map<String, String> paramMap = new HashMap<>();
        // 库存组织
//        paramMap.put(EsbConstant.ERP_IP_P01, String.valueOf(organizationId));
        paramMap.put(EsbConstant.ERP_SDP_P01, String.valueOf(organizationId));
        String startDateStr = getGlPeriodFirstDay(glPeriod);
        String endDateStr = getGlPeriodLastDay(glPeriod);
        // 总账日期起
//        paramMap.put(EsbConstant.ERP_IP_P02, startDateStr);
        paramMap.put(EsbConstant.ERP_SDP_P02, startDateStr);
        // 总账日期止
//        paramMap.put(EsbConstant.ERP_IP_P03, endDateStr);
        paramMap.put(EsbConstant.ERP_SDP_P03, endDateStr);
        // 页码
//        paramMap.put(EsbConstant.ERP_IP_P04, String.valueOf(pageNum));
        paramMap.put(EsbConstant.ERP_SDP_P04, String.valueOf(pageNum));

//        List<ERPMassQueryReturnVo> erpMassQueryReturnVos = esbService.callEsbMassQuery(erpInterfaceCode, paramMap);
        List<SdpTradeResultResponseEleDto> erpMassQueryReturnVos = sdpCarrierServicel.callSdpMassQuery(erpInterfaceCode, paramMap);

        return erpMassQueryReturnVos;
    }

}
