package com.midea.pam.ctc.contract.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.IdEntity;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.PurchaseContractNameDuplicateVo;
import com.midea.pam.common.basedata.dto.PurchaseContractTrackLeadDto;
import com.midea.pam.common.basedata.dto.TaxInfoDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.VendorSiteBank;
import com.midea.pam.common.basedata.entity.VendorSiteBankForDisplay;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.AssertErrorMessage;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDetailDto;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.CtcOperatingRecordDto;
import com.midea.pam.common.ctc.dto.GlegalContractFileInfoDto;
import com.midea.pam.common.ctc.dto.HroRequirementItemDto;
import com.midea.pam.common.ctc.dto.HroWorkingHourItemDto;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDto;
import com.midea.pam.common.ctc.dto.PaymentPlanDTO;
import com.midea.pam.common.ctc.dto.ProjectContractBudgetMaterialDto;
import com.midea.pam.common.ctc.dto.PunishmentAmountDTO;
import com.midea.pam.common.ctc.dto.PurchaseContractBudgetChangeHistoryDto;
import com.midea.pam.common.ctc.dto.PurchaseContractBudgetDto;
import com.midea.pam.common.ctc.dto.PurchaseContractChangeHeaderDto;
import com.midea.pam.common.ctc.dto.PurchaseContractChangeHistoryDTO;
import com.midea.pam.common.ctc.dto.PurchaseContractChangeHistoryVO;
import com.midea.pam.common.ctc.dto.PurchaseContractContentChangeHistoryVO;
import com.midea.pam.common.ctc.dto.PurchaseContractDTO;
import com.midea.pam.common.ctc.dto.PurchaseContractDetailDTO;
import com.midea.pam.common.ctc.dto.PurchaseContractPenaltyChangeHistoryDTO;
import com.midea.pam.common.ctc.dto.PurchaseContractProgressBudgetRelDto;
import com.midea.pam.common.ctc.dto.PurchaseContractProgressDetailSubjectDto;
import com.midea.pam.common.ctc.dto.PurchaseContractProgressDto;
import com.midea.pam.common.ctc.dto.PurchaseContractPunishmentDto;
import com.midea.pam.common.ctc.dto.PurchaseContractQualityReportDto;
import com.midea.pam.common.ctc.dto.PurchaseContractStampDto;
import com.midea.pam.common.ctc.dto.PurchaseContractStandardTermsDto;
import com.midea.pam.common.ctc.dto.PurchaseContractTemplateDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialReleaseDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.SealAdministratorDTO;
import com.midea.pam.common.ctc.entity.CarryoverBillExample;
import com.midea.pam.common.ctc.entity.CostCollection;
import com.midea.pam.common.ctc.entity.CtcOperatingRecord;
import com.midea.pam.common.ctc.entity.GscPaymentInvoiceDetail;
import com.midea.pam.common.ctc.entity.HroWorkingHourItem;
import com.midea.pam.common.ctc.entity.MaterialOutsourceCostDetail;
import com.midea.pam.common.ctc.entity.MaterialOutsourceCostDetailExample;
import com.midea.pam.common.ctc.entity.MaterialOutsourcingContractConfig;
import com.midea.pam.common.ctc.entity.MaterialOutsourcingContractConfigExample;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentApplyExample;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetail;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetailExample;
import com.midea.pam.common.ctc.entity.PaymentPenaltyProfit;
import com.midea.pam.common.ctc.entity.PaymentPenaltyProfitExample;
import com.midea.pam.common.ctc.entity.PaymentPlan;
import com.midea.pam.common.ctc.entity.PaymentPlanChangeHistory;
import com.midea.pam.common.ctc.entity.PaymentPlanChangeHistoryDto;
import com.midea.pam.common.ctc.entity.PaymentPlanChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PaymentPlanExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterial;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialExample;
import com.midea.pam.common.ctc.entity.ProjectContractBudgetMaterial;
import com.midea.pam.common.ctc.entity.ProjectContractBudgetMaterialChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectContractBudgetMaterialChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectContractBudgetMaterialExample;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectWbsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractBudget;
import com.midea.pam.common.ctc.entity.PurchaseContractBudgetChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseContractBudgetChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseContractBudgetExample;
import com.midea.pam.common.ctc.entity.PurchaseContractChangeHeader;
import com.midea.pam.common.ctc.entity.PurchaseContractChangeHeaderExample;
import com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseContractChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseContractDetail;
import com.midea.pam.common.ctc.entity.PurchaseContractDetailChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseContractDetailChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseContractDetailExample;
import com.midea.pam.common.ctc.entity.PurchaseContractExample;
import com.midea.pam.common.ctc.entity.PurchaseContractPenalty;
import com.midea.pam.common.ctc.entity.PurchaseContractPenaltyChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseContractPenaltyChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseContractPenaltyExample;
import com.midea.pam.common.ctc.entity.PurchaseContractProgress;
import com.midea.pam.common.ctc.entity.PurchaseContractProgressBudgetRel;
import com.midea.pam.common.ctc.entity.PurchaseContractProgressBudgetRelExample;
import com.midea.pam.common.ctc.entity.PurchaseContractProgressDetailSubject;
import com.midea.pam.common.ctc.entity.PurchaseContractProgressDetailSubjectExample;
import com.midea.pam.common.ctc.entity.PurchaseContractProgressExample;
import com.midea.pam.common.ctc.entity.PurchaseContractPunishment;
import com.midea.pam.common.ctc.entity.PurchaseContractQualityReport;
import com.midea.pam.common.ctc.entity.PurchaseContractQualityReportExample;
import com.midea.pam.common.ctc.entity.PurchaseContractStamp;
import com.midea.pam.common.ctc.entity.PurchaseContractStampExample;
import com.midea.pam.common.ctc.entity.PurchaseContractStandardTerms;
import com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviation;
import com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviationExample;
import com.midea.pam.common.ctc.entity.PurchaseContractTemplate;
import com.midea.pam.common.ctc.entity.PurchaseContractTemplateConfig;
import com.midea.pam.common.ctc.entity.PurchaseContractTemplateConfigExample;
import com.midea.pam.common.ctc.entity.PurchaseContractTemplateExample;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementExample;
import com.midea.pam.common.ctc.excelVo.TemplatePaymentPlanExcelVO;
import com.midea.pam.common.ctc.excelVo.TemplatePurchaseContractBudgetExcelVO;
import com.midea.pam.common.ctc.excelVo.TemplatePurchaseContractDetailExcelVO;
import com.midea.pam.common.ctc.excelVo.TemplatePurchaseContractExcelVO;
import com.midea.pam.common.ctc.excelVo.TemplatePurchaseContractProgressExcelVO;
import com.midea.pam.common.ctc.vo.ProjectContractBudgetMaterialVo;
import com.midea.pam.common.ctc.vo.PurchaseContractBudgetPdfVo;
import com.midea.pam.common.ctc.vo.PurchaseContractDetailPdfVo;
import com.midea.pam.common.ctc.vo.PurchaseContractPdfVo;
import com.midea.pam.common.ctc.vo.PurchaseContractProgressVo;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentVo;
import com.midea.pam.common.ctc.vo.PurchaseContractVO;
import com.midea.pam.common.enums.AttachmentStatus;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.CarryoverBillReverseStatus;
import com.midea.pam.common.enums.ChangeType;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.ContractStatus;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.FreezeFlag;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.MilepostStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PaymentApplyBizStatus;
import com.midea.pam.common.enums.PaymentApplyGcebErpStatus;
import com.midea.pam.common.enums.PaymentApplySourceNameEnum;
import com.midea.pam.common.enums.PaymentInvoiceAccountEntryTypeEnum;
import com.midea.pam.common.enums.PurchaseContractChangeType;
import com.midea.pam.common.enums.PurchaseContractPunishmentApproveEnums;
import com.midea.pam.common.enums.PurchaseContractPunishmentSourceEnums;
import com.midea.pam.common.enums.PurchaseContractStatus;
import com.midea.pam.common.enums.ReceiptsConfirmModeEnum;
import com.midea.pam.common.enums.RequirementStatusEnum;
import com.midea.pam.common.enums.RequirementTypeEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.Builder;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.CollectionsUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CommonErpStatus;
import com.midea.pam.ctc.common.enums.ContractTermsFlgEnum;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.CtcOperatingRecordModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.enums.MaterialOutsourceCostDetailType;
import com.midea.pam.ctc.common.enums.ProjectWbsReceiptsBudgetDemandTypeEnums;
import com.midea.pam.ctc.common.enums.PurchaseContractChangeHeaderStatus;
import com.midea.pam.ctc.common.enums.PurchaseContractProgressCollectionStatus;
import com.midea.pam.ctc.common.enums.PurchaseContractProgressStatus;
import com.midea.pam.ctc.common.enums.PurchaseContractStampType;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementPurchaseTypeEnums;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.contract.service.GlegalPurchaseContractWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.LegalContractService;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.contract.service.PaymentPlanService;
import com.midea.pam.ctc.contract.service.PurchaseContractBudgetMaterialService;
import com.midea.pam.ctc.contract.service.PurchaseContractBudgetService;
import com.midea.pam.ctc.contract.service.PurchaseContractDetailService;
import com.midea.pam.ctc.contract.service.PurchaseContractPunishmentService;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.contract.service.helper.PurchaseContractHelper;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.feign.basedata.feign.BasedataTaxListFeignClient;
import com.midea.pam.ctc.mapper.CarryoverBillExtMapper;
import com.midea.pam.ctc.mapper.CarryoverBillMapper;
import com.midea.pam.ctc.mapper.CostRatioConfigDetailExtMapper;
import com.midea.pam.ctc.mapper.CtcOperatingRecordExtMapper;
import com.midea.pam.ctc.mapper.CtcOperatingRecordMapper;
import com.midea.pam.ctc.mapper.GlegalFileInfoExtMapper;
import com.midea.pam.ctc.mapper.GscPaymentInvoiceDetailExtMapper;
import com.midea.pam.ctc.mapper.HroRequirementItemExtMapper;
import com.midea.pam.ctc.mapper.HroWorkingHourBillItemExtMapper;
import com.midea.pam.ctc.mapper.HroWorkingHourExtMapper;
import com.midea.pam.ctc.mapper.HroWorkingHourItemMapper;
import com.midea.pam.ctc.mapper.MaterialOutsourceCostDetailMapper;
import com.midea.pam.ctc.mapper.MaterialOutsourcingContractConfigMapper;
import com.midea.pam.ctc.mapper.PaymentApplyExtMapper;
import com.midea.pam.ctc.mapper.PaymentApplyMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailExtMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceExtMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.PaymentPenaltyProfitMapper;
import com.midea.pam.ctc.mapper.PaymentPlanChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.PaymentPlanChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PaymentPlanExtMapper;
import com.midea.pam.ctc.mapper.PaymentPlanMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetMaterialMapper;
import com.midea.pam.ctc.mapper.ProjectContractBudgetMaterialChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectContractBudgetMaterialExtMapper;
import com.midea.pam.ctc.mapper.ProjectContractBudgetMaterialMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsMapper;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetMapper;
import com.midea.pam.ctc.mapper.PurchaseContractChangeHeaderExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractChangeHeaderMapper;
import com.midea.pam.ctc.mapper.PurchaseContractChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseContractDetailChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseContractDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseContractExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPenaltyChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPenaltyMapper;
import com.midea.pam.ctc.mapper.PurchaseContractProgressBudgetRelExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractProgressBudgetRelMapper;
import com.midea.pam.ctc.mapper.PurchaseContractProgressDetailSubjectMapper;
import com.midea.pam.ctc.mapper.PurchaseContractProgressExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractProgressMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPunishmentExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPunishmentMapper;
import com.midea.pam.ctc.mapper.PurchaseContractQualityReportMapper;
import com.midea.pam.ctc.mapper.PurchaseContractStampMapper;
import com.midea.pam.ctc.mapper.PurchaseContractStandardTermsDeviationMapper;
import com.midea.pam.ctc.mapper.PurchaseContractStandardTermsMapper;
import com.midea.pam.ctc.mapper.PurchaseContractTemplateConfigMapper;
import com.midea.pam.ctc.mapper.PurchaseContractTemplateExtMapper;
import com.midea.pam.ctc.mapper.PurchaseContractTemplateMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementExtMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.BusiSceneNonSaleService;
import com.midea.pam.ctc.service.CostCollectionService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.HroWorkingHourService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.PaymentInvoiceService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.PurchaseContractProgressBudgetRelService;
import com.midea.pam.ctc.service.PurchaseContractProgressService;
import com.midea.pam.ctc.service.PurchaseContractQualityReportService;
import com.midea.pam.ctc.service.PurchaseContractStampService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-6-4
 * @description 采购合同
 */
public class PurchaseContractServiceImpl implements PurchaseContractService {

    private static final Logger logger = LoggerFactory.getLogger(PurchaseContractServiceImpl.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    public static final Long NOT_EXIST_ID = -1L;

    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private PaymentApplyExtMapper paymentApplyExtMapper;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private PaymentApplyMapper paymentApplyMapper;
    @Resource
    private CarryoverBillMapper carryoverBillMapper;
    @Resource
    private CarryoverBillExtMapper carryoverBillExtMapper;
    @Resource
    private PaymentPlanMapper paymentPlanMapper;
    @Resource
    private ProjectWbsBudgetService projectWbsBudgetService;
    @Resource
    private PurchaseContractPenaltyMapper purchaseContractPenaltyMapper;
    @Resource
    private PurchaseContractPenaltyChangeHistoryMapper purchaseContractPenaltyChangeHistoryMapper;
    @Resource
    private PurchaseContractDetailMapper purchaseContractDetailMapper;
    @Resource
    private PaymentInvoiceDetailMapper paymentInvoiceDetailMapper;
    @Resource
    private PurchaseContractDetailService purchaseContractDetailService;
    @Resource
    private PurchaseContractChangeHeaderMapper purchaseContractChangeHeaderMapper;
    @Resource
    private PaymentPlanService paymentPlanService;
    @Resource
    private PurchaseContractChangeHistoryMapper purchaseContractChangeHistoryMapper;
    @Resource
    private PurchaseContractDetailChangeHistoryMapper purchaseContractDetailChangeHistoryMapper;
    @Resource
    private PaymentPlanChangeHistoryMapper paymentPlanChangeHistoryMapper;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private LegalContractService legalContractService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ProjectService projectService;
    @Resource
    private PurchaseContractBudgetMaterialService purchaseContractBudgetMaterialService;
    @Resource
    private ProjectContractBudgetMaterialExtMapper budgetMaterialExtMapper;
    @Resource
    private ProjectContractBudgetMaterialMapper budgetMaterialMapper;
    @Resource
    private ProjectBudgetMaterialMapper projectBudgetMaterialMapper;
    @Resource
    private PurchaseContractExtMapper purchaseContractExtMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private ProjectContractBudgetMaterialMapper projectContractBudgetMaterialMapper;
    @Resource
    private ProjectContractBudgetMaterialChangeHistoryMapper projectContractBudgetMaterialChangeHistoryMapper;
    @Resource
    private BasedataTaxListFeignClient basedataTaxListFeignClient;
    @Resource
    private GlegalFileInfoExtMapper glegalFileInfoExtMapper;
    @Resource
    private PaymentInvoiceExtMapper paymentInvoiceExtMapper;
    @Resource
    private PurchaseContractProgressMapper purchaseContractProgressMapper;
    @Resource
    private PurchaseContractProgressService purchaseContractProgressService;
    @Resource
    private PurchaseContractProgressBudgetRelMapper purchaseContractProgressBudgetRelMapper;
    @Resource
    private PurchaseContractProgressBudgetRelService purchaseContractProgressBudgetRelService;
    @Resource
    private PurchaseContractQualityReportMapper purchaseContractQualityReportMapper;
    @Resource
    private PurchaseContractQualityReportService purchaseContractQualityReportService;
    @Resource
    private PurchaseContractBudgetMapper purchaseContractBudgetMapper;
    @Resource
    private PurchaseContractBudgetService purchaseContractBudgetService;
    @Resource
    private PurchaseContractBudgetChangeHistoryMapper purchaseContractBudgetChangeHistoryMapper;
    @Resource
    private PurchaseContractBudgetExtMapper purchaseContractBudgetExtMapper;
    @Resource
    private PurchaseContractService purchaseContractService;
    @Resource
    private PurchaseMaterialRequirementMapper purchaseMaterialRequirementMapper;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private ProjectWbsReceiptsMapper projectWbsReceiptsMapper;
    @Resource
    private PurchaseContractStampService purchaseContractStampService;
    @Resource
    private PurchaseContractStampMapper purchaseContractStampMapper;
    @Resource
    private PurchaseMaterialRequirementExtMapper purchaseMaterialRequirementExtMapper;
    @Resource
    private ProjectWbsReceiptsExtMapper projectWbsReceiptsExtMapper;
    @Resource
    private PurchaseContractProgressBudgetRelExtMapper purchaseContractProgressBudgetRelExtMapper;
    @Resource
    private PurchaseContractProgressExtMapper purchaseContractProgressExtMapper;
    @Resource
    private ProjectWbsReceiptsBudgetExtMapper projectWbsReceiptsBudgetExtMapper;
    @Resource
    private PurchaseContractChangeHistoryExtMapper purchaseContractChangeHistoryExtMapper;
    @Resource
    private PaymentPlanChangeHistoryExtMapper paymentPlanChangeHistoryExtMapper;
    @Resource
    private PurchaseContractBudgetChangeHistoryExtMapper purchaseContractBudgetChangeHistoryExtMapper;
    @Resource
    private PurchaseContractChangeHeaderExtMapper purchaseContractChangeHeaderExtMapper;
    @Resource
    private CtcOperatingRecordMapper ctcOperatingRecordMapper;
    @Resource
    private CtcOperatingRecordExtMapper ctcOperatingRecordExtMapper;
    @Resource
    private PurchaseContractProgressDetailSubjectMapper purchaseContractProgressDetailSubjectMapper;
    @Resource
    private HroRequirementItemExtMapper hroRequirementItemExtMapper;
    @Resource
    private HroWorkingHourItemMapper hourItemMapper;
    @Resource
    private HroWorkingHourExtMapper hroWorkingHourExtMapper;
    @Resource
    private PurchaseMaterialRequirementService purchaseMaterialRequirementService;
    @Resource
    private HroWorkingHourBillItemExtMapper hroWorkingHourBillItemExtMapper;
    @Resource
    private ProjectExtMapper projectExtMapper;
    @Resource
    private HroWorkingHourService hroWorkingHourService;
    @Resource
    private BusiSceneNonSaleService busiSceneNonSaleService;
    @Resource
    private PaymentInvoiceDetailExtMapper paymentInvoiceDetailExtMapper;
    @Resource
    private PaymentApplyService paymentApplyService;
    @Resource
    private GlegalPurchaseContractWorkflowCallbackService glegalPurchaseContractWorkflowCallbackService;
    @Resource
    private PurchaseContractTemplateMapper purchaseContractTemplateMapper;
    @Resource
    private PurchaseContractTemplateExtMapper purchaseContractTemplateExtMapper;
    @Resource
    private PurchaseContractTemplateConfigMapper purchaseContractTemplateConfigMapper;
    @Resource
    private MaterialOutsourcingContractConfigMapper materialOutsourcingContractConfigMapper;
    @Resource
    private CostRatioConfigDetailExtMapper costRatioConfigDetailExtMapper;
    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;
    @Resource
    private MaterialOutsourceCostDetailMapper materialOutsourceCostDetailMapper;
    @Resource
    private CostCollectionService costCollectionService;
    @Resource
    private PaymentPlanExtMapper paymentPlanExtMapper;
    @Resource
    private PurchaseContractPunishmentMapper purchaseContractPunishmentMapper;
    @Resource
    private PurchaseContractPunishmentService purchaseContractPunishmentService;
    @Resource
    private PaymentPenaltyProfitMapper paymentPenaltyProfitMapper;
    @Resource
    private PurchaseContractPunishmentExtMapper purchaseContractPunishmentExtMapper;
    @Resource
    private PaymentInvoiceService paymentInvoiceService;
    @Resource
    private GscPaymentInvoiceDetailExtMapper gscPaymentInvoiceDetailExtMapper;
    @Resource
    private PurchaseContractStandardTermsMapper purchaseContractStandardTermsMapper;
    @Resource
    private PurchaseContractStandardTermsDeviationMapper purchaseContractStandardTermsDeviationMapper;


    @Override
    public List<PurchaseContract> selectByExample(PurchaseContractExample example) {
        return purchaseContractMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public int save(PurchaseContract purchaseContract) {
        return purchaseContractMapper.insertSelective(purchaseContract);
    }

    @Override
    public int update(PurchaseContract purchaseContract) {
        return purchaseContractMapper.updateByPrimaryKeyWithBLOBs(purchaseContract);
    }

    @Override
    public PurchaseContract findById(Long id) {
        return purchaseContractMapper.selectByPrimaryKey(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseContractVO saveBaseInfo(PurchaseContractDTO purchaseContractDTO) {
        baseInfoParamCheck(purchaseContractDTO);
        return saveBaseInfoTemporary(purchaseContractDTO);
    }

    private void baseInfoParamCheck(PurchaseContractDTO purchaseContractDTO) {
        Assert.notNull(purchaseContractDTO.getProjectId(), "关联项目不能为空");
        Assert.notNull(purchaseContractDTO.getName(), "合同名称不能为空");
        Assert.notNull(purchaseContractDTO.getVendorId(), "供应商不能为空");
        Assert.notNull(purchaseContractDTO.getAmount(), "合同金额不能为空");
        Assert.notNull(purchaseContractDTO.getStartTime(), "合同有效期不能为空");
        Assert.notNull(purchaseContractDTO.getEndTime(), "合同有效期不能为空");
//        Assert.notNull(purchaseContractDTO.getOriginalContractAnnex(), "合同原件不能为空");
        Assert.notNull(purchaseContractDTO.getPurchasingFollower(), "采购跟进人不能为空");
        Assert.notNull(purchaseContractDTO.getTaxId(), "合同税率不能为空");
        Assert.hasText(purchaseContractDTO.getTaxRate(), "合同税率不能为空");
        Assert.notNull(purchaseContractDTO.getInvoiceType(), "发票类型不能为空");

        Set<String> typeSet = organizationCustomDictService.queryByName("PAM采购合同写入法务系统审批", SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
        if (typeSet.size() != 1) {
            throw new BizException(Code.ERROR, "组织参数【PAM采购合同写入法务系统审批】配置错误");
        }
        String value = typeSet.iterator().next(); // 获取集合中的唯一元素
        int dictConfig = Integer.parseInt(value);
        switch (dictConfig) {
            case 0:
                purchaseContractDTO.setIsSynchronizeLegalSystemFlag(false);
                purchaseContractDTO.setNotsyncType("组织参数配置不同步");
                break;
            case 1:
                purchaseContractDTO.setIsSynchronizeLegalSystemFlag(true);
                break;
            case 2:
                if (Objects.isNull(purchaseContractDTO.getIsSynchronizeLegalSystemFlag())) {
                    throw new MipException("是否同步法务系统不能为空");
                }
                if (Boolean.FALSE.equals(purchaseContractDTO.getIsSynchronizeLegalSystemFlag())) {
                    Assert.notBlank(purchaseContractDTO.getNotsyncType(), "不同步法务类型不能为空");
                    Assert.notBlank(purchaseContractDTO.getNotsyncReason(), "不同步法务原因不能为空");
                }
                break;
            default:
                throw new MipException("组织参数【PAM采购合同写入法务系统审批】配置错误");
        }

        // 有效性校验
        // 校验社会统一信用代码
        VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(purchaseContractDTO.getVendorId());
        if (Boolean.TRUE.equals(purchaseContractDTO.getIsElectronicContract()) && StringUtils.isEmpty(vendorSiteBankDto.getUniteCreditCode())) {
            throw new BizException(Code.ERROR, "主体缺少社会统一信用代码，请走纸质合同");
        }
        // 校验供应商是否是不可采购状态
        if (!Objects.equals(vendorSiteBankDto.getPurchasingSiteFlag(), "Y")) {
            throw new BizException(Code.ERROR, String.format("%s当前是不可采购状态，如需采购请至SRM系统进行修改", vendorSiteBankDto.getVendorName()));
        }

        // 校验法务合同编号
        final String legalAffairsCode = purchaseContractDTO.getLegalAffairsCode();

        if (StringUtils.isNotEmpty(legalAffairsCode)) {
            legalContractService.check(legalAffairsCode, purchaseContractDTO.getId());
        }

        Project project = projectMapper.selectByPrimaryKey(purchaseContractDTO.getProjectId());
        // 项目的启用wbs = false 走原逻辑
        if (null == project || !Boolean.TRUE.equals(project.getWbsEnabled())) {
            // 检查合同金额是否超项目预算，公式： 可用项目预算（不含税） -  合同金额（不含税）  >= 0
            BigDecimal projectBudget = projectBusinessService.findAvailableProjectBudget(purchaseContractDTO.getProjectId(),
                    purchaseContractDTO.getId());
            double taxRate = Double.parseDouble(purchaseContractDTO.getTaxRate().replace("%", "")) * 0.01;
            BigDecimal excludingTaxAmount = purchaseContractDTO.getAmount().multiply(BigDecimal.valueOf(taxRate));
            if (BigDecimalUtils.isGreater(excludingTaxAmount, projectBudget)) {
                throw new BizException(Code.ERROR, "可用项目预算[" + projectBudget + "]不足，请重新修改合同金额");
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseContractVO saveBaseInfoTemporary(PurchaseContractDTO purchaseContractDTO) {
        if (StringUtils.isNotEmpty(purchaseContractDTO.getContractTerms()) && purchaseContractDTO.getContractTerms().length() > 100000) {
            throw new ApplicationBizException("合同条款长度不能超过100000");
        }
        if (StringUtils.isNotEmpty(purchaseContractDTO.getRemark()) && purchaseContractDTO.getRemark().length() > 1000) {
            throw new ApplicationBizException("备注字段超长");
        }

        PurchaseContract purchaseContract = new PurchaseContract();
        BeanConverter.copy(purchaseContractDTO, purchaseContract);

        Boolean gleSignFlag = StringUtils.hasText(purchaseContract.getOriginalContractAnnex()) ? Boolean.TRUE : Boolean.FALSE;
        if (purchaseContract.getId() == null) {
            purchaseContract.setStatus(PurchaseContractStatus.DRAFT.getCode());
            purchaseContract.setDeletedFlag(Boolean.FALSE);
            //根据虚拟单位ID获取单据前缀
            String seqPerfix = basedataExtService.getUnitSeqPerfix(SystemContext.getUnitId());
            // 合同编号
            final String code = PurchaseContractHelper.generateCode(seqPerfix);
            purchaseContract.setCode(code);
            purchaseContract.setGleSignFlag(gleSignFlag);

            save(purchaseContract);
            //获取标准条款信息
            purchaseContractStandardTermsSave(purchaseContractDTO, purchaseContract);
            purchaseContractMapper.updateByPrimaryKey(purchaseContract);
            //保存模组信息
            if (ListUtils.isNotEmpty(purchaseContractDTO.getProjectContractBudgetMaterials())) {
                List<ProjectContractBudgetMaterial> materialList = purchaseContractDTO.getProjectContractBudgetMaterials();
                materialList.stream().forEach(m -> {
                    m.setPurchaseContractId(purchaseContract.getId());
                });
                purchaseContractBudgetMaterialService.batchInsert(materialList, code);
            }
        } else {
            final PurchaseContract localPurchaseContract = findById(purchaseContract.getId());
            //如果合同已经全流程走完了,在提交之前
            if (Objects.nonNull(localPurchaseContract) && !Objects.equals(localPurchaseContract.getProjectId(), purchaseContractDTO.getProjectId())) {
                logger.info("草稿状态修改了关联的项目编号");
                ProjectContractBudgetMaterialExample materialExample = new ProjectContractBudgetMaterialExample();
                materialExample.createCriteria()
                        .andPurchaseContractIdEqualTo(localPurchaseContract.getId())
                        .andDeletedFlagEqualTo(false);
                List<ProjectContractBudgetMaterial> budgetMaterials = budgetMaterialMapper.selectByExample(materialExample);
                if (CollectionUtils.isNotEmpty(budgetMaterials)) {
                    List<Long> ids = budgetMaterials.stream()
                            .map(ProjectContractBudgetMaterial::getId).collect(Collectors.toList());
                    logger.info("获取这样的id集合为:{}", ids);
                    if (CollectionUtils.isNotEmpty(ids)) {
                        budgetMaterialExtMapper.updateBatchDeletedFlag(ids, Boolean.TRUE);
                    }
                }
            }
            if (localPurchaseContract != null) {
                localPurchaseContract.setTypeId(purchaseContractDTO.getTypeId());
                localPurchaseContract.setTypeName(purchaseContractDTO.getTypeName());
                localPurchaseContract.setProjectId(purchaseContractDTO.getProjectId());
                localPurchaseContract.setLegalAffairsCode(purchaseContractDTO.getLegalAffairsCode());
                localPurchaseContract.setName(purchaseContractDTO.getName());
                localPurchaseContract.setOuId(purchaseContractDTO.getOuId());
                localPurchaseContract.setVendorId(purchaseContractDTO.getVendorId());
                localPurchaseContract.setVendorName(purchaseContractDTO.getVendorName());
                localPurchaseContract.setVendorCode(purchaseContractDTO.getVendorCode());
                localPurchaseContract.setErpVendorSiteId(purchaseContractDTO.getErpVendorSiteId());
                localPurchaseContract.setVendorSiteCode(purchaseContractDTO.getVendorSiteCode());
                localPurchaseContract.setAmount(purchaseContractDTO.getAmount());
                localPurchaseContract.setStartTime(purchaseContractDTO.getStartTime());
                localPurchaseContract.setEndTime(purchaseContractDTO.getEndTime());
                localPurchaseContract.setAnnex(purchaseContractDTO.getAnnex());
                localPurchaseContract.setOriginalContractAnnex(purchaseContractDTO.getOriginalContractAnnex());
                localPurchaseContract.setManager(purchaseContractDTO.getManager());
                localPurchaseContract.setManagerName(purchaseContractDTO.getManagerName());
                localPurchaseContract.setPaymentMethod(purchaseContractDTO.getPaymentMethod());
                localPurchaseContract.setInvoiceType(purchaseContractDTO.getInvoiceType());
                localPurchaseContract.setPurchasingFollower(purchaseContractDTO.getPurchasingFollower());
                localPurchaseContract.setPurchasingFollowerName(purchaseContractDTO.getPurchasingFollowerName());
                localPurchaseContract.setRemark(purchaseContractDTO.getRemark());
                localPurchaseContract.setCarryoverFlag(purchaseContractDTO.getCarryoverFlag());
                localPurchaseContract.setTaxId(purchaseContractDTO.getTaxId());
                localPurchaseContract.setTaxRate(purchaseContract.getTaxRate());
                localPurchaseContract.setCurrency(purchaseContract.getCurrency());
                localPurchaseContract.setConversionDate(purchaseContract.getConversionDate());
                localPurchaseContract.setConversionRate(purchaseContract.getConversionRate());
                localPurchaseContract.setConversionType(purchaseContract.getConversionType());
                localPurchaseContract.setSealCategory(purchaseContractDTO.getSealCategory());
                localPurchaseContract.setSealAdminAccountIds(purchaseContractDTO.getSealAdminAccountIds());
                localPurchaseContract.setIfWatermarking(purchaseContractDTO.getIfWatermarking());
                localPurchaseContract.setNoWatermarkingReason(purchaseContractDTO.getNoWatermarkingReason());
                localPurchaseContract.setContractTerms(purchaseContractDTO.getContractTerms());
                localPurchaseContract.setDeliveryType(purchaseContractDTO.getDeliveryType());
                localPurchaseContract.setDeliveryClause(purchaseContractDTO.getDeliveryClause());
                localPurchaseContract.setDeliveryDate(purchaseContractDTO.getDeliveryDate());
                localPurchaseContract.setPaymentTerm(purchaseContractDTO.getPaymentTerm());
                localPurchaseContract.setIsElectronicContract(purchaseContractDTO.getIsElectronicContract());
                localPurchaseContract.setOtherEmail(purchaseContractDTO.getOtherEmail());
                localPurchaseContract.setOtherId(purchaseContractDTO.getOtherId());
                localPurchaseContract.setOtherName(purchaseContractDTO.getOtherName());
                localPurchaseContract.setOtherPhone(purchaseContractDTO.getOtherPhone());
                localPurchaseContract.setPublicOrPrivate(purchaseContractDTO.getPublicOrPrivate());
                localPurchaseContract.setBelongArea(purchaseContractDTO.getBelongArea());
                localPurchaseContract.setIsSynchronizeLegalSystemFlag(localPurchaseContract.getIsSynchronizeLegalSystemFlag());
                localPurchaseContract.setNotsyncType(localPurchaseContract.getNotsyncType());
                localPurchaseContract.setNotsyncReason(localPurchaseContract.getNotsyncReason());
                localPurchaseContract.setGleSignFlag(gleSignFlag);
                localPurchaseContract.setContractTermsFlg(purchaseContractDTO.getContractTermsFlg());
                purchaseContractStandardTermsSave(purchaseContractDTO,localPurchaseContract);
                update(localPurchaseContract);

                //为空判断
                if (ListUtils.isNotEmpty(purchaseContractDTO.getProjectContractBudgetMaterials())) {
                    List<ProjectContractBudgetMaterial> materialList = purchaseContractDTO.getProjectContractBudgetMaterials();
                    materialList.stream().forEach(m -> {
                        m.setPurchaseContractId(purchaseContract.getId());
                    });
                    purchaseContractBudgetMaterialService.updateBudgetMaterial(materialList);
                }
            }
        }
        return findDetailById(purchaseContract.getId());
    }

    private void purchaseContractStandardTermsSave(PurchaseContractDTO purchaseContractDTO, PurchaseContract purchaseContract) {
        //保存标准条款信息
        List<PurchaseContractStandardTermsDto> standardTermsDtoList = purchaseContractDTO.getStandardTermsDtoList();
        if (CollectionUtils.isNotEmpty(standardTermsDtoList)){
            List<Long> termsIds = new ArrayList<>();
            for (PurchaseContractStandardTermsDto standardTermsDto : standardTermsDtoList) {
                PurchaseContractStandardTerms purchaseContractStandardTerms = new PurchaseContractStandardTerms();
                purchaseContractStandardTerms.setPurchaseContractId(purchaseContract.getId());
                purchaseContractStandardTerms.setAssociationTermsId(standardTermsDto.getAssociationTermsId());
                purchaseContractStandardTerms.setTermsCode(standardTermsDto.getTermsCode());
                purchaseContractStandardTerms.setTermsName(standardTermsDto.getTermsName());
                purchaseContractStandardTerms.setTermsDisplayContent(standardTermsDto.getTermsDisplayContent());
                purchaseContractStandardTerms.setDeletedFlag(false);
                purchaseContractStandardTermsMapper.insert(purchaseContractStandardTerms);
                termsIds.add(purchaseContractStandardTerms.getId());

                List<PurchaseContractStandardTermsDeviation> standardTermsDeviationList = standardTermsDto.getStandardTermsDeviationList();
                if (CollectionUtils.isNotEmpty(standardTermsDeviationList)){
                    for (PurchaseContractStandardTermsDeviation purchaseContractStandardTermsDeviation : standardTermsDeviationList) {
                        PurchaseContractStandardTermsDeviation standardTermsDeviation = new PurchaseContractStandardTermsDeviation();
                        standardTermsDeviation.setAssociationPurchaseTermsId(purchaseContractStandardTerms.getId());
                        String deviationInfo = purchaseContractStandardTermsDeviation.getDeviationInfo();
                        if (StringUtils.isNotEmpty(deviationInfo)) {
                            if (deviationInfo.length() > 1024) {
                                throw new ApplicationBizException(
                                        String.format("偏离项信息长度不能超过1024，当前长度：%d", deviationInfo.length())
                                );
                            }
                            standardTermsDeviation.setDeviationInfo(deviationInfo);
                        }
                        standardTermsDeviation.setDeletedFlag(false);
                        purchaseContractStandardTermsDeviationMapper.insert(standardTermsDeviation);
                    }
                }
            }
            String contractTermsIds = org.apache.commons.lang3.StringUtils.join(termsIds, ",");
            purchaseContract.setContractTermsIds(contractTermsIds);
        }
    }

    /**
     * 检查同一个单位下合同名称不能相同
     *
     * @param contractName
     * @param contractId
     */
    public PurchaseContractNameDuplicateVo checkAndFetchPurchaseContracts(String contractName, Long contractId) {

        List<Long> ouList = getCurrentUserOuList();

        PurchaseContractExample purchaseContractExample = new PurchaseContractExample();
        PurchaseContractExample.Criteria criteria = purchaseContractExample.createCriteria();
        criteria.andOuIdIn(ouList);
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andNameEqualTo(contractName);
        criteria.andStatusNotIn(Collections.singletonList(PurchaseContractStatus.CANCEL.getCode()));
        if (Objects.nonNull(contractId)) {
            criteria.andIdNotEqualTo(contractId);
        }
        List<PurchaseContract> purchaseContracts = purchaseContractMapper.selectByExample(purchaseContractExample);
        PurchaseContractNameDuplicateVo purchaseContractNameDuplicateVo = new PurchaseContractNameDuplicateVo();
        if (ListUtils.isNotEmpty(purchaseContracts)) {
            purchaseContractNameDuplicateVo.setIsDuplicate(Boolean.TRUE);
            List<String> contractNameList = purchaseContracts.stream().map(PurchaseContract::getName).collect(Collectors.toList());
            List<Long> contractIdList = purchaseContracts.stream().map(PurchaseContract::getId).collect(Collectors.toList());
            purchaseContractNameDuplicateVo.setDuplicateContractNames(contractNameList);
            purchaseContractNameDuplicateVo.setDuplicateContractIds(contractIdList);
        }

        return purchaseContractNameDuplicateVo;
    }

    private List<Long> getCurrentUserOuList() {
        // 获取当前单位ID
        Long unitId = SystemContext.getUnitId();
        // 获取当前用户OU列表
        List<Long> ouList = new ArrayList<>(CacheDataUtils.getOuIdListByUnitId(unitId));
        // 为空，传入查询异常
        ouList.add(NOT_EXIST_ID);
        return ouList;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseContractVO makePurchaseDetail(PurchaseContractDTO purchaseContractDTO) {
        purchaseDetailParamCheck(purchaseContractDTO);
        return makePurchaseDetailTemporary(purchaseContractDTO);
    }

    @Override
    public boolean uploadPurchaseDetail(List<PurchaseContractDetailDTO> purchaseContractDetailDTOList) {
        boolean finalResult = true;
        for (PurchaseContractDetailDTO detailDTO : purchaseContractDetailDTOList) {
            boolean result = true;
            StringBuilder sb = new StringBuilder();
            if (StringUtils.isEmpty(detailDTO.getName())) {
                result = false;
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append("名称不能为空");
            }
            if (StringUtils.isEmpty(detailDTO.getModel())) {
                result = false;
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append("规格型号不能为空");
            }
            String taxRate = detailDTO.getTaxRate();
            if (StringUtils.isEmpty(taxRate)) {
                result = false;
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append("税率不能为空");
            } else {
                // 对税率校验 taxRate传进来是0.03等小数
                if (taxRate.split("\\.").length > 0) {
                    try {
                        NumberFormat percentInstance = NumberFormat.getPercentInstance();
                        percentInstance.setMaximumFractionDigits(2); // 保留小数两位
                        taxRate = percentInstance.format(new BigDecimal(taxRate)); // 结果是81.25% ，最后一们四舍五入了
                    } catch (Exception e) {
                        result = false;
                        if (sb.length() > 0) {
                            sb.append(",");
                        }
                        sb.append("税率格式不正确");
                    }
                }
                String taxList = basedataTaxListFeignClient.getTaxList(null, taxRate, "1", null, 1, 100);
                JSONObject jsonParam = JSONObject.parseObject(taxList);
                String list = jsonParam.getString("list");
                logger.info(String.format("uploadPurchaseDetail的taxRate:%s", list));
                List<TaxInfoDto> taxLists = JSON.parseObject(list, new TypeReference<List<TaxInfoDto>>() {
                });
                if (ListUtils.isNotEmpty(taxLists)) {
                    TaxInfoDto taxInfoDto = taxLists.get(0);
                    detailDTO.setTaxId(taxInfoDto.getId());
                    detailDTO.setTaxRate(taxInfoDto.getTaxRate());
                } else {
                    result = false;
                    sb.append("税率不存在");
                }
            }
//            String unitPriceStr = detailDTO.getUnitPriceStr();
//            if (BigDecimalUtils.isBigDecimal(unitPriceStr)) {
//                BigDecimal unitPrice = new BigDecimal(unitPriceStr);
//                if (Objects.isNull(unitPrice)) {
//                    result = false;
//                    if (sb.length() > 0) {
//                        sb.append(",");
//                    }
//                    sb.append("单价（含税）不能为空");
//                } else {
//                    if (unitPrice.scale() > 2) {
//                        result = false;
//                        if (sb.length() > 0) {
//                            sb.append(",");
//                        }
//                        sb.append("单价（含税）最多只能 2 位小数");
//                    }
//                }
//                detailDTO.setUnitPrice(unitPrice);
//            } else {
//                result = false;
//                logger.error("单价（含税）的值为:{}", detailDTO.getUnitPrice());
//                sb.append("单价（含税）有误，导入时注意清除公式");
//            }

            String unitName = detailDTO.getUnitName();
            if (StringUtils.isEmpty(unitName)) {
                result = false;
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append("单位不能为空");
            } else {
                List<DictDto> dictDtoList = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), unitName, null);
                if (CollectionUtils.isEmpty(dictDtoList)) {
                    result = false;
                    if (sb.length() > 0) {
                        sb.append(",");
                    }
                    sb.append("单位只能填写系统已有单位");
                } else {
                    DictDto dictDto = dictDtoList.get(0);
                    detailDTO.setUnitId(dictDto.getId());
                    detailDTO.setUnitCode(dictDto.getCode());
                }
            }
            String numberStr = detailDTO.getNumberStr();
            logger.info("数量的输入值为:{}", numberStr);
            if (StringUtils.isEmpty(numberStr)) {
                result = false;
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append("数量不能为空");
            } else {
                if (isNum(numberStr)) {
                    BigDecimal num = new BigDecimal(numberStr);
                    if (num.compareTo(BigDecimal.ZERO) >= 0) {
                        detailDTO.setNumber(num);
                    } else {
                        result = false;
                        if (sb.length() > 0) {
                            sb.append(",");
                        }
                        sb.append("数量要大于或等于0");
                    }

                } else {
                    result = false;
                    if (sb.length() > 0) {
                        sb.append(",");
                    }
                    sb.append("数量只能为数值类型");
                }
            }

            //金额（含税）和金额（不含税）选择一个填写即可，不允许都填写
            String totalPriceStr = detailDTO.getTotalPriceStr();
            String noTaxTotalPriceStr = detailDTO.getNoTaxTotalPriceStr();
            if (StringUtils.isNotEmpty(totalPriceStr) && StringUtils.isEmpty(noTaxTotalPriceStr)) {
                logger.info("金额（含税）不为空,金额（不含税）为空");
                if (BigDecimalUtils.isBigDecimal(totalPriceStr)) {
                    BigDecimal totalPrice = new BigDecimal(totalPriceStr);
                    if (Objects.isNull(totalPrice)) {
                        result = false;
                        if (sb.length() > 0) {
                            sb.append(",");
                        }
                        sb.append("金额（含税）不能为空");
                    } else {
                        if (totalPrice.scale() > 2) {
                            result = false;
                            if (sb.length() > 0) {
                                sb.append(",");
                            }
                            sb.append("金额（含税）最多只能 2 位小数");
                        }
                    }

                    if (result) { //前面的参数校验过了,才会计算
                        //这里需要计算出单价含税 = 金额(含税)/数量
                        BigDecimal number = detailDTO.getNumber();//数量
                        BigDecimal unitPrice = BigDecimal.ZERO.setScale(2);
                        if (number.compareTo(BigDecimal.ZERO) > 0) {
                            unitPrice = totalPrice.divide(number, 6, BigDecimal.ROUND_HALF_UP);
                        }
                        //金额(不含税) = 金额(含税)/(1+税率)
                        String taxRate1 = detailDTO.getTaxRate();
                        //将税率由百分数转换为小数
                        BigDecimal rate = new BigDecimal(taxRate1.replace("%", "")).divide(new BigDecimal(100)).setScale(4);
                        BigDecimal noTaxTotalPrice = totalPrice.divide((BigDecimal.ONE.add(rate)), 2, BigDecimal.ROUND_HALF_UP);
                        detailDTO.setUnitPrice(unitPrice);
                        detailDTO.setNoTaxTotalPrice(noTaxTotalPrice);
                        logger.info("物料的单价()含税为:{},金额(含税)为:{},金额(不含税)为:{}", unitPrice, totalPrice, noTaxTotalPrice);

                    }

                    detailDTO.setTotalPrice(totalPrice);

                } else {
                    result = false;
                    logger.error("金额（含税）的值为:{}", totalPriceStr);
                    sb.append("金额（含税）有误，导入时注意清除公式");
                }

            } else if (StringUtils.isEmpty(totalPriceStr) && StringUtils.isNotEmpty(noTaxTotalPriceStr)) {
                logger.info("金额（含税）为空,金额（不含税）不为空");
                if (BigDecimalUtils.isBigDecimal(noTaxTotalPriceStr)) {
                    BigDecimal noTaxTotalPrice = new BigDecimal(noTaxTotalPriceStr);
                    if (Objects.isNull(noTaxTotalPrice)) {
                        result = false;
                        if (sb.length() > 0) {
                            sb.append(",");
                        }
                        sb.append("金额（不含税）不能为空");
                    } else {
                        if (noTaxTotalPrice.scale() > 2) {
                            result = false;
                            if (sb.length() > 0) {
                                sb.append(",");
                            }
                            sb.append("金额（不含税）最多只能 2 位小数");
                        }
                    }

                    //这里需要判断前面校验的值是否已经出现异常
                    if (result) {
                        //这里需要计算出金额含税
                        String taxRate1 = detailDTO.getTaxRate();
                        //将税率由百分数转换为小数
                        BigDecimal rate = new BigDecimal(taxRate1.replace("%", ""))
                                .divide(new BigDecimal(100), 6, BigDecimal.ROUND_HALF_UP);
                        BigDecimal totalPrice = noTaxTotalPrice.multiply(BigDecimal.ONE.add(rate)).setScale(2, BigDecimal.ROUND_HALF_UP);
                        //单价(含税)
                        BigDecimal number = detailDTO.getNumber();//数量
                        logger.info("数量为:{}", number);
                        BigDecimal unitPrice = BigDecimal.ZERO;
                        if (number.compareTo(BigDecimal.ZERO) > 0) { //数量填写的是0
                            unitPrice = noTaxTotalPrice.multiply(BigDecimal.ONE.add(rate))
                                    .divide(number, 6, BigDecimal.ROUND_HALF_UP);
                        }
                        detailDTO.setUnitPrice(unitPrice);
                        detailDTO.setTotalPrice(totalPrice);
                        logger.info("物料的单价()含税为:{},金额(含税)为:{},金额(不含税)为:{}", unitPrice, totalPrice, noTaxTotalPrice);
                    }
                    detailDTO.setNoTaxTotalPrice(noTaxTotalPrice);
                } else {
                    result = false;
                    logger.error("金额（不含税）的值为:{}", noTaxTotalPriceStr);
                    sb.append("金额（不含税）有误，导入时注意清除公式");
                }

            } else if (StringUtils.isNotEmpty(totalPriceStr) && StringUtils.isNotEmpty(noTaxTotalPriceStr)) {
                logger.info("金额（含税）:{}不为空,金额（不含税）:{}不为空", totalPriceStr, noTaxTotalPriceStr);
                result = false;
                sb.append("金额（含税）和金额（不含税）选择一个填写即可");

            } else {
                logger.info("金额（含税）为空,金额（不含税）为空");
                result = false;
                sb.append("金额（含税）和金额（不含税）不能都为空");
            }

            // 模板验证不通过
            if (!result) {
                detailDTO.setValidResult(sb.toString());
                // 只要有一行数据校验不成功就不能导入
                finalResult = false;
            }
        }

        return finalResult;
    }

    private boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    private boolean isNum(String str) {
        Pattern pattern = Pattern.compile("^(-?\\d+)(\\.\\d+)?$");
        return pattern.matcher(str).matches();
    }

    private void purchaseDetailParamCheck(PurchaseContractDTO purchaseContractDTO) {
        final List<PurchaseContractDetailDTO> purchaseContractDetails = purchaseContractDTO.getPurchaseContractDetails();
        Assert.notEmpty(purchaseContractDetails, "采购内容不能为空");

        purchaseContractDetails.forEach(purchaseContractDetail -> {
            Assert.notNull(purchaseContractDetail.getName(), "名称不能为空");
            Assert.notNull(purchaseContractDetail.getModel(), "规格型号不能为空");
            Assert.notNull(purchaseContractDetail.getTaxId(), "税率不能为空");
            Assert.notNull(purchaseContractDetail.getUnitPrice(), "单价不能为空");
            Assert.notNull(purchaseContractDetail.getUnitCode(), "单位不能为空");
            Assert.notNull(purchaseContractDetail.getNumber(), "数量不能为空");
            //当名称和规格型号不为空时,但此时字段长度超过了数据库允许的最大长度时,会添加数据库失败,报错
            //合同内容中名称、型号、品牌最大长度支持256
            if (StringUtils.isNotEmpty(purchaseContractDetail.getName())) {
                Assert.isTrue(purchaseContractDetail.getName().length() <= 256, "合同内容中名称最大长度不能超过256");
            }
            if (StringUtils.isNotEmpty(purchaseContractDetail.getModel())) {
                Assert.isTrue(purchaseContractDetail.getModel().length() <= 256, "合同内容中规格型号最大长度不能超过256");
            }
            if (StringUtils.isNotEmpty(purchaseContractDetail.getBrand())) {
                Assert.isTrue(purchaseContractDetail.getBrand().length() <= 256, "合同内容中品牌最大长度不能超过256");
            }
        });

        if (null != purchaseContractDTO.getProjectId()) {
            Project project = projectMapper.selectByPrimaryKey(purchaseContractDTO.getProjectId());
            if (Boolean.TRUE.equals(project.getWbsEnabled()) && CollectionUtils.isEmpty(purchaseContractDTO.getPurchaseContractWbsBudgets())) {
                throw new ApplicationBizException("关联预算不能为空");
            }
        }

        BigDecimal totalAmount = BigDecimal.ZERO;
        for (PurchaseContractDetailDTO purchaseContractDetailDTO : purchaseContractDetails) {
            final BigDecimal totalPrice = purchaseContractDetailDTO.getTotalPrice();
            totalAmount = totalAmount.add(totalPrice == null ? BigDecimal.ZERO : totalPrice);
        }

        final Long id = purchaseContractDTO.getId();
        final PurchaseContract localPurchaseContract = findById(id);

        if (localPurchaseContract == null) {
            throw new MipException("采购合同不存在");
        }
        // 校验总金额
        if (totalAmount.compareTo(localPurchaseContract.getAmount()) != 0) {
            throw new MipException("采购拆分金额与总金额不一致");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseContractVO makePurchaseDetailTemporary(PurchaseContractDTO purchaseContractDTO) {
        final Long contractId = purchaseContractDTO.getId();
        String lockName = "PurchaseContractServiceImpl.makePurchaseDetailTemporary";
        String value = "makePurchaseDetailTemporary";
        try {
            if (DistributedCASLock.lock(lockName, value, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(contractId);
                Guard.notNull(contract, "合同不存在");

                Project project = projectBusinessService.selectByPrimaryKey(contract.getProjectId());
                checkPurchaseContractBudget(purchaseContractDTO, project, contract);
                List<PurchaseContractDetailDTO> purchaseContractDetails = purchaseContractDTO.getPurchaseContractDetails();
                if (CollectionUtils.isNotEmpty(purchaseContractDetails)) {
                    List<PurchaseContractDetail> purchaseContractDetailList = new ArrayList<>();
                    BigDecimal[] excludingTaxAmount = {BigDecimal.ZERO};
                    for (PurchaseContractDetailDTO purchaseContractDetailDTO : purchaseContractDetails) {
                        //合同内容中名称、型号、品牌最大长度支持256
                        if (StringUtils.isNotEmpty(purchaseContractDetailDTO.getName())) {
                            Assert.isTrue(purchaseContractDetailDTO.getName().length() <= 256, "合同内容中名称最大长度不能超过256");
                        }
                        if (StringUtils.isNotEmpty(purchaseContractDetailDTO.getModel())) {
                            Assert.isTrue(purchaseContractDetailDTO.getModel().length() <= 256, "合同内容中规格型号最大长度不能超过256");
                        }
                        if (StringUtils.isNotEmpty(purchaseContractDetailDTO.getBrand())) {
                            Assert.isTrue(purchaseContractDetailDTO.getBrand().length() <= 256, "合同内容中品牌最大长度不能超过256");
                        }
                        PurchaseContractDetail purchaseContractDetail = new PurchaseContractDetail();
                        BeanConverter.copy(purchaseContractDetailDTO, purchaseContractDetail);
                        final Long detailId = purchaseContractDetail.getId();
                        if (detailId == null) {
                            purchaseContractDetail.setContractId(contractId);
                            purchaseContractDetail.setDeletedFlag(Boolean.FALSE);
                            purchaseContractDetail.setId(null);
                            purchaseContractDetailList.add(purchaseContractDetail);
                        } else {
                            final PurchaseContractDetail localPurchaseContractDetail = purchaseContractDetailService.findById(detailId);
                            if (localPurchaseContractDetail != null) {
                                localPurchaseContractDetail.setBrand(purchaseContractDetailDTO.getBrand());
                                localPurchaseContractDetail.setModel(purchaseContractDetailDTO.getModel());
                                localPurchaseContractDetail.setName(purchaseContractDetailDTO.getName());
                                localPurchaseContractDetail.setTaxId(purchaseContractDetailDTO.getTaxId());
                                localPurchaseContractDetail.setTaxRate(purchaseContractDetailDTO.getTaxRate());
                                localPurchaseContractDetail.setTotalPrice(purchaseContractDetailDTO.getTotalPrice());
                                localPurchaseContractDetail.setUnitCode(purchaseContractDetailDTO.getUnitCode());
                                localPurchaseContractDetail.setUnitName(purchaseContractDetailDTO.getUnitName());
                                localPurchaseContractDetail.setNumber(purchaseContractDetailDTO.getNumber());
                                localPurchaseContractDetail.setTotalPrice(purchaseContractDetailDTO.getTotalPrice());
                                localPurchaseContractDetail.setUnitPrice(purchaseContractDetailDTO.getUnitPrice());
                                localPurchaseContractDetail.setNoTaxTotalPrice(purchaseContractDetailDTO.getNoTaxTotalPrice());
                                localPurchaseContractDetail.setRemark(purchaseContractDetailDTO.getRemark());
                                localPurchaseContractDetail.setDeletedFlag(Boolean.FALSE);
                                localPurchaseContractDetail.setId(null);
                                purchaseContractDetailList.add(localPurchaseContractDetail);
                            }
                        }

                        excludingTaxAmount[0] = excludingTaxAmount[0].add(purchaseContractDetail.getNoTaxTotalPrice() == null ? BigDecimal.ZERO :
                                purchaseContractDetail.getNoTaxTotalPrice());
                    }
                    // 保存采购内容
                    if (CollectionUtils.isNotEmpty(purchaseContractDetailList)) {
                        purchaseContractDetailService.deleteByContractId(contractId);
                        purchaseContractDetailService.batchInsert(purchaseContractDetailList);
                    }

                    // 更新不含税金额
                    PurchaseContract purchaseContract = new PurchaseContract();
                    purchaseContract.setId(contractId);
                    if (excludingTaxAmount[0].compareTo(BigDecimal.ZERO) != 0) {
                        purchaseContract.setExcludingTaxAmount(excludingTaxAmount[0]);
                        purchaseContractMapper.updateByPrimaryKeySelective(purchaseContract);
                    }
                }

                //查询一下采购合同的关联项目是否修改了,如果项目修改了,那么需要删除已经写入数据库中的关联预算信息
                ProjectContractBudgetMaterialExample materialExample = new ProjectContractBudgetMaterialExample();
                materialExample.createCriteria()
                        .andPurchaseContractIdEqualTo(contract.getId())
                        .andDeletedFlagEqualTo(false);
                List<ProjectContractBudgetMaterial> budgetMaterials = budgetMaterialMapper.selectByExample(materialExample);
                if (CollectionUtils.isNotEmpty(budgetMaterials)) {
                    List<Long> ids = budgetMaterials.stream()
                            .filter(x -> !Objects.equals(x.getProjectId(), contract.getProjectId())) //获取那些与当前关联项目不匹配的数据
                            .map(ProjectContractBudgetMaterial::getId).collect(Collectors.toList());
                    logger.info("获取这样的id集合为:{}", ids);
                    if (CollectionUtils.isNotEmpty(ids)) {
                        budgetMaterialExtMapper.updateBatchDeletedFlag(ids, Boolean.TRUE);
                    }
                }
                // 保存模组信息
                List<ProjectContractBudgetMaterial> projectContractBudgetMaterials = purchaseContractDTO.getProjectContractBudgetMaterials();
                if (CollectionUtils.isNotEmpty(projectContractBudgetMaterials)) {
                    /**这个删除只能删除当前项目下的其他模组信息*/
                    budgetMaterialExtMapper.deleteByProjectIdAndCode(purchaseContractDTO.getProjectId(), purchaseContractDTO.getCode());
                    for (ProjectContractBudgetMaterial projectContractBudgetMaterial : projectContractBudgetMaterials) {
                        ProjectContractBudgetMaterialExample example = new ProjectContractBudgetMaterialExample();
                        ProjectContractBudgetMaterialExample.Criteria criteria = example.createCriteria();
                        criteria.andCodeEqualTo(contract.getCode()).andDeletedFlagEqualTo(false)
                                .andProjectIdEqualTo(projectContractBudgetMaterial.getProjectId())
                                .andStatusEqualTo(false).andMaterialIdEqualTo(projectContractBudgetMaterial.getMaterialId());
                        List<ProjectContractBudgetMaterial> bm = budgetMaterialMapper.selectByExample(example);
                        if (CollectionUtils.isNotEmpty(bm)) {
                            projectContractBudgetMaterial.setId(bm.get(0).getId());
                            budgetMaterialMapper.updateByPrimaryKeySelective(projectContractBudgetMaterial);
                        } else {
                            projectContractBudgetMaterial.setPurchaseContractId(contractId);
                            projectContractBudgetMaterial.setCode(contract.getCode());
                            projectContractBudgetMaterial.setStatus(false);
                            projectContractBudgetMaterial.setDeletedFlag(false);
                            budgetMaterialMapper.insert(projectContractBudgetMaterial);
                        }
                    }
                }

                // 保存wbs关联预算
                List<PurchaseContractBudgetDto> purchaseContractWbsBudgets = purchaseContractDTO.getPurchaseContractWbsBudgets();
                if (CollectionUtils.isNotEmpty(purchaseContractWbsBudgets)) {
                    checkFreeze(purchaseContractWbsBudgets);
                    List<Long> requirementIdList =
                            purchaseContractWbsBudgets.stream().map(PurchaseContractBudgetDto::getPurchaseRequirementId).distinct().collect(Collectors.toList());
                    // 获取本合同以外的预算占用数量
                    List<PurchaseMaterialRequirementDto> requirementDtoList = purchaseContractBudgetExtMapper.countReleasedQuantity(contractId,
                            requirementIdList);
                    Map<Long, PurchaseMaterialRequirementDto> requirementDtoMap = requirementDtoList.stream()
                            .collect(Collectors.toMap(PurchaseMaterialRequirementDto::getId, Function.identity(), (key1, key2) -> key2));
                    // 查询关联项目需求的最新发布日期
                    Map<Long, Date> publishTimeMap = purchaseMaterialRequirementExtMapper.selectLatestPublishTime(requirementIdList).stream()
                            .collect(Collectors.toMap(PurchaseMaterialReleaseDetailDto::getPurchaseRequirementId,
                                    PurchaseMaterialReleaseDetailDto::getPublishTime));

                    List<PurchaseContractBudget> purchaseContractBudgetList = new ArrayList<>();
                    for (PurchaseContractBudgetDto budgetDto : purchaseContractWbsBudgets) {
                        PurchaseMaterialRequirementDto requirementDto = requirementDtoMap.get(budgetDto.getPurchaseRequirementId());
                        Assert.isTrue(requirementDto != null, "外包物料需求不存在!");
                        // 判断是否超未签订采购合同数量
                        if (!Objects.equals(Boolean.TRUE, budgetDto.getDeletedFlag())
                                && budgetDto.getNumber().compareTo(requirementDto.getUnreleasedAmount()) > 0) {
                            throw new MipException(budgetDto.getPamCode() + "签订数量不能大于未签订采购合同数量");
                        }
                        // 活动事项校验
                        if (!StringUtils.isEmpty(budgetDto.getActivityCode()) && !StringUtils.isEmpty(budgetDto.getWbsSummaryCode())) {
                            // 先判断活动事项以及活动事项的上级（活动事项的上下级关系在“项目活动事项维护”中设置）存不存在，上级存在也算存在
                            String wbsBudgetCode = budgetDto.getWbsSummaryCode();
                            // 截取wbscode,去除项目编号
                            wbsBudgetCode = wbsBudgetCode.substring(project.getCode().length() + 1, wbsBudgetCode.length());
                            ProjectWbsBudget projectWbsBudget =
                                    projectWbsBudgetService.existWbsBudgetByActivity(project.getId(), wbsBudgetCode, budgetDto.getActivityCode());
                            if (projectWbsBudget == null) {
                                // 查询配置了预算事项的活动事项或上级(判断这个活动事项是不是预算事项)
                                String budgetActivityCode =
                                        projectWbsBudgetService.checkWbsBudgetByActivity(SystemContext.getUnitId(), budgetDto.getActivityCode());
                                Assert.isTrue(StringUtils.isNotEmpty(budgetActivityCode),
                                        "WBS[" + wbsBudgetCode + "]+活动事项[" + budgetDto.getActivityCode() + "]的预算不存在，" +
                                                "且无法生成对应的项目预算（该活动事项及上级都为非预算事项）");
                                // 填报的WBS+活动事项（通过角色绑定）在这个项目的预算中不存在，则在预算中自动生成该条预算，预算金额为0
                                projectWbsBudgetService.addWbsBudgetByActivity(project.getId(), wbsBudgetCode, budgetActivityCode);
                            }
                        }

                        PurchaseContractBudget purchaseContractBudget = BeanConverter.copy(budgetDto, PurchaseContractBudget.class);
                        purchaseContractBudget.setPurchaseContractId(contractId);
                        purchaseContractBudget.setPublishTime(publishTimeMap.get(purchaseContractBudget.getPurchaseRequirementId()));
                        purchaseContractBudget.setPurchaseContractCreateAt(contract.getCreateAt());
                        // 修改（包含删除）
                        if (null != purchaseContractBudget.getId()) {
                            purchaseContractBudget.setId(null);
                            purchaseContractBudgetList.add(purchaseContractBudget);
                            // 如果是删除,恢复采购需求状态
                            if (Objects.equals(Boolean.TRUE, purchaseContractBudget.getDeletedFlag())) {
                                PurchaseMaterialRequirement requirement = new PurchaseMaterialRequirement();
                                requirement.setId(purchaseContractBudget.getPurchaseRequirementId());
                                if (requirementDto.getUnreleasedAmount().compareTo(BigDecimal.ZERO) == 0) {
                                    requirement.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
                                } else {
                                    requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
                                }
                                purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(requirement);
                            }
                        } else {
                            purchaseContractBudget.setDeletedFlag(false);
                            purchaseContractBudget.setVersion(1L);
                            purchaseContractBudget.setId(null);
                            purchaseContractBudgetList.add(purchaseContractBudget);
                        }
                    }

                    // 保存采购合同预算
                    if (CollectionUtils.isNotEmpty(purchaseContractBudgetList)) {
                        purchaseContractBudgetService.deleteByPurchaseContractId(contractId);

                        purchaseContractBudgetService.batchInsert(purchaseContractBudgetList);
                    }
                }

                //查询合同新增时关联的采购物料，如果已采购量=总需求量(即未采购量=0)，采购需求状态改为：已关闭；否则改为：待采购
                purchaseContractBudgetService.updateRequirementStatus(contractId);

                return findDetailById(contractId);
            }
        } catch (Exception e) {
            logger.error("makePurchaseDetailTemporary加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, value);
        }
        return null;
    }

    private void checkFreeze(List<PurchaseContractBudgetDto> purchaseContractWbsBudgets) {
        List<String> freezeErrorMessages = new ArrayList<>();
        for (PurchaseContractBudgetDto purchaseContractWbsBudget : purchaseContractWbsBudgets) {
            Long purchaseRequirementId = purchaseContractWbsBudget.getPurchaseRequirementId();
            PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(purchaseRequirementId);
            if (requirement != null) {
                if (requirement.getFreezeFlag().equals(FreezeFlag.FREEZE.getCode())) {
                    freezeErrorMessages.add(String.format("[%s]的[%s]冻结状态为已冻结,不可进行暂存", requirement.getRequirementCode(), requirement.getPamCode()));
                }
            }
        }
        if (!freezeErrorMessages.isEmpty()) {
            throw new BizException(Code.ERROR, String.join(";", freezeErrorMessages));
        }
    }

    private void checkPurchaseContractBudget(PurchaseContractDTO purchaseContractDTO, Project project, PurchaseContract contract) {
        List<PurchaseContractBudgetDto> purchaseContractWbsBudgets = purchaseContractDTO.getPurchaseContractWbsBudgets();
        if (ListUtils.isNotEmpty(purchaseContractWbsBudgets)) {
            List<Long> purchaseRequirementIds = purchaseContractWbsBudgets.stream()
                    .filter(e -> !Objects.equals(e.getDeletedFlag(), Boolean.TRUE))
                    .map(PurchaseContractBudgetDto::getPurchaseRequirementId).collect(Collectors.toList());
            if (!purchaseRequirementIds.isEmpty()) {
                PurchaseMaterialRequirementExample requirementExample = new PurchaseMaterialRequirementExample();
                requirementExample.createCriteria().andIdIn(purchaseRequirementIds);
                List<PurchaseMaterialRequirement> requirementList = purchaseMaterialRequirementMapper.selectByExample(requirementExample);
                List<String> pamCodes = requirementList.stream()
                        .filter(e -> !e.getProjectId().equals(project.getId()))
                        .map(PurchaseMaterialRequirement::getPamCode).collect(Collectors.toList());
                if (!pamCodes.isEmpty()) {
                    throw new BizException(Code.ERROR, String.format("PAM编码为【%s】的项目需求不属于项目【%S】",
                            String.join("，", pamCodes), project.getName()));
                }
                if ("点工采购".equals(contract.getTypeName())) {
                    pamCodes = requirementList.stream().filter(e -> e.getRequirementType() != null && e.getRequirementType() != 2)
                            .map(PurchaseMaterialRequirement::getPamCode).collect(Collectors.toList());
                    if (!pamCodes.isEmpty()) {
                        throw new BizException(Code.ERROR, String.format("PAM编码为【%s】的项目需求不属于【点工采购】需求",
                                String.join("，", pamCodes)));
                    }
                } else {
                    pamCodes = requirementList.stream().filter(e -> e.getRequirementType() != null && e.getRequirementType() == 2)
                            .map(PurchaseMaterialRequirement::getPamCode).collect(Collectors.toList());
                    if (!pamCodes.isEmpty()) {
                        throw new BizException(Code.ERROR, String.format("PAM编码为【%s】的项目需求不属于【服务+物料】需求",
                                String.join("，", pamCodes)));
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseContractVO makePaymentPlan(PurchaseContractDTO purchaseContractDTO) {
        paymentPlanParamCheck(purchaseContractDTO);
        return makePaymentPlanTemporary(purchaseContractDTO);
    }

    private void paymentPlanParamCheck(PurchaseContractDTO purchaseContractDTO) {
        final List<PaymentPlanDTO> paymentPlans = purchaseContractDTO.getPaymentPlans();
        if (ListUtils.isNotEmpty(paymentPlans)) {
            paymentPlans.forEach(paymentPlanDTO -> {
                Assert.notNull(paymentPlanDTO.getProportion(), "计划付款比例不能为空");
                Assert.notNull(paymentPlanDTO.getAmount(), "计划付款金额不能为空");
                Assert.notNull(paymentPlanDTO.getDate(), "计划付款日期不能为空");
                Assert.notNull(paymentPlanDTO.getPaymentMethodId(), "付款方式不能为空");
                if (StringUtils.isNotEmpty(paymentPlanDTO.getRequirement())
                        && paymentPlanDTO.getRequirement().length() > 100) {
                    throw new MipException("付款条件不能超过100字符");
                }
            });

            BigDecimal totalAmount = BigDecimal.ZERO;
            for (PaymentPlanDTO paymentPlanDTO : paymentPlans) {
                final BigDecimal amount = paymentPlanDTO.getAmount();
                totalAmount = totalAmount.add(amount == null ? BigDecimal.ZERO : amount);
            }

            final Long id = purchaseContractDTO.getId();
            final PurchaseContract localPurchaseContract = findById(id);

            if (localPurchaseContract == null) {
                throw new MipException("采购合同不存在");
            }
            if (Objects.equals(localPurchaseContract.getStatus(), PurchaseContractStatus.CANCEL.getCode())) {
                throw new MipException("合同已作废，无法提交审批");
            }
            // 校验总金额
            if (totalAmount.compareTo(localPurchaseContract.getAmount()) != 0) {
                throw new MipException("采购拆分金额与总金额不一致");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseContractVO makePaymentPlanTemporary(PurchaseContractDTO purchaseContractDTO) {
        final Long contractId = purchaseContractDTO.getId();
        final List<PaymentPlanDTO> paymentPlans = purchaseContractDTO.getPaymentPlans();
        List<PaymentPlan> insertPaymentPlanList = new ArrayList<>();
        List<PaymentPlan> updatePaymentPlanList = new ArrayList<>();

        String lockName = "PurchaseContractServiceImpl.makePaymentPlanTemporary";
        String value = "makePaymentPlanTemporary";
        try {
            if (DistributedCASLock.lock(lockName, value, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {

                final List<PaymentPlan> localPaymentPlans = paymentPlanService.findByContractId(contractId);
                Set<Long> detailIdSet = localPaymentPlans.stream().map(PaymentPlan::getId).collect(Collectors.toSet());
                Map<Long, PaymentPlan> localPaymentMap = localPaymentPlans.stream().collect(Collectors.toMap(PaymentPlan::getId, Function.identity()));
                if (ListUtils.isNotEmpty(paymentPlans)) {
                    for (int i = 0; i < paymentPlans.size(); i++) {
                        PaymentPlanDTO paymentPlanDTO = paymentPlans.get(i);
                        final Long id = paymentPlanDTO.getId();

                        PaymentPlan paymentPlan = new PaymentPlan();
                        BeanConverter.copy(paymentPlanDTO, paymentPlan);

                        // 设置笔数
                        paymentPlan.setNum(i + 1);
                        if (id == null) {
                            paymentPlan.setDeletedFlag(Boolean.FALSE);
                            paymentPlan.setContractId(contractId);
                            logger.info("付款计划保存或暂存时:{}", JSON.toJSONString(paymentPlan));
                            insertPaymentPlanList.add(paymentPlan);
                        } else {
                            PaymentPlan localPaymentPlan = localPaymentMap.get(id);
                            if (localPaymentPlan != null) {
                                localPaymentPlan.setProportion(paymentPlanDTO.getProportion());
                                localPaymentPlan.setAmount(paymentPlanDTO.getAmount());
                                localPaymentPlan.setDate(paymentPlanDTO.getDate());
                                localPaymentPlan.setRequirement(paymentPlanDTO.getRequirement());
                                localPaymentPlan.setPaymentMethodId(paymentPlanDTO.getPaymentMethodId());
                                localPaymentPlan.setPaymentMethodName(paymentPlanDTO.getPaymentMethodName());
                                localPaymentPlan.setAdvancePaymentFlag(paymentPlanDTO.getAdvancePaymentFlag());
                                localPaymentPlan.setMilestoneId(paymentPlanDTO.getMilestoneId());
                                localPaymentPlan.setNum(paymentPlan.getNum());
                                localPaymentPlan.setPrepaymentFlag(paymentPlanDTO.getPrepaymentFlag());
                                updatePaymentPlanList.add(localPaymentPlan);
                            }

                            detailIdSet.remove(id);
                        }
                    }
                }

                //批量删除
                if (detailIdSet.size() > 0) {
                    detailIdSet.forEach(id -> {
                        PaymentPlan paymentPlan = new PaymentPlan();
                        paymentPlan.setId(id);
                        paymentPlan.setDeletedFlag(Boolean.TRUE);
                        updatePaymentPlanList.add(paymentPlan);
                    });
                }
                //批量插入
                if (insertPaymentPlanList.size() > 0) {
                    paymentPlanExtMapper.batchInsert(insertPaymentPlanList);
                }
                //批量更新
                if (updatePaymentPlanList.size() > 0) {
                    paymentPlanExtMapper.batchUpdate(updatePaymentPlanList);
                }

                //回写合同原件和合同附件
                if (purchaseContractDTO.getOriginalContractAnnex() != null || purchaseContractDTO.getAnnex() != null) {
                    PurchaseContract item = new PurchaseContract();
                    item.setId(contractId);
                    item.setOriginalContractAnnex(purchaseContractDTO.getOriginalContractAnnex());
                    item.setAnnex(purchaseContractDTO.getAnnex());
                    purchaseContractMapper.updateByPrimaryKeySelective(item);
                }
                return findDetailById(contractId);
            }
        } catch (Exception e) {
            logger.error("makePaymentPlanTemporary加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, value);
        }
        return null;
    }

    @Override
    public PurchaseContractVO findDetailById(Long id, String type) {
        PurchaseContractVO vo = findDetailById(id);
        if (StringUtils.isNotEmpty(type) && "change".equals(type)) {
            PurchaseContractChangeHeaderExample example = new PurchaseContractChangeHeaderExample();
            example.setOrderByClause("create_at desc");
            example.createCriteria().andPurchaseContractIdEqualTo(id)
                    .andChangeTypeNotEqualTo(PurchaseContractChangeType.CONTRACTDETAIL.getCode())
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            List<PurchaseContractChangeHeader> purchaseContractChangeHeaders = purchaseContractChangeHeaderMapper.selectByExample(example);
            PurchaseContractChangeHeader po = purchaseContractChangeHeaders.stream().findFirst().orElse(null);
            vo.setFileAttachmentId(Objects.isNull(po) ? null : po.getFileAttachmentId());
        }
        return vo;
    }

    @Override
    public PurchaseContractVO findDetailById(Long id) {
        final PurchaseContract purchaseContract = findById(id);
        Assert.notNull(purchaseContract, "采购合同不存在");
        final List<PurchaseContractDetail> purchaseContractDetails = purchaseContractDetailService.findByContractId(id);
        final List<PaymentPlan> paymentPlans = paymentPlanService.findByContractId(id);

        PurchaseContractVO purchaseContractVO = new PurchaseContractVO();
        BeanConverter.copy(purchaseContract, purchaseContractVO);
        final List<PurchaseContractDetailDTO> purchaseContractDetailDTOList =
                BeanConverter.copy(purchaseContractDetails, PurchaseContractDetailDTO.class);
        purchaseContractVO.setPurchaseContractDetails(purchaseContractDetailDTOList);

        //补全供应商信息
        setVendorInfo(purchaseContractVO);

        //采购合同标准条款信息
        setStandardTermsInfo(purchaseContractVO);

        List<PaymentPlanDTO> paymentPlanDTOList = BeanConverter.copy(paymentPlans, PaymentPlanDTO.class);

        boolean punishmentHandleQuery = getPunishmentHandleQuery();

        paymentPlanDTOList.forEach(paymentPlanDTO -> {
            // 累计申请付款金额（含税）--排除作废和审批中
            //取值同PaymentPlan的actualAmount，实际付款金额
            //BigDecimal actualApplyAmount = paymentApplyExtMapper.actualApplyAmount(paymentPlanDTO.getId());
            //logger.info("findDetailById的actualApplyAmount:{}", actualApplyAmount);
            if (Objects.isNull(paymentPlanDTO.getActualAmount())) {
                paymentPlanDTO.setActualAmount(BigDecimal.ZERO);
            }
            paymentPlanDTO.setActualApplyAmount(paymentPlanDTO.getActualAmount());
            // 在途金额
            BigDecimal transitAmount = paymentApplyExtMapper.transitAmount(paymentPlanDTO.getId());
            logger.info("findDetailById的transitAmount:{}", transitAmount);
            paymentPlanDTO.setTransitAmount(transitAmount);
            // 累计罚扣款（含税）
            BigDecimal totalpenaltyAmount = paymentApplyExtMapper.totalpenaltyAmount(paymentPlanDTO.getId());
            BigDecimal allocationPunishmentAmountWithTax = paymentPlanDTO.getAllocationPunishmentAmountWithTax();
            if (Objects.nonNull(allocationPunishmentAmountWithTax)) {
                totalpenaltyAmount = totalpenaltyAmount.add(allocationPunishmentAmountWithTax);
                paymentPlanDTO.setTotalpenaltyAmount(totalpenaltyAmount);
                paymentPlanDTO.setAllocationPunishmentAmountWithTax(totalpenaltyAmount);
            } else {
                paymentPlanDTO.setTotalpenaltyAmount(totalpenaltyAmount);
                paymentPlanDTO.setAllocationPunishmentAmountWithTax(totalpenaltyAmount);
            }

            if (punishmentHandleQuery) {
                paymentPlanDTO.setAllocationPunishmentAmountWithTax(allocationPunishmentAmountWithTax);
            }

            // 里程碑
            final ProjectMilepost projectMilepost = projectMilepostService.selectByPrimaryKey(paymentPlanDTO.getMilestoneId());
            if (projectMilepost != null) {
                paymentPlanDTO.setMilestoneName(projectMilepost.getName());
                paymentPlanDTO.setMilestoneEndTime(projectMilepost.getEndTime());
                paymentPlanDTO.setMilestoneStatus(projectMilepost.getStatus());
            }

        });

        purchaseContractVO.setPaymentPlans(paymentPlanDTOList);

        // 项目
        if (purchaseContractVO.getProjectId() != null) {
            final Project project = projectBusinessService.selectByPrimaryKey(purchaseContract.getProjectId());
            if (project != null) {
                purchaseContractVO.setProjectName(project.getName());
                purchaseContractVO.setProjectCode(project.getCode());
                purchaseContractVO.setProjectStatus(project.getStatus());
                purchaseContractVO.setWbsEnabled(project.getWbsEnabled());
            }
        }

        // 所属业务实体
        if (purchaseContractVO.getOuId() != null) {
            final OperatingUnit operatingUnit = CacheDataUtils.findOuById(purchaseContractVO.getOuId());
            if (operatingUnit != null) {
                purchaseContractVO.setOuName(operatingUnit.getOperatingUnitName());
            }
        }

        // 项目经理
        if (purchaseContractVO.getManager() != null) {
            final UserInfo userInfo = CacheDataUtils.findUserById(purchaseContractVO.getManager());
            if (userInfo != null) {
                purchaseContractVO.setManagerName(userInfo.getName());
            }
        }

        // 采购跟进人
        if (purchaseContractVO.getPurchasingFollower() != null) {
            final UserInfo userInfo = CacheDataUtils.findUserById(purchaseContractVO.getPurchasingFollower());
            if (userInfo != null) {
                purchaseContractVO.setPurchasingFollowerName(userInfo.getName());
            }
        }

        //查询该合同下是否存在付款申请（作废/取消支付除外）
        BigDecimal totalTaxPayIncludedPrice = paymentApplyExtMapper.totalTaxPayIncludedPrice(id);
        if (totalTaxPayIncludedPrice.compareTo(BigDecimal.ZERO) == 0) {
            purchaseContractVO.setIsExitPaymentApply(false);
        } else {
            purchaseContractVO.setIsExitPaymentApply(true);
        }

        //当采购合同存在有效结转时，不能变更关联项目
        Long countCarryBill = carryoverBillExtMapper.countCarryBillByContractId(id);
        if (countCarryBill != null && countCarryBill > 0) {
            purchaseContractVO.setIsExitCarryBill(Boolean.TRUE);
        } else {
            purchaseContractVO.setIsExitCarryBill(Boolean.FALSE);
        }
        if (purchaseContract != null) {
            purchaseContractVO.setConversionDate(purchaseContract.getConversionDate());
            purchaseContractVO.setConversionRate(purchaseContract.getConversionRate());
            purchaseContractVO.setConversionType(purchaseContract.getConversionType());
        }
        final VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(purchaseContractVO.getVendorId());

        if (vendorSiteBankDto != null) {
            purchaseContractVO.setUniteCreditCode(vendorSiteBankDto.getUniteCreditCode());
        }

        //添加模组
        if (purchaseContractVO.getProjectId() != null && StringUtils.isNotEmpty(purchaseContractVO.getCode())) {

            final ProjectContractBudgetMaterialExample example = new ProjectContractBudgetMaterialExample();
            ProjectContractBudgetMaterialExample.Criteria criteria = example.createCriteria();

            //详情只拿变更前的数据(针对变更中的数据 详情页只取变更前, 若非变更中的数据 当前代码为原逻辑)
            criteria.andProjectIdEqualTo(purchaseContractVO.getProjectId()).andCodeEqualTo(purchaseContractVO.getCode())
                    .andDeletedFlagEqualTo(Boolean.FALSE).andStatusEqualTo(Boolean.FALSE);

            final List<ProjectContractBudgetMaterial> materialList = budgetMaterialMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(materialList)) {
                List<ProjectContractBudgetMaterialVo> materialVos = new ArrayList<>();
                BigDecimal budgetMaterialTotalPrice = BigDecimal.ZERO;
                for (ProjectContractBudgetMaterial budgetMaterial : materialList) {
                    ProjectBudgetMaterial material = projectBudgetMaterialMapper.selectByPrimaryKey(budgetMaterial.getMaterialId());
                    if (material != null) {
                        BigDecimal budgetPriceTotal = purchaseContractBudgetMaterialService.getBudgetMaterialTotalAmount(material.getPriceTotal(),
                                material.getParentId());
                        budgetMaterialTotalPrice = budgetMaterialTotalPrice.add(budgetPriceTotal);
                    }

                    ProjectContractBudgetMaterialVo copy = BeanConverter.copy(budgetMaterial, ProjectContractBudgetMaterialVo.class);
                    // 每个合同的可用预算（不含税）
                    ProjectContractBudgetMaterialDto budgetMaterialDto =
                            purchaseContractBudgetMaterialService.getContractBudgetByMaterialId(budgetMaterial.getMaterialId(), id);
                    copy.setSpendableBalance(budgetMaterialDto != null ? budgetMaterialDto.getSpendableBalance() : BigDecimal.ZERO);
                    // 关联金额总额
                    BigDecimal priceTotal = BigDecimal.ZERO;
                    if (material != null) {
                        priceTotal = purchaseContractBudgetMaterialService.getBudgetMaterialTotalAmount(material.getPriceTotal(),
                                material.getParentId()).stripTrailingZeros();
                    }
                    if (priceTotal.scale() > 0) {
                        priceTotal = priceTotal.setScale(2, RoundingMode.HALF_UP);
                    }
                    copy.setPriceTotal(priceTotal);
                    //预算占用金额（不含税）-本位币
                    BigDecimal conversionRate = Optional.ofNullable(purchaseContractVO.getConversionRate()).orElse(BigDecimal.ONE);
                    copy.setExcludingTaxTotalBudgetAmount(BigDecimalUtils.multiplyAndScale(purchaseContractVO.getExcludingTaxAmount(),
                            conversionRate));
                    materialVos.add(copy);
                }
                purchaseContractVO.setBudgetTotalBalance(budgetMaterialTotalPrice);
                purchaseContractVO.setProjectContractBudgetMaterialVos(materialVos);
            }
        }

        //预算占用总额（不含税）-本位币
        if (purchaseContractVO.getExcludingTaxAmount() != null) {
            BigDecimal conversionRate = Optional.ofNullable(purchaseContractVO.getConversionRate()).orElse(BigDecimal.ONE);
            purchaseContractVO.setExcludingTaxTotalBudgetAmount(BigDecimalUtils.multiplyAndScale(purchaseContractVO.getExcludingTaxAmount(),
                    conversionRate));
        }

        //已开税票
        PaymentInvoiceDetailExample example = new PaymentInvoiceDetailExample();
        PaymentInvoiceDetailExample.Criteria criteria = example.createCriteria();
        criteria.andPurchaseContractCodeEqualTo(purchaseContract.getCode())
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<PaymentInvoiceDetail> paymentInvoiceDetails = paymentInvoiceDetailMapper.selectByExample(example);
        BigDecimal taxIncludePriceTotal = paymentInvoiceDetails.stream().map(PaymentInvoiceDetail::getTaxIncludedPrice).reduce(BigDecimal.ZERO,
                BigDecimal::add);

        purchaseContractVO.setPaymentInvoiceMoney(taxIncludePriceTotal);

        // 模板库返回的附件attachmentId(删除的数据也算上，都相同)----改成fileAttachmentId   (废弃)

        // wbs关联预算
        purchaseContractVO.setPurchaseContractWbsBudgets(purchaseContractBudgetService.getWbsContractBudgetByPurchaseContractId(purchaseContractVO.getId()));

        //采购合同进度执行金额（不含税）
        PurchaseContractProgressVo progressVo = purchaseContractService.calculateExecuteAmountTotal(id);
        purchaseContractVO.setPurchasingContractProgressAmount(progressVo.getExecuteAmountTotalHeader());

        //已入账税票金额（不含税）
        BigDecimal amountOfTaxReceipts = paymentInvoiceExtMapper.calculateAmountOfTaxReceipts(id);
        purchaseContractVO.setAmountOfTaxReceipts(amountOfTaxReceipts);

        // 关联预算，启用WBS时，显示采购合同详情页关联预算部分的WBS号，用逗号隔开，排重
        Project project = projectMapper.selectByPrimaryKey(purchaseContractVO.getProjectId());
        if (Boolean.TRUE.equals(project.getWbsEnabled())) {
            List<String> wbsCodeList = purchaseContractExtMapper.findWbsByPurchaseContract(id);
            if (ListUtils.isNotEmpty(wbsCodeList)) {
                purchaseContractVO.setRelevanceBudget(org.apache.commons.lang3.StringUtils.join(wbsCodeList, ","));
            }
        }

        //关联税票，数据取税票登记列表中该合同的所有税票
        List<PaymentInvoiceDetailDto> paymentInvoiceDetailList = paymentInvoiceDetailExtMapper.selectListByPurchaseContractId(id);
        paymentInvoiceDetailList.forEach(s -> s.setAttachmentDtos(ctcAttachmentService.selectListByModuleAndModuleId(CtcAttachmentModule.PAYMENT_INVOVOICE_DETAIL.code(), s.getId()))); //发票原件
        paymentInvoiceDetailList.forEach(invoice -> {
            String gscLineId = invoice.getGscLineId();
            String gscInvoiceNumber = invoice.getGscInvoiceNumber();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(gscLineId) && org.apache.commons.lang3.StringUtils.isNotBlank(gscInvoiceNumber)) {
                GscPaymentInvoiceDetail gscPaymentInvoiceDetail = gscPaymentInvoiceDetailExtMapper.getDetailByLineIdAndInvoiceNumber(gscLineId, gscInvoiceNumber);
                invoice.setSourceSystemName(PaymentApplySourceNameEnum.GSC.getName());
                invoice.setGscPaymentInvoiceDetailDto(gscPaymentInvoiceDetail);
            }
        });
        purchaseContractVO.setPaymentInvoiceDetails(paymentInvoiceDetailList);

        //关联应付发票，数据取应付发票列表中该合同的所有应付发票
        List<PaymentInvoiceDto> paymentInvoiceList = paymentInvoiceExtMapper.selectListByPurchaseContractId(id);
        BigDecimal entrypayableInvoiceAmount = paymentInvoiceList.stream()
                .filter(e -> Objects.equals(e.getStatus(), 2) && Objects.equals(e.getErpStatus(), 1) && Objects.equals(e.getSource(), 0))
                .map(PaymentInvoiceDto::getTotalInvoiceIncludedPrice).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        purchaseContractVO.setEntrypayableInvoiceAmount(entrypayableInvoiceAmount);
        paymentInvoiceList.stream()
                .filter(e -> Objects.equals(e.getAccountEntryType(), PaymentInvoiceAccountEntryTypeEnum.NO_INVOICE_PENALTY.getCode()))
                .filter(e -> Objects.nonNull(e.getTotalInvoiceIncludedPrice()))
                .forEach(invoice -> invoice.setTaxExcludedPrice(invoice.getTotalInvoiceIncludedPrice()));
        //应前端数据解析要求，此字段值复制到 详情页中 整体情况中的 已入账发票金额 即
        boolean isFirst = true;
        for (PaymentPlanDTO paymentPlanDTO : purchaseContractVO.getPaymentPlans()) {
            if (isFirst) {
                paymentPlanDTO.setEntrypayableInvoiceAmount(entrypayableInvoiceAmount);
                isFirst = false;
            } else {
                paymentPlanDTO.setEntrypayableInvoiceAmount(BigDecimal.ZERO);
            }
        }
        purchaseContractVO.setPaymentInvoices(paymentInvoiceList);

        //关联付款，数据取付款申请列表中该合同的所有付款申请
        PaymentApplyExample paymentApplyExample = new PaymentApplyExample();
        paymentApplyExample.createCriteria().andPurchaseContractIdEqualTo(id).andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
        paymentApplyExample.setOrderByClause("submit_date desc");
        List<PaymentApplyDto> paymentApplyList = BeanConverter.copy(paymentApplyMapper.selectByExample(paymentApplyExample), PaymentApplyDto.class);
        paymentApplyList.forEach(s -> statusTransformation(s));
        purchaseContractVO.setPaymentApplies(paymentApplyList);
        //罚扣信息查询
        List<PurchaseContractPunishmentVo> purchaseContractPunishmentVos = punishmentSelect(id, paymentApplyList);
        //purchaseContractPunishmentVos.removeIf(punishment->punishment.getSource()!=1);
        purchaseContractVO.setPunishmentList(purchaseContractPunishmentVos);
        BigDecimal totalPunishmentAmount = purchaseContractPunishmentVos.stream().map(PurchaseContractPunishment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal excludingTaxAmount = purchaseContractVO.getExcludingTaxAmount();
        if (Objects.nonNull(excludingTaxAmount)) {
            //减去手动罚扣金额总和 （原币）
            BigDecimal minusPunishmentExcludingTaxAmount = excludingTaxAmount.subtract(totalPunishmentAmount);
            purchaseContractVO.setExclusionPunishmentAmount(minusPunishmentExcludingTaxAmount);
            //设置本位币
            BigDecimal conversionRate = purchaseContractVO.getConversionRate();
            BigDecimal localMinusPunishmentExcludingTaxAmount = BigDecimalUtils.multiplyAndScale(minusPunishmentExcludingTaxAmount, conversionRate);
            purchaseContractVO.setLocalExclusionPunishmentAmount(localMinusPunishmentExcludingTaxAmount);
        }

        return purchaseContractVO;
    }

    private void setStandardTermsInfo(PurchaseContractVO purchaseContractVO) {
        if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(purchaseContractVO.getContractTermsFlg())) {
            String contractTermsIds = purchaseContractVO.getContractTermsIds();
            List<PurchaseContractStandardTermsDto> standardTermsDtoList = new ArrayList<>();
            if (StringUtils.isNotEmpty(contractTermsIds)) {
                List<Long> standardTermsIdList = Arrays.stream(contractTermsIds.split(","))
                        .map(String::trim)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(standardTermsIdList)) {
                    for (Long standardTermsId : standardTermsIdList) {
                        PurchaseContractStandardTerms purchaseContractStandardTerms = purchaseContractStandardTermsMapper.selectByPrimaryKey(standardTermsId);
                        if (purchaseContractStandardTerms != null) {
                            PurchaseContractStandardTermsDto standardTermsDto = new PurchaseContractStandardTermsDto();
                            BeanConverter.copy(purchaseContractStandardTerms, standardTermsDto);

                            PurchaseContractStandardTermsDeviationExample example = new PurchaseContractStandardTermsDeviationExample();
                            PurchaseContractStandardTermsDeviationExample.Criteria criteria = example.createCriteria();
                            criteria.andDeletedFlagEqualTo(false).andAssociationPurchaseTermsIdEqualTo(standardTermsId);
                            List<PurchaseContractStandardTermsDeviation> deviations = purchaseContractStandardTermsDeviationMapper.selectByExample(example);
                            if (CollectionUtils.isNotEmpty(deviations)) {
                                standardTermsDto.setStandardTermsDeviationList(deviations);
                            }
                            standardTermsDtoList.add(standardTermsDto);
                        }
                    }
                    purchaseContractVO.setStandardTermsDtoList(standardTermsDtoList);
                }
            }
        }
    }

    private boolean getPunishmentHandleQuery() {
        boolean punishmentHandleQueryFlag = false;
        try {
            // 获取请求体
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            Map<String, String> params = ServletUtil.getParamMap(request);

            if (MapUtils.isEmpty(params)) return punishmentHandleQueryFlag;

            //获取采购合同中罚扣处理查询标记
            String punishmentHandleQuery = params.get("punishmentHandleQuery");

            if (StringUtils.isNotEmpty(punishmentHandleQuery)) {
                return Boolean.parseBoolean(punishmentHandleQuery);
            }
            return punishmentHandleQueryFlag;
        } catch (Exception e) {
            logger.error("采购合同详情-获取是否罚扣处理查询标记失败", e);
            return punishmentHandleQueryFlag;
        }
    }

    /**
     * 罚扣信息查询
     *
     * @param id
     * @param paymentApplyList
     */
    private List<PurchaseContractPunishmentVo> punishmentSelect(Long id, List<PaymentApplyDto> paymentApplyList) {
        //查询关联的罚扣信息
        List<PurchaseContractPenalty> penaltyList = getPenaltyByContractId(id);

        //获取新的合同罚扣信息
        PurchaseContractPunishmentDto purchaseContractPunishmentDto = new PurchaseContractPunishmentDto();
        purchaseContractPunishmentDto.setPurchaseContractId(id);
        purchaseContractPunishmentDto.setStatus(PurchaseContractPunishmentApproveEnums.EFFECTIVE.getCode());
        List<PurchaseContractPunishmentVo> purchaseContractPunishmentVos;
        purchaseContractPunishmentVos = purchaseContractPunishmentService.selectListByStatus(purchaseContractPunishmentDto);
        for (PurchaseContractPunishmentVo purchaseContractPunishmentVo : purchaseContractPunishmentVos) {
            //设置罚扣来源
            purchaseContractPunishmentVo.setSource(PurchaseContractPunishmentSourceEnums.MANUAL.getCode());
            //设置含税金额
            String taxRate = purchaseContractPunishmentVo.getTaxRate();
            int taxRateNum = Integer.parseInt(taxRate.replace("%", ""));
            BigDecimal amount = purchaseContractPunishmentVo.getAmount();
            if (amount != null && amount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal taxIncludedAmount = amount.add(BigDecimal.valueOf(taxRateNum).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).multiply(amount));
                //设置含税金额
                purchaseContractPunishmentVo.setTaxIncludedAmount(taxIncludedAmount);
                //设置税额
                purchaseContractPunishmentVo.setTaxAmount(BigDecimal.valueOf(taxRateNum).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).multiply(amount));
            }
            purchaseContractPunishmentVo.setCreator(CacheDataUtils.findUserNameById(purchaseContractPunishmentVo.getCreateBy()));
        }
        //处理合同变更类型的罚扣
        if (ListUtils.isNotEmpty(penaltyList)) {
            for (PurchaseContractPenalty purchaseContractPenalty : penaltyList) {
                PurchaseContractPunishmentVo punishmentVoNew = new PurchaseContractPunishmentVo();
                if (purchaseContractPenalty.getPenaltySource() != null) {
                    punishmentVoNew.setSource(PurchaseContractPunishmentSourceEnums.CHANGE.getCode());
                }
                punishmentVoNew.setConfigName(purchaseContractPenalty.getPenaltyTypeName());
                //含税金额
                BigDecimal amount = purchaseContractPenalty.getAmount();
                //税率
                String taxRate = purchaseContractPenalty.getTaxRate();
                int taxRateNum = Integer.parseInt(taxRate.replace("%", ""));
                // 求出不含税金额
                BigDecimal notIncludedAmount = amount.divide(BigDecimal.valueOf(taxRateNum)
                        .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                        .add(new BigDecimal(1)), 2, RoundingMode.HALF_UP);
                // 设置不含税金额
                punishmentVoNew.setAmount(notIncludedAmount);
                // 设置含税金额
                punishmentVoNew.setTaxIncludedAmount(purchaseContractPenalty.getAmount());
                //设置税率
                punishmentVoNew.setTaxRate(purchaseContractPenalty.getTaxRate());
                //设置税额
                punishmentVoNew.setTaxAmount(purchaseContractPenalty.getTaxAmount());
                punishmentVoNew.setDeletedFlag(Boolean.FALSE);
                punishmentVoNew.setId(purchaseContractPenalty.getId());
                punishmentVoNew.setPurchaseContractId(purchaseContractPenalty.getPurchaseContractId());
                punishmentVoNew.setTaxId(purchaseContractPenalty.getTaxId());
                punishmentVoNew.setPenaltyType(Objects.nonNull(purchaseContractPenalty.getPenaltyType()) ? purchaseContractPenalty.getPenaltyType().toString() : null);
                punishmentVoNew.setPenaltyTypeName(purchaseContractPenalty.getPenaltyTypeName());
                punishmentVoNew.setPunishmentType(purchaseContractPenalty.getPenaltyTypeName());
                punishmentVoNew.setCreateAt(purchaseContractPenalty.getCreateAt());
                punishmentVoNew.setCreateBy(purchaseContractPenalty.getCreateBy());
                punishmentVoNew.setCreator(CacheDataUtils.findUserNameById(purchaseContractPenalty.getCreateBy()));
                //加入罚扣列表
                purchaseContractPunishmentVos.add(punishmentVoNew);
            }
        }

        //处理付款申请罚扣
        if (ListUtils.isNotEmpty(paymentApplyList)) {
            List<Long> paymentApplyIdList = paymentApplyList.stream().map(IdEntity::getId).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(paymentApplyIdList)) {
                PaymentPenaltyProfitExample paymentPenaltyProfitExample = new PaymentPenaltyProfitExample();
                paymentPenaltyProfitExample.createCriteria().andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code()).andPaymentApplyIdIn(paymentApplyIdList);
                List<PaymentPenaltyProfit> paymentPenaltyProfits = paymentPenaltyProfitMapper.selectByExample(paymentPenaltyProfitExample);
                if (ListUtils.isNotEmpty(paymentPenaltyProfits)) {
                    for (PaymentPenaltyProfit paymentPenaltyProfit : paymentPenaltyProfits) {
                        PurchaseContractPunishmentVo punishmentVo = new PurchaseContractPunishmentVo();
                        punishmentVo.setSource(PurchaseContractPunishmentSourceEnums.PAYMENT.getCode());
                        punishmentVo.setPaymentApplyId(paymentPenaltyProfit.getPaymentApplyId());
                        //设置罚扣编号，取付款申请单号
                        paymentApplyList.stream()
                                .filter(apply -> apply.getId().equals(paymentPenaltyProfit.getPaymentApplyId()))
                                .findFirst()
                                .ifPresent(apply -> {
                                    punishmentVo.setCode(apply.getPaymentApplyCode());
                                });
                        //设置罚扣类型
                        punishmentVo.setConfigName(paymentPenaltyProfit.getPayPenaltyType());
                        //罚扣金额（含税）
                        punishmentVo.setTaxIncludedAmount(paymentPenaltyProfit.getAmount());
                        //税率
                        if (Objects.nonNull(paymentPenaltyProfit.getTaxRate())) {
                            punishmentVo.setTaxRate(paymentPenaltyProfit.getTaxRate().toString());
                        }
                        //罚扣金额（不含税）和税额
                        if (Objects.nonNull(paymentPenaltyProfit.getTaxRate()) && paymentPenaltyProfit.getTaxRate().compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal paymentPenaltyProfitTaxRate = paymentPenaltyProfit.getTaxRate();
                            BigDecimal amount = paymentPenaltyProfit.getAmount();
                            // 求出不含税金额
                            BigDecimal notIncludedAmount = amount.divide(paymentPenaltyProfitTaxRate
                                    .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                                    .add(new BigDecimal(1)), 2, RoundingMode.HALF_UP);
                            punishmentVo.setAmount(notIncludedAmount);
                            punishmentVo.setTaxAmount(amount.multiply(paymentPenaltyProfitTaxRate));
                        } else {
                            punishmentVo.setAmount(paymentPenaltyProfit.getAmount());
                            punishmentVo.setTaxRate("0");
                            punishmentVo.setTaxAmount(BigDecimal.ZERO);
                        }

                        if (Objects.nonNull(paymentPenaltyProfit.getPayPenaltyType())) {
                            //罚扣款类型
                            Dict dict = basedataExtService.findDictByTypeAndCode(Constants.Prefix.PENALTY_TYPE, paymentPenaltyProfit.getPayPenaltyType());
                            punishmentVo.setPenaltyType(dict.getName());
                            punishmentVo.setPunishmentType(dict.getName());
                        }

                        punishmentVo.setId(paymentPenaltyProfit.getId());
                        punishmentVo.setDeletedFlag(Boolean.FALSE);
                        punishmentVo.setPurchaseContractId(id);
                        punishmentVo.setPenaltyTypeName(paymentPenaltyProfit.getReason());
                        punishmentVo.setCreateAt(paymentPenaltyProfit.getCreateAt());
                        punishmentVo.setCreator(CacheDataUtils.findUserNameById(paymentPenaltyProfit.getCreateBy()));
                        //加入罚扣列表
                        purchaseContractPunishmentVos.add(punishmentVo);

                    }
                }
            }
        }

        logger.info("采购合同明细-罚扣信息查询,采购合同ID:{},罚扣列表:{}", id, JSON.toJSONString(purchaseContractPunishmentVos));
        return purchaseContractPunishmentVos;
    }


    /**
     * 逻辑取自付款申请列表
     * PengBo:bizStatus
     */
    private void statusTransformation(PaymentApplyDto paymentApplyDto) {
        String auditStatus = paymentApplyDto.getAuditStatus() + "";
        if (auditStatus.equals(PaymentApplyBizStatus.DRAFT.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.PENDING.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.REFUSE.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.EFFECTIVE.getCode()) ||
                auditStatus.equals(PaymentApplyBizStatus.CANCEL.getCode())
        ) {
            paymentApplyDto.setBizStatus(paymentApplyDto.getAuditStatus() + "");
        }

        if (auditStatus.equals(PaymentApplyBizStatus.EFFECTIVE.getCode())) {
            String is = paymentApplyDto.getIsCharge() + "";//是否预付款(1:是 0否)
            String status = "";
            if ("1".equals(is)) {
                status = paymentApplyDto.getGcebStatus();
            } else {
                status = paymentApplyDto.getErpStatus();
            }
            if (status.equals(PaymentApplyGcebErpStatus.SUCCESS.getCode())) {
                paymentApplyDto.setBizStatus(PaymentApplyBizStatus.SUCCESS.getCode());
            } else if (status.equals(PaymentApplyGcebErpStatus.CANCEL.getCode())) {
                paymentApplyDto.setBizStatus(PaymentApplyBizStatus.CANCEL_PAY.getCode());
            } else if (status.equals(PaymentApplyGcebErpStatus.PARTIAL_PAYMENT.getCode())) {
                paymentApplyDto.setBizStatus(PaymentApplyBizStatus.PARTIAL_PAYMENT.getCode());
            } else if (status.equals(PaymentApplyGcebErpStatus.EXCEPTION.getCode())) {
                paymentApplyDto.setBizStatus(PaymentApplyBizStatus.EXCEPTION.getCode());
            } else if (status.equals(PaymentApplyGcebErpStatus.REJECTED.getCode())) {
                paymentApplyDto.setBizStatus(PaymentApplyBizStatus.REJECTED.getCode());
            }
        }
    }

    private void setVendorInfo(PurchaseContractVO purchaseContractVO) {
        //补全供应商信息
        Long ouId = purchaseContractVO.getOuId();
        String erpVendorSiteId = purchaseContractVO.getErpVendorSiteId();
        String vendorCode = purchaseContractVO.getVendorCode();
        if (StringUtils.isNotEmpty(erpVendorSiteId) && StringUtils.isNotEmpty(vendorCode)) {
            VendorSiteBankForDisplay vendorSiteBankInfo = basedataExtService.getVendorSiteBankStatus(ouId, vendorCode, erpVendorSiteId);
            if (vendorSiteBankInfo != null) {
                // 供应商失效状态
                purchaseContractVO.setEndDateActive(vendorSiteBankInfo.getEndDateActive());
                purchaseContractVO.setPvsStatus(vendorSiteBankInfo.getPvsStatus());
                purchaseContractVO.setVendorTypeLookupCode(vendorSiteBankInfo.getVendorTypeLookupCode());
                purchaseContractVO.setVendorTypeName(vendorSiteBankInfo.getVendorTypeName());
            }
        }
    }

    private List<PurchaseContractPenalty> getPenaltyByContractId(Long id) {
        PurchaseContractPenaltyExample example = new PurchaseContractPenaltyExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andPurchaseContractIdEqualTo(id);
        return purchaseContractPenaltyMapper.selectByExample(example);
    }

    @Override
    public List<PurchaseContract> listPurchaseContract(PurchaseContractDTO purchaseContractDTO, String orderParam, Integer orderType) {
        final PurchaseContractExample purchaseContractExample = buildPurchaseContractExample(purchaseContractDTO, orderParam, orderType);
        final List<PurchaseContractExample.Criteria> criterias = purchaseContractExample.getOredCriteria();
        // 关联项目
        if (StringUtils.isNotEmpty(purchaseContractDTO.getProjectName())) {
            ProjectExample projectExample = new ProjectExample();
            final ProjectExample.Criteria projectExampleCriteria = projectExample.createCriteria();
            projectExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE);
            projectExampleCriteria.andNameLike(StringUtils.buildSqlLikeCondition(purchaseContractDTO.getProjectName()));
            final List<Project> projects = projectBusinessService.selectByExample(projectExample);
            if (ListUtils.isNotEmpty(projects)) {
                List<Long> projectIdSet = projects.stream().map(Project::getId).collect(Collectors.toList());
                criterias.get(0).andProjectIdIn(projectIdSet);
            } else {
                // 项目未找到，直接放回空列表
                return Lists.newArrayList();
            }
        }

        // 数据权限 (仅限使用单位内授权部门下的采购合同)
//        final List<OperatingUnitDto> ouList =
//                getOperatingUnit(SystemContext.getUserId(), SystemContext.getUnitId());
//        if (ouList.size() > 0) {
//            final List<Long> ouIdList = ouList.stream().map(OperatingUnit::getId).collect(Collectors.toList());
//            criteria.andOuIdIn(ouIdList);
//        } else {
//            return Lists.newArrayList();
//        }

        final List<PurchaseContract> purchaseContracts = selectByExample(purchaseContractExample);
        return purchaseContracts;
    }

    @Override
    public List<PurchaseContractVO> listPurchaseContractVO(PurchaseContractDTO purchaseContractDTO, String orderParam, Integer orderType) {
        final List<PurchaseContract> purchaseContracts = listPurchaseContract(purchaseContractDTO, orderParam, orderType);
        if (ListUtils.isNotEmpty(purchaseContracts)) {
            final List<PurchaseContractVO> purchaseContractVOList = BeanConverter.copy(purchaseContracts, PurchaseContractVO.class);
            final List<Long> projectIdList = purchaseContractVOList.stream().map(PurchaseContractVO::getProjectId).collect(Collectors.toList());
            ProjectExample projectExample = new ProjectExample();
            final ProjectExample.Criteria projectExampleCriteria = projectExample.createCriteria();
            projectExampleCriteria.andIdIn(projectIdList);
            final List<Project> projects = projectBusinessService.selectByExample(projectExample);
            if (ListUtils.isNotEmpty(projects)) {
                Map<Long, String> nameMap = new HashMap<>();
                projects.forEach(project -> {
                    nameMap.put(project.getId(), project.getName());
                });

                purchaseContractVOList.forEach(purchaseContractVO -> {
                    final Long projectId = purchaseContractVO.getProjectId();
                    if (projectId != null) {
                        purchaseContractVO.setProjectName(nameMap.get(projectId));
                    }
                });
            }

            // 实际付款金额
            purchaseContractVOList.forEach(purchaseContractVO -> purchaseContractVO.setActualAmount(BigDecimal.ZERO));

            return purchaseContractVOList;
        }
        return Lists.newArrayList();
    }

    private PurchaseContractExample buildPurchaseContractExample(PurchaseContractDTO purchaseContractDTO, String orderParam, Integer orderType) {
        PurchaseContractExample purchaseContractExample = new PurchaseContractExample();
        final PurchaseContractExample.Criteria criteria = purchaseContractExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);

        // 合同编号
        if (StringUtils.isNotEmpty(purchaseContractDTO.getCode())) {
            criteria.andCodeLike(StringUtils.buildSqlLikeCondition(purchaseContractDTO.getCode()));
        }

        // 合同名称
        if (StringUtils.isNotEmpty(purchaseContractDTO.getName())) {
            criteria.andNameLike(StringUtils.buildSqlLikeCondition(purchaseContractDTO.getName()));
        }

        // 供应商
        if (StringUtils.isNotEmpty(purchaseContractDTO.getVendorName())) {
            criteria.andVendorNameLike(StringUtils.buildSqlLikeCondition(purchaseContractDTO.getVendorName()));
        }

        // 采购跟进人
        if (StringUtils.isNotEmpty(purchaseContractDTO.getPurchasingFollowerName())) {
            criteria.andPurchasingFollowerNameLike(StringUtils.buildSqlLikeCondition(purchaseContractDTO.getPurchasingFollowerName()));
        }

        // 开始时间
        if (purchaseContractDTO.getStartTime() != null) {
            criteria.andStartTimeGreaterThanOrEqualTo(purchaseContractDTO.getStartTime());
        }

        // 结束时间
        if (purchaseContractDTO.getEndTime() != null) {
            criteria.andEndTimeLessThanOrEqualTo(purchaseContractDTO.getEndTime());
        }

        if (purchaseContractDTO.getFilingStartDate() != null) {
            criteria.andFilingDateGreaterThanOrEqualTo(purchaseContractDTO.getFilingStartDate());
        }

        if (purchaseContractDTO.getFilingEndDate() != null) {
            criteria.andFilingDateLessThanOrEqualTo(purchaseContractDTO.getFilingEndDate());
        }

        // 合同状态
        if (purchaseContractDTO.getStatus() != null) {
            criteria.andStatusEqualTo(purchaseContractDTO.getStatus());
        }

        // 数据权限 (仅限使用单位内授权部门下的采购合同)
        final List<Long> ouList = SystemContext.getOus();
        // 为空，传入查询异常
        ouList.add(NOT_EXIST_ID);
        criteria.andOuIdIn(ouList);

        // 排序
        if (StringUtils.isNotEmpty(orderParam)) {
            // 转义下字符
            if (Objects.equals("filingDate", orderParam)) {
                orderParam = "filing_date";
            }

            if (1 == orderType) {
                purchaseContractExample.setOrderByClause(orderParam + " asc");
            } else {
                purchaseContractExample.setOrderByClause(orderParam + " desc");
            }
        } else {
            purchaseContractExample.setOrderByClause(" create_at desc");
        }

        return purchaseContractExample;
    }

    private List<OperatingUnitDto> getOperatingUnit(Long userId, Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("userId", userId);
        param.put("unitId", unitId);
        String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/orgUnit/selectUserOuList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        final DataResponse<List<OperatingUnitDto>> response =
                JSON.parseObject(res, new TypeReference<DataResponse<List<OperatingUnitDto>>>() {
                });

        if (response != null && response.getData() != null) {
            final List<OperatingUnitDto> list = response.getData();
            return list;
        }

        return Lists.newArrayList();
    }


    @Override
    public PageInfo<PurchaseContractVO> pagePurchaseContract(PurchaseContractDTO purchaseContractDTO, String orderParam, Integer orderType,
                                                             int pageNum, int pageSize) {
        // 关联项目
        // 为了分页参数不作为查询条件传入
        if (StringUtils.isNotEmpty(purchaseContractDTO.getProjectName())) {
            ProjectExample projectExample = new ProjectExample();
            final ProjectExample.Criteria projectExampleCriteria = projectExample.createCriteria();
            projectExampleCriteria.andDeletedFlagEqualTo(Boolean.FALSE);
            projectExampleCriteria.andNameLike(StringUtils.buildSqlLikeCondition(purchaseContractDTO.getProjectName()));
            final List<Project> projects = projectBusinessService.selectByExample(projectExample);
            if (ListUtils.isNotEmpty(projects)) {
                List<Long> projectIdSet = projects.stream().map(Project::getId).collect(Collectors.toList());
                purchaseContractDTO.setProjectIds(projectIdSet);
            } else {
                // 项目未找到，直接放回空列表
                return new PageInfo<>();
            }
        }

        // 转以下字符
        if (Objects.equals("filingDate", orderParam)) {
            orderParam = "filing_date";
        }

        // 数据权限 (仅限使用单位内授权部门下的采购合同)
        final List<Long> ouList = SystemContext.getOus();
        // 为空，传入查询异常
        ouList.add(NOT_EXIST_ID);
        purchaseContractDTO.setOuIds(ouList);

        PageHelper.startPage(pageNum, pageSize);
        final List<PurchaseContract> purchaseContracts = purchaseContractExtMapper.selectList(purchaseContractDTO, orderParam, orderType);
        final PageInfo<PurchaseContractVO> pageInfo = BeanConverter.convertPage(purchaseContracts, PurchaseContractVO.class);
        final List<PurchaseContractVO> list = pageInfo.getList();
        if (ListUtils.isNotEmpty(list)) {
            ListUtils.splistList(list, 500).forEach(subList -> {

                List<Long> projectIds = subList.stream().map(PurchaseContractVO::getProjectId)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList());
                Map<Long, Project> projectMap = Collections.emptyMap();
                if (!projectIds.isEmpty()) {
                    projectMap = projectExtMapper.selectByIds(projectIds).stream().collect(Collectors.toMap(Project::getId, e -> e));
                }

                List<String> vendorCodes = subList.stream().filter(e ->
                                StringUtils.isNotEmpty(e.getErpVendorSiteId()) && StringUtils.isNotEmpty(e.getVendorCode()))
                        .map(PurchaseContractVO::getVendorCode).distinct().collect(Collectors.toList());
                Map<String, VendorSiteBankDto> vendorSiteBankDtoMap = basedataExtService.getVendorSiteBankByCode(vendorCodes).stream()
                        .collect(Collectors.toMap(e -> e.getOperatingUnitId() + e.getVendorCode() + e.getErpVendorSiteId(), e -> e, (e1, e2) -> e1));

                List<Long> ouIds = subList.stream().map(PurchaseContractVO::getOuId)
                        .filter(Objects::nonNull).distinct().collect(Collectors.toList());

                Map<Long, String> ouMap = basedataExtService.getOuInfoByOuIds(ouIds).stream()
                        .collect(Collectors.toMap(OperatingUnitDto::getId, OperatingUnitDto::getOperatingUnitName));

                List<Long> contractIds = subList.stream().map(PurchaseContractVO::getId).collect(Collectors.toList());
                Map<Long, BigDecimal> actualPaymentAmountMap = paymentPlanService.getActualPaymentAmountByContractIds(contractIds);

                for (PurchaseContractVO vo : subList) {
                    Project project = projectMap.get(vo.getProjectId());
                    if (project != null) {
                        vo.setProjectName(project.getName());
                        vo.setProjectCode(project.getCode());
                    }

                    if (StringUtils.isNotEmpty(vo.getErpVendorSiteId()) && StringUtils.isNotEmpty(vo.getVendorCode())) {
                        VendorSiteBankDto vendorSiteBankDto = vendorSiteBankDtoMap.get(vo.getOuId() + vo.getVendorCode() + vo.getErpVendorSiteId());
                        if (vendorSiteBankDto != null) {
                            vo.setEndDateActive(vendorSiteBankDto.getEndDateActive());
                            vo.setPvsStatus(vendorSiteBankDto.getPvsStatus());
                            vo.setVendorTypeLookupCode(vendorSiteBankDto.getVendorTypeLookupCode());
                            vo.setVendorTypeName(vendorSiteBankDto.getVendorTypeName());
                        }
                    }

                    if (vo.getOuId() != null) {
                        vo.setOuName(ouMap.get(vo.getOuId()));
                    }

                    vo.setActualAmount(actualPaymentAmountMap.get(vo.getId()));
                }
            });
        }
        return pageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteById(Long id) {
        final PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(id);
        if (purchaseContract == null) {
            return 1;
        }

        final Integer status = purchaseContract.getStatus();
        if (PurchaseContractStatus.DRAFT.getCode() != status) {
            throw new MipException("合同不为草稿状态，不能删除");
        }

        final List<PurchaseContractDetail> purchaseContractDetails = purchaseContractDetailService.findByContractId(id);
        if (ListUtils.isNotEmpty(purchaseContractDetails)) {
            purchaseContractDetails.forEach(purchaseContractDetail -> purchaseContractDetailService.deleteById(purchaseContractDetail.getId()));
        }

        final List<PaymentPlan> paymentPlans = paymentPlanService.findByContractId(id);
        if (ListUtils.isNotEmpty(paymentPlans)) {
            paymentPlans.forEach(paymentPlan -> paymentPlanService.deleteById(id));
        }

        PurchaseContractBudgetExample budgetExample = new PurchaseContractBudgetExample();
        budgetExample.createCriteria().andPurchaseContractIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE);
        List<PurchaseContractBudget> purchaseContractBudgets = purchaseContractBudgetMapper.selectByExample(budgetExample);
        if (ListUtils.isNotEmpty(purchaseContractBudgets)) {
            for (PurchaseContractBudget purchaseContractBudget : purchaseContractBudgets) {
                purchaseContractBudget.setDeletedFlag(Boolean.TRUE);
                purchaseContractBudgetMapper.updateByPrimaryKeySelective(purchaseContractBudget);
                // 更新采购需求状态
                PurchaseMaterialRequirement requirement = new PurchaseMaterialRequirement();
                requirement.setId(purchaseContractBudget.getPurchaseRequirementId());
                requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
                purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(requirement);
            }
        }

        purchaseContract.setStatus(PurchaseContractStatus.CANCEL.getCode());
        purchaseContractMapper.updateByPrimaryKey(purchaseContract);
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int cancelContractById(Long id) {
        final PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(id);
        if (purchaseContract == null) {
            return 1;
        }

        // 驳回、已结束状态
        if (!Objects.equals(PurchaseContractStatus.DRAFT.getCode(), purchaseContract.getStatus())
                && !Objects.equals(PurchaseContractStatus.REFUSE.getCode(), purchaseContract.getStatus())
                && !Objects.equals(PurchaseContractStatus.COMPLETE.getCode(), purchaseContract.getStatus())) {
            throw new MipException("合同不为草稿、驳回或已结束状态，不能作废");
        }

        purchaseContract.setStatus(PurchaseContractStatus.CANCEL.getCode());
        purchaseContractMapper.updateByPrimaryKey(purchaseContract);

        //查询合同新增时关联的采购物料，如果已采购量=总需求量(即未采购量=0)，采购需求状态改为：已关闭；否则改为：待采购
        purchaseContractBudgetService.updateRequirementStatus(purchaseContract.getId());
        return 1;
    }

    @Override
    public void abandon(Long id) {
        Assert.notNull(id, "变更记录ID不能为空");
        PurchaseContractChangeHeader header = new PurchaseContractChangeHeader();
        header.setId(id);
        header.setStatus(PurchaseContractChangeHeaderStatus.ABANDON.code());
        purchaseContractChangeHeaderMapper.updateByPrimaryKeySelective(header);
    }


    @Override
    public List<PurchaseContract> findNeedUpdateStatus() {
        PurchaseContractExample example = new PurchaseContractExample();
        final PurchaseContractExample.Criteria criteria = example.createCriteria();
        List<Integer> statusList = new ArrayList<>();
        statusList.add(PurchaseContractStatus.TO_BE_EFFECTIVE.getCode());
        statusList.add(PurchaseContractStatus.EFFECTIVE.getCode());
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andStatusIn(statusList);
        return purchaseContractMapper.selectByExample(example);
    }

    @Override
    public List<PurchaseContractDTO> findPurchaseContractListByProjectAndTypeNames(Long projectId, List<String> typeNames, Boolean carryoverFlag) {
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);

        PurchaseContractExample example = new PurchaseContractExample();
        final PurchaseContractExample.Criteria criteria = example.createCriteria();
        if (ListUtils.isNotEmpty(typeNames)) {
            criteria.andTypeNameIn(typeNames);
        }
        if (carryoverFlag != null) {
            criteria.andCarryoverFlagEqualTo(carryoverFlag);
        }
        criteria.andProjectIdEqualTo(projectId);
        criteria.andStatusEqualTo(PurchaseContractStatus.EFFECTIVE.getCode());

        List<PurchaseContract> list = purchaseContractMapper.selectByExample(example);
        return BeanConverter.convert(list, PurchaseContractDTO.class);
    }


    public VendorSiteBank getVendorSitBank(Long vendorId) {
        Map<String, Object> param = new HashMap<>();
        param.put("vendorId", vendorId);
        final String url = StringUtils.buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/getVendorById", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<VendorSiteBank> response =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<VendorSiteBank>>() {
                });
        return response.getData();
    }


    @Override
    public PageInfo<PurchaseContractChangeHeaderDto> getChangeHistoryList(Long purchaseContractId, Integer pageNum, Integer pageSize) {
        List<Integer> filterStatus = new ArrayList<>();
        filterStatus.add(PurchaseContractChangeHeaderStatus.DELETE.code());

        PurchaseContractChangeHeaderExample example = new PurchaseContractChangeHeaderExample();
        example.setOrderByClause("create_at desc");
        example.createCriteria().andPurchaseContractIdEqualTo(purchaseContractId)
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andStatusNotIn(filterStatus)
                .andChangeTypeNotEqualTo(PurchaseContractChangeType.BUILD.getCode()); //变更记录排除类型0:项目新增
        PageHelper.startPage(pageNum, pageSize);
        return new PageInfo<>(BeanConverter.copy(purchaseContractChangeHeaderMapper.selectByExample(example), PurchaseContractChangeHeaderDto.class));
    }

    /**
     * 校验获取采购合同
     *
     * @param purchaseContractId 合同id
     * @return 采购合同
     */
    private PurchaseContract getPurchaseContract(Long purchaseContractId) {
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);
        Assert.notNull(purchaseContract, AssertErrorMessage.PURCHASE_CONTRACT_NOT_EXIST);
        return purchaseContract;
    }

    /**
     * 创建变更信息头表
     * 如果是二次变更就删除行结构
     *
     * @param type 变更类型
     * @return 变更头
     */
    @Transactional(rollbackFor = Exception.class)
    public PurchaseContractChangeHeader buildHeader(PurchaseContractChangeType type, PurchaseContractDTO contractDto) {
        if (contractDto.getHeaderId() != null) {
            PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(contractDto.getHeaderId());

            Assert.notNull(header, AssertErrorMessage.PURCHASE_CONTRACT_CHANGE_NOT_EXIST);
            if (Objects.equals(PurchaseContractChangeHeaderStatus.CHECKING.code(), header.getStatus()) || Objects.equals(PurchaseContractChangeHeaderStatus.PASS.code(), header.getStatus())) {
                throw new BizException(Code.ERROR, "流程提交后不允许再次保存草稿！");
            }

            //删除基本信息变更项
            PurchaseContractChangeHistoryExample contractChangeHistoryExample = new PurchaseContractChangeHistoryExample();
            contractChangeHistoryExample.createCriteria().andHeaderIdEqualTo(contractDto.getHeaderId());
            List<PurchaseContractChangeHistory> list = purchaseContractChangeHistoryMapper.selectByExample(contractChangeHistoryExample);
            if (PublicUtil.isNotEmpty(list)) {
                list.forEach(purchaseContractChangeHistory -> {
                    purchaseContractChangeHistoryMapper.deleteByPrimaryKey(purchaseContractChangeHistory.getId());
                });
            }
            //删除采购合同内容变更项
            PurchaseContractDetailChangeHistoryExample detailChangeHistoryExample = new PurchaseContractDetailChangeHistoryExample();
            detailChangeHistoryExample.createCriteria().andHeaderIdEqualTo(contractDto.getHeaderId());
            List<PurchaseContractDetailChangeHistory> detailChangeHistoryList =
                    purchaseContractDetailChangeHistoryMapper.selectByExample(detailChangeHistoryExample);
            if (PublicUtil.isNotEmpty(detailChangeHistoryList)) {
                detailChangeHistoryList.forEach(detailChangeHistory -> {
                    purchaseContractDetailChangeHistoryMapper.deleteByPrimaryKey(detailChangeHistory.getId());
                });
            }
            //删除采购合同付款计划变更项
            PaymentPlanChangeHistoryExample paymentPlanChangeHistoryExample = new PaymentPlanChangeHistoryExample();
            paymentPlanChangeHistoryExample.createCriteria().andHeaderIdEqualTo(contractDto.getHeaderId());
            List<PaymentPlanChangeHistory> paymentPlanChangeHistoryList =
                    paymentPlanChangeHistoryMapper.selectByExample(paymentPlanChangeHistoryExample);
            if (PublicUtil.isNotEmpty(paymentPlanChangeHistoryList)) {
                paymentPlanChangeHistoryList.forEach(paymentPlanChangeHistory -> {
                    paymentPlanChangeHistoryMapper.deleteByPrimaryKey(paymentPlanChangeHistory.getId());
                });
            }
            //删除采购合同罚扣变更项
            PurchaseContractPenaltyChangeHistoryExample penaltyChangeHistoryExample = new PurchaseContractPenaltyChangeHistoryExample();
            penaltyChangeHistoryExample.createCriteria().andHeaderIdEqualTo(contractDto.getHeaderId());
            List<PurchaseContractPenaltyChangeHistory> penaltyChangeHistoryList =
                    purchaseContractPenaltyChangeHistoryMapper.selectByExample(penaltyChangeHistoryExample);
            if (PublicUtil.isNotEmpty(penaltyChangeHistoryList)) {
                penaltyChangeHistoryList.forEach(penaltyChangeHistory -> {
                    purchaseContractPenaltyChangeHistoryMapper.deleteByPrimaryKey(penaltyChangeHistory.getId());
                });
            }
            //删除采购合同关联预算(WBS)
            PurchaseContractBudgetChangeHistoryExample budgetChangeHistoryExample = new PurchaseContractBudgetChangeHistoryExample();
            budgetChangeHistoryExample.createCriteria().andHeaderIdEqualTo(contractDto.getHeaderId());
            List<PurchaseContractBudgetChangeHistory> budgetChangeHistoryList =
                    purchaseContractBudgetChangeHistoryMapper.selectByExample(budgetChangeHistoryExample);
            if (PublicUtil.isNotEmpty(budgetChangeHistoryList)) {
                budgetChangeHistoryList.forEach(budgetChangeHistory -> {
                    purchaseContractBudgetChangeHistoryMapper.deleteByPrimaryKey(budgetChangeHistory.getId());
                });
            }
            //删除采购合同关联预算(非WBS)
            ProjectContractBudgetMaterialChangeHistoryExample budgetMaterialChangeHistoryExample =
                    new ProjectContractBudgetMaterialChangeHistoryExample();
            budgetMaterialChangeHistoryExample.createCriteria().andHeaderIdEqualTo(contractDto.getHeaderId());
            List<ProjectContractBudgetMaterialChangeHistory> budgetMaterialChangeHistoryList =
                    projectContractBudgetMaterialChangeHistoryMapper.selectByExample(budgetMaterialChangeHistoryExample);
            if (PublicUtil.isNotEmpty(budgetMaterialChangeHistoryList)) {
                budgetMaterialChangeHistoryList.forEach(budgetMaterialChangeHistory -> {
                    projectContractBudgetMaterialChangeHistoryMapper.deleteByPrimaryKey(budgetMaterialChangeHistory.getId());
                });
            }
            //驳回重新编辑，系统不能修改为草稿状态
            if (!Objects.equals(header.getStatus(), PurchaseContractChangeHeaderStatus.RETURN.code()) && !Objects.equals(header.getStatus(),
                    PurchaseContractChangeHeaderStatus.REJECT.code())) {
                header.setStatus(PurchaseContractChangeHeaderStatus.DRAFT.code());
            }
            header.setReason(contractDto.getReason());
            header.setChangeRessonType(contractDto.getChangeRessonType());
            header.setChangeRessonTypeName(contractDto.getChangeRessonTypeName());
            header.setIsElectronicContract(contractDto.getIsElectronicContract());
            header.setOtherName(contractDto.getOtherName());
            header.setOtherPhone(contractDto.getOtherPhone());
            header.setOtherEmail(contractDto.getOtherEmail());
            header.setPublicOrPrivate(contractDto.getPublicOrPrivate());
            header.setSealCategory(contractDto.getSealCategory());
            header.setSealAdminAccountIds(contractDto.getSealAdministratorChangeIds());
            header.setChangeProtocolFile(contractDto.getChangeProtocolFile());
            header.setFileAttachmentId(contractDto.getFileAttachmentId());
            purchaseContractChangeHeaderMapper.updateByPrimaryKeySelective(header);
            return header;
        }
        PurchaseContractChangeHeader header = new PurchaseContractChangeHeader();
        header.setChangeType(type.getCode());
        header.setDeletedFlag(DeletedFlag.VALID.code());
        header.setPurchaseContractId(contractDto.getId());
        header.setStatus(PurchaseContractChangeHeaderStatus.DRAFT.code());
        header.setReason(contractDto.getReason());
        header.setChangeRessonType(contractDto.getChangeRessonType());
        header.setChangeRessonTypeName(contractDto.getChangeRessonTypeName());
        header.setIsElectronicContract(contractDto.getIsElectronicContract());
        header.setOtherName(contractDto.getOtherName());
        header.setOtherPhone(contractDto.getOtherPhone());
        header.setOtherEmail(contractDto.getOtherEmail());
        header.setPublicOrPrivate(contractDto.getPublicOrPrivate());
        header.setSealCategory(contractDto.getSealCategory());
        header.setSealAdminAccountIds(contractDto.getSealAdministratorChangeIds());
        header.setChangeProtocolFile(contractDto.getChangeProtocolFile());
        header.setFileAttachmentId(contractDto.getFileAttachmentId());
        purchaseContractChangeHeaderMapper.insertSelective(header);
        return header;
    }

    /**
     * 生成合同版本历史版本号，规则：“PAM采购合同编号”+“-”+一位流水号   比如，KCG22030043-1
     *
     * @return
     */
    @Override
    public String generateVersionCode(PurchaseContractChangeHeader header, String purchaseContractCode) {
        if (StringUtils.isNotEmpty(header.getVersionCode())) return header.getVersionCode();

        Integer newOrder = 1;
        //1.查询已有流水号
        PurchaseContractChangeHeaderExample example = new PurchaseContractChangeHeaderExample();
        example.createCriteria().andPurchaseContractIdEqualTo(header.getPurchaseContractId());
        List<PurchaseContractChangeHeader> headList = purchaseContractChangeHeaderMapper.selectByExample(example);
        if (ListUtil.isPresent(headList)) {
            List<Integer> codeList = new ArrayList<>();
            for (PurchaseContractChangeHeader changeHeader : headList) {
                if (StringUtils.isNotEmpty(changeHeader.getVersionCode())) {
                    String[] code = StringUtils.split(changeHeader.getVersionCode(), "-");
                    if (code.length < 1) {
                        continue;
                    }
                    codeList.add(Integer.valueOf(code[code.length - 1]));
                }
            }
            if (ListUtils.isNotEmpty(codeList)) {
                //2.创建新流水号：获取version_code最大值+1
                newOrder = codeList.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList()).get(0) + 1;
            }
        }

        //3.拼接生成进度执行单号
        StringBuffer versionCode = new StringBuffer();
        versionCode.append(purchaseContractCode);
        versionCode.append("-");
        versionCode.append(newOrder);
        return versionCode.toString();
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveBaseInfoChangeHistory(PurchaseContractDTO contractDTO, PurchaseContract purchaseContract, Long headerId) {
        PurchaseContractChangeHistory change = new PurchaseContractChangeHistory();
        BeanUtils.copyProperties(contractDTO, change);
        change.setId(null);
        change.setHistoryType(com.midea.pam.common.enums.HistoryType.CHANGE.getCode());
        change.setOriginId(contractDTO.getId());
        change.setHeaderId(headerId);
        change.setUpdateAt(new Date());
        change.setUpdateBy(SystemContext.getUserId());
        if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(contractDTO.getContractTermsFlg())){
            List<PurchaseContractStandardTermsDto> standardTermsDtoList = contractDTO.getStandardTermsDtoList();
            if (CollectionUtils.isNotEmpty(standardTermsDtoList)) {
                List<Long> termsIds = new ArrayList<>();
                for (PurchaseContractStandardTermsDto standardTermsDto : standardTermsDtoList) {
                    PurchaseContractStandardTerms purchaseContractStandardTerms = new PurchaseContractStandardTerms();
                    purchaseContractStandardTerms.setPurchaseContractId(purchaseContract.getId());
                    purchaseContractStandardTerms.setAssociationTermsId(standardTermsDto.getAssociationTermsId());
                    purchaseContractStandardTerms.setTermsCode(standardTermsDto.getTermsCode());
                    purchaseContractStandardTerms.setTermsName(standardTermsDto.getTermsName());
                    purchaseContractStandardTerms.setTermsDisplayContent(standardTermsDto.getTermsDisplayContent());
                    purchaseContractStandardTerms.setDeletedFlag(false);
                    purchaseContractStandardTermsMapper.insert(purchaseContractStandardTerms);
                    termsIds.add(purchaseContractStandardTerms.getId());

                    List<PurchaseContractStandardTermsDeviation> standardTermsDeviationList = standardTermsDto.getStandardTermsDeviationList();
                    if (CollectionUtils.isNotEmpty(standardTermsDeviationList)) {
                        for (PurchaseContractStandardTermsDeviation purchaseContractStandardTermsDeviation : standardTermsDeviationList) {
                            PurchaseContractStandardTermsDeviation standardTermsDeviation = new PurchaseContractStandardTermsDeviation();
                            standardTermsDeviation.setAssociationPurchaseTermsId(purchaseContractStandardTerms.getId());
                            String deviationInfo = purchaseContractStandardTermsDeviation.getDeviationInfo();
                            if (StringUtils.isNotEmpty(deviationInfo)) {
                                if (deviationInfo.length() > 1024) {
                                    throw new ApplicationBizException(
                                            String.format("偏离项信息长度不能超过1024，当前长度：%d", deviationInfo.length())
                                    );
                                }
                                standardTermsDeviation.setDeviationInfo(deviationInfo);
                            }
                            standardTermsDeviation.setDeletedFlag(false);
                            purchaseContractStandardTermsDeviationMapper.insert(standardTermsDeviation);
                        }
                    }
                }
                String contractTermsIds = org.apache.commons.lang3.StringUtils.join(termsIds, ",");
                change.setContractTermsIds(contractTermsIds);
            }
        }
        purchaseContractChangeHistoryMapper.insertSelective(change);

        PurchaseContractChangeHistory history = new PurchaseContractChangeHistory();
        BeanUtils.copyProperties(purchaseContract, history);
        history.setId(null);
        history.setHistoryType(com.midea.pam.common.enums.HistoryType.HISTORY.getCode());
        history.setOriginId(contractDTO.getId());
        history.setHeaderId(headerId);
        history.setDeletedFlag(history.getDeletedFlag() == null ? Boolean.FALSE : history.getDeletedFlag());
        purchaseContractChangeHistoryMapper.insertSelective(history);
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePaymentPlanChangeHistory(List<PaymentPlanChangeHistory> paymentPlanChangeHistoryList, PurchaseContract purchaseContract,
                                             Long headerId) {
        if (ListUtils.isEmpty(paymentPlanChangeHistoryList)) {
            return;
        }
        PaymentPlanExample example = new PaymentPlanExample();
        final PaymentPlanExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andContractIdEqualTo(purchaseContract.getId());
        final List<PaymentPlan> paymentPlanList = paymentPlanMapper.selectByExample(example);

        // 变更前付款计划
        if (ListUtils.isNotEmpty(paymentPlanList)) {
            paymentPlanList.forEach(paymentPlan -> {
                final PaymentPlanChangeHistory paymentPlanChangeHistory =
                        BeanConverter.copyProperties(paymentPlan, PaymentPlanChangeHistory.class);
                paymentPlanChangeHistory.setHeaderId(headerId);
                paymentPlanChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                paymentPlanChangeHistory.setOriginId(paymentPlan.getId());
                paymentPlanChangeHistory.setDeletedFlag(Boolean.FALSE);
                paymentPlanChangeHistory.setId(null);
                paymentPlanChangeHistory.setUpdateAt(null);
                paymentPlanChangeHistory.setUpdateBy(null);
                paymentPlanChangeHistory.setAllocationPunishmentAmountWithTax(paymentPlan.getAllocationPunishmentAmountWithTax());
                paymentPlanChangeHistoryMapper.insert(paymentPlanChangeHistory);
            });
        }

        // 变更后付款计划
        paymentPlanChangeHistoryList.forEach(changeHistory -> {
            changeHistory.setOriginId(changeHistory.getId());
            //如果是新增行，生成付款计划编号
            if (null == changeHistory.getOriginId()) {
                String seqPerfix = basedataExtService.getUnitSeqPerfix(SystemContext.getUnitId());
                String paymentPlanCode = PurchaseContractHelper.generatePaymentPlanCode(seqPerfix);
                changeHistory.setCode(paymentPlanCode);
            }
            changeHistory.setHeaderId(headerId);
            changeHistory.setHistoryType(HistoryType.CHANGE.getCode());
            changeHistory.setDeletedFlag(changeHistory.getDeletedFlag() == null ? Boolean.FALSE : changeHistory.getDeletedFlag());
            changeHistory.setId(null);
            changeHistory.setUpdateAt(null);
            changeHistory.setUpdateBy(null);
            //前端传进来的罚扣金额包含历史罚扣金额，需要减去历史已经分配的罚扣金额
            BigDecimal allocationPunishmentAmountWithTax = changeHistory.getAllocationPunishmentAmountWithTax();
            BigDecimal totalPenaltyAmount = paymentApplyExtMapper.totalpenaltyAmount(changeHistory.getOriginId());
            changeHistory.setAllocationPunishmentAmountWithTax(allocationPunishmentAmountWithTax.subtract(totalPenaltyAmount));
            if (StringUtils.isNotEmpty(changeHistory.getRequirement())
                    && changeHistory.getRequirement().length() > 100
                    && !changeHistory.getDeletedFlag()) {
                throw new MipException("付款条件不能超过100字符");
            }
            paymentPlanChangeHistoryMapper.insert(changeHistory);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePenaltyChangeHistory(List<PurchaseContractPenaltyChangeHistory> penaltyChangeHistoryList, PurchaseContract purchaseContract,
                                         Long headerId) {
        PurchaseContractPenaltyExample example = new PurchaseContractPenaltyExample();
        final PurchaseContractPenaltyExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andPurchaseContractIdEqualTo(purchaseContract.getId());
        final List<PurchaseContractPenalty> penaltyList = purchaseContractPenaltyMapper.selectByExample(example);

        // 变更前罚扣信息
        if (ListUtils.isNotEmpty(penaltyList)) {
            penaltyList.forEach(penalty -> {
                final PurchaseContractPenaltyChangeHistory penaltyChangeHistory =
                        BeanConverter.copyProperties(penalty, PurchaseContractPenaltyChangeHistory.class);
                penaltyChangeHistory.setHeaderId(headerId);
                penaltyChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                penaltyChangeHistory.setOriginId(penalty.getId());
                penaltyChangeHistory.setDeletedFlag(Boolean.FALSE);
                penaltyChangeHistory.setId(null);
                purchaseContractPenaltyChangeHistoryMapper.insert(penaltyChangeHistory);
            });
        }

        // 变更后罚扣信息
        penaltyChangeHistoryList.forEach(changeHistory -> {
            changeHistory.setOriginId(changeHistory.getId());
            changeHistory.setHeaderId(headerId);
            changeHistory.setHistoryType(HistoryType.CHANGE.getCode());
            changeHistory.setDeletedFlag(changeHistory.getDeletedFlag() == null ? Boolean.FALSE : changeHistory.getDeletedFlag());
            changeHistory.setId(null);
            purchaseContractPenaltyChangeHistoryMapper.insert(changeHistory);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long baseInfoChange(PurchaseContractDTO contractDto) {
        buttonPermissionCheck(contractDto.getId());
        if (StringUtils.isNotEmpty(contractDto.getContractTerms()) && contractDto.getContractTerms().length() > 100000) {
            throw new ApplicationBizException("合同条款长度不能超过100000");
        }
        if (StringUtils.isNotEmpty(contractDto.getRemark()) && contractDto.getRemark().length() > 1000) {
            throw new ApplicationBizException("备注字段超长");
        }

        PurchaseContract purchaseContractInfo = getPurchaseContract(contractDto.getId());
        Assert.isTrue(PurchaseContractStatus.EFFECTIVE.getCode() == purchaseContractInfo.getStatus(), AssertErrorMessage.PURCHASE_CONTRACT_ILLEGAL);

        //变更流程中包含了项目号变更，校验逻辑：变更前的项目如果存在冲销状态=“未冲销”且结转单是有效的结转单，则不允许提交变更
        if (!Objects.equals(contractDto.getProjectId(), purchaseContractInfo.getProjectId())) {
            MaterialOutsourcingContractConfig contractConfig = costRatioConfigDetailExtMapper.findDetail2(purchaseContractInfo.getId(), SystemContext.getUnitId());
            if (Objects.nonNull(contractConfig) && "01".equals(contractConfig.getCostCarryForwardMethod())) { //按计划比例结转
                CarryoverBillExample carryoverBillExample = new CarryoverBillExample();
                carryoverBillExample.createCriteria().andProjectIdEqualTo(purchaseContractInfo.getProjectId())
                        .andReverseStatusEqualTo(CarryoverBillReverseStatus.CAN.getCode())
                        .andDeletedFlagEqualTo(Boolean.FALSE);
                long count = carryoverBillMapper.countByExample(carryoverBillExample);
                if (count > 0) {
                    throw new ApplicationBizException("采购合同成本已归集，需要将结转单全部冲销才可以冲销变更前项目已归集成本，冲销后可重新发起采购合同关联项目变更");
                }
            }
        }

        PurchaseContractChangeHeader header = buildHeader(PurchaseContractChangeType.BASEINFO, contractDto);

        this.saveBaseInfoChangeHistory(contractDto, purchaseContractInfo, header.getId());
        //保存附件信息
        this.saveCtcAttachmentDtos(contractDto.getAttachmentDtos(), CtcAttachmentModule.PURCHASE_CONTRACT_BASE_INFO_CHANGE.code(), header.getId());

        /*//采购合同关联预算
        this.contractBudgetMaterialChange(contractDto.getId(), contractDto.getCode(), contractDto.getProjectContractBudgetMaterials());*/

        return header.getId();
    }

    /**
     * 备份采购合同相关信息
     *
     * @param purchaseContract
     * @param headerId
     * @param baseInfoFlag
     * @param paymentPlanFlag
     * @param budgetFlag
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void backup(PurchaseContract purchaseContract, Long headerId, boolean baseInfoFlag, boolean paymentPlanFlag, boolean budgetFlag,
                       boolean detailFlag) {
        //备份基础信息
        if (baseInfoFlag) {
            PurchaseContractChangeHistoryExample changeHistoryExample = new PurchaseContractChangeHistoryExample();
            changeHistoryExample.createCriteria().andHeaderIdEqualTo(headerId);
            long infoCount = purchaseContractChangeHistoryMapper.countByExample(changeHistoryExample);
            if (infoCount == 0) {
                PurchaseContractChangeHistory history = new PurchaseContractChangeHistory();
                BeanUtils.copyProperties(purchaseContract, history);
                history.setId(null);
                history.setHistoryType(HistoryType.CHANGE.getCode());
                history.setOriginId(purchaseContract.getId());
                history.setHeaderId(headerId);
                history.setDeletedFlag(Boolean.FALSE);
                purchaseContractChangeHistoryMapper.insertSelective(history);
            }
        }
        //备份付款计划
        if (paymentPlanFlag) {
            PaymentPlanChangeHistoryExample planChangeHistoryExample = new PaymentPlanChangeHistoryExample();
            planChangeHistoryExample.createCriteria().andHeaderIdEqualTo(headerId);
            long planCount = paymentPlanChangeHistoryMapper.countByExample(planChangeHistoryExample);
            if (planCount == 0) {
                PaymentPlanExample planExample = new PaymentPlanExample();
                final PaymentPlanExample.Criteria criteria = planExample.createCriteria();
                criteria.andDeletedFlagEqualTo(Boolean.FALSE);
                criteria.andContractIdEqualTo(purchaseContract.getId());
                final List<PaymentPlan> paymentPlanList = paymentPlanMapper.selectByExample(planExample);
                if (ListUtils.isNotEmpty(paymentPlanList)) {
                    paymentPlanList.forEach(paymentPlan -> {
                        final PaymentPlanChangeHistory paymentPlanChangeHistory =
                                BeanConverter.copyProperties(paymentPlan, PaymentPlanChangeHistory.class);
                        paymentPlanChangeHistory.setHeaderId(headerId);
                        paymentPlanChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
                        paymentPlanChangeHistory.setOriginId(paymentPlan.getId());
                        paymentPlanChangeHistory.setDeletedFlag(Boolean.FALSE);
                        paymentPlanChangeHistory.setId(null);
                        paymentPlanChangeHistory.setAllocationPunishmentAmountWithTax(paymentPlan.getAllocationPunishmentAmountWithTax());
                        paymentPlanChangeHistoryMapper.insert(paymentPlanChangeHistory);
                    });
                }
            }
        }
        //备份预算信息
        if (budgetFlag) {
            PurchaseContractBudgetChangeHistoryExample budgetChangeHistoryExample = new PurchaseContractBudgetChangeHistoryExample();
            budgetChangeHistoryExample.createCriteria().andHeaderIdEqualTo(headerId);
            long budgetCount = purchaseContractBudgetChangeHistoryMapper.countByExample(budgetChangeHistoryExample);
            if (budgetCount == 0) {
                List<PurchaseContractBudgetDto> purchaseContractBudgetDtos =
                        purchaseContractBudgetService.getWbsContractBudgetByPurchaseContractId(purchaseContract.getId());
                if (CollectionUtils.isNotEmpty(purchaseContractBudgetDtos)) {
                    purchaseContractBudgetDtos.forEach(contractBudget -> {
                        final PurchaseContractBudgetChangeHistory budgetChangeHistory =
                                BeanConverter.copy(contractBudget, PurchaseContractBudgetChangeHistory.class);
                        budgetChangeHistory.setHeaderId(headerId);
                        budgetChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
                        budgetChangeHistory.setOriginId(contractBudget.getId());
                        budgetChangeHistory.setDeletedFlag(Boolean.FALSE);
                        budgetChangeHistory.setId(null);
                        purchaseContractBudgetChangeHistoryMapper.insert(budgetChangeHistory);
                    });
                }
            }
        }
        //备份合同内容
        if (detailFlag) {
            PurchaseContractDetailChangeHistoryExample detailChangeHistoryExample = new PurchaseContractDetailChangeHistoryExample();
            detailChangeHistoryExample.createCriteria().andHeaderIdEqualTo(headerId);
            long detailCount = purchaseContractDetailChangeHistoryMapper.countByExample(detailChangeHistoryExample);
            if (detailCount == 0) {
                PurchaseContractDetailExample detailExample = new PurchaseContractDetailExample();
                detailExample.createCriteria().andContractIdEqualTo(purchaseContract.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<PurchaseContractDetail> purchaseContractDetailList = purchaseContractDetailMapper.selectByExample(detailExample);
                if (CollectionUtils.isNotEmpty(purchaseContractDetailList)) {
                    purchaseContractDetailList.forEach(detail -> {
                        final PurchaseContractDetailChangeHistory detailChangeHistory =
                                BeanConverter.copy(detail, PurchaseContractDetailChangeHistory.class);
                        detailChangeHistory.setHeaderId(headerId);
                        detailChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
                        detailChangeHistory.setOriginId(detail.getId());
                        detailChangeHistory.setDeletedFlag(Boolean.FALSE);
                        detailChangeHistory.setId(null);
                        purchaseContractDetailChangeHistoryMapper.insert(detailChangeHistory);
                    });
                }
            }
        }
    }

    /**
     * 批量备份采购合同相关信息
     *
     * @param purchaseContract
     * @param headerId
     * @param contractChangeHistoryList
     * @param paymentPlanChangeHistoryList
     * @param budgetChangeHistoryList
     */
    public void backupBatch(PurchaseContract purchaseContract,
                            Long headerId,
                            Long importLot,
                            List<PurchaseContractChangeHistory> contractChangeHistoryList,
                            List<PaymentPlanChangeHistory> paymentPlanChangeHistoryList,
                            List<PurchaseContractBudgetChangeHistory> budgetChangeHistoryList) {

        //备份基础信息
        PurchaseContractChangeHistory history = new PurchaseContractChangeHistory();
        BeanUtils.copyProperties(purchaseContract, history);
        history.setId(null);
        history.setHistoryType(HistoryType.CHANGE.getCode());
        history.setOriginId(purchaseContract.getId());
        history.setHeaderId(headerId);
        history.setDeletedFlag(DeletedFlag.VALID.code());
        history.setCreateBy(importLot);
        contractChangeHistoryList.add(history);

        //备份付款计划
        PaymentPlanExample planExample = new PaymentPlanExample();
        final PaymentPlanExample.Criteria criteria = planExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andContractIdEqualTo(purchaseContract.getId());
        final List<PaymentPlan> paymentPlanList = paymentPlanMapper.selectByExample(planExample);
        if (ListUtils.isNotEmpty(paymentPlanList)) {
            paymentPlanList.forEach(paymentPlan -> {
                final PaymentPlanChangeHistory paymentPlanChangeHistory = BeanConverter.copyProperties(paymentPlan, PaymentPlanChangeHistory.class);
                paymentPlanChangeHistory.setHeaderId(headerId);
                paymentPlanChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
                paymentPlanChangeHistory.setOriginId(paymentPlan.getId());
                paymentPlanChangeHistory.setDeletedFlag(DeletedFlag.VALID.code());
                paymentPlanChangeHistory.setId(null);
                paymentPlanChangeHistory.setCreateBy(importLot);
                paymentPlanChangeHistoryList.add(paymentPlanChangeHistory);
            });
        }

        //备份预算信息
        List<PurchaseContractBudgetDto> purchaseContractBudgetDtos =
                purchaseContractBudgetService.getWbsContractBudgetByPurchaseContractId(purchaseContract.getId());
        if (CollectionUtils.isNotEmpty(purchaseContractBudgetDtos)) {
            purchaseContractBudgetDtos.forEach(contractBudget -> {
                final PurchaseContractBudgetChangeHistory budgetChangeHistory = BeanConverter.copy(contractBudget,
                        PurchaseContractBudgetChangeHistory.class);
                budgetChangeHistory.setHeaderId(headerId);
                budgetChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
                budgetChangeHistory.setOriginId(contractBudget.getId());
                budgetChangeHistory.setDeletedFlag(Boolean.FALSE);
                budgetChangeHistory.setId(null);
                budgetChangeHistory.setCreateBy(importLot);
                budgetChangeHistoryList.add(budgetChangeHistory);
            });
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long paymentPlanChange(PurchaseContractDTO contractDto) {
        buttonPermissionCheck(contractDto.getId());
        PurchaseContract purchaseContractInfo = getPurchaseContract(contractDto.getId());

        Assert.isTrue(PurchaseContractStatus.EFFECTIVE.getCode() == purchaseContractInfo.getStatus(), AssertErrorMessage.PURCHASE_CONTRACT_ILLEGAL);
        PurchaseContractChangeHeader header = buildHeader(PurchaseContractChangeType.PAYMENTPLAN, contractDto);

        final List<PaymentPlanChangeHistory> paymentPlanChangeHistoryList = contractDto.getPaymentPlanChangeHistoryList();

        punishmentAmountCheck(paymentPlanChangeHistoryList, purchaseContractInfo);


        this.savePaymentPlanChangeHistory(paymentPlanChangeHistoryList, purchaseContractInfo, header.getId());
        //保存附件信息
        this.saveCtcAttachmentDtos(contractDto.getAttachmentDtos(), CtcAttachmentModule.PAYMENT_PLAN_CHANGE.code(), header.getId());

        return header.getId();
    }

    /**
     * 检查付款记录分配金额是否与生效的罚扣含税金额相等
     *
     * @param paymentPlanChangeHistoryList
     * @param purchaseContractInfo
     */
    private void punishmentAmountCheck(List<PaymentPlanChangeHistory> paymentPlanChangeHistoryList, PurchaseContract purchaseContractInfo) {
        if (ListUtils.isEmpty(paymentPlanChangeHistoryList)) return;
        BigDecimal oldTotalAmount = paymentPlanChangeHistoryList.stream()
                .map(PaymentPlanChangeHistory::getAllocationPunishmentAmountWithTax)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        PaymentPlanExample paymentPlanExample = new PaymentPlanExample();
        paymentPlanExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andContractIdEqualTo(purchaseContractInfo.getId());
        List<PaymentPlan> paymentPlansNew = paymentPlanMapper.selectByExample(paymentPlanExample);
        if (ListUtils.isNotEmpty(paymentPlansNew)) {
            BigDecimal totalpenaltyAmount = BigDecimal.ZERO;
            BigDecimal totalAmountNew = paymentPlansNew.stream().map(PaymentPlan::getAllocationPunishmentAmountWithTax)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (PaymentPlan paymentPlan : paymentPlansNew) {
                totalpenaltyAmount = totalpenaltyAmount.add(paymentApplyExtMapper.totalpenaltyAmount(paymentPlan.getId()));
            }
            totalAmountNew = totalAmountNew.add(totalpenaltyAmount);
            if (totalAmountNew.compareTo(oldTotalAmount) != 0) {
                throw new BizException(Code.ERROR, "罚扣数据有更新，请刷新页面");
            }
        }
    }

    @Override
    public PurchaseContractContentChangeHistoryVO getPaymentPlanChangeHistory(Long headerId) {
        PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        Assert.notNull(header, AssertErrorMessage.PURCHASE_CONTRACT_CHANGE_NOT_EXIST);
        PurchaseContract purchaseContractInfo = getPurchaseContract(header.getPurchaseContractId());
        PurchaseContractContentChangeHistoryVO contractChangeHistoryVO = BeanConverter.copy(purchaseContractInfo,
                PurchaseContractContentChangeHistoryVO.class);
        packageHistoryVO(contractChangeHistoryVO);
        contractChangeHistoryVO.setHeader(header);
        //构建付款计划变更信息
        buildPaymentPlanChangeHistory(headerId, contractChangeHistoryVO);

        //获取附件信息
        contractChangeHistoryVO.setAttachmentDtos(this.findCtcAttachmentDto(headerId, CtcAttachmentModule.PAYMENT_PLAN_CHANGE.code()));

        return contractChangeHistoryVO;
    }

    private void packageHistoryVO(PurchaseContractContentChangeHistoryVO contractChangeHistoryVO) {
        //查询该合同下是否存在付款申请（作废/取消支付除外）
        BigDecimal totalTaxPayIncludedPrice = paymentApplyExtMapper.totalTaxPayIncludedPrice(contractChangeHistoryVO.getId());
        if (totalTaxPayIncludedPrice.compareTo(BigDecimal.ZERO) == 0) {
            contractChangeHistoryVO.setIsExitPaymentApply(false);
        } else {
            contractChangeHistoryVO.setIsExitPaymentApply(true);
        }

        //当采购合同存在有效结转时，不能变更关联项目
        Long countCarryBill = carryoverBillExtMapper.countCarryBillByContractId(contractChangeHistoryVO.getId());
        if (countCarryBill != null && countCarryBill > 0) {
            contractChangeHistoryVO.setIsExitPaymentApply(Boolean.TRUE);
        } else {
            contractChangeHistoryVO.setIsExitPaymentApply(Boolean.FALSE);
        }
    }

    private void buildPaymentPlanChangeHistory(Long headerId, PurchaseContractContentChangeHistoryVO contractChangeHistoryVO) {
        List<PaymentPlanChangeHistoryDto> beforeHistoryDtoList = getPaymentPlanHistoryDto(headerId, HistoryType.HISTORY);
        Map<Long, PaymentPlanChangeHistoryDto> beforeChangeHistoryMap = new HashMap<>();
        if (ListUtils.isNotEmpty(beforeHistoryDtoList)) {
            beforeHistoryDtoList.forEach(historyDto -> {
                if (historyDto.getOriginId() != null) {
                    //累计付款金额（含税）
                    BigDecimal actualAmount = paymentApplyExtMapper.actualAmount(historyDto.getOriginId());
                    historyDto.setActualAmount(actualAmount);
                    //累计申请付款金额（含税）
                    BigDecimal actualApplyAmount = paymentApplyExtMapper.actualApplyAmount(historyDto.getOriginId());
                    historyDto.setActualApplyAmount(actualApplyAmount);
                    //累计罚扣款（含税）
                    BigDecimal totalpenaltyAmount = paymentApplyExtMapper.totalpenaltyAmount(historyDto.getOriginId());
                    BigDecimal allocationPunishmentAmountWithTax = historyDto.getAllocationPunishmentAmountWithTax();
                    if (Objects.nonNull(allocationPunishmentAmountWithTax)) {
                        totalpenaltyAmount = totalpenaltyAmount.add(allocationPunishmentAmountWithTax);
                    }
                    historyDto.setTotalpenaltyAmount(totalpenaltyAmount);
                    // 里程碑
                    final ProjectMilepost projectMilepost = projectMilepostService.selectByPrimaryKey(historyDto.getMilestoneId());
                    if (projectMilepost != null) {
                        historyDto.setMilestoneName(projectMilepost.getName());
                        historyDto.setMilestoneEndTime(projectMilepost.getEndTime());
                        historyDto.setMilestoneStatus(projectMilepost.getStatus());
                    }

                    beforeChangeHistoryMap.put(historyDto.getOriginId(), historyDto);
                }
            });
        }
        List<PaymentPlanChangeHistoryDto> afterHistoryDtoList = getPaymentPlanHistoryDto(headerId, HistoryType.CHANGE);
        if (ListUtils.isNotEmpty(afterHistoryDtoList)) {
            // 付款计划变更记录
            List<PurchaseContractContentChangeHistoryVO.ChangeHistory> paymentPlanChangeHistories = new ArrayList<>();
            afterHistoryDtoList.forEach(changeDto -> {
                final Long originId = changeDto.getOriginId();
                final Boolean deletedFlag = changeDto.getDeletedFlag();
                PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistory = buildChangeHistory(originId, deletedFlag,
                        beforeChangeHistoryMap);
                BigDecimal allocationPunishmentAmountWithTax = changeDto.getAllocationPunishmentAmountWithTax();
                BigDecimal totalPenaltyAmount = Optional.ofNullable(changeDto.getTotalpenaltyAmount()).orElse(BigDecimal.ZERO);
                if (Objects.nonNull(allocationPunishmentAmountWithTax)) {
                    changeDto.setTotalpenaltyAmount(allocationPunishmentAmountWithTax.add(totalPenaltyAmount));
                }

                // 实时的在途金额
                BigDecimal transitAmount = paymentApplyExtMapper.transitAmount(originId);
                changeDto.setTransitAmount(transitAmount);
                changeHistory.setChange(changeDto);
                paymentPlanChangeHistories.add(changeHistory);
            });

            contractChangeHistoryVO.setPaymentPlanHistories(paymentPlanChangeHistories);
        }
    }

    private void buildPenaltyChangeHistory(Long headerId, PurchaseContractContentChangeHistoryVO contractChangeHistoryVO) {
        PurchaseContractPenaltyChangeHistoryExample historyExample = new PurchaseContractPenaltyChangeHistoryExample();
        historyExample.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.HISTORY.getCode());
        final List<PurchaseContractPenaltyChangeHistory> beforeChangeHistories =
                purchaseContractPenaltyChangeHistoryMapper.selectByExample(historyExample);
        //变更前记录
        Map<Long, PurchaseContractPenaltyChangeHistory> beforeChangeHistoryMap = new HashMap<>();
        if (ListUtils.isNotEmpty(beforeChangeHistories)) {
            beforeChangeHistories.forEach(historyDto -> {
                if (historyDto.getOriginId() != null) {
                    PurchaseContractPenaltyChangeHistoryDTO historyDTO = com.midea.pam.support.utils.BeanConverter.copy(historyDto, PurchaseContractPenaltyChangeHistoryDTO.class);
                    historyDTO.setSource(2);
                    beforeChangeHistoryMap.put(historyDto.getOriginId(), historyDTO);
                }
            });
        }
        PurchaseContractPenaltyChangeHistoryExample changeExample = new PurchaseContractPenaltyChangeHistoryExample();
        changeExample.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        final List<PurchaseContractPenaltyChangeHistory> afterChangeHistories =
                purchaseContractPenaltyChangeHistoryMapper.selectByExample(changeExample);
        // 付款计划变更记录
        List<PurchaseContractContentChangeHistoryVO.ChangeHistory> penaltyChangeHistories = new ArrayList<>();
        if (ListUtils.isNotEmpty(afterChangeHistories)) {
            afterChangeHistories.forEach(changeDto -> {
                final Long originId = changeDto.getOriginId();
                final Boolean deletedFlag = changeDto.getDeletedFlag();
                PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistory = buildChangeHistory(originId, deletedFlag,
                        beforeChangeHistoryMap);
                PurchaseContractPenaltyChangeHistoryDTO historyDTO = com.midea.pam.support.utils.BeanConverter.copy(changeDto, PurchaseContractPenaltyChangeHistoryDTO.class);
                historyDTO.setSource(2);
                changeHistory.setChange(historyDTO);
                penaltyChangeHistories.add(changeHistory);
            });

            //处理手动罚扣
            if (ListUtils.isNotEmpty(penaltyChangeHistories)) {
                PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistory = penaltyChangeHistories.get(0);
                PurchaseContractPenaltyChangeHistory change = (PurchaseContractPenaltyChangeHistory) changeHistory.getChange();
                Long purchaseContractId = change.getPurchaseContractId();
                List<PurchaseContractPunishmentVo> purchaseContractPunishmentVos = punishmentSelect(purchaseContractId, null);
                if (ListUtils.isNotEmpty(purchaseContractPunishmentVos)) {
                    purchaseContractPunishmentVos.removeIf(vo -> vo.getSource() != 1);
                    for (PurchaseContractPunishmentVo purchaseContractPunishmentVo : purchaseContractPunishmentVos) {
                        PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistoryNew = new PurchaseContractContentChangeHistoryVO.ChangeHistory();
                        PurchaseContractPenaltyChangeHistoryDTO penaltyChangeHistoryNew = new PurchaseContractPenaltyChangeHistoryDTO();
                        penaltyChangeHistoryNew.setId(purchaseContractPunishmentVo.getId());
                        penaltyChangeHistoryNew.setOriginId(change.getOriginId());
                        penaltyChangeHistoryNew.setHistoryType(1);
                        penaltyChangeHistoryNew.setHeaderId(headerId);
                        penaltyChangeHistoryNew.setPenaltyTypeName(purchaseContractPunishmentVo.getConfigName());
                        penaltyChangeHistoryNew.setTaxRate(purchaseContractPunishmentVo.getTaxRate());
                        penaltyChangeHistoryNew.setSource(1);
                        //不含税金额
                        BigDecimal amount = purchaseContractPunishmentVo.getAmount();
                        //税率
                        String taxRate = purchaseContractPunishmentVo.getTaxRate();
                        if (Objects.nonNull(amount) && Objects.nonNull(taxRate)) {
                            //计算税额
                            BigDecimal taxAmount = amount.multiply(new BigDecimal(taxRate)).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
                            //计算含税金额
                            BigDecimal taxAmountTotal = amount.add(taxAmount);
                            penaltyChangeHistoryNew.setTaxAmount(taxAmount);
                            penaltyChangeHistoryNew.setAmount(taxAmountTotal);
                            penaltyChangeHistoryNew.setDeletedFlag(Boolean.FALSE);
                            penaltyChangeHistoryNew.setPurchaseContractId(purchaseContractId);
                            changeHistoryNew.setChange(purchaseContractPunishmentVo);
                            changeHistoryNew.setType(ChangeType.ADD.code());
                            penaltyChangeHistories.add(changeHistoryNew);
                        }

                    }
                }
            }
            contractChangeHistoryVO.setPenaltyHistories(penaltyChangeHistories);
        } else {
            PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
            List<PurchaseContractPunishmentVo> purchaseContractPunishmentVos = punishmentSelect(header.getPurchaseContractId(), null);
            purchaseContractPunishmentVos.removeIf(vo -> vo.getSource() != 1);
            if (ListUtils.isNotEmpty(purchaseContractPunishmentVos))
                for (PurchaseContractPunishmentVo purchaseContractPunishmentVo : purchaseContractPunishmentVos) {
                    PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistoryNew = new PurchaseContractContentChangeHistoryVO.ChangeHistory();
                    changeHistoryNew.setChange(purchaseContractPunishmentVo);
                    changeHistoryNew.setType(ChangeType.ADD.code());
                    penaltyChangeHistories.add(changeHistoryNew);
                }
            contractChangeHistoryVO.setPenaltyHistories(penaltyChangeHistories);
        }
    }

    private List<PaymentPlanChangeHistoryDto> getPaymentPlanHistoryDto(Long headerId, HistoryType type) {
        PaymentPlanChangeHistoryExample example = new PaymentPlanChangeHistoryExample();
        PaymentPlanChangeHistoryExample.Criteria criteria = example.createCriteria();
        criteria.andHeaderIdEqualTo(headerId);
        if (null != type) {
            criteria.andHistoryTypeEqualTo(type.getCode());
        }
        final List<PaymentPlanChangeHistory> changeHistoryList = paymentPlanChangeHistoryMapper.selectByExample(example);
        List<PaymentPlanChangeHistoryDto> historyDtoList = BeanConverter.copy(changeHistoryList, PaymentPlanChangeHistoryDto.class);
        if (!CollectionUtils.isEmpty(historyDtoList)) {
            for (PaymentPlanChangeHistoryDto historyDto : historyDtoList) {
                packagePaymentPlanHistoryDto(historyDto);
            }
        }
        return historyDtoList;
    }

    private void packagePaymentPlanHistoryDto(PaymentPlanChangeHistoryDto historyDto) {
        // 里程碑
        final ProjectMilepost projectMilepost = projectMilepostService.selectByPrimaryKey(historyDto.getMilestoneId());
        if (projectMilepost != null) {
            historyDto.setMilestoneName(projectMilepost.getName());
            historyDto.setMilestoneEndTime(projectMilepost.getEndTime());
            historyDto.setMilestoneStatus(projectMilepost.getStatus());
        }
        Long paymentPlanId = historyDto.getOriginId();
        if (paymentPlanId != null) {
            //累计付款金额（含税）")
            BigDecimal actualAmount = paymentApplyExtMapper.actualAmount(paymentPlanId);
            historyDto.setActualAmount(actualAmount);
            //累计罚扣款（含税）")
            BigDecimal totalpenaltyAmount = paymentApplyExtMapper.totalpenaltyAmount(paymentPlanId);
            historyDto.setTotalpenaltyAmount(totalpenaltyAmount);
        }
    }

    /**
     * 构建对象
     *
     * @param originId
     * @param deletedFlag
     * @param beforeOriginMap
     * @return
     */
    private PurchaseContractContentChangeHistoryVO.ChangeHistory buildChangeHistory(Long originId, Boolean deletedFlag, Map beforeOriginMap) {
        PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistory = new PurchaseContractContentChangeHistoryVO.ChangeHistory();

        if (originId != null) {
            // 删除
            if (deletedFlag) {
                changeHistory.setType(ChangeType.DEL.code());
                // 更新
            } else {
                changeHistory.setHistory(beforeOriginMap.get(originId));
                changeHistory.setType(ChangeType.UPDATE.code());
            }
        } else {
            // 新增
            changeHistory.setType(ChangeType.ADD.code());
        }

        return changeHistory;
    }

    private void buildContractDetailChangeHistory(Long headerId, PurchaseContractContentChangeHistoryVO contractChangeHistoryVO) {
        PurchaseContractDetailChangeHistoryExample historyExample = new PurchaseContractDetailChangeHistoryExample();
        historyExample.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.HISTORY.getCode());
        final List<PurchaseContractDetailChangeHistory> beforeChangeHistories =
                purchaseContractDetailChangeHistoryMapper.selectByExample(historyExample);
        //变更前记录
        Map<Long, PurchaseContractDetailChangeHistory> beforeChangeHistoryMap = new HashMap<>();
        if (ListUtils.isNotEmpty(beforeChangeHistories)) {
            beforeChangeHistories.forEach(history -> {
                if (history.getOriginId() != null) {
                    beforeChangeHistoryMap.put(history.getOriginId(), history);
                }
            });
        }

        PurchaseContractDetailChangeHistoryExample changeExample = new PurchaseContractDetailChangeHistoryExample();
        changeExample.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        //变更后记录
        final List<PurchaseContractDetailChangeHistory> afterChangeHistories =
                purchaseContractDetailChangeHistoryMapper.selectByExample(changeExample);
        if (ListUtils.isNotEmpty(afterChangeHistories)) {
            // 采购合同行数据变更记录
            List<PurchaseContractContentChangeHistoryVO.ChangeHistory> detailChangeHistories = new ArrayList<>();

            afterChangeHistories.forEach(change -> {
                final Long originId = change.getOriginId();
                final Boolean deletedFlag = change.getDeletedFlag();
                PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistory = buildChangeHistory(originId, deletedFlag,
                        beforeChangeHistoryMap);
                changeHistory.setChange(change);
                detailChangeHistories.add(changeHistory);
            });

            contractChangeHistoryVO.setDetailHistories(detailChangeHistories);
        }
    }

    private void buildProjectContractBudgetMaterialChangeHistory(Long headerId, PurchaseContractContentChangeHistoryVO contractChangeHistoryVO) {
        ProjectContractBudgetMaterialChangeHistoryExample historyExample = new ProjectContractBudgetMaterialChangeHistoryExample();
        historyExample.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeIn(Lists.newArrayList(HistoryType.HISTORY.getCode(),
                HistoryType.CHANGE.getCode()));
        List<ProjectContractBudgetMaterialChangeHistory> histories = projectContractBudgetMaterialChangeHistoryMapper.selectByExample(historyExample);

        //变更前记录
        List<ProjectContractBudgetMaterialChangeHistory> beforeChangeHistories = CollectionUtils.isEmpty(histories) ? new ArrayList<>()
                : histories.stream().filter(e -> Objects.equals(e.getHistoryType(), HistoryType.HISTORY.getCode())).collect(Collectors.toList());
        Map<Long, ProjectContractBudgetMaterialChangeHistory> beforeChangeHistoryMap = new HashMap<>();
        beforeChangeHistories.forEach(history -> {
            if (history.getOriginId() != null) {
                beforeChangeHistoryMap.put(history.getOriginId(), history);
            }
        });

        //变更后记录
        List<ProjectContractBudgetMaterialChangeHistory> afterChangeHistories = CollectionUtils.isEmpty(histories) ? new ArrayList<>()
                : histories.stream().filter(e -> Objects.equals(e.getHistoryType(), HistoryType.CHANGE.getCode())).collect(Collectors.toList());
        // 关联预算数据变更记录
        List<PurchaseContractContentChangeHistoryVO.ChangeHistory> projectContractBudgetMaterialChangeHistories = new ArrayList<>();
        afterChangeHistories.forEach(change -> {
            PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistory = buildChangeHistory(change.getOriginId(), change.getDeletedFlag(),
                    beforeChangeHistoryMap);
            changeHistory.setChange(change);
            projectContractBudgetMaterialChangeHistories.add(changeHistory);
        });
        contractChangeHistoryVO.setProjectContractBudgetMaterialChangeHistories(projectContractBudgetMaterialChangeHistories);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long contractDetailChange(PurchaseContractDTO contractDto) {
        buttonPermissionCheck(contractDto.getId());
        checkPrice(contractDto);
        checkChangeNumber(contractDto);
        checkPurchaseNumber(contractDto);
        checkRolePrice(contractDto);
        checkFreeze(contractDto);
        PurchaseContract purchaseContractInfo = getPurchaseContract(contractDto.getId());
        checkPunishmentAmount(contractDto);

        Assert.isTrue(PurchaseContractStatus.EFFECTIVE.getCode() == purchaseContractInfo.getStatus(), AssertErrorMessage.PURCHASE_CONTRACT_ILLEGAL);
        //按采购合同类型配置是否启用应付入账金额超合同金额校验
        paymentInvoiceService.checkAmountInvoiceOverContract(contractDto.getId(), contractDto.getAmount(), null);

        PurchaseContractChangeHeader header = buildHeader(PurchaseContractChangeType.CONTRACTDETAIL, contractDto);
        final List<PurchaseContractDetailChangeHistory> detailChangeHistoryList = contractDto.getPurchaseContractDetailChangeHistoryList();
        final List<PaymentPlanChangeHistory> paymentPlanChangeHistoryList = contractDto.getPaymentPlanChangeHistoryList();
        final List<PurchaseContractPenaltyChangeHistory> penaltyChangeHistoryList = contractDto.getPurchaseContractPenaltyChangeHistoryList();
        final List<ProjectContractBudgetMaterial> projectContractBudgetMaterials = contractDto.getProjectContractBudgetMaterials();
        final List<PurchaseContractBudgetChangeHistoryDto> budgetChangeHistoryList = contractDto.getPurchaseContractBudgetChangeHistoryList();
        //保存合同金额信息/合同附件变更
        this.saveBaseInfoChangeHistory(contractDto, purchaseContractInfo, header.getId());
        //保存合同内容变更
        this.saveContractDetailChangeHistory(detailChangeHistoryList, purchaseContractInfo, header.getId());
        // 保存关联预算 TODO 可能会优化成对比
        //待添加的数据,前端可能会传递其他项目的数据
        //原因是合同内容变更的时候,切换到其他页面,再次切换回来的时候,导致数据重写异常,后端做一次这样的过滤
        if (CollectionUtils.isNotEmpty(projectContractBudgetMaterials)) {
            List<ProjectContractBudgetMaterial> list = projectContractBudgetMaterials.stream()
                    .filter(x -> !Objects.equals(contractDto.getProjectId(), x.getProjectId()))
                    .collect(Collectors.toList());
            logger.info("前端上送的项目ID:{},上送的信息为:{}", contractDto.getProjectId(), JSON.toJSONString(projectContractBudgetMaterials));
            if (CollectionUtils.isNotEmpty(list)) {
                logger.info("项目内容变更后,关联预算的项目编号不匹配");
                throw new MipException("关联预算和变更后的项目不匹配，请刷新页面重新编辑");
            }
        }

        List<ProjectContractBudgetMaterial> budgetMaterialList =
                this.contractBudgetMaterialChange(purchaseContractInfo.getId(), purchaseContractInfo.getCode(), projectContractBudgetMaterials);
        // 保存关联预算变更
        this.saveProjectContractBudgetMaterialChangeHistory(budgetMaterialList, purchaseContractInfo, header.getId());
        //保存付款计划变更
        this.savePaymentPlanChangeHistory(paymentPlanChangeHistoryList, purchaseContractInfo, header.getId());
        //保存罚扣信息变更
        this.savePenaltyChangeHistory(penaltyChangeHistoryList, purchaseContractInfo, header.getId());
        //保存附件信息
        this.saveCtcAttachmentDtos(contractDto.getAttachmentDtos(), CtcAttachmentModule.PURCHASE_CONTRACT_DETAIL_CHANGE.code(), header.getId());
        //保存WBS预算列表
        this.saveWbsBudgetChangeHistory(budgetChangeHistoryList, purchaseContractInfo, header.getId());

        return header.getId();
    }

    private void checkFreeze(PurchaseContractDTO contractDto) {
        List<PurchaseContractBudgetChangeHistoryDto> purchaseContractBudgetChangeHistoryList = contractDto.getPurchaseContractBudgetChangeHistoryList();
        List<String> freezeErrorMessages = new ArrayList<>();
        for (PurchaseContractBudgetChangeHistoryDto purchaseContractBudgetChangeHistoryDto : purchaseContractBudgetChangeHistoryList) {
            Long purchaseRequirementId = purchaseContractBudgetChangeHistoryDto.getPurchaseRequirementId();
            PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(purchaseRequirementId);
            if (requirement != null) {
                if (requirement.getFreezeFlag().equals(FreezeFlag.FREEZE.getCode())) {
                    freezeErrorMessages.add(String.format("[%s]的[%s]冻结状态为已冻结,不可进行变更", requirement.getRequirementCode(), requirement.getPamCode()));
                }
            }
        }
        if (!freezeErrorMessages.isEmpty()) {
            throw new BizException(Code.ERROR, String.join(";", freezeErrorMessages));
        }
    }

    /**
     * 检查提交的罚扣总金额与实时查询的罚扣总金额是否一致
     *
     * @param contractDto
     */
    private void checkPunishmentAmount(PurchaseContractDTO contractDto) {
        //罚扣金额
        BigDecimal penaltyAmount = contractDto.getPenaltyAmount();
        if (Objects.nonNull(penaltyAmount)) {
            //查询当前合同罚扣汇总金额
            List<PunishmentAmountDTO> punishmentAmountDTOS = purchaseContractPunishmentExtMapper.getEffectivePunishmentAmountByContractId(Lists.newArrayList(contractDto.getId()));
            if (ListUtils.isNotEmpty(punishmentAmountDTOS)) {
                PunishmentAmountDTO punishmentAmountDTO = punishmentAmountDTOS.get(0);
                //获取不含税金额汇总
                BigDecimal notTaxAmount = punishmentAmountDTO.getNotTaxAmount();
                if (Objects.nonNull(notTaxAmount) && penaltyAmount.compareTo(notTaxAmount) != 0) {
                    throw new BizException(Code.ERROR, "罚扣数据有更新，请刷新页面");
                }
            }
        }
    }

    private void checkRolePrice(PurchaseContractDTO contractDto) {
        if (!"点工采购".equals(contractDto.getTypeName())) {
            return;
        }
        List<Long> roleIds = hroWorkingHourBillItemExtMapper.selectBillRoleByContractId(contractDto.getId());
        List<PurchaseContractBudgetChangeHistoryDto> changeList = contractDto.getPurchaseContractBudgetChangeHistoryList();
        PurchaseContractBudgetExample budgetExample = new PurchaseContractBudgetExample();
        budgetExample.createCriteria().andPurchaseContractIdEqualTo(contractDto.getId());
        Map<Long, BigDecimal> priceMap = purchaseContractBudgetMapper.selectByExample(budgetExample)
                .stream().collect(Collectors.toMap(PurchaseContractBudget::getId, PurchaseContractBudget::getPrice));
        changeList.stream().filter(e -> e.getId() != null && roleIds.contains(e.getMaterialId())).forEach(e -> {
            if (priceMap.get(e.getId()).compareTo(e.getPrice()) != 0) {
                throw new BizException(Code.ERROR, "角色单价已作过对账，不允许变更");
            }
        });
    }

    /**
     * 相同角色不允许价格不同
     */
    private void checkPrice(PurchaseContractDTO contractDto) {
        if (!"点工采购".equals(contractDto.getTypeName())) {
            return;
        }
        List<PurchaseContractBudgetChangeHistoryDto> changeList = contractDto.getPurchaseContractBudgetChangeHistoryList();
        Map<String, List<PurchaseContractBudgetChangeHistoryDto>> changeGroup = changeList.stream()
                .collect(Collectors.groupingBy(PurchaseContractBudgetChangeHistoryDto::getRoleName));
        changeGroup.forEach(((roleName, list) ->
                list.forEach(e -> {
                    if (e.getPrice().compareTo(list.get(0).getPrice()) != 0) {
                        throw new BizException(Code.ERROR, String.format("角色：%s 不允许存在多个“小时单价（不含税）-原币”", roleName));
                    }
                })
        ));
    }

    /**
     * 签订数量不能大于未签订采购合同数量
     */
    private void checkChangeNumber(PurchaseContractDTO contractDto) {
        List<PurchaseContractBudgetChangeHistoryDto> changeList = contractDto.getPurchaseContractBudgetChangeHistoryList();
        if (ListUtils.isEmpty(changeList)) {
            return;
        }
        List<Long> ids = changeList.stream().map(PurchaseContractBudgetChangeHistoryDto::getId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        PurchaseContractBudgetExample example = new PurchaseContractBudgetExample();
        example.createCriteria().andIdIn(ids);
        Map<Long, PurchaseContractBudget> oldBudgetMap = purchaseContractBudgetMapper.selectByExample(example)
                .stream().collect(Collectors.toMap(PurchaseContractBudget::getId, e -> e));
        Map<Long, PurchaseMaterialRequirementDto> outRequirementInfo =
                purchaseMaterialRequirementService.getOutRequirementInfo(contractDto.getProjectId(), contractDto.getHeaderId())
                        .stream().collect(Collectors.toMap(PurchaseMaterialRequirementDto::getId, e -> e));
        changeList.forEach(e -> {
            PurchaseContractBudget oldBudget = oldBudgetMap.get(e.getId());
            BigDecimal oldQuantity = BigDecimal.ZERO;
            if (oldBudget != null) {
                oldQuantity = oldBudget.getNumber();
            }
            PurchaseMaterialRequirementDto purchaseMaterialRequirementDto = outRequirementInfo.get(e.getPurchaseRequirementId());
            if (purchaseMaterialRequirementDto != null) {
                BigDecimal releasedQuantity = purchaseMaterialRequirementDto.getReleasedQuantity();
                if (oldQuantity != null) {
                    releasedQuantity = releasedQuantity.subtract(oldQuantity);
                }
                if (e.getNeedTotal().compareTo(e.getNumber().add(releasedQuantity)) < 0) {
                    throw new BizException(Code.ERROR, "签订数量不能大于未签订采购合同数量");
                }
            }
        });
    }

    /**
     * 校验签订数量不得少于已导入工时数量
     */
    private void checkPurchaseNumber(PurchaseContractDTO contractDto) {
        if (!"点工采购".equals(contractDto.getTypeName())) {
            return;
        }
        List<HroWorkingHourItemDto> hroWorkingHourItemDtoList = hroWorkingHourExtMapper.summaryWorkingHourToRole(contractDto.getId());
        hroWorkingHourService.mergeChangingWorkingHour(hroWorkingHourItemDtoList);
        Map<String, BigDecimal> hroWorkingHourMap = hroWorkingHourItemDtoList.stream().collect(Collectors.toMap(
                HroWorkingHourItemDto::getRoleName,
                HroWorkingHourItem::getWorkingHour, BigDecimal::add));

        if (!hroWorkingHourMap.isEmpty()) {
            Map<String, BigDecimal> budgetMap = contractDto.getPurchaseContractBudgetChangeHistoryList()
                    .stream().collect(Collectors.toMap(PurchaseContractBudgetChangeHistoryDto::getRoleName,
                            PurchaseContractBudgetChangeHistoryDto::getNumber, BigDecimal::add));
            List<String> errMsgs = new ArrayList<>();
            budgetMap.forEach((k, v) -> {
                BigDecimal workingHour = hroWorkingHourMap.get(k);
                if (workingHour != null && workingHour.compareTo(v) > 0) {
                    errMsgs.add(String.format("角色【%s】的签订数量不得少于已导入工时数量【%s】", k, workingHour));
                }
            });
            if (!errMsgs.isEmpty()) {
                throw new BizException(Code.ERROR, String.join(",", errMsgs));
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveWbsBudgetChangeHistory(List<PurchaseContractBudgetChangeHistoryDto> budgetChangeHistoryList, PurchaseContract purchaseContract,
                                           Long headerId) {
        //变更前关联预算
        List<PurchaseContractBudgetDto> purchaseContractBudgetDtos =
                purchaseContractBudgetService.getWbsContractBudgetByPurchaseContractId(purchaseContract.getId());
        if (CollectionUtils.isNotEmpty(purchaseContractBudgetDtos)) {
            purchaseContractBudgetDtos.forEach(contractBudget -> {
                final PurchaseContractBudgetChangeHistory budgetChangeHistory = BeanConverter.copy(contractBudget,
                        PurchaseContractBudgetChangeHistory.class);
                budgetChangeHistory.setHeaderId(headerId);
                budgetChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                budgetChangeHistory.setOriginId(contractBudget.getId());
                budgetChangeHistory.setDeletedFlag(Boolean.FALSE);
                budgetChangeHistory.setId(null);
                purchaseContractBudgetChangeHistoryMapper.insert(budgetChangeHistory);
            });
        }

        //变更后关联预算
        budgetChangeHistoryList.forEach(changeHistory -> {
            changeHistory.setHeaderId(headerId);
            changeHistory.setHistoryType(HistoryType.CHANGE.getCode());
            changeHistory.setOriginId(changeHistory.getId());
            changeHistory.setPurchaseContractId(purchaseContract.getId()); //新增时需要设置
            changeHistory.setDeletedFlag(changeHistory.getDeletedFlag() == null ? Boolean.FALSE : changeHistory.getDeletedFlag());
            changeHistory.setId(null);
            changeHistory.setNumber(Optional.ofNullable(changeHistory.getNumber()).orElse(BigDecimal.ZERO));
            changeHistory.setPrice(Optional.ofNullable(changeHistory.getPrice()).orElse(BigDecimal.ZERO));
            purchaseContractBudgetChangeHistoryMapper.insert(changeHistory);
        });
    }


    @Override
    public PurchaseContractContentChangeHistoryVO getContractDetailChangeHistory(Long headerId) {
        PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        Assert.notNull(header, AssertErrorMessage.PURCHASE_CONTRACT_CHANGE_NOT_EXIST);
        PurchaseContract purchaseContractInfo = getPurchaseContract(header.getPurchaseContractId());
        Project project = projectMapper.selectByPrimaryKey(purchaseContractInfo.getProjectId());
        Asserts.notEmpty(project, ErrorCode.CTC_PROJECT_NOT_FIND);

        PurchaseContractChangeHistoryExample changeHistoryExample = new PurchaseContractChangeHistoryExample();
        changeHistoryExample.createCriteria().andHeaderIdEqualTo(header.getId())
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractChangeHistory> purchaseContractChangeHistories =
                purchaseContractChangeHistoryMapper.selectByExample(changeHistoryExample);
        if (CollectionUtils.isEmpty(purchaseContractChangeHistories)) return null;

        PurchaseContractContentChangeHistoryVO contractChangeHistoryVO = BeanConverter.copy(purchaseContractChangeHistories.get(0),
                PurchaseContractContentChangeHistoryVO.class);
        contractChangeHistoryVO.setWbsEnabled(project.getWbsEnabled());
        contractChangeHistoryVO.setConversionRate(purchaseContractInfo.getConversionRate());
        if (contractChangeHistoryVO.getVendorId() != null) {
            final VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(contractChangeHistoryVO.getVendorId());
            contractChangeHistoryVO.setUniteCreditCode(vendorSiteBankDto.getUniteCreditCode());
            contractChangeHistoryVO.setVendorTypeLookupCode(vendorSiteBankDto.getVendorTypeLookupCode());
            contractChangeHistoryVO.setPvsStatus(vendorSiteBankDto.getPvsStatus());
        }

        packageHistoryVO(contractChangeHistoryVO);
        contractChangeHistoryVO.setHeader(header);
        //BUG2023072555715 返回格式与销售合同统一
        contractChangeHistoryVO.setLegalUrl(header.getLegalUrl());
        //构建合同基本信息变更
        buildBaseInfoChangeHistory(headerId, contractChangeHistoryVO);
        //构建合同内容变更信息
        buildContractDetailChangeHistory(headerId, contractChangeHistoryVO);
        // 构建关联预算变更信息
        /*buildProjectContractBudgetMaterialChangeHistory(headerId, contractChangeHistoryVO);*/
        //构建付款计划变更信息
        buildPaymentPlanChangeHistory(headerId, contractChangeHistoryVO);
        //构建罚扣变更信息
        buildPenaltyChangeHistory(headerId, contractChangeHistoryVO);
        //构建WBS预算变更信息
        buildPurchaseContractBudgetChangeHistory(headerId, contractChangeHistoryVO, purchaseContractInfo.getTypeName());

        //获取附件信息
        contractChangeHistoryVO.setAttachmentDtos(this.findCtcAttachmentDto(headerId, CtcAttachmentModule.PURCHASE_CONTRACT_DETAIL_CHANGE.code()));

        // 关联预算
        buildProjectContractBudgetMaterialVos(headerId, contractChangeHistoryVO, purchaseContractInfo);

        //如果是变更中的合同 传给前端需要传变更后的项目id(适用于 改或不改项目的所有情况)
        if (Objects.equals(purchaseContractInfo.getStatus(), PurchaseContractStatus.CHANGEING.getCode()) && CollectionUtils.isNotEmpty(contractChangeHistoryVO.getProjectContractBudgetMaterialVos())) {
            contractChangeHistoryVO.setProjectId(contractChangeHistoryVO.getProjectContractBudgetMaterialVos().get(0).getProjectId());
        }
        //印章无法回显问题-----此处还需统一 TODO chenchong
        contractChangeHistoryVO.setSealAdministratorChangeIds(contractChangeHistoryVO.getSealAdminAccountIds());
        //变更详细，需要显示印章管理员名称
        String sealAdminName = getSealAdminName(contractChangeHistoryVO.getSealAdminAccountIds(), purchaseContractInfo.getOuId());
        if (StringUtils.isNotEmpty(sealAdminName)) {
            contractChangeHistoryVO.setSealAdminAccountNames(sealAdminName);
        }

        //罚扣信息查询
       /* List<PurchaseContractPunishmentVo> purchaseContractPunishmentVos = punishmentSelect(header.getPurchaseContractId(), null);
        if(ListUtils.isNotEmpty(purchaseContractPunishmentVos)){
            purchaseContractPunishmentVos.removeIf(vo->vo.getSource()!=1);
            contractChangeHistoryVO.setPunishmentList(purchaseContractPunishmentVos);
        }*/

        return contractChangeHistoryVO;
    }

    public String getSealAdminName(String sealAdminAccountIds, Long ouId) {
        // 所属业务实体
        if (ouId != null && StringUtils.isNotEmpty(sealAdminAccountIds)) {
            final OperatingUnit operatingUnit = CacheDataUtils.findOuById(ouId);
            if (operatingUnit != null) {
                Map<String, Object> param = new HashMap<>();
                param.put("idStr", sealAdminAccountIds);
                param.put("sealName", operatingUnit.getOperatingUnitName());
                final String url = StringUtils.buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "sealAdministrator/getSealAdministratorDTOs", param);
                ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
                DataResponse<List<SealAdministratorDTO>> response = JSON.parseObject(responseEntity.getBody(),
                        new TypeReference<DataResponse<List<SealAdministratorDTO>>>() {
                        });
                if (Objects.isNull(response)) {
                    return null;
                }
                List<SealAdministratorDTO> sealAdministratorDTOList = response.getData();
                return CollectionUtils.isNotEmpty(sealAdministratorDTOList) ? sealAdministratorDTOList.get(0).getSealAdminName() : "";
            }
        }
        return null;
    }

    public void buildProjectContractBudgetMaterialVos(Long headerId,
                                                      PurchaseContractContentChangeHistoryVO contractChangeHistoryVO,
                                                      PurchaseContract purchaseContractInfo) {
        //先查询项目合同关联预算记录变更表headerid对应数据
        ProjectContractBudgetMaterialChangeHistoryExample historyExample = new ProjectContractBudgetMaterialChangeHistoryExample();
        ProjectContractBudgetMaterialChangeHistoryExample.Criteria historyCriteria = historyExample.createCriteria();
        //因为查历史变更记录 所以要查更新状态的历史记录给前端
        historyCriteria.andHeaderIdEqualTo(headerId).andDeletedFlagEqualTo(false).andStatusEqualTo(true);
        List<ProjectContractBudgetMaterialChangeHistory> historieMaterial =
                projectContractBudgetMaterialChangeHistoryMapper.selectByExample(historyExample);

        ProjectContractBudgetMaterialExample example = new ProjectContractBudgetMaterialExample();
        ProjectContractBudgetMaterialExample.Criteria criteria = example.createCriteria();
        if (CollectionUtils.isNotEmpty(historieMaterial)) {
            criteria.andIdIn(historieMaterial.stream().map(ProjectContractBudgetMaterialChangeHistory::getOriginId).collect(Collectors.toList()));
        } else {
            criteria.andProjectIdEqualTo(purchaseContractInfo.getProjectId())
                    .andCodeEqualTo(purchaseContractInfo.getCode())
                    .andStatusEqualTo(Objects.equals(purchaseContractInfo.getStatus(), PurchaseContractStatus.CHANGEING.getCode()))
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        }
        List<ProjectContractBudgetMaterial> materialList = budgetMaterialMapper.selectByExample(example);
        List<ProjectContractBudgetMaterialVo> materialVos = BeanConverter.copy(materialList, ProjectContractBudgetMaterialVo.class);
        for (ProjectContractBudgetMaterialVo vo : materialVos) {
            ProjectBudgetMaterial material = projectBudgetMaterialMapper.selectByPrimaryKey(vo.getMaterialId());
            // 关联金额总额
            BigDecimal priceTotal = BigDecimal.ZERO;
            if (material != null) {
                priceTotal = purchaseContractBudgetMaterialService.getBudgetMaterialTotalAmount(material.getPriceTotal(),
                        material.getParentId()).stripTrailingZeros();
            }

            if (priceTotal.scale() > 0) {
                priceTotal = priceTotal.setScale(2, RoundingMode.HALF_UP);
            }
            vo.setPriceTotal(priceTotal);
            //预算占用金额（不含税）-本位币
            BigDecimal conversionRate = Optional.ofNullable(purchaseContractInfo.getConversionRate()).orElse(BigDecimal.ONE);
            vo.setExcludingTaxTotalBudgetAmount(BigDecimalUtils.multiplyAndScale(purchaseContractInfo.getExcludingTaxAmount(), conversionRate));
        }
        contractChangeHistoryVO.setProjectContractBudgetMaterialVos(materialVos);
    }

    private void buildPurchaseContractBudgetChangeHistory(Long headerId,
                                                          PurchaseContractContentChangeHistoryVO contractChangeHistoryVO,
                                                          String typeName) {
        PurchaseContractBudgetChangeHistoryExample historyExample = new PurchaseContractBudgetChangeHistoryExample();
        historyExample.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.HISTORY.getCode());
        final List<PurchaseContractBudgetChangeHistory> beforeChangeHistories =
                purchaseContractBudgetChangeHistoryMapper.selectByExample(historyExample);
        //变更前记录
        Map<Long, PurchaseContractBudgetChangeHistory> beforeChangeHistoryMap = new HashMap<>();
        if (ListUtils.isNotEmpty(beforeChangeHistories)) {
            beforeChangeHistories.forEach(history -> {
                if (history.getOriginId() != null) {
                    beforeChangeHistoryMap.put(history.getOriginId(), history);
                }
            });
        }

        PurchaseContractBudgetChangeHistoryExample changeExample = new PurchaseContractBudgetChangeHistoryExample();
        changeExample.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        //变更后记录
        final List<PurchaseContractBudgetChangeHistory> afterChangeHistories =
                purchaseContractBudgetChangeHistoryMapper.selectByExample(changeExample);
        if (ListUtils.isNotEmpty(afterChangeHistories)) {
            List<PurchaseContractBudgetChangeHistoryDto> afterChangeHistorieDtos = BeanConverter.copy(afterChangeHistories,
                    PurchaseContractBudgetChangeHistoryDto.class);
            fillRoleInfo(afterChangeHistorieDtos, typeName);
            // 采购合同行数据变更记录
            List<PurchaseContractContentChangeHistoryVO.ChangeHistory> purchaseContractBudgetChangeHistories = new ArrayList<>();

            afterChangeHistorieDtos.forEach(change -> {
                final Long originId = change.getOriginId();
                final Boolean deletedFlag = change.getDeletedFlag();
                PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistory = buildChangeHistory(originId, deletedFlag,
                        beforeChangeHistoryMap);
                changeHistory.setChange(change);
                purchaseContractBudgetChangeHistories.add(changeHistory);
            });
            contractChangeHistoryVO.setPurchaseContractBudgetChangeHistories(purchaseContractBudgetChangeHistories);
        }
    }

    private void fillRoleInfo(List<PurchaseContractBudgetChangeHistoryDto> afterChangeHistories, String typeName) {
        if ("点工采购".equals(typeName)) {
            List<Long> purchaseRequirementIds = afterChangeHistories.stream().map(PurchaseContractBudgetChangeHistory::getPurchaseRequirementId)
                    .collect(Collectors.toList());
            Map<Long, HroRequirementItemDto> hroRequirementItemMap = hroRequirementItemExtMapper
                    .selectByRequirementIds(purchaseRequirementIds)
                    .stream().collect(Collectors.toMap(HroRequirementItemDto::getPurchaseMaterialRequirementId, e -> e));
            afterChangeHistories.forEach(e -> {
                HroRequirementItemDto hroRequirementItemDto = hroRequirementItemMap.get(e.getPurchaseRequirementId());
                if (hroRequirementItemDto != null) {
                    e.setRoleName(hroRequirementItemDto.getRoleName());
                    e.setStartDate(hroRequirementItemDto.getStartDate());
                    e.setEndDate(hroRequirementItemDto.getEndDate());
                }
            });
        }
    }

    private void buildBaseInfoChangeHistory(Long headerId, PurchaseContractContentChangeHistoryVO contractChangeHistoryVO) {
        PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistory = new PurchaseContractContentChangeHistoryVO.ChangeHistory();
        PurchaseContractChangeHistoryExample example = new PurchaseContractChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headerId);
        List<PurchaseContractChangeHistory> contractChangeHistoryList = purchaseContractChangeHistoryMapper.selectByExampleWithBLOBs(example);
        if (!CollectionUtils.isEmpty(contractChangeHistoryList)) {
            for (PurchaseContractChangeHistory contractChangeHistory : contractChangeHistoryList) {
                PurchaseContractChangeHistoryDTO contractChangeHistoryDTO = BeanConverter.copy(contractChangeHistory,
                        PurchaseContractChangeHistoryDTO.class);
                packageContractDto(contractChangeHistoryDTO);
                if (HistoryType.HISTORY.getCode().equals(contractChangeHistory.getHistoryType())) {
                    changeHistory.setHistory(contractChangeHistoryDTO);
                } else {
                    changeHistory.setChange(contractChangeHistoryDTO);
                }
            }
        }
        changeHistory.setType(ChangeType.UPDATE.code());//基本信息变更只能更新
        contractChangeHistoryVO.setPurchaseContractHistory(changeHistory);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveContractDetailChangeHistory(List<PurchaseContractDetailChangeHistory> detailChangeHistoryList,
                                                PurchaseContract purchaseContract, Long headerId) {
        PurchaseContractDetailExample example = new PurchaseContractDetailExample();
        final PurchaseContractDetailExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andContractIdEqualTo(purchaseContract.getId());
        final List<PurchaseContractDetail> detailList = purchaseContractDetailMapper.selectByExample(example);

        // 变更前付款计划
        if (ListUtils.isNotEmpty(detailList)) {
            detailList.forEach(purchaseContractDetail -> {
                final PurchaseContractDetailChangeHistory detailChangeHistory =
                        BeanConverter.copyProperties(purchaseContractDetail, PurchaseContractDetailChangeHistory.class);
                detailChangeHistory.setHeaderId(headerId);
                detailChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                detailChangeHistory.setOriginId(purchaseContractDetail.getId());
                detailChangeHistory.setDeletedFlag(Boolean.FALSE);
                detailChangeHistory.setId(null);
                purchaseContractDetailChangeHistoryMapper.insert(detailChangeHistory);
            });
        }

        // 变更后付款计划
        detailChangeHistoryList.forEach(changeHistory -> {
            changeHistory.setOriginId(changeHistory.getId());
            changeHistory.setHeaderId(headerId);
            changeHistory.setHistoryType(HistoryType.CHANGE.getCode());
            changeHistory.setDeletedFlag(changeHistory.getDeletedFlag() == null ? Boolean.FALSE : changeHistory.getDeletedFlag());
            changeHistory.setId(null);
            purchaseContractDetailChangeHistoryMapper.insert(changeHistory);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveProjectContractBudgetMaterialChangeHistory(List<ProjectContractBudgetMaterial> projectContractBudgetMaterials,
                                                               PurchaseContract purchaseContract, Long headerId) {
        // 变更前关联预算
        String code = purchaseContract.getCode();
        Long projectId = purchaseContract.getProjectId();
        if (Objects.nonNull(projectId) && StringUtils.isNotEmpty(code)) {
            ProjectContractBudgetMaterialExample example = new ProjectContractBudgetMaterialExample();
            ProjectContractBudgetMaterialExample.Criteria criteria = example.createCriteria();
            criteria.andProjectIdEqualTo(projectId).andCodeEqualTo(code).andDeletedFlagEqualTo(false);
            if (purchaseContract.getStatus().equals(PurchaseContractStatus.CHANGEING.getCode())) {
                criteria.andStatusEqualTo(true);
            } else {
                criteria.andStatusEqualTo(false);
            }
            List<ProjectContractBudgetMaterial> budgetMaterialList = budgetMaterialMapper.selectByExample(example);
            budgetMaterialList.forEach(budgetMaterial -> {
                ProjectContractBudgetMaterialChangeHistory materialChangeHistory =
                        BeanConverter.copyProperties(budgetMaterial, ProjectContractBudgetMaterialChangeHistory.class);
                materialChangeHistory.setHeaderId(headerId);
                materialChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                materialChangeHistory.setOriginId(budgetMaterial.getId());
                materialChangeHistory.setDeletedFlag(false);
                materialChangeHistory.setId(null);
                projectContractBudgetMaterialChangeHistoryMapper.insert(materialChangeHistory);
            });
        }

        // 变更后关联预算
        projectContractBudgetMaterials.forEach(budgetMaterial -> {
            ProjectContractBudgetMaterialChangeHistory materialChangeHistory =
                    BeanConverter.copyProperties(budgetMaterial, ProjectContractBudgetMaterialChangeHistory.class);
            materialChangeHistory.setOriginId(budgetMaterial.getId());
            materialChangeHistory.setHeaderId(headerId);
            materialChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
            materialChangeHistory.setId(null);
            projectContractBudgetMaterialChangeHistoryMapper.insert(materialChangeHistory);
        });
    }

    @Override
    public PurchaseContractChangeHistoryVO getBaseChangeHistory(Long headerId) {
        PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        Assert.notNull(header, AssertErrorMessage.PURCHASE_CONTRACT_CHANGE_NOT_EXIST);
        PurchaseContractChangeHistoryVO contractChangeHistoryVO = BeanConverter.copy(header, PurchaseContractChangeHistoryVO.class);

        PurchaseContractChangeHistoryExample example = new PurchaseContractChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headerId);
        List<PurchaseContractChangeHistory> contractChangeHistoryList = purchaseContractChangeHistoryMapper.selectByExampleWithBLOBs(example);
        if (!CollectionUtils.isEmpty(contractChangeHistoryList)) {
            for (PurchaseContractChangeHistory contractChangeHistory : contractChangeHistoryList) {
                PurchaseContractChangeHistoryDTO contractChangeHistoryDTO = BeanConverter.copy(contractChangeHistory,
                        PurchaseContractChangeHistoryDTO.class);
                packageContractDto(contractChangeHistoryDTO);
                if (HistoryType.HISTORY.getCode().equals(contractChangeHistory.getHistoryType())) {
                    contractChangeHistoryVO.setHistory(contractChangeHistoryDTO);
                } else {
                    contractChangeHistoryVO.setChange(contractChangeHistoryDTO);
                }
            }
        }
        contractChangeHistoryVO.setType(ChangeType.UPDATE.code());//基本信息变更只能更新
        //获取附件信息
        contractChangeHistoryVO.setAttachmentDtos(this.findCtcAttachmentDto(headerId, CtcAttachmentModule.PURCHASE_CONTRACT_BASE_INFO_CHANGE.code()));
        //变更详细，需要显示印章管理员名称
        PurchaseContract purchaseContractInfo = getPurchaseContract(header.getPurchaseContractId());
        Assert.notNull(purchaseContractInfo, AssertErrorMessage.PURCHASE_CONTRACT_NOT_EXIST);
        String sealAdminName = getSealAdminName(contractChangeHistoryVO.getSealAdminAccountIds(), purchaseContractInfo.getOuId());
        if (StringUtils.isNotEmpty(sealAdminName)) {
            contractChangeHistoryVO.setSealAdminAccountNames(sealAdminName);
        }
        return contractChangeHistoryVO;
    }

    private void packageContractDto(PurchaseContractChangeHistoryDTO contractChangeHistoryDTO) {
        // 项目
        if (contractChangeHistoryDTO.getProjectId() != null) {
            final Project project = projectBusinessService.selectByPrimaryKey(contractChangeHistoryDTO.getProjectId());
            if (project != null) {
                contractChangeHistoryDTO.setProjectName(project.getName());
                contractChangeHistoryDTO.setProjectCode(project.getCode());
            }
        }

        //业务实体名称
        if (null != contractChangeHistoryDTO.getOuId()) {
            OperatingUnit ou = CacheDataUtils.findOuById(contractChangeHistoryDTO.getOuId());
            contractChangeHistoryDTO.setOuName(ou.getOperatingUnitName());
        }

        // 项目经理
        if (contractChangeHistoryDTO.getManager() != null) {
            final UserInfo userInfo = CacheDataUtils.findUserById(contractChangeHistoryDTO.getManager());
            if (userInfo != null) {
                contractChangeHistoryDTO.setManagerName(userInfo.getName());
            }
        }

        // 采购跟进人
        if (contractChangeHistoryDTO.getPurchasingFollower() != null) {
            final UserInfo userInfo = CacheDataUtils.findUserById(contractChangeHistoryDTO.getPurchasingFollower());
            if (userInfo != null) {
                contractChangeHistoryDTO.setPurchasingFollowerName(userInfo.getName());
            }
        }
        // 供应商信息补充
        if (contractChangeHistoryDTO.getVendorId() != null) {
            final VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(contractChangeHistoryDTO.getVendorId());
            contractChangeHistoryDTO.setVendorTypeLookupCode(vendorSiteBankDto.getVendorTypeLookupCode());
            contractChangeHistoryDTO.setPvsStatus(vendorSiteBankDto.getPvsStatus());
        }

        //标准条款信息
        if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(contractChangeHistoryDTO.getContractTermsFlg())) {
            String contractTermsIds = contractChangeHistoryDTO.getContractTermsIds();
            List<PurchaseContractStandardTermsDto> standardTermsDtoList = new ArrayList<>();
            if (StringUtils.isNotEmpty(contractTermsIds)) {
                List<Long> standardTermsIdList = Arrays.stream(contractTermsIds.split(","))
                        .map(String::trim)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(standardTermsIdList)) {
                    for (Long standardTermsId : standardTermsIdList) {
                        PurchaseContractStandardTerms purchaseContractStandardTerms = purchaseContractStandardTermsMapper.selectByPrimaryKey(standardTermsId);
                        if (purchaseContractStandardTerms != null) {
                            PurchaseContractStandardTermsDto standardTermsDto = new PurchaseContractStandardTermsDto();
                            BeanConverter.copy(purchaseContractStandardTerms, standardTermsDto);

                            PurchaseContractStandardTermsDeviationExample example = new PurchaseContractStandardTermsDeviationExample();
                            PurchaseContractStandardTermsDeviationExample.Criteria criteria = example.createCriteria();
                            criteria.andDeletedFlagEqualTo(false).andAssociationPurchaseTermsIdEqualTo(standardTermsId);
                            List<PurchaseContractStandardTermsDeviation> deviations = purchaseContractStandardTermsDeviationMapper.selectByExample(example);
                            if (CollectionUtils.isNotEmpty(deviations)) {
                                standardTermsDto.setStandardTermsDeviationList(deviations);
                            }
                            standardTermsDtoList.add(standardTermsDto);
                        }
                    }
                    contractChangeHistoryDTO.setStandardTermsDtoList(standardTermsDtoList);
                }
            }
        }
    }

    /**
     * 保存附件信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCtcAttachmentDtos(List<CtcAttachmentDto> attachmentDtos, Integer module, Long moduleId) {
        if (ListUtil.isPresent(attachmentDtos)) {
            for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                if (attachmentDto.getId() == null
                        && !(Objects.nonNull(attachmentDto.getDeletedFlag()) && Objects.equals(Boolean.TRUE, attachmentDto.getDeletedFlag()))) {
                    attachmentDto.setModule(module);
                    attachmentDto.setModuleId(moduleId);
                    attachmentDto.setStatus(AttachmentStatus.CHECKING.code());
                }
                ctcAttachmentService.save(attachmentDto, SystemContext.getUserId());
            }
        }
    }

    /**
     * 构建附件信息
     */
    private List<CtcAttachmentDto> findCtcAttachmentDto(Long moduleId, Integer module) {
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(module);
        attachmentQuery.setModuleId(moduleId);
        List<CtcAttachmentDto> ctcAttachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
        if (CollectionUtils.isEmpty(ctcAttachmentDtos)) {
            return Collections.emptyList();
        }
        return ctcAttachmentDtos;
    }

    /**
     * 采购合同预算材料变更
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ProjectContractBudgetMaterial> contractBudgetMaterialChange(Long purchaseContractId, String code,
                                                                            List<ProjectContractBudgetMaterial> list) {
        //首次变更、二次变革....
        if (ListUtils.isNotEmpty(list)) {
            ProjectContractBudgetMaterialExample example = new ProjectContractBudgetMaterialExample();
            example.createCriteria().andCodeEqualTo(code).andPurchaseContractIdEqualTo(purchaseContractId)
                    .andDeletedFlagEqualTo(Boolean.FALSE).andStatusEqualTo(Boolean.TRUE);
            List<ProjectContractBudgetMaterial> materialList = budgetMaterialMapper.selectByExample(example);

            List<Long> longList = CollectionsUtil
                    .ofNullable(materialList)
                    .orElseGet(null)
                    .stream()
                    .map(ProjectContractBudgetMaterial::getId).collect(Collectors.toList());
            //删除变更的材料
            if (longList.size() != 0) {
                budgetMaterialExtMapper.deleteBatch(longList);
            }

            //添加
            List<ProjectContractBudgetMaterial> budgetMaterials = list.stream().map(m -> {
                ProjectBudgetMaterial material = projectBudgetMaterialMapper.selectByPrimaryKey(m.getMaterialId());
                return Builder.of(ProjectContractBudgetMaterial::new)
                        .with(ProjectContractBudgetMaterial::setStatus, Boolean.TRUE)
                        .with(ProjectContractBudgetMaterial::setMaterialId, m.getMaterialId())
                        .with(ProjectContractBudgetMaterial::setCode, code)
                        .with(ProjectContractBudgetMaterial::setDeletedFlag, Boolean.FALSE)
                        .with(ProjectContractBudgetMaterial::setMoney, m.getMoney())
                        .with(ProjectContractBudgetMaterial::setExcludingTaxAmount, m.getExcludingTaxAmount())
                        .with(ProjectContractBudgetMaterial::setName, material.getName())
                        .with(ProjectContractBudgetMaterial::setNumber, material.getNumber())
                        .with(ProjectContractBudgetMaterial::setProjectId, m.getProjectId())
                        .with(ProjectContractBudgetMaterial::setUnit, material.getUnit())
                        .with(ProjectContractBudgetMaterial::setPurchaseContractId, purchaseContractId)
                        .build();
            }).collect(Collectors.toList());

            if (budgetMaterials.size() != 0) {
                budgetMaterialExtMapper.insertBatch(budgetMaterials);
            }
            return budgetMaterials;
        }
        return new ArrayList<>();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean changeStatus(PurchaseContractStatus status, Long purchaseContractId) {
        PurchaseContract purchaseContract = new PurchaseContract();
        purchaseContract.setId(purchaseContractId);
        purchaseContract.setStatus(status.getCode());
        return purchaseContractMapper.updateByPrimaryKeySelective(purchaseContract) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long changeHeadStatus(Integer status, Long headerId) {
        PurchaseContractChangeHeader header = new PurchaseContractChangeHeader();
        header.setId(headerId);
        header.setStatus(status);
        purchaseContractChangeHeaderMapper.updateByPrimaryKeySelective(header);
        header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        if (status.equals(PurchaseContractChangeHeaderStatus.CHECKING.code())) {
            changeStatus(PurchaseContractStatus.CHANGEING, header.getPurchaseContractId());
        } else if (status.equals(PurchaseContractChangeHeaderStatus.RETURN.code())
                || status.equals(PurchaseContractChangeHeaderStatus.REJECT.code())
                || status.equals(PurchaseContractChangeHeaderStatus.PASS.code())) {
            changeStatus(PurchaseContractStatus.EFFECTIVE, header.getPurchaseContractId());
        }
        return headerId;
    }

    public PaymentInvoiceDto queryReversePaymentInvoiceInfo(Long paymentInvoiceId) {
        PaymentInvoiceDto dto = BeanConverter.copy(paymentInvoiceMapper.selectByPrimaryKey(paymentInvoiceId), PaymentInvoiceDto.class);
        if (Objects.isNull(dto)) {
            return null;
        }
        //金额取反
        dto.setTotalInvoiceIncludedPrice(Optional.ofNullable(dto.getTotalInvoiceIncludedPrice()).map(BigDecimal::negate).orElse(BigDecimal.ZERO));
        dto.setTaxAmount(Optional.ofNullable(dto.getTaxAmount()).map(BigDecimal::negate).orElse(BigDecimal.ZERO));

        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(dto.getPurchaseContractId());
        if (Objects.nonNull(purchaseContract)) {
            dto.setPurchaseContractName(purchaseContract.getName());
            dto.setVendorId(purchaseContract.getVendorId());
            dto.setVendorCode(purchaseContract.getVendorCode());
            dto.setVendorName(purchaseContract.getVendorName());
            dto.setVendorSiteCode(purchaseContract.getVendorSiteCode());
            dto.setConversionRate(purchaseContract.getConversionRate());
            dto.setCurrency(purchaseContract.getCurrency());
            dto.setProjectId(purchaseContract.getProjectId());
        }
        return dto;
    }

    /**
     * 归集冲销
     *
     * @param headerId
     */
    @Transactional(rollbackFor = Exception.class)
    public void collectionReverse(Long headerId) {
        //查询变更记录
        PurchaseContractChangeHistoryExample example = new PurchaseContractChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headerId);
        List<PurchaseContractChangeHistory> changeList = purchaseContractChangeHistoryMapper.selectByExample(example);

        // 同时满足以下条件，变更流程通过后冲销变更前项目成本，并且清空发票归集状态，变更后项目根据归集逻辑重新归集
        // 1）采购合同基本信息变更流程中包含了项目号变更
        // 2）采购合同对应的成本结转方式“按发票入账结转”
        // 3）采购合同在material_outsource_cost_detail表中，按照合同号合计金额不等于0（如果采购合同在material_outsource_cost_detail表中不存在有效数据，不属于适用对象）
        if (changeList != null && changeList.size() == 2 && !Objects.equals(changeList.get(0).getProjectId(), changeList.get(1).getProjectId())) {
            Long purchaseContractId = changeList.get(0).getOriginId();
            String purchaseContractCode = changeList.get(0).getCode();
            Long unitId = CacheDataUtils.getTopUnitIdByOuId(changeList.get(0).getOuId());
            //归集明细列表
            MaterialOutsourceCostDetailExample materialOutsourceCostDetailExample = new MaterialOutsourceCostDetailExample();
            materialOutsourceCostDetailExample.createCriteria().andPurchaseContractCodeEqualTo(purchaseContractCode).andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
            List<MaterialOutsourceCostDetail> materialOutsourceCostDetailList = materialOutsourceCostDetailMapper.selectByExample(materialOutsourceCostDetailExample);
            BigDecimal reduceSum = materialOutsourceCostDetailList.stream().map(s -> s.getInvoiceAmount()).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
            //物料外包合同配置
            MaterialOutsourcingContractConfig contractConfig = costRatioConfigDetailExtMapper.findDetail2(purchaseContractId, unitId);
            if (reduceSum.compareTo(BigDecimal.ZERO) != 0 && Objects.nonNull(contractConfig) && "02".equals(contractConfig.getCostCarryForwardMethod())) { //按发票入账结转
                Map<Long, List<MaterialOutsourceCostDetail>> groupMap = materialOutsourceCostDetailList.stream().collect(Collectors.groupingBy(MaterialOutsourceCostDetail::getPaymentInvoiceId));
                for (Long paymentInvoiceId : groupMap.keySet()) {
                    List<MaterialOutsourceCostDetail> list = groupMap.get(paymentInvoiceId);
                    BigDecimal reduce = list.stream().map(s -> s.getInvoiceAmount()).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
                    if (reduce.compareTo(BigDecimal.ZERO) != 0) {
                        PaymentInvoiceDto i = queryReversePaymentInvoiceInfo(paymentInvoiceId);
                        if (i == null) {
                            continue;
                        }
                        Project project = projectMapper.selectByPrimaryKey(i.getProjectId());

                        //插入归集表
                        CostCollection costCollection = costCollectionService.saveMaterialCostOut(null, project, i);
                        // 汇率
                        BigDecimal conversionRate = Optional.ofNullable(i.getConversionRate()).orElse(BigDecimal.ONE);
                        // 发票含税金额
                        BigDecimal totalInvoiceIncludedPrice = Optional.ofNullable(i.getTotalInvoiceIncludedPrice()).orElse(BigDecimal.ZERO);
                        // 发票税额
                        BigDecimal taxAmount = Optional.ofNullable(i.getTaxAmount()).orElse(BigDecimal.ZERO);
                        // 本位币金额
                        BigDecimal localCurrencyAmount = totalInvoiceIncludedPrice.subtract(taxAmount).multiply(conversionRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                        //插明细表
                        MaterialOutsourceCostDetail build = Builder.of(MaterialOutsourceCostDetail::new)
                                .with(MaterialOutsourceCostDetail::setCostCollectionId, costCollection.getId())
                                .with(MaterialOutsourceCostDetail::setApInvoiceCode, i.getApInvoiceCode())
                                .with(MaterialOutsourceCostDetail::setPurchaseContractCode, i.getPurchaseContractCode())
                                .with(MaterialOutsourceCostDetail::setPurchaseContractName, i.getPurchaseContractName())
                                .with(MaterialOutsourceCostDetail::setInvoiceAmount, totalInvoiceIncludedPrice)
                                .with(MaterialOutsourceCostDetail::setTaxAmount, taxAmount)
                                .with(MaterialOutsourceCostDetail::setCreateAt, new Date())
                                .with(MaterialOutsourceCostDetail::setDeletedFlag, 0)
                                .with(MaterialOutsourceCostDetail::setVendorId, i.getVendorId())
                                .with(MaterialOutsourceCostDetail::setVendorCode, i.getVendorCode())
                                .with(MaterialOutsourceCostDetail::setVendorSiteCode, i.getVendorSiteCode())
                                .with(MaterialOutsourceCostDetail::setVendorName, i.getVendorName())
                                .with(MaterialOutsourceCostDetail::setInvoiceCurrency, i.getCurrency())
                                .with(MaterialOutsourceCostDetail::setLocalCurrency, "CNY") // todo 发票外包方案暂未实现，默认CNY
                                .with(MaterialOutsourceCostDetail::setLocalCurrencyAmount, localCurrencyAmount)
                                .with(MaterialOutsourceCostDetail::setConversionRate, conversionRate)
                                .with(MaterialOutsourceCostDetail::setPaymentInvoiceId, i.getId())
                                .with(MaterialOutsourceCostDetail::setInvoiceDate, i.getAuditDate() != null ? i.getAuditDate() : i.getInvoiceDate())
                                .with(MaterialOutsourceCostDetail::setType, MaterialOutsourceCostDetailType.PAYMENT_INVOICE.getCode())
                                .build();
                        materialOutsourceCostDetailMapper.insertSelective(build);

                        //将本次变更的采购合同应付发票已归集的标识置为：未归集
                        PaymentInvoice paymentInvoice = new PaymentInvoice();
                        paymentInvoice.setId(paymentInvoiceId);
                        paymentInvoice.setCollectionStatus(0);
                        paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long baseInfoChangePass(Long headerId) {
        Guard.notNull(headerId, "采购合同基础信息变更审批通过审批回调 formInstanceId为空");
        logger.info("采购合同基础信息变更审批通过审批回调 formInstanceId:{}", headerId);

        final PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        Guard.notNull(header, String.format("采购合同基础信息变更审批通过审批回调 formInstanceId：%s对应合同不存在", headerId));

        //防止重复回调
        if (Objects.equals(header.getStatus(), PurchaseContractChangeHeaderStatus.PASS.code())) {
            return null;
        }
        //归集冲销
        collectionReverse(headerId);

        PurchaseContractChangeHistoryExample example = new PurchaseContractChangeHistoryExample();
        example.createCriteria().andHistoryTypeEqualTo(HistoryType.CHANGE.getCode()).andHeaderIdEqualTo(headerId);
        List<PurchaseContractChangeHistory> contractChangeHistoryList =
                purchaseContractChangeHistoryMapper.selectByExampleWithBLOBs(example);
        if (PublicUtil.isNotEmpty(contractChangeHistoryList)) {
            PurchaseContractChangeHistory contractChangeHistory = contractChangeHistoryList.get(0);
            // 获取原始合同信息，保留原始的创建时间和创建人
            PurchaseContract originalContract = purchaseContractMapper.selectByPrimaryKey(contractChangeHistory.getOriginId());
            Date originalCreateAt = originalContract.getCreateAt();
            Long originalCreateBy = originalContract.getCreateBy();
            PurchaseContract purchaseContract = BeanConverter.copy(contractChangeHistory, PurchaseContract.class);
            purchaseContract.setId(contractChangeHistory.getOriginId());
            // 保留原始合同的创建时间和创建人，避免被变更历史的时间覆盖
            purchaseContract.setCreateAt(originalCreateAt);
            purchaseContract.setCreateBy(originalCreateBy);
            purchaseContractMapper.updateByPrimaryKeySelective(purchaseContract);
            final Project project = projectMapper.selectByPrimaryKey(purchaseContract.getProjectId());
            //供应商变更需同步更新采购发票对应的供应商信息（供应商ID，供应商名称、供应商编号、供应商地点）
            PaymentInvoiceDetailExample invoiceDetailExample = new PaymentInvoiceDetailExample();
            invoiceDetailExample.createCriteria().andPurchaseContractIdEqualTo(header.getPurchaseContractId());
            //采购发票列表
            List<PaymentInvoiceDetail> invoiceDetailList = paymentInvoiceDetailMapper.selectByExample(invoiceDetailExample);
            if (!CollectionUtils.isEmpty(invoiceDetailList)) {
                for (PaymentInvoiceDetail paymentInvoiceDetail : invoiceDetailList) {
                    paymentInvoiceDetail.setVendorId(contractChangeHistory.getVendorId());
                    paymentInvoiceDetail.setVendorCode(contractChangeHistory.getVendorCode());
                    paymentInvoiceDetail.setVendorName(contractChangeHistory.getVendorName());
                    paymentInvoiceDetail.setVendorSiteCode(contractChangeHistory.getVendorSiteCode());
                    paymentInvoiceDetail.setPurchaseContractName(purchaseContract.getName());
                    paymentInvoiceDetail.setProjectId(purchaseContract.getProjectId());
                    paymentInvoiceDetail.setProjectCode(project.getCode());
                    paymentInvoiceDetail.setProjectName(project.getName());
                    paymentInvoiceDetailMapper.updateByPrimaryKeySelective(paymentInvoiceDetail);
                }
            }
            //更新付款申请的冗余字段信息
            PaymentApplyExample applyExample = new PaymentApplyExample();
            applyExample.createCriteria().andPurchaseContractIdEqualTo(header.getPurchaseContractId());
            List<PaymentApply> paymentApplyList = paymentApplyMapper.selectByExample(applyExample);
            if (!CollectionUtils.isEmpty(paymentApplyList)) {
                for (PaymentApply paymentApply : paymentApplyList) {
                    paymentApply.setPurchaseContractName(purchaseContract.getName());
                    paymentApply.setProjectId(purchaseContract.getProjectId());
                    paymentApply.setProjectCode(project.getCode());
                    paymentApply.setProjectName(project.getName());
                    paymentApplyMapper.updateByPrimaryKey(paymentApply);
                }
            }
        }
        //更新采购合同历史变更头状态：通过
        changeHeadStatus(PurchaseContractChangeHeaderStatus.PASS.code(), headerId);

        //生成版本历史版本号
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(header.getPurchaseContractId());
        String versionCode = this.generateVersionCode(header, purchaseContract.getCode());
        PurchaseContractChangeHeader updateHeader = new PurchaseContractChangeHeader();
        updateHeader.setId(headerId);
        updateHeader.setVersionCode(versionCode);
        purchaseContractChangeHeaderMapper.updateByPrimaryKeySelective(updateHeader);

        //备份付款计划/预算/合同内容
        this.backup(purchaseContract, header.getId(), false, true, true, true);

        return headerId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long contractDetailChangePass(Long headerId) {
        Guard.notNull(headerId, "采购合同内容变更审批通过审批回调 formInstanceId为空");

        final PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        Guard.notNull(header, String.format("采购合同内容变更审批通过审批回调 formInstanceId：%s对应合同不存在", headerId));

        //防止重复回调
        if (Objects.equals(header.getStatus(), PurchaseContractChangeHeaderStatus.PASS.code())) {
            return null;
        }

        //业务处理
        handleContractDetailChangePass(header, null, "");

        //生成版本历史版本号
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(header.getPurchaseContractId());
        String versionCode = this.generateVersionCode(header, purchaseContract.getCode());
        PurchaseContractChangeHeader updateHeader = new PurchaseContractChangeHeader();
        updateHeader.setId(headerId);
        updateHeader.setVersionCode(versionCode);
        purchaseContractChangeHeaderMapper.updateByPrimaryKeySelective(updateHeader);

        return headerId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleContractDetailChangePass(PurchaseContractChangeHeader header, List<GlegalContractFileInfoDto.FileInfos> fileInfos,
                                               String contractStatus) {
        Long headerId = header.getId();
        Long purchaseContractId = header.getPurchaseContractId();
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);
        Project project = projectMapper.selectByPrimaryKey(purchaseContract.getProjectId());

        // 更新合同基本信息（金额/合同附件）
        handleBaseInfoChange(headerId, fileInfos);
        // 更新合同内容
        handleContractDetailChange(headerId);
        // 更新付款计划
        handlePaymentPlanChange(headerId);
        // 更新罚扣信息
        handlePenaltyChange(headerId);
        // 更新关联材料
        handleBudgetMaterial(purchaseContractId);
        // 更新关联预算
        handleProjectContractBudgetMaterialChangeHistory(headerId);

        if (project.getWbsEnabled() != null && project.getWbsEnabled()) {
            //更新WBS预算
            handlePurchaseContractBudgetChangeHistory(header);
        }

        //更新采购合同历史变更头状态：通过
        if (fileInfos == null && StringUtils.isEmpty(contractStatus)) {
            changeHeadStatus(PurchaseContractChangeHeaderStatus.PASS.code(), headerId);
        } else {
            glegalPurchaseContractWorkflowCallbackService.changeHeadStatus(PurchaseContractChangeHeaderStatus.PASS.code(), headerId, fileInfos,
                    contractStatus);
        }

        //查询合同新增时关联的采购物料，如果已采购量=总需求量(即未采购量=0)，采购需求状态改为：已关闭；否则改为：待采购
        purchaseContractBudgetService.updateRequirementStatus(purchaseContract.getId());

        if (project.getWbsEnabled() != null && project.getWbsEnabled()) {
            //更新进度执行百分比
            PurchaseContractProgressVo progressVo = this.calculateExecuteAmountTotal(purchaseContract.getId());
            PurchaseContract updateEntity = new PurchaseContract();
            updateEntity.setId(purchaseContract.getId());
            updateEntity.setExecuteContractPercentTotal(progressVo.getExecuteContractPercentTotal());
            purchaseContractMapper.updateByPrimaryKeySelective(updateEntity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long paymentPlanChangePass(Long headerId) {
        logger.info("采购合同付款计划变更审批通过审批回调 formInstanceId:{}", headerId);
        Guard.notNull(headerId, "采购合同付款计划变更审批通过审批回调 formInstanceId为空");

        final PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        Guard.notNull(header, String.format("采购合同付款计划变更审批通过审批回调 formInstanceId：%s对应合同不存在", headerId));

        //防止重复回调
        if (Objects.equals(header.getStatus(), PurchaseContractChangeHeaderStatus.PASS.code())) {
            return null;
        }

        //更新付款计划
        handlePaymentPlanChange(headerId);

        //更新采购合同历史变更头状态：通过
        changeHeadStatus(PurchaseContractChangeHeaderStatus.PASS.code(), headerId);

        //生成版本历史版本号
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(header.getPurchaseContractId());
        String versionCode = this.generateVersionCode(header, purchaseContract.getCode());
        PurchaseContractChangeHeader updateHeader = new PurchaseContractChangeHeader();
        updateHeader.setId(headerId);
        updateHeader.setVersionCode(versionCode);
        purchaseContractChangeHeaderMapper.updateByPrimaryKeySelective(updateHeader);

        //备份合同基本信息/预算/合同内容
        this.backup(purchaseContract, header.getId(), true, false, true, true);

        return headerId;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleBaseInfoChange(Long headerId, List<GlegalContractFileInfoDto.FileInfos> fileInfos) {
        PurchaseContractChangeHistoryExample example = new PurchaseContractChangeHistoryExample();
        example.createCriteria().andHistoryTypeEqualTo(HistoryType.CHANGE.getCode()).andHeaderIdEqualTo(headerId);
        List<PurchaseContractChangeHistory> contractChangeHistoryList = purchaseContractChangeHistoryMapper.selectByExampleWithBLOBs(example);
        if (PublicUtil.isNotEmpty(contractChangeHistoryList)) {
            PurchaseContractChangeHistory contractChangeHistory = contractChangeHistoryList.get(0);
            // 获取原始合同信息，保留原始的创建时间和创建人
            PurchaseContract originalContract = purchaseContractMapper.selectByPrimaryKey(contractChangeHistory.getOriginId());
            Date originalCreateAt = originalContract.getCreateAt();
            Long originalCreateBy = originalContract.getCreateBy();
            PurchaseContract purchaseContract = BeanConverter.copy(contractChangeHistory, PurchaseContract.class);
            purchaseContract.setId(contractChangeHistory.getOriginId());

            // 保留原始合同的创建时间和创建人，避免被变更历史的时间覆盖
            purchaseContract.setCreateAt(originalCreateAt);
            purchaseContract.setCreateBy(originalCreateBy);
            if (fileInfos != null) {
                //List<String> ids = fileInfos.stream().map(GlegalContractFileInfoDto.FileInfos::getId).collect(Collectors.toList());
                List<String> ids =
                        fileInfos.stream().filter(i -> !i.getDeleteFlag() && ("StampArchive".equals(i.getFileSign()) || "ChangeProtocol".equals(i.getFileSign()))).map(GlegalContractFileInfoDto.FileInfos::getId).collect(Collectors.toList());
                String ifUploadChangeFile = String.join(",", ids);
                purchaseContract.setIfUploadChangeFile(ifUploadChangeFile);
            }
            purchaseContractMapper.updateByPrimaryKeySelective(purchaseContract);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleContractDetailChange(Long headerId) {
        PurchaseContractDetailChangeHistoryExample example = new PurchaseContractDetailChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        final List<PurchaseContractDetailChangeHistory> detailChangeHistoryList = purchaseContractDetailChangeHistoryMapper.selectByExample(example);

        if (!CollectionUtils.isEmpty(detailChangeHistoryList)) {
            for (PurchaseContractDetailChangeHistory detailChangeHistory : detailChangeHistoryList) {
                final Long originId = detailChangeHistory.getOriginId();
                if (null == originId) {
                    //新增
                    final PurchaseContractDetail detail = BeanConverter.copyProperties(detailChangeHistory, PurchaseContractDetail.class);
                    detail.setId(null);
                    purchaseContractDetailMapper.insert(detail);
                } else {
                    //更新
                    final PurchaseContractDetail detail = BeanConverter.copyProperties(detailChangeHistory, PurchaseContractDetail.class);
                    detail.setId(originId);
                    purchaseContractDetailMapper.updateByPrimaryKeySelective(detail);
                }

            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleProjectContractBudgetMaterialChangeHistory(Long headerId) {
        ProjectContractBudgetMaterialChangeHistoryExample example = new ProjectContractBudgetMaterialChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        final List<ProjectContractBudgetMaterialChangeHistory> detailChangeHistoryList =
                projectContractBudgetMaterialChangeHistoryMapper.selectByExample(example);
        for (ProjectContractBudgetMaterialChangeHistory detailChangeHistory : detailChangeHistoryList) {
            final ProjectContractBudgetMaterial detail = BeanConverter.copyProperties(detailChangeHistory, ProjectContractBudgetMaterial.class);
            final Long originId = detailChangeHistory.getOriginId();
            if (Objects.isNull(originId)) {
                //新增
                detail.setId(null);
                detail.setStatus(false);
                projectContractBudgetMaterialMapper.insert(detail);
            } else {
                //更新
                detail.setId(originId);
                detail.setStatus(false);
                projectContractBudgetMaterialMapper.updateByPrimaryKeySelective(detail);
            }

        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handlePenaltyChange(Long headerId) {
        PurchaseContractPenaltyChangeHistoryExample example = new PurchaseContractPenaltyChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        final List<PurchaseContractPenaltyChangeHistory> penaltyChangeHistoryList =
                purchaseContractPenaltyChangeHistoryMapper.selectByExample(example);

        if (!CollectionUtils.isEmpty(penaltyChangeHistoryList)) {
            for (PurchaseContractPenaltyChangeHistory penaltyChangeHistory : penaltyChangeHistoryList) {
                final Long originId = penaltyChangeHistory.getOriginId();
                if (null == originId) {
                    //新增
                    final PurchaseContractPenalty penalty = BeanConverter.copyProperties(penaltyChangeHistory, PurchaseContractPenalty.class);
                    penalty.setId(null);
                    purchaseContractPenaltyMapper.insert(penalty);
                } else {
                    //更新
                    final PurchaseContractPenalty penalty = BeanConverter.copyProperties(penaltyChangeHistory, PurchaseContractPenalty.class);
                    penalty.setId(originId);
                    purchaseContractPenaltyMapper.updateByPrimaryKeySelective(penalty);
                }

            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleBudgetMaterial(Long purchaseContractId) {
        //查询使用的材料
        ProjectContractBudgetMaterialExample example = new ProjectContractBudgetMaterialExample();
        example.createCriteria()
                .andPurchaseContractIdEqualTo(purchaseContractId)
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andStatusEqualTo(Boolean.FALSE);
        List<ProjectContractBudgetMaterial> materialList = budgetMaterialMapper.selectByExample(example);
        //删除原先关联材料
        if (ListUtils.isNotEmpty(materialList)) {
            List<Long> ids = materialList.stream()
                    .map(ProjectContractBudgetMaterial::getId).collect(Collectors.toList());
            budgetMaterialExtMapper.updateBatchDeletedFlag(ids, Boolean.TRUE);
        }

        //变更材料通过处理
        ProjectContractBudgetMaterialExample materialExample = new ProjectContractBudgetMaterialExample();
        materialExample.createCriteria().andPurchaseContractIdEqualTo(purchaseContractId)
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andStatusEqualTo(Boolean.TRUE);
        final List<ProjectContractBudgetMaterial> list = budgetMaterialMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(list)) {
            List<Long> ids = list.stream()
                    .map(ProjectContractBudgetMaterial::getId).collect(Collectors.toList());
            budgetMaterialExtMapper.updateBatchStatus(ids, Boolean.FALSE);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void handlePaymentPlanChange(Long headerId) {
        PaymentPlanChangeHistoryExample example = new PaymentPlanChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(headerId).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        final List<PaymentPlanChangeHistory> changeHistoryList = paymentPlanChangeHistoryMapper.selectByExample(example);

        if (!CollectionUtils.isEmpty(changeHistoryList)) {
            for (PaymentPlanChangeHistory paymentPlanChangeHistory : changeHistoryList) {
                final Long originId = paymentPlanChangeHistory.getOriginId();
                if (null == originId) {
                    //新增
                    final PaymentPlan paymentPlan = BeanConverter.copyProperties(paymentPlanChangeHistory, PaymentPlan.class);
                    paymentPlan.setId(null);
                    paymentPlanMapper.insert(paymentPlan);
                    logger.info("采购合同内容或付款计划变更,创建付款计划信息:{}", JSON.toJSONString(paymentPlan));
                } else {
                    //更新
                    final PaymentPlan paymentPlan = BeanConverter.copyProperties(paymentPlanChangeHistory, PaymentPlan.class);
                    paymentPlan.setId(originId);
                    // 合同内容变更/付款计划变更，不回写付款状态和实际付款金额  BUG2023081172440
                    paymentPlan.setStatus(null);
                    paymentPlan.setActualAmount(null);
                    // 合同内容变更/付款计划变更，付款计划编码被篡改  BUG2023102549837
                    paymentPlan.setCode(null);
                    paymentPlan.setCreateBy(null);
                    paymentPlan.setCreateAt(null);
                    paymentPlanMapper.updateByPrimaryKeySelective(paymentPlan);
                    // 更新付款计划状态
                    paymentApplyService.updatePaymentPlanStatus(originId);

                    // 更新付款申请的冗余字段信息
                    PaymentApplyExample applyExample = new PaymentApplyExample();
                    applyExample.createCriteria().andPaymentPlanIdEqualTo(paymentPlan.getId());
                    List<PaymentApply> paymentApplyList = paymentApplyMapper.selectByExample(applyExample);
                    if (!CollectionUtils.isEmpty(paymentApplyList)) {
                        for (PaymentApply paymentApply : paymentApplyList) {
                            paymentApply.setPaymentPlanNum(paymentPlan.getNum());
                            paymentApply.setTaxPlanIncludedPrice(paymentPlan.getAmount());
                            paymentApply.setPaymentPlanDate(paymentPlan.getDate());
                            paymentApplyMapper.updateByPrimaryKey(paymentApply);
                        }
                    }
                    logger.info("采购合同内容或付款计划变更,更新付款计划信息:{},更新付款申请信息:{}", JSON.toJSONString(paymentPlan), JSON.toJSONString(paymentApplyList));
                }

            }
        }
    }

    @Transactional
    public void handlePurchaseContractBudgetChangeHistory(PurchaseContractChangeHeader header) {
        PurchaseContractBudgetChangeHistoryExample example = new PurchaseContractBudgetChangeHistoryExample();
        example.createCriteria().andHeaderIdEqualTo(header.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        final List<PurchaseContractBudgetChangeHistory> budgetChangeHistoryList = purchaseContractBudgetChangeHistoryMapper.selectByExample(example);

        List<PurchaseContractBudgetChangeHistory> changeList = budgetChangeHistoryList.stream().filter(s -> Objects.equals(s.getHistoryType(),
                HistoryType.CHANGE.getCode())).collect(Collectors.toList());
        Map<Long, PurchaseContractBudgetChangeHistory> historyMap = budgetChangeHistoryList.stream()
                .filter(s -> Objects.equals(s.getHistoryType(), HistoryType.HISTORY.getCode()))
                .collect(Collectors.toMap(PurchaseContractBudgetChangeHistory::getOriginId, Function.identity()));

        if (!CollectionUtils.isEmpty(changeList)) {
            for (PurchaseContractBudgetChangeHistory budgetChangeHistory : changeList) {
                final Long originId = budgetChangeHistory.getOriginId();
                Date contractApprovalTime = new Date();
                // 查询关联项目需求的最新发布日期
                List<Long> requirementIdList =
                        changeList.stream().map(PurchaseContractBudgetChangeHistory::getPurchaseRequirementId).collect(Collectors.toList());
                Map<Long, Date> publishTimeMap = purchaseMaterialRequirementExtMapper.selectLatestPublishTime(requirementIdList).stream()
                        .collect(Collectors.toMap(PurchaseMaterialReleaseDetailDto::getPurchaseRequirementId,
                                PurchaseMaterialReleaseDetailDto::getPublishTime));
                /** 采购合同预算新增 或者 单价or数量有变更，更新需求发布时间/合同创建时间/合同审批通过时间 BUG2023031739954 **/
                if (null == originId) {
                    //新增
                    final PurchaseContractBudget budget = BeanConverter.copyProperties(budgetChangeHistory, PurchaseContractBudget.class);
                    budget.setId(null);
                    budget.setPublishTime(publishTimeMap.get(budget.getPurchaseRequirementId()));
                    budget.setPurchaseContractCreateAt(header.getCreateAt());
                    budget.setContractApprovalTime(contractApprovalTime);
                    budget.setContractChangeHeaderId(header.getId());
                    purchaseContractBudgetMapper.insert(budget);
                } else {
                    //更新
                    final PurchaseContractBudget budget = BeanConverter.copyProperties(budgetChangeHistory, PurchaseContractBudget.class);
                    PurchaseContractBudgetChangeHistory history = historyMap.get(budgetChangeHistory.getOriginId());
                    if (history != null && !(BigDecimalUtils.isEquals(history.getPrice(), budget.getPrice()) && BigDecimalUtils.isEquals(history.getNumber(), budget.getNumber()))) {
                        budget.setPublishTime(publishTimeMap.get(budget.getPurchaseRequirementId()));
                        budget.setPurchaseContractCreateAt(header.getCreateAt());
                        budget.setContractApprovalTime(contractApprovalTime);
                        budget.setContractChangeHeaderId(header.getId());
                    }
                    budget.setId(originId);
                    purchaseContractBudgetMapper.updateByPrimaryKeySelective(budget);
                }
            }
        }

    }

    /**
     * 移动审批查询采购合同新增
     *
     * @param id
     * @return
     */
    @Override
    public ResponseMap getPurchaseContractApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<AduitAtta> fileList = new ArrayList<>();
        List<Map<String, String>> detailMapList1 = new ArrayList<>();
        List<Map<String, String>> detailMapList2 = new ArrayList<>();
        List<Map<String, String>> detailMapList4 = new ArrayList<>();
        List<Map<String, String>> detailMapList3 = new ArrayList<>();
        List<Map<String, String>> detailMapList5 = new ArrayList<>();

        if (id != null) {
            PurchaseContractVO contractVO = findDetailById(id);
            if (contractVO != null) {
                //补全项目信息
                final Long projectId = contractVO.getProjectId();
                if (projectId != null) {
                    final Project project = projectBusinessService.selectByPrimaryKey(projectId);
                    if (project != null) {
                        contractVO.setProjectName(project.getName());
                        contractVO.setProjectCode(project.getCode());
                    }
                }
                //基本信息
                headMap.put("name", contractVO.getName()); //合同名称
                headMap.put("type", contractVO.getTypeName()); //合同类型
                headMap.put("vendorName", contractVO.getVendorName()); //供应商名称
                headMap.put("projectCode", contractVO.getProjectCode()); //关联项目编号
                headMap.put("projectName", contractVO.getProjectName()); //关联项目名称
                headMap.put("currency", contractVO.getCurrency()); //币种
                headMap.put("amount",
                        null == contractVO.getAmount() ? "" : contractVO.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //合同金额(含税)
                headMap.put("excludingTaxAmount", null == contractVO.getExcludingTaxAmount() ? "" : contractVO.getExcludingTaxAmount().setScale(2,
                        BigDecimal.ROUND_HALF_UP).toString()); //合同总额(不含税)
                //headMap.put("taxRate", contractVO.getTaxRate()); //税率
                headMap.put("ouName", contractVO.getOuName() == null ? "" : contractVO.getOuName()); //业务实体
                headMap.put("contractMaterial", getContractMaterial(contractVO.getCode(), contractVO.getProjectId()));  //关联物料预算
                if (ContractTermsFlgEnum.NOT_STANDARD_TERMS.getCode().equals(contractVO.getContractTermsFlg())){
                    headMap.put("contractTerms",contractVO.getContractTerms());
                }

                //合同内容
                List<PurchaseContractDetailDTO> detailDTOList = contractVO.getPurchaseContractDetails();
                if (CollectionUtils.isNotEmpty(detailDTOList)) {
                    for (PurchaseContractDetailDTO detailDTO : detailDTOList) {
                        Map<String, String> detailMap = new HashMap<>();
                        detailMap.put("detailName", detailDTO.getName()); //名称
                        detailMap.put("model", detailDTO.getModel()); //型号
                        detailMap.put("brand", detailDTO.getBrand()); //品牌
                        detailMap.put("taxRate", contractVO.getTaxRate());//税率
                        detailMap.put("number", detailDTO.getNumber() + ""); //数量
                        detailMap.put("unitPrice", null == detailDTO.getUnitPrice() ? "" : detailDTO.getUnitPrice().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString()); //单价(含税)
                        detailMap.put("totalPrice", null == detailDTO.getTotalPrice() ? "" : detailDTO.getTotalPrice().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString()); //总价(含税)
                        detailMap.put("noTaxTotalPrice", null == detailDTO.getNoTaxTotalPrice() ? "" : detailDTO.getNoTaxTotalPrice().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString()); //总价(不含税)
                        detailMap.put("remark", detailDTO.getRemark());
                        detailMapList1.add(detailMap);
                    }
                }
                //预算信息
                List<PurchaseContractBudgetDto> wbsBudgetList = contractVO.getPurchaseContractWbsBudgets();
                if (CollectionUtils.isNotEmpty(wbsBudgetList)) {
                    for (PurchaseContractBudgetDto wbsBudgetDto : wbsBudgetList) {
                        Map<String, String> wbsBudgetMap = new HashMap<>();
                        wbsBudgetMap.put("materielDescr", wbsBudgetDto.getMaterielDescr() + ""); //物料描述
                        wbsBudgetMap.put("number", null == wbsBudgetDto.getNumber() ? "" : wbsBudgetDto.getNumber().toPlainString()); //采购合同签订数量
                        wbsBudgetMap.put("price", null == wbsBudgetDto.getPrice() ? "" : wbsBudgetDto.getPrice().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString()); //单价（不含税）
                        wbsBudgetMap.put("totalPrice", null == wbsBudgetDto.getTotalPrice() ? "" : wbsBudgetDto.getTotalPrice().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString()); //总价（不含税）
                        wbsBudgetMap.put("codingMiddleclass", wbsBudgetDto.getCodingMiddleclass() + ""); //物料中类
                        wbsBudgetMap.put("materialType", wbsBudgetDto.getMaterialType() + ""); //物料小类
                        wbsBudgetMap.put("model", StrUtil.blankToDefault(wbsBudgetDto.getModel(), StrUtil.EMPTY)); //图号/型号
                        wbsBudgetMap.put("brand", StrUtil.blankToDefault(wbsBudgetDto.getBrand(), StrUtil.EMPTY)); //品牌
                        wbsBudgetMap.put("chartVersion", StrUtil.blankToDefault(wbsBudgetDto.getChartVersion(), StrUtil.EMPTY)); //图纸版本号
                        wbsBudgetMap.put("requirementCode", StrUtil.blankToDefault(wbsBudgetDto.getRequirementCode(), StrUtil.EMPTY)); //需求发布单据编号
                        wbsBudgetMap.put("wbsSummaryCode", wbsBudgetDto.getWbsSummaryCode() + ""); //WBS号
                        wbsBudgetMap.put("wbsDemandOsCost", null == wbsBudgetDto.getWbsDemandOsCost() ? "" :
                                wbsBudgetDto.getWbsDemandOsCost().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //WBS需求预算占用（外包）
                        if (null != wbsBudgetDto.getWbsRemainingDemandOsCost() && wbsBudgetDto.getWbsRemainingDemandOsCost().compareTo(BigDecimal.ZERO) > 0) {
                            wbsBudgetMap.put("wbsRemainingDemandOsCost", wbsBudgetDto.getWbsRemainingDemandOsCost().setScale(2,
                                    BigDecimal.ROUND_HALF_UP).toString());//剩余WBS需求预算占用（外包）
                        } else {
                            wbsBudgetMap.put("wbsRemainingDemandOsCost", "0"); //剩余WBS需求预算占用（外包）
                        }
                        wbsBudgetMap.put("contractTotalAmount", null == wbsBudgetDto.getContractTotalAmount() ? "" :
                                wbsBudgetDto.getContractTotalAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //累计采购合同占用金额
                        wbsBudgetMap.put("needTotal", null == wbsBudgetDto.getNeedTotal() ? "" : wbsBudgetDto.getNeedTotal().toPlainString()); //总需求量
                        wbsBudgetMap.put("releasedQuantity", null == wbsBudgetDto.getReleasedQuantity() ? "" :
                                wbsBudgetDto.getReleasedQuantity().toPlainString()); //已签订采购合同数量
                        wbsBudgetMap.put("unit", wbsBudgetDto.getUnit() + ""); //单位
                        wbsBudgetMap.put("demandCost", null == wbsBudgetDto.getDemandCost() ? "" : wbsBudgetDto.getDemandCost().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString()); //需求预算
                        wbsBudgetMap.put("activityCode", wbsBudgetDto.getActivityCode() + ""); //活动事项编码
                        wbsBudgetMap.put("pamCode", wbsBudgetDto.getPamCode() + ""); //PAM物料编码
                        detailMapList2.add(wbsBudgetMap);
                    }
                }
                List<PurchaseContractBudgetDto> hroBudgetList = purchaseContractBudgetService.getWbsContractBudgetByPurchaseContractId(id);
                //预算信息-点工
                if (CollectionUtils.isNotEmpty(hroBudgetList)) {
                    for (PurchaseContractBudgetDto wbsBudgetDto : hroBudgetList) {
                        Map<String, String> wbsBudgetMap = new HashMap<>();
                        wbsBudgetMap.put("roleName", StrUtil.blankToDefault(wbsBudgetDto.getRoleName(), StrUtil.EMPTY)); //角色
                        wbsBudgetMap.put("investmentTime",
                                DateUtil.format(wbsBudgetDto.getStartDate(), DateUtil.DATE_PATTERN) + "至" + DateUtil.format(wbsBudgetDto.getEndDate(),
                                        DateUtil.DATE_PATTERN));//投入时间
                        wbsBudgetMap.put("requirementCode", StrUtil.blankToDefault(wbsBudgetDto.getRequirementCode(), StrUtil.EMPTY)); //需求单号
                        wbsBudgetMap.put("wbsSummaryCode", StrUtil.blankToDefault(wbsBudgetDto.getWbsSummaryCode(), StrUtil.EMPTY)); //WBS号
                        wbsBudgetMap.put("wbsDemandOsCost", null == wbsBudgetDto.getWbsDemandOsCost() ? "" :
                                wbsBudgetDto.getWbsDemandOsCost().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //可用需求预算
                        if (null != wbsBudgetDto.getWbsRemainingDemandOsCost() && wbsBudgetDto.getWbsRemainingDemandOsCost().compareTo(BigDecimal.ZERO) > 0) {
                            wbsBudgetMap.put("wbsRemainingDemandOsCost", wbsBudgetDto.getWbsRemainingDemandOsCost().setScale(2,
                                    BigDecimal.ROUND_HALF_UP).toString()); //剩余可用需求预算
                        } else {
                            wbsBudgetMap.put("wbsRemainingDemandOsCost", "0"); //剩余可用需求预算
                        }
                        wbsBudgetMap.put("contractTotalAmount", null == wbsBudgetDto.getContractTotalAmount() ? "" :
                                wbsBudgetDto.getContractTotalAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //累计采购合同占用金额
                        wbsBudgetMap.put("needTotal", null == wbsBudgetDto.getNeedTotal() ? "" : wbsBudgetDto.getNeedTotal().toPlainString()); //总需求量
                        wbsBudgetMap.put("unreleasedAmount", null == wbsBudgetDto.getUnreleasedAmount() ? "" :
                                wbsBudgetDto.getUnreleasedAmount().toPlainString()); //未签订采购合同数量
                        wbsBudgetMap.put("releasedQuantity", null == wbsBudgetDto.getReleasedQuantity() ? "" :
                                wbsBudgetDto.getReleasedQuantity().toPlainString()); //已签订采购合同数量
                        wbsBudgetMap.put("unit", StrUtil.blankToDefault(wbsBudgetDto.getUnit(), StrUtil.EMPTY)); //单位
                        wbsBudgetMap.put("demandCost", null == wbsBudgetDto.getDemandCost() ? "" : wbsBudgetDto.getDemandCost().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString()); //需求预算
                        wbsBudgetMap.put("activityCode", StrUtil.blankToDefault(wbsBudgetDto.getActivityCode(), StrUtil.EMPTY)); //活动事项编码
                        wbsBudgetMap.put("pamCode", StrUtil.blankToDefault(wbsBudgetDto.getPamCode(), StrUtil.EMPTY)); //PAM物料编码
                        wbsBudgetMap.put("number", null == wbsBudgetDto.getNumber() ? "" : wbsBudgetDto.getNumber().toPlainString()); //采购合同签订数量（小时）
                        wbsBudgetMap.put("price", null == wbsBudgetDto.getPrice() ? "" : wbsBudgetDto.getPrice().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString()); //小时单价（不含税）-原币
                        wbsBudgetMap.put("totalPrice", null == wbsBudgetDto.getTotalPrice() ? "" : wbsBudgetDto.getTotalPrice().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString()); //总价（不含税）-原币
                        wbsBudgetMap.put("localTotalPrice", null == wbsBudgetDto.getLocalTotalPrice() ? "" :
                                wbsBudgetDto.getLocalTotalPrice().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //总价（不含税）-本位币
                        detailMapList4.add(wbsBudgetMap);
                    }
                }
                //付款计划
                List<PaymentPlanDTO> paymentPlanDTOList = contractVO.getPaymentPlans();
                if (CollectionUtils.isNotEmpty(paymentPlanDTOList)) {
                    for (PaymentPlanDTO planDTO : paymentPlanDTOList) {
                        Map<String, String> detailMap = new HashMap<>();
                        detailMap.put("num", planDTO.getNum() + ""); //笔数
                        //获取当前组织配置的付款计划标识
                        Boolean prepaymentFlag = paymentPlanService.getCurrentOrgPrepaymentFlagForCustomDict();
                        if (prepaymentFlag) {
                            detailMap.put("prepaymentFlag", Optional.ofNullable(planDTO.getPrepaymentFlag()).orElse(new Byte("0")) == 1 ? "是" : "否");//预付款标识
                        }
                        detailMap.put("planAmount", null == planDTO.getAmount() ? "" :
                                planDTO.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //计划付款金额(含税)
                        detailMap.put("paymentMethodName", planDTO.getPaymentMethodName()); //付款方式
                        detailMap.put("milestone", planDTO.getMilestoneName()); //关联里程碑
                        detailMap.put("date", DateUtil.format(planDTO.getDate(), DateUtil.DATE_PATTERN)); //计划付款日期
                        detailMap.put("requirement", planDTO.getRequirement()); //付款条件
                        detailMapList3.add(detailMap);
                    }
                }

                if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(contractVO.getContractTermsFlg())) {
                    List<PurchaseContractStandardTermsDto> standardTermsDtoList = contractVO.getStandardTermsDtoList();
                    if (CollectionUtils.isNotEmpty(standardTermsDtoList)) {
                        for (PurchaseContractStandardTermsDto standardTermsDto : standardTermsDtoList) {
                            Map<String, String> standardTermsListMap = new HashMap<>();
                            standardTermsListMap.put("termsCode", standardTermsDto.getTermsCode());
                            standardTermsListMap.put("termsName", standardTermsDto.getTermsName());
                            standardTermsListMap.put("termsDisplayContent", standardTermsDto.getTermsDisplayContent());

                            List<PurchaseContractStandardTermsDeviation> standardTermsDeviationList = standardTermsDto.getStandardTermsDeviationList();
                            StringBuilder deviationBuilder = new StringBuilder();
                            if (CollectionUtils.isNotEmpty(standardTermsDeviationList)) {
                                int count = 1;
                                for (PurchaseContractStandardTermsDeviation standardTermsDeviation : standardTermsDeviationList) {
                                    if (StringUtils.isNotEmpty(standardTermsDeviation.getDeviationInfo())) {
                                        if (count > 1) deviationBuilder.append("\n");
                                        deviationBuilder.append("偏离项").append(count++).append("：").append(standardTermsDeviation.getDeviationInfo());
                                    }
                                }
                            }
                            standardTermsListMap.put("standardTermsDeviation", deviationBuilder.toString());
                            detailMapList5.add(standardTermsListMap);
                        }
                    }
                }
            } else {
                responseMap.setMsg("头信息为空！");
                responseMap.setStatus("fail");
            }

            String annex = contractVO.getAnnex();
            if (StringUtils.isNotEmpty(annex)) {
                for (String oAnnex : annex.split(",")) {
                    try {
                        AduitAtta aduitAtta = new AduitAtta();
                        aduitAtta.setFdId(oAnnex);
                        fileList.add(aduitAtta);
                    } catch (NumberFormatException e) {
                        logger.error(e.getMessage(), e);
                    }
                }
            }
            responseMap.setMsg("成功");
            responseMap.setStatus("success");
            responseMap.setHeadMap(headMap);
            responseMap.setList1(detailMapList1);
            if ("点工采购".equals(contractVO.getTypeName())) {
                responseMap.setList4(detailMapList4);
                responseMap.setList2(new ArrayList<>());
            } else {
                responseMap.setList2(detailMapList2);
                responseMap.setList4(new ArrayList<>());
            }
            responseMap.setList3(detailMapList3);
            if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(contractVO.getContractTermsFlg())){
                responseMap.setList5(detailMapList5);
            }
            responseMap.setFileList(fileList);
        }
        return responseMap;
    }

    private String getContractMaterial(String code, Long projectId) {
        ProjectContractBudgetMaterialExample example = new ProjectContractBudgetMaterialExample();
        ProjectContractBudgetMaterialExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId).andCodeEqualTo(code).andDeletedFlagEqualTo(Boolean.FALSE).andStatusEqualTo(Boolean.FALSE);

        List<ProjectContractBudgetMaterial> materialList = budgetMaterialMapper.selectByExample(example);
        StringBuilder stringBuilder = new StringBuilder();

        if (ListUtils.isNotEmpty(materialList)) {
            for (ProjectContractBudgetMaterial budgetMaterial : materialList) {
                if (StringUtils.isEmpty(stringBuilder.toString())) {
                    stringBuilder.append(budgetMaterial.getName()).append(":").append(budgetMaterial.getMoney());
                } else {
                    stringBuilder.append(",").append(budgetMaterial.getName()).append(":").append(budgetMaterial.getMoney());
                }

            }
        }
        return StringUtils.isEmpty(stringBuilder.toString()) ? null : stringBuilder.toString();
    }

    /**
     * 移动审批查询采购合同基本信息变更
     *
     * @param id
     * @return
     */
    @Override
    public ResponseMap getPurchaseContractInfoChangeApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> detailMapList1 = new ArrayList<>();
        List<Map<String, String>> detailMapList2 = new ArrayList<>();
        List<Map<String, String>> detailMapList3 = new ArrayList<>();
        List<AduitAtta> fileList = new ArrayList<>();
        if (id != null) {
            PurchaseContractChangeHistoryVO changeHistoryVO = getBaseChangeHistory(id);
            if (changeHistoryVO != null) {
                PurchaseContractChangeHistoryDTO history = changeHistoryVO.getHistory();
                PurchaseContractChangeHistoryDTO change = changeHistoryVO.getChange();
                //变更原因
                headMap.put("changeType", changeHistoryVO.getChangeRessonTypeName()); //变更原因类型
                headMap.put("changeReason", changeHistoryVO.getReason()); //变更原因说明
                headMap.put("name", history.getName()); //合同名称
                headMap.put("contractCode", history.getCode()); //合同编号

                //变更内容--合同名称
                if (!Objects.equals(history.getName(), change.getName())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "合同名称"); //字段
                    detailMap1.put("changeBefore", history.getName()); //变更前
                    detailMap1.put("changeAfter", change.getName()); //变更后
                    detailMapList1.add(detailMap1);
                }

                //变更内容--合同开始日期
                if (!DateUtils.equalsDate(history.getStartTime(), change.getStartTime())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "合同开始日期"); //字段
                    detailMap1.put("changeBefore", DateUtil.format(history.getStartTime(), DateUtil.DATE_PATTERN)); //变更前
                    detailMap1.put("changeAfter", DateUtil.format(change.getStartTime(), DateUtil.DATE_PATTERN)); //变更后
                    detailMapList1.add(detailMap1);
                }

                //变更内容--合同结束日期
                if (!DateUtils.equalsDate(history.getEndTime(), change.getEndTime())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "合同结束日期"); //字段
                    detailMap1.put("changeBefore", DateUtil.format(history.getEndTime(), DateUtil.DATE_PATTERN)); //变更前
                    detailMap1.put("changeAfter", DateUtil.format(change.getEndTime(), DateUtil.DATE_PATTERN)); //变更后
                    detailMapList1.add(detailMap1);
                }

                //变更内容--供应商名称
                if (!Objects.equals(history.getVendorName(), change.getVendorName())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "供应商名称"); //字段
                    detailMap1.put("changeBefore", history.getVendorName()); //变更前
                    detailMap1.put("changeAfter", change.getVendorName()); //变更后
                    detailMapList1.add(detailMap1);
                }

                //变更内容--供应商编码
                if (!Objects.equals(history.getVendorCode(), change.getVendorCode())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "供应商编码"); //字段
                    detailMap1.put("changeBefore", history.getVendorCode()); //变更前
                    detailMap1.put("changeAfter", change.getVendorCode()); //变更后
                    detailMapList1.add(detailMap1);
                }

                //变更内容--供应商地点
                if (!Objects.equals(history.getVendorSiteCode(), change.getVendorSiteCode())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "供应商地点"); //字段
                    detailMap1.put("changeBefore", history.getVendorSiteCode()); //变更前
                    detailMap1.put("changeAfter", change.getVendorSiteCode()); //变更后
                    detailMapList1.add(detailMap1);
                }

                //变更内容--关联项目
                if (!Objects.equals(history.getProjectName(), change.getProjectName())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "关联项目"); //字段
                    detailMap1.put("changeBefore", history.getProjectName()); //变更前
                    detailMap1.put("changeAfter", change.getProjectName()); //变更后
                    detailMapList1.add(detailMap1);
                }

                //变更内容--关联项目编号
                if (!Objects.equals(history.getProjectCode(), change.getProjectCode())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "关联项目编号"); //字段
                    detailMap1.put("changeBefore", history.getProjectCode()); //变更前
                    detailMap1.put("changeAfter", change.getProjectCode()); //变更后
                    detailMapList1.add(detailMap1);
                }

                //变更内容--引用法务系统合同编号
                if (!Objects.equals(history.getLegalAffairsCode(), change.getLegalAffairsCode())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "引用法务系统合同编号"); //字段
                    detailMap1.put("changeBefore", history.getLegalAffairsCode()); //变更前
                    detailMap1.put("changeAfter", change.getLegalAffairsCode()); //变更后
                    detailMapList1.add(detailMap1);
                }

                //变更内容--采购跟进人
                if (!Objects.equals(history.getPurchasingFollower(), change.getPurchasingFollower())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "采购跟进人"); //字段
                    detailMap1.put("changeBefore", history.getPurchasingFollowerName()); //变更前
                    detailMap1.put("changeAfter", change.getPurchasingFollowerName()); //变更后
                    detailMapList1.add(detailMap1);
                }

                //变更内容--备注
                if (!Objects.equals(history.getRemark(), change.getRemark())) {
                    Map<String, String> detailMap1 = new HashMap<>();
                    detailMap1.put("field", "备注"); //字段
                    detailMap1.put("changeBefore", history.getRemark()); //变更前
                    detailMap1.put("changeAfter", change.getRemark()); //变更后
                    detailMapList1.add(detailMap1);
                }

                //标准条款
                standardTermsSet(detailMapList2, detailMapList3, history, change);


                //获取合同附件
                String annex = change.getAnnex();
                Set<Long> annexIds = new HashSet<>();
                if (StringUtils.isNotEmpty(annex)) {
                    for (String oAnnex : annex.split(",")) {
                        if (StringUtils.isNotEmpty(oAnnex)) {
                            annexIds.add(Long.valueOf(oAnnex));
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(annexIds)) {
                    for (Long annexId : annexIds) {
                        try {
                            AduitAtta aduitAtta = new AduitAtta();
                            aduitAtta.setFdId(annexId + "");
                            fileList.add(aduitAtta);
                        } catch (NumberFormatException e) {
                            logger.error(e.getMessage(), e);
                        }
                    }
                }
                //获取流程附件
                List<CtcAttachmentDto> attachmentDtos = changeHistoryVO.getAttachmentDtos();
                if (!CollectionUtils.isEmpty(attachmentDtos)) {
                    for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                        AduitAtta aduitAtta = new AduitAtta();
                        aduitAtta.setFdId(String.valueOf(attachmentDto.getAttachId()));
                        aduitAtta.setFileSize(String.valueOf(attachmentDto.getFileSize()));
                        aduitAtta.setFileName(attachmentDto.getAttachName());
                        fileList.add(aduitAtta);
                    }
                }
            } else {
                responseMap.setMsg("头信息为空！");
                responseMap.setStatus("fail");
            }

            responseMap.setMsg("成功");
            responseMap.setStatus("success");
            responseMap.setHeadMap(headMap);
            responseMap.setList1(detailMapList1);
            responseMap.setList2(detailMapList2);
            responseMap.setList3(detailMapList3);
            if (!CollectionUtils.isEmpty(fileList)) {
                responseMap.setFileList(fileList);
            }
        }
        return responseMap;
    }

    private static void standardTermsSet(List<Map<String, String>> detailMapList2, List<Map<String, String>> detailMapList3, PurchaseContractChangeHistoryDTO history, PurchaseContractChangeHistoryDTO change) {
        //变更前
        if (history.getContractTermsFlg().equals(ContractTermsFlgEnum.NOT_STANDARD_TERMS.getCode())) {
            Map<String, String> detailMap2 = new HashMap<>();
            detailMap2.put("field", "非标准条款");
            detailMap2.put("contractTerms", history.getContractTerms());
            detailMapList2.add(detailMap2);
        } else {
            List<PurchaseContractStandardTermsDto> standardTermsDtoList = history.getStandardTermsDtoList();
            if (CollectionUtils.isNotEmpty(standardTermsDtoList)) {
                for (PurchaseContractStandardTermsDto standardTermsDto : standardTermsDtoList) {
                    Map<String, String> detailMap2 = new HashMap<>();
                    detailMap2.put("field", "标准条款");
                    detailMap2.put("termsCode", standardTermsDto.getTermsCode());
                    detailMap2.put("termsName", standardTermsDto.getTermsName());
                    detailMap2.put("termsDisplayContent", standardTermsDto.getTermsDisplayContent());

                    List<PurchaseContractStandardTermsDeviation> standardTermsDeviationList = standardTermsDto.getStandardTermsDeviationList();
                    StringBuilder deviationBuilder = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(standardTermsDeviationList)) {
                        int count = 1;
                        for (PurchaseContractStandardTermsDeviation standardTermsDeviation : standardTermsDeviationList) {
                            if (StringUtils.isNotEmpty(standardTermsDeviation.getDeviationInfo())) {
                                if (count > 1) deviationBuilder.append("\n");
                                deviationBuilder.append("偏离项").append(count++).append("：").append(standardTermsDeviation.getDeviationInfo());
                            }
                        }
                    }
                    detailMap2.put("standardTermsDeviation", deviationBuilder.toString());
                    detailMapList2.add(detailMap2);
                }
            }
        }

        //变更后
        if (change.getContractTermsFlg().equals(ContractTermsFlgEnum.NOT_STANDARD_TERMS.getCode())) {
            Map<String, String> detailMap3 = new HashMap<>();
            detailMap3.put("field", "非标准条款");
            detailMap3.put("contractTerms", change.getContractTerms());
            detailMapList3.add(detailMap3);
        } else {
            List<PurchaseContractStandardTermsDto> standardTermsDtoList = change.getStandardTermsDtoList();
            if (CollectionUtils.isNotEmpty(standardTermsDtoList)) {
                for (PurchaseContractStandardTermsDto standardTermsDto : standardTermsDtoList) {
                    Map<String, String> detailMap3 = new HashMap<>();
                    detailMap3.put("field", "标准条款");
                    detailMap3.put("termsCode", standardTermsDto.getTermsCode());
                    detailMap3.put("termsName", standardTermsDto.getTermsName());
                    detailMap3.put("termsDisplayContent", standardTermsDto.getTermsDisplayContent());

                    List<PurchaseContractStandardTermsDeviation> standardTermsDeviationList = standardTermsDto.getStandardTermsDeviationList();
                    StringBuilder deviationBuilder = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(standardTermsDeviationList)) {
                        int count = 1;
                        for (PurchaseContractStandardTermsDeviation standardTermsDeviation : standardTermsDeviationList) {
                            if (StringUtils.isNotEmpty(standardTermsDeviation.getDeviationInfo())) {
                                if (count > 1) deviationBuilder.append("\n");
                                deviationBuilder.append("偏离项").append(count++).append("：").append(standardTermsDeviation.getDeviationInfo());
                            }
                        }
                    }
                    detailMap3.put("standardTermsDeviation", deviationBuilder.toString());
                    detailMapList3.add(detailMap3);
                }
            }
        }
    }

    /**
     * 移动审批查询采购合同内容变更
     *
     * @param id
     * @return
     */
    @Override
    public ResponseMap getPurchaseContractDetailChangeApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> detailMapList1 = new ArrayList<>();
        List<Map<String, String>> detailMapList2 = new ArrayList<>();
        List<Map<String, String>> historyStandardTermsDtoList = new ArrayList<>();
        List<Map<String, String>> changeStandardTermsDtoList = new ArrayList<>();
        List<AduitAtta> fileList = new ArrayList<>();
        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>(); //附件
        if (id != null) {
            PurchaseContractContentChangeHistoryVO changeHistoryVO = getContractDetailChangeHistory(id);
            PurchaseContractChangeHistoryDTO history = (PurchaseContractChangeHistoryDTO) changeHistoryVO.getPurchaseContractHistory().getHistory();
            PurchaseContractChangeHistoryDTO change = (PurchaseContractChangeHistoryDTO) changeHistoryVO.getPurchaseContractHistory().getChange();
            List<PurchaseContractContentChangeHistoryVO.ChangeHistory> paymentPlanHistoryList = changeHistoryVO.getPaymentPlanHistories();
            if (changeHistoryVO != null) {
                attachmentDtos = changeHistoryVO.getAttachmentDtos();
                //变更原因
                headMap.put("changeType", changeHistoryVO.getHeader().getChangeRessonTypeName()); //变更原因类型
                headMap.put("changeReason", changeHistoryVO.getHeader().getReason()); //变更原因说明
                headMap.put("name", history.getName()); //合同名称
                headMap.put("contractCode", history.getCode()); //合同编号

                //变更内容--总额(含税)
                Map<String, String> detailMap1 = new HashMap<>();
                detailMap1.put("field", "金额(含税)"); //字段
                detailMap1.put("changeBefore", null == history.getAmount() ? "" :
                        history.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //变更前
                detailMap1.put("changeAfter",
                        null == change.getAmount() ? "" : change.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //变更后
                detailMapList1.add(detailMap1);

                //变更内容--总额(不含税)
                Map<String, String> detailMap2 = new HashMap<>();
                detailMap2.put("field", "总额(不含税)"); //字段
                detailMap2.put("changeBefore", null == history.getExcludingTaxAmount() ? "" : history.getExcludingTaxAmount().setScale(2,
                        BigDecimal.ROUND_HALF_UP).toString()); //变更前
                detailMap2.put("changeAfter", null == change.getExcludingTaxAmount() ? "" : change.getExcludingTaxAmount().setScale(2,
                        BigDecimal.ROUND_HALF_UP).toString()); //变更后
                detailMapList1.add(detailMap2);

                // 点工采购（变更后-合同内容，预算信息）
                if ("点工采购".equals(changeHistoryVO.getTypeName())) {
                    // 变更后-合同内容
                    List<PurchaseContractContentChangeHistoryVO.ChangeHistory> detailHistories = changeHistoryVO.getDetailHistories();
                    List<Map<String, String>> detailMapList3 = getHroPurchaseByDetailHistories(detailHistories);// 变更后合同内容
                    if (CollectionUtils.isNotEmpty(detailMapList3)) responseMap.setList3(detailMapList3);
                    // 变更后-预算信息
                    List<PurchaseContractContentChangeHistoryVO.ChangeHistory> budgetChangeHistories =
                            changeHistoryVO.getPurchaseContractBudgetChangeHistories();
                    List<Map<String, String>> detailMapList4 = getHroPurchaseBudgetByDetailHistories(budgetChangeHistories);// 变更后预算信息
                    if (CollectionUtils.isNotEmpty(detailMapList4)) responseMap.setList4(detailMapList4);
                } else {
                    //变更内容--税率（非点工也需隐藏）
                    //Map<String, String> detailMap3 = new HashMap<>();
                    //detailMap3.put("field", "税率"); //字段
                    //detailMap3.put("changeBefore", history.getTaxRate()); //变更前
                    //detailMap3.put("changeAfter", change.getTaxRate()); //变更后
                    //detailMapList1.add(detailMap3);

                    //变更后-付款计划
                    if (CollectionUtils.isNotEmpty(paymentPlanHistoryList)) {
                        for (PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistory : paymentPlanHistoryList) {
                            if (null != changeHistory.getChange()) {
                                PaymentPlanChangeHistoryDto planChangeHistoryDto = (PaymentPlanChangeHistoryDto) changeHistory.getChange();
                                if (planChangeHistoryDto.getDeletedFlag()) {
                                    continue;
                                }
                                Map<String, String> detailMap4 = new HashMap<>();
                                detailMap4.put("num", planChangeHistoryDto.getNum() + ""); //笔数
                                //获取当前组织配置的付款计划标识
                                Boolean prepaymentFlag = paymentPlanService.getCurrentOrgPrepaymentFlagForCustomDict();
                                if (prepaymentFlag) {
                                    detailMap4.put("prepaymentFlag", Optional.ofNullable(planChangeHistoryDto.getPrepaymentFlag()).orElse(new Byte("0")) == 1 ? "是" : "否");//预付款标识
                                }
                                detailMap4.put("code", planChangeHistoryDto.getCode()); //付款计划编号
                                detailMap4.put("date", DateUtil.format(planChangeHistoryDto.getDate(), DateUtil.DATE_PATTERN)); //计划付款日期
                                detailMap4.put("planAmount", null == planChangeHistoryDto.getAmount() ? "" :
                                        planChangeHistoryDto.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //计划付款金额
                                detailMap4.put("paymentMethodName", planChangeHistoryDto.getPaymentMethodName()); //付款方式
                                detailMap4.put("requirement", planChangeHistoryDto.getRequirement()); //付款条件
                                detailMap4.put("milestone", planChangeHistoryDto.getMilestoneName()); //关联里程碑
                                detailMap4.put("milestoneDate", DateUtil.format(planChangeHistoryDto.getMilestoneEndTime(), DateUtil.DATE_PATTERN)); //里程碑计划结束日期
                                detailMap4.put("milestoneStatus", MilepostStatus.getValue(planChangeHistoryDto.getMilestoneStatus())); //里程碑状态
                                detailMap4.put("payedAmount", null == planChangeHistoryDto.getActualAmount() ? "" :
                                        planChangeHistoryDto.getActualAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //已付款金额
                                detailMap4.put("penaltyAmount", null == planChangeHistoryDto.getTotalpenaltyAmount() ? "" :
                                        planChangeHistoryDto.getTotalpenaltyAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //罚扣金额
                                detailMapList2.add(detailMap4);
                            }
                        }
                    }
                }

                //标准条款
                standardTermsSet(historyStandardTermsDtoList, changeStandardTermsDtoList, history, change);

                //获取合同附件
                String annex = change.getAnnex();
                Set<Long> annexIds = new HashSet<>();
                if (StringUtils.isNotEmpty(annex)) {
                    for (String oAnnex : annex.split(",")) {
                        if (StringUtils.isNotEmpty(oAnnex)) {
                            annexIds.add(Long.valueOf(oAnnex));
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(annexIds)) {
                    for (Long annexId : annexIds) {
                        try {
                            AduitAtta aduitAtta = new AduitAtta();
                            aduitAtta.setFdId(annexId + "");
                            fileList.add(aduitAtta);
                        } catch (NumberFormatException e) {
                            logger.error(e.getMessage(), e);
                        }
                    }
                }
            } else {
                responseMap.setMsg("头信息为空！");
                responseMap.setStatus("fail");
            }

            responseMap.setMsg("成功");
            responseMap.setStatus("success");
            responseMap.setHeadMap(headMap);
            responseMap.setList1(detailMapList1);
            responseMap.setList2(detailMapList2);
            responseMap.setList5(historyStandardTermsDtoList);
            responseMap.setList6(changeStandardTermsDtoList);

            //获取附件
            if (!CollectionUtils.isEmpty(attachmentDtos)) {
                for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                    AduitAtta aduitAtta = new AduitAtta();
                    aduitAtta.setFdId(String.valueOf(attachmentDto.getAttachId()));
                    aduitAtta.setFileSize(String.valueOf(attachmentDto.getFileSize()));
                    aduitAtta.setFileName(attachmentDto.getAttachName());
                    fileList.add(aduitAtta);
                }
            }
            if (!CollectionUtils.isEmpty(fileList)) {
                responseMap.setFileList(fileList);
            }
        }
        return responseMap;
    }

    private List<Map<String, String>> getHroPurchaseBudgetByDetailHistories(List<PurchaseContractContentChangeHistoryVO.ChangeHistory> budgetChangeHistories) {
        if (CollectionUtils.isEmpty(budgetChangeHistories)) return null;
        List<Map<String, String>> detailMapList4 = new ArrayList<>();
        budgetChangeHistories.forEach(changeHistory -> {
            if (changeHistory != null) {
                PurchaseContractBudgetChangeHistoryDto budgetChangeHistoryDto = (PurchaseContractBudgetChangeHistoryDto) changeHistory.getChange();
                Map<String, String> contractDetailChangeMap = new HashMap<>();
                contractDetailChangeMap.put("roleName", budgetChangeHistoryDto.getRoleName());// 角色
                contractDetailChangeMap.put("number", budgetChangeHistoryDto.getNumber() == null ?
                        "0" : budgetChangeHistoryDto.getNumber().setScale(0, RoundingMode.UNNECESSARY).toPlainString());// 采购合同签订数量（小时）
                contractDetailChangeMap.put("totalPrice", budgetChangeHistoryDto.getTotalPrice() == null ?
                        "0.00" : budgetChangeHistoryDto.getTotalPrice().setScale(2, RoundingMode.UNNECESSARY).toPlainString());// 总价（不含税）-原币
                contractDetailChangeMap.put("inputTime", DateUtils.format(budgetChangeHistoryDto.getStartDate(), DateUtils.FORMAT_SHORT)
                        + " - " + DateUtils.format(budgetChangeHistoryDto.getEndDate(), DateUtils.FORMAT_SHORT));// 投入时间
                contractDetailChangeMap.put("requirementCode", budgetChangeHistoryDto.getRequirementCode());// 需求单号
                contractDetailChangeMap.put("wbsSummaryCode", budgetChangeHistoryDto.getWbsSummaryCode());// WBS号
                contractDetailChangeMap.put("wbsDemandOsCost", budgetChangeHistoryDto.getWbsDemandOsCost() == null ?
                        "0.00" : budgetChangeHistoryDto.getWbsDemandOsCost().setScale(2, RoundingMode.UNNECESSARY).toPlainString());// 可用需求预算
                contractDetailChangeMap.put("wbsRemainingDemandOsCost", budgetChangeHistoryDto.getWbsRemainingDemandOsCost() == null ?
                        "0.00" : budgetChangeHistoryDto.getWbsRemainingDemandOsCost().setScale(2, RoundingMode.UNNECESSARY).toPlainString());//
                // 剩余可用需求预算
                contractDetailChangeMap.put("contractTotalAmount", budgetChangeHistoryDto.getContractTotalAmount() == null ?
                        "0.00" : budgetChangeHistoryDto.getContractTotalAmount().setScale(2, RoundingMode.UNNECESSARY).toPlainString());// 累计采购合同占用金额
                contractDetailChangeMap.put("needTotal", budgetChangeHistoryDto.getNeedTotal() == null ?
                        "0" : budgetChangeHistoryDto.getNeedTotal().setScale(0, RoundingMode.UNNECESSARY).toPlainString());// 总需求量
                contractDetailChangeMap.put("unreleasedAmount", budgetChangeHistoryDto.getUnreleasedAmount() == null ?
                        "0" : budgetChangeHistoryDto.getUnreleasedAmount().setScale(0, RoundingMode.UNNECESSARY).toPlainString());// 未签订采购合同数量
                contractDetailChangeMap.put("releasedQuantity", budgetChangeHistoryDto.getReleasedQuantity() == null ?
                        "0" : budgetChangeHistoryDto.getReleasedQuantity().setScale(0, RoundingMode.UNNECESSARY).toPlainString());// 已签订采购合同数量
                contractDetailChangeMap.put("unit", budgetChangeHistoryDto.getUnit());// 单位
                contractDetailChangeMap.put("demandCost", budgetChangeHistoryDto.getDemandCost() == null ?
                        "0.00" : budgetChangeHistoryDto.getDemandCost().setScale(2, RoundingMode.UNNECESSARY).toPlainString());// 需求预算
                contractDetailChangeMap.put("activityCode", budgetChangeHistoryDto.getActivityCode());// 活动事项编码
                contractDetailChangeMap.put("pamCode", budgetChangeHistoryDto.getPamCode());// PAM物料编码
                contractDetailChangeMap.put("price", budgetChangeHistoryDto.getPrice() == null ?
                        "0.00" : budgetChangeHistoryDto.getPrice().setScale(2, RoundingMode.UNNECESSARY).toPlainString());// 小时单价（不含税）-原币
                contractDetailChangeMap.put("localTotalPrice", budgetChangeHistoryDto.getLocalTotalPrice() == null ?
                        "0.00" : budgetChangeHistoryDto.getLocalTotalPrice().setScale(2, RoundingMode.UNNECESSARY).toPlainString());// 总价（不含税）-本位币
                detailMapList4.add(contractDetailChangeMap);
            }
        });
        return detailMapList4;
    }

    private List<Map<String, String>> getHroPurchaseByDetailHistories(List<PurchaseContractContentChangeHistoryVO.ChangeHistory> detailHistories) {
        if (CollectionUtils.isEmpty(detailHistories)) return null;
        List<Map<String, String>> detailMapList3 = new ArrayList<>();
        detailHistories.forEach(changeHistory -> {
            if (changeHistory != null) {
                PurchaseContractDetailChangeHistory contractDetailChangeHistory = (PurchaseContractDetailChangeHistory) changeHistory.getChange();
                Map<String, String> contractDetailChangeMap = new HashMap<>();
                contractDetailChangeMap.put("name", contractDetailChangeHistory.getName());// 名称
                contractDetailChangeMap.put("model", contractDetailChangeHistory.getModel());// 型号
                contractDetailChangeMap.put("brand", contractDetailChangeHistory.getBrand());//品牌
                contractDetailChangeMap.put("taxRate", contractDetailChangeHistory.getTaxRate());// 税率
                contractDetailChangeMap.put("number", contractDetailChangeHistory.getNumber() == null ?
                        "0" : "" + contractDetailChangeHistory.getNumber());// 数量
                contractDetailChangeMap.put("unitPrice", contractDetailChangeHistory.getUnitPrice() == null ?
                        "0.00" : contractDetailChangeHistory.getUnitPrice().setScale(2, RoundingMode.UNNECESSARY).toPlainString());// 单价（含税）
                contractDetailChangeMap.put("totalPrice", contractDetailChangeHistory.getTotalPrice() == null ?
                        "0.00" : contractDetailChangeHistory.getTotalPrice().setScale(2, RoundingMode.UNNECESSARY).toPlainString());// 总价（含税）
                contractDetailChangeMap.put("noTaxTotalPrice", contractDetailChangeHistory.getNoTaxTotalPrice() == null ?
                        "0.00" : contractDetailChangeHistory.getNoTaxTotalPrice().setScale(2, RoundingMode.UNNECESSARY).toPlainString());// 总价（不含税）
                contractDetailChangeMap.put("remark", contractDetailChangeHistory.getRemark());// 备注
                detailMapList3.add(contractDetailChangeMap);
            }
        });
        return detailMapList3;
    }

    /**
     * 移动审批查询采购合同付款计划变更
     *
     * @param id
     * @return
     */
    @Override
    public ResponseMap getPurchaseContractPlanChangeApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> detailMapList1 = new ArrayList<>();
        if (id != null) {
            PurchaseContractContentChangeHistoryVO changeHistoryVO = getPaymentPlanChangeHistory(id);
            PurchaseContractVO contract = findDetailById(changeHistoryVO.getHeader().getPurchaseContractId());
            List<PurchaseContractContentChangeHistoryVO.ChangeHistory> paymentPlanHistoryList = changeHistoryVO.getPaymentPlanHistories();
            if (changeHistoryVO != null) {
                //变更原因
                headMap.put("changeType", changeHistoryVO.getHeader().getChangeRessonTypeName()); //变更原因类型
                headMap.put("changeReason", changeHistoryVO.getHeader().getReason()); //变更原因说明
                headMap.put("name", contract.getName()); //合同名称
                headMap.put("contractCode", contract.getCode()); //合同编号

                //变更后-付款计划
                for (PurchaseContractContentChangeHistoryVO.ChangeHistory changeHistory : paymentPlanHistoryList) {
                    if (null != changeHistory.getChange()) {
                        PaymentPlanChangeHistoryDto planChangeHistoryDto = (PaymentPlanChangeHistoryDto) changeHistory.getChange();
                        if (planChangeHistoryDto.getDeletedFlag()) {
                            continue;
                        }
                        Map<String, String> detailMap4 = new HashMap<>();
                        detailMap4.put("num", planChangeHistoryDto.getNum() + ""); //笔数
                        //获取当前组织配置的付款计划标识
                        Boolean prepaymentFlag = paymentPlanService.getCurrentOrgPrepaymentFlagForCustomDict();
                        if (prepaymentFlag) {
                            detailMap4.put("prepaymentFlag", Optional.ofNullable(planChangeHistoryDto.getPrepaymentFlag()).orElse(new Byte("0")) == 1 ? "是" : "否");//预付款标识
                        }
                        detailMap4.put("code", planChangeHistoryDto.getCode()); //付款计划编号
                        detailMap4.put("date", DateUtil.format(planChangeHistoryDto.getDate(), DateUtil.DATE_PATTERN)); //计划付款日期
                        detailMap4.put("planAmount", null == planChangeHistoryDto.getAmount() ? "" : planChangeHistoryDto.getAmount().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString()); //计划付款金额
                        detailMap4.put("paymentMethodName", planChangeHistoryDto.getPaymentMethodName()); //付款方式
                        detailMap4.put("requirement", planChangeHistoryDto.getRequirement()); //付款条件
                        detailMap4.put("milestone", planChangeHistoryDto.getMilestoneName()); //关联里程碑
                        detailMap4.put("milestoneDate", DateUtil.format(planChangeHistoryDto.getMilestoneEndTime(), DateUtil.DATE_PATTERN));
                        //里程碑计划结束日期
                        detailMap4.put("milestoneStatus", MilepostStatus.getValue(planChangeHistoryDto.getMilestoneStatus())); //里程碑状态
                        detailMap4.put("payedAmount", null == planChangeHistoryDto.getActualAmount() ? "" :
                                planChangeHistoryDto.getActualAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //已付款金额
                        detailMap4.put("penaltyAmount", null == planChangeHistoryDto.getTotalpenaltyAmount() ? "" :
                                planChangeHistoryDto.getTotalpenaltyAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //罚扣金额
                        detailMapList1.add(detailMap4);
                    }
                }

            } else {
                responseMap.setMsg("头信息为空！");
                responseMap.setStatus("fail");
            }

            responseMap.setMsg("成功");
            responseMap.setStatus("success");
            responseMap.setHeadMap(headMap);
            responseMap.setList1(detailMapList1);
            //获取附件
            List<CtcAttachmentDto> attachmentDtos = changeHistoryVO.getAttachmentDtos();
            if (!CollectionUtils.isEmpty(attachmentDtos)) {
                List<AduitAtta> fileList = new ArrayList<>();
                for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                    AduitAtta aduitAtta = new AduitAtta();
                    aduitAtta.setFdId(String.valueOf(attachmentDto.getAttachId()));
                    aduitAtta.setFileSize(String.valueOf(attachmentDto.getFileSize()));
                    aduitAtta.setFileName(attachmentDto.getAttachName());
                    fileList.add(aduitAtta);
                }
                responseMap.setFileList(fileList);
            }
        }
        return responseMap;
    }

    @Override
    public Boolean checkBaseInfoChange(Long id) {
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(id);
        if (purchaseContract != null) {
            Long projectId = purchaseContract.getProjectId();
            if (projectId == null) {
                return Boolean.TRUE;
            }

            return !projectService.checkIncomeCompleted(projectId);
        }
        return Boolean.FALSE;
    }

    /**
     * 判断当前登录用户是否创建人
     *
     * @param contractId
     * @return boolean true是 false否
     */
    @Override
    public Boolean isCreater(Long contractId) {
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(contractId);
        return isCreater(purchaseContract);
    }

    /**
     * 判断当前登录用户是否创建人
     *
     * @param purchaseContract
     * @return boolean true是 false否
     */
    private Boolean isCreater(PurchaseContract purchaseContract) {
        if (purchaseContract == null) {
            return false;
        }
        Long id = purchaseContract.getCreateBy();
        if (id == null) {
            return false;
        }
        long currentUserId = SystemContext.getUserId();//当前登录用户ID
        if (id.longValue() == currentUserId) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Boolean isManager(Long contractId) {
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(contractId);
        return isManager(purchaseContract);
    }

    public Boolean isManager(PurchaseContract purchaseContract) {
        if (purchaseContract == null) {
            return false;
        }
        Long id = purchaseContract.getManager();
        if (id == null) {
            return false;
        }
        long currentUserId = SystemContext.getUserId();//当前登录用户ID
        if (id.longValue() == currentUserId) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断当前登录用户是否采购跟进人
     *
     * @param contractId
     * @return boolean true是 false否
     */
    @Override
    public Boolean isPurchasingFollower(Long contractId) {
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(contractId);
        return isPurchasingFollower(purchaseContract);
    }


    /**
     * 判断当前登录用户是否采购跟进人
     *
     * @param purchaseContract
     * @return boolean true是 false否
     */
    private Boolean isPurchasingFollower(PurchaseContract purchaseContract) {
        if (purchaseContract == null) {
            return false;
        }
        Long id = purchaseContract.getPurchasingFollower();
        if (id == null) {
            return false;
        }
        long currentUserId = SystemContext.getUserId();//当前登录用户ID
        if (id.longValue() == currentUserId) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断当前登录用户是否有数据权限
     *
     * @param contractId
     * @return boolean true是 false否
     */
    @Override
    public Boolean isDataAuthor(Long contractId) {
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(contractId);
        return isDataAuthor(purchaseContract);
    }

    @Override
    public Boolean isContractIsView(Long contractId) {
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(contractId);
        return isContractIsView(purchaseContract);
    }

    private Boolean isContractIsView(PurchaseContract purchaseContract) {
        if (purchaseContract == null) {
            return false;
        }
        //只要是创建人、有数据权限、项目经理、采购跟进人
        if (isCreater(purchaseContract) || isDataAuthor(purchaseContract) || isManager(purchaseContract) || isPurchasingFollower(purchaseContract)) {
            return true;
        }
        return false;
    }

    /**
     * 判断当前登录用户是否有数据权限
     *
     * @param purchaseContract
     * @return boolean true是 false否
     */
    public Boolean isDataAuthor(PurchaseContract purchaseContract) {
        if (purchaseContract == null) {
            return false;
        }
        Long id = purchaseContract.getOuId();
        if (id == null) {
            return false;
        }
        List<Long> list = SystemContext.getOus();//当前登录用户拥有二级部门权限
        if (list == null || list.size() == 0) {
            return false;
        }
        if (list.contains(id)) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 校验器
     */
    private final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response purchaseContract(Long unitId, List<TemplatePurchaseContractExcelVO> list) {
        // 校验数据并封装失败原因
        unitId = unitId != null ? unitId : SystemContext.getUnitId();

        boolean result = validPurchaseContract(unitId, list);

        // 根据校验结果返回数据
        DataResponse<List<TemplatePurchaseContractExcelVO>> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            // 校验成功，则再次封装准备入库的数据
            for (TemplatePurchaseContractExcelVO vo : list) {
                PurchaseContract purchaseContract = BeanConverter.copy(vo, PurchaseContract.class);
                // 是否参与结转
                purchaseContract.setCarryoverFlag(vo.getCarryoverFlag() == null || vo.getCarryoverFlag() == 1);
                // 新增或更新采购合同信息
                if (purchaseContract.getId() == null) {
                    purchaseContract.setStatus(5);
                    purchaseContract.setDeletedFlag(Boolean.FALSE);
                    purchaseContract.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    purchaseContract.setCreateAt(new Date());
                    purchaseContractMapper.insertSelective(purchaseContract);
                } else {
                    purchaseContract.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    purchaseContract.setUpdateAt(new Date());
                    purchaseContractMapper.updateByPrimaryKeySelective(purchaseContract);
                }

                // 更新项目合同关联预算
                ProjectContractBudgetMaterial pcbm = vo.getProjectContractBudgetMaterial();
                if (pcbm != null) {
                    if (pcbm.getId() == null) {
                        pcbm.setPurchaseContractId(purchaseContract.getId());
                        pcbm.setCode(purchaseContract.getCode());
                        pcbm.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                        pcbm.setCreateAt(new Date());
                        pcbm.setDeletedFlag(Boolean.FALSE);
                        projectContractBudgetMaterialMapper.insert(pcbm);
                    } else {
                        pcbm.setPurchaseContractId(purchaseContract.getId());
                        pcbm.setCode(purchaseContract.getCode());
                        pcbm.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                        pcbm.setUpdateAt(new Date());
                        projectContractBudgetMaterialMapper.updateByPrimaryKeySelective(pcbm);
                    }
                }
            }
        } else {
            dataResponse.setMsg("FAIL");
            dataResponse.setData(list.stream().filter(e -> StringUtils.isNotEmpty(e.getValidResult())).collect(Collectors.toList()));
        }
        return dataResponse;
    }

    private boolean validPurchaseContract(Long unitId, List<TemplatePurchaseContractExcelVO> list) {

        list.forEach(c -> c.setValidResult(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (list.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getValidResult()))) {
            return false;
        }

        Set<String> usernames = new HashSet<>(list.size());
        list.forEach(e -> {
            usernames.add(e.getPurchasingFollowerMip());
            usernames.add(e.getManagerMip());
        });
        Map<String, UserInfoDto> userMap = getUserByUsernames(usernames).stream()
                .collect(Collectors.toMap(UserInfoDto::getUsername, e -> e));

        // 查询合同类型
        Set<String> productTypeSet = organizationCustomDictService.queryByName("采购合同类型", unitId, OrgCustomDictOrgFrom.COMPANY);
        // 查询业务实体
        Map<String, Long> ouMap = basedataExtService.queryCurrentUnitOu(unitId).stream()
                .collect(Collectors.toMap(OperatingUnitDto::getOperatingUnitName,
                        OperatingUnitDto::getOperatingUnitId, (key1, key2) -> key2));

        List<String> codes = list.stream().map(TemplatePurchaseContractExcelVO::getCode).collect(Collectors.toList());
        PurchaseContractExample pcExample = new PurchaseContractExample();
        pcExample.createCriteria().andDeletedFlagEqualTo(false).andCodeIn(codes);
        Map<String, Long> purchaseContractMap = purchaseContractMapper.selectByExample(pcExample)
                .stream().collect(Collectors.toMap(PurchaseContract::getCode, PurchaseContract::getId));

        List<String> projectCodes = list.stream().map(TemplatePurchaseContractExcelVO::getProjectCode).distinct().collect(Collectors.toList());
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andDeletedFlagEqualTo(false).andCodeIn(projectCodes);
        Map<String, Project> projectMap = projectMapper.selectByExample(projectExample).stream().collect(Collectors.toMap(Project::getCode, e -> e));

        List<Long> projectIds = projectMap.values().stream().map(Project::getId).distinct().collect(Collectors.toList());
        Map<Long, List<ProjectBudgetMaterial>> projectBudgetMaterialMap;
        Map<Long, Map<String, ProjectContractBudgetMaterial>> pcbmMap = new HashMap<>();
        if (projectIds.isEmpty()) {
            projectBudgetMaterialMap = new HashMap<>(0);
        } else {
            ProjectBudgetMaterialExample pbmExample = new ProjectBudgetMaterialExample();
            pbmExample.createCriteria()
                    .andProjectIdIn(projectIds)
                    .andDeletedFlagEqualTo(Boolean.FALSE)
                    .andExtEqualTo(Boolean.TRUE);
            projectBudgetMaterialMap = projectBudgetMaterialMapper.selectByExample(pbmExample).stream()
                    .collect(Collectors.groupingBy(ProjectBudgetMaterial::getProjectId));

            ProjectContractBudgetMaterialExample pcbmExample = new ProjectContractBudgetMaterialExample();
            pcbmExample.createCriteria()
                    .andDeletedFlagEqualTo(Boolean.FALSE)
                    .andProjectIdIn(projectIds);
            projectContractBudgetMaterialMapper.selectByExample(pcbmExample).forEach(e -> {
                pcbmMap.computeIfAbsent(e.getProjectId(), k -> new HashMap<>()).put(e.getCode(), e);
            });
        }

        List<String> vendorCodes = list.stream().map(TemplatePurchaseContractExcelVO::getVendorCode).distinct().collect(Collectors.toList());
        Map<String, List<VendorSiteBankDto>> vendorSiteBankGroup = getVendorSiteBankByCodes(vendorCodes).stream()
                .collect(Collectors.groupingBy(VendorSiteBankDto::getVendorCode));

        for (TemplatePurchaseContractExcelVO vo : list) {
            List<String> validResultList = new ArrayList<>();

            vo.setId(purchaseContractMap.get(vo.getCode()));

            // 校验合同类型
            if (!productTypeSet.contains(vo.getTypeName())) {
                validResultList.add("采购合同类型不存在");
            }
            // 校验项目编码并赋值项目id
            Project project = projectMap.get(vo.getProjectCode());
            if (project != null) {
                vo.setProjectId(project.getId());
                // 关联预算
                List<ProjectBudgetMaterial> projectBudgetMaterials = projectBudgetMaterialMap.get(project.getId());
                ProjectBudgetMaterial pbm = null;
                if (ListUtils.isNotEmpty(projectBudgetMaterials)) {
                    if (vo.getModel() != null) {
                        Optional<ProjectBudgetMaterial> first =
                                projectBudgetMaterials.stream().filter(e -> vo.getModel().equals(e.getName())).findFirst();
                        if (first.isPresent()) {
                            pbm = first.get();
                        }
                    } else {
                        pbm = projectBudgetMaterials.get(0);
                    }
                }
                if (pbm != null) {
                    // 项目采购合同关联预算记录
                    ProjectContractBudgetMaterial pcbm = pcbmMap.get(project.getId()).get(vo.getCode());
                    if (pcbm == null) {
                        pcbm = new ProjectContractBudgetMaterial();
                        pcbm.setProjectId(project.getId());
                        pcbm.setMaterialId(pbm.getId());
                        pcbm.setName(pbm.getName());
                        pcbm.setUnit(pbm.getUnit());
                        pcbm.setNumber(pbm.getNumber());
                        pcbm.setMoney(vo.getAmount());
                        pcbm.setExcludingTaxAmount(vo.getExcludingTaxAmount());
                        pcbm.setDeletedFlag(false);
                        pcbm.setStatus(false);
                    } else {
                        pcbm.setMaterialId(pbm.getId());
                        pcbm.setName(pbm.getName());
                        pcbm.setUnit(pbm.getUnit());
                        pcbm.setNumber(pbm.getNumber());
                        pcbm.setMoney(vo.getAmount());
                        pcbm.setExcludingTaxAmount(vo.getExcludingTaxAmount());
                    }
                    vo.setProjectContractBudgetMaterial(pcbm);
                }
            } else {
                validResultList.add("项目编码不存在或存在多个");
            }

            // 校验业务实体并赋值id
            Long ouId = ouMap.get(vo.getOuName());
            if (ouId != null) {
                vo.setOuId(ouId);
                // 校验供应商名称编码、赋值id、地点
                List<VendorSiteBankDto> vendorSiteBankDtos = vendorSiteBankGroup.get(vo.getVendorCode());
                if (ListUtils.isNotEmpty(vendorSiteBankDtos)) {
                    Optional<VendorSiteBankDto> first = vendorSiteBankDtos.stream().filter(e ->
                            ouId.equals(e.getOperatingUnitId()) &&
                                    vo.getVendorName().equals(e.getVendorName()) &&
                                    vo.getVendorSiteCode().equals(e.getVendorSiteCode())).findFirst();
                    if (first.isPresent()) {
                        VendorSiteBankDto vendorSiteBankDto = first.get();
                        vo.setVendorId(first.get().getId());
                        vo.setErpVendorSiteId(vendorSiteBankDto.getErpVendorSiteId() + "");
                    } else {
                        validResultList.add(vo.getVendorCode() + "供应商数据不匹配");
                    }
                } else {
                    validResultList.add(vo.getVendorCode() + "供应商不存在");
                }
            } else {
                validResultList.add("当前使用单位无此业务实体");
            }

            // 校验币种
            // 校验采购跟进人
            UserInfoDto user = userMap.get(vo.getPurchasingFollowerMip());
            if (user == null) {
                validResultList.add("采购跟进人MIP账号不存在");
            } else {
                vo.setPurchasingFollower(user.getId());
            }

            // 项目经理
            user = userMap.get(vo.getManagerMip());
            if (user != null) {
                vo.setManager(user.getId());
                vo.setManagerName(user.getName());
            }

            vo.setValidResult(Joiner.on("，").skipNulls().join(validResultList));
        }
        return list.stream().allMatch(e -> StringUtils.isEmpty(e.getValidResult()));
    }

    private List<UserInfoDto> getUserByUsernames(Set<String> usernames) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "employeeInfo/listUserByUserNames";
        String res = restTemplate.postForObject(url, usernames, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<UserInfoDto>>() {
        });
    }

    /**
     * 根据供应商编号查询供应商
     */
    private List<VendorSiteBankDto> getVendorSiteBankByCodes(List<String> codes) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "vendor/getVendorSiteBankByCodes";
        String res = restTemplate.postForObject(url, codes, String.class);
        return JSON.parseObject(res, new TypeReference<List<VendorSiteBankDto>>() {
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response purchaseContractDetail(List<TemplatePurchaseContractDetailExcelVO> list) {
        // 校验数据并封装失败原因
        boolean result = validPurchaseContractDetail(list);

        // 根据校验结果返回数据
        DataResponse<List<TemplatePurchaseContractDetailExcelVO>> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            // 校验成功，则再次封装准备入库的数据
            for (TemplatePurchaseContractDetailExcelVO vo : list) {
                PurchaseContractDetail purchaseContractDetail = BeanConverter.copy(vo, PurchaseContractDetail.class);
                purchaseContractDetail.setDeletedFlag(false);
                purchaseContractDetail.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                purchaseContractDetail.setCreateAt(new Date());
                purchaseContractDetailMapper.insert(purchaseContractDetail);
            }
            // 更新采购合同和含税及不含税金额
            List<Long> contractIds = list.stream().map(TemplatePurchaseContractDetailExcelVO::getContractId).distinct().collect(Collectors.toList());
            purchaseContractExtMapper.updateAmountByContractIds(contractIds);
        } else {
            dataResponse.setMsg("FAIL");
            dataResponse.setData(list.stream().filter(e -> StringUtils.isNotEmpty(e.getValidResult())).collect(Collectors.toList()));
        }
        return dataResponse;
    }

    private boolean validPurchaseContractDetail(List<TemplatePurchaseContractDetailExcelVO> list) {

        list.forEach(c -> c.setValidResult(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (list.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getValidResult()))) {
            return false;
        }

        List<String> taxRates = list.stream().map(TemplatePurchaseContractDetailExcelVO::getTaxRate).distinct().collect(Collectors.toList());
        Map<String, TaxInfoDto> taxInfoMap = getTaxInfoByTaxRates(taxRates, 1).stream()
                .collect(Collectors.toMap(TaxInfoDto::getTaxRate, e -> e));

        // 查询基础数据
        // 获取计量单位
        Map<String, String> unitMap = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null)
                .stream().collect(Collectors.toMap(DictDto::getName, DictDto::getCode, (key1, key2) -> key2));

        List<String> pcCodes = list.stream().map(TemplatePurchaseContractDetailExcelVO::getCode).distinct().collect(Collectors.toList());
        Map<String, PurchaseContract> purchaseContractMap = purchaseContractExtMapper.selectByCodes(pcCodes).stream()
                .collect(Collectors.toMap(PurchaseContract::getCode, e -> e));

        for (TemplatePurchaseContractDetailExcelVO vo : list) {

            List<String> validResultList = new ArrayList<>();
            // 校验合同编码新，赋值合同ID
            PurchaseContract purchaseContract = purchaseContractMap.get(vo.getCode());
            if (purchaseContract != null) {
                vo.setContractId(purchaseContract.getId());
            } else {
                validResultList.add("PAM合同不存在");
            }

            // 校验计量单位
            vo.setUnitCode(unitMap.get(vo.getUnitName()));
            if (vo.getUnitCode() == null) {
                validResultList.add("计量单位不存在");
            }

            TaxInfoDto taxInfoDto = taxInfoMap.get(vo.getTaxRate());
            if (taxInfoDto != null) {
                vo.setTaxId(taxInfoDto.getId());
            } else {
                validResultList.add("税率不存在");
            }
            vo.setNumber(vo.getNumber_().intValue());
            vo.setValidResult(String.join("，", validResultList));
        }
        return list.stream().allMatch(e -> StringUtils.isEmpty(e.getValidResult()));
    }

    private List<TaxInfoDto> getTaxInfoByTaxRates(List<String> taxRates, Integer taxType) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "finance/taxInfo/getTaxInfoByTaxRate?taxType=" + taxType;
        String res = restTemplate.postForObject(url, taxRates, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<TaxInfoDto>>() {
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response paymentPlan(List<TemplatePaymentPlanExcelVO> list) {
        // 校验数据并封装失败原因
        boolean result = validPaymentPlan(list);

        // 根据校验结果返回数据
        DataResponse<List<TemplatePaymentPlanExcelVO>> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            // 校验成功，则再次封装准备入库的数据
            for (TemplatePaymentPlanExcelVO paymentPlanExcelVO : list) {
                PaymentPlan paymentPlan = BeanConverter.copy(paymentPlanExcelVO, PaymentPlan.class);
                if (paymentPlan.getId() == null) {
                    paymentPlan.setDeletedFlag(false);
                    paymentPlan.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    paymentPlan.setCreateAt(new Date());
                    paymentPlanMapper.insert(paymentPlan);
                } else {
                    paymentPlan.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    paymentPlan.setUpdateAt(new Date());
                    paymentPlanMapper.updateByPrimaryKeySelective(paymentPlan);
                }
                logger.info("导入付款计划模板:{}", JSON.toJSONString(paymentPlan));
            }
            // 更新笔数
            List<Long> contractIds = list.stream().map(TemplatePaymentPlanExcelVO::getContractId).collect(Collectors.toList());
            PaymentPlanExample planExample = new PaymentPlanExample();
            planExample.createCriteria().andIdIn(contractIds);
            List<PaymentPlan> paymentPlans = paymentPlanMapper.selectByExample(planExample);
            Map<Long, List<PaymentPlan>> paymentPlanGroup = paymentPlans.stream().collect(Collectors.groupingBy(PaymentPlan::getContractId));
            List<PaymentPlan> planList = new ArrayList<>(paymentPlans.size());
            paymentPlanGroup.values().forEach(group -> {
                group.sort((e1, e2) -> {
                    if (e1.getNum() == null && e2.getNum() != null) {
                        return -1;
                    }
                    if (e2.getNum() != null) {
                        return e2.getNum() - e1.getNum();
                    }
                    return 1;
                });
                int num = 1;
                for (PaymentPlan p : group) {
                    PaymentPlan plan = new PaymentPlan();
                    plan.setId(p.getId());
                    plan.setNum(num++);
                    planList.add(plan);
                }
            });
            planList.forEach(p -> paymentPlanMapper.updateByPrimaryKeySelective(p));
        } else {
            dataResponse.setMsg("FAIL");
            dataResponse.setData(list.stream().filter(e -> StringUtils.isNotEmpty(e.getValidResult())).collect(Collectors.toList()));
        }
        return dataResponse;
    }

    private boolean validPaymentPlan(List<TemplatePaymentPlanExcelVO> list) {

        list.forEach(c -> c.setValidResult(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (list.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getValidResult()))) {
            return false;
        }

        List<String> contractCodes = list.stream().map(TemplatePaymentPlanExcelVO::getContractCode)
                .distinct().collect(Collectors.toList());
        PurchaseContractExample contractExample = new PurchaseContractExample();
        contractExample.createCriteria().andDeletedFlagEqualTo(false).andCodeIn(contractCodes);
        Map<String, Long> purchaseContractMap = purchaseContractMapper.selectByExample(contractExample).stream()
                .collect(Collectors.toMap(PurchaseContract::getCode, PurchaseContract::getId, (e1, e2) -> e1));

        List<String> planCodes = list.stream().map(TemplatePaymentPlanExcelVO::getCode).collect(Collectors.toList());
        PaymentPlanExample planExample = new PaymentPlanExample();
        planExample.createCriteria().andDeletedFlagEqualTo(false).andCodeIn(planCodes);
        Map<String, Long> paymentPlanMap = paymentPlanMapper.selectByExample(planExample).stream()
                .collect(Collectors.toMap(PaymentPlan::getCode, PaymentPlan::getId));

        Map<String, DictDto> paymentTypeMap = getDictByType("payment_type").stream()
                .collect(Collectors.toMap(DictDto::getName, e -> e));

        // 查询基础数据
        for (TemplatePaymentPlanExcelVO vo : list) {
            List<String> validResultList = new ArrayList<>();

            // 校验合同编码新，赋值合同ID
            Long contractId = purchaseContractMap.get(vo.getContractCode());
            if (contractId != null) {
                vo.setContractId(contractId);
            } else {
                validResultList.add("PAM合同编号-新不存在或存在多个");
            }

            Long paymentPlanId = paymentPlanMap.get(vo.getCode());
            if (paymentPlanId != null) {
                vo.setId(paymentPlanId);
            }

            if (vo.getActualAmount() == null) {
                vo.setActualAmount(BigDecimal.ZERO);
            }

            DictDto paymentType = paymentTypeMap.get(vo.getPaymentMethodName());
            if (paymentType != null) {
                vo.setPaymentMethodId(paymentType.getId());
            } else {
                validResultList.add("付款方式不存在");
            }

            vo.setValidResult(Joiner.on("，").skipNulls().join(validResultList));
        }
        return list.stream().allMatch(e -> StringUtils.isEmpty(e.getValidResult()));
    }

    private List<DictDto> getDictByType(String type) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "ltcDict/listByType?type=" + type;
        String res = restTemplate.getForObject(url, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<DictDto>>() {
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response purchaseContractBudget(List<TemplatePurchaseContractBudgetExcelVO> list) {
        // 校验数据并封装失败原因
        boolean result = validPurchaseContractBudget(list);

        // 根据校验结果返回数据
        DataResponse<List<TemplatePurchaseContractBudgetExcelVO>> dataResponse = Response.dataResponse();
        if (result) {
            int batchNum = 1000;
            Long importLot = Long.valueOf(DateUtils.getNow("yyyyMMdd")); //标识导入批次

            UserInfo userInfo = CacheDataUtils.findUserById(SystemContext.getUserId());
            List<ProjectWbsReceipts> receiptsList = new ArrayList<>();
            List<ProjectWbsReceiptsBudget> parentBudgetList = new ArrayList<>();
            List<PurchaseMaterialRequirementDto> requirementList = new ArrayList<>();
            List<PurchaseContractBudget> budgetList = new ArrayList<>();
            //按需求发布单号分组
            Map<String, List<TemplatePurchaseContractBudgetExcelVO>> excelVOMap =
                    list.stream().collect(Collectors.groupingBy(TemplatePurchaseContractBudgetExcelVO::getRequirementCode));

            dataResponse.setMsg("SUCCESS");
            // 校验成功，则再次封装准备入库的数据
            /** 生成需求发布单据 **/
            for (TemplatePurchaseContractBudgetExcelVO excelVO : list) {
                ProjectWbsReceipts receipts = BeanConverter.copy(excelVO, ProjectWbsReceipts.class);
                receipts.setRequirementStatus(RequirementStatusEnum.PROCESS.getCode());
                receipts.setRequirementType(RequirementTypeEnum.REQUIREMENT_PUBLISH.getCode());
                receipts.setConfirmMode(ReceiptsConfirmModeEnum.PURCHASE.getCode());
                receipts.setProducerId(userInfo.getId());
                receipts.setProducerName(userInfo.getName());
                receipts.setHandleBy(userInfo.getId());
                receipts.setHandleName(userInfo.getName());
                receipts.setHandleAt(new Date());
                receipts.setUnitId(SystemContext.getUnitId());
                receipts.setDeletedFlag(DeletedFlag.VALID.code());
                receipts.setCreateBy(importLot);
                receiptsList.add(receipts);
            }
            //排重
            receiptsList = receiptsList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
                    -> new TreeSet<>(Comparator.comparing(ProjectWbsReceipts::getRequirementCode))), ArrayList::new));
            if (ListUtils.isNotEmpty(receiptsList)) {
                List<List<ProjectWbsReceipts>> lists = ListUtils.splistList(receiptsList, batchNum);
                for (List<ProjectWbsReceipts> splitList : lists) {
                    projectWbsReceiptsExtMapper.batchInsert(splitList);
                }
            }
            Map<String, ProjectWbsReceipts> receiptsMap = receiptsList.stream().collect(Collectors.toMap(ProjectWbsReceipts::getRequirementCode,
                    Function.identity(), (a, b) -> a));

            /** 生成需求发布预算信息 **/
            for (String requirementCode : receiptsMap.keySet()) {
                //默认1个单据生成1条记录，提供采购需求外包列表-需求预算字段取值
                ProjectWbsReceiptsBudget parentBudget = new ProjectWbsReceiptsBudget();
                parentBudget.setProjectWbsReceiptsId(receiptsMap.get(requirementCode).getId());
                parentBudget.setBudgetOccupiedAmount(excelVOMap.get(requirementCode).get(0).getExcludingTaxAmount());  //预算占用金额：合同金额不含税
                parentBudget.setWbsSummaryCode(excelVOMap.get(requirementCode).get(0).getWbsSummaryCode());  //默认先取第一个WBS号
                parentBudget.setDeletedFlag(DeletedFlag.VALID.code());
                parentBudget.setVersion(1L);
                parentBudget.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode());
                parentBudget.setCreateBy(importLot);
                parentBudgetList.add(parentBudget);
            }
            if (ListUtils.isNotEmpty(parentBudgetList)) {
                List<List<ProjectWbsReceiptsBudget>> lists = ListUtils.splistList(parentBudgetList, batchNum);
                for (List<ProjectWbsReceiptsBudget> splitList : lists) {
                    projectWbsReceiptsBudgetExtMapper.batchInsert(splitList);
                }
            }

            /** 生成外包（整包）的采购需求 **/
            for (TemplatePurchaseContractBudgetExcelVO excelVO : list) {
                PurchaseMaterialRequirementDto requirement = BeanConverter.copy(excelVO, PurchaseMaterialRequirementDto.class);
                requirement.setProjectWbsReceiptsId(receiptsMap.get(excelVO.getRequirementCode()).getId());
                requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());        //状态：待下达
                if (excelVO.getUnreleasedAmount().compareTo(BigDecimal.ZERO) == 0) {
                    requirement.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());        //当未下达量为0，状态改为:已关闭
                }
                requirement.setNeedTotal(excelVO.getUnreleasedAmount().add(excelVO.getNumber()));  //总需求量：未下达量+数量
                requirement.setMaterielId(excelVO.getMaterialId());
                requirement.setReceiptsPublishDate(new Date());
                requirement.setDeletedFlag(DeletedFlag.VALID.code());
                requirement.setCreateBy(importLot);
                requirement.setRequirementType(1);
                //wbs需求预算占用（外包）：这个合同（需求单据，一个合同对应一个需求单据）下相同WBS（该WBS）的总价（不含税）加和
                BigDecimal reduce = excelVOMap.get(excelVO.getRequirementCode()).stream()
                        .filter(s -> Objects.equals(s.getWbsSummaryCode(), excelVO.getWbsSummaryCode()))
                        .map(TemplatePurchaseContractBudgetExcelVO::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                requirement.setWbsDemandOsCost(reduce);
                requirementList.add(requirement);
            }
            if (ListUtils.isNotEmpty(requirementList)) {
                List<List<PurchaseMaterialRequirementDto>> lists = ListUtils.splistList(requirementList, batchNum);
                for (List<PurchaseMaterialRequirementDto> splitList : lists) {
                    purchaseMaterialRequirementExtMapper.batchInsert1(splitList);
                }
            }
            Map<Integer, PurchaseMaterialRequirementDto> requirementListMap =
                    requirementList.stream().collect(Collectors.toMap(PurchaseMaterialRequirementDto::getNum, Function.identity(), (a, b) -> a));

            /** 生成采购合同预算 **/
            for (TemplatePurchaseContractBudgetExcelVO excelVO : list) {
                PurchaseContractBudget budget = BeanConverter.copy(excelVO, PurchaseContractBudget.class);
                PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(excelVO.getPurchaseContractId());
                BigDecimal conversionRate = contract.getConversionRate() != null ? contract.getConversionRate() : BigDecimal.ONE;
                // 按照合同汇率给本位币单价和进度执行
                budget.setLocalTotalPrice(budget.getTotalPrice().multiply(conversionRate).setScale(2, BigDecimal.ROUND_HALF_UP));
                budget.setLocalBudgetExecuteAmountTotal(budget.getBudgetExecuteAmountTotal().multiply(conversionRate).setScale(2, BigDecimal.ROUND_HALF_UP));
                budget.setPurchaseRequirementId(requirementListMap.get(excelVO.getNum()).getId());
                budget.setDeletedFlag(DeletedFlag.VALID.code());
                budget.setCreateBy(importLot);
                budgetList.add(budget);
            }
            if (ListUtils.isNotEmpty(budgetList)) {
                List<List<PurchaseContractBudget>> lists = ListUtils.splistList(budgetList, batchNum);
                for (List<PurchaseContractBudget> splitList : lists) {
                    purchaseContractBudgetExtMapper.batchInsert(splitList);
                }
            }

            /** 生成版本历史 **/
            List<PurchaseContractChangeHeader> headerList = new ArrayList<>();
            List<PurchaseContractChangeHistory> contractChangeHistoryList = new ArrayList<>();
            List<PaymentPlanChangeHistory> paymentPlanChangeHistoryList = new ArrayList<>();
            List<PurchaseContractBudgetChangeHistory> budgetChangeHistoryList = new ArrayList<>();

            //查询所有合同
            List<Long> contractIds =
                    list.stream().map(TemplatePurchaseContractBudgetExcelVO::getPurchaseContractId).distinct().collect(Collectors.toList());
            PurchaseContractExample contractExample = new PurchaseContractExample();
            contractExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andIdIn(contractIds);
            Map<Long, PurchaseContract> contractMap =
                    purchaseContractMapper.selectByExample(contractExample).stream().collect(Collectors.toMap(PurchaseContract::getId,
                            Function.identity()));

            //生成版本历史，变更类型0：合同新增
            for (Long contractId : contractMap.keySet()) {
                PurchaseContract purchaseContract = contractMap.get(contractId);
                PurchaseContractChangeHeader header = new PurchaseContractChangeHeader();
                header.setPurchaseContractId(purchaseContract.getId());
                header.setVersionCode(purchaseContractService.generateVersionCode(header, purchaseContract.getCode())); //版本号
                header.setChangeType(PurchaseContractChangeType.BUILD.getCode());
                header.setDeletedFlag(DeletedFlag.VALID.code());
                header.setPurchaseContractId(purchaseContract.getId());
                header.setStatus(PurchaseContractChangeHeaderStatus.PASS.code());
                header.setIsElectronicContract(purchaseContract.getIsElectronicContract());
                header.setOtherName(purchaseContract.getOtherName());
                header.setOtherPhone(purchaseContract.getOtherPhone());
                header.setPublicOrPrivate(purchaseContract.getPublicOrPrivate());
                header.setSealCategory(purchaseContract.getSealCategory());
                header.setSealAdminAccountIds(purchaseContract.getSealAdminAccountIds());
                header.setCreateBy(importLot);
                headerList.add(header);
            }
            if (ListUtils.isNotEmpty(headerList)) {
                List<List<PurchaseContractChangeHeader>> lists = ListUtils.splistList(headerList, batchNum);
                for (List<PurchaseContractChangeHeader> splitList : lists) {
                    purchaseContractChangeHeaderExtMapper.batchInsert(splitList);
                }
            }
            Map<Long, Long> headerMap = headerList.stream().collect(Collectors.toMap(PurchaseContractChangeHeader::getPurchaseContractId,
                    PurchaseContractChangeHeader::getId));

            //备份合同基本信息/付款计划/预算
            for (Long contractId : contractMap.keySet()) {
                this.backupBatch(contractMap.get(contractId), headerMap.get(contractId), importLot, contractChangeHistoryList,
                        paymentPlanChangeHistoryList, budgetChangeHistoryList);
            }
            if (ListUtils.isNotEmpty(contractChangeHistoryList)) {
                List<List<PurchaseContractChangeHistory>> lists = ListUtils.splistList(contractChangeHistoryList, batchNum);
                for (List<PurchaseContractChangeHistory> splitList : lists) {
                    purchaseContractChangeHistoryExtMapper.batchInsert(splitList);
                }
            }
            if (ListUtils.isNotEmpty(paymentPlanChangeHistoryList)) {
                List<List<PaymentPlanChangeHistory>> lists = ListUtils.splistList(paymentPlanChangeHistoryList, batchNum);
                for (List<PaymentPlanChangeHistory> splitList : lists) {
                    paymentPlanChangeHistoryExtMapper.batchInsert(splitList);
                }
            }
            if (ListUtils.isNotEmpty(budgetChangeHistoryList)) {
                List<List<PurchaseContractBudgetChangeHistory>> lists = ListUtils.splistList(budgetChangeHistoryList, batchNum);
                for (List<PurchaseContractBudgetChangeHistory> splitList : lists) {
                    purchaseContractBudgetChangeHistoryExtMapper.batchInsert(splitList);
                }
            }

        } else {
            dataResponse.setMsg("FAIL");
            dataResponse.setData(list);
        }
        return dataResponse;
    }

    private boolean validPurchaseContractBudget(List<TemplatePurchaseContractBudgetExcelVO> list) {
        //非空检验
        list.forEach(c -> c.setValidResult(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));
        if (list.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getValidResult()))) return false;

        //查询所有有效组织代码
        List<OrganizationRel> organizationList = basedataExtService.getValidOrganization();
        Map<String, OrganizationRel> organizationMap = organizationList.stream().collect(Collectors.toMap(OrganizationRel::getOrganizationCode,
                Function.identity(), (a, b) -> a));

        //查询所有单位
        List<DictDto> dictDtoList = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Map<String, DictDto> unitMap = dictDtoList.stream().collect(Collectors.toMap(DictDto::getName, Function.identity()));

        //查询所有项目
        List<String> projectCodes = list.stream().map(TemplatePurchaseContractBudgetExcelVO::getProjectCode).distinct().collect(Collectors.toList());
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andCodeIn(projectCodes);
        Map<String, Project> projectMap = projectMapper.selectByExample(projectExample).stream().collect(Collectors.toMap(Project::getCode,
                Function.identity(), (a, b) -> a));

        //查询所有合同
        List<String> codes = list.stream().map(TemplatePurchaseContractBudgetExcelVO::getCode).collect(Collectors.toList());
        PurchaseContractExample contractExample = new PurchaseContractExample();
        contractExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andCodeIn(codes);
        Map<String, List<PurchaseContract>> contractMap =
                purchaseContractMapper.selectByExample(contractExample).stream().collect(Collectors.groupingBy(PurchaseContract::getCode));

        //查询所有单据
        List<String> requirementCodes =
                list.stream().map(TemplatePurchaseContractBudgetExcelVO::getRequirementCode).distinct().collect(Collectors.toList());
        ProjectWbsReceiptsExample receiptsExample = new ProjectWbsReceiptsExample();
        receiptsExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andRequirementCodeIn(requirementCodes);
        List<String> requirementCodeExistList =
                projectWbsReceiptsMapper.selectByExampleWithBLOBs(receiptsExample).stream().map(ProjectWbsReceipts::getRequirementCode).collect(Collectors.toList());

        Map<String, MaterialDto> materialMap = new HashMap<>();
        String validResultCollect = "";

        // 查询基础数据
        for (TemplatePurchaseContractBudgetExcelVO excelVO : list) {
            List<String> validResultList = new ArrayList<>();
            //防重
            if (requirementCodeExistList.contains(excelVO.getRequirementCode())) {
                validResultList.add("需求发布单据编号已存在");
            }

            // 项目号-新
            Project project = projectMap.get(excelVO.getProjectCode());
            if (project != null) {
                excelVO.setProjectId(project.getId());
                excelVO.setProjectName(project.getName());
            } else {
                validResultList.add("项目号-新不存在");
            }

            // PAM合同编号-新
            List<PurchaseContract> contracts = contractMap.get(excelVO.getCode());
            if (ListUtil.isPresent(contracts) && contracts.size() == 1) {
                PurchaseContract contract = contracts.get(0);
                excelVO.setPurchaseContractId(contract.getId());
                excelVO.setExcludingTaxAmount(contract.getExcludingTaxAmount());
            } else {
                validResultList.add("PAM合同编号-新不存在或存在多个");
            }

            // 组织代码
            OrganizationRel organizationRel = organizationMap.get(excelVO.getOrganizationCode());
            if (Objects.nonNull(organizationRel)) {
                excelVO.setOrganizationId(organizationRel.getOrganizationId());
            } else {
                validResultList.add("组织代码不存在");
            }

            // 物料
            if (excelVO.getOrganizationId() != null) {
                String key = excelVO.getPamCode() + "@" + excelVO.getOrganizationId();
                MaterialDto materialDto = materialMap.get(key);
                if (materialDto == null) {
                    materialDto = materialExtService.invokeMaterialApiGetByPamCode(excelVO.getPamCode(), excelVO.getOrganizationId());
                    if (Objects.nonNull(materialDto)) {
                        excelVO.setMaterialId(materialDto.getId());
                        excelVO.setMaterielDescr(materialDto.getItemInfo());
                        materialDto.setExistFlag(Boolean.TRUE);
                        materialMap.put(key, materialDto);
                    } else {
                        validResultList.add("PAM编码-新对应的物料不存在");
                        materialMap.put(key, new MaterialDto(Boolean.FALSE));
                    }
                } else {
                    if (materialDto.getExistFlag()) {
                        excelVO.setMaterialId(materialDto.getId());
                        excelVO.setMaterielDescr(materialDto.getItemInfo());
                    } else {
                        validResultList.add("PAM编码-新对应的物料不存在");
                    }
                }
            }

            // 单位-新
            DictDto dictDto = unitMap.get(excelVO.getUnit());
            if (Objects.nonNull(dictDto)) {
                excelVO.setUnitCode(dictDto.getCode());
            } else {
                validResultList.add("单位-新对应的单位编码不存在");
            }

            // 是否急件
            if (Objects.equals(excelVO.getDispatchIsStr(), "是")) {
                excelVO.setDispatchIs(Boolean.TRUE);
            } else if (Objects.equals(excelVO.getDispatchIsStr(), "否")) {
                excelVO.setDispatchIs(Boolean.FALSE);
            }

            // 是否外包
            if (Objects.equals(excelVO.getExtIsStr(), "是")) {
                excelVO.setExtIs(Boolean.TRUE);
                excelVO.setPurchaseType(PurchaseMaterialRequirementPurchaseTypeEnums.OUTSOURCE.getCode()); //外包（整包）需求
            } else if (Objects.equals(excelVO.getExtIsStr(), "否")) {
                excelVO.setExtIs(Boolean.FALSE);
                excelVO.setPurchaseType(PurchaseMaterialRequirementPurchaseTypeEnums.WBS.getCode()); //物料采购需求（WBS）
            }

            String validResult = Joiner.on("，").skipNulls().join(validResultList);
            if (StringUtils.isNotEmpty(validResult)) {
                validResultCollect = String.format("%s【%s】%s", validResultCollect, excelVO.getNum(), validResult);
            }
            excelVO.setValidResult(validResultCollect);
        }
        return list.stream().allMatch(e -> StringUtils.isEmpty(e.getValidResult()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response purchaseContractProgress(List<TemplatePurchaseContractProgressExcelVO> list) {
        // 校验数据并封装失败原因
        boolean result = validPurchaseContractProgress(list);

        // 根据校验结果返回数据
        DataResponse<List<TemplatePurchaseContractProgressExcelVO>> dataResponse = Response.dataResponse();
        if (result) {
            int batchNum = 1000;
            Long importLot = Long.valueOf(DateUtils.getNow("yyyyMMdd")); //标识导入批次

            List<PurchaseContractProgressDto> progressList = new ArrayList<>();
            List<PurchaseContractProgressBudgetRel> relList = new ArrayList<>();
            //按合同分组
            Map<Long, List<TemplatePurchaseContractProgressExcelVO>> excelVOMap =
                    list.stream().collect(Collectors.groupingBy(TemplatePurchaseContractProgressExcelVO::getPurchaseContractId));

            dataResponse.setMsg("SUCCESS");
            // 校验成功，则再次封装准备入库的数据
            /** 保存合同进度信息 **/
            /** 约定合同若存在多条进度，在同一批次导入，累计进度执行金额相应累加 **/
            for (TemplatePurchaseContractProgressExcelVO excelVO : list) {
                PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(excelVO.getPurchaseContractId());
                PurchaseContractProgressDto progress = BeanConverter.copy(excelVO, PurchaseContractProgressDto.class);
                // 合同进度的汇率信息按照采购合同的汇率信息来
                progress.setConversionDate(contract.getConversionDate());
                progress.setConversionRate(contract.getConversionRate());
                progress.setConversionType(contract.getConversionType());
                //采购合同进度的序号列表
                List<Integer> numList =
                        excelVOMap.get(excelVO.getPurchaseContractId()).stream().map(TemplatePurchaseContractProgressExcelVO::getNum).sorted(Comparator.naturalOrder()).collect(Collectors.toList());

                progress.setProgressCode(generateProgressCodeImport(contract.getCode(), numList.indexOf(excelVO.getNum()) + 1)); //进度执行单号
                progress.setProgressStatus(PurchaseContractProgressStatus.PASS.getCode());    //状态：通过
                progress.setExcludingTaxAmount(contract.getExcludingTaxAmount());             //当期采购合同金额（不含税）
                if (progress.getExecuteAmount() != null && progress.getExcludingTaxAmount() != null && progress.getExcludingTaxAmount().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal divide = progress.getExecuteAmount().divide(progress.getExcludingTaxAmount(), 4, BigDecimal.ROUND_HALF_UP);
                    progress.setExecutePercent(divide.multiply(new BigDecimal(100)));     //当期进度执行百分比
                }
                BigDecimal reduce = excelVOMap.get(excelVO.getPurchaseContractId()).stream()
                        .filter(s -> s.getNum() <= excelVO.getNum())
                        .map(TemplatePurchaseContractProgressExcelVO::getExecuteAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                progress.setExecuteAmountTotal(reduce);
                BigDecimal reduceCNY = excelVOMap.get(excelVO.getPurchaseContractId()).stream()
                        .filter(s -> s.getNum() <= excelVO.getNum())
                        .map(TemplatePurchaseContractProgressExcelVO::getExecuteAmountCNY).reduce(BigDecimal.ZERO, BigDecimal::add);
                progress.setLocalBudgetExecuteAmountTotal(reduceCNY);                                           //累计进度执行金额（不含税）
                if (progress.getExecuteAmountTotal() != null && progress.getExcludingTaxAmount() != null && progress.getExcludingTaxAmount().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal divide1 = progress.getExecuteAmountTotal().divide(progress.getExcludingTaxAmount(), 4, BigDecimal.ROUND_HALF_UP);
                    progress.setExecutePercentTotal(divide1.multiply(new BigDecimal(100)));   //累计进度执行百分比
                }
                progress.setApproveAt(new Date());
                progress.setProjectId(contract.getProjectId());
                progress.setCollectionStatus(PurchaseContractProgressCollectionStatus.UNCOLLECTED.getCode());
                progress.setDeletedFlag(DeletedFlag.VALID.code());
                progress.setCreateBy(importLot);
                progressList.add(progress);

//                //查找合同金额为0的数据
//                if (progress.getExecutePercent() == null) {
//                    dataResponse.setMsg(dataResponse.getMsg() + excelVO.getNum() + "；");
//                }
            }
            if (ListUtils.isNotEmpty(progressList)) {
                List<List<PurchaseContractProgressDto>> lists = ListUtils.splistList(progressList, batchNum);
                for (List<PurchaseContractProgressDto> splitList : lists) {
                    purchaseContractProgressExtMapper.batchInsert(splitList);
                }
            }
            Map<Integer, PurchaseContractProgressDto> progressMap =
                    progressList.stream().collect(Collectors.toMap(PurchaseContractProgressDto::getNum, Function.identity(), (a, b) -> a));

            /** 生成采购合同进度预算关联 **/
            /** 模板【采购合同关联外包物料需求】要先于【采购合同进度】导入，不然采购合同进度预算关联会没数据 **/
            for (TemplatePurchaseContractProgressExcelVO excelVO : list) {
                PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(excelVO.getPurchaseContractId());
                PurchaseContractProgressDto progress = progressMap.get(excelVO.getNum());
                BigDecimal executeAmount = progress.getExecuteAmount();
                BigDecimal executeAmountCNY = excelVO.getExecuteAmountCNY(); // 本位币
                BigDecimal executeAmountTotal = progress.getExecuteAmountTotal();
                BigDecimal localBudgetExecuteAmountTotal = progress.getLocalBudgetExecuteAmountTotal(); // 累计本位币

                // 查询采购合同预算
                PurchaseContractBudgetExample budgetExample = new PurchaseContractBudgetExample();
                budgetExample.createCriteria().andPurchaseContractIdEqualTo(contract.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<PurchaseContractBudget> budgetList = purchaseContractBudgetMapper.selectByExample(budgetExample);
                BigDecimal budgetSummary = budgetList.stream().map(PurchaseContractBudget::getBudgetExecuteAmountTotal).reduce(BigDecimal.ZERO,
                        BigDecimal::add);

                if (ListUtil.isPresent(budgetList)) {
                    for (PurchaseContractBudget budget : budgetList) {
                        PurchaseContractProgressBudgetRel rel = new PurchaseContractProgressBudgetRel();
                        rel.setPurchaseContractId(contract.getId());
                        rel.setPurchaseContractProgressId(progress.getId());
                        rel.setPurchaseContractBudgetId(budget.getId());
                        if (budgetSummary.compareTo(BigDecimal.ZERO) > 0) {
                            rel.setBudgetExecuteAmount(executeAmount.multiply(budget.getBudgetExecuteAmountTotal()).divide(budgetSummary, 2,
                                    BigDecimal.ROUND_HALF_UP));
                            rel.setBudgetExecuteAmountTotal(executeAmountTotal.multiply(budget.getBudgetExecuteAmountTotal()).divide(budgetSummary,
                                    2, BigDecimal.ROUND_HALF_UP));

                            rel.setLocalBudgetExecuteAmount(executeAmountCNY.multiply(budget.getBudgetExecuteAmountTotal()).divide(budgetSummary, 2,
                                    BigDecimal.ROUND_HALF_UP));
                            rel.setLocalBudgetExecuteAmountTotal(localBudgetExecuteAmountTotal.multiply(budget.getBudgetExecuteAmountTotal()).divide(budgetSummary,
                                    2, BigDecimal.ROUND_HALF_UP));
                        } else {
                            rel.setBudgetExecuteAmount(BigDecimal.ZERO);
                            rel.setLocalBudgetExecuteAmount(BigDecimal.ZERO);
                            rel.setBudgetExecuteAmountTotal(BigDecimal.ZERO);
                            rel.setLocalBudgetExecuteAmountTotal(BigDecimal.ZERO);
                        }
                        rel.setDeletedFlag(DeletedFlag.VALID.code());
                        rel.setCreateBy(importLot);
                        relList.add(rel);
                    }
                }
            }
            if (ListUtils.isNotEmpty(relList)) {
                List<List<PurchaseContractProgressBudgetRel>> lists = ListUtils.splistList(relList, batchNum);
                for (List<PurchaseContractProgressBudgetRel> splitList : lists) {
                    purchaseContractProgressBudgetRelExtMapper.batchInsert(splitList);
                }
            }

        } else {
            dataResponse.setMsg("FAIL");
            dataResponse.setData(list);
        }
        return dataResponse;
    }

    private boolean validPurchaseContractProgress(List<TemplatePurchaseContractProgressExcelVO> list) {
        //非空检验
        list.forEach(c -> c.setValidResult(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));
        if (list.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getValidResult()))) return false;

        String validResultCollect = "";

        //查询所有合同
        List<String> codes = list.stream().map(TemplatePurchaseContractProgressExcelVO::getCode).distinct().collect(Collectors.toList());
        PurchaseContractExample contractExample = new PurchaseContractExample();
        contractExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andCodeIn(codes);
        List<PurchaseContract> contractList = purchaseContractMapper.selectByExample(contractExample);
        Map<String, List<PurchaseContract>> contractMap = contractList.stream().collect(Collectors.groupingBy(PurchaseContract::getCode));

        //查询所有单据
//        List<Long> contractIds = contractList.stream().map(PurchaseContract::getId).distinct().collect(Collectors.toList());
//        PurchaseContractProgressExample progressExample = new PurchaseContractProgressExample();
//        progressExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andPurchaseContractIdIn(contractIds);
//        List<Long> progressExistList = purchaseContractProgressMapper.selectByExample(progressExample).stream().map
//        (PurchaseContractProgress::getPurchaseContractId).collect(Collectors.toList());

        // 查询基础数据
        for (TemplatePurchaseContractProgressExcelVO excelVO : list) {
            List<String> validResultList = new ArrayList<>();

            // 校验合同编码新，赋值合同ID/项目ID
            List<PurchaseContract> contracts = contractMap.get(excelVO.getCode());
            if (ListUtil.isPresent(contracts) && contracts.size() == 1) {
                PurchaseContract contract = contracts.get(0);
                excelVO.setPurchaseContractId(contract.getId());
                excelVO.setProjectId(contract.getProjectId());
            } else {
                validResultList.add("PAM合同编号-新不存在或存在多个");
            }

            //防重，约定每个合同只导入一条合同进度
//            if (excelVO.getPurchaseContractId() != null && progressExistList.contains(excelVO.getPurchaseContractId())) {
//                validResultList.add("PAM合同编号-新已导入过采购合同进度");
//            }

            // 校验合同进度执行结束时间
            if (excelVO.getProgressStartTime() != null && excelVO.getProgressEndTime() != null
                    && excelVO.getProgressEndTime().before(excelVO.getProgressStartTime())) {
                validResultList.add("合同进度执行时间-至不能早于合同进度执行时间-从");
            }

            String validResult = Joiner.on("，").skipNulls().join(validResultList);
            if (StringUtils.isNotEmpty(validResult)) {
                validResultCollect = String.format("%s【%s】%s", validResultCollect, excelVO.getNum(), validResult);
            }
            excelVO.setValidResult(validResultCollect);
        }
        return list.stream().allMatch(e -> StringUtils.isEmpty(e.getValidResult()));
    }

    @Override
    public PurchaseContractProgressVo getProgressList(PurchaseContractProgressDto dto) {
        Asserts.notEmpty(dto.getPurchaseContractId(), ErrorCode.CTC_PURCHASE_CONTRACT_ID_NOT_NULL);
        paramsHandle(dto);
        List<PurchaseContractProgress> progressList = purchaseContractProgressMapper.selectByExample(buildProgressCondition(dto));
        //计算当前累计进度执行金额和采购合同金额
        PurchaseContractProgressVo progressVo = this.calculateExecuteAmountTotal(dto.getPurchaseContractId());
        //处理合同不含税金额减去罚扣不含税金额总和
        punishmentAmountHandle(dto, progressVo, progressList);

        //采购合同进度列表
        List<PurchaseContractProgressDto> progressDtos = BeanConverter.copy(progressList, PurchaseContractProgressDto.class);
        progressDtos.forEach(s -> s.setCreateBy(s.getCreateBy()));
        progressVo.setProgressList(progressDtos);
        return progressVo;
    }

    /**
     * 处理合同不含税金额减去罚扣不含税金额总和
     *
     * @param dto
     * @param progressVo
     * @param progressList
     */
    private void punishmentAmountHandle(PurchaseContractProgressDto dto, PurchaseContractProgressVo progressVo, List<PurchaseContractProgress> progressList) {
        List<PunishmentAmountDTO> punishmentAmountDTOList = purchaseContractPunishmentExtMapper.getEffectivePunishmentAmountByContractId(Lists.newArrayList(dto.getPurchaseContractId()));
        if (ListUtils.isNotEmpty(punishmentAmountDTOList)) {
            BigDecimal excludingTaxAmountHeader = progressVo.getExcludingTaxAmountHeader();
            PunishmentAmountDTO punishmentAmountDTO = punishmentAmountDTOList.get(0);
            BigDecimal notTaxAmount = punishmentAmountDTO.getNotTaxAmount();

            for (PurchaseContractProgress purchaseContractProgress : progressList) {
                Long purchaseContractId = purchaseContractProgress.getPurchaseContractId();
                BigDecimal excludingTaxAmount = purchaseContractProgress.getExcludingTaxAmount();
                punishmentAmountDTOList.stream()
                        .filter(punishmentAmountDTO1 -> punishmentAmountDTO1.getContractId().equals(purchaseContractId))
                        .findFirst().ifPresent(amount -> {
                            BigDecimal notTaxAmount1 = amount.getNotTaxAmount();
                            purchaseContractProgress.setExcludingTaxAmount(excludingTaxAmount.subtract(notTaxAmount1));
                        });
            }
        }
    }

    @Override
    public PurchaseContractProgressVo calculateExecuteAmountTotal(Long purchaseContractId) {
        PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);
        Asserts.notEmpty(contract, ErrorCode.CTC_PURCHASE_CONTRACT_NOT_EXISTS);

        PurchaseContractProgressVo progressVo = new PurchaseContractProgressVo();
        BigDecimal executeAmountTotal = BigDecimal.ZERO;
        BigDecimal punishmentNotTaxAmount = BigDecimal.ZERO;
        PurchaseContractProgressExample example = new PurchaseContractProgressExample();
        example.createCriteria().andPurchaseContractIdEqualTo(purchaseContractId)
                .andProgressStatusEqualTo(PurchaseContractProgressStatus.PASS.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractProgress> progressList = purchaseContractProgressMapper.selectByExample(example);

        if (ListUtil.isPresent(progressList)) {
            //计算累计进度执行金额（不含税）
            for (PurchaseContractProgress progress : progressList) {
                executeAmountTotal = executeAmountTotal.add(progress.getExecuteAmount());
            }
            List<Long> progressIdList = progressList.stream().map(PurchaseContractProgress::getId).collect(Collectors.toList());
            progressVo.setProgressIdList(progressIdList);
        }

        //加入关联罚扣金额（不含税）
        List<PunishmentAmountDTO> punishmentAmountDTOList = purchaseContractPunishmentExtMapper.getEffectivePunishmentAmountByContractId(Lists.newArrayList(purchaseContractId));
        if (ListUtils.isNotEmpty(punishmentAmountDTOList)) {
            punishmentNotTaxAmount = punishmentAmountDTOList.get(0).getNotTaxAmount();
        }

        //计算当前累计进度执行金额（不含税）
        progressVo.setExecuteAmountTotalHeader(executeAmountTotal);
        //当前采购合同金额（不含税）
        progressVo.setExcludingTaxAmountHeader(contract.getExcludingTaxAmount().subtract(punishmentNotTaxAmount));
        //累计进度执行百分比
        progressVo.setExecuteContractPercentTotal(BigDecimal.ZERO);
        if (Objects.nonNull(progressVo.getExcludingTaxAmountHeader()) && progressVo.getExcludingTaxAmountHeader().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal divide = executeAmountTotal.divide(progressVo.getExcludingTaxAmountHeader(), 4, BigDecimal.ROUND_HALF_UP); //取4位小数四舍五入
            progressVo.setExecuteContractPercentTotal(divide.multiply(new BigDecimal(100)));
        }
        return progressVo;
    }

    /**
     * 构建查询条件
     */
    private PurchaseContractProgressExample buildProgressCondition(PurchaseContractProgressDto dto) {
        PurchaseContractProgressExample example = new PurchaseContractProgressExample();
        example.setOrderByClause(" create_at ");
        PurchaseContractProgressExample.Criteria criteria = example.createCriteria();
        criteria.andPurchaseContractIdEqualTo(dto.getPurchaseContractId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        if (StringUtils.isNotEmpty(dto.getProgressCode())) {
            criteria.andProgressCodeLike("%" + dto.getProgressCode() + "%");
        }
        if (ListUtil.isPresent(dto.getProgressStatusList())) {
            criteria.andProgressStatusIn(dto.getProgressStatusList());
        }
        if (StringUtils.isNotEmpty(dto.getFrontStartTimeStr())) {
            criteria.andProgressStartTimeGreaterThanOrEqualTo(DateUtils.parse(dto.getFrontStartTimeStr()));
        }
        if (StringUtils.isNotEmpty(dto.getFrontEndTimeStr())) {
            criteria.andProgressStartTimeLessThanOrEqualTo(DateUtils.parse(dto.getFrontEndTimeStr()));
        }
        if (StringUtils.isNotEmpty(dto.getBackStartTimeStr())) {
            criteria.andProgressEndTimeGreaterThanOrEqualTo(DateUtils.parse(dto.getBackStartTimeStr()));
        }
        if (StringUtils.isNotEmpty(dto.getBackEndTimeStr())) {
            criteria.andProgressEndTimeLessThanOrEqualTo(DateUtils.parse(dto.getBackEndTimeStr()));
        }
        return example;
    }

    // 字符串转数组
    private void paramsHandle(PurchaseContractProgressDto dto) {
        if (StringUtils.isNotEmpty(dto.getProgressStatusStr())) {
            List<Integer> list = new ArrayList<>();
            String[] arrStr = dto.getProgressStatusStr().split(",");
            for (String s : arrStr) {
                if (StringUtils.isNotEmpty(s)) {
                    list.add(Integer.valueOf(s));
                }
            }
            dto.setProgressStatusList(list);
        }
    }

    @Override
    public PurchaseContractProgressDto findProgressDetail(Long purchaseContractProgressId) {
        PurchaseContractProgress progress = purchaseContractProgressMapper.selectByPrimaryKey(purchaseContractProgressId);
        if (Objects.isNull(progress)) return null;
        PurchaseContractProgressDto dto = BeanConverter.copy(progress, PurchaseContractProgressDto.class);
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(progress.getPurchaseContractId());
        //进度执行金额分配列表
        List<PurchaseContractBudgetDto> purchaseContractBudgetDtos =
                purchaseContractBudgetService.getWbsContractBudgetByPurchaseContractId(progress.getPurchaseContractId());
        if (ListUtil.isPresent(purchaseContractBudgetDtos)) {
            //查询采购合同进度和预算关联
            PurchaseContractProgressBudgetRelExample relExample = new PurchaseContractProgressBudgetRelExample();
            relExample.createCriteria().andPurchaseContractProgressIdEqualTo(purchaseContractProgressId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<PurchaseContractProgressBudgetRel> progressBudgetRelList = purchaseContractProgressBudgetRelMapper.selectByExample(relExample);
            if (ListUtil.isPresent(progressBudgetRelList)) {
                Map<Long, PurchaseContractProgressBudgetRel> progressBudgetRelMap =
                        progressBudgetRelList.stream().collect(Collectors.toMap(PurchaseContractProgressBudgetRel::getPurchaseContractBudgetId,
                                Function.identity()));
                for (PurchaseContractBudgetDto purchaseContractBudgetDto : purchaseContractBudgetDtos) {
                    PurchaseContractProgressBudgetRel progressBudgetRel = progressBudgetRelMap.get(purchaseContractBudgetDto.getId());
                    //设为采购合同不含税金额
                    purchaseContractBudgetDto.setExcludingTaxAmount(purchaseContract.getExcludingTaxAmount());
                    if (progressBudgetRel != null) {
                        //本次进度执行金额（不含税）分配
                        purchaseContractBudgetDto.setBudgetExecuteAmount(progressBudgetRel.getBudgetExecuteAmount());
                        purchaseContractBudgetDto.setLocalBudgetExecuteAmount(progressBudgetRel.getLocalBudgetExecuteAmount());
                        //累计进度执行金额（不含税）
                        purchaseContractBudgetDto.setBudgetExecuteAmountTotal(progressBudgetRel.getBudgetExecuteAmountTotal());
                        purchaseContractBudgetDto.setLocalBudgetExecuteAmountTotal(progressBudgetRel.getLocalBudgetExecuteAmountTotal());
                        //采购合同进度预算关联id
                        purchaseContractBudgetDto.setPurchaseContractProgressBudgetRelId(progressBudgetRel.getId());
                    } else {
                        /** 做了进度执行之后，合同又新增了预算的场景 **/
                        purchaseContractBudgetDto.setBudgetExecuteAmount(BigDecimal.ZERO);
                        purchaseContractBudgetDto.setLocalBudgetExecuteAmount(BigDecimal.ZERO);
                        purchaseContractBudgetDto.setBudgetExecuteAmountTotal(BigDecimal.ZERO);
                        purchaseContractBudgetDto.setLocalBudgetExecuteAmountTotal(BigDecimal.ZERO);
                    }
                }
            }
        }
        dto.setPurchaseContractWbsBudgets(purchaseContractBudgetDtos);

        //附件列表
        List<CtcAttachmentDto> ctcAttachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(purchaseContractProgressId,
                CtcAttachmentModule.PURCHASE_CONTRACT_PROGRESS.code(), null);
        dto.setAttachmentDtos(ctcAttachmentDtos);

        //入账信息明细
        PurchaseContractProgressDetailSubjectExample subjectExample = new PurchaseContractProgressDetailSubjectExample();
        subjectExample.createCriteria().andPurchaseContractProgressIdEqualTo(purchaseContractProgressId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractProgressDetailSubject> progressDetailSubjectList =
                purchaseContractProgressDetailSubjectMapper.selectByExample(subjectExample);
        dto.setProgressDetailSubjectDtos(BeanConverter.copy(progressDetailSubjectList, PurchaseContractProgressDetailSubjectDto.class));

        return dto;
    }

    /**
     * 按钮-合同变更/进度执行 权限校验，和前端校验逻辑保持一致：存在未完成的合同进度，不能进行合同变更or新的进度执行
     *
     * @param purchaseContractId
     */
    public void buttonPermissionCheck(Long purchaseContractId) {
        Asserts.notEmpty(purchaseContractId, ErrorCode.CTC_PURCHASE_CONTRACT_ID_NOT_NULL);

        PurchaseContractProgressExample progressExample = new PurchaseContractProgressExample();
        progressExample.createCriteria().andPurchaseContractIdEqualTo(purchaseContractId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        progressExample.setOrderByClause("create_at desc");
        List<PurchaseContractProgress> oldProgressList = purchaseContractProgressMapper.selectByExample(progressExample);
        if (CollectionUtils.isNotEmpty(oldProgressList)) {
            PurchaseContractProgress progress = oldProgressList.get(0);
            if (!Objects.equals(progress.getProgressStatus(), PurchaseContractProgressStatus.PASS.getCode())
                    && !Objects.equals(progress.getProgressStatus(), PurchaseContractProgressStatus.CANCEL.getCode())) {
                throw new MipException("存在未完成的合同进度，请先处理");
            }
        }
    }

    @Override
    @Transactional
    public PurchaseContractProgressDto progressExecute(PurchaseContractProgressDto dto, Long userBy) {
        Long purchaseContractId = dto.getPurchaseContractId();
        Asserts.notEmpty(purchaseContractId, ErrorCode.CTC_PURCHASE_CONTRACT_ID_NOT_NULL);
        Asserts.notEmpty(dto.getExcludingTaxAmount(), ErrorCode.CTC_PURCHASE_CONTRACT_AMOUNT_NOT_NULL);
        Asserts.notEmpty(dto.getExecuteAmount(), ErrorCode.CTC_PURCHASE_CONTRACT_EXECUTE_AMOUNT_NOT_NULL);
        Asserts.notEmpty(dto.getExecutePercent(), ErrorCode.CTC_PURCHASE_CONTRACT_EXECUTE_PERCENT_NOT_NULL);
        PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(purchaseContractId);
        Asserts.notEmpty(contract, ErrorCode.CTC_PURCHASE_CONTRACT_NOT_EXISTS);
        Project project = projectService.selectByPrimaryKey(contract.getProjectId());
        Asserts.notEmpty(project, ErrorCode.CTC_PROJECT_NOT_FIND);

        if (Objects.equals(contract.getStatus(), PurchaseContractStatus.CHANGEING.getCode())) {
            throw new MipException("合同变更中，请先处理");
        }

        //按钮-新建进度 权限校验（重新编辑提交的不校验）
        if (dto.getId() == null) {
            buttonPermissionCheck(purchaseContractId);
        }

        //BUG2023020706146-采购合同进度有百分比没有金额
        if (contract.getExcludingTaxAmount().compareTo(BigDecimal.ZERO) != 0 && dto.getExecutePercent().compareTo(BigDecimal.ZERO) != 0 && dto.getExecuteAmount().compareTo(BigDecimal.ZERO) == 0) {
            throw new MipException("进度执行金额数据异常");
        }

        //采购合同进度是否生成入账信息，需要按采购合同成本归集配置中的设置，来判断是否生成
        //1.按合同进度入账结转，则审批通过后，生成入账信息
        //2.没有配置，或配置为按计划比例结转、按发票入账结转、按点工对账单入账结转时，则审批通过后，不生成入账信息
        //3.生成入账信息的要校验非销售业务场景配置，不生成入账信息的不需要校验
        MaterialOutsourcingContractConfigExample configExample = new MaterialOutsourcingContractConfigExample();
        configExample.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                .andOutsourcingContractTypeNameEqualTo(contract.getTypeName())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<MaterialOutsourcingContractConfig> configList = materialOutsourcingContractConfigMapper.selectByExample(configExample);
        if (CollectionUtils.isNotEmpty(configList) && "03".equals(configList.get(0).getCostCarryForwardMethod())) {
            List<BusiSceneNonSaleDetailDto> busiSceneNonSaleDetailDtos = busiSceneNonSaleService.getBusiSceneDetailListByName("采购合同进度入账",
                    contract.getOuId());
            if (CollectionUtils.isEmpty(busiSceneNonSaleDetailDtos)
                    || StringUtils.isEmpty(busiSceneNonSaleDetailDtos.get(0).getAccountGroupCredit())
                    || StringUtils.isEmpty(busiSceneNonSaleDetailDtos.get(0).getAccountGroupDebit())
                    || (busiSceneNonSaleDetailDtos.get(0).getStartDate() != null && busiSceneNonSaleDetailDtos.get(0).getStartDate().after(new Date()))
                    || (busiSceneNonSaleDetailDtos.get(0).getEndDate() != null && busiSceneNonSaleDetailDtos.get(0).getEndDate().before(new Date()))) {
                throw new MipException("无法获取合同进度入账信息，请检查非销售业务场景配置");
            }
            dto.setErpStatus(CommonErpStatus.NOT_PUSH.code()); //生成入账信息的ERP同步状态才设值
        }

        //如果重复提交，删除上一次保存的 预算关联/附件
        if (dto.getId() != null) {
            purchaseContractProgressBudgetRelService.deleteByProgressId(dto.getId());
            CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
            attachmentQuery.setModuleId(dto.getId());
            attachmentQuery.setModule(CtcAttachmentModule.PURCHASE_CONTRACT_PROGRESS.code());
            ctcAttachmentService.deleteByModule(attachmentQuery);
        }

        //计算当前累计进度执行金额
        PurchaseContractProgressVo progressVo = this.calculateExecuteAmountTotal(purchaseContractId);
        BigDecimal executeAmountTotal = progressVo.getExecuteAmountTotalHeader();
        BigDecimal executePercentTotal = progressVo.getExecuteContractPercentTotal();

        //保存合同进度信息
        dto.setProgressCode(generateProgressCode(dto, contract.getCode()));
        dto.setExecuteAmountTotal(executeAmountTotal);
        dto.setExecutePercentTotal(executePercentTotal);
        dto.setProgressStatus(PurchaseContractProgressStatus.DRAFT.getCode()); //草稿
        dto.setCollectionStatus(PurchaseContractProgressCollectionStatus.UNCOLLECTED.getCode()); //未收集
        purchaseContractProgressService.save(dto, userBy);

        //启用WBS才有进度执行金额分配
        if (project.getWbsEnabled()) {
            List<PurchaseContractProgressBudgetRelDto> progressBudgetRelDtos = dto.getProgressBudgetRelDtos();
            Asserts.notEmpty(progressBudgetRelDtos, ErrorCode.CTC_PURCHASE_CONTRACT_PROGRESS_BUDGET_REL_LIST_NOT_NULL);
            //保存采购合同进度预算关联
            for (PurchaseContractProgressBudgetRelDto progressBudgetRelDto : progressBudgetRelDtos) {
                progressBudgetRelDto.setId(null);
                progressBudgetRelDto.setPurchaseContractProgressId(dto.getId());
                progressBudgetRelDto.setPurchaseContractId(dto.getPurchaseContractId());
                progressBudgetRelDto.setBudgetExecuteAmount(Optional.ofNullable(progressBudgetRelDto.getBudgetExecuteAmount()).orElse(BigDecimal.ZERO));
                progressBudgetRelDto.setLocalBudgetExecuteAmount(Optional.ofNullable(progressBudgetRelDto.getLocalBudgetExecuteAmount()).orElse(BigDecimal.ZERO));
                //查询预算当前累计进度执行金额
                Asserts.notEmpty(progressBudgetRelDto.getPurchaseContractBudgetId(), ErrorCode.CTC_PURCHASE_CONTRACT_BUDGET_ID_NOT_NULL);
                PurchaseContractBudget budget = purchaseContractBudgetMapper.selectByPrimaryKey(progressBudgetRelDto.getPurchaseContractBudgetId());
                if (Objects.nonNull(budget)) {
                    progressBudgetRelDto.setBudgetExecuteAmountTotal(budget.getBudgetExecuteAmountTotal());
                    progressBudgetRelDto.setLocalBudgetExecuteAmountTotal(budget.getLocalBudgetExecuteAmountTotal());
                }
                purchaseContractProgressBudgetRelService.save(progressBudgetRelDto, userBy);
            }
        }

        //保存附件信息
        if (ListUtil.isPresent(dto.getAttachmentDtos())) {
            for (CtcAttachmentDto attachmentDto : dto.getAttachmentDtos()) {
                if (attachmentDto.getId() == null) {
                    attachmentDto.setModule(CtcAttachmentModule.PURCHASE_CONTRACT_PROGRESS.code());
                    attachmentDto.setModuleId(dto.getId());
                }
                ctcAttachmentService.save(attachmentDto, userBy);
            }
        }
        return this.findProgressDetail(dto.getId());
    }

    /**
     * 生成进度执行单号，规则：“PAM采购合同编号”+“-”+“JD”+两位的流水号   比如，KCG22030043-JD01
     *
     * @return
     */
    public String generateProgressCode(PurchaseContractProgressDto dto, String purchaseContractCode) {
        if (StringUtils.isNotEmpty(dto.getProgressCode())) return dto.getProgressCode();

        Integer newOrder = 1;
        //1.查询已有流水号
        PurchaseContractProgressExample example = new PurchaseContractProgressExample();
        example.createCriteria().andPurchaseContractIdEqualTo(dto.getPurchaseContractId());
        List<PurchaseContractProgress> progressList = purchaseContractProgressMapper.selectByExample(example);
        if (ListUtil.isPresent(progressList)) {
            List<Integer> codeList = new ArrayList<>();
            for (PurchaseContractProgress progress : progressList) {
                String[] code = StringUtils.split(progress.getProgressCode(), "-JD");
                if (code.length < 1) {
                    continue;
                }
                codeList.add(Integer.valueOf(code[code.length - 1]));
            }
            if (ListUtils.isNotEmpty(codeList)) {
                //2.创建新流水号：获取progress_code最大值+1
                newOrder = codeList.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList()).get(0) + 1;
            }
        }
        String newOrderStr = String.valueOf(newOrder);
        if (newOrderStr.length() < 2) {
            NumberFormat nf = NumberFormat.getInstance();
            nf.setMinimumIntegerDigits(2); //流水号至少2位
            newOrderStr = nf.format(newOrder);
        }

        //3.拼接生成进度执行单号
        StringBuffer progressCode = new StringBuffer();
        progressCode.append(purchaseContractCode);
        progressCode.append("-");
        progressCode.append("JD");
        progressCode.append(newOrderStr);
        return progressCode.toString();
    }

    /**
     * 生成进度执行单号(导入专用)，规则：“PAM采购合同编号”+“-”+“JD”+两位的流水号   比如，KCG22030043-JD01
     *
     * @return
     */
    public String generateProgressCodeImport(String purchaseContractCode, Integer newOrder) {
        String newOrderStr = String.valueOf(newOrder);
        if (newOrderStr.length() < 2) {
            NumberFormat nf = NumberFormat.getInstance();
            nf.setMinimumIntegerDigits(2); //流水号至少2位
            newOrderStr = nf.format(newOrder);
        }
        StringBuffer progressCode = new StringBuffer();
        progressCode.append(purchaseContractCode);
        progressCode.append("-");
        progressCode.append("JD");
        progressCode.append(newOrderStr);
        return progressCode.toString();
    }

    @Override
    public Integer cancelProgress(Long id) {
        // 更新采购合同进度执行状态：作废
        PurchaseContractProgress progress = new PurchaseContractProgress();
        progress.setId(id);
        progress.setProgressStatus(PurchaseContractProgressStatus.CANCEL.getCode());
        purchaseContractProgressMapper.updateByPrimaryKeySelective(progress);
        return 1;
    }

    @Override
    public List<PurchaseContractQualityReportDto> getQualityReportList(Long contractId) {
        PurchaseContractQualityReportExample example = new PurchaseContractQualityReportExample();
        example.createCriteria().andPurchaseContractIdEqualTo(contractId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractQualityReport> qualityReports = purchaseContractQualityReportMapper.selectByExample(example);
        List<PurchaseContractQualityReportDto> qualityReportDtos = BeanConverter.copy(qualityReports, PurchaseContractQualityReportDto.class);
        if (ListUtil.isPresent(qualityReportDtos)) {
            for (PurchaseContractQualityReportDto qualityReportDto : qualityReportDtos) {
                //附件列表
                List<CtcAttachmentDto> ctcAttachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(qualityReportDto.getId(),
                        CtcAttachmentModule.PURCHASE_CONTRACT_QUALITY_REPORT.code(), null);
                qualityReportDto.setAttachmentDtos(ctcAttachmentDtos);
            }
        }
        return qualityReportDtos;
    }

    @Override
    public Long saveQualityReport(PurchaseContractQualityReportDto dto, Long userBy) {
        purchaseContractQualityReportService.save(dto, userBy);

        if (ListUtil.isPresent(dto.getAttachmentDtos())) {
            for (CtcAttachmentDto attachmentDto : dto.getAttachmentDtos()) {
                if (attachmentDto.getId() == null) {
                    attachmentDto.setModule(CtcAttachmentModule.PURCHASE_CONTRACT_QUALITY_REPORT.code());
                    attachmentDto.setModuleId(dto.getId());
                }
                ctcAttachmentService.save(attachmentDto, userBy);
            }
        }
        return dto.getId();
    }

    @Override
    public Integer deleteQualityReport(Long qualityReportId) {
        PurchaseContractQualityReport qualityReport = new PurchaseContractQualityReport();
        qualityReport.setId(qualityReportId);
        qualityReport.setDeletedFlag(DeletedFlag.INVALID.code());
        purchaseContractQualityReportMapper.updateByPrimaryKeySelective(qualityReport);
        return 1;
    }

    @Override
    public List<PurchaseContractChangeHeaderDto> getVersionHistoryList(Long contractId) {
        PurchaseContractChangeHeaderExample example = new PurchaseContractChangeHeaderExample();
        example.createCriteria().andPurchaseContractIdEqualTo(contractId)
                .andStatusEqualTo(PurchaseContractChangeHeaderStatus.PASS.code())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code())
                .andVersionCodeIsNotNull();
        List<PurchaseContractChangeHeaderDto> changeHeaderDtos = BeanConverter.copy(purchaseContractChangeHeaderMapper.selectByExample(example),
                PurchaseContractChangeHeaderDto.class);
        if (CollectionUtils.isNotEmpty(changeHeaderDtos)) {
            //统计下载次数
            List<Long> headIds = changeHeaderDtos.stream().map(PurchaseContractChangeHeaderDto::getId).collect(Collectors.toList());
            List<CtcOperatingRecordDto> recordList =
                    ctcOperatingRecordExtMapper.statistics(CtcOperatingRecordModule.PURCHASE_CONTRACT_VERSION_DOWNLOAD.code(), headIds);
            Map<Long, Integer> recordMap = recordList.stream().collect(Collectors.toMap(CtcOperatingRecordDto::getModuleId,
                    CtcOperatingRecordDto::getNum));

            //倒序排序
            for (PurchaseContractChangeHeaderDto changeHeaderDto : changeHeaderDtos) {
                String[] code = changeHeaderDto.getVersionCode().split("-");
                if (code.length < 1) {
                    continue;
                }
                changeHeaderDto.setSort(Integer.valueOf(code[code.length - 1]));
            }
            changeHeaderDtos.sort(Comparator.comparingInt(PurchaseContractChangeHeaderDto::getSort).reversed());

            for (int i = 0; i < changeHeaderDtos.size(); i++) {
                PurchaseContractChangeHeaderDto dto = changeHeaderDtos.get(i);
                //标识当前版本
                if (i == 0) {
                    dto.setTag(1);
                }
                //标识初始版本
                if (i == changeHeaderDtos.size() - 1 && Objects.equals(dto.getChangeType(), PurchaseContractChangeType.BUILD.code())) {
                    dto.setTag(0);
                }
                //设置提交人名字
                dto.setCreateBy(dto.getCreateBy());
                //设置下次次数
                dto.setDownloadCount(Optional.ofNullable(recordMap.get(dto.getId())).orElse(0));
            }
        }
        return changeHeaderDtos;
    }

    @Override
    public PurchaseContractPdfVo getVersionHistoryDetail(Long headerId, Integer tag) {
        PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        Assert.notNull(header, AssertErrorMessage.PURCHASE_CONTRACT_CHANGE_NOT_EXIST);

        PurchaseContractChangeHistoryExample changeHistoryExample = new PurchaseContractChangeHistoryExample();
        changeHistoryExample.createCriteria().andHeaderIdEqualTo(header.getId());
        List<PurchaseContractChangeHistory> historyList = purchaseContractChangeHistoryMapper.selectByExampleWithBLOBs(changeHistoryExample);
        Map<Integer, PurchaseContractChangeHistory> historyMap =
                historyList.stream().collect(Collectors.toMap(PurchaseContractChangeHistory::getHistoryType, Function.identity(),
                        (key1, key2) -> key1));
        PurchaseContractChangeHistory history = historyMap.get(HistoryType.HISTORY.getCode());
        PurchaseContractChangeHistory change = historyMap.get(HistoryType.CHANGE.getCode());
        if (Objects.isNull(change)) return null;

        // 查询合同历史版本数
        PurchaseContractChangeHeaderExample example = new PurchaseContractChangeHeaderExample();
        example.createCriteria().andPurchaseContractIdEqualTo(header.getPurchaseContractId())
                .andStatusEqualTo(PurchaseContractChangeHeaderStatus.PASS.code())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code())
                .andVersionCodeIsNotNull();
        long versionHistoryCount = purchaseContractChangeHeaderMapper.countByExample(example);

        PurchaseContractPdfVo baseVO = new PurchaseContractPdfVo();
        //合同版本号
        baseVO.setVersionCode(header.getVersionCode());
        //使用单位名称
        final Unit unit = CacheDataUtils.findUnitById(SystemContext.getUnitId());
        if (unit != null) {
            baseVO.setUnitName(unit.getUnitName());
        }
        //采购合同编号
        baseVO.setContractCode(change.getCode());
        //合同日期
        baseVO.setContractDate(new Date());
        //卖方
        baseVO.setVendorName(change.getVendorCode() + " " + change.getVendorName());
        //买方
        baseVO.setPurchasingFollowerName(change.getPurchasingFollowerName());
        //买方邮箱
        if (change.getPurchasingFollower() != null) {
            final UserInfo userInfo = CacheDataUtils.findUserById(change.getPurchasingFollower());
            if (userInfo != null) {
                baseVO.setPurchasingFollowerEmail(Optional.ofNullable(userInfo.getOriginalMail()).orElse(userInfo.getEmail()));
            }
        }
        //不含税总价
        baseVO.setExcludingTaxAmount(change.getExcludingTaxAmount());
        //总价
        baseVO.setAmount(change.getAmount());
        //税
        if (baseVO.getExcludingTaxAmount() != null && baseVO.getAmount() != null) {
            baseVO.setTax(baseVO.getAmount().subtract(baseVO.getExcludingTaxAmount()));
        }
        //交货方式
        baseVO.setDeliveryType(change.getDeliveryType());
        //交货条款
        baseVO.setDeliveryClause(change.getDeliveryClause());
        //币种
        baseVO.setCurrency(change.getCurrency());

        //付款条件/方式
        List<String> payPlanInfoList = new ArrayList<>();
        PaymentPlanChangeHistoryExample paymentPlanChangeHistoryExample = new PaymentPlanChangeHistoryExample();
        paymentPlanChangeHistoryExample.createCriteria().andHeaderIdEqualTo(header.getId()).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode()).andDeletedFlagEqualTo(false);
        List<PaymentPlanChangeHistory> paymentPlanChangeHistoryList = paymentPlanChangeHistoryMapper.selectByExample(paymentPlanChangeHistoryExample);
        if (ListUtil.isPresent(paymentPlanChangeHistoryList)) {
            for (PaymentPlanChangeHistory paymentPlanChangeHistory : paymentPlanChangeHistoryList) {
                StringBuffer payPlanInfo = new StringBuffer();
//                payPlanInfo.append(paymentPlan.getProportion()).append("%").append("/");
                payPlanInfo.append(paymentPlanChangeHistory.getRequirement()).append("/");
                payPlanInfo.append(paymentPlanChangeHistory.getPaymentMethodName());
                payPlanInfoList.add(payPlanInfo.toString());
            }
        } else {
            /** 兼容 合同录入时没添加付款计划，后续操作添加后，历史版本也展示付款计划 **/
            if (tag != null && (tag == 1 || (tag == 0 && versionHistoryCount == 1))) {
                PaymentPlanExample paymentPlanExample = new PaymentPlanExample();
                paymentPlanExample.createCriteria().andContractIdEqualTo(header.getPurchaseContractId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<PaymentPlan> paymentPlanList = paymentPlanMapper.selectByExample(paymentPlanExample);
                for (PaymentPlan paymentPlan : paymentPlanList) {
                    StringBuffer payPlanInfo = new StringBuffer();
//                    payPlanInfo.append(paymentPlan.getProportion()).append("%").append("/");
                    payPlanInfo.append(paymentPlan.getRequirement()).append("/");
                    payPlanInfo.append(paymentPlan.getPaymentMethodName());
                    payPlanInfoList.add(payPlanInfo.toString());
                }
            }
        }
        baseVO.setPayPlanInfoList(payPlanInfoList);

        //预算信息列表
//        List<PurchaseContractBudgetPdfVo> budgetInfoList = getBudgetInf(header);
//        baseVO.setBudgetInfoList(budgetInfoList);

        //合同内容列表
        Project project = projectMapper.selectByPrimaryKey(change.getProjectId());
        PurchaseContractDetailChangeHistoryExample detailChangeHistoryExample = new PurchaseContractDetailChangeHistoryExample();
        detailChangeHistoryExample.createCriteria().andHeaderIdEqualTo(header.getId())
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractDetailChangeHistory> detailChangeHistoryList =
                purchaseContractDetailChangeHistoryMapper.selectByExample(detailChangeHistoryExample);
        List<PurchaseContractDetailPdfVo> detailInfoList = new ArrayList<>();
        if (ListUtil.isPresent(detailChangeHistoryList)) {
            for (int i = 0; i < detailChangeHistoryList.size(); i++) {
                PurchaseContractDetailChangeHistory detailChangeHistory = detailChangeHistoryList.get(i);
                PurchaseContractDetailPdfVo detailPdfVo = BeanConverter.copy(detailChangeHistory, PurchaseContractDetailPdfVo.class);
                detailPdfVo.setNum(i + 1);
                detailPdfVo.setDeliveryDate(change.getDeliveryDate());
                detailPdfVo.setItemDescription(detailChangeHistory.getModel() + "/" + detailChangeHistory.getBrand());
                detailPdfVo.setProjectCode(project.getCode());
                BigDecimal taxRate = BigDecimal.ZERO;
                /** 税率处理，varchar类型，历史数据同时存在0.01和1%... **/
                if (StringUtils.hasText(detailChangeHistory.getTaxRate())) {
                    String[] split = detailChangeHistory.getTaxRate().split("%");
                    if (split.length > 0) {
                        taxRate = new BigDecimal(split[0]);
                        if (taxRate.compareTo(BigDecimal.ZERO) >= 0) {
                            taxRate = taxRate.divide(new BigDecimal(100));
                        }
                    }
                }
                if (detailChangeHistory.getUnitPrice() != null) {
                    detailPdfVo.setNoTaxUnitPrice(detailChangeHistory.getUnitPrice().divide(BigDecimal.ONE.add(taxRate), 2,
                            BigDecimal.ROUND_HALF_UP));
                }
                detailInfoList.add(detailPdfVo);
            }
        } else {
            /** 兼容历史数据，历史版本合同内容没备份到变更表，跟产品确认只兼顾最新的版本有数据 **/
            if (tag != null && (tag == 1 || (tag == 0 && versionHistoryCount == 1))) {
                PurchaseContractDetailExample detailExample = new PurchaseContractDetailExample();
                detailExample.createCriteria().andContractIdEqualTo(header.getPurchaseContractId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                List<PurchaseContractDetail> detailList = purchaseContractDetailMapper.selectByExample(detailExample);
                if (ListUtil.isPresent(detailList)) {
                    for (int i = 0; i < detailList.size(); i++) {
                        PurchaseContractDetail detail = detailList.get(i);
                        PurchaseContractDetailPdfVo detailPdfVo = BeanConverter.copy(detail, PurchaseContractDetailPdfVo.class);
                        detailPdfVo.setNum(i + 1);
                        detailPdfVo.setDeliveryDate(change.getDeliveryDate());
                        detailPdfVo.setItemDescription(detail.getModel() + "/" + detail.getBrand());
                        detailPdfVo.setProjectCode(project.getCode());
                        BigDecimal taxRate = BigDecimal.ZERO;
                        if (StringUtils.hasText(detail.getTaxRate())) {
                            String[] split = detail.getTaxRate().split("%");
                            if (split.length > 0) {
                                taxRate = new BigDecimal(split[0]);
                                if (taxRate.compareTo(BigDecimal.ZERO) >= 0) {
                                    taxRate = taxRate.divide(new BigDecimal(100));
                                }
                            }
                        }
                        if (detail.getUnitPrice() != null) {
                            detailPdfVo.setNoTaxUnitPrice(detail.getUnitPrice().divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_UP));
                        }
                        detailInfoList.add(detailPdfVo);
                    }
                }
            }
        }
        baseVO.setDetailInfoList(detailInfoList);

        //买方盖章
        PurchaseContractStampExample stampExample = new PurchaseContractStampExample();
        stampExample.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                .andStampTypeEqualTo(PurchaseContractStampType.UNIT_SEAL.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractStamp> unitSeals = purchaseContractStampMapper.selectByExample(stampExample);
        if (ListUtil.isPresent(unitSeals)) {
            baseVO.setPurchaserAttachId(unitSeals.get(0).getAttachId());
        }

        //公司信息和收货地址
        setCompayAndDeliveryAddress(baseVO);

        //设置版本创建时间
        baseVO.setVersionCreateTime(header.getCreateAt());

        //核准签名
        String approveInfo = header.getApproveInfo();
        /** 变更时，如果金额增加了，取变更流程的审批人的签名图片，金额没有增加，取合同新建流程的审批人的签名图片 **/
        if (history != null && history.getAmount() != null && change.getAmount() != null && change.getAmount().compareTo(history.getAmount()) <= 0) {
            PurchaseContractChangeHeaderExample headerExample = new PurchaseContractChangeHeaderExample();
            headerExample.createCriteria().andPurchaseContractIdEqualTo(header.getPurchaseContractId())
                    .andChangeTypeEqualTo(PurchaseContractChangeType.BUILD.code())
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<PurchaseContractChangeHeader> buildHeaders = purchaseContractChangeHeaderMapper.selectByExample(headerExample);
            if (ListUtil.isPresent(buildHeaders)) {
                approveInfo = buildHeaders.get(0).getApproveInfo();
            }
        }
        if (StringUtils.hasText(approveInfo)) {
            List<String> approveInfoList = Lists.newArrayList(approveInfo.split("；"));
            List<String> mipNameList = new ArrayList<>();
            for (String item : approveInfoList) {
                String[] split = item.split("@");
                if (split.length > 1) {
                    mipNameList.add(split[1]);
                }
            }
            stampExample = new PurchaseContractStampExample();
            stampExample.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                    .andStampTypeEqualTo(PurchaseContractStampType.SIGNATURE.getCode())
                    .andMipNameIn(mipNameList)
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<PurchaseContractStamp> signatures = purchaseContractStampMapper.selectByExample(stampExample);
            baseVO.setApproveInfoList(signatures);
        }

        //标准条款信息
        baseVO.setContractTermsFlg(change.getContractTermsFlg());
        setStandardTerms(change, baseVO);

        // 记录下载操作
        CtcOperatingRecord record = new CtcOperatingRecord();
        record.setModule(CtcOperatingRecordModule.PURCHASE_CONTRACT_VERSION_DOWNLOAD.code());
        record.setModuleId(headerId);
        record.setOperatorId(SystemContext.getUserId());
        final UserInfo user = CacheDataUtils.findUserById(SystemContext.getUserId());
        if (user != null) {
            record.setOperatorName(user.getName());
        }
        record.setDeletedFlag(DeletedFlag.VALID.code());
        ctcOperatingRecordMapper.insertSelective(record);

        return baseVO;
    }

    private void setStandardTerms(PurchaseContractChangeHistory change, PurchaseContractPdfVo baseVO) {
        if (ContractTermsFlgEnum.STANDARD_TERMS.getCode().equals(change.getContractTermsFlg())) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(change.getContractTermsIds())) {
                String contractTermsIds = change.getContractTermsIds();
                List<PurchaseContractStandardTermsDto> standardTermsDtoList = new ArrayList<>();
                if (StringUtils.isNotEmpty(contractTermsIds)) {
                    List<Long> standardTermsIdList = Arrays.stream(contractTermsIds.split(","))
                            .map(String::trim)
                            .map(Long::valueOf)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(standardTermsIdList)) {
                        for (Long standardTermsId : standardTermsIdList) {
                            PurchaseContractStandardTerms purchaseContractStandardTerms = purchaseContractStandardTermsMapper.selectByPrimaryKey(standardTermsId);
                            if (purchaseContractStandardTerms != null) {
                                PurchaseContractStandardTermsDto standardTermsDto = new PurchaseContractStandardTermsDto();
                                BeanConverter.copy(purchaseContractStandardTerms, standardTermsDto);

                                PurchaseContractStandardTermsDeviationExample standardTermsDeviationExample = new PurchaseContractStandardTermsDeviationExample();
                                PurchaseContractStandardTermsDeviationExample.Criteria criteria = standardTermsDeviationExample.createCriteria();
                                criteria.andDeletedFlagEqualTo(false).andAssociationPurchaseTermsIdEqualTo(standardTermsId);
                                List<PurchaseContractStandardTermsDeviation> deviations = purchaseContractStandardTermsDeviationMapper.selectByExample(standardTermsDeviationExample);
                                if (CollectionUtils.isNotEmpty(deviations)) {
                                    standardTermsDto.setStandardTermsDeviationList(deviations);
                                }
                                standardTermsDtoList.add(standardTermsDto);
                            }
                        }
                        baseVO.setStandardTermsDtoList(standardTermsDtoList);
                    }
                }
            }
        }else {
            //合同条款
            baseVO.setContractTerms(change.getContractTerms());
        }
    }

    private void setCompayAndDeliveryAddress(PurchaseContractPdfVo baseVO) {
        PurchaseContractStampExample companyStampExample = new PurchaseContractStampExample();
        companyStampExample.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                .andStampTypeEqualTo(PurchaseContractStampType.COMPANY.getCode())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractStamp> unitCompanyInfo = purchaseContractStampMapper.selectByExampleWithBLOBs(companyStampExample);
        if (ListUtil.isPresent(unitCompanyInfo)) {
            baseVO.setCompanyInfo(unitCompanyInfo.get(0).getCompanyInfo());
            baseVO.setDeliveryAddress(unitCompanyInfo.get(0).getDeliveryAddress());
        }
    }

    private List<PurchaseContractBudgetPdfVo> getBudgetInf(PurchaseContractChangeHeader header) {
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(header.getPurchaseContractId());
        if ("点工采购".equals(purchaseContract.getTypeName())) {
            return purchaseContractBudgetExtMapper.getHroPurchaseBudgetInfo(header.getId());
        } else {
            return purchaseContractBudgetExtMapper.getWbsContractBudgetFromHistory(header.getId());
        }
    }

    @Override
    public Long saveStamp(PurchaseContractStampDto dto, Long userBy) {
        PurchaseContractStampExample example = new PurchaseContractStampExample();
        if (Objects.equals(dto.getStampType(), PurchaseContractStampType.SIGNATURE.getCode())) {
            example.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                    .andMipNameEqualTo(dto.getMipName())
                    .andStampTypeEqualTo(dto.getStampType())
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            long count = purchaseContractStampMapper.countByExample(example);
            Assert.isTrue(count == 0, "核准人签名已维护，不能重复提交");
        }
        if (Objects.equals(dto.getStampType(), PurchaseContractStampType.UNIT_SEAL.getCode())) {
            example.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                    .andStampTypeEqualTo(dto.getStampType())
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            long count = purchaseContractStampMapper.countByExample(example);
            Assert.isTrue(count == 0, "单位盖章已维护，不能重复提交");
        }
        if (Objects.equals(dto.getStampType(), PurchaseContractStampType.COMPANY.getCode())) {
            Long stampId = dto.getId();
            if (Objects.isNull(stampId)) {
                example.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId())
                        .andStampTypeEqualTo(dto.getStampType())
                        .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                long count = purchaseContractStampMapper.countByExample(example);
                Assert.isTrue(count == 0, "公司信息和收货地址已维护，不能重复提交");
            } else {
                purchaseContractStampMapper.updateByPrimaryKeySelective(dto);
                return dto.getId();
            }
        }
        dto.setUnitId(SystemContext.getUnitId());
        purchaseContractStampService.save(dto, userBy);
        return dto.getId();
    }

    @Override
    public List<PurchaseContractStampDto> getStampList(Integer stampType) {
        PurchaseContractStampExample example = new PurchaseContractStampExample();
        example.createCriteria().andStampTypeEqualTo(stampType)
                .andUnitIdEqualTo(SystemContext.getUnitId())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<PurchaseContractStamp> purchaseContractStamps = purchaseContractStampMapper.selectByExampleWithBLOBs(example);
        return BeanConverter.copy(purchaseContractStamps, PurchaseContractStampDto.class);
    }

    @Override
    public Integer deleteStamp(Long stampId) {
        PurchaseContractStamp stamp = new PurchaseContractStamp();
        stamp.setId(stampId);
        stamp.setDeletedFlag(DeletedFlag.INVALID.code());
        purchaseContractStampMapper.updateByPrimaryKeySelective(stamp);
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNodeInfo(Long headerId, String handlerId, String nodeId) {
        PurchaseContractChangeHeader header = purchaseContractChangeHeaderMapper.selectByPrimaryKey(headerId);
        List<String> list = new ArrayList<>();
        if (header.getApproveInfo() != null) {
            list = Lists.newArrayList(header.getApproveInfo().split("；"));
        }
        //多个节点审批人相同的话回调重叠
        if (StringUtils.hasText(nodeId)) {
            nodeId = nodeId.split(",")[0];
        }
        String nodeInfo = nodeId + "@" + handlerId;
        //防止重复回调
        if (!list.contains(nodeInfo)) {
            list.add(nodeInfo);
        }
        header.setApproveInfo(Joiner.on("；").skipNulls().join(list));
        purchaseContractChangeHeaderMapper.updateByPrimaryKeySelective(header);
    }

    @Override
    public void purchaseContractProgressCallBack(Long purchaseContractProgressId, boolean status, String msg) {
        PurchaseContractProgress progress = purchaseContractProgressMapper.selectByPrimaryKey(purchaseContractProgressId);
        if (null == progress) return;
        if (status) {
            progress.setErpStatus(CommonErpStatus.PUSHED.code());
        } else {
            progress.setErpStatus(CommonErpStatus.PUSH_FAILED.code());
        }
        progress.setErpMessage(msg);
        purchaseContractProgressMapper.updateByPrimaryKeySelective(progress);
    }

    @Override
    public int pushToERP(Long purchaseContractProgressId) {
        PurchaseContractProgress progress = purchaseContractProgressMapper.selectByPrimaryKey(purchaseContractProgressId);
        if (Objects.equals(progress.getErpStatus(), CommonErpStatus.PUSHED.code())
                || Objects.equals(progress.getErpStatus(), CommonErpStatus.PUSHING.code())) {
            throw new BizException(Code.ERROR, "同步中或已同步的采购合同进度不能进行此操作!");
        }
        if (!Objects.equals(progress.getProgressStatus(), PurchaseContractProgressStatus.PASS.getCode())) {
            throw new BizException(Code.ERROR, "审批通过的采购合同进度才能进行此操作!");
        }
        // 入账信息插入erp推送队列
        HandleDispatcher.route(BusinessTypeEnums.PURCHASE_CONTRACT_PROGRESS.getCode(), purchaseContractProgressId.toString(), null, null, true);
        // 更新ERP同步状态：同步中
        progress.setErpStatus(CommonErpStatus.PUSHING.code());
        return purchaseContractProgressMapper.updateByPrimaryKeySelective(progress);
    }

    @Override
    public List<PurchaseContractDetailDTO> listPurchaseContractDetail(Long contractId) {
        List<PurchaseContractDetail> contractDetails = purchaseContractDetailService.findByContractId(contractId);
        return BeanConverter.copy(contractDetails, PurchaseContractDetailDTO.class);
    }

    /**
     * 根据合同编码查询合同信息
     *
     * @param code 合同编码
     * @return 采购合同实体
     */
    private List<PurchaseContract> getPurchaseContractByCode(String code) {
        PurchaseContractExample example = new PurchaseContractExample();
        PurchaseContractExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(false).andCodeEqualTo(code);
        return purchaseContractMapper.selectByExample(example);
    }

    /**
     * 根据编码查询项目信息
     *
     * @param code 项目编号
     * @return 项目实体类
     */
    private List<Project> getProjectByCode(String code) {
        ProjectExample example = new ProjectExample();
        ProjectExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(false).andCodeEqualTo(code);
        return projectMapper.selectByExample(example);
    }

    @Override
    public PurchaseContractProgressDto progressDetailApp(Long progressId) {
        PurchaseContractProgress progress = purchaseContractProgressMapper.selectByPrimaryKey(progressId);
        PurchaseContractProgressDto progressDto = BeanConverter.copy(progress, PurchaseContractProgressDto.class);
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(progress.getPurchaseContractId());
        PurchaseContractDTO purchaseContractDto = BeanConverter.copy(purchaseContract, PurchaseContractDTO.class);
        progressDto.setPurchaseContractDTO(purchaseContractDto);
        Project project = projectMapper.selectByPrimaryKey(purchaseContract.getProjectId());
        purchaseContractDto.setProjectName(project.getName());

        //附件列表
        List<CtcAttachmentDto> ctcAttachmentDtoList = ctcAttachmentService.findCtcAttachmentDtos(progressId,
                CtcAttachmentModule.PURCHASE_CONTRACT_PROGRESS.code(), null);
        if (ListUtils.isNotEmpty(ctcAttachmentDtoList)) {
            progressDto.setAttachmentDtos(ctcAttachmentDtoList);
        }
        // 所属业务实体
        if (purchaseContract.getOuId() != null) {
            final OperatingUnit operatingUnit = CacheDataUtils.findOuById(purchaseContract.getOuId());
            if (operatingUnit != null) {
                purchaseContractDto.setOuName(operatingUnit.getOperatingUnitName());
            }
        }
        return progressDto;
    }

    @Override
    public Integer updateVendorInfoBatch() {
        return purchaseContractExtMapper.updateVendorInfoBatch();
    }

    /**
     * @param purchaseContractId 不传 获取默认模板
     * @param templateCode       不传 获取最近一次的保存的模板
     * @return
     */
    @Override
    public PurchaseContractTemplateDto getTemplateDetail(Long purchaseContractId, String templateCode) {
        if (purchaseContractId == null) {
            return getDefaultTemplate(templateCode);
        }
        PurchaseContractTemplateExample example = new PurchaseContractTemplateExample();
        example.setOrderByClause("create_at desc");
        PurchaseContractTemplateExample.Criteria criteria = example.createCriteria();
        criteria.andPurchaseContractIdEqualTo(purchaseContractId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        if (StringUtils.hasText(templateCode)) {
            criteria.andTemplateCodeEqualTo(templateCode);
        }
        List<PurchaseContractTemplate> list = purchaseContractTemplateMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isNotEmpty(list)) {
            return BeanConverter.copy(list.get(0), PurchaseContractTemplateDto.class);
        }
        return getDefaultTemplate(templateCode);
    }

    public PurchaseContractTemplateDto getDefaultTemplate(String templateCode) {
        if (StringUtils.isNotEmpty(templateCode)) {
            PurchaseContractTemplateConfigExample configExample = new PurchaseContractTemplateConfigExample();
            configExample.createCriteria().andTemplateCodeEqualTo(templateCode).andUnitIdEqualTo(SystemContext.getUnitId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            List<PurchaseContractTemplateConfig> configList = purchaseContractTemplateConfigMapper.selectByExampleWithBLOBs(configExample);
            if (CollectionUtils.isNotEmpty(configList)) {
                return BeanConverter.copy(configList.get(0), PurchaseContractTemplateDto.class);
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveTemplate(PurchaseContractTemplateDto dto) {
        //先逻辑删除已有的
        purchaseContractTemplateExtMapper.logicalDelete(dto.getPurchaseContractId(), dto.getTemplateCode(), SystemContext.getUserId());

        //保存模板
        PurchaseContractTemplate template = BeanConverter.copy(dto, PurchaseContractTemplate.class);
        template.setDeletedFlag(DeletedFlag.VALID.code());
        purchaseContractTemplateMapper.insert(template);

        //合同原件字段回写正式表
        if (StringUtils.isNotEmpty(dto.getFileId())) {
            PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(dto.getPurchaseContractId());
            contract.setOriginalContractAnnex(StringUtils.isEmpty(contract.getOriginalContractAnnex()) ? dto.getFileId() :
                    contract.getOriginalContractAnnex() + "," + dto.getFileId());
            purchaseContractMapper.updateByPrimaryKeySelective(contract);
        }
        return template.getId();
    }

    @Override
    public Boolean addStatusCheck(Long id, String operationType) {
        PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(id);
        if (Objects.equals(ContractStatus.PENDING.getCode(), contract.getStatus()) || Objects.equals(ContractStatus.CHANGING.getCode(),
                contract.getStatus()) || Objects.equals(ContractStatus.EFFECTIVE.getCode(), contract.getStatus())) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean changeStatusCheck(Long id, String operationType, Long changeHeaderId) {
        if (Objects.nonNull(changeHeaderId)) {
            PurchaseContractChangeHeader purchaseContractChangeHeader = purchaseContractChangeHeaderMapper.selectByPrimaryKey(changeHeaderId);
            if (Objects.equals(PurchaseContractChangeHeaderStatus.CHECKING.code(), purchaseContractChangeHeader.getStatus()) || Objects.equals(PurchaseContractChangeHeaderStatus.PASS.code(), purchaseContractChangeHeader.getStatus())) {
                return false;
            }
        }
        PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(id);
        if (Objects.equals(ContractStatus.PENDING.getCode(), contract.getStatus()) || Objects.equals(ContractStatus.CHANGING.getCode(),
                contract.getStatus())) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean updateContractStatus(Long id, Integer status) {
        PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(id);
        if (!Objects.equals(ContractStatus.EFFECTIVE.getCode(), contract.getStatus()) && !Objects.equals(ContractStatus.EFFECTIVE.getCode(),
                status)) {
            contract.setStatus(status);
            purchaseContractMapper.updateByPrimaryKeySelective(contract);
            return true;
        }
        return false;
    }

    @Override
    public Integer updateContractAndChangeway(Long id) {
        PurchaseContractChangeHeader purchaseContractChangeHeader = purchaseContractChangeHeaderMapper.selectByPrimaryKey(id);
        purchaseContractChangeHeader.setStatus(PurchaseContractChangeHeaderStatus.CHECKING.code());
        purchaseContractChangeHeaderMapper.updateByPrimaryKey(purchaseContractChangeHeader);
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(purchaseContractChangeHeader.getPurchaseContractId());
        purchaseContract.setStatus(PurchaseContractStatus.CHANGEING.getCode());
        return purchaseContractMapper.updateByPrimaryKeySelective(purchaseContract);
    }

    @Override
    public Boolean updateContractById(Long id, String trackLeadTime, String trackLeadTimeRemark) {
        PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(id);
        Assert.notNull(contract, AssertErrorMessage.PURCHASE_CONTRACT_NOT_EXIST);
        Long userId = SystemContext.getUserId();
        if (!Objects.equals(contract.getPurchasingFollower(), userId)) {
            throw new MipException("只有该合同的采购跟进人：" + contract.getPurchasingFollowerName() + "允许编辑！");
        }
        if (StringUtils.isNotEmpty(trackLeadTime)) {
            contract.setTrackLeadTime(DateUtil.parseDate(trackLeadTime, DateUtil.TIMESTAMP_PATTERN));
        } else {
            contract.setTrackLeadTime(null);
        }
        if (StringUtils.isNotEmpty(trackLeadTimeRemark)) {
            contract.setTrackLeadTimeRemark(trackLeadTimeRemark);
        } else {
            contract.setTrackLeadTimeRemark("");
        }
        purchaseContractMapper.updateByPrimaryKeyWithBLOBs(contract);
        return true;
    }

    @Override
    public List<PurchaseContractTrackLeadDto> selectListByCodes(List<Long> getOus, String codes) {
        PurchaseContractExample example = new PurchaseContractExample();
        final PurchaseContractExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotEmpty(codes)) {
            List<String> codeList = Arrays.asList(codes.split(","));
            criteria.andCodeIn(codeList);
        }

        criteria.andOuIdIn(getOus);
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        List<PurchaseContract> list = purchaseContractMapper.selectByExample(example);
        return BeanConverter.convert(list, PurchaseContractTrackLeadDto.class);
    }

    @Override
    public void importPurchaseContractTrackLead(List<PurchaseContractTrackLeadDto> dtoList) {
        if (ListUtils.isNotEmpty(dtoList)) {
            List<String> codeList = dtoList.stream().map(PurchaseContractTrackLeadDto::getCode).collect(Collectors.toList());
            PurchaseContractExample example = new PurchaseContractExample();
            final PurchaseContractExample.Criteria criteria = example.createCriteria();
            criteria.andCodeIn(codeList);
            criteria.andDeletedFlagEqualTo(Boolean.FALSE);
            List<PurchaseContract> list = purchaseContractMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(list)) {
                Map<String, PurchaseContract> trackLeadMap = list.stream().collect(Collectors.toMap(PurchaseContract::getCode, Function.identity(),
                        (a, b) -> a));
                for (PurchaseContractTrackLeadDto dto : dtoList) {
                    if (trackLeadMap.containsKey(dto.getCode())) {
                        PurchaseContract purchaseContract = trackLeadMap.get(dto.getCode());
                        purchaseContract.setTrackLeadTime(DateUtil.parseDate(dto.getTrackLeadTime()));
                        purchaseContract.setTrackLeadTimeRemark(dto.getTrackLeadTimeRemark());
                        purchaseContractMapper.updateByPrimaryKeySelective(purchaseContract);
                    }
                }
            }
        }
    }

    @Override
    public PageInfo<PurchaseContractVO> getDuplicateNameContractList(PurchaseContractDTO purchaseContractDTO, Integer pageNum, Integer pageSize) {
        Guard.notNull(purchaseContractDTO.getName(), "合同名称不能为空");

        List<Long> currentUserOuList = getCurrentUserOuList();

        purchaseContractDTO.setOuIds(currentUserOuList);

        String manyStatus = purchaseContractDTO.getManyStatus();

        List<Integer> statusList = new ArrayList<>();
        if (StringUtils.isNotEmpty(manyStatus)) {
            //将manyStatus转换成Integer List
            statusList = Arrays.stream(manyStatus.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        }

        PageHelper.startPage(pageNum, pageSize);

        List<PurchaseContractDTO> duplicateNameContractList = purchaseContractExtMapper.getDuplicateNameContractList(purchaseContractDTO, statusList);

        return BeanConverter.convertPage(duplicateNameContractList, PurchaseContractVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer punishmentHandle(PurchaseContractDTO dto) {
        logger.info("采购合同罚扣处理-提交参数:{}", JSON.toJSONString(dto));
        List<Long> punishmentIds = dto.getPunishmentIds();
        Guard.notNullOrEmpty(punishmentIds, "采购合同罚扣不能为空");
        Long contractId = dto.getId();
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(contractId);
        Integer status = purchaseContract.getStatus();
        // 合同变更中，不能做罚扣处理
        if (Objects.equals(status, ContractStatus.CHANGING.getCode())) {
            throw new BizException(Code.ERROR, "合同变更中，请等待合同变更完成再做罚扣处理");
        }

        //处理付款计划
        List<PaymentPlanDTO> paymentPlans = dto.getPaymentPlans();
        if (ListUtils.isNotEmpty(paymentPlans)) {
            for (PaymentPlanDTO paymentPlan : paymentPlans) {
                Guard.notNull(paymentPlan.getId(), "付款计划ID不能为空");
                Guard.notNull(paymentPlan.getAllocationPunishmentAmountWithTax(), "付款计划分配罚款金额不能为空");
                PaymentPlanDTO paymentPlanDTO = paymentPlanService.findPaymentPlanById(paymentPlan.getId());
                paymentPlanDTO.setAllocationPunishmentAmountWithTax(paymentPlan.getAllocationPunishmentAmountWithTax());
                paymentPlanDTO.setUpdateAt(new Date());
                paymentPlanDTO.setUpdateBy(SystemContext.getUserId());
                paymentPlanService.update(paymentPlanDTO);
                paymentApplyService.updatePaymentPlanStatus(paymentPlan.getId());
                logger.info("采购合同罚扣处理-保存付款计划成功-id:{},allocationPunishmentAmountWithTax:{}", paymentPlan.getId(), paymentPlan.getAllocationPunishmentAmountWithTax());
            }
        }

        //处理WBS预算
        List<PurchaseContractBudgetDto> purchaseContractWbsBudgets = dto.getPurchaseContractWbsBudgets();
        if (ListUtils.isNotEmpty(purchaseContractWbsBudgets)) {
            for (PurchaseContractBudgetDto purchaseContractWbsBudget : purchaseContractWbsBudgets) {
                Guard.notNull(purchaseContractWbsBudget.getId(), "WBS预算ID不能为空");
                Guard.notNull(purchaseContractWbsBudget.getPrice(), "WBS预算金额不能为空");
                PurchaseContractBudget purchaseContractBudget = purchaseContractBudgetMapper.selectByPrimaryKey(purchaseContractWbsBudget.getId());
                Guard.notNull(purchaseContractBudget, "WBS预算记录不存在");
                purchaseContractBudget.setPrice(purchaseContractWbsBudget.getPrice());
                BigDecimal totalPrice = purchaseContractBudget.getNumber().multiply(purchaseContractWbsBudget.getPrice());
                purchaseContractBudget.setTotalPrice(totalPrice);
                BigDecimal conversionRate = purchaseContract.getConversionRate();
                if (conversionRate == null) conversionRate = BigDecimal.ONE;
                BigDecimal localTotalPrice = totalPrice.multiply(conversionRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                purchaseContractBudget.setLocalTotalPrice(localTotalPrice);
                purchaseContractBudget.setUpdateAt(new Date());
                purchaseContractBudget.setUpdateBy(SystemContext.getUserId());
                purchaseContractBudgetMapper.updateByPrimaryKey(purchaseContractBudget);
                logger.info("采购合同罚扣处理-保存WBS预算成功-id:{},price:{}", purchaseContractWbsBudget.getId(), purchaseContractWbsBudget.getPrice());
            }
        }

        //处理模组预算
        List<ProjectContractBudgetMaterial> projectContractBudgetMaterials = dto.getProjectContractBudgetMaterials();
        if (ListUtils.isNotEmpty(projectContractBudgetMaterials)) {
            for (ProjectContractBudgetMaterial projectContractBudgetMaterial : projectContractBudgetMaterials) {
                Guard.notNull(projectContractBudgetMaterial.getId(), "模组预算ID不能为空");
                Guard.notNull(projectContractBudgetMaterial.getExcludingTaxAmount(), "模组预算金额不能为空");
                ProjectContractBudgetMaterial projectContractBudgetMaterialEntity = projectContractBudgetMaterialMapper.selectByPrimaryKey(projectContractBudgetMaterial.getId());
                projectContractBudgetMaterialEntity.setExcludingTaxAmount(projectContractBudgetMaterial.getExcludingTaxAmount());
                projectContractBudgetMaterialEntity.setUpdateAt(new Date());
                projectContractBudgetMaterialEntity.setUpdateBy(SystemContext.getUserId());
                projectContractBudgetMaterialMapper.updateByPrimaryKey(projectContractBudgetMaterialEntity);
                logger.info("采购合同罚扣处理-保存模组预算成功-id:{},excludingTaxAmount:{}", projectContractBudgetMaterial.getId(), projectContractBudgetMaterial.getExcludingTaxAmount());
            }
        }

        //处理罚扣信息
        for (Long punishmentId : punishmentIds) {
            PurchaseContractPunishment purchaseContractPunishment = purchaseContractPunishmentMapper.selectByPrimaryKey(punishmentId);
            purchaseContractPunishment.setStatus(PurchaseContractPunishmentApproveEnums.EFFECTIVE.getCode());
            purchaseContractPunishment.setUpdateBy(SystemContext.getUserId());
            purchaseContractPunishment.setUpdateAt(new Date());
            purchaseContractPunishmentMapper.updateByPrimaryKey(purchaseContractPunishment);
            logger.info("采购合同罚扣处理-罚扣更新成功,id:{},status:{}", punishmentId, purchaseContractPunishment.getStatus());
        }
        return 0;
    }
}
