package com.midea.pam.ctc.service.impl;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDetailDto;
import com.midea.pam.common.ctc.dto.CarryoverBillAccountingDto;
import com.midea.pam.common.ctc.dto.CarryoverBillDto;
import com.midea.pam.common.ctc.dto.CostAccountingErpDto;
import com.midea.pam.common.ctc.dto.InvoiceReceivableErpDto;
import com.midea.pam.common.ctc.entity.BusiSceneNonSale;
import com.midea.pam.common.ctc.entity.BusiSceneNonSaleExample;
import com.midea.pam.common.ctc.entity.CarryoverBill;
import com.midea.pam.common.ctc.entity.CarryoverBillAccounting;
import com.midea.pam.common.ctc.entity.CarryoverBillAccountingExample;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CarryoverBillAccountingType;
import com.midea.pam.ctc.common.enums.CarryoverBillErpStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.BusiSceneNonSaleMapper;
import com.midea.pam.ctc.mapper.CarryoverBillAccountingMapper;
import com.midea.pam.ctc.mapper.CarryoverBillMapper;
import com.midea.pam.ctc.service.BusiSceneNonSaleService;
import com.midea.pam.ctc.service.CarryoverBillAccountingService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.OrganizationRelExtService;
import com.midea.pam.ctc.service.SdpService;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CarryoverBillAccountingServiceImpl implements CarryoverBillAccountingService {

    @Resource
    CarryoverBillAccountingMapper accountingMapper;
    @Resource
    CarryoverBillMapper carryoverBillMapper;
    @Resource
    private BusiSceneNonSaleMapper busiSceneNonSaleMapper;
    @Resource
    BusiSceneNonSaleService busiSceneNonSaleService;
    @Resource
    private EsbService esbService;
    @Resource
    OrganizationRelExtService organizationRelExtService;
    @Resource
    private SdpService sdpService;


    @Override
    public List<CarryoverBillAccountingDto> list(CarryoverBillAccountingDto query) {
        CarryoverBillAccountingExample example = new CarryoverBillAccountingExample();
        CarryoverBillAccountingExample.Criteria criteria = example.createCriteria();
        if (!ObjectUtils.isEmpty(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (!ObjectUtils.isEmpty(query.getCarryoverBillId())) {
            criteria.andCarryoverBillIdEqualTo(query.getCarryoverBillId());
        }
        List<CarryoverBillAccounting> accountingList = accountingMapper.selectByExample(example);
        List<CarryoverBillAccountingDto> dtoList = BeanConverter.copy(accountingList, CarryoverBillAccountingDto.class);
        for (CarryoverBillAccountingDto dto : dtoList) {
            if (1 == dto.getType()) {
                dto.setLocalAmount(dto.getIncomeAmount());
            } else {
                dto.setLocalAmount(dto.getDebitAmount());
            }
        }
        return dtoList;
    }


    @Override
    public void save(CarryoverBillDto carryoverBillDto) {
        CustomerDto customerDto = CacheDataUtils.findCustomerById(carryoverBillDto.getCustomerId());
        //保存收入记账信息
        CarryoverBillAccounting incomeAccounting = new CarryoverBillAccounting();
        incomeAccounting.setType(1);
        incomeAccounting.setGlPeriod(carryoverBillDto.getPeriodName());
        incomeAccounting.setCarryoverBillId(carryoverBillDto.getId());
        incomeAccounting.setAccountingNum(carryoverBillDto.getBillNum() + "-1");
        incomeAccounting.setCustomerId(carryoverBillDto.getCustomerId());
        if (!ObjectUtils.isEmpty(customerDto)) {
            incomeAccounting.setCustomerCode(customerDto.getCrmCode());//crm编码
        }
        incomeAccounting.setOuId(carryoverBillDto.getOuId());
        incomeAccounting.setOuName(carryoverBillDto.getOuName());
        incomeAccounting.setIncomeAmount(carryoverBillDto.getTotalIncome());
        incomeAccounting.setGlDate(DateUtils.getShortDate(new Date()));//总账日期
        incomeAccounting.setInvoiceDate(DateUtils.getShortDate(new Date()));
        incomeAccounting.setCurrency("CNY");
        this.formatIncomeBusiSceneDetail(incomeAccounting);
        incomeAccounting.setErpStatus(CarryoverBillErpStatus.NOT_SYNCHRONIZED.code());
        incomeAccounting.setDeletedFlag(Boolean.FALSE);
        accountingMapper.insert(incomeAccounting);

        //保存成本记账信息
        CarryoverBillAccounting costAccounting = new CarryoverBillAccounting();
        costAccounting.setType(2);
        costAccounting.setGlPeriod(carryoverBillDto.getPeriodName());
        costAccounting.setCarryoverBillId(carryoverBillDto.getId());
        costAccounting.setAccountingNum(carryoverBillDto.getBillNum() + "-2");
        costAccounting.setCustomerId(carryoverBillDto.getCustomerId());
        if (!ObjectUtils.isEmpty(customerDto)) {
            costAccounting.setCustomerCode(customerDto.getCrmCode());//crm编码
        }
        costAccounting.setOuId(carryoverBillDto.getOuId());
        costAccounting.setOuName(carryoverBillDto.getOuName());
        costAccounting.setGlDate(DateUtils.getShortDate(new Date()));//总账日期
        costAccounting.setCurrency("CNY");
        costAccounting.setDailyBatchNum(CacheDataUtils.generateSequence(2, ""));
        costAccounting.setDailyBatchName("PAM日记账导入" + "GL成本结转");
        this.formatCostBusiSceneDetail(costAccounting);
        costAccounting.setDebitAmount(carryoverBillDto.getCurrentCostActual());
        costAccounting.setCreditAmount(carryoverBillDto.getCurrentCostActual());
        costAccounting.setDeletedFlag(Boolean.FALSE);
        costAccounting.setErpStatus(CarryoverBillErpStatus.NOT_SYNCHRONIZED.code());
        accountingMapper.insert(costAccounting);
    }

    @Override
    public void update(CarryoverBillAccountingDto dto) {
        CarryoverBillAccounting accounting = new CarryoverBillAccounting();
        BeanUtils.copyProperties(dto, accounting);
        accountingMapper.updateByPrimaryKeySelective(accounting);
        accounting = accountingMapper.selectByPrimaryKey(accounting.getId());
        dto.setCarryoverBillId(accounting.getCarryoverBillId());
    }

    private void formatIncomeBusiSceneDetail(CarryoverBillAccounting incomeAccounting) {
        BusiSceneNonSaleExample example = new BusiSceneNonSaleExample();
        example.createCriteria().andBusiSceneCodeEqualTo("AR20190622");//todo 按编码确定?
        List<BusiSceneNonSale> entityList = busiSceneNonSaleMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(entityList)) {
            Map<String, Object> param = new HashMap();
            param.put(Constants.Page.PAGE_NUM.toString(), 1);
            param.put(Constants.Page.PAGE_SIZE.toString(), 1);
            param.put("busiSceneNonSaleId", String.valueOf(entityList.get(0).getId()));
            PageInfo<BusiSceneNonSaleDetailDto> sceneNonSaleDetailDto = busiSceneNonSaleService.detailPage(param);
            incomeAccounting.setBusiSceneId(entityList.get(0).getId());
            incomeAccounting.setBusiSceneCode(entityList.get(0).getBusiSceneCode());
            incomeAccounting.setBusiSceneName(entityList.get(0).getBusiSceneDesc());
            incomeAccounting.setCustTrxType(sceneNonSaleDetailDto.getList().get(0).getCustTrxTypeIdName());//事务处理类型
        }
    }

    private void formatCostBusiSceneDetail(CarryoverBillAccounting costAccounting) {
        BusiSceneNonSaleExample example = new BusiSceneNonSaleExample();
        example.createCriteria().andBusiSceneCodeEqualTo("GL20190612");//todo 按编码确定?
        List<BusiSceneNonSale> entityList = busiSceneNonSaleMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(entityList)) {
            Map<String, Object> param = new HashMap();
            param.put(Constants.Page.PAGE_NUM.toString(), 1);
            param.put(Constants.Page.PAGE_SIZE.toString(), 1);
            param.put("busiSceneNonSaleId", String.valueOf(entityList.get(0).getId()));
            PageInfo<BusiSceneNonSaleDetailDto> sceneNonSaleDetailDto = busiSceneNonSaleService.detailPage(param);
            BusiSceneNonSaleDetailDto sceneDetail = sceneNonSaleDetailDto.getList().get(0);
            costAccounting.setBusiSceneId(entityList.get(0).getId());
            costAccounting.setBusiSceneCode(entityList.get(0).getBusiSceneCode());
            costAccounting.setBusiSceneName(entityList.get(0).getBusiSceneDesc());
            costAccounting.setDebitSubject(sceneDetail.getAccountGroupDebit());//借方科目
            costAccounting.setCreditSubject(sceneDetail.getAccountGroupCredit());//贷方科目
        }
    }

    @Override
    public void pushToErp(Long carryoverBillId) {
        Integer erpStatus = null;
        CarryoverBillAccountingDto query = new CarryoverBillAccountingDto();
        query.setCarryoverBillId(carryoverBillId);
        List<CarryoverBillAccountingDto> dtoList = this.list(query);
        for (CarryoverBillAccountingDto dto : dtoList) {
            //未同步 or 同步失败
            if (CarryoverBillErpStatus.NOT_SYNCHRONIZED.code().equals(dto.getErpStatus()) ||
                    CarryoverBillErpStatus.SYNCHRONIZATION_FAILED.code().equals(dto.getErpStatus())) {
                if (CarryoverBillAccountingType.INCOME.code().equals(dto.getType())) {
                    //PAM-ERP-018 应收发票写入
                    HandleDispatcher.route(BusinessTypeEnums.CARRYOVER_INCOME.getCode(), String.valueOf(dto.getId()), null, null, true, null, dto.getOuId());
                    erpStatus = CarryoverBillErpStatus.SYNCHRONIZING.code();
                } else {
                    //PAM-ERP-029 总账日记账写入
                    HandleDispatcher.route(BusinessTypeEnums.CARRYOVER_COST.getCode(), String.valueOf(dto.getId()), null, null, true);
                    erpStatus = CarryoverBillErpStatus.SYNCHRONIZING.code();
                }
                //更新行状态
                if (!ObjectUtils.isEmpty(erpStatus)) {
                    dto.setErpStatus(erpStatus);
                    this.update(dto);
                }
            }
        }
        //更新头状态
        if (!ObjectUtils.isEmpty(erpStatus)) {
            CarryoverBill bill = new CarryoverBill();
            bill.setId(carryoverBillId);
            bill.setErpSyncStatus(erpStatus);
            carryoverBillMapper.updateByPrimaryKeySelective(bill);
        }
    }

    /**
     * ERP回调处理
     *
     * @param id
     * @param isSuccess
     * @param msg
     */
    @Override
    public void pamResultReturnHandle(Long id, Boolean isSuccess, String msg) {
        CarryoverBillAccountingDto dto = new CarryoverBillAccountingDto();
        dto.setId(id);
        if (isSuccess) {
            dto.setErpStatus(CarryoverBillErpStatus.SYNCED.code());
            dto.setErpMessage(StringUtil.EMPTY_STRING);
        } else {
            dto.setErpStatus(CarryoverBillErpStatus.SYNCHRONIZATION_FAILED.code());
            dto.setErpMessage(msg);
        }
        //更新行状态
        this.update(dto);
        //更新头状态
        this.updateCarryoverErpStatus(dto);
    }

    /**
     * 更新结转单状态
     *
     * @param dto
     */
    private void updateCarryoverErpStatus(CarryoverBillAccountingDto dto) {
        CarryoverBill bill = new CarryoverBill();
        bill.setId(dto.getCarryoverBillId());
        if (CarryoverBillErpStatus.SYNCHRONIZATION_FAILED.code().equals(dto.getErpStatus())) {
            bill.setErpSyncStatus(CarryoverBillErpStatus.SYNCHRONIZATION_FAILED.code());
        } else {
            //1.设置默认状态为已同步
            bill.setErpSyncStatus(CarryoverBillErpStatus.SYNCED.code());
            //2.检查所有行，如果有一个不是已同步则结转单状态为同步失败
            CarryoverBillAccountingDto query = new CarryoverBillAccountingDto();
            query.setCarryoverBillId(dto.getCarryoverBillId());
            List<CarryoverBillAccountingDto> allAccounts = this.list(query);
            for (CarryoverBillAccountingDto accounting : allAccounts) {
                if (!CarryoverBillErpStatus.SYNCED.code().equals(accounting.getErpStatus())) {
                    bill.setErpSyncStatus(CarryoverBillErpStatus.SYNCHRONIZATION_FAILED.code());//同步失败
                }
            }
        }
        carryoverBillMapper.updateByPrimaryKeySelective(bill);
    }

    @Override
    public void incomePushErpTest(Long id) {
        List<InvoiceReceivableErpDto> dtos = new ArrayList<>();
        Integer lineNum = 1;
        CarryoverBillAccountingDto query = new CarryoverBillAccountingDto();
        query.setId(id);
        List<CarryoverBillAccountingDto> dtoList = this.list(query);
        //正数行
        InvoiceReceivableErpDto positiveDto = this.formatErpInvoiceApiData(dtoList.get(0), lineNum, true);
        lineNum++;
        dtos.add(positiveDto);
        //负数行
        InvoiceReceivableErpDto dto = this.formatErpInvoiceApiData(dtoList.get(0), lineNum, false);
        lineNum++;
        dtos.add(dto);
        sdpService.callErpArInvoice(dtos, "\\收");
    }

    //todo 每次2行，数量1（正+负)，金额=数量*单价
    private InvoiceReceivableErpDto formatErpInvoiceApiData(CarryoverBillAccountingDto accountingDto, Integer lineNum, boolean isPositive) {
        InvoiceReceivableErpDto dto = new InvoiceReceivableErpDto();
        dto.setSourceLineNum(String.valueOf(accountingDto.getId()) + lineNum);
        dto.setOrgId(new BigDecimal(accountingDto.getOuId()));
        dto.setBatchSourceName("PAM导入");
        dto.setCustomerNumber(accountingDto.getCustomerCode());
        dto.setCustTrxTypeName(accountingDto.getCustTrxType());
        dto.setTermName("立即");
        dto.setTrxDate(DateUtils.formatDate(accountingDto.getGlDate()));
        dto.setGlDate(DateUtils.formatDate(accountingDto.getGlDate()));
        dto.setTrxNumber(accountingDto.getAccountingNum());
        dto.setLineNumber(new BigDecimal(lineNum.intValue()));
//        dto.setSalesOrder();
//        dto.setSalesOrderLine();
        dto.setSalesrepNumber("10156");//销售人员
        dto.setHeaderAttribute6(DateUtils.formatDate(accountingDto.getGlDate()));
        dto.setCurrencyCode(accountingDto.getCurrency());
//        dto.setConversionType();
//        dto.setConversionDate();
//        dto.setConversionRate();
        if (isPositive) {
            dto.setQuantity(new BigDecimal(1));
        } else {
            dto.setQuantity(new BigDecimal(-1));
        }
        dto.setAmount(accountingDto.getIncomeAmount().multiply(dto.getQuantity())); //数量*单价
//        dto.setQuantityOrdered();
        dto.setUnitSellingPrice(accountingDto.getIncomeAmount());
//        dto.setUnitStandardPrice();
        dto.setTaxRateCode("VAT0_OUT");
//        dto.setInventoryItemCode();
//        dto.setUomCode();
//        dto.setLineType();
        dto.setComments(dto.getCustomerNumber() + "_PAM");//todo 项目+里程碑+客户+来源
        if (isPositive) {
            dto.setDescription("收入确认");//
        } else {
            dto.setDescription("预开票冲减");
        }
        return dto;
    }

    /**
     * costPushErp
     *
     * @param id
     */
    @Override
    public void costPushErpTest(Long id) {
        Integer lineNum = 1;
        List<CostAccountingErpDto> dtos = new ArrayList<>();
        CarryoverBillAccountingDto query = new CarryoverBillAccountingDto();
        query.setId(id);
        List<CarryoverBillAccountingDto> dtoList = this.list(query);
        //借方行数据
        CostAccountingErpDto debitDto = formatCostAccountingErpDto(dtoList.get(0), lineNum, true);
        dtos.add(debitDto);
        lineNum++;
        //贷方行数据
        CostAccountingErpDto creditDto = formatCostAccountingErpDto(dtoList.get(0), lineNum, false);
        dtos.add(creditDto);
        lineNum++;
//        esbService.callCUXGLJOURNALSIMPORTAPIPKGPortType(dtos);
        sdpService.callGERPGlJournalsImport(dtos);
    }

    //todo 每次2行，Debit and Credit
    private CostAccountingErpDto formatCostAccountingErpDto(CarryoverBillAccountingDto accountingDto, Integer lineNum, boolean isDebit) {
        CostAccountingErpDto dto = new CostAccountingErpDto();
        dto.setLineNumber(new BigDecimal(lineNum));
        dto.setId(accountingDto.getId());
        dto.setLedgerId(new BigDecimal(this.getLedgerId(accountingDto.getOuId()))); //根据ouId查找ledgerId
        dto.setStatus("U");
        dto.setAccountingDate(DateUtils.formatDate(accountingDto.getGlDate()));
        dto.setActualFlag("A");
        dto.setUserJeCategoryName(".记账凭证");
        dto.setCurrencyCode(accountingDto.getCurrency());
        dto.setBathName("PAM_日记账导入+_成本结转" + accountingDto.getAccountingNum());
        dto.setJournalName("PAM_日记账导入+_成本结转" + accountingDto.getAccountingNum());
        //借方0r贷方
        String[] subject = null;
        if (isDebit) {
            dto.setEnterEddr(accountingDto.getDebitAmount());
            dto.setAccountEddr(accountingDto.getDebitAmount());
            subject = StringUtils.split(accountingDto.getDebitSubject(), ".");
        } else {
            dto.setEnterEdcr(accountingDto.getCreditAmount());
            dto.setAccountEdcr(accountingDto.getCreditAmount());
            subject = StringUtils.split(accountingDto.getCreditSubject(), ".");//贷方科目
        }
        dto.setSegment1(subject[0]);
        dto.setSegment2(subject[1]);
        dto.setSegment3(subject[2]);
        dto.setSegment4(subject[3]);
        dto.setSegment5(subject[4]);
        dto.setSegment6(subject[5]);
        dto.setSegment7(subject[6]);
        dto.setReference5("PAM_日记账导入_成本结转" + accountingDto.getAccountingNum());
        dto.setReference10("PAM_日记账导入_成本结转" + accountingDto.getAccountingNum());
        dto.setOperationType("CREATE");
        return dto;
    }

    /**
     * 根据ouId查找ledgerId
     *
     * @param ouId
     * @return
     */
    private Long getLedgerId(Long ouId) {
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setPamEnabled(0);
        query.setOperatingUnitId(ouId);
        PageInfo<OrganizationRelDto> orgDtoPage = organizationRelExtService.invokeApiList(query);
        return orgDtoPage.getList().get(0).getLedgerId();
    }

}
