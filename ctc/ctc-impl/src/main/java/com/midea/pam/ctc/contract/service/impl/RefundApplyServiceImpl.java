package com.midea.pam.ctc.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.BeanUtils;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.UnitOuRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.crm.entity.Customer;
import com.midea.pam.common.crm.entity.CustomerBankAccount;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.FinanceEntryDTO;
import com.midea.pam.common.ctc.dto.InvoiceReceivableErpDto;
import com.midea.pam.common.ctc.dto.RefundApplyDTO;
import com.midea.pam.common.ctc.dto.RefundApplyDetailDTO;
import com.midea.pam.common.ctc.dto.RefundGlperiodInfo;
import com.midea.pam.common.ctc.entity.InvoiceApplyDetailSplit;
import com.midea.pam.common.ctc.entity.InvoicePlanDetail;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.InvoiceReceivableExample;
import com.midea.pam.common.ctc.entity.MilepostTemplateStage;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ReceiptClaimContractRel;
import com.midea.pam.common.ctc.entity.ReceiptClaimContractRelExample;
import com.midea.pam.common.ctc.entity.ReceiptClaimDetail;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetail;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailExample;
import com.midea.pam.common.ctc.entity.RefundApply;
import com.midea.pam.common.ctc.entity.RefundApplyDetail;
import com.midea.pam.common.ctc.entity.RefundApplyDetailExample;
import com.midea.pam.common.ctc.entity.RefundApplyExample;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.EmailStatus;
import com.midea.pam.common.enums.GlPeriodType;
import com.midea.pam.common.enums.NoticeBusinessType;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ReceiptClaimDistributeContractType;
import com.midea.pam.common.enums.RefundStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.Backlog;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.api.server.PassGcebToPamService;
import com.midea.pam.ctc.common.enums.CommonErpStatus;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.enums.InvoiceReceivableErpStatus;
import com.midea.pam.ctc.common.enums.InvoiceReceivableSource;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.InvoicePlanService;
import com.midea.pam.ctc.contract.service.RefundApplyService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.InvoiceApplyDetailSplitMapper;
import com.midea.pam.ctc.mapper.InvoicePlanDetailMapper;
import com.midea.pam.ctc.mapper.InvoicePlanMapper;
import com.midea.pam.ctc.mapper.InvoiceReceivableMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimContractRelMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimDetailMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailMapper;
import com.midea.pam.ctc.mapper.RefundApplyDetailExtMapper;
import com.midea.pam.ctc.mapper.RefundApplyDetailMapper;
import com.midea.pam.ctc.mapper.RefundApplyExtMapper;
import com.midea.pam.ctc.mapper.RefundApplyMapper;
import com.midea.pam.ctc.service.AgencySynService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CrmExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.MilepostTemplateStageService;
import com.midea.pam.ctc.service.NoticeService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.ctc.service.WriteOffService;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: common-module
 * @description: 退款申请
 * @author:zhongpeng
 * @create:2020-08-05 08:50
 **/
public class RefundApplyServiceImpl implements RefundApplyService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BasedataExtService basedataExtService;
    @Resource
    private PassGcebToPamService passGcebToPamService;
    @Autowired
    private SdpService sdpService;
    @Resource
    private RefundApplyMapper refundApplyMapper;
    @Resource
    private RefundApplyDetailMapper refundApplyDetailMapper;
    @Resource
    private RefundApplyDetailExtMapper refundApplyDetailExtMapper;
    @Resource
    private RefundApplyExtMapper refundApplyExtMapper;
    @Resource
    private NoticeService noticeService;
    @Resource
    private CrmExtService crmExtService;
    @Resource
    private InvoicePlanMapper invoicePlanMapper;
    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;
    @Resource
    private InvoicePlanService invoicePlanService;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private MilepostTemplateStageService milepostTemplateStageService;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private ReceiptPlanDetailMapper receiptPlanDetailMapper;
    @Resource
    private ReceiptClaimContractRelMapper receiptClaimContractRelMapper;
    @Resource
    private ReceiptClaimDetailMapper receiptClaimDetailMapper;
    @Resource
    private WriteOffService writeOffService;
    @Resource
    private AgencySynService agencySynService;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private InvoiceReceivableMapper invoiceReceivableMapper;
    @Resource
    private InvoiceApplyDetailSplitMapper invoiceApplyDetailSplitMapper;

    @Value("${route.contractUrl}")
    private String contractUrl;


    @Override
    public List<RefundApply> selectByExample(RefundApplyExample example) {
        return refundApplyMapper.selectByExample(example);
    }

    @Override
    public RefundApply selectByPrimaryKey(Long id) {
        return refundApplyMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(RefundApply record) {
        return refundApplyMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(RefundApply record) {
        return refundApplyMapper.updateByPrimaryKey(record);
    }

    @Override
    public PageInfo<RefundApplyDTO> pageRefundApply(RefundApplyDTO refundApplyDTO, Integer pageNum, Integer pageSize) {
        // 构建查询条件
        Map<String, Object> param = new HashMap<>();
        buildListContractParam(refundApplyDTO, false, param);
        PageHelper.startPage(pageNum, pageSize);
        List<RefundApplyDTO> receiptClaimDtoList = refundApplyExtMapper.getRefundApplyDTOList(param);
        return BeanConverter.convertPage(receiptClaimDtoList, RefundApplyDTO.class);
    }

    /**
     * 退款申请新增or修改
     *
     * @param refundApplyDTO
     * @return
     */
    @Override
    public Long persistence(RefundApplyDTO refundApplyDTO) {
        final Long res = null == refundApplyDTO.getId() ? add(refundApplyDTO) : update(refundApplyDTO);
        return res;
    }

    /**
     * 获取退款申请详情信息
     *
     * @param id
     * @return
     */
    @Override
    public RefundApplyDTO getRefundApplyDetail(Long id) {
        return this.findById(id);
    }

    /**
     * 新增退款申请
     *
     * @param refundApplyDTO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long add(RefundApplyDTO refundApplyDTO) {
        RefundApply refundApply = new RefundApply();
        final List<RefundApplyDetailDTO> refundApplyDetailDTOList = refundApplyDTO.getRefundApplyDetailDTOList();
        // 利用Stream API获取所有不同的Currency值 不需要过滤空值
        Set<String> uniqueCurrencies = refundApplyDetailDTOList.stream()
                .map(RefundApplyDetailDTO::getCurrency)
                .collect(Collectors.toSet());
        // 如果uniqueCurrencies中的元素个数大于1，说明存在不同的Currency值
        if (uniqueCurrencies.size() > 1) {
            // 存在不同的Currency值
            throw new MipException("退款单币种与合同币种不一致，暂不支持该场景退款。");
        }
        if (StringUtils.isEmpty(refundApplyDTO.getCurrency()) || !uniqueCurrencies.contains(refundApplyDTO.getCurrency())) {
            throw new MipException("退款单币种与合同币种不一致，暂不支持该场景退款。");
        }

        String dictName = "客户退款应收发票类型";
        Set<String> valueSet = organizationCustomDictService.queryByName(dictName, refundApplyDTO.getOuId(), OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));

        if (CollectionUtils.isEmpty(valueSet)) {
            throw new MipException("该ou下没有配置组织类型，无法做开票申请！");
        }

        Long companyId = null;
        UnitOuRel unitOuRel = CacheDataUtils.findUnitOuRelByOuId(refundApplyDTO.getOuId());
        final OperatingUnit operatingUnit = CacheDataUtils.findOuById(refundApplyDTO.getOuId());
        if (unitOuRel != null && unitOuRel.getUnitId() != null) {
            Long unitId = unitOuRel.getUnitId();
            companyId = CacheDataUtils.getTopUnitIdByUnitId(unitId);//公司id
        }
        String seqPerfix = basedataExtService.getUnitSeqPerfix(companyId);
        String code = "TK";
        code = seqPerfix + code + CacheDataUtils.generateSequence(3, seqPerfix + code, DateUtil.DATE_YYMM_PATTERN);

        refundApplyDTO.setRefundApplyCode(code);
        refundApplyDTO.setRefundApplyStatus(RefundStatus.DRAFT.getCode());
        refundApplyDTO.setDeletedFlag(Boolean.FALSE);
        refundApplyDTO.setApplyDate(new Date());
        refundApplyDTO.setApplyBy(SystemContext.getUserId());
        refundApplyDTO.setAttribute1("0");
        if (operatingUnit != null) {
            refundApplyDTO.setOuName(operatingUnit.getOperatingUnitName());
        }
        if (!StringUtils.isNotEmpty(refundApplyDTO.getInvoiceCustomeName())) {
            // 取开票客户名称【BUG2020111964038】
            final CustomerBankAccount customerBankInfo = crmExtService.getCustomerBankInfo(refundApplyDTO.getCustomerCode(), refundApplyDTO.getOuId());
            String invoiceCustomerName = "";
            if (customerBankInfo != null) {
                invoiceCustomerName = customerBankInfo.getInvoiceName();
            }
            refundApplyDTO.setInvoiceCustomeName(invoiceCustomerName);
        }
        final UserInfo userInfo = CacheDataUtils.findUserById(SystemContext.getUserId());
        if (userInfo != null) {
            refundApplyDTO.setApplyName(userInfo.getName());
        }
        BeanUtils.copyProperties(refundApplyDTO, refundApply);
        refundApplyMapper.insert(refundApply);

        if (CollectionUtils.isNotEmpty(refundApplyDetailDTOList)) {
            final List<RefundApplyDetail> refundApplyDetailList = BeanUtils.copyPropertiesByList(refundApplyDetailDTOList, RefundApplyDetail.class);
            refundApplyDetailList.forEach(refundApplyDetail -> {
                refundApplyDetail.setRefundApplyId(refundApply.getId());
                refundApplyDetail.setDeletedFlag(Boolean.FALSE);
                refundApplyDetailMapper.insert(refundApplyDetail);
            });
        }

        return refundApply.getId();
    }

    /**
     * 修改退款申请(用于编辑草稿状态的退款申请)
     *
     * @param refundApplyDTO
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long update(RefundApplyDTO refundApplyDTO) {
        Long refundApplyId = refundApplyDTO.getId();
        List<RefundApplyDetailDTO> refundApplyDetailDTOList = refundApplyDTO.getRefundApplyDetailDTOList();
        // 删除行信息
        refundApplyDetailExtMapper.deleteByHeaderId(refundApplyId);
        // 插入行信息
        if (CollectionUtils.isNotEmpty(refundApplyDetailDTOList)) {
            List<RefundApplyDetail> refundApplyDetailList = BeanUtils.copyPropertiesByList(refundApplyDetailDTOList, RefundApplyDetail.class);
            refundApplyDetailList.forEach(refundApplyDetail -> {
                refundApplyDetail.setId(null);
                refundApplyDetail.setRefundApplyId(refundApplyId);
                refundApplyDetail.setDeletedFlag(Boolean.FALSE);
                refundApplyDetailMapper.insert(refundApplyDetail);
            });
        }
        refundApplyMapper.updateByPrimaryKey(refundApplyDTO);
        return refundApplyDTO.getId();
    }

    private void emailNotice(RefundApply refundApply) {
        List<RefundApplyDetailDTO> list = refundApplyDetailExtMapper.getList(refundApply);

        List<Map<Long, List<RefundApplyDetailDTO>>> mapList = new ArrayList<>();
        Map<Long, List<RefundApplyDetailDTO>> map1 = list.stream()
                .filter(e -> Objects.nonNull(e.getManagerId()))
                .collect(Collectors.groupingBy(RefundApplyDetailDTO::getManagerId));

        Map<Long, List<RefundApplyDetailDTO>> map2 = list.stream()
                .filter(e -> Objects.nonNull(e.getSalesManagerId()))
                .collect(Collectors.groupingBy(RefundApplyDetailDTO::getSalesManagerId));

        if (!map1.isEmpty()) {
            mapList.add(map1);
        }
        if (!map2.isEmpty()) {
            mapList.add(map2);
        }

        List<Email> emails = convertToEmail(mapList);
        logger.info("退款+提醒邮件待推送数据总数：{}", emails.size());
        // 邮件推送
        noticeService.sendMail(emails);

        // 待办通知
        List<Backlog> backlogs = convertToBacklog(mapList);
        logger.info("退款提醒邮件待推送数据总数：{}", emails.size());
        // 待办推送
        noticeService.sendBacklog(backlogs);
    }

    private List<Backlog> convertToBacklog(List<Map<Long, List<RefundApplyDetailDTO>>> maplist) {
        List<Backlog> backlogs = new ArrayList<>();

        for (Map<Long, List<RefundApplyDetailDTO>> map : maplist) {

            if (map == null || map.size() == 0) {
                return backlogs;
            }
            Set<Map.Entry<Long, List<RefundApplyDetailDTO>>> entries = map.entrySet();
            for (Map.Entry<Long, List<RefundApplyDetailDTO>> entry : entries) {
                Long userId = entry.getKey();
                List<RefundApplyDetailDTO> list = entry.getValue();

                if (userId == null) {
                    continue;
                }
                for (RefundApplyDetailDTO dto : list) {

                    Backlog backlog = new Backlog();

                    backlog.setSubject("主合同名称：" + dto.getParentContractName() + ",本次退款金额:" + dto.getThisAmount());
                    backlog.setMobileFlag(Boolean.FALSE);
                    backlog.setDeletedFlag(Boolean.FALSE);
                    backlog.setStatus(EmailStatus.TO_DO.getCode());
                    UserInfo userInfo = CacheDataUtils.findUserById(userId);
                    if (userInfo == null) {
                        continue;
                    }
                    backlog.setReceiver(userInfo.getUsername());
                    backlog.setLongSubject("主合同名称：" + dto.getParentContractName() + ",本次退款金额:" + dto.getThisAmount());
                    backlog.setBusinessType(NoticeBusinessType.REFUND_APPLY.getType());
                    backlog.setDeletedFlag(Boolean.FALSE);
                    backlog.setStatus(EmailStatus.TO_DO.getCode());
                    backlogs.add(backlog);
                }

            }
        }

        return backlogs;
    }

    private List<Email> convertToEmail(List<Map<Long, List<RefundApplyDetailDTO>>> mapList) {
        List<Email> emails = new ArrayList<>();
        for (Map<Long, List<RefundApplyDetailDTO>> map : mapList) {
            if (map == null || map.size() == 0) {
                return emails;
            }
            Set<Map.Entry<Long, List<RefundApplyDetailDTO>>> entries = map.entrySet();
            for (Map.Entry<Long, List<RefundApplyDetailDTO>> entry : entries) {
                Long userId = entry.getKey();
                List<RefundApplyDetailDTO> list = entry.getValue();
                if (userId == null) {
                    continue;
                }

                Email email = new Email();
                email.setSubject("PAM系统退款提醒");
                email.setLanguage("zh-CN");
                UserInfo userInfo = CacheDataUtils.findUserById(userId);
                if (userInfo == null) {
                    continue;
                }
                email.setReceiver(userInfo.getUsername());
                String content = buildContent(list);
                email.setContent(content);
                email.setBusinessType(NoticeBusinessType.REFUND_APPLY.getType());
                email.setDeletedFlag(Boolean.FALSE);
                email.setStatus(EmailStatus.TO_DO.getCode());
                email.setFromAddress("pam");
                emails.add(email);
            }
        }
        return emails;
    }

    private String buildContent(List<RefundApplyDetailDTO> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        String header = "<div style='font-size: 12px;'>" + "您所负责的合同存在退款，请登录系统查阅详情" + "</div><br/>";

        String table = buildTable(list);
        sb.append(header);

        sb.append(table);
        sb.append("</html>");
        return sb.toString();
    }

    private String buildTable(List<RefundApplyDetailDTO> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("<table style='font-size: 12px;border-collapse:collapse;table-layout: fixed;word-wrap:break-word;width: 1000px;'>");
        sb.append("<tr>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:15%;'>序号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>项目编号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:70%;'>项目名称</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:100%;'>客户名称</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>本次退款金额</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>到款日期</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:50%;'>业务分类</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:40%;'>子合同编号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:70%;'>子合同名称</td>");
        sb.append("</tr>");
        int n = 1;
        for (RefundApplyDetailDTO dto : list) {
            sb.append("<tr>");
            String projectCode = dto.getProjectCode();
            String projectName = dto.getProjectName();
            BigDecimal thisAmount = dto.getThisAmount();
            Date payDate = dto.getPayDate();
            String unitName = CacheDataUtils.findUnitById(dto.getProfitDepartmentId()).getUnitName();
            String contractCode = dto.getContractCode();
            String contractName = dto.getContractName();
            Long contractId = dto.getContractId();
            sb.append(buildTd(String.valueOf(n++)));
            sb.append(buildTd(projectCode));
            sb.append(buildTd(projectName));
            sb.append(buildTd(dto.getCustomerName()));
            sb.append(buildTd(String.valueOf(thisAmount)));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sb.append(buildTd(sdf.format(payDate)));
            sb.append(buildTd(unitName));
            sb.append(buildTd(contractCode));
            sb.append(buildTdHref(contractName, contractUrl + contractId));
            sb.append("</tr>");
        }
        sb.append("</table>");
        return sb.toString();
    }

    private String buildTd(String value) {
        return "<td style='border: #999 1px solid; align:center;'>" + (value == null ? "" : value) + "</td>";
    }

    private String buildTdHref(String value, String url) {
        String urlStr = " <a href='" + url + "' target='_blank'>" + value + "</a>";
        return "<td style='border: #999 1px solid; align:center;'>" + urlStr + "</td>";
    }

    @Override
    public RefundApplyDTO findById(Long id) {
        if (id == null) {
            return null;
        }
        RefundApply refundApply = refundApplyMapper.selectByPrimaryKey(id);
        if (refundApply == null) {
            return null;
        }

        final OperatingUnit operatingUnit = CacheDataUtils.findOuById(refundApply.getOuId());
        if (operatingUnit != null) {
            refundApply.setOuName(operatingUnit.getOperatingUnitName());
        }
        RefundApplyDTO refundApplyDTO = BeanConverter.copy(refundApply, RefundApplyDTO.class);
        String customerCode = refundApplyDTO.getCustomerCode();
        if (StringUtils.isNotEmpty(customerCode)) {
            List<String> customerCodeList = Collections.singletonList(customerCode);
            Map<String, CustomerDto> customerMap = crmExtService.getCustomerByCode(customerCodeList)
                    .stream().collect(Collectors.toMap(CustomerDto::getCrmCode, e -> e));
            Customer customer = customerMap.get(customerCode);
            if (Objects.nonNull(customer)) {
                refundApplyDTO.setCustomerId(customer.getId());
            }
        }
        List<FinanceEntryDTO> financeEntryDTOList = new ArrayList<>();//财务入账信息
        RefundApplyDetailExample refundApplyDetailExample = new RefundApplyDetailExample();
        refundApplyDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andRefundApplyIdEqualTo(id);
        List<RefundApplyDetail> refundApplyDetailList = refundApplyDetailMapper.selectByExample(refundApplyDetailExample);
        if (CollectionUtils.isNotEmpty(refundApplyDetailList)) {
            final List<RefundApplyDetailDTO> refundApplyDetailDTOList = BeanConverter.convert(refundApplyDetailList, RefundApplyDetailDTO.class);
            for (RefundApplyDetailDTO refundApplyDetailDTO : refundApplyDetailDTOList) {
                refundApplyDetailDTO.setRefundApplyCode(refundApplyDTO.getRefundApplyCode());
                refundApplyDetailDTO.setRefundApplyStatus(refundApplyDTO.getRefundApplyStatus());

                final Long invoicePlanDetailId = refundApplyDetailDTO.getInvoicePlanDetailId();
                if (invoicePlanDetailId != null) {
                    final InvoicePlanDetail invoicePlanDetail = invoicePlanDetailMapper.selectByPrimaryKey(invoicePlanDetailId);
                    if (invoicePlanDetail != null) {
                        refundApplyDetailDTO.setInvoicePlanDetailCode(invoicePlanDetail.getCode());
                        final Long milestoneId = invoicePlanDetail.getMilestoneId();
                        if (milestoneId != null) {
                            final ProjectMilepost projectMilepost = projectMilepostService.selectByPrimaryKey(milestoneId);
                            if (projectMilepost != null) {
                                refundApplyDetailDTO.setMilestoneName(projectMilepost.getName());
                            } else {
                                final MilepostTemplateStage milepostTemplateStage = milepostTemplateStageService.selectByPrimaryKey(milestoneId);
                                if (milepostTemplateStage != null) {
                                    refundApplyDetailDTO.setMilestoneName(milepostTemplateStage.getMilepostStage());
                                }
                            }
                        }
                    }
                }
                final ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(refundApplyDetailDTO.getReceiptClaimDetailId());

                refundApplyDetailDTO.setClaimAmount(receiptClaimDetail.getClaimAmount());
                //已申请退款金额
                BigDecimal contractRefundedAmount = refundApplyDetailExtMapper.getRelfundedAmount(invoicePlanDetailId);
                refundApplyDetailDTO.setContractRefundedAmount(contractRefundedAmount);

                ReceiptClaimContractRelExample relExample = new ReceiptClaimContractRelExample();
                relExample.createCriteria().andReceiptClaimDetailIdEqualTo(refundApplyDetailDTO.getReceiptClaimDetailId())
                        .andContractIdEqualTo(refundApplyDetailDTO.getContractId())
                        .andInvoicePlanDetailIdEqualTo(refundApplyDetailDTO.getInvoicePlanDetailId())
                        .andDeletedFlagEqualTo(0);
                final List<ReceiptClaimContractRel> receiptClaimContractRelList = receiptClaimContractRelMapper.selectByExample(relExample);
                if (CollectionUtils.isNotEmpty(receiptClaimContractRelList)) {
                    refundApplyDetailDTO.setAllocatedAmount(receiptClaimContractRelList.get(0).getAllocatedAmount());
                }

            }


            refundApplyDTO.setRefundApplyDetailDTOList(refundApplyDetailDTOList);
        }
        // 获取财务入账信息
        if (refundApplyDTO.getRefundApplyStatus().equals(RefundStatus.PAYMENTTED.getCode())) {
            FinanceEntryDTO financeEntryDTOReceipt = new FinanceEntryDTO();
            financeEntryDTOReceipt.setId(refundApplyDTO.getId());
            financeEntryDTOReceipt.setBillType("收款");
            financeEntryDTOReceipt.setBillNumber(refundApplyDTO.getRefundCashCode());
            financeEntryDTOReceipt.setBillAmount(refundApplyDTO.getAmount());
            financeEntryDTOReceipt.setSundryType("A02客户退款");
            financeEntryDTOList.add(financeEntryDTOReceipt);
        }
        if (CollectionUtils.isNotEmpty(refundApplyDetailList)) {
            List<Long> refundApplyDetailIds = refundApplyDetailList.stream().map(RefundApplyDetail::getId).collect(Collectors.toList());
            InvoiceReceivableExample invoiceReceivableExample = new InvoiceReceivableExample();
            invoiceReceivableExample.createCriteria().andRefundApplyDetailIdIn(refundApplyDetailIds).andDeletedFlagEqualTo(0);
            List<InvoiceReceivable> invoiceReceivableList = invoiceReceivableMapper.selectByExample(invoiceReceivableExample);
            if (CollectionUtils.isNotEmpty(invoiceReceivableList)) {
                invoiceReceivableList.forEach(invoiceReceivable -> {
                    FinanceEntryDTO financeEntryDTORefund = new FinanceEntryDTO();
                    financeEntryDTORefund.setId(invoiceReceivable.getId());
                    financeEntryDTORefund.setBillType("发票");
                    financeEntryDTORefund.setBillNumber(invoiceReceivable.getInvoiceCode());
                    financeEntryDTORefund.setBillAmount(invoiceReceivable.getTaxIncludedPrice());
                    financeEntryDTORefund.setInvoiceType(invoiceReceivable.getInvoiceType());
                    financeEntryDTORefund.setApplyHeaderId(invoiceReceivable.getApplyHeaderId());
                    financeEntryDTORefund.setCustomerId(invoiceReceivable.getCustomerId());
                    financeEntryDTOList.add(financeEntryDTORefund);
                });
            }
        }
        refundApplyDTO.setFinanceEntryDTOList(financeEntryDTOList);
        return refundApplyDTO;
    }

    @Override
    public RefundApplyDTO findByRefundCode(String refundCode) {
        if (StringUtils.isEmpty(refundCode)) {
            return null;
        }
        RefundApplyExample example = new RefundApplyExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andRefundApplyCodeEqualTo(refundCode);

        List<RefundApply> refundApplyList = refundApplyMapper.selectByExample(example);
        if (ListUtils.isEmpty(refundApplyList)) {
            return null;
        }
        RefundApplyDTO refundApplyDTO = BeanConverter.copy(refundApplyList.get(0), RefundApplyDTO.class);
        String customerCode = refundApplyDTO.getCustomerCode();
        if (StringUtils.isNotEmpty(customerCode)) {
            List<String> customerCodeList = Collections.singletonList(customerCode);
            Map<String, CustomerDto> customerMap = crmExtService.getCustomerByCode(customerCodeList)
                    .stream().collect(Collectors.toMap(CustomerDto::getCrmCode, e -> e));
            Customer customer = customerMap.get(customerCode);
            if (Objects.nonNull(customer)) {
                refundApplyDTO.setCustomerId(customer.getId());
            }
        }
        RefundApplyDetailExample refundApplyDetailExample = new RefundApplyDetailExample();
        refundApplyDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andRefundApplyIdEqualTo(refundApplyDTO.getId());
        List<RefundApplyDetail> refundApplyDetailList = refundApplyDetailMapper.selectByExample(refundApplyDetailExample);
        refundApplyDTO.setRefundApplyDetailDTOList(BeanConverter.convert(refundApplyDetailList, RefundApplyDetailDTO.class));
        return refundApplyDTO;
    }

    /**
     * @param refundCode     退款编号
     * @param isSuccess
     * @param msg
     * @param refundCashCode 资金流水号
     */
    @Override
    public void refundApplyResultReturnHandle(String refundCode, boolean isSuccess, String msg, String handleStatus, String refundCashCode) {
        RefundApplyDTO refundApply = findByRefundCode(refundCode);
        if (refundApply != null) {
            if (isSuccess) {
//                refundApply.setOuterStatus(RefundStatus.NOT_SYNCHRONIZED.getCode());
                refundApply.setRefundApplyStatus(RefundStatus.PAYMENTTED.getCode());
                refundApply.setRefundCashCode(refundCashCode);
                refundApply.setOuterMsg(null);
                refundApply.setAttribute2(DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                refundApplyMapper.updateByPrimaryKey(refundApply);

                List<RefundApplyDetailDTO> refundApplyDetailDTOList = refundApply.getRefundApplyDetailDTOList();
                if (ListUtils.isNotEmpty(refundApplyDetailDTOList)) {
                    refundApplyDetailDTOList.forEach(e -> {
                        //更新开票计划可分配额allocatable_amount
                        logger.info("更新开票计划实际回款金额actualReceiptAmount");
                        invoicePlanService.updateAllocatableAmount(e.getInvoicePlanDetailId(), ReceiptClaimDistributeContractType.REFUND);
                    });
                    //重新计算所有涉及合同的回款金额
                    List<Long> contractIds = refundApplyDetailDTOList.stream().map(RefundApplyDetailDTO::getContractId).collect(Collectors.toList());
                    writeOffService.reCalculateContractReceiptAmount(contractIds);
                    //按照refund_apply_detail生成应收发票插invoice_receivable表
                    generateInvoice(refundApply);
                }
            } else {
                if ("3".equals(handleStatus) || "2".equals(handleStatus)) {
                    //取消支付
                    refundApply.setOuterStatus(RefundStatus.SYNCED.getCode());
                    refundApply.setRefundApplyStatus(RefundStatus.CANCEL.getCode());
                } else {
                    refundApply.setOuterStatus(RefundStatus.SYNCHRONIZATION_FAILED.getCode());
                    refundApply.setRefundApplyStatus(RefundStatus.PAYMENTTING.getCode());
                }
                refundApply.setOuterMsg(msg);
                refundApplyMapper.updateByPrimaryKey(refundApply);
            }

            if (refundApply.getRefundApplyStatus() == RefundStatus.PAYMENTTED.getCode()) {
                //发送邮件提醒
                try {
                    this.emailNotice(refundApply);
                } catch (Exception e) {
                    logger.error("退款：{}，邮件发送异常", refundCode, e);
                }
            }
        }
    }

    private void generateInvoice(RefundApplyDTO refundApply) {
        List<RefundApplyDetailDTO> dtos = refundApply.getRefundApplyDetailDTOList();
        int i = 1;
        for (RefundApplyDetailDTO dto : dtos) {
            //发票头
            InvoiceReceivable invoice = new InvoiceReceivable();
            invoice.setApplyHeaderId(0L);
            invoice.setApplyDetailId(0L);
            invoice.setContractId(dto.getContractId());
            invoice.setContractCode(dto.getContractCode());
            invoice.setCustomerId(refundApply.getCustomerId());
            invoice.setOuId(refundApply.getOuId());
            invoice.setInvoiceCode(refundApply.getRefundApplyCode() + "-" + i);
            invoice.setCurrencyCode(dto.getCurrency());
            invoice.setLimitPrice(new BigDecimal(999999));
            invoice.setTaxIncludedPrice(dto.getRefundAmount());
            invoice.setExclusiveOfTax(dto.getRefundAmount());
            // 默认税率为0，税码为"VAT0_OUT"
            invoice.setTaxRace(BigDecimal.ZERO);
            invoice.setTaxCode("VAT0_OUT");
            invoice.setExchangeRateType(refundApply.getExchangeRateType());
            Dict dict = basedataExtService.findDictById(Long.parseLong(refundApply.getExchangeRateType()));
            if (dict != null) {
                invoice.setExchangeRateType(dict.getName());
            }
            invoice.setExchangeRateDate(refundApply.getExchangeRateDate());
            invoice.setStatus("审批通过");
            invoice.setExternalErpStatus(CommonErpStatus.NOT_PUSH.code().toString());
            invoice.setErpStatus(InvoiceReceivableErpStatus.TODO.getCode());
            invoice.setDeletedFlag(DeletedFlagEnum.VALID.code());
            invoice.setCreateAt(new Date());
            invoice.setCreateBy(refundApply.getCreateBy());
            // 客户退款应收发票类型
            String dictName = "客户退款应收发票类型";
            Set<String> valueSet = organizationCustomDictService.queryByName(dictName, refundApply.getOuId(), OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
            if (!org.springframework.util.CollectionUtils.isEmpty(valueSet)) {
                String next = valueSet.iterator().next();
                invoice.setInvoiceType(next);
            }
            invoice.setGlDate(refundApply.getRefundEntryDate());
            invoice.setCustomerName(refundApply.getCustomerName());
            invoice.setCustomerCode(refundApply.getCustomerCode());
            invoice.setSendToKpy(0);//不同步到开票易
            invoice.setConversionRate(BigDecimal.ONE);
            invoice.setSyncErpTime(new Date());
            invoice.setSource(InvoiceReceivableSource.REFUND_APPLY.getCode());
            invoice.setDueDateStatus(3);
            invoice.setRefundApplyDetailId(dto.getId());
            invoiceReceivableMapper.insertSelective(invoice);

            //发票行(开票明细)
            InvoiceApplyDetailSplit invoiceDetail = new InvoiceApplyDetailSplit();
            invoiceDetail.setInvoiceReceivableId(invoice.getId());
            invoiceDetail.setProduct(InvoiceReceivableSource.REFUND_APPLY.getName());
            invoiceDetail.setProductTaxName(InvoiceReceivableSource.REFUND_APPLY.getName());
            invoiceDetail.setUnit("笔");
            invoiceDetail.setQuantity(BigDecimal.ONE);
            // 默认税率为0，税码为"VAT0_OUT"
            invoiceDetail.setTaxRace(BigDecimal.ZERO);
            invoiceDetail.setTaxCode("VAT0_OUT");
            invoiceDetail.setPrice(dto.getRefundAmount());
            invoiceDetail.setExclusiveOfTax(dto.getRefundAmount());
            invoiceDetail.setTaxIncludedPrice(dto.getRefundAmount());
            invoiceDetail.setRemark(InvoiceReceivableSource.REFUND_APPLY.getName());
            invoiceDetail.setCreateBy(refundApply.getCreateBy());
            invoiceDetail.setDeletedFlag(DeletedFlag.VALID.code());
            invoiceApplyDetailSplitMapper.insertSelective(invoiceDetail);
            i++;
            //插入erp推送队列
            HandleDispatcher.route(BusinessTypeEnums.INVOICE_RECEIVABLE.getCode(), String.valueOf(invoice.getId()), String.valueOf(invoice.getId()), null, true, null, invoice.getOuId());
            agencySynService.syndataToErp(BusinessTypeEnums.INVOICE_RECEIVABLE.getCode(), invoice.getId());
        }
    }

    /**
     * @param id
     * @param isSuccess
     * @param msg
     */
    @Override
    public void invoiceRefundApplyResultReturnHandle(Long id, boolean isSuccess, String msg) {
        RefundApply refundApply = refundApplyMapper.selectByPrimaryKey(id);
        if (refundApply != null) {
            if (isSuccess) {
                refundApply.setOuterStatus(RefundStatus.SYNCED.getCode());
                refundApply.setOuterMsg(null);
            } else {
                refundApply.setOuterStatus(RefundStatus.SYNCHRONIZATION_FAILED.getCode());
                refundApply.setOuterMsg(msg);
            }
            refundApplyMapper.updateByPrimaryKey(refundApply);
        }

    }

    @Override
    public RefundApplyDTO pushToGceb(RefundApplyDTO refundApplyDTO) {
        //退款申请单
        refundApplyDTO = findById(refundApplyDTO.getId());
        if (null == refundApplyDTO) {
            throw new BizException(Code.ERROR, "退款申请单不能为空");
        }
        passGcebToPamService.refundPaymentApply(refundApplyDTO);
        return refundApplyDTO;
    }

    @Override
    public RefundApplyDTO pushToGerp(RefundApplyDTO refundApply) {
        //退款申请单
        refundApply = findById(refundApply.getId());
        if (null == refundApply) {
            throw new BizException(Code.ERROR, "退款申请单不能为空");
        }
        List<InvoiceReceivableErpDto> receivableErpDtos = new ArrayList<>();
        InvoiceReceivableErpDto dto = new InvoiceReceivableErpDto();
        dto.setId(refundApply.getId());
        dto.setSourceNum(refundApply.getRefundApplyCode());//*来源编号  header num
        dto.setSourceLineNum(String.valueOf(refundApply.getId()));//*来源行编号 row num
        dto.setEsbDataSource(BusinessTypeEnums.INVOICE_REFUND_APPLY.getCode());//PAM-ERP-018-3
        dto.setOrgId(BigDecimal.valueOf(refundApply.getOuId()));//*业务实体ID
        dto.setBatchSourceName("PAM导入");//*事务来源名称
        dto.setCustomerNumber(refundApply.getCustomerCode());//*客户编码
        // 组织参数控制金额
        String dictName = "客户退款应收发票类型";
        Set<String> valueSet = organizationCustomDictService.queryByName(dictName, refundApply.getOuId(), OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
        if (!org.springframework.util.CollectionUtils.isEmpty(valueSet)) {
            String next = valueSet.iterator().next();
            dto.setCustTrxTypeName(next);//*事务处理类型名称
        }
        dto.setTrxNumber(refundApply.getRefundApplyCode());//*事务处理编号
        dto.setTermName("立即");//*付款条件
        dto.setTrxDate(DateUtils.formatDate(new Date()));//*事务处理日期
        dto.setGlDate(DateUtils.formatDate(refundApply.getRefundEntryDate()));//*总账日期
        dto.setLineNumber(BigDecimal.valueOf(1));//*事务处理行号
        dto.setSalesrepNumber("10156");//销售人员
        dto.setCurrencyCode(refundApply.getCurrency());//*币种
        dto.setAmount(refundApply.getAmount());//*含税金额
        dto.setQuantity(BigDecimal.ONE);//*数量
        dto.setUnitSellingPrice(refundApply.getAmount());//*单价=金额/数量
        dto.setTaxRateCode("VAT0_OUT");//*税码
        dto.setComments(refundApply.getRemark());//*备注
        dto.setDescription("退款");//*摘要
        dto.setHeaderAttribute6(DateUtils.formatDate(refundApply.getRefundEntryDate()));//*账龄起算日期
        dto.setHeaderAttribute11(refundApply.getRefundApplyCode());//中转关联号
        logger.info("{}", JSON.toJSON(dto));
        logger.info("-------------------------------------------");
        receivableErpDtos.add(dto);
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(receivableErpDtos)) {
            sdpService.callErpArInvoice(receivableErpDtos, "\\收");
        }
        return refundApply;
    }

    @Override
    public Boolean abandonRefundApply(Long id) {
        RefundApply refundApply = refundApplyMapper.selectByPrimaryKey(id);
        if (refundApply != null) {
            refundApply.setRefundApplyStatus(RefundStatus.CANCEL.getCode());
            refundApplyMapper.updateByPrimaryKey(refundApply);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Transactional
    @Override
    public void asynRefundAmount(String date) {
        RefundApplyExample refundApplyExample = new RefundApplyExample();
        refundApplyExample.createCriteria().andRefundApplyStatusEqualTo(RefundStatus.PAYMENTTED.getCode())
                .andAttribute1NotEqualTo("1").andDeletedFlagEqualTo(Boolean.FALSE);
        final List<RefundApply> refundApplyList = refundApplyMapper.selectByExample(refundApplyExample);
        for (RefundApply refundApply : refundApplyList) {

            RefundApplyDetailExample refundApplyDetailExample = new RefundApplyDetailExample();
            refundApplyDetailExample.createCriteria().andRefundApplyIdEqualTo(refundApply.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
            final List<RefundApplyDetail> refundApplyDetailList = refundApplyDetailMapper.selectByExample(refundApplyDetailExample);
            if (CollectionUtils.isNotEmpty(refundApplyDetailList)) {
                Map<Long, List<RefundApplyDetail>> map
                        = refundApplyDetailList.stream().collect(Collectors.groupingBy(d -> d.getContractId()));
                for (Long contractId : map.keySet()) {
                    final List<RefundApplyDetail> details = map.get(contractId);
                    BigDecimal refundApplyTotalAmount = BigDecimal.ZERO;
                    for (RefundApplyDetail detail : details) {
                        BigDecimal refundAmount = detail.getRefundAmount() == null ? BigDecimal.ZERO : detail.getRefundAmount();
                        refundApplyTotalAmount = refundApplyTotalAmount.add(refundAmount);
                    }

                    //找出该子合同对应的回款计划信息
                    ReceiptPlanDetailExample receiptPlanDetailExample = new ReceiptPlanDetailExample();
                    receiptPlanDetailExample.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE);
                    List<ReceiptPlanDetail> receiptPlanDetailList = receiptPlanDetailMapper.selectByExample(receiptPlanDetailExample);
                    if (CollectionUtils.isNotEmpty(receiptPlanDetailList)) {
                        for (ReceiptPlanDetail receiptPlanDetail : receiptPlanDetailList) {
                            if (BigDecimalUtils.isEquals(BigDecimal.ZERO, refundApplyTotalAmount)) {
                                break;
                            }
                            BigDecimal receiptActualAmount = receiptPlanDetail.getActualAmount() == null ? BigDecimal.ZERO : receiptPlanDetail.getActualAmount();
                            if (BigDecimalUtils.isEquals(BigDecimal.ZERO, receiptActualAmount)) {
                                continue;
                            } else {
                                if (BigDecimalUtils.isGreater(refundApplyTotalAmount, receiptActualAmount)) {
                                    refundApplyTotalAmount = refundApplyTotalAmount.subtract(receiptActualAmount);
                                    receiptPlanDetail.setActualAmount(BigDecimal.ZERO);
                                } else {
                                    receiptPlanDetail.setActualAmount(receiptActualAmount.subtract(refundApplyTotalAmount));
                                    refundApplyTotalAmount = BigDecimal.ZERO;
                                }
                                receiptPlanDetailMapper.updateByPrimaryKey(receiptPlanDetail);
                            }

                        }
                    }


                }


            }

            refundApply.setAttribute1("1");
            refundApplyMapper.updateByPrimaryKey(refundApply);
        }
    }

    @Override
    public ResponseMap getRefundApplyApp(Long id) {
        final RefundApplyDTO refundApplyDTO = this.findById(id);
        if (StringUtils.isNotEmpty(refundApplyDTO.getPaymentType())) {
            final DictDto dictByTypeAndCode = CacheDataUtils.findDictByTypeAndCode("Receipt refund", refundApplyDTO.getPaymentType());
            refundApplyDTO.setPaymentType(dictByTypeAndCode.getName());
        }
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<AduitAtta> fileList = new ArrayList<>();
        if (ObjectUtils.isEmpty(refundApplyDTO)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("数据为空");
        } else {
            //移动审批头数据
            if (!ObjectUtils.isEmpty(refundApplyDTO)) {
                headMap.put("refundApplyCode", refundApplyDTO.getRefundApplyCode() == null ? " " : refundApplyDTO.getRefundApplyCode());
                headMap.put("returnOriginalWayIs", Objects.equals(refundApplyDTO.getReturnOriginalWayIs(), 1) ? "是" : "否");
                headMap.put("customerName", refundApplyDTO.getCustomerName() == null ? " " : refundApplyDTO.getCustomerName());
                headMap.put("amount", refundApplyDTO.getAmount() == null ? " " : refundApplyDTO.getAmount().toString());
                headMap.put("currency", refundApplyDTO.getCurrency() == null ? " " : refundApplyDTO.getCurrency());
                headMap.put("paymentType", refundApplyDTO.getPaymentType() == null ? " " : refundApplyDTO.getPaymentType());
                headMap.put("receiptCode", refundApplyDTO.getReceiptCode() == null ? " " : refundApplyDTO.getReceiptCode());
                headMap.put("ouName", refundApplyDTO.getOuName() == null ? "" : refundApplyDTO.getOuName());
                headMap.put("applyDate", refundApplyDTO.getApplyDate() == null ? "" : DateUtil.format(refundApplyDTO.getApplyDate(), DateUtil.DATE_PATTERN));
                headMap.put("applyName", refundApplyDTO.getApplyName() == null ? "" : refundApplyDTO.getApplyName());
                headMap.put("refundReasonDes", refundApplyDTO.getRefundReasonDes() == null ? " " : refundApplyDTO.getRefundReasonDes());
                headMap.put("nonReturnOriginalWayDes", refundApplyDTO.getNonReturnOriginalWayDes() == null ? " " : refundApplyDTO.getNonReturnOriginalWayDes());
                headMap.put("payeeBankName", refundApplyDTO.getPayeeBankName() == null ? " " : refundApplyDTO.getPayeeBankName());
                headMap.put("payeeBankAccount", refundApplyDTO.getPayeeBankAccount() == null ? " " : refundApplyDTO.getPayeeBankAccount());
                headMap.put("payeeCnaps", refundApplyDTO.getPayeeCnaps() == null ? " " : refundApplyDTO.getPayeeCnaps());
            }

            //附件
            CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
            attachmentQuery.setModule(CtcAttachmentModule.REFUND_APPLY_APP.code());
            attachmentQuery.setModuleId(id);
            List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(attachmentDtos)) {
                for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                    AduitAtta aduitAtta = new AduitAtta();
                    aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" : String.valueOf(attachmentDto.getAttachId()));
                    aduitAtta.setFileName(attachmentDto.getFileName() == null ? attachmentDto.getAttachName() : attachmentDto.getFileName());
                    aduitAtta.setFileSize(attachmentDto.getFileSize().toString());
                    fileList.add(aduitAtta);
                }
            }
            responseMap.setStatus("success");
            responseMap.setMsg("success");
            responseMap.setHeadMap(headMap);
            responseMap.setFileList(fileList);
        }
        return responseMap;
    }

    @Override
    public RefundApplyDTO getRefundApplyByfdId(Long id) {
        final RefundApplyDTO refundApplyDTO = this.findById(id);
        if (StringUtils.isNotEmpty(refundApplyDTO.getPaymentType())) {
            final DictDto dictByTypeAndCode = CacheDataUtils.findDictByTypeAndCode("Receipt refund", refundApplyDTO.getPaymentType());
            refundApplyDTO.setPaymentType(dictByTypeAndCode.getName());
        }
        return refundApplyDTO;
    }

    @Transactional
    @Override
    public Long updateRefundDetail(RefundApplyDetail refundApplyDetail) {
        refundApplyDetailMapper.updateByPrimaryKeySelective(refundApplyDetail);
        return 1l;
    }

    @Override
    public int updateAccountingDate(Long id, Date refundEntryDate) {
        if (id == null) {
            throw new BizException(Code.ERROR, "单据号不能为空");
        }
        if (refundEntryDate == null) {
            throw new BizException(Code.ERROR, "退款入账日期不能为空");
        }

        RefundApply updateItem = new RefundApply();
        updateItem.setId(id);
        updateItem.setRefundEntryDate(refundEntryDate);
        return refundApplyMapper.updateByPrimaryKeySelective(updateItem);
    }

    @Override
    public void initInvoice(String refundCode) {
        RefundApplyDTO refundApply = findByRefundCode(refundCode);
        List<RefundApplyDetailDTO> dtos = refundApply.getRefundApplyDetailDTOList();
        int i = 1;
        for (RefundApplyDetailDTO dto : dtos) {
            //发票头
            InvoiceReceivable invoice = new InvoiceReceivable();
            invoice.setApplyHeaderId(0L);
            invoice.setApplyDetailId(0L);
            invoice.setContractId(dto.getContractId());
            invoice.setContractCode(dto.getContractCode());
            invoice.setCustomerId(refundApply.getCustomerId());
            invoice.setOuId(refundApply.getOuId());
            invoice.setInvoiceCode(refundApply.getRefundApplyCode() + "-" + i);
            invoice.setCurrencyCode(dto.getCurrency());
            invoice.setLimitPrice(new BigDecimal(999999));
            invoice.setTaxIncludedPrice(dto.getRefundAmount());
            invoice.setExclusiveOfTax(dto.getRefundAmount());
            // 默认税率为0，税码为"VAT0_OUT"
            invoice.setTaxRace(BigDecimal.ZERO);
            invoice.setTaxCode("VAT0_OUT");
            invoice.setExchangeRateType(refundApply.getExchangeRateType());
            invoice.setExchangeRateDate(refundApply.getExchangeRateDate());
            invoice.setStatus("审批通过");
            invoice.setExternalErpStatus(CommonErpStatus.PUSHED.code().toString());
            invoice.setErpStatus(InvoiceReceivableErpStatus.SUCCESS.getCode());
            invoice.setDeletedFlag(DeletedFlagEnum.VALID.code());
            invoice.setCreateAt(new Date());
            invoice.setCreateBy(refundApply.getCreateBy());
            // 客户退款应收发票类型
            String dictName = "客户退款应收发票类型";
            Set<String> valueSet = organizationCustomDictService.queryByName(dictName, refundApply.getOuId(), OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
            if (!org.springframework.util.CollectionUtils.isEmpty(valueSet)) {
                String next = valueSet.iterator().next();
                invoice.setInvoiceType(next);
            }
            invoice.setGlDate(refundApply.getRefundEntryDate());
            invoice.setCustomerName(refundApply.getCustomerName());
            invoice.setCustomerCode(refundApply.getCustomerCode());
            invoice.setSendToKpy(0);//不同步到开票易
            invoice.setConversionRate(BigDecimal.ONE);
            invoice.setSyncErpTime(new Date());
            invoice.setSource(InvoiceReceivableSource.REFUND_APPLY.getCode());
            invoice.setDueDateStatus(3);
            invoice.setRefundApplyDetailId(dto.getId());
            invoiceReceivableMapper.insertSelective(invoice);

            //发票行(开票明细)
            InvoiceApplyDetailSplit invoiceDetail = new InvoiceApplyDetailSplit();
            invoiceDetail.setInvoiceReceivableId(invoice.getId());
            invoiceDetail.setProduct(InvoiceReceivableSource.REFUND_APPLY.getName());
            invoiceDetail.setProductTaxName(InvoiceReceivableSource.REFUND_APPLY.getName());
            invoiceDetail.setUnit("笔");
            invoiceDetail.setQuantity(BigDecimal.ONE);
            // 默认税率为0，税码为"VAT0_OUT"
            invoiceDetail.setTaxRace(BigDecimal.ZERO);
            invoiceDetail.setTaxCode("VAT0_OUT");
            invoiceDetail.setPrice(dto.getRefundAmount());
            invoiceDetail.setExclusiveOfTax(dto.getRefundAmount());
            invoiceDetail.setTaxIncludedPrice(dto.getRefundAmount());
            invoiceDetail.setRemark(InvoiceReceivableSource.REFUND_APPLY.getName());
            invoiceDetail.setCreateBy(refundApply.getCreateBy());
            invoiceDetail.setDeletedFlag(DeletedFlag.VALID.code());
            invoiceApplyDetailSplitMapper.insertSelective(invoiceDetail);
            i++;
        }
    }

    /**
     * 根据会计期间来确定退款入账日期
     *
     * @param operatingUnit
     * @param glperiodDate
     * @return
     */
    @Override
    public RefundGlperiodInfo getandCheckGlperiodInfo(String operatingUnit, String glperiodDate) {
        RefundGlperiodInfo refundGlperiodInfo = new RefundGlperiodInfo();
        List<GlPeriodDto> finalGlPeriodDtoList = null;
        Long periodYear = null;
        if (StringUtils.isNotEmpty(glperiodDate)) {
            periodYear = Long.valueOf(glperiodDate.substring(0, 4));
        } else {
            final LocalDate localDate = LocalDate.now();
            final int year = localDate.getYear();
            periodYear = Long.valueOf(year);
        }
        OrganizationRelQuery organizationRelQuery = new OrganizationRelQuery();
        organizationRelQuery.setOperatingUnitId(Long.valueOf(operatingUnit));
        final List<OrganizationRelDto> organizationRels = basedataExtService.getOrganizationRel(organizationRelQuery);
        if (CollectionUtils.isNotEmpty(organizationRels)) {
            final OrganizationRelDto organizationRelDto = organizationRels.get(0);
            if (!StringUtils.isNotEmpty(glperiodDate)) {
                final List<GlPeriodDto> glPeriodDtoList = basedataExtService.getGlPeriod1(organizationRelDto.getLedgerId(), GlPeriodType.RECEIVABLES_PERIOD.getCode().toString(), null, periodYear, null);
                finalGlPeriodDtoList = glPeriodDtoList.stream().filter(glPeriodDto -> (glPeriodDto.getShowStatus().equals("Open") || glPeriodDto.getShowStatus().equals("Opened"))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(finalGlPeriodDtoList)) {
                    refundGlperiodInfo.setCode("0");
                    refundGlperiodInfo.setMsg("数据获取正常！");
                    refundGlperiodInfo.setGlPeriodDtoList(finalGlPeriodDtoList);
                } else {
                    refundGlperiodInfo.setCode("1");
                    refundGlperiodInfo.setMsg("没有找到对应的会计期间数据！");
                    refundGlperiodInfo.setGlPeriodDtoList(finalGlPeriodDtoList);
                }
            } else {
                final List<GlPeriodDto> glPeriodDtoList = basedataExtService.getGlPeriod1(organizationRelDto.getLedgerId(), GlPeriodType.RECEIVABLES_PERIOD.getCode().toString(), null, periodYear, glperiodDate);
                finalGlPeriodDtoList = glPeriodDtoList.stream().filter(glPeriodDto -> (glPeriodDto.getShowStatus().equals("Open") || glPeriodDto.getShowStatus().equals("Opened"))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(finalGlPeriodDtoList)) {
                    refundGlperiodInfo.setCode("0");
                    refundGlperiodInfo.setMsg("数据获取正常！");
                    refundGlperiodInfo.setGlPeriodDtoList(finalGlPeriodDtoList);
                } else {
                    refundGlperiodInfo.setCode("1");
                    refundGlperiodInfo.setMsg("没有找到对应的会计期间数据！");
//                    throw new MipException("退款入账日期对应的应收会计期间未打开，请重新填写!");
                }
            }
        }
        return refundGlperiodInfo;
    }

    /**
     * 组装查询条件
     *
     * @param refundApplyDTO
     * @param isReturn
     * @param param
     * @return
     */
    private boolean buildListContractParam(RefundApplyDTO refundApplyDTO, boolean isReturn, Map<String, Object> param) {

        boolean flag = true;

        if (StringUtils.isNotEmpty(refundApplyDTO.getRefundApplyCode())) {
            param.put("refundApplyCode", refundApplyDTO.getRefundApplyCode());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getRefundApplyStatusStr())) {
            List<Integer> refundApplyStatusList = Arrays.stream(refundApplyDTO.getRefundApplyStatusStr().split(","))
                    .map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            param.put("refundApplyStatusList", refundApplyStatusList);
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getRefundEntryStartDate())) {
            param.put("refundEntryStartDate", refundApplyDTO.getRefundEntryStartDate());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getRefundEntryEndDate())) {
            param.put("refundEntryEndDate", refundApplyDTO.getRefundEntryEndDate());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getCustomerCode())) {
            param.put("customerCode", refundApplyDTO.getCustomerCode());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getCustomerName())) {
            param.put("customerName", refundApplyDTO.getCustomerName());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getPayeeBankName())) {
            param.put("payeeBankName", refundApplyDTO.getPayeeBankName());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getPayeeBankAccount())) {
            param.put("payeeBankAccount", refundApplyDTO.getPayeeBankAccount());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getPaymentTypeStr())) {
            List<Integer> paymentTypeStatusList = Arrays.stream(refundApplyDTO.getPaymentTypeStr().split(","))
                    .map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            param.put("paymentTypeStatusList", paymentTypeStatusList);
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getContractCode())) {
            param.put("contractCode", refundApplyDTO.getContractCode());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getContractName())) {
            param.put("contractName", refundApplyDTO.getContractName());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getApplyName())) {
            param.put("applyName", refundApplyDTO.getApplyName());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getApplyStartDate())) {
            param.put("applyStartDate", refundApplyDTO.getApplyStartDate());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getApplyEndDate())) {
            param.put("applyEndDate", refundApplyDTO.getApplyEndDate());
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getOuterStatusStr())) {
            List<Integer> outerStatusStatusList = Arrays.stream(refundApplyDTO.getOuterStatusStr().split(","))
                    .map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            param.put("outerStatusStatusList", outerStatusStatusList);
        }

        if (StringUtils.isNotEmpty(refundApplyDTO.getOuIdStr())) {
            List<Integer> ouIdList = Arrays.stream(refundApplyDTO.getOuIdStr().split(","))
                    .map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            param.put("ouIdList", ouIdList);
        }
        List<Long> currentOuIdList = SystemContext.getOus();
        if (ListUtils.isEmpty(currentOuIdList)) {
            currentOuIdList.add(-1L);
        }
        // 有权限OU
        param.put("currentOuIdList", currentOuIdList);
        return flag;
    }

}