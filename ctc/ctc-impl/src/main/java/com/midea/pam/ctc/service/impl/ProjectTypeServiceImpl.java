package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.FeeItemDto;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.ctc.dto.CheckContractDto;
import com.midea.pam.common.ctc.dto.FeeItemExpenseTypeDto;
import com.midea.pam.common.ctc.dto.OtherFeesDto;
import com.midea.pam.common.ctc.dto.ProjectTypeCheckRelDto;
import com.midea.pam.common.ctc.dto.ProjectTypeDto;
import com.midea.pam.common.ctc.entity.CodeRule;
import com.midea.pam.common.ctc.entity.CodeRuleExample;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.ContractExample;
import com.midea.pam.common.ctc.entity.MilepostTemplate;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ProjectTypeCheckRel;
import com.midea.pam.common.ctc.entity.ProjectTypeCheckRelExample;
import com.midea.pam.common.ctc.entity.ProjectTypeExample;
import com.midea.pam.common.ctc.entity.ProjectTypeRemind;
import com.midea.pam.common.ctc.entity.ProjectTypeRemindExample;
import com.midea.pam.common.ctc.vo.ProjectTypeVO;
import com.midea.pam.common.enums.BudgetConfigEnums;
import com.midea.pam.common.enums.CostMethod;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.common.util.Utils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.mapper.CheckItemMapper;
import com.midea.pam.ctc.mapper.ContractMapper;
import com.midea.pam.ctc.mapper.FeeItemExtMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectTypeCheckRelExtMapper;
import com.midea.pam.ctc.mapper.ProjectTypeCheckRelMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.ProjectTypeRemindMapper;
import com.midea.pam.ctc.service.CodeRuleService;
import com.midea.pam.ctc.service.MilepostTemplateService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.system.SystemContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class ProjectTypeServiceImpl implements ProjectTypeService {

    @Resource
    private ContractMapper contractMapper;
    @Resource
    private CheckItemMapper checkItemMapper;
    @Resource
    private ProjectTypeMapper projectTypeMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectTypeCheckRelMapper projectTypeCheckRelMapper;
    @Resource
    private ProjectTypeCheckRelExtMapper projectTypeCheckRelExtMapper;
    @Resource
    private MilepostTemplateService milepostTemplateService;
    @Resource
    private CodeRuleService codeRuleService;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private FeeItemExtMapper feeItemExtMapper;
    @Resource
    private ProjectTypeRemindMapper projectTypeRemindMapper;


    @Override
    public long countByExample(ProjectTypeExample example) {
        return projectTypeMapper.countByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return projectTypeMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(ProjectType record) {
        return projectTypeMapper.insert(record);
    }

    @Override
    public int insertSelective(ProjectType record) {
        return projectTypeMapper.insertSelective(record);
    }

    @Override
    public List<ProjectType> selectByExample(ProjectTypeExample example) {
        return projectTypeMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public ProjectType selectByPrimaryKey(Long id) {
        return projectTypeMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjectType record) {
        return projectTypeMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjectType record) {
        return projectTypeMapper.updateByPrimaryKeyWithBLOBs(record);
    }

    /**
     * 新增或者修改项目类型.
     *
     * @param record 项目类型
     * @return 项目类型id
     */
    @Override
    public Long persistence(final ProjectType record) {
        if (record.getTransferProject() != null && record.getTransferProject() && "3".equals(record.getCodeRuleConfig())) {
            throw new MipException("是否转质保项目=是，并且编码规则=质保期内项目专用，不允许保存");
        }
        Long projectTypeId = record.getId();
        if (null != projectTypeId) {
            Assert.isTrue(null != selectByPrimaryKey(projectTypeId), "项目类型不存在");

            ProjectTypeExample example = new ProjectTypeExample();
            example.createCriteria().andTransferProjectTypeIdEqualTo(projectTypeId).andUnitIdEqualTo(SystemContext.getUnitId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            long count = projectTypeMapper.countByExample(example);
            if (count > 0 && !"3".equals(record.getCodeRuleConfig())) {
                throw new MipException("该项目类型已被其它项目类型引用为转质保项目，不能修改其编码规则");
            }
            updateByPrimaryKeySelective(record);
        } else {
            record.setDeletedFlag(record.getDeletedFlag() != null && record.getDeletedFlag());
            insertSelective(record);
        }
        return projectTypeId;
    }

    @Override
    public PageInfo<ProjectTypeDto> selectByPage(final String name, final String code, final Boolean deletedFlag,
                                                 final String isAll, final Integer pageNum, final Integer pageSize, Boolean manualFlag, Long unitId) {

        List<String> projectTypeName = new ArrayList<>();
        final OrgCustomDictOrgFrom orgCustomDictOrgFrom = OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom("company");
        Long ordId = SystemContext.getUnitId();
        List<OrganizationCustomDict> organizationCustomDicts = organizationCustomDictService.queryByOrdId(ordId, "项目类型", orgCustomDictOrgFrom);
        if (organizationCustomDicts.size() > 0) {
            projectTypeName = Arrays.asList(organizationCustomDicts.get(0).getValue().split(","));
        }

        PageHelper.startPage(pageNum, pageSize);
        final ProjectTypeExample condition = new ProjectTypeExample();
        final ProjectTypeExample.Criteria criteria = condition.createCriteria();

        if (manualFlag != null && manualFlag) {
            criteria.andValidateProjectManualEqualTo(true);
        }

        if (StringUtils.isNotEmpty(name)) {
            criteria.andNameLike(Utils.buildLike(name));
        }
        if (StringUtils.isNotEmpty(code)) {
            criteria.andCodeLike(Utils.buildLike(code));
        }
        if (deletedFlag != null) {
            criteria.andDeletedFlagEqualTo(deletedFlag);
        }

        // 不查询所有，则使用默认数据权限
        if (!Objects.equals(isAll, "Y") && null == unitId) {
            // 数据权限控制，只能查找到对应使用单位下的数据
            unitId = SystemContext.getUnitId();
            criteria.andUnitIdEqualTo(unitId);
        } else if (null != unitId) {
            criteria.andUnitIdEqualTo(unitId);
        }

        condition.setOrderByClause("create_at asc");
        final List<ProjectType> dataList = selectByExample(condition);
        PageInfo<ProjectTypeDto> pageInfo = BeanConverter.convertPage(dataList, ProjectTypeDto.class);
        if (CollectionUtils.isEmpty(pageInfo.getList())) return pageInfo;
        for (ProjectTypeDto dto : pageInfo.getList()) {
            final Unit unit = CacheDataUtils.findUnitById(dto.getUnitId());
            dto.setUnitName(unit == null ? null : unit.getUnitName());
            dto.setCostMethodName(CostMethod.getValue(dto.getCostMethod()));
            final MilepostTemplate milepostTemplate = milepostTemplateService.selectByPrimaryKey(dto.getMilepostTemplateId());
            dto.setMilepostTemplateName(null != milepostTemplate ? milepostTemplate.getName() : null);
            DictDto dict = CacheDataUtils.findDictByTypeAndCode(DictType.INCOME_POINT.code(), dto.getIncomePoint());
            dto.setIncomePointName(dict == null ? null : dict.getName());
            dto.setCodeRule(this.getCodeRule(dto.getId()));

            if (!projectTypeName.contains(dto.getName())) {
                dto.setPreviewFlag(true);
            }
        }
        return pageInfo;
    }

    @Override
    public PageInfo<ProjectTypeDto> selectList(final String name, final String code, final Boolean deletedFlag,
                                               final String isAll, final Integer pageNum, final Integer pageSize,
                                               Boolean manualFlag, Long unitId) {
        PageHelper.startPage(pageNum, pageSize);
        final ProjectTypeExample condition = new ProjectTypeExample();
        final ProjectTypeExample.Criteria criteria = condition.createCriteria();

        if (manualFlag != null && manualFlag) {
            criteria.andValidateProjectManualEqualTo(true);
        }
        if (StringUtils.isNotEmpty(name)) {
            criteria.andNameLike(Utils.buildLike(name));
        }
        if (StringUtils.isNotEmpty(code)) {
            criteria.andCodeLike(Utils.buildLike(code));
        }
        if (deletedFlag != null) {
            criteria.andDeletedFlagEqualTo(deletedFlag);
        }

        // 不查询所有，则使用默认数据权限
        if (!Objects.equals(isAll, "Y") && null == unitId) {
            // 数据权限控制，只能查找到对应使用单位下的数据
            unitId = SystemContext.getUnitId();
            criteria.andUnitIdEqualTo(unitId);
        } else if (null != unitId) {
            criteria.andUnitIdEqualTo(unitId);
        }
        condition.setOrderByClause("create_at asc");
        final List<ProjectType> dataList = projectTypeMapper.selectByExample(condition);
        PageInfo<ProjectTypeDto> pageInfo = BeanConverter.convertPage(dataList, ProjectTypeDto.class);
        return pageInfo;
    }

    /**
     * 项目类型保存
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional
    public Long save(ProjectTypeDto dto) {
        ProjectType projectType = new ProjectType();
        BeanUtils.copyProperties(dto, projectType);
        //项目类型保存
        Long id = this.persistence(projectType);
        //项目编码规则保存
        dto.setId(id);
        this.saveCodeRule(dto);
        return id;
    }

    @Override
    public List<ProjectTypeCheckRelDto> selectProjectTypeCheckRelDtoById(Long id) {
        return id == null ? null : projectTypeCheckRelExtMapper.selectList(id);
    }

    @Override
    public ProjectTypeDto selectProjectTypeByContractId(Long contractId) {
        Contract contract = contractMapper.selectByPrimaryKey(contractId);
        if (null != contract) {
            if (null != contract.getBusinessTypeId()) {
                ProjectType projectType = projectTypeMapper.selectByPrimaryKey(contract.getBusinessTypeId());
                ProjectTypeDto data = BeanConverter.copy(projectType, ProjectTypeDto.class);
                return data;
            }
        }
        return null;
    }

    @Override
    public int addOrDelProjectTypeCheckRel(Long projectTypeId, String addCheckItemIds, String delCheckItemIds) {
        if (projectTypeId == null) {
            throw new BizException(ErrorCode.CTC_PROJECT_TYPE_NOT_NULL);
        }

        if (StringUtils.isNotEmpty(addCheckItemIds)) {
            String[] args = addCheckItemIds.split(",");
            if (args.length != 0) {
                for (int i = 0; i < args.length; i++) {
                    String idStr = args[i];
                    if (StringUtils.isNotEmpty(idStr)) {
                        Long id = Long.parseLong(idStr);
                        ProjectTypeCheckRelExample example = new ProjectTypeCheckRelExample();
                        ProjectTypeCheckRelExample.Criteria criteria = example.createCriteria();
                        criteria.andProjectTypeIdEqualTo(projectTypeId);
                        criteria.andCheckItemIdEqualTo(id);
                        List<ProjectTypeCheckRel> list = projectTypeCheckRelMapper.selectByExample(example);
                        if (list != null && list.size() != 0) {
                            continue;
                        }
                        ProjectTypeCheckRel record = new ProjectTypeCheckRel();
                        record.setProjectTypeId(projectTypeId);
                        record.setCheckItemId(id);
                        record.setYesOrNo(true);
                        record.setVersion(0L);
                        record.setCreateAt(new Date());
                        record.setCreateBy(SystemContext.getUserId());
                        record.setUpdateAt(new Date());
                        record.setUpdateBy(SystemContext.getUserId());
                        projectTypeCheckRelMapper.insert(record);
                    }
                }
            }

        }
        if (StringUtils.isNotEmpty(delCheckItemIds)) {
            if (StringUtils.isNotEmpty(delCheckItemIds)) {
                String[] args = delCheckItemIds.split(",");
                if (args.length != 0) {
                    List<Long> ids = new ArrayList<Long>();
                    for (int i = 0; i < args.length; i++) {
                        String idStr = args[i];
                        if (StringUtils.isNotEmpty(idStr)) {
                            ids.add(Long.parseLong(idStr));
                        }
                    }
                    if (ids.size() != 0) {
                        ProjectTypeCheckRelExample example = new ProjectTypeCheckRelExample();
                        ProjectTypeCheckRelExample.Criteria criteria1 = example.createCriteria();
                        criteria1.andCheckItemIdIn(ids);
                        criteria1.andProjectTypeIdEqualTo(projectTypeId);
                        List<ProjectTypeCheckRel> list = projectTypeCheckRelMapper.selectByExample(example);
                        if (list != null && list.size() != 0) {
                            for (ProjectTypeCheckRel projectTypeCheckRel : list) {
                                projectTypeCheckRelMapper.deleteByPrimaryKey(projectTypeCheckRel.getId());
                            }
                        }
                    }
                }
            }
        }
        return 0;
    }

    @Override
    public int delProjectTypeCheckRelById(Long relId) {
        return projectTypeCheckRelMapper.deleteByPrimaryKey(relId);
    }

    @Override
    public int isStrictlyControl(Long relId, boolean yesOrNo) {
        ProjectTypeCheckRel projectTypeCheckRel = projectTypeCheckRelMapper.selectByPrimaryKey(relId);
        if (projectTypeCheckRel != null) {
            projectTypeCheckRel.setYesOrNo(yesOrNo);
            projectTypeCheckRel.setUpdateBy(SystemContext.getUserId());
            projectTypeCheckRel.setUpdateAt(new Date());
            projectTypeCheckRelMapper.updateByPrimaryKey(projectTypeCheckRel);
        }
        return 1;
    }

    @Override
    public Long checkProjectTypeByContractId(CheckContractDto dto) {
        ContractExample example = new ContractExample();
        example.createCriteria().andIdIn(dto.getContractIds());
        List<Contract> list = contractMapper.selectByExample(example);
        Long businessTypeId = null;
        //校验是否有多个不同的项目类型
        for (Contract c : list) {
            if (null == businessTypeId) {
                businessTypeId = c.getBusinessTypeId();
            } else {
                Asserts.equals(businessTypeId, c.getBusinessTypeId(), ErrorCode.CTC_PROJECT_TYPE_NOT_EQUALS);
            }

        }
        return null;
    }

    @Override
    public ProjectTypeVO queryById(ProjectType projectType) {
        ProjectType projectTypeSelect = projectTypeMapper.selectByPrimaryKey(projectType.getId());
        ProjectTypeVO projectTypeVO = new ProjectTypeVO();
        if (Boolean.TRUE.equals(projectTypeSelect.getWbsEnabled())) {
            BeanUtils.copyProperties(projectTypeSelect, projectTypeVO);
            return projectTypeVO;
        } else {
            projectTypeVO.setWbsEnabled(false);
            return projectTypeVO;
        }
    }

    /**
     * 校验当前单位是否存在wbs类型的项目类型
     *
     * @return
     */
    @Override
    public boolean checkWbsProjectType() {
        ProjectTypeExample example = new ProjectTypeExample();
        example.createCriteria().andDeletedFlagEqualTo(false)
                .andUnitIdEqualTo(SystemContext.getUnitId())
                .andWbsEnabledEqualTo(true);
        return projectTypeMapper.countByExample(example) > 0;
    }

    /**
     * 项目编码规则
     *
     * @param projectTypeId
     * @return
     */
    private CodeRule getCodeRule(Long projectTypeId) {
        CodeRuleExample example = new CodeRuleExample();
        example.createCriteria().andProjectTypeIdEqualTo(projectTypeId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<CodeRule> codeRuleList = codeRuleService.selectByExample(example);
        if (CollectionUtils.isNotEmpty(codeRuleList)) {
            return codeRuleList.get(0);
        }
        return null;
    }

    /**
     * 编码规则保存
     *
     * @param dto
     */
    private void saveCodeRule(ProjectTypeDto dto) {
        // codeRuleConfig 等价于 ruleType（1按项目类型、2按应用行业、3质保期内项目专用、4手工填写项目号），1、3类型的需要同步修改对应项目编码
        if (PublicUtil.isNotEmpty(dto.getCodeRule()) && ("1".equals(dto.getCodeRuleConfig()) || "3".equals(dto.getCodeRuleConfig()))) {
            CodeRule codeRule = dto.getCodeRule();
            // 限制编码规则类型不能为空
            Asserts.notNull(codeRule.getRuleType(), ErrorCode.CTC_PROJECT_CODE_RULE_TYPE_NOT_NULL);
            codeRule.setProjectTypeId(dto.getId());
            codeRule.setUnitId(dto.getUnitId());
            codeRule.setName(dto.getName());
            codeRuleService.persistence(codeRule);
        }
    }

    /**
     * 获取费用类型设置
     *
     * @param projectTypeId
     * @return
     */
    @Override
    public List<FeeItemDto> selectExpenseTypeSet(Long projectTypeId) {
        ProjectType projectType = projectTypeMapper.selectByPrimaryKey(projectTypeId);
        if (projectType != null && StringUtils.isNotBlank(projectType.getBudgetConfig())) {
            JSONArray otherFees = JSON.parseObject(projectType.getBudgetConfig()).getJSONArray(BudgetConfigEnums.OTHER_FEES.getCode());
            JSONArray array = JSON.parseObject(otherFees.get(1).toString()).getJSONArray(BudgetConfigEnums.VALUE.getCode());
            return array.toJavaList(FeeItemDto.class);
        }
        return new ArrayList<>();
    }

    /**
     * 获取费用类型经济事项
     *
     * @param projectTypeId
     * @return
     */
    @Override
    public List<FeeItemExpenseTypeDto> selectFeeItemExpenseType(Long projectTypeId) {
        // 获取项目类型
        ProjectType projectType = projectTypeMapper.selectByPrimaryKey(projectTypeId);
        List<OtherFeesDto> otherFeeConfig = new ArrayList<>();
        if (StringUtils.isNotBlank(projectType.getBudgetConfig())) {
            JSONArray otherFees = JSON.parseObject(projectType.getBudgetConfig()).getJSONArray(BudgetConfigEnums.OTHER_FEES.getCode());
            JSONArray array = JSON.parseObject(otherFees.get(1).toString()).getJSONArray(BudgetConfigEnums.VALUE.getCode());
            for (int i = 0; i < array.size(); i++) {
                OtherFeesDto otherFeesDto = JSON.parseObject(JSON.toJSONString(array.get(i)), OtherFeesDto.class);
                otherFeeConfig.add(otherFeesDto);
            }
        }

        // 获取费用类型经济事项
        List<FeeItemExpenseTypeDto> feeItemExpenseTypeList = feeItemExtMapper.selectFeeItemExpenseType(SystemContext.getUnitId());
        if (CollectionUtils.isNotEmpty(feeItemExpenseTypeList)) {
            for (FeeItemExpenseTypeDto feeItemExpenseTypeDto : feeItemExpenseTypeList) {
                // 匹配 fee_Type_id && fee_setting_mode
                List<OtherFeesDto> filterOtherFees = otherFeeConfig.stream().filter(a -> Objects.equals(a.getFeeTypeId(), feeItemExpenseTypeDto.getFeeTypeId()) && Objects.equals(a.getFeeSettingMode(), feeItemExpenseTypeDto.getFeeSettingMode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterOtherFees)) {
                    // 项目类型能匹配到，取项目类型-预算设置的配置
                    feeItemExpenseTypeDto.setSyncEMS(Objects.equals("true", filterOtherFees.get(0).getSyncEMS()));
                } else {
                    // 项目类型匹配不到，默认true
                    feeItemExpenseTypeDto.setSyncEMS(true);
                }
            }
        }
        return feeItemExpenseTypeList;
    }

    @Override
    public List<ProjectType> getByIds(List<Long> projectTypeIds) {
        if (ListUtils.isEmpty(projectTypeIds)) {
            return Collections.emptyList();
        }
        ProjectTypeExample example = new ProjectTypeExample();
        example.createCriteria().andIdIn(projectTypeIds);
        return projectTypeMapper.selectByExample(example);
    }

    @Override
    public List<ProjectType> queryByTransferType() {
        CodeRuleExample ruleExample = new CodeRuleExample();
        ruleExample.createCriteria().andUnitIdEqualTo(SystemContext.getUnitId()).andRuleTypeEqualTo(3).andDeletedFlagEqualTo(Boolean.FALSE);
        List<Long> projectTypeIdList = codeRuleService.selectByExample(ruleExample).stream().map(CodeRule::getProjectTypeId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(projectTypeIdList)) {
            return new ArrayList<>();
        }
        ProjectTypeExample example = new ProjectTypeExample();
        example.createCriteria().andIdIn(projectTypeIdList);
        return projectTypeMapper.selectByExample(example);
    }

    @Override
    public ProjectTypeRemind queryRemindConfig(Long projectTypeId) {
        ProjectTypeRemindExample example = new ProjectTypeRemindExample();
        example.createCriteria().andProjectTypeIdEqualTo(projectTypeId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<ProjectTypeRemind> projectTypeRemindList = projectTypeRemindMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(projectTypeRemindList)) {
            return projectTypeRemindList.get(0);
        }
        return null;
    }

    @Override
    public Long saveRemindConfig(ProjectTypeRemind projectTypeRemind) {
        Long projectTypeId = projectTypeRemind.getProjectTypeId();
        Asserts.notEmpty(projectTypeId, ErrorCode.CTC_PROJECT_TYPE_ID_NOT_NULL);
        ProjectTypeRemind projectTypeRemindOrigin = queryRemindConfig(projectTypeId);
        if (projectTypeRemindOrigin == null) {
            projectTypeRemind.setId(null);
            projectTypeRemind.setDeletedFlag(DeletedFlag.VALID.code());
            projectTypeRemindMapper.insert(projectTypeRemind);
        } else {
            projectTypeRemind.setId(projectTypeRemindOrigin.getId());
            projectTypeRemindMapper.updateByPrimaryKeySelective(projectTypeRemind);
        }
        return projectTypeRemind.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updeteBudgetConfig() {
        ProjectTypeExample projectTypeExample = new ProjectTypeExample();
        ProjectTypeExample.Criteria criteria = projectTypeExample.createCriteria();
        criteria.andDeletedFlagEqualTo(false);
        List<ProjectType> projectTypes = projectTypeMapper.selectByExampleWithBLOBs(projectTypeExample);
        if (CollectionUtils.isNotEmpty(projectTypes)) {
            for (ProjectType projectType : projectTypes) {
                String budgetConfig = projectType.getBudgetConfig();
                if (StringUtils.isNotBlank(budgetConfig)) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        ObjectNode rootNode = (ObjectNode) objectMapper.readTree(budgetConfig);

                        ObjectNode budgetPreparationNode = objectMapper.createObjectNode();
                        budgetPreparationNode.put("label", "预算编写");
                        budgetPreparationNode.put("code", "budgetPreparationFlag");
                        budgetPreparationNode.put("value", 1);
                        budgetPreparationNode.put("msg", "启用");

                        ArrayNode budgetPreparationArray = objectMapper.createArrayNode();
                        budgetPreparationArray.add(budgetPreparationNode);

                        rootNode.set("budgetPreparation", budgetPreparationArray);

                        String updatedBudgetConfig = objectMapper.writeValueAsString(rootNode);
                        projectType.setBudgetConfig(updatedBudgetConfig);
                        projectTypeMapper.updateByPrimaryKeyWithBLOBs(projectType);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.info("初始化预算设置失败", e);
                        return "初始化预算设置失败";
                    }
                }
            }
        }
        return "SUCCESS";
    }
}
