package com.midea.pam.ctc.service.config;


import com.midea.gpam.server.gpamresultreturnservice.v1.GpamHelperService;
import com.midea.gpam.server.gpamresultreturnservice.v1.GpamHelperServiceImpl;
import com.midea.pam.cache.RedisValueCache;
import com.midea.pam.cache.ValueCache;
import com.midea.pam.common.util.ApplicationContextProvider;
import com.midea.pam.common.util.MethodInvoker;
import com.midea.pam.ctc.api.server.PassGcebToPamService;
import com.midea.pam.ctc.api.service.helper.GcebToPamHelper;
import com.midea.pam.ctc.api.service.impl.PassGcebToPamServiceImpl;
import com.midea.pam.ctc.carrybill.service.CarryoverBillOutsourcingCostCollectionService;
import com.midea.pam.ctc.carrybill.service.CostRatioConfigDetailService;
import com.midea.pam.ctc.carrybill.service.CostRatioConfigService;
import com.midea.pam.ctc.carrybill.service.MaterialOutsourcingContractConfigService;
import com.midea.pam.ctc.carrybill.service.impl.CarryoverBillOutsourcingCostCollectionServiceImpl;
import com.midea.pam.ctc.carrybill.service.impl.CostRatioConfigDetailServiceImpl;
import com.midea.pam.ctc.carrybill.service.impl.CostRatioConfigServiceImpl;
import com.midea.pam.ctc.carrybill.service.impl.MaterialOutsourcingContractConfigServiceImpl;
import com.midea.pam.ctc.common.config.GlegalContractProperties;
import com.midea.pam.ctc.common.config.OssServiceProperties;
import com.midea.pam.ctc.common.redis.RedisLuaScriptServer;
import com.midea.pam.ctc.common.redis.RedisUtilServer;
import com.midea.pam.ctc.common.utils.CarrierHelper;
import com.midea.pam.ctc.contract.service.ContractBaseInfoChanageCallBackService;
import com.midea.pam.ctc.contract.service.ContractChangewayService;
import com.midea.pam.ctc.contract.service.ContractCheckService;
import com.midea.pam.ctc.contract.service.ContractDrafterChangeCallBackService;
import com.midea.pam.ctc.contract.service.ContractHisService;
import com.midea.pam.ctc.contract.service.ContractProductCostService;
import com.midea.pam.ctc.contract.service.ContractReceiptPlanChanageCallBackService;
import com.midea.pam.ctc.contract.service.ContractService;
import com.midea.pam.ctc.contract.service.ContractTerminationCallBackService;
import com.midea.pam.ctc.contract.service.ContractWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.CustomerTransferReverseWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.CustomerTransferWorkflowCallBackService;
import com.midea.pam.ctc.contract.service.GSCInvoiceDetailWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.GSCInvoiceIspWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.GlegalContractWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.GlegalPurchaseContractWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.InvoicePlanService;
import com.midea.pam.ctc.contract.service.InvoiceReceivableReverseCallBackService;
import com.midea.pam.ctc.contract.service.LegalContractHistoryService;
import com.midea.pam.ctc.contract.service.LegalContractService;
import com.midea.pam.ctc.contract.service.MilepostDesignPlanChangeWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.contract.service.PaymentApplyWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.PaymentPlanService;
import com.midea.pam.ctc.contract.service.ProjectContractRsChangeHistoryService;
import com.midea.pam.ctc.contract.service.PurchaseContractBudgetMaterialService;
import com.midea.pam.ctc.contract.service.PurchaseContractBudgetService;
import com.midea.pam.ctc.contract.service.PurchaseContractDetailService;
import com.midea.pam.ctc.contract.service.PurchaseContractProgressWorkflowCallBackService;
import com.midea.pam.ctc.contract.service.PurchaseContractPunishmentConfigService;
import com.midea.pam.ctc.contract.service.PurchaseContractPunishmentDetailService;
import com.midea.pam.ctc.contract.service.PurchaseContractPunishmentService;
import com.midea.pam.ctc.contract.service.PurchaseContractPunishmentWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.contract.service.PurchaseContractWorkflowCallbackService;
import com.midea.pam.ctc.contract.service.PurchaseOrderCallBackService;
import com.midea.pam.ctc.contract.service.PurchaseOrderChangeCallBackService;
import com.midea.pam.ctc.contract.service.PurchaseOrderChangeService;
import com.midea.pam.ctc.contract.service.ReceiptInvoiceRelationService;
import com.midea.pam.ctc.contract.service.ReceiptPlanService;
import com.midea.pam.ctc.contract.service.RefundApplyService;
import com.midea.pam.ctc.contract.service.StandardTermsCallbackService;
import com.midea.pam.ctc.contract.service.helper.ContractHelper;
import com.midea.pam.ctc.contract.service.helper.ContractHisHelper;
import com.midea.pam.ctc.contract.service.helper.ContractModifyCheck;
import com.midea.pam.ctc.contract.service.impl.ContractBaseInfoChanageCallBackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ContractChangewayServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ContractCheckServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ContractDrafterChangeCallBackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ContractHisServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ContractProductChanageWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ContractProductCostServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ContractReceiptPlanChanageCallBackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ContractServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ContractTerminationCallBackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ContractWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.CustomerTransferReverseWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.CustomerTransferWorkflowCallBackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.GSCInvoiceDetailWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.GSCInvoiceIspWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.GlegalContractWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.GlegalPurchaseContractWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.InvoicePlanServiceImpl;
import com.midea.pam.ctc.contract.service.impl.InvoiceReceivableReverseCallBackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.LegalContractHistoryServiceImpl;
import com.midea.pam.ctc.contract.service.impl.LegalContractServiceImpl;
import com.midea.pam.ctc.contract.service.impl.MilepostDesignPlanChangeWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.OssService;
import com.midea.pam.ctc.contract.service.impl.OssServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PaymentApplyServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PaymentApplyWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PaymentPlanServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ProjectContractRsChangeHistoryServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseContractBudgetMaterialServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseContractBudgetServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseContractDetailServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseContractProgressWorkflowCallBackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseContractPunishmentConfigServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseContractPunishmentDetailServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseContractPunishmentServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseContractPunishmentWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseContractServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseContractWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseOrderCallBackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseOrderChangeCallBackServiceImpl;
import com.midea.pam.ctc.contract.service.impl.PurchaseOrderChangeServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ReceiptInvoiceRelationServiceImpl;
import com.midea.pam.ctc.contract.service.impl.ReceiptPlanServiceImpl;
import com.midea.pam.ctc.contract.service.impl.RefundApplyServiceImpl;
import com.midea.pam.ctc.contract.service.impl.StandardTermsCallbackServiceImpl;
import com.midea.pam.ctc.eam.service.impl.impl.EamInvokeHelperServiceImpl;
import com.midea.pam.ctc.eam.service.impl.impl.EamInvokeServiceImpl;
import com.midea.pam.ctc.eam.service.impl.impl.EamPurchaseInfoServiceImpl;
import com.midea.pam.ctc.eam.service.impl.impl.EamPurchaseInvokeLogServiceImpl;
import com.midea.pam.ctc.eam.service.service.EamInvokeHelperService;
import com.midea.pam.ctc.eam.service.service.EamInvokeService;
import com.midea.pam.ctc.eam.service.service.EamPurchaseInfoService;
import com.midea.pam.ctc.eam.service.service.EamPurchaseInvokeLogService;
import com.midea.pam.ctc.esb.service.BasicCacheService;
import com.midea.pam.ctc.esb.service.EsbResultReturnRecordService;
import com.midea.pam.ctc.esb.service.EsbResultReturnService;
import com.midea.pam.ctc.esb.service.impl.EsbResultReturnRecordServiceImpl;
import com.midea.pam.ctc.esb.service.impl.EsbResultReturnServiceImpl;
import com.midea.pam.ctc.esb.service.impl.SdpCallbackServiceImpl;
import com.midea.pam.ctc.excel.service.impl.ProjectContractRsFlexibleImportService;
import com.midea.pam.ctc.excel.service.impl.ProjectContractRsFlexibleImportServiceImpl;
import com.midea.pam.ctc.excel.service.impl.ProjectFlexibleImportService;
import com.midea.pam.ctc.excel.service.impl.ProjectFlexibleImportServiceImpl;
import com.midea.pam.ctc.excel.service.impl.ProjectMilepostFlexibleImportService;
import com.midea.pam.ctc.excel.service.impl.ProjectMilepostFlexibleImportServiceImpl;
import com.midea.pam.ctc.excel.service.impl.ProjectWbsBudgetFlexibleImportService;
import com.midea.pam.ctc.excel.service.impl.ProjectWbsBudgetFlexibleImportServiceImpl;
import com.midea.pam.ctc.ext.service.config.BasedataExtServiceImpl;
import com.midea.pam.ctc.ext.service.config.BasicCacheServiceImpl;
import com.midea.pam.ctc.ext.service.config.CrmExtServiceImpl;
import com.midea.pam.ctc.ext.service.impl.AdapterServiceImpl;
import com.midea.pam.ctc.glegal.service.ApplyContractUnitService;
import com.midea.pam.ctc.glegal.service.GlegalInterfaceService;
import com.midea.pam.ctc.glegal.service.GlegalInterfaceV2Service;
import com.midea.pam.ctc.glegal.service.SealAdministratorService;
import com.midea.pam.ctc.glegal.service.impl.ApplyContractUnitServiceImpl;
import com.midea.pam.ctc.glegal.service.impl.GlegalInterfaceServiceImpl;
import com.midea.pam.ctc.glegal.service.impl.GlegalInterfaceV2ServiceImpl;
import com.midea.pam.ctc.glegal.service.impl.SealAdministratorServiceImpl;
import com.midea.pam.ctc.project.service.MilepostNoticeService;
import com.midea.pam.ctc.project.service.ProjectAssetChangeWorkflowCallbackService;
import com.midea.pam.ctc.project.service.ProjectBudgetAssetChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetChangePushEmsService;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeService;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeSummaryHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeWorkflowCallbackService;
import com.midea.pam.ctc.project.service.ProjectBudgetFeeChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetFeeChangeSummaryHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetHumanChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetHumanChangeSummaryHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetMaterialAddRelationService;
import com.midea.pam.ctc.project.service.ProjectBudgetMaterialChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetMaterialChangeSummaryHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetTravelChangeHistoryService;
import com.midea.pam.ctc.project.service.ProjectBudgetTravelChangeSummaryHistoryService;
import com.midea.pam.ctc.project.service.ProjectContractChangeWorkflowCallbackService;
import com.midea.pam.ctc.project.service.ProjectGanttChartService;
import com.midea.pam.ctc.project.service.ProjectProfitChangeHistoryService;
import com.midea.pam.ctc.project.service.impl.MilepostNoticeServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectAssetChangeWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetAssetChangeHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetChangeEsbServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetChangeHelper;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetChangePushEmsServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetChangeServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetChangeSummaryHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetChangeSyncServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetChangeWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetFeeChangeHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetFeeChangeSummaryHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetHumanChangeHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetHumanChangeSummaryHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetMaterialAddRelationServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetMaterialChangeHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetMaterialChangeSummaryHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetTravelChangeHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectBudgetTravelChangeSummaryHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectContractChangeWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectGanttChartServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectProfitChangeHistoryServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectSyncServiceImpl;
import com.midea.pam.ctc.project.service.impl.ProjectTerminationWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.rdm.service.RdmInterfaceService;
import com.midea.pam.ctc.rdm.service.RdmResourcePlanService;
import com.midea.pam.ctc.rdm.service.RdmSettlementSheetDetailService;
import com.midea.pam.ctc.rdm.service.RdmSettlementSheetService;
import com.midea.pam.ctc.rdm.service.RdmWorkingHourService;
import com.midea.pam.ctc.rdm.service.TransitionRdmResourcePlanService;
import com.midea.pam.ctc.rdm.service.TransitionRdmWorkingHourService;
import com.midea.pam.ctc.rdm.service.impl.RdmInterfaceServiceImpl;
import com.midea.pam.ctc.rdm.service.impl.RdmResourcePlanServiceImpl;
import com.midea.pam.ctc.rdm.service.impl.RdmSettlementSheetDetailServiceImpl;
import com.midea.pam.ctc.rdm.service.impl.RdmSettlementSheetServiceImpl;
import com.midea.pam.ctc.rdm.service.impl.RdmWorkingHourServiceImpl;
import com.midea.pam.ctc.rdm.service.impl.TransitionRdmResourcePlanServiceImpl;
import com.midea.pam.ctc.rdm.service.impl.TransitionRdmWorkingHourServiceImpl;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsBudgetChangeHistoryService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsBudgetService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsRequirementChangeRecordRelationService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsRequirementChangeRecordService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsWorkflowCallBackService;
import com.midea.pam.ctc.requirement.service.impl.ProjectWbsReceiptsBudgetChangeHistoryServiceImpl;
import com.midea.pam.ctc.requirement.service.impl.ProjectWbsReceiptsBudgetServiceImpl;
import com.midea.pam.ctc.requirement.service.impl.ProjectWbsReceiptsRequirementChangeRecordRelationServiceImpl;
import com.midea.pam.ctc.requirement.service.impl.ProjectWbsReceiptsRequirementChangeRecordServiceImpl;
import com.midea.pam.ctc.requirement.service.impl.ProjectWbsReceiptsServiceImpl;
import com.midea.pam.ctc.requirement.service.impl.ProjectWbsReceiptsWorkflowCallBackServiceImpl;
import com.midea.pam.ctc.requirement.service.impl.ProjectWbsSubmitReceiptsWorkflowCallBackServiceImpl;
import com.midea.pam.ctc.rocketmq.consumer.MilepostDesignPlanChangeSubmitRocketMQConsumer;
import com.midea.pam.ctc.rocketmq.consumer.SubmitHistoryWithRecursiveDetailRocketMQConsumer;
import com.midea.pam.ctc.rocketmq.impl.CommonRocketMQProducerServiceImpl;
import com.midea.pam.ctc.rocketmq.service.CommonRocketMQProducerService;
import com.midea.pam.ctc.sdp.mq.SdpDataOutStrategySpi;
import com.midea.pam.ctc.sdp.mq.SdpLogServiceImpl;
import com.midea.pam.ctc.sdp.mq.SdpMessageConsumer;
import com.midea.pam.ctc.sdp.mq.item.ItemSdpAssdTopicDataOutStrategySpi;
import com.midea.pam.ctc.sdp.mq.item.ItemSdpGERPFaTopicDataOutStrategySpi;
import com.midea.pam.ctc.sdp.mq.item.ItemSdpGERPPoTopicDataOutStrategySpi;
import com.midea.pam.ctc.sdp.mq.item.ItemSdpQsqedTopicDataOutStrategySpi;
import com.midea.pam.ctc.sdp.mq.item.ItemSdpSrTopicDataOutStrategySpi;
import com.midea.pam.ctc.sdp.service.SdpLogService;
import com.midea.pam.ctc.service.AdapterService;
import com.midea.pam.ctc.service.AgencySynService;
import com.midea.pam.ctc.service.ApplicationIndustryService;
import com.midea.pam.ctc.service.AssetDeprnAccountingService;
import com.midea.pam.ctc.service.AsyncCostCollectionResultService;
import com.midea.pam.ctc.service.AsyncRequestResultService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.BudgetItemService;
import com.midea.pam.ctc.service.BusiSceneNonSaleService;
import com.midea.pam.ctc.service.CarryoverAccountingService;
import com.midea.pam.ctc.service.CarryoverBillAccountingService;
import com.midea.pam.ctc.service.CarryoverBillBatchManageService;
import com.midea.pam.ctc.service.CarryoverBillCostCollectionRelService;
import com.midea.pam.ctc.service.CarryoverBillExtService;
import com.midea.pam.ctc.service.CarryoverBillIncomeCollectionService;
import com.midea.pam.ctc.service.CarryoverBillProcessScheduleService;
import com.midea.pam.ctc.service.CarryoverBillService;
import com.midea.pam.ctc.service.CarryoverBillWorkingHourRelService;
import com.midea.pam.ctc.service.CheckBasicInfoService;
import com.midea.pam.ctc.service.CheckItemService;
import com.midea.pam.ctc.service.CheckResultService;
import com.midea.pam.ctc.service.CheckSceneBasicRelService;
import com.midea.pam.ctc.service.CheckSceneInfoService;
import com.midea.pam.ctc.service.CloudPrintExtService;
import com.midea.pam.ctc.service.CnapsInfoService;
import com.midea.pam.ctc.service.CodeRuleService;
import com.midea.pam.ctc.service.CollectionConfigurationSerivce;
import com.midea.pam.ctc.service.ContractClassificationService;
import com.midea.pam.ctc.service.CostCollectionService;
import com.midea.pam.ctc.service.CostElementService;
import com.midea.pam.ctc.service.CrmExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.DeliveryAddressService;
import com.midea.pam.ctc.service.DesignPlanDetailMatchResultService;
import com.midea.pam.ctc.service.DesignPlanMaterialDetailService;
import com.midea.pam.ctc.service.DocumentLibraryService;
import com.midea.pam.ctc.service.ErpCodeRuleService;
import com.midea.pam.ctc.service.ExchangeAccountService;
import com.midea.pam.ctc.service.FeeCostDetailService;
import com.midea.pam.ctc.service.GSCPaymentAndInvoiceStatusPushService;
import com.midea.pam.ctc.service.GlegalContractService;
import com.midea.pam.ctc.service.GscPaymentInvoiceService;
import com.midea.pam.ctc.service.HroRequirementService;
import com.midea.pam.ctc.service.HroWorkingHourBillService;
import com.midea.pam.ctc.service.HroWorkingHourService;
import com.midea.pam.ctc.service.IFlowExceptionReBuildService;
import com.midea.pam.ctc.service.IhrAttendDetailSerice;
import com.midea.pam.ctc.service.InnerSwapApplyService;
import com.midea.pam.ctc.service.InvoiceApplyCallBackService;
import com.midea.pam.ctc.service.InvoiceApplyService;
import com.midea.pam.ctc.service.InvoiceReceivableService;
import com.midea.pam.ctc.service.LaborCostDetailService;
import com.midea.pam.ctc.service.LtcDictExtService;
import com.midea.pam.ctc.service.MailExtService;
import com.midea.pam.ctc.service.MaterialActualCostDetailService;
import com.midea.pam.ctc.service.MaterialAdjustExtService;
import com.midea.pam.ctc.service.MaterialAdjustService;
import com.midea.pam.ctc.service.MaterialChangeService;
import com.midea.pam.ctc.service.MaterialCostExtService;
import com.midea.pam.ctc.service.MaterialCostTransferCallBackService;
import com.midea.pam.ctc.service.MaterialCostTransferService;
import com.midea.pam.ctc.service.MaterialCustomDictService;
import com.midea.pam.ctc.service.MaterialDelistCallBackService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MaterialGetService;
import com.midea.pam.ctc.service.MaterialReturnCallBackService;
import com.midea.pam.ctc.service.MaterialReturnService;
import com.midea.pam.ctc.service.MaterialTransferCallBackService;
import com.midea.pam.ctc.service.MaterialTransferService;
import com.midea.pam.ctc.service.MeiXinPushMsgService;
import com.midea.pam.ctc.service.MilepostDesignMemberService;
import com.midea.pam.ctc.service.MilepostDesignPlanChangeRecordService;
import com.midea.pam.ctc.service.MilepostDesignPlanConfirmRecordRelationService;
import com.midea.pam.ctc.service.MilepostDesignPlanConfirmRecordService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeRecordService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailSubmitHistoryService;
import com.midea.pam.ctc.service.MilepostDesignPlanNotPublishRequirementService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.MilepostDesignPlanSubmitRecordRelationService;
import com.midea.pam.ctc.service.MilepostDesignPlanSubmitRecordService;
import com.midea.pam.ctc.service.MilepostTemplateDeliveryStandardsService;
import com.midea.pam.ctc.service.MilepostTemplateService;
import com.midea.pam.ctc.service.MilepostTemplateStageGroupService;
import com.midea.pam.ctc.service.MilepostTemplateStageService;
import com.midea.pam.ctc.service.MrpService;
import com.midea.pam.ctc.service.NoticeService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.OrganizationRelExtService;
import com.midea.pam.ctc.service.PaymentApplyDetailRelService;
import com.midea.pam.ctc.service.PaymentApplyInvoiceRelService;
import com.midea.pam.ctc.service.PaymentInvoiceCallBackService;
import com.midea.pam.ctc.service.PaymentInvoiceDetailService;
import com.midea.pam.ctc.service.PaymentInvoiceService;
import com.midea.pam.ctc.service.PaymentPenaltyProfitService;
import com.midea.pam.ctc.service.PaymentRecordService;
import com.midea.pam.ctc.service.PaymentWriteOffRecordService;
import com.midea.pam.ctc.service.PreProjectCallBackService;
import com.midea.pam.ctc.service.ProductTaxSettingService;
import com.midea.pam.ctc.service.ProjectAwardDeductionService;
import com.midea.pam.ctc.service.ProjectAwardService;
import com.midea.pam.ctc.service.ProjectBaseInfoChangeCallBackService;
import com.midea.pam.ctc.service.ProjectBudgetCostService;
import com.midea.pam.ctc.service.ProjectBudgetFeeService;
import com.midea.pam.ctc.service.ProjectBudgetHumanService;
import com.midea.pam.ctc.service.ProjectBudgetMaterialExtService;
import com.midea.pam.ctc.service.ProjectBudgetMaterialService;
import com.midea.pam.ctc.service.ProjectBudgetPushEmsErrorService;
import com.midea.pam.ctc.service.ProjectBudgetSynchroService;
import com.midea.pam.ctc.service.ProjectBudgetTravelService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectCallBackService;
import com.midea.pam.ctc.service.ProjectCloseMilestoneWorkflowCallBackService;
import com.midea.pam.ctc.service.ProjectContractRsService;
import com.midea.pam.ctc.service.ProjectCustomerSatisfactionService;
import com.midea.pam.ctc.service.ProjectDeliveriesService;
import com.midea.pam.ctc.service.ProjectFeeCollectionService;
import com.midea.pam.ctc.service.ProjectFundsDeployService;
import com.midea.pam.ctc.service.ProjectIncomeCostPlanService;
import com.midea.pam.ctc.service.ProjectMemberSerice;
import com.midea.pam.ctc.service.ProjectMilepostChangeHistoryService;
import com.midea.pam.ctc.service.ProjectMilepostDeliveryStandardsService;
import com.midea.pam.ctc.service.ProjectMilepostGroupService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectMilepostWorkflowCallBackService;
import com.midea.pam.ctc.service.ProjectPreviewCallBackService;
import com.midea.pam.ctc.service.ProjectProblemCommentService;
import com.midea.pam.ctc.service.ProjectProblemOperationRecordService;
import com.midea.pam.ctc.service.ProjectProblemService;
import com.midea.pam.ctc.service.ProjectProductService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectReopenService;
import com.midea.pam.ctc.service.ProjectRiskSettingService;
import com.midea.pam.ctc.service.ProjectRoleService;
import com.midea.pam.ctc.service.ProjectSecurityIncidentService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectStorgeSynchroService;
import com.midea.pam.ctc.service.ProjectSynchroService;
import com.midea.pam.ctc.service.ProjectTransferQualityService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.service.ProjectWbsReceiptsDesignPlanRelService;
import com.midea.pam.ctc.service.ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackService;
import com.midea.pam.ctc.service.PurchaseContractDetailChangeCallBackService;
import com.midea.pam.ctc.service.PurchaseContractInfoChangeCallBackService;
import com.midea.pam.ctc.service.PurchaseContractPlanChangeCallBackService;
import com.midea.pam.ctc.service.PurchaseContractProgressBudgetRelService;
import com.midea.pam.ctc.service.PurchaseContractProgressService;
import com.midea.pam.ctc.service.PurchaseContractQualityReportService;
import com.midea.pam.ctc.service.PurchaseContractStampService;
import com.midea.pam.ctc.service.PurchaseMaterialCloseDetailService;
import com.midea.pam.ctc.service.PurchaseMaterialReleaseDetailService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementDeliveryAddressHistoryService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.PurchaseOrderDetailDeliveryAddressHistoryService;
import com.midea.pam.ctc.service.PurchaseOrderDetailService;
import com.midea.pam.ctc.service.PurchaseOrderService;
import com.midea.pam.ctc.service.PurchaseProgressService;
import com.midea.pam.ctc.service.ReceiptClaimService;
import com.midea.pam.ctc.service.ReceiptWorkOrderCallBackService;
import com.midea.pam.ctc.service.ReceiptWorkOrderService;
import com.midea.pam.ctc.service.RefundApplyWorkflowCallbackService;
import com.midea.pam.ctc.service.RevenueCostOrderService;
import com.midea.pam.ctc.service.SdpCallbackService;
import com.midea.pam.ctc.service.SdpPurchaseContractService;
import com.midea.pam.ctc.service.SdpReceivePaymentApplyService;
import com.midea.pam.ctc.service.SdpReceivePaymentInvoiceDetailRequestService;
import com.midea.pam.ctc.service.SdpReceivePaymentInvoiceIspRequestService;
import com.midea.pam.ctc.service.StandardTermsService;
import com.midea.pam.ctc.service.StorageExtService;
import com.midea.pam.ctc.service.StorageInventoryExtService;
import com.midea.pam.ctc.service.SubmitHistoryWithRecursiveDetailService;
import com.midea.pam.ctc.service.SupplierQualityDeactivationService;
import com.midea.pam.ctc.service.SwapExecuteService;
import com.midea.pam.ctc.service.TicketTasksService;
import com.midea.pam.ctc.service.TicketWorkingHourImportService;
import com.midea.pam.ctc.service.VendorAslService;
import com.midea.pam.ctc.service.VendorPenaltyCallBackService;
import com.midea.pam.ctc.service.VendorPenaltyConfigService;
import com.midea.pam.ctc.service.VendorPenaltyRecordService;
import com.midea.pam.ctc.service.VendorPenaltyService;
import com.midea.pam.ctc.service.WbsPushToErpService;
import com.midea.pam.ctc.service.WorkingHourAccountingService;
import com.midea.pam.ctc.service.WorkingHourCostChangeHeaderService;
import com.midea.pam.ctc.service.WorkingHourDistributeDetailService;
import com.midea.pam.ctc.service.WorkingHourDistributeService;
import com.midea.pam.ctc.service.WorkingHourRemindWhiteListService;
import com.midea.pam.ctc.service.WorkingHourService;
import com.midea.pam.ctc.service.WriteOffService;
import com.midea.pam.ctc.service.helper.CarryoverBillHelper;
import com.midea.pam.ctc.service.helper.CheckItemHelper;
import com.midea.pam.ctc.service.helper.GlPeriodHelper;
import com.midea.pam.ctc.service.helper.LaborCostRealMonthHelper;
import com.midea.pam.ctc.service.helper.MilepostDesignPlanHelper;
import com.midea.pam.ctc.service.helper.OrgLaborCostTypeSetHelper;
import com.midea.pam.ctc.service.helper.OrganizationRelHelper;
import com.midea.pam.ctc.service.helper.SystemContextHelper;
import com.midea.pam.ctc.service.impl.AgencySynServiceImpl;
import com.midea.pam.ctc.service.impl.ApplicationIndustryServiceImpl;
import com.midea.pam.ctc.service.impl.AssetDeprnAccountingServiceImpl;
import com.midea.pam.ctc.service.impl.AsyncCostCollectionResultServiceImpl;
import com.midea.pam.ctc.service.impl.AsyncRequestResultServiceImpl;
import com.midea.pam.ctc.service.impl.BudgetItemServiceImpl;
import com.midea.pam.ctc.service.impl.BusiSceneNonSaleServiceImpl;
import com.midea.pam.ctc.service.impl.CarryoverAccountingServiceImpl;
import com.midea.pam.ctc.service.impl.CarryoverBillAccountingServiceImpl;
import com.midea.pam.ctc.service.impl.CarryoverBillBatchManageServiceImpl;
import com.midea.pam.ctc.service.impl.CarryoverBillCostCollectionRelServiceImpl;
import com.midea.pam.ctc.service.impl.CarryoverBillExtServiceImpl;
import com.midea.pam.ctc.service.impl.CarryoverBillIncomeCollectionServiceImpl;
import com.midea.pam.ctc.service.impl.CarryoverBillProcessScheduleServiceImpl;
import com.midea.pam.ctc.service.impl.CarryoverBillServiceImpl;
import com.midea.pam.ctc.service.impl.CarryoverBillWorkingHourRelServiceImpl;
import com.midea.pam.ctc.service.impl.CheckBasicInfoServiceImpl;
import com.midea.pam.ctc.service.impl.CheckItemServiceImpl;
import com.midea.pam.ctc.service.impl.CheckResultServiceImpl;
import com.midea.pam.ctc.service.impl.CheckSceneBasicRelServiceImpl;
import com.midea.pam.ctc.service.impl.CheckSceneInfoServiceImpl;
import com.midea.pam.ctc.service.impl.CloudPrintExtServiceImpl;
import com.midea.pam.ctc.service.impl.CnapsInfoServiceImpl;
import com.midea.pam.ctc.service.impl.CodeRuleServiceImpl;
import com.midea.pam.ctc.service.impl.CollectionConfigurationSerivceImpl;
import com.midea.pam.ctc.service.impl.ContractClassificationServiceImpl;
import com.midea.pam.ctc.service.impl.CostCollectionServiceImpl;
import com.midea.pam.ctc.service.impl.CostElementServiceImpl;
import com.midea.pam.ctc.service.impl.CtcAttachmentServiceImpl;
import com.midea.pam.ctc.service.impl.DeliveryAddressServiceImpl;
import com.midea.pam.ctc.service.impl.DesignPlanDetailMatchResultServiceImpl;
import com.midea.pam.ctc.service.impl.DesignPlanMaterialDetailServiceImpl;
import com.midea.pam.ctc.service.impl.DocumentLibraryServiceImpl;
import com.midea.pam.ctc.service.impl.ErpCodeRuleServiceImpl;
import com.midea.pam.ctc.service.impl.ExchangeAccountServiceImpl;
import com.midea.pam.ctc.service.impl.FeeCostDetailServiceImpl;
import com.midea.pam.ctc.service.impl.GSCPaymentAndInvoiceStatusPushServiceImpl;
import com.midea.pam.ctc.service.impl.GlegalContractServiceImpl;
import com.midea.pam.ctc.service.impl.GscPaymentInvoiceServiceImpl;
import com.midea.pam.ctc.service.impl.HroRequirementServiceImpl;
import com.midea.pam.ctc.service.impl.HroWorkingHourBillServiceImpl;
import com.midea.pam.ctc.service.impl.HroWorkingHourServiceImpl;
import com.midea.pam.ctc.service.impl.IFlowExceptionReBuildServiceImpl;
import com.midea.pam.ctc.service.impl.IhrAttendDetailServiceImpl;
import com.midea.pam.ctc.service.impl.InnerSwappApplyServiceImpl;
import com.midea.pam.ctc.service.impl.InvoiceApplyCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.InvoiceApplyServiceImpl;
import com.midea.pam.ctc.service.impl.InvoiceReceivableServiceImpl;
import com.midea.pam.ctc.service.impl.LaborCostDetailServiceImpl;
import com.midea.pam.ctc.service.impl.LtcDictExtServiceImpl;
import com.midea.pam.ctc.service.impl.MailExtServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialActualCostDetailServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialAdjustExtServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialAdjustServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialChangeServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialCostExtServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialCostTransferCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialCostTransferServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialCustomDictServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialDelistCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialExtServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialGetServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialReturnCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialReturnServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialTransferCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.MaterialTransferServiceImpl;
import com.midea.pam.ctc.service.impl.MeiXinPushMsgServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignMemberServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanChangeRecordServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanConfirmRecordRelationServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanConfirmRecordServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanDetailChangeRecordServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanDetailChangeServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanDetailServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanDetailSubmitHistoryServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanNotPublishRequirementServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanPurchaseRecordRelationServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanPurchaseRecordServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanSubmitRecordRelationServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanSubmitRecordServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostTemplateDeliveryStandardsServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostTemplateServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostTemplateStageGroupServiceImpl;
import com.midea.pam.ctc.service.impl.MilepostTemplateStageServiceImpl;
import com.midea.pam.ctc.service.impl.MrpServiceImpl;
import com.midea.pam.ctc.service.impl.NoticeServiceImpl;
import com.midea.pam.ctc.service.impl.OrganizationCustomDictServiceImpl;
import com.midea.pam.ctc.service.impl.OrganizationRelExtServiceImpl;
import com.midea.pam.ctc.service.impl.PaymentApplyDetailRelServiceImpl;
import com.midea.pam.ctc.service.impl.PaymentApplyInvoiceRelServiceImpl;
import com.midea.pam.ctc.service.impl.PaymentInvoiceCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.PaymentInvoiceDetailServiceImpl;
import com.midea.pam.ctc.service.impl.PaymentInvoiceServiceImpl;
import com.midea.pam.ctc.service.impl.PaymentPenaltyProfitServiceImpl;
import com.midea.pam.ctc.service.impl.PaymentRecordServiceImpl;
import com.midea.pam.ctc.service.impl.PaymentWriteOffRecordServiceImpl;
import com.midea.pam.ctc.service.impl.PreProjectCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.ProductTaxSettingServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectAwardDeductionServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectAwardServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBaseInfoChangeCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBudgetCostServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBudgetFeeServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBudgetHumanServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBudgetMaterialExtServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBudgetMaterialServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBudgetPushEmsErrorServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBudgetSynchroServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBudgetTargetServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBudgetTravelServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectBusinessServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectCloseMilestoneWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectContractRsServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectCustomerSatisfactionServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectDeliveriesServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectFeeCollectionServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectFundsDeployServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectIncomeCostPlanServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectMemberServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectMilepostChangeHistoryServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectMilepostDeliveryStandardsServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectMilepostGroupServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectMilepostServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectMilepostWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectPreviewCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectProblemCommentServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectProblemOperationRecordServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectProblemServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectProductServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectProfitServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectReopenServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectRiskSettingServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectRoleServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectSecurityIncidentServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectStorgeSynchroServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectSynchroServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectTerminationServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectTerminationTypeServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectTransferQualityServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectTypeServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectWbsReceiptsDesignPlanRelServiceImpl;
import com.midea.pam.ctc.service.impl.ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseBpaPriceServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseContractDetailChangeCallBackImpl;
import com.midea.pam.ctc.service.impl.PurchaseContractInfoChangeCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseContractPlanChangeCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseContractProgressBudgetRelServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseContractProgressServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseContractQualityReportServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseContractStampServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseMaterialCloseDetailServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseMaterialReleaseDetailServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseMaterialRequirementDeliveryAddressHistoryServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseMaterialRequirementServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseOrderDetailDeliveryAddressHistoryServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseOrderDetailServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseOrderServiceImpl;
import com.midea.pam.ctc.service.impl.PurchaseProgressServiceImpl;
import com.midea.pam.ctc.service.impl.ReceiptClaimServiceImpl;
import com.midea.pam.ctc.service.impl.ReceiptWorkOrderCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.ReceiptWorkOrderServiceImpl;
import com.midea.pam.ctc.service.impl.RefundApplyWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.service.impl.RevenueCostOrderServiceImpl;
import com.midea.pam.ctc.service.impl.SdpBuyersServiceImpl;
import com.midea.pam.ctc.service.impl.SdpPurchaseContractImpl;
import com.midea.pam.ctc.service.impl.SdpReceivePaymentApplyServiceImpl;
import com.midea.pam.ctc.service.impl.SdpReceivePaymentInvoiceDetailRequestServiceImpl;
import com.midea.pam.ctc.service.impl.SdpReceivePaymentInvoiceIspRequestServiceImpl;
import com.midea.pam.ctc.service.impl.StandardTermsServiceImpl;
import com.midea.pam.ctc.service.impl.StorageExtServiceImpl;
import com.midea.pam.ctc.service.impl.StorageInventoryExtServiceImpl;
import com.midea.pam.ctc.service.impl.SubmitHistoryWithRecursiveDetailServiceImpl;
import com.midea.pam.ctc.service.impl.SupplierQualityDeactivationServiceImpl;
import com.midea.pam.ctc.service.impl.SwapExecuteServiceImpl;
import com.midea.pam.ctc.service.impl.TicketTasksServiceImpl;
import com.midea.pam.ctc.service.impl.TicketWorkingHourImportServiceImpl;
import com.midea.pam.ctc.service.impl.VendorAslServiceImpl;
import com.midea.pam.ctc.service.impl.VendorPenaltyCallBackServiceImpl;
import com.midea.pam.ctc.service.impl.VendorPenaltyConfigServiceImpl;
import com.midea.pam.ctc.service.impl.VendorPenaltyRecordServiceImpl;
import com.midea.pam.ctc.service.impl.VendorPenaltyServiceImpl;
import com.midea.pam.ctc.service.impl.WbsPushToErpServiceImpl;
import com.midea.pam.ctc.service.impl.WorkingHourAccountingServiceImpl;
import com.midea.pam.ctc.service.impl.WorkingHourCostChangeHeaderServiceImpl;
import com.midea.pam.ctc.service.impl.WorkingHourDistributeDetailServiceImpl;
import com.midea.pam.ctc.service.impl.WorkingHourDistributeServiceImpl;
import com.midea.pam.ctc.service.impl.WorkingHourRemindWhiteListServiceImpl;
import com.midea.pam.ctc.service.impl.WorkingHourServiceImpl;
import com.midea.pam.ctc.service.impl.WriteOffServiceImpl;
import com.midea.pam.ctc.service.listener.AssetDeprnAccountingListener;
import com.midea.pam.ctc.service.listener.CarryoverBillCheckListener;
import com.midea.pam.ctc.service.listener.CarryoverBillCollectionSummaryListener;
import com.midea.pam.ctc.service.listener.CarryoverBillIncomeCalculationListener;
import com.midea.pam.ctc.service.listener.CarryoverBillProcessScheduleListener;
import com.midea.pam.ctc.service.listener.ConfirmCarryBilloverListener;
import com.midea.pam.ctc.service.listener.ContractBaseInfoChanageCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.ContractClassificationListener;
import com.midea.pam.ctc.service.listener.ContractProductChanageWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.ContractReceiptPlanChanageCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.ContractWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.CostCollectionResultListener;
import com.midea.pam.ctc.service.listener.CustomerTransferReverseWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.DesignPlanDetailChangeApprovalListener;
import com.midea.pam.ctc.service.listener.DesignPlanDetailChangeListener;
import com.midea.pam.ctc.service.listener.DesignPlanDetailChangeMatchResultListener;
import com.midea.pam.ctc.service.listener.DesignPlanDetailConfirmApprovalListener;
import com.midea.pam.ctc.service.listener.DesignPlanDetailConfirmApprovalOldListener;
import com.midea.pam.ctc.service.listener.DesignPlanDetailMatchResultListener;
import com.midea.pam.ctc.service.listener.DesignPlanDetailPurchaseApprovalListener;
import com.midea.pam.ctc.service.listener.DesignPlanDetailSubmitApprovalListener;
import com.midea.pam.ctc.service.listener.DesignPlanDetailSubmitForProjectListener;
import com.midea.pam.ctc.service.listener.DesignPlanDetailSubmitListener;
import com.midea.pam.ctc.service.listener.ExchangeAccountListener;
import com.midea.pam.ctc.service.listener.GscInvoiceDetailWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.GscInvoiceIspWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.InvoiceApplyCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.KsDesignPlanDetailMatchResultListener;
import com.midea.pam.ctc.service.listener.KsMatchingMaterialCodeListener;
import com.midea.pam.ctc.service.listener.MatchingMaterialCodeListener;
import com.midea.pam.ctc.service.listener.MateriaNewErpCodeEventListener;
import com.midea.pam.ctc.service.listener.MaterialAdjustApprovalListener;
import com.midea.pam.ctc.service.listener.MaterialCostTransferCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.MaterialDelistCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.MaterialGetWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.MaterialReturnCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.MaterialTransferCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.MilepostDesignPlanChangeApprovalListener;
import com.midea.pam.ctc.service.listener.MilepostDesignPlanNotPublishRequirementListener;
import com.midea.pam.ctc.service.listener.PaymentApplyWorkflowCallbackAbandonListener;
import com.midea.pam.ctc.service.listener.PaymentApplyWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.PaymentInvoiceCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.ProjectAssetChangeWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.ProjectBaseInfoChangeCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.ProjectBudgetChangeWorkflowCallbackListener;
import com.midea.pam.ctc.service.listener.ProjectBudgetListener;
import com.midea.pam.ctc.service.listener.ProjectBudgetTargetListener;
import com.midea.pam.ctc.service.listener.ProjectBusinessApprovalListener;
import com.midea.pam.ctc.service.listener.ProjectContractChangeWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.ProjectFeeItemListener;
import com.midea.pam.ctc.service.listener.ProjectMaterialListener;
import com.midea.pam.ctc.service.listener.ProjectPendingCloseCheckEventListener;
import com.midea.pam.ctc.service.listener.ProjectPreviewCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.ProjectStorgeSynchroListener;
import com.midea.pam.ctc.service.listener.ProjectSynchroListener;
import com.midea.pam.ctc.service.listener.ProjectTransferQualityWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.ProjectWbsBudgetChangeWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.ProjectWbsBudgetFeeListener;
import com.midea.pam.ctc.service.listener.ProjectWbsReceiptsApprovalListener;
import com.midea.pam.ctc.service.listener.ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.ProjectWbsSubmitReceiptsApprovalListener;
import com.midea.pam.ctc.service.listener.PurchaseContractDetailChangeCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.PurchaseContractInfoChangeCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.PurchaseContractPlanChangeCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.PurchaseContractProgressWorkflowCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.PurchaseContractPunishmentWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.PurchaseContractWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.PurchaseOrderCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.PurchaseOrderChangeCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.PurchaseOrderChangeSyncListener;
import com.midea.pam.ctc.service.listener.PurchaseOrderSyncListener;
import com.midea.pam.ctc.service.listener.PushToErpSyncListener;
import com.midea.pam.ctc.service.listener.ReceiptWorkOrderCallBackApprovalListener;
import com.midea.pam.ctc.service.listener.RefundApplyWorkflowCallbackApprovalListener;
import com.midea.pam.ctc.service.listener.SyncRedInvoiceToMifListener;
import com.midea.pam.ctc.service.listener.UpdateMaterialCostRequirementListener;
import com.midea.pam.ctc.service.listener.UpdateRequirementStatusListener;
import com.midea.pam.ctc.share.service.DifferenceShareAccountDetailService;
import com.midea.pam.ctc.share.service.DifferenceShareAccountService;
import com.midea.pam.ctc.share.service.DifferenceShareAccountSummaryService;
import com.midea.pam.ctc.share.service.DifferenceShareDataSyncService;
import com.midea.pam.ctc.share.service.DifferenceShareEsbSyncService;
import com.midea.pam.ctc.share.service.DifferenceShareProjectService;
import com.midea.pam.ctc.share.service.DifferenceShareResultDetailService;
import com.midea.pam.ctc.share.service.DifferenceShareService;
import com.midea.pam.ctc.share.service.InvoicePriceDifferenceRecordService;
import com.midea.pam.ctc.share.service.MaterialUpdateDifferenceRecordService;
import com.midea.pam.ctc.share.service.SubjectBalanceRecordService;
import com.midea.pam.ctc.share.service.SubjectBalanceService;
import com.midea.pam.ctc.share.service.impl.DifferenceShareAccountDetailServiceImpl;
import com.midea.pam.ctc.share.service.impl.DifferenceShareAccountServiceImpl;
import com.midea.pam.ctc.share.service.impl.DifferenceShareAccountSummaryServiceImpl;
import com.midea.pam.ctc.share.service.impl.DifferenceShareDataSyncServiceImpl;
import com.midea.pam.ctc.share.service.impl.DifferenceShareEsbSyncServiceImpl;
import com.midea.pam.ctc.share.service.impl.DifferenceShareProjectServiceImpl;
import com.midea.pam.ctc.share.service.impl.DifferenceShareResultDetailServiceImpl;
import com.midea.pam.ctc.share.service.impl.DifferenceShareServiceImpl;
import com.midea.pam.ctc.share.service.impl.InvoicePriceDifferenceRecordServiceImpl;
import com.midea.pam.ctc.share.service.impl.MaterialUpdateDifferenceRecordServiceImpl;
import com.midea.pam.ctc.share.service.impl.PurchasePriceDifferenceRecordServiceImpl;
import com.midea.pam.ctc.share.service.impl.SubjectBalanceRecordServiceImpl;
import com.midea.pam.ctc.share.service.impl.SubjectBalanceServiceImpl;
import com.midea.pam.ctc.share.service.listener.DifferenceShareAccountCreateListener;
import com.midea.pam.ctc.transfer.service.CustomerTransferService;
import com.midea.pam.ctc.transfer.service.CustomerTransferServiceImpl;
import com.midea.pam.ctc.wbs.service.PreviewWbsBudgetInfoChangeService;
import com.midea.pam.ctc.wbs.service.ProjectActivityService;
import com.midea.pam.ctc.wbs.service.ProjectBaselineBatchContractRsService;
import com.midea.pam.ctc.wbs.service.ProjectBaselineBatchService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBaselineChangeCallBackService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetBaselineChangeHistoryService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetBaselineService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetChangeHistoryService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetChangeService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetChangeWorkflowCallbackService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetCheckHelpper;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetDynamicChangeHistoryService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetDynamicService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetSummaryChangeHistoryService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetSummaryService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetSummaryServiceV2;
import com.midea.pam.ctc.wbs.service.WbsTemplateInfoService;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleDetailService;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleService;
import com.midea.pam.ctc.wbs.service.impl.PreviewWbsBudgetInfoChangeServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectActivityServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectBaselineBatchContractRsServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectBaselineBatchServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBaselineChangeCallBackServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetBaselineChangeHistoryServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetBaselineServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetChangeEsbServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetChangeHistoryServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetChangeServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetChangeWorkflowCallbackServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetCheckHelpperImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetDynamicChangeHistoryServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetDynamicServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetSummaryChangeHistoryServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetSummaryServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.ProjectWbsBudgetSummaryServiceV2Impl;
import com.midea.pam.ctc.wbs.service.impl.WbsTemplateInfoServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.WbsTemplateRuleDetailServiceImpl;
import com.midea.pam.ctc.wbs.service.impl.WbsTemplateRuleServiceImpl;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
@EnableConfigurationProperties({GlegalContractProperties.class})
public class CtcServiceAutoConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(CtcServiceAutoConfiguration.class);

    @Resource
    private SystemEnvironmentConfig systemEnvironmentConfig;

    @Resource
    private CommonPropertiesConfig commonPropertiesConfig;

    @Resource
    private GlegalContractProperties glegalContractProperties;

    @Bean
    public ValueCache valueCache() {
        return new RedisValueCache();
    }

    @Bean
    public ProjectBudgetFeeService projectBudgetFeeService() {
        return new ProjectBudgetFeeServiceImpl();
    }

    @Bean
    public ProjectBudgetHumanService projectBudgetHumanService() {
        return new ProjectBudgetHumanServiceImpl();
    }

    @Bean
    public ProjectBudgetMaterialService projectBudgetMaterialService() {
        return new ProjectBudgetMaterialServiceImpl();
    }

    @Bean
    public ProjectBudgetTravelService projectBudgetTravelService() {
        return new ProjectBudgetTravelServiceImpl();
    }

    @Bean
    public ProjectContractRsService projectContractRsService() {
        return new ProjectContractRsServiceImpl();
    }

    @Bean
    public ProjectProductService projectProductService() {
        return new ProjectProductServiceImpl();
    }

    @Bean
    public ProjectService projectService() {
        return new ProjectServiceImpl();
    }

    @Bean
    public ProjectBudgetTargetServiceImpl projectBudgetTargetService() {
        return new ProjectBudgetTargetServiceImpl();
    }

    @Bean
    public OssService ossService() {
        return new OssServiceImpl();
    }

    @Bean
    public OssServiceProperties ossProperties() {
        return new OssServiceProperties();
    }

    @Bean
    public ProjectTypeService projectTypeService() {
        return new ProjectTypeServiceImpl();
    }

    @Bean
    public CheckItemService checkItemService() {
        return new CheckItemServiceImpl();
    }

    @Bean
    public WorkingHourCostChangeHeaderService workingHourCostChangeHeaderService() {
        return new WorkingHourCostChangeHeaderServiceImpl();
    }

    @Bean
    public BudgetItemService budgetItemService() {
        return new BudgetItemServiceImpl();
    }

    @Bean
    public BusiSceneNonSaleService busiSceneNonSaleService() {
        return new BusiSceneNonSaleServiceImpl();
    }

    @Bean
    public CnapsInfoService cnapsInfoService() {
        return new CnapsInfoServiceImpl();
    }

    @Bean
    public CodeRuleService codeRuleService() {
        return new CodeRuleServiceImpl();
    }

    @Bean
    public ProjectMemberSerice projectMemberSerice() {
        return new ProjectMemberServiceImpl();
    }

    @Bean
    public ProjectBusinessService projectBusinessService() {
        return new ProjectBusinessServiceImpl();
    }

    @Bean
    public ApplicationContextProvider applicationContextProvider() {
        return new ApplicationContextProvider();
    }

    @Bean
    public WorkingHourService workingHourService() {
        return new WorkingHourServiceImpl();
    }

//    @Bean
//    public MxAttendService mxAttendService() {
//        URL url = null;
//        try {
//            url = new URL(commonPropertiesConfig.getHrUrlPath());
//            return new MxAttendService(url, MxAttendService.SERVICE);
//        } catch (Exception e) {
//            logger.error("Can not initialize the default wsdl from " + commonPropertiesConfig.getHrUrlPath(), e);
//        }
//        return new MxAttendService();
//    }

    @Bean
    public MilepostTemplateService milepostTemplateService() {
        return new MilepostTemplateServiceImpl();
    }

    @Bean
    public MilepostTemplateStageService milepostTemplateStageService() {
        return new MilepostTemplateStageServiceImpl();
    }

    @Bean
    public ReceiptClaimService receiptClaimService() {
        return new ReceiptClaimServiceImpl();
    }

    @Bean
    public ProjectMilepostService projectMilepostService() {
        return new ProjectMilepostServiceImpl();
    }

    @Bean
    public ProjectMilepostWorkflowCallBackService projectMilepostWorkflowCallBackService() {
        return new ProjectMilepostWorkflowCallbackServiceImpl();
    }

    @Bean
    public ProjectCloseMilestoneWorkflowCallBackService projectCloseMilestoneWorkflowCallBackService() {
        return new ProjectCloseMilestoneWorkflowCallbackServiceImpl();
    }

    @Bean
    public ProjectRoleService projectRoleService() {
        return new ProjectRoleServiceImpl();
    }

    @Bean
    public ProjectBudgetListener projectBudgetListener() {
        return new ProjectBudgetListener();
    }

    @Bean
    public MateriaNewErpCodeEventListener materiaNewErpCodeEventListener() {
        return new MateriaNewErpCodeEventListener();
    }

    @Bean
    public ProjectSynchroService projectSynchroService() {
        return new ProjectSynchroServiceImpl();
    }

    @Bean
    public CtcAttachmentService ctcAttachmentService() {
        return new CtcAttachmentServiceImpl();
    }

    @Bean
    public SdpBuyersServiceImpl ddpBuyersServiceImpl() {
        return new SdpBuyersServiceImpl();
    }

    @Bean
    public ProjectBudgetChangeWorkflowCallbackListener projectBudgetChangeWorkflowCallbackListener() {
        return new ProjectBudgetChangeWorkflowCallbackListener();
    }

    @Bean
    public MilepostDesignPlanDetailService milepostDesignPlanDetailService() {
        return new MilepostDesignPlanDetailServiceImpl();
    }

    @Bean
    public MilepostDesignPlanSubmitRecordService milepostDesignPlanSubmitRecordService() {
        return new MilepostDesignPlanSubmitRecordServiceImpl();
    }

    @Bean
    public MilepostDesignPlanService milepostDesignPlanService() {
        return new MilepostDesignPlanServiceImpl();
    }

    @Bean
    public ProjectProfitService projectProfitService() {
        return new ProjectProfitServiceImpl();
    }

    @Bean
    public ProjectDeliveriesService projectDeliveriesService() {
        return new ProjectDeliveriesServiceImpl();
    }

    @Bean
    public ContractService contractService() {
        return new ContractServiceImpl();
    }

    @Bean
    public ProjectBudgetSynchroService projectBudgetSynchroService() {
        return new ProjectBudgetSynchroServiceImpl();
    }

    @Bean
    public InvoiceApplyService invoiceApplyService() {
        return new InvoiceApplyServiceImpl();
    }

    @Bean
    public InvoiceReceivableService invoiceReceivableService() {
        return new InvoiceReceivableServiceImpl();
    }

    @Bean
    public ContractWorkflowCallbackService contractWorkflowCallbackService() {
        return new ContractWorkflowCallbackServiceImpl();
    }

    @Bean
    public ContractProductChanageWorkflowCallbackServiceImpl contractProductChanageWorkflowCallbackService() {
        return new ContractProductChanageWorkflowCallbackServiceImpl();
    }

    @Bean
    public ProjectSynchroListener projectSynchroListener() {
        return new ProjectSynchroListener();
    }

    @Bean
    public ProjectBudgetTargetListener projectBudgetTargetListener() {
        return new ProjectBudgetTargetListener();
    }

    @Bean
    public ProjectMaterialListener projectMaterialListener() {
        return new ProjectMaterialListener();
    }

    @Bean
    public ProjectFeeItemListener projectFeeItemListener() {
        return new ProjectFeeItemListener();
    }

    @Bean
    public ProjectFeeCollectionService projectFeeCollectionService() {
        return new ProjectFeeCollectionServiceImpl();
    }

    @Bean
    public ProjectFundsDeployService projectFundsDeployService() {
        return new ProjectFundsDeployServiceImpl();
    }

    @Bean
    public ProductTaxSettingService productTaxSettingService() {
        return new ProductTaxSettingServiceImpl();
    }

    @Bean
    public ProjectRiskSettingService projectRiskSettingService() {
        return new ProjectRiskSettingServiceImpl();
    }

    @Bean
    public WriteOffService writeOffService() {
        return new WriteOffServiceImpl();
    }

    @Bean
    public InvoicePlanService invoicePlanService() {
        return new InvoicePlanServiceImpl();
    }

    @Bean
    public ReceiptPlanService receiptPlanService() {
        return new ReceiptPlanServiceImpl();
    }

    @Bean
    public LegalContractService legalContractService() {
        return new LegalContractServiceImpl();
    }

    @Bean
    public MaterialExtService materialExtService() {
        return new MaterialExtServiceImpl();
    }

    @Bean
    public MaterialCostExtService materialCostExtService() {
        return new MaterialCostExtServiceImpl();
    }

    @Bean
    public MailExtService mailExtService() {
        return new MailExtServiceImpl();
    }

    @Bean
    public ErpCodeRuleService erpCodeRuleService() {
        return new ErpCodeRuleServiceImpl();
    }

    @Bean
    public MilepostDesignPlanConfirmRecordService milepostDesignPlanConfirmRecordService() {
        return new MilepostDesignPlanConfirmRecordServiceImpl();
    }

    @Bean
    public MilepostDesignPlanConfirmRecordRelationService milepostDesignPlanConfirmRecordRelationService() {
        return new MilepostDesignPlanConfirmRecordRelationServiceImpl();
    }

    @Bean
    public MilepostDesignPlanSubmitRecordRelationService milepostDesignPlanSubmitRecordRelationService() {
        return new MilepostDesignPlanSubmitRecordRelationServiceImpl();
    }

    @Bean
    public LegalContractHistoryService legalContractHistoryService() {
        return new LegalContractHistoryServiceImpl();
    }

    @Bean
    public ProjectStorgeSynchroService projectStorgeSynchroService() {
        return new ProjectStorgeSynchroServiceImpl();
    }

    @Bean
    public ProjectStorgeSynchroListener projectStorgeSynchroListener() {
        return new ProjectStorgeSynchroListener();
    }

    @Bean
    public PurchaseMaterialReleaseDetailService purchaseMaterialReleaseDetailService() {
        return new PurchaseMaterialReleaseDetailServiceImpl();
    }

    @Bean
    public PurchaseMaterialCloseDetailService purchaseMaterialCloseDetailService() {
        return new PurchaseMaterialCloseDetailServiceImpl();
    }

    @Bean
    public PurchaseMaterialRequirementService purchaseMaterialRequirementService() {
        return new PurchaseMaterialRequirementServiceImpl();
    }

    @Bean
    public PurchaseOrderService purchaseOrderService() {
        return new PurchaseOrderServiceImpl();
    }

    @Bean
    public PurchaseOrderChangeService purchaseOrderChangeService() {
        return new PurchaseOrderChangeServiceImpl();
    }

    @Bean
    public PurchaseOrderDetailService purchaseOrderDetailService() {
        return new PurchaseOrderDetailServiceImpl();
    }

    @Bean
    public MaterialGetService MaterialGetService() {
        return new MaterialGetServiceImpl();
    }

    @Bean
    public MaterialCostTransferService materialCostTransferService() {
        return new MaterialCostTransferServiceImpl();
    }

    @Bean
    public MaterialReturnService materialReturnService() {
        return new MaterialReturnServiceImpl();
    }

    @Bean
    public LtcDictExtService ltcDictExtService() {
        return new LtcDictExtServiceImpl();
    }

    @Bean
    public OrganizationCustomDictService organizationCustomDictService() {
        return new OrganizationCustomDictServiceImpl();
    }

    @Bean
    public MaterialCustomDictService materialCustomDictService() {
        return new MaterialCustomDictServiceImpl();
    }

    @Bean
    VendorAslService vendorAslService() {
        return new VendorAslServiceImpl();
    }

    @Bean
    public MaterialTransferService materialTransferService() {
        return new MaterialTransferServiceImpl();
    }

    @Bean
    public StorageExtService storageExtService() {
        return new StorageExtServiceImpl();
    }

    @Bean
    public AdapterService adapterService() {
        return new AdapterServiceImpl();
    }

    @Bean
    public OrganizationRelExtService organizationRelExtService() {
        return new OrganizationRelExtServiceImpl();
    }

    @Bean
    public ContractHelper contractHelper() {
        return new ContractHelper();
    }

    @Bean
    public ContractHisHelper contractHisHelper() {
        return new ContractHisHelper();
    }

    @Bean
    public PurchaseContractService purchaseContractService() {
        return new PurchaseContractServiceImpl();
    }

    @Bean
    public PurchaseContractDetailService purchaseContractDetailService() {
        return new PurchaseContractDetailServiceImpl();
    }

    @Bean
    public PaymentPlanService paymentPlanService() {
        return new PaymentPlanServiceImpl();
    }

    @Bean
    public ProjectBudgetCostService projectBudgetCostService() {
        return new ProjectBudgetCostServiceImpl();
    }

    @Bean
    public CarryoverBillService carryoverBillService() {
        return new CarryoverBillServiceImpl();
    }

    @Bean
    public CarryoverBillIncomeCollectionService carryoverBillIncomeCollectionService() {
        return new CarryoverBillIncomeCollectionServiceImpl();
    }

    @Bean
    public CostCollectionService costCollectionService() {
        return new CostCollectionServiceImpl();
    }

    @Bean
    public PurchaseContractWorkflowCallbackService purchaseContractWorkflowCallbackService() {
        return new PurchaseContractWorkflowCallbackServiceImpl();
    }

    @Bean
    public MaterialActualCostDetailService materialActualCostDetailService() {
        return new MaterialActualCostDetailServiceImpl();
    }

    @Bean
    public CarryoverBillCostCollectionRelService carryoverBillCostCollectionRelService() {
        return new CarryoverBillCostCollectionRelServiceImpl();
    }

    @Bean
    public CarryoverBillProcessScheduleService carryoverBillProcessScheduleService() {
        return new CarryoverBillProcessScheduleServiceImpl();
    }

    @Bean
    public LaborCostDetailService laborCostDetailService() {
        return new LaborCostDetailServiceImpl();
    }

    @Bean
    public CarryoverBillExtService carryoverBillExtService() {
        return new CarryoverBillExtServiceImpl();
    }

    @Bean
    public PurchaseProgressService purchaseProgressService() {
        return new PurchaseProgressServiceImpl();
    }

    @Bean
    public PurchaseBpaPriceServiceImpl purchaseBpaPriceService() {
        return new PurchaseBpaPriceServiceImpl();
    }

    @Bean
    public FeeCostDetailService feeCostDetailService() {
        return new FeeCostDetailServiceImpl();
    }

    @Bean
    public HroRequirementService hroRequirementService() {
        return new HroRequirementServiceImpl();
    }

    @Bean
    public HroWorkingHourService hroWorkingHourService() {
        return new HroWorkingHourServiceImpl();
    }

    @Bean
    public HroWorkingHourBillService hroWorkingHourBillService() {
        return new HroWorkingHourBillServiceImpl();
    }

    @Bean
    public IhrAttendDetailSerice ihrAttendDetailSerice() {
        return new IhrAttendDetailServiceImpl();
    }

    @Bean
    public CarryoverBillCollectionSummaryListener carryoverBillCollectionSummaryListener() {
        return new CarryoverBillCollectionSummaryListener();
    }

    @Bean
    public CarryoverBillCheckListener carryoverBillCheckListener() {
        return new CarryoverBillCheckListener();
    }

    @Bean
    public CarryoverBillIncomeCalculationListener carryoverBillIncomeCalculationListener() {
        return new CarryoverBillIncomeCalculationListener();
    }

    @Bean
    public CarryoverBillAccountingService carryoverBillAccountingService() {
        return new CarryoverBillAccountingServiceImpl();
    }

    @Bean
    public MilepostDesignPlanDetailChangeService milepostDesignPlanDetailChangeService() {
        return new MilepostDesignPlanDetailChangeServiceImpl();
    }

    @Bean
    public MilepostDesignPlanChangeRecordService milepostDesignPlanChangeRecordService() {
        return new MilepostDesignPlanChangeRecordServiceImpl();
    }

    @Bean
    public ProjectBudgetChangeService projectBudgetChangeService() {
        return new ProjectBudgetChangeServiceImpl();
    }

    @Bean
    public ProjectBudgetMaterialChangeHistoryService projectBudgetMaterialChangeHistoryService() {
        return new ProjectBudgetMaterialChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectBudgetMaterialChangeSummaryHistoryService projectBudgetMaterialChangeSummaryHistoryService() {
        return new ProjectBudgetMaterialChangeSummaryHistoryServiceImpl();
    }

    @Bean
    public ProjectBudgetHumanChangeHistoryService projectBudgetHumanChangeHistoryService() {
        return new ProjectBudgetHumanChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectBudgetHumanChangeSummaryHistoryService projectBudgetHumanChangeSummaryHistoryService() {
        return new ProjectBudgetHumanChangeSummaryHistoryServiceImpl();
    }

    @Bean
    public ProjectBudgetTravelChangeHistoryService projectBudgetTravelChangeHistoryService() {
        return new ProjectBudgetTravelChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectBudgetTravelChangeSummaryHistoryService projectBudgetTravelChangeSummaryHistoryService() {
        return new ProjectBudgetTravelChangeSummaryHistoryServiceImpl();
    }

    @Bean
    public ProjectBudgetFeeChangeSummaryHistoryService projectBudgetFeeChangeSummaryHistoryService() {
        return new ProjectBudgetFeeChangeSummaryHistoryServiceImpl();
    }

    @Bean
    public ProjectBudgetFeeChangeHistoryService projectBudgetFeeChangeHistoryService() {
        return new ProjectBudgetFeeChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectBudgetChangeSummaryHistoryService projectBudgetChangeSummaryHistoryService() {
        return new ProjectBudgetChangeSummaryHistoryServiceImpl();
    }

    @Bean
    public RevenueCostOrderService revenueCostOrderService() {
        return new RevenueCostOrderServiceImpl();
    }

    @Bean
    public ProjectProfitChangeHistoryService projectProfitChangeHistoryService() {
        return new ProjectProfitChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectBudgetChangeWorkflowCallbackService projectBudgetChangeWorkflowCallbackService() {
        return new ProjectBudgetChangeWorkflowCallbackServiceImpl();
    }

    @Bean
    public WorkingHourAccountingService workingHourAccountingService() {
        return new WorkingHourAccountingServiceImpl();
    }

    @Bean
    public ProjectBudgetChangeHelper projectBudgetChangeHelper() {
        return new ProjectBudgetChangeHelper();
    }

    @Bean("projectBudgetChangeEsbService")
    public ProjectBudgetChangeEsbServiceImpl projectBudgetChangeEsbService() {
        return new ProjectBudgetChangeEsbServiceImpl();
    }

    @Bean("projectWbsBudgetChangeEsbService")
    public ProjectWbsBudgetChangeEsbServiceImpl projectWbsBudgetChangeEsbServiceImpl() {
        return new ProjectWbsBudgetChangeEsbServiceImpl();
    }

    @Bean
    public AsyncRequestResultService asyncRequestResultService() {
        return new AsyncRequestResultServiceImpl();
    }

    @Bean
    public DesignPlanDetailMatchResultService designPlanDetailMatchResultService() {
        return new DesignPlanDetailMatchResultServiceImpl();
    }

    @Bean
    public DesignPlanDetailMatchResultListener designPlanDetailMatchResultListener() {
        return new DesignPlanDetailMatchResultListener();
    }

    @Bean
    public DesignPlanDetailSubmitListener designPlanDetailSubmitListener() {
        return new DesignPlanDetailSubmitListener();
    }

    @Bean
    public DesignPlanDetailSubmitForProjectListener designPlanDetailSubmitForProjectEvent() {
        return new DesignPlanDetailSubmitForProjectListener();
    }

    @Bean
    public DesignPlanDetailSubmitApprovalListener designPlanDetailSubmitApprovalListener() {
        return new DesignPlanDetailSubmitApprovalListener();
    }

    @Bean
    public ConfirmCarryBilloverListener confirmCarryBilloverListener() {
        return new ConfirmCarryBilloverListener();
    }

    @Bean
    public DesignPlanDetailConfirmApprovalListener designPlanDetailConfirmApprovalListener() {
        return new DesignPlanDetailConfirmApprovalListener();
    }

    @Bean
    public DesignPlanDetailConfirmApprovalOldListener designPlanDetailConfirmApprovalOldListener() {
        return new DesignPlanDetailConfirmApprovalOldListener();
    }

    @Bean
    public DesignPlanDetailChangeListener designPlanDetailChangeListener() {
        return new DesignPlanDetailChangeListener();
    }

    @Bean
    public DesignPlanDetailChangeApprovalListener designPlanDetailChangeApprovalListener() {
        return new DesignPlanDetailChangeApprovalListener();
    }

    @Bean
    public CostCollectionResultListener costCollectionResultListener() {
        return new CostCollectionResultListener();
    }

    @Bean
    public PurchaseOrderSyncListener purchaseOrderSyncListener() {
        return new PurchaseOrderSyncListener();
    }

    @Bean
    public PurchaseOrderChangeSyncListener purchaseOrderChangeSyncListener() {
        return new PurchaseOrderChangeSyncListener();
    }

    @Bean
    public ProjectBudgetMaterialAddRelationService projectBudgetMaterialAddRelationService() {
        return new ProjectBudgetMaterialAddRelationServiceImpl();
    }

    @Bean
    public ProjectBudgetChangePushEmsService projectBudgetChangePushEmsService() {
        return new ProjectBudgetChangePushEmsServiceImpl();
    }

    @Bean("projectBudgetChangeSyncService")
    public ProjectBudgetChangeSyncServiceImpl projectBudgetChangeSyncService() {
        return new ProjectBudgetChangeSyncServiceImpl();
    }

    @Bean
    public EsbResultReturnService esbResultReturnService() {
        return new EsbResultReturnServiceImpl();
    }

    @Bean
    public RdmInterfaceService rdmInterfaceService() {
        return new RdmInterfaceServiceImpl();
    }

    @Bean
    public EamPurchaseInfoService eamPurchaseInfoService() {
        return new EamPurchaseInfoServiceImpl();
    }

    @Bean
    public RdmSettlementSheetDetailService rdmSettlementSheetDetailService() {
        return new RdmSettlementSheetDetailServiceImpl();
    }

    @Bean
    public RdmSettlementSheetService rdmSettlementSheetService() {
        return new RdmSettlementSheetServiceImpl();
    }

    @Bean
    public RdmWorkingHourService rdmWorkingHourService() {
        return new RdmWorkingHourServiceImpl();
    }

    @Bean
    public TransitionRdmWorkingHourService transitionRdmWorkingHourService() {
        return new TransitionRdmWorkingHourServiceImpl();
    }

    @Bean
    public RdmResourcePlanService rdmResourcePlanService() {
        return new RdmResourcePlanServiceImpl();
    }

    @Bean
    public TransitionRdmResourcePlanService transitionRdmResourcePlanService() {
        return new TransitionRdmResourcePlanServiceImpl();
    }

    @Bean
    public StorageInventoryExtService storageInventoryExtService() {
        return new StorageInventoryExtServiceImpl();
    }

    @Bean
    public MaterialOutsourcingContractConfigService materialOutsourcingContractConfigService() {
        return new MaterialOutsourcingContractConfigServiceImpl();
    }

    @Bean
    public CostRatioConfigService costRatioConfigService() {
        return new CostRatioConfigServiceImpl();
    }

    @Bean
    public CostRatioConfigDetailService costRatioConfigDetailService() {
        return new CostRatioConfigDetailServiceImpl();
    }


    @Bean
    public BasedataExtService basedataExtService() {
        return new BasedataExtServiceImpl();
    }


    @Bean
    public PaymentApplyWorkflowCallbackService paymentApplyWorkflowCallbackService() {
        return new PaymentApplyWorkflowCallbackServiceImpl();
    }

    @Bean
    public PaymentInvoiceService paymentInvoiceService() {
        return new PaymentInvoiceServiceImpl();
    }

    @Bean
    public CarryoverAccountingService carryoverAccountingService() {
        return new CarryoverAccountingServiceImpl();
    }

    @Bean
    public CrmExtService crmExtService() {
        return new CrmExtServiceImpl();
    }

    @Bean
    public PaymentInvoiceDetailService paymentInvoiceDetailService() {
        return new PaymentInvoiceDetailServiceImpl();
    }

    @Bean
    public PaymentPenaltyProfitService paymentPenaltyProfitService() {
        return new PaymentPenaltyProfitServiceImpl();
    }

    @Bean
    public PaymentApplyInvoiceRelService paymentApplyInvoiceRelService() {
        return new PaymentApplyInvoiceRelServiceImpl();
    }

    @Bean
    public PaymentApplyDetailRelService paymentApplyDetailRelService() {
        return new PaymentApplyDetailRelServiceImpl();
    }

    @Bean
    public CarryoverBillOutsourcingCostCollectionService carryoverBillOutsourcingCostCollectionService() {
        return new CarryoverBillOutsourcingCostCollectionServiceImpl();
    }

    @Bean
    public SystemEnvironmentConfig systemEnvironmentConfig() {
        return new SystemEnvironmentConfig();
    }

    @Bean
    public PaymentApplyService paymentApplyService() {
        return new PaymentApplyServiceImpl(this.systemEnvironmentConfig.getSymbol());
    }

    @Bean
    public PaymentRecordService paymentRecordService() {
        return new PaymentRecordServiceImpl();
    }

    @Bean
    public PaymentWriteOffRecordService paymentWriteOffRecordService() {
        return new PaymentWriteOffRecordServiceImpl();
    }

    @Bean
    public ProjectAwardDeductionService projectAwardDeductionService() {
        return new ProjectAwardDeductionServiceImpl();
    }

    @Bean
    public ProjectAwardService projectAwardService() {
        return new ProjectAwardServiceImpl();
    }

    @Bean
    public ContractProductCostService contractProductCostService() {
        return new ContractProductCostServiceImpl();
    }

    @Bean
    public ReceiptInvoiceRelationService receiptInvoiceRelationService() {
        return new ReceiptInvoiceRelationServiceImpl();
    }

    @Bean
    public WorkingHourDistributeService workingHourDistributeService() {
        return new WorkingHourDistributeServiceImpl();
    }

    @Bean
    public WorkingHourRemindWhiteListService workingHourRemindWhiteListService() {
        return new WorkingHourRemindWhiteListServiceImpl();
    }

    @Bean
    public WorkingHourDistributeDetailService workingHourDistributeDetailService() {
        return new WorkingHourDistributeDetailServiceImpl();
    }

    @Bean
    public MeiXinPushMsgService meiXinPushMsgService() {
        return new MeiXinPushMsgServiceImpl();
    }

    @Bean
    public MilepostDesignPlanHelper milepostDesignPlanHelper() {
        return new MilepostDesignPlanHelper();
    }

    @Bean
    public GlPeriodHelper glPeriodHelper() {
        return new GlPeriodHelper();
    }

    @Bean
    public OrganizationRelHelper organizationRelHelper() {
        return new OrganizationRelHelper();
    }

    @Bean
    public LaborCostRealMonthHelper laborCostRealMonthHelper() {
        return new LaborCostRealMonthHelper();
    }

    @Bean
    public ProjectIncomeCostPlanService projectIncomeCostPlanService() {
        return new ProjectIncomeCostPlanServiceImpl();
    }

    @Bean
    public CarryoverBillHelper carryoverBillHelper() {
        return new CarryoverBillHelper();
    }

    @Bean
    public DifferenceShareAccountService differenceShareAccountService() {
        return new DifferenceShareAccountServiceImpl();
    }

    @Bean
    public DifferenceShareAccountDetailService differenceShareAccountDetailService() {
        return new DifferenceShareAccountDetailServiceImpl();
    }

    @Bean
    public DifferenceShareAccountSummaryService differenceShareAccountSummaryService() {
        return new DifferenceShareAccountSummaryServiceImpl();
    }

    @Bean
    public MaterialUpdateDifferenceRecordService materialUpdateDifferenceRecordService() {
        return new MaterialUpdateDifferenceRecordServiceImpl();
    }

    @Bean
    public PurchasePriceDifferenceRecordServiceImpl purchasePriceDifferenceRecordService() {
        return new PurchasePriceDifferenceRecordServiceImpl();
    }

    @Bean
    public SubjectBalanceService subjectBalanceService() {
        return new SubjectBalanceServiceImpl();
    }

    @Bean
    public ContractChangewayService contractChangewayService() {
        return new ContractChangewayServiceImpl();
    }

    @Bean
    public ContractModifyCheck contractModifyCheck() {
        return new ContractModifyCheck();
    }

    @Bean
    public DifferenceShareDataSyncService differenceShareDataSyncService() {
        return new DifferenceShareDataSyncServiceImpl();
    }

    @Bean
    public InvoicePriceDifferenceRecordService invoicePriceDifferenceRecordService() {
        return new InvoicePriceDifferenceRecordServiceImpl();
    }

    @Bean
    public DifferenceShareProjectService differenceShareProjectService() {
        return new DifferenceShareProjectServiceImpl();
    }

    @Bean
    public DifferenceShareService differenceShareService() {
        return new DifferenceShareServiceImpl();
    }

    @Bean
    public DifferenceShareAccountCreateListener differenceShareAccountCreateListener() {
        return new DifferenceShareAccountCreateListener();
    }

    @Bean
    public SubjectBalanceRecordService subjectBalanceRecordService() {
        return new SubjectBalanceRecordServiceImpl();
    }

    @Bean
    public DifferenceShareResultDetailService differenceShareResultDetailService() {
        return new DifferenceShareResultDetailServiceImpl();
    }

    @Bean
    public DifferenceShareEsbSyncService differenceShareEsbSyncService() {
        return new DifferenceShareEsbSyncServiceImpl();
    }

    @Bean
    public CarryoverBillBatchManageService carryoverBillBatchManageService() {
        return new CarryoverBillBatchManageServiceImpl();
    }

    @Bean
    public MilepostNoticeService milepostNoticeService() {
        return new MilepostNoticeServiceImpl();
    }

    @Bean
    public MilepostNoticeProperties milepostNoticeProperties() {
        return new MilepostNoticeProperties();
    }

    @Bean
    public ContractHisService contractHisService() {
        return new ContractHisServiceImpl();
    }

    @Bean
    public NoticeService noticeService() {
        return new NoticeServiceImpl();
    }

    @Bean
    public CarryoverBillWorkingHourRelService carryoverBillWorkingHourRelService() {
        return new CarryoverBillWorkingHourRelServiceImpl();
    }

    @Bean
    public CheckItemHelper checkItemHelper() {
        return new CheckItemHelper();
    }

    @Bean
    public ProjectSyncServiceImpl projectSyncService() {
        return new ProjectSyncServiceImpl();
    }

    @Bean
    public ProjectTerminationTypeServiceImpl projectTerminationTypeService() {
        return new ProjectTerminationTypeServiceImpl();
    }

    @Bean
    public ProjectTerminationWorkflowCallbackServiceImpl projectTerminationWorkflowCallbackService() {
        return new ProjectTerminationWorkflowCallbackServiceImpl();
    }

    @Bean
    public ProjectTerminationServiceImpl projectTerminationService() {
        return new ProjectTerminationServiceImpl();
    }

    @Bean
    public MilepostDesignPlanPurchaseRecordServiceImpl milepostDesignPlanPurchaseRecordService() {
        return new MilepostDesignPlanPurchaseRecordServiceImpl();
    }

    @Bean
    public MilepostDesignPlanPurchaseRecordRelationServiceImpl milepostDesignPlanPurchaseRecordRelationService() {
        return new MilepostDesignPlanPurchaseRecordRelationServiceImpl();
    }

    @Bean
    public DesignPlanDetailPurchaseApprovalListener designPlanDetailPurchaseApprovalListener() {
        return new DesignPlanDetailPurchaseApprovalListener();
    }

    @Bean
    public DesignPlanDetailChangeMatchResultListener designPlanDetailChangeMatchResultListener() {
        return new DesignPlanDetailChangeMatchResultListener();
    }

    @Bean
    public CommonPropertiesConfig commonPropertiesConfig() {
        return new CommonPropertiesConfig();
    }

    @Bean
    public AgencySynService agencySynService() {
        return new AgencySynServiceImpl();
    }


    @Bean
    public RefundApplyService refundApplyService() {
        return new RefundApplyServiceImpl();
    }

    @Bean
    public AsyncCostCollectionResultService asyncCostCollectionResultService() {
        return new AsyncCostCollectionResultServiceImpl();
    }

    @Bean
    public CarryoverBillProcessScheduleListener carryoverBillProcessScheduleListener() {
        return new CarryoverBillProcessScheduleListener();
    }

    @Bean
    public EsbResultReturnRecordService esbResultReturnRecordService() {
        return new EsbResultReturnRecordServiceImpl();
    }

    @Bean
    public ApplyContractUnitService applyContractUnitService() {
        return new ApplyContractUnitServiceImpl();
    }

    @Bean
    public GlegalInterfaceService glegalInterfaceService() {
        return new GlegalInterfaceServiceImpl(glegalContractProperties);
    }

    @Bean
    public GlegalInterfaceV2Service glegalInterfaceV2Service() {
        return new GlegalInterfaceV2ServiceImpl(glegalContractProperties);
    }

    @Bean
    public SealAdministratorService sealAdministratorService() {
        return new SealAdministratorServiceImpl();
    }

    @Bean
    public GlegalContractWorkflowCallbackService glegalContractWorkflowCallbackService() {
        return new GlegalContractWorkflowCallbackServiceImpl();
    }

    @Bean
    public GlegalPurchaseContractWorkflowCallbackService glegalPurchaseContractWorkflowCallbackService() {
        return new GlegalPurchaseContractWorkflowCallbackServiceImpl();
    }

    @Bean
    public GlegalContractProperties glegalContractProperties() {
        return new GlegalContractProperties();
    }

    @Bean
    public ProjectGanttChartService projectGanttChartService() {
        return new ProjectGanttChartServiceImpl();
    }

    @Bean
    public ProjectBudgetPushEmsErrorService projectBudgetPushEmsErrorService() {
        return new ProjectBudgetPushEmsErrorServiceImpl();
    }

    @Bean
    public ApplicationIndustryService applicationIndustryService() {
        return new ApplicationIndustryServiceImpl();
    }

    @Bean
    public InnerSwapApplyService innerSwapApplyService() {
        return new InnerSwappApplyServiceImpl();
    }

    @Bean
    public SwapExecuteService swapExecuteService() {
        return new SwapExecuteServiceImpl();
    }

    @Bean
    public VendorPenaltyConfigService vendorPenaltyConfigService() {
        return new VendorPenaltyConfigServiceImpl();
    }

    @Bean
    public VendorPenaltyService vendorPenaltyService() {
        return new VendorPenaltyServiceImpl();
    }

    @Bean
    public WbsPushToErpService wbsPushToErpService() {
        return new WbsPushToErpServiceImpl();
    }

    @Bean
    public MaterialAdjustService materialAdjustService() {
        return new MaterialAdjustServiceImpl();
    }

    @Bean
    public KsDesignPlanDetailMatchResultListener ksDesignPlanDetailMatchResultListener() {
        return new KsDesignPlanDetailMatchResultListener();
    }

    @Bean
    public UpdateRequirementStatusListener updateRequirementStatusListener() {
        return new UpdateRequirementStatusListener();
    }

    @Bean
    public MilepostDesignMemberService milepostDesignMemberService() {
        return new MilepostDesignMemberServiceImpl();
    }

    @Bean
    public TicketTasksService ticketTasksService() {
        return new TicketTasksServiceImpl();
    }

    @Bean
    public TicketWorkingHourImportService ticketWorkingHourImportService() {
        return new TicketWorkingHourImportServiceImpl();
    }

    @Bean
    public DesignPlanMaterialDetailService designPlanMaterialDetailService() {
        return new DesignPlanMaterialDetailServiceImpl();
    }

    @Bean
    public MrpService mrpService() {
        return new MrpServiceImpl();
    }

    @Bean
    public CloudPrintExtService cloudPrintExtService() {
        return new CloudPrintExtServiceImpl();
    }

    @Bean
    public PurchaseContractBudgetMaterialService PurchaseContractBudgetMaterialService() {
        return new PurchaseContractBudgetMaterialServiceImpl();
    }

    @Bean
    public ProjectProblemService projectProblemService() {
        return new ProjectProblemServiceImpl();
    }

    @Bean
    public ProjectProblemCommentService projectProblemCommentService() {
        return new ProjectProblemCommentServiceImpl();
    }

    @Bean
    public ProjectProblemOperationRecordService projectProblemOperationRecordService() {
        return new ProjectProblemOperationRecordServiceImpl();
    }

    @Bean
    public DocumentLibraryService documentLibraryService() {
        return new DocumentLibraryServiceImpl();
    }

    @Bean
    public CostElementService costElementService() {
        return new CostElementServiceImpl();
    }

    @Bean
    public CollectionConfigurationSerivce collectionConfigurationSerivceImpl() {
        return new CollectionConfigurationSerivceImpl();
    }

    @Bean
    public MatchingMaterialCodeListener matchingMaterialCodeListener() {
        return new MatchingMaterialCodeListener();
    }

    @Bean
    public KsMatchingMaterialCodeListener ksMatchingMaterialCodeListener() {
        return new KsMatchingMaterialCodeListener();
    }

    @Bean
    public ContractClassificationService contractClassificationService() {
        return new ContractClassificationServiceImpl();
    }

    @Bean
    public ContractClassificationListener contractClassificationListener() {
        return new ContractClassificationListener();
    }

    @Bean
    public ProjectTransferQualityService projectTransferQualityService() {
        return new ProjectTransferQualityServiceImpl();
    }

    @Bean
    public ProjectReopenService projectReopenService() {
        return new ProjectReopenServiceImpl();
    }

    @Bean
    public OrgLaborCostTypeSetHelper orgLaborCostTypeSetHelper() {
        return new OrgLaborCostTypeSetHelper();
    }

    @Bean
    public ProjectBudgetMaterialExtService projectBudgetMaterialExtService() {
        return new ProjectBudgetMaterialExtServiceImpl();
    }

    @Bean
    public WbsTemplateRuleDetailService wbsTemplateRuleDetailService() {
        return new WbsTemplateRuleDetailServiceImpl();
    }

    @Bean
    public WbsTemplateInfoService wbsTemplateInfoService() {
        return new WbsTemplateInfoServiceImpl();
    }

    @Bean
    public WbsTemplateRuleService wbsTemplateRuleService() {
        return new WbsTemplateRuleServiceImpl();
    }

    @Bean
    public ProjectActivityService projectActivityService() {
        return new ProjectActivityServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetService projectWbsBudgetService() {
        return new ProjectWbsBudgetServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetSummaryService projectWbsBudgetSummaryService() {
        return new ProjectWbsBudgetSummaryServiceImpl();
    }

    @Bean
    public MilepostTemplateStageGroupService milepostTemplateStageGroupService() {
        return new MilepostTemplateStageGroupServiceImpl();
    }

    @Bean
    public MilepostTemplateDeliveryStandardsService milepostTemplateDeliveryStandardService() {
        return new MilepostTemplateDeliveryStandardsServiceImpl();
    }

    @Bean
    public ProjectMilepostDeliveryStandardsService projectMilepostDeliveryStandardsService() {
        return new ProjectMilepostDeliveryStandardsServiceImpl();
    }

    @Bean
    public ProjectMilepostGroupService projectMilepostGroupService() {
        return new ProjectMilepostGroupServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetChangeHistoryService projectWbsBudgetChangeHistoryService() {
        return new ProjectWbsBudgetChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetChangeService projectWbsBudgetChangeService() {
        return new ProjectWbsBudgetChangeServiceImpl();
    }

    @Bean
    public RedisUtilServer redisUtilServer() {
        return new RedisUtilServer();
    }

    @Bean
    public CarrierHelper carrierHelper() {
        return new CarrierHelper();
    }

    @Bean
    public ProjectWbsBudgetDynamicChangeHistoryService projectWbsBudgetDynamicChangeHistoryService() {
        return new ProjectWbsBudgetDynamicChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetBaselineChangeHistoryService projectWbsBudgetBaselineChangeHistoryService() {
        return new ProjectWbsBudgetBaselineChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectWbsReceiptsService projectWbsReceiptsService() {
        return new ProjectWbsReceiptsServiceImpl();
    }

    @Bean
    public ProjectWbsReceiptsWorkflowCallBackService projectWbsReceiptsWorkflowCallBackService() {
        return new ProjectWbsReceiptsWorkflowCallBackServiceImpl();
    }

    @Bean
    public ProjectWbsReceiptsBudgetService projectWbsReceiptsBudgetService() {
        return new ProjectWbsReceiptsBudgetServiceImpl();
    }

    @Bean
    public ProjectWbsReceiptsDesignPlanRelService projectWbsReceiptsDesignPlanRelService() {
        return new ProjectWbsReceiptsDesignPlanRelServiceImpl();
    }

    @Bean
    public BasicCacheService basicCacheService() {
        return new BasicCacheServiceImpl();
    }

    @Bean
    public PurchaseContractProgressService purchaseContractProgressService() {
        return new PurchaseContractProgressServiceImpl();
    }

    @Bean
    public PurchaseContractProgressBudgetRelService purchaseContractProgressBudgetRelService() {
        return new PurchaseContractProgressBudgetRelServiceImpl();
    }

    @Bean
    public PurchaseContractQualityReportService purchaseContractQualityReportService() {
        return new PurchaseContractQualityReportServiceImpl();
    }

    @Bean
    public MilepostDesignPlanDetailSubmitHistoryService milepostDesignPlanDetailSubmitHistoryService() {
        return new MilepostDesignPlanDetailSubmitHistoryServiceImpl();
    }

    @Bean
    public PurchaseContractProgressWorkflowCallBackService purchaseContractProgressWorkflowCallBackService() {
        return new PurchaseContractProgressWorkflowCallBackServiceImpl();
    }

    @Bean
    public PurchaseContractBudgetService purchaseContractBudgetService() {
        return new PurchaseContractBudgetServiceImpl();
    }

    @Bean
    public ProjectWbsSubmitReceiptsWorkflowCallBackServiceImpl projectWbsSubmitReceiptsWorkflowCallBackService() {
        return new ProjectWbsSubmitReceiptsWorkflowCallBackServiceImpl();
    }

    @Bean
    public MilepostDesignPlanChangeWorkflowCallbackService milepostDesignPlanChangeWorkflowCallbackService() {
        return new MilepostDesignPlanChangeWorkflowCallbackServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetFeeListener projectWbsBudgetFeeListener() {
        return new ProjectWbsBudgetFeeListener();
    }

    @Bean
    public ProjectWbsBudgetChangeWorkflowCallbackService projectWbsBudgetChangeWorkflowCallbackService() {
        return new ProjectWbsBudgetChangeWorkflowCallbackServiceImpl();
    }

    @Bean
    public PurchaseOrderCallBackService purchaseOrderCallBackService() {
        return new PurchaseOrderCallBackServiceImpl();
    }

    @Bean
    public PurchaseOrderChangeCallBackService purchaseOrderChangeCallBackService() {
        return new PurchaseOrderChangeCallBackServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetBaselineService projectWbsBudgetBaselineService() {
        return new ProjectWbsBudgetBaselineServiceImpl();
    }

    @Bean
    public PreviewWbsBudgetInfoChangeService previewWbsBudgetInfoChangeService() {
        return new PreviewWbsBudgetInfoChangeServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetSummaryChangeHistoryService projectWbsBudgetSummaryChangeHistoryService() {
        return new ProjectWbsBudgetSummaryChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectMilepostChangeHistoryService projectMilepostChangeHistoryService() {
        return new ProjectMilepostChangeHistoryServiceImpl();
    }

    @Bean
    public CustomerTransferService customerTransferService() {
        return new CustomerTransferServiceImpl();
    }

    @Bean
    public CustomerTransferWorkflowCallBackService customerTransferWorkflowCallBackService() {
        return new CustomerTransferWorkflowCallBackServiceImpl();
    }

    @Bean
    public CustomerTransferReverseWorkflowCallbackService customerTransferReverseWorkflowCallBackService() {
        return new CustomerTransferReverseWorkflowCallbackServiceImpl();
    }

    @Bean
    public ProjectBaselineBatchService projectBaselineBatchService() {
        return new ProjectBaselineBatchServiceImpl();
    }

    @Bean(name = "sdpDataOutStrategy.PAM_QSQED")
    public SdpDataOutStrategySpi itemSdpQsqedTopicDataOutStrategySpi() {
        return new ItemSdpQsqedTopicDataOutStrategySpi();
    }

    @Bean(name = "sdpDataOutStrategy.PAM_SR")
    public SdpDataOutStrategySpi itemSdpSrTopicDataOutStrategySpi() {
        return new ItemSdpSrTopicDataOutStrategySpi();
    }

    @Bean(name = "sdpDataOutStrategy.PAM_GERPFA")
    public SdpDataOutStrategySpi itemSdpGERPFaTopicDataOutStrategySpi() {
        return new ItemSdpGERPFaTopicDataOutStrategySpi();
    }

    @Bean(name = "sdpDataOutStrategy.PAM_ASSD")
    public SdpDataOutStrategySpi itemSdpAssdTopicDataOutStrategySpi() {
        return new ItemSdpAssdTopicDataOutStrategySpi();
    }

    @Bean(name = "sdpDataOutStrategy.PAM_PO")
    public SdpDataOutStrategySpi ItemSdpGERPPoTopicDataOutStrategySpi() {
        return new ItemSdpGERPPoTopicDataOutStrategySpi();
    }

    @Bean
    public SdpMessageConsumer sdpMessageConsumer() {
        return new SdpMessageConsumer();
    }

    @Bean
    public ProjectWbsBudgetCheckHelpper projectWbsBudgetCheckHelpper() {
        return new ProjectWbsBudgetCheckHelpperImpl();
    }

    @Bean
    public SupplierQualityDeactivationService supplierQualityDeactivationService() {
        return new SupplierQualityDeactivationServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetSummaryServiceV2 projectWbsBudgetSummaryServiceV2() {
        return new ProjectWbsBudgetSummaryServiceV2Impl();
    }

    @Bean
    public ProjectWbsReceiptsBudgetChangeHistoryService projectWbsReceiptsBudgetChangeHistoryService() {
        return new ProjectWbsReceiptsBudgetChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectContractRsChangeHistoryService projectContractRsChangeHistoryService() {
        return new ProjectContractRsChangeHistoryServiceImpl();
    }

    @Bean
    public ProjectContractChangeWorkflowCallbackService projectContractChangeWorkflowCallbackService() {
        return new ProjectContractChangeWorkflowCallbackServiceImpl();
    }

    @Bean
    public ProjectBaselineBatchContractRsService projectBaselineBatchContractRsService() {
        return new ProjectBaselineBatchContractRsServiceImpl();
    }

    @Bean
    public MaterialAdjustExtService materialAdjustExtService() {
        return new MaterialAdjustExtServiceImpl();
    }

    @Bean
    public ProjectFlexibleImportService projectFlexibleImportService() {
        return new ProjectFlexibleImportServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetFlexibleImportService projectWbsBudgetFlexibleImortService() {
        return new ProjectWbsBudgetFlexibleImportServiceImpl();
    }

    @Bean
    public ProjectContractRsFlexibleImportService projectContractRsFlexibleImportService() {
        return new ProjectContractRsFlexibleImportServiceImpl();
    }

    @Bean
    public ProjectWbsBudgetDynamicService projectWbsBudgetDynamicService() {
        return new ProjectWbsBudgetDynamicServiceImpl();
    }

    @Bean
    public ProjectMilepostFlexibleImportService projectMilepostFlexibleImportService() {
        return new ProjectMilepostFlexibleImportServiceImpl();
    }

    @Bean
    public PurchaseContractStampService purchaseContractStampService() {
        return new PurchaseContractStampServiceImpl();
    }

    @Bean
    public SyncRedInvoiceToMifListener syncRedInvoiceToMifListener() {
        return new SyncRedInvoiceToMifListener();
    }

    @Bean
    public ProjectCustomerSatisfactionService projectCustomerSatisfactionService() {
        return new ProjectCustomerSatisfactionServiceImpl();
    }

    @Bean
    public ProjectSecurityIncidentService projectSecurityIncidentService() {
        return new ProjectSecurityIncidentServiceImpl();
    }

    @Bean
    public EamPurchaseInvokeLogService eamPurchaseInvokeLogService() {
        return new EamPurchaseInvokeLogServiceImpl();
    }

    @Bean
    public EamInvokeService eamInvokeService() {
        return new EamInvokeServiceImpl();
    }

    @Bean
    public EamInvokeHelperService eamInvokeHelperService() {
        return new EamInvokeHelperServiceImpl();
    }

    @Bean
    public ProjectWbsSubmitReceiptsApprovalListener projectWbsSubmitReceiptsApprovalListener() {
        return new ProjectWbsSubmitReceiptsApprovalListener();
    }

    @Bean
    public WorkflowCallbackService workflowCallbackService() {
        return new WorkflowCallbackServiceImpl();
    }

    @Bean
    public MaterialChangeService materialChangeService() {
        return new MaterialChangeServiceImpl();
    }

    @Bean
    public ProjectWbsReceiptsApprovalListener projectWbsReceiptsApprovalListener() {
        return new ProjectWbsReceiptsApprovalListener();
    }

    @Bean
    public MilepostDesignPlanChangeApprovalListener milepostDesignPlanChangeApprovalListener() {
        return new MilepostDesignPlanChangeApprovalListener();
    }

    @Bean
    public MaterialAdjustApprovalListener materialAdjustApprovalListener() {
        return new MaterialAdjustApprovalListener();
    }

    @Bean
    public MilepostDesignPlanNotPublishRequirementService milepostDesignPlanNotPublishRequirementService() {
        return new MilepostDesignPlanNotPublishRequirementServiceImpl();
    }

    @Bean
    public MilepostDesignPlanNotPublishRequirementListener milepostDesignPlanNotPublishRequirementListener() {
        return new MilepostDesignPlanNotPublishRequirementListener();
    }

    @Bean
    public ProjectCallBackService projectCallBackService() {
        return new ProjectCallBackServiceImpl();
    }

    @Bean
    public ProjectBusinessApprovalListener projectBusinessApprovalListener() {
        return new ProjectBusinessApprovalListener();
    }

    @Bean
    public VendorPenaltyRecordService vendorPenaltyRecordService() {
        return new VendorPenaltyRecordServiceImpl();
    }

    @Bean
    public ProjectPreviewCallBackService projectPreviewCallBackService() {
        return new ProjectPreviewCallBackServiceImpl();
    }

    @Bean
    public ProjectPreviewCallBackApprovalListener projectPreviewCallBackApprovalListener() {
        return new ProjectPreviewCallBackApprovalListener();
    }

    @Bean
    public PreProjectCallBackService preProjectCallBackService() {
        return new PreProjectCallBackServiceImpl();
    }

    @Bean
    public ProjectBaseInfoChangeCallBackApprovalListener projectBaseInfoChangeCallBackApprovalListener() {
        return new ProjectBaseInfoChangeCallBackApprovalListener();
    }

    @Bean
    public ProjectBaseInfoChangeCallBackService projectBaseInfoChangeCallBackService() {
        return new ProjectBaseInfoChangeCallBackServiceImpl();
    }

    @Bean
    public ReceiptWorkOrderService receiptWorkOrderService() {
        return new ReceiptWorkOrderServiceImpl();
    }

    @Bean
    public UpdateMaterialCostRequirementListener updateMaterialCostRequirementListener() {
        return new UpdateMaterialCostRequirementListener();
    }

    @Bean
    public ProjectContractChangeWorkflowCallbackApprovalListener projectContractChangeWorkflowCallbackApprovalListener() {
        return new ProjectContractChangeWorkflowCallbackApprovalListener();
    }

    @Bean
    public RedisLuaScriptServer redisLuaScriptServer() {
        return new RedisLuaScriptServer();
    }

    @Bean
    public PurchaseContractWorkflowCallbackApprovalListener purchaseContractWorkflowCallbackApprovalListener() {
        return new PurchaseContractWorkflowCallbackApprovalListener();
    }

    @Bean
    public ContractWorkflowCallbackApprovalListener contractWorkflowCallbackApprovalListener() {
        return new ContractWorkflowCallbackApprovalListener();
    }

    @Bean
    public ContractProductChanageWorkflowCallbackApprovalListener contractProductChanageWorkflowCallbackApprovalListener() {
        return new ContractProductChanageWorkflowCallbackApprovalListener();
    }

    @Bean
    public PurchaseOrderCallBackApprovalListener purchaseOrderCallBackApprovalListener() {
        return new PurchaseOrderCallBackApprovalListener();
    }

    @Bean
    public PurchaseContractProgressWorkflowCallBackApprovalListener purchaseContractProgressWorkflowCallBackApprovalListener() {
        return new PurchaseContractProgressWorkflowCallBackApprovalListener();
    }

    @Bean
    public PaymentApplyWorkflowCallbackApprovalListener paymentApplyWorkflowCallbackApprovalListener() {
        return new PaymentApplyWorkflowCallbackApprovalListener();
    }

    @Bean
    public PaymentApplyWorkflowCallbackAbandonListener paymentApplyWorkflowCallbackAbandonListener() {
        return new PaymentApplyWorkflowCallbackAbandonListener();
    }

    @Bean
    public MaterialGetWorkflowCallbackApprovalListener materialGetWorkflowCallbackApprovalListener() {
        return new MaterialGetWorkflowCallbackApprovalListener();
    }

    @Bean
    public InvoiceApplyCallBackService invoiceApplyCallBackService() {
        return new InvoiceApplyCallBackServiceImpl();
    }

    @Bean
    public InvoiceApplyCallBackApprovalListener invoiceApplyCallBackApprovalListener() {
        return new InvoiceApplyCallBackApprovalListener();
    }

    @Bean
    public RefundApplyWorkflowCallbackService refundApplyWorkflowCallbackService() {
        return new RefundApplyWorkflowCallbackServiceImpl();
    }

    @Bean
    public RefundApplyWorkflowCallbackApprovalListener refundApplyWorkflowCallbackApprovalListener() {
        return new RefundApplyWorkflowCallbackApprovalListener();
    }

    @Bean
    public ProjectTransferQualityWorkflowCallbackApprovalListener projectTransferQualityWorkflowCallbackApprovalListener() {
        return new ProjectTransferQualityWorkflowCallbackApprovalListener();
    }

    @Bean
    public MaterialReturnCallBackService materialReturnCallBackService() {
        return new MaterialReturnCallBackServiceImpl();
    }

    @Bean
    public MaterialReturnCallBackApprovalListener materialReturnCallBackApprovalListener() {
        return new MaterialReturnCallBackApprovalListener();
    }

    @Bean
    public MaterialCostTransferCallBackService materialCostTransferCallBackService() {
        return new MaterialCostTransferCallBackServiceImpl();
    }

    @Bean
    public MaterialCostTransferCallBackApprovalListener materialCostTransferCallBackApprovalListener() {
        return new MaterialCostTransferCallBackApprovalListener();
    }

    @Bean
    public ProjectWbsBudgetChangeWorkflowCallbackApprovalListener projectWbsBudgetChangeWorkflowCallbackApprovalListener() {
        return new ProjectWbsBudgetChangeWorkflowCallbackApprovalListener();
    }

    @Bean
    public PushToErpSyncListener pushToErpSyncListener() {
        return new PushToErpSyncListener();
    }

    @Bean
    public PaymentInvoiceCallBackService paymentInvoiceCallBackService() {
        return new PaymentInvoiceCallBackServiceImpl();
    }

    @Bean
    public PaymentInvoiceCallBackApprovalListener paymentInvoiceCallBackApprovalListener() {
        return new PaymentInvoiceCallBackApprovalListener();
    }

    @Bean
    public CommonRocketMQProducerService commonRocketMQProducerService() {
        return new CommonRocketMQProducerServiceImpl();
    }

    @Bean
    public SubmitHistoryWithRecursiveDetailService submitHistoryWithRecursiveDetailService() {
        return new SubmitHistoryWithRecursiveDetailServiceImpl();
    }

    @Bean
    public SubmitHistoryWithRecursiveDetailRocketMQConsumer submitHistoryWithRecursiveDetailRocketMQConsumer() {
        return new SubmitHistoryWithRecursiveDetailRocketMQConsumer();
    }

    @Bean
    public PurchaseContractDetailChangeCallBackService purchaseContractDetailChangeCallBackService() {
        return new PurchaseContractDetailChangeCallBackImpl();
    }

    @Bean
    public PurchaseContractDetailChangeCallBackApprovalListener purchaseContractDetailChangeCallBackApprovalListener() {
        return new PurchaseContractDetailChangeCallBackApprovalListener();
    }

    @Bean
    public ContractReceiptPlanChanageCallBackService contractReceiptPlanChanageCallBackService() {
        return new ContractReceiptPlanChanageCallBackServiceImpl();
    }

    @Bean
    public ContractReceiptPlanChanageCallBackApprovalListener contractReceiptPlanChanageCallBackApprovalListener() {
        return new ContractReceiptPlanChanageCallBackApprovalListener();
    }

    @Bean
    public ContractBaseInfoChanageCallBackService contractBaseInfoChanageCallBackService() {
        return new ContractBaseInfoChanageCallBackServiceImpl();
    }

    @Bean
    public ContractBaseInfoChanageCallBackApprovalListener contractBaseInfoChanageCallBackApprovalListener() {
        return new ContractBaseInfoChanageCallBackApprovalListener();
    }

    @Bean
    public MilepostDesignPlanChangeSubmitRocketMQConsumer milepostDesignPlanChangeSubmitRocketMQConsumer() {
        return new MilepostDesignPlanChangeSubmitRocketMQConsumer();
    }

    @Bean
    public MilepostDesignPlanDetailChangeRecordService milepostDesignPlanDetailChangeRecordService() {
        return new MilepostDesignPlanDetailChangeRecordServiceImpl();
    }

    @Bean
    public PurchaseOrderChangeCallBackApprovalListener purchaseOrderChangeCallBackApprovalListener() {
        return new PurchaseOrderChangeCallBackApprovalListener();
    }

    @Bean
    public MaterialDelistCallBackApprovalListener materialDelistCallBackApprovalListener() {
        return new MaterialDelistCallBackApprovalListener();
    }

    @Bean
    public MaterialDelistCallBackService materialDelistCallBackService() {
        return new MaterialDelistCallBackServiceImpl();
    }

    @Bean
    public MaterialTransferCallBackService materialTransferCallBackService() {
        return new MaterialTransferCallBackServiceImpl();
    }

    @Bean
    public MaterialTransferCallBackApprovalListener materialTransferCallBackApprovalListener() {
        return new MaterialTransferCallBackApprovalListener();
    }

    @Bean
    public PurchaseContractInfoChangeCallBackService purchaseContractInfoChangeCallBackService() {
        return new PurchaseContractInfoChangeCallBackServiceImpl();
    }

    @Bean
    public PurchaseContractInfoChangeCallBackApprovalListener purchaseContractInfoChangeCallBackApprovalListener() {
        return new PurchaseContractInfoChangeCallBackApprovalListener();
    }

    @Bean
    public ProjectPendingCloseCheckEventListener projectPendingCloseCheckEventListener() {
        return new ProjectPendingCloseCheckEventListener();
    }

    @Bean
    public PurchaseContractPlanChangeCallBackApprovalListener purchaseContractPlanChangeCallBackApprovalListener() {
        return new PurchaseContractPlanChangeCallBackApprovalListener();
    }

    @Bean
    public PurchaseContractPlanChangeCallBackService purchaseContractPlanChangeCallBackService() {
        return new PurchaseContractPlanChangeCallBackServiceImpl();
    }

    @Bean
    public ProjectWbsReceiptsRequirementChangeRecordService projectWbsReceiptsRequirementChangeRecordService() {
        return new ProjectWbsReceiptsRequirementChangeRecordServiceImpl();
    }

    @Bean
    public ProjectWbsReceiptsRequirementChangeRecordRelationService projectWbsReceiptsRequirementChangeRecordRelationService() {
        return new ProjectWbsReceiptsRequirementChangeRecordRelationServiceImpl();
    }

    @Bean
    public ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackService projectWbsReceiptsRequirementChangeRecordWorkflowCallbackService() {
        return new ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackServiceImpl();
    }

    @Bean
    public ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackApprovalListener projectWbsReceiptsRequirementChangeRecordWorkflowCallbackApprovalListener() {
        return new ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackApprovalListener();
    }

    @Bean
    public SdpCallbackService sdpCallbackServiceImpl() {
        return new SdpCallbackServiceImpl();
    }

    @Bean
    public CustomerTransferReverseWorkflowCallbackApprovalListener customerTransferReverseWorkflowCallbackApprovalListener() {
        return new CustomerTransferReverseWorkflowCallbackApprovalListener();
    }

    @Bean
    public VendorPenaltyCallBackService vendorPenaltyCallBackService() {
        return new VendorPenaltyCallBackServiceImpl();
    }

    @Bean
    public ReceiptWorkOrderCallBackService receiptWorkOrderCallBackService() {
        return new ReceiptWorkOrderCallBackServiceImpl();
    }

    @Bean
    public ReceiptWorkOrderCallBackApprovalListener receiptWorkOrderCallBackApprovalListener() {
        return new ReceiptWorkOrderCallBackApprovalListener();
    }

    @Bean
    public CheckBasicInfoService checkBasicInfoService() {
        return new CheckBasicInfoServiceImpl();
    }

    @Bean
    public CheckSceneInfoService checkSceneInfoService() {
        return new CheckSceneInfoServiceImpl();
    }

    @Bean
    public CheckSceneBasicRelService checkSceneBasicRelService() {
        return new CheckSceneBasicRelServiceImpl();
    }

    @Bean
    public CheckResultService checkResultService() {
        return new CheckResultServiceImpl();
    }

    @Bean
    public ContractCheckService contractCheckService() {
        return new ContractCheckServiceImpl();
    }

    @Bean
    public ContractTerminationCallBackService contractTerminationCallBackService() {
        return new ContractTerminationCallBackServiceImpl();
    }

    @Bean
    public GlegalContractService glegalContractService() {
        return new GlegalContractServiceImpl();
    }

    @Bean
    public ContractDrafterChangeCallBackService contractDrafterChangeCallBackService() {
        return new ContractDrafterChangeCallBackServiceImpl();
    }

    @Bean
    public ExchangeAccountService exchangeAccountService() {
        return new ExchangeAccountServiceImpl();
    }

    @Bean
    public ExchangeAccountListener exchangeAccountListener() {
        return new ExchangeAccountListener();
    }

    @Bean
    public PurchaseContractPunishmentService purchaseContractPunishmentService() {
        return new PurchaseContractPunishmentServiceImpl();
    }

    @Bean
    public PurchaseContractPunishmentDetailService purchaseContractPunishmentDetailService() {
        return new PurchaseContractPunishmentDetailServiceImpl();
    }

    @Bean
    public PurchaseContractPunishmentConfigService purchaseContractPunishmentConfigService() {
        return new PurchaseContractPunishmentConfigServiceImpl();
    }

    @Bean
    public PurchaseContractPunishmentWorkflowCallbackService purchaseContractPunishmentWorkflowCallbackService() {
        return new PurchaseContractPunishmentWorkflowCallbackServiceImpl();
    }

    @Bean
    public PurchaseContractPunishmentWorkflowCallbackApprovalListener purchaseContractPunishmentWorkflowCallbackApprovalListener() {
        return new PurchaseContractPunishmentWorkflowCallbackApprovalListener();
    }

    @Bean
    public ProjectAssetChangeWorkflowCallbackService projectAssetChangeWorkflowCallbackService() {
        return new ProjectAssetChangeWorkflowCallbackServiceImpl();
    }

    @Bean
    public ProjectAssetChangeWorkflowCallbackApprovalListener projectAssetChangeWorkflowCallbackApprovalListener() {
        return new ProjectAssetChangeWorkflowCallbackApprovalListener();
    }

    @Bean
    public AssetDeprnAccountingService assetDeprnAccountingService() {
        return new AssetDeprnAccountingServiceImpl();
    }

    @Bean
    public AssetDeprnAccountingListener assetDeprnAccountingListener() {
        return new AssetDeprnAccountingListener();
    }

    @Bean
    public ProjectWbsBaselineChangeCallBackService projectWbsBaselineChangeCallBackService() {
        return new ProjectWbsBaselineChangeCallBackServiceImpl();
    }


    @Bean
    public SdpPurchaseContractService sdpPurchaseContractService(){
        return new SdpPurchaseContractImpl();
    }

    @Bean
    public SdpLogService sdpLogService(){
        return new SdpLogServiceImpl();
    }

    @Bean
    public MethodInvoker methodInvoker(){
       return new MethodInvoker();
    }


    @Bean
    public SdpReceivePaymentApplyService subscribePaymentApplyService(){
        return new SdpReceivePaymentApplyServiceImpl();
    }

    @Bean
    public GSCPaymentAndInvoiceStatusPushService gscPaymentAndInvoiceStatusPushService(){
        return new GSCPaymentAndInvoiceStatusPushServiceImpl();
    }

    @Bean
    public SdpReceivePaymentInvoiceIspRequestService subscribePaymentInvoiceRequestService(){
        return new SdpReceivePaymentInvoiceIspRequestServiceImpl();
    }

    @Bean
    public GSCInvoiceIspWorkflowCallbackService gscInvoiceIspWorkflowCallbackService(){
        return new GSCInvoiceIspWorkflowCallbackServiceImpl();
    }

    @Bean
    public SdpReceivePaymentInvoiceDetailRequestService subscribePaymentInvoiceDetailRequestService(){
        return new SdpReceivePaymentInvoiceDetailRequestServiceImpl();
    }

    @Bean
    public GSCInvoiceDetailWorkflowCallbackService gscInvoiceDetailWorkflowCallbackService(){
        return new GSCInvoiceDetailWorkflowCallbackServiceImpl();
    }

    @Bean
    public GscInvoiceDetailWorkflowCallbackApprovalListener gscInvoiceDetailWorkflowCallbackApprovalListener(){
        return new GscInvoiceDetailWorkflowCallbackApprovalListener();
    }

    @Bean
    public GscInvoiceIspWorkflowCallbackApprovalListener gscInvoiceIspWorkflowCallbackApprovalListener(){
        return new GscInvoiceIspWorkflowCallbackApprovalListener();
    }

    @Bean
    public ProjectBudgetAssetChangeHistoryService projectBudgetAssetChangeHistoryService() {
        return new ProjectBudgetAssetChangeHistoryServiceImpl();
    }

    @Bean
    public InvoiceReceivableReverseCallBackService invoiceReceivableReverseCallBackService() {
        return new InvoiceReceivableReverseCallBackServiceImpl();
    }

    @Bean
    public DeliveryAddressService deliveryAddressService() {
        return new DeliveryAddressServiceImpl();
    }

    @Bean
    public PurchaseMaterialRequirementDeliveryAddressHistoryService purchaseMaterialRequirementDeliveryAddressHistoryService(){
        return new PurchaseMaterialRequirementDeliveryAddressHistoryServiceImpl();
    }
    @Bean
    public GpamHelperService gpamHelperService() {
        return new GpamHelperServiceImpl();
    }
    @Bean
    public GscPaymentInvoiceService gscPaymentInvoiceService() {
        return new GscPaymentInvoiceServiceImpl();
    }

    @Bean
    public IFlowExceptionReBuildService iFlowExceptionReBuildService() {
        return new IFlowExceptionReBuildServiceImpl();
    }

    @Bean
    public PurchaseOrderDetailDeliveryAddressHistoryService PurchaseOrderDetailDeliveryAddressHistoryService(){
        return new PurchaseOrderDetailDeliveryAddressHistoryServiceImpl();
    }

    @Bean
    public PassGcebToPamService passGcebToPamService() {
        return new PassGcebToPamServiceImpl();
    }

    @Bean
    public GcebToPamHelper gcebToPamHelper() {
        return new GcebToPamHelper();
    }

    @Bean
    public SystemContextHelper systemContextHelper() {
        return new SystemContextHelper();
    }

    @Bean
    public StandardTermsService standardTermsService() {
        return new StandardTermsServiceImpl();
    }

    @Bean
    public StandardTermsCallbackService standardTermsCallbackService() {
        return new StandardTermsCallbackServiceImpl();
    }
}
