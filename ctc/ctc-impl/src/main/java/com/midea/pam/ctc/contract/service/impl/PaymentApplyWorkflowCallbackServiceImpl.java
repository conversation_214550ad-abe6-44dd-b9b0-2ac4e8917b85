package com.midea.pam.ctc.contract.service.impl;

import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.enums.AuditStatus;
import com.midea.pam.common.enums.GSCPaymentAndInvoiceStatusPushEnum;
import com.midea.pam.common.enums.PaymentApplySourceNameEnum;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.contract.service.PaymentApplyWorkflowCallbackService;
import com.midea.pam.ctc.service.GSCPaymentAndInvoiceStatusPushService;
import com.midea.pam.ctc.service.event.PaymentApplyWorkflowCallbackAbandonEvent;
import com.midea.pam.ctc.service.event.PaymentApplyWorkflowCallbackApprovalEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2019-8-6
 * @description 付款申请流程回调
 */
public class PaymentApplyWorkflowCallbackServiceImpl extends PaymentApplyWorkflowCallbackService {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//5分钟

    @Resource
    private PaymentApplyService paymentApplyService;
    @Resource
    private WorkflowCallbackService workflowCallbackService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private GSCPaymentAndInvoiceStatusPushService gscPaymentAndInvoiceStatusPushService;

    @Override
    public void pass(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                     Long createUserId, String nodeId, String timestamp) {
        applicationEventPublisher.publishEvent(new PaymentApplyWorkflowCallbackApprovalEvent(this, formInstanceId, fdInstanceId, formUrl,
                eventName, handlerId, companyId, createUserId, nodeId, timestamp));
    }

    @Override
    public void abandon(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                     Long createUserId) {
        applicationEventPublisher.publishEvent(new PaymentApplyWorkflowCallbackAbandonEvent(this, formInstanceId, fdInstanceId, formUrl,
                eventName, handlerId, companyId, createUserId));
    }
    
    @Override
    public void draftSubmit(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                            Long createUserId) {
        logger.info("付款申请审批提交审批回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("PaymentApplyWorkflowCallback_draftSubmit_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final PaymentApply paymentApply = paymentApplyService.findById(formInstanceId);
                    Guard.notNull(paymentApply, "付款申请审批提交审批回调 formInstanceId对应的付款申请不存在，不处理");

                    paymentApply.setAuditStatus(AuditStatus.PENDING.getCode());
                    paymentApplyService.update(paymentApply);
                    logger.info("付款申请审批提交审批回调成功，状态更新为待审批");

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);

                    // 推送GSC付款申请审批状态
                   /* if(Objects.equals(paymentApply.getSourceSystemName(), PaymentApplySourceNameEnum.GSC.getName())){
                        gscPaymentAndInvoiceStatusPushService.pushPaymentAndInvoiceStatus(GSCPaymentAndInvoiceStatusPushEnum.PAYMENT_APPLY_APPROVAL_STATUS_PUSH_SUBMIT, paymentApply.getId());
                    }*/
                } catch (ApplicationBizException e) {
                    logger.info("付款申请审批提交审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("付款申请审批提交审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("付款申请审批提交审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void refuse(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                       Long createUserId) {
        logger.info("付款申请审批驳回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("PaymentApplyWorkflowCallback_refuse_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final PaymentApply paymentApply = paymentApplyService.findById(formInstanceId);
                    Guard.notNull(paymentApply, "付款申请审批驳回回调 formInstanceId对应的付款申请不存在，不处理");

                    paymentApply.setAuditStatus(AuditStatus.REFUSE.getCode());
                    paymentApplyService.update(paymentApply);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);

                    // 推送GSC付款申请审批状态
                    if(Objects.equals(paymentApply.getSourceSystemName(), PaymentApplySourceNameEnum.GSC.getName())){
                        gscPaymentAndInvoiceStatusPushService.pushPaymentAndInvoiceStatus(GSCPaymentAndInvoiceStatusPushEnum.PAYMENT_APPLY_APPROVAL_STATUS_PUSH_REJECT, paymentApply.getId());
                    }
                } catch (ApplicationBizException e) {
                    logger.info("付款申请审批驳回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("付款申请审批驳回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("付款申请审批驳回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void draftReturn(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                            Long createUserId) {
        logger.info("付款申请审批撤回回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("PaymentApplyWorkflowCallback_draftReturn_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    final PaymentApply paymentApply = paymentApplyService.findById(formInstanceId);
                    Guard.notNull(paymentApply, "付款申请审批撤回回调 formInstanceId对应的付款申请不存在，不处理");

                    paymentApply.setAuditStatus(AuditStatus.DRAFT.getCode());
                    paymentApplyService.update(paymentApply);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("付款申请审批撤回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("付款申请审批撤回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("付款申请审批撤回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                               Long createUserId) {
        logger.info("付款申请审批删除回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("PaymentApplyWorkflowCallback_deleteCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    final PaymentApply paymentApply = paymentApplyService.findById(formInstanceId);
                    Guard.notNull(paymentApply, "付款申请审批删除回调 formInstanceId对应的付款申请不存在，不处理");

                    if (paymentApply.getDeletedFlag() == 1) {
                        logger.error("付款申请审批删除回调，formInstanceId对应付款申请已删除，不处理");
                        return;
                    }
                    paymentApply.setDeletedFlag(1);
                    //流程删除时将付款申请的审批状态设置为作废
                    paymentApply.setAuditStatus(AuditStatus.CANCEL.getCode());
                    paymentApplyService.update(paymentApply);

                    if (AuditStatus.CANCEL.getCode() != paymentApply.getAuditStatus()) {
                        //释放发票行/发票头
                        paymentApplyService.cancelApply(paymentApply);
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("付款申请审批删除回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("付款申请审批删除回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("付款申请审批删除回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }
}
