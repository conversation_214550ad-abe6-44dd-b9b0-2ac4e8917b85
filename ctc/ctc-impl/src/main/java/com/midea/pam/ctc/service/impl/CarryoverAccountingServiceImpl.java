package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.CoaSubject;
import com.midea.pam.common.basedata.entity.ProjectProductMaintenance;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDetailDto;
import com.midea.pam.common.ctc.dto.CarryoverBillCostCollectionRelDto;
import com.midea.pam.common.ctc.dto.CarryoverBillDto;
import com.midea.pam.common.ctc.dto.CarryoverCostAccountingDto;
import com.midea.pam.common.ctc.dto.CarryoverIncomeAccountingDTO;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.dto.PurchaseContractDTO;
import com.midea.pam.common.ctc.entity.AgencySynInfo;
import com.midea.pam.common.ctc.entity.AgencySynInfoExample;
import com.midea.pam.common.ctc.entity.BusiSceneNonSale;
import com.midea.pam.common.ctc.entity.BusiSceneNonSaleExample;
import com.midea.pam.common.ctc.entity.CarryoverBill;
import com.midea.pam.common.ctc.entity.CarryoverBillIncomeCollectionDetail;
import com.midea.pam.common.ctc.entity.CarryoverBillIncomeCollectionDetailExample;
import com.midea.pam.common.ctc.entity.CarryoverCostAccounting;
import com.midea.pam.common.ctc.entity.CarryoverCostAccountingExample;
import com.midea.pam.common.ctc.entity.CarryoverIncomeAccounting;
import com.midea.pam.common.ctc.entity.CarryoverIncomeAccountingExample;
import com.midea.pam.common.ctc.entity.CostElement;
import com.midea.pam.common.ctc.entity.CostElementExample;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.OrganizationCustomDictExample;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentInvoiceExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.RevenueCostOrderDetail;
import com.midea.pam.common.ctc.entity.RevenueCostOrderDetailExample;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.CarryoverBillResourceType;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CostMethod;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.IncomePoint;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PaymentInvoiceStatusEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.ObjUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.CommonErpStatus;
import com.midea.pam.ctc.common.enums.CostAccountingElementEnum;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.AgencySynInfoMapper;
import com.midea.pam.ctc.mapper.BusiSceneNonSaleMapper;
import com.midea.pam.ctc.mapper.CarryoverBillIncomeCollectionDetailMapper;
import com.midea.pam.ctc.mapper.CarryoverBillMapper;
import com.midea.pam.ctc.mapper.CarryoverCostAccountingMapper;
import com.midea.pam.ctc.mapper.CarryoverIncomeAccountingMapper;
import com.midea.pam.ctc.mapper.CostElementMapper;
import com.midea.pam.ctc.mapper.CostRatioConfigDetailExtMapper;
import com.midea.pam.ctc.mapper.OrganizationCustomDictMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.RevenueCostOrderDetailMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.BusiSceneNonSaleService;
import com.midea.pam.ctc.service.CarryoverAccountingService;
import com.midea.pam.ctc.service.CarryoverBillCostCollectionRelService;
import com.midea.pam.ctc.service.CostCollectionService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.framework.core.exception.Guard;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * @program: pam
 * @description: CarryoverAccountingServiceImpl
 * @author: gaojh1
 * @create: 2019-8-15 10:51
 **/
public class CarryoverAccountingServiceImpl implements CarryoverAccountingService {

    @Resource
    OrganizationCustomDictService organizationCustomDictService;
    @Resource
    BasedataExtService basedataExtService;
    @Resource
    private CostCollectionService costCollectionService;
    @Resource
    private CarryoverBillCostCollectionRelService carryoverBillCostCollectionRelService;
    @Resource
    private BusiSceneNonSaleMapper busiSceneNonSaleMapper;
    @Resource
    BusiSceneNonSaleService busiSceneNonSaleService;
    @Resource
    CarryoverIncomeAccountingMapper incomeAccountingMapper;
    @Resource
    ProjectTypeMapper projectTypeMapper;
    @Resource
    ProjectMilepostMapper projectMilepostMapper;
    @Resource
    CarryoverCostAccountingMapper costAccountingMapper;
    @Resource
    CarryoverBillMapper carryoverBillMapper;
    @Resource
    CarryoverBillIncomeCollectionDetailMapper carryoverBillIncomeCollectionDetailMapper;
    @Resource
    private RevenueCostOrderDetailMapper revenueCostOrderDetailMapper;
    @Resource
    private ProjectIncomeCostPlanMapper projectIncomeCostPlanMapper;
    @Resource
    private AgencySynInfoMapper agencySynInfoMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OrganizationCustomDictMapper organizationCustomDictMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private CostElementMapper costElementMapper;
    @Resource
    private PurchaseContractService purchaseContractService;
    @Resource
    private CostRatioConfigDetailExtMapper costRatioConfigDetailExtMapper;
    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;


    @Override
    public List<CarryoverIncomeAccounting> incomeAccountingList(CarryoverIncomeAccounting query) {
        CarryoverIncomeAccountingExample example = new CarryoverIncomeAccountingExample();
        example.createCriteria().andCarryoverBillIdEqualTo(query.getCarryoverBillId()).andDeletedFlagEqualTo(false);
        List<CarryoverIncomeAccounting> incomeAccountingList = incomeAccountingMapper.selectByExample(example);
        return incomeAccountingList;
    }

    @Override
    public List<CarryoverIncomeAccountingDTO> queryIncomeAccounting(CarryoverIncomeAccounting query) {
        CarryoverBill carryoverBill = carryoverBillMapper.selectByPrimaryKey(query.getCarryoverBillId());
        Guard.notNull(carryoverBill, ErrorCode.CTC_CARRYOVER_BILL_ID_NOT_NULL.getMsg());
        CarryoverIncomeAccountingExample example = new CarryoverIncomeAccountingExample();
        example.createCriteria().andCarryoverBillIdEqualTo(query.getCarryoverBillId()).andDeletedFlagEqualTo(false);
        List<CarryoverIncomeAccounting> incomeAccountingList = incomeAccountingMapper.selectByExample(example);
        if (ListUtils.isEmpty(incomeAccountingList)) {
            return null;
        }
        List<CarryoverIncomeAccountingDTO> incomeAccountingDTOList = new ArrayList<>();
        Map<Long, List<CarryoverIncomeAccounting>> incomeAccountingMap =
                incomeAccountingList.stream().collect(Collectors.groupingBy(CarryoverIncomeAccounting::getCustomerId));
        for (List<CarryoverIncomeAccounting> invoiceRelDtoList : incomeAccountingMap.values()) {
            CarryoverIncomeAccounting incomeAccounting = invoiceRelDtoList.get(0);
            BigDecimal incomeAmount = incomeAccounting.getIncomeAmount() != null ? incomeAccounting.getIncomeAmount() : BigDecimal.ZERO;
            // 取收入确认金额，如果是预开票转出则取负值
            if (Objects.equals(incomeAccounting.getType(), 1)) {
                incomeAmount = incomeAmount.multiply(new BigDecimal(-1));
            }
            CarryoverIncomeAccountingDTO incomeAccountingDTO = new CarryoverIncomeAccountingDTO();
            CustomerDto customerDto = CacheDataUtils.findCustomerById(incomeAccounting.getCustomerId());
            if (customerDto != null) {
                incomeAccountingDTO.setCustomerName(customerDto.getName());
            }
            incomeAccountingDTO.setLocalCurrency(carryoverBill.getLocalCurrency());
            incomeAccountingDTO.setConversionRate(carryoverBill.getConversionRate());
            incomeAccountingDTO.setBillNum(incomeAccounting.getBillNum());
            incomeAccountingDTO.setCustomerCode(incomeAccounting.getCustomerCode());
            incomeAccountingDTO.setIncomeAmount(incomeAmount);
            incomeAccountingDTO.setErpStatus(incomeAccounting.getErpStatus());
            incomeAccountingDTO.setErpMessage(incomeAccounting.getErpMessage());
            incomeAccountingDTO.setIncomeAccountingList(invoiceRelDtoList);
            incomeAccountingDTOList.add(incomeAccountingDTO);
        }

        return incomeAccountingDTOList;
    }

    @Override
    public List<CarryoverCostAccounting> costAccountingList(CarryoverCostAccounting query) {
        CarryoverCostAccountingExample example = new CarryoverCostAccountingExample();
        example.createCriteria().andCarryoverBillIdEqualTo(query.getCarryoverBillId()).andDeletedFlagEqualTo(false);
        List<CarryoverCostAccounting> costAccountingList = costAccountingMapper.selectByExample(example);
        return costAccountingList;
    }

    @Override
    public List<CarryoverCostAccountingDto> costAccountingListExt(CarryoverCostAccounting query) {
        CarryoverCostAccountingExample example = new CarryoverCostAccountingExample();
        example.createCriteria().andCarryoverBillIdEqualTo(query.getCarryoverBillId()).andDeletedFlagEqualTo(false);
        List<CarryoverCostAccountingDto> costAccountingDtoList = BeanConverter.copy(costAccountingMapper.selectByExample(example), CarryoverCostAccountingDto.class);

        CarryoverBill carryoverBill = carryoverBillMapper.selectByPrimaryKey(query.getCarryoverBillId());
        //查询本位币
        String localCurrency = basedataExtService.queryLocalCurrencyByOuId(carryoverBill.getOuId());
        for (CarryoverCostAccountingDto dto : costAccountingDtoList) {
            dto.setStandardCurrency(localCurrency);
        }
        return costAccountingDtoList;
    }

    /**
     * 记账保存
     *
     * @param carryoverBillDto
     */
    @Override
    public void save(CarryoverBillDto carryoverBillDto) {
        ProjectType type = projectTypeMapper.selectByPrimaryKey(carryoverBillDto.getProjectType());
        //获取结转项目对应业务分类的产品段信息
        String flexValue = "";
        Project p = projectMapper.selectByPrimaryKey(carryoverBillDto.getProjectId());
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", p.getUnitId());
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "unit/selectCoaSubjectByUnitId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<CoaSubject> coaSubjectDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<CoaSubject>>() {
        });
        if (null != coaSubjectDataResponse.getData()) {
            flexValue = coaSubjectDataResponse.getData().getFlexValue();
            if (null == flexValue) {
                throw new BizException(ErrorCode.ERROR, "【" + p.getCode() + "】" + "对应的业务分类的组织关系的产品段字段未维护，请先维护!");
            }
        }
        //收入记账、成本记账都用，因此单独提出来 end

        //收入记账保存(记录结转单客户列表)
        this.saveIncomeAccounting(carryoverBillDto, type, flexValue);
        //成本记账保存
        carryoverBillDto.setCostCollectionDtos(this.getCostCollection(carryoverBillDto.getId()));
        this.saveCostAccounting(carryoverBillDto, type, flexValue);
    }


    /**
     * 收入记账保存
     *
     * @param carryoverBillDto
     */
    private void saveIncomeAccounting(CarryoverBillDto carryoverBillDto, ProjectType type, String flexValue) {
        CarryoverBillIncomeCollectionDetailExample incomeCollectionDetailExample = new CarryoverBillIncomeCollectionDetailExample();
        incomeCollectionDetailExample.createCriteria().andCarryoverBillIdEqualTo(carryoverBillDto.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<CarryoverBillIncomeCollectionDetail> incomeCollectionDetailList = carryoverBillIncomeCollectionDetailMapper.selectByExample(incomeCollectionDetailExample);
        if (ListUtils.isEmpty(incomeCollectionDetailList)) {
            return;
        }
        // 结转单客户id列表,用于生成结转单编号
        List<Long> customerIdList = incomeCollectionDetailList.stream().map(o -> o.getCustomerId()).distinct().collect(Collectors.toList());
        // 结转单客户列表
        List<CustomerDto> customerDtoList = new ArrayList<>();
        for (CarryoverBillIncomeCollectionDetail incomeCollectionDetail : incomeCollectionDetailList) {
            Long customerId = incomeCollectionDetail.getCustomerId();
            CustomerDto customerDto = CacheDataUtils.findCustomerById(customerId);
            customerDtoList.add(customerDto);

            if (incomeCollectionDetail.getCurrentIncomeAmount() == null
                    || BigDecimalUtils.isEquals(incomeCollectionDetail.getCurrentIncomeAmount(), BigDecimal.ZERO)) {
                continue;
            }

            String billNum = carryoverBillDto.getBillNum();
            if (customerIdList.size() > 1) {
                // 如果结转单只有多个客户,结转单编号需要拼接序号
                Integer customerIndex = customerIdList.indexOf(customerId) + 1;
                billNum = carryoverBillDto.getBillNum() + "-C" + customerIndex;
            }

            //预开票转出
            CarryoverIncomeAccounting invoiceForwardAccounting = new CarryoverIncomeAccounting();
            invoiceForwardAccounting.setType(1);
            invoiceForwardAccounting.setBillNum(billNum);
            invoiceForwardAccounting.setMemo("预开票转出");
            invoiceForwardAccounting.setOuId(carryoverBillDto.getOuId());
            invoiceForwardAccounting.setProjectTypeId(carryoverBillDto.getProjectId());
            invoiceForwardAccounting.setIncomeSubject(this.getSubjectFromCustTrxType(carryoverBillDto, customerDto, type, flexValue, 1, null));//科目
            invoiceForwardAccounting.setCustTrxTypeName(this.getCustTrxTypeName(invoiceForwardAccounting));//应收事务处理名称
            invoiceForwardAccounting.setSubjectDescription(this.getCoaSubjectDescription(invoiceForwardAccounting.getIncomeSubject()));//科目说明
            invoiceForwardAccounting.setCarryoverBillId(carryoverBillDto.getId());
            invoiceForwardAccounting.setCustomerId(incomeCollectionDetail.getCustomerId());
            if (!ObjectUtils.isEmpty(customerDto)) {
                invoiceForwardAccounting.setCustomerCode(customerDto.getCrmCode());//crm编码
            }
            //四舍五入
            BigDecimal roundAmount = incomeCollectionDetail.getCurrentIncomeAmount().setScale(2, RoundingMode.HALF_UP);
            invoiceForwardAccounting.setIncomeAmount(roundAmount.multiply(new BigDecimal(-1)));
            invoiceForwardAccounting.setGlDate(this.getGlDate(carryoverBillDto.getPeriodName()));//总账日期
            invoiceForwardAccounting.setInvoiceDate(DateUtils.getShortDate(new Date()));//发票日期
            invoiceForwardAccounting.setCurrency("CNY");
            invoiceForwardAccounting.setErpStatus(CommonErpStatus.PUSHING.code());
            invoiceForwardAccounting.setDeletedFlag(Boolean.FALSE);
            incomeAccountingMapper.insert(invoiceForwardAccounting);

            //收入确认
            CarryoverIncomeAccounting incomeAccounting = new CarryoverIncomeAccounting();
            incomeAccounting.setType(2);
            incomeAccounting.setBillNum(billNum);
            incomeAccounting.setMemo("收入确认");
            incomeAccounting.setCarryoverBillId(carryoverBillDto.getId());
            incomeAccounting.setCustomerId(incomeCollectionDetail.getCustomerId());
            if (!ObjectUtils.isEmpty(customerDto)) {
                incomeAccounting.setCustomerCode(customerDto.getCrmCode());//crm编码
            }
            incomeAccounting.setOuId(carryoverBillDto.getOuId());
            incomeAccounting.setProjectTypeId(carryoverBillDto.getProjectId());
            //incomeAccounting.setIncomeSubject(this.getSubjectFromProjectType(carryoverBillDto, customerDto, carryoverBillDto.getResourceType()));//科目
            incomeAccounting.setIncomeSubject(this.getSubjectFromCustTrxType(carryoverBillDto, customerDto, type, flexValue, 2, null));//科目
            incomeAccounting.setCustTrxTypeName(invoiceForwardAccounting.getCustTrxTypeName());//应收事务处理类型名
            incomeAccounting.setSubjectDescription(this.getCoaSubjectDescription(incomeAccounting.getIncomeSubject()));//科目说明
            incomeAccounting.setIncomeAmount(roundAmount);
            incomeAccounting.setGlDate(this.getGlDate(carryoverBillDto.getPeriodName()));//总账日期
            incomeAccounting.setInvoiceDate(DateUtils.getShortDate(new Date()));//发票日期
            incomeAccounting.setCurrency("CNY");
            incomeAccounting.setErpStatus(CommonErpStatus.PUSHING.code());
            incomeAccounting.setDeletedFlag(Boolean.FALSE);
            incomeAccountingMapper.insert(incomeAccounting);
        }
        carryoverBillDto.setCustomerDtoList(customerDtoList);
        //PAM-ERP-018 应收发票写入
        HandleDispatcher.route(BusinessTypeEnums.CARRYOVER_INCOME.getCode(), String.valueOf(carryoverBillDto.getId()), null, null, true, null, carryoverBillDto.getOuId());
    }

    /**
     * 成本记账保存
     *
     * @param carryoverBillDto
     */
    private void saveCostAccounting(CarryoverBillDto carryoverBillDto, ProjectType type, String flexValue) {
        //收入记账、成本记账都用，因此单独提出来 begin
        List<CustomerDto> customerDtoList = carryoverBillDto.getCustomerDtoList();
        if (ListUtils.isEmpty(customerDtoList)) {
            return;
        }
        //todo 由于不能关联多个内部客户，这里暂且默认取第一个项目关联客户
        CustomerDto customerDto = customerDtoList.get(0);
        List<CostCollectionDto> costCollectionDtoList = carryoverBillDto.getCostCollectionDtos();
        BigDecimal innerLaborCost = BigDecimal.ZERO;
        BigDecimal outerLaborCost = BigDecimal.ZERO;
        BigDecimal materialActualCost = BigDecimal.ZERO;
        BigDecimal materialPenaltyCost = BigDecimal.ZERO;
        BigDecimal materialOutsourceCost = carryoverBillDto.getCurrentCostOutsourcingCost();
        BigDecimal adjustmentCost = carryoverBillDto.getCurrentIncomeAdjustment();
        BigDecimal feeCost = BigDecimal.ZERO;
        BigDecimal differenceCost = BigDecimal.ZERO;
        BigDecimal assetDeprnCost = BigDecimal.ZERO;
        Boolean isPushToErp = false;
        BigDecimal detailMaterialCost = BigDecimal.ZERO;
        //归集成本数据汇总
        for (CostCollectionDto collectionDto : costCollectionDtoList) {
            innerLaborCost = innerLaborCost.add(BigDecimalUtils.defaultZero(collectionDto.getInnerLaborCost()));
            outerLaborCost = outerLaborCost.add(BigDecimalUtils.defaultZero(collectionDto.getOuterLaborCost()));
            materialActualCost = materialActualCost.add(BigDecimalUtils.defaultZero(collectionDto.getMaterialActualCost()));
            materialPenaltyCost = materialPenaltyCost.add(BigDecimalUtils.defaultZero(collectionDto.getMaterialPenaltyCost()));
            differenceCost = differenceCost.add(BigDecimalUtils.defaultZero(collectionDto.getMaterialDifferenceCost()));
            feeCost = feeCost.add(BigDecimalUtils.defaultZero(collectionDto.getFeeCost()));
            assetDeprnCost = assetDeprnCost.add(BigDecimalUtils.defaultZero(collectionDto.getAssetDeprnCost()));
        }
        //手工工单成本数据汇总
        List<RevenueCostOrderDetail> orderDetailList = this.getRevenueCostOrderDetail(carryoverBillDto.getId());
        for (RevenueCostOrderDetail orderDetail : orderDetailList) {
            if (CostMethod.FAT_INCOME.getCode().equals(carryoverBillDto.getCostMethod()) && IncomePoint.MILEPOST.getCode().equals(carryoverBillDto.getIncomePoint())) {
                BigDecimal orderDetailMaterialCost = Optional.ofNullable(orderDetail.getMaterialCost()).orElse(BigDecimal.ZERO);
                detailMaterialCost = detailMaterialCost.add(orderDetailMaterialCost);
                //materialActualCost = detailMaterialCost;
            } else {
                if (PublicUtil.isNotEmpty(orderDetail.getInnerLaborCost()))
                    innerLaborCost = innerLaborCost.add(orderDetail.getInnerLaborCost());
                if (PublicUtil.isNotEmpty(orderDetail.getOuterLaborCost()))
                    outerLaborCost = outerLaborCost.add(orderDetail.getOuterLaborCost());
                if (PublicUtil.isNotEmpty(orderDetail.getMaterialCost()))
                    materialActualCost = materialActualCost.add(orderDetail.getMaterialCost());
                // 工单费用区分差旅/非差旅
                if (PublicUtil.isNotEmpty(orderDetail.getFeeCost())) feeCost = feeCost.add(orderDetail.getFeeCost());
                if (PublicUtil.isNotEmpty(orderDetail.getOtherCost()))
                    feeCost = feeCost.add(orderDetail.getOtherCost());
            }
        }
        materialActualCost = materialActualCost.add(detailMaterialCost);

        //非销售业务场景-GL成本结转
        List<BusiSceneNonSaleDetailDto> detailDtoList = this.getBusiSceneDetailList(carryoverBillDto.getOuId());

        //物料记账
        if (materialActualCost != null && !BigDecimalUtils.isEquals(materialActualCost, BigDecimal.ZERO)) {
            String costElement = CostAccountingElementEnum.JZ_A1.code();
            String lineRemark = CostAccountingElementEnum.JZ_A1.msg();
            //debit
            CarryoverCostAccounting materialCostDebit =
                    this.formatCostAccounting(carryoverBillDto, materialActualCost, costElement, lineRemark, true, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(materialCostDebit);
            //credit
            CarryoverCostAccounting materialCostCredit =
                    this.formatCostAccounting(carryoverBillDto, materialActualCost, costElement, lineRemark, false, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(materialCostCredit);
            isPushToErp = true;
        }

/*        // 按发票入账的外包物料不含税金额
        if (null == materialOutsourceCost || BigDecimalUtils.isEquals(materialOutsourceCost, BigDecimal.ZERO)) {
            materialCost = materialCost.add(getMaterialCost(carryoverBillDto,type));
        }*/

        //材料罚扣记账
        if (materialPenaltyCost != null && !BigDecimalUtils.isEquals(materialPenaltyCost, BigDecimal.ZERO)) {
            String costElement = CostAccountingElementEnum.JZ_A7.code();
            String lineRemark = CostAccountingElementEnum.JZ_A7.msg();
            //debit
            CarryoverCostAccounting materialPenaltyDebit =
                    this.formatCostAccounting(carryoverBillDto, materialPenaltyCost, costElement, lineRemark, true, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(materialPenaltyDebit);
            //credit
            CarryoverCostAccounting materialPenaltyCredit =
                    this.formatCostAccounting(carryoverBillDto, materialPenaltyCost, costElement, lineRemark, false, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(materialPenaltyCredit);
            isPushToErp = true;
        }

        //物料外包记账
        if (materialOutsourceCost != null
                && !BigDecimalUtils.isEquals(materialOutsourceCost, BigDecimal.ZERO)) {
            String costElement = CostAccountingElementEnum.JZ_A2.code();
            String lineRemark = CostAccountingElementEnum.JZ_A2.msg();
            //debit
            CarryoverCostAccounting materialOutsourceCostDebit =
                    this.formatCostAccounting(carryoverBillDto, materialOutsourceCost, costElement, lineRemark, true, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(materialOutsourceCostDebit);
            //credit
            CarryoverCostAccounting materialOutsourceCostCredit =
                    this.formatCostAccounting(carryoverBillDto, materialOutsourceCost, costElement, lineRemark, false, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(materialOutsourceCostCredit);
            isPushToErp = true;

        }

        // 历史追溯收入调整逻辑及数据调整,本期差异分摊成本"只取material_difference_cost汇总  不用包含CurrentIncomeAdjustment
//        adjustmentCost = adjustmentCost.add(differenceCost);

        //物料价差记账
        if (differenceCost != null && !BigDecimalUtils.isEquals(differenceCost, BigDecimal.ZERO)) {
            String costElement = CostAccountingElementEnum.JZ_A3.code();
            String lineRemark = CostAccountingElementEnum.JZ_A3.msg();
            //debit
            CarryoverCostAccounting adjustmentCostDebit =
                    this.formatCostAccounting(carryoverBillDto, differenceCost, costElement, lineRemark, true, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(adjustmentCostDebit);
            //credit
            CarryoverCostAccounting adjustmentCostCredit =
                    this.formatCostAccounting(carryoverBillDto, differenceCost, costElement, lineRemark, false, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(adjustmentCostCredit);
            isPushToErp = true;
        }

        //人工内部记账
        if (innerLaborCost != null && !BigDecimalUtils.isEquals(innerLaborCost, BigDecimal.ZERO)) {
            String costElement = CostAccountingElementEnum.GS_A4.code();
            String lineRemark = CostAccountingElementEnum.GS_A4.msg();
            //debit
            CarryoverCostAccounting innerLaborCostDebit =
                    this.formatCostAccounting(carryoverBillDto, innerLaborCost, costElement, lineRemark, true, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(innerLaborCostDebit);
            //credit
            CarryoverCostAccounting innerLaborCostCredit =
                    this.formatCostAccounting(carryoverBillDto, innerLaborCost, costElement, lineRemark, false, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(innerLaborCostCredit);
            isPushToErp = true;
        }

        //人工外部记账
        if (outerLaborCost != null && !BigDecimalUtils.isEquals(outerLaborCost, BigDecimal.ZERO)) {
            String costElement = CostAccountingElementEnum.GS_A5.code();
            String lineRemark = CostAccountingElementEnum.GS_A5.msg();
            //debit
            CarryoverCostAccounting outerLaborCostDebit =
                    this.formatCostAccounting(carryoverBillDto, outerLaborCost, costElement, lineRemark, true, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(outerLaborCostDebit);
            //credit
            CarryoverCostAccounting outerLaborCostCredit =
                    this.formatCostAccounting(carryoverBillDto, outerLaborCost, costElement, lineRemark, false, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(outerLaborCostCredit);
            isPushToErp = true;
        }

        //费用记账
        if (feeCost != null && !BigDecimalUtils.isEquals(feeCost, BigDecimal.ZERO)) {
            String costElement = CostAccountingElementEnum.JZ_A6.code();
            String lineRemark = CostAccountingElementEnum.JZ_A6.msg();
            //debit
            CarryoverCostAccounting feeCostDebit =
                    this.formatCostAccounting(carryoverBillDto, feeCost, costElement, lineRemark, true, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(feeCostDebit);
            //credit
            CarryoverCostAccounting feeCostCredit =
                    this.formatCostAccounting(carryoverBillDto, feeCost, costElement, lineRemark, false, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(feeCostCredit);
            isPushToErp = true;
        }

        //资产折旧记账
        if (assetDeprnCost != null && !BigDecimalUtils.isEquals(assetDeprnCost, BigDecimal.ZERO)) {
            String costElement = CostAccountingElementEnum.JZ_A8.code();
            String lineRemark = CostAccountingElementEnum.JZ_A8.msg();
            //debit
            CarryoverCostAccounting assetDeprnCostDebit =
                    this.formatCostAccounting(carryoverBillDto, assetDeprnCost, costElement, lineRemark, true, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(assetDeprnCostDebit);
            //credit
            CarryoverCostAccounting assetDeprnCostCredit =
                    this.formatCostAccounting(carryoverBillDto, assetDeprnCost, costElement, lineRemark, false, detailDtoList, customerDto, type, flexValue);
            costAccountingMapper.insert(assetDeprnCostCredit);
            isPushToErp = true;
        }

        //PAM-ERP-029 总账日记账写入
        if (isPushToErp) {
            HandleDispatcher.route(BusinessTypeEnums.CARRYOVER_COST.getCode(), String.valueOf(carryoverBillDto.getId()), null, null, true);
        }
    }

    private BigDecimal getMaterialCost(CarryoverBillDto carryoverBillDto, ProjectType type) {
        String organizationCustomDictName = "采购合同类型";//由于数据字典中,采购合同类型对应的code值(code=2)不固定,无法通过灵活读取
        List<String> typeNames = new ArrayList<>();
        Set<String> typeNameSet = organizationCustomDictService.queryByName(organizationCustomDictName, type.getUnitId(), OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.COMPANY.code()));
        if (!CollectionUtils.isEmpty(typeNameSet)) {
            typeNames.addAll(typeNameSet);
        }
        List<PurchaseContractDTO> purchaseContractDTOS = purchaseContractService.findPurchaseContractListByProjectAndTypeNames(carryoverBillDto.getProjectId(), typeNames, true);
        List<PaymentInvoice> payments = new ArrayList<>();
        if (ListUtils.isNotEmpty(purchaseContractDTOS)) {
            purchaseContractDTOS
                    .stream()
                    .filter(s ->
                            ObjUtils.ofNullable(costRatioConfigDetailExtMapper.findDetail2(s.getId(), type.getUnitId()))
                                    .accept(i -> i != null && "02".equals(i.getCostCarryForwardMethod()))
                                    .get()
                    ).forEach(t -> {
                        PaymentInvoiceExample paymentInvoiceExample = new PaymentInvoiceExample();
                        paymentInvoiceExample.createCriteria()
                                .andPurchaseContractIdEqualTo(t.getId())
                                .andStatusEqualTo(PaymentInvoiceStatusEnum.PASS.getCode())
                                .andErpStatusEqualTo(1)
                                .andCollectionStatusEqualTo(1)
                                .andDeletedFlagEqualTo(false);
                        List<PaymentInvoice> paymentInvoices = paymentInvoiceMapper.selectByExample(paymentInvoiceExample);
                        //更新发票为已归集
                        paymentInvoices.forEach(s -> {
                            s.setCollectionStatus(1);
                            paymentInvoiceMapper.updateByPrimaryKey(s);
                        });
                        payments.addAll(paymentInvoices);
                    });
        }

        BigDecimal total = payments.stream()
                .map(PaymentInvoice::getTotalInvoiceIncludedPrice)
                .reduce(BigDecimal.ZERO, BigDecimalUtils::add);

        BigDecimal totalTaxAmount = payments.stream()
                .map(PaymentInvoice::getTaxAmount)
                .reduce(BigDecimal.ZERO, BigDecimalUtils::add);

        total = total.subtract(totalTaxAmount);
        return total;

    }

    /**
     * 成本归集列表
     *
     * @param carryoverBillId
     * @return
     */
    private List<CostCollectionDto> getCostCollection(Long carryoverBillId) {
        CarryoverBillCostCollectionRelDto costCollectionRelQuery = new CarryoverBillCostCollectionRelDto();
        costCollectionRelQuery.setCarryoverBillId(carryoverBillId);
        List<CarryoverBillCostCollectionRelDto> relDtoList = carryoverBillCostCollectionRelService.selectList(costCollectionRelQuery);
        if (ListUtils.isNotEmpty(relDtoList)) {
            List<Long> costCollectionIds = ListUtil.map(relDtoList, "costCollectionId");
            CostCollectionDto costCollectionQuery = new CostCollectionDto();
            costCollectionQuery.setIds(costCollectionIds);
            return costCollectionService.list(costCollectionQuery);
        }
        return new ArrayList<CostCollectionDto>();
    }

    private List<RevenueCostOrderDetail> getRevenueCostOrderDetail(Long carryoverBillId) {
        RevenueCostOrderDetailExample example = new RevenueCostOrderDetailExample();
        example.createCriteria().andCarryoverBillIdEqualTo(carryoverBillId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<RevenueCostOrderDetail> costOrderDetailList = revenueCostOrderDetailMapper.selectByExample(example);
        return costOrderDetailList;
    }


    /**
     * 取应收事务处理类型的收入科目
     *
     * @param carryoverBillDto
     * @return
     */
    @Override
    public String getSubjectFromCustTrxType(CarryoverBillDto carryoverBillDto, CustomerDto customerDto, ProjectType type, String flexValue, Integer subjectType, String costElement) {
/*        String trxTypeName = "";
        //从组织参数配置取应收事务处理类型名
        Set<String> valueSet = organizationCustomDictService.queryByName("开票应收类型", invoiceForwardAccounting.getOuId(),
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
        if (CollectionUtils.isEmpty(valueSet)) {
            return null;
        } else {
            trxTypeName = valueSet.iterator().next();
        }
        //取应收事务处理类型的收入科目
        List<CustTrxType> custTrxTypeList = basedataExtService.getCustTrxType(trxTypeName, "Invoice", invoiceForwardAccounting.getOuId());
        if (CollectionUtils.isEmpty(custTrxTypeList)) {
            return null;
        }

        String subject = custTrxTypeList.get(0).getGlRevConc();
        String[] subjectArray = subject.split("\\.");
        //如果“客户类型”是内部客户，并且CRM客户编码为“E0”开头则取CRM编码的后6位数替换原来获取到的收入科目的第六段内容
        String crmCodeLastSixNum = this.getCrmCodeLastSixNum(customerDto);
        if (StringUtils.isNotEmpty(crmCodeLastSixNum)) {
            subjectArray[5] = crmCodeLastSixNum;
        }

        subject = ArrayUtils.toString(subjectArray);
        subject = subject.substring(1, subject.length() - 1);//去除{}
        subject = subject.replace(",", ".");//逗号替换回点号*/

        //6.15-6.25新增需求
        StringBuilder subject = new StringBuilder();
        String subjectConfig = type.getSubjectConfig();
        if (null != subjectConfig) {
            JSONObject object = JSON.parseObject(subjectConfig);
            //获取收入科目-借方
            JSONObject incomeDebit = JSON.parseObject(object.get("incomeDebit").toString());
            //获取收入科目-贷方
            JSONObject incomeLoan = JSON.parseObject(object.get("incomeLoan").toString());
            //获取成本科目-借方
            JSONObject costDebit = JSON.parseObject(object.get("costDebit").toString());
            //获取成本科目-贷方
            JSONObject costLoan = JSON.parseObject(object.get("costLoan").toString());

            JSONObject st = null;
            if (subjectType == 1) {
                st = incomeDebit;
            } else if (subjectType == 2) {
                st = incomeLoan;
            } else if (subjectType == 3) {
                st = costDebit;
            } else if (subjectType == 4) {
                st = costLoan;
            }

            //查询成本要素配置
            CostElementExample costElementExample = new CostElementExample();
            costElementExample.createCriteria().andDeletedFlagEqualTo(false);
            List<CostElement> costElements = costElementMapper.selectByExample(costElementExample);
            Map<String, String> costCodeAndSub = costElements.stream().collect(Collectors.toMap(CostElement::getCostCode, CostElement::getSubject, (key1, key2) -> key2));

            //根据项目类型配置的科目类型设置拼接科目
            //公司 第一段
            if (Objects.equals(st.get("company"), "常规")) {
                subject.append(st.get("companyCode")).append(".");
            } else if (Objects.equals(st.get("company"), "OU短码")) {
                OrganizationCustomDictExample organizationCustomDictExample = new OrganizationCustomDictExample();
                organizationCustomDictExample.createCriteria().andNameEqualTo("OU_CODE").andOrgIdEqualTo(carryoverBillDto.getOuId()).andDeletedFlagEqualTo(false);
                List<OrganizationCustomDict> organizationCustomDicts = organizationCustomDictMapper.selectByExample(organizationCustomDictExample);
                if (ListUtils.isNotEmpty(organizationCustomDicts)) {
                    subject.append(organizationCustomDicts.get(0).getValue()).append(".");
                }
            }
            //成本中心 第二段
            if (Objects.equals(st.get("costCenter"), "常规")) {
                subject.append(st.get("costCenterCode")).append(".");
            }
            //科目 第三段
            if (subjectType == 4) { //成本科目-贷方  没有国外
                if (Objects.equals(st.get("subject"), "常规")) {
                    subject.append(st.get("subjectCode")).append(".");
                } else if (Objects.equals(st.get("subject"), "成本要素")) {
                    subject.append(costCodeAndSub.get(costElement)).append(".");
                }
            } else {
                if (customerDto != null && Objects.equals("境外企业", customerDto.getEnterpriseNatureName())) { //国外
                    if (Objects.equals(st.get("subjectAbroad"), "常规")) {
                        subject.append(st.get("subjectAbroadCode")).append(".");
                    } else if (Objects.equals(incomeDebit.get("subjectAbroad"), "成本要素")) {
                        subject.append(costCodeAndSub.get(costElement)).append(".");
                    }
                } else { //国内
                    if (Objects.equals(st.get("subjectHome"), "常规")) {
                        subject.append(st.get("subjectHomeCode")).append(".");
                    } else if (Objects.equals(st.get("subjectHome"), "成本要素")) {
                        subject.append(costCodeAndSub.get(costElement)).append(".");
                    }
                }
            }
            //区域 第四段
            if (subjectType == 4) { //成本科目-贷方  没有国外
                if (Objects.equals(st.get("area"), "常规")) {
                    subject.append(st.get("areaCode")).append(".");
                }
            } else {
                if (customerDto != null && Objects.equals("境外企业", customerDto.getEnterpriseNatureName())) { //国外
                    if (Objects.equals(st.get("areaAbroad"), "常规")) {
                        subject.append(st.get("areaAbroadCode")).append(".");
                    }
                } else { //国内
                    if (Objects.equals(st.get("areaHome"), "常规")) {
                        subject.append(st.get("areaHomeCode")).append(".");
                    }
                }
            }
            //产品 第五段
            if (Objects.equals(st.get("product"), "常规")) {
                subject.append(st.get("productCode")).append(".");
            } else if (Objects.equals(st.get("product"), "业务分类")) {
                subject.append(flexValue).append(".");
            } else if (Objects.equals(st.get("product"), "项目产品")) {
                Project project = projectMapper.selectByPrimaryKey(carryoverBillDto.getProjectId());
                ProjectProductMaintenance projectProductMaintenance = basedataExtService.getProjectProductMaintenance(project.getProductId());
                if (null != projectProductMaintenance) {
                    subject.append(projectProductMaintenance.getProductCode()).append(".");
                } else {
                    throw new BizException(Code.ERROR, "【" + project.getCode() + "】" + "项目产品未维护，请先维护!");
                }
            }
            //往来 第六段
            if (Objects.equals(st.get("comeAndGo"), "常规")) {
                subject.append(st.get("comeAndGoCode")).append(".");
            } else if (Objects.equals(st.get("comeAndGo"), "客户")) {
                //如果客户CRM编码E0开头，则取客户编码的后六位数，作为此科目段的值;否则,“0” 作为此科目段的值
                String crmCode = customerDto.getCrmCode();
                if (null != customerDto.getInner() && null != crmCode && customerDto.getInner() && crmCode.startsWith("E0")) {
                    String crmCodeLastSixNum = crmCode.substring(crmCode.length() - 6, crmCode.length());
                    subject.append(crmCodeLastSixNum).append(".");
                } else {
                    subject.append(0).append(".");
                }
            }
            //备用 第七段
            if (Objects.equals(st.get("spare"), "常规")) {
                subject.append(st.get("spareCode"));
            }

        } else {
            throw new BizException(ErrorCode.ERROR, "项目类型:【" + type.getName() + "】" + "，未配置科目，请先维护");
        }
        return subject.toString();
    }

    /**
     * 取应收事务处理类型名称
     *
     * @param invoiceForwardAccounting
     * @return
     */
    private String getCustTrxTypeName(CarryoverIncomeAccounting invoiceForwardAccounting) {
        String trxTypeName = "";
        Set<String> valueSet = organizationCustomDictService.queryByName("收入确认应收类型", invoiceForwardAccounting.getOuId(),
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
        if (!CollectionUtils.isEmpty(valueSet)) {
            trxTypeName = valueSet.iterator().next();
        }
        return trxTypeName;
    }


    /**
     * 根据里程碑类型去项目类型的主/辅科目
     *
     * @param carryoverBillDto
     * @return
     */
    private String getSubjectFromProjectType(CarryoverBillDto carryoverBillDto, CustomerDto customerDto, Integer resourceType) {
        String subject = "";

        ProjectType projectType = projectTypeMapper.selectByPrimaryKey(carryoverBillDto.getProjectType());

        if (CarryoverBillResourceType.MILEPOST.getCode().equals(resourceType)) {
            ProjectMilepost projectMilepost = projectMilepostMapper.selectByPrimaryKey(carryoverBillDto.getProjectMilepostId());
            if (PublicUtil.isEmpty(projectMilepost)) {
                return null;
            }
            if (projectMilepost.getHelpFlag()) {
                subject = projectType.getHelpIncomeSubject();
            } else {
                subject = projectType.getMainIncomeSubject();
            }
        } else {
            final ProjectIncomeCostPlan projectIncomeCostPlan = projectIncomeCostPlanMapper.selectByPrimaryKey(carryoverBillDto.getProjectMilepostId());
            if (projectIncomeCostPlan == null) {
                return null;
            }

            subject = projectType.getMainIncomeSubject();
        }

        if (StringUtils.isEmpty(subject)) {
            throw new BizException(ErrorCode.ERROR, "项目类型未配置科目，请先维护");
        }

        String[] subjectArray = subject.split("\\.");

        if (subjectArray.length != 7) {
            throw new BizException(ErrorCode.ERROR, "项目类型未配置正确，请先维护");
        }

        //根据业务实体取“ERP组织关系”中的“公司代码”作为公司段，替换原来获取的第一段
        OrganizationRelQuery orgQuery = new OrganizationRelQuery();
        orgQuery.setOperatingUnitId(carryoverBillDto.getOuId());
        List<OrganizationRelDto> organizationRelDtoList = basedataExtService.getOrganizationRel(orgQuery);
        if (ListUtils.isNotEmpty(organizationRelDtoList)) {
            String companyCode = organizationRelDtoList.get(0).getCompanyCode();
            subjectArray[0] = companyCode;
        }
        //如果“客户类型”是内部客户，并且CRM客户编码为“E0”开头则取CRM编码的后6位数替换原来获取到的收入科目的第六段内容
        String crmCodeLastSixNum = this.getCrmCodeLastSixNum(customerDto);
        if (StringUtils.isNotEmpty(crmCodeLastSixNum)) {
            subjectArray[5] = crmCodeLastSixNum;
        }

        subject = ArrayUtils.toString(subjectArray);
        subject = subject.substring(1, subject.length() - 1);//去除{}
        subject = subject.replace(",", ".");//逗号替换回点号
        return subject;
    }

    /**
     * @param carryoverBillDto
     * @param costAmount
     * @param costElement
     * @param lineRemark
     */
    private CarryoverCostAccounting formatCostAccounting(
            CarryoverBillDto carryoverBillDto, BigDecimal costAmount,
            String costElement, String lineRemark, boolean isDebit, List<BusiSceneNonSaleDetailDto> detailDtoList, CustomerDto customerDto, ProjectType type, String flexValue) {
        CarryoverCostAccounting costAccounting = new CarryoverCostAccounting();
        costAccounting.setCostElement(costElement);
        costAccounting.setLineRemark(lineRemark);
        costAccounting.setGlPeriod(carryoverBillDto.getPeriodName());
        costAccounting.setCarryoverBillId(carryoverBillDto.getId());
        costAccounting.setDailyBatchNum(carryoverBillDto.getBillNum());
        costAccounting.setDailyBatchName("PAM_日记账导入_成本结转入账_" + carryoverBillDto.getBillNum());

        costAccounting.setOuId(carryoverBillDto.getOuId());
        costAccounting.setGlDate(this.getGlDate(carryoverBillDto.getPeriodName()));//总账日期
        costAccounting.setCurrency("CNY");
        // 6.15-6.25需求，借方/贷方科目不从非销售业务场景配置取借方
        //从非销售业务场景配置取借方/或贷方科目
/*        for (BusiSceneNonSaleDetailDto nonSaleDetailDto : detailDtoList) {
            if (costElement.equals(nonSaleDetailDto.getFlag())) {
                if (isDebit) {
                    //借方科目
                    costAccounting.setCostSubject(nonSaleDetailDto.getAccountGroupDebit());
                    //科目名称-科目中第3段查询COA
                    costAccounting.setCostSubjectName(this.getCoaSubjectDescription(costAccounting.getCostSubject()));
                    costAccounting.setDebitCredit(1);
                } else {
                    //贷方科目
                    costAccounting.setCostSubject(nonSaleDetailDto.getAccountGroupCredit());
                    //科目名称-科目中第3段查询COA
                    costAccounting.setCostSubjectName(this.getCoaSubjectDescription(costAccounting.getCostSubject()));
                    costAccounting.setDebitCredit(2);
                }
                //非销售业务场景id
                costAccounting.setBusiSceneId(nonSaleDetailDto.getBusiSceneNonSaleId());
                break;
            }
        }*/

        //成本科目调整
        this.adjustCostSubject(costAccounting, carryoverBillDto, customerDto, type, flexValue, isDebit);
        costAccounting.setCostAmount(costAmount);
        costAccounting.setDeletedFlag(Boolean.FALSE);
        costAccounting.setErpStatus(CommonErpStatus.PUSHING.code());
        return costAccounting;
    }

    /**
     * 获取非销售业务场景配置
     *
     * @return
     */
    private List<BusiSceneNonSaleDetailDto> getBusiSceneDetailList(Long ouId) {
        BusiSceneNonSaleExample example = new BusiSceneNonSaleExample();
        example.createCriteria().andBusiSceneNameEqualTo("GL成本结转").andOuIdEqualTo(ouId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<BusiSceneNonSale> busiSceneNonSales = busiSceneNonSaleMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(busiSceneNonSales)) {
            Map<String, Object> param = new HashMap();
            param.put(Constants.Page.PAGE_NUM.toString(), 1);
            param.put(Constants.Page.PAGE_SIZE.toString(), 100);
            param.put("busiSceneNonSaleId", String.valueOf(busiSceneNonSales.get(0).getId()));
            PageInfo<BusiSceneNonSaleDetailDto> pageInfo = busiSceneNonSaleService.detailPage(param);
            return pageInfo.getList();
        }
        return new ArrayList<BusiSceneNonSaleDetailDto>();
    }

    @Override
    public void pushToErp(Long carryoverBillId) {
        //收入入账推送
        CarryoverIncomeAccounting incomeQuery = new CarryoverIncomeAccounting();
        incomeQuery.setCarryoverBillId(carryoverBillId);
        List<CarryoverIncomeAccounting> incomeAccountingList = this.incomeAccountingList(incomeQuery);
        for (CarryoverIncomeAccounting incomeAccounting : incomeAccountingList) {
            if (incomeAccounting.getErpStatus().equals(CommonErpStatus.NOT_PUSH.code()) ||
                    incomeAccounting.getErpStatus().equals(CommonErpStatus.PUSH_FAILED.code())) {
                //PAM-ERP-018 应收发票写入
                HandleDispatcher.route(BusinessTypeEnums.CARRYOVER_INCOME.getCode(), String.valueOf(carryoverBillId), null, null, true, null, incomeAccounting.getOuId());
                //记录此次同步的信息到代办管理数据同步信息记录项中
                this.saveAgencySynInfo(BusinessTypeEnums.CARRYOVER_INCOME.getCode(), carryoverBillId.toString());
                incomeAccounting.setErpMessage(null);
                incomeAccountingMapper.updateByPrimaryKey(incomeAccounting);
                break;
            }
        }

        //成本入账推送
        CarryoverCostAccounting costQuery = new CarryoverCostAccounting();
        costQuery.setCarryoverBillId(carryoverBillId);
        List<CarryoverCostAccounting> costAccountingList = this.costAccountingList(costQuery);
        for (CarryoverCostAccounting costAccounting : costAccountingList) {
            if (costAccounting.getErpStatus().equals(CommonErpStatus.NOT_PUSH.code()) ||
                    costAccounting.getErpStatus().equals(CommonErpStatus.PUSH_FAILED.code())) {
                //PAM-ERP-029 总账日记账写入
                HandleDispatcher.route(BusinessTypeEnums.CARRYOVER_COST.getCode(), String.valueOf(carryoverBillId), null, null, true);
                //记录此次同步的信息到代办管理数据同步信息记录项中
                this.saveAgencySynInfo(BusinessTypeEnums.CARRYOVER_COST.getCode(), carryoverBillId.toString());
                costAccounting.setErpMessage(null);
                costAccountingMapper.updateByPrimaryKey(costAccounting);
                break;
            }
        }
        //更新结转单状态-推送中
        CarryoverBill bill = new CarryoverBill();
        bill.setId(carryoverBillId);
        bill.setErpSyncStatus(CommonErpStatus.PUSHING.code());
        this.updateCarryoverBillStatus(bill);
    }

    /**
     * 更新收入记账状态
     *
     * @param id
     * @param isSuccess
     * @param msg
     */
    @Override
    public void incomeResultReturnHandle(Long id, Boolean isSuccess, String msg) {
        CarryoverIncomeAccounting query = new CarryoverIncomeAccounting();
        query.setCarryoverBillId(id);
        List<CarryoverIncomeAccounting> accountingList = this.incomeAccountingList(query);
        for (CarryoverIncomeAccounting accounting : accountingList) {
            if (Objects.equals(accounting.getErpStatus(), CommonErpStatus.PUSHED.code())) {
                continue;
            }
            if (isSuccess) {
                accounting.setErpStatus(CommonErpStatus.PUSHED.code());
                accounting.setErpMessage(StringUtil.EMPTY_STRING);
            } else {
                accounting.setErpStatus(CommonErpStatus.PUSH_FAILED.code());
                accounting.setErpMessage(msg);
            }
            incomeAccountingMapper.updateByPrimaryKeySelective(accounting);
        }
        //更新头状态
        this.checkAndUpdateBillStatus(id);
    }

    /**
     * 更新成本记账状态
     *
     * @param id
     * @param isSuccess
     * @param msg
     */
    @Override
    public void costResultReturnHandle(Long id, Boolean isSuccess, String msg) {
        CarryoverCostAccounting query = new CarryoverCostAccounting();
        query.setCarryoverBillId(id);
        List<CarryoverCostAccounting> accountingList = this.costAccountingList(query);
        for (CarryoverCostAccounting accounting : accountingList) {
            if (Objects.equals(accounting.getErpStatus(), CommonErpStatus.PUSHED.code())) {
                continue;
            }
            if (isSuccess) {
                accounting.setErpStatus(CommonErpStatus.PUSHED.code());
                accounting.setErpMessage(StringUtil.EMPTY_STRING);
            } else {
                accounting.setErpStatus(CommonErpStatus.PUSH_FAILED.code());
                accounting.setErpMessage(msg);
            }
            costAccountingMapper.updateByPrimaryKeySelective(accounting);
        }
        //更新头状态
        this.checkAndUpdateBillStatus(id);
    }

    /**
     * 科目中第3段查询COA获取科目说明
     *
     * @param subject
     * @return
     */
    private String getCoaSubjectDescription(String subject) {
        String[] flexValueSetIds = subject.split("\\.");
        CoaSubject coaSubject = basedataExtService.getCoaSubject(flexValueSetIds[2]);
        if (!ObjectUtils.isEmpty(coaSubject)) {
            return coaSubject.getDescription();
        }
        return null;
    }

    private void checkAndUpdateBillStatus(Long carryoverBillId) {
        Integer erpStatus = null;
        CarryoverBill bill = new CarryoverBill();
        bill.setId(carryoverBillId);
        //检查收入入账推送erp状态
        CarryoverIncomeAccounting incomeQuery = new CarryoverIncomeAccounting();
        incomeQuery.setCarryoverBillId(carryoverBillId);
        List<CarryoverIncomeAccounting> incomeAccountingList = this.incomeAccountingList(incomeQuery);
        for (CarryoverIncomeAccounting incomeAccounting : incomeAccountingList) {
            if (CommonErpStatus.PUSH_FAILED.code().equals(incomeAccounting.getErpStatus())) {
                erpStatus = CommonErpStatus.PUSH_FAILED.code();
                bill.setErpSyncStatus(erpStatus);
                this.updateCarryoverBillStatus(bill);
                return;
            } else {
                erpStatus = incomeAccounting.getErpStatus();
            }
        }

        //检查成本入账推送erp状态
        CarryoverCostAccounting costQuery = new CarryoverCostAccounting();
        costQuery.setCarryoverBillId(carryoverBillId);
        List<CarryoverCostAccounting> costAccountingList = this.costAccountingList(costQuery);
        for (CarryoverCostAccounting costAccounting : costAccountingList) {
            if (CommonErpStatus.PUSH_FAILED.code().equals(costAccounting.getErpStatus())) {
                erpStatus = CommonErpStatus.PUSH_FAILED.code();
                bill.setErpSyncStatus(erpStatus);
                this.updateCarryoverBillStatus(bill);
                return;
            } else {
                erpStatus = costAccounting.getErpStatus();
            }
        }
        //全部已经推送或推送中,更新结转单状态
        bill.setErpSyncStatus(erpStatus);
        bill.setSyncErpTime(new Date());
        this.updateCarryoverBillStatus(bill);
    }

    /**
     * 结转单状态更新
     *
     * @param bill
     */
    private void updateCarryoverBillStatus(CarryoverBill bill) {
        carryoverBillMapper.updateByPrimaryKeySelective(bill);
    }

    /**
     * @param glPeriod
     * @return
     */
    private Date getGlDate(String glPeriod) {
        String currentYearMonth = DateUtils.getYearMonth(new Date());
        if (glPeriod.equals(currentYearMonth)) {
            return DateUtils.getShortDate(new Date());
        } else {
            Date glDate = DateUtils.parse(glPeriod, "yyyy-MM");
            return DateUtil.getEndingOfMonth(glDate);
        }
    }

    /**
     * 如果“客户类型”是内部客户，并且CRM客户编码为“E0”开头则取CRM编码的后6位数
     *
     * @param customerDto
     * @return
     */
    private String getCrmCodeLastSixNum(CustomerDto customerDto) {
        String crmCode = null;
        String crmCodeLastSixNum = "";
        if (null != customerDto) {
            crmCode = customerDto.getCrmCode();
            if (null != customerDto.getInner() && null != crmCode && customerDto.getInner() && crmCode.startsWith("E0")) {
                crmCodeLastSixNum = crmCode.substring(crmCode.length() - 6, crmCode.length());
            }
        }
        return crmCodeLastSixNum;
    }

    /**
     * 成本科目调整
     *
     * @param costAccounting
     * @param carryoverBillDto
     * @return
     */
    private void adjustCostSubject(CarryoverCostAccounting costAccounting, CarryoverBillDto carryoverBillDto, CustomerDto customerDto, ProjectType type, String flexValue, boolean isDebit) {
/*        if (StrUtil.isNotEmpty(costAccounting.getCostSubject())) {
            String[] subjectArray = costAccounting.getCostSubject().split("\\.");
            //科目组合中第三段是“6401”,需调整科目的产品段及往来段
            if (subjectArray[2].startsWith("6401")) {
                //调整产品段，需与收入科目产品段保持一致
                String incomeSubject = "";
                ProjectType projectType = projectTypeMapper.selectByPrimaryKey(carryoverBillDto.getProjectType());
                final Integer resourceType = carryoverBillDto.getResourceType();

                if (Objects.equals(resourceType, CarryoverBillResourceType.MILEPOST.getCode())) {
                    ProjectMilepost projectMilepost = projectMilepostMapper.selectByPrimaryKey(carryoverBillDto.getProjectMilepostId());
                    if (PublicUtil.isEmpty(projectMilepost)) {
                        return;
                    }

                    if (projectMilepost.getHelpFlag()) {
                        incomeSubject = projectType.getHelpIncomeSubject();
                    } else {
                        incomeSubject = projectType.getMainIncomeSubject();
                    }
                } else {
                    final ProjectIncomeCostPlan projectIncomeCostPlan = projectIncomeCostPlanMapper.selectByPrimaryKey(carryoverBillDto.getProjectMilepostId());
                    if (PublicUtil.isEmpty(projectIncomeCostPlan)) {
                        return;
                    }

                    incomeSubject = projectType.getMainIncomeSubject();
                }

                String[] incomeSubjectArray = incomeSubject.split("\\.");
                subjectArray[4] = incomeSubjectArray[4];

                //调整往来段
                CustomerDto customerDto = CacheDataUtils.findCustomerById(carryoverBillDto.getCustomerId());
                String crmCodeLastSixNum = this.getCrmCodeLastSixNum(customerDto);
                if (StringUtils.isNotEmpty(crmCodeLastSixNum)) {
                    subjectArray[5] = crmCodeLastSixNum;
                }

                String adjustedSubject = "";
                adjustedSubject = ArrayUtils.toString(subjectArray);
                adjustedSubject = adjustedSubject.substring(1, adjustedSubject.length() - 1);//去除{}
                adjustedSubject = adjustedSubject.replace(",", ".");//逗号替换回点号
                costAccounting.setCostSubject(adjustedSubject);
            }
        }*/

        //6.15-6.25新增需求
        if (isDebit) {
            //借方科目
            costAccounting.setCostSubject(this.getSubjectFromCustTrxType(carryoverBillDto, customerDto, type, flexValue, 3, costAccounting.getCostElement()));
            //科目名称-科目中第5段查询COA
            costAccounting.setCostSubjectName(this.getCoaSubjectDescription(costAccounting.getCostSubject()));
            costAccounting.setDebitCredit(1);
        } else {
            //贷方科目
            costAccounting.setCostSubject(this.getSubjectFromCustTrxType(carryoverBillDto, customerDto, type, flexValue, 4, costAccounting.getCostElement()));
            //科目名称-科目中第5段查询COA
            costAccounting.setCostSubjectName(this.getCoaSubjectDescription(costAccounting.getCostSubject()));
            costAccounting.setDebitCredit(2);
        }
    }

    private void saveAgencySynInfo(String businessType, String applyNo) {
        AgencySynInfoExample agencySynInfoExample = new AgencySynInfoExample();
        agencySynInfoExample.createCriteria().andBusinessTypeEqualTo(businessType)
                .andApplyNoEqualTo(applyNo).andDeletedFlagEqualTo(Boolean.FALSE);
        final List<AgencySynInfo> agencySyninfoList = agencySynInfoMapper.selectByExample(agencySynInfoExample);
        if (ListUtils.isNotEmpty(agencySyninfoList)) {
            for (AgencySynInfo agencySyninfo : agencySyninfoList) {
                agencySyninfo.setSynStartTime(new Date());
                agencySynInfoMapper.updateByPrimaryKey(agencySyninfo);
            }
        } else {
            AgencySynInfo agencySyninfo = new AgencySynInfo();
            agencySyninfo.setBusinessType(businessType);
            agencySyninfo.setApplyNo(applyNo);
            agencySyninfo.setSynStartTime(new Date());
            agencySyninfo.setDeletedFlag(Boolean.FALSE);
            agencySynInfoMapper.insert(agencySyninfo);
        }
    }
}
