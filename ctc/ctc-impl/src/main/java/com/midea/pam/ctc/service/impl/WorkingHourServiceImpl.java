package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.midea.mcomponent.core.util.Assert;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.LaborCostDto;
import com.midea.pam.common.basedata.dto.LaborCostTypeDto;
import com.midea.pam.common.basedata.dto.OrgLaborCostTypeSetDTO;
import com.midea.pam.common.basedata.dto.UnitDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrgLaborCostTypeSet;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.VendorMip;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto;
import com.midea.pam.common.ctc.dto.WorkingHourDto;
import com.midea.pam.common.ctc.dto.WorkingHourQueryDto;
import com.midea.pam.common.ctc.dto.WorkingHourResultDto;
import com.midea.pam.common.ctc.dto.WorkingHourStatDto;
import com.midea.pam.common.ctc.dto.WorkingHourStatResultDto;
import com.midea.pam.common.ctc.dto.WorkingHourWeekInfoDTO;
import com.midea.pam.common.ctc.entity.CnbEmpBasicData;
import com.midea.pam.common.ctc.entity.CnbEmpBasicDataExample;
import com.midea.pam.common.ctc.entity.CostCollection;
import com.midea.pam.common.ctc.entity.IhrAttendDetail;
import com.midea.pam.common.ctc.entity.IhrAttendDetailExample;
import com.midea.pam.common.ctc.entity.LaborCostDetail;
import com.midea.pam.common.ctc.entity.LaborCostDetailExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ProjectMember;
import com.midea.pam.common.ctc.entity.ProjectMemberExample;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ProjectWbsBudget;
import com.midea.pam.common.ctc.entity.WorkingHour;
import com.midea.pam.common.ctc.entity.WorkingHourExample;
import com.midea.pam.common.ctc.entity.WorkingHourHistory;
import com.midea.pam.common.ctc.excelVo.WorkingHourExcelVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.DeleteFlagEnum;
import com.midea.pam.common.enums.EmailStatus;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.LaborCostTypeCodeEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.NoticeBusinessType;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ProjectSource;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.UserType;
import com.midea.pam.common.enums.WorkingHourSourceFlag;
import com.midea.pam.common.enums.WorkingHourStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.Backlog;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.gateway.entity.TempMsgPush;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.enums.WorkHourStatus;
import com.midea.pam.ctc.common.redis.RedisContant;
import com.midea.pam.ctc.common.redis.RedisUtilServer;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.feign.basedata.feign.BasedataOrgLaborCostTypeSetFeignClient;
import com.midea.pam.ctc.feign.basedata.feign.EmployeeInfoFeignClient;
import com.midea.pam.ctc.mapper.CnbEmpBasicDataMapper;
import com.midea.pam.ctc.mapper.CostCollectionMapper;
import com.midea.pam.ctc.mapper.EmailExtMapper;
import com.midea.pam.ctc.mapper.IhrAttendDetailMapper;
import com.midea.pam.ctc.mapper.LaborCostDetailMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectMemberMapper;
import com.midea.pam.ctc.mapper.WorkingHourExtMapper;
import com.midea.pam.ctc.mapper.WorkingHourHistoryMapper;
import com.midea.pam.ctc.mapper.WorkingHourMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.MeiXinPushMsgService;
import com.midea.pam.ctc.service.NoticeService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.service.WorkingHourService;
import com.midea.pam.ctc.service.config.CommonPropertiesConfig;
import com.midea.pam.ctc.service.config.MilepostNoticeProperties;
import com.midea.pam.ctc.service.helper.OrgLaborCostTypeSetHelper;
import com.midea.pam.ctc.service.helper.PageUtils;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 工时管理service
 * @date 2019-4-8
 */
public class WorkingHourServiceImpl implements WorkingHourService {

    private final static String USERID = "userId";
    private final static String USERTYPE = "userType";
    private final static String USERNAME = "userName";
    private final static String EMPLOYEEINFOQUERY = "/employeeInfo/query";
    private final static String STATUS = "status";
    private final static String MAXHOURS = "maxhours";
    private final static String DATAFORMAT = "yyyy-MM-dd";
    private final static String DATE1 = "date1";
    private final static String DATE2 = "date2";
    private final static String DATE3 = "date3";
    private final static String DATE4 = "date4";
    private final static String DATE5 = "date5";

    private static Logger logger = LoggerFactory.getLogger(WorkingHourServiceImpl.class);
    private SimpleDateFormat sdf = new SimpleDateFormat(DATAFORMAT);

    @Value("${route.workingHourUrl}")
    private String workingHourUrl;

    @Value("${route.workingHourSubmitUrl}")
    private String workingHourSubmitUrl;

    @Resource
    private CommonPropertiesConfig commonPropertiesConfig;
    @Resource
    private RedisUtilServer redisUtilServer;
    @Resource
    private WorkingHourMapper workingHourMapper;
    @Resource
    private ProjectExtMapper projectExtMapper;
    @Resource
    private IhrAttendDetailMapper ihrAttendDetailMapper;
    @Resource
    private WorkingHourHistoryMapper workingHourHistoryMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectMemberMapper projectMemberMapper;
    @Resource
    private LaborCostDetailMapper laborCostDetailMapper;
    @Resource
    private CostCollectionMapper costCollectionMapper;
    @Resource
    private com.midea.pam.ctc.service.ProjectService projectService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private MeiXinPushMsgService meiXinPushMsgService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private WorkingHourExtMapper workingHourExtMapper;
    @Resource
    private NoticeService noticeService;
    @Resource
    private MilepostNoticeProperties milepostNoticeProperties;
    @Resource
    private WorkingHourService workingHourService;
    @Resource
    private ProjectTypeService projectTypeService;
    @Resource
    private EmailExtMapper emailExtMapper;
    @Resource
    private BasedataOrgLaborCostTypeSetFeignClient basedataOrgLaborCostTypeSetFeignClient;
    @Resource
    private OrgLaborCostTypeSetHelper orgLaborCostTypeSetHelper;
    @Resource
    private ProjectWbsBudgetService projectWbsBudgetService;
    @Resource
    private EmployeeInfoFeignClient employeeInfoFeignClient;
    @Resource
    private Validator validator;
    @Resource
    private CnbEmpBasicDataMapper cnbEmpBasicDataMapper;


    @Override
    public int batchInsert(List<WorkingHour> workingHours) {
        if (ListUtils.isEmpty(workingHours)) {
            return 0;
        }

        int batchSize = 1500;
        if (workingHours.size() <= batchSize) {
            return workingHourExtMapper.batchInsert(workingHours);
        }

        int batchNum = workingHours.size() / batchSize;
        int n = workingHours.size() % batchSize;
        if (n > 0) {
            batchNum = batchNum + 1;
        }

        int resultNum = 0;
        for (int i = 0; i < batchNum; i++) {
            List<WorkingHour> subList = subList(workingHours, (i + 1), batchSize);
            resultNum = resultNum + workingHourExtMapper.batchInsert(subList);
        }

        return resultNum;
    }

    private List<WorkingHour> subList(List<WorkingHour> workingHours, int batchNum, int batchSize) {
        int size = workingHours.size();
        int startIndex = (batchNum - 1) * batchSize;
        int endIndex = batchNum * batchSize;
        if (endIndex > size) {
            endIndex = size;
        }

        List<WorkingHour> batchList = new ArrayList<>();
        for (int i = startIndex; i < endIndex; i++) {
            batchList.add(workingHours.get(i));
        }
        return batchList;
    }


    private WorkingHour getWorkingHour(Long id, Long userId, Long projectId, String applyDate, Integer sourceFlag,
                                       String wbsBudgetCode, Long laborWbsCostId, String laborWbsCostName) {
        logger.info("【查询工时入参】id：{}，userId：{}，projectId：{}，applyDate：{}，sourceFlag：{}，wbsBudgetCode：{}，laborWbsCostId：{}，laborWbsCostName：{}",
                id, userId, projectId, applyDate, sourceFlag, wbsBudgetCode, laborWbsCostId, laborWbsCostName);

        WorkingHour workingHour = null;

        if (id != null) {
            workingHour = workingHourMapper.selectByPrimaryKey(id);
        } else {

            if (StringUtils.isBlank(applyDate) || projectId == null) {
                return null;
            }

            Date date = null;
            try {
                date = new SimpleDateFormat(DATAFORMAT).parse(applyDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
                date = null;
            }

            if (userId != null && projectId != null && date != null) {

                List<WorkingHour> workingHourList = workingHourExtMapper.selectWorkHour(userId, projectId, date, wbsBudgetCode, laborWbsCostName);

                if (workingHourList == null || workingHourList.size() == 0) {
                    //没找到
                    workingHour = new WorkingHour();
                    workingHour.setUserId(userId);
                    UserInfo userInfo = CacheDataUtils.findUserById(userId);
                    if (userInfo != null) {
                        workingHour.setUserMip(userInfo.getUsername());
                    }
                    workingHour.setProjectId(projectId);
                    workingHour.setApplyDate(date);
                    workingHour.setWbsBudgetCode(wbsBudgetCode);
                    workingHour.setLaborWbsCostId(laborWbsCostId);
                    workingHour.setLaborWbsCostName(laborWbsCostName);
                    workingHour.setDeleteFlag(0);
                    workingHour.setSourceFlag(sourceFlag != null ? sourceFlag : WorkingHourSourceFlag.PC.getCode());//默认PC填报
                } else {
                    //找到记录
                    workingHour = workingHourList.get(0);
                }
            }
        }

        //如果职级为空，或人天单价为空，则调用用户接口，查询职级及人天单价信息
        if (workingHour != null && (StringUtils.isBlank(workingHour.getLevel()) || workingHour.getCostMoney() == null)) {
            //获取项目信息
            Project project = projectMapper.selectByPrimaryKey(workingHour.getProjectId());
            if (project != null && StringUtils.isNotBlank(project.getPriceType())) {
                workingHour.setProjectCode(project.getCode());
                if (StringUtils.isEmpty(workingHour.getWbsSummaryCode()) && StringUtils.isNotEmpty(wbsBudgetCode)) {
                    workingHour.setWbsSummaryCode(String.format("%s-%s", project.getCode(), wbsBudgetCode));
                }
                Unit secondUnit = CacheDataUtils.findUnitById(project.getUnitId());//获取使用单位
                Assert.notNull(secondUnit, "项目单位异常");
                // 优先取人力费率来源id
                Long laborCostSourceUnitId = orgLaborCostTypeSetHelper.getLaborCostSourceUnitIdByUserId(workingHour.getUserId());
                laborCostSourceUnitId = laborCostSourceUnitId != null ? laborCostSourceUnitId : secondUnit.getParentId();
                //项目单价类型1:内部;2:外部;3:研发
                String url = ModelsEnum.BASEDATA.getBaseUrl() + "employeeInfo/queryLevelAndFee/" +
                        workingHour.getUserId() + "/" + project.getPriceType() + "/" + laborCostSourceUnitId;
                if (laborWbsCostId != null) {
                    url += "?laborWbsCostId=" + laborWbsCostId;
                }
                String res = restTemplate.getForObject(url, String.class);
                logger.info("queryLevelAndFee:{}", res);

                JSONObject json = JSON.parseObject(res);
                if (json.containsKey("levelName")) {
                    workingHour.setLevel(json.getString("levelName"));
                }
                if (json.containsKey("fee")) {
                    workingHour.setCostMoney(json.getBigDecimal("fee"));
                }
                if (json.containsKey("levelType")) {
                    workingHour.setLaborCostType(json.getString("levelType"));
                }
            }
        }

        return workingHour;
    }

    /**
     * @param userId             用户ID
     * @param projectId          项目ID
     * @param applyDate          申请日期
     * @param status             状态
     * @param week               周
     * @param applyWorkingHours  申请工时
     * @param processId          流程ID
     * @param actualWorkingHours 确认工时
     * @param remarks            备注
     * @param stayApproveUserId  商机项目待审批人
     * @return
     */
    private Boolean operation(Long id, Long userId, Long unitId, Long projectId, String applyDate, Integer status,
                              String week, Float applyWorkingHours, Long processId, Float actualWorkingHours,
                              String remarks, String action, Long stayApproveUserId, String stayApproveUserMip,
                              Integer sourceFlag, String wbsBudgetCode, Long laborWbsCostId, String laborWbsCostName, String projectActivityCode) {
        WorkingHour workingHour = this.getWorkingHour(id, userId, projectId, applyDate,
                sourceFlag, wbsBudgetCode, laborWbsCostId, laborWbsCostName);
        workingHour.setProjectActivityCode(projectActivityCode);
        projectId = projectId == null ? workingHour.getProjectId() : projectId;
        Project project = projectMapper.selectByPrimaryKey(projectId);

        if (Objects.equals(1, workingHour.getRdmFlag())) {
            throw new BizException(Code.ERROR, "RDM工时暂不支持在PAM系统操作");
        }
        if (Objects.equals(action, "提交审批")) {
            //获取工时填报限制天数
            Integer workingHourLimitDay = getWorkingHourLimitDay(unitId);
            if (workingHourLimitDay != null && workingHour.getApplyDate() != null) {
                Date startDateOfCalculation = workingHour.getApplyDate();
                //如果工时状态为驳回，则计算起始时间为驳回日期
                if (Objects.equals(workingHour.getStatus(), WorkingHourStatus.REJECT.getCode()) && workingHour.getApproveTime() != null) {
                    startDateOfCalculation = workingHour.getApproveTime();
                }
                int daysBetween = DateUtils.daysOfTwo(startDateOfCalculation, new Date());
                if (daysBetween > workingHourLimitDay) {
                    throw new BizException(Code.ERROR, String.format("%s天前的工时不允许填写，请及时填写工时", workingHourLimitDay));
                }
            }

            //设置项目OU
            workingHour.setOuId(project.getOuId());
            //设置发薪OU
            String statisticDate = DateUtil.format(workingHour.getApplyDate(), "yyyyMM");
            CnbEmpBasicDataExample cnbEmpBasicDataExample = new CnbEmpBasicDataExample();
            cnbEmpBasicDataExample.createCriteria().andStatisticDateEqualTo(statisticDate)
                    .andUserIdEqualTo(workingHour.getUserId())
                    .andOuIdIsNotNull()
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            cnbEmpBasicDataExample.setOrderByClause("create_at desc");
            List<CnbEmpBasicData> cnbEmpBasicDataList = cnbEmpBasicDataMapper.selectByExample(cnbEmpBasicDataExample);
            if (!CollectionUtils.isEmpty(cnbEmpBasicDataList)) {
                workingHour.setFullPayOuId(cnbEmpBasicDataList.get(0).getOuId());
            }
        }

        //原来的状态
        Integer oldStatus = workingHour.getStatus();
        if (status != null) workingHour.setStatus(status);
        if (status.equals(3) || status.equals(4)) {//3-审批驳回 4-审批通过
            //记录审批信息
            workingHour.setApproveUserId(SystemContext.getUserId());
            UserInfo userInfo = CacheDataUtils.findUserById(SystemContext.getUserId());
            workingHour.setApproveUserName(userInfo.getName());
            workingHour.setApproveTime(new Date());
        }

        // 提交操作
        if (status == 2 && workingHour.getId() != null && oldStatus != 1 && oldStatus != 3) {
            String errorTip = "项目：" + project.getName();
            if (wbsBudgetCode != null) {
                errorTip += "，WBS：" + wbsBudgetCode;
                if (laborWbsCostId != null) {
                    errorTip += "+角色：" + laborWbsCostName;
                }
            } else if (laborWbsCostId != null) {
                errorTip += "，角色：" + laborWbsCostName;
            }
            errorTip += "，日期：" + applyDate + "已填报";
            throw new BizException(Code.ERROR, errorTip);
        }

        // 撤回操作
        if (status == 1 && oldStatus == 4) {
            throw new BizException(Code.ERROR, "不能撤回已通过的工时");
        }

        // 只能审批或驳回状态为待审批的工时
        if ((status == 4 || status == 3) && oldStatus != 2) {
            return Boolean.FALSE;
        }

        if (((status.equals(2) || status.equals(5))) && applyWorkingHours < 0f) {
            throw new BizException(Code.ERROR, "工时填报不能小于0");
        }
        //如果是审批通过工时又是0的情况,直接删除
        if (status.equals(4) && actualWorkingHours.compareTo(0f) == 0) {
            workingHour.setDeleteFlag(DeleteFlagEnum.DELETED.getIntValue());
        }
        if (status.equals(2) || status.equals(5)) {//2-审批提交 5-变更中
            String startDateStr = getWorkingHourStartDate(project);
            //如果配置了起始时间并且项目为导入数据
            if (project.getIsImport() && StringUtil.isNotNull(startDateStr)) {
                Date startDate = DateUtils.parse(startDateStr, DateUtils.FORMAT_SHORT);
                if (workingHour.getApplyDate().before(startDate)) {
                    String errorMsg = "初始化项目[" + project.getName() + "]不允许填报" + DateUtil.format(startDate, DateUtil.DATE_PATTERN) + "之前的工时";
                    throw new BizException(Code.ERROR, errorMsg);
                }
            }
            //根据项目销售部门查询使用单位
            Unit unit = CacheDataUtils.findUnitById(project.getUnitId());
            if (null != unit && null != unit.getParentId()) {
                unitId = unit.getParentId();
            }

            //检查人力费用是否配置
            checkLaborCost(userId);

            // 判断WBS+活动事项的预算不存在，且无法生成对应的项目预算
            if (Boolean.TRUE.equals(project.getWbsEnabled())) {
                String url = ModelsEnum.BASEDATA.getBaseUrl() + "laborCost/getLaborCostByIds";
                // 根据id批量查询角色费率信息
                String res = restTemplate.postForObject(url, Arrays.asList(laborWbsCostId), String.class);
                Assert.notNull(res, "根据id批量查询费率失败");
                List<LaborCostDto> laborCostDtoList = JSON.parseObject(res, new TypeReference<List<LaborCostDto>>() {
                });
                Assert.isTrue(ListUtils.isNotEmpty(laborCostDtoList), "批量查询费率失败");
                LaborCostDto laborCostDto = laborCostDtoList.get(0);
                // 先判断活动事项以及活动事项的上级（活动事项的上下级关系在“项目活动事项维护”中设置）存不存在，上级存在也算存在
                ProjectWbsBudget projectWbsBudget = projectWbsBudgetService.existWbsBudgetByActivity(projectId, wbsBudgetCode, laborCostDto.getProjectActivityCode());
                if (projectWbsBudget == null) {
                    // 查询配置了预算事项的活动事项或上级(判断这个活动事项是不是预算事项)
                    String budgetActivityCode = projectWbsBudgetService.checkWbsBudgetByActivity(workingHour.getBizUnitId(), laborCostDto.getProjectActivityCode());
                    Assert.isTrue(StringUtils.isNotBlank(budgetActivityCode),
                            "WBS[" + wbsBudgetCode + "]+活动事项[" + laborCostDto.getProjectActivityCode() + "]的预算不存在，" +
                                    "且无法生成对应的项目预算（该活动事项及上级都为非预算事项）");
                    // 填报的WBS+活动事项（通过角色绑定）在这个项目的预算中不存在，则在预算中自动生成该条预算，预算金额为0
                    projectWbsBudgetService.addWbsBudgetByActivity(projectId, wbsBudgetCode, budgetActivityCode);
                }
            }
        }
        if (StringUtils.isNotBlank(week)) workingHour.setWeek(week);
        if (applyWorkingHours != null)
            workingHour.setApplyWorkingHours(new BigDecimal(Float.toString(applyWorkingHours)));
        if (processId != null) workingHour.setProcessId(processId);
        if (actualWorkingHours != null)
            workingHour.setActualWorkingHours(new BigDecimal(Float.toString(actualWorkingHours)));
        if (remarks != null) workingHour.setRemarks(remarks);

        //判断是否超过8个工时  2-审批提交 5-变更中
        if ((status.equals(2) || status.equals(5))) {
            int limitHour = getLimitHours(unitId);
            if (isOverHour(workingHour, limitHour)) {
                throw new BizException(Code.ERROR, String.format("一天工时不能超过%s，含跨单位工时", limitHour));
            }
        }

        Boolean isCollection = false;
        if (workingHour.getId() != null) {
            isCollection = isCollection(workingHour.getId());
        }
        UserInfo userInfo = CacheDataUtils.findUserById(workingHour.getUserId());
//        workingHour.setUserType(userInfo.getType());
        OrgLaborCostTypeSet orgLaborCostTypeSet = orgLaborCostTypeSetHelper.getLaborCostTypeByUserId(workingHour.getUserId());
        if (orgLaborCostTypeSet != null) {
            if (Objects.equals(orgLaborCostTypeSet.getRoleType(), 0)) {
                workingHour.setUserType(UserType.INTERNAL.code()); //内部
            } else if (Objects.equals(orgLaborCostTypeSet.getRoleType(), 1)) {
                workingHour.setUserType(UserType.RECRUIT.code()); //自招外包
            }
        }

        //工时修改，判断是否已入账和归集
        if (status.equals(5) && isCollection) {
            String msg = userInfo.getName() + "（" + userInfo.getUsername() + "）"
                    + DateUtil.format(workingHour.getApplyDate(), DateUtil.DATE_PATTERN) + "的工时财务已入账，不可修改";
            throw new BizException(Code.ERROR, msg);
        }

        //如果是变更通过，判断是否已结转
        if (status.equals(4) && oldStatus.equals(5)) {
            if (isCollection) {
                String msg = userInfo.getName() + "（" + userInfo.getUsername() + "）"
                        + DateUtil.format(workingHour.getApplyDate(), DateUtil.DATE_PATTERN) + "的工时财务已入账，不可修改，请驳回";
                throw new BizException(Code.ERROR, msg);
            }
            //更新归集表（labor_cost_detail、cost_collection）中的工时数和成本值
            updateCollection(workingHour);
        }
        //如果是变更驳回（即之前状态为变更中）
        if (status.equals(3) && oldStatus.equals(5)) {
            //修改状态为已通过，并且工时数改为之前的
            workingHour.setStatus(WorkingHourStatus.PASS.getCode());
            workingHour.setApplyWorkingHours(workingHour.getActualWorkingHours());
        }
        // 如果是移动端提交&商机项目（移动端传的ID是错的）
        if (StringUtils.isNotEmpty(stayApproveUserMip)) {
            UserInfo stayApproveUser = CacheDataUtils.findUserByMip(stayApproveUserMip);
            if (stayApproveUser != null) {
                stayApproveUserId = stayApproveUser.getId();
            }
        }
        if (Objects.equals(project.getProjectSource(), ProjectSource.BUSINESS.getCode())) {
            workingHour.setStayApproveUserId(stayApproveUserId);
            workingHour.setStayApproveUserMip(stayApproveUserMip);
        }
        if (workingHour.getId() == null) {
            if (unitId == null) {
                unitId = SystemContext.getUnitId();
                if (unitId == null) {
                    //从项目获取虚拟部门
                    Long secondUnitId = project.getUnitId();
                    List<UnitDto> unitDtos = basedataExtService.findUnitDtos(Long.toString(secondUnitId), null);
                    if (unitDtos != null && unitDtos.size() > 0) {
                        unitId = unitDtos.get(0).getParentId();
                    }
                }
            }
            workingHour.setBizUnitId(unitId);
            //查询考勤时长
            workingHour.setIhrAttendHours(getIhrAttendHours(workingHour.getUserId(), workingHour.getApplyDate()));
            //查询部门
            workingHour.setApplyOrg(cutOrgNamePath(userInfo));
            // 人力费率
            workingHour.setLaborCostTypeSetId(userInfo.getLaborCostTypeSetId());
            // 人力费率来源单位id
            workingHour.setLaborCostSourceUnitId(orgLaborCostTypeSetHelper.getLaborCostSourceUnitIdByUserId(workingHour.getUserId()));
            // 获取人力部门
            workingHour.setOrgId(orgLaborCostTypeSet == null ? null : orgLaborCostTypeSet.getOrgId());
            workingHour.setOrgName(orgLaborCostTypeSet == null ? null : orgLaborCostTypeSet.getOrgName());

            workingHour.setCreateAt(new Date());
            workingHour.setCreateBy(SystemContext.getUserId());
            workingHourMapper.insertSelective(workingHour);
        } else {
            workingHour.setUserType(null); //入库后不再更新用户类型
            workingHourMapper.updateByPrimaryKeySelective(workingHour);
        }

        WorkingHourHistory history = new WorkingHourHistory();
        history.setWeek(workingHour.getWeek());
        history.setApplyDate(workingHour.getApplyDate());
        history.setApplyWorkingHours(workingHour.getApplyWorkingHours());
        history.setStatus(workingHour.getStatus());
        history.setUserId(workingHour.getUserId());
        history.setProjectId(workingHour.getProjectId());
        history.setProcessId(workingHour.getProcessId());
        history.setLevel(workingHour.getLevel());
        history.setCostMoney(workingHour.getCostMoney());
        history.setActualWorkingHours(workingHour.getActualWorkingHours());
        history.setRemarks(workingHour.getRemarks());
        history.setAction(action);

        workingHourHistoryMapper.insertSelective(history);
        return true;
    }

    /**
     * 获取工时填报限制天数
     *
     * @param unitId
     * @return
     */
    private Integer getWorkingHourLimitDay(Long unitId) {
        // 查询组织参数：工时填报限制天数
        Set<String> workingHourLimitDaySet = organizationCustomDictService.queryByName(Constants.WORKING_HOUR_LIMIT_DAY, unitId, OrgCustomDictOrgFrom.COMPANY);
        if (!CollectionUtils.isEmpty(workingHourLimitDaySet)) {
            try {
                return Integer.valueOf(workingHourLimitDaySet.iterator().next());
            } catch (Exception e) {
                throw new BizException(100, "组织参数-工时填报限制天数配置错误");
            }
        }
        return null;
    }

    /**
     * 截取组织机构上两层(集团-公司)
     */
    private String cutOrgNamePath(UserInfo userInfo) {
        if (userInfo.getOrginization() == null) {
            return null;
        }
        String orgNamePath = userInfo.getOrginization().getNamePath();
        if (Objects.nonNull(orgNamePath)) {

            if (orgNamePath.contains("/")) {
                orgNamePath = orgNamePath.substring(orgNamePath.indexOf("/") + 1, orgNamePath.length());
            }

            if (orgNamePath.contains("/")) {
                orgNamePath = orgNamePath.substring(orgNamePath.indexOf("/") + 1, orgNamePath.length());
            }

        }
        return orgNamePath;
    }

    private BigDecimal getIhrAttendHours(Long userId, Date applyDate) {
        IhrAttendDetailExample example = new IhrAttendDetailExample();
        example.createCriteria().andUserIdEqualTo(userId).andAttendDateEqualTo(applyDate);
        List<IhrAttendDetail> detailList = ihrAttendDetailMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(detailList)) {
            return detailList.get(0).getActualHours();
        }
        return null;
    }

    private void updateCollection(WorkingHour workingHour) {
        LaborCostDetailExample detailExample = new LaborCostDetailExample();
        detailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andWorkingHourIdEqualTo(workingHour.getId());
        List<LaborCostDetail> laborCostDetails = laborCostDetailMapper.selectByExample(detailExample);
        //入账
        if (laborCostDetails != null && laborCostDetails.size() > 0) {
            LaborCostDetail laborCostDetail = laborCostDetails.get(0);
            BigDecimal oldCost = laborCostDetail.getCostTotal();
            laborCostDetail.setActualWorkingHours(workingHour.getApplyWorkingHours());
            BigDecimal newCost = laborCostDetail.getCostMoney().multiply(laborCostDetail.getActualWorkingHours().divide(new BigDecimal(8)));
            laborCostDetail.setCostTotal(newCost);
            laborCostDetailMapper.updateByPrimaryKeySelective(laborCostDetail);
            //结转
            CostCollection costCollection = costCollectionMapper.selectByPrimaryKey(laborCostDetail.getCostCollectionId());
            if (costCollection != null) {
                //内部
                if ("1".equals(laborCostDetail.getUserType())) {
                    costCollection.setInnerLaborCost(costCollection.getInnerLaborCost().add(newCost.subtract(oldCost)));
                } else {
                    costCollection.setOuterLaborCost(costCollection.getOuterLaborCost().add(newCost.subtract(oldCost)));
                }
                costCollectionMapper.updateByPrimaryKeySelective(costCollection);
            }
        }
    }

    private boolean isCollection(Long workingHourId) {
        //判断是否已归集
        LaborCostDetailExample detailExample = new LaborCostDetailExample();
        detailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andWorkingHourIdEqualTo(workingHourId);
        List<LaborCostDetail> laborCostDetails = laborCostDetailMapper.selectByExample(detailExample);
        return ListUtils.isNotEmpty(laborCostDetails);
    }

    private void checkLaborCost(Long userId) {
        UserInfo user = CacheDataUtils.findUserById(userId);
        Guard.notNull(user, "当前用户不存在");

        //查询用户职级是否配置了人力预算
        Long laborCostTypeSetId = user.getLaborCostTypeSetId();
        Guard.notNull(laborCostTypeSetId, user.getName() + "(" + user.getUsername() + ")用户的人力费率角色设置ID未维护，请联系项目财务处理");
        OrgLaborCostTypeSet orgLaborCostTypeSet = orgLaborCostTypeSetHelper.getByOrgLaborCostTypeSetById(laborCostTypeSetId);
        Guard.isTrue(orgLaborCostTypeSet != null && !Objects.equals(orgLaborCostTypeSet.getDeletedFlag(), Boolean.TRUE), user.getName() + "(" + user.getUsername() + ")用户的部门费率类型设置未维护，请联系项目财务处理");
    }

    @Override
    public JSONObject getUserStatus(Long userId, Long projectId, String projectName, String userType, Integer status, String startDate, String endDate) {
        WorkingHourQueryDto query = new WorkingHourQueryDto();

        if (userId != null) {
            List<Long> userIds = new ArrayList<>();
            userIds.add(userId);
            query.setUserIds(userIds);
        }

        if (projectId != null) {
            List<Long> list = new ArrayList<>();
            list.add(projectId);
            query.setProjectIds(list);
        }

        if (StringUtils.isNotBlank(userType)) {
            Map<String, Object> param = new HashMap<>();
            param.put(USERID, userId);
            param.put(USERTYPE, userType);

            String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), EMPLOYEEINFOQUERY, param);
            String res = restTemplate.getForEntity(url, String.class).getBody();

            List<Long> userIds = new ArrayList<>();
            JSONArray array = JSONArray.parseArray(res);
            for (int i = 0; i < array.size(); i++) {
                JSONObject json = array.getJSONObject(i);

                if (json.containsKey("id")) {
                    userIds.add(json.getLong("id"));
                }
            }
            if (userIds.size() > 0) query.setUserIds(userIds);
        }

        if (status != null) {
            List<Integer> statusList = new ArrayList<>();
            statusList.add(status);
            query.setStatus(statusList);
        }

        if (StringUtils.isNotBlank(projectName)) {
            query.setProjectName(projectName);
        }

        if (StringUtils.isNotBlank(startDate)) {
            try {
                Date sDate = sdf.parse(startDate);
                query.setStartDate(sDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }

        if (StringUtils.isNotBlank(endDate)) {
            try {
                Date eDate = sdf.parse(endDate);
                query.setEndDate(eDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }
        //只查询当前使用单位下的填报工时
        query.setBizUnitId(SystemContext.getUnitId());
        //TODO 缩小查询的返回数据
//        List<WorkingHourResultDto> list = workingHourExtMapper.selectForDisplay(query);
//        supply(list);//补充用户名和项目类型
//        Map<Integer, Integer> statusMap = new HashMap<>();
//        Map<String, String> statusWeekMap = new HashMap<>();
//        Date monday, sunday;
//        String week;
//        for (WorkingHourResultDto dto : list) {
//            //rdm同步过来的审批中数据不统计
//            if (Objects.equals(dto.getRdmFlag(), 1) && Objects.equals(dto.getStatus(), 2)) {
//                continue;
//            }
//
//            if (statusMap.get(dto.getStatus()) == null) {
//                statusMap.put(dto.getStatus(), 0);
//            }
//
//            statusMap.put(dto.getStatus(), statusMap.get(dto.getStatus()) + 1);
//            monday = getThisWeekMonday(dto.getApplyDate());
//            sunday = DateUtils.addDay(monday, 6);
//            week = sdf.format(monday) + " - " + sdf.format(sunday);
//            statusWeekMap.put(dto.getStatus() + "_" + week, week);
//        }

        JSONObject result = new JSONObject();
        result.put(USERID, userId);
        JSONArray array = new JSONArray();

        Map<Integer, Integer> statusMap = new HashMap<>();
        Map<Integer, List<String>> statusWeekMap = new HashMap<>();

        List<WorkingHourWeekInfoDTO> weekInfoList = workingHourExtMapper.getWeekInfo(query);
        if (ListUtils.isNotEmpty(weekInfoList)) {
            for (WorkingHourWeekInfoDTO weekInfoDTO : weekInfoList) {
                Integer whStatus = weekInfoDTO.getStatus();
                int sum = weekInfoDTO.getSum();
                String weekRegion = weekInfoDTO.getWeekRegion();

                int count = 0;
                if (statusMap.containsKey(whStatus)) {
                    count = statusMap.get(whStatus);
                    count = count + sum;
                } else {
                    count = sum;
                }
                statusMap.put(whStatus, count);

                List<String> list;
                if (statusWeekMap.containsKey(whStatus)) {
                    list = statusWeekMap.get(whStatus);
                } else {
                    list = new ArrayList<>();
                    statusWeekMap.put(whStatus, list);
                }
                list.add(weekRegion);
            }
        }

        for (Map.Entry<Integer, Integer> statusEntity : statusMap.entrySet()) {
            JSONObject json = new JSONObject();
            Integer whStatus = statusEntity.getKey();
            json.put(STATUS, whStatus);
            json.put("count", statusEntity.getValue());
            json.put("week", statusWeekMap.get(whStatus));

            array.add(json);
        }

        result.put(STATUS, array);

        UserInfo userInfo = CacheDataUtils.findUserById(userId);
        String type = userInfo.getType();//1内；2外；
        if ("1".equals(type)) {//TODO
            result.put(MAXHOURS, 8);
        } else {
            result.put(MAXHOURS, 12);
        }

        return result;
    }

    @Override
    public JSONObject getPendingStatusByManager(Long userId, Integer status) {
        JSONObject result = new JSONObject();
        result.put(USERID, userId);
        List<Integer> projectStatus = new ArrayList<>();
        projectStatus.add(4);//项目进行中
        projectStatus.add(7);//预立项确认中
        projectStatus.add(9);//项目变更中
        projectStatus.add(10);//已结项
        projectStatus.add(-3);//预立项转正驳回
        WorkingHourQueryDto query = new WorkingHourQueryDto();
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andManagerIdEqualTo(userId).andDeletedFlagEqualTo(Boolean.FALSE).andStatusIn(projectStatus);
        projectExample.or().andManagerIdEqualTo(userId).andDeletedFlagEqualTo(Boolean.FALSE).andStatusEqualTo(-3);

        List<Project> listProject = projectMapper.selectByExample(projectExample);
        List<Long> ids = new ArrayList<>();
        for (Project project : listProject) {
            ids.add(project.getId());
        }
        WorkingHourExample workingHourExample = new WorkingHourExample();
        workingHourExample.createCriteria().andStayApproveUserIdEqualTo(userId).andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code());
        List<WorkingHour> workingHours = workingHourService.selectByExample(workingHourExample);
        if (ListUtils.isNotEmpty(workingHours)) {
            for (WorkingHour workingHour : workingHours) {
                Project project = projectMapper.selectByPrimaryKey(workingHour.getProjectId());
                if ((project.getStatus() >= 4 || project.getStatus() == -3) && project.getStatus() != 12) {
                    ids.add(workingHour.getProjectId());
                }
            }
        }
        if (ListUtils.isNotEmpty(ids)) {
            query.setProjectIds(ids);
        } else {
            return result;
        }

        if (status != null) {
            List<Integer> statusList = new ArrayList<>();
            statusList.add(status);
            query.setStatus(statusList);
        }
        if (null != SystemContext.getUnitId()) {
            query.setBizUnitId(SystemContext.getUnitId());
        }
        query.setRdmFlag(0);//rdm工时不统计
        query.setManagerId(userId);
        query.setSourceFlags(Arrays.asList(1, 2, 3));
        List<WorkingHourResultDto> list = workingHourExtMapper.selectForDisplay(query);
        supply(list);//补充用户名和项目类型

        Map<Integer, Integer> statusMap = new HashMap<>();
        Map<String, String> statusWeekMap = new HashMap<>();
        Date monday, sunday;
        String week;
        for (WorkingHourResultDto dto : list) {
            //rdm同步过来的审批中数据不统计
            if (Objects.equals(dto.getRdmFlag(), 1) && Objects.equals(dto.getStatus(), 2)) {
                continue;
            }
            if (statusMap.get(dto.getStatus()) == null) {
                statusMap.put(dto.getStatus(), 0);
            }
            statusMap.put(dto.getStatus(), statusMap.get(dto.getStatus()) + 1);
            monday = getThisWeekMonday(dto.getApplyDate());
            sunday = DateUtils.addDay(monday, 6);
            week = sdf.format(monday) + " - " + sdf.format(sunday);
            statusWeekMap.put(dto.getStatus() + "_" + week, week);
        }

        JSONArray array = new JSONArray();
        for (Map.Entry<Integer, Integer> statusEntity : statusMap.entrySet()) {
            JSONObject json = new JSONObject();
            json.put(STATUS, statusEntity.getKey());
            json.put("count", statusEntity.getValue());
            json.put("week", new JSONArray());

            for (Map.Entry<String, String> weekEntity : statusWeekMap.entrySet()) {
                if (weekEntity.getKey().startsWith(String.valueOf(statusEntity.getKey()))) {
                    json.getJSONArray("week").add(weekEntity.getValue());
                }
            }
            array.add(json);
        }

        result.put(STATUS, array);

        UserInfo userInfo = CacheDataUtils.findUserById(userId);
        String type = userInfo.getType();//1内；2外；
        if ("1".equals(type)) {//TODO
            result.put(MAXHOURS, 8);
        } else {
            result.put(MAXHOURS, 12);
        }

        return result;
    }

    @Override
    public JSONObject personalWorkinghour(String startDate, String endDate, Long userId) {
        JSONObject result = new JSONObject();
        result.put(USERID, userId);
        WorkingHourQueryDto query = new WorkingHourQueryDto();
        query.setUserId(userId);
        query.setBizUnitId(SystemContext.getUnitId());
        if (StringUtils.isNotBlank(startDate)) {
            try {
                Date sDate = sdf.parse(startDate);
                query.setStartDate(sDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }

        if (StringUtils.isNotBlank(endDate)) {
            try {
                Date eDate = sdf.parse(endDate);
                query.setEndDate(eDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }
        BigDecimal totalApplyWorkinghour = workingHourExtMapper.totalApplyWorkinghour(query);
        BigDecimal totalConfirmWorkinghour = workingHourExtMapper.totalConfirmWorkinghour(query);
        result.put("totalApplyWorkinghour", totalApplyWorkinghour.divide(new BigDecimal(8), 2, BigDecimal.ROUND_HALF_UP));
        result.put("totalConfirmWorkinghour", totalConfirmWorkinghour.divide(new BigDecimal(8), 2, BigDecimal.ROUND_HALF_UP));
        return result;
    }

    @Override
    public List<WorkingHourResultDto> getWorkingHourResult(Long userId, String startDate, String endDate, Long unitId, String dateStr) {
        List<String> dateList = null;
        Date startTime = null;
        Date endTime = null;
        if (StringUtils.isNotEmpty(dateStr)) {
            dateList = Arrays.asList(dateStr.split(","));
        } else if (StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)) {
            startTime = DateUtil.parseDate(startDate);
            endTime = DateUtil.parseDate(endDate);
        } else {
            return Collections.emptyList();
        }
        return workingHourExtMapper.getWorkingHourResultByUserId(userId, startTime, endTime, unitId, dateList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response validImportWorkingHourDetail(List<WorkingHourExcelVo> workingHourExcelVoList, Long unitId) { //工时明细初始化数据校验
        // 导入数据校验，并返回修改校验结果
        unitId = unitId == null ? SystemContext.getUnitId() : unitId;
        boolean result = getValidWorkingHourDetailResults(workingHourExcelVoList, unitId);
        DataResponse<Object> dataResponse = Response.dataResponse();
        if (result) {
            dataResponse.setMsg("SUCCESS");
            List<WorkingHour> workingHourList = BeanConverter.copy(workingHourExcelVoList, WorkingHour.class);
            workingHourList.forEach(wh -> wh.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd"))));
            List<List<WorkingHour>> workingHourLists = ListUtils.splistList(workingHourList, 500);
            workingHourLists.forEach(subList -> workingHourExtMapper.batchInsert(subList));
            return dataResponse;
        }
        dataResponse.setMsg("FAIL");
        return dataResponse.setData(workingHourExcelVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getValidResult())).collect(Collectors.toList()));
    }

    private boolean getValidWorkingHourDetailResults(List<WorkingHourExcelVo> voList, Long company) {

        voList.forEach(e -> {
            if (e.getBizUnitId() == null) {
                e.setBizUnitId(company);
            }
        });

        voList.forEach(e -> {
            if (e.getLaborCostSourceUnitId() == null) {
                e.setLaborCostSourceUnitId(company);
            }
        });

        voList.forEach(c -> c.setValidResult(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (voList.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getValidResult()))) {
            return false;
        }

        List<String> projectCodes = voList.stream().map(WorkingHourExcelVo::getProjectCode)
                .distinct().collect(Collectors.toList());

        //获取昆山使用单位下所有的项目编号
        Map<String, Long> projectMap = getProjectByCode(projectCodes).stream()
                .collect(Collectors.toMap(Project::getCode, Project::getId, (key1, key2) -> key2));

        List<Long> laborCostSourceUnitIds = voList.stream().map(WorkingHourExcelVo::getLaborCostSourceUnitId)
                .distinct().collect(Collectors.toList());

        //查询使用单位下的角色分类
        Map<Long, Map<String, LaborCostTypeDto>> laborCostTypeMap = new HashMap<>();
        getLaborCostTypeList(laborCostSourceUnitIds).forEach(lct -> laborCostTypeMap.computeIfAbsent(lct.getUnitId(), k -> new HashMap<>())
                .put(lct.getName(), lct));

        //查询使用单位下的角色
        Map<Long, Map<String, LaborCostDto>> laborCostMap = new HashMap<>();
        getLaborCosList(laborCostSourceUnitIds).forEach(lc -> laborCostMap.computeIfAbsent(lc.getBizUnitId(), k -> new HashMap<>())
                .put(lc.getName(), lc));


        List<String> joinCodes = voList.stream().map(e -> e.getProjectCode() + "-" + e.getWbsBudgetCode()).distinct().collect(Collectors.toList());
        Map<String, ProjectWbsBudgetDto> projectWbsBudgetMap = projectWbsBudgetService.findByProjectCodeAndWbsFullCode(joinCodes).stream()
                .collect(Collectors.toMap(e -> e.getProjectCode() + "-" + e.getWbsFullCode(), e -> e, (e1, e2) -> e2));

        for (WorkingHourExcelVo wh : voList) {
            List<String> validResultList = new ArrayList<>();
            wh.setStatus(4);
            wh.setIsImport(true);
            wh.setWriteOffStatus(0);
            wh.setDeleteFlag(0);
            wh.setRdmFlag(0);
            wh.setSourceFlag(1);
            if (wh.getCreateAt() == null) {
                wh.setCreateAt(DateUtils.subtractDay(new Date(), 1));
            }
            wh.setApproveTime(wh.getCreateAt());
            wh.setProjectId(projectMap.get(wh.getProjectCode()));
            if (wh.getProjectId() == null) {
                validResultList.add("(项目号-新)不存在");
            }

            UserInfo userInfo = CacheDataUtils.findUserByMip(wh.getUserMip());
            if (userInfo == null) {
                validResultList.add("填报成员姓名MIP账号不存在");
            } else {
                wh.setUserId(userInfo.getId());
                wh.setUserMip(userInfo.getUsername());
                wh.setApplyOrg(cutOrgNamePath(userInfo));
            }

            if ("内部".equals(wh.getUserTypeName())) {
                wh.setUserType("1");
            } else if ("外包".equals(wh.getUserTypeName())) {
                wh.setUserType("2");
            } else {
                validResultList.add("用户类型错误");
            }

            if (StringUtils.isNotEmpty(wh.getLaborCostType())) {
                if (!"role".equals(wh.getLaborCostType()) && !"position".equals(wh.getLaborCostType())) {
                    validResultList.add("费率类型错误");
                }
            } else {
                // 默认值
                wh.setLaborCostType("role");
            }

            if (StringUtils.isNotEmpty(wh.getRoleType())) {
                LaborCostTypeDto lct = laborCostTypeMap.get(wh.getBizUnitId())
                        .get(wh.getRoleType());
                if (lct == null) {
                    validResultList.add("工时角色分类不存在");
                } else {
                    String userType;
                    if (lct.getDepartmentType() == null || lct.getDepartmentType() == 1) {
                        userType = "内部";
                    } else {
                        userType = "外包";
                    }
                    if (!userType.equals(wh.getUserTypeName())) {
                        validResultList.add("工时角色分类和用户类型不匹配");
                    }
                }
            }

            if (StringUtils.isNotEmpty(wh.getLaborWbsCostName())) {
                LaborCostDto lc = laborCostMap.get(wh.getBizUnitId()).get(wh.getLaborWbsCostName());
                if (lc == null) {
                    validResultList.add("wbs工时角色费率不存在");
                } else {
                    wh.setLaborWbsCostId(lc.getId());
                    if (StringUtils.isEmpty(wh.getWbsBudgetCode())) {
                        validResultList.add("wbs号不能为空");
                    } else {
                        ProjectWbsBudgetDto projectWbsBudgetDto = projectWbsBudgetMap.get(wh.getProjectCode() + "-" + wh.getWbsBudgetCode());
                        if (projectWbsBudgetDto == null) {
                            validResultList.add("wbs号不存在");
                        }
                    }
                    if (StringUtils.isNotEmpty(wh.getProjectActivityCode())) {
                        if (!wh.getProjectActivityCode().equals(lc.getProjectActivityCode())) {
                            validResultList.add("活动事项编号不匹配");
                        }
                    } else {
                        wh.setProjectActivityCode(lc.getProjectActivityCode());
                    }
                }
            }

            if (wh.getApplyWorkingHours().scale() > 2) {
                validResultList.add("填报工时数小数位不得超过两位");
            }

            if (wh.getActualWorkingHours().scale() > 2) {
                validResultList.add("实际工时数小数位不得超过两位");
            }

            if (wh.getCostMoney().scale() > 2) {
                validResultList.add("人天单价小数位不得超过两位");
            }
            // 补充wbs全码
            wh.setWbsSummaryCode(wh.getProjectCode() + "-" + wh.getWbsBudgetCode());
            wh.setValidResult(Joiner.on("，").join(validResultList));
        }
        return voList.stream().allMatch(e -> StringUtils.isEmpty(e.getValidResult()));
    }

    private List<LaborCostDto> getLaborCosList(List<Long> unitIds) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "/laborCost/getLaborCostByUnitIds";
        String res = restTemplate.postForObject(url, unitIds, String.class);
        return JSON.parseObject(res, new TypeReference<List<LaborCostDto>>() {
        });
    }

    private List<LaborCostTypeDto> getLaborCostTypeList(List<Long> unitIds) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "/laborCost/getLaborCostTypeByUnitIds";
        String res = restTemplate.postForObject(url, unitIds, String.class);
        return JSON.parseObject(res, new TypeReference<List<LaborCostTypeDto>>() {
        });
    }

    private List<Project> getProjectByCode(List<String> codes) {
        //查询当前使用单位下的项目
        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE);
        criteria.andCodeIn(codes);
        return projectMapper.selectByExample(projectExample);
    }

    private static Date getThisWeekMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }

    @Override
    public List<WorkingHourStatResultDto> query(Long userId, String projectIds, String projectName, String managerName, String userType,
                                                Integer status, Integer businessFlag, String startDate, String endDate, String dateStr) {
        WorkingHourQueryDto query = new WorkingHourQueryDto();

        if (userId != null) {
            List<Long> userIds = new ArrayList<>();
            userIds.add(userId);
            query.setUserIds(userIds);
        }

        if (StringUtils.isNotBlank(projectIds)) {
            query.setProjectIds(Arrays.stream(projectIds.split(",")).map(Long::parseLong).collect(Collectors.toList()));
        }

        if (StringUtils.isNotBlank(userType)) {
            Map<String, Object> param = new HashMap<>();
            param.put(USERID, userId);
            param.put(USERTYPE, userType);

            String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), EMPLOYEEINFOQUERY, param);
            String res = restTemplate.getForEntity(url, String.class).getBody();

            List<Long> userIds = new ArrayList<>();
            JSONArray array = JSONArray.parseArray(res);
            for (int i = 0; i < array.size(); i++) {
                JSONObject json = array.getJSONObject(i);

                if (json.containsKey("id")) {
                    userIds.add(json.getLong("id"));
                }
            }
            if (userIds.size() > 0) query.setUserIds(userIds);
        }

        if (status != null) {
            List<Integer> statusList = new ArrayList<>();
            statusList.add(status);
            query.setStatus(statusList);
        }

        if (StringUtils.isNotBlank(projectName)) {
            query.setProjectName(projectName);
        }

        if (StringUtils.isNotBlank(managerName)) {
            query.setManagerName(managerName);
        }

        if (businessFlag != null) {
            query.setBusinessFlag(businessFlag);
        }

        if (StringUtils.isNotBlank(startDate)) {
            try {
                Date sDate = sdf.parse(startDate);
                query.setStartDate(sDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }

        if (StringUtils.isNotBlank(endDate)) {
            try {
                Date eDate = sdf.parse(endDate);
                query.setEndDate(eDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }

        if (StringUtils.isNotBlank(dateStr)) {
            String[] dates = dateStr.split(",");
            List<String> dateList = new ArrayList<>();
            for (String s : dates) {
                if (StringUtil.isNotNull(s)) {
                    dateList.add(s);
                }
            }
            query.setDates(dateList);
        }
        query.setSourceFlags(Arrays.asList(1, 2, 3));
        List<WorkingHourResultDto> list = workingHourExtMapper.selectForDisplay(query);
        supply(list);//补充用户名和项目类型
        return changeListToJsonArray(userId, query.getProjectIds(), list, query.getStartDate(), query.getEndDate(), query.getDates(),
                true, false, businessFlag, managerName);
    }

    @Override
    public List<WorkingHourStatResultDto> queryForUpdate(Long userId, Long projectId, String projectName, String managerName, String userType,
                                                         Integer status, Integer businessFlag, String startDate, String endDate) {

        WorkingHourQueryDto query = new WorkingHourQueryDto();
        if (userId != null) {
            List<Long> userIds = new ArrayList<>();
            userIds.add(userId);
            query.setUserIds(userIds);
        }
        List<Long> ids = new ArrayList<>();
        ids.add(-1L);//防止空值
        //指定项目
        if (null != projectId && projectId > 0) {
            ids.add(projectId);
            query.setProjectIds(ids);
        }

        if (StringUtils.isNotBlank(userType)) {
            Map<String, Object> param = new HashMap<>();
            param.put(USERID, userId);
            param.put(USERTYPE, userType);

            String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), EMPLOYEEINFOQUERY, param);
            String res = restTemplate.getForEntity(url, String.class).getBody();

            List<Long> userIds = new ArrayList<>();
            JSONArray array = JSONArray.parseArray(res);
            for (int i = 0; i < array.size(); i++) {
                JSONObject json = array.getJSONObject(i);

                if (json.containsKey("id")) {
                    userIds.add(json.getLong("id"));
                }
            }
            if (userIds.size() > 0) query.setUserIds(userIds);
        }

        if (businessFlag != null) {
            query.setBusinessFlag(businessFlag);
        }

        if (StringUtils.isNotBlank(managerName)) {
            query.setManagerName(managerName);
        }

        if (status != null) {
            List<Integer> statusList = new ArrayList<>();
            statusList.add(status);
            query.setStatus(statusList);
        }

        if (StringUtils.isNotBlank(projectName)) {
            query.setProjectName(projectName);
        }

        if (StringUtils.isNotBlank(startDate)) {
            try {
                Date sDate = sdf.parse(startDate);
                query.setStartDate(sDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }

        if (StringUtils.isNotBlank(endDate)) {
            try {
                Date eDate = sdf.parse(endDate);
                query.setEndDate(eDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }

        List<WorkingHourResultDto> list = workingHourExtMapper.selectForDisplay(query);
        supply(list);//补充用户名和项目类型
        return changeListToJsonArray(userId, query.getProjectIds(), list, query.getStartDate(), query.getEndDate(),
                null, true, true, businessFlag, managerName);
    }

    @Override
    public List<WorkingHourStatResultDto> queryApplyHour(Long userId, Long projectId, String projectName, String managerName, String userType,
                                                         Integer businessFlag, String projectStatusStr, String startDate, String endDate) {

        WorkingHourQueryDto query = new WorkingHourQueryDto();
        if (userId != null) {
            List<Long> userIds = new ArrayList<>();
            userIds.add(userId);
            query.setUserIds(userIds);
        }
        List<Long> ids = new ArrayList<>();
        ids.add(-1L);//防止空值
        if (null != projectId && projectId > 0) {
            ids.add(projectId);
            query.setProjectIds(ids);
        }

        if (businessFlag != null) {
            query.setBusinessFlag(businessFlag);
        }

        if (StringUtils.isNotBlank(managerName)) {
            query.setManagerName(managerName);
        }

        if (StringUtils.isNotBlank(userType)) {
            Map<String, Object> param = new HashMap<>();
            param.put(USERID, userId);
            param.put(USERTYPE, userType);

            String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), EMPLOYEEINFOQUERY, param);
            String res = restTemplate.getForEntity(url, String.class).getBody();

            List<Long> userIds = new ArrayList<>();
            JSONArray array = JSONArray.parseArray(res);
            for (int i = 0; i < array.size(); i++) {
                JSONObject json = array.getJSONObject(i);

                if (json.containsKey("id")) {
                    userIds.add(json.getLong("id"));
                }
            }
            if (userIds.size() > 0) query.setUserIds(userIds);
        }

        List<Integer> statusList = new ArrayList<>();
        //申请状态：2审批中、3被驳回、4通过、5变更中
        statusList.add(2);
        statusList.add(3);
        statusList.add(4);
        statusList.add(5);
        query.setStatus(statusList);

        //指定项目状态
        List<Integer> projectStatusList = new ArrayList<>();
        projectStatusList.add(4);
        projectStatusList.add(7);
        projectStatusList.add(9);
        projectStatusList.add(10);
        projectStatusList.add(-3);
        projectStatusList.add(15);
        projectStatusList.add(16);
        query.setProjectStatus(projectStatusList);

        if (StringUtils.isNotBlank(projectName)) {
            query.setProjectName(projectName);
        }

        if (StringUtils.isNotBlank(startDate)) {
            try {
                Date sDate = sdf.parse(startDate);
                query.setStartDate(sDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }

        if (StringUtils.isNotBlank(endDate)) {
            try {
                Date eDate = sdf.parse(endDate);
                query.setEndDate(eDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }
        List<WorkingHourResultDto> list = workingHourExtMapper.selectForDisplay(query);

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Long> projectIds = new ArrayList<>();
        for (WorkingHourResultDto workingHourResultDto : list) {
            if (!projectIds.contains(workingHourResultDto.getProjectId())) {
                projectIds.add(workingHourResultDto.getProjectId());
            }
        }

        supply(list);//补充用户名和项目类型
        return changeListToJsonArray(userId, projectIds, list, query.getStartDate(), query.getEndDate(), query.getDates(),
                true, false, businessFlag, managerName);
    }

    private List<WorkingHourStatResultDto> changeListToJsonArray(Long userId, List<Long> projectIds, List<WorkingHourResultDto> list, Date startDate, Date endDate, List<String> dates,
                                                                 Boolean defaultUserData, Boolean isAllProject, Integer businessFlag, String managerName) {
        Map<String, WorkingHourStatResultDto> userMap = new LinkedHashMap<>();
        List<ProjectDto> projectList2 = new ArrayList<>();
        if (CollectionUtils.isEmpty(projectIds)) {
            //如果不是查询全部，则只查询4-项目进行中 9-项目变更中 7-预立项确认中
            projectList2 = projectService.getProjectByUserId(userId, isAllProject ? null : "4,7,9",
                    isAllProject ? null : businessFlag, isAllProject ? null : managerName, null);
        } else {
            //如果指定了项目
            ProjectExample projectExample = new ProjectExample();
            ProjectExample.Criteria criteria = projectExample.createCriteria();
            criteria.andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(projectIds);
            if (businessFlag != null) {
                if (businessFlag == 1) {
                    criteria.andProjectSourceNotEqualTo(ProjectSource.BUSINESS.getCode());
                } else {
                    criteria.andProjectSourceEqualTo(ProjectSource.BUSINESS.getCode());
                }
            }
            if (StringUtils.isNotBlank(managerName)) {
                criteria.andManagerNameLike("%" + managerName + "%");
            }
            projectList2 = BeanConverter.convert(projectMapper.selectByExample(projectExample), ProjectDto.class);
        }

        Set<Long> userIds = new HashSet<>();
        for (WorkingHourResultDto workingHourResultDto : list) {
            userIds.add(workingHourResultDto.getUserId());
        }

        if (defaultUserData && userIds.isEmpty()) {
            userIds.add(userId);//如果userIds未空，则取当前用户id
        }

        //查询用户在所有项目中的资源计划引入情况
        ProjectMemberExample example = new ProjectMemberExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andUserIdIn(Lists.newArrayList(userIds));
        List<ProjectMember> projectMemberList = projectMemberMapper.selectByExample(example);
        Map<String, List<ProjectMember>> projectMemberMap = new HashMap<>();
        if (ListUtils.isNotEmpty(projectMemberList)) {
            for (ProjectMember projectMember : projectMemberList) {
                List<ProjectMember> memberList = projectMemberMap.get(projectMember.getProjectId() + "-" + projectMember.getUserId());
                if (ListUtils.isEmpty(memberList)) {
                    memberList = new ArrayList<>();
                }
                memberList.add(projectMember);
                projectMemberMap.put(projectMember.getProjectId() + "-" + projectMember.getUserId(), memberList);
            }
        }
        Map<String, List<WorkingHourResultDto>> projectWorkHourMap = new HashMap<>();
        if (ListUtils.isNotEmpty(list) && ListUtils.isNotEmpty(projectList2)) {
            for (WorkingHourResultDto dto : list) {
                projectWorkHourMap.computeIfAbsent(dto.getProjectId() + "_" + dto.getUserId(), e -> new ArrayList<>()).add(dto);
            }
        }

        Map<Long, UserInfo> approveUserMap = new HashMap<>();

        for (Long uid : userIds) {
            UserInfo userInfo = CacheDataUtils.findUserById(uid);
            if (userInfo == null) {
                continue;
            }

            List<Date> dateList = new ArrayList<>(7);
            if (startDate != null && endDate != null) {
                Date date = startDate;
                do {
                    dateList.add(date);
                    date = DateUtils.addDay(date, 1);
                } while (date.compareTo(endDate) <= 0);
            } else {
                for (String dateStr : dates) {
                    dateList.add(DateUtil.parseDate(dateStr));
                }
            }

            //初始化数据
            if (!CollectionUtils.isEmpty(projectList2)) {
                projectList2.sort((e1, e2) -> {
                    int s1 = e1.getProjectSource() != null ? e1.getProjectSource() : 10;
                    int s2 = e2.getProjectSource() != null ? e2.getProjectSource() : 10;
                    return s1 - s2;
                });

                List<Long> projectTypeIds = projectList2.stream().map(ProjectDto::getType).distinct().collect(Collectors.toList());
                Map<Long, ProjectType> projectTypeMap = projectTypeService.getByIds(projectTypeIds).stream().collect(Collectors.toMap(
                        ProjectType::getId, e -> e, (e1, e2) -> e2));

                for (ProjectDto project : projectList2) {
                    WorkingHourStatResultDto statResultDto = new WorkingHourStatResultDto(
                            project.getId(), project.getName(), project.getCode(), project.getManagerId(), project.getManagerName(),
                            project.getStartDate(), uid, userInfo.getName(), null, null, null, null);

                    ProjectType projectType = projectTypeMap.get(project.getType());
                    Asserts.notNull(projectType, ErrorCode.CTC_PROJECT_TYPE_NOT_NULL);
                    statResultDto.setProjectType(projectType.getName());
                    String puKey = project.getId() + "_" + uid;
                    userMap.put(puKey, statResultDto);
                    final List<ProjectMember> projectMembers = projectMemberMap.get(project.getId() + "-" + userId);

                    statResultDto.setWbs(Boolean.TRUE.equals(project.getWbsEnabled()));
                    List<WorkingHourResultDto> projectWorkHourList = projectWorkHourMap.get(puKey);
                    if (ListUtils.isNotEmpty(projectWorkHourList)) {
                        // 柔性分组维度：wbs+角色
                        // 非柔性分组维度：角色
                        Map<String, List<WorkingHourResultDto>> wbsWorkHourMap = projectWorkHourList.stream()
                                .collect(Collectors.groupingBy(e -> e.getWbsBudgetCode() + e.getLaborWbsCostId()));

                        for (List<WorkingHourResultDto> wbsWhList : wbsWorkHourMap.values()) {
                            int i = 0;
                            WorkingHourResultDto wbsWh = new WorkingHourResultDto();
                            wbsWh.setWbsBudgetCode(wbsWhList.get(0).getWbsBudgetCode());
                            wbsWh.setWbsSummaryCode(wbsWhList.get(0).getWbsSummaryCode());
                            wbsWh.setLaborWbsCostId(wbsWhList.get(0).getLaborWbsCostId());
                            wbsWh.setLaborWbsCostName(wbsWhList.get(0).getLaborWbsCostName());
                            wbsWh.setWorkingHourResultDtoList(new ArrayList<>(7));
                            statResultDto.getWorkingHours().add(wbsWh);
                            for (Date date : dateList) {
                                WorkingHourResultDto dto = null;
                                for (WorkingHourResultDto wh : wbsWhList) {
                                    if (wh.getApplyDate().compareTo(date) == 0) {
                                        dto = wh;
                                        break;
                                    }
                                }
                                if (dto == null) {
                                    dto = new WorkingHourResultDto();
                                    dto.setApplyWorkingHours(BigDecimal.ZERO);
                                    dto.setActualWorkingHours(BigDecimal.ZERO);
                                    dto.setWbsBudgetCode(wbsWh.getWbsBudgetCode());
                                    dto.setWbsSummaryCode(wbsWh.getWbsSummaryCode());
                                    dto.setLaborWbsCostId(wbsWh.getLaborWbsCostId());
                                    dto.setLaborWbsCostName(wbsWh.getLaborWbsCostName());
                                }
                                dto.setIndex(i++);
                                wbsWh.getWorkingHourResultDtoList().add(dto);
                                fillWorkHourInfo(dto, date, project, userInfo, projectMembers, projectType);
                                setStayApprove(dto, approveUserMap);
                            }
                        }
                    } else {
                        int i = 0;
                        for (Date date : dateList) {
                            WorkingHourResultDto dto = new WorkingHourResultDto();
                            dto.setApplyWorkingHours(BigDecimal.ZERO);
                            dto.setActualWorkingHours(BigDecimal.ZERO);
                            dto.setIndex(i++);
                            if (statResultDto.getWorkingHours().isEmpty()) {
                                WorkingHourResultDto wbsWh = new WorkingHourResultDto();
                                wbsWh.setWorkingHourResultDtoList(new ArrayList<>(7));
                                statResultDto.getWorkingHours().add(wbsWh);
                            }
                            statResultDto.getWorkingHours().get(0).getWorkingHourResultDtoList().add(dto);
                            fillWorkHourInfo(dto, date, project, userInfo, projectMembers, projectType);
                            setStayApprove(dto, approveUserMap);
                        }
                    }
                }
            }
        }

        //统计总数
        userMap.forEach((k, v) -> {
            int size = v.getWorkingHours().get(0).getWorkingHourResultDtoList().size();
            Map<Long, BigDecimal> whSummary1 = new HashMap<>(size);
            Map<Long, BigDecimal> whSummary2 = new HashMap<>(size);
            BigDecimal total1 = BigDecimal.ZERO;
            BigDecimal total2 = BigDecimal.ZERO;
            for (WorkingHourResultDto wbsWh : v.getWorkingHours()) {
                BigDecimal workHour1 = BigDecimal.ZERO;
                BigDecimal workHour2 = BigDecimal.ZERO;
                for (WorkingHourResultDto wh : wbsWh.getWorkingHourResultDtoList()) {

                    BigDecimal applyWh = wh.getApplyWorkingHours() == null ? BigDecimal.ZERO : wh.getApplyWorkingHours();
                    BigDecimal actualWh = wh.getActualWorkingHours() == null ? BigDecimal.ZERO : wh.getActualWorkingHours();

                    total1 = total1.add(applyWh);
                    total2 = total2.add(actualWh);

                    workHour1 = workHour1.add(applyWh);
                    workHour2 = workHour2.add(actualWh);
                    long time = wh.getApplyDate().getTime();
                    BigDecimal applyBigDecimal = whSummary1.get(time);
                    if (applyBigDecimal == null) {
                        applyBigDecimal = BigDecimal.ZERO;
                    }
                    whSummary1.put(time, applyBigDecimal.add(applyWh));

                    BigDecimal actualBigDecimal = whSummary2.get(time);
                    if (actualBigDecimal == null) {
                        actualBigDecimal = BigDecimal.ZERO;
                    }
                    whSummary2.put(time, actualBigDecimal.add(actualWh));
                }
                wbsWh.setApplyWorkingHours(workHour1);
                wbsWh.setActualWorkingHours(workHour2);
            }
            v.setApplyWorkHourSummary(whSummary1);
            v.setActualWorkHourSummary(whSummary2);
            v.setTotalApplyWorkingHours(total1);
            v.setTotalActualWorkingHours(total2);
        });
        return new ArrayList<>(userMap.values());
    }

    /**
     * 商机项目填报工时是否支持多角色
     */
    private boolean businessProjectEnableMultipleRole() {
        Set<String> values = organizationCustomDictService.queryByName("商机项目填报工时是否支持多角色",
                SystemContext.getUnitId(),
                OrgCustomDictOrgFrom.COMPANY);
        if (values != null && values.size() > 0) {
            return values.contains("1");
        }
        return false;
    }

    /**
     * 工时记录填充相关信息
     *
     * @param dto            ：工时记录
     * @param date           : 工时日期
     * @param project        ：项目信息
     * @param userInfo       ：用户信息
     * @param projectMembers ： 项目成员
     * @param projectType    ： 项目类型信息
     */
    private void fillWorkHourInfo(WorkingHourResultDto dto,
                                  Date date,
                                  ProjectDto project,
                                  UserInfo userInfo,
                                  List<ProjectMember> projectMembers,
                                  ProjectType projectType) {
        dto.setProjectId(project.getId());
        dto.setProjectCreateTime(project.getStartDate());
        dto.setProjectName(project.getName());
        dto.setProjectCode(project.getCode());
        dto.setUserId(userInfo.getId());
        dto.setUserName(userInfo.getName());
        dto.setMipName(userInfo.getUsername());
        dto.setApplyDate(date);
        dto.setRdmFlag(getRdmFlag(project, projectMembers, date));
        dto.setOverDateFlag(getOverDateFlag(projectType, projectMembers, date));
    }

    /**
     * 设置待审批人
     *
     * @param dto            ：工时记录
     * @param approveUserMap ： 审批人
     */
    private void setStayApprove(WorkingHourResultDto dto, Map<Long, UserInfo> approveUserMap) {
        if (dto.getStayApproveUserId() != null) {
            if (approveUserMap.containsKey(dto.getStayApproveUserId())) {
                UserInfo approveUser = approveUserMap.get(dto.getStayApproveUserId());
                if (approveUser != null) {
                    dto.setStayApproveUserName(approveUser.getUsername());
                }
            } else {
                UserInfo approveUser = CacheDataUtils.findUserById(dto.getStayApproveUserId());
                if (approveUser != null) {
                    dto.setStayApproveUserName(approveUser.getUsername());
                }
                approveUserMap.put(dto.getStayApproveUserId(), approveUser);
            }
        }
    }

    /**
     * //如果项目关联资源计划，判断用户是否为资源计划引入，并且判断开始结束时间
     *
     * @param project        : 项目
     * @param projectMembers 项目成员
     * @param iDate          ：工时日期
     * @return rdm 标志
     */
    private Integer getRdmFlag(ProjectDto project, List<ProjectMember> projectMembers, Date iDate) {
        if (StringUtils.isNotBlank(project.getResourceCode()) && ListUtils.isNotEmpty(projectMembers)) {
            for (ProjectMember projectMember : projectMembers) {
                if (!(projectMember.getStartDate() != null && iDate.before(projectMember.getStartDate()))
                        && !(projectMember.getEndDate() != null && iDate.after(projectMember.getEndDate()))) {
                    return (projectMember.getIsFromResource() != null && projectMember.getIsFromResource()) ? 1 : 0;
                }
            }
        }
        return 0;
    }

    /**
     * //如果配置了项目成员投入时间必填,则控制填报工时区间
     *
     * @param projectType    ：项目类型信息
     * @param projectMembers ：项目成员
     * @param iDate          ： 工时日期
     * @return 是否超期
     */
    private int getOverDateFlag(ProjectType projectType, List<ProjectMember> projectMembers, Date iDate) {
        int overDateFlag = 0;
        if (projectType != null && projectType.getValidateProjectMember() != null
                && projectType.getValidateProjectMember()
                && ListUtils.isNotEmpty(projectMembers)) {
            for (ProjectMember projectMember : projectMembers) {
                if (projectMember.getStartDate() == null || projectMember.getEndDate() == null) {
                    continue;
                }
                if (!iDate.before(projectMember.getStartDate()) && !iDate.after(projectMember.getEndDate())) {
                    overDateFlag = 1;
                    break;
                }
            }
        } else {
            overDateFlag = ListUtils.isNotEmpty(projectMembers) ? 1 : 0;
        }
        return overDateFlag;
    }

    private JSONArray monthListToJsonArray(List<WorkingHourResultDto> list, WorkingHourQueryDto query) {
        Map<String, WorkingHourStatResultDto> userMap = new HashMap<>();
        for (WorkingHourResultDto resultDto : list) {
            Long uid = resultDto.getUserId();
            //查询具体明细
            List<Long> userIds = new ArrayList<>();
            List<Long> projectIds = new ArrayList<>();
            userIds.add(uid);
            projectIds.add(resultDto.getProjectId());
            query.setUserIds(userIds);
            query.setProjectIds(projectIds);
            List<WorkingHourResultDto> detailList = workingHourExtMapper.selectForDisplay(query);
            String userType = resultDto.getUserType();//用户类型 1内部 2外部
            String userName = null;
            String roleName = null;
            String hrDepartment = null;
            String vendorName = null;
            if ("1".equals(userType)) {
                UserInfo userInfo = CacheDataUtils.findUserById(uid);
                if (userInfo != null) {
                    userName = userInfo.getName();
                    // 获取人力费率表id
                    Long id = userInfo.getLaborCostTypeSetId();
                    DataResponse<OrgLaborCostTypeSetDTO> laborCostTypeSet = basedataOrgLaborCostTypeSetFeignClient.findOrgLaborCostTypeSetById(id, null);
                    String laborCostName = laborCostTypeSet.getData().getLaborCostName(); //TODO【非柔性角色字段替换】 我的工时-工时统计 改造（目前不兼容多角色）
                    if (laborCostName != null) {
                        roleName = laborCostName;
                    } else {
                        //若HR部门对应的角色为空，取ltc_position中的name字段
                        String positionName = workingHourService.getPositionName(resultDto.getUserId());
                        if (positionName != null) {
                            //角色名称
                            roleName = positionName;
                        }
                    }
                    hrDepartment = laborCostTypeSet.getData().getHrOrgName();
                    vendorName = null;
                }

            } else if ("2".equals(userType)) {
                roleName = resultDto.getRoleName();//角色名称
                hrDepartment = null;
                vendorName = workingHourExtMapper.getVendorName(resultDto.getUserId());
                userName = workingHourExtMapper.getVendorName(resultDto.getUserId());
            }
            Project project = projectMapper.selectByPrimaryKey(resultDto.getProjectId());
            WorkingHourStatResultDto statResultDto = new WorkingHourStatResultDto(project.getId(), project.getName(), project.getCode(), project.getManagerId(), project.getManagerName(), project.getStartDate(), uid, userName, userType, roleName, hrDepartment, vendorName);
            statResultDto.setName(resultDto.getName());
            userMap.put(project.getId() + "_" + uid, statResultDto);
            if (query.getStartDate() != null && query.getEndDate() != null) {
                Date iDate = query.getStartDate();
                int i = 0;
                do {
                    WorkingHourResultDto dto = new WorkingHourResultDto();
                    dto.setProjectId(project.getId());
                    dto.setProjectCreateTime(project.getStartDate());
                    dto.setProjectName(project.getName());
                    dto.setProjectCode(project.getCode());
                    dto.setUserId(uid);
                    dto.setUserName(statResultDto.getUserName());
                    dto.setMipName(resultDto.getUserMip());
                    dto.setApplyDate(iDate);
                    dto.setApplyWorkingHours(BigDecimal.ZERO);
                    dto.setActualWorkingHours(BigDecimal.ZERO);
                    dto.setIndex(i++);
                    statResultDto.getWorkingHours().add(dto);

                    iDate = DateUtils.addDay(iDate, 1);//i++
                }
                while (iDate.getTime() <= query.getEndDate().getTime() && (query.getEndDate().getTime() - iDate.getTime()) < 366L * 24 * 3600 * 1000);
            }

            //循环结果集，改变初始化的数据
            for (WorkingHourResultDto result : detailList) {
                if (statResultDto != null) {
                    for (WorkingHourResultDto workingHour : statResultDto.getWorkingHours()) {
                        if (workingHour.getApplyDate().equals(result.getApplyDate())) {
                            workingHour.setId(result.getId());
                            workingHour.setWeek(result.getWeek());
                            workingHour.setActualWorkingHours(result.getActualWorkingHours());
                            workingHour.setApplyWorkingHours(result.getApplyWorkingHours());
                            workingHour.setStatus(result.getStatus());
                            workingHour.setProcessId(result.getProcessId());
                            workingHour.setLevel(result.getLevel());
                            workingHour.setCostMoney(result.getCostMoney());
                            workingHour.setRemarks(result.getRemarks());
                            workingHour.setDeleteFlag(result.getDeleteFlag());
                            workingHour.setApproveTime(result.getApproveTime());
                            workingHour.setApproveUserId(result.getApproveUserId());
                            workingHour.setApproveUserName(result.getApproveUserName());
                            workingHour.setIsCollection(result.getIsCollection());
                            workingHour.setMipName(result.getMipName());
                        }
                    }
                }
            }

        }

        //统计总数
        userMap.forEach((k, v) -> {
            for (WorkingHourResultDto workingHour : v.getWorkingHours()) {
                v.setTotalApplyWorkingHours(v.getTotalApplyWorkingHours().add(workingHour.getApplyWorkingHours() == null ? BigDecimal.ZERO : workingHour.getApplyWorkingHours()));
                v.setTotalActualWorkingHours(v.getTotalActualWorkingHours().add(workingHour.getActualWorkingHours() == null ? BigDecimal.ZERO : workingHour.getActualWorkingHours()));
            }
        });

        JSONArray array = new JSONArray();
        userMap.forEach((k, v) -> {
            String str = JSON.toJSONString(v);
            if (v.getTotalApplyWorkingHours().compareTo(BigDecimal.ZERO) > 0) {
                array.add(JSON.parseObject(str));
            }
        });

        return array;
    }

    private JSONArray myListToJsonArray(List<WorkingHourResultDto> list) {
        JSONArray array = new JSONArray();
        if (ListUtils.isEmpty(list)) {
            return array;
        }

        Map<String, WorkingHourStatResultDto> userMap = new HashMap<>();
        Map<String, WorkingHourResultDto> wbsWhMap = new HashMap<>(10);

        // 项目id列表
        List<Long> projectIds = list.stream()
                .map(WorkingHourResultDto::getProjectId).distinct().collect(Collectors.toList());

        Map<Long, Project> projectMap = projectService.getProjectByIds(projectIds).stream()
                .collect(Collectors.toMap(Project::getId, e -> e));

        int i = 0;
        for (WorkingHourResultDto result : list) {
            Project project = projectMap.get(result.getProjectId());
            Asserts.notNull(project, Code.ERROR);
            WorkingHourStatResultDto statResultDto = userMap.get(result.getProjectId() + "_" + result.getUserId());
            if (null == statResultDto) {
                statResultDto = new WorkingHourStatResultDto(result.getProjectId(), result.getProjectName(), result.getProjectCode(),
                        result.getManagerId(), result.getManagerName(), result.getProjectCreateTime(), result.getUserId(), result.getUserName(), null, null, null, null);
                userMap.put(result.getProjectId() + "_" + result.getUserId(), statResultDto);
            }
            statResultDto.setMaxHour(result.getMaxHour());
            result.setIndex(i++);

            statResultDto.setWbs(Boolean.TRUE.equals(project.getWbsEnabled()));
            WorkingHourResultDto wbsWh = wbsWhMap.get(result.getProjectId() + result.getUserId() + result.getWbsBudgetCode() + result.getLaborWbsCostId());
            if (wbsWh == null) {
                wbsWh = new WorkingHourResultDto();
                wbsWh.setWbsBudgetCode(result.getWbsBudgetCode());
                wbsWh.setWbsSummaryCode(result.getWbsSummaryCode());
                wbsWh.setLaborWbsCostId(result.getLaborWbsCostId());
                wbsWh.setLaborWbsCostName(result.getLaborWbsCostName());
                wbsWh.setWorkingHourResultDtoList(new ArrayList<>(7));
                statResultDto.getWorkingHours().add(wbsWh);
                wbsWhMap.put(result.getProjectId() + result.getUserId() + result.getWbsBudgetCode() + result.getLaborWbsCostId(), wbsWh);
            }
            wbsWh.getWorkingHourResultDtoList().add(result);
        }

        //统计总数
        userMap.forEach((k, v) -> {
            Map<Long, BigDecimal> whSummary1 = new HashMap<>(7);
            Map<Long, BigDecimal> whSummary2 = new HashMap<>(7);
            BigDecimal total1 = BigDecimal.ZERO;
            BigDecimal total2 = BigDecimal.ZERO;
            for (WorkingHourResultDto wbsWh : v.getWorkingHours()) {
                BigDecimal workHour1 = BigDecimal.ZERO;
                BigDecimal workHour2 = BigDecimal.ZERO;
                for (WorkingHourResultDto wh : wbsWh.getWorkingHourResultDtoList()) {
                    BigDecimal applyWh = wh.getApplyWorkingHours() == null ? BigDecimal.ZERO : wh.getApplyWorkingHours();
                    BigDecimal actualWh = wh.getActualWorkingHours() == null ? BigDecimal.ZERO : wh.getActualWorkingHours();
                    total1 = total1.add(applyWh);
                    total2 = total2.add(actualWh);
                    workHour1 = workHour1.add(applyWh);
                    workHour2 = workHour2.add(actualWh);
                    long time = wh.getApplyDate().getTime();
                    BigDecimal applyBigDecimal = whSummary1.get(time);
                    if (applyBigDecimal == null) {
                        applyBigDecimal = BigDecimal.ZERO;
                    }
                    whSummary1.put(time, applyBigDecimal.add(applyWh));

                    BigDecimal actualBigDecimal = whSummary2.get(time);
                    if (actualBigDecimal == null) {
                        actualBigDecimal = BigDecimal.ZERO;
                    }
                    whSummary2.put(time, actualBigDecimal.add(actualWh));
                }
                wbsWh.setApplyWorkingHours(workHour1);
                wbsWh.setActualWorkingHours(workHour2);
            }
            v.setApplyWorkHourSummary(whSummary1);
            v.setActualWorkHourSummary(whSummary2);
            v.setTotalApplyWorkingHours(total1);
            v.setTotalActualWorkingHours(total2);
        });

        userMap.forEach((k, v) -> {
            String str = JSON.toJSONString(v);
            array.add(JSON.parseObject(str));
        });
        return array;
    }

    @Override
    public JSONArray approveList(Long userId, String startDate, String endDate, String projectIds) {
        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andManagerIdEqualTo(userId);
        List<Long> ids = new ArrayList<>();
        //指定项目
        if (StringUtils.isNotBlank(projectIds)) {
            criteria.andIdIn(Arrays.stream(projectIds.split(",")).map(Long::parseLong).collect(Collectors.toList()));
        }
        List<Project> listProject = projectMapper.selectByExample(projectExample);
        ids.add(-1L);//防止空值
        for (Project project : listProject) {
            ids.add(project.getId());
        }
        WorkingHourExample workingHourExample = new WorkingHourExample();
        workingHourExample.createCriteria().andStayApproveUserIdEqualTo(userId).andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code());
        List<WorkingHour> workingHours = workingHourService.selectByExample(workingHourExample);
        if (ListUtils.isNotEmpty(workingHours)) {
            for (WorkingHour workingHour : workingHours) {
                ids.add(workingHour.getProjectId());
            }
        }
        WorkingHourQueryDto query = new WorkingHourQueryDto();
        query.setProjectIds(ids);

        if (StringUtils.isNotBlank(startDate)) {
            try {
                Date sDate = sdf.parse(startDate);
                query.setStartDate(sDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }

        if (StringUtils.isNotBlank(endDate)) {
            try {
                Date eDate = sdf.parse(endDate);
                query.setEndDate(eDate);
            } catch (ParseException e) {
                logger.error(e.getMessage(), e);
            }
        }

        query.setRdmFlag(0);
        query.setManagerId(userId);
        if (null != SystemContext.getUnitId()) {
            query.setBizUnitId(SystemContext.getUnitId());
        }
        query.setSourceFlags(Arrays.asList(1, 2, 3));
        List<WorkingHourResultDto> list = workingHourExtMapper.selectForDisplay(query);
        supply(list);//补充用户名和项目类型
        return myListToJsonArray(list);
    }

    @Override
    public PageInfo<WorkingHourResultDto> pageForApprove(WorkingHourQueryDto query, Long userId, String projectIdStr, String statusStr) {
        ProjectExample projectExample = new ProjectExample();
        ProjectExample.Criteria criteria = projectExample.createCriteria();
        criteria.andManagerIdEqualTo(userId);
        List<Long> ids = new ArrayList<>();
        //指定项目
        if (StringUtil.isNotNull(projectIdStr)) {
            String[] projectIds = projectIdStr.split(",");
            for (String projectId : projectIds) {
                ids.add(Long.parseLong(projectId));
            }
            criteria.andIdIn(ids);
        }
        List<Project> listProject = projectMapper.selectByExample(projectExample);
        ids.clear();
        ids.add(-1L);//防止空值
        for (Project project : listProject) {
            ids.add(project.getId());
        }
        //商机项目
        WorkingHourExample workingHourExample = new WorkingHourExample();
        workingHourExample.createCriteria().andStayApproveUserIdEqualTo(userId).andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code());
        List<WorkingHour> workingHours = workingHourService.selectByExample(workingHourExample);
        if (ListUtils.isNotEmpty(workingHours)) {
            for (WorkingHour workingHour : workingHours) {
                ids.add(workingHour.getProjectId());
            }
        }
        query.setProjectIds(ids);
        List<Integer> statusList = new ArrayList<>();
        //指定状态
        if (StringUtil.isNotNull(statusStr)) {
            String[] statusArray = statusStr.split(",");
            for (String status : statusArray) {
                statusList.add(Integer.valueOf(status));
            }
        } else {
            statusList.add(2);//2审批中
            statusList.add(5);//5变更中
        }
        query.setStatus(statusList);
        query.setRdmFlag(0);//rdm工时不统计
        query.setManagerId(userId);
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<WorkingHourResultDto> list = workingHourExtMapper.selectForDisplay(query);
        supply(list);//补充用户名和项目类型
        PageInfo<WorkingHourResultDto> workingHourResults = BeanConverter.convertPage(list, WorkingHourResultDto.class);
        return workingHourResults;
    }

    /**
     * 补充用户名和项目类型
     *
     * @param list
     * @return
     */
    private void supply(List<WorkingHourResultDto> list) {
        Map<Long, UserInfo> userInfoMap = new HashMap<>();
        for (WorkingHourResultDto dto : list) {
            UserInfo userInfo = null;
            if (userInfoMap.containsKey(dto.getUserId())) {
                userInfo = userInfoMap.get(dto.getUserId());
            } else {
                userInfo = CacheDataUtils.findUserById(dto.getUserId());
                userInfoMap.put(dto.getUserId(), userInfo);
            }
            if (userInfo != null) {
                dto.setUserName(userInfo.getName());
                dto.setMipName(userInfo.getUsername());
                if ("1".equals(userInfo.getType())) {
                    dto.setUserType("内部");
                } else {
                    dto.setUserType("外部");
                }
            }
        }
    }

    public List<Long> getProjectByUnit() {
        ProjectExample projectExample = new ProjectExample();
        //查询当前使用单位下有权限的业务实体
        List<Long> ouIds = SystemContext.getOus();
        ouIds.add(-1L);//防止空值
        projectExample.createCriteria().andOuIdIn(ouIds).andDeletedFlagEqualTo(Boolean.FALSE)
                .andStatusNotEqualTo(12)//非作废状态
                .andStatusGreaterThanOrEqualTo(4);//大于等于4，表示非草稿，非财务确认中，非财务确认通过，非审批中
        List<Project> projects = projectMapper.selectByExample(projectExample);
        if (CollectionUtils.isEmpty(projects)) {
            return null;
        }
        List<Long> ids = new ArrayList<>();
        for (Project project : projects) {
            ids.add(project.getId());
        }
        return ids;
    }

    private List<Long> getProjectIdByUserId(Long userId) {
        ProjectMemberExample projectMemberExample = new ProjectMemberExample();
        projectMemberExample.createCriteria().andUserIdEqualTo(userId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<ProjectMember> list = projectMemberMapper.selectByExample(projectMemberExample);

        if (list == null || list.size() == 0) {
            return null;
        }

        List<Long> ids = new ArrayList<>();
        for (ProjectMember projectMember : list) {
            ids.add(projectMember.getProjectId());
        }
        return ids;
    }

    private List<Long> getProjectIdByManager(Long userId) {
        List<Long> ids = new ArrayList<>();
        ids.add(-1L);
        List<Integer> projectStatus = new ArrayList<>();
        projectStatus.add(4);//项目进行中
        projectStatus.add(7);//预立项确认中
        projectStatus.add(9);//项目变更中
        projectStatus.add(10);//已结项
        projectStatus.add(-3);//预立项转正驳回
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andManagerIdEqualTo(userId)
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code())
                .andStatusIn(projectStatus);
        List<Project> list = projectMapper.selectByExample(projectExample);

        if (list == null || list.size() == 0) {
            return ids;
        }
        for (Project project : list) {
            ids.add(project.getId());
        }
        return ids;
    }

    private WorkingHourQueryDto getQuery(Long userId, Integer year, Integer month, Long projectId, String userType, String userName) {
        WorkingHourQueryDto query = new WorkingHourQueryDto();
        List<Integer> status = new ArrayList<>();
        //申请状态：2审批中、3被驳回、4通过、5变更中、6变更驳回
        status.add(4);
        query.setStatus(status);
        if (projectId != null) {
            List<Long> ids = new ArrayList<>();
            ids.add(projectId);
            query.setProjectIds(ids);
        }

        if (StringUtils.isNotBlank(userName) || StringUtils.isNotBlank(userType)) {
            Map<String, Object> param = new HashMap<>();
            param.put(USERNAME, userName);
            param.put(USERTYPE, userType);

            String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), EMPLOYEEINFOQUERY, param);
            String res = restTemplate.getForEntity(url, String.class).getBody();

            List<Long> userIds = new ArrayList<>();
            JSONArray array = JSONArray.parseArray(res);
            for (int i = 0; i < array.size(); i++) {
                JSONObject json = array.getJSONObject(i);

                if (json.containsKey("id")) {
                    userIds.add(json.getLong("id"));
                }
            }

            if (userIds.size() > 0) {
                query.setUserIds(userIds);
            } else {
                userIds.add(-2l);
                query.setUserIds(userIds);
            }
        }

        if (year != null && month != null) {
            Date sDate = DateUtils.getBeginTime(year, month);
            Date eDate = DateUtils.getEndTime(year, month);
            query.setStartDate(sDate);
            query.setEndDate(eDate);
        }

        return query;
    }

    @Override
    public PageInfo<WorkingHourStatDto> statProjectAndMemberByMonth(Long userId, Date startDate, Date endDate, Long projectId, String userType, String userName, Integer pageNum, Integer pageSize) {
        if (startDate.compareTo(endDate) > 0) {
            throw new BizException(Code.ERROR, "结束日期不得小于开始日期");
        }

        List<Long> projectIds;
        if (projectId != null) {
            projectIds = new ArrayList<>();
            projectIds.add(projectId);
        } else {
            projectIds = getProjectByUnit();
        }

        List<WorkingHourStatDto> dtoList = statWorkingHourByMonth(projectIds, userType, userName, startDate, endDate);
        return PageUtils.buildPageInfo(dtoList, pageSize, pageNum);
    }

    @Override
    public PageInfo<WorkingHourStatDto> managerProjectAndMemberByMonth(
            Long userId, Date startDate, Date endDate, Long projectId,
            String userType, String userName, Integer pageNum, Integer pageSize) {

        if (startDate.compareTo(endDate) > 0) {
            throw new BizException(Code.ERROR, "结束日期不得小于开始日期");
        }

        List<Long> projectIds;
        if (projectId != null) {
            projectIds = new ArrayList<>();
            projectIds.add(projectId);
        } else {
            projectIds = getProjectIdByManager(userId);
        }

        List<WorkingHourStatDto> workingHourStatList = statWorkingHourByMonth(projectIds, userType, userName, startDate, endDate);
        return PageUtils.buildPageInfo(workingHourStatList, pageSize, pageNum);
    }

    public List<WorkingHourStatDto> statWorkingHourByMonth(List<Long> projectIds, String userType, String userName, Date startDate, Date endDate) {
        Long unitId = SystemContext.getUnitId();

        List<WorkingHourResultDto> workingHourList = workingHourExtMapper.selectToStat(
                projectIds, unitId, userType, startDate, endDate);

        List<WorkingHourStatDto> workingHourStatList = new ArrayList<>();

        if (!workingHourList.isEmpty()) {
            // 按项目及用户分组
            Map<String, List<WorkingHourResultDto>> workHourMap = workingHourList.stream()
                    .collect(Collectors.groupingBy(e -> e.getProjectId() + "," + e.getUserId()));

            Map<Long, Project> projectMap = projectService.getProjectByIds(projectIds).stream()
                    .collect(Collectors.toMap(Project::getId, e -> e));

            List<Long> userIds = workingHourList.stream()
                    .map(WorkingHourResultDto::getUserId).distinct().collect(Collectors.toList());

            Map<Long, UserInfoDto> userMap = employeeInfoFeignClient.getUserByIds(userIds).stream()
                    .collect(Collectors.toMap(UserInfoDto::getId, e -> e));

            // 查询供应商MIP
            Map<Long, VendorMip> vendorMipMap = basedataExtService.getVendorMipList(null).stream().collect(Collectors.toMap(VendorMip::getVendorMipId, Function.identity(), (k1, k2) -> k2));

            for (Map.Entry<String, List<WorkingHourResultDto>> kv : workHourMap.entrySet()) {
                String[] keys = kv.getKey().split(",");
                List<WorkingHourResultDto> whs = kv.getValue();
                // 项目用户维度的工时列表
                List<WorkingHourResultDto> projectUserWhs = kv.getValue();
                Long pid = Long.valueOf(keys[0]);
                Long uid = Long.valueOf(keys[1]);
                UserInfoDto user = userMap.get(uid);
                VendorMip vendorMip = vendorMipMap.get(uid);
                if (userName != null && !user.getName().contains(userName)) {
                    continue;
                }
                Project project = projectMap.get(pid);
                Integer status = project.getStatus();
                if ((status >= 4 || status == -3) && status != 12) {
                    // 项目维度的工时
                    WorkingHourStatDto statDto = new WorkingHourStatDto();
                    statDto.setProjectId(project.getId());
                    statDto.setProjectName(project.getName());
                    statDto.setProjectCode(project.getCode());
                    if (user != null) { //内部工时
                        statDto.setUserId(user.getId());
                        statDto.setName(user.getName());
                        statDto.setHrDepartment(user.getHrOrgName());
//                        statDto.setUserType("1".equals(user.getType()) ? "内部" : "外部");
                    } else if (vendorMip != null) { //外部工时
                        statDto.setVendorName(vendorMip.getVendorName());
//                        statDto.setUserType("外部");
                    }
                    if (!CollectionUtils.isEmpty(whs)) {
                        statDto.setUserName(whs.get(0).getLevel()); //页面对应角色名称
                    }
                    statDto.setWbs(Boolean.TRUE.equals(project.getWbsEnabled()));
                    statDto.setWorkingHourStatDtoList(new ArrayList<>());
                    // 按wbs+费率角色分组
                    Map<String, List<WorkingHourResultDto>> wbsWhGroup = projectUserWhs.stream()
                            .collect(Collectors.groupingBy(e -> e.getWbsBudgetCode() + "," + e.getLaborWbsCostId()));
                    wbsWhGroup.forEach((wbs, wbsWhs) -> {
                        // wbs+费率角色维度的工时
                        WorkingHourStatDto wbsWhStatDto = new WorkingHourStatDto();
                        wbsWhStatDto.setWbsBudgetCode(wbsWhs.get(0).getWbsBudgetCode());
                        wbsWhStatDto.setLaborWbsCostId(wbsWhs.get(0).getLaborWbsCostId());
                        wbsWhStatDto.setLaborWbsCostName(wbsWhs.get(0).getLaborWbsCostName());
                        wbsWhStatDto.initWorkHourByMonth(startDate, endDate);
                        wbsWhStatDto.setWorkingHourStatDtoList(new ArrayList<>());
                        wbsWhs.forEach(wh -> wbsWhStatDto.statWorkHour(startDate, wh.getApplyDate(), wh.getActualWorkingHours()));
                        statDto.getWorkingHourStatDtoList().add(wbsWhStatDto);
                        statDto.statWbsToProject();
                    });
                    workingHourStatList.add(statDto);
                }
            }
        }

        return workingHourStatList;
    }

    @Override
    public Map<Date, List<WorkingHourResultDto>> myProjectAndMemberByMonth(Long userId, String startDate, String endDate, Long projectId) {

        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            WorkingHourQueryDto dto = new WorkingHourQueryDto();
            dto.setBizUnitId(SystemContext.getUnitId());
            if (projectId != null) {
                List<Long> projectIds = new ArrayList<>();
                projectIds.add(projectId);
                dto.setProjectIds(projectIds);
            }
            if (userId != null) {
                List<Long> userIds = new ArrayList<>();
                userIds.add(userId);
                dto.setUserIds(userIds);
            }
            List<Integer> status = new ArrayList<>();
            //工时状态：2审批中、4通过
            status.add(2);
            status.add(4);
            dto.setStatus(status);
            Date beginTime = DateUtils.parse(startDate, DATAFORMAT);
            Date endTime = DateUtils.parse(endDate, DATAFORMAT);
            Assert.isTrue(beginTime.compareTo(endTime) <= 0, "结束日期不得小于开始日期");
            dto.setStartDate(beginTime);
            dto.setEndDate(endTime);
            List<WorkingHourResultDto> list = workingHourExtMapper.selectForDisplay(dto);
            supply(list);//补充用户名和项目类型
            return statMyWorkHourByMonthDate(list);
        } else {
            return Collections.emptyMap();
        }
    }

    private Map<Date, List<WorkingHourResultDto>> statMyWorkHourByMonthDate(List<WorkingHourResultDto> workingHourList) {
        Map<Date, List<WorkingHourResultDto>> whGroupByDay = workingHourList.stream().collect(Collectors.groupingBy(WorkingHourResultDto::getApplyDate));

        Map<Date, List<WorkingHourResultDto>> workHourStatListMap = new HashMap<>(whGroupByDay.size());

        // 项目id列表
        List<Long> projectIds = workingHourList.stream().map(WorkingHour::getProjectId).distinct().collect(Collectors.toList());
        Map<Long, Project> projectMap = projectService.getProjectByIds(projectIds).stream().collect(Collectors.toMap(Project::getId, e -> e));
        whGroupByDay.forEach((date, whs) -> {
            Map<Long, List<WorkingHourResultDto>> projectWhGroup = whs.stream().collect(Collectors.groupingBy(WorkingHourResultDto::getProjectId));
            List<WorkingHourResultDto> workHourStatList = new ArrayList<>(projectWhGroup.size());
            projectWhGroup.forEach((pid, pwhs) -> {
                Project project = projectMap.get(pid);
                Asserts.notNull(project, Code.ERROR);
                boolean isWbs = Boolean.TRUE.equals(project.getWbsEnabled());
                WorkingHourResultDto projectWhs = new WorkingHourResultDto();
                projectWhs.setWbs(isWbs);
                BigDecimal totalPass = BigDecimal.ZERO;
                BigDecimal totalApproval = BigDecimal.ZERO;
                for (WorkingHourResultDto wh : pwhs) {
                    if (wh.getStatus() == 4) {
                        totalPass = totalPass.add(wh.getActualWorkingHours());
                    } else if (wh.getStatus() == 2) {
                        totalApproval = totalApproval.add(wh.getApplyWorkingHours());
                    }
                }
                projectWhs.setProjectName(pwhs.get(0).getProjectName());
                projectWhs.setTotalPass(totalPass);
                projectWhs.setTotalApproval(totalApproval);
                projectWhs.setWorkingHourResultDtoList(pwhs);
                workHourStatList.add(projectWhs);
            });
            if (!workHourStatList.isEmpty()) {
                workHourStatListMap.put(date, workHourStatList);
            }
        });

        return workHourStatListMap;
    }

    @Override
    public PageInfo<WorkingHourStatDto> statProjectAndMemberByYear(Long userId, Integer year, Long projectId,
                                                                   String userType, String userName,
                                                                   Integer pageNum, Integer pageSize) {
        Date startDate = DateUtils.getBeginTime(year, 1);
        Date endDate = DateUtils.getEndTime(year, 12);
        List<Long> projectIds;
        if (projectId != null) {
            projectIds = new ArrayList<>();
            projectIds.add(projectId);
        } else {
            projectIds = getProjectByUnit();
        }

        List<WorkingHourStatDto> dtoList = statWorkingHourByYear(projectIds, userType, userName, startDate, endDate);
        return PageUtils.buildPageInfo(dtoList, pageSize, pageNum);
    }

    @Override
    public List<WorkingHourStatDto> managerProjectAndMemberByYear(Long userId, Integer year, Long projectId, String userType, String userName) {

        Date startDate = DateUtils.getBeginTime(year, 1);
        Date endDate = DateUtils.getEndTime(year, 12);
        List<Long> projectIds;
        if (projectId != null) {
            projectIds = new ArrayList<>();
            projectIds.add(projectId);
        } else {
            projectIds = getProjectIdByManager(userId);
        }

        return statWorkingHourByYear(projectIds, userType, userName, startDate, endDate);
    }

    public List<WorkingHourStatDto> statWorkingHourByYear(List<Long> projectIds, String userType, String userName, Date startDate, Date endDate) {
        Long unitId = SystemContext.getUnitId();

        List<WorkingHourResultDto> workingHourList = workingHourExtMapper.selectToStat(projectIds, unitId, userType, startDate, endDate);

        List<WorkingHourStatDto> workingHourStatDtoList = new ArrayList<>();
        if (workingHourList.isEmpty()) {
            return workingHourStatDtoList;
        }

        // 按项目id及用户分组
        Map<String, List<WorkingHourResultDto>> workHourMap = workingHourList.stream()
                .collect(Collectors.groupingBy(e -> e.getProjectId() + "," + e.getUserId()));

        Map<Long, Project> projectMap = projectService.getProjectByIds(projectIds).stream()
                .collect(Collectors.toMap(Project::getId, e -> e));

        List<Long> userIds = workingHourList.stream()
                .map(WorkingHourResultDto::getUserId).distinct().collect(Collectors.toList());

        Map<Long, UserInfoDto> userMap = employeeInfoFeignClient.getUserByIds(userIds).stream()
                .collect(Collectors.toMap(UserInfoDto::getId, e -> e));

        // 查询供应商MIP
        Map<Long, VendorMip> vendorMipMap = basedataExtService.getVendorMipList(null).stream().collect(Collectors.toMap(VendorMip::getVendorMipId, Function.identity(), (k1, k2) -> k2));

        for (Map.Entry<String, List<WorkingHourResultDto>> kv : workHourMap.entrySet()) {
            String[] keys = kv.getKey().split(",");
            List<WorkingHourResultDto> whs = kv.getValue();
            Long pid = Long.valueOf(keys[0]);
            Long uid = Long.valueOf(keys[1]);
            UserInfoDto user = userMap.get(uid);
            VendorMip vendorMip = vendorMipMap.get(uid);
            if (userName != null && !user.getName().contains(userName)) {
                continue;
            }
            Project project = projectMap.get(pid);
            Integer status = project.getStatus();
            if ((status >= 4 || status == -3) && status != 12) {
                WorkingHourStatDto statDto = new WorkingHourStatDto();
                statDto.setProjectId(project.getId());
                statDto.setProjectName(project.getName());
                statDto.setProjectCode(project.getCode());
                if (user != null) { //内部工时
                    statDto.setUserId(user.getId());
                    statDto.setName(user.getName());
                    statDto.setHrDepartment(user.getHrOrgName());
//                    statDto.setUserType("1".equals(user.getType()) ? "内部" : "外部");
                } else if (vendorMip != null) { //外部工时
                    statDto.setVendorName(vendorMip.getVendorName());
//                    statDto.setUserType("外部");
                }
                if (!CollectionUtils.isEmpty(whs)) {
                    statDto.setUserName(whs.get(0).getLevel()); //页面对应角色名称
                }
                statDto.setWbs(Boolean.TRUE.equals(project.getWbsEnabled()));
                statDto.initWorkHourSum();
                statDto.setWorkingHourStatDtoList(new ArrayList<>());
                // 按wbs+费率角色分组
                Map<String, List<WorkingHourResultDto>> wbsWhGroup = whs.stream()
                        .collect(Collectors.groupingBy(e -> e.getWbsBudgetCode() + "," + e.getLaborWbsCostId()));
                wbsWhGroup.forEach((wbs, wbsWhs) -> {
                    WorkingHourStatDto wbsStat = new WorkingHourStatDto();
                    wbsStat.setWbsBudgetCode(wbsWhs.get(0).getWbsBudgetCode());
                    wbsStat.setLaborWbsCostId(wbsWhs.get(0).getLaborWbsCostId());
                    wbsStat.setLaborWbsCostName(wbsWhs.get(0).getLaborWbsCostName());
                    wbsStat.initWorkHourSum();
                    wbsWhs.forEach(wh -> wbsStat.addWorkHour(wh.getApplyDate(), wh.getActualWorkingHours()));
                    statDto.getWorkingHourStatDtoList().add(wbsStat);
                });
                statDto.statWbsWorkHour();
                workingHourStatDtoList.add(statDto);
            }
        }

        return workingHourStatDtoList;
    }

    @Override
    public String getPositionName(Long userId) {
        String positionName = workingHourExtMapper.getPositionName(userId);
        return positionName;
    }

    /**
     * wbs项目预算校验: 校验预算是否存在及是否超预算
     *
     * @param workingHourDtos ： 工时列表
     */
    @Override
    public Map<String, String> budgetValid(WorkingHourDto[] workingHourDtos) {
        Map<String, String> budgetValidCheckMap = new HashMap<>();

        List<WorkingHourDto> workingHourList = Arrays.stream(workingHourDtos)
                .peek(e -> {
                    if ("".equals(e.getWbsBudgetCode())) {
                        e.setWbsBudgetCode(null);
                    }
                }).collect(Collectors.toList());

        // 根据项目id查询项目信息
        List<Long> projectIds = workingHourList.stream()
                .map(WorkingHourDto::getProjectId).distinct().collect(Collectors.toList());
        Map<Long, Project> projectMap = projectService.getProjectByIds(projectIds)
                .stream().collect(Collectors.toMap(Project::getId, e -> e));

        // 工时类型校验
        workingHourList.forEach(wh -> {
            Project project = projectMap.get(wh.getProjectId());
            Assert.notNull(project, String.format("id为%d的项目不存在", wh.getProjectId()));
            if (Boolean.TRUE.equals(project.getWbsEnabled()) && wh.getWbsBudgetCode() == null) {
                throw new BizException(Code.ERROR, String.format("项目【%s】为wbs项目，只能提交wbs工时", project.getName()));
            }
            if (!Boolean.TRUE.equals(project.getWbsEnabled()) && wh.getWbsBudgetCode() != null) {
                throw new BizException(Code.ERROR, String.format("项目【%s】不是wbs项目，不能提交wbs工时", project.getName()));
            }
        });

        // 根据项目id分组wbs工时
        Map<Long, List<WorkingHourDto>> projectWorkHourList = workingHourList.stream()
                .filter(e -> e.getWbsBudgetCode() != null)
                .collect(Collectors.groupingBy(WorkingHourDto::getProjectId));

        if (!projectWorkHourList.isEmpty()) {
            // 获取工时关联的所有角色费率id
            List<Long> laborWbsCostIdList = workingHourList.stream()
                    .filter(e -> e.getWbsBudgetCode() != null)
                    .map(WorkingHourDto::getLaborWbsCostId).distinct().collect(Collectors.toList());

            String url = ModelsEnum.BASEDATA.getBaseUrl() + "laborCost/getLaborCostByIds";
            // 根据id批量查询角色费率信息
            String res = restTemplate.postForObject(url, laborWbsCostIdList, String.class);
            Assert.notNull(res, "根据id批量查询费率失败");
            List<LaborCostDto> laborCostDtoList = JSON.parseObject(res, new TypeReference<List<LaborCostDto>>() {
            });

            // 以id为key的角色费率 Map
            Map<Long, LaborCostDto> laborCostDtoMap = laborCostDtoList.stream()
                    .collect(Collectors.toMap(LaborCostDto::getId, e -> e));

            laborCostDtoMap.values().forEach(lc -> {
                Assert.notNull(lc.getProjectActivityId(), "角色对应的活动事项缺失，请联系财务维护角色对应的活动事项");
            });

            // 实时统计项目成本信息
            Map<Long, List<ProjectWbsBudgetDto>> projectWbsBudgetMap = new HashMap<>();
            final String url1 = String.format("%sstatistics/project/wbsCost/getProjectWbsCostSummary", ModelsEnum.STATISTICS.getBaseUrl());
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url1, projectIds, String.class);
            DataResponse<Map<Long, List<ProjectWbsBudgetDto>>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<Long, List<ProjectWbsBudgetDto>>>>() {
            });
            if (dataResponse != null && Objects.equals(dataResponse.getCode(), 0) && dataResponse.getData() != null) {
                projectWbsBudgetMap = dataResponse.getData();
            } else {
                throw new BizException(Code.ERROR, "查询项目剩余可用预算出现异常");
            }

            for (Map.Entry<Long, List<WorkingHourDto>> kv : projectWorkHourList.entrySet()) {

                Long projectId = kv.getKey();
                List<WorkingHourDto> projectWhs = kv.getValue();

                Project project = projectMap.get(projectId);
                ProjectType projectType = projectTypeService.selectByPrimaryKey(project.getType());

                // 预算控制信息
                List<Object> budgetControlInfo = getBudgetControlFlag(projectType);
                int budgetControlFlag = (int) budgetControlInfo.get(0);
                BigDecimal proportion = (BigDecimal) budgetControlInfo.get(1);

                // 获取项目wbs预算信息
//                Map<String, List<ProjectWbsBudgetDto>> wbsBudgetDtoMap = projectWbsBudgetService.findDetailWbsBudget(projectId)
//                        .stream().collect(Collectors.groupingBy(ProjectWbsBudgetDto::getWbsFullCode));
                List<ProjectWbsBudgetDto> wbsBudgetDtoList = Optional.ofNullable(projectWbsBudgetMap.get(projectId)).orElse(new ArrayList<>());
                Map<String, List<ProjectWbsBudgetDto>> wbsBudgetDtoMap = wbsBudgetDtoList.stream().collect(Collectors.groupingBy(ProjectWbsBudgetDto::getWbsFullCode));

                // 按WBS+活动事项汇总项目工时*角色费率/8
                Map<String, BigDecimal> workHourCostSum = gatherStrictBudget(projectWhs, laborCostDtoMap, project);
                List<String> budgetWarnList = new ArrayList<>(5);

                for (Map.Entry<String, BigDecimal> entry : workHourCostSum.entrySet()) {
                    String key = entry.getKey();
                    BigDecimal cost = entry.getValue();
                    String[] keys = key.split(",");
                    List<ProjectWbsBudgetDto> projectWbsBudgetDtoList = wbsBudgetDtoMap.get(keys[0]);
                    if (projectWbsBudgetDtoList == null) {
//                        throw new BizException(Code.ERROR, String.format("项目【%s】不存在wbs预算【%s】", project.getName(), keys[0]));
                        budgetValidCheckMap.put(buildBudgetNotExistKey(projectId), String.format("项目【%s】不存在wbs预算【%s】", project.getName(), keys[0]));
                        continue;
                    }
                    ProjectWbsBudgetDto projectWbsBudgetDto = findNearRelation(projectWbsBudgetDtoList, keys[1]);
                    if (projectWbsBudgetDto != null) {
                        logger.info("【WBS工时填报预算校验】项目号：{}，WBS号：{}，活动事项编号：{}，当前填写需要占用的工时成本：{}，剩余可用预算：{}，预算金额：{}，需求预算：{}，在途成本：{}，已发生成本：{}"
                                , project.getCode(), projectWbsBudgetDto.getWbsSummaryCode(), projectWbsBudgetDto.getActivityCode()
                                , cost, projectWbsBudgetDto.getRemainingCost(), projectWbsBudgetDto.getPrice()
                                , projectWbsBudgetDto.getDemandCost(), projectWbsBudgetDto.getOnTheWayCost(), projectWbsBudgetDto.getIncurredCost());
                    } else {
                        logger.info("【WBS工时填报预算校验】项目号：{}，WBS号：{}，活动事项编号：{}，预算不存在"
                                , project.getCode(), keys[0], keys[1]);
                    }

                    // 预算控制级别：提醒
                    if (budgetControlFlag == 1) {
                        // 如果活动事项不存在,直接发送邮件
                        if (projectWbsBudgetDto == null || (projectWbsBudgetDto.getPrice().multiply(proportion)
                                .subtract(projectWbsBudgetDto.getOnTheWayCost())
                                .subtract(projectWbsBudgetDto.getIncurredCost()).compareTo(cost) < 0)) {
                            budgetWarnList.add(key);
                        }
                    }
                    // 预算控制级别：严控
                    if (budgetControlFlag == 2) {
                        // 严控的场景下,活动事项必须存在
                        if (projectWbsBudgetDto == null) {
                            budgetValidCheckMap.put(buildBudgetNotEnoughKey(projectId, keys[0], keys[1]), String.format("预算【WBS：%s，活动事项：%s】不存在", keys[0], keys[1]));
                            continue;
                        }
//                        Assert.notNull(projectWbsBudgetDto, String.format("预算【WBS：%s，活动事项：%s】不存在", keys[0], keys[1]));
                        boolean isOverBudget = projectWbsBudgetDto.getRemainingCost().compareTo(cost) < 0;
//                        Assert.isTrue(!isOverBudget, String.format("WBS：%s，活动事项：%s，预算不足", keys[0], keys[1]));
                        if (isOverBudget) {
                            budgetValidCheckMap.put(buildBudgetNotEnoughKey(projectId, keys[0], keys[1]), String.format("WBS：%s，活动事项：%s，预算不足，请联系项目经理", keys[0], keys[1]));
                        }
                    }
                }
                if (CollectionUtils.isEmpty(budgetValidCheckMap)) {
                    sendBudgetRemainEmailToManager(budgetWarnList, project);
                }
            }
        }
        return budgetValidCheckMap;
    }

    private String buildBudgetNotExistKey(Long projectId) {
        return String.format("buildBudgetNotExistKey_%s", projectId);
    }

    private String buildBudgetNotEnoughKey(Long projectId, String wbsSummaryCode, String projectActivityCode) {
        return String.format("buildBudgetNotEnoughKey_%s_%s_%s", projectId, wbsSummaryCode, projectActivityCode);
    }

    /**
     * 获取项目预算控制信息
     *
     * @param projectType：项目类型信息
     * @return List<Object>：预算控制信息，元素1：预算控制级别：0：不控，1：提醒，2：严控；元素2：提醒级别的预算百分比
     */
    private List<Object> getBudgetControlFlag(ProjectType projectType) {
        List<Object> budgetControlInfo = new ArrayList<>(2);
        JSONObject jsonObject = JSON.parseObject(projectType.getBudgetConfig());
        JSONArray manpower = jsonObject.getJSONArray("manpower");
        List<Map> maps = manpower.toJavaList(Map.class);
        Optional<Map> code = maps.stream().filter(e -> "workhourBudgetControl".equals(e.get("code"))).findFirst();
        if (!code.isPresent()) {
            throw new BizException(Code.ERROR, "预算控制配置信息获取失败");
        }
        Map map = code.get();
        budgetControlInfo.add(map.get("value"));

        BigDecimal proportion;
        try {
            String str = (String) map.get("proportion");
            if (StringUtils.isEmpty(str)) {
                proportion = BigDecimal.valueOf(1L);
            } else {
                BigDecimal proportion_ = new BigDecimal(str);
                proportion = proportion_.divide(new BigDecimal(100), 5, RoundingMode.HALF_UP);
            }
        } catch (Exception e) {
            logger.error("预算控制配置信息：proportion有误");
            throw new BizException(Code.ERROR, "预算控制配置信息：proportion有误");
        }
        budgetControlInfo.add(proportion);
        return budgetControlInfo;
    }

    /**
     * 向项目经理发送预算超支的邮件
     *
     * @param budgetWarnList ： 超支的wbs+活动事件
     * @param project        ：项目信息
     */
    private void sendBudgetRemainEmailToManager(List<String> budgetWarnList, Project project) {
        try {
            if (!budgetWarnList.isEmpty()) {
                String subject = String.format("PAM工时成本超预算提醒：请关注项目：“%s” 工时成本超预算", project.getName());
                StringBuilder content = new StringBuilder("<html><div style='font-size: 12px;'>");
                content.append(String.format("<div>PAM工时成本超预算提醒：请关注项目：“%s” 工时成本</div>", project.getName()));
                content.append(String.format("<div><a href='%s' target='_blank'>点击跳转至PAM处理</a></div>", workingHourUrl + project.getManagerId()));
                content.append(String.format("<div>项目名称：%s</div>", project.getName()));
                content.append(String.format("<div>项目编号：%s</div>", project.getCode()));
                String wbsActivityItems = budgetWarnList.stream().map(e -> {
                    String[] codes = e.split(",");
                    return String.format("WBS：%s + 活动事项：%s", codes[0], codes[1]);
                }).collect(Collectors.joining("、"));
                content.append(String.format("<div>请关注项目预算：%s的预算情况</div>", wbsActivityItems));
                content.append("</div></html>");
                sendEmail(project.getManagerId(), null, NoticeBusinessType.WORKINGHOUR_BUDGET_REMIND.getType(), subject, content.toString());
            }
        } catch (Exception e) {
            logger.error("项目【{}】工时成本超预算提醒邮件发送失败", project.getName(), e);
        }
    }

    /**
     * 发送邮件
     *
     * @param receiveMipAccount ：接收人Mip账号
     * @param receiverId        ：接收人id，无receiveMipAccount，则通过receiverId查询receiverMipAccount
     * @param subject           : 邮件主题
     * @param content           ：邮件内容
     */
    private void sendEmail(Long receiverId, String receiveMipAccount, int businessType, String subject, String content) {
        Email email = new Email();
        email.setFromAddress("pam");
        email.setStatus(0);
        email.setLanguage("zh-CN");
        email.setBusinessType(businessType);
        if (receiveMipAccount == null) {
            UserInfo user = CacheDataUtils.findUserById(receiverId);
            receiveMipAccount = user.getUsername();
        }
        email.setReceiver(receiveMipAccount);
        email.setSubject(subject);
        email.setContent(content);
        email.setDeletedFlag(Boolean.FALSE);
        emailExtMapper.insert(email);
    }

    /**
     * 按WBS+活动事项汇总项目工时*角色费率/8
     *
     * @param workingHourDtoList ： 项目工时列表
     * @param laborCostDtoMap    ： 角色费率
     * @param project            : 项目
     * @return ： 以（wbs编号+活动事项编号）为key的预算汇总信息
     */
    private Map<String, BigDecimal> gatherStrictBudget(List<WorkingHourDto> workingHourDtoList,
                                                       Map<Long, LaborCostDto> laborCostDtoMap,
                                                       Project project) {
        Map<String, BigDecimal> workHourCostSum = new HashMap<>();
        workingHourDtoList.forEach(wh -> {
            LaborCostDto laborCostDto = laborCostDtoMap.get(wh.getLaborWbsCostId());
            Assert.notNull(laborCostDto, String.format("id为%d的角色费率记录为空", wh.getLaborWbsCostId()));
            wh.setProjectActivityCode(laborCostDto.getProjectActivityCode());
            String key = wh.getWbsBudgetCode() + "," + laborCostDto.getProjectActivityCode();
            BigDecimal budgetSum = workHourCostSum.get(key);
            if (budgetSum == null) {
                budgetSum = BigDecimal.ZERO;
            }
            BigDecimal applyWorkHour = BigDecimal.valueOf(wh.getApplyWorkingHours());
            BigDecimal budgetItem = applyWorkHour.multiply(
                    // 是否是研发型项目
                    "3".equals(project.getPriceType()) ?
                            laborCostDto.getInternalDevelopmentCost() : laborCostDto.getInternalProjectCost()
            ).divide(BigDecimal.valueOf(8), 5, RoundingMode.HALF_UP);
            budgetSum = budgetSum.add(budgetItem);
            workHourCostSum.put(key, budgetSum);
        });
        return workHourCostSum;
    }


    /**
     * 从projectWbsBudgetDtoList中查找活动事项编码与targetCode配置的一条记录，不存在相等的则往上查找匹配的父级活动事项
     *
     * @param projectWbsBudgetDtoList ： 项目wbs预算列表
     * @param targetCode              ： 项目活动事项编号
     * @return ：匹配的项目预算
     */
    private ProjectWbsBudgetDto findNearRelation(List<ProjectWbsBudgetDto> projectWbsBudgetDtoList, String targetCode) {
        Optional<ProjectWbsBudgetDto> projectWbsBudgetDto = projectWbsBudgetDtoList.stream()
                .filter(e -> targetCode.startsWith(e.getActivityCode()))
                .max(Comparator.comparingInt(e -> e.getActivityCode().length()));
        boolean present = projectWbsBudgetDto.isPresent();
        return projectWbsBudgetDto.orElse(null);
    }

    private void sendEmailForReject(List<WorkingHourDto> workingHourDtos, String rejectComment) {
        try {
            if (workingHourDtos.isEmpty()) {
                return;
            }

            Map<String, List<WorkingHourDto>> workHourGroup = workingHourDtos.stream()
                    .collect(Collectors.groupingBy(e -> e.getProjectId() + e.getWbsBudgetCode() + e.getLaborWbsCostId()));

            String subject = "PAM工时驳回：请关注项目：“%s”工时驳回情况";
            String content1 = "<div>PAM工时驳回：请关注项目：“%s”工时驳回情况</div>";
            String content2 = "<div><a href='%s' target='_blank'>点击跳转至PAM处理</a></div>";
            String content3 = "<div>项目名称：%s</div>";
            String content4 = "<div>项目编号：%s</div>";
            String content5 = "<div>填报WBS：%s</div>";
            String content6 = "<div>填报角色：%s</div>";
            String content7 = "<div>工时驳回情况：%s</div>";
            String content8 = "<div>驳回意见：%s</div>";

            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

            for (List<WorkingHourDto> whs : workHourGroup.values()) {
                WorkingHourDto wh = whs.get(0);
                String subject_ = String.format(subject, wh.getProjectName());
                Calendar calendar = Calendar.getInstance();
                String applyDates = whs.stream().map(e -> {
                    calendar.setTimeInMillis(Long.parseLong(e.getApplyDate()));
                    return format.format(calendar.getTime()) + " " + e.getApplyWorkingHours() + "h";
                }).collect(Collectors.joining("  "));

                String content_ = "<html><div style='font-size: 12px;'>" +
                        String.format(content1, wh.getProjectName()) +
                        String.format(content2, workingHourUrl + wh.getUserId()) +
                        String.format(content3, wh.getProjectName()) +
                        String.format(content4, wh.getProjectCode()) +
                        String.format(content5, wh.getWbsBudgetCode()) +
                        String.format(content6, wh.getLaborWbsCostName()) +
                        String.format(content7, applyDates) +
                        String.format(content8, rejectComment) +
                        "</div></html>";

                sendEmail(null, wh.getMipName(), NoticeBusinessType.WORKING_REJECT_REMIND.getType(), subject_, content_);
            }
        } catch (Exception e) {
            logger.error("驳回提醒邮件发送失败", e);
        }
    }

    @Override
    public PageInfo<WorkingHourStatDto> statProjectByYear(Long userId, Integer year, Integer pageNum, Integer pageSize) {

        Date startDate = DateUtils.getBeginTime(year, 1);
        Date endDate = DateUtils.getEndTime(year, 12);

        List<WorkingHour> workingHourList = workingHourExtMapper.selectWorkHourByYear(
                userId, SystemContext.getUnitId(), startDate, endDate);

        // 按项目id分组
        Map<Long, List<WorkingHour>> workHourMap = workingHourList.stream()
                .collect(Collectors.groupingBy(WorkingHour::getProjectId));

        // 项目id列表
        List<Long> projectIds = workingHourList.stream()
                .map(WorkingHour::getProjectId).distinct().collect(Collectors.toList());

        Map<Long, Project> projectMap = projectService.getProjectByIds(projectIds).stream()
                .collect(Collectors.toMap(Project::getId, e -> e));

        List<WorkingHourStatDto> workingHourStatDtoList = new ArrayList<>();
        // 工时按项目按月汇总
        workHourMap.forEach((pid, projectWhs) -> {
            Project project = projectMap.get(pid);
            Asserts.notNull(project, Code.ERROR);
            Integer status = project.getStatus();
            if ((status >= 4 || status == -3) && status != 12) {
                WorkingHourStatDto statDto = new WorkingHourStatDto();
                statDto.setProjectId(project.getId());
                statDto.setProjectName(project.getName());
                statDto.setProjectCode(project.getCode());
                statDto.initWorkHourSum();
                statDto.setWbs(Boolean.TRUE.equals(project.getWbsEnabled()));
                statDto.setWorkingHourStatDtoList(new ArrayList<>());
                // 柔性分组维度：wbs+角色
                // 非柔性分组维度：角色
                Map<String, List<WorkingHour>> wbsWhGroup = projectWhs.stream()
                        .collect(Collectors.groupingBy(e -> e.getWbsBudgetCode() + "," + e.getLaborWbsCostId()));
                wbsWhGroup.forEach((wbs, wbsWhs) -> {
                    WorkingHourStatDto wbsStat = new WorkingHourStatDto();
                    wbsStat.setWbsBudgetCode(wbsWhs.get(0).getWbsBudgetCode());
                    wbsStat.setLaborWbsCostId(wbsWhs.get(0).getLaborWbsCostId());
                    wbsStat.setLaborWbsCostName(wbsWhs.get(0).getLaborWbsCostName());
                    wbsStat.initWorkHourSum();
                    wbsWhs.forEach(wh -> wbsStat.addWorkHour(wh.getApplyDate(), wh.getActualWorkingHours()));
                    statDto.getWorkingHourStatDtoList().add(wbsStat);
                });
                statDto.statWbsWorkHour();
                workingHourStatDtoList.add(statDto);
            }
        });
        return PageUtils.buildPageInfo(workingHourStatDtoList, pageSize, pageNum);
    }

    @Override
    public Boolean save(Long workHourId, Long userId, Long projectId, String applyDate, Float applyWorkingHours, String week,
                        String wbsBudgetCode, Long laborWbsCostId, String laborWbsCostName) {
        Boolean completed = projectService.checkIncomeCompleted(projectId);
        if (completed) {
            Project project = projectService.selectByPrimaryKey(projectId);
            throw new BizException(Code.ERROR, project.getName() + "最后一个收入节点对应的里程碑已交付，不允许继续填报工时，如有疑问请联系项目财务.");
        }
        return operation(workHourId, userId, null, projectId, applyDate, 1, week, applyWorkingHours,
                null, null, null, "暂存", null, null, null,
                wbsBudgetCode, laborWbsCostId, laborWbsCostName, null);
    }

    @Override
    public Boolean reset(Long workHourId, Long userId, Long projectId, String applyDate, Float applyWorkingHours, String week,
                         String wbsBudgetCode, Long laborWbsCostId, String laborWbsCostName) {
        WorkingHour workingHour = this.getWorkingHour(workHourId, userId, projectId, applyDate, null,
                wbsBudgetCode, laborWbsCostId, laborWbsCostName);
        //如果是暂存/驳回状态，则删除
        if (workingHour != null && (workingHour.getStatus().equals(1) || workingHour.getStatus().equals(3))) {
            if (workingHour.getId() != null) {
                this.workingHourMapper.deleteByPrimaryKey(workingHour.getId());
            }
        }
        return true;
    }

    @Transactional
    @Override
    public void submit(Long userId, Long unitId, WorkingHourDto[] workingHourDtos) {
        if (userId == null) {
            userId = SystemContext.getUserId();//获取当前用户
        }
        // 并发时候的key值
        String lockKey = Constants.DistributedLockKey.DISTRIBUTED_LOCK_WORKINGHOUR_SUBMIT + userId;
        try {
            if (redisUtilServer.lock(lockKey, RedisContant.EXPIRE_TIME_LONG, RedisContant.EXPIRE_TIME_LONG)) {
                // 参数校验
                checkParam(workingHourDtos);
                // 工时重复性校验
                checkRepeatability(workingHourDtos);
                // 非wbs工时预算校验
                Map<String, String> checkBudgetCanSubmitMap = checkBudgetCanSubmit(workingHourDtos);
                if (!CollectionUtils.isEmpty(checkBudgetCanSubmitMap)) {
                    throw new BizException(ErrorCode.ERROR, Joiner.on("；").join(checkBudgetCanSubmitMap.values()));
                }
                // wbs工时预算校验
                Map<String, String> budgetValidCheckMap = budgetValid(workingHourDtos);
                if (!CollectionUtils.isEmpty(budgetValidCheckMap)) {
                    throw new BizException(ErrorCode.ERROR, Joiner.on("；").join(budgetValidCheckMap.values()));
                }

                for (WorkingHourDto workingHourDto : workingHourDtos) {
                    checkYueyunCanSubmit(userId, workingHourDto.getApplyDate(), unitId);//检测粤云是否允许填报工时
                    Boolean completed = projectService.checkIncomeCompleted(workingHourDto.getProjectId());

                    Project project = projectService.selectByPrimaryKey(workingHourDto.getProjectId());
                    if (project.getStatus() == ProjectStatus.TERMINATIONING.getCode() || project.getStatus() == ProjectStatus.TERMINATION.getCode()) {
                        String msg = project.getCode() + (project.getStatus() == ProjectStatus.TERMINATIONING.getCode() ? "终止审批中" : "已终止");
                        throw new BizException(ErrorCode.ERROR, "采购合同对应项目" + msg + "，不允许继续填报工时");
                    }
                    if (completed) {
                        throw new BizException(Code.ERROR, project.getName() + "最后一个收入节点对应的里程碑已交付，不允许继续填报工时，如有疑问请联系项目财务.");
                    }
                    ProjectType projectType = projectTypeService.selectByPrimaryKey(project.getType());
                    if (projectType != null && Objects.equals(projectType.getProjectMemberDistinct(), Boolean.TRUE)) {
                        checkProjectMemberDistinct(userId, workingHourDto.getApplyDate(), workingHourDto.getProjectId());//检测粤云一个人员一天只能在一个项目中填报工时
                    }

                    operation(null, userId, unitId,
                            workingHourDto.getProjectId(),
                            workingHourDto.getApplyDate(),
                            2,
                            workingHourDto.getWeek(),
                            workingHourDto.getApplyWorkingHours(),
                            null, null,
                            null, "提交审批",
                            workingHourDto.getStayApproveUserId(),
                            workingHourDto.getStayApproveUserMip(),
                            workingHourDto.getSourceFlag(),
                            workingHourDto.getWbsBudgetCode(),
                            workingHourDto.getLaborWbsCostId(),
                            workingHourDto.getLaborWbsCostName(),
                            workingHourDto.getProjectActivityCode());
                }
            }
        } catch (Exception e) {
            logger.error("工时填报异常", e);
            throw new BizException(Code.ERROR, e.getMessage());
        } finally {
            redisUtilServer.unLockByNxValue(lockKey, RedisContant.EXPIRE_TIME_LONG);
        }
    }

    /**
     * 非wbs工时预算校验
     *
     * @param workingHourDtos
     */
    public Map<String, String> checkBudgetCanSubmit(WorkingHourDto[] workingHourDtos) {
        Map<String, String> checkBudgetCanSubmitMap = new HashMap<>();
        if (ArrayUtils.isEmpty(workingHourDtos)) {
            return checkBudgetCanSubmitMap;
        }
        List<Long> projectIdList = Arrays.stream(workingHourDtos).map(WorkingHourDto::getProjectId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(projectIdList)) {
            return checkBudgetCanSubmitMap;
        }
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andIdIn(projectIdList);
        List<Project> projectList = projectMapper.selectByExample(projectExample);
        if (!CollectionUtils.isEmpty(projectList) && Objects.equals(projectList.get(0).getWbsEnabled(), Boolean.TRUE)) {
            return checkBudgetCanSubmitMap;
        }

        //商机项目不校验
        Map<Long, Project> projectMap = projectList.stream().collect(Collectors.toMap(Project::getId, Function.identity()));
        List<WorkingHourDto> checkList = new ArrayList<>();
        List<String> businessProjectList = new ArrayList<>();
        for (WorkingHourDto workingHourDto : workingHourDtos) {
            Project project = projectMap.get(workingHourDto.getProjectId());
            Asserts.notEmpty(project, ErrorCode.CTC_PROJECT_NOT_FIND);
            workingHourDto.setProjectCode(project.getCode());
            ProjectType projectType = projectTypeService.selectByPrimaryKey(project.getType());
            if (projectType != null && projectType.getName().indexOf("商机项目") == -1) {
                checkList.add(workingHourDto);
            } else {
                businessProjectList.add(project.getCode());
            }
        }
        if (CollectionUtils.isEmpty(checkList)) {
            return checkBudgetCanSubmitMap;
        }
        //商机工时与项目工时一并提交的时候，提示用户：xx是商机项目，请单独填报工时
        if (!CollectionUtils.isEmpty(businessProjectList)) {
            throw new BizException(Code.ERROR, String.format("%s是商机项目，请单独填报工时", String.join(",", businessProjectList)));
        }
        WorkingHourDto[] checkArray = checkList.toArray(new WorkingHourDto[checkList.size()]);

        //查询人天价格
        for (WorkingHourDto dto : checkArray) {
            Long userId = Optional.ofNullable(dto.getUserId()).orElse(SystemContext.getUserId());
            String userName = CacheDataUtils.findUserNameById(userId);
            WorkingHour workingHour = this.getWorkingHour(dto.getId(), userId, dto.getProjectId(), dto.getApplyDate(), dto.getSourceFlag(), null, null, null);
//            Guard.notNull(workingHour, "查询用户人天价格失败");
            if (workingHour == null) {
                checkBudgetCanSubmitMap.put(buildCostMoneyNotExistKey(dto.getProjectId(), userId), String.format("项目%s，用户%s，查询用户人天价格失败", dto.getProjectCode(), userName));
            } else {
                dto.setCostMoney(workingHour.getCostMoney());
            }
        }
        if (!checkBudgetCanSubmitMap.isEmpty()) {
            return checkBudgetCanSubmitMap;
        }

        //预算校验
        final String url = String.format("%sstatistics/project/cost/checkBudgetCanSubmitNew", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, checkArray, String.class);
        DataResponse<Map<String, String>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, String>>>() {
        });
        if (dataResponse != null && Objects.equals(dataResponse.getCode(), 0) && dataResponse.getData() != null) {
            checkBudgetCanSubmitMap.putAll(dataResponse.getData());
            return checkBudgetCanSubmitMap;
        } else {
            throw new BizException(Code.ERROR, "填报工时预算校验出现异常");
        }
    }

    @Override
    public void updateLaborCostTypeSetId(WorkingHourResultDto queryDto) {
        // 查询需要更新的数据
        List<WorkingHourResultDto> toUpdateLaborList = workingHourExtMapper.selectToUpdateLaborCostTypeSetId(queryDto);
        if (ListUtils.isEmpty(toUpdateLaborList)) {
            return;
        }
        // 根据人工成本明细id更新未入账状态下的科目
        for (WorkingHourResultDto workingHourResultDto : toUpdateLaborList) {
            WorkingHour workingHour = new WorkingHour();
            workingHour.setId(workingHourResultDto.getId());
            workingHour.setLaborCostTypeSetId(workingHourResultDto.getLaborCostTypeSetId());
            workingHourMapper.updateByPrimaryKeySelective(workingHour);
        }
    }

    @Override
    public Integer noWbsFixLaborCostTypeSetId() {
        return workingHourExtMapper.noWbsFixLaborCostTypeSetId();
    }

    @Override
    public List<OperatingUnit> queryFullPayOuIdList() {
        return workingHourExtMapper.queryFullPayOuIdList();
    }

    @Override
    public Integer initFullPayOuId() {
        return workingHourExtMapper.initFullPayOuId();
    }

    private String buildCostMoneyNotExistKey(Long projectId, Long userId) {
        return String.format("buildCostMoneyNotExistKey_%s_%s", projectId, userId);
    }

    private void checkParam(WorkingHourDto[] workingHourDtos) {
        for (WorkingHourDto workingHourDto : workingHourDtos) {
            if (workingHourDto.getApplyWorkingHours() == null) {
                throw new BizException(Code.ERROR, "申请工时不能为空");
            }
        }
    }

    private void checkRepeatability(WorkingHourDto[] workingHourDtos) {

        Map<String, List<WorkingHourDto>> workingHourGroup = Arrays.stream(workingHourDtos).collect(Collectors.groupingBy(e ->
                e.getProjectId() + e.getApplyDate() + e.getWbsBudgetCode() + e.getLaborWbsCostId()
        ));
        List<WorkingHourDto> repeatWorkingHours = workingHourGroup.values().stream().filter(e -> e.size() > 1)
                .map(e -> e.get(0)).collect(Collectors.toList());
        if (!repeatWorkingHours.isEmpty()) {
            // 项目id列表
            List<Long> projectIds = repeatWorkingHours.stream()
                    .map(WorkingHourDto::getProjectId).distinct().collect(Collectors.toList());

            Map<Long, String> projectMap = projectService.getProjectByIds(projectIds).stream()
                    .collect(Collectors.toMap(Project::getId, Project::getName));

            String errTip = repeatWorkingHours.stream().map(e -> {
                String errItem = String.format("项目：%s，日期：%s", projectMap.get(e.getProjectId()), e.getApplyDate());
                if (StringUtils.isNotEmpty(e.getWbsBudgetCode())) {
                    errItem += "，wbs号：" + e.getWbsBudgetCode();
                }
                if (e.getLaborWbsCostId() != null) {
                    errItem += "，角色：" + e.getLaborWbsCostName();
                }
                return errItem;
            }).collect(Collectors.joining("；"));
            throw new BizException(Code.ERROR, "存在重复工时：" + errTip);
        }
    }

    private void checkProjectMemberDistinct(Long userId, String applyDate, Long projectId) {
        List<WorkingHourDto> workingHourList = workingHourExtMapper.selectProjectMemberDistinct(userId, applyDate, projectId);
        if (ListUtils.isNotEmpty(workingHourList)) {
            throw new BizException(Code.ERROR,
                    "一个人员一天只能在一个项目中填报工时");
        }
    }

    private void checkYueyunCanSubmit(Long userId, String applyDate, Long unitId) {
        //判断当前使用单位是否为粤云
        if (!Objects.equals(commonPropertiesConfig.getYueyunUnitId(), unitId != null ? unitId : SystemContext.getUnitId())) {
            return;
        }
        //如果当前登录用户人员的职级类型为角色
        UserInfo userInfo = CacheDataUtils.findUserById(userId);
        if (userInfo != null && Objects.equals(userInfo.getLaborCostTypeCode(), LaborCostTypeCodeEnum.ROLE.getCode())) {
            //判断填报日期是否存在人力外包RDM项目
            List<Project> projectList = projectExtMapper.selectRdmProjectByApplyDate(userId, applyDate);
            if (ListUtils.isNotEmpty(projectList)) {
                throw new BizException(Code.ERROR,
                        "您属于【" + projectList.get(0).getName() + "】的集团人力外包项目中，只能在该项目中填报工时.");
            }
        }
    }

    @Override
    public Boolean reject(Long id, String remarks) {
        return operation(id, null, null, null, null, 3, null, null,
                null, null, remarks, "审批驳回", null, null,
                null, null, null, null, null);
    }

    @Override
    public Boolean reject(List<WorkingHourDto> workingHourDtoList, String rejectComment) {
        Boolean result = true;
        Set<Long> createBySet = new HashSet<>();
        List<WorkingHourDto> rejectWbsWhList = new ArrayList<>();
        for (WorkingHourDto workingHourDto : workingHourDtoList) {
            Long id = workingHourDto.getId();
            WorkingHour workingHour = this.getWorkingHour(id, null, null, null, null,
                    null, null, null);
            if (null == workingHour) {
                throw new ApplicationBizException(String.format("工时ID：%s不存在", id));
            }
            if (null != workingHour.getCreateBy() && workingHour.getWbsBudgetCode() == null) {
                createBySet.add(workingHour.getCreateBy());
            }
            Long projectId = workingHour.getProjectId();
            if (null == projectId) {
                throw new ApplicationBizException(String.format("工时ID：%s对应的项目ID为空", id));
            }
            Project project = projectMapper.selectByPrimaryKey(projectId);
            if (null == project) {
                throw new ApplicationBizException(String.format("工时ID：%s对应的项目不存在", id));
            }

            Boolean rejectResult = operation(id, null, null, projectId, null, 3, null, null,
                    null, null, rejectComment, "审批驳回", null, null,
                    null, null, null, null, null);
            if (Boolean.TRUE.equals(rejectResult)) {
                if (StringUtils.isNotEmpty(workingHourDto.getWbsBudgetCode())) {
                    workingHourDto.setUserId(workingHour.getUserId());
                    rejectWbsWhList.add(workingHourDto);
                }
            } else {
                result = rejectResult;
            }
        }

        // wbs工时驳回邮件
        sendEmailForReject(rejectWbsWhList, rejectComment);

        for (Long createBy : createBySet) {
            UserInfo userInfo = CacheDataUtils.findUserById(createBy);
            if (null != userInfo) {
                Email email = new Email();
                email.setSubject("PAM系统工时驳回提醒");
                String content = buildRejectHeader(createBy);
                email.setContent(content);
                email.setLanguage("zh-CN");
                email.setReceiver(userInfo.getUsername());
                email.setCreateAt(new Date());
                email.setDeletedFlag(Boolean.FALSE);
                email.setBusinessType(NoticeBusinessType.WORKING_REJECT_REMIND.getType());
                email.setStatus(EmailStatus.TO_DO.getCode());
                email.setFromAddress(milepostNoticeProperties.getEmail().getFrom());
                logger.info("工时驳回的userId：{}的邮件信息：{}", createBy, JSON.toJSONString(email));
                emailExtMapper.insert(email);
            }
        }

        return result;
    }

    @Override
    public Boolean pass(Long id, Float actualWorkingHours, String remarks) {
        return operation(id, null, null, null, null, 4, null, null, null, actualWorkingHours, remarks, "审批通过", null, null, null, null, null, null, null);
    }

    @Override
    public Boolean modify(Long id, Long userId, Long projectId, Float applyWorkingHours) {
        return operation(id, userId, null, projectId, null, 5, null, applyWorkingHours, null, null, null, "变更中", null, null, null, null, null, null, null);
    }

    @Override
    public Boolean modifyReject(Long id, String remarks) {
        return operation(id, null, null, null, null, 6, null, null, null, null, remarks, "变更驳回", null, null, null, null, null, null, null);
    }

    @Override
    public Boolean modifyReturn(Long id) {
        WorkingHour workingHour = this.getWorkingHour(id, null, null, null, null, null, null, null);
        if (null == workingHour) {
            throw new BizException(Code.ERROR, "工时不存在！");
        }
        //修改状态为已通过，并且工时数改为之前的
        workingHour.setStatus(WorkingHourStatus.PASS.getCode());
        workingHour.setApplyWorkingHours(workingHour.getActualWorkingHours());
        workingHourMapper.updateByPrimaryKeySelective(workingHour);

        WorkingHourHistory history = new WorkingHourHistory();
        history.setWeek(workingHour.getWeek());
        history.setApplyDate(workingHour.getApplyDate());
        history.setApplyWorkingHours(workingHour.getApplyWorkingHours());
        history.setStatus(workingHour.getStatus());
        history.setUserId(workingHour.getUserId());
        history.setProjectId(workingHour.getProjectId());
        history.setProcessId(workingHour.getProcessId());
        history.setLevel(workingHour.getLevel());
        history.setCostMoney(workingHour.getCostMoney());
        history.setActualWorkingHours(workingHour.getActualWorkingHours());
        history.setRemarks(workingHour.getRemarks());
        history.setAction("变更撤回");
        workingHourHistoryMapper.insertSelective(history);
        return true;
    }

    @Override
    public List<WorkingHour> selectByExample(WorkingHourExample example) {
        return workingHourMapper.selectByExample(example);
    }

    @Override
    public List<WorkingHour> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        WorkingHourExample example = new WorkingHourExample();
        WorkingHourExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        criteria.andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code());

        return workingHourMapper.selectByExample(example);
    }

    @Override
    public String sendSubmitRemind() {
        List<Integer> projectStatus = new ArrayList<>();
        projectStatus.add(4);//项目进行中
        projectStatus.add(7);//预立项确认中
        projectStatus.add(9);//项目变更中
        projectStatus.add(-3);//预立项转正驳回
        //查询所有项目
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria()
                .andDeletedFlagEqualTo(false)
                .andStatusIn(projectStatus);

        List<Project> projects = projectMapper.selectByExample(projectExample);
        List<Long> projectIds = new ArrayList<>();
        projectIds.add(-1L);
        for (Project project : projects) {
            projectIds.add(project.getId());
        }
        //查询所有项目成员
        ProjectMemberExample projectMemberExample = new ProjectMemberExample();
        projectMemberExample.createCriteria().andProjectIdIn(projectIds).andDeletedFlagEqualTo(false);
        List<ProjectMember> members = projectMemberMapper.selectByExample(projectMemberExample);
        List<Long> userIds = new ArrayList<>();
        List<String> userNames = new ArrayList<>();
        //根据成员id汇总工时
        Hashtable<Long, BigDecimal> userNameAndHours = new Hashtable<>();
        for (ProjectMember member : members) {
            if (member.getUserId() != null) {
                userIds.add(member.getUserId());
                userNameAndHours.put(member.getUserId(), BigDecimal.ZERO);
            }
        }
        //申请状态：1未提交、2审批中、3被驳回、4通过、5变更中、6变更驳回
        List<Integer> status = new ArrayList<>();
        status.add(2);
        status.add(4);
        status.add(5);
        //统计本周所有已填报工时
        Map<String, Date> applyDateBetween = DateUtil.getWeekDate();
        WorkingHourExample workingHourExample = new WorkingHourExample();
        WorkingHourExample.Criteria criteria = workingHourExample.createCriteria();
        criteria.andStatusIn(status).andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code())
                .andApplyDateBetween(applyDateBetween.get(DATE1), applyDateBetween.get(DATE5))
                .andUserIdIn(userIds);
        List<WorkingHour> workingHours = workingHourMapper.selectByExample(workingHourExample);
        //统计每个人的工时
        for (WorkingHour workingHour : workingHours) {
            BigDecimal oldHour = userNameAndHours.get(workingHour.getUserId());
            BigDecimal thisHour = null == workingHour.getActualWorkingHours() ? workingHour.getApplyWorkingHours() : workingHour.getActualWorkingHours();
            userNameAndHours.put(workingHour.getUserId(), oldHour.add(thisHour));
        }
        for (Iterator<Long> iterator = userNameAndHours.keySet().iterator(); iterator.hasNext(); ) {
            Long key = iterator.next();
            BigDecimal totalHour = userNameAndHours.get(key);
            if (totalHour.compareTo(new BigDecimal("40.00")) < 0) {
                UserInfo userInfo = CacheDataUtils.findUserById(key);
                if (userInfo != null) {
                    userNames.add(userInfo.getUsername());
                }
            }
        }
        String response = "";
        if (userNames != null && userNames.size() > 0) {
            String batchTousers = "";
            for (String userName : userNames) {
                batchTousers += userName + ",";
            }
            batchTousers = batchTousers.substring(0, batchTousers.length() - 1);
            //推送通知
            TempMsgPush msg = new TempMsgPush();
            msg.setTitle("工时填报");
            msg.setSummary("您有未填工时，请尽快填报");
            msg.setShareRange(0);
            msg.setOpenType(3);
            msg.setCreateTime(DateUtils.format(new Date()));
            msg.setMc_widget_identifier(Constants.MC_WIDGET_IDENTIFIER_MOBILE);
            msg.setBatchToUsers(batchTousers);
            msg.setJumpUrl(Constants.SUBMIT_JUMP_URL);
            JSONObject object = new JSONObject();
            String dateStr = DateUtil.format(applyDateBetween.get(DATE1), DateUtil.DATE_PATTERN) + ",";
            dateStr += DateUtil.format(applyDateBetween.get(DATE2), DateUtil.DATE_PATTERN) + ",";
            dateStr += DateUtil.format(applyDateBetween.get(DATE3), DateUtil.DATE_PATTERN) + ",";
            dateStr += DateUtil.format(applyDateBetween.get(DATE4), DateUtil.DATE_PATTERN) + ",";
            dateStr += DateUtil.format(applyDateBetween.get(DATE5), DateUtil.DATE_PATTERN);
            object.put("dateStr", dateStr);
            msg.setExtras(object.toString());
            response = meiXinPushMsgService.sendTempMsg(msg);
        }
        return response;
    }

    @Override
    public void sendSubmitRemindByWhiteList(Date startDate, Date endDate) {
        WorkingHourQueryDto query = new WorkingHourQueryDto();
        query.setStartDate(startDate);
        query.setEndDate(endDate);
        List<WorkingHourResultDto> workingHourResultDtoList = basedataExtService.selectSubmitRemind(query);
        if (ListUtils.isEmpty(workingHourResultDtoList)) {
            return;
        }
        Map<String, List<WorkingHourResultDto>> stringListMap =
                workingHourResultDtoList.stream().collect(Collectors.groupingBy(dto -> dto.getMipName()));
        String response = "";
        List<Email> emails = new ArrayList<>();
        for (List<WorkingHourResultDto> workingHourResultDtos : stringListMap.values()) {
            Email email = new Email();
            email.setSubject("PAM工时填报提醒");
            email.setLanguage("zh-CN");
            email.setReceiver(workingHourResultDtos.get(0).getMipName());

            String content = buildContent(workingHourResultDtos);
            email.setContent(content);
            email.setBusinessType(NoticeBusinessType.WORKING_SUBMIT_REMIND.getType());
            email.setDeletedFlag(Boolean.FALSE);
            email.setStatus(EmailStatus.TO_DO.getCode());
            email.setFromAddress(milepostNoticeProperties.getEmail().getFrom());
            emails.add(email);
        }
        logger.info("PAM工时填报提醒邮件待推送数据总数：{}", emails.size());
        // 邮件推送
        noticeService.sendMail(emails);
    }

    private String buildContent(List<WorkingHourResultDto> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        String header = buildTdHref("您存在工时填报异常，避免考核请尽快补填。", workingHourSubmitUrl);
        String table = buildTable(list);
        sb.append(header);

        sb.append(table);
        sb.append("</html>");
        return sb.toString();
    }

    private String buildTable(List<WorkingHourResultDto> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("<table style='line-height: 20px;font-size: 12px;border-collapse:collapse;table-layout: fixed;word-wrap:break-word;width: 1000px;'>");
        sb.append("<tr>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;text-align:center;width:15%;'>序号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;text-align:center;width:40%;'>出勤日期</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;text-align:center;width:30%;'>考勤工时数</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;text-align:center;width:30%;'>填报工时数</td>");
        sb.append("</tr>");
        int n = 1;
        for (WorkingHourResultDto workingHourResultDto : list) {
            sb.append("<tr>");
            sb.append(buildTd(String.valueOf(n++)));
            sb.append(buildTd(DateUtil.format(workingHourResultDto.getApplyDate(), DateUtil.DATE_PATTERN)));
            sb.append(buildTd(workingHourResultDto.getIhrAttendHours() + ""));
            sb.append(buildTd(workingHourResultDto.getApplyWorkingHours() + ""));
            sb.append("</tr>");
        }
        sb.append("</table>");
        return sb.toString();
    }

    private String buildTd(String value) {
        return "<td style='border: #999 1px solid; text-align:center;'>" + (value == null ? "" : value) + "</td>";
    }

    private String buildTdHref(String value, String url) {
        String urlStr = "<p><a href='" + url + "' target='_blank'>" + value + "</a></p>";
        return urlStr;
    }

    @Override
    public String sendApproveRemind() {
        Set<String> userNames = new HashSet<>();//审批人
        //"申请状态：1未提交、2审批中、3被驳回、4通过、5变更中、6变更驳回"
        List<Integer> status = new ArrayList<>();
        status.add(2);
        //统计本周未审批的工时
        Map<String, Date> applyDateBetween = DateUtil.getWeekDate();
        WorkingHourExample workingHourExample = new WorkingHourExample();
        WorkingHourExample.Criteria criteria = workingHourExample.createCriteria();
        criteria.andStatusIn(status).andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code())
                .andApplyDateBetween(applyDateBetween.get(DATE1), applyDateBetween.get(DATE5));
        List<WorkingHour> workingHours = workingHourMapper.selectByExample(workingHourExample);

        for (WorkingHour workingHour : workingHours) {
            //查询项目
            Project project = projectMapper.selectByPrimaryKey(workingHour.getProjectId());
            UserInfo userInfo = CacheDataUtils.findUserById(project.getManagerId());
            if (userInfo != null) {
                userNames.add(userInfo.getUsername());
            }
        }
        String response = "";
        if (userNames != null && userNames.size() > 0) {
            String batchTousers = "";
            for (String userName : userNames) {
                batchTousers += userName + ",";
            }
            batchTousers = batchTousers.substring(0, batchTousers.length() - 1);
            //推送通知
            TempMsgPush msg = new TempMsgPush();
            msg.setTitle("项目工时审批");
            msg.setSummary("请您审批项目成员的工时");
            msg.setShareRange(0);
            msg.setOpenType(3);
            msg.setCreateTime(DateUtils.format(new Date()));
            msg.setMc_widget_identifier(Constants.MC_WIDGET_IDENTIFIER_APPROVE);
            msg.setBatchToUsers(batchTousers);
            msg.setJumpUrl(Constants.APPROVE_JUMP_URL);
            response = meiXinPushMsgService.sendTempMsg(msg);
        }
        return response;
    }

    public static String buildGetUrl(final String baseUrl, final String action, Map<String, Object> param) {
        final StringBuffer url = new StringBuffer(baseUrl).append(action).append("?1=1");
        if (null != param) {
            param.forEach((k, v) -> {
                if (!org.springframework.util.StringUtils.isEmpty(v)) {
                    url.append("&").append(k).append("=").append(v);
                }
            });
        }
        return url.toString();
    }

    public Boolean isOverHour(WorkingHour workingHour, int limitHour) {
        BigDecimal totalHour = workingHour.getApplyWorkingHours();
        WorkingHourExample example = new WorkingHourExample();
        List<Integer> status = new ArrayList<>();
        //申请状态：1未提交、2审批中、3被驳回、4通过、5变更中、6变更驳回
        status.add(Integer.valueOf(WorkHourStatus.CHECKING.code()));
        status.add(Integer.valueOf(WorkHourStatus.PASS.code()));
        status.add(Integer.valueOf(WorkHourStatus.CHANGING.code()));
        example.createCriteria().andApplyDateEqualTo(workingHour.getApplyDate())
                .andStatusIn(status)
                .andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code())
                .andUserIdEqualTo(workingHour.getUserId());
        List<WorkingHour> workingHours = workingHourMapper.selectByExample(example);
        for (WorkingHour wk : workingHours) {
            if (null == workingHour.getId()
                    || (null != workingHour.getId() && !workingHour.getId().equals(wk.getId()))) {
                // 如果工时是变更中，取变更前/变更后的最大值（因为变更有可能审批驳回）
                if (wk.getStatus() == 5 && wk.getApplyWorkingHours().compareTo(wk.getActualWorkingHours()) < 0) {
                    totalHour = totalHour.add(wk.getActualWorkingHours());
                } else {
                    totalHour = totalHour.add(wk.getApplyWorkingHours());
                }
            }
        }
        return totalHour.compareTo(new BigDecimal(limitHour)) > 0;
    }

    /**
     * 取工时填报起始日
     *
     * @param project
     * @return
     */
    private String getWorkingHourStartDate(Project project) {
        String startDate = "";
        if (project.getOuId() == null) {
            return null;
        }
        Set<String> valueSet = organizationCustomDictService.queryByName("工时填报起始日", project.getOuId(),
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.OU.code()));
        if (!CollectionUtils.isEmpty(valueSet)) {
            startDate = valueSet.iterator().next();
        }
        return startDate;
    }


    /**
     * 根据使用单位取工时填报上限
     *
     * @param unitId
     * @return
     */
    private int getLimitHours(Long unitId) {
        String limitHour = "";
        Set<String> valueSet = organizationCustomDictService.queryByName("工时填报小时数上限", unitId,
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.COMPANY.code()));
        if (!CollectionUtils.isEmpty(valueSet)) {
            limitHour = valueSet.iterator().next();
        }
        if (StringUtil.isNotNull(limitHour)) {
            return Integer.parseInt(limitHour);
        }
        return 8;
    }

    @Override
    public void sendWorkingHourRemind() {
        Map<Long, String> map = new HashMap<>();
        //"申请状态：1未提交、2审批中、3被驳回、4通过、5变更中、6变更驳回"
        List<Integer> status = new ArrayList<>();
        status.add(2);
        //统计本周未审批的工时
        WorkingHourExample workingHourExample = new WorkingHourExample();
        WorkingHourExample.Criteria criteria = workingHourExample.createCriteria();
        criteria.andStatusIn(status).andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code()).andRdmFlagEqualTo(0);
        List<WorkingHour> workingHours = workingHourMapper.selectByExample(workingHourExample);

        for (WorkingHour workingHour : workingHours) {
            //查询项目
            Project project = projectMapper.selectByPrimaryKey(workingHour.getProjectId());
            if (project != null && project.getManagerId() != null) {
                UserInfo userInfo = CacheDataUtils.findUserById(project.getManagerId());
                if (userInfo != null) {
                    map.put(project.getManagerId(), userInfo.getUsername());
                }
            }
        }
        List<Email> emails = new ArrayList<>();
        List<Backlog> backlogs = new ArrayList<>();
        if (map != null && map.size() > 0) {
            Set<Map.Entry<Long, String>> entries = map.entrySet();
            for (Map.Entry<Long, String> entry : entries) {
                //推送通知
                Email email = new Email();
                email.setSubject("PAM系统待审核工时提醒");
                String content = buildHeader(entry.getKey());
                email.setContent(content);
                email.setLanguage("zh-CN");
                email.setReceiver(entry.getValue());
                email.setCreateAt(new Date());
                email.setDeletedFlag(Boolean.FALSE);
                email.setBusinessType(NoticeBusinessType.WORKING_REMIND.getType());
                email.setStatus(EmailStatus.TO_DO.getCode());
                email.setFromAddress(milepostNoticeProperties.getEmail().getFrom());
                emails.add(email);

                Backlog backlog = new Backlog();
                backlog.setSubject("PAM系统待审核工时提醒");
                backlog.setMobileFlag(Boolean.FALSE);
                backlog.setDeletedFlag(Boolean.FALSE);
                backlog.setStatus(EmailStatus.TO_DO.getCode());
                backlog.setReceiver(entry.getValue());
                backlog.setBusinessType(NoticeBusinessType.WORKING_REMIND.getType());
                backlog.setLongSubject("您有待审核的工时，请登录PAM系统处理。如已处理请忽略");
                backlogs.add(backlog);
            }
        }

        // 邮件推送
        noticeService.sendMail(emails);
        // 待办推送
        noticeService.sendBacklog(backlogs);
    }

    private String buildHeader(Long userId) {
        String url = workingHourUrl + userId;
        String urlStr = "<a href='" + url + "' target='_blank'>您有待审核的工时，请登录PAM系统处理。如已处理请忽略</a>";
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        urlStr = "<div style='font-size: 12px;'>" + urlStr + "</div><br/>";
        sb.append(urlStr);
        sb.append("</html>");
        return sb.toString();
    }

    private String buildRejectHeader(Long userId) {
        String url = workingHourSubmitUrl + "?active=submit&userId=" + userId;
        String urlStr = "<a href='" + url + "' target='_blank'>您有被驳回的工时，请登录PAM系统处理。如已处理请忽略</a>";
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        urlStr = "<div style='font-size: 12px;'>" + urlStr + "</div><br/>";
        sb.append(urlStr);
        sb.append("</html>");
        return sb.toString();
    }

    @Override
    public Map<String, Boolean> isExistMultiRoleProject() {
        Long userId = SystemContext.getUserId();
        if (userId == null) {
            throw new BizException(Code.ERROR, "用户信息查找失败");
        }
        Map<String, Boolean> res = new HashMap<>(1);
        Integer flag = workingHourExtMapper.checkIsExistsWbsProject(userId);
        if (flag != null) {
            res.put("isMultiRole", true);
            return res;
        }
        List<String> values = workingHourExtMapper.selectOrgValueByUserId(userId);
        if (ListUtils.isNotEmpty(values)) {
            Optional<String> first = values.stream().filter("1"::equals).findFirst();
            if (first.isPresent()) {
                res.put("isMultiRole", true);
                return res;
            }
        }
        res.put("isMultiRole", false);
        return res;
    }

    @Override
    public List<Map<String, Object>> getWbsByProjectId(Long projectId, Long userId) {
        List<Map<String, Object>> mapList = projectExtMapper.getWbsByProjectId(projectId, userId);
        return mapList;
    }
}
