package com.midea.pam.ctc.service.listener;

import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.enums.AuditStatus;
import com.midea.pam.common.enums.PaymentApplyEsbStatus;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.service.event.PaymentApplyWorkflowCallbackAbandonEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

public class PaymentApplyWorkflowCallbackAbandonListener implements ApplicationListener<PaymentApplyWorkflowCallbackAbandonEvent> {

    private final static Logger logger = LoggerFactory.getLogger(PaymentApplyWorkflowCallbackAbandonListener.class);

    private final static Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private PaymentApplyService paymentApplyService;
    @Resource
    private WorkflowCallbackService workflowCallbackService;

    
    @Transactional(rollbackFor = Exception.class)
    @Override
    @Async
    public void onApplicationEvent(PaymentApplyWorkflowCallbackAbandonEvent event) {
        logger.info("付款申请审批作废回调的异步处理参数为:{}", JsonUtils.toString(event));

        pass(event.getFormInstanceId(), event.getFdInstanceId(), event.getFormUrl(), event.getEventName(), event.getHandlerId(),
                event.getCompanyId(), event.getCreateUserId());
    }
    
    @Transactional(rollbackFor = Exception.class)
    public void pass(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                     Long createUserId) {
        logger.info("付款申请审批作废回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("PaymentApplyWorkflowCallback_abandonCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    final PaymentApply paymentApply = paymentApplyService.findById(formInstanceId);
                    Guard.notNull(paymentApply, "付款申请审批作废回调 formInstanceId对应的付款申请不存在，不处理");

                    if (Objects.equals(paymentApply.getAuditStatus(), AuditStatus.CANCEL.getCode())) {
                        return;
                    }
                    //只有草稿、驳回、esb同步异常,状态可以作废
//                    if (!(AuditStatus.REFUSE.getCode() == paymentApply.getAuditStatus()
//                            || AuditStatus.DRAFT.getCode() == paymentApply.getAuditStatus()
//                            || PaymentApplyEsbStatus.ABNORMAL.getCode().equals(paymentApply.getEsbStatus()))) {
//                        return;
//                    }

                    paymentApply.setAuditStatus(AuditStatus.CANCEL.getCode());
                    paymentApplyService.update(paymentApply);
                    //释放发票行/发票头
                    paymentApplyService.cancelApply(paymentApply);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            false);
                } catch (ApplicationBizException e) {
                    logger.info("付款申请审批作废回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("付款申请审批作废回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("付款申请审批作废回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }
}
