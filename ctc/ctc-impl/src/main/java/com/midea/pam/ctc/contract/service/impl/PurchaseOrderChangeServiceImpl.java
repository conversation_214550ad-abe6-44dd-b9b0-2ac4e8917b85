package com.midea.pam.ctc.contract.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import com.google.common.collect.Maps;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderChangeHistoryDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderChangeRecordDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailChangeHistoryDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderMergeChangeHistoryDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderReceiptsDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderTitleDto;
import com.midea.pam.common.ctc.dto.SaveBatchPurchaseOrderRecordDto;
import com.midea.pam.common.ctc.dto.SavePurchaseOrderChangeRecordDto;
import com.midea.pam.common.ctc.dto.StandardTermsDto;
import com.midea.pam.common.ctc.entity.CtcAttachment;
import com.midea.pam.common.ctc.entity.CtcAttachmentExample;
import com.midea.pam.common.ctc.entity.PurchaseBpaPrice;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import com.midea.pam.common.ctc.entity.PurchaseOrder;
import com.midea.pam.common.ctc.entity.PurchaseOrderChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseOrderChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderChangeRecord;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetail;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderDetailExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderMerge;
import com.midea.pam.common.ctc.entity.PurchaseOrderMergeChangeHistory;
import com.midea.pam.common.ctc.entity.PurchaseOrderMergeChangeHistoryExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderMergeExample;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsExample;
import com.midea.pam.common.ctc.entity.StandardTermsDeviation;
import com.midea.pam.common.ctc.entity.StandardTermsDeviationExample;
import com.midea.pam.common.ctc.query.PurchaseBpaPriceQuery;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.FreezeFlag;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.OrderStatusEnum;
import com.midea.pam.common.enums.PricingTypeEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.PurchaseOrderChangeRecordStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderDetailStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderStatus;
import com.midea.pam.ctc.common.enums.PurchaseOrderSyncStatus;
import com.midea.pam.ctc.contract.service.PurchaseOrderChangeService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.CtcAttachmentMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderChangeRecordExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderChangeRecordMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderDetailMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderExtMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderMergeChangeHistoryMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderMergeMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderStandardTermsMapper;
import com.midea.pam.ctc.mapper.StandardTermsDeviationMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.PurchaseBpaPriceService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.PurchaseOrderService;
import com.midea.pam.ctc.service.SdpBuyersService;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class PurchaseOrderChangeServiceImpl implements PurchaseOrderChangeService {

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;
    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;
    @Resource
    private PurchaseOrderExtMapper purchaseOrderExtMapper;
    @Resource
    private PurchaseOrderMergeMapper purchaseOrderMergeMapper;
    @Resource
    private PurchaseOrderChangeRecordMapper purchaseOrderChangeRecordMapper;
    @Resource
    private PurchaseOrderChangeHistoryMapper purchaseOrderChangeHistoryMapper;
    @Resource
    private PurchaseOrderDetailChangeHistoryMapper purchaseOrderDetailChangeHistoryMapper;
    @Resource
    private PurchaseOrderMergeChangeHistoryMapper purchaseOrderMergeChangeHistoryMapper;
    @Resource
    private CtcAttachmentMapper ctcAttachmentMapper;
    @Resource
    private PurchaseOrderChangeRecordExtMapper purchaseOrderChangeRecordExtMapper;
    @Resource
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private PurchaseBpaPriceService purchaseBpaPriceService;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private SdpBuyersService sdpBuyersService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private PurchaseMaterialRequirementMapper purchaseMaterialRequirementMapper;
    @Resource
    private PurchaseOrderStandardTermsMapper purchaseOrderStandardTermsMapper;
    @Resource
    private StandardTermsDeviationMapper standardTermsDeviationMapper;


    @Resource
    private PurchaseOrderDetailChangeHistoryExtMapper purchaseOrderDetailChangeHistoryExtMapper;

    @Resource
    private PurchaseMaterialRequirementService purchaseMaterialRequirementService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveRecord(SavePurchaseOrderChangeRecordDto purchaseOrderRecordDto) {
        Long recordId = null;

        SaveBatchPurchaseOrderRecordDto purchaseOrderReceiptsDtoListAll = purchaseOrderRecordDto.getPurchaseOrderReceiptsDtoListAll();
        freezeCheck(purchaseOrderReceiptsDtoListAll);
        if (StringUtils.isEmpty(purchaseOrderRecordDto.getId())) {
            //保存记录
            PurchaseOrderChangeRecord purchaseOrderChangeRecord = new PurchaseOrderChangeRecordDto();
            purchaseOrderChangeRecord.setChangeId(SystemContext.getUserId());
            purchaseOrderChangeRecord.setChangeName(SystemContext.getUserName());
            purchaseOrderChangeRecord.setStatus(PurchaseOrderChangeRecordStatus.WAIT_ENABLE.code());
            purchaseOrderChangeRecord.setSyncStatus(PurchaseOrderSyncStatus.NOT_SYNCED.code());
            purchaseOrderChangeRecord.setChangeContent(purchaseOrderRecordDto.getChangeContent());
            purchaseOrderChangeRecord.setChangeType(purchaseOrderRecordDto.getChangeType());
            purchaseOrderChangeRecord.setDeletedFlag(false);
            purchaseOrderChangeRecordMapper.insertSelective(purchaseOrderChangeRecord);
            recordId = purchaseOrderChangeRecord.getId();

            //保存原数据
            saveOriginPurchaseOrder(purchaseOrderRecordDto, recordId);
            //保存变更数据
            saveChangePurchaseOrder(recordId, purchaseOrderRecordDto);
        } else {      // 二次编辑
            //变更记录表;
            PurchaseOrderChangeRecord purchaseOrderChangeRecordResult = new PurchaseOrderChangeRecordDto();
            BeanUtils.copyProperties(purchaseOrderRecordDto, purchaseOrderChangeRecordResult);
            purchaseOrderChangeRecordResult.setId(purchaseOrderRecordDto.getId());
            purchaseOrderChangeRecordMapper.updateByPrimaryKeySelective(purchaseOrderChangeRecordResult);

            //判断是否审批中了
            PurchaseOrderChangeRecord purchaseOrderChangeRecord = purchaseOrderChangeRecordMapper.selectByPrimaryKey(purchaseOrderRecordDto.getId());
            if (purchaseOrderChangeRecord != null
                    && String.valueOf(PurchaseOrderStatus.PENDING.code()).equals(String.valueOf(purchaseOrderChangeRecord.getStatus()))) {
                throw new MipException("不能重复提交审批");
            }
            if (purchaseOrderChangeRecord == null) {
                throw new MipException("不存在变更记录");
            }
            //删除原数据
            deleteOriginPurchaseOrder(purchaseOrderRecordDto.getId(), HistoryType.HISTORY.getCode());
            //删除变更数据
            deleteChangePurchaseOrder(purchaseOrderRecordDto.getId(), HistoryType.CHANGE.getCode());
            //保存原数据
            saveOriginPurchaseOrder(purchaseOrderRecordDto, purchaseOrderRecordDto.getId());
            //保存变更数据
            saveChangePurchaseOrder(purchaseOrderRecordDto.getId(), purchaseOrderRecordDto);

            recordId = purchaseOrderRecordDto.getId();
        }
        return recordId;
    }

    private void freezeCheck(SaveBatchPurchaseOrderRecordDto purchaseOrderReceiptsDtoListAll) {
        List<String> freezeErrorMessages = new ArrayList<>();
        List<PurchaseOrderReceiptsDto> purchaseOrderReceiptsDtoList = purchaseOrderReceiptsDtoListAll.getPurchaseOrderReceiptsDtoList();
        for (PurchaseOrderReceiptsDto purchaseOrderReceiptsDto : purchaseOrderReceiptsDtoList) {
            List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = purchaseOrderReceiptsDto.getPurchaseOrderDetailDtoList();
            for (PurchaseOrderDetailDto purchaseOrderDetailDto : purchaseOrderDetailDtoList) {
                Long id = purchaseOrderDetailDto.getRequirementId();
                PurchaseMaterialRequirement requirement = purchaseMaterialRequirementMapper.selectByPrimaryKey(id);
                if (requirement != null){
                    if (requirement.getFreezeFlag().equals(FreezeFlag.FREEZE.getCode())){
                        freezeErrorMessages.add(String.format("[%s]的[%s]冻结状态为已冻结,不可进行变更", requirement.getRequirementCode(), requirement.getPamCode()));
                    }
                }
            }
        }
        if (!freezeErrorMessages.isEmpty()) {
            throw new BizException(Code.ERROR, String.join(";", freezeErrorMessages));
        }
    }

    //保存变更数据到记录表
    private void saveChangePurchaseOrder(Long recordId, SavePurchaseOrderChangeRecordDto purchaseOrderRecordDto) {
        //变更后头数据
        checkErpBuyerId(purchaseOrderRecordDto.getPurchaseOrderReceiptsDtoListAll().getPurchaseOrderReceiptsDtoList());
        List<PurchaseOrderReceiptsDto> purchaseOrderReceiptsDtoList =
                purchaseOrderRecordDto.getPurchaseOrderReceiptsDtoListAll().getPurchaseOrderReceiptsDtoList();
        for (PurchaseOrderReceiptsDto purchaseOrderReceiptsDto : purchaseOrderReceiptsDtoList) {
            List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = purchaseOrderReceiptsDto.getPurchaseOrderDetailDtoList();

            PurchaseOrderTitleDto purchaseOrderTitleDto = purchaseOrderReceiptsDto.getPurchaseOrderTitle();
            PurchaseOrderChangeHistory purchaseOrderChangeHistoryAfter = new PurchaseOrderChangeHistory();
            BeanUtils.copyProperties(purchaseOrderTitleDto, purchaseOrderChangeHistoryAfter);
            purchaseOrderChangeHistoryAfter.setRecordId(recordId);
            purchaseOrderChangeHistoryAfter.setHistoryType(HistoryType.CHANGE.getCode());
            purchaseOrderChangeHistoryAfter.setDeletedFlag(false);
            purchaseOrderChangeHistoryAfter.setOriginId(purchaseOrderChangeHistoryAfter.getId());

            purchaseOrderChangeHistoryAfter.setProjectId(purchaseOrderDetailDtoList.get(0).getProjectId());
            if (!StringUtils.isEmpty(purchaseOrderDetailDtoList.get(0).getVendorId())) {
                purchaseOrderChangeHistoryAfter.setVendorAslId(Long.valueOf(purchaseOrderDetailDtoList.get(0).getVendorId()));
            } else if (!StringUtils.isEmpty(purchaseOrderDetailDtoList.get(0).getVendorAslId())) {
                purchaseOrderChangeHistoryAfter.setVendorAslId(purchaseOrderDetailDtoList.get(0).getVendorAslId());
            }
            purchaseOrderChangeHistoryAfter.setSource("2");// 来源:非wbs:1;wbs:2
            purchaseOrderChangeHistoryAfter.setId(null);
            purchaseOrderChangeHistoryAfter.setOrderStatus(PurchaseOrderStatus.WAIT_ENABLE.code());
            purchaseOrderChangeHistoryAfter.setSyncStatus(0);
            purchaseOrderChangeHistoryAfter.setCurrency(purchaseOrderTitleDto.getCurrencyCode());
            purchaseOrderChangeHistoryAfter.setOuId(purchaseOrderTitleDto.getProjectOuId());
            purchaseOrderChangeHistoryMapper.insertSelective(purchaseOrderChangeHistoryAfter);

            //变更后行数据
            for (PurchaseOrderDetailDto purchaseOrderDetailDto : purchaseOrderDetailDtoList) {
                List<PurchaseOrderDetailDto> purchaseOrderDetailDtoChildList = purchaseOrderDetailDto.getChildren();

                PurchaseOrderDetailChangeHistory purchaseOrderDetailChangeHistory = new PurchaseOrderDetailChangeHistoryDto();
                BeanUtils.copyProperties(purchaseOrderDetailDto, purchaseOrderDetailChangeHistory);
                if (!StringUtils.isEmpty(purchaseOrderDetailDto.getVendorId())) {
                    purchaseOrderDetailDto.setVendorAslId(Long.valueOf(purchaseOrderDetailDto.getVendorId()));
                } else if (!StringUtils.isEmpty(purchaseOrderDetailDto.getVendorAslId())) {
                    purchaseOrderDetailDto.setVendorAslId(purchaseOrderDetailDto.getVendorAslId());
                }
                purchaseOrderDetailChangeHistory.setVendorNum(Optional.ofNullable(purchaseOrderDetailDto.getVendorNum()).orElse(purchaseOrderDetailDto.getVendorCode()));
                purchaseOrderDetailChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
                purchaseOrderDetailChangeHistory.setRecordId(recordId);
                purchaseOrderDetailChangeHistory.setDeletedFlag(false);
                if (purchaseOrderDetailDto.getRequirementId() != null) {
                    purchaseOrderDetailChangeHistory.setMaterialPurchaseRequirementId(purchaseOrderDetailDto.getRequirementId());
                } else {
                    purchaseOrderDetailChangeHistory.setMaterialPurchaseRequirementId(purchaseOrderDetailDto.getMaterialPurchaseRequirementId());
                }
                purchaseOrderDetailChangeHistory.setOriginId(purchaseOrderDetailDto.getId());

                purchaseOrderDetailChangeHistory.setId(null);
                purchaseOrderDetailChangeHistory.setPurchaseOrderId(purchaseOrderChangeHistoryAfter.getId());
                if (ListUtils.isNotEmpty(purchaseOrderDetailDtoChildList)) {
                    purchaseOrderDetailChangeHistory.setMergeRows(1);
                } else {
                    purchaseOrderDetailChangeHistory.setMergeRows(0);
                    // 新增数据
                    if (purchaseOrderDetailChangeHistory.getOriginId() == null) {
                        // 校验采购需求未下达量是否超
                        purchaseOrderService.checkRequirementNum(purchaseOrderDetailChangeHistory.getErpCode(),
                                purchaseOrderDetailChangeHistory.getMaterialPurchaseRequirementId(), purchaseOrderDetailChangeHistory.getOrderNum());
                    }
                }
                //purchaseOrderDetailChangeHistory.setLineNumber();
                purchaseOrderDetailChangeHistoryMapper.insertSelective(purchaseOrderDetailChangeHistory);

                // 保存行合并变更
                for (int j = 0; purchaseOrderDetailDtoChildList != null && j < purchaseOrderDetailDtoChildList.size(); j++) {
                    PurchaseOrderMergeChangeHistory purchaseOrderMergeChangeHistory = new PurchaseOrderMergeChangeHistoryDto();
                    BeanUtils.copyProperties(purchaseOrderDetailDtoChildList.get(j), purchaseOrderMergeChangeHistory);
                    purchaseOrderMergeChangeHistory.setPurchaseOrderId(purchaseOrderDetailChangeHistory.getId());
                    purchaseOrderMergeChangeHistory.setHistoryType(HistoryType.CHANGE.getCode());
                    purchaseOrderMergeChangeHistory.setRecordId(recordId);
                    purchaseOrderMergeChangeHistory.setDeletedFlag(false);

                    purchaseOrderMergeChangeHistory.setOriginId(purchaseOrderDetailDtoChildList.get(j).getId());

                    purchaseOrderMergeChangeHistory.setId(null);
                    purchaseOrderMergeChangeHistoryMapper.insertSelective(purchaseOrderMergeChangeHistory);
                    // 新增数据
                    if (purchaseOrderMergeChangeHistory.getOriginId() == null) {
                        // 校验采购需求未下达量是否超
                        purchaseOrderService.checkRequirementNum(purchaseOrderMergeChangeHistory.getErpCode(),
                                purchaseOrderMergeChangeHistory.getMaterialPurchaseRequirementId(), purchaseOrderMergeChangeHistory.getOrderNum());
                    }
                }
            }
        }
    }

    private void checkErpBuyerId(List<PurchaseOrderReceiptsDto> purchaseOrderReceiptsDtoList) {
        purchaseOrderReceiptsDtoList.forEach(e -> {
            PurchaseOrderTitleDto title = e.getPurchaseOrderTitle();
            if (title.getErpBuyerId() != null) {
                if (!sdpBuyersService.checkBuyerIdIsEffective(title.getErpBuyerId())) {
                    throw new BizException(Code.ERROR, String.format("订单%s的ERP采购员无效，不允许变更", title.getNum()));
                }
            }
        });
    }

    //保存原数据到记录表
    private void saveOriginPurchaseOrder(SavePurchaseOrderChangeRecordDto purchaseOrderRecordDto, Long recordId) {

        //保存变更说明文件
        List<CtcAttachmentDto> ctcAttachmentDtoChangeList = purchaseOrderRecordDto.getCtcAttachmentList();
        for (CtcAttachmentDto ctcAttachmentDto : ctcAttachmentDtoChangeList) {
            CtcAttachment ctcAttachment = new CtcAttachment();
            BeanUtils.copyProperties(ctcAttachmentDto, ctcAttachment);
            ctcAttachment.setModuleId(recordId);
            ctcAttachment.setModule(CtcAttachmentModule.PURCHASE_ORDER_CHANGE.code());
            ctcAttachment.setDeletedFlag(false);
            ctcAttachment.setId(null);
            ctcAttachmentMapper.insertSelective(ctcAttachment);
        }

        List<PurchaseOrderReceiptsDto> purchaseOrderReceiptsDtoList =
                purchaseOrderRecordDto.getPurchaseOrderReceiptsDtoListAll().getPurchaseOrderReceiptsDtoList();

        for (PurchaseOrderReceiptsDto purchaseOrderReceiptsDto : purchaseOrderReceiptsDtoList) {
            PurchaseOrderTitleDto purchaseOrderTitleDto = purchaseOrderReceiptsDto.getPurchaseOrderTitle();
            PurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(purchaseOrderTitleDto.getId());

            PurchaseOrderDetailExample purchaseOrderDetailExample = new PurchaseOrderDetailExample();
            purchaseOrderDetailExample.createCriteria().andPurchaseOrderIdEqualTo(purchaseOrder.getId());
            List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderDetailMapper.selectByExample(purchaseOrderDetailExample);

            //变更前头数据
            PurchaseOrderChangeHistory purchaseOrderChangeHistory = new PurchaseOrderChangeHistory();
            BeanUtils.copyProperties(purchaseOrder, purchaseOrderChangeHistory);
            purchaseOrderChangeHistory.setCurrencyCode(purchaseOrder.getCurrency());
            purchaseOrderChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
            purchaseOrderChangeHistory.setOriginId(purchaseOrder.getId());
            purchaseOrderChangeHistory.setRecordId(recordId);
            purchaseOrderChangeHistory.setDeletedFlag(false);
            purchaseOrderChangeHistory.setId(null);
            purchaseOrderChangeHistoryMapper.insertSelective(purchaseOrderChangeHistory);

            //变更前行数据
            for (PurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetailList) {
                PurchaseOrderMergeExample purchaseOrderMergeExample = new PurchaseOrderMergeExample();
                purchaseOrderMergeExample.createCriteria().andPurchaseOrderIdEqualTo(purchaseOrderDetail.getId());
                List<PurchaseOrderMerge> purchaseOrderMergeList = purchaseOrderMergeMapper.selectByExample(purchaseOrderMergeExample);

                PurchaseOrderDetailChangeHistory purchaseOrderDetailChangeHistory = new PurchaseOrderDetailChangeHistoryDto();
                BeanUtils.copyProperties(purchaseOrderDetail, purchaseOrderDetailChangeHistory);
                purchaseOrderDetailChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                purchaseOrderDetailChangeHistory.setOriginId(purchaseOrderDetail.getId());
                purchaseOrderDetailChangeHistory.setRecordId(recordId);
                purchaseOrderDetailChangeHistory.setPurchaseOrderId(purchaseOrderChangeHistory.getId());
                purchaseOrderDetailChangeHistory.setId(null);
                purchaseOrderDetailChangeHistoryMapper.insertSelective(purchaseOrderDetailChangeHistory);

                for (PurchaseOrderMerge purchaseOrderMerge : purchaseOrderMergeList) {
                    PurchaseOrderMergeChangeHistory purchaseOrderMergeChangeHistory = new PurchaseOrderMergeChangeHistoryDto();
                    BeanUtils.copyProperties(purchaseOrderMerge, purchaseOrderMergeChangeHistory);
                    purchaseOrderMergeChangeHistory.setHistoryType(HistoryType.HISTORY.getCode());
                    purchaseOrderMergeChangeHistory.setRecordId(recordId);
                    purchaseOrderMergeChangeHistory.setOriginId(purchaseOrderMerge.getId());
                    purchaseOrderMergeChangeHistory.setPurchaseOrderId(purchaseOrderDetailChangeHistory.getId());
                    purchaseOrderMergeChangeHistory.setDeletedFlag(false);
                    purchaseOrderMergeChangeHistory.setId(null);
                    purchaseOrderMergeChangeHistoryMapper.insertSelective(purchaseOrderMergeChangeHistory);
                }
            }

       /*     List<CtcAttachmentDto> ctcAttachmentDtoList = purchaseOrderReceiptsDto.getCtcAttachmentList();
            //保存采购文件
            for (CtcAttachmentDto ctcAttachmentDto : ctcAttachmentDtoList) {
                CtcAttachment ctcAttachment = new CtcAttachment();
                BeanUtils.copyProperties(ctcAttachmentDto,ctcAttachment);
                ctcAttachment.setModuleId(purchaseOrderChangeHistoryAfter.getId());
                ctcAttachment.setModule(CtcAttachmentModule.PURCHASE_ORDER.code());
                ctcAttachment.setDeletedFlag(false);
                ctcAttachment.setId(null);
                ctcAttachmentMapper.insertSelective(ctcAttachment);
            }*/
        }
    }

    //删除记录表变更数据
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteChangePurchaseOrder(Long recordId, Integer historyType) {
        //删除变更文件
        CtcAttachmentExample ctcAttachmentExample = new CtcAttachmentExample();
        ctcAttachmentExample.createCriteria().andModuleEqualTo(CtcAttachmentModule.PURCHASE_ORDER_CHANGE.code())
                .andModuleIdEqualTo(recordId);
        List<CtcAttachment> ctcAttachmentList = ctcAttachmentMapper.selectByExample(ctcAttachmentExample);
        for (CtcAttachment ctcAttachment : ctcAttachmentList) {
            ctcAttachmentMapper.deleteByPrimaryKey(ctcAttachment.getId());
        }
        purchaseOrderChangeRecordExtMapper.deleteChangeHistory(recordId, historyType);
        purchaseOrderChangeRecordExtMapper.deleteDetailChangeHistory(recordId, historyType);
        purchaseOrderChangeRecordExtMapper.deleteMergeChangeHistory(recordId, historyType);
    }

    //删除记录表原数据
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOriginPurchaseOrder(Long recordId, Integer historyType) {
        //删除变更文件
       /* CtcAttachmentExample ctcAttachmentExample = new CtcAttachmentExample();
        ctcAttachmentExample.createCriteria().andModuleEqualTo(CtcAttachmentModule.PURCHASE_ORDER_CHANGE.code())
                .andModuleIdEqualTo(recordId);
        List<CtcAttachment> ctcAttachmentList = ctcAttachmentMapper.selectByExample(ctcAttachmentExample);
        for (CtcAttachment ctcAttachment : ctcAttachmentList) {
            ctcAttachmentMapper.deleteByPrimaryKey(ctcAttachment.getId());
        }*/
        //purchaseOrderChangeRecordExtMapper.deleteRecord(recordId,historyType);
        purchaseOrderChangeRecordExtMapper.deleteChangeHistory(recordId, historyType);
        purchaseOrderChangeRecordExtMapper.deleteDetailChangeHistory(recordId, historyType);
        purchaseOrderChangeRecordExtMapper.deleteMergeChangeHistory(recordId, historyType);
    }

    //删除并保存订单表数据
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteAndSavePurchaseOrder(Long recordId) {
        //保存订单变更数据
        PurchaseOrderChangeHistoryExample purchaseOrderChangeHistoryExample_change = new PurchaseOrderChangeHistoryExample();
        purchaseOrderChangeHistoryExample_change.createCriteria()
                .andRecordIdEqualTo(recordId)
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        List<PurchaseOrderChangeHistory> purchaseOrderChangeHistoryList_change =
                purchaseOrderChangeHistoryMapper.selectByExample(purchaseOrderChangeHistoryExample_change);
        for (PurchaseOrderChangeHistory purchaseOrderChangeHistory : purchaseOrderChangeHistoryList_change) {
            //purchaseOrderMapper.insert(purchaseOrder);
            PurchaseOrderDetailChangeHistoryExample purchaseOrderDetailChangeHistoryExample = new PurchaseOrderDetailChangeHistoryExample();
            purchaseOrderDetailChangeHistoryExample.createCriteria()
                    .andPurchaseOrderIdEqualTo(purchaseOrderChangeHistory.getId())
                    .andRecordIdEqualTo(recordId)
                    .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
            List<PurchaseOrderDetailChangeHistory> purchaseOrderDetailChangeHistoryList =
                    purchaseOrderDetailChangeHistoryMapper.selectByExample(purchaseOrderDetailChangeHistoryExample);

            int lineNumber = 0;
            Long originId = 0L;
            for (PurchaseOrderDetailChangeHistory purchaseOrderDetailChangeHistory : purchaseOrderDetailChangeHistoryList) {
                if (!StringUtils.isEmpty(purchaseOrderDetailChangeHistory.getOriginId())) {
                    originId = purchaseOrderDetailChangeHistory.getOriginId();
                }
            }
            PurchaseOrderDetail purchaseOrderDetail_purchaseOrderId = purchaseOrderDetailMapper.selectByPrimaryKey(originId);

            //查询订单行数量;
            PurchaseOrderDetailExample purchaseOrderDetailExample = new PurchaseOrderDetailExample();
            purchaseOrderDetailExample.createCriteria()
                    .andDeletedFlagEqualTo(false)
                    .andPurchaseOrderIdEqualTo(purchaseOrderDetail_purchaseOrderId.getPurchaseOrderId());
            List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderDetailMapper.selectByExample(purchaseOrderDetailExample);
            if (ListUtils.isNotEmpty(purchaseOrderDetailList)) {
                lineNumber = purchaseOrderDetailList.size();
            }

            for (PurchaseOrderDetailChangeHistory purchaseOrderDetailChangeHistory : purchaseOrderDetailChangeHistoryList) {
                if (StringUtils.isEmpty(purchaseOrderDetailChangeHistory.getOriginId())) {
                    lineNumber += 1;
                    PurchaseOrderDetail purchaseOrderDetail = new PurchaseOrderDetail();
                    BeanUtils.copyProperties(purchaseOrderDetailChangeHistory, purchaseOrderDetail);
                    purchaseOrderDetail.setId(null);
                    purchaseOrderDetail.setPurchaseOrderId(purchaseOrderDetail_purchaseOrderId.getPurchaseOrderId());
                    purchaseOrderDetail.setMaterialPurchaseRequirementId(purchaseOrderDetailChangeHistory.getMaterialPurchaseRequirementId());
                    purchaseOrderDetail.setLineNumber(lineNumber);
                    purchaseOrderDetail.setDeletedFlag(false);
                    purchaseOrderDetail.setMergeRows(Objects.equals(purchaseOrderDetailChangeHistory.getMergeRows(), 1));
                    purchaseOrderDetail.setStatus(PurchaseOrderDetailStatus.ORDER_PLACED.code());
                    //变更新增的行数据才有变更记录id;
                    purchaseOrderDetail.setRecordId(recordId);
                    purchaseOrderDetailMapper.insert(purchaseOrderDetail);

                    PurchaseOrderMergeChangeHistoryExample purchaseOrderMergeChangeHistoryExample = new PurchaseOrderMergeChangeHistoryExample();
                    purchaseOrderMergeChangeHistoryExample.createCriteria()
                            .andPurchaseOrderIdEqualTo(purchaseOrderDetailChangeHistory.getId())
                            .andRecordIdEqualTo(recordId)
                            .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
                    List<PurchaseOrderMergeChangeHistory> purchaseOrderMergeChangeHistoryList =
                            purchaseOrderMergeChangeHistoryMapper.selectByExample(purchaseOrderMergeChangeHistoryExample);
                    for (PurchaseOrderMergeChangeHistory purchaseOrderMergeChangeHistory : purchaseOrderMergeChangeHistoryList) {
                        PurchaseOrderMerge purchaseOrderMerge = new PurchaseOrderMerge();
                        BeanUtils.copyProperties(purchaseOrderMergeChangeHistory, purchaseOrderMerge);
                        purchaseOrderMerge.setId(null);
                        purchaseOrderMerge.setPurchaseOrderId(purchaseOrderDetail.getId());
                        purchaseOrderMerge.setMergeRows(1);
                        purchaseOrderMerge.setDeletedFlag(false);
                        purchaseOrderMergeMapper.insert(purchaseOrderMerge);
                    }

                    //合并表冗余start
                    if (CollectionUtils.isEmpty(purchaseOrderMergeChangeHistoryList)) {
                        PurchaseOrderMerge purchaseOrderMergeRedundant = new PurchaseOrderMerge();
                        BeanUtils.copyProperties(purchaseOrderDetail, purchaseOrderMergeRedundant);
                        //purchaseOrderMergeRedundant.setMaterialPurchaseRequirementId(purchaseOrderDetail.getId());
                        purchaseOrderMergeRedundant.setId(null);
                        purchaseOrderMergeRedundant.setDeletedFlag(Boolean.FALSE);
                        purchaseOrderMergeRedundant.setPurchaseOrderId(purchaseOrderDetail.getId());
                        purchaseOrderMergeRedundant.setCancelNum(new BigDecimal(0));
                        purchaseOrderMergeRedundant.setMergeRows(0);
                        purchaseOrderMergeMapper.insertSelective(purchaseOrderMergeRedundant);
                    }
                    // end

                }
            }
            // 更新同步状态
            PurchaseOrder purchaseOrder = new PurchaseOrder();
            purchaseOrder.setId(purchaseOrderChangeHistory.getOriginId());
            purchaseOrder.setSyncStatus(PurchaseOrderSyncStatus.CHANGE_IN_SYNC.code());
            purchaseOrderMapper.updateByPrimaryKeySelective(purchaseOrder);
            // erp推送;
            HandleDispatcher.route(BusinessTypeEnums.PURCHASE_ORDER_CHANGE.getCode(), String.valueOf(purchaseOrderChangeHistory.getOriginId()),
                    String.valueOf(purchaseOrderChangeHistory.getRecordId()), null, true, null, null);
        }
    }

    @Override
    public ResponseMap getMobileApprovalChangeDetail(Long recordId) {
        SavePurchaseOrderChangeRecordDto changeRecord = changeDetails(recordId, null);
        ResponseMap responseMap = new ResponseMap();
        if (Objects.isNull(changeRecord) || Objects.isNull(changeRecord.getPurchaseOrderReceiptsDtoListAll())) {
            responseMap.setMsg("未查询到变更记录!");
            responseMap.setStatus("fail");
            return responseMap;
        }
        // 变更历史数据
        SaveBatchPurchaseOrderRecordDto PurchaseOrderRecordDtoAll = changeRecord.getPurchaseOrderReceiptsDtoListAll();
        List<PurchaseOrderReceiptsDto> orderReceiptsDtoList = PurchaseOrderRecordDtoAll.getPurchaseOrderReceiptsDtoList();

        for (PurchaseOrderReceiptsDto orderReceiptsDto : orderReceiptsDtoList) {
            PurchaseOrderDto orderChangeHistory = orderReceiptsDto.getPurchaseOrderDto();
            Map<String, String> headMap = Maps.newHashMap();
            // 变更原因
            headMap.put("changeReason", changeRecord.getChangeContent());
            headMap.put("changeType", changeRecord.getChangeType());
            // 基础信息
            headMap.put("num", orderChangeHistory.getNum());
            headMap.put("pricingType", PricingTypeEnum.getValue(orderChangeHistory.getPricingType()));
            String dispatchIs = BooleanUtil.isTrue(orderChangeHistory.getDispatchIs()) ? "是" : "否";
            headMap.put("dispatchIs", dispatchIs);
            headMap.put("buyerName", orderChangeHistory.getBuyerName());
            headMap.put("erpBuyerName", purchaseOrderService.getErpBuyerName(orderChangeHistory.getErpBuyerId()));
            headMap.put("vendorCode", orderChangeHistory.getVendorNum());
            headMap.put("vendorName", orderChangeHistory.getVendorName());
            headMap.put("vendorSiteCode", orderChangeHistory.getVendorSiteCode());
            headMap.put("currencyCode", orderChangeHistory.getCurrency());
            headMap.put("conversionType", orderChangeHistory.getConversionType());
            headMap.put("conversionRate", String.valueOf(orderChangeHistory.getConversionRate()));
            String conversionDate = DateUtils.format(orderChangeHistory.getConversionDate(), DateUtils.FORMAT_SHORT);
            headMap.put("conversionDate", conversionDate);
            headMap.put("taxRate", orderChangeHistory.getTaxRate());
            headMap.put("paymentMethodName", orderChangeHistory.getPaymentMethodName());
            headMap.put("paymentWay", orderChangeHistory.getPaymentWay());
            headMap.put("deliveryType", orderChangeHistory.getDeliveryType());
            headMap.put("deliveryClause", orderChangeHistory.getDeliveryClause());
            headMap.put("contractTermsDetail", "详见附件合同条款");
            headMap.put("orderDetail", "详见附件订单明细");
            headMap.put("operatingUnitName", orderChangeHistory.getOrgName());
            headMap.put("remark", orderChangeHistory.getRemark());
            responseMap.setHeadMap(headMap);

            // 预算信息
            List<Map<String, String>> list1 = new ArrayList<>();
            List<PurchaseOrderDetailDto> orderDetailChangeHistoryList = orderReceiptsDto.getPurchaseOrderDetailDtoList();
            for (PurchaseOrderDetailDto orderDetailChangeHistory : orderDetailChangeHistoryList) {
                Map<String, String> listMap = Maps.newHashMap();
                listMap.put("requirementCode", orderDetailChangeHistory.getRequirementCode());
                listMap.put("wbsSummaryCode", orderDetailChangeHistory.getWbsSummaryCode());
                BigDecimal discountMoney = orderDetailChangeHistory.getDiscountMoney();
                // 从订单头取汇率
                BigDecimal conversionRate = Optional.ofNullable(orderChangeHistory.getConversionRate()).orElse(new BigDecimal(1));
                discountMoney = discountMoney.multiply(conversionRate);
                listMap.put("discountMoney", String.valueOf(discountMoney));
                list1.add(listMap);
            }
            responseMap.setList1(list1);
        }

        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(CtcAttachmentModule.PURCHASE_ORDER_CHANGE.code());
        attachmentQuery.setModuleId(recordId);
        // 查询附件(包含移动端与pc端)
        List<CtcAttachmentDto> attachmentList = ctcAttachmentService.selectList(attachmentQuery);
        if (CollectionUtil.isNotEmpty(attachmentList)) {
            List<AduitAtta> fileList = new ArrayList<>();
            attachmentList.forEach(attachment -> {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(String.valueOf(attachment.getAttachId()));
                String fileName = attachment.getFileName() == null ? attachment.getAttachName() : attachment.getFileName();
                aduitAtta.setFileName(fileName);
                aduitAtta.setFileSize(String.valueOf(attachment.getFileSize()));
                fileList.add(aduitAtta);
            });
            responseMap.setFileList(fileList);
        }
        responseMap.setStatus("success");
        return responseMap;
    }

    @Override
    public List<PurchaseOrderChangeHistoryDto> getOrderChangeByRecordId(Long recordId) {
        PurchaseOrderChangeHistoryExample example = new PurchaseOrderChangeHistoryExample();
        example.createCriteria()
                .andRecordIdEqualTo(recordId)
                .andDeletedFlagEqualTo(false)
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());

        List<PurchaseOrderChangeHistory> purchaseOrderChangeList = purchaseOrderChangeHistoryMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(purchaseOrderChangeList)) {
            return Lists.newArrayList();
        }
        List<PurchaseOrderChangeHistoryDto> historyDtos = BeanConverter.copy(purchaseOrderChangeList, PurchaseOrderChangeHistoryDto.class);
        Map<Long, String> erpBuyerNames = new HashMap<>();
        historyDtos.forEach(e ->
                e.setErpBuyerName(erpBuyerNames.computeIfAbsent(e.getErpBuyerId(), key -> purchaseOrderService.getErpBuyerName(key)))
        );
        return historyDtos;
    }

    @Override
    public List<PurchaseOrderDetailChangeHistoryDto> getOrderDetailChangeHistoryList(Long orderId, Long recordId) {
        PurchaseOrderDetailChangeHistoryExample purchaseOrderDetailChangeHistoryExample = new PurchaseOrderDetailChangeHistoryExample();
        purchaseOrderDetailChangeHistoryExample.createCriteria()
                .andPurchaseOrderIdEqualTo(orderId)
                .andRecordIdEqualTo(recordId);

        List<PurchaseOrderDetailChangeHistory> orderDetailChangeHistoryList =
                purchaseOrderDetailChangeHistoryMapper.selectByExample(purchaseOrderDetailChangeHistoryExample);
        if (CollectionUtil.isEmpty(orderDetailChangeHistoryList)) {
            return Lists.newArrayList();
        }
        return BeanConverter.copy(orderDetailChangeHistoryList, PurchaseOrderDetailChangeHistoryDto.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateOrder(Long formInstanceId, Integer PurchaseOrderStatus, Integer purchaseOrderSyncStatus, Integer detailStatus) {
        PurchaseOrderChangeHistoryExample purchaseOrderChangeHistoryExample = new PurchaseOrderChangeHistoryExample();
        purchaseOrderChangeHistoryExample.createCriteria().andRecordIdEqualTo(formInstanceId).andHistoryTypeEqualTo(HistoryType.HISTORY.getCode());
        List<PurchaseOrderChangeHistory> purchaseOrderChangeHistoryList =
                purchaseOrderChangeHistoryMapper.selectByExample(purchaseOrderChangeHistoryExample);
        List<PurchaseOrder> purchaseOrderList = new ArrayList<>();
        for (PurchaseOrderChangeHistory purchaseOrderChangeHistory : purchaseOrderChangeHistoryList) {
            PurchaseOrder purchaseOrder = new PurchaseOrder();
            purchaseOrder.setId(purchaseOrderChangeHistory.getOriginId());
            purchaseOrder.setOrderStatus(PurchaseOrderStatus);
            purchaseOrder.setSyncStatus(purchaseOrderSyncStatus);
            purchaseOrderList.add(purchaseOrder);
        }
        if (CollectionUtils.isNotEmpty(purchaseOrderList)) {
            purchaseOrderExtMapper.batchUpdate(purchaseOrderList);
        }

        // 修改变更明细表状态
        PurchaseOrderDetailChangeHistoryExample orderDetailChangeHistoryExample = new PurchaseOrderDetailChangeHistoryExample();
        orderDetailChangeHistoryExample.createCriteria()
                .andRecordIdEqualTo(formInstanceId)
                .andOriginIdIsNull()
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        List<PurchaseOrderDetailChangeHistory> orderDetailChangeHistoryList =
                purchaseOrderDetailChangeHistoryMapper.selectByExample(orderDetailChangeHistoryExample);
        List<PurchaseOrderDetailChangeHistory> updateChangeHistoryList = new ArrayList<>();
        for (PurchaseOrderDetailChangeHistory orderDetailChangeHistory : orderDetailChangeHistoryList) {
            PurchaseOrderDetailChangeHistory pd = new PurchaseOrderDetailChangeHistory();
            pd.setId(orderDetailChangeHistory.getId());
            pd.setStatus(detailStatus);
            updateChangeHistoryList.add(pd);
        }
        if (CollectionUtils.isNotEmpty(updateChangeHistoryList)) {
            purchaseOrderDetailChangeHistoryExtMapper.batchUpdate(updateChangeHistoryList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRecord(Long formInstanceId, Integer recordStatus, Integer purchaseOrderSyncStatus) {
        PurchaseOrderChangeRecord purchaseOrderChangeRecord = new PurchaseOrderChangeRecordDto();
        purchaseOrderChangeRecord.setId(formInstanceId);
        purchaseOrderChangeRecord.setStatus(recordStatus);
        purchaseOrderChangeRecord.setSyncStatus(purchaseOrderSyncStatus);
        purchaseOrderChangeRecordMapper.updateByPrimaryKeySelective(purchaseOrderChangeRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRequirementStatusByRecordId(Long changeRecordId) {
        List<Long> requirementIdList = new ArrayList<>();
        PurchaseOrderDetailChangeHistoryExample example = new PurchaseOrderDetailChangeHistoryExample();
        example.createCriteria().andRecordIdEqualTo(changeRecordId)
                .andHistoryTypeEqualTo(1)
                .andOriginIdIsNull()
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<PurchaseOrderDetailChangeHistory> list = purchaseOrderDetailChangeHistoryMapper.selectByExample(example);
        for (int i = 0; list != null && i < list.size(); i++) {
            PurchaseOrderDetailChangeHistory orderDetailChangeHistory = list.get(i);
            if (Objects.equals(1, orderDetailChangeHistory.getMergeRows())) {
                PurchaseOrderMergeChangeHistoryExample mergeExample = new PurchaseOrderMergeChangeHistoryExample();
                example.createCriteria().andRecordIdEqualTo(changeRecordId)
                        .andHistoryTypeEqualTo(1)
                        .andOriginIdIsNull()
                        .andPurchaseOrderIdEqualTo(orderDetailChangeHistory.getId())
                        .andDeletedFlagEqualTo(Boolean.FALSE);
                List<PurchaseOrderMergeChangeHistory> mergeList = purchaseOrderMergeChangeHistoryMapper.selectByExample(mergeExample);
                for (PurchaseOrderMergeChangeHistory mergeChangeHistory : mergeList) {
                    requirementIdList.add(mergeChangeHistory.getMaterialPurchaseRequirementId());
                }
            } else {
                requirementIdList.add(orderDetailChangeHistory.getMaterialPurchaseRequirementId());
            }
        }
        purchaseMaterialRequirementService.batchUpdateStatus(requirementIdList);
    }

    @Override
    public SavePurchaseOrderChangeRecordDto changeDetails(Long recordId, Integer editFlag) {
        SavePurchaseOrderChangeRecordDto savePurchaseOrderChangeRecordDto_1 = new SavePurchaseOrderChangeRecordDto();

        SaveBatchPurchaseOrderRecordDto saveBatchPurchaseOrderRecordDto_2 = new SaveBatchPurchaseOrderRecordDto();

        List<PurchaseOrderReceiptsDto> purchaseOrderReceiptsDtoList_3 = new ArrayList<>();
        List<PurchaseOrderDetailDto> purchaseOrderDetailAllList = new ArrayList<>();

        //查询记录和头表
        PurchaseOrderChangeRecord purchaseOrderChangeRecord = purchaseOrderChangeRecordMapper.selectByPrimaryKey(recordId);

        PurchaseOrderChangeHistoryExample purchaseOrderChangeHistoryExample = new PurchaseOrderChangeHistoryExample();
        purchaseOrderChangeHistoryExample.createCriteria()
                .andRecordIdEqualTo(recordId)
                .andDeletedFlagEqualTo(false)
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        List<PurchaseOrderChangeHistory> purchaseOrderChangeHistoryList =
                purchaseOrderChangeHistoryMapper.selectByExampleWithBLOBs(purchaseOrderChangeHistoryExample);
        if (ListUtils.isEmpty(purchaseOrderChangeHistoryList)) {
            return savePurchaseOrderChangeRecordDto_1;
        }
        Map<Long, BigDecimal> conversionRateMap = purchaseOrderChangeHistoryList.stream()
                .collect(Collectors.toMap(PurchaseOrderChangeHistory::getId, PurchaseOrderChangeHistory::getConversionRate));
        //查询行表
        PurchaseOrderDetailChangeHistoryExample purchaseOrderDetailChangeHistoryExample = new PurchaseOrderDetailChangeHistoryExample();
        purchaseOrderDetailChangeHistoryExample.createCriteria()
                .andRecordIdEqualTo(recordId);
        List<PurchaseOrderDetailChangeHistory> orderDetailChangeHistoryList =
                purchaseOrderDetailChangeHistoryMapper.selectByExample(purchaseOrderDetailChangeHistoryExample);
        if (ListUtils.isEmpty(orderDetailChangeHistoryList)) {
            return savePurchaseOrderChangeRecordDto_1;
        }

        Map<Long, List<PurchaseOrderDetailChangeHistory>> orderDetailChangeHistoryMap = new HashMap<>();
        List<Long> projectWbsReceiptsIdList = new ArrayList<>();
        for (PurchaseOrderDetailChangeHistory purchaseOrderDetailChangeHistory : orderDetailChangeHistoryList) {
            Long projectWbsReceiptsId = purchaseOrderDetailChangeHistory.getProjectWbsReceiptsId();
            projectWbsReceiptsIdList.add(projectWbsReceiptsId);

            Long purchaseOrderId = purchaseOrderDetailChangeHistory.getPurchaseOrderId();
            if (!orderDetailChangeHistoryMap.containsKey(purchaseOrderId)) {
                orderDetailChangeHistoryMap.put(purchaseOrderId, Lists.newArrayList(purchaseOrderDetailChangeHistory));
            } else {
                orderDetailChangeHistoryMap.get(purchaseOrderId).add(purchaseOrderDetailChangeHistory);
            }
        }

        //查询合并
        PurchaseOrderMergeChangeHistoryExample purchaseOrderMergeChangeHistoryExample = new PurchaseOrderMergeChangeHistoryExample();
        purchaseOrderMergeChangeHistoryExample.createCriteria()
                .andRecordIdEqualTo(recordId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<PurchaseOrderMergeChangeHistory> orderMergeChangeHistoryList =
                purchaseOrderMergeChangeHistoryMapper.selectByExample(purchaseOrderMergeChangeHistoryExample);
        Map<Long, List<PurchaseOrderMergeChangeHistory>> orderMergeChangeHistoryMap = new HashMap<>();
        if (ListUtils.isNotEmpty(orderMergeChangeHistoryList)) {
            List<Long> purchaseOrderDetailChangeHistoryIdList = new ArrayList<>();
            for (PurchaseOrderMergeChangeHistory purchaseOrderMergeChangeHistory : orderMergeChangeHistoryList) {
                Long purchaseOrderDetailChangeHistoryId = purchaseOrderMergeChangeHistory.getPurchaseOrderId();
                purchaseOrderDetailChangeHistoryIdList.add(purchaseOrderDetailChangeHistoryId);

                if (!orderMergeChangeHistoryMap.containsKey(purchaseOrderDetailChangeHistoryId)) {
                    orderMergeChangeHistoryMap.put(purchaseOrderDetailChangeHistoryId, Lists.newArrayList(purchaseOrderMergeChangeHistory));
                } else {
                    orderMergeChangeHistoryMap.get(purchaseOrderDetailChangeHistoryId).add(purchaseOrderMergeChangeHistory);
                }
            }
            PurchaseOrderDetailChangeHistoryExample changeHistoryExample = new PurchaseOrderDetailChangeHistoryExample();
            changeHistoryExample.createCriteria().andIdIn(purchaseOrderDetailChangeHistoryIdList).andDeletedFlagEqualTo(Boolean.FALSE);
            List<PurchaseOrderDetailChangeHistory> changeHistoryList = purchaseOrderDetailChangeHistoryMapper.selectByExample(changeHistoryExample);
            if (ListUtils.isNotEmpty(changeHistoryList)) {
                projectWbsReceiptsIdList.addAll(changeHistoryList.stream()
                        .map(PurchaseOrderDetailChangeHistory::getProjectWbsReceiptsId).distinct().collect(Collectors.toList()));
            }
        }
        // 根据需求发布单据+wbscode查询已占用预算(折后不含税-取消数量*单价)
        List<PurchaseOrderDetailDto> discountMoneyList =
                purchaseOrderExtMapper.getDiscountMoneyByProjectWbsReceiptsIdList(projectWbsReceiptsIdList, null, null);
        Map<String, List<PurchaseOrderDetailDto>> progressNumMap = new HashMap<>();
        if (ListUtils.isNotEmpty(discountMoneyList)) {
            progressNumMap = discountMoneyList.stream().collect
                    (Collectors.groupingBy(orderDetail -> orderDetail.getWbsSummaryCode() + ":" + orderDetail.getProjectWbsReceiptsId()));
        }
        Map<Long, String> erpBuyerNames = new HashMap<>();
        // 变更头明细
        for (PurchaseOrderChangeHistory history : purchaseOrderChangeHistoryList) {
            PurchaseOrderReceiptsDto purchaseOrderReceiptsDto_4 = new PurchaseOrderReceiptsDto();
            PurchaseOrderTitleDto purchaseOrderTitleDto = new PurchaseOrderTitleDto();
            String erpBuyerName = erpBuyerNames.computeIfAbsent(history.getErpBuyerId(), key -> purchaseOrderService.getErpBuyerName(key));
            BeanUtils.copyProperties(history, purchaseOrderTitleDto);
            purchaseOrderTitleDto.setId(history.getOriginId());
            purchaseOrderTitleDto.setErpBuyerName(erpBuyerName);
            PurchaseOrder purchaseOrder = purchaseOrderMapper.selectByPrimaryKey(history.getOriginId());
            purchaseOrderTitleDto.setContractTermsFlg(purchaseOrder.getContractTermsFlg());
            List<StandardTermsDto> standardTermsDtoList = new ArrayList<>();
            PurchaseOrderStandardTermsExample purchaseOrderStandardTermsExample = new PurchaseOrderStandardTermsExample();
            PurchaseOrderStandardTermsExample.Criteria criteria = purchaseOrderStandardTermsExample.createCriteria();
            criteria.andPurchaseOrderIdEqualTo(history.getOriginId()).andDeletedFlagEqualTo(false);
            List<PurchaseOrderStandardTerms> purchaseOrderStandardTerms = purchaseOrderStandardTermsMapper.selectByExampleWithBLOBs(purchaseOrderStandardTermsExample);
            if (CollectionUtils.isNotEmpty(purchaseOrderStandardTerms)){
                for (PurchaseOrderStandardTerms purchaseOrderStandardTerm : purchaseOrderStandardTerms) {
                    StandardTermsDto standardTermsDto = new StandardTermsDto();
                    standardTermsDto.setId(purchaseOrderStandardTerm.getId());
                    standardTermsDto.setPurchaseOrderId(purchaseOrderStandardTerm.getPurchaseOrderId());
                    standardTermsDto.setAssociationTermsId(purchaseOrderStandardTerm.getAssociationTermsId());
                    standardTermsDto.setTermsCode(purchaseOrderStandardTerm.getTermsCode());
                    standardTermsDto.setTermsName(purchaseOrderStandardTerm.getTermsName());
                    standardTermsDto.setRemark(purchaseOrderStandardTerm.getRemark());
                    standardTermsDto.setTermsDisplayContent(purchaseOrderStandardTerm.getTermsDisplayContent());
                    StandardTermsDeviationExample standardTermsDeviationExample = new StandardTermsDeviationExample();
                    StandardTermsDeviationExample.Criteria deviationExampleCriteria = standardTermsDeviationExample.createCriteria();
                    deviationExampleCriteria.andDeletedFlagEqualTo(false).andAssociationPurchaseTermsIdEqualTo(purchaseOrderStandardTerm.getId());
                    List<StandardTermsDeviation> standardTermsDeviations = standardTermsDeviationMapper.selectByExample(standardTermsDeviationExample);
                    if (CollectionUtils.isNotEmpty(standardTermsDeviations)) {
                        standardTermsDto.setStandardTermsDeviationList(standardTermsDeviations);
                    }
                    standardTermsDtoList.add(standardTermsDto);
                }
            }
            purchaseOrderTitleDto.setStandardTermsDtoList(standardTermsDtoList);
            purchaseOrderReceiptsDto_4.setPurchaseOrderTitle(purchaseOrderTitleDto);
            PurchaseOrderDto purchaseOrderDto = new PurchaseOrderDto();
            BeanUtils.copyProperties(history, purchaseOrderDto);
            purchaseOrderDto.setId(history.getOriginId());
            purchaseOrderDto.setErpBuyerName(erpBuyerName);
            purchaseOrderReceiptsDto_4.setPurchaseOrderDto(purchaseOrderDto);

            //查询行表
            List<PurchaseOrderDetailDto> purchaseOrderDetailDtoList = new ArrayList<>();
            List<PurchaseOrderDetailChangeHistory> purchaseOrderDetailChangeHistoryList = orderDetailChangeHistoryMap.get(history.getId());
            if (ListUtils.isEmpty(purchaseOrderDetailChangeHistoryList)) {
                purchaseOrderReceiptsDtoList_3.add(purchaseOrderReceiptsDto_4);
                continue;
            }
            for (PurchaseOrderDetailChangeHistory purchaseOrderDetailChangeHistory : purchaseOrderDetailChangeHistoryList) {
                PurchaseOrderDetailDto purchaseOrderDetailDto = new PurchaseOrderDetailDto();
                Integer orderStatus = history.getOrderStatus();
                Integer pricingType = history.getPricingType();
                String currency = history.getCurrency();
                Long orgId = history.getOrgId();
                Long originId = purchaseOrderDetailChangeHistory.getOriginId();
                // 订单为草稿、撤回、驳回状态,且定价类型是一揽子价格,需要查询当前最新单价
                boolean meetCondition = (Objects.equals(orderStatus, OrderStatusEnum.DRAFT.getCode())
                        || Objects.equals(orderStatus, OrderStatusEnum.REJECT.getCode())
                        || Objects.equals(orderStatus, OrderStatusEnum.CANCEL.getCode()));
                if (meetCondition && PricingTypeEnum.PACKAGE_AGREEMENT.code().equals(pricingType) && Objects.isNull(originId)) {
                    PurchaseBpaPriceQuery priceQuery = new PurchaseBpaPriceQuery();
                    priceQuery.setCurrency(currency);
                    priceQuery.setUnit(purchaseOrderDetailChangeHistory.getUnit());
                    OrganizationRelDto organization = CacheDataUtils.findOrganizationById(orgId);
                    Long organizationId = Objects.nonNull(organization) ? organization.getOperatingUnitId() : new Long(-1);
                    priceQuery.setOuId(organizationId);
                    priceQuery.setVendorSiteCode(purchaseOrderDetailChangeHistory.getVendorSiteCode());
                    priceQuery.setMaterialCode(purchaseOrderDetailChangeHistory.getErpCode());
                    // 查询一揽子协议价格
                    PurchaseBpaPrice purchaseBpaPrice = purchaseBpaPriceService.getPurchaseBpaPrice(priceQuery);
                    // 如果一揽子协议为空,单价设为0
                    BigDecimal priceOverride = Objects.isNull(purchaseBpaPrice) ? BigDecimal.ZERO : purchaseBpaPrice.getPriceOverride();
                    // 折后价: 单价*(1-折扣%)
                    BigDecimal newDiscountPrice = calculateDiscountedPrice(priceOverride, purchaseOrderDetailChangeHistory.getDiscount());
                    BigDecimal newDiscountMoney = newDiscountPrice.multiply(purchaseOrderDetailChangeHistory.getOrderNum());
                    // 计算完后更新值
                    purchaseOrderDetailChangeHistory.setUnitPrice(priceOverride);
                    purchaseOrderDetailChangeHistory.setDiscountPrice(newDiscountPrice);
                    purchaseOrderDetailChangeHistory.setDiscountMoney(newDiscountMoney);
                    // 根据id更新数据
                    purchaseOrderDetailChangeHistoryMapper.updateByPrimaryKeySelective(purchaseOrderDetailChangeHistory);
                }
                BeanUtils.copyProperties(purchaseOrderDetailChangeHistory, purchaseOrderDetailDto);
                purchaseOrderDetailDto.setRequirementId(purchaseOrderDetailDto.getMaterialPurchaseRequirementId());
                // 新增的行, 返回变更明细表id,否则前端无法做列表展示,再次提交的时候前端会自动去掉
                if (purchaseOrderDetailChangeHistory.getOriginId() != null) {
                    purchaseOrderDetailDto.setId(purchaseOrderDetailChangeHistory.getOriginId());
                }

                //查询合并
                List<PurchaseOrderDetailDto> purchaseOrderDetailDtoListChild = new ArrayList<>();
                List<PurchaseOrderMergeChangeHistory> purchaseOrderMergeChangeHistoryList =
                        orderMergeChangeHistoryMap.get(purchaseOrderDetailChangeHistory.getId());
                if (ListUtils.isNotEmpty(purchaseOrderMergeChangeHistoryList)) {
                    for (PurchaseOrderMergeChangeHistory purchaseOrderMergeChangeHistory : purchaseOrderMergeChangeHistoryList) {
                        PurchaseOrderDetailDto purchaseOrderDetailDtoChild = new PurchaseOrderDetailDto();
                        // 满足更新一揽子协议条件
                        if (meetCondition && Objects.isNull(purchaseOrderMergeChangeHistory.getOriginId())) {
                            // 折后价: 单价*(1-折扣%)
                            BigDecimal priceOverride = purchaseOrderDetailChangeHistory.getUnitPrice();
                            BigDecimal newDiscountPrice = calculateDiscountedPrice(priceOverride, purchaseOrderMergeChangeHistory.getDiscount());
                            BigDecimal newDiscountMoney = newDiscountPrice.multiply(purchaseOrderMergeChangeHistory.getOrderNum());
                            // 计算完成,重新赋值
                            purchaseOrderMergeChangeHistory.setUnitPrice(priceOverride);
                            purchaseOrderMergeChangeHistory.setDiscountPrice(newDiscountMoney);
                            purchaseOrderMergeChangeHistory.setDiscountMoney(newDiscountMoney);
                            // 根据id,更新合并明细的价格
                            purchaseOrderMergeChangeHistoryMapper.updateByPrimaryKeySelective(purchaseOrderMergeChangeHistory);
                        }
                        BeanUtils.copyProperties(purchaseOrderMergeChangeHistory, purchaseOrderDetailDtoChild);
                        // purchase_order_merge 没有 projectWbsReceiptsId
                        purchaseOrderDetailDtoChild.setProjectWbsReceiptsId(purchaseOrderDetailDto.getProjectWbsReceiptsId());
                        purchaseOrderDetailDtoChild.setRequirementId(purchaseOrderDetailDtoChild.getMaterialPurchaseRequirementId());
                        // 新增的行, 返回变更明细表id,否则前端无法做列表展示,再次提交的时候前端会自动去掉
                        if (purchaseOrderMergeChangeHistory.getOriginId() != null) {
                            purchaseOrderDetailDtoChild.setId(purchaseOrderMergeChangeHistory.getOriginId());
                        }
                        // 查询已占用预算
                        purchaseOrderService.calculateRemainMoney(purchaseOrderDetailDtoChild, progressNumMap);
                        purchaseOrderDetailDtoListChild.add(purchaseOrderDetailDtoChild);
                    }
                }
                // 查询已占用预算
                purchaseOrderService.calculateRemainMoney(purchaseOrderDetailDto, progressNumMap);
                purchaseOrderDetailDto.setChildren(purchaseOrderDetailDtoListChild);
                purchaseOrderDetailDtoList.add(purchaseOrderDetailDto);

                // get文件数据  start
                CtcAttachmentExample ctcAttachmentExampleOrder = new CtcAttachmentExample();
                CtcAttachmentExample.Criteria criteriaOrder = ctcAttachmentExampleOrder.createCriteria();
                criteriaOrder.andModuleEqualTo(CtcAttachmentModule.PURCHASE_ORDER.code());
                criteriaOrder.andModuleIdEqualTo(history.getOriginId());
                List<CtcAttachment> ctcAttachmentList = ctcAttachmentMapper.selectByExample(ctcAttachmentExampleOrder);
                List<CtcAttachmentDto> ctcAttachmentDtoList = new ArrayList<>();
                for (int j = 0; j < ctcAttachmentList.size(); j++) {
                    CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
                    BeanUtils.copyProperties(ctcAttachmentList.get(j), ctcAttachmentDto);
                    UserInfo userInfo = CacheDataUtils.findUserById(ctcAttachmentList.get(j).getCreateBy());
                    if (userInfo != null) {
                        ctcAttachmentDto.setCreateUserName(userInfo.getName());
                    }
                    ctcAttachmentDtoList.add(ctcAttachmentDto);
                }
                purchaseOrderReceiptsDto_4.setPurchaseOrderDetailDtoList(purchaseOrderDetailDtoList);
                purchaseOrderDetailAllList.addAll(purchaseOrderDetailDtoList);
                purchaseOrderReceiptsDto_4.setCtcAttachmentList(ctcAttachmentDtoList);
                // get文件数据  end
            }
            purchaseOrderReceiptsDtoList_3.add(purchaseOrderReceiptsDto_4);
        }

        //查询上传文件变更的
        CtcAttachmentExample ctcAttachmentExample = new CtcAttachmentExample();
        ctcAttachmentExample.createCriteria()
                .andModuleIdEqualTo(recordId)
                .andModuleEqualTo(CtcAttachmentModule.PURCHASE_ORDER_CHANGE.code());
        List<CtcAttachment> ctcAttachmentList = ctcAttachmentMapper.selectByExample(ctcAttachmentExample);
        List<CtcAttachmentDto> ctcAttachmentDtoList = new ArrayList<>();
        for (CtcAttachment ctcAttachment : ctcAttachmentList) {
            CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
            BeanUtils.copyProperties(ctcAttachment, ctcAttachmentDto);
            UserInfo user = CacheDataUtils.findUserById(ctcAttachment.getCreateBy());
            if (user != null) {
                ctcAttachmentDto.setCreateUserName(user.getName());
            }
            ctcAttachmentDtoList.add(ctcAttachmentDto);
        }
        savePurchaseOrderChangeRecordDto_1.setCtcAttachmentList(ctcAttachmentDtoList);

        //如果是重新编辑，实时查历史价
        if (Objects.equals(editFlag, 1)) {
            //批量查询订单行库存组织ID
            List<Long> materielIdList = purchaseOrderDetailAllList.stream().map(s -> s.getMaterielId()).collect(Collectors.toList());
            Map<Long, Long> organizationMap = basedataExtService.getMaterialByIds(materielIdList);
            purchaseOrderDetailAllList.forEach(s -> s.setOrgId(organizationMap.get(s.getMaterielId())));

            //批量查询物料历史价格
            List<Long> orgErrorList = new ArrayList<>();
            Map<String, BigDecimal> materialPriceMap = purchaseOrderService.getMaterialPriceMap(purchaseOrderDetailAllList, orgErrorList);
            for (PurchaseOrderDetailDto detailDto : purchaseOrderDetailAllList) {
                //新增的订单行才需要更新历史价
                if (detailDto.getOriginId() != null) {
                    continue;
                }
                if (!orgErrorList.contains(detailDto.getOrgId())) {
                    //用物料价格表取到的“最新价格”/订单上的汇率，作为该物料的历史价
                    BigDecimal conversionRate = conversionRateMap.getOrDefault(detailDto.getPurchaseOrderId(), BigDecimal.ONE);
                    //柔性采购订单变更明细表purchase_order_id存的其实是变更头表的主键id..
                    BigDecimal amount = materialPriceMap.get(buildMaterialPriceGroupKey(detailDto.getErpCode(), detailDto.getOrgId()));
                    detailDto.setStandardHistoryPrice(amount);
                    detailDto.setHistoryPrice(null);
                    if (amount != null) {
                        detailDto.setHistoryPrice(BigDecimalUtils.divide(amount, conversionRate));
                    } else {
                        detailDto.setErrorMsg("历史价不存在");
                    }
                } else {
                    detailDto.setErrorMsg("组织参数：WBS采购订单历史价价格类型，配置有误，请联系IT处理");
                }
            }
        }

        saveBatchPurchaseOrderRecordDto_2.setPurchaseOrderReceiptsDtoList(purchaseOrderReceiptsDtoList_3);
        BeanUtils.copyProperties(purchaseOrderChangeRecord, savePurchaseOrderChangeRecordDto_1);
        savePurchaseOrderChangeRecordDto_1.setPurchaseOrderReceiptsDtoListAll(saveBatchPurchaseOrderRecordDto_2);
        return savePurchaseOrderChangeRecordDto_1;
    }

    private String buildMaterialPriceGroupKey(String erpCode, Long organizationId) {
        return String.format("buildMaterialPriceGroupKey_%s_%s", erpCode, organizationId);
    }

    private BigDecimal calculateDiscountedPrice(BigDecimal price, BigDecimal discount) {
        if (Objects.isNull(price)) {
            price = BigDecimal.ZERO;
        }
        if (Objects.isNull(discount)) {
            discount = BigDecimal.ZERO;
        }
        discount = new BigDecimal("1").subtract(discount.divide(new BigDecimal(100)));
        return price.multiply(discount);
    }
}
