package com.midea.pam.ctc.wbs.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.dto.WbsConstraintDto;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.ProjectActivityCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleDetailCache;
import com.midea.pam.common.ctc.dto.FeeItemExpenseTypeDto;
import com.midea.pam.common.ctc.dto.ProjectBaselineBatchDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetObjectDto;
import com.midea.pam.common.ctc.dto.WbsDynamicFieldsDto;
import com.midea.pam.common.ctc.dto.WbsTemplateRuleDto;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectActivity;
import com.midea.pam.common.ctc.entity.ProjectActivityExample;
import com.midea.pam.common.ctc.entity.ProjectBaselineBatch;
import com.midea.pam.common.ctc.entity.ProjectBaselineBatchExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetBaseline;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetDynamic;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetDynamicExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetExample;
import com.midea.pam.common.ctc.entity.WbsCustomizeRule;
import com.midea.pam.common.ctc.entity.WbsCustomizeRuleExample;
import com.midea.pam.common.ctc.entity.WbsTemplateInfo;
import com.midea.pam.common.ctc.entity.WbsTemplateRuleDetail;
import com.midea.pam.common.ctc.entity.WbsTemplateRuleDetailExample;
import com.midea.pam.common.ctc.excelVo.ProjectWbsBudgetImportResponseExcelVO;
import com.midea.pam.common.enums.DescribeDisplayEnum;
import com.midea.pam.common.enums.FeeSettingModeEnum;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.common.util.cache.ProjectActivityCacheUtils;
import com.midea.pam.common.util.cache.WbsTemplateRuleDetailCacheUtils;
import com.midea.pam.ctc.feign.basedata.feign.WbsConstraintFeignClient;
import com.midea.pam.ctc.mapper.FeeItemExtMapper;
import com.midea.pam.ctc.mapper.ProjectActivityMapper;
import com.midea.pam.ctc.mapper.ProjectBaselineBatchMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetBaselineExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetBaselineMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetDynamicExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetDynamicMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryExtMapper;
import com.midea.pam.ctc.mapper.WbsCustomizeRuleMapper;
import com.midea.pam.ctc.mapper.WbsTemplateInfoMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleDetailMapper;
import com.midea.pam.ctc.mapper.WbsTemplateRuleExtMapper;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.wbs.service.ProjectActivityService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetBaselineService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetCheckHelpper;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetSummaryService;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleDetailService;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.midea.pam.common.constants.ProjectWbsCostConstant.SYSTEM_ACTIVITY_CODE;
import static com.midea.pam.common.constants.ProjectWbsCostConstant.SYSTEM_ACTIVITY_ORDER_NO;


public class ProjectWbsBudgetServiceImpl implements ProjectWbsBudgetService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private WbsTemplateRuleService wbsTemplateRuleService;
    @Resource
    private ProjectActivityService projectActivityService;
    @Resource
    private ProjectActivityMapper projectActivityMapper;
    @Resource
    private WbsTemplateRuleExtMapper wbsTemplateRuleExtMapper;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectWbsBudgetDynamicMapper projectWbsBudgetDynamicMapper;
    @Resource
    private ProjectWbsBudgetSummaryService projectWbsBudgetSummaryService;
    @Resource
    private ProjectBaselineBatchMapper projectBaselineBatchMapper;
    @Resource
    private ProjectWbsBudgetMapper projectWbsBudgetMapper;
    @Resource
    private ProjectWbsBudgetBaselineMapper projectWbsBudgetBaselineMapper;
    @Resource
    private ProjectWbsBudgetBaselineService projectWbsBudgetBaselineService;
    @Resource
    private ProjectWbsBudgetExtMapper projectWbsBudgetExtMapper;
    @Resource
    private ProjectWbsBudgetBaselineExtMapper projectWbsBudgetBaselineExtMapper;
    @Resource
    private ProjectWbsBudgetDynamicExtMapper projectWbsBudgetDynamicExtMapper;
    @Resource
    private ProjectWbsBudgetSummaryExtMapper projectWbsBudgetSummaryExtMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private WbsConstraintFeignClient wbsConstraintFeignClient;
    @Resource
    private FeeItemExtMapper feeItemExtMapper;
    @Resource
    private ProjectWbsBudgetCheckHelpper projectWbsBudgetCheckHelpper;
    @Resource
    private ProjectTypeService projectTypeService;
    @Resource
    private WbsTemplateInfoMapper wbsTemplateInfoMapper;
    @Resource
    private WbsCustomizeRuleMapper wbsCustomizeRuleMapper;
    @Resource
    private WbsTemplateRuleDetailMapper wbsTemplateRuleDetailMapper;
    @Resource
    private WbsTemplateRuleDetailService wbsTemplateRuleDetailService;


    /**
     * 批量导入
     *
     * @param dataList
     * @param batchSize
     * @return
     */
    @Override
    public int batchInsert(List<ProjectWbsBudget> dataList, int batchSize) {
        if (CollectionUtils.isEmpty(dataList)) {
            return 0;
        }
        if (dataList.size() <= batchSize) {
            return projectWbsBudgetExtMapper.batchInsert(dataList);
        }
        int batchNum = dataList.size() / batchSize;
        int n = dataList.size() % batchSize;
        if (n > 0) {
            batchNum = batchNum + 1;
        }
        int resultNum = 0;
        for (int i = 0; i < batchNum; i++) {
            List<ProjectWbsBudget> subList = subList(dataList, (i + 1), batchSize);
            resultNum = resultNum + projectWbsBudgetExtMapper.batchInsert(subList);
        }
        return resultNum;
    }

    private List<ProjectWbsBudget> subList(List<ProjectWbsBudget> dataList, int batchNum, int batchSize) {
        int size = dataList.size();
        int startIndex = (batchNum - 1) * batchSize;
        int endIndex = batchNum * batchSize;
        if (endIndex > size) {
            endIndex = size;
        }
        return dataList.subList(startIndex, endIndex);
    }

    /**
     * 新建项目-wbs预算汇总
     *
     * @param paramMap
     * @return
     */
    @Override
    public List<Map> summaryWbsBudget(Map paramMap) {
        // 预立项转正场景
        boolean isPreview = Boolean.TRUE.equals(MapUtils.getBoolean(paramMap, "isPreview"));

        Long wbsTemplateInfoId = MapUtils.getLong(paramMap, WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID);
        if (Objects.isNull(wbsTemplateInfoId)) {
            throw new ApplicationBizException("参数wbsTemplateInfoId不能为空");
        }
        String projectCode = MapUtils.getString(paramMap, WbsBudgetFieldConstant.PROJECT_CODE);
        if (StringUtils.isBlank(projectCode)) {
            throw new ApplicationBizException("参数projectCode能为空");
        }
        // 动态列
        List<WbsDynamicFieldsDto> dynamicFields = wbsTemplateRuleService.getWbsDynamicFields(wbsTemplateInfoId);
        if (CollectionUtils.isEmpty(dynamicFields)) {
            throw new ApplicationBizException("项目wbs模板规则为空，请维护项目wbs模板");
        }
        List<Map> dataList = (List<Map>) MapUtils.getObject(paramMap, "data");
        if (CollectionUtils.isEmpty(dataList)) {
            throw new ApplicationBizException("参数data不能为空：" + JSON.toJSONString(paramMap));
        }

        // 前端标识删除的不展示
        Iterator<Map> iterator = dataList.iterator();
        while (iterator.hasNext()) {
            Map map = iterator.next();
            if (Boolean.TRUE.equals(MapUtils.getBoolean(map, WbsBudgetFieldConstant.DELETED_FLAG))) {
                iterator.remove();
            }
        }

        // 预立项转正场景，汇总的时候取变更后金额，统计到price上面
        if (isPreview) {
            for (Map data : dataList) {
                data.put(WbsBudgetFieldConstant.PRICE, data.get(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE));
            }
        }
        String firseProjectCode = "";
        Object projectCode1 = paramMap.get(WbsBudgetFieldConstant.PROJECT_CODE);
        if (projectCode1 != null) {
            firseProjectCode = projectCode1.toString();
        }
        List<Map> tree = buildWbsTree(projectCode, 0, dynamicFields, dataList, wbsTemplateInfoId, firseProjectCode);
        Map node = new HashMap();
        node.put(dynamicFields.get(0).getKey(), projectCode);
        // 汇总金额
        BigDecimal sumPrice = dataList.stream()
                .map(map -> new BigDecimal(map.get(WbsBudgetFieldConstant.PRICE).toString()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 四舍五入金额
        ProjectWbsBudgetUtils.roundingHalfUp(node, WbsBudgetFieldConstant.PRICE, sumPrice);
        node.put(WbsBudgetFieldConstant.CHILDS, tree);
        node.put(WbsBudgetFieldConstant.SUMMARY_TYPE, WbsBudgetFieldConstant.WBS);
        node.put(WbsBudgetFieldConstant.SUMMARY_CODE, projectCode);
        List<Map> result = new ArrayList<>();
        result.add(node);
        return result;
    }

    /**
     * @param projectCode   项目编码
     * @param index         动态列序号
     * @param dynamicFields 动态列
     * @param dataList
     * @return
     */
    private List<Map> buildWbsTree(String projectCode,
                                   int index,
                                   List<WbsDynamicFieldsDto> dynamicFields,
                                   List<Map> dataList,
                                   Long wbsTemplateInfoId, String firstProjectCode) {
        List<Map> result = new ArrayList<>();
        // 动态列
        WbsDynamicFieldsDto dynamicField = dynamicFields.get(index);
        // Map<动态列field , List<Data>
        Map<String, List<Map>> groupMap = dataList.stream().collect(Collectors.groupingBy(a -> a.get(dynamicField.getKey()) + ""));

        groupMap.forEach((dynamicFieldKey, details) -> {
            Map<String, Object> node = new HashMap();
            node.put(WbsBudgetFieldConstant.SUMMARY_TYPE, WbsBudgetFieldConstant.WBS);
            node.put(WbsBudgetFieldConstant.SUMMARY_CODE, projectCode + "-" + dynamicFieldKey);
            // 最后一层动态列
            if (index == dynamicFields.size() - 1) {
                node.put(WbsBudgetFieldConstant.PROJECT_DETAIL_SELECT_FLAG, true);
                node.put(WbsBudgetFieldConstant.PROJECT_CODE, MapUtils.getString(details.get(0), WbsBudgetFieldConstant.PROJECT_CODE));
                // 设置描述（wbs描述去重，2000长度拦截）
                setWbsDescription(node, details);
                for (WbsDynamicFieldsDto df : dynamicFields) {
                    node.put(df.getKey(), MapUtils.getString(details.get(0), df.getKey()));
                }
                BigDecimal sumPrice = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(details)) {
                    sumPrice = details.stream().map(map -> {
                        // 明细层
                        map.put(WbsBudgetFieldConstant.SUMMARY_TYPE, WbsBudgetFieldConstant.DETAIL);
                        map.put(WbsBudgetFieldConstant.SUMMARY_CODE, ProjectWbsBudgetUtils.getWbsFullCode(map, dynamicFields));
                        map.put(WbsBudgetFieldConstant.PROJECT_DETAIL_SELECT_FLAG, false);
                        return new BigDecimal(map.get(WbsBudgetFieldConstant.PRICE).toString());
                    }).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 按summaryCode + activityOrderNo排序
                    details.sort(Comparator.comparing(a -> MapUtils.getString(a, WbsBudgetFieldConstant.SUMMARY_CODE) + MapUtils.getString(a, WbsBudgetFieldConstant.ACTIVITY_ORDER_NO), Comparator.nullsFirst(Comparator.naturalOrder())));
                }
                node.put(WbsBudgetFieldConstant.PRICE, sumPrice);
                // 四舍五入金额
                ProjectWbsBudgetUtils.roundingHalfUp(details, WbsBudgetFieldConstant.PRICE);
                node.put(WbsBudgetFieldConstant.DETAILS, details);
            }
            // 其他动态列
            else {
                node.put(WbsBudgetFieldConstant.PROJECT_DETAIL_SELECT_FLAG, false);
                List<Map> childs = buildWbsTree(projectCode + "-" + dynamicFieldKey, index + 1, dynamicFields, details, wbsTemplateInfoId, firstProjectCode);
                BigDecimal sumPrice = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(childs)) {
                    // 汇总金额
                    sumPrice = childs.stream().map(map -> new BigDecimal(map.get(WbsBudgetFieldConstant.PRICE).toString())).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                // 按summaryCode排序
                childs.sort(Comparator.comparing(a -> MapUtils.getString(a, WbsBudgetFieldConstant.SUMMARY_CODE), Comparator.nullsFirst(Comparator.naturalOrder())));
                node.put(WbsBudgetFieldConstant.CHILDS, childs);
                // 四舍五入金额
                ProjectWbsBudgetUtils.roundingHalfUp(node, WbsBudgetFieldConstant.PRICE, sumPrice);

                //获取动态列
                WbsTemplateRuleDto param = new WbsTemplateRuleDto();
                param.setDeletedFlag(false);
                param.setWbsTemplateInfoId(wbsTemplateInfoId);
                List<WbsTemplateRuleDto> wbsRuleList = wbsTemplateRuleExtMapper.selectList(param);
                if (CollectionUtils.isNotEmpty(wbsRuleList)) {
                    Collections.sort(wbsRuleList);
                    List<WbsDynamicFieldsDto> resultList = new ArrayList<>();
                    WbsDynamicFieldsDto dynamicFieldDto;
                    for (int i = 0; i < wbsRuleList.size(); i++) {
                        if (wbsRuleList.get(i).getOrderNo().equals("1")
                                && wbsRuleList.get(i).getRuleName().equals("项目")) {
                            continue;
                        }
                        dynamicFieldDto = new WbsDynamicFieldsDto();
                        dynamicFieldDto.setKey("field_" + (resultList.size() + 2));
                        dynamicFieldDto.setWbsTemplateRuleId(wbsRuleList.get(i).getId());
                        dynamicFieldDto.setLable(wbsRuleList.get(i).getRuleName());
                        resultList.add(dynamicFieldDto);
                    }

                    Object summaryCode = node.get(WbsBudgetFieldConstant.SUMMARY_CODE);
                    if (summaryCode != null) {
                        String summaryCodeString = summaryCode.toString();
                        int projectCodeSegments = firstProjectCode.split("-").length;
                        String[] parts = summaryCodeString.split("-");
                        if (parts.length > projectCodeSegments) {
                            int dynamicFieldLevel = parts.length - projectCodeSegments - 1;
                            if (dynamicFieldLevel >= 0 && dynamicFieldLevel < resultList.size()) {
                                String lastPart = parts[parts.length - 1];
                                WbsDynamicFieldsDto wbsDynamicFieldsDto = resultList.get(dynamicFieldLevel);
                                Long wbsTemplateRuleId = wbsDynamicFieldsDto.getWbsTemplateRuleId();
                                WbsTemplateRuleDetailExample detailExample = new WbsTemplateRuleDetailExample();
                                detailExample.createCriteria()
                                        .andWbsTemplateRuleIdEqualTo(wbsTemplateRuleId)
                                        .andCodeEqualTo(lastPart)
                                        .andDeletedFlagEqualTo(false);
                                List<WbsTemplateRuleDetail> detailsList = wbsTemplateRuleDetailMapper.selectByExample(detailExample);
                                if (CollectionUtils.isNotEmpty(detailsList)) {
                                    node.put(WbsBudgetFieldConstant.DESCRIPTION, detailsList.get(0).getDescription());
                                }
                            }
                        }
                    }
                }
            }
            result.add(node);
        });
        // 四舍五入金额
        ProjectWbsBudgetUtils.roundingHalfUp(result, WbsBudgetFieldConstant.PRICE);
        return result;
    }

    /**
     * 设置描述（wbs描述去重，2000长度拦截）
     *
     * @param node      当前节点
     * @param childList 子节点
     */
    private void setWbsDescription(Map<String, Object> node, List<Map> childList) {
        // 设置描述（wbs描述去重，2000长度拦截）
        Set<String> desSet = new HashSet<>();
        StringBuilder desString = new StringBuilder();
        for (Map map : childList) {
            String des = MapUtils.getString(map, WbsBudgetFieldConstant.DESCRIPTION);
            if (StringUtils.isNotBlank(des) && !desSet.contains(des)) {
                desSet.add(des);
                if (desString.length() == 0) {
                    desString.append(des);
                } else {
                    desString.append("," + des);
                }
            }
        }
        node.put(WbsBudgetFieldConstant.DESCRIPTION, desString.length() > 2000 ? desString.substring(0, 1999) : desString.toString());
    }


    /**
     * 新建项目-项目活动预算汇总
     *
     * @param paramMap
     * @return
     */
    @Override
    public List<Map> summaryProjectActivityBudget(Map paramMap) {
        // 预立项转正场景
        boolean isPreview = Boolean.TRUE.equals(MapUtils.getBoolean(paramMap, "isPreview"));

        Long wbsTemplateInfoId = MapUtils.getLong(paramMap, WbsBudgetFieldConstant.WBS_TEMPLATE_INFO_ID);
        if (Objects.isNull(wbsTemplateInfoId)) {
            throw new ApplicationBizException("参数wbsTemplateInfoId不能为空");
        }
        String projectCode = MapUtils.getString(paramMap, WbsBudgetFieldConstant.PROJECT_CODE);
        if (StringUtils.isBlank(projectCode)) {
            throw new ApplicationBizException("参数projectCode不能为空");
        }
        // 动态列
        List<WbsDynamicFieldsDto> dynamicFields = wbsTemplateRuleService.getWbsDynamicFields(wbsTemplateInfoId);
        if (CollectionUtils.isEmpty(dynamicFields)) {
            throw new ApplicationBizException("项目wbs模板规则为空，请维护项目wbs模板");
        }
        List<Map> dataList = (List<Map>) MapUtils.getObject(paramMap, "data");
        if (CollectionUtils.isEmpty(dataList)) {
            throw new ApplicationBizException("参数data不能为空：" + JSON.toJSONString(paramMap));
        }

        // 前端标识删除的不展示
        Iterator<Map> iterator = dataList.iterator();
        while (iterator.hasNext()) {
            Map map = iterator.next();
            if (Boolean.TRUE.equals(MapUtils.getBoolean(map, WbsBudgetFieldConstant.DELETED_FLAG))) {
                iterator.remove();
            }
        }

        // 预立项转正场景，汇总的时候取变更后金额，统计到price上面
        if (isPreview) {
            for (Map data : dataList) {
                data.put(WbsBudgetFieldConstant.PRICE, data.get(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE));
            }
        }

        // 获取data中唯一的orderNo
        List<String> groupOrderNo = dataList.stream()
                .map(a -> a.get(WbsBudgetFieldConstant.ACTIVITY_ORDER_NO) + "")
                .distinct()
                .collect(Collectors.toList());

        // 上级order集合 <orderNo,下级>
        Map<String, HashSet<String>> parentOrderMap = new HashMap<>();

        // 第一层orderNo
        HashSet<String> firstOrderNoSet = new HashSet<>();

        for (String orderNo : groupOrderNo) {
            // 组装父层orderNo
            setParentOrderNo(orderNo, parentOrderMap, firstOrderNoSet);
        }
        // Map orderNo 分组 <orderNo, data>
        Map<String, List<Map>> groupMap = dataList.stream().collect(Collectors.groupingBy(a -> MapUtils.getString(a, WbsBudgetFieldConstant.ACTIVITY_ORDER_NO)));

        // parentOrderMap获取活动事项列表 <orderNo, ProjectActivity>
        Map<String, ProjectActivity> parentActivity = getOrderMapActivity(parentOrderMap);

        List<Map> result = new ArrayList<>();
        for (String firstOrderNo : firstOrderNoSet) {
            Map firstNode = buildActivityTree(firstOrderNo, null, parentActivity, parentOrderMap, groupMap, dynamicFields);
            result.add(firstNode);
        }
        return result;
    }

    /**
     * parentOrderMap获取活动事项列表
     *
     * @param parentOrderMap
     * @return <orderNo, ProjectActivity>
     */
    private Map<String, ProjectActivity> getOrderMapActivity(Map<String, HashSet<String>> parentOrderMap) {
        List<String> orderNoList = new ArrayList<>(parentOrderMap.keySet());
        ProjectActivityExample example = new ProjectActivityExample();
        ProjectActivityExample.Criteria criteria = example.createCriteria();
        criteria.andUnitIdEqualTo(SystemContext.getUnitId());
        if (orderNoList.size() > 0) {
            criteria.andOrderNoIn(orderNoList);
        }
        List<ProjectActivity> list = projectActivityMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream()
                .collect(Collectors.toMap(ProjectActivity::getOrderNo, Function.identity(), (a1, a2) -> a1));
    }

    /**
     * 拼接父层orderNo
     *
     * @param orderNo
     * @param parentOrderMap
     * @param firstOrderNoSet
     */
    private void setParentOrderNo(String orderNo,
                                  Map<String, HashSet<String>> parentOrderMap,
                                  Set<String> firstOrderNoSet) {
        if (orderNo.contains(".")) {
            // 获取父orderNo，将当前orderNo放到 parentOrderMap里
            if (parentOrderMap.containsKey(StringUtils.substringBeforeLast(orderNo, "."))) {
                parentOrderMap.get(StringUtils.substringBeforeLast(orderNo, ".")).add(orderNo);
            } else {
                HashSet<String> set = new HashSet();
                set.add(orderNo);
                parentOrderMap.put(StringUtils.substringBeforeLast(orderNo, "."), set);
            }
            setParentOrderNo(StringUtils.substringBeforeLast(orderNo, "."), parentOrderMap, firstOrderNoSet);
        } else {
            // 保存第一层orderNo
            firstOrderNoSet.add(orderNo);
        }
    }

    /**
     * 构建树
     *
     * @param parentOrderNo  父级node
     * @param projectCode    项目编码
     * @param parentActivity 活动事项列表 <orderNo, ProjectActivity>
     * @param parentOrderMap 上级order集合 <orderNo,下级>
     * @param groupMap       Map orderNo 分组 <orderNo, data>
     * @param dynamicFields  动态列
     * @return
     */
    private Map buildActivityTree(String parentOrderNo,
                                  String projectCode,
                                  Map<String, ProjectActivity> parentActivity,
                                  Map<String, HashSet<String>> parentOrderMap,
                                  Map<String, List<Map>> groupMap,
                                  List<WbsDynamicFieldsDto> dynamicFields) {

        Map node = new HashMap();

        node.put(WbsBudgetFieldConstant.SUMMARY_TYPE, WbsBudgetFieldConstant.ACTIVITY);

        // groupMap能匹配到就是最底层
        if (groupMap.containsKey(parentOrderNo)) {
            List<Map> childs = groupMap.get(parentOrderNo);
            if (CollectionUtils.isEmpty(childs)) {
                return node;
            }
            node.put(WbsBudgetFieldConstant.SUMMARY_CODE, (StringUtils.isNotBlank(projectCode) ? projectCode + "." : "") + MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_CODE));
            node.put(WbsBudgetFieldConstant.PROJECT_DETAIL_SELECT_FLAG, true);
            node.put(WbsBudgetFieldConstant.ACTIVITY_NAME, MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_NAME));
            node.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_TYPE));
            node.put(WbsBudgetFieldConstant.ACTIVITY_CODE, MapUtils.getString(childs.get(0), WbsBudgetFieldConstant.ACTIVITY_CODE));
            BigDecimal sumPrice = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(childs)) {
                sumPrice = childs.stream()
                        .map(map -> {
                            // 明细层
                            map.put(WbsBudgetFieldConstant.SUMMARY_TYPE, WbsBudgetFieldConstant.DETAIL);
                            map.put(WbsBudgetFieldConstant.PROJECT_DETAIL_SELECT_FLAG, false);
                            map.put(WbsBudgetFieldConstant.WBS_FULL_CODE, ProjectWbsBudgetUtils.getWbsFullCode(map, dynamicFields));
                            return new BigDecimal(map.get(WbsBudgetFieldConstant.PRICE).toString());
                        }).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 按wbsFullCode + activityOrderNo排序
                childs.sort(Comparator.comparing(a -> MapUtils.getString(a, WbsBudgetFieldConstant.WBS_FULL_CODE) + MapUtils.getString(a, WbsBudgetFieldConstant.ACTIVITY_ORDER_NO), Comparator.nullsFirst(Comparator.naturalOrder())));
                node.put(WbsBudgetFieldConstant.WBS_FULL_CODE, ProjectWbsBudgetUtils.getWbsFullCode(childs.get(0), dynamicFields));
            }

            // 四舍五入金额
            ProjectWbsBudgetUtils.roundingHalfUp(node, WbsBudgetFieldConstant.PRICE, sumPrice);

            node.put(WbsBudgetFieldConstant.DETAILS, childs);
            node.put(WbsBudgetFieldConstant.PROJECT_DETAIL_SELECT_FLAG, true);
        } else {

            ProjectActivity currentActivity = parentActivity.get(parentOrderNo);
            node.put(WbsBudgetFieldConstant.SUMMARY_CODE,
                    (StringUtils.isNotBlank(projectCode) ? projectCode + "." : "") + currentActivity.getCode());
            HashSet<String> childOrder = parentOrderMap.get(parentOrderNo);
            List<Map> childs = new ArrayList<>();
            for (String order : childOrder) {
                Map child = buildActivityTree(order,
                        (StringUtils.isNotBlank(projectCode) ? projectCode + "." : "") + currentActivity.getCode(),
                        parentActivity,
                        parentOrderMap,
                        groupMap,
                        dynamicFields);
                childs.add(child);
            }
            node.put(WbsBudgetFieldConstant.ACTIVITY_NAME, currentActivity.getName());
            node.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, currentActivity.getType());
            node.put(WbsBudgetFieldConstant.ACTIVITY_CODE, currentActivity.getCode());
            node.put(WbsBudgetFieldConstant.ACTIVITY_ORDER_NO, currentActivity.getOrderNo());
            node.put(WbsBudgetFieldConstant.PROJECT_DETAIL_SELECT_FLAG, false);
            BigDecimal sumPrice = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(childs)) {
                sumPrice = childs.stream()
                        .map(map -> new BigDecimal(map.get(WbsBudgetFieldConstant.PRICE).toString()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 按wbsFullCode + activityOrderNo排序
                childs.sort(Comparator.comparing(a -> MapUtils.getString(a, WbsBudgetFieldConstant.WBS_FULL_CODE) + MapUtils.getString(a, WbsBudgetFieldConstant.ACTIVITY_ORDER_NO), Comparator.nullsFirst(Comparator.naturalOrder())));
                node.put(WbsBudgetFieldConstant.WBS_FULL_CODE, ProjectWbsBudgetUtils.getWbsFullCode(childs.get(0), dynamicFields));
                node.put(WbsBudgetFieldConstant.CHILDS, childs);
            }
            // 四舍五入金额
            ProjectWbsBudgetUtils.roundingHalfUp(node, WbsBudgetFieldConstant.PRICE, sumPrice);
        }
        return node;
    }

    /**
     * 导入wbs预算检查
     * 导入wbs基线检查
     *
     * @param importResponseExcelVO
     * @param batchCode             batchCode批次号
     * @param wbsTemplateInfoId     wbs模板id
     * @return
     */
    @Override
    public ProjectWbsBudgetImportResponseExcelVO checkWbsBudgetFromExcel(ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO,
                                                                         String batchCode,
                                                                         Long wbsTemplateInfoId) {
        ProjectWbsBudgetImportResponseExcelVO result = new ProjectWbsBudgetImportResponseExcelVO();
        /* Excel导入记录 */
        List<Map> excelMapList = importResponseExcelVO.getExcelMapList();
        // 检查excel
        List<String> errorMsgList = checkImportExcelAndInit(batchCode, excelMapList, wbsTemplateInfoId, false);
        if (!CollectionUtils.isEmpty(errorMsgList)) {
            logger.warn("wbs导入检查错误：{}", JSON.toJSONString(errorMsgList));
            result.setErrorMsg(errorMsgList);
        }
        return result;
    }

    /**
     * 导入wbs预算 版本v2
     *
     * @param importResponseExcelVO
     * @return
     */
    @Override
    public ProjectWbsBudgetImportResponseExcelVO importWbsBudgetFromExcel(ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO) {
        ProjectWbsBudgetImportResponseExcelVO result = new ProjectWbsBudgetImportResponseExcelVO();
        Long wbsTemplateInfoId = importResponseExcelVO.getWbsTemplateInfoId();
        //校验是否已经开启描述自定义
        String describeDisplay = "";
        WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(wbsTemplateInfoId);
        if (null != wbsTemplateInfo) {
            describeDisplay = wbsTemplateInfo.getDescribeDisplay();
        }

        /* wbs预算基线（前端页面数据） */
        List<Map<String, Object>> pageBaselineMapList = importResponseExcelVO.getPageBaselineMapList();
        /* wbs预算（前端页面数据） */
        List<Map<String, Object>> pageBudgetMapList = importResponseExcelVO.getPageBudgetMapList();
        /* Excel导入记录 */
        List<Map> excelMapList = importResponseExcelVO.getExcelMapList();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("任务1");
        // wbs动态列
        List<WbsDynamicFieldsDto> wbsDynamicFieldList = wbsTemplateRuleService.getWbsDynamicFields(importResponseExcelVO.getWbsTemplateInfoId());
        // 检查excel，设置默认值
        List<String> errorMsgList = checkImportExcelAndInit(importResponseExcelVO.getBatchCode(), excelMapList, importResponseExcelVO.getWbsTemplateInfoId(), false);
        if (!CollectionUtils.isEmpty(errorMsgList)) {
            // 20220704达浩确认这种报错1条1条报
            throw new ApplicationBizException(errorMsgList.get(0));
        }
        stopWatch.stop();


        stopWatch.start("任务2");
        // wbs预算Map，只存有效记录（增量或覆盖导入）
        Map<String, Map> uniqueBudgetMap = new HashMap();

        // wbs预算集合，包含已删除的wbs预算
        List<Map> budgetResultList = mergeWbsBudget(importResponseExcelVO,
                excelMapList,
                pageBudgetMapList,
                uniqueBudgetMap,
                wbsDynamicFieldList,
                wbsTemplateInfoId,
                describeDisplay);
        stopWatch.stop();


        stopWatch.start("任务3");
        // 校验同一个WBS下面，同一个经济事项不允许重复
        errorMsgList = projectWbsBudgetCheckHelpper.checkWbsRepeatFeeType(budgetResultList, importResponseExcelVO.getWbsTemplateInfoId(), SystemContext.getUnitId());
        if (CollectionUtils.isNotEmpty(errorMsgList)) {
            // 20220704达浩确认这种报错1条1条报
            throw new ApplicationBizException(errorMsgList.get(0));
        }
        stopWatch.stop();

        stopWatch.start("任务4");
        // wbs预算集合，包含已删除的wbs预算
        List<Map> baselineResultList = mergeWbsBaseline(importResponseExcelVO,
                excelMapList,
                pageBaselineMapList,
                uniqueBudgetMap,
                wbsDynamicFieldList,
                wbsTemplateInfoId,
                describeDisplay);
        stopWatch.stop();


        stopWatch.start("任务5");
        // 四舍五入金额
        ProjectWbsBudgetUtils.roundingHalfUp(budgetResultList, WbsBudgetFieldConstant.PRICE);
        ProjectWbsBudgetUtils.roundingHalfUp(baselineResultList, WbsBudgetFieldConstant.BASELINE_COST);
        result.setWbsBudgetList(budgetResultList);
        result.setWbsBudgetBaselineList(baselineResultList);
        stopWatch.stop();

        // 任务总的耗时  如果你想获取到每个任务详情（包括它的任务名、耗时等等）可使用
        logger.warn("所有任务总耗时：" + stopWatch.getTotalTimeMillis());
        logger.warn("任务总数：" + stopWatch.getTaskCount());
        logger.warn("所有任务详情：");
        for (int i = 0; i < stopWatch.getTaskCount(); i++) {
            StopWatch.TaskInfo taskInfo = stopWatch.getTaskInfo()[i];
            logger.warn("任务名：{}，耗时(毫秒)：{}，耗时（秒）:{}", taskInfo.getTaskName(), taskInfo.getTimeMillis(), taskInfo.getTimeSeconds());

        }

        return result;
    }

    /**
     * wbs预算（增量或覆盖导入）
     *
     * @param importResponseExcelVO 导入参数
     * @param excelMapList          Excel导入记录
     * @param pageBudgetMapList     前端预算记录
     * @param uniqueBudgetMap       wbs预算Map，只存有效记录（增量或覆盖导入）
     * @param wbsDynamicFieldList   wbs动态列
     * @return
     */
    private List<Map> mergeWbsBudget(ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO, List<Map> excelMapList, List<Map<String, Object>> pageBudgetMapList, Map<String, Map> uniqueBudgetMap, List<WbsDynamicFieldsDto> wbsDynamicFieldList, Long wbsTemplateInfoId, String describeDisplay) {
        // wbs预算（增量或覆盖导入） 包含已删除的wbs预算
        List<Map> budgetResultList = new ArrayList<>();
        // 已删除的wbs预算
        List<Map> removeBudgetMapList = new ArrayList<>();

        // 获取费用类型经济事项
        List<FeeItemExpenseTypeDto> feeItemExpenseTypeDtoList = projectTypeService.selectFeeItemExpenseType(importResponseExcelVO.getProjectType());

        /* 增量导入 */
        if (WbsBudgetFieldConstant.ADD.equals(importResponseExcelVO.getImportType()) && !CollectionUtils.isEmpty(pageBudgetMapList)) {
            // 前端预算合并到uniqueBudgetMap
            ProjectWbsBudgetUtils.wbsBudget2UniqueMap(pageBudgetMapList, uniqueBudgetMap, removeBudgetMapList, wbsDynamicFieldList);
        }
        for (Map excel : excelMapList) {
            // 唯一key = wbs动态列 + activity活动事项
            String uniqueBudgetKey = ProjectWbsBudgetUtils.getWbsBudgetUniqueKey(excel, wbsDynamicFieldList, null);
            Map budgetMap = new HashMap();
            if (uniqueBudgetMap.containsKey(uniqueBudgetKey)) {
                budgetMap = uniqueBudgetMap.get(uniqueBudgetKey);
                // wbs预算 默认2位小数
                BigDecimal wbsBudgetPrice = BigDecimal.valueOf(MapUtils.getDouble(budgetMap, WbsBudgetFieldConstant.PRICE));
                // excel预算基线
                BigDecimal excelPrice = BigDecimal.valueOf(MapUtils.getDouble(excel, WbsBudgetFieldConstant.BASELINE_COST));
                budgetMap.put(WbsBudgetFieldConstant.PRICE, wbsBudgetPrice.add(excelPrice).setScale(2, RoundingMode.HALF_UP).toPlainString());
            } else {
                budgetMap.putAll(excel);
                // wbs预算 默认2位小数
                budgetMap.put(WbsBudgetFieldConstant.PRICE, BigDecimal.valueOf(MapUtils.getDouble(excel, WbsBudgetFieldConstant.BASELINE_COST)).setScale(2, RoundingMode.HALF_UP).toPlainString());
                // wbs预算基线
                budgetMap.put(WbsBudgetFieldConstant.BASELINE_COST, BigDecimal.ZERO);
                // 设置经济事项
                setFeeExpenseType(feeItemExpenseTypeDtoList, budgetMap, wbsDynamicFieldList);
            }
            String descriptionString = "";
            Object description = budgetMap.get(WbsBudgetFieldConstant.DESCRIPTION);
            if (description != null) {
                descriptionString = description.toString();
            }
            //如果开启了自定义描述，则通过自定义描述返回，否则返回原来末级的描述
            descriptionRuleSet(wbsTemplateInfoId, describeDisplay, budgetMap, descriptionString);
            // 默认rowId
            budgetMap.put(WbsBudgetFieldConstant.ROW_ID, budgetMap.containsKey(WbsBudgetFieldConstant.ID) ? budgetMap.get(WbsBudgetFieldConstant.ID) : UUID.randomUUID().toString());

            uniqueBudgetMap.put(uniqueBudgetKey, budgetMap);
        }

        budgetResultList.addAll(uniqueBudgetMap.values());
        if (!CollectionUtils.isEmpty(removeBudgetMapList)) {
            budgetResultList.addAll(removeBudgetMapList);
        }
        return budgetResultList;
    }

    private void descriptionRuleSet(Long wbsTemplateInfoId, String describeDisplay, Map budgetMap, String descriptionString) {
        if (DescribeDisplayEnum.CUSTOMIZE.getCode().equals(describeDisplay)) {
            TreeMap<Integer, String> fieldValueMap = new TreeMap<>();
            for (Object keyObj : budgetMap.keySet()) {
                String key = keyObj.toString();
                if (key.startsWith("field_")) {
                    int fieldNum = Integer.parseInt(key.substring(6));
                    String fieldValue = budgetMap.get(key).toString();
                    fieldValueMap.put(fieldNum, fieldValue);
                }
            }
            String dynamicCombination = String.join("-", fieldValueMap.values());
            if (!dynamicCombination.isEmpty()) {
                WbsCustomizeRuleExample example = new WbsCustomizeRuleExample();
                example.createCriteria()
                        .andDynamicCombinationEqualTo(dynamicCombination)
                        .andWbsTemplateInfoIdEqualTo(wbsTemplateInfoId)
                        .andDeletedFlagEqualTo(false);

                List<WbsCustomizeRule> rules = wbsCustomizeRuleMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(rules)) {
                    budgetMap.put(WbsBudgetFieldConstant.DESCRIPTION, rules.get(0).getDescription());
                } else {
                    // 如果没有查询到自定义描述，可以设置为原来末级带出来的描述
                    budgetMap.put(WbsBudgetFieldConstant.DESCRIPTION, descriptionString);
                }
            }
        }
    }

    /**
     * wbs基线（增量或覆盖导入）
     *
     * @param importResponseExcelVO 导入参数
     * @param excelMapList          Excel导入记录
     * @param pageBaselineMapList   前端基线记录
     * @param uniqueBudgetMap       wbs预算Map，只存有效记录（增量或覆盖导入）
     * @param wbsDynamicFieldList   wbs动态列
     * @return
     */
    private List<Map> mergeWbsBaseline(ProjectWbsBudgetImportResponseExcelVO importResponseExcelVO, List<Map> excelMapList, List<Map<String, Object>> pageBaselineMapList, Map<String, Map> uniqueBudgetMap, List<WbsDynamicFieldsDto> wbsDynamicFieldList, Long wbsTemplateInfoId, String describeDisplay) {

        // wbs基线（增量或覆盖导入） 包含已删除的wbs基线
        List<Map> baselineResultList = new ArrayList<>();
        // 已删除的wbs基线
        List<Map> removeBaselineMapList = new ArrayList<>();
        // wbs基线Map，只存有效记录（增量或覆盖导入）
        Map<String, Map> uniqueBaselineMap = new HashMap<>();

        /* 增量导入 */
        if (WbsBudgetFieldConstant.ADD.equals(importResponseExcelVO.getImportType()) && !CollectionUtils.isEmpty(pageBaselineMapList)) {
            // 前端基线合并到uniqueBaselineMap
            ProjectWbsBudgetUtils.wbsBaseline2UniqueMap(pageBaselineMapList, uniqueBudgetMap, uniqueBaselineMap, removeBaselineMapList, wbsDynamicFieldList);
        }
        for (Map excel : excelMapList) {
            // 唯一key = wbs动态列 + activity活动事项 + 批次号
            String uniqueBaselineKey = ProjectWbsBudgetUtils.getWbsBaselineUniqueKey(excel, wbsDynamicFieldList, importResponseExcelVO.getBatchCode(), null);
            Map baselineMap = new HashMap();
            if (uniqueBaselineMap.containsKey(uniqueBaselineKey)) {
                baselineMap = uniqueBaselineMap.get(uniqueBaselineKey);
                BigDecimal baselineCost = BigDecimal.valueOf(MapUtils.getDouble(baselineMap, WbsBudgetFieldConstant.BASELINE_COST));
                BigDecimal excelPrice = BigDecimal.valueOf(MapUtils.getDouble(excel, WbsBudgetFieldConstant.BASELINE_COST));
                baselineMap.put(WbsBudgetFieldConstant.BASELINE_COST, baselineCost.add(excelPrice));
            } else {
                baselineMap.putAll(excel);
            }

            String descriptionString = "";
            Object description = baselineMap.get(WbsBudgetFieldConstant.DESCRIPTION);
            if (description != null) {
                descriptionString = description.toString();
            }
            //如果开启了自定义描述，则通过自定义描述返回，否则返回原来末级的描述
            descriptionRuleSet(wbsTemplateInfoId, describeDisplay, baselineMap, descriptionString);

            // 预算默认2位小数
            baselineMap.put(WbsBudgetFieldConstant.BASELINE_COST, BigDecimal.valueOf(MapUtils.getDouble(baselineMap, WbsBudgetFieldConstant.BASELINE_COST)).setScale(2, RoundingMode.HALF_UP).toPlainString());
            // 默认rowId
            baselineMap.put(WbsBudgetFieldConstant.ROW_ID, baselineMap.containsKey(WbsBudgetFieldConstant.ID) ?
                    baselineMap.get(WbsBudgetFieldConstant.ID) : UUID.randomUUID().toString());

            // 设置wbs预算id
            String uniqueBudgetKey = ProjectWbsBudgetUtils.getWbsBudgetUniqueKey(baselineMap, wbsDynamicFieldList, null);
            Map wbsBudget = uniqueBudgetMap.get(uniqueBudgetKey);
            if (StringUtils.isNotBlank(MapUtils.getString(wbsBudget, WbsBudgetFieldConstant.ROW_ID))) {
                baselineMap.put(WbsBudgetFieldConstant.PROJECT_WBS_BUDGET_ID, MapUtils.getString(wbsBudget, WbsBudgetFieldConstant.ROW_ID));
            }
            uniqueBaselineMap.put(uniqueBaselineKey, baselineMap);
        }

        baselineResultList.addAll(uniqueBaselineMap.values());
        if (!CollectionUtils.isEmpty(removeBaselineMapList)) {
            baselineResultList.addAll(removeBaselineMapList);
        }
        return baselineResultList;
    }


    /**
     * 查询wbs预算列表
     *
     * @param projectId
     * @return
     */
    @Override
    public List<ProjectWbsBudgetDto> findDetailWbsBudget(Long projectId) {
        Project project = projectMapper.selectByPrimaryKey(projectId);
        if (Objects.isNull(project)) {
            throw new ApplicationBizException("项目不存在");
        }
        Map<String, Object> param = new HashMap<>();
        param.put(WbsBudgetFieldConstant.PROJECT_ID, projectId);
        List<ProjectWbsBudgetDto> projectWbsBudgetList = projectWbsBudgetExtMapper.listByParam(param);
        //TODO 之前添加的 wbsSummaryCode字段还有点问题，先采用拼接
        projectWbsBudgetList.forEach(s -> s.setWbsSummaryCode(s.getProjectCode() + "-" + s.getWbsFullCode()));
        return projectWbsBudgetList;
    }

    /**
     * 检查导入excel（wbs预算导入，wbs预算基线导入）
     * 并设置默认值
     *
     * @param batchCode         批次号
     * @param excelMapList      excel记录（基线）
     * @param wbsTemplateInfoId wbs模板id
     * @param isAllowMinuses    是否允许金额负数：true允许,false不允许
     * @return 错误提示
     */
    @Override
    public List<String> checkImportExcelAndInit(String batchCode, List<Map> excelMapList, Long wbsTemplateInfoId, boolean isAllowMinuses) {

        // WBS模板
        WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(wbsTemplateInfoId);

        // 获取WBS自定义规则
        WbsCustomizeRuleExample example = new WbsCustomizeRuleExample();
        example.createCriteria().andWbsTemplateInfoIdEqualTo(wbsTemplateInfoId).andDeletedFlagEqualTo(Boolean.FALSE);
        Map<String, String> wbsCustomizeRuleMap = wbsCustomizeRuleMapper.selectByExample(example).stream().collect(Collectors.toMap(WbsCustomizeRule::getDynamicCombination, WbsCustomizeRule::getDescription, (a, b) -> a));

        List<String> errorMsgList = new ArrayList<>();
        List<WbsTemplateRuleCache> wbsCacheList = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);

        // activity缓存Map（存起来减少redis交互）
        Map<String, ProjectActivityCache> activityCacheMap = new HashMap<>();
        // wbs规则详情缓存Map（存起来减少redis交互）
        Map<String, WbsTemplateRuleDetailCache> wbsCacheMap = new HashMap<>();

        for (int i = 0; i < excelMapList.size(); i++) {
            Map excelMap = excelMapList.get(i);
            /**
             * 校验金额
             */
            if (!BigDecimalUtils.isBigDecimal(MapUtils.getString(excelMap, WbsBudgetFieldConstant.BASELINE_COST))) {
                errorMsgList.add(String.format("行%s列%s，活动事项%s，金额有误", excelMap.get(WbsBudgetFieldConstant.EXCEL_ROW_NUM), excelMap.get(excelMap.get(WbsBudgetFieldConstant.ACTIVITY_NAME)), excelMap.get(WbsBudgetFieldConstant.ACTIVITY_CODE)));
                continue;
            } else if (!isAllowMinuses && MapUtils.getDouble(excelMap, WbsBudgetFieldConstant.BASELINE_COST) < 0) {
                errorMsgList.add(String.format("行%s列%s，活动事项%s，金额不能为负数", excelMap.get(WbsBudgetFieldConstant.EXCEL_ROW_NUM), excelMap.get(excelMap.get(WbsBudgetFieldConstant.ACTIVITY_NAME)), excelMap.get(WbsBudgetFieldConstant.ACTIVITY_CODE)));
                continue;
            }

            /**
             * 活动事项校验
             * 1、code必须存在
             * 2、预算事项=true
             */
            String activityCode = MapUtils.getString(excelMap, WbsBudgetFieldConstant.ACTIVITY_CODE);
            ProjectActivityCache activityCache;
            if (activityCacheMap.containsKey(activityCode)) {
                activityCache = activityCacheMap.get(activityCode);
            } else {
                activityCache = ProjectActivityCacheUtils.getCache(SystemContext.getUnitId(), activityCode);
                activityCacheMap.put(activityCode, activityCache);
            }
            String error1 = projectWbsBudgetCheckHelpper.checkActivityValid(activityCache, activityCode);
            if (StringUtils.isNotBlank(error1)) {
                errorMsgList.add(String.format("行%s列%s，%s", excelMap.get(WbsBudgetFieldConstant.EXCEL_ROW_NUM), excelMap.get(excelMap.get(WbsBudgetFieldConstant.ACTIVITY_NAME)), error1));
                continue;
            }

            /**
             * wbs是否严控校验
             * 1、code必须存在
             * 2、层级必须=明细
             * 3、code未失效
             */
            String error2 = projectWbsBudgetCheckHelpper.checkWbsValid(excelMap, wbsCacheList, wbsCacheMap);
            if (StringUtils.isNotBlank(error2)) {
                errorMsgList.add(String.format("行%s%s", excelMap.get(WbsBudgetFieldConstant.EXCEL_ROW_NUM), error2));
                continue;
            }

            /**
             * 1、activity 类别属性 = "预算"才需要校验经济事项
             * 2、wbs + activity 预算必须绑定一个且只能绑定一个经济事项
             */
            String error3 = projectWbsBudgetCheckHelpper.checkWbsActivityByOnlyOneFeeType(excelMap, activityCacheMap, wbsCacheList, SystemContext.getUnitId());
            if (StringUtils.isNotBlank(error3)) {
                errorMsgList.add(String.format("行%s列%s，%s", excelMap.get(WbsBudgetFieldConstant.EXCEL_ROW_NUM), excelMap.get(excelMap.get(WbsBudgetFieldConstant.ACTIVITY_NAME)), error3));
                continue;
            }

            // activity默认值
            excelMap.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, activityCache.getType());
            excelMap.put(WbsBudgetFieldConstant.ACTIVITY_NAME, activityCache.getName());
            excelMap.put(WbsBudgetFieldConstant.ACTIVITY_ORDER_NO, activityCache.getOrderNo());

            // wbs默认值
            WbsTemplateRuleDetailCache wbsCache;
            Long lastWbsId = wbsCacheList.get(wbsCacheList.size() - 1).getId();
            String lastWbsCode = MapUtils.getString(excelMap, wbsCacheList.get(wbsCacheList.size() - 1).getKey());
            if (StringUtils.isNotEmpty(lastWbsCode)) {
                lastWbsCode = lastWbsCode.trim();
            }
            String wbsKey = lastWbsId + lastWbsCode;
            if (wbsCacheMap.containsKey(wbsKey)) {
                wbsCache = wbsCacheMap.get(wbsKey);
            } else {
                wbsCache = WbsTemplateRuleDetailCacheUtils.getCache(lastWbsId, lastWbsCode);
                wbsCacheMap.put(wbsKey, wbsCache);
            }
            if (null != wbsCache) {
 //               excelMap.put(WbsBudgetFieldConstant.DESCRIPTION, wbsCache.getDescription());
                excelMap.put(WbsBudgetFieldConstant.WBS_FULL_CODE, ProjectWbsBudgetUtils.getCacheWbsFullCode(excelMap, wbsCacheList));
            }

            // 从最后一级开始，逐级向上查找,获取描述
            String description = "";
            for (int j = wbsCacheList.size() - 1; j >= 0; j--) {

                Long wbsId = wbsCacheList.get(j).getId();
                String wbsCode = MapUtils.getString(excelMap, wbsCacheList.get(j).getKey());
                wbsCache = WbsTemplateRuleDetailCacheUtils.getCache(wbsId, wbsCode);
                description = wbsTemplateRuleDetailService.getLastWbs(wbsTemplateInfo, MapUtils.getString(excelMap, WbsBudgetFieldConstant.WBS_FULL_CODE), wbsCache, wbsCustomizeRuleMap);
                if (StringUtils.isNotBlank(description)) {
                    break;
                }
            }
            excelMap.put(WbsBudgetFieldConstant.DESCRIPTION, description);
        }

        /**
         *  检查wbs与activity是否存在重复值：（导入场景，只校验excel）
         **/
        errorMsgList.addAll(projectWbsBudgetCheckHelpper.checkWbsActivityByRepeat(excelMapList, wbsCacheList));
        return errorMsgList;
    }

    /**
     * 异步：创建wbs预算
     * 先删除历史记录，再创建新的记录
     *
     * @param projectInfo       项目
     * @param currentThreadUser 缓存中的当前用户
     * @param saveProjectBudget 是否保存项目预算
     * @return 汇总预算
     */
    @Async
    @Override
    public void asyncCreateProjectWbsBudget(ProjectDto projectInfo, UserInfoDto currentThreadUser, boolean saveProjectBudget) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        logger.info("异步：创建wbs预算，projectId={}", projectInfo.getId());
        /* 异步重新获取ThreadLocal */
        if (null == SystemContext.get()) {
            SystemContext.set(currentThreadUser);
            logger.info("异步：创建wbs预算，currentThreadUser={}", currentThreadUser != null ? JSON.toJSONString(currentThreadUser) : null);
        }
        try {
            /* 阻塞1秒，让主线程项目保存先跑完 */
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            logger.error("项目立项asyncCreateProjectWbsBudget线程sleep异常：{}", e.getMessage());
            // Restore interrupted state...
            Thread.currentThread().interrupt();
        }
        /* 清空历史wbs预算相关记录 */
        if (null != projectInfo.getId()) {
            projectWbsBudgetExtMapper.deleteByProjectId(projectInfo.getId());
            projectWbsBudgetBaselineExtMapper.deleteByProjectId(projectInfo.getId());
            projectWbsBudgetDynamicExtMapper.deleteByProjectId(projectInfo.getId());
            projectWbsBudgetSummaryExtMapper.deleteByProjectId(projectInfo.getId());
        }

        // wbs基线批次
        List<ProjectBaselineBatchDto> wbsBaselineBatchList = projectInfo.getProjectWbsBudgetObjectDTO().getWbsBaselineBatchList();
        if (!CollectionUtils.isEmpty(wbsBaselineBatchList)) {
            for (ProjectBaselineBatchDto batch : wbsBaselineBatchList) {
                if (StringUtils.indexOf(batch.getCode(), "项目号") == 0) {
                    // 用项目编码替换前端固定写死的“项目号”
                    batch.setCode(projectInfo.getCode() + StringUtils.substringAfter(batch.getCode(), "项目号"));
                }
            }
        }
        // 项目wbs预算
        List<Map<String, Object>> wbsBudgetList = projectInfo.getProjectWbsBudgetObjectDTO().getWbsBudgetList();
        if (!CollectionUtils.isEmpty(wbsBudgetList)) {
            for (Map<String, Object> map : wbsBudgetList) {
                // 清空前端传的id，保存走新增逻辑
                map.remove(WbsBudgetFieldConstant.ID);
            }
            // 没有更新场景，已经在保存入口清理过历史数据）前端传递deletedFlag=1的数据直接不处理
            Iterator<Map<String, Object>> iterator = wbsBudgetList.iterator();
            while (iterator.hasNext()) {
                if (Objects.equals(Boolean.TRUE, MapUtils.getBoolean(iterator.next(), WbsBudgetFieldConstant.DELETED_FLAG))) {
                    iterator.remove();
                }
            }
        }
        // 项目wbs预算基线
        List<Map<String, Object>> wbsBudgetBaselineList = projectInfo.getProjectWbsBudgetObjectDTO().getWbsBudgetBaselineList();
        if (!CollectionUtils.isEmpty(wbsBudgetBaselineList)) {
            for (Map<String, Object> map : wbsBudgetBaselineList) {
                // 清空前端传的id，保存走新增逻辑
                map.remove(WbsBudgetFieldConstant.ID);
                if (StringUtils.indexOf(MapUtils.getString(map, WbsBudgetFieldConstant.PROJECT_BASELINE_BATCH_CODE), "项目号") == 0) {
                    // 用项目编码替换前端固定写死的“项目号”
                    map.put(WbsBudgetFieldConstant.PROJECT_BASELINE_BATCH_CODE, projectInfo.getCode() + StringUtils.substringAfter(MapUtils.getString(map, WbsBudgetFieldConstant.PROJECT_BASELINE_BATCH_CODE), "项目号"));
                }
            }
            // 没有更新场景，已经在保存入口清理过历史数据）前端传递deletedFlag=1的数据直接不处理
            Iterator<Map<String, Object>> iterator = wbsBudgetBaselineList.iterator();
            while (iterator.hasNext()) {
                if (Objects.equals(Boolean.TRUE, MapUtils.getBoolean(iterator.next(), WbsBudgetFieldConstant.DELETED_FLAG))) {
                    iterator.remove();
                }
            }
        }
        // 获取wbs动态列
        List<WbsDynamicFieldsDto> dynamicFields = wbsTemplateRuleService.getWbsDynamicFields(projectInfo.getWbsTemplateInfoId());

        // 1.保存项目基线批次
        List<ProjectBaselineBatch> projectBaselineBatchList = saveProjectBaselineBatch(projectInfo.getId(), wbsBaselineBatchList);

        // 2.保存项目wbs预算 + 3.保存项目wbs预算动态列
        List<ProjectWbsBudget> projectWbsBudgetList = saveProjectWbsBudget(projectInfo, wbsBudgetList, dynamicFields, SystemContext.getUnitId());

        // 4.保存项目wbs预算基线
        saveProjectWbsBudgetBaseline(projectInfo, projectWbsBudgetList, projectBaselineBatchList, wbsBudgetBaselineList, dynamicFields);

        // 5.生成预算统计数据
        BigDecimal budgetCost = projectWbsBudgetSummaryService.saveWbsBudgetSummary(projectInfo.getId());

        if (saveProjectBudget) {
            // 6.更新项目预算
            Project project = new Project();
            project.setId(projectInfo.getId());
            project.setBudgetCost(budgetCost);
            projectMapper.updateByPrimaryKeySelective(project);
        }
        logger.info("异步：创建wbs预算，projectId={},耗时={}秒", projectInfo.getId(), stopwatch.stop().elapsed(TimeUnit.SECONDS));
    }

    /**
     * 获取前端格式的wbs预算
     *
     * @param projectId 项目id
     * @return
     */
    @Override
    public List<Map<String, Object>> findWbsBudgetMapsByWebfront(Long projectId) {
        /* wbs预算基线 */
        List<Map<String, Object>> wbsBudgetMapList = new ArrayList<>();
        ProjectWbsBudgetExample projectWbsBudgetExample = new ProjectWbsBudgetExample();
        projectWbsBudgetExample.createCriteria().andProjectIdEqualTo(projectId);
        List<ProjectWbsBudget> wbsBudgetList = projectWbsBudgetMapper.selectByExample(projectWbsBudgetExample);
        for (ProjectWbsBudget wbsBudget : wbsBudgetList) {
            // 过滤System
            if (Objects.equals(SYSTEM_ACTIVITY_CODE, wbsBudget.getActivityCode()) && Objects.equals(SYSTEM_ACTIVITY_ORDER_NO, wbsBudget.getActivityOrderNo())) {
                continue;
            }
            Map<String, Object> wbsBudgetMap = ProjectWbsBudgetDto.entity2Map(wbsBudget);
            wbsBudgetMap.put(WbsBudgetFieldConstant.ROW_ID, wbsBudgetMap.get(WbsBudgetFieldConstant.ID));
            wbsBudgetMapList.add(wbsBudgetMap);
        }
        return wbsBudgetMapList;
    }

    /**
     * 获取前端格式的wbs预算关联信息
     *
     * @param projectId 项目id
     * @return
     */
    @Override
    public ProjectWbsBudgetObjectDto findWbsBudgetInfoByWebfront(Long projectId) {
        ProjectWbsBudgetObjectDto result = new ProjectWbsBudgetObjectDto();
        /* wbs预算 */
        List<Map<String, Object>> wbsBudgetMapList = findWbsBudgetMapsByWebfront(projectId);

        /* wbs预算基线 */
        List<Map<String, Object>> wbsBudgetBaselineMapList = projectWbsBudgetBaselineService.findWbsBudgetBaselineMapsByWebfront(projectId);

        /* 基线批次 */
        ProjectBaselineBatchExample projectBaselineBatchExample = new ProjectBaselineBatchExample();
        projectBaselineBatchExample.createCriteria().andProjectIdEqualTo(projectId);
        List<ProjectBaselineBatch> baselineBatchList = projectBaselineBatchMapper.selectByExample(projectBaselineBatchExample);
        List<ProjectBaselineBatchDto> baselineBatchDtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(baselineBatchList)) {
            baselineBatchDtoList = BeanConverter.copy(baselineBatchList, ProjectBaselineBatchDto.class);
        }

        result.setWbsBudgetList(wbsBudgetMapList);
        result.setWbsBudgetBaselineList(wbsBudgetBaselineMapList);
        result.setWbsBaselineBatchList(baselineBatchDtoList);
        return result;
    }

    @Override
    public String checkWbsBudgetByActivity(Long unitId, String activityCode) {
        unitId = unitId != null ? unitId : SystemContext.getUnitId();
        if (unitId == null) {
            return null;
        }
        if (StringUtils.isEmpty(activityCode)) {
            return null;
        }
        List<String> activityCodeList = new ArrayList<>();
        activityCodeList.add(activityCode);
        // 查询所有上级
        while (activityCode.contains(".")) {
            activityCode = activityCode.substring(0, activityCode.lastIndexOf("."));
            activityCodeList.add(activityCode);
        }
        // 根据活动编码+使用单位查询
        ProjectActivityExample projectActivityExample = new ProjectActivityExample();
        projectActivityExample.createCriteria()
                .andUnitIdEqualTo(unitId)
                .andCodeIn(activityCodeList)
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andBudgetMattersStateEqualTo(Boolean.TRUE);
        List<ProjectActivity> projectActivityList = projectActivityMapper.selectByExample(projectActivityExample);
        if (ListUtils.isNotEmpty(projectActivityList)) {
            return projectActivityList.get(0).getCode();
        }
        return null;
    }

    @Override
    public ProjectWbsBudget existWbsBudgetByActivity(Long projectId, String wbsFullCode, String activityCode) {
        if (StringUtils.isEmpty(activityCode)
                || projectId == null
                || StringUtils.isEmpty(wbsFullCode)) {
            return null;
        }
        List<String> activityCodeList = new ArrayList<>();
        activityCodeList.add(activityCode);
        // 查询所有上级
        while (activityCode.contains(".")) {
            activityCode = activityCode.substring(0, activityCode.lastIndexOf("."));
            activityCodeList.add(activityCode);
        }

        // 根据wbscode查询所有预算
        ProjectWbsBudgetExample projectWbsBudgetExample = new ProjectWbsBudgetExample();
        projectWbsBudgetExample.createCriteria()
                .andProjectIdEqualTo(projectId)
                .andWbsFullCodeEqualTo(wbsFullCode)
                .andActivityCodeIn(activityCodeList)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsBudget> projectWbsBudgetList = projectWbsBudgetMapper.selectByExample(projectWbsBudgetExample);
        if (ListUtils.isEmpty(projectWbsBudgetList)) {
            return null;
        }
        return projectWbsBudgetList.get(0);
    }

    @Override
    public Long addWbsBudgetByActivity(Long projectId, String wbsFullCode, String activityCode) {
        Project project = projectMapper.selectByPrimaryKey(projectId);
        if (project == null) {
            return null;
        }
        Unit parentUnit = CacheDataUtils.findUnitById(project.getUnitId());

        // 查询活动事项以及活动事项的上级（活动事项的上下级关系在“项目活动事项维护”中设置）存不存在
        ProjectWbsBudget wbsBudgetByActivity = existWbsBudgetByActivity(projectId, wbsFullCode, activityCode);
        if (wbsBudgetByActivity != null) {
            return wbsBudgetByActivity.getId();
        }

        // 根据wbscode查询所有预算
        ProjectWbsBudgetExample projectWbsBudgetExample = new ProjectWbsBudgetExample();
        projectWbsBudgetExample.createCriteria()
                .andProjectIdEqualTo(projectId)
                .andWbsFullCodeEqualTo(wbsFullCode)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsBudget> projectWbsBudgetList = projectWbsBudgetMapper.selectByExample(projectWbsBudgetExample);
        if (ListUtils.isEmpty(projectWbsBudgetList)) {
            return null;
        }

        // 根据活动编码+使用单位查询
        ProjectActivityExample projectActivityExample = new ProjectActivityExample();
        projectActivityExample.createCriteria()
                .andUnitIdEqualTo(parentUnit.getParentId())
                .andCodeEqualTo(activityCode)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectActivity> projectActivityList = projectActivityMapper.selectByExample(projectActivityExample);
        if (ListUtils.isEmpty(projectActivityList)) {
            return null;
        }
        ProjectActivity projectActivity = projectActivityList.get(0);
        ProjectWbsBudget projectWbsBudget = projectWbsBudgetList.get(0);
        projectWbsBudget.setId(null);
        projectWbsBudget.setProjectId(projectId);
        projectWbsBudget.setProjectCode(project.getCode());
        if (StringUtils.isNotEmpty(projectWbsBudget.getProjectCode()) && StringUtils.isNotEmpty(projectWbsBudget.getWbsFullCode())) {
            projectWbsBudget.setWbsSummaryCode(String.format("%s-%s", projectWbsBudget.getProjectCode(), projectWbsBudget.getWbsFullCode()));
        }
        projectWbsBudget.setActivityOrderNo(projectActivity.getOrderNo());
        projectWbsBudget.setActivityCode(projectActivity.getCode());
        projectWbsBudget.setActivityName(projectActivity.getName());
        projectWbsBudget.setActivityType(projectActivity.getType());
        projectWbsBudget.setPrice(BigDecimal.ZERO);
        projectWbsBudget.setBaselineCost(BigDecimal.ZERO);
        projectWbsBudget.setDemandCost(BigDecimal.ZERO);
        projectWbsBudget.setOnTheWayCost(BigDecimal.ZERO);
        projectWbsBudget.setIncurredCost(BigDecimal.ZERO);
        projectWbsBudget.setRemainingCost(BigDecimal.ZERO);
        projectWbsBudget.setChangeAccumulateCost(BigDecimal.ZERO);
        projectWbsBudget.setCreateAt(new Date());
        projectWbsBudget.setCreateBy(SystemContext.getUserId());
        projectWbsBudget.setUpdateAt(null);
        projectWbsBudget.setUpdateBy(null);
        projectWbsBudgetMapper.insert(projectWbsBudget);

        // 保存项目wbs预算动态列
        insertProjectWbsBudgetDynamic(projectWbsBudget);
        return projectWbsBudget.getId();
    }

    private void insertProjectWbsBudgetDynamic(ProjectWbsBudget projectWbsBudget) {
        if (StringUtils.isNotBlank(projectWbsBudget.getDynamicFields()) && StringUtils.isNotBlank(projectWbsBudget.getDynamicValues()) && StringUtils.isNotBlank(projectWbsBudget.getDynamicWbsTemplateRuleIds())) {
            String[] dynamicFieldArr = projectWbsBudget.getDynamicFields().split(",");
            String[] dynamicValueArr = projectWbsBudget.getDynamicValues().split(",");
            String[] dynamicWbsTemplateRuleIdsArr = projectWbsBudget.getDynamicWbsTemplateRuleIds().split(",");
            // 保存动态列
            for (int i = 0; i < dynamicFieldArr.length; i++) {
                ProjectWbsBudgetDynamic projectWbsBudgetDynamic = new ProjectWbsBudgetDynamic();
                projectWbsBudgetDynamic.setProjectId(projectWbsBudget.getProjectId());
                projectWbsBudgetDynamic.setProjectWbsBudgetId(projectWbsBudget.getId());
                projectWbsBudgetDynamic.setFieldName(dynamicFieldArr[i]);
                projectWbsBudgetDynamic.setWbsTemplateRuleId(Long.parseLong(dynamicWbsTemplateRuleIdsArr[i]));
                projectWbsBudgetDynamic.setWbsTemplateRuleDetailCode(dynamicValueArr[i]);
                projectWbsBudgetDynamic.setDeletedFlag(false);
                projectWbsBudgetDynamic.setVersion(1L);
                projectWbsBudgetDynamicMapper.insertSelective(projectWbsBudgetDynamic);
            }
        }

    }

    @Override
    public List<ProjectWbsBudgetDto> findUserWbsBudget(Long projectId) {

        // 查找用户关联的HR部门费率类型设置关联的wbs约束
        List<WbsConstraintDto> wbsConstraintDtoList = wbsConstraintFeignClient
                .getUserWbsConstraint(SystemContext.getUserId());

        Project project = projectMapper.selectByPrimaryKey(projectId);

        // 项目相关的wbs约束
        Map<String, WbsConstraintDto> ruleWbsConstraintMap = wbsConstraintDtoList.stream()
                .filter(e -> e.getTemplateId().equals(project.getWbsTemplateInfoId()))
                .collect(Collectors.toMap(e -> String.valueOf(e.getRuleId()), e -> e));

        // 查询项目wbs预算
        ProjectWbsBudgetExample projectWbsBudgetExample = new ProjectWbsBudgetExample();
        projectWbsBudgetExample.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andProjectIdEqualTo(projectId);
        List<ProjectWbsBudget> projectWbsBudgetList =
                projectWbsBudgetMapper.selectByExample(projectWbsBudgetExample);

        // 过滤wbsFullCode相同的wbs预算
        Set<String> unionWbs = new HashSet<>();
        List<ProjectWbsBudgetDto> projectWbsBudgetDtoList = new ArrayList<>(projectWbsBudgetList.size());
        for (ProjectWbsBudget pwb : projectWbsBudgetList) {
            if (pwb.getDynamicWbsTemplateRuleIds() == null || pwb.getWbsFullCode() == null) {
                continue;
            }
            if (!unionWbs.contains(pwb.getWbsFullCode())) {
                unionWbs.add(pwb.getWbsFullCode());
                ProjectWbsBudgetDto projectWbs = new ProjectWbsBudgetDto();
                projectWbs.setProjectId(pwb.getProjectId());
                projectWbs.setWbsFullCode(pwb.getWbsFullCode());
                projectWbs.setDynamicWbsTemplateRuleIds(pwb.getDynamicWbsTemplateRuleIds());
                projectWbsBudgetDtoList.add(projectWbs);
            }
        }

        // 根据配置的wbs约束将对wbs预算进行过滤
        projectWbsBudgetDtoList = projectWbsBudgetDtoList.stream().filter(e -> {
            String[] ruleIds = e.getDynamicWbsTemplateRuleIds().split(",");
            String[] ruleCodes = e.getWbsFullCode().split("-");
            for (int i = 0; i < ruleIds.length; i++) {
                WbsConstraintDto wbsConstraintDto = ruleWbsConstraintMap.get(ruleIds[i]);
                if (wbsConstraintDto != null) {
                    if (!wbsConstraintDto.getConstraints().contains(ruleCodes[i])) {
                        return false;
                    }
                }
            }
            return true;
        }).collect(Collectors.toList());

        return projectWbsBudgetDtoList;
    }

    @Override
    public List<ProjectWbsBudgetDto> findByProjectCodeAndWbsFullCode(List<String> joinCodes) {
        if (ListUtils.isEmpty(joinCodes)) {
            return Collections.emptyList();
        }
        return projectWbsBudgetExtMapper.selectByProjectCodeAndWbsFullCode(joinCodes);
    }

    /**
     * 保存项目基线批次
     *
     * @param projectId            项目id
     * @param wbsBaselineBatchList wbs基线批次
     * @return
     */
    @Override
    public List<ProjectBaselineBatch> saveProjectBaselineBatch(Long projectId, List<ProjectBaselineBatchDto> wbsBaselineBatchList) {
        List<ProjectBaselineBatch> result = new ArrayList<>();

        // 从数据库查出来，前端有可能传一个id为空的批次号就会重复生成
        ProjectBaselineBatchExample example = new ProjectBaselineBatchExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(projectId);
        List<ProjectBaselineBatch> dbBatchList = projectBaselineBatchMapper.selectByExample(example);

        for (ProjectBaselineBatchDto webBatch : wbsBaselineBatchList) {
            // 与数据库记录查重
            List<ProjectBaselineBatch> filterDbBatchs = dbBatchList.stream().filter(dbBatch -> {
                if (null != webBatch.getId() && webBatch.getId().equals(dbBatch.getId())) {
                    return true;
                }
                if (dbBatch.getCode().equals(webBatch.getCode())) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            ProjectBaselineBatch saveBatch = new ProjectBaselineBatch();
            ;
            if (!CollectionUtils.isEmpty(filterDbBatchs)) {
                saveBatch = filterDbBatchs.get(0);
            }
            saveBatch.setProjectId(projectId);
            saveBatch.setCode(webBatch.getCode());
            saveBatch.setRemark(webBatch.getRemark());
            // 修改（没有修改场景，已经在保存入口清理过历史数据）
            if (null != saveBatch.getId()) {
                saveBatch.setDeletedFlag(Boolean.TRUE.equals(webBatch.getDeletedFlag()));
                projectBaselineBatchMapper.updateByPrimaryKey(saveBatch);
            }
            // 新增（
            else if (!Boolean.TRUE.equals(saveBatch.getDeletedFlag())) {
                saveBatch.setId(null);
                saveBatch.setDeletedFlag(false);
                saveBatch.setVersion(1L);
                projectBaselineBatchMapper.insert(saveBatch);
            }
            result.add(saveBatch);
        }
        return result;
    }

    /**
     * 保存wbs预算 + wbs预算动态列
     *
     * @param projectInfo
     * @param budgetMaps              wbs预算
     * @param wbsDynamicFieldsDtoList 动态列
     * @param unitId                  组织id
     * @return 未删除的wbs预算
     */
    private List<ProjectWbsBudget> saveProjectWbsBudget(ProjectDto projectInfo, List<Map<String, Object>> budgetMaps, List<WbsDynamicFieldsDto> wbsDynamicFieldsDtoList, Long unitId) {
        List<ProjectWbsBudget> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(budgetMaps)) {
            return result;
        }
        for (Map<String, Object> wbsBudgetMap : budgetMaps) {
            ProjectWbsBudget projectWbsBudget = new ProjectWbsBudget();
            // 没有更新场景，已经在保存入口清理过历史数据）
            if (BigDecimalUtils.isBigDecimal(MapUtils.getString(wbsBudgetMap, WbsBudgetFieldConstant.ID))) {
                projectWbsBudget = projectWbsBudgetMapper.selectByPrimaryKey(MapUtils.getLong(wbsBudgetMap, WbsBudgetFieldConstant.ID));
            }
            if (null == projectWbsBudget) {
                projectWbsBudget = new ProjectWbsBudget();
            }

            /* 预算金额 */
            projectWbsBudget.setPrice(BigDecimal.valueOf(MapUtils.getDouble(wbsBudgetMap, WbsBudgetFieldConstant.PRICE)));
            /* 需求预算 */
            BigDecimal demandCost = BigDecimal.ZERO;
            if (BigDecimalUtils.isBigDecimal(MapUtils.getString(wbsBudgetMap, WbsBudgetFieldConstant.DEMAND_COST))) {
                demandCost = new BigDecimal(MapUtils.getString(wbsBudgetMap, WbsBudgetFieldConstant.DEMAND_COST));
            }
            /* 在途成本 */
            BigDecimal onTheWayCost = BigDecimal.ZERO;
            if (BigDecimalUtils.isBigDecimal(MapUtils.getString(wbsBudgetMap, WbsBudgetFieldConstant.ON_THE_WAY_COST))) {
                onTheWayCost = new BigDecimal(MapUtils.getString(wbsBudgetMap, WbsBudgetFieldConstant.ON_THE_WAY_COST));
            }
            /* 已发生成本 */
            BigDecimal incurredCost = BigDecimal.ZERO;
            if (BigDecimalUtils.isBigDecimal(MapUtils.getString(wbsBudgetMap, WbsBudgetFieldConstant.INCURRED_COST))) {
                incurredCost = new BigDecimal(MapUtils.getString(wbsBudgetMap, WbsBudgetFieldConstant.INCURRED_COST));
            }
            // 剩余可用预算 = 预算金额 - 需求预算 - 在途成本 - 已发生成本
            /* 剩余可用预算 */
            projectWbsBudget.setRemainingCost(projectWbsBudget.getPrice().subtract(demandCost).subtract(onTheWayCost).subtract(incurredCost));

            /* 修改 */ // 没有修改场景，已经在保存入口清理过历史数据）
            if (null != projectWbsBudget.getId()) {
                projectWbsBudget.setDeletedFlag(Boolean.TRUE.equals(MapUtils.getBoolean(wbsBudgetMap, WbsBudgetFieldConstant.DELETED_FLAG)));
                projectWbsBudget.setId(MapUtils.getLong(wbsBudgetMap, WbsBudgetFieldConstant.ID));
                projectWbsBudgetMapper.updateByPrimaryKey(projectWbsBudget);
            }
            /* 新增 */
            else if (!Boolean.TRUE.equals(projectWbsBudget.getDeletedFlag())) {
                // activity缓存
                ProjectActivityCache projectActivityCache = ProjectActivityCacheUtils.getCache(unitId, MapUtils.getString(wbsBudgetMap, WbsBudgetFieldConstant.ACTIVITY_CODE));
                if (null == projectActivityCache) {
                    throw new ApplicationBizException(String.format("组织%s活动事项编码%s不存在", unitId, MapUtils.getString(wbsBudgetMap, WbsBudgetFieldConstant.ACTIVITY_CODE)));
                }
                StringBuilder wbsFullCode = new StringBuilder();
                StringBuilder wbsLastCode = new StringBuilder();
                StringBuilder dynamicWbsTemplateRuleIds = new StringBuilder();
                StringBuilder dynamicFields = new StringBuilder();
                StringBuilder dynamicValues = new StringBuilder();
                // 动态列获取wbs
                for (int i = 0; i < wbsDynamicFieldsDtoList.size(); i++) {
                    WbsDynamicFieldsDto dynamicFieldDto = wbsDynamicFieldsDtoList.get(i);
                    // wbs编码
                    String wbsCode = MapUtils.getString(wbsBudgetMap, dynamicFieldDto.getKey());
                    if (StringUtils.isNotEmpty(wbsCode)) {
                        wbsCode = wbsCode.trim();
                    }
                    // 严控，wbs编码必须系统存在
                    if (Boolean.TRUE.equals(dynamicFieldDto.getStrictlyControl())) {
                        WbsTemplateRuleDetailCache wbsTemplateRuleDetailCache = WbsTemplateRuleDetailCacheUtils.getCache(dynamicFieldDto.getWbsTemplateRuleId(), wbsCode);
                        if (null == wbsTemplateRuleDetailCache) {
                            throw new ApplicationBizException(String.format("wbs规则%s对应的wbs编码%s不存在", dynamicFieldDto.getLable(), wbsCode));
                        }
                    }
                    if (i == 0) {
                        wbsFullCode.append(wbsCode);
                        dynamicValues.append(wbsCode);
                        dynamicWbsTemplateRuleIds.append(dynamicFieldDto.getWbsTemplateRuleId());
                        dynamicFields.append(dynamicFieldDto.getKey());
                    } else {
                        wbsFullCode.append("-" + wbsCode);
                        dynamicValues.append("," + wbsCode);
                        dynamicWbsTemplateRuleIds.append("," + dynamicFieldDto.getWbsTemplateRuleId());
                        dynamicFields.append("," + dynamicFieldDto.getKey());
                    }

                    if (i == wbsDynamicFieldsDtoList.size() - 1) {
                        wbsLastCode.append(wbsCode);
                    }
                }
                projectWbsBudget.setActivityName(projectActivityCache.getName());
                projectWbsBudget.setActivityCode(projectActivityCache.getCode());
                projectWbsBudget.setActivityOrderNo(projectActivityCache.getOrderNo());
                projectWbsBudget.setActivityType(projectActivityCache.getType());
                projectWbsBudget.setDescription(MapUtils.getString(wbsBudgetMap, WbsBudgetFieldConstant.DESCRIPTION));
                projectWbsBudget.setProjectId(projectInfo.getId());
                projectWbsBudget.setProjectCode(projectInfo.getCode());
                projectWbsBudget.setDynamicWbsTemplateRuleIds(dynamicWbsTemplateRuleIds.toString());
                projectWbsBudget.setDynamicFields(dynamicFields.toString());
                projectWbsBudget.setDynamicValues(dynamicValues.toString());
                projectWbsBudget.setWbsFullCode(wbsFullCode.toString());
                if (StringUtils.isNotEmpty(projectWbsBudget.getProjectCode()) && StringUtils.isNotEmpty(projectWbsBudget.getWbsFullCode())) {
                    projectWbsBudget.setWbsSummaryCode(String.format("%s-%s", projectWbsBudget.getProjectCode(), projectWbsBudget.getWbsFullCode()));
                }
                projectWbsBudget.setWbsLastCode(wbsLastCode.toString());
                projectWbsBudget.setId(null);
                projectWbsBudget.setDeletedFlag(false);
                projectWbsBudget.setVersion(1L);
                projectWbsBudget.setBaselineCost(BigDecimal.ZERO);
                projectWbsBudget.setDemandCost(demandCost);
                projectWbsBudget.setOnTheWayCost(onTheWayCost);
                projectWbsBudget.setIncurredCost(incurredCost);
                projectWbsBudget.setChangeAccumulateCost(BigDecimal.ZERO);
                projectWbsBudgetMapper.insert(projectWbsBudget);
            }

            // 3.保存项目wbs预算动态列
            saveProjectWbsBudgetDynamic(projectInfo.getId(), wbsBudgetMap, projectWbsBudget, wbsDynamicFieldsDtoList);
            if (!Boolean.TRUE.equals(projectWbsBudget.getDeletedFlag())) {
                result.add(projectWbsBudget);
            }
        }
        return result;
    }

    /**
     * 设置经济事项
     *
     * @param feeItemExpenseTypeDtoList
     * @param wbsBudget
     * @param wbsDynamicFieldList       wbs动态列
     */
    @Override
    public void setFeeExpenseType(List<FeeItemExpenseTypeDto> feeItemExpenseTypeDtoList, Map wbsBudget, List<WbsDynamicFieldsDto> wbsDynamicFieldList) {
        String wbsLastCode = ProjectWbsBudgetUtils.getWbsLastCode(wbsBudget, wbsDynamicFieldList);
        if (CollectionUtils.isNotEmpty(feeItemExpenseTypeDtoList) && StringUtils.isBlank(MapUtils.getString(wbsBudget, WbsBudgetFieldConstant.FEE_TYPE_ID)) && StringUtils.isNotBlank(wbsLastCode)) {
            // wbs经济事项
            List<FeeItemExpenseTypeDto> wbsFeeItems = feeItemExpenseTypeDtoList.stream().filter(a -> Objects.equals(a.getName(), wbsLastCode) && Objects.equals(a.getFeeSettingMode(), FeeSettingModeEnum.WBS.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(wbsFeeItems)) {
                FeeItemExpenseTypeDto wbsFeeItem = wbsFeeItems.get(0);
                wbsBudget.put(WbsBudgetFieldConstant.FEE_TYPE_ID, wbsFeeItem.getFeeTypeId());
                wbsBudget.put(WbsBudgetFieldConstant.FEE_TYPE_NAME, wbsFeeItem.getFeeTypeName());
                wbsBudget.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, Boolean.TRUE.equals(wbsFeeItem.getSyncEMS()));
                return;
            }
            // activity经济事项
            List<FeeItemExpenseTypeDto> activityFeeItems = feeItemExpenseTypeDtoList.stream().filter(a -> Objects.equals(a.getName(), MapUtils.getString(wbsBudget, WbsBudgetFieldConstant.ACTIVITY_CODE)) && Objects.equals(a.getFeeSettingMode(), FeeSettingModeEnum.PROJECT_ACTIVITY.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(activityFeeItems)) {
                FeeItemExpenseTypeDto activityFeeItem = activityFeeItems.get(0);
                wbsBudget.put(WbsBudgetFieldConstant.FEE_TYPE_ID, activityFeeItem.getFeeTypeId());
                wbsBudget.put(WbsBudgetFieldConstant.FEE_TYPE_NAME, activityFeeItem.getFeeTypeName());
                wbsBudget.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, Boolean.TRUE.equals(activityFeeItem.getSyncEMS()));
                return;
            }
        }
    }

    /**
     * 保存wbs预算动态列
     *
     * @param projectId
     * @param budgetMap
     * @param projectWbsBudget
     * @param wbsDynamicFieldsDtoList
     */
    private void saveProjectWbsBudgetDynamic(Long projectId, Map<String, Object> budgetMap, ProjectWbsBudget projectWbsBudget, List<WbsDynamicFieldsDto> wbsDynamicFieldsDtoList) {
        ProjectWbsBudgetDynamicExample example = new ProjectWbsBudgetDynamicExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andProjectWbsBudgetIdEqualTo(projectWbsBudget.getId());
        // 如果已经生成过动态列，则不再重复生成
        if (projectWbsBudgetDynamicMapper.countByExample(example) > 0) {
            return;
        }

        for (WbsDynamicFieldsDto wbsDynamicFieldsDto : wbsDynamicFieldsDtoList) {
            String wbsRuleDetailCode = MapUtils.getString(budgetMap, wbsDynamicFieldsDto.getKey());
            // 严控，wbs编码必须系统存在
            if (Boolean.TRUE.equals(wbsDynamicFieldsDto.getStrictlyControl())) {
                WbsTemplateRuleDetailCache wbsTemplateRuleDetailCache = WbsTemplateRuleDetailCacheUtils.getCache(wbsDynamicFieldsDto.getWbsTemplateRuleId(), wbsRuleDetailCode);
                if (null == wbsTemplateRuleDetailCache) {
                    throw new ApplicationBizException(String.format("wbs规则%s对应的wbs编码%s不存在", wbsDynamicFieldsDto.getLable(), wbsRuleDetailCode));
                }
            }

            ProjectWbsBudgetDynamic projectWbsBudgetDynamic = new ProjectWbsBudgetDynamic();
            projectWbsBudgetDynamic.setProjectId(projectId);
            projectWbsBudgetDynamic.setProjectWbsBudgetId(projectWbsBudget.getId());
            projectWbsBudgetDynamic.setFieldName(wbsDynamicFieldsDto.getKey());
            //wbs规则id
            projectWbsBudgetDynamic.setWbsTemplateRuleId(wbsDynamicFieldsDto.getWbsTemplateRuleId());
            //wbs模板规则详情编码
            projectWbsBudgetDynamic.setWbsTemplateRuleDetailCode(wbsRuleDetailCode);
            projectWbsBudgetDynamic.setDeletedFlag(false);
            projectWbsBudgetDynamic.setVersion(1L);
            projectWbsBudgetDynamicMapper.insertSelective(projectWbsBudgetDynamic);
        }
    }

    /**
     * 保存wbs预算基线
     *
     * @param projectInfo              项目
     * @param projectWbsBudgetList     wbs预算
     * @param projectBaselineBatchList wbs预算基线批次
     * @param wbsBudgetBaselineList    wbs预算基线
     * @param wbsDynamicFieldsDtoList  wbs动态列
     */
    private void saveProjectWbsBudgetBaseline(ProjectDto projectInfo, List<ProjectWbsBudget> projectWbsBudgetList, List<ProjectBaselineBatch> projectBaselineBatchList, List<Map<String, Object>> wbsBudgetBaselineList, List<WbsDynamicFieldsDto> wbsDynamicFieldsDtoList) {

        ProjectWbsBudget projectWbsBudget;
        // 未删除的预算基线集合
        List<ProjectWbsBudgetBaseline> projectWbsBudgetBaselineList = new ArrayList<>();
        for (Map<String, Object> baseLineMap : wbsBudgetBaselineList) {
            projectWbsBudget = null;
            /* 匹配wbs与activity相同的wbs预算 */
            List<ProjectWbsBudget> filterWbsBudgets = projectWbsBudgetList.stream().filter(a ->
                    ProjectWbsBudgetDto.entityEqulasWbsActivityByMap(a, baseLineMap, wbsDynamicFieldsDtoList)
            ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterWbsBudgets)) {
                projectWbsBudget = filterWbsBudgets.get(0);
            }

            ProjectWbsBudgetBaseline projectWbsBudgetBaseline = new ProjectWbsBudgetBaseline();
            // 没有更新场景，已经在保存入口清理过历史数据）
            if (BigDecimalUtils.isBigDecimal(MapUtils.getString(baseLineMap, WbsBudgetFieldConstant.ID))) {
                projectWbsBudgetBaseline = projectWbsBudgetBaselineMapper.selectByPrimaryKey(MapUtils.getLong(baseLineMap, WbsBudgetFieldConstant.ID));
            }
            if (null == projectWbsBudgetBaseline) {
                projectWbsBudgetBaseline = new ProjectWbsBudgetBaseline();
            }

            // 预算基线
            projectWbsBudgetBaseline.setBaselineCost(new BigDecimal(MapUtils.getString(baseLineMap, WbsBudgetFieldConstant.BASELINE_COST)));

            /* 修改 */ // 没有更新场景，已经在保存入口清理过历史数据）
            if (null != projectWbsBudgetBaseline.getId()) {
                /* 能匹配到wbs预算 */
                if (null != projectWbsBudget) {
                    // 预算描述同步到基线描述
                    projectWbsBudgetBaseline.setDescription(projectWbsBudget.getDescription());
                    projectWbsBudgetBaseline.setProjectWbsBudgetId(projectWbsBudget.getId());
                } else {
                    projectWbsBudgetBaseline.setProjectWbsBudgetId(null);
                }
                projectWbsBudgetBaseline.setDeletedFlag(Boolean.TRUE.equals(MapUtils.getBoolean(baseLineMap, WbsBudgetFieldConstant.DELETED_FLAG)));
                projectWbsBudgetBaselineMapper.updateByPrimaryKey(projectWbsBudgetBaseline);
            }
            /* 新增 */
            else {
                // 项目id
                projectWbsBudgetBaseline.setProjectId(projectInfo.getId());
                // 项目编码
                projectWbsBudgetBaseline.setProjectCode(projectInfo.getCode());
                // 描述
                projectWbsBudgetBaseline.setDescription(MapUtils.getString(baseLineMap, WbsBudgetFieldConstant.DESCRIPTION));
                // 活动事项编码
                projectWbsBudgetBaseline.setActivityCode(MapUtils.getString(baseLineMap, WbsBudgetFieldConstant.ACTIVITY_CODE));
                // 活动类别名称
                projectWbsBudgetBaseline.setActivityName(MapUtils.getString(baseLineMap, WbsBudgetFieldConstant.ACTIVITY_NAME));
                // 活动事项序号
                projectWbsBudgetBaseline.setActivityOrderNo(MapUtils.getString(baseLineMap, WbsBudgetFieldConstant.ACTIVITY_ORDER_NO));
                // 活动类别属性
                projectWbsBudgetBaseline.setActivityType(MapUtils.getString(baseLineMap, WbsBudgetFieldConstant.ACTIVITY_TYPE));
                // 删除状态
                projectWbsBudgetBaseline.setDeletedFlag(false);
                // 版本号
                projectWbsBudgetBaseline.setVersion(1L);
                /* 基线批次 */
                if (!CollectionUtils.isEmpty(projectBaselineBatchList)) {
                    for (ProjectBaselineBatch projectBaselineBatch : projectBaselineBatchList) {
                        if (projectBaselineBatch.getCode().equals(MapUtils.getString(baseLineMap, WbsBudgetFieldConstant.PROJECT_BASELINE_BATCH_CODE))) {
                            // 项目基线批次id
                            projectWbsBudgetBaseline.setProjectBaselineBatchId(projectBaselineBatch.getId());
                            // 项目基线批次编码
                            projectWbsBudgetBaseline.setProjectBaselineBatchCode(projectBaselineBatch.getCode());
                            break;
                        }
                    }
                }
                /* wbs动态列（能匹配到wbs预算） */
                if (null != projectWbsBudget) {
                    // wbs预算编制id
                    projectWbsBudgetBaseline.setProjectWbsBudgetId(projectWbsBudget.getId());
                    // 预算描述同步到基线描述
                    projectWbsBudgetBaseline.setDescription(projectWbsBudget.getDescription());
                    projectWbsBudgetBaseline.setWbsFullCode(projectWbsBudget.getWbsFullCode());
                    projectWbsBudgetBaseline.setWbsLastCode(projectWbsBudget.getWbsLastCode());
                    projectWbsBudgetBaseline.setDynamicFields(projectWbsBudget.getDynamicFields());
                    projectWbsBudgetBaseline.setDynamicValues(projectWbsBudget.getDynamicValues());
                    projectWbsBudgetBaseline.setDynamicWbsTemplateRuleIds(projectWbsBudget.getDynamicWbsTemplateRuleIds());
                }
                /* wbs动态列（匹配不到wbs预算） */
                else {
                    StringBuilder wbsFullCode = new StringBuilder();
                    StringBuilder wbsLastCode = new StringBuilder();
                    StringBuilder dynamicWbsTemplateRuleIds = new StringBuilder();
                    StringBuilder dynamicFields = new StringBuilder();
                    StringBuilder dynamicValues = new StringBuilder();
                    // 动态列获取wbs
                    for (int i = 0; i < wbsDynamicFieldsDtoList.size(); i++) {
                        WbsDynamicFieldsDto dynamicFieldDto = wbsDynamicFieldsDtoList.get(i);
                        // wbs编码
                        String wbsCode = MapUtils.getString(baseLineMap, dynamicFieldDto.getKey());
                        // 严控，wbs编码必须系统存在
                        if (Boolean.TRUE.equals(dynamicFieldDto.getStrictlyControl())) {
                            WbsTemplateRuleDetailCache wbsTemplateRuleDetailCache = WbsTemplateRuleDetailCacheUtils.getCache(dynamicFieldDto.getWbsTemplateRuleId(), wbsCode);
                            if (null == wbsTemplateRuleDetailCache) {
                                throw new ApplicationBizException(String.format("wbs规则%s对应的wbs编码%s不存在", dynamicFieldDto.getLable(), wbsCode));
                            }
                        }
                        if (i == 0) {
                            wbsFullCode.append(wbsCode);
                            dynamicValues.append(wbsCode);
                            dynamicWbsTemplateRuleIds.append(dynamicFieldDto.getWbsTemplateRuleId());
                            dynamicFields.append(dynamicFieldDto.getKey());
                        } else {
                            wbsFullCode.append("-" + wbsCode);
                            dynamicValues.append("," + wbsCode);
                            dynamicWbsTemplateRuleIds.append("," + dynamicFieldDto.getWbsTemplateRuleId());
                            dynamicFields.append("," + dynamicFieldDto.getKey());
                        }

                        if (i == wbsDynamicFieldsDtoList.size() - 1) {
                            wbsLastCode.append(wbsCode);
                        }
                    }
                    projectWbsBudgetBaseline.setWbsFullCode(wbsFullCode.toString());
                    projectWbsBudgetBaseline.setWbsLastCode(wbsLastCode.toString());
                    projectWbsBudgetBaseline.setDynamicFields(dynamicFields.toString());
                    projectWbsBudgetBaseline.setDynamicValues(dynamicValues.toString());
                    projectWbsBudgetBaseline.setDynamicWbsTemplateRuleIds(dynamicWbsTemplateRuleIds.toString());
                }
                projectWbsBudgetBaselineMapper.insert(projectWbsBudgetBaseline);
            }
            if (!Boolean.TRUE.equals(projectWbsBudgetBaseline.getDeletedFlag())) {
                projectWbsBudgetBaselineList.add(projectWbsBudgetBaseline);
            }
        }
        // 汇总预算基线 和 累计变更金额 （ = 预算金额 - 预算基线 ）
        sumWbsBaseline2wbsBudget(projectWbsBudgetList, projectWbsBudgetBaselineList, wbsDynamicFieldsDtoList);
    }

    /**
     * 汇总预算基线 和 累计变更金额 （ = 预算金额 - 预算基线 ）
     *
     * @param projectWbsBudgetList         未删除wbs预算
     * @param projectWbsBudgetBaselineList 未删除wbs预算基线
     */
    private void sumWbsBaseline2wbsBudget(List<ProjectWbsBudget> projectWbsBudgetList, List<ProjectWbsBudgetBaseline> projectWbsBudgetBaselineList, List<WbsDynamicFieldsDto> wbsDynamicFieldsDtoList) {
        if (CollectionUtils.isEmpty(projectWbsBudgetList) || CollectionUtils.isEmpty(projectWbsBudgetBaselineList)) {
            return;
        }

        Map<String, List<ProjectWbsBudget>> wbsBudgetGroupMap = projectWbsBudgetList.stream()
                .collect(Collectors.groupingBy(a -> a.getWbsFullCode() + ProjectWbsBudgetUtils.DEFAULT_SPLIT + a.getActivityCode()));


        Map<String, List<ProjectWbsBudgetBaseline>> wbsBUdgetBaselineGroupMap = projectWbsBudgetBaselineList.stream()
                .collect(Collectors.groupingBy(a -> a.getWbsFullCode() + ProjectWbsBudgetUtils.DEFAULT_SPLIT + a.getActivityCode()));

        BigDecimal sumBaselineCost;
        for (String key : wbsBUdgetBaselineGroupMap.keySet()) {
            if (wbsBudgetGroupMap.containsKey(key)) {
                ProjectWbsBudget wbsBudget = wbsBudgetGroupMap.get(key).get(0);
                List<ProjectWbsBudgetBaseline> baselines = wbsBUdgetBaselineGroupMap.get(key);
                sumBaselineCost = BigDecimal.ZERO;
                for (ProjectWbsBudgetBaseline baseline : baselines) {
                    sumBaselineCost = sumBaselineCost.add(baseline.getBaselineCost());
                }
                /* 更新wbs预算记录 预算基线&累计变更金额 */
                wbsBudget.setBaselineCost(sumBaselineCost);
                wbsBudget.setChangeAccumulateCost(wbsBudget.getPrice().subtract(sumBaselineCost));
                projectWbsBudgetMapper.updateByPrimaryKeySelective(wbsBudget);
            }
        }
    }

    @Override
    public List<String> findWbsByProjectId(Long projectId) {
        ProjectWbsBudgetExample projectWbsBudgetExample = new ProjectWbsBudgetExample();
        projectWbsBudgetExample.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andProjectIdEqualTo(projectId);
        List<ProjectWbsBudget> projectWbsBudgetList =
                projectWbsBudgetMapper.selectByExample(projectWbsBudgetExample);
        return projectWbsBudgetList.stream().map(e -> e.getProjectCode() + "-" + e.getWbsFullCode())
                .distinct().collect(Collectors.toList());
    }
}
