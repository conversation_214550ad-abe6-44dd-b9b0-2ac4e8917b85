package com.midea.pam.ctc.project.service.impl;

import com.midea.pam.common.ctc.dto.WorkflowCallbackCommonDto;
import com.midea.pam.common.ctc.entity.*;
import com.midea.pam.common.enums.*;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.ContractService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.*;
import com.midea.pam.ctc.project.service.ProjectTerminationWorkflowCallbackService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectContractRsService;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: common-module
 * @description: 项目终止流程回调业务逻辑
 * @author:zhongpeng
 * @create:2020-06-05 08:59
 **/
public class ProjectTerminationWorkflowCallbackServiceImpl extends ProjectTerminationWorkflowCallbackService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private WorkflowCallbackService workflowCallbackService;

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private ProjectTerminationMapper projectTerminationMapper;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private ProjectMilepostMapper projectMilepostMapper;

    @Resource
    private ProjectContractRsMapper projectContractRsMapper;

    @Resource
    private ContractMapper contractMapper;

    @Resource
    private TicketTasksExtMapper ticketTasksExtMapper;

    @Resource
    private ProjectIncomeCostPlanMapper projectIncomeCostPlanMapper;

    @Resource
    private ProjectProfitMapper projectProfitMapper;

    @Resource
    private OrganizationCustomDictService organizationCustomDictService;

    @Resource
    private ProjectTypeMapper projectTypeMapper;

    @Resource
    private ProjectContractRsService projectContractRsService;

    @Resource
    private ContractService contractService;

    @Resource
    private InvoicePlanDetailMapper invoicePlanDetailMapper;

    @Resource
    private InvoiceApplyDetailsMapper invoiceApplyDetailsMapper;

    @Resource
    private InvoiceReceivableMapper invoiceReceivableMapper;

    @Resource
    private ProjectBusinessService projectBusinessService;

    @Resource
    private ProjectTerminationTypeMapper projectTerminationTypeMapper;

    @Resource
    private ProjectResourceRelMapper projectResourceRelMapper;


    @Resource
    private WorkingHourMapper workingHourMapper;

    /**
     * 项目终止流程提交
     *
     * @param workflowCallbackCommonDto
     */
    @Override
    public void draftSubmit(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("项目终止提交审批回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("projectTerminiationAppWorkflowCallBack_draftSubmitCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }
                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    if (workflowCallbackCommonDto.getFormInstanceId() == null) {
                        logger.error("项目终止流程审批提交审批回调 formInstanceId为空，不处理");
                        return;
                    }

                    ProjectTerminationExample projectTerminationExample = new ProjectTerminationExample();
                    projectTerminationExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(formInstanceId);

                    final List<ProjectTermination> projectTerminationList = projectTerminationMapper.selectByExample(projectTerminationExample);
                    if (!CollectionUtils.isNotEmpty(projectTerminationList)) {
                        logger.error("项目终止流程审批回调 formInstanceId对应项目终止不存在，不处理");
                        return;
                    }
                    ProjectTermination projectTermination = projectTerminationList.get(0);
                    //更新项目项目状态
                    Project project = projectMapper.selectByPrimaryKey(projectTermination.getProjectId());
                    project.setStatus(ProjectStatus.TERMINATIONING.getCode());
                    projectMapper.updateByPrimaryKey(project);

                    projectTermination.setStatus(ProjectTerminationEnums.SUBMIT.getCode());
                    projectTerminationMapper.updateByPrimaryKey(projectTermination);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目终止提交审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目终止提交审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }
            }
        } catch (Exception e) {
            logger.error("项目终止提交审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    /**
     * 项目终止流程驳回
     *
     * @param workflowCallbackCommonDto
     */
    @Override
    public void refuse(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("项目终止驳回审批回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("projectTerminiationAppWorkflowCallBack_refuseCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    if (workflowCallbackCommonDto.getFormInstanceId() == null) {
                        logger.error("项目终止流程审批驳回审批回调 formInstanceId为空，不处理");
                        return;
                    }

                    ProjectTerminationExample projectTerminationExample = new ProjectTerminationExample();
                    projectTerminationExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(formInstanceId);

                    final List<ProjectTermination> projectTerminationList = projectTerminationMapper.selectByExample(projectTerminationExample);
                    if (!CollectionUtils.isNotEmpty(projectTerminationList)) {
                        logger.error("项目终止流程审批驳回审批回调 formInstanceId对应项目终止不存在，不处理");
                        return;
                    }
                    ProjectTermination projectTermination = projectTerminationList.get(0);
                    //更新项目项目状态
                    Project project = projectMapper.selectByPrimaryKey(projectTermination.getProjectId());
                    project.setStatus(ProjectStatus.APPROVALED.getCode());
                    projectMapper.updateByPrimaryKey(project);

                    projectTermination.setStatus(3); //驳回
                    projectTerminationMapper.updateByPrimaryKey(projectTermination);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目终止驳回审批回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目终止驳回审批回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }
            }
        } catch (Exception e) {
            logger.error("项目终止提交审批回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    /**
     * 项目终止流程撤回
     *
     * @param workflowCallbackCommonDto
     */
    @Override
    public void draftReturn(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("项目终止审批撤回回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("projectTerminiationAppWorkflowCallBack_draftReturnCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    if (workflowCallbackCommonDto.getFormInstanceId() == null) {
                        logger.error("项目终止流程审批撤回审批回调 formInstanceId为空，不处理");
                        return;
                    }

                    ProjectTerminationExample projectTerminationExample = new ProjectTerminationExample();
                    projectTerminationExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(formInstanceId);

                    final List<ProjectTermination> projectTerminationList = projectTerminationMapper.selectByExample(projectTerminationExample);
                    if (!CollectionUtils.isNotEmpty(projectTerminationList)) {
                        logger.error("项目终止流程审批撤回审批回调 formInstanceId对应项目终止不存在，不处理");
                        return;
                    }
                    ProjectTermination projectTermination = projectTerminationList.get(0);
                    //更新项目项目状态
                    Project project = projectMapper.selectByPrimaryKey(projectTermination.getProjectId());
                    project.setStatus(ProjectStatus.APPROVALED.getCode());
                    projectMapper.updateByPrimaryKey(project);

                    projectTermination.setStatus(4); //驳回
                    projectTerminationMapper.updateByPrimaryKey(projectTermination);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目终止审批撤回回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目终止审批撤回回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目终止审批撤回回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    /**
     * 项目终止流程审批通过
     *
     * @param workflowCallbackCommonDto
     */
    @Override
    public void pass(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("项目终止审批通过回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("projectTerminiationAppWorkflowCallBack_passCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    if (workflowCallbackCommonDto.getFormInstanceId() == null) {
                        logger.error("项目终止流程审批撤回审批回调 formInstanceId为空，不处理");
                        return;
                    }

                    ProjectTerminationExample projectTerminationExample = new ProjectTerminationExample();
                    projectTerminationExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(formInstanceId);

                    final List<ProjectTermination> projectTerminationList = projectTerminationMapper.selectByExample(projectTerminationExample);
                    if (!CollectionUtils.isNotEmpty(projectTerminationList)) {
                        logger.error("项目终止流程审批撤回审批回调 formInstanceId对应项目终止不存在，不处理");
                        return;
                    }
                    ProjectTermination projectTermination = projectTerminationList.get(0);
                    //更新项目项目状态
                    Project project = projectMapper.selectByPrimaryKey(projectTermination.getProjectId());
                    final Long terminationTypeId = projectTermination.getTerminationTypeId();
                    final ProjectTerminationType projectTerminationType = projectTerminationTypeMapper.selectByPrimaryKey(terminationTypeId);
                    if (projectTerminationType != null && projectTerminationType.getTerminationName().equals("项目废弃")) {  //项目作废
                        project.setStatus(ProjectStatus.INVALID.getCode());
                        project.setContractId(null);
                        projectMapper.updateByPrimaryKey(project);
                        ProjectContractRsExample projectContractRsExample = new ProjectContractRsExample();
                        projectContractRsExample.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                        final List<ProjectContractRs> projectContractRsList = projectContractRsMapper.selectByExample(projectContractRsExample);
                        //失效关联的合同
                        if (CollectionUtils.isNotEmpty(projectContractRsList)) {
                            // 批量失效项目合同关系
                            projectContractRsList.forEach(item -> {
                                ProjectContractRs projectContractRs = new ProjectContractRs();
                                projectContractRs.setId(item.getId());
                                projectContractRs.setDeletedFlag(Boolean.TRUE);
                                projectContractRsMapper.updateByPrimaryKeySelective(projectContractRs);
                            });
                        }

                        //作废原先的项目资源关系表
                        ProjectResourceRelExample resourceRelExample = new ProjectResourceRelExample();
                        resourceRelExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                                .andProjectIdEqualTo(project.getId());
                        List<ProjectResourceRel> projectResourceRelList = projectResourceRelMapper.selectByExample(resourceRelExample);
                        if (ListUtils.isNotEmpty(projectResourceRelList)) {
                            for (ProjectResourceRel projectResourceRel : projectResourceRelList) {
                                projectResourceRel.setDeletedFlag(Boolean.TRUE);
                                projectResourceRelMapper.updateByPrimaryKeySelective(projectResourceRel);
                            }
                        }

                    } else {   //项目终止
                        project.setStatus(ProjectStatus.TERMINATION.getCode());
                        projectMapper.updateByPrimaryKey(project);
                        /**
                         *
                         * 流程通过后未交付的里程碑，状态变更为“终止”,此为里程碑的最终状态，此状态的里程碑不需要“交付”；
                         * 里程碑的实际结束日期都为终止流程审批通过日期；
                         */
                        ProjectMilepostExample projectMilepostExample = new ProjectMilepostExample();
                        projectMilepostExample.setOrderByClause("id asc");
                        //未交付状态就是项目里程碑状态是进行中的数据
                        projectMilepostExample.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE).andStatusEqualTo(0).andHelpFlagEqualTo(Boolean.FALSE);


                        ProjectMilepostExample projectMilepostExample1 = new ProjectMilepostExample();
                        projectMilepostExample1.setOrderByClause("id asc");
                        //未交付状态就是项目辅里程碑状态是进行中的数据
                        projectMilepostExample1.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE).andStatusEqualTo(0).andHelpFlagEqualTo(Boolean.TRUE);

                        ProjectMilepostExample projectMilepostExampled = new ProjectMilepostExample();
                        projectMilepostExampled.setOrderByClause("id asc");
                        //已交付状态的项目里程碑状态是非进行中的数据
                        projectMilepostExampled.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE).andStatusNotEqualTo(0).andHelpFlagEqualTo(Boolean.FALSE);

                        ProjectMilepostExample projectMilepostExampled1 = new ProjectMilepostExample();
                        projectMilepostExampled1.setOrderByClause("id asc");
                        //已交付状态的项目辅里程碑状态非进行中的数据
                        projectMilepostExampled1.createCriteria().andProjectIdEqualTo(project.getId()).andDeletedFlagEqualTo(Boolean.FALSE).andStatusNotEqualTo(0).andHelpFlagEqualTo(Boolean.TRUE);

                        //未交付的项目主里程碑
                        List<ProjectMilepost> mainProjectMilepostList = projectMilepostMapper.selectByExample(projectMilepostExample);

                        //已交付的项目主里程碑
                        List<ProjectMilepost> mainProjectMilepostList1 = projectMilepostMapper.selectByExample(projectMilepostExampled);

                        //未交付的项目辅里程碑
                        List<ProjectMilepost> helpProjectMilepostList = projectMilepostMapper.selectByExample(projectMilepostExample1);

                        //已交付的项目辅里程碑
                        List<ProjectMilepost> helpProjectMilepostList1 = projectMilepostMapper.selectByExample(projectMilepostExampled1);

                        // 成本方法(“成本百分比+月度”)下，只有项目状态为结项、终止时才会存在最后一个收入节点
                        projectBusinessService.addLatestIncomeMonth(project, new Date());

                        // 查询是否有月度确认收入的数据（包含已结转和未结转的项目月度收入）
                        ProjectIncomeCostPlanExample projectIncomeCostPlanExample = new ProjectIncomeCostPlanExample();
                        projectIncomeCostPlanExample.setOrderByClause("id asc");
                        projectIncomeCostPlanExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(project.getId());
                        List<ProjectIncomeCostPlan> projectIncomeCostPlanList = projectIncomeCostPlanMapper.selectByExample(projectIncomeCostPlanExample);
                        if (CollectionUtils.isNotEmpty(mainProjectMilepostList)) {
                            projectMilePostInfluence(mainProjectMilepostList, projectIncomeCostPlanList, Boolean.TRUE, mainProjectMilepostList1);
                        }
                        if (CollectionUtils.isNotEmpty(helpProjectMilepostList)) {
                            projectMilePostInfluence(helpProjectMilepostList, null, Boolean.FALSE, helpProjectMilepostList1);
                        }

                        // 查询组织参数配置【收入总额不固定类型】
                        final Set<String> dictSet = organizationCustomDictService.queryByName("收入总额不固定类型", project.getOuId(), OrgCustomDictOrgFrom.OU);
                        if (project.getType() != null && !org.springframework.util.CollectionUtils.isEmpty(dictSet)) {
                            ProjectType projectType = projectTypeMapper.selectByPrimaryKey(project.getType());
                            //项目类型在组织参数“收入总额不固定类型”中有设置
                            for (String dict : dictSet) {
                                if (projectType != null && Objects.equals(projectType.getName(), dict)) {
                                    //以实际开票总金额（不含税）更新项目金额和收入成本计划
                                    updateActualAmount(project);
                                    break;
                                }
                            }
                        }
                    }

                    // 项目终止流程审批通过，把项目下 工单任务=未发布、已发布 状态 修改为终止状态
                    ticketTasksExtMapper.terminationByProjectId(project.getId());

                    //失效未提交和驳回的工时
                    List<Integer> workingHourStatus = new ArrayList<>();
                    workingHourStatus.add(1); // 未提交
                    workingHourStatus.add(3); // 驳回
                    WorkingHourExample workingHourExample = new WorkingHourExample();
                    workingHourExample.createCriteria().andDeleteFlagEqualTo(DeletedFlagEnum.VALID.code()).andProjectIdEqualTo(project.getId()).andStatusIn(workingHourStatus);
                    final List<WorkingHour> workingHourList = workingHourMapper.selectByExample(workingHourExample);
                    if (ListUtils.isNotEmpty(workingHourList)) {
                        for (WorkingHour workingHour : workingHourList) {
                            workingHour.setDeleteFlag(DeletedFlagEnum.INVALID.code());
                            workingHourMapper.updateByPrimaryKeySelective(workingHour);
                        }
                    }
                    HandleDispatcher.syncProjectStatusToEms(project.getId(), ProjectSyncStatus.TERMINATION.getCode());// 同步项目状态给EMS
                    projectTermination.setStatus(ProjectTerminationEnums.APPROVALED.getCode()); //审批通过
                    projectTerminationMapper.updateByPrimaryKey(projectTermination);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目终止审批通过回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目终止审批通过回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目终止审批通过回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    /**
     * @param projectMilepostList       项目里程碑
     * @param projectIncomeCostPlanList 项目收入成本计划
     * @param isMainFlag                是否主辅
     */
    // 项目终止流程通过之后对项目里程碑的影响
    private final void projectMilePostInfluence(List<ProjectMilepost> projectMilepostList,
                                                List<ProjectIncomeCostPlan> projectIncomeCostPlanList,
                                                Boolean isMainFlag,
                                                List<ProjectMilepost> projectMilepostPassedList) {

        //统计非进行中的里程碑收入，成本金额
        BigDecimal incomeRatioPassed = BigDecimal.ZERO;
        BigDecimal incomePassed = BigDecimal.ZERO;
        BigDecimal costRatioPassed = BigDecimal.ZERO;
        BigDecimal costPassed = BigDecimal.ZERO;

        if (CollectionUtils.isNotEmpty(projectMilepostPassedList)) {
            for (ProjectMilepost milepost : projectMilepostPassedList) {
                BigDecimal incomeRatio = milepost.getIncomeRatio() == null ? BigDecimal.ZERO : milepost.getIncomeRatio();
                incomeRatioPassed = incomeRatioPassed.add(incomeRatio);
                BigDecimal income = milepost.getIncome() == null ? BigDecimal.ZERO : milepost.getIncome();
                incomePassed = incomePassed.add(income);
                BigDecimal costRatio = milepost.getCostRatio() == null ? BigDecimal.ZERO : milepost.getCostRatio();
                costRatioPassed = costRatioPassed.add(costRatio);
                BigDecimal cost = milepost.getCost() == null ? BigDecimal.ZERO : milepost.getCost();
                costPassed = costPassed.add(cost);
            }
        }


        //所有是收入节点的项目里程碑
        Long projectMilepostId = null;
        ProjectMilepost projectMilepostIncome = null;
        List<ProjectMilepost> projectMilepostListIncome = projectMilepostList.stream().filter(p -> p.getIncomeFlag().equals(Boolean.TRUE)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(projectMilepostListIncome)) {
            //取第一个收入节点的项目里程碑id
            projectMilepostId = projectMilepostListIncome.get(0).getId();
            projectMilepostIncome = projectMilepostListIncome.get(0);
        }
        //项目收益
        ProjectProfitExample projectProfitExample = new ProjectProfitExample();
        projectProfitExample.createCriteria().andProjectIdEqualTo(projectMilepostList.get(0).getProjectId()).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectProfit> projectProfitList = projectProfitMapper.selectByExample(projectProfitExample);
        BigDecimal income = BigDecimal.ZERO;
        BigDecimal budget = BigDecimal.ZERO;

        if (CollectionUtils.isNotEmpty(projectProfitList)) {
            if (isMainFlag) {
                income = projectProfitList.get(0).getMainIncome();
                budget = projectProfitList.get(0).getMainBudget();
            } else {
                income = projectProfitList.get(0).getHelpIncome();
                budget = projectProfitList.get(0).getHelpBudget();
            }
        }

        if (CollectionUtils.isNotEmpty(projectMilepostList)) {

            for (ProjectMilepost projectMilepost : projectMilepostList) {
                projectMilepost.setStatus(MilepostStatus.MILEPOS_TERMINAL.getCode()); //状态变更为“终止”,26为终止状态
                if (projectMilepost.getActualStartTime() == null) {
                    projectMilepost.setActualStartTime(projectMilepost.getStartTime());
                }
                projectMilepost.setActualEndTime(new Date());
                projectMilepostMapper.updateByPrimaryKey(projectMilepost);
            }
            // 对于终止状态的里程碑，只保留第一个收入节点，其他节点结转状态变更为"不需要结转",也就是全部改为非收入节点;
            for (int i = 0; i < projectMilepostList.size(); i++) {
                if (projectMilepostId != null && projectMilepostList.get(i).getId().equals(projectMilepostId)) {
                    projectMilepostIncome.setIncomeRatio(BigDecimal.valueOf(100).subtract(incomeRatioPassed));
                    projectMilepostIncome.setCostRatio(BigDecimal.valueOf(100).subtract(costRatioPassed));
                    projectMilepostIncome.setIncome(income.subtract(incomePassed));
                    projectMilepostIncome.setCost(budget.subtract(costPassed));
                    projectMilepostMapper.updateByPrimaryKey(projectMilepostIncome);
                    continue;
                }
                projectMilepostList.get(i).setIncomeFlag(Boolean.FALSE);
                projectMilepostList.get(i).setIncomeRatio(BigDecimal.ZERO);
                projectMilepostList.get(i).setCostRatio(BigDecimal.ZERO);
                projectMilepostList.get(i).setIncome(BigDecimal.ZERO);
                projectMilepostList.get(i).setCost(BigDecimal.ZERO);
                projectMilepostMapper.updateByPrimaryKey(projectMilepostList.get(i));
            }
        }

        if (CollectionUtils.isNotEmpty(projectIncomeCostPlanList)) {
            //未结转收入成本计划
            List<ProjectIncomeCostPlan> projectIncomeCostPlanList1 = projectIncomeCostPlanList.stream()
                    .filter(p -> p.getCarryoverBillId() == null).collect(Collectors.toList());
            //已结转收入成本计划
            List<ProjectIncomeCostPlan> projectIncomeCostPlanList2 = projectIncomeCostPlanList.stream()
                    .filter(p -> (StringUtils.isNotEmpty(p.getCarryStatus()) && p.getCarryStatus().equals("1") && p.getCarryoverBillId() != null)).collect(Collectors.toList());

            //未结转且是收入节点的收入成本计划
            List<ProjectIncomeCostPlan> projectIncomeCostPlanList3 = projectIncomeCostPlanList1.stream().filter(p -> p.getIncomeFlag().equals(Boolean.TRUE)).collect(Collectors.toList());
            Long projectIncomeCostPlanIncomeId = null;
            ProjectIncomeCostPlan projectIncomeCostPlanIncome = null;
            if (CollectionUtils.isNotEmpty(projectIncomeCostPlanList3)) {
                // 取第一个未结转且是收入节点的收入成本计划
                projectIncomeCostPlanIncomeId = projectIncomeCostPlanList3.get(0).getId();
                projectIncomeCostPlanIncome = projectIncomeCostPlanList3.get(0);
            }

            // 只取第一个未结转且是收入节点的收入成本计划,其他的都改为非收入节点
            if (CollectionUtils.isNotEmpty(projectIncomeCostPlanList1)) {
                for (int i = 0; i < projectIncomeCostPlanList1.size(); i++) {
                    if (projectIncomeCostPlanIncomeId != null && projectIncomeCostPlanList1.get(i).getId().equals(projectIncomeCostPlanIncomeId)) {
                        continue;
                    }
                    projectIncomeCostPlanList1.get(i).setIncomeFlag(Boolean.FALSE);
                    projectIncomeCostPlanList1.get(i).setIncomeRatio(BigDecimal.ZERO);
                    projectIncomeCostPlanList1.get(i).setIncomeAmount(BigDecimal.ZERO);
                    projectIncomeCostPlanList1.get(i).setCostRatio(BigDecimal.ZERO);
                    projectIncomeCostPlanList1.get(i).setCostAmount(BigDecimal.ZERO);
                    projectIncomeCostPlanMapper.updateByPrimaryKey(projectIncomeCostPlanList1.get(i));
                }
            }


            // 已结转收入成本计划比例之和
            BigDecimal incomeRatioTotal = BigDecimal.ZERO;
            BigDecimal costRatioTotal = BigDecimal.ZERO;
            BigDecimal incomeAmountTotal = BigDecimal.ZERO;
            BigDecimal costAmountTotal = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(projectIncomeCostPlanList2)) {
                for (ProjectIncomeCostPlan incomeCostPlan : projectIncomeCostPlanList2) {
                    BigDecimal incomeRatio = incomeCostPlan.getIncomeRatio() == null ? BigDecimal.ZERO : incomeCostPlan.getIncomeRatio();
                    incomeRatioTotal = incomeRatioTotal.add(incomeRatio);
                    BigDecimal costRatio = incomeCostPlan.getCostRatio() == null ? BigDecimal.ZERO : incomeCostPlan.getCostRatio();
                    costRatioTotal = costRatioTotal.add(costRatio);
                    BigDecimal incomeAmount = incomeCostPlan.getIncomeAmount() == null ? BigDecimal.ZERO : incomeCostPlan.getIncomeAmount();
                    incomeAmountTotal = incomeAmountTotal.add(incomeAmount);
                    BigDecimal costAmount = incomeCostPlan.getCostAmount() == null ? BigDecimal.ZERO : incomeCostPlan.getCostAmount();
                    costAmountTotal = costAmountTotal.add(costAmount);
                }
            }

            // 第一个未结转且是收入节点的收入成本计划,由于月度收入成本计划没有分主辅，这里都只当做主里程碑处理
            if (isMainFlag) {
                if (projectIncomeCostPlanIncome != null) {
                    // 该收入比例=100-已结转的收入成本比例之和
                    BigDecimal incomeRatio = BigDecimal.valueOf(100).subtract(incomeRatioTotal);
                    //收入金额 = 收入总额*收入成本比例/100
//                    BigDecimal incomeAmount = income.multiply(incomeRatio).divide(BigDecimal.valueOf(100));
                    BigDecimal incomeAmount = income.subtract(incomeAmountTotal);
                    // 该成本比例=100-已结转的成本比例之和
                    BigDecimal costRatio = BigDecimal.valueOf(100).subtract(costRatioTotal);
                    //成本金额 = 预算总额*成本比例/100
//                    BigDecimal costRatioAmount = budget.multiply(costRatio).divide(BigDecimal.valueOf(100));
                    BigDecimal costRatioAmount = budget.subtract(costAmountTotal);
                    projectIncomeCostPlanIncome.setIncomeRatio(incomeRatio);
                    projectIncomeCostPlanIncome.setIncomeAmount(incomeAmount);
                    projectIncomeCostPlanIncome.setCostRatio(costRatio);
                    projectIncomeCostPlanIncome.setCostAmount(costRatioAmount);
                    projectIncomeCostPlanMapper.updateByPrimaryKey(projectIncomeCostPlanIncome);
                }

            }

        }

    }

    public void updateActualAmount(Project project) {
        //先判断允差是否+-1
        BigDecimal invoiceAmount = BigDecimal.ZERO;//开票总金额(含税)
        BigDecimal invoiceAmountWithoutTax = BigDecimal.ZERO;//开票总金额(不含税)
        BigDecimal differenceAmount = BigDecimal.ZERO;//差异金额
        //合同编号
        final List<ProjectContractRs> projectContractRs = projectContractRsService.selectByProjectId(project.getId());
        if (ListUtils.isNotEmpty(projectContractRs)) {
            final Long contractId = projectContractRs.get(0).getContractId();
            if (contractId != null) {
                //子合同
                final Contract contract = contractService.findById(contractId);
                //开票计划行
                InvoicePlanDetailExample invoicePlanDetailExample = new InvoicePlanDetailExample();
                invoicePlanDetailExample.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE);
                List<InvoicePlanDetail> detailList = invoicePlanDetailMapper.selectByExample(invoicePlanDetailExample);
                for (InvoicePlanDetail planDetail : detailList) {
                    //开票申请行
                    List<InvoiceReceivable> invoiceReceivableList = new ArrayList<>();
                    InvoiceApplyDetailsExample applyDetailsExample = new InvoiceApplyDetailsExample();
                    applyDetailsExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andStatusEqualTo(InvoiceApplyStatusEnum.DETAILS_PASS.code())
                            .andPlanDetailIdEqualTo(planDetail.getId());
                    List<InvoiceApplyDetails> applyDetailsList = invoiceApplyDetailsMapper.selectByExample(applyDetailsExample);
                    for (InvoiceApplyDetails invoiceApplyDetail : applyDetailsList) {
                        //应收发票
                        InvoiceReceivableExample receivableExample = new InvoiceReceivableExample();
                        receivableExample.createCriteria().andContractIdEqualTo(contractId).andApplyDetailIdEqualTo(invoiceApplyDetail.getId())
                                .andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code()).andStatusEqualTo("开票成功");
                        List<InvoiceReceivable> invoiceReceivables = invoiceReceivableMapper.selectByExampleWithBLOBs(receivableExample);
                        if (!org.springframework.util.CollectionUtils.isEmpty(invoiceReceivables)) {
                            invoiceReceivableList.addAll(invoiceReceivables);
                        }
                    }
                    //如果开票计划下没有应收发票而且是初始化项目
                    if (invoiceReceivableList.size() <= 0 && null != planDetail.getIsImport() && planDetail.getIsImport()) {
                        invoiceAmount = invoiceAmount.add(null != planDetail.getTaxIncludedPrice() ? planDetail.getTaxIncludedPrice() : BigDecimal.ZERO);
                        invoiceAmountWithoutTax = invoiceAmountWithoutTax.add(null != planDetail.getExclusiveOfTax() ? planDetail.getExclusiveOfTax() : BigDecimal.ZERO);
                    }
                    for (InvoiceReceivable invoiceReceivable : invoiceReceivableList) {
                        invoiceAmount = invoiceAmount.add(invoiceReceivable.getTaxIncludedPrice());
                        invoiceAmountWithoutTax = invoiceAmountWithoutTax.add(invoiceReceivable.getExclusiveOfTax());
                    }
                }
                differenceAmount = invoiceAmount.subtract(contract.getAmount());
                //允差+-1
                if (differenceAmount.compareTo(new BigDecimal(1)) > 0 || differenceAmount.compareTo(new BigDecimal(-1)) < 0) {
                    Date afterEndTime = contract.getEndTime();
                    if (afterEndTime == null) {
                        //主合同
                        final Contract parentContract = contractService.findById(contract.getParentId());
                        afterEndTime = parentContract.getEndTime();
                    }
                    //以实际开票总金额（不含税）更新项目金额
                    projectBusinessService.changeIncomeCostPlans(project.getId(), invoiceAmountWithoutTax, contract.getStartTime(), afterEndTime, invoiceAmountWithoutTax, null);
                }
            }
        }
    }

    @Override
    public void abandon(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("项目终止审批作废回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("projectTerminiationAppWorkflowCallBack_abandonCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    if (workflowCallbackCommonDto.getFormInstanceId() == null) {
                        logger.error("项目终止流程作废审批回调 formInstanceId为空，不处理");
                        return;
                    }

                    ProjectTerminationExample projectTerminationExample = new ProjectTerminationExample();
                    projectTerminationExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(formInstanceId);

                    final List<ProjectTermination> projectTerminationList = projectTerminationMapper.selectByExample(projectTerminationExample);
                    if (!CollectionUtils.isNotEmpty(projectTerminationList)) {
                        logger.error("项目终止流程作废审批回调 formInstanceId对应项目终止不存在，不处理");
                        return;
                    }
                    ProjectTermination projectTermination = projectTerminationList.get(0);
                    //更新项目项目状态
                    Project project = projectMapper.selectByPrimaryKey(projectTermination.getProjectId());
                    if (!project.getStatus().equals(ProjectStatus.CLOSE.getCode())){
                        project.setStatus(ProjectStatus.APPROVALED.getCode());
                        projectMapper.updateByPrimaryKey(project);
                    }
                    projectTermination.setStatus(ProjectTerminationEnums.ABANDON.getCode()); // 废弃
                    projectTerminationMapper.updateByPrimaryKey(projectTermination);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目终止审批作废回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目终止审批作废回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目终止审批作废回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    @Override
    public void delete(WorkflowCallbackCommonDto workflowCallbackCommonDto) {
        logger.info("项目终止审批删除回调的处理参数为:{}", JsonUtils.toString(workflowCallbackCommonDto));
        Long formInstanceId = workflowCallbackCommonDto.getFormInstanceId();
        String fdInstanceId = workflowCallbackCommonDto.getFdInstanceId();
        String formUrl = workflowCallbackCommonDto.getFormUrl();
        String eventName = workflowCallbackCommonDto.getEventName();
        Long createUserId = workflowCallbackCommonDto.getCreateUserId();
        String lockName = String.format("projectTerminiationAppWorkflowCallBack_deleteCallback_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    if (workflowCallbackCommonDto.getFormInstanceId() == null) {
                        logger.error("项目终止流程删除审批回调 formInstanceId为空，不处理");
                        return;
                    }

                    ProjectTerminationExample projectTerminationExample = new ProjectTerminationExample();
                    projectTerminationExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(formInstanceId);

                    final List<ProjectTermination> projectTerminationList = projectTerminationMapper.selectByExample(projectTerminationExample);
                    if (!CollectionUtils.isNotEmpty(projectTerminationList)) {
                        logger.error("项目终止流程删除审批回调 formInstanceId对应项目终止不存在，不处理");
                        return;
                    }
                    ProjectTermination projectTermination = projectTerminationList.get(0);
                    //更新项目项目状态
                    Project project = projectMapper.selectByPrimaryKey(projectTermination.getProjectId());
                    if (!project.getStatus().equals(ProjectStatus.CLOSE.getCode())){
                        project.setStatus(ProjectStatus.APPROVALED.getCode());
                        projectMapper.updateByPrimaryKey(project);
                    }
                    projectTermination.setStatus(ProjectTerminationEnums.DELETE.getCode()); // 废弃
                    projectTerminationMapper.updateByPrimaryKey(projectTermination);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("项目终止审批删除回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目终止审批删除回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目终止审批删除回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }
}
