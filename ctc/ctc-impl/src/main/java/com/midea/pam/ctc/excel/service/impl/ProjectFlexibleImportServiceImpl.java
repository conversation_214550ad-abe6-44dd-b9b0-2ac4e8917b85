package com.midea.pam.ctc.excel.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.*;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.crm.entity.Customer;
import com.midea.pam.common.crm.entity.CustomerExample;
import com.midea.pam.common.ctc.entity.*;
import com.midea.pam.common.ctc.excelVo.ProjectImportExcelV2Vo;
import com.midea.pam.common.ctc.excelVo.TemplateSalesSubContractExcelVO;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.*;
import com.midea.pam.ctc.service.AdapterService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.WbsPushToErpService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * 数据初始化-项目基本信息导入
 */
public class ProjectFlexibleImportServiceImpl implements ProjectFlexibleImportService{

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private ProjectTypeMapper projectTypeMapper;
    @Resource
    private UnitExtMapper unitExtMapper;
    @Resource
    private CustomerExtMapper customerExtMapper;
    @Resource
    private BudgetDepExtMapper budgetDepExtMapper;
    @Resource
    private OperatingUnitExtMapper operatingUnitExtMapper;
    @Resource
    private BudgetTreeExtMapper budgetTreeExtMapper;
    @Resource
    private ApplicationIndustryMapper applicationIndustryMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private ResendExecuteMapper resendExecuteMapper;
    @Resource
    private WbsTemplateInfoMapper wbsTemplateInfoMapper;
    @Resource
    private WbsPushToErpService wbsPushToErpService;
    @Resource
    private AdapterService adapterService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private OrganizationCustomDictExtMapper organizationCustomDictExtMapper;

    /**
     * 数据初始化-项目基本信息导入
     *
     * @param excelVoList
     */
    @Override
    public Response importData(List<ProjectImportExcelV2Vo> excelVoList) {
        //抽取抽取部分必填字段进行过滤
        List<ProjectImportExcelV2Vo> filteredList = excelVoList.stream()
                .filter(vo -> vo.getCode() != null)
                .filter(vo -> vo.getName() != null)
                .filter(vo -> vo.getTypeName() != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredList)) {
            throw new ApplicationBizException("excel导入数据不能为空");
        }
        Unit unit = unitExtMapper.selectByPrimaryKey(SystemContext.getUnitId());
        if (null == unit) {
            throw new ApplicationBizException("参数unitId" + SystemContext.getUnitId() + "使用单位不存在");
        }
        List<String> projectCodes = filteredList.stream().map(ProjectImportExcelV2Vo::getCode).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(projectCodes)) {
            UnitExample unitExample = new UnitExample();
            unitExample.createCriteria().andDeleteFlagEqualTo(0).andParentIdEqualTo(SystemContext.getUnitId());
            List<Unit> unitList = unitExtMapper.selectByExample(unitExample);

            ProjectExample projectExample = new ProjectExample();
            ProjectExample.Criteria projectCriteria = projectExample.createCriteria();
            projectCriteria.andDeletedFlagEqualTo(false);
            projectCriteria.andCodeIn(projectCodes);
            if (CollectionUtils.isNotEmpty(unitList)) {
                List<Long> unitId = unitList.stream().map(Unit::getId).collect(Collectors.toList());
                projectCriteria.andUnitIdIn(unitId);
            }
            List<Project> projectList = projectMapper.selectByExample(projectExample);
            if(CollectionUtils.isNotEmpty(projectList)){
                List<String> esixtsCodes = projectList.stream().map(Project::getCode).collect(Collectors.toList());
                throw new ApplicationBizException("项目" + StringUtils.join(esixtsCodes, ",") + "已存在");
            }
        }

        // 项目列表
        List<Project> projectList = new ArrayList<>();
        // 初始化并校验
        boolean check = setAndValidData(filteredList, projectList, unit);

        DataResponse<Object> dataResponse = Response.dataResponse();
        if (check) {
            dataResponse.setMsg("SUCCESS");
            // 保存项目
            saveData(projectList);
            // 项目同步第三方系统
            saveToResend(projectList);
        } else {
            dataResponse.setMsg("FAIL");
        }
        return dataResponse.setData(filteredList.stream().filter(a-> StringUtils.isNotBlank(a.getValidResult())).collect(Collectors.toList()));
    }

    /**
     * 初始化并校验
     *
     * @param excelVoList
     * @param projectList
     * @param unit
     * @return
     */
    private boolean setAndValidData(List<ProjectImportExcelV2Vo> excelVoList, List<Project> projectList, Unit unit) {
        boolean check = true;

        // 查询使用单位下组织参数“产品中心”对应的值
        Set<String> productCenterSet = organizationCustomDictService.queryByName(Constants.PRODUCT_CENTER, SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
        // 查询使用单位下组织参数“利润中心”对应的值
        Set<String> profitCenterSet = organizationCustomDictService.queryByName(Constants.PROFIT_CENTER, SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
        // 查询产品
        Map<String, Long> productMap = getProjectProduct();

        // 缓存
        Map<String, Customer> customerMap = new HashMap<>();
        Map<String, WbsTemplateInfo> wbsTemplateInfoMap = new HashMap<>();
        Map<String, Unit> unitMap = new HashMap<>();
        Map<String, BudgetDep> budgetDepMap = new HashMap<>();
        Map<String, BudgetTree> budgetTreeMap = new HashMap<>();
        Map<String, ApplicationIndustry> applicationIndustryMap = new HashMap<>();
        Map<String, OperatingUnit> operatingUnitMap = new HashMap<>();

        for (ProjectImportExcelV2Vo excelVo : excelVoList) {
            // 错误消息
            List<String> errorMsg = new ArrayList<>();
            Project project = new Project();
            // 设置项目基本信息（项目编号、项目名称、项目状态、项目概述、项目级别、预算合计）
            errorMsg.addAll(setProjectInfo(excelVo, project));
            // 设置项目类型
            errorMsg.addAll(setProjectType(excelVo, project));
            // 设置项目开始、结束日期
            errorMsg.addAll(setProjectStartEndDate(excelVo, project));
            // 设置项目经理
            errorMsg.addAll(setManager(excelVo, project));
            // 设置客户
            errorMsg.addAll(setCustomer(excelVo, project, customerMap));
            // 设置项目财务
            errorMsg.addAll(setFinancial(excelVo, project));
            // 设置项目技术负责人
            errorMsg.addAll(setTechnologyLeader(excelVo, project));
            // 设置项目方案设计人
            errorMsg.addAll(setPlanDesigner(excelVo, project));
            // 设置业务分类（组织）
            errorMsg.addAll(setUnitId(excelVo, project, unitMap));
            // 设置项目属性
            errorMsg.addAll(setPriceTypeName(excelVo, project));
            // 设置业务实体
            errorMsg.addAll(setOperatingUnit(excelVo, project, unit, operatingUnitMap));
            // 设置预算部门
            errorMsg.addAll(setBudgetDeptId(excelVo, project, budgetDepMap));
            // 设置预算树头
            errorMsg.addAll(setBudgetHeader(excelVo, project, budgetTreeMap));
            // 设置应用行业
            errorMsg.addAll(setApplicationIndustry(excelVo, project, applicationIndustryMap));
            // 设置利润中心
            errorMsg.addAll(setProfitCenter(excelVo, project, profitCenterSet));
            // 设置产品中心
            errorMsg.addAll(setProductCenter(excelVo, project, productCenterSet));
            // 设置wbs模板id
            errorMsg.addAll(setwbsTemplateInfoId(excelVo, project, wbsTemplateInfoMap));
            // 设置产品
            errorMsg.addAll(setProductId(excelVo, project, productMap));

            // 币种校验
            if (StringUtils.isBlank(excelVo.getCurrency())) {
                errorMsg.add("币种不能为空");
            } else {
                // 设置币种
                project.setCurrency(excelVo.getCurrency());
            }

            //业务分部
            if (StringUtils.isNotBlank(excelVo.getBusinessSegment())) {
                String businessSegment = excelVo.getBusinessSegment();
                Set<String> organizationCustomDict = organizationCustomDictService.queryByName("虚拟部门对应业务分部", project.getUnitId(), OrgCustomDictOrgFrom.VIRTUAL_ORG);
                if (organizationCustomDict != null && !organizationCustomDict.isEmpty()) {

                    Set<String> allowedSegments = organizationCustomDict.stream()
                            .flatMap(s -> Arrays.stream(s.split(",")))
                            .map(String::trim)
                            .collect(Collectors.toSet());

                    if (!allowedSegments.contains(businessSegment.trim())) {
                        errorMsg.add("业务分部必须存在“虚拟部门对应业务分部”配置里");
                    }else {
                        project.setBusinessSegment(businessSegment);
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(errorMsg)){
                excelVo.setValidResult(Joiner.on("，").join(errorMsg));
                check = false;
            }

            projectList.add(project);
        }
        return check;
    }

    /**
     * 保存项目
     *
     * @param projectList
     * @return
     */
    private void saveData(List<Project> projectList) {
        for (Project project : projectList) {
            //根据项目号查询项目信息
            ProjectExample projectExample = new ProjectExample();
            ProjectExample.Criteria criteria = projectExample.createCriteria();
            criteria.andCodeEqualTo(project.getCode());
            List<Project> projects = projectMapper.selectByExample(projectExample);
            if (ListUtils.isNotEmpty(projects)) {
                project.setId(projects.get(0).getId());
                project.setUpdateAt(new Date());
                project.setUpdateBy(SystemContext.getUserId());
                projectMapper.updateByPrimaryKeySelective(project);
            } else {
                if (null == project.getId()) {
                    project.setDeletedFlag(DeletedFlag.VALID.code());
                    // 项目预算为0，在预算导入时再统计
                    project.setBudgetCost(Optional.ofNullable(project.getBudgetCost()).orElse(BigDecimal.ZERO));
                    // 项目金额为0，在合同导入时再统计
                    project.setAmount(BigDecimal.ZERO);
                    project.setProjectSource(1);
                    project.setIsImport(true);
                    project.setIsObjectiveProject(0);
                    project.setCreateBy(SystemContext.getUserId());
                    project.setCreateAt(new Date());
                    project.setPreviewFlag(false);
                    projectMapper.insert(project);
                } else {
                    project.setUpdateAt(new Date());
                    project.setUpdateBy(SystemContext.getUserId());
                    projectMapper.updateByPrimaryKeySelective(project);
                }
            }
        }
    }

    /**
     * 设置项目基本信息
     * 项目编号-新
     * 项目名称-新
     * 项目状态
     * 项目概述
     * 项目级别
     *
     * @param excelVo
     * @param project
     */
    private List<String> setProjectInfo(ProjectImportExcelV2Vo excelVo, Project project){
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isBlank(excelVo.getCode())) {
            errorMsg.add("项目编号-新不能为空");
        }
        if (StringUtils.isBlank(excelVo.getName())) {
            errorMsg.add("项目名称-新不能为空");
        }
        if (StringUtils.isBlank(excelVo.getStatusName())) {
            errorMsg.add("项目状态不能为空");
        }
        if(CollectionUtils.isNotEmpty(errorMsg)){
            return errorMsg;
        }
        // 项目编号
        project.setCode(excelVo.getCode());
        // 项目名称
        project.setName(excelVo.getName());
        // 项目状态
        if(Objects.equals("进行中", excelVo.getStatusName())){
            project.setStatus(ProjectStatus.APPROVALED.getCode());
        }
        if(Objects.equals("结项", excelVo.getStatusName())){
            project.setStatus(ProjectStatus.CLOSE.getCode());
        }
        // 项目概述（内容为空时取项目名称）
        if(StringUtils.isBlank(excelVo.getSummary())){
            project.setSummary(excelVo.getName());
        }else{
            project.setSummary(excelVo.getSummary());
        }
        // 项目级别（非必填）
        if (StringUtils.isNotBlank(excelVo.getProjectLevelName())) {
            if (Objects.equals("A", excelVo.getProjectLevelName())) {
                project.setProjectLevel(1);
            } else if (Objects.equals("B", excelVo.getProjectLevelName())) {
                project.setProjectLevel(2);
            } else if (Objects.equals("C", excelVo.getProjectLevelName())) {
                project.setProjectLevel(3);
            }
        }
        // 预算合计
        if (StringUtils.isNotBlank(excelVo.getBudgetCostStr())) {
            if (!BigDecimalUtils.isBigDecimal(excelVo.getBudgetCostStr())) {
                errorMsg.add("预算合计金额必须为数字");
            } else {
                BigDecimal budgetCost = new BigDecimal(excelVo.getBudgetCostStr());
                if (budgetCost != null && budgetCost.scale() > 2) {
                    errorMsg.add("预算合计金额不能超过两位小数");
                }
                project.setBudgetCost(budgetCost);
            }
        }
        return errorMsg;
    }

    /**
     * 设置项目类型
     *
     * @param excelVo
     * @return
     */
    private List<String> setProjectType(ProjectImportExcelV2Vo excelVo, Project project) {
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isBlank(excelVo.getTypeName())) {
            errorMsg.add("项目类型不能为空");
            return errorMsg;
        }
        ProjectTypeExample example = new ProjectTypeExample();
        example.createCriteria()
                .andDeletedFlagEqualTo(false)
                .andUnitIdEqualTo(SystemContext.getUnitId())
                .andNameEqualTo(excelVo.getTypeName());
        List<ProjectType> projectTypeList = projectTypeMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(projectTypeList)) {
            errorMsg.add(String.format("项目类型%s不存在", excelVo.getTypeName()));
            return errorMsg;
        }
        project.setType(projectTypeList.get(0).getId());
        return errorMsg;
    }

    /**
     * 设置项目开始、结束日期
     *
     * @param excelVo
     * @param project
     */
    private List<String> setProjectStartEndDate(ProjectImportExcelV2Vo excelVo, Project project ){
        List<String> errorMsg = new ArrayList<>();
        if(null == excelVo.getStartDate() || null == excelVo.getEndDate()){
            if (null == excelVo.getStartDate()) {
                errorMsg.add("项目开始日期不存在");
            }
            if (null == excelVo.getEndDate()) {
                errorMsg.add("项目结束日期不存在");
            }
            return errorMsg;
        }
        if(excelVo.getEndDate().before(excelVo.getStartDate())){
            errorMsg.add("项目开始日期不能晚于项目结束日期");
            return errorMsg;
        }
        project.setStartDate(excelVo.getStartDate());
        project.setEndDate(excelVo.getEndDate());
        return errorMsg;
    }

    /**
     * 设置业务分类（组织）
     *
     * @param excelVo
     * @param project
     */
    private List<String> setUnitId(ProjectImportExcelV2Vo excelVo, Project project, Map<String, Unit> unitMap) {
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isBlank(excelVo.getUnitName())) {
            errorMsg.add("业务分类不能为空");
            return errorMsg;
        }
        Unit unit = null;
        if (unitMap.containsKey(excelVo.getUnitName())) {
            unit = unitMap.get(excelVo.getUnitName());
        } else {
            UnitExample example = new UnitExample();
            example.createCriteria().andDeleteFlagEqualTo(0).andParentIdEqualTo(SystemContext.getUnitId()).andUnitNameEqualTo(excelVo.getUnitName());
            List<Unit> unitList = unitExtMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(unitList)) {
                unit = unitList.get(0);
            }
            unitMap.put(excelVo.getUnitName(), unit);
        }
        if (null == unit) {
            errorMsg.add(String.format("业务分类%s不存在", excelVo.getUnitName()));
            return errorMsg;
        }
        project.setUnitId(unit.getId());
        return errorMsg;
    }

    /**
     * 设置客户
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setCustomer(ProjectImportExcelV2Vo excelVo, Project project, Map<String, Customer> customerMap) {
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isBlank(excelVo.getCustomerCRMCode())) {
            errorMsg.add("CRM客户编码不能为空");
            return errorMsg;
        }
        if (StringUtils.isBlank(excelVo.getCustomerName())) {
//            errorMsg.add("客户名称不能为空");
//            return errorMsg;
        }
        Customer customer = null;
        if(customerMap.containsKey(excelVo.getCustomerCRMCode())){
            customer = customerMap.get(excelVo.getCustomerCRMCode());
        }else{
            //根据客户编码查询客户名称
            CustomerExample customerExample = new CustomerExample();
            customerExample.createCriteria().andDeleteFlagEqualTo(false).andCrmCodeEqualTo(excelVo.getCustomerCRMCode());
            List<Customer> customerList = customerExtMapper.selectByExample(customerExample);
            if(CollectionUtils.isNotEmpty(customerList)){
                customer = customerList.get(0);
            }
            customerMap.put(excelVo.getCustomerCRMCode(), customer);
        }
        if(null == customer){
            errorMsg.add(String.format("CRM客户编码%s不存在", excelVo.getCustomerCRMCode()));
            return errorMsg;
        }
        project.setCustomerId(customer.getId());
        project.setCustomerName(customer.getName());
        return errorMsg;
    }

    /**
     * 设置项目经理
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setManager(ProjectImportExcelV2Vo excelVo, Project project){
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isBlank(excelVo.getManagerMip())) {
            errorMsg.add("项目经理MIP账号不能为空");
            return errorMsg;
        }
        if (StringUtils.isBlank(excelVo.getManagerName())) {
            errorMsg.add("项目经理姓名不能为空");
            return errorMsg;
        }
        UserInfo userInfo = CacheDataUtils.findUserByMip(excelVo.getManagerMip());
        if (null == userInfo) {
            errorMsg.add(String.format("项目经理MIP账号%s不存在", excelVo.getManagerMip()));
        } else {
            if (Objects.equals("N", userInfo.getStatus())) {
                errorMsg.add(String.format("项目经理MIP账号%s已失效", excelVo.getManagerMip()));
            } else {
                project.setManagerId(userInfo.getId());
                project.setManagerName(userInfo.getName());
            }
        }
        return errorMsg;
    }

    /**
     * 设置项目财务
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setFinancial(ProjectImportExcelV2Vo excelVo, Project project){
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isBlank(excelVo.getFinancialMip())) {
            errorMsg.add("项目财务MIP账号不能为空");
            return errorMsg;
        }
        if (StringUtils.isBlank(excelVo.getFinancialName())) {
            errorMsg.add("项目财务姓名不能为空");
            return errorMsg;
        }
        UserInfo userInfo = CacheDataUtils.findUserByMip(excelVo.getFinancialMip());
        if (null == userInfo) {
            errorMsg.add(String.format("项目财务MIP账号%s不存在", excelVo.getFinancialMip()));
        } else {
            if (Objects.equals("N", userInfo.getStatus())) {
                errorMsg.add(String.format("项目财务MIP账号%s已失效", excelVo.getFinancialMip()));
            } else {
                project.setFinancial(userInfo.getId());
            }
        }
        return errorMsg;
    }

    /**
     * 设置项目技术负责人
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setTechnologyLeader(ProjectImportExcelV2Vo excelVo, Project project){
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isBlank(excelVo.getTechnologyLeaderMip())) {
//            errorMsg.add("技术负责人MIP账号不能为空");
            return errorMsg;
        }
//        if (StringUtils.isBlank(excelVo.getTechnologyLeaderName())) {
//            errorMsg.add("技术负责人姓名不能为空");
//            return errorMsg;
//        }
        UserInfo userInfo = CacheDataUtils.findUserByMip(excelVo.getTechnologyLeaderMip());
        if (null == userInfo) {
            errorMsg.add(String.format("技术负责人MIP账号%s不存在", excelVo.getTechnologyLeaderMip()));
        } else {
            if (Objects.equals("N", userInfo.getStatus())) {
                errorMsg.add(String.format("技术负责人MIP账号%s已失效", excelVo.getTechnologyLeaderMip()));
            } else {
                project.setTechnologyLeaderId(userInfo.getId());
                project.setTechnologyLeaderName(userInfo.getName());
            }
        }
        return errorMsg;
    }

    /**
     * 设置项目方案设计人
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setPlanDesigner(ProjectImportExcelV2Vo excelVo, Project project){
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isBlank(excelVo.getPlanDesignerMip())) {
//            errorMsg.add("方案设计人MIP账号不能为空");
            return errorMsg;
        }
//        if (StringUtils.isBlank(excelVo.getPlanDesignerName())) {
//            errorMsg.add("方案设计人姓名不能为空");
//            return errorMsg;
//        }
        UserInfo userInfo = CacheDataUtils.findUserByMip(excelVo.getPlanDesignerMip());
        if (null == userInfo) {
            errorMsg.add(String.format("方案设计人MIP账号%s不存在", excelVo.getPlanDesignerMip()));
        } else {
            if (Objects.equals("N", userInfo.getStatus())) {
                errorMsg.add(String.format("方案设计人MIP账号%s已失效", excelVo.getPlanDesignerMip()));
            } else {
                project.setPlanDesignerId(userInfo.getId());
                project.setPlanDesignerName(userInfo.getName());
            }
        }
        return errorMsg;
    }

    /**
     * 设置项目属性
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setPriceTypeName(ProjectImportExcelV2Vo excelVo, Project project){
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isBlank(excelVo.getPriceTypeName())) {
            errorMsg.add("项目属性不能为空");
            return errorMsg;
        }
        if (Objects.equals("内部", excelVo.getPriceTypeName())) {
            project.setPriceType("1");
        } else if (Objects.equals("外部", excelVo.getPriceTypeName())) {
            project.setPriceType("2");
        } else if (Objects.equals("研发", excelVo.getPriceTypeName())) {
            project.setPriceType("3");
        } else {
            errorMsg.add(String.format("项目属性%s不存在", excelVo.getPriceTypeName()));
        }
        return errorMsg;
    }

    /**
     * 设置预算部门
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setBudgetDeptId(ProjectImportExcelV2Vo excelVo, Project project, Map<String, BudgetDep> budgetDepMap){
        List<String> errorMsg = new ArrayList<>();
        if(StringUtils.isBlank(excelVo.getBudgetDeptName())){
            errorMsg.add("预算部门不能为空");
            return errorMsg;
        }

        BudgetDep budgetDep = null;
        if (budgetDepMap.containsKey(excelVo.getBudgetDeptName())) {
            budgetDep = budgetDepMap.get(excelVo.getBudgetDeptName());
        } else {
            BudgetDepExample budgetDepExample = new BudgetDepExample();
            budgetDepExample.createCriteria().andEnableFlagEqualTo("Y").andBudgetDepNameEqualTo(excelVo.getBudgetDeptName());
            List<BudgetDep> budgetDepList = budgetDepExtMapper.selectByExample(budgetDepExample);
            if (CollectionUtils.isNotEmpty(budgetDepList)) {
                budgetDep = budgetDepList.get(0);
            }
            budgetDepMap.put(excelVo.getBudgetDeptName(), budgetDep);
        }
        if(null == budgetDep){
            errorMsg.add(String.format("预算部门%s不存在", excelVo.getBudgetDeptName()));
            return errorMsg;
        }
        project.setBudgetDeptId(budgetDep.getId());
        return errorMsg;
    }

    /**
     * 设置业务实体
     *
     * @param excelVo
     * @param project
     * @param unit
     * @return
     */
    private List<String> setOperatingUnit(ProjectImportExcelV2Vo excelVo, Project project, Unit unit, Map<String, OperatingUnit> operatingUnitMap) {
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isBlank(excelVo.getOuName())) {
            errorMsg.add("业务实体不能为空");
            return errorMsg;
        }
        OperatingUnit operatingUnit = null;
        if (operatingUnitMap.containsKey(excelVo.getOuName())) {
            operatingUnit = operatingUnitMap.get(excelVo.getOuName());
        } else {
            List<Long> secondUnitIdList = new ArrayList<>();
            // 使用单位
            if (null == unit.getParentId()) {
                UnitExample unitExample = new UnitExample();
                unitExample.createCriteria().andDeleteFlagEqualTo(0).andParentIdEqualTo(unit.getId());
                List<Unit> unitList = unitExtMapper.selectByExample(unitExample);
                if (CollectionUtils.isEmpty(unitList)) {
                    errorMsg.add(String.format("使用单位下的业务实体%s不存在", excelVo.getOuName()));
                    return errorMsg;
                }
                for (Unit secondUnit : unitList) {
                    secondUnitIdList.add(secondUnit.getId());
                }
            }
            // 虚拟部门（业务实体）
            else {
                secondUnitIdList.add(unit.getId());
            }
            List<OperatingUnit> operatingUnitList = operatingUnitExtMapper.selectByUnitId(excelVo.getOuName(), secondUnitIdList);
            if (CollectionUtils.isNotEmpty(operatingUnitList)) {
                operatingUnit = operatingUnitList.get(0);
            }
            operatingUnitMap.put(excelVo.getOuName(), operatingUnit);
        }
        if (null == operatingUnit) {
            errorMsg.add(String.format("业务实体%s不存在", excelVo.getOuName()));
            return errorMsg;
        }
        project.setOuId(operatingUnit.getOperatingUnitId());
        return errorMsg;
    }

    /**
     * 设置预算树头
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setBudgetHeader(ProjectImportExcelV2Vo excelVo, Project project,Map<String, BudgetTree> budgetTreeMap){
        List<String> errorMsg = new ArrayList<>();
        if(StringUtils.isBlank(excelVo.getBudgetHeaderName())){
            errorMsg.add("预算树头不能为空");
            return errorMsg;
        }
        BudgetTree budgetTree = null;
        if(budgetTreeMap.containsKey(excelVo.getBudgetHeaderName())){
            budgetTree = budgetTreeMap.get(excelVo.getBudgetHeaderName());
        }else{
            BudgetTreeExample budgetTreeExample = new BudgetTreeExample();
            budgetTreeExample.createCriteria().andBudgetNameEqualTo(excelVo.getBudgetHeaderName());
            List<BudgetTree> budgetTreeList = budgetTreeExtMapper.selectByExample(budgetTreeExample);
            if(CollectionUtils.isNotEmpty(budgetTreeList)){
                budgetTree = budgetTreeList.get(0);
            }
            budgetTreeMap.put(excelVo.getBudgetHeaderName(), budgetTree);
        }
        if(null == budgetTree){
            errorMsg.add(String.format("预算树头%s不存在", excelVo.getBudgetHeaderName()));
            return errorMsg;
        }
        project.setBudgetHeaderId(budgetTree.getBudgetHeadersId());
        project.setBudgetHeaderName(excelVo.getBudgetHeaderName());
        return errorMsg;
    }

    /**
     * 设置应用行业
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setApplicationIndustry(ProjectImportExcelV2Vo excelVo, Project project, Map<String, ApplicationIndustry> applicationIndustryMap){
        List<String> errorMsg = new ArrayList<>();

        if(StringUtils.isBlank(excelVo.getApplicationIndustryName()) ){
            // 非昆山必填
//            if(!Constants.YL.equals(SystemContext.getUnitId())){
//                errorMsg.add("应用行业不能为空");
//            }
            return errorMsg;
        }

        ApplicationIndustry applicationIndustry = null;
        if (applicationIndustryMap.containsKey(excelVo.getApplicationIndustryName())) {
            applicationIndustry = applicationIndustryMap.get(excelVo.getApplicationIndustryName());
        } else {
            ApplicationIndustryExample applicationIndustryExample = new ApplicationIndustryExample();
            ApplicationIndustryExample.Criteria criteria = applicationIndustryExample.createCriteria();
            criteria.andEnabledEqualTo(1).andUnitIdEqualTo(SystemContext.getUnitId()).andNameEqualTo(excelVo.getApplicationIndustryName());
            List<ApplicationIndustry> applicationIndustryList = applicationIndustryMapper.selectByExample(applicationIndustryExample);
            if (CollectionUtils.isNotEmpty(applicationIndustryList)) {
                applicationIndustry = applicationIndustryList.get(0);
            }
        }

        if (null == applicationIndustry) {
            errorMsg.add("应用行业不存在");
            return errorMsg;
        } else {
            project.setApplicationIndustry(applicationIndustry.getName());
        }
        return errorMsg;
    }

    /**
     * 设置利润中心
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setProfitCenter(ProjectImportExcelV2Vo excelVo, Project project, Set<String> profitCenterSet){
        List<String> errorMsg = new ArrayList<>();
        if(StringUtils.isBlank(excelVo.getProfitCenter())){
//            errorMsg.add("利润中心不能为空");
            return errorMsg;
        }

        // 昆山单位
        if (Constants.YL.equals(SystemContext.getUnitId())) {
            if (StringUtils.isNotBlank(excelVo.getProfitCenter())) {
                if (!profitCenterSet.contains(excelVo.getProfitCenter())) {
                    errorMsg.add("利润中心不存在");
                    return errorMsg;
                } else {
                    project.setApplicationIndustry(excelVo.getProfitCenter());
                }
            }
        } else {
            if (StringUtils.isBlank(excelVo.getProfitCenter())) {
                errorMsg.add("利润中心不能为空");
                return errorMsg;
            } else {
                if (!profitCenterSet.contains(excelVo.getProfitCenter())) {
                    errorMsg.add("利润中心不存在");
                    return errorMsg;
                } else {
                    project.setApplicationIndustry(excelVo.getProfitCenter());
                }
            }
        }
        return errorMsg;
    }

    /**
     * 设置产品中心
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setProductCenter(ProjectImportExcelV2Vo excelVo, Project project, Set<String> productCenterSet){
        List<String> errorMsg = new ArrayList<>();
        if(StringUtils.isBlank(excelVo.getProductCenter())){
//            errorMsg.add("产品中心不能为空");
            return errorMsg;
        }
        // 昆山单位
        if (Constants.YL.equals(SystemContext.getUnitId())) {
            if (StringUtils.isNotBlank(excelVo.getProductCenter())) {
                if (!productCenterSet.contains(excelVo.getProductCenter())) {
                    errorMsg.add("产品中心不存在");
                    return errorMsg;
                } else {
                    project.setApplicationIndustry(excelVo.getProductCenter());
                }
            }
        } else {
            if (StringUtils.isBlank(excelVo.getProductCenter())) {
                errorMsg.add("产品中心不能为空");
                return errorMsg;
            } else {
                if (!productCenterSet.contains(excelVo.getProductCenter())) {
                    errorMsg.add("产品中心不存在");
                    return errorMsg;
                } else {
                    project.setApplicationIndustry(excelVo.getProductCenter());
                }
            }
        }
        return errorMsg;
    }

    /**
     * 设置wbs模板id
     *
     * @param excelVo
     * @param project
     * @return
     */
    private List<String> setwbsTemplateInfoId(ProjectImportExcelV2Vo excelVo, Project project, Map<String, WbsTemplateInfo> wbsTemplateInfoMap){
        List<String> errorMsg = new ArrayList<>();
        if(StringUtils.isBlank(excelVo.getWbsTemplateInfoName())){
            project.setWbsEnabled(false);
            return errorMsg;
        }

        WbsTemplateInfo wbsTemplateInfo = null;
        if (wbsTemplateInfoMap.containsKey(excelVo.getWbsTemplateInfoName())) {
            wbsTemplateInfo = wbsTemplateInfoMap.get(excelVo.getWbsTemplateInfoName());
        } else {
            WbsTemplateInfoExample wbsTemplateInfoExample = new WbsTemplateInfoExample();
            wbsTemplateInfoExample.createCriteria().andDeletedFlagEqualTo(false).andUnitIdEqualTo(SystemContext.getUnitId()).andTemplateNameEqualTo(excelVo.getWbsTemplateInfoName());
            List<WbsTemplateInfo> wbsTemplateInfoList = wbsTemplateInfoMapper.selectByExample(wbsTemplateInfoExample);
            if(CollectionUtils.isNotEmpty(wbsTemplateInfoList)){
                wbsTemplateInfo = wbsTemplateInfoList.get(0);
            }
            wbsTemplateInfoMap.put(excelVo.getWbsTemplateInfoName(), wbsTemplateInfo);
        }
        if(null == wbsTemplateInfo){
            errorMsg.add(String.format("wbs模板%s不存在", excelVo.getWbsTemplateInfoName()));
            return errorMsg;
        }
        project.setWbsTemplateInfoId(wbsTemplateInfo.getId());
        project.setWbsEnabled(true);
        return errorMsg;
    }

    /**
     * 设置产品
     *
     * @param excelVo
     * @param project
     * @param productMap
     * @return
     */
    private List<String> setProductId(ProjectImportExcelV2Vo excelVo, Project project, Map<String, Long> productMap){
        List<String> errorMsg = new ArrayList<>();
        if(StringUtils.isBlank(excelVo.getProductName())){
            return errorMsg;
        }
        if (!productMap.containsKey(excelVo.getProductName())) {
            errorMsg.add(String.format("产品%s不存在", excelVo.getProductName()));
            return errorMsg;
        }
        project.setProductId(productMap.get(excelVo.getProductName()));
        return errorMsg;
    }

    /**
     * 获取产品
     *
     * @return
     */
    private Map<String, Long> getProjectProduct() {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", SystemContext.getUnitId());
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/projectProductMaintenance/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectProductMaintenance>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<ProjectProductMaintenance>>>() {
                });

        //查询产品信息
        List<ProjectProductMaintenance> projectProduct = response.getData();
        if (CollectionUtils.isNotEmpty(projectProduct)) {
            return projectProduct.stream().collect(Collectors.toMap(ProjectProductMaintenance::getProjectProductName, ProjectProductMaintenance::getId, (key1, key2) -> key2));
        }
        return new HashMap<>();
    }

    /**
     * 项目同步第三方系统
     *
     * @param projectList
     */
    private void saveToResend(List<Project> projectList) {
        for (Project project : projectList) {

            // ProjectSynchroEvent 写入项目号推送接口表事件
            HandleDispatcher.route(BusinessTypeEnums.CREATE_PROJECT_CODE_EMS.getCode(), String.valueOf(project.getId()), null, null, false);
            HandleDispatcher.route(BusinessTypeEnums.CREATE_PROJECT_CODE_ERP.getCode(), String.valueOf(project.getId()), null, null, false);
            HandleDispatcher.route(BusinessTypeEnums.CREATE_PROJECT_CODE_GFP.getCode(), String.valueOf(project.getId()), null, null, false);
        }

        // 异步执行resend同步第三方系统 5秒后再运行 本地不要执行
        adapterService.syncAction(5000L);
    }
}
