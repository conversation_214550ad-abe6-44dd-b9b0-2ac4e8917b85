package com.midea.pam.ctc.wbs.service.impl;

import com.alibaba.fastjson.JSON;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.ProjectActivityCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleDetailCache;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.FeeItemExpenseTypeDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetChangeSummaryHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetChangeDTO;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetObjectDto;
import com.midea.pam.common.ctc.dto.WbsDynamicFieldsDto;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.OrganizationCustomDictExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangeSummaryHistory;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.ctc.entity.ProjectWbsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetBaseline;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetBaselineExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetDynamic;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetDynamicChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetDynamicExample;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetExample;
import com.midea.pam.common.ctc.vo.ProjectWbsBudgetChangeVO;
import com.midea.pam.common.enums.AttachmentStatus;
import com.midea.pam.common.enums.ChangeType;
import com.midea.pam.common.enums.CtcProjectChangeType;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.FeeSettingModeEnum;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.ProjectChangeStatus;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.common.util.cache.ProjectActivityCacheUtils;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.mapper.OrganizationCustomDictMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetChangeSummaryHistoryExtMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetChangeSummaryHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.ProjectHistoryHeaderMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetBaselineChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetBaselineMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetDynamicChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetDynamicChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetDynamicMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryChangeHistoryExtMapper;
import com.midea.pam.ctc.mapper.UnitExtMapper;
import com.midea.pam.ctc.project.service.ProjectBudgetChangeSummaryHistoryService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetChangeHistoryService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetChangeService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetCheckHelpper;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetDynamicChangeHistoryService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetService;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetSummaryService;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ProjectWbsBudgetChangeServiceImpl implements ProjectWbsBudgetChangeService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectTypeService projectTypeService;
    @Resource
    private ProjectHistoryHeaderMapper projectHistoryHeaderMapper;
    @Resource
    private ProjectWbsBudgetMapper projectWbsBudgetMapper;
    @Resource
    private ProjectWbsBudgetChangeHistoryMapper projectWbsBudgetChangeHistoryMapper;
    @Resource
    private ProjectBudgetChangeSummaryHistoryService projectBudgetChangeSummaryHistoryService;
    @Resource
    private ProjectWbsBudgetChangeHistoryService projectWbsBudgetChangeHistoryService;
    @Resource
    private ProjectWbsBudgetSummaryService projectWbsBudgetSummaryService;
    @Resource
    private ProjectWbsBudgetDynamicMapper projectWbsBudgetDynamicMapper;
    @Resource
    private ProjectWbsBudgetDynamicChangeHistoryMapper projectWbsBudgetDynamicChangeHistoryMapper;
    @Resource
    private ProjectWbsBudgetDynamicChangeHistoryService projectWbsBudgetDynamicChangeHistoryService;
    @Resource
    private WbsTemplateRuleService wbsTemplateRuleService;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private ProjectBudgetChangeSummaryHistoryMapper projectBudgetChangeSummaryHistoryMapper;
    @Resource
    private ProjectWbsBudgetChangeHistoryExtMapper projectWbsBudgetChangeHistoryExtMapper;
    @Resource
    private ProjectWbsBudgetBaselineChangeHistoryExtMapper projectWbsBudgetBaselineChangeHistoryExtMapper;
    @Resource
    private ProjectWbsBudgetSummaryChangeHistoryExtMapper projectWbsBudgetSummaryChangeHistoryExtMapper;
    @Resource
    private ProjectWbsBudgetDynamicChangeHistoryExtMapper projectWbsBudgetDynamicChangeHistoryExtMapper;
    @Resource
    private ProjectBudgetChangeSummaryHistoryExtMapper projectBudgetChangeSummaryHistoryExtMapper;
    @Resource
    private ProjectWbsBudgetCheckHelpper projectWbsBudgetCheckHelpper;
    @Resource
    private ProjectWbsBudgetBaselineMapper projectWbsBudgetBaselineMapper;
    @Resource
    private UnitExtMapper unitExtMapper;
    @Resource
    private OrganizationCustomDictMapper organizationCustomDictMapper;
    @Resource
    private ProjectWbsBudgetService projectWbsBudgetService;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private ProjectExtMapper projectExtMapper;


    /**
     * 发起WBS预算变更
     *
     * @param projectWbsBudgetChangeDTO 变更信息
     * @return
     */
    @Override
    @Transactional
    public Long wbsBudgetChange(ProjectWbsBudgetChangeDTO projectWbsBudgetChangeDTO) {
        // 参数校验
        wbsBudgetChangeParamCheck(projectWbsBudgetChangeDTO);

        final Long projectId = projectWbsBudgetChangeDTO.getProjectId();
        final Project project = projectService.selectByPrimaryKey(projectId);
        Assert.notNull(project, "项目不存在" + projectId);
        Assert.isTrue(Objects.equals(projectWbsBudgetChangeDTO.getProjectCurrentUpdateAt(), project.getUpdateAt()), "预算信息有变动，请刷新页面");
        // 权限校验
        authCheck(project);

        /* 历史数据：预算 */
        ProjectWbsBudgetExample example1 = new ProjectWbsBudgetExample();
        example1.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(projectId);
        List<ProjectWbsBudget> projectWbsBudgetList = projectWbsBudgetMapper.selectByExample(example1);

        // 1、正常提交；2、驳回后提交
        final Integer type = projectWbsBudgetChangeDTO.getType();
        if (2 == type) {
            if (null == projectWbsBudgetChangeDTO.getHeaderId()) {
                throw new ApplicationBizException("驳回后发起wbs预算变更，变更记录的headerId不能为空");
            }
            logger.info("驳回后提交发起WBS预算变更：projectId={},headerId={}", projectWbsBudgetChangeDTO.getProjectId(), projectWbsBudgetChangeDTO.getHeaderId());

            // 变更后提交需要特殊处理，将正式表ID赋值到变更记录ID
            if (CollectionUtils.isNotEmpty(projectWbsBudgetChangeDTO.getProjectWbsBudgetChangeHistories())) {
                for (Map<String, Object> history : projectWbsBudgetChangeDTO.getProjectWbsBudgetChangeHistories()) {
                    Long id = MapUtils.getLong(history, WbsBudgetFieldConstant.ID);
                    Long originId = MapUtils.getLong(history, WbsBudgetFieldConstant.ORIGIN_ID);
                    if (null != originId) {
                        // 如果是历史记录，驳回后重新提交要把原id重新赋值到id上
                        history.put(WbsBudgetFieldConstant.ID, originId);
                    } else if (CollectionUtils.isNotEmpty(projectWbsBudgetList)) {
                        // 如果是新增的记录，驳回后重新提交要把id清空
                        if (projectWbsBudgetList.stream().filter(a -> a.getId().equals(id)).count() == 0) {
                            history.remove(WbsBudgetFieldConstant.ID);
                        }
                    }
                }
            }
        } else {
            logger.info("正常提交发起WBS预算变更：projectId={},headerId={}", projectWbsBudgetChangeDTO.getProjectId(), projectWbsBudgetChangeDTO.getHeaderId());
        }

        final Long newHeaderId = saveOrUpdateChangeRecord(projectWbsBudgetChangeDTO);
        /* 删除变更记录 */
        if (null != newHeaderId) {
            projectWbsBudgetChangeHistoryExtMapper.deleteByHeaderId(newHeaderId);
            projectWbsBudgetBaselineChangeHistoryExtMapper.deleteByHeaderId(newHeaderId);
            projectWbsBudgetSummaryChangeHistoryExtMapper.deleteByHeaderId(newHeaderId);
            projectWbsBudgetDynamicChangeHistoryExtMapper.deleteByHeaderId(newHeaderId);
            projectBudgetChangeSummaryHistoryExtMapper.deleteByHeaderId(newHeaderId);
        }

        // wbs预算变更保存
        wbsBudgetChangeSaveAll(projectWbsBudgetChangeDTO.getProjectWbsBudgetChangeHistories(), project, newHeaderId);

        // 项目变更汇总记录保存
        ProjectBudgetChangeSummaryHistory projectBudgetChangeSummaryHistory = projectWbsBudgetChangeDTO.getProjectBudgetChangeSummaryHistory();
        budgetSummaryChangeSave(projectBudgetChangeSummaryHistory, projectId, newHeaderId);

        //保存附件信息
        this.saveCtcAttachmentDtos(AttachmentStatus.CHECKING.code(), projectWbsBudgetChangeDTO.getAttachmentDtos(), CtcAttachmentModule.PROJECT_WBS_BUDGET_CHANGE.code(), newHeaderId);

        return newHeaderId;
    }

    /**
     * 获取WBS预算变更记录
     *
     * @param headerId
     * @param data
     * @return
     */
    @Override
    public ProjectWbsBudgetChangeVO getWbsBudgetHistory(Long headerId, ProjectWbsBudgetChangeVO data) {

        ProjectWbsBudgetChangeVO vo = new ProjectWbsBudgetChangeVO();

        ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(headerId);
        Assert.notNull(projectHistoryHeader, "变更记录不存在");
        vo.setHeader(projectHistoryHeader);
        vo.setRefuseType(data.getRefuseType());

        final Long projectId = projectHistoryHeader.getProjectId();
        final Project project = projectService.selectByPrimaryKey(projectId);
        if (project != null) {
            vo.setCode(project.getCode());
            vo.setProjectName(project.getName());
            vo.setProjectAmount(project.getAmount());
            vo.setWbsEnabled(project.getWbsEnabled());
            vo.setWbsTemplateInfoId(project.getWbsTemplateInfoId());
        }

        ProjectBudgetChangeSummaryHistoryDto summaryHistory = BeanConverter.copyProperties(projectBudgetChangeSummaryHistoryService.getByHeaderId(headerId), ProjectBudgetChangeSummaryHistoryDto.class);
        if (null != summaryHistory.getBeforeAmount()) {
            summaryHistory.setBeforeAmount(new BigDecimal(summaryHistory.getBeforeAmount().stripTrailingZeros().toPlainString()));
        }
        if (null != summaryHistory.getAfterAmount()) {
            summaryHistory.setAfterAmount(new BigDecimal(summaryHistory.getAfterAmount().stripTrailingZeros().toPlainString()));
        }
        if (null != summaryHistory.getChangeAmount()) {
            summaryHistory.setChangeAmount(new BigDecimal(summaryHistory.getChangeAmount().stripTrailingZeros().toPlainString()));
        }
        ProjectDto query = new ProjectDto();
        query.setId(projectId);
        projectBusinessService.calculationContractAmount(query);
        summaryHistory.setStandardAmount(query.getStandardAmount()); //项目金额（不含税）-本位币
        summaryHistory.setCurrency(project.getCurrency()); //币种
        vo.setSummaryHistory(summaryHistory);

        // wbs预算变更
        setWbsBudgetChangeHistorys(projectHistoryHeader, vo);

        // 获取附件
        vo.setAttachmentDtos(findCtcAttachmentDto(CtcAttachmentModule.PROJECT_WBS_BUDGET_CHANGE.code(), headerId));

        return vo;
    }

    /**
     * 设置wbs预算变更
     *
     * @param header
     * @param vo
     */
    private void setWbsBudgetChangeHistorys(ProjectHistoryHeader header, ProjectWbsBudgetChangeVO vo) {
        Map<String, Object> param = new HashMap<>();
        param.put(WbsBudgetFieldConstant.HEADER_ID, header.getId());
        List<ProjectWbsBudgetChangeHistoryDto> changeHistoryList = projectWbsBudgetChangeHistoryExtMapper.listByParam(param);
        if (CollectionUtils.isEmpty(changeHistoryList)) {
            return;
        }
        List<ProjectWbsBudgetChangeHistoryDto> historyList2 = changeHistoryList.stream().filter(a -> a.getHistoryType().equals(HistoryType.HISTORY.getCode()) && Boolean.TRUE.equals(a.getShowCase())).collect(Collectors.toList());
        List<ProjectWbsBudgetChangeHistoryDto> changeList2 = changeHistoryList.stream().filter(a -> a.getHistoryType().equals(HistoryType.CHANGE.getCode()) && Boolean.TRUE.equals(a.getShowCase())).collect(Collectors.toList());
        List<Map<String, Object>> historyMapList = ProjectWbsBudgetChangeHistoryDto.dto2MapBatch(historyList2);
        //审批撤回或驳回，变更前的值取当前的项目wbs预算 BUG2023022823863
        if (Objects.equals(header.getStatus(), ProjectChangeStatus.REFUSE.getCode()) || Objects.equals(header.getStatus(), ProjectChangeStatus.RETURN.getCode())) {
            ProjectWbsBudgetObjectDto wbsBudgetInfo = projectWbsBudgetService.findWbsBudgetInfoByWebfront(header.getProjectId());
            if (wbsBudgetInfo != null) {
                historyMapList = wbsBudgetInfo.getWbsBudgetList();
                for (Map<String, Object> history : historyMapList) {
                    history.put(WbsBudgetFieldConstant.ORIGIN_ID, MapUtils.getLong(history, WbsBudgetFieldConstant.ID));
                }
            }
        }
        List<Map<String, Object>> changeMapList = ProjectWbsBudgetChangeHistoryDto.dto2MapBatch(changeList2);
        /* 驳回后重新查询 */
        if (vo.getRefuseType() == 2) {
            // 计算 需求预算、在途成本、已发生成本
//            historyMapList = projectWbsBudgetSummaryService.calculationCost(historyMapList);
            // 计算 需求预算、在途成本、已发生成本
//            changeMapList = projectWbsBudgetSummaryService.calculationCost(changeMapList);
        }

        List<ProjectWbsBudgetChangeVO.ChangeHistory> result = new ArrayList<>();

        ProjectWbsBudgetChangeVO.ChangeHistory changeHistory;
        for (Map<String, Object> change : changeMapList) {
            changeHistory = new ProjectWbsBudgetChangeVO.ChangeHistory();
            change.put(WbsBudgetFieldConstant.PROJECT_WBS_BUDGET_ID, MapUtils.getLong(change, WbsBudgetFieldConstant.ORIGIN_ID));
            /* 变更后金额，为了方便前端查询组装的字段 */
            // 【change】变更后预算
            change.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, BigDecimal.valueOf(MapUtils.getDouble(change, WbsBudgetFieldConstant.PRICE)).setScale(2, RoundingMode.HALF_UP));
            // 【change】累计变更金额
            change.put(WbsBudgetFieldConstant.AFTER_CHANGE_CHANGE_ACCUMULATE_COST, BigDecimal.valueOf(MapUtils.getDouble(change, WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST)).setScale(2, RoundingMode.HALF_UP));
            // 【change】变更后剩余可用预算
            change.put(WbsBudgetFieldConstant.AFTER_CHANGE_REMAINING_COST, BigDecimal.valueOf(MapUtils.getDouble(change, WbsBudgetFieldConstant.REMAINING_COST)).setScale(2, RoundingMode.HALF_UP));

            List<Map<String, Object>> filterHistoryList = new ArrayList<>();
            Long originId = MapUtils.getLong(change, WbsBudgetFieldConstant.ORIGIN_ID);
            if (null != originId) {
                filterHistoryList = historyMapList.stream().filter(a -> Objects.equals(MapUtils.getLong(a, WbsBudgetFieldConstant.ORIGIN_ID), originId)).collect(Collectors.toList());
            }

            if (null == originId || CollectionUtils.isEmpty(filterHistoryList)) {
                // 【新增】 前端也要显示变更前，方便数据展示
                Map<String, Object> history = new HashMap<>();
                history.putAll(change);
                /* 新增默认金额都为0 */
                history.put(WbsBudgetFieldConstant.PRICE, BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString());
                history.put(WbsBudgetFieldConstant.BASELINE_COST, BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString());
                history.put(WbsBudgetFieldConstant.DEMAND_COST, BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString());
                history.put(WbsBudgetFieldConstant.ON_THE_WAY_COST, BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString());
                history.put(WbsBudgetFieldConstant.INCURRED_COST, BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString());
                history.put(WbsBudgetFieldConstant.REMAINING_COST, BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString());
                // 【history】变更后预算
                history.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString());
                // 【change】变更金额 change.price - history.price
                change.put(WbsBudgetFieldConstant.CHANGE_PRICE, BigDecimal.valueOf(MapUtils.getDouble(change, WbsBudgetFieldConstant.PRICE)).subtract(BigDecimal.valueOf(MapUtils.getDouble(history, WbsBudgetFieldConstant.PRICE))).setScale(2, RoundingMode.HALF_UP).toPlainString());
                changeHistory.setType(ChangeType.ADD.code());
                changeHistory.setHistory(history);
            } else {
                Map<String, Object> history = new HashMap<>();
                history.putAll(filterHistoryList.get(0));
                // 【history】变更后剩余可用预算
                history.put(WbsBudgetFieldConstant.AFTER_CHANGE_REMAINING_COST, BigDecimal.valueOf(MapUtils.getDouble(history, WbsBudgetFieldConstant.REMAINING_COST)).setScale(2, RoundingMode.HALF_UP).toPlainString());
                // 【history】变更后预算
                history.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, BigDecimal.valueOf(MapUtils.getDouble(history, WbsBudgetFieldConstant.PRICE)).setScale(2, RoundingMode.HALF_UP).toPlainString());
                // 【change】变更金额 change.price - history.price
                change.put(WbsBudgetFieldConstant.CHANGE_PRICE, BigDecimal.valueOf(MapUtils.getDouble(change, WbsBudgetFieldConstant.PRICE)).subtract(BigDecimal.valueOf(MapUtils.getDouble(history, WbsBudgetFieldConstant.PRICE))).setScale(2, RoundingMode.HALF_UP).toPlainString());
                changeHistory.setHistory(history);

                if (Boolean.TRUE.equals(MapUtils.getBoolean(change, WbsBudgetFieldConstant.DELETED_FLAG))) {
                    // 【删除】
                    changeHistory.setType(ChangeType.DEL.code());
                } else {
                    if (Boolean.TRUE.equals(ProjectWbsBudgetChangeHistoryDto.equlas(history, change))) {
                        // 【无变更】 内容相同
                        changeHistory.setType(ChangeType.NO.code());
                    } else {
                        // 【修改】
                        changeHistory.setType(ChangeType.UPDATE.code());
                    }
                }
            }
            changeHistory.setChange(change);
            result.add(changeHistory);
        }
        vo.setWbsBudgets(result);
    }

    /**
     * wbs预算变更保存（全量数据）
     *
     * @param projectWbsBudgetChangeHistorys
     * @param project
     * @param headerId
     */
    private void wbsBudgetChangeSaveAll(List<Map<String, Object>> projectWbsBudgetChangeHistorys, Project project, Long headerId) {

        // wbs预算id
        List<Long> idList = projectWbsBudgetChangeHistorys.stream().map(a -> MapUtils.getLong(a, WbsBudgetFieldConstant.ID)).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());

        // 获取费用类型经济事项
        List<FeeItemExpenseTypeDto> feeItemExpenseTypeDtoList = projectTypeService.selectFeeItemExpenseType(project.getType());
        /**
         * 变更前WBS预算信息
         *
         * 新增记录：
         * wbs预算：originId = 原id
         * wbs动态：originId = 原id ； ProjectWbsBudgetId = 不变
         *
         * 变更记录：
         * wbs预算：originId = 原id
         * wbs动态：originId = 原id ； ProjectWbsBudgetId = 不变
         */
        /* wbs预算 - 更新前 */
        ProjectWbsBudgetChangeHistory history;
        ProjectWbsBudgetExample example1 = new ProjectWbsBudgetExample();
        example1.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(project.getId());
        List<ProjectWbsBudget> projectWbsBudgetList = projectWbsBudgetMapper.selectByExample(example1);
        if (CollectionUtils.isNotEmpty(projectWbsBudgetList)) {
            List<ProjectWbsBudgetChangeHistory> batchHistoryList = new ArrayList<>();
            for (ProjectWbsBudget projectWbsBudget : projectWbsBudgetList) {
                history = BeanConverter.copyProperties(projectWbsBudget, ProjectWbsBudgetChangeHistory.class);
                history.setOriginId(projectWbsBudget.getId());
                history.setHeaderId(headerId);
                history.setHistoryType(HistoryType.HISTORY.getCode());
                history.setDeletedFlag(Boolean.FALSE);
                history.setId(null);
                if (idList.contains(projectWbsBudget.getId())) {
                    history.setShowCase(true);
                } else {
                    history.setShowCase(false);
                }
                batchHistoryList.add(history);
            }
            // 批量插入
            if (CollectionUtils.isNotEmpty(batchHistoryList)) {
                projectWbsBudgetChangeHistoryService.batchInsert(batchHistoryList, 1000);
            }
        }
        /* 动态列 - 更新前 */
        ProjectWbsBudgetDynamicChangeHistory historyDynamic;
        ProjectWbsBudgetDynamicExample example2 = new ProjectWbsBudgetDynamicExample();
        example2.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(project.getId());
        List<ProjectWbsBudgetDynamic> projectWbsBudgetDynamicList = projectWbsBudgetDynamicMapper.selectByExample(example2);
        if (!CollectionUtils.isEmpty(projectWbsBudgetDynamicList)) {
            List<ProjectWbsBudgetDynamicChangeHistory> batchDynamicHistoryList = new ArrayList<>();
            for (ProjectWbsBudgetDynamic projectWbsBudgetDynamic : projectWbsBudgetDynamicList) {
                historyDynamic = BeanConverter.copyProperties(projectWbsBudgetDynamic, ProjectWbsBudgetDynamicChangeHistory.class);
                historyDynamic.setOriginId(projectWbsBudgetDynamic.getId());
                historyDynamic.setHeaderId(headerId);
                historyDynamic.setHistoryType(HistoryType.HISTORY.getCode());
                historyDynamic.setProjectId(project.getId());
                historyDynamic.setDeletedFlag(Boolean.FALSE);
                historyDynamic.setId(null);
                batchDynamicHistoryList.add(historyDynamic);
            }
            // 批量插入
            if (CollectionUtils.isNotEmpty(batchDynamicHistoryList)) {
                projectWbsBudgetDynamicChangeHistoryService.batchInsert(batchDynamicHistoryList, 1000);
            }
        }

        /**
         * 变更后WBS预算信息
         *
         * 新增记录：
         * wbs预算：originId = null
         * wbs动态：originId = null ； ProjectWbsBudgetId = wbs预算变更.id
         *
         * 变更记录：
         * wbs预算：originId = 原id
         * wbs动态：originId = 原id ； ProjectWbsBudgetId = wbs预算变更.id
         */
        /* wbs预算(前端选中的记录) - 更新后 */
        List<WbsDynamicFieldsDto> wbsDynamicFields = wbsTemplateRuleService.getWbsDynamicFields(project.getWbsTemplateInfoId());
        // 记录所有变更后（包括未选中的记录）
        List<ProjectWbsBudgetChangeHistory> changeSaveAll = new ArrayList<>();
        ProjectWbsBudgetChangeHistory change;
        ProjectWbsBudgetDynamicChangeHistory changeDynamic;
        List<ProjectWbsBudgetChangeHistory> batchChangeList = new ArrayList<>();
        // 选中的WBS编码
        Set<String> choseWbsFullCodeSet = new HashSet<>();
        // WBS规则缓存
        List<WbsTemplateRuleCache> wbsCacheList = ProjectWbsBudgetUtils.getEligibilityWbsCache(project.getWbsTemplateInfoId());

        // wbs规则详情缓存Map（存起来减少redis交互）
        Map<String, WbsTemplateRuleDetailCache> wbsCacheMap = new HashMap<>();
        // 本地存放activityCache，减少重复查询
        Map<String, ProjectActivityCache> activityCacheMap = new HashMap<>();

        for (Map<String, Object> map : projectWbsBudgetChangeHistorys) {
            // 校验值有效性，设置默认值
            validityAndInit(map, wbsCacheList, activityCacheMap, wbsCacheMap);
            try {
                change = ProjectWbsBudgetChangeHistoryDto.map2Entity(map, wbsDynamicFields);
                choseWbsFullCodeSet.add(change.getWbsFullCode());
            } catch (Exception e) {
                throw new ApplicationBizException("map转换entity异常：" + JSON.toJSONString(map));
            }

            // 变更后预算金额
            String afterChangePriceStr = MapUtils.getString(map, WbsBudgetFieldConstant.AFTER_CHANGE_PRICE);
            if (null == afterChangePriceStr) {
                throw new ApplicationBizException("变更后预算金额不能为空");
            }

            BigDecimal afterChangePrice = new BigDecimal(afterChangePriceStr);
            change.setPrice(afterChangePrice);
            if (null == change.getBaselineCost()) {
                // 预算基线
                change.setBaselineCost(BigDecimal.ZERO);
            }
            if (null == change.getDemandCost()) {
                // 需求预算
                change.setDemandCost(BigDecimal.ZERO);
            }
            if (null == change.getOnTheWayCost()) {
                // 在途成本
                change.setOnTheWayCost(BigDecimal.ZERO);
            }
            if (null == change.getIncurredCost()) {
                // 已发生成本
                change.setIncurredCost(BigDecimal.ZERO);
            }

            /**
             * 变更后预算不能 < 已发生成本 + 在途成本
             */
            if (BigDecimal.ZERO.compareTo(afterChangePrice.subtract(change.getOnTheWayCost()).subtract(change.getIncurredCost())) > 0) {
                throw new ApplicationBizException(String.format("WBS：%s，活动事项%s，更后预算不能小于已发生成本+在途成本", change.getWbsFullCode(), change.getActivityCode()));
            }

            // 变更后剩余可用预算 = 变更后预算金额 - 需求预算 - 在途成本 - 已发生成本
            BigDecimal remainingCost = afterChangePrice.subtract(change.getDemandCost()).subtract(change.getOnTheWayCost()).subtract(change.getIncurredCost());
            change.setRemainingCost(remainingCost);

            // 累计变更金额 = 变更后预算金额 - 预算基线
            BigDecimal changeAccumulateCost = afterChangePrice.subtract(change.getBaselineCost());
            change.setChangeAccumulateCost(changeAccumulateCost);

            Long originId = change.getId();
            change.setOriginId(originId);
            change.setProjectId(project.getId());
            change.setHeaderId(headerId);
            change.setHistoryType(HistoryType.CHANGE.getCode());
            change.setShowCase(true);
            change.setDeletedFlag(change.getDeletedFlag() == null ? Boolean.FALSE : change.getDeletedFlag());
            change.setId(null);
            if (null == originId) {
                // 全新的预算设置经济事项
                setFeeExpenseType(feeItemExpenseTypeDtoList, change);
            }
            batchChangeList.add(change);
            changeSaveAll.add(change);
        }
        // 保存选中行的动态列
        if (CollectionUtils.isNotEmpty(batchChangeList)) {
            /* 批量插入预算 */
            projectWbsBudgetChangeHistoryService.batchInsert(batchChangeList, 1000);
            for (ProjectWbsBudgetChangeHistory batchCHange : batchChangeList) {
                List<ProjectWbsBudgetDynamicChangeHistory> batchDynamicHistoryList = new ArrayList<>();
                /* 动态列(前端选中的记录-新增) - 更新后 */
                if (null == batchCHange.getOriginId()) {
                    if (!CollectionUtils.isEmpty(wbsDynamicFields)) {
                        for (WbsDynamicFieldsDto dynamicField : wbsDynamicFields) {
                            Map batchChangeMap = ProjectWbsBudgetChangeHistoryDto.entity2Map(batchCHange);
                            changeDynamic = new ProjectWbsBudgetDynamicChangeHistory();
                            changeDynamic.setProjectWbsBudgetId(batchCHange.getId());
                            changeDynamic.setWbsTemplateRuleId(dynamicField.getWbsTemplateRuleId());
                            changeDynamic.setFieldName(dynamicField.getKey());
                            changeDynamic.setWbsTemplateRuleDetailCode(MapUtils.getString(batchChangeMap, dynamicField.getKey()));
                            changeDynamic.setOriginId(null);
                            changeDynamic.setProjectId(project.getId());
                            changeDynamic.setHeaderId(headerId);
                            changeDynamic.setHistoryType(HistoryType.CHANGE.getCode());
                            changeDynamic.setDeletedFlag(batchCHange.getDeletedFlag() == null ? Boolean.FALSE : batchCHange.getDeletedFlag());
                            changeDynamic.setId(null);
                            batchDynamicHistoryList.add(changeDynamic);
                        }
                    }
                }
                /* 动态列(前端选中的记录-修改) - 更新后 */
                else {
                    List<ProjectWbsBudgetDynamic> filterWbsBudgetDynamicList = projectWbsBudgetDynamicList.stream().filter(a -> a.getProjectWbsBudgetId().equals(batchCHange.getOriginId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filterWbsBudgetDynamicList)) {
                        for (ProjectWbsBudgetDynamic wbsBudgetDynamic : filterWbsBudgetDynamicList) {
                            changeDynamic = BeanConverter.copyProperties(wbsBudgetDynamic, ProjectWbsBudgetDynamicChangeHistory.class);
                            changeDynamic.setProjectWbsBudgetId(batchCHange.getId());
                            changeDynamic.setOriginId(changeDynamic.getId());
                            changeDynamic.setProjectId(project.getId());
                            changeDynamic.setHeaderId(headerId);
                            changeDynamic.setHistoryType(HistoryType.CHANGE.getCode());
                            changeDynamic.setVersion(1L);
                            changeDynamic.setDeletedFlag(batchCHange.getDeletedFlag() == null ? Boolean.FALSE : batchCHange.getDeletedFlag());
                            changeDynamic.setId(null);
                            batchDynamicHistoryList.add(changeDynamic);
                        }
                    }
                }
                /* 批量插入动态列 */
                if (CollectionUtils.isNotEmpty(batchDynamicHistoryList)) {
                    projectWbsBudgetDynamicChangeHistoryService.batchInsert(batchDynamicHistoryList, 1000);
                }
            }
        }

        // 保存前端没有选中的记录
        if (!CollectionUtils.isEmpty(projectWbsBudgetList)) {
            batchChangeList = new ArrayList<>();
            /* wbs预算(前端没有选中的记录) - 更新后 */
            for (ProjectWbsBudget projectWbsBudget : projectWbsBudgetList) {
                if (idList.contains(projectWbsBudget.getId())) {
                    continue;
                }
                change = BeanConverter.copyProperties(projectWbsBudget, ProjectWbsBudgetChangeHistory.class);
                change.setOriginId(projectWbsBudget.getId());
                change.setHeaderId(headerId);
                change.setHistoryType(HistoryType.CHANGE.getCode());
                change.setDeletedFlag(Boolean.FALSE);
                change.setId(null);
                change.setShowCase(false);
                batchChangeList.add(change);
                changeSaveAll.add(change);
            }
            if (CollectionUtils.isNotEmpty(batchChangeList)) {
                /* 批量插入预算 */
                projectWbsBudgetChangeHistoryService.batchInsert(batchChangeList, 1000);
                for (ProjectWbsBudgetChangeHistory batchCHange : batchChangeList) {
                    /* 动态列(前端没有选中的记录) - 更新后 */
                    List<ProjectWbsBudgetDynamic> filterWbsBudgetDynamicList = projectWbsBudgetDynamicList.stream().filter(a -> a.getProjectWbsBudgetId().equals(batchCHange.getOriginId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filterWbsBudgetDynamicList)) {
                        List<ProjectWbsBudgetDynamicChangeHistory> batchDynamicHistoryList = new ArrayList<>();
                        for (ProjectWbsBudgetDynamic wbsBudgetDynamic : filterWbsBudgetDynamicList) {
                            changeDynamic = BeanConverter.copyProperties(wbsBudgetDynamic, ProjectWbsBudgetDynamicChangeHistory.class);
                            changeDynamic.setProjectWbsBudgetId(batchCHange.getId());
                            changeDynamic.setOriginId(changeDynamic.getId());
                            changeDynamic.setProjectId(project.getId());
                            changeDynamic.setHeaderId(headerId);
                            changeDynamic.setHistoryType(HistoryType.CHANGE.getCode());
                            changeDynamic.setVersion(1L);
                            changeDynamic.setDeletedFlag(batchCHange.getDeletedFlag() == null ? Boolean.FALSE : batchCHange.getDeletedFlag());
                            changeDynamic.setId(null);
                            batchDynamicHistoryList.add(changeDynamic);
                        }
                        /* 批量插入动态列 */
                        if (CollectionUtils.isNotEmpty(batchDynamicHistoryList)) {
                            projectWbsBudgetDynamicChangeHistoryService.batchInsert(batchDynamicHistoryList, 1000);
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(changeSaveAll)) {
            // 选中的WBS维度校验：wbs维度变更后预算不能 < 已发生成本+在途成本+需求预算
            if (!choseWbsFullCodeSet.isEmpty()) {
                for (String wbsFullCode : choseWbsFullCodeSet) {
                    List<ProjectWbsBudgetChangeHistory> filterList = changeSaveAll.stream().filter(a -> a.getWbsFullCode().equals(wbsFullCode)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filterList)) {
                        // 预算金额
                        BigDecimal sumPrice = filterList.stream().map(e -> Optional.ofNullable(e.getPrice()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 在途成本
                        BigDecimal sumOnTheWayCost = filterList.stream().map(e -> Optional.ofNullable(e.getOnTheWayCost()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 已发生成本
                        BigDecimal sumIncurredCost = filterList.stream().map(e -> Optional.ofNullable(e.getIncurredCost()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 需求预算
                        BigDecimal sumDemandCost = filterList.stream().map(e -> Optional.ofNullable(e.getDemandCost()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (BigDecimal.ZERO.compareTo(sumPrice.subtract(sumIncurredCost).subtract(sumOnTheWayCost).subtract(sumDemandCost)) > 0) {
                            throw new ApplicationBizException(String.format("WBS：%s，总预算%s不能小于(已发生成本+在途成本+需求预算)%s",
                                    wbsFullCode,
                                    sumPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString(),
                                    sumOnTheWayCost.add(sumIncurredCost).add(sumDemandCost).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString()));
                        }
                    }
                }
            }

            List<Map> changeSaveMapList = ProjectWbsBudgetChangeHistoryDto.entity2MapBatch(changeSaveAll);
            // 校验同一个WBS下面，同一个经济事项不允许重复
            List<String> errorMsgList = projectWbsBudgetCheckHelpper.checkWbsRepeatFeeType(changeSaveMapList, project.getWbsTemplateInfoId(), SystemContext.getUnitId());
            if (CollectionUtils.isNotEmpty(errorMsgList)) {
                // 20220704达浩确认这种报错1条1条报
                throw new ApplicationBizException(errorMsgList.get(0));
            }
        }
    }

    /**
     * 设置经济事项
     *
     * @param feeItemExpenseTypeDtoList
     * @param wbsBudget
     */
    private void setFeeExpenseType(List<FeeItemExpenseTypeDto> feeItemExpenseTypeDtoList, ProjectWbsBudgetChangeHistory wbsBudget) {
        if (CollectionUtils.isNotEmpty(feeItemExpenseTypeDtoList)) {
            // wbs经济事项
            List<FeeItemExpenseTypeDto> wbsFeeItems = feeItemExpenseTypeDtoList.stream().filter(a -> Objects.equals(a.getName(), wbsBudget.getWbsLastCode()) && Objects.equals(a.getFeeSettingMode(), FeeSettingModeEnum.WBS.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(wbsFeeItems)) {
                FeeItemExpenseTypeDto wbsFeeItem = wbsFeeItems.get(0);
                wbsBudget.setFeeTypeId(wbsFeeItem.getFeeTypeId());
                wbsBudget.setFeeTypeName(wbsFeeItem.getFeeTypeName());
                wbsBudget.setFeeSyncEms(Boolean.TRUE.equals(wbsFeeItem.getSyncEMS()));
                return;
            }

            // activity经济事项
            List<FeeItemExpenseTypeDto> activityFeeItems = feeItemExpenseTypeDtoList.stream().filter(a -> Objects.equals(a.getName(), wbsBudget.getActivityCode()) && Objects.equals(a.getFeeSettingMode(), FeeSettingModeEnum.PROJECT_ACTIVITY.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(activityFeeItems)) {
                FeeItemExpenseTypeDto activityFeeItem = activityFeeItems.get(0);
                wbsBudget.setFeeTypeId(activityFeeItem.getFeeTypeId());
                wbsBudget.setFeeTypeName(activityFeeItem.getFeeTypeName());
                wbsBudget.setFeeSyncEms(Boolean.TRUE.equals(activityFeeItem.getSyncEMS()));
                return;
            }
        }
    }

    /**
     * 检查变更数据有效性，设置默认值
     *
     * @param map
     * @param wbsCacheList
     * @param activityCacheMap 本地存放activityCache，减少重复查询
     * @param wbsCacheMap      wbs规则详情缓存Map（存起来减少redis交互）
     */
    private void validityAndInit(Map<String, Object> map, List<WbsTemplateRuleCache> wbsCacheList, Map<String, ProjectActivityCache> activityCacheMap, Map<String, WbsTemplateRuleDetailCache> wbsCacheMap) {

        // 只有新增才需要校验并赋值
        if (null != map.get("id")) {
            return;
        }

        /**
         * 活动事项校验
         * 1、code必须存在
         * 2、预算事项=true
         */
        String activityCode = MapUtils.getString(map, WbsBudgetFieldConstant.ACTIVITY_CODE);
        ProjectActivityCache activityCache = ProjectActivityCacheUtils.getCache(SystemContext.getUnitId(), activityCode);
        String error1 = projectWbsBudgetCheckHelpper.checkActivityValid(activityCache, activityCode);
        if (StringUtils.isNotBlank(error1)) {
            throw new ApplicationBizException(error1);
        }

        /**
         * wbs是否严控校验
         * 1、code必须存在
         * 2、层级必须=明细
         * 3、code未失效
         */
        String error2 = projectWbsBudgetCheckHelpper.checkWbsValid(map, wbsCacheList, wbsCacheMap);
        if (StringUtils.isNotBlank(error2)) {
            throw new ApplicationBizException(error2);
        }

        /**
         * 1、activity 类别属性 = "预算"才需要校验经济事项
         * 2、wbs + activity 预算必须绑定一个且只能绑定一个经济事项
         *
         * @param unitId            单位id
         * @param map               预算记录
         * @param wbsTemplateInfoId wbs模板id
         * @return 错误消息
         */
        String error3 = projectWbsBudgetCheckHelpper.checkWbsActivityByOnlyOneFeeType(map, activityCacheMap, wbsCacheList, SystemContext.getUnitId());
        if (StringUtils.isNotBlank(error3)) {
            throw new ApplicationBizException(error3);
        }

        // 活动事项
        map.put("activityName", activityCache.getName());
        map.put("activityOrderNo", activityCache.getOrderNo());
        map.put("activityType", activityCache.getType());
    }

    /**
     * 项目变更汇总记录保存
     *
     * @param history
     * @param projectId
     * @param headerId
     */
    private void budgetSummaryChangeSave(ProjectBudgetChangeSummaryHistory history, Long projectId, Long headerId) {
        history.setId(null);
        history.setHeaderId(headerId);
        history.setProjectId(projectId);
        history.setDeletedFlag(Boolean.FALSE);
        projectBudgetChangeSummaryHistoryService.insert(history);
    }

    private Long saveOrUpdateChangeRecord(ProjectWbsBudgetChangeDTO projectWbsBudgetChangeDTO) {
        final Long projectId = projectWbsBudgetChangeDTO.getProjectId();
        Long headerId = projectWbsBudgetChangeDTO.getHeaderId();

        // 变更记录，删除原有的数据，重新保存一次
        if (headerId != null) {
            final ProjectHistoryHeader localProjectHistoryHeader = new ProjectHistoryHeader();
            localProjectHistoryHeader.setId(headerId);
            localProjectHistoryHeader.setChangeType(CtcProjectChangeType.WBS_BUDGET.getCode());
            localProjectHistoryHeader.setProjectId(projectId);
            localProjectHistoryHeader.setReason(projectWbsBudgetChangeDTO.getReason());
            localProjectHistoryHeader.setReasonType(projectWbsBudgetChangeDTO.getReasonType());
            localProjectHistoryHeader.setStatus(ProjectChangeStatus.DRAFT.getCode());
            projectHistoryHeaderMapper.updateByPrimaryKeySelective(localProjectHistoryHeader);
            // 第一步保存新增
        } else {
            ProjectHistoryHeader newProjectHistoryHeader = new ProjectHistoryHeader();
            newProjectHistoryHeader.setChangeType(CtcProjectChangeType.WBS_BUDGET.getCode());
            newProjectHistoryHeader.setProjectId(projectId);
            newProjectHistoryHeader.setReason(projectWbsBudgetChangeDTO.getReason());
            newProjectHistoryHeader.setReasonType(projectWbsBudgetChangeDTO.getReasonType());
            newProjectHistoryHeader.setStatus(ProjectChangeStatus.DRAFT.getCode());
            newProjectHistoryHeader.setDeletedFlag(Boolean.FALSE);
            // 和WBS变更单建立关联关系
            newProjectHistoryHeader.setProjectWbsReceiptsId(projectWbsBudgetChangeDTO.getProjectWbsReceiptsId());
            projectHistoryHeaderMapper.insert(newProjectHistoryHeader);

            headerId = newProjectHistoryHeader.getId();
            projectWbsBudgetChangeDTO.setHeaderId(headerId);
        }
        return headerId;
    }

    /**
     * wbs预算变更 - 审批通过更新数据
     *
     * @param headerId
     */
    @Override
    public Long changeSuccess(Long headerId) {
        logger.info("wbs预算变更开始：" + headerId);
        final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(headerId);
        Assert.notNull(projectHistoryHeader, "变更记录不存在");
        projectHistoryHeader.setStatus(ProjectChangeStatus.APPROVALED.getCode());
        projectHistoryHeaderMapper.updateByPrimaryKeySelective(projectHistoryHeader);

        final Long projectId = projectHistoryHeader.getProjectId();
        final Long createBy = projectHistoryHeader.getCreateBy();

        // 将变更记录写入正式表
        handleChangeWbsBudget(headerId, projectId, createBy);
        // 重新处理汇总表
        projectWbsBudgetSummaryService.saveWbsBudgetSummary(projectId);
        // 处理附件信息
        List<CtcAttachmentDto> attachmentList = findCtcAttachmentDto(CtcAttachmentModule.PROJECT_WBS_BUDGET_CHANGE.code(), headerId);
        saveCtcAttachmentDtos(AttachmentStatus.PASSED.code(), attachmentList, CtcAttachmentModule.PROJECT_WBS_BUDGET_CHANGE.code(), headerId);

        logger.info("wbs预算变更结束：" + headerId);
        return headerId;
    }

    /**
     * 将变更记录写入正式表
     *
     * @param headerId
     * @param projectId
     * @param updateBy
     */
    private void handleChangeWbsBudget(Long headerId, Long projectId, Long updateBy) {

        // wbs预算变更前
        ProjectWbsBudgetExample example = new ProjectWbsBudgetExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectIdEqualTo(projectId);
        List<ProjectWbsBudget> projectWbsBudgetList = projectWbsBudgetMapper.selectByExample(example);
        // key=wbs+activity
        Map<String, ProjectWbsBudget> oldProjectWbsBudgetMap = projectWbsBudgetList.stream()
                .collect(Collectors.toMap(projectWbsBudget -> projectWbsBudget.getWbsFullCode() + "-" + projectWbsBudget.getActivityCode(), Function.identity(), (key1, key2) -> key2));

        // wbs预算变更后
        List<ProjectWbsBudgetChangeHistory> afterBudgetHistoryList = projectWbsBudgetChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);

        // wbs预算动态列变更后
        List<ProjectWbsBudgetDynamicChangeHistory> afterDynamiceHistoryList = projectWbsBudgetDynamicChangeHistoryService.getHistoryByHeaderIdAndType(headerId, HistoryType.CHANGE, null);

        // wbs预算动态列变更前
        ProjectWbsBudgetDynamicExample dynamicExample = new ProjectWbsBudgetDynamicExample();
        dynamicExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(projectId);
        List<ProjectWbsBudgetDynamic> beforeDynamiceList = projectWbsBudgetDynamicMapper.selectByExample(dynamicExample);

        if (!CollectionUtils.isEmpty(afterBudgetHistoryList)) {
            Long originId;
            for (ProjectWbsBudgetChangeHistory afterBudgetHistory : afterBudgetHistoryList) {
                // 《删除》
                if (Boolean.TRUE.equals(afterBudgetHistory.getDeletedFlag()) && !Objects.isNull(afterBudgetHistory.getOriginId())) {
                    // 删除wbs预算
                    ProjectWbsBudget wbsBudget = new ProjectWbsBudget();
                    wbsBudget.setId(afterBudgetHistory.getOriginId());
                    wbsBudget.setDeletedFlag(true);
                    projectWbsBudgetMapper.updateByPrimaryKeySelective(wbsBudget);
                    // 删除动态列
                    List<ProjectWbsBudgetDynamic> filterDynamicList = beforeDynamiceList.stream().filter(a -> a.getProjectWbsBudgetId().equals(wbsBudget.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(filterDynamicList)) {
                        for (ProjectWbsBudgetDynamic dynamic : filterDynamicList) {
                            ProjectWbsBudgetDynamic wbsBudgetDynamic = new ProjectWbsBudgetDynamic();
                            wbsBudgetDynamic.setId(dynamic.getId());
                            wbsBudgetDynamic.setDeletedFlag(true);
                            projectWbsBudgetDynamicMapper.updateByPrimaryKeySelective(wbsBudgetDynamic);
                        }
                    }
                } else {
                    originId = afterBudgetHistory.getOriginId();
                    ProjectWbsBudget afterBudget = BeanConverter.copyProperties(afterBudgetHistory, ProjectWbsBudget.class);
                    // 《更新》
                    if (originId != null) {
                        afterBudget.setId(originId);
                        // 不要覆盖创建人创建时间
                        ProjectWbsBudget dbBudget = projectWbsBudgetMapper.selectByPrimaryKey(originId);
                        afterBudget.setCreateAt(dbBudget.getCreateAt());
                        afterBudget.setCreateBy(dbBudget.getCreateBy());
                        projectWbsBudgetMapper.updateByPrimaryKey(afterBudget);
                        /* 涉及变更改动，将预算的描述同步到基线 */
                        if (Boolean.TRUE.equals(afterBudgetHistory.getShowCase())) {
                            ProjectWbsBudgetBaselineExample baselineExample = new ProjectWbsBudgetBaselineExample();
                            baselineExample.createCriteria().andDeletedFlagEqualTo(false).andProjectWbsBudgetIdEqualTo(originId);
                            List<ProjectWbsBudgetBaseline> baselineList = projectWbsBudgetBaselineMapper.selectByExample(baselineExample);
                            if (CollectionUtils.isNotEmpty(baselineList)) {
                                for (ProjectWbsBudgetBaseline baseline : baselineList) {
                                    // 描述如果有改动则同步
                                    if (!Objects.equals(baseline.getDescription(), afterBudget.getDescription())) {
                                        baseline.setDescription(afterBudget.getDescription());
                                        projectWbsBudgetBaselineMapper.updateByPrimaryKey(baseline);
                                    }
                                }
                            }
                        }
                        // 变更动态列
                        List<ProjectWbsBudgetDynamicChangeHistory> filterDynamicList = afterDynamiceHistoryList.stream().filter(a -> a.getProjectWbsBudgetId().equals(afterBudgetHistory.getId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(filterDynamicList)) {
                            for (ProjectWbsBudgetDynamicChangeHistory dynamicChangeHistory : filterDynamicList) {
                                ProjectWbsBudgetDynamic afterDynamic = BeanConverter.copyProperties(dynamicChangeHistory, ProjectWbsBudgetDynamic.class);
                                afterDynamic.setProjectWbsBudgetId(afterBudget.getId());
                                afterDynamic.setId(dynamicChangeHistory.getOriginId());
                                projectWbsBudgetDynamicMapper.updateByPrimaryKey(afterDynamic);
                            }
                        }
                    }
                    // 《新增》
                    else {
                        // BUG2024041801871 PAM-运维-高-WBS项目，同一个WBS+活动事项的预算重复生成
                        ProjectWbsBudget projectWbsBudget = oldProjectWbsBudgetMap.getOrDefault((afterBudgetHistory.getWbsFullCode() + "-" + afterBudgetHistory.getActivityCode()), null);
                        if (Objects.nonNull(projectWbsBudget)) {
                            projectWbsBudget.setPrice(BigDecimalUtils.add(projectWbsBudget.getPrice(), afterBudget.getPrice()));
                            projectWbsBudget.setBaselineCost(BigDecimalUtils.add(projectWbsBudget.getBaselineCost(), afterBudget.getBaselineCost()));
                            projectWbsBudget.setRemainingCost(BigDecimalUtils.add(projectWbsBudget.getRemainingCost(), afterBudget.getRemainingCost()));
                            projectWbsBudgetMapper.updateByPrimaryKey(projectWbsBudget);
                        } else {
                            afterBudget.setId(null);
                            afterBudget.setVersion(1L);
                            afterBudget.setCreateBy(updateBy);
                            afterBudget.setUpdateAt(null);
                            afterBudget.setUpdateBy(null);
                            projectWbsBudgetMapper.insertSelective(afterBudget);
                            // 变更动态列
                            List<ProjectWbsBudgetDynamicChangeHistory> filterDynamicList = afterDynamiceHistoryList.stream().filter(a -> a.getProjectWbsBudgetId().equals(afterBudgetHistory.getId())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(filterDynamicList)) {
                                for (ProjectWbsBudgetDynamicChangeHistory dynamicChangeHistory : filterDynamicList) {
                                    ProjectWbsBudgetDynamic afterDynamic = BeanConverter.copyProperties(dynamicChangeHistory, ProjectWbsBudgetDynamic.class);
                                    afterDynamic.setProjectWbsBudgetId(afterBudget.getId());
                                    afterDynamic.setId(null);
                                    projectWbsBudgetDynamicMapper.insertSelective(afterDynamic);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 参数校验
     *
     * @param projectWbsBudgetChangeDTO 入参
     */
    private void wbsBudgetChangeParamCheck(ProjectWbsBudgetChangeDTO projectWbsBudgetChangeDTO) {
        final Long projectId = projectWbsBudgetChangeDTO.getProjectId();
        final String reason = projectWbsBudgetChangeDTO.getReason();
        Assert.notNull(projectId, "项目不能为空");
        Assert.notNull(reason, "变更原因不能为空");
        Assert.isTrue(reason.length() < 1024, "变更原因长度不能超过1024");

        // 判断金额的阀值
        BigDecimal max = new BigDecimal("999999999999");

        final ProjectBudgetChangeSummaryHistory projectBudgetChangeSummaryHistory = projectWbsBudgetChangeDTO.getProjectBudgetChangeSummaryHistory();

        if (projectBudgetChangeSummaryHistory != null) {
            final BigDecimal afterAmount = projectBudgetChangeSummaryHistory.getAfterAmount();
            final BigDecimal beforeAmount = projectBudgetChangeSummaryHistory.getBeforeAmount();
            final BigDecimal changeAmount = projectBudgetChangeSummaryHistory.getChangeAmount();

            if (afterAmount != null) {
                Assert.isTrue(max.compareTo(afterAmount) > 0, "项目总预算超出金额上限999,999,999,999，请联系系统管理员");
            }

            if (beforeAmount != null) {
                Assert.isTrue(max.compareTo(beforeAmount) > 0, "项目总预算超出金额上限999,999,999,999，请联系系统管理员");
            }

            if (changeAmount != null) {
                Assert.isTrue(max.compareTo(changeAmount) > 0, "项目总预算超出金额上限999,999,999,999，请联系系统管理员");
            }
        }

        if (CollectionUtils.isEmpty(projectWbsBudgetChangeDTO.getProjectWbsBudgetChangeHistories())) {
            throw new ApplicationBizException("变更wbs预算不能为空");
        }
    }

    /**
     * 权限校验
     *
     * @param project
     */
    private void authCheck(Project project) {
        final Long managerId = project.getManagerId();
        final Long financial = project.getFinancial();
        final Long userId = SystemContext.getUserId();
        Unit unit = unitExtMapper.selectByPrimaryKey(project.getUnitId());
        OrganizationCustomDictExample ocdExample = new OrganizationCustomDictExample();
        ocdExample.createCriteria().andDeletedFlagEqualTo(false).andOrgIdEqualTo(unit.getParentId()).andNameEqualTo("项目财务是否允许预算变更");
        List<OrganizationCustomDict> organizationCustomDictList = organizationCustomDictMapper.selectByExample(ocdExample);
        if (CollectionUtils.isEmpty(organizationCustomDictList) || !Objects.equals("1", organizationCustomDictList.get(0).getValue())) {
            Assert.isTrue(Objects.equals(managerId, userId), "只有项目经理可以发起项目预算变更");
        } else {
            Assert.isTrue(Objects.equals(managerId, userId) || Objects.equals(financial, userId), "只有项目经理或者项目财务可以发起项目预算变更");
        }
        final Integer status = project.getStatus();
        Assert.isTrue(Objects.equals(status, ProjectStatus.APPROVALED.getCode()), "项目进行中才能发起项目变更");
    }

    /**
     * 构建附件信息
     */
    private List<CtcAttachmentDto> findCtcAttachmentDto(Integer module, Long moduleId) {
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(module);
        attachmentQuery.setModuleId(moduleId);
        attachmentQuery.setDeletedFlag(false);
        List<CtcAttachmentDto> ctcAttachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
        if (CollectionUtils.isEmpty(ctcAttachmentDtos)) {
            return Collections.emptyList();
        }
        return ctcAttachmentDtos;
    }

    /**
     * 保存附件信息
     *
     * @param status         AttachmentStatus.CHECKING.code()
     * @param attachmentDtos
     * @param module
     * @param moduleId
     */
    private void saveCtcAttachmentDtos(Integer status, List<CtcAttachmentDto> attachmentDtos, Integer module, Long moduleId) {
        if (ListUtil.isPresent(attachmentDtos)) {
            for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                if (AttachmentStatus.PASSED.code().equals(attachmentDto.getStatus())) {
                    continue;
                }
                attachmentDto.setModule(module);
                attachmentDto.setModuleId(moduleId);
                attachmentDto.setStatus(status);
                this.ctcAttachmentService.save(attachmentDto, SystemContext.getUserId());
            }
        }
    }

    /**
     * 移动审批获取WBS预算变更
     *
     * @param formInstanceId 变更信息
     * @return
     */
    @Override
    public ResponseMap getProjectWbsBudgetChangeApp(Long formInstanceId) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();

        // wbs预算变更列表
        List<Map<String, String>> wbsMapList = new ArrayList<>();

        final Long headerId = formInstanceId;
        final ProjectHistoryHeader projectHistoryHeader = projectHistoryHeaderMapper.selectByPrimaryKey(headerId);
        Project project = projectService.selectByPrimaryKey(projectHistoryHeader.getProjectId());
        Asserts.notEmpty(projectHistoryHeader, ErrorCode.CTC_PROJECT_NOT_FIND);
        final ProjectBudgetChangeSummaryHistory summaryHistory = projectBudgetChangeSummaryHistoryService.getByHeaderId(headerId);
        Asserts.notEmpty(summaryHistory, ErrorCode.CTC_CHANGE_RECORD_NOT_FIND);

        //客户信息
        headMap.put("name", project.getName()); //项目名称
        headMap.put("code", project.getCode()); // 项目编号
        headMap.put("type", "预算变更"); // 变更类型
        headMap.put("currency", project.getCurrency()); // 币种
        headMap.put("reasonType", projectHistoryHeader.getReasonType());//变更原因
        headMap.put("reason", projectHistoryHeader.getReason());//变更说明

        if (Boolean.TRUE.equals(project.getWbsEnabled())) {
            ProjectWbsBudgetChangeVO param = new ProjectWbsBudgetChangeVO();
            param.setRefuseType(1);
            // 变更内容--wbs预算
            ProjectWbsBudgetChangeVO changeVO = getWbsBudgetHistory(headerId, param);

            if (null != changeVO.getSummaryHistory()) {
                // 调整差异总额
                BigDecimal changeAmount = null == summaryHistory.getChangeAmount() ? BigDecimal.ZERO : summaryHistory.getChangeAmount();
                // 项目不含税金额（本位币）
                BigDecimal excludingTaxAmount = projectExtMapper.getProjectContractStandardAmount(project.getId());
                // 变更前-项目总预算
                BigDecimal beforeProjectBudget = null == summaryHistory.getBeforeAmount() ? BigDecimal.ZERO : summaryHistory.getBeforeAmount();
                // 变更前-毛利额 = 项目金额（不含税） - 变更前-项目总预算
                BigDecimal beforeProfitWithoutTax = excludingTaxAmount.subtract(beforeProjectBudget);
                // 变更前-毛利率 = 变更前-毛利额 / 项目金额（不含税）
                BigDecimal beforeProfitPercentWithoutTax = BigDecimal.ZERO;
                if (beforeProfitWithoutTax.compareTo(BigDecimal.ZERO) != 0 && excludingTaxAmount.compareTo(BigDecimal.ZERO) != 0) {
                    beforeProfitPercentWithoutTax = beforeProfitWithoutTax.divide(excludingTaxAmount, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                }
                // 变更后-项目总预算
                BigDecimal afterProjectBudget = null == summaryHistory.getAfterAmount() ? BigDecimal.ZERO : summaryHistory.getAfterAmount();
                // 变更后-毛利额 = 项目金额（不含税） - 变更后-项目总预算
                BigDecimal afterProfitWithoutTax = excludingTaxAmount.subtract(afterProjectBudget);
                // 变更后-毛利率 = 变更后-毛利额 / 项目金额（不含税）
                BigDecimal afterProfitPercentWithoutTax = BigDecimal.ZERO;
                if (afterProfitWithoutTax.compareTo(BigDecimal.ZERO) != 0 && excludingTaxAmount.compareTo(BigDecimal.ZERO) != 0) {
                    afterProfitPercentWithoutTax = afterProfitWithoutTax.divide(excludingTaxAmount, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                }

                headMap.put("changePrice", changeAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
                headMap.put("beforeProjectBudget", beforeProjectBudget.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
                headMap.put("beforeProfitWithoutTax", beforeProfitWithoutTax.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
                headMap.put("beforeProfitPercentWithoutTax", beforeProfitPercentWithoutTax.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                headMap.put("afterProjectBudget", afterProjectBudget.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
                headMap.put("afterProfitWithoutTax", afterProfitWithoutTax.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
                headMap.put("afterProfitPercentWithoutTax", afterProfitPercentWithoutTax.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
            }
            if (CollectionUtils.isNotEmpty(changeVO.getWbsBudgets())) {
                for (ProjectWbsBudgetChangeVO.ChangeHistory changeHistory : changeVO.getWbsBudgets()) {
                    Map<String, String> map = new HashMap<>();
                    // wbs
                    map.put(WbsBudgetFieldConstant.WBS_FULL_CODE, MapUtils.getString(changeHistory.getChange(), WbsBudgetFieldConstant.WBS_FULL_CODE));
                    // 描述
                    map.put(WbsBudgetFieldConstant.DESCRIPTION, MapUtils.getString(changeHistory.getChange(), WbsBudgetFieldConstant.DESCRIPTION));
                    // 活动事项
                    map.put(WbsBudgetFieldConstant.ACTIVITY_CODE, MapUtils.getString(changeHistory.getChange(), WbsBudgetFieldConstant.ACTIVITY_CODE));
                    // 预算金额（变更前预算）
                    map.put("beforePrice", MapUtils.getString(changeHistory.getHistory(), WbsBudgetFieldConstant.PRICE));
                    // 预算基线
                    map.put(WbsBudgetFieldConstant.BASELINE_COST, MapUtils.getString(changeHistory.getHistory(), WbsBudgetFieldConstant.BASELINE_COST));
                    // 需求预算
                    map.put(WbsBudgetFieldConstant.DEMAND_COST, MapUtils.getString(changeHistory.getHistory(), WbsBudgetFieldConstant.DEMAND_COST));
                    // 在途成本
                    map.put(WbsBudgetFieldConstant.ON_THE_WAY_COST, MapUtils.getString(changeHistory.getHistory(), WbsBudgetFieldConstant.ON_THE_WAY_COST));
                    // 已发生成本
                    map.put(WbsBudgetFieldConstant.INCURRED_COST, MapUtils.getString(changeHistory.getHistory(), WbsBudgetFieldConstant.INCURRED_COST));
                    // 剩余可用预算
                    map.put(WbsBudgetFieldConstant.REMAINING_COST, MapUtils.getString(changeHistory.getHistory(), WbsBudgetFieldConstant.REMAINING_COST));
                    // 变更后预算
                    map.put("afterPrice", MapUtils.getString(changeHistory.getChange(), WbsBudgetFieldConstant.PRICE));
                    // 变更后剩余可用预算
                    map.put(WbsBudgetFieldConstant.AFTER_CHANGE_REMAINING_COST, MapUtils.getString(changeHistory.getChange(), WbsBudgetFieldConstant.AFTER_CHANGE_REMAINING_COST));
                    // 变更金额
                    map.put(WbsBudgetFieldConstant.CHANGE_PRICE, MapUtils.getString(changeHistory.getChange(), WbsBudgetFieldConstant.CHANGE_PRICE));
                    // 累计变更金额
                    map.put(WbsBudgetFieldConstant.AFTER_CHANGE_CHANGE_ACCUMULATE_COST, MapUtils.getString(changeHistory.getChange(), WbsBudgetFieldConstant.AFTER_CHANGE_CHANGE_ACCUMULATE_COST));
                    wbsMapList.add(map);
                }
            }
        }
        responseMap.setHeadMap(headMap);

        //获取附件
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(CtcAttachmentModule.PROJECT_BUDGET_CHANGE.code());
        attachmentQuery.setModuleId(headerId);
        List<CtcAttachmentDto> ctcAttachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
        if (!CollectionUtils.isEmpty(ctcAttachmentDtos)) {
            List<AduitAtta> fileList = new ArrayList<>();
            for (CtcAttachmentDto attachmentDto : ctcAttachmentDtos) {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(String.valueOf(attachmentDto.getAttachId()));
                aduitAtta.setFileSize(String.valueOf(attachmentDto.getFileSize()));
                aduitAtta.setFileName(attachmentDto.getAttachName());
                fileList.add(aduitAtta);
            }
            responseMap.setFileList(fileList);
        }

        responseMap.setList1(wbsMapList);
        responseMap.setStatus("success");
        responseMap.setMsg("success");
        return responseMap;
    }

    @Override
    public boolean budgetBaselineChangeCheck(Long projectId) {
        Project project = projectService.selectByPrimaryKey(projectId);
        Assert.notNull(project, "项目不存在" + projectId);
        Assert.isTrue(Objects.equals(project.getWbsEnabled(),Boolean.TRUE), "项目类型未启用wbs");
        // 权限校验
        authCheck(project);
        return Boolean.TRUE;
    }
}
