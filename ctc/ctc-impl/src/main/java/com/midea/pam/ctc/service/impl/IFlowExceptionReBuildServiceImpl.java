package com.midea.pam.ctc.service.impl;

import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.SdpReceivePaymentApplyHandleResultDTO;
import com.midea.pam.common.ctc.dto.SdpReceivePaymentInvoiceDetailRequestResultDTO;
import com.midea.pam.common.ctc.dto.SdpReceivePaymentInvoiceRequestISPResultDTO;
import com.midea.pam.common.ctc.entity.GscPaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentApplyExample;
import com.midea.pam.common.ctc.entity.PaymentPlan;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.enums.AuditStatus;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.GscPaymentInvoiceTaxInvoiceStatusEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.mapper.GscPaymentInvoiceExtMapper;
import com.midea.pam.ctc.mapper.GscPaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.PaymentApplyMapper;
import com.midea.pam.ctc.mapper.PaymentPlanMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.IFlowExceptionReBuildService;
import com.midea.pam.ctc.service.ProjectService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

public class IFlowExceptionReBuildServiceImpl implements IFlowExceptionReBuildService {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(IFlowExceptionReBuildServiceImpl.class);

    private static final BigDecimal TOTAL_INVOICE_AMOUNT_THRESHOLD = new BigDecimal(500000);

    @Resource
    private ProjectService projectService;

    @Resource
    private PaymentApplyMapper paymentApplyMapper;

    @Resource
    private BasedataExtService basedataExtService;

    @Resource
    private GscPaymentInvoiceMapper gscPaymentInvoiceMapper;

    @Resource
    private PurchaseContractMapper purchaseContractMapper;

    @Resource
    private GscPaymentInvoiceExtMapper gscPaymentInvoiceExtMapper;

    @Resource
    private PaymentPlanMapper paymentPlanMapper;

    @Override
    public SdpReceivePaymentApplyHandleResultDTO paymentApplyFlowDataQuery(Long paymentApplyId) {
        logger.info("异常流程重新提交-付款申请流程数据查询, paymentApplyId: {}", paymentApplyId);
        
        SdpReceivePaymentApplyHandleResultDTO resultDTO = new SdpReceivePaymentApplyHandleResultDTO();
        
        try {
            // 查询付款申请信息
            PaymentApplyExample example = new PaymentApplyExample();
            example.createCriteria()
                .andIdEqualTo(paymentApplyId)
                .andDeletedFlagEqualTo(0);
            List<PaymentApply> paymentApplies = paymentApplyMapper.selectByExample(example);
            
            if (ListUtils.isEmpty(paymentApplies)) {
                throw new BizException(Code.ERROR, "未找到对应的付款申请记录");
            }
            
            PaymentApply paymentApply = paymentApplies.get(0);

            if(!Objects.equals(paymentApply.getAuditStatus(), AuditStatus.EXCEPTION.getCode())){
                logger.info("异常流程重新提交-付款申请流程数据查询,当前付款申请审批状态不是异常状态, paymentApplyId: {}", paymentApplyId);
                throw new BizException(Code.ERROR, "当前付款申请审批状态不是异常状态");
            }

            // 设置返回结果
            resultDTO.setPaymentApplyId(paymentApply.getId());
            resultDTO.setPaymentApplyCode(paymentApply.getPaymentApplyCode());
            
            // 获取项目信息
            Project project = projectService.findDetail(paymentApply.getProjectId());
            if (project != null) {
                resultDTO.setProjectCode(project.getCode());
            }

            PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(paymentApply.getPurchaseContractId());

            //设置项目经理MIP
            if(Objects.nonNull(purchaseContract.getManager())){
                UserInfo managerUserInfo = CacheDataUtils.findUserById(purchaseContract.getManager());
                resultDTO.setManagerMip(managerUserInfo.getUsername());
            }

            //设置采购跟进人MIP
            if(Objects.nonNull(purchaseContract.getPurchasingFollower())){
                UserInfo purchasingFollowerUserInfo = CacheDataUtils.findUserById(purchaseContract.getPurchasingFollower());
                resultDTO.setPurchasingFollower(purchasingFollowerUserInfo.getUsername());
            }

            //设置项目财务人员
            if (project != null && Objects.nonNull(project.getFinancial())) {
                UserInfo projectFinanceUserInfo = CacheDataUtils.findUserById(project.getFinancial());
                resultDTO.setProjectFinance(projectFinanceUserInfo.getUsername());
            }

            resultDTO.setPurchaseContractName(paymentApply.getPurchaseContractName());
            
            // 设置单位ID
            Long topUnitId = CacheDataUtils.getTopUnitIdByOuId(paymentApply.getOuId());
            resultDTO.setUnitId(topUnitId);
            
            // 设置是否第三方票据
           /* Dict dict = basedataExtService.findDictById(paymentApply.getPaymentMethodId());
            if (dict != null && dict.getType().contains("third_bill")) {
                resultDTO.setThirdPartyTicket(3); // 是第三方票据
            } else {
                resultDTO.setThirdPartyTicket(1); // 不是第三方票据
            }*/

            Long paymentPlanId = paymentApply.getPaymentPlanId();
            if (Objects.isNull(paymentPlanId)) {
                throw new BizException(Code.ERROR, "未找到对应的付款计划");
            }
            PaymentPlan paymentPlan = paymentPlanMapper.selectByPrimaryKey(paymentPlanId);
            // 获取付款计划的预付款标识
            Byte prepaymentFlag = paymentPlan.getPrepaymentFlag();
            boolean isPrepayment = Objects.nonNull(prepaymentFlag) && prepaymentFlag == 1;
            boolean isThirdParty = paymentPlan.getPaymentMethodName().contains("第三方");

            logger.info("异常流程重新提交-付款申请分支判断, 付款方式:{}, 是否第三方:{}, 预付款标识:{}, 是否预付款:{}",
                    paymentPlan.getPaymentMethodName(), isThirdParty, prepaymentFlag, isPrepayment);

            // 分支A：付款方式为非第三方 或 (付款方式为第三方 且 是否预付款=否)
            // 分支B：付款方式为第三方 且 是否预付款=是
            if(isThirdParty && isPrepayment){
                resultDTO.setThirdPartyTicket(3); // 分支B
                logger.info("异常流程重新提交-付款申请分支判断结果 - 执行分支B(ThirdPartyTicket=3): 付款方式为第三方且是预付款");
            }else if((isPrepayment & !isThirdParty) || !isPrepayment){
                resultDTO.setThirdPartyTicket(1); // 分支A
                logger.info("异常流程重新提交-付款申请分支判断结果 - 执行分支A(ThirdPartyTicket=1): {}是预付款且{}不是第三方付款方式, 或{}是预付款",
                        isPrepayment ? "" : "不", isThirdParty ? "" : "不", isPrepayment ? "" : "不");
            }
            
            resultDTO.setIsSuccess(true);
            resultDTO.setAbandonFlow(false);
            
        } catch (Exception e) {
            logger.error("异常流程重新提交-付款申请流程数据查询异常", e);
            resultDTO.setIsSuccess(false);
            resultDTO.setErrorMsg(e.getMessage());
        }
        
        return resultDTO;
    }

    @Override
    public SdpReceivePaymentInvoiceRequestISPResultDTO paymentInvoiceFlowDataQuery(Long paymentInvoiceId) {
        logger.info("异常流程重新提交-ISP开票申请流程数据查询, paymentInvoiceId: {}", paymentInvoiceId);
        
        SdpReceivePaymentInvoiceRequestISPResultDTO resultDTO = new SdpReceivePaymentInvoiceRequestISPResultDTO();
        
        try {
            // 查询GSC支付发票信息
            GscPaymentInvoice gscPaymentInvoice = gscPaymentInvoiceMapper.selectByPrimaryKey(paymentInvoiceId);
            if (Objects.isNull(gscPaymentInvoice)) {
                throw new BizException(Code.ERROR, "未找到对应的ISP发票申请记录");
            }

            if(!Objects.equals(gscPaymentInvoice.getStatus(), AuditStatus.EXCEPTION.getCode())){
                logger.info("异常流程重新提交-ISP开票申请流程数据查询,当前ISP开票申请审批状态不是异常状态, paymentInvoiceId: {}", paymentInvoiceId);
                throw new BizException(Code.ERROR, "当前ISP开票申请审批状态不是异常状态");
            }

            // 查询采购合同
            PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(gscPaymentInvoice.getPurchaseContractId());
            if (Objects.isNull(purchaseContract)) {
                throw new BizException(Code.ERROR, "未找到对应的采购合同");
            }

            // 查询项目信息
            Project project = projectService.findDetail(purchaseContract.getProjectId());
            if (Objects.isNull(project)) {
                throw new BizException(Code.ERROR, "未找到对应的项目信息");
            }

            // 设置基本信息
            resultDTO.setIspInvoiceApplyId(gscPaymentInvoice.getId());
            resultDTO.setInvoiceNumber(gscPaymentInvoice.getInvoiceNumber());
            
            // 设置项目经理MIP
            if (Objects.nonNull(project.getManagerId())) {
                UserInfo managerUserInfo = CacheDataUtils.findUserById(project.getManagerId());
                resultDTO.setManagerMip(managerUserInfo.getUsername());
            }

            //设置采购跟进人
            if (Objects.nonNull(purchaseContract.getPurchasingFollower())) {
                UserInfo purchasingFollowerUserInfo = CacheDataUtils.findUserById(purchaseContract.getPurchasingFollower());
                resultDTO.setPurchasingFollowerMip(purchasingFollowerUserInfo.getUsername());
            }

            //设置项目财务
            if (Objects.nonNull(project.getFinancial())) {
                UserInfo projectFinanceUserInfo = CacheDataUtils.findUserById(project.getFinancial());
                resultDTO.setProjectFinance(projectFinanceUserInfo.getUsername());
            }

            // 设置单位ID
            Long topUnitId = CacheDataUtils.getTopUnitIdByOuId(gscPaymentInvoice.getOuId());
            resultDTO.setUnitId(topUnitId);



            if(Objects.nonNull(gscPaymentInvoice.getTotalInvoiceAmount()) && gscPaymentInvoice.getTotalInvoiceAmount().compareTo(TOTAL_INVOICE_AMOUNT_THRESHOLD) > 0){
                resultDTO.setTotalInvoiceAmountFlag(2);
            }else{
                resultDTO.setTotalInvoiceAmountFlag(1);
            }

            // 设置流程相关信息
            resultDTO.setReSubmitFlow(false);
            resultDTO.setAbandonFlow(false);
            resultDTO.setIsSuccess(true);

        } catch (Exception e) {
            logger.error("异常流程重新提交-ISP开票申请流程数据查询异常", e);
            resultDTO.setIsSuccess(false);
            resultDTO.setErrorMsg(e.getMessage());
        }
        
        return resultDTO;
    }

    @Override
    public SdpReceivePaymentInvoiceDetailRequestResultDTO paymentInvoiceDetailFlowDataQuery(Long paymentInvoiceId) {
        logger.info("异常流程重新提交-税票申请流程数据查询开始, paymentInvoiceId: {}", paymentInvoiceId);
        
        SdpReceivePaymentInvoiceDetailRequestResultDTO resultDTO = new SdpReceivePaymentInvoiceDetailRequestResultDTO();
        
        try {
            // 查询GSC支付发票信息
            GscPaymentInvoice gscPaymentInvoice = gscPaymentInvoiceMapper.selectByPrimaryKey(paymentInvoiceId);
            if (Objects.isNull(gscPaymentInvoice)) {
                logger.error("异常流程重新提交-未找到对应的发票申请记录, paymentInvoiceId: {}", paymentInvoiceId);
                throw new BizException(Code.ERROR, "未找到对应的发票申请记录");
            }
            logger.info("异常流程重新提交-查询到发票信息: {}", JsonUtils.toString(gscPaymentInvoice));

            if(!Objects.equals(gscPaymentInvoice.getTaxInvoiceStatus(), GscPaymentInvoiceTaxInvoiceStatusEnum.INVOICE_EXCEPTION.getCode())){
                logger.info("异常流程重新提交-税票申请流程数据查询,当前税票申请审批状态不是异常状态, paymentInvoiceId: {}", paymentInvoiceId);
                throw new BizException(Code.ERROR, "当前税票申请审批状态不是异常状态");
            }

            // 查询采购合同
            PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(gscPaymentInvoice.getPurchaseContractId());
            if (Objects.isNull(purchaseContract)) {
                logger.error("异常流程重新提交-未找到对应的采购合同, purchaseContractId: {}", gscPaymentInvoice.getPurchaseContractId());
                throw new BizException(Code.ERROR, "未找到对应的采购合同");
            }
            logger.info("异常流程重新提交-查询到采购合同信息: {}", JsonUtils.toString(purchaseContract));

            // 查询项目信息
            Project project = projectService.findDetail(purchaseContract.getProjectId());
            if (Objects.isNull(project)) {
                logger.error("异常流程重新提交-未找到对应的项目信息, projectId: {}", purchaseContract.getProjectId());
                throw new BizException(Code.ERROR, "未找到对应的项目信息");
            }
            logger.info("异常流程重新提交-查询到项目信息: {}", JsonUtils.toString(project));

            // 设置基本信息
            resultDTO.setInvoiceNumber(gscPaymentInvoice.getInvoiceNumber());
            resultDTO.setInvoiceApplyId(gscPaymentInvoice.getId());

            // 设置项目经理信息
            if (Objects.nonNull(project.getManagerId())) {
                UserInfo managerUserInfo = CacheDataUtils.findUserById(project.getManagerId());
                if (Objects.nonNull(managerUserInfo)) {
                    logger.info("异常流程重新提交-设置项目经理信息, managerId: {}, managerName: {}", 
                        project.getManagerId(), managerUserInfo.getUsername());
                    resultDTO.setManagerMip(managerUserInfo.getUsername());
                }
            }

            // 设置采购跟进人信息
            if (Objects.nonNull(purchaseContract.getPurchasingFollower())) {
                UserInfo followerUserInfo = CacheDataUtils.findUserById(purchaseContract.getPurchasingFollower());
                if (Objects.nonNull(followerUserInfo)) {
                    logger.info("异常流程重新提交-设置采购跟进人信息, followerId: {}, followerName: {}", 
                        purchaseContract.getPurchasingFollower(), followerUserInfo.getUsername());
                    resultDTO.setPurchasingFollower(followerUserInfo.getUsername());
                }
            }

            // 设置单位ID
            Long topUnitId = CacheDataUtils.getTopUnitIdByOuId(gscPaymentInvoice.getOuId());
            resultDTO.setUnitId(topUnitId);
            logger.info("异常流程重新提交-设置单位信息, ouId: {}, topUnitId: {}", 
                gscPaymentInvoice.getOuId(), topUnitId);

            // 设置流程相关信息
            resultDTO.setFormUrl("gscPaymentInvoiceDetailApp");
            resultDTO.setIsSuccess(true);
            
            logger.info("异常流程重新提交-税票申请流程数据查询完成, resultDTO: {}", JsonUtils.toString(resultDTO));

        } catch (Exception e) {
            logger.error("异常流程重新提交-税票申请流程数据查询异常", e);
            resultDTO.setIsSuccess(false);
            resultDTO.setErrorMsg(e.getMessage());
        }
        
        return resultDTO;
    }
}
