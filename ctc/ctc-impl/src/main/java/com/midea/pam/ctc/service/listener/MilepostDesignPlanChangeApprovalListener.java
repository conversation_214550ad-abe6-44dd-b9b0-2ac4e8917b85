package com.midea.pam.ctc.service.listener;

import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsBudgetChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.entity.MaterialAdjustHeader;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChange;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsDesignPlanRel;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementExample;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.MaterialAdjustEnum;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.RequirementStatusEnum;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.ChangeTypeEnum;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DesignPlanDetailGenerateRequire;
import com.midea.pam.ctc.common.enums.MilepostDesignPlanDetailModelStatus;
import com.midea.pam.ctc.common.enums.ProjectWbsReceiptsBudgetDemandTypeEnums;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementPurchaseTypeEnums;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementStatus;
import com.midea.pam.ctc.common.enums.ReleaseDetailStatus;
import com.midea.pam.ctc.common.redis.RedisUtilServer;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.ProjectExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsDesignPlanRelExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsMapper;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetExtMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsBudgetChangeHistoryService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsBudgetService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.service.MaterialAdjustService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeRecordService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectWbsReceiptsDesignPlanRelService;
import com.midea.pam.ctc.service.PurchaseMaterialRequirementService;
import com.midea.pam.ctc.service.PurchaseOrderService;
import com.midea.pam.ctc.service.event.MilepostDesignPlanChangeApprovalEvent;
import com.midea.pam.ctc.service.helper.SystemContextHelper;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public class MilepostDesignPlanChangeApprovalListener implements ApplicationListener<MilepostDesignPlanChangeApprovalEvent> {

    private static final Logger logger = LoggerFactory.getLogger(MilepostDesignPlanChangeApprovalListener.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private ProjectWbsReceiptsDesignPlanRelExtMapper projectWbsReceiptsDesignPlanRelExtMapper;

    @Resource
    private PurchaseContractBudgetExtMapper purchaseContractBudgetExtMapper;

    @Resource
    private ProjectWbsReceiptsBudgetMapper projectWbsReceiptsBudgetMapper;

    @Resource
    private ProjectWbsReceiptsDesignPlanRelService projectWbsReceiptsDesignPlanRelService;

    @Resource
    private MaterialExtService materialExtService;

    @Resource
    private PurchaseMaterialRequirementMapper purchaseMaterialRequirementMapper;

    @Resource
    private PurchaseMaterialRequirementService purchaseMaterialRequirementService;

    @Resource
    private ProjectWbsReceiptsBudgetService projectWbsReceiptsBudgetService;

    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;

    @Resource
    private MaterialAdjustService materialAdjustService;

    @Resource
    private PurchaseOrderService purchaseOrderService;

    @Resource
    private ProjectExtMapper projectExtMapper;

    @Resource
    private ProjectService projectService;

    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;

    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;

    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;

    @Resource
    private ProjectWbsReceiptsMapper projectWbsReceiptsMapper;

    @Resource
    private MilepostDesignPlanDetailChangeService milepostDesignPlanDetailChangeService;

    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;

    @Resource
    private ProjectWbsReceiptsBudgetChangeHistoryService projectWbsReceiptsBudgetChangeHistoryService;

    @Resource
    private ProjectWbsReceiptsExtMapper projectWbsReceiptsExtMapper;

    @Resource
    private WorkflowCallbackService workflowCallbackService;

    @Resource
    private MilepostDesignPlanDetailChangeRecordService milepostDesignPlanDetailChangeRecordService;

    @Resource
    private SystemContextHelper systemContextHelper;

    @Transactional(rollbackFor = Exception.class)
    @Async
    @Override
    public void onApplicationEvent(MilepostDesignPlanChangeApprovalEvent event) {
        logger.info("详设变更单据审批通过回调的异步处理参数为:{}", JsonUtils.toString(event));
        // 设置当前用户信息
        systemContextHelper.dealUserInfo(event.getCreateUserId(), event.getCompanyId());
        pass(event.getFormInstanceId(), event.getFdInstanceId(), event.getFormUrl(), event.getEventName(), event.getHandlerId(),
                event.getCompanyId(), event.getCreateUserId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void pass(Long formInstanceId, String fdInstanceId, String formUrl, String eventName, String handlerId, Long companyId,
                     Long createUserId) {
        logger.info("详设变更单据审批通过回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, createUserId:{}",
                formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, companyId);
        String lockName = String.format("MilepostDesignPlanChange_pass_%s_%s_%s_%s", formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    //该流程的流程实例id 改造为了 详设变更记录表的id
                    Long originFormInstanceId = milepostDesignPlanDetailChangeRecordService.getOriginFormInstanceId(formInstanceId);
                    Guard.notNull(originFormInstanceId, String.format("详设变更单据提交审批回调 formInstanceId:%s对应详设变更单变更记录不存在，不处理", formInstanceId));

                    ProjectWbsReceipts receipt = projectWbsReceiptsMapper.selectByPrimaryKey(originFormInstanceId);
                    Guard.notNull(receipt, String.format("详设变更单据审批通过回调 formInstanceId:%s对应详设变更单不存在，不处理", formInstanceId));

                    // 根据详设变更单据，获取详细设计
                    List<MilepostDesignPlanDetailChange> planDetailChangeHistoryList =
                            milepostDesignPlanDetailChangeService.getDetailByReceiptsId(receipt.getId());
                    Guard.notNullOrEmpty(planDetailChangeHistoryList, String.format("详设变更单据审批通过回调 formInstanceId:%s对应详细设计关联不存在", formInstanceId));
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，planDetailChangeHistoryList：{}", formInstanceId,
                            JsonUtils.toString(planDetailChangeHistoryList));

                    //变更所选模组
                    List<Long> checkIds = Arrays.stream(receipt.getBatchUploadPathIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，checkIds：{}", formInstanceId, JsonUtils.toString(checkIds));

                    List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
                    List<MilepostDesignPlanDetailChange> planDetailChangeUpdateList = new ArrayList<>();
                    List<MilepostDesignPlanDetailChange> planDetailChangeAddList = new ArrayList<>();

                    List<Long> deleteDetailIds = new ArrayList<>();
                    Map<Long, MilepostDesignPlanDetailChange> designPlanDetailChangeMap = planDetailChangeHistoryList.stream()
                            .collect(Collectors.toMap(MilepostDesignPlanDetailChange::getId, Function.identity()));
                    // 当前详设变更id 和 对应的父级DTO
                    Map<Long, MilepostDesignPlanDetailChange> parentChangeMap = new HashMap<>();
                    Map<Long, MilepostDesignPlanDetailChange> currentChangeMap = new HashMap<>();
                    List<Long> designPlanDetailIdList = new ArrayList<>();
                    for (MilepostDesignPlanDetailChange change : planDetailChangeHistoryList) {
                        if (!Objects.equals(change.getHistoryType(), HistoryType.CHANGE.getCode())) {
                            continue;
                        }
                        if (Objects.nonNull(change.getDesignPlanDetailId()) && Objects.equals(ChangeTypeEnum.DELETE.getCode(),
                                change.getChangeType())) {
                            deleteDetailIds.add(change.getDesignPlanDetailId());
                        }
                        if (Objects.nonNull(change.getDesignPlanDetailId())) {
                            planDetailChangeUpdateList.add(change);
                            designPlanDetailIdList.add(change.getDesignPlanDetailId());
                        } else {
                            planDetailChangeAddList.add(change);
                        }
                        Long parentId = change.getParentId();
                        if (null != parentId && parentId != -1) {
                            parentChangeMap.put(change.getId(), designPlanDetailChangeMap.get(parentId));
                        }
                        currentChangeMap.put(change.getId(), change);
                    }
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，deleteDetailIds：{}", formInstanceId, JsonUtils.toString(deleteDetailIds));
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，parentChangeMap：{}", formInstanceId, JsonUtils.toString(parentChangeMap));
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，planDetailChangeUpdateList：{}", formInstanceId,
                            JsonUtils.toString(planDetailChangeUpdateList));
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，planDetailChangeAddList：{}", formInstanceId,
                            JsonUtils.toString(planDetailChangeAddList));

                    // 获取变更的详设是否对应其他正在确认的单据
                    Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(designPlanDetailIdList)) {
                        List<ProjectWbsReceiptsDto> receiptsDtoList = projectWbsReceiptsService.selectByDetailIds(designPlanDetailIdList);
                        receiptsDtoMap.putAll(CollectionUtils.isEmpty(receiptsDtoList) ? new HashMap<>()
                                : receiptsDtoList.stream()
                                .filter(e -> Objects.equals(RequirementStatusEnum.DRAFT.getCode(), e.getRequirementStatus())
                                        || Objects.equals(RequirementStatusEnum.TODO.getCode(), e.getRequirementStatus())
                                        || Objects.equals(RequirementStatusEnum.PENDING.getCode(), e.getRequirementStatus()))
                                .collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId)));
                    }

                    // 先修改
                    for (MilepostDesignPlanDetailChange milepostDesignPlanDetailChange : planDetailChangeUpdateList) {
                        MilepostDesignPlanDetailDto milepostDesignPlanDetail =
                                BeanConverter.copyProperties(milepostDesignPlanDetailChange, MilepostDesignPlanDetailDto.class);
                        Long designPlanDetailId = milepostDesignPlanDetailChange.getDesignPlanDetailId();
                        milepostDesignPlanDetail.setId(designPlanDetailId);
                        // TODO 踩坑，milepostDesignPlanDetailChange的parent_id不是详设的parent_id，一定要置为null
                        milepostDesignPlanDetail.setParentId(null);
                        List<ProjectWbsReceiptsDto> receiptsDtoList =
                                projectWbsReceiptsExtMapper.selectProcessReceiptsByDetailIds(Collections.singletonList(designPlanDetailId));
                        if (ListUtils.isNotEmpty(receiptsDtoList)) {
                            milepostDesignPlanDetail.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());
                            // 重新设置模组状态
                            Long changeId = milepostDesignPlanDetailChange.getId();
                            MilepostDesignPlanDetailChange change = currentChangeMap.get(changeId);
                            change.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());
                            currentChangeMap.put(changeId, change);
                        }
                        if (!Boolean.FALSE.equals(milepostDesignPlanDetail.getWbsLastLayer())) {
                            if (receiptsDtoMap.containsKey(designPlanDetailId)) {
                                continue;
                            }

                            milepostDesignPlanDetail.setStatus(CheckStatus.WBS_RECEIPTS_PASSED.code());
                        }
                        updateDesignPlanDetailList.add(milepostDesignPlanDetail);
                    }
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，updateDesignPlanDetailList：{}", formInstanceId,
                            JsonUtils.toString(updateDesignPlanDetailList));
                    //批量更新当前的详细设计
                    if (CollectionUtils.isNotEmpty(updateDesignPlanDetailList)) {
                        milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
                    }

                    List<Long> addChangeIdList = new ArrayList<>();
                    // 再添加
                    for (MilepostDesignPlanDetailChange currentChange : planDetailChangeAddList) {
                        MilepostDesignPlanDetailChange parentChange = parentChangeMap.get(currentChange.getId());
                        fillParentId(parentChange, currentChange, parentChangeMap, currentChangeMap, checkIds, addChangeIdList);
                    }

                    // 库存组织id
                    Long organizationId = projectService.getOrganizationIdByProjectId(receipt.getProjectId());
                    // 存新生成的erpCode，避免重复生成
                    Map<Long, String> materialIdWithNewErpCode = new HashMap<>();
                    // key=pamCode value=物料
                    Map<String, Material> materialMap = new HashMap<>();

                    List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos = milepostDesignPlanDetailService.selectDetailByIds(checkIds);
                    List<MilepostDesignPlanDetailDto> topParentMilepostDesignPlanDetailDtos =
                            milepostDesignPlanService.filterTopParent(milepostDesignPlanDetailDtos);
                    topParentMilepostDesignPlanDetailDtos.removeAll(Collections.singleton(null));
                    List<MilepostDesignPlanDetailDto> detailDtosTree =
                            milepostDesignPlanService.buildMilepostDesignPlanDetailTree(milepostDesignPlanDetailDtos,
                                    topParentMilepostDesignPlanDetailDtos);
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，detailDtosTree：{}", formInstanceId, JsonUtils.toString(detailDtosTree));
                    List<ProjectWbsReceiptsDesignPlanRel> addDesignPlanRelList = new ArrayList<>();
                    List<ProjectWbsReceiptsDesignPlanRel> updateDesignPlanRelList = new ArrayList<>();
                    List<MilepostDesignPlanDetail> finalPlanDetailList = new ArrayList<>();

                    ProjectWbsReceiptsBudgetChangeHistoryExample changeHistoryExample = new ProjectWbsReceiptsBudgetChangeHistoryExample();
                    changeHistoryExample.createCriteria().andProjectWbsChangeReceiptsIdEqualTo(receipt.getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                    List<ProjectWbsReceiptsBudgetChangeHistoryDto> changeHistoryDtoList =
                            BeanConverter.copy(projectWbsReceiptsBudgetChangeHistoryService.selectByExample(changeHistoryExample),
                                    ProjectWbsReceiptsBudgetChangeHistoryDto.class);
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，changeHistoryDtoList：{}", formInstanceId, JsonUtils.toString(changeHistoryDtoList));

                    if (ListUtils.isNotEmpty(changeHistoryDtoList)) {
                        // 需求发布后的详设变更
                        //当前变更单据的需求发布分组
                        Map<Long, List<ProjectWbsReceiptsBudgetChangeHistoryDto>> publishReceiptsMap =
                                changeHistoryDtoList.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsBudgetChangeHistoryDto::getProjectWbsPublishReceiptsId));
                        for (MilepostDesignPlanDetailDto detailDto : detailDtosTree) {
                            //当前所选模组对应需求发布单据
                            Long checkReceiptsId = projectWbsReceiptsDesignPlanRelService.getReceiptsIdByDetailId(detailDto.getId());

                            //存进度确认
                            Map<Long, List<MilepostDesignPlanDetailDto>> progressConfirmReceiptsAndDetailList = new HashMap<>();
                            checkPartConfirm(detailDto, checkIds, materialMap, materialIdWithNewErpCode, organizationId, publishReceiptsMap,
                                    progressConfirmReceiptsAndDetailList, checkReceiptsId, null, originFormInstanceId, deleteDetailIds, formInstanceId);
                            if (MapUtils.isEmpty(progressConfirmReceiptsAndDetailList)) {
                                continue;
                            }
                            logger.info("详设变更单据审批通过回调的formInstanceId：{}，materialIdWithNewErpCode：{}", formInstanceId,
                                    JsonUtils.toString(materialIdWithNewErpCode));
                            logger.info("详设变更单据审批通过回调的formInstanceId：{}，publishReceiptsMap：{}", formInstanceId,
                                    JsonUtils.toString(publishReceiptsMap));
                            logger.info("详设变更单据审批通过回调的formInstanceId：{}，progressConfirmReceiptsAndDetailList：{}", formInstanceId,
                                    JsonUtils.toString(progressConfirmReceiptsAndDetailList));

                            logger.info("详设变更单据审批通过回调的formInstanceId：{}，keySet：{}", formInstanceId,
                                    JsonUtils.toString(progressConfirmReceiptsAndDetailList.keySet()));
                            List<ProjectWbsReceiptsDesignPlanRel> designPlanRelList =
                                    projectWbsReceiptsDesignPlanRelService.selectByReceiptsIds(new ArrayList<>(progressConfirmReceiptsAndDetailList.keySet()));
                            Map<Long, List<ProjectWbsReceiptsDesignPlanRel>> designPlanRelMap = CollectionUtils.isEmpty(designPlanRelList) ?
                                    new HashMap<>()
                                    :
                                    designPlanRelList.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsDesignPlanRel::getProjectWbsReceiptsId));
                            ProjectWbsReceiptsExample receiptsExample = new ProjectWbsReceiptsExample();
                            receiptsExample.createCriteria().andDeletedFlagEqualTo(false).andIdIn(new ArrayList<>(progressConfirmReceiptsAndDetailList.keySet()));
                            List<ProjectWbsReceipts> projectWbsReceiptsList = projectWbsReceiptsMapper.selectByExampleWithBLOBs(receiptsExample);
                            Map<Long, ProjectWbsReceipts> projectWbsReceiptsMap = CollectionUtils.isEmpty(projectWbsReceiptsList) ? new HashMap<>()
                                    : projectWbsReceiptsList.stream().collect(Collectors.toMap(ProjectWbsReceipts::getId, Function.identity()));
                            logger.info("详设变更单据审批通过回调的formInstanceId：{}，designPlanRelMap：{}，projectWbsReceiptsMap：{}",
                                    formInstanceId, JsonUtils.toString(designPlanRelMap), JsonUtils.toString(projectWbsReceiptsMap));

                            /** 批量统计采购需求 未采购量/总需求量/已采购量/关闭数量 **/
                            List<Long> requirementIdList = new ArrayList<>();
                            Map<Long, PurchaseMaterialRequirementDto> quantityInfoMap = new HashMap<>();
                            Map<String, PurchaseMaterialRequirement> requirementMap = new HashMap<>();
                            List<Long> skipDesignPlanDetailIdList = new ArrayList<>();
                            List<Long> notSkipDesignPlanDetailIdList = new ArrayList<>();
                            for (Long key : progressConfirmReceiptsAndDetailList.keySet()) {
                                List<ProjectWbsReceiptsDesignPlanRel> relByReceipts = designPlanRelMap.get(key);
                                List<MilepostDesignPlanDetailDto> progressConfirmDetailDtos = progressConfirmReceiptsAndDetailList.get(key);
                                ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMap.get(key);

                                for (MilepostDesignPlanDetail planDetail : progressConfirmDetailDtos) {
                                    // 已被确认，跳过
                                    if (CollectionUtils.isNotEmpty(relByReceipts)
                                            && relByReceipts.stream().anyMatch(a -> a.getDesignPlanDetailId().equals(planDetail.getId())
                                            && !a.getProjectWbsReceiptsId().equals(key))) {
                                        skipDesignPlanDetailIdList.add(planDetail.getId());
                                        continue;
                                    }
                                    notSkipDesignPlanDetailIdList.add(planDetail.getId());

                                    PurchaseMaterialRequirementExample example = new PurchaseMaterialRequirementExample();
                                    PurchaseMaterialRequirementExample.Criteria criteria = example.createCriteria();
                                    criteria.andProjectIdEqualTo(planDetail.getProjectId());
                                    // 如果详细设计上面没有交货日期，就忽略。让下游业务 --采购合同控制
                                    if (null != planDetail.getDeliveryTime()) {
                                        criteria.andDeliveryTimeEqualTo(planDetail.getDeliveryTime());
                                    }
                                    criteria.andPamCodeEqualTo(planDetail.getPamCode());
                                    criteria.andWbsSummaryCodeEqualTo(planDetail.getWbsSummaryCode());
                                    criteria.andDeletedFlagEqualTo(false);
                                    criteria.andRequirementCodeEqualTo(projectWbsReceipts.getRequirementCode());
                                    List<PurchaseMaterialRequirement> requirementList = purchaseMaterialRequirementMapper.selectByExample(example);

                                    /* 物料采购需求 */
                                    PurchaseMaterialRequirement requirement = CollectionUtils.isEmpty(requirementList) ? null :
                                            requirementList.get(0);
                                    if (requirement != null) {
                                        requirementIdList.add(requirement.getId());
                                        requirementMap.put(buildRequirementKey(projectWbsReceipts, planDetail), requirement);
                                    }
                                }
                            }
                            if (CollectionUtils.isNotEmpty(requirementIdList)) {
                                logger.info("详设变更单据审批通过回调的formInstanceId：{}，requirementIdList：{}",
                                        formInstanceId, JsonUtils.toString(requirementIdList));
                                quantityInfoMap = purchaseMaterialRequirementService.getDetailByIds(requirementIdList).stream()
                                        .collect(Collectors.toMap(PurchaseMaterialRequirementDto::getId, Function.identity(), (a, b) -> a));
                            }
                            logger.info("详设变更单据审批通过回调的formInstanceId：{}，skipDesignPlanDetailIdList：{}，notSkipDesignPlanDetailIdList：{}，"
                                            + "quantityInfoMap：{}，requirementMap：{}",
                                    formInstanceId, JsonUtils.toString(skipDesignPlanDetailIdList), JsonUtils.toString(notSkipDesignPlanDetailIdList),
                                    JsonUtils.toString(quantityInfoMap), JsonUtils.toString(requirementMap));

                            for (Long key : progressConfirmReceiptsAndDetailList.keySet()) {
                                List<ProjectWbsReceiptsDesignPlanRel> relByReceipts = designPlanRelMap.get(key);
                                List<MilepostDesignPlanDetailDto> progressConfirmDetailDtos = progressConfirmReceiptsAndDetailList.get(key);
                                // pamCpde
                                List<String> pamCodeList = new ArrayList<>();
                                for (MilepostDesignPlanDetailDto progressConfirmDetail : progressConfirmDetailDtos) {
                                    //把历史的更新
                                    List<ProjectWbsReceiptsDesignPlanRel> existRelList = CollectionUtils.isEmpty(relByReceipts) ? new ArrayList<>()
                                            : relByReceipts.stream().filter(rel -> rel.getDesignPlanDetailId().equals(progressConfirmDetail.getId()))
                                            .collect(Collectors.toList());
                                    BigDecimal purchaseNum = progressConfirmDetail.getNumber()
                                            .multiply(milepostDesignPlanDetailService.getParentSigmaById(progressConfirmDetail.getParentId(), null));
                                    if (ListUtils.isNotEmpty(existRelList)) {
                                        ProjectWbsReceiptsDesignPlanRel designPlanRel = existRelList.get(0);
                                        designPlanRel.setPurchaseNum(purchaseNum);
                                        updateDesignPlanRelList.add(designPlanRel);
                                    } else {//新的加入需求发布单里
                                        ProjectWbsReceiptsDesignPlanRel designPlanRel = new ProjectWbsReceiptsDesignPlanRel();
                                        designPlanRel.setPurchaseNum(purchaseNum);
                                        designPlanRel.setDesignPlanDetailId(progressConfirmDetail.getId());
                                        designPlanRel.setProjectWbsReceiptsId(key);
                                        designPlanRel.setProjectId(progressConfirmDetail.getProjectId());
                                        designPlanRel.setDeletedFlag(false);
                                        addDesignPlanRelList.add(designPlanRel);
                                    }

                                    pamCodeList.add(progressConfirmDetail.getPamCode());
                                }

                                //预算生效
                                List<ProjectWbsReceiptsBudgetChangeHistoryDto> projectWbsReceiptsBudgetChangeHistoryDtos =
                                        publishReceiptsMap.get(key);
                                if (ListUtils.isNotEmpty(projectWbsReceiptsBudgetChangeHistoryDtos)) {
                                    //先按照 demandType 0,1,2升序排序，且肯定有0
                                    projectWbsReceiptsBudgetChangeHistoryDtos.sort(Comparator.comparing(ProjectWbsReceiptsBudgetChangeHistoryDto::getDemandType));
                                    for (ProjectWbsReceiptsBudgetChangeHistoryDto changeHistory : projectWbsReceiptsBudgetChangeHistoryDtos) {
                                        ProjectWbsReceiptsBudgetExample example = new ProjectWbsReceiptsBudgetExample();
                                        example.createCriteria().andProjectWbsReceiptsIdEqualTo(changeHistory.getProjectWbsPublishReceiptsId())
                                                .andWbsSummaryCodeEqualTo(changeHistory.getWbsSummaryCode()).andDeletedFlagEqualTo(Boolean.FALSE);
                                        List<ProjectWbsReceiptsBudgetDto> budgetDtoList = projectWbsReceiptsBudgetService.selectByExample(example);
                                        if (ListUtils.isNotEmpty(budgetDtoList)) {
                                            // 预算表中存在，则根据需求分类 字段去处理
                                            ProjectWbsReceiptsBudgetDto projectWbsReceiptsBudgetDto = null;
                                            Long parentId = null;
                                            for (ProjectWbsReceiptsBudgetDto budgetDto : budgetDtoList) {
                                                if (Objects.equals(budgetDto.getDemandType(), changeHistory.getDemandType())) {
                                                    projectWbsReceiptsBudgetDto = budgetDto;
                                                }
                                                if (null == budgetDto.getParentid() || -1 == budgetDto.getParentid()) {
                                                    parentId = budgetDto.getId();
                                                }
                                            }
                                            if (null != projectWbsReceiptsBudgetDto) {
                                                // 这里说明修改 汇总 或者 物料采购 或者 物料外包(整包) 预算
                                                projectWbsReceiptsBudgetDto.setBudgetOccupiedAmount(changeHistory.getBudgetOccupiedAmountAfter());
                                                projectWbsReceiptsBudgetService.updateByPrimaryKeySelective(projectWbsReceiptsBudgetDto);
                                            } else {
                                                // 这里说明汇总预算肯定存在，且添加 物料采购 或者 物料外包(整包) 预算
                                                ProjectWbsReceiptsBudget sonBudget = BeanConverter.copy(changeHistory,
                                                        ProjectWbsReceiptsBudget.class);
                                                sonBudget.setId(null);
                                                sonBudget.setProjectWbsReceiptsId(changeHistory.getProjectWbsPublishReceiptsId());
                                                sonBudget.setBudgetOccupiedAmount(changeHistory.getBudgetOccupiedAmountAfter());
                                                sonBudget.setParentid(parentId);
                                                sonBudget.setCreateAt(new Date());
                                                sonBudget.setCreateBy(createUserId);
                                                sonBudget.setUpdateAt(null);
                                                sonBudget.setUpdateBy(null);
                                                projectWbsReceiptsBudgetMapper.insert(sonBudget);
                                            }
                                        } else {
                                            // 说明预算表中不存在任何的预算，且第一条循环是汇总预算
                                            ProjectWbsReceiptsBudget parentBudget = BeanConverter.copy(changeHistory, ProjectWbsReceiptsBudget.class);
                                            parentBudget.setProjectWbsReceiptsId(changeHistory.getProjectWbsPublishReceiptsId());
                                            parentBudget.setBudgetOccupiedAmount(changeHistory.getBudgetOccupiedAmountAfter());
                                            parentBudget.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode());
                                            parentBudget.setParentid(-1L);
                                            parentBudget.setCreateAt(new Date());
                                            parentBudget.setCreateBy(createUserId);
                                            parentBudget.setUpdateAt(null);
                                            parentBudget.setUpdateBy(null);
                                            projectWbsReceiptsBudgetMapper.insert(parentBudget);
                                        }
                                    }
                                }

                                ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMap.get(key);
                                // 物料
                                List<Material> materials = materialExtService.invokeMaterialApiGetByPamCodeList(pamCodeList, organizationId);
                                for (Material material : materials) {
                                    materialMap.put(material.getPamCode(), material);
                                }
                                for (MilepostDesignPlanDetail planDetail : progressConfirmDetailDtos) {
                                    // 已被确认，跳过
                                    if (CollectionUtils.isNotEmpty(relByReceipts)
                                            && relByReceipts.stream().anyMatch(a -> a.getDesignPlanDetailId().equals(planDetail.getId())
                                            && !a.getProjectWbsReceiptsId().equals(key))) {
                                        continue;
                                    }

                                    planDetail.setStatus(CheckStatus.WBS_RECEIPTS_PASSED.code());

                                    // 模组确认状态：已确认
                                    planDetail.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());

                                    // 设置ERP物料编码
                                    setErpCode(planDetail, materialIdWithNewErpCode);

                                    // 产生采购需求
                                    generateRequirement(formInstanceId, planDetail, materialMap, projectWbsReceipts, receipt, deleteDetailIds,
                                            quantityInfoMap, requirementMap);
                                    finalPlanDetailList.add(planDetail);
                                }
                            }

                        }

                        List<Long> publishReceiptIdList =
                                changeHistoryDtoList.stream().map(ProjectWbsReceiptsBudgetChangeHistoryDto::getProjectWbsPublishReceiptsId)
                                        .collect(Collectors.toList());
                        // 更新采购订单行的需求占用预算
                        purchaseOrderService.updateBudgetOccupiedAmount(publishReceiptIdList);
                    } else {
                        // 需求发布前的详设变更
                        for (MilepostDesignPlanDetailDto detailDto : detailDtosTree) {
                            //当前所选模组对应需求发布单据
                            Long checkReceiptsId = projectWbsReceiptsDesignPlanRelService.getReceiptsIdByDetailId(detailDto.getId());
                            //存进度确认
                            Map<Long, List<MilepostDesignPlanDetailDto>> progressConfirmReceiptsAndDetailList = new HashMap<>();
                            checkPartConfirm(detailDto, checkIds, materialMap, materialIdWithNewErpCode, organizationId, new HashMap<>(),
                                    progressConfirmReceiptsAndDetailList, checkReceiptsId, null, originFormInstanceId, deleteDetailIds, formInstanceId);
                            if (MapUtils.isEmpty(progressConfirmReceiptsAndDetailList)) {
                                continue;
                            }
                            logger.info("详设变更单据审批通过回调的formInstanceId：{}，materialIdWithNewErpCode：{}", formInstanceId,
                                    JsonUtils.toString(materialIdWithNewErpCode));
                            logger.info("详设变更单据审批通过回调的formInstanceId：{}，progressConfirmReceiptsAndDetailList：{}", formInstanceId,
                                    JsonUtils.toString(progressConfirmReceiptsAndDetailList));

                            List<ProjectWbsReceiptsDesignPlanRel> designPlanRelList =
                                    projectWbsReceiptsDesignPlanRelService.selectByReceiptsIds(new ArrayList<>(progressConfirmReceiptsAndDetailList.keySet()));
                            Map<Long, List<ProjectWbsReceiptsDesignPlanRel>> designPlanRelMap = CollectionUtils.isEmpty(designPlanRelList) ?
                                    new HashMap<>()
                                    :
                                    designPlanRelList.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsDesignPlanRel::getProjectWbsReceiptsId));

                            for (Long key : progressConfirmReceiptsAndDetailList.keySet()) {
                                List<ProjectWbsReceiptsDesignPlanRel> relByReceipts = designPlanRelMap.get(key);
                                List<MilepostDesignPlanDetailDto> progressConfirmDetailDtos = progressConfirmReceiptsAndDetailList.get(key);
                                for (MilepostDesignPlanDetailDto progressConfirmDetail : progressConfirmDetailDtos) {
                                    //把历史的更新
                                    List<ProjectWbsReceiptsDesignPlanRel> existRelList = CollectionUtils.isEmpty(relByReceipts) ? new ArrayList<>()
                                            : relByReceipts.stream().filter(rel -> rel.getDesignPlanDetailId().equals(progressConfirmDetail.getId()))
                                            .collect(Collectors.toList());
                                    BigDecimal purchaseNum = progressConfirmDetail.getNumber()
                                            .multiply(milepostDesignPlanDetailService.getParentSigmaById(progressConfirmDetail.getParentId(), null));
                                    if (ListUtils.isNotEmpty(existRelList)) {
                                        ProjectWbsReceiptsDesignPlanRel designPlanRel = existRelList.get(0);
                                        designPlanRel.setPurchaseNum(purchaseNum);
                                        updateDesignPlanRelList.add(designPlanRel);
                                    } else {//新的加入需求发布单里
                                        ProjectWbsReceiptsDesignPlanRel designPlanRel = new ProjectWbsReceiptsDesignPlanRel();
                                        designPlanRel.setPurchaseNum(purchaseNum);
                                        designPlanRel.setDesignPlanDetailId(progressConfirmDetail.getId());
                                        designPlanRel.setProjectWbsReceiptsId(key);
                                        designPlanRel.setProjectId(progressConfirmDetail.getProjectId());
                                        designPlanRel.setDeletedFlag(false);
                                        addDesignPlanRelList.add(designPlanRel);
                                    }
                                }

                                for (MilepostDesignPlanDetail planDetail : progressConfirmDetailDtos) {
                                    // 已被确认，跳过
                                    if (CollectionUtils.isNotEmpty(relByReceipts)
                                            && relByReceipts.stream().anyMatch(a -> a.getDesignPlanDetailId().equals(planDetail.getId())
                                            && !a.getProjectWbsReceiptsId().equals(key))) {
                                        continue;
                                    }

                                    planDetail.setStatus(CheckStatus.WBS_RECEIPTS_PASSED.code());

                                    finalPlanDetailList.add(planDetail);
                                }
                            }
                        }
                    }


                    // 需求发布前后的详设变更都会走的逻辑
                    // 更新project_wbs_receipts_design_plan_rel的关联关系；更新详细设计的status、moduleStatus和erpCode；物理删除详设
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，addDesignPlanRelList：{}", formInstanceId, JsonUtils.toString(addDesignPlanRelList));
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，updateDesignPlanRelList：{}", formInstanceId,
                            JsonUtils.toString(updateDesignPlanRelList));
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，finalPlanDetailList：{}", formInstanceId, JsonUtils.toString(finalPlanDetailList));
                    logger.info("详设变更单据审批通过回调的formInstanceId：{}，deleteDetailIds：{}", formInstanceId, JsonUtils.toString(deleteDetailIds));
                    if (ListUtils.isNotEmpty(addDesignPlanRelList)) {
                        projectWbsReceiptsDesignPlanRelExtMapper.batchInsert(addDesignPlanRelList);
                    }
                    if (ListUtils.isNotEmpty(updateDesignPlanRelList)) {
                        projectWbsReceiptsDesignPlanRelExtMapper.batchUpdate(updateDesignPlanRelList);
                    }
                    if (ListUtils.isNotEmpty(finalPlanDetailList)) {
                        milepostDesignPlanDetailExtMapper.batchUpdate(finalPlanDetailList);
                    }
                    if (ListUtils.isNotEmpty(deleteDetailIds)) {
                        milepostDesignPlanDetailService.deleteByDetailIds(deleteDetailIds);
                    }

                    // 详细设计发布审批通过后，往物料新增添和变更行表插入记录
                    MaterialAdjustHeader materialAdjustHeader = new MaterialAdjustHeader();
                    materialAdjustHeader.setApplyBy(receipt.getCreateBy());
                    UserInfo userInfo = CacheDataUtils.findUserById(receipt.getCreateBy());
                    if (userInfo != null) {
                        materialAdjustHeader.setApplyByName(userInfo.getName());
                    }
                    materialAdjustHeader.setApplyTime(receipt.getCreateAt());
                    materialAdjustHeader.setCreateBy(receipt.getCreateBy());
                    materialAdjustHeader.setCreateAt(new Date());
                    materialAdjustHeader.setUpdateBy(receipt.getCreateBy());
                    materialAdjustHeader.setUpdateAt(new Date());
                    // 生成单据号
                    Long unitId = projectExtMapper.getUnitIdByProjectId(receipt.getProjectId());
                    materialAdjustHeader.setAdjustCode(materialAdjustService.generateMaterialAdjustCode(unitId));
                    materialAdjustHeader.setDeletedFlag(Boolean.FALSE);
                    materialAdjustHeader.setStatus(MaterialAdjustEnum.APPROVED.code());
                    // 封装库存组织id
                    materialAdjustHeader.setOrganizationId(organizationId);
                    // 封装业务实体id
                    Long ouId = projectService.selectByPrimaryKey(receipt.getProjectId()).getOuId();
                    materialAdjustHeader.setOuId(ouId);
                    materialAdjustHeader.setAdjustType(MaterialAdjustEnum.MATERIAL_ADJUST_ADDED.code());
                    materialAdjustHeader.setResource(1);
                    materialAdjustHeader.setSyncStatus(MaterialAdjustEnum.SYNC_STATUS_SUCCESS.code());
                    materialAdjustHeader.setSyncMes("");
                    materialAdjustHeader.setProjectId(receipt.getProjectId());
                    materialAdjustService.insertHeader(materialAdjustHeader);

                    // 更新详设变更单据状态：生效
                    receipt.setRequirementStatus(RequirementStatusEnum.PROCESS.getCode());
                    receipt.setUpdateBy(createUserId);
                    receipt.setUpdateAt(new Date());
                    projectWbsReceiptsMapper.updateByPrimaryKeySelective(receipt);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            true);
                    try {
                        projectWbsReceiptsService.ProjectWbsDesignPlanAutoConfirm(receipt.getProjectId(),receipt.getId());
                    } catch (Exception e) {
                        logger.error("详设变更单据审批-执行自动确认失败", e);
                    }
                } catch (ApplicationBizException e) {
                    logger.info("详设变更单据审批通过回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("详设变更单据审批通过回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("详设变更单据审批通过回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }

    }

    private void checkPartConfirm(MilepostDesignPlanDetailDto detailDto, List<Long> checkIds, Map<String, Material> materialMap,
                                  Map<Long, String> materialIdWithNewErpCode, Long organizationId,
                                  Map<Long, List<ProjectWbsReceiptsBudgetChangeHistoryDto>> publishReceiptsMap,
                                  Map<Long, List<MilepostDesignPlanDetailDto>> progressConfirmReceiptsAndDetailList,
                                  Long checkReceiptsId, Long parentCheckReceiptsId, Long changeReceiptsId, List<Long> deleteDetailIds, Long formInstanceId) {
        logger.info("checkPartConfirm的formInstanceId：{}，detailDto：{}，checkIds：{}，materialMap：{}，materialIdWithNewErpCode：{}，" +
                        "organizationId：{}，publishReceiptsMap：{}，progressConfirmReceiptsAndDetailList：{}，checkReceiptsId：{}，parentCheckReceiptsId" +
                        "：{}" +
                        "，deleteDetailIds：{}", formInstanceId, JsonUtils.toString(detailDto), JsonUtils.toString(checkIds),
                JsonUtils.toString(materialMap), JsonUtils.toString(materialIdWithNewErpCode), organizationId, JsonUtils.toString(publishReceiptsMap),
                JsonUtils.toString(progressConfirmReceiptsAndDetailList), checkReceiptsId, parentCheckReceiptsId,
                JsonUtils.toString(deleteDetailIds));

        List<Long> partConfirmReceiptsIdList =
                publishReceiptsMap.keySet().stream().filter(a -> !Objects.equals(a, checkReceiptsId)).collect(Collectors.toList());
        logger.info("checkPartConfirm的formInstanceId：{}，partConfirmReceiptsIdList：{}", formInstanceId, partConfirmReceiptsIdList);
        List<Long> currentChangeReceiptsOfPublishDetails = new ArrayList<>();
        if (ListUtils.isNotEmpty(partConfirmReceiptsIdList)) {
            List<ProjectWbsReceiptsDesignPlanRel> projectWbsReceiptsDesignPlanRels =
                    projectWbsReceiptsDesignPlanRelService.selectByReceiptsIds(partConfirmReceiptsIdList);
            if (ListUtils.isNotEmpty(projectWbsReceiptsDesignPlanRels)) {
                currentChangeReceiptsOfPublishDetails.addAll(projectWbsReceiptsDesignPlanRels.stream()
                        .map(ProjectWbsReceiptsDesignPlanRel::getDesignPlanDetailId).collect(Collectors.toList()));
            }
        }
        logger.info("checkPartConfirm的formInstanceId：{}，currentChangeReceiptsOfPublishDetails：{}", formInstanceId,
                JsonUtils.toString(currentChangeReceiptsOfPublishDetails));

        List<MilepostDesignPlanDetailDto> sonDesignPlanDetails =
                BeanConverter.copy(milepostDesignPlanDetailService.selectByParentId(detailDto.getId()), MilepostDesignPlanDetailDto.class);
        logger.info("checkPartConfirm的formInstanceId：{}，sonDesignPlanDetails：{}", formInstanceId, JsonUtils.toString(sonDesignPlanDetails));

        List<Long> needConfirmedDetailIds = new ArrayList<>();
        //先查子集有没有已经部分确认的 修改对应预算
        if (ListUtils.isNotEmpty(sonDesignPlanDetails)) {
            for (MilepostDesignPlanDetailDto sonDetail : sonDesignPlanDetails) {
                if (!checkIds.contains(sonDetail.getId()) && !MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code().equals(sonDetail.getModuleStatus())
                        && currentChangeReceiptsOfPublishDetails.contains(sonDetail.getId())) {
                    needConfirmedDetailIds.add(sonDetail.getId());
                }
            }
            //如果有对应 先处理部分确认的(包括删除的详设)
            if (ListUtils.isNotEmpty(needConfirmedDetailIds)) {
                sonDesignPlanDetails = dealWithPartConfirm(formInstanceId, needConfirmedDetailIds, publishReceiptsMap, organizationId, materialMap,
                        materialIdWithNewErpCode, sonDesignPlanDetails, deleteDetailIds, changeReceiptsId);
            }
            logger.info("checkPartConfirm的formInstanceId：{}，最终sonDesignPlanDetails：{}", formInstanceId, JsonUtils.toString(sonDesignPlanDetails));

            if (Objects.nonNull(checkReceiptsId)) {
                List<ProjectWbsReceiptsDesignPlanRel> planRelList = projectWbsReceiptsDesignPlanRelService.getRelByReceiptsId(checkReceiptsId);
                // 当前详设生效的单据id 在关系表存在时（肯定存在）
                if (ListUtils.isNotEmpty(planRelList)) {
                    //当前所选模组对应需求发布单的所有详设id
                    for (MilepostDesignPlanDetailDto sonDetail : sonDesignPlanDetails) {
                        // 如果子详设有对应的生效的单据，key 就为生效单据id,否则key则去父详设对应生效的单据id
                        Long sonCheckReceiptsId = projectWbsReceiptsDesignPlanRelService.getReceiptsIdByDetailId(sonDetail.getId());
                        if (Objects.nonNull(sonCheckReceiptsId)) {
                            if (ListUtils.isNotEmpty(progressConfirmReceiptsAndDetailList.get(sonCheckReceiptsId))) {
                                progressConfirmReceiptsAndDetailList.get(sonCheckReceiptsId).add(sonDetail);
                            } else {
                                progressConfirmReceiptsAndDetailList.put(sonCheckReceiptsId, Lists.newArrayList(sonDetail));
                            }
                        } else {
                            if (ListUtils.isNotEmpty(progressConfirmReceiptsAndDetailList.get(checkReceiptsId))) {
                                progressConfirmReceiptsAndDetailList.get(checkReceiptsId).add(sonDetail);
                            } else {
                                progressConfirmReceiptsAndDetailList.put(checkReceiptsId, Lists.newArrayList(sonDetail));
                            }
                        }
                        if (checkIds.contains(sonDetail.getId())) {
                            checkPartConfirm(sonDetail, checkIds, materialMap, materialIdWithNewErpCode, organizationId, publishReceiptsMap,
                                    progressConfirmReceiptsAndDetailList, sonCheckReceiptsId, checkReceiptsId, changeReceiptsId, deleteDetailIds, formInstanceId);
                        }
                    }
                }
            } else {
                // 这种情况则是该详设的上级也是新增的
                for (MilepostDesignPlanDetailDto sonDetail : sonDesignPlanDetails) {
                    Long sonCheckReceiptsId = projectWbsReceiptsDesignPlanRelService.getReceiptsIdByDetailId(sonDetail.getId());
                    if (Objects.nonNull(sonCheckReceiptsId)) {
                        if (ListUtils.isNotEmpty(progressConfirmReceiptsAndDetailList.get(sonCheckReceiptsId))) {
                            progressConfirmReceiptsAndDetailList.get(sonCheckReceiptsId).add(sonDetail);
                        } else {
                            progressConfirmReceiptsAndDetailList.put(sonCheckReceiptsId, Lists.newArrayList(sonDetail));
                        }
                    } else {
                        // 要用父级的 checkReceiptsId，并且不能为空
                        if (Objects.nonNull(parentCheckReceiptsId)) {
                            if (ListUtils.isNotEmpty(progressConfirmReceiptsAndDetailList.get(parentCheckReceiptsId))) {
                                progressConfirmReceiptsAndDetailList.get(parentCheckReceiptsId).add(sonDetail);
                            } else {
                                progressConfirmReceiptsAndDetailList.put(parentCheckReceiptsId, Lists.newArrayList(sonDetail));
                            }
                        }
                    }
                    if (checkIds.contains(sonDetail.getId())) {
                        // 这里的 parentCheckReceiptsId 用最上层的父级 checkReceiptsId
                        checkPartConfirm(sonDetail, checkIds, materialMap, materialIdWithNewErpCode, organizationId, publishReceiptsMap,
                                progressConfirmReceiptsAndDetailList, sonCheckReceiptsId, parentCheckReceiptsId, changeReceiptsId, deleteDetailIds, formInstanceId);
                    }
                }
            }
        }

    }

    private List<MilepostDesignPlanDetailDto> dealWithPartConfirm(Long formInstanceId, List<Long> needConfirmedDetailIds,
                                                                  Map<Long, List<ProjectWbsReceiptsBudgetChangeHistoryDto>> publishReceiptsMap,
                                                                  Long organizationId, Map<String, Material> materialMap,
                                                                  Map<Long, String> materialIdWithNewErpCode,
                                                                  List<MilepostDesignPlanDetailDto> sonDesignPlanDetails,
                                                                  List<Long> deleteDetailIds, Long changeReceiptsId) {
        List<ProjectWbsReceiptsDesignPlanRel> projectWbsReceiptsDesignPlanRels =
                projectWbsReceiptsDesignPlanRelService.selectByDetailIds(needConfirmedDetailIds);
        Map<Long, List<ProjectWbsReceiptsDesignPlanRel>> needConfirmedPublishReceiptsMap = projectWbsReceiptsDesignPlanRels.stream()
                .collect(Collectors.groupingBy(ProjectWbsReceiptsDesignPlanRel::getProjectWbsReceiptsId));
        logger.info("详设变更单据审批通过回调的formInstanceId：{}，needConfirmedPublishReceiptsMap：{}，needConfirmedDetailIds：{}",
                formInstanceId, JsonUtils.toString(needConfirmedPublishReceiptsMap), JsonUtils.toString(needConfirmedDetailIds));
        ProjectWbsReceipts changeWbsReceipts = projectWbsReceiptsService.getById(changeReceiptsId); //变更单据
        for (Long key : needConfirmedPublishReceiptsMap.keySet()) {
            //预算生效
            List<ProjectWbsReceiptsBudgetChangeHistoryDto> projectWbsReceiptsBudgetChangeHistoryDtos = publishReceiptsMap.get(key);
            if (ListUtils.isNotEmpty(projectWbsReceiptsBudgetChangeHistoryDtos)) {
                for (ProjectWbsReceiptsBudgetChangeHistoryDto changeHistory : projectWbsReceiptsBudgetChangeHistoryDtos) {
                    ProjectWbsReceiptsBudgetExample example = new ProjectWbsReceiptsBudgetExample();
                    example.createCriteria().andProjectWbsReceiptsIdEqualTo(changeHistory.getProjectWbsPublishReceiptsId())
                            .andDemandTypeEqualTo(changeHistory.getDemandType()).andWbsSummaryCodeEqualTo(changeHistory.getWbsSummaryCode())
                            .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                    List<ProjectWbsReceiptsBudgetDto> budget = projectWbsReceiptsBudgetService.selectByExample(example);
                    if (ListUtils.isNotEmpty(budget)) {
                        budget.get(0).setBudgetOccupiedAmount(changeHistory.getBudgetOccupiedAmountAfter());
                        projectWbsReceiptsBudgetService.updateByPrimaryKeySelective(budget.get(0));
                    }
                }
            }
            List<ProjectWbsReceiptsDesignPlanRel> needConfirmedReceiptsDesignPlanRels = needConfirmedPublishReceiptsMap.get(key);
            ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsService.getById(key);
            MilepostDesignPlanDetailExample planDetailExample = new MilepostDesignPlanDetailExample();
            planDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                    .andIdIn(needConfirmedReceiptsDesignPlanRels.stream().map(ProjectWbsReceiptsDesignPlanRel::getDesignPlanDetailId).collect(Collectors.toList()));
            List<MilepostDesignPlanDetail> needConfirmedPlanDetailList = milepostDesignPlanDetailMapper.selectByExample(planDetailExample);
            // pamCpde
            List<String> pamCodeList = needConfirmedPlanDetailList.stream().map(MilepostDesignPlanDetail::getPamCode).collect(Collectors.toList());
            // 物料
            List<Material> materials = materialExtService.invokeMaterialApiGetByPamCodeList(pamCodeList, organizationId);
            for (Material material : materials) {
                materialMap.put(material.getPamCode(), material);
            }

            /** 批量统计采购需求 未采购量/总需求量/已采购量/关闭数量 **/
            List<Long> requirementIdList = new ArrayList<>();
            Map<Long, PurchaseMaterialRequirementDto> quantityInfoMap = new HashMap<>();
            Map<String, PurchaseMaterialRequirement> requirementMap = new HashMap<>();
            for (MilepostDesignPlanDetail planDetail : needConfirmedPlanDetailList) {
                // 已被确认，跳过
                if (needConfirmedReceiptsDesignPlanRels.stream().anyMatch(a -> a.getDesignPlanDetailId().equals(planDetail.getId())
                        && !a.getProjectWbsReceiptsId().equals(key))) {
                    continue;
                }
                PurchaseMaterialRequirementExample example = new PurchaseMaterialRequirementExample();
                PurchaseMaterialRequirementExample.Criteria criteria = example.createCriteria();
                criteria.andProjectIdEqualTo(planDetail.getProjectId());
                // 如果详细设计上面没有交货日期，就忽略。让下游业务 --采购合同控制
                if (null != planDetail.getDeliveryTime()) {
                    criteria.andDeliveryTimeEqualTo(planDetail.getDeliveryTime());
                }
                criteria.andPamCodeEqualTo(planDetail.getPamCode());
                criteria.andWbsSummaryCodeEqualTo(planDetail.getWbsSummaryCode());
                criteria.andDeletedFlagEqualTo(false);
                criteria.andRequirementCodeEqualTo(projectWbsReceipts.getRequirementCode());
                List<PurchaseMaterialRequirement> requirementList = purchaseMaterialRequirementMapper.selectByExample(example);

                /* 物料采购需求 */
                PurchaseMaterialRequirement requirement = CollectionUtils.isEmpty(requirementList) ? null : requirementList.get(0);
                if (requirement != null) {
                    requirementIdList.add(requirement.getId());
                    requirementMap.put(buildRequirementKey(projectWbsReceipts, planDetail), requirement);
                }
            }
            if (CollectionUtils.isNotEmpty(requirementIdList)) {
                quantityInfoMap = purchaseMaterialRequirementService.getDetailByIds(requirementIdList).stream()
                        .collect(Collectors.toMap(PurchaseMaterialRequirementDto::getId, Function.identity(), (a, b) -> a));
            }

            for (MilepostDesignPlanDetail planDetail : needConfirmedPlanDetailList) {
                // 已被确认，跳过
                if (needConfirmedReceiptsDesignPlanRels.stream().anyMatch(a -> a.getDesignPlanDetailId().equals(planDetail.getId())
                        && !a.getProjectWbsReceiptsId().equals(key))) {
                    continue;
                }

                planDetail.setStatus(CheckStatus.WBS_RECEIPTS_PASSED.code());

                // 模组确认状态：已确认
                planDetail.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());

                // 设置ERP物料编码
                setErpCode(planDetail, materialIdWithNewErpCode);

                // 产生采购需求
                generateRequirement(formInstanceId, planDetail, materialMap, projectWbsReceipts, changeWbsReceipts, deleteDetailIds, quantityInfoMap, requirementMap);
                // 更新详细设计
                milepostDesignPlanDetailMapper.updateByPrimaryKeySelective(planDetail);
            }
        }
        // 子级中去掉已经部分确认的详设
        return sonDesignPlanDetails.stream().filter(sonDetail -> !needConfirmedDetailIds.contains(sonDetail.getId())).collect(Collectors.toList());
    }

    private void fillParentId(MilepostDesignPlanDetailChange parentChange, MilepostDesignPlanDetailChange currentChange,
                              Map<Long, MilepostDesignPlanDetailChange> parentChangeMap, Map<Long, MilepostDesignPlanDetailChange> currentChangeMap,
                              List<Long> checkIds, List<Long> addChangeIdList) {
        Long parentDesignPlanDetailId = parentChange.getDesignPlanDetailId();
        // 一直递归按照详设层级的顺序的开始添加
        if (null == parentDesignPlanDetailId) {
            MilepostDesignPlanDetailChange grandChange = parentChangeMap.get(parentChange.getId());
            fillParentId(grandChange, parentChange, parentChangeMap, currentChangeMap, checkIds, addChangeIdList);
        } else {
            Long changeId = currentChange.getId();
            // 防止重复添加详设
            if (addChangeIdList.contains(changeId)) {
                return;
            }
            // 添加有上级详设id的当前change
            MilepostDesignPlanDetailDto currentMilepostDesignPlanDetail = BeanConverter.copyProperties(currentChange,
                    MilepostDesignPlanDetailDto.class);
            currentMilepostDesignPlanDetail.setId(null);
            currentMilepostDesignPlanDetail.setParentId(parentDesignPlanDetailId);
            // 用父级的moduleStatus
            currentMilepostDesignPlanDetail.setModuleStatus(currentChangeMap.get(parentChange.getId()).getModuleStatus());
            if (!Boolean.FALSE.equals(currentMilepostDesignPlanDetail.getWbsLastLayer())) {
                currentMilepostDesignPlanDetail.setStatus(CheckStatus.WBS_RECEIPTS_PASSED.code());
            }
            milepostDesignPlanDetailService.save(currentMilepostDesignPlanDetail, null);
            // 重新设置当前 添加生成的详设id 和模组状态
            MilepostDesignPlanDetailChange change = currentChangeMap.get(changeId);
            change.setDesignPlanDetailId(currentMilepostDesignPlanDetail.getId());
            change.setModuleStatus(currentMilepostDesignPlanDetail.getModuleStatus());
            currentChangeMap.put(changeId, change);
            if (Boolean.TRUE.equals(currentMilepostDesignPlanDetail.getWhetherModel())) {
                checkIds.add(currentMilepostDesignPlanDetail.getId());
            }
            addChangeIdList.add(changeId);
        }
    }

    /**
     * 生成erpCode
     */
    private void setErpCode(MilepostDesignPlanDetail planDetail, Map<Long, String> materialIdWithNewErpCode) {
        Long organizationId = projectService.getOrganizationIdByProjectId(planDetail.getProjectId());
        // 采购件才生成erp编码 看板物料 外购物料 是否外包=false,去除是否外包的判断,原判断逻辑：&& Boolean.FALSE.equals(planDetail.getExtIs())
        if (("看板物料".equals(planDetail.getMaterialCategory()) || "外购物料".equals(planDetail.getMaterialCategory()))) {
            planDetail.setErpCode(
                    materialExtService.handleNewMaterial(
                            planDetail.getErpCode(),
                            planDetail.getPamCode(),
                            planDetail.getMaterielType(),
                            organizationId,
                            materialIdWithNewErpCode,
                            planDetail.getWhetherModel())
            );
        }
    }

    /**
     * 产生采购需求
     *
     * @param planDetail
     * @param materialMap
     * @param receipts
     */
    private void generateRequirement(Long formInstanceId, MilepostDesignPlanDetail planDetail, Map<String, Material> materialMap,
                                     ProjectWbsReceipts receipts, ProjectWbsReceipts changeReceipts, List<Long> deleteDetailIds,
                                     Map<Long, PurchaseMaterialRequirementDto> quantityInfoMap,
                                     Map<String, PurchaseMaterialRequirement> requirementMap) {
        logger.info("MilepostDesignPlanChangeApprovalListener.generateRequirement的formInstanceId：{}，planDetail：{}，materialMap：{}，" +
                        "receipts：{}，deleteDetailIds：{}",
                formInstanceId, JsonUtils.toString(planDetail), JsonUtils.toString(materialMap), JsonUtils.toString(receipts),
                JsonUtils.toString(deleteDetailIds));

//        PurchaseMaterialRequirementExample example = new PurchaseMaterialRequirementExample();
//        PurchaseMaterialRequirementExample.Criteria criteria = example.createCriteria();
//        criteria.andProjectIdEqualTo(planDetail.getProjectId());
//        // 如果详细设计上面没有交货日期，就忽略。让下游业务 --采购合同控制
//        if (null != planDetail.getDeliveryTime()) {
//            criteria.andDeliveryTimeEqualTo(planDetail.getDeliveryTime());
//        }
//        criteria.andPamCodeEqualTo(planDetail.getPamCode());
//        criteria.andWbsSummaryCodeEqualTo(planDetail.getWbsSummaryCode());
//        criteria.andDeletedFlagEqualTo(false);
//        criteria.andRequirementCodeEqualTo(receipts.getRequirementCode());
//        List<PurchaseMaterialRequirement> requirementList = purchaseMaterialRequirementMapper.selectByExample(example);
//
//        /* 物料采购需求 */
//        PurchaseMaterialRequirement requirement = CollectionUtils.isEmpty(requirementList) ? null : requirementList.get(0);
        PurchaseMaterialRequirement requirement = requirementMap.get(buildRequirementKey(receipts, planDetail));
        logger.info("MilepostDesignPlanChangeApprovalListener.generateRequirement的formInstanceId：{}，requirement：{}",
                formInstanceId, JsonUtils.toString(requirement));
        if (null == requirement) {
            requirement = new PurchaseMaterialRequirementDto();
            // 生成物料采购需求
            requirement.setProjectId(planDetail.getProjectId());
            requirement.setErpCode(planDetail.getErpCode());
            requirement.setPamCode(planDetail.getPamCode());
            requirement.setDispatchIs(planDetail.getDispatchIs());
            requirement.setWbsSummaryCode(planDetail.getWbsSummaryCode());
            requirement.setActivityCode(planDetail.getActivityCode());
            requirement.setDeliveryTime(planDetail.getDeliveryTime());
            requirement.setChartVersion(planDetail.getChartVersion());
            Material material = materialMap.get(planDetail.getPamCode());
            if (null != material) {
                requirement.setMaterielId(material.getId());
                requirement.setMaterielDescr(material.getItemInfo());
            }
            requirement.setUnitCode(planDetail.getUnitCode());
            requirement.setUnit(planDetail.getUnit());
        }

        requirement.setDesignReleaseLotNumber(planDetail.getDesignReleaseLotNumber());
        // 详细设计部分确认和进度确认，只有外购物料才会产生采购需求
        if (null != planDetail.getNumber() && (Objects.equals("外购物料", planDetail.getMaterialCategory()))) {
            //计算当前设计信息的最新发布总量: 设计信息的数量 * 所有父级的积数
            BigDecimal parentSigmaById = milepostDesignPlanDetailService.getParentSigmaById(planDetail.getParentId(), null);
            logger.info("MilepostDesignPlanChangeApprovalListener.generateRequirement的formInstanceId：{}，planDetailId：{}，parentSigmaById：{}",
                    formInstanceId, planDetail.getId(), parentSigmaById);
            BigDecimal differenceNumber = planDetail.getNumber().multiply(parentSigmaById);

            BigDecimal theLastNeedTotal = differenceNumber;
            if (null != requirement.getNeedTotal()) {
                theLastNeedTotal = theLastNeedTotal.subtract(requirement.getNeedTotal());
            }

            if (deleteDetailIds.contains(planDetail.getId())) {
                theLastNeedTotal = BigDecimal.ZERO.subtract(Optional.ofNullable(requirement.getNeedTotal()).orElse(BigDecimal.ZERO));
                requirement.setNeedTotal(BigDecimal.ZERO);
            } else {
                requirement.setNeedTotal(differenceNumber);
            }
            requirement.setApprovedSupplierNumber(0);
            requirement.setProjectWbsReceiptsId(receipts.getId());
            requirement.setRequirementCode(receipts.getRequirementCode());
            requirement.setPurchaseType(Boolean.TRUE.equals(planDetail.getExtIs()) ? PurchaseMaterialRequirementPurchaseTypeEnums.OUTSOURCE.getCode()
                    : PurchaseMaterialRequirementPurchaseTypeEnums.WBS.getCode());

            /* 获取wbs下的预算占用金额（外包） */
            BigDecimal wbsBudgetOccupiedAmount = projectWbsReceiptsBudgetService.getWbsBudgetOccupiedAmountByOs(receipts.getId(),
                    planDetail.getWbsSummaryCode());
            // 外包金额
            requirement.setWbsDemandOsCost(wbsBudgetOccupiedAmount);
            requirement.setDeletedFlag(false);
            requirement.setRequirementType(planDetail.getRequirementType());

            /* 保存 */
            if (null == requirement.getId()) {
                // 状态已关闭--总需求量=0
                if (BigDecimal.ZERO.compareTo(requirement.getNeedTotal()) == 0) {
                    requirement.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
                } else {
                    // 状态待采购--新增的采购需求=待采购
                    requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
                }
                requirement.setCreateBy(receipts.getProducerId());
                purchaseMaterialRequirementMapper.insertSelective(requirement);
            } else {
                Project project = projectService.selectByPrimaryKey(planDetail.getProjectId());
                requirement.setUpdateBy(receipts.getProducerId());
                purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(requirement);

                //更新采购需求状态(如果项目已结项/终止，不再更新)
                if (!Objects.equals(ProjectStatus.CLOSE.getCode(), project.getStatus()) && !Objects.equals(ProjectStatus.TERMINATION.getCode(), project.getStatus())) {
                    //如果是WBS类型
                    if (Objects.equals(requirement.getPurchaseType(), PurchaseMaterialRequirementPurchaseTypeEnums.WBS.getCode())) {
                        PurchaseMaterialRequirementDto quantityInfoDto = quantityInfoMap.get(requirement.getId());
                        if (quantityInfoDto != null) {
                            // 未下达量的统计是在needTotal更新前查询的，需要追加本次变更差异值，否则采购需求状态判断不对
                            BigDecimal changeNeedTotal = requirement.getNeedTotal().subtract(quantityInfoDto.getNeedTotal());
                            quantityInfoDto.setUnreleasedAmount(quantityInfoDto.getUnreleasedAmount().add(changeNeedTotal));
                            quantityInfoDto.setNeedTotal(requirement.getNeedTotal()); //needTotal在前面已更新，此处需要更新
                            purchaseMaterialRequirementService.updateStatus(quantityInfoDto);
                        }
//                        purchaseMaterialRequirementService.batchUpdateStatus(Arrays.asList(requirement.getId()));
                        //如果是外包类型
                    } else if (Objects.equals(requirement.getPurchaseType(), PurchaseMaterialRequirementPurchaseTypeEnums.OUTSOURCE.getCode())) {
                        //已采购量
                        BigDecimal releasedQuantity = purchaseContractBudgetExtMapper.calculateReleasedQuantity(requirement.getId());
                        //未采购量 = 总需求量 - 关闭数量 - 已采购量
                        BigDecimal unreleasedAmount = requirement.getNeedTotal().subtract(
                                Optional.ofNullable(requirement.getClosedQuantity()).orElse(BigDecimal.ZERO)).subtract(releasedQuantity);
                        logger.info("MilepostDesignPlanChangeApprovalListener.generateRequirement的unreleasedAmount：{}，needTotal：{}，" +
                                        "closedQuantity：{}，releasedQuantity：{}",
                                unreleasedAmount, requirement.getNeedTotal(), requirement.getClosedQuantity(), releasedQuantity);
                        if (unreleasedAmount.compareTo(BigDecimal.ZERO) == 0) {
                            requirement.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
                        } else {
                            requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
                        }
                        purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(requirement);
                    }
                }
            }

            // 添加采购需求发布明细（WBS）
            purchaseMaterialRequirementService.addWbsPurchaseMaterialReleaseDetail(requirement, receipts, changeReceipts, planDetail,
                    ReleaseDetailStatus.CHANGE.code(), receipts.getProducerId(), theLastNeedTotal, receipts.getProducerId());

            // 产生采购需求
            planDetail.setGenerateRequirement(DesignPlanDetailGenerateRequire.ALREADY_PRODUCED.code());
        }
    }

    private String buildRequirementKey(ProjectWbsReceipts receipts, MilepostDesignPlanDetail planDetail) {
        return receipts.getRequirementCode() + "@" + planDetail.getId();
    }
}
