package com.midea.pam.ctc.service.listener;

import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.event.ProjectContractChangeWorkflowCallbackApprovalEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

public class ProjectContractChangeWorkflowCallbackApprovalListener implements ApplicationListener<ProjectContractChangeWorkflowCallbackApprovalEvent> {

    private final static Logger logger = LoggerFactory.getLogger(ProjectContractChangeWorkflowCallbackApprovalListener.class);

    private final static Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Resource
    private ProjectBusinessService projectBusinessService;

    @Resource
    private WorkflowCallbackService workflowCallbackService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    @Async
    public void onApplicationEvent(ProjectContractChangeWorkflowCallbackApprovalEvent event) {
        logger.info("项目关联合同变更审批通过回调的异步处理参数为:{}", JsonUtils.toString(event));

        pass(event.getProjectDto());
    }

    @Transactional(rollbackFor = Exception.class)
    public void pass(ProjectDto projectDto) {
        Long formInstanceId = projectDto.getFormInstanceId();
        String fdInstanceId = projectDto.getFdInstanceId();
        String formUrl = projectDto.getFormUrl();
        String eventName = projectDto.getEventName();
        String handlerId = projectDto.getHandlerId();
        Long companyId = projectDto.getCompanyId();
        Long createUserId = projectDto.getCreateUserId();
        logger.info("项目关联合同变更审批通过回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("ProjectContractChangeWorkflowCallbackApproval_pass_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getFormInstance(formInstanceId, formUrl);

                    projectBusinessService.contractChangeApprovedHandler(formInstanceId, companyId, createUserId);
                    //更新wbs预算快照事件
                    projectBusinessService.publishProjectWbsBudgetVersion(formInstanceId);

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName, formInstance,
                            true);
                } catch (ApplicationBizException e) {
                    logger.info("项目关联合同变更审批通过回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("项目关联合同变更审批通过回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("项目关联合同变更审批通过回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }
}
