package com.midea.pam.ctc.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.entity.VendorSiteBank;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.PaymentPlanDTO;
import com.midea.pam.common.ctc.dto.SdpReceivePaymentApplyHandleResultDTO;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentApplyExample;
import com.midea.pam.common.ctc.entity.PaymentApplyExternalAttachment;
import com.midea.pam.common.ctc.entity.PaymentApplyExternalAttachmentExample;
import com.midea.pam.common.ctc.entity.PaymentPlan;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractExample;
import com.midea.pam.common.ctc.entity.SdpLog;
import com.midea.pam.common.enums.AuditStatus;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.GSCSubmitOperationTypeEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PaymentApplySourceNameEnum;
import com.midea.pam.common.enums.PaymentGscInvoiceRequestStatusEnum;
import com.midea.pam.common.enums.SdpLogOperaTypeEnum;
import com.midea.pam.common.enums.SdpLogStatusEnum;
import com.midea.pam.common.enums.TopicCodeEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.sdp.dto.SdpReceivePaymentApplyInfo;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.mapper.PaymentApplyExternalAttachmentExtMapper;
import com.midea.pam.ctc.mapper.PaymentApplyExternalAttachmentMapper;
import com.midea.pam.ctc.mapper.PaymentApplyMapper;
import com.midea.pam.ctc.mapper.PaymentPlanExtMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.sdp.service.SdpLogService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.SdpReceivePaymentApplyService;
import com.midea.pam.system.SystemContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidationException;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

public class SdpReceivePaymentApplyServiceImpl implements SdpReceivePaymentApplyService {

    private static final Logger logger = LoggerFactory.getLogger(SdpReceivePaymentApplyServiceImpl.class);

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private PurchaseContractService purchaseContractService;

    @Resource
    private PaymentPlanExtMapper paymentPlanExtMapper;

    @Resource
    private PaymentApplyMapper paymentApplyMapper;

    @Resource
    private PaymentApplyService paymentApplyService;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private BasedataExtService basedataExtService;

    @Resource
    private SdpLogService sdpLogService;

    @Resource
    private PaymentApplyExternalAttachmentMapper paymentApplyExternalAttachmentMapper;

    @Resource
    private PaymentApplyExternalAttachmentExtMapper paymentApplyExternalAttachmentExtMapper;

    @Resource
    private OrganizationCustomDictService organizationCustomDictService;

    /**
     * 记录业务校验失败日志
     * @param errorMessage 原始错误消息
     * @param sourceSerialNum 源系统流水号
     * @param failedParam 校验失败的参数名称
     * @param requestParamValue 请求参数值
     * @param systemParamValue 系统参数值
     */
    private void logBizValidationError(String errorMessage, String sourceSerialNum,
                                       String failedParam, String requestParamValue, String systemParamValue) {
        logger.error("{},sourceSerialNum:{}, 校验失败参数:{}, 请求参数值:{}, 系统参数值:{}",
                errorMessage, sourceSerialNum, failedParam, requestParamValue, systemParamValue);
    }

    /**
     * 付款申请写入   文档地址：https://confluence.midea.com/pages/viewpage.action?pageId=262669740
     * @param sdpReceivePaymentApplyInfoList
     * @param sourceSerialNum
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SdpReceivePaymentApplyHandleResultDTO> paymentApplyHandle(List<SdpReceivePaymentApplyInfo> sdpReceivePaymentApplyInfoList, String sourceSerialNum){
        ArrayList<SdpReceivePaymentApplyHandleResultDTO> resultDTOS = new ArrayList<>();
        if(ListUtils.isEmpty(sdpReceivePaymentApplyInfoList)) return resultDTOS;
        logger.info("SDP付款申请接收接口处理开始,sourceSerialNum:{},参数:{}",sourceSerialNum, JsonUtils.toString(sdpReceivePaymentApplyInfoList));
        for (SdpReceivePaymentApplyInfo sdpReceivePaymentApplyInfo : sdpReceivePaymentApplyInfoList) {
            //设置返回结果供在gateway服务自动发起流程
            SdpReceivePaymentApplyHandleResultDTO resultDTO = new SdpReceivePaymentApplyHandleResultDTO();
            SdpLog sdpLog = buildSdpLog(sourceSerialNum, sdpReceivePaymentApplyInfo);
            try {
                validateContractInfo(sdpReceivePaymentApplyInfo);
                String purchaseCode = sdpReceivePaymentApplyInfo.getPurchaseCode();
                Long ouId = sdpReceivePaymentApplyInfo.getOuId();
                String paymentPlanCode = sdpReceivePaymentApplyInfo.getPaymentPlanCode();
                List<PurchaseContract> purchaseContractList = getPurchaseContractByCode(purchaseCode, ouId);
                PaymentPlan paymentPlan = paymentPlanExtMapper.getByPaymentPlanCode(paymentPlanCode);
                List<DictDto> fixedPaymentMethodList = getPaymentMethodList();
                //获取供应商信息
                VendorSiteBank vendorSiteBank = getVendorSiteBank(ouId,sdpReceivePaymentApplyInfo.getVendorCode(),sdpReceivePaymentApplyInfo.getErpVendorSiteId());

                List<PaymentApply> gscPaymentApplyList = getGscPaymentApply(sdpReceivePaymentApplyInfo.getPaymentApplyCode());

                //GSC传入审批状态为作废时 PAM将对应的付款申请审批状态设置为作废
                if(ListUtils.isNotEmpty(gscPaymentApplyList) && Objects.equals(sdpReceivePaymentApplyInfo.getOperationType(), GSCSubmitOperationTypeEnum.ABANDON.getCode())){
                    PaymentApply paymentApply = gscPaymentApplyList.get(0);
                    Integer auditStatus = paymentApply.getAuditStatus();
                    if(Objects.equals(auditStatus, AuditStatus.REFUSE.getCode()) && Objects.equals(paymentApply.getSourceSystemName(), PaymentApplySourceNameEnum.GSC.getCode())){
                        paymentApply.setAuditStatus(AuditStatus.CANCEL.getCode());
                        paymentApplyMapper.updateByPrimaryKeySelective(paymentApply);
                        resultDTO.setPaymentApplyId(paymentApply.getId());
                        resultDTO.setUnitId(CacheDataUtils.getTopUnitIdByOuId(paymentApply.getOuId()));
                        resultDTO.setIsSuccess(Boolean.TRUE);
                        resultDTO.setAbandonFlow(true);
                        sdpLog.setApplyId(paymentApply.getId());
                        sdpLog.setStatus(SdpLogStatusEnum.RECEIVE_HANDLE_SUCCESS.getCode());
                        resultDTOS.add(resultDTO);
                        logger.info("SDP付款申请接收接口处理-单据作废,formId:{},businessId:{},formAuditStatus:{}", paymentApply.getId(),sourceSerialNum,paymentApply.getAuditStatus());
                        return resultDTOS;
                    }else{

                        resultDTO.setIsSuccess(Boolean.FALSE);
                        logger.error("SDP付款申请接收接口处理失败-单据审批状态不为驳回,formId:{},formAuditStatus:{},businessId:{}",paymentApply.getId(),auditStatus,sourceSerialNum);
                        throw new BizException(Code.ERROR, "PAM已有相同的单据在审批或审批通过");
                    }
                }


                boolean vendorNotMatch = isVendorNotMatch(sdpReceivePaymentApplyInfo, vendorSiteBank);
                // 合同编号、业务实体ID、付款计划编号与PAM有对应关系 与 供应商信息与PAM一致性 校验
                if(ListUtils.isEmpty(purchaseContractList) || Objects.isNull(paymentPlan) || vendorNotMatch){
                    String failedParam = "";
                    String requestParamValue = "";
                    String systemParamValue = "";

                    if(ListUtils.isEmpty(purchaseContractList)) {
                        failedParam = "采购合同";
                        requestParamValue = "purchaseCode=" + purchaseCode + ", ouId=" + ouId;
                        systemParamValue = "无匹配记录";
                    } else if(Objects.isNull(paymentPlan)) {
                        failedParam = "付款计划";
                        requestParamValue = "paymentPlanCode=" + paymentPlanCode;
                        systemParamValue = "无匹配记录";
                    } else if(vendorNotMatch) {
                        failedParam = "供应商信息";
                        requestParamValue = "vendorCode=" + sdpReceivePaymentApplyInfo.getVendorCode()
                                + ", erpVendor=" + sdpReceivePaymentApplyInfo.getErpVendor()
                                + ", erpVendorId=" + sdpReceivePaymentApplyInfo.getErpVendorId()
                                + ", erpVendorSiteId=" + sdpReceivePaymentApplyInfo.getErpVendorSiteId()
                                + ", bankPaymentMethod=" + sdpReceivePaymentApplyInfo.getBankPaymentMethod();
                        systemParamValue = Objects.nonNull(vendorSiteBank) ?
                                "vendorCode=" + vendorSiteBank.getVendorCode()
                                        + ", vendorName=" + vendorSiteBank.getVendorName()
                                        + ", erpVendorId=" + vendorSiteBank.getErpVendorId()
                                        + ", erpVendorSiteId=" + vendorSiteBank.getErpVendorSiteId()
                                        + ",paymentMethodCode=" + vendorSiteBank.getPaymentMethodCode()
                                : "无匹配记录";
                    }

                    logBizValidationError("SDP付款申请接收接口处理失败-没有找到对应的采购合同或付款计划,purchaseCode:" + purchaseCode + ",paymentPlanCode:" + paymentPlanCode,
                            sourceSerialNum, failedParam, requestParamValue, systemParamValue);
                    throw new BizException(Code.ERROR,"存在合同编号、业务实体ID、付款计划编号、供应商信息与PAM不一致，请检查修改后再提交");
                }

                PurchaseContract purchaseContract = purchaseContractList.get(0);

                //校验付款申请的币种与PAM是否一致，且如果非本位币时，需要进一步校验外汇付款信息
                boolean currencyNotMatch = !Objects.equals(sdpReceivePaymentApplyInfo.getCurrency(), purchaseContract.getCurrency());

                //付款方式和供应商银行账号相关信息是否与PAM一致
                boolean paymentMethodNotMatch = !isPaymentMethodMatch(sdpReceivePaymentApplyInfo, fixedPaymentMethodList, vendorSiteBank);

                if(currencyNotMatch || paymentMethodNotMatch){
                    String failedParam = "";
                    String requestParamValue = "";
                    String systemParamValue = "";

                    if(currencyNotMatch) {
                        failedParam = "币种";
                        requestParamValue = "currency=" + sdpReceivePaymentApplyInfo.getCurrency();
                        systemParamValue = "currency=" + purchaseContract.getCurrency();
                    } else if(paymentMethodNotMatch) {
                        failedParam = "付款方式或供应商银行账号";
                        requestParamValue = "paymentMethod=" + sdpReceivePaymentApplyInfo.getPaymentMethod()
                                + ", vendorBankNum=" + sdpReceivePaymentApplyInfo.getVendorBankNum();
                        systemParamValue = "paymentMethod=" + fixedPaymentMethodList.stream().map(DictDto::getCode).collect(Collectors.joining(","))
                                + ", bankAccount=" + vendorSiteBank.getBankAccount();
                    }

                    logBizValidationError("SDP付款申请接收接口处理失败-付款申请内容与PAM不一致",
                            sourceSerialNum, failedParam, requestParamValue, systemParamValue);
                    throw new BizException(Code.ERROR,"付款申请内容与PAM不一致，请检查修改后再提交");
                }

                //检查当前的合同付款计划是不是已有进行中的付款申请流程在审批中
                boolean paymentApplyExistPending = paymentApplyCheck(paymentPlan);
                if(paymentApplyExistPending){
                    // 查找待审批的付款申请
                    PaymentApplyExample paymentApplyExample = new PaymentApplyExample();
                    paymentApplyExample.createCriteria().andPaymentPlanIdEqualTo(paymentPlan.getId()).andDeletedFlagEqualTo(0);
                    List<String> pendingApplications = paymentApplyMapper.selectByExample(paymentApplyExample).stream()
                            .filter(app -> Objects.equals(app.getAuditStatus(), AuditStatus.PENDING.getCode()))
                            .map(app -> String.valueOf(app.getId()))
                            .collect(Collectors.toList());

                    String failedParam = "付款申请状态";
                    String requestParamValue = "paymentPlanId=" + paymentPlan.getId() + ", 希望发起新申请";
                    String systemParamValue = "存在未审批申请：" + String.join(",", pendingApplications);

                    logBizValidationError("SDP付款申请接收接口处理失败-该付款计划当前有未审批的付款申请，不可以发起新的付款申请",
                            sourceSerialNum, failedParam, requestParamValue, systemParamValue);
                    throw new BizException(Code.ERROR,"该付款计划当前有未审批的付款申请，不可以发起新的付款申请");
                }

                if(Objects.equals(paymentPlan.getPaymentApplySource(), PaymentApplySourceNameEnum.PAM.getName())){
                    String failedParam = "付款申请来源";
                    String requestParamValue = "来源=GSC";
                    String systemParamValue = "付款计划来源=" + paymentPlan.getPaymentApplySource();

                    logBizValidationError("SDP付款申请接收接口处理失败-该付款计划已在PAM发起付款申请，不可在GSC发起",
                            sourceSerialNum, failedParam, requestParamValue, systemParamValue);
                    throw new BizException(Code.ERROR,"该付款计划已在PAM发起付款申请，不可在GSC发起");
                }

                //校验PAM的付款申请是否有相同的GSC付款申请编号
                boolean isRepeatRequest = checkPaymentApplyRepeatRequest(gscPaymentApplyList);
                if(isRepeatRequest){
                    SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
                    filter.getIncludes().add("paymentApplyCode");
                    filter.getIncludes().add("auditStatus");
                    logger.error("SDP付款申请接收接口处理失败-PAM已存在相同单据，不可再发起，paymentApplyCode:{},paymentApplyList:{}"
                            , sdpReceivePaymentApplyInfo.getPaymentApplyCode(), JSONObject.toJSONString(gscPaymentApplyList, filter));
                    throw new BizException(Code.ERROR,"PAM已存在相同单据，不可再发起");
                }

                //检查 本次申请付款金额有没超额，申请付款金额是否小于等于剩余付款金额
                Optional<PaymentApply> refusePaymentApplyOptional = Optional.ofNullable(gscPaymentApplyList)
                        .filter(list -> !list.isEmpty())
                        .flatMap(list -> list.stream()
                                .filter(payment -> Objects.equals(AuditStatus.REFUSE.getCode(),payment.getAuditStatus()))
                                .findFirst());
                boolean exceedSurplusAmount = checkTaxPayIncludedPrice(sdpReceivePaymentApplyInfo, paymentPlan,refusePaymentApplyOptional.map(PaymentApply::getTaxPayIncludedPrice).orElse(BigDecimal.ZERO));
                if(exceedSurplusAmount){
                    BigDecimal surplusAmount = BigDecimal.ZERO;
                    List<PaymentPlanDTO> paymentPlanDTOS = paymentPlanExtMapper.calculateSurplusAmount(paymentPlan.getId());
                    if(ListUtils.isNotEmpty(paymentPlanDTOS)){
                        PaymentPlanDTO surplusPaymentPlanInfo = paymentPlanDTOS.get(0);
                        surplusAmount = Optional.ofNullable(surplusPaymentPlanInfo.getSurplusAmount()).orElse(BigDecimal.ZERO);
                    }

                    String failedParam = "申请付款金额";
                    String requestParamValue = "taxPayIncludedPrice=" + sdpReceivePaymentApplyInfo.getTaxPayIncludedPrice();
                    String systemParamValue = "剩余可用金额=" + surplusAmount;

                    logBizValidationError("SDP付款申请接收接口处理失败-申请付款金额大于剩余付款金额，请检查修改后再提交",
                            sourceSerialNum, failedParam, requestParamValue, systemParamValue);
                    throw new BizException(Code.ERROR,"申请付款金额大于剩余付款金额，请检查修改后再提交");
                }

                //本次GSC付款申请编号是否首次创建
                AtomicBoolean isFirstCreate= new AtomicBoolean(true);

                //构建当前用户信息
                buildUserInfo(ouId);

                //创建付款申请
                PaymentApplyDto paymentApplyDto = new PaymentApplyDto();
                refusePaymentApplyOptional.ifPresent(gscPaymentApply->{
                    isFirstCreate.set(false);
                    paymentApplyDto.setId(gscPaymentApply.getId());
                });
                paymentApplyDto.setPurchaseContractId(purchaseContract.getId());
                paymentApplyDto.setRequirement(paymentPlan.getRequirement());
                fixedPaymentMethodList.stream().filter(d -> Objects.equals(d.getCode(), sdpReceivePaymentApplyInfo.getPaymentMethod())).findFirst().ifPresent(d->{
                    paymentApplyDto.setPaymentMethodName(d.getName());
                });
                paymentApplyDto.setActualAmount(paymentPlan.getActualAmount());
                paymentApplyDto.setNum(paymentPlan.getNum());
                paymentApplyDto.setCurrency(purchaseContract.getCurrency());
                paymentApplyDto.setConversionRate(purchaseContract.getConversionRate());
                paymentApplyDto.setConversionDate(purchaseContract.getConversionDate());
                paymentApplyDto.setConversionType(purchaseContract.getConversionType());
                paymentApplyDto.setPurchaseContractCode(purchaseContract.getCode());
                paymentApplyDto.setPurchaseContractName(purchaseContract.getName());
                paymentApplyDto.setOuId(ouId);
                OperatingUnitDto operatingUnitDto = CacheDataUtils.findOuById(ouId);
                if(Objects.nonNull(operatingUnitDto)){
                    paymentApplyDto.setOuName(operatingUnitDto.getOperatingUnitName());
                }
                paymentApplyDto.setVendorId(sdpReceivePaymentApplyInfo.getErpVendorId());
                paymentApplyDto.setVendorCode(sdpReceivePaymentApplyInfo.getVendorCode());
                paymentApplyDto.setVendorName(sdpReceivePaymentApplyInfo.getErpVendor());
                paymentApplyDto.setErpVendorSiteId(sdpReceivePaymentApplyInfo.getErpVendorSiteId());
                paymentApplyDto.setVendorSiteCode(vendorSiteBank.getVendorSiteCode());
                paymentApplyDto.setProjectId(purchaseContract.getProjectId());
                Project project = projectMapper.selectByPrimaryKey(purchaseContract.getProjectId());
                if(Objects.nonNull(project)){
                    paymentApplyDto.setProjectCode(project.getCode());
                    paymentApplyDto.setProjectName(project.getName());
                }
                paymentApplyDto.setContractTypeName(purchaseContract.getTypeName());
                paymentApplyDto.setIsCharge(1);
                paymentApplyDto.setInvoiceMethod(1);
                paymentApplyDto.setDateEntry(new Date());
                paymentApplyDto.setVendorBankNum(sdpReceivePaymentApplyInfo.getVendorBankNum());
                paymentApplyDto.setPaymentMethod(vendorSiteBank.getPaymentMethodName());//取供应商付款方式
                paymentApplyDto.setAccountNum(vendorSiteBank.getAccountNum());
                paymentApplyDto.setAccountName(vendorSiteBank.getAccountName());
                //备注字段内容，按照预定规则自动生成，GSC传过来的备注内容不接，规则：PAM预付款申请 申请人：取采购合同采购跟进人 PAM合同号：取采购合同编号 项目号：取采购合同关联项目编号，
                //   示例：PAM预付款申请 申请人：黄萌 PAM合同号：KCG25030014 项目号：SPO25003
                paymentApplyDto.setRemark("PAM预付款申请 申请人：" + purchaseContract.getPurchasingFollowerName() + " PAM合同号：" + purchaseContract.getCode() + " 项目号：" + project.getCode());
                paymentApplyDto.setTaxPayIncludedPrice(sdpReceivePaymentApplyInfo.getTaxPayIncludedPrice());
                paymentApplyDto.setPaymentPlanId(paymentPlan.getId());
                //设置PaymentMethodId
                fixedPaymentMethodList.stream().filter(p->Objects.equals(p.getCode(),sdpReceivePaymentApplyInfo.getPaymentMethod())).findFirst().ifPresent(p->{
                    paymentApplyDto.setPaymentMethodId(p.getId());
                });

                //TODO START 项目预算号 待feature/20250321/AAIG030590_budget_project_number_ex_wuyh42分支上线后，反合分支后改造
                Dict typeAndCodedict = basedataExtService.findDictByTypeAndCode("organization_custom_dict", "11");
                if (typeAndCodedict != null) {
                    if (ouId != null && ouId > 0) {
                        OrganizationCustomDict organizationCustomDict = basedataExtService.getOrganizationCustomDictByName(ouId, OrgCustomDictOrgFrom.OU.code(), typeAndCodedict.getName());
                        if (organizationCustomDict != null) {
                            paymentApplyDto.setItemNumber(organizationCustomDict.getValue());//预算项目号
                            paymentApplyDto.setItemNumberName(typeAndCodedict.getName());//预算号名称
                        }
                    }
                }
                // TODO END

                //设置来源名称和编码
                paymentApplyDto.setSourceSystemCode(sdpReceivePaymentApplyInfo.getPaymentApplyCode());
                paymentApplyDto.setSourceSystemName(PaymentApplySourceNameEnum.GSC.getName());

                PaymentApplyDto paymentApplyDtoResult = paymentApplyService.saveOrUpdate(paymentApplyDto);

                //更新付款计划来源
                if(isFirstCreate.get()){
                    paymentPlanExtMapper.updatePaymentApplySource(paymentPlan.getId(), PaymentApplySourceNameEnum.GSC.getCode());
                }

                //附件处理
                uploadFileHandle(sdpReceivePaymentApplyInfo, paymentApplyDto,isFirstCreate.get());

                resultDTO.setPaymentApplyId(paymentApplyDtoResult.getId());
                resultDTO.setPaymentApplyCode(paymentApplyDto.getPaymentApplyCode());
                resultDTO.setProjectCode(paymentApplyDto.getProjectCode());
                resultDTO.setPurchaseContractName(purchaseContract.getName());
                resultDTO.setIsSuccess(Boolean.TRUE);
                // 获取付款计划的预付款标识
                Byte prepaymentFlag = paymentPlan.getPrepaymentFlag();
                boolean isPrepayment = Objects.nonNull(prepaymentFlag) && prepaymentFlag == 1;
                boolean isThirdParty = paymentApplyDto.getPaymentMethodName().contains("第三方");

                logger.info("付款申请分支判断 - 付款方式:{}, 是否第三方:{}, 预付款标识:{}, 是否预付款:{}, sourceSerialNum:{}",
                        paymentApplyDto.getPaymentMethodName(), isThirdParty, prepaymentFlag, isPrepayment, sourceSerialNum);

                // 分支A：付款方式为非第三方 或 (付款方式为第三方 且 是否预付款=否)
                // 分支B：付款方式为第三方 且 是否预付款=是
                if(isThirdParty && isPrepayment){
                    resultDTO.setThirdPartyTicket(3); // 分支B
                    logger.info("付款申请分支判断结果 - 执行分支B(ThirdPartyTicket=3): 付款方式为第三方且是预付款, sourceSerialNum:{}", sourceSerialNum);
                }else if((isPrepayment & !isThirdParty) || !isPrepayment){
                    resultDTO.setThirdPartyTicket(1); // 分支A
                    logger.info("付款申请分支判断结果 - 执行分支A(ThirdPartyTicket=1): {}是预付款且{}不是第三方付款方式, 或{}是预付款, sourceSerialNum:{}",
                            isPrepayment ? "" : "不", isThirdParty ? "" : "不", isPrepayment ? "" : "不", sourceSerialNum);
                }

                //付款申请合同原件控制校验
                validateContractOriginalAttachment(purchaseContract, sourceSerialNum);

                //设置项目经理MIP
                if(Objects.nonNull(purchaseContract.getManager())){
                    UserInfo managerUserInfo = CacheDataUtils.findUserById(purchaseContract.getManager());
                    resultDTO.setManagerMip(managerUserInfo.getUsername());
                }

                //设置采购跟进人MIP
                if(Objects.nonNull(purchaseContract.getPurchasingFollower())){
                    UserInfo purchasingFollowerUserInfo = CacheDataUtils.findUserById(purchaseContract.getPurchasingFollower());
                    resultDTO.setPurchasingFollower(purchasingFollowerUserInfo.getUsername());
                }

                //设置项目财务人员
                if(Objects.nonNull(project.getFinancial())){
                    UserInfo projectFinanceUserInfo = CacheDataUtils.findUserById(project.getFinancial());
                    resultDTO.setProjectFinance(projectFinanceUserInfo.getUsername());
                }
                Long topUnitId = CacheDataUtils.getTopUnitIdByOuId(ouId);
                resultDTO.setUnitId(topUnitId);
                sdpLog.setApplyId(paymentApplyDtoResult.getId());
                sdpLog.setStatus(SdpLogStatusEnum.RECEIVE_HANDLE_SUCCESS.getCode());
                logger.info("SDP付款申请接收接口处理-付款申请写入结果:{},返回结果:{},sourceSerialNum:{}",JSON.toJSONString(paymentApplyDtoResult),resultDTO,sourceSerialNum);
            } catch (Exception e) {
                logger.error("SDP付款申请接收接口处理-付款申请写入异常",e);
                resultDTO.setIsSuccess(Boolean.FALSE);
                resultDTO.setErrorMsg(e.getMessage());
                sdpLog.setStatus(SdpLogStatusEnum.RECEIVE_HANDLE_FAIL.getCode());
                sdpLog.setResponseMessage(e.getMessage());
            }finally {
                sdpLogService.save(sdpLog);
            }

            resultDTOS.add(resultDTO);
        }

        return resultDTOS;
    }

    private static SdpLog buildSdpLog(String sourceSerialNum, SdpReceivePaymentApplyInfo sdpReceivePaymentApplyInfo) {
        SdpLog sdpLog = new SdpLog();
        sdpLog.setSdpBusinessId(sourceSerialNum);
        sdpLog.setOperationType(SdpLogOperaTypeEnum.RECEIVE.getCode());
        sdpLog.setSdpBusinessType(TopicCodeEnum.PAYMENT_APPLY.getTopicCode());
        sdpLog.setOuId(sdpReceivePaymentApplyInfo.getOuId());
        sdpLog.setDataCount(1);
        sdpLog.setDeletedFlag(Boolean.FALSE);
        return sdpLog;
    }

    private void uploadFileHandle(SdpReceivePaymentApplyInfo sdpReceivePaymentApplyInfo, PaymentApplyDto paymentApplyDto, boolean isFirstCreate) {
        try {
            String attachment = sdpReceivePaymentApplyInfo.getAttachment();
            if (StringUtils.isEmpty(attachment) || !JSONUtil.isJson(attachment)) {
                logger.error("SDP付款申请接收接口处理-付款申请附件为空");
                throw new BizException(Code.ERROR,"没有上传附件，请上传后重新提交");
            }
            
            List<SdpReceivePaymentApplyInfo.attachmentInfo> attachmentInfos = JSON.parseArray(attachment, SdpReceivePaymentApplyInfo.attachmentInfo.class);
            //非首次创建，删除附件信息
            if (!isFirstCreate) {
                paymentApplyExternalAttachmentExtMapper.logicDeleteByPaymentApplyId(paymentApplyDto.getId());
            }
            if (ListUtils.isNotEmpty(attachmentInfos)) {
                for (SdpReceivePaymentApplyInfo.attachmentInfo attachmentInfo : attachmentInfos) {
                    PaymentApplyExternalAttachment paymentApplyExternalAttachment = new PaymentApplyExternalAttachment();
                    paymentApplyExternalAttachment.setFileName(attachmentInfo.getPdfDocName());
                    paymentApplyExternalAttachment.setFileId(attachmentInfo.getPdfDocId());
                    paymentApplyExternalAttachment.setPaymentApplyId(paymentApplyDto.getId());
                    paymentApplyExternalAttachment.setDeletedFlag(Boolean.FALSE);
                    paymentApplyExternalAttachmentMapper.insert(paymentApplyExternalAttachment);
                }
            } else {
                logger.error("SDP付款申请接收接口处理-付款申请附件列表为空");
                throw new RuntimeException("没有上传附件，请上传后重新提交");
            }
        } catch (Exception e) {
            logger.error("SDP付款申请接收接口处理-付款申请附件保存失败", e);
            throw new RuntimeException(e);
        }
    }

    private static void buildUserInfo(Long ouId) {
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setUsername("GSC");
        userInfoDto.setUnitId(CacheDataUtils.getTopUnitIdByOuId(ouId));
        SystemContext.set(userInfoDto);
    }

    private List<DictDto> getPaymentMethodList(){
        final Map<String, Object> param = new HashMap<>();
        param.put("types", "third_bill_payment_type,prepayment_type");
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/ltcDict/selectByTypeList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<DictDto>> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<DictDto>>>() {
        });
        List<DictDto> dictList = dataResponse.getData();
        List<String> fixedPaymentMethodName = Arrays.asList("美易单", "电子承兑汇票", "电子付款", "电汇", "支票", "纸制承兑汇票", "信用证", "第三方票据-迪链", "第三方票据-宝象","现汇","承兑");
        //移除不符合的付款方式
        dictList.removeIf(dictDto -> !fixedPaymentMethodName.contains(dictDto.getName()));
        return dictList;
    }

    private boolean checkTaxPayIncludedPrice(SdpReceivePaymentApplyInfo sdpReceivePaymentApplyInfo, PaymentPlan paymentPlan,BigDecimal refusePaymentApplyTaxAmount) {
        boolean exceedSurplusAmount = true;
        List<PaymentPlanDTO> paymentPlanDTOS = paymentPlanExtMapper.calculateSurplusAmount(paymentPlan.getId());
        if(ListUtils.isNotEmpty(paymentPlanDTOS)){
            PaymentPlanDTO surplusPaymentPlanInfo = paymentPlanDTOS.get(0);
            BigDecimal surplusAmount = Optional.ofNullable(surplusPaymentPlanInfo.getSurplusAmount()).orElse(BigDecimal.ZERO);
            //判断驳回的付款申请金额不为空，则将剩余金额加上驳回的付款申请金额
            BigDecimal finalSurplusAmount = surplusAmount;
            surplusAmount = Optional.ofNullable(refusePaymentApplyTaxAmount).map(finalSurplusAmount::add).orElse(surplusAmount);
            BigDecimal taxPayIncludedPrice = sdpReceivePaymentApplyInfo.getTaxPayIncludedPrice();
            exceedSurplusAmount = taxPayIncludedPrice.compareTo(surplusAmount) > 0;
        }
        return exceedSurplusAmount;
    }

    private boolean paymentApplyCheck(PaymentPlan paymentPlan) {
        PaymentApplyExample paymentApplyExample = new PaymentApplyExample();
        paymentApplyExample.createCriteria().andPaymentPlanIdEqualTo(paymentPlan.getId()).andDeletedFlagEqualTo(0);
        List<PaymentApply> paymentApplyList = paymentApplyMapper.selectByExample(paymentApplyExample);
        return paymentApplyList.stream().map(PaymentApply::getAuditStatus).anyMatch(s -> Objects.equals(s, AuditStatus.PENDING.getCode()));
    }

    private boolean isPaymentMethodMatch(SdpReceivePaymentApplyInfo sdpReceivePaymentApplyInfo, List<DictDto> fixedPaymentMethodList, VendorSiteBank vendorSiteBank) {
        // 使用 Objects.equals 避免空指针异常
        boolean paymentMethodMatch = fixedPaymentMethodList.stream().map(DictDto::getCode).anyMatch(s -> Objects.equals(s, sdpReceivePaymentApplyInfo.getPaymentMethod()));
        boolean vendorBankNumMatch = Objects.equals(sdpReceivePaymentApplyInfo.getVendorBankNum(), vendorSiteBank.getBankAccount());
        return paymentMethodMatch && vendorBankNumMatch;
    }

    private static boolean isVendorNotMatch(SdpReceivePaymentApplyInfo sdpReceivePaymentApplyInfo, VendorSiteBank vendorSiteBank) {
        boolean vendorNotMatch = true;
        //校验供应商信息
        if(Objects.nonNull(vendorSiteBank)){
            String requestVendorCode = sdpReceivePaymentApplyInfo.getVendorCode();
            String requestErpVendor = sdpReceivePaymentApplyInfo.getErpVendor();
            Long requestErpVendorId = sdpReceivePaymentApplyInfo.getErpVendorId();
            Long requestErpVendorSiteId = sdpReceivePaymentApplyInfo.getErpVendorSiteId();
            String bankPaymentMethod = sdpReceivePaymentApplyInfo.getBankPaymentMethod();
            vendorNotMatch =
                            !Objects.equals(requestVendorCode, vendorSiteBank.getVendorCode()) ||
                            !Objects.equals(requestErpVendor, vendorSiteBank.getVendorName()) ||
                            !Objects.equals(requestErpVendorId, vendorSiteBank.getErpVendorId()) ||
                            !Objects.equals(requestErpVendorSiteId, vendorSiteBank.getErpVendorSiteId()) ||
                            (!org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(bankPaymentMethod, vendorSiteBank.getPaymentMethodCode()));
        }
        return vendorNotMatch;
    }

    private VendorSiteBank getVendorSiteBank(Long ouId,String vendorCode,Long erpVendorSiteId) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("ouId", ouId);
        param.put("vendorCode", vendorCode);
        param.put("erpVendorSiteId", erpVendorSiteId);
        param.put("purchasingSiteFlag", "Y");
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "vendor/selectList", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<VendorSiteBank>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<VendorSiteBank>>>() {
        });
        if (response != null) {
            List<VendorSiteBank> vendorSiteBankList = response.getData();
            if(ListUtils.isNotEmpty(vendorSiteBankList)){
                return response.getData().get(0);
            }
        }
        return null;
    }

    private List<PurchaseContract> getPurchaseContractByCode(String purchaseCode, Long ouId) {
        PurchaseContractExample purchaseContractExample = new PurchaseContractExample();
        purchaseContractExample.createCriteria().andCodeEqualTo(purchaseCode)
                .andOuIdEqualTo(ouId).andDeletedFlagEqualTo(Boolean.FALSE);
        return purchaseContractService.selectByExample(purchaseContractExample);
    }

    /**
     * 验证GSC申请申请信息
     * 此方法会对所有必填字段进行校验，如果存在为空的必填字段，将抛出详细的验证异常
     *
     * @param sdpReceivePaymentApplyInfo GSC付款申请
     * @throws ValidationException 当存在必填字段为空时抛出此异常
     */
    private void validateContractInfo(SdpReceivePaymentApplyInfo sdpReceivePaymentApplyInfo) {
        // 创建验证器工厂和验证器实例
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();

        // 存储所有验证错误信息
        List<String> errorMessages = new ArrayList<>();

        // 验证主合同信息
        Set<ConstraintViolation<SdpReceivePaymentApplyInfo>> violations = validator.validate(sdpReceivePaymentApplyInfo);
        for (ConstraintViolation<SdpReceivePaymentApplyInfo> violation : violations) {
            errorMessages.add(violation.getMessage());
        }

        // 如果存在验证错误，抛出异常并包含所有错误信息
        if (!errorMessages.isEmpty()) {
            throw new ValidationException("付款申请信息验证失败：" + String.join(",", errorMessages));
        }
    }

    /**
     * 检查付款申请是否重复提交
     * @param gscPaymentApplyList
     * @return
     */
    private boolean checkPaymentApplyRepeatRequest(List<PaymentApply> gscPaymentApplyList){
        boolean isRepeatRequest = false;
        if(ListUtils.isNotEmpty(gscPaymentApplyList)){
            isRepeatRequest = gscPaymentApplyList.stream().map(PaymentApply::getAuditStatus).anyMatch(s->!Objects.equals(s, AuditStatus.REFUSE.getCode()));
         }

        return isRepeatRequest;
    }

    private List<PaymentApply> getGscPaymentApply(String paymentApplyCode) {
        PaymentApplyExample paymentApplyExample = new PaymentApplyExample();
        paymentApplyExample
                .createCriteria()
                .andDeletedFlagEqualTo(0)
                .andSourceSystemNameEqualTo(PaymentApplySourceNameEnum.GSC.getName())
                .andSourceSystemCodeEqualTo(paymentApplyCode);
        return paymentApplyMapper.selectByExample(paymentApplyExample);

    }

    /**
     * 校验付款申请合同原件控制
     * 根据组织参数"付款申请合同原件控制"判断是否需要校验采购合同原件附件
     *
     * @param purchaseContract 采购合同
     * @param sourceSerialNum 源系统流水号
     * @throws BizException 当开启校验且没有合同原件时抛出异常
     */
    private void validateContractOriginalAttachment(PurchaseContract purchaseContract, String sourceSerialNum) {
        try {
            // 获取顶级单位ID
            Long topUnitId = CacheDataUtils.getTopUnitIdByOuId(purchaseContract.getOuId());
            if (Objects.isNull(topUnitId)) {
                logger.warn("SDP付款申请接收接口处理-合同原件校验跳过，无法获取顶级单位ID，ouId:{}, sourceSerialNum:{}",
                        purchaseContract.getOuId(), sourceSerialNum);
                return;
            }

            // 查询组织参数：付款申请合同原件控制
            Set<String> paramValueSet = organizationCustomDictService.queryByName("付款申请合同原件控制", topUnitId, OrgCustomDictOrgFrom.COMPANY);

            logger.info("SDP付款申请接收接口处理-合同原件校验开始，合同ID:{}, 合同编号:{}, 组织参数值:{}, sourceSerialNum:{}",
                    purchaseContract.getId(), purchaseContract.getCode(), paramValueSet, sourceSerialNum);

            // 付款申请合同原件控制，参数值=1为开启
            if (paramValueSet.size() == 1 && Objects.equals(paramValueSet.iterator().next(), "1")) {
                // 校验采购合同合同原件是否有附件
                String originalContractAnnex = purchaseContract.getOriginalContractAnnex();
                boolean hasOriginalAttachment = Objects.nonNull(originalContractAnnex) && StringUtils.hasText(originalContractAnnex);

                if (!hasOriginalAttachment) {
                    String failedParam = "合同原件附件";
                    String requestParamValue = "需要合同原件附件";
                    String systemParamValue = "originalContractAnnex=" + originalContractAnnex;

                    logBizValidationError("SDP付款申请接收接口处理失败-没有上传合同双签原件无法发起付款申请",
                            sourceSerialNum, failedParam, requestParamValue, systemParamValue);

                    logger.error("SDP付款申请接收接口处理-合同原件校验失败，合同ID:{}, 合同编号:{}, 原件附件:{}, sourceSerialNum:{}",
                            purchaseContract.getId(), purchaseContract.getCode(), originalContractAnnex, sourceSerialNum);

                    throw new BizException(Code.ERROR, "没有上传合同双签原件无法发起付款申请");
                } else {
                    logger.info("SDP付款申请接收接口处理-合同原件校验通过，合同ID:{}, 合同编号:{}, 原件附件:{}, sourceSerialNum:{}",
                            purchaseContract.getId(), purchaseContract.getCode(), originalContractAnnex, sourceSerialNum);
                }
            } else {
                logger.info("SDP付款申请接收接口处理-合同原件校验跳过，未开启校验，合同ID:{}, 合同编号:{}, 组织参数值:{}, sourceSerialNum:{}",
                        purchaseContract.getId(), purchaseContract.getCode(), paramValueSet, sourceSerialNum);
            }
        } catch (BizException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            logger.error("SDP付款申请接收接口处理-合同原件校验异常，合同ID:{}, 合同编号:{}, sourceSerialNum:{}",
                    purchaseContract.getId(), purchaseContract.getCode(), sourceSerialNum, e);
            // 校验异常时不阻断流程，记录日志后继续
        }
    }
}