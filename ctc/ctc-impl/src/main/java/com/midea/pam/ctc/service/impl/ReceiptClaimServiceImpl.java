package com.midea.pam.ctc.service.impl;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.GlDailyRateDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.dto.ReceiptMethodDto;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDetailDto;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimDetailDTO;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.ctc.entity.AgencySynInfo;
import com.midea.pam.common.ctc.entity.AgencySynInfoExample;
import com.midea.pam.common.ctc.entity.BudgetItem;
import com.midea.pam.common.ctc.entity.BudgetItemExample;
import com.midea.pam.common.ctc.entity.CnapsInfo;
import com.midea.pam.common.ctc.entity.CnapsInfoExample;
import com.midea.pam.common.ctc.entity.CustomerTransfer;
import com.midea.pam.common.ctc.entity.CustomerTransferSeq;
import com.midea.pam.common.ctc.entity.CustomerTransferSeqExample;
import com.midea.pam.common.ctc.entity.InvoiceReceivable;
import com.midea.pam.common.ctc.entity.InvoiceReceivableExample;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentApplyExample;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.ReceiptClaim;
import com.midea.pam.common.ctc.entity.ReceiptClaimDetail;
import com.midea.pam.common.ctc.entity.ReceiptClaimDetailExample;
import com.midea.pam.common.ctc.entity.ReceiptClaimExample;
import com.midea.pam.common.ctc.entity.ReceiptInvoiceRelation;
import com.midea.pam.common.ctc.entity.ReceiptInvoiceRelationExample;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetail;
import com.midea.pam.common.ctc.entity.ReceiptPlanDetailExample;
import com.midea.pam.common.ctc.entity.ReceiptWorkOrder;
import com.midea.pam.common.ctc.entity.ReceiptWorkOrderAccounting;
import com.midea.pam.common.ctc.entity.ReceiptWorkOrderAccountingExample;
import com.midea.pam.common.ctc.entity.ReceiptWorkOrderExample;
import com.midea.pam.common.ctc.vo.ReceiptClaimContractRelVO;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CustomerTransferErpStatus;
import com.midea.pam.common.enums.CustomerTransferType;
import com.midea.pam.common.enums.GcebInterfaceSynStatus;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.PaymentApplyBizStatus;
import com.midea.pam.common.enums.PaymentInvoiceAccountEntryTypeEnum;
import com.midea.pam.common.enums.PaymentInvoiceErpStatusEnum;
import com.midea.pam.common.enums.PaymentInvoiceSourceEnum;
import com.midea.pam.common.enums.PaymentInvoiceStatusEnum;
import com.midea.pam.common.enums.ReceiptWorkOrderSyncStatus;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.api.server.PassGcebToPamService;
import com.midea.pam.ctc.common.enums.CommonErpStatus;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.DeletedFlagEnum;
import com.midea.pam.ctc.common.enums.InvoiceReceivableErpStatus;
import com.midea.pam.ctc.common.enums.InvoiceReceivableSource;
import com.midea.pam.ctc.common.enums.ReceiptClaimEnum;
import com.midea.pam.ctc.common.enums.WriteOffEnum;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.AgencySynInfoMapper;
import com.midea.pam.ctc.mapper.BudgetItemMapper;
import com.midea.pam.ctc.mapper.CnapsInfoMapper;
import com.midea.pam.ctc.mapper.CustomerTransferMapper;
import com.midea.pam.ctc.mapper.CustomerTransferSeqMapper;
import com.midea.pam.ctc.mapper.InvoiceReceivableMapper;
import com.midea.pam.ctc.mapper.PaymentApplyExtMapper;
import com.midea.pam.ctc.mapper.PaymentApplyMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceExtMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.PaymentPlanExtMapper;
import com.midea.pam.ctc.mapper.PaymentPlanMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimContractRelMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimDetailMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimExtMapper;
import com.midea.pam.ctc.mapper.ReceiptClaimMapper;
import com.midea.pam.ctc.mapper.ReceiptInvoiceRelationMapper;
import com.midea.pam.ctc.mapper.ReceiptPlanDetailMapper;
import com.midea.pam.ctc.mapper.ReceiptWorkOrderAccountingMapper;
import com.midea.pam.ctc.mapper.ReceiptWorkOrderMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.BusiSceneNonSaleService;
import com.midea.pam.ctc.service.CrmExtService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.PaymentInvoiceService;
import com.midea.pam.ctc.service.ReceiptClaimService;
import com.midea.pam.ctc.service.WriteOffService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import io.netty.util.internal.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: pam
 * @description: ReceiptClaimServiceImpl
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
public class ReceiptClaimServiceImpl implements ReceiptClaimService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    BusiSceneNonSaleService busiSceneNonSaleService;
    @Resource
    ReceiptClaimMapper receiptClaimMapper;
    @Resource
    ReceiptClaimExtMapper receiptClaimExtMapper;
    @Resource
    ReceiptClaimDetailMapper receiptClaimDetailMapper;
    @Resource
    private BudgetItemMapper budgetItemMapper;
    @Resource
    private WriteOffService writeOffService;
    @Resource
    ReceiptClaimContractRelMapper rcContractRelMapper;
    @Resource
    private ReceiptPlanDetailMapper receiptPlanDetailMapper;
    @Resource
    private ReceiptInvoiceRelationMapper receiptInvoiceRelationMapper;
    @Resource
    private AgencySynInfoMapper agencySynInfoMapper;
    @Resource
    private PaymentApplyMapper paymentApplyMapper;
    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;
    @Resource
    private PaymentApplyExtMapper paymentApplyExtMapper;
    @Resource
    private PaymentInvoiceExtMapper paymentInvoiceExtMapper;
    @Resource
    private CrmExtService crmExtService;
    @Resource
    private InvoiceReceivableMapper invoiceReceivableMapper;
    @Resource
    private CustomerTransferMapper customerTransferMapper;
    @Resource
    private CustomerTransferSeqMapper customerTransferSeqMapper;
    @Resource
    private PaymentPlanMapper paymentPlanMapper;
    @Resource
    private PaymentPlanExtMapper paymentPlanExtMapper;
    @Resource
    private CnapsInfoMapper cnapsInfoMapper;
    @Resource
    private PaymentApplyService paymentApplyService;
    @Resource
    private PaymentInvoiceService paymentInvoiceService;
    @Resource
    ReceiptWorkOrderMapper receiptWorkOrderMapper;
    @Resource
    ReceiptWorkOrderAccountingMapper receiptWorkOrderAccountingMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private PassGcebToPamService passGcebToPamService;

    private ReceiptClaimExample buildExample(ReceiptClaimDto dto) {
        ReceiptClaimExample example = new ReceiptClaimExample();
        ReceiptClaimExample.Criteria criteria = example.createCriteria();
        if (!ObjectUtils.isEmpty(dto.getCashReceiptCode())) {
            criteria.andCashReceiptCodeLike("%" + dto.getCashReceiptCode() + "%");
        }
        return example;
    }

    public List<ReceiptClaim> list(ReceiptClaimDto dto) {
        ReceiptClaimExample example = this.buildExample(dto);
        List<ReceiptClaim> receiptClaims = receiptClaimMapper.selectByExample(example);
        return receiptClaims;
    }

    /**
     * 认款详细保存
     *
     * @param dto
     * @return
     */
    @Override
    public ReceiptClaimDetail saveClaimDetail(ReceiptClaimDto dto) {
        if (!(BigDecimalUtils.isGreater(dto.getAmountReceived()
                , new BigDecimal("0")))) {
            throw new BizException(Code.ERROR, "到款金额不能为0或空!");
        } else if (Objects.isNull(dto.getClaimAmount()) || !(BigDecimalUtils.equals(dto.getClaimAmount(),
                dto.getAmountReceived().add(Objects.isNull(dto.getCommission()) ? new BigDecimal("0")
                        : dto.getCommission())))) {
            throw new BizException(Code.ERROR, "认款总额数值错误!");
        }
        ReceiptClaimDetail detail = new ReceiptClaimDetail();
        BeanUtils.copyProperties(dto, detail);
        if (ObjectUtils.isEmpty(detail.getId())) {
            detail.setWriteOffStatus(WriteOffEnum.NON_WRITE_OFF.getCode());//未核销
            detail.setDeletedFlag(0);
            receiptClaimDetailMapper.insert(detail);
        } else {
            receiptClaimDetailMapper.updateByPrimaryKeySelective(detail);
        }
        return detail;
    }

    /**
     * PAM-ERP-019 收款写入 回调
     *
     * @param id
     * @param isSuccess
     * @param msg
     * @param sourceId3
     * @return
     */
    @Transactional
    @Override
    public Boolean callBack(Long id, boolean isSuccess, String msg, String sourceId3) {
        ReceiptClaimDetail receiptDetail = receiptClaimDetailMapper.selectByPrimaryKey(id);
        if (Objects.isNull(receiptDetail)) return null;
        ReceiptClaim receipt = receiptClaimMapper.selectByPrimaryKey(receiptDetail.getReceiptClaimId());
        if (Objects.equals(2, receipt.getSource())) {//回款工单
            String cashReceiptCode = receipt.getCashReceiptCode();
            //查询日记帐
            ReceiptWorkOrderAccountingExample accountingExampleexample = new ReceiptWorkOrderAccountingExample();
            accountingExampleexample.createCriteria().andDailyBatchNumEqualTo(cashReceiptCode).andDeletedFlagEqualTo(Boolean.FALSE);
            List<ReceiptWorkOrderAccounting> accountings = receiptWorkOrderAccountingMapper.selectByExample(accountingExampleexample);
            if (CollectionUtils.isNotEmpty(accountings)) {
                ReceiptWorkOrderAccounting orderAccounting = accountings.get(0);
                ReceiptWorkOrderExample example = new ReceiptWorkOrderExample();
                example.createCriteria().andReceiptCodeEqualTo(cashReceiptCode).andDeletedFlagEqualTo(Boolean.FALSE);
                List<ReceiptWorkOrder> receiptWorkOrders = receiptWorkOrderMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(receiptWorkOrders)) {
                    ReceiptWorkOrder receiptWorkOrder = receiptWorkOrders.get(0);
                    if (isSuccess && Objects.equals(ReceiptWorkOrderSyncStatus.SYNC_SUCCESS.getCode(), orderAccounting.getErpStatus())) {
                        receiptWorkOrder.setSyncStatus(ReceiptWorkOrderSyncStatus.SYNC_SUCCESS.getCode());
                    } else if (!isSuccess) {
                        receiptWorkOrder.setSyncStatus(ReceiptWorkOrderSyncStatus.SYNC_FAIL.getCode());
                    }
                    receiptWorkOrderMapper.updateByPrimaryKeySelective(receiptWorkOrder);
                }
            }
        }

        if (isSuccess) {
            //更新回款明细ERP同步状态：已记账
            ReceiptClaimDetail updateEntity = new ReceiptClaimDetail();
            updateEntity.setId(id);
            updateEntity.setErpStatus(ReceiptClaimEnum.ERP_ACCOUNTED.getCode());//已记账
            updateEntity.setErpMessage("");
            updateEntity.setContractSyncStatus(CommonErpStatus.PUSHED.code());
            updateEntity.setContractSyncMessage("");
            receiptClaimExtMapper.updateClaimDetailErpStatus(updateEntity);

            // 客户间转款生成的收款，需要更新客户间转款单状态
            if (Objects.nonNull(receipt) && Objects.equals(receipt.getSource(), InvoiceReceivableSource.TRANSFER.getCode())) {
                //发票
                InvoiceReceivableExample invoiceExample = new InvoiceReceivableExample();
                invoiceExample.createCriteria().andCustomerTransferIdEqualTo(receipt.getCustomerTransferId()).andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
                List<InvoiceReceivable> invoiceReceivables = invoiceReceivableMapper.selectByExample(invoiceExample);

                if (ListUtil.isPresent(invoiceReceivables)) {
                    InvoiceReceivable invoice = invoiceReceivables.get(0);
                    CustomerTransfer transfer = customerTransferMapper.selectByPrimaryKey(receipt.getCustomerTransferId());

                    // 跨ou转款生成的收款，若发票/收款/关联交易均已同步到ERP，更新客户间转款单状态为：已同步
                    if (Objects.equals(transfer.getTransferType(), CustomerTransferType.CROSS_OU.getCode())) {
                        //关联交易
                        CustomerTransferSeqExample seqExample = new CustomerTransferSeqExample();
                        seqExample.createCriteria().andCustomerTransferIdEqualTo(receipt.getCustomerTransferId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
                        List<CustomerTransferSeq> customerTransferSeqs = customerTransferSeqMapper.selectByExample(seqExample);

                        if (ListUtil.isPresent(customerTransferSeqs)
                                && Objects.equals(customerTransferSeqs.get(0).getErpStatus(), CustomerTransferErpStatus.SUCCESS.getCode())
                                && Objects.equals(invoice.getErpStatus(), InvoiceReceivableErpStatus.SUCCESS.getCode())) {
                            //更新客户间转款单状态：已同步
                            transfer.setErpStatus(CustomerTransferErpStatus.SUCCESS.getCode());
                            customerTransferMapper.updateByPrimaryKeySelective(transfer);
                        }

                        // 第三方票据转款生成的收款，若发票/收款均已同步到ERP，更新客户间转款单状态为：已同步
                    } else if (Objects.equals(transfer.getTransferType(), CustomerTransferType.THIRD_NOTE.getCode())) {
                        if (Objects.equals(invoice.getErpStatus(), InvoiceReceivableErpStatus.SUCCESS.getCode())) {
                            //更新客户间转款单状态：已同步
                            transfer.setErpStatus(CustomerTransferErpStatus.SUCCESS.getCode());
                            customerTransferMapper.updateByPrimaryKeySelective(transfer);
                        }
                    }

                }
            }

        } else {
            //更新回款明细ERP同步状态：记账失败
            ReceiptClaimDetail updateEntity = new ReceiptClaimDetail();
            updateEntity.setId(id);
            updateEntity.setErpStatus(ReceiptClaimEnum.ERP_ACCOUNT_FAILED.getCode());//记账失败
            if (!StringUtils.isBlank(msg) && msg.length() > 200) {
                msg = msg.substring(0, 199);
            }
            updateEntity.setErpMessage(msg);
            updateEntity.setContractSyncStatus(CommonErpStatus.PUSH_FAILED.code());
            updateEntity.setContractSyncMessage("");
            receiptClaimExtMapper.updateClaimDetailErpStatus(updateEntity);

            // 客户间转款生成的收款，更新客户间转款单状态：同步失败
            if (Objects.nonNull(receipt) && Objects.equals(receipt.getSource(), InvoiceReceivableSource.TRANSFER.getCode())) {
                CustomerTransfer transfer = new CustomerTransfer();
                transfer.setId(receipt.getCustomerTransferId());
                transfer.setErpStatus(CustomerTransferErpStatus.FAILURE.getCode());
                transfer.setErpMessage(msg);
                customerTransferMapper.updateByPrimaryKeySelective(transfer);
            }
        }
        return true;
    }

    @Override
    public void reverseFromERPById(Long id, String reason, String erpReversalDate) {
        if (checkReceiptClaimDetailErpStatusById(id)) {
            throw new BizException(Code.ERROR, "认款状态为【已认款或资金撤回失败】时,才能进行操作!");
        }
        ReceiptClaimDto param = new ReceiptClaimDto();
        param.setId(id);
        List<ReceiptClaimDto> dtoList = this.queryReceiptClaimDetailList(param);
        List<ReceiptClaimDto> reversalList = new ArrayList<>();
        for (ReceiptClaimDto dto : dtoList) {
            int num = this.findEffectiveContractRel(dto.getId());
            if (num > 0) {
                throw new BizException(Code.ERROR, "请先删除回款分配合同，然后再撤至认款或资金!");
            }
            if (!dto.getErpStatus().equals(ReceiptClaimEnum.ERP_ACCOUNTED.getCode()) &&
                    !dto.getErpStatus().equals(ReceiptClaimEnum.ERP_REVERSE_FAILED.getCode())) {
                throw new BizException(Code.ERROR, "只有已记账或冲销失败的认款才能进行冲销!");
            } else if (!WriteOffEnum.NON_WRITE_OFF.getCode().equals(dto.getWriteOffStatus())) {
                throw new BizException(Code.ERROR, "只有未核销的认款才能进行冲销!");
            }
            reversalList.add(dto);
        }
        this.reverseFromERP(reversalList, reason, erpReversalDate);
    }

    /**
     * 如果为非PAM上线单位：只校验必填；
     * <p>
     * 以下情况均属于非PAM上线单位：A、在PAM的组织清单中未沟通“启用”的业务实体
     * B、PAM的增加组织参数 “PAM中收款例外OU” 中的OU ID
     *
     * @param receiptCode
     * @param ouId
     * @param customerId
     * @return
     */
    @Override
    public ReceiptClaimDetailDTO checkExist(String receiptCode, Long ouId, Long customerId) {
        ReceiptClaimDetailDTO receiptClaimDetail = receiptClaimExtMapper.getByReceiptCodeAndCustomerId(receiptCode, ouId, customerId);
        // 查询组织参数：PAM中收款例外OU
        Set<String> set = organizationCustomDictService.queryByName("PAM中收款例外OU", SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
        //如果转出方业务实体为PAM上线单位：需要做收款单号是否存在PAM校验；
        List<OrganizationRel> organizationRelList = basedataExtService.queryByOuId(ouId);
        if (!set.contains(String.valueOf(ouId)) && CollectionUtils.isNotEmpty(organizationRelList) && receiptClaimDetail == null) {
            throw new BizException(Code.ERROR, "收款编号不存在，请确认后重新填写");
        }
        return receiptClaimDetail;
    }

    @Override
    public void checkPushToEepStatus(ReceiptClaimDto dto) {
        if (ListUtils.isEmpty(dto.getIdList())) {
            throw new BizException(Code.ERROR, "未选择资金单据”");
        }
        ReceiptClaimDetailExample receiptDetailExample = new ReceiptClaimDetailExample();
        receiptDetailExample.createCriteria().andIdIn(dto.getIdList());
        List<ReceiptClaimDetail> receiptClaimDetails = receiptClaimDetailMapper.selectByExample(receiptDetailExample);
        //同步状态为“同步失败”时才可以使用“ERP同步记账”- ps: dealWithReceiptClaimAsyc 会过滤不符合的数据，按产品需求添加控制
        List<ReceiptClaimDetail> receiptClaimDetailList = receiptClaimDetails.stream().filter(e -> !Objects.equals(ReceiptClaimEnum.ERP_ACCOUNT_FAILED.getCode(), e.getErpStatus())).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(receiptClaimDetailList)) {
            throw new BizException(Code.ERROR, "同步失败的单据才可以重新“ERP同步记账”");
        }
    }

    @Override
    public ReceiptClaimDetail saveClaimDetailErpStatus(ReceiptClaimDto dto) {
        ReceiptClaimDetail detail = new ReceiptClaimDetail();
        BeanUtils.copyProperties(dto, detail);
        receiptClaimExtMapper.updateClaimDetailErpStatus(detail);
        return detail;
    }

    @Override
    public ReceiptClaimDetail saveClaimDetailContractSyncStatus(ReceiptClaimDto dto) {
        ReceiptClaimDetail detail = new ReceiptClaimDetail();
        BeanUtils.copyProperties(dto, detail);
        receiptClaimExtMapper.updateClaimDetailContractSyncStatus(detail);
        return detail;
    }

    @Override
    public void saveReceipt(ReceiptClaim receiptClaim) {
        if (ObjectUtils.isEmpty(receiptClaim.getId())) {
            receiptClaim.setDeletedFlag(0);
            receiptClaimMapper.insert(receiptClaim);
        } else {
            receiptClaimMapper.updateByPrimaryKeySelective(receiptClaim);
        }
    }


    @Override
    public PageInfo<ReceiptClaimDto> queryReceiptClaimDetailPage(ReceiptClaimDto dtoParam) {
        PageHelper.startPage(dtoParam.getPageNum(), dtoParam.getPageSize());
        List<ReceiptClaimDto> receiptClaimDtoList = this.queryReceiptClaimDetailList(dtoParam);
        return BeanConverter.convertPage(receiptClaimDtoList, ReceiptClaimDto.class);
    }

    @Override
    public List<ReceiptClaimDto> queryReceiptClaimDetailList(ReceiptClaimDto dtoParam) {
        List<ReceiptClaimDto> receiptClaimDtoList = receiptClaimExtMapper.queryDetailList(dtoParam);
        for (ReceiptClaimDto dto : receiptClaimDtoList) {
            this.setRecMethodName(dto);
            this.setOuName(dto);
            this.setUserName(dto);
        }
        return receiptClaimDtoList;
    }


    @Override
    public ReceiptClaimDto getDetailByReceiptClaimId(Long receiptClaimId) {
        //param
        ReceiptClaimDto receiptClaimDto = new ReceiptClaimDto();
        ReceiptClaimDto paramDto = new ReceiptClaimDto();
        paramDto.setReceiptClaimId(receiptClaimId);
        //头信息
        ReceiptClaim receiptClaim = receiptClaimMapper.selectByPrimaryKey(receiptClaimId);
        Guard.notNull(receiptClaim, String.format("收款认领id:%s对应的数据不存在", receiptClaimId));
        BeanUtils.copyProperties(receiptClaim, receiptClaimDto);
        this.setOuName(receiptClaimDto);
        OrganizationRelQuery organizationRelQuery = new OrganizationRelQuery();
        organizationRelQuery.setOperatingUnitId(receiptClaim.getOuId());
        final List<OrganizationRelDto> organizationRels = basedataExtService.getOrganizationRel(organizationRelQuery);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(organizationRels)) {
            final OrganizationRelDto organizationRelDto = organizationRels.get(0);
            String currency = organizationRelDto.getCurrency();
            String currencyCode = receiptClaimDto.getCurrencyCode();
            Date payDate = receiptClaim.getPayDate();
            PageInfo<GlDailyRateDto> pageInfo = basedataExtService.queryGlDailyRate(currencyCode, currency, payDate);
            if (currencyCode.equals(currency)) {
                receiptClaimDto.setConversionDate(null);
                receiptClaimDto.setConversionRate(null);
                receiptClaimDto.setConversionType(null);
                receiptClaimDto.setEditEnable(false);
            } else {
                receiptClaimDto.setEditEnable(true);
            }
            if (ListUtils.isNotEmpty(pageInfo.getList())) {
                receiptClaimDto.setConversionDate(pageInfo.getList().get(0).getExchangeDate());
                receiptClaimDto.setConversionRate(pageInfo.getList().get(0).getExchangeRate());
                receiptClaimDto.setConversionType(pageInfo.getList().get(0).getExchangeType());
            }
        }

        //拆款行信息
        receiptClaimDto.setDivideList(receiptClaimExtMapper.queryDetailList(paramDto));

        return receiptClaimDto;
    }

    private boolean checkReceiptClaimDetailErpStatusById(Long id) {
        //查询同步状态
        ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(id);
        if (Objects.isNull(receiptClaimDetail)) {
            return true;
        }
        //认款状态claimStatus = 2已认款 、4资金撤回失败  才允许操作
        if (!Arrays.asList(ReceiptClaimEnum.CLAIMED.getCode(), ReceiptClaimEnum.GCEB_REVERSE_FAILED.getCode()).
                contains(receiptClaimDetail.getClaimStatus())) {
            return true;
        }
        return false;
    }


    @Override
    public ReceiptClaimDto getDetailByReceiptClaimDetailId(Long receiptClaimDetailId) {

        ReceiptClaimDto receiptClaimDto = new ReceiptClaimDto();

        ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(receiptClaimDetailId);

        ReceiptClaim receiptClaim = receiptClaimMapper.selectByPrimaryKey(receiptClaimDetail.getReceiptClaimId());
        BeanUtils.copyProperties(receiptClaim, receiptClaimDto);
        this.setOuName(receiptClaimDto);
        OrganizationRelQuery organizationRelQuery = new OrganizationRelQuery();
        organizationRelQuery.setOperatingUnitId(receiptClaim.getOuId());
        final List<OrganizationRelDto> organizationRels = basedataExtService.getOrganizationRel(organizationRelQuery);
        if (ListUtils.isNotEmpty(organizationRels)) {
            final OrganizationRelDto organizationRelDto = organizationRels.get(0);
            String currency = organizationRelDto.getCurrency();
            String currencyCode = receiptClaimDto.getCurrencyCode();
            Date payDate = receiptClaim.getPayDate();
            PageInfo<GlDailyRateDto> pageInfo = basedataExtService.queryGlDailyRate(currencyCode, currency, payDate);
            if (currencyCode.equals(currency)) {
                receiptClaimDto.setConversionDate(null);
                receiptClaimDto.setConversionRate(null);
                receiptClaimDto.setConversionType(null);
            }
            if (ListUtils.isNotEmpty(pageInfo.getList())) {
                receiptClaimDto.setConversionDate(pageInfo.getList().get(0).getExchangeDate());
                receiptClaimDto.setConversionRate(pageInfo.getList().get(0).getExchangeRate());
                receiptClaimDto.setConversionType(pageInfo.getList().get(0).getExchangeType());
            }
        }
        if (receiptClaimDetail.getPaymentApplyId() != null) {
            PaymentApply paymentApply = paymentApplyMapper.selectByPrimaryKey(receiptClaimDetail.getPaymentApplyId());
            receiptClaimDto.setPaymentApply(paymentApply);
        }
        if (receiptClaimDetail.getPaymentInvoiceId() != null) {
            PaymentInvoice paymentInvoice =
                    paymentInvoiceMapper.selectByPrimaryKey(receiptClaimDetail.getPaymentInvoiceId());
            receiptClaimDto.setPaymentInvoice(paymentInvoice);
        }
        if (receiptClaimDetail.getVendorCode() != null) {
            receiptClaimDto.setVendorCode(receiptClaimDetail.getVendorCode());
        }
        if (receiptClaimDetail.getVendorName() != null) {
            receiptClaimDto.setVendorName(receiptClaimDetail.getVendorName());
        }
        if (receiptClaimDetail.getVendorSiteCode() != null) {
            receiptClaimDto.setVendorSiteCode(receiptClaimDetail.getVendorSiteCode());
        }

        if (receiptClaimDetail.getOriginalPaymentCode() != null) {
            PaymentApplyExample example = new PaymentApplyExample();
            example.createCriteria().andPaymentApplyCodeEqualTo(receiptClaimDetail.getOriginalPaymentCode());
            PaymentApply oldPaymentApply = paymentApplyMapper.selectByExample(example).get(0);
            receiptClaimDto.setOriginalPaymentId(oldPaymentApply.getId());
            receiptClaimDto.setOriginalPaymentCode(receiptClaimDetail.getOriginalPaymentCode());
        }
        return receiptClaimDto;
    }


    /**
     * 拆款保存
     *
     * @param dto
     */
    @Override
    @Transactional
    public void saveDivide(ReceiptClaimDto dto) {
        ReceiptClaim receiptClaim = new ReceiptClaim();
        BeanUtils.copyProperties(dto, receiptClaim);
        ReceiptClaim existEntity = receiptClaimMapper.selectByPrimaryKey(dto.getId());

        if (com.midea.pam.common.util.StringUtils.isLengthExceedsMax(dto.getRemark(), 240)) {
            throw new BizException(Code.ERROR, "备注最多240个字符,中文算三个");
        }

        //拆款行信息保存
        if (CollectionUtils.isNotEmpty(dto.getDivideList())) {
            receiptClaimCheck(dto.getDivideList());
            if (Boolean.TRUE.equals(dto.getIsClaim())) {
                receiptClaim.setDivideStatus(ReceiptClaimEnum.CLAIMED.getCode());//已拆
            }
            this.saveDivideDetail(dto);
        }
        if (Boolean.TRUE.equals(dto.getIsClaim())) {
            //头信息
            if (ObjectUtils.isEmpty(existEntity)) {
                receiptClaimMapper.insert(receiptClaim);
            } else {
                receiptClaimMapper.updateByPrimaryKeySelective(receiptClaim);
            }
        }
    }

    private void receiptClaimCheck(List<ReceiptClaimDto> divideList) {
        List<String> errorCodes = new ArrayList<>();
        divideList.stream()
                .filter(e -> Objects.nonNull(e.getId()))
                .forEach(obj -> {
                    ReceiptClaimDetail receiptDetail = receiptClaimDetailMapper.selectByPrimaryKey(obj.getId());
                    if (!Objects.equals(obj.getClaimStatus(), receiptDetail.getClaimStatus())) {
                        errorCodes.add(obj.getReceiptCode());
                    }
                });
        if (!errorCodes.isEmpty()) {
            throw new BizException(Code.ERROR, "收款编号: " + String.join(", ", errorCodes) + " 的认款状态已更新，请刷新页面后重试");
        }
    }

    /**
     * 拆款行信息保存
     *
     * @param dto
     */
    private void saveDivideDetail(ReceiptClaimDto dto) {
        List<ReceiptClaimDto> divideDtoList = dto.getDivideList();
        BigDecimal totalpayAmount = BigDecimal.ZERO;

        for (ReceiptClaimDto divideDto : divideDtoList) {
            if (BigDecimalUtils.equals(divideDto.getAmountReceived(), new BigDecimal("0"))) {
                throw new BizException(Code.ERROR, "到款金额不能为0或空!");
            } else if (Objects.isNull(divideDto.getClaimAmount()) || !(BigDecimalUtils.equals(divideDto.getClaimAmount(),
                    divideDto.getAmountReceived().add(Objects.isNull(divideDto.getCommission()) ? new BigDecimal("0")
                            : divideDto.getCommission())))) {
                throw new BizException(Code.ERROR, "认款总额数值错误!");
            }
            ReceiptClaimDetail receiptClaimDetail = new ReceiptClaimDetail();
            BeanUtils.copyProperties(divideDto, receiptClaimDetail);
            receiptClaimDetail.setRemark(dto.getRemark());//备注 查询返回取头表的备注。长度验证也是取头表的备注
            totalpayAmount = totalpayAmount.add(divideDto.getClaimAmount());
            if (ObjectUtils.isEmpty(divideDto.getId())) {
                // 重复提交校验
                if (org.apache.commons.lang3.StringUtils.isNotBlank(receiptClaimDetail.getReceiptCode())) {
                    ReceiptClaimDetailExample receiptClaimDetailExample = new ReceiptClaimDetailExample();
                    receiptClaimDetailExample.createCriteria().andDeletedFlagEqualTo(0)
                            .andReceiptClaimIdEqualTo(dto.getId()).andReceiptCodeEqualTo(receiptClaimDetail.getReceiptCode());
                    if (receiptClaimDetailMapper.countByExample(receiptClaimDetailExample) > 0) {
                        throw new ApplicationBizException("收款编号" + receiptClaimDetail.getReceiptCode() + "已存在");
                    }
                }
                receiptClaimDetail.setId(null);
                receiptClaimDetail.setReceiptClaimId(dto.getId());
                receiptClaimDetail.setAccountingDate(dto.getAccountingDate());
                receiptClaimDetail.setErpStatus(ReceiptClaimEnum.ERP_NOT_PUSH.getCode());
                receiptClaimDetail.setContractSyncStatus(CommonErpStatus.NOT_PUSH.code());
                receiptClaimDetail.setContractStatus(ReceiptClaimEnum.CONTRACT_UNALLOCATED.getCode());
                receiptClaimDetail.setWriteOffStatus(WriteOffEnum.NON_WRITE_OFF.getCode());
                receiptClaimDetail.setDeletedFlag(0);
                receiptClaimDetailMapper.insert(receiptClaimDetail);
            } else {
                receiptClaimDetailMapper.updateByPrimaryKeySelective(receiptClaimDetail);
            }
        }
        if (Boolean.TRUE.equals(dto.getIsClaim())) {
            if (!(dto.getPayAmount().compareTo(totalpayAmount) == 0)) {
                throw new BizException(Code.ERROR, "金额总量和到账金额不一致!");
            }
        }
    }

    /**
     * 认款批量保存
     *
     * @param dtoList
     * @return
     */
    @Override
    @Transactional
    public void saveClaim(List<ReceiptClaimDto> dtoList) {
        for (ReceiptClaimDto dto : dtoList) {
            //查询同步状态
            ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(dto.getId());
            if (Objects.isNull(receiptClaimDetail)) {
                continue;
            }
            //同步状态erpStatus：1-未同步、7撤回失败 才允许操作
            if (!Arrays.asList(ReceiptClaimEnum.ERP_NOT_PUSH.getCode(), ReceiptClaimEnum.ERP_REVERSE_FAILED.getCode()).
                    contains(receiptClaimDetail.getErpStatus())) {
                continue;
            }
            //认款状态claimStatus：1未认款 、4资金撤回失败  才允许；
            if (!Arrays.asList(ReceiptClaimEnum.UNCLAIM.getCode(), ReceiptClaimEnum.GCEB_REVERSE_FAILED.getCode()).
                    contains(receiptClaimDetail.getClaimStatus())) {
                continue;
            }
            if (com.midea.pam.common.util.StringUtils.isLengthExceedsMax(dto.getRemark(), 240)) {
                throw new BizException(Code.ERROR, "备注内容过长，请打开单据调整至240长度内！");
            }

            dto.setClaimStatus(ReceiptClaimEnum.CLAIMED.getCode());//已认款
            dto.setClaimBy(SystemContext.getUserId());
            dto.setClaimDate(DateUtils.getCurrentDate());

            dto.setWriteOffStatus(WriteOffEnum.NON_WRITE_OFF.getCode());//未核销
            dto.setErpStatus(ReceiptClaimEnum.ERP_ACCOUNTING.getCode());//ERP记账中
            this.saveClaimDetail(dto);
            //插入erp推送队列
            HandleDispatcher.route(BusinessTypeEnums.RECEIPT_CLAIM.getCode(), String.valueOf(dto.getId()), null, null, true);

            //客户间转款生成的销售回款认领，只要有一个（应收发票、销售回款认领、关联交易台账）单据同步中，转款单据显示同步中
            if (Objects.equals(dto.getBusinessType(), ReceiptClaimEnum.SALE.getCode())) {
                ReceiptClaim receiptClaim = receiptClaimMapper.selectByPrimaryKey(dto.getReceiptClaimId());
                if (Objects.nonNull(receiptClaim)
                        && Objects.equals(receiptClaim.getSource(), ReceiptClaimEnum.SOURCE_TRANSFER.getCode())
                        && receiptClaim.getCustomerTransferId() != null) {
                    CustomerTransfer updateEntity = new CustomerTransfer();
                    updateEntity.setId(receiptClaim.getCustomerTransferId());
                    updateEntity.setErpStatus(CustomerTransferErpStatus.DOING.getCode());
                    customerTransferMapper.updateByPrimaryKeySelective(updateEntity);
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRefundClaimBatch(List<ReceiptClaimDto> dtoList) {
        for (ReceiptClaimDto dto : dtoList) {
            PaymentApplyDto paymentApplyDto = paymentApplyExtMapper.findByPaymentCode(dto.getOriginalPaymentCode());
            paymentInvoiceService.checkGlDate(paymentApplyDto.getOuId(), dto.getAccountingDate());
            dto.setClaimStatus(ReceiptClaimEnum.CLAIMED.getCode());//已认款
            dto.setClaimBy(SystemContext.getUserId());
            dto.setClaimDate(DateUtils.getCurrentDate());
            ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(dto.getId());
            paymentApplyDto.setClaimAmount(receiptClaimDetail.getClaimAmount());
            paymentApplyDto.setAccountingDate(dto.getAccountingDate());
            paymentApplyDto.setGlDate(dto.getAccountingDate());

            if (paymentApplyDto.getReallyPayIncludedPrice().compareTo(paymentApplyDto.getRefundAmount()) <= 0) {
                throw new BizException(Code.ERROR, dto.getOriginalPaymentCode() + "该付款单没有可退款金额！");
            }

            if (paymentApplyDto.getClaimAmount().compareTo
                    (paymentApplyDto.getReallyPayIncludedPrice().subtract(paymentApplyDto.getRefundAmount())) > 0) {
                throw new BizException(Code.ERROR, paymentApplyDto.getPaymentApplyCode() + "该付款单可退款金额少于认款金额！");
            }

            PaymentApply newPaymentApply = generatePaymentApply(paymentApplyDto);//生成负数付款申请
            PaymentInvoice newPaymentInvoice = generatePaymentInvoice(paymentApplyDto, newPaymentApply.getId());//生成AP发票
            dto.setPaymentInvoiceId(newPaymentInvoice.getId());
            dto.setPaymentApplyId(newPaymentApply.getId());
            this.saveClaimDetail(dto);

            //更新收款的预算项目号
            ReceiptClaim receiptClaim = receiptClaimMapper.selectByPrimaryKey(dto.getReceiptClaimId());
            receiptClaim.setBudgetItemCode(dto.getBudgetItemCode());
            receiptClaimMapper.updateByPrimaryKeySelective(receiptClaim);

            //更新付款计划支付状态和实际付款金额
            if (paymentApplyDto.getPaymentPlanId() != null) {
                paymentApplyService.updatePaymentPlanStatusAndActualAmount(paymentApplyDto.getPaymentPlanId());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public PaymentInvoice generatePaymentInvoice(PaymentApplyDto paymentApplyDto, Long paymentApplyId) {

        PaymentInvoice newPaymentInvoice = new PaymentInvoice();
        newPaymentInvoice.setPaymentApplyId(paymentApplyId);//付款申请单id
        newPaymentInvoice.setInvoiceDate(paymentApplyDto.getAccountingDate());//发票日期
        newPaymentInvoice.setGlDate(paymentApplyDto.getGlDate());//总帐日期
        newPaymentInvoice.setPaymentPlanId(paymentApplyDto.getPaymentPlanId());//付款计划id
        newPaymentInvoice.setPaymentPlanCode(paymentApplyDto.getPaymentPlanCode());//付款计划编号
        newPaymentInvoice.setPurchaseContractId(paymentApplyDto.getPurchaseContractId());//采购合同id
        newPaymentInvoice.setPurchaseContractCode(paymentApplyDto.getPurchaseContractCode());//采购合同编号
        newPaymentInvoice.setOuId(paymentApplyDto.getOuId());//业务实体


        int num = paymentInvoiceExtMapper.statisticalNum(paymentApplyDto.getPaymentApplyCode());
        newPaymentInvoice.setPaymentApplyCode(paymentApplyDto.getPaymentApplyCode() + "-" + num);
        newPaymentInvoice.setApInvoiceCode("PAM-" + paymentApplyDto.getPaymentApplyCode() + "-" + num);//ap发票号
        newPaymentInvoice.setTotalInvoiceIncludedPrice(paymentApplyDto.getClaimAmount());//发票金额
        newPaymentInvoice.setSurplusAmount(paymentApplyDto.getClaimAmount());//剩余可用金额
        newPaymentInvoice.setTotalPayIncludedPrice(BigDecimal.ZERO);//erp实际已付款额
        newPaymentInvoice.setTaxAmount(BigDecimal.ZERO);//税额
        //newPaymentInvoice.setTaxCode(paymentInvoiceList.get(0).getTaxCode());//税码
        newPaymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.WAITING.code());
        newPaymentInvoice.setAvailable(1);//状态为0-已占用时,做预付款核销时,无法提交
        newPaymentInvoice.setCreateAt(new Date());
        newPaymentInvoice.setCreateBy(SystemContext.getUserId());
        newPaymentInvoice.setDeletedFlag(false);
        newPaymentInvoice.setSource(PaymentInvoiceSourceEnum.REFUND.getCode());//来源退款
        newPaymentInvoice.setStatus(PaymentInvoiceStatusEnum.PASS.getCode());
        newPaymentInvoice.setAuditDate(new Date());//审批通过日期
        newPaymentInvoice.setDueDate(new Date());//发票到期日
        newPaymentInvoice.setAccountEntryType(PaymentInvoiceAccountEntryTypeEnum.REGULAR_INVOICE.getCode());
        //查询项目预算号
        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(newPaymentInvoice.getPurchaseContractId());
        Pair<String, String> budgetProjectNumber = basedataExtService.getBudgetProjectNumberByVendorId(newPaymentInvoice.getPurchaseContractId(), purchaseContract.getVendorId(), purchaseContract.getOuId());
        if (Objects.nonNull(budgetProjectNumber)) {
            newPaymentInvoice.setItemNumber(budgetProjectNumber.getValue());
        }
        paymentInvoiceMapper.insertSelective(newPaymentInvoice);

        HandleDispatcher.route(BusinessTypeEnums.REFUND_PAYMENT_INVOICE.getCode(),
                String.valueOf(newPaymentInvoice.getId()), null, null, true);

        return newPaymentInvoice;
    }

    @Transactional(rollbackFor = Exception.class)
    public PaymentApply generatePaymentApply(PaymentApplyDto paymentApplyDto) {

        Long oldId = paymentApplyDto.getId();
        PaymentApply paymentApply = new PaymentApply();
        BeanConverter.copy(paymentApplyDto, paymentApply);

        paymentApply.setId(null);
        int num = paymentApplyExtMapper.statisticalNum(paymentApplyDto.getPaymentApplyCode());
        paymentApply.setPaymentApplyCode(paymentApplyDto.getPaymentApplyCode() + "-" + num);

        paymentApply.setTaxPlanIncludedPrice(paymentApplyDto.getClaimAmount().negate());//计划付款金额
        paymentApply.setTaxPayIncludedPrice(paymentApplyDto.getClaimAmount().negate());//本次付款金额
        paymentApply.setReallyPayIncludedPrice(paymentApplyDto.getClaimAmount().negate());//实际付款金额

        paymentApply.setCreateAt(new Date());
        paymentApply.setCreateBy(SystemContext.getUserId());
        paymentApply.setUpdateAt(null);
        paymentApply.setUpdateBy(null);
        paymentApply.setRemark(null);
        paymentApply.setAuditDate(new Date());
        paymentApply.setSubmitBy(SystemContext.getUserId());
        paymentApply.setSubmitDate(new Date());
        if (paymentApplyDto.getSubmitBy() != null) {
            UserInfo userInfo = CacheDataUtils.findUserById(paymentApplyDto.getSubmitBy());
            if (userInfo != null) {
                paymentApply.setSubmitName(userInfo.getName());
            }
        }
        paymentApply.setErpMsg(null);
        paymentApply.setGcebMsg(null);
        //paymentApply.setErpStatus(PaymentApplyGcebErpStatus.SUCCESS.getCode());
        paymentApply.setEsbStatus(GcebInterfaceSynStatus.CANNOT.getCode());
        paymentApply.setAuditStatus(Integer.valueOf(PaymentApplyBizStatus.EFFECTIVE.getCode()));
        paymentApply.setOriginalPaymentId(oldId);

        //查询预算号
        Pair<String, String> budgetProjectNumber = basedataExtService.getBudgetProjectNumberByVendorId(paymentApplyDto.getPurchaseContractId(), paymentApplyDto.getVendorId(), paymentApplyDto.getOuId());
        if (Objects.nonNull(budgetProjectNumber)) {
            paymentApply.setItemNumber(budgetProjectNumber.getValue());
            paymentApply.setItemNumberName(budgetProjectNumber.getKey());
        }

        paymentApplyMapper.insertSelective(paymentApply);

        return paymentApply;
    }

    @Override
    public ReceiptClaim findByUniqueKey(ReceiptClaimDto dto) {
        ReceiptClaimExample example = new ReceiptClaimExample();
        ReceiptClaimExample.Criteria criteria = example.createCriteria();
        criteria.andCashReceiptCodeEqualTo(dto.getCashReceiptCode()).andDeletedFlagEqualTo(0);
        List<ReceiptClaim> entityList = receiptClaimMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(entityList)) {
            return entityList.get(0);
        }
        return null;
    }

    /**
     * saveFromGceb
     *
     * @param receiptClaimDtoList
     */
    @Override
    public void saveFromGceb(List<ReceiptClaimDto> receiptClaimDtoList) {
        for (ReceiptClaimDto dto : receiptClaimDtoList) {
            //根据预算项目号配置默认回款类型(销售/非销售)
            Integer businessType = null;
            BudgetItemExample example = new BudgetItemExample();
            BudgetItemExample.Criteria criteria = example.createCriteria();
            if (!ObjectUtils.isEmpty(dto.getBudgetItemCode())) {
                criteria.andBudgetItemCodeEqualTo(dto.getBudgetItemCode());
                if (!ObjectUtils.isEmpty(dto.getOuId())) {
                    criteria.andOuIdEqualTo(dto.getOuId());
                }
                List<BudgetItem> budgetItems = budgetItemMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(budgetItems)) {
                    businessType = budgetItems.get(0).getBusinessType();
                }
            }
            //默认销售
            if (ObjectUtils.isEmpty(businessType)) {
                businessType = ReceiptClaimEnum.SALE.getCode();
            }
            //头表
            dto.setDivideStatus(ReceiptClaimEnum.UNDIVIDE.getCode());//未经人工拆分
            dto.setDeletedFlag(0);
            dto.setSource(ReceiptClaimEnum.SOURCE_INVOICE_APPLY.getCode()); //来源：资金
            ReceiptClaim receiptClaim = new ReceiptClaim();
            BeanUtils.copyProperties(dto, receiptClaim);
            receiptClaimMapper.insert(receiptClaim);
            //明细表
            ReceiptClaimDetail receiptClaimDetail = new ReceiptClaimDetail();
            receiptClaimDetail.setId(null);
            receiptClaimDetail.setAccountingDate(receiptClaim.getPayDate());
            receiptClaimDetail.setBusinessType(businessType);//业务类型
            receiptClaimDetail.setClaimAmount(receiptClaim.getPayAmount());//认款总额
            receiptClaimDetail.setAmountReceived(receiptClaim.getPayAmount()); //到款金额  手续费默认为0 所以认款总额与到款金额一致
            receiptClaimDetail.setCommission(new BigDecimal("0")); //GCEB推送销售回款单时手续费默认为0
            receiptClaimDetail.setReceiptCode(receiptClaim.getCashReceiptCode() + "-1");
            receiptClaimDetail.setReceiptClaimId(receiptClaim.getId());
            receiptClaimDetail.setCustomerId(dto.getCustomerId());
            receiptClaimDetail.setCustomerCode(receiptClaim.getCrmCustomerCode());
            receiptClaimDetail.setCustomerName(receiptClaim.getCrmCustomerName());
            receiptClaimDetail.setClaimStatus(ReceiptClaimEnum.UNCLAIM.getCode());//未认款
            receiptClaimDetail.setErpStatus(ReceiptClaimEnum.ERP_NOT_PUSH.getCode());//erp未处理
            receiptClaimDetail.setContractSyncStatus(CommonErpStatus.NOT_PUSH.code());//未同步合同号到erp
            receiptClaimDetail.setContractStatus(ReceiptClaimEnum.CONTRACT_UNALLOCATED.getCode());//合同状态
            receiptClaimDetail.setWriteOffStatus(WriteOffEnum.NON_WRITE_OFF.getCode());//未核销
            receiptClaimDetail.setDeletedFlag(0);
            receiptClaimDetailMapper.insert(receiptClaimDetail);
        }
    }

    /**
     * ERP推送
     *
     * @param paramDto
     * @return
     */
    @Override
    public void pushToERP(ReceiptClaimDto paramDto) {
        // 并发时候的key值
        String lockKey = Constants.DistributedLockKey.DISTRIBUTED_LOCK_RECEIPTCLAIMASYC;
        try {
            if (DistributedCASLock.lock(lockKey, BusinessTypeEnums.RECEIPT_CLAIM.getCode(), 1000 * 60L * 10,
                    1000 * 60L * 10)) {
                List<ReceiptClaimDto> receiptClaimDtoList = receiptClaimExtMapper.queryDetailList(paramDto);
                for (ReceiptClaimDto receiptClaimDto : receiptClaimDtoList) {
                    dealWithReceiptClaimAsyc(receiptClaimDto);
                }
            }
        } catch (Exception e) {
            logger.error("收款写入推送ERP异常", e);
            throw new BizException(Code.ERROR, e.getMessage());
        } finally {
            DistributedCASLock.unLock(lockKey, BusinessTypeEnums.RECEIPT_CLAIM.getCode());
        }
    }

    @Transactional
    @Override
    public void dealWithReceiptClaimAsyc(ReceiptClaimDto receiptClaimDto) {
        //查询同步状态
        ReceiptClaimDetail receiptClaimDetail = receiptClaimDetailMapper.selectByPrimaryKey(receiptClaimDto.getId());
        if (Objects.equals(receiptClaimDetail.getErpStatus(), ReceiptClaimEnum.ERP_ACCOUNTING.getCode()) ||
                Objects.equals(receiptClaimDetail.getErpStatus(), ReceiptClaimEnum.ERP_ACCOUNTED.getCode())) {
            logger.info(receiptClaimDto.getReceiptCode() + "不符合推送条件.");
            return;
        }
        // 未认款的无需推送到erp
        if (Objects.equals(receiptClaimDetail.getClaimStatus(), ReceiptClaimEnum.UNCLAIM.getCode())) {
            logger.info(receiptClaimDto.getReceiptCode() + "不符合推送条件.");
            return;
        }
        //更新状态为记账中
        ReceiptClaimDetail detail = new ReceiptClaimDetail();
        detail.setId(receiptClaimDto.getId());
        detail.setErpStatus(ReceiptClaimEnum.ERP_ACCOUNTING.getCode());
        detail.setContractSyncStatus(CommonErpStatus.PUSHING.code());
        receiptClaimDetailMapper.updateByPrimaryKeySelective(detail);
        //记录此次同步的信息到代办管理数据同步信息记录项中
        AgencySynInfoExample agencySynInfoExample = new AgencySynInfoExample();
        agencySynInfoExample.createCriteria().andBusinessTypeEqualTo(BusinessTypeEnums.RECEIPT_CLAIM.getCode())
                .andApplyNoEqualTo(receiptClaimDto.getId().toString()).andDeletedFlagEqualTo(Boolean.FALSE);
        final List<AgencySynInfo> agencySyninfoList = agencySynInfoMapper.selectByExample(agencySynInfoExample);
        if (ListUtils.isNotEmpty(agencySyninfoList)) {
            for (AgencySynInfo agencySyninfo : agencySyninfoList) {
                agencySyninfo.setSynStartTime(new Date());
                agencySynInfoMapper.updateByPrimaryKey(agencySyninfo);
            }
        } else {
            AgencySynInfo agencySyninfo = new AgencySynInfo();
            agencySyninfo.setBusinessType(BusinessTypeEnums.RECEIPT_CLAIM.getCode());
            agencySyninfo.setApplyNo(receiptClaimDto.getId().toString());
            agencySyninfo.setSynStartTime(new Date());
            agencySyninfo.setDeletedFlag(Boolean.FALSE);
            agencySynInfoMapper.insert(agencySyninfo);
        }

        //插入erp推送队列
        HandleDispatcher.route(BusinessTypeEnums.RECEIPT_CLAIM.getCode(), String.valueOf(receiptClaimDto.getId()),
                null, null, true);
    }

    /**
     * ERP冲销
     *
     * @param dtoList
     * @return
     */
    @Override
    public ReceiptClaimDto reverseFromERP(List<ReceiptClaimDto> dtoList, String reason, String erpReversalDate) {
        for (ReceiptClaimDto dto : dtoList) {
            dto.setErpReversalReason(reason);
            dto.setErpReversalDate(DateUtil.parseDate(erpReversalDate, DateUtil.DATE_PATTERN));
            dto.setErpStatus(ReceiptClaimEnum.ERP_REVERING.getCode());
            this.saveClaimDetail(dto);
            //插入erp推送队列
            HandleDispatcher.route(BusinessTypeEnums.RECEIPT_CLAIM_REVERSAL.getCode(), String.valueOf(dto.getId()),
                    null, null, true);
        }
        return null;
    }


    @Override
    public void pamResultReturnHandleReversal(Long id, Boolean isSuccess, String msg) {
        ReceiptClaimDto dtoParam = new ReceiptClaimDto();
        dtoParam.setId(id);
        ReceiptClaimDto dto = this.queryReceiptClaimDetailList(dtoParam).get(0);
        if (isSuccess) {
            dto.setErpStatus(ReceiptClaimEnum.ERP_REVERSED.getCode());//冲销成功
            dto.setErpMessage("");

            this.saveClaimDetail(dto);
            this.createReceiptClaimDetail(dto);

            //如果ReceiptClaim行都冲销成功，且处于资金撤回中，才插入erp推送队列
            ReceiptClaimDetailExample example = new ReceiptClaimDetailExample();
            example.createCriteria().andReceiptClaimIdEqualTo(dto.getReceiptClaimId()).andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
            List<ReceiptClaimDetail> receiptClaimDetails = receiptClaimDetailMapper.selectByExample(example);
            if (ListUtils.isNotEmpty(receiptClaimDetails)) {
                //如果list中的所有行receiptClaimDetail中 getClaimStatus = 5 , getErpStatus = 6 或者1 则返回true
                boolean flag = receiptClaimDetails.stream()
                        .allMatch(e -> Objects.equals(e.getClaimStatus(), ReceiptClaimEnum.GCEB_REVERSING.getCode()) &&
                                (Objects.equals(e.getErpStatus(), ReceiptClaimEnum.ERP_NOT_PUSH.getCode()) || Objects.equals(e.getErpStatus(), ReceiptClaimEnum.ERP_REVERSED.getCode())));
                if (flag) {
                    //插入erp推送队列 PAM-GFP-006
                    HandleDispatcher.route(BusinessTypeEnums.CANCEL_THE_CLAIM.getCode(), String.valueOf(dto.getReceiptClaimId()),
                            null, null, false);
                }
            }
            //如果是撤回那么回款计划详情列表里面的回款金额需要减掉
            ReceiptClaimContractRelVO relVO = writeOffService.getContractedList(id);
            List<ReceiptClaimContractRelDto> receiptClaimContractRelDtos = Objects.isNull(relVO) ? new ArrayList<>()
                    : relVO.getReceiptClaimContractRelDtoList();
            if (ListUtils.isEmpty(receiptClaimContractRelDtos)) {
                return;
            }
            for (ReceiptClaimContractRelDto receiptClaimContractRelDto : receiptClaimContractRelDtos) {
                this.unDoReceiptPlanDetails(receiptClaimContractRelDto.getContractId(),
                        receiptClaimContractRelDto.getAllocatedAmount(),
                        receiptClaimContractRelDto.getInvoicePlanDetailId());
                receiptClaimContractRelDto.setDeletedFlag(1);
                rcContractRelMapper.updateByPrimaryKeySelective(receiptClaimContractRelDto);
            }
        } else {
            dto.setErpStatus(ReceiptClaimEnum.ERP_REVERSE_FAILED.getCode());//冲销失败
            dto.setErpMessage(msg);
            this.saveClaimDetail(dto);
        }
    }

    /**
     * 撤销回款金额_回款计划详情列表
     */
    @Override
    public void unDoReceiptPlanDetails(Long contractId, BigDecimal allocatedAmount, Long invoicePlanDetailId) {
        ReceiptInvoiceRelationExample receiptInvoiceRelationExample = new ReceiptInvoiceRelationExample();
        ReceiptInvoiceRelationExample.Criteria receiptInvoiceRelationExampleCriteria =
                receiptInvoiceRelationExample.createCriteria();
        receiptInvoiceRelationExampleCriteria.andInvoicePlanDetailIdEqualTo(invoicePlanDetailId).andDeletedFlagEqualTo(Boolean.FALSE).andContractIdEqualTo(contractId);
        List<ReceiptInvoiceRelation> receiptInvoiceRelationList =
                receiptInvoiceRelationMapper.selectByExample(receiptInvoiceRelationExample);
        Set<Long> receiptPlanDetailIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(receiptInvoiceRelationList)) {
            for (ReceiptInvoiceRelation receiptInvoiceRelation : receiptInvoiceRelationList) {
                Optional.ofNullable(receiptInvoiceRelation).map(rec -> receiptPlanDetailIds.add(rec.getReceiptPlanDetailId()));
            }
        }

        List<ReceiptPlanDetail> allReceiptPlanDetailList = new ArrayList<>();
        List<ReceiptPlanDetail> receiptPlanDetailList = null;
        List<ReceiptPlanDetail> receiptPlanDetailList1 = null;
        ReceiptPlanDetailExample example = new ReceiptPlanDetailExample();
        ReceiptPlanDetailExample example1 = new ReceiptPlanDetailExample();
        example.setOrderByClause("date desc");
        if (receiptPlanDetailIds != null && receiptPlanDetailIds.size() > 0) {
            example.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(new ArrayList<>(receiptPlanDetailIds));
            receiptPlanDetailList = receiptPlanDetailMapper.selectByExample(example);
            allReceiptPlanDetailList.addAll(receiptPlanDetailList);
        }


        if (receiptPlanDetailIds != null && receiptPlanDetailIds.size() > 0) {
            example1.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE).andIdNotIn(new ArrayList<>(receiptPlanDetailIds));
        } else {
            example1.createCriteria().andContractIdEqualTo(contractId).andDeletedFlagEqualTo(Boolean.FALSE);
        }
        receiptPlanDetailList1 = receiptPlanDetailMapper.selectByExample(example1);
        allReceiptPlanDetailList.addAll(receiptPlanDetailList1);

        List<ReceiptPlanDetail> list = new ArrayList<>();
        for (int i = allReceiptPlanDetailList.size() - 1; i >= 0; i--) {
            if (ObjectUtils.isEmpty(allReceiptPlanDetailList.get(i).getActualAmount()) || allReceiptPlanDetailList.get(i).getActualAmount().compareTo(BigDecimal.valueOf(0)) == 0) {
                continue;
            }
            allocatedAmount = allReceiptPlanDetailList.get(i).getActualAmount().subtract(allocatedAmount); //剩余待回退的分配金额
            if (allocatedAmount.compareTo(BigDecimal.valueOf(0L)) > -1) { // 实际回款金额大于等于此次分配的金额
                allReceiptPlanDetailList.get(i).setActualAmount(allocatedAmount);
                list.add(allReceiptPlanDetailList.get(i));
                break;
            } else { // 实际回款金额小于此次分配的金额
                allocatedAmount = allocatedAmount.abs(); //剩余待回退的分配金额
                allReceiptPlanDetailList.get(i).setActualAmount(BigDecimal.valueOf(0));
                list.add(allReceiptPlanDetailList.get(i));
            }
        }

        for (ReceiptPlanDetail receiptPlanDetail : list) {
            receiptPlanDetailMapper.updateByPrimaryKeySelective(receiptPlanDetail);
        }

    }

    @Override
    public PageInfo<ReceiptClaimDto> refundGetCashReceiptCode(Map<String, Object> param, Integer pageNum,
                                                              Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<ReceiptClaimDto> receiptClaimDtoList = receiptClaimExtMapper.getCashReceiptCodeForRefund(param);
        //查询付款方联行号
        List<String> bankNameList = receiptClaimDtoList.stream().map(ReceiptClaimDto::getPayBankName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bankNameList)) return new PageInfo<>();

        CnapsInfoExample example = new CnapsInfoExample();
        example.createCriteria().andBankNameIn(bankNameList).andDeletedFlagEqualTo(DeletedFlagEnum.VALID.code());
        Map<String, List<CnapsInfo>> bankNameMap = cnapsInfoMapper.selectByExample(example).stream().collect(Collectors.groupingBy(CnapsInfo::getBankName));
        //查询关联合同信息
        List<Long> idList = receiptClaimDtoList.stream().map(ReceiptClaimDto::getId).collect(Collectors.toList());
        final Map<String, Object> map = new HashMap<>();
        map.put("idList", idList);
        Map<Long, List<ReceiptClaimContractRelDto>> receiptClaimContractRelMap = receiptClaimExtMapper.getContractRelInfo(map).stream().collect(Collectors.groupingBy(ReceiptClaimContractRelDto::getReceiptClaimDetailId));
        //查询客户银行信息
//        List<String> crmCodeList = receiptClaimDtoList.stream().map(ReceiptClaimDto::getCustomerCode).collect(Collectors.toList());
//        CustomerDto customerDto = new CustomerDto();
//        customerDto.setCrmCodeList(crmCodeList);
//        Map<String, CustomerBankAccount> customerBankInfoMap = crmExtService.getCustomerBankInfoByCrmCodes(customerDto);

        if (CollectionUtils.isNotEmpty(receiptClaimDtoList)) {
            for (int i = 0; i < receiptClaimDtoList.size(); i++) {
                ReceiptClaimDto receiptClaimDto = receiptClaimDtoList.get(i);
                //这个历史远程调用接口SystemContext.getUnitId()报错，永远为空，直接注释优化掉
//                final CustomerBankAccount customerBankInfo = crmExtService.getCustomerBankInfo(receiptClaimDtoList.get(i).getCustomerCode(), receiptClaimDtoList.get(i).getOuId());
                //客户银行信息
//                CustomerBankAccount customerBankInfo = customerBankInfoMap.get(receiptClaimDto.getCustomerCode());
//                if (customerBankInfo != null) {
//                    receiptClaimDtoList.get(i).setRecBankName(customerBankInfo.getBankAccountName());
//                    receiptClaimDtoList.get(i).setRecAccountNo(customerBankInfo.getBankAccountNum());
//                    receiptClaimDtoList.get(i).setCnapsCode(customerBankInfo.getCnapsCode());
//                }

//                final Map<String, Object> map = new HashMap<>();
//                map.put("id", receiptClaimDtoList.get(i).getId());
//                final List<ReceiptClaimContractRelDto> receiptClaimContractRelDtos = receiptClaimExtMapper.getContractRelInfo(map);
//                receiptClaimDtoList.get(i).setReceiptClaimContractRelDtoList(receiptClaimContractRelDtos);
                //关联合同
                receiptClaimDto.setReceiptClaimContractRelDtoList(receiptClaimContractRelMap.get(receiptClaimDto.getId()));
                //联行号
                List<CnapsInfo> cnapsInfoList = bankNameMap.get(receiptClaimDtoList.get(i).getPayBankName());
                if (cnapsInfoList != null && cnapsInfoList.size() == 1) {
                    receiptClaimDto.setPayCnapsCode(cnapsInfoList.get(0).getCnapsCode());
                }
            }
        }
        return BeanConverter.convertPage(receiptClaimDtoList, ReceiptClaimDto.class);
    }

    @Override
    public PageInfo<ReceiptClaimContractRelDto> refundGetContractInfo(Map<String, Object> param, Integer pageNum,
                                                                      Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        final List<ReceiptClaimContractRelDto> contractRelInfo = receiptClaimExtMapper.getContractRelInfo(param);
        return BeanConverter.convertPage(contractRelInfo, ReceiptClaimContractRelDto.class);
    }


    /**
     * 1.erp撤回后创建新收款编号防止编号重复
     * 2.撤回资金(claimStatus!=4&&5)发起的erp撤回无需重新创建新收款 and 删除旧记录
     * 3.只有冲销成功(ErpStatus=6)的才需要创建新收款
     *
     * @param oldDto
     */
    @Override
    public void createReceiptClaimDetail(ReceiptClaimDto oldDto) {
        if (!oldDto.getClaimStatus().equals(ReceiptClaimEnum.GCEB_REVERSING.getCode()) &&
                !oldDto.getClaimStatus().equals(ReceiptClaimEnum.GCEB_REVERSE_FAILED.getCode())) {
            Integer newOrder = 1;
            //1.查询已有编号
            ReceiptClaimDetailExample example = new ReceiptClaimDetailExample();
            example.createCriteria().andReceiptClaimIdEqualTo(oldDto.getReceiptClaimId());
            List<ReceiptClaimDetail> detailList = receiptClaimDetailMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(detailList)) {
                List<Integer> codeList = new ArrayList<>();
                for (ReceiptClaimDetail receiptClaimDetail : detailList) {
                    String[] code = StringUtils.split(receiptClaimDetail.getReceiptCode(), '-');
                    if (code.length < 1) {
                        continue;
                    }
                    codeList.add(Integer.valueOf(code[code.length - 1]));
                }
                if (ListUtils.isNotEmpty(codeList)) {
                    //2.创建新编号：获取receipt_code最大值+1
                    newOrder = codeList.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList()).get(0) + 1;
                }
            }
            //2.创建新收款明细
            ReceiptClaimDto newDto = new ReceiptClaimDto();
            BeanUtils.copyProperties(oldDto, newDto, "id", "deletedFlag");
            newDto.setReceiptCode(oldDto.getCashReceiptCode() + "-" + String.valueOf(newOrder));
            newDto.setClaimStatus(ReceiptClaimEnum.UNCLAIM.getCode());//未认款
            newDto.setErpStatus(ReceiptClaimEnum.ERP_NOT_PUSH.getCode());//未同步
            newDto.setDeletedFlag(0);
            this.saveClaimDetail(newDto);
            //3.删除被冲销的记录
            oldDto.setDeletedFlag(1);
            this.saveClaimDetail(oldDto);
        }
    }

    /**
     * 收款中的所有认款从ERP撤回
     *
     * @param receiptClaimId
     * @return
     */
    @Override
    public boolean reverseFromErpAll(Long receiptClaimId, String reason, String erpReversalDate) {
        List<ReceiptClaimDto> reversalList = new ArrayList<>();
        ReceiptClaimDto dtoParam = new ReceiptClaimDto();
        dtoParam.setReceiptClaimId(receiptClaimId);
        List<ReceiptClaimDto> receiptClaimDtoList = receiptClaimExtMapper.queryDetailList(dtoParam);
        for (ReceiptClaimDto dto : receiptClaimDtoList) {
            //场景1：已认款并且同步状态=已同步，操作从ERP撤回资金：
            //已记账+冲销失败
            if (Arrays.asList(ReceiptClaimEnum.CLAIMED.getCode(), ReceiptClaimEnum.GCEB_REVERSE_FAILED.getCode()).
                    contains(dto.getClaimStatus())
                    && Arrays.asList(ReceiptClaimEnum.ERP_ACCOUNTED.getCode(), ReceiptClaimEnum.ERP_REVERSE_FAILED.getCode()).
                    contains(dto.getErpStatus())) {
                reversalList.add(dto);
            }
            //场景2：认款状态=未认款，操作从PAM撤回资金：
            else if (Arrays.asList(ReceiptClaimEnum.UNCLAIM.getCode(), ReceiptClaimEnum.GCEB_REVERSE_FAILED.getCode()).
                    contains(dto.getClaimStatus())
                    && Arrays.asList(ReceiptClaimEnum.ERP_NOT_PUSH.getCode(), ReceiptClaimEnum.ERP_REVERSE_FAILED.getCode()).
                    contains(dto.getErpStatus())
            ) {

            } else {
                throw new BizException(Code.ERROR, "认款状态或Erp同步状态不符合要求，不进行操作!");
            }

            int num = receiptClaimExtMapper.findEffectiveContractRel(dto.getId());
            if (num > 0) {
                throw new BizException(Code.ERROR, "请先删除回款分配合同，然后再撤至认款或资金!");
            }
            if (!WriteOffEnum.NON_WRITE_OFF.getCode().equals(dto.getWriteOffStatus())) {
                throw new BizException(Code.ERROR, dto.getReceiptCode() + "认款进行过核销,不能执行撤回操作!");
            }
        }
        this.reverseFromERP(reversalList, reason, erpReversalDate);
        //如果reversalList不为空，则收款冲销一定触发，此时PAM-GFP-006在收款冲销回调中触发
        if (ListUtils.isNotEmpty(reversalList)) {
            return true;
        }
        return false;
    }


    @Override
    @Transactional
    public void refundUndo(Long receiptClaimId, String reason, String erpReversalDate) {
        ReceiptClaimDto dtoParam = new ReceiptClaimDto();
        dtoParam.setId(receiptClaimId);
        List<ReceiptClaimDto> receiptClaimDtoList = receiptClaimExtMapper.queryDetailList(dtoParam);
        ReceiptClaimDto dto = receiptClaimDtoList.get(0);

        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(dto.getPaymentInvoiceId());
        if (paymentInvoice.getPaymentApplyId() != null) {
            throw new BizException(Code.ERROR, "退款已经重付，不能撤回!");
        }
        dto.setErpReversalReason(reason);
        dto.setErpReversalDate(DateUtil.parseDate(erpReversalDate, DateUtil.DATE_PATTERN));
        dto.setErpStatus(ReceiptClaimEnum.ERP_REVERING.getCode());
        ReceiptClaimDetail detail = new ReceiptClaimDetail();
        BeanUtils.copyProperties(dto, detail);
        receiptClaimDetailMapper.updateByPrimaryKey(detail);

        //更新应付发票的同步状态
        paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.CHECKING.code());//同步中
        paymentInvoiceMapper.updateByPrimaryKeySelective(paymentInvoice);

        HandleDispatcher.route(BusinessTypeEnums.CANCEL_INVOICE.getCode(), String.valueOf(paymentInvoice.getId()),
                null, null, true);
    }

    @Override
    public int findEffectiveContractRel(Long id) {
        return receiptClaimExtMapper.findEffectiveContractRel(id);
    }

    /**
     * 资金撤回中状态更新
     *
     * @return
     */
    @Override
    public void gcebDrawbacking(Long receiptClaimId, boolean flag) {
        //头状态更新为-资金撤回中
        ReceiptClaim receiptClaim = new ReceiptClaim();
        receiptClaim.setId(receiptClaimId);
        receiptClaim.setCashStatus(ReceiptClaimEnum.GCEB_REVERSING.getCode());
        this.saveReceipt(receiptClaim);
        //行状态更新为-资金撤回中
        ReceiptClaimDto dtoParam = new ReceiptClaimDto();
        dtoParam.setReceiptClaimId(receiptClaimId);
        List<ReceiptClaimDto> receiptClaimDtoList = receiptClaimExtMapper.queryDetailList(dtoParam);
        for (ReceiptClaimDto dto : receiptClaimDtoList) {
            dto.setClaimStatus(ReceiptClaimEnum.GCEB_REVERSING.getCode());//资金撤回中
            this.saveClaimDetail(dto);
        }
        if (!flag) {
            //插入erp推送队列 未推送给erp的场景
            HandleDispatcher.route(BusinessTypeEnums.CANCEL_THE_CLAIM.getCode(), String.valueOf(receiptClaimId),
                    null, null, false);
        }
    }

    /**
     * 撤回至资金定时任务
     *
     * @return
     */
    @Override
    public void drawbackToGcebJob() {
        List<Integer> statusList = new ArrayList<>();
        statusList.add((ReceiptClaimEnum.GCEB_REVERSING.getCode()));
        statusList.add((ReceiptClaimEnum.GCEB_REVERSE_FAILED.getCode()));
        ReceiptClaimExample example = new ReceiptClaimExample();
        example.createCriteria().andCashStatusIn(statusList).andDeletedFlagEqualTo(0);
        List<ReceiptClaim> receiptClaimList = receiptClaimMapper.selectByExample(example);
        for (ReceiptClaim receiptClaim : receiptClaimList) {
            this.drawbackToGceb(receiptClaim.getId());
        }
    }

    /**
     * 撤回至资金
     *
     * @param receiptClaimId
     * @return
     */
    @Override
    public void drawbackToGceb(Long receiptClaimId) {
        ReceiptClaimDto dtoParam = new ReceiptClaimDto();
        dtoParam.setReceiptClaimId(receiptClaimId);
        List<ReceiptClaimDto> receiptClaimDtoList = receiptClaimExtMapper.queryDetailList(dtoParam);
        String msg = StringUtil.EMPTY_STRING;
        //校验
        for (ReceiptClaimDto dto : receiptClaimDtoList) {
            if (!Objects.equals(ReceiptClaimEnum.ERP_NOT_PUSH.getCode(), dto.getErpStatus()) &&
                    !Objects.equals(ReceiptClaimEnum.ERP_REVERSED.getCode(), dto.getErpStatus()) &&
                    !Objects.equals(ReceiptClaimEnum.ERP_ACCOUNT_FAILED.getCode(), dto.getErpStatus())) {
                if (!StringUtils.isEmpty(msg)) {
                    msg = msg + ",";
                }
                msg = msg + dto.getReceiptCode();
            }
        }
        if (!StringUtils.isEmpty(msg)) {
            logger.info("存在ERP未冲销的收款：" + msg);
            return;
        }
        //资金撤回
        ReceiptClaim receiptClaim = receiptClaimMapper.selectByPrimaryKey(receiptClaimId);
        BeanUtils.copyProperties(receiptClaim, dtoParam);
        //收款认领撤销
        callGcebCancelTheClaim(dtoParam);
        //更新资金状态
        receiptClaim.setCashStatus(dtoParam.getCashStatus());
        receiptClaim.setAttribute6(dtoParam.getAttribute6());
        receiptClaimMapper.updateByPrimaryKeySelective(receiptClaim);
        //更新认款状态
        if (ReceiptClaimEnum.GCEB_REVERSED.getCode().equals(dtoParam.getCashStatus())) {
            for (ReceiptClaimDto dto : receiptClaimDtoList) {
                dto.setClaimStatus(ReceiptClaimEnum.GCEB_REVERSED.getCode());//已撤回资金
                this.saveClaimDetail(dto);
            }
        }
    }

    private void callGcebCancelTheClaim(ReceiptClaimDto dto) {
        Integer status = null;
        String msg = StringUtil.EMPTY_STRING;
        EsbResponse<String> stringEsbResponse = passGcebToPamService.receiveCancel(dto);
        //返回结果处理
        if (Objects.nonNull(stringEsbResponse)) {
            if (EsbConstant.SUCCESS_CODE.equals(stringEsbResponse.getResponsecode())) {
                status = ReceiptClaimEnum.GCEB_REVERSED.getCode();//成功
            } else {
                msg = stringEsbResponse.getResponsecode() + stringEsbResponse.getResponsemessage();
                status = ReceiptClaimEnum.GCEB_REVERSE_FAILED.getCode();//撤回失败
            }
        }
        dto.setCashStatus(status);
        dto.setAttribute6(msg);
    }

    /**
     * 非销售业务场景
     *
     * @param busiSceneNonSaleId
     * @return
     */
    @Override
    public ReceiptClaimDto getNonSaleSceneDetail(Long busiSceneNonSaleId) {
        ReceiptClaimDto receiptClaimDto = new ReceiptClaimDto();
        //非销售场景查询
        BusiSceneNonSaleDetailDto paramDto = new BusiSceneNonSaleDetailDto();
        paramDto.setBusiSceneNonSaleId(busiSceneNonSaleId);
        List<BusiSceneNonSaleDetailDto> detailDtoList = busiSceneNonSaleService.detailDtoList(paramDto);
        //根据业务规则取场景配置值
        for (BusiSceneNonSaleDetailDto detailDto : detailDtoList) {
            receiptClaimDto.setBusiScene(detailDto.getBusiSceneName());
            if ("AP".equals(detailDto.getModule())) {
                if ("FP".equals(detailDto.getType())) {//应付发票
                    receiptClaimDto.setApInvoiceSubject(detailDto.getAccountGroupDebit());
                } else if ("FK".equals(detailDto.getType())) {//付款
                    //todo
                }
            } else if ("AR".equals(detailDto.getModule())) {
                if ("FP".equals(detailDto.getType())) {//应收发票
                    receiptClaimDto.setArInvoiceType(detailDto.getCustTrxTypeIdName());
                    receiptClaimDto.setCustTrxTypeId(detailDto.getCustTrxTypeId());
                } else if ("BZSK".equals(detailDto.getType())) {//标准收款
                    //todo
                } else if ("ZS".equals(detailDto.getType())) {//杂收
                    receiptClaimDto.setArReceiptActivity(detailDto.getReceivablesTrxName());
                    receiptClaimDto.setReceivablesTrxId(detailDto.getReceivablesTrxId());
                }
            } else if ("GL".equals(detailDto.getModule())) {
                if ("RJZ".equals(detailDto.getType())) {//日记账
                    receiptClaimDto.setGlSubject(detailDto.getAccountGroupDebit());
                }
            }
        }
        receiptClaimDto.setBusiSceneId(busiSceneNonSaleId);
        return receiptClaimDto;
    }

    /**
     * 从缓存获取ou名称
     *
     * @param dto
     */
    private void setOuName(ReceiptClaimDto dto) {
        OperatingUnit ou = CacheDataUtils.findOuById(dto.getOuId());
        if (!ObjectUtils.isEmpty(ou)) {
            dto.setOuName(ou.getOperatingUnitName());
        }
    }

    /**
     * 从缓存用户名称
     *
     * @param dto
     */
    private void setUserName(ReceiptClaimDto dto) {
        if (!ObjectUtils.isEmpty(dto.getClaimBy())) {
            UserInfo user = CacheDataUtils.findUserById(dto.getClaimBy());
            if (!ObjectUtils.isEmpty(user)) {
                dto.setClaimByName(user.getName());
            }
        }
    }

    /**
     * 查询收款方法名称
     *
     * @param dto
     */
    private void setRecMethodName(ReceiptClaimDto dto) {
        if (!ObjectUtils.isEmpty(dto.getOuId()) && !ObjectUtils.isEmpty(dto.getRecMethod())) {
            List<ReceiptMethodDto> list = basedataExtService.getReceiptMethod(dto.getOuId(), Long.parseLong(dto.getRecMethod()), null, null);
            if (CollectionUtils.isNotEmpty(list)) {
                dto.setRecMethodName(list.get(0).getReceiptMethodName());
            }
        }
    }
}
