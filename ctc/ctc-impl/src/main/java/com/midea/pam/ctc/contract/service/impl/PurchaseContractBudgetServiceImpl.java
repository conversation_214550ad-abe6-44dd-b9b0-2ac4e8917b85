package com.midea.pam.ctc.contract.service.impl;

import com.midea.pam.common.ctc.dto.HroRequirementDto;
import com.midea.pam.common.ctc.dto.PurchaseContractBudgetDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractBudget;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import com.midea.pam.common.enums.PurchaseContractStatus;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementPurchaseTypeEnums;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementStatus;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.contract.service.PurchaseContractBudgetService;
import com.midea.pam.ctc.contract.service.PurchaseContractService;
import com.midea.pam.ctc.mapper.PurchaseContractBudgetExtMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementMapper;
import com.midea.pam.ctc.mapper.PurchaseOrderExtMapper;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class PurchaseContractBudgetServiceImpl implements PurchaseContractBudgetService {

    @Resource
    private PurchaseContractBudgetExtMapper purchaseContractBudgetExtMapper;
    @Resource
    private PurchaseMaterialRequirementMapper purchaseMaterialRequirementMapper;
    @Resource
    private PurchaseContractBudgetService purchaseContractBudgetService;
    @Resource
    private PurchaseContractService purchaseContractService;
    @Resource
    private PurchaseOrderExtMapper purchaseOrderExtMapper;


    /**
     * 获取采购合同wbs关联预算
     *
     * @param purchaseContractId 采购合同id
     * @return
     */
    @Override
    public List<PurchaseContractBudgetDto> getWbsContractBudgetByPurchaseContractId(Long purchaseContractId) {
        PurchaseContract purchaseContract = purchaseContractService.findById(purchaseContractId);
        if ("点工采购".equals(purchaseContract.getTypeName())) {
            List<PurchaseContractBudgetDto> purchaseContractBudgetDtos = purchaseContractBudgetExtMapper
                    .selectHroRequirementBudget(purchaseContractId);
            if (!purchaseContractBudgetDtos.isEmpty()) {
                List<Long> purchaseRequirementIds = purchaseContractBudgetDtos.stream()
                        .map(PurchaseContractBudgetDto::getPurchaseRequirementId).collect(Collectors.toList());
                List<PurchaseContractBudgetDto> budgetDtos = purchaseContractBudgetExtMapper
                        .selectRequirementBudgetCostInfo(purchaseRequirementIds);
                Map<Long, BigDecimal> budgetMap = new HashMap<>(purchaseRequirementIds.size());
                Map<Long, BigDecimal> numberMap = new HashMap<>(purchaseRequirementIds.size());
                budgetDtos.forEach(e -> {
                    if (e.getContractStatus() != PurchaseContractStatus.DRAFT.getCode()
                            && e.getContractStatus() != PurchaseContractStatus.REFUSE.getCode()) {
                        BigDecimal budget = budgetMap.computeIfAbsent(e.getPurchaseRequirementId(), key -> BigDecimal.ZERO);
                        budgetMap.put(e.getPurchaseRequirementId(), budget.add(e.getLocalTotalPrice()));
                    }
                    BigDecimal number = numberMap.computeIfAbsent(e.getPurchaseRequirementId(), key -> BigDecimal.ZERO);
                    numberMap.put(e.getPurchaseRequirementId(), number.add(e.getNumber()));
                });
                purchaseContractBudgetDtos.forEach(e -> {
                    e.setContractTotalAmount(budgetMap.get(e.getPurchaseRequirementId()));
                    e.setReleasedQuantity(numberMap.get(e.getPurchaseRequirementId()));
                    e.setUnreleasedAmount(e.getNeedTotal().subtract(
                            e.getReleasedQuantity() == null ? BigDecimal.ZERO : e.getReleasedQuantity()));
                    e.setWbsRemainingDemandOsCost(e.getWbsDemandOsCost().subtract(
                            e.getContractTotalAmount() == null ? BigDecimal.ZERO : e.getContractTotalAmount()));
                    e.setExcludingTaxAmount(purchaseContract.getExcludingTaxAmount());
                });
                List<String> requirementCodes = purchaseContractBudgetDtos.stream()
                        .map(PurchaseContractBudgetDto::getRequirementCode).collect(Collectors.toList());
                Map<String, BigDecimal> requirementBudgetMap = purchaseContractBudgetExtMapper.selectRequirementBudget(requirementCodes)
                        .stream().collect(Collectors.toMap(HroRequirementDto::getRequirementCode, HroRequirementDto::getTotalBudget));
                purchaseContractBudgetDtos.forEach(e -> e.setDemandCost(requirementBudgetMap.get(e.getRequirementCode())));
            }
            return purchaseContractBudgetDtos;
        } else {
            Map<String, Object> param = new HashMap<>(2);
            param.put("purchaseContractId", purchaseContractId);
            param.put("purchaseType", PurchaseMaterialRequirementPurchaseTypeEnums.OUTSOURCE.getCode());
            List<PurchaseContractBudgetDto> wbsContractBudgetList = purchaseContractBudgetExtMapper.getWbsContractBudgetByParam(param);
            if (CollectionUtils.isNotEmpty(wbsContractBudgetList)) {
                //webType=1走新的计算方式
                if (Objects.equals(wbsContractBudgetList.get(0).getWebType(), 1)) {
                    List<Long> projectWbsReceiptsIdList = wbsContractBudgetList.stream().map(PurchaseContractBudgetDto::getReceiptsId).collect(Collectors.toList());
                    projectWbsReceiptsIdList.add(-1L); //防空
                    List<PurchaseOrderDetailDto> orderTotalAmountList = purchaseOrderExtMapper.getDiscountMoneyByProjectWbsReceiptsIdList(projectWbsReceiptsIdList, null, null);
                    Map<String, BigDecimal> orderTotalAmountMap = orderTotalAmountList.stream()
                            .collect(Collectors.groupingBy(s -> buildWbsGroupKey(s.getProjectWbsReceiptsId(), s.getWbsSummaryCode())
                                    , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().map(PurchaseOrderDetailDto::getDiscountMoney).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
                    for (PurchaseContractBudgetDto dto : wbsContractBudgetList) {
                        BigDecimal wbsDemandTotalCost = Optional.ofNullable(dto.getWbsDemandTotalCost()).orElse(BigDecimal.ZERO);
                        BigDecimal contractTotalAmount = Optional.ofNullable(dto.getContractTotalAmount()).orElse(BigDecimal.ZERO);
                        BigDecimal orderTotalAmount = orderTotalAmountMap.getOrDefault(buildWbsGroupKey(dto.getReceiptsId(), dto.getWbsSummaryCode()), BigDecimal.ZERO);
                        //剩余WBS需求预算占用（外包） = 预算占用金额（汇总） - 累计采购合同占用金额 - 累计采购订单占用金额
                        dto.setWbsRemainingDemandOsCost(wbsDemandTotalCost.subtract(contractTotalAmount).subtract(orderTotalAmount));
                        /** 页面计算超预算是： WBS需求预算占用（外包）- 累计采购合同占用金额 的差额，和填写的金额比较。这样改的原因，为了让新的计算方式适配前端的超预算计算  **/
                        //WBS需求预算占用（外包） = 预算占用金额（汇总） - 累计采购订单占用金额
                        dto.setWbsDemandOsCost(wbsDemandTotalCost.subtract(orderTotalAmount));
                    }
                }
            }
            return wbsContractBudgetList;
        }
    }

    @Override
    public void updateRequirementStatus(Long contractId) {
        //查询合同新增时关联的采购物料，如果已采购量=总需求量(即未采购量=0)，采购需求状态改为：已关闭；否则改为：待采购
        List<PurchaseContractBudgetDto> budgetDtos =
                purchaseContractBudgetService.getWbsContractBudgetByPurchaseContractId(contractId);
        if (ListUtil.isPresent(budgetDtos)) {
            for (PurchaseContractBudgetDto budget : budgetDtos) {
                PurchaseMaterialRequirement requirement = new PurchaseMaterialRequirement();
                requirement.setId(budget.getPurchaseRequirementId());
                if (BigDecimal.ZERO.compareTo(budget.getUnreleasedAmount()) == 0) {
                    requirement.setStatus(PurchaseMaterialRequirementStatus.CLOSED.code());
                } else {
                    requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
                }
                purchaseMaterialRequirementMapper.updateByPrimaryKeySelective(requirement);
            }
        }
    }

    @Override
    public void deleteByPurchaseContractId(Long purchaseContractId) {
        purchaseContractBudgetExtMapper.deleteByPurchaseContractId(purchaseContractId);

    }

    @Override
    public void batchInsert(List<PurchaseContractBudget> purchaseContractBudgetList) {
        purchaseContractBudgetExtMapper.batchInsert(purchaseContractBudgetList);
    }

    private String buildWbsGroupKey(Long projectWbsReceiptsId, String wbsSummaryCode) {
        return String.format("buildWbsGroupKey_%s_%s", wbsSummaryCode, projectWbsReceiptsId);
    }
}
