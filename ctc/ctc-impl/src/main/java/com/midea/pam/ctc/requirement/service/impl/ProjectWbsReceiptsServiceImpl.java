package com.midea.pam.ctc.requirement.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.ProjectActivityCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleDetailCache;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailChangeDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailSubmitHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectHistoryHeaderDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsBudgetChangeHistoryDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsBudgetDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDesignPlanRelDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.dto.ProjectWbsSummaryDto;
import com.midea.pam.common.ctc.entity.ErpCodeRule;
import com.midea.pam.common.ctc.entity.ErpCodeRuleExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChange;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChangeExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChangeRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistoryExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeaderExample;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummary;
import com.midea.pam.common.ctc.entity.ProjectWbsBudgetSummaryExample;
import com.midea.pam.common.ctc.entity.ProjectWbsChangeReceiptDetail;
import com.midea.pam.common.ctc.entity.ProjectWbsChangeReceiptDetailExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetChangeHistory;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetChangeHistoryExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudgetExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsDesignPlanRel;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsDesignPlanRelExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsRequirementChangeRecord;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsRequirementChangeRecordExample;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementExample;
import com.midea.pam.common.ctc.entity.RequirementCodeRecord;
import com.midea.pam.common.ctc.entity.RequirementCodeRecordExample;
import com.midea.pam.common.ctc.entity.WbsCustomizeRule;
import com.midea.pam.common.ctc.entity.WbsCustomizeRuleExample;
import com.midea.pam.common.ctc.entity.WbsTemplateInfo;
import com.midea.pam.common.ctc.excelVo.ImportProjectRequirementPublishExcelVO;
import com.midea.pam.common.ctc.excelVo.ImportProjectWbsRequirementPublishExcelVO;
import com.midea.pam.common.ctc.excelVo.ProjectWbsReceiptsDetailExcelVo;
import com.midea.pam.common.ctc.vo.DesignPlanDetailChangeHistoryVO;
import com.midea.pam.common.enums.*;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.gateway.entity.WorkFlowDraftSubmitTemporaryRecord;
import com.midea.pam.common.gateway.entity.WorkFlowDraftSubmitTemporaryRecordExample;
import com.midea.pam.common.statistics.vo.ProjectWbsCostByReceiptsVO;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BeanMapTool;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.common.util.cache.WbsTemplateRuleDetailCacheUtils;
import com.midea.pam.ctc.common.enums.ChangeTypeEnum;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.MilepostDesignPlanDetailModelStatus;
import com.midea.pam.ctc.common.enums.ProjectWbsReceiptsBudgetDemandTypeEnums;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementPurchaseTypeEnums;
import com.midea.pam.ctc.common.enums.PurchaseMaterialRequirementStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.mapper.EmailExtMapper;
import com.midea.pam.ctc.mapper.ErpCodeRuleMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailSubmitHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectHistoryHeaderMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryMapper;
import com.midea.pam.ctc.mapper.ProjectWbsChangeReceiptDetailExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsChangeReceiptDetailMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsDesignPlanRelExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsDesignPlanRelMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsExtMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsMapper;
import com.midea.pam.ctc.mapper.ProjectWbsReceiptsRequirementChangeRecordMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementExtMapper;
import com.midea.pam.ctc.mapper.PurchaseMaterialRequirementMapper;
import com.midea.pam.ctc.mapper.RequirementCodeRecordExtMapper;
import com.midea.pam.ctc.mapper.RequirementCodeRecordMapper;
import com.midea.pam.ctc.mapper.WbsCustomizeRuleMapper;
import com.midea.pam.ctc.mapper.WbsTemplateInfoMapper;
import com.midea.pam.ctc.mapper.WorkFlowDraftSubmitTemporaryRecordExtMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsBudgetChangeHistoryService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsBudgetService;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeRecordService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailSubmitHistoryService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectWbsReceiptsDesignPlanRelService;
import com.midea.pam.ctc.service.impl.MilepostDesignPlanConfirmRecordServiceImpl;
import com.midea.pam.ctc.wbs.service.ProjectWbsBudgetService;
import com.midea.pam.ctc.wbs.service.WbsTemplateRuleDetailService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 详细设计单据
 */
public class ProjectWbsReceiptsServiceImpl implements ProjectWbsReceiptsService {

    private final static Logger logger = LoggerFactory.getLogger(ProjectWbsReceiptsServiceImpl.class);

    private final static Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    @Value("${route.projectWbsReceiptsUrl}")
    private String projectWbsReceiptsUrl;

    @Value("${route.milepostDesignPlanChangeUrl}")
    private String milepostDesignPlanChangeUrl;

    @Value("${route.wbsChangeReceiptsUrl}")
    private String wbsChangeReceiptsUrl;

    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectWbsReceiptsMapper projectWbsReceiptsMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private ProjectTypeMapper projectTypeMapper;
    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;
    @Resource
    private MilepostDesignPlanConfirmRecordServiceImpl milepostDesignPlanConfirmRecordServiceImpl;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private ProjectWbsReceiptsBudgetService projectWbsReceiptsBudgetService;
    @Resource
    private ProjectWbsReceiptsDesignPlanRelMapper ProjectWbsReceiptsDesignPlanRelMapper;
    @Resource
    private ProjectWbsReceiptsExtMapper projectWbsReceiptsExtMapper;
    @Resource
    private ProjectWbsReceiptsDesignPlanRelService projectWbsReceiptsDesignPlanRelService;
    @Resource
    private MilepostDesignPlanDetailSubmitHistoryService milepostDesignPlanDetailSubmitHistoryService;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private ProjectWbsReceiptsDesignPlanRelMapper projectWbsReceiptsDesignPlanRelMapper;
    @Resource
    private ProjectWbsReceiptsBudgetChangeHistoryMapper projectWbsReceiptsBudgetChangeHistoryMapper;
    @Resource
    private ProjectWbsReceiptsBudgetChangeHistoryService projectWbsReceiptsBudgetChangeHistoryService;
    @Resource
    private MilepostDesignPlanDetailChangeService milepostDesignPlanDetailChangeService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;
    @Resource
    private PurchaseMaterialRequirementExtMapper purchaseMaterialRequirementExtMapper;
    @Resource
    private ProjectWbsReceiptsDesignPlanRelExtMapper projectWbsReceiptsDesignPlanRelExtMapper;
    @Resource
    private ProjectWbsBudgetSummaryMapper projectWbsBudgetSummaryMapper;
    @Resource
    private PurchaseMaterialRequirementMapper purchaseMaterialRequirementMapper;
    @Resource
    private WorkFlowDraftSubmitTemporaryRecordExtMapper workFlowDraftSubmitTemporaryRecordExtMapper;
    @Resource
    private ErpCodeRuleMapper erpCodeRuleMapper;
    @Resource
    private RequirementCodeRecordMapper requirementCodeRecordMapper;
    @Resource
    private RequirementCodeRecordExtMapper requirementCodeRecordExtMapper;
    @Resource
    private ProjectWbsReceiptsBudgetMapper projectWbsReceiptsBudgetMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private EmailExtMapper emailExtMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MilepostDesignPlanDetailChangeRecordService milepostDesignPlanDetailChangeRecordService;
    @Resource
    private ProjectWbsReceiptsRequirementChangeRecordMapper projectWbsReceiptsRequirementChangeRecordMapper;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private MilepostDesignPlanDetailSubmitHistoryMapper submitHistoryMapper;
    @Resource
    private WbsTemplateInfoMapper wbsTemplateInfoMapper;
    @Resource
    private ProjectWbsReceiptsBudgetExtMapper projectWbsReceiptsBudgetExtMapper;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private ProjectWbsBudgetService projectWbsBudgetService;
    @Resource
    private ProjectWbsChangeReceiptDetailMapper projectWbsChangeReceiptDetailMapper;
    @Resource
    private ProjectWbsChangeReceiptDetailExtMapper projectWbsChangeReceiptDetailExtMapper;
    @Resource
    private ProjectHistoryHeaderMapper projectHistoryHeaderMapper;
    @Resource
    private WbsTemplateRuleDetailService wbsTemplateRuleDetailService;
    @Resource
    private WbsCustomizeRuleMapper wbsCustomizeRuleMapper;


    /**
     * 查看详情
     *
     * @param receiptsId
     * @return
     */
    @Override
    public ProjectWbsReceiptsDto findRequirementDetail(Long receiptsId) {
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(receiptsId);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);


        // 在途成本、已发生成本、剩余可用预算
        Map<String, Map<String, BigDecimal>> summaryMap = getSummaryMap(receipts.getProjectId());

        ProjectWbsReceiptsDto dto = BeanConverter.copy(receipts, ProjectWbsReceiptsDto.class);


        if (Objects.nonNull(receipts.getBuyerId())) {
            UserInfo userInfo = CacheDataUtils.findUserById(receipts.getBuyerId());
            if (Objects.nonNull(userInfo)) {
                dto.setBuyerMip(userInfo.getUsername());
            }
        }

        //查询附件信息
        List<CtcAttachmentDto> attachmentDtos =
                ctcAttachmentService.findCtcAttachmentDtos(receiptsId, CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code(), null);
        dto.setAttachmentList(attachmentDtos);

        //1.查询需求发布单据关联的详设行
        ProjectWbsReceiptsDesignPlanRelExample relExample1 = new ProjectWbsReceiptsDesignPlanRelExample();
        relExample1.createCriteria().andProjectWbsReceiptsIdEqualTo(receiptsId).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsReceiptsDesignPlanRel> receiptsDesignPlanRels = projectWbsReceiptsDesignPlanRelMapper.selectByExample(relExample1);
        dto.setDesignRelList(BeanConverter.copy(receiptsDesignPlanRels, ProjectWbsReceiptsDesignPlanRelDto.class));

        //查询变更记录，即该需求发布单据相关的全部详细设计变更单据
        List<ProjectWbsReceiptsDto> changeReceiptsList =
                projectWbsReceiptsBudgetChangeHistoryService.selectChangeReceiptsByPublishReceiptsId(receiptsId);
        dto.setChangeReceiptsList(changeReceiptsList);

        //查询需求发布单据关联的 详细设计需求预算变更记录
        ProjectWbsReceiptsRequirementChangeRecordExample changeRecordExample = new ProjectWbsReceiptsRequirementChangeRecordExample();
        changeRecordExample.createCriteria().andProjectWbsReceiptsIdEqualTo(receiptsId).andDeletedFlagEqualTo(Boolean.FALSE);
        changeRecordExample.setOrderByClause("create_at desc");
        List<ProjectWbsReceiptsRequirementChangeRecord> requirementChangeRecordList =
                projectWbsReceiptsRequirementChangeRecordMapper.selectByExample(changeRecordExample);
        if (ListUtils.isNotEmpty(requirementChangeRecordList)) {
            changeReceiptsList = CollectionUtils.isEmpty(dto.getChangeReceiptsList()) ? new ArrayList<>() : dto.getChangeReceiptsList();

            for (ProjectWbsReceiptsRequirementChangeRecord changeRecord : requirementChangeRecordList) {
                ProjectWbsReceiptsDto copy = BeanConverter.copy(changeRecord, ProjectWbsReceiptsDto.class);
                copy.setRequirementStatus(changeRecord.getRecordStatus());
                copy.setRequirementType(RequirementTypeEnum.RECEIPTS_REQUIREMENT_CHANGE.getCode());
                changeReceiptsList.add(copy);
            }
            changeReceiptsList.sort(Comparator.comparing(ProjectWbsReceiptsDto::getCreateAt).reversed());
            dto.setChangeReceiptsList(changeReceiptsList);
        }

        //查询详设信息
        if (Objects.equals(dto.getConfirmMode(), ReceiptsConfirmModeEnum.PURCHASE.getCode())) {
            setDetailByPurchase(dto);
        } else if (Objects.equals(dto.getConfirmMode(), ReceiptsConfirmModeEnum.CONFIRM.getCode())) {
            setDetailByConfirm(dto);
        }
        //查询预算信息
        List<ProjectWbsReceiptsBudgetDto> budgetList = projectWbsReceiptsBudgetService.findRequirementBudget(receipts, receipts.getProjectId());
        logger.info("findDetail的budgetList：{}", JSON.toJSONString(budgetList));
        if (MapUtils.isNotEmpty(summaryMap)) {
            for (ProjectWbsReceiptsBudgetDto budgetDto : budgetList) {
                Map<String, BigDecimal> bigDecimalMap = summaryMap.get(budgetDto.getWbsSummaryCode());
                if (bigDecimalMap != null) {
                    budgetDto.setPrice(Optional.ofNullable(bigDecimalMap.get(WbsBudgetFieldConstant.PRICE)).orElse(BigDecimal.ZERO));
                    budgetDto.setDemandCost(Optional.ofNullable(bigDecimalMap.get(WbsBudgetFieldConstant.DEMAND_COST)).orElse(BigDecimal.ZERO));
                    budgetDto.setOnTheWayCost(Optional.ofNullable(bigDecimalMap.get(WbsBudgetFieldConstant.ON_THE_WAY_COST)).orElse(BigDecimal.ZERO));
                    budgetDto.setIncurredCost(Optional.ofNullable(bigDecimalMap.get(WbsBudgetFieldConstant.INCURRED_COST)).orElse(BigDecimal.ZERO));
                    budgetDto.setRemainingCost(Optional.ofNullable(bigDecimalMap.get(WbsBudgetFieldConstant.REMAINING_COST)).orElse(BigDecimal.ZERO));
                } else {
                    budgetDto.setPrice(BigDecimal.ZERO);
                    budgetDto.setDemandCost(BigDecimal.ZERO);
                    budgetDto.setOnTheWayCost(BigDecimal.ZERO);
                    budgetDto.setIncurredCost(BigDecimal.ZERO);
                    budgetDto.setRemainingCost(BigDecimal.ZERO);
                }
            }
        }
        dto.setBudgetList(budgetList);
        //需求发布单为维度，预算占用金额
        BigDecimal totalAmount = budgetList.stream().map(ProjectWbsReceiptsBudgetDto::getBudgetOccupiedAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        dto.setMaxTotalAmount(totalAmount);
        return dto;
    }

    private Map<String, Map<String, BigDecimal>> getSummaryMap(Long projectId) {
        Map<String, Map<String, BigDecimal>> resultMap = new HashMap<>();
        ProjectWbsBudgetSummaryExample projectWbsBudgetSummaryExample = new ProjectWbsBudgetSummaryExample();
        projectWbsBudgetSummaryExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(projectId)
                .andSummaryTypeEqualTo(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType()).andProjectDetailSelectFlagEqualTo(true);
        List<ProjectWbsBudgetSummary> summaryList = projectWbsBudgetSummaryMapper.selectByExample(projectWbsBudgetSummaryExample);
        logger.info("getSummaryMap的summaryList：{}", JSON.toJSONString(summaryList));
        if (CollectionUtils.isEmpty(summaryList)) {
            return resultMap;
        }

        Map<String, List<ProjectWbsBudgetSummary>> wbsSummaryCodeMap = summaryList.stream().filter(e -> StringUtils.isNotEmpty(e.getSummaryCode()))
                .collect(Collectors.groupingBy(ProjectWbsBudgetSummary::getSummaryCode));
        // k:summaryCode  v:List<ProjectWbsBudgetSummary>
        wbsSummaryCodeMap.forEach((k, v) -> {
            BigDecimal price =
                    v.stream().map(e -> Optional.ofNullable(e.getPrice()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal demandCost =
                    v.stream().map(e -> Optional.ofNullable(e.getDemandCost()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal onTheWayCost =
                    v.stream().map(e -> Optional.ofNullable(e.getOnTheWayCost()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal incurredCost =
                    v.stream().map(e -> Optional.ofNullable(e.getIncurredCost()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal remainingCost =
                    v.stream().map(e -> Optional.ofNullable(e.getRemainingCost()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            Map<String, BigDecimal> projectWbsSummaryMap = new HashMap<>();
            projectWbsSummaryMap.put(WbsBudgetFieldConstant.PRICE, price);
            projectWbsSummaryMap.put(WbsBudgetFieldConstant.DEMAND_COST, demandCost);
            projectWbsSummaryMap.put(WbsBudgetFieldConstant.ON_THE_WAY_COST, onTheWayCost);
            projectWbsSummaryMap.put(WbsBudgetFieldConstant.INCURRED_COST, incurredCost);
            projectWbsSummaryMap.put(WbsBudgetFieldConstant.REMAINING_COST, remainingCost);
            resultMap.put(k, projectWbsSummaryMap);
        });
        logger.info("getSummaryMap的resultMap：{}", JSON.toJSONString(resultMap));
        return resultMap;
    }

    /**
     * 树过滤（前端搜索结果反查父级）
     *
     * @param dto
     * @return
     */
    @Override
    public ProjectWbsReceiptsDto filterTreeSearch(ProjectWbsReceiptsDto dto) {
        List<Long> searchIdList = dto.getSearchIdList();
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = dto.getDesignPlanDetailDtos();
        if (CollectionUtils.isEmpty(designPlanDetailDtos) || CollectionUtils.isEmpty(searchIdList)) return dto;

        //标识需要删除的节点
        Boolean flag = false;
        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
            if (ListUtil.isPresent(designPlanDetailDto.getSonDtos())) {
                //递归标识勾选节点及其父节点之外的节点
                flag = checkWithRecursiveSearch(designPlanDetailDto, searchIdList);
            }
            if (designPlanDetailDto != null && searchIdList.contains(designPlanDetailDto.getId())) {
                flag = true;
            }
            designPlanDetailDto.setDeletedFlag(!flag);
        }

        //删减标识的节点
        Iterator<MilepostDesignPlanDetailDto> iterator = designPlanDetailDtos.iterator();
        while (iterator.hasNext()) {
            MilepostDesignPlanDetailDto item = iterator.next();
            if (item.getDeletedFlag()) iterator.remove();
        }
        if (ListUtil.isPresent(designPlanDetailDtos)) {
            for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                //递归删除勾选节点及其父节点之外的节点
                deleteWithRecursive(designPlanDetailDto);
            }
        }
        return dto;
    }

    /**
     * 树过滤（勾选项反查父级）
     *
     * @param dto
     * @return
     */
    @Override
    public ProjectWbsReceiptsDto filterTree(ProjectWbsReceiptsDto dto) {
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = dto.getDesignPlanDetailDtos();
        Asserts.notEmpty(designPlanDetailDtos, ErrorCode.CTC_PROJECT_WBS_DESIGN_PLAN_DETAIL_LIST_NOT_NULL);

        //标识需要删除的节点
        boolean flag = false;
        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
            if (ListUtil.isPresent(designPlanDetailDto.getSonDtos())) {
                //递归标识勾选节点及其父节点之外的节点
                flag = checkWithRecursive(designPlanDetailDto);
            }
            if ((designPlanDetailDto.getIsCheck() != null && designPlanDetailDto.getIsCheck()) && !(Objects.equals(designPlanDetailDto.getModuleStatus(), MilepostDesignPlanDetailModelStatus.CONFIRMED.code()) || Objects.equals(designPlanDetailDto.getModuleStatus(),
                    MilepostDesignPlanDetailModelStatus.CONFIRMING.code()))) {
                flag = true;
            }
            designPlanDetailDto.setDeletedFlag(!flag);
        }

        //删减标识的节点
        designPlanDetailDtos.removeIf(MilepostDesignPlanDetail::getDeletedFlag);
        if (ListUtil.isPresent(designPlanDetailDtos)) {
            for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
                //递归删除勾选节点及其父节点之外的节点
                deleteWithRecursive(designPlanDetailDto);
            }
        }
        return dto;
    }

    private static void deleteWithRecursive(MilepostDesignPlanDetailDto designPlanDetailDto) {
        List<MilepostDesignPlanDetailDto> sonDtos = designPlanDetailDto.getSonDtos();
        if (CollectionUtils.isEmpty(sonDtos)) return;

        Iterator<MilepostDesignPlanDetailDto> iterator = sonDtos.iterator();
        while (iterator.hasNext()) {
            MilepostDesignPlanDetailDto item = iterator.next();
            if (item.getDeletedFlag()) iterator.remove();
        }
        if (ListUtil.isPresent(sonDtos)) {
            for (MilepostDesignPlanDetailDto sonDto : sonDtos) {
                deleteWithRecursive(sonDto);
            }
        }
    }

    private static Boolean checkWithRecursive(MilepostDesignPlanDetailDto designPlanDetailDto) {
        List<MilepostDesignPlanDetailDto> sonDtos = designPlanDetailDto.getSonDtos();
        if (CollectionUtils.isEmpty(sonDtos)) return false;

        boolean flag = false;
        for (MilepostDesignPlanDetailDto sonDto : sonDtos) {
            if (ListUtil.isPresent(designPlanDetailDto.getSonDtos())) {
                flag = checkWithRecursive(sonDto);
            }
            if ((Objects.nonNull(sonDto) && sonDto.getIsCheck() != null && sonDto.getIsCheck()) && !(Objects.equals(sonDto.getModuleStatus(),
                    MilepostDesignPlanDetailModelStatus.CONFIRMED.code()) || Objects.equals(sonDto.getModuleStatus(),
                    MilepostDesignPlanDetailModelStatus.CONFIRMING.code()) || Boolean.TRUE.equals(sonDto.getHasConfirmed()))) {
                flag = true;
            }
            sonDto.setDeletedFlag(!flag); //节点自身或其子节点没有勾选的，标识删除
        }
        return sonDtos.stream().anyMatch(s -> !s.getDeletedFlag()); //子节点只要有一个是没有标识删除的则返回true
    }

    private static Boolean checkWithRecursiveSearch(MilepostDesignPlanDetailDto designPlanDetailDto,
                                                    List<Long> searchIdList) {
        List<MilepostDesignPlanDetailDto> sonDtos = designPlanDetailDto.getSonDtos();
        if (CollectionUtils.isEmpty(sonDtos)) return false;

        boolean flag = false;
        for (MilepostDesignPlanDetailDto sonDto : sonDtos) {
            if (ListUtil.isPresent(designPlanDetailDto.getSonDtos())) {
                flag = checkWithRecursiveSearch(sonDto, searchIdList);
            }
            if (Objects.nonNull(sonDto) && searchIdList != null && searchIdList.contains(sonDto.getId())) {
                flag = true;
            }
            sonDto.setDeletedFlag(!flag); //节点自身或其子节点不在搜索结果集里的，标识删除
        }
        return sonDtos.stream().anyMatch(s -> !s.getDeletedFlag()); //子节点只要有一个是没有标识删除的则返回true
    }

    /**
     * 保存 新建界面第二步
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectWbsReceiptsDto saveReceipts(ProjectWbsReceiptsDto dto, Long userBy) {
        Guard.notNull(dto, "入参不能为空");
        logger.info("saveReceipts的dto：{}，userBy：{}", JsonUtils.toString(dto), userBy);
        Guard.notNull(dto.getProducerId(), "制单人id不能为空");
        Guard.notNull(dto.getHandleBy(), "处理人id不能为空");
        Guard.notNull(dto.getConfirmMode(), "确认方式不能为空");

        if (Objects.nonNull(dto.getId())) {
            ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMapper.selectByPrimaryKey(dto.getId());
            Guard.notNull(projectWbsReceipts, String.format("需求发布单据id：%s不存在", dto.getId()));
            if (Objects.equals(projectWbsReceipts.getRequirementStatus(), RequirementStatusEnum.PROCESS.getCode())) {
                throw new ApplicationBizException("单据状态已更新，请刷新页面");
            }
        }

        List<ProjectWbsReceiptsDesignPlanRelDto> designRelList = dto.getDesignRelList();
        Guard.notNullOrEmpty(designRelList, "WBS详细设计关联列表不能为空");

        List<Long> designPlanDetailIdList =
                designRelList.stream().map(ProjectWbsReceiptsDesignPlanRelDto::getDesignPlanDetailId).collect(Collectors.toList());
        List<ProjectWbsReceiptsDto> receiptsDtoList = selectByDetailIds(designPlanDetailIdList);
        Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = receiptsDtoList.stream().filter(e -> !Objects.equals(e.getId(), dto.getId())
                && Objects.nonNull(e.getDesignPlanDetailId())).collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId));

        MilepostDesignPlanDetailExample planDetailExample = new MilepostDesignPlanDetailExample();
        planDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(designPlanDetailIdList);
        List<MilepostDesignPlanDetail> detailList = milepostDesignPlanDetailMapper.selectByExample(planDetailExample);
        // 不校验工具层的P码
        detailList = detailList.stream().filter(e -> !Boolean.TRUE.equals(e.getWbsLastLayer())).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(detailList)) {
            List<String> pamCodeList = new ArrayList<>();
            for (MilepostDesignPlanDetail designPlanDetail : detailList) {
                List<ProjectWbsReceiptsDto> projectWbsReceiptsDtoList = receiptsDtoMap.get(designPlanDetail.getId());
                Integer moduleStatus = designPlanDetail.getModuleStatus();
                Guard.isFalse((Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMED.code(), moduleStatus)
                                || Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMING.code(), moduleStatus))
                                && ListUtils.isNotEmpty(projectWbsReceiptsDtoList),
                        String.format("P码：%s，WBS：%s的详设已经确认中或者已确认", designPlanDetail.getPamCode(), designPlanDetail.getWbsSummaryCode()));

                pamCodeList.add(designPlanDetail.getPamCode());
            }
            Long organizationId = projectService.getOrganizationIdByProjectId(dto.getProjectId());
            List<Material> materialList = materialExtService.invokeMaterialApiGetByPamCodeList(pamCodeList, organizationId);
            List<String> notExistPamCodeList =
                    pamCodeList.stream().filter(pamCode -> materialList.stream().noneMatch(e -> Objects.equals(e.getPamCode(), pamCode)))
                            .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(notExistPamCodeList)) {
                throw new ApplicationBizException(String.format("P码：[%s]的物料已被删除", notExistPamCodeList.toString()));
            }
        }

        saveCheck(dto);

        //部分确认：当前"外购物料"、"看板物料"存在没有物料到货日期的，不允许勾选
        //进度确认：当前模组下级的"外购物料"、"看板物料"存在没有物料到货日期的，不允许勾选
        for (ProjectWbsReceiptsDesignPlanRelDto relDto : designRelList) {
            if (("看板物料".equals(relDto.getMaterialCategory()) || "外购物料".equals(relDto.getMaterialCategory())) && Objects.isNull(relDto.getDeliveryTime())) {
                throw new MipException("详细设计行没有物料到货日期，不能勾选");
            }
        }

        //草稿状态下，删除单据现有详细设计关联
        if (dto.getId() != null && Objects.equals(dto.getRequirementStatus(), RequirementStatusEnum.DRAFT.getCode())) {
            if (!Objects.equals(dto.getProducerId(), SystemContext.getUserId())) {
                throw new MipException("当前用户不是单据的制单人，不能操作确认");
            }
            projectWbsReceiptsExtMapper.updateReceiptsDel(dto.getId());
        }

        //保存单据信息
//        dto.setProducerId(userBy);
        dto.setUnitId(SystemContext.getUnitId());
        dto.setRequirementStatus(RequirementStatusEnum.TODO.getCode());
        dto.setRequirementType(RequirementTypeEnum.REQUIREMENT_PUBLISH.getCode());
        dto.setProcessName(generateProcessName(dto.getProjectName(), dto.getConfirmMode()));

        if (dto.getId() != null || StringUtils.isNotEmpty(dto.getRequirementCode())) {
            if (dto.getId() != null) {
                ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMapper.selectByPrimaryKey(dto.getId());
                if (StringUtils.isEmpty(projectWbsReceipts.getRequirementCode())) {
                    dto.setRequirementCode(generateRequirementCode());
                }
            } else {
                ProjectWbsReceipts receipts = getByRequirementCode(dto.getRequirementCode());
                if (null != receipts) {
                    dto.setId(receipts.getId());
                }
            }
        } else {
            dto.setRequirementCode(generateRequirementCode());
        }

        if (dto.getId() == null) {
            // 查询组织参数：不区分外包WBS模板
            Set<String> valueSet = organizationCustomDictService.queryByName(Constants.WBS_TEMPLATE_NEW_WEB_TYPE, SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
            if (!CollectionUtils.isEmpty(valueSet)) {
                Project project = projectService.selectByPrimaryKey(dto.getProjectId());
                Asserts.notNull(project, ErrorCode.CTC_PROJECT_NOT_FIND);
                WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(project.getWbsTemplateInfoId());
                if (wbsTemplateInfo != null && valueSet.contains(wbsTemplateInfo.getTemplateName())) {
                    dto.setWebType(1);
                }
            }
        }
        ProjectWbsReceiptsDto resultDto = this.save(dto, SystemContext.getUserId());

        // 保存详细设计关联
        if (Objects.equals(dto.getConfirmMode(), ReceiptsConfirmModeEnum.PURCHASE.getCode())) {
            savePurchaseRecordWithRecursiveDetail(dto, userBy);
        } else if (Objects.equals(dto.getConfirmMode(), ReceiptsConfirmModeEnum.CONFIRM.getCode())) {
            saveConfirmRecordWithRecursiveDetail(dto, userBy);
        } else {
            throw new ApplicationBizException("确认模式错误");
        }

        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
        ctcAttachmentDto.setModuleId(dto.getId());

        ctcAttachmentService.deleteByModule(ctcAttachmentDto);
        //保存附件信息
        if (ListUtil.isPresent(dto.getAttachmentList())) {
            for (CtcAttachmentDto attachmentDto : dto.getAttachmentList()) {
                if (attachmentDto.getId() == null) {
                    attachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
                    attachmentDto.setModuleId(resultDto.getId());
                }
                ctcAttachmentService.save(attachmentDto, userBy);
            }
        }

        // 创建需求预算
        projectWbsReceiptsBudgetService.createRequirementBudget(resultDto);
        sendEmailForProjectWbsReceiptTodo(resultDto, resultDto.getHandleBy(), NoticeBusinessType.PROJECT_WBS_RECEIPTS_TODO);
        return resultDto;
    }

    private String buildTodoRouteUrl(ProjectWbsReceipts projectWbsReceipts) {
        RequirementTypeEnum requirementTypeEnum = RequirementTypeEnum.getEnumByCode(projectWbsReceipts.getRequirementType());
        switch (requirementTypeEnum) {
            case RECEIPTS_REQUIREMENT_CHANGE:
                return projectWbsReceiptsUrl + projectWbsReceipts.getId()
                        + "?id=" + projectWbsReceipts.getId() + "&projectName=" + projectWbsReceipts.getProjectName()
                        + "&projectCode=" + projectWbsReceipts.getProjectCode() + "&projectId=" + projectWbsReceipts.getProjectId()
                        + "&requirementStatus=" + projectWbsReceipts.getRequirementStatus() + "&type=" + projectWbsReceipts.getConfirmMode()
                        + "&requirementType=" + projectWbsReceipts.getRequirementType();
            case WBS_CHANGE:
                return wbsChangeReceiptsUrl
                        + "?id=" + projectWbsReceipts.getId()
                        + "&projectId=" + projectWbsReceipts.getProjectId()
                        + "&requirementType=" + projectWbsReceipts.getRequirementType();
            default:
                return "";
        }
    }

    private void sendEmailForProjectWbsReceiptTodo(ProjectWbsReceipts projectWbsReceipts, Long receiverId, NoticeBusinessType noticeBusinessType) {
        if (null == projectWbsReceipts) {
            return;
        }

        try {
            String subject = String.format("PAM详细设计单据提醒：请关注详细设计单据“%s”的处理情况（待处理）", projectWbsReceipts.getRequirementCode());
            StringBuilder content = new StringBuilder("<html><div style='font-size: 12px;'>");
            content.append(String.format("<div>PAM详细设计单据提醒：请关注详细设计单据“%s”的处理情况</div>", projectWbsReceipts.getRequirementCode()));
            content.append(String.format("<div><a href=\"%s\" target='_blank'>点击跳转至PAM处理</a></div>", buildTodoRouteUrl(projectWbsReceipts)));
            content.append(String.format("<div>项目名称：%s</div>", projectWbsReceipts.getProjectName()));
            content.append(String.format("<div>单据号：%s</div>", projectWbsReceipts.getRequirementCode()));
            content.append(String.format("<div>单据状态：%s</div>",
                    Optional.ofNullable(RequirementStatusEnum.getNameByCode(projectWbsReceipts.getRequirementStatus())).orElse("")));
            content.append(String.format("<div>制单人：%s</div>", projectWbsReceipts.getProducerName()));
            content.append(String.format("<div>处理人：%s</div>", projectWbsReceipts.getHandleName()));
            content.append(String.format("<div>单据类型：%s</div>",
                    Optional.ofNullable(RequirementTypeEnum.getNameByCode(projectWbsReceipts.getRequirementType())).orElse("")));
            content.append(String.format("<div>确认方式：%s</div>",
                    Optional.ofNullable(ReceiptsConfirmModeEnum.getNameByCode(projectWbsReceipts.getConfirmMode())).orElse("")));
            content.append("</div></html>");

            sendEmail(receiverId, null, noticeBusinessType.getType(), subject, content.toString());
        } catch (Exception e) {
            logger.error("{}邮件发送失败", noticeBusinessType.getName(), e);
        }
    }

    /**
     * 保存 新建界面第二步
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectWbsReceiptsDto saveReceiptsForChange(ProjectWbsReceiptsDto dto, Long userBy) {
        Guard.notNull(dto, "saveReceiptsForChange入参不能为空");
        logger.info("saveReceiptsForChange的dto：{}，userBy：{}", JsonUtils.toString(dto), userBy);

        //草稿状态下，删除单据现有详细设计关联
        if (dto.getId() != null && Objects.equals(dto.getRequirementStatus(), RequirementStatusEnum.DRAFT.getCode())) {
            if (!Objects.equals(dto.getProducerId(), userBy)) {
                throw new MipException("当前用户不是单据的制单人，不能操作确认");
            }
            projectWbsReceiptsExtMapper.updateReceiptsDel(dto.getId());
        }

        dto.setConfirmMode(null);

        //保存单据信息
//        dto.setProducerId(userBy);
        if (Boolean.TRUE.equals(dto.getHasConfirmed())) {
            dto.setRequirementStatus(RequirementStatusEnum.TODO.getCode());
        } else {
            dto.setRequirementStatus(RequirementStatusEnum.DRAFT.getCode());
        }
        dto.setRequirementType(RequirementTypeEnum.DESIGN_PLAN_CHANGE.getCode());
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
        } else {
            dto.setUpdateBy(userBy);
        }

        ProjectWbsReceiptsDto resultDto = this.save(dto, userBy);

        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
        ctcAttachmentDto.setModuleId(resultDto.getId());

        ctcAttachmentService.deleteByModule(ctcAttachmentDto);

        //保存附件信息
        if (ListUtil.isPresent(dto.getAttachmentList())) {
            for (CtcAttachmentDto attachmentDto : dto.getAttachmentList()) {
                if (attachmentDto.getId() == null) {
                    attachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
                    attachmentDto.setModuleId(resultDto.getId());
                }
                ctcAttachmentService.save(attachmentDto, userBy);
            }
        }

        //如果重新编辑
        ProjectWbsReceiptsBudgetChangeHistoryExample budgetChangeHistoryExample =
                new ProjectWbsReceiptsBudgetChangeHistoryExample();
        budgetChangeHistoryExample.createCriteria().andProjectWbsChangeReceiptsIdEqualTo(dto.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<ProjectWbsReceiptsBudgetChangeHistory> projectWbsReceiptsBudgetChangeHistories =
                projectWbsReceiptsBudgetChangeHistoryMapper.selectByExample(budgetChangeHistoryExample);

        if (ListUtils.isNotEmpty(projectWbsReceiptsBudgetChangeHistories)) {
            projectWbsReceiptsBudgetChangeHistories.forEach(history -> {
                history.setDeletedFlag(DeletedFlag.INVALID.code());
                history.setCreateAt(null);
                history.setCreateBy(null);
                projectWbsReceiptsBudgetChangeHistoryMapper.updateByPrimaryKeySelective(history);
            });
        }

        MilepostDesignPlanDetailChangeDto detailChangeDto = dto.getDetailChangeDto();
        //先获取所有的变更记录
        if (Objects.nonNull(detailChangeDto)) {
            final MilepostDesignPlanDetailChangeDto addDetailChangeDto = detailChangeDto;
            //找到所有被选的详设id，跟前端约定好传勾选的getUploadPathList
            List<Long> checkIds = dto.getUploadPathList().stream().map(MilepostDesignPlanDetailDto::getId).collect(Collectors.toList());

            if (ListUtils.isNotEmpty(checkIds)) {
                //从所有变更记录中找到对应所选id 从树形结构转换为普通list
                List<MilepostDesignPlanDetailChangeDto> detailChangeDtos = new ArrayList<>();
                if (checkIds.contains(detailChangeDto.getDesignPlanDetailId())) {
                    detailChangeDtos.add(detailChangeDto);
                }
                // 递归筛选出所有子级详设（被勾选），包含以前它自己的son
                if (ListUtils.isNotEmpty(detailChangeDto.getSonDtos())) {
                    filterCheckDetailDtos(detailChangeDto.getSonDtos(), detailChangeDtos, checkIds);
                }
                List<String> alreadyCreatePublishReceiptsIdAndWbsSummaryCode = new ArrayList<>();
                //筛选出变更的详设行
                for (MilepostDesignPlanDetailChangeDto checkDto : detailChangeDtos) {
                    dealCheckModel(checkDto, dto, userBy, alreadyCreatePublishReceiptsIdAndWbsSummaryCode);
                }
            }

            // 删除和修改 先生成了对应的需求发布预算变更，再处理添加的（用最原始的detailChangeDto）
            logger.info("dealCurrentDto前的addDetailChangeDto：{}", JsonUtils.toString(addDetailChangeDto));
            ProjectWbsReceiptsBudgetChangeHistoryExample example = new ProjectWbsReceiptsBudgetChangeHistoryExample();
            example.createCriteria().andProjectWbsChangeReceiptsIdEqualTo(dto.getId()).andDeletedFlagEqualTo(false);
            List<ProjectWbsReceiptsBudgetChangeHistory> existList =
                    projectWbsReceiptsBudgetChangeHistoryMapper.selectByExample(budgetChangeHistoryExample);
            logger.info("dealCurrentDto前的projectWbsChangeReceiptsId：{}，existList：{}", dto.getId(), JsonUtils.toString(existList));
            Long designPlanDetailId = addDetailChangeDto.getDesignPlanDetailId();
            Long parentDesignPlanDetailId = null;
            if (designPlanDetailId != null) {
                MilepostDesignPlanDetail milepostDesignPlanDetail = milepostDesignPlanDetailMapper.selectByPrimaryKey(designPlanDetailId);
                parentDesignPlanDetailId = Optional.ofNullable(milepostDesignPlanDetail).map(MilepostDesignPlanDetail::getParentId).orElse(null);
            }
            dealCurrentDto(addDetailChangeDto, dto, userBy, null, parentDesignPlanDetailId);
        }
        return resultDto;
    }

    private void dealCurrentDto(MilepostDesignPlanDetailChangeDto detailChangeDto, ProjectWbsReceiptsDto dto, Long userBy,
                                Long projectWbsPublishReceiptsId, Long parentDesignPlanDetailId) {
        String changeType = detailChangeDto.getChangeType();
        String materialCategory = detailChangeDto.getMaterialCategory();
        Boolean afterRequirementPublish = detailChangeDto.getAfterRequirementPublish();
        // 是添加并且是需求发布后、且是看板物料或者外购物料 的详设变更，添加物料外包(整包) 需求发布预算变更
        if (ChangeTypeEnum.ADD.getCode().equals(changeType) && Boolean.TRUE.equals(afterRequirementPublish)
                && (Objects.equals(materialCategory, "看板物料") || Objects.equals(materialCategory, "外购物料"))) {
            if (null == projectWbsPublishReceiptsId) {
                if (null != parentDesignPlanDetailId) {
                    // 每个当前物料循环的第一个if 的 parentDesignPlanDetailId肯定不为空
                    // 当前父级详设行id查到对应需求发布单据
                    List<ProjectWbsReceiptsDto> receiptsDtoList =
                            projectWbsReceiptsExtMapper.selectProcessReceiptsByDetailIds(Collections.singletonList(parentDesignPlanDetailId));
                    // 筛选出已生效的单据
                    List<ProjectWbsReceiptsDto> hasConfirmedReceiptsDtoList = receiptsDtoList.stream().filter(a -> Objects.equals(a.getProjectId(),
                                    dto.getProjectId()))
                            .sorted(Comparator.comparing(ProjectWbsReceiptsDto::getCreateAt).reversed()).collect(Collectors.toList());
                    if (ListUtils.isNotEmpty(hasConfirmedReceiptsDtoList)) {
                        projectWbsPublishReceiptsId = hasConfirmedReceiptsDtoList.get(0).getId();
                        dealAddAndAfterRequirementPublish(detailChangeDto, dto, userBy, projectWbsPublishReceiptsId);
                        // 当前是添加的物料，处理该物料的所有子级（projectWbsPublishReceiptsId都相同）
                        dealSonDtoList(detailChangeDto.getSonDtos(), dto, userBy, projectWbsPublishReceiptsId,
                                Optional.ofNullable(detailChangeDto.getDesignPlanDetailId()).orElse(parentDesignPlanDetailId));
                    }
                }
            } else {
                // projectWbsPublishReceiptsId为空说明父级肯定是添加的物料
                dealAddAndAfterRequirementPublish(detailChangeDto, dto, userBy, projectWbsPublishReceiptsId);
                // 当前是添加的物料，处理该物料的所有子级（projectWbsPublishReceiptsId都相同）
                dealSonDtoList(detailChangeDto.getSonDtos(), dto, userBy, projectWbsPublishReceiptsId,
                        Optional.ofNullable(detailChangeDto.getDesignPlanDetailId()).orElse(parentDesignPlanDetailId));
            }
        } else {
            List<MilepostDesignPlanDetailChangeDto> sonDtoList = detailChangeDto.getSonDtos();
            if (CollectionUtils.isEmpty(sonDtoList)) {
                return;
            }

            List<Long> designPlanDetailIdList = sonDtoList.stream().map(MilepostDesignPlanDetailChangeDto::getDesignPlanDetailId)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, Long> parentDesignPlanDetailIdMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(designPlanDetailIdList)) {
                List<MilepostDesignPlanDetailDto> dtoList = milepostDesignPlanDetailService.selectDetailByIds(designPlanDetailIdList);
                for (MilepostDesignPlanDetailDto detailDto : dtoList) {
                    parentDesignPlanDetailIdMap.put(detailDto.getId(), detailDto.getParentId());
                }
            }
            for (MilepostDesignPlanDetailChangeDto sonDto : sonDtoList) {
                // planDetailId为空则代表上下级都是添加
                Long planDetailId =
                        Optional.ofNullable(detailChangeDto.getDesignPlanDetailId()).orElse(parentDesignPlanDetailIdMap.get(sonDto.getDesignPlanDetailId()));
                dealCurrentDto(sonDto, dto, userBy, projectWbsPublishReceiptsId, Optional.ofNullable(planDetailId).orElse(parentDesignPlanDetailId));
            }
        }
    }

    private void dealSonDtoList(List<MilepostDesignPlanDetailChangeDto> sonDtoList, ProjectWbsReceiptsDto dto, Long userBy,
                                Long projectWbsPublishReceiptsId, Long parentDesignPlanDetailId) {
        if (CollectionUtils.isEmpty(sonDtoList)) {
            return;
        }

        List<Long> designPlanDetailIdList = sonDtoList.stream().map(MilepostDesignPlanDetailChangeDto::getDesignPlanDetailId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, Long> parentDesignPlanDetailIdMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(designPlanDetailIdList)) {
            List<MilepostDesignPlanDetailDto> dtoList = milepostDesignPlanDetailService.selectDetailByIds(designPlanDetailIdList);
            for (MilepostDesignPlanDetailDto detailDto : dtoList) {
                parentDesignPlanDetailIdMap.put(detailDto.getId(), detailDto.getParentId());
            }
        }
        for (MilepostDesignPlanDetailChangeDto sonDto : sonDtoList) {
            dealCurrentDto(sonDto, dto, userBy, projectWbsPublishReceiptsId,
                    Optional.ofNullable(parentDesignPlanDetailId).orElse(parentDesignPlanDetailIdMap.get(sonDto.getDesignPlanDetailId())));
        }
    }

    private void dealAddAndAfterRequirementPublish(MilepostDesignPlanDetailChangeDto detailChangeDto, ProjectWbsReceiptsDto dto,
                                                   Long userBy, Long projectWbsPublishReceiptsId) {
        String wbsSummaryCode = detailChangeDto.getWbsSummaryCode();
        ProjectWbsReceiptsBudgetExample budgetExample = new ProjectWbsReceiptsBudgetExample();
        budgetExample.createCriteria().andProjectWbsReceiptsIdEqualTo(projectWbsPublishReceiptsId)
                .andWbsSummaryCodeEqualTo(wbsSummaryCode).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsReceiptsBudget> budgetList = projectWbsReceiptsBudgetMapper.selectByExample(budgetExample);
        Map<Integer, BigDecimal> budgetMap = budgetList.stream()
                .collect(Collectors.toMap(ProjectWbsReceiptsBudget::getDemandType, ProjectWbsReceiptsBudget::getBudgetOccupiedAmount));

        ProjectWbsReceiptsBudgetChangeHistoryExample budgetChangeHistoryExample = new ProjectWbsReceiptsBudgetChangeHistoryExample();
        ProjectWbsReceiptsBudgetChangeHistoryExample.Criteria criteria = budgetChangeHistoryExample.createCriteria();
        criteria.andProjectWbsChangeReceiptsIdEqualTo(dto.getId()).andProjectWbsPublishReceiptsIdEqualTo(projectWbsPublishReceiptsId)
                .andWbsSummaryCodeEqualTo(wbsSummaryCode).andDeletedFlagEqualTo(false);
        List<ProjectWbsReceiptsBudgetChangeHistory> changeHistoryList =
                projectWbsReceiptsBudgetChangeHistoryMapper.selectByExample(budgetChangeHistoryExample);
        if (CollectionUtils.isEmpty(changeHistoryList)) {
            ProjectWbsReceiptsBudgetChangeHistory changeHistory = new ProjectWbsReceiptsBudgetChangeHistory();
            changeHistory.setProjectWbsChangeReceiptsId(dto.getId());
            changeHistory.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
            changeHistory.setId(null);
            changeHistory.setCreateBy(userBy);
            changeHistory.setCreateAt(new Date());
            changeHistory.setDeletedFlag(false);
            changeHistory.setWbsSummaryCode(wbsSummaryCode);
            changeHistory.setBudgetOccupiedAmountBefore(budgetMap.getOrDefault(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode(),
                    BigDecimal.ZERO));
            changeHistory.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode());
            changeHistory.setParentid(-1L);
            changeHistory.setVersion(1L);
            projectWbsReceiptsBudgetChangeHistoryMapper.insert(changeHistory);

            ProjectWbsReceiptsBudgetChangeHistory sonChangeHistory = new ProjectWbsReceiptsBudgetChangeHistory();
            sonChangeHistory.setProjectWbsChangeReceiptsId(dto.getId());
            sonChangeHistory.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
            sonChangeHistory.setId(null);
            sonChangeHistory.setCreateBy(userBy);
            sonChangeHistory.setCreateAt(new Date());
            sonChangeHistory.setDeletedFlag(false);
            sonChangeHistory.setWbsSummaryCode(wbsSummaryCode);
            if (Boolean.TRUE.equals(detailChangeDto.getExtIs())) {
                sonChangeHistory.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode());
            } else {
                sonChangeHistory.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode());
            }
            sonChangeHistory.setBudgetOccupiedAmountBefore(budgetMap.getOrDefault(sonChangeHistory.getDemandType(), BigDecimal.ZERO));
            sonChangeHistory.setParentid(changeHistory.getId());
            sonChangeHistory.setVersion(1L);
            projectWbsReceiptsBudgetChangeHistoryMapper.insert(sonChangeHistory);
        } else {
            Long parentId =
                    changeHistoryList.stream().filter(e -> Objects.equals(-1L, e.getParentid())).map(ProjectWbsReceiptsBudgetChangeHistory::getId)
                            .findFirst().orElse(-1L);
            if (Boolean.TRUE.equals(detailChangeDto.getExtIs())) {
                boolean exist = changeHistoryList.stream().anyMatch(e -> Objects.equals(e.getWbsSummaryCode(), wbsSummaryCode)
                        && Objects.equals(e.getDemandType(), ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode()));
                if (!exist) {
                    ProjectWbsReceiptsBudgetChangeHistory sonChangeHistory = new ProjectWbsReceiptsBudgetChangeHistory();
                    sonChangeHistory.setProjectWbsChangeReceiptsId(dto.getId());
                    sonChangeHistory.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
                    sonChangeHistory.setId(null);
                    sonChangeHistory.setCreateBy(userBy);
                    sonChangeHistory.setCreateAt(new Date());
                    sonChangeHistory.setDeletedFlag(false);
                    sonChangeHistory.setWbsSummaryCode(wbsSummaryCode);
                    sonChangeHistory.setBudgetOccupiedAmountBefore(budgetMap.getOrDefault(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode(), BigDecimal.ZERO));
                    sonChangeHistory.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode());
                    sonChangeHistory.setParentid(parentId);
                    projectWbsReceiptsBudgetChangeHistoryMapper.insert(sonChangeHistory);
                }
            } else {
                boolean exist = changeHistoryList.stream().anyMatch(e -> Objects.equals(e.getWbsSummaryCode(), wbsSummaryCode)
                        && Objects.equals(e.getDemandType(), ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode()));
                if (!exist) {
                    ProjectWbsReceiptsBudgetChangeHistory sonChangeHistory = new ProjectWbsReceiptsBudgetChangeHistory();
                    sonChangeHistory.setProjectWbsChangeReceiptsId(dto.getId());
                    sonChangeHistory.setProjectWbsPublishReceiptsId(projectWbsPublishReceiptsId);
                    sonChangeHistory.setId(null);
                    sonChangeHistory.setCreateBy(userBy);
                    sonChangeHistory.setCreateAt(new Date());
                    sonChangeHistory.setDeletedFlag(false);
                    sonChangeHistory.setWbsSummaryCode(wbsSummaryCode);
                    sonChangeHistory.setBudgetOccupiedAmountBefore(budgetMap.getOrDefault(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode(),
                            BigDecimal.ZERO));
                    sonChangeHistory.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode());
                    sonChangeHistory.setParentid(parentId);
                    projectWbsReceiptsBudgetChangeHistoryMapper.insert(sonChangeHistory);
                }
            }
        }
    }

    private void dealCheckModel(MilepostDesignPlanDetailChangeDto checkDto, ProjectWbsReceiptsDto dto, Long userBy,
                                List<String> alreadyCreatePublishReceiptsIdAndWbsSummaryCode) {
        logger.info("dealCheckModel的checkDto：{}，dto：{}，userBy：{}，alreadyCreatePublishReceiptsIdAndWbsSummaryCode：{}",
                JsonUtils.toString(checkDto), JsonUtils.toString(dto), userBy, JsonUtils.toString(alreadyCreatePublishReceiptsIdAndWbsSummaryCode));
        List<MilepostDesignPlanDetailChangeDto> hasConfirmedDtos = new ArrayList<>();
        List<MilepostDesignPlanDetailChangeDto> sonDtos = checkDto.getSonDtos();
        List<Long> needCofirmingIds = new ArrayList<>();
        // 有子级，并且子级中只要有一个正在进行 更新或者删除操作 并且不是模组
        if (ListUtils.isNotEmpty(sonDtos) && sonDtos.stream().anyMatch(sonDto -> (ChangeTypeEnum.UPDATE.getCode().equals(sonDto.getChangeType())
                || ChangeTypeEnum.DELETE.getCode().equals(sonDto.getChangeType())) && !Boolean.TRUE.equals(sonDto.getWhetherModel()))) {
            // 子级详设去单据表中查出所有的单据，这些单据只要任意一个单据状态为生效，且该行详设为 更新或者删除操作，且不是模组，这个子级详设就是被确认的详设行
            hasConfirmedDtos = sonDtos.stream().filter(a -> {
                List<ProjectWbsReceiptsDto> receiptsDtos =
                        projectWbsReceiptsExtMapper.selectProcessReceiptsByDetailIds(Collections.singletonList(a.getDesignPlanDetailId()));
                if (ListUtils.isNotEmpty(receiptsDtos)
                        && (ChangeTypeEnum.UPDATE.getCode().equals(a.getChangeType()) || ChangeTypeEnum.DELETE.getCode().equals(a.getChangeType()))
                        && !Boolean.TRUE.equals(a.getWhetherModel())) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
        }
        logger.info("dealCheckModel的sonDtos：{}，hasConfirmedDtos：{}", JsonUtils.toString(sonDtos), JSON.toJSONString(hasConfirmedDtos));

        // 当前详设行id查到对应需求发布单据
        List<ProjectWbsReceiptsDto> projectWbsReceiptsDtoList =
                projectWbsReceiptsExtMapper.selectProcessReceiptsByDetailIds(Collections.singletonList(checkDto.getDesignPlanDetailId()));
        // 筛选出已生效的单据
        List<ProjectWbsReceiptsDto> hasConfirmedReceiptsDtos = projectWbsReceiptsDtoList.stream()
                .filter(a -> Objects.equals(a.getProjectId(), dto.getProjectId()))
                .sorted(Comparator.comparing(ProjectWbsReceiptsDto::getCreateAt).reversed()).collect(Collectors.toList());
        logger.info("dealCheckModel的hasConfirmedReceiptsDtos：{}", JSON.toJSONString(hasConfirmedReceiptsDtos));

        // 筛选出已生效的单据如果有数据，当前的详设就是被进度确认
        if (ListUtils.isNotEmpty(hasConfirmedReceiptsDtos)) {
            if (ListUtils.isNotEmpty(sonDtos)) {
                needCofirmingIds = sonDtos.stream().filter(sonDto -> !(CheckStatus.WBS_RECEIPTS_DRAFT.code().equals(sonDto.getStatus())
                                || CheckStatus.WBS_RECEIPTS_PENDING.code().equals(sonDto.getStatus())
                                || CheckStatus.WBS_RECEIPTS_CHECKING.code().equals(sonDto.getStatus())))
                        .map(MilepostDesignPlanDetailChangeDto::getDesignPlanDetailId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }

            // 一个详设行只能被确认一次，一个详设行对应一个生效的需求发布单据
            ProjectWbsReceiptsDto hasConfirmedReceiptsDto = hasConfirmedReceiptsDtos.get(0);

            if (ListUtils.isNotEmpty(hasConfirmedDtos)) {
                dealWithPartConfirm(hasConfirmedDtos, dto.getId(), userBy, hasConfirmedReceiptsDto.getId(), checkDto.getMaterialCategory(),
                        alreadyCreatePublishReceiptsIdAndWbsSummaryCode, dto);
            }
            // 生成预算变更
            if (StringUtils.isNotEmpty(checkDto.getChangeType()) && Boolean.TRUE.equals(checkDto.getAfterRequirementPublish())) {
                if (Objects.equals(checkDto.getMaterialCategory(), "看板物料") || Objects.equals(checkDto.getMaterialCategory(), "外购物料")) {
                    projectWbsReceiptsBudgetChangeHistoryService.createReceiptsBudgetChangeHistory(dto.getId(), hasConfirmedReceiptsDto.getId(),
                            userBy, alreadyCreatePublishReceiptsIdAndWbsSummaryCode, checkDto.getWbsSummaryCode(),
                            new HashSet<>(Collections.singleton(checkDto.getExtIs())));
                } else if (Objects.equals(checkDto.getMaterialCategory(), "装配件") || Objects.equals(checkDto.getMaterialCategory(), "虚拟件")) {
                    dealFirstLevelChangeDto(checkDto.getSonDtos(), dto, userBy, alreadyCreatePublishReceiptsIdAndWbsSummaryCode);
                }
            }

            needCofirmingIds.add(checkDto.getDesignPlanDetailId());

            // 进度确认需求发布后的详设行的状态变更
            milepostDesignPlanService.updateDetailForChangeByDetailIds(needCofirmingIds, userBy, CheckStatus.WBS_RECEIPTS_PENDING.code(),
                    MilepostDesignPlanDetailModelStatus.CONFIRMING.code());
        } else if (ListUtils.isNotEmpty(sonDtos)) {
            // 有子级筛选出正在进行其他单据，留下所有可以被变更详设行状态的详设
            List<Long> needChangeStatusIds =
                    sonDtos.stream().filter(sonDto -> !Boolean.TRUE.equals(sonDto.getWhetherModel()) && !(CheckStatus.WBS_RECEIPTS_DRAFT.code().equals(sonDto.getStatus())
                                    || CheckStatus.WBS_RECEIPTS_PENDING.code().equals(sonDto.getStatus())
                                    || CheckStatus.WBS_RECEIPTS_CHECKING.code().equals(sonDto.getStatus())))
                            .map(MilepostDesignPlanDetailChangeDto::getDesignPlanDetailId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
            needChangeStatusIds.add(checkDto.getDesignPlanDetailId());
            if (ListUtils.isNotEmpty(hasConfirmedDtos)) {
                // 部分确认
                dealWithPartConfirm(hasConfirmedDtos, dto.getId(), userBy, null, checkDto.getMaterialCategory(),
                        alreadyCreatePublishReceiptsIdAndWbsSummaryCode, dto);
                // 需求发布后的详设行的状态变更
                milepostDesignPlanService.updateDetailForChangeByDetailIds(needChangeStatusIds, userBy, CheckStatus.WBS_RECEIPTS_PENDING.code(),
                        null);
            } else {
                // 当前详设（wbs最低层+装配件+虚拟件）+需求发布前当前详设:（wbs最低层+装配件+虚拟件）的所有下级中只要有一个详设,即是外购物料并且已确认,就要走需求发布后的流程
                List<Long> designPlanDetailIdList = new ArrayList<>();
                if ((Boolean.TRUE.equals(checkDto.getWbsLastLayer()) || Objects.equals(checkDto.getMaterialCategory(), "装配件")
                        || Objects.equals(checkDto.getMaterialCategory(), "虚拟件"))
                        && (ChangeTypeEnum.UPDATE.getCode().equals(checkDto.getChangeType()) || ChangeTypeEnum.DELETE.getCode().equals(checkDto.getChangeType()))) {
                    List<MilepostDesignPlanDetailChangeDto> dtoList = new ArrayList<>();
                    for (MilepostDesignPlanDetailChangeDto sonDto : sonDtos) {
                        Long designPlanDetailId = sonDto.getDesignPlanDetailId();
                        if (null != designPlanDetailId && Boolean.TRUE.equals(sonDto.getAfterRequirementPublish())
                                && (Objects.equals(sonDto.getMaterialCategory(), "看板物料") || Objects.equals(sonDto.getMaterialCategory(), "外购物料"))) {
                            // 当前详设行id查到对应需求发布单据
                            List<ProjectWbsReceiptsDto> receiptsDtoList =
                                    projectWbsReceiptsExtMapper.selectProcessReceiptsByDetailIds(Collections.singletonList(designPlanDetailId))
                                            .stream().filter(a -> Objects.equals(a.getProjectId(), dto.getProjectId()))
                                            .sorted(Comparator.comparing(ProjectWbsReceiptsDto::getCreateAt).reversed()).collect(Collectors.toList());
                            if (ListUtils.isNotEmpty(receiptsDtoList)) {
                                dtoList.add(sonDto);
                                designPlanDetailIdList.add(designPlanDetailId);
                            }
                        }
                    }
                    if (ListUtils.isNotEmpty(dtoList)) {
                        dealWithPartConfirm(dtoList, dto.getId(), userBy, null, checkDto.getMaterialCategory(),
                                alreadyCreatePublishReceiptsIdAndWbsSummaryCode, dto);
                    }
                }

                // 需求发布前的详设行的状态变更
                needChangeStatusIds = needChangeStatusIds.stream().filter(e -> !designPlanDetailIdList.contains(e)).collect(Collectors.toList());
                milepostDesignPlanService.updateDetailForChangeByDetailIds(needChangeStatusIds, userBy, CheckStatus.WBS_RECEIPTS_DRAFT.code(), null);
            }
        }
    }

    private void dealFirstLevelChangeDto(List<MilepostDesignPlanDetailChangeDto> sonDtoList, ProjectWbsReceiptsDto dto, Long userBy,
                                         List<String> alreadyCreatePublishReceiptsIdAndWbsSummaryCode) {
        if (ListUtils.isEmpty(sonDtoList)) {
            return;
        }

        for (MilepostDesignPlanDetailChangeDto sonDto : sonDtoList) {
            if (Objects.equals(sonDto.getMaterialCategory(), "看板物料") || Objects.equals(sonDto.getMaterialCategory(), "外购物料")) {
                // 当前详设行id查到对应需求发布单据
                List<ProjectWbsReceiptsDto> receiptsDtos =
                        projectWbsReceiptsExtMapper.selectProcessReceiptsByDetailIds(Collections.singletonList(sonDto.getDesignPlanDetailId()));
                if (ListUtils.isNotEmpty(receiptsDtos) && !Boolean.TRUE.equals(sonDto.getWhetherModel())) {
                    // 筛选出已生效的单据
                    List<ProjectWbsReceiptsDto> hasConfirmedReceiptsDtos = receiptsDtos.stream()
                            .filter(a -> Objects.equals(a.getProjectId(), dto.getProjectId()))
                            .sorted(Comparator.comparing(ProjectWbsReceiptsDto::getCreateAt).reversed()).collect(Collectors.toList());
                    if (ListUtils.isEmpty(hasConfirmedReceiptsDtos)) {
                        continue;
                    }

                    // 一个详设行只能被确认一次，一个详设行对应一个生效的需求发布单据
                    ProjectWbsReceiptsDto hasConfirmedReceiptsDto = hasConfirmedReceiptsDtos.get(0);
                    projectWbsReceiptsBudgetChangeHistoryService.createReceiptsBudgetChangeHistory(dto.getId(), hasConfirmedReceiptsDto.getId(),
                            userBy, alreadyCreatePublishReceiptsIdAndWbsSummaryCode, sonDto.getWbsSummaryCode(),
                            new HashSet<>(Collections.singleton(sonDto.getExtIs())));

                    // 进度确认需求发布后的详设行的状态变更
                    milepostDesignPlanService.updateDetailForChangeByDetailIds(Collections.singletonList(sonDto.getDesignPlanDetailId()), userBy,
                            CheckStatus.WBS_RECEIPTS_PENDING.code(), MilepostDesignPlanDetailModelStatus.CONFIRMING.code());
                }
            } else if (Objects.equals(sonDto.getMaterialCategory(), "装配件") || Objects.equals(sonDto.getMaterialCategory(), "虚拟件")) {
                dealFirstLevelChangeDto(sonDto.getSonDtos(), dto, userBy, alreadyCreatePublishReceiptsIdAndWbsSummaryCode);
            }
        }
    }

    private void dealWithPartConfirm(List<MilepostDesignPlanDetailChangeDto> hasConfirmedDtos,
                                     Long projectWbsChangeReceiptsId, Long userBy,
                                     Long parentProjectWbsPublishReceiptsId, String parentMaterialCategory,
                                     List<String> alreadyCreatePublishReceiptsIdAndWbsSummaryCode, ProjectWbsReceiptsDto dto) {
        List<Long> hasConfirmedIdList = new ArrayList<>();
        Map<Long, String> designPlanDetailIdAndWbsSummaryCodeMap = new HashMap<>();
        Set<Boolean> extIsSet = new HashSet<>();
        for (MilepostDesignPlanDetailChangeDto changeDto : hasConfirmedDtos) {
            if (Boolean.TRUE.equals(changeDto.getAfterRequirementPublish())
                    && (Objects.equals(changeDto.getMaterialCategory(), "看板物料") || Objects.equals(changeDto.getMaterialCategory(), "外购物料"))) {
                hasConfirmedIdList.add(changeDto.getDesignPlanDetailId());
                designPlanDetailIdAndWbsSummaryCodeMap.put(changeDto.getDesignPlanDetailId(), changeDto.getWbsSummaryCode());
                extIsSet.add(changeDto.getExtIs());
            }
        }
        if (CollectionUtils.isEmpty(hasConfirmedIdList)) {
            return;
        }

        //通过已被确认的详设行id查到对应需求发布单据
        List<ProjectWbsReceiptsDto> projectWbsReceiptsDtoList = projectWbsReceiptsExtMapper.selectProcessReceiptsByDetailIds(hasConfirmedIdList);
        List<ProjectWbsReceiptsDto> hasConfirmedReceiptsDtos = projectWbsReceiptsDtoList.stream()
                .filter(a -> Objects.equals(a.getProjectId(), dto.getProjectId()))
                .sorted(Comparator.comparing(ProjectWbsReceiptsDto::getCreateAt).reversed()).collect(Collectors.toList());
        //如果有进度确认的父级也发生了变化 那就移除掉
        if (Objects.nonNull(parentProjectWbsPublishReceiptsId)
                && (Objects.equals(parentMaterialCategory, "看板物料") || Objects.equals(parentMaterialCategory, "外购物料"))) {
            hasConfirmedReceiptsDtos = hasConfirmedReceiptsDtos.stream().filter(a -> !Objects.equals(a.getId(), parentProjectWbsPublishReceiptsId))
                    .collect(Collectors.toList());
        }
        logger.info("dealWithPartConfirm的hasConfirmedReceiptsDtos：{}", JSON.toJSONString(hasConfirmedReceiptsDtos));

        if (ListUtils.isNotEmpty(hasConfirmedReceiptsDtos)) {
            // 剩下的都是不在这个当前详设的进度确认的需求发布单，然后对所有单生成对应的预算变更
            Map<Long, List<ProjectWbsReceiptsDto>> hasConfirmedReceiptsMaps =
                    hasConfirmedReceiptsDtos.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getId));
            //通过单据查到对应预算 再把预算数据插入单据预算变更表中
            for (Long key : hasConfirmedReceiptsMaps.keySet()) {
                List<ProjectWbsReceiptsDto> projectWbsReceiptsDtos = hasConfirmedReceiptsMaps.get(key);
                Set<String> wbsSummaryCodeSet = new HashSet<>();
                for (ProjectWbsReceiptsDto projectWbsReceiptsDto : projectWbsReceiptsDtos) {
                    String wbsSummaryCode = designPlanDetailIdAndWbsSummaryCodeMap.get(projectWbsReceiptsDto.getDesignPlanDetailId());
                    if (StringUtils.isNotEmpty(wbsSummaryCode)) {
                        wbsSummaryCodeSet.add(wbsSummaryCode);
                    }
                }
                for (String wbsSummaryCode : wbsSummaryCodeSet) {
                    projectWbsReceiptsBudgetChangeHistoryService.createReceiptsBudgetChangeHistory(projectWbsChangeReceiptsId, key, userBy,
                            alreadyCreatePublishReceiptsIdAndWbsSummaryCode, wbsSummaryCode, extIsSet);
                }
            }

            milepostDesignPlanService.updateDetailForChangeByDetailIds(hasConfirmedIdList, userBy, CheckStatus.WBS_RECEIPTS_PENDING.code(),
                    MilepostDesignPlanDetailModelStatus.CONFIRMING.code());
        }
    }

    private void filterCheckDetailDtos(List<MilepostDesignPlanDetailChangeDto> sonDtos,
                                       List<MilepostDesignPlanDetailChangeDto> detailChangeDtos, List<Long> checkIds) {
        sonDtos.forEach(dto -> {
            if (checkIds.contains(dto.getDesignPlanDetailId())
                    && !detailChangeDtos.stream().map(MilepostDesignPlanDetailChangeDto::getDesignPlanDetailId).collect(Collectors.toList()).contains(dto.getDesignPlanDetailId())) {
                detailChangeDtos.add(dto);
            }
            if (ListUtils.isNotEmpty(dto.getSonDtos())) {
                filterCheckDetailDtos(dto.getSonDtos(), detailChangeDtos, checkIds);
            }
        });
    }

    private void saveCheck(ProjectWbsReceiptsDto dto) {
        // 校验能否新建需求发布单据
        List<Long> checkDetails =
                dto.getDesignRelList().stream().map(ProjectWbsReceiptsDesignPlanRel::getDesignPlanDetailId).collect(Collectors.toList());
        List<Long> checkReceipts =
                dto.getDesignRelList().stream().map(ProjectWbsReceiptsDesignPlanRel::getProjectWbsReceiptsId).collect(Collectors.toList());
        List<MilepostDesignPlanDetailSubmitHistoryDto> historyDtos =
                milepostDesignPlanDetailSubmitHistoryService.selectByDetailIds(checkDetails);
        //查询相关单据
        List<Long> submitReceipts =
                historyDtos.stream().map(MilepostDesignPlanDetailSubmitHistory::getProjectWbsReceiptsId).collect(Collectors.toList());
        checkReceipts.addAll(submitReceipts);
        List<ProjectWbsReceiptsDto> receiptsDtos =
                selectApprovingReceiptsByIds(checkReceipts);

        if (Objects.nonNull(dto.getId())) {
            receiptsDtos = receiptsDtos.stream().filter(receiptsDto -> !Objects.equals(receiptsDto.getId(),
                    dto.getId())).collect(Collectors.toList());
        }
        if (ListUtils.isNotEmpty(receiptsDtos)) {
            throw new MipException("详细设计行还有正在详设发布审批中的单据,不允许需求发布");
        }
    }

    /**
     * 提交 新建界面第三步
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectWbsReceiptsDto submitReceipts(ProjectWbsReceiptsDto dto) {
        Guard.notNull(dto, "需求发布单据不能为空");
        logger.info("submitReceipts提交需求发布单据，dto：{}", JsonUtils.toString(dto));
        if (Objects.nonNull(dto.getId())) {
            ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMapper.selectByPrimaryKey(dto.getId());
            Guard.notNull(projectWbsReceipts, String.format("需求发布单据id：%s不存在", dto.getId()));
            if (Objects.equals(projectWbsReceipts.getRequirementStatus(), RequirementStatusEnum.PROCESS.getCode())) {
                throw new ApplicationBizException("单据状态已更新，请刷新页面");
            }

            //非待审核状态的单据不能提交
            if (!Objects.equals(projectWbsReceipts.getRequirementStatus(), RequirementStatusEnum.TODO.getCode()) &&
                    !Objects.equals(projectWbsReceipts.getRequirementStatus(), RequirementStatusEnum.REFUSE.getCode())) {
                throw new BizException(Code.ERROR, "单据不为待处理、驳回状态，不能填写预算，请刷新页面");
            }
        }


        if (!Objects.equals(dto.getHandleBy(), SystemContext.getUserId())) {
            throw new ApplicationBizException("当前用户不是单据指定的处理人，不能操作提交审批");
        }

        List<ProjectWbsReceiptsDesignPlanRelDto> designRelList = dto.getDesignRelList();
        if (ListUtils.isNotEmpty(designRelList)) {
            List<Long> designPlanDetailIdList =
                    designRelList.stream().map(ProjectWbsReceiptsDesignPlanRelDto::getDesignPlanDetailId).collect(Collectors.toList());
            List<ProjectWbsReceiptsDto> receiptsDtoList = selectByDetailIds(designPlanDetailIdList);
            Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = receiptsDtoList.stream().filter(e -> !Objects.equals(e.getId(), dto.getId())
                    && Objects.nonNull(e.getDesignPlanDetailId())).collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId));

            MilepostDesignPlanDetailExample planDetailExample = new MilepostDesignPlanDetailExample();
            planDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(designPlanDetailIdList);
            List<MilepostDesignPlanDetail> detailList = milepostDesignPlanDetailMapper.selectByExample(planDetailExample);
            // 不校验工具层的P码
            detailList = detailList.stream().filter(e -> !Boolean.TRUE.equals(e.getWbsLastLayer())).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(detailList)) {
                List<String> pamCodeList = new ArrayList<>();
                for (MilepostDesignPlanDetail designPlanDetail : detailList) {
                    List<ProjectWbsReceiptsDto> projectWbsReceiptsDtoList = receiptsDtoMap.get(designPlanDetail.getId());
                    Integer moduleStatus = designPlanDetail.getModuleStatus();
                    Guard.isFalse((Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMED.code(), moduleStatus)
                                    || Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMING.code(), moduleStatus))
                                    && ListUtils.isNotEmpty(projectWbsReceiptsDtoList),
                            String.format("P码：%s，WBS：%s的详设已经确认中或者已确认", designPlanDetail.getPamCode(), designPlanDetail.getWbsSummaryCode()));

                    pamCodeList.add(designPlanDetail.getPamCode());
                }
                Long organizationId = projectService.getOrganizationIdByProjectId(dto.getProjectId());
                List<Material> materialList = materialExtService.invokeMaterialApiGetByPamCodeList(pamCodeList, organizationId);
                List<String> notExistPamCodeList =
                        pamCodeList.stream().filter(pamCode -> materialList.stream().noneMatch(e -> Objects.equals(e.getPamCode(), pamCode)))
                                .collect(Collectors.toList());
                if (ListUtils.isNotEmpty(notExistPamCodeList)) {
                    throw new ApplicationBizException(String.format("P码：[%s]的物料已被删除", notExistPamCodeList.toString()));
                }
            }
        }

        ProjectWbsReceiptsDesignPlanRelExample example = new ProjectWbsReceiptsDesignPlanRelExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andProjectWbsReceiptsIdEqualTo(dto.getId());
        List<ProjectWbsReceiptsDesignPlanRel> relList = projectWbsReceiptsDesignPlanRelMapper.selectByExample(example);
        List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
        for (ProjectWbsReceiptsDesignPlanRel rel : relList) {
            MilepostDesignPlanDetail planDetail = new MilepostDesignPlanDetail();
            planDetail.setId(rel.getDesignPlanDetailId());
            planDetail.setStatus(CheckStatus.WBS_RECEIPTS_DRAFT.code());
            updateDesignPlanDetailList.add(planDetail);
        }
        if (ListUtils.isNotEmpty(updateDesignPlanDetailList)) {
            milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
        }

        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
        ctcAttachmentDto.setModuleId(dto.getId());

        ctcAttachmentService.deleteByModule(ctcAttachmentDto);
        // 保存附件信息
        if (ListUtil.isPresent(dto.getAttachmentList())) {
            for (CtcAttachmentDto attachmentDto : dto.getAttachmentList()) {
                if (attachmentDto.getId() == null) {
                    attachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
                    attachmentDto.setModuleId(dto.getId());
                }
                ctcAttachmentService.save(attachmentDto, SystemContext.getUserId());
            }
        }

        List<ProjectWbsReceiptsBudgetDto> budgetList = dto.getBudgetList();

        BigDecimal demandTotalAmount = BigDecimal.ZERO;
        BigDecimal outsourceTotalAmount = BigDecimal.ZERO;
        for (ProjectWbsReceiptsBudgetDto budgetDto : budgetList) {
            List<ProjectWbsReceiptsBudgetDto> childs = budgetDto.getChilds();
            if (ListUtils.isEmpty(childs)) {
                continue;
            }

            for (ProjectWbsReceiptsBudgetDto child : childs) {
                Integer demandType = child.getDemandType();
                if (Objects.equals(demandType, ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode())) {
                    demandTotalAmount = demandTotalAmount.add(child.getBudgetOccupiedAmount());
                } else if (Objects.equals(demandType, ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode())) {
                    outsourceTotalAmount = outsourceTotalAmount.add(child.getBudgetOccupiedAmount());
                }
            }
        }

        // 保存预算信息
        projectWbsReceiptsBudgetService.saveRequirementBudget(budgetList);

        //单据从"待处理"状态变为"审核中"状态
        ProjectWbsReceipts receipts = BeanConverter.copy(dto, ProjectWbsReceipts.class);
        receipts.setRequirementStatus(RequirementStatusEnum.DRAFT.getCode());
        receipts.setHandleAt(new Date());
        projectWbsReceiptsMapper.updateByPrimaryKeySelective(receipts);

        ProjectWbsReceiptsDto resultDto = new ProjectWbsReceiptsDto();
        resultDto.setId(dto.getId());
        resultDto.setRequirementCode(dto.getRequirementCode());
        resultDto.setDemandTotalAmount(demandTotalAmount);
        resultDto.setOutsourceTotalAmount(outsourceTotalAmount);
        return resultDto;
    }

    /**
     * 退回 新建界面第三步
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer reject(Long id) {
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);
        if (!Objects.equals(receipts.getHandleBy(), SystemContext.getUserId()) && !Objects.equals(receipts.getProducerId(),
                SystemContext.getUserId())) {
            throw new MipException("当前用户不是单据指定的处理人或制单人，不能操作退回");
        }
        Integer requirementStatus = receipts.getRequirementStatus();
        if (!Objects.equals(requirementStatus, RequirementStatusEnum.TODO.getCode()) && !Objects.equals(requirementStatus,
                RequirementStatusEnum.REFUSE.getCode())) {
            throw new MipException("单据不为待处理或驳回状态，不能退回");
        }

        if (Objects.equals(receipts.getRequirementType(), RequirementTypeEnum.DESIGN_PLAN_SUBMIT.getCode())) {
            MilepostDesignPlanDetailSubmitHistoryExample example = new MilepostDesignPlanDetailSubmitHistoryExample();
            example.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andProjectWbsReceiptsIdEqualTo(id);
            List<MilepostDesignPlanDetailSubmitHistory> submitHistoryList = milepostDesignPlanDetailSubmitHistoryService.selectByExample(example);

            List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
            for (MilepostDesignPlanDetailSubmitHistory history : submitHistoryList) {
                if (null != history.getDesignPlanDetailId() && !Boolean.FALSE.equals(history.getWbsLastLayer())) {
                    MilepostDesignPlanDetail planDetail = new MilepostDesignPlanDetail();
                    planDetail.setId(history.getDesignPlanDetailId());
                    planDetail.setStatus(CheckStatus.WBS_RECEIPTS_DRAFT.code());
                    updateDesignPlanDetailList.add(planDetail);
                }
            }
            if (ListUtils.isNotEmpty(updateDesignPlanDetailList)) {
                milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
            }
        } else {
            ProjectWbsReceiptsDesignPlanRelExample relExample = new ProjectWbsReceiptsDesignPlanRelExample();
            relExample.createCriteria().andDeletedFlagEqualTo(false).andProjectWbsReceiptsIdEqualTo(id);
            List<ProjectWbsReceiptsDesignPlanRel> relList = projectWbsReceiptsDesignPlanRelMapper.selectByExample(relExample);

            List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
            for (ProjectWbsReceiptsDesignPlanRel rel : relList) {
                MilepostDesignPlanDetail planDetail = new MilepostDesignPlanDetail();
                planDetail.setId(rel.getDesignPlanDetailId());
                planDetail.setStatus(CheckStatus.WBS_RECEIPTS_DRAFT.code());
                updateDesignPlanDetailList.add(planDetail);
            }
            if (ListUtils.isNotEmpty(updateDesignPlanDetailList)) {
                milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
            }
        }

        //逻辑删除单据保存时创建的预算
        //projectWbsReceiptsExtMapper.updateWbsRequirementBudgetDel(id);

        //单据从"待处理"状态变为"草稿"状态
        receipts.setRequirementStatus(RequirementStatusEnum.DRAFT.getCode());
        //退回邮件提醒
        sendEmailForProjectWbsReceiptReject(receipts, receipts.getProducerId(), NoticeBusinessType.PROJECT_WBS_RECEIPTS_DRAFT);
        return projectWbsReceiptsMapper.updateByPrimaryKeySelective(receipts);
    }

    private void sendEmailForProjectWbsReceiptReject(ProjectWbsReceipts projectWbsReceipts, Long receiverId, NoticeBusinessType noticeBusinessType) {
        if (null == projectWbsReceipts) {
            return;
        }

        try {
            String subject = String.format("PAM详细设计单据：%s已撤回，请处理", projectWbsReceipts.getRequirementCode());
            StringBuilder content = new StringBuilder("<html><div style='font-size: 12px;'>");
            content.append("<div>");
            content.append(String.format("PAM详细设计单据：%s已撤回，请重新提交或作废单据，", projectWbsReceipts.getRequirementCode()));
            String href;
            // 详设变更的退回
            if (Objects.equals(projectWbsReceipts.getRequirementType(), RequirementTypeEnum.DESIGN_PLAN_CHANGE.getCode())) {
                href = String.format("<a href=\"%s\" target='_blank'>去处理</a>",
                        milepostDesignPlanChangeUrl + projectWbsReceipts.getId()
                                + "?id=" + projectWbsReceipts.getId() + "&requirementStatus=" + projectWbsReceipts.getRequirementStatus());
            } else {
                href = String.format("<a href=\"%s\" target='_blank'>去处理</a>", projectWbsReceiptsUrl + projectWbsReceipts.getId()
                        + "?id=" + projectWbsReceipts.getId() + "&projectName=" + projectWbsReceipts.getProjectName()
                        + "&projectCode=" + projectWbsReceipts.getProjectCode() + "&projectId=" + projectWbsReceipts.getProjectId()
                        + "&requirementStatus=" + projectWbsReceipts.getRequirementStatus() + "&type=" + projectWbsReceipts.getConfirmMode()
                        + "&requirementType=" + projectWbsReceipts.getRequirementType());
            }
            content.append(href);
            content.append("</div>");
            content.append("</div></html>");

            sendEmail(receiverId, null, noticeBusinessType.getType(), subject, content.toString());
        } catch (Exception e) {
            logger.error("{}邮件发送失败", noticeBusinessType.getName(), e);
        }
    }

    /**
     * 发送邮件
     *
     * @param receiveMipAccount ：接收人Mip账号
     * @param receiverId        ：接收人id，无receiveMipAccount，则通过receiverId查询receiverMipAccount
     * @param subject           : 邮件主题
     * @param content           ：邮件内容
     */
    private void sendEmail(Long receiverId, String receiveMipAccount, int businessType, String subject, String content) {
        Email email = new Email();
        email.setFromAddress("pam");
        email.setStatus(0);
        email.setLanguage("zh-CN");
        email.setBusinessType(businessType);
        if (StringUtils.isEmpty(receiveMipAccount)) {
            UserInfo user = CacheDataUtils.findUserById(receiverId);
            receiveMipAccount = Optional.ofNullable(user).map(UserInfo::getUsername).orElse(SystemContext.getUserMip());
        }
        email.setReceiver(receiveMipAccount);
        email.setSubject(subject);
        email.setContent(content);
        email.setDeletedFlag(Boolean.FALSE);
        emailExtMapper.insert(email);
    }

    /**
     * 作废 新建界面第二步(草稿状态)
     *
     * @param id
     * @return 详细设计单据id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cancel(Long id) {
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(id);
        Guard.notNull(receipts, "详细设计单据不存在");
        Long managerId = getManagerIdFromProjectId(receipts.getProjectId());
        if (!Objects.equals(receipts.getProducerId(), SystemContext.getUserId()) && !Objects.equals(managerId, SystemContext.getUserId())) {
            throw new MipException("当前用户不是单据的制单人或该项目的项目经理，不能操作作废");
        }
        if (!Objects.equals(receipts.getRequirementStatus(), RequirementStatusEnum.DRAFT.getCode())) {
            throw new MipException("单据不为草稿状态，不能作废");
        }

        //取消详细设计单据与详细设计方案行的关联关系
//        projectWbsReceiptsExtMapper.updateReceiptsDel(id);

        List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
        //需求发布单，关联详设状态变为"0：未确认"
        if (Objects.equals(receipts.getRequirementType(), RequirementTypeEnum.REQUIREMENT_PUBLISH.getCode())) {
            List<MilepostDesignPlanDetail> designPlanDetails = this.getDesignPlanDetail(receipts);
            if (ListUtils.isNotEmpty(designPlanDetails)) {
                List<Long> designPlanDetailIdList = designPlanDetails.stream().map(MilepostDesignPlanDetail::getId).collect(Collectors.toList());
                List<ProjectWbsReceiptsDto> receiptsDtoList = selectByDetailIds(designPlanDetailIdList);
                Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = receiptsDtoList.stream()
                        .filter(e -> !Objects.equals(e.getId(), receipts.getId()) && Objects.nonNull(e.getDesignPlanDetailId()))
                        .collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId));

                for (MilepostDesignPlanDetail designPlanDetail : designPlanDetails) {
                    Integer moduleStatus = designPlanDetail.getModuleStatus();
                    List<ProjectWbsReceiptsDto> projectWbsReceiptsDtoList = receiptsDtoMap.get(designPlanDetail.getId());
                    if ((Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMED.code(), moduleStatus)
                            || Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMING.code(), moduleStatus))
                            && ListUtils.isNotEmpty(projectWbsReceiptsDtoList)) {
                        continue;
                    }

                    MilepostDesignPlanDetail planDetail = new MilepostDesignPlanDetail();
                    planDetail.setId(designPlanDetail.getId());
                    planDetail.setStatus(CheckStatus.WBS_RECEIPTS_CANCEL.code());
                    planDetail.setModuleStatus(MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code());
                    updateDesignPlanDetailList.add(planDetail);
                }
            }
        } else {
            MilepostDesignPlanDetailSubmitHistoryExample example = new MilepostDesignPlanDetailSubmitHistoryExample();
            example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectWbsReceiptsIdEqualTo(id);
            List<MilepostDesignPlanDetailSubmitHistory> submitHistoryList = milepostDesignPlanDetailSubmitHistoryService.selectByExample(example);
            List<Long> designPlanDetailIdList = submitHistoryList.stream().map(MilepostDesignPlanDetailSubmitHistory::getDesignPlanDetailId)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            MilepostDesignPlanDetailExample planDetailExample = new MilepostDesignPlanDetailExample();
            planDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(designPlanDetailIdList);
            List<MilepostDesignPlanDetail> detailList = milepostDesignPlanDetailMapper.selectByExample(planDetailExample);

            List<ProjectWbsReceiptsDto> receiptsDtoList = selectByDetailIds(designPlanDetailIdList);
            Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = receiptsDtoList.stream().filter(e -> !Objects.equals(e.getId(), receipts.getId())
                    && Objects.nonNull(e.getDesignPlanDetailId())).collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId));

            for (MilepostDesignPlanDetail designPlanDetail : detailList) {
                Integer moduleStatus = designPlanDetail.getModuleStatus();
                List<ProjectWbsReceiptsDto> projectWbsReceiptsDtoList = receiptsDtoMap.get(designPlanDetail.getId());
                if ((Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMED.code(), moduleStatus)
                        || Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMING.code(), moduleStatus))
                        && ListUtils.isNotEmpty(projectWbsReceiptsDtoList)) {
                    continue;
                }

                if (!Boolean.FALSE.equals(designPlanDetail.getWbsLastLayer())) {
                    MilepostDesignPlanDetail planDetail = new MilepostDesignPlanDetail();
                    planDetail.setId(designPlanDetail.getId());
                    planDetail.setStatus(CheckStatus.WBS_RECEIPTS_CANCEL.code());
                    updateDesignPlanDetailList.add(planDetail);
                }
            }
        }

        if (ListUtils.isNotEmpty(updateDesignPlanDetailList)) {
            milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
        }

        //逻辑删除单据上传的附件
        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
        ctcAttachmentDto.setModuleId(receipts.getId());
        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
        ctcAttachmentService.deleteByModule(ctcAttachmentDto);

        //单据从"草稿"状态变为"作废"状态
        receipts.setRequirementStatus(RequirementStatusEnum.CANCEL.getCode());
        projectWbsReceiptsMapper.updateByPrimaryKeySelective(receipts);
        return 1;
    }

    @Override
    public Map<String, Object> detailExport(Long receiptsId) {
        //基本信息
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(receiptsId);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);
        ProjectWbsReceiptsDetailExcelVo receiptsDetail = BeanConverter.copy(receipts,
                ProjectWbsReceiptsDetailExcelVo.class);


        //明细内容
        List<MilepostDesignPlanDetailDto> designPlanDetailList = new ArrayList<>();
        if (Objects.equals(receipts.getConfirmMode(), ReceiptsConfirmModeEnum.PURCHASE.getCode())) {
            designPlanDetailList = setDetailByPurchase(BeanConverter.copy(receipts, ProjectWbsReceiptsDto.class));
        } else if (Objects.equals(receipts.getConfirmMode(), ReceiptsConfirmModeEnum.CONFIRM.getCode())) {
            designPlanDetailList = setDetailByConfirm(BeanConverter.copy(receipts, ProjectWbsReceiptsDto.class));
        } else if (receipts.getRequirementType().equals(RequirementTypeEnum.DESIGN_PLAN_SUBMIT.getCode())) {
            designPlanDetailList = buildTree(BeanConverter.copy(this.getDesignPlanDetailForSubmit(receipts),
                    MilepostDesignPlanDetailDto.class));
        } else if (receipts.getRequirementType().equals(RequirementTypeEnum.DESIGN_PLAN_CHANGE.getCode())) {
            designPlanDetailList = buildTree(BeanConverter.copy(this.getDesignPlanDetailForChange(receipts),
                    MilepostDesignPlanDetailDto.class));
        }
        List<MilepostDesignPlanDetailDto> resultDetails = new ArrayList<>();
        buildDesignPlanDetailTree(resultDetails, designPlanDetailList, "");

        Map<String, Object> resultMap = new HashMap<>();

        if (receipts.getRequirementType().equals(RequirementTypeEnum.REQUIREMENT_PUBLISH.getCode())) {
            //预算信息-汇总
            List<ProjectWbsReceiptsBudgetDto> budgetSummaryDtos =
                    projectWbsReceiptsBudgetService.findRequirementBudget(receipts, receipts.getProjectId());
            //预算信息-明细
            List<ProjectWbsReceiptsBudgetDto> budgetDetailList = new ArrayList<>();
            budgetSummaryDtos.forEach(budget -> {
                budgetDetailList.addAll(budget.getChilds());
                budget.setChilds(null);
            });
            resultMap.put("budgetSummaryList", budgetSummaryDtos);
            resultMap.put("budgetDetailList", budgetDetailList);
        } else if (receipts.getRequirementType().equals(RequirementTypeEnum.DESIGN_PLAN_CHANGE.getCode())) {
            ProjectWbsReceiptsDto receiptsDto = BeanConverter.copy(receipts, ProjectWbsReceiptsDto.class);
            //预算信息-汇总
            List<ProjectWbsReceiptsBudgetChangeHistoryDto> budgetSummaryChangeHistoryDtos =
                    projectWbsReceiptsBudgetChangeHistoryService.findReceiptsBudgetHistories(receiptsDto, receipts.getProjectId());
            List<ProjectWbsReceiptsBudgetChangeHistoryDto> budgetDetailList = new ArrayList<>();
            List<ProjectWbsReceiptsBudgetChangeHistoryDto> exportBudgetSummaryChangeHistoryDtos = new ArrayList<>();
            //预算信息-明细
            if (ListUtils.isNotEmpty(budgetSummaryChangeHistoryDtos)) {

                Map<String, List<ProjectWbsReceiptsBudgetChangeHistoryDto>> groupByWbsMap =
                        budgetSummaryChangeHistoryDtos.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsBudgetChangeHistoryDto::getWbsSummaryCode));
                groupByWbsMap.keySet().forEach(key -> {
                    List<ProjectWbsReceiptsBudgetChangeHistoryDto> projectWbsReceiptsBudgetChangeHistoryDtos = groupByWbsMap.get(key);
                    ProjectWbsReceiptsBudgetChangeHistoryDto projectWbsReceiptsBudgetChangeHistoryDto =
                            commonSet(projectWbsReceiptsBudgetChangeHistoryDtos, key, receipts.getProjectId());
                    exportBudgetSummaryChangeHistoryDtos.add(projectWbsReceiptsBudgetChangeHistoryDto);
                });
                budgetSummaryChangeHistoryDtos.forEach(budget -> {
                    budgetDetailList.addAll(budget.getChilds());
                    budget.setChilds(null);
                });
            }
            resultMap.put("budgetSummaryList", exportBudgetSummaryChangeHistoryDtos);
            resultMap.put("budgetDetailList", budgetDetailList);
        }

        resultMap.put("receiptsDetailList", Arrays.asList(receiptsDetail));
        resultMap.put("designPlanDetailList", resultDetails);
        return resultMap;
    }

    /**
     * 组合树
     */
    private void buildDesignPlanDetailTree(List<MilepostDesignPlanDetailDto> resultDetails,
                                           List<MilepostDesignPlanDetailDto> details, String parentIndex) {
        if (ListUtil.isPresent(details)) {
            int sonIndex = 1;
            for (MilepostDesignPlanDetailDto detail : details) {
                String serialNumber = this.buildSerialNumber(parentIndex, sonIndex);
                detail.setSerialNumber(serialNumber);
                resultDetails.add(detail);
                List<MilepostDesignPlanDetailDto> sonDetails = detail.getSonDtos();
                if (ListUtil.isPresent(sonDetails)) {
                    this.buildDesignPlanDetailTree(resultDetails, sonDetails, serialNumber);
                }
                ++sonIndex;
            }
        }
    }

    /**
     * 构建序号
     */
    private String buildSerialNumber(String parentIndex, int sonIndex) {
        if (StringUtils.isNotEmpty(parentIndex)) {
            return parentIndex + "." + sonIndex;
        }
        return sonIndex + "";
    }

    /**
     * 根据详细设计单据，获取详细设计
     *
     * @param receipts
     * @return
     */
    @Override
    public List<MilepostDesignPlanDetail> getDesignPlanDetail(ProjectWbsReceipts receipts) {
        ProjectWbsReceiptsDesignPlanRelExample example = new ProjectWbsReceiptsDesignPlanRelExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectWbsReceiptsIdEqualTo(receipts.getId());
        List<ProjectWbsReceiptsDesignPlanRel> relations = ProjectWbsReceiptsDesignPlanRelMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(relations)) {
            return new ArrayList<>();
        }
        MilepostDesignPlanDetailExample planDetailExample = new MilepostDesignPlanDetailExample();
        planDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(
                relations.stream().map(ProjectWbsReceiptsDesignPlanRel::getDesignPlanDetailId).collect(Collectors.toList()));
        return milepostDesignPlanDetailMapper.selectByExample(planDetailExample);
    }

    /**
     * 保存详细设计关联（部分确认）
     *
     * @param dto
     * @param userId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void savePurchaseRecordWithRecursiveDetail(ProjectWbsReceiptsDto dto, Long userId) {
        List<ProjectWbsReceiptsDesignPlanRelDto> relationDtos = BeanConverter.copy(dto.getDesignRelList(), ProjectWbsReceiptsDesignPlanRelDto.class);

        // 查询当前使用单位的物料编码规则
        String materialCodeRule = materialExtService.getMaterialCodeRule(SystemContext.getUnitId());
        ErpCodeRuleExample erpCodeRuleExample = new ErpCodeRuleExample();
        ErpCodeRuleExample.Criteria criteria = erpCodeRuleExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE).andRuleNameEqualTo(materialCodeRule);
        List<ErpCodeRule> erpCodeRuleList = erpCodeRuleMapper.selectByExample(erpCodeRuleExample);
        Map<String, List<ErpCodeRule>> erpCodeRuleMap = new HashMap<>();

        if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
            erpCodeRuleMap.putAll(erpCodeRuleList.stream().collect(Collectors.groupingBy(e ->
                    getFlexiblePurchaseRecordMaterialERPCodeRuleKeyKUNSHANKey(e.getCodingMiddleclass(), e.getCodingSubclass()))));
        } else if (MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
            erpCodeRuleMap.putAll(erpCodeRuleList.stream().collect(Collectors.groupingBy(e ->
                    getFlexiblePurchaseRecordMaterialERPCodeRuleKeyKUKA2022Key(e.getCodingClass(), e.getCodingMiddleclass(), e.getCodingSubclass()))));
        }

        List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
        List<ProjectWbsReceiptsDesignPlanRelDto> projectWbsReceiptsDesignPlanRelDtoList = new ArrayList<>();
        for (ProjectWbsReceiptsDesignPlanRelDto relationDto : relationDtos) {
            String pamCode = relationDto.getPamCode();
            String materialCategory = relationDto.getMaterialCategory();
            String materialClassification = relationDto.getMaterialClassification();
            String codingMiddleClass = relationDto.getCodingMiddleClass();
            String materielType = relationDto.getMaterielType();
            Integer moduleStatus = relationDto.getModuleStatus();
            if ((Objects.equals(materialCategory, "外购物料") || Objects.equals(materialCategory, "看板物料"))
                    && (Objects.equals(MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code(), moduleStatus) || null == moduleStatus)) {
                if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
                    if (StringUtils.isNotEmpty(codingMiddleClass) && StringUtils.isNotEmpty(materielType)
                            && !erpCodeRuleMap.containsKey(getFlexiblePurchaseRecordMaterialERPCodeRuleKeyKUNSHANKey(codingMiddleClass,
                            materielType))) {
                        throw new ApplicationBizException(String.format("详细设计数据的pam编码：%s的物料中类：%s，物料小类：%s的物料编码规则：%s不存在",
                                pamCode, codingMiddleClass, materielType, materialCodeRule));
                    }
                } else if (MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
                    if (StringUtils.isNotEmpty(materialClassification) && StringUtils.isNotEmpty(codingMiddleClass) && StringUtils.isNotEmpty(materielType)
                            && !erpCodeRuleMap.containsKey(getFlexiblePurchaseRecordMaterialERPCodeRuleKeyKUKA2022Key(materialClassification,
                            codingMiddleClass, materielType))) {
                        throw new ApplicationBizException(String.format("详细设计数据的pam编码：%s的物料大类：%s，物料中类：%s，物料小类：%s的物料编码规则：%s不存在",
                                pamCode, materialClassification, codingMiddleClass, materielType, materialCodeRule));
                    }
                }
            }

            MilepostDesignPlanDetail planDetail = new MilepostDesignPlanDetail();
            planDetail.setId(relationDto.getDesignPlanDetailId());
            planDetail.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMING.code());
            planDetail.setStatus(CheckStatus.WBS_RECEIPTS_PENDING.code());
            updateDesignPlanDetailList.add(planDetail);
            relationDto.setProjectWbsReceiptsId(dto.getId());
            relationDto.setDeletedFlag(false);
            projectWbsReceiptsDesignPlanRelDtoList.add(relationDto);
        }
        if (!CollectionUtils.isEmpty(updateDesignPlanDetailList)) {
            milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
        }
        if (!CollectionUtils.isEmpty(projectWbsReceiptsDesignPlanRelDtoList)) {
            projectWbsReceiptsDesignPlanRelService.batchSave(projectWbsReceiptsDesignPlanRelDtoList, userId);
        }
    }

    private String getFlexiblePurchaseRecordMaterialERPCodeRuleKeyKUKA2022Key(String codingClass, String codingMiddleclass, String codingSubclass) {
        return String.format("getFlexiblePurchaseRecordMaterialERPCodeRuleKeyKUKA2022Key_%s_%s_%s", codingClass, codingMiddleclass, codingSubclass);
    }

    private String getFlexiblePurchaseRecordMaterialERPCodeRuleKeyKUNSHANKey(String codingMiddleclass, String codingSubclass) {
        return String.format("getFlexiblePurchaseRecordMaterialERPCodeRuleKeyKUNSHANKey_%s_%s", codingMiddleclass, codingSubclass);
    }

    /**
     * 保存详细设计关联（进度确认）
     *
     * @param dto
     * @param userBy
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveConfirmRecordWithRecursiveDetail(ProjectWbsReceiptsDto dto, Long userBy) {
        //校验模组是否可进度确认
        checkParams(dto);
        if (dto.getProjectSubmit() == null) {
            dto.setProjectSubmit(Boolean.FALSE);
        }

        // 查询当前使用单位的物料编码规则
        String materialCodeRule = materialExtService.getMaterialCodeRule(SystemContext.getUnitId());
        ErpCodeRuleExample erpCodeRuleExample = new ErpCodeRuleExample();
        ErpCodeRuleExample.Criteria criteria = erpCodeRuleExample.createCriteria();
        criteria.andDeletedFlagEqualTo(Boolean.FALSE).andRuleNameEqualTo(materialCodeRule);
        List<ErpCodeRule> erpCodeRuleList = erpCodeRuleMapper.selectByExample(erpCodeRuleExample);
        Map<String, List<ErpCodeRule>> erpCodeRuleMap = new HashMap<>();

        if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
            erpCodeRuleMap.putAll(erpCodeRuleList.stream().collect(Collectors.groupingBy(e ->
                    getFlexibleConfirmRecordMaterialERPCodeRuleKeyKUNSHANKey(e.getCodingMiddleclass(), e.getCodingSubclass()))));
        } else if (MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
            erpCodeRuleMap.putAll(erpCodeRuleList.stream().collect(Collectors.groupingBy(e ->
                    getFlexibleConfirmRecordMaterialERPCodeRuleKeyKUKA2022Key(e.getCodingClass(), e.getCodingMiddleclass(), e.getCodingSubclass()))));
        }

        //保存单据详设关联关系
        List<ProjectWbsReceiptsDesignPlanRelDto> relationDtos = BeanConverter.copy(dto.getDesignRelList(), ProjectWbsReceiptsDesignPlanRelDto.class);
        List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
        List<ProjectWbsReceiptsDesignPlanRelDto> projectWbsReceiptsDesignPlanRelDtoList = new ArrayList<>();
        for (ProjectWbsReceiptsDesignPlanRelDto relationDto : relationDtos) {
            String pamCode = relationDto.getPamCode();
            String materialCategory = relationDto.getMaterialCategory();
            String materialClassification = relationDto.getMaterialClassification();
            String codingMiddleClass = relationDto.getCodingMiddleClass();
            String materielType = relationDto.getMaterielType();
            Integer moduleStatus = relationDto.getModuleStatus();
            // 校验是否在编码规则维护的大中小类不存在
            materialERPCodeRuleCheck(pamCode, materialCategory, materialClassification, codingMiddleClass, materielType, moduleStatus,
                    erpCodeRuleMap, materialCodeRule);

            List<MilepostDesignPlanDetail> sonPlanDetailList =
                    milepostDesignPlanDetailExtMapper.selectByParentId(relationDto.getDesignPlanDetailId());
            // 递归校验所有的下级以及下下级
            milepostDesignPlanDetailCheck(sonPlanDetailList, erpCodeRuleMap, materialCodeRule);

            //已存在单据即部分确认的单据 不再保存
            List<ProjectWbsReceiptsDto> receipts =
                    projectWbsReceiptsExtMapper.selectProcessReceiptsByDetailIds(Collections.singletonList(relationDto.getDesignPlanDetailId()));
            if (ListUtils.isNotEmpty(receipts)) {
                continue;
            }

            relationDto.setProjectWbsReceiptsId(dto.getId());
            relationDto.setDeletedFlag(false);
            projectWbsReceiptsDesignPlanRelDtoList.add(relationDto);
            MilepostDesignPlanDetail planDetail = new MilepostDesignPlanDetail();
            planDetail.setId(relationDto.getDesignPlanDetailId());
            planDetail.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMING.code());
            planDetail.setStatus(CheckStatus.WBS_RECEIPTS_PENDING.code());
            updateDesignPlanDetailList.add(planDetail);
        }
        if (!CollectionUtils.isEmpty(updateDesignPlanDetailList)) {
            milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
        }
        if (!CollectionUtils.isEmpty(projectWbsReceiptsDesignPlanRelDtoList)) {
            projectWbsReceiptsDesignPlanRelService.batchSave(projectWbsReceiptsDesignPlanRelDtoList, userBy);
        }
    }

    private void milepostDesignPlanDetailCheck(List<MilepostDesignPlanDetail> planDetailList, Map<String, List<ErpCodeRule>> erpCodeRuleMap,
                                               String materialCodeRule) {
        if (CollectionUtils.isEmpty(planDetailList)) {
            return;
        }
        for (MilepostDesignPlanDetail detail : planDetailList) {
            String pamCode = detail.getPamCode();
            String materialCategory = detail.getMaterialCategory();
            String materialClassification = detail.getMaterialClassification();
            String codingMiddleClass = detail.getCodingMiddleClass();
            String materielType = detail.getMaterielType();
            Integer moduleStatus = detail.getModuleStatus();
            materialERPCodeRuleCheck(pamCode, materialCategory, materialClassification, codingMiddleClass, materielType, moduleStatus,
                    erpCodeRuleMap, materialCodeRule);

            List<MilepostDesignPlanDetail> sonPlanDetailList = milepostDesignPlanDetailExtMapper.selectByParentId(detail.getId());
            milepostDesignPlanDetailCheck(sonPlanDetailList, erpCodeRuleMap, materialCodeRule);
        }
    }

    private void materialERPCodeRuleCheck(String pamCode, String materialCategory, String materialClassification, String codingMiddleClass,
                                          String materielType, Integer moduleStatus, Map<String, List<ErpCodeRule>> erpCodeRuleMap,
                                          String materialCodeRule) {
        if ((Objects.equals(materialCategory, "外购物料") || Objects.equals(materialCategory, "看板物料"))
                && (Objects.equals(MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code(), moduleStatus) || null == moduleStatus)) {
            if (MaterialCodeRuleEnum.KUNSHAN.getCode().equals(materialCodeRule)) {
                if (StringUtils.isNotEmpty(codingMiddleClass) && StringUtils.isNotEmpty(materielType)
                        && !erpCodeRuleMap.containsKey(getFlexibleConfirmRecordMaterialERPCodeRuleKeyKUNSHANKey(codingMiddleClass, materielType))) {
                    throw new ApplicationBizException(String.format("详细设计数据的pam编码：%s的物料中类：%s，物料小类：%s的物料编码规则：%s不存在",
                            pamCode, codingMiddleClass, materielType, materialCodeRule));
                }
            } else if (MaterialCodeRuleEnum.KUKA2022.getCode().equals(materialCodeRule)) {
                if (StringUtils.isNotEmpty(materialClassification) && StringUtils.isNotEmpty(codingMiddleClass) && StringUtils.isNotEmpty(materielType)
                        && !erpCodeRuleMap.containsKey(getFlexibleConfirmRecordMaterialERPCodeRuleKeyKUKA2022Key(materialClassification,
                        codingMiddleClass, materielType))) {
                    throw new ApplicationBizException(String.format("详细设计数据的pam编码：%s的物料大类：%s，物料中类：%s，物料小类：%s的物料编码规则：%s不存在",
                            pamCode, materialClassification, codingMiddleClass, materielType, materialCodeRule));
                }
            }
        }
    }

    private String getFlexibleConfirmRecordMaterialERPCodeRuleKeyKUKA2022Key(String codingClass, String codingMiddleclass, String codingSubclass) {
        return String.format("getFlexibleConfirmRecordMaterialERPCodeRuleKeyKUKA2022Key_%s_%s_%s", codingClass, codingMiddleclass, codingSubclass);
    }

    private String getFlexibleConfirmRecordMaterialERPCodeRuleKeyKUNSHANKey(String codingMiddleclass, String codingSubclass) {
        return String.format("getFlexibleConfirmRecordMaterialERPCodeRuleKeyKUNSHANKey_%s_%s", codingMiddleclass, codingSubclass);
    }

    private void checkParams(ProjectWbsReceiptsDto dto) {
        Asserts.notEmpty(dto.getProjectId(), ErrorCode.CTC_PROJECT_ID_NULL);

        //判断是否还有详细设计中物料有审批中的，如果有这不能进行模组确认
        Project project = projectService.selectByPrimaryKey(dto.getProjectId());
        List<Long> designPlanDetailIds =
                dto.getDesignRelList().stream().map((ProjectWbsReceiptsDesignPlanRelDto::getDesignPlanDetailId)).collect(Collectors.toList());
        List<Long> listId = new ArrayList<>();
        Set<String> valueSet = organizationCustomDictService.queryByName("模组确认时需检查回款的项目类型",
                SystemContext.getUnitId(),
                OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom(OrgCustomDictOrgFrom.COMPANY.code()));
        ProjectType projectType = projectTypeMapper.selectByPrimaryKey(project.getType());
        if (!valueSet.isEmpty()) {
            for (String value : valueSet) {
                if (value.contains(projectType.getName())) {
                    if (StringUtils.isNotEmpty(project.getContractId() + "")) {
                        listId =
                                milepostDesignPlanConfirmRecordServiceImpl.findReceiptClaimContractRel(project.getContractId());
                    }
                    Asserts.isTrue(ListUtils.isNotEmpty(listId), ErrorCode.CTC_DESIGN_PLAN_RECEIPTCLAIMCONTRACT);
                }
            }
        }
        for (Long designPlanDetailId : designPlanDetailIds) {
            //反查父级项目物料预算id和交货时间不为空的模组
            List<MilepostDesignPlanDetailDto> parentDesignPlanDetails =
                    milepostDesignPlanDetailExtMapper.selectModule(designPlanDetailId);
            if (ListUtils.isNotEmpty(parentDesignPlanDetails)) {
                if (Boolean.FALSE.equals(parentDesignPlanDetails.get(0).getDeletedFlag())) {
                    Asserts.isTrue(!ObjectUtils.isEmpty(parentDesignPlanDetails.get(0).getDeliveryTime()),
                            ErrorCode.CTC_DELIVERY_TIME_IS_NOT_NULL);
                }
            }
            //查询当前模组下所有的模组和物料
            List<MilepostDesignPlanDetailDto> sonDesignPlanDetails =
                    milepostDesignPlanDetailExtMapper.selectById(designPlanDetailId);
            if (ListUtils.isNotEmpty(sonDesignPlanDetails)) {
                for (MilepostDesignPlanDetailDto plan : sonDesignPlanDetails) {
                    //草稿
                    Asserts.isTrue(CheckStatus.DRAFT.code() != plan.getStatus(),
                            ErrorCode.CTC_DESIGNPLAN_DRFT_IS_NOT_NULL);
                    //草稿审批中
                    Asserts.isTrue(CheckStatus.CHECKING.code() != plan.getStatus(),
                            ErrorCode.CTC_DESIGNPLAN_DRFT_IS_NOT_NULL);
                    //草稿被驳回
                    //Asserts.isTrue(CheckStatus.REJECT.code() != plan.getStatus(), ErrorCode
                    // .CTC_DESIGNPLAN_DRFT_IS_NOT_NULL);
                }
            }
        }
    }


    /**
     * 生成详细设计单据编号，规则：REL+年+月+日+流水号   比如，REL2022050700001
     *
     * @return
     */
    public String generateRequirementCode() {
        // 并发时候的key值
        String lockKey = "ProjectWbsReceiptsServiceImpl.generateRequirementCode.lockKey";
        String value = "ProjectWbsReceiptsServiceImpl.generateRequirementCode.value";
        String requirementCode = null;
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");

        // 创建事务模板
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);

        try {
            // 获取分布式锁
            if (DistributedCASLock.lock(lockKey, value, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 在事务中执行序号生成逻辑
                requirementCode = transactionTemplate.execute(status -> {
                    try {
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append("REL");
                        stringBuffer.append(currentDateStr);

                        // 1. 查询当天记录
                        RequirementCodeRecordExample example = new RequirementCodeRecordExample();
                        RequirementCodeRecordExample.Criteria criteria = example.createCriteria();
                        criteria.andDateStrEqualTo(currentDateStr);
                        List<RequirementCodeRecord> recordList = requirementCodeRecordMapper.selectByExample(example);

                        if (CollectionUtils.isEmpty(recordList)) {
                            // 2a. 新增记录
                            RequirementCodeRecord record = new RequirementCodeRecord();
                            record.setDateStr(currentDateStr);
                            record.setSerialNumber(1);
                            requirementCodeRecordMapper.insertSelective(record);
                            stringBuffer.append(StringUtils.getNextSerial(Constants.CODE_REQUIREMENT_CODE_LENGTH, 0));
                        } else {
                            // 2b. 更新序号
                            RequirementCodeRecord record = recordList.get(0);
                            Integer serialNumber = record.getSerialNumber();
                            Guard.notNull(serialNumber, "详细设计单据编号为空");

                            // 使用乐观锁更新,并添加重试机制
                            int maxRetries = 3;
                            int retryCount = 0;
                            int updated = 0;

                            while (retryCount < maxRetries && updated == 0) {
                                updated = requirementCodeRecordExtMapper.addSerialNumber(record.getId(), serialNumber);
                                if (updated == 0) {
                                    // 更新失败,重新获取最新序号
                                    record = requirementCodeRecordMapper.selectByPrimaryKey(record.getId());
                                    if (record == null) {
                                        break;
                                    }
                                    serialNumber = record.getSerialNumber();
                                    retryCount++;
                                    // 短暂休眠避免立即重试
                                    Thread.sleep(10);
                                }
                            }

                            if (updated == 0) {
                                // 重试次数用完仍然失败,回滚事务
                                logger.error("更新序号失败,已重试{}次", maxRetries);
                                status.setRollbackOnly();
                                return null;
                            }
                            stringBuffer.append(StringUtils.getNextSerial(Constants.CODE_REQUIREMENT_CODE_LENGTH, serialNumber));
                        }
                        return stringBuffer.toString();
                    } catch (Exception e) {
                        // 发生异常回滚事务
                        status.setRollbackOnly();
                        throw new RuntimeException("生成单据编号异常", e);
                    }
                });
            }
        } catch (ApplicationBizException e) {
            logger.info("生成详细设计单据编号处理异常:{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("生成详细设计单据编号处理异常", e);
            throw e;
        } finally {
            // 释放分布式锁
            DistributedCASLock.unLock(lockKey, value);
        }

        Guard.notNullOrEmpty(requirementCode, "详细设计单据编号生成失败");
        return requirementCode;
    }


    /**
     * 生成流程名称
     *
     * @param projectName
     * @param confirmMode
     * @return
     */
    public String generateProcessName(String projectName, Integer confirmMode) {
        StringBuffer processName = new StringBuffer();
        processName.append(projectName);
        if (Objects.equals(confirmMode, ReceiptsConfirmModeEnum.PURCHASE.getCode())) {
            processName.append("_提前采购_");
        } else if (Objects.equals(confirmMode, ReceiptsConfirmModeEnum.CONFIRM.getCode())) {
            processName.append("_模组确认_");
        }
        processName.append(CacheDataUtils.generateSequence(Constants.CODE_CONFIRM_RECORD_PROCESS_LENGTH,
                Constants.SEQUENCE_TYPE_CONFIRM_RECORD_PROCESS));
        return processName.toString();
    }

    /**
     * 保存详细设计单据
     *
     * @param dto
     * @return
     */
    public ProjectWbsReceiptsDto save(ProjectWbsReceiptsDto dto, Long userBy) {
        if (dto.getId() == null) {
            if (null != userBy && null == dto.getCreateBy()) {
                dto.setCreateBy(userBy);
            }
            dto.setCreateAt(new Date());
            return this.add(dto);
        } else {
            if (null != userBy) {
                dto.setUpdateBy(userBy);
            }
            dto.setUpdateAt(new Date());
            return this.update(dto);
        }
    }

    /**
     * 新增详细设计单据
     *
     * @param dto
     * @return
     */
    public ProjectWbsReceiptsDto add(ProjectWbsReceiptsDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        ProjectWbsReceipts entity = BeanConverter.copy(dto, ProjectWbsReceipts.class);
        projectWbsReceiptsMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    /**
     * 更新详细设计单据
     *
     * @param dto
     * @return
     */
    public ProjectWbsReceiptsDto update(ProjectWbsReceiptsDto dto) {
        ProjectWbsReceipts entity = BeanConverter.copy(dto, ProjectWbsReceipts.class);
        projectWbsReceiptsMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }


    @Override
    public ProjectWbsReceiptsDto getById(Long id) {
        return BeanConverter.copy(projectWbsReceiptsMapper.selectByPrimaryKey(id), ProjectWbsReceiptsDto.class);
    }

    @Override
    public List<ProjectWbsReceiptsDto> selectList(ProjectWbsReceiptsDto query) {
        return null;
    }

    @Override
    public PageInfo<ProjectWbsReceiptsDto> selectPage(ProjectWbsReceiptsDto query) {
        return null;
    }

    @Override
    public List<ProjectWbsReceipts> selectByExample(ProjectWbsReceiptsExample example) {
        return projectWbsReceiptsMapper.selectByExample(example);
    }

    @Override
    public List<ProjectWbsReceipts> selectByExampleWithBLOBs(ProjectWbsReceiptsExample example) {
        return projectWbsReceiptsMapper.selectByExampleWithBLOBs(example);
    }

    public List<MilepostDesignPlanDetailDto> setDetailByConfirm(ProjectWbsReceiptsDto dto) {
        //获取确认模组ids
        ProjectWbsReceiptsDesignPlanRelExample relationExample = new ProjectWbsReceiptsDesignPlanRelExample();
        relationExample.createCriteria().andProjectWbsReceiptsIdEqualTo(dto.getId());
        List<ProjectWbsReceiptsDesignPlanRel> relations = ProjectWbsReceiptsDesignPlanRelMapper.selectByExample(relationExample);
        List<ProjectWbsReceiptsDesignPlanRelDto> relationDtos = BeanConverter.copy(relations,
                ProjectWbsReceiptsDesignPlanRelDto.class);
        if (ListUtils.isEmpty(relationDtos)) {
            return new ArrayList<>();
        }
        List<Long> confirmDesignPlanDetailIds = ListUtil.map(relationDtos, "designPlanDetailId");

        //查询详设提交数据以及所有上级数据
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                milepostDesignPlanDetailExtMapper.selectDesignPlanDetailForConfirm(confirmDesignPlanDetailIds);
        logger.info("setDetailByConfirm的milepostDesignPlanDetailDtos:{}", JsonUtils.toString(milepostDesignPlanDetailDtos));

        milepostDesignPlanDetailDtos.forEach(detailDto -> {
            if (confirmDesignPlanDetailIds.contains(detailDto.getId())) {
                detailDto.setIsCheck(true);
            }
        });

        //获取详设关联附件
        Map<Long, String> attachMap = relationDtos.stream().filter(rel -> StringUtils.isNotEmpty(rel.getAttachs()))
                .collect(Collectors.toMap(ProjectWbsReceiptsDesignPlanRelDto::getDesignPlanDetailId, ProjectWbsReceiptsDesignPlanRelDto::getAttachs,
                        (oldValue, newValue) -> oldValue + "," + newValue));
        logger.info("setDetailByConfirm的attachMap:{}", JsonUtils.toString(attachMap));
        milepostDesignPlanDetailDtos.forEach(s -> s.setAttachs(attachMap.get(s.getId())));

        //构建树形结构
        List<MilepostDesignPlanDetailDto> list = buildTree(milepostDesignPlanDetailDtos);
        dto.setDesignPlanDetailDtos(list);
        return list;
    }

    public List<MilepostDesignPlanDetailDto> setDetailByPurchase(ProjectWbsReceiptsDto dto) {
        //获取提前采购的物料ids
        ProjectWbsReceiptsDesignPlanRelExample relationExample = new ProjectWbsReceiptsDesignPlanRelExample();
        relationExample.createCriteria().andProjectWbsReceiptsIdEqualTo(dto.getId());
        List<ProjectWbsReceiptsDesignPlanRel> relations = ProjectWbsReceiptsDesignPlanRelMapper.selectByExample(relationExample);
        List<ProjectWbsReceiptsDesignPlanRelDto> relationDtos = BeanConverter.copy(relations, ProjectWbsReceiptsDesignPlanRelDto.class);
        if (ListUtils.isEmpty(relationDtos)) {
            return new ArrayList<>();
        }
        List<Long> confirmDesignPlanDetailIds = ListUtil.map(relationDtos, "designPlanDetailId");

        //查询详设提交数据以及所有上级数据
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos =
                milepostDesignPlanDetailExtMapper.selectDesignPlanDetailForPurchase(confirmDesignPlanDetailIds, dto.getId());
        logger.info("setDetailByPurchase的milepostDesignPlanDetailDtos:{}", JsonUtils.toString(milepostDesignPlanDetailDtos));

        milepostDesignPlanDetailDtos.forEach(detailDto -> {
            if (confirmDesignPlanDetailIds.contains(detailDto.getId())) {
                detailDto.setIsCheck(true);
            }
        });

        //获取详设关联附件
        Map<Long, String> attachMap = relationDtos.stream().filter(rel -> StringUtils.isNotEmpty(rel.getAttachs()))
                .collect(Collectors.toMap(ProjectWbsReceiptsDesignPlanRelDto::getDesignPlanDetailId, ProjectWbsReceiptsDesignPlanRelDto::getAttachs,
                        (oldValue, newValue) -> oldValue + "," + newValue));
        logger.info("setDetailByPurchase的attachMap:{}", JsonUtils.toString(attachMap));
        milepostDesignPlanDetailDtos.forEach(s -> s.setAttachs(attachMap.get(s.getId())));

        //构建树形结构
        List<MilepostDesignPlanDetailDto> list = buildTree(milepostDesignPlanDetailDtos);
        if (ListUtil.isPresent(list)) {
            countRequirementNum(list.get(0), BigDecimal.ONE);
            dto.setDesignPlanDetailDtos(list);
        }
        return list;
    }

    private List<MilepostDesignPlanDetailDto> buildTree(List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos) {

        // 根节点
        List<MilepostDesignPlanDetailDto> rootDetailDtos = new ArrayList<>();

        // 组装所有的根节点
        for (MilepostDesignPlanDetailDto detailDto : milepostDesignPlanDetailDtos) {
            if (detailDto.getParentId() == -1) {
                rootDetailDtos.add(detailDto);
            }
        }
        //为根节点设置子节点，getClild递归调用
        for (MilepostDesignPlanDetailDto rootDetailDto : rootDetailDtos) {
            // 获取根节点下面的所有子节点，使用getClild方法
            List<MilepostDesignPlanDetailDto> childList = getChild(rootDetailDto.getId(), milepostDesignPlanDetailDtos);
            //给根节点设置子节点
            rootDetailDto.setSonDtos(childList);
        }

        return rootDetailDtos;

    }

    private List<MilepostDesignPlanDetailDto> getChild(Long id, List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos) {
        // 子节点
        List<MilepostDesignPlanDetailDto> childList = new ArrayList<MilepostDesignPlanDetailDto>();
        for (MilepostDesignPlanDetailDto detailDtoAll : milepostDesignPlanDetailDtos) {
            // 遍历所有节点，将所有节点的父id与传过来的根节点的id进行比较，相等则说明：为该根节点的子节点
            if (detailDtoAll.getParentId().equals(id)) {
                childList.add(detailDtoAll);
            }
        }

        // 进行递归
        for (MilepostDesignPlanDetailDto dto : childList) {
            dto.setSonDtos(getChild(dto.getId(), milepostDesignPlanDetailDtos));
        }

        //如果节点下没有子节点，返回一个空List（递归退出)
        if (childList.size() == 0) {
            return new ArrayList<>();
        }
        return childList;
    }

    private void countRequirementNum(MilepostDesignPlanDetailDto dto, BigDecimal preNumber) {
        if (dto.getNumber() == null) return;
        BigDecimal parentNumber = dto.getNumber().multiply(preNumber);
        List<MilepostDesignPlanDetailDto> sonDtos = dto.getSonDtos();
        if (CollectionUtils.isEmpty(sonDtos)) {
            return;
        }
        Iterator<MilepostDesignPlanDetailDto> iterator = sonDtos.iterator();
        while (iterator.hasNext()) {
            MilepostDesignPlanDetailDto son = iterator.next();
            String materialCategory = son.getMaterialCategory();
            if (!CollectionUtils.isEmpty(son.getSonDtos()) || Objects.equals(
                    "装配件",
                    materialCategory) || Objects.equals("虚拟件", materialCategory)
                    || Objects.isNull(materialCategory)) {
                countRequirementNum(son, parentNumber);
                son.setRequirementNum(null);//模块不需要统计
            } else {
                //需求数量
                son.setTotalNum(Optional.ofNullable(son.getNumber()).orElse(BigDecimal.ZERO).multiply(parentNumber));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectWbsReceiptsDto saveReceiptsForSubmit(ProjectWbsReceiptsDto dto, Long userBy) {

        //草稿状态下，删除单据现有详细设计关联
        if (dto.getId() != null && Objects.equals(dto.getRequirementStatus(),
                RequirementStatusEnum.DRAFT.getCode())) {
            if (!Objects.equals(dto.getProducerId(), userBy)) {
                throw new MipException("当前用户不是单据的制单人，不能操作确认");
            }
            projectWbsReceiptsExtMapper.updateReceiptsDel(dto.getId());
        }

        //保存单据信息
//        dto.setProducerId(userBy);
        dto.setRequirementStatus(RequirementStatusEnum.DRAFT.getCode());
        dto.setRequirementType(RequirementTypeEnum.DESIGN_PLAN_SUBMIT.getCode());
        dto.setConfirmMode(null);
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
        } else {
            dto.setUpdateBy(userBy);
        }

        if (dto.getId() != null || StringUtils.isNotEmpty(dto.getRequirementCode())) {
            if (dto.getId() != null) {
                ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMapper.selectByPrimaryKey(dto.getId());
                if (StringUtils.isEmpty(projectWbsReceipts.getRequirementCode())) {
                    dto.setRequirementCode(generateRequirementCode());
                }
            } else {
                ProjectWbsReceipts receipts = getByRequirementCode(dto.getRequirementCode());
                if (null != receipts) {
                    dto.setId(receipts.getId());
                }
            }
        } else {
            dto.setRequirementCode(generateRequirementCode());
        }
        ProjectWbsReceiptsDto resultDto = this.save(dto, userBy);

        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
        ctcAttachmentDto.setModuleId(resultDto.getId());

        ctcAttachmentService.deleteByModule(ctcAttachmentDto);

        //保存附件信息
        if (ListUtil.isPresent(dto.getAttachmentList())) {
            for (CtcAttachmentDto attachmentDto : dto.getAttachmentList()) {
                if (attachmentDto.getId() == null) {
                    attachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
                    attachmentDto.setModuleId(resultDto.getId());
                }
                ctcAttachmentService.save(attachmentDto, userBy);
            }
        }

        return resultDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRelForSubmit(ProjectWbsReceiptsDto dto, Long userBy, List<Long> historyIds) {
        if (null == dto || CollectionUtils.isEmpty(historyIds)) {
            return;
        }

        //并发时候的key值，只要执行该方法就得先去获得锁，防止添加project_wbs_receipts_design_plan_rel表主键冲突
        String lockKey = Constants.DistributedLockKey.SAVE_PROJECT_WBS_RECEIPTS_DESIGN_PLAN_REL;
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockKey, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                List<ProjectWbsReceiptsDesignPlanRel> designPlanRelList = new ArrayList<>();
                for (Long historyId : historyIds) {
                    ProjectWbsReceiptsDesignPlanRel projectWbsReceiptsDesignPlanRel = new ProjectWbsReceiptsDesignPlanRel();
                    projectWbsReceiptsDesignPlanRel.setProjectId(dto.getProjectId());
                    projectWbsReceiptsDesignPlanRel.setDesignPlanDetailId(historyId);
                    projectWbsReceiptsDesignPlanRel.setProjectWbsReceiptsId(dto.getId());
                    projectWbsReceiptsDesignPlanRel.setDeletedFlag(false);
                    projectWbsReceiptsDesignPlanRel.setCreateBy(userBy);
                    projectWbsReceiptsDesignPlanRel.setCreateAt(new Date());
                    designPlanRelList.add(projectWbsReceiptsDesignPlanRel);
                }
                projectWbsReceiptsDesignPlanRelExtMapper.batchInsert(designPlanRelList);
            }
        } catch (Exception e) {
            logger.error("添加详细设计关联表异常", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockKey, currentDateStr);
        }
    }

    @Override
    public ProjectWbsReceiptsDto findDetailForSubmit(Long id) {
        //查单据
        ProjectWbsReceiptsDto receipts = BeanConverter.copyProperties(projectWbsReceiptsMapper.selectByPrimaryKey(id), ProjectWbsReceiptsDto.class);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);

        if (Objects.nonNull(receipts.getBuyerId())) {
            UserInfo userInfo = CacheDataUtils.findUserById(receipts.getBuyerId());
            if (Objects.nonNull(userInfo)) {
                receipts.setBuyerMip(userInfo.getUsername());
            }
        }

        //查询附件信息
        List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(id, CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code(), null);
        receipts.setAttachmentList(attachmentDtos);

        //查询详设发布详设方案excel表格附件信息
        List<CtcAttachmentDto> excelAttachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(id,
                CtcAttachmentModule.WBS_DESIGN_PLAN_NO_PURCHASE.code(), null);
        if (!excelAttachmentDtos.isEmpty()) {
            receipts.setExcelAttachment(excelAttachmentDtos.get(0));
        }

        if (Objects.equals(receipts.getRequirementType(), RequirementTypeEnum.DESIGN_PLAN_SUBMIT.getCode())) {
            //根据单据id查单据详设关系表
            ProjectWbsReceiptsDesignPlanRelExample relExample = new ProjectWbsReceiptsDesignPlanRelExample();
            relExample.createCriteria().andProjectWbsReceiptsIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE);
            List<ProjectWbsReceiptsDesignPlanRelDto> designPlanRelDtoList =
                    BeanConverter.copy(projectWbsReceiptsDesignPlanRelService.selectExample(relExample),
                            ProjectWbsReceiptsDesignPlanRelDto.class);
            if (ListUtils.isNotEmpty(designPlanRelDtoList)) {
                receipts.setDesignRelList(designPlanRelDtoList);
                List<Long> designPlanIds =
                        designPlanRelDtoList.stream().map(ProjectWbsReceiptsDesignPlanRel::getDesignPlanDetailId).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(designPlanIds)) {
                    //根据关系表查发布历史记录id
                    MilepostDesignPlanDetailSubmitHistoryExample historyExample = new MilepostDesignPlanDetailSubmitHistoryExample();
                    historyExample.createCriteria().andIdIn(designPlanIds).andDeletedFlagEqualTo(Boolean.FALSE);
                    List<MilepostDesignPlanDetailSubmitHistory> submitHistoryList =
                            milepostDesignPlanDetailSubmitHistoryService.selectByExample(historyExample);
                    List<MilepostDesignPlanDetailDto> submitHistoryPlanDetailDtos = BeanConverter.copy(submitHistoryList,
                            MilepostDesignPlanDetailDto.class);
                    List<MilepostDesignPlanDetailDto> topParentDesignPlanDetailDtos =
                            milepostDesignPlanService.filterTopParent(submitHistoryPlanDetailDtos);
                    topParentDesignPlanDetailDtos.removeAll(Collections.singleton(null));
                    List<MilepostDesignPlanDetailDto> designPlanDetailDtos =
                            milepostDesignPlanService.buildMilepostDesignPlanDetailTree(submitHistoryPlanDetailDtos,
                                    topParentDesignPlanDetailDtos);
                    if (ListUtils.isNotEmpty(designPlanDetailDtos)) {
                        countRequirementNum(designPlanDetailDtos.get(0), BigDecimal.ONE);
                    }
                    receipts.setDesignPlanDetailDtos(designPlanDetailDtos);
                }
            }
        }
        return receipts;
    }

    @Override
    public ProjectWbsReceiptsDto findNonHierarchicalSubmitDetail(Long id) {
        //查单据
        ProjectWbsReceiptsDto receipts = BeanConverter.copyProperties(projectWbsReceiptsMapper.selectByPrimaryKey(id), ProjectWbsReceiptsDto.class);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);

        if (Objects.nonNull(receipts.getBuyerId())) {
            UserInfo userInfo = CacheDataUtils.findUserById(receipts.getBuyerId());
            if (Objects.nonNull(userInfo)) {
                receipts.setBuyerMip(userInfo.getUsername());
            }
        }

        //查询附件信息
        List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(id, CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code(), null);
        receipts.setAttachmentList(attachmentDtos);

        //查询详设发布详设方案excel表格附件信息
        List<CtcAttachmentDto> excelAttachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(id,
                CtcAttachmentModule.WBS_DESIGN_PLAN_NO_PURCHASE.code(), null);
        if (!excelAttachmentDtos.isEmpty()) {
            receipts.setExcelAttachment(excelAttachmentDtos.get(0));
        }

        if (Objects.equals(receipts.getRequirementType(), RequirementTypeEnum.DESIGN_PLAN_SUBMIT.getCode())) {
            //根据单据id查单据详设关系表
            ProjectWbsReceiptsDesignPlanRelExample relExample = new ProjectWbsReceiptsDesignPlanRelExample();
            relExample.createCriteria().andProjectWbsReceiptsIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE);
            List<ProjectWbsReceiptsDesignPlanRelDto> designPlanRelDtoList =
                    BeanConverter.copy(projectWbsReceiptsDesignPlanRelService.selectExample(relExample),
                            ProjectWbsReceiptsDesignPlanRelDto.class);
            if (ListUtils.isNotEmpty(designPlanRelDtoList)) {
                receipts.setDesignRelList(designPlanRelDtoList);
                List<Long> designPlanIds =
                        designPlanRelDtoList.stream().map(ProjectWbsReceiptsDesignPlanRel::getDesignPlanDetailId).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(designPlanIds)) {
                    //根据关系表查发布历史记录id
                    MilepostDesignPlanDetailSubmitHistoryExample historyExample = new MilepostDesignPlanDetailSubmitHistoryExample();
                    historyExample.createCriteria().andIdIn(designPlanIds).andHistoryTypeEqualTo(Boolean.TRUE).andDeletedFlagEqualTo(Boolean.FALSE);
                    List<MilepostDesignPlanDetailSubmitHistory> submitHistoryList =
                            milepostDesignPlanDetailSubmitHistoryService.selectByExample(historyExample);
                    List<MilepostDesignPlanDetailDto> submitHistoryPlanDetailDtos = BeanConverter.copy(submitHistoryList,
                            MilepostDesignPlanDetailDto.class);

                    for (MilepostDesignPlanDetailSubmitHistory milepostDesignPlanDetailSubmitHistory : submitHistoryList) {
                        for (MilepostDesignPlanDetailDto submitHistoryPlanDetailDto : submitHistoryPlanDetailDtos) {
                            if (milepostDesignPlanDetailSubmitHistory.getId().equals(submitHistoryPlanDetailDto.getId())) {
                                submitHistoryPlanDetailDto.setSubmitId(milepostDesignPlanDetailSubmitHistory.getId());
                                submitHistoryPlanDetailDto.setSubmitParentId(milepostDesignPlanDetailSubmitHistory.getParentId());
                            }
                        }
                    }
//                    List<MilepostDesignPlanDetailDto> topParentDesignPlanDetailDtos =
//                            milepostDesignPlanService.filterTopParent(submitHistoryPlanDetailDtos);
//                    topParentDesignPlanDetailDtos.removeAll(Collections.singleton(null));
//                    List<MilepostDesignPlanDetailDto> designPlanDetailDtos =
//                            milepostDesignPlanService.buildMilepostDesignPlanDetailTree(submitHistoryPlanDetailDtos,
//                                    topParentDesignPlanDetailDtos);
                    if (ListUtils.isNotEmpty(submitHistoryPlanDetailDtos)) {
                        countRequirementNum(submitHistoryPlanDetailDtos.get(0), BigDecimal.ONE);
                        for (MilepostDesignPlanDetailDto submitHistoryPlanDetailDto : submitHistoryPlanDetailDtos) {
                            Long submitHistoryParentId = submitHistoryPlanDetailDto.getParentId();
                            if (Objects.nonNull(submitHistoryParentId)) {
                                MilepostDesignPlanDetailSubmitHistory parentSubmitHistory = submitHistoryMapper.selectByPrimaryKey(submitHistoryParentId);
                                submitHistoryPlanDetailDto.setParentId(parentSubmitHistory.getDesignPlanDetailId());
                                MilepostDesignPlanDetail parentDesignPlanDetail = milepostDesignPlanDetailMapper.selectByPrimaryKey(parentSubmitHistory.getDesignPlanDetailId());
                                if (Objects.nonNull(parentDesignPlanDetail)) {
                                    submitHistoryPlanDetailDto.setParentMaterielDescr(parentDesignPlanDetail.getMaterielDescr());
                                }
                            }
                        }
                    }

                    // 设置父级物料描述信息 - 从集合中查找上级对象的materielDescr
                    if (ListUtils.isNotEmpty(submitHistoryPlanDetailDtos)) {
                        // 构建ID到对象的映射，便于快速查找
                        Map<Long, MilepostDesignPlanDetailDto> idToDetailMap = submitHistoryPlanDetailDtos.stream()
                                .collect(Collectors.toMap(MilepostDesignPlanDetailDto::getSubmitId, Function.identity(), (existing, replacement) -> existing));

                        // 遍历集合，为每个对象设置parentMaterielDescr
                        for (MilepostDesignPlanDetailDto detail : submitHistoryPlanDetailDtos) {
                            if (Objects.nonNull(detail.getSubmitParentId())) {
                                MilepostDesignPlanDetailDto parentDetail = idToDetailMap.get(detail.getSubmitParentId());
                                if (Objects.nonNull(parentDetail) && Objects.nonNull(parentDetail.getMaterielDescr())) {
                                    detail.setParentMaterielDescr(parentDetail.getMaterielDescr());
                                }
                            }
                        }
                    }

                    receipts.setDesignPlanDetailDtos(submitHistoryPlanDetailDtos);
                }
            }
        }
        return receipts;
    }

    /**
     * 根据详细设计单据，获取详细设计
     *
     * @param receipts
     * @return
     */
    @Override
    public List<MilepostDesignPlanDetailSubmitHistory> getDesignPlanDetailForSubmit(ProjectWbsReceipts receipts) {
        ProjectWbsReceiptsDesignPlanRelExample example = new ProjectWbsReceiptsDesignPlanRelExample();
        example.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andProjectWbsReceiptsIdEqualTo(receipts.getId());
        List<ProjectWbsReceiptsDesignPlanRel> relations =
                ProjectWbsReceiptsDesignPlanRelMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(relations)) {
            return new ArrayList<>();
        }
        MilepostDesignPlanDetailSubmitHistoryExample planDetailHistoryExample =
                new MilepostDesignPlanDetailSubmitHistoryExample();
        planDetailHistoryExample.createCriteria()
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andIdIn(relations.stream().map(ProjectWbsReceiptsDesignPlanRel::getDesignPlanDetailId).collect(
                        Collectors.toList()));
        return milepostDesignPlanDetailSubmitHistoryService.selectByExample(planDetailHistoryExample);
    }

    /**
     * 作废 新建界面第二步(草稿状态)
     *
     * @param id
     * @return 详细设计单据id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteForSubmit(Long id) {
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);


        //逻辑删除单据上传的附件
        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
        ctcAttachmentDto.setModuleId(receipts.getId());
        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
        ctcAttachmentService.deleteByModule(ctcAttachmentDto);

        milepostDesignPlanDetailSubmitHistoryService.deleteForReceipts(id);

        //取消详细设计单据与详细设计方案行的关联关系

        return projectWbsReceiptsExtMapper.updateReceiptsDel(id);
    }

    @Override
    public List<ProjectWbsReceiptsDto> selectByIds(List<Long> ids) {
        return projectWbsReceiptsExtMapper.selectByIds(ids);
    }

    //根据id查询出正在审批的单据
    @Override
    public List<ProjectWbsReceiptsDto> selectApprovingReceiptsByIds(List<Long> ids) {
        return projectWbsReceiptsExtMapper.selectApprovingReceiptsByIds(ids);
    }

    @Override
    public ProjectWbsReceiptsDto findDetailForChange(Long id) {
        //查单据
        ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMapper.selectByPrimaryKey(id);
        Guard.notNull(projectWbsReceipts, "详细设计单据不存在");
        ProjectWbsReceiptsDto receipts = BeanConverter.copyProperties(projectWbsReceipts, ProjectWbsReceiptsDto.class);

        if (Objects.nonNull(receipts.getBuyerId())) {
            UserInfo userInfo = CacheDataUtils.findUserById(receipts.getBuyerId());
            if (Objects.nonNull(userInfo)) {
                receipts.setBuyerMip(userInfo.getUsername());
            }
        }

        //查询附件信息
        List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(id, CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code(), null);
        receipts.setAttachmentList(attachmentDtos);

        //查询详设变更详设方案excel表格附件信息
        List<CtcAttachmentDto> excelAttachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(id,
                CtcAttachmentModule.MDP_CHANGE_THE_CHANGE.code(), null);
        if (!excelAttachmentDtos.isEmpty()) {
            receipts.setExcelAttachment(excelAttachmentDtos.get(0));
        }

        if (!Objects.equals(receipts.getRequirementType(), RequirementTypeEnum.DESIGN_PLAN_CHANGE.getCode())) {
            return receipts;
        }

        List<MilepostDesignPlanDetailChangeDto> historyAndChangeDetails =
                BeanConverter.copy(milepostDesignPlanDetailChangeService.getDetailByReceiptsIdForChange(id),
                        MilepostDesignPlanDetailChangeDto.class);
        DesignPlanDetailChangeHistoryVO changeHistoryVO = new DesignPlanDetailChangeHistoryVO();
        List<MilepostDesignPlanDetailChangeDto> changeDetails = new ArrayList<>();
        List<MilepostDesignPlanDetailChangeDto> historyDetails = new ArrayList<>();
        if (ListUtils.isNotEmpty(historyAndChangeDetails)) {
            List<Long> detailIds = new ArrayList<>();
            Map<Long, MilepostDesignPlanDetailChangeDto> detailChangeDtoMap = new HashMap<>();
            for (MilepostDesignPlanDetailChangeDto designPlanDetailChangeDto : historyAndChangeDetails) {
                detailChangeDtoMap.put(designPlanDetailChangeDto.getId(), designPlanDetailChangeDto);
            }
            Map<Long, MilepostDesignPlanDetailChangeDto> parentDetailChangeDtoMap = new HashMap<>();
            for (MilepostDesignPlanDetailChangeDto designPlanDetailChangeDto : historyAndChangeDetails) {
                if (null != designPlanDetailChangeDto.getDesignPlanDetailId()) {
                    detailIds.add(designPlanDetailChangeDto.getDesignPlanDetailId());
                }
                parentDetailChangeDtoMap.put(designPlanDetailChangeDto.getId(), detailChangeDtoMap.get(designPlanDetailChangeDto.getParentId()));
            }
            //详设是否被确认
            List<ProjectWbsReceiptsDto> processReceiptsDtoList = selectProcessReceiptsByDetailIds(detailIds);
            Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = ListUtils.isEmpty(processReceiptsDtoList) ? new HashMap<>()
                    : processReceiptsDtoList.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId));
            for (MilepostDesignPlanDetailChangeDto designPlanDetailChangeDto : historyAndChangeDetails) {
                Long designPlanDetailId = designPlanDetailChangeDto.getDesignPlanDetailId();
                if (null != designPlanDetailId && ListUtils.isNotEmpty(receiptsDtoMap.get(designPlanDetailId))) {
                    designPlanDetailChangeDto.setHasConfirmed(true);
                } else {
                    designPlanDetailChangeDto.setHasConfirmed(false);
                }
            }

            MilepostDesignPlanDetailExample planDetailExample = new MilepostDesignPlanDetailExample();
            planDetailExample.createCriteria().andIdIn(detailIds).andDeletedFlagEqualTo(Boolean.FALSE);
            List<MilepostDesignPlanDetail> detailList = milepostDesignPlanDetailMapper.selectByExample(planDetailExample);
            Map<Long, MilepostDesignPlanDetail> detailMap = detailList.stream().collect(Collectors.toMap(MilepostDesignPlanDetail::getId,
                    Function.identity()));
            for (MilepostDesignPlanDetailChangeDto changeDto : historyAndChangeDetails) {
                if (!Boolean.FALSE.equals(changeDto.getWbsLastLayer())) {
                    if (Objects.nonNull(changeDto.getDesignPlanDetailId())) {
                        MilepostDesignPlanDetail milepostDesignPlanDetail = detailMap.get(changeDto.getDesignPlanDetailId());
                        if ((Objects.nonNull(milepostDesignPlanDetail) && CheckStatus.WBS_RECEIPTS_DRAFT.code().equals(milepostDesignPlanDetail.getStatus())
                                && !detailIds.contains(milepostDesignPlanDetail.getId()))
                                || (Objects.nonNull(milepostDesignPlanDetail) && CheckStatus.WBS_RECEIPTS_PENDING.code().equals(milepostDesignPlanDetail.getStatus()))
                                || (Objects.nonNull(milepostDesignPlanDetail) && CheckStatus.WBS_RECEIPTS_CHECKING.code().equals(milepostDesignPlanDetail.getStatus()))) {
                            changeDto.setEditable(false);
                        } else {
                            changeDto.setEditable(true);
                        }
                    } else {
                        changeDto.setEditable(true);
                    }
                }
                MilepostDesignPlanDetailChangeDto parentDetailChangeDto = parentDetailChangeDtoMap.get(changeDto.getId());
                if (null != parentDetailChangeDto && Boolean.TRUE.equals(parentDetailChangeDto.getHasConfirmed())) {
                    changeDto.setHasConfirmed(true);
                }
                if (!(changeDto.getParentId() == null || changeDto.getParentId() == -1L)) {
                    continue;
                }
                if (Objects.equals(HistoryType.CHANGE.getCode(), changeDto.getHistoryType())) {
                    changeDetails.add(changeDto);
                }
                if (Objects.equals(HistoryType.HISTORY.getCode(), changeDto.getHistoryType())) {
                    historyDetails.add(changeDto);
                }
            }
        }
        if (ListUtils.isNotEmpty(changeDetails)) {
            changeHistoryVO.setChange(changeDetails.get(0));
            // 设置变更后数据的父级ID
            if (changeDetails.get(0).getDesignPlanDetailId() != null) {
                MilepostDesignPlanDetail detail = milepostDesignPlanDetailMapper.selectByPrimaryKey(changeDetails.get(0).getDesignPlanDetailId());
                if (detail != null && detail.getParentId() != null) {
                    changeDetails.get(0).setDesignPlanParentId(detail.getParentId());
                }
            }
        }
        if (ListUtils.isNotEmpty(historyDetails)) {
            changeHistoryVO.setHistory(historyDetails.get(0));
            // 设置历史数据的父级ID
            if (historyDetails.get(0).getDesignPlanDetailId() != null) {
                MilepostDesignPlanDetail detail = milepostDesignPlanDetailMapper.selectByPrimaryKey(historyDetails.get(0).getDesignPlanDetailId());
                if (detail != null && detail.getParentId() != null) {
                    historyDetails.get(0).setDesignPlanParentId(detail.getParentId());
                }
            }
        }
        if (ListUtils.isNotEmpty(changeDetails) && null != changeDetails.get(0) && null != changeDetails.get(0).getId()
                && ListUtils.isNotEmpty(historyDetails) && null != historyDetails.get(0) && null != historyDetails.get(0).getId()) {
            changeHistoryVO.setSonVos(getDetailVOByParentId(changeDetails.get(0).getId(), historyDetails.get(0).getId(), historyAndChangeDetails));
        }
        receipts.setDesignPlanDetailChangeHistoryVO(changeHistoryVO);

        List<ProjectWbsReceiptsBudgetChangeHistoryDto> budgetHistories =
                projectWbsReceiptsBudgetChangeHistoryService.findReceiptsBudgetHistories(receipts, receipts.getProjectId());

        BigDecimal maxTotalAmount = BigDecimal.ZERO;
        if (ListUtils.isNotEmpty(budgetHistories)) {
            Map<String, List<ProjectWbsReceiptsBudgetChangeHistoryDto>> groupByWbsMap =
                    budgetHistories.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsBudgetChangeHistoryDto::getWbsSummaryCode));
            List<ProjectWbsReceiptsBudgetChangeHistoryDto> budgetChangeHistoryLists = new ArrayList<>();
            for (String key : groupByWbsMap.keySet()) {
                List<ProjectWbsReceiptsBudgetChangeHistoryDto> projectWbsReceiptsBudgetChangeHistoryDtos = groupByWbsMap.get(key);
                BigDecimal totalAmount = projectWbsReceiptsBudgetChangeHistoryDtos.stream().map(ProjectWbsReceiptsBudgetChangeHistoryDto::getBudgetOccupiedAmountAfter).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
                if (totalAmount.compareTo(maxTotalAmount) > 0) {
                    maxTotalAmount = totalAmount;
                }

                ProjectWbsReceiptsBudgetChangeHistoryDto projectWbsReceiptsBudgetChangeHistoryDto =
                        commonSet(projectWbsReceiptsBudgetChangeHistoryDtos, key, receipts.getProjectId());
                //如果页面类型=1（新页面），返回结构层级和需求发布单据一致
                if (Objects.equals(projectWbsReceipts.getWebType(), 1)) {
                    projectWbsReceiptsBudgetChangeHistoryDto.setChilds(projectWbsReceiptsBudgetChangeHistoryDtos);
                    budgetChangeHistoryLists.add(projectWbsReceiptsBudgetChangeHistoryDto);
                    continue;
                }
                for (ProjectWbsReceiptsBudgetChangeHistoryDto changeHistoryDto : projectWbsReceiptsBudgetChangeHistoryDtos) {
                    List<ProjectWbsReceiptsBudgetChangeHistoryDto> childs = changeHistoryDto.getChilds();
                    List<ProjectWbsReceiptsBudgetChangeHistoryDto> demandClasses = new ArrayList<>();
                    List<ProjectWbsReceiptsBudgetChangeHistoryDto> materialOutsourcings = new ArrayList<>();
                    for (ProjectWbsReceiptsBudgetChangeHistoryDto historyDto : childs) {
                        if (Objects.equals(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode(), historyDto.getDemandType())) {
                            demandClasses.add(historyDto);
                        }
                        if (Objects.equals(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode(), historyDto.getDemandType())) {
                            materialOutsourcings.add(historyDto);
                        }
                    }

                    if (ListUtils.isNotEmpty(demandClasses)) {
                        ProjectWbsReceiptsBudgetChangeHistoryDto demandClass = commonSet(demandClasses, key, receipts.getProjectId());
                        demandClass.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode());
                        demandClass.setChilds(demandClasses);
                        if (ListUtils.isNotEmpty(projectWbsReceiptsBudgetChangeHistoryDto.getChilds())) {
                            projectWbsReceiptsBudgetChangeHistoryDto.getChilds().add(demandClass);
                        } else {
                            List<ProjectWbsReceiptsBudgetChangeHistoryDto> changeHistoryDtos = new ArrayList<>();
                            changeHistoryDtos.add(demandClass);
                            projectWbsReceiptsBudgetChangeHistoryDto.setChilds(changeHistoryDtos);
                        }
                    }
                    if (ListUtils.isNotEmpty(materialOutsourcings)) {
                        ProjectWbsReceiptsBudgetChangeHistoryDto materialOutsourcing = commonSet(materialOutsourcings, key, receipts.getProjectId());
                        materialOutsourcing.setDemandType(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode());
                        materialOutsourcing.setChilds(materialOutsourcings);
                        if (ListUtils.isNotEmpty(projectWbsReceiptsBudgetChangeHistoryDto.getChilds())) {
                            projectWbsReceiptsBudgetChangeHistoryDto.getChilds().add(materialOutsourcing);
                        } else {
                            List<ProjectWbsReceiptsBudgetChangeHistoryDto> changeHistoryDtos = new ArrayList<>();
                            changeHistoryDtos.add(materialOutsourcing);
                            projectWbsReceiptsBudgetChangeHistoryDto.setChilds(changeHistoryDtos);
                        }
                    }
                }
                budgetChangeHistoryLists.add(projectWbsReceiptsBudgetChangeHistoryDto);
            }
            receipts.setProjectWbsReceiptsBudgetChangeHistories(budgetChangeHistoryLists);
        }

        // 在返回结果前设置设计计划详情的父级ID
        if (receipts.getDesignPlanDetailChangeHistoryVO() != null) {
            List<DesignPlanDetailChangeHistoryVO> vos = new ArrayList<>();
            vos.add(receipts.getDesignPlanDetailChangeHistoryVO());
            setDesignPlanParentId(vos);
            // 设置父级物料描述
            setParentMaterielDescr(receipts.getDesignPlanDetailChangeHistoryVO());
        }

        //需求发布单为维度，预算占用金额，取金额最大的需求发布单
        receipts.setMaxTotalAmount(maxTotalAmount);
        receipts.setHasConfirmed(historyAndChangeDetails.stream().anyMatch(s -> Objects.equals(s.getHasConfirmed(), Boolean.TRUE)));
        return receipts;
    }

    private ProjectWbsReceiptsBudgetChangeHistoryDto commonSet(List<ProjectWbsReceiptsBudgetChangeHistoryDto> sumBudgetChangeHistoryDtoList,
                                                               String wbsSummaryCode, Long projectId) {
        logger.info("commonSet的sumBudgetChangeHistoryDtoList：{},wbsSummaryCode：{},projectId：{}",
                JSONObject.toJSONString(sumBudgetChangeHistoryDtoList), wbsSummaryCode, projectId);
        ProjectWbsReceiptsBudgetChangeHistoryDto projectWbsReceiptsBudgetChangeHistoryDto = new ProjectWbsReceiptsBudgetChangeHistoryDto();
        projectWbsReceiptsBudgetChangeHistoryDto.setWbsSummaryCode(wbsSummaryCode);

        BigDecimal budgetOccupiedAmountBefore = BigDecimal.ZERO;
        BigDecimal budgetOccupiedAmountAfter = BigDecimal.ZERO;
        List<Long> projectWbsPublishReceiptsIdList = new ArrayList<>();
        List<String> projectWbsPublishReceiptsIdAndDemandTypeList = new ArrayList<>();
        for (ProjectWbsReceiptsBudgetChangeHistoryDto changeHistoryDto : sumBudgetChangeHistoryDtoList) {
            if (Objects.equals(changeHistoryDto.getDemandType(), ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode())) {
                ProjectWbsReceiptsBudgetChangeHistoryExample budgetChangeHistoryExample = new ProjectWbsReceiptsBudgetChangeHistoryExample();
                budgetChangeHistoryExample.createCriteria().andProjectWbsChangeReceiptsIdEqualTo(changeHistoryDto.getProjectWbsChangeReceiptsId())
                        .andProjectWbsPublishReceiptsIdEqualTo(changeHistoryDto.getProjectWbsPublishReceiptsId())
                        .andWbsSummaryCodeEqualTo(wbsSummaryCode)
                        .andDeletedFlagEqualTo(Boolean.FALSE);
                List<ProjectWbsReceiptsBudgetChangeHistory> budgetChangeHistoryList =
                        projectWbsReceiptsBudgetChangeHistoryMapper.selectByExample(budgetChangeHistoryExample);
                for (ProjectWbsReceiptsBudgetChangeHistory allChangeHistory : budgetChangeHistoryList) {
                    if (Objects.equals(allChangeHistory.getDemandType(), ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode())) {
                        continue;
                    }
                    budgetOccupiedAmountBefore =
                            budgetOccupiedAmountBefore.add(Optional.ofNullable(allChangeHistory.getBudgetOccupiedAmountBefore()).orElse(BigDecimal.ZERO));
                    budgetOccupiedAmountAfter =
                            budgetOccupiedAmountAfter.add(Optional.ofNullable(allChangeHistory.getBudgetOccupiedAmountAfter()).orElse(BigDecimal.ZERO));
                    projectWbsPublishReceiptsIdAndDemandTypeList.add(getProjectWbsPublishReceiptsIdAndDemandTypeValue(allChangeHistory.getProjectWbsPublishReceiptsId(), allChangeHistory.getDemandType()));
                }
            } else {
                budgetOccupiedAmountBefore =
                        budgetOccupiedAmountBefore.add(Optional.ofNullable(changeHistoryDto.getBudgetOccupiedAmountBefore()).orElse(BigDecimal.ZERO));
                budgetOccupiedAmountAfter =
                        budgetOccupiedAmountAfter.add(Optional.ofNullable(changeHistoryDto.getBudgetOccupiedAmountAfter()).orElse(BigDecimal.ZERO));
                projectWbsPublishReceiptsIdAndDemandTypeList.add(getProjectWbsPublishReceiptsIdAndDemandTypeValue(changeHistoryDto.getProjectWbsPublishReceiptsId(), changeHistoryDto.getDemandType()));
            }
            projectWbsPublishReceiptsIdList.add(changeHistoryDto.getProjectWbsPublishReceiptsId());
        }
        List<ProjectWbsCostByReceiptsVO> filterSummaryBudgets = getChangeSummaryMap(projectId, wbsSummaryCode, projectWbsPublishReceiptsIdList);
        BigDecimal summaryBudgetsDemandCost = BigDecimal.ZERO;
        BigDecimal summaryBudgetsOnTheWayCost = BigDecimal.ZERO;
        BigDecimal summaryBudgetsIncurredCost = BigDecimal.ZERO;
        for (ProjectWbsCostByReceiptsVO receiptsVO : filterSummaryBudgets) {
            Integer summaryBudgetsDemandType = receiptsVO.getDemandType().intValue();
            Long projectWbsReceiptsId = receiptsVO.getProjectWbsReceiptsId();
            if (projectWbsPublishReceiptsIdAndDemandTypeList.contains(getProjectWbsPublishReceiptsIdAndDemandTypeValue(projectWbsReceiptsId,
                    summaryBudgetsDemandType))) {
                summaryBudgetsDemandCost =
                        summaryBudgetsDemandCost.add(Optional.ofNullable(receiptsVO.getDemandCost()).orElse(BigDecimal.ZERO));
                summaryBudgetsOnTheWayCost =
                        summaryBudgetsOnTheWayCost.add(Optional.ofNullable(receiptsVO.getOnTheWayCost()).orElse(BigDecimal.ZERO));
                summaryBudgetsIncurredCost =
                        summaryBudgetsIncurredCost.add(Optional.ofNullable(receiptsVO.getIncurredCost()).orElse(BigDecimal.ZERO));
            }
        }

        ProjectWbsBudgetSummaryExample budgetSummaryExample = new ProjectWbsBudgetSummaryExample();
        budgetSummaryExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code())
                .andProjectIdEqualTo(projectId)
                .andSummaryCodeEqualTo(wbsSummaryCode)
                .andSummaryTypeEqualTo(ProjectWbsBudgetSummarySummaryTypeEnums.WBS.getType())
                .andProjectDetailSelectFlagEqualTo(Boolean.TRUE);
        List<ProjectWbsBudgetSummary> projectWbsBudgetSummaries = projectWbsBudgetSummaryMapper.selectByExample(budgetSummaryExample);
        ProjectWbsBudgetSummary projectWbsBudgetSummary = ListUtils.isNotEmpty(projectWbsBudgetSummaries) ? projectWbsBudgetSummaries.get(0)
                : new ProjectWbsBudgetSummary();

        projectWbsReceiptsBudgetChangeHistoryDto.setPrice(Optional.ofNullable(projectWbsBudgetSummary.getPrice()).orElse(BigDecimal.ZERO));
//        projectWbsReceiptsBudgetChangeHistoryDto.setBaselineCost(baselineCost);
        BigDecimal demandCost = Optional.ofNullable(projectWbsBudgetSummary.getDemandCost()).orElse(BigDecimal.ZERO)
                .subtract(summaryBudgetsDemandCost);
        BigDecimal onTheWayCost = Optional.ofNullable(projectWbsBudgetSummary.getOnTheWayCost()).orElse(BigDecimal.ZERO)
                .subtract(summaryBudgetsOnTheWayCost);
        BigDecimal incurredCost = Optional.ofNullable(projectWbsBudgetSummary.getIncurredCost()).orElse(BigDecimal.ZERO)
                .subtract(summaryBudgetsIncurredCost);
        BigDecimal remainingCost = Optional.ofNullable(projectWbsBudgetSummary.getPrice()).orElse(BigDecimal.ZERO)
                .subtract(demandCost).subtract(onTheWayCost).subtract(incurredCost);
        projectWbsReceiptsBudgetChangeHistoryDto.setDemandCost(demandCost);
        projectWbsReceiptsBudgetChangeHistoryDto.setOnTheWayCost(onTheWayCost);
        projectWbsReceiptsBudgetChangeHistoryDto.setIncurredCost(incurredCost);
        projectWbsReceiptsBudgetChangeHistoryDto.setRemainingCost(remainingCost);
//        projectWbsReceiptsBudgetChangeHistoryDto.setChangeAccumulateCost(changeAccumulateCost);
        projectWbsReceiptsBudgetChangeHistoryDto.setBudgetOccupiedAmountBefore(budgetOccupiedAmountBefore);
        projectWbsReceiptsBudgetChangeHistoryDto.setBudgetOccupiedAmountAfter(budgetOccupiedAmountAfter);
        return projectWbsReceiptsBudgetChangeHistoryDto;
    }

    private String getProjectWbsPublishReceiptsIdAndDemandTypeValue(Long projectWbsPublishReceiptsId, Integer demandType) {
        return projectWbsPublishReceiptsId + "_" + demandType;
    }

    private List<ProjectWbsCostByReceiptsVO> getChangeSummaryMap(Long projectId, String wbsSummaryCode, List<Long> projectWbsPublishReceiptsIdList) {
        ProjectWbsSummaryDto projectWbsSummaryDto = new ProjectWbsSummaryDto();
        projectWbsSummaryDto.setProjectId(projectId);
        projectWbsSummaryDto.setWbsSummaryCode(wbsSummaryCode);
        projectWbsSummaryDto.setProjectWbsPublishReceiptsIdList(projectWbsPublishReceiptsIdList);
        String url = String.format("%sstatistics/project/wbsCost/requirementCodeBudget", ModelsEnum.STATISTICS.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(120 * 1000);
        httpRequestFactory.setConnectTimeout(120 * 1000);
        httpRequestFactory.setReadTimeout(120 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        DataResponse<List<ProjectWbsCostByReceiptsVO>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<ProjectWbsCostByReceiptsVO>>>() {
                });
        return response.getData();
    }

    private List<DesignPlanDetailChangeHistoryVO> getDetailVOByParentId(Long parentChangeId, Long parentHistoryId,
                                                                        List<MilepostDesignPlanDetailChangeDto> historyAndChangeDetails) {
        List<DesignPlanDetailChangeHistoryVO> designPlanDetailChangeHistoryVOs = new ArrayList<>();
        List<MilepostDesignPlanDetailChangeDto> sonHistoryAndChangeDetails =
                historyAndChangeDetails.stream().filter(detail -> (null != parentChangeId && parentChangeId.equals(detail.getParentId()))
                        || (null != parentHistoryId && parentHistoryId.equals(detail.getParentId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sonHistoryAndChangeDetails)) {
            return designPlanDetailChangeHistoryVOs;
        }

        Map<Long, List<MilepostDesignPlanDetailChangeDto>> changeDtoMap = new HashMap<>();
        List<MilepostDesignPlanDetailChangeDto> newDtoList = new ArrayList<>();
        for (MilepostDesignPlanDetailChangeDto detailChangeDto : sonHistoryAndChangeDetails) {
            Long designPlanDetailId = detailChangeDto.getDesignPlanDetailId();
            if (null != designPlanDetailId) {
                if (changeDtoMap.containsKey(designPlanDetailId)) {
                    changeDtoMap.get(designPlanDetailId).add(detailChangeDto);
                } else {
                    changeDtoMap.put(designPlanDetailId, Lists.newArrayList(detailChangeDto));
                }
            } else {
                newDtoList.add(detailChangeDto);
            }
        }
        if (MapUtils.isNotEmpty(changeDtoMap)) {
            for (Long key : changeDtoMap.keySet()) {
                DesignPlanDetailChangeHistoryVO changeHistoryVO = new DesignPlanDetailChangeHistoryVO();
                List<MilepostDesignPlanDetailChangeDto> milepostDesignPlanDetailChangeDtos = changeDtoMap.get(key);
                List<MilepostDesignPlanDetailChangeDto> changeDetails = new ArrayList<>();
                List<MilepostDesignPlanDetailChangeDto> historyDetails = new ArrayList<>();
                for (MilepostDesignPlanDetailChangeDto changeDto : milepostDesignPlanDetailChangeDtos) {
                    if (Objects.equals(HistoryType.CHANGE.getCode(), changeDto.getHistoryType())) {
                        changeDetails.add(changeDto);
                    }
                    if (Objects.equals(HistoryType.HISTORY.getCode(), changeDto.getHistoryType())) {
                        historyDetails.add(changeDto);
                    }
                }
                Long changeId = null;
                Long historyId = null;
                if (ListUtils.isNotEmpty(changeDetails)) {
                    changeHistoryVO.setChange(changeDetails.get(0));
                    changeId = changeDetails.get(0).getId();
                }
                if (ListUtils.isNotEmpty(historyDetails)) {
                    changeHistoryVO.setHistory(historyDetails.get(0));
                    historyId = historyDetails.get(0).getId();
                }
                //change不会为空 历史没有 变更有的为新增
                if (null != changeId || null != historyId) {
                    changeHistoryVO.setSonVos(getDetailVOByParentId(changeId, historyId, historyAndChangeDetails));
                }
                designPlanDetailChangeHistoryVOs.add(changeHistoryVO);
            }
        }
        if (ListUtils.isNotEmpty(newDtoList)) {
            for (MilepostDesignPlanDetailChangeDto dto : newDtoList) {
                DesignPlanDetailChangeHistoryVO changeHistoryVO = new DesignPlanDetailChangeHistoryVO();
                changeHistoryVO.setChange(dto);
                if (null != dto.getId()) {
                    changeHistoryVO.setSonVos(getDetailVOByParentId(dto.getId(), null, historyAndChangeDetails));
                }
                designPlanDetailChangeHistoryVOs.add(changeHistoryVO);
            }
        }
        return designPlanDetailChangeHistoryVOs;
    }

    /**
     * 设置设计计划详情的父级ID
     *
     * @param designPlanDetailChangeHistoryVOs 设计计划详情变更历史VO列表
     */
    private void setDesignPlanParentId(List<DesignPlanDetailChangeHistoryVO> designPlanDetailChangeHistoryVOs) {
        if (ListUtils.isEmpty(designPlanDetailChangeHistoryVOs)) {
            return;
        }

        // 第一步：先收集所有变更记录ID和对应的设计详情ID，构建映射关系
        Map<Long, Long> changeIdToDetailIdMap = new HashMap<>();
        collectChangeIdToDetailIdMap(designPlanDetailChangeHistoryVOs, changeIdToDetailIdMap);

        // 第二步：使用收集的映射关系设置所有节点的designPlanParentId
        processDesignPlanParentId(designPlanDetailChangeHistoryVOs, changeIdToDetailIdMap);
    }

    /**
     * 收集所有变更记录ID和对应的设计详情ID的映射关系
     *
     * @param voList    变更历史VO列表
     * @param resultMap 结果映射
     */
    private void collectChangeIdToDetailIdMap(List<DesignPlanDetailChangeHistoryVO> voList, Map<Long, Long> resultMap) {
        if (ListUtils.isEmpty(voList)) {
            return;
        }

        for (DesignPlanDetailChangeHistoryVO vo : voList) {
            // 处理变更记录
            if (vo.getChange() != null) {
                Long changeId = vo.getChange().getId();
                Long detailId = vo.getChange().getDesignPlanDetailId();

                if (changeId != null) {
                    // 如果当前节点有设计详情ID，则直接添加映射
                    if (detailId != null) {
                        resultMap.put(changeId, detailId);
                        logger.debug("添加映射: changeId={}, detailId={}", changeId, detailId);
                    }
                    // 如果当前节点没有设计详情ID但有父节点，尝试查询父节点的设计详情ID
                    else if (vo.getChange().getParentId() != null) {
                        try {
                            MilepostDesignPlanDetailChangeDto parentChangeDto =
                                    milepostDesignPlanDetailChangeService.getById(vo.getChange().getParentId());
                            if (parentChangeDto != null && parentChangeDto.getDesignPlanDetailId() != null) {
                                // 记录父节点的映射关系
                                resultMap.put(vo.getChange().getParentId(), parentChangeDto.getDesignPlanDetailId());
                                logger.debug("添加父节点映射: parentChangeId={}, parentDetailId={}",
                                        vo.getChange().getParentId(), parentChangeDto.getDesignPlanDetailId());
                            }
                        } catch (Exception e) {
                            logger.error("获取父节点信息失败: parentId={}, error={}", vo.getChange().getParentId(), e.getMessage());
                        }
                    }
                }
            }

            // 递归处理子节点
            if (ListUtils.isNotEmpty(vo.getSonVos())) {
                collectChangeIdToDetailIdMap(vo.getSonVos(), resultMap);
            }
        }
    }

    /**
     * 使用收集的映射关系处理所有节点的designPlanParentId
     *
     * @param voList                变更历史VO列表
     * @param changeIdToDetailIdMap 变更ID到详情ID的映射
     */
    private void processDesignPlanParentId(List<DesignPlanDetailChangeHistoryVO> voList,
                                           Map<Long, Long> changeIdToDetailIdMap) {
        if (ListUtils.isEmpty(voList)) {
            return;
        }

        for (DesignPlanDetailChangeHistoryVO vo : voList) {
            // 设置变更后数据的父级ID
            if (vo.getChange() != null) {
                // 情况1: 已有设计详情ID的节点
                if (vo.getChange().getDesignPlanDetailId() != null) {
                    try {
                        MilepostDesignPlanDetail detail =
                                milepostDesignPlanDetailMapper.selectByPrimaryKey(vo.getChange().getDesignPlanDetailId());
                        if (detail != null && detail.getParentId() != null) {
                            vo.getChange().setDesignPlanParentId(detail.getParentId());
                            logger.debug("设置已有节点父ID: detailId={}, parentId={}",
                                    vo.getChange().getDesignPlanDetailId(), detail.getParentId());
                        }
                    } catch (Exception e) {
                        logger.error("查询设计详情失败: detailId={}, error={}",
                                vo.getChange().getDesignPlanDetailId(), e.getMessage());
                    }
                }
                // 情况2: 新增节点，通过parentId查找父节点的设计详情ID
                else if (vo.getChange().getParentId() != null) {
                    Long parentChangeId = vo.getChange().getParentId();
                    // 从映射中获取父节点的设计详情ID
                    Long parentDetailId = changeIdToDetailIdMap.get(parentChangeId);

                    if (parentDetailId != null) {
                        vo.getChange().setDesignPlanParentId(parentDetailId);
                        logger.debug("设置新增节点父ID: changeId={}, parentChangeId={}, parentDetailId={}",
                                vo.getChange().getId(), parentChangeId, parentDetailId);
                    } else {
                        // 如果映射中没有，尝试直接查询
                        try {
                            MilepostDesignPlanDetailChangeDto parentChangeDto =
                                    milepostDesignPlanDetailChangeService.getById(parentChangeId);
                            if (parentChangeDto != null && parentChangeDto.getDesignPlanDetailId() != null) {
                                vo.getChange().setDesignPlanParentId(parentChangeDto.getDesignPlanDetailId());
                                logger.debug("通过查询设置新增节点父ID: changeId={}, parentDetailId={}",
                                        vo.getChange().getId(), parentChangeDto.getDesignPlanDetailId());
                            } else {
                                // 如果父节点也是新增节点,则将变更表id设置到DesignPlanParentId
                                if (parentChangeDto != null) {
                                    vo.getChange().setDesignPlanParentId(parentChangeDto.getId());
                                } else {
                                    vo.getChange().setDesignPlanParentId(-1L);
                                }
                                logger.warn("父节点是新增节点，则将父节点变更表id设置到DesignPlanParentId: changeId={}, parentChangeId={}",
                                        vo.getChange().getId(), parentChangeId);
                            }
                        } catch (Exception e) {
                            // 异常情况下设置为-1
                            vo.getChange().setDesignPlanParentId(-1L);
                            logger.error("获取父节点信息异常，设置为-1: parentId={}, error={}",
                                    parentChangeId, e.getMessage());
                        }
                    }
                } else {
                    // 没有父节点ID的情况，设置为-1
                    vo.getChange().setDesignPlanParentId(-1L);
                    logger.debug("节点无父ID，设置为-1: changeId={}", vo.getChange().getId());
                }
            }

            // 设置历史数据的父级ID
            if (vo.getHistory() != null && vo.getHistory().getDesignPlanDetailId() != null) {
                try {
                    MilepostDesignPlanDetail detail =
                            milepostDesignPlanDetailMapper.selectByPrimaryKey(vo.getHistory().getDesignPlanDetailId());
                    if (detail != null && detail.getParentId() != null) {
                        vo.getHistory().setDesignPlanParentId(detail.getParentId());
                        logger.debug("设置历史节点父ID: historyId={}, parentId={}",
                                vo.getHistory().getId(), detail.getParentId());
                    }
                } catch (Exception e) {
                    logger.error("查询历史设计详情失败: detailId={}, error={}",
                            vo.getHistory().getDesignPlanDetailId(), e.getMessage());
                }
            }

            // 递归处理子节点
            if (ListUtils.isNotEmpty(vo.getSonVos())) {
                processDesignPlanParentId(vo.getSonVos(), changeIdToDetailIdMap);
            }
        }
    }

    /**
     * 提交 变更单据新建界面第三步
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProjectWbsReceiptsDto submitChangeReceipts(ProjectWbsReceiptsDto dto) {
        logger.info("提交变更单据submitChangeReceipts，dto:{}", JSONObject.toJSONString(dto));
        if (!Objects.equals(dto.getHandleBy(), SystemContext.getUserId())) {
            throw new ApplicationBizException("当前用户不是单据指定的处理人，不能操作提交审批");
        }
        ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMapper.selectByPrimaryKey(dto.getId());
        Asserts.notNull(projectWbsReceipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);

        List<ProjectWbsReceiptsBudgetChangeHistoryDto> changeHistories = dto.getProjectWbsReceiptsBudgetChangeHistories();

        List<Long> projectWbsPublishReceiptsIdList = new ArrayList<>();
        List<ProjectWbsReceiptsBudgetChangeHistoryDto> finalChangeHistories = new ArrayList<>();
        for (ProjectWbsReceiptsBudgetChangeHistoryDto changeHistory : changeHistories) {
            List<ProjectWbsReceiptsBudgetChangeHistoryDto> childs = changeHistory.getChilds();
            if (ListUtils.isEmpty(childs)) {
                continue;
            }

            for (ProjectWbsReceiptsBudgetChangeHistoryDto child : childs) {
                List<ProjectWbsReceiptsBudgetChangeHistoryDto> grandChilds = child.getChilds();
                if (ListUtils.isEmpty(grandChilds)) {
                    continue;
                }
                for (ProjectWbsReceiptsBudgetChangeHistoryDto grandChild : grandChilds) {
                    Long projectWbsPublishReceiptsId = grandChild.getProjectWbsPublishReceiptsId();
                    if (!projectWbsPublishReceiptsIdList.contains(projectWbsPublishReceiptsId)) {
                        projectWbsPublishReceiptsIdList.add(projectWbsPublishReceiptsId);
                    }
                }

                finalChangeHistories.addAll(grandChilds);
            }
        }
        logger.info("提交变更单据submitChangeReceipts，finalChangeHistories:{}", JSONObject.toJSONString(finalChangeHistories));

        if (ListUtils.isNotEmpty(projectWbsPublishReceiptsIdList)) {
            List<ProjectWbsReceiptsDto> receiptsDtoList = projectWbsReceiptsExtMapper.selectByIds(projectWbsPublishReceiptsIdList);
            Map<Long, ProjectWbsReceiptsDto> receiptsDtoMap = receiptsDtoList.stream()
                    .collect(Collectors.toMap(ProjectWbsReceiptsDto::getId, Function.identity()));
            ProjectWbsReceiptsRequirementChangeRecordExample changeRecordExample = new ProjectWbsReceiptsRequirementChangeRecordExample();
            changeRecordExample.createCriteria().andProjectWbsReceiptsIdIn(projectWbsPublishReceiptsIdList).andDeletedFlagEqualTo(Boolean.FALSE);
            List<ProjectWbsReceiptsRequirementChangeRecord> changeRecordList =
                    projectWbsReceiptsRequirementChangeRecordMapper.selectByExample(changeRecordExample);
            if (ListUtil.isPresent(changeRecordList)) {
                for (ProjectWbsReceiptsRequirementChangeRecord changeRecord : changeRecordList) {
                    ProjectWbsReceiptsDto receiptsDto = receiptsDtoMap.get(changeRecord.getProjectWbsReceiptsId());
                    Guard.notNull(receiptsDto, String.format("需求发布单据：%s，不存在", changeRecord.getProjectWbsReceiptsId()));

                    if (Objects.equals(changeRecord.getRecordStatus(), ProjectWbsReceiptsRequirementChangeRecordStatusEnum.PENDING.getCode())) {
                        throw new ApplicationBizException(String.format("需求发布单据：%s，存在状态为：审批中的需求预算变更流程，请处理",
                                receiptsDto.getRequirementCode()));
                    }
                }
            }

            //详细设计变更停留待处理，但是需求预算已经发生变化的场景，需要校验，变更前的需求预算如果已经发生变化，提示：变更前的需求预算已经发生变化，请刷新页面
            ProjectWbsReceiptsBudgetExample projectWbsReceiptsBudgetExample = new ProjectWbsReceiptsBudgetExample();
            projectWbsReceiptsBudgetExample.createCriteria().andProjectWbsReceiptsIdIn(projectWbsPublishReceiptsIdList).andDeletedFlagEqualTo(Boolean.FALSE);
            List<ProjectWbsReceiptsBudget> projectWbsReceiptsBudgetList =
                    projectWbsReceiptsBudgetMapper.selectByExample(projectWbsReceiptsBudgetExample);
            Map<String, BigDecimal> projectWbsReceiptsBudgetMap = projectWbsReceiptsBudgetList.stream()
                    .collect(Collectors.toMap(e -> e.getProjectWbsReceiptsId() + "_" + e.getWbsSummaryCode() + "_" + e.getDemandType(),
                            ProjectWbsReceiptsBudget::getBudgetOccupiedAmount));
            logger.info("提交变更单据submitChangeReceipts，projectWbsReceiptsBudgetMap:{}", JSONObject.toJSONString(projectWbsReceiptsBudgetMap));
            for (ProjectWbsReceiptsBudgetChangeHistoryDto historyDto : finalChangeHistories) {
                String key = historyDto.getProjectWbsPublishReceiptsId() + "_" + historyDto.getWbsSummaryCode() + "_" + historyDto.getDemandType();
                if (projectWbsReceiptsBudgetMap.containsKey(key)) {
                    BigDecimal budgetOccupiedAmount = projectWbsReceiptsBudgetMap.get(key);
                    if (!Objects.equals(budgetOccupiedAmount.stripTrailingZeros().toPlainString(),
                            historyDto.getBudgetOccupiedAmountBefore().stripTrailingZeros().toPlainString())) {
                        //变更前的需求预算已经发生变化，请刷新页面
                        this.projectWbsReceiptsBudgetChangeHistory(dto.getId());

                        ProjectWbsReceiptsDto wbsReceiptsDto = new ProjectWbsReceiptsDto();
                        wbsReceiptsDto.setId(dto.getId());
                        wbsReceiptsDto.setProjectWbsReceiptsRequirementIsChange(true);
                        return wbsReceiptsDto;
                    }
                }
            }
        }

        List<ProjectWbsReceiptsDto> totalAmountDtoList = new ArrayList<>();
        List<ProjectWbsReceiptsBudgetChangeHistoryDto> summaryList = new ArrayList<>();
        //如果页面类型=1（新页面），单据详情返回结构层级和需求发布单一致
        if (Objects.equals(projectWbsReceipts.getWebType(), 1)) {
            //第二层对应的是project_wbs_receipts_budget_change_history表demand_type=0的数据，一并放入list，批量更新
            for (ProjectWbsReceiptsBudgetChangeHistoryDto changeHistory : changeHistories) {
                List<ProjectWbsReceiptsBudgetChangeHistoryDto> childs = changeHistory.getChilds();
                if (ListUtils.isNotEmpty(childs)) {
                    finalChangeHistories.addAll(childs);
                    summaryList.addAll(childs);
                }
            }
            if (!CollectionUtils.isEmpty(finalChangeHistories)) {
                List<List<ProjectWbsReceiptsBudgetChangeHistoryDto>> lists = ListUtils.splistList(finalChangeHistories, 200);
                lists.forEach(projectWbsReceiptsBudgetExtMapper::batchUpdateChangeHistory);
            }

            //以需求发布单为维度合计填写的金额，返回给前端，判断走审批分支
            if (!CollectionUtils.isEmpty(summaryList)) {
                Map<String, BigDecimal> totalAmountMap = summaryList.stream()
                        .collect(Collectors.groupingBy(ProjectWbsReceiptsBudgetChangeHistoryDto::getPublishRequirementCode
                                , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().map(s -> s.getBudgetOccupiedAmountAfter()).reduce(BigDecimal.ZERO, BigDecimalUtils::add))));
                for (String key : totalAmountMap.keySet()) {
                    ProjectWbsReceiptsDto totalAmountDto = new ProjectWbsReceiptsDto();
                    totalAmountDto.setRequirementCode(key);
                    totalAmountDto.setTotalAmount(totalAmountMap.get(key));
                    totalAmountDtoList.add(totalAmountDto);
                }
            }

        } else {
            Map<String, List<ProjectWbsReceiptsBudgetChangeHistoryDto>> changeHistoryDtoMap =
                    finalChangeHistories.stream().collect(Collectors.groupingBy(ProjectWbsReceiptsBudgetChangeHistoryDto::getPublishRequirementCode));
            for (String key : changeHistoryDtoMap.keySet()) {
                ProjectWbsReceiptsExample example = new ProjectWbsReceiptsExample();
                example.createCriteria().andDeletedFlagEqualTo(false).andRequirementCodeEqualTo(key);
                List<ProjectWbsReceipts> projectWbsReceiptsList = projectWbsReceiptsMapper.selectByExample(example);
                if (ListUtils.isEmpty(projectWbsReceiptsList)) {
                    continue;
                }

                ProjectWbsReceiptsBudgetExample budgetExample = new ProjectWbsReceiptsBudgetExample();
                budgetExample.createCriteria().andProjectWbsReceiptsIdEqualTo(projectWbsReceiptsList.get(0).getId()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<ProjectWbsReceiptsBudget> budgetList = projectWbsReceiptsBudgetMapper.selectByExample(budgetExample);
                BigDecimal originDemandBudgetOccupiedAmount = BigDecimal.ZERO;
                BigDecimal originOutsourceBudgetOccupiedAmount = BigDecimal.ZERO;
                for (ProjectWbsReceiptsBudget budget : budgetList) {
                    Integer demandType = budget.getDemandType();
                    BigDecimal budgetOccupiedAmount = Optional.ofNullable(budget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO);
                    if (Objects.equals(demandType, ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode())) {
                        originDemandBudgetOccupiedAmount = originDemandBudgetOccupiedAmount.add(budgetOccupiedAmount);
                    } else if (Objects.equals(demandType, ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode())) {
                        originOutsourceBudgetOccupiedAmount = originOutsourceBudgetOccupiedAmount.add(budgetOccupiedAmount);
                    }
                }

                List<ProjectWbsReceiptsBudgetChangeHistoryDto> changeHistoryDtos = changeHistoryDtoMap.get(key);
                BigDecimal budgetOccupiedAmountBeforeDemandTotalAmount = BigDecimal.ZERO;
                BigDecimal budgetOccupiedAmountBeforeOutsourceTotalAmount = BigDecimal.ZERO;
                BigDecimal budgetOccupiedAmountAfterDemandTotalAmount = BigDecimal.ZERO;
                BigDecimal budgetOccupiedAmountAfterOutsourceTotalAmount = BigDecimal.ZERO;
                ProjectWbsReceiptsDto totalAmountDto = new ProjectWbsReceiptsDto();
                for (ProjectWbsReceiptsBudgetChangeHistoryDto changeHistoryDto : changeHistoryDtos) {
                    Integer demandType = changeHistoryDto.getDemandType();
                    BigDecimal budgetOccupiedAmountBefore = Optional.ofNullable(changeHistoryDto.getBudgetOccupiedAmountBefore()).orElse(BigDecimal.ZERO);
                    BigDecimal budgetOccupiedAmountAfter = Optional.ofNullable(changeHistoryDto.getBudgetOccupiedAmountAfter()).orElse(BigDecimal.ZERO);
                    if (Objects.equals(demandType, ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode())) {
                        budgetOccupiedAmountBeforeDemandTotalAmount = budgetOccupiedAmountBeforeDemandTotalAmount.add(budgetOccupiedAmountBefore);
                        budgetOccupiedAmountAfterDemandTotalAmount = budgetOccupiedAmountAfterDemandTotalAmount.add(budgetOccupiedAmountAfter);
                    } else if (Objects.equals(demandType, ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode())) {
                        budgetOccupiedAmountBeforeOutsourceTotalAmount = budgetOccupiedAmountBeforeOutsourceTotalAmount.add(budgetOccupiedAmountBefore);
                        budgetOccupiedAmountAfterOutsourceTotalAmount = budgetOccupiedAmountAfterOutsourceTotalAmount.add(budgetOccupiedAmountAfter);
                    }
                }

                BigDecimal demandTotalAmount = budgetOccupiedAmountAfterDemandTotalAmount.subtract(budgetOccupiedAmountBeforeDemandTotalAmount)
                        .add(originDemandBudgetOccupiedAmount);
                BigDecimal outsourceTotalAmount = budgetOccupiedAmountAfterOutsourceTotalAmount.subtract(budgetOccupiedAmountBeforeOutsourceTotalAmount)
                        .add(originOutsourceBudgetOccupiedAmount);

                totalAmountDto.setRequirementCode(key);
                totalAmountDto.setDemandTotalAmount(demandTotalAmount);
                totalAmountDto.setOutsourceTotalAmount(outsourceTotalAmount);
                totalAmountDtoList.add(totalAmountDto);
            }

            // 保存预算信息
            projectWbsReceiptsBudgetChangeHistoryService.saveReceiptsBudgetChangeHistory(dto.getId(), changeHistories);
        }

        MilepostDesignPlanDetailChangeRecord oldChangeRecord = milepostDesignPlanDetailChangeRecordService.saveAndUpdate(dto.getId(), dto.getFormTemplateId());
        // 提交是先插入一条提交逻辑没有完成的数据
        Long milepostDesignPlanDetailChangeRecordId =
                milepostDesignPlanDetailChangeRecordService.getMilepostDesignPlanDetailChangeRecordId(dto.getId(), dto.getFormTemplateId());
        WorkFlowDraftSubmitTemporaryRecordExample example = new WorkFlowDraftSubmitTemporaryRecordExample();
        example.createCriteria().andFormUrlEqualTo(dto.getFormTemplateId()).andFormInstanceIdEqualTo(milepostDesignPlanDetailChangeRecordId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<WorkFlowDraftSubmitTemporaryRecord> recordList = workFlowDraftSubmitTemporaryRecordExtMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(recordList)) {
            WorkFlowDraftSubmitTemporaryRecord record = new WorkFlowDraftSubmitTemporaryRecord();
            record.setFormUrl(dto.getFormTemplateId());
            record.setFormInstanceId(milepostDesignPlanDetailChangeRecordId);
            record.setStatus(WorkFlowDraftSubmitTemporaryRecordStatus.DONE.getIndex());
            record.setSubmitBusinessFinished(Boolean.FALSE);
            record.setDeletedFlag(Boolean.FALSE);
            logger.info("submitChangeReceipts的工作流提交临时记录add的record：{}", JSONObject.toJSONString(record));
            workFlowDraftSubmitTemporaryRecordExtMapper.insertSelective(record);
        } else {
            logger.info("submitChangeReceipts的表单url：{}，表单实例id：{}对应的提交记录已存在", dto.getFormTemplateId(), dto.getId());
        }

        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
        ctcAttachmentDto.setModuleId(dto.getId());

        ctcAttachmentService.deleteByModule(ctcAttachmentDto);
        // 保存附件信息
        if (ListUtil.isPresent(dto.getAttachmentList())) {
            for (CtcAttachmentDto attachmentDto : dto.getAttachmentList()) {
                if (attachmentDto.getId() == null) {
                    attachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
                    attachmentDto.setModuleId(dto.getId());
                }
                ctcAttachmentService.save(attachmentDto, SystemContext.getUserId());
            }
        }

        ProjectWbsReceiptsDto resultDto = new ProjectWbsReceiptsDto();
        resultDto.setId(dto.getId());
        resultDto.setProjectWbsReceiptsRequirementIsChange(false);
        resultDto.setTotalAmountDtoList(totalAmountDtoList);
        //设置是否修改处理人标识，返回时gateway服务处理上一个处理人的流程实例
        if (oldChangeRecord != null) {
            resultDto.setMilepostDesignPlanDetailChangeRecordId(oldChangeRecord.getId());
            resultDto.setOldHandleUserMip(CacheDataUtils.findUserMipById(oldChangeRecord.getCreateBy()));
        }

        return resultDto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void projectWbsReceiptsBudgetChangeHistory(Long projectWbsChangeReceiptsId) {
        Guard.notNull(projectWbsChangeReceiptsId, "详细设计单据id不能为空");

        ProjectWbsReceiptsBudgetChangeHistoryExample example = new ProjectWbsReceiptsBudgetChangeHistoryExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectWbsChangeReceiptsIdEqualTo(projectWbsChangeReceiptsId);
        List<ProjectWbsReceiptsBudgetChangeHistory> budgetChangeHistoryList = projectWbsReceiptsBudgetChangeHistoryMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(budgetChangeHistoryList)) {
            return;
        }

        Map<String, ProjectWbsReceiptsBudgetChangeHistory> changeHistoryMap = new HashMap<>();
        List<Long> projectWbsReceiptIdList = new ArrayList<>();
        ProjectWbsReceiptsBudgetChangeHistory parentChangeHistory = null;
        for (ProjectWbsReceiptsBudgetChangeHistory changeHistory : budgetChangeHistoryList) {
            if (Objects.equals(changeHistory.getDemandType(), ProjectWbsReceiptsBudgetDemandTypeEnums.ALL.getCode())) {
                parentChangeHistory = changeHistory;
            } else {
                projectWbsReceiptIdList.add(changeHistory.getProjectWbsPublishReceiptsId());
                String key =
                        changeHistory.getProjectWbsPublishReceiptsId() + "_" + changeHistory.getWbsSummaryCode() + "_" + changeHistory.getDemandType();
                changeHistoryMap.put(key, changeHistory);
            }
        }

        BigDecimal parentBudgetOccupiedAmountBeforeDifference = BigDecimal.ZERO;
        ProjectWbsReceiptsBudgetExample projectWbsReceiptsBudgetExample = new ProjectWbsReceiptsBudgetExample();
        projectWbsReceiptsBudgetExample.createCriteria().andProjectWbsReceiptsIdIn(projectWbsReceiptIdList).andDeletedFlagEqualTo(Boolean.FALSE);
        List<ProjectWbsReceiptsBudget> projectWbsReceiptsBudgets = projectWbsReceiptsBudgetMapper.selectByExample(projectWbsReceiptsBudgetExample);
        for (ProjectWbsReceiptsBudget budget : projectWbsReceiptsBudgets) {
            String budgetKey = budget.getProjectWbsReceiptsId() + "_" + budget.getWbsSummaryCode() + "_" + budget.getDemandType();
            if (changeHistoryMap.containsKey(budgetKey)) {
                ProjectWbsReceiptsBudgetChangeHistory changeHistory = changeHistoryMap.get(budgetKey);
                BigDecimal budgetOccupiedAmount = Optional.ofNullable(budget.getBudgetOccupiedAmount()).orElse(BigDecimal.ZERO);
                BigDecimal budgetOccupiedAmountBefore = Optional.ofNullable(changeHistory.getBudgetOccupiedAmountBefore()).orElse(BigDecimal.ZERO);
                parentBudgetOccupiedAmountBeforeDifference = parentBudgetOccupiedAmountBeforeDifference
                        .add(budgetOccupiedAmount.subtract(budgetOccupiedAmountBefore));

                ProjectWbsReceiptsBudgetChangeHistory updateChangeHistory = new ProjectWbsReceiptsBudgetChangeHistory();
                updateChangeHistory.setId(changeHistory.getId());
                updateChangeHistory.setBudgetOccupiedAmountBefore(budgetOccupiedAmount);
                projectWbsReceiptsBudgetChangeHistoryMapper.updateByPrimaryKeySelective(updateChangeHistory);
            }
        }

        if (null != parentChangeHistory) {
            BigDecimal budgetOccupiedAmountBefore = Optional.ofNullable(parentChangeHistory.getBudgetOccupiedAmountBefore()).orElse(BigDecimal.ZERO);
            budgetOccupiedAmountBefore = budgetOccupiedAmountBefore.add(parentBudgetOccupiedAmountBeforeDifference);

            ProjectWbsReceiptsBudgetChangeHistory updateParentChangeHistory = new ProjectWbsReceiptsBudgetChangeHistory();
            updateParentChangeHistory.setId(parentChangeHistory.getId());
            updateParentChangeHistory.setBudgetOccupiedAmountBefore(budgetOccupiedAmountBefore);
            projectWbsReceiptsBudgetChangeHistoryMapper.updateByPrimaryKeySelective(updateParentChangeHistory);
        }
    }

    /**
     * 退回 新建界面第三步
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeReceiptsReject(Long id) {
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);
        if (!Objects.equals(receipts.getHandleBy(), SystemContext.getUserId()) && !Objects.equals(receipts.getProducerId(),
                SystemContext.getUserId())) {
            throw new MipException("当前用户不是单据指定的处理人或制单人，不能操作退回");
        }
        Integer requirementStatus = receipts.getRequirementStatus();
        if (!Objects.equals(requirementStatus, RequirementStatusEnum.TODO.getCode()) && !Objects.equals(requirementStatus,
                RequirementStatusEnum.REFUSE.getCode())) {
            throw new MipException("单据不为待处理或驳回状态，不能退回");
        }

        MilepostDesignPlanDetailChangeExample example = new MilepostDesignPlanDetailChangeExample();
        example.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andProjectWbsReceiptsIdEqualTo(id)
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        List<MilepostDesignPlanDetailChange> designPlanDetailChangeList = milepostDesignPlanDetailChangeService.selectByExample(example);

        List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
        for (MilepostDesignPlanDetailChange change : designPlanDetailChangeList) {
            if (null != change.getDesignPlanDetailId() && !Boolean.FALSE.equals(change.getWbsLastLayer())) {
                MilepostDesignPlanDetailDto milepostDesignPlanDetailDto = new MilepostDesignPlanDetailDto();
                milepostDesignPlanDetailDto.setId(change.getDesignPlanDetailId());
                milepostDesignPlanDetailDto.setStatus(CheckStatus.WBS_RECEIPTS_DRAFT.code());
                List<ProjectWbsReceiptsDto> receiptsDto =
                        selectProcessReceiptsByDetailIds(Collections.singletonList(change.getDesignPlanDetailId()));
                if (ListUtils.isNotEmpty(receiptsDto)) {
                    milepostDesignPlanDetailDto.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());
                } else {
                    milepostDesignPlanDetailDto.setModuleStatus(MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code());
                }
                updateDesignPlanDetailList.add(milepostDesignPlanDetailDto);
            }
        }
        if (!CollectionUtils.isEmpty(updateDesignPlanDetailList)) {
            milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
        }
        //逻辑删除单据保存时创建的预算
        //projectWbsReceiptsExtMapper.updateWbsRequirementBudgetDel(id);

        //单据从"待处理"状态变为"草稿"状态
        receipts.setRequirementStatus(RequirementStatusEnum.DRAFT.getCode());
        //退回邮件提醒
        sendEmailForProjectWbsReceiptReject(receipts, receipts.getProducerId(), NoticeBusinessType.PROJECT_WBS_RECEIPTS_DRAFT);
        return projectWbsReceiptsMapper.updateByPrimaryKeySelective(receipts) > 0;
    }

    /**
     * 作废 新建界面第二步(草稿状态)
     *
     * @param id
     * @return 详细设计单据id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeReceiptsCancel(Long id) {
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);
        Long managerId = getManagerIdFromProjectId(receipts.getProjectId());
        if (!Objects.equals(receipts.getProducerId(), SystemContext.getUserId()) && !Objects.equals(managerId, SystemContext.getUserId())) {
            throw new MipException("当前用户不是单据的制单人或该项目的项目经理，不能操作作废");
        }
        if (!Objects.equals(receipts.getRequirementStatus(),
                RequirementStatusEnum.DRAFT.getCode()) && !Objects.equals(receipts.getRequirementStatus(),
                RequirementStatusEnum.REFUSE.getCode())) {
            throw new MipException("单据不为草稿或驳回状态，不能作废");
        }

        MilepostDesignPlanDetailChangeExample example = new MilepostDesignPlanDetailChangeExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectWbsReceiptsIdEqualTo(id)
                .andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        List<MilepostDesignPlanDetailChange> changeList = milepostDesignPlanDetailChangeService.selectByExample(example);
        if (ListUtils.isNotEmpty(changeList)) {
            List<Long> designPlanDetailIdList = changeList.stream().map(MilepostDesignPlanDetailChange::getDesignPlanDetailId)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            List<ProjectWbsReceiptsDto> receiptsDtoList = selectByDetailIds(designPlanDetailIdList);
            Map<Long, List<ProjectWbsReceiptsDto>> receiptsDtoMap = new HashMap<>(CollectionUtils.isEmpty(receiptsDtoList)
                    ? new HashMap<>() : receiptsDtoList.stream()
                    .filter(e -> Objects.equals(RequirementStatusEnum.DRAFT.getCode(), e.getRequirementStatus())
                            || Objects.equals(RequirementStatusEnum.TODO.getCode(), e.getRequirementStatus())
                            || Objects.equals(RequirementStatusEnum.PENDING.getCode(), e.getRequirementStatus()))
                    .collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getDesignPlanDetailId)));

            List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
            for (MilepostDesignPlanDetailChange change : changeList) {
                if (null != change.getDesignPlanDetailId() && !Boolean.FALSE.equals(change.getWbsLastLayer())) {
                    if (receiptsDtoMap.containsKey(change.getDesignPlanDetailId())) {
                        continue;
                    }

                    MilepostDesignPlanDetail milepostDesignPlanDetail = new MilepostDesignPlanDetail();
                    milepostDesignPlanDetail.setId(change.getDesignPlanDetailId());
                    milepostDesignPlanDetail.setStatus(CheckStatus.WBS_RECEIPTS_CANCEL.code());
                    List<ProjectWbsReceiptsDto> receiptsDtos =
                            projectWbsReceiptsExtMapper.selectProcessReceiptsByDetailIds(Collections.singletonList(change.getDesignPlanDetailId()));
                    if (ListUtils.isNotEmpty(receiptsDtos)) {
                        milepostDesignPlanDetail.setModuleStatus(MilepostDesignPlanDetailModelStatus.CONFIRMED.code());
                    } else {
                        milepostDesignPlanDetail.setModuleStatus(MilepostDesignPlanDetailModelStatus.UNCONFIRMED.code());
                    }
                    updateDesignPlanDetailList.add(milepostDesignPlanDetail);
                }
            }
            if (ListUtils.isNotEmpty(updateDesignPlanDetailList)) {
                milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);
            }
        }


        //逻辑删除单据上传的附件
        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
        ctcAttachmentDto.setModuleId(receipts.getId());
        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS.code());
        ctcAttachmentService.deleteByModule(ctcAttachmentDto);

        //单据从"草稿"状态变为"作废"状态
        receipts.setRequirementStatus(RequirementStatusEnum.CANCEL.getCode());

        return projectWbsReceiptsMapper.updateByPrimaryKeySelective(receipts) > 0;
    }

    @Override
    public String getRequirementCodeByReceiptsId(Long receiptsId) {
        return projectWbsReceiptsExtMapper.getRequirementCodeByReceiptsId(receiptsId);
    }

    @Override
    public List<ProjectWbsReceiptsDto> selectProcessReceiptsByDetailIds(List<Long> detailIds) {
        return projectWbsReceiptsExtMapper.selectProcessReceiptsByDetailIds(detailIds);
    }

    @Override
    public List<ProjectWbsReceiptsDto> selectByDetailIds(List<Long> detailIds) {
        return projectWbsReceiptsExtMapper.selectByDetailIds(detailIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importProjectWbsRequirementPublish(List<ImportProjectWbsRequirementPublishExcelVO> detailExcelVOList) {
        List<DictDto> dictDtoList = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Map<String, DictDto> unitMap = dictDtoList.stream().collect(Collectors.toMap(DictDto::getName, Function.identity()));
        Map<String, OrganizationRel> organizationMap = basedataExtService.queryCurrentUnitOrganization();

        List<String> projectCodeList = new ArrayList<>();
        List<String> pamCodeList = new ArrayList<>();
        List<String> wbsSummaryCodeList = new ArrayList<>();
        List<Date> deliveryTimeList = new ArrayList<>();
        List<Long> organizationIdList = new ArrayList<>();
        Map<String, Long> orgMap = new HashMap<>();
        for (ImportProjectWbsRequirementPublishExcelVO excelVO : detailExcelVOList) {
            String wbsSummaryCode = excelVO.getWbsSummaryCode();
            wbsSummaryCodeList.add(wbsSummaryCode);
            String pamCode = excelVO.getPamCode();
            pamCodeList.add(pamCode);
            Date deliveryTime = excelVO.getDeliveryTime();
            deliveryTimeList.add(deliveryTime);
            String organizationCode = excelVO.getOrganizationCode();
            OrganizationRel organizationRel = organizationMap.get(organizationCode);
            Long organizationId = organizationRel.getOrganizationId();
            if (Objects.nonNull(organizationId)) {
                organizationIdList.add(organizationId);
                orgMap.put(organizationCode, organizationId);
            }
            projectCodeList.add(excelVO.getProjectCode());
        }

        MilepostDesignPlanDetailExample planDetailExample = new MilepostDesignPlanDetailExample();
        planDetailExample.createCriteria().andPamCodeIn(pamCodeList).andWbsSummaryCodeIn(wbsSummaryCodeList).andDeliveryTimeIn(deliveryTimeList)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<MilepostDesignPlanDetail> designPlanDetailList = milepostDesignPlanDetailMapper.selectByExample(planDetailExample);
        Map<String, MilepostDesignPlanDetail> designPlanDetailMap = new HashMap<>();
        for (MilepostDesignPlanDetail detail : designPlanDetailList) {
            designPlanDetailMap.put(getPamCodeAndWbsSummaryCodeAndDeliveryTimeKey(detail.getPamCode(), detail.getWbsSummaryCode(),
                    detail.getDeliveryTime()), detail);
        }

        ProjectExample example = new ProjectExample();
        example.createCriteria().andCodeIn(projectCodeList);
        List<Project> projectList = projectService.selectByExample(example);
        Guard.notNullOrEmpty(projectList, "所有的项目号-新都不存在不存在");
        Map<String, Project> projectMap = new HashMap<>();
        for (Project project : projectList) {
            projectMap.put(project.getCode(), project);
        }

        List<Material> pamCodeMaterialList = materialExtService.invokeMaterialApiGetByPamCodeListAndOrgIdList(pamCodeList, organizationIdList);
        Map<String, Material> pamCodeMaterialMap = CollectionUtils.isEmpty(pamCodeMaterialList) ? new HashMap<>()
                : pamCodeMaterialList.stream().collect(Collectors.toMap(e -> getPamCodeMaterialKey(e.getPamCode(), e.getOrganizationId()),
                Function.identity()));

        Long userId = SystemContext.getUserId();
        UserInfo userInfo = CacheDataUtils.findUserById(userId);
        executeEachTreadExcelVosWBS(detailExcelVOList, unitMap, orgMap, projectMap, userInfo, designPlanDetailMap, pamCodeMaterialMap);

        /*List<Boolean> booleanList = new ArrayList<>();
        List<String> msgList = new ArrayList<>();
        int threadNum = detailExcelVOList.size() / EACH_THREAD_DATA_NUM + (detailExcelVOList.size() % EACH_THREAD_DATA_NUM == 0 ? 0 : 1);
        final CountDownLatch countDownLatch = new CountDownLatch(threadNum);
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * EACH_THREAD_DATA_NUM;
            int endIndex = i == threadNum ? detailExcelVOList.size() : i * EACH_THREAD_DATA_NUM;
            List<ImportProjectWbsRequirementPublishExcelVO> eachTreadExcelVos = detailExcelVOList.subList(startIndex, endIndex);
            importProjectWbsRequirementPublishExecutor.execute(() -> {
                try {
                    // 多线程异步执行主干导入方法
                    Boolean executeEachTreadResult = executeEachTreadExcelVos(eachTreadExcelVos, unitMap, orgMap, projectMap,
                            userInfo, designPlanDetailMap, pamCodeMaterialMap);
                    booleanList.add(executeEachTreadResult);
                } catch (ApplicationBizException e) {
                    msgList.add(e.getMessage());
                    booleanList.add(false);
                } catch (Exception e) {
                    logger.error("importProjectWbsRequirementPublish的executeEachTreadExcelVos操作记录失败", e);
                    booleanList.add(false);
                } finally {
                    countDownLatch.countDown();
                    if (Objects.equals(countDownLatch.getCount(), 0)) {
                        try {
                            Thread.sleep(200L);
                        } catch (InterruptedException e) {
                            logger.error("failed to sleep after importProjectWbsRequirementPublish executeEachTreadExcelVos", e);
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException ex) {
            logger.error("importProjectWbsRequirementPublish的executeEachTreadExcelVos的await失败", ex);
            Thread.currentThread().interrupt();
        }

        if (booleanList.contains(false)) {
            if (CollectionUtils.isEmpty(msgList)) {
                throw new ApplicationBizException("柔性项目需求发布初始化导入失败");
            } else {
                throw new ApplicationBizException(msgList.toString());
            }
        }*/
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeEachTreadExcelVosWBS(List<ImportProjectWbsRequirementPublishExcelVO> eachTreadExcelVos, Map<String, DictDto> unitMap,
                                            Map<String, Long> orgMap, Map<String, Project> projectMap,
                                            UserInfo userInfo, Map<String, MilepostDesignPlanDetail> designPlanDetailMap,
                                            Map<String, Material> pamCodeMaterialMap) {
        List<ProjectWbsReceipts> projectWbsReceiptsList = new ArrayList<>();
        Map<String, ProjectWbsReceipts> excelMap = new HashMap<>();
        for (ImportProjectWbsRequirementPublishExcelVO excelVo : eachTreadExcelVos) {
            String projectCode = excelVo.getProjectCode();
            String requirementCode = excelVo.getRequirementCode();
            String projectWbsReceiptsKey = getProjectWbsReceiptsKey(projectCode, requirementCode);
            if (!excelMap.containsKey(projectWbsReceiptsKey)) {
                ProjectWbsReceipts projectWbsReceipts = new ProjectWbsReceipts();
                projectWbsReceipts.setProducerId(userInfo.getId());
                projectWbsReceipts.setProducerName(userInfo.getName());
                projectWbsReceipts.setUnitId(SystemContext.getUnitId());
                projectWbsReceipts.setRequirementType(RequirementTypeEnum.REQUIREMENT_PUBLISH.getCode());
                projectWbsReceipts.setRequirementCode(requirementCode);
                projectWbsReceipts.setDesignReleaseLotNumber(excelVo.getDesignReleaseLotNumber());
                projectWbsReceipts.setRequirementStatus(RequirementStatusEnum.PROCESS.getCode());
                projectWbsReceipts.setRequirementType(RequirementTypeEnum.REQUIREMENT_PUBLISH.getCode());
                projectWbsReceipts.setDeletedFlag(false);
                Project project = projectMap.get(projectCode);
                Guard.notNull(project, String.format("序号：%s的项目号-新：%s不存在", excelVo.getSerialNumber(), projectCode));
                Long projectId = project.getId();
                projectWbsReceipts.setProjectId(projectId);
                projectWbsReceipts.setProjectCode(projectCode);
                projectWbsReceipts.setProjectName(project.getName());
                projectWbsReceipts.setInitSequence(excelVo.getInitSequence());
                projectWbsReceipts.setInit(true);
                excelMap.put(projectWbsReceiptsKey, projectWbsReceipts);
                projectWbsReceiptsList.add(projectWbsReceipts);
            }
        }
        Map<String, Long> projectWbsReceiptsMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(projectWbsReceiptsList)) {
            projectWbsReceiptsExtMapper.batchInsert(projectWbsReceiptsList);
            projectWbsReceiptsMap.putAll(projectWbsReceiptsList.stream().collect(Collectors.toMap(e ->
                    getProjectWbsReceiptsKey(e.getProjectCode(), e.getRequirementCode()), ProjectWbsReceipts::getId)));
        }

        List<ProjectWbsReceiptsDesignPlanRel> designPlanRelList = new ArrayList<>();
        List<PurchaseMaterialRequirement> requirementList = new ArrayList<>();
        for (ImportProjectWbsRequirementPublishExcelVO excelVo : eachTreadExcelVos) {
            Integer serialNumber = excelVo.getSerialNumber();
            // 组织代码
            String organizationCode = excelVo.getOrganizationCode();
            Long organizationId = orgMap.get(organizationCode);
            Guard.notNull(organizationId, String.format("序号：%s的组织代码：%s在当前单位下不存在", serialNumber, organizationCode));
            // 项目号-新
            String projectCode = excelVo.getProjectCode();
            Project project = projectMap.get(projectCode);
            Guard.notNull(project, String.format("序号：%s的项目号-新：%s不存在", serialNumber, projectCode));
            Long projectId = project.getId();
            // WBS号
            String wbsSummaryCode = excelVo.getWbsSummaryCode();
            Guard.isTrue(wbsSummaryCode.startsWith(projectCode), String.format("序号：%s的WBS号：%s对应的项目号-新：%s不正确",
                    serialNumber, wbsSummaryCode, projectCode));
            // PAM编码-新
            String pamCode = excelVo.getPamCode();
            MilepostDesignPlanDetail designPlanDetail = designPlanDetailMap.get(getPamCodeAndWbsSummaryCodeAndDeliveryTimeKey(pamCode,
                    wbsSummaryCode, excelVo.getDeliveryTime()));

            Material material = pamCodeMaterialMap.get(getPamCodeMaterialKey(pamCode, organizationId));
            Guard.notNull(material, String.format("序号：%s的PAM编码-新：%s不存在", serialNumber, pamCode));
            // 单位-新
            String unit = excelVo.getUnit();
            DictDto dictDto = unitMap.get(unit);
            Guard.notNull(dictDto, String.format("序号：%s的单位-新：%s不存在", serialNumber, unit));

            // 初始化序列
            String initSequence = excelVo.getInitSequence();
            // 需求发布单据编号
            String requirementCode = excelVo.getRequirementCode();

            BigDecimal purchaseNum = Objects.isNull(designPlanDetail) ? null
                    : designPlanDetail.getNumber().multiply(milepostDesignPlanDetailService.getParentSigmaById(designPlanDetail.getParentId(), null));
            ProjectWbsReceiptsDesignPlanRel designPlanRel = new ProjectWbsReceiptsDesignPlanRel();
            designPlanRel.setProjectId(projectId);
            designPlanRel.setProjectWbsReceiptsId(projectWbsReceiptsMap.get(getProjectWbsReceiptsKey(projectCode, requirementCode)));
            designPlanRel.setDesignPlanDetailId(Optional.ofNullable(designPlanDetail).map(MilepostDesignPlanDetail::getId).orElse(null));
            designPlanRel.setPurchaseNum(purchaseNum);
            designPlanRel.setInit(true);
            designPlanRel.setInitSequence(initSequence);
            designPlanRel.setDeletedFlag(false);
            designPlanRelList.add(designPlanRel);

            PurchaseMaterialRequirement requirement = new PurchaseMaterialRequirement();
            requirement.setProjectId(projectId);
            requirement.setPamCode(pamCode);
            requirement.setErpCode(excelVo.getErpCode());
            requirement.setDeliveryTime(excelVo.getDeliveryTime());
            requirement.setMaterielId(material.getId());
            requirement.setMaterielDescr(material.getItemInfo());
            requirement.setUnit(unit);
            requirement.setUnitCode(dictDto.getCode());
            requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
            requirement.setNeedTotal(excelVo.getUnreleasedAmount());
            requirement.setPurchaseType(Boolean.TRUE.equals(excelVo.getExtIs()) ? PurchaseMaterialRequirementPurchaseTypeEnums.OUTSOURCE.getCode()
                    : PurchaseMaterialRequirementPurchaseTypeEnums.WBS.getCode());
            requirement.setProjectWbsReceiptsId(projectWbsReceiptsMap.get(getProjectWbsReceiptsKey(projectCode, requirementCode)));
            requirement.setRequirementCode(excelVo.getRequirementCode());
            requirement.setWbsSummaryCode(wbsSummaryCode);
            requirement.setDispatchIs(excelVo.getDispatchIs());
            requirement.setReceiptsPublishDate(excelVo.getPublishTime());
            requirement.setDesignReleaseLotNumber(excelVo.getDesignReleaseLotNumber());
            requirement.setChartVersion(excelVo.getChartVersion());
            requirement.setActivityCode(excelVo.getActivityCode());
            requirement.setInit(true);
            requirement.setInitSequence(initSequence);
            requirement.setDeletedFlag(false);
            requirement.setRequirementType(1);
            requirementList.add(requirement);
        }
        if (!CollectionUtils.isEmpty(designPlanRelList)) {
            projectWbsReceiptsDesignPlanRelExtMapper.batchInsert(designPlanRelList);
        }
        if (!CollectionUtils.isEmpty(requirementList)) {
            purchaseMaterialRequirementExtMapper.batchInsert(requirementList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importProjectRequirementPublish(List<ImportProjectRequirementPublishExcelVO> detailExcelVOList) {
        List<DictDto> dictDtoList = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null);
        Map<String, DictDto> unitMap = dictDtoList.stream().collect(Collectors.toMap(DictDto::getName, Function.identity()));
        Map<String, OrganizationRel> organizationMap = basedataExtService.queryCurrentUnitOrganization();

        List<String> projectCodeList = new ArrayList<>();
        List<String> pamCodeList = new ArrayList<>();
        List<Date> deliveryTimeList = new ArrayList<>();
        List<Long> organizationIdList = new ArrayList<>();
        Map<String, Long> orgMap = new HashMap<>();
        for (ImportProjectRequirementPublishExcelVO excelVO : detailExcelVOList) {
            String pamCode = excelVO.getPamCode();
            pamCodeList.add(pamCode);
            Date deliveryTime = excelVO.getDeliveryTime();
            deliveryTimeList.add(deliveryTime);
            String organizationCode = excelVO.getOrganizationCode();
            OrganizationRel organizationRel = organizationMap.get(organizationCode);
            Long organizationId = organizationRel.getOrganizationId();
            if (Objects.nonNull(organizationId)) {
                organizationIdList.add(organizationId);
                orgMap.put(organizationCode, organizationId);
            }
            projectCodeList.add(excelVO.getProjectCode());
        }

        ProjectExample example = new ProjectExample();
        example.createCriteria().andCodeIn(projectCodeList);
        List<Project> projectList = projectService.selectByExample(example);
        Guard.notNullOrEmpty(projectList, "所有的项目号-新都不存在不存在");
        Map<String, Project> projectMap = new HashMap<>();
        for (Project project : projectList) {
            projectMap.put(project.getCode(), project);
        }

        List<Material> pamCodeMaterialList = materialExtService.invokeMaterialApiGetByPamCodeListAndOrgIdList(pamCodeList, organizationIdList);
        Map<String, Material> pamCodeMaterialMap = CollectionUtils.isEmpty(pamCodeMaterialList) ? new HashMap<>()
                : pamCodeMaterialList.stream().collect(Collectors.toMap(e -> getPamCodeMaterialKey(e.getPamCode(), e.getOrganizationId()),
                Function.identity()));

        executeEachTreadExcelVos(detailExcelVOList, unitMap, orgMap, projectMap, pamCodeMaterialMap);
    }

    @Override
    public ProjectWbsReceipts getByRequirementCode(String requirementCode) {
        ProjectWbsReceiptsExample example = new ProjectWbsReceiptsExample();
        example.createCriteria().andRequirementCodeEqualTo(requirementCode).andDeletedFlagEqualTo(false);
        List<ProjectWbsReceipts> projectWbsReceiptsList = projectWbsReceiptsMapper.selectByExampleWithBLOBs(example);
        return CollectionUtils.isEmpty(projectWbsReceiptsList) ? null : projectWbsReceiptsList.get(0);
    }

    @Override
    public List<String> getProjectWbsReceiptsBudgetErrorCode(Long startId, Long endId) {
        ProjectWbsReceiptsExample example = new ProjectWbsReceiptsExample();
        example.createCriteria().andDeletedFlagEqualTo(false).andRequirementTypeEqualTo(RequirementTypeEnum.REQUIREMENT_PUBLISH.getCode())
                .andRequirementStatusNotEqualTo(RequirementStatusEnum.CANCEL.getCode()).andIdGreaterThanOrEqualTo(startId).andIdLessThanOrEqualTo(endId);
        List<ProjectWbsReceipts> projectWbsReceiptsList = projectWbsReceiptsMapper.selectByExample(example);

        int threadNum = projectWbsReceiptsList.size() / 100 + (projectWbsReceiptsList.size() % 100 == 0 ? 0 : 1);
        List<String> requirementCodeList = new ArrayList<>();
        for (int i = 1; i <= threadNum; i++) {
            int startIndex = (i - 1) * 100;
            int endIndex = i == threadNum ? projectWbsReceiptsList.size() : i * 100;
            List<ProjectWbsReceipts> eachTreadExcelVos = projectWbsReceiptsList.subList(startIndex, endIndex);
            executeEachTreadExcelVo(eachTreadExcelVos, requirementCodeList);
        }

        return requirementCodeList;
    }

    @Override
    public List<ProjectWbsReceiptsDto> getByIdList(List<Long> idList) {
        ProjectWbsReceiptsExample example = new ProjectWbsReceiptsExample();
        example.createCriteria().andIdIn(idList).andDeletedFlagEqualTo(false);
        List<ProjectWbsReceipts> projectWbsReceiptsList = projectWbsReceiptsMapper.selectByExampleWithBLOBs(example);
        return BeanConverter.copy(projectWbsReceiptsList, ProjectWbsReceiptsDto.class);
    }

    @Override
    public List<ProjectWbsReceiptsDto> getByRequirementCodeFuzzy(String fuzzyLike) {
        //只返回99条，防止以后数据量过大
        PageHelper.startPage(1, 99);
        ProjectWbsReceiptsExample example = new ProjectWbsReceiptsExample();
        example.createCriteria().andRequirementCodeLike("%" + fuzzyLike + "%")
                .andRequirementTypeIn(Arrays.asList(RequirementTypeEnum.DESIGN_PLAN_CHANGE.getCode(),
                        RequirementTypeEnum.REQUIREMENT_PUBLISH.getCode()))
                .andUnitIdEqualTo(SystemContext.getUnitId())
                .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<ProjectWbsReceipts> projectWbsReceiptsList = projectWbsReceiptsMapper.selectByExample(example);
        return BeanConverter.copy(projectWbsReceiptsList, ProjectWbsReceiptsDto.class);
    }

    @Override
    public void changeMilepostDesignPlanDetailStatusReceiptsPassed(String milepostDesignPlanDetailIdStr) {
        Guard.notNullOrEmpty(milepostDesignPlanDetailIdStr, "详细设计id列表为空");
        //将以','分隔的milepostDesignPlanDetailIdStr转换为List<Long>
        List<Long> milepostDesignPlanDetailIdList =
                Arrays.stream(milepostDesignPlanDetailIdStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<MilepostDesignPlanDetail> updateDesignPlanDetailList = new ArrayList<>();
        for (Long milepostDesignPlanDetailId : milepostDesignPlanDetailIdList) {
            MilepostDesignPlanDetail milepostDesignPlanDetail = new MilepostDesignPlanDetail();
            milepostDesignPlanDetail.setId(milepostDesignPlanDetailId);
            milepostDesignPlanDetail.setStatus(CheckStatus.WBS_RECEIPTS_PASSED.code());
            updateDesignPlanDetailList.add(milepostDesignPlanDetail);
        }
        logger.info("批量修改milepost_design_plan_detail表的status为1004的数据为：{}", updateDesignPlanDetailList);
        milepostDesignPlanDetailExtMapper.batchUpdate(updateDesignPlanDetailList);

    }

    @Override
    public void deleteById(Long id) {
        Guard.notNull(id, "id为空");

        ProjectWbsReceipts projectWbsReceipts = new ProjectWbsReceipts();
        projectWbsReceipts.setId(id);
        projectWbsReceipts.setDeletedFlag(Boolean.TRUE);
        projectWbsReceiptsMapper.updateByPrimaryKeySelective(projectWbsReceipts);
    }

    private void executeEachTreadExcelVo(List<ProjectWbsReceipts> eachTreadExcelVos, List<String> requirementCodeList) {
        List<Long> projectWbsReceiptsIdList = eachTreadExcelVos.stream().map(ProjectWbsReceipts::getId).collect(Collectors.toList());
        ProjectWbsReceiptsDesignPlanRelExample relationExample = new ProjectWbsReceiptsDesignPlanRelExample();
        relationExample.createCriteria().andProjectWbsReceiptsIdIn(projectWbsReceiptsIdList);
        List<ProjectWbsReceiptsDesignPlanRel> relations = ProjectWbsReceiptsDesignPlanRelMapper.selectByExample(relationExample);
        Map<Long, List<Long>> relationMap = new HashMap<>();
        List<Long> designPlanDetailIdList = new ArrayList<>();
        for (ProjectWbsReceiptsDesignPlanRel designPlanRel : relations) {
            Long projectWbsReceiptsId = designPlanRel.getProjectWbsReceiptsId();
            Long designPlanDetailId = designPlanRel.getDesignPlanDetailId();
            if (relationMap.containsKey(projectWbsReceiptsId)) {
                relationMap.get(projectWbsReceiptsId).add(designPlanDetailId);
            } else {
                relationMap.put(projectWbsReceiptsId, Lists.newArrayList(designPlanDetailId));
            }
            designPlanDetailIdList.add(designPlanDetailId);
        }
        Map<Long, List<MilepostDesignPlanDetail>> designPlanDetailMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(designPlanDetailIdList)) {
            //查询详设提交数据以及所有上级数据
            MilepostDesignPlanDetailExample planDetailExample = new MilepostDesignPlanDetailExample();
            planDetailExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andIdIn(designPlanDetailIdList);
            List<MilepostDesignPlanDetail> planDetailList = milepostDesignPlanDetailMapper.selectByExample(planDetailExample);
            relationMap.forEach((k, v) -> {
                List<MilepostDesignPlanDetail> designPlanDetailList =
                        planDetailList.stream().filter(e -> v.contains(e.getId())).collect(Collectors.toList());
                designPlanDetailMap.put(k, designPlanDetailList);
            });
        }

        ProjectWbsReceiptsBudgetExample example = new ProjectWbsReceiptsBudgetExample();
        example.createCriteria().andProjectWbsReceiptsIdIn(projectWbsReceiptsIdList).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<ProjectWbsReceiptsBudget> budgetList = projectWbsReceiptsBudgetMapper.selectByExample(example);
        Map<Long, List<ProjectWbsReceiptsBudget>> budgetChangeHistoryMap = budgetList.stream()
                .collect(Collectors.groupingBy(ProjectWbsReceiptsBudget::getProjectWbsReceiptsId));
        Map<Long, Set<Integer>> demandTypeMap = new HashMap<>();
        for (Long projectWbsReceiptsId : budgetChangeHistoryMap.keySet()) {
            List<ProjectWbsReceiptsBudget> receiptsBudgetList = budgetChangeHistoryMap.getOrDefault(projectWbsReceiptsId, new ArrayList<>());
            Set<Integer> demandTypeSet = new HashSet<>();
            for (ProjectWbsReceiptsBudget receiptsBudget : receiptsBudgetList) {
                Integer demandType = receiptsBudget.getDemandType();
                if (Objects.equals(demandType, ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode())
                        || Objects.equals(demandType, ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode())) {
                    demandTypeSet.add(demandType);
                }
            }
            demandTypeMap.put(projectWbsReceiptsId, demandTypeSet);
        }

        for (ProjectWbsReceipts receipts : eachTreadExcelVos) {
            Long id = receipts.getId();
            Set<Integer> demandTypeSet = demandTypeMap.getOrDefault(id, new HashSet<>());
            List<MilepostDesignPlanDetail> detailList = designPlanDetailMap.getOrDefault(id, new ArrayList<>());
            if (CollectionUtils.isEmpty(detailList) || detailList.stream().noneMatch(e -> Objects.equals(e.getMaterialCategory(), "外购物料"))) {
                continue;
            }

            Set<Boolean> extIsSet = detailList.stream().map(MilepostDesignPlanDetail::getExtIs).filter(Objects::nonNull).collect(Collectors.toSet());
            if (extIsSet.size() != demandTypeSet.size()) {
                requirementCodeList.add(receipts.getRequirementCode());
            } else if (extIsSet.size() == 1) {
                if ((extIsSet.contains(true) && demandTypeSet.contains(ProjectWbsReceiptsBudgetDemandTypeEnums.DEMAND.getCode()))
                        || (extIsSet.contains(false) && demandTypeSet.contains(ProjectWbsReceiptsBudgetDemandTypeEnums.OUTSOURCE.getCode()))) {
                    requirementCodeList.add(receipts.getRequirementCode());
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeEachTreadExcelVos(List<ImportProjectRequirementPublishExcelVO> eachTreadExcelVos, Map<String, DictDto> unitMap,
                                         Map<String, Long> orgMap, Map<String, Project> projectMap, Map<String, Material> pamCodeMaterialMap) {
        List<PurchaseMaterialRequirement> requirementList = new ArrayList<>();
        for (ImportProjectRequirementPublishExcelVO excelVo : eachTreadExcelVos) {
            Integer serialNumber = excelVo.getSerialNumber();
            // 组织代码
            String organizationCode = excelVo.getOrganizationCode();
            Long organizationId = orgMap.get(organizationCode);
            Guard.notNull(organizationId, String.format("序号：%s的组织代码：%s在当前单位下不存在", serialNumber, organizationCode));
            // 项目号-新
            String projectCode = excelVo.getProjectCode();
            Project project = projectMap.get(projectCode);
            Guard.notNull(project, String.format("序号：%s的项目号-新：%s不存在", serialNumber, projectCode));
            Long projectId = project.getId();
            // PAM编码-新
            String pamCode = excelVo.getPamCode();
            Material material = pamCodeMaterialMap.get(getPamCodeMaterialKey(pamCode, organizationId));
            Guard.notNull(material, String.format("序号：%s的PAM编码-新：%s不存在", serialNumber, pamCode));
            // 单位-新
            String unit = excelVo.getUnit();
            DictDto dictDto = unitMap.get(unit);
            Guard.notNull(dictDto, String.format("序号：%s的单位-新：%s不存在", serialNumber, unit));
            // 检验：同一个ERP编码+项目编码，不会同时存在2条以上的采购需求
            PurchaseMaterialRequirementExample checkExample = new PurchaseMaterialRequirementExample();
            checkExample.createCriteria().andProjectIdEqualTo(projectId)
                    .andErpCodeEqualTo(excelVo.getErpCode())
                    .andDeletedFlagEqualTo(DeletedFlag.VALID.code());
            long count = purchaseMaterialRequirementMapper.countByExample(checkExample);
            Guard.isTrue(count == 0, String.format("序号：%s，同一个ERP编码+项目编码，采购需求已存在，不能重复导入", serialNumber, unit));

            PurchaseMaterialRequirement requirement = new PurchaseMaterialRequirement();
            requirement.setProjectId(projectId);
            requirement.setPamCode(pamCode);
            requirement.setErpCode(excelVo.getErpCode());
            requirement.setDeliveryTime(excelVo.getDeliveryTime());
            requirement.setMaterielId(material.getId());
            requirement.setMaterielDescr(material.getItemInfo());
            requirement.setUnit(unit);
            requirement.setUnitCode(dictDto.getCode());
            requirement.setStatus(PurchaseMaterialRequirementStatus.WAIT_ORDER.code());
            requirement.setNeedTotal(excelVo.getUnreleasedAmount());
            requirement.setPurchaseType(excelVo.getPurchaseType());
            requirement.setChartVersion(excelVo.getChartVersion());
            requirement.setInit(true);
            requirement.setInitSequence(excelVo.getInitSequence());
            requirement.setDeletedFlag(false);
            requirement.setRequirementType(1);
            requirementList.add(requirement);
        }

        if (!CollectionUtils.isEmpty(requirementList)) {
            purchaseMaterialRequirementExtMapper.batchInsert(requirementList);
        }
    }

    private String getProjectWbsReceiptsKey(String projectCode, String requirementCode) {
        return String.format("getProjectWbsReceiptsKey_%s_%s", projectCode, requirementCode);
    }

    private String getPamCodeAndWbsSummaryCodeAndDeliveryTimeKey(String pamCode, String wbsSummaryCode, Date deliveryTime) {
        return String.format("getPamCodeAndWbsSummaryCodeKey_%s_%s_%s", pamCode, wbsSummaryCode, DateUtils.format(deliveryTime));
    }

    private String getPamCodeMaterialKey(String pamCode, Long organizationId) {
        return String.format("getMaterialKey_%s_%s", pamCode, organizationId);
    }

    @Override
    public ResponseMap getRequirementPublishApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<AduitAtta> fileList = new ArrayList<>();
        List<Map<String, String>> detailMapList1 = new ArrayList<>();

        ProjectWbsReceiptsDto dto = this.findRequirementDetail(id);
        if (Objects.nonNull(dto)) {
            //详设信息
            List<MilepostDesignPlanDetailDto> designPlanDetailList = this.getAllChild(dto.getDesignPlanDetailDtos());
            //基本信息
            headMap.put("requirementCode", dto.getRequirementCode()); //单据编号
            headMap.put("requirementType", RequirementTypeEnum.REQUIREMENT_PUBLISH.getName()); //单据类型
            headMap.put("band", designPlanDetailList.stream().map(s -> s.getBrand()).filter(StringUtils::hasText).findFirst().orElse("")); //品牌
            headMap.put("projectCode", dto.getProjectCode()); //项目编号
            headMap.put("projectName", dto.getProjectName()); //项目名称
            headMap.put("producerName", dto.getProducerName()); //制单人
            headMap.put("handleName", dto.getHandleName()); //处理人
            headMap.put("confirmMode", ReceiptsConfirmModeEnum.getNameByCode(dto.getConfirmMode())); //确认方式
            headMap.put("detail", "详见附件\"需求发布明细\""); //发布明细内容
            headMap.put("remark", dto.getRemark());  //备注
            //预算信息
            List<ProjectWbsReceiptsBudgetDto> budgetList = dto.getBudgetList();
            if (ListUtil.isPresent(budgetList)) {
                for (ProjectWbsReceiptsBudgetDto budgetDto : budgetList) {
                    if (ListUtil.isPresent(budgetDto.getChilds())) {
                        for (ProjectWbsReceiptsBudgetDto childBudgetDto : budgetDto.getChilds()) {
                            Map<String, String> budgetMap = new HashMap<>();
                            budgetMap.put("wbsSummaryCode", childBudgetDto.getWbsSummaryCode()); //WBS
                            budgetMap.put("demandType", ReceiptsBudgetDemandTypeEnum.getNameByCode(childBudgetDto.getDemandType())); //需求分类
                            budgetMap.put("budgetOccupiedAmount", null == childBudgetDto.getBudgetOccupiedAmount() ? "" :
                                    childBudgetDto.getBudgetOccupiedAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); //预算占用金额
                            detailMapList1.add(budgetMap);
                        }
                    }
                }
            }
        } else {
            responseMap.setMsg("头信息为空！");
            responseMap.setStatus("fail");
        }

        //需求发布明细-移动端附件
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(CtcAttachmentModule.REQUIREMENT_PUBLISH_DELIVERY_DETAIL.code());
        attachmentQuery.setModuleId(id);
        List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
        if (ListUtil.isPresent(attachmentDtos)) {
            CtcAttachmentDto attachmentDto = attachmentDtos.get(0);//驳回/撤回会重新生成，取最新的一个即可
            AduitAtta aduitAtta = new AduitAtta();
            aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" : String.valueOf(attachmentDto.getAttachId()));
            aduitAtta.setFileName(Optional.ofNullable(attachmentDto.getFileName()).orElse(attachmentDto.getAttachName()));
            aduitAtta.setFileSize(attachmentDto.getFileSize() == null ? "" : String.valueOf(attachmentDto.getFileSize()));
            fileList.add(aduitAtta);
        }
        //需求发布时上传的附件
        attachmentDtos = dto.getAttachmentList();
        if (ListUtil.isPresent(attachmentDtos)) {
            for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" : String.valueOf(attachmentDto.getAttachId()));
                aduitAtta.setFileName(Optional.ofNullable(attachmentDto.getFileName()).orElse(attachmentDto.getAttachName()));
                aduitAtta.setFileSize(attachmentDto.getFileSize() == null ? "" : String.valueOf(attachmentDto.getFileSize()));
                fileList.add(aduitAtta);
            }
        }

        responseMap.setMsg("成功");
        responseMap.setStatus("success");
        responseMap.setHeadMap(headMap);
        responseMap.setList1(detailMapList1);
        responseMap.setFileList(fileList);
        return responseMap;
    }

    /**
     * 传入详设列表，将详设列表及其下所有子节点封到原列表中
     *
     * @param list
     * @return
     */
    public List<MilepostDesignPlanDetailDto> getAllChild(List<MilepostDesignPlanDetailDto> list) {
        if (!CollectionUtils.isEmpty(list)) {
            for (int i = list.size() - 1; i >= 0; i--) {
                MilepostDesignPlanDetailDto dto = list.get(i);
                if (!CollectionUtils.isEmpty(dto.getSonDtos())) {
                    list.addAll(getAllChild(dto.getSonDtos()));
                }
                dto.setSonDtos(null);
            }
        }
        return list;
    }

    /**
     * 根据详细设计单据，获取详细设计
     *
     * @param receipts
     * @return
     */
    @Override
    public List<MilepostDesignPlanDetailChange> getDesignPlanDetailForChange(ProjectWbsReceipts receipts) {
        MilepostDesignPlanDetailChangeExample example = new MilepostDesignPlanDetailChangeExample();
        example.createCriteria().andProjectWbsReceiptsIdEqualTo(receipts.getId()).andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());

        return milepostDesignPlanDetailChangeService.selectByExample(example);
    }

    @Override
    public void updateRequirementStatus(Long id, Integer requirementStatus) {
        if (id == null) {
            return;
        }
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(id);
        Guard.notNull(receipts, String.format("单据id：%s对应的单据不存在", id));

        ProjectWbsReceipts projectWbsReceipts = new ProjectWbsReceipts();
        projectWbsReceipts.setId(id);
        projectWbsReceipts.setRequirementStatus(requirementStatus);
        projectWbsReceiptsMapper.updateByPrimaryKeySelective(projectWbsReceipts);
    }

    @Override
    public List<ProjectWbsReceipts> queryDesignInfoByProjectId(Long projectId) {
        return projectWbsReceiptsExtMapper.queryDesignInfoByProjectId(projectId);
    }

    @Override
    public String queryRequirementCodeByDesignId(Long designId) {
        return projectWbsReceiptsExtMapper.queryRequirementCodeByDesignId(designId);
    }

    @Transactional
    @Override
    public ProjectWbsReceiptsDto saveWbsChangeReceipts(ProjectWbsReceiptsDto dto, Long userId) {
        Guard.notNull(dto, "详设单据不能为空");
        dto.setUnitId(SystemContext.getUnitId());
        if (dto.getId() != null || StringUtils.isNotEmpty(dto.getRequirementCode())) {
            if (dto.getId() != null) {
                ProjectWbsReceipts projectWbsReceipts = getById(dto.getId());
                if (StringUtils.isEmpty(projectWbsReceipts.getRequirementCode())) {
                    dto.setRequirementCode(generateRequirementCode());
                }
            } else {
                ProjectWbsReceipts receipts = getByRequirementCode(dto.getRequirementCode());
                if (null != receipts) {
                    dto.setId(receipts.getId());
                }
            }
        } else {
            dto.setRequirementCode(generateRequirementCode());
        }
        dto.setRequirementStatus(RequirementStatusEnum.TODO.getCode());
        dto.setRequirementType(RequirementTypeEnum.WBS_CHANGE.getCode());
        dto.setWebType(0);
        ProjectWbsReceiptsDto resultDto = save(dto, userId);
        Long projectWbsReceiptsId = resultDto.getId();
        //关联附件
//        CtcAttachmentDto ctcAttachmentDto = new CtcAttachmentDto();
//        ctcAttachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS_WBS_BUDGET_CHANGE.code());
//        ctcAttachmentDto.setModuleId(dto.getId());
//        ctcAttachmentService.deleteByModule(ctcAttachmentDto);
        //保存附件信息
        if (ListUtil.isPresent(dto.getAttachmentList())) {
            for (CtcAttachmentDto attachmentDto : dto.getAttachmentList()) {
                if (attachmentDto.getId() == null) {
                    attachmentDto.setModule(CtcAttachmentModule.PROJECT_WBS_RECEIPTS_WBS_BUDGET_CHANGE.code());
                    attachmentDto.setModuleId(resultDto.getId());
                }
                ctcAttachmentService.save(attachmentDto, userId);
            }
        }
        projectWbsChangeReceiptDetailExtMapper.deleteByProjectWbsReceiptsId(dto.getId());
        List<ProjectWbsChangeReceiptDetail> projectWbsChangeReceiptDetailList = dto.getProjectWbsChangeReceiptDetailList();
        Guard.notNullOrEmpty(projectWbsChangeReceiptDetailList, "wbs变更单明细行不能为空");
        projectWbsChangeReceiptDetailList.forEach(e -> {
            e.setProjectWbsReceiptsId(projectWbsReceiptsId);
            e.setProjectWbsBudgetId(e.getId());
            e.setProjectId(resultDto.getProjectId());
            e.setCreateBy(userId);
            e.setUpdateAt(null);
            e.setUpdateBy(null);
            e.setDeletedFlag(Boolean.FALSE);
            e.setVersion(1L);
            e.setId(null);
        });
        projectWbsChangeReceiptDetailExtMapper.batchInsert(projectWbsChangeReceiptDetailList);

        // 邮件提醒
        sendEmailForProjectWbsReceiptTodo(resultDto, resultDto.getHandleBy(), NoticeBusinessType.PROJECT_WBS_RECEIPTS_TODO);

        return findWbsChangeDetail(projectWbsReceiptsId);
    }

    @Override
    public ProjectWbsReceiptsDto findWbsChangeDetail(Long id) {
        Guard.notNull(id, "详设单据id不能为空");
        // 根据ID查询详设单据
        ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsMapper.selectByPrimaryKey(id);
        Guard.notNull(projectWbsReceipts, String.format("Id:%s对应详设单据不存在", id));
        // 转换为DTO对象
        ProjectWbsReceiptsDto dto = BeanConverter.copy(projectWbsReceipts, ProjectWbsReceiptsDto.class);
        // 查询关联的wbs变更单明细行
        List<ProjectWbsChangeReceiptDetail> detailList = projectWbsChangeReceiptDetailExtMapper.queryByProjectWbsReceiptsId(id);
        // 处理动态列
        List<Map<String, Object>> detailMapList = new ArrayList<>();
        for (ProjectWbsChangeReceiptDetail detail : detailList) {
            Map<String, Object> detailMap = BeanMapTool.beanToMap(detail);
            if (StringUtils.isNotEmpty(detail.getDynamicFields()) && StringUtils.isNotEmpty(detail.getDynamicValues())) {
                String[] dynamicFieldArr = detail.getDynamicFields().split(",");
                String[] dynamicValueArr = detail.getDynamicValues().split(",");
                for (int i = 0; i < dynamicFieldArr.length; i++) {
                    detailMap.put(dynamicFieldArr[i], dynamicValueArr[i]);
                }
            }
            if (Objects.equals(detail.getTag(), "New")) {
                detailMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_REMAINING_COST, BigDecimal.ZERO);
                detailMap.put(WbsBudgetFieldConstant.CHANGE_PRICE, BigDecimal.ZERO);
                detailMap.put(WbsBudgetFieldConstant.AFTER_CHANGE_CHANGE_ACCUMULATE_COST, BigDecimal.ZERO);
            }
            detailMapList.add(detailMap);
        }
        dto.setProjectWbsChangeReceiptDetailMapList(detailMapList);
        // 构建查询条件，获取项目历史变更头
        ProjectHistoryHeaderExample example = new ProjectHistoryHeaderExample();
        ProjectHistoryHeaderExample.Criteria criteria = example.createCriteria();
        // 设置查询条件：项目ID、变更类型为WBS预算、关联的WBS收款单ID、有效
        criteria.andProjectIdEqualTo(projectWbsReceipts.getProjectId())
                .andChangeTypeEqualTo(CtcProjectChangeType.WBS_BUDGET.getCode())
                .andProjectWbsReceiptsIdEqualTo(id)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        // 按创建时间降序排序
        example.setOrderByClause("create_at desc");
        // 执行查询
        List<ProjectHistoryHeader> projectHistoryHeaders = projectHistoryHeaderMapper.selectByExample(example);
        // 如果查询到历史记录
        if (ListUtils.isNotEmpty(projectHistoryHeaders)) {
            // 转换为DTO
            List<ProjectHistoryHeaderDto> projectHistoryHeaderDtos = BeanConverter.copy(projectHistoryHeaders, ProjectHistoryHeaderDto.class);
            // 获取EMS推送状态
            projectService.getEmsPushStatus(projectHistoryHeaderDtos);
            dto.setProjectHistoryHeaderList(projectHistoryHeaderDtos);
        }
        //查询附件信息
        List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(id, CtcAttachmentModule.PROJECT_WBS_RECEIPTS_WBS_BUDGET_CHANGE.code(), null);
        dto.setAttachmentList(attachmentDtos);
        // 返回完整的DTO对象
        return dto;
    }

    @Override
    public Boolean commonReject(Long id) {
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);
        Project project = projectService.selectByPrimaryKey(receipts.getProjectId());
        Asserts.notEmpty(project, ErrorCode.CTC_PROJECT_NOT_FIND);

        Long userId = SystemContext.getUserId();
        if (!Objects.equals(project.getManagerId(), userId) && !Objects.equals(receipts.getHandleBy(), userId) && !Objects.equals(receipts.getProducerId(), userId)) {
            throw new BizException(ErrorCode.ERROR, "当前用户不是单据指定的处理人或制单人或项目经理，不能操作撤回");
        }
        Integer requirementStatus = receipts.getRequirementStatus();
        if (!Objects.equals(requirementStatus, RequirementStatusEnum.TODO.getCode()) && !Objects.equals(requirementStatus,
                RequirementStatusEnum.REFUSE.getCode())) {
            throw new BizException(ErrorCode.ERROR, "单据不为待处理或驳回状态，不能操作撤回");
        }

        ProjectHistoryHeaderExample headerExample = new ProjectHistoryHeaderExample();
        headerExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andProjectWbsReceiptsIdEqualTo(id);
        long count = projectHistoryHeaderMapper.countByExample(headerExample);
        if (count > 0) {
            throw new BizException(ErrorCode.ERROR, "已提交预算变更流程，不允许撤回");
        }

        //单据从"待处理"状态变为"草稿"状态
        receipts.setRequirementStatus(RequirementStatusEnum.DRAFT.getCode());

        return projectWbsReceiptsMapper.updateByPrimaryKeySelective(receipts) > 0;
    }

    @Override
    public Boolean commonCancel(Long id) {
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(id);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);
        Long managerId = getManagerIdFromProjectId(receipts.getProjectId());
        if (!Objects.equals(receipts.getProducerId(), SystemContext.getUserId()) && !Objects.equals(managerId, SystemContext.getUserId())) {
            throw new BizException(ErrorCode.ERROR, "当前用户不是单据的制单人或该项目的项目经理，不能操作作废");
        }
        if (!Objects.equals(receipts.getRequirementStatus(),
                RequirementStatusEnum.DRAFT.getCode()) && !Objects.equals(receipts.getRequirementStatus(),
                RequirementStatusEnum.REFUSE.getCode())) {
            throw new BizException(ErrorCode.ERROR, "单据不为草稿或驳回状态，不能作废");
        }

        //单据从"草稿"状态变为"作废"状态
        receipts.setRequirementStatus(RequirementStatusEnum.CANCEL.getCode());

        return projectWbsReceiptsMapper.updateByPrimaryKeySelective(receipts) > 0;
    }

    @Override
    public ProjectDto buildTemplateContent(Long projectWbsReceiptsId, String tag) {
        ProjectWbsReceipts receipts = projectWbsReceiptsMapper.selectByPrimaryKey(projectWbsReceiptsId);
        Asserts.notEmpty(receipts, ErrorCode.CTC_PROJECT_WBS_RECEIPTS_NOT_EXISTS);
        ProjectDto projectDto = BeanConverter.copy(projectService.selectByPrimaryKey(receipts.getProjectId()), ProjectDto.class);
        Asserts.notEmpty(projectDto, ErrorCode.CTC_PROJECT_NOT_FIND);
        if (!Objects.equals(receipts.getHandleBy(), SystemContext.getUserId())) {
            throw new BizException(ErrorCode.ERROR, "当前用户不是单据指定处理人，不能操作");
        }

        // 查询WBS单据明细
        List<ProjectWbsChangeReceiptDetail> projectWbsChangeReceiptDetailList = projectWbsChangeReceiptDetailExtMapper.queryByProjectWbsReceiptsId(projectWbsReceiptsId);

        if (Objects.equals(tag, "Set0")) {
            List<Long> projectWbsBudgetIdList = projectWbsChangeReceiptDetailList.stream().map(s -> s.getProjectWbsBudgetId()).collect(Collectors.toList());
            // 查询WBS预算
            List<ProjectWbsBudgetDto> projectWbsBudgetList = projectWbsBudgetService.findDetailWbsBudget(projectDto.getId());
            projectWbsBudgetList.removeIf(s -> Objects.equals(s.getActivityCode(), "System"));
            projectWbsBudgetList.sort(Comparator.comparing(ProjectWbsBudgetDto::getWbsFullCode));
            projectWbsBudgetList.forEach(s -> s.setAfterChangePrice(projectWbsBudgetIdList.contains(s.getId()) ? BigDecimal.ZERO : null));
            projectDto.setProjectWbsBudgetList(projectWbsBudgetList);
        } else if (Objects.equals(tag, "New")) {
            List<ProjectWbsChangeReceiptDetail> newList = projectWbsChangeReceiptDetailList.stream().filter(s -> s.getProjectWbsBudgetId() == null).collect(Collectors.toList());
            projectDto.setProjectWbsBudgetList(BeanConverter.copy(newList, ProjectWbsBudgetDto.class));
        }
        return projectDto;
    }

    /**
     * 从项目中获取项目经理的ID
     *
     * @param projectId
     * @return
     */
    private Long getManagerIdFromProjectId(Long projectId) {
        Guard.notNull(projectId, "项目id不能为空");
        List<Project> projectList = projectService.getProjectByIds(Collections.singletonList(projectId));
        Guard.notNullOrEmpty(projectList, "项目不存在");

        Project project = projectList.get(0);
        Guard.notNull(project, "项目不能为空");

        return project.getManagerId();
    }

    @Override
    public Map<String, Object> checkTemplateWbsChange(ProjectWbsReceiptsDto dto) {
        Long projectId = dto.getProjectId();
        List<List<String>> wbsExcelRows = dto.getWbsExcelRows();

        //查询项目
        Project project = projectService.selectByPrimaryKey(projectId);

        //查询wbs预算
        List<ProjectWbsBudgetDto> projectWbsBudgetList = projectWbsBudgetService.findDetailWbsBudget(projectId);
        projectWbsBudgetList.forEach(s -> s.setWbsSummaryCode(s.getProjectCode() + "-" + s.getWbsFullCode()));

        //检查该项目是否存在状态为 草稿/待处理 的WBS变更单据
        Map<Long, Set<String>> existingWbsFullCodesMap = new HashMap<>();
        ProjectWbsReceiptsExample example = new ProjectWbsReceiptsExample();
        ProjectWbsReceiptsExample.Criteria criteria = example.createCriteria()
                .andProjectIdEqualTo(projectId)
                .andRequirementTypeEqualTo(RequirementTypeEnum.WBS_CHANGE.getCode()) // WBS变更单
                .andRequirementStatusIn(Arrays.asList(RequirementStatusEnum.DRAFT.getCode(), RequirementStatusEnum.TODO.getCode()))
                .andDeletedFlagEqualTo(Boolean.FALSE);
        if (dto.getProjectWbsReceiptsId() != null) {
            criteria.andIdNotEqualTo(dto.getProjectWbsReceiptsId());
        }
        List<ProjectWbsReceipts> existingReceipts = projectWbsReceiptsMapper.selectByExample(example);
        Map<Long, String> requirementCodeMap = existingReceipts.stream().collect(Collectors.toMap(s -> s.getId(), s -> s.getRequirementCode()));
        if (!CollectionUtils.isEmpty(existingReceipts)) {
            List<Long> receiptsIdList = existingReceipts.stream().map(ProjectWbsReceipts::getId).collect(Collectors.toList());
            // 查询已有的WBS变更单据明细
            ProjectWbsChangeReceiptDetailExample detailExample = new ProjectWbsChangeReceiptDetailExample();
            detailExample.createCriteria()
                    .andProjectWbsReceiptsIdIn(receiptsIdList)
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            List<ProjectWbsChangeReceiptDetail> existingDetails = projectWbsChangeReceiptDetailMapper.selectByExample(detailExample);
            if (!CollectionUtils.isEmpty(existingDetails)) {
                // 提取已有明细的WBS编码集合，用于快速查找
                existingWbsFullCodesMap = existingDetails.stream()
                        .collect(Collectors.groupingBy(ProjectWbsChangeReceiptDetail::getProjectWbsReceiptsId
                                , Collectors.collectingAndThen(Collectors.toList(), m -> m.stream().map(ProjectWbsChangeReceiptDetail::getWbsFullCode).collect(Collectors.toSet()))));
            }
        }

        return wbsChangeExcelRows2Map(wbsExcelRows, projectWbsBudgetList, project, existingWbsFullCodesMap, requirementCodeMap);
    }

    public Map<String, Object> wbsChangeExcelRows2Map(List<List<String>> importExcelRows,
                                                      List<ProjectWbsBudgetDto> projectWbsBudgetDtoList,
                                                      Project project,
                                                      Map<Long, Set<String>> existingWbsFullCodesMap,
                                                      Map<Long, String> requirementCodeMap) {
        // 获取有效的wbs动态列
        List<WbsTemplateRuleCache> wbsCacheList = ProjectWbsBudgetUtils.getEligibilityWbsCache(project.getWbsTemplateInfoId());

        // 获取有效的activity活动事项 预算事项=是 当前时间<结束时间
        List<ProjectActivityCache> activityCacheList = ProjectWbsBudgetUtils.getEligibilityActivityCache();
        Map<String, ProjectActivityCache> activityCacheMap = activityCacheList.stream().collect(Collectors.toMap(ProjectActivityCache::getCode, Function.identity(), (key1, key2) -> key2));

        // 项目中已存在的WBS
        Set<String> wbsFullCodeExistSet = projectWbsBudgetDtoList.stream().map(ProjectWbsBudgetDto::getWbsFullCode).collect(Collectors.toSet());

        // 将项目预算列表按WBS全码+活动事项编码分组，用于快速查找
        Map<String, List<ProjectWbsBudgetDto>> projectWbsBudgetDtoMap = projectWbsBudgetDtoList.stream()
                .collect(Collectors.groupingBy(s -> buildProjectWbsBudgetKey(s.getWbsSummaryCode(), s.getActivityCode())));

        // WBS模板
        WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(project.getWbsTemplateInfoId());

        // 获取WBS自定义规则
        WbsCustomizeRuleExample example = new WbsCustomizeRuleExample();
        example.createCriteria().andWbsTemplateInfoIdEqualTo(project.getWbsTemplateInfoId()).andDeletedFlagEqualTo(Boolean.FALSE);
        Map<String, String> wbsCustomizeRuleMap = wbsCustomizeRuleMapper.selectByExample(example).stream().collect(Collectors.toMap(WbsCustomizeRule::getDynamicCombination, WbsCustomizeRule::getDescription, (a, b)-> a));

        // 获取Excel标题行
        List<String> titleList = importExcelRows.get(0);

        // 创建WBS规则详情缓存Map，减少重复查询
        Map<String, WbsTemplateRuleDetailCache> wbsCacheMap = new HashMap<>();
        // 描述缓存Map，减少重复查询
        Map<String, String> descriptionMap = new HashMap<>();

        List<Map<String, Object>> dataList = new ArrayList<>();
        List<String> errMsgList = new ArrayList<>();

        // 从第二行开始处理数据（跳过标题行）
        for (int i = 1; i < importExcelRows.size(); i++) {
            List<String> excelRow = importExcelRows.get(i);
            // 行号
            Integer orderNo = i + 2;
            // 操作类型
            String tag = excelRow.get(1);

            // 只处理New或Set0类型的记录
            if (!(Objects.equals(tag, "New") || Objects.equals(tag, "Set0"))) {
                continue;
            }

            // WBS全码
            String wbsSummaryCode = excelRow.get(2 + wbsCacheList.size());
            // 描述
            String description = excelRow.get(2 + wbsCacheList.size() + 1);
            // 活动事项编码
            String activityCode = excelRow.get(2 + wbsCacheList.size() + 2);
            // 变更后预算金额
            String afterChangePriceStr = excelRow.get(2 + wbsCacheList.size() + 3);
            // 备注
            String remark = excelRow.get(2 + wbsCacheList.size() + 4);
            // 最底层wbs编码
            String lastWbsCode = excelRow.get(2 + wbsCacheList.size() - 1);

            // 读取wbs动态列并构建Map
            Map<String, Object> wbsMap = new HashMap<>();
            for (int j = 0; j < wbsCacheList.size(); j++) {
                // key=动态列key:field_2  value=wbs编码
                String wbsCode = StringUtils.isNotEmpty(excelRow.get(j + 2)) ? excelRow.get(j + 2).trim() : null;
                wbsMap.put(wbsCacheList.get(j).getKey(), wbsCode);
            }

            // 根据操作类型执行不同的校验逻辑
            if (Objects.equals(tag, "Set0")) {
                // 校验WBS全码
                if (StringUtils.isEmpty(wbsSummaryCode)) {
                    errMsgList.add(String.format("行【%s】，WBS号不能为空", orderNo));
                    continue;
                }

                // 校验WBS全码格式
                String[] wbsCodeSplit = wbsSummaryCode.split("-");
                if (wbsCodeSplit.length < wbsCacheList.size()) {
                    errMsgList.add(String.format("行【%s】，WBS号填写有误", orderNo));
                    continue;
                }

                // Set0类型需要校验活动事项编码
                if (StringUtils.isEmpty(activityCode)) {
                    errMsgList.add(String.format("行【%s】，活动事项编码不能为空", orderNo));
                    continue;
                }

                // 校验WBS动态列与WBS全码的一致性
                for (int j = 0; j < wbsCacheList.size(); j++) {
                    String wbsCode = excelRow.get(j + 2);
                    if (StringUtils.isEmpty(wbsCode)) {
                        errMsgList.add(String.format("行【%s】，%s不能为空", orderNo, titleList.get(j + 2)));
                    } else {
                        if (!Objects.equals(wbsCode, wbsCodeSplit[wbsCodeSplit.length - wbsCacheList.size() + j])) {
                            errMsgList.add(String.format("行【%s】，%s填写有误", orderNo, titleList.get(j + 2)));
                        }
                    }
                }

                // 校验WBS+活动事项是否存在于预算中
                String key = buildProjectWbsBudgetKey(wbsSummaryCode, activityCode);
                List<ProjectWbsBudgetDto> lists = projectWbsBudgetDtoMap.get(key);
                if (CollectionUtils.isEmpty(lists)) {
                    errMsgList.add(String.format("行【%s】，WBS+活动事项不存在", orderNo));
                } else if (lists.size() > 1) {
                    errMsgList.add(String.format("行【%s】，WBS+活动事项存在2条及以上", orderNo));
                } else {
                    // 校验通过，处理数据
                    Map<String, Object> data = BeanMapTool.beanToMap(lists.get(0));

                    //检查该项目是否存在状态为"待处理"的WBS变更单据
                    String wbsFullCode = MapUtils.getString(data, WbsBudgetFieldConstant.WBS_FULL_CODE);
                    existingWbsFullCodesMap.forEach((k, v) -> {
                        if (v.contains(wbsFullCode)) {
                            errMsgList.add(String.format("行【%s】，处理中/草稿的单据号%s存在相同的WBS数据，不能导入", orderNo, requirementCodeMap.get(k)));
                        }
                    });

                    data.putAll(wbsMap);
                    data.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, BigDecimal.ZERO);
                    data.put(WbsBudgetFieldConstant.PROJECT_WBS_BUDGET_ID, MapUtils.getLong(data, WbsBudgetFieldConstant.ID));
                    data.put(WbsBudgetFieldConstant.TAG, tag);
                    data.put(WbsBudgetFieldConstant.DESCRIPTION, description);
                    data.put(WbsBudgetFieldConstant.REMARK, remark);
                    dataList.add(data);
                }
            } else if (Objects.equals(tag, "New")) {
                // New类型的处理逻辑
                Map<String, Object> data = new HashMap<>();
                data.putAll(wbsMap);

                // 校验活动事项
                if (StringUtils.isNotEmpty(activityCode)) {
                    ProjectActivityCache activityCache = activityCacheMap.get(activityCode);
                    if (activityCache == null) {
                        errMsgList.add(String.format("行【%s】，活动事项编码%s不存在", orderNo, activityCode));
                        continue;
                    }

                    // 设置活动事项相关信息
                    data.put(WbsBudgetFieldConstant.ACTIVITY_CODE, activityCache.getCode());
                    data.put(WbsBudgetFieldConstant.ACTIVITY_NAME, activityCache.getName());
                    data.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, activityCache.getType());
                    data.put(WbsBudgetFieldConstant.ACTIVITY_ORDER_NO, activityCache.getOrderNo());
                } else {
                    data.put(WbsBudgetFieldConstant.ACTIVITY_CODE, "");
                }

                // 校验WBS是否有效
                String errorMsg = checkWbsValid(data, wbsCacheList, wbsCacheMap);
                if (StringUtils.isNotEmpty(errorMsg)) {
                    errMsgList.add(String.format("行【%s】，%s", orderNo, errorMsg));
                    continue;
                }

                // 校验变更后预算金额
                data.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, BigDecimal.ZERO);
                if (StringUtils.isNotEmpty(afterChangePriceStr)) {
                    if (BigDecimalUtils.isBigDecimal(afterChangePriceStr)) {
                        BigDecimal afterChangePrice = new BigDecimal(afterChangePriceStr);
                        if (afterChangePrice.compareTo(BigDecimal.ZERO) < 0 || afterChangePrice.scale() > 2) {
                            errMsgList.add(String.format("行【%s】，只能填写2位小数的正数", orderNo));
                            continue;
                        }
                        data.put(WbsBudgetFieldConstant.AFTER_CHANGE_PRICE, afterChangePrice);
                    } else {
                        errMsgList.add(String.format("行【%s】，只能填写2位小数的正数", orderNo));
                    }
                }

                // 校验WBS是否已存在
                String wbsFullCode = ProjectWbsBudgetUtils.getCacheWbsFullCode(data, wbsCacheList);
                if (wbsFullCodeExistSet.contains(wbsFullCode)) {
                    errMsgList.add(String.format("行【%s】，项目已存在该WBS，无需新增", orderNo));
                    continue;
                }
                // 检查该项目是否存在状态为"待处理"的WBS变更单据
                existingWbsFullCodesMap.forEach((k, v) -> {
                    if (v.contains(wbsFullCode)) {
                        errMsgList.add(String.format("行【%s】，处理中/草稿的单据号%s存在相同的WBS数据，不能导入", orderNo, requirementCodeMap.get(k)));
                    }
                });

                // 设置其他信息
                data.put(WbsBudgetFieldConstant.PROJECT_CODE, project.getCode());
                data.put(WbsBudgetFieldConstant.PROJECT_NAME, project.getName());
                data.put(WbsBudgetFieldConstant.REMARK, remark);
                data.put(WbsBudgetFieldConstant.TAG, tag);
                data.put(WbsBudgetFieldConstant.DYNAMIC_FIELDS, ProjectWbsBudgetUtils.getCacheDynamicFields(wbsCacheList));
                data.put(WbsBudgetFieldConstant.DYNAMIC_VALUES, ProjectWbsBudgetUtils.getCacheDynamicValues(data, wbsCacheList));
                data.put(WbsBudgetFieldConstant.WBS_FULL_CODE, wbsFullCode);
                if (StringUtils.isNotEmpty(wbsFullCode) && StringUtils.isNotEmpty(project.getCode())) {
                    data.put(WbsBudgetFieldConstant.WBS_SUMMARY_CODE, project.getCode() + "-" + wbsFullCode);
                }

                // 描述=空，按照WBS规则取
                if (StringUtils.isEmpty(description) && StringUtils.isNotEmpty(lastWbsCode) && StringUtils.isNotEmpty(wbsFullCode)) {
                    WbsTemplateRuleDetailCache wbsCache;
                    Long lastWbsId = wbsCacheList.get(wbsCacheList.size() - 1).getId();
                    String wbsKey = lastWbsId + lastWbsCode;
                    if (wbsCacheMap.containsKey(wbsKey)) {
                        wbsCache = wbsCacheMap.get(wbsKey);
                    } else {
                        wbsCache = WbsTemplateRuleDetailCacheUtils.getCache(lastWbsId, lastWbsCode);
                        wbsCacheMap.put(wbsKey, wbsCache);
                    }
                    description = wbsTemplateRuleDetailService.getLastWbs(wbsTemplateInfo, wbsFullCode, wbsCache, wbsCustomizeRuleMap);;
                }

                // 补0
                data.put(WbsBudgetFieldConstant.PRICE, BigDecimal.ZERO);
                data.put(WbsBudgetFieldConstant.BASELINE_COST, BigDecimal.ZERO);
                data.put(WbsBudgetFieldConstant.DEMAND_COST, BigDecimal.ZERO);
                data.put(WbsBudgetFieldConstant.ON_THE_WAY_COST, BigDecimal.ZERO);
                data.put(WbsBudgetFieldConstant.INCURRED_COST, BigDecimal.ZERO);
                data.put(WbsBudgetFieldConstant.REMAINING_COST, BigDecimal.ZERO);
                data.put(WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST, BigDecimal.ZERO);
                data.put(WbsBudgetFieldConstant.DESCRIPTION, Optional.ofNullable(description).orElse(""));

                dataList.add(data);
            }
        }

        // 对dataList进行双重排序：首先按tag字段降序，然后按wbsFullCode字段升序
        dataList.sort(Comparator
                .comparing((Map<String, Object> data) -> MapUtils.getString(data, WbsBudgetFieldConstant.TAG), Comparator.reverseOrder())
                .thenComparing(data -> MapUtils.getString(data, WbsBudgetFieldConstant.WBS_FULL_CODE)));

        // 构建返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("flag", CollectionUtils.isEmpty(errMsgList));
        resultMap.put("errMsg", errMsgList);
        if (CollectionUtils.isEmpty(errMsgList)) {
            resultMap.put("dataList", dataList);
        }
        return resultMap;
    }

    private static String checkWbsValid(Map map, List<WbsTemplateRuleCache> wbsCacheList, Map<String, WbsTemplateRuleDetailCache> wbsCacheMap) {
        for (int i = 0; i < wbsCacheList.size(); i++) {
            WbsTemplateRuleCache wbsRuleCache = wbsCacheList.get(i);
            String wbsCode = MapUtils.getString(map, wbsRuleCache.getKey());
            if (StringUtils.isEmpty(wbsCode)) {
                return String.format("列%s，\"%s\"为必填项，请补充完整", i + 3, wbsRuleCache.getLable().trim());
            }
            wbsCode = wbsCode.trim();
            if (Boolean.TRUE.equals(wbsRuleCache.getStrictlyControl())) {
                String key = wbsRuleCache.getId() + wbsCode;
                WbsTemplateRuleDetailCache wbsCache;
                if (wbsCacheMap.containsKey(key)) {
                    wbsCache = wbsCacheMap.get(key);
                } else {
                    wbsCache = WbsTemplateRuleDetailCacheUtils.getCache(wbsRuleCache.getId(), wbsCode);
                    wbsCacheMap.put(key, wbsCache);
                }
                if (null == wbsCache || !wbsCache.getLevel().equals("明细") || (null != wbsCache.getEndTime() && wbsCache.getEndTime().before(new Date()))) {
                    return String.format("列%s，\"%s\"的值%s有误，请确认该值在项目WBS模板中是否有配置", i + 3, wbsRuleCache.getLable(), wbsCode);
                }
            }
        }
        return null;
    }

    private static String buildProjectWbsBudgetKey(String wbsSummaryCode, String activityCode) {
        return String.format("%s_buildProjectWbsBudgetKey_%s", wbsSummaryCode, activityCode);
    }

    /**
     * 设置父级物料描述
     *
     * @param designPlanDetailChangeHistoryVO 设计计划详情变更历史VO
     */
    private void setParentMaterielDescr(DesignPlanDetailChangeHistoryVO designPlanDetailChangeHistoryVO) {
        if (designPlanDetailChangeHistoryVO == null) {
            return;
        }

        // 收集所有节点的映射关系，便于查找父级物料描述
        Map<Long, String> changeIdToMaterielDescrMap = new HashMap<>();
        Map<Long, String> historyIdToMaterielDescrMap = new HashMap<>();

        // 第一步：收集所有节点的物料描述映射
        collectMaterielDescrMapping(designPlanDetailChangeHistoryVO, changeIdToMaterielDescrMap, historyIdToMaterielDescrMap);

        // 第二步：设置父级物料描述
        processParentMaterielDescr(designPlanDetailChangeHistoryVO, changeIdToMaterielDescrMap, historyIdToMaterielDescrMap);
    }

    /**
     * 收集所有节点的物料描述映射关系
     *
     * @param vo 变更历史VO
     * @param changeIdToMaterielDescrMap 变更ID到物料描述的映射
     * @param historyIdToMaterielDescrMap 历史ID到物料描述的映射
     */
    private void collectMaterielDescrMapping(DesignPlanDetailChangeHistoryVO vo,
                                           Map<Long, String> changeIdToMaterielDescrMap,
                                           Map<Long, String> historyIdToMaterielDescrMap) {
        if (vo == null) {
            return;
        }

        // 收集变更记录的物料描述
        if (vo.getChange() != null && vo.getChange().getId() != null && vo.getChange().getMaterielDescr() != null) {
            changeIdToMaterielDescrMap.put(vo.getChange().getId(), vo.getChange().getMaterielDescr());
        }

        // 收集历史记录的物料描述
        if (vo.getHistory() != null && vo.getHistory().getId() != null && vo.getHistory().getMaterielDescr() != null) {
            historyIdToMaterielDescrMap.put(vo.getHistory().getId(), vo.getHistory().getMaterielDescr());
        }

        // 递归处理子节点
        if (ListUtils.isNotEmpty(vo.getSonVos())) {
            for (DesignPlanDetailChangeHistoryVO sonVo : vo.getSonVos()) {
                collectMaterielDescrMapping(sonVo, changeIdToMaterielDescrMap, historyIdToMaterielDescrMap);
            }
        }
    }

    /**
     * 处理父级物料描述设置
     *
     * @param vo 变更历史VO
     * @param changeIdToMaterielDescrMap 变更ID到物料描述的映射
     * @param historyIdToMaterielDescrMap 历史ID到物料描述的映射
     */
    private void processParentMaterielDescr(DesignPlanDetailChangeHistoryVO vo,
                                          Map<Long, String> changeIdToMaterielDescrMap,
                                          Map<Long, String> historyIdToMaterielDescrMap) {
        if (vo == null) {
            return;
        }

        // 处理变更记录的父级物料描述
        if (vo.getChange() != null && vo.getChange().getParentId() != null) {
            String parentMaterielDescr = changeIdToMaterielDescrMap.get(vo.getChange().getParentId());
            if (parentMaterielDescr != null) {
                vo.getChange().setParentMaterielDescr(parentMaterielDescr);
                logger.debug("设置变更记录父级物料描述: changeId={}, parentId={}, parentMaterielDescr={}",
                           vo.getChange().getId(), vo.getChange().getParentId(), parentMaterielDescr);
            }
        }

        // 处理历史记录的父级物料描述
        if (vo.getHistory() != null && vo.getHistory().getParentId() != null) {
            String parentMaterielDescr = historyIdToMaterielDescrMap.get(vo.getHistory().getParentId());
            if (parentMaterielDescr != null) {
                vo.getHistory().setParentMaterielDescr(parentMaterielDescr);
                logger.debug("设置历史记录父级物料描述: historyId={}, parentId={}, parentMaterielDescr={}",
                           vo.getHistory().getId(), vo.getHistory().getParentId(), parentMaterielDescr);
            }
        }

        // 递归处理子节点
        if (ListUtils.isNotEmpty(vo.getSonVos())) {
            for (DesignPlanDetailChangeHistoryVO sonVo : vo.getSonVos()) {
                processParentMaterielDescr(sonVo, changeIdToMaterielDescrMap, historyIdToMaterielDescrMap);
            }
        }
    }

    @Override
    public Boolean ProjectWbsDesignPlanAutoConfirm(Long projectId, Long projectWbsReceiptsId) {
        if(Objects.isNull(projectId)){
            logger.info("项目id为空，不能自动确认，projectId: {}", projectId);
            return false;
        }
        if(Objects.isNull(projectWbsReceiptsId)){
            logger.info("项目wbs单据id为空，不能自动确认，projectWbsReceiptsId: {}", projectWbsReceiptsId);
            return false;
        }

        Project project = projectService.selectByPrimaryKey(projectId);
        if(Objects.isNull(project)){
            logger.info("项目不存在，不能自动确认，projectId: {}", projectId);
            return false;
        }

        if(!Objects.equals(project.getStatus(), ProjectStatus.APPROVALED.getCode())){
            logger.info("项目状态不是进行中，不能自动确认，projectId: {}, 当前状态: {}, 期望状态: {}",
                    projectId, project.getStatus(), ProjectStatus.APPROVALED.getCode());
            return false;
        }

        if(Objects.equals(project.getDeletedFlag(), Boolean.TRUE)){
            logger.info("项目已删除，不能自动确认，projectId: {}, deletedFlag: {}",
                    projectId, project.getDeletedFlag());
            return false;
        }

        if(Objects.isNull(project.getType())){
            logger.info("项目类型为空，不能自动确认，projectId: {}, projectType: {}",
                    projectId, project.getType());
            return false;
        }

        Long typeId = project.getType();

        ProjectType projectType = projectTypeMapper.selectByPrimaryKey(typeId);

        Boolean wbsDesignPlanAutoConfirm = projectType.getWbsDesignPlanAutoConfirm();

        if(!Objects.equals(wbsDesignPlanAutoConfirm,Boolean.TRUE)){
            logger.info("项目类型未开启WBS详设自动确认，不能自动确认，projectId: {}, typeId: {}, wbsDesignPlanAutoConfirm: {}",
                    projectId, typeId, wbsDesignPlanAutoConfirm);
            return false;
        }

        ProjectWbsReceiptsDto projectWbsReceiptsDto = getById(projectWbsReceiptsId);

        if(Objects.isNull(projectWbsReceiptsDto)){
            logger.info("项目wbs单据不存在，不能自动确认，projectWbsReceiptsId: {}", projectWbsReceiptsId);
            return false;
        }

        if(!Objects.equals(projectWbsReceiptsDto.getRequirementStatus(),RequirementStatusEnum.PROCESS.getCode())){
            logger.info("项目wbs单据状态不是生效，不能自动确认，projectWbsReceiptsId: {}, 当前状态: {}, 期望状态: {}",
                    projectWbsReceiptsId, projectWbsReceiptsDto.getRequirementStatus(), RequirementStatusEnum.PROCESS.getCode());
            return false;
        }

        if(!Objects.equals(projectWbsReceiptsDto.getRequirementType(),RequirementTypeEnum.DESIGN_PLAN_SUBMIT.getCode())
                && !Objects.equals(projectWbsReceiptsDto.getRequirementType(),RequirementTypeEnum.DESIGN_PLAN_CHANGE.getCode())){
            logger.info("项目wbs单据类型不是详细设计发布或者详设变更，不能自动确认，projectWbsReceiptsId: {}, 当前类型: {}, 期望类型: {} 或 {}",
                    projectWbsReceiptsId, projectWbsReceiptsDto.getRequirementType(),
                    RequirementTypeEnum.DESIGN_PLAN_SUBMIT.getCode(), RequirementTypeEnum.DESIGN_PLAN_CHANGE.getCode());
            return false;
        }

        //处理详细设计发布
        if(Objects.equals(projectWbsReceiptsDto.getRequirementType(),RequirementTypeEnum.DESIGN_PLAN_SUBMIT.getCode())){
            //根据projectReceiptsId查询详细设计方案发布历史
            MilepostDesignPlanDetailSubmitHistoryExample historyExample = new MilepostDesignPlanDetailSubmitHistoryExample();
            historyExample.createCriteria().andProjectWbsReceiptsIdEqualTo(projectWbsReceiptsId).andDeletedFlagEqualTo(Boolean.FALSE)
                    .andHistoryTypeEqualTo(Boolean.TRUE); //排除非变更项的记录

            List<MilepostDesignPlanDetailSubmitHistory> submitHistoryList =
                    milepostDesignPlanDetailSubmitHistoryService.selectByExample(historyExample);

            if(ListUtils.isEmpty(submitHistoryList)){
                logger.info("项目wbs单据没有对应的详细设计方案发布历史，不能自动确认，projectWbsReceiptsId: {}", projectWbsReceiptsId);
                return false;
            }

            //根据submitHistoryList 集合中的projectId,pamCode,deliveryTime,wbsSummaryCode 这四个字段查询对应详设(milepost_design_plan_detail)
            List<Long> projectIdList = submitHistoryList.stream().map(MilepostDesignPlanDetailSubmitHistory::getProjectId).collect(Collectors.toList());
            List<String> pamCodeList = submitHistoryList.stream().map(MilepostDesignPlanDetailSubmitHistory::getPamCode).collect(Collectors.toList());
            List<Date> deliveryTimeList = submitHistoryList.stream().map(MilepostDesignPlanDetailSubmitHistory::getDeliveryTime).collect(Collectors.toList());
            List<String> wbsSummaryCodeList = submitHistoryList.stream().map(MilepostDesignPlanDetailSubmitHistory::getWbsSummaryCode).collect(Collectors.toList());

            // 查询详设数据并处理确认逻辑
            List<MilepostDesignPlanDetail> milepostDesignPlanDetailList = queryMilepostDesignPlanDetails(projectIdList, pamCodeList, deliveryTimeList, wbsSummaryCodeList);
            if(ListUtils.isEmpty(milepostDesignPlanDetailList)){
                logger.info("项目wbs单据对应的详细设计方案发布历史没有对应的外购物料，不能自动确认，projectWbsReceiptsId: {}", projectWbsReceiptsId);
                return false;
            }

            Map<String, List<MilepostDesignPlanDetail>> milepostDesignPlanDetailMap = groupByWbsSummaryCode(milepostDesignPlanDetailList);
            logger.info( "项目wbs单据对应的详细设计方案发布历史对应的外购物料，可以自动确认，projectWbsReceiptsId: {}, milepostDesignPlanDetailMap: {}",
                    projectWbsReceiptsId, JsonUtils.toString(milepostDesignPlanDetailMap));

            processDesignPlanConfirmation(milepostDesignPlanDetailMap, project, projectWbsReceiptsDto, projectWbsReceiptsId);


        }else if( Objects.equals(projectWbsReceiptsDto.getRequirementType(),RequirementTypeEnum.DESIGN_PLAN_CHANGE.getCode())){
            //处理详细设计变更
            // 根据 projectWbsReceiptsId 查询 milepost_design_plan_detail_change 表
            MilepostDesignPlanDetailChangeExample milepostDesignPlanDetailChangeExample = new MilepostDesignPlanDetailChangeExample();
            milepostDesignPlanDetailChangeExample.createCriteria().andProjectWbsReceiptsIdEqualTo(projectWbsReceiptsId).andDeletedFlagEqualTo(Boolean.FALSE)
                    .andHistoryTypeEqualTo(1) //排除非变更项的记录
                    .andChangeTypeIn(Lists.newArrayList("0","1")) //只查询新增和修改的记录
                    .andMaterialCategoryEqualTo("外购物料");//只查询外购物料的记录
            List<MilepostDesignPlanDetailChange> milepostDesignPlanDetailChanges = milepostDesignPlanDetailChangeService.selectByExample(milepostDesignPlanDetailChangeExample);

            //根据milepostDesignPlanDetailChanges 集合中的projectId,pamCode,deliveryTime,wbsSummaryCode 这四个字段查询对应详设(milepost_design_plan_detail)
            List<Long> projectIdList = milepostDesignPlanDetailChanges.stream().map(MilepostDesignPlanDetailChange::getProjectId).collect(Collectors.toList());
            List<String> pamCodeList = milepostDesignPlanDetailChanges.stream().map(MilepostDesignPlanDetailChange::getPamCode).collect(Collectors.toList());
            List<Date> deliveryTimeList = milepostDesignPlanDetailChanges.stream().map(MilepostDesignPlanDetailChange::getDeliveryTime).collect(Collectors.toList());
            List<String> wbsSummaryCodeList = milepostDesignPlanDetailChanges.stream().map(MilepostDesignPlanDetailChange::getWbsSummaryCode).collect(Collectors.toList());

            List<MilepostDesignPlanDetail> milepostDesignPlanDetailList = queryMilepostDesignPlanDetails(projectIdList, pamCodeList, deliveryTimeList, wbsSummaryCodeList);

            if(ListUtils.isEmpty(milepostDesignPlanDetailList)){
                logger.info("项目wbs单据对应的详细设计方案变更历史没有对应的外购物料，不能自动确认，projectWbsReceiptsId: {}", projectWbsReceiptsId);
                return false;
            }

            Map<String, List<MilepostDesignPlanDetail>> milepostDesignPlanDetailMap = groupByWbsSummaryCode(milepostDesignPlanDetailList);

            logger.info( "项目wbs单据对应的详细设计方案变更历史对应的外购物料，可以自动确认，projectWbsReceiptsId: {}, milepostDesignPlanDetailMap: {}",
                    projectWbsReceiptsId, JsonUtils.toString(milepostDesignPlanDetailMap));

            processDesignPlanConfirmation(milepostDesignPlanDetailMap, project, projectWbsReceiptsDto, projectWbsReceiptsId);
        }

        return Boolean.TRUE;
    }

    /**
     * 查询详设数据的通用方法
     * @param projectIdList 项目ID列表
     * @param pamCodeList PAM编码列表
     * @param deliveryTimeList 交付时间列表
     * @param wbsSummaryCodeList WBS汇总编码列表
     * @return 过滤后的详设数据列表
     */
    private List<MilepostDesignPlanDetail> queryMilepostDesignPlanDetails(List<Long> projectIdList, List<String> pamCodeList, List<Date> deliveryTimeList, List<String> wbsSummaryCodeList) {
        // 记录方法入参信息
        logger.info("开始查询详设数据 - 入参信息: projectIdList={}, pamCodeList={}, deliveryTimeList={}, wbsSummaryCodeList={}",
                JsonUtils.toString(projectIdList), JsonUtils.toString(pamCodeList),
                JsonUtils.toString(deliveryTimeList), JsonUtils.toString(wbsSummaryCodeList));

        // 参数校验和统计
        int projectIdCount = projectIdList != null ? projectIdList.size() : 0;
        int pamCodeCount = pamCodeList != null ? pamCodeList.size() : 0;
        int deliveryTimeCount = deliveryTimeList != null ? deliveryTimeList.size() : 0;
        int wbsSummaryCodeCount = wbsSummaryCodeList != null ? wbsSummaryCodeList.size() : 0;

        logger.info("查询详设数据 - 参数统计: 项目ID数量={}, PAM编码数量={}, 交付时间数量={}, WBS汇总编码数量={}",
                projectIdCount, pamCodeCount, deliveryTimeCount, wbsSummaryCodeCount);

        // 检查参数是否为空
        if (projectIdCount == 0 || pamCodeCount == 0 || deliveryTimeCount == 0 || wbsSummaryCodeCount == 0) {
            logger.warn("查询详设数据 - 发现空参数列表，可能影响查询结果: projectIdEmpty={}, pamCodeEmpty={}, deliveryTimeEmpty={}, wbsSummaryCodeEmpty={}",
                    projectIdCount == 0, pamCodeCount == 0, deliveryTimeCount == 0, wbsSummaryCodeCount == 0);
        }

        // 构建查询条件
        logger.info("查询详设数据 - 开始构建查询条件");
        MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
        milepostDesignPlanDetailExample.createCriteria().andProjectIdIn(projectIdList).andPamCodeIn(pamCodeList)
                .andDeliveryTimeIn(deliveryTimeList)
                .andWbsSummaryCodeIn(wbsSummaryCodeList)
                .andModuleStatusEqualTo(0)
                .andDeletedFlagEqualTo(Boolean.FALSE).andMaterialCategoryEqualTo("外购物料");

        logger.info("查询详设数据 - 查询条件构建完成，固定条件: moduleStatus=0, deletedFlag=false, materialCategory=外购物料");

        // 执行数据库查询
        long startTime = System.currentTimeMillis();
        logger.info("查询详设数据 - 开始执行数据库查询");

        List<MilepostDesignPlanDetail> milepostDesignPlanDetailList = milepostDesignPlanDetailService.selectByExample(milepostDesignPlanDetailExample);

        long endTime = System.currentTimeMillis();
        int resultCount = milepostDesignPlanDetailList != null ? milepostDesignPlanDetailList.size() : 0;

        logger.info("查询详设数据 - 数据库查询完成，耗时={}ms, 查询结果数量={}", (endTime - startTime), resultCount);

        // 记录查询结果详情（仅在DEBUG级别下记录详细数据，避免日志过多）
        if (logger.isDebugEnabled() && resultCount > 0) {
            logger.debug("查询详设数据 - 查询结果详情: {}", JsonUtils.toString(milepostDesignPlanDetailList));
        }
        // 记录关键字段统计信息
        if (resultCount > 0) {
            Map<Long, Long> projectIdStats = milepostDesignPlanDetailList.stream()
                    .collect(Collectors.groupingBy(MilepostDesignPlanDetail::getProjectId, Collectors.counting()));
            Map<String, Long> wbsCodeStats = milepostDesignPlanDetailList.stream()
                    .collect(Collectors.groupingBy(MilepostDesignPlanDetail::getWbsSummaryCode, Collectors.counting()));

            logger.info("查询详设数据 - 结果统计: 涉及项目数量={}, 涉及WBS编码数量={}",
                    projectIdStats.size(), wbsCodeStats.size());
            logger.info("查询详设数据 - 项目分布: {}", JsonUtils.toString(projectIdStats));
            logger.info("查询详设数据 - WBS编码分布: {}", JsonUtils.toString(wbsCodeStats));
        } else {
            logger.warn("查询详设数据 - 未查询到任何符合条件的详设数据，请检查查询条件是否正确");
        }

        logger.info("查询详设数据 - 方法执行完成，返回结果数量={}", resultCount);
        return milepostDesignPlanDetailList;
    }

    /**
     * 按wbsSummaryCode分组
     * @param milepostDesignPlanDetailList 详设数据列表
     * @return 分组后的Map
     */
    private Map<String, List<MilepostDesignPlanDetail>> groupByWbsSummaryCode(List<MilepostDesignPlanDetail> milepostDesignPlanDetailList) {
        return milepostDesignPlanDetailList.stream().collect(Collectors.groupingBy(MilepostDesignPlanDetail::getWbsSummaryCode));
    }

    /**
     * 处理设计方案确认的核心逻辑
     * @param milepostDesignPlanDetailMap 按wbsSummaryCode分组的详设数据
     * @param project 项目信息
     * @param projectWbsReceiptsDto 原始单据信息
     * @param projectWbsReceiptsId 单据ID
     */
    private void processDesignPlanConfirmation(Map<String, List<MilepostDesignPlanDetail>> milepostDesignPlanDetailMap,
                                             Project project,
                                             ProjectWbsReceiptsDto projectWbsReceiptsDto,
                                             Long projectWbsReceiptsId) {
        // 开始调用部分确认接口
        // 数据组装
        for (String wbsSummaryCode : milepostDesignPlanDetailMap.keySet()) {
            List<MilepostDesignPlanDetail> milepostDesignPlanDetailListByWbsSummaryCode = milepostDesignPlanDetailMap.get(wbsSummaryCode);

            // 构建ProjectWbsReceiptsDto
            ProjectWbsReceiptsDto preHandleProjectWbsReceiptsDto = buildProjectWbsReceiptsDto(project, projectWbsReceiptsDto);

            // 构建designRelList
            List<ProjectWbsReceiptsDesignPlanRelDto> designRelList = buildDesignRelList(milepostDesignPlanDetailListByWbsSummaryCode);
            preHandleProjectWbsReceiptsDto.setDesignRelList(designRelList);

            ProjectWbsReceiptsDto afterProjectWbsReceiptsDto = this.saveReceipts(preHandleProjectWbsReceiptsDto, projectWbsReceiptsDto.getCreateBy());
            logger.info("项目wbs单据自动确认，projectWbsReceiptsId: {}, afterProjectWbsReceiptsDto: {}", afterProjectWbsReceiptsDto.getId(), JSON.toJSONString(afterProjectWbsReceiptsDto));
            afterProjectWbsReceiptsDto.setRemark(projectWbsReceiptsDto.getRensonRemark());
            projectWbsReceiptsMapper.updateByPrimaryKeySelective(BeanConverter.copy(afterProjectWbsReceiptsDto, ProjectWbsReceipts.class));
        }
    }

    /**
     * 构建ProjectWbsReceiptsDto基础信息
     * @param project 项目信息
     * @param originalDto 原始单据信息
     * @return 构建好的ProjectWbsReceiptsDto
     */
    private ProjectWbsReceiptsDto buildProjectWbsReceiptsDto(Project project, ProjectWbsReceiptsDto originalDto) {
        ProjectWbsReceiptsDto preHandleProjectWbsReceiptsDto = new ProjectWbsReceiptsDto();
        preHandleProjectWbsReceiptsDto.setProjectId(project.getId());
        preHandleProjectWbsReceiptsDto.setProjectCode(project.getCode());
        preHandleProjectWbsReceiptsDto.setProjectName(project.getName());
        preHandleProjectWbsReceiptsDto.setRequirementType(RequirementTypeEnum.REQUIREMENT_PUBLISH.getCode());
        preHandleProjectWbsReceiptsDto.setProducerName(originalDto.getProducerName());
        preHandleProjectWbsReceiptsDto.setProducerId(originalDto.getProducerId());
        preHandleProjectWbsReceiptsDto.setConfirmMode(1);
        preHandleProjectWbsReceiptsDto.setHandleBy(originalDto.getHandleBy());
        preHandleProjectWbsReceiptsDto.setHandleName(originalDto.getHandleName());
        return preHandleProjectWbsReceiptsDto;
    }

    /**
     * 构建设计关系列表
     * @param milepostDesignPlanDetailList 详设数据列表
     * @return 设计关系列表
     */
    private List<ProjectWbsReceiptsDesignPlanRelDto> buildDesignRelList(List<MilepostDesignPlanDetail> milepostDesignPlanDetailList) {
        List<ProjectWbsReceiptsDesignPlanRelDto> designRelList = new ArrayList<>();

        for (MilepostDesignPlanDetail milepostDesignPlanDetail : milepostDesignPlanDetailList) {
            ProjectWbsReceiptsDesignPlanRelDto projectWbsReceiptsDesignPlanRelDto = new ProjectWbsReceiptsDesignPlanRelDto();
            projectWbsReceiptsDesignPlanRelDto.setDesignPlanDetailId(milepostDesignPlanDetail.getId());
            projectWbsReceiptsDesignPlanRelDto.setAttachs("");
            projectWbsReceiptsDesignPlanRelDto.setPurchaseNum(milepostDesignPlanDetail.getNumber());
            projectWbsReceiptsDesignPlanRelDto.setDeliveryTime(milepostDesignPlanDetail.getDeliveryTime());
            projectWbsReceiptsDesignPlanRelDto.setMaterialCategory(milepostDesignPlanDetail.getMaterialCategory());
            projectWbsReceiptsDesignPlanRelDto.setPamCode(milepostDesignPlanDetail.getPamCode());
            projectWbsReceiptsDesignPlanRelDto.setMaterialClassification(milepostDesignPlanDetail.getMaterialClassification());
            projectWbsReceiptsDesignPlanRelDto.setCodingMiddleClass(milepostDesignPlanDetail.getCodingMiddleClass());
            projectWbsReceiptsDesignPlanRelDto.setMaterielType(milepostDesignPlanDetail.getMaterielType());
            projectWbsReceiptsDesignPlanRelDto.setModuleStatus(milepostDesignPlanDetail.getModuleStatus());
            designRelList.add(projectWbsReceiptsDesignPlanRelDto);
        }

        return designRelList;
    }
}
